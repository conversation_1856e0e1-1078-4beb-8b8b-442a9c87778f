name: Run bash script

on:
  push:
    branches: [ "pre-release-staging" ]

jobs:
  runscript:
    name: Automation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'
          cache-from: type=gha
          cache-to: type=gha,mode=max
      - id: files
        uses: jitterbit/get-changed-files@v1
      - name: Call a Bash Script
        env:
          DOCKER_HUB_USERNAME: ${{ secrets.DOCKER_HUB_USERNAME }}
          DOCKER_HUB_TOKEN: ${{ secrets.DOCKER_HUB_TOKEN }}
          RANCHER_ACCESS_KEY: ${{ secrets.RANCHER_AWS_ACCESS_KEY }}
          RANCHER_SECRET_KEY: ${{ secrets.RANCHER_AWS_SECRET_KEY }}
        run: |
          chmod +x "${GITHUB_WORKSPACE}/scripts/deploy-to-staging1.sh"
          bash ${GITHUB_WORKSPACE}/scripts/deploy-to-staging1.sh $DOCKER_HUB_USERNAME $DOCKER_HUB_TOKEN $RANCHER_ACCESS_KEY $RANCHER_SECRET_KEY "${{ github.event.head_commit.message }}" "${{ steps.files.outputs.all }}"