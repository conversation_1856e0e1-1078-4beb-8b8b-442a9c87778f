name: Run bash script

on:
  push:
    branches: [ "pre-release-staging1" ]

jobs:
  runscript:
    name: Automation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v3
        with:
          java-version: '17'
          distribution: 'adopt'
          cache-from: type=gha
          cache-to: type=gha,mode=max
          # maven settings.xml config
          server-id: github # value of repository/id field of the pom.xml
          server-username: GITHUB_USER_REF  # env variable name for username
          server-password: GITHUB_TOKEN_REF # env variable name for GitHub Personal Access Token
      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2
          key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-m2-
      - name: Build with Maven
        run: mvn -B -Pgithub install -DskipTests --file pom.xml
        env:
          GITHUB_USER_REF: ${{ secrets.GH_PACKAGE_REPO_USERNAME }}
          GITHUB_TOKEN_REF: ${{ secrets.GH_PACKAGE_REPO_PASSWORD }}

      - id: files
        uses: jitterbit/get-changed-files@v1
      - name: Call a Bash Script
        env:
          DOCKER_HUB_USERNAME: ${{ secrets.DOCKER_HUB_USERNAME }}
          DOCKER_HUB_TOKEN: ${{ secrets.DOCKER_HUB_TOKEN }}
          RANCHER_ACCESS_TOKEN: ${{ secrets.RANCHER_R2_ACCESS_TOKEN }}
        #        run: |
        #          chmod +x "${GITHUB_WORKSPACE}/scripts/deploy-to-staging.sh"
        #          bash ${GITHUB_WORKSPACE}/scripts/deploy-to-staging.sh $DOCKER_HUB_USERNAME $DOCKER_HUB_TOKEN $RANCHER_ACCESS_TOKEN "${{ github.event.head_commit.message }}" "${{ steps.files.outputs.all }}"
        run: |
          chmod +x "${GITHUB_WORKSPACE}/scripts/deploy-to-staging.sh"
          bash ${GITHUB_WORKSPACE}/scripts/deploy-to-staging.sh $DOCKER_HUB_USERNAME $DOCKER_HUB_TOKEN $RANCHER_ACCESS_TOKEN "${{ github.event.head_commit.message }}" "${{ steps.files.outputs.all }}"