# spring-cloud-alibaba

1. Development environment

   - Java 17
   - Maven 1.8.0
   - Docker 20.10.7
   - Docker Compose: 1.29.2
   - Install Lombok plugin in IDE

2. Installment

   - Execute the command `sh install-all.sh` under the directory `/project-initial/src/main/script`
     - <PERSON> and <PERSON><PERSON><PERSON> are optional.

3. Check the following services:

   | Service  | Address                      |
   | -------- | ---------------------------- |
   | Nacos    | http://localhost:8848/nacos  |
   | Sentinel | http://localhost:8090/       |
   | Zipkin   | http://localhost:9411/zipkin |
   | RabbitMQ | http://localhost:15672/      |


4. Update Passwords
   - Update your password for your local services: Redis, RabbitMQ, Nacos, etc. so that they can match with your applications' env variables.
   
5. Launch all microservices
    - Launch each SpringBoot application one by one.


6. Local development with Cloud Nacos

   - Connect to the remote Nacos.

   - Get into the directory: /apnv3-backend/project-initial/src/main/script/

   - Run `sh init-nacos-config.sh -p 8848 -t dev`

   - Set Env: NAMESPACE=dev

7. Set VM options (for JDK16 or above):

   --add-opens java.base/java.lang=ALL-UNNAMED 

   --add-opens java.base/java.lang.reflect=ALL-UNNAMED

8. Deploy with Docker (e.g. deploy company-service)
    ```
    mvn clean package -DskipTests spring-boot:repackage
    version="$(date +'%y%m%d%H%M')"
    docker buildx build --platform linux/amd64 -t minghealtomni/apn-v3-company-service:v${version} .
    docker push minghealtomni/apn-v3-company-service:v${version}
    ```

