<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264119782-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="agency"/>
            </not>
        </preConditions>
        <createTable tableName="agency">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="phone" type="VARCHAR(50)"/>
            <column name="email" type="VARCHAR(50)"/>
            <column name="agency_contact_first_name" type="VARCHAR(255)"/>
            <column name="agency_contact_last_name" type="VARCHAR(255)"/>
            <column name="agency_owner_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="agency_creator_user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(5000)"/>
            <column name="timezone" type="VARCHAR(50)"/>
            <column name="location" type="VARCHAR(200)"/>

            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_agency_agency_owner_user_id" tableName="agency">
            <column name="agency_owner_user_id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>
