<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264430697-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="agency_submit_application"/>
            </not>
        </preConditions>
        <createTable tableName="agency_submit_application">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="agency_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="job_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="talent_recruitment_process_id" type="BIGINT"/>
            <column name="talent_id" type="BIGINT"/>

            <column name="last_status" type="INT"/>

            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_agency_submit_application_agency_id" tableName="agency_submit_application">
            <column name="agency_id"/>
        </createIndex>
        <createIndex indexName="idx_agency_submit_application_job_id" tableName="agency_submit_application">
            <column name="job_id"/>
        </createIndex>
        <createIndex indexName="idx_agency_submit_application_trpid" tableName="agency_submit_application">
            <column name="talent_recruitment_process_id"/>
        </createIndex>
        <createIndex indexName="idx_agency_submit_application_talent_id" tableName="agency_submit_application">
            <column name="talent_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
