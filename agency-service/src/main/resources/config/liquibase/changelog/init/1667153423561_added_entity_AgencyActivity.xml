<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1667153423561-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="agency_activity"/>
            </not>
        </preConditions>
        <createTable tableName="agency_activity">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="agency_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="activity_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="read_status" type="INT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="details" type="TEXT"/>

            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>

<!--            &lt;!&ndash; 添加虚拟列 &ndash;&gt;-->
<!--            <column name="talent_name" type="VARCHAR(255)" generatedAlways="true" as="json_unquote(json_extract(details, '$.talentName'))"/>-->
<!--            <column name="job_title" type="VARCHAR(255)" generatedAlways="true" as="json_unquote(json_extract(details, '$.jobTitle'))"/>-->
        </createTable>

        <createIndex indexName="idx_agency_activity_agency_id" tableName="agency_activity">
            <column name="agency_id"/>
        </createIndex>

        <createIndex indexName="idx_agency_activity_activity_type" tableName="agency_activity">
            <column name="activity_type"/>
        </createIndex>

        <!-- 使用原生 SQL 添加虚拟列 -->
        <sql>
            ALTER TABLE agency_activity ADD COLUMN talent_name VARCHAR(255)
                GENERATED ALWAYS AS (json_unquote(json_extract(details, '$.talentName'))) VIRTUAL;
        </sql>

        <sql>
            ALTER TABLE agency_activity ADD COLUMN job_title VARCHAR(255)
                GENERATED ALWAYS AS (json_unquote(json_extract(details, '$.jobTitle'))) VIRTUAL;
        </sql>

        <sql>
            ALTER TABLE agency_activity ADD COLUMN job_id BIGINT
                GENERATED ALWAYS AS (json_unquote(json_extract(details, '$.jobId'))) VIRTUAL;
        </sql>
    </changeSet>

</databaseChangeLog>
