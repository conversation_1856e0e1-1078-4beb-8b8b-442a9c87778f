<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264449736-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="agency_submit_duplicate_talent"/>
            </not>
        </preConditions>
        <createTable tableName="agency_submit_duplicate_talent">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="agency_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="job_id" type="BIGINT"/>
            <column name="original_talent_json" type="TEXT"/>
            <column name="merged_talent_id" type="BIGINT"/>
            <column name="duplicate_talent_ids" type="VARCHAR(1024)"/>

            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_agency_submit_duplicate_talent_agency_id" tableName="agency_submit_duplicate_talent">
            <column name="agency_id"/>
        </createIndex>
        <createIndex indexName="idx_agency_submit_duplicate_talent_merged_talent_id" tableName="agency_submit_duplicate_talent">
            <column name="merged_talent_id"/>
        </createIndex>
        <createIndex indexName="idx_agency_submit_duplicate_talent_job_id" tableName="agency_submit_duplicate_talent">
            <column name="job_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
