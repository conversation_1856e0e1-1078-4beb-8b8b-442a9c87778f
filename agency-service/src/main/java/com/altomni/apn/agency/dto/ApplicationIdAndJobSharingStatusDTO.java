package com.altomni.apn.agency.dto;

import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * A AgencyApplicationStatsDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationIdAndJobSharingStatusDTO {

    private Long applicationId;

    private JobShareStatus jobShareStatus;

    private NodeType lastStatus;

}
