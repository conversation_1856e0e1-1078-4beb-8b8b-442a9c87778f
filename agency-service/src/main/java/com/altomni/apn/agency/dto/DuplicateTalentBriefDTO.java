package com.altomni.apn.agency.dto;

import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * A TalentBriefDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DuplicateTalentBriefDTO {

    private List<TalentDTOV3> existingTalents;

    @JsonRawValue
    private String duplicateTalent;

}
