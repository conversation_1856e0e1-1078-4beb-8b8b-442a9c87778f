package com.altomni.apn.agency.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessNodeVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A RecruitmentProcessNode.
 */
@Entity
@Table(name = "agency_sharing_user")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AgencySharingUser extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 3821512723147087243L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "agency_id")
    private Long agencyId;

    @Column(name = "user_id")
    private Long userId;

    public AgencySharingUser(Long agencyId, Long userId) {
        this.agencyId = agencyId;
        this.userId = userId;
    }

}
