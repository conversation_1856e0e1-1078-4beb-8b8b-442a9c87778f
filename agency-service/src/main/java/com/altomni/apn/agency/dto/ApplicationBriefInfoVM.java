package com.altomni.apn.agency.dto;

import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationBriefInfoVM {
    private Long applicationId;
    private NodeType lastNodeType;
    private NodeStatus lastNodeStatus;
    private Long talentId;
    private Long jobId;
    private String talentName;
    private String jobTitle;
    private Long agencyId;
}
