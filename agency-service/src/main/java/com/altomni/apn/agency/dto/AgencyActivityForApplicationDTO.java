package com.altomni.apn.agency.dto;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * A AgencyActivityDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencyActivityForApplicationDTO {

    private Long talentId;

    private String talentName;

    private Long jobId;

    private String jobTitle;

    private boolean isPrivateJob;

    private String privateJob;

    private Long applicationId;

    private NodeType nodeType;

}
