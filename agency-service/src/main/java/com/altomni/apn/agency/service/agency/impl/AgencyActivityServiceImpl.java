package com.altomni.apn.agency.service.agency.impl;

import cn.hutool.core.util.NumberUtil;
import com.altomni.apn.agency.domain.Agency;
import com.altomni.apn.agency.domain.AgencyActivity;
import com.altomni.apn.agency.domain.AgencySubmitApplication;
import com.altomni.apn.agency.dto.AgencyActivityDTO;
import com.altomni.apn.agency.dto.AgencyActivityForApplicationDTO;
import com.altomni.apn.agency.dto.SimpleUserInfoDTO;
import com.altomni.apn.agency.repository.*;
import com.altomni.apn.agency.service.agency.AgencyActivityService;
import com.altomni.apn.agency.service.user.UserService;
import com.altomni.apn.agency.web.rest.vm.AgencyActivityVO;
import com.altomni.apn.common.domain.enumeration.agency.AgencyActivityType;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing AgencyActivity.
 */
@Service
@Transactional
public class AgencyActivityServiceImpl implements AgencyActivityService {

    private final Logger log = LoggerFactory.getLogger(AgencyActivityServiceImpl.class);

    private static final Set<AgencyActivityType> AGENCY_ACTIVITY_TYPE_SET = Collections.unmodifiableSet(new HashSet<>() {{
        add(AgencyActivityType.APPLICATION_UPDATE);
        add(AgencyActivityType.EMAIL_CONTACT);
        add(AgencyActivityType.JOB_SHARE);
    }});

    @Resource
    private AgencyRepository agencyRepository;

    @Resource
    private AgencyActivityRepository agencyActivityRepository;

    @Resource
    private UserService userService;

    @Resource
    private AgencySubmitApplicationRepository agencySubmitApplicationRepository;

    @Override
    public Page<AgencyActivityVO> getActivities(Long agencyId, String search, Pageable pageable) {
        Page<AgencyActivity> res = agencyActivityRepository.getAllByAgencyId(agencyId, search, pageable);

        List<AgencyActivityVO> result = res.stream().map(AgencyActivityVO::new).collect(Collectors.toList());

        Set<Long> userIds = result.stream().map(activity -> SecurityUtils.getUserIdFromCreatedBy(activity.getCreatedBy())).collect(Collectors.toSet());
        List<UserBriefDTO> userBriefList =  userService.findBriefUsers(userIds);
        Map<Long, SimpleUserInfoDTO> userMap = userBriefList.stream().map(SimpleUserInfoDTO::new).collect(Collectors.toMap(SimpleUserInfoDTO::getId, Function.identity()));

        Set<Long> agencyIds = result.stream().map(AgencyActivityVO::getAgencyId).collect(Collectors.toSet());
        Map<Long, String> agencyMap = getAllAgencyNameByIds(agencyIds);

        result.forEach(activity -> {
            activity.setCreator(userMap.get(SecurityUtils.getUserIdFromCreatedBy(activity.getCreatedBy())));
            activity.setAgencyName(agencyMap.get(activity.getAgencyId()));
        });


        return new PageImpl<>(result, pageable, res.getTotalElements());
    }

    @Override
    public Page<AgencyActivityVO> getActivitiesByJobId(Long jobId, Pageable pageable) {
        Page<AgencyActivity> res = agencyActivityRepository.getAllByAgencyIdAndJobId(jobId, pageable);

        List<AgencyActivityVO> result = res.stream().map(AgencyActivityVO::new).collect(Collectors.toList());

        Set<Long> userIds = result.stream().map(activity -> SecurityUtils.getUserIdFromCreatedBy(activity.getCreatedBy())).collect(Collectors.toSet());
        List<UserBriefDTO> userBriefList =  userService.findBriefUsers(userIds);
        Map<Long, SimpleUserInfoDTO> userMap = userBriefList.stream().map(SimpleUserInfoDTO::new).collect(Collectors.toMap(SimpleUserInfoDTO::getId, Function.identity()));

        Set<Long> agencyIds = result.stream().map(AgencyActivityVO::getAgencyId).collect(Collectors.toSet());
        Map<Long, String> agencyMap = getAllAgencyNameByIds(agencyIds);

        result.forEach(activity -> {
            activity.setCreator(userMap.get(SecurityUtils.getUserIdFromCreatedBy(activity.getCreatedBy())));
            activity.setAgencyName(agencyMap.get(activity.getAgencyId()));
        });


        return new PageImpl<>(result, pageable, res.getTotalElements());
    }

    @Override
    public Page<AgencyActivityVO> getALlUnreadActivities(Instant startTime, Instant endTime, Long agencyId, Pageable pageable) {
        Page<AgencyActivity> res = null;
        if (Objects.nonNull(startTime) && Objects.nonNull(endTime)) {
            res = agencyActivityRepository.getAllByAgencyIdAndReadStatusAndActivityTypeInAndCreatedDateBetween(agencyId, false, AGENCY_ACTIVITY_TYPE_SET, startTime, endTime, pageable);
        } else {
            res = agencyActivityRepository.getAllByAgencyIdAndReadStatusAndActivityTypeIn(agencyId, false, AGENCY_ACTIVITY_TYPE_SET, pageable);
        }

        List<AgencyActivityVO> result = res.stream().map(AgencyActivityVO::new).collect(Collectors.toList());
        return new PageImpl<>(result, pageable, res.getTotalElements());
    }

    @Override
    public AgencyActivityVO createActivity(AgencyActivityDTO activityDTO) {
        AgencyActivity agencyActivity = new AgencyActivity(activityDTO);
        agencyActivity = agencyActivityRepository.save(agencyActivity);
        return new AgencyActivityVO(agencyActivity);
    }

    @Override
    public AgencyActivityVO createActivityForApplication(AgencyActivityForApplicationDTO agencyActivityForApplicationDTO) {
        AgencySubmitApplication agencySubmitApplication = agencySubmitApplicationRepository.findByTalentRecruitmentProcessId(agencyActivityForApplicationDTO.getApplicationId()).orElseThrow(() -> new CustomParameterizedException("Cannot find application: " + agencyActivityForApplicationDTO.getApplicationId()));
        if (!agencySubmitApplication.getJobId().equals(agencyActivityForApplicationDTO.getJobId()) || !agencySubmitApplication.getTalentId().equals(agencyActivityForApplicationDTO.getTalentId())) {
            throw new CustomParameterizedException("Incomplete data for agency activity!");
        }
        AgencyActivity agencyActivity = new AgencyActivity();
        agencyActivity.setAgencyId(agencySubmitApplication.getAgencyId());
        agencyActivity.setActivityType(AgencyActivityType.APPLICATION_UPDATE);
        agencyActivity.setReadStatus(false);

        com.alibaba.fastjson.JSONObject details = new com.alibaba.fastjson.JSONObject();
        details.fluentPut("node", agencyActivityForApplicationDTO.getNodeType().toString());
        details.fluentPut("talentRecruitmentProcessId", agencyActivityForApplicationDTO.getApplicationId());
        details.fluentPut("jobId", agencyActivityForApplicationDTO.getJobId());
        details.fluentPut("jobTitle", agencyActivityForApplicationDTO.getJobTitle());
        details.fluentPut("isPrivateJob", agencyActivityForApplicationDTO.isPrivateJob());
        details.fluentPut("talentId", agencyActivityForApplicationDTO.getTalentId());
        details.fluentPut("talentName", agencyActivityForApplicationDTO.getTalentName());
        agencyActivity.setDetails(details.toJSONString());

        agencyActivity = agencyActivityRepository.saveAndFlush(agencyActivity);
        return new AgencyActivityVO(agencyActivity);
    }

    @Override
    public List<AgencyActivityVO> saveAll(List<AgencyActivityDTO> activityDTOs) {
        List<AgencyActivity> agencyActivityList = activityDTOs.stream().map(AgencyActivity::new).collect(Collectors.toList());
        agencyActivityList = agencyActivityRepository.saveAll(agencyActivityList);
        return agencyActivityList.stream().map(AgencyActivityVO::new).collect(Collectors.toList());
    }

    @Override
    public void markAsRead(List<Long> activityIds) {
        List<AgencyActivity> activities = agencyActivityRepository.findAllById(activityIds);

        Long agencyId = SecurityUtils.getAgencyId();
        List<AgencyActivity> activityList = activities.stream().filter(a -> a.getAgencyId().equals(agencyId)).map(a -> {a.setReadStatus(true); return a;}).collect(Collectors.toList());
        agencyActivityRepository.saveAll(activityList);
    }

    private Map<Long, String> getAllAgencyNameByIds(Set<Long> agencyIds) {
        return agencyRepository.findAllByIdIn(agencyIds).stream().collect(Collectors.toMap(Agency::getId, Agency::getName));
    }

}
