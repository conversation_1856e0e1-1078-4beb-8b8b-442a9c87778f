package com.altomni.apn.agency.service.application.impl;

import com.altomni.apn.agency.dto.ApplicationBriefInfoVM;
import com.altomni.apn.agency.dto.InProcessApplicationBriefVM;
import com.altomni.apn.agency.service.application.ApplicationClient;
import com.altomni.apn.agency.service.application.ApplicationService;
import com.altomni.apn.agency.web.rest.vm.ApplicationIdAndStatusListVO;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

@Service
@Transactional
public class ApplicationServiceImpl implements ApplicationService {

    @Resource
    private ApplicationClient applicationClient;

    @Override
    public Integer countUnfinishedApplicationsByIds(List<Long> applicationIds) {
        ResponseEntity<Integer> response = applicationClient.countUnfinishedApplicationsByIds(applicationIds);
        return response != null ? response.getBody() : null;
    }

    @Override
    public TalentRecruitmentProcessVO submitToJob(TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
        ResponseEntity<TalentRecruitmentProcessVO> response = applicationClient.submitToJob(submitToJobVO);
        return response != null ? response.getBody() : null;
    }

    @Override
    public ApplicationIdAndStatusListVO getAllApplicationsStatus(Set<Long> applicationIds, NodeType nodeType, Pageable pageable) {
        ResponseEntity<ApplicationIdAndStatusListVO> response = applicationClient.getStats(applicationIds, nodeType, pageable);
        return response != null ? response.getBody() : null;
    }

    @Override
    public LinkedHashMap<String, Integer> getMyApplicationCandidates(Instant startTime, Instant endTime, List<Long> userIdList, List<Long> applicationIds) {
        ResponseEntity<LinkedHashMap<String, Integer>> response = applicationClient.getMyApplicationCandidates(startTime, endTime, userIdList, applicationIds);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<InProcessApplicationBriefVM> getUnfinishedApplicationsByIds(Set<Long> applicationIds) {
        ResponseEntity<List<InProcessApplicationBriefVM>> response = applicationClient.getUnfinishedApplicationsByIds(applicationIds);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<ApplicationBriefInfoVM> getApplicationsBriefByIds(Set<Long> applicationIds) {
        ResponseEntity<List<ApplicationBriefInfoVM>> response = applicationClient.getApplicationsBriefInoByIds(applicationIds);
        return response != null ? response.getBody() : null;
    }
}
