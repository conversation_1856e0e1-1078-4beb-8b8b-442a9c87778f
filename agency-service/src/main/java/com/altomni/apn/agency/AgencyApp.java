package com.altomni.apn.agency;

import com.altomni.apn.common.auth.agency_auth.JwtAgencyUserTokenStore;
import com.altomni.apn.common.config.*;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.config.env.CommonApplicationProperties;
import com.altomni.apn.common.config.loadbalancer.LoadBalanceConfiguration;
import com.altomni.apn.common.datapermission.config.DataPermissionAutoConfiguration;
import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.errors.ExceptionTranslator;
import com.altomni.apn.common.interceptor.CustomResponseBodyAdviceAdapter;
import com.altomni.apn.common.interceptor.FeignClientInterceptor;
import com.altomni.apn.common.interceptor.SecurityObjectLevelInterceptor;
import com.altomni.apn.common.repository.agency.SimpleAgencyRepository;
import com.altomni.apn.common.repository.user.CustomUserRepository;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CachedFeignSsoUserMapping;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.canal.impl.CanalServiceImpl;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.service.http.impl.HttpServiceImpl;
import com.altomni.apn.common.service.log.impl.LoggingServiceImpl;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SpringUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableJpaRepositories("com.altomni.apn.*.repository")
@EntityScan(value = {"com.altomni.apn.*.domain", "com.altomni.apn.*.web.rest.vm"})
@EnableFeignClients(basePackages = {"com.altomni.apn"})
@SpringBootApplication
@EnableDiscoveryClient
@EnableScheduling
@LoadBalancerClients(defaultConfiguration = LoadBalanceConfiguration.class)
@Import({ExceptionTranslator.class,
        PublicBeanInjection.class,
        HttpServiceImpl.class,
        DataPermissionAutoConfiguration.class,
        CachePermission.class,
        CachedFeignSsoUserMapping.class,
        CanalServiceImpl.class,
        CommonRedisService.class,
        CommonApplicationProperties.class,
        ApplicationIPGProperties.class,
        TeamDataPermissionRule.class,
        FeignClientInterceptor.class,
        SecurityObjectLevelInterceptor.class,
//        WebMvcConfig.class,
//        SecurityDataLevelInterceptor.class,
        CustomResponseBodyAdviceAdapter.class,
        LoggingServiceImpl.class,
        SecurityUtils.class,
        AppInit.class,
        GlobalCacheConfig.class,
        JacksonConfiguration.class,
        CacheConfig.class,
        SpringUtil.class,
        EnumCommonService.class,
        EnumCurrencyService.class,
        CommonApiMultilingualConfig.class
        })
public class AgencyApp {

    public static void main(String[] args) {
        SpringApplication.run(AgencyApp.class, args);
    }
}

