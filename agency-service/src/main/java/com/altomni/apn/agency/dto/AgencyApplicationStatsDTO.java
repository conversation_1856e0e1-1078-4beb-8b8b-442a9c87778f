package com.altomni.apn.agency.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * A AgencyApplicationStatsDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencyApplicationStatsDTO {

    private Long agencyId;

    private List<Long> talentIds;

    private Integer ongoingApplicationCount;

    private Instant latestSubmissionDate;

}
