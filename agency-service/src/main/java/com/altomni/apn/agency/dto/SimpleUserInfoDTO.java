package com.altomni.apn.agency.dto;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SimpleUserInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String fullName;

    private String firstName;

    private String lastName;

    public SimpleUserInfoDTO(UserBriefDTO userBriefDTO) {
        this.id = userBriefDTO.getId();
        this.firstName = userBriefDTO.getFirstName();
        this.lastName = userBriefDTO.getLastName();
        this.fullName = userBriefDTO.getFullName();
    }
}
