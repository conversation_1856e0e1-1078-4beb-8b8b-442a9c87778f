package com.altomni.apn.agency.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum EmploymentStatus implements ConvertedEnum<Integer> {


    ONBOARDED(0), // 入职
    WITHIN_WARRANTY_DATE(1), // 保证期内
    EMPLOYED(2); // 在职

    private final int dbValue;
    EmploymentStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<EmploymentStatus, Integer> resolver =
        new ReverseEnumResolver<>(EmploymentStatus.class, EmploymentStatus::toDbValue);

    public static EmploymentStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
