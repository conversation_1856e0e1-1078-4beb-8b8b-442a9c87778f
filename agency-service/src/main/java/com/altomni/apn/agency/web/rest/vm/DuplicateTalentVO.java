package com.altomni.apn.agency.web.rest.vm;

import com.altomni.apn.agency.dto.TalentBriefDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DuplicateTalentVO {

    private Long agencySubmitDuplicateTalentId;

    private Long duplicateId;

    private List<TalentBriefDTO> duplicateTalents;

    private String firstName;

    private String lastName;

    private String fullName;

    private Long mergeToTalentId;

}
