package com.altomni.apn.agency.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A RecruitmentProcessNodePageSection.
 */
@Entity
@Table(name = "agency_submit_application")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AgencySubmitApplication extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5070679567300167427L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "agency_id")
    private Long agencyId;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "last_status")
    private NodeType lastStatus;

}
