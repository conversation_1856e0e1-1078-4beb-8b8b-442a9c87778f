package com.altomni.apn.agency.service.sync.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.altomni.apn.agency.config.ScheduledProperties;
import com.altomni.apn.agency.config.env.AgencyMQProperties;
import com.altomni.apn.agency.config.thread.AgencySyncToMqThreadTask;
import com.altomni.apn.agency.service.esfiller.EsFillerAgencyService;
import com.altomni.apn.agency.service.redis.RedisService;
import com.altomni.apn.agency.service.sync.AgencySyncService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.NotificationUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.*;

@Service
public class AgencySyncServiceImpl implements AgencySyncService {

    private final Logger log = LoggerFactory.getLogger(AgencySyncServiceImpl.class);

    @Resource
    private EsFillerAgencyService esFillerAgencyService;

    @Resource
    private ScheduledProperties properties;

    @Resource
    private AgencyMQProperties agencyMQProperties;

    @Resource
    private RedisService redisService;

    @Resource
    private CanalService canalService;

    private static final String JOB_RUNNING = "JOB_RUNNING";

    private volatile ExecutorService executorService;

    private static Integer localThreadNum = 0;

    private ExecutorService getExecutorService() {
        if (executorService == null) {
            synchronized (AgencySyncServiceImpl.class) {
                if (executorService == null) {
                    localThreadNum = properties.getThreadNum();
                    executorService = new ThreadPoolExecutor(
                            properties.getThreadNum(),
                            properties.getThreadNum() * 2,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-scheduled-sync-job-to-es", false));
                }
            }
        }
        if (!Objects.equals(localThreadNum, properties.getThreadNum())) {
            log.info("jobThreadSize refresh = [{}]", properties.getThreadNum());
            executorService.shutdownNow();
            executorService = null;
            return getExecutorService();
        }
        return executorService;
    }

    @Override
    public void syncAgenciesToMQ(Collection<Long> agencyIds, int priority) {
        log.info("[syncAgenciesToMQ] sync agencies start at: {}", LocalDateTime.now());

        List<List<Long>> agencyGroups = CollUtil.split(agencyIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(agencyGroups.size());
        agencyGroups.forEach(ids -> getExecutorService().execute(new AgencySyncToMqThreadTask(esFillerAgencyService, redisService, countDownLatch, ids, priority, agencyMQProperties, canalService)));

        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[syncAgenciesToMQ] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(agencyIds, SyncIdTypeEnum.AGENCY, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[syncAgenciesToMQ] countDownLatch Error" +
                    "\n\tAgency ID: " + agencyIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(agencyMQProperties.getLarkWebhookKey(), agencyMQProperties.getLarkWebhookUrl(), message);
        }

        log.info("[syncAgenciesToMQ] Sync Agencies to MQ Done!");
    }

}
