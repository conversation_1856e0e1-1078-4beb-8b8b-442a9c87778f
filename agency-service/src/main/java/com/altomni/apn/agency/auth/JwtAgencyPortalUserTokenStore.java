package com.altomni.apn.agency.auth;

import cn.hutool.core.convert.Convert;
import com.altomni.apn.common.auth.agency_auth.AgencyUserTokenStore;
import com.altomni.apn.common.domain.agency.SimpleAgency;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.dto.user.AgencyLoginUserDTO;
import com.altomni.apn.common.dto.user.RoleDTO;
import com.altomni.apn.common.dto.user.TokenAndAgencyLoginUserDTO;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.UserNotActivatedException;
import com.altomni.apn.common.repository.agency.SimpleAgencyRepository;
import com.altomni.apn.common.repository.user.CustomUserRepository;
import com.altomni.apn.common.utils.JsonUtil;
import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jose.crypto.MACVerifier;
import com.nimbusds.jwt.JWTClaimNames;
import com.nimbusds.jwt.JWTClaimsSet;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class JwtAgencyPortalUserTokenStore implements AgencyUserTokenStore {

    @Value("${application.oauth2.jwtSignKey}")
    private String SIGN_KEY;

    private final CustomUserRepository userRepository;

    private final SimpleAgencyRepository agencyRepository;

//    public JwtAgencyUserTokenStore(SimpleUserRepository simpleUserRepository, SimpleAgencyRepository agencyRepository) {
//        this.userRepository = simpleUserRepository;
//        this.agencyRepository = agencyRepository;
//    }


    @Override
    public TokenAndAgencyLoginUserDTO getUser(String token) throws JOSEException, ParseException {
        if (!verifyToken(token)) {
            return null;
        }
        JWSObject jwsObject = JWSObject.parse(token);
        Map<String, Object> claimsMap = jwsObject.getPayload().toJSONObject();
//        AgencyLoginUserDTO agencyUser = JsonUtil.fromJson(JsonUtil.toJson(claimsMap), AgencyLoginUserDTO.class);
        Long agencyId = (Long) claimsMap.get("id");

        SimpleAgency agency = agencyRepository.findById(agencyId).orElseThrow(() -> new CustomParameterizedException("Agency not found"));
        if (ActiveStatus.INACTIVE.equals(agency.getStatus())) {
//            throw new CustomParameterizedException("User is not activated");
            return null;
        }
//        SimpleUser simpleUser = userRepository.findById(agency.getAgencyOwnerUserId()).orElseThrow(() -> new CustomParameterizedException("User not found"));
        LoginUserDTO userDTO = getUser(agency.getAgencyOwnerUserId()).orElseThrow(() -> new CustomParameterizedException("User not found"));

        //copy attributes from simpleUser to user
//        BeanUtils.copyProperties(simpleUser, agencyUser);
        AgencyLoginUserDTO agencyUser = AgencyLoginUserDTO.fromOwnerUser(userDTO);

        agencyUser.setRoles(userDTO.getRoles().stream().map(RoleDTO::getName).collect(Collectors.toSet()));
        agencyUser.setAgencyId(agencyId);
        agencyUser.setAgencyName(agency.getName());
        agencyUser.setAgencyContactName(agency.getAgencyContactFirstName() + " " + agency.getAgencyContactLastName());

        String newGeneratedInternalToken = generateToken(agencyUser, 3600);
//        SecurityUtils.getUserId()

        return new TokenAndAgencyLoginUserDTO(newGeneratedInternalToken, agencyUser);
    }

    private boolean verifyToken(String token) throws JOSEException, ParseException {
        // 解析JWT
        JWSObject jwsObject = JWSObject.parse(token);

        // 验证签名是否有效
        JWSVerifier verifier = new MACVerifier(SIGN_KEY);

        // 如果签名验证通过，继续处理
        if (jwsObject.verify(verifier)) {
            // 解析JWT的负载（claims）
            Payload payload = jwsObject.getPayload();
            Map<String, Object> jsonObject = payload.toJSONObject();

            // 处理claims，验证过期时间等信息
            JWTClaimsSet claimsSet = JWTClaimsSet.parse(jsonObject);

            // 检查是否过期
            Date expirationTime = claimsSet.getExpirationTime();
            if (expirationTime != null && new Date().before(expirationTime)) {
                // 令牌未过期，验证通过
                return true;
            } else {
                // 令牌已过期
                return false;
            }
        } else {
            // 签名验证失败
            return false;
        }
    }

    private Optional<LoginUserDTO> getUser(Long userId) {
        return userRepository.findOneWithRolesById(userId)
                .map(user -> {
                    List<RoleDTO> roles = user.getRoles().stream().map(role -> Convert.convert(RoleDTO.class, role)).toList();
                    user.setTeamId(userRepository.findPrimaryTeamId(user.getId()));
                    LoginUserDTO loginUserDTO = new LoginUserDTO();
                    BeanUtils.copyProperties(user, loginUserDTO);
                    loginUserDTO.setRoles(roles);
                    List<Long> companyIds = userRepository.findCompanyIdsByUserIdWithAm(user.getId());
                    loginUserDTO.setCompanyIdsWithAm(companyIds);
                    loginUserDTO.setUserType(TenantUserTypeEnum.HEADHUNTER);

                    return loginUserDTO;
                });
    }

    private String generateToken(AgencyLoginUserDTO user, Integer expire) throws JOSEException, ParseException {

        Map<String, Object> claims = JsonUtil.fromJson(JsonUtil.toJson(user), LinkedHashMap.class);
        claims.put(JWTClaimNames.EXPIRATION_TIME, expire);
        // 创建JWT头部
        JWSHeader header = new JWSHeader(JWSAlgorithm.HS256);

        // 创建JWT有效载荷
        JWTClaimsSet claimSet = JWTClaimsSet.parse(claims);

        // 创建并签名JWT
        JWSObject jwsObject = new JWSObject(header, new Payload(claimSet.toJSONObject()));

        // 使用HMAC密钥进行签名
        jwsObject.sign(new MACSigner(SIGN_KEY));

        // 生成JWT字符串
        String jwt = jwsObject.serialize();

        return jwt;
    }

}
