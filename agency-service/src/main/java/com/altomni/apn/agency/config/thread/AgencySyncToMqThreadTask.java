package com.altomni.apn.agency.config.thread;

import com.altomni.apn.agency.config.env.AgencyMQProperties;
import com.altomni.apn.agency.service.esfiller.EsFillerAgencyService;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.agency.service.redis.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.util.StopWatch;

import java.util.List;
import java.util.concurrent.CountDownLatch;

@Slf4j
public class AgencySyncToMqThreadTask extends CopyTokenChildThread {

    private final EsFillerAgencyService esFillerAgencyService;

    private final RedisService redisService;

    private final CountDownLatch countDownLatch;

    private final List<Long> ids;

    private final int priority;

    private final AgencyMQProperties esfillerMQProperties;

    private final CanalService canalService;

    public AgencySyncToMqThreadTask(EsFillerAgencyService esFillerAgencyService, RedisService redisService, CountDownLatch countDownLatch, List<Long> ids, int priority, AgencyMQProperties esfillerMQProperties, CanalService canalService) {
        super();
        this.esFillerAgencyService = esFillerAgencyService;
        this.redisService = redisService;
        this.countDownLatch = countDownLatch;
        this.ids = ids;
        this.priority = priority;
        this.esfillerMQProperties = esfillerMQProperties;
        this.canalService = canalService;
    }

    @Override
    public void runTask() {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        //List<Long> failedIds = new ArrayList<>();
        try {
            esFillerAgencyService.extractAgencyToMq(ids, priority);
        } catch (Exception e) {
            log.error("[EsFillerJobService: syncJobToAgencyMQ @{}] SyncJobsToAgencyMQ is error, jobIds: {}, error: {}", SecurityUtils.getUserId(), ids, ExceptionUtils.getStackTrace(e));
            canalService.insertAll(ids, SyncIdTypeEnum.AGENCY, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "Extract Agency Job Error" +
                    "\n\tJob IDs: " + ids +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        } finally {
            countDownLatch.countDown();
            stopWatch.stop();
            //log.info("[EsFillerJobService: syncJobToMQ @{}] SyncJobsToMQFinished time = [{}ms], successIds = [{}], failIds = [{}]", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), CollUtil.disjunction(ids, failedIds), failedIds);
        }
    }


}
