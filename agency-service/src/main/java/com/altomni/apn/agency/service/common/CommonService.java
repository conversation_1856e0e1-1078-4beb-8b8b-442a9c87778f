package com.altomni.apn.agency.service.common;

import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.redis.ParserResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(value = "common-service")
public interface CommonService {

    @PostMapping("/common/api/v3/campaign/send_html_mail")
    ResponseEntity<Long> sendHtmlMail(@RequestBody MailVM mailVM);

    @PostMapping( "/common/api/v3/campaign/mail/save-draft")
    ResponseEntity<String> saveDraft(@RequestBody MailVM mailVM);

    @PostMapping("/common/api/v3/campaign/mail/send-draft")
    ResponseEntity<String> sendDraft(@RequestBody MailVM mailVM);

    @GetMapping("/common/api/v3/campaign/mail/list/{id}/ignore-userId")
    ResponseEntity<String> getMailById(@PathVariable("id") Long id);

    @GetMapping("/common/api/v3/parsers/resume/status")
    ResponseEntity<ParserResponse> checkResumeParseResultOrGetUploadUrl(@RequestParam("uuid") String uuid, @RequestParam("priority") Integer priority, @RequestParam("fileName") String fileName, @RequestParam("contentType") String contentType);

    @GetMapping("/common/api/v3/parsers/resume/result-status/{uuid}")
    ResponseEntity<ParserResponse> getResumeParseResultStatusOnly(@PathVariable("uuid") String uuid);

    @GetMapping("/common/api/v3/parsers/resume/result/{uuid}")
    ResponseEntity<ParserResponse> getParserResumeResult(@PathVariable("uuid") String uuid);

    @GetMapping("/common/api/v3/parsers/resume/info/{uuid}")
    ResponseEntity<ParserResponse> getParserResumeInfo(@PathVariable("uuid") String uuid);

}
