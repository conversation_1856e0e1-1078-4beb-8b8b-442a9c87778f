package com.altomni.apn.agency.dto;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class AgencySyncEsDocument {

    private Long _id;

    private String _tenant_id;

    private String _type;

//    private String _return_routing_key;

    private JSONObject _source;

    @Data
    public static class Source {

        private Long id;
        private String agencyName;
        private Boolean active;
        private String agencyContactName; //TODO
        private String agencyContactFirstName;
        private String agencyContactLastName;
        private String agencyContactFullName;

        private List<Contact> contacts;
//        private List<Location> locations;

        private String note;

        private String createdDate;
        private String lastModifiedDate;

        private List<Responsibility> responsibility0; // agency creator
        private List<Responsibility> responsibility1; // agency owner
        private List<Responsibility> responsibility2; // agency sharing users
        private List<Responsibility> responsibility3;
        private List<Responsibility> responsibility4;
        private List<Responsibility> responsibility5;
        private List<Responsibility> responsibility6;
        private List<Responsibility> responsibility7;
        private List<Responsibility> responsibility8;
        private List<Responsibility> responsibility9;
        private List<Responsibility> responsibility10;
        private List<Responsibility> responsibility11;
        private List<Responsibility> responsibility12;
        private List<Responsibility> responsibility13;
        private List<Responsibility> responsibility14;
        private List<Responsibility> responsibility15;
        private List<Responsibility> responsibility16;
        private List<Responsibility> responsibility17;
        private List<Responsibility> responsibility18;
        private List<Responsibility> responsibility19;
        private List<Responsibility> responsibility20;
    }

    @Data
    public static class Location {
        private String location;
        private String addressLine;
        private String city;
        private String province;
        private String county;
        private String zipcode;
        private String country;
        private String originDisplay;
        private String id;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Responsibility {
        private String id;
        private String name;
    }

    @Data
    public static class Contact {
        private String contact;
        private String type;
    }

}
