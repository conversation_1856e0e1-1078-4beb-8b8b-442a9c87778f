package com.altomni.apn.agency.service.message;

import com.altomni.apn.common.dto.message.MessageCreateWithTalentInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "common-service")
public interface MessageClientService {

    @PostMapping("/common/api/v3/message/talent-info-update")
    ResponseEntity<Void> createMessageWithTalentInfoUpdate(@RequestBody MessageCreateWithTalentInfoDTO messageCreateDTO);

}
