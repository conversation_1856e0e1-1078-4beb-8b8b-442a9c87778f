package com.altomni.apn.agency.service.talent.impl;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.agency.domain.Agency;
import com.altomni.apn.agency.domain.AgencySharedJob;
import com.altomni.apn.agency.domain.AgencySubmitApplication;
import com.altomni.apn.agency.domain.AgencySubmitDuplicateTalent;
import com.altomni.apn.agency.dto.AgencyActivityDTO;
import com.altomni.apn.agency.dto.TalentInfoInputWithId;
import com.altomni.apn.agency.dto.TalentInfoInputWithJobIdForApplicationDTO;
import com.altomni.apn.agency.repository.AgencyRepository;
import com.altomni.apn.agency.repository.AgencySharedJobRepository;
import com.altomni.apn.agency.repository.AgencySubmitApplicationRepository;
import com.altomni.apn.agency.repository.AgencySubmitDuplicateTalentRepository;
import com.altomni.apn.agency.service.agency.AgencyActivityService;
import com.altomni.apn.agency.service.agency.AgencyService;
import com.altomni.apn.agency.service.application.ApplicationService;
import com.altomni.apn.agency.service.job.JobService;
import com.altomni.apn.agency.service.talent.TalentClient;
import com.altomni.apn.agency.service.talent.TalentService;
import com.altomni.apn.common.domain.enumeration.agency.AgencyActivityType;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.DuplicateException;
import com.altomni.apn.common.errors.WithDataException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.altomni.apn.common.config.constants.RedisConstants.AGENCY_NEW_APPLICATION_KEY_PATTERN;

@Service
public class TalentServiceImpl implements TalentService {

    private final Logger log = LoggerFactory.getLogger(TalentServiceImpl.class);

    @Resource
    private TalentClient talentClient;

    @Resource
    private JobService jobService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private AgencySubmitApplicationRepository agencySubmitApplicationRepository;

    @Resource
    private AgencySubmitDuplicateTalentRepository agencySubmitDuplicateTalentRepository;

    @Resource
    private AgencySharedJobRepository agencySharedJobRepository;

    @Resource
    private AgencyRepository agencyRepository;

    @Resource
    private AgencyActivityService agencyActivityService;

    @Resource
    private AgencyService agencyService;

    @Override
    public TalentDTOV3 getTalentById(Long id) {
        ResponseEntity<TalentDTOV3> response = talentClient.getTalent(id);
        return response != null ? response.getBody() : null;
    }


    @Override
    public TalentDTOV3 getTalentWithoutEntity(Long id) {
        ResponseEntity<TalentDTOV3> response = talentClient.getTalentWithoutEntity(id);
        return response != null ? response.getBody() : null;
    }

    @Override
    public TalentBriefDTO getTalentBrief(Long id) {
        ResponseEntity<List<TalentBriefDTO>> response = talentClient.getTalentWithoutEntityList(Set.of(id));
        return response != null && response.getBody() != null ? response.getBody().stream().findFirst().orElse(null) : null;
    }

    @Override
    public List<TalentBriefDTO> getTalentBriefByTalentIds(Collection<Long> ids) {
        Set<Long> talentIds = new HashSet<>(ids);
        ResponseEntity<List<TalentBriefDTO>> response = talentClient.getTalentWithoutEntityList(talentIds);
        return response != null && response.getBody() != null ? response.getBody() : null;
    }

    @Override
    public List<TalentOwnership> getAllTalentOwners(Long talentId, List<TalentOwnershipType> talentOwnershipType) {
        ResponseEntity<List<TalentOwnership>> response = talentClient.getAllTalentOwners(talentId, talentOwnershipType);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<TalentOwnership> saveAllOwnerships(List<TalentOwnership> talentOwnerships) {
        ResponseEntity<List<TalentOwnership>> response = talentClient.saveAllOwnerships(talentOwnerships);
        return response != null ? response.getBody() : null;
    }

    @Override
    public TalentResumeDTO getTalentResumeByTalentResumeRelationId(Long relationId) {
        ResponseEntity<TalentResumeDTO> response = talentClient.getTalentResumeByTalentResumeRelationId(relationId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public void updateTalentExperience(Long talentId, TalentExperienceDTO talentExperienceDTO) {
        talentClient.updateTalentExperience(talentId, talentExperienceDTO);
    }

    @Override
    public void deleteTalentExperience(Long talentId, Long talentRecruitmentProcessId) {
        talentClient.deleteTalentExperience(talentId, talentRecruitmentProcessId);
    }

    @Override
    public void deleteTalentNote(Long noteId) {
        talentClient.deleteTalentNote(noteId);
    }

    @Override
    public List<TalentNoteDTO> getAllNotesByTalentId(Long talentId) {
        AgencySubmitApplication submitApplication = agencySubmitApplicationRepository.findByTalentId(talentId).orElseThrow(() -> new CustomParameterizedException("Cannot find talent: " + talentId));
        if (!submitApplication.getAgencyId().equals(SecurityUtils.getAgencyId())) {
            throw new CustomParameterizedException("You cannot view this talent: " + talentId);
        }
        ResponseEntity<List<TalentNoteDTO>> response = talentClient.getAllTalentNotesForTalent(talentId, submitApplication.getAgencyId());
        return response != null ? response.getBody() : null;
    }

    @Override
    public TalentNote createTalentNote(TalentNote talentNote) {
        AgencySubmitApplication submitApplication = agencySubmitApplicationRepository.findByTalentId(talentNote.getTalentId()).orElseThrow(() -> new CustomParameterizedException("Cannot find talent: " + talentNote.getTalentId()));
        if (!submitApplication.getAgencyId().equals(SecurityUtils.getAgencyId())) {
            throw new CustomParameterizedException("You cannot view this talent: " + talentNote.getTalentId());
        }

        talentNote.setAgencyId(SecurityUtils.getAgencyId());
        ResponseEntity<TalentNote> response = talentClient.createTalentNote(talentNote);
        return response != null ? response.getBody() : null;
    }

    @Override
    public AgencySubmitApplication createTalentAndApplyToJob(TalentInfoInputWithJobIdForApplicationDTO input) {
        if (CollectionUtils.isEmpty(input.getResumes())) {
            throw new CustomParameterizedException("No resumes!");
        }

        Long jobId = input.getJobId();
        input.setJobId(null); //for create talent, DO NOT set extra info or field

        Long agencyId = SecurityUtils.getAgencyId();
        AgencySharedJob agencySharedJob = agencySharedJobRepository.findByAgencyIdAndJobId(agencyId, jobId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " and jobId: " + jobId + " not found"));
        if (JobShareStatus.INACTIVE.equals(agencySharedJob.getShareStatus())) {
            throw new CustomParameterizedException("This job sharing has already been terminated!");
        }

        Agency agency = agencyRepository.findById(agencyId).orElseThrow(() -> new CustomParameterizedException("Agency with id: " + agencyId + " not found"));

        //set default talent ownership as agency owner
        TalentOwnershipDTO talentOwnershipDTO = new TalentOwnershipDTO();
        talentOwnershipDTO.setOwnershipType(TalentOwnershipType.TALENT_OWNER);
        talentOwnershipDTO.setUserId(agency.getAgencyOwnerUserId());
        List<TalentOwnershipDTO> ownership = new ArrayList<>();
        ownership.add(talentOwnershipDTO);
        input.setOwnerships(ownership);

        //set source chanel
        input.setSource(ResumeSourceType.AGENCY);

        try {
            try {
                agencyService.refreshTalentResumeParseResult(input.getResumes());
            } catch (Exception e) {
                log.error("[createAndResult] refresh talent resumes error, resume: {}, msg: {}", input.getResumes(), e.getMessage());
            }

            ResponseEntity<TalentDTOV3> t = talentClient.createTalent(input);

            if (Objects.isNull(t) || Objects.isNull(t.getBody())) {
                log.error("[createAndResult] Talent creation failed!");
                throw new CustomParameterizedException("Talent creation failed!");
            }
            TalentDTOV3 talent = t.getBody();

            log.info("[createAndResult] successfully create talent: {} ", talent.getId());
            Long talentId = talent.getId();

            input.setJobId(jobId);
            TalentRecruitmentProcessSubmitToJobVO submitToJobVO = new TalentRecruitmentProcessSubmitToJobVO();
            submitToJobVO.setTalentId(talent.getId());


            JobDTOV3 job = null;
            try {
                job = jobService.getJob(jobId);
            } catch (Exception e) {
                log.error("[createAndResult] get job error, msg: {}", e.getMessage(), e);
                throw new CustomParameterizedException("Cannot find job: " + jobId);
            }
            if (Objects.isNull(job)) {
                log.error("[createAndResult] get job error, null job: {}", jobId);
                throw new CustomParameterizedException("Find job null: " + jobId);
            }

            if (!job.getTenantId().equals(SecurityUtils.getTenantId())) {
                throw new CustomParameterizedException("You cannot apply for this job: " + job.getId());
            }
            submitToJobVO.setJobId(job.getId());
            submitToJobVO.setRecruitmentProcessId(job.getRecruitmentProcess().getId());
//            submitToJobVO.setJobId(jobId);
//            submitToJobVO.setRecruitmentProcessId(28L);

            List<TalentResumeDTO> resumes = talentClient.getTalentResumesByTalentId(talentId).getBody();

            if (CollectionUtils.isEmpty(resumes)) {
                log.error("[createAndResult] talent doesn't have any resumes, talentId: {}, for input brief: {}", talent.getId(), input.toSimpleString());
                throw new CustomParameterizedException("Cannot find resume for talent: " + talent.getId());
            }
            TalentResumeDTO resume = resumes.stream().findFirst().orElseThrow(() -> new CustomParameterizedException("Cannot find resume for talent: " + talentId));
            submitToJobVO.setTalentResumeRelationId(resume.getId());
            attachKpiUsers(agency, submitToJobVO);
//
//            System.out.println(JSONUtil.toJsonPrettyStr(submitToJobVO));

            TalentRecruitmentProcessVO res = applicationService.submitToJob(submitToJobVO);
            log.info("[createAndResult] successfully submit talent: {} to job with application: {} ", res.getTalentId(), res);

            AgencySubmitApplication submitApplication = new AgencySubmitApplication();
//            submitApplication.setAgencyId(agencyId);
//            submitApplication.setJobId(jobId);
            submitApplication.setAgencyId(SecurityUtils.getAgencyId());
            submitApplication.setJobId(submitToJobVO.getJobId());
            submitApplication.setTalentId(res.getTalentId());
            submitApplication.setTalentRecruitmentProcessId(res.getId());

            agencySubmitApplicationRepository.save(submitApplication);

            try { //agency activity
                AgencyActivityDTO agencyActivityDTO = new AgencyActivityDTO();
                agencyActivityDTO.setAgencyId(SecurityUtils.getAgencyId());
                agencyActivityDTO.setActivityType(AgencyActivityType.APPLICATION_UPDATE);

                com.alibaba.fastjson.JSONObject details = new com.alibaba.fastjson.JSONObject();
                details.fluentPut("node", "SUBMIT");
                details.fluentPut("talentRecruitmentProcessId", res.getId());
                details.fluentPut("isPrivateJob", job.getIsPrivateJob());
                details.fluentPut("jobId", submitToJobVO.getJobId());
                details.fluentPut("jobTitle", res.getJob().getTitle());
                details.fluentPut("talentId", res.getTalent().getTalentId());
                details.fluentPut("talentName", res.getTalent().getFullName());
                agencyActivityDTO.setDetails(details);
                agencyActivityService.createActivity(agencyActivityDTO);
            } catch (Exception e) {
                log.error("[createTalentAndApplyToJob] save activity failed for applicationId: {}", res.getId(), e);
            }


            return submitApplication;

        } catch (WithDataException exception) {
            input.setJobId(jobId);
            log.error("Duplicate talent when create, error: {}, duplicate data: {}, input brief:{}", exception.getMessage(), exception.getData(), input.toSimpleString());

            ObjectMapper objectMapper = new ObjectMapper();

            List<LinkedHashMap> duplications = (List<LinkedHashMap>) exception.getData();

            List<SuspectedDuplications> dup = new ArrayList<>();
            for (int i = 0; i < duplications.size(); i++) {
                LinkedHashMap d = duplications.get(i);
                SuspectedDuplications res = new SuspectedDuplications();
                try {
                    res = objectMapper.convertValue(d, SuspectedDuplications.class);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (Objects.nonNull(res)) {
                    dup.add(res);
                }
            }

//            List<Long> duplicateIds = duplications.stream().map(d -> objectMapper.convertValue(d, SuspectedDuplications.class)).map(SuspectedDuplications::get_id).map(Long::valueOf).collect(Collectors.toList());

            List<Long> duplicateIds = dup.stream().map(SuspectedDuplications::get_id).map(Long::valueOf).collect(Collectors.toList());

            String duplicateIdStr = duplicateIds.stream().map(String::valueOf).collect(Collectors.joining(","));
            log.error("[Duplicate talent when create]2 duplicate talent ids: {}, input brief:{}", duplicateIdStr, input.toSimpleString());

            AgencySubmitDuplicateTalent duplicateTalent = new AgencySubmitDuplicateTalent();
            duplicateTalent.setAgencyId(SecurityUtils.getAgencyId());
            duplicateTalent.setOriginalTalentJson(JSON.toJSONString(input));
            duplicateTalent.setDuplicateTalentIds(duplicateIds);
            duplicateTalent.setJobId(jobId);
            agencySubmitDuplicateTalentRepository.saveAndFlush(duplicateTalent);

            throw new DuplicateException("Duplicate talent!");
        } catch (CustomParameterizedException exception) {
            input.setJobId(jobId);
            log.error("[createAndResult: CustomParameterizedException] exception when create talent, error msg: {}, for input brief:{}, ", exception.getMessage(), input.toSimpleString(), exception);
            throw new CustomParameterizedException("Talent application failed, with error: {}", exception.getMessage());
        } catch (Exception e) {
            input.setJobId(jobId);
            log.error("[createAndResult: Exception] exception when create talent with errorMsg: {}, for input brief: {}, ", e.getMessage(), input.toSimpleString(), e);
            throw new CustomParameterizedException("Talent application failed, please contact administrator!");
        }
    }

    private void attachKpiUsers(Agency agency, TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
        List<TalentRecruitmentProcessKpiUserVO> kpiUsers = new ArrayList<>();

        TalentRecruitmentProcessKpiUserVO am = new TalentRecruitmentProcessKpiUserVO();
        am.setUserId(agency.getAgencyOwnerUserId());
        am.setUserRole(UserRole.AM);
        kpiUsers.add(am);

//        TalentRecruitmentProcessKpiUserVO source = new TalentRecruitmentProcessKpiUserVO();
//        source.setUserId(agency.getAgencyOwnerUserId());
//        source.setUserRole(UserRole.SOURCER);
//        kpiUsers.add(source);
//
//        TalentRecruitmentProcessKpiUserVO recruiter = new TalentRecruitmentProcessKpiUserVO();
//        recruiter.setUserId(agency.getAgencyOwnerUserId());
//        recruiter.setUserRole(UserRole.RECRUITER);
//        kpiUsers.add(recruiter);

        submitToJobVO.setKpiUsers(kpiUsers);
    }


    @Override
    public String searchCollegeName(String collegeName) {
        ResponseEntity<String> response = talentClient.searchCollegeNames(collegeName);
        return response != null ? response.getBody() : null;
    }

    @Override
    public JobDTOV3 test(Long jobId) {
        JobDTOV3 job = jobService.getJob(jobId);
        return job;
    }

    @Override
    public TalentDTOV3 updateTalentInfo(Long id, TalentInfoInputWithId talentInfoInput) {
        ResponseEntity<TalentDTOV3> response = talentClient.updateTalentInfo(id, talentInfoInput);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<SuspectedDuplications> suspectedDuplicatePhonesCheck(TalentInfoInputWithJobIdForApplicationDTO talentDTO) {
        ResponseEntity<List<SuspectedDuplications>> response = null;
        try {
            response = talentClient.SuspectedDuplicatePhonesCheck(talentDTO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return response != null ? response.getBody() : null;
    }

}
