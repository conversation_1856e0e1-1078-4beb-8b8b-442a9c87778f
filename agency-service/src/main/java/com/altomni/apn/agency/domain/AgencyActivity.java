package com.altomni.apn.agency.domain;

import com.altomni.apn.agency.dto.AgencyActivityDTO;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.agency.AgencyActivityType;
import com.altomni.apn.common.domain.enumeration.agency.AgencyActivityTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "agency_activity")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencyActivity extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "agency_id", nullable = false)
    private Long agencyId;

    @Column(name = "activity_type", nullable = false)
    @Convert(converter = AgencyActivityTypeConverter.class)
    private AgencyActivityType activityType;

    @Column(name = "read_status", nullable = false)
    private Boolean readStatus;

    @Column(name = "details", columnDefinition = "TEXT")
    private String details;

    // 虚拟列
    @Column(name = "talent_name", insertable = false, updatable = false)
    private String talentName;

    @Column(name = "job_title", insertable = false, updatable = false)
    private String jobTitle;

    @Column(name = "job_id", insertable = false, updatable = false)
    private Long jobId;

    public AgencyActivity(AgencyActivityDTO activityDTO) {
        this.id = activityDTO.getId();
        this.agencyId = activityDTO.getAgencyId();
        this.activityType = activityDTO.getActivityType();
        this.readStatus = Boolean.FALSE;
        this.details = activityDTO.getDetails().toJSONString();
    }

}