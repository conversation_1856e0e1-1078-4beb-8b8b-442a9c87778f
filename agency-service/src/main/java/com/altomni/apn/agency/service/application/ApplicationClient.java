package com.altomni.apn.agency.service.application;

import com.altomni.apn.agency.dto.ApplicationBriefInfoVM;
import com.altomni.apn.agency.dto.InProcessApplicationBriefVM;
import com.altomni.apn.agency.web.rest.vm.ApplicationIdAndStatusListVO;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

@Component
@FeignClient(value = "application-service")
public interface ApplicationClient {

    @PostMapping("/application/api/v3/talent-recruitment-processes/in-process/by-application-ids/count")
    ResponseEntity<Integer> countUnfinishedApplicationsByIds(@RequestBody List<Long> applicationIds);

    @PostMapping("/application/api/v3/talent-recruitment-processes/in-process/by-application-ids")
    ResponseEntity<List<InProcessApplicationBriefVM>> getUnfinishedApplicationsByIds(@RequestBody Set<Long> applicationIds);

    @PostMapping("/application/api/v3/talent-recruitment-processes/brief-info/by-application-ids")
    ResponseEntity<List<ApplicationBriefInfoVM>> getApplicationsBriefInoByIds(@RequestBody Set<Long> applicationIds);

    @PostMapping("/application/api/v3/talent-recruitment-processes/submit-to-job")
    ResponseEntity<TalentRecruitmentProcessVO> submitToJob(@RequestBody TalentRecruitmentProcessSubmitToJobVO submitToJobVO);

    @GetMapping("/application/api/v3/talent-recruitment-processes/stats")
    ResponseEntity<ApplicationIdAndStatusListVO> getStats(@RequestParam("talentRecruitmentProcessIds") Set<Long> talentRecruitmentProcessIds, @RequestParam("nodeType") NodeType nodeType, Pageable pageable);

    @GetMapping("/application/api/v3/dashboard/my-application-candidates/with-application-ids")
    ResponseEntity<LinkedHashMap<String, Integer>> getMyApplicationCandidates(@RequestParam("startTime") Instant startTime, @RequestParam("endTime") Instant endTime, @RequestParam("userIdList") List<Long> userIdList, @RequestParam("applicationIds") List<Long> applicationIds);
}
