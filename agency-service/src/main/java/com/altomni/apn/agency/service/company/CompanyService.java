package com.altomni.apn.agency.service.company;

import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;
import com.altomni.apn.company.service.dto.CompanyDTO;

import java.util.List;
import java.util.Map;

public interface CompanyService {

    CompanyDTO getCompany(Long companyId);

    List<SkipSubmitToAmCompanyUser>  getAllSkipSubmitToAmCompanyUsers(Long companyId);

    List<Long> getAllAmIdsByCompanyId(Long companyId);

    String getCompanyNameByJobId(Long jobId);
}
