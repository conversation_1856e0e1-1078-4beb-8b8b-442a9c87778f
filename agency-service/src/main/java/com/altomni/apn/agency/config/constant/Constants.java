package com.altomni.apn.application.config.constant;

import com.altomni.apn.common.domain.enumeration.user.UserRole;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Application constants.
 */
public final class Constants {

    public static final Long TENANT_DEFAULT = -1L;

    public static final Long TENANT_IPG = 4L;

    public static final Set<Long> IPG_RULE_TENANT_IDS = Set.of(4L, 14L);

    public final static BigDecimal TALENT_OWNERSHIP_PERCENTAGE = new BigDecimal(10);

    public final static List<Integer> SPECIAL_NUMBER_OF_SIZE = Arrays.asList(3, 6, 7, 9);

    public final static Integer THREE = 3;

    public final static Integer SIX = 6;

    public final static Integer SEVEN = 7;

    public static final String TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD = "TALENT_RECOMMENDATION_RIGHTS_PROTECTION_PERIOD";

    public static final String TALENT_OWNERSHIP_PROTECTION_PERIOD = "TALENT_OWNERSHIP_PROTECTION_PERIOD";

    public static Set<UserRole> RECRUITER_AND_SOURCER = new HashSet<>(Arrays.asList(UserRole.RECRUITER, UserRole.SOURCER));

    public static Set<UserRole> ALL_BUT_EXCLUDE_OWNER = new HashSet<>(Arrays.asList(UserRole.AM,UserRole.CO_AM, UserRole.RECRUITER, UserRole.SOURCER, UserRole.DM, UserRole.AC, UserRole.PR));

    public final static Integer DEFAULT_PAGE_NUMBER = 1;

    public final static Integer DEFAULT_PAGE_SIZE = 20;

    public final static Instant MAX_DATETIME = Instant.parse("9999-12-31T23:59:59.999Z");


    public final static String AUTO_ELIMINATION_NOTE_TEMPLATE  = "- 自动淘汰（说明：该职位开启自动淘汰规则，%s 天未更新流程将自动淘汰候选人）/Automatic elimination(Note:This position has an automatic elimination rule.Candidates will be automatically eliminated if there is no process update for ，%s days.)";

    private Constants() {}
}
