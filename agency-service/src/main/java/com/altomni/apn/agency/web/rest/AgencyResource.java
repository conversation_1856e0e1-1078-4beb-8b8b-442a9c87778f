package com.altomni.apn.agency.web.rest;

import com.altomni.apn.agency.dto.*;
import com.altomni.apn.agency.service.agency.AgencyService;
import com.altomni.apn.agency.web.rest.vm.*;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class AgencyResource {

    @Resource
    private AgencyService agencyService;

    @PostMapping("/agency")
    public ResponseEntity<AgencyVO> createAgency(@RequestBody AgencyDTO agencyDTO) {
        log.info("[APN: Agency @{}] REST request to create agency, param = {}", SecurityUtils.getUserId(), agencyDTO);
        Long agencyId = agencyService.create(agencyDTO);
        AgencyVO agencyVO = agencyService.getAgencyById(agencyId);
        return ResponseEntity.ok(agencyVO);
    }

    @GetMapping("/{id}")
    public ResponseEntity<AgencyVO> getAgencyById(@PathVariable Long id) {
        log.info("[APN: Agency @{}] REST request to get agency by id: {}", SecurityUtils.getUserId(), id);
        AgencyVO agencyVO = agencyService.getAgencyById(id);
        return ResponseEntity.ok(agencyVO);
    }

    @PutMapping("/agency/{id}")
    public ResponseEntity<AgencyVO> updateAgency(@PathVariable Long id, @RequestBody AgencyDTO agencyDTO) {
        log.info("[APN: Agency @{}] REST request to update agency, id: {}, param = {}", SecurityUtils.getUserId(), id, agencyDTO);
        agencyService.update(id, agencyDTO);
        AgencyVO agencyVO = agencyService.getAgencyById(id);
        return ResponseEntity.ok(agencyVO);
    }

    @GetMapping("/{id}/in-progress")
    public ResponseEntity<Integer> getAgencyInProgressApplicationByAgencyId(@PathVariable Long id) {
        log.info("[APN: Agency @{}] REST request to get agency in-progress applications by agency id: {}", SecurityUtils.getUserId(), id);
        Integer count = agencyService.getAgencyInProgressApplicationsByAgencyId(id);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/{id}/job/{jobId}/in-progress")
    public ResponseEntity<Integer> getAgencyInProgressApplicationByAgencyId(@PathVariable Long id, @PathVariable Long jobId) {
        log.info("[APN: Agency @{}] REST request to get agency in-progress applications by agency id: {} and job id: {}", SecurityUtils.getUserId(), id, jobId);
        Integer count = agencyService.getAgencyInProgressApplicationsByAgencyIdAndJobId(id, jobId);
        return ResponseEntity.ok(count);
    }

    @GetMapping("/agency/{agencyId}/jobs")
    public ResponseEntity<List<SharedJobVO>> getAllSharedJobInfoByAgencyId(@PathVariable Long agencyId, @RequestParam(defaultValue = "") String jobTitle, @RequestParam(defaultValue = "") String companyName, @PageableDefault Pageable pageable) {
        log.info("[APN: Agency @{}] REST request to get all shared job info by agencyId: {}", SecurityUtils.getUserId(), agencyId);
        Page<SharedJobVO> sharedJobs = agencyService.getSharedJobs(agencyId, jobTitle, companyName, pageable);

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(sharedJobs, "/api/v3/agency/" + agencyId + "/jobs");
        return ResponseEntity.ok().headers(headers).body(sharedJobs.getContent());
    }

    @GetMapping("/agency/{agencyId}/jobs/{jobId}/duplicate-talents")
    public ResponseEntity<List<DuplicateTalentVO>> getDuplicateTalentsInfoByJobId(@PathVariable Long agencyId, @PathVariable Long jobId) {
        log.info("[APN: Agency @{}] REST request to get duplicate talent info by agencyId: {} and jobId: {}", SecurityUtils.getUserId(), agencyId, jobId);
        List<DuplicateTalentVO> duplicateTalents = agencyService.getDuplicateTalents(agencyId, jobId);
        return ResponseEntity.ok(duplicateTalents);
    }

    @GetMapping("/agency/{agencyId}/jobs/{jobId}/duplicate-talents/{duplicateId}")
    public ResponseEntity<DuplicateTalentBriefDTO> getDuplicateTalent(@PathVariable Long agencyId, @PathVariable Long jobId, @PathVariable Long duplicateId) {
        log.info("[APN: Agency @{}] REST request to get duplicate talent info by agencyId: {}, jobId: {} and duplicateId: {}", SecurityUtils.getUserId(), agencyId, jobId, duplicateId);
        DuplicateTalentBriefDTO duplicateTalent = agencyService.getDuplicateTalent(agencyId, jobId, duplicateId);
        return ResponseEntity.ok(duplicateTalent);
    }

    @PutMapping("/agency/{agencyId}/jobs/{jobId}/duplicate-talents/{duplicateId}")
    public ResponseEntity<TalentDTOV3> mergeDuplicateTalent(@PathVariable Long agencyId, @PathVariable Long jobId, @PathVariable Long duplicateId, @RequestBody TalentInfoInputWithId talentInfoInput) {
        log.info("[APN: Agency @{}] REST request to merge duplicate talent info by agencyId: {}, jobId: {}, duplicateId: {} and talentInfoInput: {}", SecurityUtils.getUserId(), agencyId, jobId, duplicateId, talentInfoInput);
        TalentDTOV3 result = agencyService.updateTalentInfo(agencyId, jobId, duplicateId, talentInfoInput);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @GetMapping("/agency/job/{jobId}/all-agencies")
    public ResponseEntity<List<AgencyStatsVO>> getAllAgenciesByJobId(@PathVariable Long jobId) {
        log.info("[APN: Agency @{}] REST request to get all agency stats info info by jobId: {}", SecurityUtils.getUserId(), jobId);
        List<AgencyStatsVO> agencyStatsVOList = agencyService.getAllAgenciesByJobId(jobId);

        return ResponseEntity.ok(agencyStatsVOList);
    }

    @GetMapping("/job/{jobId}/shared-info")
    public ResponseEntity<SharedToAgencyJobInfoVM> getJobSharingToAgencyInfoByJobId(@PathVariable Long jobId) {
        log.info("[APN: Agency @{}] REST request to get job sharing to agency info by jobId: {}", SecurityUtils.getUserId(), jobId);
        SharedToAgencyJobInfoVM jobSharingInfo = agencyService.getJobSharingToAgencyInfoByJobId(jobId);

        return ResponseEntity.ok(jobSharingInfo);
    }

    @GetMapping("/portal/job/{jobId}/shared-info")
    public ResponseEntity<SharedToAgencyJobInfoVM> getJobSharingInfoByJobId(@PathVariable Long jobId) {
        log.info("[APN: Agency @{}] REST request to get job sharing to agency info by jobId: {}", SecurityUtils.getUserId(), jobId);
        SharedToAgencyJobInfoVM jobSharingInfo = agencyService.getJobSharingToAgencyInfoByJobId(jobId);

        return ResponseEntity.ok(jobSharingInfo);
    }

    @PostMapping("/job/{jobId}/shared-info")
    public ResponseEntity<SharedToAgencyJobInfoDTO> createJobSharingToAgencyInfoByJobId(@PathVariable Long jobId, @RequestBody SharedToAgencyJobInfoDTO sharedToAgencyJobInfoDTO) {
        log.info("[APN: Agency @{}] REST request to get job sharing to agency info by jobId: {}", SecurityUtils.getUserId(), jobId);
        SharedToAgencyJobInfoDTO jobSharingInfo = agencyService.createJobSharingToAgencyInfo(jobId, sharedToAgencyJobInfoDTO);

        return ResponseEntity.ok(jobSharingInfo);
    }

    @PostMapping("/job/{jobId}/share")
    public ResponseEntity<List<AgencyStatsVO>> shareJobToAgency(@PathVariable Long jobId, @RequestBody AgencyIdListDTO agencyIds) {
        log.info("[APN: Agency @{}] REST request to share job: {} to agencyList: {}", SecurityUtils.getUserId(), jobId, agencyIds);

        List<AgencyStatsVO> res = agencyService.shareJobToAgencies(jobId, agencyIds.getAgencyIds());

        return ResponseEntity.ok(res);
    }

    @PostMapping("/job/{jobId}/stop-sharing")
    public ResponseEntity<Void> stopSharingToAgency(@PathVariable Long jobId, @RequestBody AgencyIdListDTO agencyIds) {
        log.info("[APN: Agency @{}] REST request to stop share job: {} to agencyList: {}", SecurityUtils.getUserId(), jobId, agencyIds);

        agencyService.stopShareJobToAgencies(jobId, agencyIds.getAgencyIds());

        return ResponseEntity.ok().build();

    }

    @PostMapping("/job/{jobId}/stop-sharing/all")
    public void stopShareJobToAllAgencies(@PathVariable Long jobId) {
        log.info("[APN: Agency @{}] REST request to stop share job: {} to all agencies", SecurityUtils.getUserId(), jobId);

        agencyService.stopShareJobToAllAgencies(jobId);

        ResponseEntity.ok().build();
    }

    @PostMapping("/talent-recruitment-processes/submit-to-job")
    public ResponseEntity<TalentRecruitmentProcessVO> submitToJob(@RequestBody TalentRecruitmentProcessSubmitToJobVO submitToJobVO) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to SUBMIT_TO_JOB : {}", submitToJobVO);
        TalentRecruitmentProcessVO result = agencyService.submitToJob(submitToJobVO);
//        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.submitToJob(submitToJobVO, false);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/submit-to-job" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert("TalentRecruitmentProcess", result.getId().toString()))
                .body(result);
    }

    @GetMapping("/test")
    public ResponseEntity<String> test() {
        log.info("[APN: Agency @{}] REST test", SecurityUtils.getUserId());

        System.out.println("test1");
        return ResponseEntity.ok("test1");
    }

    @GetMapping("/check-has-new-candidate")
    public ResponseEntity<Boolean> checkIfHasNewCandidate() {
        log.info("[APN: checkIfHasNewCandidate @{}] REST request to check if my agencies has new candidate", SecurityUtils.getUserId());
        Boolean hasNewCandidate = agencyService.checkIfHasNewCandidate();
        return ResponseEntity.ok(hasNewCandidate);
    }

}
