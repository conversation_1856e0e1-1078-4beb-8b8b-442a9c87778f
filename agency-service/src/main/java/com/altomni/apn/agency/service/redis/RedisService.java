package com.altomni.apn.agency.service.redis;

import com.altomni.apn.common.dto.redis.ParserRedisResponse;
import com.altomni.apn.job.service.dto.redis.RedisResponse;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface RedisService {

    String get(String key);

    void saveData(String key,String value,int expireSecond);

    void set(String key, String value);

    void set(String key, String value, Integer expireInSeconds);

    void pipeline(List<String> keys, String value, int expireInSeconds);

    Long getTTL(String key);

    Boolean exists(String key);

    Boolean scanForKeyPattern(String pattern);

    Long incr(String key);

    Long delete(String key);

    Long delete(String[] keys);

    void saveJobId(Collection<Long> jobIds);

    Set<String> getJobIds(long count);

    Long checkSize(String key);

    void deleteRaterRedisKeyWildcard(String key);

    void saveFailedAgencyIds(Collection<Long> agencyIds);

}
