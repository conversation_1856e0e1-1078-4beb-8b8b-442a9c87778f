package com.altomni.apn.agency.interceptor;

import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.config.env.CommonApplicationProperties;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.dto.user.AgencyLoginUserDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.servlet.DispatcherType;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class AgencyInterceptor implements HandlerInterceptor {

    public static final String APN_INTERNAL_PIN = "APN_INTERNAL_PIN";

    @Resource
    private CachePermission cachePermission;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private CommonApplicationProperties commonApplicationProperties;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (DispatcherType.REQUEST.name().equals(request.getDispatcherType().name())) {
            response.addHeader("responseTime", String.valueOf(System.currentTimeMillis()));
            // 1. GET /user/api/v3/tenant/4/user/100
            // 2. GET /user/api/v3/tenant/{tenantId}/user/{userId}
            // 3. GET /user/api/v3/tenant/{}/user/{}
            // 4. check permission
            // ******** 1. Output the original API path*********
            log.debug("requestURI: {} {}", request.getMethod(), request.getRequestURI());
            log.debug("enableObjectLevelSecurity: {}", commonApplicationProperties.isEnableObjectLevelSecurity());

            // ******** 2. Get formatted API path *********
            HandlerMethod handlerMethod = (HandlerMethod) handler;
            Method method = handlerMethod.getMethod();
            String classRequestMapping = "";
            if (method.getDeclaringClass().isAnnotationPresent(RequestMapping.class)) {
                classRequestMapping = method.getDeclaringClass().getAnnotation(RequestMapping.class).value()[0];
            }
            RequestMapping methodAnnotation = handlerMethod.getMethodAnnotation(RequestMapping.class);
            String methodRequestMapping = methodAnnotation.value()[0];
            // /user/api/v3/tenant/{tenantId}/user/{userId}
            String requestURI = request.getMethod() + commonApplicationProperties.getContextPath() + classRequestMapping + methodRequestMapping;

            // ********* 3. Uniform API path ***********
            // /user/api/v3/tenant/{}/user/{}
            String uniformURI = requestURI.replaceAll("[{].*?[}]", "{}");
            response.addHeader("URI", uniformURI);

            // if the request is from internal service, skip checking api permission
            final String internalServicePin = request.getHeader(APN_INTERNAL_PIN);
            if (StringUtils.hasText(internalServicePin) && StringUtils.hasText(commonApplicationProperties.getApnInternalPin()) && internalServicePin.equals(commonApplicationProperties.getApnInternalPin())) {
                return Boolean.TRUE;
            }

//            final LoginUserDTO loginUserDTO = SecurityUtils.getCurrentUserLogin().orElse(new LoginUserDTO().setId(-1L));

//            // 如果是agency c端用户，已经通过filter过滤了api权限，直接放行
//            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
//            if (authentication == null) {
//                return false;
//            }
//            Object principal = authentication.getPrincipal();
//            if (principal instanceof AgencyLoginUserDTO) {
//                return Boolean.TRUE;
//            }


            final Long userId = SecurityUtils.getUserId();
            final Long tenantId = SecurityUtils.getTenantId();


            this.countApi(userId, tenantId, null, null, uniformURI);
            //this.checkUserAuthentication(request, uniformURI, loginUserDTO);

            if (Boolean.FALSE.equals(commonApplicationProperties.isEnableObjectLevelSecurity())) {
                return Boolean.TRUE;
            }

            // check public APIs
            if (cachePermission.isPublicApi(uniformURI)) {
                return true;
            }

            cachePermission.checkImpersonationLoginPermission(userId, uniformURI, request.getMethod());

            // if the request is from jobdiva service, skip checking api permission
            if (commonApplicationProperties.getContextPath().equals("/jobdiva")
//                    && StringUtils.hasLength(loginUserDTO.getUid())
//                    && loginUserDTO.getUid().contains("_")
            ) {
                return Boolean.TRUE;
            }

            if (SecurityUtils.isSystemAdmin() || SecurityUtils.isAdmin()) {
                return Boolean.TRUE;
            }

            //
            String uri = commonApplicationProperties.getContextPath() + classRequestMapping + methodRequestMapping;
            if (uri.startsWith("/agency/api/v3/portal/")){
                if (Objects.nonNull(SecurityUtils.getAgencyId())) {
                    return Boolean.TRUE;
                }
            }

            cachePermission.checkUserPrivileges(userId, uniformURI, request.getMethod());
        }
        return true;
    }

    private void countApi(Long userId, Long tenantId, String ip, String timezone, String uniformURI) {

        if (!userId.equals(-1L)) {

            commonRedisService.set(String.format(RedisConstants.STATISTIC_USER_ONLINE, tenantId, userId),
                    DateUtil.currentTime(DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z) + ";" + timezone,
                    commonApplicationProperties.getOnlineUserStatisticPeriod());
        }
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {

    }

    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {

    }
}