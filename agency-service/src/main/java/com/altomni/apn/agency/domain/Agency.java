package com.altomni.apn.agency.domain;

import com.altomni.apn.agency.dto.AgencyDTO;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatusConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A RecruitmentProcess.
 */
@Entity
@Table(name = "agency")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class Agency extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4442477104358727842L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "phone")
    private String phone;

    @Column(name = "email")
    private String email;

    @Column(name = "agency_contact_first_name")
    private String agencyContactFirstName;

    @Column(name = "agency_contact_last_name")
    private String agencyContactLastName;

    @NotNull
    @Column(name = "agency_owner_user_id")
    private Long agencyOwnerUserId;

    @NotNull
    @Column(name = "agency_creator_user_id")
    private Long agencyCreatorUserId;

    @NotNull
    @Column(name = "tenant_id")
    private Long tenantId;

    @Convert(converter = ActiveStatusConverter.class)
    @Column(name = "status")
    private ActiveStatus status;

    @Column(name = "description")
    private String description;

    @Column(name = "timezone")
    private String timezone;

    @Column(name = "location")
    private String location;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id"));

//    public static Agency fromConfigVO(RecruitmentProcessConfigVO vo) {
//        Agency result = new Agency();
//        ServiceUtils.myCopyProperties(vo, result);
//        return result;
//    }

    public Agency(AgencyDTO agencyDTO, Long creatorUserId, Long tenantId) {
        this.name = agencyDTO.getName();
        this.phone = agencyDTO.getPhone();
        this.email = StringUtils.toRootLowerCase(agencyDTO.getEmail());
        this.agencyContactFirstName = agencyDTO.getAgencyContactFirstName();
        this.agencyContactLastName = agencyDTO.getAgencyContactLastName();
        this.agencyOwnerUserId = agencyDTO.getAgencyOwner();
        this.agencyCreatorUserId = creatorUserId;
        this.tenantId = tenantId;
        this.status = ActiveStatus.ACTIVE;
        this.description = agencyDTO.getDescription();
        this.timezone = agencyDTO.getTimezone();
        if (agencyDTO.getLocation() != null) {
            this.location = agencyDTO.getLocation().toJSONString();
        } else {
            this.location = "{}";
        }
    }

}
