package com.altomni.apn.agency.web.rest.vm;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationIdAndStatusVO {
    private Long applicationId;

    @Convert(converter = NodeTypeConverter.class)
    private NodeType latestStatus;

    public ApplicationIdAndStatusVO(Long applicationId, String statusValue) {
        this.applicationId = applicationId;
        this.latestStatus = NodeType.valueOf(statusValue);
    }
}
