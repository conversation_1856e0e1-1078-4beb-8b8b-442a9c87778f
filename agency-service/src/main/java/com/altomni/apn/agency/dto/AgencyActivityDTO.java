package com.altomni.apn.agency.dto;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.agency.domain.AgencyActivity;
import com.altomni.apn.common.domain.enumeration.agency.AgencyActivityType;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * A AgencyActivityDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencyActivityDTO {

    private Long id;

    private Long agencyId;

    private AgencyActivityType activityType;

    private JSONObject details;

}
