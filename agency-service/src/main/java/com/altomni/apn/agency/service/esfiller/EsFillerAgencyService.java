package com.altomni.apn.agency.service.esfiller;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.JobNoteDTO;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.job.service.dto.folder.JobCategoryCountRequestDTO;
import com.altomni.apn.job.service.dto.job.JobEsSyncDocument;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface EsFillerAgencyService {

    void extractAgencyToMq(Collection<Long> jobIds, int priority);

}
