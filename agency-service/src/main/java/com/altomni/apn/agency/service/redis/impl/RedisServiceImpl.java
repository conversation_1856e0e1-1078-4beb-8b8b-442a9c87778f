package com.altomni.apn.agency.service.redis.impl;

import com.altomni.apn.agency.service.redis.RedisService;
import com.altomni.apn.common.config.constants.RedisConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import redis.clients.jedis.*;

import java.util.*;

@Slf4j
@Service
@RefreshScope
public class RedisServiceImpl implements RedisService {

    private static JedisPool pool;

    private static JedisPoolConfig jedisPoolConfig;

    private static JedisPoolConfig jedisPoolConfigRater;

    private static JedisPoolConfig jedisPoolConfigParser;

    private static final int maxIdle = 10;

    private static final long maxWaitMillis = 50 * 1000;

    private static final int maxActive = 100;

    private static final int timeout = 2000;

    @Value("${spring.redis.host}")
    private String redisHost;

    @Value("${spring.redis.host-rater}")
    private String redisHostRater;

    @Value("${spring.redis.host-parser}")
    private String redisHostParser;

    @Value("${spring.redis.port}")
    private Integer redisPort;

    @Value("${spring.redis.port-rater}")
    private Integer redisPortRater;

    @Value("${spring.redis.port-parser}")
    private Integer redisPortParser;

    @Value("${spring.redis.database}")
    private Integer redisDb;

    static {
        try {
            jedisPoolConfig = new JedisPoolConfig();
            jedisPoolConfig.setMaxTotal(maxActive);
            jedisPoolConfig.setMaxWaitMillis(maxWaitMillis);
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setTestOnBorrow(true);
            jedisPoolConfig.setTestOnReturn(true);
        } catch (Exception e) {
            log.error("error", e);
        }

        try {
            jedisPoolConfigRater = new JedisPoolConfig();
            jedisPoolConfig.setMaxTotal(maxActive);
            jedisPoolConfig.setMaxWaitMillis(maxWaitMillis);
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setTestOnBorrow(true);
            jedisPoolConfig.setTestOnReturn(true);
        } catch (Exception e) {
            log.error("error", e);
        }

        try {
            jedisPoolConfigParser = new JedisPoolConfig();
            jedisPoolConfig.setMaxTotal(maxActive);
            jedisPoolConfig.setMaxWaitMillis(maxWaitMillis);
            jedisPoolConfig.setMaxIdle(maxIdle);
            jedisPoolConfig.setTestOnBorrow(true);
            jedisPoolConfig.setTestOnReturn(true);
        } catch (Exception e) {
            log.error("error", e);
        }
    }

    private void close(Jedis jedis) {
        if (jedis != null) {
            try {
                jedis.close();
            } catch (Exception e) {
                log.error("close jedis connection error: {}", e.getMessage());
                try {
                    jedis.disconnect();
                } catch (Exception e1) {
                    log.error("disconnect jedis connection error: {}" , e1.getMessage());
                }
            }
        }
    }

    private synchronized Jedis getJedis() {
        if (pool == null) {
            pool = new JedisPool(jedisPoolConfig, redisHost, redisPort, timeout, null, redisDb);
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 1st error message: {}", e.getMessage());
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 2cnd error message: {}", e.getMessage());
        }
        return pool.getResource();
    }

    private synchronized Jedis getJedisForRater() {
        if (pool == null) {
            pool = new JedisPool(jedisPoolConfigRater, redisHostRater, redisPortRater, timeout, null, redisDb);
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 1st error message: {}", e.getMessage());
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 2cnd error message: {}", e.getMessage());
        }
        return pool.getResource();
    }

    private synchronized Jedis getJedisForParser() {
        if (pool == null) {
            pool = new JedisPool(jedisPoolConfigParser, redisHostParser, redisPortParser, timeout, null, redisDb);
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 1st error message: {}", e.getMessage());
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 2cnd error message: {}", e.getMessage());
        }
        return pool.getResource();
    }

    @Override
    public String get(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null && jedis.exists(key)) {
                return jedis.get(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    @Override
    public void saveData(String key, String value, int expireSecond) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null)
            {
                jedis.set(key, value);
                jedis.expire(key, expireSecond);
            }
        } catch (Exception e)
        {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally
        {
            close(jedis);
        }
    }

    private Integer valueOf(String s) {
        return StringUtils.isEmpty(s) ? 0 : Integer.parseInt(s);
    }

    @Override
    public void set(String key, String value) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.set(key, value);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void set(String key, String value, Integer expireSecond) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null){
                jedis.set(key, value);
                if (Objects.nonNull(expireSecond)){
                    jedis.expire(key, expireSecond);
                }
            }
        } catch (Exception e){
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally{
            close(jedis);
        }
    }

    @Override
    public void pipeline(List<String> keys, String value, int expireInSeconds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null){

                Pipeline pipeline = jedis.pipelined();
                for (String key : keys) {
                    pipeline.setex(key, expireInSeconds, value); // 直接使用 setex
                }
                pipeline.sync();
                log.debug("Keys set successfully with TTL using pipeline!");
            }
        } catch (Exception e){
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally{
            close(jedis);
        }
    }

    @Override
    public Long getTTL(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.ttl(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return -1L;
    }

    @Override
    public Boolean exists(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.exists(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean scanForKeyPattern(String pattern) {
        Jedis jedis = null;

        String cursor = "0";
        ScanParams scanParams = new ScanParams().match(pattern).count(100);
        try {
            jedis = getJedis();
            if (jedis != null) {
                do {
                    ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                    if (!scanResult.getResult().isEmpty()) {
                        return true; // 发现匹配的 key，直接返回
                    }
                    cursor = String.valueOf(scanResult.getCursor()); // 更新 cursor 继续扫描
                } while (!cursor.equals("0"));
                return false;
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return Boolean.FALSE;
    }

    @Override
    public Long incr(String key){
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.incr(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return -1L;
    }

    @Override
    public Long delete(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.del(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    @Override
    public Long delete(String[] keys) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.del(keys);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    @Override
    public void saveJobId(Collection<Long> jobIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long jobId : jobIds) {
                    jedis.sadd(RedisConstants.DATA_KEY_SYNCES_JOB, jobId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public Set<String> getJobIds(long count) {
        Set<String> result = new HashSet<>();
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                if (jedis.exists(RedisConstants.DATA_KEY_SYNCES_JOB)){
                    result.addAll(jedis.spop(RedisConstants.DATA_KEY_SYNCES_JOB, count));
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    @Override
    public Long checkSize(String key) {
        Long size = 0L;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                size = jedis.scard(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return size;
    }

    @Override
    public void deleteRaterRedisKeyWildcard(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedisForRater();
            if (jedis != null) {
                String pattern = key + ":*";
                ScanParams scanParams = new ScanParams().match(pattern).count(100);
                String cursor = ScanParams.SCAN_POINTER_START;

                do {
                    ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
                    List<String> keys = scanResult.getResult();

                    if (!keys.isEmpty()) {
                        jedis.del(keys.toArray(new String[0]));
                    }

                    cursor = scanResult.getStringCursor();
                } while (!cursor.equals(ScanParams.SCAN_POINTER_START));
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void saveFailedAgencyIds(Collection<Long> agencyIds) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Long agencyId : agencyIds) {
                    jedis.sadd(RedisConstants.DATA_KEY_SYNCES_FAILED_AGENCY, agencyId.toString());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }
}
