package com.altomni.apn.agency.web.rest;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.agency.domain.AgencySubmitApplication;
import com.altomni.apn.agency.dto.ApplicationSearchDTO;
import com.altomni.apn.agency.dto.TalentInfoInputWithJobIdForApplicationDTO;
import com.altomni.apn.agency.service.agency.AgencyService;
import com.altomni.apn.agency.service.common.CommonService;
import com.altomni.apn.agency.service.data.DataService;
import com.altomni.apn.agency.service.talent.TalentService;
import com.altomni.apn.agency.utils.HeaderConvertUtil;
import com.altomni.apn.agency.web.rest.vm.ApplicationIdAndStatusListVO;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.aop.user.AttachSimpleUser;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.redis.ParserResponse;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.common.dto.talent.SuspectedDuplications;
import com.altomni.apn.common.dto.talent.TalentNoteDTO;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3/portal")
public class AgencyPortalResource {

    private static final String ENTITY_NAME = "agencyPortal";

    @Resource
    private TalentService talentService;

    @Resource
    private CommonService commonService;

    @Resource
    private AgencyService agencyService;

    @Resource
    private DataService dataService;

    /**
     * DELETE  /talent-notes/:id : delete the "id" talentNote.
     *
     * @param id the id of the talentNote to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "Delete a note", notes = "Only owner can delete")
    @DeleteMapping("/talent-notes/{id}")
    @Timed
    @NoRepeatSubmit
    @Transactional
    public ResponseEntity<Void> deleteTalentNoteById(@PathVariable Long id) throws IOException {
        log.info("[Agency: AgencyPortal @{}] REST request to delete TalentNote : {}", SecurityUtils.getUserId(), id);
        talentService.deleteTalentNote(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }

    @ApiOperation(value = "Get all notes for a talent")
    @GetMapping("/talent-notes/talent/{talentId}")
    @Timed
    public ResponseEntity<List<TalentNoteDTO>> getTalentNotesForTalent(@PathVariable("talentId") Long talentId) {
        log.info("[Agency: AgencyPortal @{}] REST request to get all TalentNotes for talent {}", SecurityUtils.getUserId(), talentId);
        return ResponseEntity.ok(talentService.getAllNotesByTalentId(talentId));
    }

    /**
     * POST  /talent-notes : Create a new talentNote.
     *
     * @param talentNote the talentNote to create
     * @return the ResponseEntity with status 201 (Created) and with body the new talentNote, or with status 400 (Bad Request) if the talentNote has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "user create note on talent", notes = "note owner will be current user.")
    @PostMapping("/talent-notes")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<TalentNote> createTalentNote(@Valid @RequestBody TalentNote talentNote) throws URISyntaxException, IOException {
        talentNote.setAgencyId(SecurityUtils.getAgencyId());
        log.info("[Agency: AgencyPortal @{}] REST request to save TalentNote : {}", SecurityUtils.getUserId(), JSON.toJSONString(talentNote));
        TalentNote result = talentService.createTalentNote(talentNote);
        return ResponseEntity.created(new URI("/api/v3/talent-notes/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    /**
     * 用于Agency C端提交候选人之前的疑似重复分机号检查
     * @param talentDTO
     * @return
     * @throws URISyntaxException
     */
    @PostMapping("/suspected-duplicate-phones-check")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<SuspectedDuplications>> suspectedDuplicatePhonesCheck(@Valid @RequestBody TalentInfoInputWithJobIdForApplicationDTO talentDTO) throws URISyntaxException {
        log.info("[Agency: AgencyPortal @{}] REST request suspected duplicate phones check: {}", SecurityUtils.getUserId(), talentDTO.toSimpleString());
        return ResponseEntity.ok(talentService.suspectedDuplicatePhonesCheck(talentDTO));
    }


    /**
     * POST  /talents-v3 : Create a new talent.
     *
     * @param talentDTO the talent to create
     * @return the ResponseEntity with status 201 (Created) and with body the new talent, or with status 400 (Bad Request) if the talent has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Create talent", notes = "Create can be APN-Pro crawling or parser parsed resume and create through ATS. When create talent for first time, all " +
            "related entities (educations, experiences etc.) will also be created. Create talent will also create " +
            "refer activity and add the talent to my watch list.", tags = {"ATS-Candidates", "APN-Pro"})
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping("/talent-and-application")
    @AttachSimpleUser
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<AgencySubmitApplication> createTalent(@Valid @RequestBody TalentInfoInputWithJobIdForApplicationDTO talentDTO) throws URISyntaxException {
        log.info("[Agency: AgencyPortal @{}] REST request to create talent and submit application, Talent brief: {}", SecurityUtils.getUserId(), talentDTO.toSimpleString());

        AgencySubmitApplication result = talentService.createTalentAndApplyToJob(talentDTO);
        log.info("[Agency: AgencyPortal @{}] successfully created talent: {} and submit to job: {}", SecurityUtils.getUserId(), result.getTalentId(), result);
        agencyService.cacheNewApplicationToRedis(result);
        return ResponseEntity.created(new URI("/api/v3/talents/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @GetMapping("/parsers/resume/status")
    @Timed
    public ResponseEntity<ParserResponse> checkResumeParseResultOrGetUploadUrl(@RequestParam("uuid") String uuid, @RequestParam("priority") Integer priority, @RequestParam("fileName") String fileName, @RequestParam("contentType") String contentType) {
        log.info("[Agency: AgencyPortal @{}] REST request to check if we already parsed the resume and still have parse result or generating pre-signed upload url, with uuid: {}, filename: {}, contentType: {} and priority: {}", SecurityUtils.getUserId(), uuid, fileName, contentType, priority);

        return commonService.checkResumeParseResultOrGetUploadUrl(uuid, priority, fileName, contentType);
    }

    @GetMapping("/parsers/resume/result/{uuid}")
    @Timed
    public ResponseEntity<ParserResponse> getParserResumeResult(@PathVariable("uuid") String uuid) {
        log.info("[Agency: AgencyPortal @{}] REST request to get parsed resume result from redis. uuid: {}", SecurityUtils.getUserId(), uuid);
        return commonService.getParserResumeResult(uuid);
    }

    @GetMapping("/parsers/resume/result-status/{uuid}")
    @Timed
    public ResponseEntity<ParserResponse> getResumeParseResultStatusOnly(@PathVariable("uuid") String uuid) {
        log.info("[Agency: AgencyPortal @{}] REST request to get resume parse result status only for uuid: {}", SecurityUtils.getUserId(), uuid);
        return commonService.getResumeParseResultStatusOnly(uuid);
    }

    @GetMapping("/parsers/resume/info/{uuid}")
    @Timed
    public ResponseEntity<ParserResponse> getParserResumeInfo(@PathVariable("uuid") String uuid) {
        log.info("[Agency: AgencyPortal @{}] REST request to get parsed resume result info from redis. uuid: {}", SecurityUtils.getUserId(), uuid);
        return commonService.getParserResumeInfo(uuid);
    }

    @PostMapping("/applications")
    @Timed
    public ResponseEntity<ApplicationIdAndStatusListVO> getAllApplications(@RequestBody ApplicationSearchDTO applicationSearchDTO, Pageable pageable) {
        log.info("[Agency: AgencyPortal @{}] REST request to get all applications by nodeType: {}", SecurityUtils.getUserId(), applicationSearchDTO);
        ApplicationIdAndStatusListVO res = agencyService.getAllApplications(applicationSearchDTO, pageable);
        return ResponseEntity.ok(res);
    }

    @GetMapping("/dashboard/my-application-candidates")
    @Timed
    public ResponseEntity<LinkedHashMap<String, Integer>> myApplicationCandidates(@RequestParam("startTime") Instant startTime, @RequestParam("endTime") Instant endTime) {
        log.info("[Agency: AgencyPortal @{}] REST request to get myApplicationCandidates, startTime: {}, endTime: {}", SecurityUtils.getUserId(), startTime, endTime);
        LinkedHashMap<String, Integer> res = agencyService.getMyApplicationCandidates(startTime, endTime);
        return ResponseEntity.ok(res);
    }

    @GetMapping("/auto-complete/college")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> getSearchSchool(@RequestParam("search") String schoolName) throws IOException {
        log.info("[Agency: AgencyPortal @{}] REST request to get schoolName : {}", SecurityUtils.getUserId(), schoolName);
        return ResponseEntity.ok().body(talentService.searchCollegeName(schoolName));
    }

    @PostMapping("/data-service/search")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> searchFromDataService(@RequestBody SearchGroup searchGroup, Pageable pageable) throws IOException {
        log.info("[Agency: AgencyPortal @{}] search from data service with search param: {}", SecurityUtils.getUserId(), searchGroup);

        HttpResponse response = dataService.searchFromDataService(searchGroup, pageable);
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping("/data-service/jobs/city-names")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> getAgencyJobAllCityNames() throws IOException {
        log.info("[Agency: AgencyPortal @{}] get all agency job all city names from data service", SecurityUtils.getUserId());

        HttpResponse response = dataService.getAllCityNames();
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping("/test/{id}")
    public ResponseEntity<JobDTOV3> test(@PathVariable Long id) {
        log.info("[Agency: AgencyPortal @{}] search from data service with search param: {}", SecurityUtils.getUserId(), id);

        JobDTOV3 res = talentService.test(id);
        return ResponseEntity.ok(res);
    }
}
