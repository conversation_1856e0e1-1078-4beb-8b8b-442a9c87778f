package com.altomni.apn.agency.config;

import com.altomni.apn.agency.config.env.AgencyMQProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.SerializerMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RabbitMqConfig {

    @Resource
    private AgencyMQProperties agencyMQProperties;

    private static final String AUTHORIZATION_HEADER = "Authorization";

    @Bean(name = "agencyPortalMqConnectionFactory")
    @Primary
    public ConnectionFactory agencyPortalMqConnectionFactory() {
        return connectionFactory(agencyMQProperties.getHost(), agencyMQProperties.getPort(), agencyMQProperties.getVirtualHost(), agencyMQProperties.getUsername(), agencyMQProperties.getPassword());
    }

    private CachingConnectionFactory connectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPublisherConfirms(true);
        return connectionFactory;
    }

    @Bean(name = "agencyPortalRabbitTemplate")
    @Primary
    public RabbitTemplate agencyPortalRabbitTemplate(@Qualifier("agencyPortalMqConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate parsersRabbitTemplate = new RabbitTemplate(connectionFactory);
        parsersRabbitTemplate.setMandatory(true);
        parsersRabbitTemplate.setMessageConverter(new SerializerMessageConverter());
        return parsersRabbitTemplate;
    }

}