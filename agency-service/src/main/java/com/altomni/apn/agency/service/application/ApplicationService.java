package com.altomni.apn.agency.service.application;

import com.altomni.apn.agency.dto.ApplicationBriefInfoVM;
import com.altomni.apn.agency.dto.InProcessApplicationBriefVM;
import com.altomni.apn.agency.web.rest.vm.ApplicationIdAndStatusListVO;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;

public interface ApplicationService {

    Integer countUnfinishedApplicationsByIds(List<Long> applicationIds);

    TalentRecruitmentProcessVO submitToJob(TalentRecruitmentProcessSubmitToJobVO submitToJobVO);

    ApplicationIdAndStatusListVO getAllApplicationsStatus(Set<Long> applicationIds, NodeType nodeType, Pageable pageable);

    LinkedHashMap<String, Integer> getMyApplicationCandidates(Instant startTime, Instant endTime, List<Long> userIdList, List<Long> applicationIds);

    List<InProcessApplicationBriefVM> getUnfinishedApplicationsByIds(Set<Long> applicationIds);

    List<ApplicationBriefInfoVM> getApplicationsBriefByIds(Set<Long> applicationIds);

}
