package com.altomni.apn.application.config.env;

import com.altomni.apn.common.dto.TitleInfoDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * 获取多语言配置信息
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "api-properties")
public class ApplicationApiPromptProperties {

    Map<String, TitleInfoDTO> appl;
}