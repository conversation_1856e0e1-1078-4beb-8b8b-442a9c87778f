package com.altomni.apn.agency.repository;

import com.altomni.apn.common.domain.user.TimeSheetUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the AgencySubmitDuplicateTalent entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TimesheetUserCustomRepository extends JpaRepository<TimeSheetUser, Long> {

    @Query(value = " select t.* from time_sheet_user t where SUBSTRING_INDEX(t.uid,'_',1) in ?1 and t.user_type = ?2 ", nativeQuery = true)
    List<TimeSheetUser> findByUidLikeAndUserType(List<Long> talentId, Integer userType);

}
