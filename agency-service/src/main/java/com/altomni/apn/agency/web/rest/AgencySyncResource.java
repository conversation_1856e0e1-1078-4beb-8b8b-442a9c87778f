package com.altomni.apn.agency.web.rest;

import com.altomni.apn.agency.dto.AgencySyncToMqVM;
import com.altomni.apn.agency.service.sync.AgencySyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class AgencySyncResource {

    @Resource
    private AgencySyncService agencySyncService;

    @PostMapping("/canal/sync-agencies-to-mq")
    public ResponseEntity<Void> syncJobsToAgencyMQ(@RequestBody AgencySyncToMqVM agencySyncToMqVM) {
//        log.info("[AgencySyncResource: syncJobsToAgencyMQ @{}] syncJobsToAgencyMQ: {}", SecurityUtils.getUserId(), agencySyncToMqVM);
        agencySyncService.syncAgenciesToMQ(agencySyncToMqVM.getAgencyIds(), agencySyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

}
