package com.altomni.apn.agency.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.commonService}")
    private String apnCommonServiceUrl;

    @Value("${application.redisMsgExpDay}")
    private Integer redisMsgExpDay;

}
