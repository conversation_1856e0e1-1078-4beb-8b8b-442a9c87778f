package com.altomni.apn.agency.service.company;

import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;
import com.altomni.apn.company.service.dto.CompanyDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@Component
@FeignClient(value = "company-service")
public interface CompanyClient {

    @GetMapping("/company/api/v3/company/{id}")
    ResponseEntity<CompanyDTO> getCompany(@PathVariable("id") Long id);

    @GetMapping("/company/api/v3/skip-submit-to-am-companies/{companyId}/all-users")
    ResponseEntity<List<SkipSubmitToAmCompanyUser>> getAllSkipSubmitToAmCompanyUsers(@PathVariable("companyId") Long companyId);

    @GetMapping("/company/api/v3/sales-leads/{companyId}/am")
    ResponseEntity<List<Long>> getAllAmByCompany(@PathVariable("companyId") Long companyId);

    @PostMapping("/company/api/v3/company/job-to-company")
    ResponseEntity<Map<Long, String>> getCompanyNamesByJobIds(@RequestBody List<Long> jobIds);

}
