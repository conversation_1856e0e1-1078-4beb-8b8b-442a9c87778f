package com.altomni.apn.agency.repository;

import com.altomni.apn.agency.domain.AgencySharedJob;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;


/**
 * Spring Data  repository for the AgencySharedJob entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AgencySharedJobRepository extends JpaRepository<AgencySharedJob, Long> {

    @Query(value = "SELECT asj.jobId FROM AgencySharedJob asj WHERE asj.agencyId = ?1 AND asj.shareStatus = ?2")
    List<Long> findAllJobIdsByAgencyIdAndShareStatus(Long agencyId, JobShareStatus shareStatus);

    List<AgencySharedJob> findAllByAgencyIdAndShareStatus(Long agencyId, JobShareStatus shareStatus);

    Optional<AgencySharedJob> findByAgencyIdAndJobId(Long agencyId, Long jobId);

//    List<AgencySharedJob> findAllByJobId(Long jobId);

    List<AgencySharedJob> findAllByJobIdAndShareStatus(Long jobId, JobShareStatus shareStatus);

    List<AgencySharedJob> findAllByJobId(Long jobId);

    List<AgencySharedJob> findAllByJobIdAndAgencyIdIn(Long jobId, List<Long> agencyIds);

    @Query(value = "SELECT asj.job_id AS 'jobId', " +
            "j.title AS 'jobTitle', " +
            "j.status as 'jobStatus', " +
            "j.created_date as 'jobCreatedDate', " +
            "j.pteam_id as 'pteamId', " +
            "c.id as 'companyId', " +
            "c.full_business_name as 'companyName' " +
            "FROM agency_shared_job asj " +
            "LEFT JOIN job j ON asj.job_id = j.id " +
            "LEFT JOIN company c ON j.company_id = c.id " +
            "WHERE asj.agency_id = ?1 " +
            "AND j.title LIKE %?2% AND c.full_business_name LIKE %?3%",
            countQuery = "SELECT COUNT(asj.id) FROM agency_shared_job asj " +
                    "LEFT JOIN job j ON asj.job_id = j.id " +
                    "LEFT JOIN company c ON j.company_id = c.id " +
                    "WHERE asj.agency_id = ?1 " +
                    "AND j.title LIKE %?2% AND c.full_business_name LIKE %?3%", nativeQuery = true)
    Page<Map<String, Object>> findAllJobsWithDetailsByAgencyId(
            Long agencyId, String jobTitle, String companyName, Pageable pageable);

}
