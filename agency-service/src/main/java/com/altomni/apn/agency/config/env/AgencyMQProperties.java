package com.altomni.apn.agency.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
public class AgencyMQProperties {

    @Value("${spring.rabbitmq.addresses}")
    private String host;

    @Value("${spring.rabbitmq.port}")
    private int port;

    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;


    @Value("${spring.rabbitmq.username}")
    private String username;

    @Value("${spring.rabbitmq.password}")
    private String password;

    @Value("${application.esfillerMQ.esFillerExchange}")
    private String esfillerMQExchange;

    @Value("${application.esfillerMQ.toEsFillerRoutingKey}")
    private String toEsFillerRoutingKey;

    @Value("${application.agencyPortal.agencyPortalExchange}")
    private String agencyPortalExchange;

    @Value("${application.agencyPortal.agencyPortalRoutingKey}")
    private String agencyPortalRoutingKey;

    @Value("${application.agencyPortal.agencyPortalQueue}")
    private String agencyPortalQueue;

    @Value("${application.esfillerMQ.retryThreshold}")
    private int retryThreshold;

    @Value("${application.esfillerMQ.notification.lark.webhookKey}")
    private String larkWebhookKey;

    @Value("${application.esfillerMQ.notification.lark.webhookUrl}")
    private String larkWebhookUrl;

}
