package com.altomni.apn.agency.web.rest;

import com.altomni.apn.agency.dto.AgencyActivityDTO;
import com.altomni.apn.agency.dto.AgencyActivityForApplicationDTO;
import com.altomni.apn.agency.dto.IdListDTO;
import com.altomni.apn.agency.service.agency.AgencyActivityService;
import com.altomni.apn.agency.web.rest.vm.AgencyActivityVO;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class AgencyActivityResource {

    @Resource
    private AgencyActivityService agencyActivityService;

    @GetMapping("/activity/{agencyId}/all")
    public ResponseEntity<List<AgencyActivityVO>> getAllAgencyActivities(@PathVariable Long agencyId, @RequestParam(required = false) String search, Pageable pageable) {
        log.info("[APN: AgencyActivity @{}] REST request to get all agency activities by agencyId: {}, with search param: {}", SecurityUtils.getUserId(), agencyId, search);

        Page<AgencyActivityVO> agencyList = agencyActivityService.getActivities(agencyId, search, pageable);

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(agencyList, "/api/v3/activity/" + agencyId + "/all");
        return ResponseEntity.ok().headers(headers).body(agencyList.getContent());
    }

    @GetMapping("/activity/job/{jobId}")
    public ResponseEntity<List<AgencyActivityVO>> getAgencyActivitiesByJobId(@PathVariable Long jobId, Pageable pageable) {
        log.info("[APN: AgencyActivity @{}] REST request to get all agency activities by jobId: {}", SecurityUtils.getUserId(), jobId);

        Page<AgencyActivityVO> agencyList = agencyActivityService.getActivitiesByJobId(jobId, pageable);

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(agencyList, "/api/v3/activity/job/" + jobId);
        return ResponseEntity.ok().headers(headers).body(agencyList.getContent());
    }

    @PostMapping("/activity/create")
    public ResponseEntity<AgencyActivityVO> createNewActivity(@RequestBody AgencyActivityDTO agencyActivityDTO) {
        log.info("[APN: AgencyActivity @{}] REST request to create new agencyActivity: {}", SecurityUtils.getUserId(), agencyActivityDTO);

        AgencyActivityVO agencyActivityVO = agencyActivityService.createActivity(agencyActivityDTO);

        return ResponseEntity.ok(agencyActivityVO);
    }

    @PostMapping("/activity/create/application")
    public ResponseEntity<AgencyActivityVO> createNewActivity(@RequestBody AgencyActivityForApplicationDTO agencyActivityForApplicationDTO) {
        log.info("[APN: AgencyActivity @{}] REST request to create new agencyActivityForApplicationDTO: {}", SecurityUtils.getUserId(), agencyActivityForApplicationDTO);

        AgencyActivityVO agencyActivityVO = agencyActivityService.createActivityForApplication(agencyActivityForApplicationDTO);

        return ResponseEntity.ok(agencyActivityVO);
    }

    @GetMapping("/portal/activity/all")
    public ResponseEntity<List<AgencyActivityVO>> getAllUnreadActivitiesByDefault(@RequestParam(required = false) Instant startTime, @RequestParam(required = false) Instant endTime, @PageableDefault(value = 100, sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN: AgencyActivity @{}] REST request to get all agency activities by default agencyId: {}", SecurityUtils.getUserId(), SecurityUtils.getAgencyId());

        Page<AgencyActivityVO> agencyList = agencyActivityService.getALlUnreadActivities(startTime, endTime, SecurityUtils.getAgencyId(), pageable);

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(agencyList, "/api/v3/activity/all");
        return ResponseEntity.ok().headers(headers).body(agencyList.getContent());
    }

    @PostMapping("/portal/activity/mark-read")
    public ResponseEntity<Void> markActivityToReadByDefault(@RequestBody IdListDTO idListDTO) {
        log.info("[APN: AgencyActivity @{}] REST request to mark agency activities to read by default agencyId: {}, with search param: {}", SecurityUtils.getUserId(), SecurityUtils.getAgencyId(), idListDTO);

        agencyActivityService.markAsRead(idListDTO.getIds());

        return ResponseEntity.ok().build();
    }

}
