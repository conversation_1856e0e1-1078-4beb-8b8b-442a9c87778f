package com.altomni.apn.agency.web.rest.vm;

import com.altomni.apn.agency.domain.Agency;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgencyBriefVO {

    private Long id;

    private String name;

    private ActiveStatus status;

    private String description;

    @Transient
    private Long agencyCreatorUserId;

    @Transient
    private Long agencyOwnerUserId;

    private boolean hasEditPermission;

    private boolean hasViewPermission;

    public AgencyBriefVO(final Agency agency) {
        this.id = agency.getId();
        this.name = agency.getName();
        this.status = agency.getStatus();
        this.description = agency.getDescription();
    }

    public AgencyBriefVO(Long id, String name, ActiveStatus status, String description, Long agencyCreatorUserId, Long agencyOwnerUserId) {
        this.id = id;
        this.name = name;
        this.status = status;
        this.description = description;
        this.agencyCreatorUserId = agencyCreatorUserId;
        this.agencyOwnerUserId = agencyOwnerUserId;
    }

}
