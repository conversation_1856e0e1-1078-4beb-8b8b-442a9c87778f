package com.altomni.apn.agency.service.job.impl;

import com.altomni.apn.agency.service.job.JobClient;
import com.altomni.apn.agency.service.job.JobService;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class JobServiceImpl implements JobService {

    @Resource
    private JobClient jobClient;

    @Override
    public JobDTOV3 getJob(Long jobId) {
        ResponseEntity<JobDTOV3> response = jobClient.getJob(jobId);
        if (response == null || response.getBody() == null) {
            return null;
        }
        return response.getBody();
    }

    @Override
    public void updateStatus(Long jobId, JobStatus status) {
        jobClient.numberOfOfferAcceptedEqualsOpenings(jobId, status.toDbValue());
    }

    @Override
    public void updateNoSubmitCandidateReminderXxlJobForJob(Long jobId, List<TalentRecruitmentProcessKpiUserVO> kpiUsers) {
        //TODO 待删除
        jobClient.updateNoSubmitCandidateReminderXxlJobForJob(jobId, kpiUsers);
    }

    @Override
    public void updateNoInterviewCandidateReminderXxlJobForJob(Long jobId, List<TalentRecruitmentProcessKpiUserVO> kpiUsers) {
        //TODO 待删除
        jobClient.updateNoInterviewCandidateReminderXxlJobForJob(jobId, kpiUsers);
    }
}
