package com.altomni.apn.agency.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.agency.domain.enumeration.StringListToJsonConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * A RecruitmentProcessNodePageSection.
 */
@Entity
@Table(name = "agency_submit_duplicate_talent")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AgencySubmitDuplicateTalent extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5070679567300167427L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "agency_id")
    private Long agencyId;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "original_talent_json")
    private String originalTalentJson;

    @Column(name = "merged_talent_id")
    private Long mergedTalentId;

    @Column(name = "duplicate_talent_ids")
    @Convert(converter = StringListToJsonConverter.class)
    private List<Long> duplicateTalentIds;



}
