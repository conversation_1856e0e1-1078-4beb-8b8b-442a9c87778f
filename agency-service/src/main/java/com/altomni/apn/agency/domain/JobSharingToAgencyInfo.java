package com.altomni.apn.agency.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

/**
 * A RecruitmentProcessNodePageSection.
 */
@Entity
@Table(name = "job_sharing_to_agency_info")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobSharingToAgencyInfo extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5070679567300167427L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "sharing_to_agency_info")
    private String sharingToAgencyInfo;

    @Column(name = "company_name_visible")
    private Boolean companyNameVisible;

    public JobSharingToAgencyInfo(Long jobId, String sharingToAgencyInfo) {
        this.jobId = jobId;
        this.sharingToAgencyInfo = sharingToAgencyInfo;
    }

}
