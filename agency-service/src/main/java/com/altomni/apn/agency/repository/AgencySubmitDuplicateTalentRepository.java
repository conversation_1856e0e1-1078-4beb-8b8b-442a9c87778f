package com.altomni.apn.agency.repository;

import com.altomni.apn.agency.domain.AgencySubmitDuplicateTalent;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the AgencySubmitDuplicateTalent entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AgencySubmitDuplicateTalentRepository extends JpaRepository<AgencySubmitDuplicateTalent, Long> {

    Integer countAgencySubmitDuplicateTalentByAgencyIdAndJobId(Long agencyId, Long jobId);

    List<AgencySubmitDuplicateTalent> findAllByAgencyIdAndJobId(Long agencyId, Long jobId);

}
