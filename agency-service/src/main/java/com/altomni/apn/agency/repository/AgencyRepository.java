package com.altomni.apn.agency.repository;

import com.altomni.apn.agency.domain.Agency;
import com.altomni.apn.agency.web.rest.vm.AgencyBriefVO;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * Spring Data  repository for the Agency entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AgencyRepository extends JpaRepository<Agency, Long> {

    @Query(value = "SELECT a.id FROM Agency a WHERE a.agencyOwnerUserId = ?1")
    List<Long> getAgencyIdByAgencyOwnerUserId(Long userId);

    @Query(value = "SELECT a.id FROM Agency a WHERE a.agencyCreatorUserId = ?1")
    Set<Long> getAgencyIdByAgencyCreatorUserId(Long userId);

    @Query(value = "SELECT NEW com.altomni.apn.agency.web.rest.vm.AgencyBriefVO(a.id, a.name, a.status, a.description, a.agencyCreatorUserId, a.agencyOwnerUserId) FROM Agency a WHERE a.agencyCreatorUserId = ?1")
    List<AgencyBriefVO> getAgencyBriefListByAgencyCreatorUserId(Long userId);

    @Query(value = "SELECT NEW com.altomni.apn.agency.web.rest.vm.AgencyBriefVO(a.id, a.name, a.status, a.description, a.agencyCreatorUserId, a.agencyOwnerUserId) FROM Agency a WHERE a.tenantId = ?1")
    List<AgencyBriefVO> getAgencyBriefListByTenantId(Long tenantId);

    @Query(value = "SELECT a.id FROM Agency a WHERE a.tenantId = ?1")
    Set<Long> getAgencyIdByTenantId(Long tenantId);

    List<Agency> findAllByIdIn(Collection<Long> ids);

//    Page<Agency> findAllByIdInAndNameLike(Collection<Long> ids, String name, Pageable pageable);
    @Query("SELECT a FROM Agency a WHERE a.id IN :ids AND a.name LIKE %:name%")
    Page<Agency> findAllByIdInAndNameLike(@Param("ids") Collection<Long> ids, @Param("name") String name, Pageable pageable);

    @Query("SELECT a FROM Agency a WHERE a.status = :status AND a.id IN :ids AND a.name LIKE %:name%")
    Page<Agency> findAllByStatusAndIdInAndNameLike(@Param("status") ActiveStatus status, @Param("ids") Collection<Long> ids, @Param("name") String name, Pageable pageable);

    Page<Agency> findByIdIn(Collection<Long> ids, Pageable pageable);

    Page<Agency> findByAgencyCreatorUserId(Long agencyCreatorUserId, Pageable pageable);

    @Query("SELECT a FROM Agency a WHERE a.agencyCreatorUserId = :creatorUserId AND a.name LIKE %:name%")
    Page<Agency> findAllByAgencyCreatorUserIdAndNameLike(@Param("creatorUserId") Long creatorUserId, @Param("name") String name, Pageable pageable);

    Page<Agency> findByStatusAndIdIn(ActiveStatus status, Collection<Long> ids, Pageable pageable);

    List<Agency> findAllByStatusAndIdIn(ActiveStatus status, Collection<Long> ids);

    Page<Agency> findByTenantId(Long tenantId, Pageable pageable);

    boolean existsByTenantId(Long tenantId);

    @Query("SELECT a FROM Agency a WHERE a.tenantId = :tenantId AND a.name LIKE %:name%")
    Page<Agency> findAllByTenantIdAndNameLike(@Param("tenantId") Long tenantId, @Param("name") String name, Pageable pageable);

//    @Query("SELECT a FROM Agency a " +
//            "WHERE ((a.phone IS NOT NULL AND a.phone != '' AND a.phone = :phone) " +
//            "OR (a.email IS NOT NULL AND a.email != '' AND a.email = :email) " +
//            "OR a.name = :name) " +
//            "AND (:id IS NULL OR a.id != :id)")
//    Optional<Agency> existsByPhoneOrEmailExcludingId(@Param("phone") String phone, @Param("email") String email, @Param("name") String name, @Param("id") Long id);

    @Query("SELECT a FROM Agency a " +
            "WHERE ((a.phone IS NOT NULL AND a.phone != '' AND a.phone = :phone) " +
            "OR (a.email IS NOT NULL AND a.email != '' AND a.email = :email)) " +
            "AND (:id IS NULL OR a.id != :id) ")
    Optional<Agency> existsByPhoneOrEmailExcludingId(@Param("phone") String phone, @Param("email") String email, @Param("id") Long id);

//    @Query("SELECT a FROM Agency a " +
//            "WHERE ((a.email IS NOT NULL AND a.email != '' AND a.email = :email) OR " +
//            " a.name = :name) " +
//            "AND (:id IS NULL OR a.id != :id)")
//    Optional<Agency> existsByEmailExcludingId(@Param("email") String email, @Param("name") String name, @Param("id") Long id);

    @Query("SELECT a FROM Agency a " +
            "WHERE (a.email IS NOT NULL AND a.email != '' AND a.email = :email) " +
            "AND (:id IS NULL OR a.id != :id) ")
    Optional<Agency> existsByEmailExcludingId(@Param("email") String email, @Param("id") Long id);

    @Query("SELECT a FROM Agency a " +
            "WHERE a.name = :name " +
            "AND (:id IS NULL OR a.id != :id) AND a.tenantId = :tenantId")
    Optional<Agency> existsByNameExcludingId(@Param("name") String name, @Param("id") Long id, @Param("tenantId") Long tenantId);
}
