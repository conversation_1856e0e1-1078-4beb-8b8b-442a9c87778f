package com.altomni.apn.agency.web.rest;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.agency.dto.AgencyActivityDTO;
import com.altomni.apn.agency.dto.AgencyEmailDTO;
import com.altomni.apn.agency.service.agency.AgencyActivityService;
import com.altomni.apn.agency.service.common.CommonService;
import com.altomni.apn.common.domain.enumeration.agency.AgencyActivityType;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/v3/mail")
public class AgencyMailResource {

    @Resource
    private CommonService commonService;

    @Resource
    private AgencyActivityService agencyActivityService;

    @ApiOperation(value = "front-end use this api to send emails")
    @PostMapping(value = "/save-draft", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> saveDraft(@RequestBody AgencyEmailDTO agencyEmailDTO) {
        log.info("[APN: Agency @{}] REST request to save email draft, with params AgencyEmailDTO: {}", SecurityUtils.getUserId(), agencyEmailDTO);
        agencyEmailDTO.setCustomerId1("AGENCY" + agencyEmailDTO.getAgencyId());
        agencyEmailDTO.setCustomerId2("USER" + SecurityUtils.getUserId());
        return commonService.saveDraft(agencyEmailDTO);
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @PostMapping(value = "/send-draft", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> sendDraft(@RequestBody AgencyEmailDTO agencyEmailDTO) {
        log.info("[APN: Agency @{}] REST request to send email draft, with params AgencyEmailDTO: {}", SecurityUtils.getUserId(), agencyEmailDTO);
        agencyEmailDTO.setCustomerId1("AGENCY" + agencyEmailDTO.getAgencyId());
        agencyEmailDTO.setCustomerId2("USER" + SecurityUtils.getUserId());
        ResponseEntity<String> res = commonService.sendDraft(agencyEmailDTO);
        String body = res != null ? res.getBody() : null;
        if (StringUtils.isNotBlank(body)) {
            JSONObject json = JSONObject.parseObject(body);
            if (json.containsKey("id")) {
                AgencyActivityDTO agencyActivityDTO = new AgencyActivityDTO();
                agencyActivityDTO.setAgencyId(agencyEmailDTO.getAgencyId());
                agencyActivityDTO.setActivityType(AgencyActivityType.EMAIL_CONTACT);
                JSONObject details = new JSONObject();
                details.put("emailLogId", json.getString("id"));
                agencyActivityDTO.setDetails(details);

                agencyActivityService.createActivity(agencyActivityDTO);

                return res;
            }
        }

        AgencyActivityDTO agencyActivityDTO = new AgencyActivityDTO();
        agencyActivityDTO.setAgencyId(agencyEmailDTO.getAgencyId());
        agencyActivityDTO.setActivityType(AgencyActivityType.EMAIL_CONTACT);
        JSONObject details = new JSONObject();
        details.put("emailStatus", "FAILED");
        details.put("emailVM", agencyEmailDTO.toString());
        agencyActivityDTO.setDetails(details);
        agencyActivityService.createActivity(agencyActivityDTO);

        return res;
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @GetMapping(value = "/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getById(@PathVariable Long id) {
        log.info("[APN: Agency @{}] REST request to get email by id: {}", SecurityUtils.getUserId(), id);
        return commonService.getMailById(id);
    }

}
