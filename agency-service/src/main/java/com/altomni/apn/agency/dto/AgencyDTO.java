package com.altomni.apn.agency.dto;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * A AgencyDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencyDTO {

    private String name;

    private String phone;

    private String email;

    private String agencyContactFirstName;

    private String agencyContactLastName;

    private String description;

    private Long agencyOwner;

    private List<Long> sharedUsers;

    private ActiveStatus status;

    private String timezone;

    private JSONObject location;

}
