package com.altomni.apn.agency.web.rest.vm;

import com.altomni.apn.agency.domain.Agency;
import com.altomni.apn.agency.dto.AssociatedUserDTO;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatusConverter;
import com.altomni.apn.common.utils.CommonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AgencyVO extends AbstractAuditingEntity implements Serializable {

    @ApiModelProperty(value = "Agency ID")
    private Long id;

    @ApiModelProperty(value = "Agency name")
    private String name;

    @Convert(converter = ActiveStatusConverter.class)
    private ActiveStatus status;

    @ApiModelProperty(value = "Agency phone")
    private String phone;

    private String email;

    private String agencyContactFirstName;

    private String agencyContactLastName;

    private String agencyContactFullName;

    private String description;

    private String timezone;

    @JsonRawValue
    private String location;

    private AssociatedUserDTO agencyOwner;

    private AssociatedUserDTO agencyCreator;

    private List<AssociatedUserDTO> sharedUsers;

    private boolean hasNewCandidate = false;

    public AgencyVO(final Agency agency, AssociatedUserDTO agencyOwner, AssociatedUserDTO agencyCreator, List<AssociatedUserDTO> sharedUsers) {
        this.id = agency.getId();
        this.name = agency.getName();
        this.status = agency.getStatus();
        this.phone = agency.getPhone();
        this.email = agency.getEmail();
        this.agencyContactFirstName = agency.getAgencyContactFirstName();
        this.agencyContactLastName = agency.getAgencyContactLastName();
        if (CommonUtils.isChineseCharacter(this.agencyContactFirstName + this.agencyContactLastName)) {
            this.agencyContactFullName = this.agencyContactLastName + this.agencyContactFirstName;
        } else {
            this.agencyContactFullName = agencyContactFirstName + " " + agencyContactLastName;
        }
        this.description = agency.getDescription();
        this.timezone = agency.getTimezone();
        this.location = agency.getLocation();
        this.agencyOwner = agencyOwner;
        this.agencyCreator = agencyCreator;
        this.sharedUsers = sharedUsers;
        this.setCreatedBy(agency.getCreatedBy());
        this.setCreatedDate(agency.getCreatedDate());
        this.setLastModifiedBy(agency.getLastModifiedBy());
        this.setLastModifiedDate(agency.getLastModifiedDate());
    }

}
