package com.altomni.apn.agency.repository;

import com.altomni.apn.agency.domain.AgencyActivity;
import com.altomni.apn.common.domain.enumeration.agency.AgencyActivityType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.Set;

/**
 * Spring Data  repository for the AgencyActivity entity.
 */
@Repository
public interface AgencyActivityRepository extends JpaRepository<AgencyActivity, Long> {

    @Query(value = "SELECT aa FROM AgencyActivity aa WHERE aa.agencyId = ?1 AND (?2 IS NULL OR ?2 = '' OR aa.jobTitle LIKE CONCAT('%', ?2, '%') OR aa.talentName LIKE CONCAT('%', ?2, '%'))")
    Page<AgencyActivity> getAllByAgencyId(Long agencyId, String search, Pageable pageable);

    @Query(value = "SELECT aa FROM AgencyActivity aa WHERE aa.jobId = ?1")
    Page<AgencyActivity> getAllByAgencyIdAndJobId(Long jobId, Pageable pageable);

    Page<AgencyActivity> getAllByAgencyIdAndReadStatusAndActivityTypeIn(Long agencyId, Boolean readStatus, Set<AgencyActivityType> activityTypes, Pageable pageable);

    Page<AgencyActivity> getAllByAgencyIdAndReadStatusAndActivityTypeInAndCreatedDateBetween(Long agencyId, Boolean readStatus, Set<AgencyActivityType> activityTypes, Instant startTime, Instant endTime, Pageable pageable);

}
