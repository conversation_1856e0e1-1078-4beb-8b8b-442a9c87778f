package com.altomni.apn.agency.repository;

import com.altomni.apn.agency.domain.AgencySharingUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Set;


/**
 * Spring Data  repository for the AgencySharingUser entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AgencySharingUserRepository extends JpaRepository<AgencySharingUser, Long> {

    List<AgencySharingUser> findAllByAgencyId(Long agencyId);

    @Query(value = "SELECT asu.agencyId FROM AgencySharingUser asu WHERE asu.userId = ?1")
    Set<Long> findAllAgencyIdsByUserId(Long userId);

    @Query(value = "SELECT asu.userId FROM AgencySharingUser asu WHERE asu.agencyId = ?1")
    Set<Long> findAllUserIdsByAgencyId(Long agencyId);

    @Modifying
    @Transactional
    void deleteAllByAgencyId(Long agencyId);
}
