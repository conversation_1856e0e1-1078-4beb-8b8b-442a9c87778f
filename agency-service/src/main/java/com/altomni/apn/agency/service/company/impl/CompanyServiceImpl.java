package com.altomni.apn.agency.service.company.impl;

import com.altomni.apn.agency.service.company.CompanyClient;
import com.altomni.apn.agency.service.company.CompanyService;
import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;
import com.altomni.apn.company.service.dto.CompanyDTO;
import org.apache.commons.collections4.MapUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Service
public class CompanyServiceImpl implements CompanyService {

    @Resource
    private CompanyClient companyClient;

    @Override
    public CompanyDTO getCompany(Long companyId) {
        ResponseEntity<CompanyDTO> response = companyClient.getCompany(companyId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<SkipSubmitToAmCompanyUser> getAllSkipSubmitToAmCompanyUsers(Long companyId) {
        ResponseEntity<List<SkipSubmitToAmCompanyUser>> response = companyClient.getAllSkipSubmitToAmCompanyUsers(companyId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<Long> getAllAmIdsByCompanyId(Long companyId) {
        ResponseEntity<List<Long>> allAmByCompany = companyClient.getAllAmByCompany(companyId);
        List<Long> amIds = new ArrayList<>();
        if (Objects.nonNull(allAmByCompany) && allAmByCompany.getStatusCode() == HttpStatus.OK){
            amIds.addAll(allAmByCompany.getBody());
        }
        return amIds;
    }

    @Override
    public String getCompanyNameByJobId(Long jobId) {
        List<Long> jobIdList = new ArrayList<>();
        jobIdList.add(jobId);
        ResponseEntity<Map<Long, String>> response = companyClient.getCompanyNamesByJobIds(jobIdList);
        if (response != null && response.getStatusCode() == HttpStatus.OK){
            Map<Long, String> companyNames = response.getBody();
            if (MapUtils.isNotEmpty(companyNames)) {
                return companyNames.getOrDefault(jobId, null);
            } else {
                return null;
            }
        }
        return null;
    }
}
