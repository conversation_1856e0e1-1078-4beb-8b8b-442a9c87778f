package com.altomni.apn.agency.dto;

import com.altomni.apn.agency.domain.Agency;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * A AgencyDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencySyncToMqDTO {

    private Long agencyId;

    private String name;

    private String phone;

    private String email;

    private ActiveStatus status;

    private String agencyContactFirstName;

    private String agencyContactLastName;

    private Long apnTenantId;

    private String timezone;

    private String type;

    public AgencySyncToMqDTO(Agency agency, Long tenantId, String type) {
        this.agencyId = agency.getId();
        this.name = agency.getName();
        this.phone = agency.getPhone();
        this.email = agency.getEmail();
        this.status = agency.getStatus();
        this.agencyContactFirstName = agency.getAgencyContactFirstName();
        this.agencyContactLastName = agency.getAgencyContactLastName();
        this.timezone = agency.getTimezone();
        this.apnTenantId = tenantId;
        this.type = type;
    }

}
