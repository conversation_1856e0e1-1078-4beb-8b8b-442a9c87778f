package com.altomni.apn.agency.web.rest.vm;

import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SharedJobVO {

    private Long companyId;

    private String companyName;

    private Long jobId;

    private String jobTitle;

    private String jobStatus;

    private Boolean isVisible;

    private Instant jobCreatedDate;

    private Integer submittedTalentsCount;

    private Integer duplicateTalentsCount;

    private Boolean hasNewCandidate;

    private List<Long> submittedTalentIds;

    public SharedJobVO(JobDTOV3 jobDTOV3) {
        this.companyId = jobDTOV3.getCompanyId();
        this.companyName = jobDTOV3.getCompany().getName();
        this.jobId = jobDTOV3.getId();
        this.jobTitle = jobDTOV3.getTitle();
        this.jobStatus = jobDTOV3.getStatus().name();
        this.jobCreatedDate = jobDTOV3.getCreatedDate();
    }

    public SharedJobVO(Long companyId, String companyName, Long jobId, String jobTitle, JobStatus jobStatus, Boolean isVisible, Instant jobCreatedDate) {
        this.companyId = companyId;
        this.companyName = companyName;
        this.jobId = jobId;
        this.jobTitle = jobTitle;
        this.jobStatus = jobStatus.name();
        this.jobCreatedDate = jobCreatedDate;
        this.isVisible = isVisible;
    }

    public static SharedJobVO fromMap(Map<String, Object> jobMap) {
        Long companyId = Long.valueOf(StringUtil.valueOf(jobMap.get("companyId")));
        String companyName = StringUtil.valueOf(jobMap.get("companyName"));
        Long jobId = Long.valueOf(StringUtil.valueOf(jobMap.get("jobId")));
        String jobTitle = StringUtil.valueOf(jobMap.get("jobTitle"));
        JobStatus jobStatus = JobStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(jobMap.get("jobStatus"))));
        Boolean isVisible = !StringUtils.equals("10000000000", StringUtil.valueOf(jobMap.get("pteamId"))); // if pteam_id is '10000000000', then it's private job
        Instant jobCreatedDate = DateUtil.fromStringToInstant(StringUtil.valueOf(jobMap.get("jobCreatedDate")));

        return new SharedJobVO(companyId, companyName, jobId, jobTitle, jobStatus, isVisible, jobCreatedDate);
    }

}
