package com.altomni.apn.agency.service.agency;

import com.altomni.apn.agency.dto.AgencyActivityDTO;
import com.altomni.apn.agency.dto.AgencyActivityForApplicationDTO;
import com.altomni.apn.agency.web.rest.vm.AgencyActivityVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.List;

/**
 * Service Interface for managing AgencyActivity.
 */
public interface AgencyActivityService {

    Page<AgencyActivityVO> getActivities(Long agencyId, String search, Pageable pageable);

    Page<AgencyActivityVO> getActivitiesByJobId(Long jobId, Pageable pageable);

    Page<AgencyActivityVO> getALlUnreadActivities(Instant startTime, Instant endTime, Long agencyId, Pageable pageable);

    AgencyActivityVO createActivity(AgencyActivityDTO activityDTO);

    AgencyActivityVO createActivityForApplication(AgencyActivityForApplicationDTO agencyActivityForApplicationDTO);

    List<AgencyActivityVO> saveAll(List<AgencyActivityDTO> activityDTOs);

    void markAsRead(List<Long> activityIds);

}