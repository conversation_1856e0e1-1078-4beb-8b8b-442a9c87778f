package com.altomni.apn.agency.service.talent;

import com.altomni.apn.agency.domain.AgencySubmitApplication;
import com.altomni.apn.agency.dto.TalentInfoInputWithId;
import com.altomni.apn.agency.dto.TalentInfoInputWithJobIdForApplicationDTO;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.talent.*;
import org.springframework.http.ResponseEntity;

import java.util.Collection;
import java.util.List;

public interface TalentService {

    TalentDTOV3 getTalentById(Long id);

    TalentDTOV3 getTalentWithoutEntity(Long id);

    TalentBriefDTO getTalentBrief(Long id);

    List<TalentBriefDTO> getTalentBriefByTalentIds(Collection<Long> ids);

    List<TalentOwnership> getAllTalentOwners(Long talentId, List<TalentOwnershipType> talentOwnershipType);

    List<TalentOwnership> saveAllOwnerships(List<TalentOwnership> talentOwnerships);

    TalentResumeDTO getTalentResumeByTalentResumeRelationId(Long resumeId);

    void updateTalentExperience(Long talentId, TalentExperienceDTO talentExperienceDTO);

    void deleteTalentExperience(Long talentId, Long talentRecruitmentProcessId);

    void deleteTalentNote(Long noteId);

    List<TalentNoteDTO> getAllNotesByTalentId(Long talentId);

    TalentNote createTalentNote(TalentNote talentNote);

    AgencySubmitApplication createTalentAndApplyToJob(TalentInfoInputWithJobIdForApplicationDTO input);

    String searchCollegeName(String collegeName);

    JobDTOV3 test(Long jobId);

    TalentDTOV3 updateTalentInfo(Long id, TalentInfoInputWithId talentInfoInput);

    List<SuspectedDuplications> suspectedDuplicatePhonesCheck(TalentInfoInputWithJobIdForApplicationDTO talentDTO);
}
