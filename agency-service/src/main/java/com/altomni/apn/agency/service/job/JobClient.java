package com.altomni.apn.agency.service.job;

import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(value = "job-service")
public interface JobClient {

    @GetMapping("/job/api/v3/jobs/brief/{jobId}")
    ResponseEntity<JobDTOV3> getJob(@PathVariable("jobId") Long jobId);

    @PutMapping("/job/api/v3/jobs/number-of-offer-accepted-equals-opengings/{jobId}/{status}")
    void numberOfOfferAcceptedEqualsOpenings(@PathVariable("jobId") Long jobId, @PathVariable("status") Integer status);

    @PutMapping("/job/api/v3/jobs/update-no-submit-candidate-reminder-xxl-job-for-job/{jobId}")
    ResponseEntity<Void> updateNoSubmitCandidateReminderXxlJobForJob(@PathVariable("jobId") Long jobId, @RequestBody List<TalentRecruitmentProcessKpiUserVO> kpiUsers);

    @PutMapping("/job/api/v3/jobs/update-no-interview-candidate-reminder-xxl-job-for-job/{jobId}")
    ResponseEntity<Void> updateNoInterviewCandidateReminderXxlJobForJob(@PathVariable("jobId") Long jobId, @RequestBody List<TalentRecruitmentProcessKpiUserVO> kpiUsers);

    @GetMapping("/job/api/v3/jobs/getJobHash/{id}")
    ResponseEntity<Integer> getJobHash(@PathVariable("id") Long jobId);

//
//    @GetMapping("/job/api/v3/jobs//dict/currency/all")
//    ResponseEntity<List<EnumCurrency>> findAllEnumCurrency();
//
//    @GetMapping("/job/api/v3/dict/currency/info/id/{id}")
//    ResponseEntity<EnumCurrency> findEnumCurrencyById(@PathVariable("id") Integer id);
//
//    @GetMapping("/job/api/v3/dict/currency/info/name/{name}")
//    ResponseEntity<EnumCurrency> findEnumCurrencyByName(@PathVariable("name") String name);

    @GetMapping("/job/api/v3/jobs/no-token/basic-info/{id}")
    ResponseEntity<JobDTOV3> findOneBasicInfoNoToken(@PathVariable("id") Long jobId);
}
