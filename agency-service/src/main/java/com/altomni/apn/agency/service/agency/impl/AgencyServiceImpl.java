package com.altomni.apn.agency.service.agency.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.agency.config.env.ApplicationProperties;
import com.altomni.apn.agency.domain.*;
import com.altomni.apn.agency.dto.*;
import com.altomni.apn.agency.repository.*;
import com.altomni.apn.agency.service.agency.AgencyActivityService;
import com.altomni.apn.agency.service.agency.AgencyService;
import com.altomni.apn.agency.service.application.ApplicationService;
import com.altomni.apn.agency.service.common.CommonService;
import com.altomni.apn.agency.service.company.CompanyService;
import com.altomni.apn.agency.service.job.JobService;
import com.altomni.apn.agency.service.rabbitmq.RabbitMqService;
import com.altomni.apn.agency.service.redis.RedisService;
import com.altomni.apn.agency.service.talent.TalentService;
import com.altomni.apn.agency.service.user.UserService;
import com.altomni.apn.agency.web.rest.vm.*;
import com.altomni.apn.common.config.constants.ElasticSearchConstants;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.agency.AgencyActivityType;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetUserType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.redis.ParserResponse;
import com.altomni.apn.common.dto.search.*;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.WithDataException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.altomni.apn.common.config.constants.RedisConstants.*;

/**
 * Service Implementation for managing RecruitmentProcessNode.
 */
@Service
@Transactional
public class AgencyServiceImpl implements AgencyService {

    private final Logger log = LoggerFactory.getLogger(AgencyServiceImpl.class);

    private static final List<String> ES_AGENCY_SOURCES = Arrays.asList("agencyContactName.fullName","agencyContactName.firstName","agencyContactName.lastName","lastModifiedDate","phonesDisplay", "emails","active","description","responsibility0","responsibility1","responsibility2","agencyName","createdDate");

    private static final List<String> ES_AGENCY_JOB_SOURCES = Arrays.asList("title","status","startDate","companyName","companyId", "createdDate", "affiliations");

    private static final int REDIS_NEW_APPLICATION_REMINDER_TIME = 3600 * 24 * 30; // 30 days

    @Resource
    private AgencyRepository agencyRepository;

    @Resource
    private AgencySharingUserRepository agencySharingUserRepository;

    @Resource
    private AgencySharedJobRepository agencySharedJobRepository;

    @Resource
    private AgencySubmitApplicationRepository agencySubmitApplicationRepository;

    @Resource
    private AgencySubmitDuplicateTalentRepository agencySubmitDuplicateTalentRepository;

    @Resource
    private UserService userService;

    @Resource
    private JobService jobService;

    @Resource
    private TalentService talentService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private JobSharingToAgencyInfoRepository jobSharingToAgencyInfoRepository;

    @Resource
    private AgencyActivityService agencyActivityService;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private RedisService redisService;

    @Resource
    private CompanyService companyService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private HttpService httpService;

    @Resource
    private TimesheetUserCustomRepository timesheetUserCustomRepository;

    @Resource
    private CommonService commonService;

    private int redisNewApplicationReminderExpTime;

    @PostConstruct
    public void init() {
        this.redisNewApplicationReminderExpTime = applicationProperties.getRedisMsgExpDay() * 24 * 3600; // in seconds
    }

    private String dataServiceAgencyBaseUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/agency";
    }


    @Override
    public Long create(AgencyDTO agencyDTO) {

        check(agencyDTO);
        checkDuplication(agencyDTO.getName(), agencyDTO.getPhone(), agencyDTO.getEmail(), null);

        Agency agency = new Agency(agencyDTO, SecurityUtils.getUserId(), SecurityUtils.getTenantId());
        agency = agencyRepository.saveAndFlush(agency);
        Long agencyId = agency.getId();
        List<AgencySharingUser> sharingUsers = agencyDTO.getSharedUsers().stream().map(userId -> new AgencySharingUser(agencyId, userId)).collect(Collectors.toList());
        agencySharingUserRepository.saveAll(sharingUsers);

        AgencyActivityDTO agencyActivityDTO = new AgencyActivityDTO();
        agencyActivityDTO.setAgencyId(agency.getId());
        agencyActivityDTO.setActivityType(AgencyActivityType.AGENCY_STATUS);

        com.alibaba.fastjson.JSONObject details = new com.alibaba.fastjson.JSONObject();
        details.fluentPut("operation", "CREATE");
        agencyActivityDTO.setDetails(details);

        agencyActivityService.saveAll(Collections.singletonList(agencyActivityDTO));

        constructAndSendToMq(agency, "CREATE");

        return agencyId;

//        return getAgencyById(agencyId);
    }

    private void checkDuplication(String name, String phone, String email, Long excludeId) {
        phone = StringUtils.trimToNull(StringUtils.toRootLowerCase(phone));
        email = StringUtils.trimToNull(StringUtils.toRootLowerCase(email));
//        boolean foundDup = true;
        Optional<Agency> existedAgency = Optional.empty();
        if (StringUtils.isNotBlank(phone)) {
            existedAgency = agencyRepository.existsByPhoneOrEmailExcludingId(phone, email, excludeId);
        } else {
            existedAgency = agencyRepository.existsByEmailExcludingId(email, excludeId);
        }

        if (existedAgency.isPresent()) {
            Agency agency = existedAgency.get();
            if (agency.getTenantId().equals(SecurityUtils.getTenantId())) {
                Map<String, Object> params = new HashMap<>();
                params.put("agencyId", agency.getId());
                params.put("agencyName", agency.getName());
                throw new CustomParameterizedException("Duplicate phone or email address!", params);
            } else {
                throw new CustomParameterizedException("Duplicate phone or email address!");
            }
        }

        existedAgency = agencyRepository.existsByNameExcludingId(name, excludeId, SecurityUtils.getTenantId());
        if (existedAgency.isPresent()) {
            Agency agency = existedAgency.get();
            Map<String, Object> params = new HashMap<>();
            params.put("agencyId", agency.getId());
            params.put("agencyName", agency.getName());
            throw new CustomParameterizedException("Duplicate name!", params);
        }
    }

    private void check(AgencyDTO agencyDTO) {
        String email = StringUtils.trimToNull(StringUtils.toRootLowerCase(agencyDTO.getEmail()));
        if (StringUtils.isEmpty(email)) {
            throw new CustomParameterizedException("Email is empty!");
        }
        agencyDTO.setEmail(email);
        String phone = StringUtils.trimToEmpty(StringUtils.toRootLowerCase(agencyDTO.getPhone()));
        agencyDTO.setPhone(phone);
        String name = StringUtils.trimToNull(StringUtils.toRootLowerCase(agencyDTO.getName()));
        agencyDTO.setName(name);
    }

    @Override
    public AgencyVO getAgencyById(Long id) {

        Agency agency = agencyRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("Agency with id " + id + " not found"));

        if (!agency.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException("You are not permitted to view this agency");
        }

        List<AgencySharingUser> agencySharingUsers = agencySharingUserRepository.findAllByAgencyId(agency.getId());
        Set<Long> sharingUserIds = agencySharingUsers.stream().map(AgencySharingUser::getUserId).collect(Collectors.toSet());

        if (!SecurityUtils.isAdmin() && !SecurityUtils.getUserId().equals(agency.getAgencyCreatorUserId()) && !sharingUserIds.contains(SecurityUtils.getUserId()) && !SecurityUtils.getUserId().equals(agency.getAgencyOwnerUserId())) {
            throw new CustomParameterizedException("You are not allowed to view this agency, id: " + agency.getId());
        }

        List<UserBriefDTO> sharingUsersBrief = userService.findBriefUsers(sharingUserIds);
        List<AssociatedUserDTO> sharingUsers = sharingUsersBrief.stream().map(AssociatedUserDTO::new).collect(Collectors.toList());

        UserBriefDTO agencyOwnerUserBrief = userService.getUserById(agency.getAgencyOwnerUserId());
        AssociatedUserDTO agencyOwner = new AssociatedUserDTO(agencyOwnerUserBrief);

        UserBriefDTO agencyCreatorUserBrief = userService.getUserById(agency.getAgencyCreatorUserId());
        AssociatedUserDTO agencyCreator = new AssociatedUserDTO(agencyCreatorUserBrief);

        return new AgencyVO(agency, agencyOwner, agencyCreator, sharingUsers);
    }

    @Override
    public Integer getAgencyInProgressApplicationsByAgencyId(Long agencyId) {
        checkPermission(agencyId);
        List<Long> applicationIds = agencySubmitApplicationRepository.findAllApplicationIdsByAgencyId(agencyId);

        return applicationService.countUnfinishedApplicationsByIds(applicationIds);
    }

    @Override
    public Integer getAgencyInProgressApplicationsByAgencyIdAndJobId(Long agencyId, Long jobId) {
        checkPermission(agencyId);
        List<Long> applicationIds = agencySubmitApplicationRepository.findAllApplicationIdsByAgencyIdAndJobId(agencyId, jobId);

        return applicationService.countUnfinishedApplicationsByIds(applicationIds);
    }

    private Agency checkPermission(Long id) {
        Agency agency = agencyRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("Agency with id " + id + " not found"));

        List<AgencySharingUser> agencySharingUsers = agencySharingUserRepository.findAllByAgencyId(agency.getId());
        Set<Long> sharingUserIds = agencySharingUsers.stream().map(AgencySharingUser::getUserId).collect(Collectors.toSet());

        if (!(SecurityUtils.isAdmin() && agency.getTenantId().equals(SecurityUtils.getTenantId())) && !sharingUserIds.contains(SecurityUtils.getUserId()) && !SecurityUtils.getUserId().equals(agency.getAgencyOwnerUserId()) && !SecurityUtils.getUserId().equals(agency.getAgencyCreatorUserId())) {
            throw new CustomParameterizedException("You are not allowed to view this agency, id: " + agency.getId());
        }

        return agency;
    }

    @Override
    public Page<AgencyVO> getAgencies(String search, Pageable pageable) {
        Set<Long> agencyIds = agencySharingUserRepository.findAllAgencyIdsByUserId(SecurityUtils.getUserId());
        List<Long> ownedAgencyIds = agencyRepository.getAgencyIdByAgencyOwnerUserId(SecurityUtils.getUserId());
        Set<Long> creatorAgencyIds = agencyRepository.getAgencyIdByAgencyCreatorUserId(SecurityUtils.getUserId());

        agencyIds.addAll(ownedAgencyIds);
        agencyIds.addAll(creatorAgencyIds);

        Page<Agency> agencyList = null;

        if (SecurityUtils.isAdmin()) {
            if (StringUtils.isEmpty(search)) {
                agencyList = agencyRepository.findByTenantId(SecurityUtils.getTenantId(), pageable);
            } else {
                agencyList = agencyRepository.findAllByTenantIdAndNameLike(SecurityUtils.getTenantId(), search, pageable);
            }
        } else if (StringUtils.isBlank(search)) {
            agencyList = agencyRepository.findByIdIn(agencyIds, pageable);
        } else {
            agencyList = agencyRepository.findAllByIdInAndNameLike(agencyIds, search, pageable);
        }

        List<Long> ids = agencyList.getContent().stream().map(Agency::getId).collect(Collectors.toList());

        List<AgencyVO> res = new ArrayList<>();
        for (Long agencyId : ids) {
            AgencyVO agencyVO = getAgencyById(agencyId);
            res.add(agencyVO);
        }
        return new PageImpl<>(res, pageable, agencyList.getTotalElements());
    }

    @Override
    public Page<AgencyVO> getAgenciesFromEs(String search, Pageable pageable) {
        HttpResponse response = searchAgencyFromDataService(search, pageable);

        Page<AgencyVO> res = parseResponse(response, pageable);
        return res;
    }

    private Page<AgencyVO> parseResponse(HttpResponse response, Pageable pageable) {
        if (!ObjectUtil.isEmpty(response) && !ObjectUtil.isEmpty(response.getBody())) {
            int count = ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders());
            List<AgencyVO> res = parseBody(response.getBody());
            return new PageImpl<>(res, pageable, count);
        } else {
            List<AgencyVO> res = new ArrayList<>();
            return new PageImpl<>(res, pageable, 0L);
        }
    }

    private List<AgencyVO> parseBody(String body) {
        List<AgencyVO> res = new ArrayList<>();
        cn.hutool.json.JSONArray resultArray = JSONUtil.parseArray(body);
        if (!resultArray.isEmpty()) {
            Long userId = SecurityUtils.getUserId();
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject searchData = JSONUtil.parseObj(resultArray.get(i));
                String id = searchData.getStr("_id");
                JSONObject source = searchData.getJSONObject("_source");

                AgencyVO agencyVO = new AgencyVO();
                agencyVO.setId(Long.valueOf(id));
                agencyVO.setName(source.getStr("agencyName"));
                boolean active = source.getBool("active");
                agencyVO.setStatus(active ? ActiveStatus.ACTIVE : ActiveStatus.INACTIVE);
                agencyVO.setDescription(source.getStr("description"));
                cn.hutool.json.JSONArray phones = source.getJSONArray("phonesDisplay");
                if (Objects.nonNull(phones) && !phones.isEmpty()) {
                    agencyVO.setPhone(String.valueOf(phones.get(0)));
                }

                cn.hutool.json.JSONArray emails = source.getJSONArray("emails");
                if (Objects.nonNull(emails) && !emails.isEmpty()) {
                    agencyVO.setEmail(String.valueOf(emails.get(0)));
                }

                JSONObject agencyContact = source.getJSONObject("agencyContactName");
                if (Objects.nonNull(agencyContact) && !agencyContact.isEmpty()) {
                    agencyVO.setAgencyContactFirstName(agencyContact.getStr("firstName"));
                    agencyVO.setAgencyContactLastName(agencyContact.getStr("lastName"));
                    agencyVO.setAgencyContactFullName(agencyContact.getStr("fullName"));
                }


                cn.hutool.json.JSONArray responsibility0 = source.getJSONArray("responsibility0");
                agencyVO.setAgencyCreator(parseAssociatedUserDTO(responsibility0.getJSONObject(0)));

                cn.hutool.json.JSONArray responsibility1 = source.getJSONArray("responsibility1");
                agencyVO.setAgencyOwner(parseAssociatedUserDTO(responsibility1.getJSONObject(0)));

                cn.hutool.json.JSONArray responsibility2 = source.getJSONArray("responsibility2");
                if (Objects.nonNull(responsibility2) && !responsibility2.isEmpty()) {
                    List<AssociatedUserDTO> sharedUsers = new ArrayList<>();
                    for (int j = 0; j < responsibility2.size(); j++) {
                        JSONObject obj = responsibility2.getJSONObject(j);
                        AssociatedUserDTO user = parseAssociatedUserDTO(obj);
                        sharedUsers.add(user);
                    }
                    agencyVO.setSharedUsers(sharedUsers);
                }
                String createdDateStr = source.getStr("createdDate");
                agencyVO.setCreatedDate(Instant.parse(createdDateStr));

                String lastModifiedDateStr = source.getStr("lastModifiedDate");
                agencyVO.setLastModifiedDate(Instant.parse(lastModifiedDateStr));

                boolean hasNewCandidate = hasNewCandidate(agencyVO.getId(), userId);
                agencyVO.setHasNewCandidate(hasNewCandidate);

                res.add(agencyVO);
            }
        }
        return res;
    }

    private boolean hasNewCandidate(Long agencyId, Long userId) {
        String pattern = String.format(AGENCY_NEW_APPLICATION_KEY_PATTERN_PREFIX_USER_ID_AGENCY_ID, userId, agencyId);
        return redisService.scanForKeyPattern(pattern);
    }

    private AssociatedUserDTO parseAssociatedUserDTO(JSONObject associatedUserDTO) {
        AssociatedUserDTO res = new AssociatedUserDTO();
        res.setId(Long.valueOf(associatedUserDTO.getStr("id")));
        res.setFullName(associatedUserDTO.getStr("name"));
        return res;
    }

    private HttpResponse searchAgencyFromDataService(String search, Pageable pageable) {
        SearchGroup searchGroup = buildEsSearchObj(search);

        String condition = JSONUtil.toJsonStr(JSONUtil.parse(searchGroup));
        log.info("[AgencyServiceImpl: searchAgencyFromDataService] AGENCY_SEARCH_CONDITION= " + condition);
        if (ObjectUtil.isNull(condition)) {
            return null;
        }

        String url = dataServiceAgencyBaseUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = dataServiceAgencyBaseUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                for (Sort.Order sort : pageable.getSort()) {
                    url += "&sort=" + parseSortParam(sort);
                }
            }
        }

//        System.out.println(url);
//        System.out.println(condition);

        try {
            Instant start = Instant.now();

            HttpResponse response = httpService.post(url, condition);

            Instant end = Instant.now();
            log.info("[apn module={}, index={}, timeZone={}] EsFillerJobService.searchFromCommonService time = {}ms", searchGroup.getModule(), searchGroup.getIndex(), searchGroup.getTimeZone(), Duration.between(start, end).toMillis());

            if (response != null) {
                if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.info("[Agency: DataServiceImpl @{}] search agency from data service success, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                    //Special handling when an error code is 404/422 "Empty query", return 200
                } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                    boolean hasAgency = agencyRepository.existsByTenantId(SecurityUtils.getTenantId());
                    if (!hasAgency) {
                        return new HttpResponse();
                    } else {
                        throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                    }
                } else {
                    log.error("[Agency: DataServiceImpl @{}] search agency from data service error, searchRequest: {}, pageable:{}, response code: {}, response message: {}", SecurityUtils.getUserId(), condition, pageable, response.getCode(), response.getBody());
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[Agency: DataServiceImpl @{}] search agency from data service error and response is null, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                throw new ExternalServiceInterfaceException();
            }
            return response;
        } catch (Exception e) {
            log.error("[Agency: DataServiceImpl @{}] search agency from data service error, IOException. searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
    }

    private String parseSortParam(Sort.Order sort) {
        if (StringUtils.endsWithIgnoreCase("active", sort.getProperty())) {
            return sort.getProperty() + StrUtil.COMMA + (sort.getDirection().isAscending() ? Sort.Direction.DESC : Sort.Direction.ASC);
        } else {
            return sort.getProperty() + StrUtil.COMMA + sort.getDirection();
        }
    }

    private SearchGroup buildEsSearchObj(String search) {
        SearchGroup searchGroup = new SearchGroup();
        searchGroup.setOwnershipRestriction(null);
        searchGroup.setTimeZone("Asia/Shanghai");
        searchGroup.setLanguage("zh");
        searchGroup.setModule("AGENCY");
        searchGroup.setIndex("agencies_" + SecurityUtils.getTenantId());

        //set filter, userId must in one of creator(responsibility0) or owner(responsibility1) or sharedUsers(responsibility2)
        SearchFilterDTO filter = new SearchFilterDTO();
        filter.setSource(ES_AGENCY_SOURCES);
        if (!SecurityUtils.isAdmin()) { //admin has access to all agencies
            setUserIdFilter(SecurityUtils.getUserId(), filter);
        }
        searchGroup.setFilter(filter);

        //set general search query
//        if (StringUtils.isNotBlank(search)) {
            setSearchParam(search, searchGroup);
//        }

        return searchGroup;
    }

    private void setUserIdFilter(Long userId, SearchFilterDTO filter) {
        SearchParam userIdFilter = new SearchParam();
        userIdFilter.setRelation(Relation.OR);

        List<ConditionParam> conditions = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            ConditionParam condition = new ConditionParam();
            condition.setKey("responsibility" + i + ".id");
            JSONObject valueObj = new JSONObject();
            valueObj.put("data", userId);
            condition.setValue(valueObj);

            conditions.add(condition);
        }

        userIdFilter.setCondition(conditions);
        List<SearchParam> queryFilters = new ArrayList<>();
        queryFilters.add(userIdFilter);
        filter.setQueryFilter(queryFilters);
    }

    private void setSearchParam(String search, SearchGroup searchGroup) {
        if (Objects.isNull(search)) { //to avoid empty search error in data-service
            search = "";
        }

        ConditionParam condition = new ConditionParam();
        condition.setKey("generalText");
        JSONObject valueObj = new JSONObject();
        valueObj.put("data", search);
        condition.setValue(valueObj);

        List<ConditionParam> conditions = new ArrayList<>();
        conditions.add(condition);

        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.OR);
        searchParam.setCondition(conditions);

        List<SearchParam> searchParams = new ArrayList<>();
        searchParams.add(searchParam);

        searchGroup.setSearch(searchParams);
    }

    @Override
    public List<AgencyBriefVO> getAllAgencyBriefs() {
        if (SecurityUtils.isAdmin()) {
            List<AgencyBriefVO> res =  agencyRepository.getAgencyBriefListByTenantId(SecurityUtils.getTenantId());
            return res.stream().peek(a -> {
                a.setHasEditPermission(true);
                a.setHasViewPermission(true);
            }).collect(Collectors.toList());
        } else {
            List<AgencyBriefVO> res = agencyRepository.getAgencyBriefListByTenantId(SecurityUtils.getTenantId());
            Set<Long> sharedAgencyIds = agencySharingUserRepository.findAllAgencyIdsByUserId(SecurityUtils.getUserId());

            Long currentUserId = SecurityUtils.getUserId();
            return res.stream().peek(a -> {
                if (a.getAgencyCreatorUserId().equals(currentUserId)) {
                    a.setHasEditPermission(true);
                    a.setHasViewPermission(true);
                } else if (sharedAgencyIds.contains(a.getId()) || a.getAgencyOwnerUserId().equals(currentUserId)) {
                    a.setHasViewPermission(true);
                    a.setHasEditPermission(true);
                } else {
                    a.setHasEditPermission(false);
                    a.setHasViewPermission(false);
                }
            }).collect(Collectors.toList());
        }
    }

    @Override
    public void update(Long id, AgencyDTO agencyDTO) {
        Agency agency = agencyRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("Agency with id " + id + " not found"));

        Set<Long> sharingUserIdSet = agencySharingUserRepository.findAllUserIdsByAgencyId(id);
        if (!(SecurityUtils.isAdmin() && Objects.equals(SecurityUtils.getTenantId(), agency.getTenantId())) && !SecurityUtils.getUserId().equals(agency.getAgencyCreatorUserId()) && !SecurityUtils.getUserId().equals(agency.getAgencyOwnerUserId()) && !sharingUserIdSet.contains(SecurityUtils.getUserId())) {
            throw new CustomParameterizedException("You are not allowed to update this agency, id: " + id);
        }

        check(agencyDTO);
        checkDuplication(agency.getName(), agencyDTO.getPhone(), agencyDTO.getEmail(), id);

        if (!Objects.equals(agency.getStatus(), agencyDTO.getStatus())) {

            AgencyActivityDTO agencyActivityDTO = new AgencyActivityDTO();
            agencyActivityDTO.setAgencyId(id);
            agencyActivityDTO.setActivityType(AgencyActivityType.AGENCY_STATUS);

            com.alibaba.fastjson.JSONObject details = new com.alibaba.fastjson.JSONObject();
            details.fluentPut("operation", ActiveStatus.ACTIVE.equals(agencyDTO.getStatus()) ? "ACTIVATE" : "DEACTIVATE");
            agencyActivityDTO.setDetails(details);

            agencyActivityService.saveAll(Collections.singletonList(agencyActivityDTO));

            if (ActiveStatus.INACTIVE.equals(agencyDTO.getStatus())) {
                stopAllSharedJobByAgencyId(agency.getId());
            }
        }

        ServiceUtils.myCopyProperties(agencyDTO, agency);
        agency.setAgencyOwnerUserId(agencyDTO.getAgencyOwner());
        String location = Objects.isNull(agencyDTO.getLocation()) ? "{}" : agencyDTO.getLocation().toJSONString();
        agency.setLocation(location);
        agency = agencyRepository.saveAndFlush(agency);

        List<AgencySharingUser> agencySharingUsers = agencySharingUserRepository.findAllByAgencyId(agency.getId());
        Set<Long> sharingUserIds = agencySharingUsers.stream().map(AgencySharingUser::getUserId).collect(Collectors.toSet());
        if (!CollectionUtils.isEqualCollection(agencyDTO.getSharedUsers(), sharingUserIds)) {
            agencySharingUserRepository.deleteAllByAgencyId(agency.getId());
            List<AgencySharingUser> sharingUsers = agencyDTO.getSharedUsers().stream().map(userId -> new AgencySharingUser(id, userId)).collect(Collectors.toList());
            agencySharingUserRepository.saveAll(sharingUsers);
        }

        constructAndSendToMq(agency, "UPDATE");

//        return getAgencyById(id);
    }

    @Override
    public Page<SharedJobVO> getSharedJobs(Long agencyId, String jobTitle, String companyName, Pageable pageable) {
        Agency agency = agencyRepository.findById(agencyId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " not found"));
        //todo check authority

        HttpResponse response = searchAgencyJobsFromDataService(agencyId, jobTitle, companyName, pageable);

        Page<SharedJobVO> res = parseAgencyJobResponse(response, pageable);

        List<String> keysToDelete = new ArrayList<>();

//        Page<Map<String, Object>> res = agencySharedJobRepository.findAllJobsWithDetailsByAgencyId(agencyId, jobTitle, companyName, pageable);
        List<SharedJobVO> result = res.getContent().stream().map(sharedJobVO-> {
//            Integer submittedTalentsCount = agencySubmitApplicationRepository.countAgencySubmitApplicationByAgencyIdAndJobId(agencyId, sharedJobVO.getJobId());
            List<Long> submittedTalentIds = agencySubmitApplicationRepository.findAllTalentIdsByJobIdAndAgencyId(sharedJobVO.getJobId(), agencyId);
            Integer duplicateTalentsCount = agencySubmitDuplicateTalentRepository.countAgencySubmitDuplicateTalentByAgencyIdAndJobId(agencyId, sharedJobVO.getJobId());

            sharedJobVO.setSubmittedTalentIds(submittedTalentIds);
            sharedJobVO.setSubmittedTalentsCount(CollectionUtils.size(submittedTalentIds));
            sharedJobVO.setDuplicateTalentsCount(duplicateTalentsCount);

            //{userId}:{agencyId}:{jobId} -> 最新{applicationId}, expire at 30 days
            String redisKey = String.format(AGENCY_NEW_APPLICATION_KEY_PATTERN, SecurityUtils.getUserId(), agencyId, sharedJobVO.getJobId());
            boolean hasNewCandidate = redisService.exists(redisKey);
            sharedJobVO.setHasNewCandidate(hasNewCandidate);
            if (hasNewCandidate) {
                keysToDelete.add(redisKey);
            }

            return sharedJobVO;
        }).collect(Collectors.toList());

        redisService.delete(keysToDelete.toArray(new String[0]));

//        return res;
        return new PageImpl<>(result, pageable, res.getTotalElements());
    }

    private HttpResponse searchAgencyJobsFromDataService(Long agencyId, String jobTitle, String companyName, Pageable pageable) {

        SearchGroup searchGroup = buildAgencyJobEsSearchObj(agencyId, jobTitle, companyName);

        String condition = JSONUtil.toJsonStr(JSONUtil.parse(searchGroup));
        log.info("[AgencyServiceImpl: searchAgencyJobsFromDataService] AGENCY_JOB_SEARCH_CONDITION= " + condition);
        if (ObjectUtil.isNull(condition)) {
            return null;
        }

        String url = dataServiceAgencyBaseUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = dataServiceAgencyBaseUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                for (Sort.Order sort : pageable.getSort()) {
                    url += "&sort=" + sort.getProperty() + StrUtil.COMMA + sort.getDirection();
                }
            }
        }

//        System.out.println(url);
//        System.out.println(condition);

        try {
            Instant start = Instant.now();

            HttpResponse response = httpService.post(url, condition);

            Instant end = Instant.now();
            log.info("[apn module={}, index={}, timeZone={}] EsFillerJobService.searchFromCommonService time = {}ms", searchGroup.getModule(), searchGroup.getIndex(), searchGroup.getTimeZone(), Duration.between(start, end).toMillis());

            if (response != null) {
                if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.info("[Agency: DataServiceImpl @{}] search agency job from data service success, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                    //Special handling when an error code is 404/422 "Empty query", return 200
                } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                    boolean hasAgency = agencyRepository.existsByTenantId(SecurityUtils.getTenantId());
                    if (!hasAgency) {
                        return new HttpResponse();
                    } else {
                        throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                    }
                } else {
                    log.error("[Agency: DataServiceImpl @{}] search agency job from data service error, searchRequest: {}, pageable:{}, response code: {}, response message: {}", SecurityUtils.getUserId(), condition, pageable, response.getCode(), response.getBody());
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[Agency: DataServiceImpl @{}] search agency job from data service error and response is null, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                throw new ExternalServiceInterfaceException();
            }
            return response;
        } catch (Exception e) {
            log.error("[Agency: DataServiceImpl @{}] search agency job from data service error, IOException. searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
    }

    private SearchGroup buildAgencyJobEsSearchObj(Long agencyId, String jobTitle, String companyName) {
        SearchGroup searchGroup = new SearchGroup();
        searchGroup.setOwnershipRestriction(null);
        searchGroup.setTimeZone("Asia/Shanghai");
        searchGroup.setLanguage("zh");
        searchGroup.setModule("AGENCY_JOB");
        searchGroup.setIndex("agency_jobs_" + SecurityUtils.getTenantId());

        //set filter, userId must in one of creator(responsibility0) or owner(responsibility1) or sharedUsers(responsibility2)
        SearchFilterDTO filter = new SearchFilterDTO();
        filter.setSource(ES_AGENCY_JOB_SOURCES);

        setAgencyIdFilter(agencyId, filter);

        searchGroup.setFilter(filter);

        setSearchParamForAgencyJob(companyName, jobTitle, searchGroup);

        return searchGroup;
    }

    private void setAgencyIdFilter(Long agencyId, SearchFilterDTO filter) {
        SearchParam userIdFilter = new SearchParam();
        userIdFilter.setRelation(Relation.OR);

        List<ConditionParam> conditions = new ArrayList<>();

        ConditionParam condition = new ConditionParam();
        condition.setKey("sharedAgencies");
        JSONObject valueObj = new JSONObject();
        valueObj.put("data", agencyId);
        condition.setValue(valueObj);

        conditions.add(condition);


        userIdFilter.setCondition(conditions);
        List<SearchParam> queryFilters = new ArrayList<>();
        queryFilters.add(userIdFilter);
        filter.setQueryFilter(queryFilters);
    }

    private void setSearchParamForAgencyJob(String companyName, String jobTitle, SearchGroup searchGroup) {

        List<ConditionParam> conditions = new ArrayList<>();
        if (StringUtils.isNotBlank(companyName)) {
            ConditionParam condition = new ConditionParam();
            condition.setKey("companyName");
            JSONObject valueObj = new JSONObject();
            valueObj.put("data", companyName);
            condition.setValue(valueObj);
            conditions.add(condition);
        }
        if (StringUtils.isNotBlank(jobTitle)) {
            ConditionParam condition = new ConditionParam();
            condition.setKey("jobTitle");
            JSONObject valueObj = new JSONObject();
            valueObj.put("data", List.of(jobTitle));
            condition.setValue(valueObj);
            conditions.add(condition);
        }

        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.AND);
        searchParam.setCondition(conditions);

        List<SearchParam> searchParams = new ArrayList<>();
        searchParams.add(searchParam);

        searchGroup.setSearch(searchParams);
    }

    private Page<SharedJobVO> parseAgencyJobResponse(HttpResponse response, Pageable pageable) {
        if (!ObjectUtil.isEmpty(response) && !ObjectUtil.isEmpty(response.getBody())) {
            int count = ElasticSearchUtil.getObjectCountFromResponseHeader(response.getHeaders());
            List<SharedJobVO> res = parseAgencyJobBody(response.getBody());
            return new PageImpl<>(res, pageable, count);
        } else {
            List<SharedJobVO> res = new ArrayList<>();
            return new PageImpl<>(res, pageable, 0L);
        }
    }

    private List<SharedJobVO> parseAgencyJobBody(String body) {
        List<SharedJobVO> res = new ArrayList<>();
        cn.hutool.json.JSONArray resultArray = JSONUtil.parseArray(body);
        if (!resultArray.isEmpty()) {
            Set<String> privateJobTeamIds = getPrivateJobTeamIdsByTenantId();
            for (int i = 0; i < resultArray.size(); i++) {
                JSONObject searchData = JSONUtil.parseObj(resultArray.get(i));
                String id = searchData.getStr("_id");
                JSONObject source = searchData.getJSONObject("_source");

                SharedJobVO sharedJobVO = new SharedJobVO();

                sharedJobVO.setJobId(Long.valueOf(id));

                sharedJobVO.setJobTitle(source.getStr("title"));
                sharedJobVO.setJobStatus(source.getStr("status"));
                sharedJobVO.setCompanyName(source.getStr("companyName"));
                sharedJobVO.setCompanyId(source.getLong("companyId"));

                boolean isPrivateJob = checkIfPrivateJob(source, privateJobTeamIds);
                sharedJobVO.setIsVisible(!isPrivateJob);

                String createdDateStr = source.getStr("createdDate");
                sharedJobVO.setJobCreatedDate(Instant.parse(createdDateStr));

                res.add(sharedJobVO);
            }
        }
        return res;
    }

    private Set<String> getPrivateJobTeamIdsByTenantId() {
        Set<String> privateJobTeamIds = jobSharingToAgencyInfoRepository.findPrivateJobTeamIds(SecurityUtils.getTenantId()).stream().map(t -> "pteam_" + t).collect(Collectors.toSet());
        return privateJobTeamIds;
    }

    private boolean checkIfPrivateJob(JSONObject jobSource, Set<String> privateJobTeamIds) {
        boolean isPrivateJob = false;
        if (jobSource.containsKey(ElasticSearchConstants.JOB_SEARCH_SOURCE_AFFILIATIONS)){
            Set<String> affiliations = jobSource.get(ElasticSearchConstants.JOB_SEARCH_SOURCE_AFFILIATIONS, Set.class);
            isPrivateJob = CollectionUtils.isNotEmpty(SetUtils.intersection(affiliations, privateJobTeamIds));
        }
        return isPrivateJob;
    }

    @Override
    public List<DuplicateTalentVO> getDuplicateTalents(Long agencyId, Long jobId) {
        Agency agency = agencyRepository.findById(agencyId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " not found"));
        //todo check authority

        AgencySharedJob agencySharedJob = agencySharedJobRepository.findByAgencyIdAndJobId(agencyId, jobId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " and jobId: " + jobId + " not found"));
        if (JobShareStatus.INACTIVE.equals(agencySharedJob.getShareStatus())) {
            throw new CustomParameterizedException("This job sharing has already be terminated!");
        }
        List<AgencySubmitDuplicateTalent> agencySubmitDuplicateTalents = agencySubmitDuplicateTalentRepository.findAllByAgencyIdAndJobId(agencyId, jobId);

        List<DuplicateTalentVO> res = new ArrayList<>();
        for (AgencySubmitDuplicateTalent duplicateTalent : agencySubmitDuplicateTalents) {
            JSONObject submittedTalent = JSONUtil.parseObj(duplicateTalent.getOriginalTalentJson());
            String firstName = submittedTalent.getStr("firstName");
            String lastName = submittedTalent.getStr("lastName");
            String fullName = submittedTalent.getStr("fullName");
            if (StringUtils.isBlank(fullName)) {
                fullName = CommonUtils.formatFullName(firstName, lastName);
            }
            List<TalentBriefDTO> talentBriefDTOS = talentService.getTalentBriefByTalentIds(duplicateTalent.getDuplicateTalentIds());

            List<com.altomni.apn.agency.dto.TalentBriefDTO> duplicateTalents = talentBriefDTOS.stream().map(t -> new com.altomni.apn.agency.dto.TalentBriefDTO(t.getTalentId(), t.getFullName())).collect(Collectors.toList());

            DuplicateTalentVO duplicateTalentVO = new DuplicateTalentVO();
            duplicateTalentVO.setAgencySubmitDuplicateTalentId(duplicateTalent.getId());
            duplicateTalentVO.setDuplicateId(duplicateTalent.getId());
            duplicateTalentVO.setFirstName(firstName);
            duplicateTalentVO.setLastName(lastName);
            duplicateTalentVO.setFullName(fullName);
            duplicateTalentVO.setDuplicateTalents(duplicateTalents);
            duplicateTalentVO.setMergeToTalentId(duplicateTalent.getMergedTalentId());

            res.add(duplicateTalentVO);
        }

        return res;
    }

    @Override
    public DuplicateTalentBriefDTO getDuplicateTalent(Long agencyId, Long jobId, Long duplicateTalentId) {
        Agency agency = agencyRepository.findById(agencyId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " not found"));
        //todo check authority

        AgencySharedJob agencySharedJob = agencySharedJobRepository.findByAgencyIdAndJobId(agencyId, jobId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " and jobId: " + jobId + " not found"));
        if (JobShareStatus.INACTIVE.equals(agencySharedJob.getShareStatus())) {
            throw new CustomParameterizedException("This job sharing has already be terminated!");
        }

        AgencySubmitDuplicateTalent duplicateTalent = agencySubmitDuplicateTalentRepository.findById(duplicateTalentId).orElseThrow(() -> new CustomParameterizedException("Duplicate talent id " + duplicateTalentId + " not found"));
        if (!duplicateTalent.getJobId().equals(jobId) || !duplicateTalent.getAgencyId().equals(agencyId)) {
            throw new CustomParameterizedException("Duplicate talent id " + duplicateTalentId + " cannot be retrived");
        }


        List<TimeSheetUser> timeSheetUsers = timesheetUserCustomRepository.findByUidLikeAndUserType(duplicateTalent.getDuplicateTalentIds(), TimeSheetUserType.TALENT.toDbValue());
        Map<Long, String> talentIdAndEmailMap = timeSheetUsers.stream().collect(Collectors.toMap(u -> Long.parseLong(u.getUid().split("_")[0]), TimeSheetUser::getEmail));
        List<TalentDTOV3> existingTalents = new ArrayList<>();
        for (Long id: duplicateTalent.getDuplicateTalentIds()) {
            TalentDTOV3 existing = talentService.getTalentById(id);
            if (talentIdAndEmailMap.containsKey(id)) {
                processingTimesheetUserEmail(existing.getContacts(), talentIdAndEmailMap.get(id));
            }
            existingTalents.add(existing);
        }
//        List<TalentBriefDTO> existingTalents = talentService.getTalentBriefByTalentIds(duplicateTalent.getDuplicateTalentIds());

        DuplicateTalentBriefDTO res = new DuplicateTalentBriefDTO();
        res.setExistingTalents(existingTalents);
        res.setDuplicateTalent(duplicateTalent.getOriginalTalentJson());

        return res;
    }

    private void processingTimesheetUserEmail(List<TalentContactDTO> contacts, String timesheetLoginEmail) {
        for (TalentContactDTO contact : contacts) {
            if ((ContactType.EMAIL.equals(contact.getType()) || ContactType.PRIMARY_EMAIL.equals(contact.getType())) && StringUtils.equalsIgnoreCase(contact.getContact().trim(), timesheetLoginEmail)) {
                contact.setTimesheetUserEmail(true);
            }
        }
    }

    @Override
    public List<AgencyStatsVO> getAllAgenciesByJobId(Long jobId) {
//        List<AgencySharedJob> agencies = agencySharedJobRepository.findAllByJobIdAndShareStatus(jobId, JobShareStatus.ACTIVE);

        List<AgencySharedJob> agencies = agencySharedJobRepository.findAllByJobId(jobId);

        Map<Long, JobShareStatus> shareStatusMap = agencies.stream().collect(Collectors.toMap(AgencySharedJob::getAgencyId, AgencySharedJob::getShareStatus));
        Set<Long> agencyIds = shareStatusMap.keySet();
        List<Agency> agencyList = agencyRepository.findAllByStatusAndIdIn(ActiveStatus.ACTIVE, agencyIds);
        Map<Long, AgencyApplicationStatsDTO> latestAgencyStats = fetchAgencyStatsByJobId(jobId);


        Set<Long> agencyOwnerIds = agencyList.stream().map(Agency::getAgencyOwnerUserId).collect(Collectors.toSet());

        List<UserBriefDTO> agencyOwnerList = userService.findBriefUsers(agencyOwnerIds);
        Map<Long, AssociatedUserDTO> agencyOwnerMap = agencyOwnerList.stream().map(AssociatedUserDTO::new).collect(Collectors.toMap(AssociatedUserDTO::getId, Function.identity()));

        List<AgencyStatsVO> res = new ArrayList<>();
        for (Agency agency : agencyList) {
            AgencyStatsVO agencyStatsVO = new AgencyStatsVO(agency);
            agencyStatsVO.setJobShareStatus(shareStatusMap.get(agency.getId()));
            agencyStatsVO.setAgencyOwner(agencyOwnerMap.get(agency.getAgencyOwnerUserId()));
            AgencyApplicationStatsDTO statsDTO = latestAgencyStats.get(agency.getId());
            if (Objects.isNull(statsDTO)) {
                agencyStatsVO.setSubmittedTalents(0);
                agencyStatsVO.setOngoingApplicationCount(0);
                agencyStatsVO.setSubmittedTalentIds(new ArrayList<>());
                agencyStatsVO.setLastSubmittedTime(null);
            } else {
                agencyStatsVO.setSubmittedTalents(CollectionUtils.size(statsDTO.getTalentIds()));
                agencyStatsVO.setSubmittedTalentIds(statsDTO.getTalentIds());
                agencyStatsVO.setOngoingApplicationCount(statsDTO.getOngoingApplicationCount());
                agencyStatsVO.setLastSubmittedTime(statsDTO.getLatestSubmissionDate());
            }

            res.add(agencyStatsVO);
        }

        res.sort(Comparator.comparing(AgencyStatsVO::getLastSubmittedTime,
                Comparator.nullsLast(Comparator.reverseOrder())));

        return res;
    }

    private Map<Long, AgencyApplicationStatsDTO> fetchAgencyStatsByJobId(Long jobId) {
        // 调用 repository 查询
        List<Object[]> results = agencySubmitApplicationRepository.findAgencyStatsByJobId(jobId);

        Map<Long, AgencyApplicationStatsDTO> agencyStatsMap = new HashMap<>();
        for (Object[] result : results) {
            Long agencyId = Long.valueOf(StringUtil.valueOf(result[0]));
//            Long processCount = result[1] != null ? Long.valueOf(StringUtil.valueOf(result[1])) : 0L;
            String applicationIds = StringUtil.valueOf(result[1]);
            List<Long> idList = new ArrayList<>();
            if (StringUtils.isNotEmpty(applicationIds)) {
                idList = Arrays.stream(applicationIds.split(","))
                        .map(Long::parseLong)
                        .collect(Collectors.toList());
            }
            Integer ongoingApplicationCount = result[2] != null ? Integer.valueOf(StringUtil.valueOf(result[2])) : 0;
            Instant latestCreatedDate = DateUtil.fromStringToInstant(StringUtil.valueOf(result[3]));

            AgencyApplicationStatsDTO statsDTO = new AgencyApplicationStatsDTO(agencyId, idList, ongoingApplicationCount, latestCreatedDate);

//            agencyStatsMap.put(agencyId, new Object[] { processCount, latestCreatedDate });
            agencyStatsMap.put(agencyId, statsDTO);
        }

        return agencyStatsMap;
    }

    @Override
    public SharedToAgencyJobInfoVM getJobSharingToAgencyInfoByJobId(Long jobId) {
        //find job sharing info first
        JobSharingToAgencyInfo jobSharingToAgencyInfo = jobSharingToAgencyInfoRepository.findByJobId(jobId).orElseThrow(() -> new CustomParameterizedException("This job doesn't share any info to agency yet"));
        SharedToAgencyJobInfoVM jobInfoVM = new SharedToAgencyJobInfoVM();
        jobInfoVM.setSharedInfo(jobSharingToAgencyInfo.getSharingToAgencyInfo());
        jobInfoVM.setCompanyNameVisible(jobSharingToAgencyInfo.getCompanyNameVisible());
        if (BooleanUtils.isTrue(jobSharingToAgencyInfo.getCompanyNameVisible())) {
            String companyName = companyService.getCompanyNameByJobId(jobId);
            jobInfoVM.setCompanyName(companyName);
        }
        return jobInfoVM;
    }

    @Override
    public String getJobSharingToAgencyInfoByJobId(Long jobId, Long agencyId) {
        AgencySharedJob agencySharedJob = agencySharedJobRepository.findByAgencyIdAndJobId(agencyId, jobId).orElseThrow(() -> new CustomParameterizedException("Shared job id " + jobId + " not found"));
        if (JobShareStatus.INACTIVE.equals(agencySharedJob.getShareStatus())) {
            throw new CustomParameterizedException("This job sharing has already be terminated!");
        }
        JobSharingToAgencyInfo jobSharingToAgencyInfo = jobSharingToAgencyInfoRepository.findByJobId(jobId).orElseThrow(() -> new CustomParameterizedException("This job doesn't share any info to agency yet"));
        return jobSharingToAgencyInfo.getSharingToAgencyInfo();
    }

    @Override
    public SharedToAgencyJobInfoDTO createJobSharingToAgencyInfo(Long jobId, SharedToAgencyJobInfoDTO sharedToAgencyJobInfoDTO) {
        JobDTOV3 job = jobService.getJob(jobId);
        if (Objects.isNull(job)) {
            throw new CustomParameterizedException("This job doesn't exist!");
        }
        if (!Objects.equals(job.getTenantId(), SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException("You aren't allowed to view this job!");
        }

        //find job sharing info first
        Optional<JobSharingToAgencyInfo> jobSharingToAgencyInfo = jobSharingToAgencyInfoRepository.findByJobId(jobId);
        if (jobSharingToAgencyInfo.isPresent()) {
            JobSharingToAgencyInfo exist = jobSharingToAgencyInfo.get();
            exist.setSharingToAgencyInfo(JSONUtil.toJsonStr(sharedToAgencyJobInfoDTO.getSharedInfo()));
            exist.setCompanyNameVisible(BooleanUtils.toBooleanDefaultIfNull(sharedToAgencyJobInfoDTO.getCompanyNameVisible(), false));
            jobSharingToAgencyInfoRepository.save(exist);
            return sharedToAgencyJobInfoDTO;
        } else {
            JobSharingToAgencyInfo save = new JobSharingToAgencyInfo(jobId, JSONUtil.toJsonStr(sharedToAgencyJobInfoDTO.getSharedInfo()));
            save.setCompanyNameVisible(BooleanUtils.toBooleanDefaultIfNull(sharedToAgencyJobInfoDTO.getCompanyNameVisible(), false));
            jobSharingToAgencyInfoRepository.save(save);
            return sharedToAgencyJobInfoDTO;
        }
    }

    public List<AgencyStatsVO> shareJobToAgencies(Long jobId, List<Long> agencyIds) {
        JobDTOV3 job = jobService.getJob(jobId);

        List<AgencySharedJob> sharedAgency = agencySharedJobRepository.findAllByJobIdAndAgencyIdIn(jobId, agencyIds);
        Set<Long> alreadySharedAgencySet  = new HashSet<>();
        for (AgencySharedJob agencySharedJob : sharedAgency) {
            alreadySharedAgencySet.add(agencySharedJob.getAgencyId());
            if (JobShareStatus.INACTIVE.equals(agencySharedJob.getShareStatus())) {
                agencySharedJob.setShareStatus(JobShareStatus.ACTIVE);
            }
        }

        for (Long agencyId : agencyIds) {
            if (!alreadySharedAgencySet.contains(agencyId)) {
                AgencySharedJob agencySharedJob = new AgencySharedJob();
                agencySharedJob.setAgencyId(agencyId);
                agencySharedJob.setJobId(jobId);
                agencySharedJob.setShareStatus(JobShareStatus.ACTIVE);

                sharedAgency.add(agencySharedJob);
            }
        }

        sharedAgency = agencySharedJobRepository.saveAllAndFlush(sharedAgency);

        Map<Long, String> agencyMap = agencyRepository.findAllByIdIn(sharedAgency.stream().map(AgencySharedJob::getAgencyId).collect(Collectors.toSet())).stream().collect(Collectors.toMap(Agency::getId, Agency::getName));

        List<AgencyActivityDTO> activityDTOs = sharedAgency.stream().map(shared -> {
            AgencyActivityDTO agencyActivityDTO = new AgencyActivityDTO();
            agencyActivityDTO.setAgencyId(shared.getAgencyId());
            agencyActivityDTO.setActivityType(AgencyActivityType.JOB_SHARE);

            com.alibaba.fastjson.JSONObject details = new com.alibaba.fastjson.JSONObject();
            details.fluentPut("jobId", jobId);
            details.fluentPut("isPrivateJob", job.getIsPrivateJob());
            details.fluentPut("jobTitle", job.getTitle());
            details.fluentPut("agencyId", shared.getAgencyId());
            details.fluentPut("agencyName", agencyMap.get(shared.getAgencyId()));
            details.fluentPut("operation", "START_SHARE");
            agencyActivityDTO.setDetails(details);
            return agencyActivityDTO;
        }).collect(Collectors.toList());
        agencyActivityService.saveAll(activityDTOs);

        return getAllAgenciesByJobId(jobId);
//        return null;
    }

    @Override
    public void stopShareJobToAgencies(Long jobId, List<Long> agencyIds) {
        List<AgencySharedJob> sharedAgency = agencySharedJobRepository.findAllByJobIdAndAgencyIdIn(jobId, agencyIds);

        stopShareJobToAgency(jobId, sharedAgency);
    }

    @Async
    @Override
    public void stopShareJobToAllAgencies(Long jobId) {
        List<AgencySharedJob> sharedAgency = agencySharedJobRepository.findAllByJobIdAndShareStatus(jobId, JobShareStatus.ACTIVE);

        stopShareJobToAgency(jobId, sharedAgency);
    }

    private void stopShareJobToAgency(Long jobId, List<AgencySharedJob> sharedAgency) {
        JobDTOV3 job = jobService.getJob(jobId); //TODO: private job access

        for (AgencySharedJob agencySharedJob : sharedAgency) {
            agencySharedJob.setShareStatus(JobShareStatus.INACTIVE);
        }
        sharedAgency = agencySharedJobRepository.saveAllAndFlush(sharedAgency);

        Map<Long, String> agencyMap = agencyRepository.findAllByIdIn(sharedAgency.stream().map(AgencySharedJob::getAgencyId).collect(Collectors.toSet())).stream().collect(Collectors.toMap(Agency::getId, Agency::getName));

        List<AgencySubmitApplication> agencySubmitApplications = agencySubmitApplicationRepository.findAllByJobId(jobId);
        Map<Long, Long> map = agencySubmitApplications.stream().collect(Collectors.toMap(AgencySubmitApplication::getTalentRecruitmentProcessId, AgencySubmitApplication::getAgencyId));
        Map<Long, List<ApplicationBriefInfoVM>> inProcessApplicationBriefVMMap = new HashMap<>();
        if (MapUtils.isNotEmpty(map)) {
//            List<InProcessApplicationBriefVM> inProcessApplicationBriefList = applicationService.getUnfinishedApplicationsByIds(map.keySet());
//            inProcessApplicationBriefVMMap = inProcessApplicationBriefList.stream().peek(a -> a.setAgencyId(map.get(a.getApplicationId())))
//                    .collect(Collectors.groupingBy(InProcessApplicationBriefVM::getAgencyId));

            List<ApplicationBriefInfoVM> applicationBriefList = applicationService.getApplicationsBriefByIds(map.keySet());
            applicationBriefList.forEach(a -> a.setAgencyId(map.get(a.getApplicationId())));

            Map<Long, ApplicationBriefInfoVM> applicationBriefInfoMap = applicationBriefList.stream().collect(Collectors.toMap(ApplicationBriefInfoVM::getApplicationId, Function.identity()));
            agencySubmitApplications.forEach(submittedApplication -> {
                ApplicationBriefInfoVM briefInfoVM = applicationBriefInfoMap.get(submittedApplication.getTalentRecruitmentProcessId());
                if (briefInfoVM != null) {
                    if (NodeStatus.ELIMINATED.equals(briefInfoVM.getLastNodeStatus())) {
                        submittedApplication.setLastStatus(NodeType.ELIMINATED);
                    } else {
                        submittedApplication.setLastStatus(briefInfoVM.getLastNodeType());
                    }
                }
            });

            agencySubmitApplicationRepository.saveAll(agencySubmitApplications);

            inProcessApplicationBriefVMMap = applicationBriefList.stream().filter(a -> NodeStatus.ACTIVE.equals(a.getLastNodeStatus()) && !NodeType.ON_BOARD.equals(a.getLastNodeType())).collect(Collectors.groupingBy(ApplicationBriefInfoVM::getAgencyId));
        }

        Map<Long, List<ApplicationBriefInfoVM>> finalInProcessApplicationBriefVMMap = inProcessApplicationBriefVMMap;

        List<AgencyActivityDTO> activityDTOs = sharedAgency.stream().map(shared -> {
            AgencyActivityDTO agencyActivityDTO = new AgencyActivityDTO();
            agencyActivityDTO.setAgencyId(shared.getAgencyId());
            agencyActivityDTO.setActivityType(AgencyActivityType.JOB_SHARE);

            com.alibaba.fastjson.JSONObject details = new com.alibaba.fastjson.JSONObject();
            details.fluentPut("jobId", jobId);
            details.fluentPut("jobTitle", job.getTitle());
            details.fluentPut("isPrivateJob", job.getIsPrivateJob());
            details.fluentPut("agencyId", shared.getAgencyId());
            details.fluentPut("agencyName", agencyMap.get(shared.getAgencyId()));
            details.fluentPut("operation", "STOP_SHARE");
            if (MapUtils.isNotEmpty(finalInProcessApplicationBriefVMMap) && finalInProcessApplicationBriefVMMap.containsKey(shared.getAgencyId())) {
                List<ApplicationBriefInfoVM> inProcessApplicationBriefVMList = finalInProcessApplicationBriefVMMap.get(shared.getAgencyId());
                if (CollectionUtils.isNotEmpty(inProcessApplicationBriefVMList)) {
                    JSONArray inProgressApplications = new JSONArray();
                    inProcessApplicationBriefVMList.stream().forEach(a -> {
                        com.alibaba.fastjson.JSONObject app = new com.alibaba.fastjson.JSONObject();
                        app.fluentPut("applicationId", a.getApplicationId());
                        app.fluentPut("talentId", a.getTalentId());
                        app.fluentPut("talentName", a.getTalentName());
                        inProgressApplications.add(app);
                    });

                    details.fluentPut("inProgressApplications", inProgressApplications);
                }
            }

            agencyActivityDTO.setDetails(details);
            return agencyActivityDTO;
        }).collect(Collectors.toList());
        agencyActivityService.saveAll(activityDTOs);
    }

    private void stopAllSharedJobByAgencyId(Long agencyId) {

        List<AgencySharedJob> sharedAgency = agencySharedJobRepository.findAllByAgencyIdAndShareStatus(agencyId, JobShareStatus.ACTIVE);

        for (AgencySharedJob shared : sharedAgency) {
            stopShareJobToAgency(shared.getJobId(), List.of(shared));
        }
    }

    @Override
    @Transactional
    public TalentRecruitmentProcessVO submitToJob(TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
        Long jobId = submitToJobVO.getJobId();

        Long agencyId = SecurityUtils.getTenantId(); //TODO
        AgencySharedJob agencySharedJob = agencySharedJobRepository.findByAgencyIdAndJobId(agencyId, jobId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " and jobId: " + jobId + " not found"));
        if (JobShareStatus.INACTIVE.equals(agencySharedJob.getShareStatus())) {
            throw new CustomParameterizedException("This job sharing has already be terminated!");
        }

        Agency agency = agencyRepository.findById(agencyId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " not found"));
        attachKpiUsers(agency, submitToJobVO);

        TalentRecruitmentProcessVO res = applicationService.submitToJob(submitToJobVO);

        AgencySubmitApplication submitApplication = new AgencySubmitApplication();
        submitApplication.setAgencyId(agencyId);
        submitApplication.setJobId(jobId);
        submitApplication.setTalentId(res.getTalentId());
        submitApplication.setTalentRecruitmentProcessId(submitToJobVO.getTalentRecruitmentProcessId());

        agencySubmitApplicationRepository.save(submitApplication);

        return res;
    }

    private void attachKpiUsers(Agency agency, TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
        List<TalentRecruitmentProcessKpiUserVO> kpiUsers = new ArrayList<>();

        TalentRecruitmentProcessKpiUserVO am = new TalentRecruitmentProcessKpiUserVO();
        am.setUserId(agency.getAgencyOwnerUserId());
        am.setUserRole(UserRole.AM);
        kpiUsers.add(am);

        TalentRecruitmentProcessKpiUserVO source = new TalentRecruitmentProcessKpiUserVO();
        am.setUserId(agency.getAgencyOwnerUserId());
        am.setUserRole(UserRole.SOURCER);
        kpiUsers.add(am);

        TalentRecruitmentProcessKpiUserVO recruiter = new TalentRecruitmentProcessKpiUserVO();
        am.setUserId(agency.getAgencyOwnerUserId());
        am.setUserRole(UserRole.RECRUITER);
        kpiUsers.add(am);

        submitToJobVO.setKpiUsers(kpiUsers);
    }

    @Override
    public ApplicationIdAndStatusListVO getAllApplications(ApplicationSearchDTO applicationSearchDTO, Pageable pageable) {
        List<Long> applicationIds = new ArrayList<>();
        NodeType applicationStatus = null;
        if (Objects.nonNull(applicationSearchDTO)) {
            applicationIds = applicationSearchDTO.getApplicationIds();
            applicationStatus = applicationSearchDTO.getApplicationStatus();
        }
        Agency agency = checkPermission(SecurityUtils.getAgencyId());

        List<ApplicationIdAndJobSharingStatusDTO> applicationIdAndJobSharingStatusList = agencySubmitApplicationRepository.findAllApplicationIdAndJobSharingStatusByAgencyId(agency.getId());

        Set<Long> allRelatedIds = new HashSet<>();
//        Set<Long> stoppedSharedApplicationIds = new HashSet<>();
        Map<Long, NodeType> stoppedSharedApplicationMap = new HashMap<>();
        for (ApplicationIdAndJobSharingStatusDTO a : applicationIdAndJobSharingStatusList) {
            if (JobShareStatus.INACTIVE.equals(a.getJobShareStatus())) {
//                stoppedSharedApplicationIds.add(a.getApplicationId());
                stoppedSharedApplicationMap.put(a.getApplicationId(), a.getLastStatus());
            }
            allRelatedIds.add(a.getApplicationId());
        }

//        Set<Long> allRelatedIds = new HashSet<>(agencySubmitApplicationRepository.findAllApplicationIdsByAgencyId(agency.getId()));
        Set<Long> ids = new HashSet<>();
        if (CollectionUtils.isNotEmpty(applicationIds)) {
            for (Long applicationId : applicationIds) {
                if (allRelatedIds.contains(applicationId)) {
                    ids.add(applicationId);
                }
            }
        } else {
            ids.addAll(allRelatedIds);
        }

        ApplicationIdAndStatusListVO res = applicationService.getAllApplicationsStatus(ids, applicationStatus, pageable);
        if (MapUtils.isNotEmpty(stoppedSharedApplicationMap)) {
            for (int i = 0; i < CollectionUtils.size(res.getApplications()); i++) {
                ApplicationIdAndStatusVO applicationIdAndStatusVO = res.getApplications().get(i);
                if (stoppedSharedApplicationMap.containsKey(applicationIdAndStatusVO.getApplicationId())) {
                    NodeType lastStatus = stoppedSharedApplicationMap.get(applicationIdAndStatusVO.getApplicationId());
                    applicationIdAndStatusVO.setLatestStatus(lastStatus);
                }
            }
        }

        return res;
    }

    @Override
    public LinkedHashMap<String, Integer> getMyApplicationCandidates(Instant startTime, Instant endTime) {

        Agency agency = agencyRepository.findById(SecurityUtils.getAgencyId()).orElseThrow(() -> new CustomParameterizedException("Agency with id " + SecurityUtils.getAgencyId() + " not found"));
        if (!SecurityUtils.getUserId().equals(agency.getAgencyOwnerUserId())) {
            throw new CustomParameterizedException("You are not allowed to view this agency's applications, id: " + agency.getId());
        }

        List<Long> applicationIds = agencySubmitApplicationRepository.findAllApplicationIdsByAgencyId(agency.getId());

        List<Long> userIdList = new ArrayList<>();
        userIdList.add(SecurityUtils.getUserId());
        return applicationService.getMyApplicationCandidates(startTime, endTime, userIdList, applicationIds);
    }

    @Override
    public Boolean checkIfHasNewCandidate() {
        String key = String.format(AGENCY_NEW_APPLICATION_KEY_PATTERN_PREFIX_USER_ID, SecurityUtils.getUserId());
        return redisService.scanForKeyPattern(key);
    }

    private void constructAndSendToMq(Agency agency, String type) {
        AgencySyncToMqDTO agencySyncToMqDTO = new AgencySyncToMqDTO(agency, SecurityUtils.getTenantId(), type);
        rabbitMqService.sendAgencyInfoToPortal(JSONUtil.toJsonStr(agencySyncToMqDTO));
    }

    @Override
    public void cacheNewApplicationToRedis(AgencySubmitApplication result) {
        try {
            Set<Long> userIds = findAllRelatedUserIdsByAgencyId(result.getAgencyId());

            Long agencyId = result.getAgencyId();
            Long jobId = result.getJobId();
            Long applicationId = result.getTalentRecruitmentProcessId();

            List<String> keys = new ArrayList<>();
            for (Long userId : userIds) {
                String key = String.format(AGENCY_NEW_APPLICATION_KEY_PATTERN, userId, agencyId, jobId);
                keys.add(key);
            }

            redisService.pipeline(keys, String.valueOf(applicationId), redisNewApplicationReminderExpTime);
        } catch (Exception e) {
            log.error("[AgencyService: cacheNewApplicationToRedis] error msg: {}", e.getMessage(), e);
        }
    }

    private Set<Long> findAllRelatedUserIdsByAgencyId(Long agencyId) {
        Agency agency = agencyRepository.findById(agencyId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " not found"));
        Set<Long> userIds = agencySharingUserRepository.findAllUserIdsByAgencyId(agencyId);
        userIds.add(agency.getAgencyOwnerUserId());
        userIds.add(agency.getAgencyCreatorUserId());

        return userIds;
    }

    public TalentDTOV3 updateTalentInfo(Long agencyId, Long jobId, Long duplicateId, TalentInfoInputWithId input) {
        if (Objects.isNull(input.getId())) {
            throw new CustomParameterizedException("talent id is null!");
        }
        Long id = input.getId();
        input.setId(null);

        Agency agency = agencyRepository.findById(agencyId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " not found"));
        //todo check authority

        AgencySharedJob agencySharedJob = agencySharedJobRepository.findByAgencyIdAndJobId(agencyId, jobId).orElseThrow(() -> new CustomParameterizedException("Agency with id " + agencyId + " and jobId: " + jobId + " not found"));
        if (JobShareStatus.INACTIVE.equals(agencySharedJob.getShareStatus())) {
            throw new CustomParameterizedException("This job sharing has already be terminated!");
        }

        AgencySubmitDuplicateTalent duplicateTalent = agencySubmitDuplicateTalentRepository.findById(duplicateId).orElseThrow(() -> new CustomParameterizedException("Duplicate talent id " + duplicateId + " not found"));
        if (!duplicateTalent.getJobId().equals(jobId) || !duplicateTalent.getAgencyId().equals(agencyId)) {
            throw new CustomParameterizedException("Duplicate talent id " + duplicateId + " cannot be retrived");
        }

        Set<Long> duplicateTalentIds = new HashSet<>(duplicateTalent.getDuplicateTalentIds());
        if (!duplicateTalentIds.contains(id)) {
            throw new CustomParameterizedException("You cannot update this talent: " + id);
        }

        refreshTalentResumeParseResult(input.getResumes());

        try {
            TalentDTOV3 result = talentService.updateTalentInfo(id, input);

            duplicateTalent.setMergedTalentId(id);
            agencySubmitDuplicateTalentRepository.saveAndFlush(duplicateTalent);

            return result;
        } catch (WithDataException ex) {
            log.error("[AgencyService: updateTalentInfo] found duplicate when update talentId: {}, duplicateId: {}, error msg: {}, talentDTO: {}", id, duplicateId, ex.getMessage(), input, ex);
            throw ex;
        } catch (Exception e) {
            log.error("[AgencyService: updateTalentInfo] talentId: {}, duplicateId: {}, error msg: {}, talentDTO: {}", id, duplicateId, e.getMessage(), input, e);
            throw new CustomParameterizedException("Failed to update this talent: " + id + " with error: " + e.getMessage());
        }
    }


    public void refreshTalentResumeParseResult(List<TalentResumeDTO> resumes) {
        if (CollectionUtils.isEmpty(resumes)) {
            return;
        }

        for (TalentResumeDTO resume : resumes) {
            if (Objects.isNull(resume.getId())) {
                if (StringUtils.isAnyBlank(resume.getUuid(), resume.getFileName())) {
                    log.error("[refreshTalentResumeParseResult] talent resume info not complete, resume: {}", resume);
                    continue;
                }
                String contentType = "application/pdf";
                if (Objects.nonNull(resume.getImagesInfo()) && StringUtils.isNotEmpty(resume.getImagesInfo().getContentType())) {
                    contentType = resume.getImagesInfo().getContentType();
                }
                String uuid = resume.getUuid();
                String filename = resume.getFileName();
                log.info("[refreshTalentResumeParseResult] refresh resume: {}", resume);
                try {
                    ParserResponse response = commonService.checkResumeParseResultOrGetUploadUrl(uuid, 1, filename, contentType).getBody();
                    if (Objects.isNull(response)) {
                        log.error("[refreshTalentResumeParseResult] refresh resume uuid: {}, response is null", uuid);
                    }
                    if (ParseStatus.NONE.equals(response.getStatus())) {
                        log.error("[refreshTalentResumeParseResult] refresh resume uuid: {} error, cannot find resume in s3", uuid);
                    }

                    if (ParseStatus.FINISHED.equals(response.getStatus())) {
                        log.info("[refreshTalentResumeParseResult] refresh resume uuid: {} finished, does not need to refresh", uuid);
                        continue;
                    }
                    if (ParseStatus.EDIT.equals(response.getStatus()) || ParseStatus.NOT_ACCEPT.equals(response.getStatus()) || ParseStatus.TIMEOUT.equals(response.getStatus())) {
                        log.error("[refreshTalentResumeParseResult] refresh resume uuid: {} error, parse status: {}", uuid, response.getStatus());
                    }

                    boolean refreshFinished = false;
                    ParseStatus lastParseStatus = null;
                    //STARTED, QUEUED
                    for (int i = 0; i < 10; i++) {
                        try {
                            TimeUnit.SECONDS.sleep(1);

                            ParserResponse response1 = commonService.checkResumeParseResultOrGetUploadUrl(uuid, 1, filename, contentType).getBody();
                            if (Objects.nonNull(response1) && ParseStatus.FINISHED.equals(response1.getStatus())) {
                                refreshFinished = true;
                                break;
                            } else {
                                if (Objects.nonNull(response1)) {
                                    lastParseStatus = response1.getStatus();
                                }
                                log.debug("[refreshTalentResumeParseResult] {}th loop refresh resume: {}, parser status: {}", i + 1, uuid, Objects.isNull(response1) ? "null" : response1.getStatus());
                            }
                        } catch (InterruptedException ex) {
                            log.error("[refreshTalentResumeParseResult] interrupted exception when {}th loop refresh resume: {}, error msg: {}", i+1, uuid, ex.getMessage());
                        } catch (Exception exception) {
                            log.error("[refreshTalentResumeParseResult] error when {}th loop refresh resume: {}, error msg: {}", i+1, uuid, exception.getMessage(), exception);
                        }
                    }

                    if (!refreshFinished) {
                        log.error("[refreshTalentResumeParseResult] cannot finish refresh resume: {}, last parse status is: {}", uuid, lastParseStatus);
                    } else {
                        log.info("[refreshTalentResumeParseResult] successfully refresh resume: {}", uuid);
                    }

                } catch (Exception e) {
                    log.error("[refreshTalentResumeParseResult] error when refresh resume uuid: {}, error msg: {}, e", uuid, e.getMessage(), e);
                }
            }
        }
    }

}
