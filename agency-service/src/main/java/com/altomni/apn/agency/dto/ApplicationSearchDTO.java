package com.altomni.apn.agency.dto;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * A ApplicationSearchDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationSearchDTO {

    private List<Long> applicationIds;

    private NodeType applicationStatus;

}
