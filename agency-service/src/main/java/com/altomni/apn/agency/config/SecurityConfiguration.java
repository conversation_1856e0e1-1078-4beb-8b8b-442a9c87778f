package com.altomni.apn.agency.config;

import com.altomni.apn.agency.interceptor.AgencyInterceptor;
import com.altomni.apn.common.auth.SkipOAuthTokenResolver;
import com.altomni.apn.common.auth.agency_auth.AgencyTokenFilter;
import com.altomni.apn.common.auth.agency_auth.AgencyUserTokenStore;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationEntryPoint;
import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@EnableMethodSecurity
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfiguration implements WebMvcConfigurer {

    private static final String[] PUBLIC_ENDPOINTS = {
            "/api/v3/liveness",
            "/actuator/**"
    };

    private static final String[] AGENCY_ENDPOINTS = {
            "/api/v3/talent-recruitment-processes/submit-to-job",
            "/api/v3/portal/suspected-duplicate-phones-check",
            "/api/v3/portal/activity/all",
            "/api/v3/portal/activity/mark-read",
            "/api/v3/portal/job/{jobId}/shared-info",
            "/api/v3/portal/talent-notes/{id}",
            "/api/v3/portal/talent-notes/talent/{talentId}",
            "/api/v3/portal/talent-notes",
            "/api/v3/portal/talent-and-application",
            "/api/v3/portal/applications",
            "/api/v3/portal/dashboard/my-application-candidates",
            "/api/v3/portal/parsers/resume/status",
            "/api/v3/portal/parsers/resume/result/{uuid}",
            "/api/v3/portal/parsers/resume/result-status/{uuid}",
            "/api/v3/portal/parsers/resume/info/{uuid}",
            "/api/v3/portal/auto-complete/college",
            "/api/v3/portal/data-service/search",
            "/api/v3/portal/data-service/jobs/city-names",
            "/api/v3/mail/{id}",
            "/api/v3/portal/test/{id}",
            "/api/v3/test"
    };

    private final AgencyUserTokenStore agencyUserTokenStore;

    private final AgencyInterceptor securityInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(securityInterceptor);
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 资源服务器配置
        http.oauth2ResourceServer(auth -> auth.opaqueToken(Customizer.withDefaults()));
        // 没有 token 或者 token 过期，返回 401
        http.exceptionHandling(exceptionHandling -> exceptionHandling.authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint()));
        // 无状态 session
        http.sessionManagement((session) -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        // 资源权限配置，所有请求都需要认证
        http.authorizeHttpRequests(
                authorizationManagerRequestMatcherRegistry -> authorizationManagerRequestMatcherRegistry
                        .requestMatchers(PUBLIC_ENDPOINTS).permitAll()
                        .anyRequest().authenticated());

        http.addFilterBefore(agencyTokenFilter(agencyUserTokenStore), BearerTokenAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public BearerTokenResolver bearerTokenResolver() {
        return new SkipOAuthTokenResolver();
    }

    public AgencyTokenFilter agencyTokenFilter(AgencyUserTokenStore agencyUserTokenStore) {
        return new AgencyTokenFilter(agencyUserTokenStore, AGENCY_ENDPOINTS);
    }

}