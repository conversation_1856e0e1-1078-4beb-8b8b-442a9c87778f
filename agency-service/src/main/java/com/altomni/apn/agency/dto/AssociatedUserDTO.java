package com.altomni.apn.agency.dto;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * A AssociatedUserDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AssociatedUserDTO {

    private Long id;

    private String firstName;

    private String lastName;

    private String fullName;

    public AssociatedUserDTO(UserBriefDTO user) {
        this.id = user.getId();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.fullName = user.getFullName();
    }
}
