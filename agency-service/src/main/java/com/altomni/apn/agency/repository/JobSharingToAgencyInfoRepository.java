package com.altomni.apn.agency.repository;

import com.altomni.apn.agency.domain.JobSharingToAgencyInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.Set;


/**
 * Spring Data repository for the JobSharingToAgencyInfo entity.
 */
@SuppressWarnings("unused")
@Repository
public interface JobSharingToAgencyInfoRepository extends JpaRepository<JobSharingToAgencyInfo, Long> {

    Optional<JobSharingToAgencyInfo> findByJobId(Long jobId);

    @Query(value = "select j.id from job_project j " +
            "where j.tenant_id =:tenantId", nativeQuery = true)
    Set<Long> findPrivateJobTeamIds(@Param("tenantId") Long tenantId);

}
