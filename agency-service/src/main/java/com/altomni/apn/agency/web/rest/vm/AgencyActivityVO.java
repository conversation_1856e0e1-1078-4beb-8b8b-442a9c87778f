package com.altomni.apn.agency.web.rest.vm;

import com.altomni.apn.agency.domain.AgencyActivity;
import com.altomni.apn.agency.dto.SimpleUserInfoDTO;
import com.altomni.apn.common.domain.enumeration.agency.AgencyActivityType;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * A AgencyActivityVO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencyActivityVO {

    private Long id;

    private Long agencyId;

    private String agencyName;

    private AgencyActivityType activityType;

    @JsonRawValue
    private String details;

    private SimpleUserInfoDTO creator;

    private String createdBy;

    private Instant createdDate;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    public AgencyActivityVO(AgencyActivity agencyActivity) {
        this.id = agencyActivity.getId();
        this.agencyId = agencyActivity.getAgencyId();
        this.activityType = agencyActivity.getActivityType();
        this.details = agencyActivity.getDetails();
        this.createdBy = agencyActivity.getCreatedBy();
        this.createdDate = agencyActivity.getCreatedDate();
        this.lastModifiedBy = agencyActivity.getLastModifiedBy();
        this.lastModifiedDate = agencyActivity.getLastModifiedDate();
    }

}
