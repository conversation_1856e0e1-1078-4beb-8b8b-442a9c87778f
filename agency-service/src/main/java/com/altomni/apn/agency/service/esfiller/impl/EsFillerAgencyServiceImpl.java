package com.altomni.apn.agency.service.esfiller.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.agency.config.env.AgencyMQProperties;
import com.altomni.apn.agency.domain.Agency;
import com.altomni.apn.agency.domain.AgencySharingUser;
import com.altomni.apn.agency.dto.AgencySyncEsDocument;
import com.altomni.apn.agency.repository.AgencyRepository;
import com.altomni.apn.agency.repository.AgencySharingUserRepository;
import com.altomni.apn.agency.service.esfiller.EsFillerAgencyService;
import com.altomni.apn.agency.service.rabbitmq.RabbitMqService;
import com.altomni.apn.agency.service.redis.RedisService;
import com.altomni.apn.agency.service.user.UserService;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Resource
@Service
public class EsFillerAgencyServiceImpl implements EsFillerAgencyService {

    @Resource
    private AgencyMQProperties agencyMQProperties;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private UserService userService;

    @Resource
    private AgencyRepository agencyRepository;

    @Resource
    private AgencySharingUserRepository agencySharingUserRepository;

    @Resource
    private RedisService redisService;

    @Resource
    private CanalService canalService;

    @Override
    public void extractAgencyToMq(Collection<Long> agencyIds, int priority) {
        log.info("[EsFillerAgencyService: extractAgencyToMq @{}] agencyProfiles start ids: {}", SecurityUtils.getUserId(), agencyIds);

        List<AgencySyncEsDocument> agencyProfiles = agencyRepository.findAllByIdIn(agencyIds).stream().map(j -> buildAgencyProfile(j, 0)).collect(Collectors.toList());

        log.info("[EsFillerAgencyService: extractAgencyToMq @{}] agencyProfiles length: {}, ids: {}", SecurityUtils.getUserId(), agencyProfiles.size(), agencyIds);
        for (AgencySyncEsDocument agencyProfile : agencyProfiles) {
            if (Objects.isNull(agencyProfile)) {
                continue;
            }
            Long id = agencyProfile.get_id();
            try {
//                System.out.println("---------------------------------------------------");
//                System.out.println(JSONUtil.toJsonStr(agencyProfile));
//                System.out.println("---------------------------------------------------");

                rabbitMqService.saveAgencyInfoToEs(JSONUtil.toJsonStr(agencyProfile), priority);
                canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.AGENCY);
                log.info("[EsFillerAgencyService: extractAgencyToMq @{}] save agency to MQ success, id: {}, last modified date: {}", SecurityUtils.getUserId(), id, agencyProfile.get_source().getStr("lastModifiedDate"));
            } catch (Exception e) {
                log.error("[EsFillerAgencyService: extractAgencyToMq @{}] save agency to MQ error, id: {}", SecurityUtils.getUserId(), id);
                canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.AGENCY, FailReasonEnum.ERROR, e.getMessage(), 0);
                String message = "Send Agency to ES Error" +
                        "\n\tAgency ID: " + id +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(agencyMQProperties.getLarkWebhookKey(), agencyMQProperties.getLarkWebhookUrl(), message);
            }
        }
    }

    private AgencySyncEsDocument buildAgencyProfile(Agency agency, int retryCount) {

        Long agencyId = agency.getId();
        Long tenantId = agency.getTenantId();

        AgencySyncEsDocument document = new AgencySyncEsDocument();
        try {

            JSONObject source = this.buildEsSourceForAgency(agency);


            document.set_id(agencyId);
            document.set_tenant_id(String.valueOf(tenantId));
            document.set_type("agency");
//            document.set_return_routing_key(esfillerMQProperties.getApnNormalizedJobRoutingKey());
            document.set_source(source);

        } catch (Exception e) {
            log.error("[EsFillerAgencyService: buildAgencyProfile @{}] buildAgencyProfile error, id: {}, error: {}", SecurityUtils.getUserId(), agencyId, ExceptionUtils.getStackTrace(e));
            if (retryCount < agencyMQProperties.getRetryThreshold()) {
                document = this.buildAgencyProfile(agency, retryCount + 1);
            } else {
                redisService.saveFailedAgencyIds(Arrays.asList(agencyId));
                document = null;
                String message = "Build Agency Profile Error" +
                        "\n\tAgency ID: " + agencyId +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(agencyMQProperties.getLarkWebhookKey(), agencyMQProperties.getLarkWebhookUrl(), message);
            }
        }
        String json = JSONUtil.toJsonStr(document);
        return document;
    }

    private JSONObject buildEsSourceForAgency(Agency agency) {
        log.info("[EsFillerAgencyService: buildEsSourceForAgency @{}] buildEsSourceForAgency: agencyId:{}, lastModifiedDate:", SecurityUtils.getUserId(), agency.getId());

        List<AgencySharingUser> agencySharingUsers = agencySharingUserRepository.findAllByAgencyId(agency.getId());

        JSONObject source = new JSONObject();
        source.put("agencyName", agency.getName());
        source.put("active", ActiveStatus.ACTIVE.equals(agency.getStatus()));

        // set agency contact name
        JSONObject agencyContact = new JSONObject();
        agencyContact.put("firstName", agency.getAgencyContactFirstName());
        agencyContact.put("lastName", agency.getAgencyContactLastName());
        agencyContact.put("fullName", CommonUtils.formatFullNameWithBlankCheck(agency.getAgencyContactFirstName(), agency.getAgencyContactLastName()));
        source.put("agencyContactName", agencyContact);

        source.put("description", agency.getDescription());
        source.put("createdDate", agency.getCreatedDate());
        source.put("lastModifiedDate", agency.getLastModifiedDate());

        // add contacts info
        addContacts(source, agency);

        // add associated users
        List<Long> sharingUserIds = agencySharingUsers.stream().map(AgencySharingUser::getUserId).toList();
        Set<Long> userIds = new HashSet<>(sharingUserIds);
        userIds.add(agency.getAgencyCreatorUserId());
        userIds.add(agency.getAgencyOwnerUserId());
        List<UserBriefDTO> users = userService.findBriefUsers(userIds);
        Map<Long, UserBriefDTO> userMap = users.stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));

        // add associated users: 1. set agency creator
        List<UserBriefDTO> creator = new ArrayList<>();
        creator.add(userMap.get(agency.getAgencyCreatorUserId()));
        addAssociatedUsers(source, creator, "responsibility0");

        // add associated users: 2. set agency owner
        List<UserBriefDTO> owner = new ArrayList<>();
        owner.add(userMap.get(agency.getAgencyOwnerUserId()));
        addAssociatedUsers(source, owner, "responsibility1");

        // add associated users: 3. set agency sharing users
        List<UserBriefDTO> sharingUsers = sharingUserIds.stream().map(userMap::get).filter(Objects::nonNull).toList();
        addAssociatedUsers(source, sharingUsers, "responsibility2");

        return source;
    }

    private void addContacts(JSONObject esDocumentJson, Agency agency) {
        List<JSONObject> contactList = new ArrayList<>();

        if (StringUtils.isNotBlank(agency.getEmail())) {
            JSONObject obj = new JSONObject();
            obj.put("contact", agency.getEmail());
            obj.put("type", ContactType.EMAIL);

            contactList.add(obj);
        }

        if (StringUtils.isNotBlank(agency.getPhone())) {
            JSONObject obj = new JSONObject();
            obj.put("contact", agency.getPhone());
            obj.put("type", ContactType.PHONE);

            contactList.add(obj);
        }

        esDocumentJson.put("contacts", contactList);
    }

    private void addAssociatedUsers(JSONObject esDocumentJson, List<UserBriefDTO> users, String responsibility) {
        if (CollUtil.isNotEmpty(users)) {
            esDocumentJson.put(responsibility, users
                    .stream().filter(Objects::nonNull)
                    .map(u -> new AgencySyncEsDocument.Responsibility(String.valueOf(u.getId()), CommonUtils.formatFullNameWithBlankCheck(u.getFirstName(), u.getLastName())))
                    .collect(Collectors.toList()));
        }
    }

}
