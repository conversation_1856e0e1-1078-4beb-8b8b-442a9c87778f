package com.altomni.apn.agency.service.data.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.agency.dto.AgencyJobRelatedLocationSearchDTO;
import com.altomni.apn.agency.repository.AgencySharedJobRepository;
import com.altomni.apn.agency.service.data.DataService;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.agency.config.env.ApplicationProperties;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.Iterator;
import java.util.List;

@Service
public class DataServiceImpl implements DataService {

    private final Logger log = LoggerFactory.getLogger(DataServiceImpl.class);

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private AgencySharedJobRepository agencySharedJobRepository;

    @Resource
    private HttpService httpService;

    private String dataServiceAgencyBaseUrl() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/agency";
    }

    @Override
    public HttpResponse searchFromDataService(SearchGroup searchGroup, Pageable pageable) throws IOException {
        String condition = JSONUtil.toJsonStr(JSONUtil.parse(searchGroup));
        log.info("AGENCY_JOB_SEARCH_CONDITION= " + condition);
        if (ObjectUtil.isNull(condition)) {
            return null;
        }
        String url = dataServiceAgencyBaseUrl();
        if (ObjectUtil.isNotEmpty(pageable)) {
            if (ObjectUtil.isNotNull(pageable.getPageSize()) && ObjectUtil.isNotNull(pageable.getPageNumber())) {
                url = dataServiceAgencyBaseUrl() + "?page=" + pageable.getPageNumber() + "&size=" + pageable.getPageSize();
            }
            if (pageable.getSort().isSorted()) {
                for (Sort.Order sort : pageable.getSort()) {
//                                        url += "&sort=" + convertESSortKeyToJobEsKey(sort.getProperty()) + StrUtil.COMMA + sort.getDirection();
                    url += "&sort=" + sort.getProperty() + StrUtil.COMMA + sort.getDirection();
                }
            }
        }
        Instant start = Instant.now();
        HttpResponse response = httpService.post(url, condition);

//        System.out.println(url);
//        System.out.println(condition);

        Instant end = Instant.now();
        log.info("[apn module={}, index={}, timeZone={}] EsFillerJobService.searchFromCommonService time = {}ms", searchGroup.getModule(), searchGroup.getIndex(), searchGroup.getTimeZone(), Duration.between(start, end).toMillis());
        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[Agency: DataServiceImpl @{}] search agency job from data service success, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData = 0;
//                if (ModuleType.AGENCY_JOB.getName().equals(searchGroup.getModule())) {
//                    //search job by tenantId
//                    countData = jobRepository.countByTenantId(SecurityUtils.getTenantId());
//                }
                if (countData == 0) {
                    return new HttpResponse();
                } else {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[Agency: DataServiceImpl @{}] search agency job from data service error, searchRequest: {}, pageable:{}, response code: {}, response message: {}", SecurityUtils.getUserId(), condition, pageable, response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[Agency: DataServiceImpl @{}] search agency job from data service error and response is null, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }

    @Override
    public HttpResponse getAllCityNames() throws IOException {

        log.info("[DataServiceImpl: getAllCityNames] get all city names for tenantId: {}", SecurityUtils.getTenantId());

        List<Long> jobIds = agencySharedJobRepository.findAllJobIdsByAgencyIdAndShareStatus(SecurityUtils.getAgencyId(), JobShareStatus.ACTIVE);

        AgencyJobRelatedLocationSearchDTO searchDTO  = new AgencyJobRelatedLocationSearchDTO();
        searchDTO.setTenantId(SecurityUtils.getTenantId());
        searchDTO.setJobIds(jobIds);

//        String url = dataServiceAgencyBaseUrl() + "/jobs/city-names?tenantId=" + SecurityUtils.getTenantId();
        String url = dataServiceAgencyBaseUrl() + "/jobs/city-names";

        HttpResponse response = httpService.post(url, JSONUtil.toJsonStr(searchDTO));

        if (response != null) {
            if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {

                log.info("[Agency: DataServiceImpl @{}] get all city names for tenantId: {}, success", SecurityUtils.getUserId(), SecurityUtils.getTenantId());
                //Special handling when an error code is 404/422 "Empty query", return 200
            } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                Integer countData = 0;
//                if (ModuleType.AGENCY_JOB.getName().equals(searchGroup.getModule())) {
//                    //search job by tenantId
//                    countData = jobRepository.countByTenantId(SecurityUtils.getTenantId());
//                }
                if (countData == 0) {
                    return new HttpResponse();
                } else {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[Agency: DataServiceImpl @{}] get all city names for tenantId: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), SecurityUtils.getTenantId(), response.getCode(), response.getBody());
                throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
            }
        } else {
            log.error("[Agency: DataServiceImpl @{}] get all city names for tenantId: {}, error and response is null", SecurityUtils.getUserId(), SecurityUtils.getTenantId());
            throw new ExternalServiceInterfaceException();
        }
        return response;
    }
}
