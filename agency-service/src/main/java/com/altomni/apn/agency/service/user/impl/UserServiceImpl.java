package com.altomni.apn.agency.service.user.impl;

import com.altomni.apn.agency.service.user.UserClient;
import com.altomni.apn.agency.service.user.UserService;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

@Service
@Transactional
public class UserServiceImpl implements UserService {

    @Resource
    private UserClient userClient;

    @Override
    public UserBriefDTO getUserById(Long userId) {
        if (userId == null) {
            return null;
        }
        List<UserBriefDTO> userBriefDTOS = userClient.getAllBriefUsersByIds(List.of(userId)).getBody();
        return CollectionUtils.isNotEmpty(userBriefDTOS) ? userBriefDTOS.get(0) : null;
    }

    @Override
    public List<UserBriefDTO> findBriefUsers(Collection<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.emptyList();
        }
        ResponseEntity<List<UserBriefDTO>> response = userClient.getAllBriefUsersByIds(ids.stream().filter(Objects::nonNull).toList());
        if (response != null && response.getBody() != null) {
            return response.getBody();
        }
        return Collections.emptyList();
    }

    @Override
    public Integer getTenantParamValue(String paramKey) {
        ResponseEntity<Integer> response = userClient.getTenantParamValue(paramKey);
        return response != null ? response.getBody() : null;
    }

    @Override
    public void clearJobPreferenceByRecruitmentProcessId(Long recruitmentProcessId) {
        userClient.removeUserPreferenceRecruitmentProcess(recruitmentProcessId);
    }
}
