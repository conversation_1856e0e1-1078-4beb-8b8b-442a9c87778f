package com.altomni.apn.agency.web.rest.vm;

import com.altomni.apn.agency.domain.Agency;
import com.altomni.apn.agency.dto.AssociatedUserDTO;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.utils.CommonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import javax.persistence.Column;
import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgencyStatsVO {

    private Long id;

    private String name;

    private String agencyContactFirstName;

    private String agencyContactLastName;

    private String agencyContactFullName;

    private String description;

    private JobShareStatus jobShareStatus;

    private Integer submittedTalents;

    private List<Long> submittedTalentIds;

    private Integer ongoingApplicationCount;

    private Instant lastSubmittedTime;

    private String createdBy;

    private Instant createdDate;

    private String lastModifiedBy;

    private Instant lastModifiedDate;

    private AssociatedUserDTO agencyOwner;

    public AgencyStatsVO(final Agency agency) {
        this.id = agency.getId();
        this.name = agency.getName();
        this.agencyContactFirstName = agency.getAgencyContactFirstName();
        this.agencyContactLastName = agency.getAgencyContactLastName();
        if (CommonUtils.isChineseCharacter(this.agencyContactFirstName + this.agencyContactLastName)) {
            this.agencyContactFullName = this.agencyContactLastName + this.agencyContactFirstName;
        } else {
            this.agencyContactFullName = agencyContactFirstName + " " + agencyContactLastName;
        }
        this.description = agency.getDescription();
        this.submittedTalents = 0;
        this.lastSubmittedTime = Instant.now();
        this.createdBy = agency.getCreatedBy();
        this.createdDate = agency.getCreatedDate();
        this.lastModifiedBy = agency.getLastModifiedBy();
        this.lastModifiedDate = agency.getLastModifiedDate();
    }

}
