package com.altomni.apn.agency.utils;

import okhttp3.Headers;
import org.springframework.http.HttpHeaders;

import java.util.Objects;

public final class HeaderConvertUtil {

    private HeaderConvertUtil() {
    }

    public static HttpHeaders convertHeaders(Headers headers) {
        HttpHeaders res = new HttpHeaders();
        if (Objects.isNull(headers)) {
            return res;
        }
        // 允许前端代码跨域访问 www-authenticate 头
        headers.names().stream().filter(header -> header.equalsIgnoreCase(HttpHeaders.WWW_AUTHENTICATE))
                .findFirst().ifPresent(header -> res.add(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, header));

        for (String headerName : headers.names()) {
            res.add(headerName, headers.get(headerName));
        }

        return res;
    }
}