package com.altomni.apn.agency.repository;

import com.altomni.apn.agency.domain.AgencySubmitApplication;
import com.altomni.apn.agency.dto.ApplicationIdAndJobSharingStatusDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * Spring Data  repository for the AgencySubmitApplication entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AgencySubmitApplicationRepository extends JpaRepository<AgencySubmitApplication, Long> {

    Integer countAgencySubmitApplicationByAgencyIdAndJobId(Long agencyId, Long jobId);

//    @Query(value = "SELECT subquery.agency_id, subquery.created_date " +
//            "FROM ( " +
//            "  SELECT a.agency_id as agency_id, a.created_date as created_date, ROW_NUMBER() OVER (PARTITION BY a.agency_id ORDER BY a.created_date DESC) AS rn " +
//            "  FROM agency_data a " +
//            "  WHERE a.agency_id IN (:agencyIds) " +
//            ") AS subquery " +
//            "WHERE subquery.rn = 1", nativeQuery = true)
//    List<Object[]> findLatestCreatedDateByAgencyIds(@Param("agencyIds") List<Long> agencyIds);

//    @Query(value = "SELECT subquery.agency_id, subquery.talent_recruitment_process_count, subquery.latest_created_date " +
//            "FROM ( " +
//            "    SELECT a.agency_id, " +
//            "           COUNT(DISTINCT a.talent_recruitment_process_id) AS talent_recruitment_process_count, " +
//            "           MAX(a.created_date) OVER (PARTITION BY a.agency_id) AS latest_created_date, " +
//            "           ROW_NUMBER() OVER (PARTITION BY a.agency_id ORDER BY a.created_date DESC) AS rn " +
//            "    FROM agency_submit_application a " +
//            "    WHERE a.job_id = :jobId " +
//            "    GROUP BY a.agency_id, a.created_date, a.talent_recruitment_process_id " +
//            ") AS subquery " +
//            "WHERE subquery.rn = 1",
//            nativeQuery = true)
//    List<Object[]> findAgencyStatsByJobId(@Param("jobId") Long jobId);

    @Query(value = "WITH latest_created AS ( " +
            "    SELECT " +
            "        agency_id, " +
//            "        COUNT(DISTINCT talent_recruitment_process_id) AS talent_recruitment_process_count, " +
            "        GROUP_CONCAT(DISTINCT asa.talent_id SEPARATOR ',') AS talent_ids, " +
            "        COUNT(DISTINCT trpn.talent_recruitment_process_id) as ongoing_application_count, " +
            "        MAX(asa.created_date) AS latest_created_date " +
            "    FROM agency_submit_application asa " +
            "    LEFT JOIN talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = asa.talent_recruitment_process_id and trpn.node_status = 1 and trpn.node_type <> 60 " +
            "    WHERE job_id = :jobId " +
            "    GROUP BY agency_id " +
            ") " +
            "SELECT * FROM latest_created", nativeQuery = true)
    List<Object[]> findAgencyStatsByJobId(@Param("jobId") Long jobId);

    @Query(value = "SELECT talentRecruitmentProcessId FROM AgencySubmitApplication WHERE agencyId = ?1")
    List<Long> findAllApplicationIdsByAgencyId(Long agencyId);

    @Query(value = "SELECT NEW com.altomni.apn.agency.dto.ApplicationIdAndJobSharingStatusDTO(asa.talentRecruitmentProcessId, asj.shareStatus, asa.lastStatus) FROM AgencySubmitApplication asa LEFT JOIN AgencySharedJob asj ON asj.jobId = asa.jobId WHERE asa.agencyId = ?1")
    List<ApplicationIdAndJobSharingStatusDTO> findAllApplicationIdAndJobSharingStatusByAgencyId(Long agencyId);

    List<Long> findAllApplicationIdsByAgencyIdAndJobId(Long agencyId, Long jobId);

    List<AgencySubmitApplication> findAllByJobId(Long jobId);

    List<AgencySubmitApplication> findAllByAgencyId(Long agencyId);

    Optional<AgencySubmitApplication> findByTalentId(Long talentId);

    Optional<AgencySubmitApplication> findByTalentRecruitmentProcessId(Long applicationId);

    @Query(value = "SELECT a.talentId FROM AgencySubmitApplication a WHERE a.jobId = ?1 AND a.agencyId = ?2")
    List<Long> findAllTalentIdsByJobIdAndAgencyId(Long jobId, Long agencyId);

}
