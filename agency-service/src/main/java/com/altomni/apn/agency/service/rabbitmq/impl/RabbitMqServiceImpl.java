package com.altomni.apn.agency.service.rabbitmq.impl;

import com.altomni.apn.agency.config.env.AgencyMQProperties;
import com.altomni.apn.agency.service.rabbitmq.RabbitMqService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class RabbitMqServiceImpl implements RabbitMqService {

    private final Logger log = LoggerFactory.getLogger(RabbitMqServiceImpl.class);

    @Resource
    private AgencyMQProperties agencyMQProperties;

    @Resource(name = "agencyPortalRabbitTemplate")
    private RabbitTemplate rabbitTemplate;


    @Override
    public void sendAgencyInfoToPortal(String agencyInfo) {
        log.info("send agencyInfo to agency portal rabbitMQ " + agencyInfo);
        try {
            rabbitTemplate.convertAndSend(agencyMQProperties.getAgencyPortalExchange(), agencyMQProperties.getAgencyPortalRoutingKey(), agencyInfo, message -> {
                log.info("send agencyInfo to agency portal rabbitMQ, Message: {}", message);
                return message;
            });
        }catch (Exception e) {
            log.error("Send agencyInfo to agency portal rabbitMQ error: {}", e.getMessage());
        }
    }

    @Override
    public void saveAgencyInfoToEs(String agencyInfo, int priority) {
        log.info("[saveAgencyInfoToEs: syncAgencyToMQ @{}] save agency info to esfiller rabbitMQ : {}", SecurityUtils.getUserId(), agencyInfo);
        try {
            rabbitTemplate.convertAndSend(agencyMQProperties.getEsfillerMQExchange(), agencyMQProperties.getToEsFillerRoutingKey(), agencyInfo, message -> {
                message.getMessageProperties().setPriority(priority);
                log.info("send agency info to esfiller rabbitMQ, Message: {}", message);
                return message;
            });
        }catch (Exception e) {
            log.error("[saveAgencyInfoToEs: syncAgencyToMQ @{}] save agency info to esfiller rabbitMQ error: {}", SecurityUtils.getUserId(), e.getMessage());
        }
    }

}
