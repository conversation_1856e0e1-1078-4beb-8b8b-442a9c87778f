package com.altomni.apn.agency.service.agency;

import com.altomni.apn.agency.domain.AgencySubmitApplication;
import com.altomni.apn.agency.dto.*;
import com.altomni.apn.agency.web.rest.vm.*;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Service Interface for managing RecruitmentProcess.
 */
public interface AgencyService {

    Long create(AgencyDTO agency);

    AgencyVO getAgencyById(Long id);

    Integer getAgencyInProgressApplicationsByAgencyId(Long agencyId);

    Integer getAgencyInProgressApplicationsByAgencyIdAndJobId(Long agencyId, Long jobId);

    Page<AgencyVO> getAgencies(String string, Pageable pageable);

    Page<AgencyVO> getAgenciesFromEs(String string, Pageable pageable);

    List<AgencyBriefVO> getAllAgencyBriefs();

    void update(Long id, AgencyDTO agency);

    Page<SharedJobVO> getSharedJobs(Long agencyId, String jobTitle, String companyName, Pageable pageable);

    List<DuplicateTalentVO> getDuplicateTalents(Long agencyId, Long jobId);

    DuplicateTalentBriefDTO getDuplicateTalent(Long agencyId, Long jobId, Long duplicateTalentId);

    List<AgencyStatsVO> getAllAgenciesByJobId(Long jobId);

    SharedToAgencyJobInfoVM getJobSharingToAgencyInfoByJobId(Long jobId);

    String getJobSharingToAgencyInfoByJobId(Long jobId, Long agencyId);

    SharedToAgencyJobInfoDTO createJobSharingToAgencyInfo(Long jobId, SharedToAgencyJobInfoDTO sharedToAgencyJobInfoDTO);

    List<AgencyStatsVO> shareJobToAgencies(Long jobId, List<Long> agencyIds);

    void stopShareJobToAgencies(Long jobId, List<Long> agencyIds);

    void stopShareJobToAllAgencies(Long jobId);

    TalentRecruitmentProcessVO submitToJob(TalentRecruitmentProcessSubmitToJobVO submitToJobVO);

    ApplicationIdAndStatusListVO getAllApplications(ApplicationSearchDTO applicationSearchDTO, Pageable pageable);

    LinkedHashMap<String, Integer> getMyApplicationCandidates(Instant startTime, Instant endTime);

    Boolean checkIfHasNewCandidate();

    void cacheNewApplicationToRedis(AgencySubmitApplication result);

    TalentDTOV3 updateTalentInfo(Long agencyId, Long jobId, Long duplicateTalentId, TalentInfoInputWithId input);

    void refreshTalentResumeParseResult(List<TalentResumeDTO> resumes);

}
