package com.altomni.apn.agency.service.data;

import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.search.SearchGroup;
import org.springframework.data.domain.Pageable;

import java.io.IOException;

public interface DataService {

    HttpResponse searchFromDataService(SearchGroup searchGroup, Pageable pageable) throws IOException;

    HttpResponse getAllCityNames() throws IOException;

}
