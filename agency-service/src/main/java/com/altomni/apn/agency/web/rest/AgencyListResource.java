package com.altomni.apn.agency.web.rest;

import com.altomni.apn.agency.service.agency.AgencyService;
import com.altomni.apn.agency.web.rest.vm.AgencyBriefVO;
import com.altomni.apn.agency.web.rest.vm.AgencyVO;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class AgencyListResource {

    @Resource
    private AgencyService agencyService;

    @GetMapping("/all/old")
    public ResponseEntity<List<AgencyVO>> getAllAgencies(@RequestParam(required = false) String search, Pageable pageable) {
        log.info("[APN: Agency @{}] REST request to get all agencies", SecurityUtils.getUserId());
        Page<AgencyVO> agencyList = agencyService.getAgencies(search, pageable);

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(agencyList, "/api/v3/all");
        return ResponseEntity.ok().headers(headers).body(agencyList.getContent());
    }

    @GetMapping("/all")
    public ResponseEntity<List<AgencyVO>> getAllAgenciesFromEs(@RequestParam(required = false) String search, Pageable pageable) {
        log.info("[APN: Agency @{}] REST request to get all agencies", SecurityUtils.getUserId());
        Page<AgencyVO> agencyList = agencyService.getAgenciesFromEs(search, pageable);

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(agencyList, "/api/v3/all");
        return ResponseEntity.ok().headers(headers).body(agencyList.getContent());
    }

    @GetMapping("/all/brief-list")
    public ResponseEntity<List<AgencyBriefVO>> getAllAgenciesBrief() {
        log.info("[APN: Agency @{}] REST request to get all agency brief list", SecurityUtils.getUserId());
        List<AgencyBriefVO> agencyBriefList = agencyService.getAllAgencyBriefs();

//        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(agencyBriefList, "/api/v3/all/brief-list");
//        return ResponseEntity.ok().headers(headers).body(agencyBriefList.getContent());
        return ResponseEntity.ok().body(agencyBriefList);
    }

}
