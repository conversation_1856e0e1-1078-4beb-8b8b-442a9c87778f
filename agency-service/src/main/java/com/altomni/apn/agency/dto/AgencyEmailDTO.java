package com.altomni.apn.agency.dto;

import com.altomni.apn.common.dto.mail.MailVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class AgencyEmailDTO extends MailVM implements Serializable {

    @ApiModelProperty(value = "agencyId for record")
    private Long agencyId;

}
