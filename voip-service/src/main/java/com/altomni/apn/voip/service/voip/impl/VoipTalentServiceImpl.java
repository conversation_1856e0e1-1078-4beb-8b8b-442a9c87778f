package com.altomni.apn.voip.service.voip.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.dto.search.ConditionParam;
import com.altomni.apn.common.dto.search.Relation;
import com.altomni.apn.common.dto.search.SearchConditionDTO;
import com.altomni.apn.common.dto.search.SearchParam;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.voip.service.application.ApplicationService;
import com.altomni.apn.voip.service.job.JobService;
import com.altomni.apn.voip.service.vo.voiptalent.VoipTalentRelatedJobVO;
import com.altomni.apn.voip.service.voip.VoipTalentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Service
@Transactional
public class VoipTalentServiceImpl implements VoipTalentService {

    private ApplicationService applicationService;

    private JobService jobService;

    public VoipTalentServiceImpl(ApplicationService applicationService, JobService jobService) {
        this.applicationService = applicationService;
        this.jobService = jobService;
    }

    @Override
    public List<VoipTalentRelatedJobVO> findTalentRelatedJobs(Long talentId) throws Throwable {
        List<VoipTalentRelatedJobVO> relatedJobs = new ArrayList<>();
        //get talent application process job first
        List<TalentRecruitmentProcessVO> processes = applicationService.getTalentRecruitmentProcessBriefByTalentId(talentId).getBody();
        relatedJobs.addAll(processes.stream().map(process -> new VoipTalentRelatedJobVO(process.getJobId(), null, true, process.getLastModifiedDate()))
                .sorted(Comparator.comparing(VoipTalentRelatedJobVO::getLastModifiedDate, Comparator.reverseOrder())).limit(10).toList());
        List<Long> processJobIds = processes.stream().map(TalentRecruitmentProcessVO::getJobId).toList();
        Map<Long, String> briefJobMap = jobService.getBriefJobListByIds(processJobIds).getBody().stream().collect(Collectors.toMap(JobBriefDTO::getId, JobBriefDTO::getTitle));
        relatedJobs.forEach(job -> job.setJobTitle(briefJobMap.get(job.getJobId())));
        relatedJobs = new ArrayList<>(relatedJobs.stream().filter(voipTalentRelatedJobVO -> voipTalentRelatedJobVO.getJobTitle() != null).toList());
        Set<Long> talentRecruitmentJobIds = relatedJobs.stream().map(VoipTalentRelatedJobVO::getJobId).collect(Collectors.toSet());
        //get user related job
        SearchParam searchParam = new SearchParam();
        searchParam.setRelation(Relation.OR);
        JSONObject paramVal1 = new JSONObject();
        paramVal1.put("data", List.of(SecurityUtils.getUserId()));
        paramVal1.put("relation", Relation.OR);
        ConditionParam param1 = new ConditionParam("createdBy", paramVal1, null);
        JSONObject paramVal2 = new JSONObject();
        paramVal2.put("data", List.of(SecurityUtils.getUserId()));
        paramVal1.put("relation", Relation.OR);
        ConditionParam param2 = new ConditionParam("assignedUser", paramVal2, null);
        searchParam.setCondition(List.of(param1, param2));
        SearchParam filterParam = new SearchParam();
        filterParam.setRelation(Relation.AND);
        filterParam.setCondition(new ArrayList<>());
        SearchConditionDTO searchConditionDTO = new SearchConditionDTO();
        searchConditionDTO.setSearch(List.of(searchParam));
        searchConditionDTO.setFilter(List.of(filterParam));
        searchConditionDTO.setLanguage(LanguageEnum.EN);
        searchConditionDTO.setModule(ModuleType.JOB);
        searchConditionDTO.setTimezone("America/Los_Angeles");
        Pageable pageable = PageRequest.of(1, 10, Sort.by("lastModifiedDate").descending());
        String res = jobService.searchJob(searchConditionDTO, pageable).getBody();
        //get user related private job
        SearchConditionDTO privateJobSearchCondition = new SearchConditionDTO();
        privateJobSearchCondition.setSearch(List.of(searchParam));
        privateJobSearchCondition.setFilter(List.of(filterParam));
        privateJobSearchCondition.setLanguage(LanguageEnum.EN);
        privateJobSearchCondition.setModule(ModuleType.PRIVATE_JOB);
        privateJobSearchCondition.setTimezone("America/Los_Angeles");
        String privateJobRes = jobService.searchJob(privateJobSearchCondition, pageable).getBody();
        JSONArray jsonArray = JSONUtil.parseArray(res);
        JSONArray privateJobJsonArray = JSONUtil.parseArray(privateJobRes);
        //merge two json array
        List<JSONObject> sortedList = Stream.concat(jsonArray.stream(), privateJobJsonArray.stream())
                .map(JSONObject.class::cast)
                .sorted((o1, o2) -> {
                    JSONObject source1 = o1.getJSONObject("_source");
                    JSONObject source2 = o2.getJSONObject("_source");
                    // 如果 _source 为空，排在最后
                    if (source1 == null && source2 == null) return 0;
                    if (source1 == null) return 1;
                    if (source2 == null) return -1;
                    String date1 = source1.getStr("lastModifiedDate");
                    String date2 = source2.getStr("lastModifiedDate");
                    // 如果 lastModifiedDate 为空，排在最后
                    if (date1 == null && date2 == null) return 0;
                    if (date1 == null) return 1;
                    if (date2 == null) return -1;
                    return date2.compareTo(date1); // 按 lastModifiedDate 倒序
                })
                .toList();
        // 转回 JSONArray
        JSONArray mergedSortedJsonArray = new JSONArray(sortedList);

        for(int i = 0 ; relatedJobs.size() <= 10 && i < mergedSortedJsonArray.size(); i++) {
            VoipTalentRelatedJobVO voipTalentRelatedJobVO = new VoipTalentRelatedJobVO();
            JSONObject jsonObject = (JSONObject) mergedSortedJsonArray.get(i);
            if(talentRecruitmentJobIds.contains(jsonObject.getLong("_id"))) continue;
            voipTalentRelatedJobVO.setJobId(jsonObject.getLong("_id"));
            voipTalentRelatedJobVO.setJobTitle(jsonObject.getJSONObject("_source").getStr("title"));
            voipTalentRelatedJobVO.setIsApplicationJob(false);
            Instant lastModifiedDate = Instant.parse(jsonObject.getJSONObject("_source").getStr("lastModifiedDate"));
            voipTalentRelatedJobVO.setLastModifiedDate(lastModifiedDate);
            relatedJobs.add(voipTalentRelatedJobVO);
        }
        return relatedJobs;
    }


}
