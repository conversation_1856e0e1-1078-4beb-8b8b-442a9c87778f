package com.altomni.apn.voip.service.vo.voiptalent;

import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModel;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModelConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoipTalentContactVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long talentId;

    private String phoneNumber;

    private String extNumber;

    private Integer totalEmailSent;

    @Convert(converter = TranscriptionModelConverter.class)
    private TranscriptionModel transcriptionModel;

}
