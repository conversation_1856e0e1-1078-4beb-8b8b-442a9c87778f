package com.altomni.apn.voip.service.dto.openai;

import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TranscriptionSummaryDTO implements Serializable {

    @NotNull
    private String phoneCallId;

    @NotNull
    private TranscriptionModel transcriptionModel;

}
