package com.altomni.apn.voip.service.lark.impl;

import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.voip.config.env.ApplicationProperties;
import com.altomni.apn.voip.service.lark.LarkMessageNotificationService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class LarkMessageNotificationServiceImpl implements LarkMessageNotificationService {

    private final ApplicationProperties properties;

    private String token;

    private final OkHttpClient client;

    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");


    public LarkMessageNotificationServiceImpl(ApplicationProperties properties) throws IOException {
        this.properties = properties;
        this.client = new OkHttpClient().newBuilder().build();
        this.getToken();
    }

    private String escapeJsonString(String jsonString) {
        StringBuilder escapedString = new StringBuilder();
        escapedString.append("\""); // 添加起始的双引号

        for (char c : jsonString.toCharArray()) {
            switch (c) {
                case '"':
                    escapedString.append("\\\""); // 转义双引号
                    break;
                case '\\':
                    escapedString.append("\\\\"); // 转义反斜杠
                    break;
                case '\n':
                    escapedString.append("\\n"); // 转义换行符
                    break;
                case '\r':
                    escapedString.append("\\r"); // 转义回车符
                    break;
                case '\t':
                    escapedString.append("\\t"); // 转义制表符
                    break;
                default:
                    escapedString.append(c); // 其他字符直接添加
            }
        }

        escapedString.append("\""); // 添加结束的双引号
        return escapedString.toString();
    }

    @Scheduled(fixedRate = 6000000)
    protected void getToken() throws IOException, JSONException {
        JSONObject json = new JSONObject();
        json.put("app_id", properties.getLarkSendMessageServiceAppId());
        json.put("app_secret", properties.getLarkSendMessageServiceAppSecret());
        RequestBody requestBody = RequestBody.create(json.toString(), JSON_TYPE);
        Request request = new Request.Builder()
                .url(properties.getLarkAuthenticationEndpoint())
                .post(requestBody)
                .build();
        try (Response response = this.client.newCall(request).execute()) {
            if (!response.isSuccessful()) log.error("lark service retrieve token error: {}", response);
            if(response.body() == null) log.error("lark service retrieve token error. The Get Token return null");
            JSONObject jsonResponse = new JSONObject(response.body().string());
            this.token = jsonResponse.getStr("tenant_access_token");
        }
        catch (IOException e) {
            log.error("lark service retrieve token error, stack: {}", ExceptionUtils.getStackTrace(e));
        }
    }

    @Override
    public void sendLarkMessage(String content, String email) {
        try {
            JSONObject requestBodyJson = new JSONObject();
            JSONObject text = new JSONObject();
            text.put("text", content);

            requestBodyJson.put("receive_id", email);
            requestBodyJson.put("msg_type", "text");
            requestBodyJson.put("content", text.toString());
            RequestBody requestBody = RequestBody.create(requestBodyJson.toString(), JSON_TYPE);

            Request request = new Request.Builder()
                    .url(properties.getLarkSendMessageEndpoint() + "?receive_id_type=email")
                    .header("Authorization", "Bearer " + token)
                    .post(requestBody)
                    .build();
            log.info("send lark message for voice mail");
            try (Response response = this.client.newCall(request).execute()) {
                if (!response.isSuccessful()) log.error("lark service send message error: {}", response);
//            JSONObject jsonResponse = new JSONObject(response.body().string());
            }
            catch (Exception e) {
                log.error("lark service send message error, stack: {}", ExceptionUtils.getStackTrace(e));
            }
        }
        catch (Exception e) {
            log.error("lark service send message error, stack: {}", ExceptionUtils.getStackTrace(e));
        }

    }






}
