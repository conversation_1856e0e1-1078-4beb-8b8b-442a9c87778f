package com.altomni.apn.voip.web.rest.voip;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.voip.service.vo.voiptalent.VoipTalentRelatedJobVO;
import com.altomni.apn.voip.service.voip.VoipTalentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * REST controller for managing voip.
 */
@RestController
@RequestMapping("/api/v3")
public class VoipTalentResource {

    private final Logger log = LoggerFactory.getLogger(VoipTalentResource.class);

    private static final String ENTITY_NAME = "voipTalent";

    private final VoipTalentService voipTalentService;

    public VoipTalentResource(VoipTalentService voipTalentService) {
       this.voipTalentService = voipTalentService;
    }

    @NoRepeatSubmit
    @GetMapping("/voip-talent-relate-jobs")
    public ResponseEntity<List<VoipTalentRelatedJobVO>> getVoipTalentRelatedJobs(@RequestParam("talentId") Long talentId) throws Throwable {
        log.info("[APN: Voip @{}] REST request to get talent related jobs by talentId : {} .", SecurityUtils.getUserId(), talentId);
        List<VoipTalentRelatedJobVO> talentRelatedJobs = voipTalentService.findTalentRelatedJobs(talentId);
        return ResponseEntity.ok().body(talentRelatedJobs);
    }
}
