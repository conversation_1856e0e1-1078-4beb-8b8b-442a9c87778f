package com.altomni.apn.voip.service.dto.amazonconnect;

import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModel;
import com.altomni.apn.voip.domain.enumeration.CallPlatform;
import com.altomni.apn.voip.domain.enumeration.CallPlatformConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AmazonConnectVoipCallingStatusRequestDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String phoneCallId;

    @Convert(converter = CallPlatformConverter.class)
    private CallPlatform callPlatform;

    private Long userId;

    //only for sse display username
    private String userFullName;

    //only for recording user using voip call status
    private String userEmail;

    private Long tenantId;

    //for apn is talent id, crm is contact id
    private Long referenceCalleeId;

    //only for sse display referenceCalleeName
    private String referenceCalleeName;

    private TranscriptionModel transcriptionModel;

    Boolean isCalling;
}
