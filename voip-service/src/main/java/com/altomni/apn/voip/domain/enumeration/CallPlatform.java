package com.altomni.apn.voip.domain.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CallPlatform enumeration.
 */
public enum CallPlatform implements ConvertedEnum<Integer> {

    APN_WEB(1),
    APN_MOBILE_IOS(2),
    CRM_WEB(6),
    CRM_MOBILE_IOS(7);

    private final int dbValue;

    CallPlatform(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CallPlatform, Integer> resolver =
        new ReverseEnumResolver<>(CallPlatform.class, CallPlatform::toDbValue);

    public static CallPlatform fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
