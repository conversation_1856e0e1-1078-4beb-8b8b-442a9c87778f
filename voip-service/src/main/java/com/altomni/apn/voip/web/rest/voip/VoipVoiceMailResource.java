package com.altomni.apn.voip.web.rest.voip;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.voip.service.vo.VoicemailNoteNewVO;
import com.altomni.apn.voip.service.voicemail.VoicemailNotificationService;
import com.altomni.apn.voip.web.rest.vm.voip.VoicemailNotificationRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.FileNotFoundException;

/**
 * REST controller for managing voicemail.
 */
@RestController
@RequestMapping("/api/v3")
public class VoipVoiceMailResource {

    private final Logger log = LoggerFactory.getLogger(VoipVoiceMailResource.class);

    private static final String ENTITY_NAME = "voipVoicemail";

    private final VoicemailNotificationService voicemailNotificationService;

    public VoipVoiceMailResource(VoicemailNotificationService voicemailNotificationService) {
        this.voicemailNotificationService = voicemailNotificationService;
    }

    @NoRepeatSubmit
    @GetMapping("/voice-mail/new/{talentId}")
    public ResponseEntity<VoicemailNoteNewVO> getVoicemailNoteNotificationStatus(@PathVariable Long talentId) {
        log.info("[APN: Voip @{}] REST request to get voicemail note notification status by talentId: {}", SecurityUtils.getUserId(), talentId);
        VoicemailNoteNewVO voicemailNoteNewVO = voicemailNotificationService.getVoicemailNoteNotification(talentId);
        return ResponseEntity.ok().body(voicemailNoteNewVO);
    }

    @NoRepeatSubmit
    @GetMapping("/voice-mail/recording/{phoneCallId}")
    public ResponseEntity<String> getVoiceMailRecording(@PathVariable String phoneCallId) {
        log.info("[APN: Voip @{}] REST request to get voice mail recording by phoneCallId: {}", SecurityUtils.getUserId(), phoneCallId);
        return voicemailNotificationService.getVoiceMailRecording(phoneCallId);
    }

    @NoRepeatSubmit
    @PostMapping("/voice-mail/check-valid-user")
    public ResponseEntity<String> checkValidUserId(@RequestBody VoicemailNotificationRequest request) {
        log.info("[APN: Voip @{}] REST request to check valid user : {}", SecurityUtils.getUserId(), request);
        voicemailNotificationService.checkValidUserIdForVoicemail(request);
        return ResponseEntity.ok().build();
    }
}
