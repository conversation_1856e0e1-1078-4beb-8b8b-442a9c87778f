package com.altomni.apn.voip.repository.phonetranscription;


import com.altomni.apn.common.domain.voip.PhoneTranscription;
import com.altomni.apn.common.domain.voip.Transcription;
import com.mongodb.client.result.UpdateResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Set;

@Component
public class PhoneTranscriptionRepository {

    @Autowired
    @Qualifier(value = "voipMongoTemplate")
    protected MongoTemplate mongoTemplate;

    public PhoneTranscription getPhoneTranscriptionByPhoneCallId(String phoneCallId) {
        Query query = new Query(Criteria.where("phoneCallId").is(phoneCallId));
        PhoneTranscription transcription = mongoTemplate.findOne(query, PhoneTranscription.class);
        if (transcription == null) {
            return null;
        }
        if (transcription.getTranscriptions() != null && !transcription.getTranscriptions().isEmpty()) {
            transcription.getTranscriptions().sort(Comparator.comparing(Transcription::getStartTime));
        }
        return transcription;
    }

    public List<PhoneTranscription> getPhoneTranscriptionsByPhoneCallIdsIn(Set<String> phoneCallIds) {
        Query query = new Query(Criteria.where("phoneCallId").in(phoneCallIds));
        List<PhoneTranscription> transcriptions = mongoTemplate.find(query, PhoneTranscription.class);
        return transcriptions;
    }

    /** only for data manipulate*/
    public List<PhoneTranscription> getPhoneTranscriptionsByTalentIdIsNull() {
        Query query = new Query(Criteria.where("talent_id").is(null));
        List<PhoneTranscription> transcriptions = mongoTemplate.find(query, PhoneTranscription.class);
        return transcriptions;
    }

    public List<PhoneTranscription> getAllPhoneTranscriptions() {
        Query query = new Query();
        List<PhoneTranscription> transcriptions = mongoTemplate.find(query, PhoneTranscription.class);
        return transcriptions;
    }


    public void saveOrUpdate(PhoneTranscription phoneTranscription) {
        if (phoneTranscription == null || phoneTranscription.getPhoneCallId() == null) {
            return;
        }
        mongoTemplate.save(phoneTranscription, "phone_transcription");
    }






}
