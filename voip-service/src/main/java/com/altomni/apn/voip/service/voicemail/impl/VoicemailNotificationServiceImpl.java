package com.altomni.apn.voip.service.voicemail.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.NotePriority;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteStatus;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.dto.message.MessageCreateWithVoicemailDTO;
import com.altomni.apn.common.dto.search.*;
import com.altomni.apn.common.dto.talent.CreateTalentNoteDTO;
import com.altomni.apn.common.dto.talent.CreateTalentVoiceMessageNoteDTO;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.voipserver.VoipProxyAPIHttp;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.voip.config.env.ApplicationProperties;
import com.altomni.apn.voip.domain.enumeration.CallPlatform;
import com.altomni.apn.voip.service.common.CommonService;
import com.altomni.apn.voip.service.dto.voicemail.WhisperVoicemailDTO;
import com.altomni.apn.voip.service.lark.LarkMessageNotificationService;
import com.altomni.apn.voip.service.talent.TalentService;
import com.altomni.apn.voip.service.user.UserService;
import com.altomni.apn.voip.service.vo.VoicemailNoteNewVO;
import com.altomni.apn.voip.service.voicemail.VoicemailNotificationService;
import com.altomni.apn.voip.web.rest.vm.voip.VoicemailNotificationRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import java.io.*;
import java.net.URISyntaxException;
import java.util.*;

import static com.altomni.apn.common.domain.enumeration.ContactType.*;
import static com.altomni.apn.common.domain.enumeration.talent.TalentNoteType.CALL_CANDIDATE_VOICE_MESSAGE;
import static com.altomni.apn.voip.utils.http.HttpUtils.formatEndpointPath;

@Slf4j
@Service
public class VoicemailNotificationServiceImpl implements VoicemailNotificationService {

    private final ApplicationProperties properties;

    private final LarkMessageNotificationService larkMessageNotificationService;

    private final UserService userService;

    private final TalentService talentService;

    private final CommonService commonService;

    private final VoipProxyAPIHttp voipProxyAPIHttp;

    private static final String CALL_NOTE = "callNote";

    private static final String VOICE_MAIL_RECORDING_GET = "/api/v1/voice-mail/recording/{}";


    public VoicemailNotificationServiceImpl(ApplicationProperties properties, LarkMessageNotificationService larkMessageNotificationService,
                                            UserService userService, TalentService talentService, CommonService commonService, VoipProxyAPIHttp voipProxyAPIHttp) {
        this.properties = properties;
        this.larkMessageNotificationService = larkMessageNotificationService;
        this.userService = userService;
        this.talentService = talentService;
        this.commonService = commonService;
        this.voipProxyAPIHttp = voipProxyAPIHttp;
    }

    private void sendLarkNotification(String content, UserBriefDTO userBriefDTO) {
        LoginUtil.simulateLoginWithClient();
        log.info("send lark voicemail notification to user: {}", userBriefDTO);
        try {
            if(userBriefDTO == null || userBriefDTO.getEmail().isBlank()) log.error("The user: {} cannot be found when send lark voicemail notification.", userBriefDTO);
            else larkMessageNotificationService.sendLarkMessage(content, userBriefDTO.getEmail());
        }
        catch (Exception e) {
            log.error("send lark voicemail notification to user: {} error. Error message: {}, stackTrace: {}", userBriefDTO, e.getMessage(), e.getStackTrace());
        }
    }


    private CallPlatform extractCallPlatformFromExtNumber(Long userId) {
        String extNumber = userId.toString();
        return CallPlatform.fromDbValue(Character.getNumericValue(extNumber.charAt(0)));
    }

    private Long extractUserIdFromExtNumber(Long userId) {
        String extNumber = userId.toString();
        return Long.valueOf(extNumber.substring(1));
    }

    @Override
    public void getWhisperVoicemail(WhisperVoicemailDTO whisperVoicemailDTO) throws URISyntaxException, IOException {
        log.info("process whisper voicemail message notification: {}", whisperVoicemailDTO);
        CallPlatform callPlatform = extractCallPlatformFromExtNumber(whisperVoicemailDTO.getUserId());
        if(!callPlatform.equals(CallPlatform.APN_WEB) && !callPlatform.equals(CallPlatform.APN_MOBILE_IOS)) {
            log.info("The voicemail call platform is not from APN, the call platform is: {}", callPlatform);
            return;
        }
        LoginUtil.simulateLoginWithClient();
        Long userId = extractUserIdFromExtNumber(whisperVoicemailDTO.getUserId());
        String phone = whisperVoicemailDTO.getPhoneNumber();
        String voicemailMessage = whisperVoicemailDTO.getVoicemailMessage();
        String phoneCallId = whisperVoicemailDTO.getVoipCallId();
        UserBriefDTO userBriefDTO = userService.findById(userId).getBody();
        TalentDTOV3 talent = null;
        if(userBriefDTO.getTenantId() != null) talent = findTalentByPhone(phone, userBriefDTO.getTenantId());
        String candidate = "Candidate: " + (talent == null ? "Unknown" : talent.getFullName());
        String phoneNumber = "Phone: " + phone;
        String transcribedVoicemail = "Transcribed voicemail: " + voicemailMessage;
        String link = null;
        String linkUrl = null;
        Long userPrimaryTeamId = userService.getPrimaryTeamIdByUserId(userId).getBody();
        if(talent != null) {
            Map<String, Object> additionalInfo = new HashMap<>();
            additionalInfo.put("isAudio", true);
            additionalInfo.put("phoneCallId", phoneCallId);
            additionalInfo.put("voiceMessage", voicemailMessage);
            CreateTalentVoiceMessageNoteDTO createTalentVoiceMessageNoteDTO = new CreateTalentVoiceMessageNoteDTO();
            createTalentVoiceMessageNoteDTO.setTalentId(talent.getId());
            createTalentVoiceMessageNoteDTO.setNote("");
            createTalentVoiceMessageNoteDTO.setNoteType(CALL_CANDIDATE_VOICE_MESSAGE);
            createTalentVoiceMessageNoteDTO.setPriority(NotePriority.NORMAL);
            createTalentVoiceMessageNoteDTO.setVisible(true);
            createTalentVoiceMessageNoteDTO.setUserId(userId);
            createTalentVoiceMessageNoteDTO.setAdditionalInfo(additionalInfo);
            createTalentVoiceMessageNoteDTO.setReadStatus(false);
            createTalentVoiceMessageNoteDTO.setIsSystem(true);
            createTalentVoiceMessageNoteDTO.setPermissionTeamId(userPrimaryTeamId);
            createTalentVoiceMessageNoteDTO.setPermissionUserId(userId);
            createTalentVoiceMessageNoteDTO.setCreatedBy(userBriefDTO.getId() + "," + userBriefDTO.getTenantId());
            createTalentVoiceMessageNoteDTO.setLastModifiedBy(null);
            createTalentVoiceMessageNoteDTO.setLastModifiedDate(null);
            talentService.createTalentVoiceMessageNote(createTalentVoiceMessageNoteDTO);
            linkUrl = properties.getApnHostUrl() + "/candidates/detail/" + talent.getId() + "?from=allCandidates&noteType=CALL_CANDIDATE_VOICE_MESSAGE";
        }
        link = (linkUrl != null && !StringUtils.isBlank(linkUrl)) ? "[Check the message details](" + linkUrl + ")" : "Check the message details";
        String notification  = candidate + "\n" + phoneNumber + "\n" + transcribedVoicemail + "\n" + link;
        sendLarkNotification(notification, userBriefDTO);
        if(talent != null) {
            MessageCreateWithVoicemailDTO messageCreateWithVoicemailDTO = new MessageCreateWithVoicemailDTO(userBriefDTO, talent.getFullName(), talent.getId(), userBriefDTO.getTenantId());
            commonService.createMessageWithVoicemail(messageCreateWithVoicemailDTO);
        }
    }

    /**
     * Find the talent by phone number
     * @param phoneNumber
     * @param tenantId
     * @return
     */

    private TalentDTOV3 findTalentByPhone(String phoneNumber, Long tenantId) {
        TalentDTOV3 talentDTOV3 = new TalentDTOV3();
        talentDTOV3.setTenantId(tenantId);
        talentDTOV3.setContacts(List.of(new TalentContactDTO(PHONE, phoneNumber), new TalentContactDTO(CELL_PHONE, phoneNumber), new TalentContactDTO(HOME_PHONE, phoneNumber), new TalentContactDTO(PRIMARY_PHONE, phoneNumber), new TalentContactDTO(WORK_PHONE, phoneNumber)));
        try {
            ResponseEntity<List<TalentDTOV3>> res = talentService.searchTalentsByContact(talentDTOV3);
            if(res.getStatusCode().value() < 400) {
                List<TalentDTOV3> talents = res.getBody();
                if(talents != null && !talents.isEmpty()) {
                    talents.sort(Comparator.comparing(TalentDTOV3::getCreatedBy).reversed());
                    return talents.get(0);
                }
                else log.info("voip voice mail service retrieve none talent by phone number: {}", phoneNumber);
            }
            else log.error("voip voice mail service retrieve talent by phone number error, status code: {}, error message: {}, phoneNumber: {}", res.getStatusCode(), res.getBody(), phoneNumber);
        }
        catch (Exception e) {
            log.error("voip voice mail service retrieve talent by phone number error, error message: {}, error stackTrace: {}", e.getMessage(), e.getStackTrace());
        }
        return null;
    }

    private List<TalentContactDTO> findTalentContact(String contact) {
        try {
            List<ContactType> types = Arrays.asList(PHONE, PRIMARY_PHONE, HOME_PHONE, CELL_PHONE, WORK_PHONE);
            ResponseEntity<List<TalentContactDTO>> res = talentService.findTalentContactByContactAndTypes(contact, types);
            if(res.getStatusCode().value() < 400) {
                List<TalentContactDTO> contacts = res.getBody();
                return contacts;
            }
            else log.error("voip voice mail service retrieve talent contact error, status code: {}, error message: {}", res.getStatusCode(), res.getBody());
        }
        catch (Exception e) {
            log.error("voip voice mail service retrieve talent contact error, error message: {}, error stackTrace: {}", e.getMessage(), e.getStackTrace());
        }
        return null;
    }


    @Override
    public VoicemailNoteNewVO getVoicemailNoteNotification(Long talentId) {
        List<Map<String, Object>> talentNotes = talentService.getTalentNoteByType(talentId, List.of(CALL_NOTE), CALL_CANDIDATE_VOICE_MESSAGE.toString()).getBody();
        Boolean isNew = !(talentNotes.stream().map(note -> (Boolean) note.get("readStatus")).filter(Objects::nonNull).reduce(true, (a, b) -> a && b));
        VoicemailNoteNewVO voicemailNoteNewVO = new VoicemailNoteNewVO(isNew);
        return voicemailNoteNewVO;
    }

    @Override
    public ResponseEntity<String> getVoiceMailRecording(String phoneCallId) {
        return voipProxyAPIHttp.proxyApiRequest(formatEndpointPath(VOICE_MAIL_RECORDING_GET, phoneCallId), null, HttpMethod.GET, String.class);
    }

    @Override
    public void checkValidUserIdForVoicemail(VoicemailNotificationRequest request) {
        LoginUtil.simulateLoginWithClient();
        Long extNumber = request.getUserId();
        if(extNumber == null) {
            log.error("voip voicemail service retrieve user error with userId is none");
            throw new CustomParameterizedException("voip voicemail service retrieve user error with userId is none.");
        }
        CallPlatform callPlatform = extractCallPlatformFromExtNumber(extNumber);
        if(!callPlatform.equals(CallPlatform.APN_WEB) && !callPlatform.equals(CallPlatform.APN_MOBILE_IOS)) {
            log.info("The voicemail call platform is not from APN, the call platform is: {}", callPlatform);
            return;
        }
        Long userId = extractUserIdFromExtNumber(extNumber);
        UserBriefDTO userBriefDTO = userService.findById(userId).getBody();
        if(userBriefDTO == null) {
            log.error("voip voicemail service retrieve user error with userId: {}", userId.toString());
            throw new CustomParameterizedException("voip voicemail service retrieve user error with userId: {}", userId.toString());
        }
        else {
            List<TalentContactDTO> contacts = findTalentContact(request.getPhone());
            if(contacts != null && !contacts.isEmpty()) {
                TalentDTOV3 talent = findTalentByPhone(request.getPhone(), userBriefDTO.getTenantId());
                if(talent == null) {
                    log.error("voip voicemail service retrieve user error with userId: {}, and with talent phone number: {}.", userId.toString(), request.getPhone());
                    throw new CustomParameterizedException("voip voicemail service retrieve user error with userId: {}, and with talent phone number: {}.", userId.toString(), request.getPhone());
                }
            }
        }
    }

}
