package com.altomni.apn.voip.config.rabbit;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.voip.domain.amazonconnect.AmazonConnectAccountMessage;
import com.altomni.apn.voip.service.amazonconnect.AmazonConnectService;
import com.altomni.apn.voip.service.dto.amazonconnect.AmazonConnectAccountDTO;
import com.altomni.apn.voip.service.dto.amazonconnect.AmazonConnectRoutingProfileDTO;
import com.altomni.apn.voip.service.dto.amazonconnect.AmazonConnectSecurityProfileDTO;
import com.altomni.apn.voip.service.dto.voicemail.WhisperVoicemailDTO;
import com.altomni.apn.voip.service.user.UserService;
import com.altomni.apn.voip.service.voicemail.VoicemailNotificationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Component
public class WhisperVoicemailRabbit implements RabbitTemplate.ConfirmCallback {

    @Resource
    private VoicemailNotificationService voicemailNotificationService;

    @Resource(name = "whisperVoicemailRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource(name = "whisperVoicemailRabbitAdmin")
    private RabbitAdmin whisperVoicemailRabbitAdmin;

    @Value("${application.whisperVoicemail.whisperVoicemailExchange}")
    private String exchange;

    @Value("${application.whisperVoicemail.whisperVoicemailQueue}")
    private String queue;

    @Value("${application.whisperVoicemail.whisperVoicemailRoutingKey}")
    private String routingKey;

    @PostConstruct
    public void init() {
        //specify confirmcallback
        rabbitTemplate.setConfirmCallback(this);

        DirectExchange directExchange = new DirectExchange(exchange);
        whisperVoicemailRabbitAdmin.declareExchange(directExchange);

        Queue voipQueue = new Queue(queue, true);
        whisperVoicemailRabbitAdmin.declareQueue(voipQueue);

        //绑定队列及交换机
        whisperVoicemailRabbitAdmin.declareBinding(BindingBuilder.bind(voipQueue).to(directExchange).with(routingKey));
    }

    @RabbitListener(containerFactory = "whisperVoicemailFactory", queues = {"${application.whisperVoicemail.whisperVoicemailQueue}"})
    @RabbitHandler
    public void process(Message message) {
        if(message.getBody() != null) {
            String json = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("[APN] voip voicemail: " + json);
            try {
                JSONObject jsonObject = JSONUtil.parseObj(json);
                if((!jsonObject.containsKey("voipCallId")) || (!jsonObject.containsKey("phoneNumber"))) {
                    log.error("[APN] voip voicemail missing phoneCallId/phoneNumber info in the message");
                    return;
                }
                WhisperVoicemailDTO whisperVoicemailDTO = JSONUtil.toBean(jsonObject, WhisperVoicemailDTO.class);
                try {
                    voicemailNotificationService.getWhisperVoicemail(whisperVoicemailDTO);
                } catch (Exception e) {
                    log.error("[APN] voip voicemail handle whisper voicemail message error, error message：" + e.getMessage());
                }
            }
            catch (Exception e) {
                log.error("[APN] voip voicemail process whisper voicemail message error, error message：" + e.getMessage());
            }
        }
        else log.error("[APN] voip voicemail get empty mq message.");
    }



    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String cause) {
        if (ack) {
            log.info("send message to rabbit success");
        } else {
            log.error("send message to rabbit error, error message = [{}]", cause);
        }
    }

}
