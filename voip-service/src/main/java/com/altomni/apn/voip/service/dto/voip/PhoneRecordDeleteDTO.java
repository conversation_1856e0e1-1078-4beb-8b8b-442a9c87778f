package com.altomni.apn.voip.service.dto.voip;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PhoneRecordDeleteDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    private String phoneCallId;

    @NotNull
    private Boolean isRecordDelete = false;

}
