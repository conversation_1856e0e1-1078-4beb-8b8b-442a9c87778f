package com.altomni.apn.voip.service.sse;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.http.HttpService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * sse 的服务客户端
 */

@Slf4j
@Component
@RefreshScope
public class VoipSseClient {

    @Resource
    private HttpService httpService;

    /**
     * sse 的推送消息接口
     */
    @Value("${sse.push.url:http://127.0.0.1:8001/api/sse/push}")
    private String ssePushUrl;

    @Value("${sse.access_token_value:123456789}")
    private String sseDefaultTokenValue;

    @Value("${sse.access_token_key:from}")
    private String sseDefaultTokenKey;

    @Value("${sse.skip:true}")
    private Boolean skip;

    /**
     * 通过sse服务推送消息，通过userId,tenantId 来确定推送消息对象，data是推送的具体数据
     * @param userId
     * @param tenantId
     * @param data
     */
    @Async
    public void pushSseMessage(Long userId, Long tenantId, Map<String, Object> data) {
        log.info("pushSseMessage skip = {}, userId = {}, tenantId = {}, data = {}", skip, userId, tenantId, data);
        if (skip) {
            return;
        }
        HttpResponse response = new HttpResponse();
        Map<String, Object> map = new HashMap<>(16);
        map.put("userId", userId);
        map.put("tenantId", tenantId);
        map.put("appName", "voip");
        map.put("data", JSONUtil.toJsonStr(data));
        try {
            response = httpService.post(ssePushUrl, getHeaders(), JSONUtil.toJsonStr(map));
            if (Objects.equals(response.getCode(), 200)) {
                log.info("[APN sse] push sse message is success, param = {}, response = {}", map, response.getBody());
            } else {
                log.info("[APN sse] push sse message is error, param = {}, response = {}", map, response.getBody());
            }
        } catch (Exception e) {
            log.info("[APN sse] push sse message is error, param = {}, response = {}", map, response.getBody());
        }
    }

    private Headers getHeaders() {
        Map<String, String> headerMap = new HashMap<>(16);
        headerMap.put(sseDefaultTokenKey, sseDefaultTokenValue);
        return Headers.of(headerMap);
    }

}
