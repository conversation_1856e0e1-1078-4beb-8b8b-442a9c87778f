package com.altomni.apn.voip.service.lark.impl;

import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.voip.config.env.ApplicationProperties;
import com.altomni.apn.voip.service.lark.LarkLiveCaptionService;
import okhttp3.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Base64;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class LarkLiveCaptionServiceImpl implements LarkLiveCaptionService {

    private Logger log = LoggerFactory.getLogger(LarkLiveCaptionServiceImpl.class);

    private final ApplicationProperties properties;
    private String token;

    private AtomicInteger uniqueStreamCalls = new AtomicInteger(0);

    private Set<String> streamIds = ConcurrentHashMap.newKeySet();

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private final OkHttpClient client;

    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");


    public LarkLiveCaptionServiceImpl (ApplicationProperties properties) throws IOException, JSONException {
        this.properties = properties;
        this.client = new OkHttpClient().newBuilder().build();
        this.getToken();
        scheduler.scheduleAtFixedRate(() -> {
            uniqueStreamCalls.set(0);
            streamIds.clear();
        }, 0, 120, TimeUnit.SECONDS);
    }


    @Scheduled(fixedRate = 6000000)
    protected void getToken() throws IOException, JSONException {
        JSONObject json = new JSONObject();
        json.put("app_id", properties.getLarkStreamRecognitionServiceAppId());
        json.put("app_secret", properties.getLarkStreamRecognitionServiceAppSecret());
        RequestBody requestBody = RequestBody.create(json.toString(), JSON_TYPE);
        Request request = new Request.Builder()
                .url(properties.getLarkAuthenticationEndpoint())
                .post(requestBody)
                .build();
        try (Response response = this.client.newCall(request).execute()) {
            if (!response.isSuccessful()) log.error("lark service retrieve token error: {}", response);
            if(response.body() == null) log.error("lark service retrieve token error. The Get Token return null");
            JSONObject jsonResponse = new JSONObject(response.body().string());
            this.token = jsonResponse.getStr("tenant_access_token");
        }
        catch (IOException e) {
            log.error("lark service retrieve token error, stack: {}", ExceptionUtils.getStackTrace(e));
        }
    }

    @Override
    public String getLiveCaption(byte[] pcmData, String streamId, int sequenceId, Integer actionType) throws JSONException, IOException {
        if (streamIds.add(streamId)) uniqueStreamCalls.incrementAndGet();
        JSONObject speech = new JSONObject();
        speech.put("speech", Base64.getEncoder().encodeToString(pcmData));
        JSONObject config = new JSONObject();
        config.put("stream_id", streamId);
        config.put("sequence_id", sequenceId);
        config.put("action", actionType);
        config.put("format", "pcm");
        config.put("engine_type", "16k_auto");
        JSONObject requestBodyJson = new JSONObject();
        requestBodyJson.put("speech", speech);
        requestBodyJson.put("config", config);
        RequestBody requestBody = RequestBody.create(requestBodyJson.toString(), JSON_TYPE);
        Request request = new Request.Builder()
                .url(properties.getLarkStreamRecognitionEndpoint())
                .header("Authorization", "Bearer " + token)
                .post(requestBody)
                .build();
        try (Response response = this.client.newCall(request).execute()) {
            if (!response.isSuccessful()) log.error("lark service request live caption error: {}, message: {}", response, response.body().string());
            if (response.body() == null) {
                log.error("lark service request live caption error. The Get caption return null");
                return null;
            }
            String responseStr = response.body().string();
            JSONObject jsonResponse = new JSONObject(responseStr);
            JSONObject data = jsonResponse.getJSONObject("data");
            if(data == null) {
                return null;
            }
            else {
                if(data.getInt("code") != null && data.getInt("code") != 0) {
                    log.error("lark service request live caption error: {}", data.getStr("msg"));
                    throw new CustomParameterizedException("The live caption service reaches to the max capacity, currently unavailable.");
                }
                else return data.getStr("recognition_text");
            }
        } catch (IOException e) {
            log.error("lark service get live caption error, stack: {}", ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    @Override
    public int getCurrentUniqueStreamUsage() {
        log.info("current usage of lark service: {}", uniqueStreamCalls.get());
        return uniqueStreamCalls.get();
    }



}
