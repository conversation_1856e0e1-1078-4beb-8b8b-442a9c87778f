package com.altomni.apn.voip.service.application;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.net.URISyntaxException;
import java.util.List;

@Component
@FeignClient(value = "application-service")
public interface ApplicationService {

    @GetMapping("/application/api/v3/talent-recruitment-processes/talentId/{talentId}/jobId/{jobId}")
    ResponseEntity<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByTalentIdAndJobId(
            @ApiParam(value = "talent id", required = true) @PathVariable("talentId") Long talentId,
            @ApiParam(value = "job id", required = true) @PathVariable("jobId") Long jobId) throws URISyntaxException;


    @GetMapping("/application/api/v3/talent-recruitment-processes/{talentRecruitmentProcessId}/stages/{stageType}")
    ResponseEntity<Object> getStageByType(@PathVariable(name = "talentRecruitmentProcessId") Long talentRecruitmentProcessId, @PathVariable(name = "stageType") NodeType nodeType);

    @GetMapping("/application/api/v3/talent-all-recruitment-processes-brief/talentId/{talentId}")
    ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessBriefByTalentId(@PathVariable("talentId") Long talentId);

}
