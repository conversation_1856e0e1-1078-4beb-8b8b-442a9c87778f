package com.altomni.apn.voip.service.vo.openai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TranscriptionNoteVO {

    private List<TranscriptionTopicVO> notes = new ArrayList<>();

    private List<TranscriptionActionItemVO> actionItems = new ArrayList<>();

}
