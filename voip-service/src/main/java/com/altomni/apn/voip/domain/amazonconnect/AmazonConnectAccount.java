package com.altomni.apn.voip.domain.amazonconnect;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Entity
@Table(name = "amazon_connect_account")
public class AmazonConnectAccount extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "amazon_connect_account_id")
    private String accountId;

    @Column(name = "amazon_connect_instance_id")
    private String instanceId;

    @NotNull
    @Column(name = "user_id")
    private Long userId;

    @NotNull
    @Column(name = "tenant_id")
    private Long tenantId;

    @NotNull
    @Column(name = "email")
    private String email;

    @NotNull
    @Column(name = "security_profile_id")
    private String securityProfileId;

    @NotNull
    @Column(name = "routing_profile_id")
    private String routingProfileId;

}
