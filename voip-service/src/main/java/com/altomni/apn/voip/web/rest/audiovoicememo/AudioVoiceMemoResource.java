package com.altomni.apn.voip.web.rest.audiovoicememo;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.voip.service.alicloud.AliCloudASRService;
import com.altomni.apn.voip.service.audio.AudioProcess;
import com.altomni.apn.voip.web.rest.vm.voip.LiveCaptionRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import java.io.IOException;
import java.util.Base64;

import static com.altomni.apn.voip.service.audio.AudioProcess.pcmToWav;

/**
 * REST controller for managing Barcode.
 */
@RestController
@RequestMapping("/api/v3")
public class AudioVoiceMemoResource {

    private final Logger log = LoggerFactory.getLogger(AudioVoiceMemoResource.class);

    private static final String ENTITY_NAME = "lark";

    private final AliCloudASRService aliCloudASRService;

    public AudioVoiceMemoResource(AliCloudASRService aliCloudASRService) {
        this.aliCloudASRService = aliCloudASRService;
    }

    @NoRepeatSubmit
    @PostMapping("/audio/live-transcription")
    public ResponseEntity<Void> getLarkLiveTranscription(@RequestBody LiveCaptionRequest liveCaptionRequest) throws IOException {
        log.info("[APN: Voice Audio Memo @{}] REST request to get audio live caption by : {}", SecurityUtils.getUserId(), liveCaptionRequest);
        byte[] pcmRaw = Base64.getDecoder().decode(liveCaptionRequest.getPcmData());
        AudioInputStream audioStream = AudioProcess.byteArrayToAudioInputStream(pcmRaw, 48000, 16, 1, true, false);
        AudioFormat format16k = new AudioFormat(16000, 16, 1, true, false);
        AudioInputStream resampledStream = AudioProcess.audioResampling(audioStream, format16k);
        byte[] audioResampledBytes = AudioProcess.audioInputStreamToByteArray(resampledStream);
        aliCloudASRService.sendVoiceAudioToTranscribe(pcmToWav(audioResampledBytes,16000, 1, 16), liveCaptionRequest.getModel(), liveCaptionRequest.getIsEnd());
        return ResponseEntity.ok().build();
    }
}
