package com.altomni.apn.voip.service.dto.voip;

import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSpeakerType;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSpeakerTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoiceMemoDTO implements Serializable {

    private String streamId;

    private Integer sequenceId;

    private String content;

    private Boolean isEnd;
}
