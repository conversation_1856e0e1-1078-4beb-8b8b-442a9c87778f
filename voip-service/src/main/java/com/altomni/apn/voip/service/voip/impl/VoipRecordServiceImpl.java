package com.altomni.apn.voip.service.voip.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.TypeReference;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.TalentContactVerificationStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSpeakerType;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.voip.VoipContactWithTalentContact;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessInterviewVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOnboardVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.dto.talent.TalentOwnershipDTO;
import com.altomni.apn.common.dto.talent.UpdateTalentContactVerificationStatusDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.voip.VoipReportDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.enums.EnumVoipCallResultService;
import com.altomni.apn.common.service.enums.EnumVoipCallTypeService;
import com.altomni.apn.common.service.voipserver.VoipProxyAPIHttp;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import com.altomni.apn.common.vo.voip.VoipContactDetailVO;
import com.altomni.apn.voip.config.env.ApplicationProperties;
import com.altomni.apn.voip.domain.amazonconnect.AmazonConnectAccount;
import com.altomni.apn.common.domain.voip.PhoneTranscription;
import com.altomni.apn.common.domain.voip.VoipContact;
import com.altomni.apn.voip.domain.enumeration.CallPlatform;
import com.altomni.apn.voip.repository.amazonconnect.AmazonConnectAccountRepository;
import com.altomni.apn.voip.repository.phonetranscription.PhoneTranscriptionRepository;
import com.altomni.apn.voip.repository.voip.VoipContactRepository;
import com.altomni.apn.voip.service.alicloud.AliCloudASRService;
import com.altomni.apn.voip.service.application.ApplicationService;
import com.altomni.apn.voip.service.common.CommonService;
import com.altomni.apn.voip.service.dto.amazonconnect.AmazonConnectAccountDTO;
import com.altomni.apn.voip.service.dto.voip.PhoneRecordDeleteDTO;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.voip.service.dto.voip.PhoneTranscriptionDTO;
import com.altomni.apn.voip.service.mapper.voip.VoipContactMapper;
import com.altomni.apn.voip.service.talent.TalentService;
import com.altomni.apn.voip.service.user.UserService;
import com.altomni.apn.voip.service.vo.voiptalent.VoipTalentContactVO;
import com.altomni.apn.voip.service.voip.VoipRecordService;
import com.altomni.apn.voip.service.voip.VoipTranscriptionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.criteria.*;
import javax.transaction.Transactional;
import java.io.IOException;
import java.net.URISyntaxException;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import static com.altomni.apn.voip.constants.VoipCallType.*;
import static com.altomni.apn.voip.utils.http.HttpUtils.formatEndpointPath;

@Slf4j
@Service
@Transactional
public class VoipRecordServiceImpl implements VoipRecordService {

    private final ApplicationProperties properties;

    private final PhoneTranscriptionRepository phoneTranscriptionRepository;

    private final VoipContactRepository voipContactRepository;

    private final EntityManager em;

    private final VoipContactMapper voipContactMapper;

    private final TalentService talentService;

    private final CommonService commonService;

    private final ApplicationService applicationService;

    private final EnumVoipCallTypeService enumVoipCallTypeService;

    private final UserService userService;

    private final VoipProxyAPIHttp voipProxyAPIHttp;

    private static final String AMAZON_CONECT_ACCOUNT_GET = "/api/v1/amazon-connect/account/{}";

    public VoipRecordServiceImpl(ApplicationProperties properties, PhoneTranscriptionRepository phoneTranscriptionRepository,
                                 VoipContactRepository voipContactRepository, VoipContactMapper voipContactMapper,
                                 TalentService talentService, CommonService commonService, ApplicationService applicationService,
                                 EnumVoipCallTypeService enumVoipCallTypeService, UserService userService, VoipProxyAPIHttp voipProxyAPIHttp,
                                 EntityManager entityManager) {
        this.properties = properties;
        this.phoneTranscriptionRepository = phoneTranscriptionRepository;
        this.voipContactRepository = voipContactRepository;
        this.voipContactMapper = voipContactMapper;
        this.talentService = talentService;
        this.commonService = commonService;
        this.applicationService = applicationService;
        this.enumVoipCallTypeService = enumVoipCallTypeService;
        this.userService = userService;
        this.voipProxyAPIHttp = voipProxyAPIHttp;
        this.em = entityManager;
    }

    private static Instant toStartOfDayInstant(LocalDate date, String timeZone) {
        LocalDateTime startOfDay = date.atStartOfDay(); // 00:00:00
        return startOfDay.atZone(ZoneId.of(timeZone)).toInstant();
    }

    // 将 endTime 转换为当天 23:59:59 的 Instant
    private static Instant toEndOfDayInstant(LocalDate date, String timeZone) {
        LocalDateTime endOfDay = date.atTime(23, 59, 59); // 23:59:59
        return endOfDay.atZone(ZoneId.of(timeZone)).toInstant();
    }


    private Specification<VoipContact> searchVoipContact(VoipReportDTO voipReportDTO) {
        Specification<VoipContact> specification = (root, query, criteriaBuilder) -> {
            Predicate predicate = criteriaBuilder.conjunction();
            if(voipReportDTO.getStartTime() != null) {
                predicate.getExpressions().add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdDate"), toStartOfDayInstant(voipReportDTO.getStartTime(), voipReportDTO.getTimeZone())));
            }
            if(voipReportDTO.getEndTime() != null) {
                predicate.getExpressions().add(criteriaBuilder.lessThanOrEqualTo(root.get("createdDate"), toEndOfDayInstant(voipReportDTO.getEndTime(), voipReportDTO.getTimeZone())));
            }
            if(voipReportDTO.getUserIdList() != null || voipReportDTO.getTeamIdList() != null) {
                Set<Long> userIds = new HashSet<>();
                if(voipReportDTO.getUserIdList() != null) userIds.addAll(voipReportDTO.getUserIdList());
                if(voipReportDTO.getTeamIdList() != null) {
                    Set<Long> teamIds = new HashSet<>(voipReportDTO.getTeamIdList());
                    Set<Long> teamUserIds = userService.getAllTeamUserIdsByPermissionTeamIdIn(teamIds).getBody();
                    if(teamUserIds != null) userIds.addAll(teamUserIds);
                }
                CriteriaBuilder.In<Long> in = criteriaBuilder.in(root.get("userId"));
                userIds.forEach(in::value);
                predicate.getExpressions().add(in);
            }
            if(voipReportDTO.getCallResultList() != null && !voipReportDTO.getCallResultList().isEmpty()) {
                Set<Long> callResultIds = voipReportDTO.getCallResultList();
                CriteriaBuilder.In<Long> in = criteriaBuilder.in(root.get("callResultId"));
                callResultIds.forEach(in::value);
                predicate.getExpressions().add(in);
            }
            if(voipReportDTO.getCallTypeList() != null && !voipReportDTO.getCallTypeList().isEmpty()) {
                Set<Long> callTypeIds = voipReportDTO.getCallTypeList();
                CriteriaBuilder.In<Long> in = criteriaBuilder.in(root.get("callTypeId"));
                callTypeIds.forEach(in::value);
                predicate.getExpressions().add(in);
            }
            if(voipReportDTO.getTenantId() != null) {
                predicate.getExpressions().add(criteriaBuilder.equal(root.get("tenantId"), voipReportDTO.getTenantId()));
            }
            return predicate;
        };
        return specification;
    }

    @Override
    public VoipContactDTO recordVoipContact(VoipContactDTO voipContactDTO) {
        try {
            ResponseEntity<AmazonConnectAccountDTO> res = voipProxyAPIHttp.proxyApiRequest(formatEndpointPath(AMAZON_CONECT_ACCOUNT_GET, voipContactDTO.getUserId()), null, HttpMethod.GET, AmazonConnectAccountDTO.class);
            if(res.getBody() == null || res.getBody().getAccountInstance() == null || res.getBody().getAccountInstance().getInstanceId() == null || res.getBody().getAccountInstance().getInstanceId().isEmpty()) {
                throw new CustomParameterizedException("The user doesn't have a valid voip access.");
            }
            else {
                AmazonConnectAccountDTO amazonConnectAccountDTO = res.getBody();
                Optional<VoipContact> exist = voipContactRepository.findByPhoneCallId(voipContactDTO.getPhoneCallId());
                if(exist.isPresent()) throw new CustomParameterizedException("Cannot record one phone call twice!");
                VoipContact voipContact = voipContactMapper.toEntity(voipContactDTO);
                voipContact.setInstanceId(amazonConnectAccountDTO.getAccountInstance().getInstanceId());
                voipContact = voipContactRepository.save(voipContact);
                return voipContactMapper.toDto(voipContact);
            }
        }
        catch (CustomParameterizedException e) {
            log.error("Error recording voip contact: {}", e.getMessage());
            throw e;
        }
        catch (Exception e) {
            log.error("Error fetching user amazon connect account for user id : {}", voipContactDTO.getUserId(), e.getMessage());
            throw new CustomParameterizedException("Error fetching user's amazon connect account for userId: " + voipContactDTO.getUserId());
        }
    }

    @Override
    public VoipContactDTO updateVoipContactPhoneCallingStatus(VoipContactDTO voipContactDTO) {
        VoipContact voipContact = voipContactRepository.findByPhoneCallId(voipContactDTO.getPhoneCallId()).orElseThrow(() -> new CustomParameterizedException("The phone contact doesn't exist."));
        voipContact.setPhoneCallStatus(voipContactDTO.getPhoneCallStatus());
        voipContact = voipContactRepository.save(voipContact);
        return voipContactMapper.toDto(voipContact);
    }

    @Override
    public VoipContactDTO getVoipContact(String phoneCallId) {
        Optional<VoipContactDTO> voipContactDTOOptional = voipContactRepository.findByPhoneCallId(phoneCallId).map(voipContactMapper::toDto);
        return voipContactDTOOptional.orElse(null);
    }

    @Override
    public VoipContactDetailVO getVoipContactDetail(String phoneCallId) {
        VoipContactDTO voipContact = voipContactRepository.findByPhoneCallId(phoneCallId).map(voipContactMapper::toDto).orElse(null);
        PhoneTranscription phoneTranscription = phoneTranscriptionRepository.getPhoneTranscriptionByPhoneCallId(phoneCallId);
        VoipContactDetailVO recordDetail = new VoipContactDetailVO(voipContact.getId(), voipContact.getPhoneCallId(), voipContact.getUserId(), voipContact.getUserName(), voipContact.getTalentId(), voipContact.getTalentName(), phoneTranscription != null ? phoneTranscription.getPhone() : voipContact.getPhoneNumber(),
                voipContact.getTenantId(), voipContact.getCallTypeId(), voipContact.getCallResultId(), voipContact.getJobId());
        return recordDetail;
    }

    @Override
    public List<VoipContactDetailVO> getVoipContactDetails(Set<String> phoneCallIds) {
        List<VoipContactDTO> voipContacts = voipContactRepository.findAllByPhoneCallIdIn(phoneCallIds).stream().map(voipContactMapper::toDto).toList();
        Map<String, PhoneTranscription> phoneTranscriptions = phoneTranscriptionRepository.getPhoneTranscriptionsByPhoneCallIdsIn(phoneCallIds).stream().collect(Collectors.toMap(PhoneTranscription::getPhoneCallId, phoneTranscription -> phoneTranscription));
        return voipContacts.stream().map(voipContact ->
            new VoipContactDetailVO(voipContact.getId(), voipContact.getPhoneCallId(), voipContact.getUserId(), voipContact.getUserName(), voipContact.getTalentId(), voipContact.getTalentName(), phoneTranscriptions.get(voipContact.getPhoneCallId()) != null ? phoneTranscriptions.get(voipContact.getPhoneCallId()).getPhone() : voipContact.getPhoneNumber(),
                    voipContact.getTenantId(), voipContact.getCallTypeId(), voipContact.getCallResultId(), voipContact.getJobId())
        ).toList();
    }

    @Override
    public VoipContactDTO getLastVoipContact() {
        Long userId = SecurityUtils.getUserId();
        Optional<VoipContactDTO> voipContactDTOOptional = voipContactRepository.findFirstByUserIdOrderByCreatedDateDesc(userId).map(voipContactMapper::toDto);
        return voipContactDTOOptional.orElse(null);
    }

    @Override
    public VoipTalentContactVO getVoipTalentContactVO(Long talentId, CallPlatform callPlatform) throws IOException {
        VoipTalentContactVO contactVO = new VoipTalentContactVO();
        Long userId = SecurityUtils.getUserId();
        TalentOwnershipDTO talentOwnership = talentService.findTalentOwnerByTalentId(talentId).getBody();
        Optional<VoipContactDTO> voipContactDTOOptional = voipContactRepository.findFirstByUserIdOrderByCreatedDateDesc(userId).map(voipContactMapper::toDto);
        if(voipContactDTOOptional.isPresent()) {
            contactVO.setTranscriptionModel(voipContactDTOOptional.get().getTranscriptionModel());
        }
        contactVO.setPhoneNumber(properties.getVoiceMailPhoneNumber());
        contactVO.setExtNumber(callPlatform.toDbValue() + String.valueOf(talentOwnership != null ? talentOwnership.getUserId() : null));
        contactVO.setTalentId(talentId);
        String count = commonService.countVoipTalentNoticeEmail(talentId).getBody();
        if(count == null) log.error("voip talent contact get info error, count is null");
        contactVO.setTotalEmailSent(count != null ? Integer.valueOf(count) : 0);
        return contactVO;
    }


    @Override
    public VoipContactDTO updateVoipContactRecordDelete(PhoneRecordDeleteDTO phoneRecordDeleteDTO) {
        VoipContact voipContact = voipContactRepository.findByPhoneCallId(phoneRecordDeleteDTO.getPhoneCallId()).orElseThrow(() -> new CustomParameterizedException("The phone contact doesn't exist."));
        voipContact.setIsRecordDelete(phoneRecordDeleteDTO.getIsRecordDelete());
        voipContact = voipContactRepository.save(voipContact);
        return voipContactMapper.toDto(voipContact);
    }

    @Override
    public EnumDictVO getCallTypeByTalentAndJob(Long talentId, Long jobId, SortType sortType) throws URISyntaxException {
        String callType = null;
        //find correspond application with talent and job
        TalentRecruitmentProcessVO processVO = applicationService.getTalentRecruitmentProcessByTalentIdAndJobId(talentId, jobId).getBody();
        if(processVO == null) {
            log.info("Voip service cannot find process by talent id: {} and job id: {} .", talentId, jobId);
            return null;
        }
        ZonedDateTime threeMonthsAgo = Instant.now()
                .atZone(ZoneId.systemDefault())
                .minusMonths(3);

        ZonedDateTime lastModifiedDate = processVO.getLastModifiedDate()
                .atZone(ZoneId.systemDefault());

        if (lastModifiedDate.isBefore(threeMonthsAgo)) {
            callType = INITIAL_SCREENING;
        }
        else {
            if(processVO.getLastNodeStatus().equals(NodeStatus.ELIMINATED)) {
               return null;
            }
            else {
                NodeType nodeType = processVO.getLastNodeType();
                switch (nodeType) {
                    case SUBMIT_TO_JOB -> callType = JOB_COMMUNICATION;
                    case SUBMIT_TO_CLIENT -> callType = CANDIDATE_ENGAGEMENT;
                    case INTERVIEW -> {
                        Object res = applicationService.getStageByType(processVO.getId(), NodeType.INTERVIEW).getBody();
                        List<TalentRecruitmentProcessInterviewVO> interviewStages = Convert.convert(new TypeReference<List<TalentRecruitmentProcessInterviewVO>>() {}, res);
                        if(interviewStages != null && !interviewStages.isEmpty()) {
                            interviewStages.sort(Comparator.comparing(TalentRecruitmentProcessInterviewVO::getLastModifiedDate).reversed());
                            TalentRecruitmentProcessInterviewVO latestInterviewStage = interviewStages.get(0);
                            Instant currentTime = Instant.now();
                            if(!latestInterviewStage.getFromTime().isAfter(currentTime)) callType = INTERVIEW_SCHEDULING;
                            else callType = INTERVIEW_FEEDBACK;
                        }
                        else {
                            log.info("Voip service cannot find any available interview stage by application process id: {}", processVO.getId());
                            return null;
                        }
                    }
                    case OFFER -> callType = OFFER_DISCUSSION;
                    case ON_BOARD -> {
                        Object res = applicationService.getStageByType(processVO.getId(), NodeType.ON_BOARD).getBody();
                        TalentRecruitmentProcessOnboardVO onboard = Convert.convert(TalentRecruitmentProcessOnboardVO.class , res);
                        if(onboard != null) {
                            LocalDate today = LocalDate.now();
                            if(!onboard.getOnboardDate().isAfter(today)) callType = ONBOARDING_REMINDER;
                            else callType = ONBOARDING_FOLLOW_UP;
                        }
                        else {
                            log.info("Voip service cannot find any available onboarding info by application process id: {}", processVO.getId());
                            return null;
                        }
                    }
                }
            }
        }
        return enumVoipCallTypeService.findByNameAndSortType(callType, sortType);
    }

    @Override
    public void updateVoipContactRecord(VoipContactDTO voipContactDTO) {
        String phoneCallId = voipContactDTO.getPhoneCallId();
        VoipContact existContact = voipContactRepository.findByPhoneCallId(phoneCallId).orElseThrow(() -> new CustomParameterizedException("The contact with contact id: " + phoneCallId + " doesn't exist or is not valid."));
        existContact.setCallTypeId(voipContactDTO.getCallTypeId());
        existContact.setCallResultId(voipContactDTO.getCallResultId());
        UpdateTalentContactVerificationStatusDTO contactVerificationStatusDTO = new UpdateTalentContactVerificationStatusDTO();
        contactVerificationStatusDTO.setTalentId(existContact.getTalentId());
        contactVerificationStatusDTO.setContactInfo(existContact.getPhoneNumber());
        contactVerificationStatusDTO.setContactType(voipContactDTO.getContactType());
        if(voipContactDTO.getCallResultId().equals(8L)) {
            contactVerificationStatusDTO.setVerificationStatus(TalentContactVerificationStatus.WRONG_CONTACT);
        }
        else contactVerificationStatusDTO.setVerificationStatus(TalentContactVerificationStatus.VERIFIED);
        talentService.updateTalentContactVerificationStatus(contactVerificationStatusDTO);
        existContact.setJobId(voipContactDTO.getJobId());
        voipContactRepository.save(existContact);
    }

//    @Override
//    public List<VoipContactDTO> findAllByVoipReport(VoipReportDTO voipReportDTO) {
//        List<VoipContactDTO> voipContacts = voipContactMapper.toDto(voipContactRepository.findAll(searchVoipContact(voipReportDTO)));
//        return voipContacts;
//    }

    @Override
    public List<VoipContactDTO> findAllByVoipReport(VoipReportDTO dto) {
        CriteriaBuilder cb = em.getCriteriaBuilder();
        // 注意：CriteriaQuery 的泛型要与 DTO 对应
        CriteriaQuery<VoipContactDTO> cq =
                cb.createQuery(VoipContactDTO.class);

        Root<VoipContactWithTalentContact> root = cq.from(VoipContactWithTalentContact.class);
        // 左连接 TalentContact 并加上 ON 条件
        Join<VoipContactWithTalentContact, TalentContact> tcJoin =
                root.join("talentContact", JoinType.LEFT);
        tcJoin.on(cb.and(
                cb.equal(root.get("talentId"), tcJoin.get("talentId")),
                cb.equal(root.get("phoneNumber"), tcJoin.get("contact")),
                cb.equal(tcJoin.get("type"), ContactType.PHONE),
                cb.notEqual(tcJoin.get("verificationStatus"),TalentContactVerificationStatus.WRONG_CONTACT)
        ));

        // 动态构造 WHERE 条件
        List<Predicate> predicates = new ArrayList<>();
        if (dto.getStartTime() != null) {
            predicates.add(cb.greaterThanOrEqualTo(
                    root.get("createdDate"),
                    toStartOfDayInstant(dto.getStartTime(), dto.getTimeZone())
            ));
        }
        if (dto.getEndTime() != null) {
            predicates.add(cb.lessThanOrEqualTo(
                    root.get("createdDate"),
                    toEndOfDayInstant(dto.getEndTime(), dto.getTimeZone())
            ));
        }
        // 用户 & 团队 ID 条件
        if (dto.getUserIdList() != null || dto.getTeamIdList() != null) {
            Set<Long> userIds = new HashSet<>();
            if (dto.getUserIdList() != null)    userIds.addAll(dto.getUserIdList());
            if (dto.getTeamIdList() != null) {
                Set<Long> teamIds = new HashSet<>(dto.getTeamIdList());
                Set<Long> teamUserIds = userService.getAllTeamUserIdsByPermissionTeamIdIn(teamIds).getBody();
                if (teamUserIds != null) userIds.addAll(teamUserIds);
            }
            if (!userIds.isEmpty()) {
                CriteriaBuilder.In<Long> in = cb.in(root.get("userId"));
                userIds.forEach(in::value);
                predicates.add(in);
            }
        }
        // 呼叫结果、类型、租户条件……（同理添加）
        if (dto.getCallResultList() != null && !dto.getCallResultList().isEmpty()) {
            CriteriaBuilder.In<Long> in = cb.in(root.get("callResultId"));
            dto.getCallResultList().forEach(in::value);
            predicates.add(in);
        }
        if (dto.getCallTypeList() != null && !dto.getCallTypeList().isEmpty()) {
            CriteriaBuilder.In<Long> in = cb.in(root.get("callTypeId"));
            dto.getCallTypeList().forEach(in::value);
            predicates.add(in);
        }
        if (dto.getTenantId() != null) {
            predicates.add(cb.equal(root.get("tenantId"), dto.getTenantId()));
        }

        cq.where(predicates.toArray(new Predicate[0]));

        // SELECT 构造 DTO
        cq.select(cb.construct(
                VoipContactDTO.class,
                root,
                tcJoin.get("verificationStatus")
        ));

        return em.createQuery(cq).getResultList();
    }

    @Override
    public List<VoipContactDTO> findAllByUserId(Long userId) {
        List<VoipContactDTO> voipContacts = voipContactMapper.toDto(voipContactRepository.findAllByUserId(userId));
        return voipContacts;
    }
}
