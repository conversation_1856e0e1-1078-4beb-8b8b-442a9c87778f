package com.altomni.apn.voip.service.voicemail;

import com.altomni.apn.voip.service.dto.voicemail.WhisperVoicemailDTO;
import com.altomni.apn.voip.service.vo.VoicemailNoteNewVO;
import com.altomni.apn.voip.web.rest.vm.voip.VoicemailNotificationRequest;
import org.springframework.http.ResponseEntity;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.net.URISyntaxException;

public interface VoicemailNotificationService {

    VoicemailNoteNewVO getVoicemailNoteNotification(Long talentId);

    ResponseEntity<String> getVoiceMailRecording(String phoneCallId);

    void checkValidUserIdForVoicemail(VoicemailNotificationRequest request);

    void getWhisperVoicemail(WhisperVoicemailDTO whisperVoicemailDTO) throws URISyntaxException, IOException;

}
