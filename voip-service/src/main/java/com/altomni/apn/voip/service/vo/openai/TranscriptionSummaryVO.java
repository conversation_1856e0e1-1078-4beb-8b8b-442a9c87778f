package com.altomni.apn.voip.service.vo.openai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TranscriptionSummaryVO {

    private String subTopic = "";

    private String summary = "";

    private String startStreamId = "";

    private List<String> conclusions = new ArrayList<>();

}
