package com.altomni.apn.voip.service.amazonconnect.impl;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.voip.config.env.ApplicationProperties;
import com.altomni.apn.voip.domain.amazonconnect.AmazonConnectAccount;
import com.altomni.apn.voip.repository.amazonconnect.AmazonConnectAccountRepository;
import com.altomni.apn.voip.service.amazonconnect.AmazonConnectService;
import com.altomni.apn.voip.service.dto.amazonconnect.*;
import com.altomni.apn.voip.service.dto.voip.PhoneRecordDeleteDTO;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.voip.service.dto.voip.PhoneRecordDeleteRequestDTO;
import com.altomni.apn.common.dto.voip.PhoneRecordingRequestDTO;
import com.altomni.apn.voip.service.mapper.amazonconnect.AmazonConnectAccountMapper;
import com.altomni.apn.voip.service.user.UserService;
import com.altomni.apn.voip.service.vo.amazonconnect.AmazonConnectMetricStatusVO;
import com.altomni.apn.voip.service.voip.VoipRecordService;
import com.altomni.apn.common.service.voipserver.VoipProxyAPIHttp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.connect.ConnectClient;
import software.amazon.awssdk.services.connect.model.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.altomni.apn.voip.utils.amazonconnect.AmazonConnectUtils.USER_VOIP_AMAZON_CONNECT_REDIS_EXPIRE_PREFIX;

@Slf4j
@Service
@Transactional
public class AmazonConnectServiceImpl implements AmazonConnectService {

    private final ConnectClient connectClient;

    private final AmazonConnectAccountRepository amazonConnectAccountRepository;

    private final AmazonConnectAccountMapper amazonConnectAccountMapper;

    private final VoipRecordService voipRecordService;

    private final UserService userService;

    private final VoipProxyAPIHttp voipProxyAPIHttp;

    private static final String PHONE_RECORDING_POST = "/api/v1/connect/phone-recording\n";

    private static final String PHONE_RECORDING_DELETE_PUT = "/api/v1/connect/phone-recording/delete";

    private static final String METRICS_STATUS_POST = "/api/v1/amazon-connect/metrics/status";

    private static final String VOIP_CALLING_STATUS_IS_CALLING_PUT = "/api/v1/amazon-connect/voip-calling-status/is-calling";


    public AmazonConnectServiceImpl(ApplicationProperties properties, AmazonConnectAccountRepository amazonConnectAccountRepository, AmazonConnectAccountMapper amazonConnectAccountMapper,
                                    VoipRecordService voipRecordService, UserService userService, VoipProxyAPIHttp voipProxyAPIHttp) {
        this.amazonConnectAccountRepository = amazonConnectAccountRepository;
        this.amazonConnectAccountMapper = amazonConnectAccountMapper;
        this.voipRecordService = voipRecordService;
        this.userService = userService;
        this.voipProxyAPIHttp = voipProxyAPIHttp;

        AwsBasicCredentials awsCreds = AwsBasicCredentials.create(properties.getAwsAccessKey(), properties.getAwsSecretKey());
        connectClient = ConnectClient.builder().region(Region.of(properties.getAwsRegion()))
                .credentialsProvider(StaticCredentialsProvider.create(awsCreds))
                .build();
    }

    @Override
    public AmazonConnectAccountDTO createAmazonConnectUser(AmazonConnectAccountDTO amazonConnectAccountDTO) {
        AmazonConnectSecurityProfileDTO securityProfileDTO = amazonConnectAccountDTO.getSecurityProfile();
        AmazonConnectRoutingProfileDTO routingProfileDTO = amazonConnectAccountDTO.getRoutingProfile();
        CreateUserRequest request = CreateUserRequest.builder().
                instanceId(amazonConnectAccountDTO.getAccountInstance().getInstanceId()).
                username(amazonConnectAccountDTO.getEmail()).
                identityInfo(UserIdentityInfo.builder().
                        firstName(amazonConnectAccountDTO.getFirstName()).
                        lastName(amazonConnectAccountDTO.getLastName()).
                        build()).
                securityProfileIds(new ArrayList<>(List.of(securityProfileDTO.getSecurityProfileId()))).
                routingProfileId(routingProfileDTO.getRoutingProfileId()).
                phoneConfig(UserPhoneConfig.builder().
                        afterContactWorkTimeLimit(0).
                        autoAccept(false).
                        phoneType(PhoneType.SOFT_PHONE).
                        build()).
                build();
        CreateUserResponse response = connectClient.createUser(request);
        AmazonConnectAccount amazonConnectAccount = amazonConnectAccountMapper.toEntity(amazonConnectAccountDTO);
        amazonConnectAccount.setInstanceId(amazonConnectAccountDTO.getAccountInstance().getInstanceId());
        amazonConnectAccount.setRoutingProfileId(amazonConnectAccountDTO.getRoutingProfile().getRoutingProfileId());
        amazonConnectAccount.setSecurityProfileId(amazonConnectAccountDTO.getSecurityProfile().getSecurityProfileId());
        amazonConnectAccount.setAccountId(response.userId());
        amazonConnectAccount = amazonConnectAccountRepository.save(amazonConnectAccount);
        AmazonConnectAccountDTO updateAmazonConnectAccountDTO = amazonConnectAccountMapper.toDto(amazonConnectAccount);
        Map<String, AmazonConnectInstanceDTO> instanceMap = getAmazonConnectInstanceList().stream().collect(Collectors.toMap(AmazonConnectInstanceDTO::getInstanceId, amazonConnectInstanceDTO -> amazonConnectInstanceDTO));
        Map<String, AmazonConnectRoutingProfileDTO> routingMap = getAmazonConnectRoutingProfileList(amazonConnectAccountDTO.getAccountInstance().getInstanceId()).stream().collect(Collectors.toMap(AmazonConnectRoutingProfileDTO::getRoutingProfileId, amazonConnectRoutingProfileDTO -> amazonConnectRoutingProfileDTO));
        Map<String, AmazonConnectSecurityProfileDTO> securityMap = getAmazonConnectSecurityProfileList(amazonConnectAccountDTO.getAccountInstance().getInstanceId()).stream().collect(Collectors.toMap(AmazonConnectSecurityProfileDTO::getSecurityProfileId, amazonConnectSecurityProfileDTO -> amazonConnectSecurityProfileDTO));
        updateAmazonConnectAccountDTO.setFirstName(amazonConnectAccountDTO.getFirstName());
        updateAmazonConnectAccountDTO.setLastName(amazonConnectAccountDTO.getLastName());
        updateAmazonConnectAccountDTO.setAccountInstance(instanceMap.get(amazonConnectAccount.getInstanceId()));
        updateAmazonConnectAccountDTO.setRoutingProfile(routingMap.get(amazonConnectAccount.getRoutingProfileId()));
        updateAmazonConnectAccountDTO.setSecurityProfile(securityMap.get(amazonConnectAccount.getSecurityProfileId()));
        return updateAmazonConnectAccountDTO;
    }

    @Override
    public void deleteAmazonConnectUser(Long userId) {
        Optional<AmazonConnectAccount> amazonConnectAccountOptional = amazonConnectAccountRepository.findByUserId(userId);
        if (amazonConnectAccountOptional.isEmpty())
            throw new CustomParameterizedException("The user doesn't have valid amazon connect account!");
        AmazonConnectAccount account = amazonConnectAccountOptional.get();
        DeleteUserRequest request = DeleteUserRequest.builder().
                instanceId(account.getInstanceId()).
                userId(account.getAccountId()).
                build();
        DeleteUserResponse response = connectClient.deleteUser(request);
        if (response.sdkHttpResponse().isSuccessful()) {
            amazonConnectAccountRepository.deleteByUserId(userId);
        } else {
            log.error("Delete amazon connect account with account id: {} fail!", account.getAccountId());
            throw new CustomParameterizedException("Delete amazon connect account fail! Please retry.");
        }
    }

    @Override
    public List<AmazonConnectInstanceDTO> getAmazonConnectInstanceList() {
        ListInstancesRequest request = ListInstancesRequest.builder().
                maxResults(10).
                build();
        ListInstancesResponse response = connectClient.listInstances(request);
        List<InstanceSummary> instanceSummaries = response.instanceSummaryList().stream().filter(summary -> summary.identityManagementType().equals(DirectoryType.SAML)).toList();
        List<AmazonConnectInstanceDTO> instances = instanceSummaries.stream().map(summary -> new AmazonConnectInstanceDTO(summary.id(), summary.instanceAlias())).toList();
        return instances;
    }

    @Override
    public List<AmazonConnectRoutingProfileDTO> getAmazonConnectRoutingProfileList(String instanceId) {
        ListRoutingProfilesRequest request = ListRoutingProfilesRequest.builder().
                instanceId(instanceId).
                maxResults(100)
                .build();
        ListRoutingProfilesResponse response = connectClient.listRoutingProfiles(request);
        List<RoutingProfileSummary> routingProfileSummaries = response.routingProfileSummaryList();
        List<AmazonConnectRoutingProfileDTO> routingProfiles = routingProfileSummaries.stream().map(summary -> new AmazonConnectRoutingProfileDTO(summary.id(), summary.name())).toList();
        return routingProfiles;
    }

    @Override
    public List<AmazonConnectSecurityProfileDTO> getAmazonConnectSecurityProfileList(String instanceId) {
        ListSecurityProfilesRequest request = ListSecurityProfilesRequest.builder().
                instanceId(instanceId).
                maxResults(100)
                .build();
        ListSecurityProfilesResponse response = connectClient.listSecurityProfiles(request);
        List<SecurityProfileSummary> securityProfileSummaries = response.securityProfileSummaryList();
        List<AmazonConnectSecurityProfileDTO> securityProfiles = securityProfileSummaries.stream().map(summary -> new AmazonConnectSecurityProfileDTO(summary.id(), summary.name())).toList();
        return securityProfiles;
    }

    private VoipContactDTO validContactRecord(String phoneCallId) {
        VoipContactDTO contact = voipRecordService.getVoipContact(phoneCallId);
        if(contact == null) throw new CustomParameterizedException(404, "Not Found Exception", "The contact record doesn't exist!");
        return contact;
    }

    private InstanceStorageConfig getStorageConfig(VoipContactDTO contact) {
        ListInstanceStorageConfigsRequest storageConfigsRequest = ListInstanceStorageConfigsRequest.builder().
                resourceType(InstanceStorageResourceType.CALL_RECORDINGS).
                instanceId(contact.getInstanceId()).
                build();
        ListInstanceStorageConfigsResponse storageConfigsResponse = connectClient.listInstanceStorageConfigs(storageConfigsRequest);
        List<InstanceStorageConfig> storageConfigs = storageConfigsResponse.storageConfigs();
        if(storageConfigs.isEmpty()) {
            log.error("Cannot find the amazon connect instance corresponding S3 bucket.");
            return null;
        }
        return storageConfigs.get(0);
    }

    @Override
    public ResponseEntity<String> getAmazonConnectRecording(String phoneCallId) {
        VoipContactDTO existContact = voipRecordService.getVoipContact(phoneCallId);
        if(existContact == null) throw new CustomParameterizedException("The contact with contact id: " + phoneCallId + " doesn't exist or is not valid.");
        PhoneRecordingRequestDTO request = new PhoneRecordingRequestDTO();
        request.setPhoneCallId(phoneCallId);
        Long userId = existContact.getUserId();
        UserBriefDTO userBriefDTO = userService.findById(userId).getBody();
        if(userBriefDTO == null) throw new CustomParameterizedException("The user with id: " + userId + " doesn't exist or is not valid.");
        request.setUserEmail(userBriefDTO.getEmail());
        request.setCreatedDate(existContact.getCreatedDate());
        return voipProxyAPIHttp.proxyApiRequest(PHONE_RECORDING_POST, request, HttpMethod.POST, String.class);

    }

    @Override
    public ResponseEntity<Void> deleteAmazonConnectRecording(PhoneRecordDeleteDTO phoneRecordDeleteDTO) {
        VoipContactDTO existContact = validContactRecord(phoneRecordDeleteDTO.getPhoneCallId());
        String phoneCallId = phoneRecordDeleteDTO.getPhoneCallId();
        PhoneRecordDeleteRequestDTO request = new PhoneRecordDeleteRequestDTO();
        request.setPhoneCallId(phoneCallId);
        request.setIsRecordDelete(phoneRecordDeleteDTO.getIsRecordDelete());
        Long userId = existContact.getUserId();
        UserBriefDTO userBriefDTO = userService.findById(userId).getBody();
        if(userBriefDTO == null) throw new CustomParameterizedException("The user with id: " + userId + " doesn't exist or is not valid.");
        request.setUserEmail(userBriefDTO.getEmail());
        request.setCreatedDate(existContact.getCreatedDate());
        return voipProxyAPIHttp.proxyApiRequest(PHONE_RECORDING_DELETE_PUT, request, HttpMethod.PUT, Void.class);
    }

    @Override
    public ResponseEntity<AmazonConnectMetricStatusVO> checkAmazonConnectMetricsStatus() {
        String userEmail = SecurityUtils.getEmail();
        AmazonConnectMetricStatusRequestDTO request = new AmazonConnectMetricStatusRequestDTO(userEmail);
        return voipProxyAPIHttp.proxyApiRequest(METRICS_STATUS_POST, request, HttpMethod.POST, AmazonConnectMetricStatusVO.class);
    }

    @Override
    public ResponseEntity<Void> recordUserVoipAmazonConnectCall(AmazonConnectVoipCallingStatusRequestDTO request) {
        return voipProxyAPIHttp.proxyApiRequest(VOIP_CALLING_STATUS_IS_CALLING_PUT, request, HttpMethod.PUT, Void.class);
    }


}
