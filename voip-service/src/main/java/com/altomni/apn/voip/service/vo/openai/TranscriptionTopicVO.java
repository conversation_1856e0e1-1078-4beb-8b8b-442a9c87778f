package com.altomni.apn.voip.service.vo.openai;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TranscriptionTopicVO {

    private String topic = "";

    private List<TranscriptionSummaryVO> content = new ArrayList<>();

    private String startStreamId = "";

}
