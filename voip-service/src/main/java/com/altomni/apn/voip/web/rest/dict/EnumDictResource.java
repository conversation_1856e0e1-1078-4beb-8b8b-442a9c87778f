package com.altomni.apn.voip.web.rest.dict;


import com.altomni.apn.common.aop.cache.CacheControl;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.enums.*;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * REST controller for voip dict.
 */
@Api(tags = {"APN-Voip-EnumDict"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class EnumDictResource {

    @Resource
    private EnumVoipCallResultService enumVoipCallResultService;

    @Resource
    private EnumVoipCallTypeService enumVoipCallTypeService;


    @ApiOperation(value = "Get/Filter request to get level of voip call result enum data . ")
    @GetMapping("/dict/call-result")
    @Timed
    @CacheControl
    @NoRepeatSubmit
    public ResponseEntity<List<EnumDictVO>> getVoipCallResult(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get voip call result data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumVoipCallResultService.findAllOrderBySortType(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get level of voip call type enum data . ")
    @GetMapping("/dict/call-type")
    @Timed
    @CacheControl
    @NoRepeatSubmit
    public ResponseEntity<List<EnumDictVO>> getVoipCallType(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get voip call type data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumVoipCallTypeService.findAllOrderBySortType(type), HttpStatus.OK);
    }

}
