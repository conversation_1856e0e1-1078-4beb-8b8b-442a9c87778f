package com.altomni.apn.voip.service.talent;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.aop.user.AttachSimpleUser;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.dto.search.TalentSearchConditionDTO;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import com.altomni.apn.voip.web.rest.vm.talent.TalentContactSearchVM;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
@FeignClient(value = "talent-service")
public interface TalentService {
    @PostMapping("/talent/api/v3/talents-basic/search-by-contacts")
    ResponseEntity<List<TalentDTOV3>> searchTalentsByContact(@RequestBody TalentDTOV3 talentDTOV3);

    @GetMapping("/talent/api/v3/talent/{id}/note")
    ResponseEntity<List<Map<String, Object>>> getTalentNoteByType(@PathVariable("id") Long talentId, @RequestParam(value = "type") List<String> type, @RequestParam(value = "noteType", required = false) String noteType);

    @PostMapping("/talent/api/v3/talent-voice-message-notes")
    ResponseEntity<TalentNote> createTalentVoiceMessageNote(@Valid @RequestBody CreateTalentVoiceMessageNoteDTO talentNote) throws URISyntaxException, IOException;

    @GetMapping("/talent/api/v3/talents/{id}/owner")
    ResponseEntity<TalentOwnershipDTO> findTalentOwnerByTalentId(@ApiParam(value = "talent id", required = true) @PathVariable("id") Long id);

    @GetMapping("/talent/api/v3/talent-contacts/by-contact-types")
    ResponseEntity<List<TalentContactDTO>> findTalentContactByContactAndTypes(@RequestParam(value = "contact") String contact, @RequestParam(value = "types") List<ContactType> types);

    @PostMapping("/talent/api/v3/talent-contacts/verification-status")
    ResponseEntity<TalentContact> updateTalentContactVerificationStatus(@RequestBody UpdateTalentContactVerificationStatusDTO updateContactVerificationStatusDTO);

    @PostMapping("/talent/api/v3/brief-talents")
    ResponseEntity<Set<TalentBriefVO>> findBriefTalentsByTalentIds(@RequestBody Set<Long> talentIds);
}
