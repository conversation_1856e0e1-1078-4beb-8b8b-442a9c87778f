package com.altomni.apn.voip.repository.voip;

import com.altomni.apn.common.domain.voip.VoipContact;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface VoipContactRepository extends JpaRepository<VoipContact, Long>, JpaSpecificationExecutor<VoipContact> {

    Optional<VoipContact> findByPhoneCallId(String phoneCallId);

    List<VoipContact> findAllByPhoneCallIdIn(Set<String> phoneCallIds);

    Optional<VoipContact> findFirstByUserIdOrderByCreatedDateDesc(Long userId);

    List<VoipContact> findAllByUserId(Long userId);


    /** only for voip data manipulate*/
    List<VoipContact> findAllByPhoneNumberIsNull();

}
