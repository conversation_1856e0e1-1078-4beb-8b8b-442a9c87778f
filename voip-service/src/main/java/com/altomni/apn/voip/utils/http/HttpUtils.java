package com.altomni.apn.voip.utils.http;

public final class HttpUtils {

    /**
     * 格式化URL路径，替换占位符 {} 为对应的值
     *
     * @param endpointTemplate 包含占位符的URL模板
     * @param pathVariables 要替换的路径变量值，顺序与占位符出现顺序一致
     * @return 替换后的URL
     */
    public static String formatEndpointPath(String endpointTemplate, Object... pathVariables) {
        String result = endpointTemplate;

        for (Object var : pathVariables) {
            result = result.replaceFirst("\\{\\}", var.toString());
        }

        return result;
    }
}
