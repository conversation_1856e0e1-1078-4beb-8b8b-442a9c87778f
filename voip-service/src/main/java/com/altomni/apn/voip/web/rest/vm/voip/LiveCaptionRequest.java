package com.altomni.apn.voip.web.rest.vm.voip;

import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
@Getter
@Setter
public class LiveCaptionRequest {

    private String pcmData;

    private TranscriptionModel model;

    private Boolean isEnd;

    @Override
    public String toString() {
        return "LiveCaptionRequest{" +
                "pcmData='" + pcmData + '\'' +
                ", model=" + model +
                ", isEnd=" + isEnd +
                '}';
    }
}
