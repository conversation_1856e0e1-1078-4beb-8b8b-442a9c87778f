package com.altomni.apn.voip.service.mapper.voip;
import com.altomni.apn.common.domain.voip.PhoneTranscription;
import com.altomni.apn.voip.service.dto.voip.PhoneTranscriptionDTO;
import com.altomni.apn.voip.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity Tag and its DTO TagDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface PhoneTranscriptionMapper extends EntityMapper<PhoneTranscriptionDTO, PhoneTranscription> {

    default PhoneTranscription fromId(String phoneCallId) {
        if (phoneCallId == null) {
            return null;
        }
        PhoneTranscription phoneTranscription = new PhoneTranscription();
        phoneTranscription.setPhoneCallId(phoneCallId);
        return phoneTranscription;
    }
}
