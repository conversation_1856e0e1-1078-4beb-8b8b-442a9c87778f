package com.altomni.apn.voip.service.dto.voip;


import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSource;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSourceConverter;
import com.altomni.apn.common.domain.voip.Transcription;
import lombok.Data;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class PhoneTranscriptionDTO implements Serializable {


    private String phoneCallId;

    private String accountId;

    private Long userId;

    private Long referenceCalleeId;

    private Long tenantId;

    private String phone;

    @Convert(converter = TranscriptionSourceConverter.class)
    private TranscriptionSource source;

    private List<TranscriptionDTO> transcriptions;

    private String totalTime;

    private Long jobId;

    private String jobTitle;

    public PhoneTranscriptionDTO() {
        this.transcriptions = new ArrayList<>();
    }

}
