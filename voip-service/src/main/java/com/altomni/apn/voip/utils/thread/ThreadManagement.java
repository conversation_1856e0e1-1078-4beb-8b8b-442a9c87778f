package com.altomni.apn.voip.utils.thread;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;

@Slf4j
@Service
public class ThreadManagement {

    public ThreadManagement() {}

    public void printThreadInfo() {
        ThreadMXBean threadMXBean = ManagementFactory.getThreadMXBean();
        int threadCount = threadMXBean.getThreadCount();  // 当前线程总数
        int peakThreadCount = threadMXBean.getPeakThreadCount();  // 峰值线程数
        long totalStartedThreadCount = threadMXBean.getTotalStartedThreadCount();  // 已启动的线程总数
        log.info("[APN: Voip] 当前线程数: " + threadCount);
        log.info("[APN: Voip] 峰值线程数: " + peakThreadCount);
        log.info("[APN: Voip] 启动过的线程总数: " + totalStartedThreadCount);
    }

}
