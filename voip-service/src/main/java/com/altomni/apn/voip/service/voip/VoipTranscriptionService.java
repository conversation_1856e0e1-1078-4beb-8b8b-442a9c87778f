package com.altomni.apn.voip.service.voip;

import com.altomni.apn.common.domain.voip.PhoneTranscription;
import com.altomni.apn.common.domain.voip.VoipContact;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.common.dto.voip.VoipReportDTO;
import com.altomni.apn.voip.service.dto.voip.PhoneTranscriptionDTO;
import com.altomni.apn.voip.web.rest.vm.voip.ConnectTranscriptionRequest;

import java.util.List;
import java.util.Set;

public interface VoipTranscriptionService {

    PhoneTranscriptionDTO getTranscription(String phoneCallId);


    List<PhoneTranscription> findAllPhoneTranscriptionByPhoneCallIds(Set<String> phoneCallIds);


}
