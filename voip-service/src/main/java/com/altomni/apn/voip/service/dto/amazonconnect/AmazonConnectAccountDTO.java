package com.altomni.apn.voip.service.dto.amazonconnect;

import lombok.NoArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
public class AmazonConnectAccountDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String accountId;

    private AmazonConnectInstanceDTO accountInstance;

    private Long userId;

    private Long tenantId;

    private String firstName;

    private String lastName;

    private String email;

    @NotNull
    private AmazonConnectSecurityProfileDTO securityProfile;

    @NotNull
    private AmazonConnectRoutingProfileDTO routingProfile;

    public AmazonConnectAccountDTO(String instanceId, Long userId, Long tenantId, String firstName, String lastName,  String email, String securityProfileId, String routingProfileId) {
            this.accountInstance = new AmazonConnectInstanceDTO();
            this.accountInstance.setInstanceId(instanceId);
            this.userId = userId;
            this.tenantId = tenantId;
            this.firstName = firstName;
            this.lastName = lastName;
            this.email = email;
            this.securityProfile = new AmazonConnectSecurityProfileDTO();
            this.securityProfile.setSecurityProfileId(securityProfileId);
            this.routingProfile = new AmazonConnectRoutingProfileDTO();
            this.routingProfile.setRoutingProfileId(routingProfileId);
    }

}
