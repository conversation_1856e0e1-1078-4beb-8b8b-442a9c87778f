package com.altomni.apn.voip.service.audio;

import org.springframework.web.multipart.MultipartFile;

import javax.sound.sampled.*;
import java.io.*;

public class AudioProcess {



    public static AudioInputStream byteArrayToAudioInputStream(byte[] audioBytes, float sampleRate, int sampleSizeInBits, int channels, boolean signed, boolean bigEndian) {
        AudioFormat format = new AudioFormat(sampleRate, sampleSizeInBits, channels, signed, bigEndian);
        ByteArrayInputStream bais = new ByteArrayInputStream(audioBytes);
        AudioInputStream audioInputStream = new AudioInputStream(bais, format, audioBytes.length / format.getFrameSize());
        return audioInputStream;
    }

    public static byte[] audioInputStreamToByteArray(AudioInputStream audioStream) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024]; // byte buffer
        int length;
        try {
            while ((length = audioStream.read(buffer)) != -1) {
                out.write(buffer, 0, length);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                audioStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return out.toByteArray();
    }

    public static byte[] convertWavToPcm(MultipartFile file) throws IOException, UnsupportedAudioFileException {
        InputStream inputStream = new BufferedInputStream(file.getInputStream());
        AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(inputStream);
        AudioFormat pcmFormat = new AudioFormat(16000, 16, 1, true, false);
        audioInputStream = AudioSystem.getAudioInputStream(pcmFormat, audioInputStream);
        byte[] pcmData = audioInputStreamToByteArray(audioInputStream);
        inputStream.close();
        return pcmData;
    }

    public static AudioInputStream audioResampling(AudioInputStream inputStream, AudioFormat audioFormat) {
        AudioInputStream resampledStream = null;
        try {
            resampledStream = AudioSystem.getAudioInputStream(audioFormat, inputStream);
            inputStream.close();
            return resampledStream;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return resampledStream;
    }


    public static double calculateAudioDurationInSeconds(int totalBytes, int sampleRate) {
        // 16 bit PCM, 2 byte, momo
        return (double) totalBytes / (sampleRate * 2);
    }

    public static double calculateAverageDecibels(byte[] audioData) {
        long sum = 0;
        for (int i = 0; i < audioData.length; i += 2) {
            int sample = (audioData[i+1] << 8) | (audioData[i] & 0xFF);
            sum += sample * sample;
        }
        double rms = Math.sqrt((double)sum / (audioData.length / 2));
        rms = rms / Short.MAX_VALUE;
        if (rms <= 0.0001) rms = 0.0001;
        return 20 * Math.log10(rms);
    }

    public static double calculateWindowedMaximumDecibels(byte[] audioData, int windowSize) {
        if (audioData.length < windowSize) {
            throw new IllegalArgumentException("Window size must be smaller than audio data length.");
        }

        double maxDecibels = Double.NEGATIVE_INFINITY; // 初始设置为极小值

        // 遍历每个窗口
        for (int start = 0; start <= audioData.length - windowSize; start += windowSize) {
            long sumSquare = 0; // 窗口内样本平方和

            // 处理当前窗口内的样本
            for (int i = start; i < start + windowSize; i += 2) {
                if (i + 1 >= audioData.length) break; // 防止数组越界
                int sample = (audioData[i+1] << 8) | (audioData[i] & 0xFF);
                sumSquare += sample * sample;
            }

            double rms = Math.sqrt((double)sumSquare / (windowSize / 2));
            rms = rms / Short.MAX_VALUE; // 归一化
            if (rms <= 0.0001) rms = 0.0001; // 防止对数计算出错
            double decibels = 20 * Math.log10(rms); // 计算当前窗口的分贝值

            if (decibels > maxDecibels) {
                maxDecibels = decibels; // 更新最大分贝值
            }
        }

        return maxDecibels;
    }


    public static double calculateMaximumDecibels(byte[] audioData) {
        long maxSumSquare = 0; // 用于存储最大的样本平方和
        for (int i = 0; i < audioData.length; i += 2) {
            int sample = (audioData[i+1] << 8) | (audioData[i] & 0xFF);
            long sampleSquare = sample * sample;
            if (sampleSquare > maxSumSquare) {
                maxSumSquare = sampleSquare; // 更新最大的样本平方和
            }
        }
        double maxRms = Math.sqrt((double)maxSumSquare); // 计算最大RMS值
        maxRms = maxRms / Short.MAX_VALUE; // 归一化
        if (maxRms <= 0.0001) maxRms = 0.0001;
        return 20 * Math.log10(maxRms); // 计算分贝值
    }

    public static double calculateAverageDecibelsForLastSeconds(byte[] audioData, int sampleRate, int seconds) {
        int bytesPerSample = 2; // 16位PCM编码意味着每个样本2字节
        int numberOfSamplesInOneSecond = sampleRate * bytesPerSample * seconds;

        // 确保音频数据至少有一秒
        if (audioData.length < numberOfSamplesInOneSecond) {
            return Double.MAX_VALUE; // 或其他逻辑，表示数据不足一秒
        }

        // 提取最后一秒的音频数据
        byte[] lastSecondData = new byte[numberOfSamplesInOneSecond];
        System.arraycopy(audioData, audioData.length - numberOfSamplesInOneSecond, lastSecondData, 0, numberOfSamplesInOneSecond);

        // 计算最后一秒音频数据的平均分贝值
        return calculateAverageDecibels(lastSecondData);
    }

    public static File convertPCMtoWAV(byte[] audioData) throws IOException {
        File wavFile = File.createTempFile("temp", ".wav", new File(System.getProperty("java.io.tmpdir")));
        AudioInputStream resampledStream = byteArrayToAudioInputStream(audioData, 16000, 16, 1, true, false);
        AudioSystem.write(resampledStream, AudioFileFormat.Type.WAVE, wavFile);
        return wavFile;
    }

    public static InputStream convertPCMtoWAV(InputStream pcmStream, long pcmDataLength, float sampleRate, int channels, int bitsPerSample) throws IOException {
        // 生成 WAV 文件头
        byte[] wavHeader = writeWavHeader(channels, bitsPerSample,  sampleRate, pcmDataLength);
        InputStream wavHeaderStream = new ByteArrayInputStream(wavHeader);
        return new SequenceInputStream(wavHeaderStream, pcmStream);
    }

    private static byte[] writeWavHeader(int channels, int bitsPerSample, float sampleRate, long pcmDataLength) throws IOException {
        int totalDataLength = (int) (pcmDataLength + 36); // PCM 数据长度 + 头部固定长度
        int byteRate = (int) sampleRate * channels * bitsPerSample / 8;
        byte[] header = new byte[44];
        // RIFF chunk
        System.arraycopy("RIFF".getBytes(), 0, header, 0, 4);
        System.arraycopy(intToByteArray(totalDataLength), 0, header, 4, 4);
        System.arraycopy("WAVE".getBytes(), 0, header, 8, 4);

        // fmt subchunk
        System.arraycopy("fmt ".getBytes(), 0, header, 12, 4);
        System.arraycopy(intToByteArray(16), 0, header, 16, 4); // PCM 子块大小
        System.arraycopy(shortToByteArray((short) 1), 0, header, 20, 2); // PCM 格式
        System.arraycopy(shortToByteArray((short) channels), 0, header, 22, 2); // 通道数
        System.arraycopy(intToByteArray((int) sampleRate), 0, header, 24, 4); // 采样率
        System.arraycopy(intToByteArray(byteRate), 0, header, 28, 4); // ByteRate
        System.arraycopy(shortToByteArray((short) (channels * bitsPerSample / 8)), 0, header, 32, 2); // BlockAlign
        System.arraycopy(shortToByteArray((short) bitsPerSample), 0, header, 34, 2); // BitsPerSample

        // data subchunk
        System.arraycopy("data".getBytes(), 0, header, 36, 4);
        System.arraycopy(intToByteArray((int) pcmDataLength), 0, header, 40, 4);

        return header;
    }

    private static byte[] intToByteArray(int value) {
        return new byte[] {
                (byte) (value & 0xFF),
                (byte) ((value >> 8) & 0xFF),
                (byte) ((value >> 16) & 0xFF),
                (byte) ((value >> 24) & 0xFF),
        };
    }

    private static byte[] shortToByteArray(short value) {
        return new byte[] {
                (byte) (value & 0xFF),
                (byte) ((value >> 8) & 0xFF),
        };
    }

    public static byte[] pcmToWav(byte[] pcm, int sampleRate, int channels, int bitDepth) throws IOException {
        int byteRate = sampleRate * channels * bitDepth / 8;
        int totalDataLen = pcm.length + 36;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        out.write("RIFF".getBytes());
        out.write(intToLittleEndian(totalDataLen + 8));
        out.write("WAVE".getBytes());
        out.write("fmt ".getBytes());
        out.write(intToLittleEndian(16));
        out.write(shortToLittleEndian((short) 1));
        out.write(shortToLittleEndian((short) channels));
        out.write(intToLittleEndian(sampleRate));
        out.write(intToLittleEndian(byteRate));
        out.write(shortToLittleEndian((short) (channels * bitDepth / 8)));
        out.write(shortToLittleEndian((short) bitDepth));
        out.write("data".getBytes());
        out.write(intToLittleEndian(pcm.length));
        out.write(pcm);
        return out.toByteArray();
    }
    private static byte[] intToLittleEndian(int value) {
        return new byte[] {
                (byte) (value & 0xff),
                (byte) ((value >> 8) & 0xff),
                (byte) ((value >> 16) & 0xff),
                (byte) ((value >> 24) & 0xff)
        };
    }
    private static byte[] shortToLittleEndian(short value) {
        return new byte[] {
                (byte) (value & 0xff),
                (byte) ((value >> 8) & 0xff)
        };
    }


}
