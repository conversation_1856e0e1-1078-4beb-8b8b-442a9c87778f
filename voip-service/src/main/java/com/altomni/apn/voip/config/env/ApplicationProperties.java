package com.altomni.apn.voip.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.amazonConnectService.accessKey}")
    private String awsAccessKey;

    @Value("${application.amazonConnectService.secretKey}")
    private String awsSecretKey;

    @Value("${application.amazonConnectService.region}")
    private String awsRegion;

    @Value("${application.amazonConnectService.instanceId}")
    private String awsConnectInstanceId;

    @Value("${application.amazonConnectService.voicemail.bucket}")
    private String awsVoicemailBucket;

    @Value("${application.amazonConnectService.voicemail.folder}")
    private String awsVoicemailFolder;

    @Value("${application.amazonConnectService.maxConcurrentCall}")
    private Integer maxConcurrentCall;

    @Value("${application.larkService.authenticationEndPoint}")
    private String larkAuthenticationEndpoint;

    @Value("${application.larkService.streamRecognitionEndpoint}")
    private String larkStreamRecognitionEndpoint;

    @Value("${application.larkService.sendMessageEndpoint}")
    private String larkSendMessageEndpoint;

    @Value("${application.larkService.streamRecognitionService.appId}")
    private String larkStreamRecognitionServiceAppId;

    @Value("${application.larkService.streamRecognitionService.appSecret}")
    private String larkStreamRecognitionServiceAppSecret;

    @Value("${application.larkService.messageNotificationService.appId}")
    private String larkSendMessageServiceAppId;

    @Value("${application.larkService.messageNotificationService.appSecret}")
    private String larkSendMessageServiceAppSecret;

    @Value("${application.openAIService.apiKey}")
    private String openAIApiKey;

    @Value("${application.openAIService.chatGPTEndpoint}")
    private String openAIChatGPTEndpoint;

    @Value("${application.openAIService.chatGPTModel}")
    private String openAIChatGPTModel;

    @Value("${application.aliCloud.appKey}")
    private String aliCloudAppKey;

    @Value("${application.aliCloud.appEngKey}")
    private String aliCloudEngAppKey;

    @Value("${application.aliCloud.accessKey}")
    private String aliCloudAccessKey;

    @Value("${application.aliCloud.secretKey}")
    private String aliCloudSecretKey;

    @Value("${application.aliCloud.fileTranscriptionEndPoint}")
    private String aliCloudFileTranscriptionEndPoint;

    @Value("${application.amazonConnectService.voicemail.phoneNumber}")
    private String voiceMailPhoneNumber;

    @Value("${apn.hostUrl}")
    private String apnHostUrl;
}
