package com.altomni.apn.voip.service.mapper.voip;
import com.altomni.apn.common.domain.voip.Transcription;
import com.altomni.apn.voip.service.dto.voip.TranscriptionDTO;
import com.altomni.apn.voip.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity Tag and its DTO TagDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface TranscriptionMapper extends EntityMapper<TranscriptionDTO, Transcription> {

    default Transcription fromId(String streamId) {
        if (streamId == null) {
            return null;
        }
        Transcription transcription = new Transcription();
        transcription.setStreamId(streamId);
        return transcription;
    }
}
