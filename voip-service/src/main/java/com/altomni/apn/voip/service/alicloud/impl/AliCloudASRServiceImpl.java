package com.altomni.apn.voip.service.alicloud.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nls.client.AccessToken;
import com.alibaba.nls.client.protocol.InputFormatEnum;
import com.alibaba.nls.client.protocol.NlsClient;
import com.alibaba.nls.client.protocol.SampleRateEnum;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriber;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriberListener;
import com.alibaba.nls.client.protocol.asr.SpeechTranscriberResponse;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModel;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.voip.config.env.ApplicationProperties;
import com.altomni.apn.voip.service.alicloud.AliCloudASRService;
import com.altomni.apn.voip.service.dto.voip.VoiceMemoDTO;
import com.altomni.apn.voip.service.sse.VoipSseClient;
import com.altomni.apn.voip.web.rest.vm.voip.LiveCaptionRequest;
import okhttp3.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

import static com.altomni.apn.voip.utils.mapping.ObjectMappingUtils.objectToMap;

@Service
public class AliCloudASRServiceImpl implements AliCloudASRService {

    private Logger log = LoggerFactory.getLogger(AliCloudASRServiceImpl.class);

    private final ApplicationProperties properties;

    private final OkHttpClient okHttpClient;

    private static final float SAMPLE_RATE = 16000f;

    private static final String WAV_TYPE = "wav";

    private IAcsClient iAcsClient;

    private NlsClient client;

    private final VoipSseClient voipSseClient;

    private final Map<Long, SpeechTranscriber> sessionMap = new ConcurrentHashMap<>();

    public AliCloudASRServiceImpl(ApplicationProperties properties, VoipSseClient voipSseClient) {
        this.properties = properties;
        this.voipSseClient = voipSseClient;
        this.okHttpClient = new OkHttpClient().newBuilder().build();
    }

    private void initClient() throws IOException {
        AccessToken accessToken = new AccessToken(properties.getAliCloudAccessKey(), properties.getAliCloudSecretKey());
        accessToken.apply();
        String token = accessToken.getToken();
        this.client = new NlsClient(token);
    }

    //temporary method for voice memo transcription
    private void removeTranscriber(Long userId) throws Exception {
        SpeechTranscriber t = sessionMap.remove(userId);
        if (t != null) {
            t.stop();
            t.close(); // 结束会话
        }
    }

    private SpeechTranscriber getOrCreateTranscriber(Long userId, Long tenantId, TranscriptionModel model) throws Exception {
        return sessionMap.computeIfAbsent(userId, uid -> {
            try {
                SpeechTranscriber transcriber = generateTranscriber(uid, tenantId, model);
                transcriber.start();
                return transcriber;
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
    }


    protected SpeechTranscriberListener getTranscriberListener(Long userId, Long tenantId) {
        return new SpeechTranscriberListener() {
            //识别出中间结果。仅当setEnableIntermediateResult为true时，才会返回该消息。
            @Override
            public void onTranscriptionResultChange(SpeechTranscriberResponse response) {
                System.out.println("start, task_id: " + response.getTaskId() + ", name: " + response.getName() + ", status: " + response.getStatus());
                try {
                    VoiceMemoDTO voiceMemo = new VoiceMemoDTO(response.getTaskId(), response.getTransSentenceIndex(), response.getTransSentenceText(), false);
                    Map<String, Object> data = objectToMap(voiceMemo);
                    voipSseClient.pushSseMessage(userId, tenantId, data);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
            @Override
            public void onTranscriberStart(SpeechTranscriberResponse response) {
                // 识别开始。
                System.out.println("start, task_id: " + response.getTaskId() + ", name: " + response.getName() + ", status: " + response.getStatus());
            }
            @Override
            public void onSentenceBegin(SpeechTranscriberResponse response) {
                // 识别到一句话的开始。仅当setEnableIntermediateResult为true时，才会返回该消息。
                System.out.println("sentence begin, task_id: " + response.getTaskId() + ", name: " + response.getName() + ", status: " + response.getStatus() + ", sentence_index: " + response.getTransSentenceIndex() + ", sentence_text: " + response.getTransSentenceText());
                try {
                    VoiceMemoDTO voiceMemo = new VoiceMemoDTO(response.getTaskId(), response.getTransSentenceIndex(), response.getTransSentenceText(), true);
                    Map<String, Object> data = objectToMap(voiceMemo);
                    voipSseClient.pushSseMessage(userId, tenantId, data);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
            @Override
            public void onSentenceEnd(SpeechTranscriberResponse response) {
                try {
                    VoiceMemoDTO voiceMemo = new VoiceMemoDTO(response.getTaskId(), response.getTransSentenceIndex(), response.getTransSentenceText(), true);
                    Map<String, Object> data = objectToMap(voiceMemo);
                    voipSseClient.pushSseMessage(userId, tenantId, data);
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }

            //识别完毕
            @Override
            public void onTranscriptionComplete(SpeechTranscriberResponse response) {
                System.out.println("complete, task_id: " + response.getTaskId() + ", name: " + response.getName() + ", status: " + response.getStatus());
            }

            @Override
            public void onFail(SpeechTranscriberResponse response) {
                //task_id是调用方和服务端通信的唯一标识，遇到问题时，需要提供此task_id。
                System.out.println("task_id: " + response.getTaskId() +  ", status: " + response.getStatus() + ", status_text: " + response.getStatusText());
            }
        };
    }

    @Override
    public SpeechTranscriber generateTranscriber(Long userId, Long tenantId, TranscriptionModel model) throws Exception {
        this.initClient();
        SpeechTranscriber transcriber = new SpeechTranscriber(client, getTranscriberListener(userId, tenantId));
        if(model != null && model.equals(TranscriptionModel.ENGLISH)) {
            transcriber.setAppKey(properties.getAliCloudEngAppKey());
        }
        else transcriber.setAppKey(properties.getAliCloudAppKey());
        transcriber.setFormat(InputFormatEnum.PCM);
        //输入音频采样率。
        transcriber.setSampleRate(SampleRateEnum.SAMPLE_RATE_16K);
        //是否返回中间识别结果。
        transcriber.setEnableIntermediateResult(true);
        //是否生成并返回标点符号。
        transcriber.setEnablePunctuation(true);
        //是否将返回结果规整化，比如将一百返回为100。
        transcriber.setEnableITN(false);
        //设置vad断句参数。默认值：800ms，有效值：200ms～2000ms。
        //transcriber.addCustomedParam("max_sentence_silence", 600);
        //设置是否语义断句。
        transcriber.addCustomedParam("enable_semantic_sentence_detection",true);
        //设置是否开启过滤语气词，即声音顺滑。
        transcriber.addCustomedParam("disfluency",true);
        //设置是否开启词模式。
        transcriber.addCustomedParam("enable_words",true);
        return transcriber;
    }

    @Override
    public void sendVoiceAudioToTranscribe(byte[] stream, TranscriptionModel model, Boolean isEnd) {
        try {
            try {
                SpeechTranscriber speechTranscriber = getOrCreateTranscriber(SecurityUtils.getUserId(), SecurityUtils.getTenantId(), model);
                speechTranscriber.send(stream, stream.length);
                if(isEnd != null && isEnd) {
                    removeTranscriber(SecurityUtils.getUserId());
                }
            }
            catch (Exception e) {
                log.error("Error generating transcriber: {}", ExceptionUtils.getStackTrace(e));
                removeTranscriber(SecurityUtils.getUserId());
            }
        }
        catch (Exception e) {
            log.error("Error sending audio to transcriber: {}", ExceptionUtils.getStackTrace(e));
        }
    }

//    private CompletableFuture<Void> sendAudioBytesToAliCloudAsync(KVSStreamTrackObject kvsStreamTrackObject, ConnectTranscriptionRequest request, VoipCallDTO voipCallDTO, TranscriptionSpeakerType transcriptionSpeakerType, TranscriptionModel model) {
//        return CompletableFuture.runAsync(() -> {
//
//            SpeechTranscriber transcriber = null;
//            try {
//                int index = 1;
//                Queue<byte[]> audioQueue = new LinkedList<byte[]>();
//                long lastTime = System.currentTimeMillis();
//                try {
//                    ByteBuffer audioBuffer = KVSUtils.getByteBufferFromStream(kvsStreamTrackObject.getStreamingMkvReader(),
//                            kvsStreamTrackObject.getFragmentVisitor(), kvsStreamTrackObject.getTagProcessor(), request.getConnectPhoneCallId(), kvsStreamTrackObject.getTrackName());
//                    while (audioBuffer.remaining() > 0) {
////                        if(transcriber.getState())
//                        byte[] audioBytes = new byte[audioBuffer.remaining()];
//                        audioBuffer.get(audioBytes);
////                        System.out.println("1 " + transcriber.getState());
//                        kvsStreamTrackObject.getOutputStream().write(audioBytes);
//                        AudioInputStream audioStream = AudioProcess.byteArrayToAudioInputStream(audioBytes, AUDIO_RATE, SAMPLE_BIT_SIZE, AUDIO_CHANNEL, true, false);
//                        AudioFormat format16k = new AudioFormat(SAMPLE_RATE, SAMPLE_BIT_SIZE, AUDIO_CHANNEL, true, false);
//                        AudioInputStream resampledStream = AudioProcess.audioResampling(audioStream, format16k);
//                        byte[] audioResampledBytes = audioInputStreamToByteArray(resampledStream);
//                        audioQueue.add(audioResampledBytes);
//                        int totalLength = audioQueue.stream().mapToInt(arr -> arr.length).sum();
//                        byte[] stream = new byte[totalLength];
////                        System.out.println("2 " + transcriber.getState());
//                        int pos = 0;
//                        for (byte[] arr : audioQueue) {
//                            System.arraycopy(arr, 0, stream, pos, arr.length);
//                            pos += arr.length;
//                        }
////                        if(index == 1) transcriber.send(stream, totalLength);
//                        long currentTime = System.currentTimeMillis();
////                        if(currentTime - lastTime >= SAMPLE_DURATION) {
////                            lastTime = currentTime;
//                        if(index == 1) {
//                            transcriber = generateTranscriber(request, voipCallDTO, transcriptionSpeakerType);
//                            transcriber.start();
//                        }
//                        transcriber.send(stream, totalLength);
//                        audioQueue.clear();
//                        index++;
////                        }
//                        audioBuffer = KVSUtils.getByteBufferFromStream(kvsStreamTrackObject.getStreamingMkvReader(),
//                                kvsStreamTrackObject.getFragmentVisitor(), kvsStreamTrackObject.getTagProcessor(), request.getConnectPhoneCallId(), kvsStreamTrackObject.getTrackName());
//                    }
//                    transcriber.stop();
//                }
//                catch (Exception e) {
//                    log.error(e.getMessage());
////                    throw new RuntimeException(e);
//                }
//            }
//            catch (Exception e) {
//                log.error(e.getMessage());
//            }
//            finally {
//                if(transcriber != null) transcriber.close();
//            }
//        });
//    }








    @Deprecated
    @Override
    public String aliCloudFastVoicemailTranscription(byte[] audioData) throws IOException {
        AccessToken accessToken = new AccessToken(properties.getAliCloudAccessKey(), properties.getAliCloudSecretKey());
        accessToken.apply();
        String url = properties.getAliCloudFileTranscriptionEndPoint() +
                "?appkey=" + properties.getAliCloudAppKey() +
                "&token=" + accessToken.getToken() +
                "&format=" + WAV_TYPE +
                "&sample_rate=" + SAMPLE_RATE +
                "&first_channel_only=" + true;
        RequestBody requestBody = RequestBody.create(audioData, MediaType.parse("application/octet-stream"));
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .build();
        try (Response response = this.okHttpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) log.error("alicloud retrieve audio file transcription error: {}", response);
            if(response.body() == null) log.error("lark service retrieve token error. The Get Token return null");
            JSONObject jsonResponse = new JSONObject(response.body().string());
            JSONArray sentences = jsonResponse.getJSONArray("flash_result");
            StringBuilder combinedText = new StringBuilder();
            for (int i = 0; i < sentences.size(); i++) {
                JSONObject sentence = sentences.getJSONObject(i);
                combinedText.append(sentence.getStr("text"));
            }
            return combinedText.toString();
        }
        catch (IOException e) {
            log.error("lark service retrieve token error, stack: {}", ExceptionUtils.getStackTrace(e));
        }
        return "";
    }


    //only for demo

    // 地域ID，常量，固定值。
    public static final String REGIONID = "cn-shanghai";
    public static final String ENDPOINTNAME = "cn-shanghai";
    public static final String PRODUCT = "nls-filetrans";
    public static final String DOMAIN = "filetrans.cn-shanghai.aliyuncs.com";
    public static final String API_VERSION = "2018-08-17";  // 中国站版本
    public static final String POST_REQUEST_ACTION = "SubmitTask";
    public static final String GET_REQUEST_ACTION = "GetTaskResult";
    // 请求参数
    public static final String KEY_APP_KEY = "appkey";
    public static final String KEY_FILE_LINK = "file_link";
    public static final String KEY_VERSION = "version";
    public static final String KEY_ENABLE_WORDS = "enable_words";
    // 响应参数
    public static final String KEY_TASK = "Task";
    public static final String KEY_TASK_ID = "TaskId";
    public static final String KEY_STATUS_TEXT = "StatusText";
    public static final String KEY_RESULT = "Result";
    // 状态值
    public static final String STATUS_SUCCESS = "SUCCESS";
    private static final String STATUS_RUNNING = "RUNNING";
    private static final String STATUS_QUEUEING = "QUEUEING";


    @Deprecated
    @Override
    public String aliCloudVoicemailTranscription(String fileLink) throws IOException, ClientException {
        DefaultProfile.addEndpoint(ENDPOINTNAME, REGIONID, PRODUCT, DOMAIN);
        DefaultProfile profile = DefaultProfile.getProfile(REGIONID, properties.getAliCloudAccessKey(), properties.getAliCloudSecretKey());
        this.iAcsClient = new DefaultAcsClient(profile);

        //create transcribing task
        CommonRequest postRequest = new CommonRequest();
        postRequest.setDomain(DOMAIN);
        postRequest.setVersion(API_VERSION);
        postRequest.setAction(POST_REQUEST_ACTION);
        postRequest.setProduct(PRODUCT);
        JSONObject taskObject = new JSONObject();
        taskObject.put(KEY_APP_KEY, properties.getAliCloudAppKey());
        taskObject.put(KEY_FILE_LINK, fileLink);
        // 新接入请使用4.0版本，已接入（默认2.0）如需维持现状，请注释掉该参数设置。
        taskObject.put(KEY_VERSION, "4.0");
        String task = taskObject.toString();
        System.out.println(task);
        // 设置以上JSON字符串为Body参数。
        postRequest.putBodyParameter(KEY_TASK, task);
        // 设置为POST方式的请求。
        postRequest.setMethod(MethodType.POST);
        // postRequest.setHttpContentType(FormatType.JSON);    //当aliyun-java-sdk-core 版本为4.6.0及以上时，请取消该行注释

        String taskId = null;
        try {
            CommonResponse postResponse = iAcsClient.getCommonResponse(postRequest);
            System.err.println("提交录音文件识别请求的响应：" + postResponse.getData());
            if (postResponse.getHttpStatus() == 200) {
                JSONObject result =  JSONUtil.parseObj(postResponse.getData());
                String statusText = result.getStr(KEY_STATUS_TEXT);
                if (STATUS_SUCCESS.equals(statusText)) {
                    taskId = result.getStr(KEY_TASK_ID);
                }
            }
        } catch (ClientException e) {
            e.printStackTrace();
        }

        //getFileTransResult
        if(taskId != null) {
            CommonRequest getRequest = new CommonRequest();
            getRequest.setDomain(DOMAIN);
            getRequest.setVersion(API_VERSION);
            getRequest.setAction(GET_REQUEST_ACTION);
            getRequest.setProduct(PRODUCT);
            getRequest.putQueryParameter(KEY_TASK_ID, taskId);
            getRequest.setMethod(MethodType.GET);
            String result = null;
            while (true) {
                try {
                    CommonResponse getResponse = iAcsClient.getCommonResponse(getRequest);
                    System.err.println("识别查询结果：" + getResponse.getData());
                    if (getResponse.getHttpStatus() != 200) {
                        break;
                    }
                    JSONObject rootObj = JSONUtil.parseObj(getResponse.getData());
                    String statusText = rootObj.getStr(KEY_STATUS_TEXT);
                    if (STATUS_RUNNING.equals(statusText) || STATUS_QUEUEING.equals(statusText)) {
                        // 继续轮询，注意设置轮询时间间隔。
                        Thread.sleep(5000);
                    }
                    else {
                        // 状态信息为成功，返回识别结果；状态信息为异常，返回空。
                        if (STATUS_SUCCESS.equals(statusText)) {
                            JSONObject res = rootObj.getJSONObject(KEY_RESULT);
                            JSONArray sentencesArray = res.getJSONArray("Sentences");
                            result = sentencesArray.stream()
                                    .map(obj -> ((JSONObject) obj).getStr("Text"))
                                    .reduce("", String::concat);

                            // 状态信息为成功，但没有识别结果，则可能是由于文件里全是静音、噪音等导致识别为空。
                            if(result == null) {
                                result = "";
                            }
                        }
                        break;
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
            return result;
        }
        return "";
    }


}
