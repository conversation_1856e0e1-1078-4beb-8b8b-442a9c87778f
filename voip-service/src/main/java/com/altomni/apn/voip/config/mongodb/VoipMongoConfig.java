package com.altomni.apn.voip.config.mongodb;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;

@Configuration
public class VoipMongoConfig {

    @Value("${spring.data.voipMongo.uri}")
    private String voipMongoUri;

    @Bean(name = "voipMongoTemplate")
    public MongoTemplate voipMongoTemplate() {
        ConnectionString connectionString = new ConnectionString(voipMongoUri);
        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(connectionString)
                .build();
        MongoClient mongoClient = MongoClients.create(settings);

        String databaseName = connectionString.getDatabase();
        if (databaseName == null) {
            throw new IllegalArgumentException("Database name must be specified in URI");
        }
        return new MongoTemplate(mongoClient, databaseName);
    }
}
