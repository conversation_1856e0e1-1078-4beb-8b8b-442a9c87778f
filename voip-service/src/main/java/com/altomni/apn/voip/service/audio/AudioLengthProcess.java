package com.altomni.apn.voip.service.audio;

import java.util.Queue;

public class AudioLengthProcess {

    private static final int SAMPLE_RATE = 16000; // 采样率，例如16000Hz
    private static final int BYTES_PER_SAMPLE = 2; // 每个样本的字节数，16位PCM为2字节
    private static final double SECONDS_TO_PROCESS = 2; // 每次处理的秒数
    public static boolean shouldProcessAudioQueue(Queue<byte[]> audioQueue) {
        // 计算1秒内的音频数据字节数
        int bytesPerSecond = SAMPLE_RATE * BYTES_PER_SAMPLE;
        int accumulatedBytes = 0;
        // 遍历音频队列累积字节长度
        for (byte[] frame : audioQueue) {
            accumulatedBytes += frame.length;
            if (accumulatedBytes >= bytesPerSecond * SECONDS_TO_PROCESS) {
                // 如果累积的字节长度达到或超过了n秒的音频数据，准备处理
                return true;
            }
        }
        return false; // 如果累积的数据还没有达到1秒，继续累积
    }

}
