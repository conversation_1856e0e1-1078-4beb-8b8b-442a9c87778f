package com.altomni.apn.voip.service.job;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.dto.search.SearchConditionDTO;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.net.URISyntaxException;
import java.util.List;

@Component
@FeignClient(value = "job-service")
public interface JobService {

    @PostMapping(value = "/job/api/v3/jobs/search", consumes = "application/json; charset=utf-8")
    ResponseEntity<String> searchJob(@RequestBody SearchConditionDTO searchDTO, Pageable pageable) throws Throwable;

    @PostMapping(value = "/job/api/v3/brief-jobs/ids", consumes = "application/json; charset=utf-8")
    ResponseEntity<List<JobBriefDTO>> getBriefJobListByIds(@RequestBody List<Long> ids);

}
