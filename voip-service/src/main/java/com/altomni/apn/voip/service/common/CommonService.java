package com.altomni.apn.voip.service.common;

import com.altomni.apn.common.dto.message.MessageCreateWithVoicemailDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.io.IOException;


@Component
@FeignClient(value = "common-service")
public interface CommonService {

    @PostMapping("/common/api/v3/message/voicemail")
    ResponseEntity<Void> createMessageWithVoicemail(@RequestBody MessageCreateWithVoicemailDTO messageCreateWithVoicemailDTO);

    @GetMapping(value = "/common/api/v3/voip/count-by-talent", produces = {MediaType.APPLICATION_JSON_VALUE})
    ResponseEntity<String> countVoipTalentNoticeEmail(@RequestParam("talentId") Long talentId) throws IOException;



}
