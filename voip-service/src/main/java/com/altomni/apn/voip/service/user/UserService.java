package com.altomni.apn.voip.service.user;


import com.altomni.apn.common.dto.user.UserBriefDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@Component
@FeignClient(value = "user-service")
public interface UserService {

    @GetMapping("/user/api/v3/users/find-by-email")
    ResponseEntity<UserBriefDTO> findByEmail(@RequestParam("email") String email);

    @GetMapping("/user/api/v3/users/find-by-id")
    ResponseEntity<UserBriefDTO> findById(@RequestParam("id") Long id);

    @GetMapping("/user/api/v3/permissions/teams/user/{userId}/primary_team_id")
    ResponseEntity<Long> getPrimaryTeamIdByUserId(@PathVariable("userId") Long userId);

    @PostMapping("/user/api/v3/permissions/teams/users/all-status-team-user-ids")
    ResponseEntity<Set<Long>> getAllTeamUserIdsByPermissionTeamIdIn(@RequestBody Set<Long> teamIds);

    @PostMapping("/user/api/v3/users/all-brief-by-ids")
    ResponseEntity<List<UserBriefDTO>> getBriefUsersByIds(@RequestBody List<Long> ids);

}
