package com.altomni.apn.voip.service.alicloud;

import com.alibaba.nls.client.protocol.asr.SpeechTranscriber;
import com.aliyuncs.exceptions.ClientException;
import com.altomni.apn.common.domain.enumeration.voip.TranscriptionModel;
import com.altomni.apn.voip.web.rest.vm.voip.LiveCaptionRequest;

import java.io.IOException;

public interface AliCloudASRService {

    SpeechTranscriber generateTranscriber(Long userId, Long tenantId, TranscriptionModel model) throws Exception;

    void sendVoiceAudioToTranscribe(byte[] audioData, TranscriptionModel model, Boolean isEnd);

    @Deprecated
    String aliCloudFastVoicemailTranscription(byte[] audioData) throws IOException;

    @Deprecated
    String aliCloudVoicemailTranscription(String fileLink) throws IOException, ClientException;

}
