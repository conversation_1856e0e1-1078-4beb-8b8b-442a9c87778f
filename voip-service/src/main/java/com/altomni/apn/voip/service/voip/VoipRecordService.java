package com.altomni.apn.voip.service.voip;

import com.altomni.apn.common.dto.voip.VoipReportDTO;
import com.altomni.apn.common.vo.voip.VoipContactDetailVO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.vo.dict.EnumDictVO;
import com.altomni.apn.voip.domain.enumeration.CallPlatform;
import com.altomni.apn.voip.service.dto.voip.PhoneRecordDeleteDTO;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.voip.service.vo.voiptalent.VoipTalentContactVO;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Set;

public interface VoipRecordService {

    VoipContactDTO recordVoipContact(VoipContactDTO voipContactDTO);

    VoipContactDTO updateVoipContactPhoneCallingStatus(VoipContactDTO voipContactDTO);

    VoipContactDTO getVoipContact(String phoneCallId);

    VoipContactDetailVO getVoipContactDetail(String phoneCallId);

    List<VoipContactDetailVO> getVoipContactDetails(Set<String> phoneCallIds);

    VoipContactDTO getLastVoipContact();

    VoipTalentContactVO getVoipTalentContactVO(Long talentId, CallPlatform callPlatform) throws IOException;

    EnumDictVO getCallTypeByTalentAndJob(Long talentId, Long jobId, SortType sortType) throws URISyntaxException;

    VoipContactDTO updateVoipContactRecordDelete(PhoneRecordDeleteDTO phoneRecordDeleteDTO);

    void updateVoipContactRecord(VoipContactDTO voipContactDTO);

    List<VoipContactDTO> findAllByVoipReport(VoipReportDTO voipReportDTO);

    List<VoipContactDTO> findAllByUserId(Long userId);

}
