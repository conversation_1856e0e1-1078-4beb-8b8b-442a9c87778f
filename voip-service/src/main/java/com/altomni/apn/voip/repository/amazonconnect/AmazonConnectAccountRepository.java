package com.altomni.apn.voip.repository.amazonconnect;

import com.altomni.apn.voip.domain.amazonconnect.AmazonConnectAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface AmazonConnectAccountRepository extends JpaRepository<AmazonConnectAccount, Long> {

    Optional<AmazonConnectAccount> findByAccountId(String accountId);

    Optional<AmazonConnectAccount> findByUserId(Long userId);

    void deleteByUserId(Long userId);

}


