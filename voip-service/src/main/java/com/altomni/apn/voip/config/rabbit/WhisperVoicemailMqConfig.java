package com.altomni.apn.voip.config.rabbit;

import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@RefreshScope
public class WhisperVoicemailMqConfig {

    @Value("${application.whisperVoicemail.host}")
    private String addresses;

    @Value("${application.whisperVoicemail.port}")
    private Integer port;

    @Value("${application.whisperVoicemail.virtual-host:/}")
    private String virtualHost;

    @Value("${application.whisperVoicemail.username}")
    private String username;

    @Value("${application.whisperVoicemail.password}")
    private String password;

    @Bean(name = "whisperVoicemailConnectionFactory")
    @Primary
    public ConnectionFactory whisperVoicemailNotificationFactory() {
        return connectionFactory (addresses, port, virtualHost, username, password);
    }

    public CachingConnectionFactory connectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        return connectionFactory;
    }


    @Bean(name = "whisperVoicemailFactory")
    public SimpleRabbitListenerContainerFactory secondFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("whisperVoicemailConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, connectionFactory);
        return factory;
    }



    @Bean(name = "whisperVoicemailRabbitAdmin")
    public RabbitAdmin whisperVoicemailRabbitAdmin(@Qualifier("whisperVoicemailConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        rabbitAdmin.setAutoStartup(true);
        return rabbitAdmin;
    }

    @Bean(name = "whisperVoicemailRabbitTemplate")
    public RabbitTemplate voipRabbitTemplate(
            @Qualifier("whisperVoicemailConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate voipRabbitTemplate = new RabbitTemplate(connectionFactory);
        voipRabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return voipRabbitTemplate;
    }

}
