package com.altomni.apn.voip.service.mapper.amazonconnect;
import com.altomni.apn.voip.domain.amazonconnect.AmazonConnectAccount;
import com.altomni.apn.voip.service.dto.amazonconnect.AmazonConnectAccountDTO;
import com.altomni.apn.voip.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity Tag and its DTO TagDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface AmazonConnectAccountMapper extends EntityMapper<AmazonConnectAccountDTO, AmazonConnectAccount> {

    default AmazonConnectAccount fromId(Long id) {
        if (id == null) {
            return null;
        }
        AmazonConnectAccount amazonConnectAccount = new AmazonConnectAccount();
        amazonConnectAccount.setId(id);
        return amazonConnectAccount;
    }
}
