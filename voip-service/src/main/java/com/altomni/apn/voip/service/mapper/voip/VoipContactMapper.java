package com.altomni.apn.voip.service.mapper.voip;
import com.altomni.apn.common.domain.voip.VoipContact;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.voip.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity Tag and its DTO TagDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface VoipContactMapper extends EntityMapper<VoipContactDTO, VoipContact> {

    default VoipContact fromId(Long id) {
        if (id == null) {
            return null;
        }
        VoipContact voipContact = new VoipContact();
        voipContact.setId(id);
        return voipContact;
    }
}
