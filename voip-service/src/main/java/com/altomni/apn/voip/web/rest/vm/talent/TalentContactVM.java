package com.altomni.apn.voip.web.rest.vm.talent;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.ContactTypeConverterV2;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Convert;
import javax.validation.constraints.NotNull;

/**
 * A TalentContact VM.
 */
@ApiModel(description = "Talent's contacts. Talent can have many contact information, from different providers. They are also used to help identify if a talent " +
    "already exists, or should create a new one.")
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public class TalentContactVM {

    @NotNull
    @Convert(converter = ContactTypeConverterV2.class)
    private ContactType type;

    @ApiModelProperty(value = "the actual contact. e.g. Email address, Linkedin ID, etc.", required = true)
    @NotNull
    private String contact;

    public TalentContactVM() {}

    public TalentContactVM(ContactType type, String contact) {
        this.type = type;
        this.contact = contact;
    }
}
