package com.altomni.apn.voip.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;

/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
public class WebSecurityConfig {

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 资源服务器配置
        http.oauth2ResourceServer(auth -> auth.opaqueToken(Customizer.withDefaults()));
        // 没有 token 或者 token 过期，返回 401
        http.exceptionHandling(exceptionHandling -> exceptionHandling.authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint()));
        // 无状态 session
        http.sessionManagement((session) -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        // 资源权限配置，所有请求都需要认证
        http.csrf().disable().authorizeHttpRequests(
                authorizationManagerRequestMatcherRegistry -> authorizationManagerRequestMatcherRegistry
                        .requestMatchers("/api/v3/liveness").permitAll()
                        .requestMatchers("/actuator/**").permitAll()
                        .requestMatchers("/api/v3/connect/live-transcription/lark").permitAll()
                        .requestMatchers("/api/v3/connect/live-transcription/aliCloud").permitAll()
                        .requestMatchers("/api/v3/connect/live-transcription/voicemail/notification").permitAll()
                        .requestMatchers("/api/v3/voice-mail/check-valid-user").permitAll()
                        .anyRequest().authenticated());
        return http.build();
    }

}