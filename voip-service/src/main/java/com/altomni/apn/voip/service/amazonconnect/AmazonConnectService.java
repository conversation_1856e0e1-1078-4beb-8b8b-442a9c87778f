package com.altomni.apn.voip.service.amazonconnect;

import com.altomni.apn.voip.service.dto.amazonconnect.*;
import com.altomni.apn.voip.service.dto.voip.PhoneRecordDeleteDTO;
import com.altomni.apn.voip.service.vo.amazonconnect.AmazonConnectMetricStatusVO;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

public interface AmazonConnectService {

    AmazonConnectAccountDTO createAmazonConnectUser(AmazonConnectAccountDTO amazonConnectAccountDTO);

    void deleteAmazonConnectUser(Long userId);

    List<AmazonConnectInstanceDTO> getAmazonConnectInstanceList();

    List<AmazonConnectRoutingProfileDTO> getAmazonConnectRoutingProfileList(String instanceId);

    List<AmazonConnectSecurityProfileDTO> getAmazonConnectSecurityProfileList(String instanceId);

    ResponseEntity<String> getAmazonConnectRecording(String phoneCallId);

    ResponseEntity<Void> deleteAmazonConnectRecording(PhoneRecordDeleteDTO phoneRecordDeleteDTO);

    ResponseEntity<AmazonConnectMetricStatusVO>  checkAmazonConnectMetricsStatus();

    ResponseEntity<Void> recordUserVoipAmazonConnectCall(AmazonConnectVoipCallingStatusRequestDTO request);

}
