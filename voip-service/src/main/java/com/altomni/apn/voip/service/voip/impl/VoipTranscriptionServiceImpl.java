package com.altomni.apn.voip.service.voip.impl;

import com.altomni.apn.common.domain.enumeration.voip.TranscriptionSpeakerType;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import com.altomni.apn.common.domain.voip.PhoneTranscription;
import com.altomni.apn.common.domain.voip.VoipContact;
import com.altomni.apn.voip.repository.job.JobBriefRepository;
import com.altomni.apn.voip.repository.phonetranscription.PhoneTranscriptionRepository;
import com.altomni.apn.voip.repository.voip.VoipContactRepository;
import com.altomni.apn.voip.service.dto.voip.PhoneTranscriptionDTO;
import com.altomni.apn.voip.service.talent.TalentService;
import com.altomni.apn.voip.service.user.UserService;
import com.altomni.apn.voip.service.voip.VoipTranscriptionService;
import com.altomni.apn.common.service.voipserver.VoipProxyAPIHttp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.altomni.apn.voip.utils.http.HttpUtils.formatEndpointPath;

@Slf4j
@Service
@Transactional
public class VoipTranscriptionServiceImpl implements VoipTranscriptionService {

    private final VoipProxyAPIHttp voipProxyAPIHttp;

    private final PhoneTranscriptionRepository phoneTranscriptionRepository;

    private final VoipContactRepository voipContactRepository;

    private final UserService userService;

    private final JobBriefRepository jobBriefRepository;

    private final TalentService talentService;

    private static final String TRANSCRIPTIONS_RECORD_GET = "/api/v1/connect/transcriptions-record/{}";

    public VoipTranscriptionServiceImpl(VoipProxyAPIHttp voipProxyAPIHttp, PhoneTranscriptionRepository phoneTranscriptionRepository,
                                        VoipContactRepository voipContactRepository, UserService userService, JobBriefRepository jobBriefRepository, TalentService talentService) {
        this.voipProxyAPIHttp = voipProxyAPIHttp;
        this.phoneTranscriptionRepository = phoneTranscriptionRepository;
        this.voipContactRepository = voipContactRepository;
        this.userService = userService;
        this.jobBriefRepository = jobBriefRepository;
        this.talentService = talentService;
    }



    @Override
    public PhoneTranscriptionDTO getTranscription(String phoneCallId) {

        try {
            ResponseEntity<PhoneTranscriptionDTO> res = voipProxyAPIHttp.proxyApiRequest(formatEndpointPath(TRANSCRIPTIONS_RECORD_GET, phoneCallId), null, HttpMethod.GET, PhoneTranscriptionDTO.class);
            if(res.getBody() == null) {
                return null;
            }
            else {
                PhoneTranscriptionDTO phoneTranscriptionDTO = res.getBody();
                VoipContact voipContact = voipContactRepository.findByPhoneCallId(phoneCallId).orElseThrow(() -> new CustomParameterizedException("No related transcription with phoneCallId: " + phoneCallId));
                UserBriefDTO user = userService.findById(phoneTranscriptionDTO.getUserId()).getBody();
                String userName = user != null ? user.getFullName() : null;
                Set<TalentBriefVO> talentBriefs = talentService.findBriefTalentsByTalentIds(Set.of(phoneTranscriptionDTO.getReferenceCalleeId())).getBody();
                String talentName = (talentBriefs != null && !talentBriefs.isEmpty()) ? talentBriefs.iterator().next().getFullName() : null ;
                phoneTranscriptionDTO.getTranscriptions().forEach(transcriptionDTO -> {
                    if (transcriptionDTO.getSpeakerType().equals(TranscriptionSpeakerType.RECRUITER)) {
                        transcriptionDTO.setSpeaker(userName);
                    } else {
                        transcriptionDTO.setSpeaker(talentName);
                    }
                });
                phoneTranscriptionDTO.setJobId(voipContact.getJobId());
                JobBriefDTO job = jobBriefRepository.findBriefJobsById(voipContact.getJobId());
                phoneTranscriptionDTO.setJobTitle(job != null ? job.getTitle() : null);
                return phoneTranscriptionDTO;
            }
        }
        catch (Exception e) {
            log.error("Error fetching transcription for phoneCallId {}: {}", phoneCallId, e.getMessage());
            throw new CustomParameterizedException("Error fetching transcription for phoneCallId: " + phoneCallId);
        }
    }

    @Override
    public List<PhoneTranscription> findAllPhoneTranscriptionByPhoneCallIds(Set<String> phoneCallIds) {
        List<PhoneTranscription> phoneTranscriptions = phoneTranscriptionRepository.getPhoneTranscriptionsByPhoneCallIdsIn(phoneCallIds);
        return phoneTranscriptions;
    }

}


