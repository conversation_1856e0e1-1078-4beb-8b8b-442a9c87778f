package com.altomni.apn.voip.utils.mapping;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class ObjectMappingUtils {
    public static Map<String, Object> objectToMap(Object obj) throws IllegalAccessException {
        Map<String, Object> map = new HashMap<>();
        Class<?> clazz = obj.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            map.put(field.getName(), field.get(obj));
        }
        return map;
    }
}
