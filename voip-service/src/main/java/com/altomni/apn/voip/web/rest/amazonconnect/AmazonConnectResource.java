package com.altomni.apn.voip.web.rest.amazonconnect;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.voip.service.dto.amazonconnect.*;
import com.altomni.apn.voip.service.amazonconnect.AmazonConnectService;
import com.altomni.apn.voip.service.vo.amazonconnect.AmazonConnectMetricStatusVO;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * REST controller for managing Amazon Connect.
 */
@Api(tags = {"APN-AmazonConnect"})
@RestController
@RequestMapping("/api/v3")
public class AmazonConnectResource {

    private final Logger log = LoggerFactory.getLogger(AmazonConnectResource.class);

    private static final String ENTITY_NAME = "amazonConnect";

    private final AmazonConnectService amazonConnectService;


    public AmazonConnectResource(AmazonConnectService amazonConnectService) {
        this.amazonConnectService = amazonConnectService;
    }

    @Deprecated
    @NoRepeatSubmit
    @PostMapping("/amazon-connect/create-user")
    public ResponseEntity<AmazonConnectAccountDTO> createUser(@RequestBody AmazonConnectAccountDTO accountDTO) {
        log.info("[APN: Amazon Connect @{}] REST request to create amazon connect user: {}", SecurityUtils.getUserId(), accountDTO);
        AmazonConnectAccountDTO newAccount = amazonConnectService.createAmazonConnectUser(accountDTO);
        return ResponseEntity.ok().body(newAccount);
    }

    @Deprecated
    @NoRepeatSubmit
    @DeleteMapping("/amazon-connect/delete-user/{userId}")
    public ResponseEntity<Void> deleteUser(@PathVariable Long userId) {
        log.info("[APN: Amazon Connect @{}] REST request to delete amazon connect user by id: {}", SecurityUtils.getUserId(), userId);
        amazonConnectService.deleteAmazonConnectUser(userId);
        return ResponseEntity.ok().build();
    }

    @Deprecated
    @NoRepeatSubmit
    @GetMapping("/amazon-connect/instances")
    public ResponseEntity<List<AmazonConnectInstanceDTO>> getAmazonConnectInstanceList() {
        log.info("[APN: Amazon Connect @{}] REST request to get amazon connect instances", SecurityUtils.getUserId());
        List<AmazonConnectInstanceDTO> instances = amazonConnectService.getAmazonConnectInstanceList();
        return ResponseEntity.ok().body(instances);
    }

    @Deprecated
    @NoRepeatSubmit
    @GetMapping("/amazon-connect/routing-profiles/{instanceId}")
    public ResponseEntity<List<AmazonConnectRoutingProfileDTO>> getAmazonConnectRoutingProfileList(@PathVariable String instanceId) {
        log.info("[APN: Amazon Connect @{}] REST request to get amazon connect routing profiles by instanceId: {}", SecurityUtils.getUserId(), instanceId);
        List<AmazonConnectRoutingProfileDTO> routingProfiles = amazonConnectService.getAmazonConnectRoutingProfileList(instanceId);
        return ResponseEntity.ok().body(routingProfiles);
    }

    @Deprecated
    @NoRepeatSubmit
    @GetMapping("/amazon-connect/security-profiles/{instanceId}")
    public ResponseEntity<List<AmazonConnectSecurityProfileDTO>> getAmazonConnectSecurityProfileList(@PathVariable String instanceId) {
        log.info("[APN: Amazon Connect @{}] REST request to get amazon connect security profiles by instanceId: {}", SecurityUtils.getUserId(), instanceId);
        List<AmazonConnectSecurityProfileDTO> securityProfiles = amazonConnectService.getAmazonConnectSecurityProfileList(instanceId);
        return ResponseEntity.ok().body(securityProfiles);
    }

    @NoRepeatSubmit
    @GetMapping("/amazon-connect/metrics/status")
    public ResponseEntity<AmazonConnectMetricStatusVO> getAmazonConnectMetricStatus() {
        log.info("[APN: Amazon Connect @{}] REST request to get amazon connect metrics status", SecurityUtils.getUserId());
        return amazonConnectService.checkAmazonConnectMetricsStatus();
    }

    @NoRepeatSubmit
    @PutMapping("/amazon-connect/voip-calling-status/is-calling")
    public ResponseEntity<Void> updateAmazonConnectVoipCallingStatus(@RequestBody AmazonConnectVoipCallingStatusRequestDTO request) {
        return amazonConnectService.recordUserVoipAmazonConnectCall(request);
    }
}
