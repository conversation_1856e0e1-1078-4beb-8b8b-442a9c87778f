<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264879858-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="voip_contact"/>
            </not>
        </preConditions>
        <createTable tableName="voip_contact">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="contact_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>

            <column name="instance_id" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>

            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>

            <column name="user_name" type="VARCHAR(255)" />

            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>

            <column name="talent_name" type="VARCHAR(255)" />

            <column name="phone_number" type="VARCHAR(255)" />

            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>

            <column name="transcription_model" type="int" />

            <column name="phone_call_status" type="int" />

            <column name="call_type_id" type="bigint" />

            <column name="call_result_id" type="bigint" />

            <column name="job_id" type="bigint" />

            <column name="is_record_delete" type="boolean" defaultValueBoolean="false">
                <constraints nullable="false" />
            </column>

            <column name="summary" type="TEXT" />


            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

    </changeSet>

</databaseChangeLog>
