<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263860013-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="oauth_client_details"/>
            </not>
        </preConditions>
        <createTable tableName="oauth_client_details">
            <column name="client_id" type="VARCHAR(255)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="resource_ids" type="VARCHAR(255)"/>
            <column name="client_secret" type="VARCHAR(255)"/>
            <column name="scope" type="VARCHAR(255)"/>
            <column name="authorized_grant_types" type="VARCHAR(255)"/>
            <column name="web_server_redirect_uri" type="VARCHAR(255)"/>
            <column name="authorities" type="VARCHAR(255)"/>
            <column name="access_token_validity" type="INT"/>
            <column name="refresh_token_validity" type="INT"/>
            <column name="additional_information" type="VARCHAR(4000)"/>
            <column name="autoapprove" type="VARCHAR(4000)"/>
        </createTable>

    </changeSet>
</databaseChangeLog>
