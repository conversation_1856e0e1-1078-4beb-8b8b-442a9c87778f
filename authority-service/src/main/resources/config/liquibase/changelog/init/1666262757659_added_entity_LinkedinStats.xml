<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263830328-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="linkedin_stats"/>
            </not>
        </preConditions>
        <createTable tableName="linkedin_stats">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" type="BIGINT"/>
            <column name="linkedin" type="VARCHAR(128)"/>
            <column name="count" type="INT"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="type" type="INT"/>
            <column name="tenant_id" type="BIGINT"/>
        </createTable>

        <addUniqueConstraint columnNames="user_id, linkedin" constraintName="idx_linkedin_stats_user_id_linkedin"
                             tableName="linkedin_stats"/>
        <createIndex indexName="idx_linkedin_stats_tenant_id_type_created_date_user_id" tableName="linkedin_stats">
            <column name="tenant_id"/>
            <column name="type"/>
            <column name="created_date"/>
            <column name="user_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
