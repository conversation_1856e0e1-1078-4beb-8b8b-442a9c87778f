<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263820514-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="linkedin_project_talent"/>
            </not>
        </preConditions>
        <createTable tableName="linkedin_project_talent">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="linkedin_project_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="linkedin_talent_id" type="VARCHAR(65)">
                <constraints nullable="false"/>
            </column>
            <column name="candidate_status" type="INT"/>
            <column name="contact_status" type="INT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_linkedin_project_talent_lkpid" tableName="linkedin_project_talent">
            <column name="linkedin_project_id"/>
        </createIndex>
        <createIndex indexName="idx_linkedin_project_talent_lktid" tableName="linkedin_project_talent">
            <column name="linkedin_talent_id"/>
        </createIndex>
        <createIndex indexName="idx_linkedin_project_talent_tenant_id" tableName="linkedin_project_talent">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
