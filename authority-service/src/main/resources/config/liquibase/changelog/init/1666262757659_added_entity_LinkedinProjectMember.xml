<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263810635-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="linkedin_project_member"/>
            </not>
        </preConditions>
        <createTable tableName="linkedin_project_member">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="linkedin_project_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_linkedin_project_member_lpid" tableName="linkedin_project_member">
            <column name="linkedin_project_id"/>
        </createIndex>
        <createIndex indexName="idx_linkedin_project_member_uid" tableName="linkedin_project_member">
            <column name="user_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
