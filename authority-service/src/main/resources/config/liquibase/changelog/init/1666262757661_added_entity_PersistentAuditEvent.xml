<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264084377-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="persistent_audit_event"/>
            </not>
        </preConditions>
        <createTable tableName="persistent_audit_event">
            <column autoIncrement="true" name="event_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="principal" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="event_date" type="timestamp"/>
            <column name="event_type" type="VARCHAR(255)"/>
        </createTable>

        <createIndex indexName="idx_persistent_audit_event" tableName="persistent_audit_event">
            <column name="principal"/>
            <column name="event_date"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
