<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264096534-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="persistent_audit_evt_data"/>
            </not>
        </preConditions>
        <createTable tableName="persistent_audit_evt_data">
            <column name="event_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="name" type="VARCHAR(150)">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="value" type="VARCHAR(255)"/>
        </createTable>

        <createIndex indexName="idx_persistent_audit_evt_data" tableName="persistent_audit_evt_data">
            <column name="event_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
