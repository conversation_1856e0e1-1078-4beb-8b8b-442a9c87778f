<?xml version='1.0' encoding='utf-8'?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <include file="config/liquibase/changelog/init/1666262757659_added_entity_LinkedinProject.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_LinkedinProjectMember.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_LinkedinProjectTalent.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_LinkedinStats.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_LinkedinTalent.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_LinkedinTalentContact.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_OauthClientDetails.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_PersistentAuditEvent.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_PersistentAuditEvtData.xml" relativeToChangelogFile="false"/>

</databaseChangeLog>