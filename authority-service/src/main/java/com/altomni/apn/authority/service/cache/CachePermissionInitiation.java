package com.altomni.apn.authority.service.cache;

import com.altomni.apn.common.datapermission.db.DataPermissionDatabaseHandler;
import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.dto.permission.PermissionTableDTO;
import com.altomni.apn.common.dto.permission.PermissionTeamCodeDTO;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.repository.permission.PermissionRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class CachePermissionInitiation {

    @Resource
    private PermissionRepository permissionRepository;

    @Value("${application.permission.privilegeIdForPrivateJob}")
    private Long privilegeIdForPrivateJob;

    private TeamDataPermissionRespDTO loadDataPermission(Long userId, Long tenantId, Module module){
        TeamDataPermissionRespDTO teamDataPermissionRespDTO = new TeamDataPermissionRespDTO();
        boolean hasPrivateJobPermission = Boolean.FALSE;
        List<Long> jobProjectIds = new ArrayList<>();
        Integer maxDataScopeByUserId = 0;
        Integer userDataScopeById = 0;
        if (module == Module.CLIENT_CONTACT){
            maxDataScopeByUserId = permissionRepository.getMaxClientContactDataScopeByUserId(userId);
            userDataScopeById = permissionRepository.findUserClientContactDataScopeById(userId);
        }else if (module == Module.REPORT){
            jobProjectIds = permissionRepository.findAllJobProjectIdsByTenantId(tenantId);
            if (CollectionUtils.isNotEmpty(jobProjectIds)){
                teamDataPermissionRespDTO.setTeamIdForPrivateJob(jobProjectIds.get(0));
            }
            hasPrivateJobPermission = CollectionUtils.isNotEmpty(permissionRepository.existPrivateJobPermission(userId, privilegeIdForPrivateJob));
            teamDataPermissionRespDTO.setPrivateJobPermission(hasPrivateJobPermission);
            maxDataScopeByUserId = permissionRepository.getMaxReportDataScopeByUserId(userId);
            userDataScopeById = permissionRepository.findUserReportDataScopeById(userId);
        }else if (module == Module.HOME_AND_CALENDAR){
            jobProjectIds = permissionRepository.findAllJobProjectIdsByTenantId(tenantId);
            if (CollectionUtils.isNotEmpty(jobProjectIds)){
                teamDataPermissionRespDTO.setTeamIdForPrivateJob(jobProjectIds.get(0));
            }
            hasPrivateJobPermission = CollectionUtils.isNotEmpty(permissionRepository.existPrivateJobPermission(userId, privilegeIdForPrivateJob));
            teamDataPermissionRespDTO.setPrivateJobPermission(hasPrivateJobPermission);
            maxDataScopeByUserId = permissionRepository.getMaxHomeAndCalendarDataScopeByUserId(userId);
            userDataScopeById = permissionRepository.findUserHomeAndCalendarDataScopeById(userId);
        }else if (module == Module.CHINA_INVOICING){
            jobProjectIds = permissionRepository.findAllJobProjectIdsByTenantId(tenantId);
            if (CollectionUtils.isNotEmpty(jobProjectIds)){
                teamDataPermissionRespDTO.setTeamIdForPrivateJob(jobProjectIds.get(0));
            }
            hasPrivateJobPermission = CollectionUtils.isNotEmpty(permissionRepository.existPrivateJobPermission(userId, privilegeIdForPrivateJob));
            teamDataPermissionRespDTO.setPrivateJobPermission(hasPrivateJobPermission);
            maxDataScopeByUserId = permissionRepository.getMaxChinaInvoicingDataScopeByUserId(userId);
            userDataScopeById = permissionRepository.findUserChinaInvoicingDataScopeById(userId);
        }else if (module == Module.CANDIDATE_PIPELINE_MANAGEMENT) {
            jobProjectIds = permissionRepository.findAllJobProjectIdsByTenantId(tenantId);
            if (CollectionUtils.isNotEmpty(jobProjectIds)) {
                teamDataPermissionRespDTO.setTeamIdForPrivateJob(jobProjectIds.get(0));
            }
            hasPrivateJobPermission = CollectionUtils.isNotEmpty(permissionRepository.existPrivateJobPermission(userId, privilegeIdForPrivateJob));
            teamDataPermissionRespDTO.setPrivateJobPermission(hasPrivateJobPermission);
            maxDataScopeByUserId = permissionRepository.getMaxCandidatePipelineManagementDataScopeByUserId(userId);
            userDataScopeById = permissionRepository.findUserCandidatePipelineManagementDataScopeById(userId);
        }else {
            jobProjectIds = permissionRepository.findAllJobProjectIdsByTenantId(tenantId);
            if (CollectionUtils.isNotEmpty(jobProjectIds)){
                teamDataPermissionRespDTO.setTeamIdForPrivateJob(jobProjectIds.get(0));
            }
            hasPrivateJobPermission = CollectionUtils.isNotEmpty(permissionRepository.existPrivateJobPermission(userId, privilegeIdForPrivateJob));
            teamDataPermissionRespDTO.setPrivateJobPermission(hasPrivateJobPermission);
            maxDataScopeByUserId = permissionRepository.getMaxDataScopeByUserId(userId);
            userDataScopeById = permissionRepository.findUserDataScopeById(userId);
        }

        if (Objects.isNull(maxDataScopeByUserId)){
            maxDataScopeByUserId = -1;
        }
        if (Objects.isNull(userDataScopeById)){
            userDataScopeById = -1;
        }
        switch (Math.max(maxDataScopeByUserId, userDataScopeById)){
            case 99:
                // DataScope.PERMISSION_ALL
                teamDataPermissionRespDTO.setAll(true);
                break;
            case 3:
                //DataScope.PERMISSION_EXTRA_TEAM
            case 2:
                // DataScope.PERMISSION_TEAM
                // TODO consider Object level security
                teamDataPermissionRespDTO.setSelfPermissionIds(permissionRepository.findJobIdsByAssignedUserId(userId)); // only for job data permission

                Set<Long> teamIds = permissionRepository.findTeamIdsByUserId(userId);
                Set<Long> writableTeamIds = new HashSet<>(teamIds);
                Set<Long> readableTeamIds = new HashSet<>(teamIds);
                Set<Long> allPermissionTeamIds = new HashSet<>(teamIds);

                if (hasPrivateJobPermission){
                    readableTeamIds.addAll(jobProjectIds);
                    writableTeamIds.addAll(jobProjectIds);
                }

                if (maxDataScopeByUserId.equals(3)){
                    Set<Long> allTeamIdsByUserId = permissionRepository.getAllTeamIdsFromExtraRoleByUserId(userId, module.toDbValue());
                    Set<Long> writableTeamIdsByUserId = permissionRepository.getWritableTeamIdsFromExtraRoleByUserId(userId, module.toDbValue());
                    writableTeamIds.addAll(writableTeamIdsByUserId);
                    readableTeamIds.addAll(allTeamIdsByUserId);
                    allPermissionTeamIds.addAll(allTeamIdsByUserId);
                }

                if (userDataScopeById.equals(3)){
                    Set<Long> writableTeamIdsByUserId = permissionRepository.getWritableTeamIdsFromExtraUserByUserId(userId, module.toDbValue());
                    Set<Long> allTeamIdsByUserId = permissionRepository.getAllTeamIdsFromExtraUserByUserId(userId, module.toDbValue());
                    writableTeamIds.addAll(writableTeamIdsByUserId);
                    readableTeamIds.addAll(allTeamIdsByUserId);
                    allPermissionTeamIds.addAll(allTeamIdsByUserId);
                }

                teamDataPermissionRespDTO.setWritableTeamIds(writableTeamIds);
                teamDataPermissionRespDTO.setNestedTeamIds(this.getNestedTeamIds(allPermissionTeamIds, tenantId));
                readableTeamIds.addAll(teamDataPermissionRespDTO.getNestedTeamIds());
                teamDataPermissionRespDTO.setReadableTeamIds(readableTeamIds);
                break;
            case 1:
                // DataScope.PERMISSION_SELF
                teamDataPermissionRespDTO.setSelfPermissionIds(permissionRepository.findJobIdsByAssignedUserId(userId)); // only for job data permission
                teamDataPermissionRespDTO.setSelf(true);
                break;
            default:
                break;
        }
        return teamDataPermissionRespDTO;
    }

    private Set<Long> getNestedTeamIds(Set<Long> allPermissionTeamIds, Long tenantId){
        if (CollectionUtils.isEmpty(allPermissionTeamIds)){
            return new HashSet<>();
        }
        List<PermissionTeamCodeDTO> teams = permissionRepository.findByTeamIds(allPermissionTeamIds);
        Set<String> prefixes = new HashSet<>();
        String lastPrefix = "-";
        for (PermissionTeamCodeDTO t : teams) {
            if (!t.getCode().startsWith(lastPrefix)){
                lastPrefix = t.getCode();
                prefixes.add(t.getCode());
            }
        }
        Set<Long> nestedTeamIds = new HashSet<>();
        List<PermissionTeamCodeDTO> allTeams = permissionRepository.findTeamsByTenantId(tenantId);
        for (PermissionTeamCodeDTO t : allTeams) {
            for (String teamCodePrefix : prefixes) {
                if (t.getCode().startsWith(teamCodePrefix)){
                    nestedTeamIds.add(t.getId());
                }
            }
        }
        return nestedTeamIds;
    }

    /**
     * Get data permission by user ID. If not exist in cache, then fetch from db and save in the cache. The cache will expire in 30s?
     * @param userId
     * @return
     */
    @Cacheable(cacheNames = {"Security:data-permission"}, key = "'user:' + #userId")
    public TeamDataPermissionRespDTO getDeptDataPermissionFromCacheOrDB(Long userId, Long tenantId) {
        return this.loadDataPermission(userId, tenantId, Module.JOB);
    }

    @Cacheable(cacheNames = {"Security:data-permission-client-contact"}, key = "'user:' + #userId")
    public TeamDataPermissionRespDTO getClientContactDataPermissionFromCacheOrDB(Long userId, Long tenantId) {
        return this.loadDataPermission(userId, tenantId, Module.CLIENT_CONTACT);
    }

    @Cacheable(cacheNames = {"Security:data-permission-report"}, key = "'user:' + #userId")
    public TeamDataPermissionRespDTO getReportDataPermissionFromCacheOrDB(Long userId, Long tenantId) {
        return this.loadDataPermission(userId, tenantId, Module.REPORT);
    }

    @Cacheable(cacheNames = {"Security:data-permission-home-and-calendar"}, key = "'user:' + #userId")
    public TeamDataPermissionRespDTO getHomeAndCalendarDataPermissionFromCacheOrDB(Long userId, Long tenantId) {
        return this.loadDataPermission(userId, tenantId, Module.HOME_AND_CALENDAR);
    }

    @Cacheable(cacheNames = {"Security:data-permission-china-invoicing"}, key = "'user:' + #userId")
    public TeamDataPermissionRespDTO initiateChinaInvoicingDataPermissionByUserId(Long userId, Long tenantId) {
        return this.loadDataPermission(userId, tenantId, Module.CHINA_INVOICING);
    }

    @Cacheable(cacheNames = {"Security:data-permission-rules"}, key = "'tenant:' + #tenantId")
    public List<TeamDataPermissionRule> getPermissionRulesByTenantIdFromCacheOrDB(Long tenantId){
        List<PermissionTableDTO> involvedPermissionTables = permissionRepository.findInvolvedPermissionByTenantId(tenantId);
        TeamDataPermissionRule teamDataPermissionRule = new TeamDataPermissionRule();
        involvedPermissionTables.forEach(table -> {
            teamDataPermissionRule.addUserColumn(table.getName(), table.getUserOwnerColumn());
            teamDataPermissionRule.addTeamColumn(table.getName(), table.getTeamOwnerColumn());
        });

        List<TeamDataPermissionRule> rules = Arrays.asList(teamDataPermissionRule);
        DataPermissionDatabaseHandler.ContextHolder.init(rules);
        return rules;
    }

    @Cacheable(cacheNames = {"Security:data-permission-candidate-pipeline-management"}, key = "'user:' + #userId")
    public TeamDataPermissionRespDTO getCandidatePipelineManagementDataPermissionFromCacheOrDB(Long userId, Long tenantId) {
        return this.loadDataPermission(userId, tenantId, Module.CANDIDATE_PIPELINE_MANAGEMENT);
    }
}
