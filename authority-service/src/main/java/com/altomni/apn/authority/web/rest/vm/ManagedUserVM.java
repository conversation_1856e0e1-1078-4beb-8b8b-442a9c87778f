package com.altomni.apn.authority.web.rest.vm;

import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.user.service.dto.user.UserDTO;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.constraints.Size;
import java.time.Instant;

/**
 * View Model extending the UserDTO, which is meant to be used in the user management UI.
 */
public class ManagedUserVM extends UserDTO {

    public static final int PASSWORD_MIN_LENGTH = 6;

    public static final int PASSWORD_MAX_LENGTH = 100;

    @ApiModelProperty(value = "password. min length is 6.", required = true)
    @Size(min = PASSWORD_MIN_LENGTH, max = PASSWORD_MAX_LENGTH)
    private String password;

    public ManagedUserVM() {
        // Empty constructor needed for Jackson.
    }

    public ManagedUserVM(Long id, String username, String password, String firstName, String lastName,
                         String email, boolean activated, Long divisionId, String imageUrl, String langKey,
                         String createdBy, Instant createdDate, String lastModifiedBy, Instant lastModifiedDate,
                         Integer credit, Integer bulkCredit, String note) {

        super(id, username, firstName, lastName, email, activated, divisionId, imageUrl, langKey,
                createdBy, createdDate, lastModifiedBy, lastModifiedDate, null, 2L,credit,bulkCredit, note);

        this.password = password;
    }

    public ManagedUserVM(Long id, String username, String password, String firstName, String lastName,
                         String email, boolean activated, Long divisionId, String imageUrl, String langKey,
                         String createdBy, Instant createdDate, String lastModifiedBy, Instant lastModifiedDate,
                         Tenant tenant, Long tenantId, Integer credit, Integer bulkCredit, String note) {

        super(id, username, firstName, lastName, email, activated, divisionId, imageUrl, langKey,
                createdBy, createdDate, lastModifiedBy, lastModifiedDate, tenant, tenantId, credit,bulkCredit, note);

        this.password = password;
    }

    public String getPassword() {
        return password;
    }

    @Override
    public String toString() {
        return "ManagedUserVM{" +
                "} " + super.toString();
    }
}
