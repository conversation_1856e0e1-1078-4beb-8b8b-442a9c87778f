package com.altomni.apn.authority.service.impl;

import com.altomni.apn.user.repository.user.UserAdminRepository;
import com.altomni.apn.authority.service.AbstractAuthorityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * Service class for managing users.
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserAuthorityServiceImpl extends AbstractAuthorityService {

    @Resource
    private UserAdminRepository userAdminRepository;

    @Override
    @Transactional(readOnly = true)
    public Optional getUserWithAuthoritiesByLogin(String login) {
        return userAdminRepository.findOneWithRolesByUsernameOrEmail(login);
    }

}
