package com.altomni.apn.authority.web.rest;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.authority.repository.TokenRepository;
import com.altomni.apn.authority.service.AuthorityService;
import com.altomni.apn.authority.service.SocialAuthorityService;
import com.altomni.apn.authority.web.rest.vm.LoginVM;
import com.altomni.apn.authority.web.rest.vm.RefreshTokenVM;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.user.domain.enumeration.LoginType;
import com.altomni.apn.user.service.dto.user.SocialUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * REST controller for managing the current user's account.
 */

@Api(tags = {"Authority"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class AuthorityResource {

    @Resource
    private AuthorityService authorityServiceImpl;

    @Resource
    private AuthorityService userAuthorityServiceImpl;

    @Resource
    private SocialAuthorityService socialUserService;

    @Resource
    private AuthorityService timeSheetAuthorityServiceImpl;

    @Resource
    private AuthorityService managementAuthorityServiceImpl;

    @Resource
    private TokenRepository tokenRepository;

    @ApiOperation(value = "Get credential", response = CredentialDTO.class)
    @PostMapping(path = "/credential", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<CredentialDTO> getCredential(@RequestBody LoginVM loginVM) {
        CredentialDTO credentialDTO = authorityServiceImpl.findCredential(loginVM);
        return ResponseEntity.ok(credentialDTO);
    }

    @ApiOperation(value = "Get credential for admin", response = CredentialDTO.class)
    @PostMapping(path = "/credential/admin", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<CredentialDTO> getAdminCredential(@RequestBody LoginVM loginVM) {
        CredentialDTO credentialDTO = userAuthorityServiceImpl.findCredential(loginVM);
        return ResponseEntity.ok(credentialDTO);
    }

    @ApiOperation(value = "Get credential for timesheet", response = CredentialDTO.class)
    @PostMapping(path = "/credential/timesheet", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<CredentialDTO> getTimeSheetCredential(@RequestBody LoginVM loginVM) {
        CredentialDTO credentialDTO = timeSheetAuthorityServiceImpl.findCredential(loginVM);
        return ResponseEntity.ok(credentialDTO);
    }

    @ApiOperation(value = "Get credential for management", response = CredentialDTO.class)
    @PostMapping(path = "/credential/management", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<CredentialDTO> getManagementCredential(@RequestBody LoginVM loginVM) {
        CredentialDTO credentialDTO = managementAuthorityServiceImpl.findCredential(loginVM);
        return ResponseEntity.ok(credentialDTO);
    }

    @ApiOperation(value = "Social credential", response = User.class, tags = {"Wechat"})
    @PostMapping(path = "/social/social", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<User> socialLogin(@Valid @RequestBody JSONObject userObj) {
        String provider = userObj.getString("provider");
        SocialUser socialUser;
        if (provider.equals(LoginType.LINKEDIN.name())) {
            socialUser = socialUserService.linkedinLogin(userObj.getString("code"), userObj.getString("redirect_uri"));
        } else {
            throw new CustomParameterizedException("Invalid User Object");
        }
        return new ResponseEntity<>(socialUserService.socialLogin(socialUser), HttpStatus.OK);
    }

    @ApiOperation(value = "Refresh credential", notes = "Use the refresh token to request a new access token when it expired", response = CredentialDTO.class)
    @PostMapping(path = "/refresh-token", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<CredentialDTO> refreshToken(@Valid @RequestBody RefreshTokenVM refreshTokenVM) {
        return new ResponseEntity<>(authorityServiceImpl.refreshToken(refreshTokenVM), HttpStatus.OK);
    }

    /**
     * GET  /authenticate : check if the user is authenticated, and return its login.
     *
     * @param request the HTTP request
     * @return the login if the user is authenticated
     */
    @ApiOperation(value = "Check authentication", notes = "Check is user is authenticated given access token")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authentication", value = "beaer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/authenticate")
    public String isAuthenticated(HttpServletRequest request) {
        log.debug("REST request to check if the current user is authenticated");
        return request.getRemoteUser();
    }

    @ApiOperation(value = "force logout")
    @Transactional(rollbackFor = Exception.class)
    @GetMapping("/logoutByUid")
    public ResponseEntity<Void> logoutByUid(@RequestParam("uid") String uid) {
        //todo
//        if (StringUtils.isNotEmpty(uid)) {
//            String refreshToken = tokenRepository.getRefreshTokenByUid(uid);
//            if (refreshToken != null) {
//                tokenRepository.deleteRefreshTokenByRefreshToken(refreshToken);
//                tokenRepository.deleteAccessTokenByRefreshToken(refreshToken);
//            }
//        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @GetMapping("/login-users")
    public ResponseEntity<LoginUserDTO> getLoginUserByEmail(@RequestParam String email) {
        log.info("REST request to get LoginUser by email : {}", email);
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }
        LoginUserDTO loginUserDTO = authorityServiceImpl.getLoginUserByEmail(email);
        return ResponseEntity.ok(loginUserDTO);
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }
}
