package com.altomni.apn.authority.oauth2.core;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.authority.service.jobdiva.JobdivaClient;
import com.altomni.apn.authority.service.management.ManagementClient;
import com.altomni.apn.authority.web.rest.vm.LoginVM;
import com.altomni.apn.common.domain.user.AdminManagementUser;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.domain.user.UserSecurityInterface;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.dto.user.RoleDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.UserNotActivatedException;
import com.altomni.apn.common.service.cache.CachedFeignSsoUserMapping;
import com.altomni.apn.common.vo.talent.TenantPublicVO;
import com.altomni.apn.user.domain.enumeration.AccountType;
import com.altomni.apn.user.domain.user.UserImpersonation;
import com.altomni.apn.user.repository.user.UserAdminRepository;
import com.altomni.apn.user.repository.user.UserImpersonationRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.ipg.resourceserver.user.SsoOidcUser;
import com.ipg.resourceserver.user.UserMapping;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Component("userDetailsService")
@Slf4j
public class DomainUserDetailsService implements UserDetailsService, UserMapping {

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserImpersonationRepository userImpersonationRepository;

    @Resource
    private UserAdminRepository userAdminRepository;

    @Resource
    private JobdivaClient jobdivaClient;

    @Resource
    private ManagementClient managementClient;

    @Override
    public UserDetails loadUserByUsername(final String username) throws UsernameNotFoundException {
        final String uid;
        LoginVM loginVM = JSON.parseObject(username, LoginVM.class);
        log.info("Authenticating {}", username);
        if (username.contains("uid")) {
            LoginUserDTO loginUserDTO = JSON.parseObject(username, LoginUserDTO.class);
            uid = loginUserDTO.getUid();
        }else{
            uid = loginVM.getUsername();
        }
        UserSecurityInterface user = null;
        //APN user
        boolean isNormalUser = false;
        // For impersonation purpose
        Long originalUserId = null;
        if (uid.indexOf(StrUtil.COMMA) > 0 && !uid.contains("ADMIN")) {
            String[] split = uid.split(StrUtil.COMMA);
            long userId = Long.parseLong(split[0]);
            long tenantId = Long.parseLong(split[1]);
            String accountType = split[1];
            if (accountType.equalsIgnoreCase(AccountType.ADMIN.toString())){
                user = userAdminRepository.findOneWithRolesById(userId).orElseThrow(() -> new UsernameNotFoundException("User " + uid + " was not found in the database"));
            }else{ // Regular User
                // For impersonation purpose
                String password = userRepository.findPasswordUserId(userId);
                if (Objects.nonNull(loginVM.getTargetUserId())){
                    Long targetUserId = loginVM.getTargetUserId();
                    final Optional<UserImpersonation> impersonationsOpt = userImpersonationRepository.findFirstByGrantFromUserIdAndGrantToUserIdAndTenantIdAndExpireAtAfter(targetUserId, userId, tenantId, Instant.now());
                    if (impersonationsOpt.isEmpty()) {
                        throw new CustomParameterizedException("Permission denied");
                    }
                    originalUserId = userId;
                    userId = targetUserId;
                }
                User u = userRepository.findOneWithRolesById(userId).orElseThrow(() -> new UsernameNotFoundException("User " + uid + " was not found in the database"));
                u.setTeamId(userRepository.findPrimaryTeamId(userId));
                u.setPassword(password);
                user = u;
                isNormalUser = true;
            }
//            }else{
//                User u = userRepository.findOneWithRolesById(userId).orElseThrow(() -> new UsernameNotFoundException("User " + uid + " was not found in the database"));
//                u.setTeamId(userRepository.findPrimaryTeamId(userId));
//                user = u;
//            }
        }
        //TimeSheetUser
        if (uid.indexOf(StrUtil.UNDERLINE) > 0) {
            user = Convert.convert(TimeSheetUser.class,jobdivaClient.findUserWithAuthorities(uid).getBody());
        }

        //ManagementUser
        if (uid.contains("ADMIN")) {
            user = Convert.convert(AdminManagementUser.class, managementClient.findUserWithAuthorities(uid).getBody());
        }
//        return createSpringSecurityUser(uid, user, loginVM);
        return createSpringSecurityUser(uid, user, loginVM, isNormalUser, originalUserId);
    }

    private UserDetails createSpringSecurityUser(String lowercaseLogin, UserSecurityInterface user, LoginVM loginVM, boolean isNormalUser, Long originalUserId) {
        if (!user.isActivated()) {
            throw new UserNotActivatedException("User " + lowercaseLogin + " was not activated");
        }
        List<GrantedAuthority> grantedAuthorities = user.getRoles()
                .stream()
                .map(role -> new SimpleGrantedAuthority(role.getName()))
                .collect(Collectors.toList());
        LoginUserDTO loginUserDTO = new LoginUserDTO();
        BeanUtils.copyProperties(user, loginUserDTO);
        if (isNormalUser){
            List<Long> companyIds = userRepository.findCompanyIdsByUserIdWithAm(user.getId());
            loginUserDTO.setCompanyIdsWithAm(companyIds);
        }
        loginUserDTO.setIp(loginVM.getIp());
        loginUserDTO.setTimezone(loginVM.getTimeZone());
        if (user.getTenantId() != null) {
            TenantPublicVO tenant = managementClient.queryTenant(user.getTenantId()).getBody();
            if(tenant != null && tenant.getUserType() != null){
                loginUserDTO.setUserType(tenant.getUserType());
            }
        }
        return new org.springframework.security.core.userdetails.User(JSON.toJSONString(loginUserDTO), user.getPassword(), grantedAuthorities);
        //return new CustomUserDetails(user.getUsername(), user.getPassword(), user.isActivated(), grantedAuthorities, user.getFirstName() + user.getLastName(), user.getId(), user.getTenantId(), user.getTenant().getName());
    }

    public Optional<LoginUserDTO> findLoginUser(String email) {
        return userRepository.findUserWithRoles(email)
                .map(user -> {
                    List<RoleDTO> roles = user.getRoles().stream().map(role -> Convert.convert(RoleDTO.class, role)).toList();
                    user.setTeamId(userRepository.findPrimaryTeamId(user.getId()));
                    LoginUserDTO loginUserDTO = new LoginUserDTO();
                    BeanUtils.copyProperties(user, loginUserDTO);
                    loginUserDTO.setTimezone(user.getCustomTimezone());
                    loginUserDTO.setRoles(roles);
                    List<Long> companyIds = userRepository.findCompanyIdsByUserIdWithAm(user.getId());
                    loginUserDTO.setCompanyIdsWithAm(companyIds);
                    if (user.getTenantId() != null) {
                        TenantPublicVO tenant = managementClient.queryTenant(user.getTenantId()).getBody();
                        if (tenant != null && tenant.getUserType() != null) {
                            loginUserDTO.setUserType(tenant.getUserType());
                        }
                    }
                    return loginUserDTO;
                });
    }

    @Cacheable(value = {"APN:SSO:loginUser"}, key = "#email", unless = "#result == null")
    public LoginUserDTO cachedFindLoginUser(String email) {
        return findLoginUser(email).orElse(null);
    }

    @Override
    public SsoOidcUser mapping(OidcUser originalUser) {
        log.info("Mapping OIDC user: {}", originalUser.getEmail());
        String email = originalUser.getEmail();
        return findLoginUser(email).map(loginUserDTO -> CachedFeignSsoUserMapping.getLoginUserDTO(loginUserDTO, originalUser)).orElse(null);
    }
}
