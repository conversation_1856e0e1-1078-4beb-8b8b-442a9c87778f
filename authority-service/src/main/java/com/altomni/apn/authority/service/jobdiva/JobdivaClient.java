package com.altomni.apn.authority.service.jobdiva;

import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(value = "jobdiva-service")
public interface JobdivaClient {


    @PostMapping("/jobdiva/api/v3/timesheet/user/save")
    ResponseEntity<TimeSheetUserDTO> saveTimeSheetUser(@RequestBody TimeSheetUserDTO timeSheetUserDTO);

    @GetMapping("/jobdiva/api/v3/timesheet/user/findOneWithRolesByUsername")
    ResponseEntity<TimeSheetUserDTO> findOneWithRolesByUsername(@RequestParam("username") String username);

    @GetMapping("/jobdiva/api/v3/timesheet/user/findUserWithAuthorities")
    ResponseEntity<TimeSheetUserDTO> findUserWithAuthorities(@RequestParam("login") String login);
}
