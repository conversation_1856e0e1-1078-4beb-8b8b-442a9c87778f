package com.altomni.apn.authority;

import com.altomni.apn.common.config.*;
import com.altomni.apn.common.config.env.CommonApplicationProperties;
import com.altomni.apn.common.config.loadbalancer.LoadBalanceConfiguration;
import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.errors.ExceptionTranslator;
import com.altomni.apn.common.interceptor.FeignClientInterceptor;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CommonRedisService;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

/**
 * <AUTHOR>
 */
@EnableJpaRepositories("com.altomni.apn.*.repository")
@EntityScan("com.altomni.apn.*.domain")
@SpringBootApplication
@EnableDiscoveryClient
@EnableFeignClients(basePackages = {"com.altomni.apn"})
@LoadBalancerClients(defaultConfiguration = LoadBalanceConfiguration.class)
@Import({ExceptionTranslator.class,
        PublicBeanInjection.class,
        CachePermission.class,
        CommonRedisService.class,
        CommonApplicationProperties.class,
        TeamDataPermissionRule.class,
        FeignClientInterceptor.class,
        AppInit.class,
        GlobalCacheConfig.class,
        JacksonConfiguration.class,
        CommonApiMultilingualConfig.class})
public class AuthorityApp {

    public static void main(String[] args) {
        SpringApplication.run(AuthorityApp.class, args);
    }
}
