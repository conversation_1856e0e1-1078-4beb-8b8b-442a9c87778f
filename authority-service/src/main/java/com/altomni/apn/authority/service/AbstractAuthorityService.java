package com.altomni.apn.authority.service;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.authority.config.env.ApplicationProperties;
import com.altomni.apn.authority.oauth2.core.DomainUserDetailsService;
import com.altomni.apn.authority.web.rest.vm.LoginVM;
import com.altomni.apn.authority.web.rest.vm.RefreshTokenVM;
import com.altomni.apn.common.domain.user.UserSecurityInterface;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractAuthorityService implements AuthorityService {

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    protected RestTemplate restTemplate;

    @Resource
    protected DomainUserDetailsService domainUserDetailsService;

    /**
     * User login. Call the oauth2 login api and return user profile
     * @param loginVM username and password
     * @return jsonObject access token & refresh token
     */
    @Override
    public CredentialDTO findCredential(LoginVM loginVM) {
        try {
            String tokenUrl = String.format("http://%s%s/oauth/token", applicationProperties.getService(), applicationProperties.getContextPath());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.add("Authorization", String.format("Basic %s", applicationProperties.getSecret()));

            MultiValueMap<String, String> map= new LinkedMultiValueMap<>();
            map.add("username", JSON.toJSONString(loginVM));
            map.add("password", loginVM.getPassword());
            map.add("grant_type", "password");

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);

            ResponseEntity<CredentialDTO> response = restTemplate.postForEntity(tokenUrl, request, CredentialDTO.class);

            CredentialDTO credentialDTO = response.getBody();

            return credentialDTO;
        } catch (Exception e) {
            log.error("[UserService.login] Login user {}, exception {}", loginVM, ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("The username and password do not match.");
        }
    }

    @Override
    public CredentialDTO refreshToken(RefreshTokenVM refreshTokenVM) {
        try {
            String tokenUrl = String.format("http://%s%s/oauth/token", applicationProperties.getService(), applicationProperties.getContextPath());
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.add("Authorization", String.format("Basic %s", applicationProperties.getSecret()));

            MultiValueMap<String, String> map= new LinkedMultiValueMap<>();
            map.add("refresh_token", refreshTokenVM.refresh_token);
            map.add("grant_type", "refresh_token");

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(map, headers);

            ResponseEntity<CredentialDTO> response = restTemplate.postForEntity(tokenUrl, request, CredentialDTO.class);

            CredentialDTO credentialDTO = response.getBody();

            return credentialDTO;
        } catch (Exception e) {
            log.error("[UserService.refreshToken] refresh token exception {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("Bad Credential");
        }
    }

    @Override
    public LoginUserDTO getLoginUserByEmail(String email) {
        return domainUserDetailsService.cachedFindLoginUser(email);
    }

    public abstract Optional<UserSecurityInterface> getUserWithAuthoritiesByLogin(String login);
}
