package com.altomni.apn.authority.service.impl;

import cn.hutool.core.convert.Convert;
import com.altomni.apn.authority.service.AbstractAuthorityService;
import com.altomni.apn.authority.service.jobdiva.JobdivaClient;
import com.altomni.apn.common.domain.user.TimeSheetUser;
import com.altomni.apn.common.dto.user.TimeSheetUserDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * Service class for managing users.
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TimeSheetAuthorityServiceImpl extends AbstractAuthorityService {

    @Resource
    private JobdivaClient jobdivaClient;

    @Override
    @Transactional(readOnly = true)
    public Optional getUserWithAuthoritiesByLogin(String login) {
        return Optional.of(Convert.convert(TimeSheetUser.class, jobdivaClient.findOneWithRolesByUsername(login).getBody()));
    }

}
