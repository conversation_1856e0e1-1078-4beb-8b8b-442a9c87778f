package com.altomni.apn.authority.web.rest.vm;

import org.hibernate.validator.constraints.Email;
import org.hibernate.validator.constraints.NotBlank;

public class ResetEmailVM {

    @NotBlank
    @Email
    private String email;

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public String toString() {
        return "ResetEmailVM{" +
            "email='" + email + '\'' +
            '}';
    }
}
