package com.altomni.apn.authority.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {
    @Value("${oauth2.secret}")
    private String secret;

    @Value("${spring.application.name}")
    private String service;

    @Value("${server.servlet.context-path}")
    private String contextPath;

    @Value("${server.port}")
    private String serverPort;

    @Value("${oauth2.secret}")
    private String Oauth2AuthHeader;
}
