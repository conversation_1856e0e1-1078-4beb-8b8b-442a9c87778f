package com.altomni.apn.authority.service.management;

import com.altomni.apn.common.dto.user.ManagementLoginUserDTO;
import com.altomni.apn.common.dto.user.ManagementUserDTO;
import com.altomni.apn.common.vo.talent.TenantPublicVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(value = "management-service")
public interface ManagementClient {

    @GetMapping("/management/api/v3/admin-management/user/findOneByUsername")
    ResponseEntity<ManagementLoginUserDTO> findOneByUsername(@RequestParam("username") String username);

    @GetMapping("/management/api/v3/admin-management/user/findUserWithAuthorities")
    ResponseEntity<ManagementUserDTO> findUserWithAuthorities(@RequestParam("login") String login);

    @GetMapping("/management/api/v3/public/tenants/{id}")
    ResponseEntity<TenantPublicVO> queryTenant(@PathVariable("id") Long id);
}
