package com.altomni.apn.authority.service;


import com.altomni.apn.authority.web.rest.vm.LoginVM;
import com.altomni.apn.authority.web.rest.vm.RefreshTokenVM;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.LoginUserDTO;

/**
 * <AUTHOR>
 */
public interface AuthorityService {

    CredentialDTO findCredential(LoginVM loginVM);

    CredentialDTO refreshToken(RefreshTokenVM refreshTokenVM);

    LoginUserDTO getLoginUserByEmail(String email);
}
