package com.altomni.apn.authority.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationEntryPoint;
import org.springframework.security.web.SecurityFilterChain;


/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class WebSecurityConfig {


    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http.oauth2ResourceServer().opaqueToken(Customizer.withDefaults());
        // 没有 token 或者 token 过期，返回 401
        http.exceptionHandling(exceptionHandling -> exceptionHandling.authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint()));

        // 无状态 session
        http.sessionManagement((session) -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        http.csrf().disable()
                .authorizeRequests()
                .requestMatchers("/api/v3/login-users").permitAll()
                .requestMatchers("/api/v3/credential").permitAll()
                .requestMatchers("/api/v3/credential/admin").permitAll()
                .requestMatchers("/api/v3/credential/timesheet").permitAll()
                .requestMatchers("/api/v3/credential/management").permitAll()
                .requestMatchers("/api/v3/social/credential").permitAll()
                .requestMatchers("/api/v3/refresh-token").permitAll()
                .requestMatchers("/api/v3/logoutByUid").permitAll()
                .requestMatchers("/api/v3/cache/**").permitAll()
                .requestMatchers("/actuator/**").permitAll()
                
                .requestMatchers("/api/v3/liveness").permitAll().anyRequest().authenticated();

        return http.build();
    }
}
