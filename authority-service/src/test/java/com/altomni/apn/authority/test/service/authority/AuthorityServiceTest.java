package com.altomni.apn.authority.test.service.authority;

import com.altomni.apn.authority.service.AuthorityService;
import com.altomni.apn.common.dto.CredentialDTO;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit4.SpringRunner;

import static com.altomni.apn.authority.test.common.authority.AuthorityCommon.genTestData;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class AuthorityServiceTest
{
    @Mock
    private AuthorityService service;
    
    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    public void testFindCredential() {
        CredentialDTO credentialDTO = genTestData();
        when(service.findCredential(null)).thenReturn(credentialDTO);
        CredentialDTO result = service.findCredential(null);
        Assertions.assertThat(result.getAccess_token()).isEqualTo(credentialDTO.getAccess_token());
    }
}
