package com.altomni.apn.authority.test.rest.authority;

import com.altomni.apn.authority.web.rest.AuthorityResource;
import com.altomni.apn.common.dto.CredentialDTO;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

import static com.altomni.apn.authority.test.common.authority.AuthorityCommon.genTestData;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class AuthorityResourceMockTest {
    @Mock
    private AuthorityResource authorityResource;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetCredential() throws Exception {
        CredentialDTO credentialDTO = genTestData();
        Mockito.when(authorityResource.getCredential(null)).thenReturn(new ResponseEntity<>(credentialDTO, HttpStatus.OK));
        ResponseEntity<CredentialDTO> response = authorityResource.getCredential(null);

        Assertions.assertThat(response.getBody().getAccess_token()).isEqualTo(credentialDTO.getAccess_token());
    }
}
