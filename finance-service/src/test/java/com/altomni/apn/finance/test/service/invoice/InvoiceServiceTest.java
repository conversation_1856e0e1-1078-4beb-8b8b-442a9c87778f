package com.altomni.apn.finance.test.service.invoice;

import com.altomni.apn.finance.service.dto.invoice.InvoiceDTO;
import com.altomni.apn.finance.service.invoice.InvoiceService;
import com.altomni.apn.finance.test.common.invoice.InvoiceCommon;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class InvoiceServiceTest
{
    @Mock
    private InvoiceService invoiceService;
    
    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    public void testCreateFteInvoice() {
        InvoiceDTO invoiceDTO = InvoiceCommon.genTestData();
        when(invoiceService.createFteInvoice(invoiceDTO)).thenReturn(List.of(invoiceDTO));
        List<InvoiceDTO> result = invoiceService.createFteInvoice(invoiceDTO);

        Assertions.assertThat(result).isNotEmpty();
        Assertions.assertThat(result.get(0).getInvoiceNo()).isEqualTo(invoiceDTO.getInvoiceNo());
    }
}
