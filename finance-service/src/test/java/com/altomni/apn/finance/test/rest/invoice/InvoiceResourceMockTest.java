package com.altomni.apn.finance.test.rest.invoice;

import com.altomni.apn.finance.service.dto.invoice.InvoiceDTO;
import com.altomni.apn.finance.test.common.invoice.InvoiceCommon;
import com.altomni.apn.finance.web.rest.invoice.InvoiceResource;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class InvoiceResourceMockTest {
    @Mock
    private InvoiceResource invoiceResource;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateFteInvoice() throws Exception {
        InvoiceDTO invoiceDTO = InvoiceCommon.genTestData();
        Mockito.when(invoiceResource.createFteInvoice(invoiceDTO)).thenReturn(new ResponseEntity<>(List.of(invoiceDTO), HttpStatus.OK));
        ResponseEntity<List<InvoiceDTO>> response = invoiceResource.createFteInvoice(invoiceDTO);

        Assertions.assertThat(response.getBody()).isNotEmpty();
        Assertions.assertThat(response.getBody().get(0).getInvoiceNo()).isEqualTo(invoiceDTO.getInvoiceNo());
    }
}
