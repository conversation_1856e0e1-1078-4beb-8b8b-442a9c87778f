package com.altomni.apn.finance.test.repository.invoice;

import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.repository.invoice.InvoiceRepository;
import com.altomni.apn.finance.service.dto.invoice.InvoiceDTO;
import com.altomni.apn.finance.test.common.invoice.InvoiceCommon;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit4.SpringRunner;

import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class InvoiceRepositoryTest {

  @Mock
  private InvoiceRepository invoiceRepository;

  @BeforeEach
  public void init() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testSave() {
    InvoiceDTO invoiceDTO = InvoiceCommon.genTestData();
    Invoice invoice = new Invoice();
    invoice.setInvoiceNo(invoiceDTO.getInvoiceNo());
    when(invoiceRepository.save(invoice)).thenReturn(invoice);
    Invoice result = invoiceRepository.save(invoice);

    Assertions.assertThat(result.getInvoiceNo()).isEqualTo(invoice.getInvoiceNo());
  }
}
