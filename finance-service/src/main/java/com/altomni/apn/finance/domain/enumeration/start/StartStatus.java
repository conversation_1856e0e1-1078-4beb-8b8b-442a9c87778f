package com.altomni.apn.finance.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum StartStatus implements ConvertedEnum<Integer> {

    /**
     * Eliminated
     */
    ELIMINATED(-1),

    /**
     * Active
     */
    ACTIVE(0),

    /**
     * For contract job: Terminated
     */
    CONTRACT_TERMINATED(5),

    /**
     * For contract job: The start will be updated to this status when a start extended.
     */
    CONTRACT_EXTENDED(10),

    /**
     * For FTE job: Fail warranty
     */
    FTE_FAIL_WARRANTY(15);

    private final int dbValue;
    StartStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<StartStatus, Integer> resolver =
        new ReverseEnumResolver<>(StartStatus.class, StartStatus::toDbValue);

    public static StartStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
