package com.altomni.apn.finance.domain.invoicing;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "enum_invoicing_service_tax")
public class EnumInvoicingServiceTax implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id ;

    /** 服务名称 */
    @Column(name = "service_name")
    private String serviceName ;

    /** 服务名称 */
    @Column(name = "service_name_eng")
    private String serviceNameEng ;

    /** 税率 */
    @Column(name = "tax")
    private Double tax ;
}
