package com.altomni.apn.finance.service.invoice.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.company.CompanyClientInfoVO;
import com.altomni.apn.common.vo.company.CompanyClientInvoicingInfoVO;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceInfoType;
import com.altomni.apn.finance.domain.invoice.InvoiceTypeConfig;
import com.altomni.apn.finance.repository.invoice.InvoiceTypeConfigRepository;
import com.altomni.apn.finance.service.company.CompanyService;
import com.altomni.apn.finance.service.invoice.InvoiceTypeConfigService;
import com.altomni.apn.finance.service.vo.invoice.InvoiceDetailInfoVO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceTypeConfigVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class InvoiceTypeConfigServiceImpl implements InvoiceTypeConfigService {

    private Logger log = LoggerFactory.getLogger(InvoiceTypeConfigServiceImpl.class);

    @Resource
    InvoiceTypeConfigRepository invoiceTypeConfigRepository;

    @Resource
    CompanyService companyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Override
    public List<InvoiceTypeConfigVO> findAllByTenantId() {
        List<InvoiceTypeConfig> configList = invoiceTypeConfigRepository.findAllByTenantIdAndStatus(SecurityUtils.getTenantId(), 1);
        if (configList.isEmpty()) {
            return new ArrayList<>();
        }
        List<InvoiceTypeConfigVO> result = new ArrayList<>();
        configList.forEach(v -> {
            InvoiceTypeConfigVO vo = new InvoiceTypeConfigVO();
            vo.setId(v.getId());
            vo.setName(v.getName());
            vo.setLabel(v.getLabel());
            vo.setInvoiceInfoType(v.getInvoiceInfoType());
            result.add(vo);
        });
        return result;
    }

    @Override
    public List<InvoiceDetailInfoVO> findInvoiceInfoList(Long companyId, Long typeConfigId) {
        InvoiceTypeConfig invoiceTypeConfig = invoiceTypeConfigRepository.findAllByTenantIdAndStatusAndId(SecurityUtils.getTenantId(), 1, typeConfigId);
        if (null == invoiceTypeConfig) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_TYPE_NOT_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        List<InvoiceDetailInfoVO> voList = new ArrayList<>();
        if (invoiceTypeConfig.getInvoiceInfoType().equals(InvoiceInfoType.OVERSEAS)) {
            List<CompanyClientInfoVO> companyClientInfoVOS = companyService.getCompanyClientInfoList(companyId);
            if (!companyClientInfoVOS.isEmpty()) {
                companyClientInfoVOS.forEach(v -> {
                    InvoiceDetailInfoVO vo = new InvoiceDetailInfoVO();
                    vo.setId(v.getId());
                    vo.setCompanyId(v.getCompanyId());
                    vo.setClientName(v.getClientName());
                    vo.setClientAddress(v.getClientAddress());
                    vo.setClientDivision(v.getClientDivision());
                    vo.setClientLocation(v.getClientLocation());
                    vo.setClientEmail(v.getClientEmail());
                    voList.add(vo);
                });
            }
        } if (invoiceTypeConfig.getInvoiceInfoType().equals(InvoiceInfoType.CHINA)) {
            List<CompanyClientInvoicingInfoVO> invoicingInfoVOList = companyService.getCompanyInvoicingClientInfoList(companyId);
            if (!invoicingInfoVOList.isEmpty()) {
                invoicingInfoVOList.forEach(v -> {
                    InvoiceDetailInfoVO vo = new InvoiceDetailInfoVO();
                    vo.setId(v.getId());
                    vo.setCompanyId(v.getCompanyId());
                    vo.setClientName(v.getClientName());
                    vo.setBankName(v.getBankName());
                    vo.setBankAccount(v.getBankAccount());
                    vo.setPhone(v.getPhone());
                    vo.setInvoicingAddress(v.getInvoicingAddress());
                    vo.setSocialCreditCode(v.getSocialCreditCode());
                    voList.add(vo);
                });
            }
        }
        return voList;
    }

    @Override
    public InvoiceDetailInfoVO findInvoiceInfoByTypeId(Long id, Long invoiceId) {
        Optional<InvoiceTypeConfig> invoiceTypeConfigOpt = invoiceTypeConfigRepository.findById(id);
        if(!invoiceTypeConfigOpt.isPresent()){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_TYPE_NOT_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        InvoiceTypeConfig invoiceTypeConfig = invoiceTypeConfigOpt.get();
        InvoiceDetailInfoVO infoVO = new InvoiceDetailInfoVO();
        if (invoiceTypeConfig.getInvoiceInfoType().equals(InvoiceInfoType.OVERSEAS)) {
            CompanyClientInfoVO companyClientInfoVO = companyService.getCompanyClientInfoById(invoiceId);
            if(null != companyClientInfoVO){
                infoVO.setId(companyClientInfoVO.getId());
                infoVO.setCompanyId(companyClientInfoVO.getCompanyId());
                infoVO.setClientName(companyClientInfoVO.getClientName());
                infoVO.setClientAddress(companyClientInfoVO.getClientAddress());
                infoVO.setClientDivision(companyClientInfoVO.getClientDivision());
                infoVO.setClientLocation(companyClientInfoVO.getClientLocation());
                infoVO.setClientEmail(companyClientInfoVO.getClientEmail());
            }

        } if (invoiceTypeConfig.getInvoiceInfoType().equals(InvoiceInfoType.CHINA)) {
            CompanyClientInvoicingInfoVO invoicingInfoVO = companyService.getInvoicingClientInfoById(invoiceId);
            if(null != invoicingInfoVO){
                infoVO.setId(invoicingInfoVO.getId());
                infoVO.setCompanyId(invoicingInfoVO.getCompanyId());
                infoVO.setClientName(invoicingInfoVO.getClientName());
                infoVO.setBankName(invoicingInfoVO.getBankName());
                infoVO.setBankAccount(invoicingInfoVO.getBankAccount());
                infoVO.setPhone(invoicingInfoVO.getPhone());
                infoVO.setInvoicingAddress(invoicingInfoVO.getInvoicingAddress());
                infoVO.setSocialCreditCode(invoicingInfoVO.getSocialCreditCode());
            }
        }

        return infoVO;
    }
}
