package com.altomni.apn.finance.domain.start;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.WorkingMode;
import com.altomni.apn.common.domain.enumeration.application.WorkingModeConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceTypeConverter;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.enumeration.start.StartStatusConverter;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.domain.enumeration.start.StartTypeConverter;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Entity
@Data
@Table(name = "start")
@JsonIdentityInfo(
        generator = ObjectIdGenerators.PropertyGenerator.class,
        property = "id")
public class Start extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -3954289099826246503L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to.")
    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Column(name = "warranty_end_date")
    private LocalDate warrantyEndDate;

    @Convert(converter = StartStatusConverter.class)
    @Column(name = "status")
    private StartStatus status;

    @Convert(converter = StartTypeConverter.class)
    @Column(name = "start_type")
    private StartType startType;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "talent_name")
    private String talentName;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "job_title")
    private String jobTitle;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "company")
    private String company;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "client_contact_id")
    private Long clientContactId;

    @Column(name = "currency")
    private Integer currency;

    @Convert(converter = JobTypeConverter.class)
    @Column(name = "position_type")
    private JobType positionType;

    @Column(name = "time_zone")
    private String timeZone;

    @Column(name = "note")
    private String note;

    @Column(name = "charge_number")
    private String chargeNumber;

    @Column(name = "tvc_number")
    private String tvcNumber;

    @Column(name = "corp_to_corp")
    private Boolean corpToCorp;

    @Column(name = "working_mode")
    @Convert(converter = WorkingModeConverter.class)
    private WorkingMode workingMode;

    @Column(name = "channel_platform")
    @Convert(converter = ResumeSourceTypeConverter.class)
    private ResumeSourceType channelPlatform;

    @Column(name = "profit_sharing_ratio")
    private BigDecimal profitSharingRatio = new BigDecimal(0);

    @Column(name = "is_substitute_talent")
    private Boolean isSubstituteTalent;

    @Column(name = "relation_process_id")
    private Long relationProcessId;

    @Column(name = "substitute_talent_id")
    private Long substituteTalentId;

    @Transient
    private StartAddress startAddress;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "tenantId", "talentId",
            "talentName", "jobId", "jobTitle", "company", "talentRecruitmentProcessId", "status"));

    public static Start fromStartDTO(StartDTO dto) {
        Start result = new Start();
        ServiceUtils.myCopyProperties(dto, result);
        return result;
    }

}
