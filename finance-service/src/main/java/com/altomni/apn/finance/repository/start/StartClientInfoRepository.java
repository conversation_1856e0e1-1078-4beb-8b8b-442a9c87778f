package com.altomni.apn.finance.repository.start;

import com.altomni.apn.finance.domain.start.StartClientInfo;
import com.altomni.apn.finance.service.dto.start.StartClientInfoCompanyDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data  repository for the StartClientInfo entity.
 */
@SuppressWarnings("unused")
@Repository
public interface StartClientInfoRepository extends JpaRepository<StartClientInfo, Long> {

    StartClientInfo findByStartId(Long startId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE start_client_info t SET t.client_name = ?2,t.client_address = ?3, t.client_division = ?4  WHERE t.start_id = ?1", nativeQuery = true)
    void updateClientInfoByStartId(Long startId, String clientName, String clientAddress, String clientDivision);



    @Modifying
    @Transactional
    @Query(value = "UPDATE start_client_info t SET t.client_info_id = ?2  WHERE t.id = ?1", nativeQuery = true)
    void updateClientInfoById(Long id, Long clientInfoId);


    List<StartClientInfo> findAllByStartIdIn(List<Long> startIds);
}
