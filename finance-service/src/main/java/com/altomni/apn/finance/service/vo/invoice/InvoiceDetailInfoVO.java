package com.altomni.apn.finance.service.vo.invoice;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InvoiceDetailInfoVO {

    private Long id ;

    private Long companyId ;

    private String clientName ;

    private String clientDivision ;

    private String clientAddress ;

    private String clientLocation ;

    private String clientEmail ;

    private String socialCreditCode ;

    private String bankName ;

    private String bankAccount ;

    private String invoicingAddress ;

    private String phone ;

    private Long invoiceTypeId;

}
