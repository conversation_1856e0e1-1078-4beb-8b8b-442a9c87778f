package com.altomni.apn.finance.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum StartTerminationConvertToFteFeeStatus implements ConvertedEnum<Integer> {

    HAS_CONVERSION_FEE(0),
    NO_CONVERSION_FEE(1);

    private final int dbValue;
    StartTerminationConvertToFteFeeStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<StartTerminationConvertToFteFeeStatus, Integer> resolver =
        new ReverseEnumResolver<>(StartTerminationConvertToFteFeeStatus.class, StartTerminationConvertToFteFeeStatus::toDbValue);

    public static StartTerminationConvertToFteFeeStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
