package com.altomni.apn.finance.service.start.impl;

import cn.hutool.core.bean.BeanUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.config.Constants;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.domain.start.*;
import com.altomni.apn.finance.repository.start.*;
import com.altomni.apn.finance.service.application.ApplicationService;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.job.JobService;
import com.altomni.apn.finance.service.start.StartCommissionService;
import com.altomni.apn.finance.service.start.StartContractRateService;
import com.altomni.apn.finance.service.start.StartExtensionService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Service Implementation for managing Start.
 * <AUTHOR>
 */
@Service
public class StartExtensionServiceImpl implements StartExtensionService {

    private final Logger log = LoggerFactory.getLogger(StartExtensionServiceImpl.class);

    @Resource
    private StartRepository startRepository;

    @Resource
    private JobService jobService;

    @Resource
    private StartCommissionService startCommissionService;

    @Resource
    private StartFailedWarrantyRepository startFailedWarrantyRepository;

    @Resource
    private StartTerminationRepository startTerminationRepository;

    @Resource
    private StartAddressRepository startAddressRepository;

    @Resource
    private StartClientInfoRepository startClientInfoRepository;

    @Resource
    private StartContractRateService startContractRateService;

    @Resource
    private StartFteRateRepository startFteRateRepository;

    @Resource
    private ApplicationService applicationService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StartDTO createStartExtension(Long startId, StartDTO extensionDto) throws IOException {
        Start start = validationStart(startId, extensionDto);
        //validateIsCreatedByAm(start);
        start.setStatus(StartStatus.CONTRACT_EXTENDED);
        startRepository.save(start);

        Start extension = new Start();
        ServiceUtils.myCopyProperties(extensionDto, extension);
        setDefaultValue(start, extension);
        extension.setChannelPlatform(extensionDto.getChannelPlatform());
        extension.setProfitSharingRatio(extensionDto.getProfitSharingRatio());
        Start savedExtension = startRepository.save(extension);
        if (CollectionUtils.isNotEmpty(extensionDto.getStartCommissions())) {
            startCommissionService.create(savedExtension, extensionDto.getStartCommissions());
        }
        if (CollectionUtils.isEmpty(extensionDto.getStartContractRates())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CREATESTARTEXTENSION_CONTRACTRATESISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        StartContractRate startContractRate = extensionDto.getStartContractRates().get(0);
        startContractRate.setStartId(savedExtension.getId());
        startContractRate.setTenantId(start.getTenantId());
        startContractRateService.create(startContractRate);
        saveStartAddress(savedExtension.getId(), extensionDto.getStartAddress());
        saveClientInfo(savedExtension.getId(), StartClientInfo.fromStartDTO(extensionDto));

        return toDto(savedExtension);
    }

    private void validateIsCreatedByAm(Start start) {
        List<StartCommission> startCommissions = startCommissionService.findByStartId(start.getId());
        List<Long> ams = new ArrayList<>();
        for (StartCommission commission : startCommissions) {
            if (Arrays.asList(UserRole.AM,UserRole.CO_AM).contains(commission.getUserRole())) {
                ams.add(commission.getUserId());
            }
        }
        if (!ams.contains(SecurityUtils.getUserId())) {
            throw new ForbiddenException("Only account managers in this application can create starts");
        }
    }

    private void setDefaultValue(Start exist, Start extension) {
        extension.setTenantId(SecurityUtils.getTenantId());
        extension.setStatus(StartStatus.ACTIVE);
        extension.setStartType(StartType.CONTRACT_EXTENSION);
        extension.setTalentRecruitmentProcessId(exist.getTalentRecruitmentProcessId());
        extension.setTalentId(exist.getTalentId());
        extension.setTalentName(exist.getTalentName());
        extension.setJobId(exist.getJobId());
        extension.setJobTitle(exist.getJobTitle());
        extension.setCompanyId(exist.getCompanyId());
        extension.setCompany(exist.getCompany());
        extension.setPositionType(exist.getPositionType());
        extension.setCurrency(exist.getCurrency());
        extension.setClientContactId(extension.getClientContactId() == null ? exist.getClientContactId() : extension.getClientContactId());
    }

    private StartClientInfo saveClientInfo(Long startId, StartClientInfo startClientInfo) {
        if (startClientInfo != null) {
            startClientInfo.setStartId(startId);
            return startClientInfoRepository.save(startClientInfo);
        }
        return null;
    }

    private StartAddress saveStartAddress(Long startId, StartAddress address) {
        if (address != null) {
            StartAddress extensionAddress = new StartAddress();
            BeanUtil.copyProperties(address, extensionAddress, "id", "startId");
            extensionAddress.setStartId(startId);
            if (address.getId() == null) {
                LocationDTO dto = new LocationDTO();
                BeanUtil.copyProperties(address, dto);
                extensionAddress.setOriginalLoc(JsonUtil.toJson(dto));
            }
            return startAddressRepository.save(extensionAddress);
        }
        return null;
    }

    private StartDTO toDto(Start start) {
        StartDTO result = new StartDTO();
        ServiceUtils.myCopyProperties(start, result);
        result.setStartFteRate(startFteRateRepository.findByStartId(start.getId()));
        result.setStartContractRates(startContractRateService.findAllByStartId(start.getId()));
        result.setStartClientInfo(startClientInfoRepository.findByStartId(start.getId()));
        result.setStartAddress(startAddressRepository.findByStartId(start.getId()).orElse(null));
        result.setStartCommissions(startCommissionService.findByStartId(start.getId()));
        result.setTermination(startTerminationRepository.findByStartId(start.getId()));
        result.setFailedWarranty(startFailedWarrantyRepository.findByStartId(start.getId()));
        return result;
    }

    private Start validationStart(Long startId, StartDTO extensionDto) {
        Optional<Start> startOptional = startRepository.findById(startId);
        if (startOptional.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATIONSTART_STARTOPTIONALISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(startId),financeApiPromptProperties.getFinanceService()));
        }
        Start start = startOptional.get();
        if (extensionDto.getStartDate().isBefore(start.getEndDate())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATIONSTART_STARTDATEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        if (!StartStatus.ACTIVE.equals(start.getStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATIONSTART_STARTSTATUSERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(start.getStatus()),financeApiPromptProperties.getFinanceService()));
        }
        JobDTOV3 job = jobService.getJob(start.getJobId());
        if (job == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATIONSTART_JOBNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(start.getJobId()),financeApiPromptProperties.getFinanceService()));
        }
        JobType jobType = applicationService.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcess().getId());
        if (!Constants.CONTRACT_AND_PAYROLL_JOB.contains(jobType)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATIONSTART_JOBTYPEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        return start;
    }

    @Override
    @Transactional
    public StartDTO updateStartExtension(StartDTO update) throws IOException {
        Optional<Start> currentStartExtensionOpt = startRepository.findById(update.getId());
        if (currentStartExtensionOpt.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_UPDATESTARTEXTENSION_STARTISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(update.getId()),financeApiPromptProperties.getFinanceService()));
        }

        Start currentStartExtension = currentStartExtensionOpt.get();
        // start date not change, update base info only and return directly
        if (update.getStartDate() == null || update.getStartDate().equals(currentStartExtension.getStartDate())) {
            ServiceUtils.myCopyProperties(update, currentStartExtension, Start.UpdateSkipProperties);
            return toDto(startRepository.save(currentStartExtension));
        }
        // start date changed, validate the new start date with the previous extended start's end date
        Start lastExtendedStart = startRepository.findLastByTalentIdAndJobId(currentStartExtension.getTalentId(), currentStartExtension.getJobId());
        if (!currentStartExtension.getId().equals(lastExtendedStart.getId()) && update.getStartDate().isBefore(lastExtendedStart.getEndDate())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_UPDATESTARTEXTENSION_STARTDATEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        ServiceUtils.myCopyProperties(update, currentStartExtension, Start.UpdateSkipProperties);
        currentStartExtension.setChannelPlatform(update.getChannelPlatform());
        currentStartExtension.setProfitSharingRatio(update.getProfitSharingRatio());
        Start savedStart = startRepository.save(currentStartExtension);
        if (CollectionUtils.isNotEmpty(update.getStartCommissions())) {
            startCommissionService.replace(savedStart, update.getStartCommissions());
        }
        return toDto(savedStart);
    }

}
