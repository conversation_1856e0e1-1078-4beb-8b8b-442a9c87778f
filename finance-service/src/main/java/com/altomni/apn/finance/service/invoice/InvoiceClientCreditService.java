package com.altomni.apn.finance.service.invoice;

import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityType;
import com.altomni.apn.finance.domain.invoice.InvoiceClientCredit;
import com.altomni.apn.finance.service.dto.invoice.InvoiceActivityDTO;
import com.altomni.apn.finance.service.dto.invoice.InvoiceClientCreditDTO;

import java.util.List;

public interface InvoiceClientCreditService {

    InvoiceActivityDTO apply(InvoiceClientCreditDTO clientCreditDTO);

    InvoiceActivityDTO deduct(Integer currency, InvoiceClientCreditDTO clientCreditDTO);

    List<InvoiceClientCredit> getAllInvoiceClientCredit(Long companyId);

    InvoiceActivityDTO saveInvoiceActivity(InvoiceClientCreditDTO clientCreditDTO, InvoiceActivityType activityType, String note);
}
