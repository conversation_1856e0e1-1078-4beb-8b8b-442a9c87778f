package com.altomni.apn.finance.service.dto.start;


import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.domain.start.Start;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class StartSearchSimpleDTO implements Serializable {

    private static final long serialVersionUID = 4407706925970162814L;

    private Long id;

    private String talentName;

    private Long jobId;

    private String jobTitle;

    public static StartSearchSimpleDTO fromStart(Start start) {
        StartSearchSimpleDTO dto = new StartSearchSimpleDTO();
        ServiceUtils.myCopyProperties(start, dto);
        return dto;
    }
}
