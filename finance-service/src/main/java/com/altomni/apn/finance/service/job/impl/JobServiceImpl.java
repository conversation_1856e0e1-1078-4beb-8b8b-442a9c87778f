package com.altomni.apn.finance.service.job.impl;

import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.job.JobPermissionDTO;
import com.altomni.apn.finance.service.job.JobClient;
import com.altomni.apn.finance.service.job.JobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
public class JobServiceImpl implements JobService {
    @Resource
    private JobClient jobClient;

    @Override
    public JobDTOV3 getJob(Long jobId) {
        ResponseEntity<JobDTOV3> response = jobClient.getJob(jobId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public JobDTOV3 getJobByIdAndTenantId(Long jobId, Long tenantId) {
        ResponseEntity<JobDTOV3> response = jobClient.getJobByIdAndTenantId(jobId,tenantId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public String getJobLocations(Long jobId) {
        ResponseEntity<String> response = jobClient.getJobLocations(jobId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<JobPermissionDTO> checkJobPermissionById(List<Long> jobids) {
        ResponseEntity<List<JobPermissionDTO>> response = jobClient.checkJobPermissionById(jobids);
        return response != null ? response.getBody() : null;
    }

    @Override
    public JobPermissionDTO checkJobPermissionByChinaInvoicing(Long jobId) {
        ResponseEntity<JobPermissionDTO> response = jobClient.checkJobPermissionByChinaInvoicing(jobId);
        return response != null ? response.getBody() : null;
    }
}
