package com.altomni.apn.finance.service.talent;

import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(value = "talent-service")
public interface TalentClient {
    @GetMapping("/talent/api/v3/talents/{id}")
    ResponseEntity<TalentV3> getTalent(@PathVariable("id") Long talentId);

    @GetMapping("/talent/api/v3/talents/without-entity/{id}")
    ResponseEntity<TalentDTOV3> getTalentWithoutEntity(@PathVariable("id") Long talentId);

    @GetMapping("/talent/api/v3/talents/sync-talent-to-es/{talentId}")
    ResponseEntity<HttpResponse> syncTalentToES(@PathVariable("talentId") Long talentId);

    @PostMapping("/talent/api/v3/talents/{id}")
    ResponseEntity<HttpResponse> updateTalent(@PathVariable("id") Long id, TalentDTOV3 talent);

    @PutMapping("/talent/api/v3/talents/{talentId}/experience")
    ResponseEntity<Void> updateTalentExperience(@PathVariable("talentId") Long talentId, @RequestBody TalentExperienceDTO talentExperienceDTO);

}
