package com.altomni.apn.finance.service.vo.invoicing;

import com.altomni.apn.common.domain.enumeration.company.*;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.List;


public class InvoicingApplicationInfoVO {

    private BigInteger id;

    /**
     * 1、全职招聘开票申请  2、预付金发票 3、废票
     */
    private String invoicingApplicationType;

    /**
     * 原id
     */
    private BigInteger invalidInvoicingId;

    /**
     * 废票开票原因
     */
    private String invoicingReason;

    /**
     * 编码
     */
    private String codeNumber;

    /**
     *
     */
    private BigInteger companyId;

    /**
     *
     */
    private BigInteger tenantId;

    /**
     * 客户开票信息
     */
    private BigInteger clientInvoicingId;

    /**
     * 1上海, 2深圳, 3尹泰
     */
    private String invoicingBody;

    /**
     * 1增值税电子专业发票, 2增值税电子普通发票, 3抵扣发票
     */
    private String invoicingType;

    /**
     * 1FTE, 2RPO, 3STF-外包, 4STF-人事代理
     */
    private String invoicingBusinessType;

    /**
     * 开票内容名称
     */
    private String invoicingTaxName;

    /**
     * 开票税率
     */
    private Double invoicingTax;

    /**
     * 发票格式 1pdf, 2ofd, 3xml
     */
    private String invoiceFormat;

    /**
     * 确认函
     */
    private String confirmationLetterUrl;

    private BigDecimal invoicingAmount;

    private BigDecimal prepaymentAmount;

    private BigDecimal paymentAmount;

    /**
     * 待回款金额
     */
    private String amountDue;

    /**
     * 发票税额
     */
    private BigDecimal invoiceTax;

    /**
     * 不包含税额
     */
    private BigDecimal taxesNotIncluded;

    /**
     * 应开票总金额
     */
    private BigDecimal gpAmount;

    /**
     * 未开票金额
     */
    private BigDecimal uninvoicedAmount;

    /**
     * 待审批、审批驳回、待开票、已开票、未回款、部分回款、逾期、全部回款、已作废
     */
    private String invoicingStatus;

    /**
     * 数电票号码
     */
    private String elecInvoiceNumber;

    /**
     * 开票日期
     */
    private Timestamp invoicingDate;

    /**
     * 账单天数
     */
    private Integer dueWithinDays;

    /**
     * 到期日期
     */
    private Timestamp paymentDueDate;

    private Timestamp createdDate;

    private String applicationUser;

    private String talentName;

    private String jobName;

    private String companyName;

    private String clientName;

    private String socialCreditCode;

    private String bankName;

    private String bankAccount;

    private String invoicingAddress;

    private String phone;

    private String invoiceArea;

    private String accountManager;

    private String recruiter;

    private String deliveryManager;

    private String sourcer;

    private String owner;

    private String bdManager;

    private String salesLeadManager;

    private String accountManagerPercentage;

    private String recruiterPercentage;

    private String deliveryManagerPercentage;

    private String sourcerPercentage;

    private String coAm;

    private List<UserCountryVO> coAmList;

    private String ownerPercentage;

    private String bdManagerPercentage;

    private String salesLeadManagerPercentage;

    private String paymentDate;

    private String paymentMethod;

    private String note;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getInvoicingApplicationType() {
        return invoicingApplicationType;
    }

    public void setInvoicingApplicationType(String invoicingApplicationType) {
        this.invoicingApplicationType = StringUtils.isNotBlank(invoicingApplicationType)?InvoicingApplicationType.getNameFromDbValue(Integer.valueOf(invoicingApplicationType)):"";
    }

    public BigInteger getInvalidInvoicingId() {
        return invalidInvoicingId;
    }

    public void setInvalidInvoicingId(BigInteger invalidInvoicingId) {
        this.invalidInvoicingId = invalidInvoicingId;
    }

    public String getInvoicingReason() {
        return invoicingReason;
    }

    public void setInvoicingReason(String invoicingReason) {
        this.invoicingReason = invoicingReason;
    }

    public String getCodeNumber() {
        return codeNumber;
    }

    public void setCodeNumber(String codeNumber) {
        this.codeNumber = codeNumber;
    }

    public BigInteger getCompanyId() {
        return companyId;
    }

    public void setCompanyId(BigInteger companyId) {
        this.companyId = companyId;
    }

    public BigInteger getTenantId() {
        return tenantId;
    }

    public void setTenantId(BigInteger tenantId) {
        this.tenantId = tenantId;
    }

    public BigInteger getClientInvoicingId() {
        return clientInvoicingId;
    }

    public void setClientInvoicingId(BigInteger clientInvoicingId) {
        this.clientInvoicingId = clientInvoicingId;
    }

    public String getInvoicingBody() {
        return invoicingBody;
    }

    public void setInvoicingBody(String invoicingBody) {
        this.invoicingBody = StringUtils.isNotBlank(invoicingBody) ? InvoicingBody.getNameFromDbValue(Integer.valueOf(invoicingBody)) : "";
    }

    public String getInvoicingType() {
        return invoicingType;
    }

    public void setInvoicingType(String invoicingType) {
        this.invoicingType = StringUtils.isNotBlank(invoicingType) ? InvoicingType.getNameFromDbValue(Integer.valueOf(invoicingType)) : "";
    }

    public String getInvoicingBusinessType() {
        return invoicingBusinessType;
    }

    public void setInvoicingBusinessType(String invoicingBusinessType) {
        this.invoicingBusinessType = StringUtils.isNotBlank(invoicingBusinessType) ? InvoicingBusinessType.getNameFromDbValue(Integer.valueOf(invoicingBusinessType)) : "";
    }

    public String getInvoicingTaxName() {
        return invoicingTaxName;
    }

    public void setInvoicingTaxName(String invoicingTaxName) {
        this.invoicingTaxName = invoicingTaxName;
    }

    public Double getInvoicingTax() {
        return invoicingTax;
    }

    public void setInvoicingTax(Double invoicingTax) {
        this.invoicingTax = invoicingTax;
    }

    public String getInvoiceFormat() {
        return invoiceFormat;
    }

    public void setInvoiceFormat(String invoiceFormat) {
        this.invoiceFormat = invoiceFormat;
    }

    public String getConfirmationLetterUrl() {
        return confirmationLetterUrl;
    }

    public void setConfirmationLetterUrl(String confirmationLetterUrl) {
        this.confirmationLetterUrl = confirmationLetterUrl;
    }

    public BigDecimal getInvoicingAmount() {
        return invoicingAmount;
    }

    public void setInvoicingAmount(BigDecimal invoicingAmount) {
        this.invoicingAmount = invoicingAmount;
    }

    public BigDecimal getPrepaymentAmount() {
        return prepaymentAmount;
    }

    public void setPrepaymentAmount(BigDecimal prepaymentAmount) {
        this.prepaymentAmount = prepaymentAmount;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public String getAmountDue() {
        return amountDue;
    }

    public void setAmountDue(String amountDue) {
        this.amountDue = amountDue;
    }

    public BigDecimal getInvoiceTax() {
        return invoiceTax;
    }

    public void setInvoiceTax(BigDecimal invoiceTax) {
        this.invoiceTax = invoiceTax;
    }

    public BigDecimal getTaxesNotIncluded() {
        return taxesNotIncluded;
    }

    public void setTaxesNotIncluded(BigDecimal taxesNotIncluded) {
        this.taxesNotIncluded = taxesNotIncluded;
    }

    public BigDecimal getGpAmount() {
        return gpAmount;
    }

    public void setGpAmount(BigDecimal gpAmount) {
        this.gpAmount = gpAmount;
    }

    public BigDecimal getUninvoicedAmount() {
        return uninvoicedAmount;
    }

    public void setUninvoicedAmount(BigDecimal uninvoicedAmount) {
        this.uninvoicedAmount = uninvoicedAmount;
    }

    public String getInvoicingStatus() {
        return invoicingStatus;
    }

    public void setInvoicingStatus(String invoicingStatus) {
        this.invoicingStatus = StringUtils.isNotBlank(invoicingStatus) ? InvoicingStatus.getNameFromDbValue(Integer.valueOf(invoicingStatus)) : "";
    }

    public String getElecInvoiceNumber() {
        return elecInvoiceNumber;
    }

    public void setElecInvoiceNumber(String elecInvoiceNumber) {
        this.elecInvoiceNumber = elecInvoiceNumber;
    }

    public Timestamp getInvoicingDate() {
        return invoicingDate;
    }

    public void setInvoicingDate(Timestamp invoicingDate) {
        this.invoicingDate = invoicingDate;
    }

    public Integer getDueWithinDays() {
        return dueWithinDays;
    }

    public void setDueWithinDays(Integer dueWithinDays) {
        this.dueWithinDays = dueWithinDays;
    }

    public Timestamp getPaymentDueDate() {
        return paymentDueDate;
    }

    public void setPaymentDueDate(Timestamp paymentDueDate) {
        this.paymentDueDate = paymentDueDate;
    }

    public Timestamp getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Timestamp createdDate) {
        this.createdDate = createdDate;
    }

    public String getApplicationUser() {
        return applicationUser;
    }

    public void setApplicationUser(String applicationUser) {
        this.applicationUser = applicationUser;
    }

    public String getTalentName() {
        return talentName;
    }

    public void setTalentName(String talentName) {
        this.talentName = talentName;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getSocialCreditCode() {
        return socialCreditCode;
    }

    public void setSocialCreditCode(String socialCreditCode) {
        this.socialCreditCode = socialCreditCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getInvoicingAddress() {
        return invoicingAddress;
    }

    public void setInvoicingAddress(String invoicingAddress) {
        this.invoicingAddress = invoicingAddress;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getInvoiceArea() {
        return invoiceArea;
    }

    public void setInvoiceArea(String invoiceArea) {
        this.invoiceArea = invoiceArea;
    }

    public String getAccountManager() {
        return accountManager;
    }

    public void setAccountManager(String accountManager) {
        this.accountManager = accountManager;
    }

    public String getRecruiter() {
        return recruiter;
    }

    public void setRecruiter(String recruiter) {
        this.recruiter = recruiter;
    }

    public String getDeliveryManager() {
        return deliveryManager;
    }

    public void setDeliveryManager(String deliveryManager) {
        this.deliveryManager = deliveryManager;
    }

    public String getSourcer() {
        return sourcer;
    }

    public void setSourcer(String sourcer) {
        this.sourcer = sourcer;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public String getBdManager() {
        return bdManager;
    }

    public void setBdManager(String bdManager) {
        this.bdManager = bdManager;
    }

    public String getSalesLeadManager() {
        return salesLeadManager;
    }

    public void setSalesLeadManager(String salesLeadManager) {
        this.salesLeadManager = salesLeadManager;
    }

    public String getAccountManagerPercentage() {
        return accountManagerPercentage;
    }

    public void setAccountManagerPercentage(String accountManagerPercentage) {
        this.accountManagerPercentage = accountManagerPercentage;
    }

    public String getRecruiterPercentage() {
        return recruiterPercentage;
    }

    public void setRecruiterPercentage(String recruiterPercentage) {
        this.recruiterPercentage = recruiterPercentage;
    }

    public String getDeliveryManagerPercentage() {
        return deliveryManagerPercentage;
    }

    public void setDeliveryManagerPercentage(String deliveryManagerPercentage) {
        this.deliveryManagerPercentage = deliveryManagerPercentage;
    }

    public String getSourcerPercentage() {
        return sourcerPercentage;
    }

    public void setSourcerPercentage(String sourcerPercentage) {
        this.sourcerPercentage = sourcerPercentage;
    }

    public String getOwnerPercentage() {
        return ownerPercentage;
    }

    public void setOwnerPercentage(String ownerPercentage) {
        this.ownerPercentage = ownerPercentage;
    }

    public String getBdManagerPercentage() {
        return bdManagerPercentage;
    }

    public void setBdManagerPercentage(String bdManagerPercentage) {
        this.bdManagerPercentage = bdManagerPercentage;
    }

    public String getSalesLeadManagerPercentage() {
        return salesLeadManagerPercentage;
    }

    public void setSalesLeadManagerPercentage(String salesLeadManagerPercentage) {
        this.salesLeadManagerPercentage = salesLeadManagerPercentage;
    }

    public String getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(String paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getCoAm() {
        return coAm;
    }

    public void setCoAm(String coAm) {
        this.coAm = coAm;
    }

    public List<UserCountryVO> getCoAmList() {
        return coAmList;
    }

    public void setCoAmList(List<UserCountryVO> coAmList) {
        this.coAmList = coAmList;
    }
}
