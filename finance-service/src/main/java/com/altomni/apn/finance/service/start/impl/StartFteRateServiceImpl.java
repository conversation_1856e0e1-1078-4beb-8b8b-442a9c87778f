package com.altomni.apn.finance.service.start.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.start.StartFteRate;
import com.altomni.apn.finance.domain.start.StartFteSalaryPackage;
import com.altomni.apn.finance.repository.start.StartFteRateRepository;
import com.altomni.apn.finance.repository.start.StartFteSalaryPackageRepository;
import com.altomni.apn.finance.service.start.StartFteRateService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class StartFteRateServiceImpl implements StartFteRateService {

    @Resource
    private StartFteRateRepository startFteRateRepository;
    @Resource
    private StartFteSalaryPackageRepository startFteSalaryPackageRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Override
    public StartFteRate update(StartFteRate startFteRate) {
        StartFteRate exists = findByStartId(startFteRate.getStartId());
        ServiceUtils.myCopyProperties(startFteRate, exists);
        if (CollectionUtils.isNotEmpty(startFteRate.getSalaryPackages())) {
            List<StartFteSalaryPackage> exist = startFteSalaryPackageRepository.findAllByStartId(startFteRate.getStartId());
            if (CollectionUtils.isNotEmpty(exist)) {
                startFteSalaryPackageRepository.deleteAll(exist);
            }
            List<StartFteSalaryPackage> salaryPackages = startFteRate.getSalaryPackages()
                    .stream().peek(startFteSalaryPackage -> startFteSalaryPackage.setStartId(startFteRate.getStartId()))
                    .collect(Collectors.toList());
            startFteSalaryPackageRepository.saveAll(salaryPackages);
        }
        return startFteRateRepository.save(exists);
    }

    @Override
    public StartFteRate findByStartId(Long startId) {
        StartFteRate startFteRate = startFteRateRepository.findByStartId(startId);
        if (startFteRate == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_FTEFINDBYSTARTID_STARTFTERATENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(startId),financeApiPromptProperties.getFinanceService()));
        }
        List<StartFteSalaryPackage>  salaryPackages = startFteSalaryPackageRepository.findAllByStartId(startId);
        if (CollectionUtils.isNotEmpty(salaryPackages)) {
            startFteRate.setSalaryPackages(salaryPackages);
        }
        return startFteRate;
    }
}
