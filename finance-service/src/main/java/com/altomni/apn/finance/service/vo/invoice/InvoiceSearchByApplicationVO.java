package com.altomni.apn.finance.service.vo.invoice;

import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatusConverter;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceTypeConverter;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;

@Data
@Entity
public class InvoiceSearchByApplicationVO {

    @Id
    private Long invoiceId;

    private String invoiceNo;

    private String subInvoiceNo;

    private LocalDate invoiceDate;

    private LocalDate dueDate;

    private Instant createdDate;

    @Convert(converter = InvoiceTypeConverter.class)
    private InvoiceType invoiceType;

    private String fullName;

    private Long talentId;

    @Convert(converter = InvoiceStatusConverter.class)
    private InvoiceStatus invoiceStatus;

    private BigDecimal dueAmount;

    private BigDecimal receivedAmount;

    private BigDecimal balanceAmount;

    private Long companyId;

    private String companyName;

    private Long jobId;

    private String jobTitle;

    private Long teamId;

    private String teamName;

    private Integer currency;

}
