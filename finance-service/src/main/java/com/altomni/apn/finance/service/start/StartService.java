package com.altomni.apn.finance.service.start;

import cn.hutool.json.JSONArray;
import com.altomni.apn.common.dto.application.dashboard.MyCandidate;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.start.Start;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.dto.start.StartSearchInvoicingDTO;
import com.altomni.apn.finance.service.dto.start.StartSearchSimpleDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;
import java.util.Set;

/**
 * Service Interface for managing Start.
 */
public interface StartService {

    /**
     * Save a start.
     *
     * @param start the entity to save
     * @return the persisted entity
     */
    StartDTO save(StartDTO start) throws IOException;

    Start update(Start start);

    /**
     *  Get the "id" start.
     *
     *  @param id the id of the entity
     *  @return the entity
     */
    StartDTO findOne(Long id);

    Start findById(Long id);

    List<StartDTO> findByTalent(Long talentId);

    StartDTO findByTalentIdAndTalentRecruitmentProcessId(Long talentId,Long talentRecruitmentProcessId,StartStatus status);

    StartDTO toDto(Long startId);

    StartDTO toDto(Start start);

    StartDTO fillDto(Start start, StartDTO startDTO);

    Start saveOnly(Start start);

    StartDTO findStartByTalentIdAndStatus(Long talentId, StartStatus status);

    StartDTO findByTalentIdAndStatusOrderByEndDate(Long talentId, StartStatus status);

    List<MyCandidate> getTotalBillAmountByTalentRecruitmentProcessId(List<Long> talentRecruitmentProcessIds);

    List<StartSearchSimpleDTO> searchStarts(String talentName);

    List<StartSearchInvoicingDTO> searchInvoicingStarts(String talentName);

    StartSearchInvoicingDTO getStartId(Long startId);

    void updateStartStatusWithEliminatedCandidate(Long talentRecruitmentProcessId);

    void updateStartStatusWithCancelEliminatedCandidate(Long talentRecruitmentProcessId);

    void updateTalentExperience(Start start);

    void checkPermissionByStartId(Long startId);

    void checkPermissionByCompanyId(Long companyId);

    Set<Long> filterTalentsWithActiveFteStarts(Set<Long> talentIds);

    StartDTO findByTalentRecruitmentProcessIdAndStatus(Long talentRecruitmentProcessId, StartStatus status);

    JSONArray deleteStartByJobId(Long jobId);
}
