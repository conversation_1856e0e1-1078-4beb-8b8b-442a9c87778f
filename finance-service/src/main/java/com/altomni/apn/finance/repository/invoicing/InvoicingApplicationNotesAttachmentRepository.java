package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.finance.domain.invoicing.InvoicingApplicationNotesAttachment;
import com.altomni.apn.finance.domain.invoicing.InvoicingCandidateInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface InvoicingApplicationNotesAttachmentRepository extends JpaRepository<InvoicingApplicationNotesAttachment, Long> {

    @Modifying
    @Transactional
    @Query(value = "update invoicing_application_attachment set status=0 where invoice_application_id = ?1", nativeQuery = true)
    void updateStatusByInvoicingId(Long invoicingId);

    List<InvoicingApplicationNotesAttachment> findByInvoiceApplicationIdAndStatus(Long invoiceApplicationId,Integer status);

    List<InvoicingApplicationNotesAttachment> findByInvoiceApplicationIdInAndStatus(List<Long> invoiceApplicationId,Integer status);

}
