package com.altomni.apn.finance.domain.invoicing;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "invoicing_outstanding_close")
public class InvoicingOutstandingClose extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**  */
    @Column(name = "invoicing_id")
    private Long invoicingId ;
    /**  */
    @Column(name = "candidate_id")
    private Long candidateId ;
    /**  */
    @Column(name = "talent_id")
    private Long talentId ;

    /** 未结清关闭金额 */
    @Column(name = "close_amount")
    private BigDecimal closeAmount ;
}
