package com.altomni.apn.finance.domain.start;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.SalaryType;
import com.altomni.apn.common.domain.enumeration.application.SalaryTypeConverter;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A TalentRecruitmentProcessOfferSalaryPackage.
 */
@Entity
@Table(name = "start_fte_salary_package")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class StartFteSalaryPackage extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7366221409334341225L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @NotNull
    @Column(name = "start_id")
    private Long startId;

    @Convert(converter = SalaryTypeConverter.class)
    @Column(name = "salary_type")
    private SalaryType salaryType;

    @Column(name = "amount", precision=20, scale=2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal amount;

    @Column(name = "need_charge")
    private Boolean needCharge;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStartId() {
        return startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public SalaryType getSalaryType() {
        return salaryType;
    }

    public void setSalaryType(SalaryType salaryType) {
        this.salaryType = salaryType;
    }

    public BigDecimal getAmount() {
        return amount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(amount));
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Boolean getNeedCharge() {
        return needCharge;
    }

    public void setNeedCharge(Boolean needCharge) {
        this.needCharge = needCharge;
    }

    @Override
    public String toString() {
        return "StartFteSalaryPackage{" +
                "id=" + id +
                ", startId=" + startId +
                ", salaryType=" + salaryType +
                ", amount=" + amount +
                ", needCharge=" + needCharge +
                '}';
    }
}
