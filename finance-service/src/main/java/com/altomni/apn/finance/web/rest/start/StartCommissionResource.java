package com.altomni.apn.finance.web.rest.start;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.finance.domain.start.StartCommission;
import com.altomni.apn.finance.service.start.StartCommissionService;
import com.altomni.apn.finance.service.start.StartService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing StartCommission.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StartCommissionResource {

    private static final String ENTITY_NAME = "commission";

    @Resource
    private StartService startService;

    @Resource
    private StartCommissionService startCommissionService;


    /**
     * POST  /start-commissions/{startId} : Create a new startCommission.
     *
     * @param startCommissions the startCommissions to create
     * @return the ResponseEntity with status 201 (Created) and with body the new startCommission, or with status 400 (Bad Request) if the startCommission has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/start-commissions/startId/{startId}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<StartCommission>> replace(@PathVariable Long startId, @RequestBody List<StartCommission> startCommissions) throws URISyntaxException {
        log.info("[APN: StartCommission @{}] REST request to save StartCommission : {}", SecurityUtils.getUserId(), startCommissions);
        startService.checkPermissionByStartId(startId);
        List<StartCommission> result = startCommissionService.replace(startService.findById(startId), startCommissions);
        return ResponseEntity.created(new URI("/api/v3/start-commissions/" + startId))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, startId.toString()))
            .body(result);
    }

    /**
     * GET  /start-commissions/{startId} : get all the commissions by start id.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of commissions in body
     */
    @GetMapping("/start-commissions/startId/{startId}")
    @Timed
    public List<StartCommission> getAllCommissions(@PathVariable Long startId) {
        log.info("[APN: StartCommission @{}] REST request to get all Commissions by start id: {}", SecurityUtils.getUserId(), startId);
        return startCommissionService.findByStartId(startId);
    }
}
