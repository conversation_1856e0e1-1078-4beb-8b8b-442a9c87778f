package com.altomni.apn.finance.config.env;

import com.altomni.apn.common.dto.CanadaProvinceTaxDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 * 利润中心负责人
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "profit-owner")
public class ProfitOwnerProperties {

    Map<String, String> profitOwnerMap;
}
