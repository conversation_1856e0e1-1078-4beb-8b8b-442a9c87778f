package com.altomni.apn.finance.service.dto.invoicing;

import com.altomni.apn.common.domain.enumeration.company.InvoicingPaymentMethodType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class InvoicingFinancialRecordPaymentDTO {

    private Long id;

    private Long invoicingId;

    private List<Long> invoicingIdList;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime paymentDate;

    private BigDecimal paymentAmount ;

    private InvoicingPaymentMethodType paymentMethod ;

    private String note;

    List<InvoicingCandidateInfoDTO> candidateJson;

}
