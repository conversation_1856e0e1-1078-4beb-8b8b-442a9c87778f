package com.altomni.apn.finance.web.rest.start;

import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.finance.domain.start.StartContractRate;
import com.altomni.apn.finance.domain.start.StartFteRate;
import com.altomni.apn.finance.service.start.StartFteRateService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.start.StartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Optional;

/**
 * REST controller for managing StartFteRate.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StartFteRateResource {

    private static final String ENTITY_NAME = "StartFteRate";

    @Resource
    private StartFteRateService startFteRateService;

    @Resource
    private StartService startService;


    @PutMapping("/start-fte-rates/startId/{startId}")
    public ResponseEntity<StartFteRate> update(@PathVariable Long startId, @RequestBody StartFteRate startFteRate) {
        log.info("[APN: StartFteRate @{}] REST request to update StartFteRate : {}, {}", SecurityUtils.getUserId(), startId, startFteRate);
        checkRate(startFteRate);
        startService.checkPermissionByStartId(startId);
        startFteRate.setStartId(startId);
        StartFteRate result = startFteRateService.update(startFteRate);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    private void checkRate(StartFteRate startFteRate) {
        if (null != startFteRate) {
            if (null != startFteRate.getSalaryPackages() && !startFteRate.getSalaryPackages().isEmpty()) {
                startFteRate.getSalaryPackages().forEach(v -> {
                    if (v.getAmount().compareTo(new BigDecimal("99999999")) > 0) {
                        throw new CustomParameterizedException("The value cannot exceed 99999999");
                    }
                });
            }
        }
    }

    @GetMapping("/start-fte-rates/startId/{startId}")
    public ResponseEntity<StartFteRate> getStartFteRate(@PathVariable Long startId) {
        log.info("[APN: StartFteRate @{}] REST request to get StartFteRate : {}", SecurityUtils.getUserId(), startId);
        StartFteRate startFteRate = startFteRateService.findByStartId(startId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(startFteRate));
    }
}
