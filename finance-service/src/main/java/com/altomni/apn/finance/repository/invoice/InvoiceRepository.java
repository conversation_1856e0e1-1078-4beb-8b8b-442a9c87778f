package com.altomni.apn.finance.repository.invoice;

import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.domain.invoice.QInvoice;
import com.altomni.apn.finance.service.vo.job.JobSimpleVO;
import com.altomni.apn.finance.service.vo.talent.TalentSimpleVO;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


/**
 * Spring Data JPA repository for the Invoice entity.
 */
@Repository
public interface InvoiceRepository extends JpaRepository<Invoice, Long>, QuerydslPredicateExecutor<Invoice>, QuerydslBinderCustomizer<QInvoice> {

    @Override
    default public void customize(QuerydslBindings bindings, QInvoice root) {
        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    }

    List<Invoice> findByInvoiceNo(String invoiceNo);

    List<Invoice> findByStartId(Long startId);

    List<Invoice> findByCompanyId(Long companyId);

    List<Invoice> findByCompanyIdAndInvoiceType(Long companyId, InvoiceType invoiceType);

    Invoice findByInvoiceNoAndInvoiceTypeAndTenantId(String invoiceNo, InvoiceType invoiceType, Long tenantId);

    @Query(value = "SELECT t.* FROM invoice t WHERE t.status IN(?1) AND t.due_date < NOW()", nativeQuery = true)
    List<Invoice> findByDueDate(List<Integer> statusList);

    List<Invoice> findByInvoiceNoAndTenantId(String invoiceNo, Long tenantId);

    @Query(value = "select j.id, j.title, c.id as companyId from job j " +
            "left join company c on c.id=j.company_id " +
            "where j.id =:jobId", nativeQuery = true)
    JobSimpleVO getSimpleJobById(@Param("jobId") Long jobId);

    @Query(value = "select t.id,t.full_name as fullName from talent t where t.id =:talentId ", nativeQuery = true)
    TalentSimpleVO getSimpleTalentById(@Param("talentId") Long talentId);

    List<Invoice> findAllByStartIdIn(Collection<Long> startIds);

    @Query(value = """
        SELECT s.talent_recruitment_process_id talentRecruitmentProcessId
        FROM invoice i
        LEFT JOIN start s ON i.start_id = s.id
        WHERE i.id = :id
""", nativeQuery = true)
    Long getTalentRecruitmentIdByInvoice(@Param("id") Long id);


}
