package com.altomni.apn.finance.service.start;


import com.altomni.apn.finance.domain.start.StartFailedWarranty;
import com.altomni.apn.finance.service.dto.start.StartDTO;

import java.io.IOException;

/**
 * Service Interface for managing StartFailedWarranty.
 */
public interface StartFailedWarrantyService {

    /**
     * Save a startFailedWarranty.
     *
     * @param startFailedWarranty the entity to save
     * @return the persisted entity
     */
    StartDTO save(StartFailedWarranty startFailedWarranty) throws IOException;

    StartDTO update(StartFailedWarranty startFailedWarranty) throws IOException;
}
