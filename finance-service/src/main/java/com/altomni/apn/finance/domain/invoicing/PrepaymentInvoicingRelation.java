package com.altomni.apn.finance.domain.invoicing;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.company.InvoicingRelationType;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Entity
@Table(name = "prepayment_invoicing_relation")
public class PrepaymentInvoicingRelation  extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 开票id */
    @Column(name = "invoicing_id")
    private Long invoicingId ;

    /** 支付id */
    @Column(name = "payment_id")
    private Long paymentId ;

    /** 总金额 */
    @Column(name = "invoicing_amount")
    private BigDecimal invoicingAmount ;

    /** 预付金id */
    @Column(name = "prepayment_invoicing_id")
    private Long prepaymentInvoicingId ;

    /** 1、添加 2使用 */
    @Column(name = "relation_type")
    private InvoicingRelationType relationType ;

    @Column(name = "status")
    private Integer status ;
}
