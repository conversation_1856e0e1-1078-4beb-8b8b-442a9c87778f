package com.altomni.apn.finance.service.dto.start;


import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.job.domain.application.OfferLetterCostRate;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Convert;
import javax.persistence.Transient;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * A StartContractRate.
 */

public class StartContractRateDTO implements Serializable {

    private static final long serialVersionUID = 8847201502936516014L;

    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to.")
    private Long tenantId;

    @NotNull
    private Long startId;

    @JsonIgnore
    @ApiModelProperty(value = "The rate that the current rate extend from.")
    private Long extendStartContractRateId;

    private LocalDate startDate;

    private LocalDate endDate;

    private Integer currency = CurrencyConstants.USD;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType rateUnitType;

    private BigDecimal finalBillRate = BigDecimal.ZERO;

    private BigDecimal finalPayRate = BigDecimal.ZERO;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    private String taxBurdenRateCode;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    private String mspRateCode;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    private String immigrationCostCode;

    private BigDecimal extraCost = BigDecimal.ZERO;

    private BigDecimal estimatedWorkingHourPerWeek = BigDecimal.ZERO;

    @ApiModelProperty(value = "GP")
    private BigDecimal totalBillAmount = BigDecimal.ZERO;

    private String note;

    @Transient
    @JsonProperty
    private OfferLetterCostRate taxBurdenRate;

    @Transient
    @JsonProperty
    private OfferLetterCostRate mspRate;

    @Transient
    @JsonProperty
    private OfferLetterCostRate immigrationCost;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("endDate"));

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getStartId() {
        return startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getExtendStartContractRateId() {
        return extendStartContractRateId;
    }

    public void setExtendStartContractRateId(Long extendStartContractRateId) {
        this.extendStartContractRateId = extendStartContractRateId;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public RateUnitType getRateUnitType() {
        return rateUnitType;
    }

    public void setRateUnitType(RateUnitType rateUnitType) {
        this.rateUnitType = rateUnitType;
    }

    public BigDecimal getFinalBillRate() {
        return finalBillRate;
    }

    public void setFinalBillRate(BigDecimal finalBillRate) {
        this.finalBillRate = finalBillRate;
    }

    public BigDecimal getFinalPayRate() {
        return finalPayRate;
    }

    public void setFinalPayRate(BigDecimal finalPayRate) {
        this.finalPayRate = finalPayRate;
    }

    public String getTaxBurdenRateCode() {
        return taxBurdenRateCode;
    }

    public void setTaxBurdenRateCode(String taxBurdenRateCode) {
        this.taxBurdenRateCode = taxBurdenRateCode;
    }

    public String getMspRateCode() {
        return mspRateCode;
    }

    public void setMspRateCode(String mspRateCode) {
        this.mspRateCode = mspRateCode;
    }

    public String getImmigrationCostCode() {
        return immigrationCostCode;
    }

    public void setImmigrationCostCode(String immigrationCostCode) {
        this.immigrationCostCode = immigrationCostCode;
    }

    public OfferLetterCostRate getTaxBurdenRate() {
        return taxBurdenRate;
    }

    public void setTaxBurdenRate(OfferLetterCostRate taxBurdenRate) {
        this.taxBurdenRate = taxBurdenRate;
    }

    public OfferLetterCostRate getMspRate() {
        return mspRate;
    }

    public void setMspRate(OfferLetterCostRate mspRate) {
        this.mspRate = mspRate;
    }

    public OfferLetterCostRate getImmigrationCost() {
        return immigrationCost;
    }

    public void setImmigrationCost(OfferLetterCostRate immigrationCost) {
        this.immigrationCost = immigrationCost;
    }

    public BigDecimal getExtraCost() {
        return extraCost;
    }

    public void setExtraCost(BigDecimal extraCost) {
        this.extraCost = extraCost;
    }

    public BigDecimal getEstimatedWorkingHourPerWeek() {
        return estimatedWorkingHourPerWeek;
    }

    public void setEstimatedWorkingHourPerWeek(BigDecimal estimatedWorkingHourPerWeek) {
        this.estimatedWorkingHourPerWeek = estimatedWorkingHourPerWeek;
    }

    public BigDecimal getTotalBillAmount() {
        return totalBillAmount;
    }

    public void setTotalBillAmount(BigDecimal totalBillAmount) {
        this.totalBillAmount = totalBillAmount;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StartContractRateDTO startContractRate = (StartContractRateDTO) o;
        if (startContractRate.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), startContractRate.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "StartContractRate{" +
            "id=" + id +
            ", tenantId=" + tenantId +
            ", startId=" + startId +
            ", extendStartContractRateId=" + extendStartContractRateId +
            ", startDate=" + startDate +
            ", endDate=" + endDate +
            ", currency=" + currency +
            ", rateUnitType=" + rateUnitType +
            ", finalBillRate=" + finalBillRate +
            ", finalPayRate=" + finalPayRate +
            ", taxBurdenRateCode='" + taxBurdenRateCode + '\'' +
            ", mspRateCode='" + mspRateCode + '\'' +
            ", immigrationCostCode='" + immigrationCostCode + '\'' +
            ", extraCost=" + extraCost +
            ", estimatedWorkingHourPerWeek=" + estimatedWorkingHourPerWeek +
            ", totalBillAmount=" + totalBillAmount +
            ", note='" + note + '\'' +
            ", taxBurdenRate=" + taxBurdenRate +
            ", mspRate=" + mspRate +
            ", immigrationCost=" + immigrationCost +
            '}';
    }
}
