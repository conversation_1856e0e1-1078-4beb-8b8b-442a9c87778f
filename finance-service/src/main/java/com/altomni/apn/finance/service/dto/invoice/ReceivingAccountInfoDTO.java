package com.altomni.apn.finance.service.dto.invoice;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReceivingAccountInfoDTO implements Serializable {

    private static final long serialVersionUID = -624738900796842116L;

    private String routing;
    private String account;
    private String accountName;
    private String accountType;
    private String accountNumber;
    private String swiftCode;
    private String accountAddress;
    private String bankName;
    private String bankAddress;
    private String iban;
    private String bic;
    private String branchName;
    private String branchCode;
    private String swiftCodeIntlwires;
    private String domestic;
    private String achAndWireRoutingNumber;
    private String routingNumber;
    private String swiftBic;
    private String sortCode;
    private String accountHolder;
    private String bankCodeBic;
    private String transitNumber;
    private String institutionNumber;

}
