package com.altomni.apn.finance.service.start.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.application.FeeType;
import com.altomni.apn.common.domain.enumeration.application.ResignationReason;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationDTO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.enumeration.start.StartFailedWarrantyActionPlan;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.start.Start;
import com.altomni.apn.finance.domain.start.StartFailedWarranty;
import com.altomni.apn.finance.domain.start.StartFteRate;
import com.altomni.apn.finance.repository.start.StartFailedWarrantyRepository;
import com.altomni.apn.finance.repository.start.StartFteRateRepository;
import com.altomni.apn.finance.service.application.ApplicationClient;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.start.StartFailedWarrantyService;
import com.altomni.apn.finance.service.start.StartService;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;

/**
 * Service Implementation for managing StartFailedWarranty.
 */
@Service
@Transactional
public class StartFailedWarrantyServiceImpl implements StartFailedWarrantyService {

    private final Logger log = LoggerFactory.getLogger(StartFailedWarrantyServiceImpl.class);

    @Resource
    private StartFailedWarrantyRepository startFailedWarrantyRepository;

    @Resource
    private StartService startService;

    @Resource
    private StartFteRateRepository startFteRateRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Resource
    private ApplicationClient applicationClient;


    /**
     * Save a startFailedWarranty.
     *
     * @param startFailedWarranty the entity to save
     * @return the persisted entity
     */
    @Override
    public StartDTO save(StartFailedWarranty startFailedWarranty) throws IOException {
        if (startFailedWarranty.getStartId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_STARTFAILEDWARRANTY_STARTIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        if (startFailedWarranty.getEndDate() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_STARTFAILEDWARRANTY_ENDDATENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        Start start = startService.findById(startFailedWarranty.getStartId());
        if (!JobType.FULL_TIME.equals(start.getPositionType())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_STARTFAILEDWARRANTY_JOBTYPEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        StartFteRate fteRate = null;
        if (StartFailedWarrantyActionPlan.NO_REPLACEMENT.equals(startFailedWarranty.getActionPlan())) {
            if (startFailedWarranty.getTotalBillAmount() == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_STARTFAILEDWARRANTY_TOTALAMOUNTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
            }
            StartFteRate startFteRate = startFteRateRepository.findByStartId(start.getId());
            startFteRate.setFeeType(FeeType.FLAT_AMOUNT);
            startFteRate.setTotalBillAmount(startFailedWarranty.getTotalBillAmount());
            fteRate = startFteRateRepository.save(startFteRate);
        }
        StartFailedWarranty failedWarranty = startFailedWarrantyRepository.save(startFailedWarranty);
        start.setStatus(StartStatus.FTE_FAIL_WARRANTY);
        start.setWarrantyEndDate(startFailedWarranty.getEndDate());
        start.setNote(startFailedWarranty.getNote());
        startService.saveOnly(start);
        // failed warranty 后，入职状态自动变为离职
        this.autoResign(start, startFailedWarranty);
        return startService.fillDto(start, new StartDTO().setStartFteRate(fteRate).setFailedWarranty(failedWarranty));
    }

    private void autoResign(Start start, StartFailedWarranty startFailedWarranty){
        ResignationReason resignationReason = null;
        try {
            resignationReason = ResignationReason.valueOf(startFailedWarranty.getReason().name());
        }catch (IllegalArgumentException e){
            log.error(ExceptionUtils.getStackTrace(e));
        }
        applicationClient.resign(new TalentRecruitmentProcessResignationDTO()
                .setTalentRecruitmentProcessId(start.getTalentRecruitmentProcessId())
                .setResignDate(startFailedWarranty.getEndDate())
                .setResignationReason(resignationReason)
                .setNote(startFailedWarranty.getNote()));
    }

    @Override
    public StartDTO update(StartFailedWarranty update) throws IOException {
        StartFailedWarranty exist =  startFailedWarrantyRepository.findByStartId(update.getStartId());
        if (exist == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_STARTFAILEDWARRANTY_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(update.getStartId()),financeApiPromptProperties.getFinanceService()));
        }
        StartFteRate fteRate = null;
        if (StartFailedWarrantyActionPlan.NO_REPLACEMENT.equals(update.getActionPlan())) {
            if (update.getTotalBillAmount() == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_STARTFAILEDWARRANTY_UPDATETOTALAMOUNTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
            }
            StartFteRate startFteRate = startFteRateRepository.findByStartId(update.getStartId());
            startFteRate.setFeeType(FeeType.FLAT_AMOUNT);
            startFteRate.setTotalBillAmount(update.getTotalBillAmount());
            fteRate = startFteRateRepository.save(startFteRate);
        }
        // end date not change, no need to update start and application.
        StartFailedWarranty failedWarranty = null;
        if (update.getEndDate() == null || exist.getEndDate().equals(update.getEndDate())) {
            ServiceUtils.myCopyProperties(update, exist, StartFailedWarranty.UpdateSkipProperties);
            failedWarranty = startFailedWarrantyRepository.save(exist);
            return startService.fillDto(startService.findById(exist.getStartId()), new StartDTO().setStartFteRate(fteRate).setFailedWarranty(failedWarranty));
        }
        // end date changed, need to update start and application.
        ServiceUtils.myCopyProperties(update, exist, StartFailedWarranty.UpdateSkipProperties);
        failedWarranty = startFailedWarrantyRepository.save(exist);
        Start start = startService.findById(exist.getStartId());
        if (update.getTotalBillAmount() != null) {
            start.setStatus(StartStatus.FTE_FAIL_WARRANTY);
            start.setWarrantyEndDate(update.getEndDate());
            startService.saveOnly(start);
        }
        return startService.fillDto(start, new StartDTO().setFailedWarranty(failedWarranty));
    }
}
