package com.altomni.apn.finance.repository.invoicing;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.enumeration.company.*;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.CoAmUserCountryVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessForTalentVO;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.StringUtil;
import com.altomni.apn.finance.service.dto.invoicing.InvoicingApplicationInfoForOverdueVO;
import com.altomni.apn.finance.service.dto.invoicing.InvoicingApplicationInfoSearchDTO;
import com.altomni.apn.finance.service.dto.start.StartInfoForInvoicingVO;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.finance.service.vo.invoicing.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class InvoicingNativeRepository {

    @Resource
    private EntityManager entityManager;

    @Transactional(readOnly = true)
    public Boolean selectIsStartAm(Long companyId, List<Integer> userRole) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select s.* from start s
                left join talent_recruitment_process_eliminate trpe
                 on s.talent_recruitment_process_id = trpe.talent_recruitment_process_id
                 join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = s.talent_recruitment_process_id
                 where s.tenant_id=:talentId and trpe.id is null and kpi.user_id =:userId and s.company_id=:companyId and kpi.user_role in (:userRole)
                 """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("companyId", companyId);
        dataQuery.setParameter("talentId", SecurityUtils.getTenantId());
        dataQuery.setParameter("userId", SecurityUtils.getUserId());
        dataQuery.setParameter("userRole", userRole);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty() || mapList == null) {
            return false;
        }
        return true;
    }

    /**
     * 查询实收金额
     *
     * @param startId
     * @return
     */
    @Transactional(readOnly = true)
    public BigDecimal selectReceivedAmount(Long startId, Long talentId, Long jobId) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select IFNULL(sum(amount_received),0) as amountReceived from invoicing_candidate_info ici
                join invoicing_application_info iai on ici.invoice_application_id = iai.id
                where iai.invoicing_status in (0,2,3,4,5,6,7) and iai.`status` = 1 and ici.`status` = 1
                and ici.start_id =:startId and iai.void_invoicing is null
                and ici.talent_id =:talentId and ici.job_id =:jobId
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("startId", startId);
        dataQuery.setParameter("talentId", talentId);
        dataQuery.setParameter("jobId", jobId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty() || mapList == null) {
            return BigDecimal.ZERO;
        }
        String amountReceivedStr = mapList.get(0).get("amountReceived") + "";
        if (amountReceivedStr == null || amountReceivedStr.equals("null") || amountReceivedStr.equals("")) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(amountReceivedStr);
    }

    /**
     * 查询am等信息
     *
     * @param startIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoicingKpiUserRoleVO> selectKpiUserRoleByStartIds(List<Long> startIds) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select CONCAT(u.first_name, ' ', u.last_name) as userName,trpku.percentage,cast(trpku.user_role as char) as userRole,s.id,u.id as userId,trpku.country
                from user u, start_commission trpku,start s
                where s.id = trpku.start_id
                and trpku.user_id = u.id and trpku.user_role in (0,1,2,3,4,7,8,9) and s.id in (:startIds)
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("startIds", startIds);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoicingKpiUserRoleVO.class));

        return dataQuery.getResultList();
    }

    /**
     * 查询bd manager等信息
     *
     * @param businessIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoicingKpiUserRoleVO> selectBdManagerByBusinessId(List<Long> businessIds) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select CAST( t.contribution as decimal(10,2) ) as percentage,CONCAT(u.first_name, ' ', u.last_name) as userName,
                cast(t.sales_lead_role as char) as userRole,b.id as id,u.id as userId
                from business_flow_administrator t
                left join user u on u.id = t.user_id
                left join job b on b.sales_lead_id = t.account_business_id
                where t.sales_lead_role in(2,1) and b.id in (:businessIds)
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("businessIds", businessIds);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoicingKpiUserRoleVO.class));

        return dataQuery.getResultList();
    }


    /**
     * Paging Query Data
     *
     * @param dto
     * @param pageable
     * @return
     */
    @Transactional(readOnly = true)
    public Page<InvoicingApplicationInfoVO> searchGroupInvoiceList(InvoicingApplicationInfoSearchDTO dto, Pageable pageable, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select DISTINCT t.id,t.created_date as createdDate,
                cast(t.invoicing_application_type as char) as invoicingApplicationType,
                u.username as applicationUser,
                (select group_concat(distinct a.talent_name SEPARATOR ',') from invoicing_candidate_info a where t.id = a.invoice_application_id and a.status=1) as talentName,
                (select group_concat(distinct a.job_name SEPARATOR ',') from invoicing_candidate_info a where t.id = a.invoice_application_id and a.status=1) as jobName,
                c.full_business_name as companyName,
                t.gp_amount as gpAmount,
                t.invoicing_amount as invoicingAmount,
                t.invoicing_tax as invoicingTax,
                t.taxes_not_included as taxesNotIncluded,
                t.invoice_tax as invoiceTax,
                t.uninvoiced_amount as uninvoicedAmount,
                cast(t.invoicing_status as char) as invoicingStatus,
                t.electronic_invoice_number as elecInvoiceNumber,
                t.code_number as codeNumber,
                t.invoicing_date as invoicingDate,
                t.due_within_days as dueWithinDays,
                t.payment_due_date as paymentDueDate,
                ccii.client_name as clientName,
                ccii.social_credit_code as socialCreditCode,
                ccii.bank_name as bankName,
                ccii.bank_account as bankAccount,
                ccii.invoicing_address as invoicingAddress,
                ccii.phone as phone,
                cast(t.invoicing_body as char) as invoicingBody,
                cast(t.invoicing_business_type as char) as invoicingBusinessType,
                (select group_concat(distinct a.invoice_area SEPARATOR ',') from invoicing_candidate_info a where t.id = a.invoice_application_id and a.status=1)as invoiceArea,
                cast(t.invoicing_type as char) as invoicingType,
                eist.service_name as invoicingTaxName,
                cast(t.invoice_format as char) as invoiceFormat,
                ai.prepaymentAmount,
                case when irpic.paymentAmount is null then 0 else irpic.paymentAmount end  as paymentAmount,
                case when invoicing_application_type=1 then CONCAT(t.amount_due,'')
                     else t.invoicing_amount - case when irpic.paymentAmount is null then '0' else CONCAT(irpic.paymentAmount,'') end end as amountDue,
                (select group_concat(a.user_name SEPARATOR ',') from invoicing_candidate_detail a where t.id = a.invoice_application_id and a.status=1 and a.user_role=1) as accountManager,
                (select group_concat(a.user_name SEPARATOR ',') from invoicing_candidate_detail a where t.id = a.invoice_application_id and a.status=1 and a.user_role=2) as recruiter,
                (select group_concat(a.user_name SEPARATOR ',') from invoicing_candidate_detail a where t.id = a.invoice_application_id and a.status=1 and a.user_role=4) as sourcer,
                (select group_concat(a.user_name SEPARATOR ',') from invoicing_candidate_detail a where t.id = a.invoice_application_id and a.status=1 and a.user_role=3) as deliveryManager,
                (select group_concat(a.user_name SEPARATOR ',') from invoicing_candidate_detail a where t.id = a.invoice_application_id and a.status=1 and a.user_role=5) as owner,
                (select group_concat(a.user_name SEPARATOR ',') from invoicing_candidate_detail a where t.id = a.invoice_application_id and a.status=1 and a.user_role=6) as salesLeadManager,
                (select group_concat(a.user_name SEPARATOR ',') from invoicing_candidate_detail a where t.id = a.invoice_application_id and a.status=1 and a.user_role=7) as bdManager,
                (select group_concat(concat(a.user_name, '-', a.country ) SEPARATOR ',') from invoicing_candidate_detail a where t.id = a.invoice_application_id and a.status=1 and a.user_role=8) as coAm
                from invoicing_application_info t
                left join invoicing_candidate_info ici on t.id = ici.invoice_application_id and ici.`status`=1
                left join company  c on c.id = t.company_id
                left join company_client_invoicing_info ccii on ccii.id = t.client_invoicing_id
                left join enum_invoicing_service_tax eist on eist.id = t.invoicing_server_tax_id
                left join (select sum(amount_prepayment) as prepaymentAmount,invoice_application_id from invoicing_candidate_info where status=1 group by invoice_application_id) ai on ai.invoice_application_id = t.id
                left join (select sum(payment_amount) as paymentAmount,invoicing_id from invoicing_record_payment_info where status=1 group by invoicing_id) irpic on irpic.invoicing_id = t.id
                left join user u on u.id = t.puser_id
                left JOIN permission_user_team put ON put.user_id = u.id and put.is_primary = 1
                where t.status=1 and t.tenant_id=:tenantId  and (t.prepayment_type =2 or t.prepayment_type is null )
                """);
        String whereSql = assemblyQueryCondition(dto, teamDataPermission);

        dataSql.append(whereSql);

        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (!Objects.isNull(order)) {
            if (order.getProperty().equals("companyName")) {
                dataSql.append(" order by CONVERT(" + order.getProperty() + "  USING GBK) " + order.getDirection());
            } else {
                dataSql.append(" order by " + order.getProperty() + " " + order.getDirection());
            }
        } else {
            dataSql.append(" order by t.invoicing_status asc,t.created_date desc");

        }

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(countSql);

        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        countQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        assemblyQueryParam(dataQuery, countQuery, dto, teamDataPermission);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoicingApplicationInfoVO.class));
        dataQuery.setFirstResult((int) pageable.getOffset());
        dataQuery.setMaxResults(pageable.getPageSize());

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<InvoicingApplicationInfoVO> invoicingApplicationInfoVOList = total > pageable.getOffset() ? dataQuery.getResultList() : new ArrayList<>();
        invoicingApplicationInfoVOList.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAm())) {
                List<UserCountryVO> voList = new ArrayList<>();
                Arrays.stream(x.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserName(amCountry[0]);
                    vo.setCountryId(amCountry[1]);
                    voList.add(vo);
                });
                x.setCoAmList(voList);
            }
        });
        return new PageImpl<>(invoicingApplicationInfoVOList, pageable, total);
    }

    private void assemblyQueryParam(Query dataQuery, Query countQuery, InvoicingApplicationInfoSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {

        if (dto.getCodeNumber() != null) {
            dataQuery.setParameter("codeNumber", dto.getCodeNumber());
            countQuery.setParameter("codeNumber", dto.getCodeNumber());
        }

        if (dto.getClientName() != null) {
            dataQuery.setParameter("clientName", dto.getClientName());
            countQuery.setParameter("clientName", dto.getClientName());
        }

        if (dto.getInvoicingApplicationTypeList() != null) {
            dataQuery.setParameter("invoicingApplicationType", dto.getInvoicingApplicationTypeList().stream().mapToInt(InvoicingApplicationType::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoicingApplicationType", dto.getInvoicingApplicationTypeList().stream().mapToInt(InvoicingApplicationType::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (dto.getElecInvoiceNumber() != null) {
            dataQuery.setParameter("elecInvoiceNumber", dto.getElecInvoiceNumber());
            countQuery.setParameter("elecInvoiceNumber", dto.getElecInvoiceNumber());
        }

        if (null != dto.getInvoicingDateStart() && null != dto.getInvoicingDateEnd()) {
            dataQuery.setParameter("invoicingDateStart", dto.getInvoicingDateStart());
            countQuery.setParameter("invoicingDateStart", dto.getInvoicingDateStart());
            dataQuery.setParameter("invoicingDateEnd", dto.getInvoicingDateEnd());
            countQuery.setParameter("invoicingDateEnd", dto.getInvoicingDateEnd());
        }

        if (null != dto.getInvoicingStatusList() && !dto.getInvoicingStatusList().isEmpty()) {
            dataQuery.setParameter("invoicingStatus", dto.getInvoicingStatusList().stream().mapToInt(InvoicingStatus::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoicingStatus", dto.getInvoicingStatusList().stream().mapToInt(InvoicingStatus::toDbValue).boxed().collect(Collectors.toList()));

        }

        if (null != dto.getInvoicingTypeList() && !dto.getInvoicingTypeList().isEmpty()) {
            dataQuery.setParameter("invoicingType", dto.getInvoicingTypeList().stream().mapToInt(InvoicingType::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoicingType", dto.getInvoicingTypeList().stream().mapToInt(InvoicingType::toDbValue).boxed().collect(Collectors.toList()));
        }
        if (null != dto.getCreatedDateStart() && null != dto.getCreatedDateEnd()) {
            dataQuery.setParameter("createdDateStart", dto.getCreatedDateStart());
            countQuery.setParameter("createdDateStart", dto.getCreatedDateStart());
            dataQuery.setParameter("createdDateEnd", dto.getCreatedDateEnd());
            countQuery.setParameter("createdDateEnd", dto.getCreatedDateEnd());
        }

        if (null != dto.getApplicationUserIdList() && !dto.getApplicationUserIdList().isEmpty()) {
            dataQuery.setParameter("applicationUserId", dto.getApplicationUserIdList());
            countQuery.setParameter("applicationUserId", dto.getApplicationUserIdList());
        }

        if (null != dto.getApplicationUserTeamIdList() && !dto.getApplicationUserTeamIdList().isEmpty()) {
            dataQuery.setParameter("applicationUserTeamId", dto.getApplicationUserTeamIdList());
            countQuery.setParameter("applicationUserTeamId", dto.getApplicationUserTeamIdList());
        }

        if (null != dto.getTalentIdList() && !dto.getTalentIdList().isEmpty()) {
            dataQuery.setParameter("talentId", dto.getTalentIdList());
            countQuery.setParameter("talentId", dto.getTalentIdList());
        }

        if (null != dto.getJobIdList() && !dto.getJobIdList().isEmpty()) {
            dataQuery.setParameter("jobId", dto.getJobIdList());
            countQuery.setParameter("jobId", dto.getJobIdList());
        }

        if (null != dto.getCompanyIdList() && !dto.getCompanyIdList().isEmpty()) {
            dataQuery.setParameter("companyId", dto.getCompanyIdList());
            countQuery.setParameter("companyId", dto.getCompanyIdList());
        }

        if (null != dto.getInvoicingBusinessTypeList() && !dto.getInvoicingBusinessTypeList().isEmpty()) {
            dataQuery.setParameter("invoicingBusinessType", dto.getInvoicingBusinessTypeList().stream().mapToInt(InvoicingBusinessType::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoicingBusinessType", dto.getInvoicingBusinessTypeList().stream().mapToInt(InvoicingBusinessType::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (null != dto.getInvoicingServerNameList() && !dto.getInvoicingServerNameList().isEmpty()) {
            dataQuery.setParameter("invoicingServiceId", dto.getInvoicingServerNameList());
            countQuery.setParameter("invoicingServiceId", dto.getInvoicingServerNameList());
        }

        if (dto.getInvoicingTax() != null) {
            dataQuery.setParameter("invoicingTax", dto.getInvoicingTax());
            countQuery.setParameter("invoicingTax", dto.getInvoicingTax());
        }

        if (dto.getDueWithinDays() != null) {
            dataQuery.setParameter("dueWithinDays", dto.getDueWithinDays());
            countQuery.setParameter("dueWithinDays", dto.getDueWithinDays());
        }

        if (null != dto.getPaymentDueDate()) {
            dataQuery.setParameter("paymentDueDate", dto.getPaymentDueDate());
            countQuery.setParameter("paymentDueDate", dto.getPaymentDueDate());
        }

        if (null != dto.getInvoicingBodyList() && !dto.getInvoicingBodyList().isEmpty()) {
            dataQuery.setParameter("invoicingBody", dto.getInvoicingBodyList().stream().mapToInt(InvoicingBody::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoicingBody", dto.getInvoicingBodyList().stream().mapToInt(InvoicingBody::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (null != dto.getInvoicingFormatList() && !dto.getInvoicingFormatList().isEmpty()) {
            dataQuery.setParameter("invoicingFormat", dto.getInvoicingFormatList().stream().mapToInt(InvoicingFormat::toDbValue).boxed().collect(Collectors.toList()));
            countQuery.setParameter("invoicingFormat", dto.getInvoicingFormatList().stream().mapToInt(InvoicingFormat::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (!SecurityUtils.isAdmin()) {
            if (teamDataPermission.getSelf()) {
                dataQuery.setParameter("userSelf", SecurityUtils.getUserId());
                countQuery.setParameter("userSelf", SecurityUtils.getUserId());
            } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                dataQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());
                countQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());

            }
        }
    }

    private String assemblyQueryCondition(InvoicingApplicationInfoSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        StringBuilder whereSql = new StringBuilder();

        if (dto.getCodeNumber() != null) {
            whereSql.append(" and t.code_number = :codeNumber");
        }

        if (dto.getClientName() != null) {
            whereSql.append(" and ccii.client_name = :clientName");
        }

        if (dto.getInvoicingApplicationTypeList() != null) {
            whereSql.append(" and t.invoicing_application_type in ( :invoicingApplicationType )");
        }

        if (dto.getElecInvoiceNumber() != null) {
            whereSql.append(" and t.electronic_invoice_number in ( :elecInvoiceNumber )");
        }

        if (null != dto.getInvoicingDateStart() && null != dto.getInvoicingDateEnd()) {
            whereSql.append(" and t.invoicing_date between :invoicingDateStart and :invoicingDateEnd ");
        }

        if (null != dto.getInvoicingStatusList() && !dto.getInvoicingStatusList().isEmpty()) {
            whereSql.append(" and t.invoicing_status in ( :invoicingStatus )");
        }

        if (null != dto.getInvoicingTypeList() && !dto.getInvoicingTypeList().isEmpty()) {
            whereSql.append(" and t.invoicing_type in ( :invoicingType )");
        }

        if (null != dto.getCreatedDateStart() && null != dto.getCreatedDateEnd()) {
            whereSql.append(" and t.created_date between :createdDateStart and :createdDateEnd ");
        }

        if (null != dto.getApplicationUserIdList() && !dto.getApplicationUserIdList().isEmpty()) {
            whereSql.append(" and t.puser_id in ( :applicationUserId )");
        }

        if (null != dto.getApplicationUserTeamIdList() && !dto.getApplicationUserTeamIdList().isEmpty()) {
            whereSql.append(" and put.team_id in ( :applicationUserTeamId )");
        }

        if (null != dto.getTalentIdList() && !dto.getTalentIdList().isEmpty()) {
            whereSql.append(" and  ici.talent_name in ( :talentId )");
        }

        if (null != dto.getJobIdList() && !dto.getJobIdList().isEmpty()) {
            whereSql.append(" and  ici.job_name in ( :jobId )");
        }

        if (null != dto.getCompanyIdList() && !dto.getCompanyIdList().isEmpty()) {
            whereSql.append(" and  t.company_id in ( :companyId )");
        }

        if (null != dto.getInvoicingBusinessTypeList() && !dto.getInvoicingBusinessTypeList().isEmpty()) {
            whereSql.append(" and  t.invoicing_business_type in ( :invoicingBusinessType )");
        }

        if (null != dto.getInvoicingServerNameList() && !dto.getInvoicingServerNameList().isEmpty()) {
            whereSql.append(" and  t.invoicing_server_tax_id in ( :invoicingServiceId )");
        }

        if (dto.getInvoicingTax() != null) {
            whereSql.append(" and t.invoicing_tax = :invoicingTax ");
        }

        if (dto.getDueWithinDays() != null) {
            whereSql.append(" and t.due_within_days = :dueWithinDays ");
        }

        if (null != dto.getPaymentDueDate()) {
            whereSql.append(" and t.payment_due_date = :paymentDueDate ");
        }

        if (null != dto.getInvoicingBodyList() && !dto.getInvoicingBodyList().isEmpty()) {
            whereSql.append(" and  t.invoicing_body in ( :invoicingBody )");
        }

        if (null != dto.getInvoicingFormatList() && !dto.getInvoicingFormatList().isEmpty()) {
            whereSql.append(" and  t.invoice_format in ( :invoicingFormat )");
        }

        if (!SecurityUtils.isAdmin()) {
            if (teamDataPermission.getSelf()) {
                whereSql.append(" and (put.user_id =:userSelf");
                whereSql.append(" or t.id in (select invoice_application_id from invoicing_candidate_detail where status=1 and user_role not in (6,7) and user_id =:userSelf) )");

            } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                String teamIds = teamDataPermission.getNestedTeamIds().stream().map(String::valueOf).collect(Collectors.joining(","));
                whereSql.append(" and (put.team_id in ( :teamId )");
                whereSql.append(" or t.id in (select invoice_application_id from invoicing_candidate_detail icd where icd.status=1 and user_role not in (6,7) and ");
                whereSql.append(" exists (select 1 from permission_user_team zzz where zzz.user_id = icd.user_id and zzz.is_primary = 1 and zzz.team_id in(" + teamIds + "))))");
            }
        }

        if (null != dto.getAccountManagerList() && !dto.getAccountManagerList().isEmpty()) {
            whereSql.append(" and EXISTS (select 1 from invoicing_candidate_detail x where x.status=1 and  x.user_id and user_role=1 and t.id = x.invoice_application_id   ");
            whereSql.append(" and user_id in (" + dto.getAccountManagerList().stream().map(String::valueOf).collect(Collectors.joining(",")) + ") )");
        }

        if (null != dto.getRecruiterList() && !dto.getRecruiterList().isEmpty()) {
            whereSql.append(" and EXISTS (select 1 from invoicing_candidate_detail x where x.status=1 and  x.user_id and user_role=2 and t.id = x.invoice_application_id   ");
            whereSql.append(" and user_id in (" + dto.getRecruiterList().stream().map(String::valueOf).collect(Collectors.joining(",")) + ") )");
        }

        if (null != dto.getDeliveryManagerList() && !dto.getDeliveryManagerList().isEmpty()) {
            whereSql.append(" and EXISTS (select 1 from invoicing_candidate_detail x where x.status=1 and  x.user_id and user_role=3 and t.id = x.invoice_application_id   ");
            whereSql.append(" and user_id in (" + dto.getDeliveryManagerList().stream().map(String::valueOf).collect(Collectors.joining(",")) + ") )");
        }
        //1、am 2、recruiter 3、dm 4、sourcer 5、owner 6、salesLeadManager 7、bdManager 8、coam

        if (null != dto.getSourcerList() && !dto.getSourcerList().isEmpty()) {
            whereSql.append(" and EXISTS (select 1 from invoicing_candidate_detail x where x.status=1 and  x.user_id and user_role=4 and t.id = x.invoice_application_id   ");
            whereSql.append(" and user_id in (" + dto.getSourcerList().stream().map(String::valueOf).collect(Collectors.joining(",")) + ") )");
        }

        if (null != dto.getOwnerList() && !dto.getOwnerList().isEmpty()) {
            whereSql.append(" and EXISTS (select 1 from invoicing_candidate_detail x where x.status=1 and  x.user_id and user_role=5 and t.id = x.invoice_application_id   ");
            whereSql.append(" and user_id in (" + dto.getOwnerList().stream().map(String::valueOf).collect(Collectors.joining(",")) + ") )");
        }

        if (null != dto.getBdManagerList() && !dto.getBdManagerList().isEmpty()) {
            whereSql.append(" and EXISTS (select 1 from invoicing_candidate_detail x where x.status=1 and  x.user_id and user_role=7 and t.id = x.invoice_application_id   ");
            whereSql.append(" and user_id in (" + dto.getBdManagerList().stream().map(String::valueOf).collect(Collectors.joining(",")) + ") )");
        }

        if (null != dto.getSalesLeadManagerList() && !dto.getSalesLeadManagerList().isEmpty()) {
            whereSql.append(" and EXISTS (select 1 from invoicing_candidate_detail x where x.status=1 and  x.user_id and user_role=6 and t.id = x.invoice_application_id   ");
            whereSql.append(" and user_id in (" + dto.getSalesLeadManagerList().stream().map(String::valueOf).collect(Collectors.joining(",")) + ") )");
        }

        if (null != dto.getCooperateAccountManagerList() && !dto.getCooperateAccountManagerList().isEmpty()) {
            whereSql.append(" and EXISTS (select 1 from invoicing_candidate_detail x where x.status=1 and  x.user_id and user_role=8 and t.id = x.invoice_application_id   ");
            whereSql.append(" and user_id in (" + dto.getCooperateAccountManagerList().stream().map(String::valueOf).collect(Collectors.joining(",")) + ") )");
        }

        return whereSql.toString();
    }

    /**
     * Paging Query Data
     *
     * @param dto
     * @return
     */
    @Transactional(readOnly = true)
    public List<InvoicingApplicationInfoDownloadVO> searchGroupInvoiceListDownload(InvoicingApplicationInfoSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select t.id,case when t.tax_payer_type=1 then '客户方' else '我方' end as taxPayerType,t.code_number as codeNumber,DATE_FORMAT(CONVERT_TZ(t.created_date,"+00:00","+08:00"),'%Y-%m-%d') as createdDate,
                case when t.invoicing_application_type=1 then '全职招聘开票申请'
                when t.invoicing_application_type=2 then '预付金发票申请' else '废票' end as invoicingApplicationType,
                u.username as applicationUser,
                ici.talent_name as talentName,
                ici.talent_id as talentId,
                ici.job_name as jobName,
                ici.job_id as jobId,
                c.full_business_name as companyName,
                case when ici.invoice_area=1 then '中国区' else
                case when t.invoicing_application_type=1 then '海外区'  else '' end
                end as invoiceArea,
                case when t.invoicing_business_type=0 then 'FTE'
                when t.invoicing_business_type=1 then 'RPO'
                when t.invoicing_business_type=2 then 'STF-外包'
                else 'STF-人事代理' end as invoicingBusinessType,
                eist.service_name as invoicingTaxName,
                case when t.invoicing_status=0 then '待审批'
                when t.invoicing_status=1 then '审批驳回'
                when t.invoicing_status=2 then '待开票'
                when t.invoicing_status=3 then '已开票'
                when t.invoicing_status=4 then '未回款'
                when t.invoicing_status=5 then '部分回款'
                when t.invoicing_status=6 then '逾期'
                when t.invoicing_status=7 then '全部回款'
                else '已作废' end as invoicingStatus,
                case when t.invoicing_application_type=1 then ici.amount_received
                when t.invoicing_application_type=2 then t.invoicing_amount-t.invoice_tax
                else t.invoicing_amount end as amountReceived,
                case when t.invoicing_application_type=1 then t.uninvoiced_amount
                else case when t.invoicing_application_type=2 then 0 else t.uninvoiced_amount end  end as uninvoicedAmount,
                case when t.invoicing_application_type=1 then ici.gp_amount
                else case when t.invoicing_application_type=2 then 0 else t.invoicing_amount end  end as talentGpAmount,
                t.invoicing_tax as invoicingTax,
                CONCAT(t.invoicing_tax,'%') as invoicingTaxStr,
                t.invoice_tax as invoiceTax,
                t.invoicing_amount as invoicingAmount,
                               
                t.electronic_invoice_number as elecInvoiceNumber,
                DATE_FORMAT(CONVERT_TZ(t.invoicing_date,"+00:00","+08:00"),'%Y-%m-%d') as invoicingDate,
                t.due_within_days as dueWithinDays,
                DATE_FORMAT(CONVERT_TZ(t.payment_due_date,"+00:00","+08:00"),'%Y-%m-%d') as paymentDueDate,
                ccii.client_name as clientName,
                ccii.social_credit_code as socialCreditCode,
                ccii.bank_name as bankName,
                ccii.bank_account as bankAccount,
                ccii.invoicing_address as invoicingAddress,
                ccii.phone as phone,
                case when t.invoicing_body=1 then '英特利普（上海）信息技术有限公司'
                when t.invoicing_body=2 then '英特利普（深圳）科技有限公司' else '尹泰（上海）劳务派遣有限公司' end as invoicingBody,
                case when t.invoicing_type=0 then '增值税电子专业发票'
                when t.invoicing_type=1 then '增值税电子普通发票' else '抵扣发票' end as invoicingType,
                cast(t.invoice_format as char) as invoiceFormat,
                ici.amount_prepayment as prepaymentAmount,
                (select group_concat(distinct DATE_FORMAT(CONVERT_TZ(a.payment_date,"+00:00","+08:00"),'%Y-%m-%d') SEPARATOR ',') from invoicing_record_payment_info a where t.id = a.invoicing_id and a.status=1 ) as paymentDate,
                               
                (select group_concat(distinct
                case when a.payment_method =0 then '汇票'
                when a.payment_method =1 then '储蓄卡'
                when a.payment_method =2 then '信用卡'
                when a.payment_method =3 then '电子汇款'
                else '预付金支付' end SEPARATOR ',') from invoicing_record_payment_info a where t.id = a.invoicing_id and a.status=1 ) as paymentMethod,
                (select group_concat(a.note SEPARATOR ',') from invoicing_record_payment_info a where t.id = a.invoicing_id and a.status=1) as note,
                CONCAT(ici.amount_received_tax,'') as closeFlag,CONCAT(ax.id,'') as voidFlag
                from invoicing_application_info t
                left join invoicing_candidate_info ici on t.id = ici.invoice_application_id and ici.`status`=1
                left join company  c on c.id = t.company_id
                left join company_client_invoicing_info ccii on ccii.id = t.client_invoicing_id
                left join enum_invoicing_service_tax eist on eist.id = t.invoicing_server_tax_id
                left join user u on u.id = t.puser_id
                left join invoicing_application_info ax on ax.invalid_invoicing_id = t.id and ax.status=1
                left JOIN permission_user_team put ON put.user_id = u.id and put.is_primary = 1
                where t.status=1 and t.tenant_id=:tenantId  and (t.prepayment_type =2 or t.prepayment_type is null ) and t.void_invoicing is null
                """);
        String whereSql = assemblyQueryCondition(dto, teamDataPermission);

        dataSql.append(whereSql);

        dataSql.append(" order by t.invoicing_status asc,t.created_date desc");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        assemblyQueryParam(dataQuery, countQuery, dto, teamDataPermission);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(InvoicingApplicationInfoDownloadVO.class));

        return dataQuery.getResultList();
    }

    /**
     * 根据用户查询部门leader
     *
     * @param userIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<UserLeaderVO> searchLeaderByUserId(List<Long> userIds) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                WITH RECURSIVE team_hierarchy AS (
                  -- 基础层级，从用户所在部门开始
                  SELECT
                    ut.user_id AS queried_user_id,
                    ut.team_id,
                    tl.user_id AS leader_user_id
                  FROM
                    permission_user_team ut
                  LEFT JOIN
                    permission_team_leader tl ON ut.team_id = tl.team_id
                  LEFT JOIN
                    permission_team t ON ut.team_id = t.id
                  WHERE
                    ut.user_id IN (:userIds) and ut.is_primary=1 and  ut.tenant_id=:tenantId
                                
                )
                                
                SELECT DISTINCT
                  queried_user_id AS userId,
                   team_id as leaderTeamId,
                   leader_user_id as leaderUserId,
                   CONCAT(u.first_name, ' ', u.last_name) as leaderUserName
                FROM
                  team_hierarchy
                Left join user u on u.id = leader_user_id	
                WHERE
                  leader_user_id IS NOT NULL -- 仅保留找到 leader 的记录
                ORDER BY
                  queried_user_id, team_id;        
                """);


        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());


        dataQuery.setParameter("userIds", userIds);
        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());


        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(UserLeaderVO.class));

        return dataQuery.getResultList();
    }

    /**
     * 根据用户查询部门code
     *
     * @param userIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<UserLeaderVO> searchTeamCodeByUserId(List<Long> userIds) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select pt.`code` as teamCode,t.user_id as userId from permission_user_team t
                  join permission_team pt on pt.id= t.team_id
                   where user_id in (:userIds) and is_primary=1 and t.tenant_id = :tenantId           
                """);


        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());


        dataQuery.setParameter("userIds", userIds);
        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());


        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(UserLeaderVO.class));

        return dataQuery.getResultList();
    }

    /**
     * 查询利润分成用户
     *
     * @return
     */
    @Transactional(readOnly = true)
    public List<PermissionTeamLeaderProfitVO> searchPermissionTeamLeaderProfit() {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select pt.code,CONCAT(u.first_name, ' ', u.last_name) as userName from permission_team_leader_profit t\s
                 left join permission_team pt on pt.id = t.team_id
                 left join user u on u.id = t.user_id where t.tenant_id = :tenantId           
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(PermissionTeamLeaderProfitVO.class));

        return dataQuery.getResultList();
    }


    @Transactional(readOnly = true)
    public List<JobSalesLeadVO> searchJobId(List<Long> jobIds) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select id,sales_lead_id as salesLeadId from job where id in (:jobIds)     
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("jobIds", jobIds);

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(JobSalesLeadVO.class));

        return dataQuery.getResultList();
    }

    public List<UserLeaderVO> findUserIdByTeamId(Set<Long> teamIdList) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""    
                   select DISTINCT t.user_id as userId from permission_user_team t
                   join permission_team pt on pt.id= t.team_id
                   where t.team_id in (:teamIdList) and t.is_primary = 1 and t.tenant_id = :tenantId      
                """);


        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());


        dataQuery.setParameter("teamIdList", teamIdList);
        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());


        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(UserLeaderVO.class));

        return dataQuery.getResultList();
    }

    public List<StartInfoForInvoicingVO> getStartInfoForInvoicing() {
        String query = """
            SELECT
                s.id as start_id,
                s.created_date as onboard_date,
                c.full_business_name as client_name,
                c.id as client_id,
                s.talent_name as talent_name,
                s.talent_id as talent_id,
                GROUP_CONCAT(DISTINCT sc.user_id) as am_ids,
                GROUP_CONCAT(DISTINCT sc.user_full_name) as am_names
            FROM
                start s
                LEFT JOIN start_client_info sci ON sci.start_id = s.id
                LEFT JOIN invoicing_candidate_info ici ON ici.start_id = s.id
                LEFT JOIN start_commission sc ON s.id = sc.start_id AND sc.user_role = 0
                LEFT JOIN company c ON c.id = s.company_id
                LEFT JOIN start_fte_rate sfr ON sfr.start_id = s.id
            WHERE
                sci.invoice_type_id = 6
                AND sfr.currency = 1
                AND ici.id IS NULL
            GROUP BY
                s.id, sci.client_name, sci.client_info_id, s.talent_name, s.talent_id
            """;

        List<Object[]> resultList = entityManager.createNativeQuery(query).getResultList();
        return objectToVO(resultList);
    }

    private List<StartInfoForInvoicingVO> objectToVO(List<Object[]> objects) {
        if (CollectionUtils.isEmpty(objects)) {
            return Collections.emptyList();
        }

        List<StartInfoForInvoicingVO> result = new ArrayList<>();

        for (Object[] obj : objects) {
            StartInfoForInvoicingVO vo = new StartInfoForInvoicingVO();

            vo.setStartId(obj[0] != null ? Long.valueOf(StringUtil.valueOf(obj[0])) : null);
            vo.setOnboardDate(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[1]))); // Instant 格式
            vo.setClientName(StringUtil.valueOf(obj[2]));
            vo.setClientId(obj[3] != null ? Long.valueOf(StringUtil.valueOf(obj[3])) : null);
            vo.setTalentName(StringUtil.valueOf(obj[4]));
            vo.setTalentId(obj[5] != null ? Long.valueOf(StringUtil.valueOf(obj[5])) : null);

            String amIdsStr = StringUtil.valueOf(obj[6]);
            String amNamesStr = StringUtil.valueOf(obj[7]);

            List<FullNameUserDTO> accountManagers = new ArrayList<>();
            if (StringUtils.isNotBlank(amIdsStr) && StringUtils.isNotBlank(amNamesStr)) {
                String[] ids = amIdsStr.split(",");
                String[] names = amNamesStr.split(",");
                int length = Math.min(ids.length, names.length);

                for (int i = 0; i < length; i++) {
                    String idStr = ids[i].trim();
                    String nameStr = names[i].trim();
                    if (StringUtils.isNotBlank(idStr) || StringUtils.isNotBlank(nameStr)) {
                        FullNameUserDTO dto = new FullNameUserDTO();
                        try {
                            dto.setId(Long.parseLong(idStr));
                        } catch (NumberFormatException e) {
                            dto.setId(null);
                        }
                        dto.setFullName(nameStr);
                        accountManagers.add(dto);
                    }
                }
            }
            vo.setAccountManagers(accountManagers);

            result.add(vo);
        }

        return result;
    }


    public List<InvoicingApplicationInfoForOverdueVO> getOverdueInvoicingApplications() {
        String query = """
            SELECT
                iai.id,
                iai.code_number AS code_number,
                iai.invoicing_amount AS amount_due,
                DATEDIFF(NOW(), iai.payment_due_date) AS overdue_day,
                c.full_business_name AS company_name,
                c.id AS company_id,
                GROUP_CONCAT(DISTINCT ici.talent_name SEPARATOR ',') AS talent_name,
                GROUP_CONCAT(DISTINCT ici.talent_id SEPARATOR ',') AS talent_id,
                iai.puser_id AS user_id
            FROM
                invoicing_application_info iai
                LEFT JOIN invoicing_candidate_info ici 
                    ON ici.invoice_application_id = iai.id AND iai.status = 1
                LEFT JOIN company c 
                    ON c.id = iai.company_id
            WHERE
                iai.invoicing_application_type = 1
                AND iai.invoicing_status = 6
            GROUP BY
                iai.id,
                iai.code_number,
                iai.invoicing_amount,
                iai.payment_due_date,
                c.full_business_name,
                c.id,
                iai.puser_id
            """;

//                    OR (
//                        iai.invoicing_status BETWEEN 4 AND 5
//                        AND iai.payment_due_date < NOW()
//                    )

        List<Object[]> objects = entityManager.createNativeQuery(query).getResultList();
        return objectToDTO2(objects);
    }

    private List<InvoicingApplicationInfoForOverdueVO> objectToDTO2(List<Object[]> objects) {
        if (CollectionUtils.isEmpty(objects)) {
            return Collections.emptyList();
        }

        List<InvoicingApplicationInfoForOverdueVO> result = new ArrayList<>();
        for (Object[] obj : objects) {
            InvoicingApplicationInfoForOverdueVO vo = new InvoicingApplicationInfoForOverdueVO();
            vo.setId(obj[0] != null ? Long.valueOf(String.valueOf(obj[0])) : null);
            vo.setCodeNumber(StringUtil.valueOf(obj[1]));
            vo.setAmountDue(obj[2] != null ? new BigDecimal(StringUtil.valueOf(obj[2])) : null);
            vo.setOverdueDay(obj[3] != null ? Integer.valueOf(StringUtil.valueOf(obj[3])) : null);
            vo.setCompanyName(StringUtil.valueOf(obj[4]));
            vo.setCompanyId(obj[5] != null ? Long.valueOf(StringUtil.valueOf(obj[5])) : null);
            vo.setTalentName(StringUtil.valueOf(obj[6]));
            vo.setTalentId(obj[7] != null ? StringUtil.valueOf(obj[7]) : null);
            vo.setUserId(obj[8] != null ? Long.valueOf(StringUtil.valueOf(obj[8])) : null);

            result.add(vo);
        }
        return result;
    }

}
