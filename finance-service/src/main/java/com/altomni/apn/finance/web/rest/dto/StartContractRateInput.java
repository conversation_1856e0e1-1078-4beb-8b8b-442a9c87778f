package com.altomni.apn.finance.web.rest.dto;

import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateVO;
import com.altomni.apn.finance.domain.start.StartClientInfo;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class StartContractRateInput {
    private Long id;

    private Long tenantId;

    private Long startId;

    private Long extendStartContractRateId;

    private LocalDate startDate;

    private LocalDate endDate;

    private Integer currency = CurrencyConstants.USD;

    private RateUnitType rateUnitType;

    private BigDecimal finalBillRate = BigDecimal.ZERO;

    private BigDecimal finalPayRate = BigDecimal.ZERO;

    private String taxBurdenRateCode;

    private String mspRateCode;

    private String immigrationCostCode;

    private BigDecimal extraCost = BigDecimal.ZERO;

    private BigDecimal estimatedWorkingHourPerWeek = BigDecimal.ZERO;

    private BigDecimal totalBillAmount = BigDecimal.ZERO;

    private String note;

    private TalentRecruitmentProcessIpgOfferLetterCostRateVO taxBurdenRate;

    private TalentRecruitmentProcessIpgOfferLetterCostRateVO mspRate;

    private TalentRecruitmentProcessIpgOfferLetterCostRateVO immigrationCost;

    private StartClientInfo startClientInfo;

    private String chargeNumber;

    private String tvcNumber;

    private Boolean corpToCorp;
}
