package com.altomni.apn.finance.service.rabbitmq.listener;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.audit.AuditUserHolder;
import com.altomni.apn.common.domain.transactionrecord.CommonMqConsumeFailedRecord;
import com.altomni.apn.common.enumeration.enums.MqTranRecordBusTypeEnums;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.repository.mqfailedrecord.FinanceMqTransactionFailedRecordRepository;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.start.StartService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class TalentOnboardMqListener {

    @Autowired
    FinanceMqTransactionFailedRecordRepository financeMqTransactionFailedRecordRepository;

    @Autowired
    CommonRedisService commonRedisService;


    @Autowired
    StartService startService;

    private TalentOnboardMqListener talentOnboardMqListener;

    @Value("${application.notification.lark.mq.webhookKey:}")
    private String LARK_WEBHOOK_KEY;

    @Value("${application.notification.lark.mq.webhookUrl:}")
    private String LARK_WEBHOOK_URL;

    @PostConstruct
    public void init() {
        talentOnboardMqListener = this;
        talentOnboardMqListener.financeMqTransactionFailedRecordRepository = this.financeMqTransactionFailedRecordRepository;
        talentOnboardMqListener.commonRedisService = this.commonRedisService;
        talentOnboardMqListener.startService = this.startService;
    }

    private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String ONBOARD_CONSUMER_PREFIX = "TALENT_ONBOARD_TX_";

    @RabbitListener(bindings = {@QueueBinding(
            value = @Queue(value = "${application.talent-onboard-tx.queue}", durable = "true"),
            exchange = @Exchange(value = "${application.talent-onboard-tx.exchange}", durable = "true", type = ExchangeTypes.DIRECT),
            key = "${application.talent-onboard-tx.routing-key}")}, containerFactory = "financeConsumerFactory")
    @RabbitHandler
    public void processHandler(Channel channel, Message message) throws IOException {
        log.info("talent onboard create start ,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        try {

            JSONObject param = JSON.parseObject(json);
            StartDTO startDTO = JSONObject.parseObject(param.toJSONString(), StartDTO.class);
            if (!checkExits(startDTO, param)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            if (param.containsKey(SecurityUtils.OPERATOR_UID)) {
                AuditUserHolder.set(param.getOrDefault(SecurityUtils.OPERATOR_UID, "").toString());
            }
            LoginUtil.simulateLoginWithClient();

            try {
                //do something
                talentOnboardMqListener.startService.save(startDTO);
                saveFailedRecord(startDTO.getTalentId(), param, 1);
                String key = ONBOARD_CONSUMER_PREFIX.concat(startDTO.getTalentId() + "-" + startDTO.getJobId());
                talentOnboardMqListener.commonRedisService.delete(key);
                log.info("talent onboard create start ,{},rabbit mq consume success", MqTranRecordBusTypeEnums.TALENT_ONBOARD.getDesc());
            } catch (Exception e) {
                log.error("[Talent onboard create start] exception: ", e);
                saveFailedRecord(startDTO.getTalentId(), param, 0);
                NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, String.format("finance-service-> 业务类型：%s ,消费数据失败，请人工处理。", MqTranRecordBusTypeEnums.TALENT_ONBOARD.getDesc()));
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("talent onboard create start {}", MqTranRecordBusTypeEnums.TALENT_ONBOARD.getDesc(), e);
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
        } finally {
            AuditUserHolder.clear();
        }
    }


    /**
     * 幂等校验
     *
     * @param startDTO
     * @param param
     * @return
     */
    private boolean checkExits(StartDTO startDTO, JSONObject param) {
        String key = ONBOARD_CONSUMER_PREFIX.concat(startDTO.getTalentId() + "-" + startDTO.getJobId());
        //redis verify exists
        String value = talentOnboardMqListener.commonRedisService.get(key);
        if (StringUtils.isNotBlank(value)) {
            log.error("talent onboard create start ,{}, redis search id exists,param:{}", MqTranRecordBusTypeEnums.UPDATE_TALENT_FINANCE.getDesc(), param);
            return false;
        }

        //数据库是否记录
//        CommonMqConsumeFailedRecord commonMqConsumeFailedRecord = talentOnboardMqListener.financeMqTransactionFailedRecordRepository.findByBusIdAndBusTypeAndReceiceStatus(BigInteger.valueOf(startDTO.getTalentId()), MqTranRecordBusTypeEnums.TALENT_ONBOARD.toDbValue(), 1);
//        if (null != commonMqConsumeFailedRecord) {
//            log.error("talent onboard create start ,{}, id exists,param:{}", MqTranRecordBusTypeEnums.UPDATE_TALENT_FINANCE.getDesc(), param);
//            return false;
//        }
        talentOnboardMqListener.commonRedisService.set(key, "1", 18000);
        return true;
    }

    /**
     * 保存消费记录
     *
     * @param talentId
     * @param param
     * @param status
     */
    private void saveFailedRecord(Long talentId, JSONObject param, Integer status) {
        CommonMqConsumeFailedRecord failedRecord = new CommonMqConsumeFailedRecord();
        failedRecord.setConsumeCount(1);
        failedRecord.setBusId(BigInteger.valueOf(talentId));
        failedRecord.setReceiveMessage(param.toJSONString());
        failedRecord.setBusType(MqTranRecordBusTypeEnums.TALENT_ONBOARD.toDbValue());
        failedRecord.setReceiceStatus(status);
        talentOnboardMqListener.financeMqTransactionFailedRecordRepository.save(failedRecord);
    }

}