package com.altomni.apn.finance.service.dto.invoicing;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InvoicingApplicationInfoForOverdueVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String codeNumber;
    private BigDecimal amountDue;
    private Integer overdueDay;
    private String companyName;
    private Long companyId;
    private String talentName;
    private String talentId;
    private Long userId;
}
