package com.altomni.apn.finance.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoiceInfoTypeConverter extends AbstractAttributeConverter<InvoiceInfoType, Integer> {
    public InvoiceInfoTypeConverter() {
        super(InvoiceInfoType::toDbValue, InvoiceInfoType::fromDbValue);
    }
}
