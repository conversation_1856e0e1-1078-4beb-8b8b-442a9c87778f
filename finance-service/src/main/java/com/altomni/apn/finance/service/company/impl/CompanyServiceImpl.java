package com.altomni.apn.finance.service.company.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.vo.company.CompanyClientInfoVO;
import com.altomni.apn.common.vo.company.CompanyClientInvoicingInfoVO;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.CompanyContact;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.finance.service.company.CompanyClient;
import com.altomni.apn.finance.service.company.CompanyService;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
public class CompanyServiceImpl implements CompanyService {
    @Resource
    private CompanyClient companyClient;

    @Override
    public Company getCompany(Long companyId) {
        ResponseEntity<CompanyDTO> response = companyClient.getCompany(companyId);
        Company company = null;
        if (response != null) {
            CompanyDTO companyDTO = response.getBody();
            company = new Company();
            //数据错误导致company返回为null的情况
            if (ObjectUtil.isNotNull(companyDTO)) {
                company.setFullBusinessName(companyDTO.getName());
                ServiceUtils.myCopyProperties(companyDTO, company);
            }
        }
        return company;
    }

    @Override
    public List<CompanyContact> getCompanyContacts(Long companyId) {
        ResponseEntity<List<CompanyContact>> response = companyClient.getCompanyContacts(companyId);
        List<CompanyContact> companyContacts = new ArrayList<>();
        if (response != null) {
            companyContacts = response.getBody();
        }
        return companyContacts;
    }

    @Override
    public List<Long> getAllAmIdsByCompanyId(Long companyId) {
        ResponseEntity<List<Long>> allAmByCompany = companyClient.getAllAmByCompany(companyId);
        List<Long> amIds = new ArrayList<>();
        if (Objects.nonNull(allAmByCompany) && allAmByCompany.getStatusCode() == HttpStatus.OK) {
            amIds.addAll(allAmByCompany.getBody());
        }
        return amIds;
    }

    @Override
    public CompanyClientInvoicingInfoVO getInvoicingClientInfoById(Long id) {
        ResponseEntity<CompanyClientInvoicingInfoVO> response = companyClient.getInvoicingClientInfoById(id);
        CompanyClientInvoicingInfoVO company = null;
        if (response != null) {
            CompanyClientInvoicingInfoVO companyDTO = response.getBody();
            company = new CompanyClientInvoicingInfoVO();
            ServiceUtils.myCopyProperties(companyDTO, company);
        }
        return company;
    }

    //海外发票信息
    @Override
    public List<CompanyClientInfoVO> getCompanyClientInfoList(Long companyId) {
        ResponseEntity<List<CompanyClientInfoVO>> response = companyClient.getCompanyClientInfoList(companyId);
        if (response != null) {
            List<CompanyClientInfoVO> infoVOList = response.getBody();
            return infoVOList;
        }
        return new ArrayList<>();
    }

    @Override
    public CompanyClientInfoVO getCompanyClientInfoById(Long id) {
        ResponseEntity<CompanyClientInfoVO> response = companyClient.getCompanyClientInfoById(id);
        if (response != null) {
            CompanyClientInfoVO infoVOList = response.getBody();
            return infoVOList;
        }
        return null;
    }

    //中国区发票信息
    @Override
    public List<CompanyClientInvoicingInfoVO> getCompanyInvoicingClientInfoList(Long companyId) {
        ResponseEntity<List<CompanyClientInvoicingInfoVO>> response = companyClient.getCompanyInvoicingClientInfoList(companyId);
        if (response != null) {
            List<CompanyClientInvoicingInfoVO> infoVOList = response.getBody();
            return infoVOList;
        }
        return new ArrayList<>();
    }
}
