package com.altomni.apn.finance.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The InvoicePaymentMethod enumeration.
 */
public enum InvoicePaymentMethod implements ConvertedEnum<Integer> {
    CASH(0),
    CHECK(5),
    CREDIT_CARD(10),
    DEBIT_CARD(15),
    WIRE_TRANSFER(20);

    private final Integer dbValue;

    InvoicePaymentMethod(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<InvoicePaymentMethod, Integer> resolver =
        new ReverseEnumResolver<>(InvoicePaymentMethod.class, InvoicePaymentMethod::toDbValue);

    public static InvoicePaymentMethod fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
