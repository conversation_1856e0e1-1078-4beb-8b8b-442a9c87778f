package com.altomni.apn.finance.web.rest.invoice;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.invoice.InvoiceTypeConfigService;
import com.altomni.apn.finance.service.vo.invoice.InvoiceDetailInfoVO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceTypeConfigVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class InvoiceTypeConfigResource {

    @Resource
    InvoiceTypeConfigService invoiceTypeConfigService;

    @GetMapping("/invoice/type/list")
    public ResponseEntity<List<InvoiceTypeConfigVO>> getInvoiceTypeList() {
        log.info("[invoice: User @{}] REST request to search type by tenantId , param = {}:", SecurityUtils.getUserId(),SecurityUtils.getTenantId());
        return ResponseEntity.ok(invoiceTypeConfigService.findAllByTenantId());
    }

    @GetMapping("/invoice/type/{typeId}/{companyId}/search")
    public ResponseEntity<List<InvoiceDetailInfoVO>> getInvoiceInfoByCompanyIdAndTypeConfigId(@PathVariable Long companyId,@PathVariable Long typeId) {
        log.info("[invoice: User @{}] REST request to search invoice info, param = {}:", SecurityUtils.getUserId(),SecurityUtils.getTenantId());
        return ResponseEntity.ok(invoiceTypeConfigService.findInvoiceInfoList(companyId,typeId));
    }

    @GetMapping("/invoice/type/{id}/{invoiceId}")
    public ResponseEntity<InvoiceDetailInfoVO> getInvoiceInfoByTypeId(@PathVariable("id") Long id,@PathVariable("invoiceId") Long invoiceId) {
        log.info("[invoice: User @{}] REST request to search invoice info by typeId , param = {}:", SecurityUtils.getUserId(),SecurityUtils.getTenantId());
        return ResponseEntity.ok(invoiceTypeConfigService.findInvoiceInfoByTypeId(id,invoiceId));
    }
}
