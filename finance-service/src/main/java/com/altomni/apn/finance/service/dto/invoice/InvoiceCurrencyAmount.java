package com.altomni.apn.finance.service.dto.invoice;

import com.altomni.apn.finance.constants.InvoiceConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InvoiceCurrencyAmount implements Serializable {

    private static final long serialVersionUID = 4615959613372764062L;

    private Integer currency;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalAmount = BigDecimal.ZERO;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalPaidAmount = BigDecimal.ZERO;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalOverdueAmount = BigDecimal.ZERO;

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalAmount));
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalPaidAmount() {
        return totalPaidAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalPaidAmount));
    }

    public void setTotalPaidAmount(BigDecimal totalPaidAmount) {
        this.totalPaidAmount = totalPaidAmount;
    }

    public BigDecimal getTotalOverdueAmount() {
        return totalOverdueAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalOverdueAmount));
    }

    public void setTotalOverdueAmount(BigDecimal totalOverdueAmount) {
        this.totalOverdueAmount = totalOverdueAmount;
    }

    @Override
    public String toString() {
        return "InvoiceCurrencyAmount{" +
                "currency=" + currency +
                ", totalAmount=" + totalAmount +
                ", totalPaidAmount=" + totalPaidAmount +
                ", totalOverdueAmount=" + totalOverdueAmount +
                '}';
    }
}
