package com.altomni.apn.finance.repository.start;

import com.altomni.apn.finance.domain.start.StartFteSalaryPackage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the StartFteSalaryPackage entity.
 */
@SuppressWarnings("unused")
@Repository
public interface StartFteSalaryPackageRepository extends JpaRepository<StartFteSalaryPackage, Long> {
    List<StartFteSalaryPackage> findAllByStartId(Long startId);

    List<StartFteSalaryPackage> findAllByStartIdIn(List<Long> startIds);
}
