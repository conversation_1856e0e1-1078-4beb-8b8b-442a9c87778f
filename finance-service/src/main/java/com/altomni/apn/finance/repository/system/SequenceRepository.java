package com.altomni.apn.finance.repository.system;

import com.altomni.apn.finance.domain.system.Sequence;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the Sequence entity.
 */
@SuppressWarnings("unused")
@Repository
public interface SequenceRepository extends JpaRepository<Sequence, Long> {

    Sequence findByName(String name);

    Sequence findByTenantIdAndName(Long tenantId, String name);
}
