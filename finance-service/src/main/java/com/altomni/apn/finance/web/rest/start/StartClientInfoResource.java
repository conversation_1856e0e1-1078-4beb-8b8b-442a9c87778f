package com.altomni.apn.finance.web.rest.start;

import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.finance.domain.start.StartClientInfo;
import com.altomni.apn.finance.service.start.StartClientInfoService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.start.StartService;
import com.altomni.apn.finance.service.vo.invoice.InvoiceDetailInfoVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.Optional;

/**
 * REST controller for managing StartClientInfo.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StartClientInfoResource {

    private static final String ENTITY_NAME = "StartClientInfo";

    @Resource
    private StartClientInfoService startClientInfoService;

    @Resource
    private StartService startService;


    @PutMapping("/start-client-infos/startId/{startId}")
    public ResponseEntity<InvoiceDetailInfoVO> updateStartClientInfo(@PathVariable Long startId, @RequestBody StartClientInfo startClientInfo) throws URISyntaxException {
        log.info("[APN: StartClientInfo @{}] REST request to update StartClientInfo : {}, {}", SecurityUtils.getUserId(), startId, startClientInfo);
        startService.checkPermissionByStartId(startId);
        startClientInfo.setStartId(startId);
        InvoiceDetailInfoVO result = startClientInfoService.update(startClientInfo);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @GetMapping("/start-client-infos/startId/{startId}")
    public ResponseEntity<InvoiceDetailInfoVO> getStartClientInfo(@PathVariable Long startId) {
        log.info("[APN: StartClientInfo @{}] REST request to get StartClientInfo : {}", SecurityUtils.getUserId(), startId);
        InvoiceDetailInfoVO infoVO = startClientInfoService.findInvoiceByStartId(startId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(infoVO));
    }

}
