package com.altomni.apn.finance.service.dto.invoice;

import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import lombok.Data;

import java.util.List;

@Data
public class InvoiceSearchDto {

    private String invoiceNo;

    private String subInvoiceNo;

    private String invoiceDate;

    private String dueDate;

    private InvoiceType[] invoiceType;

    private String talentName;

    private InvoiceStatus[] status;

    private String paymentDate;

    private String customerName;

    private String jobTitle;

    private Long teamId;

    private String fromDate;

    private String toDate;

    private String createdDate;

    private String am;

    private String coAm;

    private String recruiter;

    private String salesLeadOwner;

    private String bdOwner;

    private String ac;

    private String dm;

    private String sourcer;

    private String owner;

}
