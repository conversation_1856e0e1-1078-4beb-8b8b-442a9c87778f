package com.altomni.apn.finance.service.dto.invoicing;

import com.altomni.apn.common.domain.enumeration.company.*;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@Data
public class InvoicingFinancialApprovalDTO {

    private Long invoicingId;

    private InvoicingStatus invoicingStatus ;

    private String note ;

    /**
     * 批量审批
     */
    private List<Long> invoicingIdList ;
}
