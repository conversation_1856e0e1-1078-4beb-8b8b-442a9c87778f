package com.altomni.apn.finance.config;

import com.altomni.apn.common.domain.enumeration.application.ActivityStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.google.common.collect.ImmutableMap;

import java.math.BigDecimal;
import java.util.*;

/**
 * Application constants.
 */
public final class Constants {

    //Regex for acceptable logins
    public static final String USERNAME_REGEX = "^[_'.@A-Za-z0-9-]*$";
    //chinese characters format
    public static final String CHINESE_CHARACTERS = "[\u4e00-\u9fa5]";
    public static final String SYSTEM_ACCOUNT = "system";
    public static final String ANONYMOUS_USER = "anonymoususer";

    public static final String CUSTOM_AUTHENTICATION_KEY = "social-user-authentication";
    public static final String CUSTOM_AUTHENTICATION_PROVIDER_KEY = "provider";

    /**trial user credit limit**/
    public static final Integer PER_CREDIT = 1;


    public static final String DEFAULT_LOCATION_TABLE = "city_locations_en";

    public static final Map<String, String> LANG_LOCATION_TABLE_MAP = ImmutableMap.of(
        "en", "city_locations_en",
        "cn", "city_locations_cn"
    );

    public static final Long DEFAULT_ALL_TENANT = -1L;

    /**
     * for third party jobs tenant
     */
    public static final Long TENANT_ID_4 = 4L;

    public static final Set<Long> IPG_RULE_TENANT_IDS = Set.of(4L, 14L);

    public final static String SMART_TRACK_JOB_CREATED_BY = "20,4";

    /**
     * for LiePin demo jobs created by
     */
    public final static String MONGODB_JOB_CREATED_BY = "322,7";

    /**
     * for LiePin demo hiring manager
     */
    public final static Long MONGODB_JOB_HIRING_MANAGER_ID = 372L;

    /**
     * for LiePin demo tenant
     */
    public static final Long TENANT_ID_7 = 7L;

    /**
     * Add for Redis
     */
    public static final String RATER_SERVICE_NAME = "rater";

    public static final String RATER_TALENT_JOBS_PAIRS = "/rate/pairs";

    public static final String RATER_TALENT_JOBS_API = "/find/jobs/talent/";

    public static final Integer ACTIVE = 1;
    public static final Integer INACTIVE = 0;

    public static final String USER_CREDIT_LIMIT="USER_CREDIT_LIMIT";

    public static final String EMAILY_XXL_JOB_TOKEN = "EMAILY-XXL-JOB-TOKEN";

    /**
     * intellipro group domain name
     */
    public static final String INTELLIPRO_GROUP_COM = "@intelliprogroup.com";

    public static final String Baijiaxing = "Qian Gui Zong Bai Zhong She Qiguan Gongxi Lian Shou Bu Tian Qiang Dongfang " +
        "Wo Helian Bao Lan Nangong Murong Baili Li Yu Zhuo Niu Jiang Xian Kan Rao Zan Han Cha Ma Ouyang Qiu Liu Zu Pi " +
        "Nong Leng Xin He Qin Tan Xuanyuan Cao Tuoba Sikong Miao Cheng Jian Xie Cui Luo Hao Xuan Mao Ying Luqiu Hai " +
        "Ping Zhai Shi Zhuge Wen Chai Lai Tao Tantai Rong Si Zhangsun Chen Zhu Gongye Gu Rui Zeng Diao Wan Ximen Xiao " +
        "Yan Yong Lao Qu Duangan Jia Chang Deng Suo Gongyang Ban Gongliang Nian Jing Chunyu Zou Guo Lang An Huan Zi " +
        "Lu Hua Bian Sima Hong Zhan Xianyu Cen Yuan Shu Fei Pei Ou Gongsun Fu Lin Ji Taishu Wu Fan Ke Huo Cong Xing " +
        "Weng Chong Nie Yang Mu Sun Mo Dang Yuchi Zhang Lei Luan Moqi Yuezheng Di Bei Kong Yao Jiao Meng Long Bo Zaifu " +
        "Kui Nan Yun Sang Qi Min Liangqiu Bi Tu Ming Tang Liang Wei Pan Ba Mai Peng Ge Ling Ning Song Yuwen Jin Jiagu " +
        "Xi Dou Xue Shao Chanyu Diwu Du Gong Zhuang Zongzheng Fang Ni Shen Huai Ran Ru Xiangsi Dongmen Bie Gan Zhen " +
        "Shui Nai Sikou Xiang Duanmu Hu Guliang Qidiao Da Wuma Zheng Quan Liao Linghu Mei Zhao Zhou Hou E Kou Guang " +
        "Xiahou Wenren Shentu Teng Yue Pang Feng Zang Gou Su Wang Ding Ren Chi Situ Puyang Zai Mie Cai Kuai Zhi Chu " +
        "Huyan Cang Zuo Ai Che Man Hang Yi Zhongli Kuang Mi Tai Gao Dongguo Sheng Lou Ziche Qiao Shan Na Huangfu Weisheng " +
        "Ha Yin Shang Heng Ruan Xun Chao Gai Dai Xia Duan Bing Ju Xiong Yangshe Pu Que Sha Guan Zhuansun Geng Kang " +
        "Nanmen Shuai Huang Dong Ben You Shangguan Zhongsun Ye Zuoqiu Mou Tong Fa Ao Er Hui Xu Shuang " +
        "谬 风 于 磨 经 汗 酒 毓 姒 冼 出 翟 稽 金 盈 全 邵 奈 区 谌 卑 兴 首 湛 罕 原 韩 藩 鲜 赛 青 府 冒 揭 钟 靖 阎 房 仍 " +
        "邱 邰 姓 端 有 奉 仰 燕 皋 皮 龙 徭 茆 绍 家 蹉 粱 其 迟 帅 豆 休 夙 敏 叔 衷 虞 苍 藤 声 山 肥 穆 鄂 储 马 尹 莘 隋 " +
        "丹 慈 植 郯 阳 柳 浦 鲍 段 环 佛 周 希 祖 励 吉 胡 廖 归 粘 程 广 赧 桓 双 俎 解 厉 肇 亓 犹 倪 乔 桂 敬 蓝 向 尾 疏 " +
        "温 勇 乙 板 允 诺 逮 茹 壬 性 税 长 淡 旗 零 战 铎 烟 衡 支 建 佴 贡 符 仵 欎 檀 业 庄 督 裘 盘 释 锐 偶 柔 普 楚 巴 " +
        "令 郏 禹 来 佘 求 侍 严 敛 世 威 皇 卓 徐 户 堵 繁 庆 衅 功 卞 乜 林 斐 示 门 牵 裔 银 凤 云 卯 闫 余 缪 步 圭 靳 力 " +
        "典 嵇 蓟 闾 洋 宜 行 竹 似 诸 谏 臧 宿 蒙 益 母 禾 齐 米 戎 韦 节 覃 线 龚 撒 弥 查 糜 丛 兰 校 定 庚 寻 傅 和 辉 舄 " +
        "代 愚 喜 岑 泉 怀 蒿 舜 卜 王 盖 伟 须 祁 陈 惠 巧 年 祢 雍 针 潭 旅 德 杨 单 雪 东 季 悟 军 丘 但 苑 祝 纳 仁 康 蒋 " +
        "殴 悉 通 聊 初 召 俞 乘 申 平 兆 潮 铁 戢 姬 集 市 坚 卫 能 智 宇 奕 沐 革 汉 瑞 掌 习 阙 斋 褒 訾 赤 赵 天 永 仪 僧 " +
        "牢 宋 吴 芒 蒯 昌 生 冀 沈 娄 孟 凌 朋 汤 沃 圣 伏 闭 第 招 岳 以 帖 戈 骑 麦 钦 拱 凭 纵 紫 宣 夏 翦 歧 竺 良 巩 笪 " +
        "绪 殷 竭 麻 别 邗 连 安 清 妫 郁 空 漆 唱 毛 许 幸 斛 夕 镜 忻 史 董 席 蓬 理 闽 望 耿 虎 寒 瓮 伊 赖 告 塔 牛 机 种 " +
        "黄 仲 宫 綦 扈 巫 霍 恽 从 犁 陆 丑 达 丁 鄢 禚 旁 泷 楼 何 巢 续 管 城 尉 析 涂 修 始 辟 九 巨 印 邸 窦 宦 系 池 华 " +
        "拜 易 守 蹇 莫 登 六 彭 熊 学 甫 秋 钊 睢 封 源 杜 嘉 粟 阴 章 卢 常 邢 秘 泣 贯 戊 敖 庞 枚 公 暨 车 荤 芮 衣 柯 樊 " +
        "北 不 白 厚 张 酆 施 田 逢 袁 呼 应 祭 练 潜 籍 苟 钱 井 侯 腾 滑 恭 保 雷 务 谯 老 姚 谭 琴 柴 及 展 蒲 暴 嬴 慎 京 " +
        "慕 汝 真 赫 速 颜 彤 黎 仇 亢 顿 简 寸 利 塞 摩 富 朴 柏 阚 光 阮 碧 裴 商 孝 江 咎 璩 鱼 象 仆 郸 旷 士 苗 庾 晁 依 " +
        "泥 刁 萧 闻 昂 镇 邝 苏 刚 硕 奚 廉 愈 丙 势 宛 操 刀 罗 雀 伯 卷 抄 戴 桐 韶 进 麴 奇 况 蔡 夷 剑 计 高 尧 营 尤 范 " +
        "党 武 迮 郜 侨 赏 袭 容 桑 屈 勾 任 占 将 宗 邓 牟 红 盍 帛 焉 吕 弘 饶 戚 延 郦 度 字 莱 毕 冯 权 潘 漫 中 浮 刑 司 " +
        "越 辛 合 琦 羿 宁 贺 濯 钞 朱 郝 善 运 回 钮 明 後 过 扶 佼 栋 可 脱 晏 国 实 邬 化 隽 喻 崇 墨 劳 冷 睦 贾 伦 骆 在 " +
        "终 淦 丰 茂 左 匡 甄 贝 禽 鄞 鲁 昝 顾 弓 隐 汪 祈 胥 海 景 随 农 万 梅 栾 森 邴 甲 乐 折 澄 所 本 剧 谷 狂 貊 星 台 " +
        "野 桥 勤 称 杞 英 於 羽 矫 锁 孛 项 姜 郎 信 费 枝 检 晋 考 渠 春 逯 布 郭 菅 虢 谢 次 五 纪 葛 蛮 陀 堂 危 昔 藏 夫 " +
        "洛 寇 聂 千 受 索 让 独 干 止 牧 詹 夔 斯 闪 石 是 苌 冉 孔 充 邛 花 洪 礼 寿 古 前 咸 舒 浑 尔 波 荣 法 飞 成 艾 大 " +
        "义 泰 仉 李 载 畅 羊 留 翠 曹 遇 玉 官 方 隗 游 相 福 盛 友 僪 贵 伍 御 贰 玄 位 候 问 沙 边 元 开 庹 隆 褚 邶 缑 秦 " +
        "那 佟 抗 茅 邹 乌 翁 包 濮 文 箕 蚁 才 荀 仙 滕 己 扬 贸 无 束 瞿 闳 厍 类 叶 用 吾 蔚 陶 员 书 亥 謇 关 完 承 俟 委 " +
        "答 树 鞠 多 谈 钭 狄 诗 介 道 说 同 穰 魏 库 表 闵 乾 时 接 南 宓 养 眭 百 之 由 班 改 鹿 荆 律 戏 崔 松 霜 却 阿 强 " +
        "后 哀 弭 殳 郗 曾 绳 辜 念 驹 薛 错 宰 禄 捷 蒉 宝 童 函 素 萨 曲 频 尚 焦 贲 薄 路 融 爱 买 甘 逄 屠 苦 蔺 宏 资 都 " +
        "言 毋 么 杭 汲 居 仝 少 局 郑 孙 刘 满 哈 卿 栗 笃 唐 师 水 夹谷 段干 宇文 壤驷 尉迟 仲孙 轩辕 公羊 皇甫 宰父 南宫 闾丘 " +
        "东欧 司马 亓官 子车 赫连 聂晁 上官 微生 西门 梁丘 谷梁 羊舌 拓跋 公孙 呼延 第五 空曾 巫马 东门 太叔 闻人 宗政 公冶 濮阳 " +
        "澹台 公良 司寇 夏侯 慕容 公西 单于 颛孙 步都 端木 司徒 荔菲 百里 南门 诸葛 左丘 万俟 申屠 钟离 淳于 东方 相查 鲜于 长孙 " +
        "辗迟 乐正 漆雕 凃肖 欧阳 令狐 东郭 锺离 司空 上 事 亚 亞 亨 亮 仔 件 佑 佩 佬 佳 來 俊 俭 倫 偈 傑 儀 儉 克 冰 列 劉 劍 " +
        "劲 勁 勉 勝 勞 匯 區 协 協 博 发 只 叻 君 听 启 呂 呔 咏 咪 哂 哥 啊 啓 單 嚴 國 土 地 坐 坛 坤 埔 域 執 培 基 堅 墓 " +
        "墟 壇 壹 壽 复 夜 夢 头 夺 契 奪 好 妙 妹 姐 姑 娇 娟 娣 娴 婆 婵 婷 媚 嫂 嫦 嫻 嬋 子 孫 客 實 对 對 小 就 屋 屯 岛 " +
        "峰 島 差 帝 幻 底 庵 廣 弟 張 強 当 彬 影 必 忠 快 恒 愛 慶 懷 扁 打 执 投 护 拓 拔 拖 换 排 探 提 換 援 播 斜 斩 旋 " +
        "日 旧 旭 显 時 晓 晶 曉 最 月 朗 杏 条 杰 東 构 果 柜 标 栈 根 格 梁 條 梦 棋 棧 楊 榄 榮 構 樂 標 横 樹 權 欖 欢 欧 " +
        "歐 歡 比 汇 沟 泵 泽 洁 洒 洞 活 派 济 浩 涛 润 涧 淑 深 添 湯 湾 溝 溫 滔 滿 潔 潤 澤 澳 濟 濤 灑 灣 炳 炸 爵 爷 爺 " +
        "爽 獲 玲 珊 珍 球 琳 琼 環 瓊 瓜 甜 电 甸 畢 當 發 盤 直 省 短 砵 确 碟 確 祥 禤 禧 秀 积 積 穎 突 窝 窩 立 笑 節 簡 " +
        "紀 約 紅 純 統 維 綺 綿 線 練 约 纯 统 绮 维 绵 缺 羅 美 群 義 翅 習 耀 耕 联 聪 聯 聶 聽 肚 胖 胜 腥 興 舊 舍 色 艳 " +
        "芝 芬 芳 莉 莊 莲 获 莹 菊 華 萬 葉 葵 蓋 蓮 蔣 藍 蘇 虔 虾 蛇 蛋 蝦 蝶 裕 複 西 見 觀 见 观 角 計 記 許 詠 試 話 謙 " +
        "謝 譚 護 记 试 话 谦 豪 豹 貝 貴 費 賀 賈 賢 質 賴 贤 质 超 趙 踏 車 軒 輝 轩 辣 辨 远 违 過 達 違 遠 遮 邊 邦 鄒 鄔 " +
        "鄧 鄭 鄺 醉 鉗 銘 銳 鋼 錢 錦 錬 鍾 鏡 鐵 鐸 鑑 钢 钳 铭 锦 锺 閔 關 除 陳 陸 雨 雲 電 震 霞 霭 霸 靄 靓 静 靚 靜 非 " +
        "鞏 韓 音 項 順 須 頭 顧 顯 顺 颖 香 馬 馮 駱 騰 鬆 鬼 魯 鮑 鳳 鴨 鸭 麥 黃 齊 龍 龐 司馬 歐陽 諸葛";

    public final static String[] BAIJIAXING_ARRAY = {"qian","gui","zong","bai","zhong","she","qiguan","gongxi","lian","shou","bu","tian","qiang","dongfang","wo","helian","bao","lan","nangong","murong","baili","li","yu","zhuo","niu","jiang","xian","kan","rao","zan","han","cha","ma","ouyang","qiu","liu","zu","pi","nong","leng","xin","he","qin","tan","xuanyuan","cao","tuoba","sikong","miao","cheng","jian","xie","cui","luo","hao","xuan","mao","ying","luqiu","hai","ping","zhai","shi","zhuge","wen","chai","lai","tao","tantai","rong","si","zhangsun","chen","zhu","gongye","gu","rui","zeng","diao","wan","ximen","xiao","yan","yong","lao","qu","duangan","jia","chang","deng","suo","gongyang","ban","gongliang","nian","jing","chunyu","zou","guo","lang","an","huan","zi","lu","hua","bian","sima","hong","zhan","xianyu","cen","yuan","shu","fei","pei","ou","gongsun","fu","lin","ji","taishu","wu","fan","ke","huo","cong","xing","weng","chong","nie","yang","mu","sun","mo","dang","yuchi","zhang","lei","luan","moqi","yuezheng","di","bei","kong","yao","jiao","meng","long","bo","zaifu","kui","nan","yun","sang","qi","min","liangqiu","bi","tu","ming","tang","liang","wei","pan","ba","mai","peng","ge","ling","ning","song","yuwen","jin","jiagu","xi","dou","xue","shao","chanyu","diwu","du","gong","zhuang","zongzheng","fang","ni","shen","huai","ran","ru","xiangsi","dongmen","bie","gan","zhen","shui","nai","sikou","xiang","duanmu","hu","guliang","qidiao","da","wuma","zheng","quan","liao","linghu","mei","zhao","zhou","hou","e","kou","guang","xiahou","wenren","shentu","teng","yue","pang","feng","zang","gou","su","wang","ding","ren","chi","situ","puyang","zai","mie","cai","kuai","zhi","chu","huyan","cang","zuo","ai","che","man","hang","yi","zhongli","kuang","mi","tai","gao","dongguo","sheng","lou","ziche","qiao","shan","na","huangfu","weisheng","ha","yin","shang","heng","ruan","xun","chao","gai","dai","xia","duan","bing","ju","xiong","yangshe","pu","que","sha","guan","zhuansun","geng","kang","nanmen","shuai","huang","dong","ben","you","shangguan","zhongsun","ye","zuoqiu","mou","tong","fa","ao","er","hui","xu","shuang","谬","风","于","磨","经","汗","酒","毓","姒","冼","出","翟","稽","金","盈","全","邵","奈","区","谌","卑","兴","首","湛","罕","原","韩","藩","鲜","赛","青","府","冒","揭","钟","靖","阎","房","仍","邱","邰","姓","端","有","奉","仰","燕","皋","皮","龙","徭","茆","绍","家","蹉","粱","其","迟","帅","豆","休","夙","敏","叔","衷","虞","苍","藤","声","山","肥","穆","鄂","储","马","尹","莘","隋","丹","慈","植","郯","阳","柳","浦","鲍","段","环","佛","周","希","祖","励","吉","胡","廖","归","粘","程","广","赧","桓","双","俎","解","厉","肇","亓","犹","倪","乔","桂","敬","蓝","向","尾","疏","温","勇","乙","板","允","诺","逮","茹","壬","性","税","长","淡","旗","零","战","铎","烟","衡","支","建","佴","贡","符","仵","欎","檀","业","庄","督","裘","盘","释","锐","偶","柔","普","楚","巴","令","郏","禹","来","佘","求","侍","严","敛","世","威","皇","卓","徐","户","堵","繁","庆","衅","功","卞","乜","林","斐","示","门","牵","裔","银","凤","云","卯","闫","余","缪","步","圭","靳","力","典","嵇","蓟","闾","洋","宜","行","竹","似","诸","谏","臧","宿","蒙","益","母","禾","齐","米","戎","韦","节","覃","线","龚","撒","弥","查","糜","丛","兰","校","定","庚","寻","傅","和","辉","舄","代","愚","喜","岑","泉","怀","蒿","舜","卜","王","盖","伟","须","祁","陈","惠","巧","年","祢","雍","针","潭","旅","德","杨","单","雪","东","季","悟","军","丘","但","苑","祝","纳","仁","康","蒋","殴","悉","通","聊","初","召","俞","乘","申","平","兆","潮","铁","戢","姬","集","市","坚","卫","能","智","宇","奕","沐","革","汉","瑞","掌","习","阙","斋","褒","訾","赤","赵","天","永","仪","僧","牢","宋","吴","芒","蒯","昌","生","冀","沈","娄","孟","凌","朋","汤","沃","圣","伏","闭","第","招","岳","以","帖","戈","骑","麦","钦","拱","凭","纵","紫","宣","夏","翦","歧","竺","良","巩","笪","绪","殷","竭","麻","别","邗","连","安","清","妫","郁","空","漆","唱","毛","许","幸","斛","夕","镜","忻","史","董","席","蓬","理","闽","望","耿","虎","寒","瓮","伊","赖","告","塔","牛","机","种","黄","仲","宫","綦","扈","巫","霍","恽","从","犁","陆","丑","达","丁","鄢","禚","旁","泷","楼","何","巢","续","管","城","尉","析","涂","修","始","辟","九","巨","印","邸","窦","宦","系","池","华","拜","易","守","蹇","莫","登","六","彭","熊","学","甫","秋","钊","睢","封","源","杜","嘉","粟","阴","章","卢","常","邢","秘","泣","贯","戊","敖","庞","枚","公","暨","车","荤","芮","衣","柯","樊","北","不","白","厚","张","酆","施","田","逢","袁","呼","应","祭","练","潜","籍","苟","钱","井","侯","腾","滑","恭","保","雷","务","谯","老","姚","谭","琴","柴","及","展","蒲","暴","嬴","慎","京","慕","汝","真","赫","速","颜","彤","黎","仇","亢","顿","简","寸","利","塞","摩","富","朴","柏","阚","光","阮","碧","裴","商","孝","江","咎","璩","鱼","象","仆","郸","旷","士","苗","庾","晁","依","泥","刁","萧","闻","昂","镇","邝","苏","刚","硕","奚","廉","愈","丙","势","宛","操","刀","罗","雀","伯","卷","抄","戴","桐","韶","进","麴","奇","况","蔡","夷","剑","计","高","尧","营","尤","范","党","武","迮","郜","侨","赏","袭","容","桑","屈","勾","任","占","将","宗","邓","牟","红","盍","帛","焉","吕","弘","饶","戚","延","郦","度","字","莱","毕","冯","权","潘","漫","中","浮","刑","司","越","辛","合","琦","羿","宁","贺","濯","钞","朱","郝","善","运","回","钮","明","後","过","扶","佼","栋","可","脱","晏","国","实","邬","化","隽","喻","崇","墨","劳","冷","睦","贾","伦","骆","在","终","淦","丰","茂","左","匡","甄","贝","禽","鄞","鲁","昝","顾","弓","隐","汪","祈","胥","海","景","随","农","万","梅","栾","森","邴","甲","乐","折","澄","所","本","剧","谷","狂","貊","星","台","野","桥","勤","称","杞","英","於","羽","矫","锁","孛","项","姜","郎","信","费","枝","检","晋","考","渠","春","逯","布","郭","菅","虢","谢","次","五","纪","葛","蛮","陀","堂","危","昔","藏","夫","洛","寇","聂","千","受","索","让","独","干","止","牧","詹","夔","斯","闪","石","是","苌","冉","孔","充","邛","花","洪","礼","寿","古","前","咸","舒","浑","尔","波","荣","法","飞","成","艾","大","义","泰","仉","李","载","畅","羊","留","翠","曹","遇","玉","官","方","隗","游","相","福","盛","友","僪","贵","伍","御","贰","玄","位","候","问","沙","边","元","开","庹","隆","褚","邶","缑","秦","那","佟","抗","茅","邹","乌","翁","包","濮","文","箕","蚁","才","荀","仙","滕","己","扬","贸","无","束","瞿","闳","厍","类","叶","用","吾","蔚","陶","员","书","亥","謇","关","完","承","俟","委","答","树","鞠","多","谈","钭","狄","诗","介","道","说","同","穰","魏","库","表","闵","乾","时","接","南","宓","养","眭","百","之","由","班","改","鹿","荆","律","戏","崔","松","霜","却","阿","强","后","哀","弭","殳","郗","曾","绳","辜","念","驹","薛","错","宰","禄","捷","蒉","宝","童","函","素","萨","曲","频","尚","焦","贲","薄","路","融","爱","买","甘","逄","屠","苦","蔺","宏","资","都","言","毋","么","杭","汲","居","仝","少","局","郑","孙","刘","满","哈","卿","栗","笃","唐","师","水","夹谷","段干","宇文","壤驷","尉迟","仲孙","轩辕","公羊","皇甫","宰父","南宫","闾丘","东欧","司马","亓官","子车","赫连","聂晁","上官","微生","西门","梁丘","谷梁","羊舌","拓跋","公孙","呼延","第五","空曾","巫马","东门","太叔","闻人","宗政","公冶","濮阳","澹台","公良","司寇","夏侯","慕容","公西","单于","颛孙","步都","端木","司徒","荔菲","百里","南门","诸葛","左丘","万俟","申屠","钟离","淳于","东方","相查","鲜于","长孙","辗迟","乐正","漆雕","凃肖","欧阳","令狐","东郭","锺离","司空","上","事","亚","亞","亨","亮","仔","件","佑","佩","佬","佳","來","俊","俭","倫","偈","傑","儀","儉","克","冰","列","劉","劍","劲","勁","勉","勝","勞","匯","區","协","協","博","发","只","叻","君","听","启","呂","呔","咏","咪","哂","哥","啊","啓","單","嚴","國","土","地","坐","坛","坤","埔","域","執","培","基","堅","墓","墟","壇","壹","壽","复","夜","夢","头","夺","契","奪","好","妙","妹","姐","姑","娇","娟","娣","娴","婆","婵","婷","媚","嫂","嫦","嫻","嬋","子","孫","客","實","对","對","小","就","屋","屯","岛","峰","島","差","帝","幻","底","庵","廣","弟","張","強","当","彬","影","必","忠","快","恒","愛","慶","懷","扁","打","执","投","护","拓","拔","拖","换","排","探","提","換","援","播","斜","斩","旋","日","旧","旭","显","時","晓","晶","曉","最","月","朗","杏","条","杰","東","构","果","柜","标","栈","根","格","梁","條","梦","棋","棧","楊","榄","榮","構","樂","標","横","樹","權","欖","欢","欧","歐","歡","比","汇","沟","泵","泽","洁","洒","洞","活","派","济","浩","涛","润","涧","淑","深","添","湯","湾","溝","溫","滔","滿","潔","潤","澤","澳","濟","濤","灑","灣","炳","炸","爵","爷","爺","爽","獲","玲","珊","珍","球","琳","琼","環","瓊","瓜","甜","电","甸","畢","當","發","盤","直","省","短","砵","确","碟","確","祥","禤","禧","秀","积","積","穎","突","窝","窩","立","笑","節","簡","紀","約","紅","純","統","維","綺","綿","線","練","约","纯","统","绮","维","绵","缺","羅","美","群","義","翅","習","耀","耕","联","聪","聯","聶","聽","肚","胖","胜","腥","興","舊","舍","色","艳","芝","芬","芳","莉","莊","莲","获","莹","菊","華","萬","葉","葵","蓋","蓮","蔣","藍","蘇","虔","虾","蛇","蛋","蝦","蝶","裕","複","西","見","觀","见","观","角","計","記","許","詠","試","話","謙","謝","譚","護","记","试","话","谦","豪","豹","貝","貴","費","賀","賈","賢","質","賴","贤","质","超","趙","踏","車","軒","輝","轩","辣","辨","远","违","過","達","違","遠","遮","邊","邦","鄒","鄔","鄧","鄭","鄺","醉","鉗","銘","銳","鋼","錢","錦","錬","鍾","鏡","鐵","鐸","鑑","钢","钳","铭","锦","锺","閔","關","除","陳","陸","雨","雲","電","震","霞","霭","霸","靄","靓","静","靚","靜","非","鞏","韓","音","項","順","須","頭","顧","顯","顺","颖","香","馬","馮","駱","騰","鬆","鬼","魯","鮑","鳳","鴨","鸭","麥","黃","齊","龍","龐","司馬","歐陽","諸葛"};

    public final static Set<String> BAIJIAXING_DICT = new HashSet<>(Arrays.asList(BAIJIAXING_ARRAY));

    public final static BigDecimal TALENT_OWNERSHIP_PERCENTAGE = new BigDecimal(10);

    public final static BigDecimal ACCRUED_PERCENTAGE_NINETY = new BigDecimal(90);

    public final static BigDecimal ACCRUED_PERCENTAGE_HUNDRED = new BigDecimal(100);

    public final static List<JobType> CONTRACT_AND_PAYROLL_JOB = Arrays.asList(JobType.CONTRACT, JobType.PAY_ROLL, JobType.MSP);

    public static final String VALIDATE_CONTRACT_AMOUNT_SWITCH = "VALIDATE_CONTRACT_AMOUNT_SWITCH";

    public final static List<ActivityStatus> VALIDATE_COMMISSION_PERCENTAGE_STATUS = new ArrayList<>(Arrays.asList(ActivityStatus.Interview, ActivityStatus.Offered, ActivityStatus.Offer_Accepted));

    public final static String DEFAULT_ZERO_CODE = "ZERO";

    public final static String DEFAULT_ZERO_DESC = "0";

    public final static List<ActivityStatus> COUNT_JOB_OPENINGS_STATUS = new ArrayList<>(Arrays.asList(ActivityStatus.Offer_Accepted,
        ActivityStatus.Started, ActivityStatus.START_EXTENSION, ActivityStatus.START_FAIL_WARRANTY,
        ActivityStatus.START_TERMINATED, ActivityStatus.FAIL_TO_ONBOARD));

    public final static List<ActivityStatus> DASHBOARD_START_STATUS = new ArrayList<>(Arrays.asList(ActivityStatus.Started,
        ActivityStatus.START_EXTENSION, ActivityStatus.START_FAIL_WARRANTY, ActivityStatus.START_TERMINATED));

    private Constants() { }
}
