package com.altomni.apn.finance.repository.invoice;

import lombok.extern.slf4j.Slf4j;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class InvoiceNativeRepository {

    @Resource
    private EntityManager entityManager;

    /**
     * 查询公司地址和名称
     *
     * @param startId
     * @return
     */
    @Transactional(readOnly = true)
    public Map<String, Object> findCompanyAddressAndNameByStartId(BigInteger startId) {
        StringBuilder dataSql = new StringBuilder();
        dataSql.append("""
                select t.client_name,t.client_address,t.client_location,
                case when tc.label ='china fte' then ccii.client_name else cici.client_name end as relation_client_name,
                case when tc.label ='china fte' then ccii.invoicing_address else cici.client_address end as relation_client_address,
                case when tc.label ='china fte' then '' else cici.client_location end as relation_client_location
                from start_client_info t
                left join invoice_type_config tc on tc.id = t.invoice_type_id
                left join company_client_invoicing_info ccii on ccii.id = t.client_info_id
                left join company_invoice_client_info cici  on cici.id = t.client_info_id
                where start_id=:startId
                """);
        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("startId", startId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> mapList = dataQuery.getResultList();
        if (mapList.isEmpty()) {
            return null;
        }
        return mapList.get(0);
    }
}
