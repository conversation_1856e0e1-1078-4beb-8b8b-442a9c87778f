package com.altomni.apn.finance.service.invoicing;

import cn.hutool.json.JSONArray;
import com.altomni.apn.common.domain.enumeration.company.InvoicingBusinessType;
import com.altomni.apn.finance.service.dto.invoicing.*;
import com.altomni.apn.finance.service.vo.invoicing.InvoicingApplicationInfoVO;
import com.altomni.apn.finance.service.vo.invoicing.InvoicingApplicationInfoViewVO;
import com.altomni.apn.finance.service.vo.invoicing.InvoicingApplicationPrepaymentVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;

public interface InvoicingApplicationInfoService {

    Page<InvoicingApplicationInfoVO> searchAll(InvoicingApplicationInfoSearchDTO dto, Pageable pageable);

    void download(HttpServletResponse response,InvoicingApplicationInfoSearchDTO dto);

    InvoicingApplicationPrepaymentVO findPrepaymentByCompanyId(Long companyId, InvoicingBusinessType invoicingBusinessType);

    InvoicingApplicationInfoViewVO save(InvoicingApplicationInfoDTO dto);

    InvoicingApplicationInfoViewVO modify(InvoicingApplicationInfoDTO dto);

    void voidInvoicing(Long id);

    void financialApproval(InvoicingFinancialApprovalDTO dto);

    void financialApprovalList(InvoicingFinancialApprovalDTO dto);

    void financialInvoicingRecord(InvoicingFinancialRecordDTO dto);

    void financialInvoicingVoidRecord(InvoicingFinancialRecordDTO dto);

    void financialRecordPayment(InvoicingFinancialRecordPaymentDTO dto);

    void modifyFinancialRecordPayment(InvoicingFinancialRecordPaymentDTO dto);

    void financialBulkRecordPayment(InvoicingFinancialRecordPaymentDTO dto);

    void financialUnrecordPayment(InvoicingFinancialRecordPaymentDTO dto);

    void outstandingClose(InvoicingOutstandingCloseDTO dto);

    InvoicingApplicationInfoViewVO view(Long id);

    void updateInvoicingStatus();

    void initTalentPayment();

    void initCandidateTax();

    void completionPaymentJob();

    JSONArray deleteByStarts(Collection<Long> startIds);

    void sendUninvoicedLarkNotification();

    void sendOverdueInvoiceLarkNotification();

}
