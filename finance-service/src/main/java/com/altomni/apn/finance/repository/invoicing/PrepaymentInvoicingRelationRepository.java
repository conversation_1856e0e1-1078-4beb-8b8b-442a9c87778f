package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.common.domain.enumeration.company.InvoicingRelationType;
import com.altomni.apn.finance.domain.invoicing.PrepaymentInvoicingRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface PrepaymentInvoicingRelationRepository extends JpaRepository<PrepaymentInvoicingRelation,Long> {

    List<PrepaymentInvoicingRelation> findByInvoicingIdAndRelationTypeAndStatus(Long invoicingId, InvoicingRelationType relationType,Integer status);

    List<PrepaymentInvoicingRelation> findByPrepaymentInvoicingIdAndRelationTypeAndStatus(Long prepaymentInvoicingId, InvoicingRelationType relationType,Integer status);

    List<PrepaymentInvoicingRelation> findByPaymentIdAndRelationTypeAndStatus(Long paymentId, InvoicingRelationType relationType,Integer status);

    List<PrepaymentInvoicingRelation> findByPaymentIdInAndRelationTypeAndStatus(List<Long> paymentId, InvoicingRelationType relationType,Integer status);


    @Modifying
    @Transactional
    @Query(value = "update prepayment_invoicing_relation set status=0 where invoicing_id = ?1 and payment_id in(?2) and relation_type=1", nativeQuery = true)
    void updateStatusByInvoicingIdAndPaymentId(Long invoicingId, List<Long> paymentIdList);

    @Modifying
    @Transactional
    @Query(value = "update prepayment_invoicing_relation set status=0 where id in(?1)", nativeQuery = true)
    void updateStatusById(List<Long> id);
}
