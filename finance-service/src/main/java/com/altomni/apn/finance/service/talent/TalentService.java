package com.altomni.apn.finance.service.talent;

import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;

public interface TalentService {

    void save(TalentV3 talent);

    TalentV3 getTalent(Long talentId);

    TalentDTOV3 getTalentWithoutEntity(Long id);

    void syncTalentToES(Long talentId);

    void updateTalentExperience(Long talentId, TalentExperienceDTO talentExperienceDTO);
}