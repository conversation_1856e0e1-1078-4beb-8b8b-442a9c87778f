package com.altomni.apn.finance.service.dto.invoice;

import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatusConverter;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceTypeConverter;
import lombok.Data;

import javax.persistence.Convert;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Data
public class InvoiceSearchByApplicationDTO {

    private String invoiceNo;

    private String subInvoiceNo;

    @Convert(converter = InvoiceTypeConverter.class)
    private InvoiceType invoiceType;

    private Long talentId;

    private String fullName;

    private LocalDate invoiceStartTime;

    private LocalDate invoiceEndTime;

    private Instant createdStartTime;

    private Instant createdEndTime;

    @Convert(converter = InvoiceStatusConverter.class)
    private List<InvoiceStatus> invoiceStatusList;

    private Long companyId;

    private String companyName;

    private String jobTitle;

    private Long jobId;

    private Long teamId;

    private String teamName;

    private Integer size;

    private Integer page;

    private SearchSortDTO sort;

    private Long userId;

    private Long primaryTeamId;

}
