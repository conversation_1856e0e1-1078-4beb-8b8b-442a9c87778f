package com.altomni.apn.finance.service.user;

import com.altomni.apn.common.domain.user.User;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(value = "user-service")
public interface UserClient {

    @GetMapping("/user/api/v3/users/{id}")
    ResponseEntity<User> findById(@PathVariable("id") Long id);

    @PostMapping("/user/api/v3/users/get-all-activated-user-by-ids")
    ResponseEntity<List<User>> findALLByIdInAndActivated(@RequestBody List<Long> ids);

    @GetMapping("/user/api/v3/permissions/teams/level1-team/user/{userId}")
    ResponseEntity<Long> findLevel1TeamIdByUserId(@PathVariable("userId") Long userId);

    @GetMapping("/user/api/v3/permissions/teams/level1-team-profit-leader/user/{userId}")
    ResponseEntity<Long> findProfitTeamLeaderIdByUserId(@PathVariable("userId") Long userId);



}
