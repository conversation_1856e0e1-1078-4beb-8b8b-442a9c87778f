package com.altomni.apn.finance.domain.start;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.enumeration.start.StartFailedWarrantyActionPlan;
import com.altomni.apn.finance.domain.enumeration.start.StartFailedWarrantyActionPlanConverter;
import com.altomni.apn.finance.domain.enumeration.start.StartFailedWarrantyReason;
import com.altomni.apn.finance.domain.enumeration.start.StartFailedWarrantyReasonConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "start_failed_warranty")
public class StartFailedWarranty extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4069055487815600535L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "start_id")
    private Long startId;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Convert(converter = StartFailedWarrantyReasonConverter.class)
    @Column(name = "reason")
    private StartFailedWarrantyReason reason;

    @Convert(converter = StartFailedWarrantyActionPlanConverter.class)
    @Column(name = "action_plan")
    private StartFailedWarrantyActionPlan actionPlan;

    @ApiModelProperty(value = "GP")
    @Column(name = "total_bill_amount", precision=20, scale=2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillAmount;

    @Column(name = "note")
    private String note;

    @Column(name = "mark_candidate_available")
    private Boolean markCandidateAvailable;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("startId"));

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStartId() {
        return startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public StartFailedWarrantyReason getReason() {
        return reason;
    }

    public void setReason(StartFailedWarrantyReason reason) {
        this.reason = reason;
    }

    public StartFailedWarrantyActionPlan getActionPlan() {
        return actionPlan;
    }

    public void setActionPlan(StartFailedWarrantyActionPlan actionPlan) {
        this.actionPlan = actionPlan;
    }

    public BigDecimal getTotalBillAmount() {
        return totalBillAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalBillAmount));
    }

    public void setTotalBillAmount(BigDecimal totalBillAmount) {
        this.totalBillAmount = totalBillAmount;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Boolean getMarkCandidateAvailable() {
        return markCandidateAvailable;
    }

    public void setMarkCandidateAvailable(Boolean markCandidateAvailable) {
        this.markCandidateAvailable = markCandidateAvailable;
    }

    @Override
    public String toString() {
        return "StartFailedWarranty{" +
                "id=" + id +
                ", startId=" + startId +
                ", endDate=" + endDate +
                ", reason=" + reason +
                ", actionPlan=" + actionPlan +
                ", totalBillAmount=" + totalBillAmount +
                ", note='" + note + '\'' +
                ", markCandidateAvailable=" + markCandidateAvailable +
                '}';
    }
}
