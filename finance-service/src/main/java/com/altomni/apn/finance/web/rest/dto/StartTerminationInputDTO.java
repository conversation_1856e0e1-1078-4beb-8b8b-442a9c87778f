package com.altomni.apn.finance.web.rest.dto;

import com.altomni.apn.common.domain.enumeration.StartTerminationReason;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.enumeration.start.StartTerminationConvertToFteFeeStatus;
import lombok.Data;

import java.time.LocalDate;

@Data
public class StartTerminationInputDTO {
    private Long id;

    private Long startId;

    private StartStatus startStatus;

    private LocalDate startEndDate;

    private LocalDate terminationDate;

    private StartTerminationReason reason;

    private StartTerminationConvertToFteFeeStatus convertToFteFeeStatus;

    private String note;
    private String reasonComments;

    private Boolean markCandidateAvailable;
}
