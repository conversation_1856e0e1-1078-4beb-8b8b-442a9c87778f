package com.altomni.apn.finance.service.dto.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A InvoiceClientCreditDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InvoiceClientCreditDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5648121720931519323L;

    private Long id;

    private Long companyId;

    private Long invoiceId;

    private String invoiceNo;

    private BigDecimal amount;

    private String note;

}
