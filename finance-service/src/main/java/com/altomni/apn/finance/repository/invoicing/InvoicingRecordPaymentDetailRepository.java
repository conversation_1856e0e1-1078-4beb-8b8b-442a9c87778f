package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.finance.domain.invoicing.InvoicingRecordPaymentDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface InvoicingRecordPaymentDetailRepository extends JpaRepository<InvoicingRecordPaymentDetail,Long> {

    @Modifying
    @Transactional
    @Query(value = "update invoicing_record_payment_detail set status=0 where payment_id = ?1", nativeQuery = true)
    void updateStatusByPaymentId(Long paymentId);


    List<InvoicingRecordPaymentDetail> findByPaymentIdAndStatus(Long paymentId,Integer status);

    List<InvoicingRecordPaymentDetail> findByPaymentIdInAndStatus(List<Long> paymentIds,Integer status);
}
