package com.altomni.apn.finance.service.invoicing.impl;

import com.altomni.apn.finance.repository.invoicing.InvoicingNativeRepository;
import com.altomni.apn.finance.service.invoicing.InvoicingCandidateInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

@Service
@Slf4j
public class InvoicingCandidateInfoServiceImpl implements InvoicingCandidateInfoService {

    @Resource
    InvoicingNativeRepository invoicingNativeRepository;


    @Override
    public BigDecimal getAmountReceived(Long startId, Long talentId, Long jobId) {
        return invoicingNativeRepository.selectReceivedAmount(startId, talentId, jobId);
    }
}
