package com.altomni.apn.finance.domain.invoicing;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.Instant;

/**
 * 回款候选人明细
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "invoicing_record_payment_detail")
public class InvoicingRecordPaymentDetail extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 支付id */
    @Column(name = "payment_id")
    private Long paymentId ;

    /**  */
    @Column(name = "talent_id")
    private Long talentId ;

    /**  */
    @Column(name = "talent_name")
    private String talentName ;

    @Column(name = "gp_amount")
    private BigDecimal gpAmount ;

    /**  */
    @Column(name = "job_id")
    private Long jobId ;

    /** 支付金额 */
    @Column(name = "payment_amount")
    private BigDecimal paymentAmount ;

    /** 支付日期 */
    @Column(name = "payment_date")
    private Instant paymentDate ;

    @Column(name = "status")
    private Integer status ;
}
