package com.altomni.apn.finance.service.vo.invoicing;

import java.math.BigInteger;


public class UserLeaderVO {

    private BigInteger userId;

    private BigInteger userTeamId;

    private BigInteger leaderTeamId;

    private BigInteger leaderUserId;

    private String leaderUserName;

    private String teamCode;

    public BigInteger getUserId() {
        return userId;
    }

    public void setUserId(BigInteger userId) {
        this.userId = userId;
    }

    public BigInteger getUserTeamId() {
        return userTeamId;
    }

    public void setUserTeamId(BigInteger userTeamId) {
        this.userTeamId = userTeamId;
    }

    public BigInteger getLeaderTeamId() {
        return leaderTeamId;
    }

    public void setLeaderTeamId(BigInteger leaderTeamId) {
        this.leaderTeamId = leaderTeamId;
    }

    public BigInteger getLeaderUserId() {
        return leaderUserId;
    }

    public void setLeaderUserId(BigInteger leaderUserId) {
        this.leaderUserId = leaderUserId;
    }

    public String getLeaderUserName() {
        return leaderUserName;
    }

    public void setLeaderUserName(String leaderUserName) {
        this.leaderUserName = leaderUserName;
    }

    public String getTeamCode() {
        return teamCode;
    }

    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }
}
