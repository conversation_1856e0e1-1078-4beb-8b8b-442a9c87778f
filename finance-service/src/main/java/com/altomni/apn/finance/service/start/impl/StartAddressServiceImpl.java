package com.altomni.apn.finance.service.start.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.start.StartAddress;
import com.altomni.apn.finance.repository.start.StartAddressRepository;
import com.altomni.apn.finance.service.start.StartAddressService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;

@Service
@Transactional
public class StartAddressServiceImpl implements StartAddressService {

    private final Logger log = LoggerFactory.getLogger(StartAddressServiceImpl.class);

    @Resource
    private StartAddressRepository startAddressRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Override
    public StartAddress update(Long startId, LocationDTO locationDTO) {
        StartAddress address = startAddressRepository.findByStartId(startId).orElse(new StartAddress());
        StartAddress newAddress = new StartAddress();
        ServiceUtils.myCopyProperties(locationDTO, newAddress);
        newAddress.setOriginalLoc(JSONUtil.toJsonStr(locationDTO));
        newAddress.setStartId(startId);
        newAddress.setId(address.getId());
        return startAddressRepository.save(newAddress);
    }

    @Override
    public StartAddress findByStartId(Long startId) {
        StartAddress startAddress = startAddressRepository.findByStartId(startId).orElse(null);
        if (startAddress == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_FINDBYSTARTID_STARTADDRESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(startId),financeApiPromptProperties.getFinanceService()));
        }
        return startAddress;
    }
}
