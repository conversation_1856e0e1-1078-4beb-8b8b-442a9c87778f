package com.altomni.apn.finance.domain.start;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "start_client_info")
public class StartClientInfo extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5857723941896529236L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "start_id")
    private Long startId;

    @Column(name = "client_name")
    private String clientName;

    @Column(name = "client_division")
    private String clientDivision;

    @Column(name = "client_address")
    private String clientAddress;

    @Column(name = "client_location")
    private String clientLocation;

    @Column(name = "client_email")
    private String clientEmail;

    @Column(name = "client_info_id")
    private Long clientInfoId;

    @Column(name = "invoice_type_id")
    private Long invoiceTypeId;

    public static StartClientInfo fromStartDTO(StartDTO dto) {
        StartClientInfo result = new StartClientInfo();
        ServiceUtils.myCopyProperties(dto.getStartClientInfo(), result);
        return result;
    }

}
