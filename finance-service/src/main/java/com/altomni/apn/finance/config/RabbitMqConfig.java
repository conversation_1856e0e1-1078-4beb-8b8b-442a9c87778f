package com.altomni.apn.finance.config;

import com.altomni.apn.finance.config.env.FinanceConsumerMQProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class RabbitMqConfig {

    @Resource
    FinanceConsumerMQProperties financeConsumerMQProperties;


    /****** start finance consumer mq config  *******/
    @Bean(name = "financeConsumerConnectionFactory")
    public ConnectionFactory financeConsumerConnectionFactory() {
        return financeConsumerConnectionFactory(financeConsumerMQProperties.getHost(), financeConsumerMQProperties.getPort(), financeConsumerMQProperties.getVirtualHost(), financeConsumerMQProperties.getUserName(), financeConsumerMQProperties.getPassword());
    }

    public CachingConnectionFactory financeConsumerConnectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        connectionFactory.setPublisherReturns(true);
        connectionFactory.setPublisherConfirms(true);
        return connectionFactory;
    }

    @Bean(name = "financeConsumerFactory")
    public SimpleRabbitListenerContainerFactory financeConsumerFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("financeConsumerConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(1);
        configurer.configure(factory, connectionFactory);
        return factory;
    }


    @Bean(name = "financeConsumerRabbitTemplate")
    public RabbitTemplate financeConsumerRabbitTemplate(
            @Qualifier("financeConsumerConnectionFactory") ConnectionFactory connectionFactory
    ) {
        RabbitTemplate financeConsumerRabbitTemplate = new RabbitTemplate(connectionFactory);
        return financeConsumerRabbitTemplate;
    }
}