package com.altomni.apn.finance.service.common.impl;

import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.finance.service.common.HttpService;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Service
public class HttpServiceImpl implements HttpService {

    private final Logger log = LoggerFactory.getLogger(HttpServiceImpl.class);

    private final OkHttpClient client;

    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

//    @Value("${application.okHttpClient.connectionTimeout}")
//    private Integer connectionTimeout;
//
//    @Value("${application.okHttpClient.readTimeout}")
//    private Integer readTimeout;
//
//    @Value("${application.okHttpClient.connectionPoolSize}")
//    private Integer connectionPoolSize;
//
//    @Value("${application.okHttpClient.keepAliveDuration}")
//    private Integer keepAliveDuration;

    public HttpServiceImpl() {
        this.client = createOkHttpClient();
    }

    private OkHttpClient createOkHttpClient() {
        return new OkHttpClient().newBuilder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .connectionPool(new ConnectionPool(5, 100, TimeUnit.MILLISECONDS))
                .build();
    }

    private HttpResponse execute(Request request) throws IOException {
        try (Response response = client.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            return new HttpResponse(response.code(), responseBody);
        }
    }

    /**
     * OkHttpClient post method
     * @param url url
     * @param requestBody request body
     * @return HttpResponse
     */
    @Override
    public HttpResponse post(String url, String requestBody) throws IOException {
        return execute(new Request.Builder().url(url).post(RequestBody.create(JSON_TYPE, requestBody)).build());
    }
}
