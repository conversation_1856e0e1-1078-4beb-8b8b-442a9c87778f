package com.altomni.apn.finance.service.job;

import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.job.JobPermissionDTO;

import java.util.List;

public interface JobService {

    JobDTOV3 getJob(Long jobId);

    JobDTOV3 getJobByIdAndTenantId(Long jobId,Long tenantId);

    String getJobLocations(Long jobId);

    List<JobPermissionDTO> checkJobPermissionById(List<Long> jobids);

    JobPermissionDTO checkJobPermissionByChinaInvoicing(Long jobId);
}
