package com.altomni.apn.finance.service.start;


import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.web.rest.dto.StartTerminationInputDTO;

import java.io.IOException;
import java.util.Set;

/**
 * Service Interface for managing StartTermination.
 */
public interface StartTerminationService {

    /**
     * Save a startTermination.
     *
     * @param startTermination the entity to save
     * @return the persisted entity
     */
    StartDTO save(StartTerminationInputDTO startTermination) throws IOException;

    /**
     * Delete the "id" startTermination.
     *
     * @param startId the id of the entity
     */
    StartDTO delete(Long startId) throws IOException;

    Set<Long> findTerminatedApplicationIdsByTalentId(Long talentId);
}
