package com.altomni.apn.finance.web.rest.dto;

import com.altomni.apn.finance.domain.enumeration.start.StartFailedWarrantyActionPlan;
import com.altomni.apn.finance.domain.enumeration.start.StartFailedWarrantyReason;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class StartFailedWarrantyInputDTO {

    private Long id;

    private Long startId;

    private LocalDate endDate;

    private StartFailedWarrantyReason reason;

    private StartFailedWarrantyActionPlan actionPlan;

    private BigDecimal totalBillAmount;

    private String note;

    private Boolean markCandidateAvailable;
}
