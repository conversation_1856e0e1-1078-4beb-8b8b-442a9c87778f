package com.altomni.apn.finance.service.dto.invoicing;

import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;

@Data
public class InvoicingCandidateInfoDTO {

    private Long id;

    private Long jobId;

    private String jobName;

    private Long talentId;

    private String talentName;

    private BigDecimal gpAmount;

    private BigDecimal amountPercentage;

    private BigDecimal amountReceived;

    private BigDecimal prepaymentAmount;

    private Long startId;

    private Boolean chinaOrder;

    private Long companyId;

    private String companyName;

    //添加财务记录回款使用
    private BigDecimal paymentAmount;

    private BigDecimal expectedInvoicingAmount;

    private Instant expectedInvoicingDate;
}
