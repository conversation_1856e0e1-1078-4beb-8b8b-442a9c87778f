package com.altomni.apn.finance.repository.start;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.domain.start.Start;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * Spring Data JPA repository for the Start entity.
 */
@Repository
public interface StartRepository extends JpaRepository<Start,Long> {

    Start findFirstByTalentIdAndStatusOrderByStartDateDesc(Long talentId, StartStatus status);

    List<Start> findAllByTalentId(Long talentId);

    Start findByTalentIdAndTalentRecruitmentProcessIdAndStatus(Long talentId,Long talentRecruitmentProcessId,StartStatus status);

    @Query(value = "select t.* from start t where t.talent_id = ?1 and t.job_id = ?2 order by t.end_date desc limit 1", nativeQuery = true)
    Start findLastByTalentIdAndJobId(Long talentId, Long jobId);

    @Query(value = "select s.* from start s where s.talent_id = ?1 and s.status = ?2 order by s.end_date desc limit 1", nativeQuery = true)
    Start findByTalentIdAndStatusOrderByEndDate(Long talentId, Integer status);

    Start findByTalentIdAndJobIdAndStartType(Long talentId, Long jobId, StartType startType);

    List<Start> findAllByTalentIdAndJobIdAndStartTypeAndStartDateGreaterThanEqual(Long talentId, Long jobId, StartType startType, LocalDate startDate);

    @Modifying
    @Transactional
    @Query(value = "UPDATE Start t SET t.lastModifiedDate = current_timestamp, t.talentName = ?2 WHERE t.talentId = ?1")
    void updateStartLastEditedTimeAndTalentNameByTalentId(Long talentId, String talentName);

    @Modifying
    @Transactional
    @Query(value = "UPDATE Start t SET t.charge_number = ?2,t.tvc_number = ?3, corp_to_corp = ?4  WHERE t.id = ?1", nativeQuery = true)
    void updateStartChargeNumberAndTvcNumberAndCorpToCorpByStartId(Long startId, String chargeNumber, String tvcNumber, Boolean corpToCorp);

    List<Start> findAllByTalentRecruitmentProcessIdInAndPositionType(Collection<Long> talentRecruitmentProcessId, JobType positionType);

    List<Start> findAllByTalentRecruitmentProcessIdInAndPositionTypeAndStatusIn(List<Long> talentRecruitmentProcessId, JobType positionType, List<StartStatus> contractStartStatus);

    @Query(value = """
             select s.* from start s 
             left join talent_recruitment_process_eliminate trpe on s.talent_recruitment_process_id = trpe.talent_recruitment_process_id
             inner join start_client_info sci on sci.start_id = s.id
             left join invoice_type_config itc on itc.tenant_id = s.tenant_id and itc.id = sci.invoice_type_id and itc.status=1
             where s.tenant_id= ?1 and s.position_type= ?2 and s.talent_name like CONCAT('%', ?3, '%') and trpe.id is null and itc.label='oversea fte' limit ?4
             """, nativeQuery = true)
    List<Start> findByTenantIdAndPositionTypeAndTalentNameIsLike(Long tenantId, Integer jobType, String talentName, Integer limit);

    @Query(value = """
            select 
            DISTINCT s.id,s.company_id,s.job_id,
            s.job_title,s.talent_id,s.talent_name,s.company,
            	itc.label as note
            	,cast(sfr.total_bill_amount - IFNULL(ab.amountReceived,0) as char) as time_zone ,sfr.currency,
            	s.created_by,s.last_modified_by,s.created_date,s.last_modified_date,s.pteam_id,s.puser_id,s.channel_platform,
            	s.charge_number,s.corp_to_corp,s.status,s.start_date,s.end_date,
            	s.warranty_end_date,s.start_type,s.talent_recruitment_process_id,
            	s.client_contact_id,s.position_type,s.tvc_number,s.working_mode,
            	s.profit_sharing_ratio,s.is_substitute_talent,s.relation_process_id,s.substitute_talent_id,s.tenant_id
            from start s 
            inner join start_client_info sci on sci.start_id = s.id
            left join invoice_type_config itc on itc.tenant_id = s.tenant_id and itc.id = sci.invoice_type_id and itc.status=1
            left join talent_recruitment_process_eliminate trpe on s.talent_recruitment_process_id = trpe.talent_recruitment_process_id
            join start_commission kpi on kpi.start_id = s.id
            left join start_fte_rate sfr on sfr.start_id = s.id
            left join (
                select IFNULL(sum(amount_received),0) as amountReceived,ici.start_id from invoicing_candidate_info ici
                join invoicing_application_info iai on ici.invoice_application_id = iai.id
                where iai.invoicing_status in (0,2,3,4,5,6,7) and iai.`status` = 1 and iai.void_invoicing is null and ici.`status` = 1 group by ici.start_id
            ) ab on ab.start_id = s.id
            where  s.tenant_id= ?1  and s.position_type=3
             and (s.talent_name like CONCAT('%', ?2, '%') or s.job_title like CONCAT('%', ?2, '%') or s.talent_id like CONCAT('%', ?2, '%')) 
             and trpe.id is null 
             and kpi.user_id = ?3
             limit ?4
             """, nativeQuery = true)
    List<Start> findByTenantIdAndPositionTypeAndUserIdAndTalentNameIsLike(Long tenantId, String talentName,Long userId, Integer limit);


    @Modifying
    @Query("update Start s set s.status=:toStatus where s.talentRecruitmentProcessId=:talentRecruitmentProcessId and s.status=:fromStatus")
    void updateStatusByTalentRecruitmentProcessId(@Param("fromStatus") StartStatus fromStatus, @Param("toStatus") StartStatus toStatus, @Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query("select s.companyId from Start s " +
            " where s.id=:startId")
    Long getCompanyIdByStartId(@Param("startId") Long startId);

    @Query(value = """
            select s.* from start s left join talent_recruitment_process_eliminate trpe 
            on s.talent_recruitment_process_id = trpe.talent_recruitment_process_id 
            where s.id = ?1 and trpe.id is null
            """, nativeQuery = true)
    Start findStartByIdAndNotEliminate(Long id);

    @Query(value = "select s.talent_id from start s " +
            "left join talent_recruitment_process_resignation r on r.talent_recruitment_process_id=s.talent_recruitment_process_id " +
            "where s.talent_id in :talentIds  and s.status=0 and s.position_type= :jobType  and r.id is null", nativeQuery = true)
    Set<Long> filterTalentsWithActiveFteStarts(@Param("talentIds") Set<Long> talentIds, @Param("jobType") Integer jobType);

    Optional<Start> findByTalentRecruitmentProcessIdAndStatus(Long talentRecruitmentProcessId, StartStatus status);

    List<Start> findAllByJobId(Long jobId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE talent_recruitment_process_onboard SET is_substitute_talent = ?2,relation_process_id = ?3, substitute_talent_id = ?4  WHERE talent_recruitment_process_id = ?1", nativeQuery = true)
    void updateOnboardByTalentRecruitmentProcessId(Long talentRecruitmentProcessId, Integer isSubstituteTalent, Long relationProcessId,Long substituteTalentId);

    List<Start> findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
