package com.altomni.apn.finance.service.dto.start;


import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigInteger;

/**
 * A StartContractRate.
 */

public class StartClientInfoCompanyDTO {

    private BigInteger id;

    private String clientName;

    private String clientAddress;

    private String clientDivision;

    private BigInteger companyId;

    private BigInteger tenantId;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getClientAddress() {
        return clientAddress;
    }

    public void setClientAddress(String clientAddress) {
        this.clientAddress = clientAddress;
    }

    public String getClientDivision() {
        return clientDivision;
    }

    public void setClientDivision(String clientDivision) {
        this.clientDivision = clientDivision;
    }

    public BigInteger getCompanyId() {
        return companyId;
    }

    public void setCompanyId(BigInteger companyId) {
        this.companyId = companyId;
    }

    public BigInteger getTenantId() {
        return tenantId;
    }

    public void setTenantId(BigInteger tenantId) {
        this.tenantId = tenantId;
    }
}
