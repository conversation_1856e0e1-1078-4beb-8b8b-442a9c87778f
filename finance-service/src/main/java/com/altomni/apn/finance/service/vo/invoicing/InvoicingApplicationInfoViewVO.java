package com.altomni.apn.finance.service.vo.invoicing;

import com.altomni.apn.common.domain.enumeration.company.*;
import com.altomni.apn.finance.service.dto.invoicing.InvoicingNoteDTO;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.Instant;
import java.util.List;


public class InvoicingApplicationInfoViewVO {

    private Long id;

    /**
     * 1、全职招聘开票申请  2、预付金发票 3、废票
     */
    private String invoicingApplicationType;

    /** 1、客户方付税款  2、我方付税款 */
    private TaxPayerType taxPayerType;

    private Integer voidInvoicing;

    /**
     * 原id
     */
    private Long invalidInvoicingId;

    /**
     * 废票开票原因
     */
    private String invoicingReason;

    /**
     * 编码
     */
    private String codeNumber;

    /**
     *
     */
    private BigInteger companyId;

    /**
     *
     */
    private BigInteger tenantId;

    /**
     * 客户开票信息
     */
    private Long clientInvoicingId;

    /**
     * 1上海, 2深圳, 3尹泰
     */
    private String invoicingBody;

    /**
     * 1增值税电子专业发票, 2增值税电子普通发票, 3抵扣发票
     */
    private InvoicingType invoicingType;

    /**
     * 1FTE, 2RPO, 3STF-外包, 4STF-人事代理
     */
    private InvoicingBusinessType invoicingBusinessType;

    /**
     * 开票内容名称
     */
    private String invoicingTaxName;

    private BigInteger invoicingServerTaxId;

    /**
     * 开票税率
     */
    private Double invoicingTax;

    /**
     * 发票格式 1pdf, 2ofd, 3xml
     */
    private String invoicingFormat;

    /**
     * 确认函
     */
    private String confirmationLetterUrl;

    /**
     * 发票
     */
    private String invoiceUrl;

    /**
     * 废票确认函
     */
    private String oldConfirmationLetterUrl;

    private BigDecimal invoicingAmount;

    private BigDecimal prepaymentAmount;

    private BigDecimal paymentAmount;

    /**
     * 待回款金额
     */
    private BigDecimal amountDue;

    /**
     * 发票税额
     */
    private BigDecimal invoiceTax;

    /**
     * 不包含税额
     */
    private BigDecimal taxesNotIncluded;

    /**
     * 应开票总金额
     */
    private BigDecimal gpAmount;

    /**
     * 未开票金额
     */
    private BigDecimal uninvoicedAmount;

    /**
     * 待审批、审批驳回、待开票、已开票、未回款、部分回款、逾期、全部回款、已作废
     */
    private InvoicingStatus invoicingStatus;

    /**
     * 数电票号码
     */
    private String elecInvoiceNumber;

    /**
     * 开票日期
     */
    private Instant invoicingDate;

    /**
     * 账单天数
     */
    private Integer dueWithinDays;

    /**
     * 到期日期
     */
    private Instant paymentDueDate;

    private String note;

    private String invoiceNote;

    private Instant createdDate;

    private String applicationUser;

    private String companyName;

    private String clientName;

    private String socialCreditCode;

    private String bankName;

    private String bankAccount;

    private String invoicingAddress;

    private String phone;

    private String voidInvoicingApplication;

    private Long createUserId;

    List<InvoicingCandidateInfoVO> candidateInfoList;

    List<InvoicingRecordLogVO> recordLogList;

    List<InvoicingRecordPaymentInfoVO> recordPaymentInfoList;

    List<InvoicingNoteDTO> invoicingUrlList;

    public String getInvoicingApplicationType() {
        return invoicingApplicationType;
    }

    public void setInvoicingApplicationType(String invoicingApplicationType) {
        this.invoicingApplicationType = StringUtils.isNotBlank(invoicingApplicationType)?InvoicingApplicationType.getNameFromDbValue(Integer.valueOf(invoicingApplicationType)):"";
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getInvalidInvoicingId() {
        return invalidInvoicingId;
    }

    public void setInvalidInvoicingId(Long invalidInvoicingId) {
        this.invalidInvoicingId = invalidInvoicingId;
    }

    public TaxPayerType getTaxPayerType() { return taxPayerType; }

    public void setTaxPayerType(TaxPayerType taxPayerType) { this.taxPayerType = taxPayerType; }

    public String getInvoicingReason() {
        return invoicingReason;
    }

    public void setInvoicingReason(String invoicingReason) {
        this.invoicingReason = invoicingReason;
    }

    public String getCodeNumber() {
        return codeNumber;
    }

    public void setCodeNumber(String codeNumber) {
        this.codeNumber = codeNumber;
    }

    public BigInteger getCompanyId() {
        return companyId;
    }

    public void setCompanyId(BigInteger companyId) {
        this.companyId = companyId;
    }

    public BigInteger getTenantId() {
        return tenantId;
    }

    public void setTenantId(BigInteger tenantId) {
        this.tenantId = tenantId;
    }

    public Long getClientInvoicingId() {
        return clientInvoicingId;
    }

    public void setClientInvoicingId(Long clientInvoicingId) {
        this.clientInvoicingId = clientInvoicingId;
    }

    public String getInvoicingBody() {
        return invoicingBody;
    }

    public void setInvoicingBody(String invoicingBody) {
        this.invoicingBody = invoicingBody;
    }

    public InvoicingType getInvoicingType() {
        return invoicingType;
    }

    public void setInvoicingType(InvoicingType invoicingType) {
        this.invoicingType = invoicingType;
    }

    public InvoicingBusinessType getInvoicingBusinessType() {
        return invoicingBusinessType;
    }

    public void setInvoicingBusinessType(InvoicingBusinessType invoicingBusinessType) {
        this.invoicingBusinessType = invoicingBusinessType;
    }

    public String getInvoicingTaxName() {
        return invoicingTaxName;
    }

    public void setInvoicingTaxName(String invoicingTaxName) {
        this.invoicingTaxName = invoicingTaxName;
    }

    public Double getInvoicingTax() {
        return invoicingTax;
    }

    public void setInvoicingTax(Double invoicingTax) {
        this.invoicingTax = invoicingTax;
    }

    public String getInvoicingFormat() {
        return invoicingFormat;
    }

    public void setInvoicingFormat(String invoicingFormat) {
        this.invoicingFormat = invoicingFormat;
    }

    public String getConfirmationLetterUrl() {
        return confirmationLetterUrl;
    }

    public void setConfirmationLetterUrl(String confirmationLetterUrl) {
        this.confirmationLetterUrl = confirmationLetterUrl;
    }

    public BigDecimal getInvoicingAmount() {
        return invoicingAmount;
    }

    public void setInvoicingAmount(BigDecimal invoicingAmount) {
        this.invoicingAmount = invoicingAmount;
    }

    public BigDecimal getPrepaymentAmount() {
        return prepaymentAmount;
    }

    public void setPrepaymentAmount(BigDecimal prepaymentAmount) {
        this.prepaymentAmount = prepaymentAmount;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getAmountDue() {
        return amountDue;
    }

    public void setAmountDue(BigDecimal amountDue) {
        this.amountDue = amountDue;
    }

    public BigDecimal getInvoiceTax() {
        return invoiceTax;
    }

    public void setInvoiceTax(BigDecimal invoiceTax) {
        this.invoiceTax = invoiceTax;
    }

    public BigDecimal getTaxesNotIncluded() {
        return taxesNotIncluded;
    }

    public void setTaxesNotIncluded(BigDecimal taxesNotIncluded) {
        this.taxesNotIncluded = taxesNotIncluded;
    }

    public BigDecimal getGpAmount() {
        return gpAmount;
    }

    public void setGpAmount(BigDecimal gpAmount) {
        this.gpAmount = gpAmount;
    }

    public BigDecimal getUninvoicedAmount() {
        return uninvoicedAmount;
    }

    public void setUninvoicedAmount(BigDecimal uninvoicedAmount) {
        this.uninvoicedAmount = uninvoicedAmount;
    }

    public InvoicingStatus getInvoicingStatus() {
        return invoicingStatus;
    }

    public void setInvoicingStatus(InvoicingStatus invoicingStatus) {
        this.invoicingStatus = invoicingStatus;
    }

    public String getElecInvoiceNumber() {
        return elecInvoiceNumber;
    }

    public void setElecInvoiceNumber(String elecInvoiceNumber) {
        this.elecInvoiceNumber = elecInvoiceNumber;
    }

    public Instant getInvoicingDate() {
        return invoicingDate;
    }

    public void setInvoicingDate(Instant invoicingDate) {
        this.invoicingDate = invoicingDate;
    }

    public Integer getDueWithinDays() {
        return dueWithinDays;
    }

    public void setDueWithinDays(Integer dueWithinDays) {
        this.dueWithinDays = dueWithinDays;
    }

    public Instant getPaymentDueDate() {
        return paymentDueDate;
    }

    public void setPaymentDueDate(Instant paymentDueDate) {
        this.paymentDueDate = paymentDueDate;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public String getApplicationUser() {
        return applicationUser;
    }

    public void setApplicationUser(String applicationUser) {
        this.applicationUser = applicationUser;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getSocialCreditCode() {
        return socialCreditCode;
    }

    public void setSocialCreditCode(String socialCreditCode) {
        this.socialCreditCode = socialCreditCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getInvoicingAddress() {
        return invoicingAddress;
    }

    public void setInvoicingAddress(String invoicingAddress) {
        this.invoicingAddress = invoicingAddress;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public List<InvoicingCandidateInfoVO> getCandidateInfoList() {
        return candidateInfoList;
    }

    public void setCandidateInfoList(List<InvoicingCandidateInfoVO> candidateInfoList) {
        this.candidateInfoList = candidateInfoList;
    }

    public List<InvoicingRecordLogVO> getRecordLogList() {
        return recordLogList;
    }

    public void setRecordLogList(List<InvoicingRecordLogVO> recordLogList) {
        this.recordLogList = recordLogList;
    }

    public List<InvoicingRecordPaymentInfoVO> getRecordPaymentInfoList() {
        return recordPaymentInfoList;
    }

    public void setRecordPaymentInfoList(List<InvoicingRecordPaymentInfoVO> recordPaymentInfoList) {
        this.recordPaymentInfoList = recordPaymentInfoList;
    }

    public BigInteger getInvoicingServerTaxId() {
        return invoicingServerTaxId;
    }

    public void setInvoicingServerTaxId(BigInteger invoicingServerTaxId) {
        this.invoicingServerTaxId = invoicingServerTaxId;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Integer getVoidInvoicing() {
        return voidInvoicing;
    }

    public void setVoidInvoicing(Integer voidInvoicing) {
        this.voidInvoicing = voidInvoicing;
    }

    public String getOldConfirmationLetterUrl() {
        return oldConfirmationLetterUrl;
    }

    public void setOldConfirmationLetterUrl(String oldConfirmationLetterUrl) {
        this.oldConfirmationLetterUrl = oldConfirmationLetterUrl;
    }

    public String getVoidInvoicingApplication() {
        return voidInvoicingApplication;
    }

    public void setVoidInvoicingApplication(String voidInvoicingApplication) {
        this.voidInvoicingApplication = voidInvoicingApplication;
    }

    public Long getCreateUserId() {
        return createUserId;
    }

    public void setCreateUserId(Long createUserId) {
        this.createUserId = createUserId;
    }

    public String getInvoiceNote() {
        return invoiceNote;
    }

    public void setInvoiceNote(String invoiceNote) {
        this.invoiceNote = invoiceNote;
    }

    public List<InvoicingNoteDTO> getInvoicingUrlList() {
        return invoicingUrlList;
    }

    public void setInvoicingUrlList(List<InvoicingNoteDTO> invoicingUrlList) {
        this.invoicingUrlList = invoicingUrlList;
    }

    public String getInvoiceUrl() { return invoiceUrl; }

    public void setInvoiceUrl(String invoiceUrl) { this.invoiceUrl = invoiceUrl; }
}
