package com.altomni.apn.finance.service.invoice.impl;

import com.altomni.apn.common.service.enums.EnumInvoiceReceivingAccountService;
import com.altomni.apn.common.vo.dict.EnumReceivingAccountVO;
import com.altomni.apn.finance.service.invoice.InvoiceReceivingAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
@Slf4j
public class InvoiceReceivingAccountServiceImpl implements InvoiceReceivingAccountService {

    @Resource
    private EnumInvoiceReceivingAccountService enumInvoiceReceivingAccountService;

    @Override
    public List<EnumReceivingAccountVO> findAllTypes() {
        return enumInvoiceReceivingAccountService.findAll();
    }

}
