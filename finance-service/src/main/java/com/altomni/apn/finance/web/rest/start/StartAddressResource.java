package com.altomni.apn.finance.web.rest.start;

import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.finance.domain.start.StartAddress;
import com.altomni.apn.finance.service.start.StartAddressService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.start.StartService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * REST controller for managing StartAddress.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StartAddressResource {

    private static final String ENTITY_NAME = "StartAddress";

    @Resource
    private StartAddressService startAddressService;

    @Resource
    private StartService startService;

    @PutMapping("/start-addresses/startId/{startId}")
    public ResponseEntity<StartAddress> updateStartAddress(@PathVariable Long startId, @RequestBody LocationDTO locationDTO) {
        log.info("[APN: StartAddress @{}] REST request to update StartAddress : {}, {}", SecurityUtils.getUserId(), startId, locationDTO);
        startService.checkPermissionByStartId(startId);
        StartAddress result = startAddressService.update(startId, locationDTO);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @GetMapping("/start-addresses/startId/{startId}")
    public ResponseEntity<StartAddress> getStartAddress(@PathVariable Long startId) {
        log.info("[APN: StartAddress @{}] REST request to get StartAddress : {}", SecurityUtils.getUserId(), startId);
        StartAddress startAddress = startAddressService.findByStartId(startId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(startAddress));
    }
}
