package com.altomni.apn.finance.service.invoice;

import com.altomni.apn.finance.domain.invoice.ViewInvoiceList;
import com.altomni.apn.finance.service.dto.invoice.ViewInvoiceListSearchResult;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface ViewInvoiceListService {

    Page<ViewInvoiceList> findAll(BooleanExpression invoice, Pageable pageable);

    ViewInvoiceListSearchResult toViewInvoiceListSearchResult(BooleanExpression booleanExpression);
}
