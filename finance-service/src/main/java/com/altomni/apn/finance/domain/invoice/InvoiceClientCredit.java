package com.altomni.apn.finance.domain.invoice;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A InvoiceClientCredit.
 */
@Data
@Entity
@Table(name = "invoice_client_credit")
public class InvoiceClientCredit extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4943795801702149418L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "company_id")
    private Long companyId;

    @ApiModelProperty(value = "Currency of balance")
    @Column(name = "currency")
    private Integer currency;

    @Column(name = "balance")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal balance;

    public BigDecimal getBalance() {
        return balance == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(balance));

    }

}
