package com.altomni.apn.finance.repository.invoice;

import com.altomni.apn.finance.domain.invoice.InvoicePaymentRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


/**
 * Spring Data  repository for the InvoicePaymentRecord entity.
 */
@SuppressWarnings("unused")
@Repository
public interface InvoicePaymentRecordRepository extends JpaRepository<InvoicePaymentRecord, Long> {

    List<InvoicePaymentRecord> findByInvoiceIdAndActivated(Long invoiceId, boolean activated);

    List<InvoicePaymentRecord> findAllByInvoiceIdInAndActivated(Collection<Long> invoiceIds, boolean activated);

    List<InvoicePaymentRecord> findAllByInvoiceIdAndCloseWithoutFullPaymentAndActivated(Long invoiceId, Boolean closeWithoutFullPayment, boolean activated);

    @Transactional
    @Modifying
    @Query(value = "update invoice_payment_record set activated = 0 where id = ?1", nativeQuery = true)
    void softDeletePayment(Long paymentId);

    InvoicePaymentRecord findByIdAndActivated(Long id, boolean activated);

    Optional<InvoicePaymentRecord> findById(Long id);

    List<InvoicePaymentRecord> findAllByIdIn(Collection<Long> ids);


}
