package com.altomni.apn.finance.domain.invoice;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityType;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityTypeConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A InvoiceActivity.
 */
@Data
@Entity
@Table(name = "invoice_activity")
public class InvoiceActivity extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -4699610725897988464L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Invoice id for easy search. Read Only.")
    @Column(name = "invoice_id")
    private Long invoiceId;

    @Column(name = "invoice_no")
    private String invoiceNo;

    @Convert(converter = InvoiceActivityTypeConverter.class)
    @Column(name = "invoice_activity_type")
    private InvoiceActivityType invoiceActivityType;

    @ApiModelProperty(value = "User id.", required = true)
    @Column(name = "user_id")
    private Long userId;

    @ApiModelProperty(value = "User full name.", required = true)
    @Column(name = "user_full_name")
    private String userFullName;

    @Column(name = "amount")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal amount;

    @Column(name = "note")
    private String note;

    @Column(name = "payment_id")
    private Long paymentId;

    public BigDecimal getAmount() {
        return amount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(amount));
    }

}
