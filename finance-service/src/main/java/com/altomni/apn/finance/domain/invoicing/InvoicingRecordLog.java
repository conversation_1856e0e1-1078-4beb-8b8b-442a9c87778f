package com.altomni.apn.finance.domain.invoicing;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.company.InvoicingLogStatus;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@Data
@Entity
@Table(name = "invoicing_record_log")
public class InvoicingRecordLog extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 开票id */
    @Column(name = "invoicing_id")
    private Long invoicingId ;

    @Column(name = "invoicing_status")
    private InvoicingLogStatus invoicingStatus ;

    /** 操作人名称 */
    @Column(name = "operated_by_name")
    private String operatedByName ;

    /** 操作人id */
    @Column(name = "operated_by_id")
    private Long operatedById ;

    /** 操作日期 */
    @Column(name = "operated_date")
    private Instant operatedDate ;

    /**  */
    @Column(name = "title")
    private String title ;

    @Column(name = "sub_title")
    private String subTitle ;

    /** 备注 */
    @Column(name = "note")
    private String note ;

    /**  */
    @Column(name = "note_json")
    private String noteJson ;

    @Column(name = "electronic_invoice_number")
    private String electronicInvoiceNumber ;

    /**  */
    @Column(name = "reference_id")
    private Long referenceId ;

    /**  */
    @Column(name = "void_reference_id")
    private Long voidReferenceId ;
}
