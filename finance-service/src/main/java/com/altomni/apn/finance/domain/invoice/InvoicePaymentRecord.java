package com.altomni.apn.finance.domain.invoice;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoicePaymentMethod;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoicePaymentMethodConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * A InvoicePaymentRecord.
 */
@Data
@Entity
@Table(name = "invoice_payment_record")
public class InvoicePaymentRecord extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5880564893352238753L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Invoice id")
    @Column(name = "invoice_id")
    private Long invoiceId;

    @ApiModelProperty(value = "The date of received payment.")
    @Column(name = "payment_date")
    private LocalDate paymentDate;

    @ApiModelProperty(value = "The amount of received from client. It should be equals with due amount.")
    @Column(name = "paid_amount")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "Close or not this payment without a full payment. ")
    @Column(name = "close_without_full_payment")
    private Boolean closeWithoutFullPayment = Boolean.FALSE;

    @ApiModelProperty(value = "Amount payment method.")
    @Convert(converter = InvoicePaymentMethodConverter.class)
    @Column(name = "payment_method")
    private InvoicePaymentMethod paymentMethod;

    @ApiModelProperty(value = "Is this client already paid startup fee or not")
    @Column(name = "paid_startup_fee")
    private Boolean paidStartupFee = Boolean.FALSE;

    @ApiModelProperty(value = "Startup fee invoice date")
    @Column(name = "startup_fee_date")
    private LocalDate startupFeeDate;

    @ApiModelProperty(value = "Startup fee invoice amount")
    @Column(name = "startup_fee_amount", precision = 10, scale = 2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal startupFeeAmount;

    @ApiModelProperty(value = "Startup fee invoice number")
    @Column(name = "startup_fee_invoice_no")
    private String startupFeeInvoiceNo;

    @ApiModelProperty(value = "Apply credit")
    @Column(name = "apply_credit")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal applyCredit;

    @ApiModelProperty(value = "Currency of payment")
    @Column(name = "currency")
    private Integer currency;

    @ApiModelProperty(value = "Conversion rate between collection currency and invoice currency. " +
            "eg: if collection currency == USD,  invoice currency == CAD ,  CAD:USD = 1.35:1" +
            "exchangeRate = invoice currency : collection currency = 1.35:1")
    @Column(name = "exchange_rate")
    private String exchangeRate;

    @Column(name = "note")
    private String note;

    @ApiModelProperty(value = "Whether payment is activated. Default is true.")
    @NotNull
    @Column(nullable = false)
    private boolean activated = true;

    public BigDecimal getPaidAmount() {
        return paidAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(paidAmount));
    }

    public BigDecimal getStartupFeeAmount() {
        return startupFeeAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(startupFeeAmount));
    }

    public BigDecimal getApplyCredit() {
        return applyCredit == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(applyCredit));
    }

}
