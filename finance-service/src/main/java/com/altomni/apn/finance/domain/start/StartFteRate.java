package com.altomni.apn.finance.domain.start;


import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.domain.enumeration.application.FeeType;
import com.altomni.apn.common.domain.enumeration.application.FeeTypeConverter;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Entity
@Table(name = "start_fte_rate")
public class StartFteRate extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -1287979898313457637L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "start_id")
    private Long startId;

    @Column(name = "currency")
    private Integer currency = CurrencyConstants.USD;

    @Convert(converter = RateUnitTypeConverter.class)
    @Column(name = "rate_unit_type")
    private RateUnitType rateUnitType;

    @Column(name = "total_billable_amount", precision=20, scale=2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillableAmount = BigDecimal.ZERO;

    @Convert(converter = FeeTypeConverter.class)
    @Column(name = "fee_type")
    private FeeType feeType;

    @Column(name = "fee_percentage", precision=10, scale=4)
    private BigDecimal feePercentage;

    @ApiModelProperty(value = "GP")
    @Column(name = "total_bill_amount", precision=20, scale=2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillAmount = BigDecimal.ZERO;

    @Transient
    @JsonProperty
    private List<StartFteSalaryPackage> salaryPackages;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getStartId() {
        return startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }


    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public RateUnitType getRateUnitType() {
        return rateUnitType;
    }

    public void setRateUnitType(RateUnitType rateUnitType) {
        this.rateUnitType = rateUnitType;
    }

    public BigDecimal getTotalBillableAmount() {
        return totalBillableAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalBillableAmount));
    }

    public void setTotalBillableAmount(BigDecimal totalBillableAmount) {
        this.totalBillableAmount = totalBillableAmount;
    }

    public FeeType getFeeType() {
        return feeType;
    }

    public void setFeeType(FeeType feeType) {
        this.feeType = feeType;
    }

    public BigDecimal getFeePercentage() {
        return feePercentage;
    }

    public void setFeePercentage(BigDecimal feePercentage) {
        this.feePercentage = feePercentage;
    }

    public BigDecimal getTotalBillAmount() {
        return totalBillAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalBillAmount));
    }

    public void setTotalBillAmount(BigDecimal totalBillAmount) {
        this.totalBillAmount = totalBillAmount;
    }

    public List<StartFteSalaryPackage> getSalaryPackages() {
        return salaryPackages;
    }

    public void setSalaryPackages(List<StartFteSalaryPackage> salaryPackages) {
        this.salaryPackages = salaryPackages;
    }

    @Override
    public String toString() {
        return "StartFteRate{" +
                "id=" + id +
                ", startId=" + startId +
                ", currency=" + currency +
                ", rateUnitType=" + rateUnitType +
                ", totalBillableAmount=" + totalBillableAmount +
                ", feeType=" + feeType +
                ", feePercentage=" + feePercentage +
                ", totalBillAmount=" + totalBillAmount +
                ", salaryPackages=" + salaryPackages +
                '}';
    }
}
