package com.altomni.apn.finance.repository.start;

import com.altomni.apn.finance.domain.start.StartFailedWarranty;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the StartFailedWarranty entity.
 */
@SuppressWarnings("unused")
@Repository
public interface StartFailedWarrantyRepository extends JpaRepository<StartFailedWarranty, Long> {

    StartFailedWarranty findByStartId(Long startId);

    List<StartFailedWarranty> findAllByStartIdIn(List<Long> startIds);
}
