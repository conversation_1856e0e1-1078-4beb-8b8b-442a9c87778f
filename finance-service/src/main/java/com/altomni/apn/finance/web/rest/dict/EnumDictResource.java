package com.altomni.apn.finance.web.rest.dict;

import com.altomni.apn.common.aop.cache.CacheControl;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.dict.EnumReceivingAccountVO;
import com.altomni.apn.finance.service.invoice.InvoiceReceivingAccountService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.net.URISyntaxException;
import java.util.List;

@Api(tags = {"APN-Invoice-EnumDict"})
@Slf4j
@RestController
@RequestMapping("/api/v3/dict")
public class EnumDictResource {

    @Resource
    private InvoiceReceivingAccountService invoiceReceivingAccountService;

    /**
     * find all receiving account
     * @return
     * @throws URISyntaxException
     */
    @GetMapping("/receivingAccount")
    @CacheControl
    public ResponseEntity<List<EnumReceivingAccountVO>> findAllTypes() throws URISyntaxException {
        log.info("[APN: Invoice @{}] REST request to find all invoice receiving account", SecurityUtils.getUserId());
        List<EnumReceivingAccountVO> result = invoiceReceivingAccountService.findAllTypes();
        return ResponseEntity.ok().body(result);
    }

}
