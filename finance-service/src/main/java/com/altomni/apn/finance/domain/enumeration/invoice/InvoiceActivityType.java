package com.altomni.apn.finance.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The InvoiceActivityType enumeration.
 */
public enum InvoiceActivityType implements ConvertedEnum<Integer> {
    CREATE(0),
    VOID(1),
    DOWNLOAD(2),
    PAYMENT(3),
    STARTUP_FEE_USED(4),
    STARTUP_FEE_ROLL_BACK(5),
    CREDIT_APPLY(6),
    CREDIT_DEDUCT(7),
    CREDIT_REFUND(8),
    PAYMENT_UNRECORD(9),
    CREDIT_UNAPPLY(10);



    private final Integer dbValue;

    InvoiceActivityType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<InvoiceActivityType, Integer> resolver =
        new ReverseEnumResolver<>(InvoiceActivityType.class, InvoiceActivityType::toDbValue);

    public static InvoiceActivityType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
