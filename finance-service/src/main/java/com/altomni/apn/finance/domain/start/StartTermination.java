package com.altomni.apn.finance.domain.start;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.StartTerminationReason;
import com.altomni.apn.common.domain.enumeration.StartTerminationReasonConverter;
import com.altomni.apn.finance.domain.enumeration.start.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "start_termination")
public class StartTermination extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 839486790506237875L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "start_id")
    private Long startId;

    @Convert(converter = StartStatusConverter.class)
    @Column(name = "start_status")
    private StartStatus startStatus;

    @Column(name = "start_end_date")
    private LocalDate startEndDate;

    @Column(name = "termination_date")
    private LocalDate terminationDate;

    @Convert(converter = StartTerminationReasonConverter.class)
    @Column(name = "reason")
    private StartTerminationReason reason;

    @Convert(converter = StartTerminationConvertToFteFeeStatusConverter.class)
    @Column(name = "convert_to_fte_fee_status")
    private StartTerminationConvertToFteFeeStatus convertToFteFeeStatus;

    @Column(name = "note")
    private String note;

    @Column(name = "reason_comments")
    private String reasonComments;

    @Column(name = "mark_candidate_available")
    private Boolean markCandidateAvailable;

}
