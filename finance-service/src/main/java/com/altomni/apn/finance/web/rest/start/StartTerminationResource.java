package com.altomni.apn.finance.web.rest.start;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.start.StartService;
import com.altomni.apn.finance.service.start.StartTerminationService;
import com.altomni.apn.finance.service.talent.TalentService;
import com.altomni.apn.finance.web.rest.dto.StartTerminationInputDTO;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Set;

/**
 * REST controller for managing StartTermination.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StartTerminationResource {

    private static final String ENTITY_NAME = "StartTermination";

    @Resource
    private TalentService talentService;

    @Resource
    private StartTerminationService startTerminationService;

    @Resource
    private StartService startService;


    @PostMapping("/starts/termination/{startId}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<StartDTO> termination(@PathVariable Long startId, @Valid @RequestBody StartTerminationInputDTO startTermination) throws URISyntaxException, IOException {
        log.info("[APN: StartTermination @{}] REST request to termination StartTermination : {}", SecurityUtils.getUserId(), startTermination);
        startService.checkPermissionByStartId(startId);
        startTermination.setStartId(startId);
        StartDTO result = startTerminationService.save(startTermination);
        try {
            talentService.syncTalentToES(result.getTalentId());
        } catch (Exception e) {
            log.error("[APN: StartTermination @{}] Sync talent and start to ES error: " + e.getMessage(), SecurityUtils.getUserId());
        }
        return ResponseEntity.ok(result);
    }

    @PutMapping("/starts/cancel-termination/{startId}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<StartDTO> cancelTermination(@PathVariable Long startId) throws IOException {
        log.info("[APN: StartTermination @{}] REST request to cancel termination: {}", SecurityUtils.getUserId(), startId);
        startService.checkPermissionByStartId(startId);
        StartDTO result = startTerminationService.delete(startId);
        try {
            talentService.syncTalentToES(result.getTalentId());
        } catch (Exception e) {
            log.error("[APN: StartTermination @{}] Sync talent and start to ES error: " + e.getMessage(), SecurityUtils.getUserId());
        }
        return ResponseEntity.ok(result);
    }

    @GetMapping("/starts/termination/talent/{talentId}/applications")
    public ResponseEntity<Set<Long>> findTerminatedApplicationIdsByTalentId(@PathVariable("talentId") Long talentId){
        log.info("[APN: StartTermination @{}] REST request to get terminated applications by talentId: {}", SecurityUtils.getUserId(), talentId);
        return ResponseEntity.ok(startTerminationService.findTerminatedApplicationIdsByTalentId(talentId));
    }
}
