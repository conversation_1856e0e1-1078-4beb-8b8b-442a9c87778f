package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.finance.domain.invoicing.EnumInvoicingServiceTax;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the EnumInvoicingServiceTax entity.
 */
@SuppressWarnings("unused")
@Repository
public interface EnumInvoicingServiceTaxRepository extends JpaRepository<EnumInvoicingServiceTax, Long> {

}
