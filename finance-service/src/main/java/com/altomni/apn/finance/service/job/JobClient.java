package com.altomni.apn.finance.service.job;

import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.job.JobPermissionDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(value = "job-service")
public interface JobClient {

    @GetMapping("/job/api/v3/jobs/{id}")
    ResponseEntity<JobDTOV3> getJob(@PathVariable("id") Long jobId);

    @GetMapping("/job/api/v3/jobs/{id}/{tenantId}")
    ResponseEntity<JobDTOV3> getJobByIdAndTenantId(@PathVariable("id") Long jobId,@PathVariable("tenantId") Long tenantId);

//    @GetMapping("/job/api/v3/dict/currency/all")
//    ResponseEntity<List<EnumCurrency>> findAllEnumCurrency();

    @GetMapping("/job/api/v3/jobs/{id}/locations")
    ResponseEntity<String> getJobLocations(@PathVariable("id") Long jobId);

    @PostMapping("/job/api/v3/check-job-permission-by-id")
    ResponseEntity<List<JobPermissionDTO>> checkJobPermissionById(@RequestBody List<Long> jobIds);

    @PostMapping("/job/api/v3/check-job-permission-by-china-invoicing")
    ResponseEntity<JobPermissionDTO> checkJobPermissionByChinaInvoicing(@RequestBody Long jobId);
}
