package com.altomni.apn.finance.service.start.impl;

import cn.hutool.core.bean.BeanUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.StartTerminationReason;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.config.Constants;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.domain.start.Start;
import com.altomni.apn.finance.domain.start.StartContractRate;
import com.altomni.apn.finance.domain.start.StartTermination;
import com.altomni.apn.finance.repository.start.StartRepository;
import com.altomni.apn.finance.repository.start.StartTerminationRepository;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.start.StartContractRateService;
import com.altomni.apn.finance.service.start.StartService;
import com.altomni.apn.finance.service.start.StartTerminationService;
import com.altomni.apn.finance.service.talent.TalentService;
import com.altomni.apn.finance.web.rest.dto.StartTerminationInputDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.util.*;

/**
 * Service Implementation for managing StartTermination.
 */
@Slf4j
@Service
@Transactional
public class StartTerminationServiceImpl implements StartTerminationService {

    @Resource
    private StartTerminationRepository startTerminationRepository;

    @Resource
    private StartService startService;

    @Resource
    private StartContractRateService startContractRateService;

    @Resource
    private StartRepository startRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Resource
    private TalentService talentService;

    /**
     * Save a startTermination.
     *
     * @param startTermination the entity to save
     * @return the persisted entity
     */
    @Override
    public StartDTO save(StartTerminationInputDTO startTerminationInput) throws IOException {
        if (startTerminationInput.getStartId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_TERMINATIONSAVE_STARTIDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        if (startTerminationInput.getTerminationDate() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_TERMINATIONSAVE_DATEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        Start start = startService.findById(startTerminationInput.getStartId());
        if (!Constants.CONTRACT_AND_PAYROLL_JOB.contains(start.getPositionType())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_TERMINATIONSAVE_JOBTYPEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        StartTermination startTermination = new StartTermination();
        BeanUtil.copyProperties(startTerminationInput, startTermination);
        startTermination.setStartEndDate(start.getEndDate());
        startTermination.setStartStatus(start.getStatus());
        startTermination.setNote(startTermination.getNote());
        StartTermination termination = startTerminationRepository.save(startTermination);

        start.setStatus(StartStatus.CONTRACT_TERMINATED);
        start.setEndDate(startTermination.getTerminationDate());
        startService.saveOnly(start);
        List<StartContractRate> startContractRates = recalculateTotalAmount(startTermination.getTerminationDate(), start.getId());
        deleteExtensionStartAfterTerminationEndDateIfExists(start, startTermination);
        //StartDTO startDTO = startService.toDto(start.getId());
        StartDTO startDTO = startService.fillDto(start, new StartDTO().setTermination(termination).setStartContractRates(startContractRates));
        //startDTO.setTermination(termination);
        this.updateTalentExperienceWithEndDate(startDTO, startTermination);
        return startDTO;
    }

    private void updateTalentExperienceWithEndDate(StartDTO start, StartTermination startTermination){
        if (startTermination.getReason().equals(StartTerminationReason.CONVERTED_TO_FTE)){
            return;
        }
        TalentExperienceDTO talentExperienceDTO = new TalentExperienceDTO()
                .setTalentRecruitmentProcessId(start.getTalentRecruitmentProcessId())
                .setLocation(Objects.nonNull(start.getStartAddress()) ? start.getStartAddress().getOriginDisplay() : null)
                .setStartDate(start.getStartDate())
                .setEndDate(startTermination.getTerminationDate())
                .setCompanyId(start.getCompanyId())
                .setCompanyName(start.getCompany())
                .setTitle(start.getJobTitle())
                .setCurrent(null);
        talentService.updateTalentExperience(start.getTalentId(), talentExperienceDTO);
    }

    private void updateTalentExperienceWithoutEndDate(StartDTO start, StartTermination startTermination){
        if (startTermination.getReason().equals(StartTerminationReason.CONVERTED_TO_FTE)){
            return;
        }
        TalentExperienceDTO talentExperienceDTO = new TalentExperienceDTO()
                .setTalentRecruitmentProcessId(start.getTalentRecruitmentProcessId())
                .setLocation(Objects.nonNull(start.getStartAddress()) ? start.getStartAddress().getOriginDisplay() : null)
                .setStartDate(start.getStartDate())
                .setCompanyId(start.getCompanyId())
                .setCompanyName(start.getCompany())
                .setTitle(start.getJobTitle())
                .setCurrent(Boolean.TRUE);
        talentService.updateTalentExperience(start.getTalentId(), talentExperienceDTO);
    }

    private void deleteExtensionStartAfterTerminationEndDateIfExists(Start start, StartTermination startTermination) {
        List<Start> starts = startRepository.findAllByTalentIdAndJobIdAndStartTypeAndStartDateGreaterThanEqual(start.getTalentId(), start.getJobId(), StartType.CONTRACT_EXTENSION, startTermination.getTerminationDate());
        if (CollectionUtils.isNotEmpty(starts)) {
            List<Start> deleteStart = new ArrayList<>();
            starts.forEach(s -> {
                if (!s.getId().equals(startTermination.getStartId())) {
                    deleteStart.add(s);
                }
            });
            if (!deleteStart.isEmpty()) {
                startRepository.deleteAll(deleteStart);
            }
        }
    }

    private List<StartContractRate> recalculateTotalAmount(LocalDate endDate, Long startId) {
        StartContractRate lastRate = startContractRateService.findLastByStartId(startId);
        if (lastRate != null) {
            lastRate.setEndDate(endDate);
            lastRate.setTotalBillAmount(startContractRateService.getTotalBillAmount(lastRate));
            startContractRateService.save(lastRate);
        }
        return startContractRateService.findAllByStartId(startId);
    }

    @Override
    public StartDTO delete(Long startId) throws IOException {
        StartTermination startTermination = startTerminationRepository.findByStartId(startId);
        Start start = startService.findById(startId);
//        validateExistsStart(start.getTalentId());
        start.setEndDate(startTermination.getStartEndDate());
        //start.setStatus(startTermination.getStartStatus());
        start.setStatus(StartStatus.ACTIVE);
        startService.saveOnly(start);
        List<StartContractRate> startContractRates = recalculateTotalAmount(startTermination.getStartEndDate(), start.getId());
        startTerminationRepository.delete(startTermination);
        //StartDTO startDTO = startService.toDto(startId);
        StartDTO startDTO = startService.fillDto(start, new StartDTO().setTermination(new StartTermination()).setStartContractRates(startContractRates));
        startDTO.setTermination(null);
        this.updateTalentExperienceWithoutEndDate(startDTO, startTermination);
        return startDTO;
    }

    private void validateExistsStart(Long talentId) {
        Start activeStart = startRepository.findFirstByTalentIdAndStatusOrderByStartDateDesc(talentId, StartStatus.ACTIVE);
        if (activeStart != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATEEXISTSSTART_STARTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(activeStart.getJobId(),activeStart.getJobTitle()),financeApiPromptProperties.getFinanceService()));
        }
    }

    @Override
    public Set<Long> findTerminatedApplicationIdsByTalentId(Long talentId) {
        return startTerminationRepository.findTerminatedApplicationIdsByTalentIdAndExcludeReason(talentId, StartTerminationReason.CONVERTED_TO_FTE);
    }
}
