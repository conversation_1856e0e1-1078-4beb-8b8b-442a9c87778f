package com.altomni.apn.finance.web.rest.start;

import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.domain.start.StartFteRate;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.start.StartExtensionService;
import com.altomni.apn.finance.service.start.StartService;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * REST controller for managing StartExtensionResource.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StartExtensionResource {

    private static final String ENTITY_NAME = "Start";

    @Resource
    private StartExtensionService startExtensionService;

    @Resource
    private StartService startService;


    /**
     * POST  /starts : Create a start extension.
     *
     * @param start the start to create
     * @return the ResponseEntity with status 201 (Created) and with body the new start, or with status 400 (Bad Request) if the start has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/starts/extension/{startId}")
    @Timed
    public ResponseEntity<StartDTO> createStartExtension(@PathVariable Long startId, @Valid @RequestBody StartDTO start) throws URISyntaxException, IOException {
        log.info("[APN: StartExtension @{}] REST request to create start extension. : {}", SecurityUtils.getUserId(), start);
        if (start.getId() != null) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(ENTITY_NAME, "id exists", "A new start cannot already have an ID")).body(null);
        }
        checkRate(start);
        startService.checkPermissionByStartId(startId);
        StartDTO result = startExtensionService.createStartExtension(startId, start);
        return ResponseEntity.created(new URI("/api/v3/starts/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, String.valueOf(startId)))
            .body(result);
    }

    private void checkRate(StartDTO start) {
        if (null != start) {
            if (null != start.getStartFteRate()) {
                if (null != start.getStartFteRate().getSalaryPackages() && !start.getStartFteRate().getSalaryPackages().isEmpty()) {
                    start.getStartFteRate().getSalaryPackages().forEach(v -> {
                        if (v.getAmount().compareTo(new BigDecimal("99999999")) > 0) {
                            throw new CustomParameterizedException("The value cannot exceed 99999999");
                        }
                    });
                }
            }

            if (null != start.getStartContractRates() && !start.getStartContractRates().isEmpty() ) {
                start.getStartContractRates().forEach(v -> {
                    if (v.getFinalBillRate().compareTo(new BigDecimal("99999999")) > 0) {
                        throw new CustomParameterizedException("The value cannot exceed 99999999");
                    }
                    if (v.getFinalPayRate().compareTo(new BigDecimal("99999999")) > 0) {
                        throw new CustomParameterizedException("The value cannot exceed 99999999");
                    }
                });
            }
        }
    }

    @PutMapping("/starts/extension/{startId}")
    @Timed
    public ResponseEntity<StartDTO> updateStartExtension(@PathVariable Long startId, @Valid @RequestBody StartDTO start) throws IOException {
        log.info("[APN: StartExtension @{}] REST request to update start extension : {}", SecurityUtils.getUserId(), start);
        if (start.getId() == null) {
            start.setId(startId);
        }
        checkRate(start);
        StartDTO result = startExtensionService.updateStartExtension(start);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, start.getId().toString()))
            .body(result);
    }
}
