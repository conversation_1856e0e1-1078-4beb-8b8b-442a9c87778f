package com.altomni.apn.finance.repository.start;

import com.altomni.apn.finance.domain.start.QStartCommission;
import com.altomni.apn.finance.domain.start.StartCommission;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the StartCommission entity.
 */
@SuppressWarnings("unused")
@Repository
public interface StartCommissionRepository extends JpaRepository<StartCommission,Long> , QuerydslPredicateExecutor<StartCommission>, QuerydslBinderCustomizer<QStartCommission> {

    @Override
    default public void customize(QuerydslBindings bindings, QStartCommission root) {
        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    }

    List<StartCommission> findByStartId(Long startId);

    @Modifying
    @Query(value = "delete from start_commission where start_id = ?1", nativeQuery = true)
    void deleteByByStartId(Long startId);

    List<StartCommission> findAllByStartIdIn(List<Long> startIds);
}
