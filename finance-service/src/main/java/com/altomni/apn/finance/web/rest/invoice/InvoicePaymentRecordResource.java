package com.altomni.apn.finance.web.rest.invoice;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.domain.invoice.InvoicePaymentRecord;
import com.altomni.apn.finance.service.dto.invoice.InvoiceActivityDTO;
import com.altomni.apn.finance.service.dto.invoice.InvoicePaymentRecordDTO;
import com.altomni.apn.finance.service.invoice.InvoicePaymentRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing {@link InvoicePaymentRecord}.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
@Transactional
public class InvoicePaymentRecordResource {

    private static final String ENTITY_NAME = "invoicePaymentRecord";

    @Resource
    private InvoicePaymentRecordService invoicePaymentRecordService;


    /**
     * {@code POST  /invoice-payment-records} : Create a new invoicePaymentRecord.
     *
     * @param invoicePaymentRecordDTO the invoicePaymentRecord to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new invoicePaymentRecord, or with status {@code 400 (Bad Request)} if the invoicePaymentRecord has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("/invoice-payment-records")
    @NoRepeatSubmit
    public ResponseEntity<InvoiceActivityDTO> createInvoicePaymentRecord(@RequestBody InvoicePaymentRecordDTO invoicePaymentRecordDTO) throws URISyntaxException {
        log.info("[APN: InvoicePaymentRecord @{}] REST request to save InvoicePaymentRecord : {}", SecurityUtils.getUserId(), invoicePaymentRecordDTO);
        if (invoicePaymentRecordDTO.getId() != null) {
            throw new CustomParameterizedException("A new invoicePaymentRecord cannot already have an ID", ENTITY_NAME, "idexists");
        }
        InvoiceActivityDTO result = invoicePaymentRecordService.create(invoicePaymentRecordDTO);
        return ResponseEntity.created(new URI("/api/invoice-payment-records/" + result.getInvoiceId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getInvoiceId().toString()))
                .body(result);
    }

    /**
     * get InvoicePaymentRecords by invoice id
     * @param id
     * @return
     * @throws URISyntaxException
     */
    @GetMapping("/invoice-payment-records/invoice/{id}")
    public ResponseEntity<List<InvoicePaymentRecordDTO>> findByInvoiceId(@PathVariable @NotNull Long id) throws URISyntaxException {
        log.info("[APN: InvoicePaymentRecord @{}] REST request to get InvoicePaymentRecords by invoice id : {}", SecurityUtils.getUserId(), id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(invoicePaymentRecordService.findByInvoiceId(id)));
    }

    /**
     * unRecord payment by id
     * @param id
     * @return
     * @throws URISyntaxException
     */
    @GetMapping("/invoice-payment-records/unrecord-payment/{id}")
    @NoRepeatSubmit
    public ResponseEntity<InvoiceActivityDTO> unRecord(@PathVariable @NotNull Long id) throws URISyntaxException {
        log.info("[APN: InvoicePaymentRecord @{}] REST request to unRecord payment by payment id : {}", SecurityUtils.getUserId(), id);
        InvoiceActivityDTO result = invoicePaymentRecordService.unRecordPaymentById(id);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

}
