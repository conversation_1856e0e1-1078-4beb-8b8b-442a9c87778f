package com.altomni.apn.finance.service.dto.invoice;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoicePaymentMethod;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * A InvoicePaymentRecordDTO.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InvoicePaymentRecordDTO extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5880564893352238753L;

    private Long id;

    @ApiModelProperty(value = "Invoice id")
    private Long invoiceId;

    @ApiModelProperty(value = "The date of received payment.")
    private LocalDate paymentDate;

    @ApiModelProperty(value = "The amount of received from client. It should be equals with due amount.")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "Close or not this payment without a full payment. ")
    private Boolean closeWithoutFullPayment = Boolean.FALSE;

    @ApiModelProperty(value = "Amount payment method.")
    private InvoicePaymentMethod paymentMethod;

    @Column(name = "note")
    private String note;

    @ApiModelProperty(value = "Is this client already paid startup fee or not")
    private Boolean paidStartupFee;

    @ApiModelProperty(value = "Startup fee invoice date")
    private LocalDate startupFeeDate;

    @ApiModelProperty(value = "Startup fee invoice amount")
    private BigDecimal startupFeeAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "Startup fee invoice number")
    private String startupFeeInvoiceNo;

    @ApiModelProperty(value = "Apply credit")
    private BigDecimal applyCredit = BigDecimal.ZERO;

    @ApiModelProperty(value = "Currency of payment")
    private Integer currency;

    @ApiModelProperty(value = "Conversion rate between collection currency and invoice currency. " +
            "eg: if collection currency == USD,  invoice currency == CAD ,  CAD:USD = 1.35:1" +
            "exchangeRate = invoice currency : collection currency = 1.35:1")
    private String exchangeRate;

    public BigDecimal getPaidAmount() {
        return paidAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(paidAmount));
    }

    public BigDecimal getStartupFeeAmount() {
        return startupFeeAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(startupFeeAmount));
    }

    public BigDecimal getApplyCredit() {
        return applyCredit == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(applyCredit));
    }

}
