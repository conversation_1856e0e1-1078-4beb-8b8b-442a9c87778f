package com.altomni.apn.finance.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class StartTerminationConvertToFteFeeStatusConverter extends AbstractAttributeConverter<StartTerminationConvertToFteFeeStatus, Integer> {
    public StartTerminationConvertToFteFeeStatusConverter() {
        super(StartTerminationConvertToFteFeeStatus::toDbValue, StartTerminationConvertToFteFeeStatus::fromDbValue);
    }
}
