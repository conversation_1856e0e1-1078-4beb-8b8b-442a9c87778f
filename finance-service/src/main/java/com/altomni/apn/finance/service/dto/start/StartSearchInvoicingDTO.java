package com.altomni.apn.finance.service.dto.start;


import com.altomni.apn.finance.domain.start.Start;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class StartSearchInvoicingDTO implements Serializable {

    private static final long serialVersionUID = 4407706925970162814L;

    private Long startId;

    private Long companyId;

    private String companyName;

    private Long jobId;

    private String jobName;

    private Long talentId;

    private String talentName;

    private String gpAmount;

    private boolean contractStatus;

    private boolean relationSalesLead;

    private boolean isChinaOrder;

    private Integer currency;

    private Boolean jobPermission;

    private Boolean privateJob;

    private String dueAmount;

    private String invoiceType;

    private String jobType;
    public static StartSearchInvoicingDTO fromStart(Start start) {
        StartSearchInvoicingDTO dto = new StartSearchInvoicingDTO();
        dto.setCompanyId(start.getCompanyId());
        dto.setJobId(start.getJobId());
        dto.setJobName(start.getJobTitle());
        dto.setTalentId(start.getTalentId());
        dto.setTalentName(start.getTalentName());
        dto.setStartId(start.getId());
        dto.setCompanyName(start.getCompany());
        dto.setCurrency(start.getCurrency());
        dto.setInvoiceType(start.getNote());
        dto.setDueAmount(start.getTimeZone());
        dto.setJobType(start.getPositionType().getName());
        return dto;
    }
}
