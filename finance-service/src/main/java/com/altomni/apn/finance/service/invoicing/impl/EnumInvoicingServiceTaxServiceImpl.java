package com.altomni.apn.finance.service.invoicing.impl;

import com.altomni.apn.company.domain.company.CompanyClientInfo;
import com.altomni.apn.company.repository.company.CompanyClientInfoRepository;
import com.altomni.apn.finance.domain.invoicing.EnumInvoicingServiceTax;
import com.altomni.apn.finance.domain.start.StartClientInfo;
import com.altomni.apn.finance.repository.invoicing.EnumInvoicingServiceTaxRepository;
import com.altomni.apn.finance.repository.start.StartClientInfoRepository;
import com.altomni.apn.finance.service.dto.start.StartClientInfoCompanyDTO;
import com.altomni.apn.finance.service.invoicing.EnumInvoicingServiceTaxService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.List;

@Service
@Slf4j
public class EnumInvoicingServiceTaxServiceImpl implements EnumInvoicingServiceTaxService {

    @Resource
    EnumInvoicingServiceTaxRepository enumInvoicingServiceTaxRepository;

    @Resource
    StartClientInfoRepository startClientInfoRepository;

    @Resource
    CompanyClientInfoRepository companyClientInfoRepository;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Resource
    private EntityManager entityManager;


    @Override
    public List<EnumInvoicingServiceTax> findAll() {
        return enumInvoicingServiceTaxRepository.findAll();
    }

    @Override
    public void clientInfoSync() {

        Integer breakCount = 1;
        Integer i = 0;
        while (breakCount < 186) {

            List<StartClientInfoCompanyDTO> startClientInfoList = findAllLimit(i);
            if (startClientInfoList.isEmpty()) {
                break;
            }

            // 手动控制事务
            DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
            TransactionStatus status = transactionManager.getTransaction(definition);
            try {
                for (StartClientInfoCompanyDTO s : startClientInfoList) {
                    if (null != s.getId() && null != s.getCompanyId() &&
                            StringUtils.isNotBlank(s.getClientName()) &&
                            StringUtils.isNotBlank(s.getClientAddress()) &&
                            StringUtils.isNotBlank(s.getClientDivision())) {
                        List<CompanyClientInfo> infos = companyClientInfoRepository.findAllByCompanyIdAndClientNameAndClientAddressAndClientDivision(s.getCompanyId().longValue(), s.getClientName(), s.getClientAddress(), s.getClientDivision());
                        if (!infos.isEmpty()) {
                            startClientInfoRepository.updateClientInfoById(s.getId().longValue(), infos.get(0).getId());
                        }
                    } else if (null != s.getId() && null != s.getCompanyId() &&
                            StringUtils.isNotBlank(s.getClientName()) &&
                            StringUtils.isNotBlank(s.getClientAddress())) {
                        List<CompanyClientInfo> infos = companyClientInfoRepository.findAllByCompanyIdAndClientNameAndClientAddress(s.getCompanyId().longValue(), s.getClientName(), s.getClientAddress());
                        if (!infos.isEmpty()) {
                            startClientInfoRepository.updateClientInfoById(s.getId().longValue(), infos.get(0).getId());
                        }
                    } else if (null != s.getId() && null != s.getCompanyId() &&
                            StringUtils.isNotBlank(s.getClientName())) {
                        List<CompanyClientInfo> infos = companyClientInfoRepository.findAllByCompanyIdAndClientName(s.getCompanyId().longValue(), s.getClientName());
                        if (!infos.isEmpty()) {
                            startClientInfoRepository.updateClientInfoById(s.getId().longValue(), infos.get(0).getId());
                        }
                    }
                    i = s.getId().intValue();
                }
                breakCount++;
                transactionManager.commit(status);
            } catch (Exception e) {
                breakCount++;
                transactionManager.rollback(status);
                throw e;
            }
        }
    }

    private List<StartClientInfoCompanyDTO> findAllLimit(Integer size) {
        String sql = """
                SELECT sci.id,sci.client_name as clientName, sci.client_address as clientAddress,
                sci.client_division as clientDivision, s.company_id as companyId,s.tenant_id as tenantId\s
                FROM start_client_info sci
                left join start s on s.id = sci.start_id
                where client_info_id is null\s
                and client_name is not null\s
                and company_id is not null\s
                and sci.id > :size
                order by sci.id asc limit 20
                """;

        Query dataQuery = entityManager.createNativeQuery(sql.toString());
        dataQuery.setParameter("size", size);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(StartClientInfoCompanyDTO.class));

        return dataQuery.getResultList();
    }
}
