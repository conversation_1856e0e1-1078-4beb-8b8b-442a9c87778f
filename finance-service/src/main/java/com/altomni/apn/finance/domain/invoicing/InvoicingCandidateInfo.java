package com.altomni.apn.finance.domain.invoicing;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

@Data
@Entity
@Table(name = "invoicing_candidate_info")
public class InvoicingCandidateInfo extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    /**  */
    @Column(name = "talent_name")
    private String talentName ;

    /**  */
    @Column(name = "company_id")
    private Long companyId ;

    @Column(name = "company_name")
    private String companyName ;

    /**  */
    @Column(name = "job_id")
    private Long jobId ;

    /**  */
    @Column(name = "job_name")
    private String jobName ;

    /**  */
    @Column(name = "start_id")
    private Long startId ;

    /** 应收金额 */
    @Column(name = "gp_amount")
    private BigDecimal gpAmount ;

    /** 实收百分比 */
    @Column(name = "amount_percentage")
    private BigDecimal amountPercentage ;

    /** 实收金额 */
    @Column(name = "amount_received")
    private BigDecimal amountReceived ;

    /** 实收金额 */
    @Column(name = "amount_received_tax")
    private BigDecimal amountReceivedTax ;

    /** 预付金金额 */
    @Column(name = "amount_prepayment")
    private BigDecimal amountPrepayment ;

    /** 是否为中国区成单，1、中国区 0、海外区; 注意，此值不代表此单是否为中国区发票,仅表示start页面候选人工作地址是否为中国区or海外区 */
    @Column(name = "invoice_area")
    private Integer invoiceArea ;

    /** 开票申请id */
    @Column(name = "invoice_application_id")
    private Long invoiceApplicationId ;

    @Column(name = "status")
    private Integer status ;

    /** 预期开票日期 */
    @Column(name = "expected_invoicing_date")
    private Instant expectedInvoicingDate ;

    /** 预期开票金额 */
    @Column(name = "expected_invoicing_amount")
    private BigDecimal expectedInvoicingAmount ;
}
