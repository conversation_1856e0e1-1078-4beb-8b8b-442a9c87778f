package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.finance.domain.invoicing.InvoicingApplicationInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface InvoicingApplicationInfoRepository extends JpaRepository<InvoicingApplicationInfo, Long> {

    @Query(value = "select * from invoicing_application_info where company_id=?1 and status=?2 \n" +
            " and invoicing_status in(?3) and invoicing_application_type=?4 and tenant_id=?5 and invoicing_business_type=?6 and amount_due>0 and void_invoicing is null", nativeQuery = true)
    List<InvoicingApplicationInfo> findByCompanyId(Long companyId, Integer status, List<Integer> invoicingStatus, Integer InvoicingApplicationType, Long tenantId,Integer invoicingBusinessType);

    @Query(value = "select * from invoicing_application_info where company_id=?1 and status=?2 \n" +
            "and invoicing_application_type=?4 and tenant_id=?5 and prepayment_type=?5", nativeQuery = true)
    List<InvoicingApplicationInfo> findByCompanyIdAndPrepaymentType(Long companyId, Integer status, Integer InvoicingApplicationType, Long tenantId, Integer prepaymentType);

    List<InvoicingApplicationInfo> findByIdIn(List<Long> ids);

    InvoicingApplicationInfo findByElectronicInvoiceNumberAndStatus(String electronicInvoiceNumber,Integer status);

    List<InvoicingApplicationInfo> findByInvalidInvoicingIdAndStatus(Long invalidInvoicingId,Integer status);

    @Query(value = "select t.* from invoicing_application_info t \n" +
            "where invoicing_status in(?2) and invoicing_application_type in(?1) and status=?3 and DATE_FORMAT(CONVERT_TZ(payment_due_date,'+00:00','+08:00'),'%Y-%m-%d') < DATE_FORMAT(now(),'%Y-%m-%d')", nativeQuery = true)
    List<InvoicingApplicationInfo> findByInvoicingApplicationTypeInAndInvoicingStatusInAndStatus(List<Integer> invoicingApplicationTypes, List<Integer> invoicingStatus, Integer status);

    @Modifying
    @Transactional
    @Query(value = "update invoicing_application_info set status=0 where id in(?1)", nativeQuery = true)
    void updateStatusById(List<Long> id);

    @Modifying
    @Transactional
    @Query(value = "update invoicing_application_info set invoicing_status=?2,last_modified_date=now() where id in(?1)", nativeQuery = true)
    void updateInvoicingStatusById(List<Long> id, Integer invoicingStatus );

    @Query(value = """
        select distinct s.talent_recruitment_process_id
        FROM invoicing_application_info iai
        LEFT JOIN invoicing_candidate_info ici ON iai.id = ici.invoice_application_id
        LEFT JOIN start s ON ici.start_id = s.id
        where iai.id = ?1
""", nativeQuery = true)
    List<Long> findTalentRecruitmentProcessByInvoicingId(Long id);

    List<InvoicingApplicationInfo> findByTenantId(Long tenantId);
}
