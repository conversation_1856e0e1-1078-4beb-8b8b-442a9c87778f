package com.altomni.apn.finance.domain.start;


import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateVO;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "start_contract_rate")
public class StartContractRate extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 8847201502936516014L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to.")
    @Column(name = "tenant_id")
    private Long tenantId;

    @NotNull
    @Column(name = "start_id")
    private Long startId;

    @JsonIgnore
    @ApiModelProperty(value = "The rate that the current rate extend from.")
    @Column(name = "extend_start_contract_rate_id")
    private Long extendStartContractRateId;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Column(name = "currency")
    private Integer currency = CurrencyConstants.USD;

    @Convert(converter = RateUnitTypeConverter.class)
    @Column(name = "rate_unit_type")
    private RateUnitType rateUnitType;

    @Column(name = "final_bill_rate", precision=10, scale=2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal finalBillRate = BigDecimal.ZERO;

    @Column(name = "final_pay_rate", precision=10, scale=2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal finalPayRate = BigDecimal.ZERO;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    @Column(name = "tax_burden_rate")
    private String taxBurdenRateCode;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    @Column(name = "msp_rate")
    private String mspRateCode;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    @Column(name = "immigration_cost")
    private String immigrationCostCode;

    @Column(name = "extra_cost", precision=10, scale=2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal extraCost = BigDecimal.ZERO;

    @Column(name = "estimated_working_hour_per_week", precision=10, scale=2)
    private BigDecimal estimatedWorkingHourPerWeek = BigDecimal.ZERO;

    @ApiModelProperty(value = "GP")
    @Column(name = "total_bill_amount", precision=20, scale=2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillAmount = BigDecimal.ZERO;

    @Column(name = "note")
    private String note;

    @Transient
    @JsonProperty
    private TalentRecruitmentProcessIpgOfferLetterCostRateVO taxBurdenRate;

    @Transient
    @JsonProperty
    private TalentRecruitmentProcessIpgOfferLetterCostRateVO mspRate;

    @Transient
    @JsonProperty
    private TalentRecruitmentProcessIpgOfferLetterCostRateVO immigrationCost;

    @Transient
    @JsonProperty
    private StartClientInfo startClientInfo;

    @Transient
    @JsonProperty
    private String chargeNumber;

    @Transient
    @JsonProperty
    private String tvcNumber;

    @Transient
    @JsonProperty
    private Boolean corpToCorp;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("endDate"));

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getStartId() {
        return startId;
    }

    public void setStartId(Long startId) {
        this.startId = startId;
    }

    public Long getExtendStartContractRateId() {
        return extendStartContractRateId;
    }

    public void setExtendStartContractRateId(Long extendStartContractRateId) {
        this.extendStartContractRateId = extendStartContractRateId;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public RateUnitType getRateUnitType() {
        return rateUnitType;
    }

    public void setRateUnitType(RateUnitType rateUnitType) {
        this.rateUnitType = rateUnitType;
    }

    public BigDecimal getFinalBillRate() {
        return finalBillRate == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(finalBillRate));
    }

    public void setFinalBillRate(BigDecimal finalBillRate) {
        this.finalBillRate = finalBillRate;
    }

    public BigDecimal getFinalPayRate() {
        return finalPayRate == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(finalPayRate));
    }

    public void setFinalPayRate(BigDecimal finalPayRate) {
        this.finalPayRate = finalPayRate;
    }

    public String getTaxBurdenRateCode() {
        return taxBurdenRateCode;
    }

    public void setTaxBurdenRateCode(String taxBurdenRateCode) {
        this.taxBurdenRateCode = taxBurdenRateCode;
    }

    public String getMspRateCode() {
        return mspRateCode;
    }

    public void setMspRateCode(String mspRateCode) {
        this.mspRateCode = mspRateCode;
    }

    public String getImmigrationCostCode() {
        return immigrationCostCode;
    }

    public void setImmigrationCostCode(String immigrationCostCode) {
        this.immigrationCostCode = immigrationCostCode;
    }

    public BigDecimal getExtraCost() {
        return extraCost == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(extraCost));
    }

    public void setExtraCost(BigDecimal extraCost) {
        this.extraCost = extraCost;
    }

    public BigDecimal getEstimatedWorkingHourPerWeek() {
        return estimatedWorkingHourPerWeek;
    }

    public void setEstimatedWorkingHourPerWeek(BigDecimal estimatedWorkingHourPerWeek) {
        this.estimatedWorkingHourPerWeek = estimatedWorkingHourPerWeek;
    }

    public BigDecimal getTotalBillAmount() {
        return totalBillAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalBillAmount));
    }

    public void setTotalBillAmount(BigDecimal totalBillAmount) {
        this.totalBillAmount = totalBillAmount;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public TalentRecruitmentProcessIpgOfferLetterCostRateVO getTaxBurdenRate() {
        return taxBurdenRate;
    }

    public void setTaxBurdenRate(TalentRecruitmentProcessIpgOfferLetterCostRateVO taxBurdenRate) {
        this.taxBurdenRate = taxBurdenRate;
    }

    public TalentRecruitmentProcessIpgOfferLetterCostRateVO getMspRate() {
        return mspRate;
    }

    public void setMspRate(TalentRecruitmentProcessIpgOfferLetterCostRateVO mspRate) {
        this.mspRate = mspRate;
    }

    public TalentRecruitmentProcessIpgOfferLetterCostRateVO getImmigrationCost() {
        return immigrationCost;
    }

    public void setImmigrationCost(TalentRecruitmentProcessIpgOfferLetterCostRateVO immigrationCost) {
        this.immigrationCost = immigrationCost;
    }

    public String getChargeNumber() {
        return chargeNumber;
    }

    public void setChargeNumber(String chargeNumber) {
        this.chargeNumber = chargeNumber;
    }

    public String getTvcNumber() {
        return tvcNumber;
    }

    public void setTvcNumber(String tvcNumber) {
        this.tvcNumber = tvcNumber;
    }

    public Boolean getCorpToCorp() {
        return corpToCorp;
    }

    public void setCorpToCorp(Boolean corpToCorp) {
        this.corpToCorp = corpToCorp;
    }

    public StartClientInfo getStartClientInfo() {
        return startClientInfo;
    }

    public void setStartClientInfo(StartClientInfo startClientInfo) {
        this.startClientInfo = startClientInfo;
    }

    @Override
    public String toString() {
        return "StartContractRate{" +
                "id=" + id +
                ", tenantId=" + tenantId +
                ", startId=" + startId +
                ", extendStartContractRateId=" + extendStartContractRateId +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", currency=" + currency +
                ", rateUnitType=" + rateUnitType +
                ", finalBillRate=" + finalBillRate +
                ", finalPayRate=" + finalPayRate +
                ", taxBurdenRateCode='" + taxBurdenRateCode + '\'' +
                ", mspRateCode='" + mspRateCode + '\'' +
                ", immigrationCostCode='" + immigrationCostCode + '\'' +
                ", extraCost=" + extraCost +
                ", estimatedWorkingHourPerWeek=" + estimatedWorkingHourPerWeek +
                ", totalBillAmount=" + totalBillAmount +
                ", note='" + note + '\'' +
                ", taxBurdenRate=" + taxBurdenRate +
                ", mspRate=" + mspRate +
                ", immigrationCost=" + immigrationCost +
                '}';
    }
}
