package com.altomni.apn.finance.service.invoice.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.thread.CopyTokenChildThread;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarRelationEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.calendar.CompleteSystemCalendarDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.xxljob.XxlJobInvoiceOverdueDTO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.InvoiceTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.finance.config.env.ApplicationProperties;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityType;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.domain.invoice.InvoiceActivity;
import com.altomni.apn.finance.domain.invoice.InvoiceClientCredit;
import com.altomni.apn.finance.domain.invoice.InvoicePaymentRecord;
import com.altomni.apn.finance.domain.start.Start;
import com.altomni.apn.finance.repository.invoice.InvoiceActivityRepository;
import com.altomni.apn.finance.repository.invoice.InvoiceClientCreditRepository;
import com.altomni.apn.finance.repository.invoice.InvoicePaymentRecordRepository;
import com.altomni.apn.finance.repository.invoice.InvoiceRepository;
import com.altomni.apn.finance.repository.start.StartRepository;
import com.altomni.apn.finance.service.application.ApplicationClient;
import com.altomni.apn.finance.service.application.ApplicationService;
import com.altomni.apn.finance.service.dto.invoice.InvoiceActivityDTO;
import com.altomni.apn.finance.service.dto.invoice.InvoiceClientCreditDTO;
import com.altomni.apn.finance.service.dto.invoice.InvoicePaymentRecordDTO;
import com.altomni.apn.finance.service.invoice.InvoiceClientCreditService;
import com.altomni.apn.finance.service.invoice.InvoicePaymentRecordService;
import com.altomni.apn.finance.service.mail.MailService;
import com.altomni.apn.finance.service.user.UserService;
import com.altomni.apn.user.service.calendar.CalendarService;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
public class InvoicePaymentRecordServiceImpl implements InvoicePaymentRecordService {

    @Resource
    private InvoicePaymentRecordRepository invoicePaymentRecordRepository;
    @Resource
    private InvoiceRepository invoiceRepository;
    @Resource
    private UserService userService;
    @Resource
    private InvoiceActivityRepository invoiceActivityRepository;
    @Resource
    private StartRepository startRepository;
    @Resource
    private InvoiceClientCreditService invoiceClientCreditService;
    @Resource
    private InvoiceClientCreditRepository invoiceClientCreditRepository;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private ApplicationProperties applicationProperties;
    @Resource
    private MailService mailService;
    @Resource
    private EnumCommonService enumCommonService;
    @Resource
    private ApplicationClient applicationClient;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    public InvoicePaymentRecordServiceImpl(InvoicePaymentRecordRepository invoicePaymentRecordRepository, InvoiceRepository invoiceRepository, UserService userService, InvoiceActivityRepository invoiceActivityRepository) {
        this.invoicePaymentRecordRepository = invoicePaymentRecordRepository;
        this.invoiceRepository = invoiceRepository;
        this.userService = userService;
        this.invoiceActivityRepository = invoiceActivityRepository;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InvoiceActivityDTO create(InvoicePaymentRecordDTO invoicePaymentRecordDTO) {
        Invoice invoice = invoiceRepository.findById(invoicePaymentRecordDTO.getInvoiceId()).orElse(null);
        if (invoice == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_PAYMENT_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        if (ObjectUtil.isNull(invoicePaymentRecordDTO.getCurrency())) {
            invoicePaymentRecordDTO.setCurrency(invoice.getCurrency());
        }
        boolean illegalAmount = invoicePaymentRecordDTO.getPaidAmount().compareTo(new BigDecimal("0.00")) <= 0;
        if (!invoicePaymentRecordDTO.getCloseWithoutFullPayment() && illegalAmount) {
            throw new CustomParameterizedException("Paid amount should not be a negative value.");
        }
        //if invoice is FTE
        Start start = new Start();
        if (InvoiceType.FTE.equals(invoice.getInvoiceType())) {
            //set start up fee
            start = startRepository.findById(invoice.getStartId()).orElse(null);
            if (start == null) {
                throw new CustomParameterizedException("Start does not exists by id.");
            }
            if (invoicePaymentRecordDTO.getPaidStartupFee() != null && invoicePaymentRecordDTO.getPaidStartupFee()) {
                Invoice startupInvoice = invoiceRepository.findByInvoiceNoAndInvoiceTypeAndTenantId(invoicePaymentRecordDTO.getStartupFeeInvoiceNo(), InvoiceType.STARTUP_FEE, SecurityUtils.getTenantId());
                if (startupInvoice == null) {
                    throw new CustomParameterizedException("Startup fee invoice does not exists.");
                }
                if (InvoiceStatus.STARTUP_FEE_PAID_USED.equals(startupInvoice.getStatus())) {
                    throw new CustomParameterizedException("Startup fee invoice already used.");
                }
                if (InvoiceStatus.STARTUP_FEE_UNPAID_UNUSED.equals(startupInvoice.getStatus())) {
                    throw new CustomParameterizedException("Startup fee invoice not paid yet.");
                }
                if (!start.getCompanyId().equals(startupInvoice.getCompanyId())) {
                    throw new ForbiddenException("You are not authorized to use this startup fee invoice.");
                }
                invoicePaymentRecordDTO.setStartupFeeAmount(startupInvoice.getDueAmount());
                invoicePaymentRecordDTO.setStartupFeeDate(startupInvoice.getDueDate());
                startupInvoice.setStatus(InvoiceStatus.STARTUP_FEE_PAID_USED);
                invoiceRepository.save(startupInvoice);
                saveInvoiceActivity(invoice.getId(), invoice.getInvoiceNo(), startupInvoice.getDueAmount(), InvoiceActivityType.STARTUP_FEE_USED, invoicePaymentRecordDTO.getNote());
            }
        }

        //check total invoice amount
        List<InvoicePaymentRecord> paymentRecords = invoicePaymentRecordRepository.findByInvoiceIdAndActivated(invoice.getId(), Boolean.TRUE);
        BigDecimal totalPaidAmount = paymentRecords.stream().map(record -> {
            BigDecimal paidAmount = record.getPaidAmount();
            if (ObjectUtil.isNotNull(record.getExchangeRate())) {
                paidAmount = calPaidAmountByExchangeRate(record.getExchangeRate(), record.getPaidAmount(), paidAmount);
            } else {
                if (ObjectUtil.isNotNull(record.getApplyCredit())) {
                    paidAmount.add(record.getApplyCredit());
                }
                if (ObjectUtil.isNotNull(record.getStartupFeeAmount())) {
                    paidAmount.add(record.getStartupFeeAmount());
                }
            }
            return paidAmount;
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        InvoiceStatus invoiceStatus = InvoiceStatus.PAID;
        BigDecimal dueAmount = invoice.getDueAmount()
                .subtract(totalPaidAmount)
                .subtract(invoicePaymentRecordDTO.getApplyCredit())
                .subtract(invoicePaymentRecordDTO.getStartupFeeAmount())
                .setScale(2, RoundingMode.HALF_UP);
        //cal paidAmount
        BigDecimal currentPaidAmount = invoicePaymentRecordDTO.getPaidAmount();
        if (ObjectUtil.isNotNull(invoicePaymentRecordDTO.getExchangeRate())) {
            currentPaidAmount = calPaidAmountByExchangeRate(invoicePaymentRecordDTO.getExchangeRate(), invoicePaymentRecordDTO.getPaidAmount(), currentPaidAmount);
        }
        if (dueAmount.compareTo(currentPaidAmount) > 0 && !invoicePaymentRecordDTO.getCloseWithoutFullPayment()) {
            invoiceStatus = InvoiceStatus.PARTIALLY_PAID;
        } else if (dueAmount.compareTo(currentPaidAmount) < 0) {
            throw new CustomParameterizedException("The input amount should not be greater than the receivable amount.");
        }
        //if invoice is FTE
        if (InvoiceType.FTE.equals(invoice.getInvoiceType())) {
            //set applyCredit
            if (invoicePaymentRecordDTO.getApplyCredit() != null && invoicePaymentRecordDTO.getApplyCredit().compareTo(new BigDecimal("0.00")) > 0) {
                InvoiceClientCreditDTO invoiceClientCreditDTO = new InvoiceClientCreditDTO();
                invoiceClientCreditDTO.setCompanyId(invoice.getCompanyId());
                invoiceClientCreditDTO.setInvoiceId(invoice.getId());
                invoiceClientCreditDTO.setInvoiceNo(invoice.getInvoiceNo());
                invoiceClientCreditDTO.setAmount(invoicePaymentRecordDTO.getApplyCredit());
                invoiceClientCreditDTO.setNote(invoicePaymentRecordDTO.getNote());
                invoiceClientCreditService.deduct(invoice.getCurrency(), invoiceClientCreditDTO);
            }
        }

        if (InvoiceType.STARTUP_FEE == invoice.getInvoiceType()) {
            if (invoice.getStatus() != InvoiceStatus.STARTUP_FEE_PAID_USED) {
                invoice.setStatus(InvoiceStatus.STARTUP_FEE_PAID_UNUSED);
                invoiceRepository.save(invoice);
            }
        } else {
            //if invoiceStatus changed , save invoice
            if (invoice.getStatus() != invoiceStatus) {
                invoice.setStatus(invoiceStatus);
                invoiceRepository.save(invoice);
                if (invoiceStatus == InvoiceStatus.PAID) {
                    applicationClient.deleteInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder()
                            .fteInvoiceIdList(CollUtil.newArrayList(invoice.getId())).build());
                    SecurityContext context = SecurityContextHolder.getContext();
                    CompletableFuture.runAsync(() -> {
                        SecurityContextHolder.setContext(context);
                        notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(invoice.getId(), InvoiceTypeEnum.INVOICE), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
                    });
                }
            }

        }
        InvoicePaymentRecord invoicePaymentRecord = new InvoicePaymentRecord();
        ServiceUtils.myCopyProperties(invoicePaymentRecordDTO, invoicePaymentRecord);

        if (InvoiceType.FTE.equals(invoice.getInvoiceType())) {
            //send payment email
            sendCreatePaymentRemindEmails(invoice.getSubInvoiceNo(), invoicePaymentRecord, start.getTalentRecruitmentProcessId());
        }
        return saveInvoiceActivity(invoicePaymentRecordRepository.save(invoicePaymentRecord), InvoiceActivityType.PAYMENT);
    }

    @Resource
    private CalendarService calendarService;

    private void notifyCompleteSystemCalendar(Long talentRecruitmentProcessId, CalendarRelationEnum calendarRelation, List<CalendarTypeEnum> type) {
        if(talentRecruitmentProcessId == null) {
            return;
        }
        CompleteSystemCalendarDTO dto = new CompleteSystemCalendarDTO();
        dto.setRelationType(calendarRelation);
        dto.setType(type);
        dto.setUniqueReferenceId(talentRecruitmentProcessId);
        calendarService.completeSystemCalendar(dto);
    }

    private BigDecimal calPaidAmountByExchangeRate(String exchangeRate, BigDecimal paidAmount, BigDecimal currentPaidAmount) {
        if (exchangeRate.contains(StrUtil.COLON)) {
            String[] exchangeRates = exchangeRate.split(StrUtil.COLON);
            BigDecimal invoiceRate = new BigDecimal(exchangeRates[0]);
            BigDecimal paidRate = new BigDecimal(exchangeRates[1]);
            currentPaidAmount = paidAmount.multiply(invoiceRate).divide(paidRate, 2, RoundingMode.HALF_UP);
        }
        return currentPaidAmount;
    }

    @Override
    public List<InvoicePaymentRecordDTO> findByInvoiceId(Long id) {
        List<InvoicePaymentRecord> invoicePaymentRecords = invoicePaymentRecordRepository.findByInvoiceIdAndActivated(id, Boolean.TRUE);
        return invoicePaymentRecords.stream().map(record -> {
            InvoicePaymentRecordDTO recordDTO = new InvoicePaymentRecordDTO();
            BeanUtil.copyProperties(record, recordDTO);
            return recordDTO;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InvoiceActivityDTO unRecordPaymentById(Long id) {
        //find activated payment by id
        InvoicePaymentRecord record = invoicePaymentRecordRepository.findByIdAndActivated(id, Boolean.TRUE);
        if (ObjectUtil.isEmpty(record)) {
            throw new CustomParameterizedException("can not find this payment .");
        }
        //reset closeWithoutFullPayment
        List<InvoicePaymentRecord> closeWithoutFullPaymentRecords = invoicePaymentRecordRepository.findAllByInvoiceIdAndCloseWithoutFullPaymentAndActivated(record.getInvoiceId(), Boolean.TRUE, Boolean.TRUE);
        if (CollUtil.isNotEmpty(closeWithoutFullPaymentRecords)) {
            closeWithoutFullPaymentRecords.forEach(closeWithoutFullPaymentRecord -> {
                closeWithoutFullPaymentRecord.setCloseWithoutFullPayment(false);
            });
            invoicePaymentRecordRepository.saveAll(closeWithoutFullPaymentRecords);
        }
        Invoice invoice = invoiceRepository.findById(record.getInvoiceId()).orElse(null);
        InvoiceStatus oldStatus = invoice.getStatus();
        if (invoice == null) {
            throw new CustomParameterizedException("Invoice dose not exist");
        }
        //rollback startupFee
        rollBackStartupFee(record, invoice.getId(), invoice.getInvoiceNo());

        //refund credit & rollback apply a credit
        InvoiceClientCredit exists = invoiceClientCreditRepository.findByCurrencyAndCompanyId(invoice.getCurrency(), invoice.getCompanyId()).orElse(null);
        if (ObjectUtil.isNotNull(exists)) {
            //refund credit
            refundCredit(invoice.getId(), invoice.getInvoiceNo(), record, exists);

            //rollback apply a credit
            InvoiceActivity invoiceActivity = invoiceActivityRepository.findByInvoiceIdAndInvoiceActivityType(invoice.getId(), InvoiceActivityType.CREDIT_APPLY);
            if (ObjectUtil.isNotNull(invoiceActivity)) {
                BigDecimal applyCredit = invoiceActivity.getAmount();
                //if the balance is not enough, return it
                if (applyCredit.compareTo(exists.getBalance()) > 0) {
                    throw new CustomParameterizedException("Credit balance is not enough, Unrecord failed");
                } else {
                    exists.setBalance(exists.getBalance().subtract(applyCredit));
                }
                invoiceClientCreditService.saveInvoiceActivity(new InvoiceClientCreditDTO()
                        .setInvoiceId(invoice.getId())
                        .setInvoiceNo(invoice.getInvoiceNo())
                        .setAmount(applyCredit), InvoiceActivityType.CREDIT_UNAPPLY, null);
            }
            invoiceClientCreditRepository.save(exists);
        }

        //reset InvoiceStatus
        List<InvoicePaymentRecord> paymentRecords = invoicePaymentRecordRepository.findByInvoiceIdAndActivated(invoice.getId(), Boolean.TRUE)
                .stream().filter(paymentRecord -> !paymentRecord.getId().equals(record.getId())).collect(Collectors.toList());

        if (CollUtil.isNotEmpty(paymentRecords)) {
            invoice.setStatus(InvoiceStatus.PARTIALLY_PAID);
        } else {
            invoice.setStatus(InvoiceStatus.UNPAID);
        }

        invoiceRepository.save(invoice);
        //soft delete payment
        invoicePaymentRecordRepository.softDeletePayment(id);

        //send payment email
        sendUnRecordPaymentRemindEmails(invoice.getSubInvoiceNo(), record, invoice.getStartId());
        if (oldStatus == InvoiceStatus.PAID) {
            applicationClient.createInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().fteInvoiceIdList(CollUtil.newArrayList(invoice.getId())).build());
        }
        return saveInvoiceActivity(record, InvoiceActivityType.PAYMENT_UNRECORD);
    }

    private void sendCreatePaymentRemindEmails(String subInvoiceNumber, InvoicePaymentRecord invoicePaymentRecord, Long talentRecruitmentProcessId) {
        //get kpiUsers by talentRecruitmentProcessId
        List<TalentRecruitmentProcessKpiUserVO> kpiUserVOList = applicationService.getKpiUsersByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        List<User> kpiUsers = userService.findALLByIdInAndActivated(kpiUserVOList.stream()
                .map(TalentRecruitmentProcessKpiUserVO::getUserId)
                .distinct()
                .collect(Collectors.toList()));
        String currencySymbol = enumCommonService.findAllEnumCurrency().stream().filter(dict -> dict.getId().equals(invoicePaymentRecord.getCurrency())).findFirst().get().getLabel1();
        String subject = "Payment Recorded: Invoice " + subInvoiceNumber + " has received a payment";
        kpiUsers.forEach(user -> {
            EmailUtil.executorService.execute(new CopyTokenChildThread() {
                @Override
                public void runTask() {
                    StringBuilder sb = new StringBuilder();
                    sb.append("<body>");
                    HtmlUtil.appendParagraphCell(sb, "Dear " + user.getFirstName() + " ,");
                    HtmlUtil.appendParagraphCell(sb, "The invoice " + subInvoiceNumber + " received " + currencySymbol + StrUtil.SPACE + invoicePaymentRecord.getPaidAmount()
                            + " on " + invoicePaymentRecord.getPaymentDate() + " .");
                    sb.append("</body>");
                    mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), Collections.singletonList(user.getEmail()), null, null, subject, sb.toString(), null, true));
                }
            });
        });
    }

    private void sendUnRecordPaymentRemindEmails(String subInvoiceNumber, InvoicePaymentRecord invoicePaymentRecord, Long startId) {
        //get kpiUsers by start id
        Start start = startRepository.findById(startId).orElse(null);
        if (start == null) {
            throw new CustomParameterizedException("Start does not exists by id.");
        }
        List<TalentRecruitmentProcessKpiUserVO> kpiUserVOList = applicationService.getKpiUsersByTalentRecruitmentProcessId(start.getTalentRecruitmentProcessId());
        List<User> kpiUsers = userService.findALLByIdInAndActivated(kpiUserVOList.stream()
                .map(TalentRecruitmentProcessKpiUserVO::getUserId)
                .distinct()
                .collect(Collectors.toList()));
        String currencySymbol = enumCommonService.findAllEnumCurrency().stream().filter(dict -> dict.getId().equals(invoicePaymentRecord.getCurrency())).findFirst().get().getLabel1();
        String subject = "Payment Unrecorded: " + subInvoiceNumber + " has cancelled a payment";
        kpiUsers.forEach(user -> {
            EmailUtil.executorService.execute(new CopyTokenChildThread() {
                @Override
                public void runTask() {
                    StringBuilder sb = new StringBuilder();
                    sb.append("<body>");
                    HtmlUtil.appendParagraphCell(sb, "Dear " + user.getFirstName() + " ,");
                    HtmlUtil.appendParagraphCell(sb, "The payment received " + currencySymbol + StrUtil.SPACE + invoicePaymentRecord.getPaidAmount()
                            + " on " + invoicePaymentRecord.getPaymentDate() + " for invoice " + subInvoiceNumber + " has been withdrawn.");
                    sb.append("</body>");
                    mailService.sendHtmlMail(new MailVM(applicationProperties.getSupportSender(), Collections.singletonList(user.getEmail()), null, null, subject, sb.toString(), null, true));
                }
            });
        });
    }

    private void refundCredit(Long invoiceId, String invoiceNo, InvoicePaymentRecord record, InvoiceClientCredit exists) {
        if (ObjectUtil.isNotNull(record.getApplyCredit()) && record.getApplyCredit().compareTo(new BigDecimal("0.00")) > 0) {
            exists.setBalance(exists.getBalance().add(record.getApplyCredit()));
            invoiceClientCreditService.saveInvoiceActivity(new InvoiceClientCreditDTO()
                    .setInvoiceId(invoiceId)
                    .setInvoiceNo(invoiceNo)
                    .setAmount(record.getApplyCredit()), InvoiceActivityType.CREDIT_REFUND, null);
        }
    }

    private void rollBackStartupFee(InvoicePaymentRecord record, Long invoiceId, String invoiceNo) {
        if (record.getPaidStartupFee()) {
            // here startup fee invoice should always unique. Keep this list method in case for extension future.
            List<Invoice> startupFeeList = invoiceRepository.findByInvoiceNo(record.getStartupFeeInvoiceNo());
            if (CollUtil.isNotEmpty(startupFeeList)) {
                for (Invoice startupFee : startupFeeList) {
                    startupFee.setStatus(InvoiceStatus.STARTUP_FEE_PAID_UNUSED);
                    saveInvoiceActivity(invoiceId, invoiceNo, startupFee.getDueAmount(), InvoiceActivityType.STARTUP_FEE_ROLL_BACK, InvoiceConstants.STARTUP_FEE_ROLLBACK);
                    invoiceRepository.save(startupFee);
                }
            }
        }
    }

    /**
     * save invoice activity
     *
     * @param invoiceId invoiceId
     */
    private InvoiceActivity saveInvoiceActivity(Long invoiceId, String invoiceNo, BigDecimal amount, InvoiceActivityType invoiceActivityType, String note) {
        InvoiceActivity invoiceActivity = new InvoiceActivity();
        invoiceActivity.setInvoiceId(invoiceId);
        invoiceActivity.setInvoiceNo(invoiceNo);
        invoiceActivity.setAmount(amount);
        invoiceActivity.setUserId(SecurityUtils.getUserId());
        invoiceActivity.setUserFullName(userService.getUserFullName(SecurityUtils.getUserId()));
        invoiceActivity.setInvoiceActivityType(invoiceActivityType);
        invoiceActivity.setNote(note);
        return invoiceActivityRepository.save(invoiceActivity);
    }

    private InvoiceActivityDTO saveInvoiceActivity(InvoicePaymentRecord invoicePaymentRecord, InvoiceActivityType invoiceActivityType) {
        InvoiceActivity invoiceActivity = new InvoiceActivity();
        invoiceActivity.setInvoiceId(invoicePaymentRecord.getInvoiceId());
        invoiceActivity.setUserId(SecurityUtils.getUserId());
        invoiceActivity.setUserFullName(userService.getUserFullName(SecurityUtils.getUserId()));
        invoiceActivity.setInvoiceActivityType(invoiceActivityType);
        //如果是UNrecord 不返回note
        if(!InvoiceActivityType.PAYMENT_UNRECORD.equals(invoiceActivityType)) {
            invoiceActivity.setNote(invoicePaymentRecord.getNote());
        }
        invoiceActivity.setPaymentId(invoicePaymentRecord.getId());
        invoiceActivity.setAmount(invoicePaymentRecord.getPaidAmount());
        InvoiceActivityDTO result = InvoiceActivityDTO.fromInvoiceActivity(invoiceActivityRepository.save(invoiceActivity));
        result.setPaymentMethod(invoicePaymentRecord.getPaymentMethod());
        result.setPaymentDate(invoicePaymentRecord.getPaymentDate());
        result.setCloseWithoutFullPayment(invoicePaymentRecord.getCloseWithoutFullPayment());
        result.setCurrency(invoicePaymentRecord.getCurrency());
        result.setExchangeRate(invoicePaymentRecord.getExchangeRate());
        return result;
    }
}
