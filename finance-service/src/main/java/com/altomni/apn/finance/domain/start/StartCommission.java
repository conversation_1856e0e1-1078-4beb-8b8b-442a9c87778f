package com.altomni.apn.finance.domain.start;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.enumeration.user.UserRoleConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "start_commission")
public class StartCommission extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -1535386733969345622L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to.")
    @Column(name = "tenant_id")
    private Long tenantId;

    @NotNull
    @Column(name = "user_id")
    private Long userId;

    @Column(name = "user_full_name")
    private String userFullName;

    @NotNull
    @Convert(converter = UserRoleConverter.class)
    @Column(name = "user_role")
    private UserRole userRole;

    @NotNull
    @Column(name = "percentage")
    private BigDecimal percentage;

    @NotNull
    @Column(name = "start_id")
    private Long startId;

    @Column(name = "country")
    private String country;

    @Column(name = "period_start_date")
    private Instant periodStartDate;

    @Column(name = "period_end_date")
    private Instant periodEndDate;

    public JSONObject toJSON() {
        return new JSONObject()
            .fluentPut("userId", userId)
            .fluentPut("userFullName", userFullName)
            .fluentPut("userRole", userRole)
            .fluentPut("percentage", percentage);
    }

    public static Set<UserRole> allButExcludeOwner = new HashSet<>(Arrays.asList(UserRole.AM, UserRole.CO_AM, UserRole.RECRUITER, UserRole.SOURCER, UserRole.DM, UserRole.AC, UserRole.PR, UserRole.BD_OWNER, UserRole.SALES_LEAD_OWNER));

}
