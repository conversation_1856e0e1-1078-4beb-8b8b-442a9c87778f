package com.altomni.apn.finance.domain.start;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "start_address")
public class StartAddress extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 6779876011284097124L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "start_id")
    private Long startId;

    @Column(name = "address")
    private String address;

    @Column(name = "address_2")
    private String address2;

    @Column(name = "city")
    private String city;

    @Column(name = "city_id")
    private Long cityId;

    @Column(name = "province")
    private String province;

    @Column(name = "country")
    private String country;

    @Column(name = "zipcode")
    private String zipcode;

    @Column(name = "location")
    private String location;

    @Column(name = "original_loc")
    private String originalLoc;

    @Transient
    private List<Double> coordinate;

    @Transient
    private String originDisplay;

//    @Transient
    @Column(name = "address_line")
    private String addressLine;

    @Transient
    private String county;

    @Column(name = "residential_location")
    private Long residentialLocation;

}
