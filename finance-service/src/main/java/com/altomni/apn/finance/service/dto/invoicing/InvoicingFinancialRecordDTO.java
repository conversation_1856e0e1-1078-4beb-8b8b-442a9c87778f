package com.altomni.apn.finance.service.dto.invoicing;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;

@Data
public class InvoicingFinancialRecordDTO {

    private Long invoicingId;

    private BigDecimal invoiceTax;

    private String note;

    private String elecInvoiceNumber;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invoicingDate;

    private Integer dueWithinDays;

    private String invoiceUrl;

}
