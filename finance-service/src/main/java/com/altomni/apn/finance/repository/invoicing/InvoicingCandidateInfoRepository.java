package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.finance.domain.invoicing.InvoicingCandidateInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

@Repository
public interface InvoicingCandidateInfoRepository extends JpaRepository<InvoicingCandidateInfo, Long> {

    @Modifying
    @Transactional
    @Query(value = "update invoicing_candidate_info set status=0 where invoice_application_id = ?1", nativeQuery = true)
    void updateStatusByInvoicingId(Long invoicingId);

    @Modifying
    @Transactional
    @Query(value = "update invoicing_candidate_info set amount_prepayment=0 where invoice_application_id = ?1", nativeQuery = true)
    void updatePrepaymentByInvoicingId(Long invoicingId);

    List<InvoicingCandidateInfo> findByInvoiceApplicationIdAndStatus(Long invoiceApplicationId,Integer status);

    List<InvoicingCandidateInfo> findByInvoiceApplicationIdInAndStatus(List<Long> invoiceApplicationId,Integer status);

    List<InvoicingCandidateInfo> findByIdInAndStatus(List<Long> Ids,Integer status);


    List<InvoicingCandidateInfo> findByStatus(Integer status);

    List<InvoicingCandidateInfo> findAllByStartIdIn(Collection<Long> startIds);
}
