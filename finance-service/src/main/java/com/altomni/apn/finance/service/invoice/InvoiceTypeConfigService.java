package com.altomni.apn.finance.service.invoice;

import com.altomni.apn.finance.service.vo.invoice.InvoiceDetailInfoVO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceTypeConfigVO;

import java.util.List;

public interface InvoiceTypeConfigService {

    List<InvoiceTypeConfigVO> findAllByTenantId();

    List<InvoiceDetailInfoVO> findInvoiceInfoList(Long companyId,Long typeConfigId);

    InvoiceDetailInfoVO findInvoiceInfoByTypeId(Long id,Long invoiceId);


}
