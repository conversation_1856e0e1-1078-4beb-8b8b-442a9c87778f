package com.altomni.apn.finance.service.dto.start;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.WorkingMode;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationVO;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.domain.start.*;
import com.altomni.apn.finance.service.vo.invoice.InvoiceDetailInfoVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
//@JsonInclude(JsonInclude.Include.NON_EMPTY) //绝对不能过滤null的字段，前端某些代码要判断字段存在且为null时，有一些特殊逻辑
@Accessors(chain = true)
public class StartDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4407706925970162814L;

    private Long id;

    private Long tenantId;

    private LocalDate startDate;

    private LocalDate endDate;

    private LocalDate warrantyEndDate;

    private StartStatus status;

    private StartType startType;

    private Long talentId;

    private String talentName;

    private Long jobId;

    private String jobTitle;

    private Long companyId;

    private String company;

    private String companyPhone;

    private Long talentRecruitmentProcessId;

    private Long clientContactId;

    private JobType positionType;

    private String timeZone;

    private String note;

    private Boolean isAm;

    private StartAddress startAddress;

    private StartFteRate startFteRate;

    private StartClientInfo startClientInfo;

    private InvoiceDetailInfoVO invoiceDetailInfo;

    private List<StartContractRate> startContractRates;

    private List<StartCommission> startCommissions;

    private StartFailedWarranty failedWarranty;

    private StartTermination termination;

    private List<TalentRecruitmentProcessKpiUserVO> kpiUsers;

    private String chargeNumber;

    private String tvcNumber;

    private Boolean corpToCorp;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "talentId", "jobId"));

    private String canadaProvinceTax;

    private WorkingMode workingMode;

    private ResumeSourceType channelPlatform;

    private BigDecimal profitSharingRatio;

    private Boolean isSubstituteTalent;

    private Long substituteTalentId;

    private String substituteTalentName;

    private Long relationProcessId;

    private TalentRecruitmentProcessResignationVO resignation;

    /**
     * 当前时间早于start date时，不显示在职状态，该值为false，否则为true
     */
    private boolean employmentStatusVisible;

    public boolean getEmploymentStatusVisible(){
        return Objects.nonNull(startDate) && !startDate.isAfter(LocalDate.now());
    }
}
