package com.altomni.apn.finance.constants;

import java.text.DecimalFormat;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

public final class InvoiceConstants {

    public final static String SEQ_INVOICE_NAME = "seq_invoice_no";

    public final static String SEQ_INVOICE_PREFIX = "A";

    public final static String SEQ_STARTUP_FEE_INVOICE_NAME = "seq_startup_fee_invoice_no";

    public final static String SEQ_STARTUP_FEE_INVOICE_PREFIX = "AS";

    public final static String SEQ_INVOICE_NAME_JPY = "seq_invoice_no_jpy";

    public final static String SEQ_INVOICE_PREFIX_JPY = "JA";

    public final static String SEQ_STARTUP_FEE_INVOICE_NAME_JPY = "seq_startup_fee_invoice_no_jpy";

    public final static String SEQ_STARTUP_FEE_INVOICE_PREFIX_JPY = "JAS";

    public final static Integer MAX_VALUE = 999999;

    public final static Integer INCREMENT = 1;

    public final static String INVOICE_CREATED = "Invoice created";

    public final static String INVOICE_VOIDED = "Invoice voided";

    public final static String INVOICE_PAID = "Invoice paid";

    public final static String STARTUP_FEE_INVOICE_CREATED = "Startup fee invoice created";

    public final static String STARTUP_FEE_USED = "Startup fee used";

    public final static String STARTUP_FEE_ROLLBACK = "Startup fee rollback due to void";

    public final static String CREDIT_APPLY = "Credit apply";

    public final static String CREDIT_DEDUCT = "Credit deduct";

    public final static String CANADA = "canada";

    public final static Integer DEFAULT_PAGE_NUMBER = 1;

    public final static Integer DEFAULT_PAGE_SIZE = 20;

    public final static DecimalFormat DECIMAL_FORMAT = new DecimalFormat("0.00#");

    public final static DecimalFormat DECIMAL_FORMAT_TAX_RATE = new DecimalFormat("0.0000#");
    
    public final static Map<Integer, String> CURRENCY_STAIC_MAP = Collections.unmodifiableMap(new HashMap<>(){{
        put(0, "$");
        put(1, "￥");
        put(2, "€");
        put(3, "C$");
        put(4, "£");
        put(5, "AED");
        put(7, "A$");
        put(9, "SFr");
        put(12, "HK$");
        put(15, "₹");
        put(16, "JP¥");
        put(36, "RM");
        put(23, "NZ$");
        put(24, "₱");
        put(28, "S$");
        put(29, "฿");
        put(31, "NT$");
        put(33,"₫");
        put(54, "R$");
        put(55, "MX$");
        put(56, "SAR");
        put(9999, "$");
    }}) ;

    /**
     *  invoice PDF
     */
    public final static String INTELLIPRO_GROUP_LOGO_TEXT = "Intellipro Group";
    public final static String CUSTOMER_NAME_AND_ADDRESS = "Customer Name and Address";
    public final static String REMIT_TO = "Remit To";
    public final static String PLEASE_MAKE_PAYMENT_INFO = "Please make payment";


}
