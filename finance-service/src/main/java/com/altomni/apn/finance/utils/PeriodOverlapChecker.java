package com.altomni.apn.finance.utils;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

public class PeriodOverlapChecker {

    /**
     * 时间段实体（包含唯一标识）
     */
    public static class Period {
        private final Long id; // 唯一标识
        private final LocalDate start;
        private final LocalDate end;

        public Period(Long id, LocalDate start, LocalDate end) {
            if (start == null || end == null) {
                throw new IllegalArgumentException("起止时间不能为null");
            }
            if (start.isAfter(end)) {
                throw new IllegalArgumentException("开始时间不能晚于结束时间");
            }
            this.id = id;
            this.start = start;
            this.end = end;
        }

        // 省略getter方法

        @Override
        public boolean equals(Object o) {
            if (this == o) return true;
            if (!(o instanceof Period)) return false;
            Period period = (Period) o;
            return id.equals(period.id);
        }

        @Override
        public int hashCode() {
            return Objects.hash(id);
        }
    }

    /**
     * 检测修改时间段后的重叠情况
     * @param existingPeriods 现有时间段集合
     * @param originalPeriod 要修改的原始时间段
     * @param newStart 新时间段的开始时间
     * @param newEnd 新时间段的结束时间
     * @return true表示存在重叠
     */
    public static boolean checkUpdateOverlap(List<Period> existingPeriods,
                                             Period originalPeriod,
                                             LocalDate newStart,
                                             LocalDate newEnd) {
        // 创建修改后的临时集合
        Set<Period> tempPeriods = new HashSet<>(existingPeriods);
        tempPeriods.remove(originalPeriod);
        tempPeriods.add(new Period(originalPeriod.id, newStart, newEnd));

        return hasOverlap(new ArrayList<>(tempPeriods));
    }

    /**
     * 检测时间段列表是否存在重叠（核心方法）
     * @param periods 时间段列表（允许无序）
     * @return true表示存在重叠
     */
    private static boolean hasOverlap(List<Period> periods) {
        if (periods.size() <= 1) return false;

        // 按开始时间排序
        List<Period> sorted = periods.stream()
                .sorted(Comparator.comparing(p -> p.start))
                .collect(Collectors.toList());

        // 检查相邻时间段重叠
        for (int i = 1; i < sorted.size(); i++) {
            Period prev = sorted.get(i - 1);
            Period curr = sorted.get(i);

            if (!prev.end.isBefore(curr.start)) {
                return true;
            }
        }
        return false;
    }
}
