package com.altomni.apn.finance.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.emailService.supportSender}")
    private String supportSender;

    @Value("${application.larkService.callbackUrl}")
    private String larkServiceCallbackUrl;

    @Value("${application.larkService.uninvoiceUrl}")
    private String larkServiceUninvoiceUrl;

    @Value("${application.larkService.overdueInvoiceUrl}")
    private String larkServiceOverdueInvoiceUrl;


    @Value("${application.invoice.viewInvoiceUrl}")
    private String viewInvoiceUrl;

    @Value("${application.invoice.defaultInvoiceUrl}")
    private String defaultInvoiceUrl;

    @Value("${application.invoice.createFteChinaInvoice}")
    private String createFteChinaInvoiceUrl;

    @Value("${application.invoice.fteChinaInvoiceList}")
    private String fteChinaInvoiceListUrl;

}
