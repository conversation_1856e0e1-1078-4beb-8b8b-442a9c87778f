package com.altomni.apn.finance.domain.invoice;

import com.altomni.apn.finance.constants.InvoiceConstants;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.time.LocalDate;

public class SubInvoice {

    private LocalDate invoiceDate;

    private LocalDate dueDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal dueAmount;

    public LocalDate getDueDate() {
        return dueDate;
    }

    public LocalDate getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(LocalDate invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getDueAmount() {
        return dueAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(dueAmount));
    }

    public void setDueAmount(BigDecimal dueAmount) {
        this.dueAmount = dueAmount;
    }

    @Override
    public String toString() {
        return "SubInvoice{" +
                "invoiceDate=" + invoiceDate +
                ", dueDate=" + dueDate +
                ", dueAmount=" + dueAmount +
                '}';
    }
}
