package com.altomni.apn.finance.service.dto.invoice;

import com.altomni.apn.finance.domain.invoice.ViewInvoiceList;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ViewInvoiceListSearchResult implements Serializable {

    private static final long serialVersionUID = 8466222594248724263L;

    private List<InvoiceCurrencyAmount> currencyAmounts = new ArrayList<>();

    private List<ViewInvoiceList> elements = new ArrayList<>();

    public List<InvoiceCurrencyAmount> getCurrencyAmounts() {
        return currencyAmounts;
    }

    public void setCurrencyAmounts(List<InvoiceCurrencyAmount> currencyAmounts) {
        this.currencyAmounts = currencyAmounts;
    }

    public List<ViewInvoiceList> getElements() {
        return elements;
    }

    public void setElements(List<ViewInvoiceList> elements) {
        this.elements = elements;
    }

    @Override
    public String toString() {
        return "InvoiceSearchResult{" +
            "currencyAmounts=" + currencyAmounts +
            ", elements=" + elements +
            '}';
    }
}
