package com.altomni.apn.finance.web.rest.invoicing;

import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.domain.invoicing.EnumInvoicingServiceTax;
import com.altomni.apn.finance.service.dto.start.StartSearchInvoicingDTO;
import com.altomni.apn.finance.service.dto.start.StartSearchSimpleDTO;
import com.altomni.apn.finance.service.invoicing.EnumInvoicingServiceTaxService;
import com.altomni.apn.finance.service.start.StartService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v3")
@Transactional
public class EnumInvoicingServiceTaxResource {

    private final Logger log = LoggerFactory.getLogger(EnumInvoicingServiceTaxResource.class);

    @Resource
    private EnumInvoicingServiceTaxService enumInvoicingServiceTaxService;

    @Resource
    private StartService startService;


    @GetMapping("/invoicing/common/service-tax")
    public ResponseEntity<List<EnumInvoicingServiceTax>> getAllInvoicingServiceTax() {
        log.info("[APN: Invoice @{}] REST request to get all Invoicing service tax", SecurityUtils.getUserId());
        List<EnumInvoicingServiceTax> result = enumInvoicingServiceTaxService.findAll();
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(result));
    }

    @GetMapping("/invoicing/common/talent_name/{talentName}")
    public ResponseEntity<List<StartSearchInvoicingDTO>> getStartsWithFullTimePosition(@PathVariable("talentName") String talentName) {
        log.info("[APN: Start @{}] REST request to search starts by talent name {}", SecurityUtils.getUserId(), talentName);
        return new ResponseEntity<>(startService.searchInvoicingStarts(talentName), HttpStatus.OK);
    }

    @GetMapping("/invoicing/common/starts/{startId}")
    public ResponseEntity<StartSearchInvoicingDTO> getStartsWithId(@PathVariable("startId") Long startId) {
        log.info("[APN: Start @{}] REST request to search starts by id {}", SecurityUtils.getUserId(), startId);
        return new ResponseEntity<>(startService.getStartId(startId), HttpStatus.OK);
    }

    @GetMapping("/invoicing/common/client-sync")
    public ResponseEntity<Void> clientInfoSync() {
        log.info("[APN: Invoice @{}] REST request to get all Invoicing service tax", SecurityUtils.getUserId());
        enumInvoicingServiceTaxService.clientInfoSync();
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
