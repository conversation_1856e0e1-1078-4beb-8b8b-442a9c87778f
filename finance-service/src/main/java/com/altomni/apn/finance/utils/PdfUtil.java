package com.altomni.apn.finance.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.dict.EnumReceivingAccount;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.utils.ChineseCharacterCheckerUtil;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.invoice.InvoicePaymentRecord;
import com.altomni.apn.finance.service.dto.invoice.InvoiceDTO;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.*;

import static com.altomni.apn.common.utils.DateUtil.MM_DD_YYYY;
import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_;
import static com.altomni.apn.finance.constants.InvoiceConstants.*;

public class PdfUtil {

    private final Logger log = LoggerFactory.getLogger(PdfUtil.class);

    private static final Font FONT_BOLD_9 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 9, BaseColor.BLACK);
    private static final Font FONT_BOLD_11 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 11, BaseColor.BLACK);
    private static Font FONT_BOLD_20 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 20, BaseColor.GRAY);
    private static final Font FONT_BOLD_8_BLACK = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 8, BaseColor.BLACK);
    private static final Font FONT_BOLD_8_WHITE = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 8, BaseColor.WHITE);
    private static final Font FONT_REGULAR_8 = FontFactory.getFont(FontFactory.HELVETICA, 8, BaseColor.BLACK);
    //private final Font FONT_CHINESE = FontFactory.getFont(getClass().getClassLoader().getResource("NotoSansCJKsc-Regular.otf").getFile(), BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);
    private final Font FONT_CHINESE = FontFactory.getFont("simsun.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);
    private final Font FONT_CHINESE_BOLD = FontFactory.getFont(
            "simsun.ttf",
            BaseFont.IDENTITY_H,
            BaseFont.EMBEDDED,
            8,
            Font.BOLD // 使用 Font.BOLD 替代 BaseFont.BOLD
    );

    private final Font FONT_ARIAL = FontFactory.getFont("arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);

    private final Font FONT_NOTO_SANS = FontFactory.getFont("NotoSansThai-VariableFont_wdth,wght.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);


    private static final String NEGATIVE = "-";

    private static final String LOGO_PATH = "images/intelliprogroup-logo.png";

    private static final String LOGO_PATH_SLASH = "/images/intelliprogroup-logo.png";

    private static final String VOID_PATH = "images/invoice-void.png";

    private static final String VOID_PATH_SLASH = "/images/invoice-void.png";

    private static Map<String, String> NUMBER_MAP = new HashMap<>();

    static {
        NUMBER_MAP.put("1", "First");
        NUMBER_MAP.put("2", "Second");
        NUMBER_MAP.put("3", "Third");
        NUMBER_MAP.put("4", "Fourth");
        NUMBER_MAP.put("5", "Fifth");
        NUMBER_MAP.put("6", "Sixth");
        NUMBER_MAP.put("7", "Seventh");
        NUMBER_MAP.put("8", "Eighth");
        NUMBER_MAP.put("9", "Ninth");
        NUMBER_MAP.put("10", "Tenth");
    }

    public void createInvoicePdf(Document document, InvoiceDTO invoice, List<InvoicePaymentRecord> records, List<EnumReceivingAccount> receivingAccountList) throws DocumentException {
        //获取当前选择的账户信息
        EnumReceivingAccount receivingAccount = receivingAccountList.stream()
                .filter(ra -> invoice.getReceivingAccountId().equals(ra.getId())).findAny().orElse(null);
        if (ObjectUtil.isNull(receivingAccount)) {
            throw new ForbiddenException("Invoice receiving account does not exists .");
        }
        BigDecimal totalStartUpFeeAmount = records.stream().map(InvoicePaymentRecord::getStartupFeeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalApplyCredit = records.stream().map(InvoicePaymentRecord::getApplyCredit)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //计算table_left、table_right的最大行数, 并组装右侧accountInfo cells
        JSONObject accountInfoObject = JSONUtil.parseObj(receivingAccount.getAccountInfo(), false, true);
        List<PdfPCell> accountCells = new ArrayList<>();
        calMaxRowspanAndInitAccountCells(accountInfoObject, accountCells);

        // 创建一个包含两个列的表格
        PdfPTable mainTable = new PdfPTable(7);
        mainTable.setWidthPercentage(87);
        mainTable.setSpacingBefore(5f);
        mainTable.setSpacingAfter(5f);
        mainTable.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
        mainTable.setHorizontalAlignment(Element.ALIGN_CENTER);

        //PDF 左上table
        PdfPTable tableLeft = new PdfPTable(3);
        tableLeft.setSpacingBefore(5f);
        tableLeft.setSpacingAfter(5f);
        tableLeft.setHorizontalAlignment(Element.ALIGN_LEFT);
        initTableLeftData(invoice, receivingAccount, tableLeft);

        //PDF 右上table
        PdfPTable tableRight = new PdfPTable(4);
        tableRight.setSpacingBefore(5f);
        tableRight.setSpacingAfter(5f);
        tableRight.setHorizontalAlignment(Element.ALIGN_RIGHT);
        initTableRight(invoice, accountCells, tableRight);

        //PDF 下方table
        PdfPTable tableBottom = new PdfPTable(7);
        tableBottom.setWidthPercentage(87);
        tableBottom.setSpacingBefore(5f);
        tableBottom.setSpacingAfter(5f);
        tableBottom.setHorizontalAlignment(Element.ALIGN_CENTER);
        initTableBottom(invoice, tableBottom, totalStartUpFeeAmount, totalApplyCredit);

        //把左右两个table合并成一个, 为内部表格 A 设置列占比（4/7）,为内部表格 B 设置列占比（3/7）
        PdfPCell cellTableLeft = new PdfPCell(tableLeft);
        cellTableLeft.setBorder(PdfPCell.NO_BORDER);
        cellTableLeft.setColspan(4);
        PdfPCell cellTableRight = new PdfPCell(tableRight);
        cellTableRight.setBorder(PdfPCell.NO_BORDER);
        cellTableRight.setColspan(3);
        mainTable.addCell(cellTableLeft);
        mainTable.addCell(cellTableRight);

        // 创建第一页并添加到文档
        document.add(mainTable);
        document.add(tableBottom);

    }

    public void createInvoicePdf2(Document document, InvoiceDTO invoice, List<InvoicePaymentRecord> records, List<EnumReceivingAccount> receivingAccountList) throws DocumentException {
        //获取当前选择的账户信息
        EnumReceivingAccount receivingAccount = receivingAccountList.stream()
                .filter(ra -> invoice.getReceivingAccountId().equals(ra.getId())).findAny().orElse(null);
        if (ObjectUtil.isNull(receivingAccount)) {
            throw new ForbiddenException("Invoice receiving account does not exists .");
        }
        BigDecimal totalStartUpFeeAmount = records.stream().map(InvoicePaymentRecord::getStartupFeeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalApplyCredit = records.stream().map(InvoicePaymentRecord::getApplyCredit)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //计算table_left、table_right的最大行数, 并组装右侧accountInfo cells
        JSONObject accountInfoObject = JSONUtil.parseObj(receivingAccount.getAccountInfo(), false, true);
        List<PdfPCell> accountCells = new ArrayList<>();
        calMaxRowspanAndInitAccountCellsSingapore(accountInfoObject, accountCells);

        // 创建一个包含两个列的表格
        PdfPTable mainTable = new PdfPTable(14);
        mainTable.setWidthPercentage(87);
//        mainTable.setSpacingBefore(5f);
//        mainTable.setSpacingAfter(5f);
        mainTable.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
        mainTable.setHorizontalAlignment(Element.ALIGN_CENTER);

        //PDF 左上table
        PdfPTable tableLeft = new PdfPTable(5);
        tableLeft.setSpacingBefore(5f);
        tableLeft.setSpacingAfter(5f);
        tableLeft.setHorizontalAlignment(Element.ALIGN_LEFT);
        initTableLeftDataSingapore(invoice, receivingAccount, tableLeft, accountInfoObject);

        //PDF 右上table
        PdfPTable tableRight = new PdfPTable(9);
        tableRight.setSpacingBefore(5f);
        tableRight.setSpacingAfter(5f);
        tableRight.setHorizontalAlignment(Element.ALIGN_RIGHT);
        initTableRightSingapore(invoice, accountCells, tableRight);

        //PDF 下方table
        PdfPTable tableBottom = new PdfPTable(14);
        tableBottom.setWidthPercentage(87);
        tableBottom.setSpacingBefore(5f);
        tableBottom.setSpacingAfter(5f);
        tableBottom.setHorizontalAlignment(Element.ALIGN_CENTER);
        initTableBottomSingapore(invoice, tableBottom, totalStartUpFeeAmount, totalApplyCredit);

        //把左右两个table合并成一个, 为内部表格 A 设置列占比（4/7）,为内部表格 B 设置列占比（3/7）
        PdfPCell cellTableLeft = new PdfPCell(tableLeft);
        cellTableLeft.setBorder(PdfPCell.NO_BORDER);
        cellTableLeft.setColspan(8);
        PdfPCell cellTableRight = new PdfPCell(tableRight);
        cellTableRight.setBorder(PdfPCell.NO_BORDER);
        cellTableRight.setColspan(6);
        mainTable.addCell(cellTableLeft);
        mainTable.addCell(cellTableRight);

        // 创建第一页并添加到文档
        document.add(mainTable);
        document.add(tableBottom);

    }


    private void initTableLeftDataSingapore(InvoiceDTO invoice, EnumReceivingAccount receivingAccount, PdfPTable tableLeft, JSONObject accountInfoObject) {
        //根据receivingAccount配置表，如果accountName有值则显示，没有显示logo
        PdfPCell cell;
        if (ObjectUtil.isNull(receivingAccount.getAccountName())) {
            Image img = readImage(LOGO_PATH, LOGO_PATH_SLASH);
            if (img != null) {
                cell = new PdfPCell(img, true);
            } else {
                cell = new PdfPCell(new Phrase(INTELLIPRO_GROUP_LOGO_TEXT));
            }
            cell.setColspan(4);
            cell.setRowspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableLeft.addCell(cell);

            // invoice before space
            cell = new PdfPCell(new Phrase(" "));
            cell.setColspan(1);
            cell.setRowspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableLeft.addCell(cell);
        } else {
            cell = new PdfPCell(new Phrase(receivingAccount.getAccountName(), FONT_BOLD_11));
            cell.setColspan(5);
            cell.setRowspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableLeft.addCell(cell);

            // invoice before space
            cell = new PdfPCell(new Phrase(" "));
            cell.setColspan(5);
            cell.setRowspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableLeft.addCell(cell);
        }
        // Account Address
        cell = new PdfPCell(new Phrase("137 Telok Ayer Street, #08-01 Singapore \n" +
                                       "068602", FONT_BOLD_8_BLACK));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // invoice before space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(8);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // Customer Name and Address
        cell = new PdfPCell(new Phrase(CUSTOMER_NAME_AND_ADDRESS, FONT_BOLD_8_BLACK));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // Customer Name
        cell = new PdfPCell(new Phrase(invoice.getCustomerName(), FONT_CHINESE));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        String address = invoice.getCustomerAddress();
        if (StringUtils.isNotBlank(invoice.getCustomerLocation())) {
            address = invoice.getCustomerAddress() + ", " + invoice.getCustomerLocation();
        }

        // Customer Address
        cell = new PdfPCell(new Paragraph(address, FONT_CHINESE));
        cell.setColspan(15);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

    }


    private void calMaxRowspanAndInitAccountCellsSingapore(JSONObject jsonObject, List<PdfPCell> accountCells) {
        for (String key : jsonObject.keySet()) {
            PdfPCell cell;
            String value = StrUtil.toString(jsonObject.get(key));
            //创建accountInfo cell
            //如果是标题类型
            if (StringUtils.isBlank(value)) {
                if (key.contains(PLEASE_MAKE_PAYMENT_INFO)) {
                    cell = new PdfPCell(new Phrase(key, FONT_REGULAR_8));
                } else {
                    cell = new PdfPCell(new Phrase(key, FONT_BOLD_8_BLACK));
                }
                cell.setColspan(9);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingLeft(-5f);
                accountCells.add(cell);

                //如果是key:value类型
            } else {
                //key cell
                cell = new PdfPCell(new Phrase(key + " " + value, FONT_BOLD_8_BLACK));
                cell.setColspan(9);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                cell.setPaddingLeft(-5f);
                accountCells.add(cell);
            }

        }
    }


    public void createJPYPdf(Document document, InvoiceDTO invoice, List<InvoicePaymentRecord> records, List<EnumReceivingAccount> receivingAccountList, BigDecimal feePercentage) throws DocumentException {
        //获取当前选择的账户信息
        EnumReceivingAccount receivingAccount = receivingAccountList.stream()
                .filter(ra -> invoice.getReceivingAccountId().equals(ra.getId())).findAny().orElse(null);
        if (ObjectUtil.isNull(receivingAccount)) {
            throw new ForbiddenException("Invoice receiving account does not exists .");
        }
        BigDecimal totalStartUpFeeAmount = records.stream().map(InvoicePaymentRecord::getStartupFeeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalApplyCredit = records.stream().map(InvoicePaymentRecord::getApplyCredit)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //计算table_left、table_right的最大行数, 并组装右侧accountInfo cells
        JSONObject accountInfoObject = JSONUtil.parseObj(receivingAccount.getAccountInfo(), false, true);
        List<PdfPCell> accountCells = new ArrayList<>();
        calMaxRowspanAndInitAccountCellsForJPY(accountInfoObject, accountCells);

        // 创建一个包含两个列的表格
        PdfPTable topPart1Table = new PdfPTable(7);
        topPart1Table.setWidthPercentage(87);
        topPart1Table.setSpacingBefore(5f);
        topPart1Table.setSpacingAfter(5f);
        topPart1Table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
        topPart1Table.setHorizontalAlignment(Element.ALIGN_CENTER);

        PdfPTable topPart2Table = new PdfPTable(7);
        topPart2Table.setWidthPercentage(87);
        topPart2Table.setSpacingBefore(5f);
        topPart2Table.setSpacingAfter(5f);
        topPart2Table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
        topPart2Table.setHorizontalAlignment(Element.ALIGN_CENTER);

        PdfPTable topPart3Table = new PdfPTable(7);
        topPart3Table.setWidthPercentage(87);
        topPart3Table.setSpacingBefore(5f);
        topPart3Table.setSpacingAfter(5f);
        topPart3Table.getDefaultCell().setBorder(PdfPCell.NO_BORDER);
        topPart3Table.setHorizontalAlignment(Element.ALIGN_CENTER);

        //PDF 左上第一部分table
        PdfPTable topPart1Left = new PdfPTable(3);
        topPart1Left.setSpacingBefore(5f);
        topPart1Left.setSpacingAfter(5f);
        topPart1Left.setHorizontalAlignment(Element.ALIGN_LEFT);
        initTopPart1LeftForJPY(topPart1Left);

        //PDF 右上第一部分table
        PdfPTable topPart1Right = new PdfPTable(4);
        topPart1Right.setSpacingBefore(5f);
        topPart1Right.setSpacingAfter(5f);
        topPart1Right.setHorizontalAlignment(Element.ALIGN_RIGHT);
        initTopPart1RightForJPY(topPart1Right);

        //PDF 左上第二部分table
        PdfPTable topPart2Left = new PdfPTable(3);
        topPart2Left.setSpacingBefore(5f);
        topPart2Left.setSpacingAfter(5f);
        topPart2Left.setHorizontalAlignment(Element.ALIGN_LEFT);
        initTopPart2LeftForJPY(topPart2Left);

        //PDF 右上第二部分table
        PdfPTable topPart2Right = new PdfPTable(4);
        topPart2Right.setSpacingBefore(5f);
        topPart2Right.setSpacingAfter(5f);
        topPart2Right.setHorizontalAlignment(Element.ALIGN_RIGHT);
        initTopPart2RightForJPY(invoice, topPart2Right);

        //PDF 左上第三部分table
        PdfPTable topPart3Left = new PdfPTable(3);
        topPart3Left.setSpacingBefore(5f);
        topPart3Left.setSpacingAfter(5f);
        topPart3Left.setHorizontalAlignment(Element.ALIGN_LEFT);
        initTopPart3LeftForJPY(invoice, topPart3Left);

        //PDF 右上第三部分table
        PdfPTable topPart3Right = new PdfPTable(4);
        topPart3Right.setSpacingBefore(5f);
        topPart3Right.setSpacingAfter(5f);
        topPart3Right.setHorizontalAlignment(Element.ALIGN_RIGHT);
        initTopPart3RightForJPY(accountCells, topPart3Right);

        //PDF 下方table
        PdfPTable tableBottom = new PdfPTable(7);
        tableBottom.setWidthPercentage(87);
        tableBottom.setSpacingBefore(5f);
        tableBottom.setSpacingAfter(5f);
        tableBottom.setHorizontalAlignment(Element.ALIGN_CENTER);
        initTableBottomForJPY(invoice, tableBottom, totalStartUpFeeAmount, totalApplyCredit, feePercentage);

        //把左右两个table合并成一个, 为内部表格 A 设置列占比（4/7）,为内部表格 B 设置列占比（3/7）
        PdfPCell cellTopPart1Left = new PdfPCell(topPart1Left);
        cellTopPart1Left.setBorder(PdfPCell.NO_BORDER);
        cellTopPart1Left.setColspan(4);
        PdfPCell cellTopPart1Right = new PdfPCell(topPart1Right);
        cellTopPart1Right.setBorder(PdfPCell.NO_BORDER);
        cellTopPart1Right.setColspan(3);

        PdfPCell cellTopPart2Left = new PdfPCell(topPart2Left);
        cellTopPart2Left.setBorder(PdfPCell.NO_BORDER);
        cellTopPart2Left.setColspan(4);
        PdfPCell cellTopPart2Right = new PdfPCell(topPart2Right);
        cellTopPart2Right.setBorder(PdfPCell.NO_BORDER);
        cellTopPart2Right.setColspan(3);

        PdfPCell cellTopPart3Left = new PdfPCell(topPart3Left);
        cellTopPart3Left.setBorder(PdfPCell.NO_BORDER);
        cellTopPart3Left.setColspan(4);
        PdfPCell cellTopPart3Right = new PdfPCell(topPart3Right);
        cellTopPart3Right.setBorder(PdfPCell.NO_BORDER);
        cellTopPart3Right.setColspan(3);

        topPart1Table.addCell(cellTopPart1Left);
        topPart1Table.addCell(cellTopPart1Right);
        topPart2Table.addCell(cellTopPart2Left);
        topPart2Table.addCell(cellTopPart2Right);
        topPart3Table.addCell(cellTopPart3Left);
        topPart3Table.addCell(cellTopPart3Right);

        // 创建第一页并添加到文档
        document.add(topPart1Table);
        document.add(topPart2Table);
        document.add(topPart3Table);
        document.add(tableBottom);

    }

        private void initTableBottom(InvoiceDTO invoice, PdfPTable tableBottom, BigDecimal totalStartUpFeeAmount, BigDecimal totalApplyCredit) {
        PdfPCell cell;
        // before Item Description space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        //Item Description title
        cell = new PdfPCell(new Phrase("Item", FONT_BOLD_8_WHITE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Quantity title
        cell = new PdfPCell(new Phrase("Quantity", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Billing Rate title
        cell = new PdfPCell(new Phrase("Billing Rate", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Unit title
        cell = new PdfPCell(new Phrase("Unit", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Amount Due title
        cell = new PdfPCell(new Phrase("Amount Due", FONT_BOLD_8_WHITE));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // before Item Description value space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        //Item Description value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = new PdfPCell(new Phrase("Startup fee", FONT_BOLD_8_BLACK));
        } else {
            if (ChineseCharacterCheckerUtil.containsChineseCharacters(invoice.getTalentName())) {
                cell = new PdfPCell(new Phrase("Placement fee for " + invoice.getTalentName(), FONT_CHINESE));
            } else {
                cell = new PdfPCell(new Phrase("Placement fee for " + invoice.getTalentName(), FONT_ARIAL));
            }
        }
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Space for Quantity value, unit, billing rate
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Amount Due value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_ARIAL, true);
        } else {
            cell = constructCell(invoice.getCurrency(), invoice.getTotalBillAmount(), FONT_ARIAL, true);
        }
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        cell = new PdfPCell(new Phrase("Start Date: " + DateUtil.generateDateString(invoice.getStartDate()), FONT_REGULAR_8));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // the space behind the start date
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Discount
        if (invoice.getDiscount() != null && invoice.getDiscount().compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase("Discount", FONT_REGULAR_8));
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setPaddingLeft(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // discount amount
            cell = constructCell(invoice.getCurrency(), invoice.getDiscount(), FONT_ARIAL, false);
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
        }
        // Tax
        if (invoice.getTaxAmount() != null && invoice.getTaxAmount().compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase(StringUtils.isBlank(invoice.getCanadaProvinceTaxString()) ? "Tax" : invoice.getCanadaProvinceTaxString(), FONT_REGULAR_8));
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setPaddingLeft(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // tax amount
            cell = constructCell(invoice.getCurrency(), invoice.getTaxAmount(), FONT_ARIAL, true);

            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
        }
        // Startup fee
        if (totalStartUpFeeAmount != null && totalStartUpFeeAmount.compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase("Startup Fee", FONT_REGULAR_8));
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setPaddingLeft(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // Startup fee amount
            cell = constructCell(invoice.getCurrency(), totalStartUpFeeAmount, FONT_ARIAL, false);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
        }
        // Credit
        if (totalApplyCredit != null && totalApplyCredit.compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase("Credit", FONT_REGULAR_8));
            cell.setColspan(6);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setPaddingLeft(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // Credit amount
            cell = constructCell(invoice.getCurrency(), totalApplyCredit, FONT_ARIAL, false);

            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
        }

        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // before total amount above space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.TOP);
        cell.setBorderWidth(1.5F);
        cell.setBorderColor(new BaseColor(238, 238, 238));
        tableBottom.addCell(cell);

        // before total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);
        // total amount title
        cell = new PdfPCell(new Phrase("Total Amount: ", FONT_BOLD_9));
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setRowspan(2);
        cell.setColspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);
        // total amount value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_ARIAL, true);
        } else {
            cell = constructCell(invoice.getCurrency(), invoice.getTotalInvoiceAmount(), FONT_ARIAL, true);
        }
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        if (invoice.getSplit()) {
            // sub invoice total amount space
            cell = new PdfPCell(new Phrase("  "));
            cell.setColspan(4);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // sub invoice total amount title
            cell = new PdfPCell(new Phrase(convertSubInvoiceNo(invoice.getSubInvoiceNo()) + " Amount: ", FONT_BOLD_9));
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setColspan(2);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // sub invoice total amount value
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_ARIAL, true);

            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
        }

        // after total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Payment Due within 30 days
        cell = new PdfPCell(new Phrase("Payment due within " + ChronoUnit.DAYS.between(invoice.getInvoiceDate(), invoice.getDueDate()) + " days", FONT_REGULAR_8));
        cell.setColspan(7);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.TOP);
        cell.setBorderWidth(1.5F);
        cell.setBorderColor(new BaseColor(238, 238, 238));
        tableBottom.addCell(cell);
    }

    private void initTableBottomSingapore(InvoiceDTO invoice, PdfPTable tableBottom, BigDecimal totalStartUpFeeAmount, BigDecimal totalApplyCredit) {
        PdfPCell cell;
        // before Item Description space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(14);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        //Item Description title
        cell = new PdfPCell(new Phrase("Item", FONT_BOLD_8_WHITE));
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Quantity title
        cell = new PdfPCell(new Phrase("Quantity", FONT_BOLD_8_WHITE));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Billing Rate title
        cell = new PdfPCell(new Phrase("Billing Rate", FONT_BOLD_8_WHITE));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Unit title
        cell = new PdfPCell(new Phrase("Unit", FONT_BOLD_8_WHITE));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Amount Due title
        cell = new PdfPCell(new Phrase("Amount Due", FONT_BOLD_8_WHITE));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBackgroundColor(new BaseColor(50, 50, 50));
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // before Item Description value space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(14);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        //Item Description value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = new PdfPCell(new Phrase("Startup fee", FONT_BOLD_8_BLACK));
        } else {
            if (ChineseCharacterCheckerUtil.containsChineseCharacters(invoice.getTalentName())) {
                cell = new PdfPCell(new Phrase("Placement fee for " + invoice.getTalentName(), FONT_CHINESE_BOLD));
            } else {
                cell = new PdfPCell(new Phrase("Placement fee for " + invoice.getTalentName(), FONT_BOLD_8_BLACK));
            }
        }
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Space for Quantity value, unit, billing rate
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Amount Due value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_ARIAL, true);
        } else {
            cell = constructCell(invoice.getCurrency(), invoice.getTotalBillAmount(), FONT_ARIAL, true);
        }
        cell.setColspan(2);
        cell.setPaddingLeft(5f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        cell = new PdfPCell(new Phrase("Start Date: " + DateUtil.generateDateString(invoice.getStartDate()), FONT_BOLD_8_BLACK));
        cell.setColspan(6);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // the space behind the start date
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(8);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Startup fee
        if (totalStartUpFeeAmount != null && totalStartUpFeeAmount.compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase("Startup Fee", FONT_REGULAR_8));
            cell.setColspan(12);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setPaddingLeft(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // Startup fee amount
            cell = constructCell(invoice.getCurrency(), totalStartUpFeeAmount, FONT_ARIAL, false);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
        }
        // Credit
        if (totalApplyCredit != null && totalApplyCredit.compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase("Credit", FONT_REGULAR_8));
            cell.setColspan(12);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setPaddingLeft(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // Credit amount
            cell = constructCell(invoice.getCurrency(), totalApplyCredit, FONT_ARIAL, false);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
        }

        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(14);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // before total amount above space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(14);
        cell.setBorder(PdfPCell.TOP);
        cell.setBorderWidth(1.5F);
        cell.setBorderColor(new BaseColor(238, 238, 238));
        tableBottom.addCell(cell);


        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(8);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        cell = new PdfPCell(new Phrase("Subtotal Amount(" + invoice.getCurrencyName() + "):", FONT_BOLD_9));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setPaddingLeft(10f);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);
        // discount amount
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_BOLD_9, true);
        } else {
            cell = constructCell(invoice.getCurrency(), invoice.getTotalBillAmount(), FONT_BOLD_9, true);
        }
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setColspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);


        // Tax
        if (invoice.getTaxAmount() != null && invoice.getTaxAmount().compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase("  "));
            cell.setColspan(8);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            cell = new PdfPCell(new Phrase(StringUtils.isBlank(invoice.getCanadaProvinceTaxString()) ? "GST @:" + invoice.getTaxRate().multiply(BigDecimal.valueOf(100)).setScale(2) + "%" : invoice.getCanadaProvinceTaxString(), FONT_BOLD_9));
            cell.setColspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setPaddingLeft(10f);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // tax amount
            cell = constructCell(invoice.getCurrency(), invoice.getTaxAmount(), FONT_BOLD_9, true);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
        }
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(14);
        cell.setRowspan(5);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);
        // before total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(4);
        cell.setRowspan(2);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);
        // total amount title
        // total amount value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = constructCellWithPrefix("Total Payable includes GST(" + invoice.getCurrencyName() + "):       ",invoice.getCurrency(), invoice.getDueAmount(), FONT_BOLD_11, true);
        } else {
            cell = constructCellWithPrefix("Total Payable includes GST(" + invoice.getCurrencyName() + "):       ", invoice.getCurrency(), invoice.getTotalInvoiceAmount(), FONT_BOLD_11, true);
        }
        cell.setRowspan(4);
        cell.setColspan(10);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        if (invoice.getSplit()) {
            // sub invoice total amount space
            cell = new PdfPCell(new Phrase("  "));
            cell.setColspan(8);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // sub invoice total amount title
            cell = new PdfPCell(new Phrase(convertSubInvoiceNo(invoice.getSubInvoiceNo()) + " Amount: ", FONT_BOLD_9));
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setColspan(4);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // sub invoice total amount value
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_ARIAL, true);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
        }

        // after total amount space
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(14);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Payment Due within 30 days
        cell = new PdfPCell(new Phrase("Payment due within " + ChronoUnit.DAYS.between(invoice.getInvoiceDate(), invoice.getDueDate()) + " days", FONT_REGULAR_8));
        cell.setColspan(14);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.TOP);
        cell.setBorderWidth(1.5F);
        cell.setBorderColor(new BaseColor(238, 238, 238));
        tableBottom.addCell(cell);
    }


    private void initTableBottomForJPY(InvoiceDTO invoice, PdfPTable tableBottom, BigDecimal totalStartUpFeeAmount, BigDecimal totalApplyCredit, BigDecimal feePercentage) {
        PdfPCell cell;

        //[Item] title
        cell = new PdfPCell(new Phrase("ITEM", FONT_BOLD_8_BLACK));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);

        // [BILLING SALARY] title
        cell = new PdfPCell(new Phrase("BILLING SALARY", FONT_BOLD_8_BLACK));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setColspan(2);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);

        // [BILLING RATE] title
        cell = new PdfPCell(new Phrase("BILLING RATE", FONT_BOLD_8_BLACK));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setColspan(1);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);

        // [OTHERS] title
        cell = new PdfPCell(new Phrase("OTHERS", FONT_BOLD_8_BLACK));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setColspan(1);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);

        // [REMUNERATION] title
        cell = new PdfPCell(new Phrase("REMUNERATION", FONT_BOLD_8_BLACK));
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setColspan(2);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);


        //[Item] value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = new PdfPCell(new Phrase("Startup fee", FONT_ARIAL));
        } else {
            cell = new PdfPCell(new Phrase("Recruitment Fee", FONT_ARIAL));
        }
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);

        // [BILLING SALARY] value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
        } else {
            cell = constructCell(invoice.getCurrency(), invoice.getTotalBillablePackage(), FONT_ARIAL, true);
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);

        // [BILLING RATE] value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
        } else {
            //使用feePercentage
            cell = new PdfPCell(new Phrase(convertToPercentage(feePercentage), FONT_ARIAL));
        }
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);

        // [OTHERS] value
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);

        // [REMUNERATION] value
        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_ARIAL, true);
        } else {
            cell = constructCell(invoice.getCurrency(), invoice.getTotalBillAmount(), FONT_ARIAL, true);
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(195, 195, 195));
        tableBottom.addCell(cell);

        // Fill 6 rows with blanks
        for (int i = 0; i < 6; i++) {
            for (int j = 0; j < 5; j++) {
                cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
                if (j == 1 || j == 4) {
                    cell.setColspan(2);
                } else {
                    cell.setColspan(1);
                }
                cell.setBorderWidth(0.1F);
                cell.setBorderColor(new BaseColor(195, 195, 195));
                tableBottom.addCell(cell);
            }
        }

        // before space
        cell = new PdfPCell(new Phrase(StrUtil.SPACE));
        cell.setColspan(9);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        //右下方小表格
        //[SUBTOTAL] 行
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
        cell.setColspan(3);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        cell = new PdfPCell(new Phrase("SUBTOTAL", FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setPaddingRight(10f);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(218, 218, 218));
        tableBottom.addCell(cell);

        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_ARIAL, true);
        } else {
            cell = constructCell(invoice.getCurrency(), invoice.getTotalBillAmount(), FONT_ARIAL, true);
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(218, 218, 218));
        tableBottom.addCell(cell);

        // Discount
        if (invoice.getDiscount() != null && invoice.getDiscount().compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
            cell.setColspan(3);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);

            cell = new PdfPCell(new Phrase("DISCOUNT", FONT_REGULAR_8));
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            cell.setPaddingRight(10f);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(195, 195, 195));
            tableBottom.addCell(cell);

            cell = constructCell(invoice.getCurrency(), invoice.getDiscount(), FONT_ARIAL, false);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(195, 195, 195));
            tableBottom.addCell(cell);
        }

        //[10% APPLICABLE (EXCL. TAX)] 行
        if (!InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
            cell.setColspan(3);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);

            cell = new PdfPCell(new Phrase(convertToPercentage(invoice.getTaxRate()) + " APPLICABLE (EXCL. TAX)", FONT_ARIAL));
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            cell.setPaddingRight(10f);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(218, 218, 218));
            tableBottom.addCell(cell);

            //cal tax
            if (invoice.getTaxAmount() != null && invoice.getTaxAmount().compareTo(new BigDecimal("0.00")) > 0) {
                cell = constructCell(invoice.getCurrency(), invoice.getTaxAmount(), FONT_ARIAL, true);
            } else {
                cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
            }
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(218, 218, 218));
            tableBottom.addCell(cell);
        }

        // Startup fee 行
        if (totalStartUpFeeAmount != null && totalStartUpFeeAmount.compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
            cell.setColspan(3);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);

            cell = new PdfPCell(new Phrase("STARTUP FEE", FONT_REGULAR_8));
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            cell.setPaddingRight(10f);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(218, 218, 218));
            tableBottom.addCell(cell);

            // Startup fee amount
            cell = constructCell(invoice.getCurrency(), totalStartUpFeeAmount, FONT_ARIAL, false);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(218, 218, 218));
            tableBottom.addCell(cell);
        }
        // Credit
        if (totalApplyCredit != null && totalApplyCredit.compareTo(new BigDecimal("0.00")) > 0) {
            cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
            cell.setColspan(3);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);

            cell = new PdfPCell(new Phrase("CREDIT", FONT_REGULAR_8));
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            cell.setPaddingRight(10f);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(218, 218, 218));
            tableBottom.addCell(cell);

            // Credit amount
            cell = constructCell(invoice.getCurrency(), totalApplyCredit, FONT_ARIAL, false);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(218, 218, 218));
            tableBottom.addCell(cell);
        }

        //[TOTAL DUE] 行
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
        cell.setColspan(3);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        cell = new PdfPCell(new Phrase("TOTAL DUE", FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setPaddingRight(10f);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(218, 218, 218));
        tableBottom.addCell(cell);

        if (InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_BOLD_8_BLACK, true);
        } else {
            cell = constructCell(invoice.getCurrency(), invoice.getTotalInvoiceAmount(), FONT_BOLD_8_BLACK, true);
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorderWidth(0.1F);
        cell.setBorderColor(new BaseColor(218, 218, 218));
        tableBottom.addCell(cell);

        //invoice split
        if (invoice.getSplit()) {
            // sub invoice total amount space
            cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
            cell.setColspan(3);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableBottom.addCell(cell);
            // sub invoice total amount title
            cell = new PdfPCell(new Phrase(convertSubInvoiceNo(invoice.getSubInvoiceNo()).toUpperCase() + " AMOUNT", FONT_BOLD_8_BLACK));
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
            cell.setPaddingRight(10f);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(195, 195, 195));
            tableBottom.addCell(cell);
            // sub invoice total amount value
            cell = constructCell(invoice.getCurrency(), invoice.getDueAmount(), FONT_ARIAL, true);
            cell.setColspan(2);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorderWidth(0.1F);
            cell.setBorderColor(new BaseColor(195, 195, 195));
            tableBottom.addCell(cell);
        }

        // after space
        cell = new PdfPCell(new Phrase(StrUtil.SPACE));
        cell.setColspan(7);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        // Payment due by
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_ARIAL));
        cell.setColspan(4);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);

        cell = new PdfPCell(new Phrase("Payment due by " + DateTimeFormatter.ofPattern(YYYY_MM_DD_).format(invoice.getDueDate()), FONT_REGULAR_8));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableBottom.addCell(cell);
    }

    private void initTableRight(InvoiceDTO invoice, List<PdfPCell> accountCells, PdfPTable tableRight) {
        PdfPCell cell;
        // Invoice No
        cell = new PdfPCell(new Phrase("Invoice No." + invoice.getSubInvoiceNo(), FONT_BOLD_11));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        // invoice after space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        // Remit To
        cell = new PdfPCell(new Phrase(REMIT_TO, FONT_BOLD_8_BLACK));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        //动态生成account info数据
        accountCells.forEach(tableRight::addCell);
    }

    private void initTableRightSingapore(InvoiceDTO invoice, List<PdfPCell> accountCells, PdfPTable tableRight) {
        PdfPCell cell;
        // Invoice No
        cell = new PdfPCell(new Phrase("Invoice No." + invoice.getSubInvoiceNo(), FONT_BOLD_11));
        cell.setColspan(9);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        // Invoice Date title
        cell = new PdfPCell(new Phrase("Invoice Date: " +DateTimeFormatter.ofPattern(MM_DD_YYYY).format(invoice.getInvoiceDate()), FONT_BOLD_8_BLACK));
        cell.setColspan(9);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        // Invoice Date title
        cell = new PdfPCell(new Phrase("GST Reg No.:202202537R", FONT_BOLD_8_BLACK));
        cell.setColspan(9);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        // invoice after space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(9);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        // Remit To
        cell = new PdfPCell(new Phrase(REMIT_TO, FONT_BOLD_8_BLACK));
        cell.setColspan(9);
        cell.setPaddingLeft(-5f);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        //动态生成account info数据
        accountCells.forEach(tableRight::addCell);
    }


    private void initTopPart1RightForJPY(PdfPTable tableRight) {
        PdfPCell cell;
        //INVOICE
        cell = new PdfPCell(new Phrase("INVOICE", FONT_BOLD_20));
        cell.setColspan(4);
        cell.setRowspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        //space 5 line
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(5);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

    }

    private void initTopPart2RightForJPY(InvoiceDTO invoice, PdfPTable tableRight) {
        PdfPCell cell;

        // Invoice No
        cell = new PdfPCell(new Phrase("Invoice No." + invoice.getSubInvoiceNo(), FONT_CHINESE));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        //INVOICE DATE
        cell = new PdfPCell(new Phrase("Date:" + DateTimeFormatter.ofPattern(YYYY_MM_DD_).format(invoice.getInvoiceDate()), FONT_CHINESE));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_RIGHT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        //space 8 line
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_CHINESE));
        cell.setColspan(4);
        cell.setRowspan(8);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

    }

    private void initTopPart3RightForJPY(List<PdfPCell> accountCells, PdfPTable tableRight) {
        PdfPCell cell;

        // Remit To
        cell = new PdfPCell(new Phrase(REMIT_TO, FONT_BOLD_8_BLACK));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableRight.addCell(cell);

        //动态生成account info数据
        accountCells.forEach(tableRight::addCell);
    }

    private void initTableLeftData(InvoiceDTO invoice, EnumReceivingAccount receivingAccount, PdfPTable tableLeft) {
        //根据receivingAccount配置表，如果accountName有值则显示，没有显示logo
        PdfPCell cell;
        if (ObjectUtil.isNull(receivingAccount.getAccountName())) {
            Image img = readImage(LOGO_PATH, LOGO_PATH_SLASH);
            if (img != null) {
                cell = new PdfPCell(img, true);
            } else {
                cell = new PdfPCell(new Phrase(INTELLIPRO_GROUP_LOGO_TEXT));
            }
            cell.setColspan(2);
            cell.setRowspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableLeft.addCell(cell);

            // invoice before space
            cell = new PdfPCell(new Phrase(" "));
            cell.setColspan(1);
            cell.setRowspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_CENTER);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableLeft.addCell(cell);
        } else {
            cell = new PdfPCell(new Phrase(receivingAccount.getAccountName(), FONT_BOLD_11));
            cell.setColspan(3);
            cell.setRowspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableLeft.addCell(cell);

            // invoice before space
            cell = new PdfPCell(new Phrase(" "));
            cell.setColspan(3);
            cell.setRowspan(4);
            cell.setHorizontalAlignment(Element.ALIGN_LEFT);
            cell.setBorder(PdfPCell.NO_BORDER);
            tableLeft.addCell(cell);
        }
        // Customer Name and Address
        cell = new PdfPCell(new Phrase(CUSTOMER_NAME_AND_ADDRESS, FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // Customer Name
        cell = new PdfPCell(new Phrase(invoice.getCustomerName(), FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        String address = invoice.getCustomerAddress();
        if (StringUtils.isNotBlank(invoice.getCustomerLocation())) {
            address = invoice.getCustomerAddress() + ", " + invoice.getCustomerLocation();
        }

        // Customer Address
        cell = new PdfPCell(new Paragraph(address, FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // Customer Reference # title
        cell = new PdfPCell(new Phrase("Customer Reference #: ", FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);
        // Customer Reference # value
        cell = new PdfPCell(new Phrase(invoice.getCustomerReference(), FONT_CHINESE));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // PO # title
        cell = new PdfPCell(new Phrase("PO#: ", FONT_BOLD_8_BLACK));
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        cell.setColspan(2);
        tableLeft.addCell(cell);
        // PO # value
        cell = new PdfPCell(new Phrase(invoice.getPoNo(), FONT_CHINESE));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // Invoice Date title
        cell = new PdfPCell(new Phrase("Invoice Date: ", FONT_BOLD_8_BLACK));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);
        // Invoice Date value
        cell = new PdfPCell(new Phrase(DateTimeFormatter.ofPattern(MM_DD_YYYY).format(invoice.getInvoiceDate()), FONT_REGULAR_8));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);
    }

    private void initTopPart1LeftForJPY(PdfPTable tableLeft) {
        PdfPCell cell;
        Image img = readImage(LOGO_PATH, LOGO_PATH_SLASH);
        if (img != null) {
            cell = new PdfPCell(img, true);
        } else {
            cell = new PdfPCell(new Phrase(INTELLIPRO_GROUP_LOGO_TEXT));
        }
        cell.setColspan(2);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // invoice before space
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(1);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        //space 3 line
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_CHINESE));
        cell.setColspan(3);
        cell.setRowspan(3);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

    }

    private void initTopPart2LeftForJPY(PdfPTable tableLeft) {
        PdfPCell cell;

        //location，Tel，VAT Registration Number
        cell = new PdfPCell(new Phrase("〒100-0004", FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        cell = new PdfPCell(new Phrase("211 3F Shin Otemachi Building,", FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        cell = new PdfPCell(new Phrase("2-2-1 Otemachi, Chiyoda-ku, Tokyo 100-0004, Japan", FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        cell = new PdfPCell(new Phrase("VAT Registration Number：T4010001244137", FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        //space 4 line
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_CHINESE));
        cell.setColspan(3);
        cell.setRowspan(4);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

    }

    private void initTopPart3LeftForJPY(InvoiceDTO invoice, PdfPTable tableLeft) {
        PdfPCell cell;

        // Customer Name and Address
        cell = new PdfPCell(new Phrase(CUSTOMER_NAME_AND_ADDRESS, FONT_BOLD_8_BLACK));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        //space
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_CHINESE));
        cell.setColspan(3);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // Customer Name
        cell = new PdfPCell(new Phrase(invoice.getCustomerName(), FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        //space
        cell = new PdfPCell(new Phrase(StrUtil.SPACE, FONT_CHINESE));
        cell.setColspan(3);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);

        // Customer Address
        String address = invoice.getCustomerAddress();
        if (StringUtils.isNotBlank(invoice.getCustomerLocation())) {
            address = invoice.getCustomerAddress() + ", " + invoice.getCustomerLocation();
        }

        cell = new PdfPCell(new Paragraph(address, FONT_CHINESE));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        tableLeft.addCell(cell);
    }

    private void calMaxRowspanAndInitAccountCells(JSONObject jsonObject, List<PdfPCell> accountCells) {
        for (String key : jsonObject.keySet()) {
            PdfPCell cell;
            String value = StrUtil.toString(jsonObject.get(key));
            //创建accountInfo cell
            //如果是标题类型
            if (StringUtils.isBlank(value)) {
                if (key.contains(PLEASE_MAKE_PAYMENT_INFO)) {
                    cell = new PdfPCell(new Phrase(key, FONT_REGULAR_8));
                } else {
                    cell = new PdfPCell(new Phrase(key, FONT_BOLD_8_BLACK));
                }
                cell.setColspan(4);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                accountCells.add(cell);

                //如果是key:value类型
            } else {
                //key cell
                cell = new PdfPCell(new Phrase(key, FONT_BOLD_8_BLACK));
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                accountCells.add(cell);

                //value cell
                cell = new PdfPCell(new Phrase(value, FONT_BOLD_8_BLACK));
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                accountCells.add(cell);
            }

        }
    }

    private void calMaxRowspanAndInitAccountCellsForJPY(JSONObject jsonObject, List<PdfPCell> accountCells) {
        for (String key : jsonObject.keySet()) {
            PdfPCell cell;
            String value = StrUtil.toString(jsonObject.get(key));
            //创建accountInfo cell
            //如果是标题类型
            if (StringUtils.isBlank(value)) {
                if (key.contains(PLEASE_MAKE_PAYMENT_INFO)) {
                    cell = new PdfPCell(new Phrase(key, FONT_REGULAR_8));
                } else {
                    cell = new PdfPCell(new Phrase(key, FONT_BOLD_8_BLACK));
                }
                cell.setColspan(4);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                accountCells.add(cell);

                //如果是key:value类型
            } else {
                //key cell
                cell = new PdfPCell(new Phrase(key, FONT_BOLD_8_BLACK));
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                accountCells.add(cell);

                //value cell
                cell = new PdfPCell(new Phrase(value, FONT_REGULAR_8));
                cell.setColspan(2);
                cell.setHorizontalAlignment(Element.ALIGN_LEFT);
                cell.setBorder(PdfPCell.NO_BORDER);
                accountCells.add(cell);

            }

        }
    }

    public void addWatermarkForVoidInvoice(PdfReader reader, PdfStamper stamper) throws IOException, DocumentException {
        // image watermark
        Image img = readImage(VOID_PATH, VOID_PATH_SLASH);
        float w = img.getScaledWidth();
        float h = img.getScaledHeight();
        // transparency
        PdfGState gs1 = new PdfGState();
        gs1.setFillOpacity(0.5f);
        // properties
        PdfContentByte over;
        over = stamper.getOverContent(1);
        over.saveState();
        over.setGState(gs1);
        over.addImage(img, w, 0, 0, h, (595.0F / 2) - (w / 2), (842.0F / 2) - (h / 2));
        over.restoreState();
        stamper.close();
        reader.close();
    }

    private Image readImage(String imagePath, String imagePathSlash) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        Image image = null;
        try {
            File folderInput = new File(imagePath);
            log.debug("[PdfUtil.readImage][{}] read by File: {}", imagePath, folderInput.exists());
            if (folderInput.exists()) {
                BufferedImage folderImage = ImageIO.read(folderInput);
                ImageIO.write(folderImage, "png", out);
                byte[] byteArray = out.toByteArray();
                image = Image.getInstance(byteArray);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by File error.", imagePath);
        }

        try {
            ClassPathResource imgFile = new ClassPathResource(imagePath);
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource: ", imagePath, imgFile.exists());
            if (imgFile.exists()) {
                byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
                image = Image.getInstance(bytes);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource error.", imagePath);
        }

        try {
            URL url = PdfUtil.class.getClassLoader().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() error.", imagePath);
        }

        try {
            URL url = this.getClass().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() error.", imagePath);
        }

        try {
            URL url = Thread.currentThread().getContextClassLoader().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() error.", imagePath);
        }

        try {
            URL url = ClassLoader.getSystemClassLoader().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() error.", imagePath);
        }

        // ********************************imagePathSlash************************************/

        try {
            File folderInput = new File(imagePathSlash);
            log.debug("[PdfUtil.readImage][{}] read by File: {}", imagePathSlash, folderInput.exists());
            if (folderInput.exists()) {
                BufferedImage folderImage = ImageIO.read(folderInput);
                ImageIO.write(folderImage, "png", out);
                byte[] byteArray = out.toByteArray();
                image = Image.getInstance(byteArray);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by File error.", imagePathSlash);
        }

        try {
            ClassPathResource imgFile = new ClassPathResource(imagePathSlash);
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource: ", imagePathSlash, imgFile.exists());
            if (imgFile.exists()) {
                byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
                image = Image.getInstance(bytes);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource error.", imagePathSlash);
        }

        try {
            URL url = PdfUtil.class.getClassLoader().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() error.", imagePathSlash);
        }

        try {
            URL url = this.getClass().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() error.", imagePathSlash);
        }

        try {
            URL url = Thread.currentThread().getContextClassLoader().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() error.", imagePathSlash);
        }

        try {
            URL url = ClassLoader.getSystemClassLoader().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() error.", imagePathSlash);
        }


        try {
            out.close();
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage] close ByteArrayOutputStream error.");
        }

        return image;
    }

    private String convertSubInvoiceNo(String invoiceNo) {
        if (StringUtils.isNotEmpty(invoiceNo)) {
            return NUMBER_MAP.get(StringUtils.split(invoiceNo, "-")[1]);
        }
        return null;
    }

    /**
     * 根据币种，金额，字体，正负号 来构造PdfCell对象返回
     * Tips:如果币种为1，即为人民币，那么字体将强制使用FONT_CHINESE渲染
     *
     * @param currency 币种类型
     * @param amount   金额
     * @param font     字体
     * @param positive 正负号
     * @return
     */
    private PdfPCell constructCell(Integer currency, BigDecimal amount, Font font, boolean positive) {
        StringBuilder amountStr = new StringBuilder();
        if (!positive) {
            amountStr.append(NEGATIVE);
        }
        amountStr.append(CURRENCY_STAIC_MAP.get(currency))
                .append(CommonUtils.formatDecimalwithComma(amount));
        // if currency == 1 ,the font must be FONT_CHINESE
        if (currency != null && currency.equals(1)) {
            return new PdfPCell(new Phrase(amountStr.toString(), FONT_CHINESE));
        }
        if (currency != null && currency.equals(29)) {
            return new PdfPCell(new Phrase(amountStr.toString(),FONT_NOTO_SANS));
        }
        return new PdfPCell(new Phrase(amountStr.toString(), font));
    }

    private PdfPCell constructCellWithPrefix(String prefix, Integer currency, BigDecimal amount, Font font, boolean positive) {
        StringBuilder amountStr = new StringBuilder();
        amountStr.append(prefix);
        if (!positive) {
            amountStr.append(NEGATIVE);
        }
        amountStr.append(CURRENCY_STAIC_MAP.get(currency))
                .append(CommonUtils.formatDecimalwithComma(amount));
        // if currency == 1 ,the font must be FONT_CHINESE
        if (currency != null && currency.equals(1)) {
            return new PdfPCell(new Phrase(amountStr.toString(), FONT_CHINESE));
        }
        if (currency != null && currency.equals(29)) {
            return new PdfPCell(new Phrase(amountStr.toString(),FONT_NOTO_SANS));
        }
        return new PdfPCell(new Phrase(amountStr.toString(), font));
    }

    public static String convertToPercentage(BigDecimal value) {
        BigDecimal hundred = new BigDecimal("100");
        return value.multiply(hundred).setScale(2, RoundingMode.HALF_UP) + "%";
    }


}
