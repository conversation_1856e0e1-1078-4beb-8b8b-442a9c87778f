package com.altomni.apn.finance.web.rest.invoice;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.system.SequenceService;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * REST controller for managing sequence.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class SequenceResource {


    @Resource
    SequenceService sequenceService;

    @GetMapping("/sequence/{sequenceName}/{len}/{prefix}/{maxValue}")
    @Timed
    public ResponseEntity<String> getCommonSequence(@PathVariable("sequenceName") String sequenceName,
                                                    @PathVariable("len") Integer len,
                                                    @PathVariable("prefix") String prefix,
                                                    @PathVariable("maxValue") Long maxValue){
        log.info("[APN: SequenceResource @{}] REST request to get sequence : {}", SecurityUtils.getUserId());
        String sequence = sequenceService.getCommonSequence(sequenceName,len,prefix,maxValue);
        return ResponseEntity.ok(sequence);
    }
}
