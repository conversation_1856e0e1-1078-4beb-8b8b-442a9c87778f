package com.altomni.apn.finance.service.application;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.*;

import java.util.List;

public interface ApplicationService {

    TalentRecruitmentProcessIpgOfferLetterCostRateVO getOfferLetterCostRate(String code);

    TalentRecruitmentProcessVO getTalentRecruitmentProcess(Long id);

    List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByTalentId(Long talentId);

    List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByJobId(Long jobId);

    TalentRecruitmentProcessVO getTalentRecruitmentProcessByTalentIdAndJobId(Long talentId, Long jobId);

    List<TalentRecruitmentProcessKpiUserVO> getKpiUsersByTalentRecruitmentProcessId(Long id);

    JobType getJobTypeByRecruitmentProcessId(Long recruitmentProcessId);

    TalentRecruitmentProcessResignationVO getResignation(Long talentRecruitmentProcessId);

    void updateSubstituteTalentIdByTalentRecruitmentProcessId(TalentRecruitmentProcessUpdateSubstituteTalentDTO dto);
}
