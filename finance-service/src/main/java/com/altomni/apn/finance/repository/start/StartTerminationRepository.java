package com.altomni.apn.finance.repository.start;

import com.altomni.apn.common.domain.enumeration.StartTerminationReason;
import com.altomni.apn.finance.domain.start.StartTermination;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * Spring Data  repository for the StartTermination entity.
 */
@SuppressWarnings("unused")
@Repository
public interface StartTerminationRepository extends JpaRepository<StartTermination, Long> {

    StartTermination findByStartId(Long startId);

    @Query("select s.talentRecruitmentProcessId from StartTermination st " +
            "inner join  Start s  on st.startId=s.id " +
            "where s.talentId=:talentId and st.reason <> :reason")
    Set<Long> findTerminatedApplicationIdsByTalentIdAndExcludeReason(@Param("talentId") Long talentId, @Param("reason") StartTerminationReason reason);

    List<StartTermination> findAllByStartIdIn(List<Long> startIds);
}
