package com.altomni.apn.finance.web.rest.invoice;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.finance.service.vo.invoice.InvoiceCompanyInfoVO;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.domain.invoice.InvoiceActivity;
import com.altomni.apn.finance.domain.invoice.ViewInvoiceList;
import com.altomni.apn.finance.service.dto.invoice.*;
import com.altomni.apn.finance.service.invoice.InvoiceService;
import com.altomni.apn.finance.service.invoice.ViewInvoiceListService;
import com.altomni.apn.finance.service.query.InvoiceSearch;
import com.altomni.apn.finance.service.query.ViewInvoiceListSearch;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.vo.invoice.InvoiceSearchByApplicationVO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceTalentInfoVO;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.querydsl.core.types.dsl.BooleanExpression;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.swing.text.View;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Invoice.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class InvoiceResource {

    private static final String ENTITY_NAME = "invoice";

    @Resource
    private InvoiceService invoiceService;
    @Resource
    private ViewInvoiceListService viewInvoiceListService;

    /**
     * POST  /invoices/fte : Create a new fte invoice.
     *
     * @return the ResponseEntity with status 201 (Created) and with body the new invoice, or with status 400 (Bad Request) if the invoice has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/invoices/fte")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<InvoiceDTO>> createFteInvoice(@RequestBody InvoiceDTO invoiceDTO) throws URISyntaxException {
        log.info("[APN: Invoice @{}] REST request to create FTE invoice : {}", SecurityUtils.getUserId(), invoiceDTO);
        if (invoiceDTO.getId() != null) {
            throw new CustomParameterizedException("A new invoice cannot already have an ID ", ENTITY_NAME, "id exists");
        }
        List<InvoiceDTO> result = invoiceService.createFteInvoice(invoiceDTO);
        return ResponseEntity.created(new URI("/api/v3/invoices/invoice-no/" + result.get(0).getInvoiceNo()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.get(0).getInvoiceNo()))
                .body(result);
    }

    /**
     * POST  /invoices/startup-fee : Create a new startup fee invoice.
     *
     * @return the ResponseEntity with status 201 (Created) and with body the new invoice, or with status 400 (Bad Request) if the invoice has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/invoices/startup-fee")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<InvoiceDTO> createStartupFeeInvoice(@RequestBody Invoice invoice) throws URISyntaxException {
        log.info("[APN: Invoice @{}] REST request to create startup fee invoice : {}", SecurityUtils.getUserId(), invoice);
        if (invoice.getId() != null) {
            throw new CustomParameterizedException("A new invoice cannot already have an ID ", ENTITY_NAME, "id exists");
        }
        InvoiceDTO result = invoiceService.createStartupFeeInvoice(invoice);
        return ResponseEntity.created(new URI("/api/v3/invoices/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    /**
     * PUT  /invoices/void : Void an existing invoice.
     *
     * @return the ResponseEntity with status 200 (OK) and with body the updated invoice,
     * or with status 400 (Bad Request) if the invoice is not valid,
     * or with status 500 (Internal Server Error) if the invoice couldn't be updated
     */
    @PutMapping("/invoices/void")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<InvoiceActivity> voidInvoiceById(@RequestBody InvoiceVoidDTO invoiceVoidRecord) {
        log.info("[APN: Invoice @{}] REST request to void invoice: {}", SecurityUtils.getUserId(), invoiceVoidRecord);
        InvoiceActivity result = invoiceService.voidInvoice(invoiceVoidRecord);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    /**
     * PUT  /invoices/void-by-no : Void existing invoice.
     *
     * @param invoiceVoidRecord the invoice to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated invoice,
     * or with status 400 (Bad Request) if the invoice is not valid,
     * or with status 500 (Internal Server Error) if the invoice couldn't be updated
     */
//    @PutMapping("/invoices/void-by-no")
//    @Timed
//    @NoRepeatSubmit
//    public ResponseEntity<List<InvoiceActivity>> voidInvoiceByNo(@Valid @RequestBody InvoiceVoidDTO invoiceVoidRecord) {
//        log.info("[APN: Invoice @{}] REST request to void by invoice no: {}", SecurityUtils.getUserId(), invoiceVoidRecord);
//        List<InvoiceActivity> result = invoiceService.voidInvoiceByNo(invoiceVoidRecord);
//        return ResponseEntity.ok()
//                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, invoiceVoidRecord.toString()))
//                .body(result);
//    }


    @PostMapping("/invoices")
    @Timed
    public ResponseEntity<ViewInvoiceListSearchResult> getAllInvoices(@RequestBody InvoiceSearchDto dto, Pageable pageable) {
        log.info("[APN: Invoice @{}] REST request to get all Invoices: {}", SecurityUtils.getUserId(), dto);

        BooleanExpression expression = ViewInvoiceListSearch.invoice(dto.getInvoiceNo(), dto.getSubInvoiceNo(), dto.getInvoiceDate(), dto.getDueDate(), dto.getInvoiceType(), dto.getTalentName(), dto.getStatus(), dto.getPaymentDate(),
                dto.getCustomerName(), dto.getJobTitle(), dto.getTeamId(), dto.getFromDate(), dto.getToDate(), dto.getCreatedDate(), dto.getAm(),dto.getCoAm(), dto.getRecruiter(), dto.getSalesLeadOwner(), dto.getBdOwner(), dto.getAc(), dto.getDm(), dto.getSourcer(), dto.getOwner());
        Page<ViewInvoiceList> invoicePage = viewInvoiceListService.findAll(expression, pageable);
        ViewInvoiceListSearchResult result = viewInvoiceListService.toViewInvoiceListSearchResult(expression); //TODO: double check
        List<ViewInvoiceList> resultList = invoicePage.getContent();
        setCoAm(resultList);
        result.setElements(resultList);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(invoicePage, "/invoices");
        return new ResponseEntity<>(result, headers, HttpStatus.OK);
    }

    private void setCoAm(List<ViewInvoiceList> list) {
        list.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAm())) {
                List<UserCountryVO> voList = new ArrayList<>();
                Arrays.stream(x.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserName(amCountry[0]);
                    vo.setCountryId(amCountry[1]);
                    voList.add(vo);
                });
                x.setCoAmList(voList);
            }
        });
    }

    /**
     * GET  /invoices : get my the invoices.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of invoices in body
     */
    @GetMapping("/my-invoices")
    @Timed
    public ResponseEntity<InvoiceSearchResult> getMyInvoices(@RequestParam(value = "search", required = false) String search,
                                                             @RequestParam(value = "fromDate", required = false) String fromDate,
                                                             @RequestParam(value = "toDate", required = false) String toDate, Pageable pageable) {
        log.info("[APN: Invoice @{}] REST request to get my Invoices: fromDate: {}  toDate:{}, invoiceNo:{}", SecurityUtils.getUserId(), fromDate, toDate);
        BooleanExpression expression = InvoiceSearch.myInvoice(search, DateUtil.stringToLocalDate(fromDate), DateUtil.stringToLocalDate(toDate));
        Page<Invoice> invoicePage = invoiceService.findMyInvoices(expression, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(invoicePage, "/my-invoices");
        return new ResponseEntity<>(invoiceService.toSearchResult(invoicePage.getContent()), headers, HttpStatus.OK);
    }

    /**
     * GET  /invoices : 获取今日币种利率
     *
     */
    @PostMapping("/invoices/currency-rate-day")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity getDayCurrencyRate() {
        log.info("[APN: Invoice @{}] REST request to get currency-rate-day", SecurityUtils.getUserId());
        invoiceService.getDayCurrencyRate();
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * GET  /invoices : 获取今日币种利率
     *
     */
    @GetMapping("/invoices/all-currency-rate-day")
    @Timed
    public ResponseEntity getAllDayCurrencyRate() {
        log.info("[APN: Invoice @{}] REST request to get currency-rate-day", SecurityUtils.getUserId());
        invoiceService.getAllCurrencyRate();
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * GET  /invoices/:id : get the "id" invoice.
     *
     * @param id the id of the invoice to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the invoice, or with status 404 (Not Found)
     */
    @GetMapping("/invoices/{id}")
    @Timed
    public ResponseEntity<InvoiceDTO> getInvoice(@PathVariable Long id) {
        log.info("[APN: Invoice @{}] REST request to get Invoice : {}", SecurityUtils.getUserId(), id);
        InvoiceDTO invoice = invoiceService.findOne(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(invoice));
    }

    /**
     * GET  /invoices/invoice-no/{invoiceNo} get invoices by no.
     *
     * @param invoiceNo the id of the invoice to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the invoice, or with status 404 (Not Found)
     */
    @GetMapping("/invoices/invoice-no/{invoiceNo}")
    @Timed
    public ResponseEntity<List<InvoiceDTO>> getInvoicesByNo(@PathVariable String invoiceNo) {
        log.info("[APN: Invoice @{}] REST request to get Invoice List by invoice no: {}", SecurityUtils.getUserId(), invoiceNo);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(invoiceService.findByInvoiceNo(invoiceNo)));
    }

    @GetMapping("/invoices/startup-fee/companyId/{companyId}")
    @Timed
    public ResponseEntity<List<InvoiceDTO>> getStartupFeeInvoicesByCompanyId(@PathVariable Long companyId) {
        log.info("[APN: Invoice @{}] REST request to get startup-fee invoices by companyId: {}", SecurityUtils.getUserId(), companyId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(invoiceService.getStartupFeeInvoicesByCompanyId(companyId)));
    }

    /**
     * GET  /invoices/download/:id : download invoice by pdf.
     */
    @GetMapping("/invoices/download/{id}")
    @Timed
    public void downloadInvoice(HttpServletResponse response, @PathVariable Long id) {
        log.info("[APN: Invoice @{}] REST request to download invoice Id : {}", SecurityUtils.getUserId(), id);
        invoiceService.downloadInvoice(response, id);
    }

    /**
     * GET  /invoices/companyId/{companyId} : get all the invoices by companyId.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of invoices in body
     */
    @GetMapping("/invoices/companyId/{companyId}")
    @Timed
    public ResponseEntity<InvoiceSearchResult> getInvoicesByCompanyId(@PathVariable Long companyId,
                                                                      @RequestParam(value = "invoiceType", required = false) InvoiceType[] invoiceType,
                                                                      @RequestParam(value = "invoiceStatus", required = false) InvoiceStatus[] invoiceStatus,
                                                                      Pageable pageable) {
        log.info("[APN: Invoice @{}] REST request to get all the invoices by companyId: {}, invoiceType:{}, invoiceStatus:{}", SecurityUtils.getUserId(), companyId, invoiceType, invoiceStatus);
        BooleanExpression expression = InvoiceSearch.invoiceByCompany(invoiceType, companyId, invoiceStatus);
        Page<Invoice> invoicePage = invoiceService.findAll(expression, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(invoicePage, "/invoices/companyId/" + companyId);
        return new ResponseEntity<>(invoiceService.toSearchResult(invoicePage.getContent()), headers, HttpStatus.OK);
    }

    /**
     * GET  /invoices/startId/{startId} get invoices by startId.
     *
     * @param startId the id of the invoice to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the invoice, or with status 404 (Not Found)
     */
    @GetMapping("/invoices/startId/{startId}")
    @Timed
    public ResponseEntity<List<InvoiceDTO>> getInvoicesByStartId(@PathVariable Long startId) {
        log.info("[APN: Invoice @{}] REST request to get Invoice List by startId: {}", SecurityUtils.getUserId(), startId);
        List<InvoiceDTO> result = invoiceService.findByByStartId(startId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(result));
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }

    @PostMapping("/invoices/search-page-by-application")
    public ResponseEntity<List<InvoiceSearchByApplicationVO>> searchFtePageByApplication(@RequestBody InvoiceSearchByApplicationDTO invoiceSearchByApplicationDTO) {
        log.info("[invoice: User @{}] REST request to search fte page by application , param = {}:", SecurityUtils.getUserId(), invoiceSearchByApplicationDTO);
        Page<InvoiceSearchByApplicationVO> page = invoiceService.searchFtePageByApplication(invoiceSearchByApplicationDTO);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/contractor/invoice/search-page-by-application");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @GetMapping("/invoices/company-by-tenantId")
    public ResponseEntity<List<InvoiceCompanyInfoVO>> companyByTenantId() {
        log.info("[invoice: User @{}] REST request to search company by tenantId , param = {}:", SecurityUtils.getUserId(),SecurityUtils.getTenantId());
        return ResponseEntity.ok(invoiceService.companyByTenantId(SecurityUtils.getTenantId()));
    }

    @GetMapping("/invoices/talent-by-tenantId")
    public ResponseEntity<List<InvoiceTalentInfoVO>> talentByTenantId() {
        log.info("[invoice: User @{}] REST request to search talent by tenantId , param = {}:", SecurityUtils.getUserId(),SecurityUtils.getTenantId());
        return ResponseEntity.ok(invoiceService.talentByTenantId(SecurityUtils.getTenantId()));
    }

}
