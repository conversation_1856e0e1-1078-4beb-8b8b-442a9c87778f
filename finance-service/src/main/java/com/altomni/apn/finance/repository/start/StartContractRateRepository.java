package com.altomni.apn.finance.repository.start;

import com.altomni.apn.finance.domain.start.StartContractRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


/**
 * Spring Data  repository for the StartContractRate entity.
 */
@SuppressWarnings("unused")
@Repository
public interface StartContractRateRepository extends JpaRepository<StartContractRate, Long> {

    List<StartContractRate> findAllByStartId(Long startId);

    @Query(value = "select src.* from start_contract_rate src where src.start_id = ?1 order by src.start_date desc limit 1", nativeQuery = true)
    StartContractRate findLastByStartId(Long startId);

    @Query(value = " select scr.* from start_contract_rate scr inner join (select * from start s where s.talent_recruitment_process_id = ?1 AND s.position_type = ?2 and s.status in ?3 ORDER BY s.start_date DESC LIMIT 1) a on a.id = scr.start_id ", nativeQuery = true)
    List<StartContractRate> findByTalentRecruitmentProcessIdAndPositionTypeAndStatusInOrderByStartDate(Long talentRecruitmentProcessId, Integer positionType, List<Integer> contractStartStatus);

    List<StartContractRate> findAllByStartIdIn(Collection<Long> startIds);

}
