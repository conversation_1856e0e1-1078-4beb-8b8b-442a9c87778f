package com.altomni.apn.finance.service.application;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.*;
import com.altomni.apn.common.dto.xxljob.XxlJobInvoiceOverdueDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@Component
@FeignClient(value = "application-service")
public interface ApplicationClient {

    @GetMapping("/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates/{code}")
    ResponseEntity<TalentRecruitmentProcessIpgOfferLetterCostRateVO> getOfferLetterCostRate(@PathVariable("code") String code);

    @GetMapping("/application/api/v3/talent-recruitment-processes/{id}")
    ResponseEntity<TalentRecruitmentProcessVO> getTalentRecruitmentProcess(@PathVariable("id") Long id);

    @GetMapping("/application/api/v3/talent-recruitment-processes/talentId/{talentId}")
    ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessByTalentId(@PathVariable("talentId") Long talentId);

    @GetMapping("/application/api/v3/talent-recruitment-processes/jobId/{jobId}")
    ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessByJobId(@PathVariable("jobId") Long jobId);

    @GetMapping("/application/api/v3/talent-recruitment-processes/talentId/{talentId}/jobId/{jobId}")
    ResponseEntity<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByTalentIdAndJobId(@PathVariable("talentId") Long talentId, @PathVariable("jobId") Long jobId);

    @GetMapping("/application/api/v3/talent-recruitment-processes/{id}/kpi-users")
    ResponseEntity<List<TalentRecruitmentProcessKpiUserVO>> getKpiUsersByTalentRecruitmentProcessId(@PathVariable("id") Long id);

    @PostMapping("/application/api/v3/xxl-job/invoice-overdue-reminder")
    ResponseEntity<Void> createInvoiceOverDueReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    @DeleteMapping("/application/api/v3/xxl-job/invoice-overdue-reminder")
    ResponseEntity<Void> deleteInvoiceOverDueReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    @GetMapping("/application/api/v3/recruitment-processes/{id}")
    ResponseEntity<RecruitmentProcessVO> getRecruitmentProcessById(@PathVariable("id")Long recruitmentProcessId);

    @PostMapping("/application/api/v3/xxl-job/onboard-no-invoice-reminder-by-invoice-void")
    ResponseEntity<Void> onboardNoInvoiceReminderByInvoiceVoid(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    @PostMapping("/application/api/v3/talent-recruitment-processes/resign")
    ResponseEntity<Void> resign(@RequestBody TalentRecruitmentProcessResignationDTO resignationDTO);

    @GetMapping("/application/api/v3/talent-recruitment-processes/{talentRecruitmentProcessId}/resignation")
    ResponseEntity<TalentRecruitmentProcessResignationVO> getResignation(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @PostMapping("/application/api/v3/talent-recruitment-processes/updateSubstituteTalentIdByTalentRecruitmentProcessId")
    ResponseEntity<Void> updateSubstituteTalentIdByTalentRecruitmentProcessId(@RequestBody TalentRecruitmentProcessUpdateSubstituteTalentDTO substituteTalentDTO);
}
