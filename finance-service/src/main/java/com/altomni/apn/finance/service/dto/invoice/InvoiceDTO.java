package com.altomni.apn.finance.service.dto.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.domain.business.SalesLeadClientContact;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.domain.invoice.SubInvoice;
import com.altomni.apn.user.domain.user.Division;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InvoiceDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -624738900796842116L;

    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to.")
    private Long tenantId;

    @ApiModelProperty(value = "Invoice number")
    private String invoiceNo;

    @ApiModelProperty(value = "Sub invoice number")
    private String subInvoiceNo;

    @ApiModelProperty(value = "Split invoice or not")
    private Boolean split;

    @ApiModelProperty(value = "Invoice type: FTE Invoice, Startup Fee Invoice")
    private InvoiceType invoiceType;

    @ApiModelProperty(value = "Invoice status: Created, Approve, Paid, Void")
    private InvoiceStatus status;

    @ApiModelProperty(value = "Create start process id")
    private Long startId;

    @ApiModelProperty(value = "Create start process date.")
    private LocalDate startDate;

    @ApiModelProperty(value = "Job id.")
    private Long jobId;

    @ApiModelProperty(value = "Job title.")
    private String jobTitle;

    @ApiModelProperty(value = "Talent id.")
    private Long talentId;

    @ApiModelProperty(value = "Talent name.")
    private String talentName;

    @ApiModelProperty(value = "Program order number.")
    private String poNo;

    @ApiModelProperty(value = "Customer reference number.")
    private String customerReference;

    @ApiModelProperty(value = "The id of company")
    private Long companyId;

    @ApiModelProperty(value = "Company name.")
    private String customerName;

    @ApiModelProperty(value = "Company address.")
    private String customerAddress;

    @ApiModelProperty(value = "Company location.")
    private String customerLocation;

    @ApiModelProperty(value = "Canada Province tax .")
    private String canadaProvinceTaxString;

    @ApiModelProperty(value = "Client contact id.")
    private Long clientContactId;

    @ApiModelProperty(value = "The team id user belongs to.")
    private Long teamId;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillablePackage = BigDecimal.ZERO;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillAmount = BigDecimal.ZERO;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal taxRate = BigDecimal.ZERO;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal taxAmount = BigDecimal.ZERO;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "Discount amount")
    private BigDecimal discount = BigDecimal.ZERO;

    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty(value = "Total amount: finalFee - discount.")
    private BigDecimal totalInvoiceAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "The amount for current invoice. If it is a split invoice, merge all due amounts equals total amount." +
            "eg: if split == false, then totalAmount = dueAmount;" +
            "if split == true, totalAmount += all sub invoice's dueAmount")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal dueAmount = BigDecimal.ZERO;

    @ApiModelProperty(value = "The amount to be paid for this invoice ")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal unpaidAmount = BigDecimal.ZERO;

    private Long receivingAccountId;

    private LocalDate invoiceDate;

    private LocalDate dueDate;

    private Integer currency;

    private String currencyName;

    private String note;

    private Division division;

    private SalesLeadClientContact clientContact;

    private TalentV3 talent;

    private JobV3 job;

    private List<SubInvoice> subInvoiceList;

    private List<InvoiceActivityDTO> activities;

    public static InvoiceDTO fromInvoice(Invoice t) {
        InvoiceDTO dto = new InvoiceDTO();
        ServiceUtils.myCopyProperties(t, dto);
        return dto;
    }

    public BigDecimal getTotalBillAmount() {
        return totalBillAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalBillAmount));
    }

    public void setTotalBillAmount(BigDecimal totalBillAmount) {
        this.totalBillAmount = totalBillAmount;
    }

    public BigDecimal getTaxRate() {
        return taxRate == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT_TAX_RATE.format(taxRate));
    }

    public BigDecimal getTaxAmount() {
        return taxAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(taxAmount));
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getDiscount() {
        return discount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(discount));
    }

    public BigDecimal getTotalInvoiceAmount() {
        return totalInvoiceAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalInvoiceAmount));
    }

    public BigDecimal getDueAmount() {
        return dueAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(dueAmount));
    }

    public BigDecimal getUnpaidAmount() {
        return unpaidAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(unpaidAmount));
    }

    public Long getReceivingAccountId() {
        return receivingAccountId;
    }

    public LocalDate getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(LocalDate invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public void setClientContact(SalesLeadClientContact clientContact) {
        this.clientContact = clientContact;
    }

    public TalentV3 getTalent() {
        return talent;
    }

    public void setTalent(TalentV3 talent) {
        this.talent = talent;
    }

    public JobV3 getJob() {
        return job;
    }

    public void setJob(JobV3 job) {
        this.job = job;
    }

    public List<SubInvoice> getSubInvoiceList() {
        return subInvoiceList;
    }

    public void setActivities(List<InvoiceActivityDTO> activities) {
        this.activities = activities;
    }



}
