package com.altomni.apn.finance.service.query;

import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.domain.invoice.QViewInvoiceList;
import com.altomni.apn.common.utils.SecurityUtils;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.PathBuilder;
import org.apache.commons.lang3.StringUtils;

import java.time.Instant;


public class ViewInvoiceListSearch {

    public static BooleanExpression invoice(String invoiceNo, String subInvoiceNo, String invoiceDate, String dueDate, InvoiceType[] invoiceType,
                                            String talentName, InvoiceStatus[] status, String paymentDate, String customerName, String jobTitle,
                                            Long teamId, String fromDate, String toDate, String createdDate, String am,String coAm, String recruiter,
                                            String salesLeadOwner, String bdOwner, String ac, String dm, String sourcer, String owner) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        PathBuilder<Invoice> entityPath = new PathBuilder<>(Invoice.class, "ViewInvoiceList");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(null);
        BooleanExpression isTenantInvoice = qViewInvoiceList.tenantId.eq(SecurityUtils.getTenantId());
        return isTenantInvoice.and(builder.build(entityPath)).and(range(fromDate, toDate)).and(subInvoiceNo(subInvoiceNo)).and(invoiceNo(invoiceNo))
                .and(talentName(talentName)).and(customerName(customerName)).and(jobTitle(jobTitle)).and(teamId(teamId)).and(enumTypes(invoiceType, status))
                .and(date(invoiceDate, dueDate, paymentDate, createdDate)).and(am(am)).and(coAm(coAm)).and(recruiter(recruiter))
                .and(salesLeadOwner(salesLeadOwner)).and(bdOwner(bdOwner)).and(ac(ac)).and(dm(dm)).and(sourcer(sourcer)).and(owner(owner));
    }

    private static BooleanExpression am(String am) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(am)) {
            return qViewInvoiceList.am.contains(am);
        }
        return null;
    }

    private static BooleanExpression coAm(String coAm) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(coAm)) {
            return qViewInvoiceList.coAm.contains(coAm);
        }
        return null;
    }

    private static BooleanExpression recruiter(String recruiter) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(recruiter)) {
            return qViewInvoiceList.recruiter.contains(recruiter);
        }
        return null;
    }

    private static BooleanExpression salesLeadOwner(String salesLeadOwner) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(salesLeadOwner)) {
            return qViewInvoiceList.salesLeadOwner.contains(salesLeadOwner);
        }
        return null;
    }

    private static BooleanExpression bdOwner(String bdOwner) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(bdOwner)) {
            return qViewInvoiceList.bdOwner.contains(bdOwner);
        }
        return null;
    }

    private static BooleanExpression ac(String ac) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(ac)) {
            return qViewInvoiceList.ac.contains(ac);
        }
        return null;
    }

    private static BooleanExpression dm(String dm) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(dm)) {
            return qViewInvoiceList.dm.contains(dm);
        }
        return null;
    }

    private static BooleanExpression sourcer(String sourcer) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(sourcer)) {
            return qViewInvoiceList.sourcer.contains(sourcer);
        }
        return null;
    }

    private static BooleanExpression owner(String owner) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(owner)) {
            return qViewInvoiceList.owner.contains(owner);
        }
        return null;
    }

    private static BooleanExpression invoiceNo(String invoiceNo) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(invoiceNo)) {
            return qViewInvoiceList.invoiceNo.contains(invoiceNo);
        }
        return null;
    }

    private static BooleanExpression subInvoiceNo(String subInvoiceNo) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(subInvoiceNo)) {
            return qViewInvoiceList.subInvoiceNo.contains(subInvoiceNo);
        }
        return null;
    }

    private static BooleanExpression talentName(String talentName) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(talentName)) {
            return qViewInvoiceList.talentName.contains(talentName);
        }
        return null;
    }

    private static BooleanExpression customerName(String customerName) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(customerName)) {
            return qViewInvoiceList.customerName.contains(customerName);
        }
        return null;
    }

    private static BooleanExpression jobTitle(String jobTitle) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (StringUtils.isNotEmpty(jobTitle)) {
            return qViewInvoiceList.jobTitle.contains(jobTitle);
        }
        return null;
    }

    private static BooleanExpression teamId(Long teamId) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (teamId != null) {
            return qViewInvoiceList.teamId.eq(teamId);
        }
        return null;
    }

    private static BooleanExpression range(String fromDate, String toDate) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        if (fromDate != null && toDate != null) {
            return qViewInvoiceList.invoiceDate.between(DateUtil.stringToLocalDate(fromDate), DateUtil.stringToLocalDate(toDate));
        }
        return null;
    }

    private static BooleanExpression enumTypes(InvoiceType[] invoiceType, InvoiceStatus[] status) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        BooleanExpression exp = null;
        if (invoiceType != null && invoiceType.length!=0) {
            exp = qViewInvoiceList.invoiceType.in(invoiceType);
        }
        if (status != null && status.length!=0) {
            exp = exp != null ? exp.and(qViewInvoiceList.status.in(status)) : qViewInvoiceList.status.in(status);
        }
        return exp;
    }

    private static BooleanExpression date(String invoiceDate, String dueDate, String paymentDate, String createdDate) {
        QViewInvoiceList qViewInvoiceList = QViewInvoiceList.viewInvoiceList;
        BooleanExpression exp = null;
        if (StringUtils.isNotEmpty(dueDate)) {
            exp = qViewInvoiceList.dueDate.eq(DateUtil.stringToLocalDate(dueDate));
        }
        if (StringUtils.isNotEmpty(paymentDate)) {
            exp = exp != null ? exp.and(qViewInvoiceList.paymentDate.eq(DateUtil.stringToLocalDate(paymentDate))) : qViewInvoiceList.paymentDate.eq(DateUtil.stringToLocalDate(paymentDate));
        }
        if (StringUtils.isNotEmpty(invoiceDate)) {
            exp = exp != null ? exp.and(qViewInvoiceList.invoiceDate.eq(DateUtil.stringToLocalDate(invoiceDate))) : qViewInvoiceList.invoiceDate.eq(DateUtil.stringToLocalDate(invoiceDate));
        }
        if (createdDate != null) {
            Instant fromCreatedDate = Instant.parse(createdDate + "T00:00:00.00Z");
            Instant toCreatedDate = Instant.parse(createdDate + "T23:59:59.00Z");
            exp = exp != null ? exp.and(qViewInvoiceList.createdDate.between(fromCreatedDate, toCreatedDate)) : qViewInvoiceList.createdDate.between(fromCreatedDate, toCreatedDate);
        }
        return exp;
    }

}
