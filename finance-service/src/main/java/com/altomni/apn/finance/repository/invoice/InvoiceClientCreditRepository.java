package com.altomni.apn.finance.repository.invoice;

import com.altomni.apn.finance.domain.invoice.InvoiceClientCredit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Spring Data SQL repository for the InvoiceClientCredit entity.
 */
@SuppressWarnings("unused")
@Repository
public interface InvoiceClientCreditRepository extends JpaRepository<InvoiceClientCredit, Long> {

    Optional<InvoiceClientCredit> findByCurrencyAndCompanyId(Integer currency, Long companyId);

    List<InvoiceClientCredit> findAllByCompanyId(Long companyId);

}
