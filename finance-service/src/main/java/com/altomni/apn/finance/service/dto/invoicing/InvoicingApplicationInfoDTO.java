package com.altomni.apn.finance.service.dto.invoicing;

import com.altomni.apn.common.domain.enumeration.company.*;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@Data
public class InvoicingApplicationInfoDTO {

    private Long id;

    /** 1、全职招聘开票申请  2、预付金发票 3、废票 */
    private InvoicingApplicationType invoicingApplicationType ;

    /** 1、客户方付税款  2、我方付税款 */
    private TaxPayerType taxPayerType;

    /** 原id */
    private Long invalidInvoicingId ;

    /** 废票开票原因 */
    private String invoicingReason ;

    /** 编码 */
    private String codeNumber ;

    /**  */
    private Long companyId ;

    /**  */
    private Long tenantId ;

    /** 客户开票信息 */
    private Long clientInvoicingId ;

    /** 1上海, 2深圳, 3尹泰 */
    private InvoicingBody invoicingBody ;

    /** 1增值税电子专业发票, 2增值税电子普通发票, 3抵扣发票 */
    private InvoicingType invoicingType ;

    /** 1FTE, 2RPO, 3STF-外包, 4STF-人事代理 */
    private InvoicingBusinessType invoicingBusinessType ;

    /** 开票内容名称 */
    private Long invoicingServerTaxId ;

    /** 开票税率 */
    private Double invoicingTax ;

    /** 发票格式 1pdf, 2ofd, 3xml */
    private String invoicingFormat ;

    /** 确认函 */
    private String confirmationLetterUrl ;

    /** 发票 */
    private String invoiceUrl;

    /** 开票总金额 */
    private Double invoicingAmount ;

    /** 待回款金额 */
    private Double amountDue ;

    /** 发票税额 */
    private Double invoiceTax ;

    /** 不包含税额 */
    private Double taxesNotIncluded ;

    /** 应开票总金额 */
    private Double gpAmount ;

    /** 未开票金额 */
    private Double uninvoicedAmount ;

    /** 待审批、审批驳回、待开票、已开票、未回款、部分回款、逾期、全部回款、已作废 */
    private InvoicingStatus invoicingStatus ;

    private List<InvoicingCandidateInfoDTO> candidateInfoList;

    /** 数电票号码 */
    private String electronicInvoiceNumber ;

    /** 开票日期 */
    private Instant invoicingDate ;

    /** 账单天数 */
    private Integer dueWithinDays ;

    /** 到期日期 */
    private Instant paymentDueDate ;

    /** 开票备注 */
    private String note ;

    /** 财务开票备注 */
    private String invoiceNote;

    private List<InvoicingNoteDTO> invoicingUrlList;
}
