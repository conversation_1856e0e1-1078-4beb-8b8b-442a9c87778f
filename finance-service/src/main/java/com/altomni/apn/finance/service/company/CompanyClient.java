package com.altomni.apn.finance.service.company;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.vo.company.CompanyClientInfoVO;
import com.altomni.apn.common.vo.company.CompanyClientInvoicingInfoVO;
import com.altomni.apn.company.domain.company.CompanyContact;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@Component
@FeignClient(value = "company-service")
public interface CompanyClient {
    @GetMapping("/company/api/v3/company/{id}")
    ResponseEntity<CompanyDTO> getCompany(@PathVariable("id") Long id);

    @GetMapping("/company/api/v3/company/{id}/company-contacts")
    ResponseEntity<List<CompanyContact>> getCompanyContacts(@PathVariable("id") Long id);

    @GetMapping("/company/api/v3/sales-leads/{companyId}/am")
    ResponseEntity<List<Long>> getAllAmByCompany(@PathVariable("companyId") Long companyId);

    @GetMapping("/company/api/v3/company/invoicing/client/{id}")
    ResponseEntity<CompanyClientInvoicingInfoVO> getInvoicingClientInfoById(@PathVariable("id") Long id);

    //海外发票信息
    @GetMapping("/company/api/v3/company/client-list/{companyId}")
    ResponseEntity<List<CompanyClientInfoVO>> getCompanyClientInfoList(@PathVariable("companyId") Long companyId);

    //海外发票信息根据id获取
    @GetMapping("/company/api/v3/company/client-list/id/{id}")
    ResponseEntity<CompanyClientInfoVO> getCompanyClientInfoById(@PathVariable("id") Long id);

    //中国区发票信息
    @GetMapping("/company/api/v3/company/invoicing/client-list/{companyId}")
    ResponseEntity<List<CompanyClientInvoicingInfoVO>> getCompanyInvoicingClientInfoList(@PathVariable("companyId") Long companyId);
}
