package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.finance.domain.invoicing.InvoicingCandidateDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

@Repository
public interface InvoicingCandidateDetailRepository extends JpaRepository<InvoicingCandidateDetail, Long> {

    @Modifying
    @Transactional
    @Query(value = "update invoicing_candidate_detail set status=0 where invoice_application_id = ?1", nativeQuery = true)
    void updateStatusByInvoicingId(Long invoiceApplicationId);

    List<InvoicingCandidateDetail> findByInvoiceApplicationIdAndStatus(Long invoiceApplicationId,Integer status);

    List<InvoicingCandidateDetail> findByInvoiceApplicationIdInAndStatus(Collection<Long> invoiceApplicationId, Integer status);
}
