package com.altomni.apn.finance.service.invoice.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.invoice.InvoicePaymentRecord;
import com.altomni.apn.finance.domain.invoice.ViewInvoiceList;
import com.altomni.apn.finance.repository.invoice.InvoicePaymentRecordRepository;
import com.altomni.apn.finance.repository.invoice.ViewInvoiceListRepository;
import com.altomni.apn.finance.service.dto.invoice.InvoiceCurrencyAmount;
import com.altomni.apn.finance.service.dto.invoice.ViewInvoiceListSearchResult;
import com.altomni.apn.finance.service.invoice.ViewInvoiceListService;
import com.fasterxml.jackson.databind.util.ObjectBuffer;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.collections4.IterableUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@Service
public class ViewInvoiceListServiceImpl implements ViewInvoiceListService {

    private final Logger log = LoggerFactory.getLogger(ViewInvoiceListServiceImpl.class);

    @Resource
    private InvoicePaymentRecordRepository invoicePaymentRecordRepository;
    @Resource
    private ViewInvoiceListRepository viewInvoiceListRepository;

    @Override
    public Page<ViewInvoiceList> findAll(BooleanExpression booleanExpression, Pageable pageable) {
        return viewInvoiceListRepository.findAll(booleanExpression, pageable);
    }

    @Override
    public ViewInvoiceListSearchResult toViewInvoiceListSearchResult(BooleanExpression expression) {
        ViewInvoiceListSearchResult result = new ViewInvoiceListSearchResult();
        Iterable<ViewInvoiceList> viewInvoiceLists = viewInvoiceListRepository.findAll(expression);
        if (IterableUtils.isEmpty(viewInvoiceLists)) {
            return result;
        }
        List<Long> invoiceIds = StreamSupport.stream(viewInvoiceLists.spliterator(), false).map(ViewInvoiceList::getId).toList();

        Map<Long, List<InvoicePaymentRecord>> paymentRecordMap = invoicePaymentRecordRepository.findAllByInvoiceIdInAndActivated(invoiceIds, true)
            .stream().collect(Collectors.groupingBy(InvoicePaymentRecord::getInvoiceId, Collectors.toList()));

        List<InvoiceCurrencyAmount> currencyAmounts = StreamSupport.stream(viewInvoiceLists.spliterator(), false)
            .collect(Collectors.groupingBy(ViewInvoiceList::getCurrency, Collectors.toList()))
            .entrySet().stream().map(entry -> {
                Integer currency = entry.getKey();
                List<ViewInvoiceList> invoiceList = entry.getValue();

                InvoiceCurrencyAmount invoiceCurrencyAmount = new InvoiceCurrencyAmount();
                invoiceCurrencyAmount.setCurrency(currency);
                invoiceCurrencyAmount.setTotalAmount(invoiceList.stream().map(ViewInvoiceList::getDueAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

                BigDecimal totalOverDueAmount = invoiceList.stream().filter(invoice -> InvoiceStatus.OVERDUE == invoice.getStatus())
                    .map(ViewInvoiceList::getDueAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal totalPaidAmount = invoiceList.stream().filter(invoice -> isPaid(invoice.getStatus()))
                    .flatMap(invoice -> paymentRecordMap.getOrDefault(invoice.getId(), Collections.emptyList()).stream())
                    .map(payment -> {
                        BigDecimal amount = payment.getPaidAmount();
                        if(ObjectUtil.isNotNull(payment.getExchangeRate())) {
                            //根据分号拆分 exchangeRate, 根据当时的汇率计算
                            String[] exchangeRate = payment.getExchangeRate().split(StrUtil.COLON);
                            amount = amount.multiply(new BigDecimal(exchangeRate[0])).divide(new BigDecimal(exchangeRate[1]), 2, BigDecimal.ROUND_HALF_UP);
                        }
                        return amount;
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                invoiceCurrencyAmount.setTotalOverdueAmount(totalOverDueAmount);
                invoiceCurrencyAmount.setTotalPaidAmount(totalPaidAmount);
                return invoiceCurrencyAmount;
            }).toList();

        result.setCurrencyAmounts(currencyAmounts);
        return result;
    }

    private boolean isPaid(InvoiceStatus invoiceStatus) {
        return invoiceStatus == InvoiceStatus.PAID || invoiceStatus == InvoiceStatus.PARTIALLY_PAID || invoiceStatus == InvoiceStatus.STARTUP_FEE_PAID_USED || invoiceStatus == InvoiceStatus.STARTUP_FEE_PAID_UNUSED;
    }
}
