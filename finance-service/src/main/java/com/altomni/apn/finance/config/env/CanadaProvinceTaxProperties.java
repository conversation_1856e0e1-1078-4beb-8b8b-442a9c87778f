package com.altomni.apn.finance.config.env;

import com.altomni.apn.common.dto.CanadaProvinceTaxDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "canada-province-tax")
public class CanadaProvinceTaxProperties {

    Map<String, CanadaProvinceTaxDTO> canadaProvinceTax;
}
