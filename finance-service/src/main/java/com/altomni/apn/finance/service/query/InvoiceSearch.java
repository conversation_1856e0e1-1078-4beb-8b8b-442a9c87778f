package com.altomni.apn.finance.service.query;

import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.domain.invoice.QInvoice;
import com.altomni.apn.finance.domain.start.QStartCommission;
import com.altomni.apn.common.utils.SecurityUtils;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.jpa.JPAExpressions;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;


public class InvoiceSearch {

    public static BooleanExpression invoice(String search, String invoiceNo, InvoiceType[] invoiceType, InvoiceStatus[] status, LocalDate fromDate, LocalDate toDate) {
        QInvoice qInvoice = QInvoice.invoice;
        PathBuilder<Invoice> entityPath = new PathBuilder<>(Invoice.class, "invoice");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);
        BooleanExpression isTenantInvoice = qInvoice.tenantId.eq(SecurityUtils.getTenantId());
        return isTenantInvoice.and(builder.build(entityPath)).and(range(fromDate, toDate)).and(invoiceNo(invoiceNo)).and(enumTypes(invoiceType, status));
    }

    private static BooleanExpression invoiceNo(String invoiceNo) {
        QInvoice qInvoice = QInvoice.invoice;
        if (StringUtils.isNotEmpty(invoiceNo)) {
            return qInvoice.invoiceNo.contains(invoiceNo);
        }
        return null;
    }

    private static BooleanExpression range(LocalDate fromDate, LocalDate toDate) {
        QInvoice qInvoice = QInvoice.invoice;
        if (fromDate != null && toDate != null) {
            return qInvoice.invoiceDate.between(fromDate, toDate);
        }
        return null;
    }

    public static BooleanExpression invoiceByCompany(InvoiceType[] invoiceType, Long companyId, InvoiceStatus[] invoiceStatus) {
        QInvoice qInvoice = QInvoice.invoice;
        return qInvoice.tenantId.eq(SecurityUtils.getTenantId()).and(qInvoice.companyId.eq(companyId)).and(enumTypes(invoiceType, invoiceStatus));
    }

    private static BooleanExpression enumTypes(InvoiceType[] invoiceType, InvoiceStatus[] status) {
        QInvoice qInvoice = QInvoice.invoice;
        BooleanExpression exp = null;
        if (invoiceType != null && invoiceType.length!=0) {
            exp = qInvoice.invoiceType.in(invoiceType);
        }
        if (status != null && status.length!=0) {
            exp = exp != null ? exp.and(qInvoice.status.in(status)) : qInvoice.status.in(status);
        }
        return exp;
    }

    public static BooleanExpression myInvoice(String search, LocalDate fromDate, LocalDate toDate) {
        QInvoice qInvoice = QInvoice.invoice;
        PathBuilder<Invoice> entityPath = new PathBuilder<>(Invoice.class, "invoice");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);
        BooleanExpression isTenantInvoice = qInvoice.tenantId.eq(SecurityUtils.getTenantId());
        QStartCommission qStartCommission = QStartCommission.startCommission;
        BooleanExpression myStart = qInvoice.startId.in(JPAExpressions.selectFrom(qStartCommission)
                .where(qStartCommission.userId.eq(SecurityUtils.getUserId())).select(qStartCommission.startId).distinct());
        return isTenantInvoice.and(myStart).and(builder.build(entityPath)).and(range(fromDate, toDate));
    }
}
