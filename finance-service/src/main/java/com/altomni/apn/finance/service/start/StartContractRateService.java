package com.altomni.apn.finance.service.start;

import com.altomni.apn.finance.domain.start.StartContractRate;
import com.altomni.apn.finance.service.vo.StartContractRateCheckVO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface StartContractRateService {

    List<StartContractRate> create(StartContractRate startContractRate);

    List<StartContractRate> rateChange(StartContractRate startContractRate);

    List<StartContractRate> update(StartContractRate startContractRate);

    List<StartContractRate> findAllByStartId(Long startId);

    List<StartContractRate> delete(Long id);

    StartContractRate findById(Long id);

    BigDecimal getTotalBillAmount(StartContractRate startContractRate);

    StartContractRate findLastByStartId(Long startId);

    StartContractRate save(StartContractRate startContractRate);

    StartContractRateCheckVO checkRateDate(Long startId, StartContractRate startContractRate);
}
