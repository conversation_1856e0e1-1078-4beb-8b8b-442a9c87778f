package com.altomni.apn.finance.service.application.impl;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.finance.service.application.ApplicationClient;
import com.altomni.apn.finance.service.application.ApplicationService;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ApplicationServiceImpl implements ApplicationService {

    @Resource
    private ApplicationClient applicationClient;

    @Override
    public TalentRecruitmentProcessIpgOfferLetterCostRateVO getOfferLetterCostRate(String code) {
        ResponseEntity<TalentRecruitmentProcessIpgOfferLetterCostRateVO> response = applicationClient.getOfferLetterCostRate(code);
        return response != null ? response.getBody() : null;
    }

    @Override
    public TalentRecruitmentProcessVO getTalentRecruitmentProcess(Long id) {
        ResponseEntity<TalentRecruitmentProcessVO> response = applicationClient.getTalentRecruitmentProcess(id);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByTalentId(Long talentId) {
        ResponseEntity<List<TalentRecruitmentProcessVO>> response = applicationClient.getTalentRecruitmentProcessByTalentId(talentId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByJobId(Long jobId) {
        ResponseEntity<List<TalentRecruitmentProcessVO>> response = applicationClient.getTalentRecruitmentProcessByJobId(jobId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public TalentRecruitmentProcessVO getTalentRecruitmentProcessByTalentIdAndJobId(Long talentId, Long jobId) {
        ResponseEntity<TalentRecruitmentProcessVO> response = applicationClient.getTalentRecruitmentProcessByTalentIdAndJobId(talentId, jobId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<TalentRecruitmentProcessKpiUserVO> getKpiUsersByTalentRecruitmentProcessId(Long id) {
        ResponseEntity<List<TalentRecruitmentProcessKpiUserVO>> response = applicationClient.getKpiUsersByTalentRecruitmentProcessId(id);
        return response != null ? response.getBody() : new ArrayList<>();
    }

    @Override
    public JobType getJobTypeByRecruitmentProcessId(Long recruitmentProcessId){
        JobType jobType = Optional.of(applicationClient.getRecruitmentProcessById(recruitmentProcessId).getBody())
                .orElseThrow(
                        ()-> new CustomParameterizedException("Did not find job type for recruitment process " + recruitmentProcessId)
                )
                .getJobType();
        return jobType;
    }

    @Override
    public TalentRecruitmentProcessResignationVO getResignation(Long talentRecruitmentProcessId) {
        return applicationClient.getResignation(talentRecruitmentProcessId).getBody();
    }

    @Override
    public void updateSubstituteTalentIdByTalentRecruitmentProcessId(TalentRecruitmentProcessUpdateSubstituteTalentDTO dto) {
        applicationClient.updateSubstituteTalentIdByTalentRecruitmentProcessId(dto);
    }
}
