package com.altomni.apn.finance.domain.enumeration.commission;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CommissionStatus enumeration.
 */
public enum CommissionStatus implements ConvertedEnum<Integer> {
    UNPAID(0),
    PAID(1);

    private final Integer dbValue;

    CommissionStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<CommissionStatus, Integer> resolver =
        new ReverseEnumResolver<>(CommissionStatus.class, CommissionStatus::toDbValue);

    public static CommissionStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
