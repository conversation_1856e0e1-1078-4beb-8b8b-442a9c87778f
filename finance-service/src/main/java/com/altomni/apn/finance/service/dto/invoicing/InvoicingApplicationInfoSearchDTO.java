package com.altomni.apn.finance.service.dto.invoicing;

import com.altomni.apn.common.domain.enumeration.company.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

@Data
public class InvoicingApplicationInfoSearchDTO {

    private Long id;

    private String codeNumber;

    /**
     * 数电票号码
     */
    private List<String> elecInvoiceNumber;

    /**
     * 开票日期
     */
    private String invoicingDateStart;

    /**
     * 开票日期
     */
    private String invoicingDateEnd;

    private List<InvoicingStatus> invoicingStatusList;

    private List<InvoicingType> invoicingTypeList;

    private String createdDateStart;

    private String createdDateEnd;

    private List<Long> applicationUserIdList;

    private List<Long> applicationUserTeamIdList;

    private List<String> talentIdList;

    private List<String> jobIdList;

    private List<Long> companyIdList;

    private List<Long> invoicingServerNameList;

    private List<InvoicingBusinessType> invoicingBusinessTypeList;

    private List<InvoicingApplicationType> invoicingApplicationTypeList;

    private Double invoicingTax;
    private Integer dueWithinDays;

    private String paymentDueDate;

    private List<InvoicingBody> invoicingBodyList;
    private List<InvoicingFormat> invoicingFormatList;
    private List<Long> accountManagerList;
    private List<Long> accountManagerTeamList;
    private List<Long> cooperateAccountManagerList;
    private List<Long> cooperateAccountManagerTeamList;
    private List<Long> recruiterList;
    private List<Long> recruiterTeamList;
    private List<Long> deliveryManagerList;
    private List<Long> deliveryManagerTeamList;
    private List<Long> sourcerList;
    private List<Long> sourcerTeamList;
    private List<Long> ownerList;
    private List<Long> ownerTeamList;
    private List<Long> bdManagerList;
    private List<Long> bdManagerTeamList;
    private List<Long> salesLeadManagerList;
    private List<Long> salesLeadManagerTeamList;

    private String clientName;

}
