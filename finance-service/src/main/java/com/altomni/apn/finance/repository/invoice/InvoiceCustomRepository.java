package com.altomni.apn.finance.repository.invoice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.service.dto.invoice.InvoiceSearchByApplicationDTO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceCompanyInfoVO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceSearchByApplicationVO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceTalentInfoVO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class InvoiceCustomRepository {

    @Resource
    private EntityManager entityManager;

    public List<InvoiceSearchByApplicationVO> searchDataByApplication(InvoiceSearchByApplicationDTO invoiceSearchByApplicationDTO) {
        String sql = " SELECT i.id invoice_id, i.invoice_no, i.sub_invoice_no, i.invoice_date, i.due_date, i.created_date, i.invoice_type, t.full_name full_name,t.id talent_id, i.status invoice_status, " +
                " vil.due_amount due_amount, vil.received_amount received_amount, vil.balance balance_amount , i.company_id company_id, c.`full_business_name` company_name,j.id job_id, j.title job_title, " +
                " pt.id team_id, pt.name team_name, i.currency " +
                " FROM invoice i inner join start s on s.id = i.start_id " +
                " inner join view_invoice_list vil on vil.id = i.id " +
                " inner join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = s.talent_recruitment_process_id " +
                " inner join talent t on t.id = i.talent_id " +
                " inner join company c on c.id = i.company_id " +
                " inner join job j on i.job_id = j.id " +
                " INNER JOIN permission_user_team put ON put.user_id = kpi.user_id " +
                " inner join permission_team pt on pt.id = i.team_id " +
                " where i.invoice_type = 0 " + getWhereSql(invoiceSearchByApplicationDTO) +
                " GROUP BY i.id " + getOrderBySql(invoiceSearchByApplicationDTO.getSort()) +
                " limit :startOffset, :size ";
        Query query = entityManager.createNativeQuery(sql, InvoiceSearchByApplicationVO.class);
        int startOffset = (invoiceSearchByApplicationDTO.getPage() - 1) * invoiceSearchByApplicationDTO.getSize();
        query.setParameter("startOffset", startOffset);
        query.setParameter("size", invoiceSearchByApplicationDTO.getSize());
        setParameter(query, invoiceSearchByApplicationDTO);
        return query.getResultList();
    }

    public Long searchCountByApplication(InvoiceSearchByApplicationDTO invoiceSearchByApplicationDTO) {
        String sql = " select count(1) from (SELECT i.id " +
                " FROM invoice i inner join start s on s.id = i.start_id " +
                " inner join view_invoice_list vil on vil.id = i.id " +
                " inner join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = s.talent_recruitment_process_id " +
                " inner join talent t on t.id = i.talent_id " +
                " inner join company c on c.id = i.company_id " +
                " inner join job j on i.job_id = j.id " +
                " INNER JOIN permission_user_team put ON put.user_id = kpi.user_id " +
                " inner join permission_team pt on pt.id = i.team_id " +
                " where i.invoice_type = 0 " + getWhereSql(invoiceSearchByApplicationDTO) +
                " GROUP BY i.id ) temp ";
        Query query = entityManager.createNativeQuery(sql);
        setParameter(query, invoiceSearchByApplicationDTO);
        return Long.parseLong(String.valueOf(query.getSingleResult()));
    }

    private void setParameter(Query query, InvoiceSearchByApplicationDTO invoiceSearchByApplicationDTO) {
        if (ObjectUtil.isNotNull(invoiceSearchByApplicationDTO.getUserId())) {
            query.setParameter("userId", invoiceSearchByApplicationDTO.getUserId());
        }
        if (ObjectUtil.isNotNull(invoiceSearchByApplicationDTO.getPrimaryTeamId())) {
            query.setParameter("primaryTeamId", invoiceSearchByApplicationDTO.getPrimaryTeamId());
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getInvoiceNo())) {
            query.setParameter("invoiceNo", "%" + invoiceSearchByApplicationDTO.getInvoiceNo() + "%");
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getInvoiceNo())) {
            query.setParameter("invoiceNo", "%" + invoiceSearchByApplicationDTO.getInvoiceNo() + "%");
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getSubInvoiceNo())) {
            query.setParameter("subInvoiceNo", "%" + invoiceSearchByApplicationDTO.getSubInvoiceNo() + "%");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getInvoiceType())) {
            query.setParameter("invoiceType", invoiceSearchByApplicationDTO.getInvoiceType().toDbValue());
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getInvoiceStartTime()) && ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getInvoiceEndTime())) {
            query.setParameter("invoiceStartTime", invoiceSearchByApplicationDTO.getInvoiceStartTime());
            query.setParameter("invoiceEndTime", invoiceSearchByApplicationDTO.getInvoiceEndTime());
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getCreatedStartTime()) && ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getCreatedEndTime())) {
            query.setParameter("createdStartTime", invoiceSearchByApplicationDTO.getCreatedStartTime());
            query.setParameter("createdEndTime", invoiceSearchByApplicationDTO.getCreatedEndTime());
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getFullName())) {
            query.setParameter("fullName", "%" + invoiceSearchByApplicationDTO.getFullName() + "%");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getTalentId())) {
            query.setParameter("talentId", invoiceSearchByApplicationDTO.getTalentId());
        }
        if (CollUtil.isNotEmpty(invoiceSearchByApplicationDTO.getInvoiceStatusList())) {
            query.setParameter("invoiceStatusList", invoiceSearchByApplicationDTO.getInvoiceStatusList().stream().map(InvoiceStatus::toDbValue).collect(Collectors.toList()));
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getCompanyId())) {
            query.setParameter("companyId", invoiceSearchByApplicationDTO.getCompanyId());
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getCompanyName())) {
            query.setParameter("companyName", "%" + invoiceSearchByApplicationDTO.getCompanyName() + "%");
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getJobTitle())) {
            query.setParameter("jobTitle", "%" + invoiceSearchByApplicationDTO.getJobTitle() + "%");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getJobId())) {
            query.setParameter("jobId", invoiceSearchByApplicationDTO.getJobId());
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getTeamId())) {
            query.setParameter("teamId", invoiceSearchByApplicationDTO.getTeamId());
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getTeamName())) {
            query.setParameter("teamName", "%" + invoiceSearchByApplicationDTO.getTeamName() + "%");
        }
    }

    private String getOrderBySql(SearchSortDTO sort) {
        String orderBySql;
        if (ObjectUtil.isEmpty(sort)) {
            orderBySql = " order by i.created_date desc ";
        } else {
            Map<String, String> chineseMap = new HashMap<>(16);
            chineseMap.put("fullName", "t.full_name");
            chineseMap.put("companyName", "c.full_business_name");
            chineseMap.put("jobTitle", "j.title");
            chineseMap.put("teamName", "pt.name");
            if (chineseMap.containsKey(sort.getProperty())) {
                orderBySql = " order by CONVERT(" + chineseMap.get(sort.getProperty()) + "  USING GBK) " + sort.getDirection() + " ";
            } else {
                orderBySql = " order by " + StrUtil.toUnderlineCase(sort.getProperty()) + " " + sort.getDirection() + " ";
            }
        }
        return orderBySql;
    }

    private String getWhereSql(InvoiceSearchByApplicationDTO invoiceSearchByApplicationDTO) {
        StringBuilder whereSb = new StringBuilder();
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getUserId())) {
            whereSb.append(" and kpi.user_id = :userId ");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getPrimaryTeamId())) {
            whereSb.append(" AND put.team_id = :primaryTeamId ");
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getInvoiceNo())) {
            whereSb.append(" and i.invoice_no like :invoiceNo ");
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getSubInvoiceNo())) {
            whereSb.append(" and i.sub_invoice_no like :subInvoiceNo ");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getInvoiceType())) {
            whereSb.append(" and i.invoice_type = :invoiceType ");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getInvoiceStartTime()) && ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getInvoiceEndTime())) {
            whereSb.append(" and i.invoice_date BETWEEN :invoiceStartTime and :invoiceEndTime ");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getCreatedStartTime()) && ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getCreatedEndTime())) {
            whereSb.append(" and i.created_date BETWEEN :createdStartTime and :createdEndTime ");
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getFullName())) {
            whereSb.append(" and t.full_name like :fullName ");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getTalentId())) {
            whereSb.append(" and t.id = :talentId ");
        }
        if (CollUtil.isNotEmpty(invoiceSearchByApplicationDTO.getInvoiceStatusList())) {
            whereSb.append(" and i.status in ( :invoiceStatusList ) ");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getCompanyId())) {
            whereSb.append(" and c.id = :companyId ");
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getCompanyName())) {
            whereSb.append(" and c.full_business_name like :companyName ");
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getJobTitle())) {
            whereSb.append(" and j.title like :jobTitle ");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getJobId())) {
            whereSb.append(" and j.id = :jobId ");
        }
        if (ObjectUtil.isNotEmpty(invoiceSearchByApplicationDTO.getTeamId())) {
            whereSb.append(" and i.team_id = :teamId ");
        }
        if (StrUtil.isNotBlank(invoiceSearchByApplicationDTO.getTeamName())) {
            whereSb.append(" and pt.name like :teamName ");
        }
        return whereSb.toString();
    }

    public List<InvoiceCompanyInfoVO> searchCompanyByTenantId(Long tenantId) {
        String sql = " SELECT c.id company_id, c.full_business_name company_name " +
                " FROM invoice i " +
                " inner join company c on c.id = i.company_id " +
                " where i.invoice_type = 0 and i.tenant_id = :tenantId " +
                " GROUP BY c.id ";
        Query query = entityManager.createNativeQuery(sql, InvoiceCompanyInfoVO.class);
        query.setParameter("tenantId", tenantId);
        return query.getResultList();
    }

    public List<InvoiceTalentInfoVO> searchTalentByTenantId(Long tenantId) {
        String sql = " SELECT t.id talent_id, t.full_name talent_name " +
                " FROM invoice i " +
                " inner join talent t on t.id = i.talent_id " +
                " where i.invoice_type = 0 and i.tenant_id = :tenantId " +
                " GROUP BY t.id ";
        Query query = entityManager.createNativeQuery(sql, InvoiceTalentInfoVO.class);
        query.setParameter("tenantId", tenantId);
        return query.getResultList();
    }
}
