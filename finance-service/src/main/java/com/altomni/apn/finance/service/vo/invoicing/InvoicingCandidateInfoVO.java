package com.altomni.apn.finance.service.vo.invoicing;

import cn.hutool.json.JSONObject;
import com.altomni.apn.finance.service.dto.start.StartSearchInvoicingDTO;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InvoicingCandidateInfoVO {

    private Long id;

    private String gpAmount;

    private String amountPercentage;

    private String amountReceived;

    private String amountReceivedTax;

    private String prepaymentAmount;

    private String paymentAmount;

    private Boolean chinaOrder;

    private Long startId;

    private Long talentId;

    private Long companyId;

    private String talentName;

    private String jobName;

    private Long jobId;

    private boolean contractStatus;

    private String accountManager;

    private String recruiter;

    private String deliveryManager;

    private String sourcer;

    private String owner;

    private String bdManager;

    private String salesLeadManager;

    private String cooperateAccountManager;

    private String cooperateAccountManagerPercentage;

    private List<UserCountryVO> coAmList;

    private String accountManagerPercentage;

    private String recruiterPercentage;

    private String deliveryManagerPercentage;

    private String sourcerPercentage;

    private String ownerPercentage;

    private String bdManagerPercentage;

    private String salesLeadManagerPercentage;

    private JSONObject teamLeaderName;

    private JSONObject profitCenterManager;

    private String expectedInvoicingAmount;

    private Instant expectedInvoicingDate;

    List<InvoiceContractVO> contractList;

    private StartSearchInvoicingDTO startInvoicing;
}
