package com.altomni.apn.finance.repository.start;

import com.altomni.apn.finance.domain.start.StartFteRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


/**
 * Spring Data  repository for the StartFteRate entity.
 */
@SuppressWarnings("unused")
@Repository
public interface StartFteRateRepository extends JpaRepository<StartFteRate, Long> {

    StartFteRate findByStartId(Long startId);

    List<StartFteRate> findAllByStartIdIn(Collection<Long> startIds);
}
