package com.altomni.apn.finance.service.start;

import com.altomni.apn.finance.domain.start.Start;
import com.altomni.apn.finance.domain.start.StartCommission;

import java.util.List;

/**
 * Service Implementation for managing StartCommission.
 */
public interface StartCommissionService {

    List<StartCommission> create(Start start, List<StartCommission> startCommission);

    List<StartCommission> replace(Start start, List<StartCommission> startCommission);

    List<StartCommission> findByStartId(Long startId);
}
