package com.altomni.apn.finance.service.vo.invoicing;

import com.altomni.apn.common.domain.enumeration.company.TaxPayerType;

import java.math.BigDecimal;
import java.math.BigInteger;


public class InvoicingApplicationInfoDownloadVO {

    private BigInteger id;

    private String taxPayerType;

    /**
     * 1、全职招聘开票申请  2、预付金发票 3、废票
     */
    private String invoicingApplicationType;

    /**
     * 编码
     */
    private String codeNumber;

    /**
     *
     */
    private BigInteger tenantId;

    /**
     * 1上海, 2深圳, 3尹泰
     */
    private String invoicingBody;

    /**
     * 1增值税电子专业发票, 2增值税电子普通发票, 3抵扣发票
     */
    private String invoicingType;

    /**
     * 1FTE, 2RPO, 3STF-外包, 4STF-人事代理
     */
    private String invoicingBusinessType;

    /**
     * 开票内容名称
     */
    private String invoicingTaxName;

    /**
     * 开票税率
     */
    private Double invoicingTax;

    private String invoicingTaxStr;

    /**
     * 发票格式 1pdf, 2ofd, 3xml
     */
    private String invoiceFormat;

    /**
     * 确认函
     */
    private String confirmationLetterUrl;

    private BigDecimal talentGpAmount;

    private BigDecimal invoicingAmount;

    private BigDecimal prepaymentAmount;

    private BigDecimal paymentAmount;

    /**
     * 待回款金额
     */
    private BigDecimal amountDue;

    /**
     * 发票税额
     */
    private BigDecimal invoiceTax;

    /**
     * 不包含税额
     */
    private BigDecimal taxesNotIncluded;

    /**
     * 应收金额
     */
    private BigDecimal gpAmount;

    /**
     * 实开金额
     */
    private BigDecimal amountReceived;

    /**
     * 实开金额(含税)
     */
    private BigDecimal amountReceivedTax;

    /**
     * 未开票金额
     */
    private BigDecimal uninvoicedAmount;

    /**
     * 待审批、审批驳回、待开票、已开票、未回款、部分回款、逾期、全部回款、已作废
     */
    private String invoicingStatus;

    /**
     * 数电票号码
     */
    private String elecInvoiceNumber;

    /**
     * 开票日期
     */
    private String invoicingDate;

    /**
     * 账单天数
     */
    private Integer dueWithinDays;

    /**
     * 到期日期
     */
    private String paymentDueDate;

    private String createdDate;

    private String applicationUser;

    private String talentName;

    private BigInteger talentId;

    private BigInteger jobId;

    private String jobName;

    private String companyName;

    private String clientName;

    private String socialCreditCode;

    private String bankName;

    private String bankAccount;

    private String invoicingAddress;

    private String phone;

    private String invoiceArea;

    private String participateName;

    private String participateRole;

    private String participateUserPercentage;

    private String participateUserGp;

    private String participateCashGp;

    private String participateTeamLeader;

    private String profitOwner;

    private String paymentDate;

    private String paymentMethod;

    private String note;

    private String closeFlag;

    private String voidFlag;

    public BigInteger getId() {
        return id;
    }

    public void setId(BigInteger id) {
        this.id = id;
    }

    public String getTaxPayerType() { return taxPayerType; }

    public void setTaxPayerType(String taxPayerType) { this.taxPayerType = taxPayerType; }

    public String getInvoicingApplicationType() {
        return invoicingApplicationType;
    }

    public void setInvoicingApplicationType(String invoicingApplicationType) {
        this.invoicingApplicationType = invoicingApplicationType;
    }

    public String getCodeNumber() {
        return codeNumber;
    }

    public void setCodeNumber(String codeNumber) {
        this.codeNumber = codeNumber;
    }

    public BigInteger getTenantId() {
        return tenantId;
    }

    public void setTenantId(BigInteger tenantId) {
        this.tenantId = tenantId;
    }

    public String getInvoicingBody() {
        return invoicingBody;
    }

    public void setInvoicingBody(String invoicingBody) {
        this.invoicingBody = invoicingBody;
    }

    public String getInvoicingType() {
        return invoicingType;
    }

    public void setInvoicingType(String invoicingType) {
        this.invoicingType = invoicingType;
    }

    public String getInvoicingBusinessType() {
        return invoicingBusinessType;
    }

    public void setInvoicingBusinessType(String invoicingBusinessType) {
        this.invoicingBusinessType = invoicingBusinessType;
    }

    public String getInvoicingTaxName() {
        return invoicingTaxName;
    }

    public void setInvoicingTaxName(String invoicingTaxName) {
        this.invoicingTaxName = invoicingTaxName;
    }

    public Double getInvoicingTax() {
        return invoicingTax;
    }

    public void setInvoicingTax(Double invoicingTax) {
        this.invoicingTax = invoicingTax;
    }

    public String getInvoicingTaxStr() {
        return invoicingTaxStr;
    }

    public void setInvoicingTaxStr(String invoicingTaxStr) {
        this.invoicingTaxStr = invoicingTaxStr;
    }

    public String getInvoiceFormat() {
        return invoiceFormat;
    }

    public void setInvoiceFormat(String invoiceFormat) {
        this.invoiceFormat = invoiceFormat;
    }

    public String getConfirmationLetterUrl() {
        return confirmationLetterUrl;
    }

    public void setConfirmationLetterUrl(String confirmationLetterUrl) {
        this.confirmationLetterUrl = confirmationLetterUrl;
    }

    public BigDecimal getTalentGpAmount() {
        return talentGpAmount;
    }

    public void setTalentGpAmount(BigDecimal talentGpAmount) {
        this.talentGpAmount = talentGpAmount;
    }

    public BigDecimal getInvoicingAmount() {
        return invoicingAmount;
    }

    public void setInvoicingAmount(BigDecimal invoicingAmount) {
        this.invoicingAmount = invoicingAmount;
    }

    public BigDecimal getPrepaymentAmount() {
        return prepaymentAmount;
    }

    public void setPrepaymentAmount(BigDecimal prepaymentAmount) {
        this.prepaymentAmount = prepaymentAmount;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public void setPaymentAmount(BigDecimal paymentAmount) {
        this.paymentAmount = paymentAmount;
    }

    public BigDecimal getAmountDue() {
        return amountDue;
    }

    public void setAmountDue(BigDecimal amountDue) {
        this.amountDue = amountDue;
    }

    public BigDecimal getInvoiceTax() {
        return invoiceTax;
    }

    public void setInvoiceTax(BigDecimal invoiceTax) {
        this.invoiceTax = invoiceTax;
    }

    public BigDecimal getTaxesNotIncluded() {
        return taxesNotIncluded;
    }

    public void setTaxesNotIncluded(BigDecimal taxesNotIncluded) {
        this.taxesNotIncluded = taxesNotIncluded;
    }

    public BigDecimal getGpAmount() {
        return gpAmount;
    }

    public void setGpAmount(BigDecimal gpAmount) {
        this.gpAmount = gpAmount;
    }

    public BigDecimal getAmountReceived() {
        return amountReceived;
    }

    public void setAmountReceived(BigDecimal amountReceived) {
        this.amountReceived = amountReceived;
    }

    public BigDecimal getUninvoicedAmount() {
        return uninvoicedAmount;
    }

    public void setUninvoicedAmount(BigDecimal uninvoicedAmount) {
        this.uninvoicedAmount = uninvoicedAmount;
    }

    public String getInvoicingStatus() {
        return invoicingStatus;
    }

    public void setInvoicingStatus(String invoicingStatus) {
        this.invoicingStatus = invoicingStatus;
    }

    public String getElecInvoiceNumber() {
        return elecInvoiceNumber;
    }

    public void setElecInvoiceNumber(String elecInvoiceNumber) {
        this.elecInvoiceNumber = elecInvoiceNumber;
    }

    public String getInvoicingDate() {
        return invoicingDate;
    }

    public void setInvoicingDate(String invoicingDate) {
        this.invoicingDate = invoicingDate;
    }

    public Integer getDueWithinDays() {
        return dueWithinDays;
    }

    public void setDueWithinDays(Integer dueWithinDays) {
        this.dueWithinDays = dueWithinDays;
    }

    public String getPaymentDueDate() {
        return paymentDueDate;
    }

    public void setPaymentDueDate(String paymentDueDate) {
        this.paymentDueDate = paymentDueDate;
    }

    public String getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(String createdDate) {
        this.createdDate = createdDate;
    }

    public String getApplicationUser() {
        return applicationUser;
    }

    public void setApplicationUser(String applicationUser) {
        this.applicationUser = applicationUser;
    }

    public String getTalentName() {
        return talentName;
    }

    public void setTalentName(String talentName) {
        this.talentName = talentName;
    }

    public BigInteger getTalentId() {
        return talentId;
    }

    public void setTalentId(BigInteger talentId) {
        this.talentId = talentId;
    }

    public String getJobName() {
        return jobName;
    }

    public void setJobName(String jobName) {
        this.jobName = jobName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getSocialCreditCode() {
        return socialCreditCode;
    }

    public void setSocialCreditCode(String socialCreditCode) {
        this.socialCreditCode = socialCreditCode;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankAccount() {
        return bankAccount;
    }

    public void setBankAccount(String bankAccount) {
        this.bankAccount = bankAccount;
    }

    public String getInvoicingAddress() {
        return invoicingAddress;
    }

    public void setInvoicingAddress(String invoicingAddress) {
        this.invoicingAddress = invoicingAddress;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getInvoiceArea() {
        return invoiceArea;
    }

    public void setInvoiceArea(String invoiceArea) {
        this.invoiceArea = invoiceArea;
    }

    public String getParticipateName() {
        return participateName;
    }

    public void setParticipateName(String participateName) {
        this.participateName = participateName;
    }

    public String getParticipateRole() {
        return participateRole;
    }

    public void setParticipateRole(String participateRole) {
        this.participateRole = participateRole;
    }

    public String getParticipateUserPercentage() {
        return participateUserPercentage;
    }

    public void setParticipateUserPercentage(String participateUserPercentage) {
        this.participateUserPercentage = participateUserPercentage;
    }

    public String getParticipateUserGp() {
        return participateUserGp;
    }

    public void setParticipateUserGp(String participateUserGp) {
        this.participateUserGp = participateUserGp;
    }

    public String getParticipateCashGp() {
        return participateCashGp;
    }

    public void setParticipateCashGp(String participateCashGp) {
        this.participateCashGp = participateCashGp;
    }

    public String getParticipateTeamLeader() {
        return participateTeamLeader;
    }

    public void setParticipateTeamLeader(String participateTeamLeader) {
        this.participateTeamLeader = participateTeamLeader;
    }

    public String getProfitOwner() {
        return profitOwner;
    }

    public void setProfitOwner(String profitOwner) {
        this.profitOwner = profitOwner;
    }

    public String getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(String paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public BigDecimal getAmountReceivedTax() {
        return amountReceivedTax;
    }

    public void setAmountReceivedTax(BigDecimal amountReceivedTax) {
        this.amountReceivedTax = amountReceivedTax;
    }

    public String getCloseFlag() {
        return closeFlag;
    }

    public void setCloseFlag(String closeFlag) {
        this.closeFlag = closeFlag;
    }

    public String getVoidFlag() {
        return voidFlag;
    }

    public void setVoidFlag(String voidFlag) {
        this.voidFlag = voidFlag;
    }

    public BigInteger getJobId() {
        return jobId;
    }

    public void setJobId(BigInteger jobId) {
        this.jobId = jobId;
    }
}
