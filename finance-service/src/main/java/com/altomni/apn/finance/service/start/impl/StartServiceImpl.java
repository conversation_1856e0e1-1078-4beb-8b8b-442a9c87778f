package com.altomni.apn.finance.service.start.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.CanadaProvinceTaxDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.application.dashboard.MyCandidate;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessUpdateSubstituteTalentDTO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.job.JobPermissionDTO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.CompanyContact;
import com.altomni.apn.finance.config.env.CanadaProvinceListProperties;
import com.altomni.apn.finance.config.env.CanadaProvinceTaxProperties;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.domain.start.*;
import com.altomni.apn.finance.repository.start.*;
import com.altomni.apn.finance.service.application.ApplicationService;
import com.altomni.apn.finance.service.company.CompanyService;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.dto.start.StartSearchInvoicingDTO;
import com.altomni.apn.finance.service.dto.start.StartSearchSimpleDTO;
import com.altomni.apn.finance.service.invoice.InvoiceService;
import com.altomni.apn.finance.service.invoice.InvoiceTypeConfigService;
import com.altomni.apn.finance.service.invoicing.InvoicingApplicationInfoService;
import com.altomni.apn.finance.service.invoicing.InvoicingCandidateInfoService;
import com.altomni.apn.finance.service.job.JobService;
import com.altomni.apn.finance.service.start.StartCommissionService;
import com.altomni.apn.finance.service.start.StartContractRateService;
import com.altomni.apn.finance.service.start.StartService;
import com.altomni.apn.finance.service.talent.TalentService;
import com.altomni.apn.finance.service.vo.invoice.InvoiceDetailInfoVO;
import com.altomni.apn.finance.service.vo.invoicing.InvoiceContractVO;
import com.ipg.resourceserver.client.ClientTokenHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SessionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service Implementation for managing Start.
 */
@Slf4j
@Service
public class StartServiceImpl implements StartService {
    @Resource
    private SessionFactory sessionFactory;
    @Resource
    private StartRepository startRepository;
    @Resource
    private ApplicationService applicationService;
    @Resource
    private JobService jobService;
    @Resource
    private StartCommissionService startCommissionService;
    @Resource
    private TalentService talentService;
    @Resource
    private StartFailedWarrantyRepository startFailedWarrantyRepository;
    @Resource
    private StartTerminationRepository startTerminationRepository;
    @Resource
    private StartAddressRepository startAddressRepository;
    @Resource
    private StartClientInfoRepository startClientInfoRepository;
    @Resource
    private StartContractRateService startContractRateService;
    @Resource
    private StartFteRateRepository startFteRateRepository;
    @Resource
    private StartContractRateRepository startContractRateRepository;
    @Resource
    private StartFteSalaryPackageRepository startFteSalaryPackageRepository;
    @Resource
    private StartCommissionRepository startCommissionRepository;

    @Resource
    InvoiceTypeConfigService invoiceTypeConfigService;

    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Resource
    private CompanyService companyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Resource
    CanadaProvinceTaxProperties canadaProvinceTaxProperties;

    @Resource
    CanadaProvinceListProperties canadaProvinceListProperties;

    @Value("${application.crmUrl}")
    private String crmUrl;

    @Resource
    private InvoicingCandidateInfoService invoicingCandidateInfoService;
    @Resource
    private InvoicingApplicationInfoService invoicingApplicationInfoService;
    @Resource
    private InvoiceService invoiceService;

    private static Integer SEARCH_STARTS_LIMIT_SIZE = 20;

    /**
     * Save a startDto.
     *
     * @param startDto the entity to save
     * @return the persisted entity
     */
    @Override
    @Transactional
    //@GlobalTransactional
    public StartDTO save(StartDTO startDto) throws IOException {
        log.info("save startDto : {}", startDto);
        if (CollectionUtils.isEmpty(startDto.getStartCommissions())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_SAVE_COMMISSIONSNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
//        validateExistsStart(startDto.getTalentId());
        if (startDto.getTalentRecruitmentProcessId() == null) {
            TalentRecruitmentProcessVO talentRecruitmentProcess = getTalentRecruitmentProcess(startDto.getTalentId(), startDto.getJobId());
            startDto.setTalentRecruitmentProcessId(talentRecruitmentProcess.getId());
        }
        setTalentJobCompanyDefaultValues(startDto);
        Start savedStart;
        if (JobType.FULL_TIME == startDto.getPositionType()) {
            if (!StartType.CONVERT_TO_FTE.equals(startDto.getStartType()) && startDto.getWarrantyEndDate() == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_SAVE_WARRANTENDDATENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
            }
            savedStart = createForFte(startDto);
            startDto.setStartType(startDto.getStartType() != null ? startDto.getStartType() : StartType.FTE_NEW_HIRE);
        } else {
            savedStart = createForContract(startDto);
            startDto.setStartType(startDto.getStartType() != null ? startDto.getStartType() : StartType.CONTRACT_NEW_HIRE);
        }
        if (CollectionUtils.isNotEmpty(startDto.getStartCommissions())) {
            startCommissionService.create(savedStart, startDto.getStartCommissions());
        }
        StartDTO result = toDto(savedStart);
        result.setStartAddress(startDto.getStartAddress());
        result.setStartFteRate(startDto.getStartFteRate());
        result.setStartCommissions(startDto.getStartCommissions());
        result.setStartClientInfo(startDto.getStartClientInfo());
        setInvoiceInfo(result,startDto.getStartFteRate());
        return result;
    }

    private void setInvoiceInfo(StartDTO result,StartFteRate startFteRate){
        if(result.getStartClientInfo() != null){
            if (null != result.getStartClientInfo().getInvoiceTypeId() && null != result.getStartClientInfo().getClientInfoId()) {
                InvoiceDetailInfoVO invoiceDetailInfoVO = invoiceTypeConfigService.findInvoiceInfoByTypeId(result.getStartClientInfo().getInvoiceTypeId(), result.getStartClientInfo().getClientInfoId());
                if (null != invoiceDetailInfoVO) {
                    result.setInvoiceDetailInfo(invoiceDetailInfoVO);
                } else {
                    setInvoiceDetailInfo(result);
                }
            } else {
                setInvoiceDetailInfo(result);
            }
            if (StringUtils.isNotBlank(result.getInvoiceDetailInfo().getClientLocation())) {
                String province = CanadaProvinceTaxUtil.containsCanadaProvince(result.getInvoiceDetailInfo().getClientLocation(),canadaProvinceListProperties.getInclude());
                if (result.getInvoiceDetailInfo().getClientLocation().indexOf("Canada") != -1 && province != null) {
                    CanadaProvinceTaxDTO dto = canadaProvinceTaxProperties.getCanadaProvinceTax().get(province);
                    if (startFteRate !=null && startFteRate.getCurrency() == 3) {
                        result.setCanadaProvinceTax(dto.getTaxRate());
                    }
                }
            }
        }
    }

    private Start createForFte(StartDTO startDto) {
        StartFteRate startFteRate = startDto.getStartFteRate();
        Start start = Start.fromStartDTO(startDto);
        start.setCurrency(startDto.getStartFteRate().getCurrency());
        Start savedStart = startRepository.save(start);

        saveStartFteRate(savedStart.getId(), startFteRate);
        saveStartAddress(savedStart.getId(), startDto.getStartAddress());
        saveClientInfo(savedStart.getId(), startDto.getStartClientInfo());

        return savedStart;
    }

    private StartClientInfo saveClientInfo(Long startId, StartClientInfo startClientInfo) {
        if (startClientInfo != null) {
            startClientInfo.setStartId(startId);
            return startClientInfoRepository.save(startClientInfo);
        }
        return null;
    }

    private StartAddress saveStartAddress(Long startId, StartAddress address) {
        if (address != null) {
            StartAddress extensionAddress = new StartAddress();
            BeanUtil.copyProperties(address, extensionAddress, "id", "startId");
            extensionAddress.setStartId(startId);
            if (address.getId() == null) {
                LocationDTO dto = new LocationDTO();
                BeanUtil.copyProperties(address, dto);
                extensionAddress.setOriginalLoc(JsonUtil.toJson(dto));
            }
            return startAddressRepository.save(extensionAddress);
        }
        return null;
    }

    private void saveStartFteRate(Long startId, StartFteRate startFteRate) {
        startFteRate.setStartId(startId);
        startFteRateRepository.save(startFteRate);
        if (CollectionUtils.isNotEmpty(startFteRate.getSalaryPackages())) {
            List<StartFteSalaryPackage> salaryPackages = startFteRate.getSalaryPackages()
                    .stream().peek(startFteSalaryPackage -> startFteSalaryPackage.setStartId(startId))
                    .collect(Collectors.toList());
            startFteSalaryPackageRepository.saveAll(salaryPackages);
        }
    }


    private Start createForContract(StartDTO startDto) {
        if (CollectionUtils.isEmpty(startDto.getStartContractRates())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CREATEFORCONTRACT_CONTRACTRATESNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        StartContractRate startContractRate = startDto.getStartContractRates().get(0);
        Start start = Start.fromStartDTO(startDto);
        start.setCurrency(startContractRate.getCurrency());
        Start savedStart = startRepository.save(start);

        startContractRate.setStartDate(startDto.getStartDate());
        startContractRate.setEndDate(startDto.getEndDate());
        startContractRate.setStartId(savedStart.getId());
        startContractRate.setTenantId(startDto.getTenantId());
        startContractRateService.create(startContractRate);

        saveStartAddress(savedStart.getId(), startDto.getStartAddress());
        saveClientInfo(savedStart.getId(), StartClientInfo.fromStartDTO(startDto));

        return savedStart;
    }

    private void validateExistsStart(Long talentId) {
        Start activeStart = startRepository.findFirstByTalentIdAndStatusOrderByStartDateDesc(talentId, StartStatus.ACTIVE);
        if (activeStart != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATEEXISTSSTART_STARTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(activeStart.getJobId(),activeStart.getJobTitle()),financeApiPromptProperties.getFinanceService()));
        }

    }

    private void setTalentJobCompanyDefaultValues(StartDTO start) {
        TalentV3 talent = getTalent(start.getTalentId());
        start.setTalentName(talent.getFullName());
        JobDTOV3 job = getJobByIdAndTenantId(start.getJobId(),talent.getTenantId());
        log.info("=========job=========: {}", job);
        start.setJobTitle(job.getTitle());
        start.setStatus(start.getStatus() != null ? start.getStatus() : StartStatus.ACTIVE);
        JobType jobType = applicationService.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcess().getId());
        start.setPositionType(start.getPositionType() != null ? start.getPositionType() : jobType);
        start.setCompany(job.getCompany().getName());
        start.setCompanyId(job.getCompany().getId());
        start.setTenantId(job.getTenantId());
    }

    private TalentRecruitmentProcessVO getTalentRecruitmentProcess(Long talentId, Long jobId) {
        TalentRecruitmentProcessVO result = applicationService.getTalentRecruitmentProcessByTalentIdAndJobId(talentId, jobId);
        if (result == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_GETTALENTRECRUITMENTPROCESS_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(talentId),financeApiPromptProperties.getFinanceService()));
        }
        return result;
    }

    private TalentV3 getTalent(Long talentId) {
        TalentV3 talent = talentService.getTalent(talentId);
        if (talent == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_GETTALENT_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(talentId),financeApiPromptProperties.getFinanceService()));
        }
        return talent;
    }

    private JobDTOV3 getJob(Long jobId) {
        JobDTOV3 job = jobService.getJob(jobId);
        if (job == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_GETJOB_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(jobId),financeApiPromptProperties.getFinanceService()));
        }
        return job;
    }

    private JobDTOV3 getJobByIdAndTenantId(Long jobId,Long tenantId) {
        JobDTOV3 job = jobService.getJobByIdAndTenantId(jobId,tenantId);
        if (job == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_GETJOB_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(jobId),financeApiPromptProperties.getFinanceService()));
        }
        return job;
    }

    @Override
    public StartDTO findStartByTalentIdAndStatus(Long talentId, StartStatus status) {
        Start activeStart = startRepository.findFirstByTalentIdAndStatusOrderByStartDateDesc(talentId, status);
        if (activeStart == null) {
            return null;
        }
        StartDTO startDto = new StartDTO();
        BeanUtil.copyProperties(activeStart, startDto);
        return startDto;
    }

    @Override
    public StartDTO findByTalentIdAndStatusOrderByEndDate(Long talentId, StartStatus status) {
        Start activeStart = startRepository.findByTalentIdAndStatusOrderByEndDate(talentId, status.toDbValue());
        if (activeStart == null) {
            return null;
        }
        StartDTO startDto = new StartDTO();
        BeanUtil.copyProperties(activeStart, startDto);
        return startDto;
    }

    /**
     * Update start basic info only
     * @param start Start object
     * @return Start object
     */
    @Override
    @Transactional
    public Start update(Start start) {
        Optional<Start> existingOpt = startRepository.findById(start.getId());
        if (existingOpt.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_UPDATE_STARTISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        Start existing = existingOpt.get();
        // 如果start date有变化，则更新 talent experience
        if (Objects.nonNull(start.getStartDate()) && !start.getStartDate().equals(existing.getStartDate())){
            existing.setStartDate(start.getStartDate());
            existing.setStartAddress(start.getStartAddress());
            this.updateTalentExperience(existing);
        }
        ServiceUtils.myCopyProperties(start, existing, Start.UpdateSkipProperties);
        existing.setChannelPlatform(start.getChannelPlatform());
        existing.setProfitSharingRatio(start.getProfitSharingRatio());
        existing.setSubstituteTalentId(start.getSubstituteTalentId());
        existing.setRelationProcessId(start.getRelationProcessId());
        existing.setIsSubstituteTalent(null != existing.getSubstituteTalentId() ? true : false);

        startRepository.updateOnboardByTalentRecruitmentProcessId(existing.getTalentRecruitmentProcessId(),
                null != existing.getSubstituteTalentId() ? 1 : 0,
                existing.getRelationProcessId(),
                existing.getSubstituteTalentId());

        return startRepository.save(existing);
    }

    @Override
    public void updateTalentExperience(Start start){
        TalentExperienceDTO talentExperienceDTO = new TalentExperienceDTO()
                .setTalentRecruitmentProcessId(start.getTalentRecruitmentProcessId())
                .setLocation(Objects.nonNull(start.getStartAddress()) ? start.getStartAddress().getOriginDisplay() : null)
                .setStartDate(start.getStartDate())
                .setCompanyId(start.getCompanyId())
                .setCompanyName(start.getCompany())
                .setTitle(start.getJobTitle())
                .setCurrent(Boolean.TRUE);
//        if (start.getStartDate().isBefore(LocalDate.now())){
//            talentExperienceDTO.setCurrent(Boolean.TRUE);
//        }
        talentService.updateTalentExperience(start.getTalentId(), talentExperienceDTO);
    }

    @Override
    public void checkPermissionByStartId(Long startId) {
        if (BooleanUtils.isTrue(SecurityUtils.isAdmin())){
            return;
        }
        Long companyId = startRepository.getCompanyIdByStartId(startId);
        this.checkPermissionByCompanyId(companyId);
    }

    @Override
    public void checkPermissionByCompanyId(Long companyId) {
        if (Objects.isNull(companyId)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CHECKPERMISSIONBYCOMPANYID_COMPANYISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        if (BooleanUtils.isTrue(SecurityUtils.isAdmin())){
            return;
        }
        List<Long> allAmIdsByCompanyId = companyService.getAllAmIdsByCompanyId(companyId);
        boolean isCurrentCompanyAm = allAmIdsByCompanyId.contains(SecurityUtils.getUserId());
        if (BooleanUtils.isFalse(isCurrentCompanyAm)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CHECKPERMISSIONBYCOMPANYID_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
    }

    @Override
    public StartDTO toDto(Long startId) {
        return toDto(startRepository.findById(startId).get());
    }

    @Override
    public StartDTO toDto(Start start) {
        if (start == null) {
            return null;
        }
        StartDTO result = new StartDTO();
        ServiceUtils.myCopyProperties(start, result);
        CompletableFuture<StartFteRate> startFteRateCompletableFuture = CompletableFuture.supplyAsync(() -> startFteRateRepository.findByStartId(start.getId()));
        CompletableFuture<List<StartFteSalaryPackage>> salaryPackageCompletableFuture = CompletableFuture.supplyAsync(() -> startFteSalaryPackageRepository.findAllByStartId(start.getId()));
        CompletableFuture<StartClientInfo> startClientInfoCompletableFuture = CompletableFuture.supplyAsync(() -> startClientInfoRepository.findByStartId(start.getId()));
        CompletableFuture<StartAddress> startAddressCompletableFuture = CompletableFuture.supplyAsync(() -> startAddressRepository.findByStartId(start.getId()).orElse(null));
        //CompletableFuture<String> addressCompletableFuture = CompletableFuture.supplyAsync(() -> startAddressRepository.findAddressesFromJob(start.getJobId()));
        CompletableFuture<List<StartCommission>> commissionCompletableFuture = CompletableFuture.supplyAsync(() -> startCommissionService.findByStartId(start.getId()));
        CompletableFuture<StartTermination> startTerminationCompletableFuture = CompletableFuture.supplyAsync(() -> startTerminationRepository.findByStartId(start.getId()));
        CompletableFuture<StartFailedWarranty> startFailedWarrantyCompletableFuture = CompletableFuture.supplyAsync(() -> startFailedWarrantyRepository.findByStartId(start.getId()));
        Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<List<Long>> amCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return companyService.getAllAmIdsByCompanyId(start.getCompanyId());
        });
        CompletableFuture<List<StartContractRate>> contractRateCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return startContractRateService.findAllByStartId(start.getId());
        });
        CompletableFuture<List<TalentRecruitmentProcessKpiUserVO>> kpiUserList = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return applicationService.getKpiUsersByTalentRecruitmentProcessId(start.getTalentRecruitmentProcessId());
        });
        CompletableFuture<Company> companyFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return companyService.getCompany(start.getCompanyId());
        });
        CompletableFuture<List<CompanyContact>> companyContactFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return companyService.getCompanyContacts(start.getCompanyId());
        });

        try {
            result.setCompany(companyFuture.get().getFullBusinessName());
            if(CollUtil.isNotEmpty(companyContactFuture.get())) {
                result.setCompanyPhone(companyContactFuture.get().stream().filter(companyContactDTO -> companyContactDTO.getType()
                        .equals(ContactType.PHONE.toDbValue()))
                        .sorted(Comparator.comparing(CompanyContact::getId))
                        .findFirst().get().getContact());
            }
            result.setStartContractRates(contractRateCompletableFuture.get());
            //result.setStartContractRates(startContractRateService.findAllByStartId(start.getId()));
            result.setIsAm(amCompletableFuture.get().contains(SecurityUtils.getUserId()));
            StartFteRate startFteRate = startFteRateCompletableFuture.get();
            List<StartFteSalaryPackage> salaryPackages = salaryPackageCompletableFuture.get();
            if (CollectionUtils.isNotEmpty(salaryPackages)) {
                startFteRate.setSalaryPackages(salaryPackages);
            }
            if (start.getPositionType().equals(JobType.FULL_TIME)) {
                if(startFteRate != null ){
                    if (startFteRate.getRateUnitType() == null) {
                        startFteRate.setRateUnitType(RateUnitType.YEARLY);
                    }
                }
            }
            result.setStartFteRate(startFteRate);
            result.setKpiUsers(kpiUserList.get());
            result.setStartClientInfo(startClientInfoCompletableFuture.get());
            setInvoiceInfo(result,startFteRate);
            StartAddress startAddress = startAddressCompletableFuture.get();
            if (Objects.nonNull(startAddress)){
                String originalLoc = startAddress.getOriginalLoc();
                LocationDTO locationDTO = StringUtils.isNotEmpty(originalLoc) ? JSON.parseObject(originalLoc, LocationDTO.class) : new LocationDTO();
                String originDisplay = locationDTO.getOriginDisplay();
                if (StringUtils.isEmpty(originalLoc)){
                    originDisplay = startAddress.getLocation();
                }
                if (StringUtils.isEmpty(originDisplay)){
                    originDisplay = Stream.of(startAddress.getAddress(), startAddress.getAddress2(), startAddress.getCity(), startAddress.getProvince(), startAddress.getCountry()).filter(StringUtils::isNotEmpty).collect(Collectors.joining(","));
                }
                startAddress.setOriginDisplay(originDisplay);
            }
            result.setStartAddress(startAddress);

            /*
             if (Objects.isNull(result.getStartAddress())){
                String addressesFromJob = addressCompletableFuture.get();
                if (StringUtils.isNotEmpty(addressesFromJob)){
                    JSONArray jsonArray = JSON.parseArray(addressesFromJob);
                    if (CollUtil.isNotEmpty(jsonArray)) {
                        jsonArray.stream().map(obj -> {
                            String json = JSON.toJSONString(obj);
                            StartAddress startAddress = JSON.parseObject(json, StartAddress.class);
                            startAddress.setOriginalLoc(json);
                            return startAddress;
                        }).findFirst().ifPresent(result::setStartAddress);
                    }
                }
            }
             */
            result.setStartCommissions(commissionCompletableFuture.get());
            result.setTermination(startTerminationCompletableFuture.get());
            result.setFailedWarranty(startFailedWarrantyCompletableFuture.get());
            result.setResignation(applicationService.getResignation(start.getTalentRecruitmentProcessId()));
            if (null != result.getSubstituteTalentId()) {
                TalentV3 talentV3 = talentService.getTalent(result.getSubstituteTalentId());
                result.setSubstituteTalentName(talentV3.getFullName());
            }
        }catch (InterruptedException e){
            log.error(e.getMessage());
            log.error("error", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_COMMON_INTERNETERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }catch (ExecutionException e){
            log.error(e.getMessage());
            log.error("error", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_COMMON_INTERNETERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        return result;
    }

    private void setInvoiceDetailInfo(StartDTO result){
        InvoiceDetailInfoVO vo = new InvoiceDetailInfoVO();
        vo.setId(result.getStartClientInfo().getClientInfoId());
        vo.setClientEmail(result.getStartClientInfo().getClientEmail());
        vo.setClientAddress(result.getStartClientInfo().getClientAddress());
        vo.setClientName(result.getStartClientInfo().getClientName());
        vo.setClientDivision(result.getStartClientInfo().getClientDivision());
        vo.setClientLocation(result.getStartClientInfo().getClientLocation());
        result.setInvoiceDetailInfo(vo);
    }

    @Override
    public StartDTO fillDto(Start start, StartDTO startDTO) {
        //StartDTO result = new StartDTO();
        ServiceUtils.myCopyProperties(start, startDTO);
        CompletableFuture<StartFteRate> startFteRateCompletableFuture = Objects.isNull(startDTO.getStartFteRate()) ? CompletableFuture.supplyAsync(() -> startFteRateRepository.findByStartId(start.getId())) : null;
        CompletableFuture<List<StartFteSalaryPackage>> salaryPackageCompletableFuture = CompletableFuture.supplyAsync(() -> startFteSalaryPackageRepository.findAllByStartId(start.getId()));
        CompletableFuture<StartClientInfo> startClientInfoCompletableFuture = Objects.isNull(startDTO.getStartClientInfo()) ? CompletableFuture.supplyAsync(() -> startClientInfoRepository.findByStartId(start.getId())) : null;
        CompletableFuture<StartAddress> startAddressCompletableFuture = Objects.isNull(startDTO.getStartAddress()) ? CompletableFuture.supplyAsync(() -> startAddressRepository.findByStartId(start.getId()).orElse(null)) : null;
        CompletableFuture<String> addressCompletableFuture = Objects.isNull(startDTO.getStartAddress()) ? CompletableFuture.supplyAsync(() -> startAddressRepository.findAddressesFromJob(start.getJobId())) : null;
        CompletableFuture<List<StartCommission>> commissionCompletableFuture = CollectionUtils.isEmpty(startDTO.getStartCommissions()) ? CompletableFuture.supplyAsync(() -> startCommissionService.findByStartId(start.getId())) : null;
        CompletableFuture<StartTermination> startTerminationCompletableFuture = Objects.isNull(startDTO.getTermination()) ? CompletableFuture.supplyAsync(() -> startTerminationRepository.findByStartId(start.getId())) : null;
        CompletableFuture<StartFailedWarranty> startFailedWarrantyCompletableFuture = Objects.isNull(startDTO.getFailedWarranty()) ? CompletableFuture.supplyAsync(() -> startFailedWarrantyRepository.findByStartId(start.getId())) : null;
        Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<List<Long>> amCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return companyService.getAllAmIdsByCompanyId(start.getCompanyId());
        });
        CompletableFuture<List<StartContractRate>> contractRateCompletableFuture = CollectionUtils.isNotEmpty(startDTO.getStartContractRates()) ? null : CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return startContractRateService.findAllByStartId(start.getId());
        });

        try {
            startDTO.setStartContractRates(Objects.nonNull(contractRateCompletableFuture) ? contractRateCompletableFuture.get() : startDTO.getStartContractRates());
            startDTO.setIsAm(amCompletableFuture.get().contains(SecurityUtils.getUserId()));
            StartFteRate startFteRate = Objects.nonNull(startFteRateCompletableFuture) ? startFteRateCompletableFuture.get() : startDTO.getStartFteRate();
            List<StartFteSalaryPackage> salaryPackages = salaryPackageCompletableFuture.get();
            if (CollectionUtils.isNotEmpty(salaryPackages)) {
                startFteRate.setSalaryPackages(salaryPackages);
            }
            startDTO.setStartFteRate(startFteRate);

            startDTO.setStartClientInfo(Objects.nonNull(startClientInfoCompletableFuture) ? startClientInfoCompletableFuture.get() : startDTO.getStartClientInfo());
            setInvoiceInfo(startDTO, startFteRate);
            startDTO.setStartAddress(Objects.nonNull(startAddressCompletableFuture) ? startAddressCompletableFuture.get() : startDTO.getStartAddress());
            if (Objects.isNull(startDTO.getStartAddress())){
                String addressesFromJob = addressCompletableFuture.get();
                if (StringUtils.isNotEmpty(addressesFromJob)){
                    List<StartAddress> startAddresses = JSONArray.parseArray(addressesFromJob, StartAddress.class);
                    if (CollectionUtils.isNotEmpty(startAddresses)){
                        startDTO.setStartAddress(startAddresses.get(0));
                    }
                }
            }
            startDTO.setStartCommissions(Objects.nonNull(commissionCompletableFuture) ? commissionCompletableFuture.get() : startDTO.getStartCommissions());
            startDTO.setTermination(Objects.nonNull(startTerminationCompletableFuture) ? startTerminationCompletableFuture.get() : startDTO.getTermination());
            startDTO.setFailedWarranty(Objects.nonNull(startFailedWarrantyCompletableFuture) ? startFailedWarrantyCompletableFuture.get() : startDTO.getFailedWarranty());
        }catch (InterruptedException e){
            log.error(e.getMessage());
            log.error("error", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_COMMON_INTERNETERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }catch (ExecutionException e){
            log.error(e.getMessage());
            log.error("error", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_COMMON_INTERNETERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        return startDTO;
    }
    @Override
    public Start saveOnly(Start start) {
        return startRepository.save(start);
    }

    /**
     *  Get one start by id.
     *
     *  @param id the id of the entity
     *  @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public StartDTO findOne(Long id) {
        return toDto(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Start findById(Long id) {
        Optional<Start> startOptional = startRepository.findById(id);
        if (startOptional.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_FINDBYID_STARTNOEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        return startOptional.get();
    }

    @Override
    public List<StartDTO> findByTalent(Long talentId) {
        List<Start> starts = startRepository.findAllByTalentId(talentId);
        if (CollectionUtils.isEmpty(starts)) {
            return Collections.emptyList();
        }
        return starts.stream().map(this::toDto).collect(Collectors.toList());
    }

    @Override
    public StartDTO findByTalentIdAndTalentRecruitmentProcessId(Long talentId,Long talentRecruitmentProcessId,StartStatus status) {
        Start start = startRepository.findByTalentIdAndTalentRecruitmentProcessIdAndStatus(talentId,talentRecruitmentProcessId,status);
        if (null == start) {
            return null;
        }
        return toDto(start);
    }

    @Override
    public List<MyCandidate> getTotalBillAmountByTalentRecruitmentProcessId(List<Long> talentRecruitmentProcessIds) {
        List<StartStatus> contractStartStatus = List.of(StartStatus.ACTIVE, StartStatus.CONTRACT_TERMINATED);
        List<StartStatus> payRollStartStatus = List.of(StartStatus.ACTIVE, StartStatus.CONTRACT_TERMINATED);

        Function<List<StartStatus>, CompletableFuture<Map<Long, List<StartContractRate>>>> getStartContractRateMap = status -> {
           return CompletableFuture.supplyAsync(() -> {
                    return startRepository.findAllByTalentRecruitmentProcessIdInAndPositionTypeAndStatusIn(talentRecruitmentProcessIds, JobType.CONTRACT, contractStartStatus);
                }, executor)
                .thenApplyAsync(starts -> {
                    Map<Long, Long> startProcessMap = starts.stream().collect(Collectors.toMap(Start::getId, Start::getTalentRecruitmentProcessId));
                    return startContractRateRepository.findAllByStartIdIn(startProcessMap.keySet()).stream()
                        .collect(Collectors.groupingBy(o -> startProcessMap.get(o.getStartId()), Collectors.toList()));
                }, executor);
        };
        CompletableFuture<Map<Long, List<StartContractRate>>> contractStartMapFuture = getStartContractRateMap.apply(contractStartStatus);

        CompletableFuture<Map<Long, List<StartContractRate>>> payRollStartMapFuture = getStartContractRateMap.apply(payRollStartStatus);

        CompletableFuture<Map<Long, List<Start>>> fillTimeStartsMapFuture = CompletableFuture.supplyAsync(() -> {
            List<Start> fullTimeStarts = startRepository.findAllByTalentRecruitmentProcessIdInAndPositionType(talentRecruitmentProcessIds, JobType.FULL_TIME);
            return fullTimeStarts.stream().collect(Collectors.groupingBy(Start::getTalentRecruitmentProcessId, Collectors.toList()));
        }, executor);

        CompletableFuture.allOf(contractStartMapFuture, payRollStartMapFuture, fillTimeStartsMapFuture)
            .exceptionally(t -> {
                log.error("Error occurred when fetching start data: ", t);
                throw new ExternalServiceInterfaceException("Error occurred when fetching start data: ");
            }).join();


        Map<Long, List<StartContractRate>> contractRateMap = contractStartMapFuture.join();
        Map<Long, List<StartContractRate>> payRollRateMap = payRollStartMapFuture.join();
        Map<Long, List<Start>> fullTimeStartsMap = fillTimeStartsMapFuture.join();
        List<Long> fteRateIds = talentRecruitmentProcessIds.stream().filter(fullTimeStartsMap::containsKey).map(fullTimeStartsMap::get)
            .filter(CollUtil::isNotEmpty).map(list -> list.get(0).getId()).toList();
        Map<Long, StartFteRate> fteRateMap = startFteRateRepository.findAllByStartIdIn(fteRateIds).stream().collect(Collectors.toMap(StartFteRate::getStartId, Function.identity()));

        return talentRecruitmentProcessIds.stream().map(talentRecruitmentProcessId -> {
            MyCandidate result = new MyCandidate();
            result.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
            List<StartContractRate> contractStart = contractRateMap.getOrDefault(talentRecruitmentProcessId, Collections.emptyList());
            if (CollUtil.isNotEmpty(contractStart)) {
                result.setTotalBillAmount(sumAllTotalBillAmount(contractStart));
                result.setCurrency(contractStart.get(0).getCurrency());
            }
            List<StartContractRate> payRollStart = payRollRateMap.getOrDefault(talentRecruitmentProcessId, Collections.emptyList());
            if (CollUtil.isNotEmpty(payRollStart)) {
                result.setTotalBillAmount(sumAllTotalBillAmount(payRollStart));
                result.setCurrency(payRollStart.get(0).getCurrency());
            }
            List<Start> fullTimeStarts = fullTimeStartsMap.getOrDefault(talentRecruitmentProcessId, Collections.emptyList());

            if (CollectionUtils.isNotEmpty(fullTimeStarts)) {
                StartFteRate fteRate = fteRateMap.get(fullTimeStarts.get(0).getId());
                result.setTotalBillAmount(fteRate != null ? fteRate.getTotalBillAmount() : null);
                result.setCurrency(fteRate != null ? fteRate.getCurrency() : null);
            }
            return result;
        }).toList();
    }

    private BigDecimal sumAllTotalBillAmount(List<StartContractRate> contractRates) {
        return contractRates.stream().map(StartContractRate::getTotalBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<StartSearchSimpleDTO> searchStarts(String talentName) {
        return startRepository.findByTenantIdAndPositionTypeAndTalentNameIsLike(SecurityUtils.getTenantId(), JobType.FULL_TIME.toDbValue(), talentName, SEARCH_STARTS_LIMIT_SIZE)
                .stream().map(StartSearchSimpleDTO::fromStart)
                .collect(Collectors.toList());
    }

    @Override
    public List<StartSearchInvoicingDTO> searchInvoicingStarts(String talentName) {
        List<StartSearchInvoicingDTO> dtoList = startRepository.findByTenantIdAndPositionTypeAndUserIdAndTalentNameIsLike(SecurityUtils.getTenantId(),
                        talentName,SecurityUtils.getUserId(), SEARCH_STARTS_LIMIT_SIZE)
                .stream().map(StartSearchInvoicingDTO::fromStart)
                .collect(Collectors.toList());
        List<Long> jobIds = dtoList.stream().map(StartSearchInvoicingDTO::getJobId).collect(Collectors.toList());
        List<JobPermissionDTO> jobPermissionDTOS = jobService.checkJobPermissionById(jobIds);
        if (null != jobPermissionDTOS) {
            Map<Long, JobPermissionDTO> permisssionMap = jobPermissionDTOS.stream().collect(Collectors.toMap(JobPermissionDTO::getId, x -> x));
            dtoList.forEach(x -> {
                if (permisssionMap.containsKey(x.getJobId())) {
                    x.setJobPermission(permisssionMap.get(x.getJobId()).getHavePermission());
                    x.setPrivateJob(permisssionMap.get(x.getJobId()).getPrivateJob());
                }
            });
        }
        return dtoList;
    }

    @Override
    public StartSearchInvoicingDTO getStartId(Long startId) {

        Start start = startRepository.findById(startId).get();
        StartSearchInvoicingDTO dto = StartSearchInvoicingDTO.fromStart(start);

        StartFteRate startFteRate = startFteRateRepository.findByStartId(start.getId());
        BigDecimal gpAmount = startFteRate.getTotalBillAmount();
        dto.setCurrency(startFteRate.getCurrency());

        if (!startFteRate.getCurrency().equals(CurrencyConstants.CNY.intValue())) {
            return new StartSearchInvoicingDTO();
        }

        dto.setChinaOrder(false);
        Optional<StartAddress> startAddress = startAddressRepository.findByStartId(start.getId());
        if (startAddress.isPresent()) {
            if (startAddress.get().getCountry().equals("CHN")) {
                dto.setChinaOrder(true);
            }
        }

        //TODO 查询开票候选人使用的金额
        BigDecimal useAmount = invoicingCandidateInfoService.getAmountReceived(start.getId(), start.getTalentId(), start.getJobId());

        if (gpAmount == null) {
            gpAmount = BigDecimal.ZERO;
        }

        if (!useAmount.equals(BigDecimal.ZERO) && !gpAmount.equals(BigDecimal.ZERO)) {
            gpAmount = gpAmount.subtract(useAmount);
        }
        dto.setGpAmount(gpAmount.toString());

        if (gpAmount.equals(BigDecimal.ZERO) || gpAmount.equals(new BigDecimal("0.00"))) {
            return new StartSearchInvoicingDTO();
        }

        if (gpAmount.compareTo(new BigDecimal("0.00")) == -1) {
            return new StartSearchInvoicingDTO();
        }

        JobDTOV3 jobDTOV3 = jobService.getJob(start.getJobId());
        if (null != jobDTOV3) {
            Long salesLeadId = jobDTOV3.getSalesLeadId();
            if (salesLeadId != null) {
                dto.setRelationSalesLead(true);
                String url = getCrmUrl(start.getCompanyId());
                String authorizationHeader = "Bearer " + ClientTokenHolder.getInstance().getClientToken().access_token();
                cn.hutool.http.HttpResponse response = cn.hutool.http.HttpUtil.createGet(url)
                        .header("Authorization", authorizationHeader)
                        .execute();

                if (response.isOk()) {
                    List<InvoiceContractVO> contractVOS = JSONUtil.toList(JSONUtil.parseArray(response.body()), InvoiceContractVO.class);
                    if (null == contractVOS && contractVOS.isEmpty()) {
                        dto.setContractStatus(false);
                    } else {
                        List<Long> idList = contractVOS.stream().map(InvoiceContractVO::getAccountBusinessId).collect(Collectors.toList());
                        if (idList.contains(salesLeadId)) {
                            dto.setContractStatus(true);
                        } else {
                            dto.setContractStatus(false);
                        }
                    }
                }
            } else {
                dto.setRelationSalesLead(false);
            }
        } else {
            dto.setContractStatus(false);
        }

        JobPermissionDTO jobPermissionDTO = jobService.checkJobPermissionByChinaInvoicing(start.getJobId());
        if (null != jobPermissionDTO) {
            dto.setJobPermission(jobPermissionDTO.getHavePermission());
            dto.setPrivateJob(jobPermissionDTO.getPrivateJob());
        }
        return dto;
    }

    private final String CRM_ACCOUNT_COMPANY_ID = "/account/api/v1/contracts/companyId/";

    private String getCrmUrl(Long id) {
        return crmUrl + CRM_ACCOUNT_COMPANY_ID + id;
        //return "http://localhost:9028" +  CRM_ACCOUNT_COMPANY_ID + id;
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = sra.getRequest();
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }

    @Transactional
    @Override
    public void updateStartStatusWithEliminatedCandidate(Long talentRecruitmentProcessId) {
        startRepository.updateStatusByTalentRecruitmentProcessId(StartStatus.ACTIVE, StartStatus.ELIMINATED, talentRecruitmentProcessId);
    }

    @Transactional
    @Override
    public void updateStartStatusWithCancelEliminatedCandidate(Long talentRecruitmentProcessId) {
        startRepository.updateStatusByTalentRecruitmentProcessId(StartStatus.ELIMINATED, StartStatus.ACTIVE, talentRecruitmentProcessId);
    }

    @Override
    public Set<Long> filterTalentsWithActiveFteStarts(Set<Long> talentIds) {
        return startRepository.filterTalentsWithActiveFteStarts(talentIds, JobType.FULL_TIME.toDbValue());
    }

    @Override
    public StartDTO findByTalentRecruitmentProcessIdAndStatus(Long talentRecruitmentProcessId, StartStatus status) {
        Optional<Start> optionalStart = startRepository.findByTalentRecruitmentProcessIdAndStatus(talentRecruitmentProcessId, status);
        if (optionalStart.isEmpty()) {
            return null;
        }
        Start start = optionalStart.get();
        StartDTO startDto = new StartDTO();
        BeanUtil.copyProperties(start, startDto);
        startDto.setStartAddress(startAddressRepository.findByStartId(start.getId()).orElse(null));
        return startDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public cn.hutool.json.JSONArray deleteStartByJobId(Long jobId) {
        cn.hutool.json.JSONArray startArray = new cn.hutool.json.JSONArray();
        List<Start> startList = startRepository.findAllByJobId(jobId);
        List<Long> startIds = startList.stream().map(Start::getId).toList();
        List<StartTermination> startTerminationList = startTerminationRepository.findAllByStartIdIn(startIds);
        List<StartContractRate> startContractRateList = startContractRateRepository.findAllByStartIdIn(startIds);
        List<StartFteRate> startFteRateList = startFteRateRepository.findAllByStartIdIn(startIds);
        List<StartAddress> startAddressList = startAddressRepository.findAllByStartIdIn(startIds);
        List<StartClientInfo> startClientInfoList = startClientInfoRepository.findAllByStartIdIn(startIds);
        List<StartFteSalaryPackage> startFteSalaryPackageList = startFteSalaryPackageRepository.findAllByStartIdIn(startIds);
        List<StartFailedWarranty> startFailedWarrantyList = startFailedWarrantyRepository.findAllByStartIdIn(startIds);
        List<StartCommission> startCommissionList = startCommissionRepository.findAllByStartIdIn(startIds);
        Map<Long, List<StartTermination>> startTerminationMap = startTerminationList.stream().collect(Collectors.groupingBy(StartTermination::getStartId));
        Map<Long, List<StartContractRate>> startContractRateMap = startContractRateList.stream().collect(Collectors.groupingBy(StartContractRate::getStartId));
        Map<Long, List<StartFteRate>> startFteRateMap = startFteRateList.stream().collect(Collectors.groupingBy(StartFteRate::getStartId));
        Map<Long, List<StartAddress>> startAddressMap = startAddressList.stream().collect(Collectors.groupingBy(StartAddress::getStartId));
        Map<Long, List<StartClientInfo>> startClientInfoMap = startClientInfoList.stream().collect(Collectors.groupingBy(StartClientInfo::getStartId));
        Map<Long, List<StartFteSalaryPackage>> startFteSalaryPackageMap = startFteSalaryPackageList.stream().collect(Collectors.groupingBy(StartFteSalaryPackage::getStartId));
        Map<Long, List<StartFailedWarranty>> startFailedWarrantyMap = startFailedWarrantyList.stream().collect(Collectors.groupingBy(StartFailedWarranty::getStartId));
        Map<Long, List<StartCommission>> startCommissionMap = startCommissionList.stream().collect(Collectors.groupingBy(StartCommission::getStartId));
        startList.forEach(o -> {
            JSONObject item = DtoToJsonUtil.toJsonWithColumnNames(o);
            item.put("start_termination", DtoToJsonUtil.toJsonArrayWithColumnNames(startTerminationMap.get(o.getId())));
            item.put("start_contract_rate", DtoToJsonUtil.toJsonArrayWithColumnNames(startContractRateMap.get(o.getId())));
            item.put("start_fte_rate", DtoToJsonUtil.toJsonArrayWithColumnNames(startFteRateMap.get(o.getId())));
            item.put("start_address", DtoToJsonUtil.toJsonArrayWithColumnNames(startAddressMap.get(o.getId())));
            item.put("start_client_info", DtoToJsonUtil.toJsonArrayWithColumnNames(startClientInfoMap.get(o.getId())));
            item.put("start_fte_salary_package", DtoToJsonUtil.toJsonArrayWithColumnNames(startFteSalaryPackageMap.get(o.getId())));
            item.put("start_failed_warranty", DtoToJsonUtil.toJsonArrayWithColumnNames(startFailedWarrantyMap.get(o.getId())));
            item.put("start_commission", DtoToJsonUtil.toJsonArrayWithColumnNames(startCommissionMap.get(o.getId())));
            startArray.add(item);
        });
        startRepository.deleteAllByIdInBatch(startIds);
        startTerminationRepository.deleteAllByIdInBatch(startTerminationList.stream().map(StartTermination::getId).toList());
        startContractRateRepository.deleteAllByIdInBatch(startContractRateList.stream().map(StartContractRate::getId).toList());
        startFteRateRepository.deleteAllByIdInBatch(startFteRateList.stream().map(StartFteRate::getId).toList());
        startAddressRepository.deleteAllByIdInBatch(startAddressList.stream().map(StartAddress::getId).toList());
        startClientInfoRepository.deleteAllByIdInBatch(startClientInfoList.stream().map(StartClientInfo::getId).toList());
        startFteSalaryPackageRepository.deleteAllByIdInBatch(startFteSalaryPackageList.stream().map(StartFteSalaryPackage::getId).toList());
        startFailedWarrantyRepository.deleteAllByIdInBatch(startFailedWarrantyList.stream().map(StartFailedWarranty::getId).toList());
        startCommissionRepository.deleteAllByIdInBatch(startCommissionList.stream().map(StartCommission::getId).toList());
        deleteInvoiceByStart(startIds, startArray);
        return startArray;
    }

    private void deleteInvoiceByStart(List<Long> startIds, cn.hutool.json.JSONArray result) {
        cn.hutool.json.JSONArray invoices = invoiceService.deleteInvoiceByStart(startIds);
        result.addAll(invoices);
        cn.hutool.json.JSONArray invoicingList = invoicingApplicationInfoService.deleteByStarts(startIds);
        result.addAll(invoicingList);
    }
}
