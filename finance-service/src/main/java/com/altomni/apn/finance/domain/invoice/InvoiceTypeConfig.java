package com.altomni.apn.finance.domain.invoice;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityTypeConverter;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceInfoType;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceInfoTypeConverter;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "invoice_type_config")
public class InvoiceTypeConfig extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**  */
    @Column(name = "name")
    private String name ;

    /**  */
    @Column(name = "label")
    private String label ;

    /** 0中国区 1海外 开票信息类型*/
    @Column(name = "invoice_info_type")
    @Convert(converter = InvoiceInfoTypeConverter.class)
    private InvoiceInfoType invoiceInfoType ;

    /**  */
    @Column(name = "tenant_id")
    private Long tenantId ;

    /** 状态 0-无效 1-有效 */
    @Column(name = "status")
    private Integer status ;
}
