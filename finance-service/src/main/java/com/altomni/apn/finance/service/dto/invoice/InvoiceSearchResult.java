package com.altomni.apn.finance.service.dto.invoice;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class InvoiceSearchResult implements Serializable {

    private static final long serialVersionUID = 8466222594248724263L;

    private List<InvoiceCurrencyAmount> currencyAmounts = new ArrayList<>();

    private List<InvoiceDTO> elements = new ArrayList<>();

    public List<InvoiceCurrencyAmount> getCurrencyAmounts() {
        return currencyAmounts;
    }

    public void setCurrencyAmounts(List<InvoiceCurrencyAmount> currencyAmounts) {
        this.currencyAmounts = currencyAmounts;
    }

    public List<InvoiceDTO> getElements() {
        return elements;
    }

    public void setElements(List<InvoiceDTO> elements) {
        this.elements = elements;
    }

    @Override
    public String toString() {
        return "InvoiceSearchResult{" +
            "currencyAmounts=" + currencyAmounts +
            ", elements=" + elements +
            '}';
    }
}
