package com.altomni.apn.finance.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class InvoicePaymentMethodConverter extends AbstractAttributeConverter<InvoicePaymentMethod, Integer> {
    public InvoicePaymentMethodConverter() {
        super(InvoicePaymentMethod::toDbValue, InvoicePaymentMethod::fromDbValue);
    }
}
