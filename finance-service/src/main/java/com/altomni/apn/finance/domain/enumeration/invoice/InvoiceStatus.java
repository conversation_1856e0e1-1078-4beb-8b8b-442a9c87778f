package com.altomni.apn.finance.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The InvoiceStatus enumeration.
 */
public enum InvoiceStatus implements ConvertedEnum<Integer> {
    PAID(0),
    UNPAID(1),
    OVERDUE(2),
    // for startup fee status
    STARTUP_FEE_PAID_USED(3),
    STARTUP_FEE_PAID_UNUSED(4),
    STARTUP_FEE_UNPAID_UNUSED(6),
    // for startup fee status
    VOID(7),
    PARTIALLY_PAID(8);

    private final Integer dbValue;

    InvoiceStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<InvoiceStatus, Integer> resolver =
        new ReverseEnumResolver<>(InvoiceStatus.class, InvoiceStatus::toDbValue);

    public static InvoiceStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
