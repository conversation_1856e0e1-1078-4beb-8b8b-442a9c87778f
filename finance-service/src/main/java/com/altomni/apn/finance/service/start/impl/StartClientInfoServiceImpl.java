package com.altomni.apn.finance.service.start.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.start.StartClientInfo;
import com.altomni.apn.finance.repository.start.StartClientInfoRepository;
import com.altomni.apn.finance.service.invoice.InvoiceTypeConfigService;
import com.altomni.apn.finance.service.start.StartClientInfoService;
import com.altomni.apn.finance.service.vo.invoice.InvoiceDetailInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;

@Service
@Transactional
public class StartClientInfoServiceImpl implements StartClientInfoService {

    private final Logger log = LoggerFactory.getLogger(StartClientInfoServiceImpl.class);

    @Resource
    private StartClientInfoRepository startClientInfoRepository;

    @Resource
    InvoiceTypeConfigService invoiceTypeConfigService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public InvoiceDetailInfoVO update(StartClientInfo startClientInfo) {
        StartClientInfo exists = findByStartId(startClientInfo.getStartId());
        if (exists == null) {
            startClientInfoRepository.save(startClientInfo);
        }
        ServiceUtils.myCopyProperties(startClientInfo, exists);
        startClientInfoRepository.save(exists);
        return findInvoiceByStartId(startClientInfo.getStartId());
    }

    @Override
    public StartClientInfo findByStartId(Long startId) {
        StartClientInfo startClientInfo = startClientInfoRepository.findByStartId(startId);
        if (startClientInfo == null) {
            return null;
            //throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_FINDBYSTARTID_STARTCLIENTISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(startId),financeApiPromptProperties.getFinanceService()));
        }
        return startClientInfo;
    }

    @Override
    public InvoiceDetailInfoVO findInvoiceByStartId(Long startId) {
        StartClientInfo startClientInfo = startClientInfoRepository.findByStartId(startId);
        if (startClientInfo == null) {
            return null;
            //throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_FINDBYSTARTID_STARTCLIENTISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(startId),financeApiPromptProperties.getFinanceService()));
        }
        if (null != startClientInfo.getInvoiceTypeId() && null != startClientInfo.getClientInfoId()) {
            InvoiceDetailInfoVO invoiceDetailInfoVO = invoiceTypeConfigService.findInvoiceInfoByTypeId(startClientInfo.getInvoiceTypeId(), startClientInfo.getClientInfoId());
            if (null != invoiceDetailInfoVO) {
                invoiceDetailInfoVO.setInvoiceTypeId(startClientInfo.getInvoiceTypeId());
                return invoiceDetailInfoVO;
            }
        }
        return null;
    }
}
