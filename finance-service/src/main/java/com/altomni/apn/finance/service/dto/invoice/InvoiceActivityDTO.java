package com.altomni.apn.finance.service.dto.invoice;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityType;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityTypeConverter;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoicePaymentMethod;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoicePaymentMethodConverter;
import com.altomni.apn.finance.domain.invoice.InvoiceActivity;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * A InvoiceActivityDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class InvoiceActivityDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = -9008110985396585561L;

    private Long id;

    @ApiModelProperty(value = "Invoice id for easy search. Read Only.")
    private Long invoiceId;

    private String invoiceNo;

    @Convert(converter = InvoiceActivityTypeConverter.class)
    private InvoiceActivityType invoiceActivityType;

    @ApiModelProperty(value = "User id.", required = true)
    private Long userId;

    @ApiModelProperty(value = "User full name.", required = true)
    private String userFullName;

    private BigDecimal amount;

    private String note;

    // for payment record
    private Long paymentId;

    @ApiModelProperty(value = "The date of received payment.")
    private LocalDate paymentDate;

    @ApiModelProperty(value = "Close or not this payment without a full payment. ")
    private Boolean closeWithoutFullPayment;

    @ApiModelProperty(value = "Amount payment method.")
    @Convert(converter = InvoicePaymentMethodConverter.class)
    private InvoicePaymentMethod paymentMethod;

    @ApiModelProperty(value = "Currency of payment")
    private Integer currency;

    @ApiModelProperty(value = "Conversion rate between collection currency and invoice currency. " +
            "eg: if collection currency == USD,  invoice currency == CAD ,  CAD:USD = 1.35:1" +
            "exchangeRate = invoice currency : collection currency = 1.35:1")
    private String exchangeRate;


    // for payment record

    public static InvoiceActivityDTO fromInvoiceActivity(InvoiceActivity invoiceActivity) {
        InvoiceActivityDTO activityDTO = new InvoiceActivityDTO();
        activityDTO.setId(invoiceActivity.getId());
        activityDTO.setInvoiceId(invoiceActivity.getInvoiceId());
        activityDTO.setInvoiceNo(invoiceActivity.getInvoiceNo());
        activityDTO.setUserId(invoiceActivity.getUserId());
        activityDTO.setUserFullName(invoiceActivity.getUserFullName());
        activityDTO.setInvoiceActivityType(invoiceActivity.getInvoiceActivityType());
        activityDTO.setNote(invoiceActivity.getNote());
        activityDTO.setAmount(invoiceActivity.getAmount());
        activityDTO.setPaymentId(invoiceActivity.getPaymentId());
        activityDTO.setCreatedDate(invoiceActivity.getCreatedDate());
        return activityDTO;
    }
}
