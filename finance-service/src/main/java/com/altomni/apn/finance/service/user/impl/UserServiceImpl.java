package com.altomni.apn.finance.service.user.impl;

import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.finance.service.user.UserClient;
import com.altomni.apn.finance.service.user.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class UserServiceImpl implements UserService {

    private final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    @Resource
    private UserClient userClient;

    @Override
    public String getUserFullName(Long userId) {
        ResponseEntity<User> response = userClient.findById(userId);
        return (response != null && response.getBody() != null) ? response.getBody().getFirstName() + " " + response.getBody().getLastName() : null;
    }

    @Override
    public List<User> findALLByIdInAndActivated(List<Long> userIds) {
        ResponseEntity<List<User>> response = userClient.findALLByIdInAndActivated(userIds);
        return (response != null && response.getBody() != null) ? response.getBody() : null;
    }

    @Override
    public String getUserEmail(Long userId) {
        ResponseEntity<User> response = userClient.findById(userId);
        return (response != null && response.getBody() != null) ? response.getBody().getEmail() : null;
    }

    @Override
    public Long findLevel1TeamIdByUserId(Long userId) {
        ResponseEntity<Long> response = userClient.findLevel1TeamIdByUserId(userId);
        return (response != null && response.getBody() != null) ? response.getBody() : null;
    }

    @Override
    public Long findProfitTeamLeaderIdByUserId(Long userId) {
        ResponseEntity<Long> response = userClient.findProfitTeamLeaderIdByUserId(userId);
        return (response != null && response.getBody() != null) ? response.getBody() : null;
    }

}
