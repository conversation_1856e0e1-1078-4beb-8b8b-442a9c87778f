package com.altomni.apn.finance.utils;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.company.InvoicingStatus;
import com.altomni.apn.finance.service.dto.invoicing.InvoicingApplicationInfoDTO;
import com.altomni.apn.job.service.dto.job.JobEsSyncDocument;

import java.security.MessageDigest;
import java.util.*;

import static java.util.Arrays.asList;

public class InvoicingUtils {

    public static final List<InvoicingStatus> voidInvoicingStatus = Arrays.asList(InvoicingStatus.INVOICED, InvoicingStatus.UNCOLLECTED, InvoicingStatus.OVERDUE, InvoicingStatus.PARTIALLY_COLLECTED, InvoicingStatus.FULLY_COLLECTED);
    public static final List<InvoicingStatus> cancelInvoicingStatus = Arrays.asList(InvoicingStatus.PENDING_APPROVAL, InvoicingStatus.REJECTED_APPROVAL);
    public static final List<InvoicingStatus> paymentInvoicingStatus = Arrays.asList(InvoicingStatus.UNCOLLECTED, InvoicingStatus.PARTIALLY_COLLECTED, InvoicingStatus.OVERDUE);
    public static final List<InvoicingStatus> paymentModifyInvoicingStatus = Arrays.asList(InvoicingStatus.PARTIALLY_COLLECTED, InvoicingStatus.OVERDUE, InvoicingStatus.FULLY_COLLECTED,InvoicingStatus.OUTSTANDING_CLOSE);
    public static final List<InvoicingStatus> recordInvoicingStatus = Arrays.asList(InvoicingStatus.PENDING_INVOICING,InvoicingStatus.UNCOLLECTED,InvoicingStatus.PARTIALLY_COLLECTED, InvoicingStatus.OVERDUE, InvoicingStatus.FULLY_COLLECTED,InvoicingStatus.VOIDED);

    public static final List<InvoicingStatus> voidRecordInvoicingStatus = Arrays.asList(InvoicingStatus.PENDING_INVOICING,InvoicingStatus.INVOICED,InvoicingStatus.UNCOLLECTED,InvoicingStatus.PARTIALLY_COLLECTED, InvoicingStatus.OVERDUE, InvoicingStatus.FULLY_COLLECTED,InvoicingStatus.VOIDED);

    public static final List<InvoicingStatus> cancelPayment = Arrays.asList(InvoicingStatus.PENDING_APPROVAL, InvoicingStatus.REJECTED_APPROVAL, InvoicingStatus.PENDING_INVOICING,InvoicingStatus.INVOICED);

    public static final List<InvoicingStatus> outstandingCloseInvoicingStatus = Arrays.asList(InvoicingStatus.UNCOLLECTED, InvoicingStatus.PARTIALLY_COLLECTED, InvoicingStatus.OVERDUE,InvoicingStatus.OUTSTANDING_CLOSE);


    public static final List<String> INVOICING_DOCUMENTS_HEADERS =
            Collections.unmodifiableList(asList("开票编码","审批提交日期",
                    "付税方",
                    "申请类型",
                    "申请人",
                    "候选人姓名",
                    "岗位名称",
                    "客户名称",
                    "海外成单",
                    "开票业务类型",
                    "开票服务名称",
                    "开票状态",
                    "入职金额",
                    "应开金额",
                    "应收金额（含税）",
                    "应开总金额",
                    "税率",
                    "税额",
                    "开票总金额（含税）",
                    "未开票金额",
                    "数电票编码",
                    "开票日期",
                    "账期",
                    "付款到期日期",
                    "客户企业名称",
                    "统一社会信用代码",
                    "客户开户银行",
                    "客户银行账号",
                    "开票地址",
                    "客户电话号码",
                    "我司开票主体",
                    "发票类型",
                    "所需发票格式",
                    "预付金使用金额",
                    "回款日期",
                    "回款金额",
                    "回款方式",
                    "剩余未回款金额",
                    "回款备注",
                    "参与者名称",
                    "参与者角色",
                    "参与用户分成比例",
                    "参与用户GP",
                    "参与用户Cash-in GP",
                    "参与用户 Team Leader",
                    "利润中心负责人"));

    public static final List<String> INVOICING_DOCUMENTS_FIELDS =
            Collections.unmodifiableList(asList("codeNumber","createdDate",
                    "taxPayerType",//付税方
                    "invoicingApplicationType",//申请类型
                    "applicationUser",//申请人
                    "talentName",//候选人姓名
                    "jobName",//岗位名称
                    "companyName",//客户名称
                    "invoiceArea",//海外成单
                    "invoicingBusinessType",//开票业务类型
                    "invoicingTaxName",//开票服务名称
                    "invoicingStatus",//开票状态
                    "talentGpAmount",//入职金额,
                    "amountReceived",//应该金额 即前端展示数据【不含税金额】 amountReceived
                    "amountReceivedTax",//应收金额（含税）
                    "gpAmount",//应开总金额
                    "invoicingTaxStr",//税率
                    "invoiceTax",//税额
                    "invoicingAmount",//开票总金额（含税）
                    "uninvoicedAmount",//未开票金额
                    "elecInvoiceNumber",//数电票编码
                    "invoicingDate",//开票日期
                    "dueWithinDays",//账期
                    "paymentDueDate",//付款到期日期
                    "clientName",//客户企业名称
                    "socialCreditCode",//统一社会信用代码
                    "bankName",//客户开户银行
                    "bankAccount",//客户银行账号
                    "invoicingAddress",//开票地址
                    "phone",//客户电话号码
                    "invoicingBody",//我司开票主体
                    "invoicingType",//发票类型
                    "invoiceFormat",//所需发票格式
                    "prepaymentAmount",//预付金使用金额
                    "paymentDate",//回款日期
                    "paymentAmount",//回款金额
                    "paymentMethod",//回款方式
                    "amountDue",//剩余未回款金额
                    "note",//回款备注
                    "participateName",//参与者名称
                    "participateRole",//参与者角色
                    "participateUserPercentage",//参与用户分成比例
                    "participateUserGp",//参与用户GP
                    "participateCashGp",//参与用户Cash-in GP
                    "participateTeamLeader",//参与用户 Team Leader
                    "profitOwner"//利润中心负责人
                    ));

    public static String getHashValue(InvoicingApplicationInfoDTO dto) {
        StringBuilder sb = new StringBuilder();
        try {
            MessageDigest object = MessageDigest.getInstance("SHA-256");
            byte[] encrypted = object.digest(JSONUtil.toJsonStr(dto).getBytes("UTF-8"));
            for (byte b : encrypted) {
                sb.append(String.format("%02x", b));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sb.toString();
    }
}
