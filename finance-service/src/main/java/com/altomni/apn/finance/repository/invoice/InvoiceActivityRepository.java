package com.altomni.apn.finance.repository.invoice;

import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityType;
import com.altomni.apn.finance.domain.invoice.InvoiceActivity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;


/**
 * Spring Data JPA repository for the InvoiceActivity entity.
 */
@SuppressWarnings("unused")
@Repository
public interface InvoiceActivityRepository extends JpaRepository<InvoiceActivity, Long> {

    List<InvoiceActivity> findByInvoiceId(Long invoiceId);

    List<InvoiceActivity> findAllByInvoiceIdIn(Collection<Long> invoiceIds);

    InvoiceActivity findByInvoiceIdAndInvoiceActivityType(Long invoiceId, InvoiceActivityType activityType);
}
