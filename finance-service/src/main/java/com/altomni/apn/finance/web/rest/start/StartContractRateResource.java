package com.altomni.apn.finance.web.rest.start;

import cn.hutool.core.bean.BeanUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.domain.start.StartContractRate;
import com.altomni.apn.finance.service.start.StartContractRateService;
import com.altomni.apn.finance.service.vo.StartContractRateCheckVO;
import com.altomni.apn.finance.web.rest.dto.StartContractRateInput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing StartContractRate.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StartContractRateResource {

    private static final String ENTITY_NAME = "StartContractRate";

    @Resource
    private StartContractRateService startContractRateService;


    @PostMapping("/start-contract-rates/startId/{startId}")
    @NoRepeatSubmit
    public ResponseEntity<List<StartContractRate>> rateChange(@PathVariable Long startId, @RequestBody StartContractRateInput startContractRateInput) throws URISyntaxException {
        log.info("[APN: StartContractRate @{}] REST request to creat StartContractRate : {}, {}", SecurityUtils.getUserId(), startId, startContractRateInput);
        StartContractRate startContractRate = new StartContractRate();
        BeanUtil.copyProperties(startContractRateInput, startContractRate);
        checkRate(startContractRate);
        startContractRate.setStartId(startId);
        List<StartContractRate> result = startContractRateService.rateChange(startContractRate);
        return ResponseEntity.created(new URI("/api/v3/start-contract-rates/startId/" + startId))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, startId.toString()))
            .body(result);
    }

    private void checkRate(StartContractRate startContractRate){
        if (null != startContractRate) {
            if (startContractRate.getFinalBillRate().compareTo(new BigDecimal("99999999")) > 0) {
                throw new CustomParameterizedException("The value cannot exceed 99999999");
            }

            if (startContractRate.getFinalPayRate().compareTo(new BigDecimal("99999999")) > 0) {
                throw new CustomParameterizedException("The value cannot exceed 99999999");
            }
        }
    }

    @PutMapping("/start-contract-rates/{id}")
    public ResponseEntity<List<StartContractRate>> update(@PathVariable Long id, @RequestBody StartContractRate startContractRate) {
        log.info("[APN: StartContractRate @{}] REST request to update StartContractRate : {}, {}", SecurityUtils.getUserId(), id, startContractRate);
        checkRate(startContractRate);
        startContractRate.setId(id);
        List<StartContractRate> result = startContractRateService.update(startContractRate);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, id.toString()))
            .body(result);
    }


    @GetMapping("/start-contract-rates/startId/{startId}")
    public List<StartContractRate> getAllStartContractRates(@PathVariable Long startId) {
        log.info("[APN: StartContractRate @{}] REST request to get all StartContractRates : {}", SecurityUtils.getUserId(), startId);
        return startContractRateService.findAllByStartId(startId);
    }

    @GetMapping("/start-contract-rates/{id}")
    public ResponseEntity<StartContractRate> getStartContractRate(@PathVariable Long id) {
        log.info("[APN: StartContractRate @{}] REST request to get StartContractRate : {}", SecurityUtils.getUserId(), id);
        StartContractRate startContractRate = startContractRateService.findById(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(startContractRate));
    }

    @DeleteMapping("/start-contract-rates/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        log.info("[APN: StartContractRate @{}] REST request to delete StartContractRate : {}", SecurityUtils.getUserId(), id);
        startContractRateService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }

    @PostMapping("/start-contract-rates/get-total-bill-amount")
    public ResponseEntity<BigDecimal> getTotalBillAmount(@RequestBody StartContractRate startContractRate) throws URISyntaxException {
        log.info("[APN: StartContractRate @{}] REST request to get total bill amount : {}", SecurityUtils.getUserId(), startContractRate);
        return ResponseEntity.ok().body(startContractRateService.getTotalBillAmount(startContractRate));
    }

    @PostMapping("/start-contract-rates/check-rate-date/startId/{startId}")
    public ResponseEntity<StartContractRateCheckVO> checkRateDate(@PathVariable Long startId, @RequestBody StartContractRate startContractRate) {
        log.info("[APN: StartContractRate @{}] REST request to check StartContractRate : {}, {}", SecurityUtils.getUserId(), startId, startContractRate);
        StartContractRateCheckVO result = startContractRateService.checkRateDate(startId,startContractRate);
        return ResponseEntity.ok().body(result);
    }
}
