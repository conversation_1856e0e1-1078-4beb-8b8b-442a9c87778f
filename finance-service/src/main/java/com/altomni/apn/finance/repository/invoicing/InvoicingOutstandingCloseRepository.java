package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.finance.domain.invoicing.InvoicingOutstandingClose;
import com.altomni.apn.finance.domain.invoicing.InvoicingRecordPaymentDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface InvoicingOutstandingCloseRepository extends JpaRepository<InvoicingOutstandingClose,Long> {

    List<InvoicingOutstandingClose> findByInvoicingId(Long invoicingId);

}
