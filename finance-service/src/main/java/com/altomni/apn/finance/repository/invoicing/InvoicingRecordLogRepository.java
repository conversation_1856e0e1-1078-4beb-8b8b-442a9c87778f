package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.common.domain.enumeration.company.InvoicingLogStatus;
import com.altomni.apn.finance.domain.invoicing.InvoicingRecordLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface InvoicingRecordLogRepository extends JpaRepository<InvoicingRecordLog,Long> {

    List<InvoicingRecordLog> findByInvoicingId(Long invoicingId);

    List<InvoicingRecordLog> findByVoidReferenceIdAndInvoicingStatus(Long invoicingId,InvoicingLogStatus invoicingStatus);

    @Modifying
    @Transactional
    @Query(value = "delete from  invoicing_record_log where invoicing_status=?1 and invoicing_id = ?2", nativeQuery = true)
    void deleteByInvoicingLogStatusAndInvoicingId(Integer invoicingStatus,Long invoicingId);

    @Modifying
    @Transactional
    @Query(value = "delete from  invoicing_record_log where invoicing_status=?1 and void_reference_id = ?2", nativeQuery = true)
    void deleteByInvoicingLogStatusAndVoidReferenceId(Integer invoicingStatus,Long voidReferenceId);

    @Modifying
    @Transactional
    @Query(value = "update invoicing_record_log set electronic_invoice_number=?2 where reference_id = ?1", nativeQuery = true)
    void updateElectronicInvoiceNumberById(Long id,String electronicInvoiceNumber);
}
