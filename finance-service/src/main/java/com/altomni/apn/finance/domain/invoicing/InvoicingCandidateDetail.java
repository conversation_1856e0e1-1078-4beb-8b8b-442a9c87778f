package com.altomni.apn.finance.domain.invoicing;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "invoicing_candidate_detail")
public class InvoicingCandidateDetail extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 开票申请id */
    @Column(name = "invoice_application_id")
    private Long invoiceApplicationId ;

    /** 候选人id */
    @ApiModelProperty(name = "候选人id")
    @Column(name = "invoice_candidate_id")
    private Long invoiceCandidateId ;

    @ApiModelProperty(name = "")
    @Column(name = "talent_id")
    private Long talentId ;

    @ApiModelProperty(name = "")
    @Column(name = "start_id")
    private Long startId ;

    /** 候选人信息 */
    @ApiModelProperty(name = "候选人信息")
    @Column(name = "user_id")
    private Long userId ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "user_name")
    private String userName ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "percentage")
    private String percentage ;

    /** 1、am 2、recruiter 3、dm 4、sourcer 5、owner 6、salesLeadManager 7、bdManager 8、coAm */
    @ApiModelProperty(name = "1、am 2、recruiter 3、dm 4、sourcer 5、owner 6、salesLeadManager 7、bdManager 8、coAm")
    @Column(name = "user_role")
    private Integer userRole ;

    @Column(name = "status")
    private Integer status ;

    @Column(name = "country")
    private String country;
}
