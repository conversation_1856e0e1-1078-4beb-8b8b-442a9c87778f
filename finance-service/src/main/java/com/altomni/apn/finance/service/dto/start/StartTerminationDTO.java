package com.altomni.apn.finance.service.dto.start;


import com.altomni.apn.finance.domain.enumeration.start.StartType;
import lombok.Data;

import java.time.LocalDate;


@Data
public class StartTerminationDTO {

    private LocalDate startEndDate;
    private LocalDate terminationDate;
    private Long startId;
    private Long talentRecruitmentProcessId;
    private StartType startType;
    private LocalDate startDate;

    public StartTerminationDTO() {}

    public StartTerminationDTO(Long talentRecruitmentProcessId,LocalDate startEndDate, LocalDate terminationDate, Long startId, StartType startType,LocalDate startDate) {
        this.startEndDate = startEndDate;
        this.terminationDate = terminationDate;
        this.startId = startId;
        this.startType = startType;
        this.startDate = startDate;
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
    }
}
