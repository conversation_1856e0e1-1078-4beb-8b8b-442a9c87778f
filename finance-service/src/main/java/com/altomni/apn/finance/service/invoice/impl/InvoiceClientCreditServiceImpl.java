package com.altomni.apn.finance.service.invoice.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityType;
import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.domain.invoice.InvoiceActivity;
import com.altomni.apn.finance.domain.invoice.InvoiceClientCredit;
import com.altomni.apn.finance.repository.invoice.InvoiceActivityRepository;
import com.altomni.apn.finance.repository.invoice.InvoiceClientCreditRepository;
import com.altomni.apn.finance.repository.invoice.InvoiceRepository;
import com.altomni.apn.finance.service.company.CompanyService;
import com.altomni.apn.finance.service.dto.invoice.InvoiceActivityDTO;
import com.altomni.apn.finance.service.dto.invoice.InvoiceClientCreditDTO;
import com.altomni.apn.finance.service.invoice.InvoiceClientCreditService;
import com.altomni.apn.finance.service.user.UserService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

@Service
public class InvoiceClientCreditServiceImpl implements InvoiceClientCreditService {

    private final Logger log = LoggerFactory.getLogger(InvoiceClientCreditServiceImpl.class);

    @Resource
    private InvoiceClientCreditRepository invoiceClientCreditRepository;
    @Resource
    private InvoiceRepository invoiceRepository;
    @Resource
    private UserService userService;
    @Resource
    private InvoiceActivityRepository invoiceActivityRepository;
    @Resource
    private CompanyService companyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;


    @Override
    public InvoiceActivityDTO apply(InvoiceClientCreditDTO clientCreditDTO) {
        Invoice invoice = checkInvoice(clientCreditDTO.getInvoiceId());
        InvoiceActivity invoiceActivity = invoiceActivityRepository.findByInvoiceIdAndInvoiceActivityType(invoice.getId(), InvoiceActivityType.CREDIT_APPLY);
        if (invoiceActivity != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_APPLY_INVOICEACTIVITYEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        InvoiceClientCredit clientCredit = invoiceClientCreditRepository.findAllByCompanyId(invoice.getCompanyId())
                .stream().filter(credit -> credit.getCurrency().equals(invoice.getCurrency()))
                .findFirst().orElse(null);
        if (clientCredit != null) {
            clientCredit.setBalance(clientCredit.getBalance().add(clientCreditDTO.getAmount()));
            invoiceClientCreditRepository.save(clientCredit);
        } else {
            InvoiceClientCredit create = new InvoiceClientCredit();
            create.setCompanyId(invoice.getCompanyId());
            create.setBalance(clientCreditDTO.getAmount());
            create.setCurrency(invoice.getCurrency());
            invoiceClientCreditRepository.save(create);
        }
        log.info("[APN: InvoiceClientCreditService @{}] Client credit apply : {}", SecurityUtils.getUserId(), clientCreditDTO);
        return saveInvoiceActivity(clientCreditDTO, InvoiceActivityType.CREDIT_APPLY, clientCreditDTO.getNote());
    }

    private Invoice checkInvoice(Long invoiceId) {
        Invoice invoice = invoiceRepository.findById(invoiceId).orElse(null);
        if (invoice == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_CHECKINVOICE_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(invoiceId),financeApiPromptProperties.getFinanceService()));
        }
        if (!SecurityUtils.getTenantId().equals(invoice.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_CHECKINVOICE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        return invoice;
    }

    @Override
    public InvoiceActivityDTO saveInvoiceActivity(InvoiceClientCreditDTO clientCreditDTO, InvoiceActivityType activityType, String note) {
        InvoiceActivity invoiceActivity = new InvoiceActivity();
        invoiceActivity.setInvoiceId(clientCreditDTO.getInvoiceId());
        invoiceActivity.setInvoiceNo(clientCreditDTO.getInvoiceNo());
        invoiceActivity.setUserId(SecurityUtils.getUserId());
        invoiceActivity.setUserFullName(userService.getUserFullName(SecurityUtils.getUserId()));
        invoiceActivity.setInvoiceActivityType(activityType);
        invoiceActivity.setAmount(clientCreditDTO.getAmount());
        invoiceActivity.setNote(note);
        return InvoiceActivityDTO.fromInvoiceActivity(invoiceActivityRepository.save(invoiceActivity));
    }

    @Override
    public InvoiceActivityDTO deduct(Integer currency, InvoiceClientCreditDTO clientCreditDTO) {
        InvoiceClientCredit exists = invoiceClientCreditRepository.findByCurrencyAndCompanyId(currency, clientCreditDTO.getCompanyId()).orElse(null);
        if (exists == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_DEDUCT_INVOICECLIENTCREDITNOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        boolean illegalAmount = clientCreditDTO.getAmount().compareTo(new BigDecimal("0.00")) < 0 ||
            exists.getBalance().compareTo(clientCreditDTO.getAmount()) < 0;
        if (illegalAmount) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_DEDUCT_NOENOUGHCREDIT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        exists.setBalance(exists.getBalance().subtract(clientCreditDTO.getAmount()));
        invoiceClientCreditRepository.save(exists);
        log.info("[APN: InvoiceClientCreditService @{}] Client credit deduct : {}", SecurityUtils.getUserId(), clientCreditDTO);
        return saveInvoiceActivity(clientCreditDTO, InvoiceActivityType.CREDIT_DEDUCT, clientCreditDTO.getNote());
    }

    private void checkCompany(Long companyId) {
       Company company = companyService.getCompany(companyId);
       if (company == null) {
           throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_CHECKCOMPANY_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(companyId),financeApiPromptProperties.getFinanceService()));
       }
       if (!SecurityUtils.getTenantId().equals(company.getTenantId())) {
           throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_CHECKCOMPANY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
       }
    }

    @Override
    public List<InvoiceClientCredit> getAllInvoiceClientCredit(Long companyId) {
        checkCompany(companyId);
        return invoiceClientCreditRepository.findAllByCompanyId(companyId);
    }
}
