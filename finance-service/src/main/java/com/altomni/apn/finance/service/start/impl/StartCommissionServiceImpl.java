package com.altomni.apn.finance.service.start.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.config.Constants;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.domain.start.Start;
import com.altomni.apn.finance.domain.start.StartCommission;
import com.altomni.apn.finance.repository.start.StartCommissionRepository;
import com.altomni.apn.finance.repository.start.StartRepository;
import com.altomni.apn.finance.service.application.ApplicationService;
import com.altomni.apn.finance.service.start.StartCommissionService;
import com.altomni.apn.finance.service.user.UserService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing StartCommission.
 */
@Service
@Transactional
public class StartCommissionServiceImpl implements StartCommissionService {

    private final Logger log = LoggerFactory.getLogger(StartCommissionServiceImpl.class);

    @Resource
    private StartCommissionRepository startCommissionRepository;

    @Resource
    private UserService userService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private StartRepository startRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;


    /**
     * Save List<StartCommission>.
     *
     * @param startCommissions the entity to save
     * @return the persisted entity
     */
    @Override
    public List<StartCommission> create(Start start, List<StartCommission> startCommissions) {
        if (CollectionUtils.isEmpty(startCommissions)) {
            return null;
        }
        if (JobType.PAY_ROLL.equals(start.getPositionType())) {
            return startCommissionRepository.saveAll(filterAndBuildStartCommission(start, startCommissions));
        }

        // do filter first, just in case the ownership commission modify by front end. The ownership must update by back end only.
        List<StartCommission> excludeOwnerCommissions = filterAndBuildStartCommission(start, startCommissions);
        List<TalentRecruitmentProcessKpiUserVO> ownershipCommissions = new ArrayList<>();
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(start.getTenantId())) {
            List<TalentRecruitmentProcessKpiUserVO> kpiUsers = applicationService.getKpiUsersByTalentRecruitmentProcessId(start.getTalentRecruitmentProcessId());
            kpiUsers.forEach(k -> log.info("UserRole=" + k.getUserRole()));
            ownershipCommissions = kpiUsers.stream()
                    .filter(kpiUser -> UserRole.OWNER.equals(kpiUser.getUserRole()))
                    .collect(Collectors.toList());
        }
        BigDecimal sumCommission = calculateTotalCommissionPercentage(excludeOwnerCommissions);
        log.info("sumCommission = " + sumCommission);
        if (CollectionUtils.isNotEmpty(ownershipCommissions)) {
//            if (sumCommission.compareTo(Constants.ACCRUED_PERCENTAGE_NINETY) != 0) {
//                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CREATE_ACCRUEDPERCENTAGENINETY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
//            }
            excludeOwnerCommissions.addAll(ownershipCommissions.stream().map(ownershipCommission -> {
                StartCommission commission = toStartCommission(ownershipCommission, start.getTenantId());
                commission.setStartId(start.getId());
                return commission;
            }).collect(Collectors.toList()));
        } else {
            if (sumCommission.compareTo(Constants.ACCRUED_PERCENTAGE_HUNDRED) != 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CREATE_ACCRUEDPERCENTAGEHUNDRED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
            }
        }
        return startCommissionRepository.saveAll(excludeOwnerCommissions);
    }

    private List<StartCommission> filterAndBuildStartCommission(Start start, List<StartCommission> startCommissions) {
        List<StartCommission> result = startCommissions.stream()
            .filter(startCommission -> StartCommission.allButExcludeOwner.contains(startCommission.getUserRole()))
            .collect(Collectors.toList());
        return result.stream().map(startCommission -> {
            StartCommission commission = new StartCommission();
            commission.setStartId(start.getId());
            commission.setTenantId(start.getTenantId());
            commission.setUserId(startCommission.getUserId());
            commission.setUserFullName(userService.getUserFullName(startCommission.getUserId()));
            commission.setUserRole(startCommission.getUserRole());
            commission.setPercentage(startCommission.getPercentage());
            commission.setPeriodStartDate(startCommission.getPeriodStartDate());
            commission.setPeriodEndDate(startCommission.getPeriodEndDate());
            commission.setCountry(startCommission.getCountry());
            return commission;
        }).collect(Collectors.toList());
    }

    private List<StartCommission> buildStartCommission(Start start, List<StartCommission> startCommissions) {
        return startCommissions.stream().map(startCommission -> {
            StartCommission commission = new StartCommission();
            commission.setStartId(start.getId());
            commission.setTenantId(start.getTenantId());
            commission.setUserId(startCommission.getUserId());
            commission.setUserFullName(userService.getUserFullName(startCommission.getUserId()));
            commission.setUserRole(startCommission.getUserRole());
            commission.setPercentage(startCommission.getPercentage());
            commission.setPeriodStartDate(startCommission.getPeriodStartDate());
            commission.setPeriodEndDate(startCommission.getPeriodEndDate());
            commission.setCountry(startCommission.getCountry());
            return commission;
        }).collect(Collectors.toList());
    }

    private StartCommission toStartCommission(TalentRecruitmentProcessKpiUserVO kpiUser, Long tenantId) {
        StartCommission result = new StartCommission();
        result.setTenantId(tenantId);
        result.setUserId(kpiUser.getUserId());
        if(kpiUser.getUserRole() != null) {
            result.setUserRole(UserRole.fromDbValue(kpiUser.getUserRole().toDbValue())); // TODO
        }
        result.setUserFullName(userService.getUserFullName(kpiUser.getUserId()));
        result.setPercentage(kpiUser.getPercentage());
        return result;
    }

    private BigDecimal calculateTotalCommissionPercentage(List<StartCommission> startCommissions) {
        return startCommissions.stream().map(StartCommission::getPercentage).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public List<StartCommission> replace(Start start, List<StartCommission> startCommissions) {
        if (CollectionUtils.isEmpty(startCommissions)) {
            return null;
        }
        if (JobType.PAY_ROLL.equals(start.getPositionType())) {
            return startCommissionRepository.saveAll(filterAndBuildStartCommission(start, startCommissions));
        }
        List<StartCommission> updateCommissions = buildStartCommission(start, startCommissions);
//        List<StartCommission> allStartCommissions = startCommissionRepository.findByStartId(start.getId());
//        List<StartCommission> ownershipUserCommissions = allStartCommissions.stream()
//            .filter(commission -> UserRole.OWNER.equals(commission.getUserRole())).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(ownershipUserCommissions)) {
////            if (calculateTotalCommissionPercentage(updateCommissions).compareTo(Constants.ACCRUED_PERCENTAGE_NINETY) != 0) {
////                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_REPLACE_ACCRUEDPERCENTAGENINETY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(ownershipUserCommissions.stream().map(StartCommission::getUserFullName).collect(Collectors.joining(","))),financeApiPromptProperties.getFinanceService()));
////            }
//            List<StartCommission> excludeOwnerCommissions = allStartCommissions.stream()
//                .filter(commission -> StartCommission.allButExcludeOwner.contains(commission.getUserRole())).collect(Collectors.toList());
//            startCommissionRepository.deleteAll(excludeOwnerCommissions);
//        } else {
//            if (calculateTotalCommissionPercentage(updateCommissions).compareTo(Constants.ACCRUED_PERCENTAGE_HUNDRED) != 0) {
//                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CREATE_ACCRUEDPERCENTAGEHUNDRED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
//            }
//            startCommissionRepository.deleteByByStartId(start.getId());
//        }

        if (calculateTotalCommissionPercentage(updateCommissions).compareTo(Constants.ACCRUED_PERCENTAGE_HUNDRED) != 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CREATE_ACCRUEDPERCENTAGEHUNDRED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        startCommissionRepository.deleteByByStartId(start.getId());
        startCommissionRepository.saveAll(updateCommissions);
        return startCommissionRepository.findByStartId(start.getId());
    }

    @Override
    public List<StartCommission> findByStartId(Long startId) {
        List<StartCommission> result = startCommissionRepository.findByStartId(startId);
        if (CollectionUtils.isNotEmpty(result)) {
            result.sort(new StartCommissionUserRoleComparator());
            return result;
        }
        Optional<Start> extensionOpt = startRepository.findById(startId);
        if (extensionOpt.isEmpty()) {
            return new ArrayList<>();
        }
        Start extension = extensionOpt.get();
        Start originalStart = startRepository.findByTalentIdAndJobIdAndStartType(extension.getTalentId(), extension.getJobId(), StartType.CONTRACT_NEW_HIRE);
        if (Objects.isNull(originalStart)){
            return new ArrayList<>();
        }
        result = startCommissionRepository.findByStartId(originalStart.getId());
        if (CollUtil.isNotEmpty(result)) {
            result.sort(new StartCommissionUserRoleComparator());
        }
        return result;
    }


    class StartCommissionUserRoleComparator implements Comparator<StartCommission> {
        private final List<UserRole> order = List.of(
                UserRole.AM, UserRole.CO_AM, UserRole.AC, UserRole.RECRUITER, UserRole.SOURCER, UserRole.DM, UserRole.PR, UserRole.OWNER
        );

        @Override
        public int compare(StartCommission u1, StartCommission u2) {
            int index1 = order.indexOf(u1.getUserRole());
            int index2 = order.indexOf(u2.getUserRole());

            if (index1 == -1) {
                return 1;
            } else if (index2 == -1) {
                return -1;
            } else {
                return Integer.compare(index1, index2);
            }
        }

    }
}
