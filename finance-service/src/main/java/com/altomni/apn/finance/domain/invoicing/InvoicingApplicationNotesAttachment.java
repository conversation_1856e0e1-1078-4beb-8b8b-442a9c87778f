package com.altomni.apn.finance.domain.invoicing;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@Data
@Entity
@Table(name = "invoicing_application_attachment")
public class InvoicingApplicationNotesAttachment implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 开票申请id */
    @Column(name = "invoice_application_id")
    private Long invoiceApplicationId ;

    /**  */
    @Column(name = "attachment_url")
    private String attachmentUrl ;

    /** 状态 0-无效 1-有效 */
    @Column(name = "status")
    private Integer status ;

    /** 创建人 */
    @Column(name = "created_by")
    private String createdBy ;

    /** 创建时间 */
    @Column(name = "created_date")
    private Instant createdDate ;
}
