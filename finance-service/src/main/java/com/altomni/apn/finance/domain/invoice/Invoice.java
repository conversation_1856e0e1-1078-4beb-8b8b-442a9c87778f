package com.altomni.apn.finance.domain.invoice;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatusConverter;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceTypeConverter;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A Invoice.
 */
@Data
@Entity
@Table(name = "invoice")
public class Invoice extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to.")
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @ApiModelProperty(value = "Invoice number")
    @Column(name = "invoice_no")
    private String invoiceNo;

    @ApiModelProperty(value = "Sub invoice number")
    @Column(name = "sub_invoice_no")
    private String subInvoiceNo;

    @ApiModelProperty(value = "Split invoice or not")
    @Column(name = "split")
    private Boolean split = Boolean.FALSE;

    @ApiModelProperty(value = "Invoice type: FTE Invoice, Startup Fee Invoice")
    @Convert(converter = InvoiceTypeConverter.class)
    @Column(name = "invoice_type")
    private InvoiceType invoiceType;

    @ApiModelProperty(value = "Invoice status: Created, Approve, Paid, Void")
    @Convert(converter = InvoiceStatusConverter.class)
    @Column(name = "status")
    private InvoiceStatus status;

    @ApiModelProperty(value = "Create start process id")
    @Column(name = "start_id")
    private Long startId;

    @ApiModelProperty(value = "Create start process date.")
    @Column(name = "start_date", nullable = false)
    private LocalDate startDate;

    @ApiModelProperty(value = "Job id.")
    @Column(name = "job_id")
    private Long jobId;

    @ApiModelProperty(value = "Job title.")
    @Column(name = "job_title")
    private String jobTitle;

    @ApiModelProperty(value = "Talent id.")
    @Column(name = "talent_id")
    private Long talentId;

    @ApiModelProperty(value = "Talent name.")
    @Column(name = "talent_name")
    private String talentName;

    @ApiModelProperty(value = "Program order number.")
    @Column(name = "po_no")
    private String poNo;

    @ApiModelProperty(value = "Customer reference number.")
    @Column(name = "customer_reference")
    private String customerReference;

    @ApiModelProperty(value = "The id of company", required = true)
    @Column(name = "company_id")
    private Long companyId;

    @ApiModelProperty(value = "Company name.")
    @Column(name = "customer_name")
    private String customerName;

    @ApiModelProperty(value = "Company address.")
    @Column(name = "customer_address")
    private String customerAddress;

    @ApiModelProperty(value = "Client contact id.")
    @Column(name = "client_contact_id")
    private Long clientContactId;

    @ApiModelProperty(value = "The division id user belongs to.")
    @Column(name = "team_id")
    private Long teamId;

    @Column(name = "total_billable_package", precision = 10, scale = 2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillablePackage;

    @ApiModelProperty(value = "Total bill amount")
    @Column(name = "total_bill_amount", precision = 10, scale = 2)
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalBillAmount;

    @Column(name = "tax_rate")
    private BigDecimal taxRate;

    @Column(name = "tax_amount")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal taxAmount;

    @ApiModelProperty(value = "Discount amount")
    @Column(name = "discount")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal discount;

    @ApiModelProperty(value = "Total invoice amount: totalBillAmount - discount - applyCredit - (startupFeeAmount if exists).")
    @Column(name = "total_invoice_amount")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal totalInvoiceAmount;

    @ApiModelProperty(value = "The amount for current invoice. If it is a split invoice, merge all due amounts equals total amount." +
            "eg: if split == false, then totalInvoiceAmount = dueAmount;" +
            "if split == true, totalInvoiceAmount += all sub invoice's dueAmount")
    @Column(name = "due_amount")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal dueAmount;

    @Column(name = "receiving_account_id")
    private Long receivingAccountId;

    @Column(name = "invoice_date")
    private LocalDate invoiceDate;

    @Column(name = "due_date")
    private LocalDate dueDate;

    @Column(name = "currency")
    private Integer currency;

    @Column(name = "note")
    private String note;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("startId", "talentId", "jobId", "clientContactId", "tenantId", "invoiceNo", "subInvoiceNo", "split"));

    public BigDecimal getTotalBillAmount() {
        return totalBillAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalBillAmount));
    }

    public BigDecimal getTaxAmount() {
        return taxAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(taxAmount));
    }

    public BigDecimal getDiscount() {
        return discount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(discount));
    }

    public BigDecimal getTotalInvoiceAmount() {
        return totalInvoiceAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(totalInvoiceAmount));
    }

    public BigDecimal getDueAmount() {
        return dueAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(dueAmount));
    }

    public static Long convertReceivingAccountIdByCurrency(Integer currency) {
        switch (currency) {
            //USD - $
            case 0:
                return 1L;
            //CAD - C$
            case 3:
                return 6L;
            //SGD - S$
            case 28:
                return 12L;
            //EUR - €
            //GBP - £
            //HKD - HK$
            //MYR - RM
            //PHP - ₱
            case 2:
            case 4:
            case 12:
            case 36:
            case 24:
                return 14L;
            //JPY - JP¥
            case 16:
                return 16L;
            default:
                return null;
        }
    }

}
