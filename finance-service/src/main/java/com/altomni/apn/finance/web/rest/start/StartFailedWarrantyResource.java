package com.altomni.apn.finance.web.rest.start;

import cn.hutool.core.bean.BeanUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.domain.start.StartFailedWarranty;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.start.StartFailedWarrantyService;
import com.altomni.apn.finance.service.start.StartService;
import com.altomni.apn.finance.web.rest.dto.StartFailedWarrantyInputDTO;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * REST controller for managing StartFailedWarranty.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StartFailedWarrantyResource {

    private static final String ENTITY_NAME = "StartFailedWarranty";

    @Resource
    private StartFailedWarrantyService startFailedWarrantyService;

    @Resource
    private StartService startService;


    @PostMapping("/starts/failed-warranty/{startId}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<StartDTO> create(@PathVariable Long startId, @Valid @RequestBody StartFailedWarrantyInputDTO input) throws URISyntaxException, IOException {
        log.info("[APN: StartFailedWarranty @{}] REST request to create StartFailedWarranty : {}", SecurityUtils.getUserId(), input);
        startService.checkPermissionByStartId(startId);
        StartFailedWarranty startFailedWarranty = new StartFailedWarranty();
        BeanUtil.copyProperties(input, startFailedWarranty);
        startFailedWarranty.setStartId(startId);
        StartDTO result = startFailedWarrantyService.save(startFailedWarranty);

        return ResponseEntity.created(new URI("/api/v3/starts/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @PutMapping("/starts/failed-warranty/{startId}")
    @Timed
    public ResponseEntity<StartDTO> update(@PathVariable Long startId, @Valid @RequestBody StartFailedWarranty startFailedWarranty) throws URISyntaxException, IOException {
        log.info("[APN: StartFailedWarranty @{}] REST request to update StartFailedWarranty : {}", SecurityUtils.getUserId(), startFailedWarranty);
        startService.checkPermissionByStartId(startId);
        startFailedWarranty.setStartId(startId);
        StartDTO result = startFailedWarrantyService.update(startFailedWarranty);
        return ResponseEntity.created(new URI("/api/v3/starts/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }
}
