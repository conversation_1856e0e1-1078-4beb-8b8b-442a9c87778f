package com.altomni.apn.finance.service.invoice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.dict.CurrencyRateDay;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumReceivingAccount;
import com.altomni.apn.common.domain.enumeration.Currency;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarRelationEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.CanadaProvinceTaxDTO;
import com.altomni.apn.common.dto.calendar.CompleteSystemCalendarDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobInvoiceOverdueDTO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.InvoiceTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.repository.enums.EnumCurrencyRepository;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.CurrencyRateDayService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.finance.config.env.CanadaProvinceListProperties;
import com.altomni.apn.finance.config.env.CanadaProvinceTaxProperties;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceActivityType;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.domain.invoice.InvoiceActivity;
import com.altomni.apn.finance.domain.invoice.InvoicePaymentRecord;
import com.altomni.apn.finance.domain.invoice.SubInvoice;
import com.altomni.apn.finance.domain.start.Start;
import com.altomni.apn.finance.domain.start.StartContractRate;
import com.altomni.apn.finance.domain.start.StartFteRate;
import com.altomni.apn.finance.repository.invoice.*;
import com.altomni.apn.finance.repository.start.StartClientInfoRepository;
import com.altomni.apn.finance.repository.start.StartContractRateRepository;
import com.altomni.apn.finance.repository.start.StartFteRateRepository;
import com.altomni.apn.finance.repository.start.StartRepository;
import com.altomni.apn.finance.service.application.ApplicationClient;
import com.altomni.apn.finance.service.company.CompanyService;
import com.altomni.apn.finance.service.dto.invoice.*;
import com.altomni.apn.finance.service.invoice.InvoiceService;
import com.altomni.apn.finance.service.job.JobService;
import com.altomni.apn.finance.service.system.SequenceService;
import com.altomni.apn.finance.service.talent.TalentService;
import com.altomni.apn.finance.service.user.UserService;
import com.altomni.apn.finance.service.vo.invoice.InvoiceCompanyInfoVO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceSearchByApplicationVO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceTalentInfoVO;
import com.altomni.apn.finance.service.vo.job.JobSimpleVO;
import com.altomni.apn.finance.service.vo.talent.TalentSimpleVO;
import com.altomni.apn.finance.utils.CurrencyRateUtil;
import com.altomni.apn.finance.utils.PdfUtil;
import com.altomni.apn.user.service.calendar.CalendarService;
import com.google.common.collect.Lists;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.itextpdf.text.pdf.PdfWriter;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class InvoiceServiceImpl implements InvoiceService {

    private  Logger log = LoggerFactory.getLogger(InvoiceServiceImpl.class);

    @Resource
    private InvoiceRepository invoiceRepository;
    @Resource
    private StartRepository startRepository;
    @Resource
    private SequenceService sequenceService;
    @Resource
    private UserService userService;
    @Resource
    private InvoiceActivityRepository invoiceActivityRepository;
    @Resource
    private InvoicePaymentRecordRepository invoicePaymentRecordRepository;
    @Resource
    private JobService jobService;
    @Resource
    private TalentService talentService;
    @Resource
    private CompanyService companyService;
    @Resource
    private StartFteRateRepository startFteRateRepository;
    @Resource
    private StartContractRateRepository startContractRateRepository;
    @Resource
    private StartClientInfoRepository startClientInfoRepository;

    @Resource
    private InvoiceNativeRepository invoiceNativeRepository;
    @Resource
    private EnumCommonService enumCommonService;
    @Resource
    private ApplicationClient applicationClient;
    @Resource
    private InvoiceCustomRepository invoiceCustomRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Resource
    CanadaProvinceTaxProperties canadaProvinceTaxProperties;

    @Resource
    CanadaProvinceListProperties canadaProvinceListProperties;

    @Resource
    private CurrencyRateDayService currencyRateDayService;

    @Resource
    EnumCurrencyRepository enumCurrencyRepository;

    @Resource(name="financeConsumerRabbitTemplate")
    RabbitTemplate rabbitTemplate;

    @Resource
    private PlatformTransactionManager transactionManager;

    @Value("${application.useCurrencyRate}")
    private boolean useCurrencyRate;

    @Value("${application.currencyRateKey}")
    private String currencyRateKey;

    @Value("${application.sync-crm-currency.exchange}")
    private String currencyExchange;

    private static volatile Map<Integer,String> currencyNameMap;

    @Override
    public List<InvoiceDTO> createFteInvoice(InvoiceDTO invoiceDTO) {
        log.debug("Request to save InvoiceDTO : {}", invoiceDTO);
        Start start = startRepository.findStartByIdAndNotEliminate(invoiceDTO.getStartId());
        if (start == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_CREATEFTEINVOICE_STARTNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        //set default value
//        JobDTOV3 job = getJob(start.getJobId());
        JobSimpleVO job= invoiceRepository.getSimpleJobById(start.getJobId());
        invoiceDTO.setJobId(job.getId());
        invoiceDTO.setJobTitle(job.getTitle());
        invoiceDTO.setCompanyId(job.getCompanyId());
        TalentSimpleVO talent = invoiceRepository.getSimpleTalentById(start.getTalentId());
        invoiceDTO.setTalentId(talent.getId());
        invoiceDTO.setTalentName(talent.getFullName());
        invoiceDTO.setTenantId(SecurityUtils.getTenantId());
        invoiceDTO.setStatus(InvoiceStatus.UNPAID);
        invoiceDTO.setInvoiceType(InvoiceType.FTE);
        /*StartClientInfo startClientInfo = startClientInfoRepository.findByStartId(start.getId());
        if (startClientInfo != null) {
            invoiceDTO.setCustomerName(startClientInfo.getClientName());
            invoiceDTO.setCustomerAddress(startClientInfo.getClientAddress());
        }*/

        if (JobType.FULL_TIME.equals(start.getPositionType())) {
            StartFteRate startFteRate = startFteRateRepository.findByStartId(start.getId());
            invoiceDTO.setCurrency(startFteRate.getCurrency() != null ? startFteRate.getCurrency() : CurrencyConstants.USD);
            invoiceDTO.setTotalBillAmount(startFteRate.getTotalBillAmount());
        } else {
            List<StartContractRate> startContractRates = startContractRateRepository.findAllByStartId(start.getId());
            if (CollectionUtils.isNotEmpty(startContractRates)) {
                Integer currency = startContractRates.get(0).getCurrency();
                invoiceDTO.setCurrency(currency);
                invoiceDTO.setTotalBillAmount(startContractRates.stream().map(StartContractRate::getTotalBillAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            }
        }
        if (invoiceDTO.getTaxRate() != null && invoiceDTO.getTaxRate().compareTo(new BigDecimal("0.00")) > 0) {
            BigDecimal beforeTax = invoiceDTO.getTotalBillAmount().subtract(invoiceDTO.getDiscount());
            invoiceDTO.setTaxAmount(beforeTax.multiply(invoiceDTO.getTaxRate()).setScale(2, RoundingMode.HALF_UP));
        }
        //double check total invoice amount
        BigDecimal calculateTotalInvoiceAmount = invoiceDTO.getTotalBillAmount().subtract(invoiceDTO.getDiscount())
                .add(invoiceDTO.getTaxAmount())
                .setScale(2, RoundingMode.HALF_UP);
        if (calculateTotalInvoiceAmount.compareTo(invoiceDTO.getTotalInvoiceAmount()) != 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_CREATEFTEINVOICE_WRONGTOTAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(calculateTotalInvoiceAmount,invoiceDTO.getTotalInvoiceAmount()),financeApiPromptProperties.getFinanceService()));
        }

        List<InvoiceDTO> result = new ArrayList<>();
        //日元币种特殊处理，invoice no  JA
        String invoiceNo = null;
        if(Currency.JPY.toDbValue().equals(invoiceDTO.getCurrency())) {
            invoiceNo = sequenceService.getInvoiceSequence(InvoiceConstants.SEQ_INVOICE_NAME_JPY);
        }else {
            invoiceNo = sequenceService.getInvoiceSequence(InvoiceConstants.SEQ_INVOICE_NAME);
        }
        List<SubInvoice> subInvoiceList = invoiceDTO.getSubInvoiceList();
        if (invoiceDTO.getSplit() != null && invoiceDTO.getSplit() && CollectionUtils.isNotEmpty(subInvoiceList)) {
            //double check sub-invoice due amount
            BigDecimal sum = subInvoiceList.stream().map(SubInvoice::getDueAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (sum.compareTo(invoiceDTO.getTotalInvoiceAmount()) != 0) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_CREATEFTEINVOICE_TOTALAMOUNTINCORRECT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
            }
            List<Invoice> savedInvoices = new ArrayList<>();
            for (int i = 0; i < subInvoiceList.size(); i++) {
                SubInvoice subInvoice = subInvoiceList.get(i);
                Invoice invoice = copyInvoiceDTO2Invoice(invoiceDTO);
                if (subInvoice.getDueAmount() == null || subInvoice.getInvoiceDate() == null || subInvoice.getDueDate() == null) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_CREATEFTEINVOICE_DUEDATENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
                }
                invoice.setDueAmount(subInvoice.getDueAmount());
                invoice.setInvoiceDate(subInvoice.getInvoiceDate());
                invoice.setDueDate(subInvoice.getDueDate());
                invoice.setInvoiceNo(invoiceNo);
                invoice.setSubInvoiceNo(invoiceNo + "-" + (i + 1));
                Invoice save = invoiceRepository.save(invoice);
                saveInvoiceActivity(save.getId(), InvoiceActivityType.CREATE, invoiceDTO.getNote());
                savedInvoices.add(save);
            }
            result.addAll(toDto(savedInvoices));
        } else {
            Invoice invoice = copyInvoiceDTO2Invoice(invoiceDTO);
            invoice.setInvoiceNo(invoiceNo);
            invoice.setSubInvoiceNo(invoiceNo);
            invoice.setDueAmount(invoice.getTotalInvoiceAmount());
            Invoice save = invoiceRepository.save(invoice);
            saveInvoiceActivity(save.getId(), InvoiceActivityType.CREATE, invoiceDTO.getNote());
            result.addAll(toDto(List.of(save)));
        }
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            List<Long> invoiceIdList = result.stream().filter(dto -> Objects.equals(dto.getInvoiceType(), InvoiceType.FTE)).map(InvoiceDTO::getId).collect(Collectors.toList());
            applicationClient.createInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO.builder().fteInvoiceIdList(invoiceIdList).build());
        });
        return result;
    }

    private Invoice copyInvoiceDTO2Invoice(InvoiceDTO invoiceDTO) {
        Invoice invoice = new Invoice();
        ServiceUtils.myCopyProperties(invoiceDTO, invoice);
        invoice.setTenantId(SecurityUtils.getTenantId());
        if (Objects.isNull(invoiceDTO.getTeamId())) {
            invoice.setTeamId(SecurityUtils.getTeamId());
        }
        return invoice;
    }

    private Company getCompany(Long companyId) {
        Company company = companyService.getCompany(companyId);
        if (company == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_GETCOMPANY_ISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(companyId),financeApiPromptProperties.getFinanceService()));
        }
        return company;
    }

    @Override
    public InvoiceDTO createStartupFeeInvoice(Invoice invoice) {
        //如果是日元币种，特殊处理invoice NO格式
        String invoiceNo = null;
        if(Currency.JPY.toDbValue().equals(invoice.getCurrency())) {
            invoiceNo = sequenceService.getInvoiceSequence(InvoiceConstants.SEQ_STARTUP_FEE_INVOICE_NAME_JPY);
        }else {
            invoiceNo = sequenceService.getInvoiceSequence(InvoiceConstants.SEQ_STARTUP_FEE_INVOICE_NAME);
        }
        invoice.setInvoiceNo(invoiceNo);
        invoice.setSubInvoiceNo(invoiceNo);
        //set default value
        Company company = getCompany(invoice.getCompanyId());
        invoice.setCustomerName(company.getFullBusinessName());
        invoice.setTenantId(SecurityUtils.getTenantId());
        invoice.setStatus(InvoiceStatus.STARTUP_FEE_UNPAID_UNUSED);
        invoice.setInvoiceType(InvoiceType.STARTUP_FEE);
        //TODO  startup fee迭代需求，临时处理成默认账户模板
        invoice.setReceivingAccountId(Invoice.convertReceivingAccountIdByCurrency(invoice.getCurrency()));
        Invoice saved = invoiceRepository.save(invoice);
        saveInvoiceActivity(saved.getId(), InvoiceActivityType.CREATE, invoice.getNote());
        return toDto(List.of(saved)).stream().findFirst().orElse(null);
    }

    @Override
    public InvoiceActivity voidInvoice(InvoiceVoidDTO invoiceVoidDTO) {
        if (invoiceVoidDTO.getInvoiceId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_VOIDINVOICE_IDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        Invoice invoice = invoiceRepository.findById(invoiceVoidDTO.getInvoiceId()).orElse(null);
        if (invoice == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_VOIDINVOICE_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        if (InvoiceStatus.VOID == invoice.getStatus()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_VOIDINVOICE_ISVOIDSTATUS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(invoice.getId()),financeApiPromptProperties.getFinanceService()));
        }
        if (invoice.getStatus() == InvoiceStatus.PAID || invoice.getStatus() == InvoiceStatus.PARTIALLY_PAID || invoice.getStatus() == InvoiceStatus.STARTUP_FEE_PAID_USED) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_VOIDINVOICE_ALREADYPAID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        invoice.setStatus(InvoiceStatus.VOID);
        invoice = invoiceRepository.save(invoice);
        applicationClient.onboardNoInvoiceReminderByInvoiceVoid(XxlJobInvoiceOverdueDTO.builder().fteInvoiceIdList(CollUtil.newArrayList(invoiceVoidDTO.getInvoiceId())).build());
        InvoiceActivity invoiceActivity = saveInvoiceActivity(invoice.getId(), InvoiceActivityType.VOID, invoiceVoidDTO.getReason());
        SecurityContext context = SecurityContextHolder.getContext();
        Long invoiceId = invoice.getId();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            notifyCompleteSystemCalendar(InvoiceTypeEnum.getTypeInvoiceId(invoiceId, InvoiceTypeEnum.INVOICE), CalendarRelationEnum.INVOICE, List.of(CalendarTypeEnum.PAYMENT_OVERDUE));
        });
        return invoiceActivity;
    }

    @Resource
    private CalendarService calendarService;

    private void notifyCompleteSystemCalendar(Long talentRecruitmentProcessId, CalendarRelationEnum calendarRelation, List<CalendarTypeEnum> type) {
        if(talentRecruitmentProcessId == null) {
            return;
        }
        CompleteSystemCalendarDTO dto = new CompleteSystemCalendarDTO();
        dto.setRelationType(calendarRelation);
        dto.setType(type);
        dto.setUniqueReferenceId(talentRecruitmentProcessId);
        calendarService.completeSystemCalendar(dto);
    }

//    @Override
//    public List<InvoiceActivity> voidInvoiceByNo(InvoiceVoidDTO invoiceVoidDto) {
//        if (invoiceVoidDto.getInvoiceNo() == null) {
//            throw new CustomParameterizedException("Invoice number can not be null");
//        }
//        List<InvoiceActivity> result = new ArrayList<>();
//        List<Invoice> invoiceList = invoiceRepository.findByInvoiceNo(invoiceVoidDto.getInvoiceNo());
//        if (CollectionUtils.isNotEmpty(invoiceList)) {
//            for (Invoice invoice : invoiceList) {
//                if (InvoiceStatus.VOID == invoice.getStatus()) {
//                    throw new CustomParameterizedException("This invoice already void. Invoice number: " + invoice.getInvoiceNo());
//                }
//                if (invoice.getStatus() == InvoiceStatus.PAID || invoice.getStatus() == InvoiceStatus.PARTIALLY_PAID || invoice.getStatus() == InvoiceStatus.STARTUP_FEE_PAID_USED) {
//                    throw new CustomParameterizedException("This invoice already paid. You can't void an invoice already paid.");
//                }
//                invoice.setStatus(InvoiceStatus.VOID);
//                invoiceRepository.save(invoice);
//                result.add(saveInvoiceActivity(invoice.getId(), InvoiceActivityType.VOID, invoiceVoidDto.getReason()));
//            }
//            rollBackStartupFee(invoiceList.get(0));
//        }
//        return result;
//    }

    @Override
    public InvoiceDTO findOne(Long id) {
        Optional<Invoice> invoiceOpt = invoiceRepository.findById(id);
        return toDto(Stream.of(invoiceOpt).filter(Optional::isPresent).map(Optional::get).toList()).stream().findFirst().orElse(null);
    }

    private List<InvoiceDTO> toDto(List<Invoice> invoices) {
        if (CollectionUtils.isEmpty(invoices)) {
            return Collections.emptyList();
        }
        List<Long> invoiceIds = invoices.stream().map(Invoice::getId).toList();
        List<InvoiceActivity> invoiceActivities = invoiceActivityRepository.findAllByInvoiceIdIn(invoiceIds);
        Map<Long, List<InvoiceActivity>> invoicrActivityMap = invoiceActivities.stream().collect(Collectors.groupingBy(InvoiceActivity::getInvoiceId, Collectors.toList()));
        Predicate<InvoiceActivity> isPayment = activity -> InvoiceActivityType.PAYMENT == activity.getInvoiceActivityType() || InvoiceActivityType.PAYMENT_UNRECORD == activity.getInvoiceActivityType();
        List<Long> paymentIds = invoiceActivities.stream().filter(isPayment).map(InvoiceActivity::getPaymentId).toList();
        Map<Long, InvoicePaymentRecord> paymentRecordMap = paymentIds.isEmpty() ? Collections.emptyMap()
            : invoicePaymentRecordRepository.findAllByIdIn(paymentIds).stream().collect(Collectors.toMap(InvoicePaymentRecord::getId, Function.identity()));
        Map<Long, List<InvoicePaymentRecord>> invoicePaymentRecordMap = invoicePaymentRecordRepository.findAllByInvoiceIdInAndActivated(invoiceIds, Boolean.TRUE)
            .stream().collect(Collectors.groupingBy(InvoicePaymentRecord::getInvoiceId, Collectors.toList()));


        return invoices.stream().map(invoice -> {
            InvoiceDTO result = InvoiceDTO.fromInvoice(invoice);
            List<InvoiceActivity> activities = invoicrActivityMap.getOrDefault(invoice.getId(), Collections.emptyList());
            List<InvoiceActivityDTO> activityDtos = activities.stream().map(activity -> {
                InvoiceActivityDTO activityDTO = InvoiceActivityDTO.fromInvoiceActivity(activity);
                if (!isPayment.test(activity)) {
                    return activityDTO;
                }

                InvoicePaymentRecord invoicePaymentRecord = paymentRecordMap.get(activity.getPaymentId());
                if (Objects.nonNull(invoicePaymentRecord)) {
                    activityDTO.setAmount(invoicePaymentRecord.getPaidAmount());
                    activityDTO.setExchangeRate(invoicePaymentRecord.getExchangeRate());
                    activityDTO.setCurrency(invoicePaymentRecord.getCurrency());
                    activityDTO.setPaymentDate(invoicePaymentRecord.getPaymentDate());
                    activityDTO.setPaymentMethod(invoicePaymentRecord.getPaymentMethod());
                    activityDTO.setCloseWithoutFullPayment(invoicePaymentRecord.getCloseWithoutFullPayment());
                }
                return activityDTO;
            }).toList();

            result.setActivities(activityDtos);
            result.setUnpaidAmount(calUnpaidAmount(invoice, invoicePaymentRecordMap.getOrDefault(invoice.getId(), Collections.emptyList())));
            return result;
        }).toList();
    }

    private BigDecimal calUnpaidAmount(Invoice invoice, List<InvoicePaymentRecord> paymentRecords) {
        //check total invoice amount
        BigDecimal totalPaidAmount = paymentRecords.stream().map(record -> {
            BigDecimal paidAmount = record.getPaidAmount();
            if (ObjectUtil.isNotNull(record.getExchangeRate())) {
                paidAmount = calPaidAmountByExchangeRate(record.getExchangeRate(), record.getPaidAmount(), paidAmount);
            }
            return paidAmount;
        }).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalStartUpFeeAmount = paymentRecords.stream()
                .map(InvoicePaymentRecord::getStartupFeeAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalApplyCreditAmount = paymentRecords.stream()
                .map(InvoicePaymentRecord::getApplyCredit)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return invoice.getDueAmount()
                .subtract(totalPaidAmount)
                .subtract(totalStartUpFeeAmount)
                .subtract(totalApplyCreditAmount)
                .setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal calPaidAmountByExchangeRate(String exchangeRate, BigDecimal paidAmount, BigDecimal currentPaidAmount) {
        if (exchangeRate.contains(StrUtil.COLON)) {
            String[] exchangeRates = exchangeRate.split(StrUtil.COLON);
            BigDecimal invoiceRate = new BigDecimal(exchangeRates[0]);
            BigDecimal paidRate = new BigDecimal(exchangeRates[1]);
            currentPaidAmount = paidAmount.multiply(invoiceRate).divide(paidRate, 2, RoundingMode.HALF_UP);
        }
        return currentPaidAmount;
    }

    @Override
    public List<InvoiceDTO> findByInvoiceNo(String invoiceNo) {
        /*List<Invoice> invoiceList = invoiceRepository.findByInvoiceNo(invoiceNo);
        if (ObjectUtil.isNotEmpty(invoiceList) && !invoiceList.stream().map(Invoice::getTenantId).collect(Collectors.toList()).contains(SecurityUtils.getTenantId())) {
            return null;
        }*/
        List<Invoice> invoiceList = invoiceRepository.findByInvoiceNoAndTenantId(invoiceNo, SecurityUtils.getTenantId());
        return toDto(invoiceList);
    }

    @Override
    public void downloadInvoice(HttpServletResponse response, Long id) {
        Invoice invoice = invoiceRepository.findById(id).orElse(null);
        List<InvoicePaymentRecord> records = invoicePaymentRecordRepository.findByInvoiceIdAndActivated(id, Boolean.TRUE);
        //find invoice receivingAccount
        List<EnumReceivingAccount> receivingAccountList = enumCommonService.findAllEnumInvoiceReceivingAccount();
        if (invoice == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.INVOICE_DOWNLOADINVOICE_INVOICENOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
        saveInvoiceActivity(id, InvoiceActivityType.DOWNLOAD, "Download invoice");
        InvoiceDTO invoiceDTO = InvoiceDTO.fromInvoice(invoice);

        //start up fee类型的invoice没有start id
        if(invoice.getStartId() != null) {
            Map<String, Object> startClientInfo = invoiceNativeRepository.findCompanyAddressAndNameByStartId(BigInteger.valueOf(invoice.getStartId()));
            if (startClientInfo != null) {
                String location = StringUtil.valueOf(startClientInfo.get("relation_client_location"));
                invoiceDTO.setCustomerName(startClientInfo.get("relation_client_name") + "");
                invoiceDTO.setCustomerAddress(startClientInfo.get("relation_client_address") + "");
                invoiceDTO.setCustomerLocation(location);
                if (StringUtils.isNotBlank(location)) {
                    String province = CanadaProvinceTaxUtil.containsCanadaProvince(location, canadaProvinceListProperties.getInclude());
                    if (location.indexOf("Canada") != -1 && province != null && Currency.CAD.toDbValue().equals(invoiceDTO.getCurrency())) {
                        CanadaProvinceTaxDTO dto = canadaProvinceTaxProperties.getCanadaProvinceTax().get(province);
                        if (dto.getTaxName().equals("GST + QST")) {
                            invoiceDTO.setCanadaProvinceTaxString("Tax(GST No.:77796 1483 RT 0001,QST No.:**********)");
                        } else if (dto.getTaxName().equals("HST")) {
                            invoiceDTO.setCanadaProvinceTaxString("Tax(HST No.:77796 1483 RT 0001)");
                        } else if (dto.getTaxName().equals("GST")) {
                            invoiceDTO.setCanadaProvinceTaxString("Tax(GST No.:77796 1483 RT 0001)");
                        } else if (dto.getTaxName().equals("QST")) {
                            invoiceDTO.setCanadaProvinceTaxString("Tax(QST No.:**********)");
                        }
                    }
                }
            }
        }

        //获取当前选择的账户信息
        EnumReceivingAccount receivingAccount = receivingAccountList.stream()
                .filter(ra -> invoice.getReceivingAccountId().equals(ra.getId())).findAny().orElse(null);
        if (ObjectUtil.isNull(receivingAccount)) {
            throw new ForbiddenException("Invoice receiving account does not exists .");
        }

        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        ByteArrayOutputStream waterMarkedBos = new ByteArrayOutputStream();
        Document document = new Document(PageSize.A4, 0, 0, 60, 0);
        try {
            PdfUtil pdfUtil = new PdfUtil();
            PdfWriter.getInstance(document, bos);
            document.open();
            invoiceDTO.setCurrencyName(getCurrencyNameMap().get(invoiceDTO.getCurrency()));
            //根据配置生成invoice PDF
            //对日元币种特殊处理 HK模版不特殊处理
            if(Currency.JPY.toDbValue().equals(invoiceDTO.getCurrency()) && !receivingAccount.getEnDisplay().equals("IPG HK - JPY Bank")) {
                //非start up fee类型，查询serviceFee
                BigDecimal serviceFee = BigDecimal.ZERO;
                if(!InvoiceType.STARTUP_FEE.equals(invoice.getInvoiceType())) {
                    StartFteRate startFteRate = startFteRateRepository.findByStartId(invoice.getStartId());
                    serviceFee  = startFteRate.getFeePercentage();
                }
                pdfUtil.createJPYPdf(document, invoiceDTO, records, receivingAccountList, serviceFee);
            }else if((Currency.USD.toDbValue().equals(invoiceDTO.getCurrency()) && receivingAccount.getEnDisplay().equals("IPG Singapore - USD Bank")) ||
                     (Currency.SGD.toDbValue().equals(invoiceDTO.getCurrency()) && receivingAccount.getEnDisplay().equals("IPG Singapore - SGD bank"))){
                pdfUtil.createInvoicePdf2(document, invoiceDTO, records, receivingAccountList);
            }else{
                pdfUtil.createInvoicePdf(document, invoiceDTO, records, receivingAccountList);
            }
            HeaderUtil.setPDFHeader(response, invoice.getInvoiceNo());
            document.close();//要先关闭document，不然生成的PDF文件损坏
            OutputStream outputStream = response.getOutputStream();
            if (InvoiceStatus.VOID.equals(invoice.getStatus())) {
                PdfReader reader = new PdfReader(bos.toByteArray());
                PdfStamper stamper = new PdfStamper(reader, waterMarkedBos);
                pdfUtil.addWatermarkForVoidInvoice(reader, stamper);
                IOUtils.write(waterMarkedBos.toByteArray(), outputStream);
            } else {
                IOUtils.write(bos.toByteArray(), outputStream);
            }
        } catch (DocumentException | IOException e) {
            log.error("error", e);
        } finally {
            document.close();
            try {
                bos.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
        }

    }



    /**
     * save invoice activity
     *
     * @param invoiceId invoiceId
     */
    private InvoiceActivity saveInvoiceActivity(Long invoiceId, InvoiceActivityType invoiceActivityType, String note) {
        InvoiceActivity invoiceActivity = new InvoiceActivity();
        invoiceActivity.setInvoiceId(invoiceId);
        invoiceActivity.setUserId(SecurityUtils.getUserId());
        invoiceActivity.setUserFullName(userService.getUserFullName(SecurityUtils.getUserId()));
        invoiceActivity.setInvoiceActivityType(invoiceActivityType);
        invoiceActivity.setNote(note);
        return invoiceActivityRepository.save(invoiceActivity);
    }

    @Override
    public Page<Invoice> findAll(BooleanExpression booleanExpression, Pageable pageable) {
        return invoiceRepository.findAll(booleanExpression, pageable);
    }

    @Override
    public InvoiceSearchResult toSearchResult(List<Invoice> invoices) {
        InvoiceSearchResult result = new InvoiceSearchResult();
        if (CollectionUtils.isEmpty(invoices)) {
            return result;
        }

        List<Long> paidInvoiceIds = invoices.stream().filter(invoice -> isPaid(invoice.getStatus())).map(Invoice::getId).toList();
        Map<Long, List<InvoicePaymentRecord>> invoicePaymentRecordMap = invoicePaymentRecordRepository.findAllByInvoiceIdInAndActivated(paidInvoiceIds, Boolean.TRUE)
            .stream().collect(Collectors.groupingBy(InvoicePaymentRecord::getInvoiceId, Collectors.toList()));

        Map<Integer, InvoiceCurrencyAmount> currencyAmountMap = new HashMap<>();
        for (Invoice invoice : invoices) {
            if (currencyAmountMap.containsKey(invoice.getCurrency())) {
                InvoiceCurrencyAmount invoiceCurrencyAmount = currencyAmountMap.get(invoice.getCurrency());
                invoiceCurrencyAmount.setTotalAmount(invoiceCurrencyAmount.getTotalAmount().add(invoice.getDueAmount()));
                if (invoice.getStatus() == InvoiceStatus.OVERDUE) {
                    invoiceCurrencyAmount.setTotalOverdueAmount(invoiceCurrencyAmount.getTotalOverdueAmount().add(invoice.getDueAmount()));
                }
                if (isPaid(invoice.getStatus())) {
                    List<InvoicePaymentRecord> paymentRecords = invoicePaymentRecordMap.getOrDefault(invoice.getId(), Collections.emptyList());
                    if (CollectionUtils.isNotEmpty(paymentRecords)) {
                        BigDecimal paiAmount = paymentRecords.stream().map(InvoicePaymentRecord::getPaidAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                        invoiceCurrencyAmount.setTotalPaidAmount(invoiceCurrencyAmount.getTotalPaidAmount().add(paiAmount));
                    }
                }
            } else {
                InvoiceCurrencyAmount invoiceCurrencyAmount = new InvoiceCurrencyAmount();
                invoiceCurrencyAmount.setCurrency(invoice.getCurrency());
                invoiceCurrencyAmount.setTotalAmount(invoice.getDueAmount());
                if (invoice.getStatus() == InvoiceStatus.OVERDUE) {
                    invoiceCurrencyAmount.setTotalOverdueAmount(invoice.getDueAmount());
                }
                if (isPaid(invoice.getStatus())) {
                    List<InvoicePaymentRecord> paymentRecords = invoicePaymentRecordMap.getOrDefault(invoice.getId(), Collections.emptyList());
                    if (CollectionUtils.isNotEmpty(paymentRecords)) {
                        invoiceCurrencyAmount.setTotalPaidAmount(paymentRecords.stream().map(InvoicePaymentRecord::getPaidAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                }
                currencyAmountMap.put(invoice.getCurrency(), invoiceCurrencyAmount);
            }
        }
        result.setElements(toDto(invoices));
        result.setCurrencyAmounts(new ArrayList<>(currencyAmountMap.values()));
        return result;
    }

    private boolean isPaid(InvoiceStatus invoiceStatus) {
        return invoiceStatus == InvoiceStatus.PAID || invoiceStatus == InvoiceStatus.PARTIALLY_PAID || invoiceStatus == InvoiceStatus.STARTUP_FEE_PAID_USED || invoiceStatus == InvoiceStatus.STARTUP_FEE_PAID_UNUSED;
    }

    @Override
    public Page<Invoice> findMyInvoices(BooleanExpression booleanExpression, Pageable pageable) {
        return invoiceRepository.findAll(booleanExpression, pageable);
    }

    @Override
    public List<InvoiceDTO> findByByStartId(Long startId) {
        List<InvoiceDTO> result = new ArrayList<>();
        List<Invoice> invoiceList = invoiceRepository.findByStartId(startId);
        return toDto(invoiceList);
    }

    @Override
    public List<InvoiceDTO> getStartupFeeInvoicesByCompanyId(Long companyId) {
        List<Invoice> invoiceList = invoiceRepository.findByCompanyIdAndInvoiceType(companyId, InvoiceType.STARTUP_FEE);
        return toDto(invoiceList);
    }

    /**
     * Schedule update the over due date invoice status to OVERDUE every minute.
     */
    @Override
    @Scheduled(fixedRateString = "60000")
    public void scheduleUpdateOverDueStatus() {
        List<Invoice> invoices = invoiceRepository.findByDueDate(Arrays.asList(InvoiceStatus.UNPAID.toDbValue()
                , InvoiceStatus.STARTUP_FEE_UNPAID_UNUSED.toDbValue(), InvoiceStatus.PARTIALLY_PAID.toDbValue()));
        if (CollectionUtils.isNotEmpty(invoices)) {
            for (Invoice invoice : invoices) {
                invoice.setStatus(InvoiceStatus.OVERDUE);
                log.info("[APN: InvoiceService] schedule update overdue invoice status: {}", invoice.getSubInvoiceNo());
                invoiceRepository.save(invoice);
            }
        }
    }

    @Override
    public Page<InvoiceSearchByApplicationVO> searchFtePageByApplication(InvoiceSearchByApplicationDTO invoiceSearchByApplicationDTO) {
        List<InvoiceSearchByApplicationVO> voList = invoiceCustomRepository.searchDataByApplication(invoiceSearchByApplicationDTO);
        Long count = invoiceCustomRepository.searchCountByApplication(invoiceSearchByApplicationDTO);
        return new PageImpl<>(voList, Pageable.unpaged(), count);
    }

    @Override
    public List<InvoiceCompanyInfoVO> companyByTenantId(Long tenantId) {
        return invoiceCustomRepository.searchCompanyByTenantId(tenantId);
    }

    @Override
    public List<InvoiceTalentInfoVO> talentByTenantId(Long tenantId) {
        return invoiceCustomRepository.searchTalentByTenantId(tenantId);
    }

    @Override
    public void getAllCurrencyRate() {
        List<EnumCurrency> currencyList = enumCommonService.findAllEnumCurrency();
        if (currencyList.isEmpty()) {
            return;
        }
        List<List<EnumCurrency>> currencyListGroup = Lists.partition(currencyList, 10);

        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime ldt = LocalDateTime.parse("2025-03-13 00:00:00",df);
        Instant localDateTimeToInstant = ldt.atZone(ZoneId.systemDefault()).toInstant();
        LocalDateTime maxDate = LocalDateTime.parse("2025-03-16 23:59:59", df);
        Instant nowInstant = maxDate.atZone(ZoneId.systemDefault()).toInstant();

        List<CurrencyRateDay> currencyRateDayList = new ArrayList<>();
        int z =0;
        while (localDateTimeToInstant.isBefore(nowInstant)) {

            log.info("[getAllCurrencyRate]: day:{},开始获取数据",DateUtil.fromInstantToDate(localDateTimeToInstant));
            DefaultTransactionDefinition definition = new DefaultTransactionDefinition();
            TransactionStatus status = transactionManager.getTransaction(definition);

            try {
                String now = DateUtil.fromInstantToDate(localDateTimeToInstant);
                Instant finalLocalDateTimeToInstant = localDateTimeToInstant;
                currencyListGroup.forEach(cu -> {
                    try {
                        Thread.sleep(2000);
                    } catch (Exception e) {

                    }
                    String currencyStr = cu.stream().map(m-> "USD" + m.getName().toUpperCase()).collect(Collectors.joining(","));
                    String url = CurrencyRateUtil.HISTORICAL_RATE_URL.concat("?api_key=").concat(currencyRateKey)
                            .concat(CurrencyRateUtil.DELIMITER).concat("currency=").concat(currencyStr)
                            .concat(CurrencyRateUtil.DELIMITER).concat("date=").concat(now);
                    String responseStr = HttpUtil.get(url);
                    JSONObject response = JSONObject.parseObject(responseStr);

                    log.info("[getAllCurrencyRate] invoke {} response info {}", url, response);
                    JSONArray quotesArray = response.getJSONArray("quotes");
                    if (!quotesArray.isEmpty()) {
                        Map<String, EnumCurrency> mapCurrency = cu.stream().collect(Collectors.toMap(EnumCurrency::getName, a -> a));
                        for (int i = 0; i < quotesArray.size(); i++) {
                            JSONObject quote = quotesArray.getJSONObject(i);
                            log.info("[DayCurrencyRate] quote {}", quote);
                            if (quote.containsKey("quote_currency")) {
                                String quote_currency = quote.getString("quote_currency");
                                if (mapCurrency.containsKey(quote_currency) && !quote_currency.equals("USD")) {
                                    EnumCurrency c = mapCurrency.get(quote_currency);
                                    CurrencyRateDay bean = new CurrencyRateDay();
                                    bean.setRateDay(finalLocalDateTimeToInstant);
                                    bean.setRateHigh(quote.getFloatValue("high"));
                                    bean.setRateLow(quote.getFloatValue("low"));
                                    BigDecimal mid = new BigDecimal(quote.getString("high"))
                                            .add(new BigDecimal(quote.getString("low")))
                                            .divide(new BigDecimal("2"), 6, BigDecimal.ROUND_HALF_UP);
                                    bean.setRateMid(mid.floatValue());
                                    bean.setCurrencyId(Long.valueOf(c.getId()));
                                    currencyRateDayList.add(bean);
                                }
                            }
                        }
                    }
                });

                if (!currencyRateDayList.isEmpty()) {
                    currencyRateDayService.batchSave(currencyRateDayList);
                    log.info("[getAllCurrencyRate] success batch save currency rate param:{}", JSONUtil.toJsonStr(currencyRateDayList));
                }
                transactionManager.commit(status);
            } catch (Exception e) {
                transactionManager.rollback(status);
                throw e;
            }

            localDateTimeToInstant = localDateTimeToInstant.plus(1,ChronoUnit.DAYS);
            z++;
        }
    }

    @Override
    @Transactional
    public cn.hutool.json.JSONArray deleteInvoiceByStart(Collection<Long> startIds) {
        cn.hutool.json.JSONArray result = new cn.hutool.json.JSONArray();
        List<Invoice> invoices = invoiceRepository.findAllByStartIdIn(startIds);
        if (CollectionUtils.isEmpty(invoices)) {
            return result;
        }
        List<InvoiceStatus> unPaidStatus = Arrays.asList(InvoiceStatus.VOID, InvoiceStatus.UNPAID);
        List<Long> invoiceIds = invoices.stream().map(Invoice::getId).toList();
        List<InvoicePaymentRecord> invoicePaymentRecords = invoicePaymentRecordRepository.findAllByIdIn(invoiceIds);
        Map<Long, List<InvoicePaymentRecord>> invoicePaymentRecordMap = invoicePaymentRecords.stream().collect(Collectors.groupingBy(InvoicePaymentRecord::getInvoiceId, Collectors.toList()));

        List<Invoice> deletedInvoices = new ArrayList<>();
        List<InvoicePaymentRecord> deletedPaymentRecords = new ArrayList<>();
        invoices.forEach(invoice -> {
            cn.hutool.json.JSONObject item = DtoToJsonUtil.toJsonWithColumnNames(invoice);
            List<InvoicePaymentRecord> invoicePaymentRecordsList = invoicePaymentRecordMap.getOrDefault(invoice.getId(), Collections.emptyList());
            if (CollUtil.isNotEmpty(invoicePaymentRecordsList)) {
                item.put("paymentRecords", DtoToJsonUtil.toJsonWithColumnNames(invoicePaymentRecordsList));
            }
            if (unPaidStatus.contains(invoice.getStatus())) {
                deletedInvoices.add(invoice);
                deletedPaymentRecords.addAll(invoicePaymentRecordsList);
            } else {
                item.put("WARN!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", "该发票已经支付，未执行删除");
            }

            result.add(item);
        });
        invoiceRepository.deleteAll(deletedInvoices);
        invoicePaymentRecordRepository.deleteAll(deletedPaymentRecords);
        return result;
    }

    /**
     * 获取当日币种利率
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void getDayCurrencyRate() {
        List<EnumCurrency> currencyList = enumCommonService.findAllEnumCurrency();
        List<EnumCurrency> updateCurrencyList = new ArrayList<>();
        log.info("[DayCurrencyRate] currency list ,param{}", JSONUtil.toJsonStr(currencyList));
        List<CurrencyRateDay> currencyRateDayList = new ArrayList<>();
        if (currencyList.isEmpty()) {
            return;
        }

       List<List<EnumCurrency>> currencyListGroup = Lists.partition(currencyList, 10);
        Instant currentTime = Instant.now();
        String now = DateUtil.fromInstantToDate(currentTime);
        currencyListGroup.forEach(cu -> {
            try{
                Thread.sleep(2000);
            }catch (Exception e){

            }
            String currencyStr = cu.stream().map(m-> "USD" + m.getName().toUpperCase()).collect(Collectors.joining(","));
            String url = CurrencyRateUtil.HISTORICAL_RATE_URL.concat("?api_key=").concat(currencyRateKey)
                    .concat(CurrencyRateUtil.DELIMITER).concat("currency=").concat(currencyStr)
                    .concat(CurrencyRateUtil.DELIMITER).concat("date=").concat(now);
            /*String url = CurrencyRateUtil.BASE_URL.concat("?api_key=").concat(CurrencyRateUtil.API_KEY)
                    .concat(CurrencyRateUtil.DELIMITER).concat("currency=").concat(currencyType)
                    .concat(CurrencyRateUtil.DELIMITER).concat("format=records")
                    .concat(CurrencyRateUtil.DELIMITER).concat("start_date=").concat(now)
                    .concat(CurrencyRateUtil.DELIMITER).concat("end_date=").concat(now)
                    .concat(CurrencyRateUtil.DELIMITER).concat("interval=daily&period=1");*/
            String responseStr = HttpUtil.get(url);
            JSONObject response = JSONObject.parseObject(responseStr);

            log.info("[DayCurrencyRate] invoke {} response info {}", url, response);
            JSONArray quotesArray = response.getJSONArray("quotes");
            if (!quotesArray.isEmpty()) {
                Map<String, EnumCurrency> mapCurrency = cu.stream().collect(Collectors.toMap(EnumCurrency::getName, a -> a));
                for (int i = 0; i < quotesArray.size(); i++) {
                    JSONObject quote = quotesArray.getJSONObject(i);
                    log.info("[DayCurrencyRate] quote {}", quote);
                    if (quote.containsKey("quote_currency")) {
                        String quote_currency = quote.getString("quote_currency");
                        if (mapCurrency.containsKey(quote_currency) && !quote_currency.equals("USD")) {
                            EnumCurrency c = mapCurrency.get(quote_currency);
                            CurrencyRateDay bean = new CurrencyRateDay();
                            bean.setRateDay(currentTime);
                            bean.setRateHigh(quote.getFloatValue("high"));
                            bean.setRateLow(quote.getFloatValue("low"));
                            BigDecimal mid = new BigDecimal(quote.getString("high"))
                                    .add(new BigDecimal(quote.getString("low")))
                                    .divide(new BigDecimal("2"), 6, BigDecimal.ROUND_HALF_UP);
                            bean.setRateMid(mid.floatValue());
                            bean.setCurrencyId(Long.valueOf(c.getId()));
                            currencyRateDayList.add(bean);

                            Double formRate = 1/Math.pow(mid.doubleValue(),1);

                            EnumCurrency currency = new EnumCurrency();
                            currency.setId(c.getId());
                            currency.setName(c.getName());
                            currency.setFromUsdRate(formRate.floatValue());
                            currency.setToUsdRate(mid.floatValue());
                            currency.setCnDisplay(c.getCnDisplay());
                            currency.setEnDisplay(c.getEnDisplay());
                            currency.setCnDisplayOrder(c.getCnDisplayOrder());
                            currency.setEnDisplayOrder(c.getEnDisplayOrder());
                            currency.setLabel1(c.getLabel1());
                            currency.setLabel2(c.getLabel2());
                            currency.setLabel3(c.getLabel3());
                            currency.setPattern(c.getPattern());
                            currency.setLastRateUpdateDate(Instant.now());
                            updateCurrencyList.add(currency);
                        }
                    }
                }
            }
        });
        log.info("[DayCurrencyRate] batch save currency rate param:{}", JSONUtil.toJsonStr(currencyRateDayList));
        if (!currencyRateDayList.isEmpty()) {
            currencyRateDayService.batchSave(currencyRateDayList);
            log.info("[DayCurrencyRate] success batch save currency rate param:{}", JSONUtil.toJsonStr(currencyRateDayList));
        }
        log.info("[DayCurrencyRate] batch update enum currency param:{}", JSONUtil.toJsonStr(updateCurrencyList));
        if(!updateCurrencyList.isEmpty()){
            enumCurrencyRepository.saveAll(updateCurrencyList);
            log.info("[DayCurrencyRate] success batch update enum currency param:{}", JSONUtil.toJsonStr(updateCurrencyList));

            rabbitTemplate.convertAndSend(currencyExchange,"",JSONUtil.toJsonStr(updateCurrencyList));
        }
    }

    Map<Integer, String> getCurrencyNameMap() {
        if (currencyNameMap == null || currencyNameMap.isEmpty()) {
            synchronized (InvoiceServiceImpl.class) {
                currencyNameMap = enumCommonService.findAllEnumCurrency().stream().filter(t -> StringUtils.isNotEmpty(t.getName())).collect(Collectors.toMap(EnumCurrency::getId, EnumCurrency::getName, (a1, a2) -> a1));
            }
        }
        return currencyNameMap;
    }
}
