package com.altomni.apn.finance.service.vo.invoicing;

import com.altomni.apn.common.domain.enumeration.company.InvoicingPaymentMethodType;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.Instant;

@Data
public class InvoicingRecordPaymentInfoVO {

    private Long id;

    private Long invoicingId ;

    private Instant paymentDate ;

    private BigDecimal paymentAmount ;

    private InvoicingPaymentMethodType paymentMethod ;

    private Long prepaymentId ;

    private String candidateJson ;

    private String note ;
}
