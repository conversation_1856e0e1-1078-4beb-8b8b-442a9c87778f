package com.altomni.apn.finance.repository.invoice;

import com.altomni.apn.finance.domain.invoice.QViewInvoiceList;
import com.altomni.apn.finance.domain.invoice.ViewInvoiceList;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ViewInvoiceListRepository extends JpaRepository<ViewInvoiceList, Long>, QuerydslPredicateExecutor<ViewInvoiceList>, QuerydslBinderCustomizer<QViewInvoiceList> {

    @Override
    default public void customize(QuerydslBindings bindings, QViewInvoiceList root) {
        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    }

    List<ViewInvoiceList> findByTenantId(Long tenantId);
}
