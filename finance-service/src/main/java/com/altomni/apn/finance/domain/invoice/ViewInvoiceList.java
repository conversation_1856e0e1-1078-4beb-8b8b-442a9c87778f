package com.altomni.apn.finance.domain.invoice;

import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatusConverter;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceType;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceTypeConverter;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.annotations.Immutable;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * A InvoiceList.
 */

@Entity
@Immutable
@Table(name = "view_invoice_list")
public class ViewInvoiceList implements Serializable {

    private static final long serialVersionUID = -3146354978975155570L;

    @Id
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "invoice_no")
    private String invoiceNo;

    @Column(name = "sub_invoice_no")
    private String subInvoiceNo;

    @Convert(converter = InvoiceTypeConverter.class)
    @Column(name = "invoice_type")
    private InvoiceType invoiceType;

    @Convert(converter = InvoiceStatusConverter.class)
    @Column(name = "status")
    private InvoiceStatus status;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "job_title")
    private String jobTitle;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "talent_name")
    private String talentName;

    @Column(name = "company_id")
    private Long companyId;

    @Column(name = "customer_name")
    private String customerName;

    @Column(name = "team_id", length = 50)
    private Long teamId;

    @ApiModelProperty(value = "The amount for current invoice. If it is a split invoice, merge all due amounts equals total amount." +
        "eg: if split == false, then totalAmount = dueAmount;" +
        "if split == true, totalAmount += all sub invoice's dueAmount")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @Column(name = "due_amount")
    private BigDecimal dueAmount;

    @Column(name = "invoice_date")
    private LocalDate invoiceDate;

    @Column(name = "due_date")
    private LocalDate dueDate;

    @Column(name = "currency")
    private Integer currency;

    @Column(name = "created_date")
    private Instant createdDate;


    @Column(name = "received_amount")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal receivedAmount;

    @Column(name = "balance")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal balance;

    @Column(name = "payment_date")
    private LocalDate paymentDate;

    @Column(name = "am")
    private String am;

    @Column(name = "co_am")
    private String coAm;

    @Column(name = "recruiter")
    private String recruiter;

    @Transient
    private List<UserCountryVO> coAmList;

    @Column(name = "sales_lead_owner")
    private String salesLeadOwner;

    @Column(name = "bd_owner")
    private String bdOwner;

    @Column(name = "ac")
    private String ac;

    @Column(name = "dm")
    private String dm;

    @Column(name = "sourcer")
    private String sourcer;

    @Column(name = "owner")
    private String owner;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getSubInvoiceNo() {
        return subInvoiceNo;
    }

    public void setSubInvoiceNo(String subInvoiceNo) {
        this.subInvoiceNo = subInvoiceNo;
    }

    public InvoiceType getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(InvoiceType invoiceType) {
        this.invoiceType = invoiceType;
    }

    public InvoiceStatus getStatus() {
        return status;
    }

    public void setStatus(InvoiceStatus status) {
        this.status = status;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public String getTalentName() {
        return talentName;
    }

    public void setTalentName(String talentName) {
        this.talentName = talentName;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long divisionId) {
        this.teamId = divisionId;
    }

    public BigDecimal getDueAmount() {
        return dueAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(dueAmount));
    }

    public void setDueAmount(BigDecimal dueAmount) {
        this.dueAmount = dueAmount;
    }

    public LocalDate getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(LocalDate invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public LocalDate getDueDate() {
        return dueDate;
    }

    public void setDueDate(LocalDate dueDate) {
        this.dueDate = dueDate;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public BigDecimal getReceivedAmount() {
        return receivedAmount == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(receivedAmount));
    }

    public void setReceivedAmount(BigDecimal receivedAmount) {
        this.receivedAmount = receivedAmount;
    }

    public BigDecimal getBalance() {
        return balance == null ? null : new BigDecimal(InvoiceConstants.DECIMAL_FORMAT.format(balance));
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public LocalDate getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(LocalDate paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getAm() {
        return am;
    }

    public void setAm(String am) {
        this.am = am;
    }

    public String getCoAm() {
        return coAm;
    }

    public void setCoAm(String coAm) {
        this.coAm = coAm;
    }

    public List<UserCountryVO> getCoAmList() {
        return coAmList;
    }

    public void setCoAmList(List<UserCountryVO> coAmList) {
        this.coAmList = coAmList;
    }

    public String getRecruiter() {
        return recruiter;
    }

    public void setRecruiter(String recruiter) {
        this.recruiter = recruiter;
    }

    public String getSalesLeadOwner() { return salesLeadOwner; }

    public void setSalesLeadOwner(String salesLeadOwner) { this.salesLeadOwner = salesLeadOwner; }

    public String getBdOwner() { return bdOwner; }

    public void setBdOwner(String bdOwner) { this.bdOwner = bdOwner; }

    public String getAc() { return ac; }

    public void setAc(String ac) { this.ac = ac; }

    public String getDm() { return dm; }

    public void setDm(String dm) { this.dm = dm; }

    public String getSourcer() { return sourcer; }

    public void setSourcer(String sourcer) { this.sourcer = sourcer; }

    public String getOwner() { return owner; }

    public void setOwner(String owner) { this.owner = owner; }

    @Override
    public String toString() {
        return "ViewInvoiceList{" +
                "id=" + id +
                ", tenantId=" + tenantId +
                ", invoiceNo='" + invoiceNo + '\'' +
                ", subInvoiceNo='" + subInvoiceNo + '\'' +
                ", invoiceType=" + invoiceType +
                ", status=" + status +
                ", jobId=" + jobId +
                ", jobTitle='" + jobTitle + '\'' +
                ", talentId=" + talentId +
                ", talentName='" + talentName + '\'' +
                ", companyId=" + companyId +
                ", customerName='" + customerName + '\'' +
                ", teamId=" + teamId +
                ", dueAmount=" + dueAmount +
                ", invoiceDate=" + invoiceDate +
                ", dueDate=" + dueDate +
                ", currency=" + currency +
                ", createdDate=" + createdDate +
                ", receivedAmount=" + receivedAmount +
                ", balance=" + balance +
                ", paymentDate=" + paymentDate +
                '}';
    }
}
