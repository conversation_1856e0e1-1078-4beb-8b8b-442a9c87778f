package com.altomni.apn.finance.repository.invoicing;

import com.altomni.apn.finance.domain.invoicing.InvoicingRecordPaymentInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

@Repository
public interface InvoicingRecordPaymentInfoRepository extends JpaRepository<InvoicingRecordPaymentInfo,Long> {

    @Modifying
    @Transactional
    @Query(value = "update invoicing_record_payment_info set status=0 where invoicing_id = ?1", nativeQuery = true)
    void updateStatusByInvoicingId(Long invoicingId);

    @Modifying
    @Transactional
    @Query(value = "update invoicing_record_payment_info set status=0 where id = ?1", nativeQuery = true)
    void updateStatusById(Long id);

    @Modifying
    @Transactional
    @Query(value = "update invoicing_record_payment_info set status=0 where id in(?1)", nativeQuery = true)
    void updateStatusByIds(List<Long> idList);

    List<InvoicingRecordPaymentInfo> findByInvoicingIdAndStatus(Long invoicingId,Integer status);

    List<InvoicingRecordPaymentInfo> findByInvoicingIdInAndStatus(List<Long> invoicingId,Integer status);

    List<InvoicingRecordPaymentInfo> findByStatus(Integer status);

    @Query(value = "select * from invoicing_record_payment_info where invoicing_id in(?1) and  prepayment_id is not null and status=1", nativeQuery = true)
    List<InvoicingRecordPaymentInfo> findAllByInvoicingId(Collection<Long> invoicingIdList);
}
