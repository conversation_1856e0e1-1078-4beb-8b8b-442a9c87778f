package com.altomni.apn.finance.web.rest.invoice;

import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.finance.domain.invoice.InvoiceClientCredit;
import com.altomni.apn.finance.service.dto.invoice.InvoiceActivityDTO;
import com.altomni.apn.finance.service.dto.invoice.InvoiceClientCreditDTO;
import com.altomni.apn.finance.service.invoice.InvoiceClientCreditService;
import com.altomni.apn.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

@RestController
@RequestMapping("/api/v3")
@Transactional
public class InvoiceClientCreditResource {

    private final Logger log = LoggerFactory.getLogger(InvoiceClientCreditResource.class);

    private static final String ENTITY_NAME = "InvoiceClientCredit";

    @Resource
    private InvoiceClientCreditService clientCreditService;

    @PostMapping("/invoice-client-credits/apply")
    public ResponseEntity<InvoiceActivityDTO> createInvoiceClientCredit(@RequestBody InvoiceClientCreditDTO clientCredit) throws URISyntaxException {
        log.info("[APN: InvoiceClientCredit @{}] REST request to save InvoiceClientCredit : {}", SecurityUtils.getUserId(), clientCredit);
        if (clientCredit.getId() != null) {
            throw new CustomParameterizedException("A new InvoiceClientCredit cannot already have an ID", ENTITY_NAME, "id exists");
        }
        InvoiceActivityDTO result = clientCreditService.apply(clientCredit);
        return ResponseEntity.created(new URI("/api/v3/invoices/" + result.getInvoiceId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getInvoiceId().toString()))
            .body(result);
    }

    @GetMapping("/invoice-client-credits/companyId/{companyId}")
    public ResponseEntity<List<InvoiceClientCredit>> getAllInvoiceClientCredit(@PathVariable Long companyId) {
        log.info("[APN: Invoice @{}] REST request to get all InvoiceClientCredit by companyId: {}", SecurityUtils.getUserId(), companyId);
        List<InvoiceClientCredit> result = clientCreditService.getAllInvoiceClientCredit(companyId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(result));
    }
}
