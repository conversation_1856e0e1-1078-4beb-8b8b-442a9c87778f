package com.altomni.apn.finance.service.invoice;

import com.altomni.apn.finance.domain.invoice.InvoiceActivity;
import com.altomni.apn.finance.service.dto.invoice.InvoiceActivityDTO;
import com.altomni.apn.finance.service.dto.invoice.InvoicePaymentRecordDTO;

import java.util.List;

public interface InvoicePaymentRecordService {

    InvoiceActivityDTO create(InvoicePaymentRecordDTO invoicePaymentRecordDTO);

    List<InvoicePaymentRecordDTO> findByInvoiceId(Long id);

    InvoiceActivityDTO unRecordPaymentById(Long id);
}
