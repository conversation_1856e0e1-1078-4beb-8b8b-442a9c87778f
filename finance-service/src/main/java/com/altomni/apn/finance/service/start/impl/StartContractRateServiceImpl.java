package com.altomni.apn.finance.service.start.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateVO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.domain.start.Start;
import com.altomni.apn.finance.domain.start.StartClientInfo;
import com.altomni.apn.finance.domain.start.StartContractRate;
import com.altomni.apn.finance.repository.start.StartAddressRepository;
import com.altomni.apn.finance.repository.start.StartClientInfoRepository;
import com.altomni.apn.finance.repository.start.StartContractRateRepository;
import com.altomni.apn.finance.repository.start.StartRepository;
import com.altomni.apn.finance.service.application.ApplicationService;
import com.altomni.apn.finance.service.company.CompanyService;
import com.altomni.apn.finance.service.start.StartContractRateService;
import com.altomni.apn.finance.service.start.StartService;
import com.altomni.apn.finance.service.vo.StartContractRateCheckVO;
import com.altomni.apn.finance.utils.PeriodOverlapChecker;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.unit.DataUnit;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import static java.time.temporal.ChronoUnit.DAYS;

@Service
@Transactional
public class StartContractRateServiceImpl implements StartContractRateService {

    private final Logger log = LoggerFactory.getLogger(StartContractRateServiceImpl.class);

    @Resource
    private EntityManager entityManager;

    @Resource
    private StartContractRateRepository startContractRateRepository;

    @Resource
    private StartClientInfoRepository startClientInfoRepository;

    @Resource
    private StartRepository startRepository;

    @Resource
    private StartService startService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private CompanyService companyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Resource
    private StartAddressRepository startAddressRepository;

    private Start validateStart(Long startId) {
        Optional<Start> startOptional = startRepository.findById(startId);
        if (startOptional.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATESTART_STARTNOEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        Start start = startOptional.get();
        if (SecurityUtils.isAdmin() || SecurityUtils.isSystemAdmin()) {
            return start;
        }
        if (!ObjectUtils.equals(start.getTenantId(), SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATESTART_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        return start;
    }

    @Override
    public List<StartContractRate> create(StartContractRate newRate) {
        this.checkPermission(newRate.getStartId());
        Start start = validateStart(newRate.getStartId());
        validation(newRate);
        checkTotalBillAmount(newRate);
        newRate.setEndDate(start.getEndDate());
        startContractRateRepository.save(newRate);
        return findAllByStartId(start.getId());
    }

    @Override
    public List<StartContractRate> rateChange(StartContractRate newRate) {
        /*if (newRate.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_RATECHANGE_STARTCONTRACTRATEIDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }*/
        this.checkPermission(newRate.getStartId());
        Start start = validateStart(newRate.getStartId());
        validation(newRate);
        //StartContractRate lastRate = startContractRateRepository.findLastByStartId(start.getId());
        StartContractRate lastRate = startContractRateRepository.findById(newRate.getId()).get();
        if (lastRate == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_RATECHANGE_LASTRATEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        lastRate.setEndDate(newRate.getStartDate().minusDays(1));
        lastRate.setTotalBillAmount(getTotalBillAmount(lastRate));

        startContractRateRepository.save(lastRate);
        //newRate.setEndDate(start.getEndDate());
        newRate.setExtendStartContractRateId(lastRate.getId());
        checkTotalBillAmount(newRate);
        newRate.setTenantId(SecurityUtils.getTenantId());
        newRate.setId(null);
        startContractRateRepository.save(newRate);
        return findAllByStartId(start.getId());
    }

    private void validation(StartContractRate startContractRate) {
        if (startContractRate.getStartDate() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATION_STARTDATEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        if (startContractRate.getFinalBillRate() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATION_FINALBILLRATEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        if (startContractRate.getFinalPayRate() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATION_FINALPAYRATEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        if (startContractRate.getTaxBurdenRateCode() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATION_TAXBURDENRATECODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        if (startContractRate.getMspRateCode() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATION_MSPRATECODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        if (startContractRate.getImmigrationCostCode() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATION_IMMIGRATIONCOSTCODE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        if (startContractRate.getEstimatedWorkingHourPerWeek() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATION_ESTIMATEDWORKINGHOURPERWEEK.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
    }

    private void checkTotalBillAmount(StartContractRate startContractRate) {
        BigDecimal calTotalBillAmount = getTotalBillAmount(startContractRate);
        if (calTotalBillAmount.compareTo(startContractRate.getTotalBillAmount()) != 0) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CHECKTOTALBILLAMOUNT_AMOUNTINCORRECT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(calTotalBillAmount), financeApiPromptProperties.getFinanceService()));
        }
    }

    @Override
    public BigDecimal getTotalBillAmount(StartContractRate startContractRate) {
        BigDecimal finalBillRate = CommonUtils.convertToHourlyRate(startContractRate.getFinalBillRate(), startContractRate.getRateUnitType());
        BigDecimal finalPayRate = CommonUtils.convertToHourlyRate(startContractRate.getFinalPayRate(), startContractRate.getRateUnitType());

        BigDecimal taxBurden = finalPayRate.multiply(getOfferLetterCostRateValue(startContractRate.getTaxBurdenRateCode())).divide(new BigDecimal(100), 10, RoundingMode.HALF_UP);
        BigDecimal msp = finalBillRate.multiply(getOfferLetterCostRateValue(startContractRate.getMspRateCode())).divide(new BigDecimal(100), 10, RoundingMode.HALF_UP);
        BigDecimal immigrationCost = getOfferLetterCostRateValue(startContractRate.getImmigrationCostCode());

        BigDecimal hourlyAmount = finalBillRate.subtract(finalPayRate).subtract(taxBurden).subtract(msp);
        long noOfDaysBetween = DAYS.between(startContractRate.getStartDate(), startContractRate.getEndDate()) + 1; //total for days between two dates should add one day more
        BigDecimal noOfWeeksBetween = new BigDecimal(noOfDaysBetween).divide(new BigDecimal(7), 14, RoundingMode.HALF_UP);
        BigDecimal yearlyAmount = hourlyAmount.multiply(noOfWeeksBetween).multiply(startContractRate.getEstimatedWorkingHourPerWeek());
        BigDecimal calTotalBillAmount = yearlyAmount.subtract(immigrationCost).subtract(startContractRate.getExtraCost()).setScale(5, RoundingMode.HALF_UP).setScale(2, RoundingMode.HALF_UP);
        return calTotalBillAmount;
    }

    private BigDecimal getOfferLetterCostRateValue(String code) {
        TalentRecruitmentProcessIpgOfferLetterCostRateVO offerLetterCostRate = applicationService.getOfferLetterCostRate(code);
        if (offerLetterCostRate == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_GETOFFERLETTERCOSTRATEVALUE_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(code), financeApiPromptProperties.getFinanceService()));
        }
        return offerLetterCostRate.getValue();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<StartContractRate> update(StartContractRate update) {
        StartContractRate exists = findById(update.getId());
        this.checkPermission(exists.getStartId());
        Start start = validateStart(exists.getStartId());
        LocalDate preStartDate = exists.getStartDate();
        List<StartContractRate> startContractRateList = startContractRateRepository.findAllByStartId(start.getId());
        startContractRateList = startContractRateList.stream().sorted(Comparator.comparing(StartContractRate::getEndDate).reversed()).collect(Collectors.toList());
        if (startContractRateList.size() == 1) {

            ServiceUtils.myCopyProperties(update, exists);
            checkTotalBillAmount(exists);
            startContractRateRepository.save(exists);
            log.info("[APN: StartContractRate @{}] REST request to update StartContractRate , save StartClientInfo: {}, {}", SecurityUtils.getUserId(), exists);

            start.setStartDate(exists.getStartDate());
            start.setEndDate(exists.getEndDate());

            startRepository.save(start);
            log.info("[APN: StartContractRate @{}] REST request to update start , userId: {}, {}", SecurityUtils.getUserId(), start);

            // 如果start date有变化，则更新 talent experience
            if (!Objects.equals(preStartDate, update.getStartDate())){
                start.setStartAddress(startAddressRepository.findByStartId(start.getId()).orElse(null));
                startService.updateTalentExperience(start);
            }

        } else {
            List<StartContractRate> deleteRate = new ArrayList<>();
            StartContractRate modifyRate = new StartContractRate();
            for (StartContractRate rate : startContractRateList) {
                if (!rate.getId().equals(update.getId())) {
                    if (update.getStartDate().isBefore(rate.getStartDate())) {
                        deleteRate.add(rate);
                    } else if (update.getStartDate().isAfter(rate.getEndDate())) {
                        BeanUtils.copyProperties(rate, modifyRate);
                        break;
                    }  else {
                        BeanUtils.copyProperties(rate, modifyRate);
                        break;
                    }
                }
            }

            if (modifyRate.getId() != null) {
                modifyRate.setEndDate(update.getStartDate().minusDays(1));
                modifyRate.setTotalBillAmount(getTotalBillAmount(modifyRate));
                startContractRateRepository.save(modifyRate);
                log.info("[APN: StartContractRate @{}] REST request to update StartContractRate , save StartClientInfo: {}, {}", SecurityUtils.getUserId(), modifyRate);
            }

            ServiceUtils.myCopyProperties(update, exists, StartContractRate.UpdateSkipProperties);
            //recalculate total bill amount for current update rate
            checkTotalBillAmount(exists);
            startContractRateRepository.save(exists);

            if (!deleteRate.isEmpty()) {
                startContractRateRepository.deleteAll(deleteRate);
                log.info("[APN: StartContractRate @{}] REST request to update StartContractRate , delete StartClientInfo: {}, {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(deleteRate));
            }
        }

        /*StartContractRate lastRate = startContractRateRepository.findLastByStartId(exists.getStartId());
        if (!exists.equals(lastRate)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_UPDATE_LASTRATENOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }

        if (lastRate.getExtendStartContractRateId() == null) {
            // only have one record
            ServiceUtils.myCopyProperties(update, exists);
            checkTotalBillAmount(exists);
            startContractRateRepository.save(exists);
            start.setStartDate(exists.getStartDate());
            start.setEndDate(exists.getEndDate());
            startRepository.save(start);
        } else {
            // have multiple rate records, use extend start contract rate id to find the previous rate.
            Optional<StartContractRate> secondLastRateOpt = startContractRateRepository.findById(lastRate.getExtendStartContractRateId());

            StartContractRate secondLastRate = secondLastRateOpt.get();
            if (update.getStartDate() != null && !exists.getStartDate().equals(update.getStartDate())) {
                // start date changed, need recalculate total bill amount for the second last rate.
                secondLastRate.setEndDate(update.getStartDate().minusDays(1));
                secondLastRate.setTotalBillAmount(getTotalBillAmount(secondLastRate));
                startContractRateRepository.save(secondLastRate);
            }
            ServiceUtils.myCopyProperties(update, exists, StartContractRate.UpdateSkipProperties);
            //recalculate total bill amount for current update rate
            checkTotalBillAmount(exists);
            startContractRateRepository.save(exists);
        }*/


        if (update.getStartClientInfo() != null) {
            StartClientInfo clientInfo = startClientInfoRepository.findByStartId(exists.getStartId());
            if (null == clientInfo) {
                clientInfo = new StartClientInfo();
                clientInfo.setStartId(exists.getStartId());
                clientInfo.setClientAddress(update.getStartClientInfo().getClientAddress());
                clientInfo.setClientName(update.getStartClientInfo().getClientName());
                clientInfo.setClientDivision(update.getStartClientInfo().getClientDivision());
                startClientInfoRepository.save(clientInfo);
                log.info("[APN: StartContractRate @{}] REST request to update StartContractRate , save StartClientInfo: {}, {}", SecurityUtils.getUserId(), clientInfo);
            } else {
                startClientInfoRepository.updateClientInfoByStartId(exists.getStartId(), update.getStartClientInfo().getClientName(), update.getStartClientInfo().getClientAddress(), update.getStartClientInfo().getClientDivision());
            }
        }
        startRepository.updateStartChargeNumberAndTvcNumberAndCorpToCorpByStartId(exists.getStartId(), update.getChargeNumber(), update.getTvcNumber(), update.getCorpToCorp());
        return findAllByStartIdWithStartInfo(start.getId(), update);
    }

    private void checkPermission(Long startId) {
        if (BooleanUtils.isTrue(SecurityUtils.isAdmin())) {
            return;
        }
        Long companyId = startRepository.getCompanyIdByStartId(startId);
        if (Objects.isNull(companyId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CHECKPERMISSION_COMPANYIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        List<Long> allAmIdsByCompanyId = companyService.getAllAmIdsByCompanyId(companyId);
        boolean isCurrentCompanyAm = allAmIdsByCompanyId.contains(SecurityUtils.getUserId());
        if (BooleanUtils.isFalse(isCurrentCompanyAm)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_CHECKPERMISSION_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
    }

    @Override
    public List<StartContractRate> findAllByStartId(Long startId) {
        return startContractRateRepository.findAllByStartId(startId).stream().map(this::addEntities).sorted(Comparator.comparing(StartContractRate::getEndDate)).collect(Collectors.toList());
    }

    public List<StartContractRate> findAllByStartIdWithStartInfo(Long startId, StartContractRate startContractRate) {
        List<StartContractRate> startContractRateList = startContractRateRepository.findAllByStartId(startId).stream().map(this::addEntities).collect(Collectors.toList());
        startContractRateList.forEach(rateChange -> {
            rateChange.setTvcNumber(startContractRate.getTvcNumber());
            rateChange.setChargeNumber(startContractRate.getChargeNumber());
            rateChange.setCorpToCorp(startContractRate.getCorpToCorp());
            if (startContractRate.getStartClientInfo() != null) {
                rateChange.setStartClientInfo(startContractRate.getStartClientInfo());
            }
        });
        return startContractRateList;
    }

    private StartContractRate addEntities(StartContractRate startContractRate) {
        if (startContractRate != null) {
            Authentication authentication = SecurityUtils.getAuthentication();
            CompletableFuture<TalentRecruitmentProcessIpgOfferLetterCostRateVO> taxBurdenRateCompletableFuture = null;
            if (StringUtils.isNotEmpty(startContractRate.getTaxBurdenRateCode())) {
                taxBurdenRateCompletableFuture = CompletableFuture.supplyAsync(() -> {
                    SecurityUtils.setAuthentication(authentication);
                    return applicationService.getOfferLetterCostRate(startContractRate.getTaxBurdenRateCode());
                });

            }
            CompletableFuture<TalentRecruitmentProcessIpgOfferLetterCostRateVO> mspRateCompletableFuture = null;
            if (StringUtils.isNotEmpty(startContractRate.getMspRateCode())) {
                mspRateCompletableFuture = CompletableFuture.supplyAsync(() -> {
                    SecurityUtils.setAuthentication(authentication);
                    return applicationService.getOfferLetterCostRate(startContractRate.getMspRateCode());
                });
            }

            CompletableFuture<TalentRecruitmentProcessIpgOfferLetterCostRateVO> immigrationCostCompletableFuture = null;
            if (StringUtils.isNotEmpty(startContractRate.getImmigrationCostCode())) {
                immigrationCostCompletableFuture = CompletableFuture.supplyAsync(() -> {
                    SecurityUtils.setAuthentication(authentication);
                    return applicationService.getOfferLetterCostRate(startContractRate.getImmigrationCostCode());
                });
            }
            try {
                if (Objects.nonNull(taxBurdenRateCompletableFuture)) {
                    startContractRate.setTaxBurdenRate(taxBurdenRateCompletableFuture.get());
                }
                if (Objects.nonNull(mspRateCompletableFuture)) {
                    startContractRate.setMspRate(mspRateCompletableFuture.get());
                }
                if (Objects.nonNull(immigrationCostCompletableFuture)) {
                    startContractRate.setImmigrationCost(immigrationCostCompletableFuture.get());
                }
            } catch (InterruptedException e) {
                log.error(e.getMessage());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_COMMON_INTERNETERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            } catch (ExecutionException e) {
                log.error(e.getMessage());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_COMMON_INTERNETERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
            }
        }
        return startContractRate;
    }

    @Override
    public List<StartContractRate> delete(Long id) {
        StartContractRate exists = findById(id);
        Start start = validateStart(exists.getStartId());

        List<StartContractRate> rateList = startContractRateRepository.findAllByStartIdIn(Arrays.asList(exists.getStartId()));
        if (rateList.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_DELETE_LASTRATENOEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        rateList = rateList.stream().sorted(Comparator.comparing(StartContractRate::getEndDate).reversed()).collect(Collectors.toList());
        if (rateList.size() == 2) {
            StartContractRate lastRate = rateList.get(0);
            StartContractRate firstRate = rateList.get(1);
            firstRate.setEndDate(lastRate.getEndDate());
            firstRate.setTotalBillAmount(getTotalBillAmount(firstRate));
            startContractRateRepository.save(firstRate);
            startContractRateRepository.delete(lastRate);

        } else if (rateList.size() > 2) {
            StartContractRate lastRate = rateList.get(0);
            StartContractRate firstRate = rateList.get(1);
            firstRate.setEndDate(lastRate.getEndDate());
            firstRate.setTotalBillAmount(getTotalBillAmount(firstRate));
            startContractRateRepository.save(firstRate);
            startContractRateRepository.delete(lastRate);
        }
        return findAllByStartId(start.getId());
    }

    @Override
    public StartContractRate findById(Long id) {
        Optional<StartContractRate> existsOpt = startContractRateRepository.findById(id);

        StartContractRate exists = existsOpt.get();
        if (exists == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_FINDBYID_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), financeApiPromptProperties.getFinanceService()));
        }
        return addEntities(exists);
    }

    @Override
    public StartContractRate findLastByStartId(Long startId) {
        StartContractRate lastRate = startContractRateRepository.findLastByStartId(startId);
        if (lastRate == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_FINDLASTBYSTARTID_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(startId), financeApiPromptProperties.getFinanceService()));
        }
        return addEntities(lastRate);
    }

    @Override
    public StartContractRate save(StartContractRate startContractRate) {
        return addEntities(startContractRateRepository.save(startContractRate));
    }

    @Override
    public StartContractRateCheckVO checkRateDate(Long startId, StartContractRate startContractRate) {
        if (startId == null || startContractRate.getStartDate() == null || startContractRate.getEndDate() == null || startContractRate.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_RATECHANGE_PARAM_NULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(startId), financeApiPromptProperties.getFinanceService()));
        }
        List<StartContractRate> startContractRateList = startContractRateRepository.findAllByStartId(startId);
        if (startContractRateList.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_VALIDATIONSTART_JOBNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(startId), financeApiPromptProperties.getFinanceService()));
        }

        StartContractRateCheckVO vo = new StartContractRateCheckVO();

        if (startContractRateList.size() != 1) {
            List<StartContractRate> deleteRate = new ArrayList<>();
            List<StartContractRate> modifyRate = new ArrayList<>();

            startContractRateList = startContractRateList.stream().sorted(Comparator.comparing(StartContractRate::getEndDate).reversed()).collect(Collectors.toList());
            for (StartContractRate rate : startContractRateList) {
                if (!rate.getId().equals(startContractRate.getId())) {
                    if (startContractRate.getStartDate().isBefore(rate.getStartDate())) {
                        deleteRate.add(rate);
                    } else if (startContractRate.getStartDate().isAfter(rate.getEndDate())) {
                        modifyRate.add(rate);
                        break;
                    } else {
                        modifyRate.add(rate);
                        break;
                    }
                }
            }

            vo.setDeleteDate(deleteRate);
            vo.setModifyDate(modifyRate);
        }
        return vo;
    }
}
