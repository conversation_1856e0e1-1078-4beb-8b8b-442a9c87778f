package com.altomni.apn.finance.service.vo.invoicing;

import com.altomni.apn.common.domain.enumeration.company.InvoicingLogStatus;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.finance.domain.invoicing.InvoicingRecordLog;
import lombok.Data;

@Data
public class InvoicingRecordLogVO {

    private String title;

    private String subTitle;

    private String operatedDate;

    private String operatedName;

    private String note;

    private String noteJson;

    private InvoicingLogStatus invoicingStatus;

    private String electronicInvoiceNumber ;

    /**  */
    private Long referenceId ;

    public static InvoicingRecordLogVO formToVO(InvoicingRecordLog log) {
        InvoicingRecordLogVO vo = new InvoicingRecordLogVO();
        vo.setTitle(log.getTitle());
        vo.setSubTitle(log.getSubTitle());
        vo.setNote(log.getNote());
        vo.setNoteJson(log.getNoteJson());
        vo.setOperatedName(log.getOperatedByName());
        vo.setOperatedDate(DateUtil.fromInstantToDate(log.getOperatedDate(),DateUtil.CN_BJ_TIMEZONE));
        vo.setInvoicingStatus(log.getInvoicingStatus());
        vo.setElectronicInvoiceNumber(log.getElectronicInvoiceNumber());
        vo.setReferenceId(log.getReferenceId());
        return vo;
    }
}
