package com.altomni.apn.finance.service.dto.invoice;

import java.io.Serializable;

/**
 * A InvoiceVoidDTO.
 */
public class InvoiceVoidDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long invoiceId;

    private String invoiceNo;

    private String reason;

    public Long getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
    }

    public String getInvoiceNo() {
        return invoiceNo;
    }

    public void setInvoiceNo(String invoiceNo) {
        this.invoiceNo = invoiceNo;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public InvoiceVoidDTO invoiceId(Long invoiceId) {
        this.invoiceId = invoiceId;
        return this;
    }

    public InvoiceVoidDTO reason(String reason) {
        this.reason = reason;
        return this;
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "InvoiceVoidDTO{" +
                "invoiceId=" + invoiceId +
                ", invoiceNo='" + invoiceNo + '\'' +
                ", reason='" + reason + '\'' +
                '}';
    }
}
