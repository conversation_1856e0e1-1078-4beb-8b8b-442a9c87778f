package com.altomni.apn.finance.service.talent.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.talent.TalentV3;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.enumeration.enums.FinanceAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.config.env.FinanceApiPromptProperties;
import com.altomni.apn.finance.service.talent.TalentClient;
import com.altomni.apn.finance.service.talent.TalentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class TalentServiceImpl implements TalentService {
    @Resource
    private TalentClient talentClient;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    FinanceApiPromptProperties financeApiPromptProperties;

    @Override
    public void save(TalentV3 talent) {
        TalentDTOV3 talentDTOV3 = new TalentDTOV3();
        ServiceUtils.myCopyProperties(talent, talentDTOV3);
        talentClient.updateTalent(talentDTOV3.getId(), talentDTOV3);
    }

    @Override
    public TalentV3 getTalent(Long talentId) {
        ResponseEntity<TalentV3> response = talentClient.getTalent(talentId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public TalentDTOV3 getTalentWithoutEntity(Long id) {
        ResponseEntity<TalentDTOV3> response = talentClient.getTalentWithoutEntity(id);
        return response != null ? response.getBody() : null;
    }

    @Override
    public void syncTalentToES(Long talentId) {
        HttpResponse response = talentClient.syncTalentToES(talentId).getBody();
        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: TalentServiceImpl @{}] syncTalentToES error, id: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response.getCode(), response.getBody());
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_SYNCTALENTTOES_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
            }
        } else {
            log.info("[APN: TalentServiceImpl @{}] syncTalentToES and response is null", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(FinanceAPIMultilingualEnum.START_SYNCTALENTTOES_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),financeApiPromptProperties.getFinanceService()));
        }
    }

    @Override
    public void updateTalentExperience(Long talentId, TalentExperienceDTO talentExperienceDTO) {
        talentClient.updateTalentExperience(talentId, talentExperienceDTO);
    }
}
