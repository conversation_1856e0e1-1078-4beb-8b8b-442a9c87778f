package com.altomni.apn.finance.service.company;

import com.altomni.apn.common.vo.company.CompanyClientInfoVO;
import com.altomni.apn.common.vo.company.CompanyClientInvoicingInfoVO;
import com.altomni.apn.company.domain.company.Company;
import com.altomni.apn.company.domain.company.CompanyContact;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

public interface CompanyService {
    Company getCompany(Long companyId);

    List<CompanyContact> getCompanyContacts(Long companyId);

    List<Long> getAllAmIdsByCompanyId(Long companyId);

    CompanyClientInvoicingInfoVO getInvoicingClientInfoById(Long id);

    List<CompanyClientInfoVO> getCompanyClientInfoList(Long companyId);

    CompanyClientInfoVO getCompanyClientInfoById(Long id);

    List<CompanyClientInvoicingInfoVO> getCompanyInvoicingClientInfoList(Long companyId);
}
