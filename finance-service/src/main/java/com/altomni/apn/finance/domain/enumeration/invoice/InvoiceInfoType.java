package com.altomni.apn.finance.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The InvoiceStatus enumeration.
 */
public enum InvoiceInfoType implements ConvertedEnum<Integer> {
    CHINA(0),
    OVERSEAS(1);


    private final Integer dbValue;

    InvoiceInfoType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<InvoiceInfoType, Integer> resolver =
        new ReverseEnumResolver<>(InvoiceInfoType.class, InvoiceInfoType::toDbValue);

    public static InvoiceInfoType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
