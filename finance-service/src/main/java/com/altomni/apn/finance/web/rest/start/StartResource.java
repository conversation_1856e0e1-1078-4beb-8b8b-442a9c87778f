package com.altomni.apn.finance.web.rest.start;

import cn.hutool.json.JSONArray;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.application.dashboard.MyCandidate;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.start.Start;
import com.altomni.apn.finance.repository.start.StartRepository;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.dto.start.StartSearchSimpleDTO;
import com.altomni.apn.finance.service.start.StartService;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * REST controller for managing Start.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StartResource {

    private static final String ENTITY_NAME = "start";

    @Resource
    private StartService startService;

    @Resource
    private StartRepository startRepository;

    /**
     * POST  /starts : Create a new start.
     *
     * @param start the start to create
     * @return the ResponseEntity with status 201 (Created) and with body the new start, or with status 400 (Bad Request) if the start has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/starts")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<StartDTO> createStart(@Valid @RequestBody StartDTO start) throws URISyntaxException, IOException {
        log.info("[APN: Start @{}] REST request to save Start : {}", SecurityUtils.getUserId(), start);
        if (start.getId() != null) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(ENTITY_NAME, "idexists", "A new start cannot already have an ID")).body(null);
        }
        startService.checkPermissionByCompanyId(start.getCompanyId());
        StartDTO result = startService.save(start);
        return ResponseEntity.created(new URI("/api/v3/starts/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, String.valueOf(result.getId())))
            .body(result);
    }

    /**
     * PUT  /starts : Updates an existing start. Only start base info, commission will update by other APIs
     *
     * @param start the start to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated start,
     * or with status 400 (Bad Request) if the start is not valid,
     * or with status 500 (Internal Server Error) if the start couldn't be updated
     */
    @PutMapping("/starts/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Start> updateStart(@PathVariable Long id, @Valid @RequestBody Start start) throws IOException {
        log.info("[APN: Start @{}] REST request to update Start : {}", SecurityUtils.getUserId(), start);
        if (start.getId() == null) {
            start.setId(id);
        }
        //startService.checkPermissionByStartId(id);
        Start result = startService.update(start);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, start.getId().toString()))
            .body(result);
    }

    /**
     * GET  /starts/:id : get the "id" start.
     *
     * @param id the id of the start to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the start, or with status 404 (Not Found)
     */
    @GetMapping("/starts/{id}")
    @Timed
    public ResponseEntity<StartDTO> getStart(@PathVariable Long id) {
        log.info("[APN: Start @{}] REST request to get Start : {}", SecurityUtils.getUserId(), id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(startService.findOne(id)));
    }

    /**
     * GET  /starts/talentId/{talentId} : get the "talentId" start.
     *
     * @param talentId the id of the start to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the start, or with status 404 (Not Found)
     */
    @GetMapping("/starts/talentId/{talentId}")
    @Timed
    public ResponseEntity<List<StartDTO>> getStartByTalentId(@PathVariable("talentId") Long talentId) {
        log.info("[APN: Start @{}] REST request to get Start by talent id : {}", SecurityUtils.getUserId(), talentId);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(startService.findByTalent(talentId)));
    }

    /**
     * GET  /starts/talentId/{talentId} : get the "talentId" start.
     *
     * @param talentId the id of the start to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the start, or with status 404 (Not Found)
     */
    @GetMapping("/starts/{talentId}/{talentRecruitmentProcessId}/{status}")
    @Timed
    public ResponseEntity<StartDTO> getStartByTalentIdAndTalentRecruitmentProcessId(@PathVariable("talentId") Long talentId,@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId,@PathVariable("status") StartStatus status) {
        log.info("[APN: Start @{}] REST request to get Start by talent id : {}, talentRecruitmentProcessId :{}", SecurityUtils.getUserId(), talentId, talentRecruitmentProcessId);
        return ResponseEntity.ok(startService.findByTalentIdAndTalentRecruitmentProcessId(talentId,talentRecruitmentProcessId,status));
    }

    @GetMapping("/starts/search/talent_name/{talentName}")
    //@Timed
    //@NoRepeatSubmit
    public ResponseEntity<List<StartSearchSimpleDTO>> getStartsWithFullTimePosition(@PathVariable("talentName") String talentName) {
        log.info("[APN: Start @{}] REST request to search starts by talent name {}", SecurityUtils.getUserId(), talentName);
        return new ResponseEntity<>(startService.searchStarts(talentName), HttpStatus.OK);
    }

    @GetMapping("/starts/talentId/{talentId}/status/{status}")
    @Timed
    public ResponseEntity<StartDTO> findStartByTalentIdAndStatus(@PathVariable("talentId") Long talentId, @PathVariable("status") StartStatus status) {
        log.info("[APN: Start ] REST request to get Start by talent id : {}", talentId);
        return ResponseEntity.ok(startService.findStartByTalentIdAndStatus(talentId, status));
    }

    @GetMapping("/starts/order/{talentId}/{status}")
    @Timed
    public ResponseEntity<StartDTO> findByTalentIdAndStatusOrderByEndDate(@PathVariable("talentId") Long talentId, @PathVariable("status") StartStatus status) {
        log.info("[APN: Start ] REST request to get Start by talent id : {}", talentId);
        return ResponseEntity.ok(startService.findByTalentIdAndStatusOrderByEndDate(talentId, status));
    }

    @PostMapping("/starts/total-bill-amount-by-talentRecruitmentProcess")
    public ResponseEntity<List<MyCandidate>> getTotalBillAmountByTalentRecruitmentProcessId(@RequestBody List<Long> talentRecruitmentProcessIds) {
        log.info("[APN: Start ] REST request to get total bill amount by talentRecruitmentProcessId : {}", talentRecruitmentProcessIds);
        return ResponseEntity.ok(startService.getTotalBillAmountByTalentRecruitmentProcessId(talentRecruitmentProcessIds));
    }

    @PutMapping("/starts/talentRecruitmentProcessId/{talentRecruitmentProcessId}/eliminate")
    @NoRepeatSubmit
    public ResponseEntity<Void> updateStartStatusWithEliminatedCandidate(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId) {
        startService.updateStartStatusWithEliminatedCandidate(talentRecruitmentProcessId);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/starts/talentRecruitmentProcessId/{talentRecruitmentProcessId}/cancel-eliminate")
    @NoRepeatSubmit
    public ResponseEntity<Void> updateStartStatusWithCancelEliminatedCandidate(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId) {
        startService.updateStartStatusWithCancelEliminatedCandidate(talentRecruitmentProcessId);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/starts/filter-talents-with-active-fte-starts")
    public ResponseEntity<Set<Long>> filterTalentsWithActiveFteStarts(@RequestBody Set<Long> talentIds) {
        log.info("[APN: Start ] REST request to get active FTE Starts by talent ids : {}", talentIds);
        return ResponseEntity.ok(startService.filterTalentsWithActiveFteStarts(talentIds));
    }

    @Timed
    @GetMapping("/starts/{talentRecruitmentProcessId}/{status}")
    public ResponseEntity<StartDTO> getStartByTalentRecruitmentProcessIdAndStatus(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId, @PathVariable("status") StartStatus status){
        log.info("[APN: Start @{}] REST request to get Start by talentRecruitmentProcessId : {}, StartStatus :{}", SecurityUtils.getUserId(), talentRecruitmentProcessId, status);
        return ResponseEntity.ok(startService.findByTalentRecruitmentProcessIdAndStatus(talentRecruitmentProcessId, status));
    }

    @DeleteMapping("/starts/job/{jobId}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<JSONArray> deleteStartByJobId(@PathVariable Long jobId) throws IOException {
        log.info("[APN: Start @{}] REST request to delete Start by jobId : {}", SecurityUtils.getUserId(), jobId);
        JSONArray result = startService.deleteStartByJobId(jobId);
        return ResponseEntity.ok().body(result);
    }
}
