package com.altomni.apn.finance.domain.invoicing;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.company.InvoicingPaymentMethodType;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;

/**
 * 开票回款记录记录
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "invoicing_record_payment_info")
public class InvoicingRecordPaymentInfo extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7325232603508110068L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /** 开票id */
    @Column(name = "invoicing_id")
    private Long invoicingId ;

    /** 支付日期 */
    @Column(name = "payment_date")
    private Instant paymentDate ;

    /** 支付金额 */
    @Column(name = "payment_amount")
    private BigDecimal paymentAmount ;

    /** 支付方式 0-汇票 1-储蓄卡 2-信用卡 3-电子汇款 4-预支付金 */
    @Column(name = "payment_method")
    private InvoicingPaymentMethodType paymentMethod ;

    /** 预付金id */
    @Column(name = "prepayment_id")
    private Long prepaymentId ;

    /** 候选人信息 */
    @Column(name = "candidate_json")
    private String candidateJson ;

    /** 备注 */
    @Column(name = "note")
    private String note ;

    @Column(name = "status")
    private Integer status ;
}
