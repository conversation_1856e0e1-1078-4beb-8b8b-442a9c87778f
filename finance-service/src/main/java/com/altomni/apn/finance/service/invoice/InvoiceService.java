package com.altomni.apn.finance.service.invoice;

import cn.hutool.json.JSONArray;
import com.altomni.apn.finance.service.vo.invoice.InvoiceCompanyInfoVO;
import com.altomni.apn.finance.domain.invoice.Invoice;
import com.altomni.apn.finance.domain.invoice.InvoiceActivity;
import com.altomni.apn.finance.service.dto.invoice.InvoiceDTO;
import com.altomni.apn.finance.service.dto.invoice.InvoiceSearchByApplicationDTO;
import com.altomni.apn.finance.service.dto.invoice.InvoiceSearchResult;
import com.altomni.apn.finance.service.dto.invoice.InvoiceVoidDTO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceSearchByApplicationVO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceTalentInfoVO;
import com.querydsl.core.types.dsl.BooleanExpression;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

public interface InvoiceService {

    List<InvoiceDTO> createFteInvoice(InvoiceDTO invoice);

    InvoiceDTO createStartupFeeInvoice(Invoice invoice);

    InvoiceDTO findOne(Long id);

    List<InvoiceDTO> findByInvoiceNo(String invoiceNo);

    void downloadInvoice(HttpServletResponse response, Long id);

    InvoiceActivity voidInvoice(InvoiceVoidDTO invoiceVoidRecord);

//    List<InvoiceActivity> voidInvoiceByNo(InvoiceVoidDTO invoiceVoidRecord);

    Page<Invoice> findAll(BooleanExpression invoice, Pageable pageable);

    InvoiceSearchResult toSearchResult(List<Invoice> invoices);

    Page<Invoice> findMyInvoices(BooleanExpression invoice, Pageable pageable);

    List<InvoiceDTO> findByByStartId(Long startId);

    void scheduleUpdateOverDueStatus();

    List<InvoiceDTO> getStartupFeeInvoicesByCompanyId(Long companyId);

    Page<InvoiceSearchByApplicationVO> searchFtePageByApplication(InvoiceSearchByApplicationDTO invoiceSearchByApplicationDTO);

    List<InvoiceCompanyInfoVO> companyByTenantId(Long tenantId);

    List<InvoiceTalentInfoVO> talentByTenantId(Long tenantId);

    void getDayCurrencyRate();

    void getAllCurrencyRate();


    JSONArray deleteInvoiceByStart(Collection<Long> startIds);

}
