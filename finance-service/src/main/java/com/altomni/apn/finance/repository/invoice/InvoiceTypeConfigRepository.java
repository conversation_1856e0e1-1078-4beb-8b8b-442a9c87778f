package com.altomni.apn.finance.repository.invoice;

import com.altomni.apn.finance.domain.invoice.InvoiceTypeConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the Invoice entity.
 */
@Repository
public interface InvoiceTypeConfigRepository extends JpaRepository<InvoiceTypeConfig, Long>{

    List<InvoiceTypeConfig> findAllByTenantIdAndStatus(Long tenantId,Integer status);

    InvoiceTypeConfig findAllByTenantIdAndStatusAndId(Long tenantId,Integer status,Long id);
}
