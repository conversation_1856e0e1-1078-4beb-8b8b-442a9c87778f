package com.altomni.apn.finance.service.dto.start;

import com.altomni.apn.common.dto.user.FullNameUserDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class StartInfoForInvoicingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long startId;
    private Instant onboardDate;
    private String clientName;
    private Long clientId;
    private String talentName;
    private Long talentId;
    private List<FullNameUserDTO> accountManagers;
}
