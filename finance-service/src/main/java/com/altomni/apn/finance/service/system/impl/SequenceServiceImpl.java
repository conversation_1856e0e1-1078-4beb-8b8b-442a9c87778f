package com.altomni.apn.finance.service.system.impl;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.constants.InvoiceConstants;
import com.altomni.apn.finance.domain.system.Sequence;
import com.altomni.apn.finance.repository.system.SequenceRepository;
import com.altomni.apn.finance.service.system.SequenceService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SequenceServiceImpl implements SequenceService {

    @Resource
    private SequenceRepository sequenceRepository;

    @Override
    public String getInvoiceSequence(String sequenceName) {
        Sequence sequence = getInvoiceSequenceCurrentValue(sequenceName);
        return sequence.getPrefix() + StringUtils.leftPad(String.valueOf(sequence.getCurrentValue()), 6, "0");
    }

    private Sequence getInvoiceSequenceCurrentValue(String sequenceName) {
        Sequence tenantSequence = sequenceRepository.findByTenantIdAndName(SecurityUtils.getTenantId(), sequenceName);
        if (tenantSequence == null) {
            Sequence create = new Sequence();
            create.setTenantId(SecurityUtils.getTenantId());
            create.setMaxValue(InvoiceConstants.MAX_VALUE);
            create.setIncrement(InvoiceConstants.INCREMENT);
            create.setName(sequenceName);
            //根据序列名获取invoice no前缀
            switch (sequenceName) {
                case InvoiceConstants.SEQ_INVOICE_NAME:
                    create.setPrefix(InvoiceConstants.SEQ_INVOICE_PREFIX);
                    break;
                case InvoiceConstants.SEQ_STARTUP_FEE_INVOICE_NAME:
                    create.setPrefix(InvoiceConstants.SEQ_STARTUP_FEE_INVOICE_PREFIX);
                    break;
                case InvoiceConstants.SEQ_INVOICE_NAME_JPY:
                    create.setPrefix(InvoiceConstants.SEQ_INVOICE_PREFIX_JPY);
                    break;
                case InvoiceConstants.SEQ_STARTUP_FEE_INVOICE_NAME_JPY:
                    create.setPrefix(InvoiceConstants.SEQ_STARTUP_FEE_INVOICE_PREFIX_JPY);
                    break;
                default:
                    return null;
            }
            create.setCurrentValue(0);
            return sequenceRepository.save(create);
        } else {
            Integer currentValue = tenantSequence.getCurrentValue();
            Integer increment = tenantSequence.getIncrement();
            Integer maxValue = tenantSequence.getMaxValue();
            if (currentValue >= maxValue) {
                tenantSequence.setCurrentValue(0);
                tenantSequence.setPrefix(getNextPrefix(tenantSequence.getPrefix()));
            } else {
                tenantSequence.setCurrentValue(currentValue + increment);
            }
            return sequenceRepository.save(tenantSequence);
        }
    }

    private String getNextPrefix(String prefix) {
        char result = 0;
        char x = prefix.charAt(0);
        char c;
        for (c = 'A'; c <= 'Z'; ++c) {
            if (c == x) {
                result = ++c;
                break;
            }
        }
        return result + "";
    }

    @Override
    public String getCommonSequence(String sequenceName, Integer len, String prefix, Long maxValue) {
        Sequence sequence = getCommonSequenceCurrentValue(sequenceName, prefix, maxValue);

        return sequence.getPrefix().equals("NO_PREFIX")
                ? StringUtils.leftPad(String.valueOf(sequence.getCurrentValue()), len, "0")
                : sequence.getPrefix() + StringUtils.leftPad(String.valueOf(sequence.getCurrentValue()), len, "0");
    }

    private Sequence getCommonSequenceCurrentValue(String sequenceName, String prefix, Long maxValue) {
        Sequence tenantSequence = sequenceRepository.findByTenantIdAndName(SecurityUtils.getTenantId(), sequenceName);
        if (tenantSequence == null) {
            Sequence create = new Sequence();
            create.setTenantId(SecurityUtils.getTenantId());
            create.setMaxValue(maxValue.intValue());
            create.setIncrement(InvoiceConstants.INCREMENT);
            create.setName(sequenceName);
            create.setPrefix(prefix);
            create.setCurrentValue(1);
            return sequenceRepository.saveAndFlush(create);
        } else {
            Integer currentValue = tenantSequence.getCurrentValue();
            Integer increment = tenantSequence.getIncrement();
            Integer sequenceMaxValue = tenantSequence.getMaxValue();
            if (currentValue >= sequenceMaxValue) {
                tenantSequence.setCurrentValue(0);
                tenantSequence.setPrefix(getNextPrefix(tenantSequence.getPrefix()));
            } else {
                tenantSequence.setCurrentValue(currentValue + increment);
            }
            return sequenceRepository.saveAndFlush(tenantSequence);
        }
    }

}
