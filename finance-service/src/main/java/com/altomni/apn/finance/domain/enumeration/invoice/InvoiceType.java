package com.altomni.apn.finance.domain.enumeration.invoice;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The InvoiceType enumeration.
 */
public enum InvoiceType implements ConvertedEnum<Integer> {
    FTE(0),
    STARTUP_FEE(1);


    private final Integer dbValue;

    InvoiceType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<InvoiceType, Integer> resolver =
        new ReverseEnumResolver<>(InvoiceType.class, InvoiceType::toDbValue);

    public static InvoiceType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
