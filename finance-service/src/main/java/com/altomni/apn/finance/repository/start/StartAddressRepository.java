package com.altomni.apn.finance.repository.start;

import com.altomni.apn.finance.domain.start.StartAddress;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * Spring Data  repository for the StartAddress entity.
 */
@SuppressWarnings("unused")
@Repository
public interface StartAddressRepository extends JpaRepository<StartAddress, Long> {

    Optional<StartAddress> findByStartId(Long startId);

    @Query(value = " SELECT CONCAT('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.job_id = ?1 and loc.original_loc is not null group by loc.job_id ", nativeQuery = true)
    String findAddressesFromJob(@Param("jobId") Long jobId);


    @Query(value = " SELECT CONCAT( '[', GROUP_CONCAT(distinct CONCAT( '\"', loc.official_country, '\"' )), ']' ) AS locations FROM job_location loc WHERE loc.job_id = ?1  AND loc.original_loc IS NOT NULL GROUP BY loc.job_id;", nativeQuery = true)
    String findCountryFromJob(@Param("jobId") Long jobId);

    List<StartAddress> findAllByStartIdIn(List<Long> startIds);

}
