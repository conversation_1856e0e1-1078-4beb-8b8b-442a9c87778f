<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263620669-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="currency_rate_day"/>
            </not>
        </preConditions>
        <createTable tableName="currency_rate_day">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="currency_id" type="BIGINT"/>
            <column name="rate_day" type="DATE"/>
            <column name="from_usd_rate_high" type="FLOAT(12)"/>
            <column name="from_usd_rate_mid" type="FLOAT(12)"/>
            <column name="from_usd_rate_low" type="FLOAT(12)"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
        <createIndex indexName="idx_crd_currency_id_rate_day" tableName="currency_rate_day">
            <column name="currency_id"/>
            <column name="rate_day"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
