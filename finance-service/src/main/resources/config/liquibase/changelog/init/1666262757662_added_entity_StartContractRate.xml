<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264278105-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="start_contract_rate"/>
            </not>
        </preConditions>
        <createTable tableName="start_contract_rate">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="start_id" type="BIGINT"/>
            <column name="extend_start_contract_rate_id" type="BIGINT"/>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="currency" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="rate_unit_type" type="INT"/>
            <column name="final_bill_rate" type="DECIMAL(10, 2)"/>
            <column name="final_pay_rate" type="DECIMAL(10, 2)"/>
            <column name="tax_burden_rate" type="VARCHAR(255)"/>
            <column name="msp_rate" type="VARCHAR(255)"/>
            <column name="immigration_cost" type="VARCHAR(255)"/>
            <column name="extra_cost" type="DECIMAL(10, 2)"/>
            <column name="estimated_working_hour_per_week" type="DECIMAL(10, 2)"/>
            <column name="total_bill_amount" type="DECIMAL(20, 2)"/>
            <column name="note" type="VARCHAR(355)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_start_contract_rate_start_id" tableName="start_contract_rate">
            <column name="start_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
