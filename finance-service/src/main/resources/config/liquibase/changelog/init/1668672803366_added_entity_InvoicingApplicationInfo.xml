<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263620367-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="invoicing_application_info"/>
            </not>
        </preConditions>
        <createTable tableName="invoicing_application_info">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="invoicing_application_type" type="TINYINT"/>
            <column name="prepayment_type" type="TINYINT"/>
            <column name="void_invoicing" type="TINYINT"/>
            <column name="invalid_invoicing_id" type="BIGINT"/>
            <column name="tenant_id" type="BIGINT"/>
            <column name="company_id" type="BIGINT"/>
            <column name="invoicing_reason" type="VARCHAR(2048)"/>
            <column name="code_number" type="VARCHAR(255)"/>
            <column name="client_invoicing_id" type="BIGINT"/>
            <column name="invoicing_body" type="TINYINT"/>
            <column name="invoicing_type" type="TINYINT"/>
            <column name="invoicing_business_type" type="TINYINT"/>
            <column name="invoicing_server_tax_id" type="BIGINT"/>
            <column name="invoicing_tax" type="DOUBLE"/>
            <column name="invoice_format" type="TINYINT"/>
            <column name="confirmation_letter_url" type="VARCHAR(6000)"/>
            <column name="invoicing_amount" type="DECIMAL(10,2)"/>
            <column name="amount_due" type="DECIMAL(10,2)"/>
            <column name="invoice_tax" type="DECIMAL(10,2)"/>
            <column name="taxes_not_included" type="DECIMAL(10,2)"/>
            <column name="gp_amount" type="DECIMAL(10,2)"/>
            <column name="uninvoiced_amount" type="DECIMAL(10,2)"/>
            <column name="invoicing_status" type="TINYINT"/>
            <column name="electronic_invoice_number" type="VARCHAR(64)"/>
            <column name="invoicing_date" type="DATETIME"/>
            <column name="due_within_days" type="INT"/>
            <column name="payment_due_date" type="DATETIME"/>
            <column name="note" type="VARCHAR(1024)"/>
            <column name="status" type="TINYINT"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
