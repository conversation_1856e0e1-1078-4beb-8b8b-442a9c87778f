<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263610876-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="invoice"/>
            </not>
        </preConditions>
        <createTable tableName="invoice">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT"/>
            <column name="invoice_no" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="sub_invoice_no" type="VARCHAR(50)"/>
            <column name="split" type="INT"/>
            <column name="invoice_type" type="INT"/>
            <column name="status" type="INT"/>
            <column name="start_id" type="BIGINT"/>
            <column name="start_date" type="timestamp"/>
            <column name="job_id" type="BIGINT"/>
            <column name="job_title" type="VARCHAR(255)"/>
            <column name="talent_id" type="BIGINT"/>
            <column name="talent_name" type="VARCHAR(255)"/>
            <column name="po_no" type="VARCHAR(100)"/>
            <column name="customer_reference" type="VARCHAR(100)"/>
            <column name="company_id" type="BIGINT"/>
            <column name="customer_name" type="VARCHAR(255)"/>
            <column name="customer_address" type="VARCHAR(255)"/>
            <column name="client_contact_id" type="BIGINT"/>
            <column name="team_id" type="BIGINT"/>
            <column name="total_billable_package" type="DECIMAL(10, 2)"/>
            <column name="total_bill_amount" type="DECIMAL(10, 2)"/>
            <column name="discount" type="DECIMAL(10, 2)"/>
            <column name="total_invoice_amount" type="DECIMAL(10, 2)"/>
            <column name="tax_rate" type="DECIMAL(10, 4)"/>
            <column name="tax_amount" type="DECIMAL(10, 2)"/>
            <column name="due_amount" type="DECIMAL(10, 2)"/>
            <column name="receiving_account_id" type="BIGINT"/>
            <column name="invoice_date" type="timestamp"/>
            <column name="due_date" type="timestamp"/>
            <column name="currency" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="note" type="VARCHAR(5000)"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_invoice_job_id" tableName="invoice">
            <column name="job_id"/>
        </createIndex>
        <createIndex indexName="idx_invoice_start_id" tableName="invoice">
            <column name="start_id"/>
        </createIndex>
        <createIndex indexName="idx_invoice_talent_id" tableName="invoice">
            <column name="talent_id"/>
        </createIndex>
        <createIndex indexName="idx_invoice_tenant_id" tableName="invoice">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
