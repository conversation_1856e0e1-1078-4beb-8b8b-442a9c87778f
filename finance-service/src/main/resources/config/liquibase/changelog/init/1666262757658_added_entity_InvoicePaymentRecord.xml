<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263643350-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="invoice_payment_record"/>
            </not>
        </preConditions>
        <createTable tableName="invoice_payment_record">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="invoice_id" type="BIGINT"/>
            <column name="payment_date" type="timestamp"/>
            <column name="paid_amount" type="DECIMAL(10, 2)"/>
            <column name="close_without_full_payment" type="INT"/>
            <column name="payment_method" type="INT"/>
            <column name="paid_startup_fee" type="INT"/>
            <column name="startup_fee_invoice_no" type="VARCHAR(50)"/>
            <column name="startup_fee_date" type="timestamp"/>
            <column name="startup_fee_amount" type="DECIMAL(10, 2)"/>
            <column name="apply_credit" type="DECIMAL(10, 2)"/>
            <column name="currency" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="exchange_rate" type="VARCHAR(20)"/>
            <column name="note" type="VARCHAR(5000)"/>
            <column defaultValueNumeric="1" name="activated" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_invoice_payment_record_invoice_id" tableName="invoice_payment_record">
            <column name="invoice_id"/>
        </createIndex>
        <createIndex indexName="idx_ipr_invoice_id_payment_date" tableName="invoice_payment_record">
            <column name="invoice_id"/>
            <column name="payment_date"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
