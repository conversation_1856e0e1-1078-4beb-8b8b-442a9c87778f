<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264298850-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="start_fte_rate"/>
            </not>
        </preConditions>
        <createTable tableName="start_fte_rate">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="start_id" type="BIGINT"/>
            <column name="client_info_id" type="BIGINT"/>
            <column name="currency" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="rate_unit_type" type="INT"/>
            <column name="salary" type="DECIMAL(10, 2)"/>
            <column name="sign_on_bonus" type="DECIMAL(10, 2)"/>
            <column name="retention_bonus" type="DECIMAL(10, 2)"/>
            <column name="annual_bonus" type="DECIMAL(10, 2)"/>
            <column name="relocation_package" type="DECIMAL(10, 2)"/>
            <column name="extra_fee" type="DECIMAL(10, 2)"/>
            <column name="total_billable_amount" type="DECIMAL(20, 2)"/>
            <column name="fee_type" type="INT"/>
            <column name="fee_percentage" type="DECIMAL(10, 2)"/>
            <column name="total_bill_amount" type="DECIMAL(10, 2)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_start_fte_rate_start_id" tableName="start_fte_rate">
            <column name="start_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
