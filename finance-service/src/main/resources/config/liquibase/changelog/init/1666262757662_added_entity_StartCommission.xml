<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264267794-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="start_commission"/>
            </not>
        </preConditions>
        <createTable tableName="start_commission">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT"/>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="user_full_name" type="VARCHAR(150)"/>
            <column name="user_role" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="percentage" type="DECIMAL(10, 4)"/>
            <column name="start_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="period_start_date" type="timestamp"/>
            <column name="period_end_date" type="timestamp"/>
            <column name="country" type="BIGINT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_start_commission_start_id" tableName="start_commission">
            <column name="start_id"/>
        </createIndex>
        <createIndex indexName="idx_start_commission_tenant_id" tableName="start_commission">
            <column name="tenant_id"/>
        </createIndex>
        <createIndex indexName="idx_start_commission_user_id" tableName="start_commission">
            <column name="user_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
