<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263620972-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="invoice_activity"/>
            </not>
        </preConditions>
        <createTable tableName="invoice_activity">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="invoice_id" type="BIGINT"/>
            <column name="invoice_no" type="VARCHAR(50)"/>
            <column name="invoice_activity_type" type="INT"/>
            <column name="status" type="INT"/>
            <column name="user_id" type="BIGINT"/>
            <column name="user_full_name" type="VARCHAR(255)"/>
            <column name="amount" type="DECIMAL(10, 2)"/>
            <column name="note" type="VARCHAR(1000)"/>
            <column name="payment_id" type="BIGINT"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
