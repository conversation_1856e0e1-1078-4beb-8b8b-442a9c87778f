<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264246792-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="start_address"/>
            </not>
        </preConditions>
        <createTable tableName="start_address">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="start_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="address_line" type="VARCHAR(255)" />
            <column name="address" type="VARCHAR(255)"/>
            <column name="address_2" type="VARCHAR(255)"/>
            <column name="city" type="VARCHAR(255)"/>
            <column name="city_id" type="BIGINT"/>
            <column name="province" type="VARCHAR(255)"/>
            <column name="country" type="VARCHAR(255)"/>
            <column name="zipcode" type="VARCHAR(255)"/>
            <column name="location" type="VARCHAR(300)"/>
            <column name="residential_location" type="BIGINT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
