<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1668672710519-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <viewExists viewName="view_invoice_list"/>
            </not>
        </preConditions>
        <createView viewName="view_invoice_list">
            select `i`.`id` AS `id`,`i`.`tenant_id` AS `tenant_id`,`i`.`invoice_no` AS `invoice_no`,`i`.`sub_invoice_no`
            AS `sub_invoice_no`,`i`.`invoice_date` AS `invoice_date`,`i`.`due_date` AS `due_date`,`i`.`invoice_type` AS
            `invoice_type`,`i`.`currency` AS `currency`,`i`.`status` AS `status`,`i`.`due_amount` AS
            `due_amount`,(select sum(`p`.`paid_amount`) from `invoice_payment_record` `p` where (`i`.`id` =
            `p`.`invoice_id`)) AS `received_amount`,(`i`.`due_amount` - (select sum(`p`.`paid_amount`) from
            `invoice_payment_record` `p` where (`i`.`id` = `p`.`invoice_id`))) AS `balance`,(select
            max(`p`.`payment_date`) from `invoice_payment_record` `p` where (`i`.`id` = `p`.`invoice_id`)) AS
            `payment_date`,`i`.`company_id` AS `company_id`,`i`.`customer_name` AS `customer_name`,`i`.`job_id` AS
            `job_id`,`i`.`job_title` AS `job_title`,`i`.`talent_id` AS `talent_id`,`i`.`talent_name` AS
            `talent_name`,`i`.`team_id` AS `team_id`,`i`.`created_date` AS `created_date` from `invoice` `i`
        </createView>
    </changeSet>
</databaseChangeLog>
