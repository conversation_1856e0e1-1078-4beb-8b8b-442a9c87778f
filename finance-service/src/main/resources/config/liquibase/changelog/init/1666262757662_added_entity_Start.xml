<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264235290-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="start"/>
            </not>
        </preConditions>
        <createTable tableName="start">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column defaultValueNumeric="0" name="tenant_id" type="BIGINT"/>
            <column name="talent_id" type="BIGINT"/>
            <column name="job_id" type="BIGINT"/>
            <column name="company_id" type="BIGINT"/>
            <column name="talent_recruitment_process_id" type="BIGINT"/>
            <column name="client_contact_id" type="BIGINT"/>
            <column name="start_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="warranty_end_date" type="date"/>
            <column name="position_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="time_zone" type="VARCHAR(255)"/>
            <column name="note" type="MEDIUMTEXT"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column defaultValueNumeric="0" name="start_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="channel_platform" type="INT"/>
            <column name="profit_sharing_ratio" type="DECIMAL(10, 2)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="currency" type="INT"/>
            <column name="talent_name" type="VARCHAR(255)"/>
            <column name="job_title" type="VARCHAR(255)"/>
            <column name="company" type="VARCHAR(255)"/>
            <column name="working_mode" type="INT" />
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_start_tenant_id" tableName="start">
            <column defaultValueNumeric="0" name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
