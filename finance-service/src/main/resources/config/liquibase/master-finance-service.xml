<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <property name="now" value="now()" dbms="h2"/>
    <property name="now" value="now()" dbms="mysql, mariadb"/>
    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb"/>
    <property name="clobType" value="clob" dbms="h2"/>
    <property name="clobType" value="clob" dbms="mysql, oracle, mssql, mariadb, postgresql"/>
    <property name="uuidType" value="varchar(36)" dbms="h2, mysql, mariadb"/>
    <property name="datetimeType" value="datetime(6)" dbms="mysql, mariadb"/>
    <property name="datetimeType" value="datetime" dbms="oracle, mssql, postgresql, h2"/>

    <include file="config/liquibase/changelog/init/1666262757658_added_entity_Invoice.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_InvoiceActivity.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_InvoiceClientCredit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_InvoicePaymentRecord.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_Sequence.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_Start.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_StartAddress.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_StartClientInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_StartCommission.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_StartContractRate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_StartFailedWarranty.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_StartFteRate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_StartFteSalaryPackage.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757662_added_entity_StartTermination.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1668672803663_added_entity_ViewInvoiceList.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1668672803669_added_entity_CurrencyRateDay.xml" relativeToChangelogFile="false"/>

</databaseChangeLog>
