package com.altomni.apn.common.repository.upgrade;

import com.altomni.apn.common.domain.upgrade.UpgradeNotification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface UpgradeNotificationRepository extends JpaRepository<UpgradeNotification, Long> {

    @Query(value = """
                    SELECT * from upgrade_notification where `global` = true
                    union all  
                    SELECT un.* from upgrade_notification un  
                    INNER JOIN upgrade_tenant_relation utr on un.id = utr.upgrade_id and utr.tenant_id = ?1  
                    ORDER BY upgrade_time DESC limit 1
            """, nativeQuery = true)
    UpgradeNotification findUpgradeNotificationByTenantId(Long tenantId);
}
