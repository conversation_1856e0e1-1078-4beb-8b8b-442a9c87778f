package com.altomni.apn.common.service.email.dto.emailVM;


import com.altomni.apn.common.service.email.dto.EmailBlastAttachmentDTO;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

public class EmailVM {

    private Long id; // for direct sending draft campaign from save-draft api without go through submit-to-approve and approve-by-admin precess

    // campaign basic info
    private String name;
    private String subject;
    private String from;
    private String replyTo;
    private String displayName;
    private String htmlContent;
    private Long industryId;

    // audience info
    private AudienceVM audienceInfo = new AudienceVM();

    // campaign schedule info
    private ScheduleVM scheduleInfo = new ScheduleVM();

    // for apnpublic use
    private Long templateId;

    // deprecated
    private Instant schedule;

    // will be removed
    private List<EmailBlastAttachmentDTO> attachments = new ArrayList<>();
    private String content;
//    private List<Recipient> recipients;

    public EmailVM() {}

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getName() { return name; }

    public void setName(String name) { this.name = name; }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getFrom() { return from; }

    public void setFrom(String from) { this.from = from; }

    public String getReplyTo() { return replyTo; }

    public void setReplyTo(String replyTo) { this.replyTo = replyTo; }

    public String getDisplayName() { return displayName; }

    public void setDisplayName(String displayName) { this.displayName = displayName; }

    public String getHtmlContent() { return htmlContent; }

    public void setHtmlContent(String htmlContent) { this.htmlContent = htmlContent; }

    public Long getIndustryId() { return industryId; }

    public void setIndustryId(Long industryId) { this.industryId = industryId; }

    public AudienceVM getAudienceInfo() { return audienceInfo; }

    public void setAudienceInfo(AudienceVM audienceInfo) { this.audienceInfo = audienceInfo; }

    public ScheduleVM getScheduleInfo() { return scheduleInfo;}

    public void setScheduleInfo(ScheduleVM scheduleInfo) { this.scheduleInfo = scheduleInfo; }

    public Long getTemplateId() { return templateId; }

    public void setTemplateId(Long templateId) { this.templateId = templateId; }

//    // deprecated
//    public List<Recipient> getRecipients() {
//        return recipients;
//    }
//
//    public void setRecipients(List<Recipient> recipients) {
//        this.recipients = recipients;
//    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public List<EmailBlastAttachmentDTO> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<EmailBlastAttachmentDTO> attachments) { this.attachments = attachments; }

    public Instant getSchedule() { return schedule; }

    public void schedule(Instant schedule) { this.schedule = schedule; }

    public void setSchedule(Instant schedule) { this.schedule = schedule; }


    @Override
    public String toString() {
        return "EmailVM{" +
            "name='" + name + '\'' +
            ", subject='" + subject + '\'' +
            ", from='" + from + '\'' +
            ", replyTo='" + replyTo + '\'' +
            ", displayName='" + displayName + '\'' +
            ", industryId=" + industryId +
            ", audienceInfo=" + audienceInfo +
            ", scheduleInfo=" + scheduleInfo +
            ", templateId=" + templateId +
            ", htmlContent='" + htmlContent + '\'' +
            '}';
    }
}
