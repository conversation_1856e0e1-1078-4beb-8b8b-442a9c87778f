package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.domain.calendar.CalendarEvent;
import com.altomni.apn.common.domain.calendar.CalendarEventAttendee;
import com.altomni.apn.common.domain.calendar.CalendarEventRelationInfo;
import lombok.Data;

import java.util.List;

@Data
public class CalendarEventData {
    private CalendarEvent calendarEvent;
    private List<CalendarEventAttendee> calendarEventAttendeeList;
    private List<CalendarEventRelationInfo> calendarEventRelationInfoList;
}
