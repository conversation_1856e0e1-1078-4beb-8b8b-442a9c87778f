package com.altomni.apn.common.utils;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.message.Message;
import lombok.experimental.UtilityClass;

import java.util.Map;

@UtilityClass
public class MessageUtil {

    public JSONObject setContextJsonWithMap(XxlJobRelationTypeEnum xxlJobRelationTypeEnum, Map<String, String> map, Integer version) {
        JSONObject contentJson = new JSONObject();
        //消息类型
        contentJson.put("type", xxlJobRelationTypeEnum.getMessageType().name());
        //通知类型
        contentJson.put("notifyType", xxlJobRelationTypeEnum.name());
        //版本号
        contentJson.put("version", version);
        contentJson.put("param", map);
        return contentJson;
    }

    /**
     * 设置内容全部是由用户填写的, 直接使用 text
     * @param xxlJobRelationTypeEnum
     * @param message
     * @param version
     * @return
     */
    public JSONObject setContextJsonWithPrimitiveMessage(XxlJobRelationTypeEnum xxlJobRelationTypeEnum, Message message, Integer version) {
        JSONObject contentJson = new JSONObject();
        //消息类型
        contentJson.put("type", xxlJobRelationTypeEnum.getMessageType().name());
        //通知类型
        contentJson.put("notifyType", xxlJobRelationTypeEnum.name());
        //版本号
        contentJson.put("version", version);
        contentJson.put("title", message.getCnTitle());
        contentJson.put("text", message.getContent());
        return contentJson;
    }

}
