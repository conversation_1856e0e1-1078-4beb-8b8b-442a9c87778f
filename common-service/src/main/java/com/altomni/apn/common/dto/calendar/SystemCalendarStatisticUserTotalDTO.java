package com.altomni.apn.common.dto.calendar;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SystemCalendarStatisticUserTotalDTO {
    private Integer notSubmitToClientUserTotal = 0;
    private Integer submitToClientNotUpdateStatusUserTotal = 0;
    private Integer offerPassNotUpdateStatusUserTotal = 0;
    private Integer paymentOverdueUserTotal = 0;

    private Integer notSubmitToClientDataTotal = 0;
    private Integer submitToClientNotUpdateStatusDataTotal = 0;
    private Integer offerPassNotUpdateStatusDataTotal = 0;
    private Integer paymentOverdueDataTotal = 0;

    private Integer followUpRecordUpdateUserTotal = 0;
    private Integer expectedOrderExpirationUserTotal = 0;
    private Integer contractNearingExpirationUserTotal = 0;
    private Integer contractExpiredUserTotal = 0;
    private Integer contactJobChangeUserTotal = 0;

    private Integer followUpRecordUpdateDataTotal = 0;
    private Integer expectedOrderExpirationDataTotal = 0;
    private Integer contractNearingExpirationDataTotal = 0;
    private Integer contractExpiredDataTotal = 0;
    private Integer contactJobChangeDataTotal = 0;

    private Integer noSubmitTalentUserTotal = 0;
    private Integer noInterviewUserTotal = 0;

    private Integer noSubmitTalentDataTotal = 0;
    private Integer noInterviewDataTotal = 0;

    public void selfAddNotSubmitToClient() {
        ++notSubmitToClientUserTotal;
    }
    public void selfAddSubmitToClientNotUpdateStatus() {
        ++submitToClientNotUpdateStatusUserTotal;
    }
    public void selfAddOfferPassNotUpdateStatus() {
        ++offerPassNotUpdateStatusUserTotal;
    }
    public void selfAddPaymentOverdue() {
        ++paymentOverdueUserTotal;
    }

    public void selfAddFollowUpRecordUpdate() {
        ++followUpRecordUpdateUserTotal;
    }
    public void selfAddExpectedOrderExpiration() {
        ++expectedOrderExpirationUserTotal;
    }
    public void selfAddContractNearingExpiration() {
        ++contractNearingExpirationUserTotal;
    }
    public void selfAddContractExpired() {
        ++contractExpiredUserTotal;
    }
    public void selfAddContactJobChange() {
        ++contactJobChangeUserTotal;
    }
    public void selfAddNoSubmitTalentUser() {
        ++noSubmitTalentUserTotal;
    }
    public void selfAddNoInterviewUser() {
        ++noInterviewUserTotal;
    }

    public void addNotSubmitToClientData(Integer count) {
        notSubmitToClientDataTotal += count;
    }

    public void addSubmitToClientNotUpdateStatusData(Integer count) {
        submitToClientNotUpdateStatusDataTotal += count;
    }

    public void addOfferPassNotUpdateStatusData(Integer count) {
        offerPassNotUpdateStatusDataTotal += count;
    }

    public void addPaymentOverdueData(Integer count) {
        paymentOverdueDataTotal += count;
    }

    public void addFollowUpRecordUpdateData(Integer count) {
        followUpRecordUpdateDataTotal += count;
    }
    public void addExpectedOrderExpirationData(Integer count) {
        expectedOrderExpirationDataTotal += count;
    }
    public void addContractNearingExpirationData(Integer count) {
        contractNearingExpirationDataTotal += count;
    }
    public void addContractExpiredData(Integer count) {
        contractExpiredDataTotal += count;
    }
    public void addContactJobChangeData(Integer count) {
        contactJobChangeDataTotal += count;
    }

    public void addNoSubmitTalentData(Integer count) {
        noSubmitTalentDataTotal += count;
    }

    public void addNoInterviewData(Integer count) {
        noInterviewDataTotal += count;
    }
}
