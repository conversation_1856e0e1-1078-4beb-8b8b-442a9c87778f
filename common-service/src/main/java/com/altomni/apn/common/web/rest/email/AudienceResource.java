package com.altomni.apn.common.web.rest.email;

import com.altomni.apn.common.domain.email.enumeration.ApplicationSource;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.email.AudienceService;
import com.altomni.apn.common.service.email.dto.MongoAudience;
import com.altomni.apn.common.service.email.dto.NameAndDescription;
import com.altomni.apn.common.utils.HeaderConvertUtil;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-EmailService"})
@RestController
@RequestMapping("/api/v3/audience")
public class AudienceResource {

    private final Logger log = LoggerFactory.getLogger(AudienceResource.class);

    private static final String ENTITY_NAME = "audienceService";

    @Resource
    private AudienceService audienceService;

    @PostMapping(value = "", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> createAudienceGroup(@Valid @RequestBody NameAndDescription nameAndDescription) throws IOException {
        log.info("[AudienceResource: createAudienceGroup @{}] REST request to create AudienceGroup : {}", SecurityUtils.getUserId(), nameAndDescription);

        HttpResponse response = audienceService.createMongoAudienceGroup(nameAndDescription);
//        MailingListDTO result = mailingListService.create(mailingListDTO);
//        return ResponseEntity.created(new URI("/api/email-lists/" + result.getId()))
////            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, result.getId().toString()))
//                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
//                .body(result);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    /**
     * {@code PUT  /mailing-list/:id} : Updates an existing emailList.
     *
     * @param nameAndDescription the emailListDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated emailListDTO,
     * or with status {@code 400 (Bad Request)} if the emailListDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the emailListDTO couldn't be updated.
     * @throws IOException if the Location URI syntax is incorrect.
     */
    @PutMapping(value = "/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> updateAudienceGroupInfo(@PathVariable Long id, @Valid @RequestBody NameAndDescription nameAndDescription) throws IOException {
        log.info("[AudienceResource: updateAudienceGroupInfo @{}] REST request to update AudienceGroup : {}", SecurityUtils.getUserId(), nameAndDescription);

        HttpResponse response = audienceService.updateMongoAudienceGroup(id, nameAndDescription);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    /**
     * {@code GET  /mailing-list} : get all the emailLists.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of emailLists in body.
     */
    @GetMapping(value = "", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getAllAudienceGroups(@RequestParam(value = "name", required = false) String name, Pageable pageable) throws IOException {
        log.info("[AudienceResource: getAllAudienceGroups @{}] REST request to get all AudienceGroups (unarchived) for current user", SecurityUtils.getUserId());
        HttpResponse response = audienceService.searchActiveMongoAudienceGroupList(name, pageable);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/unarchived", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getAllUnarchivedAudienceGroups(@RequestParam(value = "name", required = false) String name, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[AudienceResource: getAllUnarchivedAudienceGroups @{}] REST request to get all unarchived AudienceGroup for current user", SecurityUtils.getUserId());
        HttpResponse response = audienceService.searchActiveMongoAudienceGroupList(name, pageable);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/archived", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getAllArchivedAudienceGroups(@RequestParam(value = "name", required = false) String name, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[AudienceResource: getAllArchivedAudienceGroups @{}] REST request to get all archived audience groups for current user, with name like: {}", SecurityUtils.getUserId(), name);
        HttpResponse response = audienceService.searchArchivedMongoAudienceGroupList(name, pageable);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    /**
     * {@code PUT  /:id/archive} : archive the "id" emailList.
     *
     * @param id the id of the emailListDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @PutMapping(value = "/{id}/archive", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> archiveAudienceGroupById(@PathVariable Long id) throws IOException {
        log.info("[AudienceResource: archiveAudienceGroupById @{}] REST request to archive audience group by id: {}", SecurityUtils.getUserId(), id);
        HttpResponse response = audienceService.archiveAudienceGroupById(id);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    /**
     * {@code PUT  /:id/archive} : archive the "id" emailList.
     *
     * @param id the id of the emailListDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @PutMapping(value = "/{id}/unarchive", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> unarchiveAudienceGroupById(@PathVariable Long id) throws IOException {
        log.info("[AudienceResource: unarchiveAudienceGroupById @{}] REST request to unarchive audience group by id: {}", SecurityUtils.getUserId(), id);
        HttpResponse response = audienceService.unarchiveAudienceGroupById(id);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

//    /**
//     * {@code PUT  /mailing-list/:id/archive} : change the "id" mailingList status.
//     *
//     * @param id the id of the emailListDTO to change status.
//     * @return the {@link ResponseEntity} with status {@code 200 (OK)}.
//     */
//    @PutMapping("/mailing-list/{id}/change-status")
//    public ResponseEntity<MailingListDTO> changeMailingListStatus(@PathVariable Long id, @RequestBody @Valid StatusVM statusVM) {
//        log.info("[APN: MailingList @{}] REST request to change status of MailingList : {} to : {}", SecurityUtils.getUserId(), id, statusVM);
//        MailingListDTO mailingListDTO = mailingListService.changeStatus(id, statusVM);
//        return ResponseEntity.ok(mailingListDTO);
//    }

    /**
     * {@code POST  /mailing-list/:id} : Create a new emailListDetails.
     *
     * @param id the emailList id to add
     * @param audienceList the emailListDetailsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new emailListDetailsDTO, or with status {@code 400 (Bad Request)} if the emailListDetails has already an ID.
     * @throws IOException if the Location URI syntax is incorrect.
     */
    @PostMapping(value = "/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> addRecipientsToAudienceGroup(@PathVariable Long id, @Valid @RequestBody List<MongoAudience> audienceList) throws IOException {
        log.info("[AudienceResource: addRecipientsToAudienceGroup @{}] REST request to add recipient to audience group id: {}, recipient: {}", SecurityUtils.getUserId(), id, audienceList);

        audienceList = audienceList.stream().peek(a -> a.setDataSource(ApplicationSource.MANUALLY_ADDED)).collect(Collectors.toList());
        HttpResponse response = audienceService.addAudienceToGroup(id, audienceList, 10000);
//        List<MailingListContentDTO> result = mailingListService.addRecipientsToMailingList(id, mailingListDetailsDTOList);
//        return ResponseEntity.created(new URI("/api/v1/email-list/" + id + "/add"))
////            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, result.getId().toString()))
//                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, id.toString()))
//                .body(result);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    /**
     * {@code POST  /mailing-list/:id} : Create a new emailListDetails.
     *
     * @param id the emailList id to add
     * @param talentIds the emailListDetailsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new emailListDetailsDTO, or with status {@code 400 (Bad Request)} if the emailListDetails has already an ID.
     * @throws IOException if the Location URI syntax is incorrect.
     */
    @PostMapping(value = "/{id}/talent", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> addTalentsToAudienceGroup(@PathVariable Long id, @Valid @RequestBody List<Long> talentIds) throws IOException {
        log.info("[AudienceResource: addTalentsToAudienceGroup @{}] REST request to save TalentIds : {} into AudienceGroupId : {}", SecurityUtils.getUserId(), talentIds, id);
        if (CollectionUtils.isEmpty(talentIds)){
            throw new CustomParameterizedException("Talent list cannot be empty!");
        }

//        List<MailingListContentDTO> result = mailingListService.addTalentsToMailingList(id, talentIds);
        HttpResponse response = audienceService.addTalentsToGroup(id, talentIds);

//        return ResponseEntity.created(new URI("/api/v1/mailing-list/" + id + "/talent"))
//                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, id.toString()))
//                .body(result);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/{id}/contact", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> addClientContactsToAudienceGroup(@PathVariable Long id, @Valid @RequestBody List<Long> contactIds) throws IOException {
        log.info("[AudienceResource: addClientContactsToAudienceGroup @{}] REST request to save client contacts : {} into AudienceGroupId : {}", SecurityUtils.getUserId(), contactIds, id);
        if (CollectionUtils.isEmpty(contactIds)){
            throw new CustomParameterizedException("Contact list cannot be empty!");
        }
//        List<MailingListContentDTO> result = mailingListService.addContactToMailingList(id, contactIds);
//
//        return ResponseEntity.created(new URI("/api/v1/mailing-list/" + id + "/contact"))
//                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, id.toString()))
//                .body(result);

        HttpResponse response = audienceService.addClientContactsToGroup(id, contactIds);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/{id}/user", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> addUsersToAudienceGroup(@PathVariable Long id, @Valid @RequestBody List<Long> userIds) throws IOException {
        log.info("[AudienceResource: addUsersToAudienceGroup @{}] REST request to save UserIds : {} into AudienceGroupId : {}", SecurityUtils.getUserId(), userIds, id);
        if (CollectionUtils.isEmpty(userIds)){
            throw new CustomParameterizedException("User list cannot be empty!");
        }
        HttpResponse response = audienceService.addUsersToGroup(id, userIds);
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }


    /**
     * {@code GET  /:id} : get the "id" emailListDetails.
     *
     * @param id the id of the emailListDetailsDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the emailListDetailsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping(value = "/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getAudienceFromGroup(@PathVariable Long id, @RequestParam(value = "dataSource", required = false) String dataSource, @RequestParam(defaultValue = "") String name, @RequestParam(defaultValue = "") String email, Pageable pageable) throws IOException {
        log.info("[AudienceResource: getAudienceFromGroup @{}] REST request to get audience from audience group id: {}, with search params[dataSource:{}, name: {}, email: {}", SecurityUtils.getUserId(), id, dataSource, name, email);
        HttpResponse response = audienceService.getPageableAudienceByGroupIdAndSource(id, name, email, dataSource, pageable);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    /**
     * {@code DELETE  /:groupId/:audienceId} : delete the "audienceId" emailListDetails.
     *
     * @param audienceId the id of the emailListDetailsDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping(value = "/{groupId}/{audienceId}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> deleteAudience(@PathVariable Long groupId, @PathVariable String audienceId) throws IOException {
        log.info("[AudienceResource: deleteAudience @{}] REST request to delete audienceId: {} from audience group id: {}", SecurityUtils.getUserId(), audienceId, groupId);
        audienceService.deleteAudienceByIdAndAudienceIdList(groupId, List.of(audienceId));
//        mailingListService.deleteRecipient(audienceId);
        return ResponseEntity.noContent()
//            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
                .headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, audienceId.toString()))
                .build();
    }

    @PostMapping(value = "/{groupId}/delete", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> deleteAudience(@PathVariable Long groupId, @RequestBody List<String> audienceIds) throws IOException {
        log.info("[AudienceResource: deleteAudience @{}] REST request to delete from audience group id: {} with audienceIds: {}", SecurityUtils.getUserId(), groupId, audienceIds);
        audienceService.deleteAudienceByIdAndAudienceIdList(groupId, audienceIds);
//        mailingListService.deleteRecipient(audienceId);
        return ResponseEntity.noContent()
//            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
                .headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, audienceIds.toString()))
                .build();
    }

}
