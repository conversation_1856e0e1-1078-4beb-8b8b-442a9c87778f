package com.altomni.apn.common.web.rest.location;

import com.altomni.apn.common.service.location.GeoInfoService;
import com.altomni.apn.common.service.location.dto.CityProvinceSearchDTO;
import com.altomni.apn.common.service.location.dto.GeoInfoVO;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-GeoInfo"})
@RestController
@RequestMapping("/api/v3")
public class GeoInfoResource {

    private final Logger log = LoggerFactory.getLogger(GeoInfoResource.class);

    private static final String ENTITY_NAME = "geoInfo";

    @Resource
    private GeoInfoService geoInfoService;


    /**
     * GET  /geo-infos : get all the geoInfos by list of Country code.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of geoInfos in body
     */
    @GetMapping("/geoinfo/all")
    public List<GeoInfoVO> getAllGeoInfos(@ApiParam(value = "countryCode params. e.g. countryCode=US,CN") @RequestParam(value = "countryCode") List<String> countryCode) {
        log.info("[APN: GeoInfo @{}] REST request to get all GeoInfos in countryCode : {}", SecurityUtils.getUserId(), countryCode.toString());
        return geoInfoService.findAllByCountryCode(countryCode);
    }

    /**
     * GET  /geo-infos : search all the [city, state] by start characters of city name.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of string of [city, state] in body
     */
    @GetMapping("/geoinfo/en/searchCityState")
    public List<CityProvinceSearchDTO> searchGeoInfoByCity(@ApiParam(value = "city params. e.g. city=San") @RequestParam(value = "city") String city,
                                                           @ApiParam(value = "countryCode params. e.g. countryCode=US") @RequestParam(value = "countryCode") String countryCode) {
        log.info("[APN: GeoInfo @{}] REST request to search all [city, state] by city : {}, countryCode : {}", SecurityUtils.getUserId(), city, countryCode);
        return geoInfoService.searchByCity(city, countryCode);
    }

    @GetMapping("/geoinfo/search")
    public List<GeoInfoVO> searchGeoInfo(@RequestParam(value = "condition" ) String condition
            , @RequestParam(required = false) Integer items
            , @RequestParam(required = false) String  countryCode) {
        log.info("[APN: GeoInfo @{}] REST request to search all : {}", SecurityUtils.getUserId(), condition);
        return geoInfoService.searchGeoInfo(condition, items, countryCode);
    }

    @GetMapping("/geo-geoinfo/en/city-or-state")
    public List<GeoInfoVO> searchGeoInfoByCityOrStateOrCountry(@ApiParam(value = "city or state params. e.g. city/state=San") @RequestParam(value = "search") String search) {
        log.info("[APN: GeoInfo @{}] REST request to search all [city, state] by city or state : {}", SecurityUtils.getUserId(), search);
        return geoInfoService.searchByCityOrState(search);
    }

}
