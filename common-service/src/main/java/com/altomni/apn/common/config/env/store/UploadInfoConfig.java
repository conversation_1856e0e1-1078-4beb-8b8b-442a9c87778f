package com.altomni.apn.common.config.env.store;

import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.store.dto.UploadInfo;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Configuration
public class UploadInfoConfig {

    @Resource
    private UploadProperties uploadProperties;

    public static Map<String, String> bucketRegionMap = new HashMap<>();

    public static Map<String, Boolean> bucketIsPublicMap = new HashMap<>();

    /**
     * info_docs folder
     */
    public UploadInfo getUploadInfoByUploadType(String uploadType) {
        Map<String, UploadInfo> uploadInfoMap = uploadProperties.getStoreService();
        Set<String> keys = uploadInfoMap.keySet();
        if (!keys.contains(uploadType)) {
            throw new CustomParameterizedException("The upload type is invalid");
        }
        return uploadInfoMap.get(uploadType);
    }

}
