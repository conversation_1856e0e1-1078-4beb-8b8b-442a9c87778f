package com.altomni.apn.common.service.company;

import com.altomni.apn.common.dto.company.SalesLeadClientContactDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * Service Interface for managing {@link SalesLeadClientContactDTO}.
 */
@Component
@FeignClient(value = "company-service")
public interface CompanyService {

    @PostMapping("/company/api/v3/saleslead/client-contact/brief-list")
    ResponseEntity<List<SalesLeadClientContactDTO>> findByClientContactIdIn(@RequestBody List<Long> contactIds); //TODO

}
