package com.altomni.apn.common.repository.email;

import com.altomni.apn.common.domain.email.UserGmailAliasBinding;
import com.altomni.apn.common.domain.email.enumeration.GmailAliasBindingStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;


/**
 * Spring Data repository for the UserGmailAliasBinding entity.
 */
@Repository
public interface UserGmailAliasRepository extends JpaRepository<UserGmailAliasBinding, Long> {

    Optional<UserGmailAliasBinding> findByUserId(Long userId);

    List<UserGmailAliasBinding> findAllByUserId(Long userId);

    @Modifying
    @Transactional
    void deleteAllByUserId(Long userId);

    List<UserGmailAliasBinding> findAllByBindingStatusAndLastModifiedDateAfter(GmailAliasBindingStatus bindingStatus, Instant lastModifiedDate);

    @Query(value = "select email, alternate_email from user where alternate_email is not null", nativeQuery = true)
    List<Object[]> getAlternateEmail();

    @Modifying
    @Transactional
    @Query(value = "update user u set u.alternate_email = ?2 where u.id = ?1", nativeQuery = true)
    void setAlternateEmailByUserId(Long userId, String alternateEmail);
}
