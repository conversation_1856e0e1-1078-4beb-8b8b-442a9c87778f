package com.altomni.apn.common.service.xxljob;

import com.altomni.apn.common.dto.xxljob.*;

import java.io.IOException;

public interface XxlJobHandlerService {
    Long findDefaultConfig(String fieldName);
    void calendarEventMessageHandler(XxlJobCalendarEventMessageHandlerDTO xxlJobCalendarEventMessageHandlerDTO);

    void reminderForJobHandler(XxlJobReminderForJobHandlerDTO xxlJobReminderForJobHandlerDTO);

    void adminMessageHandler(XxlJobAdminMessageHandlerDTO xxlJobAdminMessageHandlerDTO);

    void reminderForApplicationHandler(XxlJobReminderForApplicationDTO xxlJobReminderForApplicationDTO);

    void reminderForInvoiceOverDueHandler(XxlJobReminderForInvoiceOverDueDTO xxlJobReminderForInvoiceOverDueDTO);

    void reminderForUnSubmitCandidatesForTeamHandler(XxlJobReminderForUnSubmitCandidatesForTeamHandlerDTO dto);

    void createNoOnboardForTeam(XxlJobBaseDTO xxlJobBaseDTO);

    void createOrUpdateUnSubmittedCandidatesForTeam(Long tenantId);

    void initCreateOrUpdateUnSubmittedCandidatesForTeam();

    void applicationNoUpdateReminder(XxlJobReminderForApplicationNodeTypeDTO xxlJobBaseDTO);

    void applicationStopReminder();

    void jobStagnationReminder();

    void regularlyCleanUpMessages();

    void sendAPNAnnouncement(XxlJobApnAnnouncementDTO xxlJobApnAnnouncementDTO) throws InterruptedException, IOException;

    void talentAutoDeclassify(XxlJobBaseDTO xxlJobBaseDTO);

    void systemCalendarJobFollow();

    void systemCalendarTalentApplicationFollow();
}
