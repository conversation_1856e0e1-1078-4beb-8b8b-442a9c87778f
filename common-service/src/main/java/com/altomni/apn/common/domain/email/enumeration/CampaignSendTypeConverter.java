package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class CampaignSendTypeConverter extends AbstractAttributeConverter<CampaignSendType, Integer> {
    public CampaignSendTypeConverter() {
        super(CampaignSendType::toDbValue, CampaignSendType::fromDbValue);
    }
}
