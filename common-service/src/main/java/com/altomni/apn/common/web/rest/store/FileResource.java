package com.altomni.apn.common.web.rest.store;

import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.TextUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.exception.TikaException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.xml.sax.SAXException;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * REST controller for managing file.
 */
@Api(tags = {"File"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class FileResource {

//    @Resource
//    private StoreService storeService;

    @ApiOperation(value = "parse image")
    @PostMapping("/files/text-extract")
    public ResponseEntity<Map<String, String>> extractText(@RequestParam("file") MultipartFile file) throws TikaException, IOException, SAXException {
        if (file == null || file.isEmpty()) {
            throw new CustomParameterizedException("File cannot be empty");
        }

        String text = TextUtil.extractSimpleText(file);
        String retText = text.trim();
        return ResponseEntity.ok(new HashMap<String, String>(){{put("text", retText);}});
    }

//    @ApiOperation(value = "upload an email attachment")
//    @PostMapping("/files/email-attachment")
//    public ResponseEntity<Map<String, String>> uploadEmailAttachment(@RequestParam("file") MultipartFile file, @RequestParam("key") String key) throws IOException {
//        if (file == null || file.isEmpty()) {
//            throw new CustomParameterizedException("File cannot be empty");
//        }
//
//        // upload to aws StoreService
//        String s3url = storeService.emailS3Upload(file, key);
//        return ResponseEntity.ok(new HashMap<String, String>(){{put("s3url", s3url);}});
//    }

//    @ApiOperation(value = "get a url to upload an email attachment")
//    @GetMapping("/files/email-attachment/upload-url")
//    public ResponseEntity<String> getPresignedEmailAttachmentUploadUrlFromS3() {
//        String key = UUID.randomUUID().toString();
//        log.info("[StoreService: getPresignedEmailAttachmentUploadUrlFromS3@{}] REST request to get presigned email attachment upload url for key: {}", SecurityUtils.getUserId(), key);
//        String uploadUrl = storeService.getPresignedCommonUploadUrlFromS3WithPostPolicy(key);
//        return ResponseEntity.ok(uploadUrl);
//    }
}
