package com.altomni.apn.common.service.parser.impl;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.config.env.store.UploadInfoConfig;
import com.altomni.apn.common.domain.parser.enumeration.ParseType;
import com.altomni.apn.common.domain.talent.Resume;
import com.altomni.apn.common.dto.parser.TenantWatermarkDTO;
import com.altomni.apn.common.dto.redis.ImagesInfoDTO;
import com.altomni.apn.common.dto.redis.ImagesInfoParser;
import com.altomni.apn.common.dto.redis.ParserRedisResponse;
import com.altomni.apn.common.dto.redis.ParserResponse;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ErrorConstants;
import com.altomni.apn.common.errors.ExternalInterfaceException;
import com.altomni.apn.common.repository.talent.ResumeRepository;
import com.altomni.apn.common.service.cache.redis.RedisService;
import com.altomni.apn.common.service.job.JobService;
import com.altomni.apn.common.service.parser.ParserService;
import com.altomni.apn.common.service.store.StoreService;
import com.altomni.apn.common.service.talent.TalentService;
import com.altomni.apn.common.service.user.UserService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.AmazonSQSClientBuilder;
import com.amazonaws.services.sqs.model.SendMessageRequest;
import com.amazonaws.services.sqs.model.SendMessageResult;
import com.google.common.reflect.TypeToken;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.jsoup.Jsoup;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.altomni.apn.common.config.constants.Constants.*;

/**
 * <AUTHOR>
 */
@Service
public class ParserServiceImpl implements ParserService {

    private final Logger log = LoggerFactory.getLogger(ParserServiceImpl.class);

    @Autowired
    private ResumeRepository resumeRepository;

    @Resource
    private RedisService redisService;

    @Resource
    private JobService jobService;

    @Resource
    private StoreService storeService;

    @Resource
    private TalentService talentService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

    @Value("${application.aws.accessKey}")
    private String accessKey;

    @Value("${application.aws.secretKey}")
    private String secretKey;

    @Value("${application.sqsService.region}")
    private String sqsServiceRegion;

    @Value("${application.sqsService.queueName}")
    private String sqsQueueName;

    @Value("${application.filesize.resume.max}")
    private Long resumeFileMaxSize;

    @Value("${application.filesize.resume.min}")
    private Long resumeFileMinSize;

    @Value("${application.filesize.jd.max}")
    private Long jdFileMaxSize;

    @Value("${application.filesize.jd.min}")
    private Long jdFileMinSize;

    @Value("${application.char.jd.max}")
    private Long jdTextMaxChar; // plain jd-text max characters


    @Resource
    private UploadInfoConfig uploadInfoConfig;

    @Resource
    private UserService userService;

    private static final int REDIS_EXPIRE_TIME = 600; //10min

    @Override
    public ParserResponse downloadResume(HttpServletResponse response, String uuid) {
        TalentResumeDTO talentResumeDTO = talentService.findByUuidAndTenantId(uuid, SecurityUtils.getTenantId()).getBody();
        if (talentResumeDTO == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_DOWNLOADRESUMEPARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
        String fileName = talentResumeDTO.getFileName();
        String displayLink = null;
        try {
//            displayLink = storeClient.getUrlFromS3(uuid, UploadTypeEnum.RESUME.getKey()).getBody();
            displayLink = storeService.getCommonUrlFromS3(uuid, UploadTypeEnum.RESUME.getKey(), null);
        } catch (Exception e) {
            log.info("info: get presigned url from amazon s3 for talent resume pfd S3Link, uuid: {}", uuid);
        }
        ParserResponse parserResponse = new ParserResponse();
        parserResponse.setFileName(fileName);
        parserResponse.setDisplayLink(displayLink);
        return parserResponse;
    }

    @Override
    public ParserResponse uploadResumeOnly(String uuid, String fileName, String contentType) {
        //1. check if talent resume table has this resume
        TalentResumeDTO talentResumeDTO = talentService.findByUuidAndTenantId(uuid, SecurityUtils.getTenantId()).getBody();

        ParserResponse parserResponse = new ParserResponse();
        parserResponse.setFileName(fileName);
        parserResponse.setUuid(uuid);
        if (ObjectUtil.isNotNull(talentResumeDTO)) { // if talent resume has same resume, return
            parserResponse.setStatus(ParseStatus.EDIT);
            parserResponse.setTalentId(talentResumeDTO.getTalentId());
            parserResponse.setFileName(talentResumeDTO.getFileName());
        } else { // if talent resume table doesn't have this resume, generate pre-signed upload link

            CloudFileObjectMetadata metadata = getObjectMetadata(ParseType.RESUME, uuid);
            if (metadata == null) { //this resume doesn't exist in s3
                //generate pre-signed upload link
//                Map<String, String> postPolicy = storeClient.getPresignedUploadUrlFromS3WithPostPolicy(ParseType.RESUME, uuid, fileName, contentType, resumeFileMinSize, resumeFileMaxSize).getBody();
                initializeRedisForResumeParser(uuid, fileName, contentType, 1); // initialize redis for resume parser
                Map<String, String> postPolicy = storeService.getPresignedUploadUrlFromS3WithPostPolicy(ParseType.RESUME, uuid, fileName, contentType, resumeFileMinSize, resumeFileMaxSize);
                parserResponse.setPostPolicy(postPolicy);
                parserResponse.setStatus(ParseStatus.NONE);
            } else {// s3 already has this resume file
                resetContentType(parserResponse, contentType, uuid);
            }
        }
        return parserResponse;
    }

    private void resetContentType(ParserResponse parserResponse, String contentType, String uuid){
        parserResponse.setStatus(ParseStatus.FINISHED);
        if (RESUME_CONTENT_TYPE_NEED_TO_RESET.contains(contentType)) {
            setDisplayuploadResumeOnlyInfos(uuid, parserResponse, null, true);
        }else{
            setDisplayuploadResumeOnlyInfos(uuid, parserResponse, contentType, false);
        }
    }

    private void setDisplayuploadResumeOnlyInfos(String uuid, ParserResponse parserResponse, String contentType, Boolean needToReset) {
        try {
            //if RESUME_CONTENT_TYPE_NEED_TO_RESET, reset contentType CONTENT_TYPE_MSWORD
            if (needToReset) {
                parserResponse.setContentType(CONTENT_TYPE_MSWORD);
//                parserResponse.setDisplayLink(storeClient.getUrlFromS3WithContentType(uuid, UploadTypeEnum.RESUME.getKey(), parserResponse.getContentType()).getBody());
                parserResponse.setDisplayLink(storeService.getCommonUrlFromS3(uuid, UploadTypeEnum.RESUME.getKey(), parserResponse.getContentType()));
            } else {
                parserResponse.setContentType(contentType);
//                parserResponse.setDisplayLink(storeClient.getUrlFromS3(uuid, UploadTypeEnum.RESUME.getKey()).getBody());
                parserResponse.setDisplayLink(storeService.getCommonUrlFromS3(uuid, UploadTypeEnum.RESUME.getKey(), null));
            }
        } catch (Exception e) {
            log.info("info: get presigned url from amazon s3 for talent resume S3Link, uuid: {}", uuid);
        }
    }

    @Override
    public ParserResponse getParserResumeResult(String uuid) {
        return getResumeParserResult(uuid);
    }

    @Override
    public ParserResponse getParserResumeInfo(String uuid) {
        return getParserInfo(uuid, ParseType.RESUME);
    }

    @Override
    public ParserResponse getParserJdResult(String uuid) {
        ParserResponse parserResponse = new ParserResponse();
        //2.get parse result from redis
        parserResponse.setUuid(uuid);
        ParserRedisResponse response = redisService.getParserJDData(uuid);
        //if has parser result in redis, format data and return parser status
        if (response.getStatus() != null) {
            parserResponse.setStatus(response.getStatus());
            if (ParseStatus.FINISHED.equals(response.getStatus()) || ParseStatus.TIMEOUT.equals(response.getStatus())) { // if parser status is TIMEOUT, it may have some incomplete result
                String data = null;
                if (StringUtils.isNotBlank(response.getData())) { //if parser status is TIMEOUT, result may miss key 'data' or data is blank
                    data = formatJD(response.getData());
                }
                String s3Link = storeService.getCommonUrlFromS3(uuid, UploadTypeEnum.JD.getKey(), null);
                parserResponse.setS3Link(s3Link);
                parserResponse.setData(data);
            } else {
                //if parser response status is not FINISHED, throw 401 Gone
                log.error("[ParserServiceImpl: uuid: {}, getParserResult] Parse status is not finished, current status is {} with ttl {}s", uuid, response.getStatus(), response.getTtl());
                throw new CustomParameterizedException(Constants.ERROR_CODE_401, Constants.ERROR_STATUS_CODE_401, ErrorConstants.ERR_HAS_NO_ACCESS_PERMISSION, null);
            }
        } else {
            //if parser response is null, throw 401 Gone
            log.error("[ParserServiceImpl: uuid: {}, getParserResult] Parse status is null", uuid);
            throw new CustomParameterizedException(Constants.ERROR_CODE_401, Constants.ERROR_STATUS_CODE_401, ErrorConstants.ERR_HAS_NO_ACCESS_PERMISSION, null);
        }
        return parserResponse;
    }

    private String formatJD(String data) {
        //TODO 暂时jd无过滤需求
        return data;
    }

    @Override
    public void sendSqsMessage(String key, CloudFileObjectMetadata metadata) {
        String body = generateSqsRequestJsonStr(key, metadata);
        // send message to SQS to trigger the parser
        sendMessageToSQS(key, body);
    }

    @Nullable
    private ParserResponse hasParseResult(String uuid, ParseType type) {
        //if has talent resume relation, format data and return
        if (ParseType.RESUME.equals(type)) {
            TalentResumeDTO talentResumeDTO = talentService.findByUuidAndTenantId(uuid, SecurityUtils.getTenantId()).getBody();
            if (talentResumeDTO != null) {
                ParserResponse responseResume = new ParserResponse();
                responseResume.setStatus(ParseStatus.EDIT);
                responseResume.setUuid(uuid);
                responseResume.setFileName(talentResumeDTO.getFileName());
                responseResume.setPortraitLink(talentResumeDTO.getImagesInfo().getPortraitLink());
                responseResume.setDisplayLink(talentResumeDTO.getImagesInfo().getDisplayLink());
                responseResume.setTalentId(talentResumeDTO.getTalentId());
                responseResume.setTalentName(talentResumeDTO.getTalentName());
                responseResume.setCompanyName(talentResumeDTO.getCompanyName());
                responseResume.setTitle(talentResumeDTO.getTitle());
                return responseResume;
            }
        }
        return null;
    }

    @Override
    public ParserResponse checkParseJdStatusOrGetUploadUrl(String uuid, String fileName, String contentType, String jdText) {
        ParseType parseType = ParseType.JD;
        if (StringUtils.isNotBlank(jdText)) {
            if (StringUtils.isNotBlank(fileName) || StringUtils.isNotBlank(contentType)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_CHECKJDFILENAMENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
            }
            if(!Jsoup.parse(jdText).wholeText().equals(jdText)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_CHECKJDCANNOTHTML.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
            }
            if (StringUtils.length(jdText) > jdTextMaxChar) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_CHECKJDTEXTLONG.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
            }
            parseType = ParseType.JDTEXT;
        } else if (StringUtils.isBlank(fileName) || StringUtils.isBlank(contentType)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_CHECKJDFILENAMENOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
        return this.checkParseStatusOrGetUploadUrl(uuid, fileName, jdText, contentType, null, parseType);
    }

    @Override
    public ParserResponse checkParseResumeStatusOrGetUploadUrl(String uuid, String fileName, String contentType, int priority) {
        return this.checkParseStatusOrGetUploadUrl(uuid, fileName, null, contentType, priority, ParseType.RESUME);
    }

    @Override
    public ParserResponse getParseResultStatus(String uuid, ParseType parseType) {
        ParserRedisResponse redis = ParseType.RESUME.equals(parseType) ? redisService.getParserResumeStatus(uuid) : redisService.getParserJDStatus(uuid);
        if (redis == null) { // if redis has parser result, format data and return parser status
            log.info("[ParserServiceImpl: uuid: {}, getParseResultStatus] Redis doesn't have any related key", uuid);
            throw new CustomParameterizedException(Constants.ERROR_CODE_401, Constants.ERROR_STATUS_CODE_401, ErrorConstants.ERR_HAS_NO_ACCESS_PERMISSION, null);
        }
        log.info("[ParserServiceImpl: uuid: {}, getParseResultStatus] Redis parser status is: {}", uuid, redis.getStatus());
        ParserResponse responseResume = new ParserResponse();
        responseResume.setUuid(uuid);
        responseResume.setStatus(redis.getStatus());
        return responseResume;
    }

    private ParserResponse checkParseStatusOrGetUploadUrl(String uuid, String fileName, String text, String contentType, Integer priority, ParseType parseType) {
        //1.check parse result from talent resume / jd
        ParserResponse responseResume = hasParseResult(uuid, parseType);
        if (responseResume != null) { // if resume already bound to a talent, find and return parsed result
            return responseResume;
        } else {
            responseResume = new ParserResponse();
        }
        //2.check parse result from redis
        responseResume.setUuid(uuid);
        ParserRedisResponse response = ParseType.RESUME.equals(parseType) ? redisService.getParserResumeData(uuid) : redisService.getParserJDData(uuid);
//        ParserRedisResponse response = redisService.getParserResumeData(uuid);
        if (response.getStatus() != null && !ParseStatus.UPLOADING.equals(response.getStatus())) { // if redis has parser result, format data and return parser status
            responseResume.setStatus(response.getStatus());
            log.info("[ParserServiceImpl: uuid: {}, checkParseStatusOrGetUploadUrl] Redis already has parse result with status: {}, ttl:{} and data: {}", uuid, response.getStatus(), response.getTtl(), response.getData());
        } else { // if not, check if file(resume/jd) exist in aws s3
            responseResume.setFileName(fileName);
            // if file(resume/jd) already exists in aws s3 (based on MD5), skip upload process and send a message to SQS to trigger the parser;
            // if not, return pre-signed upload url to front-end for direct uploading
            parseOrGetUploadLink(uuid, text, contentType, priority, responseResume, parseType);
        }
        return responseResume;
    }

    private ParserResponse getResumeParserResult(String uuid) {
        ParserResponse parserResponse = new ParserResponse();
        //2.get parse result from redis
        parserResponse.setUuid(uuid);
        ParserRedisResponse response = getResumeParseResponse(uuid);
        //if has parser result in redis, format data and return parser status
        if (response.getData() != null) {
            parserResponse.setStatus(response.getStatus());
            if (ParseStatus.FINISHED.equals(response.getStatus()) || ParseStatus.TIMEOUT.equals(response.getStatus())) { // if parser status is TIMEOUT, it may have some incomplete result
                String data = null;
                if (StringUtils.isNotBlank(response.getData())) {
                    data = formatResume(response.getData());
                }
                parserResponse.setData(data);
            }
        } else {
            initializeRedisForResumeParser(uuid, parserResponse.getFileName(), null, 1); // initialize redis for resume parser
            CloudFileObjectMetadata metadata = getObjectMetadata(ParseType.RESUME, uuid);
            if (metadata == null) { //this resume /jd doesn't exist in s3
                parserResponse.setStatus(ParseStatus.NONE);
            } else {// s3 already has this resume/jd file
                String body = generateSqsRequestJsonStr(uuid, metadata);
                sendMessageToSQS(uuid, body); // send message to SQS to trigger the parser
                parserResponse.setStatus(ParseStatus.UPLOADING);
            }
        }
        return parserResponse;
    }

    private String formatResume(String data) {
        ResponseEntity<String> response = userService.getTalentFormConfig();
        if(response.getStatusCode() != HttpStatus.OK) {
            throw new ExternalInterfaceException("/user/api/v3/talents/config/talent-form Interface exception", 500);
        }
        return filterByFormConfig(data, response.getBody());
    }

    private String filterByFormConfig(String data, String body) {
        if(StringUtils.isEmpty(data)) {
            return data;
        }
        cn.hutool.json.JSONObject jsonObject = JSONUtil.parseObj(body);
        String config = jsonObject.getStr("customConfig");
        Map<String, Object> dataMap = new Gson().fromJson(data, new TypeToken<HashMap<String, Object>>() {}.getType());
        List<Map<String, Object>> formConfigMap = new Gson().fromJson(config, new TypeToken<List<HashMap<String, Object>>>() {}.getType());
        Map<String, Object> specifiedInfo = getSpecifiedInfo(dataMap, formConfigMap);
        return JSONUtil.toJsonStr(specifiedInfo);
    }

    public Map<String, Object> getSpecifiedInfo(Map<String, Object> fullProfile, List<Map<String, Object>> requirements) {
        if (requirements == null || requirements.isEmpty()) {
            return fullProfile;
        }
        Map<String, Object> info = new HashMap<>();
        for (Map<String, Object> req : requirements) {
            Object oVisible = req.get("visible");
            if(oVisible == null) {
                continue;
            }
            if (!(Boolean)oVisible) {
                continue;
            }
            Object oSubFields = req.get("subFields");
            List<String> subFields = oSubFields == null ? new ArrayList<>() : (List<String>) oSubFields;
            Object oAdditionalInfoKeys = req.get("additionalInfoKeys");
            List<String> additionalInfoKeys = oAdditionalInfoKeys == null ? new ArrayList<>() : (List<String>) oAdditionalInfoKeys;
            for (String key : additionalInfoKeys) {
                if (info.containsKey(key)) {
                    continue;
                }
                Object deeperFullProfile = fullProfile.get(key);
                if (deeperFullProfile == null) {
                    continue;
                }
                Object value;
                if (deeperFullProfile instanceof List) {
                    value = new ArrayList<>();
                    for (Object element : (List<Object>) deeperFullProfile) {
                        if(element instanceof Number) {
                            ((List<Long>) value).add(NumberUtil.parseLong(element.toString()));
                        } else if(element instanceof String) {
                            ((List<String>) value).add(String.valueOf(element));
                        } else if(element instanceof Map) {
                            Map<String, Object> subFullProfile = new Gson().fromJson(JSONUtil.toJsonStr(element), new TypeToken<HashMap<String, Object>>() {}.getType());
                            List<Map<String, Object>> subRequirements = new Gson().fromJson(JSONUtil.toJsonStr(subFields), new TypeToken<List<HashMap<String, Object>>>() {}.getType());
                            Map<String, Object> ele = getSpecifiedInfo(subFullProfile, subRequirements);
                            if (ele != null && !ele.isEmpty()) {
                                ((List<Object>) value).add(ele);
                            }
                        }
                    }
                } else if (deeperFullProfile instanceof Map) {
                    List<Map<String, Object>> subRequirements = new Gson().fromJson(JSONUtil.toJsonStr(subFields), new TypeToken<List<HashMap<String, Object>>>() {}.getType());
                    value = getSpecifiedInfo((Map<String, Object>) deeperFullProfile, subRequirements);
                } else {
                    value = deeperFullProfile;
                }
                if (value != null || value.equals(0)) {
                    info.put(key, value);
                }
            }
        }
        return info;
    }


    private ParserRedisResponse getResumeParseResponse(String uuid) {
        ParserRedisResponse parserResumeData = redisService.getParserResumeData(uuid);
        if(parserResumeData.getData() == null) {
            Resume resume = resumeRepository.findByUuid(uuid);
            if(resume != null) {
                String parseResult = resume.getParseResult();
                if(StringUtils.isNotEmpty(parseResult)) {
                    parserResumeData.setData(parseResult);
                    parserResumeData.setStatus(ParseStatus.FINISHED);
                }
            }
        }
        return parserResumeData;
    }

    private ParserResponse getParserInfo(String uuid, ParseType type) {
        //1.check talent resume relation
        ParserResponse responseResume = hasParseResult(uuid, type);
        if (responseResume != null) {
            return responseResume;
        } else {
            responseResume = new ParserResponse();
        }
        //2.get parse result from redis
        responseResume.setUuid(uuid);
        ParserRedisResponse response = type == ParseType.RESUME ? redisService.getParserResumeData(uuid) : redisService.getParserJDData(uuid);
        //if has parser result in redis, return parser imagesInfo
        if (response.getStatus() != null && type == ParseType.RESUME) {
            ImagesInfoParser imagesInfoParser = response.getImagesInfo();
            if (imagesInfoParser != null) {
                //2.5 download and format images s3Links
                responseResume.setUploadStatus(imagesInfoParser.getUpload_status());
                ImagesInfoDTO imagesInfoDTO = formatImagesInfo(uuid, imagesInfoParser);
                ServiceUtils.myCopyProperties(imagesInfoDTO, responseResume);
            }
        }
        return responseResume;
    }

    private void parseOrGetUploadLink(String uuid, String text, String contentType, Integer priority, ParserResponse parserResponse, ParseType parseType) {

        //2.send sqs message or return policy
        if (ParseType.JDTEXT.equals(parseType)) {
            //1. init redis key
            initializeRedisForJdParser(uuid, parserResponse.getFileName(), contentType); // initialize redis for jd parser, including ParseType.JD and ParseType.JDTEXT

            String body = generateSqsRequestJsonStr(uuid, text);
            sendMessageToSQS(uuid, body);
            parserResponse.setStatus(ParseStatus.STARTED);
        } else {
            CloudFileObjectMetadata metadata = getObjectMetadata(parseType, uuid);
            if (metadata != null) {
                contentType = metadata.getContentType();
            }

            //1. init redis key
            if (ParseType.RESUME.equals(parseType)) {
                initializeRedisForResumeParser(uuid, parserResponse.getFileName(), contentType, priority); // initialize redis for resume parser
            } else {
                initializeRedisForJdParser(uuid, parserResponse.getFileName(), contentType); // initialize redis for jd parser, including ParseType.JD and ParseType.JDTEXT
            }

            if (metadata == null) { //this resume /jd doesn't exist in s3
                //generate pre-signed upload link
//            String uploadUrl = storeService.getPresignedUploadUrlFromS3(uuid).getBody();
//            responseResume.setS3Link(uploadUrl);
                Long fileMaxSize = resumeFileMaxSize;
                Long fileMinSize = resumeFileMinSize;
                if (ParseType.JD.equals(parseType)) {
                    fileMaxSize = jdFileMaxSize;
                    fileMinSize = jdFileMinSize;
                }
//                Map<String, String> postPolicy = storeClient.getPresignedUploadUrlFromS3WithPostPolicy(parseType, uuid,parserResponse.getFileName(), contentType, fileMinSize, fileMaxSize).getBody();
                Map<String, String> postPolicy = storeService.getPresignedUploadUrlFromS3WithPostPolicy(parseType, uuid,parserResponse.getFileName(), contentType, fileMinSize, fileMaxSize);
                parserResponse.setPostPolicy(postPolicy);
                parserResponse.setStatus(ParseStatus.NONE);
            } else {// s3 already has this resume/jd file
                String body = generateSqsRequestJsonStr(uuid, metadata);
                sendMessageToSQS(uuid, body); // send message to SQS to trigger the parser
                parserResponse.setStatus(ParseStatus.UPLOADING);
            }
        }

    }

    private void sendMessageToSQS(String uuid, String body) {
        AmazonSQS sqs = AmazonSQSClientBuilder.standard()
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(accessKey, secretKey)))
                .withRegion(sqsServiceRegion)
                .build();

        String queueUrl = sqs.getQueueUrl(sqsQueueName).getQueueUrl();

        SendMessageRequest request = new SendMessageRequest()
                .withQueueUrl(queueUrl)
                .withMessageBody(body);
        SendMessageResult result = sqs.sendMessage(request);
        log.info("[ParserServiceImpl: uuid: {}, sendMessageToSQS] send message to SQS, return result: {}", uuid, result);
    }

    private String generateSqsRequestJsonStr(String uuid, CloudFileObjectMetadata metadata) {
        JSONObject object = new JSONObject();
        object.fluentPut("key", uuid)
                .fluentPut("contentType", metadata.getContentType())
                .fluentPut("size", metadata.getContentLength());

        JSONObject bucket = new JSONObject();
        bucket.fluentPut("name", metadata.getBucketName());

        JSONObject s3 = new JSONObject();
        s3.fluentPut("bucket", bucket).fluentPut("object", object);

        JSONObject record = new JSONObject();
        record.fluentPut("awsRegion", sqsServiceRegion);
        record.fluentPut("s3", s3);

        JSONArray recordArray = new JSONArray();
        recordArray.add(record);

        JSONObject res = new JSONObject();
        res.fluentPut("Records", recordArray);

        return res.toJSONString();
    }

    private String generateSqsRequestJsonStr(String uuid, String text) {
        JSONObject jd = new JSONObject();
        jd.fluentPut("uuid", uuid).fluentPut("text", text);

        JSONObject record = new JSONObject();
        record.fluentPut("jd", jd);

        JSONArray recordArray = new JSONArray();
        recordArray.add(record);

        JSONObject res = new JSONObject();
        res.fluentPut("Records", recordArray);

        return res.toJSONString();
    }


    @Override
    public void initializeRedisForResumeParser(String uuid, String filename, String contentType, int priority) {
        String key = "parser:resume:" + uuid + ":metadata";
        String statusKey = "parser:resume:" + uuid + ":status";
        Boolean exist = redisService.exists(key);
        if (BooleanUtils.isTrue(exist)) {
            log.info("[ParserServiceImpl: uuid: {}, initializeRedisForResumeParser] redis already exists key: {} when init for fileName: {} with priority: {}", uuid, key, filename, priority);
            return;
        }

        Map<String, String> set = new HashMap<>();
        set.put("priority", String.valueOf(priority));
        set.put("requester", "apn");
        if(StringUtils.isEmpty(filename)) {
            TalentResumeDTO talentResumeDTO = talentService.findByUuidAndTenantId(uuid, SecurityUtils.getTenantId()).getBody();
            if (talentResumeDTO != null && StringUtils.isNotEmpty(talentResumeDTO.getFileName())) {
                set.put("filename", talentResumeDTO.getFileName());
            }
        } else {
            set.put("filename", filename);
        }
        if(contentType != null) {
            set.put("ContentType", contentType);
        }
        TenantWatermarkDTO tenantWatermarkDTO = userService.getTenantWatermarkConfig(SecurityUtils.getTenantId()).getBody();
        if(tenantWatermarkDTO != null && tenantWatermarkDTO.getActive()) {
            set.put("scattered_word_encryption_text", tenantWatermarkDTO.getTenantName());
            String bucket = uploadInfoConfig.getUploadInfoByUploadType(UploadTypeEnum.RESUME_WATERMARK.getKey()).getBucket();
            set.put("watermark_bucket_name", bucket);
            set.put("watermark_obj_key", tenantWatermarkDTO.getObjKey());
            set.put("display_output_folder_name", String.valueOf(tenantWatermarkDTO.getTenantId()));
        }

        redisService.hset(key, set, REDIS_EXPIRE_TIME);
        redisService.set(statusKey, ParseStatus.UPLOADING.name(), REDIS_EXPIRE_TIME);
        log.info("[ParserServiceImpl: uuid: {}, initializeRedisForResumeParser] Write metadata(filename: {}, contentType: {}, priority: {}) into redis with key: {} and ttl: {}", uuid, filename, contentType, priority, key, REDIS_EXPIRE_TIME);
    }

    private void initializeRedisForJdParser(String uuid, String filename, String contentType) {
        String key = "parser:jd:" + uuid + ":metadata";
        Map<String, String> set = new HashMap<>();
        set.put("requester", "apn");
        if (StringUtils.isNotEmpty(filename)) {
            set.put("filename", filename);
        }
        if (StringUtils.isNotEmpty(contentType)) {
            set.put("ContentType", contentType);
        }
        set.put("split_location", "1");
        redisService.hset(key, set, REDIS_EXPIRE_TIME);
        log.info("[ParserServiceImpl: uuid: {}, initializeRedisForResumeParser] Write metadata(filename: {} and contentType: {}) into redis with key: {} and ttl: {}", uuid, filename, contentType, key, REDIS_EXPIRE_TIME);
    }

    private CloudFileObjectMetadata getObjectMetadata(ParseType type, String uuid) {
        CloudFileObjectMetadata metadata = null;
        try {
            if (type == ParseType.RESUME) {
//                metadata = storeClient.getFileDetailWithoutFileFromS3(uuid, UploadTypeEnum.RESUME.getKey()).getBody();
                metadata = storeService.getCommonFileWithoutFileByteByTypeFromS3(uuid, UploadTypeEnum.RESUME.getKey());
            } else if (type == ParseType.JD) {
//                metadata = storeClient.getFileDetailWithoutFileFromS3(uuid, UploadTypeEnum.JD.getKey()).getBody();
                metadata = storeService.getCommonFileWithoutFileByteByTypeFromS3(uuid, UploadTypeEnum.JD.getKey());
            }
        } catch (Exception e) {
            return null;
        }
        return metadata;
    }

    private ImagesInfoDTO formatImagesInfo(String uuid, ImagesInfoParser imagesInfoParser) {
        ImagesInfoDTO imagesInfoDTO = new ImagesInfoDTO();
        if (ObjectUtil.isNotEmpty(imagesInfoParser)) {
            //1.format portrait s3Link
            if (ObjectUtil.isNotNull(imagesInfoParser.getHas_portrait()) && BooleanUtil.toBoolean(imagesInfoParser.getHas_portrait().toString())) {
                try {
//                    CloudFileObjectMetadata portraitFile = storeClient.getFileDetailWithoutFileFromS3(uuid + Constants.S3_KEY_PORTRAIT_SUFFIX, UploadTypeEnum.PORTRAIT.getKey()).getBody();
                    CloudFileObjectMetadata portraitFile = storeService.getCommonFileWithoutFileByteByTypeFromS3(uuid + Constants.S3_KEY_PORTRAIT_SUFFIX, UploadTypeEnum.PORTRAIT.getKey());
                    if (portraitFile != null) {
                        imagesInfoDTO.setPortraitLink(portraitFile.getS3Link());
                    }
                } catch (Exception e) {
                    log.info("info: get presigned url from amazon s3 for talent resume portrait S3Link, uuid: {}", uuid);
                }
            }

            //2.format s3Link and return contentType
//            CloudFileObjectMetadata resumeFile = storeClient.getFileDetailWithoutFileFromS3(uuid, UploadTypeEnum.RESUME.getKey()).getBody();
            CloudFileObjectMetadata resumeFile = storeService.getCommonFileWithoutFileByteByTypeFromS3(uuid, UploadTypeEnum.RESUME.getKey());
            String contentType = null;
            if (resumeFile != null) {
                contentType = resumeFile.getContentType();
            }
            //if RESUME_CONTENT_TYPE_ORIGINAL_RETURN
            if (RESUME_CONTENT_TYPE_ORIGINAL_RETURN.contains(contentType)) {
                //if has display s3Link, use display s3Link
                try {
//                        CloudFileObjectMetadata displayFile = storeClient.getFileDetailWithoutFileFromS3(uuid + Constants.S3_KEY_RESUME_PDF_SUFFIX, UploadTypeEnum.RESUME_DISPLAYS.getKey()).getBody();
                    CloudFileObjectMetadata displayFile = storeService.getCommonFileWithoutFileByteByTypeFromS3(uuid, UploadTypeEnum.RESUME_DISPLAYS.getKey());
                    if (displayFile != null) {
                        imagesInfoDTO.setDisplayLink(displayFile.getS3Link());
                        imagesInfoDTO.setContentType(displayFile.getContentType());
                    } else {
                        setDisplayResumeInfos(uuid, imagesInfoDTO, contentType, false);
                    }
                } catch (Exception e) {
                    log.info("info: get presigned url from amazon s3 for talent resume display S3Link, uuid: {}", uuid);
                }
                //if RESUME_CONTENT_TYPE_NO_NEED_TO_RESET
            } else if (RESUME_CONTENT_TYPE_NO_NEED_TO_RESET.contains(contentType)) {
                setDisplayResumeInfos(uuid, imagesInfoDTO, contentType, false);
                //if RESUME_CONTENT_TYPE_NEED_TO_RESET
            } else if (RESUME_CONTENT_TYPE_NEED_TO_RESET.contains(contentType)) {
                setDisplayResumeInfos(uuid, imagesInfoDTO, null, true);
            }else{
                setDisplayResumeInfos(uuid, imagesInfoDTO, contentType, false);
            }
        }
        return imagesInfoDTO;
    }

    private void setDisplayResumeInfos(String uuid, ImagesInfoDTO imagesInfoDTO, String contentType, Boolean needToReset) {
        try {
            //if RESUME_CONTENT_TYPE_NEED_TO_RESET, reset contentType CONTENT_TYPE_MSWORD
            if (needToReset) {
                imagesInfoDTO.setContentType(CONTENT_TYPE_MSWORD);
//                imagesInfoDTO.setDisplayLink(storeClient.getUrlFromS3WithContentType(uuid, UploadTypeEnum.RESUME.getKey(), imagesInfoDTO.getContentType()).getBody());
                imagesInfoDTO.setDisplayLink(storeService.getCommonUrlFromS3(uuid, UploadTypeEnum.RESUME.getKey(), imagesInfoDTO.getContentType()));
            } else {
                imagesInfoDTO.setContentType(contentType);
//                imagesInfoDTO.setDisplayLink(storeClient.getUrlFromS3(uuid, UploadTypeEnum.RESUME.getKey()).getBody());
                imagesInfoDTO.setDisplayLink(storeService.getCommonUrlFromS3(uuid, UploadTypeEnum.RESUME.getKey(), null));
            }
        } catch (Exception e) {
            log.info("info: get presigned url from amazon s3 for talent resume S3Link, uuid: {}", uuid);
        }
    }

}
