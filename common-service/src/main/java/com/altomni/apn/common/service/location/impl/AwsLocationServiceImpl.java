package com.altomni.apn.common.service.location.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.service.location.AwsLocationClient;
import com.altomni.apn.common.service.location.AwsLocationService;
import com.altomni.apn.common.service.location.dto.AwsLocationSearchDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
*
* <AUTHOR>
* date:2023-04-13
*/
@Service
@Slf4j
public class AwsLocationServiceImpl implements AwsLocationService {

    @Resource
    private AwsLocationClient awsLocationClient;

    @Override
    public JSONObject searchAwsLocationList(AwsLocationSearchDTO awsLocationSearchDTO) {
        String result = awsLocationClient.searchPlaceIndexForSuggestions(awsLocationSearchDTO);
        return JSONUtil.parseObj(result);
    }

    @Override
    public JSONObject queryAwsPlaceById(String placeId, String language) {
        String result = awsLocationClient.getPlace(placeId, language);
        return JSONUtil.parseObj(result);
    }
}
