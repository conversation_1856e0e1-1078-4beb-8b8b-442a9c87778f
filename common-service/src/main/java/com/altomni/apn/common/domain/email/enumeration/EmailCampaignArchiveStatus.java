package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EmailCampaignArchiveStatus implements ConvertedEnum<Integer> {

    NOT_ARCHIVED(0),
    ARCHIVED(4);

    private final int dbValue;

    EmailCampaignArchiveStatus(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<EmailCampaignArchiveStatus, Integer> resolver = new ReverseEnumResolver<>(EmailCampaignArchiveStatus.class, EmailCampaignArchiveStatus::toDbValue);

    public static EmailCampaignArchiveStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
