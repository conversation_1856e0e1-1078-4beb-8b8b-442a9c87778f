package com.altomni.apn.common.web.rest.location;


import cn.hutool.json.JSONObject;
import com.altomni.apn.common.service.location.AwsLocationService;
import com.altomni.apn.common.service.location.dto.AwsLocationSearchDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
* REST controller for managing AwsLocation.
* <AUTHOR>
* date:2023-04-13
*/
@Api(tags = {"APN-AwsLocation"})
@RestController
@RequestMapping("/api/v3/aws")
@Slf4j
public class AwsLocationResource {

    @Resource private AwsLocationService awsLocationService;

    @PostMapping("/search/suggestions")
    public JSONObject searchAwsLocationList(@RequestBody @Valid AwsLocationSearchDTO awsLocationSearchDTO) {
        log.info("[APN: AwsLocation @{}] REST request to search awsLocation : {}", SecurityUtils.getUserId(), awsLocationSearchDTO);
        return awsLocationService.searchAwsLocationList(awsLocationSearchDTO);
    }

    @GetMapping("/places/{placeId}")
    public JSONObject queryAwsPlaceById(@PathVariable("placeId") String placeId, @RequestParam(value = "language", defaultValue = "en") String language) {
        log.info("[APN: AwsLocation @{}] REST request to query awsPlace by placeId: {}", SecurityUtils.getUserId(), placeId);
        return awsLocationService.queryAwsPlaceById(placeId, language);
    }

}
