package com.altomni.apn.common.service.store;


import com.altomni.apn.common.domain.parser.enumeration.ParseType;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.store.CopyObjectDto;
import com.altomni.apn.common.dto.store.UploadUrlDto;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.vo.store.DisplayLinkVo;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Map;

/**
 * <AUTHOR>
 */
public interface StoreService {

    void s3Upload(String key, MultipartFile file, String uploadType);

    String companyDownloadString(String s3Link);


    String companyS3Upload(String content, String contentType);

    String S3ContentUpload(UploadTypeEnum uploadTypeEnum, String contentType, String content, String key);

    String S3ContentUpload(UploadTypeEnum uploadTypeEnum, String contentType, byte[] contentAsBytes, String key);

    String S3Upload(UploadTypeEnum uploadTypeEnum, String contentType, String content);

    Map<String, String> getPresignedUploadUrlFromS3WithPostPolicy(ParseType parseType, String key, String filename, String contentType, Long lowerLimit, Long upperLimit);

    StoreGetUploadUrlVO getPresignedCommonUploadUrlFromS3WithPostPolicy(UploadUrlDto uploadUrlDto);

    CloudFileObjectMetadata getCommonFileByTypeFromS3(String key, String uploadType);

    String getCommonUrlFromS3(String key, String uploadType, String contentType);

    CloudFileObjectMetadata getCommonFileWithoutFileByteByTypeFromS3(String key, String uploadType);

    boolean exists(String uuid, String uploadType);

    boolean copyObject(CopyObjectDto copyObjectDto);

    DisplayLinkVo getDisplayUrlVoFromS3(String uuid, String uploadType);

    String getDownloadUrlFromS3(String uuid, String uploadType, Boolean needRestResponseContentDisposition);

    String getDownloadWatermarkUrlFromS3(String uuid, String uploadType, Boolean needRestResponseContentDisposition);
}
