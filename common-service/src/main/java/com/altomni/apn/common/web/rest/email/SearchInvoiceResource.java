package com.altomni.apn.common.web.rest.email;

import com.altomni.apn.common.dto.searcheamil.SearchEmailByNameDTO;
import com.altomni.apn.common.service.email.CampaignService;
import com.altomni.apn.common.service.esfller.EsFillerService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-EmailService"})
@RestController
@RequestMapping("/api/v3/invoice")
public class SearchInvoiceResource {

    private final Logger log = LoggerFactory.getLogger(SearchInvoiceResource.class);

    @Resource
    private EsFillerService esFillerService;

    @GetMapping("/company/search/{name}")
    public ResponseEntity<String> getCompanyInfoByName(@PathVariable("name") String name) {
        log.info("[APN: search email @{}] REST request to getCompanyInfoByName : {}", SecurityUtils.getUserId(), name);
        return ResponseEntity.ok(esFillerService.getCompanyInfoByName(name));
    }

}
