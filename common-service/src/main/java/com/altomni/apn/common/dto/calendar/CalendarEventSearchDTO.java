package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import lombok.Data;

import java.time.Instant;
import java.util.List;

@Data
public class CalendarEventSearchDTO {
    private Instant startTime;
    private Instant endTime;
    private Boolean withAttendee;
    private List<Long> userIdList;
    private List<CalendarTypeEnum> calendarType;
    private List<CalendarStatusEnum> status;
    private boolean indexFlag;
}
