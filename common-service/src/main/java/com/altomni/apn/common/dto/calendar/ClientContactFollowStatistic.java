package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ClientContactFollowStatistic {
    private List<SystemCalendarStatisticItem> followUpRecordUpdate = new ArrayList<>();
    private Integer followUpRecordUpdateTotal = 0;
    private List<SystemCalendarStatisticItem> expectedOrderExpiration = new ArrayList<>();
    private Integer expectedOrderExpirationTotal = 0;
    private List<SystemCalendarStatisticItem> contractNearingExpiration = new ArrayList<>();
    private Integer contractNearingExpirationTotal = 0;
    private List<SystemCalendarStatisticItem> contractExpired = new ArrayList<>();
    private Integer contractExpiredTotal = 0;
    private List<SystemCalendarStatisticItem> contactJobChange = new ArrayList<>();
    private Integer contactJobChangeTotal = 0;

    public void count() {
        followUpRecordUpdateTotal = followUpRecordUpdate.size();
        expectedOrderExpirationTotal = expectedOrderExpiration.size();
        contractNearingExpirationTotal = contractNearingExpiration.size();
        contractExpiredTotal = contractExpired.size();
        contactJobChangeTotal = contactJobChange.size();
    }

    public Set<Long> allTalentIds() {
        return Stream.of(followUpRecordUpdate, expectedOrderExpiration, contractNearingExpiration, contractExpired, contactJobChange)
                .flatMap(List::stream)
                .map(SystemCalendarStatisticItem::getTalentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    public ClientContactFollowStatistic processConfidentialInfo(Map<Long, ConfidentialInfoDto> confidentialInfoMap, Set<Long> viewAbleConfidentialTalentIds) {
        var processItem = SystemCalendarStatisticItem.processConfidential(confidentialInfoMap, viewAbleConfidentialTalentIds);
        ClientContactFollowStatistic clientContactFollowStatistic = new ClientContactFollowStatistic();
        clientContactFollowStatistic.setFollowUpRecordUpdate(followUpRecordUpdate.stream().map(processItem).collect(Collectors.toList()));
        clientContactFollowStatistic.setExpectedOrderExpiration(expectedOrderExpiration.stream().map(processItem).collect(Collectors.toList()));
        clientContactFollowStatistic.setContractNearingExpiration(contractNearingExpiration.stream().map(processItem).collect(Collectors.toList()));
        clientContactFollowStatistic.setContractExpired(contractExpired.stream().map(processItem).collect(Collectors.toList()));
        clientContactFollowStatistic.setContactJobChange(contactJobChange.stream().map(processItem).collect(Collectors.toList()));
        clientContactFollowStatistic.count();
        return clientContactFollowStatistic;
    }

}
