package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.user.UserActiveDurationStatistic;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SystemCalendarStatisticDTO {
    private Map<Long, ApplicationFollowStatistic> applicationFollow;
    private Long applicationFollowTotal = 0L;
    private Map<Long, ClientContactFollowStatistic> clientContactFollow;
    private Long clientContactFollowTotal = 0L;
    private Map<Long, JobFollowStatistic> jobFollow;
    private Long jobFollowTotal = 0L;
    private UserActiveDurationStatistic userActiveDuration;
    private Long noSubmitTalentReminderDay = 0L;
    private Long noInterviewReminderDay = 0L;
    private SystemCalendarStatisticUserTotalDTO userTotalStatistic;

    public Set<Long> allTalentIds() {
        Set<Long> applicationTalentIds = applicationFollow.values().stream()
                .flatMap(item -> item.allTalentIds().stream())
                .collect(Collectors.toSet());
        Set<Long> clientContactTalentIds = clientContactFollow.values().stream()
                .flatMap(item -> item.allTalentIds().stream())
                .collect(Collectors.toSet());
        return Stream.concat(applicationTalentIds.stream(), clientContactTalentIds.stream()).collect(Collectors.toSet());
    }

    public SystemCalendarStatisticDTO processConfidentialInfo(Map<Long, ConfidentialInfoDto> confidentialInfoMap, Set<Long> viewAbleConfidentialTalentIds) {
        SystemCalendarStatisticDTO dto = new SystemCalendarStatisticDTO();
        Map<Long, ApplicationFollowStatistic> applicationFollow = new HashMap<>();
        this.applicationFollow.forEach((userId, followStatistic) -> {
            ApplicationFollowStatistic statistic = followStatistic.processConfidentialInfo(confidentialInfoMap, viewAbleConfidentialTalentIds);
            applicationFollow.put(userId, statistic);
        });
        Map<Long, ClientContactFollowStatistic> clientContactFollow = new HashMap<>();
        this.clientContactFollow.forEach((userId, followStatistic) -> {
            ClientContactFollowStatistic statistic = followStatistic.processConfidentialInfo(confidentialInfoMap, viewAbleConfidentialTalentIds);
            clientContactFollow.put(userId, statistic);
        });

        dto.setApplicationFollow(applicationFollow);
        dto.setApplicationFollowTotal(this.applicationFollowTotal);
        dto.setClientContactFollow(clientContactFollow);
        dto.setClientContactFollowTotal(this.clientContactFollowTotal);
        dto.setJobFollow(this.jobFollow);
        dto.setJobFollowTotal(this.jobFollowTotal);
        dto.setUserActiveDuration(this.userActiveDuration);
        dto.setNoSubmitTalentReminderDay(this.noSubmitTalentReminderDay);
        dto.setNoInterviewReminderDay(this.noInterviewReminderDay);
        dto.setUserTotalStatistic(this.userTotalStatistic);
        return dto;
    }

}
