package com.altomni.apn.common.service.email.dto;

import com.altomni.apn.common.domain.email.enumeration.EmailCampaignAuditStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailTemplateStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailTemplateType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;


@Data
public class EmailyTemplateDTO {

    @ApiModelProperty(value = "The id of this email template.")
    private Long id;

    @ApiModelProperty(value = "The name of this email template.")
    private String name;

    @ApiModelProperty(value = "The htmlContent of this email template.")
    private EmailyTemplateHtmlContentDTO htmlContent;

    private EmailTemplateStatus reviewStatus = EmailTemplateStatus.PASSED;

    @ApiModelProperty(value = "The screenshot of this email campaign template.")
    private String screenshot;

    @ApiModelProperty(value = "The type of this email campaign template.")
    private EmailTemplateType type = EmailTemplateType.USER_SAVED;

    @ApiModelProperty(value = "The auditStatus of this email campaign template.")
    private EmailCampaignAuditStatus auditStatus;

    @ApiModelProperty(value = "The tags of this email campaign template.")
    private Set<TemplateTagDTO> tags;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public EmailyTemplateHtmlContentDTO getHtmlContent() {
        return htmlContent;
    }

    public void setHtmlContent(EmailyTemplateHtmlContentDTO htmlContent) {
        this.htmlContent = htmlContent;
    }

    public EmailTemplateStatus getReviewStatus() {
        return reviewStatus;
    }

    public void setReviewStatus(EmailTemplateStatus reviewStatus) {
        this.reviewStatus = reviewStatus;
    }

    public String getScreenshot() {
        return screenshot;
    }

    public void setScreenshot(String screenshot) {
        this.screenshot = screenshot;
    }

    public EmailTemplateType getType() {
        return type;
    }

    public void setType(EmailTemplateType type) {
        this.type = type;
    }

    public EmailCampaignAuditStatus getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(EmailCampaignAuditStatus auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Set<TemplateTagDTO> getTags() { return tags; }

    public void setTags(Set<TemplateTagDTO> tags) { this.tags = tags; }

    public EmailyTemplateDTO() {
    }

    public EmailyTemplateDTO(String name, EmailyTemplateHtmlContentDTO htmlContent) {
        this.name = name;
        this.htmlContent = htmlContent;
    }

    public EmailyTemplateDTO(Long id, String name, EmailyTemplateHtmlContentDTO htmlContent) {
        this.id = id;
        this.name = name;
        this.htmlContent = htmlContent;
    }

    public EmailyTemplateDTO(String name, EmailyTemplateHtmlContentDTO htmlContent, EmailTemplateStatus reviewStatus) {
        this.name = name;
        this.htmlContent = htmlContent;
        this.reviewStatus = reviewStatus;
    }

    public EmailyTemplateDTO(Long id, String name, EmailyTemplateHtmlContentDTO htmlContent, String screenshot) {
        this.id = id;
        this.name = name;
        this.htmlContent = htmlContent;
        this.screenshot = screenshot;
    }

}
