package com.altomni.apn.common.service.upgrade.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.upgrade.UpgradeNotification;
import com.altomni.apn.common.domain.upgrade.UpgradeTenantRelation;
import com.altomni.apn.common.domain.upgrade.UpgradeUserRelation;
import com.altomni.apn.common.dto.upgrade.UpgradeCreationDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.repository.upgrade.UpgradeNotificationRepository;
import com.altomni.apn.common.repository.upgrade.UpgradeTenantRelationRepository;
import com.altomni.apn.common.repository.upgrade.UpgradeUserRelationRepository;
import com.altomni.apn.common.service.mapper.upgrade.UpgradeNotificationDTOMapper;
import com.altomni.apn.common.service.mapper.upgrade.UpgradeNotificationVOMapper;
import com.altomni.apn.common.service.upgrade.UpgradeCacheService;
import com.altomni.apn.common.service.upgrade.UpgradeService;
import com.altomni.apn.common.vo.upgrade.UpgradeNotificationVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class UpgradeServiceImpl implements UpgradeService {

    @Resource
    UpgradeNotificationRepository upgradeNotificationRepository;
    @Resource
    UpgradeTenantRelationRepository upgradeTenantRelationRepository;
    @Resource
    UpgradeUserRelationRepository upgradeUserRelationRepository;
    @Resource
    UpgradeNotificationDTOMapper upgradeNotificationMapper;
    @Resource
    UpgradeNotificationVOMapper upgradeNotificationVOMapper;
    @Resource
    UpgradeCacheService upgradeCacheService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(UpgradeCreationDTO upgradeCreationDTO) {
        checkCreationDTO(upgradeCreationDTO);
        UpgradeNotification upgradeNotification = upgradeNotificationMapper.toEntity(upgradeCreationDTO);
        upgradeNotificationRepository.saveAndFlush(upgradeNotification);
        // 当upgradeCreationDTO里Global为false时，将tenantIds转化为UpgradeTenantRelation存储
        if (!upgradeCreationDTO.getGlobal() && CollUtil.isNotEmpty(upgradeCreationDTO.getTenantIds())) {
            saveUpgradeTenantRelation(upgradeNotification.getId(), upgradeCreationDTO.getTenantIds());
        }
        // 根据upgradeCreationDTO里的内容更新redis缓存
        deleteCache(upgradeCreationDTO);
    }

    private void checkCreationDTO(UpgradeCreationDTO dto) {
        if (!dto.getGlobal() && CollUtil.isEmpty(dto.getTenantIds())) {
            throw new CustomParameterizedException("When global is false, tenantIds cannot be empty.");
        }
    }


    @Override
    @Transactional(readOnly = true)
    public UpgradeNotificationVO getUpgradeNotification(Long userId, Long tenantId, LanguageEnum language) {
        UpgradeNotification upgradeNotification = upgradeCacheService.getTenantUpgradeNotification(tenantId);
        if (ObjectUtil.isNotNull(upgradeNotification)) {
            Boolean existUserRelation = checkExistUserRelation(userId, upgradeNotification.getId());
            if (!existUserRelation) {
                return upgradeNotificationVOMapper.toDto(upgradeNotification, language);
            }
        }
        return null;
    }

    public Boolean checkExistUserRelation(Long userId, Long upgradeId) {
        List<UpgradeUserRelation> userRelations = upgradeUserRelationRepository.findAllByUpgradeIdAndUserId(upgradeId, userId);
        if (CollUtil.isNotEmpty(userRelations)) {
            return true;
        }
        return false;
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUpgradeUserRelation(Long upgradeId, Long userId, Long tenantId) {
        List<UpgradeUserRelation> userRelations = upgradeUserRelationRepository.findAllByUpgradeIdAndUserId(upgradeId, userId);
        if (CollUtil.isNotEmpty(userRelations)) {
            throw new CustomParameterizedException("user has already been upgraded");
        } else {
            upgradeUserRelationRepository.save(new UpgradeUserRelation(upgradeId, userId));
        }
    }


    private void saveUpgradeTenantRelation(Long upgradeId, List<Long> tenantIds) {
        List<UpgradeTenantRelation> tenantRelationList = tenantIds.stream().map(t -> new UpgradeTenantRelation(upgradeId, t)).collect(Collectors.toList());
        upgradeTenantRelationRepository.saveAll(tenantRelationList);
    }


    private void deleteCache(UpgradeCreationDTO upgradeCreationDTO) {
        // 当创建了一个全局的更新迭代信息，那么删除Upgrade全局所有缓存
        if (null == upgradeCreationDTO.getGlobal() || upgradeCreationDTO.getGlobal()) {
            upgradeCacheService.deleteAllUpgradeCache();
        } else if (CollUtil.isNotEmpty(upgradeCreationDTO.getTenantIds())) {
            // 当创建的更新迭代信息是非全局的，那么删除Upgrade对应租户的所有缓存
            upgradeCreationDTO.getTenantIds().forEach(t -> {
                upgradeCacheService.deleteCacheByTenantId(t);
            });
        }
    }
}
