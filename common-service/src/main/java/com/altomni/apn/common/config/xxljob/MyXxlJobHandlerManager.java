package com.altomni.apn.common.config.xxljob;

import cn.hutool.core.util.ArrayUtil;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

@Component
public class MyXxlJobHandlerManager implements BeanPostProcessor {

    private static final Map<XxlJobRelationTypeEnum, Method> jobHandlerMap = new HashMap<>();

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        // 使用 Spring 的 ClassPathScanningCandidateComponentProvider 扫描带有 @XxlJobHandler 注解的类
        ReflectionUtils.doWithMethods(bean.getClass(), method -> {
            if (method.isAnnotationPresent(XxlJobHandler.class)) {
                XxlJobHandler xxlJobHandlerAnnotation = method.getAnnotation(XxlJobHandler.class);
                XxlJobRelationTypeEnum[] typeEnumList = xxlJobHandlerAnnotation.values();
                if (ArrayUtil.isNotEmpty(typeEnumList)) {
                    Arrays.stream(typeEnumList).forEach(typeEnum -> {
                        jobHandlerMap.put(typeEnum, method);
                    });
                }
            }
        });
        return bean;
    }

    public static Map<XxlJobRelationTypeEnum, Method> getJobHandlerMap() {
        return jobHandlerMap;
    }

}
