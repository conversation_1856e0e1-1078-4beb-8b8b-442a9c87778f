package com.altomni.apn.common.service.store.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UploadInfo {

    private String bucket;

    private Long minSizeInBytes = 1024L;

    private Long maxSizeInBytes = 20480000L;

    private String folder;

    // 没有配置签名时间,默认为 10分钟
    private Long signatureDuration = 10L;

}
