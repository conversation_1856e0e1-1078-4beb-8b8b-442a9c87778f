package com.altomni.apn.common.domain.statistic;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.time.Instant;

@ApiModel(value = "流程停滞统计表",description = "")
@Entity
@Table(name="talent_recruitment_process_stop_statistics")
@Data
public class TalentRecruitmentProcessStopStatistics extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "last_execution_date")
    private Instant lastExecutionDate;
}
