package com.altomni.apn.common.repository.calendar;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import com.altomni.apn.common.domain.calendar.CalendarEventAttendee;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import com.altomni.apn.common.dto.calendar.CalendarEventAttendeeDTO;
import com.altomni.apn.common.dto.calendar.CalendarEventDTO;
import com.altomni.apn.common.dto.calendar.CalendarEventSearchDTO;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.calendar.CalendarEventAttendeeVO;
import com.altomni.apn.common.vo.calendar.CalendarEventSearchVO;
import com.altomni.apn.common.vo.calendar.CalendarEventVO;
import com.altomni.apn.common.vo.calendar.CalendarRecentDataVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigInteger;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
public class CalendarEventCustomRepository {

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private CalendarEventAttendeeRepository calendarEventAttendeeRepository;

        public CalendarEventSearchVO searchCalendarEventList(CalendarEventSearchDTO dto,
                                                             Pageable pageable) {
        resolveCalendarType(dto);
        List<CalendarEventVO> calendarEventVOList =  new ArrayList<>();
        Long total = 0L;
        if(dto.getCalendarType() != null && !dto.getCalendarType().isEmpty()) {
            StringBuilder dataSql = new StringBuilder("""
                SELECT distinct ce.id, ce.type_id, ce.reference_id, ce.title, ce.description, ce.start_time,
                 ce.end_time, ce.reminder_minutes, cea.is_reminder reminder_flag ,ce.go_to_id, ce.tenant_id,
                 ce.calendar_type,ce.priority,cea.status,cea.completed_time,
                 GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 0 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS company_id,
                 GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 0 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS company_name,
                 GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 0 THEN ceri.company_status ELSE NULL END SEPARATOR ',') AS company_status,
                 GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 1 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS company_contact_id,
                 GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 1 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS company_contact_name,
                 GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 2 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS candidate_id,
                 GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 2 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS candidate_name,
                 GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 3 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS job_id,
                 GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 3 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS job_name
                 FROM calendar_event ce
                 INNER JOIN calendar_event_attendee cea on cea.event_id = ce.id and cea.user_id in( :userId )
                 left join calendar_event_relation_info ceri on ceri.event_id = ce.id
                 where 1=1
                """);

            if (null != dto.getStartTime()) {
                dataSql.append(" and ce.end_time >= :startTime ");
            }

            if (null != dto.getEndTime()) {
                dataSql.append(" and ce.start_time <= :endTime  ");
            }

            if (null != dto.getCalendarType() && !dto.getCalendarType().isEmpty()) {
                dataSql.append(" and ce.calendar_type in (:calendarType )");
            }

            if (null != dto.getStatus() && !dto.getStatus().isEmpty()) {
                dataSql.append(" and cea.status in (:status )");
            }

            if (dto.isIndexFlag()) {
                //系统自动生成的 成单到期日
                dataSql.append(" AND (ce.type_id != 3 or ce.calendar_type != 3) ");
            }

            dataSql.append(" GROUP BY ce.id ");
            String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

            if (dto.isIndexFlag()) {
                dataSql.append(" union ");
                dataSql.append("""
                          SELECT distinct ce.id, ce.type_id, ce.reference_id, ce.title, ce.description, ce.start_time,
                       ce.end_time, ce.reminder_minutes, cea.is_reminder reminder_flag ,ce.go_to_id, ce.tenant_id,
                       ce.calendar_type,ce.priority,cea.status,cea.completed_time,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 0 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS company_id,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 0 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS company_name,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 0 THEN ceri.company_status ELSE NULL END SEPARATOR ',') AS company_status,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 1 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS company_contact_id,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 1 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS company_contact_name,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 2 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS candidate_id,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 2 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS candidate_name,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 3 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS job_id,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 3 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS job_name
                       FROM calendar_event ce
                       INNER JOIN calendar_event_attendee cea on cea.event_id = ce.id and cea.user_id in( :userId )
                        left join calendar_event_relation_info ceri on ceri.event_id = ce.id
                       where CONVERT_TZ(ce.start_time, 'UTC', :timezone) < :now and cea.status !=0
                        """);
                dataSql.append(" AND (ce.type_id != 3 or ce.calendar_type != 3) ");
                if (null != dto.getCalendarType() && !dto.getCalendarType().isEmpty()) {
                    dataSql.append(" and ce.calendar_type in (:calendarType )");
                }

                if (null != dto.getStatus() && !dto.getStatus().isEmpty()) {
                    dataSql.append(" and cea.status in (:status )");
                }

                dataSql.append(" GROUP BY ce.id ");
                dataSql.append("""
                       union 
                       SELECT distinct ce.id, ce.type_id, ce.reference_id, ce.title, ce.description, ce.start_time,
                       ce.end_time, ce.reminder_minutes, cea.is_reminder reminder_flag ,ce.go_to_id, ce.tenant_id,
                       ce.calendar_type,ce.priority,cea.status,cea.completed_time,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 0 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS company_id,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 0 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS company_name,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 0 THEN ceri.company_status ELSE NULL END SEPARATOR ',') AS company_status,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 1 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS company_contact_id,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 1 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS company_contact_name,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 2 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS candidate_id,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 2 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS candidate_name,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 3 THEN ceri.relation_id ELSE NULL END SEPARATOR ',') AS job_id,
                         GROUP_CONCAT(DISTINCT CASE WHEN ceri.relation_type = 3 THEN ceri.relation_name ELSE NULL END SEPARATOR ',') AS job_name
                       FROM calendar_event ce
                       INNER JOIN calendar_event_attendee cea on cea.event_id = ce.id and cea.user_id in( :userId )
                        left join calendar_event_relation_info ceri on ceri.event_id = ce.id
                       where date_format(CONVERT_TZ(cea.completed_time, 'UTC', :timezone),'%Y-%m-%d') = date_format(:now,'%Y-%m-%d') and cea.status=0
                          """);
                dataSql.append(" AND (ce.type_id != 3 or ce.calendar_type != 3) ");

                if (null != dto.getCalendarType() && !dto.getCalendarType().isEmpty()) {
                    dataSql.append(" and ce.calendar_type in (:calendarType )");
                }

                if (null != dto.getStatus() && !dto.getStatus().isEmpty()) {
                    dataSql.append(" and cea.status in (:status )");
                }
                dataSql.append(" GROUP BY ce.id ");
            }
            // 开始构建ORDER BY子句
            dataSql.append(" ORDER BY ");

            // 标记是否已添加排序条件
            boolean hasOrderClause = false;

            // 检查Pageable中是否包含calendar_type的排序
            if (pageable.getSort() != null && !pageable.getSort().isEmpty()) {
                for (Sort.Order order : pageable.getSort()) {
                    // 检查排序字段是否为"type"
                    if ("type".equalsIgnoreCase(order.getProperty())) {
                        // 根据排序方向添加对应的SQL排序条件
                        String direction = order.getDirection().isAscending() ? "ASC" : "DESC";

                        // 正序时：系统任务(ID>20)在前，其他任务在后
                        if (order.getDirection().isAscending()) {
                            // 使用CASE WHEN实现自定义排序逻辑
                            dataSql.append("CASE WHEN calendar_type >= 20 THEN 0 ELSE 1 END ASC, calendar_type ").append(direction);
                        }
                        // 倒序时：正常按calendar_type倒序即可
                        else {
                            dataSql.append("CASE WHEN calendar_type >= 20 THEN 1 ELSE 0 END ASC, calendar_type ").append(direction);
                        }

                        hasOrderClause = true;
                        break;  // 找到type排序后就退出循环
                    }
                }
            }

            // 添加默认排序条件
            if (hasOrderClause) {
                // 如果已经有了calendar_type排序，添加逗号分隔
                dataSql.append(", ");
            }
            dataSql.append("status DESC, start_time ASC");


            Query dataQuery = entityManager.createNativeQuery(dataSql.toString(), CalendarEventVO.class);

            Query countQuery = entityManager.createNativeQuery(countSql);


            dataQuery.setParameter("userId", dto.getUserIdList());
            countQuery.setParameter("userId", dto.getUserIdList());

            if (null != dto.getStartTime()) {
                dataQuery.setParameter("startTime", dto.getStartTime());
                countQuery.setParameter("startTime", dto.getStartTime());
            }

            if (null != dto.getEndTime()) {
                dataQuery.setParameter("endTime", dto.getEndTime());
                countQuery.setParameter("endTime", dto.getEndTime());
            }

            if (dto.isIndexFlag()) {
                String now = DateUtil.fromInstantToDate(Instant.now(),SecurityUtils.getUserTimeZone());
                dataQuery.setParameter("timezone",SecurityUtils.getUserTimeZone());
                dataQuery.setParameter("now", now);
            }

            if (null != dto.getCalendarType() && !dto.getCalendarType().isEmpty()) {
                dataQuery.setParameter("calendarType", dto.getCalendarType().stream().mapToInt(CalendarTypeEnum::toDbValue).boxed().collect(Collectors.toList()));
                countQuery.setParameter("calendarType", dto.getCalendarType().stream().mapToInt(CalendarTypeEnum::toDbValue).boxed().collect(Collectors.toList()));
            }

            if (null != dto.getStatus() && !dto.getStatus().isEmpty()) {
                dataQuery.setParameter("status", dto.getStatus().stream().mapToInt(CalendarStatusEnum::toDbValue).boxed().collect(Collectors.toList()));
                countQuery.setParameter("status", dto.getStatus().stream().mapToInt(CalendarStatusEnum::toDbValue).boxed().collect(Collectors.toList()));
            }

            dataQuery.setFirstResult((int) pageable.getOffset());
            dataQuery.setMaxResults(pageable.getPageSize());

            BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
            total = count.longValue();

            if (dto.isIndexFlag()) {
                calendarEventVOList = dataQuery.getResultList();
            } else {
                calendarEventVOList = total > pageable.getOffset() ? dataQuery.getResultList() : new ArrayList<>();
            }
        }


        this.fillPrivateJob(calendarEventVOList);
        addAttendee(calendarEventVOList, dto.getWithAttendee());
        CalendarEventSearchVO vo = new CalendarEventSearchVO();
        vo.setEventVOList(calendarEventVOList);
        vo.setCalendarCount(total);
        vo.setOverdueCalendarCount(searchOverDue(overDueSql(), dto));
        return vo;
    }

    private void resolveCalendarType(CalendarEventSearchDTO dto) {
        List<CalendarTypeEnum> calendarType = dto.getCalendarType();
        if(calendarType == null) {
            calendarType = new ArrayList<>();
        }
        if(calendarType.contains(CalendarTypeEnum.ALL_SYSTEM_TASK)) {
            calendarType.remove(CalendarTypeEnum.ALL_SYSTEM_TASK);
            calendarType.add(CalendarTypeEnum.NOT_SUBMIT_TO_CLIENT);
            calendarType.add(CalendarTypeEnum.SUBMIT_TO_CLIENT_NOT_UPDATE_STATUS);
            calendarType.add(CalendarTypeEnum.OFFER_PASS_NOT_UPDATE_STATUS);
            calendarType.add(CalendarTypeEnum.PAYMENT_OVERDUE);
            calendarType.add(CalendarTypeEnum.FOLLOW_UP_RECORD_UPDATE);
            calendarType.add(CalendarTypeEnum.EXPECTED_ORDER_EXPIRATION);
            calendarType.add(CalendarTypeEnum.CONTRACT_NEARING_EXPIRATION);
            calendarType.add(CalendarTypeEnum.CONTRACT_EXPIRED);
            calendarType.add(CalendarTypeEnum.CONTACT_JOB_CHANGE);
            calendarType.add(CalendarTypeEnum.NO_SUBMIT_TALENT);
            calendarType.add(CalendarTypeEnum.NO_INTERVIEW);
        }

        dto.setCalendarType(calendarType);
    }

    private String overDueSql() {

        StringBuilder dataSql = new StringBuilder("""
                SELECT distinct ce.id, ce.end_time
                 FROM calendar_event ce
                 INNER JOIN calendar_event_attendee cea on cea.event_id = ce.id and cea.user_id in( :userId )
                 where  CONVERT_TZ(ce.start_time, 'UTC', :timezone) <= :now and cea.status !=0
                 AND (ce.type_id != 3 or ce.calendar_type != 3) and ce.calendar_type in (0,1,2,3,4,5)
                """);

        String overDueSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) tab where tab.end_time <now()";
        return overDueSql;
    }

    private Long searchOverDue(String dataSql, CalendarEventSearchDTO dto) {
        Long total = 0L;
        if (dto.isIndexFlag()) {
            Query countQuery = entityManager.createNativeQuery(dataSql);
            countQuery.setParameter("userId", dto.getUserIdList());

            String now = DateUtil.fromInstantToDate(Instant.now(), SecurityUtils.getUserTimeZone());
            countQuery.setParameter("timezone",SecurityUtils.getUserTimeZone());
            countQuery.setParameter("now", now);

            BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
            total = count.longValue();
        }
        return total;
    }

    private void fillPrivateJob(List<CalendarEventVO> calendarEventVOList) {
        int jobTypeId = 2;
        List<Long> jobIds = calendarEventVOList.stream().filter(c -> c.getTypeId() == jobTypeId).map(CalendarEventVO::getGoToId).toList();
        if (CollectionUtils.isNotEmpty(jobIds)) {
            Set<Long> privateJobIds = calendarEventAttendeeRepository.findPrivateJobIds(jobIds);
            if (CollectionUtils.isNotEmpty(privateJobIds)) {
                calendarEventVOList.forEach(c -> c.setIsPrivateJob(c.getTypeId() == jobTypeId && privateJobIds.contains(c.getGoToId())));
            }
        }
    }

    private void addAttendee(List<CalendarEventVO> calendarEventVOList, Boolean withAttendee) {
        if (BooleanUtil.isTrue(withAttendee) && CollUtil.isNotEmpty(calendarEventVOList)) {
            Map<Long, CalendarEventVO> eventVOMap = calendarEventVOList.stream().collect(Collectors.toMap(CalendarEventVO::getId, a -> a, (a1, a2) -> a1));
            List<CalendarEventAttendee> calendarEventAttendeeList = calendarEventAttendeeRepository.findAllByEventIdIn(new ArrayList<>(eventVOMap.keySet()));
            Map<Long, List<CalendarEventAttendeeVO>> eventAttendeeMap = calendarEventAttendeeList.stream()
                    .collect(Collectors.groupingBy(CalendarEventAttendee::getEventId, Collectors.mapping(a -> {
                        CalendarEventAttendeeVO calendarEventAttendeeVO = new CalendarEventAttendeeVO();
                        BeanUtil.copyProperties(a, calendarEventAttendeeVO);
                        return calendarEventAttendeeVO;
                    }, Collectors.toList())));
            eventVOMap.forEach((k, v) -> v.setAttendees(eventAttendeeMap.get(k)));
        }
    }

    public List<CalendarEventVO> checkDuplicateCalendarEvent(CalendarEventDTO calendarEventDto) {
        if (CollUtil.isEmpty(calendarEventDto.getAttendees())) {
            return new ArrayList<>();
        }
        String eventSql = " SELECT ce.id, ce.type_id, ce.reference_id, ce.title, ce.description, ce.start_time, " +
                " ce.end_time, ce.reminder_minutes, cea.is_reminder reminder_flag ,ce.go_to_id, ce.tenant_id," +
                " ce.calendar_type,ce.priority,cea.status," +
                " '' as company_id,'' as company_name,'' as company_contact_name," +
                " '' as company_contact_id,'' as candidate_name,'' as candidate_id,null as completed_time,'' as company_status, '' as job_id, '' as job_name" +
                " FROM calendar_event ce " +
                " INNER JOIN calendar_event_attendee cea on cea.event_id = ce.id " +
                " where ce.start_time < :endTime and ce.end_time > :startTime " +
                " and cea.user_id in (:userIdList) " +
                " group by ce.id " +
                " order by ce.start_time asc ";
        List<CalendarEventVO> calendarEventVOList = entityManager.createNativeQuery(eventSql, CalendarEventVO.class)
                .setParameter("startTime", calendarEventDto.getStartTime())
                .setParameter("endTime", calendarEventDto.getEndTime())
                .setParameter("userIdList", calendarEventDto.getAttendees().stream().map(CalendarEventAttendeeDTO::getUserId).collect(Collectors.toList()))
                .getResultList();
        addAttendee(calendarEventVOList, true);
        return calendarEventVOList;
    }

    @Transactional(readOnly = true)
    public List<CalendarRecentDataVO> findAllByRelationType(Long userId, Integer type) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select distinct ri.relation_id as id,ri.relation_name as name from calendar_event_relation_info ri
                  left join calendar_event_attendee ea on ea.event_id = ri.event_id
                  where ri.relation_type=:type and ea.user_id=:userId order by ri.created_date desc limit 10
                 """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString(), CalendarRecentDataVO.class);

        dataQuery.setParameter("type", type);
        dataQuery.setParameter("userId", userId);
        List<CalendarRecentDataVO> resultList = dataQuery.getResultList();
        return resultList;
    }

    public Integer countCalendarEventList(CalendarEventSearchDTO dto) {
        StringBuilder dataSql = new StringBuilder("""
                SELECT distinct ce.id
                 FROM calendar_event ce
                 INNER JOIN calendar_event_attendee cea on cea.event_id = ce.id and cea.user_id in( :userId )
                 left join calendar_event_relation_info ceri on ceri.event_id = ce.id
                 where 1=1
                """);

        if (null != dto.getStartTime()) {
            dataSql.append(" and ce.end_time >= :startTime ");
        }

        if (null != dto.getEndTime()) {
            dataSql.append(" and ce.start_time <= :endTime  ");
        }

        if (null != dto.getCalendarType() && !dto.getCalendarType().isEmpty()) {
            dataSql.append(" and ce.calendar_type in (:calendarType )");
        }

        if (null != dto.getStatus() && !dto.getStatus().isEmpty()) {
            dataSql.append(" and cea.status in (:status )");
        }

        dataSql.append(" GROUP BY ce.id ");

        dataSql.append(" union ");
        dataSql.append("""
                          SELECT distinct ce.id
                       FROM calendar_event ce
                       INNER JOIN calendar_event_attendee cea on cea.event_id = ce.id and cea.user_id in( :userId )
                        left join calendar_event_relation_info ceri on ceri.event_id = ce.id
                       where CONVERT_TZ(ce.start_time, 'UTC', :timezone) < :now and cea.status !=0
                        """);
        if (null != dto.getCalendarType() && !dto.getCalendarType().isEmpty()) {
            dataSql.append(" and ce.calendar_type in (:calendarType )");
        }

        if (null != dto.getStatus() && !dto.getStatus().isEmpty()) {
            dataSql.append(" and cea.status in (:status )");
        }

        dataSql.append(" GROUP BY ce.id ");
        dataSql.append("""
                       union 
                       SELECT distinct ce.id
                       FROM calendar_event ce
                       INNER JOIN calendar_event_attendee cea on cea.event_id = ce.id and cea.user_id in( :userId )
                        left join calendar_event_relation_info ceri on ceri.event_id = ce.id
                       where date_format(CONVERT_TZ(cea.completed_time, 'UTC', :timezone),'%Y-%m-%d') = date_format(:now,'%Y-%m-%d') and cea.status=0
                          """);
        if (null != dto.getCalendarType() && !dto.getCalendarType().isEmpty()) {
            dataSql.append(" and ce.calendar_type in (:calendarType )");
        }

        if (null != dto.getStatus() && !dto.getStatus().isEmpty()) {
            dataSql.append(" and cea.status in (:status )");
        }
        dataSql.append(" GROUP BY ce.id ");

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query countQuery = entityManager.createNativeQuery(countSql);


        countQuery.setParameter("userId", dto.getUserIdList());

        if (null != dto.getStartTime()) {
            countQuery.setParameter("startTime", dto.getStartTime());
        }

        if (null != dto.getEndTime()) {
            countQuery.setParameter("endTime", dto.getEndTime());
        }

        if (null != dto.getCalendarType() && !dto.getCalendarType().isEmpty()) {
            countQuery.setParameter("calendarType", dto.getCalendarType().stream().mapToInt(CalendarTypeEnum::toDbValue).boxed().collect(Collectors.toList()));
        }

        if (null != dto.getStatus() && !dto.getStatus().isEmpty()) {
            countQuery.setParameter("status", dto.getStatus().stream().mapToInt(CalendarStatusEnum::toDbValue).boxed().collect(Collectors.toList()));
        }
        String now = DateUtil.fromInstantToDate(Instant.now(),SecurityUtils.getUserTimeZone());
        countQuery.setParameter("timezone",SecurityUtils.getUserTimeZone());
        countQuery.setParameter("now", now);

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));

        return count.intValue();
    }

}
