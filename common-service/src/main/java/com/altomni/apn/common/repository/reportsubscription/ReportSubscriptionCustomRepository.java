package com.altomni.apn.common.repository.reportsubscription;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.enumeration.reportSubscriptions.ReportType;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.vo.reportsubscription.ReportSubscriptionSimplifyVO;
import com.altomni.apn.common.vo.reportsubscription.ReportSubscriptionVO;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.List;

@Repository
public class ReportSubscriptionCustomRepository {

    @PersistenceContext
    private EntityManager entityManager;

    public List<ReportSubscriptionSimplifyVO> getReportSubscriptionList(Long userId, Long tenantId, ReportType reportType) {
        return entityManager.createNativeQuery("""
                select 
                id, 
                name,
                tenant_id,
                is_active, 
                push_time_type, 
                send_time, 
                day_of_month, 
                day_of_week
                from report_subscription
                where puser_id = :userId
                and tenant_id = :tenantId
                and report_type = :reportType
                order by created_date desc
                """,
                ReportSubscriptionSimplifyVO.class)
                .setParameter("userId", userId)
                .setParameter("tenantId", tenantId)
                .setParameter("reportType", reportType.toDbValue())
                .getResultList();
    }

    public ReportSubscriptionVO getReportSubscriptionDetailById(Long id) {
        String sql = """
                select 
                rs.id,
                rs.tenant_id,
                rs.name,
                rs.push_time_type,
                rs.day_of_week,
                rs.day_of_month,
                rs.send_time,
                rs.data_period,
                rs.start_date,
                rs.end_date,
                rs.report_type,
                rs.is_active,
                rs.data_permission_json,
                group_concat(distinct rsr.user_id) recipients,
                group_concat(distinct rspm.push_method) push_methods
                from report_subscription rs 
                Inner join report_subscription_recipient rsr on rsr.subscription_id = rs.id
                Inner join report_subscription_push_method rspm on rspm.subscription_id = rs.id
                where rs.id = :id
                """;
        List<ReportSubscriptionVO> voList = entityManager.createNativeQuery(sql, ReportSubscriptionVO.class)
                .setParameter("id", id)
                .getResultList();
        if (CollUtil.isEmpty(voList)) {
            throw new NotFoundException("Not found report subscription by id: " + id);
        }
        return voList.get(0);
    }
}
