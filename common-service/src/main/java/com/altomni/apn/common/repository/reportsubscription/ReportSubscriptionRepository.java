package com.altomni.apn.common.repository.reportsubscription;

import com.altomni.apn.common.domain.reportsubscription.ReportSubscription;
import com.altomni.apn.common.enumeration.reportSubscriptions.ReportType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface ReportSubscriptionRepository extends JpaRepository<ReportSubscription, Long> {

    @Modifying
    @Transactional
    @Query(value = " update report_subscription set is_active = ?2 where id = ?1 ", nativeQuery = true)
    void updateActiveStatusById(Long id, Integer active);


    @Query(value = """
    SELECT
	count( 1 ) 
    FROM
	report_subscription 
    WHERE
	tenant_id =:tenantId
	AND report_type =:reportType 
	AND is_auto_generate = 1 
	AND puser_id =:userId
    """, nativeQuery = true)
    Integer isExistAutoGenerateReportSubscription(@Param("tenantId")Long tenantId, @Param("reportType") Integer reportType, @Param("userId")Long userId);

    @Query(value = """
    SELECT
	id 
    FROM
	report_subscription 
    WHERE
	tenant_id =:tenantId
	AND report_type =:reportType
    AND is_auto_generate = 1 
	AND puser_id IN (:userIds)
    """, nativeQuery = true)
    List<Long> findIdByUserIdsAndReportType(@Param("tenantId")Long tenantId, @Param("userIds") List<Long> userIds, @Param("reportType") Integer reportType);

}