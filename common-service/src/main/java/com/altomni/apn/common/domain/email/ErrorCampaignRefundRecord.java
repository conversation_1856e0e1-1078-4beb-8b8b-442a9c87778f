package com.altomni.apn.common.domain.email;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.email.enumeration.ErrorCampaignRecordStatus;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "error_campaign_refund_record")
public class ErrorCampaignRefundRecord extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "email_campaign_id")
    private Long emailCampaignId;

    @Column(name = "message")
    private String message;

    @Column(name = "status")
    private ErrorCampaignRecordStatus status;

    @Column(name = "error")
    private String error;

    @Column(name = "exchange")
    private String exchange;

    @Column(name = "routing_key")
    private String routingKey;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEmailCampaignId() {
        return emailCampaignId;
    }

    public void setEmailCampaignId(Long emailCampaignId) {
        this.emailCampaignId = emailCampaignId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public ErrorCampaignRecordStatus getStatus() {
        return status;
    }

    public void setStatus(ErrorCampaignRecordStatus status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getExchange() {
        return exchange;
    }

    public void setExchange(String exchange) {
        this.exchange = exchange;
    }

    public String getRoutingKey() {
        return routingKey;
    }

    public void setRoutingKey(String routingKey) {
        this.routingKey = routingKey;
    }

    public ErrorCampaignRefundRecord() {
    }

    public ErrorCampaignRefundRecord(Long emailCampaignId, String message, ErrorCampaignRecordStatus status, String error, String exchange, String routingKey) {
        this.emailCampaignId = emailCampaignId;
        this.message = message;
        this.status = status;
        this.error = error;
        this.exchange = exchange;
        this.routingKey = routingKey;
    }
}
