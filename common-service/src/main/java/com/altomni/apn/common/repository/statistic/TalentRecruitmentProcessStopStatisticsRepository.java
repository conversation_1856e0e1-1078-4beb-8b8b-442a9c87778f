package com.altomni.apn.common.repository.statistic;

import com.altomni.apn.common.domain.statistic.TalentRecruitmentProcessStopStatistics;
import com.altomni.apn.common.dto.xxljob.UserStoppedProcessCount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface TalentRecruitmentProcessStopStatisticsRepository extends JpaRepository<TalentRecruitmentProcessStopStatistics, Long> {


    @Query("SELECT new com.altomni.apn.common.dto.xxljob.UserStoppedProcessCount(trpku.userId, COUNT(DISTINCT trpss.talentRecruitmentProcessId), user.tenantId) " +
            "FROM TalentRecruitmentProcessStopStatistics trpss " +
            "JOIN TalentRecruitmentProcessKpiUser trpku " +
            "ON trpss.talentRecruitmentProcessId = trpku.talentRecruitmentProcessId " +
            "JOIN User user " +
            "ON user.id = trpku.userId " +
            "AND COALESCE(user.customTimezone, 'UTC') IN (:timezones)" +
            "GROUP BY trpku.userId " +
            "ORDER BY COUNT(DISTINCT trpss.talentRecruitmentProcessId) DESC")
    List<UserStoppedProcessCount> findUserStoppedProcessCounts(@Param("timezones") Set<String> timezones);

    @Modifying
    @Query(value = """
        INSERT INTO talent_recruitment_process_stop_statistics
        (talent_recruitment_process_id, talent_id, last_execution_date, created_by, created_date, last_modified_by, last_modified_date)
        SELECT trp.id AS talent_recruitment_process_id,
        trp.talent_id,
        CURRENT_TIMESTAMP AS last_execution_date,
        COALESCE(trpss.created_by, 'system') AS created_by,
        COALESCE(trpss.created_date, CURRENT_TIMESTAMP) AS created_date,
        'system' AS last_modified_by,
        CURRENT_TIMESTAMP AS last_modified_date
        FROM talent_recruitment_process trp
        LEFT JOIN talent_recruitment_process_stop_statistics trpss ON trp.id = trpss.talent_recruitment_process_id
        WHERE NOT EXISTS (
            SELECT 1
            FROM talent_recruitment_process_node trpn
            WHERE trpn.talent_recruitment_process_id = trp.id AND (trpn.node_status = :nodeStatus OR (trpn.node_status = :activeNodeStatus AND trpn.node_type = :onBoardNodeType))
        )
        AND DATEDIFF(CURRENT_TIMESTAMP, trp.last_modified_date) > :daysDiff
        AND (trpss.id IS NULL OR trp.last_modified_date > trpss.last_execution_date)
        ON DUPLICATE KEY UPDATE
        last_execution_date = VALUES(last_execution_date),
        last_modified_by = VALUES(last_modified_by),
        last_modified_date = VALUES(last_modified_date)
        """, nativeQuery = true)
    void insertOrUpdateStopStatistics(@Param("nodeStatus") int nodeStatus, @Param("activeNodeStatus") int activeNodeStatus, @Param("onBoardNodeType") int onBoardNodeType,  @Param("daysDiff") long daysDiff);

    @Modifying
    @Query(value = "DELETE trpss FROM talent_recruitment_process_stop_statistics trpss " +
            "JOIN talent_recruitment_process trp ON trpss.talent_recruitment_process_id = trp.id " +
            "WHERE EXISTS ( " +
            "    SELECT 1 " +
            "    FROM talent_recruitment_process_node trpn " +
            "    WHERE trpn.talent_recruitment_process_id = trpss.talent_recruitment_process_id " +
            "    AND (trpn.node_status = :nodeStatus OR (trpn.node_status = :activeNodeStatus AND trpn.node_type = :onBoardNodeType)) " +
            ") " +
            "OR (DATEDIFF(CURRENT_TIMESTAMP, trp.last_modified_date) <= :daysDiff)",
            nativeQuery = true)
    int deleteStopStatistics(@Param("nodeStatus") int nodeStatus, @Param("activeNodeStatus") int activeNodeStatus, @Param("onBoardNodeType") int onBoardNodeType, @Param("daysDiff") long daysDiff);

    @Query(value = """
            SELECT
                count(1)
            FROM talent_recruitment_process_kpi_user trpku
            JOIN talent_recruitment_process trp ON trp.id = trpku.talent_recruitment_process_id
            LEFT JOIN talent_recruitment_process_stop_statistics trpss ON trpss.talent_recruitment_process_id = trp.id
            WHERE NOT EXISTS (
                        SELECT 1
                        FROM talent_recruitment_process_node trpn
                        WHERE trpn.talent_recruitment_process_id = trp.id AND (trpn.node_status = :nodeStatus OR (trpn.node_status = :activeNodeStatus AND trpn.node_type = :onBoardNodeType))
                    )
            AND DATEDIFF(CURRENT_TIMESTAMP, trp.last_modified_date) > :daysDiff
            AND trpku.user_id in (:userId) 
            AND (trpss.id IS NULL OR trp.last_modified_date > trpss.last_execution_date);
            """,
            nativeQuery = true)
    int countRefreshApplicationStop(@Param("nodeStatus") int nodeStatus, @Param("activeNodeStatus") int activeNodeStatus, @Param("onBoardNodeType") int onBoardNodeType, @Param("daysDiff") long daysDiff, @Param("userId") Set<Long> userId);


    @Query(value = """
            SELECT
                count(1)
            FROM talent_recruitment_process_stop_statistics trpss
            JOIN talent_recruitment_process trp ON trpss.talent_recruitment_process_id = trp.id
            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trpss.talent_recruitment_process_id = trpku.talent_recruitment_process_id
            WHERE (EXISTS (
                SELECT 1
                FROM talent_recruitment_process_node trpn
                WHERE trpn.talent_recruitment_process_id = trpss.talent_recruitment_process_id
                AND (trpn.node_status = :nodeStatus
                    OR (trpn.node_status = :activeNodeStatus AND trpn.node_type = :onBoardNodeType))
            )
            OR (DATEDIFF(CURRENT_TIMESTAMP, trp.last_modified_date) <= :daysDiff))
            and trpku.user_id in (:userId);
            """,
            nativeQuery = true)
    int countDeleteApplicationStop(@Param("nodeStatus") int nodeStatus, @Param("activeNodeStatus") int activeNodeStatus, @Param("onBoardNodeType") int onBoardNodeType, @Param("daysDiff") long daysDiff, @Param("userId") Set<Long> userId);


    @Modifying
    @Query(value = "UPDATE message_user_relation mr " +
            "INNER JOIN message m ON m.id = mr.message_id " +
            "SET mr.is_delete = :newIsDelete, " +
            "mr.last_modified_date = NOW(), " +
            "mr.last_modified_by = 'system' " +
            "WHERE DATEDIFF(NOW(), m.last_modified_date) > :days " +
            "AND mr.is_favorite = :isFavorite " +
            "AND mr.is_delete = :oldIsDelete", nativeQuery = true)
    void softDeleteMessageAndRelationsByDays(@Param("days") Long days,
                                             @Param("newIsDelete") Integer newIsDelete,
                                             @Param("isFavorite") Integer isFavorite,
                                             @Param("oldIsDelete") Integer oldIsDelete);


    @Modifying
    @Query(value = """
    UPDATE message_user_relation mr
    INNER JOIN message m ON m.id = mr.message_id
    INNER JOIN user u ON u.id = mr.user_id
    SET mr.is_delete = :newIsDelete,
        mr.last_modified_date = NOW(),
        mr.last_modified_by = 'system'
    WHERE JSON_EXTRACT(content, '$.notifyType') = :notifyType
    AND COALESCE(u.custom_timezone, 'UTC') IN (:timezones)
    AND mr.is_favorite = :isFavorite
    AND mr.is_delete = :oldIsDelete
""", nativeQuery = true)
    void softDeleteMessageByType(@Param("notifyType") String notifyType,
                                 @Param("timezones") Set<String> timezones,
                                             @Param("newIsDelete") Integer newIsDelete,
                                             @Param("isFavorite") Integer isFavorite,
                                             @Param("oldIsDelete") Integer oldIsDelete);


    @Modifying
    @Query(value = """
    UPDATE message_user_relation mr
    INNER JOIN message m ON m.id = mr.message_id
    SET mr.is_delete = :newIsDelete,
        mr.last_modified_date = NOW(),
        mr.last_modified_by = 'system'
    WHERE JSON_EXTRACT(content, '$.notifyType') = :notifyType
    AND mr.is_favorite = :isFavorite
    AND mr.is_delete = :oldIsDelete
    AND mr.user_id = :userId
""", nativeQuery = true)
    void softDeleteMessageByTypeAndUserId(@Param("notifyType") String notifyType,
                                 @Param("userId") Long userId,
                                 @Param("newIsDelete") Integer newIsDelete,
                                 @Param("isFavorite") Integer isFavorite,
                                 @Param("oldIsDelete") Integer oldIsDelete);
}
