package com.altomni.apn.common.service.parser.dto;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.parser.enumeration.ParseType;

import java.io.Serializable;
import java.util.Objects;

/**
 * A ParseRecordListDTO.
 */
public class ParseRecordListDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 7966706004232191096L;

    private Long id;

    private ParseType type;

    private String originalFileName;

    private Boolean isReviewed;

    private String note;

    private String uuid;

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ParseType getType() {
        return type;
    }

    public ParseRecordListDTO type(ParseType type) {
        this.type = type;
        return this;
    }

    public void setType(ParseType type) {
        this.type = type;
    }

    public String getOriginalFileName() {
        return originalFileName;
    }

    public ParseRecordListDTO originalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
        return this;
    }

    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    public Boolean getReviewed() {
        return isReviewed;
    }

    public void setReviewed(Boolean reviewed) {
        isReviewed = reviewed;
    }

    public ParseRecordListDTO isReviewed(Boolean isReviewed) {
        this.isReviewed = isReviewed;
        return this;
    }

    public String getNote() {
        return note;
    }

    public ParseRecordListDTO note(String note) {
        this.note = note;
        return this;
    }

    public void setNote(String note) {
        this.note = note;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ParseRecordListDTO parseRecord = (ParseRecordListDTO) o;
        if (parseRecord.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), parseRecord.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "ParseRecord{" +
            "id=" + getId() +
            ", type='" + getType() + "'" +
            ", originalFileName='" + getOriginalFileName() + "'" +
            ", isReviewed='" + getReviewed() + "'" +
            ", note='" + getNote() + "'" +
            "}";
    }
}
