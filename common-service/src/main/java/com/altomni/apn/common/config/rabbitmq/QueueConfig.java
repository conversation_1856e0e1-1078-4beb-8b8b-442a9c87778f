package com.altomni.apn.common.config.rabbitmq;//package com.altomni.apn.parser.config.rabbitmq;
//
//import org.springframework.amqp.core.BindingBuilder;
//import org.springframework.amqp.core.DirectExchange;
//import org.springframework.amqp.core.Queue;
//import org.springframework.amqp.rabbit.core.RabbitAdmin;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.context.annotation.Configuration;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.HashMap;
//import java.util.Map;
//
//@Configuration
//@RefreshScope
//public class QueueConfig {
//
//    @Resource(name = "parserRabbitAdmin")
//    private RabbitAdmin parserRabbitAdmin;
//
//    @Value("${spring.parser-mq.exchange}")
//    private String exchange;
//
//    @Value("${spring.parser-mq.resume-queue}")
//    private String resumeQueue;
//
//    @Value("${spring.parser-mq.resume-routing-key}")
//    private String resumeRoutingKey;
//
//    @Value("${spring.parser-mq.jd-queue}")
//    private String jdQueue;
//
//    @Value("${spring.parser-mq.jd-routing-key}")
//    private String jdRoutingKey;
//
//    @Value("${spring.parser-mq.max-priority}")
//    private int maxPriority;
//
//    @PostConstruct
//    public void emailRabbitInit() {
//        //声明交换机
//        parserRabbitAdmin.declareExchange(new DirectExchange(exchange));
//
//        //声明队列
//        Map<String, Object> args= new HashMap<>();
//        //Set the priority in the range of 0-2.
//        args.put("x-max-priority", maxPriority);
//        parserRabbitAdmin.declareQueue(new Queue(resumeQueue, true, false, false, args));
//
//        parserRabbitAdmin.declareQueue(new Queue(jdQueue, true, false, false));
//
//        //绑定队列及交换机
//        parserRabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(resumeQueue, true, false, false, args)).to(new DirectExchange(exchange)).with(resumeRoutingKey));
//
//        //绑定队列及交换机
//        parserRabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(jdQueue, true, false, false)).to(new DirectExchange(exchange)).with(jdRoutingKey));
//
//    }
//
//}
