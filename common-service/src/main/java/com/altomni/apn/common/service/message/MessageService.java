package com.altomni.apn.common.service.message;

import com.altomni.apn.common.domain.enumeration.message.MessageFavoriteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.dto.message.*;
import com.altomni.apn.common.vo.message.MessageVO;
import org.springframework.data.domain.Page;

import java.util.Map;

public interface MessageService {

    Map<String, Integer> messageStatistics();

    void createMessage(MessageCreateDTO messageCreateDTO);

    Page<MessageVO> searchMessagePage(MessageSearchPageDTO messageSearchPageDTO);

    void deleteMessage(MessageTypeEnum type);

    void updateMessageReaderStatusById(Long id);

    void updateMessageFavoriteStatusById(Long id, MessageFavoriteEnum favorite);

    void historyHandlerMessage();

    void createMessageWithTalentInfoUpdate(MessageCreateWithTalentInfoDTO messageCreateDTO);

    void createMessageWithVoicemail(MessageCreateWithVoicemailDTO messageCreateWithVoicemailDTO);

    /**
     * 发送禁猎客户被推荐到职位提醒
     * @param messageCreateDTO
     */
    void createMessageWithNoPoachingSubmit(MessageCreateWithNoPoachingSubmitDTO messageCreateDTO);

}