package com.altomni.apn.common.service.cache.redis;

import com.altomni.apn.common.domain.report.SubmittalsAgingReport;
import com.altomni.apn.common.domain.report.SummaryReport;
import com.altomni.apn.common.dto.redis.ParserRedisResponse;
import com.altomni.apn.common.dto.redis.ParserResponse;
import com.altomni.apn.common.dto.redis.RedisResponse;
import org.springframework.data.domain.Pageable;

import java.util.Collection;
import java.util.List;
import java.util.Map;


public interface RedisService {

    String get(String key);

    /**
     * Rater
     *
     * @param uuid     uuid
     * @param pageable pageable
     * @return redis response
     */
    RedisResponse getDataForJob(String uuid, Pageable pageable);

    /**
     * Rater
     *
     * @param uuid     uuid
     * @param pageable pageable
     * @return redis response
     */
    RedisResponse getTalentData(String uuid, Pageable pageable);

    /**
     * Report
     *
     * @param uuid       uuid
     * @param resultList summary report list
     */
    void saveSummaryReportData(String uuid, List<SummaryReport> resultList);

    /**
     * Report
     *
     * @param uuid uuid
     */
    RedisResponse getDataForReport(String uuid);

    /**
     * Report
     *
     * @param uuid uuid
     */
    void saveDataForReport(String uuid, String data);

    /**
     * Report
     *
     * @param uuid       uuid
     * @param resultList summary report list
     */
    void saveSubmittalsAgingData(String uuid, List<SubmittalsAgingReport> resultList);

    /**
     * Parser
     * get parsed resume data from redis
     *
     * @param uuid UUID
     * @return parser redis object
     */
    ParserRedisResponse getParserResumeData(String uuid);

    ParserRedisResponse getParserResumeStatus(String uuid);

    /**
     * Parser
     * get parsed JD data from redis
     *
     * @param uuid UUID
     * @return parser redis object
     */
    ParserRedisResponse getParserJDData(String uuid);

    ParserRedisResponse getParserJDStatus(String uuid);

    void saveData(String key, String value, int expireSecond);

    ParserResponse getResumeParseStatus(String uuid);

    String hget(String key, String field);

    void set(String key, String value);

    void set(String key, String value, Integer expireInSeconds);

    void hset(String key, Map<String, String> set);

    void hset(String key, Map<String, String> set, int expire);

    void hset(String key, String field, String value);

    void hdel(String key, String field);

    Map<String, String> hgetAll(String key);

    Boolean exists(String key);

    Long delete(String key);

    void deleteBatch(Collection<String> keys);

     void deleteByKeyPattern(String keyPattern);

    Long setNx(String key, String value);

    Long setNxAndExpire(String key, String value, Long seconds);

}
