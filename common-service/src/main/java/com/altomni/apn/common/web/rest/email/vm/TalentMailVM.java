package com.altomni.apn.common.web.rest.email.vm;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class TalentMailVM extends MailVM implements Serializable {

    private List<Long> talentIds;

    private List<Long> accountContactIds; //客户联系人id

    private Long accountCompanyId;//客户公司id

}
