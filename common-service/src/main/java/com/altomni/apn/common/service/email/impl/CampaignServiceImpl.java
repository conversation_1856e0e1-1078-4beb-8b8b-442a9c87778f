package com.altomni.apn.common.service.email.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.env.ApplicationProperties;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.config.env.email.EmailAppProperties;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignArchiveStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignStatus;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.email.EmailLogOverviewDTO;
import com.altomni.apn.common.dto.email.MailSearchDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.WithDataException;
import com.altomni.apn.common.service.cache.redis.RedisService;
import com.altomni.apn.common.service.email.CampaignService;
import com.altomni.apn.common.service.email.EmailConstans;
import com.altomni.apn.common.service.email.dto.EmailyAssociatedCampaign;
import com.altomni.apn.common.service.email.dto.EmailyCampaignSendDTO;
import com.altomni.apn.common.service.email.dto.EmailyCmpaignDenyDTO;
import com.altomni.apn.common.service.email.dto.emailVM.EmailVM;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.user.UserService;
import com.altomni.apn.common.utils.*;
import com.ipg.resourceserver.client.ClientTokenHolder;
import com.ipg.resourceserver.user.GmailAliasService;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Service
public class CampaignServiceImpl implements CampaignService {

    private final Logger log = LoggerFactory.getLogger(CampaignServiceImpl.class);

    @Resource
    private EmailAppProperties emailAppProperties;

    @Resource
    private HttpService httpService;

    @Resource
    private UserService userService;

    @Resource
    private GmailAliasService gmailAliasService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

    @Resource
    RedisService redisService;

    @Resource
    private ApplicationProperties applicationProperties;

//    private final AsyncRecordRepository asyncRecordRepository;

    private final static String HEADERS_AUTHORIZATION_KEY = "Authorization";

    private final static String URL_SEPATATOR = "/";

    private final static String SPACE = " ";

    private final static String URL_ARCHIVED = "/archived";

    private final static String URL_SEARCH_PAGE = "page=";

    private final static String URL_SEARCH_SIZE = "size=";

    private final static String URL_SEARCH_SORT = "sort=";

    private final static String URL_SEARCH_STATUS = "status=";

    private final static String URL_SEARCH_QUESTION_MARK = "?";

    private final static String URL_SEARCH_AND = "&";

    private final static String URL_SEARCH = "search=";

    private final static String EMAILY_CAMPAIGN_URL_V1 = "/api/v1/campaigns";

    private final static String BY_EXTERNAL_USER = "/by-external-user";

    private final static String EMAILY_CAMPAIGN_DRAFT_URL = "/draft";

    private final static String EMAILY_COMPANY_URL_V1 = "/api/v1/company";

    private final static String URL_ARCHIVE = "/archive";

    private final static String URL_UNARCHIVE = "/unarchive";

    private final static String URL_SEND = "/send";

    private final static String URL_DENY = "/deny";

    private final static String URL_TEST = "/test";

    private final static String URL_ACCOUNT_LIMIT = "/daily-limit";

    private String emailyBaseUrl() {
        return emailAppProperties.getEmailyBaseUrl();
    }

    private String createCampaignUrl() {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + EMAILY_CAMPAIGN_DRAFT_URL;
    }

    private String saveDraftCampaignUrl() {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + "/save-draft";
    }

    private String updateDraftCampaignUrl(Long campaignId) {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_SEPATATOR + campaignId;
    }

    private String searchCampaignsUrl(EmailCampaignArchiveStatus archiveStatus, List<EmailCampaignStatus> statusList, String search, String sort, Pageable pageable) {
        String pagination = PageableConvertUtil.convertPageableToString(pageable);
        if (archiveStatus != EmailCampaignArchiveStatus.ARCHIVED) {
            return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + "/with-mongo-audience" + URL_SEARCH_QUESTION_MARK + (ObjectUtil.isEmpty(search) ? "" : (URL_SEARCH + search) + URL_SEARCH_AND) +
                    (CollectionUtils.isEmpty(statusList) ? "" : URL_SEARCH_STATUS + StringUtils.join(statusList, ",") + URL_SEARCH_AND) +
                    pagination;
        } else {
            return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_ARCHIVED + "/with-mongo-audience" + URL_SEARCH_QUESTION_MARK + (ObjectUtil.isEmpty(search) ? "" : (URL_SEARCH + search) + URL_SEARCH_AND) +
                    (CollectionUtils.isEmpty(statusList) ? "" : URL_SEARCH_STATUS + StringUtils.join(statusList, ",") + URL_SEARCH_AND) +
                    pagination;
        }
    }

    private String searchCampaignByNameUrl(String campaignName) {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_SEARCH_QUESTION_MARK + URL_SEARCH + campaignName;
    }

    private String updateCampaignStatusFromDraftToPendingUrl(Long campaignId) {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + EMAILY_CAMPAIGN_DRAFT_URL + URL_SEPATATOR + campaignId;
    }

    private String archiveCampaignUrl() {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_ARCHIVE;
    }

    private String unarchiveCampaignUrl() {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_UNARCHIVE;
    }

    private String approveCampaignUrl(Long campaignId) {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_SEND + URL_SEPATATOR + campaignId;
    }

    private String denyCampaignUrl(Long campaignId) {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_DENY + URL_SEPATATOR + campaignId;
    }

    private String sendTestCampaignUrl(Long campaignId) {
        return emailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_SEND + URL_SEPATATOR + campaignId + URL_TEST;
    }

    private String queryTenantLimitUrl() {
        return emailyBaseUrl() + EMAILY_COMPANY_URL_V1 + URL_ACCOUNT_LIMIT;
    }

    private String getListByStatusUrl(String status, Pageable pageable) {
        String pagination = PageableConvertUtil.convertPageableToString(pageable);
        return emailyBaseUrl() + "/api/v1/mail/list?status=" + status + URL_SEARCH_AND + pagination;
    }

    private String getByIdUrl(Long id) {
        return emailyBaseUrl() + "/api/v1/mail/list/" + id;
    }

    private String getByIdUrlIgnoreUserId(Long id) {
        return emailyBaseUrl() + "/api/v1/mail/list/" + id + "/within-tenant";
    }

    private String getCountVoipTalentNoticeEmail(Long id) {
        return emailyBaseUrl() + "/api/v1/mail/count/talent?id=" + id;
    }

    private String getCountVoipContactNoticeEmail(Long id) {
        return emailyBaseUrl() + "/api/v1/mail/count/contact?id=" + id;
    }

    private String getTalentRecordSyncEsUrl(Long tenantId) {
        return applicationProperties.getRecordUrl() + "activity_talent_" + tenantId + "/_doc";
    }

    @Override
    public HttpResponse createEmailCampaign(EmailyAssociatedCampaign campaign) throws IOException {
        if (ObjectUtil.isEmpty(campaign) || ObjectUtil.isEmpty(campaign.getName())) {
            log.error("[EmailService: CampaignServiceImpl] create a email campaign to Emaily error, parameter error, the email campaign name cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(campaign, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(createCampaignUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] create a email campaign to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] create a email campaign to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse saveDraftCampaign(EmailyAssociatedCampaign campaign) throws IOException {

        String groupParamStr = JSON.toJSONString(campaign, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(saveDraftCampaignUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] save a draft email campaign to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] save a draft email campaign to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse updateEmailCampaign(Long campaignId, EmailyAssociatedCampaign emailyCampaign) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaign) || ObjectUtil.isEmpty(campaignId)) {
            log.error("[EmailService: CampaignServiceImpl] update a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        if (Objects.isNull(emailyCampaign.getIndustryId())) {
            emailyCampaign.setIndustryId(109L); //default industry
        }
        String groupParamStr = JSON.toJSONString(emailyCampaign, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(updateDraftCampaignUrl(campaignId), getRequestHeaders(), groupParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] update a email campaign to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] update a email campaign to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse updateEmailCampaignToPending(Long campaignId) throws IOException { //request admin for approval
        if (ObjectUtil.isEmpty(campaignId)) {
            log.error("[EmailService: CampaignServiceImpl] update a email campaign status to pending to Emaily error, parameter error, the emaily campaign id cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.post(updateCampaignStatusFromDraftToPendingUrl(campaignId), getRequestHeaders(), "");

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] update a email campaign status to pending to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] update a email campaign status to pending to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse getCampaignById(Long campaignId) throws IOException {
        if (ObjectUtil.isEmpty(campaignId)) {
            log.error("[EmailService: CampaignServiceImpl] query a email campaign from Emaily error, parameter error, the emaily campaign id cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(updateDraftCampaignUrl(campaignId), getRequestHeaders());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] query a email campaign from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] query a email campaign from Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse searchEmailCampaign(EmailCampaignArchiveStatus archiveStatus, List<EmailCampaignStatus> statusList, String search, String sort, Pageable pageable) throws IOException {

        System.out.println(searchCampaignsUrl(archiveStatus, statusList, search, sort, pageable));
        HttpResponse response = httpService.get(searchCampaignsUrl(archiveStatus, statusList, search, sort, pageable), getRequestHeaders());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] search email campaigns from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] search email campaigns from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;

    }

    @Override
    public HttpResponse searchEmailCampaignByName(String campaignName) throws IOException {
        if (ObjectUtil.isEmpty(campaignName)) {
            log.error("[EmailService: CampaignServiceImpl] search email campaigns by name from Emaily error, parameter error, the emaily campaign name cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(searchCampaignByNameUrl(campaignName), getRequestHeaders());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] search email campaigns by name from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] search email campaigns by name from Emaily error and response is null.}", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse archiveEmailCampaign(Long campaignId) throws IOException {
        if (ObjectUtil.isEmpty(campaignId)) {
            log.error("[EmailService: CampaignServiceImpl] archive a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(Arrays.asList(campaignId), SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(archiveCampaignUrl(), getRequestHeaders(), campaignParamStr);

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] archive a email campaign status to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] archive a email campaign status to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse unarchiveEmailCampaign(Long campaignId) throws IOException {
        if (ObjectUtil.isEmpty(campaignId)) {
            log.error("[EmailService: CampaignServiceImpl] unarchive a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(Arrays.asList(campaignId), SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(unarchiveCampaignUrl(), getRequestHeaders(), campaignParamStr);

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] unarchive a email campaign status to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] unarchive a email campaign status to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse approveEmailCampaign(Long campaignId) throws IOException {
        if (ObjectUtil.isEmpty(campaignId)) {
            log.error("[EmailService: CampaignServiceImpl] approve a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.post(approveCampaignUrl(campaignId), getRequestHeaders(), "");

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] approve a email campaign status to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] approve a email campaign status to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse denyEmailCampaign(Long campaignId, EmailyCmpaignDenyDTO denyDTO) throws IOException {
        if (ObjectUtil.isEmpty(campaignId)) {
            log.error("[EmailService: CampaignServiceImpl] deny a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(denyDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(denyCampaignUrl(campaignId), getRequestHeaders(), campaignParamStr);

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] deny a email campaign status to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] deny a email campaign status to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse sendEmailCampaignTest(Long campaignId, List<EmailyCampaignSendDTO> recipients) throws IOException {
        if (ObjectUtil.isEmpty(campaignId)) {
            log.error("[EmailService: CampaignServiceImpl] send a tested email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(recipients, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(sendTestCampaignUrl(campaignId), getRequestHeaders(), campaignParamStr);

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] send a tested email campaign to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] send a tested email campaign to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse queryTenantLimit() throws IOException {
        HttpResponse response = httpService.get(queryTenantLimitUrl(), getRequestHeaders());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] query company limit from Emaily error response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] query company limit from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public void sendHtmlMail(String from, List<String> to, List<String> cc, List<String> bcc, String subject, String html_content, List<String> links, List<MultipartFile> files, boolean systemEmail, boolean autoSwitchToOtherESP, Boolean transactional, Long accountCompanyId, Long accountContactId) throws IOException {
        String authorizationHeader = getAuthorizationHeader();
        log.info("[CampaignServiceImpl: sendHtmlMail] authorizationHeader: {}", authorizationHeader);
        sendHtmlMail(from, to, cc, bcc, subject, html_content, links, files, systemEmail, autoSwitchToOtherESP, transactional);

        //通知crm完成客户联系人跟进记录更新系统日程
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            notifyCrmCompleteContactFollow(to, authorizationHeader, accountCompanyId, accountContactId);
        });
    }

    public Headers getRequestHeaders() {
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, TOKEN_TYPE + SPACE + currentUserToken);
        headersBuilder.put(EmailConstans.HEADER_CLIENT_ID, ClientTokenHolder.getInstance().getClientId());
        if (SecurityUtils.getUserUid() != null) {
            headersBuilder.put(EmailConstans.HEADER_UID, SecurityUtils.getUserUid());
        }
        return Headers.of(headersBuilder);
    }

    private Headers getDefaultSystemUserToken() {
        Map<String, String> headersBuilder = new HashMap<>();
        LoginUtil.simulateLoginWithClient();
        String token = SecurityUtils.getCurrentUserToken();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, "bearer" + SPACE + token);
        headersBuilder.put(EmailConstans.HEADER_CLIENT_ID, ClientTokenHolder.getInstance().getClientId());
        if (SecurityUtils.getUserUid() != null) {
            headersBuilder.put(EmailConstans.HEADER_UID, SecurityUtils.getUserUid());
        }
        return Headers.of(headersBuilder);
    }

    @Override
    public void sendHtmlMail(String from, List<String> to, List<String> cc, List<String> bcc, String subject, String htmlContent, List<String> links, List<MultipartFile> files, boolean systemEmail, boolean autoSwitchToOtherESP, Boolean transactional) throws IOException {


        // pre-check
        if (StringUtils.isEmpty(from)) {
            User user = userService.findUserById(SecurityUtils.getUserId()).getBody();
            if (Objects.isNull(user)) {
                log.error("[EmailService: CampaignServiceImpl] sentHtmlEmail no from attribute and cannot find current user!");
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILNOUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
            }
            from = user.getEmail();
        }

        if (StringUtils.isEmpty(htmlContent)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILNOCONTENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        if (StringUtils.isEmpty(subject)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILNOSUBJECT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        if (CollectionUtils.isEmpty(to)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILNORECIPIENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        // build url
        HttpUrl.Builder urlBuilder = HttpUrl.parse(emailAppProperties.getEmailyBaseUrl() + "/api/v1/send_rich_mail/internal")
                .newBuilder();

//        if (autoSwitchToOtherESP) {
//            urlBuilder.addQueryParameter("autoSwitchToOtherESP", String.valueOf(true));
//        }
//
//        if (systemEmail) {
//            urlBuilder.addQueryParameter("isSystemEmail", String.valueOf(true));
//        }
//
//        if (BooleanUtils.isTrue(transactional)) {
//            urlBuilder.addQueryParameter("transactional", String.valueOf(true));
//        }


        // build multipart body
        addAlternateEmail(to, cc, bcc);

        MultipartBody.Builder formDataPartBuilder = new MultipartBody.Builder()
                .setType(MultipartBody.FORM)
                .addFormDataPart("from", from)
                .addFormDataPart("to", String.join(",", to))
                .addFormDataPart("subject", subject)
                .addFormDataPart("html_content", htmlContent);

        if (CollectionUtils.isNotEmpty(cc)) {
            formDataPartBuilder.addFormDataPart("cc", String.join(",", cc));
        }

        if (CollectionUtils.isNotEmpty(bcc)) {
            formDataPartBuilder.addFormDataPart("bcc", String.join(",", bcc));
        }

        if (CollectionUtils.isNotEmpty(links)) {
            formDataPartBuilder.addFormDataPart("links", String.join(",", links));
        }

        if (Objects.nonNull(files)) {
            for (MultipartFile file: files) {
                formDataPartBuilder.addFormDataPart("files", file.getOriginalFilename(), RequestBody.create(file.getBytes(), MediaType.parse(file.getContentType())));
            }
        }

        if (autoSwitchToOtherESP) {
            formDataPartBuilder.addFormDataPart("autoSwitchToOtherESP", String.valueOf(true));
        }

        if (systemEmail) {
            formDataPartBuilder.addFormDataPart("isSystemEmail", String.valueOf(true));
        }

        if (BooleanUtils.isTrue(transactional)) {
            formDataPartBuilder.addFormDataPart("transactional", String.valueOf(true));
        }

        RequestBody requestBody = formDataPartBuilder.build();


        // assemble token in header
        Headers headers = systemEmail ? getDefaultSystemUserToken() : getRequestHeaders();

//        else {
//            boolean checkGmailAliasBindingStatus = false;
//            try {
//                checkGmailAliasBindingStatus = userEmailService.needCheckGmailAliasBindingStatus();
//            } catch (Exception e) {
//                log.warn("[EmailService: CampaignServiceImpl @{}]needCheckGmailAliasBindingStatus Exception, e: {}", SecurityUtils.getUserId(), e.getMessage());
//            }
//            urlBuilder.addQueryParameter("checkGmailAliasBindingStatus", String.valueOf(checkGmailAliasBindingStatus));
//        }

//        boolean checkGmailAliasBindingStatus = false;
//        if (!systemEmail) {
//            try {
//                checkGmailAliasBindingStatus = userEmailService.needCheckGmailAliasBindingStatus();
//            } catch (Exception e) {
//                log.warn("[EmailService: CampaignServiceImpl @{}]needCheckGmailAliasBindingStatus Exception, e: {}", SecurityUtils.getUserId(), e.getMessage());
//            }
//        }
//
//        HttpUrl url = HttpUrl.parse(emailAppProperties.getEmailyBaseUrl() + "/api/v1/send_rich_mail/internal")
//                .newBuilder()
//                .addQueryParameter("checkGmailAliasBindingStatus", String.valueOf(checkGmailAliasBindingStatus))
//                .build();

        // construct request
        Request request = new Request.Builder()
//                .url(applicationProperties.getEmailyBaseUrl() + "/api/v1/send_rich_mail/internal")
                .url(urlBuilder.build().url())
                .post(requestBody)
                .headers(headers)
                .build();

        log.info("[CampaignServiceImpl: sendHtmlMail] url: {}, body: {}", urlBuilder.build().toString(), MultipartLoggerUtils.formatMultipartBody(formDataPartBuilder));

//        System.out.println(request.toString());

        try (okhttp3.Response response = new OkHttpClient().newBuilder().callTimeout(30, TimeUnit.SECONDS).readTimeout(30, TimeUnit.SECONDS).connectTimeout(30, TimeUnit.SECONDS).build().newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            log.info("[CampaignServiceImpl: sendHtmlMail @{}] send email to Emaily with response: {}", SecurityUtils.getUserId(), responseBody);
            if (!response.isSuccessful()) {
                log.error("[CampaignServiceImpl: sendHtmlMail @{}] send email to Emaily error: {}", SecurityUtils.getUserId(), responseBody);
                if (response.code() == 400 && StringUtils.isNotEmpty(responseBody)) {
                    try {
                        JSONObject resJson = JSONObject.parseObject(responseBody);
                        if (resJson.containsKey("errorCode") && resJson.containsKey("errorMsg")) {
                            int errorCode = resJson.getIntValue("errorCode");
                            String errorMsg = resJson.getString("errorMsg");
                            log.error("[CampaignServiceImpl: sendHtmlMail @{}] send email to Emaily error, with gmail-alias error code: {}, and error message: {}", SecurityUtils.getUserId(), errorMsg, errorMsg);
                            if (errorCode == 462 || errorCode == 420) {
                                SecurityUtils.getCurrentUserLogin().ifPresent(loginUser -> gmailAliasService.removeGmailAliasRecord(loginUser.getEmail()));
                                throw new CustomParameterizedException(412, "Gmail-Alias Binding Exception", "the binding gmail account is expired or invalid, please binding again!", new HashMap<>());
                            } else if (errorCode == 409 || errorCode == 466) { // 409 (Bad domain, defined in Emaily), 466 (Credit doesn't enough, defined in Gmail-Alias)
                                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILGMAILACCOUNTEXPIRED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
                            } else {
                                throw new CustomParameterizedException(responseBody);
                            }
                        } else {
                            throw new CustomParameterizedException(responseBody);
                        }
                    } catch (JSONException e) {
                        throw new CustomParameterizedException(responseBody);
                    }
                }

                throw new CustomParameterizedException(responseBody);
            }

        } catch (Exception e){
            log.error("[CampaignServiceImpl: sendHtmlMail @{}] send email to Emaily error, IOException", SecurityUtils.getUserId(), e);
            if (e instanceof CustomParameterizedException) {
                throw e;
            }
            throw new IOException("[CampaignServiceImpl: sendHtmlMail] Error when sending email!");
        }
    }

    private void notifyCrmCompleteContactFollow(List<String> to, String authorizationHeader, Long accountCompanyId, Long accountContactId) {
        log.info("[TalentServiceImpl: syncContactCompleteFollowSystemCalendar] sync calendar to crm, to: {}, accountCompanyId:{}, accountContactId:{}", to, accountCompanyId, accountContactId);

        String syncCrmUrl = applicationProperties.getCrmUrl() + "/common/api/v1/system-calendar/notify-contact-follow-complete";
        JSONObject param = new JSONObject();
        param.put("emailList", to);
        param.put("accountCompanyId", accountCompanyId);
        param.put("accountContactId", accountContactId);
        String body = param.toJSONString();
        log.info("sync crm system calendar url : {}, authorizationHeader: {}, body: {}", syncCrmUrl, authorizationHeader, body);
        cn.hutool.http.HttpResponse response = cn.hutool.http.HttpUtil.createRequest(Method.POST, syncCrmUrl)
                .header("Authorization", authorizationHeader)
                .body(body).execute();

        log.info("sync crm system calendar, response code : {}, response message: {}", response.getStatus(), response.body());
        int code = response.getStatus();
        if (HttpStatus.UNAUTHORIZED.value() == code) {
            throw new ExternalServiceInterfaceException("No crm update permission", HttpStatus.FORBIDDEN.value());
        } else if (HttpStatus.OK.value() == code || HttpStatus.NOT_FOUND.value() == code) {
            return;
        } else {
            if (response.getStatus() == cn.hutool.http.Status.HTTP_PRECON_FAILED) {
                cn.hutool.json.JSONObject res = JSONUtil.parseObj(response.body());
                throw new WithDataException(res.getStr("message"), cn.hutool.http.Status.HTTP_PRECON_FAILED, res.get("data"));
            } else {
                throw new ExternalServiceInterfaceException(response.body(), response.getStatus());
            }
        }
    }

    private String getAuthorizationHeader() {
        ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if(sra == null) {
            return null;
        }
        HttpServletRequest request = sra.getRequest();
        return request.getHeader(HttpHeaders.AUTHORIZATION);
    }

    @Override
    public String sendHtmlMail(com.altomni.apn.common.web.rest.email.vm.MailVM mailVM, boolean systemEmail, boolean autoSwitchToOtherESP, boolean isSending) throws IOException {
        if (StringUtils.isEmpty(mailVM.getFrom())) {
            User user = userService.findUserById(SecurityUtils.getUserId()).getBody();
            if (Objects.isNull(user)) {
                log.error("[EmailService: CampaignServiceImpl] sentHtmlEmail no from attribute and cannot find current user!");
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILNOUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
            }
            mailVM.setFrom(user.getEmail());
        }

        if (StringUtils.isEmpty(mailVM.getContent())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILNOCONTENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        if (StringUtils.isEmpty(mailVM.getSubject())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILNOSUBJECT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        if (CollectionUtils.isEmpty(mailVM.getTo())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILNORECIPIENT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        addAlternateEmail(mailVM.getTo(), mailVM.getCc(), mailVM.getBcc());

//        MultipartBody.Builder builder = new MultipartBody.Builder()
//                .setType(MultipartBody.FORM)
//                .addFormDataPart("from", from)
//                .addFormDataPart("to", String.join(",", to))
//                .addFormDataPart("subject", subject)
//                .addFormDataPart("html_content", htmlContent);
//
//        if (CollectionUtils.isNotEmpty(cc)) {
//            builder.addFormDataPart("cc", String.join(",", cc));
//        }
//
//        if (CollectionUtils.isNotEmpty(bcc)) {
//            builder.addFormDataPart("bcc", String.join(",", bcc));
//        }
//
//        if (CollectionUtils.isNotEmpty(links)) {
//            builder.addFormDataPart("links", String.join(",", links));
//        }
//
//        if (Objects.nonNull(files)) {
//            for (MultipartFile file: files) {
//                builder.addFormDataPart("files", file.getOriginalFilename(), RequestBody.create(file.getBytes(), MediaType.parse(file.getContentType())));
//            }
//        }

//        RequestBody requestBody = builder.build();

        RequestBody requestBody = RequestBody.create(okhttp3.MediaType.parse("application/json; charset=utf-8"), JSON.toJSONString(mailVM));

        Headers headers = systemEmail ? getDefaultSystemUserToken() : getRequestHeaders();

        String uri = isSending ? "send-draft": "save-draft" ;

        HttpUrl.Builder urlBuilder = HttpUrl.parse(emailAppProperties.getEmailyBaseUrl() + "/api/v1/mail/" + uri)
                .newBuilder();

        if (autoSwitchToOtherESP) {
            urlBuilder.addQueryParameter("autoSwitchToOtherESP", String.valueOf(true));
        }

        if (systemEmail) {
            urlBuilder.addQueryParameter("isSystemEmail", String.valueOf(true));
        }

        Request request = new Request.Builder()
//                .url(applicationProperties.getEmailyBaseUrl() + "/api/v1/send_rich_mail/internal")
                .url(urlBuilder.build().url())
                .post(requestBody)
                .headers(headers)
                .build();


        System.out.println(request.toString());

//            okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON_TYPE, bodyJson);
//            Request request = new Request.Builder().url(url).post(body).build();
        try (okhttp3.Response response = new OkHttpClient().newBuilder().callTimeout(30, TimeUnit.SECONDS).readTimeout(30, TimeUnit.SECONDS).connectTimeout(30, TimeUnit.SECONDS).build().newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            log.info("[EmailService: CampaignServiceImpl @{}] send email to Emaily with response: {}", SecurityUtils.getUserId(), responseBody);
            if (!response.isSuccessful()) {
                log.error("[EmailService: CampaignServiceImpl @{}] send email to Emaily error: {}", SecurityUtils.getUserId(), responseBody);
                if (response.code() == 400 && StringUtils.isNotEmpty(responseBody)) {
                    try {
                        JSONObject resJson = JSONObject.parseObject(responseBody);
                        if (resJson.containsKey("errorCode") && resJson.containsKey("errorMsg")) {
                            int errorCode = resJson.getIntValue("errorCode");
                            String errorMsg = resJson.getString("errorMsg");
                            log.error("[EmailService: CampaignServiceImpl @{}] send email to Emaily error, with gmail-alias error code: {}, and error message: {}", SecurityUtils.getUserId(), errorMsg, errorMsg);
                            if (errorCode == 462 || errorCode == 420) {
                                SecurityUtils.getCurrentUserLogin().ifPresent(loginUser -> gmailAliasService.removeGmailAliasRecord(loginUser.getEmail()));
                                throw new CustomParameterizedException(412, "Gmail-Alias Binding Exception", "the binding gmail account is expired or invalid, please binding again!", new HashMap<>());
                            } else if (errorCode == 409 || errorCode == 466) { // 409 (Bad domain, defined in Emaily), 466 (Credit doesn't enough, defined in Gmail-Alias)
                                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDHTMLMAILGMAILACCOUNTEXPIRED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
                            } else {
                                throw new CustomParameterizedException(responseBody);
                            }
                        } else if (resJson.containsKey("message")) {
                            String message = resJson.getString("message");
                            throw new CustomParameterizedException(message);
                        } else {
                            throw new CustomParameterizedException(responseBody);
                        }
                    } catch (JSONException e) {
                        throw new CustomParameterizedException(responseBody);
                    }
                }
                throw new CustomParameterizedException(responseBody);
            } else {
                return responseBody;
            }
        } catch (Exception e){
            log.error("[EmailService: CampaignServiceImpl @{}] send email to Emaily error, IOException: {}", SecurityUtils.getUserId(), e.getMessage());
            if (e instanceof CustomParameterizedException) {
                throw e;
            }
            throw new IOException("[Email Service] Error when sending email!");
        }
    }

    private void addAlternateEmail(List<String> toList, List<String> ccList, List<String> bccList) {
        Map<String, String> map = redisService.hgetAll("email-alternate-set");
        if (MapUtils.isEmpty(map)) {
            return;
        }

        Set<String> alternateEmail = new HashSet<>();
        if (CollectionUtils.isNotEmpty(toList)) {
            for (String to: toList) {
                if (map.containsKey(to)) {
                    alternateEmail.add(map.get(to));
                }
            }
            Set<String> toSet = new HashSet<>(toList);
            toSet.addAll(alternateEmail);
            toList.clear();
            toList.addAll(toSet);
        }
        alternateEmail.clear();

        if (CollectionUtils.isNotEmpty(ccList)) {
            for (String cc : ccList) {
                if (map.containsKey(cc)) {
                    alternateEmail.add(map.get(cc));
                }
            }
            Set<String> ccSet = new HashSet<>(ccList);
            ccSet.addAll(alternateEmail);
            ccList.clear();
            ccList.addAll(ccSet);
        }
        alternateEmail.clear();

        if (CollectionUtils.isNotEmpty(bccList)) {
            for (String bcc: bccList) {
                if (map.containsKey(bcc)) {
                    alternateEmail.add(map.get(bcc));
                }
            }
            Set<String> bccSet = new HashSet<>(bccList);
            bccSet.addAll(alternateEmail);
            bccList.clear();
            bccList.addAll(bccSet);
        }
    }

    @Override
    public HttpResponse getListByStatus(String status, Pageable pageable) throws IOException {

        if (ObjectUtil.isEmpty(status)) {
            log.error("[EmailService: getListByStatus] search email campaigns by name from Emaily error, parameter error, the emaily campaign name cannot be empty.");
            throw new CustomParameterizedException("You can not get list by this status!");
        }

        HttpResponse response = httpService.get(getListByStatusUrl(status, pageable), getRequestHeaders());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: getListByStatus] search email campaigns by name from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: getListByStatus @{}] search email campaigns by name from Emaily error and response is null.}", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse countVoipTalentNoticeEmail(Long id) throws IOException {
        if (ObjectUtil.isEmpty(id)) {
            log.error("[EmailService: countVoipTalentNoticeEmail] count voip talent notice email error, parameter error, the talent id empty.");
            throw new CustomParameterizedException("The talent id is empty!");
        }

        HttpResponse response = httpService.get(getCountVoipTalentNoticeEmail(id), getRequestHeaders());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: countVoipTalentNoticeEmail] count voip talent notice email error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: countVoipTalentNoticeEmail @{}] count voip talent notice email error and response is null.}", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse countVoipContactNoticeEmail(Long id) throws IOException {
        if (ObjectUtil.isEmpty(id)) {
            log.error("[EmailService: countVoipContactNoticeEmail] count voip contact notice email error, parameter error, the contact id empty.");
            throw new CustomParameterizedException("The contact id is empty!");
        }

        HttpResponse response = httpService.get(getCountVoipContactNoticeEmail(id), getRequestHeaders());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: countVoipContacttNoticeEmail] count voip contact notice email error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: countVoipContactNoticeEmail @{}] count voip contact notice email error and response is null.}", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse getById(Long id) throws IOException {
        if (ObjectUtil.isEmpty(id)) {
            log.error("[EmailService: getById] search email campaigns by name from Emaily error, parameter error, the emaily campaign name cannot be empty.");
            throw new CustomParameterizedException("You can not get email by null id!");
        }

        HttpResponse response = httpService.get(getByIdUrl(id), getRequestHeaders());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: getById] search email campaigns by name from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: getById @{}] search email campaigns by name from Emaily error and response is null.}", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse getByIdIgnoreUserId(Long id) throws IOException {
        if (ObjectUtil.isEmpty(id)) {
            log.error("[EmailService: getByIdIgnoreUserId] search email campaigns by name from Emaily error, parameter error, the emaily campaign name cannot be empty.");
            throw new CustomParameterizedException("You can not get email by null id!");
        }

        HttpResponse response = httpService.get(getByIdUrlIgnoreUserId(id), getDefaultSystemUserToken());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: getByIdIgnoreUserId] search email campaigns by name from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: getByIdIgnoreUserId @{}] search email campaigns by name from Emaily error and response is null.}", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse deleteById(Long id) throws IOException {
        if (ObjectUtil.isEmpty(id)) {
            log.error("[EmailService: delteById] search email campaigns by name from Emaily error, parameter error, the emaily campaign name cannot be empty.");
            throw new CustomParameterizedException("You can not delete email by null id!");
        }

        HttpResponse response = httpService.delete(getByIdUrl(id), getRequestHeaders());

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: getById] search email campaigns by name from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: getById @{}] search email campaigns by name from Emaily error and response is null.}", SecurityUtils.getUserId());
        }
        return response;
    }

    private boolean validateFrom(String from) {
        return from != null && (from.toLowerCase().endsWith("@intelliprogroup.com") || from.toLowerCase().endsWith("@altomni.com") || from.toLowerCase().endsWith("@hitalentech.com"));
    }

    @Override
    public void sendHtmlMail(MailVM mailVM) throws IOException {
        log.info("[EmailService: sendHtmlMail @{}] request to send html mail with MailVM: {}", SecurityUtils.getUserId(), mailVM);

        //pre-validation
        if (ObjectUtil.isNull(mailVM)) {
            log.error("[EmailService: sendHtmlMail] request to send html mail but MailVM is empty.");
            throw new CustomParameterizedException("No mailVM!");
        }

        HttpUrl.Builder builder = HttpUrl.parse(emailAppProperties.getEmailyBaseUrl() + "/api/v1/send_html_mail/internal")
                .newBuilder();

        if (mailVM.getIsSystemEmail()) {
            builder.addQueryParameter("isSystemEmail", String.valueOf(true));
        } else {
            boolean checkGmailAliasBindingStatus = false;
            try {
                checkGmailAliasBindingStatus = gmailAliasService.needCheckGmailAliasStatus(mailVM.getFrom());
            } catch (Exception e) {
                log.warn("[EmailService: CampaignServiceImpl @{}]needCheckGmailAliasBindingStatus Exception, e: {}", SecurityUtils.getUserId(), e.getMessage());
            }
            builder.addQueryParameter("checkGmailAliasBindingStatus", String.valueOf(checkGmailAliasBindingStatus));
        }

        if (BooleanUtils.isTrue(mailVM.getTransactional())) {
            builder.addQueryParameter("transactional", String.valueOf(true));
        }

//        boolean checkGmailAliasBindingStatus = false;
//        if (!mailVM.getIsSystemEmail()) {
//            try {
//                checkGmailAliasBindingStatus = userEmailService.needCheckGmailAliasBindingStatus();
//            } catch (Exception e) {
//                log.warn("[EmailService: CampaignServiceImpl @{}]needCheckGmailAliasBindingStatus Exception, e: {}", SecurityUtils.getUserId(), e.getMessage());
//            }
//        }

//        String url = HttpUrl.parse(emailAppProperties.getEmailyBaseUrl() + "/api/v1/send_html_mail/internal")
//                .newBuilder()
//                .addQueryParameter("checkGmailAliasBindingStatus", String.valueOf(checkGmailAliasBindingStatus))
//                .build().toString();

        String url = builder.build().toString();

        Headers headers = mailVM.getIsSystemEmail() ? getDefaultSystemUserToken() : getRequestHeaders();
        String groupParamStr = JSON.toJSONString(mailVM, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(url, headers, groupParamStr);

        if (response != null) {
            log.info("[EmailService: sendHtmlMail @{}] send html mail response: {}", SecurityUtils.getUserId(), response);


            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: sendHtmlMail @{}] send html mail error, response code: {}, response message: {}", SecurityUtils.getUserId(), response.getCode(), response.getBody());

                if (response.getCode() == 400 && StringUtils.isNotEmpty(response.getBody())) {
                    try {
                        JSONObject resJson = JSONObject.parseObject(response.getBody());
                        if (resJson.containsKey("errorCode") && resJson.containsKey("errorMsg")) {
                            int errorCode = resJson.getIntValue("errorCode");
                            String errorMsg = resJson.getString("errorMsg");
                            log.error("[EmailService: sendHtmlMail @{}] send html mail to Emaily error, with gmail-alias error code: {}, and error message: {}", SecurityUtils.getUserId(), errorMsg, errorMsg);

                            if (errorCode == 462 || errorCode == 420) {
                                SecurityUtils.getCurrentUserLogin().ifPresent(loginUser -> gmailAliasService.removeGmailAliasRecord(loginUser.getEmail()));
                                throw new CustomParameterizedException(412, "Gmail-Alias Binding Exception", "the binding gmail account is expired or invalid, please binding again!", new HashMap<>());
                            } else {
                                throw new CustomParameterizedException(response.getBody());
                            }
                        } else {
                            throw new CustomParameterizedException(response.getBody());
                        }
                    } catch (JSONException e) {
                        throw new CustomParameterizedException(response.getBody());
                    }
                }
                throw new CustomParameterizedException(response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: sendHtmlMail @{}] create a email campaign to Emaily error and response is null", SecurityUtils.getUserId());
//            if (e instanceof CustomParameterizedException) {
//                throw e;
//            }
            throw new IOException("[EmailService: sendHtmlMail] Error when sending email!");
        }
    }

    private void prevalidation(MailVM mailVM) {

    }

    @Override
    public HttpResponse sendEmailBlast(EmailVM emailVM) throws IOException {
        if (ObjectUtil.isNull(emailVM)) {
            log.error("[EmailService: CampaignServiceImpl] create a email campaign to Emaily error, parameter error, the email campaign name cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
        if (Objects.isNull(emailVM.getIndustryId())) {
            emailVM.setIndustryId(109L); //default industry id
        }

        if (StringUtils.isEmpty(emailVM.getFrom()) || StringUtils.isEmpty(emailVM.getDisplayName())) {
            User user = userService.findUserById(SecurityUtils.getUserId()).getBody();
            if (Objects.isNull(user)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMPANY_SENDEMAILBLASTNOTFOUNDDEFAULTUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
            }
            if (StringUtils.isEmpty(emailVM.getFrom())) {
                emailVM.setFrom(user.getEmail());
            }
            if (StringUtils.isEmpty(emailVM.getDisplayName())) {
                emailVM.setDisplayName(user.getFirstName());
            }
        }

        String groupParamStr = JSON.toJSONString(emailVM, SerializerFeature.WriteDateUseDateFormat);
        String url = emailAppProperties.getEmailyBaseUrl() + "/api/v1/mail/send/internal";
        if (Objects.nonNull(emailVM.getId())) {
            url += "/" + emailVM.getId();
        }
        log.info("[EmailService: CampaignServiceImpl] send email blast with url: {} and body: {}", url, groupParamStr);
        HttpResponse response = httpService.post(url, getRequestHeaders(), groupParamStr);

        if (response != null) {

            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: CampaignServiceImpl] create a email campaign to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: CampaignServiceImpl @{}] create a email campaign to Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public List<EmailLogOverviewDTO> searchSentEmailAndCountReplied(MailSearchDTO mailSearchDTO) throws IOException {

        String url = emailAppProperties.getEmailyBaseUrl() + "/api/v1/mail/search-sent-count-replied";

        String groupParamStr = JSON.toJSONString(mailSearchDTO, SerializerFeature.WriteDateUseDateFormat);

        System.out.println(url);
        System.out.println(groupParamStr);

        HttpResponse response = httpService.post(url, getRequestHeaders(), groupParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[CampaignServiceImpl: searchSentEmailAndCountReplied] search sent emails and count replied with params: {} from Emaily error, response code: {}, response message: {}", mailSearchDTO, response.getCode(), response.getBody());
            } else {
                String responseBody = response.getBody();
                List<EmailLogOverviewDTO> list = JSON.parseArray(responseBody, EmailLogOverviewDTO.class);
                return list;
            }
        } else {
            log.error("[CampaignServiceImpl: searchSentEmailAndCountReplied @{}] search sent emails and count replied with params: {} from Emaily error and response is null.}", mailSearchDTO, SecurityUtils.getUserId());
        }
        throw new CustomParameterizedException("[CampaignServiceImpl: searchSentEmailAndCountReplied] call emaily serivce error");
    }

    @Override
    public List<EmailLogOverviewDTO> searchSentEmailsByUsers(MailSearchDTO mailSearchDTO) throws IOException {

        String url = emailAppProperties.getEmailyBaseUrl() + "/api/v1/mail/search-sent/by-users";

        String groupParamStr = JSON.toJSONString(mailSearchDTO, SerializerFeature.WriteDateUseDateFormat);

        HttpResponse response = httpService.post(url, getDefaultSystemUserToken(), groupParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[CampaignServiceImpl: searchSentEmailsByUsers] search sent emails by users with params: {} from Emaily error, response code: {}, response message: {}", mailSearchDTO, response.getCode(), response.getBody());
                if (HttpStatus.FORBIDDEN.value() == response.getCode()) {
                    return Collections.emptyList();
                }
            } else {
                String responseBody = response.getBody();
                List<EmailLogOverviewDTO> list = JSON.parseArray(responseBody, EmailLogOverviewDTO.class);
                return list;
            }
        } else {
            log.error("[CampaignServiceImpl: searchSentEmailsByUsers @{}] search sent emails by users with params: {} from Emaily error and response is null.}", mailSearchDTO, SecurityUtils.getUserId());
        }
        throw new CustomParameterizedException("[CampaignServiceImpl: searchSentEmailsByUsers] call emaily service error");
    }

    @Override
    public void syncVoipEmailRecordToEs(Long talentId, String subject) {
        // 1. 构建 _source 部分的 JSON 对象
        JSONObject source = new JSONObject();
        source.put("createdDate", Instant.now());
        source.put("@timestamp", Instant.now());
        source.put("talentId", talentId);
        source.put("createdBy", SecurityUtils.getUserId());

        // 2. 构建 changeFields 数组
        JSONArray changeFields = new JSONArray();
        changeFields.add(createChangeField(subject, "", "insert", "SendVoipEmail"));

        source.put("changeFields", changeFields);

        // 3. 将 JSON 对象转换为字符串
        String jsonBody = source.toJSONString();

        try {
            HttpResponse response = httpService.post(getTalentRecordSyncEsUrl(SecurityUtils.getTenantId()), jsonBody);

            if (response != null) {
                if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.error("[EmailService: syncVoipEmailRecordToEs @{}] sync voip email to talent record for talentId: {} with subject : {}, error, response code: {}, response message: {}", SecurityUtils.getUserId(), talentId, subject, response.getCode(), response.getBody());
                } else {
                    log.info("[EmailService: syncVoipEmailRecordToEs @{}] successfully sync voip email to talent record for talentId: {} with subject : {}", SecurityUtils.getUserId(), talentId, subject);
                }
            } else {
                log.error("[EmailService: syncVoipEmailRecordToEs @{}] sync voip email to talent record for talentId: {} with subject: {}, error and response is null", SecurityUtils.getUserId(), talentId, subject);
            }
        } catch (Exception e) {
            log.error("[EmailService: syncVoipEmailRecordToEs @{}] error when send voip email for talent: {} with subject: {}", SecurityUtils.getUserId(), talentId, subject, e);
        }
    }

    // 创建 changeFields 对象
    private JSONObject createChangeField(String changedTo, String changedFrom, String eventType, String key) {
        JSONObject obj = new JSONObject();
        obj.put("changedTo", changedTo);
        obj.put("changedFrom", changedFrom);
        obj.put("eventType", eventType);
        obj.put("key", key);
        return obj;
    }


}
