package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class SearchAudienceCategoryConverter extends AbstractAttributeConverter<SearchAudienceCategory, Integer> {
    public SearchAudienceCategoryConverter() {
        super(SearchAudienceCategory::toDbValue, SearchAudienceCategory::fromDbValue);
    }
}
