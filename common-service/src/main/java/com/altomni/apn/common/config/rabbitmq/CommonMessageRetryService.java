package com.altomni.apn.common.config.rabbitmq;//package com.altomni.apn.parser.config.rabbitmq;
//
//import com.alibaba.fastjson.JSONObject;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.core.MessageProperties;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import javax.annotation.Resource;
//import java.nio.charset.StandardCharsets;
//import java.util.Date;
//import java.util.Map;
//import java.util.Objects;
//
//public abstract class CommonMessageRetryService {
//
//    private final Logger log = LoggerFactory.getLogger(CommonMessageRetryService.class);
//
//    @Resource(name = "parserRabbitTemplate")
//    private RabbitTemplate rabbitTemplate;
//
//    private final static String MESSAGE_RETRY_INFO = "message_retry_info";
//
//    /**
//     * Initialization message
//     *
//     * @param message
//     */
//    public void initMessage(Message message) {
//        log.info("{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
//        try {
//            //Encapsulate message
//            MessageRetryDTO messageRetryDto = buildMessageRetryInfo(message);
//            log.info("Deserialize message:{}", messageRetryDto.toString());
//            prepareAction(messageRetryDto);
//        } catch (Exception e) {
//            log.error("Processing message exception, error message：", e);
//            sendDeadMessage(message);
//        }
//    }
//
//    /**
//     * Ready to execute
//     *
//     * @param retryDto
//     */
//    protected void prepareAction(MessageRetryDTO retryDto) {
//        try {
//            execute(retryDto);
//            doSuccessCallBack(retryDto);
//        } catch (Exception e) {
//            log.error("Current task execution exception, business data：{}", retryDto.toString(), e);
//            //Execution failed. Do you need to continue to retry
//            if (retryDto.checkRetryCount()) {
//                log.info("Retry message:{}", retryDto.toString());
//                retrySend(retryDto);
//            } else {
//                log.error("The maximum number of retries of the current task has been reached. Business data：{}", retryDto.toString(), e);
//                doFailCallBack(retryDto.setErrorMsg(e.getMessage()));
//            }
//        }
//    }
//
//    /**
//     * The task is executed successfully, and the service is called back (rewritten as needed)
//     *
//     * @param messageRetryDto
//     */
//    private void doSuccessCallBack(MessageRetryDTO messageRetryDto) {
//        try {
//            successCallback(messageRetryDto);
//        } catch (Exception e) {
//            log.error("Successful execution, callback exception, queue Description: {}, error reason：{}", messageRetryDto.getSourceDesc(), e);
//        }
//    }
//
//    /**
//     * Task execution failed, callback service (rewrite as needed)
//     *
//     * @param messageRetryDto
//     */
//    private void doFailCallBack(MessageRetryDTO messageRetryDto) {
//        try {
////            saveMessageRetryInfo(messageRetryDto.setErrorMsg(messageRetryDto.getErrorMsg()));
//            failCallback(messageRetryDto);
//        } catch (Exception e) {
//            log.error("Execution failed, callback exception, queue Description: {}, error reason：{}", messageRetryDto.getSourceDesc(), e);
//        }
//    }
//
//    /**
//     * Perform tasks
//     *
//     * @param messageRetryDto
//     */
//    protected abstract void execute(MessageRetryDTO messageRetryDto);
//
//    /**
//     * Successful callback
//     *
//     * @param messageRetryDto
//     */
//    protected abstract void successCallback(MessageRetryDTO messageRetryDto);
//
//    /**
//     * Failed callback
//     *
//     * @param messageRetryDto
//     */
//    protected abstract void failCallback(MessageRetryDTO messageRetryDto);
//
//    /**
//     * Build message compensation entity
//     * @param message
//     * @return
//     */
//    private MessageRetryDTO buildMessageRetryInfo(Message message){
//        //If the header contains a compensation message entity, it is returned directly
//        Map<String, Object> messageHeaders = message.getMessageProperties().getHeaders();
//        if(messageHeaders.containsKey(MESSAGE_RETRY_INFO)){
//            Object retryMsg = messageHeaders.get(MESSAGE_RETRY_INFO);
//            if(Objects.nonNull(retryMsg)){
//                return JSONObject.parseObject(String.valueOf(retryMsg), MessageRetryDTO.class);
//            }
//        }
//        //Automatically add business messages to compensation entities
//        MessageRetryDTO messageRetryDto = new MessageRetryDTO();
//        messageRetryDto.setBodyMsg(new String(message.getBody(), StandardCharsets.UTF_8));
//        messageRetryDto.setExchangeName(message.getMessageProperties().getReceivedExchange());
//        messageRetryDto.setRoutingKey(message.getMessageProperties().getReceivedRoutingKey());
//        messageRetryDto.setQueueName(message.getMessageProperties().getConsumerQueue());
//        messageRetryDto.setCreateTime(new Date());
//        return messageRetryDto;
//    }
//
//    /**
//     * Abnormal message re warehousing
//     * @param retryDto
//     */
//    private void retrySend(MessageRetryDTO retryDto){
//        //Put the compensation message entity into the header, and the original message content remains unchanged
//        MessageProperties messageProperties = new MessageProperties();
//        messageProperties.setContentType(MessageProperties.CONTENT_TYPE_JSON);
//        messageProperties.setHeader(MESSAGE_RETRY_INFO, JSONObject.toJSON(retryDto));
//        Message message = new Message(retryDto.getBodyMsg().getBytes(), messageProperties);
//        rabbitTemplate.convertAndSend(retryDto.getExchangeName(), retryDto.getRoutingKey(), message);
//    }
//
//    /**
//     *
//     * @param
//     */
////    private void saveMessageRetryInfo(MessageRetryDTO retryDto){
////        try {
//////            Consumer persistent messages
//////            mongoTemplate.save(retryDto, MESSAGE_RETRY_INFO);
////        } catch (Exception e){
////            log.error("Exception message persistence failed, message data：{}", retryDto.toString(), e);
////        }
////    }
//
//    private void sendDeadMessage(Message message) {
//        rabbitTemplate.convertAndSend(DeadQueueConfig.DEAD_EXCHANGE, DeadQueueConfig.ROUTING_KEY, message);
//    }
//
//    public void sendDeadMessage(MessageRetryDTO retryDto) {
//        MessageProperties messageProperties = new MessageProperties();
//        messageProperties.setContentType(MessageProperties.CONTENT_TYPE_JSON);
//        messageProperties.setHeader(MESSAGE_RETRY_INFO, JSONObject.toJSON(retryDto));
//        Message message = new Message(retryDto.getBodyMsg().getBytes(), messageProperties);
//        rabbitTemplate.convertAndSend(DeadQueueConfig.DEAD_EXCHANGE, DeadQueueConfig.ROUTING_KEY, message);
//    }
//}
