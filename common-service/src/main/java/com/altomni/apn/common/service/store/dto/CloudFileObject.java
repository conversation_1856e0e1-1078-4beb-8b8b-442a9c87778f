package com.altomni.apn.common.service.store.dto;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class CloudFileObject {

    private byte[] content;

    private String contentType;

    private String fileName;

    private String s3Link;

    private long contentLength;

    private String folder;

    private String key;

    public CloudFileObject() {}

    public CloudFileObject(byte[] content, String contentType, String fileName) {
        this.content = content;
        this.contentType = contentType;
        this.fileName = fileName;
    }

    public CloudFileObject(byte[] content, String contentType, String fileName, String s3Link) {
        this.content = content;
        this.contentType = contentType;
        this.fileName = fileName;
        this.s3Link = s3Link;
    }

    public CloudFileObject(byte[] content, String contentType, String fileName, String s3Link, long contentLength, String folder, String key) {
        this.content = content;
        this.contentType = contentType;
        this.fileName = fileName;
        this.s3Link = s3Link;
        this.contentLength = contentLength;
        this.folder = folder;
        this.key = key;
    }
}
