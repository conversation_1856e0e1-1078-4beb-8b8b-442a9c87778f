package com.altomni.apn.common.web.rest.email;

import com.altomni.apn.common.domain.email.enumeration.GmailAliasBindingStatus;
import com.altomni.apn.common.service.email.UserEmailService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.web.rest.email.vm.AlternateEmailVM;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Instant;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-EmailService"})
@RestController
@RequestMapping("/api/v3/user-setting")
public class UserEmailResource {

    private final Logger log = LoggerFactory.getLogger(UserEmailResource.class);

    private static final String ENTITY_NAME = "userEmailResource";

    @Resource
    private UserEmailService userEmailService;

    @Deprecated(since = "sso")
    @GetMapping(value = "/binding-status", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<GmailAliasBindingStatus> getCurrentUserEmailSetting() {
        log.info("[UserEmailResource: getCurrentUserEmailSetting] REST request to getCurrentUserEmailSetting for user: {}", SecurityUtils.getUserId());

        GmailAliasBindingStatus res = userEmailService.getBindingStatusForCurrentUser();

        return ResponseEntity.ok(res);
    }

    @Deprecated(since = "sso")
    @PostMapping(value = "/request-binding", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> requestToBindingGmailAliasForCurrentUser() {
        log.info("[UserEmailResource: requestToBindingGmailAliasForCurrentUser] REST request to BindingGmailAliasForCurrentUser for user: {}", SecurityUtils.getUserId());

        String res = userEmailService.requestToBindingGmailAliasForCurrentUser();

        return ResponseEntity.ok(res);
    }

    @Deprecated(since = "sso")
    @PostMapping(value = "/confirm-binding", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> confirmBindingGmailAliasForCurrentUser() {
        log.info("[UserEmailResource: confirmBindingGmailAliasForCurrentUser] REST request to confirmBindingGmailAliasForCurrentUser for user: {}", SecurityUtils.getUserId());

        userEmailService.confirmBindingGmailAliasForCurrentUser();

        return ResponseEntity.ok().build();
    }


    @PostMapping(value = "/check-recent-binding-status", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> runTaskToCheckRecentConfirmedBindingStatus() {
        log.info("[UserEmailResource: runTaskToCheckRecentConfirmedBindingStatus] REST request to run task to check recent confirmed binding at: {} by: {}", Instant.now().toString(), SecurityUtils.getUserId());

        userEmailService.scheduleCheckRecentConfirmedBindingStatus();

        return ResponseEntity.ok().build();
    }

    @PostMapping(value = "/add-alternate-email", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> addAlternateEmail(@RequestBody AlternateEmailVM alternateEmailVM) {
        log.info("[UserEmailResource: addAlternateEmail] REST request add alternate email : {}", alternateEmailVM);

        userEmailService.addAlternateEmail(alternateEmailVM);

        return ResponseEntity.ok().build();
    }
}
