package com.altomni.apn.common.domain.reportsubscription;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "report_subscription_recipient")
public class ReportSubscriptionRecipient {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "subscription_id")
    private Long subscriptionId;

    @Column(name = "user_id")
    private Long userId;

}
