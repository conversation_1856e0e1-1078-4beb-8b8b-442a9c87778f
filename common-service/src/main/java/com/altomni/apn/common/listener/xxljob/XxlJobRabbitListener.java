package com.altomni.apn.common.listener.xxljob;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.xxljob.MyXxlJobHandlerManager;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.message.XxlJobFailRecord;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.xxljob.XxlJobBaseDTO;
import com.altomni.apn.common.repository.message.XxlJobFailRecordRepository;
import com.altomni.apn.common.repository.xxljob.XxlJobRelationRepository;
import com.altomni.apn.common.service.cache.redis.RedisService;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class XxlJobRabbitListener {

    @Resource(name = "xxlJobRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Value("${application.xxl-job-mq.exchange}")
    private String exchange;

    @Value("${application.xxl-job-mq.routingKey}")
    private String routingKey;

    @Value("${application.notification.lark.xxl-job.webhookKey:jPHhuEgZSNTVo4aZ5NiXyg}")
    private String webhookKey;

    @Value("${application.notification.lark.xxl-job.webhookUrl:https://open.larksuite.com/open-apis/bot/v2/hook/8b0f9662-59d4-48d3-97ce-72b06b11893c}")
    private String webhookUrl;

    @Resource
    private XxlJobFailRecordRepository xxlJobFailRecordRepository;

    @Resource
    private XxlJobRelationRepository xxlJobRelationRepository;

    @Resource
    private RedisService redisService;

    @Value("${application.xxl-job-redis-key:xxl-job-redis-key}")
    private String xxlJobRedisKey;

    @Value("${application.xxl-job-redis-key-expire:60}")
    private Long xxlJobRedisKeyExpire;


    @RabbitListener(containerFactory = "xxlJobFactory", queues = {"${application.xxl-job-mq.queue}"})
    @RabbitHandler
    public void process(Message message) {
        log.info("[XxlJobRabbitListener] Received message: {}，Business data：{}", message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        XxlJobBaseDTO xxlJobBaseDTO = JSONUtil.toBean(json, XxlJobBaseDTO.class);
        //幂等校验
        if (Objects.equals(redisService.setNxAndExpire(xxlJobRedisKey + ":" + xxlJobBaseDTO.getXxlJobId(), xxlJobBaseDTO.getXxlJobId() + "", xxlJobRedisKeyExpire), 0L)) {
            log.error("[xxlJobHandler] xxlJobRelation 幂等校验 {}s 内重复调用, xxlJobId = {}", xxlJobRedisKeyExpire, xxlJobBaseDTO.getXxlJobId());
            return;
        }
        XxlJobRelationTypeEnum xxlJobRelationTypeEnum = xxlJobBaseDTO.getXxlJobType();
        if (XxlJobRelationTypeEnum.RETRY_FAIL_XXL_JOB == xxlJobRelationTypeEnum) {
            // fail xxl job 任务重试
            log.info("retry fail xxl job task is start");
            doHandlerForRetryFailXxlJob();
            log.info("retry fail xxl job task is end");
            return;
        }
        // 团队 候选人逾期入职一定是任务,不是自定义添加的
        if (xxlJobRelationTypeEnum != XxlJobRelationTypeEnum.TEAM_OVERDUE_ONBOARD_WARN) {
            Integer xxlJobId = xxlJobBaseDTO.getXxlJobId();
            XxlJobRelation xxlJobRelation = xxlJobRelationRepository.findXxlJobRelationByXxlJobId(xxlJobId);
            if (xxlJobRelation == null) {
                log.error("[xxlJobHandler] xxlJobRelation is error, xxlJobId = {}", xxlJobId);
                return;
            }
            // 从保存的记录中获取需要的入参
            json = xxlJobRelation.getXxlJobParam();
            xxlJobBaseDTO = JSONUtil.toBean(json, XxlJobBaseDTO.class);
            xxlJobRelationTypeEnum = xxlJobBaseDTO.getXxlJobType();
        }
        try {
            log.info("[xxlJobHandler] @{}}: tenantId = {}, xxlJobId = {} is start", SecurityUtils.getUserId(), SecurityUtils.getTenantId(), xxlJobBaseDTO.getXxlJobId());
            // 定时任务使用客户端 token
            LoginUtil.simulateLoginWithClient();
//            if (StrUtil.isNotBlank(xxlJobBaseDTO.getToken())) {
//                //这个地方有大坑,当存在token的时候,接口被开放了他也会解析token是否过期,如果过期则会失败
//                LoginUtil.setSecurityContext(xxlJobBaseDTO.getToken());
//            }
            if (xxlJobRelationTypeEnum == null) {
                log.error("[xxlJobHandler] xxlJobHandler is error, type is null, param = {}", json);
                return;
            }
            Method method = MyXxlJobHandlerManager.getJobHandlerMap().get(xxlJobRelationTypeEnum);
            if (method == null) {
                log.error("[xxlJobHandler] xxlJobHandler is error, method is null, param = {}", json);
                return;
            }
            Object object = SpringUtil.getBean(method.getDeclaringClass());
            Class<?>[] classes = method.getParameterTypes();
            Object[] params = getObjectsFromJsonByClasses(json, classes);
            method.invoke(object, params);
            log.info("[xxlJobHandler] @{}}: tenantId = {}, xxlJobId = {} is success", SecurityUtils.getUserId(), SecurityUtils.getTenantId(), xxlJobBaseDTO.getXxlJobId());
        } catch (Exception e) {
            log.error("[xxlJobHandler] xxlJobHandler is error, error message：", e);
            XxlJobFailRecord xxlJobFailRecord = new XxlJobFailRecord();
            xxlJobFailRecord.setXxlJobId(xxlJobBaseDTO.getXxlJobId());
            xxlJobFailRecord.setType(xxlJobBaseDTO.getXxlJobType());
            xxlJobFailRecord.setRequestParam(json);
            xxlJobFailRecord.setResponseMessage(ExceptionUtils.getStackTrace(e));
            xxlJobFailRecordRepository.saveAndFlush(xxlJobFailRecord);
            NotificationUtils.sendAlertToLark(webhookKey, webhookUrl, "xxl-job message consumption failed, please handle manually, xxlJobId = " + xxlJobBaseDTO.getXxlJobId());
        } finally {
            LoginUtil.clearSecurityContext();
        }
    }

    private void doHandlerForRetryFailXxlJob() {
        //查询所有xxl-job fail record 的记录
        List<XxlJobFailRecord> xxlJobFailRecordList = xxlJobFailRecordRepository.findAll();
        //删除所有xxl-job fail record 的记录
        xxlJobFailRecordRepository.deleteAll();
        //发送所有需要重试的xxl-job 的记录
        xxlJobFailRecordList.forEach(xxlJobFailRecord -> rabbitTemplate.convertAndSend(exchange, routingKey, new Message(xxlJobFailRecord.getRequestParam().getBytes(), new MessageProperties()), new CorrelationData(String.valueOf(xxlJobFailRecord.getXxlJobId()))));
    }

    private Object[] getObjectsFromJsonByClasses(String json, Class<?>[] classes) {
        Object[] objects = new Object[classes.length];
        for (int i = 0; i < classes.length; i++) {
            objects[i] = JSONUtil.toBean(json, classes[i]);
        }
        return objects;
    }

}
