package com.altomni.apn.common.config.rabbitmq;

import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

@Configuration
@RefreshScope
public class XxlJobRabbitMqConfig {

    @Value("${application.xxl-job-mq.host}")
    private String host;

    @Value("${application.xxl-job-mq.port}")
    private Integer port;

    @Value("${application.xxl-job-mq.virtual-host:/}")
    private String virtualHost;

    @Value("${application.xxl-job-mq.username}")
    private String username;

    @Value("${application.xxl-job-mq.password}")
    private String password;

    @Bean(name = "xxlJobConnectionFactory")
    public ConnectionFactory emailConnectionFactory() {
        return connectionFactory (host, port, virtualHost, username, password);
    }

    public CachingConnectionFactory connectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        return connectionFactory;
    }


    @Bean(name = "xxlJobFactory")
    public SimpleRabbitListenerContainerFactory secondFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("xxlJobConnectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(1);
        factory.setPrefetchCount(1);
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "xxlJobRabbitTemplate")
    @Primary
    public RabbitTemplate xxlJobRabbitTemplate(
            @Qualifier("xxlJobConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate xxlJobRabbitTemplate = new RabbitTemplate(connectionFactory);
        xxlJobRabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return xxlJobRabbitTemplate;
    }

}
