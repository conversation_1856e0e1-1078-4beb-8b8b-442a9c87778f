package com.altomni.apn.common.service.store.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.config.env.store.UploadInfoConfig;
import com.altomni.apn.common.domain.parser.enumeration.ParseType;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.store.CopyObjectDto;
import com.altomni.apn.common.dto.store.UploadUrlDto;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.repository.talent.ResumeRepository;
import com.altomni.apn.common.service.cache.redis.RedisService;
import com.altomni.apn.common.service.store.StoreService;
import com.altomni.apn.common.service.store.dto.UploadInfo;
import com.altomni.apn.common.service.store.utils.Credentials;
import com.altomni.apn.common.service.store.utils.PostPolicy;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.FileExtensionUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.store.DisplayLinkVo;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.awscore.defaultsmode.DefaultsMode;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.S3ClientBuilder;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest;
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.time.Instant;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;

import static com.altomni.apn.common.config.constants.RedisConstants.*;
import static com.altomni.apn.common.config.env.store.UploadInfoConfig.bucketIsPublicMap;
import static com.altomni.apn.common.config.env.store.UploadInfoConfig.bucketRegionMap;


@Slf4j
@Service
@RefreshScope
public class StoreServiceImpl implements StoreService {

    /**
     * bucket
     */
    @Value("${application.storeService.resume.bucket}")
    private String resumeBucket;

    @Value("${application.storeService.jd.bucket}")
    private String jdBucket;

    /**
     * access key
     */
    @Value("${application.aws.accessKey}")
    private String accessKey;

    /**
     * secret key
     */
    @Value("${application.aws.secretKey}")
    private String secretKey;

    @Value("${application.sqsService.region}")
    private String sqsServiceRegion;

    @Value("${application.sqsService.queueName}")
    private String sqsQueueName;

    @Resource
    private UploadInfoConfig uploadInfoConfig;

    public static final String CONTENT_TYPE_MSWORD = "application/msword";

    public static final List<String> RESUME_CONTENT_TYPE_NEED_TO_RESET = new ArrayList<>(Arrays.asList("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/zip", "text/rtf"
            , "application/vnd.openxmlformats-officedocument.wordprocessingml.template", "application/vnd.oasis.opendocument.text"
            , "message/rfc822", "multipart/related", "application/kswps", "application/wps-office.docx", "application/wps-writer"));


    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

    @Resource
    private RedisService redisService;

    private static final int REDIS_EXPIRE_TIME = 600; //10min


    private S3Client initS3(String accessKey, String secretKey, String region) {
        return S3Client.builder()
                .defaultsMode(DefaultsMode.STANDARD)
                .credentialsProvider(() -> AwsBasicCredentials.create(accessKey, secretKey))
                .region(Region.of(region))
                .build();
    }

    @Override
    public void s3Upload(String key, MultipartFile file, String uploadType) {
        if (file.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_FILENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(uploadType);
        try (S3Client s3Client = initS3(accessKey, secretKey, getRegionByBucket(uploadInfo.getBucket()))){
            String contentDisposition = "filename=\"" + URLEncoder.encode(file.getOriginalFilename(), "utf-8") + "\"";
            String contentType = file.getContentType();
            //if contentType is "text/plain", set PLAIN_TEXT_UTF_8 Solve Chinese garbled code
            if (Constants.CONTENT_TYPE_TXT.equals(contentType)) {
                contentType = MediaType.TEXT_PLAIN_VALUE;
            }
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .contentDisposition(contentDisposition)
                    .contentLength(file.getSize())
                    .contentType(contentType)
                    .bucket(uploadInfo.getBucket())
                    .key(StrUtil.isBlank(uploadInfo.getFolder())? key: uploadInfo.getFolder() + key)
                    .build();
            PutObjectResponse putObjectResponse = s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(file.getInputStream(), file.getSize()));
            if (putObjectResponse.sdkHttpResponse().isSuccessful()) {
                log.info("s3 upload is success");
            } else {
                log.info("s3 upload is fail");
            }
        } catch (Exception e) {
            log.info("s3 upload is fail, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    /**
     * 根据 bucketName 获取 bucket 是否是 public, 同region一样保存到内存中
     * @param bucketName
     * @return isPublic
     */
    private boolean isPublic(String bucketName) {
        if (bucketIsPublicMap.containsKey(bucketName)) {
            return bucketIsPublicMap.get(bucketName);
        }
        try (S3Client s3Client = initS3(accessKey, secretKey, getRegionByBucket(bucketName))) {
            GetPublicAccessBlockRequest getPublicAccessBlockRequest = GetPublicAccessBlockRequest.builder()
                    .bucket(bucketName)
                    .build();
            s3Client.getPublicAccessBlock(getPublicAccessBlockRequest);
            log.info("{} is private", bucketName);
            bucketIsPublicMap.put(bucketName, false);
            return false;
        } catch (S3Exception e) {
            log.info("{} is public", bucketName);
            bucketIsPublicMap.put(bucketName, true);
            return true;
        }
    }

    /**
     * 根据 bucketName 去获取region 的方法，同时会保存在 内存的map中，当 nacos 发送变化时会清空Map
     * @param bucketName
     * @return region
     */
    private String getRegionByBucket(String bucketName) {
        if (bucketRegionMap.containsKey(bucketName)) {
            return bucketRegionMap.get(bucketName);
        }
        try (S3Client s3Client = initS3(accessKey, secretKey, "us-east-2")) {
            GetBucketLocationRequest getBucketLocationRequest = GetBucketLocationRequest.builder()
                    .bucket(bucketName)
                    .build();
            String region = s3Client.getBucketLocation(getBucketLocationRequest).locationConstraintAsString();
            bucketRegionMap.put(bucketName, region);
            log.info("getRegionByBucketName, bucketName = {}, region = {}", bucketName, region);
            return region;
        } catch (Exception e) {
            log.info("s3 get region by bucket name is fail, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    /**
     * company module
     */
    @Override
    public String companyDownloadString(String s3Link) {
        try {
            String[] strings = StrUtil.split(s3Link, "/");
            String key = strings[strings.length - 1];
            CloudFileObjectMetadata cloudFileObject = getCommonFileByTypeFromS3(key, UploadTypeEnum.DOC.getKey());
            return new String(cloudFileObject.getContent(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("company download string is error , message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("Error during file download.");
        }
    }

    @Override
    public String companyS3Upload(String content, String contentType) {
        String key = SecureUtil.md5(content);
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(UploadTypeEnum.DOC.getKey());
        try (S3Client s3Client = initS3(accessKey, secretKey, getRegionByBucket(uploadInfo.getBucket()))) {
            byte[] contentAsBytes = content.getBytes(StandardCharsets.UTF_8);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(contentAsBytes);
            String contentDisposition = "filename=\"" + URLEncoder.encode(key, StandardCharsets.UTF_8) + "\"";
            long contentLength = contentAsBytes.length;
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .contentDisposition(contentDisposition)
                    .contentLength(contentLength)
                    .contentType(contentType)
                    .bucket(uploadInfo.getBucket())
                    .key(key)
                    .build();
            PutObjectResponse putObjectResponse = s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(inputStream, contentLength));
            if (putObjectResponse.sdkHttpResponse().isSuccessful()) {
                log.info("s3 upload is success");
            } else {
                log.info("s3 upload is fail");
            }
        } catch (Exception e) {
            log.error("company s3 upload is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
        return getCommonUrlFromS3(key, UploadTypeEnum.DOC.getKey(), null);
    }

    @Override
    public String S3ContentUpload(UploadTypeEnum uploadTypeEnum, String contentType, String content, String key){
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(uploadTypeEnum.getKey());
        try (S3Client s3Client = initS3(accessKey, secretKey, getRegionByBucket(uploadInfo.getBucket()))) {
            byte[] contentAsBytes = content.getBytes(StandardCharsets.UTF_8);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(contentAsBytes);
            String contentDisposition = "filename=\"" + URLEncoder.encode(key, StandardCharsets.UTF_8) + "\"";
            long contentLength = contentAsBytes.length;
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .contentDisposition(contentDisposition)
                    .contentLength(contentLength)
                    .contentType(contentType)
                    .bucket(uploadInfo.getBucket())
                    .key(StrUtil.isBlank(uploadInfo.getFolder())? key: uploadInfo.getFolder() + key)
                    .build();
            PutObjectResponse putObjectResponse = s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(inputStream, contentLength));
            if (putObjectResponse.sdkHttpResponse().isSuccessful()) {
                log.info("s3 upload is success");
            } else {
                log.info("s3 upload is fail");
            }
        } catch (Exception e) {
            log.error("company s3 upload is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
        return getCommonUrlFromS3(key, uploadTypeEnum.getKey(), null);
    }

    @Override
    public String S3ContentUpload(UploadTypeEnum uploadTypeEnum, String contentType, byte[] contentAsBytes, String key){
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(uploadTypeEnum.getKey());
        try (S3Client s3Client = initS3(accessKey, secretKey, getRegionByBucket(uploadInfo.getBucket()))) {
            ByteArrayInputStream inputStream = new ByteArrayInputStream(contentAsBytes);
            String contentDisposition = "filename=\"" + URLEncoder.encode(key, StandardCharsets.UTF_8) + "\"";
            long contentLength = contentAsBytes.length;
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                    .contentDisposition(contentDisposition)
                    .contentLength(contentLength)
                    .contentType(contentType)
                    .bucket(uploadInfo.getBucket())
                    .key(StrUtil.isBlank(uploadInfo.getFolder())? key: uploadInfo.getFolder() + key)
                    .build();
            PutObjectResponse putObjectResponse = s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(inputStream, contentLength));
            if (putObjectResponse.sdkHttpResponse().isSuccessful()) {
                log.info("s3 upload is success");
            } else {
                log.info("s3 upload is fail");
            }
        } catch (Exception e) {
            log.error("company s3 upload is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
        return getCommonUrlFromS3(key, uploadTypeEnum.getKey(), null);
    }
    @Override
    public String S3Upload(UploadTypeEnum uploadTypeEnum, String contentType, String content) {
        String key = SecureUtil.md5(content);
        return S3ContentUpload(uploadTypeEnum, contentType, content, key);
    }

    private String getFileNameFromDisposition(String contentDisposition) {
        String filename = null;
        if (contentDisposition != null) {
            String[] parts = contentDisposition.split(";");
            for (String part : parts) {
                if (part.trim().startsWith("filename")) {
                    String[] filenameParts = part.split("=");
                    filename = filenameParts[1].trim();
                    break;
                }
            }
        }
        return StrUtil.replace(filename, "\"", "");
    }

    @Override
    public Map<String, String> getPresignedUploadUrlFromS3WithPostPolicy(ParseType parseType, String key, String filename, String contentType, Long lowerLimit, Long upperLimit) {
        String bucket;
        String region;
        if (ParseType.RESUME.equals(parseType)) {
            bucket = resumeBucket;
        } else {
            bucket = jdBucket;
        }
        region = getRegionByBucket(bucket);
        ZonedDateTime expirationTime = ZonedDateTime.now().plus(15, ChronoUnit.MINUTES);
        PostPolicy postPolicy = new PostPolicy(bucket, expirationTime);
        postPolicy.addContentLengthRangeCondition(lowerLimit,upperLimit); // 1024 1kb, 10485760 10MB
        postPolicy.addEqualsCondition("key", key);
        // to allow front-end post s3 metadata {Content-Disposition: filename="test.png"} in form data
        postPolicy.addStartsWithCondition("Content-Disposition", "");
        // to allow front-end post s3 metadata {Content-Type}
        postPolicy.addEqualsCondition("Content-Type", contentType);
        Credentials credentials = new Credentials(accessKey, secretKey, null);
        try {
            return postPolicy.formData(credentials, region);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("[Error when signing POST uploading url with policy]", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    @Override
    public StoreGetUploadUrlVO getPresignedCommonUploadUrlFromS3WithPostPolicy(UploadUrlDto uploadUrlDto) {
        StoreGetUploadUrlVO storeGetUploadUrlVo = new StoreGetUploadUrlVO();
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(uploadUrlDto.getUploadType());
        String uuid = StrUtil.isBlank(uploadInfo.getFolder())? uploadUrlDto.getUuid(): uploadInfo.getFolder() + uploadUrlDto.getUuid();
        //禁猎客户- 解析公司合同文件
        if(UploadTypeEnum.CONTRACT.getKey().equals(uploadUrlDto.getUploadType())) {
            //1.初始化redis
            initializeRedisForContractParser(uploadUrlDto.getUuid(), uploadUrlDto.getFileName(), uploadUrlDto.getContentType(), null);
        }
        ZonedDateTime expirationTime = ZonedDateTime.now().plus(uploadInfo.getSignatureDuration(), ChronoUnit.MINUTES);
        PostPolicy postPolicy = new PostPolicy(uploadInfo.getBucket(), expirationTime);
        postPolicy.addContentLengthRangeCondition(uploadInfo.getMinSizeInBytes(), uploadInfo.getMaxSizeInBytes()); // 1024 1kb, 20480000 20MB
        postPolicy.addEqualsCondition("key", uuid);
        // to allow front-end post s3 metadata {Content-Disposition: filename="test.png"} in form data
        postPolicy.addStartsWithCondition("Content-Disposition", "");
        // to allow front-end post s3 metadata {Content-Type}
        postPolicy.addEqualsCondition("Content-Type", uploadUrlDto.getContentType());
        // 设置数据到 metadata 里面,eq 表示必选和前面的时候传值一样
        Map<String, String> eqMap = uploadUrlDto.getEqMetaDataMap();
        if (CollUtil.isNotEmpty(eqMap)) {
            for (Map.Entry<String, String> entry : eqMap.entrySet()) {
                postPolicy.addEqualsCondition("x-amz-meta-" + entry.getKey(), entry.getValue());
            }
        }
        Credentials credentials = new Credentials(accessKey, secretKey, null);
        try {
            Map<String, String> resultMap = postPolicy.formData(credentials, getRegionByBucket(uploadInfo.getBucket()));
            storeGetUploadUrlVo.setPostPolicy(resultMap);
            storeGetUploadUrlVo.setUuid(uuid);
            if (isPublic(uploadInfo.getBucket())) {
                // public 的情况下直接返回永久链接,前端无需在根据 key 来获取url
                storeGetUploadUrlVo.setS3Link(resultMap.get("url") + "/" + uuid);
            }
            return storeGetUploadUrlVo;
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("[Error when signing POST uploading url with policy]", e);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    public void initializeRedisForContractParser(String uuid, String filename, String contentType, Integer skipNPages) {
        String key = CONTRACT_DATA_PRE + uuid + CONTRACT_DATA_KEY_METADATA;
        String statusKey = CONTRACT_DATA_PRE + uuid + CONTRACT_DATA_KEY_STATUS;
        Boolean exist = redisService.exists(key);
        if (BooleanUtils.isTrue(exist)) {
            log.info("[StoreServiceImpl: uuid: {}, initializeRedisForContractParser] redis already exists key: {} when init for fileName: {} with skipNPages: {}", uuid, key, filename, skipNPages);
            return;
        }

        Map<String, String> set = new HashMap<>();
        set.put("lastUpdateTime", StrUtil.toString(Instant.now()));
        set.put("filename", filename);
        if(contentType != null) {
            set.put("ContentType", contentType);
        }
        if(skipNPages != null) {
            set.put("SkipNPages", StrUtil.toString(skipNPages));
        }
        redisService.hset(key, set, REDIS_EXPIRE_TIME);
        redisService.set(statusKey, ParseStatus.UPLOADING.name(), REDIS_EXPIRE_TIME);
        log.info("[StoreServiceImpl: uuid: {}, initializeRedisForContractParser] Write metadata(filename: {}, contentType: {}, skipNPages: {}) into redis with key: {} and ttl: {}", uuid, filename, contentType, skipNPages, key, REDIS_EXPIRE_TIME);
    }

    /**
     * 获取携带 file byte[] 的对象
     * @param key
     * @param uploadType
     * @return
     */
    @Override
    public CloudFileObjectMetadata getCommonFileByTypeFromS3(String key, String uploadType) {
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(uploadType);
        String region = getRegionByBucket(uploadInfo.getBucket());
        try (S3Client amazonS3 = initS3(accessKey, secretKey, region)) {
            String fileName = null;
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .key(StrUtil.isBlank(uploadInfo.getFolder())? key: uploadInfo.getFolder() + key)
                    .bucket(uploadInfo.getBucket()).build();
            ResponseBytes<GetObjectResponse> responseResponseBytes = amazonS3.getObjectAsBytes(getObjectRequest);
            byte[] byteArray = responseResponseBytes.asByteArray();
            GetObjectResponse object = responseResponseBytes.response();
            String contentType = object.contentType();
            if (StrUtil.isNotBlank(object.contentDisposition())) {
                fileName = getFileNameFromDisposition(object.contentDisposition());
            }
            String s3Link = getCommonUrlFromS3(key, uploadType, null);
            CloudFileObjectMetadata cloudFileObject = new CloudFileObjectMetadata();
            cloudFileObject.setFileName(fileName);
            cloudFileObject.setContentType(contentType);
            cloudFileObject.setS3Link(s3Link);
            cloudFileObject.setFolder(uploadInfo.getFolder());
            cloudFileObject.setKey(key);
            cloudFileObject.setContent(byteArray);
            cloudFileObject.setBucketName(uploadInfo.getBucket());
            cloudFileObject.setRegion(region);
            cloudFileObject.setContentLength(object.contentLength());
            cloudFileObject.setUserMetadata(object.metadata());
            return cloudFileObject;
        } catch (NoSuchKeyException suchKeyException) {
            return null;
        } catch (Exception e) {
            log.error("getCommonFileByTypeFromS3 is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    @Override
    public String getCommonUrlFromS3(String key, String uploadType, String contentType) {
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(uploadType);
        String region = getRegionByBucket(uploadInfo.getBucket());
        try (S3Client s3Client = initS3(accessKey, secretKey, region);
             S3Presigner presigner = S3Presigner.builder().region(Region.of(region))
                     .credentialsProvider(() -> AwsBasicCredentials.create(accessKey, secretKey)).build()) {
            // 先检查文件是否存在
            HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                    .bucket(uploadInfo.getBucket())
                    .key(getS3FolderKey(uploadInfo, key, uploadType))
                    .build();
            s3Client.headObject(headObjectRequest);

            if (isPublic(uploadInfo.getBucket())) {
                //公开bucket 直接返回永久地址
                GetUrlRequest request = GetUrlRequest.builder()
                        .bucket(uploadInfo.getBucket())
                        .key(getS3FolderKey(uploadInfo, key, uploadType))
                        .build();
                return s3Client.utilities().getUrl(request).toExternalForm();
            } else {
                //私有的bucket 返回临时地址
                GetObjectRequest.Builder builder = GetObjectRequest.builder()
                        .bucket(uploadInfo.getBucket())
                        .key(getS3FolderKey(uploadInfo, key, uploadType));
                if (StrUtil.isNotBlank(contentType)) {
                    // 添加 contentType 用于前端展示使用
                    builder.responseContentType(contentType);
                }
                GetObjectRequest getObjectRequest = builder.build();
                GetObjectPresignRequest getObjectPresignRequest = GetObjectPresignRequest.builder()
                        // 默认的时间为10分钟, 可以在配置中配置
                        .signatureDuration(Duration.ofMinutes(uploadInfo.getSignatureDuration()))
                        .getObjectRequest(getObjectRequest)
                        .build();
                PresignedGetObjectRequest presignedRequest = presigner.presignGetObject(getObjectPresignRequest);
                return presignedRequest.url().toString();
            }
        } catch (NoSuchKeyException suchKeyException) {
            return null;
        } catch (Exception e) {
            // The call was transmitted successfully, but Amazon S3 couldn't process
            // it, so it returned an error response.
            log.error("getCommonUrlFromS3 is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    /**
     * 获取不带 file byte[]的对象
     * @param key
     * @param uploadType
     * @return
     */
    @Override
    public CloudFileObjectMetadata getCommonFileWithoutFileByteByTypeFromS3(String key, String uploadType) {
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(uploadType);
        String region = getRegionByBucket(uploadInfo.getBucket());
        try (S3Client amazonS3 = initS3(accessKey, secretKey, region)) {
            String fileName = null;
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .key(getS3FolderKey(uploadInfo, key, uploadType))
                    .bucket(uploadInfo.getBucket()).build();
            GetObjectResponse objectResponse = amazonS3.getObject(getObjectRequest).response();
            if (StrUtil.isNotBlank(objectResponse.contentDisposition())) {
                fileName = getFileNameFromDisposition(objectResponse.contentDisposition());
            }
            String s3Link = getCommonUrlFromS3(key, uploadType, null);
            CloudFileObjectMetadata cloudFileObject = new CloudFileObjectMetadata();
            cloudFileObject.setFileName(fileName);
            cloudFileObject.setContentType(objectResponse.contentType());
            cloudFileObject.setS3Link(s3Link);
            cloudFileObject.setFolder(uploadInfo.getFolder());
            cloudFileObject.setKey(key);
            cloudFileObject.setBucketName(uploadInfo.getBucket());
            cloudFileObject.setRegion(region);
            cloudFileObject.setContentLength(objectResponse.contentLength());
            cloudFileObject.setUserMetadata(objectResponse.metadata());
            return cloudFileObject;
        } catch (NoSuchKeyException suchKeyException) {
            return null;
        } catch (Exception e) {
            log.error("getCommonFileByTypeFromS3 is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    private String getS3FolderKey(UploadInfo uploadInfo, String key, String uploadType) {
        //resume_display的folder是动态的，根据租户id定
        if(UploadTypeEnum.RESUME_DISPLAYS.getKey().equals(uploadType)) {
            return SecurityUtils.getTenantId() + "/" + key;
        } else {
            return StrUtil.isBlank(uploadInfo.getFolder())? key: uploadInfo.getFolder() + key;
        }
    }

    /**
     * 判断文件是否存在
     * @param uuid  key
     * @param uploadType  bucket 类型
     * @return
     */
    @Override
    public boolean exists(String uuid, String uploadType) {
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(uploadType);
        String region = getRegionByBucket(uploadInfo.getBucket());
        try (S3Client amazonS3 = initS3(accessKey, secretKey, region)) {
            GetObjectRequest getObjectRequest = GetObjectRequest.builder()
                    .key(StrUtil.isBlank(uploadInfo.getFolder())? uuid: uploadInfo.getFolder() + uuid)
                    .bucket(uploadInfo.getBucket()).build();
            amazonS3.getObject(getObjectRequest);
            return true;
        } catch (Exception e) {
            if (e instanceof NoSuchKeyException) {
                return false;
            }
            log.error("getCommonFileByTypeFromS3 is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    /**
     * object copy 的方法，支持不同region copy
     * @param copyObjectDto
     * @return
     */
    @Override
    public boolean copyObject(CopyObjectDto copyObjectDto) {
        UploadInfo sourceUploadInfo = uploadInfoConfig.getUploadInfoByUploadType(copyObjectDto.getSourceType().getKey());
        UploadInfo destUploadInfo = uploadInfoConfig.getUploadInfoByUploadType(copyObjectDto.getDestType().getKey());
        String sourceRegion = getRegionByBucket(sourceUploadInfo.getBucket());
        String destRegion = getRegionByBucket(destUploadInfo.getBucket());
        S3ClientBuilder s3ClientBuilder = S3Client.builder().credentialsProvider(() -> AwsBasicCredentials.create(accessKey, secretKey));
        if (!Objects.equals(sourceRegion, destRegion)) {
            //不同的region 的copy, region 需要使用 dest region
            s3ClientBuilder.defaultsMode(DefaultsMode.CROSS_REGION).region(Region.of(destRegion));
        } else {
            // 相同region 的copy
            s3ClientBuilder.defaultsMode(DefaultsMode.STANDARD).region(Region.of(sourceRegion));
        }
        String sourceKey = StrUtil.isBlank(sourceUploadInfo.getFolder())? copyObjectDto.getSourceKey(): sourceUploadInfo.getFolder() + copyObjectDto.getSourceKey();
        String destKey = StrUtil.isBlank(destUploadInfo.getFolder())? copyObjectDto.getDestKey(): destUploadInfo.getFolder() + copyObjectDto.getDestKey();
        try (S3Client s3Client = s3ClientBuilder.build()) {
            s3Client.copyObject(CopyObjectRequest.builder()
                    .sourceBucket(sourceUploadInfo.getBucket())
                    .sourceKey(sourceKey)
                    .destinationBucket(destUploadInfo.getBucket())
                    .destinationKey(destKey)
                    // 复制所有的metadata
                    .metadataDirective(MetadataDirective.COPY)
                    .build());
            boolean flag = exists(copyObjectDto.getDestKey(), copyObjectDto.getDestType().getKey());
            if (BooleanUtil.isTrue(flag)) {
                log.info("sourceBucket : {}, sourceRegion : {}, sourceKey : {} to destBucket : {}, destRegion : {}, destKey : {} is success",
                        sourceUploadInfo.getBucket(), sourceRegion, sourceKey, destUploadInfo.getBucket(), destRegion, destKey);
            }
            return flag;
        } catch (Exception e) {
            if (e instanceof NoSuchKeyException) {
                log.error("copyObject is error, sourceKey is no exists, message = {}", ExceptionUtils.getStackTrace(e));
                return false;
            }
            log.error("copyObject is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    @Override
    public DisplayLinkVo getDisplayUrlVoFromS3(String uuid, String uploadType) {
        DisplayLinkVo displayLinkVo = new DisplayLinkVo();
        CloudFileObjectMetadata cloudFileObjectMetadata = getCommonFileWithoutFileByteByTypeFromS3(uuid, uploadType);
        String contentType = cloudFileObjectMetadata.getContentType();
        if (StrUtil.isBlank(contentType)) {
            displayLinkVo.setDisplayLink(getCommonUrlFromS3(uuid, uploadType, null));
        } else if (RESUME_CONTENT_TYPE_NEED_TO_RESET.contains(contentType)) {
            displayLinkVo.setContentType(CONTENT_TYPE_MSWORD);
            displayLinkVo.setDisplayLink(getCommonUrlFromS3(uuid, uploadType, CONTENT_TYPE_MSWORD));
        } else {
            displayLinkVo.setContentType(contentType);
            displayLinkVo.setDisplayLink(getCommonUrlFromS3(uuid, uploadType, null));
        }
        return displayLinkVo;
    }

    @Override
    public String getDownloadUrlFromS3(String key, String uploadType, Boolean needRestResponseContentDisposition) {
        return getDownUrl(key, uploadType, needRestResponseContentDisposition);
    }

    @Override
    public String getDownloadWatermarkUrlFromS3(String key, String uploadType, Boolean needRestResponseContentDisposition) {
        if(UploadTypeEnum.RESUME.getKey().equals(uploadType)) {
            String downUrl = getDownUrl(key, UploadTypeEnum.RESUME_DISPLAYS.getKey(), needRestResponseContentDisposition);
            if(downUrl != null) {
                return downUrl;
            }
        }
        return getDownUrl(key, uploadType, needRestResponseContentDisposition);
    }

    @Resource
    private ResumeRepository resumeRepository;

    private String getDownUrl(String key, String uploadType, Boolean needRestResponseContentDisposition) {
        UploadInfo uploadInfo = uploadInfoConfig.getUploadInfoByUploadType(uploadType);
        String region = getRegionByBucket(uploadInfo.getBucket());
        try (S3Client s3Client = initS3(accessKey, secretKey, region);
             S3Presigner presigner = S3Presigner.builder().region(Region.of(region))
                     .credentialsProvider(() -> AwsBasicCredentials.create(accessKey, secretKey)).build()) {
            if (isPublic(uploadInfo.getBucket())) {
                //公开bucket 直接返回永久地址
                GetUrlRequest.Builder builder = GetUrlRequest.builder()
                        .bucket(uploadInfo.getBucket())
                        .key(getS3FolderKey(uploadInfo, key, uploadType));
                GetUrlRequest request = builder.build();
                return s3Client.utilities().getUrl(request).toExternalForm();
            } else {
                //私有的bucket 返回临时地址
                GetObjectRequest.Builder builder = GetObjectRequest.builder()
                        .bucket(uploadInfo.getBucket())
                        .key(getS3FolderKey(uploadInfo, key, uploadType));
                //目前前端只传true
                if (BooleanUtil.isTrue(needRestResponseContentDisposition)) {
                    //获取 responseContentDisposition 属性
                    HeadObjectRequest headObjectRequest = HeadObjectRequest.builder()
                            .bucket(uploadInfo.getBucket())
                            .key(getS3FolderKey(uploadInfo, key, uploadType))
                            .build();
                    HeadObjectResponse headObjectResponse = s3Client.headObject(headObjectRequest);
                    String disposition = headObjectResponse.contentDisposition();
                    if (disposition != null) {
                        //文件中存在文件名信息  原始简历 前端会上传文件名
                         if(!disposition.contains("attachment")) {
                            //已经设置的不在需要
                            builder.responseContentDisposition("attachment;" + disposition);
                        }
                    } else {
                        //文件中不存在文件名信息 生成水印pdf简历 文件名需要自己设置
                        String contentType = headObjectResponse.contentType();
                        String fileName = resumeRepository.getFileName(key, SecurityUtils.getTenantId());
                        if(fileName != null) {
                            String newFileName = FileExtensionUtil.generateEncodedFileName(fileName, contentType);
                            builder.responseContentDisposition("attachment;" + newFileName);
                        }
                    }
                }
                GetObjectRequest getObjectRequest = builder.build();
                GetObjectPresignRequest getObjectPresignRequest = GetObjectPresignRequest.builder()
                        // 默认的时间为10分钟, 可以在配置中配置
                        .signatureDuration(Duration.ofMinutes(uploadInfo.getSignatureDuration()))
                        .getObjectRequest(getObjectRequest)
                        .build();
                PresignedGetObjectRequest presignedRequest = presigner.presignGetObject(getObjectPresignRequest);
                return presignedRequest.url().toString();
            }
        } catch (NoSuchKeyException e) {
            log.warn("File not found in S3. Key: {}, Bucket: {}", key, uploadInfo.getBucket());
            return null; // 或返回默认URL，或者其他处理方式
        } catch (Exception e) {
            // The call was transmitted successfully, but Amazon S3 couldn't process
            // it, so it returned an error response.
            log.error("getCommonUrlFromS3 is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.STORE_S3UPLOAD_EXCEPTION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }
}
