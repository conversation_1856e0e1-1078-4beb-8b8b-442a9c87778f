package com.altomni.apn.common.repository.parser;

import com.altomni.apn.common.domain.parser.ParseRecord;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the ParseRecord entity.
 */
@Repository
public interface ParseRecordRepository extends JpaRepository<ParseRecord,Long> {

    Page<ParseRecord> findAllByCreatedByAndIsReviewedOrderByIdDesc(String createdBy, Boolean isReviewed, Pageable pageable);
}
