package com.altomni.apn.common.domain.reportsubscription;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.enumeration.reportSubscriptions.*;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDate;

@Data
@Entity
@Table(name = "report_subscription")
public class ReportSubscription extends AbstractPermissionAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "name")
    private String name;

    @Column(name = "push_time_type")
    @Convert(converter = PushTimeTypeConverter.class)
    private PushTimeType pushTimeType;

    @Column(name = "day_of_week")
    private Integer dayOfWeek;

    @Column(name = "day_of_month")
    private Integer dayOfMonth;

    @Column(name = "send_time")
    private String sendTime;

    @Column(name = "data_period")
    @Convert(converter = DataPeriodConverter.class)
    private DataPeriod dataPeriod;

    @Column(name = "start_date")
    private LocalDate startDate;

    @Column(name = "end_date")
    private LocalDate endDate;

    @Column(name = "report_type")
    @Convert(converter = ReportTypeConverter.class)
    private ReportType reportType;

    @Column(name = "data_permission_json")
    private String dataPermissionJson;

    //创建自动激活
    @Column(name = "is_active")
    @Convert(converter = ActiveTypeConverter.class)
    private ActiveType isActive = ActiveType.ACTIVE;

    @Column(name = "is_auto_generate")
    private boolean isAutoGenerate;

}
