package com.altomni.apn.common.service.xxljob;

import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateBySendTimeForJobAdminDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTenantMessageConfigDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTimezoneDTO;

import java.util.List;

public interface XxlJobService {

    void updateXxlJob(XxlJobApnDTO xxlJobApnDTO);

    void updateJobsBySendTime(List<XxlJobUpdateBySendTimeForJobAdminDTO> xxlJobUpdateBySendTimeList);

    Integer createXxlJob(XxlJobApnDTO xxlJobApnDTO);

    void createXxlJobs(List<XxlJobApnDTO> xxlJobApnDTOList);

    void deleteXxlJob(Integer xxlJobId);

    void deleteXxlJobIdList(List<Integer> idList);

    void updateXxlJobByTenantMessageConfig(List<XxlJobUpdateByTenantMessageConfigDTO> dtoList);

    void updateXxlJobByTimezone(XxlJobUpdateByTimezoneDTO xxlJobUpdateByTimezoneDTO);

    void deleteXxlJobIdListByType(Integer type);
}
