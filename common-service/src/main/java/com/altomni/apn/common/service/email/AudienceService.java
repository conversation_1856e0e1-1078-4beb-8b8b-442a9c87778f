package com.altomni.apn.common.service.email;

import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.email.dto.*;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;

public interface AudienceService {

    HttpResponse createMongoAudienceGroup(String name) throws IOException;

    HttpResponse createMongoAudienceGroup(NameAndDescription nameAndDescription) throws IOException;

    HttpResponse updateMongoAudienceGroup(Long id, Long emailyGroupId, String name) throws IOException;

    HttpResponse updateMongoAudienceGroup(Long id, NameAndDescription nameAndDescription) throws IOException;

    HttpResponse deleteAudienceByIdAndAudienceIdList(Long groupId, List<String> audienceIds) throws IOException;

    HttpResponse addAudienceToGroup(Long groupId, List<MongoAudience> audienceList, int maxGroupSize) throws IOException;

    HttpResponse addTalentsToGroup(Long groupId,  List<Long> talentIds) throws IOException;

    HttpResponse addClientContactsToGroup(Long groupId,  List<Long> contactIds) throws IOException;

    HttpResponse addUsersToGroup(Long groupId,  List<Long> userIds) throws IOException;

    HttpResponse addAudienceToGroupFromCommonPool(Long id, Long emailyGroupId, SearchConditionDTOV2 condition, int maxGroupSize) throws IOException;

    HttpResponse mergeAudienceToGroup(Long targetGroupId, EmailyGroupMergeDTO emailyGroupMergeDTO) throws IOException;

    HttpResponse moveAudienceToGroup(Long sourceGroupId, Long targetGroupId, Long emailySourceGroupId, Long emailyTargetGroupId, List<String> emailyTalentIdList, int maxGroupSize) throws IOException;

    HttpResponse copyAudienceToGroup(Long sourceGroupId, Long targetGroupId, Long emailySourceGroupId, Long emailyTargetGroupId, List<String> emailyTalentIdList, int maxGroupSize) throws IOException;

    HttpResponse updateMongoAudienceGroup(Long emailyGroupId, String name) throws IOException;

    HttpResponse archiveAudienceGroupById(Long groupId) throws IOException;

    HttpResponse unarchiveAudienceGroupById(Long groupId) throws IOException;

    HttpResponse getAudienceGroupFromEmaily(Long groupId) throws IOException;

    HttpResponse getAudienceGroupInfoById(Long groupId) throws IOException;

//    HttpResponse searchAllActiveMongoAudienceGroupList(String name, TalentGroupActive talentGroupActive) throws IOException;

    HttpResponse searchArchivedMongoAudienceGroupList(String name, Pageable pageable) throws IOException;

    HttpResponse searchActiveMongoAudienceGroupList(String name, Pageable pageable) throws IOException;

    HttpResponse getPageableAudienceByGroupIdAndSource(Long groupId, String name, String email, String dataSource, Pageable pageable) throws IOException;

    HttpResponse updateEmailyAudience(Long talentId, EmailyAudienceUpdateDTO emailyAudienceUpdateDTO) throws IOException;

}
