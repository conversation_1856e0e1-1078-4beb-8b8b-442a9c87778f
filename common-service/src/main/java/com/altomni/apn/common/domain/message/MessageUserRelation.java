package com.altomni.apn.common.domain.message;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.message.*;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "message_user_relation")
@Data
public class MessageUserRelation extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "message_id")
    private Long messageId;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "is_read")
    @Convert(converter = MessageStatusEnumConverter.class)
    private MessageStatusEnum isRead = MessageStatusEnum.UNREAD;

    @Column(name = "is_favorite")
    @Convert(converter = MessageFavoriteEnumConverter.class)
    private MessageFavoriteEnum isFavorite = MessageFavoriteEnum.NOT_FAVORITE;

    @Column(name = "is_delete")
    @Convert(converter = MessageDeleteEnumConverter.class)
    private MessageDeleteEnum isDelete = MessageDeleteEnum.NOT_DELETED;


}
