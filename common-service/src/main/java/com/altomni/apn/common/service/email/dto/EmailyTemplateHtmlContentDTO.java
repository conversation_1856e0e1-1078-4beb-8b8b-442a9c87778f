package com.altomni.apn.common.service.email.dto;

import com.altomni.apn.common.utils.ServiceUtils;
import lombok.Data;

import java.util.List;
import java.util.Objects;

@Data
public class EmailyTemplateHtmlContentDTO {

    public String body;

    public String css;

    public List<HtmlContentFront> fonts;

    public String design;

    private String subject;

    public static EmailyTemplateHtmlContentDTO fromEmailyTemplateHtmlContent(EmailyTemplateHtmlContent emailyTemplateHtmlContent) {
        EmailyTemplateHtmlContentDTO emailyTemplateHtmlContentDTO = new EmailyTemplateHtmlContentDTO();
        ServiceUtils.myCopyProperties(emailyTemplateHtmlContent, emailyTemplateHtmlContentDTO);
        return emailyTemplateHtmlContentDTO;
    }

    @Override
    public int hashCode() {
        int result = 17;
        result = result * 31 + body.hashCode() + css.hashCode();
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EmailyTemplateHtmlContentDTO)) {
            return false;
        }
        if (o instanceof EmailyTemplateHtmlContentDTO) {
            EmailyTemplateHtmlContentDTO emailyTemplateHtmlContentDTO = (EmailyTemplateHtmlContentDTO)o;
            return Objects.equals(emailyTemplateHtmlContentDTO.body, body) && Objects.equals(emailyTemplateHtmlContentDTO.css, css);
        }
        return false;
    }


}
