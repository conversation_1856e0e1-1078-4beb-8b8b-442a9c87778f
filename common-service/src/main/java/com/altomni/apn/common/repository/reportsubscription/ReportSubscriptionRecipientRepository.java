package com.altomni.apn.common.repository.reportsubscription;

import com.altomni.apn.common.domain.reportsubscription.ReportSubscriptionRecipient;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ReportSubscriptionRecipientRepository extends JpaRepository<ReportSubscriptionRecipient, Long> {

    void deleteBySubscriptionId(Long subscriptionId);

    void deleteBySubscriptionIdAndUserId(Long subscriptionId, Long userId);

}
