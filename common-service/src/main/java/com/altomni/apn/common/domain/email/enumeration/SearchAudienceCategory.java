package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The EmailBlastStatus enumeration.
 */
public enum SearchAudienceCategory implements ConvertedEnum<Integer> {
    PERSON_SEARCH(0),
    SELLER_SEARCH(1),
    STORE_SEARCH(2);

    private final Integer dbValue;

    SearchAudienceCategory(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<SearchAudienceCategory, Integer> resolver =
        new ReverseEnumResolver<>(SearchAudienceCategory.class, SearchAudienceCategory::toDbValue);

    public static SearchAudienceCategory fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
