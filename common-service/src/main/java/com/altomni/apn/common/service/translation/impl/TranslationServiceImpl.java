package com.altomni.apn.common.service.translation.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.enumeration.enums.LanguageCode;
import com.altomni.apn.common.dto.translation.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.translation.TranslationService;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.io.IOException;


@Service
@RefreshScope
public class TranslationServiceImpl implements TranslationService {

    private static final Logger log = LoggerFactory.getLogger(TranslationServiceImpl.class);

    /**
     * Lark app
     */
    @Value("${application.translation.lark.url}")
    private String url;

    @Value("${application.translation.lark.appId}")
    private String appId;

    @Value("${application.translation.lark.appSecret}")
    private String appSecret;

    public static final String AUTHORIZATION = "Authorization";
    public static final String BEARER = "Bearer";
    private static final okhttp3.MediaType JSON_TYPE = okhttp3.MediaType.parse("application/json; charset=utf-8");

    private LarkAppInternalResponseDTO initLarkApp(String LARK_INTERNAL_URL) {
        log.info("[APN: TranslationService @-1] get lark app token, url: {}", LARK_INTERNAL_URL);
        String responseBody = null;
        try {
            JSONObject json = new JSONObject();
            json.put("app_id", appId);//default en
            json.put("app_secret", appSecret);
            // send with okhttp3
            okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON_TYPE, JSONUtil.toJsonStr(json));
            Request request = new Request.Builder().url(LARK_INTERNAL_URL).post(body).build();
            okhttp3.Response response = new OkHttpClient().newCall(request).execute();
            responseBody = response.body() != null ? response.body().string() : null;
            log.info("[APN: TranslationService @-1] get lark app token result: {}", responseBody);
            if (!response.isSuccessful()) {
                throw new CustomParameterizedException(responseBody);
            }
        }catch (IOException e){
            log.error("[APN: TranslationService @-1] get lark app token error: {}", e.getMessage());
            throw new CustomParameterizedException("Internal Server Error");
        }
        return JSONUtil.toBean(responseBody, LarkAppInternalResponseDTO.class);
    }

    @Override
    public TranslationResultDTO getTextTranslate(TextTranslationDTO textTranslationDTO) {
        String LARK_INTERNAL_URL = url + "/auth/v3/tenant_access_token/internal";
        String LARK_TRANSLATION_URL = url + "/translation/v1/text/translate";
        log.info("[APN: TranslationService @-1] use lark API for text translate, url: {}", LARK_TRANSLATION_URL);
        TranslationResultDataDTO result = new TranslationResultDataDTO();
        String responseBody = null;

        try {
            LarkAppInternalResponseDTO internalResponseDTO = initLarkApp(LARK_INTERNAL_URL);
            if(ObjectUtil.isNotEmpty(internalResponseDTO)){
                // send with okhttp3
                JSONObject json = new JSONObject();
                json.put("source_language", LanguageCode.EN.getCode());//default en
                json.put("text", textTranslationDTO.getText());
                json.put("target_language", textTranslationDTO.getTargetLanguage().getCode());
                okhttp3.RequestBody body = okhttp3.RequestBody.create(JSON_TYPE, JSONUtil.toJsonStr(json));
                Request request = new Request.Builder()
                        .url(LARK_TRANSLATION_URL)
                        .addHeader(AUTHORIZATION, BEARER + StrUtil.SPACE + internalResponseDTO.getTenantAccessToken())
                        .post(body).build();
                okhttp3.Response response = new OkHttpClient().newCall(request).execute();
                responseBody = response.body() != null ? response.body().string() : null;
                log.info("[APN: TranslationService @-1] use lark API for text translate, responseBody: {}", responseBody);
                if (!response.isSuccessful()) {
                    throw new CustomParameterizedException(responseBody);
                }
            }
        } catch (Exception e) {
            log.error("[APN: TranslationService @-1] use lark API for text translate error: {}", e.getMessage());
            throw new CustomParameterizedException("Internal Server Error");
        }
        return JSONUtil.toBean(responseBody, TranslationResultDTO.class);
    }

}
