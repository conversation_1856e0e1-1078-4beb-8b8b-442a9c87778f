package com.altomni.apn.common.service.email.dto.emailVM;


import com.altomni.apn.common.domain.email.enumeration.AudienceGroupType;
import com.altomni.apn.common.domain.email.enumeration.SearchAudienceCategory;

import java.io.Serializable;

public class AudienceVM implements Serializable {

//    private Long id;
    private Long id;

    private String subgroupId; //only used for mongoAudience!

    private String name;

    private AudienceGroupType type;

    private SearchAudienceCategory category;

    private Integer recipientLimit;

    public AudienceVM() {}

    public AudienceVM(Long id, String name, AudienceGroupType type, Integer recipientLimit) {
        this.id = id;
        this.name = name;
        this.type = type;
        this.recipientLimit = recipientLimit;
    }

//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getSubgroupId() { return subgroupId; }

    public void setSubgroupId(String subgroupId) { this.subgroupId = subgroupId; }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public AudienceGroupType getType() {
        return type;
    }

    public void setType(AudienceGroupType type) {
        this.type = type;
    }

    public SearchAudienceCategory getCategory() { return category; }

    public void setCategory(SearchAudienceCategory category) { this.category = category; }

    public Integer getRecipientLimit() {
        return recipientLimit;
    }

    public void setRecipientLimit(Integer recipientLimit) {
        this.recipientLimit = recipientLimit;
    }

    @Override
    public String toString() {
        return "AudienceVM{" +
            "id=" + id +
            ", subgroupId='" + subgroupId + '\'' +
            ", name='" + name + '\'' +
            ", type=" + type +
            ", category=" + category +
            ", recipientLimit=" + recipientLimit +
            '}';
    }
}
