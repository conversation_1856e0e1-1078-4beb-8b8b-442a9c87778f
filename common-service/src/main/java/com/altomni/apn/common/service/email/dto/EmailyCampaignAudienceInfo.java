package com.altomni.apn.common.service.email.dto;

import com.altomni.apn.common.domain.email.enumeration.EmailyAudienceInfoType;
import com.altomni.apn.common.domain.email.enumeration.TalentGroupActive;
import lombok.Data;

import java.io.Serializable;

@Data
public class EmailyCampaignAudienceInfo implements Serializable {

    private Long id;

    private String name;

    private String subgroupId;

    private EmailyAudienceInfoType type = EmailyAudienceInfoType.MONGO_LIST;

    private String recipientLimit;

    private TalentGroupActive active = TalentGroupActive.ACTIVE;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubgroupId() {
        return subgroupId;
    }

    public void setSubgroupId(String subgroupId) {
        this.subgroupId = subgroupId;
    }

    public EmailyAudienceInfoType getType() {
        return type;
    }

    public void setType(EmailyAudienceInfoType type) {
        this.type = type;
    }

    public String getRecipientLimit() {
        return recipientLimit;
    }

    public void setRecipientLimit(String recipientLimit) {
        this.recipientLimit = recipientLimit;
    }

    public TalentGroupActive getActive() {
        return active;
    }

    public void setActive(TalentGroupActive active) {
        this.active = active;
    }

    public EmailyCampaignAudienceInfo() {}

    public EmailyCampaignAudienceInfo(Long id, String name) {
        this.id = id;
        this.name = name;
    }

    public EmailyCampaignAudienceInfo(Long id, String name, String subgroupId) {
        this.id = id;
        this.name = name;
        this.subgroupId = subgroupId;
    }

    public EmailyCampaignAudienceInfo(Long id, String name, String subgroupId, String recipientLimit, TalentGroupActive active) {
        this.id = id;
        this.name = name;
        this.subgroupId = subgroupId;
        this.recipientLimit = recipientLimit;
        this.active = active;
    }
}
