package com.altomni.apn.common.dto.calendar;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobFollowStatistic {
    private List<SystemCalendarStatisticItem> noSubmitTalent = new ArrayList<>();
    private Integer noSubmitTalentTotal = 0;
    private List<SystemCalendarStatisticItem> noInterview = new ArrayList<>();
    private Integer noInterviewTotal = 0;

    public void count() {
        noSubmitTalentTotal = noSubmitTalent.size();
        noInterviewTotal = noInterview.size();
    }
}
