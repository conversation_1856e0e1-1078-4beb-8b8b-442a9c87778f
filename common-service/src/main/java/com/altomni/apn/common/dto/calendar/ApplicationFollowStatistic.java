package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ApplicationFollowStatistic {
    private List<SystemCalendarStatisticItem> notSubmitToClient = new ArrayList<>();
    private Integer notSubmitToClientTotal = 0;
    private List<SystemCalendarStatisticItem> submitToClientNotUpdateStatus = new ArrayList<>();
    private Integer submitToClientNotUpdateStatusTotal = 0;
    private List<SystemCalendarStatisticItem> offerPassNotUpdateStatus = new ArrayList<>();
    private Integer offerPassNotUpdateStatusTotal = 0;
    private List<SystemCalendarStatisticItem> paymentOverdue = new ArrayList<>();
    private Integer paymentOverdueTotal = 0;

    public void count() {
        notSubmitToClientTotal = notSubmitToClient.size();
        submitToClientNotUpdateStatusTotal = submitToClientNotUpdateStatus.size();
        offerPassNotUpdateStatusTotal = offerPassNotUpdateStatus.size();
        paymentOverdueTotal = paymentOverdue.size();
    }

    public Set<Long> allTalentIds() {
        return Stream.of(notSubmitToClient, submitToClientNotUpdateStatus, offerPassNotUpdateStatus, paymentOverdue)
                .flatMap(List::stream)
                .map(SystemCalendarStatisticItem::getTalentId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    public ApplicationFollowStatistic processConfidentialInfo(Map<Long, ConfidentialInfoDto> confidentialInfoMap, Set<Long> viewAbleConfidentialTalentIds) {
        var processItem = SystemCalendarStatisticItem.processConfidential(confidentialInfoMap, viewAbleConfidentialTalentIds);
        ApplicationFollowStatistic statistic = new ApplicationFollowStatistic();
        statistic.notSubmitToClient = notSubmitToClient.stream().map(processItem).toList();
        statistic.submitToClientNotUpdateStatus = submitToClientNotUpdateStatus.stream().map(processItem).toList();
        statistic.offerPassNotUpdateStatus = offerPassNotUpdateStatus.stream().map(processItem).toList();
        statistic.paymentOverdue = paymentOverdue.stream().map(processItem).toList();
        statistic.count();
        return statistic;
    }
}
