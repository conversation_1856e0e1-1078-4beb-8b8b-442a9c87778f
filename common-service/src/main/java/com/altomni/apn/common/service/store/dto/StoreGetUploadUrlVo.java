package com.altomni.apn.common.service.store.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class StoreGetUploadUrlVo {

    private String fileName;

    private String uuid;

    private Map<String, String> postPolicy;

    private String s3Link;
}
