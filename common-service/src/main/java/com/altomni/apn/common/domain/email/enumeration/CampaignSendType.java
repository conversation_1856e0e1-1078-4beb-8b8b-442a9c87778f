package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The EmailBlastStatus enumeration.
 */
public enum CampaignSendType implements ConvertedEnum<Integer> {
    NOW(0),
    SINGLE(1),
    MULTIPLE(2);

    private final Integer dbValue;

    CampaignSendType(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<CampaignSendType, Integer> resolver =
        new ReverseEnumResolver<>(CampaignSendType.class, CampaignSendType::toDbValue);

    public static CampaignSendType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
