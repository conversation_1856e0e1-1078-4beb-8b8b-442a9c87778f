package com.altomni.apn.common.config.rabbitmq;//package com.altomni.apn.parser.config.rabbitmq;
//
//import org.springframework.amqp.core.Binding;
//import org.springframework.amqp.core.BindingBuilder;
//import org.springframework.amqp.core.DirectExchange;
//import org.springframework.amqp.core.Queue;
//import org.springframework.context.annotation.Bean;
//import org.springframework.stereotype.Component;
//
//@Component
//public class DeadQueueConfig {
//
//    public static final String DEAD_EXCHANGE = "dead.Exchange";
//
//    public static final String EMAIL_DEAD_QUEUE = "dead-emai";
//
//    public static final String ROUTING_KEY = "email";
//
//    // 1. Declare the switch to create direct routing mode - dead letter switch
//    @Bean
//    DirectExchange deadExchange() {
//        return new DirectExchange(DEAD_EXCHANGE, true, false);
//    }
//
//    // 2.Declaration creation queue - dead letter queue
//    @Bean
//    Queue emailDeadQueue(){
//        return new Queue(EMAIL_DEAD_QUEUE,true);
//    }
//
//    // 3.Bind the relationship between the switch and the queue, and set the bindingkey between the switch and the queue
//    @Bean
//    Binding bindEmailDead(Queue emailDeadQueue, DirectExchange deadExchange){
//        // Only when the routingkey specified during message delivery matches the bindingkey (dead), the message will be delivered to the queue
//        return BindingBuilder.bind(emailDeadQueue).to(deadExchange).with(ROUTING_KEY);
//    }
//}
