package com.altomni.apn.common.service.email.dto;

import org.hibernate.validator.constraints.NotBlank;

import java.io.Serializable;

public class EmailBlastAttachmentDTO implements Serializable {

    @NotBlank
    private String name;

    @NotBlank
    private String link;

    public EmailBlastAttachmentDTO() {}

    public EmailBlastAttachmentDTO(String name, String link) {
        this.name = name;
        this.link = link;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    @Override
    public String toString() {
        return "EmailBlastAttachmentDTO{" +
            "name='" + getName() + "'" +
            ", link='" + getLink() + "'" +
            "}";
    }
}
