package com.altomni.apn.common.web.rest.translation;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.dto.translation.TextTranslationDTO;
import com.altomni.apn.common.dto.translation.TranslationResultDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.translation.TranslationService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * REST controller for managing TranslationService.
 */
@Api(tags = {"APN-TranslationService"})
@RestController
@RequestMapping("/api/v3")
public class TranslationResource {

    private final Logger log = LoggerFactory.getLogger(TranslationResource.class);

    @Resource
    private TranslationService translationService;

    @PostMapping("/translation/text/translate")
    public ResponseEntity<TranslationResultDTO> getTextTranslate(@RequestBody TextTranslationDTO textTranslationDTO) {
        log.info("[TranslationService: getTextTranslate@{}] REST request to get translated text by languageCode: {}, text: {}"
                , SecurityUtils.getUserId(), textTranslationDTO.getTargetLanguage(), textTranslationDTO.getText());
        if (StringUtils.isBlank(textTranslationDTO.getText())) {
            throw new CustomParameterizedException("The text to be translated is empty.");
        }
        if (ObjectUtil.isNull(textTranslationDTO.getTargetLanguage())) {
            throw new CustomParameterizedException("TargetLanguage cannot be null when text is translated.");
        }
        return ResponseEntity.ok(translationService.getTextTranslate(textTranslationDTO));
    }

}