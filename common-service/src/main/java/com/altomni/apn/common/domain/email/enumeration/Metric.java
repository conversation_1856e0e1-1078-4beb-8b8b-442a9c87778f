package com.altomni.apn.common.domain.email.enumeration;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * The EmailBlastStatus enumeration.
 */
public enum Metric {
    DELIVERED(Arrays.asList(EventType.DELIVERY, EventType.SPAM_COMPLAINT, EventType.CLICK, EventType.OPEN, EventType.INITIAL_OPEN, EventType.AMP_CLICK, EventType.AMP_OPEN, EventType.AMP_INITIAL_OPEN, EventType.LINK_UNSUBSCRIBE, EventType.LIST_UNSUBSCRIBE)),
    OPENS(Arrays.asList(EventType.OPEN, EventType.INITIAL_OPEN, EventType.AMP_OPEN, EventType.AMP_INITIAL_OPEN, EventType.LIST_UNSUBSCRIBE, EventType.LINK_UNSUBSCRIBE, EventType.SPAM_COMPLAINT, EventType.CLICK, EventType.AMP_CLICK)),
    CLICKS(Arrays.asList(EventType.CLICK, EventType.AMP_CLICK, EventType.LINK_UNSUBSCRIBE, EventType.LIST_UNSUBSCRIBE)),
    UNSUBSCRIBES(Arrays.asList(EventType.LINK_UNSUBSCRIBE, EventType.LIST_UNSUBSCRIBE)),
    SOFT_BOUNCE(Collections.singletonList(EventType.SOFT_BOUNCE)),
    BLOCK_BOUNCE(Collections.singletonList(EventType.BLOCK_BOUNCE)),
    ADMIN_BOUNCE(Collections.singletonList(EventType.ADMIN_BOUNCE)),
    UNDETERMINED_BOUNCE(Collections.singletonList(EventType.UNDETERMINED_BOUNCE)),
    HARD_BOUNCE(Collections.singletonList(EventType.HARD_BOUNCE)),

    OTHER_BOUNCE(Arrays.asList(EventType.BLOCK_BOUNCE, EventType.ADMIN_BOUNCE, EventType.UNDETERMINED_BOUNCE)),

    BOUNCE(Arrays.asList(EventType.SOFT_BOUNCE, EventType.BLOCK_BOUNCE, EventType.ADMIN_BOUNCE, EventType.UNDETERMINED_BOUNCE, EventType.HARD_BOUNCE));

    private final List<EventType> eventTypes;

    Metric(List<EventType> eventTypes) { this.eventTypes = eventTypes; }

    public List<EventType> getEventTypes() { return eventTypes; }
}
