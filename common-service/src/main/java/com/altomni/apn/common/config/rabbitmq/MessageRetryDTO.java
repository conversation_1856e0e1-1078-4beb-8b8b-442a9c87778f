package com.altomni.apn.common.config.rabbitmq;//package com.altomni.apn.parser.config.rabbitmq;
//
//
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.experimental.Accessors;
//
//import java.io.Serializable;
//import java.util.Date;
//
//@Data
//@EqualsAndHashCode(callSuper = false)
//@Accessors(chain = true)
//public class MessageRetryDTO implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * Original message body
//     */
//    private String bodyMsg;
//
//    /**
//     * Message source ID
//     */
//    private String sourceId;
//
//    /**
//     * Source description
//     */
//    private String sourceDesc;
//
//    /**
//     * Exchanger
//     */
//    private String exchangeName;
//
//    /**
//     * Routing key
//     */
//    private String routingKey;
//
//    /**
//     * Queue
//     */
//    private String queueName;
//
//    /**
//     * Status, 1: initialization, 2: success, 3: failure
//     */
//    private Integer status = 1;
//
//    /**
//     * Max retries
//     */
//    private Integer maxTryCount = 3;
//
//    /**
//     * Current number of retries
//     */
//    private Integer currentRetryCount = 0;
//
//    /**
//     * Retry interval (MS)
//     */
//    private Long retryIntervalTime = 0L;
//
//    /**
//     * Task failure information
//     */
//    private String errorMsg;
//
//    /**
//     * Creation time
//     */
//    private Date createTime;
//
//    @Override
//    public String toString() {
//        return "MessageRetryDTO{" +
//            "bodyMsg='" + bodyMsg + '\'' +
//            ", sourceId='" + sourceId + '\'' +
//            ", sourceDesc='" + sourceDesc + '\'' +
//            ", exchangeName='" + exchangeName + '\'' +
//            ", routingKey='" + routingKey + '\'' +
//            ", queueName='" + queueName + '\'' +
//            ", status=" + status +
//            ", maxTryCount=" + maxTryCount +
//            ", currentRetryCount=" + currentRetryCount +
//            ", retryIntervalTime=" + retryIntervalTime +
//            ", errorMsg='" + errorMsg + '\'' +
//            ", createTime=" + createTime +
//            '}';
//    }
//
//    /**
//     * Check whether the number of retries exceeds the maximum
//     *
//     * @return
//     */
//    public boolean checkRetryCount() {
//        retryCountCalculate();
//        return (this.currentRetryCount < this.maxTryCount);
//    }
//
//    /**
//     * Recalculate the number of retries
//     */
//    private void retryCountCalculate() {
//        this.currentRetryCount = this.currentRetryCount + 1;
//    }
//
//}
