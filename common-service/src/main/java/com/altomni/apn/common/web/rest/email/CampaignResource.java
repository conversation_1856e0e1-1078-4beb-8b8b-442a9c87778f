package com.altomni.apn.common.web.rest.email;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignArchiveStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignStatus;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.service.email.CampaignService;
import com.altomni.apn.common.service.email.dto.EmailyAssociatedCampaign;
import com.altomni.apn.common.service.email.dto.EmailyCampaignSendDTO;
import com.altomni.apn.common.service.email.dto.EmailyCmpaignDenyDTO;
import com.altomni.apn.common.service.email.dto.emailVM.EmailVM;
import com.altomni.apn.common.utils.HeaderConvertUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-EmailService"})
@RestController
@RequestMapping("/api/v3/campaign")
public class CampaignResource {

    private final Logger log = LoggerFactory.getLogger(CampaignResource.class);

    private static final String ENTITY_NAME = "emailService";

    @Resource
    private CampaignService campaignService;
    @PostMapping(value = "/send-email-blast", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> sendEmailBlast(@RequestBody EmailVM emailVM) throws IOException {
        log.info("[CampaignResource: sendEmailBlast @{}] REST request to send email blast: {}", SecurityUtils.getUserId(), emailVM);
        HttpResponse response = campaignService.sendEmailBlast(emailVM);
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
//        return new ResponseEntity<>(JSONObject.parseObject(response.getBody()), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @PostMapping(value = "/send_rich_mail", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> sendRichMail(@RequestParam(required = false) String from,
                                             @RequestParam List<String> to,
                                             @RequestParam(required = false) List<String> bcc,
                                             @RequestParam(required = false) List<String> cc,
                                             @RequestParam String subject,
                                             @RequestParam String html_content,
                                             @RequestParam(required = false) List<String> links,
                                             @RequestParam(required = false) Boolean transactional,
                                             @RequestParam(required = false) List<MultipartFile> files,
                                             @RequestParam(required = false) Long accountCompanyId,
                                             @RequestParam(required = false) Long accountContactId) throws IOException {
        log.info("[CampaignResource: sendRichMail @{}] REST request to send rich email, with params [from:{}, to: {}, bcc: {}, cc: {}, subject: {}, html_content: {}, files(number): {}", SecurityUtils.getUserId(), from, to, bcc, cc, subject, html_content, Objects.isNull(files) ? 0 : files.size());
        campaignService.sendHtmlMail(from, to, cc, bcc, subject, html_content, links, files, false, false, transactional, accountCompanyId, accountContactId);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @PostMapping(value = "/mail/save-draft", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> saveDraft(@RequestBody com.altomni.apn.common.web.rest.email.vm.MailVM mailVM) throws IOException {
        log.info("[CampaignResource: sendRichMail @{}] REST request to send rich email, with params MailVM: {}", SecurityUtils.getUserId(), mailVM);
        if (Objects.nonNull(mailVM.getTalentId())) {
            mailVM.setCustomerId2("APN:TALENT" + mailVM.getTalentId());
        }
        String res = campaignService.sendHtmlMail(mailVM, false, false, false);
        return ResponseEntity.ok(res);
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @PostMapping(value = "/mail/send-draft", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> sendDraft(@RequestBody com.altomni.apn.common.web.rest.email.vm.MailVM mailVM) throws IOException {
        log.info("[CampaignResource: sendRichMail @{}] REST request to send rich email, with params MailVM: {}", SecurityUtils.getUserId(), mailVM);
        if (Objects.nonNull(mailVM.getTalentId())) {
            mailVM.setCustomerId2("APN:TALENT" + mailVM.getTalentId());
        }
        String res = campaignService.sendHtmlMail(mailVM, false, false , true);
        return ResponseEntity.ok(res);
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @GetMapping(value = "/mail/list", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getList(@RequestParam String status, @PageableDefault(value = 20, sort = {"createdDate"}, direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[CampaignResource: getList @{}] REST request to send rich email, with status: {}", SecurityUtils.getUserId(), status);
        HttpResponse response = campaignService.getListByStatus(status, pageable);
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @GetMapping(value = "/mail/list/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getById(@PathVariable Long id) throws IOException {
        log.info("[CampaignResource: getById @{}] REST request to get rich email, with id: {}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.getById(id);
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @GetMapping(value = "/mail/list/{id}/ignore-userId", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getByIdIgnoreUserId(@PathVariable Long id) throws IOException {
        log.info("[CampaignResource: getByIdIgnoreUserId @{}] REST request to get rich email, with id: {}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.getByIdIgnoreUserId(id);
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @DeleteMapping(value = "/mail/list/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> deleteById(@PathVariable Long id) throws IOException {
        log.info("[CampaignResource: getById @{}] REST request to send rich email, with id: {}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.deleteById(id);
        return new ResponseEntity<>(null, HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @ApiOperation(value = "APN-Pro use this api to send emails")
    @PostMapping(value = "/send_rich_mail/pro", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> sendRichMailForApnPro(@RequestParam(required = false) String from,
                                                      @RequestParam List<String> to,
                                                      @RequestParam(required = false) List<String> bcc,
                                                      @RequestParam(required = false) List<String> cc,
                                                      @RequestParam String subject,
                                                      @RequestParam String html_content,
                                                      @RequestParam(required = false) List<String> links,
                                                      @RequestParam(required = false) Boolean transactional,
                                                      @RequestParam(required = false) List<MultipartFile> files) throws IOException {
        log.info("[CampaignResource: sendRichMailForApnPro @{}] REST request to send rich email, with params [from:{}, to: {}, bcc: {}, cc: {}, subject: {}, html_content: {}, files(number): {}", SecurityUtils.getUserId(), from, to, bcc, cc, subject, html_content, Objects.isNull(files) ? 0 : files.size());
        campaignService.sendHtmlMail(from, to, cc, bcc, subject, html_content, links, files, false, true, transactional);
        return ResponseEntity.ok().build();
    }

    @PostMapping(value = "/send_rich_mail_by_feign")
    public ResponseEntity<Void> sendRichMailByFeign(@RequestBody MultiValueMap<String, String> map) throws IOException {
        String from = map.getFirst("from");
        List<String> to = map.get("to");
        List<String> cc = map.get("cc");
        List<String> bcc = map.get("bcc");
        String subject =  map.getFirst("subject");
        String htmlContent = map.getFirst("html_content");
        List<String> nameList = map.get("name");
        List<String> fileNameList = map.get("fileName");
        List<String> bufferByteList = map.get("bufferByte");
        List<String> contentTypeList = map.get("contentType");
        List<String> links = map.get("links");
        String transactional = map.getFirst("transactional");
        List<MultipartFile> files = convertMultipartFileList(nameList, fileNameList, bufferByteList, contentTypeList);
        log.info("[CampaignResource: sendRichMailByFeign @{}] REST request to send rich email (by feign), with params [from:{}, to: {}, bcc: {}, cc: {}, subject: {}, html_content: {}, files(number): {}", SecurityUtils.getUserId(), from, to, bcc, cc, subject, htmlContent, files.size());

        campaignService.sendHtmlMail(from, to, cc, bcc, subject, htmlContent, links, files, true, false, Boolean.valueOf(transactional));
        return ResponseEntity.ok().build();
    }

    private List<MultipartFile> convertMultipartFileList(List<String> nameList, List<String> fileNameList, List<String> bufferByteList, List<String> contentTypeList) throws IOException {
        List<MultipartFile> files = new ArrayList<>();
        if (CollUtil.isEmpty(bufferByteList)) {
            return files;
        }
        for (int i = 0; i < bufferByteList.size(); i++) {
            InputStream inputStream = new ByteArrayInputStream(Base64.decodeBase64(bufferByteList.get(i)));
            files.add(new MockMultipartFile(nameList.get(i), fileNameList.get(i), contentTypeList.get(i), inputStream));
        }
        return files;
    }

    @PostMapping(value = "/send_html_mail", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> sendHtmlMail(@RequestBody MailVM mailVM) throws IOException {
        log.info("[CampaignResource: sendHtmlMail @{}] REST request to send html email, with accept mailVM: {}", SecurityUtils.getUserId(), mailVM);
        campaignService.sendHtmlMail(mailVM);
        log.info("[CampaignResource: sendHtmlMail @{}] REST request to send html email success, with mailVM: {}", SecurityUtils.getUserId(), mailVM);
        return ResponseEntity.ok().build();
    }

    @PostMapping(value = "", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> createCampaign(@RequestBody EmailyAssociatedCampaign campaign) throws IOException {
        log.info("[CampaignResource: createCampaign @{}] REST request to create campaign: {}", SecurityUtils.getUserId(), campaign);
        HttpResponse response = campaignService.createEmailCampaign(campaign);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/save-draft", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> saveDraftCampaign(@RequestBody EmailyAssociatedCampaign campaign) throws IOException {
        log.info("[CampaignResource: saveDraftCampaign @{}] REST request to save draft campaign: {}", SecurityUtils.getUserId(), campaign);
        HttpResponse response = campaignService.saveDraftCampaign(campaign);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PutMapping(value = "/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> updateCampaign(@PathVariable Long id, @RequestBody EmailyAssociatedCampaign campaign) throws IOException {
        log.info("[CampaignResource: updateCampaign @{}] REST request to update campaign id:{} with: {}", SecurityUtils.getUserId(), id, campaign);
        HttpResponse response = campaignService.updateEmailCampaign(id, campaign);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/{id}/request-approve", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> requestApprovalForCampaign(@PathVariable Long id) throws IOException {
        log.info("[CampaignResource: requestApprovalForCampaign @{}] REST request to approve campaign id:{}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.updateEmailCampaignToPending(id);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getCampaignById(@PathVariable Long id) throws IOException {
        log.info("[CampaignResource: getCampaignById @{}] REST request to get campaign id:{}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.getCampaignById(id);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> searchUnarchivedCampaigns(@RequestParam(defaultValue = "") String search, @RequestParam(required = false) List<EmailCampaignStatus> status, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[CampaignResource: searchUnarchivedCampaigns @{}] REST request to search campaign (unarchived) with search:{} and status in: {}", SecurityUtils.getUserId(), search, status);
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        String sort = "";
        if (!Objects.isNull(order)) {
            sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
//            builder.addQueryParameter(StringUtils.toRootLowerCase(order.getDirection().toString()), order.getProperty());
        }
        HttpResponse response = campaignService.searchEmailCampaign(EmailCampaignArchiveStatus.NOT_ARCHIVED, status, search, sort, pageable);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/archived", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> searchArchivedCampaigns(@RequestParam(defaultValue = "") String search, @RequestParam(required = false) List<EmailCampaignStatus> status, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[CampaignResource: searchArchivedCampaigns @{}] REST request to search archived campaign with search:{} and status in: {}", SecurityUtils.getUserId(), search, status);
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        String sort = "";
        if (!Objects.isNull(order)) {
            sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
//            builder.addQueryParameter(StringUtils.toRootLowerCase(order.getDirection().toString()), order.getProperty());
        }
        HttpResponse response = campaignService.searchEmailCampaign(EmailCampaignArchiveStatus.ARCHIVED, status, search, sort, pageable);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/{id}/archive", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> archiveCampaign(@PathVariable Long id) throws IOException {
        log.info("[CampaignResource: archiveCampaign @{}] REST request to archive campaign id:{}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.archiveEmailCampaign(id);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/{id}/unarchive", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> unarchiveCampaign(@PathVariable Long id) throws IOException {
        log.info("[CampaignResource: unarchiveCampaign @{}] REST request to unarchive campaign id:{}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.unarchiveEmailCampaign(id);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/{id}/approve", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> approveCampaign(@PathVariable Long id) throws IOException {
        log.info("[CampaignResource: approveCampaign @{}] REST request to approve campaign id:{}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.approveEmailCampaign(id);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/{id}/deny", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> denyCampaign(@PathVariable Long id, @RequestBody EmailyCmpaignDenyDTO denyDTO) throws IOException {
        log.info("[CampaignResource: denyCampaign @{}] REST request to approve campaign id:{}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.denyEmailCampaign(id, denyDTO);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/{id}/test-sending", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> sendTestCampaign(@PathVariable Long id, @RequestBody List<EmailyCampaignSendDTO> recipients) throws IOException {
        log.info("[CampaignResource: sendTestCampaign @{}] REST request to approve campaign id:{}", SecurityUtils.getUserId(), id);
        HttpResponse response = campaignService.sendEmailCampaignTest(id, recipients);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/limit", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> sendTestCampaign() throws IOException {
        log.info("[CampaignResource: sendTestCampaign @{}] REST request to query limit for tenant id:{}", SecurityUtils.getUserId(), SecurityUtils.getTenantId());
        HttpResponse response = campaignService.queryTenantLimit();
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }
}
