package com.altomni.apn.common.service.statistic.impl;

import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.xxljob.RefreshApplicationStopStatisticDTO;
import com.altomni.apn.common.repository.statistic.TalentRecruitmentProcessStopStatisticsRepository;
import com.altomni.apn.common.service.statistic.ApplicationStatistic;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.Set;

@Slf4j
@RefreshScope
@Service("ApplicationStatistic")
public class ApplicationStatisticImpl implements ApplicationStatistic {

    @Resource
    private TalentRecruitmentProcessStopStatisticsRepository talentRecruitmentProcessStopStatisticsRepository;

    @Value("${xxl.job.application.reminder.day:7}")
    private Long xxlJobApplicationReminderDay;

    @Override
    @Transactional
    public void refreshApplicationStopStatistic(RefreshApplicationStopStatisticDTO dto) {
        Set<Long> userIds = dto.getUserIds();
        Set<Long> input = new HashSet<>();
        input.add(SecurityUtils.getUserId());
        if(userIds != null) {
            input.addAll(userIds);
        }
        //判断当前用户需要不需要刷新
        int refresh = talentRecruitmentProcessStopStatisticsRepository.countRefreshApplicationStop(NodeStatus.ELIMINATED.toDbValue(), NodeStatus.ACTIVE.toDbValue(), NodeType.ON_BOARD.toDbValue(), xxlJobApplicationReminderDay, input);
        if(refresh > 0) {
            //统计新增以及更新的停滞流程
            talentRecruitmentProcessStopStatisticsRepository.insertOrUpdateStopStatistics(NodeStatus.ELIMINATED.toDbValue(), NodeStatus.ACTIVE.toDbValue(), NodeType.ON_BOARD.toDbValue(), xxlJobApplicationReminderDay);
        }
        int deleteCount = talentRecruitmentProcessStopStatisticsRepository.countDeleteApplicationStop(NodeStatus.ELIMINATED.toDbValue(), NodeStatus.ACTIVE.toDbValue(), NodeType.ON_BOARD.toDbValue(), xxlJobApplicationReminderDay, input);
        if(deleteCount > 0) {
            //删除目前已结束的停滞流程
            talentRecruitmentProcessStopStatisticsRepository.deleteStopStatistics(NodeStatus.ELIMINATED.toDbValue(), NodeStatus.ACTIVE.toDbValue(), NodeType.ON_BOARD.toDbValue(), xxlJobApplicationReminderDay);
        }
    }
}
