package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EmailCampaignAuditResult implements ConvertedEnum<Integer> {

    APPROVED(0),
    DENIED(1);

    private final int dbValue;

    EmailCampaignAuditResult(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<EmailCampaignAuditResult, Integer> resolver = new ReverseEnumResolver<>(EmailCampaignAuditResult.class, EmailCampaignAuditResult::toDbValue);

    public static EmailCampaignAuditResult fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
