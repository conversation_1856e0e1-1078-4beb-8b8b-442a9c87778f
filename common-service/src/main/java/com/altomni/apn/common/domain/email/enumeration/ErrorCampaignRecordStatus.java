package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ErrorCampaignRecordStatus implements ConvertedEnum<Integer> {

    NOT_PROCESSED(0),
    PROCESSED(1);

    private final int dbValue;

    ErrorCampaignRecordStatus(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<ErrorCampaignRecordStatus, Integer> resolver = new ReverseEnumResolver<>(ErrorCampaignRecordStatus.class, ErrorCampaignRecordStatus::toDbValue);

    public static ErrorCampaignRecordStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
