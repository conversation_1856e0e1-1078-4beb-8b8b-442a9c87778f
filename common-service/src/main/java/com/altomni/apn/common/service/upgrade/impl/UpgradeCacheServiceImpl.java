package com.altomni.apn.common.service.upgrade.impl;

import com.altomni.apn.common.domain.upgrade.UpgradeNotification;
import com.altomni.apn.common.repository.upgrade.UpgradeNotificationRepository;
import com.altomni.apn.common.repository.upgrade.UpgradeUserRelationRepository;
import com.altomni.apn.common.service.upgrade.UpgradeCacheService;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@CacheConfig(cacheManager = "redisCacheManager")
public class UpgradeCacheServiceImpl implements UpgradeCacheService {

    @Resource
    UpgradeNotificationRepository upgradeNotificationRepository;

    @Override
    @Cacheable(cacheNames = {"APN:Upgrade:Notification"}, key = "'tenantId:' + #tenantId", unless = "#result == null")
    public UpgradeNotification getTenantUpgradeNotification(Long tenantId) {
        return upgradeNotificationRepository.findUpgradeNotificationByTenantId(tenantId);
    }

    @Override
    @CacheEvict(cacheNames = {"APN:Upgrade:Notification"}, allEntries = true)
    public void deleteAllUpgradeCache() {
    }

    @Override
    @CacheEvict(cacheNames = {"APN:Upgrade:Notification"}, key = "'tenantId:' + #tenantId")
    public void deleteCacheByTenantId(Long tenantId) {
    }

}
