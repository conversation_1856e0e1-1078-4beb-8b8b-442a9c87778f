package com.altomni.apn.common.service.email.dto;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.email.EmailCampaign;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignArchiveStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignStatus;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
public class EmailyAssociatedCampaign extends AbstractAuditingEntity implements Serializable {

    private Long id;

    private String name;

    private String displayName;

    private String from;

    private String replyTo;

    private String subject;

    private EmailCampaignStatus status;

    private EmailCampaignArchiveStatus archiveStatus;

    private String schedule;

    private Long templateId;

    private String htmlContent;

    private String s3key;

    private String reason;

    private EmailyCampaignAudienceInfo audienceInfo;

    private EmailyCampaignScheduleInfo scheduleInfo;

    private String emailSvcProvider;

    private int recipientLimit;

    private float spamCheckScore;

    private Long industryId;

    private List<EmailBlastAttachmentDTO> attachments = new ArrayList<>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getFrom() {
        return from;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public String getDisplayName() {
        return displayName;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public EmailyCampaignAudienceInfo getAudienceInfo() {
        return audienceInfo;
    }

    public void setAudienceInfo(EmailyCampaignAudienceInfo audienceInfo) {
        this.audienceInfo = audienceInfo;
    }

    public EmailyCampaignScheduleInfo getScheduleInfo() {
        return scheduleInfo;
    }

    public void setScheduleInfo(EmailyCampaignScheduleInfo scheduleInfo) {
        this.scheduleInfo = scheduleInfo;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getHtmlContent() {
        return htmlContent;
    }

    public void setHtmlContent(String htmlContent) {
        this.htmlContent = htmlContent;
    }

    public String getReplyTo() {
        return replyTo;
    }

    public void setReplyTo(String replyTo) {
        this.replyTo = replyTo;
    }

    public EmailCampaignStatus getStatus() {
        return status;
    }

    public void setStatus(EmailCampaignStatus status) {
        this.status = status;
    }

    public String getSchedule() {
        return schedule;
    }

    public void setSchedule(String schedule) {
        this.schedule = schedule;
    }

    public String getS3key() {
        return s3key;
    }

    public void setS3key(String s3key) {
        this.s3key = s3key;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getEmailSvcProvider() {
        return emailSvcProvider;
    }

    public void setEmailSvcProvider(String emailSvcProvider) {
        this.emailSvcProvider = emailSvcProvider;
    }

    public int getRecipientLimit() {
        return recipientLimit;
    }

    public void setRecipientLimit(int recipientLimit) {
        this.recipientLimit = recipientLimit;
    }

    public float getSpamCheckScore() {
        return spamCheckScore;
    }

    public void setSpamCheckScore(float spamCheckScore) {
        this.spamCheckScore = spamCheckScore;
    }

    public List<EmailBlastAttachmentDTO> getAttachments() {
        return attachments;
    }

    public void setAttachments(List<EmailBlastAttachmentDTO> attachments) { this.attachments = attachments; }

    public EmailyAssociatedCampaign() {}

    public EmailyAssociatedCampaign(String name) {
        this.name = name;
    }

    public EmailyAssociatedCampaign(String name, String subject, String from, String displayName, String replyTo, EmailyCampaignAudienceInfo audienceInfo,
                                    EmailyCampaignScheduleInfo scheduleInfo, Long templateId, String htmlContent, Long industryId) {
        this.name = name;
        this.subject = subject;
        this.from = from;
        this.displayName = displayName;
        this.replyTo = replyTo;
        this.audienceInfo = audienceInfo;
        this.scheduleInfo = scheduleInfo;
        this.templateId = templateId;
        this.htmlContent = htmlContent;
        this.industryId = industryId;
    }

    public static EmailyAssociatedCampaign fromEmailCampaign(EmailCampaign emailCampaign) {
        EmailyAssociatedCampaign emailyAssociatedCampaign = new EmailyAssociatedCampaign();
        emailyAssociatedCampaign.setName(emailCampaign.getName());
        emailyAssociatedCampaign.setStatus(emailCampaign.getStatus());
        emailyAssociatedCampaign.setArchiveStatus(emailCampaign.getArchiveStatus());
        emailyAssociatedCampaign.setSubject(emailCampaign.getEmailSubject());
        emailyAssociatedCampaign.setFrom(emailCampaign.getSenderEmail());
        emailyAssociatedCampaign.setDisplayName(emailCampaign.getSenderName());
        emailyAssociatedCampaign.setReplyTo(emailCampaign.getDefaultReplyEmail());
        emailyAssociatedCampaign.setTemplateId(emailCampaign.getTemplateId());
        emailyAssociatedCampaign.setHtmlContent(emailCampaign.getTemplateHtmlContent());
        return emailyAssociatedCampaign;
    }

    @Override
    public int hashCode() {
        int result = 17;
        result = result * 31 + name.hashCode() + status.hashCode() + archiveStatus.hashCode() + subject.hashCode() + from.hashCode() + displayName.hashCode() + replyTo.hashCode() + templateId.hashCode();
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EmailyAssociatedCampaign)) {
            return false;
        }
        if (o instanceof EmailyAssociatedCampaign) {
            EmailyAssociatedCampaign emailyAssociatedCampaign = (EmailyAssociatedCampaign)o;
            return Objects.equals(emailyAssociatedCampaign.name, name) && Objects.equals(emailyAssociatedCampaign.status, status) && Objects.equals(emailyAssociatedCampaign.archiveStatus, archiveStatus) && Objects.equals(emailyAssociatedCampaign.subject, subject) && Objects.equals(emailyAssociatedCampaign.from, from)
                 && Objects.equals(emailyAssociatedCampaign.displayName, displayName) && Objects.equals(emailyAssociatedCampaign.replyTo, replyTo) && Objects.equals(emailyAssociatedCampaign.templateId, templateId);
        }
        return false;
    }

}
