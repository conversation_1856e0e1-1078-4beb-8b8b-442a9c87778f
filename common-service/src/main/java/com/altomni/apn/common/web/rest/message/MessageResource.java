package com.altomni.apn.common.web.rest.message;

import com.altomni.apn.common.domain.enumeration.message.MessageFavoriteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.dto.message.*;
import com.altomni.apn.common.service.message.MessageService;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.message.MessageVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class MessageResource {

    @Resource
    private MessageService messageService;

    @GetMapping("/message-statistics")
    public ResponseEntity<Map<String, Integer>> messageStatistics() {
        log.info("[APN: messageStatistics @{}] REST request to message statistics .", SecurityUtils.getUserId());
        return ResponseEntity.ok(messageService.messageStatistics());
    }

    @PostMapping("/message/page")
    public ResponseEntity<List<MessageVO>> messagePage(@RequestBody MessageSearchPageDTO messageSearchPageDTO) {
        log.info("[APN: messagePage @{}] REST request to search message page , param = {}.", SecurityUtils.getUserId(), messageSearchPageDTO);
        Page<MessageVO> page = messageService.searchMessagePage(messageSearchPageDTO);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/message/page");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @DeleteMapping("/message/{type}")
    public ResponseEntity<Void> deleteMessageByTypeAndUserId(@PathVariable("type") MessageTypeEnum type) {
        log.info("[APN: deleteMessageByTypeAndUserId @{}] REST request to delete message by type = {}.", SecurityUtils.getUserId(), type);
        messageService.deleteMessage(type);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/message/reader/{id}")
    public ResponseEntity<Void> updateMessageReaderStatusById(@PathVariable("id") Long id) {
        log.info("[APN: updateMessageReaderStatusById @{}] REST request to update message status by id = {}.", SecurityUtils.getUserId(), id);
        messageService.updateMessageReaderStatusById(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/message/favorite/{id}/{favorite}")
    public ResponseEntity<Void> updateMessageFavoriteStatusById(@PathVariable("id") Long id, @PathVariable("favorite") MessageFavoriteEnum favorite) {
        log.info("[APN: updateMessageFavoriteStatusById @{}] REST request to update message favorite by id = {}, favorite = {}.", SecurityUtils.getUserId(), id, favorite);
        messageService.updateMessageFavoriteStatusById(id, favorite);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/message")
    public ResponseEntity<Void> createMessage(@RequestBody MessageCreateDTO messageCreateDTO) {
        log.info("[APN: createMessage @{}] REST request to create message , param = {}.", SecurityUtils.getUserId(), messageCreateDTO);
        messageService.createMessage(messageCreateDTO);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/history-handler-message")
    public void historyHandlerMessage() {
        log.info("[APN: historyHandlerMessage @{}] 刷一遍历史数据.", SecurityUtils.getUserId());
        CompletableFuture.runAsync(() -> messageService.historyHandlerMessage());
    }

    @PostMapping("/message/talent-info-update")
    public ResponseEntity<Void> createMessageWithTalentInfoUpdate(@RequestBody MessageCreateWithTalentInfoDTO messageCreateDTO) {
        log.info("[APN: createMessage @{}] REST request to create message with talent info update , param = {}.", SecurityUtils.getUserId(), messageCreateDTO);
        messageService.createMessageWithTalentInfoUpdate(messageCreateDTO);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/message/voicemail")
    public ResponseEntity<Void> createMessageWithVoicemail(@RequestBody MessageCreateWithVoicemailDTO messageCreateWithVoicemailDTO) {
        log.info("[APN: createMessage @{}] REST request to create message with voice message , param = {}.", SecurityUtils.getUserId(), messageCreateWithVoicemailDTO);
        messageService.createMessageWithVoicemail(messageCreateWithVoicemailDTO);
        return ResponseEntity.ok().build();
    }


    /**
     * 发送禁猎客户员工被推荐到职位提醒
     * @param messageCreateDTO
     * @return
     */
    @PostMapping("/message/no-poaching-submit-message")
    public ResponseEntity<Void> createMessageWithNoPoachingSubmit(@RequestBody MessageCreateWithNoPoachingSubmitDTO messageCreateDTO) {
        log.info("[APN: createMessage @{}] REST request to create message with no-poaching submit , param = {}.", SecurityUtils.getUserId(), messageCreateDTO);
        messageService.createMessageWithNoPoachingSubmit(messageCreateDTO);
        return ResponseEntity.ok().build();
    }

}
