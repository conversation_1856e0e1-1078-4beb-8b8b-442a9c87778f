package com.altomni.apn.common.config.env.email;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class EmailAppProperties {

    @Value("${application.email.url}")
    private String emailyBaseUrl;

    @Value("${application.email.system.username}")
    private String username;

    @Value("${application.email.system.password}")
    private String password;

    @Value("${application.mainPath.baseUrl:https://apn-v3-staging.hitalentech.com}")
    private String mainPathBaseUrl;
}
