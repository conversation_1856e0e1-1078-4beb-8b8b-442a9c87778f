package com.altomni.apn.common.service.email.dto;

import lombok.Data;

import java.util.List;

@Data
public class EmailyGroupMergeDTO {

    private List<Long> sourceGroupIds;

    private Long targetGroupId;

    private int maxGroupSize;

    public EmailyGroupMergeDTO() {
    }

    public EmailyGroupMergeDTO(List<Long> sourceGroupIds, Long targetGroupId, int maxGroupSize) {
        this.sourceGroupIds = sourceGroupIds;
        this.targetGroupId = targetGroupId;
        this.maxGroupSize = maxGroupSize;
    }
}
