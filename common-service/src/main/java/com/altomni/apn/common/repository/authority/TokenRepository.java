package com.altomni.apn.common.repository.authority;

import com.altomni.apn.common.domain.user.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


@Repository
public interface TokenRepository extends JpaRepository<User, Long> {

    @Query(value = "select refresh_token from oauth_access_token where user_name = ?1", nativeQuery = true)
    String getRefreshTokenByUid(String uid);

    @Modifying
    @Transactional
    @Query(value = "DELETE from oauth_refresh_token where token_id = ?1", nativeQuery = true)
    void deleteAccessTokenByRefreshToken(String refreshToken);

    @Modifying
    @Transactional
    @Query(value = "DELETE from oauth_access_token where refresh_token = ?1", nativeQuery = true)
    void deleteRefreshTokenByRefreshToken(String refreshToken);

}
