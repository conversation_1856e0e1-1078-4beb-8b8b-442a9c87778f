package com.altomni.apn.common.service.email.dto;

import com.altomni.apn.common.domain.email.enumeration.ApplicationSource;
import lombok.Data;

@Data
public class EmailyAudienceUpdateDTO {

    private String name;

    private String email;

    private ApplicationSource dataSource;

    private String dataSourceId;

    public EmailyAudienceUpdateDTO() {
    }

    public EmailyAudienceUpdateDTO(String name, String email, ApplicationSource dataSource, String dataSourceId) {
        this.name = name;
        this.email = email;
        this.dataSource = dataSource;
        this.dataSourceId = dataSourceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public ApplicationSource getDataSource() {
        return dataSource;
    }

    public void setDataSource(ApplicationSource dataSource) {
        this.dataSource = dataSource;
    }

    public String getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(String dataSourceId) {
        this.dataSourceId = dataSourceId;
    }
}
