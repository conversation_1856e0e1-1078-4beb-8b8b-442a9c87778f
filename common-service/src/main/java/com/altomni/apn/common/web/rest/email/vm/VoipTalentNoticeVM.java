package com.altomni.apn.common.web.rest.email.vm;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@NoArgsConstructor
@AllArgsConstructor
public class VoipTalentNoticeVM extends MailVM implements Serializable {

    private Long talentId;

    private Long contactId;

}
