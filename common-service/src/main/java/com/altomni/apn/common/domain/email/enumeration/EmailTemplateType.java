package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EmailTemplateType implements ConvertedEnum<Integer> {

    USER_SAVED(0),
    BUILT_IN(1);

    private final int dbValue;

    EmailTemplateType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<EmailTemplateType, Integer> resolver = new ReverseEnumResolver<>(EmailTemplateType.class, EmailTemplateType::toDbValue);

    public static EmailTemplateType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
