package com.altomni.apn.common.service.cache.redis.impl;

import cn.hutool.core.convert.Convert;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.config.env.parser.ParserProperties;
import com.altomni.apn.common.domain.report.SubmittalsAgingReport;
import com.altomni.apn.common.domain.report.SummaryReport;
import com.altomni.apn.common.dto.redis.ImagesInfoParser;
import com.altomni.apn.common.dto.redis.ParserRedisResponse;
import com.altomni.apn.common.dto.redis.ParserResponse;
import com.altomni.apn.common.dto.redis.RedisResponse;
import com.altomni.apn.common.enumeration.ParseStatus;
import com.altomni.apn.common.service.cache.redis.RedisService;
import com.altomni.apn.common.utils.JsonUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.Pipeline;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;

@Service
public class RedisServiceImpl implements RedisService {

    private final Logger log = LoggerFactory.getLogger(RedisServiceImpl.class);

    private JedisPool pool;

    private static JedisPoolConfig jedisPoolConfig;

    private static final int maxIdle = 10;

    private static final long maxWaitMillis = 50 * 1000;

    private static final int maxActive = 100;

    private static final int timeout = 2000;

    private static final int EXPIRE_TIME = 3600;

    @Resource
    private ParserProperties parserProperties;

    private void close(Jedis jedis) {
        if (jedis != null) {
            try {
                jedis.close();
            } catch (Exception e) {
                log.error("close jedis connection error: {}", e.getMessage());
                try {
                    jedis.disconnect();
                } catch (Exception e1) {
                    log.error("disconnect jedis connection error: {}" , e1.getMessage());
                }
            }
        }
    }

    private synchronized Jedis getJedis() {
        if (pool == null) {
            try {
                jedisPoolConfig = new JedisPoolConfig();
                jedisPoolConfig.setMaxTotal(maxActive);
                jedisPoolConfig.setMaxWaitMillis(maxWaitMillis);
                jedisPoolConfig.setMaxIdle(maxIdle);
                jedisPoolConfig.setTestOnBorrow(true);
                jedisPoolConfig.setTestOnReturn(true);
            } catch (Exception e) {
                log.error("error", e);
            }
            pool = new JedisPool(jedisPoolConfig, parserProperties.getRedisParserHost(), parserProperties.getRedisParserPort(), timeout, null, parserProperties.getRedisDatabase());
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 1st error message: {}", e.getMessage());
        }
        try {
            return pool.getResource();
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, 2cnd error message: {}", e.getMessage());
        }
        return pool.getResource();
    }

    @Override
    public String get(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null && jedis.exists(key)) {
                return jedis.get(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    @Override
    public RedisResponse getDataForJob(String uuid, Pageable pageable) {
        RedisResponse result = new RedisResponse();
        String key = RedisConstants.DATA_KEY_RATER + uuid;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null)
            {
                if (jedis.exists(key + RedisConstants.DATA_KEY_DATA))
                {
                    if (pageable == null) {
                        result.setData(jedis.zrevrangeByScoreWithScores(key + RedisConstants.DATA_KEY_DATA, 1, 0));
                    } else {
                        result.setData(jedis.zrevrangeByScoreWithScores(key + RedisConstants.DATA_KEY_DATA, 1, 0, pageable.getPageSize() * pageable.getPageNumber(), pageable.getPageSize()));
                    }
                }

                result.setStatus(jedis.get(key + RedisConstants.DATA_KEY_STATUS));
                result.setTotal(valueOf(jedis.get(key + RedisConstants.DATA_KEY_TOTAL)));

            }
        } catch (Exception e) {
            log.error("************Get job data error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    private Integer valueOf(String s) {
        return StringUtils.isEmpty(s) ? 0 : Integer.parseInt(s);
    }

    @Override
    public RedisResponse getTalentData(String uuid, Pageable pageable) {
        RedisResponse result = new RedisResponse();
        String key = RedisConstants.DATA_KEY_RATER + uuid;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                if (jedis.exists(key + RedisConstants.DATA_KEY_DATA)) {
                    if (pageable != null) {
                        result.setData(jedis.zrevrangeByScoreWithScores(key + RedisConstants.DATA_KEY_DATA, 1, 0, pageable.getPageSize() * pageable.getPageNumber(), pageable.getPageSize()));
                    } else {
                        result.setData(jedis.zrevrangeByScoreWithScores(key + RedisConstants.DATA_KEY_DATA, 1, 0));
                    }
                }
                result.setStatus(jedis.get(key + RedisConstants.DATA_KEY_STATUS));
                result.setTotal(valueOf(jedis.get(key + RedisConstants.DATA_KEY_TOTAL)));
            }
        } catch (Exception e) {
            log.error("************Get talent data error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    @Override
    public void saveSummaryReportData(String uuid, List<SummaryReport> resultList) {
        if (resultList == null || resultList.size() == 0) {
            return;
        }
        String key = RedisConstants.DATA_KEY_REPORT + uuid + RedisConstants.DATA_KEY_DATA;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (SummaryReport summaryReport : resultList) {
                    jedis.zadd(key, 0, JsonUtil.toJsonIgnoreNullFields(summaryReport));
                }
                jedis.expire(key, EXPIRE_TIME);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public RedisResponse getDataForReport(String uuid) {
        RedisResponse result = new RedisResponse();
        String key = RedisConstants.DATA_KEY_REPORT + uuid + RedisConstants.DATA_KEY_DATA;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                String dataKey = key + RedisConstants.DATA_KEY_DATA;
                result.setData(jedis.zrevrangeByScoreWithScores(dataKey, 1, 0));
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    @Override
    public void saveDataForReport(String uuid, String data) {
        if (StringUtils.isEmpty(data)) {
            return;
        }
        String key = RedisConstants.DATA_KEY_REPORT + uuid + RedisConstants.DATA_KEY_DATA;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.set(key, data);
                jedis.expire(key, EXPIRE_TIME);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void saveSubmittalsAgingData(String uuid, List<SubmittalsAgingReport> resultList) {
        if (resultList == null || resultList.size() == 0) {
            return;
        }
        String key = RedisConstants.DATA_KEY_REPORT + uuid + RedisConstants.DATA_KEY_DATA;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (SubmittalsAgingReport submittalsAgingReport : resultList) {
                    jedis.zadd(key, 0, JsonUtil.toJsonIgnoreNullFields(submittalsAgingReport));
                }
                jedis.expire(key, EXPIRE_TIME);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public ParserRedisResponse getParserResumeData(String uuid) {
        ParserRedisResponse result = new ParserRedisResponse();
        String key = RedisConstants.DATA_KEY_PARSER + RedisConstants.DATA_KEY_RESUME + uuid;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                // 获取原有数据
                List<byte[]> results = jedis.mget(
                        (key + RedisConstants.DATA_KEY_STATUS).getBytes(),
                        (key + RedisConstants.DATA_KEY_DATA).getBytes()
                );
                if(results != null && results.size() > 0) {
                    if(results.get(0) != null) {
                        result.setStatus(ParseStatus.valueOf(new String(results.get(0), Charset.forName("UTF-8"))));
                    }
                    if(results.get(1) != null) {
                        result.setData(new String(results.get(1), Charset.forName("UTF-8")));
                    }
                }

                // 获取 imagesInfo
                Map<byte[], byte[]> map = jedis.hgetAll((key + RedisConstants.DATA_KEY_IMAGES_INFO).getBytes());
                if (map != null) {
                    Map<String, String> nmap = new HashMap<>();
                    map.forEach((k, v) -> {
                        try {
                            nmap.put(new String(k, "UTF-8"), new String(v, "UTF-8"));
                        } catch (UnsupportedEncodingException e) {
                            log.error("Error decoding map entry", e);
                        }
                    });
                    result.setImagesInfo(Convert.convert(ImagesInfoParser.class, nmap));
                }

                // 获取 document_languages SET 类型数据
                String languagesKey = key + RedisConstants.DATA_KEY_DOCUMENT_LANGUAGES;
                Set<byte[]> languageBytes = jedis.smembers(languagesKey.getBytes());
                if (languageBytes != null && !languageBytes.isEmpty()) {
                    Set<String> languages = new HashSet<>();
                    for (byte[] bytes : languageBytes) {
                        languages.add(new String(bytes, StandardCharsets.UTF_8));
                    }
                    result.setDocumentLanguages(languages);
                }

                // 获取 TTL
                Long ttl = jedis.ttl(key + RedisConstants.DATA_KEY_STATUS);
                result.setTtl(ttl);
            }
        } catch (Exception e) {
            log.error("Error fetching data from Redis", e);
        } finally {
            close(jedis);
        }
        return result;
    }

    @Override
    public ParserRedisResponse getParserResumeStatus(String uuid) {
        String key = RedisConstants.DATA_KEY_PARSER + RedisConstants.DATA_KEY_RESUME + uuid;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                if (jedis.exists(key + RedisConstants.DATA_KEY_METADATA)) {
                    String status = jedis.get(key + RedisConstants.DATA_KEY_STATUS);
                    ParserRedisResponse result = new ParserRedisResponse();
                    if (StringUtils.isNotBlank(status)) {
                        result.setStatus(ParseStatus.valueOf(status));
                    } else {
                        result.setStatus(ParseStatus.STARTED);
                    }
                    return result;
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    @Override
    public ParserRedisResponse getParserJDData(String uuid) {
        ParserRedisResponse result = new ParserRedisResponse();
        String key = RedisConstants.DATA_KEY_PARSER + RedisConstants.DATA_KEY_JD + uuid;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null)
            {
                List<String>  results = jedis.mget(key + RedisConstants.DATA_KEY_STATUS,key + RedisConstants.DATA_KEY_DATA,key + RedisConstants.DATA_KEY_LAST_UPDATE_TIME);
                if(results == null || results.size() == 0) {
                    return result;
                }
                Long ttl = jedis.ttl(key + RedisConstants.DATA_KEY_STATUS);
                result.setStatus(ParseStatus.valueOf(results.get(0)));
                result.setData(results.get(1));
                result.setLastUpdateTime(results.get(2));
                result.setTtl(ttl);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    @Override
    public ParserRedisResponse getParserJDStatus(String uuid) {
        String key = RedisConstants.DATA_KEY_PARSER + RedisConstants.DATA_KEY_JD + uuid;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                if (jedis.exists(key + RedisConstants.DATA_KEY_METADATA)) {
                    String status = jedis.get(key + RedisConstants.DATA_KEY_STATUS);
                    ParserRedisResponse result = new ParserRedisResponse();
                    if (StringUtils.isNotBlank(status)) {
                        result.setStatus(ParseStatus.valueOf(status));
                    } else {
                        result.setStatus(ParseStatus.STARTED);
                    }
                    return result;
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    @Override
    public void saveData(String key, String value, int expireSecond)
    {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null)
            {
                jedis.set(key, value);
                jedis.expire(key, expireSecond);
            }
        } catch (Exception e)
        {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally
        {
            close(jedis);
        }
    }

    @Override
    public ParserResponse getResumeParseStatus(String uuid)
    {
        ParserResponse result = new ParserResponse();
        String key = RedisConstants.DATA_KEY_PARSER + RedisConstants.DATA_KEY_RESUME + uuid;
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null)
            {
                List<byte[]>  results = jedis.mget((key + RedisConstants.DATA_KEY_STATUS).getBytes(),(key + RedisConstants.DATA_KEY_DATA).getBytes());
                if(results == null || results.size() == 0) {
                    return result;
                }
                if(results.get(0) != null) {
                    result.setStatus(ParseStatus.valueOf(new String(results.get(0), Charset.forName("UTF-8"))));
                }
                if(results.get(1) != null) {
                    result.setData(new String(results.get(1), Charset.forName("UTF-8")));
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return result;
    }

    @Override
    public void hset(String key, Map<String, String> set) {
        if (set == null) {
            return;
        }
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Map.Entry<String, String> entry : set.entrySet()) {
                    jedis.hset(key, entry.getKey(), entry.getValue());
                }
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void hset(String key, Map<String, String> set, int expire) {
        if (set == null) {
            return;
        }
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                for (Map.Entry<String, String> entry : set.entrySet()) {
                    jedis.hset(key, entry.getKey(), entry.getValue());
                }
                jedis.expire(key, expire);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void hset(String key, String field, String value) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.hset(key, field, value);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public String hget(String key, String field) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.hget(key, field);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    @Override
    public void hdel(String key, String field) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.hdel(key, field);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public Map<String, String> hgetAll(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.hgetAll(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    @Override
    public void set(String key, String value) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.set(key, value);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void set(String key, String value, Integer expireSecond) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null){
                jedis.set(key, value);
                if (Objects.nonNull(expireSecond)){
                    jedis.expire(key, expireSecond);
                }
            }
        } catch (Exception e){
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally{
            close(jedis);
        }
    }
    @Override
    public Boolean exists(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.exists(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return Boolean.FALSE;
    }

    @Override
    public Long delete(String key) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                return jedis.del(key);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return null;
    }

    @Override
    public void deleteBatch(Collection<String> keys) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                Pipeline pipelined = jedis.pipelined();
                keys.forEach(pipelined::del);
                pipelined.sync();
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public void deleteByKeyPattern(String keyPattern) {
        Jedis jedis = null;
        try {
            jedis = getJedis();
            if (jedis != null) {
                jedis.keys(keyPattern).forEach(jedis::del);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
    }

    @Override
    public Long setNx(String key, String value) {
        Jedis jedis = null;
        Long resultValue = 0L;
        try {
            jedis = getJedis();
            if (jedis != null) {
                resultValue = jedis.setnx(key, value);
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return resultValue;
    }

    public Long setNxAndExpire(String key, String value, Long seconds) {
        Jedis jedis = null;
        Long resultValue = 0L;
        try {
            jedis = getJedis();
            if (jedis != null) {
                String script = "local key = KEYS[1]\n" +
                        "local value = ARGV[1]\n" +
                        "local ttl = tonumber(ARGV[2])\n" +
                        "\n" +
                        "local exists = redis.call('SETNX', key, value)\n" +
                        "\n" +
                        "if exists == 1 then\n" +
                        "    if ttl > 0 then\n" +
                        "        redis.call('EXPIRE', key, ttl)\n" +
                        "    end\n" +
                        "end\n" +
                        "\n" +
                        "return exists\n";
                Object result = jedis.eval(script, 1, key, value, String.valueOf(seconds));
                resultValue = Long.parseLong(String.valueOf(result));
            }
        } catch (Exception e) {
            log.error("************Trying to connect Redis error, error message: {}", e.getMessage());
        } finally {
            close(jedis);
        }
        return resultValue;
    }

}
