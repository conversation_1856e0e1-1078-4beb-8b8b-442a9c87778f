package com.altomni.apn.common.service.location.dto;


import lombok.Data;

import java.io.Serializable;

/**
 * A DTO for the {@link com.altomni.apn.common.domain.location.GeoInfoCN}, {@link com.altomni.apn.common.domain.location.GeoInfoEN} entity.
 */
@Data
public class GeoInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long cityId;
    private String city;
    private String provinceCode;
    private String province;
    private String countryCode;
    private String country;

    //emaily style column
    private Long countyId;
    private String county;
    private Long metroAreaCode;
    private String metroArea;

    private String similarity;

    // attributes in Chinese
    private String cityCN;
    private String provinceCN;
    private String countryCN;

}
