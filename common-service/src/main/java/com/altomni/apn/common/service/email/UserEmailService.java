package com.altomni.apn.common.service.email;

import com.altomni.apn.common.domain.email.enumeration.GmailAliasBindingStatus;
import com.altomni.apn.common.web.rest.email.vm.AlternateEmailVM;

public interface UserEmailService {

    @Deprecated(since = "sso")
    GmailAliasBindingStatus getBindingStatusForCurrentUser();

    @Deprecated(since = "sso")
    boolean needCheckGmailAliasBindingStatus();

    @Deprecated(since = "sso")
    void scheduleCheckRecentConfirmedBindingStatus();

    @Deprecated(since = "sso")
    String requestToBindingGmailAliasForCurrentUser();

    @Deprecated(since = "sso")
    void requestToBindingGmailAlias(String email);

    @Deprecated(since = "sso")
    void confirmBindingGmailAliasForCurrentUser();

    @Deprecated(since = "sso")
    void confirmBindingGmailAlias(String email);

    @Deprecated(since = "sso")
    void removeAllBindingRecords(Long userId);

    void addAlternateEmail(AlternateEmailVM alternateEmailVM);
}
