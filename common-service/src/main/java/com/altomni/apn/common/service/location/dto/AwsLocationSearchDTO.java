package com.altomni.apn.common.service.location.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

@ApiModel
@Data
public class AwsLocationSearchDTO implements Serializable {

    @ApiModelProperty(value = "Search location text.")
    @NotEmpty
    private String text;

    @ApiModelProperty(value = "Maximum number of search results")
    private Integer maxResults;
}
