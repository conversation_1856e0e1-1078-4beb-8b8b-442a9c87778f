package com.altomni.apn.common.domain.email;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.email.enumeration.ErrorCampaignRecordStatus;
import com.altomni.apn.common.domain.email.enumeration.ErrorCampaignRecordType;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "error_campaign_record")
public class ErrorCampaignRecord extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "type")
    private ErrorCampaignRecordType type;

    @Column(name = "campaign_id")
    private Long campaignId;

    @Column(name = "message")
    private String message;

    @Column(name = "status")
    private ErrorCampaignRecordStatus status;

    @Column(name = "error")
    private String error;

    @Column(name = "exchange")
    private String exchange;

    @Column(name = "routing_key")
    private String routingKey;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ErrorCampaignRecordType getType() {
        return type;
    }

    public void setType(ErrorCampaignRecordType type) {
        this.type = type;
    }

    public Long getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Long campaignId) {
        this.campaignId = campaignId;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public ErrorCampaignRecordStatus getStatus() {
        return status;
    }

    public void setStatus(ErrorCampaignRecordStatus status) {
        this.status = status;
    }

    public String getError() {
        return error;
    }

    public void setError(String error) {
        this.error = error;
    }

    public String getExchange() {
        return exchange;
    }

    public void setExchange(String exchange) {
        this.exchange = exchange;
    }

    public String getRoutingKey() {
        return routingKey;
    }

    public void setRoutingKey(String routingKey) {
        this.routingKey = routingKey;
    }

    public ErrorCampaignRecord() {
    }

    public ErrorCampaignRecord(ErrorCampaignRecordType type, Long campaignId, String message, ErrorCampaignRecordStatus status, String error, String exchange, String routingKey) {
        this.type = type;
        this.campaignId = campaignId;
        this.message = message;
        this.status = status;
        this.error = error;
        this.exchange = exchange;
        this.routingKey = routingKey;
    }
}
