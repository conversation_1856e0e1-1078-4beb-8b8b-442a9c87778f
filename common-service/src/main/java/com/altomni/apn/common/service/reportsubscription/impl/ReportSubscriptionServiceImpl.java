package com.altomni.apn.common.service.reportsubscription.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.reportsubscription.ReportSubscription;
import com.altomni.apn.common.domain.reportsubscription.ReportSubscriptionPushMethod;
import com.altomni.apn.common.domain.reportsubscription.ReportSubscriptionRecipient;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.lark.LarkAuthRequestDTO;
import com.altomni.apn.common.dto.reportsubscription.ReportSubscriptionConfigDTO;
import com.altomni.apn.common.dto.reportsubscription.ReportSubscriptionDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnParamDTO;
import com.altomni.apn.common.enumeration.reportSubscriptions.ActiveType;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushTimeType;
import com.altomni.apn.common.enumeration.reportSubscriptions.ReportType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.repository.reportsubscription.ReportSubscriptionCustomRepository;
import com.altomni.apn.common.repository.reportsubscription.ReportSubscriptionPushMethodRepository;
import com.altomni.apn.common.repository.reportsubscription.ReportSubscriptionRecipientRepository;
import com.altomni.apn.common.repository.reportsubscription.ReportSubscriptionRepository;
import com.altomni.apn.common.repository.xxljob.XxlJobRelationRepository;
import com.altomni.apn.common.service.lark.LarkClient;
import com.altomni.apn.common.service.reportsubscription.ReportSubscriptionService;
import com.altomni.apn.common.service.user.UserService;
import com.altomni.apn.common.service.xxljob.XxlJobService;
import com.altomni.apn.common.subscription.DeleteSubscriptionBatchDTO;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.reportsubscription.ReportSubscriptionSimplifyVO;
import com.altomni.apn.common.vo.reportsubscription.ReportSubscriptionVO;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service("reportSubscriptionService")
public class ReportSubscriptionServiceImpl implements ReportSubscriptionService {

    @Resource
    private UserService userService;

    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private XxlJobRelationRepository xxlJobRelationRepository;

    @Resource
    private ReportSubscriptionRepository reportSubscriptionRepository;

    @Resource
    private ReportSubscriptionCustomRepository reportSubscriptionCustomRepository;

    @Resource
    private ReportSubscriptionRecipientRepository reportSubscriptionRecipientRepository;

    @Resource
    private ReportSubscriptionPushMethodRepository reportSubscriptionPushMethodRepository;

    @Resource
    private LarkClient larkClient;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createReportSubscription(ReportSubscriptionDTO dto) {
        ReportSubscription reportSubscription = new ReportSubscription();
        BeanUtil.copyProperties(dto, reportSubscription);
        reportSubscription.setTenantId(SecurityUtils.getTenantId());
        reportSubscription.setDataPermissionJson(dto.getDataPermission());
        reportSubscriptionRepository.save(reportSubscription);
        //设置推送和接受人
        createRecipientAndPushMethod(dto, reportSubscription.getId());
        //创建xxl-job 去定时推送消息
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            createReportSubscriptionXxlJobs(dto, reportSubscription.getId());
        });
    }

    @Override
    public void createReportSubscriptionForAutoGenerate(ReportSubscriptionDTO dto) {
        ReportSubscription reportSubscription = new ReportSubscription();
        BeanUtil.copyProperties(dto, reportSubscription);
        reportSubscription.setDataPermissionJson(dto.getDataPermission());
        //判断是否生成过该种report的默认订阅，如果存在就不做任何操作
        Integer existAutoGenerateReportSubscription = reportSubscriptionRepository.isExistAutoGenerateReportSubscription(dto.getTenantId(), dto.getReportType().toDbValue(), dto.getPermissionUserId());
        if (existAutoGenerateReportSubscription >= 1){
            return;
        }
        reportSubscriptionRepository.save(reportSubscription);
        //设置推送和接受人
        createRecipientAndPushMethod(dto, reportSubscription.getId());
        //创建xxl-job 去定时推送消息
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            createReportSubscriptionXxlJobs(dto, reportSubscription.getId());
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReportSubscription(ReportSubscriptionDTO dto) {
        ReportSubscription reportSubscription = new ReportSubscription();
        BeanUtil.copyProperties(dto, reportSubscription);
        reportSubscription.setTenantId(SecurityUtils.getTenantId());
        reportSubscription.setDataPermissionJson(dto.getDataPermission());
        reportSubscriptionRepository.save(reportSubscription);
        //删除附属表数据,重新重新创建
        reportSubscriptionRecipientRepository.deleteBySubscriptionId(dto.getId());
        reportSubscriptionPushMethodRepository.deleteBySubscriptionId(dto.getId());
        createRecipientAndPushMethod(dto, reportSubscription.getId());
        //修改定时任务去发送消息
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
           //delete
            deleteReportSubscriptionXxlJobs(dto.getId());
            //重新创建
            createReportSubscriptionXxlJobs(dto, reportSubscription.getId());
        });
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReportSubscriptionById(Long id) {
        reportSubscriptionRepository.deleteById(id);
        reportSubscriptionRecipientRepository.deleteBySubscriptionId(id);
        reportSubscriptionPushMethodRepository.deleteBySubscriptionId(id);
        //删除定时任务
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            try {
                SecurityContextHolder.setContext(context);
                deleteReportSubscriptionXxlJobs(id);
            }catch (Exception e){
                log.error("deleteReportSubscriptionById error:{}", e);
            }
        });
    }

    @Autowired
    private ReportSubscriptionService selfProxy;

    @Override
    public void deleteReportSubscriptionBatch(DeleteSubscriptionBatchDTO deleteSubscriptionBatchDTO) {
        List<Long> ids = reportSubscriptionRepository.findIdByUserIdsAndReportType(deleteSubscriptionBatchDTO.getTenantId(),deleteSubscriptionBatchDTO.getUserIds(), deleteSubscriptionBatchDTO.getReportType().toDbValue());
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        for(Long id : ids){
            selfProxy.deleteReportSubscriptionById(id);
        }
    }

    @Override
    public void updateReportSubscriptionActiveStatus(Long id, ActiveType active) {
        reportSubscriptionRepository.updateActiveStatusById(id, active.toDbValue());
        //根据状态删除或者添加定时任务 都需要先删除,防止极端情况
        deleteReportSubscriptionXxlJobs(id);
        if (active == ActiveType.ACTIVE) {
            ReportSubscriptionVO detailVo = reportSubscriptionCustomRepository.getReportSubscriptionDetailById(id);
            ReportSubscriptionDTO reportSubscriptionDTO = new ReportSubscriptionDTO();
            BeanUtil.copyProperties(detailVo, reportSubscriptionDTO, true);
            createReportSubscriptionXxlJobs(reportSubscriptionDTO, detailVo.getId());
        }
    }

    @Override
    public List<ReportSubscriptionSimplifyVO> getReportSubscriptionList(ReportType reportType) {
        return reportSubscriptionCustomRepository.getReportSubscriptionList(SecurityUtils.getUserId(), SecurityUtils.getTenantId(), reportType);
    }

    @Override
    public ReportSubscriptionVO getReportSubscriptionDetailById(Long id) {
        return reportSubscriptionCustomRepository.getReportSubscriptionDetailById(id);
    }

    private void createRecipientAndPushMethod(ReportSubscriptionDTO reportSubscriptionDTO, Long subscriptionId) {
        Optional.of(reportSubscriptionDTO.getRecipientList()).ifPresent(recipientList -> {
            List<ReportSubscriptionRecipient> reportSubscriptionRecipientList = new ArrayList<>();
            recipientList.forEach(userId -> {
                ReportSubscriptionRecipient reportSubscriptionRecipient = new ReportSubscriptionRecipient();
                reportSubscriptionRecipient.setSubscriptionId(subscriptionId);
                reportSubscriptionRecipient.setUserId(userId);
                reportSubscriptionRecipientList.add(reportSubscriptionRecipient);
            });
            reportSubscriptionRecipientRepository.saveAll(reportSubscriptionRecipientList);
        });
        Optional.of(reportSubscriptionDTO.getPushMethodList()).ifPresent(pushMethodList -> {
            List<ReportSubscriptionPushMethod> reportSubscriptionPushMethodList = new ArrayList<>();
            pushMethodList.forEach(pushMethod -> {
                ReportSubscriptionPushMethod reportSubscriptionPushMethod = new ReportSubscriptionPushMethod();
                reportSubscriptionPushMethod.setSubscriptionId(subscriptionId);
                reportSubscriptionPushMethod.setPushMethod(pushMethod);
                reportSubscriptionPushMethodList.add(reportSubscriptionPushMethod);
            });
            reportSubscriptionPushMethodRepository.saveAll(reportSubscriptionPushMethodList);
        });
    }

    private void deleteReportSubscriptionXxlJobs(Long id) {
        List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceId(XxlJobRelationTypeEnum.REPORT_SUBSCRIPTION, id);
        if (CollUtil.isNotEmpty(xxlJobRelationList)) {
            xxlJobService.deleteXxlJobIdList(xxlJobRelationList.stream().map(XxlJobRelation::getXxlJobId).toList());
        }
    }

    private void createReportSubscriptionXxlJobs(ReportSubscriptionDTO dto, Long subscriptionId) {
        List<UserTimeZoneVO> userTimeZoneVOList = userService.getTimezoneListByUserIdList(dto.getRecipientList()).getBody();
        if (CollUtil.isNotEmpty(userTimeZoneVOList)) {
            List<XxlJobApnDTO> xxlJobApnDTOList = new ArrayList<>();
            userTimeZoneVOList.forEach(userTimeZoneVO -> {
                if (StrUtil.isBlank(userTimeZoneVO.getCustomTimezone())) {
                    return;
                }
                XxlJobApnDTO xxlJobApnDTO = new XxlJobApnDTO();
                xxlJobApnDTO.setJobDesc("report subscription #subscriptionId = " + subscriptionId + " #userId=" + userTimeZoneVO.getUserId());
                XxlJobApnParamDTO xxlJobApnParamDTO = new XxlJobApnParamDTO();
                xxlJobApnParamDTO.setXxlJobType(XxlJobRelationTypeEnum.REPORT_SUBSCRIPTION);
                xxlJobApnParamDTO.setReferenceId(subscriptionId);
                xxlJobApnParamDTO.setUserId(userTimeZoneVO.getUserId());
                xxlJobApnParamDTO.setTenantId(SecurityUtils.getTenantId());
                xxlJobApnParamDTO.setTimezone(userTimeZoneVO.getCustomTimezone());
                xxlJobApnParamDTO.setSendTime(null);
                xxlJobApnParamDTO.setCron(DateUtil.generateCronExpression(dto.getPushTimeType(), dto.getDayOfWeek(), dto.getDayOfMonth(), dto.getSendTime(), userTimeZoneVO.getCustomTimezone()));
                xxlJobApnParamDTO.setReminderConfig(JSONUtil.toJsonStr(new ReportSubscriptionConfigDTO(dto.getPushTimeType(), dto.getDayOfWeek(), dto.getDayOfMonth(), dto.getSendTime())));
                xxlJobApnDTO.setXxlJobApnParamDTO(xxlJobApnParamDTO);
                Map<String, Object> paramMap = new JSONObject(xxlJobApnParamDTO);
                paramMap.put("subscriptionId", subscriptionId);
                xxlJobApnParamDTO.setXxlJobParam(paramMap);
                xxlJobApnDTOList.add(xxlJobApnDTO);
                log.info("create report subscription id = {}, userId = {}, cron = {}", subscriptionId, userTimeZoneVO.getUserId(), xxlJobApnParamDTO.getCron());
            });
            log.info("create report subscription = {}", subscriptionId);
            xxlJobService.createXxlJobs(xxlJobApnDTOList);
        }
    }

    @Override
    @Transactional
    public ReportType unsubscribeReportById(Integer id) {
        XxlJobRelation xxlJobRelation = xxlJobRelationRepository.findXxlJobRelationByXxlJobId(id);
        if (Objects.isNull(xxlJobRelation)) {
            return null;
        }
        if (!XxlJobRelationTypeEnum.REPORT_SUBSCRIPTION.equals(xxlJobRelation.getType()) || !SecurityUtils.getTenantId().equals(xxlJobRelation.getTenantId()) || !SecurityUtils.getUserId().equals(xxlJobRelation.getUserId())) {
            throw new CustomParameterizedException("Cannot unsubscribe report subscription with id " + id);
        }

        ReportSubscriptionVO reportSubscriptionVO = reportSubscriptionCustomRepository.getReportSubscriptionDetailById(xxlJobRelation.getReferenceId());
        List<Long> recipientIds = reportSubscriptionVO.getRecipientList();
        if (CollUtil.isNotEmpty(recipientIds) && recipientIds.size() == 1 && recipientIds.contains(xxlJobRelation.getUserId())) {
            this.updateReportSubscriptionActiveStatus(xxlJobRelation.getReferenceId(), ActiveType.INACTIVE);
        } else {
            xxlJobService.deleteXxlJob(id);
            reportSubscriptionRecipientRepository.deleteBySubscriptionIdAndUserId(xxlJobRelation.getReferenceId(), xxlJobRelation.getUserId());
        }

        return reportSubscriptionVO.getReportType();
    }

    @Override
    public void getLarkUserAccessTokenByAuthorizationCode(LarkAuthRequestDTO larkAuthRequestDTO) {
        String userAccessToken = larkClient.getUserAccessTokenByUserAuthorizationCode(SecurityUtils.getUserId(), larkAuthRequestDTO.getCode());
        log.info("[getLarkUserAccessTokenByAuthorizationCode] for userId: {} with code: {}, access token is: {}", SecurityUtils.getUserId(), larkAuthRequestDTO.getCode(), userAccessToken);
    }
}
