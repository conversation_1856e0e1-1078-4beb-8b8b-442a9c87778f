package com.altomni.apn.common.service.email.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.config.env.email.EmailAppProperties;
import com.altomni.apn.common.domain.email.enumeration.*;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.email.EmailConstans;
import com.altomni.apn.common.service.email.EmailyService;
import com.altomni.apn.common.service.email.dto.*;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.ipg.resourceserver.client.ClientTokenHolder;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Service
public class EmailyServiceImpl implements EmailyService {

    private final Logger log = LoggerFactory.getLogger(EmailyServiceImpl.class);

    @Resource
    private EmailAppProperties emailAppProperties;
    @Resource
    private HttpService httpService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

//    private final AsyncRecordRepository asyncRecordRepository;

    private final static String HEADERS_AUTHORIZATION_KEY = "Authorization";

    private final static String EMAILY_GROUP_CREATE_URL = "/api/v2/audience/mongo";

    private final static String URL_SEPATATOR = "/";

    private final static String URL_DELETE = "/delete";

    private final static String URL_ADD_AUDIENCE = "/add-audience-with-response";

    private final static String URL_ADD_AUDIENCE_ESID = "/esId";

    private final static String URL_ADD_AUDIENCE_SEARCH_AUDIENCE= "/add-search-audience";

    private final static String URL_MAX_GROUP_SIZE = "maxGroupSize=";

    private final static String URL_AUDIENCE_UPDATE = "/update-by-data-source-id";

    private final static String URL_MOVE = "/move";

    private final static String URL_COPY = "/copy";

    private final static String URL_MERGE = "/merge";

    private final static String SPACE = " ";

    private final static String URL_RECOVERY = "/unarchive";

    private final static String EMAILY_GROUP_CREATE_URL_V1 = "/api/v1/audience";

    private final static String EMAILY_GROUP_SORT_CREATEDDATE_DESC = "sort=createdDate,desc";

    private final static String EMAILY_GROUP_SORT_LAST_MODIFIED_DATE_DESC = "sort=last_modified_date,desc";

    private final static String EMAILY_GROUP_SORT_CREATEDDATE_ASC = "sort=createdDate,asc";

    private final static String URL_ARCHIVED = "/archived";

    private final static String URL_SEARCH_PAGE = "page=";

    private final static String URL_SEARCH_SIZE = "size=";

    private final static String URL_SEARCH_SORT = "sort=";

    private final static String URL_SEARCH_NAME = "name=";

    private final static String URL_SEARCH_STATUS = "status=";

    private final static String URL_SEARCH_DATASOURCE = "dataSource=";

    private final static String URL_SEARCH_QUESTION_MARK = "?";

    private final static String URL_SEARCH_AND = "&";

    private final static String URL_SEARCH = "search=";

    private final static String URL_SEARCH_YYPE = "type=";

    private final static String URL_INFO = "/info";

    private final static String URL_SEARCH_YYPE_VALUE = "MONGO_LIST";

    private final static String EMAILY_CAMPAIGN_URL_V1 = "/api/v1/campaigns";

    private final static String EMAILY_CAMPAIGN_DRAFT_URL = "/draft";

    private final static String EMAILY_TEMPLATE_URL_V1 = "/api/v1/template";

    private final static String EMAILY_COMPANY_URL_V1 = "/api/v1/company";

    private final static String URL_ARCHIVE = "/archive";

    private final static String URL_UNARCHIVE = "/unarchive";

    private final static String URL_SEND = "/send";

    private final static String URL_DENY = "/deny";

    private final static String URL_SAVED= "/saved";

    private final static String URL_BUILT_IN= "/built-in";

    private final static String URL_TAGS = "/tags";

    private final static String ALL = "all";

    private final static String URL_DEACTIVATE = "/deactivate";

    private final static String URL_TEST = "/test";

    private final static String URL_NAME = "/name";

    private final static String URL_ACCOUNT_LIMIT = "/daily-limit";




    private String syncGroupToEmailyBaseUrl() {
        return emailAppProperties.getEmailyBaseUrl();
    }

    private String syncGroupToEmailyCreateUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL;
    }

    private String syncGroupToEmailyUpdateUrl(Long emailyGroupId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEPATATOR + emailyGroupId;
    }

    private String syncGroupToEmailyDeleteUrl(Long emailyGroupId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEPATATOR + emailyGroupId + URL_DELETE;
    }

    private String syncAudienceToEmailyAddUrl(Long emailyGroupId, int maxGroupSize) {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEPATATOR + emailyGroupId + URL_ADD_AUDIENCE + URL_SEARCH_QUESTION_MARK + URL_MAX_GROUP_SIZE + maxGroupSize;
    }

    private String syncAudienceToEmailyByEsIdAddUrl(Long emailyGroupId, int maxGroupSize) {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEPATATOR + emailyGroupId + URL_ADD_AUDIENCE_ESID + URL_SEARCH_QUESTION_MARK + URL_MAX_GROUP_SIZE + maxGroupSize;
    }

    private String syncAudienceToEmailyFromCommonPoolAddUrl(Long emailyGroupId, int maxGroupSize) {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEPATATOR + emailyGroupId + URL_ADD_AUDIENCE_SEARCH_AUDIENCE + URL_SEARCH_QUESTION_MARK + URL_MAX_GROUP_SIZE + maxGroupSize;
    }

    private String syncAudienceToEmailyMergeUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_MERGE;
    }

    private String syncAudienceToEmailyMoveUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_MOVE;
    }

    private String syncAudienceToEmailyCopyUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_COPY;
    }

    private String syncGroupToEmailyRecoveryUrl(Long emailyGroupId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEPATATOR + emailyGroupId + URL_RECOVERY;
    }

    private String searchInActiveGroupFromEmilyUrl(String name, Pageable pageable) {
        if (ObjectUtil.isEmpty(name)) {
            return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL_V1 + URL_ARCHIVED + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + EMAILY_GROUP_SORT_CREATEDDATE_DESC;
        } else {
            return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL_V1 + URL_ARCHIVED + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH + name + URL_SEARCH_AND + EMAILY_GROUP_SORT_CREATEDDATE_DESC;
        }
    }

    private String searchActiveGroupFromEmilyUrl(String name, Pageable pageable) {
        if (ObjectUtil.isEmpty(name)) {
            return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + EMAILY_GROUP_SORT_CREATEDDATE_DESC;
        } else {
            return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH + name + URL_SEARCH_AND + EMAILY_GROUP_SORT_CREATEDDATE_DESC;
        }
    }


    private String searchAllGroupFromEmilyUrl(String name) {
        if (ObjectUtil.isEmpty(name)) {
            return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL;
        } else {
            return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEARCH_QUESTION_MARK + URL_SEARCH + name;
        }
    }

    private String searchGroupAudienceFromEmailyUrl(Long emailyGroupId, String source, Pageable pageable) {
        if (ObjectUtil.isEmpty(source)) {
            return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEPATATOR + emailyGroupId + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + EMAILY_GROUP_SORT_LAST_MODIFIED_DATE_DESC;
        } else {
            return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEPATATOR + emailyGroupId + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH_DATASOURCE + source + URL_SEARCH_AND + EMAILY_GROUP_SORT_LAST_MODIFIED_DATE_DESC;
        }
    }

    private String queryGroupFromEmilyUrl(Long emailyGroupId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL_V1 + URL_SEPATATOR + emailyGroupId + URL_SEARCH_QUESTION_MARK + URL_SEARCH_YYPE + URL_SEARCH_YYPE_VALUE;
    }

    private String queryGroupFromInfoEmilyUrl(Long emailyGroupId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_SEPATATOR + emailyGroupId + URL_INFO;
    }

    private String syncDrftCampaignToEmailyCreateUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + EMAILY_CAMPAIGN_DRAFT_URL;
    }

    private String syncDrftCampaignToEmailyUpdateUrl(Long emailyCampaignId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_SEPATATOR + emailyCampaignId;
    }

    private String searchCampaignFromEmailyUrl(EmailCampaignArchiveStatus archiveStatus, List<EmailCampaignStatus> statusList, String search, String sort, Pageable pageable) {
        if (archiveStatus != EmailCampaignArchiveStatus.ARCHIVED) {
            return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_SEARCH_QUESTION_MARK + (ObjectUtil.isEmpty(search) ? "" : (URL_SEARCH + search) + URL_SEARCH_AND) +
                (CollectionUtils.isEmpty(statusList) ? "" : URL_SEARCH_STATUS + StringUtils.join(statusList, ",") + URL_SEARCH_AND) +
                URL_SEARCH_PAGE + pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + (ObjectUtil.isEmpty(sort) ? "" : (URL_SEARCH_AND + URL_SEARCH_SORT + sort));
        } else {
            return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_ARCHIVED + URL_SEARCH_QUESTION_MARK + (ObjectUtil.isEmpty(search) ? "" : (URL_SEARCH + search) + URL_SEARCH_AND) +
                (CollectionUtils.isEmpty(statusList) ? "" : URL_SEARCH_STATUS + StringUtils.join(statusList, ",") + URL_SEARCH_AND) +
                URL_SEARCH_PAGE + pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + (ObjectUtil.isEmpty(sort) ? "" : (URL_SEARCH_AND + URL_SEARCH_SORT + sort));
        }
    }

    private String searchCampaignFromEmailyByNamerl(String emailyCampaignName) {
        return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_SEARCH_QUESTION_MARK + URL_SEARCH + emailyCampaignName;
    }

    private String syncDrftCampaignToEmailyPendingUrl(Long emailyCampaignId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + EMAILY_CAMPAIGN_DRAFT_URL + URL_SEPATATOR + emailyCampaignId;
    }

    private String queryTemplateFromEmailyUrl(Long templateId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_SEPATATOR + templateId;
    }

    private String syncTalentToEmailyUpdateUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_GROUP_CREATE_URL + URL_AUDIENCE_UPDATE;
    }

    private String syncCampaignToEmailyArchiveUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_ARCHIVE;
    }

    private String syncCampaignToEmailyUnArchiveUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_UNARCHIVE;
    }

    private String syncCampaignToEmailyApproveUrl(Long emailyCampaignId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_SEND + URL_SEPATATOR + emailyCampaignId;
    }

    private String syncCampaignToEmailyDenyUrl(Long emailyCampaignId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_DENY + URL_SEPATATOR + emailyCampaignId;
    }

    private String sendCampaignToEmailyTestUrl(Long emailyCampaignId) {
        return syncGroupToEmailyBaseUrl() + EMAILY_CAMPAIGN_URL_V1 + URL_SEND + URL_SEPATATOR + emailyCampaignId + URL_TEST;
    }

    private String searchTemplatesFromEmailyUrl(EmailTemplateType type, Long tagsId, String name, Pageable pageable, String sort) {
        if (ObjectUtil.isEmpty(tagsId)) {
            return syncGroupToEmailyBaseUrl() + EMAILY_TEMPLATE_URL_V1 + (type == EmailTemplateType.USER_SAVED ? URL_SAVED : URL_BUILT_IN) + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber()
                + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH_SORT + sort + (ObjectUtil.isNotEmpty(name) ? (URL_SEARCH_AND + URL_SEARCH_NAME + name) : "");
        } else {
            return syncGroupToEmailyBaseUrl() + EMAILY_TEMPLATE_URL_V1 + (type == EmailTemplateType.USER_SAVED ? URL_SAVED : URL_BUILT_IN) + URL_SEPATATOR + tagsId + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE +
                pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH_SORT + sort + (ObjectUtil.isNotEmpty(name) ? (URL_SEARCH_AND + URL_SEARCH_NAME + name) : "");
        }
    }

    private String searchTemplateTagsFromEmailyUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_TAGS;
    }

    private String syncTemplateToEmailyCreateUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_TEMPLATE_URL_V1;
    }

    private String syncTemplateToEmailyUpdateUrl(Long id) {
        return syncGroupToEmailyBaseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_SEPATATOR + id;
    }

    private String syncTemplateToEmailyDeactivateUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_DEACTIVATE;
    }

    private String queryTemplateFromEmailyByNameUrl(String name) {
        return syncGroupToEmailyBaseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_SAVED + URL_NAME + URL_SEARCH_QUESTION_MARK + URL_SEARCH_NAME + name;
    }

    private String queryCompanyLimitFromEmailyUrl() {
        return syncGroupToEmailyBaseUrl() + EMAILY_COMPANY_URL_V1 + URL_ACCOUNT_LIMIT;
    }

    @Override
    public HttpResponse createMongoAudienceGroup(Long id, String name) throws IOException {
        if (ObjectUtil.isEmpty(name)) {
            log.error("[EmailService: EmailyServiceImpl] create a mongo audience group to Emaily error, parameter error, the emaily group name cannot be empty. hitalentGroupId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        MongoAudience mongoAudience = new MongoAudience();
        mongoAudience.setName(name);
        String groupParamStr = JSON.toJSONString(mongoAudience, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncGroupToEmailyCreateUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] create a mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] create a mongo audience group to Emaily error and response is null, groupId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse createMongoAudienceGroup(String name) throws IOException {
        if (ObjectUtil.isEmpty(name)) {
            log.error("[EmailService: EmailyServiceImpl] create a mongo audience group to Emaily error, parameter error, the emaily group name cannot be empty. groupName: {}", name);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        MongoAudience mongoAudience = new MongoAudience();
        mongoAudience.setName(name);
        String groupParamStr = JSON.toJSONString(mongoAudience, SerializerFeature.WriteDateUseDateFormat);
        System.out.println(syncGroupToEmailyCreateUrl());
        HttpResponse response = httpService.post(syncGroupToEmailyCreateUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] create a mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] create a mongo audience group to Emaily error and response is null, groupName: {}", SecurityUtils.getUserId(), name);
        }
        return response;
    }

    @Override
    public HttpResponse updateMongoAudienceGroup(Long id, Long emailyGroupId, String name) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId) || ObjectUtil.isEmpty(name)) {
            log.error("[EmailService: EmailyServiceImpl] update a mongo audience group to Emaily error, parameter error, the emaily group id and name cannot be empty. hitalentGroupId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        MongoAudience mongoAudience = new MongoAudience();
        mongoAudience.setName(name);
        String groupParamStr = JSON.toJSONString(mongoAudience, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(syncGroupToEmailyUpdateUrl(emailyGroupId), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] update a mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] update a mongo audience group to Emaily error and response is null, hitalentGroupId: {}, emailyGroupId: {}", SecurityUtils.getUserId(), id, emailyGroupId);
        }
        return response;

    }

    @Override
    public HttpResponse updateMongoAudienceGroup(Long emailyGroupId, String name) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId) || ObjectUtil.isEmpty(name)) {
            log.error("[EmailService: EmailyServiceImpl] update a mongo audience group to Emaily error, parameter error, the emaily group id and name cannot be empty. emailyGroupId: {}", emailyGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        MongoAudience mongoAudience = new MongoAudience();
        mongoAudience.setName(name);
        String groupParamStr = JSON.toJSONString(mongoAudience, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(syncGroupToEmailyUpdateUrl(emailyGroupId), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] update a mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] update a mongo audience group to Emaily error and response is null, emailyGroupId: {}, emailyGroupName: {}", SecurityUtils.getUserId(), emailyGroupId, name);
        }
        return response;
    }

    @Override
    public HttpResponse deleteAudienceByGroupIdAndAudienceId(Long id, Long emailyGroupId, List<String> emailyTalentIdList) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId) || CollectionUtils.isEmpty(emailyTalentIdList)) {
            log.error("[EmailService: EmailyServiceImpl] delete a mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. hitalentGroupId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(emailyTalentIdList, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncGroupToEmailyDeleteUrl(emailyGroupId), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] delete a mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] delete a mongo audience group to Emaily error and response is null, hitalentGroupId: {}, emailyGroupId: {}", SecurityUtils.getUserId(), id, emailyGroupId);
        }
        return response;

    }

    @Override
    public HttpResponse deleteAudienceByGroupIdAndAudienceId(Long emailyGroupId) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId)) {
            log.error("[EmailService: EmailyServiceImpl] delete a mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. emailyGroupId: {}", emailyGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.delete(syncGroupToEmailyUpdateUrl(emailyGroupId), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.NO_CONTENT.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] delete a mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] delete a mongo audience group to Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), emailyGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse recoveryAudienceByGroupIdAndAudienceId(Long emailyGroupId) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId)) {
            log.error("[EmailService: EmailyServiceImpl] recovery a mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. emailyGroupId: {}", emailyGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.put(syncGroupToEmailyRecoveryUrl(emailyGroupId), getRequestHeaders(), "");

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.NO_CONTENT.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] recovery a mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] recovery a mongo audience group to Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), emailyGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse getAudienceGroupFromEmaily(Long emailyGroupId) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId)) {
            log.error("[EmailService: EmailyServiceImpl] get a mongo audience group from Emaily error, parameter error, the emaily group id cannot be empty. emailyGroupId: {}", emailyGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(queryGroupFromEmilyUrl(emailyGroupId), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] get a mongo audience group from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] get a mongo audience group from Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), emailyGroupId);
        }
        return response;

    }

    @Override
    public HttpResponse queryAudienceGroupInfoFromEmaily(Long emailyGroupId) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId)) {
            log.error("[EmailService: EmailyServiceImpl] query a mongo audience group info from Emaily error, parameter error, the emaily group id cannot be empty. emailyGroupId: {}", emailyGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(queryGroupFromInfoEmilyUrl(emailyGroupId), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] query a mongo audience group info from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] query a mongo audience group info from Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), emailyGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse searchAllActiveMongoAudienceGroupList(String name, TalentGroupActive talentGroupActive) throws IOException {

        HttpResponse response = httpService.get(searchAllGroupFromEmilyUrl(name), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] search all mongo audience group list from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] search all mongo audience group list from Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse searchInActiveMongoAudienceGroupList(String name, Pageable pageable) throws IOException {
        HttpResponse response = httpService.get(searchInActiveGroupFromEmilyUrl(name, pageable), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] search inActive mongo audience group list from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] search inActive mongo audience group list from Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse searchActiveMongoAudienceGroupList(String name, Pageable pageable) throws IOException {
        HttpResponse response = httpService.get(searchActiveGroupFromEmilyUrl(name, pageable), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] search active mongo audience group list from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] search active mongo audience group list from Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse searchMongoAudienceListByGroupId(Long emailyGroupId, String source, Pageable pageable) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId)) {
            log.error("[EmailService: EmailyServiceImpl] search mongo audiences from group id error, parameter error, the emaily group id cannot be empty. emailyGroupId: {}", emailyGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(searchGroupAudienceFromEmailyUrl(emailyGroupId, source, pageable), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] search mongo audiences from group id error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] search mongo audiences from group id error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), emailyGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse addAudienceToGroup(Long id, Long emailyGroupId, List<MongoAudience> emailyTalentList, int maxGroupSize) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId) || CollectionUtils.isEmpty(emailyTalentList)) {
            log.error("[EmailService: EmailyServiceImpl] add audience to mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. hitalentGroupId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(emailyTalentList, SerializerFeature.WriteDateUseDateFormat);
        String url = syncAudienceToEmailyAddUrl(emailyGroupId, maxGroupSize);
        if (emailyTalentList.get(0).getDataSource() == ApplicationSource.AI_RECOMMENDATION || emailyTalentList.get(0).getDataSource() == ApplicationSource.KEYWORD_SEARCH) {
            url = syncAudienceToEmailyByEsIdAddUrl(emailyGroupId, maxGroupSize);
        }
        HttpResponse response = httpService.post(url, getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] add audience to mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] add audience to mongo audience group to Emaily error and response is null, hitalentGroupId: {}, emailyGroupId: {}", SecurityUtils.getUserId(), id, emailyGroupId);
        }
        return response;

    }

    @Override
    public HttpResponse addAudienceToGroupFromCommonPool(Long id, Long emailyGroupId, SearchConditionDTOV2 condition, int maxGroupSize) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId)) {
            log.error("[EmailService: EmailyServiceImpl] add common audience to mongo audience group to Emaily error, parameter error, the emaily group id cannot be empty. hitalentGroupId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(condition, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncAudienceToEmailyFromCommonPoolAddUrl(emailyGroupId, maxGroupSize), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] add common audience to mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] add common audience to mongo audience group to Emaily error and response is null, hitalentGroupId: {}, emailyGroupId: {}", SecurityUtils.getUserId(), id, emailyGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse mergeAudienceToGroup(Long targetGroupId, EmailyGroupMergeDTO emailyGroupMergeDTO) throws IOException {
        if (ObjectUtil.isEmpty(targetGroupId) || ObjectUtil.isEmpty(emailyGroupMergeDTO)) {
            log.error("[EmailService: EmailyServiceImpl] merge audiences to mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. hitalentGroupId: {}", targetGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(emailyGroupMergeDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncAudienceToEmailyMergeUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] merge audiences to mongo audience group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, targetGroupId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] merge audiences to mongo audience group to Emaily error and response is null, hitalentGroupId: {}", SecurityUtils.getUserId(), targetGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse moveAudienceToGroup(Long sourceGroupId, Long targetGroupId, Long emailySourceGroupId, Long emailyTargetGroupId, List<String> emailyTalentIdList, int maxGroupSize) throws IOException {
        if (ObjectUtil.isEmpty(emailySourceGroupId) || ObjectUtil.isEmpty(emailyTargetGroupId) || CollectionUtils.isEmpty(emailyTalentIdList)) {
            log.error("[EmailService: EmailyServiceImpl] move a mongo audiences to other group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. source: {}, target: {}", sourceGroupId, targetGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(new MongoAudienceMove(emailySourceGroupId, emailyTargetGroupId, emailyTalentIdList, maxGroupSize), SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncAudienceToEmailyMoveUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] move a mongo audiences to other group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, sourceGroupId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, targetGroupId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] move a mongo audiences to other group to Emaily error and response is null, source: {}, target: {}", SecurityUtils.getUserId(), sourceGroupId, targetGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse copyAudienceToGroup(Long sourceGroupId, Long targetGroupId, Long emailySourceGroupId, Long emailyTargetGroupId, List<String> emailyTalentIdList, int maxGroupSize) throws IOException {
        if (ObjectUtil.isEmpty(emailySourceGroupId) || ObjectUtil.isEmpty(emailyTargetGroupId) || CollectionUtils.isEmpty(emailyTalentIdList)) {
            log.error("[EmailService: EmailyServiceImpl] copy a mongo audiences to other group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. source: {}, target: {}", sourceGroupId, targetGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(new MongoAudienceMove(emailySourceGroupId, emailyTargetGroupId, emailyTalentIdList, maxGroupSize), SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncAudienceToEmailyCopyUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] copy a mongo audiences to other group to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, targetGroupId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] copy a mongo audiences to other group to Emaily error and response is null, source: {}, target: {}", SecurityUtils.getUserId(), sourceGroupId, targetGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse createEmailCampaign(Long id, EmailyAssociatedCampaign emailyCampaign) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaign) || ObjectUtil.isEmpty(emailyCampaign.getName())) {
            log.error("[EmailService: EmailyServiceImpl] create a email campaign to Emaily error, parameter error, the email campaign name cannot be empty. hitalent campaignId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(emailyCampaign, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncDrftCampaignToEmailyCreateUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] create a email campaign to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] create a email campaign to Emaily error and response is null, campaignId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse updateEmailCampaign(Long id, Long emailyCampaignId, EmailyAssociatedCampaign emailyCampaign) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaign) || ObjectUtil.isEmpty(emailyCampaignId)) {
            log.error("[EmailService: EmailyServiceImpl] update a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty. hitalent campaignId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(emailyCampaign, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(syncDrftCampaignToEmailyUpdateUrl(emailyCampaignId), getRequestHeaders(), groupParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] update a email campaign to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] update a email campaign to Emaily error and response is null, campaignId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse updateEmailCampaignsPending(Long id, Long emailyCampaignId) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaignId)) {
            log.error("[EmailService: EmailyServiceImpl] update a email campaign status to pending to Emaily error, parameter error, the emaily campaign id cannot be empty. hitalent campaignId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.post(syncDrftCampaignToEmailyPendingUrl(emailyCampaignId), getRequestHeaders(), "");

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] update a email campaign status to pending to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] update a email campaign status to pending to Emaily error and response is null, campaignId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse queryEmailTemplate(Long templateId) throws IOException {
        if (ObjectUtil.isEmpty(templateId)) {
            log.error("[EmailService: EmailyServiceImpl] query a email template from Emaily error, parameter error, the emaily template id cannot be empty. templateId: {}", templateId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(queryTemplateFromEmailyUrl(templateId), getRequestHeaders());

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] query a email template from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] query a email template from Emaily error and response is null, templateId: {}", SecurityUtils.getUserId(), templateId);
        }
        return response;
    }

    @Override
    public HttpResponse updateEmailyAudience(Long talentId, EmailyAudienceUpdateDTO emailyAudienceUpdateDTO) throws IOException {
        if (ObjectUtil.isEmpty(talentId)) {
            log.error("[EmailService: EmailyServiceImpl] update a talent to Emaily error, parameter error, the talent id cannot be empty. talentId: {}", talentId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(emailyAudienceUpdateDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncTalentToEmailyUpdateUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl]  update a talent to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, talentId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}]  update a talent to Emaily error and response is null, talentId: {}", SecurityUtils.getUserId(), talentId);
        }
        return response;
    }

    @Override
    public HttpResponse queryEmailCampaign(Long id, Long emailyCampaignId) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaignId)) {
            log.error("[EmailService: EmailyServiceImpl] query a email campaign from Emaily error, parameter error, the emaily campaign id cannot be empty. campaignId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(syncDrftCampaignToEmailyUpdateUrl(emailyCampaignId), getRequestHeaders());

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl]  query a email campaign from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}]  query a email campaign from Emaily error and response is null, campaignId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse searchEmailCampaign(EmailCampaignArchiveStatus archiveStatus, List<EmailCampaignStatus> statusList, String search, String sort, Pageable pageable) throws IOException {

        HttpResponse response = httpService.get(searchCampaignFromEmailyUrl(archiveStatus, statusList, search, sort, pageable), getRequestHeaders());

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl]  search email campaigns from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}]  search email campaigns from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;

    }

    @Override
    public HttpResponse searchEmailCampaignByName(String emailyCampaignName) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaignName)) {
            log.error("[EmailService: EmailyServiceImpl] search email campaigns by name from Emaily error, parameter error, the emaily campaign name cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(searchCampaignFromEmailyByNamerl(emailyCampaignName), getRequestHeaders());

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] search email campaigns by name from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] search email campaigns by name from Emaily error and response is null.}", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse archiveEmailCampaign(Long id, Long emailyCampaignId) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaignId)) {
            log.error("[EmailService: EmailyServiceImpl] archive a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty. hitalent campaignId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(Arrays.asList(emailyCampaignId), SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncCampaignToEmailyArchiveUrl(), getRequestHeaders(), campaignParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] archive a email campaign status to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] archive a email campaign status to Emaily error and response is null, campaignId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse unArchiveEmailCampaign(Long id, Long emailyCampaignId) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaignId)) {
            log.error("[EmailService: EmailyServiceImpl] unarchive a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty. hitalent campaignId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(Arrays.asList(emailyCampaignId), SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncCampaignToEmailyUnArchiveUrl(), getRequestHeaders(), campaignParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] unarchive a email campaign status to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] unarchive a email campaign status to Emaily error and response is null, campaignId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse approveEmailCampaign(Long id, Long emailyCampaignId) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaignId)) {
            log.error("[EmailService: EmailyServiceImpl] approve a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty. hitalent campaignId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.post(syncCampaignToEmailyApproveUrl(emailyCampaignId), getRequestHeaders(), "");

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] approve a email campaign status to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] approve a email campaign status to Emaily error and response is null, campaignId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse denyEmailCampaign(Long id, Long emailyCampaignId, EmailyCmpaignDenyDTO denyDTO) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaignId)) {
            log.error("[EmailService: EmailyServiceImpl] deny a email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty. hitalent campaignId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(denyDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncCampaignToEmailyDenyUrl(emailyCampaignId), getRequestHeaders(), campaignParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] deny a email campaign status to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] deny a email campaign status to Emaily error and response is null, campaignId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse sendEmailCampaignTest(Long id, Long emailyCampaignId, List<EmailyCampaignSendDTO> emailyCampaignSendDTOList) throws IOException {
        if (ObjectUtil.isEmpty(emailyCampaignId)) {
            log.error("[EmailService: EmailyServiceImpl] send a tested email campaign to Emaily error, parameter error, the emaily campaign id cannot be empty. hitalent campaignId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(emailyCampaignSendDTOList, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(sendCampaignToEmailyTestUrl(emailyCampaignId), getRequestHeaders(), campaignParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] send a tested email campaign to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] send a tested email campaign to Emaily error and response is null, campaignId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse searchEmailTemplates(EmailTemplateType type, Long tagsId, String name, Pageable pageable, String sort) throws IOException {

        HttpResponse response = httpService.get(searchTemplatesFromEmailyUrl(type, tagsId, name, pageable, sort), getRequestHeaders());

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] search email templates from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
         } else {
            log.error("[EmailService: EmailyServiceImpl @{}] search email templates from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse searchEmailTemplateTags() throws IOException {
        HttpResponse response = httpService.get(searchTemplateTagsFromEmailyUrl(), getRequestHeaders());

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] search email template tags from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] search email template tags from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse createEmailTemplate(EmailyTemplateDTO templateDTO) throws IOException {
        if (ObjectUtil.isEmpty(templateDTO.getName()) || ObjectUtil.isEmpty(templateDTO.getHtmlContent())) {
            log.error("[EmailService: EmailyServiceImpl] create a email template to Emaily error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(templateDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncTemplateToEmailyCreateUrl(), getRequestHeaders(), campaignParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] create a email template to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] create a email template to Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse updateEmailTemplate(Long templateId, EmailyTemplateDTO templateDTO) throws IOException {
        if ((ObjectUtil.isEmpty(templateDTO.getName()) && ObjectUtil.isEmpty(templateDTO.getHtmlContent())) || ObjectUtil.isEmpty(templateId)) {
            log.error("[EmailService: EmailyServiceImpl] update a email template to Emaily error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(templateDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(syncTemplateToEmailyUpdateUrl(templateId), getRequestHeaders(), campaignParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] update a email template to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] update a email template to Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse deactivateEmailTemplates(List<Long> templateIdList) throws IOException {
        if (CollectionUtils.isEmpty(templateIdList)) {
            log.error("[EmailService: EmailyServiceImpl] deactivate email templates to Emaily error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(templateIdList, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncTemplateToEmailyDeactivateUrl(), getRequestHeaders(), campaignParamStr);

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] deactivate email templates to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] deactivate email templates to Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse queryEmailTemplateByName(String name) throws IOException {
        if (ObjectUtil.isEmpty(name)) {
            log.error("[EmailService: EmailyServiceImpl] query a email template by name from Emaily error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(name, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.get(queryTemplateFromEmailyByNameUrl(name), getRequestHeaders());

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] query a email template by name from Emaily error response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] query a email template by name from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse queryCompanyLimit() throws IOException {
        HttpResponse response = httpService.get(queryCompanyLimitFromEmailyUrl(), getRequestHeaders());

        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: EmailyServiceImpl] query company limit from Emaily error response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: EmailyServiceImpl @{}] query company limit from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    public Headers getRequestHeaders() {
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, TOKEN_TYPE + SPACE + currentUserToken);
        headersBuilder.put(EmailConstans.HEADER_CLIENT_ID, ClientTokenHolder.getInstance().getClientId());
        if (SecurityUtils.getUserUid() != null) {
            headersBuilder.put(EmailConstans.HEADER_UID, SecurityUtils.getUserUid());
        }
        return Headers.of(headersBuilder);
    }


}
