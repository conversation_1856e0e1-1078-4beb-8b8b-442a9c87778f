package com.altomni.apn.common.config;

import com.altomni.apn.common.auth.agency_auth.AgencyTokenFilter;
import com.altomni.apn.common.auth.agency_auth.AgencyUserTokenStore;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetTokenFilter;
import com.altomni.apn.common.auth.SkipOAuthTokenResolver;
import com.altomni.apn.common.auth.timesheet_auth.TimesheetUserTokenStore;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationEntryPoint;
import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;

/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class WebSecurityConfig {

    private static final String[] PUBLIC_ENDPOINTS = {
            "/api/v3/mail/liveness",
            "/api/v3/mail/send-rich-mail/system",
            "/api/v3/s3/store/url-with-redirection/**",
            "/actuator/**"
    };

    private static final String[] TIMESHEET_ENDPOINTS = {
            "/api/v3/geoinfo/search",
            "/api/v3/parsers/resume/upload-only",
            "/api/v3/s3/**"
    };

    private static final String[] AGENCY_ENDPOINTS = {
            "/**",
            "/*"
//            "/api/v3/geoinfo/search",
//            "/api/v3/parsers/resume/upload-only",
//            "/api/v3/parsers/resume/status",
//            "/api/v3/parsers/resume/result-status/{uuid}",
//            "/api/v3/parsers/resume/info/{uuid}",
//            "/api/v3/campaign/mail/list/{id}",
//            "/api/v3/s3/store/detail-without-file-byte/{uuid}/{uploadType}",
//            "/api/v3/s3/**",
    };

    private final TimesheetUserTokenStore timesheetUserTokenStore;

    private final AgencyUserTokenStore agencyUserTokenStore;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 资源服务器配置
        http.oauth2ResourceServer(auth -> auth.opaqueToken(Customizer.withDefaults()));
        // 没有 token 或者 token 过期，返回 401
        http.exceptionHandling(exceptionHandling -> exceptionHandling.authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint()));
        // 无状态 session
        http.sessionManagement((session) -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
        // 资源权限配置，所有请求都需要认证
        http.authorizeHttpRequests(
                authorizationManagerRequestMatcherRegistry -> authorizationManagerRequestMatcherRegistry
                        .requestMatchers(PUBLIC_ENDPOINTS).permitAll()
                        .anyRequest().authenticated());

        http.addFilterBefore(agencyTokenFilter(agencyUserTokenStore), BearerTokenAuthenticationFilter.class);
        http.addFilterBefore(timesheetTokenFilter(timesheetUserTokenStore), BearerTokenAuthenticationFilter.class);
        return http.build();
    }


    @Bean
    public WebSecurityCustomizer webSecurityCustomizer() {
        return web -> web.ignoring().requestMatchers(PUBLIC_ENDPOINTS);
    }

    @Bean
    public BearerTokenResolver bearerTokenResolver() {
        return new SkipOAuthTokenResolver();
    }


    public TimesheetTokenFilter timesheetTokenFilter(TimesheetUserTokenStore timesheetUserTokenStore) {
        return new TimesheetTokenFilter(timesheetUserTokenStore, TIMESHEET_ENDPOINTS);
    }

    public AgencyTokenFilter agencyTokenFilter(AgencyUserTokenStore agencyUserTokenStore) {
        return new AgencyTokenFilter(agencyUserTokenStore, AGENCY_ENDPOINTS);
    }

}
