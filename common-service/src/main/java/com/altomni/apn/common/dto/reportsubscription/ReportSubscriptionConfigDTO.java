package com.altomni.apn.common.dto.reportsubscription;

import com.altomni.apn.common.enumeration.reportSubscriptions.PushTimeType;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushTimeTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportSubscriptionConfigDTO {

    @Convert(converter = PushTimeTypeConverter.class)
    private PushTimeType pushTimeType;

    private Integer dayOfWeek;

    private Integer dayOfMonth;

    private String sendTime;

}
