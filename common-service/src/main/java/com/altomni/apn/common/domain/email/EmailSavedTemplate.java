package com.altomni.apn.common.domain.email;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.email.enumeration.EmailTemplateStatus;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "email_saved_template")
public class EmailSavedTemplate extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "emaily_template_id")
    private Long emailyTemplateId;

    @Column(name = "name")
    private String name;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "audit_status")
    private EmailTemplateStatus auditStatus = EmailTemplateStatus.NOT_STARTED;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEmailyTemplateId() {
        return emailyTemplateId;
    }

    public void setEmailyTemplateId(Long emailyTemplateId) {
        this.emailyTemplateId = emailyTemplateId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public EmailTemplateStatus getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(EmailTemplateStatus auditStatus) {
        this.auditStatus = auditStatus;
    }

    public EmailSavedTemplate() {
    }
    public EmailSavedTemplate(Long emailyTemplateId, String name, Long tenantId, EmailTemplateStatus auditStatus) {
        this.emailyTemplateId = emailyTemplateId;
        this.name = name;
        this.tenantId = tenantId;
        this.auditStatus = auditStatus;
    }

}
