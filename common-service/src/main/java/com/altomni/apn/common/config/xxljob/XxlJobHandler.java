package com.altomni.apn.common.config.xxljob;

import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface XxlJobHandler {

    XxlJobRelationTypeEnum[] values(); // 定义注解的属性，这里是任务名称

}
