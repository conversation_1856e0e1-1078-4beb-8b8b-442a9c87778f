package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum BillType implements ConvertedEnum<Integer> {

    CAMPAIGN_CHARGE(0),
    CAMPAIGN_REFUND(1);

    private final int dbValue;

    BillType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<BillType, Integer> resolver = new ReverseEnumResolver<>(BillType.class, BillType::toDbValue);

    public static BillType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
