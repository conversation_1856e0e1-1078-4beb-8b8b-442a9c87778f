package com.altomni.apn.common.service.email.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class EmailyCampaignSendDTO implements Serializable {

    @ApiModelProperty(value = "The name of this addressee.")
    private String name;

    @ApiModelProperty(value = "The email of this addressee.")
    private String email;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
