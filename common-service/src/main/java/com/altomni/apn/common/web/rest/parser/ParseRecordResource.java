package com.altomni.apn.common.web.rest.parser;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.parser.ParseRecord;
import com.altomni.apn.common.service.parser.ParserRecordService;
import com.altomni.apn.common.service.parser.dto.ParseRecordListDTO;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing ParseRecord.
 */
@RestController
@RequestMapping("/api/v3")
public class ParseRecordResource {

    private final Logger log = LoggerFactory.getLogger(ParseRecordResource.class);

    private static final String ENTITY_NAME = "parseRecord";

    @Resource
    private ParserRecordService parserRecordService;

    /**
     * POST  /parse-records : Create a new parseRecord.
     *
     * @param parseRecord the parseRecord to create
     * @return the ResponseEntity with status 201 (Created) and with body the new parseRecord, or with status 400 (Bad Request) if the parseRecord has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/parse-records")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<ParseRecord> createParseRecord(@RequestBody ParseRecord parseRecord) throws URISyntaxException {
        log.info("[APN: ParseRecord @{}] REST request to save ParseRecord, uuid : {}", SecurityUtils.getUserId(), parseRecord.getUuid());
        if (parseRecord.getId() != null) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(ENTITY_NAME, "idexists", "A new parseRecord cannot already have an ID")).body(null);
        }
        ParseRecord result = parserRecordService.save(parseRecord);
        return ResponseEntity.created(new URI("/api/parse-records/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * PUT  /parse-records : Updates an existing parseRecord.
     *
     * @param parseRecord the parseRecord to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated parseRecord,
     * or with status 400 (Bad Request) if the parseRecord is not valid,
     * or with status 500 (Internal Server Error) if the parseRecord couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/parse-records/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<ParseRecord> updateParseRecord(@PathVariable Long id, @RequestBody ParseRecord parseRecord) throws URISyntaxException {
        log.info("[APN: ParseRecord @{}] REST request to update ParseRecord, uuid : {}", SecurityUtils.getUserId(), parseRecord.getUuid());

        ParseRecord currentRecord = parserRecordService.findOne(id);
        currentRecord.setReviewed(parseRecord.getReviewed());
        currentRecord.setNote(parseRecord.getNote());

        ParseRecord result = parserRecordService.save(currentRecord);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, id.toString()))
            .body(result);
    }

    /**
     * GET  /parse-records : get all the parseRecords.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of parseRecords in body
     */
    @GetMapping("/parse-records")
    @Timed
    public List<ParseRecord> getAllParseRecords() {
        log.info("[APN: ParseRecord @{}] REST request to get all ParseRecords", SecurityUtils.getUserId());
        return parserRecordService.findAll();
    }

    @GetMapping("/my-parse-records")
    @Timed
    public ResponseEntity<List<ParseRecordListDTO>> getMyParseRecords(Pageable pageable) {
        log.info("[APN: ParseRecord @{}] REST request to get all ParseRecords", SecurityUtils.getUserId());
        Page<ParseRecordListDTO> page = parserRecordService.findAllByCreatedByAndIsReviewedOrderByIdDesc(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/my-parse-records");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * GET  /parse-records/:id : get the "id" parseRecord.
     *
     * @param id the id of the parseRecord to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the parseRecord, or with status 404 (Not Found)
     */
    @GetMapping("/parse-records/{id}")
    @Timed
    public ResponseEntity<ParseRecord> getParseRecord(@PathVariable Long id) {
        log.info("[APN: ParseRecord @{}] REST request to get ParseRecord : {}", SecurityUtils.getUserId(), id);
        ParseRecord parseRecord = parserRecordService.findOne(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(parseRecord));
    }

    /**
     * DELETE  /parse-records/:id : delete the "id" parseRecord.
     *
     * @param id the id of the parseRecord to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/parse-records/{id}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteParseRecord(@PathVariable Long id) {
        log.info("[APN: ParseRecord @{}] REST request to delete ParseRecord : {}", SecurityUtils.getUserId(), id);
        parserRecordService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }

}
