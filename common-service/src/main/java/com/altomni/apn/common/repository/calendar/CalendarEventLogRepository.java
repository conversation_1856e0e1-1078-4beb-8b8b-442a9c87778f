package com.altomni.apn.common.repository.calendar;

import com.altomni.apn.common.domain.calendar.CalendarEventLog;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CalendarEventLogRepository extends JpaRepository<CalendarEventLog, Long> {

    List<CalendarEventLog> findAllByEventId(Long eventId);
}
