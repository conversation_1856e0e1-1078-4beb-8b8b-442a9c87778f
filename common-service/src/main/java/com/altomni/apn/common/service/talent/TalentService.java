package com.altomni.apn.common.service.talent;

import com.altomni.apn.common.dto.talent.TalentAutoDeclassifyDto;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;
import com.altomni.apn.common.vo.talent.TalentEmailContactVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Service Interface for managing {@link TalentEmailContactVO}.
 */
@Component
@FeignClient(value = "talent-service")
public interface TalentService {

    @PostMapping("/talent/api/v3/talents/contacts/search")
    ResponseEntity<List<TalentEmailContactVO>> findByTalentIdIn(@RequestBody List<Long> talentIds); //TODO

    @GetMapping("/talent/api/v3/talent-resume/find-by-uuid-and-tenantId")
    ResponseEntity<TalentResumeDTO> findByUuidAndTenantId(@RequestParam("uuid") String uuid, @RequestParam("tenantId") Long tenantId);

    @GetMapping("/talent/api/v3/talents/fullName/{talentId}")
    ResponseEntity<String> findFullNameByTalentId(@PathVariable("talentId") Long talentId);

    @DeleteMapping("/talent/api/v3/talents/confidential/auto")
    ResponseEntity<Void> autoDeclassifyTalent(@RequestBody TalentAutoDeclassifyDto talentAutoDeclassifyDto);

}
