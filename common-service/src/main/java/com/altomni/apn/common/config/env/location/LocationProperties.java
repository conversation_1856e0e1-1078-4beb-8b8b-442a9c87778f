package com.altomni.apn.common.config.env.location;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class LocationProperties {

    @Value("${application.locationService.aws.locationIndex:}")
    private String locationIndex;

}
