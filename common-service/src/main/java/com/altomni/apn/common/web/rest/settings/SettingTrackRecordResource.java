package com.altomni.apn.common.web.rest.settings;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.settings.SettingTrackRecordCreateDTO;
import com.altomni.apn.common.dto.settings.SettingTrackRecordDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.settings.SettingTrackRecordTemplateService;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.settings.SettingTrackRecordVO;
import com.altomni.apn.common.vo.settings.SettingTrackRecordViewVO;
import com.altomni.apn.common.web.rest.CommonResource;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3")
@Slf4j
public class SettingTrackRecordResource {

    @Autowired
    private SettingTrackRecordTemplateService settingTrackRecordTemplateService;

    /**
     * list search
     *
     * @param dto
     * @param pageable
     * @return
     */
    @PostMapping("/track/record/search")
    @Timed
    public ResponseEntity<List<SettingTrackRecordVO>> search(@RequestBody SettingTrackRecordDTO dto, @PageableDefault Pageable pageable) {
        log.info("[track_record: User @{}] REST search track_record list:", SecurityUtils.getUserId());
        Page<SettingTrackRecordVO> settingTrackRecordVOS = settingTrackRecordTemplateService.searchSettingTrackRecordList(dto, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(settingTrackRecordVOS, "/api/v1//track/record/search");
        return ResponseEntity.ok().headers(headers).body(settingTrackRecordVOS.getContent());
    }

    @PostMapping(value = "/track/record")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity save(@RequestBody SettingTrackRecordCreateDTO dto) {
        log.info("[track_record: User @{}] REST create track_record method:", SecurityUtils.getUserId());
        settingTrackRecordTemplateService.save(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @GetMapping("/track/record/{id}")
    @Timed
    public ResponseEntity<SettingTrackRecordViewVO> getInvoice(@PathVariable BigInteger id) {
        log.info("[track_record: User @{}] get track record:", SecurityUtils.getUserId());
        SettingTrackRecordViewVO vo = settingTrackRecordTemplateService.view(id);
        return ResponseEntity.ok(vo);
    }

    @PutMapping("/track/record")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity modify(@RequestBody SettingTrackRecordCreateDTO dto) {
        log.info("[invoice: User @{}] REST invoice modify method:", SecurityUtils.getUserId());
        if (null == dto.getId()) {
            throw new CustomParameterizedException(" missing required parameter");
        }
        settingTrackRecordTemplateService.modify(dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    @DeleteMapping("/track/record/delete")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> delete(@RequestParam BigInteger id) {
        log.info("[assignment: User @{}] delete assignment:", SecurityUtils.getUserId());
        settingTrackRecordTemplateService.delete(id);
        return ResponseEntity.ok(HttpStatus.OK.value());
    }

    @PostMapping("/track/record/update")
    @Timed
    public ResponseEntity<Integer> update(@RequestParam BigInteger id, @RequestParam Integer status) {
        log.info("[assignment: User @{}] delete assignment:", SecurityUtils.getUserId());
        settingTrackRecordTemplateService.update(id, status);
        return ResponseEntity.ok(HttpStatus.OK.value());
    }

    @GetMapping("/track/record/myTrackTemp")
    @Timed
    public ResponseEntity<List<SettingTrackRecordVO>> getMyTrackTemp() {
        log.info("[track_record: User @{}] get track record:", SecurityUtils.getUserId());
        List<SettingTrackRecordVO> vo = settingTrackRecordTemplateService.queryTrackRecordList();
        return ResponseEntity.ok(vo);
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }
}