package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractType enumeration.
 */
public enum EventType implements ConvertedEnum<Integer> {
    APN_INJECTION_FAILURE(0, 0, Status.FAILURE), //APN relay request to SparkPost server failed.
    APN_INJECTION_SUCCESS(1, 1, Status.PENDING), //APN relay request to SparkPost server succeed.
    BOUNCE(2, 32, Status.FAILURE), //Recipient mailbox provider permanently rejected the email.
    DELIVERY(3,30, Status.SUCCESS), //Recipient mailbox provider acknowledged receipt of email.
    INJECTION(4, 10, Status.PENDING), //Email successfully generated or relayed to SparkPost.
    SPAM_COMPLAINT(5, 60, Status.SUCCESS), //Recipient marked the email as spam.
    OUT_OF_BAND(6, 31, Status.FAILURE), // Recipient mailbox provider rejected the email after initially accepting it.
    POLICY_REJECTION(7, 13, Status.FAILURE), //SparkPost rejected the email due to a policy reason.
    DELAY(8, 20, Status.PENDING), //Recipient mailbox provider temporarily rejected the email.
    CLICK(9, 50, Status.SUCCESS), //Recipient clicked a tracked link in the email.
    OPEN(10, 41, Status.SUCCESS), //Recipient opened the email. Recorded by a tracking pixel at the bottom of the email.
    INITIAL_OPEN(11, 40, Status.SUCCESS), //Recipient opened the email. Recorded by a tracking pixel at the top of the email.
    AMP_CLICK(12, 51, Status.SUCCESS),
    AMP_OPEN(13, 43, Status.SUCCESS),
    AMP_INITIAL_OPEN(14, 42, Status.SUCCESS),
    GENERATION_FAILURE(15, 11, Status.FAILURE), //Email generation failed due to a technical reason.
    GENERATION_REJECTION(16, 12, Status.FAILURE), //Email generation failed due to a policy reason.
    LIST_UNSUBSCRIBE(17, 61, Status.SUCCESS), //Recipient unsubscribed using a mailbox provider’s list unsubscribe feature.
    LINK_UNSUBSCRIBE(18, 62, Status.SUCCESS), //User clicked a tagged unsubscribe link.

    SOFT_BOUNCE(21, 33, Status.PENDING),
    BLOCK_BOUNCE(22, 34, Status.FAILURE),
    ADMIN_BOUNCE(23, 35, Status.FAILURE),
    UNDETERMINED_BOUNCE(24, 36, Status.FAILURE),
    HARD_BOUNCE(25, 37, Status.FAILURE);

    private final Integer dbValue;

    private final Integer priority;

    private final Status status;

    EventType(Integer dbValue, Integer priority, Status status) {
        this.dbValue = dbValue;
        this.priority = priority;
        this.status = status;
    }

    @Override
    public Integer toDbValue() { return dbValue; }

    public Integer getPriority() { return priority; }

    public Status getStatus() { return status; }

    // static resolving:
    public static final ReverseEnumResolver<EventType, Integer> resolver =
        new ReverseEnumResolver<>(EventType.class, EventType::toDbValue);

    public static EventType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
