package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EmailyAudienceInfoType implements ConvertedEnum<Integer> {

    MONGO_LIST(0),
    SEARCH(1);

    private final int dbValue;

    EmailyAudienceInfoType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<EmailyAudienceInfoType, Integer> resolver = new ReverseEnumResolver<>(EmailyAudienceInfoType.class, EmailyAudienceInfoType::toDbValue);

    public static EmailyAudienceInfoType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
