package com.altomni.apn.common.dto.xxljob;

import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnumConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class XxlJobReminderForApplicationDTO extends XxlJobBaseDTO {

    @Convert(converter = MessageTypeEnumConverter.class)
    private MessageTypeEnum type;

    private Instant sendTime;

    private String content;

    private String reminderConfig;

    private Long talentRecruitmentProcessId;

    private Long talentId;

}
