package com.altomni.apn.common.config.env.parser;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ParserProperties {

    @Value("${application.storeService.portrait.bucket}")
    private String portraitBucket;

    @Value("${spring.redis.host-parser}")
    private String redisParserHost;

    @Value("${spring.redis.port-parser}")
    private int redisParserPort;

    @Value("${spring.redis.database}")
    private int redisDatabase;

//    @Value("${spring.parser-mq.exchange}")
//    private String exchange;
//
//    @Value("${spring.parser-mq.resume-routing-key}")
//    private String resumeRoutingKey;
//
//    @Value("${spring.parser-mq.jd-routing-key}")
//    private String jdRoutingKey;
//
//    @Value("${spring.parser-mq.max-priority}")
//    private int maxPriority;

}
