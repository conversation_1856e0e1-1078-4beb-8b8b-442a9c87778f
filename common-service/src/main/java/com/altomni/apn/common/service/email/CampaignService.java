package com.altomni.apn.common.service.email;

import com.altomni.apn.common.domain.email.enumeration.EmailCampaignArchiveStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignStatus;
import com.altomni.apn.common.dto.email.EmailLogOverviewDTO;
import com.altomni.apn.common.dto.email.MailSearchDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.service.email.dto.EmailyAssociatedCampaign;
import com.altomni.apn.common.service.email.dto.EmailyCampaignSendDTO;
import com.altomni.apn.common.service.email.dto.EmailyCmpaignDenyDTO;
import com.altomni.apn.common.service.email.dto.emailVM.EmailVM;
import org.springframework.data.domain.Pageable;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface CampaignService {

    HttpResponse createEmailCampaign(EmailyAssociatedCampaign campaign) throws IOException;

    HttpResponse saveDraftCampaign(EmailyAssociatedCampaign campaign) throws IOException;

    HttpResponse updateEmailCampaign(Long campaignId, EmailyAssociatedCampaign campaign) throws IOException;

    HttpResponse updateEmailCampaignToPending(Long campaignId) throws IOException;

    HttpResponse getCampaignById(Long campaignId) throws IOException;

    HttpResponse searchEmailCampaign(EmailCampaignArchiveStatus archiveStatus, List<EmailCampaignStatus> statusList, String search, String sort, Pageable pageable) throws IOException;

    HttpResponse searchEmailCampaignByName(String campaignName) throws IOException;

    HttpResponse archiveEmailCampaign(Long campaignId) throws IOException;

    HttpResponse unarchiveEmailCampaign(Long campaignId) throws IOException;

    HttpResponse approveEmailCampaign(Long campaignId) throws IOException;

    HttpResponse denyEmailCampaign(Long campaignId, EmailyCmpaignDenyDTO denyDTO) throws IOException;

    HttpResponse sendEmailCampaignTest(Long campaignId, List<EmailyCampaignSendDTO> recipients) throws IOException;

    HttpResponse queryTenantLimit() throws IOException;

    void sendHtmlMail(String from, List<String> to, List<String> cc, List<String> bcc, String subject, String html_content, List<String> links, List<MultipartFile> files, boolean systemEmail, boolean autoSwitchToOtherESP, Boolean transactional, Long accountCompanyId, Long accountContactId) throws IOException;

    void sendHtmlMail(String from, List<String> to, List<String> cc, List<String> bcc, String subject, String html_content, List<String> links, List<MultipartFile> files, boolean systemEmail, boolean autoSwitchToOtherESP, Boolean transactional) throws IOException;

    String sendHtmlMail(com.altomni.apn.common.web.rest.email.vm.MailVM mailVM, boolean systemEmail, boolean autoSwitchToOtherESP, boolean isSending) throws IOException;

    HttpResponse countVoipTalentNoticeEmail(Long talentId) throws IOException;

    HttpResponse countVoipContactNoticeEmail(Long contactId) throws IOException;

    HttpResponse getListByStatus(String status, Pageable pageable) throws IOException;

    HttpResponse getById(Long id) throws IOException;

    HttpResponse getByIdIgnoreUserId(Long id) throws IOException;

    HttpResponse deleteById(Long id) throws IOException;

    void sendHtmlMail(MailVM mailVM) throws IOException;

    HttpResponse sendEmailBlast(EmailVM emailVM) throws IOException;

    List<EmailLogOverviewDTO> searchSentEmailAndCountReplied(MailSearchDTO mailSearchDTO) throws IOException;

    List<EmailLogOverviewDTO> searchSentEmailsByUsers(MailSearchDTO mailSearchDTO) throws IOException;

    void syncVoipEmailRecordToEs(Long talentId, String subject);

}
