package com.altomni.apn.common.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.okHttpClient.connectionTimeout}")
    private Integer connectionTimeout;

    @Value("${application.okHttpClient.readTimeout}")
    private Integer readTimeout;

    @Value("${application.okHttpClient.connectionPoolSize}")
    private Integer connectionPoolSize;

    @Value("${application.okHttpClient.keepAliveDuration}")
    private Integer keepAliveDuration;

    @Value("${application.commonService}")
    private String apnCommonServiceUrl;

    @Value("${application.commonServicePin}")
    private String apnCommonServicePin;

    @Value("${application.mainPath.baseUrl}")
    private String baseUrl;

    @Value("${application.elasticrecord.url}")
    private String recordUrl;

    @Value("${application.crmUrl}")
    private String crmUrl;

}