package com.altomni.apn.common.service.email.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.config.env.email.EmailAppProperties;
import com.altomni.apn.common.domain.email.enumeration.ApplicationSource;
import com.altomni.apn.common.dto.company.SalesLeadClientContactDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.company.CompanyService;
import com.altomni.apn.common.service.email.AudienceService;
import com.altomni.apn.common.service.email.EmailConstans;
import com.altomni.apn.common.service.email.dto.*;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.talent.TalentService;
import com.altomni.apn.common.service.user.UserService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.PageableConvertUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.talent.TalentEmailContactVO;
import com.ipg.resourceserver.client.ClientTokenHolder;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Service
public class AudienceServiceImpl implements AudienceService {

    private final Logger log = LoggerFactory.getLogger(AudienceServiceImpl.class);

    @Resource
    private HttpService httpService;

    @Resource
    private TalentService talentService;

    @Resource
    private CompanyService companyService;

    @Resource
    private UserService userService;

    @Resource
    private EmailAppProperties emailAppProperties;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

//    private final AsyncRecordRepository asyncRecordRepository;

    private final static String HEADERS_AUTHORIZATION_KEY = "Authorization";

    private final static String AUDIENCE_GROUP_BASE_URL = "/api/v2/audience/mongo";

    private final static String BY_EXTERNAL_USER = "/by-external-user";

    private final static String URL_SEPATATOR = "/";

    private final static String URL_DELETE = "/delete";

    private final static String URL_ADD_AUDIENCE = "/add-audience-with-response";

    private final static String URL_ADD_AUDIENCE_ESID = "/esId";

    private final static String URL_ADD_AUDIENCE_SEARCH_AUDIENCE= "/add-search-audience";

    private final static String URL_MAX_GROUP_SIZE = "maxGroupSize=";

    private final static String URL_AUDIENCE_UPDATE = "/update-by-data-source-id";

    private final static String URL_MOVE = "/move";

    private final static String URL_COPY = "/copy";

    private final static String URL_MERGE = "/merge";

    private final static String SPACE = " ";

    private final static String URL_UNARCHIVE = "/unarchive";

    private final static String EMAILY_GROUP_CREATE_URL_V1 = "/api/v1/audience";

    private final static String EMAILY_GROUP_SORT_CREATEDDATE_DESC = "sort=createdDate,desc";

    private final static String EMAILY_GROUP_SORT_LAST_MODIFIED_DATE_DESC = "sort=last_modified_date,desc";

    private final static String EMAILY_GROUP_SORT_CREATEDDATE_ASC = "sort=createdDate,asc";

    private final static String URL_ARCHIVED = "/archived";

    private final static String URL_SEARCH_PAGE = "page=";

    private final static String URL_SEARCH_SIZE = "size=";

    private final static String URL_SEARCH_SORT = "sort=";

    private final static String URL_SEARCH_NAME = "name=";

    private final static String URL_SEARCH_EMAIL = "email=";

    private final static String URL_SEARCH_STATUS = "status=";

    private final static String URL_SEARCH_DATASOURCE = "dataSource=";

    private final static String URL_QUESTION_MARK = "?";

    private final static String URL_AND = "&";

    private final static String URL_SEARCH = "search=";

    private final static String URL_TYPE = "type=";

    private final static String URL_INFO = "/info";

    private final static String MONGO_LIST = "MONGO_LIST";


    private String emailyBaseUrl() {
        return emailAppProperties.getEmailyBaseUrl();
    }

    private String syncGroupToEmailyCreateUrl() {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL;
    }

    private String syncGroupToEmailyUpdateUrl(Long emailyGroupId) {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId;
    }

    private String syncGroupToEmailyDeleteUrl(Long emailyGroupId) {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId + URL_DELETE;
    }

    private String syncAudienceToEmailyAddUrl(Long emailyGroupId, int maxGroupSize) {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId + URL_ADD_AUDIENCE + URL_QUESTION_MARK + URL_MAX_GROUP_SIZE + maxGroupSize;
    }

    private String syncAudienceToEmailyByEsIdAddUrl(Long emailyGroupId, int maxGroupSize) {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId + URL_ADD_AUDIENCE_ESID + URL_QUESTION_MARK + URL_MAX_GROUP_SIZE + maxGroupSize;
    }

    private String syncAudienceToEmailyFromCommonPoolAddUrl(Long emailyGroupId, int maxGroupSize) {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId + URL_ADD_AUDIENCE_SEARCH_AUDIENCE + URL_QUESTION_MARK + URL_MAX_GROUP_SIZE + maxGroupSize;
    }

    private String syncAudienceToEmailyMergeUrl() {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_MERGE;
    }

    private String syncAudienceToEmailyMoveUrl() {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_MOVE;
    }

    private String syncAudienceToEmailyCopyUrl() {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_COPY;
    }

    private String syncGroupToEmailyRecoveryUrl(Long emailyGroupId) {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId + URL_UNARCHIVE;
    }

    private String searchArchivedAudienceGroupUrl(String name, Pageable pageable) {
        String pagination = PageableConvertUtil.convertPageableToString(pageable);
        if (ObjectUtil.isEmpty(name)) {
            return emailyBaseUrl() + EMAILY_GROUP_CREATE_URL_V1 + URL_ARCHIVED + BY_EXTERNAL_USER + URL_QUESTION_MARK + pagination + URL_AND + EMAILY_GROUP_SORT_CREATEDDATE_DESC;
        } else {
            return emailyBaseUrl() + EMAILY_GROUP_CREATE_URL_V1 + URL_ARCHIVED + BY_EXTERNAL_USER + URL_QUESTION_MARK + URL_SEARCH + name + URL_AND + pagination + URL_AND + EMAILY_GROUP_SORT_CREATEDDATE_DESC;
        }
    }

    private String searchActiveAudienceGroupUrl(String name, Pageable pageable) {
        String pagination = PageableConvertUtil.convertPageableToString(pageable);
        if (ObjectUtil.isEmpty(name)) {
            return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + BY_EXTERNAL_USER + URL_QUESTION_MARK + pagination + URL_AND + EMAILY_GROUP_SORT_CREATEDDATE_DESC;
        } else {
            return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + BY_EXTERNAL_USER + URL_QUESTION_MARK + URL_SEARCH + name + URL_AND + pagination + URL_AND + EMAILY_GROUP_SORT_CREATEDDATE_DESC;
        }
    }

    private String searchAllGroupFromEmilyUrl(String name) {
        if (ObjectUtil.isEmpty(name)) {
            return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL;
        } else {
            return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_QUESTION_MARK + URL_SEARCH + name;
        }
    }

    private String searchGroupAudienceFromEmailyUrl(Long emailyGroupId, String name, String email, String dataSource, Pageable pageable) {
        String pagination = PageableConvertUtil.convertPageableToString(pageable);
        if (ObjectUtil.isEmpty(dataSource)) {
//            return emailyBaseUrl() + EMAILY_MONGO_AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + EMAILY_GROUP_SORT_LAST_MODIFIED_DATE_DESC;
            return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId + URL_QUESTION_MARK + URL_SEARCH_NAME + name + URL_AND + URL_SEARCH_EMAIL + email + URL_AND + pagination + URL_AND + EMAILY_GROUP_SORT_LAST_MODIFIED_DATE_DESC;
        } else {
            return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId + URL_QUESTION_MARK + URL_SEARCH_DATASOURCE + dataSource + URL_AND + URL_SEARCH_NAME + name + URL_AND + URL_SEARCH_EMAIL + email + URL_AND + pagination + EMAILY_GROUP_SORT_LAST_MODIFIED_DATE_DESC;
        }
    }

    private String queryGroupFromEmilyUrl(Long emailyGroupId) {
        return emailyBaseUrl() + EMAILY_GROUP_CREATE_URL_V1 + URL_SEPATATOR + emailyGroupId + URL_QUESTION_MARK + URL_TYPE + MONGO_LIST;
    }

    private String queryGroupFromInfoEmilyUrl(Long emailyGroupId) {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_SEPATATOR + emailyGroupId + URL_INFO;
    }

    private String syncTalentToEmailyUpdateUrl() {
        return emailyBaseUrl() + AUDIENCE_GROUP_BASE_URL + URL_AUDIENCE_UPDATE;
    }

    @Override
    public HttpResponse createMongoAudienceGroup(String name) throws IOException {
        if (ObjectUtil.isEmpty(name)) {
            log.error("[EmailService: AudienceServiceImpl] create a mongo audience group to Emaily error, parameter error, the emaily group name cannot be empty. groupName: {}", name);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        MongoAudience mongoAudience = new MongoAudience();
        mongoAudience.setName(name);
        String groupParamStr = JSON.toJSONString(mongoAudience, SerializerFeature.WriteDateUseDateFormat);
        System.out.println(syncGroupToEmailyCreateUrl());
        HttpResponse response = httpService.post(syncGroupToEmailyCreateUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl @{}] create a mongo audience group to Emaily error, response code: {}, response message: {}", SecurityUtils.getUserId(), response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] create a mongo audience group to Emaily error and response is null, groupName: {}", SecurityUtils.getUserId(), name);
        }
        return response;
    }

    @Override
    public HttpResponse createMongoAudienceGroup(NameAndDescription nameAndDescription) throws IOException {
        String groupParamStr = JSON.toJSONString(nameAndDescription, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncGroupToEmailyCreateUrl(), getRequestHeaders(), groupParamStr);
        return logAndReturn(response, "create a mongo audience group", groupParamStr);
    }

    private HttpResponse logAndReturn(HttpResponse response, String logMessage, String requestParams) {
        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl @{}] {} to Emaily error, with request params: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), logMessage, requestParams, response.getCode(), response.getBody());
            }
            return response;
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] {} to Emaily error and response is null, with request params: {}", SecurityUtils.getUserId(), logMessage, requestParams);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_AUDIENCE_SERVICEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    @Override
    public HttpResponse updateMongoAudienceGroup(Long id, Long emailyGroupId, String name) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId) || ObjectUtil.isEmpty(name)) {
            log.error("[EmailService: AudienceServiceImpl] update a mongo audience group to Emaily error, parameter error, the emaily group id and name cannot be empty. hitalentGroupId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        MongoAudience mongoAudience = new MongoAudience();
        mongoAudience.setName(name);
        String groupParamStr = JSON.toJSONString(mongoAudience, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(syncGroupToEmailyUpdateUrl(emailyGroupId), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] update a mongo audience group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] update a mongo audience group to Emaily error and response is null, hitalentGroupId: {}, emailyGroupId: {}", SecurityUtils.getUserId(), id, emailyGroupId);
        }
        return response;

    }

    @Override
    public HttpResponse updateMongoAudienceGroup(Long id, NameAndDescription nameAndDescription) throws IOException {
        String groupParamStr = JSON.toJSONString(nameAndDescription, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(syncGroupToEmailyUpdateUrl(id), getRequestHeaders(), groupParamStr);
        return logAndReturn(response, "update a mongo audience group", groupParamStr);
    }

    @Override
    public HttpResponse updateMongoAudienceGroup(Long emailyGroupId, String name) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId) || ObjectUtil.isEmpty(name)) {
            log.error("[EmailService: AudienceServiceImpl] update a mongo audience group to Emaily error, parameter error, the emaily group id and name cannot be empty. emailyGroupId: {}", emailyGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        MongoAudience mongoAudience = new MongoAudience();
        mongoAudience.setName(name);
        String groupParamStr = JSON.toJSONString(mongoAudience, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(syncGroupToEmailyUpdateUrl(emailyGroupId), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] update a mongo audience group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] update a mongo audience group to Emaily error and response is null, emailyGroupId: {}, emailyGroupName: {}", SecurityUtils.getUserId(), emailyGroupId, name);
        }
        return response;
    }

    @Override
    public HttpResponse deleteAudienceByIdAndAudienceIdList(Long groupId, List<String> audienceIds) throws IOException {
        if (ObjectUtil.isEmpty(groupId) || CollectionUtils.isEmpty(audienceIds)) {
            log.error("[EmailService: AudienceServiceImpl] delete a mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(audienceIds, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncGroupToEmailyDeleteUrl(groupId), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] delete a mongo audience group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] delete a mongo audience group to Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), groupId);
        }
        return response;

    }

    @Override
    public HttpResponse archiveAudienceGroupById(Long groupId) throws IOException {
        if (ObjectUtil.isEmpty(groupId)) {
            log.error("[EmailService: AudienceServiceImpl] delete a mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. emailyGroupId: {}", groupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.delete(syncGroupToEmailyUpdateUrl(groupId), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.NO_CONTENT.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] delete a mongo audience group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] delete a mongo audience group to Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), groupId);
        }
        return response;
    }

    @Override
    public HttpResponse unarchiveAudienceGroupById(Long groupId) throws IOException {
        if (ObjectUtil.isEmpty(groupId)) {
            log.error("[EmailService: AudienceServiceImpl] recovery a mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. emailyGroupId: {}", groupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.put(syncGroupToEmailyRecoveryUrl(groupId), getRequestHeaders(), "");

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.NO_CONTENT.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] recovery a mongo audience group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] recovery a mongo audience group to Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), groupId);
        }
        return response;
    }

    @Override
    public HttpResponse getAudienceGroupFromEmaily(Long emailyGroupId) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId)) {
            log.error("[EmailService: AudienceServiceImpl] get a mongo audience group from Emaily error, parameter error, the emaily group id cannot be empty. emailyGroupId: {}", emailyGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(queryGroupFromEmilyUrl(emailyGroupId), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] get a mongo audience group from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] get a mongo audience group from Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), emailyGroupId);
        }
        return response;

    }

    @Override
    public HttpResponse getAudienceGroupInfoById(Long groupId) throws IOException {
        if (ObjectUtil.isEmpty(groupId)) {
            log.error("[EmailService: AudienceServiceImpl] query a mongo audience group info from Emaily error, parameter error, the emaily group id cannot be empty. emailyGroupId: {}", groupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(queryGroupFromInfoEmilyUrl(groupId), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] query a mongo audience group info from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] query a mongo audience group info from Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), groupId);
        }
        return response;
    }

//    @Override
//    public HttpResponse searchAllActiveMongoAudienceGroupList(String name, TalentGroupActive talentGroupActive) throws IOException {
//
//        HttpResponse response = httpService.get(searchAllGroupFromEmilyUrl(name), getRequestHeaders());
//
//        if (response != null) {
//            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
//                log.error("[Hitalent to B: EmailyService] search all mongo audience group list from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
//            }
//        } else {
//            log.error("[Hitalent to B: EmailyService @{}] search all mongo audience group list from Emaily error and response is null", SecurityUtils.getUserId());
//        }
//        return response;
//    }

    @Override
    public HttpResponse searchArchivedMongoAudienceGroupList(String name, Pageable pageable) throws IOException {
        HttpResponse response = httpService.get(searchArchivedAudienceGroupUrl(name, pageable), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] search archived mongo audience group list from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] search archived mongo audience group list from Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse searchActiveMongoAudienceGroupList(String name, Pageable pageable) throws IOException {
        HttpResponse response = httpService.get(searchActiveAudienceGroupUrl(name, pageable), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] search active mongo audience group list from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] search active mongo audience group list from Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse getPageableAudienceByGroupIdAndSource(Long groupId, String name, String email, String dataSource, Pageable pageable) throws IOException {
        if (ObjectUtil.isEmpty(groupId)) {
            log.error("[EmailService: AudienceServiceImpl] search mongo audiences from group id error, parameter error, the emaily group id cannot be empty. emailyGroupId: {}", groupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(searchGroupAudienceFromEmailyUrl(groupId, name, email, dataSource, pageable), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] search mongo audiences from group id error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] search mongo audiences from group id error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), groupId);
        }
        return response;
    }

    @Override
    public HttpResponse addAudienceToGroup(Long groupId, List<MongoAudience> audienceList, int maxGroupSize) throws IOException {
        if (ObjectUtil.isEmpty(groupId) || CollectionUtils.isEmpty(audienceList)) {
            log.error("[EmailService: AudienceServiceImpl] add audience to mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(audienceList, SerializerFeature.WriteDateUseDateFormat);
        String url = syncAudienceToEmailyAddUrl(groupId, maxGroupSize);
        if (audienceList.get(0).getDataSource() == ApplicationSource.AI_RECOMMENDATION || audienceList.get(0).getDataSource() == ApplicationSource.KEYWORD_SEARCH) {
            url = syncAudienceToEmailyByEsIdAddUrl(groupId, maxGroupSize);
        }
        HttpResponse response = httpService.post(url, getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] add audience to mongo audience group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] add audience to mongo audience group to Emaily error and response is null, emailyGroupId: {}", SecurityUtils.getUserId(), groupId);
        }
        return response;

    }

    @Override
    public HttpResponse addTalentsToGroup(Long groupId, List<Long> talentIds) throws IOException {
        if (ObjectUtil.isEmpty(groupId) || CollectionUtils.isEmpty(talentIds)) {
            log.error("[EmailService: AudienceServiceImpl] add audience to mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty.");
            throw new CustomParameterizedException("Invalid Input!");
        }

        List<TalentEmailContactVO> talents = talentService.findByTalentIdIn(talentIds).getBody();
        if (Objects.isNull(talents) || talents.isEmpty()) {
            return new HttpResponse(200, "");
        }
        List<MongoAudience> talentAudience = talents.stream().map(t -> new MongoAudience(t.getFullName(), t.getEmail(), t.getTalentId().toString(), ApplicationSource.CANDIDATE_PAGE)).collect(Collectors.toList());
        return this.addAudienceToGroup(groupId, talentAudience, 10000);
    }

    @Override
    public HttpResponse addClientContactsToGroup(Long groupId,  List<Long> clientContactIds) throws IOException {
        if (ObjectUtil.isEmpty(groupId) || CollectionUtils.isEmpty(clientContactIds)) {
            log.error("[EmailService: AudienceServiceImpl] add audience to mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty.");
            throw new CustomParameterizedException("Invalid Input!");
        }

        List<SalesLeadClientContactDTO> clientContacts = companyService.findByClientContactIdIn(clientContactIds).getBody();
        if (Objects.isNull(clientContacts) || clientContacts.isEmpty()) {
            return new HttpResponse(200, "");
        }
        List<MongoAudience> clientContactAudience = clientContacts.stream().filter(o -> CollUtil.isNotEmpty(o.getContacts())).map(c -> new MongoAudience(c.getName(), c.getContacts().get(0).getContact(), c.getId().toString(), ApplicationSource.CLIENT_CONTACT)).collect(Collectors.toList());
        return this.addAudienceToGroup(groupId, clientContactAudience, 10000);
    }

    @Override
    public HttpResponse addUsersToGroup(Long groupId, List<Long> userIds) throws IOException {
        if (ObjectUtil.isEmpty(groupId) || CollectionUtils.isEmpty(userIds)) {
            log.error("[EmailService: AudienceServiceImpl] add audience to mongo audience group to Emaily error, parameter error, the emaily group id and userList cannot be empty.");
            throw new CustomParameterizedException("Invalid Input!");
        }

        List<UserBriefDTO> users = userService.getBriefUsersByIds(userIds).getBody();
        if (Objects.isNull(users) || users.isEmpty()) {
            return new HttpResponse(200, "");
        }
        List<MongoAudience> userAudience = users.stream().map(t -> new MongoAudience(CommonUtils.formatFullName(t.getFirstName(), t.getLastName()), t.getEmail(), t.getId().toString(), ApplicationSource.USER_PAGE)).collect(Collectors.toList());
        return this.addAudienceToGroup(groupId, userAudience, 10000);
    }

    @Override
    public HttpResponse addAudienceToGroupFromCommonPool(Long id, Long emailyGroupId, SearchConditionDTOV2 condition, int maxGroupSize) throws IOException {
        if (ObjectUtil.isEmpty(emailyGroupId)) {
            log.error("[EmailService: AudienceServiceImpl] add common audience to mongo audience group to Emaily error, parameter error, the emaily group id cannot be empty. hitalentGroupId: {}", id);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(condition, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncAudienceToEmailyFromCommonPoolAddUrl(emailyGroupId, maxGroupSize), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] add common audience to mongo audience group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, id, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] add common audience to mongo audience group to Emaily error and response is null, hitalentGroupId: {}, emailyGroupId: {}", SecurityUtils.getUserId(), id, emailyGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse mergeAudienceToGroup(Long targetGroupId, EmailyGroupMergeDTO emailyGroupMergeDTO) throws IOException {
        if (ObjectUtil.isEmpty(targetGroupId) || ObjectUtil.isEmpty(emailyGroupMergeDTO)) {
            log.error("[EmailService: AudienceServiceImpl] merge audiences to mongo audience group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. hitalentGroupId: {}", targetGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(emailyGroupMergeDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncAudienceToEmailyMergeUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] merge audiences to mongo audience group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, targetGroupId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] merge audiences to mongo audience group to Emaily error and response is null, hitalentGroupId: {}", SecurityUtils.getUserId(), targetGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse moveAudienceToGroup(Long sourceGroupId, Long targetGroupId, Long emailySourceGroupId, Long emailyTargetGroupId, List<String> emailyTalentIdList, int maxGroupSize) throws IOException {
        if (ObjectUtil.isEmpty(emailySourceGroupId) || ObjectUtil.isEmpty(emailyTargetGroupId) || CollectionUtils.isEmpty(emailyTalentIdList)) {
            log.error("[EmailService: AudienceServiceImpl] move a mongo audiences to other group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. source: {}, target: {}", sourceGroupId, targetGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(new MongoAudienceMove(emailySourceGroupId, emailyTargetGroupId, emailyTalentIdList, maxGroupSize), SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncAudienceToEmailyMoveUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] move a mongo audiences to other group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, sourceGroupId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, targetGroupId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] move a mongo audiences to other group to Emaily error and response is null, source: {}, target: {}", SecurityUtils.getUserId(), sourceGroupId, targetGroupId);
        }
        return response;
    }

    @Override
    public HttpResponse copyAudienceToGroup(Long sourceGroupId, Long targetGroupId, Long emailySourceGroupId, Long emailyTargetGroupId, List<String> emailyTalentIdList, int maxGroupSize) throws IOException {
        if (ObjectUtil.isEmpty(emailySourceGroupId) || ObjectUtil.isEmpty(emailyTargetGroupId) || CollectionUtils.isEmpty(emailyTalentIdList)) {
            log.error("[EmailService: AudienceServiceImpl] copy a mongo audiences to other group to Emaily error, parameter error, the emaily group id and talentList cannot be empty. source: {}, target: {}", sourceGroupId, targetGroupId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(new MongoAudienceMove(emailySourceGroupId, emailyTargetGroupId, emailyTalentIdList, maxGroupSize), SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncAudienceToEmailyCopyUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] copy a mongo audiences to other group to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, targetGroupId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] copy a mongo audiences to other group to Emaily error and response is null, source: {}, target: {}", SecurityUtils.getUserId(), sourceGroupId, targetGroupId);
        }
        return response;
    }


    @Override
    public HttpResponse updateEmailyAudience(Long talentId, EmailyAudienceUpdateDTO emailyAudienceUpdateDTO) throws IOException {
        if (ObjectUtil.isEmpty(talentId)) {
            log.error("[EmailService: AudienceServiceImpl] update a talent to Emaily error, parameter error, the talent id cannot be empty. talentId: {}", talentId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String groupParamStr = JSON.toJSONString(emailyAudienceUpdateDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncTalentToEmailyUpdateUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: AudienceServiceImpl] update a talent to Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
//            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_EMAILY_GROUP, talentId, AsyncEnum.DATA_TYPE_EMAILY_GROUP, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[EmailService: AudienceServiceImpl @{}] update a talent to Emaily error and response is null, talentId: {}", SecurityUtils.getUserId(), talentId);
        }
        return response;
    }

    public Headers getRequestHeaders() {
        Map<String, String> headersBuilder = new HashMap<>();
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, TOKEN_TYPE + SPACE + currentUserToken);
        headersBuilder.put(EmailConstans.HEADER_CLIENT_ID, ClientTokenHolder.getInstance().getClientId());
        if (SecurityUtils.getUserUid() != null) {
            headersBuilder.put(EmailConstans.HEADER_UID, SecurityUtils.getUserUid());
        }
        return Headers.of(headersBuilder);
    }


}
