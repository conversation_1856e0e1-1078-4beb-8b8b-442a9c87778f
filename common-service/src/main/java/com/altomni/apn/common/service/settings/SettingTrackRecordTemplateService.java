package com.altomni.apn.common.service.settings;

import com.altomni.apn.common.dto.settings.SettingTrackRecordCreateDTO;
import com.altomni.apn.common.dto.settings.SettingTrackRecordDTO;
import com.altomni.apn.common.vo.settings.SettingTrackRecordVO;
import com.altomni.apn.common.vo.settings.SettingTrackRecordViewVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SettingTrackRecordTemplateService {

    Page<SettingTrackRecordVO> searchSettingTrackRecordList(SettingTrackRecordDTO dto, Pageable pageable);

    void save(SettingTrackRecordCreateDTO dto);

    void modify(SettingTrackRecordCreateDTO dto);

    SettingTrackRecordViewVO view(BigInteger id);

    void delete(BigInteger id);

    void update(BigInteger id,Integer status);

    List<SettingTrackRecordVO> queryTrackRecordList();
}
