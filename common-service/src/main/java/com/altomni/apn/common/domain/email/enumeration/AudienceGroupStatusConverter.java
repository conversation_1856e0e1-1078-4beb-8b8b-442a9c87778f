package com.altomni.apn.common.domain.email.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class AudienceGroupStatusConverter extends AbstractAttributeConverter<AudienceGroupStatus, Integer> {
    public AudienceGroupStatusConverter() {
        super(AudienceGroupStatus::toDbValue, AudienceGroupStatus::fromDbValue);
    }
}
