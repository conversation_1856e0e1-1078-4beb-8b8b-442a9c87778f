package com.altomni.apn.common.domain.email;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignArchiveStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignAuditStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignStatus;
import com.altomni.apn.common.service.email.dto.EmailCampaignDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

@Data
@Entity
@Table(name = "email_campaign")
public class EmailCampaign extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "emaily_campaign_id")
    private Long emailyCampaignId;

    @Column(name = "name")
    private String name;

    @Column(name = "creator_id")
    private Long creatorId;

    @Column(name = "status")
    private EmailCampaignStatus status = EmailCampaignStatus.DRAFT;

    @Column(name = "real_time_status")
    private EmailCampaignStatus realTimeStatus = EmailCampaignStatus.DRAFT;

    @Column(name = "archive_status")
    private EmailCampaignArchiveStatus archiveStatus = EmailCampaignArchiveStatus.NOT_ARCHIVED;

    @Column(name = "job_id_list")
    private String jobIdList;

    @Column(name = "email_deafault_subject")
    private String emailDeafaultSubject;

    @Column(name = "email_subject")
    private String emailSubject;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "sender_email")
    private String senderEmail;

    @Column(name = "sender_name")
    private String senderName;

    @Column(name = "default_reply_email")
    private String defaultReplyEmail;

    @Column(name = "audit_status")
    private EmailCampaignAuditStatus auditStatus = EmailCampaignAuditStatus.NOT_APPROVED;

    @Column(name = "group_id")
    private Long groupId;

    @Column(name = "emaily_group_id")
    private Long emailyGroupId;

    @Column(name = "template_id")
    private Long templateId;

    @Column(name = "template_name")
    private String templateName;

    @Column(name = "template_html_content")
    private String templateHtmlContent;

    @Column(name = "estimated_cost")
    private int estimatedCost = 0;

    @Column(name = "reason")
    private String reason;

    @Column(name = "actual_cost")
    private int actualCost = 0;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id","emailyCampaignId", "creatorId", "status", "realTimeStatus", "tenantId", "auditStatus", "estimatedCost", "actualCost"));

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEmailyCampaignId() {
        return emailyCampaignId;
    }

    public void setEmailyCampaignId(Long emailyCampaignId) {
        this.emailyCampaignId = emailyCampaignId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCreatorId() {
        return creatorId;
    }

    public void setCreatorId(Long creatorId) {
        this.creatorId = creatorId;
    }

    public EmailCampaignStatus getStatus() {
        return status;
    }

    public void setStatus(EmailCampaignStatus status) {
        this.status = status;
    }

    public EmailCampaignStatus getRealTimeStatus() {
        return realTimeStatus;
    }

    public void setRealTimeStatus(EmailCampaignStatus realTimeStatus) {
        this.realTimeStatus = realTimeStatus;
    }

    public String getJobIdList() {
        return jobIdList;
    }

    public void setJobIdList(String jobIdList) {
        this.jobIdList = jobIdList;
    }

    public String getEmailSubject() {
        return emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getSenderEmail() {
        return senderEmail;
    }

    public void setSenderEmail(String senderEmail) {
        this.senderEmail = senderEmail;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getDefaultReplyEmail() {
        return defaultReplyEmail;
    }

    public void setDefaultReplyEmail(String defaultReplyEmail) {
        this.defaultReplyEmail = defaultReplyEmail;
    }

    public EmailCampaignAuditStatus getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(EmailCampaignAuditStatus auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getEmailyGroupId() {
        return emailyGroupId;
    }

    public void setEmailyGroupId(Long emailyGroupId) {
        this.emailyGroupId = emailyGroupId;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public int getEstimatedCost() {
        return estimatedCost;
    }

    public void setEstimatedCost(int estimatedCost) {
        this.estimatedCost = estimatedCost;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public int getActualCost() {
        return actualCost;
    }

    public void setActualCost(int actualCost) {
        this.actualCost = actualCost;
    }

    public String getEmailDeafaultSubject() {
        return emailDeafaultSubject;
    }

    public void setEmailDeafaultSubject(String emailDeafaultSubject) {
        this.emailDeafaultSubject = emailDeafaultSubject;
    }

    public String getTemplateHtmlContent() {
        return templateHtmlContent;
    }

    public void setTemplateHtmlContent(String templateHtmlContent) {
        this.templateHtmlContent = templateHtmlContent;
    }

    public static EmailCampaign fromEmailCampaignDTO(EmailCampaignDTO emailCampaignDTO) {
        EmailCampaign createEmailCampaign = new EmailCampaign();
        ServiceUtils.myCopyProperties(emailCampaignDTO, createEmailCampaign);
        return createEmailCampaign;
    }

    public EmailCampaign() {
    }

    public EmailCampaign(Long emailyCampaignId, Long creatorId, String name, Long tenantId) {
        this.emailyCampaignId = emailyCampaignId;
        this.creatorId = creatorId;
        this.name = name;
        this.tenantId = tenantId;
    }
}
