package com.altomni.apn.common.domain.calendar;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarPriorityConverter;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarPriorityEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeConverter;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import lombok.Data;

import javax.persistence.*;
import java.time.Instant;

@Data
@Entity
@Table(name = "calendar_event")
public class CalendarEvent extends AbstractPermissionAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    //0.日历,1.talentNote,2.job,3.company,4.offer,5.offerAccept,6.onboard
    //20.系统日程
    @Column(name = "type_id")
    private Integer typeId;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "reference_id")
    private Long referenceId;

    @Column(name = "go_to_id")
    private Long goToId;

    @Column(name = "title")
    private String title;

    @Column(name = "description")
    private String description;

    @Column(name = "start_time")
    private Instant startTime;

    @Column(name = "end_time")
    private Instant endTime;

    @Column(name = "lark_calendar_id")
    private String larkCalendarId;

    @Column(name = "lark_event_id")
    private String larkEventId;

    @Column(name = "reminder_minutes")
    private Integer reminderMinutes;

    /** 0 任务，1电话，2会议，3到期日，4邮件，5午餐  >=20的是系统日程*/
    @Column(name = "calendar_type")
    @Convert(converter = CalendarTypeConverter.class)
    private CalendarTypeEnum calendarType ;

    /** 0高 1中 2低 */
    @Column(name = "priority")
    @Convert(converter = CalendarPriorityConverter.class)
    private CalendarPriorityEnum priority ;
}
