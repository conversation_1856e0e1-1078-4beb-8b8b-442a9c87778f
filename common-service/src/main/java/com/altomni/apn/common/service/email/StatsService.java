package com.altomni.apn.common.service.email;

import com.altomni.apn.common.domain.email.enumeration.Metric;
import com.altomni.apn.common.dto.http.HttpResponse;
import org.springframework.data.domain.Pageable;

import java.io.IOException;

public interface StatsService {

    HttpResponse getCampaignStatsByTimeRange(Long from, Long to) throws IOException;

    HttpResponse getCampaignsReport(String search, Pageable pageable) throws IOException;

    HttpResponse getCampaignsStatsDetails(Long id, Metric metric) throws IOException;

}
