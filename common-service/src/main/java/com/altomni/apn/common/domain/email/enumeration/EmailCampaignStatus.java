package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EmailCampaignStatus implements ConvertedEnum<Integer> {

    DRAFT(0),
    PENDING(1),
    DENIED(2),
    PROCESSING(3),
    SENT(4),

    REVIEWED(6),
    DELIVERED(7),
    SENT_FALIED(8),
    SCHEDULED(10);

    private final int dbValue;

    EmailCampaignStatus(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<EmailCampaignStatus, Integer> resolver = new ReverseEnumResolver<>(EmailCampaignStatus.class, EmailCampaignStatus::toDbValue);

    public static EmailCampaignStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
