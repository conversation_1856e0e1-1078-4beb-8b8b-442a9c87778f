package com.altomni.apn.common.config.rabbitmq;//package com.altomni.apn.parser.config.rabbitmq;
//
//import com.altomni.apn.common.config.constants.RedisConstants;
//import com.altomni.apn.common.enumeration.ParseStatus;
//import com.altomni.apn.parser.config.env.ApplicationProperties;
//import com.altomni.apn.parser.domain.enumeration.ParseType;
//import com.altomni.apn.parser.service.redis.RedisService;
//import org.apache.commons.lang3.StringUtils;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.amqp.core.Message;
//import org.springframework.amqp.core.MessageProperties;
//import org.springframework.amqp.rabbit.connection.CorrelationData;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.amqp.rabbit.core.RabbitTemplate.ConfirmCallback;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import javax.annotation.Resource;
//import java.util.Map;
//
//@Component
//public class Producer implements ConfirmCallback {
//
//    private final Logger log = LoggerFactory.getLogger(Producer.class);
//
//    @Resource(name = "parserRabbitTemplate")
//    private RabbitTemplate rabbitTemplate;
//
//    @Autowired
//    private RedisService redisService;
//
//    @Resource
//    private ApplicationProperties applicationProperties;
//
//    @PostConstruct
//    public void init() {
//        //specify confirmcallback
//        rabbitTemplate.setConfirmCallback(this);
//    }
//
//    @Override
//    public void confirm(CorrelationData correlationData, boolean ack, String cause) {
//        log.info("Message unique identifier: {}, Confirmation results: {}, Failure reason: {}", correlationData, ack, cause);
//        if (ack) {
//            if (correlationData != null && StringUtils.isNotEmpty(correlationData.getId())) {
//                String messageIdType = correlationData.getId();
//                String messageId = messageIdType.split(",")[0];
//                int messageType = Integer.parseInt(messageIdType.split(",")[1]);
//                String key = "";
//                if (ParseType.RESUME == ParseType.fromDbValue(messageType)) {
//                    key = RedisConstants.DATA_KEY_PARSER + RedisConstants.DATA_KEY_RESUME + messageId + RedisConstants.DATA_KEY_STATUS;
//                }
//                if (ParseType.JD == ParseType.fromDbValue(messageType)  || ParseType.JDTEXT == ParseType.fromDbValue(messageType)) {
//                    key = RedisConstants.DATA_KEY_PARSER + RedisConstants.DATA_KEY_JD+ messageId + RedisConstants.DATA_KEY_STATUS;
//                }
//                if (StringUtils.isNotBlank(key)) {
//                    redisService.setNx(key, ParseStatus.QUEUED.name());
//                }
//            }
//        }
//    }
//
//    public void buildHeaderMap(Map<String, Object> headerMap, String fileName, ParseType contentType, String uuid,
//                               String extractType) {
//        headerMap.put("fileName", fileName);
////        headerMap.put("ContentType", contentType);
//        headerMap.put("uuid", uuid);
//        headerMap.put("fileType", getFileType(fileName));
//        headerMap.put("extractType", extractType);
//    }
//
//    public String getFileType(String fileName) {
//        String sign = ".";
//        if (StringUtils.isEmpty(fileName) || (!fileName.contains(sign))) {
//            return "";
//        }
//
//        return fileName.split("\\.")[1];
//    }
//
//    public MessageProperties buildMessageHeader(Map<String, Object> headerMap) {
//        MessageProperties messageProperties = new MessageProperties();
//        for (Map.Entry<String, Object> entry : headerMap.entrySet()) {
//            messageProperties.setHeader(entry.getKey(), entry.getValue());
//        }
//        return messageProperties;
//    }
//
//    public void sendMassage(String message, Map<String, Object> headerMap, int priority) {
//        Message mqMessage = new Message(message.getBytes(), buildMessageHeader(headerMap));
//        rabbitTemplate.convertAndSend(applicationProperties.getExchange(), applicationProperties.getResumeRoutingKey(), mqMessage, msg -> {
//            msg.getMessageProperties().setPriority(priority);
//            return msg;
//        });
//    }
//
//    public void sendMassage(byte[] message, Map<String, Object> headerMap, int priority, ParseType type) {
//        Message mqMessage = new Message(message, buildMessageHeader(headerMap));
//        CorrelationData correlationData = new CorrelationData();
//        correlationData.setId(headerMap.get("uuid").toString() + "," + type.toDbValue());
//        if (ParseType.RESUME == type) {
//            mqMessage.getMessageProperties().setPriority(Math.max(0, applicationProperties.getMaxPriority() - priority));
//            rabbitTemplate.convertAndSend(applicationProperties.getExchange(), applicationProperties.getResumeRoutingKey(), mqMessage, correlationData);
//        }
//        if (ParseType.JD == type || ParseType.JDTEXT == type ) {
//            rabbitTemplate.convertAndSend(applicationProperties.getExchange(), applicationProperties.getJdRoutingKey(), mqMessage, correlationData);
//        }
//    }
//
//}
