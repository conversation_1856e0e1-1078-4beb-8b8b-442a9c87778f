package com.altomni.apn.common.repository.location;

import com.altomni.apn.common.domain.location.GeoInfoCN;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the GeoInfo entity.
 */
@Repository
public interface GeoInfoCNRepository extends JpaRepository<GeoInfoCN, Long> {

    @Query(value = "SELECT * from city_locations_cn cl left join favorite_country fc on cl.country_iso_code =fc.country_iso_code  where cl.city_name like %?1%  or cl.country_name like %?1%   or cl.subdivision_1_name like %?1% or cl.continent_name like %?1% GROUP BY cl.id order by fc.favorite_level ASC limit ?2",nativeQuery = true)
    List<GeoInfoCN> searchGeoInfo(String condition, Integer item);

    @Query(value = "SELECT * from city_locations_cn where id in (?1) ",nativeQuery = true)
    List<GeoInfoCN> findByIds(List<Long> ids);

}
