package com.altomni.apn.common.service.email.dto;

import lombok.Data;

import java.util.List;

@Data
public class EmailyTemplateHtmlContent {

    public String body;

    public String css;

    public List<HtmlContentFront> fonts;

    public String design;

    private String screenshot;

    private String subject;

    public EmailyTemplateHtmlContent() {
    }

    public EmailyTemplateHtmlContent(String body, String css, List<HtmlContentFront> fonts, String design) {
        this.body = body;
        this.css = css;
        this.fonts = fonts;
        this.design = design;
    }
}
