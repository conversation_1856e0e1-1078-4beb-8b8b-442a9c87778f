package com.altomni.apn.common.web.rest.email;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.searcheamil.SearchEmailByNameDTO;
import com.altomni.apn.common.service.esfller.EsFillerService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.micrometer.core.annotation.Timed;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/v3/mail")
public class SearchEmailResource {

    @Resource
    private EsFillerService esFillerService;

    /**
     * 添加候选人和user 根据名称查邮箱接口
     */
    @PostMapping("/search-email-from-talent-and-user/by-name")
    @Timed
    public ResponseEntity<String> searchEmailFromTalentAndUserByName(@RequestBody SearchEmailByNameDTO searchEmailByNameDTO) {
        log.info("[APN: search email @{}] REST request to searchEmailFromTalentAndUserByName : {}", SecurityUtils.getUserId(), searchEmailByNameDTO);
        return ResponseEntity.ok(esFillerService.searchEmailFromTalentAndUserByName(searchEmailByNameDTO));
    }

    /**
     * 添加候选人和user 根据名称查邮箱接口 入参是多个名称
     */
    @PostMapping("/search-email-from-talent-and-user/by-name/list")
    @Timed
    public ResponseEntity<String> searchEmailFromTalentAndUserByNames(@RequestBody SearchEmailByNameDTO searchEmailByNameDTO) {
        log.info("[APN: search email @{}] REST request to searchEmailFromTalentAndUserByName : {}", SecurityUtils.getUserId(), searchEmailByNameDTO);
        return ResponseEntity.ok(esFillerService.searchEmailFromTalentAndUserByNames(searchEmailByNameDTO));
    }
}
