package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.Comparator;

public enum GmailAliasBindingStatus implements ConvertedEnum<Integer> {

    PENDING(0),
    CONFIRMED(1),

    INVALID(2),

    NONE(99),

    UNNECESSARY(100);

    private final int dbValue;

    public static final GmailAliasBindingStatusComparator COMPARATOR = new GmailAliasBindingStatusComparator();

    GmailAliasBindingStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    public static class GmailAliasBindingStatusComparator implements Comparator<GmailAliasBindingStatus> {

        @Override
        public int compare(GmailAliasBindingStatus status1, GmailAliasBindingStatus status2) {
            return Integer.compare(rank(status1), rank(status2));
        }

        private int rank(GmailAliasBindingStatus status) {
            switch (status) {
                case CONFIRMED: return 4;
                case PENDING: return 3;
                case INVALID: return 2;
                case NONE: return 1;
                default: return 0;
            }
        }
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<GmailAliasBindingStatus, Integer> resolver = new ReverseEnumResolver<>(GmailAliasBindingStatus.class, GmailAliasBindingStatus::toDbValue);

    public static GmailAliasBindingStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
