package com.altomni.apn.common.dto.xxljob;

import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnumConverter;
import lombok.Data;

import javax.persistence.Convert;

@Data
public class XxlJobBaseDTO {

    private Long tenantId;

    private Integer xxlJobId;

    private Long userId;

    private String token;

    private String timezone;

    @Convert(converter = XxlJobRelationTypeEnumConverter.class)
    private XxlJobRelationTypeEnum xxlJobType;

    private Integer count;

    private Integer maxTryCount = 3;

    public boolean checkRetryCount() {
        retryCountCalculate();
        return count < maxTryCount;
    }

    public void retryCountCalculate() {
        if (count == null) {
            count = 0;
        }
        count = count + 1;
    }

}
