package com.altomni.apn.common.domain.parser.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class AsyncBulkParseStatusConverter extends AbstractAttributeConverter<AsyncBulkParseStatus, Integer> {
    public AsyncBulkParseStatusConverter() {
        super(AsyncBulkParseStatus::toDbValue, AsyncBulkParseStatus::fromDbValue);
    }
}
