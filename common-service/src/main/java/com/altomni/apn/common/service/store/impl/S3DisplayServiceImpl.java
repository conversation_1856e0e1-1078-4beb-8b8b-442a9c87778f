package com.altomni.apn.common.service.store.impl;

import com.altomni.apn.common.errors.NoPermissionException;
import com.altomni.apn.common.service.store.S3DisplayService;
import com.altomni.apn.common.service.store.StoreService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SpringUtil;
import com.ipg.resourceserver.instrospection.GrpcOpaqueTokenIntrospector;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseCookie;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Optional;

@Slf4j
@Service
public class S3DisplayServiceImpl implements S3DisplayService {
    private static final String COOKIE_NAME = "APN_S3_DISPLAY_COOKIE";

    private final StoreService storeService;

    public S3DisplayServiceImpl(StoreService storeService) {
        this.storeService = storeService;
    }

    @Override
    public ResponseCookie getCookie() {
        String cookieValue = SecurityUtils.getCurrentUserToken();
        return ResponseCookie.from(COOKIE_NAME, cookieValue)
                .httpOnly(true)
                .path("/common")
                .secure(true)
                .sameSite("None")
                .maxAge(60 * 60 * 24)
                .build();
    }

    @Override
    public String getDisplayUrlVoFromS3(String uuid, String uploadType, String contentType, HttpServletRequest request) {
        Cookie[] cookies = request.getCookies();
        if (cookies == null || cookies.length == 0) {
            throw new NoPermissionException();
        }
        Optional<Cookie> cookieOpt = Arrays.stream(cookies)
                .filter(cookie -> cookie.getName().equals(COOKIE_NAME))
                .findFirst();
        if (cookieOpt.isEmpty()) {
            throw new NoPermissionException();
        }
        String value = cookieOpt.get().getValue();
        try {
            GrpcOpaqueTokenIntrospector tokenIntrospector = SpringUtil.getBean(GrpcOpaqueTokenIntrospector.class);
            tokenIntrospector.introspect(value);
        } catch (Exception e) {
            log.error("Invalid token", e);
            throw new NoPermissionException();
        }
        return storeService.getCommonUrlFromS3(uuid, uploadType, contentType);
    }

    private String formatCacheName(String cookieValue) {
        return COOKIE_NAME + ":" + cookieValue;
    }
}
