package com.altomni.apn.common.domain.email.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ApplicationStatus enumeration.
 */
public enum ApplicationSource implements ConvertedEnum<Integer> {
    BizUser(0),
    HitalentUser(1),
    WechatUser(2),
    Campus_Recruit(3),
    IPG_WEBSITE(4),

    //Source classification added by talent group.
    HEADHUNTER_RECOMMENDATION(5),
    ENTERPRISE_UPLOAD(6),
    HANDSHAKE(7),
    AI_RECOMMENDATION(8),
    KEYWORD_SEARCH(9),
    CA<PERSON>IDATE_PAGE(21),
    CLIENT_CONTACT(22),
    MANUALLY_ADDED(23),
    USER_PAGE(24),
    //Active_ Delivery is only used to retrieve
    ACTIVE_DELIVERY(100),
    NULL(404);

    private final Integer dbValue;

    ApplicationSource(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ApplicationSource, Integer> resolver =
        new ReverseEnumResolver<>(ApplicationSource.class, ApplicationSource::toDbValue);

    public static ApplicationSource fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
