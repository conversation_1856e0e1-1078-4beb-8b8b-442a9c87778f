package com.altomni.apn.common.service.xxljob;

import com.altomni.apn.common.dto.xxljob.SseUserRefresh;
import com.altomni.apn.common.service.sse.SseClient;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Service
public class AsyncSseService {
    @Resource
    private SseClient sseClient;

    @Async
    public CompletableFuture<Void> refreshSseAsync(List<? extends SseUserRefresh> userRefreshList) {
        userRefreshList.forEach(user -> sseClient.pushSseReminderMessageRefresh(user.getUserId(), user.getTenantId()));
        return CompletableFuture.completedFuture(null);
    }
}
