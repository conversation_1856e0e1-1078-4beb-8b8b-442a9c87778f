package com.altomni.apn.common.repository.calendar;

import com.altomni.apn.common.domain.calendar.CalendarEventAttendee;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;

@Repository
public interface CalendarEventAttendeeRepository extends JpaRepository<CalendarEventAttendee, Long> {

    @Modifying
    @Transactional
    @Query(value = " delete FROM calendar_event_attendee where event_id = ?1", nativeQuery = true)
    void deleteByEventId(Long eventId);

    @Modifying
    @Transactional
    @Query(value = " update calendar_event_attendee set is_reminder = ?3 where event_id = ?1 and user_id = ?2 ", nativeQuery = true)
    void updateIsReminderByEventIdAndUserId(Long eventId, Long userId, Integer isReminder);

    @Modifying
    @Transactional
    @Query(value = " update calendar_event_attendee set status = ?2 where event_id = ?1  ", nativeQuery = true)
    void updateStatusByEventIdAndUserId(Long eventId, Integer status);

    @Modifying
    @Transactional
    @Query(value = " update calendar_event_attendee set status = ?2,completed_time=now() where event_id = ?1 ", nativeQuery = true)
    void updateStatusByEventIdAndTimeAndUserId(Long eventId, Integer status);

    @Modifying
    @Transactional
    @Query(value = " update calendar_event_attendee set status = ?2,completed_time=now() where event_id in (?1) ", nativeQuery = true)
    void updateStatusByEventIdInAndTimeAndUserId(List<Long> eventId, Integer status);

    @Modifying
    @Transactional
    @Query(value = " update calendar_event_attendee set status =2 where status=1 and event_id in(select id from calendar_event where end_time<now()) ", nativeQuery = true)
    void updateAttendeesOverdueStatus();

    List<CalendarEventAttendee> findAllByEventIdIn(List<Long> eventIdList);

    List<CalendarEventAttendee> findAllByEventId(Long eventId);

    @Query(value = "select j.id from job j " +
            "inner join job_project jp on jp.id=j.pteam_id " +
            "where j.id in :jobIds", nativeQuery = true)
    Set<Long> findPrivateJobIds(@Param("jobIds") List<Long> jobIds);
}
