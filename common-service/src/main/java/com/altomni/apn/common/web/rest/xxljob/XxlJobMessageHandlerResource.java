package com.altomni.apn.common.web.rest.xxljob;

import com.altomni.apn.common.dto.xxljob.*;
import com.altomni.apn.common.service.xxljob.XxlJobHandlerService;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * xxl job 回调的所有消息预警的处理器
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/xxl-job/message")
public class XxlJobMessageHandlerResource {

    @Resource
    private XxlJobHandlerService xxlJobHandlerService;

    /**
     * 处理定时任务的 日程提醒消息
     * @param xxlJobCalendarEventMessageHandlerDTO
     * @return
     */
    @PostMapping("/calendar-event-message-handler")
    public ResponseEntity<Void> calendarEventMessageHandler(@RequestBody XxlJobCalendarEventMessageHandlerDTO xxlJobCalendarEventMessageHandlerDTO) {
        log.info("[APN: XxlJobMessageHandlerResource] REST request to handler calendar event message , param = {}.", xxlJobCalendarEventMessageHandlerDTO);
        xxlJobHandlerService.calendarEventMessageHandler(xxlJobCalendarEventMessageHandlerDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 处理定时任务的 职位xx未提交候选人,职位xx未面试
     * @param xxlJobReminderForJobHandlerDTO
     * @return
     */
    @PostMapping("/reminder-for-job-handler")
    public ResponseEntity<Void> reminderForJobHandler(@RequestBody XxlJobReminderForJobHandlerDTO xxlJobReminderForJobHandlerDTO) {
        log.info("[APN: XxlJobMessageHandlerResource] REST request to handler reminder for job message , param = {}.", xxlJobReminderForJobHandlerDTO);
        xxlJobHandlerService.reminderForJobHandler(xxlJobReminderForJobHandlerDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 处理定时任务的 admin message 的处理器
     * @param xxlJobAdminMessageHandlerDTO
     * @return
     */
    @PostMapping("/admin-message-handler")
    public ResponseEntity<Void> adminMessageHandler(@RequestBody XxlJobAdminMessageHandlerDTO xxlJobAdminMessageHandlerDTO) {
        log.info("[APN: adminMessageHandler] REST request to handler admin message , param = {}.", xxlJobAdminMessageHandlerDTO);
        xxlJobHandlerService.adminMessageHandler(xxlJobAdminMessageHandlerDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 处理定时任务的 处理候选人 逾期未入职,入职未开票
     * @param
     * @return
     */
    @PostMapping("/reminder-for-application-handler")
    public ResponseEntity<Void> reminderForApplicationHandler(@RequestBody XxlJobReminderForApplicationDTO xxlJobReminderForApplicationDTO) {
        log.info("[APN: XxlJobMessageHandlerResource] REST request to handler reminder for application message , param = {}.", xxlJobReminderForApplicationDTO);
        xxlJobHandlerService.reminderForApplicationHandler(xxlJobReminderForApplicationDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 处理定时任务的 处理入职后，逾期未回款
     * @param
     * @return
     */
    @PostMapping("/reminder-for-invoice-overdue-handler")
    public ResponseEntity<Void> reminderForInvoiceOverDueHandler(@RequestBody XxlJobReminderForInvoiceOverDueDTO xxlJobReminderForInvoiceOverDueDTO) {
        log.info("[APN: XxlJobMessageHandlerResource] REST request to reminder for invoice overdue handler , param = {}.", xxlJobReminderForInvoiceOverDueDTO);
        xxlJobHandlerService.reminderForInvoiceOverDueHandler(xxlJobReminderForInvoiceOverDueDTO);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/reminder-for-unSubmit-candidates-for-team-handler")
    public ResponseEntity<Void> reminderForUnSubmitCandidatesForTeamHandler(@RequestBody XxlJobReminderForUnSubmitCandidatesForTeamHandlerDTO dto) {
        log.info("[APN: XxlJobMessageHandlerResource @{}] REST request to reminder for unSubmit candidates for team . param = {}", dto);
        xxlJobHandlerService.reminderForUnSubmitCandidatesForTeamHandler(dto);
        return ResponseEntity.ok().build();
    }

    /**
     * 定时任务每小时一次,预警 团队未入职预警
     * @return
     */
    @PostMapping("/create-no-onboard-for-team")
    public ResponseEntity<Void> createNoOnboardForTeam(@RequestBody XxlJobBaseDTO xxlJobBaseDTO) {
        log.info("[APN: XxlJobResource @{}] REST request to init create or update no onboard for team .", SecurityUtils.getUserId());
        xxlJobHandlerService.createNoOnboardForTeam(xxlJobBaseDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 团队未提交候选人数量
     * @return
     */
    @PostMapping("/create-or-update-un-submit-talent-for-team")
    public ResponseEntity<Void> createOrUpdateUnSubmittedCandidatesForTeam(@RequestParam("tenantId") Long tenantId) {
        log.info("[APN: XxlJobResource @{}] REST request to init create or update no onboard for team .", SecurityUtils.getUserId());
        xxlJobHandlerService.createOrUpdateUnSubmittedCandidatesForTeam(tenantId);
        return ResponseEntity.ok().build();
    }

    /**
     * 初始化 团队未提交候选人消息提醒的 xxl-job
     * @return
     */
    @PostMapping("/init-create-or-update-un-submit-talent-for-team")
    public ResponseEntity<Void> initCreateOrUpdateUnSubmittedCandidatesForTeam() {
        log.info("[APN: XxlJobResource @{}] REST request to init create or update no onboard for team .", SecurityUtils.getUserId());
        xxlJobHandlerService.initCreateOrUpdateUnSubmittedCandidatesForTeam();
        return ResponseEntity.ok().build();
    }

    /**
     *  lark 发送apn上线消息的 xxl-job
     * @return
     */
    @PostMapping("/send-apn-announcement")
    public ResponseEntity<Void> sendAPNAnnouncement(@RequestBody XxlJobApnAnnouncementDTO xxlJobApnAnnouncementDTO) throws InterruptedException, IOException {
        log.info("[APN: XxlJobResource @{}] REST request to send apn announcement from lark, params = {}", SecurityUtils.getUserId(), xxlJobApnAnnouncementDTO);
        xxlJobHandlerService.sendAPNAnnouncement(xxlJobApnAnnouncementDTO);
        return ResponseEntity.ok().build();
    }

}
