package com.altomni.apn.common.service.xxljob;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobInfoDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateBySendTimeForJobAdminDTO;
import com.altomni.apn.common.utils.DateUtil;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 同步数据到xxl-job中
 */
@Slf4j
@RefreshScope
@Configuration
public class XxlJobClient {

    @Value("${xxl.job.url:http://127.0.0.1:8080/xxl-job-admin}")
    private String xxlJobBaseUrl;

    @Value("${xxl.job.groupid:1}")
    private Integer groupId;

    @Value("${xxl.job.access_token_key:admin}")
    private String accessTokenKey;

    @Value("${xxl.job.access_token_value:123456}")
    private String accessTokenValue;

    @Value("${xxl.job.skip:true}")
    private Boolean skip;

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("async-sync-xxl-job-pool-").build();

    public static final ExecutorService executorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 3,
            Runtime.getRuntime().availableProcessors() * 5, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100000), THREAD_FACTORY);

    /**
     * 增加执行任务
     *
     * @param xxlJobApnDTO
     * xxljob 的返参： {"msg":null,"code":200,"content":"13"}
     */
    public Integer addExecutorTask(XxlJobApnDTO xxlJobApnDTO, int deep) {
        log.info("addExecutorTask skip = {}", skip);
        if (skip) {
            return null;
        }
        XxlJobInfoDTO xxlJobInfoDTO = getParamEntity(xxlJobApnDTO);
        log.info("增加xxl执行任务,请求参数:{}", xxlJobInfoDTO);
        HttpResponse response = HttpRequest.post(xxlJobBaseUrl + "/jobinfo/my-add")
                .form(JSONUtil.parseObj(xxlJobInfoDTO)).addHeaders(getHeaders()).execute();
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            log.info("增加xxl执行任务成功,返回信息:{}", jsonObject);
            return jsonObject.getInt("content");
        }
        if (deep < 2) {
            try {
                TimeUnit.MILLISECONDS.sleep(deep * 500L);
                return addExecutorTask(xxlJobApnDTO, deep + 1);
            } catch (InterruptedException e) {
                log.error("add executor task sleep is error");
                throw new RuntimeException("调用xxl增加执行任务失败");
            }
        } else {
            log.error("调用xxl增加执行任务失败:{}", response.body());
            throw new RuntimeException("调用xxl增加执行任务失败");
        }
    }

    /**
     * 批量增加执行任务
     *
     * @param xxlJobApnDTOList
     * xxljob 的返参： {"msg":null,"code":200,"content":"13"}
     */
    public Map<String, Integer> addJobS(List<XxlJobApnDTO> xxlJobApnDTOList, int deep) {
        log.info("addJobS deep = {}, skip = {}", deep, skip);
        if (skip) {
            return null;
        }
        List<XxlJobInfoDTO> xxlJobInfoDTOList = xxlJobApnDTOList.stream().map(this::getParamEntity).toList();
        HttpResponse response = HttpRequest.post(xxlJobBaseUrl + "/jobinfo/add-jobs")
                .body(JSONUtil.toJsonStr(xxlJobInfoDTOList)).addHeaders(getHeaders()).execute();
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            log.info("addJobS is success ,result:{}", jsonObject);
            JSONObject jsonMap = jsonObject.getJSONObject("content");
            Map<String, Integer> resultMap = new HashMap<>(16);
            jsonMap.forEach((key, value) -> resultMap.put(key, Integer.parseInt(value.toString())));
            return resultMap;
        }
        if (deep < 2) {
            try {
                TimeUnit.MILLISECONDS.sleep(deep * 500L);
                return addJobS(xxlJobApnDTOList, deep + 1);
            } catch (InterruptedException e) {
                log.error("addJobS task sleep is error");
                throw new RuntimeException("addJobS is fail");
            }
        } else {
            log.error("addJobS is fail:{}", response.body());
            throw new RuntimeException("addJobS is fail");
        }
    }

    /**
     * 修改执行任务
     *
     * @param xxlJobApnDTO
     */
    public void updateExecutorTask(XxlJobApnDTO xxlJobApnDTO, int deep) {
        log.info("updateExecutorTask skip = {}", skip);
        if (skip) {
            return;
        }
        XxlJobInfoDTO xxlJobInfoDTO = getParamEntity(xxlJobApnDTO);
        log.info("updateExecutorTask is request , json:{}", JSONUtil.toJsonStr(xxlJobInfoDTO));
        HttpResponse response = HttpRequest.post(xxlJobBaseUrl + "/jobinfo/my-update")
                .form(JSONUtil.parseObj(xxlJobInfoDTO)).addHeaders(getHeaders()).execute();
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            log.info("updateExecutorTask is success , result:{}", jsonObject);
            return;
        }
        if (deep < 2) {
            try {
                TimeUnit.MILLISECONDS.sleep(deep * 500L);
                updateExecutorTask(xxlJobApnDTO, deep + 1);
            } catch (InterruptedException e) {
                log.error("updateExecutorTask sleep is error");
                throw new RuntimeException("updateExecutorTask is fail");
            }
        } else {
            log.error("updateExecutorTask is fail:{}", response.body());
            throw new RuntimeException("updateExecutorTask is fail");
        }
    }

    /**
     * 修改执行任务
     * 只修改发送时间
     *
     * @param xxlJobUpdateBySendTimeList
     */
    public void updateJobsBySendTime(List<XxlJobUpdateBySendTimeForJobAdminDTO> xxlJobUpdateBySendTimeList, int deep) {
        log.info("updateJobsBySendTime param: {}, deep = {} ,skip = {}", xxlJobUpdateBySendTimeList, deep, skip);
        if (skip) {
            return;
        }
        HttpResponse response = HttpRequest.post(xxlJobBaseUrl + "/jobinfo/update-jobs-by-sendTime").body(JSONUtil.toJsonStr(xxlJobUpdateBySendTimeList))
                .addHeaders(getHeaders()).execute();
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            log.info("updateJobsBySendTime is success,返回信息:{}", jsonObject);
            return;
        }
        if (deep < 2) {
            try {
                TimeUnit.MILLISECONDS.sleep(deep * 500L);
                updateJobsBySendTime(xxlJobUpdateBySendTimeList, deep + 1);
            } catch (InterruptedException e) {
                log.error("updateJobsBySendTime task sleep is error");
                throw new RuntimeException("updateJobsBySendTime is fail");
            }
        } else {
            log.error("updateJobsBySendTime is fail:{}", response.body());
            throw new RuntimeException("updateJobsBySendTime is fail");
        }
    }

    /**
     * 删除执行任务
     *
     * @param xxlJobId
     */
    public void deleteExecutorTask(Integer xxlJobId, int deep) {
        log.info("deleteExecutorTask param: {}, deep = {}, skip = {}", xxlJobId, deep, skip);
        if (skip) {
            return;
        }
        HttpResponse response = HttpRequest.post(xxlJobBaseUrl + "/jobinfo/my-remove?id=" + xxlJobId)
                .addHeaders(getHeaders()).execute();
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            log.info("deleteExecutorTask is success, result:{}", jsonObject);
            return;
        }
        if (deep < 2) {
            try {
                TimeUnit.MILLISECONDS.sleep(deep * 500L);
                deleteExecutorTask(xxlJobId, deep + 1);
            } catch (InterruptedException e) {
                log.error("deleteExecutor task sleep is error");
                throw new RuntimeException("deleteExecutorTask is fail");
            }
        } else {
            log.error("deleteExecutorTask is fail:{}", response.body());
            throw new RuntimeException("deleteExecutorTask is fail");
        }
    }

    /**
     * 增加执行任务
     *
     * @param xxlJobIds
     */
    public void deleteXxlJobs(List<Integer> xxlJobIds, int deep) {
        log.info("deleteXxlJobs param: {}, deep = {}, skip = {}", xxlJobIds, deep, skip);
        if (skip) {
            return;
        }
        HttpResponse response = HttpRequest.post(xxlJobBaseUrl + "/jobinfo/delete-jobs")
                .body(JSONUtil.toJsonStr(xxlJobIds)).addHeaders(getHeaders()).execute();
        if (response.isOk()) {
            JSONObject jsonObject = JSONUtil.parseObj(response.body());
            log.info("deleteXxlJobs is success,result:{}", jsonObject);
            return;
        }
        if (deep < 2) {
            try {
                TimeUnit.MILLISECONDS.sleep(deep * 500L);
                deleteXxlJobs(xxlJobIds, deep + 1);
            } catch (InterruptedException e) {
                log.error("deleteXxlJobs task sleep is error");
                throw new RuntimeException("deleteXxlJobs is fail");
            }
        } else {
            log.error("deleteXxlJobs is fail:{}", response.body());
            throw new RuntimeException("deleteXxlJobs is fail");
        }
    }

    private XxlJobInfoDTO getParamEntity(XxlJobApnDTO xxlJobApnDTO) {
        XxlJobInfoDTO xxlJobInfoDTO = new XxlJobInfoDTO();
        BeanUtil.copyProperties(xxlJobApnDTO, xxlJobInfoDTO, true);
        xxlJobInfoDTO.setScheduleConf(xxlJobApnDTO.getCron());
        //设置必填字段
        //xxl job 任务执行的入参
        if (CollUtil.isNotEmpty(xxlJobApnDTO.getExecutorParam())) {
            xxlJobInfoDTO.setExecutorParam(JSONUtil.toJsonStr(xxlJobApnDTO.getExecutorParam()));
        }
        // 设置执行任务的时间, xxl-job 放在utc 的时区,所有时间转为utc时间
        if (ObjectUtil.isNotEmpty(xxlJobApnDTO.getTriggerTime())) {
            xxlJobInfoDTO.setScheduleConf(DateUtil.getCron(xxlJobApnDTO.getTriggerTime()));
        }
        xxlJobInfoDTO.setJobGroup(groupId);
        return xxlJobInfoDTO;
    }

    public Map<String, String> getHeaders() {
        Map<String, String> paramMap = new HashMap<>( 16);
        paramMap.put(accessTokenKey, accessTokenValue);
        return paramMap;
    }
}
