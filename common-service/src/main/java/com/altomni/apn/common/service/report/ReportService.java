package com.altomni.apn.common.service.report;

import com.altomni.apn.common.domain.user.GetLastWeekActiveDurationUserInfoDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.dto.recruiting.UserAdoptionReportDTO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByCompanyVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.recruiting.TeamAndUserAdoptionReportVO;
import com.altomni.apn.common.vo.user.UserActiveDurationStatistic;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(value = "report-service")
public interface ReportService {

    @PostMapping("/report/api/v3/recruiting/kpi-report-by-user")
    ResponseEntity<List<RecruitingKpiByUserVO>> searchRecruitingKpiReportByUser(@RequestBody RecruitingKpiReportSearchDto searchDto);

    @PostMapping("/report/api/v3/recruiting/kpi-report-by-company")
    ResponseEntity<List<RecruitingKpiByCompanyVO>> searchRecruitingKpiReportByCompany(@RequestBody RecruitingKpiReportSearchDto searchDto);

    @PostMapping("/report/api/v3/last-week-active-duration-user-info")
    ResponseEntity<UserActiveDurationStatistic> getLastWeekActiveDurationUserInfo(GetLastWeekActiveDurationUserInfoDTO dto);

    @PostMapping("/report/api/v3/user-adoption-report/for-xxl-job")
    ResponseEntity<TeamAndUserAdoptionReportVO> e5UserAdoptionReportByUsersForXxlJob(@RequestBody UserAdoptionReportDTO userAdoptionReportDTO);
}
