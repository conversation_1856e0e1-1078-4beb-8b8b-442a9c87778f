package com.altomni.apn.common.domain.upgrade;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "upgrade_user_relation")
public class UpgradeUserRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "upgrade_id")
    private Long upgradeId;

    @Column(name = "user_id")
    private Long userId;

    public UpgradeUserRelation(Long upgradeId, Long userId) {
        this.upgradeId = upgradeId;
        this.userId = userId;
    }

    public UpgradeUserRelation() {

    }
}
