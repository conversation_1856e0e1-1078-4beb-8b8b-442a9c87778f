package com.altomni.apn.common.domain.reportsubscription;

import com.altomni.apn.common.enumeration.reportSubscriptions.PushMethod;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushMethodConverter;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "report_subscription_push_method")
public class ReportSubscriptionPushMethod {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "subscription_id")
    private Long subscriptionId;

    @Convert(converter = PushMethodConverter.class)
    @Column(name = "push_method")
    private PushMethod pushMethod;

}
