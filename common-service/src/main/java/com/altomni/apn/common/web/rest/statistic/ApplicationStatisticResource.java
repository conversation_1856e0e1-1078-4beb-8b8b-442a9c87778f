package com.altomni.apn.common.web.rest.statistic;

import com.altomni.apn.common.dto.xxljob.RefreshApplicationStopStatisticDTO;
import com.altomni.apn.common.service.statistic.ApplicationStatistic;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ApplicationStatisticResource {

    @Resource
    private ApplicationStatistic applicationStatistic;

    /**
     * 创建xxl-job任务
     * @return
     */
    @PostMapping("/statistic/refreshApplicationStopStatistic")
    public ResponseEntity<Integer> refreshApplicationStopStatistic(RefreshApplicationStopStatisticDTO dto) {
        log.info("[APN: XxlJobResource @{}] REST request to refresh application stop statistic", SecurityUtils.getUserId());
        applicationStatistic.refreshApplicationStopStatistic(dto);
        return ResponseEntity.ok().build();
    }


}
