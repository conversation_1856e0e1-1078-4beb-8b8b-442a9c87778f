package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EmailyScheduleInfoSendType implements ConvertedEnum<Integer> {

    NOW(0);

    private final int dbValue;

    EmailyScheduleInfoSendType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<EmailyScheduleInfoSendType, Integer> resolver = new ReverseEnumResolver<>(EmailyScheduleInfoSendType.class, EmailyScheduleInfoSendType::toDbValue);

    public static EmailyScheduleInfoSendType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
