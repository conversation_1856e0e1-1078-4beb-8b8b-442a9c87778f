package com.altomni.apn.common.service.email.dto;

import com.altomni.apn.common.domain.email.enumeration.ApplicationSource;
import lombok.Data;

import java.io.Serializable;

@Data
public class MongoAudience implements Serializable {

    private String audienceId;

    private String name;

    private String email;

    private String dataSourceId;

    private String subgroupId;

    private ApplicationSource dataSource;

    public MongoAudience() {}

    public MongoAudience(String dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public MongoAudience(String name, String email, String dataSourceId) {
        this.name = name;
        this.email = email;
        this.dataSourceId = dataSourceId;
    }

    public MongoAudience(String name, String email, String dataSourceId, ApplicationSource dataSource) {
        this.name = name;
        this.email = email;
        this.dataSourceId = dataSourceId;
        this.dataSource = dataSource;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDataSourceId() {
        return dataSourceId;
    }

    public void setDataSourceId(String dataSourceId) {
        this.dataSourceId = dataSourceId;
    }

    public ApplicationSource getDataSource() {
        return dataSource;
    }

    public void setDataSource(ApplicationSource dataSource) {
        this.dataSource = dataSource;
    }

    public String getAudienceId() {
        return audienceId;
    }

    public void setAudienceId(String audienceId) {
        this.audienceId = audienceId;
    }

    public String getSubgroupId() {
        return subgroupId;
    }

    public void setSubgroupId(String subgroupId) {
        this.subgroupId = subgroupId;
    }

    @Override
    public int hashCode() {
        int result = 17;
        result = result * 31 + dataSourceId.hashCode();
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof MongoAudience)) {
            return false;
        }
        if (o instanceof MongoAudience) {
            MongoAudience mongoAudience = (MongoAudience)o;
            return mongoAudience.dataSourceId.equals(dataSourceId) || mongoAudience.email.equals(email);
        }
        return false;
    }

}
