package com.altomni.apn.common.domain.parser;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.parser.enumeration.ParseType;
import com.altomni.apn.common.domain.parser.enumeration.ParseTypeConverter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A ParseRecord.
 */
@Entity
@Table(name = "parse_record")
public class ParseRecord extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Convert(converter = ParseTypeConverter.class)
    @Column(name = "jhi_type", nullable = false)
    private ParseType type = ParseType.RESUME;

    @Column(name = "original_file_name")
    private String originalFileName;

    @Column(name = "is_reviewed")
    private Boolean isReviewed = Boolean.FALSE;


    @Column(name = "original_text")
    private String originalText;

    @Column(name = "parse_result")
    private String parseResult;

    @Column(name = "note")
    private String note;

    @Column(name = "uuid")
    private String uuid;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ParseType getType() {
        return type;
    }

    public ParseRecord type(ParseType type) {
        this.type = type;
        return this;
    }

    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public String getParseResult() {
        return parseResult;
    }

    public void setParseResult(String parseResult) {
        this.parseResult = parseResult;
    }

    public void setType(ParseType type) {
        this.type = type;
    }

    public String getOriginalFileName() {
        return originalFileName;
    }

    public ParseRecord originalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
        return this;
    }

    public void setOriginalFileName(String originalFileName) {
        this.originalFileName = originalFileName;
    }

    public Boolean getReviewed() {
        return isReviewed;
    }

    public void setReviewed(Boolean reviewed) {
        isReviewed = reviewed;
    }

    public ParseRecord isReviewed(Boolean isReviewed) {
        this.isReviewed = isReviewed;
        return this;
    }

    public String getNote() {
        return note;
    }

    public ParseRecord note(String note) {
        this.note = note;
        return this;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ParseRecord parseRecord = (ParseRecord) o;
        if (parseRecord.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), parseRecord.getId());
    }


    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "ParseRecord{" +
            "id=" + getId() +
            ", type='" + getType() + "'" +
            ", originalFileName='" + getOriginalFileName() + "'" +
            ", isReviewed='" + getReviewed() + "'" +
            ", note='" + getNote() + "'" +
            "}";
    }
}
