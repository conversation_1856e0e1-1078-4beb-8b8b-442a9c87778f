package com.altomni.apn.common.domain.email.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class AudienceGroupTypeConverter extends AbstractAttributeConverter<AudienceGroupType, Integer> {
    public AudienceGroupTypeConverter() {
        super(AudienceGroupType::toDbValue, AudienceGroupType::fromDbValue);
    }
}
