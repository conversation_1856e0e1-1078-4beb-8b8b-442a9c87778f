package com.altomni.apn.common.service.upgrade;

import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.dto.upgrade.UpgradeCreationDTO;
import com.altomni.apn.common.vo.upgrade.UpgradeNotificationVO;

public interface UpgradeService {

    void create(UpgradeCreationDTO upgradeCreationDTO);

    UpgradeNotificationVO getUpgradeNotification(Long userId, Long tenantId, LanguageEnum language);

    void updateUpgradeUserRelation(Long upgradeId, Long userId, Long tenantId);
}
