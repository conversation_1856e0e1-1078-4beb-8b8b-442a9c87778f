package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ErrorCampaignRecordType implements ConvertedEnum<Integer> {

    PRODUCTER_CONNECTION_MQ_FAILED(0),
    PRODUCTER_CONNECTION_EXCHANGE_FAILED(1),
    PRODUCTER_CONNECTION_QUEUE_FAILED(2),
    CUSTOMER_RETRY_TIMES_EXCEEDED(3),
    ORDINARY(4);

    private final int dbValue;

    ErrorCampaignRecordType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<ErrorCampaignRecordType, Integer> resolver = new ReverseEnumResolver<>(ErrorCampaignRecordType.class, ErrorCampaignRecordType::toDbValue);

    public static ErrorCampaignRecordType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
