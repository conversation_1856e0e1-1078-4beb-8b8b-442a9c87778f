package com.altomni.apn.common.utils;

import com.altomni.apn.common.dto.calendar.SystemCalendarStatisticCacheDTO;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class CalendarCacheConfig {
    /**
     * 创建一个带有8小时过期时间的Guava缓存，值被包装以包含时间戳
     */
    @Bean
    public Cache<Long, TimestampedValue<SystemCalendarStatisticCacheDTO>> guavaTimestampedCache() {
        return CacheBuilder.newBuilder()
                .expireAfterWrite(8, TimeUnit.HOURS)  // 写入后8小时过期
                .maximumSize(1000)                   // 最大缓存条目数
                .recordStats()                        // 开启统计
                .build();
    }
}
