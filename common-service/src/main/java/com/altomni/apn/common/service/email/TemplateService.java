package com.altomni.apn.common.service.email;

import com.altomni.apn.common.domain.email.enumeration.EmailTemplateType;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.email.dto.EmailyTemplateDTO;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;

public interface TemplateService {

    HttpResponse getTemplateById(Long templateId) throws IOException;

    HttpResponse searchEmailTemplates(EmailTemplateType type, Long tagsId, String name, List<Long> creatorUserIds, Pageable pageable, String sort) throws IOException;

    HttpResponse searchMyTemplates(String name, Pageable pageable) throws IOException;

    HttpResponse searchApplicationTemplates(Long tagId, String name, Pageable pageable) throws IOException;

    HttpResponse getAllEmailTemplateTags() throws IOException;

    HttpResponse createEmailTemplate(EmailyTemplateDTO templateDTO) throws IOException;

    HttpResponse updateEmailTemplate(Long templateId, EmailyTemplateDTO templateDTO) throws IOException;

    HttpResponse deactivateTemplateByIds(List<Long> templateIds) throws IOException;

    HttpResponse deleteTemplateById(Long templateId) throws IOException;

    HttpResponse searchEmailTemplateByName(String name) throws IOException;

}
