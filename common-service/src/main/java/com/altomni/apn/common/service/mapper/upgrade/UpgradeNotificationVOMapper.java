package com.altomni.apn.common.service.mapper.upgrade;

import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.upgrade.UpgradeNotification;
import com.altomni.apn.common.service.mapper.EntityMapper;
import com.altomni.apn.common.vo.upgrade.UpgradeNotificationVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.WARN)
public interface UpgradeNotificationVOMapper extends EntityMapper<UpgradeNotificationVO, UpgradeNotification> {

    default String mapContentsToContent(UpgradeNotification upgradeNotification, LanguageEnum language) {
        return switch (language) {
            case EN -> upgradeNotification.getContentEn();
            case ZH -> upgradeNotification.getContentCn();
            default -> null;
        };
    }

    default String mapLinkToLink(UpgradeNotification upgradeNotification, LanguageEnum language) {
        return switch (language) {
            case EN -> upgradeNotification.getLinkEn();
            case ZH -> upgradeNotification.getLinkCn();
            default -> null;
        };
    }

    default UpgradeNotificationVO toDto(UpgradeNotification entity, LanguageEnum language) {
        if (entity == null) {
            return null;
        }
        UpgradeNotificationVO upgradeNotificationVO = new UpgradeNotificationVO();
        upgradeNotificationVO.setId(entity.getId());
        upgradeNotificationVO.setUpgradeTime(entity.getUpgradeTime());
        upgradeNotificationVO.setContent(mapContentsToContent(entity, language));
        upgradeNotificationVO.setLink(mapLinkToLink(entity, language));
        upgradeNotificationVO.setVersion(entity.getVersion());
        upgradeNotificationVO.setGlobal(entity.getGlobal());
        return upgradeNotificationVO;
    }

}
