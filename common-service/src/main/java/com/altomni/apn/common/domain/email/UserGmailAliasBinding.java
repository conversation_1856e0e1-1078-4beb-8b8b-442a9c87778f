package com.altomni.apn.common.domain.email;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.email.enumeration.GmailAliasBindingStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "email_user_gmail_alias_binding")
public class UserGmailAliasBinding extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "raw_gmail_account")
    private String rawGmailAccount;

    @Column(name = "binding_status")
    private GmailAliasBindingStatus bindingStatus = GmailAliasBindingStatus.PENDING;

}
