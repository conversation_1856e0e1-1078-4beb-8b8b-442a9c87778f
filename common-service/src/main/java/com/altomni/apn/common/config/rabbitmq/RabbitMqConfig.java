package com.altomni.apn.common.config.rabbitmq;//package com.altomni.apn.parser.config.rabbitmq;
//
//import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
//import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
//import org.springframework.amqp.rabbit.connection.ConnectionFactory;
//import org.springframework.amqp.rabbit.core.RabbitAdmin;
//import org.springframework.amqp.rabbit.core.RabbitTemplate;
//import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
//import org.springframework.cloud.context.config.annotation.RefreshScope;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Primary;
//import org.springframework.stereotype.Component;
//
//@Component
//@RefreshScope
//public class RabbitMqConfig {
//
//    @Value("${spring.rabbitmq.addresses}")
//    private String addresses;
//
//    @Value("${spring.rabbitmq.port}")
//    private Integer port;
//
//    @Value("${spring.rabbitmq.username}")
//    private String username;
//
//    @Value("${spring.rabbitmq.password}")
//    private String password;
//
//    @Bean(name = "parserConnectionFactory")
//    @Primary
//    public ConnectionFactory parserConnectionFactory() {
//        return connectionFactory (addresses, port, username, password);
//    }
//
//    public CachingConnectionFactory connectionFactory(String host, int port, String username, String password) {
//        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
//        connectionFactory.setHost(host);
//        connectionFactory.setPort(port);
//        connectionFactory.setUsername(username);
//        connectionFactory.setPassword(password);
//        connectionFactory.setPublisherConfirms(true);
//        return connectionFactory;
//    }
//
//    @Bean(name = "parserFactory")
//    public SimpleRabbitListenerContainerFactory secondFactory(
//            SimpleRabbitListenerContainerFactoryConfigurer configurer,
//            @Qualifier("parserConnectionFactory") ConnectionFactory connectionFactory
//    ) {
//        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
//        configurer.configure(factory, connectionFactory);
//        return factory;
//    }
//
//    @Bean(name = "parserRabbitAdmin")
//    public RabbitAdmin parserRabbitAdmin(@Qualifier("parserConnectionFactory") ConnectionFactory connectionFactory) {
//        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
//        rabbitAdmin.setAutoStartup(true);
//        return rabbitAdmin;
//    }
//
//    @Bean(name = "parserRabbitTemplate")
//    @Primary
//    public RabbitTemplate parserRabbitTemplate(
//            @Qualifier("parserConnectionFactory") ConnectionFactory connectionFactory
//    ){
//        RabbitTemplate parserRabbitTemplate = new RabbitTemplate(connectionFactory);
//        parserRabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
//        return parserRabbitTemplate;
//    }
//}
