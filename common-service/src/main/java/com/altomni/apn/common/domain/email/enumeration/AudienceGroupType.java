package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The EmailListSource enumeration.
 */
public enum AudienceGroupType implements ConvertedEnum<Integer> {
    IMPORT(0),
    SEARCH(1),
    MONGO_LIST(2);


    private final int dbValue;

    AudienceGroupType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<AudienceGroupType, Integer> resolver =
        new ReverseEnumResolver<>(AudienceGroupType.class, AudienceGroupType::toDbValue);

    public static AudienceGroupType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
