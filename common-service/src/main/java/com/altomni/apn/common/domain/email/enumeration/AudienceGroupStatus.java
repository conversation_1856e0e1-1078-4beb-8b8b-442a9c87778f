package com.altomni.apn.common.domain.email.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The EmailBlastStatus enumeration.
 */
public enum AudienceGroupStatus implements ConvertedEnum<Integer> {
    ARCHIVED(0),
    VALID(1);

    private final Integer dbValue;

    AudienceGroupStatus(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<AudienceGroupStatus, Integer> resolver =
        new ReverseEnumResolver<>(AudienceGroupStatus.class, AudienceGroupStatus::toDbValue);

    public static AudienceGroupStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
