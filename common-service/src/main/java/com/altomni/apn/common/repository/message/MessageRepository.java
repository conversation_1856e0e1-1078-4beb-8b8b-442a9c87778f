package com.altomni.apn.common.repository.message;

import com.altomni.apn.common.domain.message.Message;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {


    @Modifying
    @Transactional
    @Query(nativeQuery = true, value = """ 
        update message set cn_title = ?2, en_title = ?2, content = ?3 where id = ?1
    """)
    void updateMessageTitleContent(Long id, String title, String content);

}
