package com.altomni.apn.common.domain.calendar;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarRelationEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarRelationEnumConverter;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "calendar_event_relation_info")
public class CalendarEventRelationInfo extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**  */
    @Column(name = "event_id")
    private Long eventId ;

    /** 0客户 1客户联系人 2候选人 4线索 */
    @Column(name = "relation_type")
    @Convert(converter = CalendarRelationEnumConverter.class)
    private CalendarRelationEnum relationType ;

    /**  */
    @Column(name = "relation_id")
    private Long relationId ;

    /**  */
    @Column(name = "relation_name")
    private String relationName ;

    @Column(name = "company_status")
    private Integer companyStatus ;
}
