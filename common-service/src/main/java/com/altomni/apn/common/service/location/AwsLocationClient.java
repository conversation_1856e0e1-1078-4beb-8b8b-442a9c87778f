package com.altomni.apn.common.service.location;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.env.location.LocationProperties;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.location.dto.AwsLocationSearchDTO;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.location.AmazonLocation;
import com.amazonaws.services.location.AmazonLocationClientBuilder;
import com.amazonaws.services.location.model.GetPlaceRequest;
import com.amazonaws.services.location.model.GetPlaceResult;
import com.amazonaws.services.location.model.SearchPlaceIndexForSuggestionsRequest;
import com.amazonaws.services.location.model.SearchPlaceIndexForSuggestionsResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
*
* <AUTHOR>
* date:2023-04-14
*/
@Slf4j
@Service
@RefreshScope
public class AwsLocationClient {

    @Resource
    private LocationProperties locationProperties;

    private final AmazonLocation amazonLocationClient;

    public AwsLocationClient(@Value("${application.aws.accessKey:}") String accessKey,
                             @Value("${application.aws.secretKey:}") String secretKey,
                             @Value("${application.locationService.aws.region:}") String region) {
        this.amazonLocationClient = AmazonLocationClientBuilder
                .standard()
                .withCredentials(new AWSStaticCredentialsProvider(new BasicAWSCredentials(accessKey, secretKey)))
                .withRegion(region)
                .build();
    }

    public String searchPlaceIndexForSuggestions(AwsLocationSearchDTO awsLocationSearchDTO) {

        checkMaxResults(awsLocationSearchDTO);

        SearchPlaceIndexForSuggestionsRequest request =
                new SearchPlaceIndexForSuggestionsRequest()
                        .withIndexName(locationProperties.getLocationIndex())
                        .withText(awsLocationSearchDTO.getText())
                        .withMaxResults(awsLocationSearchDTO.getMaxResults());

        SearchPlaceIndexForSuggestionsResult result = amazonLocationClient.searchPlaceIndexForSuggestions(request);

        return JSONUtil.toJsonStr(result);
    }

    private void checkMaxResults(AwsLocationSearchDTO awsLocationSearchDTO) {
        Integer maxResults = awsLocationSearchDTO.getMaxResults();
        if (maxResults > 15) {
            throw new CustomParameterizedException("The maximum number of results must be less than or equal to 15");
        }
    }

    public String getPlace(String placeId, String language) {

        GetPlaceRequest request =
                new GetPlaceRequest()
                        .withIndexName(locationProperties.getLocationIndex())
                        .withPlaceId(placeId);

        GetPlaceResult result = amazonLocationClient.getPlace(request);

        return JSONUtil.toJsonStr(result);
    }


}
