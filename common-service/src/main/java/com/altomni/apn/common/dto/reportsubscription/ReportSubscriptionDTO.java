package com.altomni.apn.common.dto.reportsubscription;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.reportsubscription.ReportSubscription;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushMethod;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportSubscriptionDTO extends ReportSubscription {

    private List<Long> recipientList;

    private List<PushMethod> pushMethodList;

    private List<Long> userIdList;

    private List<Long> teamIdList;

    public String getDataPermission() {
        if (CollUtil.isEmpty(userIdList) && CollUtil.isEmpty(teamIdList)) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("userIdList", userIdList);
        jsonObject.put("teamIdList", teamIdList);
        return JSONUtil.toJsonStr(jsonObject);
    }

}
