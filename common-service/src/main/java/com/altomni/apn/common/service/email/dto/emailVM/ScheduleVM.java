package com.altomni.apn.common.service.email.dto.emailVM;

import com.altomni.apn.common.domain.email.enumeration.CampaignSendType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import javax.validation.constraints.NotNull;
import java.time.ZonedDateTime;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ScheduleVM {

    @NotNull
    private CampaignSendType sendType;

    @JsonDeserialize(using = ZonedDateTimeDeserializer.class)
    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    private ZonedDateTime startDate;

    @JsonDeserialize(using = ZonedDateTimeDeserializer.class)
    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    private ZonedDateTime endDate;

    private String timeZone;

    public ScheduleVM() {}

    public ScheduleVM(CampaignSendType sendType, ZonedDateTime startDate, ZonedDateTime endDate) {
        this.sendType = sendType;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    public CampaignSendType getSendType() {
        return sendType;
    }

    public void setSendType(CampaignSendType sendType) {
        this.sendType = sendType;
    }

    public ZonedDateTime getStartDate() {
        return startDate;
    }

    public void setStartDate(ZonedDateTime startDate) {
        this.startDate = startDate;
    }

    public ZonedDateTime getEndDate() {
        return endDate;
    }

    public void setEndDate(ZonedDateTime endDate) {
        this.endDate = endDate;
    }

    public String getTimeZone() { return timeZone; }

    public void setTimeZone(String timeZone) { this.timeZone = timeZone; }

    //    public ZonedDateTime getStartDate() {
//        return startDate;
//    }
//
//    public void setStartDate(String startDate) {
//        if (StringUtils.isNotBlank(startDate)) {
//            this.startDate = ZonedDateTime.parse(startDate, DateTimeFormatter.ISO_ZONED_DATE_TIME);
//        } else {
//            this.startDate = null;
//        }
//    }
//
//    public ZonedDateTime getEndDate() {
//        return endDate;
//    }
//
//    public void setEndDate(String endDate) {
//        if (StringUtils.isNotBlank(endDate)) {
//            this.endDate = ZonedDateTime.parse(endDate, DateTimeFormatter.ISO_ZONED_DATE_TIME);
//        } else {
//            this.endDate = null;
//        }
//    }


    @Override
    public String toString() {
        return "ScheduleVM{" +
            "sendType=" + sendType +
            ", startDate=" + startDate +
            ", endDate=" + endDate +
            ", timeZone='" + timeZone + '\'' +
            '}';
    }
}
