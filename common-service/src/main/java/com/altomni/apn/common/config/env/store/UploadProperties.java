package com.altomni.apn.common.config.env.store;

import com.altomni.apn.common.service.store.dto.UploadInfo;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "application")
public class UploadProperties {

    private Map<String, UploadInfo> storeService;

}
