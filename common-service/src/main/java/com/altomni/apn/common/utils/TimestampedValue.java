package com.altomni.apn.common.utils;

import lombok.Data;

import java.time.Instant;

@Data
public class TimestampedValue<T> {
    private final T value;            // 实际缓存的值
    private final Instant timestamp;  // 放入缓存的时间戳

    public TimestampedValue(T value, Instant instant) {
        this.value = value;
        this.timestamp = instant;
    }

    public T getValue() {
        return value;
    }

    public Instant getTimestamp() {
        return timestamp;
    }
}
