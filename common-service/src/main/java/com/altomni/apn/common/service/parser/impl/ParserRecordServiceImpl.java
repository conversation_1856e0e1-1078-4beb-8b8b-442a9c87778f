package com.altomni.apn.common.service.parser.impl;

import com.altomni.apn.common.domain.parser.ParseRecord;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.repository.parser.ParseRecordRepository;
import com.altomni.apn.common.service.parser.ParserRecordService;
import com.altomni.apn.common.service.parser.dto.ParseRecordListDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ParserRecordServiceImpl implements ParserRecordService {

    private final Logger log = LoggerFactory.getLogger(ParserRecordServiceImpl.class);

    private final ParseRecordRepository parseRecordRepository;

    public ParserRecordServiceImpl(ParseRecordRepository parseRecordRepository) {
        this.parseRecordRepository = parseRecordRepository;
    }

    @Override
    public ParseRecord save(ParseRecord parseRecord) {
        return parseRecordRepository.save(parseRecord);
    }

    @Override
    public ParseRecord findOne(Long id) {
        return parseRecordRepository.findById(id).orElseThrow(() -> new CustomParameterizedException("Cannot find parse record"));
    }

    @Override
    public List<ParseRecord> findAll() {
        return parseRecordRepository.findAll();
    }

    @Override
    public Page<ParseRecordListDTO> findAllByCreatedByAndIsReviewedOrderByIdDesc(Pageable pageable) {
        Page<ParseRecord> page = parseRecordRepository.findAllByCreatedByAndIsReviewedOrderByIdDesc(SecurityUtils.getUserUid(), Boolean.FALSE, pageable);
        return page.map(this::toDTO);
    }

    private ParseRecordListDTO toDTO(ParseRecord parseRecord) {
        ParseRecordListDTO dto = new ParseRecordListDTO();
        ServiceUtils.myCopyProperties(parseRecord, dto);
        return dto;
    }

    @Override
    public void delete(Long id) {
        parseRecordRepository.deleteById(id);
    }
}
