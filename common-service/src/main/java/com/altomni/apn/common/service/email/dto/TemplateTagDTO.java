package com.altomni.apn.common.service.email.dto;

import java.io.Serializable;

/**
 * A Template Tag DTO.
 */
public class TemplateTagDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    public TemplateTagDTO() {}

    public TemplateTagDTO(Long id) {
        this.id = id;
    }

    // jhipster-needle-entity-add-field - JHip<PERSON> will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) { this.name = name; }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove


    @Override
    public String toString() {
        return "TemplateTagDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                '}';
    }
}
