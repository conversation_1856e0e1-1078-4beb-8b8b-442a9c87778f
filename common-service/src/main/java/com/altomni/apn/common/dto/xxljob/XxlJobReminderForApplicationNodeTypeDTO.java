package com.altomni.apn.common.dto.xxljob;

import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnumConverter;
import lombok.Data;

import javax.persistence.Convert;
import java.time.Instant;

@Data
public class XxlJobReminderForApplicationNodeTypeDTO extends XxlJobBaseDTO{

    private Long talentRecruitmentProcessId;

    private Long talentId;

    private Long jobId;

    private String content;

    private String status;

    private Instant sendTime;

    @Convert(converter = MessageTypeEnumConverter.class)
    private MessageTypeEnum type;

    private String reminderConfig;

}
