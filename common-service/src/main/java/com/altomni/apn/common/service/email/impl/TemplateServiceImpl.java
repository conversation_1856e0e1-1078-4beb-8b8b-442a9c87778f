package com.altomni.apn.common.service.email.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.config.env.email.EmailAppProperties;
import com.altomni.apn.common.domain.email.enumeration.EmailTemplateType;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.email.EmailConstans;
import com.altomni.apn.common.service.email.TemplateService;
import com.altomni.apn.common.service.email.dto.EmailyTemplateDTO;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.ipg.resourceserver.client.ClientTokenHolder;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Service
public class TemplateServiceImpl implements TemplateService {

    private final Logger log = LoggerFactory.getLogger(TemplateServiceImpl.class);

    @Resource
    private HttpService httpService;

    @Resource
    private EmailAppProperties emailAppProperties;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

//    private final AsyncRecordRepository asyncRecordRepository;

    private final static String HEADERS_AUTHORIZATION_KEY = "Authorization";

    private final static String URL_SEPARATOR = "/";

    private final static String SPACE = " ";

    private final static String URL_SEARCH_PAGE = "page=";

    private final static String URL_SEARCH_SIZE = "size=";

    private final static String URL_SEARCH_SORT = "sort=";

    private final static String URL_SEARCH_NAME = "name=";

    private final static String URL_SEARCH_CREATOR = "creatorUserIds=";

    private final static String URL_SEARCH_QUESTION_MARK = "?";

    private final static String URL_SEARCH_AND = "&";

    private final static String EMAILY_TEMPLATE_URL_V1 = "/api/v1/template";

    private final static String URL_SAVED= "/saved";

    private final static String URL_BUILT_IN= "/built-in";

    private final static String URL_TAGS = "/tags";

    private final static String URL_MY = "/my";

    private final static String URL_TENANT = "/tenant";

    private final static String URL_DEACTIVATE = "/deactivate";

    private final static String URL_NAME = "/name";


    private String baseUrl() { return emailAppProperties.getEmailyBaseUrl(); }

    private String getTemplateByIdFromEmailyUrl(Long templateId) {
        return baseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_SEPARATOR + templateId;
    }

    private String searchTemplatesFromEmailyUrl(EmailTemplateType type, Long tagsId, String name, List<Long> creatorUserIds, Pageable pageable, String sort) {
        String creatorUserIdsQueryStr = "";
        if (!CollectionUtils.isEmpty(creatorUserIds)) {
            creatorUserIdsQueryStr = URL_SEARCH_AND + URL_SEARCH_CREATOR + StringUtils.join(creatorUserIds, ",");
        }
        if (ObjectUtil.isEmpty(tagsId)) {
            return baseUrl() + EMAILY_TEMPLATE_URL_V1 + (type == EmailTemplateType.USER_SAVED ? URL_SAVED : URL_BUILT_IN) + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber()
                + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH_SORT + sort + (ObjectUtil.isNotEmpty(name) ? (URL_SEARCH_AND + URL_SEARCH_NAME + name) : "") + creatorUserIdsQueryStr;
        } else {
            return baseUrl() + EMAILY_TEMPLATE_URL_V1 + (type == EmailTemplateType.USER_SAVED ? URL_SAVED : URL_BUILT_IN) + URL_SEPARATOR + tagsId + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE +
                pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH_SORT + sort + (ObjectUtil.isNotEmpty(name) ? (URL_SEARCH_AND + URL_SEARCH_NAME + name) : "") + creatorUserIdsQueryStr;
        }
    }

    private String getMyTemplatesUrl(String name, Pageable pageable, String sort) {
        return baseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_SAVED + URL_MY + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE +
                pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH_SORT + sort + (ObjectUtil.isNotEmpty(name) ? (URL_SEARCH_AND + URL_SEARCH_NAME + name) : "");
    }

    private String getAllTemplatesByTagsUrl(String name, Pageable pageable, String sort) {
        return baseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_SAVED + URL_TAGS + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE +
                pageable.getPageNumber() + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH_SORT + sort + (ObjectUtil.isNotEmpty(name) ? (URL_SEARCH_AND + URL_SEARCH_NAME + name) : "");

    }

    private String getAllTemplateTagsByTenantIdFromEmailyUrl() {
        return baseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_TAGS + URL_TENANT;
    }

    private String syncTemplateToEmailyCreateUrl() {
        return baseUrl() + EMAILY_TEMPLATE_URL_V1;
    }

    private String syncTemplateToEmailyUpdateUrl(Long id) {
        return baseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_SEPARATOR + id;
    }

    private String syncTemplateToEmailyDeactivateUrl() {
        return baseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_DEACTIVATE;
    }

    private String syncTemplateToEmailyDeleteUrl(Long templateId) {
        return baseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_SEPARATOR + templateId;
    }

    private String searchTemplateByNameFromEmailyUrl(String name) {
        return baseUrl() + EMAILY_TEMPLATE_URL_V1 + URL_SAVED + URL_NAME + URL_SEARCH_QUESTION_MARK + URL_SEARCH_NAME + name;
    }

    @Override
    public HttpResponse getTemplateById(Long templateId) throws IOException {
        if (ObjectUtil.isEmpty(templateId)) {
            log.error("[EmailService: TemplateServiceImpl] query a email template from Emaily error, parameter error, the emaily template id cannot be empty. templateId: {}", templateId);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(getTemplateByIdFromEmailyUrl(templateId), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] query a email template from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: TemplateServiceImpl @{}] query a email template from Emaily error and response is null, templateId: {}", SecurityUtils.getUserId(), templateId);
        }
        return response;
    }

    @Override
    public HttpResponse searchEmailTemplates(EmailTemplateType type, Long tagsId, String name, List<Long> creatorUserIds, Pageable pageable, String sort) throws IOException {

//        System.out.println("url:");
//        System.out.println(searchTemplatesFromEmailyUrl(type, tagsId, name, creatorUserIds, pageable, sort));

        HttpResponse response = httpService.get(searchTemplatesFromEmailyUrl(type, tagsId, name, creatorUserIds, pageable, sort), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] search email templates from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
         } else {
            log.error("[EmailService: TemplateServiceImpl @{}] search email templates from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse searchMyTemplates(String name, Pageable pageable) throws IOException {

        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        String sort = "";
        if (!Objects.isNull(order)) {
            sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
        }

        HttpResponse response = httpService.get(getMyTemplatesUrl(name, pageable, sort), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] search my templates from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: TemplateServiceImpl @{}] search my templates from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse searchApplicationTemplates(Long tagId, String name, Pageable pageable) throws IOException {
        List<Long> tagIds = new ArrayList<>();

        if (Objects.isNull(tagId)) {
            HttpResponse tagsResponse = this.getAllEmailTemplateTags();
            if (StringUtils.isNotBlank(tagsResponse.getBody())) {
                JSONArray tagArray = JSONArray.parseArray(tagsResponse.getBody());
                for (int i = 0; i < tagArray.size(); i++) {
                    JSONObject tag = tagArray.getJSONObject(i);
                    tagIds.add(tag.getLongValue("id"));
                }
            }
        } else {
            tagIds.add(tagId);
        }

        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        String sort = "";
        if (!Objects.isNull(order)) {
            sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
//            builder.addQueryParameter(StringUtils.toRootLowerCase(order.getDirection().toString()), order.getProperty());
        }

        String campaignParamStr = JSON.toJSONString(tagIds, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(getAllTemplatesByTagsUrl(name, pageable, sort), getRequestHeaders(), campaignParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] search email templates from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: TemplateServiceImpl @{}] search email templates from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse getAllEmailTemplateTags() throws IOException {
        HttpResponse response = httpService.get(getAllTemplateTagsByTenantIdFromEmailyUrl(), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] search email template tags from Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: TemplateServiceImpl @{}] search email template tags from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse createEmailTemplate(EmailyTemplateDTO templateDTO) throws IOException {
        if (ObjectUtil.isEmpty(templateDTO.getName()) || ObjectUtil.isEmpty(templateDTO.getHtmlContent())) {
            log.error("[EmailService: TemplateServiceImpl] create a email template to Emaily error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(templateDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncTemplateToEmailyCreateUrl(), getRequestHeaders(), campaignParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] create a email template to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: TemplateServiceImpl @{}] create a email template to Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse updateEmailTemplate(Long templateId, EmailyTemplateDTO templateDTO) throws IOException {
        if ((ObjectUtil.isEmpty(templateDTO.getName()) && ObjectUtil.isEmpty(templateDTO.getHtmlContent())) || ObjectUtil.isEmpty(templateId)) {
            log.error("[EmailService: TemplateServiceImpl] update a email template to Emaily error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(templateDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(syncTemplateToEmailyUpdateUrl(templateId), getRequestHeaders(), campaignParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] update a email template to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: TemplateServiceImpl @{}] update a email template to Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse deactivateTemplateByIds(List<Long> templateIds) throws IOException {
        if (CollectionUtils.isEmpty(templateIds)) {
            log.error("[EmailService: TemplateServiceImpl] deactivate email templates to Emaily error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(templateIds, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncTemplateToEmailyDeactivateUrl(), getRequestHeaders(), campaignParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] deactivate email templates to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: TemplateServiceImpl @{}] deactivate email templates to Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse deleteTemplateById(Long templateId) throws IOException {
        if (Objects.isNull(templateId)) {
            log.error("[EmailService: TemplateServiceImpl] deactivate email templates to Emaily error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.delete(syncTemplateToEmailyDeleteUrl(templateId), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] deactivate email templates to Emaily error, response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: TemplateServiceImpl @{}] deactivate email templates to Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse searchEmailTemplateByName(String name) throws IOException {
        if (ObjectUtil.isEmpty(name)) {
            log.error("[EmailService: TemplateServiceImpl] query a email template by name from Emaily error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        String campaignParamStr = JSON.toJSONString(name, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.get(searchTemplateByNameFromEmailyUrl(name), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: TemplateServiceImpl] query a email template by name from Emaily error response code: {}, response message: {}", response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } else {
            log.error("[EmailService: TemplateServiceImpl @{}] query a email template by name from Emaily error and response is null.", SecurityUtils.getUserId());
        }
        return response;
    }

    public Headers getRequestHeaders() {
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, TOKEN_TYPE + SPACE + currentUserToken);
        headersBuilder.put(EmailConstans.HEADER_CLIENT_ID, ClientTokenHolder.getInstance().getClientId());
        if (SecurityUtils.getUserUid() != null) {
            headersBuilder.put(EmailConstans.HEADER_UID, SecurityUtils.getUserUid());
        }
//        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7N5q2Y0qIuLHYwOxaoYCq-F54SDHMSO4kvUx_Goxft8");
        return Headers.of(headersBuilder);
    }

}
