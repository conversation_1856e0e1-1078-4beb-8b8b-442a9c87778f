package com.altomni.apn.common.service.email.dto;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.email.EmailCampaign;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignArchiveStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignAuditStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignStatus;
import com.altomni.apn.common.utils.ServiceUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel
public class EmailCampaignDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The name of this email campaign.")
    private String name;

    @ApiModelProperty(value = "The creatorName of this email campaign.")
    private String creatorName;

    @ApiModelProperty(value = "The status of this email campaign.")
    private EmailCampaignStatus status;

    @ApiModelProperty(value = "The archiveStatus of this email campaign.")
    private EmailCampaignArchiveStatus archiveStatus;

    @ApiModelProperty(value = "JobId associated with this email campaign.")
    private List<Long> jobIdList;

    @ApiModelProperty(value = "jobInformation associated with this email campaign.")
    private String jobInformation;

    @ApiModelProperty(value = "The emailSubject of this email campaign.")
    private String emailSubject;

    @ApiModelProperty(value = "The senderEmail of this email campaign.")
    private String senderEmail;

    @ApiModelProperty(value = "The senderName of this email campaign.")
    private String senderName;

    @ApiModelProperty(value = "The defaultReplyEmail of this email campaign.")
    private String defaultReplyEmail;

    @ApiModelProperty(value = "The auditStatus of this email campaign.")
    private EmailCampaignAuditStatus auditStatus;

    @ApiModelProperty(value = "The groupId of this email campaign.")
    private EmailyCampaignAudienceInfo audienceInfo;

    @ApiModelProperty(value = "The templateId of this email campaign.")
    private EmailyTemplateDTO template;

    @ApiModelProperty(value = "Reason for this email campaign not approved.")
    private String reason;

    @ApiModelProperty(value = "The estimatedCost of this email campaign.")
    private int estimatedCost;

    @ApiModelProperty(value = "The id of this emaily campaign.")
    private Long emailyCampaignId;

    @ApiModelProperty(value = "The companyName of this email campaign.")
    private String companyName;

    @ApiModelProperty(value = "The estimatedCost of this email campaign.")
    private int actualCost;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCreatorName() {
        return creatorName;
    }

    public void setCreatorName(String creatorName) {
        this.creatorName = creatorName;
    }

    public EmailCampaignStatus getStatus() {
        return status;
    }

    public void setStatus(EmailCampaignStatus status) {
        this.status = status;
    }


    public String getJobInformation() {
        return jobInformation;
    }

    public void setJobInformation(String jobInformation) {
        this.jobInformation = jobInformation;
    }

    public String getEmailSubject() {
        return emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }

    public String getSenderEmail() {
        return senderEmail;
    }

    public void setSenderEmail(String senderEmail) {
        this.senderEmail = senderEmail;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getDefaultReplyEmail() {
        return defaultReplyEmail;
    }

    public void setDefaultReplyEmail(String defaultReplyEmail) {
        this.defaultReplyEmail = defaultReplyEmail;
    }

    public EmailCampaignAuditStatus getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(EmailCampaignAuditStatus auditStatus) {
        this.auditStatus = auditStatus;
    }

    public EmailyCampaignAudienceInfo getAudienceInfo() {
        return audienceInfo;
    }

    public void setAudienceInfo(EmailyCampaignAudienceInfo audienceInfo) {
        this.audienceInfo = audienceInfo;
    }

    public EmailyTemplateDTO getTemplate() {
        return template;
    }

    public void setTemplate(EmailyTemplateDTO template) {
        this.template = template;
    }

    public List<Long> getJobIdList() {
        return jobIdList;
    }

    public void setJobIdList(List<Long> jobIdList) {
        this.jobIdList = jobIdList;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public int getEstimatedCost() {
        return estimatedCost;
    }

    public void setEstimatedCost(int estimatedCost) {
        this.estimatedCost = estimatedCost;
    }

    public Long getEmailyCampaignId() {
        return emailyCampaignId;
    }

    public void setEmailyCampaignId(Long emailyCampaignId) {
        this.emailyCampaignId = emailyCampaignId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public int getActualCost() {
        return actualCost;
    }

    public void setActualCost(int actualCost) {
        this.actualCost = actualCost;
    }

    public static EmailCampaignDTO fromEmailCampaign(EmailCampaign emailCampaign) {
        EmailCampaignDTO createEmailCampaignDTO = new EmailCampaignDTO();
        ServiceUtils.myCopyProperties(emailCampaign, createEmailCampaignDTO);
        return createEmailCampaignDTO;
    }

    public static EmailCampaignDTO fromEmailyAssociatedCampaign(EmailyAssociatedCampaign emailyAssociatedCampaign) {
        EmailCampaignDTO createEmailCampaignDTO = new EmailCampaignDTO();
        ServiceUtils.myCopyProperties(emailyAssociatedCampaign, createEmailCampaignDTO);
        return createEmailCampaignDTO;
    }
}
