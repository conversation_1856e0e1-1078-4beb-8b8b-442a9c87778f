package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EmailTemplateStatus implements ConvertedEnum<Integer> {

    NOT_STARTED(0),
    PASSED(1),
    FAILLED(9);

    private final int dbValue;

    EmailTemplateStatus(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<EmailTemplateStatus, Integer> resolver = new ReverseEnumResolver<>(EmailTemplateStatus.class, EmailTemplateStatus::toDbValue);

    public static EmailTemplateStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
