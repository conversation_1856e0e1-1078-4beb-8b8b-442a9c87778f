package com.altomni.apn.common.domain.upgrade;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

@ApiModel(description = "Upgrade Notification")
@Data
@Entity
@Table(name = "upgrade_notification")
public class UpgradeNotification extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "link_cn", length = 120)
    private String linkCn;

    @Column(name = "link_en", length = 120)
    private String linkEn;

    @Column(name = "content_cn", columnDefinition = "TEXT")
    private String contentCn;

    @Column(name = "content_en", columnDefinition = "TEXT")
    private String contentEn;

    @Column(name = "version", length = 30)
    private String version;

    @Column(name = "upgrade_time")
    private Instant upgradeTime;

    @Column(name = "global")
    private Boolean global;

}
