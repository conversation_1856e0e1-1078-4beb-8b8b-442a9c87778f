package com.altomni.apn.common.service.job;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@Component
@FeignClient(value = "job-service")
public interface JobService {

    @PostMapping("/job/api/v3/jobs/format-jd")
    ResponseEntity<String> formatJD(@RequestBody String data);

    @GetMapping("/job/api/v3/jobs/job-title/{jobId}")
    ResponseEntity<String> getJobTitleByJobId(@PathVariable("jobId") Long jobId);

}
