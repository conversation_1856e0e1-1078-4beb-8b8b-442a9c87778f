package com.altomni.apn.common.service.linkedin.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.linkedin.LinkedinUsageDTO;
import com.altomni.apn.common.dto.linkedin.LinkedinUsageSearchDTO;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.user.UserTeamPariDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.service.linkedin.LinkedinUsageService;
import com.altomni.apn.common.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LinkedinUsageServiceImpl implements LinkedinUsageService {

    private static final Integer TEAM_DATA_SCOPE = 2;

    private static final Integer ALL_DATA_SCOPE = 99;

    @Resource
    private HttpService httpService;

    @Resource
    private UserService userService;

    @Resource
    private InitiationService initiationService;

    @Value("${application.linkedin-usage.summary-url}")
    private String linkedinUsageSummarySearchUrl;

    @Override
    public LinkedinUsageDTO searchLinkedinUsageReport(LinkedinUsageSearchDTO linkedinUsageSearchDTO, List<Long> teamIds, List<Long> userIds, Long tenantId, Long userId) {
        try {
            Set<Long> userIdSet = getReallyUserIds(tenantId, userId, teamIds, userIds);
            linkedinUsageSearchDTO.setUserIds(userIdSet);
            String bodyJson = JSONUtil.toJsonStr(linkedinUsageSearchDTO);
            HttpResponse response = httpService.post(linkedinUsageSummarySearchUrl, bodyJson);
            if (Objects.nonNull(response) && response.getCode() == 200) {
                LinkedinUsageDTO linkedinUsageDTO = JSONUtil.toBean(response.getBody(), LinkedinUsageDTO.class);
                return linkedinUsageDTO;
            } else {
                log.error("[LinkedinUsageService: searchLinkedinUsageReport] Error while searching linkedin-usage report, with searchDTO: {}, and response code: {}, response body: {}", linkedinUsageSearchDTO, response.getCode(), response.getBody());
                throw new CustomParameterizedException("search linkedin usage summary error");
            }
        } catch (IOException e) {
            log.error("[LinkedinUsageService: searchLinkedinUsageReport] IOException when search linkedin usage summary with searchDTO: {}", linkedinUsageSearchDTO, e);
            throw new CustomParameterizedException("search linkedin usage summary IOException");
        }
    }

    public Set<Long> getReallyUserIds(Long tenantId, Long userId, List<Long> teamIds, List<Long> userIds) {
        List<UserBriefDTO> users = userService.getAllBriefUsersByTenantId(tenantId).getBody();
        Map<Long, UserBriefDTO> userMap = users.stream().filter(UserBriefDTO::isActivated).collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));
        //取过滤条件中的userIds的集合
        Set<Long> userIdListForFilter = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userIds)){
            userIdListForFilter.addAll(userIds);
        }
        if(CollectionUtils.isNotEmpty(teamIds)){
            Set<Long> teamIdSet = new HashSet<>(teamIds);
            Set<Long> teamUserIds = userService.getAllActiveTeamUserIdsByPermissionTeamIdIn(teamIdSet).getBody();
            if(CollectionUtils.isNotEmpty(teamUserIds)){
                userIdListForFilter.addAll(teamUserIds);
            }
        }
        //所有活跃用户
        Set<Long> allUserIds = userMap.keySet();
        Set<Long> getReallyUserIds;
        if(CollectionUtils.isEmpty(userIdListForFilter)){
            getReallyUserIds = allUserIds;
        }else {
            getReallyUserIds = userIdListForFilter;
        }
        Set<Long> userIdsAfterDataPermission = checkReportDataPermissionV3(getReallyUserIds, tenantId, userId);
        Set<Long> ids = userIdsAfterDataPermission;
        if (CollUtil.isEmpty(ids)){
            return Set.of();
        }
        List<Long> idFilterByTeamCategory = userService.getUserIdFilterByTeamCategory(ids.stream().toList()).getBody();
        if (CollUtil.isEmpty(idFilterByTeamCategory)){
            return Set.of();
        }
        return new HashSet<>(idFilterByTeamCategory);
    }

    private Set<Long> checkReportDataPermissionV3(Set<Long> allUserIds, Long tenantId, Long userId) {
        Set<Long> result = new HashSet<>();
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(tenantId, userId).getBody();
        if(teamDataPermission.getSelf()){
            if(allUserIds.contains(userId)){
                result.add(userId);
            }else {
                return Collections.emptySet();
            }
        }else if(CollectionUtils.isNotEmpty(teamDataPermission.getNestedTeamIds())){
            Set<Long> nestedTeamIds = teamDataPermission.getNestedTeamIds();
            List<UserTeamPariDTO> userTeamPair = userService.getUserTeamPairsByUserIds(allUserIds).getBody();
            if(CollectionUtils.isNotEmpty(userTeamPair)){
                userTeamPair.stream().filter(p -> nestedTeamIds.contains(p.getTeamId())).forEach(p -> result.add(p.getUserId()));
            }else {
                return Collections.emptySet();
            }

        }else if(teamDataPermission.getAll()){
            result.addAll(allUserIds);
        }
        return result;
    }

}
