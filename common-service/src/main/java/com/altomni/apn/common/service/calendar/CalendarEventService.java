package com.altomni.apn.common.service.calendar;

import cn.hutool.json.JSONArray;
import com.altomni.apn.common.domain.calendar.CalendarEvent;
import com.altomni.apn.common.domain.enumeration.calendar.*;
import com.altomni.apn.common.dto.calendar.*;
import com.altomni.apn.common.vo.calendar.CalendarEventSearchVO;
import com.altomni.apn.common.vo.calendar.CalendarEventVO;
import com.altomni.apn.common.vo.calendar.CalendarRecentDataVO;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface CalendarEventService {
    void createCalendarEvent(CalendarEventDTO calendarEventDto);

    void updateCalendarEvent(CalendarEventDTO calendarEventDto);

    void deleteCalendarEvent(List<Long> idList);

    void updateCalendarEventReminder(Long eventId, CalendarEventAttendeeReminderTypeEnum isReminder);

    void updateCalendarEventStatus(Long eventId,Long userId, CalendarStatusEnum status);

    void systemCalendarEventComplete(CompleteSystemCalendarDTO dto);

    void updateAttendeesOverdueStatus();

    CalendarEventSearchVO searchCalendarEventList(CalendarEventSearchDTO dto,
                                                  Pageable pageable);

    CalendarEventVO getCalendarEventById(Long eventId);

    List<CalendarRecentDataVO> getRecentDateByType(CalendarRecentDataTypeEnum type);

    CalendarEventVO getCalendarEventByTypeIdAndReferenceId(Integer typeId, Long referenceId);

    void syncOrCancelSyncCalendarEventToLark(Integer syncLark);

    List<CalendarEventVO> checkDuplicateCalendarEvent(CalendarEventDTO calendarEventDto);

    JSONArray deleteCalendarEventByRelationIds(List<Long> recruitmentProcessIds);

    PushRuleCheckVO pushRuleCheck();

    SystemCalendarStatisticDTO systemCalendarStatistic(Boolean type);

    List<SystemCalendarStatisticItem> getSystemCalendarStatisticByType(SystemCalendarStatisticByTypeDTO byTypeDTO);

    void batchSystemCalendar(BatchSystemCalendarDTO dto);

    void updateAttendees(List<CalendarEvent> existEvent, List<CalendarEventAttendeeDTO> attendees);
}
