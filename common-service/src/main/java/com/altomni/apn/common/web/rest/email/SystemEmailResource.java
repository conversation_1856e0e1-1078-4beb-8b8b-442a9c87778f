package com.altomni.apn.common.web.rest.email;

import com.altomni.apn.common.dto.email.UserResetDTO;
import com.altomni.apn.common.service.email.SystemEmailService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-EmailService"})
@RestController
@RequestMapping("/api/v3/system")
public class SystemEmailResource {

    private final Logger log = LoggerFactory.getLogger(SystemEmailResource.class);

    private static final String ENTITY_NAME = "systemEmailService";

    @Resource
    private SystemEmailService systemEmailService;

    @PostMapping(value = "/reset-pwd-init", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> sendInitPasswordEmail(@RequestBody @Valid UserResetDTO userResetDTO) throws IOException {
        log.info("[SystemEmailResource: sendInitPasswordEmail @{}] REST request to send init password email, with userResetDTO: {}", SecurityUtils.getUserId(), userResetDTO);
        systemEmailService.sendInitPasswordEmail(userResetDTO);
        return ResponseEntity.ok().build();
    }

    @PostMapping(value = "/reset-pwd", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> sendResetPasswordEmail(@RequestBody @Valid UserResetDTO userResetDTO) throws IOException {
        log.info("[SystemEmailResource: sendResetPasswordEmail @{}] REST request to send reset password email, with userResetDTO: {}", SecurityUtils.getUserId(), userResetDTO);

        systemEmailService.sendResetPasswordEmail(userResetDTO);
        return ResponseEntity.ok().build();
    }

}
