package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.vo.user.UserActiveDurationStatistic;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SystemCalendarStatisticCacheDTO {
    private ApplicationFollowStatistic applicationFollow;
    private ClientContactFollowStatistic clientContactFollow;
    private JobFollowStatistic jobFollow;
    private UserActiveDurationStatistic userActiveDuration;
}
