package com.altomni.apn.common.service.lark;

import cn.hutool.json.JSONException;

import java.io.IOException;
import java.util.List;

public interface LarkMessageNotification {

    String getToken() throws IOException, JSONException;

    List<String> obtainUserIdsByEmail(List<String> emails, String token);

    void sendLarkMessage(String content, String email, String token);

    void batchSendLarkMessage(String content, List<String> userIds, String token);


}
