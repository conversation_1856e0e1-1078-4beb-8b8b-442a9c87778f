package com.altomni.apn.common.web.rest.calendar;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarEventAttendeeReminderTypeEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarRecentDataTypeEnum;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum;
import com.altomni.apn.common.dto.calendar.*;
import com.altomni.apn.common.service.calendar.CalendarEventService;
import com.altomni.apn.common.service.lark.LarkClient;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.calendar.CalendarEventSearchVO;
import com.altomni.apn.common.vo.calendar.CalendarEventVO;
import com.altomni.apn.common.vo.calendar.CalendarRecentDataVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class CalendarResource {

    @Resource
    private CalendarEventService calendarEventService;

    @Resource
    private LarkClient larkClient;

    @PostMapping("/calendar-event")
    public ResponseEntity<Void> createCalendarEvent(@Valid @RequestBody CalendarEventDTO calendarEventDto) {
        log.info("[APN: Calendar @{}] REST request to create calendar event : data = {}", SecurityUtils.getUserId(), calendarEventDto);
        calendarEventService.createCalendarEvent(calendarEventDto);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/calendar-event/check-duplicate")
    public ResponseEntity<List<CalendarEventVO>> checkDuplicateCalendarEvent(@Valid @RequestBody CalendarEventDTO calendarEventDto) {
        log.info("[APN: Calendar @{}] REST request to check duplicate calendar event : data = {}", SecurityUtils.getUserId(), calendarEventDto);
        return ResponseEntity.ok(calendarEventService.checkDuplicateCalendarEvent(calendarEventDto));
    }

    @PutMapping("/calendar-event")
    public ResponseEntity<Void> updateCalendarEvent(@Valid @RequestBody CalendarEventDTO calendarEventDto) {
        log.info("[APN: Calendar @{}] REST request to update calendar event : data = {}", SecurityUtils.getUserId(), calendarEventDto);
        calendarEventService.updateCalendarEvent(calendarEventDto);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/calendar-event/delete")
    public ResponseEntity<Void> deleteCalendarEvent(@RequestBody List<Long> idList) {
        log.info("[APN: Calendar @{}] REST request to delete calendar event : idList = {}", SecurityUtils.getUserId(), idList);
        calendarEventService.deleteCalendarEvent(idList);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/calendar-event/{eventId}/{isReminder}")
    public ResponseEntity<Void> updateCalendarEventReminder(@PathVariable("eventId") Long eventId, @PathVariable("isReminder") CalendarEventAttendeeReminderTypeEnum isReminder) {
        log.info("[APN: Calendar @{}] REST request to update calendar event attendee is reminder: eventId = {}, isReminder = {}", SecurityUtils.getUserId(), eventId, isReminder);
        calendarEventService.updateCalendarEventReminder(eventId, isReminder);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/calendar-event/{eventId}/{userId}/{status}")
    public ResponseEntity<Void> updateCalendarEventStatus(@PathVariable("eventId") Long eventId, @PathVariable("userId") Long userId, @PathVariable("status") CalendarStatusEnum status) {
        log.info("[APN: Calendar @{}] REST request to update calendar event attendee is status: eventId = {},userId = {}, status = {}", SecurityUtils.getUserId(), eventId, userId, status);
        calendarEventService.updateCalendarEventStatus(eventId, userId, status);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/calendar-event/list")
    public ResponseEntity<CalendarEventSearchVO> searchCalendarEventList(@RequestBody CalendarEventSearchDTO dto,
                                                                         @PageableDefault Pageable pageable) {
        log.info("[APN: Calendar @{}] REST request to search calendar event list: param: {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        return ResponseEntity.ok(calendarEventService.searchCalendarEventList(dto, pageable));
    }

    @GetMapping("/calendar-event/{id}")
    public ResponseEntity<CalendarEventVO> getCalendarEventById(@PathVariable("id") Long id) {
        log.info("[APN: Calendar @{}] REST request to get calendar event by id: {}", SecurityUtils.getUserId(), id);
        return ResponseEntity.ok(calendarEventService.getCalendarEventById(id));
    }

    @GetMapping("/calendar-event/top-ten/{type}")
    public ResponseEntity<List<CalendarRecentDataVO>> getRecentDateByType(@PathVariable("type") CalendarRecentDataTypeEnum type) {
        log.info("[APN: Calendar @{}] REST request to get calendar event recent date by type: {}", SecurityUtils.getUserId(), type);
        return ResponseEntity.ok(calendarEventService.getRecentDateByType(type));
    }

    /**
     * 每15分钟跑定时任务跟新状态
     * @return
     */
    @PostMapping("/calendar-event/update-attendees-overdue-status")
    public ResponseEntity<Void> updateAttendeesOverdueStatus() {
        log.info("[APN: Calendar @{}] REST request to update calendar event attendee is status overdue", SecurityUtils.getUserId());
        calendarEventService.updateAttendeesOverdueStatus();
        return ResponseEntity.ok().build();
    }

    @GetMapping("/calendar-event/{typeId}/{referenceId}")
    public ResponseEntity<CalendarEventVO> getCalendarEventByTypeIdAndReferenceId(@PathVariable("typeId") Integer typeId, @PathVariable("referenceId") Long referenceId) {
        log.info("[APN: Calendar @{}] REST request to get calendar event by typeId: {}, referenceId: {}", SecurityUtils.getUserId(), typeId, referenceId);
        return ResponseEntity.ok(calendarEventService.getCalendarEventByTypeIdAndReferenceId(typeId, referenceId));
    }

    @GetMapping("/sync-or-cancel-calendar-event-to-lark/{syncLark}")
    public ResponseEntity<Void> syncOrCancelSyncCalendarEventToLark(@PathVariable("syncLark") Integer syncLark) {
        log.info("[APN: Calendar @{}] REST request to sync or cancel calendar event to syncLark: {}", SecurityUtils.getUserId(), syncLark);
        calendarEventService.syncOrCancelSyncCalendarEventToLark(syncLark);
        return ResponseEntity.ok().build();
    }

    /**
     * 禁猎公司推荐候选人到职位时给责任人发送Lark消息(通过webhook)
     * @param remindDTO
     * @return
     */
    @PostMapping("/lark-client/send-message-for-no-nopoaching")
    public ResponseEntity<Void> noPoachingSendMessageByUserEmails(@RequestBody NoPoachingRemindDTO remindDTO) {
        larkClient.noPoachingSendMessageByUserEmails(remindDTO);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/calendar-event/relationIds")
    public ResponseEntity<JSONArray> deleteCalendarEventByRecruitmentProcessIds(@RequestBody List<Long> relationIds) {
        log.info("REST request to delete calendar event by recruitmentProcessIds: {}", relationIds);
        JSONArray result = calendarEventService.deleteCalendarEventByRelationIds(relationIds);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/push-rule/check")
    public ResponseEntity<PushRuleCheckVO> pushRuleCheck() {
        log.info("[APN: Calendar @{}] REST request to check push rule.", SecurityUtils.getUserId());
        return ResponseEntity.ok(calendarEventService.pushRuleCheck());
    }

    @GetMapping("/system-calendar-statistic")
    public ResponseEntity<SystemCalendarStatisticDTO> systemCalendarStatistic(@RequestParam(value = "isTeam", defaultValue = "false") Boolean isTeam) {
        log.info("[APN: Calendar @{}] REST request to get system calendar statistic: {}", SecurityUtils.getUserId(), isTeam);
        return ResponseEntity.ok(calendarEventService.systemCalendarStatistic(isTeam));
    }

    @PostMapping("/system-calendar-statistic/type")
    public ResponseEntity<List<SystemCalendarStatisticItem>> getSystemCalendarStatisticByType(@RequestBody SystemCalendarStatisticByTypeDTO  byTypeDTO) {
        log.info("[APN: Calendar @{}] REST request to get system calendar statistic by type: {}", SecurityUtils.getUserId(), byTypeDTO);
        return ResponseEntity.ok(calendarEventService.getSystemCalendarStatisticByType(byTypeDTO));
    }

    @PostMapping("/system-calendar/complete")
    public ResponseEntity<Void> completeSystemCalendar(@RequestBody CompleteSystemCalendarDTO dto) {
        log.info("[APN: Calendar @{}] REST request to system calendar complete by dto: {}", SecurityUtils.getUserId(), dto);
        calendarEventService.systemCalendarEventComplete(dto);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/system-calendar/batch-add")
    public ResponseEntity<Void> batchSystemCalendar(@RequestBody BatchSystemCalendarDTO dto) {
        log.info("[APN: Calendar @{}] REST request to batch add system calendar by dto: {}", SecurityUtils.getUserId(), dto);
        calendarEventService.batchSystemCalendar(dto);
        return ResponseEntity.ok().build();
    }
}
