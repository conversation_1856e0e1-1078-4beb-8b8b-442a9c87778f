package com.altomni.apn.common.domain.message;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnumConverter;
import lombok.Data;

import javax.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "message")
@Data
public class Message extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "type")
    @Convert(converter = MessageTypeEnumConverter.class)
    private MessageTypeEnum type;

    @Column(name = "cn_title")
    private String cnTitle;

    @Column(name = "en_title")
    private String enTitle;

    @Column(name = "content")
    private String content;

    @Column(name = "send_time")
    private LocalDateTime sendTime;

}
