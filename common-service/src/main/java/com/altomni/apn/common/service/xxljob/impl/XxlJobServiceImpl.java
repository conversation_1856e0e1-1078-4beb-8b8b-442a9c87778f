package com.altomni.apn.common.service.xxljob.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.reportsubscription.ReportSubscriptionConfigDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateBySendTimeForJobAdminDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTenantMessageConfigDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTimezoneDTO;
import com.altomni.apn.common.repository.xxljob.XxlJobRelationRepository;
import com.altomni.apn.common.service.xxljob.XxlJobClient;
import com.altomni.apn.common.service.xxljob.XxlJobService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.TenantMessageConfigConstants.REMINDER_TIME_FORMAT;

@Slf4j
@Service("xxlJobService")
public class XxlJobServiceImpl implements XxlJobService {

    @Resource
    private XxlJobClient xxlJobClient;

    @Resource
    private XxlJobRelationRepository xxlJobRelationRepository;


    @Override
    public Integer createXxlJob(XxlJobApnDTO xxlJobApnDTO) {
        log.info("[apn @user {}] create xxl job", SecurityUtils.getUserId());
        XxlJobRelation xxlJobRelation = xxlJobRelationRepository.save(getXxlJobRelationEntity(xxlJobApnDTO, null));
        Integer xxlJobId = xxlJobClient.addExecutorTask(xxlJobApnDTO, 0);
        if (xxlJobId == null) {
            return 0;
        }
        xxlJobRelation.setXxlJobId(xxlJobId);
        setXxlJobIdIntoParam(xxlJobRelation);
        xxlJobRelationRepository.save(xxlJobRelation);
        return xxlJobId;
    }

    @Override
    public void createXxlJobs(List<XxlJobApnDTO> xxlJobApnDTOList) {
        log.info("[apn @user {}] create xxl jobs", SecurityUtils.getUserId());
        List<XxlJobRelation> xxlJobRelationList = new ArrayList<>();
        xxlJobApnDTOList.forEach(xxlJobApnDTO -> xxlJobRelationList.add(getXxlJobRelationEntity(xxlJobApnDTO, null)));
        xxlJobRelationRepository.saveAll(xxlJobRelationList);
        Map<String, Integer> onlyXxlJobIdMap = xxlJobClient.addJobS(xxlJobApnDTOList, 0);
        if (CollUtil.isEmpty(onlyXxlJobIdMap)) {
            return;
        }
        log.info("[apn @user {}] create xxl jobs, xxl job service create success", SecurityUtils.getUserId());
        xxlJobRelationList.forEach(xxlJobRelation -> {
            Integer xxlJobId = onlyXxlJobIdMap.get(xxlJobRelation.getReferenceId() + "-" + xxlJobRelation.getUserId());
            log.info("[apn @user {}] create xxl jobs, id = {}, xxlJobId = {}", SecurityUtils.getUserId(), xxlJobRelation.getId(), xxlJobId);
            if (xxlJobId != null) {
                xxlJobRelation.setXxlJobId(xxlJobId);
                setXxlJobIdIntoParam(xxlJobRelation);
            }
        });
        xxlJobRelationRepository.saveAll(xxlJobRelationList);
    }

    public void setXxlJobIdIntoParam(XxlJobRelation xxlJobRelation) {
        if (xxlJobRelation != null && xxlJobRelation.getXxlJobId() != null) {
            Map<String, Object> map = new HashMap<>();
            if (StrUtil.isNotBlank(xxlJobRelation.getXxlJobParam())) {
                map = new JSONObject(xxlJobRelation.getXxlJobParam());
            }
            map.put("xxlJobId", xxlJobRelation.getXxlJobId());
            xxlJobRelation.setXxlJobParam(JSONUtil.toJsonStr(map));
        }
    }


    /**
     * 获取回填入参
     * @param xxlJobApnDTO
     * @param xxlJobId
     * @return
     */
    private XxlJobRelation getXxlJobRelationEntity(XxlJobApnDTO xxlJobApnDTO, Integer xxlJobId) {
        return new XxlJobRelation(xxlJobApnDTO, xxlJobId);
    }

    @Override
    public void updateXxlJob(XxlJobApnDTO xxlJobApnDTO) {
        log.info("[apn @user {}] update xxl job", SecurityUtils.getUserId());
        xxlJobClient.updateExecutorTask(xxlJobApnDTO, 0);
        XxlJobRelation xxlJobRelation = getXxlJobRelationEntity(xxlJobApnDTO, xxlJobApnDTO.getId());
        xxlJobRelationRepository.updateTriggerByXxlJobId(xxlJobApnDTO.getId(), xxlJobApnDTO.getTriggerTime(), xxlJobRelation.getReminderConfig(), xxlJobRelation.getTimezone(), xxlJobApnDTO.getTriggerTime().toString());
    }

    @Override
    public void updateJobsBySendTime(List<XxlJobUpdateBySendTimeForJobAdminDTO> xxlJobUpdateBySendTimeList) {
        log.info("[apn @user {}] update xxl jobs", SecurityUtils.getUserId());
        xxlJobUpdateBySendTimeList.forEach(xxlJobUpdateBySendTime -> xxlJobRelationRepository.updateTriggerByXxlJobId(xxlJobUpdateBySendTime.getXxlJobId(),
                xxlJobUpdateBySendTime.getSendTime(), xxlJobUpdateBySendTime.getReminderConfig(), xxlJobUpdateBySendTime.getTimezone(), xxlJobUpdateBySendTime.getSendTime() == null ? "" : xxlJobUpdateBySendTime.getSendTime().toString()));
        xxlJobClient.updateJobsBySendTime(xxlJobUpdateBySendTimeList, 0);
    }

    @Override
    public void deleteXxlJob(Integer xxlJobId) {
        log.info("[apn @user {}] delete xxl job, xxlJobId = {}", SecurityUtils.getUserId(), xxlJobId);
        xxlJobClient.deleteExecutorTask(xxlJobId, 0);
        xxlJobRelationRepository.deleteByXxlJobId(xxlJobId);
    }

    @Override
    public void deleteXxlJobIdList(List<Integer> idList) {
        log.info("[apn @user {}] delete xxl jobs, xxlJobId = {}", SecurityUtils.getUserId(), idList);
        xxlJobClient.deleteXxlJobs(idList, 0);
        xxlJobRelationRepository.deleteByXxlJobIdList(idList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateXxlJobByTenantMessageConfig(List<XxlJobUpdateByTenantMessageConfigDTO> dtoList) {
        log.info("[apn @user {}] update xxl job by message config, param = {}", SecurityUtils.getUserId(), dtoList);
        dtoList.forEach(dto -> {
            TenantMessageMinderConfigFieldCodeEnum type = dto.getType();
            List<XxlJobRelationTypeEnum> xxlJobRelationTypeEnumList;
            if (dto.getType() == TenantMessageMinderConfigFieldCodeEnum.CANDIDATE_OVERDUE_PAYMENT_REMINDER_TIME) {
                //逾期未支付分为 fte和contract
                xxlJobRelationTypeEnumList = CollUtil.newArrayList(XxlJobRelationTypeEnum.TALENT_FTE_INVOICE_OVERDUE_WARN, XxlJobRelationTypeEnum.TALENT_CONTRACT_INVOICE_OVERDUE_WARN);
            } else {
                xxlJobRelationTypeEnumList = CollUtil.newArrayList(type.getXxlJobType());
            }
            if (CollUtil.isNotEmpty(xxlJobRelationTypeEnumList)) {
                //查询发送时间大于当前时间的数据
                List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndTenantIdAndSendTime(
                        xxlJobRelationTypeEnumList.stream().map(XxlJobRelationTypeEnum::toDbValue).toList(), SecurityUtils.getTenantId());
                if (CollUtil.isNotEmpty(xxlJobRelationList)) {
                    List<XxlJobUpdateBySendTimeForJobAdminDTO> paramDtoList = new ArrayList<>();
                    xxlJobRelationList.forEach(xxlJobRelation -> {
                        if (Objects.equals(xxlJobRelation.getReminderConfig(), dto.getNewValue())) {
                            return;
                        }
                        Instant sendInstant = xxlJobRelation.getSendTime();
                        Instant newSendInstant = null;
                        switch (dto.getType()) {
                            case POSITION_UNSUBMITTED_CANDIDATE_DAYS,POSITION_UNINTERVIEWED_DAYS:
                                //day的修改
                                newSendInstant = sendInstant.plus(Long.parseLong(dto.getNewValue()) - Long.parseLong(xxlJobRelation.getReminderConfig()), ChronoUnit.DAYS);
                                break;
                            case CANDIDATE_NOT_ONBOARDED_REMINDER_TIME, CANDIDATE_ONBOARDING_INVOICE_REMINDER_TIME,
                                    CANDIDATE_OVERDUE_PAYMENT_REMINDER_TIME, TEAM_MEMBER_UNSUBMITTED_CANDIDATE_REMINDER_TIME:
                                //time 的修改 HH:mm
                                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(REMINDER_TIME_FORMAT);
                                LocalTime oldTime = LocalTime.parse(xxlJobRelation.getReminderConfig(), formatter);
                                LocalTime newTime = LocalTime.parse(dto.getNewValue(), formatter);
                                Duration duration = Duration.between(oldTime, newTime);
                                newSendInstant = sendInstant.plus(duration.toMinutes(), ChronoUnit.MINUTES);
                                break;
                            default:
                                break;
                        }
                        if (newSendInstant != null) {
                            paramDtoList.add(XxlJobUpdateBySendTimeForJobAdminDTO.builder().xxlJobId(xxlJobRelation.getXxlJobId()).reminderConfig(dto.getNewValue())
                                    .sendTime(newSendInstant).cron(DateUtil.getCron(newSendInstant)).timezone(xxlJobRelation.getTimezone()).build());
                        }
                    });
                    if (CollUtil.isNotEmpty(paramDtoList)) {
                        updateJobsBySendTime(paramDtoList);
                    }
                }
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateXxlJobByTimezone(XxlJobUpdateByTimezoneDTO dto) {
        log.info("[apn @user {}] update xxl job by timezone, param = {}", SecurityUtils.getUserId(), dto);
        List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByUserIdAndSendTime(dto.getUserId());
        List<XxlJobUpdateBySendTimeForJobAdminDTO> paramDtoList = new ArrayList<>();
        if (CollUtil.isNotEmpty(xxlJobRelationList)) {
            xxlJobRelationList
                    .stream().filter(xxlJobRelation -> ObjectUtil.isNotEmpty(xxlJobRelation.getXxlJobId()))
                    .forEach(xxlJobRelation -> {
                if (Objects.equals(xxlJobRelation.getTimezone(), dto.getNewTimezone())) {
                    return;
                }
                Instant oldSendTime = xxlJobRelation.getSendTime();
                ZonedDateTime oldZonedDateTime = oldSendTime.atZone(ZoneId.of(xxlJobRelation.getTimezone()));
                ZonedDateTime newZonedDateTime = oldZonedDateTime.toLocalDateTime().atZone(ZoneId.of(dto.getNewTimezone()));
                Instant newSendTime = newZonedDateTime.toInstant();
                paramDtoList.add(XxlJobUpdateBySendTimeForJobAdminDTO.builder().xxlJobId(xxlJobRelation.getXxlJobId()).reminderConfig(xxlJobRelation.getReminderConfig())
                        .sendTime(newSendTime).cron(DateUtil.getCron(newSendTime)).timezone(dto.getNewTimezone()).build());
            });
        }
        // report subscription 数据没有发送的时间,是一个持续不断的过程
        List<XxlJobRelation> reportSubscriptionList = xxlJobRelationRepository.findAllByTypeAndUserId(XxlJobRelationTypeEnum.REPORT_SUBSCRIPTION, dto.getUserId());
        if (CollUtil.isNotEmpty(reportSubscriptionList)) {
            reportSubscriptionList
                    .stream().filter(reportSubscription -> ObjectUtil.isNotEmpty(reportSubscription.getXxlJobId()))
                    .forEach(xxlJobRelation -> {
                if (Objects.equals(xxlJobRelation.getTimezone(), dto.getNewTimezone())) {
                    return;
                }
                ReportSubscriptionConfigDTO configDTO = JSONUtil.toBean(xxlJobRelation.getReminderConfig(), ReportSubscriptionConfigDTO.class);
                log.info("[APN updateXxlJobByTimezone] json to bean ReportSubscriptionConfigDTO , json = {}", configDTO);
                paramDtoList.add(XxlJobUpdateBySendTimeForJobAdminDTO.builder()
                        .xxlJobId(xxlJobRelation.getXxlJobId())
                        .reminderConfig(xxlJobRelation.getReminderConfig())
                        .sendTime(null)
                        .cron(DateUtil.generateCronExpression(configDTO.getPushTimeType(), configDTO.getDayOfWeek(), configDTO.getDayOfMonth(), configDTO.getSendTime(), dto.getNewTimezone()))
                        .timezone(dto.getNewTimezone()).build());
            });
        }
        if (CollUtil.isNotEmpty(paramDtoList)) {
            updateJobsBySendTime(paramDtoList);
        }
    }

    @Override
    public void deleteXxlJobIdListByType(Integer type) {
        log.info("Batch delete begin. type: {}", type);
        XxlJobRelationTypeEnum xxlJobRelationTypeEnum = XxlJobRelationTypeEnum.fromDbValue(type);
        if(xxlJobRelationTypeEnum == null) {
            return;
        }

        List<XxlJobRelation> allByType = xxlJobRelationRepository.findAllByType(xxlJobRelationTypeEnum);
        // 提取jobId列表
        List<Integer> jobIds = allByType.stream()
                .map(XxlJobRelation::getXxlJobId)
                .collect(Collectors.toList());

        try {
            // 每100个一组进行批量处理
            int batchSize = 100;
            for (int i = 0; i < jobIds.size(); i += batchSize) {
                List<Integer> batchIds = jobIds.subList(
                        i,
                        Math.min(i + batchSize, jobIds.size())
                );
                deleteXxlJobIdList(batchIds);
            }
        } catch (Exception e) {
            // 异常处理
            log.error("批量删除失败", e);
            throw e;
        }
        log.info("Batch delete end. type: {}", type);
    }

}
