package com.altomni.apn.common.dto.upgrade;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.Instant;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class UpgradeCreationDTO {

    private Long id;

    /**
     * 升级日志内容
     */
    @NotEmpty
    private List<UpgradeContentDTO> contents;

    /**
     * 版本号
     */
    private String version;

    /**
     * 更新迭代发布时间
     */
    @NotNull
    private Instant upgradeTime;

    /**
     * 是否全局可见
     */
    private Boolean global;

    /**
     * 若为不可见，输入可见的租户id集合
     */
    private List<Long> tenantIds;

}
