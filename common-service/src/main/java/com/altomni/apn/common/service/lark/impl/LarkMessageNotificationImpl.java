package com.altomni.apn.common.service.lark.impl;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONException;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.config.application.LarkProperties;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.lark.LarkMessageNotification;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Slf4j
@Service
@RefreshScope
public class LarkMessageNotificationImpl implements LarkMessageNotification {

    private final LarkProperties properties;

    private final OkHttpClient client;

    private final HttpService httpService;

    private static final MediaType JSON_TYPE = MediaType.parse("application/json; charset=utf-8");

    private static final String USER_ID_TYPE = "user_id_type";

    private static final String USER_ID = "user_id";

    public LarkMessageNotificationImpl(LarkProperties properties, HttpService httpService) throws IOException {
        this.properties = properties;
        this.httpService = httpService;
        this.client = new OkHttpClient().newBuilder().build();
    }

    @Override
    public String getToken() throws IOException, JSONException {
        JSONObject json = new JSONObject();
        json.put("app_id", properties.getLarkSendMessageServiceAppId());
        json.put("app_secret", properties.getLarkSendMessageServiceAppSecret());
//        RequestBody requestBody = RequestBody.create(json.toString(), JSON_TYPE);
//        Request request = new Request.Builder()
//                .url(properties.getLarkAuthenticationEndpoint())
//                .post(requestBody)
//                .build();
//        try (Response response = this.client.newCall(request).execute()) {
//            if (!response.isSuccessful()) log.error("lark service retrieve token error: {}", response);
//            if(response.body() == null) log.error("lark service retrieve token error. The Get Token return null");
//            JSONObject jsonResponse = new JSONObject(response.body().string());
//            this.token = jsonResponse.getStr("tenant_access_token");
//        }
//        catch (IOException e) {
//            log.error("lark service retrieve token error, stack: {}", ExceptionUtils.getStackTrace(e));
//        }
        try {
            HttpResponse response = httpService.post(properties.getLarkAuthenticationEndpoint(), json.toString());
            if(response.getCode() != 200 && response.getCode() != 201) log.error("lark service retrieve token error: {}", response);
            else {
                if(response.getBody() == null) log.error("lark service retrieve token error. The Get Token return null");
                JSONObject jsonResponse = new JSONObject(response.getBody());
                return jsonResponse.getStr("tenant_access_token");
            }
        }
        catch (Exception e) {
            log.error("lark service retrieve token error, stack: {}", ExceptionUtils.getStackTrace(e));
        }
        return null;
    }

    @Override
    public List<String> obtainUserIdsByEmail(List<String> emails, String token) {
        log.info("lark service: obtain userIds by emails: {}", emails);
        String url = properties.getLarkObtainUserIdsEndpoint() + "?" + USER_ID_TYPE + "=" + USER_ID;
        JSONObject requestBodyJson = new JSONObject();
        requestBodyJson.put("emails", emails);
        Headers headers = new Headers.Builder().add("Authorization", "Bearer " + token)
                .build();

        try {
            HttpResponse response = httpService.post(url, headers, requestBodyJson.toString());
            if(response.getCode() != 200 && response.getCode() != 201) log.error("lark service retrieve userIds by emails error: response code: {}, response header: {}, response body: {}", response.getCode(), response.getHeaders(), response.getBody());
            else {
                if(response.getBody() == null) log.error("lark service retrieve userIds by emails error, response body don't have any valid data! ");
                JSONObject jsonResponse = new JSONObject(response.getBody());
                JSONObject data = jsonResponse.getJSONObject("data");
                if(data != null) {
                    JSONArray emailUsers = data.getJSONArray("user_list");
                    List<String> userIds = new ArrayList<>();
                    List<String> emailNotExist = new ArrayList<>();
                    for (int i = 0; i < emailUsers.size(); i++) {
                        JSONObject user = emailUsers.getJSONObject(i);
                        if (!user.isEmpty()) {
                            String userId = user.getStr("user_id");
                            String email = user.getStr("email");
                            if(userId != null && !StringUtils.isBlank(userId)) userIds.add(userId);
                            else emailNotExist.add(email);
                        }
                    }
                    log.info("lark service retrieve userIds by emails. Below are emails don't exist in the lark : {}", emailNotExist);
                    return userIds;
                }
                else log.error("lark service retrieve userIds by emails error, response body don't have any valid data! ");
            }
        }
        catch (Exception e) {
            log.error("lark service retrieve userIds by emails error, stack: {}", ExceptionUtils.getStackTrace(e));
        }
        return Collections.emptyList();

//        RequestBody requestBody = RequestBody.create(requestBodyJson.toString(), JSON_TYPE);
//        Request request = new Request.Builder()
//                .url(url)
//                .header("Authorization", "Bearer " + token)
//                .post(requestBody)
//                .build();
//        try (Response response = this.client.newCall(request).execute()) {
//            if (!response.isSuccessful()) log.error("lark service retrieve userIds by emails error: {}, response code: {}, response body: {}, response message: {}, response: {}",
//                    response, response.code(), response.body().toString(), response.message(), response);
//            JSONObject jsonResponse = new JSONObject(response.body().string());
//            JSONObject data = jsonResponse.getJSONObject("data");
//            if(data != null) {
//                JSONArray emailUsers = data.getJSONArray("user_list");
//                List<String> userIds = new ArrayList<>();
//                List<String> emailNotExist = new ArrayList<>();
//                for (int i = 0; i < emailUsers.size(); i++) {
//                    JSONObject user = emailUsers.getJSONObject(i);
//                    if (!user.isEmpty()) {
//                        String userId = user.getStr("user_id");
//                        String email = user.getStr("email");
//                        if(userId != null && !StringUtils.isBlank(userId)) userIds.add(userId);
//                        else emailNotExist.add(email);
//                    }
//                }
//                log.info("lark service retrieve userIds by emails. Below are emails don't exist in the lark : {}", emailNotExist);
//                return userIds;
//            }
//            else log.error("lark service retrieve userIds by emails error, response body don't have any valid data! ");
//
//        } catch (Exception e) {
//            log.error("lark service retrieve userIds by emails error, stack: {}", ExceptionUtils.getStackTrace(e));
//        }

    }

    //TODO: merge voicemail lark send message function
    @Override
    public void sendLarkMessage(String content, String email, String token) {
        JSONObject requestBodyJson = new JSONObject();
        JSONObject contentJson = new JSONObject();
        contentJson.put("text", content);
        requestBodyJson.put("email", email);
        requestBodyJson.put("msg_type", "text");
        requestBodyJson.put("content", contentJson);
        Headers headers = new Headers.Builder().add("Authorization", "Bearer " + token)
                .build();

        try {
            HttpResponse response = httpService.post(properties.getLarkSendMessageEndpoint(), headers, requestBodyJson.toString());
            if(response.getCode() != 200 && response.getCode() != 201) log.error("lark service send message error: {}", response);
            else {
                if(response.getBody() == null) log.error("lark service retrieve userIds by emails error, response body don't have any valid data! ");
            }
        }
        catch (Exception e) {
            log.error("lark service send message error, stack: {}", ExceptionUtils.getStackTrace(e));
        }

//        Request request = new Request.Builder()
//                .url(properties.getLarkSendMessageEndpoint())
//                .header("Authorization", "Bearer " + token)
//                .post(requestBody)
//                .build();
//        try (Response response = this.client.newCall(request).execute()) {
//            if (!response.isSuccessful()) log.error("lark service send message error: {}", response);
////            JSONObject jsonResponse = new JSONObject(response.body().string());
//        } catch (Exception e) {
//            log.error("lark service send message error, stack: {}", ExceptionUtils.getStackTrace(e));
//        }
    }


    @Override
    public void batchSendLarkMessage(String content, List<String> userIds, String token) {
        if(userIds != null && !userIds.isEmpty()) {
            JSONObject requestBodyJson = new JSONObject();
            JSONObject contentJson = new JSONObject();
            contentJson.put("text", content);
            requestBodyJson.put("user_ids", userIds);
            requestBodyJson.put("open_ids", new ArrayList<>());
            requestBodyJson.put("msg_type", "text");
            requestBodyJson.put("content", contentJson);
            RequestBody requestBody = RequestBody.create(requestBodyJson.toString(), JSON_TYPE);
            Headers headers = new Headers.Builder().add("Authorization", "Bearer " + token)
                    .build();
            try {
                HttpResponse response = httpService.post(properties.getLarkBatchSendMessageEndpoint(), headers, requestBodyJson.toString());
                if(response.getCode() != 200 && response.getCode() != 201) log.error("lark service batch send message error: {}", response);
                else {
                    JSONObject jsonResponse = new JSONObject(response.getBody());
                    JSONObject data = jsonResponse.getJSONObject("data");
                    JSONArray invalidUserIds = data.getJSONArray("invalid_user_ids");
                    String messageId = data.getStr("message_id");
                    if(invalidUserIds != null && !invalidUserIds.isEmpty()) log.info("lark service batch send message. Below are the invalid userIds : {}", invalidUserIds);
                    log.info("lark service batch send message, message id: {}", messageId);
                }
            }
            catch (Exception e) {
                log.error("lark service batch send message error, stack: {}", ExceptionUtils.getStackTrace(e));
            }
//            Request request = new Request.Builder()
//                    .url(properties.getLarkBatchSendMessageEndpoint())
//                    .header("Authorization", "Bearer " + token)
//                    .post(requestBody)
//                    .build();
//            try (Response response = this.client.newCall(request).execute()) {
//                if (!response.isSuccessful()) log.error("lark service batch send message error: {}", response);
//                JSONObject jsonResponse = new JSONObject(response.body().string());
//                JSONObject data = jsonResponse.getJSONObject("data");
//                JSONArray invalidUserIds = data.getJSONArray("invalid_user_ids");
//                String messageId = data.getStr("message_id");
//                if(invalidUserIds != null && !invalidUserIds.isEmpty()) log.info("lark service batch send message. Below are the invalid userIds : {}", invalidUserIds.toString());
//                log.info("lark service batch send message, message id: {}", messageId);
//            } catch (Exception e) {
//                log.error("lark service batch send message error, stack: {}", ExceptionUtils.getStackTrace(e));
//            }
        }
    }


}
