package com.altomni.apn.common.service.esfller.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.env.ApplicationProperties;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.searcheamil.SearchEmailByNameDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.service.esfller.EsFillerService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.Objects;

@Slf4j
@Service("esFillerService")
public class EsFillerServiceImpl implements EsFillerService {

    @Resource
    private HttpService httpService;

    @Resource
    private ApplicationProperties applicationProperties;

    private String searchEmailFromTalentAndUserByNameURL() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/apn/user-talent-name";
    }

    private String searchEmailFromTalentAndUserByNameListURL() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/apn/user-talent-name/list";
    }

    private String searchCompanyInfoByNameListURL() {
        return applicationProperties.getApnCommonServiceUrl() + "/api/v1/search/crm/company/name-search/with-source";
    }

    @Override
    public String searchEmailFromTalentAndUserByName(SearchEmailByNameDTO searchEmailByNameDTO) {
        JSONObject param = new JSONObject();
        //tenantId
        param.put("tenantId", SecurityUtils.getTenantId());
        //name
        param.put("input", searchEmailByNameDTO.getName());
        //model
        param.put("searchModule", "USER_AND_TALENT_NAME_MATCH");
        //page,size
        param.put("page", 0);
        param.put("size", Math.min(searchEmailByNameDTO.getSize(), 600));
        String url = searchEmailFromTalentAndUserByNameURL();
        String condition = JSONUtil.toJsonStr(param);
        HttpResponse response;
        try {
            response = httpService.post(url, condition);
            if (response != null) {
                if (Objects.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.info("[APN@{}] searchEmailFromTalentAndUserByName is success", SecurityUtils.getUserId());
                    //Special handling when an error code is 404/422 "Empty query", return 200
                    return response.getBody();
                } else if (Objects.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || Objects.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                } else {
                    log.error("[APN: @{}] searchEmailFromTalentAndUserByName, param = {}, code = {}, result = {}", SecurityUtils.getUserId(), condition, response.getCode(), response.getBody());
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: @{}] searchEmailFromTalentAndUserByName is null , param = {}", SecurityUtils.getUserId(), condition);
                throw new ExternalServiceInterfaceException();
            }
        } catch (Exception e) {
            log.error("[APN: @{}] searchEmailFromTalentAndUserByName is error, msg = {}", SecurityUtils.getUserId(), ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("Internet error, try later please!");
        }
    }

    @Override
    public String searchEmailFromTalentAndUserByNames(SearchEmailByNameDTO searchEmailByNameDTO) {
        JSONObject param = new JSONObject();
        //tenantId
        param.put("tenantId", SecurityUtils.getTenantId());
        //name
        param.put("input", searchEmailByNameDTO.getNameList().stream().toArray(String[]::new));
        //model
        param.put("searchModule", "USER_AND_TALENT_NAME_MATCH");
        //page,size
        param.put("page", 0);
        param.put("size", Math.min(searchEmailByNameDTO.getSize(), 600));
        String url = searchEmailFromTalentAndUserByNameListURL();
        String condition = JSONUtil.toJsonStr(param);
        HttpResponse response;
        try {
            response = httpService.post(url, condition);
            if (response != null) {
                if (Objects.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.info("[APN@{}] searchEmailFromTalentAndUserByName is success", SecurityUtils.getUserId());
                    //Special handling when an error code is 404/422 "Empty query", return 200
                    return response.getBody();
                } else if (Objects.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || Objects.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                } else {
                    log.error("[APN: @{}] searchEmailFromTalentAndUserByName, param = {}, code = {}, result = {}", SecurityUtils.getUserId(), condition, response.getCode(), response.getBody());
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: @{}] searchEmailFromTalentAndUserByName is null , param = {}", SecurityUtils.getUserId(), condition);
                throw new ExternalServiceInterfaceException();
            }
        } catch (Exception e) {
            log.error("[APN: @{}] searchEmailFromTalentAndUserByName is error, msg = {}", SecurityUtils.getUserId(), ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("Internet error, try later please!");
        }
    }

    @Override
    public String getCompanyInfoByName(String name) {
        JSONObject param = new JSONObject();
        //tenantId
        param.put("sources", Arrays.asList("businessName","registeredAddress","creditCode","locations").stream().toArray(String[]::new));
        //name
        param.put("names", Arrays.asList(name).stream().toArray(String[]::new));
        String url = searchCompanyInfoByNameListURL();
        String condition = JSONUtil.toJsonStr(param);
        HttpResponse response;
        try {
            response = httpService.post(url, condition);
            if (response != null) {
                if (Objects.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.info("[APN@{}] getCompanyInfoByName is success", SecurityUtils.getUserId());
                    //Special handling when an error code is 404/422 "Empty query", return 200
                    return response.getBody();
                } else if (Objects.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || Objects.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                } else {
                    log.error("[APN: @{}] getCompanyInfoByName, param = {}, code = {}, result = {}", SecurityUtils.getUserId(), condition, response.getCode(), response.getBody());
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                }
            } else {
                log.error("[APN: @{}] getCompanyInfoByName is null , param = {}", SecurityUtils.getUserId(), condition);
                throw new ExternalServiceInterfaceException();
            }
        } catch (Exception e) {
            log.error("[APN: @{}] getCompanyInfoByName is error, msg = {}", SecurityUtils.getUserId(), ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("Internet error, try later please!");
        }
    }
}
