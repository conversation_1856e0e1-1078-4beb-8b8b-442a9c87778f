package com.altomni.apn.common.domain.settings;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.config.SettingTrackRecordType;
import com.altomni.apn.common.domain.config.SettingTrackRecordTypeConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@ApiModel(value = "跟进记录模版表",description = "")
@Entity
@Table(name="setting_track_record_template")
@Data
public class SettingTrackRecordTemplate extends AbstractPermissionAuditingEntity implements Serializable, Cloneable{

    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id;

    @ApiModelProperty(name = "租户id")
    @Column(name = "tenant_id")
    private BigInteger tenantId ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "temp_name")
    private String tempName ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "temp_remark")
    private String tempRemark ;

    /** 1公共 2个人 */
    @ApiModelProperty(name = "1公共 2个人")
    @Column(name = "temp_type")
    @Convert(converter = SettingTrackRecordTypeConverter.class)
    private SettingTrackRecordType tempType ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "temp_content")
    private String tempContent ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "user_name")
    private String userName ;

    /** 状态 0-无效 1-有效 */
    @ApiModelProperty(name = "状态 0-无效 1-有效")
    @Column(name = "status")
    private Integer status ;

    /** 创建人 */
    @Column(name = "created_name")
    private String createdName;
}
