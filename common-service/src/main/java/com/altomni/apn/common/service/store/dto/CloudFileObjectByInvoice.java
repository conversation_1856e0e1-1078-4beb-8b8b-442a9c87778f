package com.altomni.apn.common.service.store.dto;

public class CloudFileObjectByInvoice {

    private byte[] content;

    private String contentType;

    private String fileName;

    private String s3Link;

    public CloudFileObjectByInvoice() {}

    public CloudFileObjectByInvoice(byte[] content, String contentType, String fileName,String s3Link) {
        this.content = content;
        this.contentType = contentType;
        this.fileName = fileName;
        this.s3Link = s3Link;
    }

    public byte[] getContent() {
        return content;
    }

    public void setContent(byte[] content) {
        this.content = content;
    }

    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getS3Link() {
        return s3Link;
    }

    public void setS3Link(String s3Link) {
        this.s3Link = s3Link;
    }

    @Override
    public String toString() {
        return "CloudFileObject{" +
                "fileName='" + fileName + '\'' +
                ", contentType='" + contentType + '\'' +
                '}';
    }
}
