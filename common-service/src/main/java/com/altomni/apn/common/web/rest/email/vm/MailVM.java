package com.altomni.apn.common.web.rest.email.vm;

import com.altomni.apn.common.dto.mail.AttachmentVM;
import lombok.Data;

import java.util.List;

@Data
public class MailVM {

    private Long id;
    private String from;
    private List<String> to;
    private List<String> bcc;
    private List<String> cc;
    private String subject;
    private String content;
    private List<String> links;
    private List<AttachmentVM> attachments;

    //related id
    private Long replyToThreadId;
    private String customerId1;
    private String customerId2;

    private String baseUrl; //for other internal service, including its base url for any links in mail

    private Long talentId;
}
