package com.altomni.apn.common.web.rest.store;

import com.altomni.apn.common.domain.parser.enumeration.ParseType;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.store.CopyObjectDto;
import com.altomni.apn.common.dto.store.UploadUrlDto;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.store.S3DisplayService;
import com.altomni.apn.common.service.store.StoreService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.store.DisplayLinkVo;
import com.altomni.apn.common.vo.store.StoreGetUploadUrlVO;
import io.swagger.annotations.Api;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Map;

/**
 * REST controller for managing StoreService.
 */
@Api(tags = {"APN-StoreService"})
@RestController
@RequestMapping("/api/v3")
public class StoreResource {

    private final Logger log = LoggerFactory.getLogger(StoreResource.class);

    @Resource
    private StoreService storeService;

    @Resource
    private S3DisplayService s3DisplayService;

    @PostMapping("/s3/store/upload-content-with-key")
    public ResponseEntity<String> uploadContentStringWithKey(@RequestParam UploadTypeEnum uploadTypeEnum, @RequestParam String contentType, @RequestParam String key, @RequestBody String content) throws IOException {
        log.info("[StoreService: uploadContentString @{}] REST request to upload to s3 type: {} ", SecurityUtils.getUserId(), contentType);
        String s3link = storeService.S3ContentUpload(uploadTypeEnum, contentType, content, key);
        return ResponseEntity.ok(s3link);
    }

    @PostMapping("/s3/store/upload-contentBytes-with-key")
    public ResponseEntity<String> uploadContentBytesWithKey(@RequestParam UploadTypeEnum uploadTypeEnum, @RequestParam String contentType, @RequestParam String key, @RequestBody byte[] contentBytes) throws IOException {
        log.info("[StoreService: uploadContentString @{}] REST request to upload to s3 type: {} ", SecurityUtils.getUserId(), contentType);
        String s3link = storeService.S3ContentUpload(uploadTypeEnum, contentType, contentBytes, key);
        return ResponseEntity.ok(s3link);
    }

    @GetMapping("/s3/parser/presigned-upload-url-with-post-policy")
    public ResponseEntity<Map<String, String>> getPresignedResumeUploadUrlFromS3WithPostPolicy(@RequestParam ParseType parseType, @RequestParam String key, @RequestParam String filename, @RequestParam String contentType, @RequestParam Long lowerLimit, @RequestParam Long upperLimit) {
        log.info("[StoreService: getPresignedUrlFromS3@{}] REST request to get presigned file url by key: {}", SecurityUtils.getUserId(), key);
        Map<String, String> postPolicy = storeService.getPresignedUploadUrlFromS3WithPostPolicy(parseType, key, filename, contentType, lowerLimit, upperLimit);
        return ResponseEntity.ok(postPolicy);
    }

    @PostMapping("/s3/store/upload-url")
    public ResponseEntity<StoreGetUploadUrlVO> getPresignedCommonUploadUrlFromS3WithPostPolicy(@RequestBody UploadUrlDto uploadUrlDto) {
        log.info("[StoreService: getPresignedCommonUploadUrlFromS3WithPostPolicy@{}] REST request to get presigned common upload file url by key: {}, uploadType: {}, contentType: {}", SecurityUtils.getUserId(), uploadUrlDto.getUuid(), uploadUrlDto.getUploadType(), uploadUrlDto.getContentType());
        return ResponseEntity.ok(storeService.getPresignedCommonUploadUrlFromS3WithPostPolicy(uploadUrlDto));
    }

    @GetMapping("/s3/store/url/{uuid}/{uploadType}")
    public ResponseEntity<String> getDisplayUrlFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType) {
        log.info("[StoreService: getDisplayUrlFromS3@{}] REST request to get display url by key :{} and uploadType: {}", SecurityUtils.getUserId(), uuid, uploadType);
        return ResponseEntity.ok(storeService.getCommonUrlFromS3(uuid, uploadType, null));
    }

    @GetMapping("/s3/store/url-with-content-type/{uuid}/{uploadType}")
    public ResponseEntity<String> getDisplayUrlFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType, @RequestParam("contentType") String contentType) {
        log.info("[StoreService: getDisplayUrlFromS3@{}] REST request to get display url by key :{} and uploadType: {}", SecurityUtils.getUserId(), uuid, uploadType);
        return ResponseEntity.ok(storeService.getCommonUrlFromS3(uuid, uploadType, contentType));
    }

    @GetMapping("/s3/store/url-with-response-content-type/{uuid}/{uploadType}")
    public ResponseEntity<DisplayLinkVo> getDisplayUrlVoFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType) {
        log.info("[StoreService: getDisplayUrlFromS3@{}] REST request to get display url by key :{} and uploadType: {}", SecurityUtils.getUserId(), uuid, uploadType);
        return ResponseEntity.ok(storeService.getDisplayUrlVoFromS3(uuid, uploadType));
    }

    @GetMapping("/s3/store/download-url/{uuid}/{uploadType}")
    public ResponseEntity<String> getDownloadUrlFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType, @RequestParam("needRestResponseContentDisposition") Boolean needRestResponseContentDisposition) {
        log.info("[StoreService: getDownloadUrlFromS3@{}] REST request to get download url by key :{} and uploadType: {}", SecurityUtils.getUserId(), uuid, uploadType, needRestResponseContentDisposition);
        return ResponseEntity.ok(storeService.getDownloadUrlFromS3(uuid, uploadType, needRestResponseContentDisposition));
    }

    @GetMapping("/s3/store/download-watermark-url/{uuid}/{uploadType}")
    public ResponseEntity<String> getDownloadWaterMarkUrlFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType, @RequestParam("needRestResponseContentDisposition") Boolean needRestResponseContentDisposition) {
        log.info("[StoreService: getDownloadUrlFromS3@{}] REST request to get download url by key :{} and uploadType: {}", SecurityUtils.getUserId(), uuid, uploadType, needRestResponseContentDisposition);
        return ResponseEntity.ok(storeService.getDownloadWatermarkUrlFromS3(uuid, uploadType, needRestResponseContentDisposition));
    }


    @GetMapping("/s3/store/file/{uuid}/{uploadType}")
    public ResponseEntity<CloudFileObjectMetadata> getFileFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType) {
        log.info("[StoreService: getFileFromS3@{}] REST request to get file by key :{} and uploadType: {}", SecurityUtils.getUserId(), uuid, uploadType);
        return ResponseEntity.ok(storeService.getCommonFileByTypeFromS3(uuid, uploadType));
    }

    @GetMapping("/s3/store/detail-without-file-byte/{uuid}/{uploadType}")
    public ResponseEntity<CloudFileObjectMetadata> getFileDetailWithoutFileFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType) {
        log.info("[StoreService: getFileFromS3@{}] REST request to get file by key :{} and uploadType: {}", SecurityUtils.getUserId(), uuid, uploadType);
        return ResponseEntity.ok(storeService.getCommonFileWithoutFileByteByTypeFromS3(uuid, uploadType));
    }

    @PostMapping("/s3/common/upload")
    public ResponseEntity<String> uploadDocument(@RequestBody MultipartFile file, @RequestParam("key") String key, @RequestParam("uploadType") String uploadType) throws IOException {
        log.info("[StoreService: upload common @{}] REST request to upload file, key = {}, uploadType = {}", key, uploadType, SecurityUtils.getUserId());
        if (file == null || file.isEmpty()) {
            throw new CustomParameterizedException("File cannot be empty");
        }
        storeService.s3Upload(key, file, uploadType);
        String s3link = storeService.getCommonUrlFromS3(key, uploadType, null);
        return ResponseEntity.ok(s3link);
    }

    @GetMapping("/s3/store/is-exists/{uuid}/{uploadType}")
    public ResponseEntity<Boolean> exists(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType) {
        log.info("[StoreService: isExists@{}] REST request to is exists by uuid: {}, uploadType: {}", SecurityUtils.getUserId(), uuid, uploadType);
        return ResponseEntity.ok(storeService.exists(uuid, uploadType));
    }

    @PostMapping("/s3/store/copyObject")
    public ResponseEntity<Boolean> copyObject(@RequestBody CopyObjectDto copyObjectDto) {
        log.info("[StoreService: copyObject@{}] REST request to is copyObject by dto: {}", SecurityUtils.getUserId(), copyObjectDto);
        return ResponseEntity.ok(storeService.copyObject(copyObjectDto));
    }

    @GetMapping("/s3/store/cookie")
    public void getDisplayCookie(HttpServletResponse response) {
        log.info("[StoreService: getDisplayCookie@{}] REST request to get display cookie", SecurityUtils.getUserId());
        ResponseCookie cookie = s3DisplayService.getCookie();
        response.addHeader(HttpHeaders.SET_COOKIE, cookie.toString());
    }

    @GetMapping("/s3/store/url-with-redirection/{uuid}/{uploadType}")
    public void getDisplayUrlFromS3WithRedirection(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType,
                                                   @RequestParam("contentType") String contentType,
                                                   HttpServletRequest request, HttpServletResponse response) throws IOException {
        log.info("[StoreService: getDisplayUrlFromS3WithRedirection@{}] REST request to get display url ", SecurityUtils.getUserId());
        String redirectUrl = s3DisplayService.getDisplayUrlVoFromS3(uuid, uploadType, contentType, request);
        response.sendRedirect(redirectUrl);
    }



}