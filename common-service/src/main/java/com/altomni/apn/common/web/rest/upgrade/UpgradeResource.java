package com.altomni.apn.common.web.rest.upgrade;


import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.dto.upgrade.UpgradeCreationDTO;
import com.altomni.apn.common.service.upgrade.UpgradeService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.upgrade.UpgradeNotificationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/api/v3/upgrade")
public class UpgradeResource {

    @Resource
    UpgradeService upgradeService;

    /**
     * 创建更新迭代提示
     *
     * @param upgradeCreationDTO
     * @return
     */
    @PostMapping("")
    ResponseEntity<Void> create(@RequestBody @Valid UpgradeCreationDTO upgradeCreationDTO) {
        log.info("[APN: Upgrade @{}] REST request to create upgrade notification : {}", SecurityUtils.getUserId(), upgradeCreationDTO);
        upgradeService.create(upgradeCreationDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 查询当前用户下最新的更新迭代提示
     *
     * @param language 语言
     * @return
     */
    @GetMapping("")
    ResponseEntity<UpgradeNotificationVO> getUpgradeNotification(@RequestParam("language") LanguageEnum language) {
        log.info("[APN: Upgrade @{}] REST request to query upgrade notification by language : {}", SecurityUtils.getUserId(), language);
        UpgradeNotificationVO result = upgradeService.getUpgradeNotification(SecurityUtils.getUserId(), SecurityUtils.getTenantId(), language);
        return ResponseEntity.ok(result);
    }

    /**
     * 触发当前更新迭代该用户已读
     */
    @PutMapping("/{id}")
    ResponseEntity<Void> updateUpgradeUserRelation(@PathVariable("id") Long upgradeId) {
        log.info("[APN: Upgrade @{}] REST request to update upgrade user relation: {}", SecurityUtils.getUserId(), upgradeId);
        upgradeService.updateUpgradeUserRelation(upgradeId, SecurityUtils.getUserId(), SecurityUtils.getTenantId());
        return ResponseEntity.ok().build();
    }
}
