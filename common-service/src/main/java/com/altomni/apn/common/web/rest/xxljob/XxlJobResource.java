package com.altomni.apn.common.web.rest.xxljob;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateBySendTimeForJobAdminDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTenantMessageConfigDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTimezoneDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.xxljob.XxlJobService;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class XxlJobResource {

    @Resource
    private XxlJobService xxlJobService;

    /**
     * 创建xxl-job任务
     * @param xxlJobApnDTO
     * @return
     */
    @PostMapping("/xxl-job")
    public ResponseEntity<Integer> createXxlJob(@Valid @RequestBody XxlJobApnDTO xxlJobApnDTO) {
        log.info("[APN: XxlJobResource @{}] REST request to create xxl-job param : {}", SecurityUtils.getUserId(), xxlJobApnDTO);
        return ResponseEntity.ok(xxlJobService.createXxlJob(xxlJobApnDTO));
    }

    /**
     * 批量创建xxl-job任务
     * @param xxlJobApnDTOList
     * @return
     */
    @PostMapping("/xxl-jobs")
    public ResponseEntity<Void> createXxlJobs(@Valid @RequestBody List<XxlJobApnDTO> xxlJobApnDTOList) {
        log.info("[APN: XxlJobResource @{}] REST request to create xxl-jobs param : {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(xxlJobApnDTOList));
        xxlJobService.createXxlJobs(xxlJobApnDTOList);
        return ResponseEntity.ok().build();
    }

    /**
     * 修改xxl-job任务
     * @param xxlJobApnDTO
     * @return
     */
    @PutMapping("/xxl-job")
    public ResponseEntity<Void> updateXxlJob(@Valid @RequestBody XxlJobApnDTO xxlJobApnDTO) {
        log.info("[APN: XxlJobResource @{}] REST request to update xxl-job param : {}", SecurityUtils.getUserId(), xxlJobApnDTO);
        if (ObjectUtil.isEmpty(xxlJobApnDTO.getId())) {
            throw new CustomParameterizedException("id is required");
        }
        xxlJobService.updateXxlJob(xxlJobApnDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 批量修改xxl-job发送时间
     * @param xxlJobList
     * @return
     */
    @PutMapping("/xxl-jobs-by-sendTime")
    public ResponseEntity<Void> updateJobsBySendTime(@Valid @RequestBody List<XxlJobUpdateBySendTimeForJobAdminDTO> xxlJobList) {
        log.info("[APN: XxlJobResource @{}] REST request to update xxl-jobs param : {}", SecurityUtils.getUserId(), xxlJobList);
        xxlJobService.updateJobsBySendTime(xxlJobList);
        return ResponseEntity.ok().build();
    }

    /**
     * 删除xxl-job任务
     * @param xxlJobId
     * @return
     */
    @DeleteMapping("/xxl-job/{xxlJobId}")
    public ResponseEntity<Void> deleteByXxlJobId(@PathVariable("xxlJobId") Integer xxlJobId) {
        log.info("[APN: XxlJobResource @{}] REST request to delete xxl-job xxlJobId : {}", SecurityUtils.getUserId(), xxlJobId);
        xxlJobService.deleteXxlJob(xxlJobId);
        return ResponseEntity.ok().build();
    }

    /**
     * 批量删除xxl-job任务
     * @param idList
     * @return
     */
    @DeleteMapping("/xxl-job")
    public ResponseEntity<Void> deleteXxlJobIdList(@RequestBody List<Integer> idList) {
        log.info("[APN: XxlJobResource @{}] REST request to delete xxl-job idList : {}", SecurityUtils.getUserId(), idList);
        xxlJobService.deleteXxlJobIdList(idList);
        return ResponseEntity.ok().build();
    }

    /**
     * 根据租户的message config 来修改xxl-job任务
     * @param dtoList
     * @return
     */
    @PutMapping("/xxl-job/update-xxl-job-by-tenant-message-config")
    public ResponseEntity<Void> updateXxlJobByTenantMessageConfig(@RequestBody List<XxlJobUpdateByTenantMessageConfigDTO> dtoList) {
        log.info("[APN: XxlJobResource @{}] REST request to update xxl job by tenant message config . param : {}", SecurityUtils.getUserId(), dtoList);
        xxlJobService.updateXxlJobByTenantMessageConfig(dtoList);
        return ResponseEntity.ok().build();
    }

    /**
     * 修改时区的时候来修改xxl-job任务
     * @param xxlJobUpdateByTimezoneDTO
     * @return
     */
    @PutMapping("/xxl-job/update-xxl-job-by-timezone")
    public ResponseEntity<Void> updateXxlJobByTimezone(@RequestBody XxlJobUpdateByTimezoneDTO xxlJobUpdateByTimezoneDTO) {
        log.info("[APN: XxlJobResource @{}] REST request to update xxl job by Timezone . param : {}", xxlJobUpdateByTimezoneDTO.getUserId(), xxlJobUpdateByTimezoneDTO);
        xxlJobService.updateXxlJobByTimezone(xxlJobUpdateByTimezoneDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 根据提醒type批量删除xxl-job任务
     * @param type
     * @return
     */
    @DeleteMapping("/xxl-job/type/{type}")
    public ResponseEntity<Void> deleteXxlJobIdListByType(@PathVariable("type") Integer type) {
        log.info("[APN: XxlJobResource @{}] REST request to delete  xxl-job type : {}", SecurityUtils.getUserId(), type);
        xxlJobService.deleteXxlJobIdListByType(type);
        return ResponseEntity.ok().build();
    }

}
