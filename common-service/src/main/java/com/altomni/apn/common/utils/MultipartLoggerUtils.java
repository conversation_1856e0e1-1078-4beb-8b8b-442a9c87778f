package com.altomni.apn.common.utils;

import okhttp3.MultipartBody;
import okio.Buffer;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

public class MultipartLoggerUtils {

    private static final int HTML_CONTENT_LIMIT = 100; // 限制html_content最多显示100字符

    public static String formatMultipartBody(MultipartBody.Builder builder) {
        MultipartBody requestBody = builder.build();
        StringBuilder logBuilder = new StringBuilder("MultipartBody Content:\n");

        for (MultipartBody.Part part : requestBody.parts()) {
            if (part.headers() != null) {
                String header = part.headers().toString();

                if (header.contains("name=\"html_content\"") || header.contains("name=\"content\"")) {
                    // 处理 html_content 或 content，截取前100字符
                    try {
                        Buffer buffer = new Buffer();
                        part.body().writeTo(buffer);
                        String content = buffer.readString(StandardCharsets.UTF_8);
                        String truncatedContent = content.length() > HTML_CONTENT_LIMIT
                                ? content.substring(0, HTML_CONTENT_LIMIT) + "..."
                                : content;
                        logBuilder.append("html_content: ").append(truncatedContent).append("\n");
                    } catch (IOException e) {
                        logBuilder.append("html_content: [Error reading content] \n");
                    }
                } else {
                    // 其他字段完整打印
                    logBuilder.append(header.trim()).append("\n");
                }
            } else {
                logBuilder.append("[Unknown Part]").append("\n");
            }
        }

        return logBuilder.toString();
    }

}
