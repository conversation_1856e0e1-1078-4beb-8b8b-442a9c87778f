package com.altomni.apn.common.service.lark;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.nacos.shaded.com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.altomni.apn.common.dto.calendar.CalendarLarkUpdateDTO;
import com.altomni.apn.common.dto.calendar.NoPoachingRemindDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.lark.LarkUserTokenDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.service.cache.redis.RedisService;
import com.altomni.apn.common.service.http.HttpService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.Instant;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 同步数据至lark的参考文档：https://open.larksuite.com/document/server-docs/contact-v3/user/batch_get_id
 */
@Slf4j
@RefreshScope
@Configuration
public class LarkClient {

    @Value("${lark.appId:********************}")
    private String appId;

    @Value("${lark.appSecret:ZYHNB05Kmhf3c17V4ZJcqbCihT8rIiL6}")
    private String appSecret;

    @Value("${lark.skip:true}")
    private Boolean skip;

    @Resource
    private RedisService redisService;

    @Resource
    private HttpService httpService;

    /**获取 tenant_access_token（企业自建应用） */
    private static final String TENANT_ACCESS_TOKEN_URL = "https://open.larksuite.com/open-apis/auth/v3/tenant_access_token/internal";

    /**获取 user_access_token （企业自建应用/商店应用）*/
    private static final String USER_ACCESS_TOKEN_URL = "https://open.larksuite.com/open-apis/authen/v2/oauth/token";

    /** 创建日历 */
    private static final String CREATE_CALENDAR_URL = "https://open.larksuite.com/open-apis/calendar/v4/calendars";

    /** 创建日程 */
    private static final String CREATE_CALENDAR_EVENT_URL = "https://open.larksuite.com/open-apis/calendar/v4/calendars/{}/events";

    /** 修改日程 */
    private static final String UPDATE_CALENDAR_EVENT_URL = "https://open.larksuite.com/open-apis/calendar/v4/calendars/{}/events/{}";

    /** 根据email获取lark用户id */
    private static final String SEARCH_ATTENDEE_Id_URL = "https://open.larksuite.com/open-apis/contact/v3/users/batch_get_id";

    /** 创建日程参与人 */
    private static final String CREATE_CALENDAR_EVENT_ATTENDEE_URL = "https://open.larksuite.com/open-apis/calendar/v4/calendars/{}/events/{}/attendees";

    /** 获取lark 会议参与列表 */
    private static final String SEARCH_ATTENDEE_ID_LIST_URL = "https://open.larksuite.com/open-apis/calendar/v4/calendars/{}/events/{}/attendees";

    /** 根据email获取lark用户id */
    private static final String DELETE_ATTENDEE_ID_LIST_URL = "https://open.larksuite.com/open-apis/calendar/v4/calendars/{}/events/{}/attendees/batch_delete";

    /** 删除日程 */
    private static final String DELETE_CALENDAR_EVENT_URL = "https://open.larksuite.com/open-apis/calendar/v4/calendars/{}/events/{}";

    /** lark 上传图片接口 */
    private static final String UPLOAD_IMAGE_URL = "https://open.larksuite.com/open-apis/im/v1/images";

    /** 发送云卡片 */
    private static final String SEND_FEED_CARD_URL = "https://open.larksuite.com/open-apis/message/v4/send/";

    private static final String TENANT_ACCESS_TOKEN_KEY = "tenant_access_token";
    private static final String TENANT_ACCESS_TOKEN_EXPIRE_KEY = "expire";
    private static final String JSON_DATA_KEY = "data";

    private static final String JSON_CODE_KEY = "code";
    private static final String JSON_CALENDAR_KEY = "calendar";
    private static final String JSON_CALENDAR_ID_KEY = "calendar_id";
    private static final String JSON_CALENDAR_EVENT_KEY = "event";
    private static final String JSON_CALENDAR_EVENT_ID_KEY = "event_id";
    private static final String JSON_SEARCH_USER_ID_LIST_KEY = "user_list";
    private static final String REDIS_LARK_TOKEN_KEY = "REDIS_LARK_TOKEN_KEY";
    private static final String REDIS_LARK_USER_TOKEN_KEY = "REDIS_LARK_USER_TOKEN_KEY_USER_ID:";

    private static final Integer SUCCESS_CODE = 200;

    private static final String IMAGE_KEY = "image_key";
    private static final String TOKEN_TYPE = "Bearer ";
    private static String calendarId = "";

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("async-sync-lark-pool-").build();

    public static final ExecutorService executorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 3,
            Runtime.getRuntime().availableProcessors() * 5, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100000), THREAD_FACTORY);

    /**
     * 同步日程至lark，同时添加参会者
     * @param summary
     * @param description
     * @param startTime
     * @param endTime
     * @param emails
     * @param id apn的日程Id
     * @param consumer larkCalendarId 和 larkEventId 设置apn中的方法
     */
    public void createCalendarEventWithAttendees(Integer minutes, String summary, String description, Instant startTime, Instant endTime, List<String> emails, Long id, Consumer<CalendarLarkUpdateDTO> consumer) {
        log.info("skip sync to lark: ", skip);
        if (skip) {
            return;
        }
        String calendarId = createCalendar();
        log.info("Successfully created a Lark calendar. calendarId = {}", calendarId);
        String eventId = createCalendarEvent(calendarId, summary, description, startTime, endTime, minutes);
        log.info("Successfully created a Lark calendar event. eventId = {}", eventId);
        createCalendarEventAttendee(calendarId, eventId, emails, null);
        log.info("Successfully added attendees to the Lark calendar event. calendarId = {}, eventId = {}", calendarId, eventId);
        consumer.accept(new CalendarLarkUpdateDTO(id, calendarId, eventId));
        log.info("Successfully passed the Lark calendarId and eventId to the consumer. id = {}, calendarId = {}, eventId = {}", id, calendarId, eventId);
    }

    /**
     * 同步日程至lark，同时添加参会者
     * @param summary
     * @param description
     * @param startTime
     * @param endTime
     * @param emails
     * @param calendarId
     * @param eventId
     */
    public void updateCalendarEventWithAttendees(Integer minutes, String summary, String description, Instant startTime, Instant endTime, List<String> emails, String calendarId, String eventId) {
        log.info("skip sync to lark: ", skip);
        if (skip) {
            return;
        }
        updateCalendarEvent(calendarId, eventId, summary, description, startTime, endTime, minutes);
        log.info("Successfully updated the Lark calendar event. Updated eventId = {}", eventId);
        List<String> userIdList = getCalendarAttendees(calendarId, eventId);
        log.info("Successfully retrieved the list of existing Lark calendar event attendees. UserIdList size = {}", userIdList.size());
        createCalendarEventAttendee(calendarId, eventId, emails, userIdList);
        log.info("Successfully added or updated attendees for the Lark calendar event. calendarId = {}, eventId = {}", calendarId, eventId);
    }

    private List<String> getCalendarAttendees(String calendarId, String eventId) {
        List<String> list = new ArrayList<>();
        try {
            String url = StrUtil.format(SEARCH_ATTENDEE_ID_LIST_URL + "?user_id_type=user_id&page_size=100", calendarId, eventId);
            log.info("getCalendarAttendees start url = {}", url);
            HttpResponse response = httpService.get(url, getHeaderTokenWithApplicationJson());
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                String result = response.getBody();
                log.info("url = {}, result = {}", SEARCH_ATTENDEE_Id_URL, result);
                JSONObject resultJSON = JSONUtil.parseObj(result);
                JSONObject dataJson = resultJSON.getJSONObject(JSON_DATA_KEY);
                JSONArray itemArray = dataJson.getJSONArray("items");
                itemArray.forEach(obj -> {
                    JSONObject jsonObj = (JSONObject) obj;
                    if (!Objects.equals(jsonObj.getStr("rsvp_status"), "removed")) {
                        list.add(jsonObj.getStr("user_id"));
                    }
                });
                log.info("getCalendarAttendees success, url = {}, result = {}", url, result);
                return list;
            }
            log.error("getCalendarAttendees is fail url = {}, result = {}", url, response.getBody());
        } catch (Exception e) {
            log.error("getCalendarAttendees is error, msg = {}", ExceptionUtils.getStackTrace(e));
        }
        return list;
    }

    public void deleteCalendarEvent(String calendarId, String eventId) {
        String url = StrUtil.format(DELETE_CALENDAR_EVENT_URL, calendarId, eventId);
        try {
            HttpResponse response = httpService.delete(url, getHeaderTokenWithApplicationJson());
            log.info("deleteCalendarEvent url = {}, result = {}", url, response.getBody());
        } catch (Exception e) {
            log.error("deleteCalendarEvent is error, result = {}", ExceptionUtils.getStackTrace(e));
        }
    }

    /**
     * 创建日历接口
     * 入参 eg:{"summary":"测试日历","description":"使用开放接口创建日历","permissions":"private","color":-1,"summary_alias":"日历备注名"}
     * 反参 eg:{"code":0,"data":{"calendar":{"calendar_id":"<EMAIL>","color":-1,"description":"使用开放接口创建日历","permissions":"private","role":"owner","summary":"测试日历","summary_alias":"日历备注名","type":"shared"}},"msg":"success"}
     */
    public String createCalendar() {
        if (StrUtil.isNotBlank(calendarId)) {
            return calendarId;
        }
        String paramJson = "{\"summary\":\"" + Instant.now() + "日历\",\"description\":\"日历描述\",\"permissions\":\"private\",\"color\":-1,\"summary_alias\":\"日历备注名\"}";
        try {
            log.info("createCalendar start, url = {}, param = {}", CREATE_CALENDAR_URL, paramJson);
            HttpResponse response = httpService.post(CREATE_CALENDAR_URL, getHeaderTokenWithApplicationJson(), paramJson);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                JSONObject resultJson = JSONUtil.parseObj(response.getBody());
                JSONObject dataJson = resultJson.getJSONObject(JSON_DATA_KEY);
                JSONObject calendarJson = dataJson.getJSONObject(JSON_CALENDAR_KEY);
                calendarId = calendarJson.getStr(JSON_CALENDAR_ID_KEY);
                log.info("lark get calendarId = {}", calendarId);
                return calendarId;
            }
            log.error("createCalendar is fail url = {}, param = {}, result = {}", CREATE_CALENDAR_URL, paramJson, response.getBody());
        } catch (Exception e) {
            log.error("createCalendar is error, msg = {}", ExceptionUtils.getStackTrace(e));
        }
        throw new RuntimeException("create lark calendar is error");
    }

    /**
     * 日程的创建方法
     * @param summary     标题   最大长度：1000 字符
     * @param description 描述   最大长度：40960 字符
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return
     * 入参:{"summary":"测试日程","start_time":{"timestamp":1695776400},"end_time":{"timestamp":1695777000},"description":"测试日程描述"}
     * 反参:{"code":0,"data":{"event":{"attendee_ability":"none","color":-1,"create_time":"1695724066","description":"测试日程描述","end_time":{"timestamp":"1695777000","timezone":"Asia/Shanghai"},"event_id":"62a05206-719c-4318-b435-d3c85004e736_0","free_busy_status":"busy","is_exception":false,"organizer_calendar_id":"<EMAIL>","recurrence":"","start_time":{"timestamp":"1695776400","timezone":"Asia/Shanghai"},"status":"confirmed","summary":"测试日程","visibility":"default"}},"msg":"success"}
     */
    public String createCalendarEvent(String calendarId, String summary, String description, Instant startTime, Instant endTime, Integer minutes) {
        Map<String, Object> paramMap = getCalendarEventParamMap(summary, description, startTime, endTime, minutes);
        String url = StrUtil.format(CREATE_CALENDAR_EVENT_URL, calendarId);
        String bodyJson = JSONUtil.toJsonStr(paramMap);
        try {
            log.info("createCalendarEvent start, url = {}, param = {}", url, bodyJson);
            HttpResponse response = httpService.post(url, getHeaderTokenWithApplicationJson(), bodyJson);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                String result = response.getBody();
                JSONObject resultJson = JSONUtil.parseObj(result);
                JSONObject dataJson = resultJson.getJSONObject(JSON_DATA_KEY);
                JSONObject eventJson = dataJson.getJSONObject(JSON_CALENDAR_EVENT_KEY);
                log.info("createCalendarEvent success, url = {}, param = {}, result = {}", url, bodyJson, result);
                return eventJson.getStr(JSON_CALENDAR_EVENT_ID_KEY);
            }
            log.error("createCalendarEvent is fail url = {}, param = {}, result = {}", url, bodyJson, response.getBody());
        } catch (Exception e) {
            log.error("createCalendarEvent is error, msg = {}", ExceptionUtils.getStackTrace(e));
        }
        throw new RuntimeException("createCalendarEvent is error");
    }

    /**
     * 日程的修改方法
     * @param summary     标题   最大长度：1000 字符
     * @param description 描述   最大长度：40960 字符
     * @param startTime   开始时间
     * @param endTime     结束时间
     * @return
     * 入参:{"summary":"测试日程","start_time":{"timestamp":1695776400},"end_time":{"timestamp":1695777000},"description":"测试日程描述"}
     * 反参:{"code":0,"data":{"event":{"attendee_ability":"none","color":-1,"create_time":"1695724066","description":"测试日程描述","end_time":{"timestamp":"1695777000","timezone":"Asia/Shanghai"},"event_id":"62a05206-719c-4318-b435-d3c85004e736_0","free_busy_status":"busy","is_exception":false,"organizer_calendar_id":"<EMAIL>","recurrence":"","start_time":{"timestamp":"1695776400","timezone":"Asia/Shanghai"},"status":"confirmed","summary":"测试日程","visibility":"default"}},"msg":"success"}
     */
    public String updateCalendarEvent(String calendarId, String eventId, String summary, String description, Instant startTime, Instant endTime, Integer minutes) {
        Map<String, Object> paramMap = getCalendarEventParamMap(summary, description, startTime, endTime, minutes);
        String url = StrUtil.format(UPDATE_CALENDAR_EVENT_URL, calendarId, eventId);
        String bodyJson = JSONUtil.toJsonStr(paramMap);
        try {
            log.info("updateCalendarEvent start, url = {}, param = {}", url, bodyJson);
            HttpResponse response = httpService.patch(url, getHeaderTokenWithApplicationJson(), bodyJson);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                String result = response.getBody();
                JSONObject resultJson = JSONUtil.parseObj(result);
                JSONObject dataJson = resultJson.getJSONObject(JSON_DATA_KEY);
                JSONObject eventJson = dataJson.getJSONObject(JSON_CALENDAR_EVENT_KEY);
                log.info("updateCalendarEvent success, url = {}, param = {}, result = {}", url, bodyJson, result);
                return eventJson.getStr(JSON_CALENDAR_EVENT_ID_KEY);
            }
            log.error("updateCalendarEvent is fail url = {}, param = {}, result = {}", url, bodyJson, response.getBody());
        } catch (Exception e) {
            log.error("updateCalendarEvent is error, msg = {}", ExceptionUtils.getStackTrace(e));
        }
        throw new RuntimeException("updateCalendarEvent is error");
    }

    private Map<String, Object> getCalendarEventParamMap(String summary, String description, Instant startTime, Instant endTime, Integer minutes) {
        Map<String, Object> paramMap = new HashMap<>( 16);
        paramMap.put("summary", summary);
        paramMap.put("description", description);
        Map<String, Object> startMap = getTimeMap(startTime);
        Map<String, Object> endMap = getTimeMap(endTime);
        List<Map<String, Object>> reminderList = new ArrayList<>();
        //这个参数无任何用,lark的api还不支持
        if (minutes != null) {
            Map<String, Object> reminderMap = new HashMap<>(16);
            reminderMap.put("minutes", minutes);
            reminderList.add(reminderMap);
        }
        paramMap.put("reminders", reminderList);
        paramMap.put("start_time", startMap);
        paramMap.put("end_time", endMap);
        paramMap.put("attendee_ability", "can_modify_event");
        paramMap.put("need_notification", true);
        return paramMap;
    }

    /**
     *
     * @param calendarId
     * @param eventId
     * @param emailList
     * @param existsUserIdList
     * @return
     * 入参：{"attendees":[{"third_party_email":"<EMAIL>","type":"third_party"},{"third_party_email":"<EMAIL>","type":"third_party"}]}
     * 反参：{"code":0,"data":{"attendees":[{"attendee_id":"third_party_7283082312857124870","display_name":"<EMAIL>","is_external":true,"is_organizer":false,"rsvp_status":"needs_action","third_party_email":"<EMAIL>","type":"third_party"},{"attendee_id":"third_party_7283082111740510213","display_name":"<EMAIL>","is_external":true,"is_organizer":false,"rsvp_status":"needs_action","third_party_email":"<EMAIL>","type":"third_party"}]},"msg":"success"}
     */
    public String createCalendarEventAttendee(String calendarId, String eventId, List<String> emailList, List<String> existsUserIdList) {
        Map<String, String> emailUserIdMap = searchLarkUserIdByEmail(emailList);
        Map<String, Object> paramMap = new HashMap<>( 16);
        List<Map<String, Object>> attendeeMap = new ArrayList<>();
        List<Map<String, Object>> deleteMapList = new ArrayList<>();
        emailUserIdMap.forEach((email, larkUserId) -> {
            Map<String, Object> map = new HashMap<>( 16);
            if (StrUtil.isNotBlank(larkUserId)) {
                //lark用户参加会议
                map.put("type", "user");
                map.put("user_id", larkUserId);
            } else {
                //非lark用户参加会议
                map.put("type", "third_party");
                map.put("third_party_email", email);
            }
            attendeeMap.add(map);
        });
        paramMap.put("attendees", attendeeMap);
        paramMap.put("need_notification", true);
        if (CollUtil.isNotEmpty(existsUserIdList)) {
            existsUserIdList.forEach(larkUserId -> {
                if (!emailUserIdMap.containsValue(larkUserId)) {
                    Map<String, Object> map = new HashMap<>( 16);
                    map.put("type", "user");
                    map.put("user_id", larkUserId);
                    deleteMapList.add(map);
                }
            });
        }
        if (CollUtil.isNotEmpty(deleteMapList)) {
            deleteCalendarAttendee(deleteMapList, calendarId, eventId);
        }
        return addAttendee(paramMap, calendarId, eventId);
    }

    private void deleteCalendarAttendee(List<Map<String, Object>> deleteMapList, String calendarId, String eventId) {
        String url = StrUtil.format(DELETE_ATTENDEE_ID_LIST_URL + "?user_id_type=user_id", calendarId, eventId);
        try {
            Map<String, Object> paramMap = new HashMap<>(16);
            paramMap.put("delete_ids", deleteMapList);
            String bodyJson = JSONUtil.toJsonStr(paramMap);
            log.info("deleteCalendarAttendee start url = {}, param = {}", url, paramMap);
            HttpResponse response = httpService.post(url + "?user_id_type=user_id", getHeaderTokenWithApplicationJson(), bodyJson);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                String result = response.getBody();
                log.info("deleteCalendarAttendee url = {}, param = {}, result = {}", url, bodyJson, result);
                return;
            }
            log.error("deleteCalendarAttendee is fail url = {}, param = {}, result = {}", url, bodyJson, response.getBody());
        } catch (Exception e) {
            log.error("deleteCalendarAttendee is error, msg = {}", ExceptionUtils.getStackTrace(e));
        }
        throw new RuntimeException("deleteCalendarAttendee is error");
    }

    private String addAttendee(Map<String, Object> paramMap, String calendarId, String eventId) {
        String url = StrUtil.format(CREATE_CALENDAR_EVENT_ATTENDEE_URL, calendarId, eventId);
        String bodyJson = JSONUtil.toJsonStr(paramMap);
        try {
            log.info("createCalendarEventAttendee start url = {}, param = {}", url, bodyJson);
            HttpResponse response = httpService.post(url + "?user_id_type=user_id", getHeaderTokenWithApplicationJson(), bodyJson);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                String result = response.getBody();
                log.info("createCalendarEventAttendee url = {}, param = {}, result = {}", url, bodyJson, result);
                return result;
            }
            log.error("createCalendarEventAttendee is fail url = {}, param = {}, result = {}", url, bodyJson, response.getBody());
        } catch (Exception e) {
            log.error("createCalendarEventAttendee is error, msg = {}", ExceptionUtils.getStackTrace(e));
        }
        throw new RuntimeException("createCalendarEventAttendee is error");
    }

    /**
     * lark反参：{"code":0,"data":{"user_list":[{"email":"<EMAIL>","user_id":"5331afca"},{"email":"<EMAIL>","user_id":"13937b92"},{"email":"<EMAIL>","user_id":"a265bc5a"}]},"msg":"success"}
     * @param emails
     * @return Map k:email, v:larkUserId (只有存在 larkUserId 才能同步至lark日程，否则只能以第三方的email来参加会议)
     */
    public Map<String, String> searchLarkUserIdByEmail(List<String> emails) {
        Map<String, Object> paramMap = new HashMap<>(16);
        paramMap.put("emails", emails);
        String bodyJson = JSONUtil.toJsonStr(paramMap);
        String url = SEARCH_ATTENDEE_Id_URL + "?user_id_type=user_id";
        try {
            log.info("searchLarkUserIdByEmail start url = {}, param = {}", url, bodyJson);
            HttpResponse response = httpService.post(url, getHeaderTokenWithApplicationJson(), bodyJson);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                String result = response.getBody();
                log.info("url = {}, param = {}, result = {}", SEARCH_ATTENDEE_Id_URL, bodyJson, result);
                JSONObject resultJson = JSONUtil.parseObj(result);
                JSONObject dataJson = resultJson.getJSONObject(JSON_DATA_KEY);
                JSONArray userArray = dataJson.getJSONArray(JSON_SEARCH_USER_ID_LIST_KEY);
                Map<String, String> fieldToValueMap = new HashMap<>(16);
                userArray.forEach(obj -> {
                    JSONObject jsonObj = (JSONObject) obj;
                    fieldToValueMap.put(jsonObj.getStr("email"), jsonObj.getStr("user_id"));
                });
                return fieldToValueMap;
            }
            log.error("searchLarkUserIdByEmail is fail url = {}, param = {}, result = {}", url, bodyJson, response.getBody());
        } catch (Exception e) {
            log.error("searchLarkUserIdByEmail is error, msg = {}", ExceptionUtils.getStackTrace(e));
        }
        throw new RuntimeException("searchLarkUserIdByEmail is error");
    }

    public String uploadImages(byte[] chartByte) {
        //固定值
        String boundary = "---7MA4YWxkTrZu0gW";
        MultipartBody requestBody = new MultipartBody.Builder(boundary)
                .setType(Objects.requireNonNull(MediaType.parse("multipart/form-data; boundary=" + boundary)))
                .addFormDataPart("image_type", "message")
                .addFormDataPart("image", "filename.png",
                        RequestBody.create(MediaType.parse("application/octet-stream"), chartByte))
                .build();
        try {
            log.info("lark upload images is start");
            HttpResponse response = httpService.post(UPLOAD_IMAGE_URL, getHeaderTokenWithApplicationJson(), requestBody);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                String result = response.getBody();
                log.info("lark upload images success , url = {}, result = {}", UPLOAD_IMAGE_URL, result);
                JSONObject resultJson = JSONUtil.parseObj(result);
                JSONObject dataJson = resultJson.getJSONObject(JSON_DATA_KEY);
                return dataJson.getStr(IMAGE_KEY, null);
            }
            log.error("lark upload images is fail url = {}, result = {}", UPLOAD_IMAGE_URL, response.getBody());
        } catch (Exception e) {
            log.error("lark upload images is fail url = {}, result = {}", UPLOAD_IMAGE_URL, ExceptionUtil.getAllExceptionMsg(e));
        }
        throw new RuntimeException("lark upload images is error");
    }

    public void sendFeedCardUrl(String imageKey, String larkUserId, String linkUrl, String subscriptionName, String dataRange, String unsubscribeUrl) {
        String json = """
                {
                    "user_id": "{larkUserId}",
                    "msg_type": "interactive",
                    "card": {
                        "config": {
                            "wide_screen_mode": true
                        },
                        "elements": [
                            {
                                "tag": "markdown",
                                "content": "{dataRange}"
                            },
                            {
                                "alt": {
                                    "content": "",
                                    "tag": "plain_text"
                                },
                                "img_key": "{imageKey}",
                                "tag": "img"
                            },
                            {
                                "actions": [
                                    {
                                        "tag": "button",
                                        "text": {
                                            "content": "View Report",
                                            "tag": "plain_text"
                                        },
                                        "type": "primary",
                                        "multi_url": {
                                            "url": "{linkUrl}",
                                            "pc_url": "{linkUrl}",
                                            "android_url": "{linkUrl}",
                                            "ios_url": "{linkUrl}"
                                        }
                                    },
                                    {
                                        "tag": "button",
                                        "text": {
                                            "content": "Unsubscribe",
                                            "tag": "plain_text"
                                        },
                                        "type": "default",
                                        "multi_url": {
                                            "url": "{unsubscribeUrl}",
                                            "pc_url": "{unsubscribeUrl}",
                                            "android_url": "{unsubscribeUrl}",
                                            "ios_url": "{unsubscribeUrl}"
                                        }
                                    }
                                ],
                                "tag": "action"
                            }
                        ],
                        "header": {
                            "template": "turquoise",
                            "title": {
                                "content": "{subscriptionName}",
                                "tag": "plain_text"
                            }
                        }
                    }
                }
                """;
        Map<String, String> map = new HashMap<>(16);
        map.put("imageKey", imageKey);
        map.put("linkUrl", linkUrl);
        map.put("unsubscribeUrl", unsubscribeUrl);
        map.put("larkUserId", larkUserId);
        map.put("subscriptionName", subscriptionName);
        if (StringUtils.isBlank(dataRange)) {
            map.put("dataRange", "");
        } else {
            map.put("dataRange", "Data Range: " + dataRange);
        }
        json = StrUtil.format(json, map);
        try {
            log.info("sendFeedCardUrl is start url = {}", SEND_FEED_CARD_URL);
            HttpResponse response = httpService.post(SEND_FEED_CARD_URL, getHeaderTokenWithApplicationJson(), json);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                log.info("sendFeedCardUrl is success, url = {}, result = {}", SEND_FEED_CARD_URL, response.getBody());
                return;
            }
            log.error("sendFeedCardUrl is fail url = {}, result = {}", SEND_FEED_CARD_URL, response.getBody());
        } catch (Exception e) {
            log.error("lark upload images is fail url = {}, result = {}", SEND_FEED_CARD_URL, ExceptionUtil.getAllExceptionMsg(e));
        }
        throw new RuntimeException("sendFeedCardUrl is error");
    }

    public void noPoachingSendMessageByUserEmails(NoPoachingRemindDTO remindDTO) {
        //如果没有收件人邮箱 或者 没有发送URL 则直接返回
        if (CollUtil.isEmpty(remindDTO.getEmails()) || Objects.isNull(remindDTO.getUrl())) {
            return;
        }
        log.info("noPoaching before sendFeedCardUrl, need send emails = {}", JSONUtil.toJsonStr(remindDTO.getEmails()));
        //1.组装webhook需要的key
        remindDTO.getEmails().forEach(email -> {
            JSONObject json = new JSONObject();
            json.put("email", email);
            json.put("remindType", remindDTO.getRemindType());
            json.put("companyId", remindDTO.getCompanyId());
            json.put("companyName", remindDTO.getCompanyName());
            json.put("talentId", remindDTO.getTalentId());
            json.put("talentFullName", remindDTO.getTalentFullName());
            json.put("jobId", remindDTO.getJobId());
            json.put("jobTitle", remindDTO.getJobTitle());
            json.put("submitUserFullName", remindDTO.getSubmitUserFullName());
            json.put("talentDetailLink", remindDTO.getTalentDetailLink());
            try {
                log.info("noPoaching noPoachingSendMessageByUserEmails start, url = {}, json ={}", remindDTO.getUrl(), JSONUtil.toJsonStr(json));
                HttpResponse response = httpService.post(remindDTO.getUrl(), json.toString());
                if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                    log.info("noPoaching noPoachingSendMessageByUserEmails is success, url = {}, result = {}", remindDTO.getUrl(), response.getBody());
                } else {
                    log.error("noPoaching noPoachingSendMessageByUserEmails is fail url = {}, result = {}", remindDTO.getUrl(), response.getBody());
                }
            } catch (Exception e) {
                log.error("noPoaching noPoachingSendMessageByUserEmails is fail url = {}, result = {}", remindDTO.getUrl(), ExceptionUtil.getAllExceptionMsg(e));
            }
        });
    }

    /**
     * 获取tenant tenant_access_token
     * @return {"code":0,"expire":7200,"msg":"ok","tenant_access_token":"******************************************"}
     */
    public String getTenantAccessToken() {
        String tenantAccessToken = redisService.get(REDIS_LARK_TOKEN_KEY);
        if (StrUtil.isNotBlank(tenantAccessToken)) {
            return tenantAccessToken;
        }
        String paramJson = JSONUtil.toJsonStr(getTenantAccessTokenParam());
        try {
            log.info("getTenantAccessToken start url = {}, param = {}", TENANT_ACCESS_TOKEN_URL, paramJson);
            HttpResponse response = httpService.post(TENANT_ACCESS_TOKEN_URL, paramJson);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                String result = response.getBody();
                JSONObject tokenJson = JSONUtil.parseObj(result);
                tenantAccessToken = tokenJson.getStr(TENANT_ACCESS_TOKEN_KEY);
                Integer expire = tokenJson.getInt(TENANT_ACCESS_TOKEN_EXPIRE_KEY);
                //设置 token 保存时间比过期时间少60s
                redisService.set(REDIS_LARK_TOKEN_KEY, tenantAccessToken, expire - 60);
                log.info("time {} get lark token = {}", Instant.now(), tenantAccessToken);
                return tenantAccessToken;
            }
            log.error("getTenantAccessToken is fail url = {}, param = {}, result = {}", TENANT_ACCESS_TOKEN_URL, paramJson, response.getBody());
        } catch (Exception e) {
            log.error("getTenantAccessToken is error, msg = {}", ExceptionUtils.getStackTrace(e));
        }
        throw new RuntimeException("getTenantAccessToken is error");
    }

    /**
     * 获取tenantAccessToken 的入参
     * @return
     */
    public Map<String, Object> getTenantAccessTokenParam() {
        Map<String, Object> paramMap = new HashMap<>( 16);
        paramMap.put("app_id", appId);
        paramMap.put("app_secret", appSecret);
        return paramMap;
    }

    private Headers getHeaderTokenWithApplicationJson() {
        Map<String, String> paramMap = new HashMap<>( 16);
        paramMap.put("Authorization", TOKEN_TYPE + getTenantAccessToken());
        paramMap.put("Content-Type", "application/json; charset=utf-8");
        return Headers.of(paramMap);
    }

    private Map<String, Object> getTimeMap(Instant time) {
        Map<String, Object> map = new HashMap<>( 16);
        map.put("timestamp", time.toEpochMilli() / 1000);
        return map;
    }

    public String getUserAccessToken(Long userId) {
        String userAccessToken = redisService.get(REDIS_LARK_USER_TOKEN_KEY + userId);

        if (!JSONUtil.isJson(userAccessToken)) {
            return null;
        }

        LarkUserTokenDTO dto = JSONUtil.toBean(userAccessToken, LarkUserTokenDTO.class);
        boolean isExpired = Instant.now()
                .isAfter(dto.getFetchedAt().plusSeconds(dto.getExpires_in()));
        if (!isExpired) {
            return dto.getAccess_token();
        }

        //refresh token logic here
        return null;
    }

    public String getUserAccessTokenByUserAuthorizationCode(Long userId, String authorizationCode) {

        String paramJson = JSONUtil.toJsonStr(getUserAccessTokenParam(authorizationCode));
        try {
            log.info("getUserAccessToken for user:{} start, url = {}, param = {}", userId, USER_ACCESS_TOKEN_URL, paramJson);
            HttpResponse response = httpService.post(USER_ACCESS_TOKEN_URL, paramJson);
            if (Objects.equals(response.getCode(), SUCCESS_CODE)) {
                String result = response.getBody();

                LarkUserTokenDTO dto = JSONUtil.toBean(result, LarkUserTokenDTO.class);
                dto.setFetchedAt(Instant.now().minusSeconds(10)); //设置为10s前，避免网络传输耗时导致token实际已过期

                Integer refreshTokenExpire = dto.getRefresh_token_expires_in();

                String jsonStr = JSONUtil.toJsonStr(dto);
                //设置 token 保存时间比过期时间少60s
                redisService.set(REDIS_LARK_USER_TOKEN_KEY + userId, jsonStr, refreshTokenExpire - 60);
                log.info("time {} get lark token = {}", Instant.now(), jsonStr);
                return dto.getAccess_token();
            }
            log.error("getTenantAccessToken is fail url = {}, param = {}, result = {}", TENANT_ACCESS_TOKEN_URL, paramJson, response.getBody());
        } catch (Exception e) {
            log.error("getTenantAccessToken is error, msg = {}", ExceptionUtils.getStackTrace(e));
        }
        throw new RuntimeException("getTenantAccessToken is error");
    }

    public Map<String, Object> getUserAccessTokenParam(String authorizationCode) {
        Map<String, Object> paramMap = new HashMap<>( 16);
        paramMap.put("grant_type", "authorization_code");
        paramMap.put("client_id", appId);
        paramMap.put("client_secret", appSecret);
        paramMap.put("code", authorizationCode);
        paramMap.put("redirect_uri", "");
        return paramMap;
    }

}
