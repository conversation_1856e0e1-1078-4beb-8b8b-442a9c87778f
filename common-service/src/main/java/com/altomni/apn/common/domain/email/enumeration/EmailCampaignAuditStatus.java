package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum EmailCampaignAuditStatus implements ConvertedEnum<Integer> {

    NOT_APPROVED(0),
    PENDING(1),
    AUDIT_FAILLED(2),
    AUDIT_SUCCEEDED(3),
    AUDIT_FREE(9);

    private final int dbValue;

    EmailCampaignAuditStatus(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<EmailCampaignAuditStatus, Integer> resolver = new ReverseEnumResolver<>(EmailCampaignAuditStatus.class, EmailCampaignAuditStatus::toDbValue);

    public static EmailCampaignAuditStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
