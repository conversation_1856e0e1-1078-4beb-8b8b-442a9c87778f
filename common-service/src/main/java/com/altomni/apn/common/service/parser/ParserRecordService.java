package com.altomni.apn.common.service.parser;

import com.altomni.apn.common.domain.parser.ParseRecord;
import com.altomni.apn.common.service.parser.dto.ParseRecordListDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ParserRecordService {

    ParseRecord save(ParseRecord parseRecord);

    ParseRecord findOne(Long id);

    List<ParseRecord> findAll();

    Page<ParseRecordListDTO> findAllByCreatedByAndIsReviewedOrderByIdDesc(Pageable pageable);

    void delete(Long id);
}
