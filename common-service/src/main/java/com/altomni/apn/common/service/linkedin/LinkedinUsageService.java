package com.altomni.apn.common.service.linkedin;

import com.altomni.apn.common.dto.linkedin.LinkedinUsageDTO;
import com.altomni.apn.common.dto.linkedin.LinkedinUsageSearchDTO;

import java.util.List;

public interface LinkedinUsageService {

    LinkedinUsageDTO searchLinkedinUsageReport(LinkedinUsageSearchDTO linkedinUsageSearchDTO, List<Long> teamIds, List<Long> userIds, Long tenantId, Long userId);

}
