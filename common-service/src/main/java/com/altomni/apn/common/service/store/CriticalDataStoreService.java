package com.altomni.apn.common.service.store;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;

@Slf4j
@Service
public class CriticalDataStoreService {

//    private final static String S3_SERVICE = "s3";
//
//    @Resource
//    private ApplicationProperties applicationProperties;

    private S3Client initS3(String accessKey, String secretKey, String region) {
        return S3Client.builder()
                .credentialsProvider(() -> AwsBasicCredentials.create(accessKey, secretKey))
                .region(Region.of(region))
                .build();
    }

//    private String s3Upload(InputStream inputStream, String bucketName, String key, long contentLength,
//                            String contentDisposition, String contentType) {
//        S3Client s3Client = initS3(applicationProperties.getAccessKey(), applicationProperties.getSecretKey(), applicationProperties.getRegion());
//        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
//                .contentType(contentType)
//                .contentDisposition(contentDisposition)
//                .contentLength(contentLength)
//                .build();
////        meta.setExpirationTime(null);
//        s3Client.putObject(putObjectRequest, RequestBody.fromInputStream(inputStream, contentLength));
//        return "";
//    }

//    public String getPresignedContractFromS3(String key) {
//        AmazonS3 criticalData = initS3(applicationProperties.getAccessKey(), applicationProperties.getSecretKey(), applicationProperties.getRegion());
//        try {
//            // Set the presigned URL to expire after one minute.
//            java.util.Date expiration = new java.util.Date();
//            long expTimeMillis = expiration.getTime();
//            expTimeMillis += 1000 * 60; //one minute
//            expiration.setTime(expTimeMillis);
//
//            // Generate the presigned URL.
//            GeneratePresignedUrlRequest generatePresignedUrlRequest =
//                    new GeneratePresignedUrlRequest(applicationProperties.getContractBucket(), key)
//                            .withMethod(HttpMethod.GET)
//                            .withExpiration(expiration);
//            URL url = criticalData.generatePresignedUrl(generatePresignedUrlRequest);
//
//            return url.toString();
//        } catch (AmazonServiceException e) {
//            // The call was transmitted successfully, but Amazon S3 couldn't process
//            // it, so it returned an error response.
//            log.error("error", e);
//            throw new CustomParameterizedException("Internal Server Error");
//        } catch (SdkClientException e) {
//            // Amazon S3 couldn't be contacted for a response, or the client
//            // couldn't parse the response from Amazon S3.
//            log.error("error", e);
//            throw new CustomParameterizedException("Internal Server Error");
//        }
//    }

//    public String getPresignedUploadUrl() {
//        AmazonS3 criticalData = initS3(applicationProperties.getAccessKey(), applicationProperties.getSecretKey(), applicationProperties.getRegion());
//        try {
//            // Set the pre-signed URL to expire after one hour.
//            java.util.Date expiration = new java.util.Date();
//            long expTimeMillis = expiration.getTime();
//            expTimeMillis += 1000 * 60 * 60; // 60 min
//            expiration.setTime(expTimeMillis);
//
//            // Generate the pre-signed URL.
//            System.out.println("Generating pre-signed URL.");
//            GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(applicationProperties.getContractBucket(), UUID.randomUUID().toString())
//                    .withMethod(HttpMethod.PUT)
//                    .withExpiration(expiration);
//            URL url = criticalData.generatePresignedUrl(generatePresignedUrlRequest);
//            return url.toString();
//        } catch (AmazonServiceException e) {
//            // The call was transmitted successfully, but Amazon S3 couldn't process
//            // it, so it returned an error response.
//            log.error("error", e);
//            throw new CustomParameterizedException("Internal Server Error");
//        } catch (SdkClientException e) {
//            // Amazon S3 couldn't be contacted for a response, or the client
//            // couldn't parse the response from Amazon S3.
//            log.error("error", e);
//            throw new CustomParameterizedException("Internal Server Error");
//        }
//    }

}
