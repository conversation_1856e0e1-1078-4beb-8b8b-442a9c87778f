package com.altomni.apn.common.service.email.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
public class NameAndDescription implements Serializable {

    @NotBlank
    private String name;

    private String description;

    public NameAndDescription() {}

    public NameAndDescription(String name, String description) {
        this.name = name;
        this.description = description;
    }

}
