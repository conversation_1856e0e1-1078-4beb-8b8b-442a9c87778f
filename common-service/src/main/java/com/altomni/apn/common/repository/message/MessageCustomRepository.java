package com.altomni.apn.common.repository.message;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.enumeration.message.MessageDeleteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageFavoriteEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageStatusEnum;
import com.altomni.apn.common.domain.enumeration.message.MessageTypeEnum;
import com.altomni.apn.common.dto.message.MessageSearchPageDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.message.MessageVO;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Repository
public class MessageCustomRepository {

    @PersistenceContext
    private EntityManager entityManager;

    public Map<String, Integer> messageStatistics() {
        Map<String, Integer> resultMap = new HashMap<>(16);
        // 数据库 tinyint 字段这个时候返回的是boolean类型,只有这样转成数字类型
        String sql = " SELECT m.type * 1, mur.is_read, mur.is_favorite, count(1) " +
                " FROM message m " +
                " inner join message_user_relation mur on mur.message_id = m.id " +
                " where mur.user_id = :userId and mur.is_delete = " + MessageDeleteEnum.NOT_DELETED.toDbValue() +
                " group by m.type, mur.is_read, mur.is_favorite";
        List<Object[]> objects = entityManager.createNativeQuery(sql)
                .setParameter("userId", SecurityUtils.getUserId())
                .getResultList();
        if (CollUtil.isEmpty(objects)) {
            return resultMap;
        }
        String favoriteName = "FAVORITE";
        String unReaderName = "UNREADER";
        objects.forEach(objectArray -> {
            Integer type = getIntegerFromBit(objectArray[0]);
            MessageTypeEnum messageTypeEnum = MessageTypeEnum.fromDbValue(type);
            Integer reader = getIntegerFromBit(objectArray[1]);
            Integer favorite = getIntegerFromBit(objectArray[2]);
            Integer count = Integer.parseInt(String.valueOf(objectArray[3]));
            Integer oldCount = resultMap.get(messageTypeEnum.name());
            //不同类型的数量
            resultMap.put(messageTypeEnum.name(), ObjectUtil.isEmpty(oldCount)? count : oldCount + count);
            if (Objects.equals(MessageFavoriteEnum.FAVORITE.toDbValue(), favorite)) {
                //收藏的数量
                Integer oldFavoriteNum = ObjectUtil.isEmpty(resultMap.get(favoriteName))? 0: resultMap.get(favoriteName);
                resultMap.put(favoriteName, oldFavoriteNum + count);
            }
            if (Objects.equals(MessageStatusEnum.UNREAD.toDbValue(), reader)) {
                //未读的数量
                Integer oldUnReadNum = ObjectUtil.isEmpty(resultMap.get(unReaderName))? 0: resultMap.get(unReaderName);
                resultMap.put(unReaderName, oldUnReadNum + count);
            }
        });
        return resultMap;
    }

    public Integer getIntegerFromBit(Object o) {
        if (Boolean.FALSE.equals(o)) {
            return 0;
        } else if (Boolean.TRUE.equals(o)){
            return 1;
        } else {
            return Integer.parseInt(String.valueOf(o));
        }
    }

    public List<MessageVO> searchMessageList(MessageSearchPageDTO messageSearchPageDTO) {
        String sql = "SELECT m.id, m.cn_title, m.en_title, m.content, mur.is_read, mur.is_favorite, mur.created_date " +
                " FROM message m " +
                " inner join message_user_relation mur on mur.message_id = m.id  " +
                " where mur.user_id = :userId and mur.is_delete = " + MessageDeleteEnum.NOT_DELETED.toDbValue();
        if (MessageTypeEnum.FAVORITE == messageSearchPageDTO.getType() || messageSearchPageDTO.getIsFavorite() == MessageFavoriteEnum.FAVORITE) {
            sql = sql + " and mur.is_favorite = " + MessageFavoriteEnum.FAVORITE.toDbValue();
        } else {
            sql = sql + " and m.type = " + messageSearchPageDTO.getType().toDbValue();
        }
        if (messageSearchPageDTO.getIsFavorite() == MessageFavoriteEnum.NOT_FAVORITE) {
            sql = sql + " and mur.is_favorite = " + MessageFavoriteEnum.NOT_FAVORITE.toDbValue();
        }
        sql = sql + " order by mur.created_date desc limit :startOffset , :size ";
        int startOffset = (messageSearchPageDTO.getPage() - 1) * messageSearchPageDTO.getSize();
        List<MessageVO> voList = entityManager.createNativeQuery(sql, MessageVO.class)
                .setParameter("userId", SecurityUtils.getUserId())
                .setParameter("startOffset", startOffset)
                .setParameter("size", messageSearchPageDTO.getSize()).getResultList();
        return voList;
    }

    public Long countMessage(MessageSearchPageDTO messageSearchPageDTO) {
        String sql = "SELECT count(distinct m.id) " +
                " FROM message m " +
                " inner join message_user_relation mur on mur.message_id = m.id  " +
                " where mur.user_id = :userId and mur.is_delete = " + MessageDeleteEnum.NOT_DELETED.toDbValue();
        if (MessageTypeEnum.FAVORITE == messageSearchPageDTO.getType()) {
            sql = sql + " and mur.is_favorite = " + MessageFavoriteEnum.FAVORITE.toDbValue();
        } else {
            sql = sql + " and m.type = " + messageSearchPageDTO.getType().toDbValue();
        }
        if (messageSearchPageDTO.getIsFavorite() == MessageFavoriteEnum.NOT_FAVORITE) {
            sql = sql + " and mur.is_favorite = " + MessageFavoriteEnum.NOT_FAVORITE.toDbValue();
        }
        Query query = entityManager.createNativeQuery(sql).setParameter("userId", SecurityUtils.getUserId());
        return Long.parseLong(String.valueOf(query.getSingleResult()));
    }


}
