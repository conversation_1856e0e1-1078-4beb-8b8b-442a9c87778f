package com.altomni.apn.common.domain.email;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignAuditResult;
import com.altomni.apn.common.utils.ServiceUtils;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "email_campaign_audit_record")
public class EmailCampaignAuditRecord extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "email_campaign_id")
    private Long emailCampaignId;

    @Column(name = "name")
    private String name;

    @Column(name = "job_id_list")
    private String jobIdList;

    @Column(name = "email_subject")
    private String emailSubject;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "sender_email")
    private String senderEmail;

    @Column(name = "sender_name")
    private String senderName;

    @Column(name = "default_reply_email")
    private String defaultReplyEmail;

    @Column(name = "group_id")
    private Long groupId;

    @Column(name = "template_id")
    private Long templateId;

    @Column(name = "template_html_content")
    private String templateHtmlContent;

    @Column(name = "estimated_cost")
    private int estimatedCost;

    @Column(name = "reason")
    private String reason;

    @Column(name = "audit_result")
    private EmailCampaignAuditResult auditResult;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEmailCampaignId() {
        return emailCampaignId;
    }

    public void setEmailCampaignId(Long emailCampaignId) {
        this.emailCampaignId = emailCampaignId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getJobIdList() {
        return jobIdList;
    }

    public void setJobIdList(String jobIdList) {
        this.jobIdList = jobIdList;
    }

    public String getEmailSubject() {
        return emailSubject;
    }

    public void setEmailSubject(String emailSubject) {
        this.emailSubject = emailSubject;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getSenderEmail() {
        return senderEmail;
    }

    public void setSenderEmail(String senderEmail) {
        this.senderEmail = senderEmail;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getDefaultReplyEmail() {
        return defaultReplyEmail;
    }

    public void setDefaultReplyEmail(String defaultReplyEmail) {
        this.defaultReplyEmail = defaultReplyEmail;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public String getTemplateHtmlContent() {
        return templateHtmlContent;
    }

    public void setTemplateHtmlContent(String templateHtmlContent) {
        this.templateHtmlContent = templateHtmlContent;
    }

    public int getEstimatedCost() {
        return estimatedCost;
    }

    public void setEstimatedCost(int estimatedCost) {
        this.estimatedCost = estimatedCost;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public EmailCampaignAuditResult getAuditResult() {
        return auditResult;
    }

    public void setAuditResult(EmailCampaignAuditResult auditResult) {
        this.auditResult = auditResult;
    }

    public static EmailCampaignAuditRecord fromEmailCampaign(EmailCampaign emailCampaign) {
        EmailCampaignAuditRecord emailCampaignAuditRecord = new EmailCampaignAuditRecord();
        ServiceUtils.myCopyProperties(emailCampaign, emailCampaignAuditRecord);
        emailCampaignAuditRecord.setEmailCampaignId(emailCampaign.getId());
        return emailCampaignAuditRecord;
    }
}
