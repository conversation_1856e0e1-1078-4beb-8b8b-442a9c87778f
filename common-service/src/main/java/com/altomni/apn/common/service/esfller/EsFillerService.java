package com.altomni.apn.common.service.esfller;

import com.altomni.apn.common.dto.searcheamil.SearchEmailByNameDTO;

public interface EsFillerService {

    /**
     * 根据名称从talent和user中搜索email
     * @param searchEmailByNameDTO
     * @return es 结果透传
     */
    String searchEmailFromTalentAndUserByName(SearchEmailByNameDTO searchEmailByNameDTO);

    String searchEmailFromTalentAndUserByNames(SearchEmailByNameDTO searchEmailByNameDTO);

    String getCompanyInfoByName(String name);
}
