package com.altomni.apn.common.service.email.dto;

import lombok.Data;

@Data
public class HtmlContentFront {

    public String label;

    public String value;

    public String url;

    public boolean defaultFront;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public boolean isDefaultFront() {
        return defaultFront;
    }

    public void setDefaultFront(boolean defaultFront) {
        this.defaultFront = defaultFront;
    }
}
