package com.altomni.apn.common.utils;

import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;

import java.util.ArrayList;
import java.util.List;

public final class PageableConvertUtil {

    private PageableConvertUtil() {
    }

    private final static String PAGE = "page=";

    private final static String SIZE = "size=";

    private final static String SORT = "&sort=";

    private final static String AND = "&";

    public static String convertPageableToString(Pageable pageable) {
        List<String> sorts = new ArrayList<>();
        pageable.getSort().forEach(order -> sorts.add(String.join(",", order.getProperty(), order.getDirection().toString())));
//        System.out.println(Arrays.toString(sorts.toArray()));

        String temp = StringUtils.join(sorts, SORT);
        String res = PAGE + pageable.getPageNumber() + AND + SIZE + pageable.getPageSize();
        if (StringUtils.isNotBlank(temp)) {
            res += SORT + temp;
        }
        return res;
    }
}