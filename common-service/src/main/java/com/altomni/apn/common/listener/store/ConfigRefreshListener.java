package com.altomni.apn.common.listener.store;

import com.altomni.apn.common.config.env.store.UploadInfoConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ConfigRefreshListener implements ApplicationListener<EnvironmentChangeEvent> {

    @Override
    public void onApplicationEvent(EnvironmentChangeEvent event) {
        // 配置发生变化重新获取 region 信息
        UploadInfoConfig.bucketRegionMap.clear();
        UploadInfoConfig.bucketIsPublicMap.clear();
        log.info("nacos changed, map clear");
    }

}
