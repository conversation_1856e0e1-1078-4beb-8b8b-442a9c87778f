package com.altomni.apn.common.repository.upgrade;

import com.altomni.apn.common.domain.upgrade.UpgradeUserRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UpgradeUserRelationRepository extends JpaRepository<UpgradeUserRelation, Long> {

    List<UpgradeUserRelation> findAllByUpgradeIdAndUserId(Long upgradeId, Long userId);
}
