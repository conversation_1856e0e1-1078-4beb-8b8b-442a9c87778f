package com.altomni.apn.common.repository.message;

import com.altomni.apn.common.domain.message.MessageUserRelation;
import com.altomni.apn.user.domain.validators.TwoFieldsExist;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
public interface MessageUserRelationRepository extends JpaRepository<MessageUserRelation, Long> {

    @Modifying
    @Transactional
    @Query(value = "update message_user_relation mur inner join message m on m.id = mur.message_id " +
            " set mur.is_delete = ?4 where m.type = ?1 and m.tenant_id = ?3 and mur.user_id = ?2 and mur.is_favorite = 0 ", nativeQuery = true)
    void deleteByTypeAndUserIdAndTenantId(Integer type, Long userId, Long tenantId, Integer deleteStatus);

    @Modifying
    @Transactional
    @Query(value = "update message_user_relation mur inner join message m on m.id = mur.message_id " +
            " set mur.is_favorite = 0 where m.tenant_id = ?2 and mur.user_id = ?1 and mur.is_favorite = 1 ", nativeQuery = true)
    void deleteByUserIdAndTenantId(Long userId, Long tenantId);

    @Modifying
    @Transactional
    @Query(value = " update message_user_relation set is_read = ?3 where message_id = ?1 and user_id = ?2 ", nativeQuery = true)
    void updateReaderStatusByUserIdAndMessageId(Long messageId, Long userId, Integer readStatus);

    @Modifying
    @Transactional
    @Query(value = " update message_user_relation set is_favorite = ?3 where message_id = ?1 and user_id = ?2 ", nativeQuery = true)
    void updateFavoriteStatusByUserIdAndMessageId(Long messageId, Long userId, Integer favoriteStatus);

}