package com.altomni.apn.common.repository.settings;

import com.altomni.apn.common.domain.settings.SettingTrackRecordRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface SettingTrackRecordRelationRepository extends JpaRepository<SettingTrackRecordRelation, BigInteger> {

    void deleteSettingTrackRecordRelationByTempId(BigInteger tempId);

    List<SettingTrackRecordRelation> findSettingTrackRecordRelationByTempId(BigInteger tempId);
}
