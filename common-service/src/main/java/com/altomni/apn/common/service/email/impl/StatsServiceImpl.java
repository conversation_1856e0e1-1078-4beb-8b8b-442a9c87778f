package com.altomni.apn.common.service.email.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.config.env.email.EmailAppProperties;
import com.altomni.apn.common.domain.email.enumeration.Metric;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.email.EmailConstans;
import com.altomni.apn.common.service.email.StatsService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.ipg.resourceserver.client.ClientTokenHolder;
import okhttp3.Headers;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Service
public class StatsServiceImpl implements StatsService {

    private final Logger log = LoggerFactory.getLogger(StatsServiceImpl.class);

    @Resource
    private EmailAppProperties emailAppProperties;

    @Resource
    private HttpService httpService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

//    private final AsyncRecordRepository asyncRecordRepository;

    private final static String HEADERS_AUTHORIZATION_KEY = "Authorization";

    private final static String URL_SEPATATOR = "/";

    private final static String SPACE = " ";

    private final static String URL_SEARCH_PAGE = "page=";

    private final static String URL_SEARCH_SIZE = "size=";

    private final static String URL_SEARCH_SORT = "sort=";

    private final static String URL_SEARCH_QUESTION_MARK = "?";

    private final static String URL_SEARCH_AND = "&";

    private final static String EMAILY_STATS_BASE_URL = "/api/v4/stats";

    private final static String BY_EXTERNAL_USER = "/by-external-user";

    private final static String URL_CAMPAIGNS = "/campaigns";

    private final static String URL_DETAILS = "/details";

    private final static String URL_SEARCH = "search=";

    private final static String URL_METRIC = "metric=";

    private final static String URL_FROM = "from=";

    private final static String URL_TO = "to=";


    private String baseUrl() { return emailAppProperties.getEmailyBaseUrl(); }

    private String getStatsUrl(Long from, Long to) {
        return baseUrl() + EMAILY_STATS_BASE_URL + BY_EXTERNAL_USER + URL_SEARCH_QUESTION_MARK + URL_FROM + from.toString() + URL_SEARCH_AND + URL_TO + to.toString();
    }

    private String getCampaignStatsUrl(String search, Pageable pageable) {
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        String sort = "";
        if (!Objects.isNull(order)) {
            sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
        }
        return baseUrl() + EMAILY_STATS_BASE_URL + URL_CAMPAIGNS + BY_EXTERNAL_USER + URL_SEARCH_QUESTION_MARK + URL_SEARCH_PAGE + pageable.getPageNumber()
                + URL_SEARCH_AND + URL_SEARCH_SIZE + pageable.getPageSize() + URL_SEARCH_AND + URL_SEARCH_SORT + sort + (ObjectUtil.isNotEmpty(search) ? (URL_SEARCH_AND + URL_SEARCH + search) : "");
    }

    private String getCampaignStatsDetailsUrl(Long id, Metric metric) {
        return baseUrl() + EMAILY_STATS_BASE_URL + URL_DETAILS + URL_SEPATATOR + id + URL_SEARCH_QUESTION_MARK + URL_METRIC + metric.toString();
    }

    @Override
    public HttpResponse getCampaignStatsByTimeRange(Long from, Long to) throws IOException {
        if (ObjectUtil.isEmpty(from) || ObjectUtil.isEmpty(to)) {
            log.error("[EmailService: StatsServiceImpl] get campaigns stats by time range from Emaily error, parameter error, the emaily template id cannot be empty");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        HttpResponse response = httpService.get(getStatsUrl(from, to), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: StatsServiceImpl] get campaigns stats by time range from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: StatsServiceImpl @{}] get campaigns stats by time range from Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse getCampaignsReport(String search, Pageable pageable) throws IOException {

        HttpResponse response = httpService.get(getCampaignStatsUrl(search, pageable), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: StatsServiceImpl] get campaigns report from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: StatsServiceImpl @{}] get campaigns report from Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse getCampaignsStatsDetails(Long id, Metric metric) throws IOException {
        HttpResponse response = httpService.get(getCampaignStatsDetailsUrl(id, metric), getRequestHeaders());

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: StatsServiceImpl] get campaigns stats details from Emaily error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: StatsServiceImpl @{}] get campaigns stats details from Emaily error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    public Headers getRequestHeaders() {
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, TOKEN_TYPE + SPACE + currentUserToken);
        headersBuilder.put(EmailConstans.HEADER_CLIENT_ID, ClientTokenHolder.getInstance().getClientId());
        if (SecurityUtils.getUserUid() != null) {
            headersBuilder.put(EmailConstans.HEADER_UID, SecurityUtils.getUserUid());
        }
//        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, "bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.7N5q2Y0qIuLHYwOxaoYCq-F54SDHMSO4kvUx_Goxft8");
        return Headers.of(headersBuilder);
    }

}
