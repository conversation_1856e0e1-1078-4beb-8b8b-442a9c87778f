package com.altomni.apn.common.repository.calendar;

import com.altomni.apn.common.domain.calendar.CalendarEventRelationInfo;
import com.altomni.apn.common.domain.enumeration.calendar.CalendarTypeEnum;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface CalendarEventRelationInfoRepository extends JpaRepository<CalendarEventRelationInfo, Long> {

    void deleteByEventId(Long eventId);

    @Query(value = """
                select distinct ri.relation_id as id,ri.relation_name from calendar_event_relation_info ri
                  left join calendar_event_attendee ea on ea.event_id = ri.event_id
                  where ri.relation_type=?2 and ea.user_id=?1 order by ri.created_date desc limit 10
            """, nativeQuery = true)
    List<CalendarEventRelationInfo> findAllByRelationType(Long userId,Integer type);

    List<CalendarEventRelationInfo> findAllByEventId(Long eventId);

    List<CalendarEventRelationInfo> findAllByEventIdIn(Collection<Long> eventIdSet);

    @Query(value = """
    select distinct ce.id from CalendarEvent ce
     left join CalendarEventRelationInfo ceri on ce.id = ceri.eventId
     left join CalendarEventAttendee cea on cea.eventId = ce.id
     where ce.referenceId = :referenceId
     and ce.calendarType in (:calendarType)
     AND (cea.status = com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum.TO_BE_COMPLETED
        OR cea.status = com.altomni.apn.common.domain.enumeration.calendar.CalendarStatusEnum.OVERDUE)
    """)
    List<Long> findEventIdByUniqueReference(@Param("referenceId")Long referenceId, @Param("calendarType") List<CalendarTypeEnum> calendarType);
}
