package com.altomni.apn.common.domain.settings;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@ApiModel(value = "跟踪记录模版关联表",description = "")
@Entity
@Table(name="setting_track_record_relation")
@Data
public class SettingTrackRecordRelation extends AbstractAuditingEntity implements Serializable, Cloneable{

    /**
     *
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private BigInteger id;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "temp_id")
    private BigInteger tempId ;

    /**  */
    @ApiModelProperty(name = "")
    @Column(name = "user_id")
    private BigInteger userId ;

    /** 状态 0-无效 1-有效 */
    @ApiModelProperty(name = "状态 0-无效 1-有效")
    @Column(name = "status")
    private Integer status ;
}
