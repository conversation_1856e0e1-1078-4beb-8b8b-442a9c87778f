package com.altomni.apn.common.dto.lark;

import lombok.Data;

import java.time.Instant;

@Data
public class LarkUserTokenDTO {

    private String code;
    private String access_token;
    private Integer expires_in;
    private String refresh_token;
    private Integer refresh_token_expires_in;
    private String scope;
    private String token_type;

    // 记录获取时间,用于判断token是否过期
    private Instant fetchedAt;

}
