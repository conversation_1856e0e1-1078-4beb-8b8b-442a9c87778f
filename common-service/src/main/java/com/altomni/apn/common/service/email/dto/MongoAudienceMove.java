package com.altomni.apn.common.service.email.dto;

import lombok.Data;

import java.util.List;

@Data
public class MongoAudienceMove {

    private Long from;

    private Long to;

    private List<String> audienceIds;

    private int maxGroupSize;

    public MongoAudienceMove() {}

    public MongoAudienceMove(Long from, Long to, List<String> audienceIds) {
        this.from = from;
        this.to = to;
        this.audienceIds = audienceIds;
    }

    public MongoAudienceMove(Long from, Long to, List<String> audienceIds, int maxGroupSize) {
        this.from = from;
        this.to = to;
        this.audienceIds = audienceIds;
        this.maxGroupSize = maxGroupSize;
    }

    public Long getFrom() {
        return from;
    }

    public void setFrom(Long from) {
        this.from = from;
    }

    public Long getTo() {
        return to;
    }

    public void setTo(Long to) {
        this.to = to;
    }

    public List<String> getAudienceIds() {
        return audienceIds;
    }

    public void setAudienceIds(List<String> audienceIds) {
        this.audienceIds = audienceIds;
    }

    public int getMaxGroupSize() {
        return maxGroupSize;
    }

    public void setMaxGroupSize(int maxGroupSize) {
        this.maxGroupSize = maxGroupSize;
    }
}
