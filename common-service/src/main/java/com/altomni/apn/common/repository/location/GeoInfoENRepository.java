package com.altomni.apn.common.repository.location;

import com.altomni.apn.common.domain.location.GeoInfoEN;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the GeoInfo entity.
 */
@Repository
public interface GeoInfoENRepository extends JpaRepository<GeoInfoEN, Long> {
    /**
     *
     * @param countryCodes
     * @return GeoInfoDTO
     */
    @Cacheable("GeoInfoByCountryCode")
    @Query(value = "SELECT enl.id as cityId, " +
            "    enl.city_name as city, " +
            "    enl.subdivision_1_iso_code as provinceCode, " +
            "    enl.subdivision_1_name as province, " +
            "    enl.country_iso_code as countryCode, " +
            "    enl.country_name as country, " +
            "    cnl.city_name as cityCN, " +
            "    cnl.subdivision_1_name as provinceCN, " +
            "    cnl.country_name as countryCN " +
            "FROM city_locations_en enl " +
            "join city_locations_cn cnl " +
            "on enl.id = cnl.id " +
            "where enl.city_name is not null " +
            "and trim(enl.city_name) <> '' " +
            "and enl.country_iso_code in ( ?1 ) ", nativeQuery = true)
    List<Object[]> findAllGeoInfoByCountryCode(List<String> countryCodes);

    /**
     *
     * @param city
     * @param countryCode
     * @return CityProvinceSearchDTO
     */
    @Query(value = "select en.id as cityId, " +
            "concat(en.city_name,  ', ',  en.subdivision_1_iso_code) COLLATE utf8mb4_unicode_ci " +"cityNprovince " +
            "from city_locations_en en " +
            "where  en.city_name like CONCAT(:city,'%') " +
            "and en.country_iso_code = :countryCode " +
            "union " +
            "select cn.id as cityId, " +
            "concat(cn.city_name,  ', ',  cn.subdivision_1_iso_code) cityNprovince " +
            "from city_locations_cn cn " +
            "where  cn.city_name like CONCAT(:city,'%') " +
            "and cn.country_iso_code = :countryCode " +
            "limit 30", nativeQuery = true)
    List<Object[]> searchCityNProvinceByCity(
            @Param("city")String city,
            @Param("countryCode")String countryCode);


    @Query(value = "SELECT * from city_locations_en cl left join favorite_country fc on cl.country_iso_code =fc.country_iso_code where cl.country_iso_code in ?3 and (LOCATE(?1, cl.city_name)>0 or LOCATE(?1, cl.country_name)>0 or LOCATE(?1, cl.subdivision_1_name)>0 or LOCATE(?1, cl.continent_name)>0) GROUP BY cl.id order by fc.favorite_level ASC,(CASE WHEN ( LOCATE(?1, cl.city_name )> 0 ) THEN 4 WHEN ( LOCATE(?1, cl.subdivision_1_name )> 0 ) THEN 3 WHEN ( LOCATE(?1, cl.country_name )> 0 ) THEN 2 END ) DESC limit ?2 ",nativeQuery = true)
    List<GeoInfoEN> searchGeoInfo(String condition, Integer item, String[] countyCodeList);

    @Query(value = "SELECT * from city_locations_en cl left join favorite_country fc on cl.country_iso_code =fc.country_iso_code where LOCATE(?1, cl.city_name)>0 or LOCATE(?1, cl.country_name)>0 or LOCATE(?1, cl.subdivision_1_name)>0 or LOCATE(?1, cl.continent_name)>0 GROUP BY cl.id order by fc.favorite_level ASC,(CASE WHEN ( LOCATE(?1, cl.city_name )> 0 ) THEN 4 WHEN ( LOCATE(?1, cl.subdivision_1_name )> 0 ) THEN 3 WHEN ( LOCATE(?1, cl.country_name )> 0 ) THEN 2 END ) DESC limit ?2 ",nativeQuery = true)
    List<GeoInfoEN> searchGeoInfo(String condition, Integer item);

    @Query(value = "SELECT * from city_locations_en where id in (?1) ",nativeQuery = true)
    List<GeoInfoEN> findByIds(List<Long> ids);

    @Query(value = "(SELECT id as 'cityId', city_name as 'city', subdivision_1_iso_code as 'provinceCode', subdivision_1_name as 'province', country_iso_code as 'countryCode', country_name as 'country', null as 'countyId', null as 'county', null as 'metroArea', null as 'metroAreaCode', 'city' as 'similarity' FROM city_locations_en en where en.city_name LIKE CONCAT('%', ?1, '%') AND en.subdivision_1_name LIKE CONCAT('%', ?2, '%') ORDER BY IF(en.country_iso_code = 'US', 1, 2), city_name LIMIT 40) " +
            "UNION " +
            "(SELECT id as 'cityId', city_name as 'city', subdivision_1_iso_code as 'provinceCode', subdivision_1_name as 'province', country_iso_code as 'countryCode', country_name as 'country', null as 'countyId', null as 'county', null as 'metroArea', null as 'metroAreaCode', 'province' as 'similarity' FROM city_locations_en en where en.subdivision_1_name LIKE CONCAT('%', ?1, '%') AND en.country_name LIKE CONCAT('%', ?2, '%') GROUP BY country_iso_code, subdivision_1_iso_code ORDER BY IF(en.country_iso_code = 'US', 1, 2), subdivision_1_name LIMIT 40) " +
            "UNION " +
            "(SELECT null as 'cityId', null as 'city', state_id as 'provinceCode', state_name as 'province', 'US' as 'countryCode', 'United States' as 'country', county_fips as 'countyId', county_name as 'county', null as 'metroArea', null as 'metroAreaCode', 'county' as 'similarity' from us_geo_info us WHERE us.county_name LIKE CONCAT('%', ?1, '%') AND us.state_name LIKE CONCAT('%', ?2, '%') GROUP BY us.county_fips ORDER BY county_name LIMIT 40)", nativeQuery = true)
    List<Object[]> searchByCityAndStateOrStateAndCountry(String substring, String substring1);

    @Query(value = "(SELECT id as 'cityId', city_name as 'city', subdivision_1_iso_code as 'provinceCode', subdivision_1_name as 'province', country_iso_code as 'countryCode', country_name as 'country', null as 'countyId', null as 'county', null as 'metroArea', null as 'metroAreaCode', 'city' as 'similarity' FROM city_locations_en en WHERE en.city_name LIKE CONCAT('%', ?1, '%') ORDER BY IF(en.country_iso_code = 'US', 1, 2), city_name LIMIT 40) " +
            "UNION " +
            "(SELECT id as 'cityId', city_name as 'city', subdivision_1_iso_code as 'provinceCode', subdivision_1_name as 'province', country_iso_code as 'countryCode', country_name as 'country', null as 'countyId', null as 'county', null as 'metroArea', null as 'metroAreaCode', 'province' as 'similarity' FROM city_locations_en en WHERE en.subdivision_1_name LIKE CONCAT('%', ?1, '%') GROUP BY country_iso_code, subdivision_1_iso_code ORDER BY IF(en.country_iso_code = 'US', 1, 2), subdivision_1_name LIMIT 40) " +
            "UNION " +
            "(SELECT id as 'cityId', city_name as 'city', subdivision_1_iso_code as 'provinceCode', subdivision_1_name as 'province', country_iso_code as 'countryCode', country_name as 'country', null as 'countyId', null as 'county', null as 'metroArea', null as 'metroAreaCode', 'country' as 'similarity' FROM city_locations_en en WHERE en.country_name LIKE CONCAT('%', ?1, '%') GROUP BY en.country_iso_code ORDER BY IF(en.country_iso_code = 'US', 1, 2), country_name LIMIT 40) " +
            "UNION " +
            "(SELECT null as 'cityId', null as 'city', state_id as 'provinceCode', state_name as 'province', 'US' as 'countryCode', 'United States' as 'country', county_fips as 'countyId', county_name as 'county', null as 'metroArea', null as 'metroAreaCode', 'county' as 'similarity' from us_geo_info us WHERE us.county_name LIKE CONCAT('%', ?1, '%') GROUP BY us.county_fips ORDER BY county_name LIMIT 40) " +
            "UNION " +
            "(SELECT null as 'cityId', null as 'city', null as 'provinceCode', null as 'province', 'US' as 'countryCode', 'United States' as 'country', null as 'countyId', null as 'county', metro_area_name as 'metroArea', metro_area_code as 'metroAreaCode', 'metropolitan' as 'similarity' from us_metro_area_info metro WHERE metro.metro_area_name LIKE CONCAT('%', ?1, '%') GROUP BY metro.metro_area_code ORDER BY metro_area_name LIMIT 40)", nativeQuery = true)
    List<Object[]> searchByCityOrState(String search);

}
