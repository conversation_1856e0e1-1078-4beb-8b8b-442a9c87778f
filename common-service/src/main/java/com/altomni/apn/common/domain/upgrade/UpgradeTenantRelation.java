package com.altomni.apn.common.domain.upgrade;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "upgrade_tenant_relation")
public class UpgradeTenantRelation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "upgrade_id")
    private Long upgradeId;

    @Column(name = "tenant_id")
    private Long tenantId;


    public UpgradeTenantRelation(Long upgradeId, Long tenantId) {
        this.upgradeId = upgradeId;
        this.tenantId = tenantId;
    }

    public UpgradeTenantRelation() {

    }
}
