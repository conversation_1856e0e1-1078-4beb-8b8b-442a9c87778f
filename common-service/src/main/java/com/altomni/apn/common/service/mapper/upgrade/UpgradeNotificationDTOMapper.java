package com.altomni.apn.common.service.mapper.upgrade;

import com.altomni.apn.common.domain.enumeration.LanguageEnum;
import com.altomni.apn.common.domain.upgrade.UpgradeNotification;
import com.altomni.apn.common.dto.upgrade.UpgradeContentDTO;
import com.altomni.apn.common.dto.upgrade.UpgradeCreationDTO;
import com.altomni.apn.common.service.mapper.EntityMapper;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

import java.time.Instant;
import java.util.List;

@Mapper(componentModel = "spring", unmappedTargetPolicy = ReportingPolicy.WARN)
public interface UpgradeNotificationDTOMapper extends EntityMapper<UpgradeCreationDTO, UpgradeNotification> {

    default String mapContentsToContent(List<UpgradeContentDTO> contents, LanguageEnum language) {
        if (contents == null) {
            return null;
        }
        return contents.stream()
                .filter(content -> language.equals(content.getLanguage()))
                .map(UpgradeContentDTO::getContent)
                .findFirst()
                .orElse(null);
    }

    default String mapLinkToLink(List<UpgradeContentDTO> contents, LanguageEnum language) {
        if (contents == null) {
            return null;
        }
        return contents.stream()
                .filter(content -> language.equals(content.getLanguage()))
                .map(UpgradeContentDTO::getLink)
                .findFirst()
                .orElse(null);
    }

    @Override
    default UpgradeNotification toEntity(UpgradeCreationDTO dto) {
        if (dto == null) {
            return null;
        }

        UpgradeNotification upgradeNotification = new UpgradeNotification();
        upgradeNotification.setId(dto.getId());
        upgradeNotification.setVersion(dto.getVersion());
        if (null == dto.getUpgradeTime()){
            upgradeNotification.setUpgradeTime(Instant.now());
        }else{
            upgradeNotification.setUpgradeTime(dto.getUpgradeTime());
        }
        upgradeNotification.setGlobal(dto.getGlobal());

        // Map contents to contentCn and contentEn
        upgradeNotification.setContentCn(mapContentsToContent(dto.getContents(), LanguageEnum.ZH));
        upgradeNotification.setContentEn(mapContentsToContent(dto.getContents(), LanguageEnum.EN));

        upgradeNotification.setLinkCn(mapLinkToLink(dto.getContents(), LanguageEnum.ZH));
        upgradeNotification.setLinkEn(mapLinkToLink(dto.getContents(), LanguageEnum.EN));

        return upgradeNotification;
    }
}
