package com.altomni.apn.common.service.parser;

import com.altomni.apn.common.domain.parser.enumeration.ParseType;
import com.altomni.apn.common.dto.redis.ParserResponse;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 */
public interface ParserService {

    ParserResponse downloadResume(HttpServletResponse response, String uuid);

    ParserResponse uploadResumeOnly(String uuid, String fileName, String contentType);

    ParserResponse checkParseJdStatusOrGetUploadUrl(String uuid, String fileName, String contentType, String jdText);

    ParserResponse checkParseResumeStatusOrGetUploadUrl(String uuid, String fileName, String contentType, int priority);

    ParserResponse getParseResultStatus(String uuid, ParseType parseType);

    ParserResponse getParserResumeResult(String uuid);

    ParserResponse getParserResumeInfo(String uuid);

    ParserResponse getParserJdResult(String uuid);

    void sendSqsMessage(String key, CloudFileObjectMetadata metadata);

    void initializeRedisForResumeParser(String uuid, String filename, String contentType, int priority);
}