package com.altomni.apn.common.domain.message;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnumConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "xxl_job_fail_record")
public class XxlJobFailRecord extends AbstractAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "xxl_job_id")
    private Integer xxlJobId;

    @Column(name = "type")
    @Convert(converter = XxlJobRelationTypeEnumConverter.class)
    private XxlJobRelationTypeEnum type;

    @Column(name = "request_param")
    private String requestParam;

    @Column(name = "error_message")
    private String responseMessage;

}
