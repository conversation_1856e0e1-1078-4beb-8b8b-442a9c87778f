package com.altomni.apn.common.service.email;

import com.altomni.apn.common.domain.email.enumeration.EmailCampaignArchiveStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailCampaignStatus;
import com.altomni.apn.common.domain.email.enumeration.EmailTemplateType;
import com.altomni.apn.common.domain.email.enumeration.TalentGroupActive;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.email.dto.*;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.List;

public interface EmailyService {

    HttpResponse createMongoAudienceGroup(Long id, String name) throws IOException;

    HttpResponse updateMongoAudienceGroup(Long id, Long emailyGroupId, String name) throws IOException;

    HttpResponse deleteAudienceByGroupIdAndAudienceId(Long id, Long emailyGroupId, List<String> emailyTalentIdList) throws IOException;

    HttpResponse addAudienceToGroup(Long id, Long emailyGroupId, List<MongoAudience> emailyTalentList, int maxGroupSize) throws IOException;

    HttpResponse addAudienceToGroupFromCommonPool(Long id, Long emailyGroupId, SearchConditionDTOV2 condition, int maxGroupSize) throws IOException;

    HttpResponse mergeAudienceToGroup(Long targetGroupId, EmailyGroupMergeDTO emailyGroupMergeDTO) throws IOException;

    HttpResponse moveAudienceToGroup(Long sourceGroupId, Long targetGroupId, Long emailySourceGroupId, Long emailyTargetGroupId, List<String> emailyTalentIdList, int maxGroupSize) throws IOException;

    HttpResponse copyAudienceToGroup(Long sourceGroupId, Long targetGroupId, Long emailySourceGroupId, Long emailyTargetGroupId, List<String> emailyTalentIdList, int maxGroupSize) throws IOException;

    HttpResponse createMongoAudienceGroup(String name) throws IOException;

    HttpResponse updateMongoAudienceGroup(Long emailyGroupId, String name) throws IOException;

    HttpResponse deleteAudienceByGroupIdAndAudienceId(Long emailyGroupId) throws IOException;

    HttpResponse recoveryAudienceByGroupIdAndAudienceId(Long emailyGroupId) throws IOException;

    HttpResponse getAudienceGroupFromEmaily(Long emailyGroupId) throws IOException;

    HttpResponse queryAudienceGroupInfoFromEmaily(Long emailyGroupId) throws IOException;

    HttpResponse searchAllActiveMongoAudienceGroupList(String name, TalentGroupActive talentGroupActive) throws IOException;

    HttpResponse searchInActiveMongoAudienceGroupList(String name, Pageable pageable) throws IOException;

    HttpResponse searchActiveMongoAudienceGroupList(String name, Pageable pageable) throws IOException;

    HttpResponse searchMongoAudienceListByGroupId(Long emailyGroupId, String source, Pageable pageable) throws IOException;

    HttpResponse createEmailCampaign(Long id, EmailyAssociatedCampaign emailyCampaign) throws IOException;

    HttpResponse updateEmailCampaign(Long id, Long emailyCampaignId, EmailyAssociatedCampaign emailyCampaign) throws IOException;

    HttpResponse updateEmailCampaignsPending(Long id, Long emailyCampaignId) throws IOException;

    HttpResponse queryEmailTemplate(Long templateId) throws IOException;

    HttpResponse updateEmailyAudience(Long talentId, EmailyAudienceUpdateDTO emailyAudienceUpdateDTO) throws IOException;

    HttpResponse queryEmailCampaign(Long id, Long emailyCampaignId) throws IOException;

    HttpResponse searchEmailCampaign(EmailCampaignArchiveStatus archiveStatus, List<EmailCampaignStatus> statusList, String search, String sort, Pageable pageable) throws IOException;

    HttpResponse searchEmailCampaignByName(String emailyCampaignName) throws IOException;

    HttpResponse archiveEmailCampaign(Long id, Long emailyCampaignId) throws IOException;

    HttpResponse unArchiveEmailCampaign(Long id, Long emailyCampaignId) throws IOException;

    HttpResponse approveEmailCampaign(Long id, Long emailyCampaignId) throws IOException;

    HttpResponse denyEmailCampaign(Long id, Long emailyCampaignId, EmailyCmpaignDenyDTO denyDTO) throws IOException;

    HttpResponse sendEmailCampaignTest(Long id, Long emailyCampaignId, List<EmailyCampaignSendDTO> emailyCampaignSendDTOListO) throws IOException;

    HttpResponse searchEmailTemplates(EmailTemplateType type, Long tagsId, String name, Pageable pageable, String sort) throws IOException;

    HttpResponse searchEmailTemplateTags() throws IOException;

    HttpResponse createEmailTemplate(EmailyTemplateDTO templateDTO) throws IOException;

    HttpResponse updateEmailTemplate(Long templateId, EmailyTemplateDTO templateDTO) throws IOException;

    HttpResponse deactivateEmailTemplates(List<Long> templateIdList) throws IOException;

    HttpResponse queryEmailTemplateByName(String name) throws IOException;

    HttpResponse queryCompanyLimit() throws IOException;

}
