package com.altomni.apn.common.web.rest.email;

import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.email.CampaignService;
import com.altomni.apn.common.utils.HeaderConvertUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.web.rest.email.vm.VoipTalentNoticeVM;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Objects;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-EmailService"})
@RestController
@RequestMapping("/api/v3/voip")
public class VoipMailResource {

    private final Logger log = LoggerFactory.getLogger(VoipMailResource.class);

    private static final String ENTITY_NAME = "emailService";

    @Resource
    private CampaignService campaignService;

    @ApiOperation(value = "front-end use this api to send emails")
    @PostMapping(value = "/send-mail", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> sendDraft(@RequestBody VoipTalentNoticeVM voipTalentNoticeVM) throws IOException {
        log.info("[CampaignResource: sendRichMail @{}] REST request to send rich email, with params MailVM: {}", SecurityUtils.getUserId(), voipTalentNoticeVM);
        if (Objects.nonNull(voipTalentNoticeVM.getTalentId())) {
            voipTalentNoticeVM.setCustomerId2("VOIPNOTICE:TALENT" + voipTalentNoticeVM.getTalentId());
        } else if (Objects.nonNull(voipTalentNoticeVM.getContactId())) {
            voipTalentNoticeVM.setCustomerId2("VOIPNOTICE:CONTACT" + voipTalentNoticeVM.getContactId());
        }
        String res = campaignService.sendHtmlMail(voipTalentNoticeVM, false, false , true);
        campaignService.syncVoipEmailRecordToEs(voipTalentNoticeVM.getTalentId(), voipTalentNoticeVM.getSubject());
        return ResponseEntity.ok(res);
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @GetMapping(value = "/count-by-talent", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> countVoipTalentNoticeEmail(@RequestParam("talentId") Long talentId) throws IOException {
        log.info("[CampaignResource: countVoipTalentNoticeEmail @{}] count voip talent notice email with talentId: {}", SecurityUtils.getUserId(), talentId);
        HttpResponse res = campaignService.countVoipTalentNoticeEmail(talentId);
        return new ResponseEntity<>(res.getBody(), HeaderConvertUtil.convertHeaders(res.getHeaders()), HttpStatus.resolve(res.getCode()));
    }

    @ApiOperation(value = "front-end use this api to send emails")
    @GetMapping(value = "/count-by-contact", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> countVoipContactNoticeEmail(@RequestParam("contactId") Long contactId) throws IOException {
        log.info("[CampaignResource: countVoipContactNoticeEmail @{}] count voip contact notice email with contactId: {}", SecurityUtils.getUserId(), contactId);
        HttpResponse res = campaignService.countVoipContactNoticeEmail(contactId);
        return new ResponseEntity<>(res.getBody(), HeaderConvertUtil.convertHeaders(res.getHeaders()), HttpStatus.resolve(res.getCode()));
    }

}
