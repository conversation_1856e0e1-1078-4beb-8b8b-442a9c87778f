package com.altomni.apn.common.service.email.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.config.env.email.EmailAppProperties;
import com.altomni.apn.common.dto.email.UserResetDTO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.email.EmailConstans;
import com.altomni.apn.common.service.email.SystemEmailService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.ipg.resourceserver.client.ClientTokenHolder;
import okhttp3.Headers;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Service
public class SystemEmailServiceImpl implements SystemEmailService {

    private final Logger log = LoggerFactory.getLogger(SystemEmailServiceImpl.class);

    @Resource
    private EmailAppProperties emailAppProperties;

    @Resource
    private HttpService httpService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

    private final static String HEADERS_AUTHORIZATION_KEY = "Authorization";

    private final static String SPACE = " ";

    private final static String EMAILY_SYSTEM_MAIL_BASE_URL = "/api/v1/mail";

    private final static String INIT_PWD = "/reset-pwd-init";

    private final static String RESET_PWD = "/reset-pwd";


    private String baseUrl() { return emailAppProperties.getEmailyBaseUrl(); }

    private String getInitPwdUrl() {
        return baseUrl() + EMAILY_SYSTEM_MAIL_BASE_URL + INIT_PWD;
    }

    private String getResetPwdUrl() {
        return baseUrl() + EMAILY_SYSTEM_MAIL_BASE_URL + RESET_PWD;
    }

    @Override
    public HttpResponse sendInitPasswordEmail(UserResetDTO userResetDTO) throws IOException {
        if (ObjectUtil.isEmpty(userResetDTO)) {
            log.error("[EmailService: SystemEmailServiceImpl] send init password error, parameter error, the userResetDTO cannot be empty or null");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        //inject apnV3 baseUrl
        userResetDTO.setLangKey("en"); //only have en template
        userResetDTO.setBaseUrl(emailAppProperties.getMainPathBaseUrl());

        String groupParamStr = JSON.toJSONString(userResetDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(getInitPwdUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: SystemEmailServiceImpl] send init password error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: SystemEmailServiceImpl @{}] send init password error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    @Override
    public HttpResponse sendResetPasswordEmail(UserResetDTO userResetDTO) throws IOException {
        if (ObjectUtil.isEmpty(userResetDTO)) {
            log.error("[EmailService: SystemEmailServiceImpl] send reset password error, parameter error, the userResetDTO cannot be empty or null");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_COMMON_INVALIDPARAMETER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        //inject apnV3 baseUrl
        userResetDTO.setLangKey("en");//only have en template
        userResetDTO.setBaseUrl(emailAppProperties.getMainPathBaseUrl());

        String groupParamStr = JSON.toJSONString(userResetDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(getResetPwdUrl(), getRequestHeaders(), groupParamStr);

        if (response != null) {
             
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[EmailService: SystemEmailServiceImpl] send reset password error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
        } else {
            log.error("[EmailService: SystemEmailServiceImpl @{}] send reset password error and response is null", SecurityUtils.getUserId());
        }
        return response;
    }

    public Headers getRequestHeaders() {
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, TOKEN_TYPE + SPACE + currentUserToken);
        headersBuilder.put(EmailConstans.HEADER_CLIENT_ID, ClientTokenHolder.getInstance().getClientId());
        if (SecurityUtils.getUserUid() != null) {
            headersBuilder.put(EmailConstans.HEADER_UID, SecurityUtils.getUserUid());
        }
        return Headers.of(headersBuilder);
    }

}
