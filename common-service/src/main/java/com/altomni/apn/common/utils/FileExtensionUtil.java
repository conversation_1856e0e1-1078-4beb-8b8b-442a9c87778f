package com.altomni.apn.common.utils;

import org.springframework.web.util.UriUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class FileExtensionUtil {
    private static final Map<String, String> MIME_TYPES_TO_EXTENSIONS = new HashMap<>();

    static {
        // 常见文件类型映射
        MIME_TYPES_TO_EXTENSIONS.put("application/pdf", "pdf");
        MIME_TYPES_TO_EXTENSIONS.put("application/msword", "doc");
        MIME_TYPES_TO_EXTENSIONS.put("application/vnd.openxmlformats-officedocument.wordprocessingml.document", "docx");
        MIME_TYPES_TO_EXTENSIONS.put("application/vnd.ms-excel", "xls");
        MIME_TYPES_TO_EXTENSIONS.put("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "xlsx");
        MIME_TYPES_TO_EXTENSIONS.put("application/vnd.ms-powerpoint", "ppt");
        MIME_TYPES_TO_EXTENSIONS.put("application/vnd.openxmlformats-officedocument.presentationml.presentation", "pptx");
        MIME_TYPES_TO_EXTENSIONS.put("image/jpeg", "jpg");
        MIME_TYPES_TO_EXTENSIONS.put("image/png", "png");
        MIME_TYPES_TO_EXTENSIONS.put("image/gif", "gif");
        MIME_TYPES_TO_EXTENSIONS.put("text/plain", "txt");
        MIME_TYPES_TO_EXTENSIONS.put("text/html", "html");
        MIME_TYPES_TO_EXTENSIONS.put("text/csv", "csv");
        // 可以继续添加更多映射
    }

    public static String generateEncodedFileName(String originalFileName, String contentType) {
        // 先获取正确扩展名的文件名
        String correctedFileName = updateFileNameWithCorrectExtension(originalFileName, contentType);

        try {
            // URL编码文件名
            String encodedFileName = UriUtils.encodePath(correctedFileName, StandardCharsets.UTF_8.toString());
            // 返回格式化的字符串
            return "filename=\"" + encodedFileName + "\"";
        } catch (Exception e) {
            return "filename=\"" + correctedFileName + "\"";
        }
    }

    private static String updateFileNameWithCorrectExtension(String originalFileName, String contentType) {
        if (contentType == null || contentType.isEmpty()) {
            return originalFileName;
        }

        // 获取不带扩展名的文件名
        String nameWithoutExtension;
        if (originalFileName.contains(".")) {
            nameWithoutExtension = originalFileName.substring(0, originalFileName.lastIndexOf('.'));
        } else {
            nameWithoutExtension = originalFileName;
        }

        // 根据 Content-Type 获取正确的扩展名
        String newExtension = MIME_TYPES_TO_EXTENSIONS.get(contentType.toLowerCase());

        // 如果找不到对应的扩展名，返回原文件名
        if (newExtension == null) {
            return originalFileName;
        }

        // 返回更新后的文件名
        return nameWithoutExtension + "." + newExtension;
    }
}
