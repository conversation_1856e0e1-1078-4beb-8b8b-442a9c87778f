package com.altomni.apn.common.domain.email;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.email.enumeration.BillPaymentMethod;
import com.altomni.apn.common.domain.email.enumeration.BillType;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
@Entity
@Table(name = "email_campaign_bill_record")
public class EmailCampaignBillRecord extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "email_campaign_id")
    private Long emailCampaignId;

    @Column(name = "payment_method")
    private BillPaymentMethod paymentMethod;

    @Column(name = "cost")
    private int cost;

    @Column(name = "type")
    private BillType type;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "tenant_id")
    private Long tenantId;

    public EmailCampaignBillRecord() {
    }

    public EmailCampaignBillRecord(Long emailCampaignId, BillPaymentMethod paymentMethod, int cost, BillType type, Long userId, Long tenantId) {
        this.emailCampaignId = emailCampaignId;
        this.paymentMethod = paymentMethod;
        this.cost = cost;
        this.type = type;
        this.userId = userId;
        this.tenantId = tenantId;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEmailCampaignId() {
        return emailCampaignId;
    }

    public void setEmailCampaignId(Long emailCampaignId) {
        this.emailCampaignId = emailCampaignId;
    }

    public int getCost() {
        return cost;
    }

    public void setCost(int cost) {
        this.cost = cost;
    }

    public BillType getType() {
        return type;
    }

    public void setType(BillType type) {
        this.type = type;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public BillPaymentMethod getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(BillPaymentMethod paymentMethod) {
        this.paymentMethod = paymentMethod;
    }
}
