package com.altomni.apn.common.service.email.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.AppInitForEmailService;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.config.env.CommonApiPromptProperties;
import com.altomni.apn.common.config.env.email.EmailAppProperties;
import com.altomni.apn.common.domain.email.UserGmailAliasBinding;
import com.altomni.apn.common.domain.email.enumeration.GmailAliasBindingStatus;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.enumeration.enums.CommonAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.repository.email.UserGmailAliasRepository;
import com.altomni.apn.common.service.email.EmailConstans;
import com.altomni.apn.common.service.email.UserEmailService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.user.UserService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.web.rest.email.vm.AlternateEmailVM;
import com.ipg.resourceserver.client.ClientTokenHolder;
import okhttp3.Headers;
import okhttp3.HttpUrl;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.AuthConstants.TOKEN_TYPE;

@Service
public class UserEmailServiceImpl implements UserEmailService {

    private final Logger log = LoggerFactory.getLogger(UserEmailServiceImpl.class);

    @Resource
    private EmailAppProperties emailAppProperties;

    @Resource
    private HttpService httpService;

    @Resource
    private UserService userService;

    @Resource
    private UserGmailAliasRepository userGmailAliasRepository;

    @Resource
    private ApplicationIPGProperties applicationIPGProperties;

    private OkHttpClient client;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    CommonApiPromptProperties commonApiPromptProperties;

    @Resource
    AppInitForEmailService appInitForEmailService;

    private final static String HEADERS_AUTHORIZATION_KEY = "Authorization";

    private final static String SPACE = " ";

    private final static String EMAILY_SYSTEM_MAIL_BASE_URL = "/api/v1";

    private final static String BINDING_STATUS = "/gmail-alias/check-binding-status";

    private final static String REQUEST_BINDING = "/gmail-alias/request-binding";

    private final static String CONFIRM_BINDING = "/gmail-alias/confirm-binding";

    private final static int RECENT_BINDING_HOURS = 72;

    public UserEmailServiceImpl() {
        this.client = new OkHttpClient().newBuilder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30,TimeUnit.SECONDS)
                .build();
    }


    private String baseUrl() { return emailAppProperties.getEmailyBaseUrl(); }

    private String getBindingStatusUrl() {
        return baseUrl() + EMAILY_SYSTEM_MAIL_BASE_URL + BINDING_STATUS;
    }

    private String getRequestBindingUrl() {
        return baseUrl() + EMAILY_SYSTEM_MAIL_BASE_URL + REQUEST_BINDING;
    }

    private String getConfirmBindingUrl() {
        return baseUrl() + EMAILY_SYSTEM_MAIL_BASE_URL + CONFIRM_BINDING;
    }

    @Override
    public GmailAliasBindingStatus getBindingStatusForCurrentUser() {
        if (!applicationIPGProperties.isUseGmailAlias()) {
            return GmailAliasBindingStatus.CONFIRMED;
        }

//        return this.getBindingStatusByUserId(SecurityUtils.getUserId());
        if (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            return GmailAliasBindingStatus.UNNECESSARY;
        }

        return this.getBindingStatusByUserId().orElse(GmailAliasBindingStatus.NONE);
    }

    @Override
    public boolean needCheckGmailAliasBindingStatus() {
        return this.needCheckGmailAliasBindingStatus(SecurityUtils.getUserId());
    }

    private boolean needCheckGmailAliasBindingStatus(Long userId) {
        List<UserGmailAliasBinding> bindingList = userGmailAliasRepository.findAllByUserId(userId);
        if (bindingList.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_NEEDCHECKGMAILALIASBINDING.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
        List<UserGmailAliasBinding> confirmedList = bindingList.stream().filter(b -> GmailAliasBindingStatus.CONFIRMED.equals(b.getBindingStatus())).collect(Collectors.toList());
        List<UserGmailAliasBinding> recentBindings = confirmedList.stream()
                .filter(b -> {
                    Instant lastModified = b.getLastModifiedDate();
                    Duration durationSinceModified = Duration.between(lastModified, Instant.now());
                    return durationSinceModified.toHours() <= RECENT_BINDING_HOURS;
                })
                .collect(Collectors.toList());
        return !recentBindings.isEmpty();
    }

    private List<UserGmailAliasBinding> getRecentConfirmed(Long userId) {
        List<UserGmailAliasBinding> bindingList = userGmailAliasRepository.findAllByUserId(userId);
        List<UserGmailAliasBinding> confirmedList = bindingList.stream().filter(b -> GmailAliasBindingStatus.CONFIRMED.equals(b.getBindingStatus())).collect(Collectors.toList());
        List<UserGmailAliasBinding> recentBindings = confirmedList.stream()
                .filter(b -> {
                    Instant lastModified = b.getLastModifiedDate();
                    Duration durationSinceModified = Duration.between(lastModified, Instant.now());
                    return durationSinceModified.toHours() <= RECENT_BINDING_HOURS;
                })
                .collect(Collectors.toList());
        return recentBindings;
    }

    private Optional<GmailAliasBindingStatus> getBindingStatusByUserId() {
        long userId = SecurityUtils.getUserId();
        log.info("[UserEmailServiceImpl: GmailAliasBindingStatus @{}] request to get binding status for current user", userId);
//        Optional<UserGmailAliasBinding> binding = userGmailAliasRepository.findByUserId(userId);
//        return binding.map(UserGmailAliasBinding::getBindingStatus);
        List<UserGmailAliasBinding> bindingList = userGmailAliasRepository.findAllByUserId(userId);
        if (!bindingList.isEmpty()) {
            UserGmailAliasBinding res = Collections.max(bindingList,
                    Comparator.comparing(UserGmailAliasBinding::getBindingStatus, GmailAliasBindingStatus.COMPARATOR));
//            List<UserGmailAliasBinding> recentConfirmed = this.getRecentConfirmed(userId);
            if (GmailAliasBindingStatus.CONFIRMED.equals(res.getBindingStatus())) {
                Instant lastModified = res.getLastModifiedDate();
                Duration durationSinceModified = Duration.between(lastModified, Instant.now());
                if (durationSinceModified.toHours() <= RECENT_BINDING_HOURS) {
                    if (!this.getBindingStatus(this.findCurrentUserEmail())) {
                        this.removeAllBindingRecords(userId);
                        return Optional.empty();
                    }
                }
            }
            return Optional.of(res.getBindingStatus());
        } else {
            return Optional.empty();
        }
    }

    /**
     * Schedule check the binding status of recent CONFIRMED binding records every day at 02:00 PM (UTC), 10:00 PM (Beijing) 7:00 AM (PST).
     */
    @Override
    //@Scheduled(cron = "0 0 14 * * ?", zone = "UTC")
//    @Scheduled(cron = "0 15 5 * * ?", zone = "UTC")
    public void scheduleCheckRecentConfirmedBindingStatus() {
        Instant threeDaysAgo = Instant.now().minus(RECENT_BINDING_HOURS, ChronoUnit.HOURS);
        log.info("[UserEmailServiceImpl: scheduleCheckRecentConfirmedBindingStatus] run scheduled check for binding status for binding records after: {}", threeDaysAgo.toString());
        List<UserGmailAliasBinding> recentBinding = userGmailAliasRepository.findAllByBindingStatusAndLastModifiedDateAfter(GmailAliasBindingStatus.CONFIRMED, threeDaysAgo);

        log.info("[UserEmailServiceImpl: scheduleCheckRecentConfirmedBindingStatus] found {} recent confirmed binding", CollectionUtils.size(recentBinding));
        if (!recentBinding.isEmpty()) {
            for (int i = 0; i < recentBinding.size(); i++) {
                UserGmailAliasBinding binding = recentBinding.get(i);
                Long userId = binding.getUserId();
                String email = findUserEmailById(userId);

                log.info("[UserEmailServiceImpl: scheduleCheckRecentConfirmedBindingStatus] check {} record, userId: {}, email: {}, rawGmailAccount: {}", i, userId, email, binding.getRawGmailAccount());
                for (int j = 0; j < 3; j++) {
                    try {
                        boolean valid = this.getBindingStatus(email);
                        log.info("[UserEmailServiceImpl: scheduleCheckRecentConfirmedBindingStatus] [{}]th check [{}]th record, userId: {}, email: {}, rawGmailAccount: {}, binding status is {}", j, i, userId, email, binding.getRawGmailAccount(), valid);
                        if (!valid) {
                            this.removeAllBindingRecords(userId);
                            log.info("[UserEmailServiceImpl: scheduleCheckRecentConfirmedBindingStatus] [{}]th check [{}]th record, userId: {}, email: {}, rawGmailAccount: {}, binding status is gone, remove record from database", j, i, userId, email, binding.getRawGmailAccount());
                        }
                        break;
                    } catch (Exception e) {
                        log.info("[UserEmailServiceImpl: scheduleCheckRecentConfirmedBindingStatus] Exception when [{}]th check [{}]th record, userId: {}, email: {}, rawGmailAccount: {}, message: {}", j, i, userId, email, binding.getRawGmailAccount(), e.getMessage(), e);
                    }
                }
            }
        }
    }

    private boolean getBindingStatus(String email) {
        log.info("[UserEmailServiceImpl: getBindingStatus @{}] request to get binding status for email: {}", SecurityUtils.getUserId(), email);
        email = StringUtils.trimToEmpty(email);
        if (StringUtils.isBlank(email)) {
            log.warn("[UserEmailServiceImpl: getBindingStatus @{}] email address is null", SecurityUtils.getUserId());
            throw new CustomParameterizedException("You cannot get binding status for empty email address!");
        }

        String url = HttpUrl.parse(getBindingStatusUrl())
                .newBuilder()
                .addQueryParameter("email", email)
                .build().toString();
//        try {
//            HttpResponse response = httpService.get(url, getRequestHeaders());
//            JSONObject resBody = JSON.parseObject(response.getBody());
//
//            log.info("[UserEmailServiceImpl: getBindingStatus @{}] response code is: {}, response body is: {}", SecurityUtils.getUserId(), response.getCode(), response.getBody());
//
//            if (response.getCode() == 200) {
//                return true;
//            } else if (response.getCode() == 400){
//                int errorCode = resBody.getIntValue("errorCode");
//                String errorMsg = resBody.getString("errorMsg");
//
//                if (errorCode == 420) {
//                    log.error("[UserEmailServiceImpl: getBindingStatus] response error : {} with error message: {}", errorCode, errorMsg);
//                    return false;
//                } else if (errorCode == 423) {
//                    //lark notification
//                    log.error("[UserEmailServiceImpl: getBindingStatus] response error : {} with error message: {}", errorCode, errorMsg);
//                    throw new CustomParameterizedException("Error when get binding status!!");
//                } else {
//                    log.error("[UserEmailServiceImpl: getBindingStatus] response error: {} with error message: {}", errorCode, errorMsg);
//                    throw new CustomParameterizedException("Error when get binding status!");
//                }
//            } else {
//                log.error("[UserEmailServiceImpl: getBindingStatus] IOException1");
//                throw new CustomParameterizedException("IOException when get binding status1!");
//            }
//        } catch (IOException e) {
//            log.error("[UserEmailServiceImpl: getBindingStatus] IOException");
//            throw new CustomParameterizedException("IOException when get binding status!");
//        }

        Request request = new Request.Builder()
                .url(url)
                .get()
                .headers(getRequestHeaders())
                .build();

        LoginUtil.simulateLoginWithClient();
        try (okhttp3.Response response = client.newCall(request).execute()) {
            String responseBody = response.body() != null ? response.body().string() : null;
            log.info("[UserEmailServiceImpl: getBindingStatus @{}] response code is: {}, response body is: {}", SecurityUtils.getUserId(), response.code(), responseBody);

            if (response.isSuccessful()) {
                return true;
            } else {
                if (responseBody == null) {
                    log.error("[UserEmailServiceImpl: getBindingStatus] response code: {} and response body is null", response.code());
                }

                if (response.code() == 400) {
                    JSONObject resBody = JSON.parseObject(responseBody);
                    int errorCode = resBody.getIntValue("errorCode");
                    String errorMsg = resBody.getString("errorMsg");

                    if (errorCode == 420) {
                        log.error("[UserEmailServiceImpl: getBindingStatus] response error code: {} with error message: {}", errorCode, errorMsg);
                        return false;
                    } else if (errorCode == 423) {
                        //lark notification
                        log.error("[UserEmailServiceImpl: getBindingStatus] response error code: {} with error message: {}", errorCode, errorMsg);
                        throw new CustomParameterizedException("Error when get binding status!!");
                    } else {
                        log.error("[UserEmailServiceImpl: getBindingStatus] response error code: {} with error message: {}", errorCode, errorMsg);
                        throw new CustomParameterizedException("Error when get binding status with errorCode: " + errorCode);
                    }
                } else if (response.code() > 400 && response.code() < 500) {
                    log.error("[UserEmailServiceImpl: getBindingStatus] response error with code: {}, body: {}", response.code(),responseBody);
                    throw new CustomParameterizedException("Error when get binding status with response code: " + response.code());
                } else {
                    log.error("[UserEmailServiceImpl: getBindingStatus] IOException with response code: {}", response.code());
                    throw new CustomParameterizedException("IOException when get binding status1!");
                }
            }
        } catch (IOException e) {
            log.error("[UserEmailServiceImpl: getBindingStatus] IOException");
            throw new CustomParameterizedException("IOException when get binding status!");
        }
    }

    @Override
    public String requestToBindingGmailAliasForCurrentUser() {
        log.info("[UserEmailServiceImpl: requestToBindingGmailAliasForCurrentUser @{}] request to binding gmail alias for current user", SecurityUtils.getUserId());
        Optional<GmailAliasBindingStatus> bindingStatus = this.getBindingStatusByUserId();
        String from = this.findCurrentUserEmail();
        if (bindingStatus.isEmpty()) {
            log.info("[UserEmailServiceImpl: requestToBindingGmailAliasForCurrentUser @{}] current user binding status is NONE, start binding process", SecurityUtils.getUserId());
            this.requestToBindingGmailAlias(from);
            return "REQUESTED";
        } else {
            GmailAliasBindingStatus status = bindingStatus.get();
            log.info("[UserEmailServiceImpl: requestToBindingGmailAliasForCurrentUser @{}] current user binding status is {}", SecurityUtils.getUserId(), status.name());
            if (GmailAliasBindingStatus.PENDING.equals(status)) {
                log.info("[UserEmailServiceImpl: requestToBindingGmailAliasForCurrentUser @{}] start confirm binding process", SecurityUtils.getUserId());
                try {
                    this.confirmBindingGmailAlias(from);
                } catch (Exception e) {
                    if (e instanceof CustomParameterizedException) {
                        if (428 == ((CustomParameterizedException) e).getStatus().getStatusCode()) {
                            return "REQUESTED";
                        }
                        throw e;
                    }
                }

                return "CONFIRMED";
            }
            return "INVALID";
        }
    }

    @Override
    public void requestToBindingGmailAlias(String email) {
        log.info("[UserEmailServiceImpl: requestToBindingGmailAlias @{}] request to binding gmail alias for email: {}", SecurityUtils.getUserId(), email);
        email = StringUtils.trimToEmpty(email);
        if (StringUtils.isBlank(email)) {
            log.warn("[UserEmailServiceImpl: requestToBindingGmailAlias @{}] email address is null", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_REQUESTTOBINDINGGMAILALIASEMAILNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

        JSONObject body = new JSONObject();
        body.fluentPut("email", email);
        try {
            HttpResponse response = httpService.post(getRequestBindingUrl(), getRequestHeaders(), JSON.toJSONString(body));


            log.info("[UserEmailServiceImpl: requestToBindingGmailAlias @{}] response code is: {}, response body is: {}", SecurityUtils.getUserId(), response.getCode(), response.getBody());

            JSONObject resBody = JSON.parseObject(response.getBody());

            if (GmailAliasBindingStatus.NONE.equals(this.getBindingStatusForCurrentUser())) {
                if (response.getCode() == 200 || response.getCode() == 450) {
                    String rawGmailAccount = resBody.getString("gmail_account");
                    UserGmailAliasBinding userGmailAliasBinding = new UserGmailAliasBinding();
                    userGmailAliasBinding.setUserId(SecurityUtils.getUserId());
                    userGmailAliasBinding.setBindingStatus(GmailAliasBindingStatus.PENDING);
                    userGmailAliasBinding.setRawGmailAccount(rawGmailAccount);
                    userGmailAliasRepository.saveAndFlush(userGmailAliasBinding);
                } else if (response.getCode() == 400) {
                    int errorCode = resBody.getIntValue("errorCode");
                    if (errorCode == 450) {
                        JSONObject errorMsg = resBody.getJSONObject("errorMsg");
                        String rawGmailAccount = errorMsg.getString("gmail_account");
                        UserGmailAliasBinding userGmailAliasBinding = new UserGmailAliasBinding();
                        userGmailAliasBinding.setUserId(SecurityUtils.getUserId());
                        userGmailAliasBinding.setBindingStatus(GmailAliasBindingStatus.PENDING);
                        userGmailAliasBinding.setRawGmailAccount(rawGmailAccount);
                        userGmailAliasRepository.saveAndFlush(userGmailAliasBinding);
                    } else {
                        log.error("[UserEmailServiceImpl: requestToBindingGmailAlias] response error with code: {}, body: {}, and end here with userId: {}", response.getCode(), response.getBody(), SecurityUtils.getUserId());
                        throw new CustomParameterizedException("Error when request to bind gmail alias!");
                    }
                } else {
                    log.error("[UserEmailServiceImpl: requestToBindingGmailAlias] response error with code: {}, with body: {}", response.getCode(), response.getBody());
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_REQUESTTOBINDINGGMAILALIASERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
                }
            }
        } catch (IOException e) {
            log.error("[UserEmailServiceImpl: requestToBindingGmailAlias] IOException");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_REQUESTTOBINDINGGMAILALIASIOERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    @Override
    public void confirmBindingGmailAliasForCurrentUser() {
        log.info("[UserEmailServiceImpl: confirmBindingGmailAliasForCurrentUser @{}] request to confirm gmail alias for current user", SecurityUtils.getUserId());
        String email = this.findCurrentUserEmail();
        this.confirmBindingGmailAlias(email);
    }

    @Override
    public void confirmBindingGmailAlias(String email) {
        log.info("[UserEmailServiceImpl: confirmBindingGmailAlias @{}] request to confirm gmail alias for email: {}", SecurityUtils.getUserId(), email);
        email = StringUtils.trimToEmpty(email);
        if (StringUtils.isBlank(email)) {
            log.warn("[UserEmailServiceImpl: confirmBindingGmailAlias @{}] email address is null", SecurityUtils.getUserId());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_REQUESTTOBINDINGGMAILALIASEMAILNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }

//        UserGmailAliasBinding aliasBinding = userGmailAliasRepository.findByUserId(SecurityUtils.getUserId()).orElseThrow(() -> new CustomParameterizedException("Please request to bind gmail-alias first, then refresh to confirm!"));
        List<UserGmailAliasBinding> bindingList = userGmailAliasRepository.findAllByUserId(SecurityUtils.getUserId());
        if (bindingList.isEmpty()) {
            log.warn("[UserEmailServiceImpl: confirmBindingGmailAlias @{}] cannot find any request binding record", SecurityUtils.getUserId());
            throw new CustomParameterizedException("Please request to bind gmail-alias first, then refresh to confirm!");
        }

//        boolean allConfirmed = bindingList.stream().allMatch(b -> GmailAliasBindingStatus.CONFIRMED.equals(b.getBindingStatus()));
//        if (allConfirmed) {
//            throw new CustomParameterizedException("All gmail-alias have been confirmed!");
//        }
        List<UserGmailAliasBinding> pendingList = bindingList.stream().filter(b -> GmailAliasBindingStatus.PENDING.equals(b.getBindingStatus())).collect(Collectors.toList());
//        if (pendingList.isEmpty()) {
//            throw new CustomParameterizedException("All gmail-alias have been confirmed!");
//        }
        UserGmailAliasBinding aliasBinding = pendingList.stream().findAny().orElseThrow(() -> new CustomParameterizedException("All gmail-alias have been confirmed!"));

        JSONObject body = new JSONObject();
        body.fluentPut("email", email)
                .fluentPut("rawGmailAccount", aliasBinding.getRawGmailAccount());

        try {
            HttpResponse response = httpService.post(getConfirmBindingUrl(), getRequestHeaders(), JSON.toJSONString(body));

            JSONObject resBody = JSON.parseObject(response.getBody());
            if (response.getCode() == 200) {
                aliasBinding.setBindingStatus(GmailAliasBindingStatus.CONFIRMED);
                userGmailAliasRepository.saveAndFlush(aliasBinding);
            } else {
                log.error("[UserEmailServiceImpl: confirmBindingGmailAlias] response error with body: {}", response.getBody());
                throw new CustomParameterizedException(428, "Gmail-Alias Binding Exception", resBody.getString("errorMsg"), new HashMap<>());
            }
        } catch (IOException e) {
            log.error("[UserEmailServiceImpl: confirmBindingGmailAlias] IOException");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_CONFIRMBINDINGGMAILALIASIOERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));
        }
    }

    private Headers getRequestHeaders() {
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put(HEADERS_AUTHORIZATION_KEY, TOKEN_TYPE + SPACE + currentUserToken);
        headersBuilder.put(EmailConstans.HEADER_CLIENT_ID, ClientTokenHolder.getInstance().getClientId());
        if (SecurityUtils.getUserUid() != null) {
            headersBuilder.put(EmailConstans.HEADER_UID, SecurityUtils.getUserUid());
        }
        return Headers.of(headersBuilder);
    }

    private String findCurrentUserEmail() {
        return this.findUserEmailById(SecurityUtils.getUserId());
    }

    private String findUserEmailById(Long userId) {
        User user = userService.findUserById(userId).getBody();
        if (Objects.isNull(user)) {
            log.error("[UserEmailServiceImpl: findCurrentUserEmail] cannot find current user!");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(CommonAPIMultilingualEnum.EMAIL_USEREMAIL_FINDCURRENTUSEREMAILUSERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),commonApiPromptProperties.getCommonService()));

        }
        String from = user.getEmail();
        return from;
    }

    public void removeAllBindingRecords(Long userId) {
        log.info("[UserEmailServiceImpl: removeAllBindingRecords] remove all binding records by userId: {}", userId);
        userGmailAliasRepository.deleteAllByUserId(userId);
    }

    /**
     * 给特定用户添加备用邮箱
     *
     * @param alternateEmailVM
     */
    @Override
    public void addAlternateEmail(AlternateEmailVM alternateEmailVM) {
        userGmailAliasRepository.setAlternateEmailByUserId(alternateEmailVM.getUserId(), alternateEmailVM.getAlternateEmail());
        appInitForEmailService.initAlternateEmailCache();
    }
}
