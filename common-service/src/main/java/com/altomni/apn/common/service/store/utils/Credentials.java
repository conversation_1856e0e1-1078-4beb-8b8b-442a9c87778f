package com.altomni.apn.common.service.store.utils;

//import io.minio.messages.ResponseDate;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.Objects;
//import org.simpleframework.xml.Element;
//import org.simpleframework.xml.Root;

/** Object representation of credentials access key, secret key and session token. */
//@Root(name = "Credentials", strict = false)
public class Credentials {
//    @Element(name = "AccessKeyId")
    private final String accessKey;

//    @Element(name = "SecretAccessKey")
    private final String secretKey;

//    @Element(name = "SessionToken")
    private final String sessionToken;

//    @Element(name = "Expiration")
//    private final ResponseDate expiration;

    public Credentials(
            @Nonnull String accessKey,
            @Nonnull String secretKey,
            @Nullable String sessionToken
//            @Nullable ResponseDate expiration
    ) {
        this.accessKey = Objects.requireNonNull(accessKey, "AccessKey must not be null");
        this.secretKey = Objects.requireNonNull(secretKey, "SecretKey must not be null");
        if (accessKey.isEmpty() || secretKey.isEmpty()) {
            throw new IllegalArgumentException("AccessKey and SecretKey must not be empty");
        }
        this.sessionToken = sessionToken;
//        this.expiration = expiration;
    }

    public String accessKey() {
        return accessKey;
    }

    public String secretKey() {
        return secretKey;
    }

    public String sessionToken() {
        return sessionToken;
    }

//    public boolean isExpired() {
//        if (expiration == null) {
//            return false;
//        }
//
//        return ZonedDateTime.now().plus(Duration.ofSeconds(10)).isAfter(expiration.zonedDateTime());
//    }
}
