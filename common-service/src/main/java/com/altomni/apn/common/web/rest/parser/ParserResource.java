package com.altomni.apn.common.web.rest.parser;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.parser.ResumeParseInfo;
import com.altomni.apn.common.domain.parser.enumeration.ParseType;
import com.altomni.apn.common.dto.redis.ParserResponse;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.parser.ParserService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.micrometer.core.annotation.Timed;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * REST controller for managing ParseRecord.
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3/parsers")
public class ParserResource {

    private final Logger log = LoggerFactory.getLogger(ParserResource.class);

    @Resource
    private ParserService parserService;

    @PostMapping("/jd/status")
    @Timed
    public ResponseEntity<ParserResponse> checkJdParseResultOrGetUploadUrl(@RequestParam("uuid") String uuid, @RequestParam(value = "fileName", required = false) String fileName, @RequestParam(value = "contentType", required = false) String contentType, @RequestParam(value = "jdText", required = false) String jdText) {
        log.info("[APN: Parser @{}] REST request to check if we already parsed the jd and still have parse result or generating pre-signed upload url, with uuid: {}, fileName: {}, contentType: {}", SecurityUtils.getUserId(), uuid, fileName, contentType);
        return ResponseEntity.ok(parserService.checkParseJdStatusOrGetUploadUrl(uuid, fileName, contentType, jdText));
    }

    @GetMapping("/jd/result-status/{uuid}")
    @Timed
    public ResponseEntity<ParserResponse> getJdParseResultStatusOnly(@PathVariable("uuid") String uuid) {
        log.info("[APN: Parser @{}] REST request to get jd parse result status only for uuid: {}", SecurityUtils.getUserId(), uuid);
        return ResponseEntity.ok(parserService.getParseResultStatus(uuid, ParseType.JD));
    }

    @GetMapping("/jd/result/{uuid}")
    @Timed
    public ResponseEntity<ParserResponse> getParserJdResult(@PathVariable("uuid") String uuid) {
        log.info("[APN: Parser @{}] REST request to get parsed jd result from redis with uuid: {}", SecurityUtils.getUserId(), uuid);
        return ResponseEntity.ok(parserService.getParserJdResult(uuid));
    }

    @GetMapping("/resume/status")
    @Timed
    public ResponseEntity<ParserResponse> checkResumeParseResultOrGetUploadUrl(@RequestParam("uuid") String uuid, @RequestParam("priority") Integer priority, @RequestParam("fileName") String fileName, @RequestParam("contentType") String contentType) {
        log.info("[APN: Parser @{}] REST request to check if we already parsed the resume and still have parse result or generating pre-signed upload url, with uuid: {}, filename: {}, contentType: {} and priority: {}", SecurityUtils.getUserId(), uuid, fileName, contentType, priority);
        if (StringUtils.isBlank(uuid)) {
            throw new CustomParameterizedException("UUID cannot be null");
        }
        if (StringUtils.isBlank(fileName)) {
            throw new CustomParameterizedException("FileName cannot be empty");
        }
        return ResponseEntity.ok(parserService.checkParseResumeStatusOrGetUploadUrl(uuid, fileName, contentType, priority));
    }

    @GetMapping("/resume/result-status/{uuid}")
    @Timed
    public ResponseEntity<ParserResponse> getResumeParseResultStatusOnly(@PathVariable("uuid") String uuid) {
        log.info("[APN: Parser @{}] REST request to get jd parse result status only for uuid: {}", SecurityUtils.getUserId(), uuid);
        return ResponseEntity.ok(parserService.getParseResultStatus(uuid, ParseType.RESUME));
    }

    @PostMapping("/resume/parse-info/redis")
    @Timed
    public ResponseEntity<Void> putResumeParseInfoToRedis(@RequestBody ResumeParseInfo resumeParseInfo) {
        log.info("[APN: Parser @{}] REST request to put resume parse info to redis: {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(resumeParseInfo));
        parserService.initializeRedisForResumeParser(resumeParseInfo.getUuid(), resumeParseInfo.getFilename(), resumeParseInfo.getContentType(), resumeParseInfo.getPriority());
        return ResponseEntity.ok().build();
    }

    /**
     * get parse result (return status and parsed data)
     *
     * @return parse result
     */
    @GetMapping("/resume/result/{uuid}")
    @Timed
    public ResponseEntity<ParserResponse> getParserResumeResult(@PathVariable("uuid") String uuid) {
        log.info("[APN: Parser @{}] " +
                " {}", SecurityUtils.getUserId(), uuid);
        return ResponseEntity.ok(parserService.getParserResumeResult(uuid));
    }

    /**
     * get parse result (return imageInfo and upload status)
     *
     * @return parse result
     */
    @GetMapping("/resume/info/{uuid}")
    @Timed
    public ResponseEntity<ParserResponse> getParserResumeInfo(@PathVariable("uuid") String uuid) {
        log.info("[APN: Parser @{}] REST request to get parsed resume result from redis. uuid: {}", SecurityUtils.getUserId(), uuid);
        return ResponseEntity.ok(parserService.getParserResumeInfo(uuid));
    }

    //APN pro use.
    @GetMapping("/resume/upload-only")
    @Timed
    public ResponseEntity<ParserResponse> uploadResumeOnlyWithoutParsing(@RequestParam("uuid") String uuid, @RequestParam("fileName") String fileName, @RequestParam("contentType") String contentType) {
        log.info("[APN: Parser @{}] REST request to upload a resume only without parsing", SecurityUtils.getUserId());
        return ResponseEntity.ok(parserService.uploadResumeOnly(uuid, fileName, contentType));
    }

    /**
     * download resume file
     * @param uuid: resume file uuid
     * @return parse result
     */
    @GetMapping("/resume/download/{uuid}")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<ParserResponse> downloadResume(@PathVariable("uuid") String uuid, HttpServletResponse response) {
        log.info("[APN: Parser @{}] REST request to download resume. uuid: {}", SecurityUtils.getUserId(), uuid);
        return ResponseEntity.ok(parserService.downloadResume(response, uuid));
    }


    /**
     * migration only
     */
    @PostMapping("/resume/migration/send-sqs-message")
    @Timed
    public ResponseEntity<Void> sendSqsMessage(@RequestBody CloudFileObjectMetadata metadata){
        log.info("[APN: Parser @{}] REST request to send sqs message . uuid: {}", SecurityUtils.getUserId(), metadata.getKey());
        parserService.sendSqsMessage(metadata.getKey(), metadata);
        return new ResponseEntity<>(HttpStatus.OK);
    }



}
