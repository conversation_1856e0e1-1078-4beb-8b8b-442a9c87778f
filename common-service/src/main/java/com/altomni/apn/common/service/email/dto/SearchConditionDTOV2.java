package com.altomni.apn.common.service.email.dto;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.enumeration.search.CommonPoolTalentType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Pageable;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class SearchConditionDTOV2 {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "search condition in json string")
    private List<SearchParam> search;

    private ModuleType module;

    private CommonPoolTalentType commonPoolType;

    private String timeZone;

    private Long jobId;

    private Long talentId;

    private JSONObject filter;

    public static void checkPageable(Pageable pageable) {
        int from = 0;
        int size = 0;
        from = (pageable.getPageNumber() - 1) * pageable.getPageSize() <= -1 ? 0 : (pageable.getPageNumber() - 1) * pageable.getPageSize();
        size = pageable.getPageSize() <= 0 ? 10 : pageable.getPageSize();
    }
}
