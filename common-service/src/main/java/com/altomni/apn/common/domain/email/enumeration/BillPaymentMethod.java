package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum BillPaymentMethod implements ConvertedEnum<Integer> {

    PACKAGES_COSTS(0),
    ADDITIONAL_COSTS(1);

    private final int dbValue;

    BillPaymentMethod(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<BillPaymentMethod, Integer> resolver = new ReverseEnumResolver<>(BillPaymentMethod.class, BillPaymentMethod::toDbValue);

    public static BillPaymentMethod fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
