package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class GmailAliasBindingStatusConverter extends AbstractAttributeConverter<GmailAliasBindingStatus, Integer> {
    public GmailAliasBindingStatusConverter() {
        super(GmailAliasBindingStatus::toDbValue, GmailAliasBindingStatus::fromDbValue);
    }
}
