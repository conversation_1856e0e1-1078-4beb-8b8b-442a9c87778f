package com.altomni.apn.common.web.rest.email;

import com.altomni.apn.common.dto.email.EmailLogOverviewDTO;
import com.altomni.apn.common.dto.email.MailSearchDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.email.CampaignService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.web.rest.email.vm.TalentMailVM;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-EmailService"})
@RestController
@RequestMapping("/api/v3/mail")
public class EmailResource {

    private final Logger log = LoggerFactory.getLogger(EmailResource.class);

    private static final String ENTITY_NAME = "mailService";

    private static final String PIN = "IPG888hq";

    @Resource
    private CampaignService campaignService;

    @ApiOperation(value = "front-end use this api to send emails")
    @PostMapping(value = "/send-mail", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> sendMail(@RequestBody TalentMailVM talentMailVM) throws IOException {
        log.info("[EmailResource: sendMail @{}] REST request to send email, with params TalentMailVM: {}", SecurityUtils.getUserId(), talentMailVM);
        if (CollectionUtils.isNotEmpty(talentMailVM.getTalentIds())) {
            talentMailVM.setCustomerId2(talentMailVM.getTalentIds().stream().map(id -> "APN:TALENT" + id).collect(Collectors.joining(",")));
//            voipTalentNoticeVM.setCustomerId1("APN:TALENT" + voipTalentNoticeVM.getTalentId());
        } else { //for sending email from company page to company client contact
            if (Objects.nonNull(talentMailVM.getAccountCompanyId())) {
                talentMailVM.setCustomerId1("ACCOUNT" + talentMailVM.getAccountCompanyId()); //company client id
            }
            if (CollectionUtils.isNotEmpty(talentMailVM.getAccountContactIds())) {
                talentMailVM.setCustomerId2(talentMailVM.getAccountContactIds().stream().map(id -> "AC" + id).collect(Collectors.joining(","))); //conmpany client contact id
            }
        }
        String res = campaignService.sendHtmlMail(talentMailVM, false, true , true);
        return ResponseEntity.ok(res);
    }

    @PostMapping(value = "/send-rich-mail", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> sendRichMail(@RequestParam(required = false) String from,
                                             @RequestParam List<String> to,
                                             @RequestParam(required = false) List<String> bcc,
                                             @RequestParam(required = false) List<String> cc,
                                             @RequestParam String subject,
                                             @RequestParam String html_content,
                                             @RequestParam(required = false) Boolean transactional,
                                             @RequestParam(required = false) List<MultipartFile> files) throws IOException {
        log.info("[EmailResource: sendRichMail @{}] REST request to send rich email, with params [from:{}, to: {}, bcc: {}, cc: {}, subject: {}, html_content: {}, files(number): {}", SecurityUtils.getUserId(), from, to, bcc, cc, subject, html_content, Objects.isNull(files) ? 0 : files.size());
        campaignService.sendHtmlMail(from, to, cc, bcc, subject, html_content, null, files, false, false, transactional);

        return ResponseEntity.ok().build();
    }

    @PostMapping(value = "/send-rich-mail/system", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> sendRichMailForSystemEmail(HttpServletRequest request,
                                                           @RequestParam(required = false) String from,
                                                           @RequestParam List<String> to,
                                                           @RequestParam(required = false) List<String> bcc,
                                                           @RequestParam(required = false) List<String> cc,
                                                           @RequestParam String subject,
                                                           @RequestParam String html_content,
                                                           @RequestParam(required = false) List<MultipartFile> files) throws IOException {
        String key = request.getHeader("pin");
        log.info("[EmailResource: sendRichMailForSystemEmail @{}] REST request to send rich email (system), with pin: {} and params [from:{}, to: {}, bcc: {}, cc: {}, subject: {}, html_content: {}, files(number): {}", SecurityUtils.getUserId(), key, from, to, bcc, cc, subject, html_content, Objects.isNull(files) ? 0 : files.size());
        if(!PIN.equals(key)) throw new CustomParameterizedException("You are not allowed to do this!");

        campaignService.sendHtmlMail(from, to, cc, bcc, subject, html_content, null, files, true, false, true);

        return ResponseEntity.ok().build();
    }

    @PostMapping(value = "/send-html-mail", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<Void> sendHtmlMail(@RequestBody @Valid MailVM mailVM) throws IOException {
        log.info("[EmailResource: sendHtmlMail @{}] REST request to send html email, with mailVM: {}", SecurityUtils.getUserId(), mailVM);

        campaignService.sendHtmlMail(mailVM);

        return ResponseEntity.ok().build();
    }

    @PostMapping(value = "/search-sent-count-replied", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<List<EmailLogOverviewDTO>> searchSentEmailsAndCountReplied(@RequestBody MailSearchDTO mailSearchDTO) throws IOException {
        log.info("[EmailResource: searchSentEmailsAndCountReplied @{}] REST request to search sent emails and count replied, with mailVM: {}", SecurityUtils.getUserId(), mailSearchDTO);

        List<EmailLogOverviewDTO> res = campaignService.searchSentEmailAndCountReplied(mailSearchDTO);

        return ResponseEntity.ok(res);
    }

    @PostMapping(value = "/search-sent/by-users", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<List<EmailLogOverviewDTO>> searchSentEmailsByUser(@RequestBody MailSearchDTO mailSearchDTO) throws IOException {
        log.info("[EmailResource: searchSentEmailsAndCountReplied @{}] REST request to search sent emails by users, with mailVM: {}", SecurityUtils.getUserId(), mailSearchDTO);

        List<EmailLogOverviewDTO> res = campaignService.searchSentEmailsByUsers(mailSearchDTO);

        return ResponseEntity.ok(res);
    }
}
