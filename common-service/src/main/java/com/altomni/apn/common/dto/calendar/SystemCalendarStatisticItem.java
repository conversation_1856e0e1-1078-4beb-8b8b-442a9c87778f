package com.altomni.apn.common.dto.calendar;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;

@Getter
@Setter
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SystemCalendarStatisticItem implements AttachConfidentialTalent {
    private Long talentId;
    private String talentName;
    private Long companyId;
    private String companyName;
    private Long jobId;
    private String jobName;
    private Boolean jobPrivate;
    private Long contactId;
    private String contactName;

    private Boolean confidentialTalentViewAble = null;
    private ConfidentialInfoDto confidentialInfo;

    public void encrypt() {
        this.talentName = null;
        this.contactName = null;
    }

    public static Function<SystemCalendarStatisticItem, SystemCalendarStatisticItem> processConfidential(Map<Long, ConfidentialInfoDto> confidentialInfoMap, Set<Long> viewAbleConfidentialTalentIds) {
        return item -> {
            SystemCalendarStatisticItem newItem = new SystemCalendarStatisticItem();
            newItem.setTalentId(item.getTalentId());
            newItem.setTalentName(item.getTalentName());
            newItem.setCompanyId(item.getCompanyId());
            newItem.setCompanyName(item.getCompanyName());
            newItem.setJobId(item.getJobId());
            newItem.setJobName(item.getJobName());
            newItem.setJobPrivate(item.getJobPrivate());
            newItem.setContactId(item.getContactId());
            newItem.setContactName(item.getContactName());
            if (item.getTalentId() == null) {
                return newItem;
            }
            if (!confidentialInfoMap.containsKey(item.getTalentId())) {
                return newItem;
            } else {
                newItem.setConfidentialInfo(confidentialInfoMap.get(item.getTalentId()));
            }
            if (!viewAbleConfidentialTalentIds.contains(item.getTalentId())) {
                newItem.setConfidentialTalentViewAble(false);
                newItem.encrypt();
            }
            return newItem;
        };
    }
}
