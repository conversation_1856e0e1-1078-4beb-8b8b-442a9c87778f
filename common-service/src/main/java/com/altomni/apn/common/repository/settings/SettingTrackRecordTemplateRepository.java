package com.altomni.apn.common.repository.settings;

import com.altomni.apn.common.domain.settings.SettingTrackRecordTemplate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface SettingTrackRecordTemplateRepository extends JpaRepository<SettingTrackRecordTemplate, BigInteger> {

    @Modifying
    @Transactional
    @Query(value = "update  setting_track_record_template  set status=?1,last_modified_date=now() where id =?2 ", nativeQuery = true)
    void updateStatusById(Integer status, BigInteger id);

    @Query(value = "select  t.* from setting_track_record_template t where t.temp_type=?1 and t.status=?3  and t.tenant_id=?2",nativeQuery = true)
    List<SettingTrackRecordTemplate> findByTempTypeAndTenantIdAndStatus(Integer tempType, Long tenantId, Integer status);

    @Query(value = "select DISTINCT t.* from setting_track_record_template t\n" +
            "join setting_track_record_relation sr on sr.temp_id = t.id\n" +
            "where t.temp_type=2 and t.status=1 and sr.`status`=1 and sr.user_id=?1 and t.tenant_id=?2",nativeQuery = true)
    List<SettingTrackRecordTemplate> findSettingTrackRecordTemplateByUserId(Long userId,Long tenantId);
}
