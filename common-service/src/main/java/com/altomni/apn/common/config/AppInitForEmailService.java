package com.altomni.apn.common.config;

import com.altomni.apn.common.dto.permission.PermissionPrivilegeVM;
import com.altomni.apn.common.repository.email.UserGmailAliasRepository;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.redis.RedisService;
import com.altomni.apn.common.service.email.impl.UserEmailServiceImpl;
import com.altomni.apn.common.utils.StringUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Component
public class AppInitForEmailService {

//    @Resource
//    private RequestMappingHandlerMapping requestMappingHandlerMapping;

    private final Logger log = LoggerFactory.getLogger(AppInitForEmailService.class);

    @Resource
    private RedisService redisService;

    @Resource
    private UserGmailAliasRepository userGmailAliasRepository;

    @EventListener(ApplicationReadyEvent.class)
    public void initAlternateEmailCache(){

        log.info("[initAlternateEmailCache] start");

        Map<String, String> map = getAlternateEmailFromDB();

        log.info("[initAlternateEmailCache] getAlternateEmailFromDB map size: {}", map.size());

        redisService.hset("email-alternate-set", map);

        Map<String, String> test = redisService.hgetAll("email-alternate-set");

        log.info("[initAlternateEmailCache] getAlternateEmailFromDB map size2: {}", test.size());

    }

    private Map<String, String> getAlternateEmailFromDB() {
        List<Object[]> objects = userGmailAliasRepository.getAlternateEmail();
        if (CollectionUtils.isEmpty(objects)) {
            return new HashMap<>();
        }

        Map<String, String> map = new HashMap<>();
        for (Object[] obj : objects) {
            String email = StringUtil.valueOf(obj[0]);
            String alternateEmail = StringUtil.valueOf(obj[1]);

            map.put(email, alternateEmail);
        }

        return map;
    }

    /*@PostConstruct
    public void initPermissionRule(){
        System.out.println("initPermissionRule");
        // 获得数据权限的规则
        cachePermissionReadOnly.getPermissionRulesByTenantId(4L);
    }*/

    /*@Bean
    public CommandLineRunner CommandLineRunnerBean() {
        return (args) -> {
            System.out.println("Int cache [PermissionRulesByTenantIds]");
            // 获得数据权限的规则
            cachePermissionReadOnly.getPermissionRulesByTenantIdFromCacheOnly(-1L);
            cachePermissionReadOnly.getPermissionRulesByTenantIdFromCacheOnly(4L);
        };
    }*/
}
