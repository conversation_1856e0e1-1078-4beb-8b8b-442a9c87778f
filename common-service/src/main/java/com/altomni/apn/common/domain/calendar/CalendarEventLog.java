package com.altomni.apn.common.domain.calendar;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "calendar_event_log")
public class CalendarEventLog extends AbstractPermissionAuditingEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**  */
    @Column(name = "event_id")
    private Long eventId ;

    @Column(name = "note")
    private String note ;

    @Column(name = "operate_type")
    private String operateType ;
}
