package com.altomni.apn.common.web.rest.email;

import com.altomni.apn.common.domain.email.enumeration.Metric;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.email.StatsService;
import com.altomni.apn.common.utils.HeaderConvertUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-EmailService"})
@RestController
@RequestMapping("/api/v3/stats")
public class StatsResource {

    private final Logger log = LoggerFactory.getLogger(StatsResource.class);

    private static final String ENTITY_NAME = "statsService";

    @Resource
    private StatsService statsService;

    @GetMapping(value = "", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getStats(
            @ApiParam(value = "query time from (UNIX timestamp), default value is '1970/1/1T00:00:00Z'") @RequestParam(required = false, defaultValue = "0") Long from,
            @ApiParam(value = "query time to (UNIX timestamp), default value is '2049/12/31T23:59:59Z'") @RequestParam(required = false, defaultValue = "2524607999") Long to) throws IOException {
        log.info("[EmailStatsResource: getStats @{}] REST request to get current user recent stats, from: {}, to: {}", SecurityUtils.getUserId(), from, to);
        HttpResponse response = statsService.getCampaignStatsByTimeRange(from, to);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/campaigns", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getCampaignsReport(@RequestParam(defaultValue = "") String search, Pageable pageable) throws IOException {
        log.info("[EmailStatsResource: getCampaignsReport @{}] REST request to get current user campaign history", SecurityUtils.getUserId());
//        Page<CampaignStatsDTO> campaignsReport = campaignStatsService.getCampaignsReport4(search, pageable);
//        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(campaignsReport, "/stats/campaigns");
//        return new ResponseEntity<>(campaignsReport.getContent(), headers, HttpStatus.OK);

        HttpResponse response = statsService.getCampaignsReport(search, pageable);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/details/{campaignId}", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<String> getCampaignsStatsDetails(@PathVariable Long campaignId, @RequestParam Metric metric) throws IOException {
        log.info("[EmailStatsResource: getCampaignsStatsDetails @{}] REST request to get campaign details for campaignId: {} in metric: {}", SecurityUtils.getUserId(), campaignId, metric.toString());

        HttpResponse response = statsService.getCampaignsStatsDetails(campaignId, metric);
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

}
