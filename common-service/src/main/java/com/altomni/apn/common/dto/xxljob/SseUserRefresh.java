package com.altomni.apn.common.dto.xxljob;

import lombok.Data;

import java.util.Objects;

@Data
public class SseUserRefresh {
    private Long userId;
    private Long tenantId;

    public SseUserRefresh() {
    }

    public SseUserRefresh(Long tenantId, Long userId) {
        this.tenantId = tenantId;
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SseUserRefresh that = (SseUserRefresh) o;
        return Objects.equals(userId, that.userId) && Objects.equals(tenantId, that.tenantId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, tenantId);
    }
}
