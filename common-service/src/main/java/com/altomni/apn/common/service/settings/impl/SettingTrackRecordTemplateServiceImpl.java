package com.altomni.apn.common.service.settings.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.config.SettingTrackRecordType;
import com.altomni.apn.common.domain.settings.SettingTrackRecordRelation;
import com.altomni.apn.common.domain.settings.SettingTrackRecordTemplate;
import com.altomni.apn.common.dto.settings.SettingTrackRecordCreateDTO;
import com.altomni.apn.common.dto.settings.SettingTrackRecordDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.repository.settings.SettingTrackRecordRelationRepository;
import com.altomni.apn.common.repository.settings.SettingTrackRecordTemplateNativeRepository;
import com.altomni.apn.common.repository.settings.SettingTrackRecordTemplateRepository;
import com.altomni.apn.common.service.settings.SettingTrackRecordTemplateService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.settings.SettingTrackRecordVO;
import com.altomni.apn.common.vo.settings.SettingTrackRecordViewVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class SettingTrackRecordTemplateServiceImpl implements SettingTrackRecordTemplateService {

    @Resource
    SettingTrackRecordTemplateNativeRepository settingTrackRecordTemplateNativeRepository;

    @Resource
    SettingTrackRecordTemplateRepository settingTrackRecordTemplateRepository;

    @Resource
    SettingTrackRecordRelationRepository settingTrackRecordRelationRepository;

    @Override
    public Page<SettingTrackRecordVO> searchSettingTrackRecordList(SettingTrackRecordDTO dto, Pageable pageable) {
        dto.setTenantId(SecurityUtils.getTenantId());
        return settingTrackRecordTemplateNativeRepository.searchSettingTrackRecordList(dto, pageable);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SettingTrackRecordCreateDTO dto) {

        SettingTrackRecordTemplate template = new SettingTrackRecordTemplate();
        template.setStatus(1);
        template.setTempName(dto.getTempName());
        template.setTenantId(BigInteger.valueOf(SecurityUtils.getTenantId()));
        template.setTempRemark(dto.getTempRemark());
        template.setTempContent(JSONUtil.toJsonStr(dto.getTempContent()));
        template.setTempType(dto.getTempType());
        template.setCreatedName(SecurityUtils.getUserName());
        if (null != dto.getUserName() && !dto.getUserName().isEmpty()) {
            String names = StringUtils.join(dto.getUserName(), ",");
            template.setUserName(names);
        }
        settingTrackRecordTemplateRepository.save(template);
        log.info("insert setting track record param:{}", JSONUtil.toJsonStr(template));

        insertTempRelation(dto, template.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modify(SettingTrackRecordCreateDTO dto) {
        if (dto.getId() == null) {
            throw new CustomParameterizedException("Parameters are missing");
        }

        Optional<SettingTrackRecordTemplate> settingTrackRecordTemplate = settingTrackRecordTemplateRepository.findById(dto.getId());
        if (!settingTrackRecordTemplate.isPresent()) {
            throw new CustomParameterizedException("No data found");
        }
        SettingTrackRecordTemplate template = settingTrackRecordTemplate.get();

        if (template.getTempType().equals(SettingTrackRecordType.COMMON_TYPE)) {
            if (!SecurityUtils.isAdmin()) {
                if (!SecurityUtils.getUserId().equals(template.getPermissionUserId())) {
                    throw new CustomParameterizedException("No operation permission");
                }
            }
        }

        template.setTempName(dto.getTempName());
        template.setTenantId(BigInteger.valueOf(SecurityUtils.getTenantId()));
        template.setTempRemark(dto.getTempRemark());
        template.setTempContent(JSONUtil.toJsonStr(dto.getTempContent()));
        template.setTempType(dto.getTempType());
        if (null != dto.getUserName() && !dto.getUserName().isEmpty()) {
            String names = StringUtils.join(dto.getUserName(), ",");
            template.setUserName(names);
        } else {
            template.setUserName(null);
        }
        settingTrackRecordTemplateRepository.save(template);
        log.info("update setting track record param:{}", JSONUtil.toJsonStr(template));

        settingTrackRecordRelationRepository.deleteSettingTrackRecordRelationByTempId(template.getId());
        log.info("delete setting track record relation param:{}", template.getId());

        insertTempRelation(dto, template.getId());
    }

    private void insertTempRelation(SettingTrackRecordCreateDTO dto, BigInteger tempId) {
        if (null != dto.getUserId() && !dto.getUserId().isEmpty()) {
            List<SettingTrackRecordRelation> recordRelationList = new ArrayList<>();
            for (Long id : dto.getUserId()) {
                SettingTrackRecordRelation recordRelation = new SettingTrackRecordRelation();
                recordRelation.setStatus(1);
                recordRelation.setTempId(tempId);
                recordRelation.setUserId(BigInteger.valueOf(id));
                recordRelationList.add(recordRelation);
            }

            if (!recordRelationList.isEmpty()) {
                settingTrackRecordRelationRepository.saveAll(recordRelationList);
                log.info("insert setting track record relation size:{},tempId:{}", recordRelationList.size(), tempId);
            }
        }
    }

    @Override
    public SettingTrackRecordViewVO view(BigInteger id) {
        SettingTrackRecordTemplate template = checkParam(id);
        SettingTrackRecordViewVO vo = new SettingTrackRecordViewVO();
        vo.setId(id);
        vo.setStatus(template.getStatus());
        vo.setTempContent(template.getTempContent());
        vo.setTempType(template.getTempType());
        vo.setTempName(template.getTempName());
        vo.setTempRemark(template.getTempRemark());
        vo.setCreateUserId(template.getPermissionUserId());
        if (null != template.getUserName()) {
            vo.setUserName(Arrays.asList(template.getUserName().split(",")));
        }
        if (template.getTempType().equals(SettingTrackRecordType.PERSONAL_TYPE)) {
            List<SettingTrackRecordRelation> recordRelationList = settingTrackRecordRelationRepository.findSettingTrackRecordRelationByTempId(template.getId());
            if (!recordRelationList.isEmpty()) {
                List<BigInteger> ids = recordRelationList.stream().map(SettingTrackRecordRelation::getUserId).collect(Collectors.toList());
                vo.setUserId(ids);
            }
        }
        return vo;
    }

    private SettingTrackRecordTemplate checkParam(BigInteger id) {
        if (id == null) {
            throw new CustomParameterizedException("Parameters are missing");
        }

        Optional<SettingTrackRecordTemplate> settingTrackRecordTemplate = settingTrackRecordTemplateRepository.findById(id);
        if (!settingTrackRecordTemplate.isPresent()) {
            throw new CustomParameterizedException("No data found");
        }

        return settingTrackRecordTemplate.get();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(BigInteger id) {

        checkParam(id);

        settingTrackRecordTemplateRepository.deleteById(id);
        log.info("[delete method] delete setting track record param:{}", id);

        settingTrackRecordRelationRepository.deleteSettingTrackRecordRelationByTempId(id);
        log.info("[delete method] delete setting track record relation param:{}", id);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(BigInteger id, Integer status) {

        checkParam(id);

        settingTrackRecordTemplateRepository.updateStatusById(status, id);
        log.info("[update status method] update setting track record param:{}", id);
    }

    @Override
    public List<SettingTrackRecordVO> queryTrackRecordList() {
        SettingTrackRecordDTO dto = new SettingTrackRecordDTO();
        dto.setTenantId(SecurityUtils.getTenantId());
        return settingTrackRecordTemplateNativeRepository.searchMySettingTrackRecordList(dto);
    }

    private void addTrackTemp(SettingTrackRecordTemplate c,List<SettingTrackRecordVO> voList){
        SettingTrackRecordVO vo = new SettingTrackRecordVO();
        vo.setCreatedDate(Timestamp.from(c.getCreatedDate()));
        vo.setStatus(c.getStatus().toString());
        vo.setId(c.getId());
        vo.setTempContent(c.getTempContent());
        vo.setCrowdName(c.getUserName());
        vo.setTempRemark(c.getTempRemark());
        vo.setTempType(c.getTempType().toDbValue()+"");
        vo.setCreateUser(c.getCreatedBy());
        vo.setTempName(c.getTempName());
        voList.add(vo);
    }
}
