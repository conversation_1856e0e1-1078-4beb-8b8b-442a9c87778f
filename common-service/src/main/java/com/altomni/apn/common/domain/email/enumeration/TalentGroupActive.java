package com.altomni.apn.common.domain.email.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum TalentGroupActive implements ConvertedEnum<Integer> {

    INACTIVE(0),
    ACTIVE(1);

    private final int dbValue;

    TalentGroupActive(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TalentGroupActive, Integer> resolver = new ReverseEnumResolver<>(TalentGroupActive.class, TalentGroupActive::toDbValue);

    public static TalentGroupActive fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
