package com.altomni.apn.common.config.env.store;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class StoreProperties {

    @Value("${application.aws.accessKey}")
    private String accessKey;

    @Value("${application.aws.secretKey}")
    private String secretKey;

//    @Value("${application.storeService.contract.region}")
//    private String region;

    @Value("${application.storeService.contract.bucket}")
    private String contractBucket;

}
