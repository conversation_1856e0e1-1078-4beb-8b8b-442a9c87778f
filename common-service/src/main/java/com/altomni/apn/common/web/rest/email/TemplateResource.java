package com.altomni.apn.common.web.rest.email;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.email.enumeration.EmailTemplateType;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.email.TemplateService;
import com.altomni.apn.common.service.email.dto.EmailyTemplateDTO;
import com.altomni.apn.common.service.email.dto.TemplateTagDTO;
import com.altomni.apn.common.utils.HeaderConvertUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * REST controller for managing GeoInfo.
 */
@Api(tags = {"APN-EmailService"})
@RestController
@RequestMapping("/api/v3/template")
public class TemplateResource {

    private final Logger log = LoggerFactory.getLogger(TemplateResource.class);

    private static final String ENTITY_NAME = "templateService";

    @Resource
    private TemplateService templateService;

    @GetMapping(value = "/built-in", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    public ResponseEntity<String> searchBuiltInEmailTemplates(@RequestParam(defaultValue = "") String name, @RequestParam(value = "tagId", required = false) Long tagId, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[TemplateResource: searchBuiltInEmailTemplates @{}] REST request to search built-in templates: {}.", SecurityUtils.getUserId(), name);
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        String sort = "";
        if (!Objects.isNull(order)) {
            sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
//            builder.addQueryParameter(StringUtils.toRootLowerCase(order.getDirection().toString()), order.getProperty());
        }
        HttpResponse response = templateService.searchEmailTemplates(EmailTemplateType.BUILT_IN, tagId, name, null, pageable, sort);
//        return ResponseEntity.ok(response.getBody());

//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/user-saved", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    public ResponseEntity<String> searchUserSavedEmailTemplates(@RequestParam(defaultValue = "") String name, @RequestParam(value = "tagId", required = false) Long tagId, @RequestParam(required = false) List<Long> creatorUserIds, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[TemplateResource: searchUserSavedEmailTemplates @{}] REST request to search user-saved templates: {}.", SecurityUtils.getUserId(), name);
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        String sort = "";
        if (!Objects.isNull(order)) {
            sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
//            builder.addQueryParameter(StringUtils.toRootLowerCase(order.getDirection().toString()), order.getProperty());
        }
        HttpResponse response = templateService.searchEmailTemplates(EmailTemplateType.USER_SAVED, tagId, name, creatorUserIds, pageable, sort);
//        return ResponseEntity.ok(response.getBody());

//        return new ResponseEntity<>(response.getBody(), (MultiValueMap<String, String>) response.getHeaders().toMultimap(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/my-templates", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    public ResponseEntity<String> searchMyTemplates(@RequestParam(defaultValue = "") String name, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[TemplateResource: searchMyTemplates @{}] REST request to search my templates: {}.", SecurityUtils.getUserId(), name);

        HttpResponse response = templateService.searchMyTemplates(name, pageable);

        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/application-templates", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    public ResponseEntity<String> searchApplicationTemplates(@RequestParam(defaultValue = "") String name, @RequestParam(value = "tagId", required = false) Long tagId, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[TemplateResource: searchApplicationTemplates @{}] REST request to search application templates: {}.", SecurityUtils.getUserId(), name);
        HttpResponse response;
//        if (Objects.isNull(tagId)) {
//            Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
//            String sort = "";
//            if (!Objects.isNull(order)) {
//                sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
////            builder.addQueryParameter(StringUtils.toRootLowerCase(order.getDirection().toString()), order.getProperty());
//            }
//            response = templateService.searchEmailTemplates(EmailTemplateType.USER_SAVED, tagId, name, pageable, sort);
//        } else {
            response = templateService.searchApplicationTemplates(tagId, name, pageable);
//        }
//        return ResponseEntity.ok(response.getBody());
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/email-blast", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    public ResponseEntity<String> searchEmailBlastTemplates(@RequestParam(defaultValue = "") String name, @RequestParam(required = false) List<Long> creatorUserIds, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[TemplateResource: searchEmailBlastTemplates @{}] REST request to search email blast templates: {}.", SecurityUtils.getUserId(), name);
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        String sort = "";
        if (!Objects.isNull(order)) {
            sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
//            builder.addQueryParameter(StringUtils.toRootLowerCase(order.getDirection().toString()), order.getProperty());
        }
        HttpResponse response = templateService.searchEmailTemplates(EmailTemplateType.USER_SAVED, 13L, name, creatorUserIds, pageable, sort);
//        return ResponseEntity.ok(response.getBody());
//        return new ResponseEntity<>(response.getBody(), getHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/email-merge-contacts", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    public ResponseEntity<String> searchEmailMergeContactsTemplates(@RequestParam(defaultValue = "") String name, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) throws IOException {
        log.info("[TemplateResource: searchEmailMergeContactsTemplates @{}] REST request to search email merge contacts templates: {}.", SecurityUtils.getUserId(), name);
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        String sort = "";
        if (!Objects.isNull(order)) {
            sort += order.getProperty() + "," + StringUtils.toRootLowerCase(order.getDirection().toString());
//            builder.addQueryParameter(StringUtils.toRootLowerCase(order.getDirection().toString()), order.getProperty());
        }
        HttpResponse response = templateService.searchEmailTemplates(EmailTemplateType.USER_SAVED, 14L, name, null, pageable, sort);
//        return ResponseEntity.ok(response.getBody());
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

//    @PostMapping("/search")
//    @Timed
//    @NoRepeatSubmit
//    public ResponseEntity<List<EmailyTemplate>> searchEmailTemplates(@Valid @RequestBody EmailTemplateSearchDTO emailTemplateSearchDTO) throws IOException {
//        log.info("[Hitalent to B: Email template @{}] REST request to search email templates: {}.", SecurityUtils.getUserId(), emailTemplateSearchDTO);
//        List<EmailyTemplate> templateList = templateService.searchEmailTemplates(emailTemplateSearchDTO);
//        return new ResponseEntity<>(templateList, HttpStatus.OK);
//    }

    @GetMapping(value = "/tags", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    public ResponseEntity<String> searchEmailTemplateTags() throws IOException {
        log.info("[TemplateResource: searchEmailTemplateTags @{}] REST request to get all email template tags.", SecurityUtils.getUserId());
//        List<EmailyTemplateTags> tagsList = templateService.searchEmailTemplateTags();
//        return new ResponseEntity<>(tagsList, HttpStatus.OK);
        HttpResponse response = templateService.getAllEmailTemplateTags();
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @GetMapping(value = "/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    public ResponseEntity<String> getTemplateById(@PathVariable Long id) throws IOException {
        log.info("[TemplateResource: getTemplateById @{}] REST request to get email template by id: {}.", SecurityUtils.getUserId(), id);
        HttpResponse response = templateService.getTemplateById(id);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> createEmailTemplates(@Valid @RequestBody EmailyTemplateDTO templateDTO) throws IOException {
        log.info("[TemplateResource: createEmailTemplates @{}] REST request to create email templates: {}.", SecurityUtils.getUserId(), templateDTO);
//        EmailyTemplate createEmailyTemplate = templateServiceV2.createEmailTemplates(templateDTO, false);
//        return new ResponseEntity<>(createEmailyTemplate, HttpStatus.CREATED);
        HttpResponse response = templateService.createEmailTemplate(templateDTO);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/application-templates", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> createApplicationTemplates(@Valid @RequestBody EmailyTemplateDTO templateDTO) throws IOException {
        log.info("[TemplateResource: createApplicationTemplates @{}] REST request to create application templates: {}.", SecurityUtils.getUserId(), templateDTO);
//        EmailyTemplate createEmailyTemplate = templateServiceV2.createEmailTemplates(templateDTO, false);
//        return new ResponseEntity<>(createEmailyTemplate, HttpStatus.CREATED);
        if (CollectionUtils.isEmpty(templateDTO.getTags())) {
            throw new CustomParameterizedException("No tag!");
        }
        HttpResponse response = templateService.createEmailTemplate(templateDTO);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/email-blast", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> createEmailBlastTemplates(@Valid @RequestBody EmailyTemplateDTO templateDTO) throws IOException {
        log.info("[TemplateResource: createEmailBlastTemplates @{}] REST request to create email blast templates: {}.", SecurityUtils.getUserId(), templateDTO);
//        EmailyTemplate createEmailyTemplate = templateServiceV2.createEmailTemplates(templateDTO, false);
//        return new ResponseEntity<>(createEmailyTemplate, HttpStatus.CREATED);
        templateDTO.setTags(Collections.singleton(new TemplateTagDTO(13L)));
        HttpResponse response = templateService.createEmailTemplate(templateDTO);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PostMapping(value = "/email-merge-contacts", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> createEmailMergeContactsTemplates(@Valid @RequestBody EmailyTemplateDTO templateDTO) throws IOException {
        log.info("[TemplateResource: createEmailMergeContactsTemplates @{}] REST request to create email merge contacts templates: {}.", SecurityUtils.getUserId(), templateDTO);
//        EmailyTemplate createEmailyTemplate = templateServiceV2.createEmailTemplates(templateDTO, false);
//        return new ResponseEntity<>(createEmailyTemplate, HttpStatus.CREATED);
        templateDTO.setTags(Collections.singleton(new TemplateTagDTO(14L)));
        HttpResponse response = templateService.createEmailTemplate(templateDTO);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PutMapping(value = "/application-templates/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> updateApplicationTemplates(@PathVariable Long id, @Valid @RequestBody EmailyTemplateDTO templateDTO) throws IOException {
        log.info("[TemplateResource: updateApplicationTemplates @{}] REST request to update application templates: {}.", SecurityUtils.getUserId(), templateDTO);
//        EmailyTemplate updateEmailyTemplate = templateServiceV2.updateEmailTemplates(id, templateDTO);
//        return new ResponseEntity<>(updateEmailyTemplate, HttpStatus.CREATED);
        if (CollectionUtils.isEmpty(templateDTO.getTags())) {
            throw new CustomParameterizedException("No tag!");
        }
        HttpResponse response = templateService.updateEmailTemplate(id, templateDTO);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PutMapping(value = "/email-blast/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> updateEmailBlastTemplates(@PathVariable Long id, @Valid @RequestBody EmailyTemplateDTO templateDTO) throws IOException {
        log.info("[TemplateResource: updateEmailBlastTemplates @{}] REST request to update email blast templates: {}.", SecurityUtils.getUserId(), templateDTO);
//        EmailyTemplate updateEmailyTemplate = templateServiceV2.updateEmailTemplates(id, templateDTO);
//        return new ResponseEntity<>(updateEmailyTemplate, HttpStatus.CREATED);
        templateDTO.setTags(Collections.singleton(new TemplateTagDTO(13L)));
        HttpResponse response = templateService.updateEmailTemplate(id, templateDTO);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PutMapping(value = "/email-merge-contacts/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> updateEmailMergeContactsTemplates(@PathVariable Long id, @Valid @RequestBody EmailyTemplateDTO templateDTO) throws IOException {
        log.info("[TemplateResource: updateEmailMergeContactsTemplates @{}] REST request to update email merge contacts templates: {}.", SecurityUtils.getUserId(), templateDTO);
//        EmailyTemplate updateEmailyTemplate = templateServiceV2.updateEmailTemplates(id, templateDTO);
//        return new ResponseEntity<>(updateEmailyTemplate, HttpStatus.CREATED);
        templateDTO.setTags(Collections.singleton(new TemplateTagDTO(14L)));
        HttpResponse response = templateService.updateEmailTemplate(id, templateDTO);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

    @PutMapping(value = "/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<String> updateEmailTemplates(@PathVariable Long id, @Valid @RequestBody EmailyTemplateDTO templateDTO) throws IOException {
        log.info("[TemplateResource: updateEmailTemplates @{}] REST request to update email templates: {}.", SecurityUtils.getUserId(), templateDTO);
//        EmailyTemplate updateEmailyTemplate = templateServiceV2.updateEmailTemplates(id, templateDTO);
//        return new ResponseEntity<>(updateEmailyTemplate, HttpStatus.CREATED);
        HttpResponse response = templateService.updateEmailTemplate(id, templateDTO);
//        return new ResponseEntity<>(response.getBody(), HttpStatus.resolve(response.getCode()));
        return new ResponseEntity<>(response.getBody(), HeaderConvertUtil.convertHeaders(response.getHeaders()), HttpStatus.resolve(response.getCode()));
    }

//    @PostMapping("/save/{id}")
//    @Timed
//    @NoRepeatSubmit
//    public ResponseEntity<EmailyTemplate> saveEmailTemplate(@PathVariable Long id, @Valid @RequestBody EmailyTemplateDTO templateDTO) throws IOException {
//        log.info("[Hitalent to B: Email template @{}] REST request to save a built in email template: {}.", SecurityUtils.getUserId(), id);
//        EmailyTemplate createEmailyTemplate = templateService.saveEmailTemplate(id, templateDTO);
//        return new ResponseEntity<>(createEmailyTemplate, HttpStatus.CREATED);
//    }

    @PostMapping(value = "/deactivate", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<HttpStatus> deactivateEmailTemplates(@Valid @RequestBody List<Long> templateIds) throws IOException {
        log.info("[TemplateResource: deactivateEmailTemplates @{}] REST request to deactivate email templates: {}.", SecurityUtils.getUserId(), templateIds);
        templateService.deactivateTemplateByIds(templateIds);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @DeleteMapping(value = "/{id}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<HttpStatus> deleteEmailTemplateById(@PathVariable Long id) throws IOException {
        log.info("[TemplateResource: deleteEmailTemplateById @{}] REST request to delete email template by id: {}.", SecurityUtils.getUserId(), id);
        templateService.deleteTemplateById(id);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

}
