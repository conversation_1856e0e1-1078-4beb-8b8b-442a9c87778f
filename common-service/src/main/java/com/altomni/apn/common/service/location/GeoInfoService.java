package com.altomni.apn.common.service.location;

import com.altomni.apn.common.service.location.dto.CityProvinceSearchDTO;
import com.altomni.apn.common.service.location.dto.GeoInfoVO;

import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing GeoInfoEN.
 */
public interface GeoInfoService {

    /**
     * Get all the GeoInfoENs by list of Country Code.
     *
     * @return the list of entities
     */
    List<GeoInfoVO> findAllByCountryCode(List<String> countryCodes);


    /**
     * Get the "id" GeoInfoEN.
     *
     * @param id the id of the entity
     * @return the entity
     */
    Optional<GeoInfoVO> findOne(Long id);

    /**
     * Get all the GeoInfoENs by list of Country Code.
     *
     * @return the list of entities
     */
    List<CityProvinceSearchDTO> searchByCity(String city, String countryCode);

    List<GeoInfoVO> searchGeoInfo(String search, Integer item, String countryCode);

    List<GeoInfoVO> searchByCityOrState(String search);
}
