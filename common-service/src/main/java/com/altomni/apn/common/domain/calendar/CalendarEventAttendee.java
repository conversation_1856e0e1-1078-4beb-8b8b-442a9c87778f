package com.altomni.apn.common.domain.calendar;

import com.altomni.apn.common.domain.enumeration.calendar.*;
import lombok.Data;

import javax.persistence.*;
import java.time.Instant;

@Data
@Entity
@Table(name = "calendar_event_attendee")
public class CalendarEventAttendee {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "event_id")
    private Long eventId;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "is_organizer")
    @Convert(converter = CalendarEventAttendeeTypeEnumConverter.class)
    private CalendarEventAttendeeTypeEnum isOrganizer;

    @Column(name = "is_reminder")
    @Convert(converter = CalendarEventAttendeeReminderTypeConverter.class)
    private CalendarEventAttendeeReminderTypeEnum isReminder;

    /** 0待完成 1已完成 2 逾期 */
    @Column(name = "status")
    @Convert(converter = CalendarStatusConverter.class)
    private CalendarStatusEnum status ;

    @Column(name="completed_time")
    private Instant completedTime;
}
