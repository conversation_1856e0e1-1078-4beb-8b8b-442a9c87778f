package com.altomni.apn.common.service.location.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.altomni.apn.common.domain.location.GeoInfoCN;
import com.altomni.apn.common.domain.location.GeoInfoEN;
import com.altomni.apn.common.repository.location.GeoInfoCNRepository;
import com.altomni.apn.common.repository.location.GeoInfoENRepository;
import com.altomni.apn.common.service.location.GeoInfoService;
import com.altomni.apn.common.service.location.dto.CityProvinceSearchDTO;
import com.altomni.apn.common.service.location.dto.GeoInfoVO;
import liquibase.pro.packaged.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing GeoInfoEN.
 */
@Service
@Transactional
public class GeoInfoServiceImpl implements GeoInfoService {

    @Resource
    private EntityManager entityManager;

    private final Logger log = LoggerFactory.getLogger(GeoInfoServiceImpl.class);

    private final GeoInfoCNRepository geoInfoCNRepo;
    private final GeoInfoENRepository geoInfoENRepo;

    public GeoInfoServiceImpl(GeoInfoCNRepository geoInfoCNRepo, GeoInfoENRepository geoInfoENRepo) {
        this.geoInfoCNRepo = geoInfoCNRepo;
        this.geoInfoENRepo = geoInfoENRepo;
    }

    /**
     * Get all the GeoInfoENs.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public List<GeoInfoVO> findAllByCountryCode(List<String> countryCodes) {
        log.debug("Request to get all GeoInfos : " + countryCodes.toString());

        List<Object[]> geoInfos = geoInfoENRepo.findAllGeoInfoByCountryCode(countryCodes);
        List<GeoInfoVO> geoInfoDTOS = new ArrayList<>();
        for (Object[] objects : geoInfos) {
            GeoInfoVO geoInfoVO = new GeoInfoVO();

            geoInfoVO.setCityId(Long.parseLong(String.valueOf(objects[0])));
            if(objects[1]!=null) {
                geoInfoVO.setProvinceCode(String.valueOf(objects[1]));
            }
            if(objects[2]!=null) {
                geoInfoVO.setProvince(String.valueOf(objects[2]));
            }
            if(objects[3]!=null) {
                geoInfoVO.setCountryCode(String.valueOf(objects[3]));
            }
            if(objects[4]!=null) {
                geoInfoVO.setCountry(String.valueOf(objects[4]));
            }
            if(objects[5]!=null) {
                geoInfoVO.setCityCN(String.valueOf(objects[5]));
            }
            if(objects[6]!=null) {
                geoInfoVO.setProvinceCN(String.valueOf(objects[6]));
            }
            if(objects[7]!=null) {
                geoInfoVO.setCountryCN(String.valueOf(objects[7]));
            }

            geoInfoDTOS.add(geoInfoVO);
        }
//        List<GeoInfoDTO> geoInfoDTOS = geoInfoENRepo.findAllGeoInfo();
        return geoInfoDTOS;
    }


    /**
     * Get one GeoInfoEN by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<GeoInfoVO> findOne(Long id) {
        log.debug("Request to get GeoInfoEN : {}", id);
//        return Optional.ofNullable(geoInfoCNRepository.findOne(id));
        return Optional.empty();
    }

    @Override
    @Transactional(readOnly = true)
    public List<CityProvinceSearchDTO> searchByCity(String city, String countryCode) {
        log.debug("Request to search GeoInfo by start characters of city : {}", city);
        List<CityProvinceSearchDTO> res = new ArrayList<>();

        List<Object[]> queryRes = geoInfoENRepo.searchCityNProvinceByCity(city, countryCode);
        for (Object[] objs: queryRes) {
            CityProvinceSearchDTO cityProvDTO = new CityProvinceSearchDTO();
            cityProvDTO.setCityId(Long.parseLong(String.valueOf(objs[0])));
            cityProvDTO.setCityNprovince(String.valueOf(objs[1]));
            res.add(cityProvDTO);
        }

        return res;
    }

    @Override
    public List<GeoInfoVO> searchGeoInfo(String search, Integer item,String countryCode) {
        if (item == null) {
            item = 200;
        }
        String[] countryCodeList = null;
        HashMap<List<String>, List<String>> countryProvinceMap = new HashMap<>();
        if(countryCode != null) {
            countryCodeList = countryCode.split(",");
            Arrays.stream(countryCodeList).forEach(country -> {
                List<String> list = new ArrayList<>();
                list.add(country);
                List<String> provinceList = new ArrayList<>();
                //US contains VI(US Virgin Island),PR(Puerto Rico)
                if (country.contains("US")) {
                    list.add("PR");
                    list.add("VI");
                }
//                if (country.contains("CA")) {
//                    provinceList.add("SK");
//                }
                countryProvinceMap.put(list, provinceList);
            });
        }
        List<GeoInfoEN> resultEn;
        if (CollUtil.isNotEmpty(countryProvinceMap)) {
            resultEn = doSearchByMap(search, item, countryProvinceMap, false);
        } else {
            resultEn = geoInfoENRepo.searchGeoInfo(search, item);
        }
        List<GeoInfoCN> resultCn=null;
        boolean isEN=true;
        if (CollectionUtils.isEmpty(resultEn)) {
            if (CollUtil.isNotEmpty(countryProvinceMap)) {
                resultCn = doSearchByMap(search, item, countryProvinceMap, true);
            } else {
                resultCn = geoInfoCNRepo.searchGeoInfo(search, item);
            }
            isEN = false;
        }
        if (CollectionUtils.isEmpty(resultEn) && CollectionUtils.isEmpty(resultCn)){
            return new LinkedList<>();
        }
        List<Long> cityIds = null;
        if (isEN) {
            cityIds = resultEn.stream().map(GeoInfoEN::getCityId).collect(Collectors.toList());
            resultCn =  geoInfoCNRepo.findByIds(cityIds);
        } else {
            cityIds = resultCn.stream().map(GeoInfoCN::getCityId).collect(Collectors.toList());
            resultEn =  geoInfoENRepo.findByIds(cityIds);
        }
        Map<Long, GeoInfoVO> resultMap = new LinkedHashMap<>();
        resultEn.forEach(en ->{
            GeoInfoVO geoInfoVO = new GeoInfoVO();
            geoInfoVO.setCity(en.getCity());
            geoInfoVO.setCountry(en.getCountry());
            geoInfoVO.setProvince(en.getProvince());
            geoInfoVO.setCountryCode(en.getCountryCode());
            geoInfoVO.setProvinceCode(en.getProvinceCode());
            resultMap.put(en.getCityId(), geoInfoVO);
        });
        resultCn.forEach(cn ->{
            GeoInfoVO geoInfoVO = resultMap.get(cn.getCityId());
            geoInfoVO.setCityId(cn.getCityId());
            geoInfoVO.setCityCN(cn.getCity());
            geoInfoVO.setCountryCN(cn.getCountry());
            geoInfoVO.setProvinceCN(cn.getProvince());
            if(geoInfoVO.getCity() != null && geoInfoVO.getCity().trim().length() == 0) {
                geoInfoVO.setCity(null);
            }
            if(geoInfoVO.getProvince() != null && geoInfoVO.getProvince().trim().length() == 0) {
                geoInfoVO.setProvince(null);
            }
            if(geoInfoVO.getCountry() != null && geoInfoVO.getCountry().trim().length() == 0) {
                geoInfoVO.setCountry(null);
            }
            if(geoInfoVO.getCity() != null) {
                geoInfoVO.setSimilarity("city");
            }else if(geoInfoVO.getProvince() != null) {
                geoInfoVO.setSimilarity("province");
            }else if(geoInfoVO.getCountry() != null) {
                geoInfoVO.setSimilarity("country");
            }
            resultMap.put(cn.getCityId(), geoInfoVO);
        });
        return new LinkedList<>(resultMap.values());
    }

    private <T> List<T> doSearchByMap(String search, Integer item, HashMap<List<String>, List<String>> countryProvinceMap, boolean isCn) {
        StringBuilder sb = new StringBuilder();
        String table = isCn? "city_locations_cn": "city_locations_en";
        Class calzz = isCn? GeoInfoCN.class: GeoInfoEN.class;
        sb.append(" SELECT cl.* from ").append(table).append(" cl ")
                .append(" left join favorite_country fc on cl.country_iso_code =fc.country_iso_code ");
        sb.append(" where ");
        HashMap<Integer, Object> paramMap = new HashMap<>();
        paramMap.put(1, search);
        for (Map.Entry<List<String>, List<String>> entry : countryProvinceMap.entrySet()) {
            paramMap.put(paramMap.size() + 1, entry.getKey());
            sb.append("(")
                    .append("(LOCATE(?1, cl.city_name)>0 or LOCATE(?1, cl.country_name)>0 or LOCATE(?1, cl.subdivision_1_name)>0 or LOCATE(?1, cl.continent_name)>0)")
                    .append("  and cl.country_iso_code in ?").append(paramMap.size());
            if (CollUtil.isNotEmpty(entry.getValue())) {
                // todo 临时添加去除sk省份的逻辑
                paramMap.put(paramMap.size() + 1, entry.getValue());
                sb.append(" and cl.subdivision_1_iso_code not in ?").append(paramMap.size());
            }
            sb.append(") ").append(" or ");
        }
        sb = new StringBuilder(sb.substring(0, sb.length() - 3));
        paramMap.put(paramMap.size() + 1, item);
        sb.append("GROUP BY cl.id ")
                .append(" order by fc.favorite_level ASC,(CASE WHEN ( LOCATE(?1, cl.city_name )> 0 ) THEN 4 WHEN ( LOCATE(?1, cl.subdivision_1_name )> 0 ) THEN 3 WHEN ( LOCATE(?1, cl.country_name )> 0 ) THEN 2 END ) DESC ")
                .append(" limit ?").append(paramMap.size());
        Query query = entityManager.createNativeQuery(sb.toString(), calzz);
        paramMap.forEach(query::setParameter);
        return query.getResultList();
    }

    @Override
    public List<GeoInfoVO> searchByCityOrState(String search) {
        log.debug("Request to search GeoInfo by start characters of city or state : {}", search);
        search = search.trim();
        Pattern pattern = Pattern.compile("\\s*,\\s*");
        Matcher matcher = pattern.matcher(search);
        List<Object[]> queryRes;
        List<GeoInfoVO> result = new ArrayList<>();
        if (matcher.find()) {
            queryRes = geoInfoENRepo.searchByCityAndStateOrStateAndCountry(search.substring(0, matcher.start()), search.substring(matcher.end()));
        } else {
            queryRes = geoInfoENRepo.searchByCityOrState(search);
        }
        for (Object[] objs : queryRes) {
            GeoInfoVO dto = new GeoInfoVO();
            if (ObjectUtil.isNotNull(objs[0])) {
                dto.setCityId(Long.parseLong(String.valueOf(objs[0])));
            }
            if (ObjectUtil.isNotNull(objs[1])) {
                dto.setCity(String.valueOf(objs[1]));
            }
            if (ObjectUtil.isNotNull(objs[2])) {
                dto.setProvinceCode(String.valueOf(objs[2]));
            }
            if (ObjectUtil.isNotNull(objs[3])) {
                dto.setProvince(String.valueOf(objs[3]));
            }
            if (ObjectUtil.isNotNull(objs[4])) {
                dto.setCountryCode(String.valueOf(objs[4]));
            }
            if (ObjectUtil.isNotNull(objs[5])) {
                dto.setCountry(String.valueOf(objs[5]));
            }
            if (ObjectUtil.isNotNull(objs[6])) {
                dto.setCountyId(Long.parseLong(String.valueOf(objs[6])));
            }
            if (ObjectUtil.isNotNull(objs[7])) {
                dto.setCounty(String.valueOf(objs[7]));
            }
            if (ObjectUtil.isNotNull(objs[8])) {
                dto.setMetroArea(String.valueOf(objs[8]));
            }
            if (ObjectUtil.isNotNull(objs[9])) {
                dto.setMetroAreaCode(Long.parseLong(String.valueOf(objs[9])));
            }
            if (ObjectUtil.isNotNull(objs[10])) {
                dto.setSimilarity(String.valueOf(objs[10]));
            }
            result.add(dto);
        }
        return result;
    }

}