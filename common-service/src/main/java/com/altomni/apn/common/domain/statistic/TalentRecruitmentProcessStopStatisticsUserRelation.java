package com.altomni.apn.common.domain.statistic;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.*;

@ApiModel(value = "流程停滞涉及用户统计表",description = "")
@Entity
@Table(name="talent_recruitment_process_stop_statistics_user_relation")
@Data
public class TalentRecruitmentProcessStopStatisticsUserRelation extends AbstractAuditingEntity  {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_stop_statistics_id")
    private Long talentRecruitmentProcessStopStatisticsId;

    @Column(name = "user_id")
    private Long userId;

}
