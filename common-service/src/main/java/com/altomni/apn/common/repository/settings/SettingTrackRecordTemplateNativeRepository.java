package com.altomni.apn.common.repository.settings;

import com.altomni.apn.common.domain.config.SettingTrackRecordType;
import com.altomni.apn.common.dto.settings.SettingTrackRecordDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.settings.SettingTrackRecordVO;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Repository
@Slf4j
public class SettingTrackRecordTemplateNativeRepository {

    @Resource
    private EntityManager entityManager;


    /**
     * Paging Query Data
     *
     * @param dto
     * @param pageable
     * @return
     */
    @Transactional(readOnly = true)
    public Page<SettingTrackRecordVO> searchSettingTrackRecordList(SettingTrackRecordDTO dto, Pageable pageable) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder();
        if (SecurityUtils.isAdmin()) {
            dataSql.append(adminSettingTrackRecordSQL(dto));
            dataSql.append(assemblyQueryCondition(dto));
        } else {
            if (null != dto.getTempType()) {
                if (dto.getTempType().equals(SettingTrackRecordType.COMMON_TYPE)) {
                    dataSql.append(commonSettingTrackRecordSQL(dto));
                    dataSql.append(assemblyQueryCondition(dto));
                } else {
                    dataSql.append(personalSettingTrackRecordSQL(dto));
                    dataSql.append(assemblyQueryCondition(dto));
                }
            } else {
                dataSql.append(commonSettingTrackRecordSQL(dto));
                dataSql.append(assemblyQueryCondition(dto));
                dataSql.append(" union ");
                dataSql.append(personalSettingTrackRecordSQL(dto));
                dataSql.append(assemblyQueryCondition(dto));
            }
        }


        dataSql.append("GROUP BY t.id");
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (!Objects.isNull(order)) {
            if (order.getProperty().equals("temp_name")) {
                dataSql.append(" order by CONVERT(t." + order.getProperty() + "  USING GBK) " + order.getDirection());
            } else {
                dataSql.append(" order by " + order.getProperty() + " " + order.getDirection());
            }
        } else {
            dataSql.append(" order by createdDate desc");

        }

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(countSql);

        if (null != dto.getTempName()) {
            dataQuery.setParameter("tempName", "%" + dto.getTempName() + "%");
            countQuery.setParameter("tempName", "%" + dto.getTempName() + "%");
        }

        if (!SecurityUtils.isAdmin()) {
            if (null != dto.getTempType()) {
                if (dto.getTempType().equals(SettingTrackRecordType.PERSONAL_TYPE)) {
                    dataQuery.setParameter("userId", SecurityUtils.getUserId());
                    countQuery.setParameter("userId", SecurityUtils.getUserId());
                }
            } else {
                dataQuery.setParameter("userId", SecurityUtils.getUserId());
                countQuery.setParameter("userId", SecurityUtils.getUserId());
            }
        }

        if (null != dto.getTenantId()) {
            dataQuery.setParameter("tenantId", dto.getTenantId());
            countQuery.setParameter("tenantId", dto.getTenantId());
        }

        if (null != dto.getUserIds() && !dto.getUserIds().isEmpty()) {
            dataQuery.setParameter("user_id", dto.getUserIds());
            countQuery.setParameter("user_id", dto.getUserIds());
        }

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(SettingTrackRecordVO.class));
        dataQuery.setFirstResult((int) pageable.getOffset());
        dataQuery.setMaxResults(pageable.getPageSize());

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<SettingTrackRecordVO> settingTrackRecordVOS = total > pageable.getOffset() ? dataQuery.getResultList() : new ArrayList<>();
        return new PageImpl<>(settingTrackRecordVOS, pageable, total);
    }

    private String adminSettingTrackRecordSQL(SettingTrackRecordDTO dto) {

        StringBuilder dataSql = new StringBuilder("select distinct t.id,t.temp_name as tempName,t.temp_remark as tempRemark,t.user_name as crowdName,\n" +
                "cast(t.temp_type as char) as tempType,cast(t.status as char) as status,t.created_date as createdDate,t.created_name as createUser\n" +
                " from setting_track_record_template t  where 1=1 ");
        if (null != dto.getUserIds() && !dto.getUserIds().isEmpty()) {
            dataSql.append(" and t.puser_id in ( :user_id ) ");
        }
        if (null != dto.getTempType()) {
            dataSql.append(" and t.temp_type =" + dto.getTempType().toDbValue());
        }
        return dataSql.toString();
    }

    private String commonSettingTrackRecordSQL(SettingTrackRecordDTO dto) {

        StringBuilder dataSql = new StringBuilder("select distinct t.id,t.temp_name as tempName,t.temp_remark as tempRemark,t.user_name as crowdName,\n" +
                "cast(t.temp_type as char) as tempType,cast(t.status as char) as status,t.created_date as createdDate,t.created_name as createUser,t.puser_id as createUserId\n" +
                " from setting_track_record_template t  where t.temp_type =1 ");
        if (null != dto.getUserIds() && !dto.getUserIds().isEmpty()) {
            dataSql.append(" and t.puser_id in ( :user_id ) ");
        }
        return dataSql.toString();
    }

    private String personalSettingTrackRecordSQL(SettingTrackRecordDTO dto) {

        StringBuilder dataSql = new StringBuilder("select distinct t.id,t.temp_name as tempName,t.temp_remark as tempRemark,t.user_name as crowdName,\n" +
                "cast(t.temp_type as char) as tempType,cast(t.status as char) as status,t.created_date as createdDate,t.created_name as createUser,t.puser_id as createUserId\n" +
                " from setting_track_record_template t left join setting_track_record_relation rr on rr.temp_id = t.id where t.temp_type =2 ");
        if (null != dto.getUserIds() && !dto.getUserIds().isEmpty()) {
            dataSql.append(" and (t.puser_id in ( :user_id ) or rr.user_id = :userId )");
        } else {
            dataSql.append(" and (t.puser_id =:userId  or rr.user_id = :userId )");
        }
        return dataSql.toString();
    }

    private String assemblyQueryCondition(SettingTrackRecordDTO dto) {
        StringBuilder whereSql = new StringBuilder();
        if (null != dto.getTempName() && null != dto.getTempName()) {
            whereSql.append(" and t.temp_name like :tempName ");
        }

        if (null != dto.getTenantId() && null != dto.getTenantId()) {
            whereSql.append(" and t.tenant_id = :tenantId ");
        }

        return whereSql.toString();
    }

    /**
     * Paging Query Data
     *
     * @param dto
     * @return
     */
    @Transactional(readOnly = true)
    public List<SettingTrackRecordVO> searchMySettingTrackRecordList(SettingTrackRecordDTO dto) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder();
        if (SecurityUtils.isAdmin()) {
            dataSql.append(adminSettingTrackRecordSQL(dto));
            dataSql.append(" and t.status=1");
            dataSql.append(assemblyQueryCondition(dto));
        } else {
            dataSql.append(commonSettingTrackRecordSQL(dto));
            dataSql.append(" and t.status=1 ");
            dataSql.append(assemblyQueryCondition(dto));
            dataSql.append(" union ");
            dataSql.append(personalSettingTrackRecordSQL(dto));
            dataSql.append(" and t.status=1 ");
            dataSql.append(assemblyQueryCondition(dto));
        }

        dataSql.append(" order by createdDate desc");


        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        if (!SecurityUtils.isAdmin()) {
            if (null != dto.getTempType()) {
                if (dto.getTempType().equals(SettingTrackRecordType.PERSONAL_TYPE)) {
                    dataQuery.setParameter("userId", SecurityUtils.getUserId());
                }
            } else {
                dataQuery.setParameter("userId", SecurityUtils.getUserId());
            }
        }

        if (null != dto.getTenantId()) {
            dataQuery.setParameter("tenantId", dto.getTenantId());
        }

        if (null != dto.getUserIds() && !dto.getUserIds().isEmpty()) {
            dataQuery.setParameter("user_id", dto.getUserIds());
        }

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(SettingTrackRecordVO.class));

        return dataQuery.getResultList();
    }
}
