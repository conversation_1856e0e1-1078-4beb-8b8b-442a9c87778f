package com.altomni.apn.common.dto.calendar;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ApplicationFollowUserCount {
    private Integer notSubmitToClientUserCount = 0;
    private Integer submitToClientNotUpdateStatusUserCount = 0;
    private Integer offerPassNotUpdateStatusUserCount = 0;
    private Integer paymentOverdueUserCount = 0;

}
