<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <property name="now" value="now()" dbms="h2"/>
    <property name="now" value="now()" dbms="mysql, mariadb"/>
    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb"/>
    <property name="clobType" value="clob" dbms="h2"/>
    <property name="clobType" value="clob" dbms="mysql, oracle, mssql, mariadb, postgresql"/>
    <property name="uuidType" value="varchar(36)" dbms="h2, mysql, mariadb"/>
    <property name="datetimeType" value="datetime(6)" dbms="mysql, mariadb"/>
    <property name="datetimeType" value="datetime" dbms="oracle, mssql, postgresql, h2"/>

    <include file="/config/liquibase/changelog/email/1666362857655_added_entity_EmailUserGmailAliasBinding.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/location/1666262757656_added_entity_CityLocationsCn.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/location/1666262757656_added_entity_CityLocationsEn.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/location/1666262757666_added_entity_UsGeoInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/location/1666262757666_added_entity_UsMetroAreaInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/location/1668672803663_added_entity_FavoriteCountry.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/parser/1666262757660_added_entity_ParseRecord.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/calendar/20231019200503_added_entity_CalendarEvent.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/calendar/20231019201023_added_entity_CalendarEventAttendee.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/message/20231101200503_added_entity_Message.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/message/20231101201023_added_entity_MessageUserRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/message/20231103151903_added_entity_XxlJobRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/message/20231114111222_added_entity_XxlJobFailRecord.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/upgrade/20240807105300_added_entity_upgrade_notification.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/upgrade/20240807105301_added_entity_upgrade_tenant_relation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/upgrade/20240807105302_added_entity_upgrade_user_relation.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/reportsubscription/20240821101321_added_entity_ReportSubscription.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/reportsubscription/20240821101721_added_entity_ReportSubscriptionRecipient.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/reportsubscription/20240821101821_added_entity_ReportSubscriptionPushMethod.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/statistic/20241028161300_added_entity_TalentRecruitmentProcessStopStatistics.xml" relativeToChangelogFile="false"/>

</databaseChangeLog>
