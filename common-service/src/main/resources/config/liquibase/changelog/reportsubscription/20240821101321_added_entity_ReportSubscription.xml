<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="20240821101321" author="yushan">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="report_subscription"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE report_subscription (
             id bigint AUTO_INCREMENT,
             tenant_id bigint not null comment '租户',
             name VARCHAR ( 255 ) NOT NULL comment        '订阅名称',
             push_time_type TINYINT NULL COMMENT '推送时间类型：1-每日, 2-每周, 3-每月',
             day_of_week TINYINT NULL COMMENT '每周推送，存储具体的周几，1-周一, ..., 7-周日',
             day_of_month TINYINT NULL COMMENT '每月推送，存储具体的日期，1-31',
             send_time TIME NULL COMMENT '具体的发送时间点',
             data_period TINYINT NOT NULL COMMENT '具体发送的数据时间段：1-今天, 2-昨天, 3-本周, 4-上周, 5-本月, 6-上月',
             start_date DATE NULL COMMENT '订阅开始日期',
             end_date DATE NULL COMMENT '订阅结束日期（可为空，表示无限期）',
             report_type TINYINT NOT NULL COMMENT '报表类型：1-by user, 2-by company',
             is_active TINYINT NOT NULL DEFAULT 1 COMMENT '是否生效：0-无效, 1-有效',
             `created_by` VARCHAR ( 50 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
             `created_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
             `last_modified_by` VARCHAR ( 50 ) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
             `last_modified_date` TIMESTAMP NULL DEFAULT NULL,
             `puser_id` bigint DEFAULT NULL,
             `pteam_id` bigint DEFAULT NULL,
             PRIMARY KEY ( `id` )
            ) COMMENT = '报表订阅信息';
        </sql>
    </changeSet>

</databaseChangeLog>
