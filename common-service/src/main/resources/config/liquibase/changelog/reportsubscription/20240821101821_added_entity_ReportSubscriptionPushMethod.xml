<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="20240821101821" author="yushan">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="report_subscription_push_method"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE report_subscription_push_method (
             id bigint AUTO_INCREMENT,
             subscription_id bigint NOT NULL,
             push_method TINYINT NOT NULL COMMENT '推送方式：1-Lark, 2-Email',
             PRIMARY KEY ( id )
            ) COMMENT = '每个订阅的推送方式';
        </sql>
    </changeSet>

</databaseChangeLog>
