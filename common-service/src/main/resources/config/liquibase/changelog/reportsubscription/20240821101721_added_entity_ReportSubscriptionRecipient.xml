<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="20240821101721" author="yushan">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="report_subscription_recipient"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE report_subscription_recipient (
               id bigint AUTO_INCREMENT COMMENT '唯一标识每个接收人',
               subscription_id bigint NOT NULL COMMENT '对应的订阅ID',
               user_id bigint NOT NULL COMMENT '接收人的用户ID',
               PRIMARY KEY ( `id` )
            ) COMMENT = '订阅的接收人信息';
        </sql>
    </changeSet>

</databaseChangeLog>
