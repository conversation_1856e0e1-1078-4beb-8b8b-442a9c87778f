<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="20231019200503" author="yushan">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="calendar_event"/>
            </not>
        </preConditions>
        <createTable tableName="calendar_event">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="type_id" type="TINYINT">
                <constraints nullable="false"/>
            </column>
            <column name="reference_id" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="go_to_id" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="title" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="MEDIUMTEXT"/>
            <column name="start_time" type="TIMESTAMP(3)">
                <constraints nullable="false"/>
            </column>
            <column name="end_time" type="TIMESTAMP(3)">
                <constraints nullable="false"/>
            </column>
            <column name="lark_calendar_id" type="VARCHAR(255)"/>
            <column name="lark_event_id" type="VARCHAR(255)"/>
            <column name="reminder_minutes" type="INT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>
        <createIndex tableName="calendar_event" indexName="index_calendar_event_start_date">
            <column name="start_time"/>
        </createIndex>
        <createIndex tableName="calendar_event" indexName="index_calendar_event_end_date">
            <column name="end_time"/>
        </createIndex>
        <createIndex tableName="calendar_event" indexName="index_calendar_event_relation_id">
            <column name="reference_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
