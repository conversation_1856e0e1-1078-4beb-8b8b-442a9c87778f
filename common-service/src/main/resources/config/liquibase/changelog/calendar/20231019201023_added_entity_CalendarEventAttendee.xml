<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="20231019200503" author="yushan">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="calendar_event_attendee"/>
            </not>
        </preConditions>
        <createTable tableName="calendar_event_attendee">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="event_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="is_organizer" type="tinyint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="is_reminder" type="tinyint" defaultValue="1">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex tableName="calendar_event_attendee" indexName="index_calendar_event_attendee_user_id">
            <column name="user_id"/>
        </createIndex>
        <createIndex tableName="calendar_event_attendee" indexName="index_calendar_event_attendee_event_id">
            <column name="event_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
