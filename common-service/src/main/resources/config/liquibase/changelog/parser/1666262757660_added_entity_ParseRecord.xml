<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263950726-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="parse_record"/>
            </not>
        </preConditions>
        <createTable tableName="parse_record">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="jhi_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="original_file_name" type="VARCHAR(255)"/>
            <column name="original_text" type="MEDIUMTEXT"/>
            <column name="parse_result" type="MEDIUMTEXT"/>
            <column name="is_reviewed" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="note" type="TEXT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="uuid" type="VARCHAR(255)"/>
        </createTable>

        <createIndex indexName="idx_parse_record_created_by" tableName="parse_record">
            <column name="created_by"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
