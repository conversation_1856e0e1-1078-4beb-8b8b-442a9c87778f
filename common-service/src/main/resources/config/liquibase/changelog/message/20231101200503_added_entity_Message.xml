<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="20231101200503" author="yushan">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="message"/>
            </not>
        </preConditions>
        <createTable tableName="message">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="type" type="TINYINT">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="cn_title" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="en_title" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="content" type="MEDIUMTEXT"/>
            <column name="send_time" type="TIMESTAMP(3)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
        <createIndex tableName="message" indexName="idx_message_type">
            <column name="type"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
