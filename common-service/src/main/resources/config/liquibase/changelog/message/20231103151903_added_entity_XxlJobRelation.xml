<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="20231103151903" author="yushan">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="xxl_job_relation"/>
            </not>
        </preConditions>
        <createTable tableName="xxl_job_relation">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="reference_id" type="BIGINT" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="TINYINT">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT" />
            <column name="xxl_job_id" type="int"/>
            <column name="xxl_job_param" type="mediumtext" />
            <column name="send_time" type="TIMESTAMP(3)" />
            <column name="reminder_config" type="VARCHAR(255)" />
            <column name="timezone" type="VARCHAR(50)" />
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
        <createIndex tableName="xxl_job_relation" indexName="idx_xxl_job_relation_xxl_job_id">
            <column name="xxl_job_id"/>
        </createIndex>
        <createIndex tableName="xxl_job_relation" indexName="idx_xxl_job_relation_user_id">
            <column name="user_id"/>
        </createIndex>
        <createIndex tableName="xxl_job_relation" indexName="idx_xxl_job_relation_reference_id">
            <column name="reference_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
