<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet id="20231101201023" author="yushan">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="message_user_relation"/>
            </not>
        </preConditions>
        <createTable tableName="message_user_relation">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="message_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="is_read" type="tinyint" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="is_favorite" type="tinyint" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="is_delete" type="tinyint" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
        <createIndex tableName="message_user_relation" indexName="idx_message_user_relation_user_id">
            <column name="user_id"/>
        </createIndex>
        <createIndex tableName="message_user_relation" indexName="idx_message_user_relation_message_id">
            <column name="message_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
