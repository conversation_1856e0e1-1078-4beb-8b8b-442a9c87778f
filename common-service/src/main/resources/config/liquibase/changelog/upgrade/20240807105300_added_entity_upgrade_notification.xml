<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
                                       http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd
                                       http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="generated" id="20240807105300-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="upgrade_notification"/>
            </not>
        </preConditions>
        <createTable tableName="upgrade_notification">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="link_cn" type="VARCHAR(120)">
                <constraints nullable="true"/>
            </column>
            <column name="link_en" type="VARCHAR(120)">
                <constraints nullable="true"/>
            </column>
            <column name="content_cn" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="content_en" type="TEXT">
                <constraints nullable="true"/>
            </column>
            <column name="version" type="VARCHAR(30)">
                <constraints nullable="true"/>
            </column>
            <column name="upgrade_time" type="TIMESTAMP(3)">
                <constraints nullable="true"/>
            </column>
            <column name="global" type="BIT(1)">
                <constraints nullable="true"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>
            <column name="last_modified_date" type="TIMESTAMP(3)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <createIndex indexName="idx_upgrade_notification_upgrade_time" tableName="upgrade_notification">
            <column name="upgrade_time"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
