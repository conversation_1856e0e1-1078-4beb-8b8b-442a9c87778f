<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd
                                       http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd
                                       http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="generated" id="20240807105301-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="upgrade_tenant_relation"/>
            </not>
        </preConditions>
        <createTable tableName="upgrade_tenant_relation">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="upgrade_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <createIndex indexName="idx_upgrade_tenant_relation_upgrade_id_tenant_id" tableName="upgrade_tenant_relation">
            <column name="upgrade_id"/>
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
