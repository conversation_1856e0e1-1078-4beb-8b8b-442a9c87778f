<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="*************-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="email_user_gmail_alias_binding"/>
            </not>
        </preConditions>
        <createTable tableName="email_user_gmail_alias_binding">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>

            <column name="user_id" type="BIGINT"/>
            <column name="raw_gmail_account" type="VARCHAR(255)" />
            <column defaultValueNumeric="0" name="binding_status" type="INT">
                <constraints nullable="false"/>
            </column>

            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_email_user_gmail_alias_binding_user_id" tableName="email_user_gmail_alias_binding">
            <column name="user_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
