<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264870592-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="us_metro_area_info"/>
            </not>
        </preConditions>
        <createTable tableName="us_metro_area_info">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="metro_area_code" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="metro_area_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="city_locations_en_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="city_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="state_id" type="VARCHAR(10)">
                <constraints nullable="false"/>
            </column>
            <column name="state_name" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column name="county_fips" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="county_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="us_geo_info_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
