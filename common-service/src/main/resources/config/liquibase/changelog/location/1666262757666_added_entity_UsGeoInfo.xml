<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264861100-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="us_geo_info"/>
            </not>
        </preConditions>
        <createTable tableName="us_geo_info">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="city" type="VARCHAR(127)">
                <constraints nullable="false"/>
            </column>
            <column name="city_ascii" type="VARCHAR(127)">
                <constraints nullable="false"/>
            </column>
            <column name="state_id" type="VARCHAR(2)">
                <constraints nullable="false"/>
            </column>
            <column name="state_name" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
            <column name="county_fips" type="INT"/>
            <column name="county_name" type="VARCHAR(31)">
                <constraints nullable="false"/>
            </column>
            <column name="lat" type="DOUBLE"/>
            <column name="lng" type="DOUBLE"/>
            <column name="population" type="INT"/>
            <column name="density" type="INT"/>
            <column name="source" type="VARCHAR(15)"/>
            <column name="military" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="incorporated" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="timezone" type="VARCHAR(30)"/>
            <column name="ranking" type="INT"/>
            <column name="zips" type="VARCHAR(2047)"/>
        </createTable>

        <createIndex indexName="idx_us_city" tableName="us_geo_info">
            <column name="city"/>
        </createIndex>
        <createIndex indexName="idx_us_county" tableName="us_geo_info">
            <column name="county_name"/>
        </createIndex>
        <createIndex indexName="idx_us_state" tableName="us_geo_info">
            <column name="state_name"/>
        </createIndex>
        <createIndex indexName="idx_us_state_id" tableName="us_geo_info">
            <column name="state_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
