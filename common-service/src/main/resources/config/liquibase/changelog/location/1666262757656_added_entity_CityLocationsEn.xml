<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263255470-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="city_locations_en"/>
            </not>
        </preConditions>
        <createTable tableName="city_locations_en">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="locale_code" type="VARCHAR(25)">
                <constraints nullable="false"/>
            </column>
            <column name="continent_code" type="VARCHAR(2)"/>
            <column name="continent_name" type="VARCHAR(13)">
                <constraints nullable="false"/>
            </column>
            <column name="country_iso_code" type="VARCHAR(2)"/>
            <column name="country_name" type="VARCHAR(255)"/>
            <column name="subdivision_1_iso_code" type="VARCHAR(3)"/>
            <column name="subdivision_1_name" type="VARCHAR(255)"/>
            <column name="subdivision_2_iso_code" type="VARCHAR(3)"/>
            <column name="subdivision_2_name" type="VARCHAR(38)"/>
            <column name="city_name" type="VARCHAR(255)"/>
            <column name="metro_code" type="VARCHAR(255)"/>
            <column name="time_zone" type="VARCHAR(30)"/>
            <column name="is_in_european_union" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_city" tableName="city_locations_en">
            <column name="city_name"/>
        </createIndex>
        <createIndex indexName="idx_country" tableName="city_locations_en">
            <column name="country_name"/>
        </createIndex>
        <createIndex indexName="idx_country_code" tableName="city_locations_en">
            <column name="country_iso_code"/>
        </createIndex>
        <createIndex indexName="idx_st" tableName="city_locations_en">
            <column name="subdivision_1_name"/>
        </createIndex>
        <createIndex indexName="idx_st_code" tableName="city_locations_en">
            <column name="subdivision_1_iso_code"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
