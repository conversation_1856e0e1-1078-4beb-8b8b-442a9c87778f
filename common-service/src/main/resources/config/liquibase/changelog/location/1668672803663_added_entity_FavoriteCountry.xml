<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1668672660550-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="favorite_country"/>
            </not>
        </preConditions>
        <createTable tableName="favorite_country">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="country_iso_code" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="favorite_level" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex indexName="idx_favorite_country_favorite_level" tableName="favorite_country">
            <column name="favorite_level"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
