create table permission_privilege
(
    name               varchar(100)                        not null,
    id                 bigint auto_increment
        primary key,
    api                varchar(2000)                       null,
    parent_id          int                                 null,
    leaf               tinyint                             null,
    level              int                                 null,
    is_public          tinyint                             null,
    created_by         varchar(50)                         not null,
    created_date       timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by   varchar(50)                         null,
    last_modified_date timestamp                           null
)
    charset = utf8mb4;

INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('APN', 1, null, -1, 0, 0, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Candidates', 2, null, 1, 0, 1, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Job', 3, null, 1, 0, 1, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Company', 4, null, 1, 0, 1, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add User', 6, '["POST/user/api/v3/users/add-user"]', 5, 1, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete User', 7, '["DELETE/user/api/v3/users/delete-user/{}"]', 5, 1, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get User', 8, '["GET/user/api/v3/users/get-users"]', 5, 1, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update User', 9, '["PUT/user/api/v3/users/update-user"]', 5, 1, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get My Account', 22, '["GET/user/api/v3/account"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:18:32');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Login', 23, '["POST/user/api/v3/login"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:18:49');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get All Privileges', 24, '["GET/user/api/v3/users/get-privileges"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:19:12');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add Privilege', 25, '["POST/user/api/v3/users/add-privilege"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:19:38');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Public', 29, '["GET/user/api/v3/tenants", "POST/job/api/v3/xxl-job/sync-jobs-to-es", "POST/talent/api/v3/xxl-job/sync-talents-to-es", "POST/company/api/v3/xxl-job/sync-am-report-send-email", "POST/user/api/v3/reset-password/init"]', 1, 0, 1, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 06:25:45');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Privilege Tree', 30, '["GET/user/api/v3/users/get-privilege-tree"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:19:54');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Roles', 31, '["GET/user/api/v3/users/get-roles"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:20:18');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Set Role Privilege', 32, '["POST/user/api/v3/users/set-role-privilege"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:20:40');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Privileges By Role', 33, '["GET/user/api/v3/users/role/{}/privileges"]', 29, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:20:47');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Role Tree', 34, '["GET/user/api/v3/users/get-roles-tree"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:21:15');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get All Users', 35, '["GET/user/api/v3/users/get-all-users"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:21:34');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Roles By User', 36, '["GET/user/api/v3/users/user/{}/roles"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:21:51');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Set User Role', 37, '["POST/user/api/v3/users/set-user-role"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Teams', 50, '["GET/user/api/v3/users/get-teams"]', 5, 1, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Team Members', 51, '["GET/user/api/v3/users/get-team-members/{}"]', 5, 1, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Set Data Permission', 52, '["POST/user/api/v3/users/set-role-data-permission"]', 5, 1, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create Platform Admin', 54, '["POST/user/api/v3/admin/users/platform"]', 5, 1, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create Tenant Admin', 55, '["POST/user/api/v3/admin/users/tenant"]', 5, 1, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Users', 56, '["GET/user/api/v3/users/get-users"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:22:25');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Set User Data Permission', 57, '["POST/user/api/v3/users/set-user-data-permission"]', 5, 0, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Tenants', 58, '["GET/user/api/v3/users/get-tenants"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:22:43');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Set Tenant Modules', 59, '["POST/user/api/v3/users/set-tenant-modules"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:23:00');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Platform Admins', 60, '["GET/user/api/v3/admin/users/platform-admin"]', 5, 1, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Tenant Admins', 61, '["GET/user/api/v3/admin/users/tenant-admin"]', 5, 1, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Modules', 62, '["GET/user/api/v3/admin/users/modules"]', 5, 1, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Tables', 63, '["GET/user/api/v3/admin/users/tables"]', 5, 1, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Tables by Module', 64, '["GET/user/api/v3/admin/users/module/{}/tables"]', 5, 1, 2, 1, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add User To Team', 65, '["POST/user/api/v3/users/team/{}/user/{}"]', 5, 1, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Remove User From Team', 66, '["DELETE/user/api/v3/users/team/{}/user/{}"]', 5, 1, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Refresh Token', 67, '["POST/user/api/v3/refresh-token"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:23:34');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Test login', 68, '["POST/user/api/v3/login-test","POST/user/api/v3/users/update-credits/debug"]', 29, 1, 2, 1, '', '2022-11-12 14:56:41', '322,4', '2022-11-28 01:23:51');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Dashboard', 100, null, 1, 0, 1, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Dashboard', 101, null, 100, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Onboarding Dashboard', 102, null, 100, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Candidate', 103, '["POST/application/api/v3/dashboard/my-candidates","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}"]', 101, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:44:37');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Jobs-By Companies', 104, '["GET/job/api/v3/dashboard/my-jobs/company","GET/job/api/v3/dashboard/my-jobs/{}","GET/job/api/v3/dashboard/my-jobs"]', 101, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-02 01:56:18');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Billing', 105, '["GET/finance/api/v3/my-invoices"]', 101, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:40:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Inactive Job Status Monitor', 106, '["GET/job/api/v3/dashboard/dormant/jobs","PUT/job/api/v3/jobs/{}","PUT/job/api/v3/jobs/sync/toIpg/{}/{}","POST/email/api/v3/campaign/send_rich_mail"]', 101, 0, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:55:04');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Inactive Candidate Status Monitor', 107, '["POST/application/api/v3/dashboard/my-inactive-candidate-status-monitor","POST/application/api/v3/talent-recruitment-processes/eliminate"]', 101, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:43:17');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Document View', 108, '["GET/job/api/v3/jobs/preference/{}","GET/job/api/v3/column/{}","POST/jobdiva/api/v3/dashboard/document/page","POST/job/api/v3/jobs/search/config","GET/job/api/v3/jobs/search/config/{}","DELETE/job/api/v3/jobs/search/{}","POST/job/api/v3/jobs/preference","GET/user/api/v3/activated-users","GET/company/api/v3/tenantCompany"]', 102, 0, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 07:33:01');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Package View', 109, '["POST/jobdiva/api/v3/dashboard/package/page","GET/job/api/v3/jobs/preference/{}","GET/job/api/v3/column/{}","POST/job/api/v3/jobs/search/config","GET/job/api/v3/jobs/search/config/{}","DELETE/job/api/v3/jobs/search/{}","GET/user/api/v3/activated-users"]', 102, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-23 09:07:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Test 0003', 110, '["POST/user/api/v3/login-test1"]', 10, 0, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 01:05:14');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('333333333', 111, '["POST/user/api/v3/login-test1"]', 57, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('333', 113, '["POST/user/api/v3/login-test1"]', 20, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('666', 117, '["POST/user/api/v3/login-test1"]', 110, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Test 0001', 121, '["POST/user/api/v3/login-test2"]', 57, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('zzzz', 122, '["POST/user/api/v3/login-test1"]', 14, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('111', 139, '["POST/user/api/v3/users/set-user-data-permission"]', 57, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('1111111', 140, '["POST/user/api/v3/users/set-user-data-permission"]', 137, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('*********', 142, '["POST/user/api/v3/account/admin/login"]', 53, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('222222', 143, '["POST/user/api/v3/account/admin/login"]', 53, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('4444', 144, '["POST/user/api/v3/account/admin/login"]', 53, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('3443', 145, null, 122, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('444', 158, '["POST/user/api/v3/login-test1"]', 113, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('5555', 164, '["POST/user/api/v3/users/set-user-data-permission"]', 57, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('UpdateStatus', 175, null, 155, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 176, null, 155, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 177, null, 155, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Job Info', 178, null, 177, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Post on IPG Website', 179, null, 177, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Candidate', 185, '', 155, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Pick candidate for the job', 186, '', 185, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View Applied Candidates', 187, '', 185, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Download Job Description', 188, null, 155, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Email the job to assigned users', 189, null, 155, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Note', 190, null, 155, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 192, null, 190, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 194, null, 190, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 195, null, 190, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('AI Candidate Recommendation', 196, null, 155, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Pipeline', 197, '["GET/job/api/v3/column/{}","GET/application/api/v3/pipeline-template/{}","POST/application/api/v3/my-pipelines","POST/application/api/v3/pipeline-template","PUT/application/api/v3/pipeline-template","DELETE/application/api/v3/pipeline-template/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state"]', 2, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 19:28:14');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('All Candidates/Talent Pool', 198, null, 2, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Candidates', 199, null, 2, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Hotlists', 200, '["GET/user/api/v3/teams","GET/talent/api/v3/my-hot-lists","GET/talent/api/v3/hot-list-users/hot-list/{}","DELETE/talent/api/v3/hot-list-users/{}","POST/talent/api/v3/hot-list-users/replace/{}","POST/talent/api/v3/hot-lists/","DELETE/talent/api/v3/hot-lists/{}","GET/parser/api/v3/parsers/resume/status/{}","POST/parser/api/v3/parsers/resume/upload-and-parse","GET/parser/api/v3/parsers/resume/result/{}","GET/talent/api/v3/hot-list-talents/hot-list/{}","POST/talent/api/v3/talents/search-by-contacts-and-similarity","POST/talent/api/v3/talents","POST/talent/api/v3/hot-list-talents/append/{}","DELETE/talent/api/v3/hot-list-talents/delete/{}","POST/email/api/v3/campaign/send_rich_mail","GET/email/api/v3/audience","POST/email/api/v3/audience","POST/email/api/v3/audience/{}/talent","POST/job/api/v3/jobs/search","GET/job/api/v3/jobs/{}","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","POST/application/api/v3/talent-recruitment-processes/submit-to-job","GET/email/api/v3/template/application-templates","GET/talent/api/v3/hot-lists/{}","POST/talent/api/v3/hot-lists"]', 2, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 05:20:36');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Common Pool', 201, '["GET/job/api/v3/jobs/preference/{}","POST/talent/api/v3/talents/search","POST/job/api/v3/jobs/search/config","GET/job/api/v3/jobs/search/config/{}","DELETE/job/api/v3/jobs/search/{}","GET/user/api/v3/account","POST/user/api/v3/credit-transactions/commonPool","GET/job/api/v3/dict/industries","GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/languages","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/workAuthorization","POST/talent/api/v3/recommend-jobs-for-common-talent/esId","GET/talent/api/v3/es-talents/getTalent","GET/company/api/v3/company/clients/list","POST/talent/api/v3/talents/commonPool","GET/finance/api/v3/starts/talentId/{}","GET/job/api/v3/dict/jobFunctions/creation","GET/job/api/v3/dict/industries/creation","GET/talent/api/v3/talents/{}","GET/talent/api/v3/talent-resumes/talent/{}","GET/application/api/v3/talent-recruitment-processes/talentId/{}","GET/jobdiva/api/v3/onboarding/processing/portal/{}","GET/company/api/v3/company/address/{}","POST/company/api/v3/saleslead/client-contacts/common-pool","GET/location/api/v3/geo-geoinfo/en/city-or-state"]', 2, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 19:28:42');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Search', 203, '["GET/job/api/v3/jobs/preference/{}","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/languages","GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/workAuthorization","GET/job/api/v3/dict/industries","POST/talent/api/v3/talents/search","POST/job/api/v3/jobs/preference","GET/job/api/v3/jobs/search/config/{}","POST/job/api/v3/jobs/search/config","DELETE/job/api/v3/jobs/search/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state"]', 198, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 19:28:25');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 204, '["POST/job/api/v3/jobs/preference","GET/parser/api/v3/parsers/resume/status/{}","POST/parser/api/v3/parsers/resume/upload-and-parse","POST/talent/api/v3/talents","GET/parser/api/v3/parsers/resume/result/{}","POST/talent/api/v3/talents/search-by-contacts-and-similarity","POST/talent/api/v3/talents","POST/talent/api/v3/talents/search","GET/job/api/v3/dict/industries/creation","GET/job/api/v3/dict/languages","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/workAuthorization","GET/job/api/v3/dict/jobFunctions/creation","GET/location/api/v3/geo-geoinfo/en/city-or-state","GET/talent/api/v3/talent/{}/ownerships","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/talent/api/v3/talent-resumes/talent/{}","GET/talent/api/v3/recommend-jobs-for-tenant-talent/talentId/{}","GET/parser/api/v3/parsers/resume/info/{}","GET/talent/api/v3/talents/{}"]', 198, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-23 09:37:41');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Email to Candidate', 205, '["POST/email/api/v3/campaign/send_rich_mail"]', 198, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 04:31:51');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add to Email Blast Group', 206, '["GET/email/api/v3/audience","POST/email/api/v3/audience","POST/email/api/v3/audience/{}/talent"]', 198, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-07 03:44:19');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add to a hotlist', 207, '["GET/talent/api/v3/my-hot-lists","POST/talent/api/v3/hot-list-talents/append/{}"]', 198, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 04:33:51');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Candidate''s Profile', 208, null, 198, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View Candidate Profile', 209, '["GET/talent/api/v3/talent-resumes/talent/{}","GET/job/api/v3/dict/degrees","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/talent/api/v3/talents/{}","GET/talent/api/v3/talent/{}/ownerships","GET/job/api/v3/dict/workAuthorization","GET/application/api/v3/talent-recruitment-processes/{}","POST/job/api/v3/dict/workAuthorization/id"]', 208, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-02 02:51:52');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Candidate Profile', 210, '["GET/talent/api/v3/talents/{}","GET/talent/api/v3/talent-resumes/talent/{}","GET/finance/api/v3/starts/talentId/{}","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/workAuthorization","GET/jobdiva/api/v3/onboarding/processing/portal/{}","GET/job/api/v3/dict/industries/creation","GET/job/api/v3/dict/jobFunctions/creation","GET/application/api/v3/talent-recruitment-processes/talentId/{}","GET/job/api/v3/dict/languages","GET/job/api/v3/jobs/{}","POST/talent/api/v3/talents/search-by-contacts","PUT/talent/api/v3/talents/{}"]', 208, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:24:06');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Candidate Resume', 211, null, 208, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View & Upload', 212, '["GET/talent/api/v3/talents/{}","GET/talent/api/v3/talent-resumes/talent/{}","POST/parser/api/v3/parsers/upload-resume-only","POST/talent/api/v3/talent-resumes","GET/parser/api/v3/parsers/resume/download/{}"]', 211, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:04:25');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add', 213, '["POST/talent/api/v3/talent-resumes","POST/parser/api/v3/parsers/upload-resume-only"]', 211, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:04:52');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete', 214, '["DELETE/talent/api/v3/talent-resumes/{}"]', 211, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:05:26');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add Notes', 215, null, 208, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 218, '["GET/talent/api/v3/talents/{}"]', 215, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:16:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add', 220, '["POST/talent/api/v3/talent-notes"]', 215, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:13:09');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 223, '["PUT/talent/api/v3/talent-notes/{}"]', 215, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:16:58');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add Candidate to a hotlist', 224, '["POST/talent/api/v3/hot-list-talents/append/{}","GET/talent/api/v3/my-hot-lists"]', 208, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:06:59');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Applied Job', 226, '["GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}"]', 208, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:01:52');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('AI Recommendation', 227, '["GET/talent/api/v3/recommend-jobs-for-tenant-talent/talentId/{}"]', 208, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 19:58:07');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Search', 230, '["GET/job/api/v3/jobs/preference/{}","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/languages","GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/workAuthorization","GET/job/api/v3/dict/industries","POST/talent/api/v3/talents/search","POST/job/api/v3/jobs/preference","GET/job/api/v3/jobs/search/config/{}","POST/job/api/v3/jobs/search/config","DELETE/job/api/v3/jobs/search/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state"]', 199, 0, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:26:37');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 231, '["POST/job/api/v3/jobs/preference","GET/parser/api/v3/parsers/resume/status/{}","POST/parser/api/v3/parsers/resume/upload-and-parse","GET/parser/api/v3/parsers/resume/result/{}","POST/talent/api/v3/talents/search-by-contacts-and-similarity","POST/talent/api/v3/talents","POST/talent/api/v3/talents/search","POST/talent/api/v3/talents","GET/job/api/v3/dict/industries/creation","GET/job/api/v3/dict/languages","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/workAuthorization","GET/job/api/v3/dict/jobFunctions/creation","GET/location/api/v3/geo-geoinfo/en/city-or-state","GET/talent/api/v3/talent/{}/ownerships","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/talent/api/v3/talent-resumes/talent/{}","GET/talent/api/v3/recommend-jobs-for-tenant-talent/talentId/{}","GET/parser/api/v3/parsers/resume/info/{}","GET/talent/api/v3/talents/{}"]', 199, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-23 09:38:44');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Email to Candidate', 233, '["POST/email/api/v3/campaign/send_rich_mail"]', 199, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:29:46');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add to a hotlist', 235, '["GET/talent/api/v3/my-hot-lists","POST/talent/api/v3/hot-list-talents/append/{}"]', 199, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:30:34');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add to Email Blast Group', 236, '["GET/email/api/v3/audience","POST/email/api/v3/audience","POST/email/api/v3/audience/{}/talent"]', 199, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-07 03:44:41');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Candidate''s Profile', 238, null, 199, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View Candidate Profile', 239, '["GET/talent/api/v3/talent-resumes/talent/{}","GET/job/api/v3/dict/degrees","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/talent/api/v3/talents/{}","GET/talent/api/v3/talent/{}/ownerships","GET/job/api/v3/dict/workAuthorization","GET/application/api/v3/talent-recruitment-processes/{}","POST/job/api/v3/dict/workAuthorization/id"]', 238, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-02 02:53:36');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Candidate Profile', 241, '["GET/talent/api/v3/talents/{}","GET/talent/api/v3/talent-resumes/talent/{}","GET/finance/api/v3/starts/talentId/{}","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/workAuthorization","GET/jobdiva/api/v3/onboarding/processing/portal/{}","GET/job/api/v3/dict/industries/creation","GET/job/api/v3/dict/jobFunctions/creation","GET/application/api/v3/talent-recruitment-processes/talentId/{}","GET/job/api/v3/dict/languages","GET/job/api/v3/jobs/{}","POST/talent/api/v3/talents/search-by-contacts","PUT/talent/api/v3/talents/{}"]', 238, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:33:19');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Candidate Resume', 243, null, 238, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add Notes', 244, null, 238, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add Candidate to a hotlist', 245, '["POST/talent/api/v3/hot-list-talents/append/{}","GET/talent/api/v3/my-hot-lists"]', 238, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:39:01');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Applied Job', 249, '["GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}"]', 238, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:40:32');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('AI Recommendation', 250, '["GET/talent/api/v3/recommend-jobs-for-tenant-talent/talentId/{}"]', 238, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:41:06');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View & Upload', 251, '["GET/talent/api/v3/talents/{}","GET/talent/api/v3/talent-resumes/talent/{}","POST/parser/api/v3/parsers/upload-resume-only","POST/talent/api/v3/talent-resumes","GET/parser/api/v3/parsers/resume/download/{}"]', 243, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:34:16');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add', 253, '["POST/talent/api/v3/talent-resumes","POST/parser/api/v3/parsers/upload-resume-only"]', 243, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:35:07');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete', 254, '["DELETE/talent/api/v3/talent-resumes/{}"]', 243, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:35:58');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 260, '["GET/talent/api/v3/talents/{}"]', 244, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:36:34');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add', 263, '["POST/talent/api/v3/talent-notes"]', 244, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:37:24');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 264, '["PUT/talent/api/v3/talent-notes/{}"]', 244, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:38:14');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Email Blast', 265, '["GET/email/api/v3/audience","POST/email/api/v3/audience","GET/email/api/v3/template/email-blast","POST/email/api/v3/attachment/upload","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/save-draft","POST/email/api/v3/template/email-blast","PUT/email/api/v3/template/email-blast/{}","POST/email/api/v3/campaign/send-email-blast","PUT/email/api/v3/audience/{}","PUT/email/api/v3/audience/{}/archive","GET/email/api/v3/campaign","POST/email/api/v3/campaign/{}/archive","GET/email/api/v3/campaign/{}","GET/email/api/v3/stats/campaigns","GET/email/api/v3/stats","GET/email/api/v3/audience/{}","POST/email/api/v3/audience/{}","POST/email/api/v3/audience/{}/delete","DELETE/email/api/v3/audience/{}/{}","GET/email/api/v3/audience/archived","PUT/email/api/v3/audience/{}/unarchive"]', 1, 1, 1, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 05:25:06');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Template', 266, '["GET/email/api/v3/template/application-templates","PUT/email/api/v3/template/application-templates/{}","POST/email/api/v3/template/application-templates","GET/email/api/v3/template/tags","GET/email/api/v3/template/{}","DELETE/email/api/v3/template/{}","POST/email/api/v3/template/email-blast","PUT/email/api/v3/template/email-blast/{}","GET/email/api/v3/template/email-blast","POST/email/api/v3/campaign/send_rich_mail","GET/email/api/v3/template/email-merge-contacts","PUT/email/api/v3/template/email-merge-contacts/{}","POST/email/api/v3/template/email-merge-contacts"]', 1, 1, 1, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:10:40');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Finance', 267, null, 1, 0, 1, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Invoice', 268, '["GET/finance/api/v3/invoices/{}","GET/user/api/v3/permissions/teams/tree","GET/finance/api/v3/invoices/invoice-no/{}","GET/finance/api/v3/invoices/download/{}","POST/finance/api/v3/invoice-payment-records","POST/finance/api/v3/invoice-client-credits/apply","PUT/finance/api/v3/invoices/void","POST/email/api/v3/campaign/send_rich_mail","GET/company/api/v3/saleslead/client-contact/company/{}","POST/finance/api/v3/invoices/startup-fee","GET/finance/api/v3/invoices/startup-fee/companyId/{}","GET/finance/api/v3/starts/search/talent_name/{}","GET/finance/api/v3/invoices/companyId/{}","POST/finance/api/v3/invoices/fte","GET/finance/api/v3/invoices","GET/finance/api/v3/invoice-client-credits/companyId/{}","GET/user/api/v3/divisions"]', 267, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 19:04:52');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Commission by Project', 270, null, 269, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 271, null, 270, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 272, null, 270, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Commission By Recruiters', 273, null, 269, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Application', 274, null, 1, 0, 1, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Common Process', 275, null, 274, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Timesheets', 278, null, 1, 0, 1, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Manage Timesheets/Expenses', 279, null, 278, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Upload Timesheets', 280, null, 279, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Search Timesheets/Expenses', 281, '["GET/jobdiva/api/v3/filter/list","POST/jobdiva/api/v3/timesheet/am/record/search","POST/jobdiva/api/v3/timesheet/am/record/approve","GET/jobdiva/api/v3/filter/searchFilter","POST/jobdiva/api/v3/filter/saveFilter","GET/jobdiva/api/v3/filter/delete","POST/jobdiva/api/v3/filter/saveTableHeader","POST/jobdiva/api/v3/client/timeSheet/recordDetail","POST/jobdiva/api/v3/client/timeSheet/detail/download","POST/jobdiva/api/v3/timesheet/am/record/downloadSummary","POST/jobdiva/api/v3/expense/am/record/search","POST/jobdiva/api/v3/expense/am/record/approve","POST/jobdiva/api/v3/client/expense/recordDetail","GET/job/api/v3/dict/currency/all","POST/jobdiva/api/v3/expense/am/record/detail","GET/jobdiva/api/v3/assignment/billingUnit","POST/jobdiva/api/v3/client/expense/detail/download","POST/jobdiva/api/v3/expense/am/record/downloadSummary","POST/jobdiva/api/v3/expense/am/record/downloadReceipts"]', 279, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 21:38:36');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Set Up Missing Timesheets Alerts', 282, null, 279, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('All Jobs', 283, null, 3, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Jobs', 284, null, 3, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Search ', 285, '["GET/job/api/v3/dict/jobFunctions/creation","GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/languages","GET/company/api/v3/company/clients/list","GET/job/api/v3/jobs/preference/{}","POST/job/api/v3/jobs/search","GET/location/api/v3/geo-geoinfo/en/city-or-state","POST/job/api/v3/jobs/search/config","GET/job/api/v3/jobs/search/config/{}","DELETE/job/api/v3/jobs/search/{}","POST/job/api/v3/jobs/preference","DELETE/job/api/v3/jobs/favorite/{}","POST/job/api/v3/jobs/favorite/{}","GET/job/api/v3/column/{}"]', 283, 1, 3, 1, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 08:17:45');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create Job', 286, '["POST/parser/api/v3/parsers/jd/status","POST/parser/api/v3/parsers/jd-text/upload-and-parse","GET/company/api/v3/company/project-teams","POST/parser/api/v3/parsers/jd/result","GET/company/api/v3/saleslead/client-contact/company/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state","POST/job/api/v3/jobs","POST/job/api/v3/jobs/sync/toIpg","POST/parser/api/v3/parsers/jd/upload-and-parse"]', 283, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 21:59:50');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Status', 287, '["PUT/job/api/v3/jobs/{}/{}","POST/email/api/v3/campaign/send_rich_mail"]', 283, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 20:55:59');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Job Detail Profile', 288, null, 283, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Status', 289, '["GET/job/api/v3/jobs/sync/toIpg/{}","GET/company/api/v3/saleslead/client-contact/company/{}","PUT/job/api/v3/jobs/{}","PUT/job/api/v3/jobs/sync/toIpg/{}/{}","GET/job/api/v3/jobs/{}","POST/email/api/v3/campaign/send_rich_mail"]', 288, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 21:43:23');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 290, '["GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/languages","GET/company/api/v3/company/project-teams","GET/job/api/v3/dict/degrees","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/job/api/v3/jobs/{}","GET/company/api/v3/saleslead/client-contact/company/{}","GET/job/api/v3/jobs/sync/toIpg/{}"]', 288, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 21:40:08');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 291, null, 288, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Job Info', 292, '["GET/job/api/v3/dict/jobFunctions","GET/company/api/v3/company/project-teams","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/languages","GET/company/api/v3/saleslead/client-contact/company/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state","PUT/job/api/v3/jobs/{}"]', 291, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 18:13:08');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Post on IPG Website', 293, '["PUT/job/api/v3/jobs/sync/toIpg/{}"]', 291, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 18:15:43');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Candidate', 294, null, 288, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View Applied Candidates', 296, '["GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}"]', 294, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 21:54:16');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Download Job Description', 297, null, 288, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Email the job to asssigned users', 298, '["POST/email/api/v3/campaign/send_rich_mail"]', 288, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 21:55:24');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Note', 299, null, 288, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 300, '["GET/job/api/v3/jobs/{}"]', 299, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 21:57:11');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 301, '["PUT/job/api/v3/job-notes/{}"]', 299, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 21:57:35');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 302, '["POST/job/api/v3/job-notes"]', 299, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 21:58:05');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('AI Candidate Recommendation', 303, '["GET/job/api/v3/recommend-tenant-talents/jobId/{}","GET/job/api/v3/recommend-common-talents/jobId/{}"]', 288, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 21:56:22');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Search', 304, '["GET/job/api/v3/dict/jobFunctions/creation","GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/languages","GET/company/api/v3/company/clients/list","GET/job/api/v3/jobs/preference/{}","POST/job/api/v3/jobs/search","GET/location/api/v3/geo-geoinfo/en/city-or-state","POST/job/api/v3/jobs/search/config","GET/job/api/v3/jobs/search/config/{}","DELETE/job/api/v3/jobs/search/{}","POST/job/api/v3/jobs/preference","DELETE/job/api/v3/jobs/favorite/{}","POST/job/api/v3/jobs/favorite/{}","GET/job/api/v3/column/{}"]', 284, 1, 3, 1, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 08:18:19');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create Job', 305, '["POST/parser/api/v3/parsers/jd/status","POST/parser/api/v3/parsers/jd-text/upload-and-parse","GET/company/api/v3/company/project-teams","POST/parser/api/v3/parsers/jd/result","GET/company/api/v3/saleslead/client-contact/company/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state","POST/job/api/v3/jobs","POST/job/api/v3/jobs/sync/toIpg","POST/parser/api/v3/parsers/jd/upload-and-parse"]', 284, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:01:28');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Status', 306, '["PUT/job/api/v3/jobs/{}/{}","POST/email/api/v3/campaign/send_rich_mail"]', 284, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:02:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Job Detail Profile', 307, null, 284, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update status', 308, '["GET/job/api/v3/jobs/sync/toIpg/{}","GET/company/api/v3/saleslead/client-contact/company/{}","PUT/job/api/v3/jobs/{}","PUT/job/api/v3/jobs/sync/toIpg/{}/{}","GET/job/api/v3/jobs/{}","POST/email/api/v3/campaign/send_rich_mail"]', 307, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:03:01');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 309, '["GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/languages","GET/company/api/v3/company/project-teams","GET/job/api/v3/dict/degrees","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/job/api/v3/jobs/{}","GET/company/api/v3/saleslead/client-contact/company/{}","GET/job/api/v3/jobs/sync/toIpg/{}"]', 307, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:03:59');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 310, null, 307, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Job Info', 311, '["GET/job/api/v3/dict/jobFunctions","GET/company/api/v3/company/project-teams","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/languages","GET/company/api/v3/saleslead/client-contact/company/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state","PUT/job/api/v3/jobs/{}"]', 310, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 18:17:24');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Post on IPG Website', 312, '["PUT/job/api/v3/jobs/sync/toIpg/{}"]', 310, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 18:21:23');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Candidate', 313, null, 307, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View Applied Candidates', 315, '["GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}"]', 313, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:04:48');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Download Job Description', 316, null, 307, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Email the job to assigned users', 317, '["POST/email/api/v3/campaign/send_rich_mail"]', 307, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:05:33');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Note', 318, null, 307, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 319, '["GET/job/api/v3/jobs/{}"]', 318, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:06:23');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 320, '["PUT/job/api/v3/job-notes/{}"]', 318, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:06:49');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 321, '["POST/job/api/v3/job-notes"]', 318, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:07:27');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('AI Candidate Recommendation', 322, '["GET/job/api/v3/recommend-tenant-talents/jobId/{}","GET/job/api/v3/recommend-common-talents/jobId/{}"]', 307, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-18 22:07:59');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Sumbit To Job', 323, '["POST/job/api/v3/jobs/search","GET/job/api/v3/jobs/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/application/api/v3/recruitment-processes/default","POST/application/api/v3/talent-recruitment-processes/submit-to-job","GET/application/api/v3/talent-recruitment-processes/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/job/api/v3/jobs/preference/{}","GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/languages","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/workAuthorization","GET/job/api/v3/dict/industries","POST/talent/api/v3/talents/search","POST/job/api/v3/jobs/preference","GET/talent/api/v3/talents/{}","GET/email/api/v3/template/application-templates","POST/email/api/v3/campaign/send_rich_mail","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}"]', 275, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 01:00:19');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Submit To Client', 324, '["GET/application/api/v3/talent-recruitment-processes/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/email/api/v3/template/application-templates","GET/company/api/v3/saleslead/client-contact/company/{}","POST/application/api/v3/talent-recruitment-processes/submit-to-client","POST/email/api/v3/campaign/send_rich_mail","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}"]', 275, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 00:36:35');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Interview', 325, '["GET/application/api/v3/talent-recruitment-processes/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","POST/application/api/v3/talent-recruitment-processes/interview","GET/email/api/v3/template/application-templates","GET/company/api/v3/saleslead/client-contact/company/{}","POST/email/api/v3/campaign/send_rich_mail","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/company/api/v3/company/client/{}","GET/talent/api/v3/talent/{}/ownerships"]', 275, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 01:00:51');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Offer', 326, '["GET/application/api/v3/talent-recruitment-processes/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","POST/application/api/v3/talent-recruitment-processes/offer","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","GET/email/api/v3/template/application-templates","GET/company/api/v3/saleslead/client-contact/company/{}","POST/email/api/v3/campaign/send_rich_mail","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}"]', 275, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 01:05:14');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Commission', 327, '["GET/application/api/v3/talent-recruitment-processes/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/company/api/v3/saleslead/client-contact/company/{}","POST/application/api/v3/talent-recruitment-processes/commission","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/recruitment-processes/default","GET/company/api/v3/company/client/{}","GET/talent/api/v3/talent/{}/ownerships"]', 275, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 01:05:29');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('On Board', 328, '["GET/application/api/v3/talent-recruitment-processes/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates","POST/application/api/v3/talent-recruitment-processes/onboard","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}"]', 275, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 00:39:36');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('SUBMIT_TO_JOB', 329, null, 276, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('SUBMIT_TO_CLIENT', 330, null, 276, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('INTERVIEW', 331, null, 276, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('OFFER', 332, null, 276, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('OFFER_ACCEPT', 333, null, 276, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('ON_BOARD', 334, null, 276, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('TURN_DOWN', 335, null, 276, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('SUBMIT_TO_JOB', 337, null, 277, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('SUBMIT_TO_CLIENT', 338, null, 277, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('INTERVIEW', 339, null, 277, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('OFFER', 340, null, 277, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('OFFER_ACCEPT', 341, null, 277, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('ON_BOARD', 342, null, 277, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('ASSIGNMENT', 343, null, 277, 1, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Reports', 344, null, 1, 0, 1, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Job Analytics', 345, '["GET/user/api/v3/permissions/teams/tree/with-permission","GET/job/api/v3/job-countries","POST/report/api/v3/report/job-company","POST/report/api/v3/report/job/details","POST/report/api/v3/report/job/details-excel","POST/report/api/v3/report/pipeline/details","POST/report/api/v3/report/pipeline/details-excel","POST/report/api/v3/report/job-company-excel","POST/report/api/v3/report/job-user","POST/report/api/v3/report/job-user-excel"]', 344, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 21:46:53');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Pipeline Analytics', 346, '["GET/user/api/v3/permissions/teams/tree","POST/report/api/v3/report/p1-pipeline-analytics-by-users-excel","POST/report/api/v3/report/p1-pipeline-analytics-by-users","GET/user/api/v3/permissions/teams/tree/with-permission","POST/report/api/v3/report/pipeline/details","POST/report/api/v3/report/pipeline/details-excel","GET/report/api/v3/report/p2-pipeline-analytics-by-company","GET/report/api/v3/report/p2-pipeline-analytics-by-company/user/filter","GET/report/api/v3/report/p2-pipeline-analytics-by-company-excel","GET/job/api/v3/job-countries"]', 344, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 21:55:07');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Analytics Graph', 347, '["POST/report/api/v3/report/g2-pipeline-analytics-by-submit-to-am","POST/user/api/v3/permissions/teams/users/plain","POST/report/api/v3/report/pipeline/details","POST/report/api/v3/report/g4-pipeline-analytics-by-users"]', 344, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 23:23:58');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Company Report', 348, '["GET/report/api/v3/companies/bd","GET/user/api/v3/teams","POST/user/api/v3/permissions/teams/users/plain","POST/report/api/v3/companies/bd"]', 344, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-02 01:24:52');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Hires Report', 349, '["GET/report/api/v3/report/sales/company/filter","POST/report/api/v3/report/sales-all-by-type","POST/report/api/v3/report/sales-details","POST/report/api/v3/report/sales-details-excel","POST/report/api/v3/report/sales-by-weekly-new-offer"]', 344, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 23:39:36');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Weekly Report', 350, '["GET/report/api/v3/user/linkedin-stats","GET/report/api/v3/user/es-stats","GET/user/api/v3/users/all-brief","GET/user/api/v3/users/{}","GET/user/api/v3/users","GET/report/api/v3/user/all-linkedin-stats","GET/report/api/v3/user/all-es-stats"]', 344, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-07 03:58:54');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Status Monitor', 351, '["GET/report/api/v3/resign-user-jobs-talents","POST/report/api/v3/report/jobs/jobIds","POST/report/api/v3/report/talents/talentIds","GET/user/api/v3/teams","POST/report/api/v3/dormant/jobs","POST/user/api/v3/permissions/teams/users/plain","GET/report/api/v3/dormant/jobs/{}","POST/report/api/v3/report/dormant/applications","GET/report/api/v3/report/dormant/applications/{}"]', 344, 1, 2, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 23:43:49');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Clients', 352, null, 4, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Prospects', 353, null, 4, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('All Clients', 354, null, 352, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My clients', 355, null, 352, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Search', 356, '["POST/company/api/v3/company/clients/search","GET/company/api/v3/company/noContracts","GET/company/api/v3/company/service-types","GET/company/api/v3/company/search/{}"]', 354, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:04:09');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Company Profile', 357, null, 354, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Overview Info', 358, '["GET/company/api/v3/accountBusiness/{}","GET/company/api/v3/company/service-types","POST/job/api/v3/jobs/search","GET/company/api/v3/company/client/{}"]', 357, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:06:36');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Contact Info', 359, null, 357, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Open Jobs', 360, null, 357, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Service Contract', 361, null, 357, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Program team', 362, null, 357, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Note', 363, null, 357, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('AM Report', 364, '["POST/company/api/v3/am/reports","GET/job/api/v3/jobs/contact/{}","GET/company/api/v3/am/download/{}","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","POST/company/api/v3/am/save-high-lighted-experience","POST/company/api/v3/am/save-am-update"]', 357, 1, 5, 1, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 06:33:33');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add sales lead', 366, '["GET/company/api/v3/saleslead/client-contact/company/{}","POST/company/api/v3/saleslead"]', 357, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:40:35');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Client Info', 367, '["GET/company/api/v3/company/service-types","GET/company/api/v3/company/client/{}","GET/location/api/v3/geoinfo/search","PUT/company/api/v3/company/client"]', 357, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:13:06');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 368, '["GET/company/api/v3/saleslead/client-contact/company/{}","GET/company/api/v3/saleslead/client-contacts/hasApproverPermission/{}"]', 359, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:07:34');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 369, '["GET/company/api/v3/company/address/{}","GET/company/api/v3/company/client/{}","PUT/company/api/v3/saleslead/client-contact","GET/location/api/v3/geoinfo/search","POST/company/api/v3/company/address"]', 359, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:17:28');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 370, '["GET/company/api/v3/company/address/{}","GET/company/api/v3/company/client/{}","POST/company/api/v3/saleslead/client-contact","GET/location/api/v3/geoinfo/search","POST/company/api/v3/company/address"]', 359, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:16:10');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add Contacts to email blast group', 371, '["GET/email/api/v3/audience","POST/email/api/v3/audience","POST/email/api/v3/audience/{}/contact"]', 359, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:14:01');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Set Timesheet Approver Permission', 372, '["POST/company/api/v3/saleslead/client-contacts/approver"]', 359, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:18:29');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 373, '["POST/job/api/v3/jobs/search","GET/job/api/v3/jobs/preference/{}","GET/job/api/v3/dashboard/my-jobs/{}"]', 360, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:20:46');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 374, '["POST/parser/api/v3/parsers/jd/status","POST/parser/api/v3/parsers/jd-text/upload-and-parse","GET/company/api/v3/company/project-teams","POST/parser/api/v3/parsers/jd/result","GET/company/api/v3/saleslead/client-contact/company/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state","POST/job/api/v3/jobs","POST/job/api/v3/jobs/sync/toIpg","POST/parser/api/v3/parsers/jd/upload-and-parse"]', 360, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:22:27');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View ', 375, '["GET/company/api/v3/contract/{}","GET/company/api/v3/contract/detail/{}"]', 361, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:24:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 376, '["PUT/company/api/v3/contract"]', 361, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:26:50');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create/Renew', 377, '["GET/company/api/v3/contract/upload-url","POST/company/api/v3/contract","GET/company/api/v3/contract/{}"]', 361, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:30:28');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 378, '["GET/company/api/v3/company/project-teams/company/{}"]', 362, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:31:36');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 379, '["PUT/company/api/v3/company/project-teams/{}"]', 362, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:33:31');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 380, '["POST/company/api/v3/company/project-teams","GET/company/api/v3/company/project-teams/company/{}"]', 362, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:32:50');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete', 381, '["DELETE/company/api/v3/company/project-teams/{}"]', 362, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:34:38');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 382, '["GET/company/api/v3/company/client/note/{}"]', 363, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:35:38');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 383, '["PUT/company/api/v3/company/client/note"]', 363, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:37:26');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Search', 384, '["POST/company/api/v3/company/clients/search","GET/company/api/v3/company/noContracts","GET/company/api/v3/company/service-types","GET/company/api/v3/company/search/{}"]', 355, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:49:10');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Company Profile', 385, null, 355, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Overview Info', 386, '["GET/company/api/v3/accountBusiness/{}","GET/company/api/v3/company/service-types","POST/job/api/v3/jobs/search","GET/company/api/v3/company/client/{}"]', 385, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:50:06');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Open Jobs', 387, null, 385, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Service Contract', 388, null, 385, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Contact Info', 389, null, 385, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Program team', 390, null, 385, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Note', 392, null, 385, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('AM Report', 393, '["POST/company/api/v3/am/reports","GET/job/api/v3/jobs/contact/{}","GET/company/api/v3/am/download/{}","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","POST/company/api/v3/am/save-high-lighted-experience","POST/company/api/v3/am/save-am-update"]', 385, 1, 5, 1, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 06:34:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add sales lead', 395, '["GET/company/api/v3/saleslead/client-contact/company/{}","POST/company/api/v3/saleslead"]', 385, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:09:27');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Client Info', 396, '["GET/company/api/v3/company/service-types","GET/company/api/v3/company/client/{}","GET/location/api/v3/geoinfo/search","PUT/company/api/v3/company/client"]', 385, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:13:52');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 397, '["GET/company/api/v3/saleslead/client-contact/company/{}","GET/company/api/v3/saleslead/client-contacts/hasApproverPermission/{}"]', 389, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:55:14');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 398, '["GET/company/api/v3/company/address/{}","GET/company/api/v3/company/client/{}","PUT/company/api/v3/saleslead/client-contact","GET/location/api/v3/geoinfo/search","POST/company/api/v3/company/address"]', 389, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:55:57');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 399, '["GET/company/api/v3/company/address/{}","GET/company/api/v3/company/client/{}","POST/company/api/v3/saleslead/client-contact","GET/location/api/v3/geoinfo/search","POST/company/api/v3/company/address"]', 389, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:56:38');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add Contacts to email blast group', 400, '["GET/email/api/v3/audience","POST/email/api/v3/audience","POST/email/api/v3/audience/{}/contact"]', 389, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:57:37');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Set Timesheet Approver Permission', 401, '["POST/company/api/v3/saleslead/client-contacts/approver"]', 389, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:58:18');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 402, '["POST/job/api/v3/jobs/search","GET/job/api/v3/jobs/preference/{}","GET/job/api/v3/dashboard/my-jobs/{}"]', 387, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:51:05');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 403, '["POST/parser/api/v3/parsers/jd/status","POST/parser/api/v3/parsers/jd-text/upload-and-parse","GET/company/api/v3/company/project-teams","POST/parser/api/v3/parsers/jd/result","GET/company/api/v3/saleslead/client-contact/company/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state","POST/job/api/v3/jobs","POST/job/api/v3/jobs/sync/toIpg","POST/parser/api/v3/parsers/jd/upload-and-parse"]', 387, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:51:59');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 404, '["GET/company/api/v3/contract/{}","GET/company/api/v3/contract/detail/{}"]', 388, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:52:52');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 405, '["PUT/company/api/v3/contract"]', 388, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:53:42');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create/Renew', 406, '["GET/company/api/v3/contract/upload-url","POST/company/api/v3/contract","GET/company/api/v3/contract/{}"]', 388, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 01:54:29');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 407, '["GET/company/api/v3/company/project-teams/company/{}"]', 390, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:02:39');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 408, '["PUT/company/api/v3/company/project-teams/{}"]', 390, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:04:28');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 409, '["POST/company/api/v3/company/project-teams","GET/company/api/v3/company/project-teams/company/{}"]', 390, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:05:45');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete', 410, '["DELETE/company/api/v3/company/project-teams/{}"]', 390, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:06:47');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 411, '["GET/company/api/v3/company/client/note/{}"]', 392, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:07:27');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 412, '["PUT/company/api/v3/company/client/note"]', 392, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:08:07');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('All Prospects', 413, null, 353, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Prospects', 414, null, 353, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Search', 415, '["POST/company/api/v3/company/prospects/search","GET/company/api/v3/company/search/{}"]', 413, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 02:15:47');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 416, '["GET/company/api/v3/company/service-types","GET/location/api/v3/geoinfo/search","POST/company/api/v3/company","GET/company/api/v3/accountBusiness/{}","POST/job/api/v3/jobs/search","GET/company/api/v3/company/prospect/{}","POST/company/api/v3/company/upload-avatar"]', 413, 1, 4, 1, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 05:42:57');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Company Profile', 417, null, 413, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Overview', 418, '["GET/company/api/v3/accountBusiness/{}","POST/job/api/v3/jobs/search","GET/company/api/v3/company/service-types","GET/company/api/v3/company/prospect/{}"]', 417, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 03:45:22');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Contacts', 419, null, 417, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('BD Process Notes', 420, null, 417, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Note', 421, null, 417, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Promote to client', 422, '["GET/company/api/v3/contract/upload-url","PUT/company/api/v3/company/prospect/upgrade"]', 417, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:12:58');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add sales lead', 423, '["GET/company/api/v3/saleslead/client-contact/company/{}","POST/company/api/v3/saleslead"]', 417, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:14:47');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Check Other Service Types With This Client', 424, null, 417, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-14 21:41:43');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 425, '["GET/company/api/v3/saleslead/client-contact/company/{}","GET/company/api/v3/saleslead/client-contacts/hasApproverPermission/{}"]', 419, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 03:52:17');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 426, '["GET/company/api/v3/company/address/{}","GET/company/api/v3/company/prospect/{}","GET/location/api/v3/geoinfo/search","POST/company/api/v3/company/address","PUT/company/api/v3/saleslead/client-contact"]', 419, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 03:57:47');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 427, '["GET/company/api/v3/company/address/{}","GET/company/api/v3/company/prospect/{}","POST/company/api/v3/company/address","GET/location/api/v3/geoinfo/search","POST/company/api/v3/saleslead/client-contact","GET/company/api/v3/saleslead/client-contact/company/{}"]', 419, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:00:15');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add Contacts to email blast group', 428, '["GET/email/api/v3/audience","POST/email/api/v3/audience","POST/email/api/v3/audience/{}/contact"]', 419, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:01:24');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 429, '["GET/company/api/v3/company/progress-note/{}","GET/company/api/v3/saleslead/client-contact/company/{}"]', 420, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:03:32');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 430, '["POST/company/api/v3/company/progress-note","GET/company/api/v3/company/progress-note/{}"]', 420, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:02:31');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 431, '["GET/company/api/v3/company/client/note/{}"]', 421, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:04:03');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 432, '["PUT/company/api/v3/company/client/note"]', 421, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:09:58');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Search', 433, '["POST/company/api/v3/company/prospects/search","GET/company/api/v3/company/search/{}"]', 414, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:28:35');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 434, '["GET/company/api/v3/company/service-types","GET/location/api/v3/geoinfo/search","POST/company/api/v3/company","GET/company/api/v3/accountBusiness/{}","POST/job/api/v3/jobs/search","GET/company/api/v3/company/prospect/{}"]', 414, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:29:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Company Profile', 435, null, 414, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Overview', 436, '["GET/company/api/v3/accountBusiness/{}","POST/job/api/v3/jobs/search","GET/company/api/v3/company/service-types","GET/company/api/v3/company/prospect/{}"]', 435, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:29:49');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Contacts', 437, null, 435, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('BD Process Notes', 438, null, 435, 0, 5, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Promote to client', 440, '["GET/company/api/v3/contract/upload-url","PUT/company/api/v3/company/prospect/upgrade"]', 435, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:36:01');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add sales lead', 441, '["GET/company/api/v3/saleslead/client-contact/company/{}","POST/company/api/v3/saleslead"]', 435, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:36:39');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Check Other Service Types With This Client', 442, null, 435, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-14 21:41:57');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 443, '["GET/company/api/v3/saleslead/client-contact/company/{}","GET/company/api/v3/saleslead/client-contacts/hasApproverPermission/{}"]', 437, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:30:24');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 444, '["GET/company/api/v3/company/address/{}","GET/company/api/v3/company/prospect/{}","GET/location/api/v3/geoinfo/search","POST/company/api/v3/company/address","PUT/company/api/v3/saleslead/client-contact"]', 437, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:30:58');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 445, '["GET/company/api/v3/company/address/{}","GET/company/api/v3/company/prospect/{}","POST/company/api/v3/company/address","GET/location/api/v3/geoinfo/search","POST/company/api/v3/saleslead/client-contact","GET/company/api/v3/saleslead/client-contact/company/{}"]', 437, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:31:53');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add Contacts to email blast group', 446, '["GET/email/api/v3/audience","POST/email/api/v3/audience","POST/email/api/v3/audience/{}/contact"]', 437, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:32:25');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 447, '["GET/company/api/v3/company/progress-note/{}","GET/company/api/v3/saleslead/client-contact/company/{}"]', 438, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:33:02');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 448, '["POST/company/api/v3/company/progress-note","GET/company/api/v3/company/progress-note/{}"]', 438, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:33:29');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 449, '["GET/company/api/v3/company/client/note/{}"]', 439, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:34:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 450, '["PUT/company/api/v3/company/client/note"]', 439, 1, 6, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:35:07');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Settings', 451, null, 1, 0, 1, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Onboarding', 452, null, 451, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('My Team', 453, null, 451, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Documents', 454, '["GET/jobdiva/api/v3/settings/documents/{}","GET/jobdiva/api/v3/settings/documents","POST/jobdiva/api/v3/settings/files/upload","DELETE/jobdiva/api/v3/settings/documents/{}","PUT/jobdiva/api/v3/settings/documents/{}"]', 452, 0, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:16:46');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Packages', 455, null, 452, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('E-signature', 456, null, 452, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 457, '["GET/jobdiva/api/v3/settings/documents"]', 454, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:17:41');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 458, '["POST/jobdiva/api/v3/settings/files/upload","POST/jobdiva/api/v3/settings/documents","GET/jobdiva/api/v3/settings/documents"]', 454, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:25:53');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 459, '["GET/jobdiva/api/v3/settings/documents/{}","POST/jobdiva/api/v3/settings/files/upload","PUT/jobdiva/api/v3/settings/documents/{}"]', 454, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:26:55');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete', 460, '["DELETE/jobdiva/api/v3/settings/documents/{}"]', 454, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:27:34');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Download', 461, null, 454, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Packages List', 462, null, 455, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Packages Details', 463, null, 455, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 464, '["GET/jobdiva/api/v3/settings/packages"]', 462, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:28:17');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 465, '["POST/jobdiva/api/v3/settings/packages","GET/jobdiva/api/v3/settings/packages","POST/jobdiva/api/v3/settings/documents/{}","POST/jobdiva/api/v3/settings/packages/documents/{}"]', 462, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:30:25');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete', 466, '["DELETE/jobdiva/api/v3/settings/packages/{}"]', 462, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:30:53');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 467, '["GET/jobdiva/api/v3/settings/packages-documents/{}"]', 463, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:31:34');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Select Documents', 468, '["POST/jobdiva/api/v3/settings/documents/{}","POST/jobdiva/api/v3/settings/packages/documents/{}","GET/jobdiva/api/v3/settings/packages-documents/{}"]', 463, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:32:51');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 469, '["PUT/jobdiva/api/v3/settings/packages/{}"]', 463, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:33:21');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete Documents', 470, '["DELETE/jobdiva/api/v3/settings/packages/documents/{}/{}"]', 463, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:33:56');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 471, '["GET/jobdiva/api/v3/settings/signature"]', 456, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:34:24');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 473, '["POST/jobdiva/api/v3/settings/signature"]', 456, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:36:30');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('All Users', 475, null, 453, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Teams', 476, null, 453, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Company Information', 478, null, 453, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View/Filter', 482, '["GET/user/api/v3/users/all-brief","GET/user/api/v3/users/{}","GET/user/api/v3/permissions/roles","GET/user/api/v3/permissions/teams/tree","GET/user/api/v3/tenants/credit/{}","GET/user/api/v3/users"]', 475, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-23 08:23:40');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 483, '["POST/user/api/v3/users","GET/user/api/v3/tenants/credit/{}","GET/user/api/v3/permissions/roles","GET/user/api/v3/permissions/users/{}/roles","POST/user/api/v3/permissions/users/{}/data-permissions","GET/user/api/v3/permissions/teams/tree","POST/user/api/v3/permissions/users/{}/roles","GET/user/api/v3/users/all-brief","GET/user/api/v3/users/{}"]', 475, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:49:23');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 484, '["PUT/user/api/v3/users/{}"]', 475, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:51:35');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 485, '["GET/user/api/v3/permissions/teams/tree"]', 476, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:58:23');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 486, '["POST/user/api/v3/permissions/teams","GET/user/api/v3/permissions/teams/tree"]', 476, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:59:22');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 487, '["PUT/user/api/v3/permissions/teams","GET/user/api/v3/permissions/teams/tree","POST/user/api/v3/permissions/teams"]', 476, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:01:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete', 488, '["GET/user/api/v3/permissions/teams/{}/users","DELETE/user/api/v3/permissions/teams/{}","GET/user/api/v3/permissions/teams/tree","GET/user/api/v3/permissions/teams/{}/count/active-users"]', 476, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-30 02:36:18');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 489, null, 477, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 490, null, 477, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 491, null, 477, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Delete', 492, null, 477, 1, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 493, '["GET/user/api/v3/normal-user/tenant"]', 478, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:13:40');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 495, '["POST/company/api/v3/company/upload-avatar","PUT/user/api/v3/tenants"]', 478, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:14:41');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Custom Process', 497, null, 274, 0, 2, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('FTE', 498, null, 497, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Contractor', 499, null, 497, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Payroll', 500, null, 497, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Sumbit To Job', 501, '["GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","POST/job/api/v3/jobs/search","GET/job/api/v3/jobs/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/application/api/v3/recruitment-processes/default","POST/application/api/v3/talent-recruitment-processes/submit-to-job","GET/email/api/v3/template/application-templates","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail","GET/talent/api/v3/talent-resumes/talent/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/company/api/v3/company/client/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/job/api/v3/dict/industries","POST/talent/api/v3/talents/search","GET/job/api/v3/dict/workAuthorization","GET/job/api/v3/dict/languages","GET/job/api/v3/jobs/preference/{}","POST/job/api/v3/jobs/preference","GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/degrees","GET/job/api/v3/column/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state"]', 498, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 18:49:42');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Submit To Client', 502, '["GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/email/api/v3/template/application-templates","GET/company/api/v3/saleslead/client-contact/company/{}","GET/email/api/v3/template/{}","POST/application/api/v3/talent-recruitment-processes/submit-to-client","POST/email/api/v3/campaign/send_rich_mail","GET/talent/api/v3/talent-resumes/talent/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}"]', 498, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 18:53:26');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Interview', 503, '["GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","POST/application/api/v3/talent-recruitment-processes/interview","GET/company/api/v3/saleslead/client-contact/company/{}","GET/email/api/v3/template/application-templates","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail","GET/talent/api/v3/talent-resumes/talent/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}"]', 498, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 18:54:43');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Offer', 504, '["GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","POST/application/api/v3/talent-recruitment-processes/offer","GET/email/api/v3/template/application-templates","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail","GET/talent/api/v3/talent-resumes/talent/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}"]', 498, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 18:55:09');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Offer Accept', 505, '["GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates/{}","GET/company/api/v3/saleslead/client-contact/company/{}","POST/application/api/v3/talent-recruitment-processes/offer-accept","GET/email/api/v3/template/application-templates","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail","GET/talent/api/v3/talent-resumes/talent/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}"]', 498, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 18:55:24');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('On Board', 506, '["GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates/{}","GET/company/api/v3/saleslead/client-contact/company/{}","POST/application/api/v3/talent-recruitment-processes/onboard","GET/email/api/v3/template/application-templates","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail","GET/talent/api/v3/talent-resumes/talent/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}"]', 498, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 18:44:38');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Sumbit To Job', 507, '["GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/jobs/preference/{}","GET/job/api/v3/dict/languages","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/workAuthorization","GET/job/api/v3/dict/industries","POST/talent/api/v3/talents/search","GET/job/api/v3/column/{}","GET/location/api/v3/geo-geoinfo/en/city-or-state","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/talent/api/v3/talents/{}","GET/talent/api/v3/talent-resumes/talent/{}","POST/application/api/v3/talent-recruitment-processes/submit-to-job","GET/email/api/v3/template/application-templates","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","POST/job/api/v3/jobs/search","GET/job/api/v3/jobs/{}","GET/company/api/v3/company/client/{}"]', 499, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 22:41:32');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Submit To Client', 508, '["GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/talent/api/v3/talent/{}/ownerships","GET/email/api/v3/template/application-templates","GET/company/api/v3/saleslead/client-contact/company/{}","GET/email/api/v3/template/{}","POST/application/api/v3/talent-recruitment-processes/submit-to-client","POST/email/api/v3/campaign/send_rich_mail","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/recruitment-processes/default","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/company/api/v3/company/client/{}"]', 499, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 22:18:29');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Interview', 509, '["GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","POST/application/api/v3/talent-recruitment-processes/interview","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/email/api/v3/template/application-templates","GET/company/api/v3/saleslead/client-contact/company/{}","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail","GET/company/api/v3/company/client/{}"]', 499, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 22:41:47');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Offer', 510, '["GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","POST/application/api/v3/talent-recruitment-processes/offer","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/company/api/v3/saleslead/client-contact/company/{}","GET/email/api/v3/template/application-templates","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail","GET/application/api/v3/recruitment-processes/{}","GET/company/api/v3/company/client/{}"]', 499, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 22:41:55');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Offer Accept', 511, '["GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates/{}","GET/company/api/v3/saleslead/client-contact/company/{}","POST/application/api/v3/talent-recruitment-processes/offer-accept","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","POST/email/api/v3/campaign/send_rich_mail","GET/email/api/v3/template/{}","GET/email/api/v3/template/application-templates","GET/company/api/v3/company/client/{}"]', 499, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 22:42:59');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('On Board', 512, '["GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates/{}","GET/company/api/v3/saleslead/client-contact/company/{}","POST/application/api/v3/talent-recruitment-processes/onboard","GET/email/api/v3/template/application-templates","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail","GET/application/api/v3/talent-recruitment-processes/{}","GET/company/api/v3/company/client/{}"]', 499, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 22:43:09');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Sumbit To Job', 513, '["GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/company/api/v3/saleslead/client-contact/company/{}","POST/talent/api/v3/talents/search","GET/job/api/v3/jobs/preference/{}","GET/job/api/v3/dict/jobFunctions","GET/job/api/v3/dict/languages","GET/job/api/v3/dict/degrees","GET/job/api/v3/dict/workAuthorization","GET/job/api/v3/dict/industries","GET/job/api/v3/column/{}","GET/talent/api/v3/talents/{}","GET/talent/api/v3/talent-resumes/talent/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","POST/application/api/v3/talent-recruitment-processes/submit-to-job","GET/application/api/v3/talent-recruitment-processes/talentId/{}","GET/talent/api/v3/talent/{}/ownerships","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","POST/job/api/v3/jobs/search","GET/job/api/v3/jobs/{}","POST/email/api/v3/campaign/send_rich_mail","GET/email/api/v3/template/{}","GET/email/api/v3/template/application-templates"]', 500, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 20:59:52');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('On Board', 514, '["GET/application/api/v3/talent-recruitment-processes/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/company/api/v3/saleslead/client-contact/company/{}","POST/application/api/v3/talent-recruitment-processes/onboard","GET/finance/api/v3/starts/talentId/{}","GET/email/api/v3/template/application-templates","GET/email/api/v3/template/{}","POST/email/api/v3/campaign/send_rich_mail"]', 500, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 20:58:55');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('User Permissions', 516, '["GET/user/api/v3/permissions/roles","GET/user/api/v3/permissions/users/{}/roles","GET/user/api/v3/permissions/users/{}/data-permissions","GET/user/api/v3/permissions/teams/tree","POST/user/api/v3/permissions/users/{}/data-permissions","POST/user/api/v3/permissions/users/{}/roles","GET/user/api/v3/activated-users"]', 475, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-23 09:01:50');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Active/Inactive Users', 517, '["PUT/user/api/v3/users/{}","GET/user/api/v3/tenants/credit/{}"]', 475, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 22:56:42');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Role Permissions', 518, null, 453, 0, 3, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Create', 519, '["POST/user/api/v3/permissions/roles","GET/user/api/v3/permissions/roles"]', 518, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:18:29');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Acvite/Inactive ', 520, '["GET/user/api/v3/permissions/roles/{}/count/active-users","PUT/user/api/v3/permissions/roles/{}/inactive","PUT/user/api/v3/permissions/roles/{}/active"]', 518, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:19:48');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update', 521, '["PUT/user/api/v3/permissions/roles"]', 518, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:20:33');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Page Permissions', 522, '["GET/user/api/v3/permissions/roles/{}/privileges","GET/user/api/v3/permissions/privileges/tree/all","POST/user/api/v3/permissions/roles/{}/privileges"]', 518, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:22:30');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Data Permissions', 523, '["GET/user/api/v3/permissions/roles/my-involved-modules","GET/user/api/v3/permissions/roles/{}/data-permissions","GET/user/api/v3/permissions/teams/tree","POST/user/api/v3/permissions/roles/{}/data-permissions"]', 518, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:26:18');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Team Member', 525, null, 476, 0, 4, 0, '', '2022-11-12 14:56:41', null, null);
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Add', 526, '["GET/user/api/v3/permissions/teams/{}/non-contains/users","GET/user/api/v3/permissions/teams/{}/users","POST/user/api/v3/permissions/teams/{}/users","GET/user/api/v3/permissions/teams/tree"]', 525, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:06:22');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 527, '["GET/user/api/v3/permissions/teams/{}/users"]', 525, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:04:27');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Remove from Team', 528, '["DELETE/user/api/v3/permissions/teams/{}/users"]', 525, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:07:45');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Move to Another Teams', 529, '["PUT/user/api/v3/permissions/teams/users/transfer/primary","PUT/user/api/v3/permissions/teams/users/transfer/secondary"]', 525, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-23 06:22:52');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Set / Remove Team Leader', 530, '["PUT/user/api/v3/permissions/teams/{}/leader","GET/user/api/v3/permissions/teams/tree","DELETE/user/api/v3/permissions/teams/{}/leader/{}"]', 525, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 07:14:58');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('View', 531, '["GET/user/api/v3/permissions/roles"]', 518, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-20 23:17:16');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Eliminate', 532, '["GET/application/api/v3/talent-recruitment-processes/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/company/api/v3/saleslead/client-contact/company/{}","POST/application/api/v3/talent-recruitment-processes/commission","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}","POST/application/api/v3/talent-recruitment-processes/eliminate","POST/application/api/v3/talent-recruitment-processes/cancel-eliminate/talentRecruitmentProcessId/{}"]', 275, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 01:10:26');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Eliminate', 533, '["GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/talent/api/v3/talent/{}/ownerships","GET/company/api/v3/company/client/{}","POST/application/api/v3/talent-recruitment-processes/eliminate","POST/application/api/v3/talent-recruitment-processes/cancel-eliminate/talentRecruitmentProcessId/{}","GET/talent/api/v3/talent-resumes/talent/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}"]', 498, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 18:55:33');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Eliminate', 534, '["GET/application/api/v3/talent-recruitment-process-nodes/{}/{}","GET/application/api/v3/talent-recruitment-processes/{}","POST/application/api/v3/talent-recruitment-processes/eliminate","POST/application/api/v3/talent-recruitment-processes/cancel-eliminate/talentRecruitmentProcessId/{}","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/company/api/v3/company/client/{}"]', 499, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 22:43:17');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Eliminate', 535, '["GET/application/api/v3/talent-recruitment-processes/{}","POST/application/api/v3/talent-recruitment-processes/eliminate","POST/application/api/v3/talent-recruitment-processes/cancel-eliminate/talentRecruitmentProcessId/{}","GET/talent/api/v3/talent/{}/ownerships","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-process-nodes/{}/{}"]', 500, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-21 21:14:02');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Start', 536, '["GET/application/api/v3/talent-recruitment-processes/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/recruitment-process-node-page-sections/nodeType/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates","POST/application/api/v3/talent-recruitment-processes/onboard","GET/application/api/v3/recruitment-processes/default","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","GET/company/api/v3/company/client/{}","PUT/finance/api/v3/starts/failed-warranty/{}"]', 275, 1, 3, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-02 02:56:42');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Start', 537, '["GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/finance/api/v3/starts/talentId/{}","GET/company/api/v3/company/client/{}","GET/talent/api/v3/talent/{}/ownerships","GET/application/api/v3/talent-recruitment-processes/{}","GET/talent/api/v3/talent-resumes/talent/{}","GET/company/api/v3/saleslead/client-contact/{}","PUT/finance/api/v3/starts/{}","PUT/finance/api/v3/start-addresses/startId/{}","PUT/finance/api/v3/start-fte-rates/startId/{}","PUT/finance/api/v3/start-client-infos/startId/{}","POST/finance/api/v3/start-commissions/startId/{}","POST/finance/api/v3/starts/failed-warranty/{}","GET/application/api/v3/talent-recruitment-processes/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/jobId/{}","PUT/finance/api/v3/starts/failed-warranty/{}"]', 498, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 09:44:08');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Start', 538, '["GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/company/api/v3/company/client/{}","GET/company/api/v3/saleslead/client-contact/{}","GET/company/api/v3/saleslead/client-contact/company/{}","GET/finance/api/v3/starts/{}","PUT/finance/api/v3/starts/{}","PUT/finance/api/v3/start-addresses/startId/{}","GET/finance/api/v3/start-contract-rates/startId/{}","PUT/finance/api/v3/start-contract-rates/{}","POST/finance/api/v3/start-contract-rates/startId/{}","POST/finance/api/v3/starts/extension/{}","POST/finance/api/v3/starts/termination/{}","PUT/finance/api/v3/starts/cancel-termination/{}","GET/application/api/v3/talent-recruitment-processes/talentId/{}","POST/finance/api/v3/starts","DELETE/finance/api/v3/start-contract-rates/{}"]', 499, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 06:25:43');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Start', 539, '["GET/finance/api/v3/starts/talentId/{}","GET/application/api/v3/talent-recruitment-processes/no-object/talentId/{}","GET/application/api/v3/talent-recruitment-processes/{}","GET/company/api/v3/saleslead/client-contact/{}","GET/company/api/v3/saleslead/client-contact/company/{}","GET/finance/api/v3/starts/{}","PUT/finance/api/v3/start-addresses/startId/{}","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates","GET/application/api/v3/talent-recruitment-processes/ipg-offer-letter-cost-rates/{}","PUT/finance/api/v3/start-contract-rates/{}","POST/finance/api/v3/start-contract-rates/startId/{}","GET/finance/api/v3/start-contract-rates/startId/{}","POST/finance/api/v3/starts/extension/{}","PUT/finance/api/v3/starts/extension/{}","PUT/finance/api/v3/starts/{}","POST/finance/api/v3/starts/termination/{}","PUT/finance/api/v3/starts/cancel-termination/{}","GET/application/api/v3/talent-recruitment-processes/talentId/{}","DELETE/finance/api/v3/start-contract-rates/{}"]', 500, 1, 4, 0, '', '2022-11-12 14:56:41', '322,4', '2022-12-03 06:25:53');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Check Other Sales Leads With This Client', 540, null, 357, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-14 21:42:29');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Check Other Sales Leads With This Client', 541, null, 385, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-14 21:42:15');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Prospects Info', 542, '["GET/company/api/v3/company/prospect/{}","GET/company/api/v3/company/service-types","GET/company/api/v3/saleslead/client-contact/company/{}","GET/location/api/v3/geoinfo/search","POST/company/api/v3/saleslead/client-contact","PUT/company/api/v3/company/prospect"]', 417, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:27:45');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Update Prospects Info', 543, '["GET/company/api/v3/company/prospect/{}","GET/company/api/v3/company/service-types","GET/company/api/v3/saleslead/client-contact/company/{}","GET/location/api/v3/geoinfo/search","POST/company/api/v3/saleslead/client-contact","PUT/company/api/v3/company/prospect"]', 435, 1, 5, 0, '', '2022-11-12 14:56:41', '322,4', '2022-11-19 04:37:14');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Tenant Admin Portal', 544, '["GET/user/api/v3/tenants/details","PUT/user/api/v3/tenants","POST/company/api/v3/company/upload-avatar","GET/location/api/v3/geoinfo/search","POST/user/api/v3/tenants","GET/user/api/v3/tenants/{}","GET/user/api/v3/tenants"]', 451, 1, 2, 0, '83,3', '2022-11-16 23:02:25', '322,4', '2022-11-20 23:31:19');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Test 0001', 545, '["POST/user/api/v3/login-test2"]', 57, 1, 3, 0, '322,4', '2022-11-18 01:06:24', '322,4', '2022-11-18 01:06:24');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('skip-submit-to-am-companies', 547, '["GET/company/api/v3/skip-submit-to-am-companies"]', 29, 1, 2, 1, '322,4', '2022-11-18 20:09:36', '322,4', '2022-11-28 01:24:22');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Currency', 548, '["GET/job/api/v3/dict/currency/all"]', 29, 1, 2, 1, '322,4', '2022-11-18 20:10:58', '322,4', '2022-11-28 01:24:55');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Portal Account Management', 549, null, 208, 1, 4, 0, '322,4', '2022-11-20 18:17:13', '322,4', '2022-11-20 18:17:13');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Portal Account Management', 550, null, 238, 1, 4, 0, '322,4', '2022-11-20 18:17:30', '322,4', '2022-11-20 18:17:30');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get All Brief', 551, '["GET/user/api/v3/users/all-brief"]', 29, 1, 2, 1, '322,4', '2022-11-21 23:46:01', '322,4', '2022-11-28 01:26:16');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get All Privileges Tree', 552, '["GET/user/api/v3/permissions/privileges/tree/all"]', 29, 1, 2, 1, '322,4', '2022-11-21 23:47:45', '322,4', '2022-11-28 01:27:21');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Recruitment-processes Default', 553, '["GET/application/api/v3/recruitment-processes/default"]', 29, 1, 2, 1, '322,4', '2022-11-21 23:48:43', '322,4', '2022-11-28 01:27:59');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Get Client', 554, '["GET/company/api/v3/company/clients/list"]', 29, 1, 2, 1, '322,4', '2022-11-21 23:49:28', '322,4', '2022-11-28 01:28:25');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Change Password', 555, '["POST/user/api/v3/account/change_password"]', 29, 1, 2, 1, '322,4', '2022-11-21 23:50:47', '322,4', '2022-11-28 01:15:41');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Init Password', 556, '["POST/user/api/v3/account/admin/reset-password/init"]', 29, 1, 2, 1, '322,4', '2022-11-21 23:52:15', '322,4', '2022-11-28 01:14:51');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Register', 557, '["POST/user/api/v3/register"]', 29, 1, 2, 1, '322,4', '2022-11-21 23:56:23', '322,4', '2022-11-28 01:14:26');
INSERT INTO permission_privilege (name, id, api, parent_id, leaf, level, is_public, created_by, created_date, last_modified_by, last_modified_date) VALUES ('Pro Search', 559, '["POST/talent/api/v3/linkedin-projects/search-histories","GET/talent/api/v3/all-linkedin-projects","GET/talent/api/v3/my-linkedin-projects","GET/talent/api/v3/linkedin-projects/filters","GET/talent/api/v3/linkedin-projects/search-histories","POST/talent/api/v3/talent-tracking-notes","GET/talent/api/v3/watchlist/job/{}","POST/talent/api/v3/watchlist","GET/talent/api/v3/watchlist","POST/talent/api/v3/linkedin-projects/filters","POST/talent/api/v3/linkedin-projects","GET/talent/api/v3/linkedin-project-talents/linkedinProjectId/{}","PUT/talent/api/v3/linkedin-talents/{}","POST/talent/api/v3/linkedin-talents","POST/talent/api/v3/linkedin-project-talents","DELETE/talent/api/v3/linkedin-projects/filters/{}","DELETE/talent/api/v3/linkedin-projects/search-histories/{}","PUT/talent/api/v3/linkedin-projects/{}","GET/talent/api/v3/linkedin-project-talents/statusCount/linkedinProjectId/{}","POST/talent/api/v3/talent-tracking-records/search","POST/talent/api/v3/talent-tracking-records","GET/talent/api/v3/talent-tracking-notes/sync-to-apn-talent-note","POST/email/api/v3/template","PUT/email/api/v3/template/{}","GET/talent/api/v3/talent-tracking-notes","POST/talent/api/v3/search-talents-es/by-contacts","POST/user/api/v3/credit-transactions","GET/talent/api/v3/es-talents","GET/email/api/v3/template/my-templates","POST/talent/api/v3/linkedin-projects/favorite/{}","POST/talent/api/v3/linkedin-projects/unfavorite/{}","PUT/talent/api/v3/linkedin-project-talents/{}","POST/talent/api/v3/linkedin-talent-contacts/linkedinTalentId/{}","PUT/talent/api/v3/pro/talents/{}"]', 29, 1, 2, 1, '322,4', '2022-12-02 07:48:40', '322,4', '2022-12-05 02:37:01');

create table permission_role_privilege
(
    id                 bigint auto_increment
        primary key,
    role_id            bigint                              not null,
    privilege_id       varchar(50)                         not null,
    tenant_id          bigint                              null,
    created_by         varchar(50)                         not null,
    created_date       timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by   varchar(50)                         null,
    last_modified_date timestamp                           null
)
    charset = utf8mb4;

create index idx_role_privilege
    on permission_role_privilege (role_id, privilege_id);
