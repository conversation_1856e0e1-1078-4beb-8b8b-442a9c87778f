# 对apnv2平移表进行修改
ALTER TABLE `job_contact_relation`
<PERSON>AN<PERSON> COLUMN `user_id` `client_contact_id` BIGINT NOT NULL , RENAME TO  `job_company_contact_relation` ;

ALTER TABLE `job_location`
DROP COLUMN `latitude`,
DROP COLUMN `longitude`,
DROP COLUMN `locations`,
ADD COLUMN `official_county` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL AFTER `official_province`,
ADD COLUMN `location_id` bigint NULL DEFAULT NULL AFTER `official_county`,
ADD INDEX idx_job_location_job_id_official_country(job_id, official_country),
ADD INDEX index_jl_official_country(`official_country`),
DROP INDEX idx_job_location_job_id;

ALTER TABLE job_ipg_relation MODIFY COLUMN ipg_job_description text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

ALTER TABLE linkedin_stats
ADD INDEX idx_linkedin_stats_tenant_id_type_created_date_user_id(`tenant_id`, `type`, `created_date`, `user_id`),
DROP INDEX idx_linkedin_stats_tenant_id;

ALTER TABLE `apnv3`.`talent_ownership`
CHANGE COLUMN `application_id` `talent_recruitment_process_id` bigint NULL DEFAULT NULL AFTER `expire_time`;

alter table invoice modify column due_amount decimal(12,2);
alter table invoice_payment_record modify column paid_amount decimal(10,2);

ALTER TABLE `start_termination`
ADD COLUMN `start_status` int NOT NULL;

update start_termination st set st.start_status=0 where st.application_status=9;
update start_termination st set st.start_status=5 where st.application_status=91;
update start_termination st set st.start_status=10 where st.application_status=92;
update start_termination st set st.start_status=15 where st.application_status=94;

alter table start_termination drop column application_status;

### -- update token client_secret  ####
update oauth_client_details set client_secret = '$2a$10$3AdVxOJHqBLEb1UceCiwterM22E.EUjOUu117JHxbMp3QI8PuwK2m';


alter table user add data_scope int default 1;
alter table user drop column division_id;

### -- migrate data from table authority to table role  ####
CREATE TABLE `role` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(50) DEFAULT NULL,
  `data_scope` tinyint DEFAULT NULL,
  `is_internal` tinyint DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `tenant_id` bigint DEFAULT NULL,
  `status` int DEFAULT '0',
  `description` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=151 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

insert into role (id, name, data_scope, is_internal, user_id, tenant_id, status, description)
values ('1','ROLE_SUPER_ADMIN','99','1',NULL,NULL,'0',NULL),
('2','ROLE_PLATFORM_ADMIN','99','1',NULL,NULL,'0',NULL),
('100','ROLE_TENANT_ADMIN','99','1',NULL,'4','0',NULL);

insert into role (name, data_scope, is_internal, user_id, tenant_id, status, description)
select name, 2, 0, null, 4, 1, null from authority where name != 'ROLE_TENANT_ADMIN';
### -- migrate data from table authority to table role  END =====

### -- migrate data from table user_authority to table user_role
CREATE TABLE `user_role` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `role_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_user_role` (`user_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

insert into user_role (user_id, role_id)
select ua.user_id, a.id as role_id from user_authority ua left join role a on a.name=ua.authority_name;

### -- create user account for xxl job  ####
-- SET @user_id=(select max(id) from user);

SET foreign_key_checks = 0;
drop table user_authority;
SET foreign_key_checks = 1;

CREATE TABLE `timesheet_user_role` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` bigint DEFAULT NULL,
  `role_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_user_role` (`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

insert into timesheet_user_role (user_id, role_id)
select ua.timesheet_user_id, a.id as role_id from timesheet_user_authority ua left join role a on a.name=ua.authority_name;

SET foreign_key_checks = 0;
drop table timesheet_user_authority;
SET foreign_key_checks = 1;

ALTER TABLE `time_sheet_user`
ADD COLUMN `first_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL AFTER `last_modified_date`,
ADD COLUMN `last_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NULL DEFAULT NULL AFTER `first_name`;

drop table authority;
### -- migrate data from table user_authority to table user_role END ===

alter table invoice change column division_id team_id bigint;
update invoice set team_id = 25 where team_id = 11;
update invoice set team_id = 44 where team_id in (28, 39);

### -- create view view_invoice_list ===
CREATE
    ALGORITHM = UNDEFINED
    DEFINER = `apn_admin`@`%`
    SQL SECURITY DEFINER
VIEW `view_invoice_list` AS
    SELECT
        `i`.`id` AS `id`,
        `i`.`tenant_id` AS `tenant_id`,
        `i`.`invoice_no` AS `invoice_no`,
        `i`.`sub_invoice_no` AS `sub_invoice_no`,
        `i`.`invoice_date` AS `invoice_date`,
        `i`.`due_date` AS `due_date`,
        `i`.`invoice_type` AS `invoice_type`,
        `i`.`currency` AS `currency`,
        `i`.`status` AS `status`,
        `i`.`due_amount` AS `due_amount`,
        (SELECT
                SUM(`p`.`paid_amount`)
            FROM
                `invoice_payment_record` `p`
            WHERE
                (`i`.`id` = `p`.`invoice_id`)) AS `received_amount`,
        (`i`.`due_amount` - (SELECT
                SUM(`p`.`paid_amount`)
            FROM
                `invoice_payment_record` `p`
            WHERE
                (`i`.`id` = `p`.`invoice_id`))) AS `balance`,
        (SELECT
                MAX(`p`.`payment_date`)
            FROM
                `invoice_payment_record` `p`
            WHERE
                (`i`.`id` = `p`.`invoice_id`)) AS `payment_date`,
        `i`.`company_id` AS `company_id`,
        `i`.`customer_name` AS `customer_name`,
        `i`.`job_id` AS `job_id`,
        `i`.`job_title` AS `job_title`,
        `i`.`talent_id` AS `talent_id`,
        `i`.`talent_name` AS `talent_name`,
        `i`.`team_id` AS `team_id`,
        `i`.`created_date` AS `created_date`
    FROM
        `invoice` `i`;
### -- create view view_invoice_list END ===

### -- migrate data from address to tenant_address ===
CREATE TABLE `tenant_address` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address_2` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city_id` bigint DEFAULT NULL,
  `zipcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_fk_city_id` (`city_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3000 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

insert into tenant_address
    select id, address, address_2, city_id, zipcode from address where type=4;
drop table address;
### -- migrate data from address to tenant_address END ===

ALTER TABLE `onboarding_drafts`
CHANGE COLUMN `application_id` `talent_recruitment_process_id` BIGINT NOT NULL ;

ALTER TABLE `onboarding_process_approval_details`
CHANGE COLUMN `application_id` `talent_recruitment_process_id` BIGINT NOT NULL ;

ALTER TABLE `onboarding_process_histories`
CHANGE COLUMN `application_id` `talent_recruitment_process_id` BIGINT NOT NULL ;

ALTER TABLE `onboarding_process_operation_details`
CHANGE COLUMN `application_id` `talent_recruitment_process_id` BIGINT NOT NULL ;

TRUNCATE  `async_record`;

TRUNCATE `persistent_audit_event`;

TRUNCATE `persistent_audit_evt_data`;

-- 分布式事务 Seata 使用
CREATE TABLE `undo_log`
(
    `id`            bigint(20) NOT NULL AUTO_INCREMENT,
    `branch_id`     bigint(20) NOT NULL,
    `xid`           varchar(100) NOT NULL,
    `context`       varchar(128) NOT NULL,
    `rollback_info` longblob     NOT NULL,
    `log_status`    int(11) NOT NULL,
    `log_created`   datetime     NOT NULL,
    `log_modified`  datetime     NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

-- drop duplicate user_account but keep one
delete from user_account where id not in (select * from (select max(id) from user_account group by user_id) as a);

-- Assign Tenant Admin the highest data permission
update user u left join user_role ur on u.id = ur.user_id set u.data_scope=99
where ur.role_id=100 and u.activated=1 and u.tenant_id=4;

insert into user values (1501, '1501,4','Synchronize_Talent','<EMAIL>','$2a$10$BBoc801Zxamw6wjbDIAV9OTvfjUyKsR58QnN//ZaptZzqEyugW2z2','','','','4',NULL,1,'en',NULL,NULL,'','0','0','-1,-1','2022-10-16 00:44:49.000',NULL,'-1,-1','2022-10-16 02:27:26.441','1');
insert into user values (1502, '1502,4','Synchronize_Job','<EMAIL>','$2a$10$BBoc801Zxamw6wjbDIAV9OTvfjUyKsR58QnN//ZaptZzqEyugW2z2','','','','4',NULL,1,'en',NULL,NULL,'','0','0','-1,-1','2022-10-16 00:44:49.000',NULL,'-1,-1','2022-10-16 02:27:26.441','99');
insert into user values (1503, '1503,4','Synchronize_AM_Report','<EMAIL>','$2a$10$BBoc801Zxamw6wjbDIAV9OTvfjUyKsR58QnN//ZaptZzqEyugW2z2','','','','4',NULL,1,'en',NULL,NULL,'','0','0','-1,-1','2022-10-16 00:44:49.000',NULL,'-1,-1','2022-10-16 02:27:26.441','1');

insert into role (id, name, data_scope, is_internal, user_id, tenant_id, status, description)
values ('99','ROLE_SYSTEM','1','1',NULL,'4','0',NULL);

insert into user_role (user_id, role_id)
values (1501, 99),
       (1502, 99),
       (1503, 99);
