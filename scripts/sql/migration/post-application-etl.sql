### --    start  -- ############
ALTER TABLE `start`
    ADD COLUMN `talent_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
ADD COLUMN  `job_title` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
ADD COLUMN  `company` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL;

update start s
    left join talent t on t.id=s.talent_id
    left join company c on c.id=s.company_id
    left join job j on j.id=s.job_id
    set s.talent_name=t.full_name, s.company=c.name, s.job_title=j.title;

alter table start modify note mediumtext;

### -- 将start_fte_rate表的数据迁移到start_fte_salary_package表 ####
CREATE TABLE `start_fte_salary_package` (
                                            `id` bigint NOT NULL AUTO_INCREMENT,
                                            `start_id` bigint NOT NULL,
                                            `salary_type` int DEFAULT NULL,
                                            `amount` decimal(10,2) DEFAULT NULL,
                                            `need_charge` bit(1) NOT NULL,
                                            `created_by` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
                                            `created_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                            `last_modified_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                            `last_modified_date` timestamp NULL DEFAULT NULL,
                                            PRIMARY KEY (`id`),
                                            KEY `idx_start_fte_salary_package_start_id` (`start_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

insert into start_fte_salary_package(start_id, salary_type, amount, need_charge, created_by, created_date, last_modified_by, last_modified_date)
select r.start_id, 0, r.salary, 1, r.created_by, r.created_date, r.last_modified_by, r.last_modified_date from start_fte_rate r where r.salary is not null;

insert into start_fte_salary_package(start_id, salary_type, amount, need_charge, created_by, created_date, last_modified_by, last_modified_date)
select r.start_id, 1, r.retention_bonus, 1, r.created_by, r.created_date, r.last_modified_by, r.last_modified_date from start_fte_rate r where r.retention_bonus is not null;

insert into start_fte_salary_package(start_id, salary_type, amount, need_charge, created_by, created_date, last_modified_by, last_modified_date)
select r.start_id, 2, r.sign_on_bonus, 1, r.created_by, r.created_date, r.last_modified_by, r.last_modified_date from start_fte_rate r where r.sign_on_bonus is not null;

insert into start_fte_salary_package(start_id, salary_type, amount, need_charge, created_by, created_date, last_modified_by, last_modified_date)
select r.start_id, 3, r.annual_bonus, 1, r.created_by, r.created_date, r.last_modified_by, r.last_modified_date from start_fte_rate r where r.annual_bonus is not null;

insert into start_fte_salary_package(start_id, salary_type, amount, need_charge, created_by, created_date, last_modified_by, last_modified_date)
select r.start_id, 4, r.relocation_package, 1, r.created_by, r.created_date, r.last_modified_by, r.last_modified_date from start_fte_rate r where r.relocation_package is not null;

insert into start_fte_salary_package(start_id, salary_type, amount, need_charge, created_by, created_date, last_modified_by, last_modified_date)
select r.start_id, 5, r.extra_fee, 1, r.created_by, r.created_date, r.last_modified_by, r.last_modified_date from start_fte_rate r where r.extra_fee is not null;

alter table start_fte_rate drop column salary;
alter table start_fte_rate drop column sign_on_bonus;
alter table start_fte_rate drop column retention_bonus;
alter table start_fte_rate drop column annual_bonus;
alter table start_fte_rate drop column relocation_package;
alter table start_fte_rate drop column extra_fee;

update start_fte_rate set fee_percentage=fee_percentage/100 where fee_type=1 and fee_percentage > 1;

#删除程序迁移后不再使用的表
-- ALTER TABLE `activity`
-- DROP FOREIGN KEY `fk_activity_application_id`;
-- ALTER TABLE `activity`
-- DROP INDEX `fk_activity_application_id` ;
-- DROP TABLE if exists `application`;
-- DROP TABLE if exists `activity`;
-- DROP TABLE if exists `activity_unique`;
-- DROP TABLE if exists `application_agreed_pay_rate`;
-- DROP TABLE if exists `application_commission`;
-- DROP TABLE if exists `application_offer_letter`;

# NOTE:
alter table tenant
drop foreign key fk_tenant_company_id;

# 增加talent_recruitment_process表trigger
CREATE DEFINER=`apn_admin`@`%` TRIGGER `trigger_talent_recruitment_process_insert` AFTER INSERT ON `talent_recruitment_process` FOR EACH ROW
UPDATE job j
    LEFT JOIN talent_recruitment_process c ON j.id = c.job_id
    SET j.last_edited_time = CURRENT_TIMESTAMP ()
WHERE
    c.id = NEW.id;

CREATE DEFINER=`apn_admin`@`%` TRIGGER `trigger_talent_recruitment_process_update` AFTER UPDATE ON `talent_recruitment_process` FOR EACH ROW
UPDATE job j
    LEFT JOIN talent_recruitment_process c ON j.id = c.job_id
    SET j.last_edited_time = CURRENT_TIMESTAMP ()
WHERE
    c.id = NEW.id;

CREATE DEFINER=`apn_admin`@`%` TRIGGER `trigger_talent_recruitment_process_delete` AFTER DELETE ON `talent_recruitment_process` FOR EACH ROW
UPDATE job j
    LEFT JOIN talent_recruitment_process c ON j.id = c.job_id
    SET j.last_edited_time = CURRENT_TIMESTAMP ()
WHERE
    c.id = OLD.id;
