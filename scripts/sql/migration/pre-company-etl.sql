alter table company_service_type
    modify id bigint auto_increment;

alter table company_service_type
    modify service_type bigint null;

alter table company_address
    modify company_id int null;

update apn.company set created_by = '17,4' where created_by is null;

alter table company_sales_lead_administrator
    modify sales_lead_id bigint null;

alter table tenant
drop foreign key fk_tenant_company_id;

alter table company
    modify id bigint not null;
alter table company_contract
    modify id bigint not null;
alter table company_progress_note
    modify id bigint not null;
alter table company_project_team
    modify id bigint not null;
alter table company_project_team_user
    modify id bigint not null;
alter table company_sales_lead
    modify id bigint not null;
alter table company_sales_lead_client_contact
    modify id bigint not null;
alter table company_service_type
    modify id bigint not null;