alter table company
    modify created_by var<PERSON><PERSON>(50) not null;

alter table company
    modify id bigint auto_increment;
alter table company_contract
    modify id bigint auto_increment;
alter table company_progress_note
    modify id bigint auto_increment;
alter table company_project_team
    modify id bigint auto_increment;
alter table company_project_team_user
    modify id bigint auto_increment;
alter table company_sales_lead
    modify id bigint auto_increment;
alter table company_sales_lead_client_contact
    modify id bigint auto_increment;
alter table company_service_type
    modify id bigint auto_increment;

alter table tenant
    add constraint fk_tenant_company_id
        foreign key (company_id) references company (id);

update company_address c left join city_locations_en e on c.city_id = e.id set city_id = null where city_id is not null and e.id is null;