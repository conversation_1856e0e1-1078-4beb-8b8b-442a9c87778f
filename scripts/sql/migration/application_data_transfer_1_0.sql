create table if not exists talent_recruitment_process
(
    id                     bigint auto_increment primary key,
    recruitment_process_id bigint                                    not null,
    tenant_id              bigint                                    not null,
    talent_id              bigint                                    not null,
    job_id                 bigint                                    not null,
    note                   text                                      null,
    created_by             varchar(50)                               not null,
    created_date           timestamp default CURRENT_timestamp not null,
    last_modified_by       varchar(50)                               null,
    last_modified_date     timestamp                              null
    );

create index idx_talent_recruitment_process_job_id
    on talent_recruitment_process (job_id);

create index idx_talent_recruitment_process_rpid
    on talent_recruitment_process (recruitment_process_id);

create index idx_talent_recruitment_process_talent_id
    on talent_recruitment_process (talent_id);

create index idx_talent_recruitment_process_tenant_id
    on talent_recruitment_process (tenant_id);



create table if not exists talent_recruitment_process_commission
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    note                          mediumtext                                null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_commission_trpid
    on talent_recruitment_process_commission (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_eliminate
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    reason                        int                                       not null,
    note                          mediumtext                                null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_eliminate_trpid
    on talent_recruitment_process_eliminate (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_interview
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    progress                      int                                       not null,
    from_time                     timestamp default CURRENT_timestamp not null,
    to_time                       timestamp default CURRENT_timestamp not null,
    interview_type                int                                       null,
    time_zone                     varchar(50)                               null,
    note                          mediumtext                                null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_interview_trpid_created_date
    on talent_recruitment_process_interview (talent_recruitment_process_id, created_date);

create table if not exists talent_recruitment_process_ipg_agreed_pay_rate
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    currency                      int                                       not null,
    rate_unit_type                int                                       not null,
    agreed_pay_rate               decimal(10, 2)                            not null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_ipg_agreed_pay_rate_tid
    on talent_recruitment_process_ipg_agreed_pay_rate (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_ipg_contract_fee_charge
(
    id                              bigint auto_increment primary key,
    talent_recruitment_process_id   bigint                                    not null,
    final_bill_rate                 decimal(20, 2)                            not null,
    final_pay_rate                  decimal(20, 2)                            not null,
    tax_burden_rate                 varchar(100)                              not null,
    msp_rate                        varchar(100)                              not null,
    immigration_cost                varchar(100)                              not null,
    extra_cost                      decimal(20, 2)                            null,
    estimated_working_hour_per_week decimal(10, 2)                            null,
    gm                              decimal(20, 2)                            null,
    created_by                      varchar(50)                               not null,
    created_date                    timestamp default CURRENT_timestamp not null,
    last_modified_by                varchar(50)                               null,
    last_modified_date              timestamp                              null
    );

create index idx_trp_ipg_contract_fee_charge_tid
    on talent_recruitment_process_ipg_contract_fee_charge (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_ipg_fte_salary_package
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    salary_type                   int                                       not null,
    amount                        decimal(10, 2)                            not null,
    need_charge                   bit                                       not null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_ipg_fte_salary_package_trpid
    on talent_recruitment_process_ipg_fte_salary_package (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_ipg_offer_accept
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    note                          mediumtext                                null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_ipg_offer_accept_trpid_created_date
    on talent_recruitment_process_ipg_offer_accept (talent_recruitment_process_id, created_date);

create table if not exists talent_recruitment_process_ipg_offer_letter_cost_rate
(
    id          bigint auto_increment primary key,
    tenant_id   bigint         not null,
    currency    int            not null,
    rate_type   int            not null,
    code        varchar(100)   not null,
    description varchar(250)   null,
    value       decimal(10, 4) not null,
    expire_date date           not null,
    constraint idx_trp_ipg_offer_letter_cost_rate_tid
    unique (tenant_id, currency, rate_type, code, value)
    );

create table if not exists talent_recruitment_process_kpi_user
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    user_id                       bigint                                    not null,
    user_role                     int                                       null,
    percentage                    decimal(10, 4)                            null,
    currency                      int                                       null,
    amount                        decimal(10, 2)                            null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_kpi_user_trpid_user_role_user_id
    on talent_recruitment_process_kpi_user (talent_recruitment_process_id, user_role, user_id);

create index idx_trp_kpi_user_user_id
    on talent_recruitment_process_kpi_user (user_id);

create table if not exists talent_recruitment_process_node
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    node_id                       bigint                                    not null,
    node_type                     int                                       not null,
    node_status                   int                                       not null,
    next_node_id                  bigint                                    null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_node_trpid
    on talent_recruitment_process_node (talent_recruitment_process_id);

create index idx_trp_node_node_id
    on talent_recruitment_process_node (node_id);

create index idx_trp_node_node_type
    on talent_recruitment_process_node (node_type);

create index idx_trp_node_node_status
    on talent_recruitment_process_node (node_status);


create table if not exists talent_recruitment_process_offer
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    signed_date                   date                                      null,
    estimate_onboard_date         date                                      null,
    note                          mediumtext                                null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_offer_trpid_created_date
    on talent_recruitment_process_offer (talent_recruitment_process_id, created_date);

create table if not exists talent_recruitment_process_offer_fee_charge
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    total_billable_amount         decimal(10, 2)                            null,
    fee_type                      int                                       null,
    fee_amount                    decimal(10, 2)                            null,
    total_amount                  decimal(10, 2)                            null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_offer_fee_charge_trpid
    on talent_recruitment_process_offer_fee_charge (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_offer_salary
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    currency                      int                                       not null,
    rate_unit_type                int                                       not null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_offer_salary_trpid
    on talent_recruitment_process_offer_salary (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_offer_salary_package
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    salary_type                   int                                       null,
    amount                        decimal(10, 2)                            null,
    need_charge                   bit                                       not null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_offer_salary_package_trpid
    on talent_recruitment_process_offer_salary_package (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_onboard
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    note                          mediumtext                                null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_onboard_trpid_created_date
    on talent_recruitment_process_onboard (talent_recruitment_process_id, created_date);

create table if not exists talent_recruitment_process_onboard_client_info
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    client_contact_id             bigint                                    null,
    client_name                   varchar(125)                              null,
    client_division               varchar(50)                               null,
    client_address                varchar(255)                              null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trpoci_talent_recruitment_process_id
    on talent_recruitment_process_onboard_client_info (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_onboard_date
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    onboard_date                  date                                      null,
    warranty_end_date             date                                      null,
    end_date                      date                                      null,
    currency                      int                                       null,
    rate_unit_type                int                                       null,
    created_by                    varchar(50)                               null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_onboard_date_trpid
    on talent_recruitment_process_onboard_date (talent_recruitment_process_id);

create table if not exists talent_recruitment_process_submit_to_client
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    submit_time                   timestamp default CURRENT_timestamp not null,
    note                          mediumtext                                null,
    email_tracking_number         varchar(50)                               null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_submit_to_client_trpid_created_date
    on talent_recruitment_process_submit_to_client (talent_recruitment_process_id, created_date);

create table if not exists talent_recruitment_process_submit_to_job
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    note                          mediumtext                                null,
    skills                        text                                      null,
    recommend_comments            text                                      null,
    created_by                    varchar(50)                               not null,
    created_date                  timestamp default CURRENT_timestamp not null,
    last_modified_by              varchar(50)                               null,
    last_modified_date            timestamp                              null
    );

create index idx_trp_submit_to_job_trpid_created_date
    on talent_recruitment_process_submit_to_job (talent_recruitment_process_id, created_date);

-- 初始化recruitment process（流程配置主表）并注入测试数据
create table recruitment_process
(
    id                       bigint auto_increment primary key,
    recruitment_process_type int                                       not null,
    tenant_id                bigint                                    not null,
    job_type                 int                                       null,
    name                     varchar(255)                              not null,
    description              varchar(500)                              null,
    status                   int                                       not null,
    created_by               varchar(50)                               not null,
    created_date             timestamp default CURRENT_timestamp not null,
    last_modified_by         varchar(50)                               null,
    last_modified_date       timestamp                              null
);

create index idx_recruitment_process_tid
    on recruitment_process (tenant_id);

INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (4, 2, 2, null, '谷歌校园招聘', '2022年夏季校园招聘', 0, '79,2', '2022-03-17 20:16:43', '79,2', '2022-03-17 20:16:43');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (6, 2, 14, null, '默认招聘流程', '默认招聘流程说明', 0, '774,14', '2022-04-18 06:02:14', '774,14', '2022-04-18 06:02:14');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (10, 2, 14, 3, '测试流程-普通版', '测试-普通版', 0, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (11, 2, 14, 1, '测试流程-普通版', '测试-普通版', 0, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (12, 2, 14, 4, '测试流程-普通版', '测试-普通版', 0, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (13, 2, 14, 5, '测试流程-普通版', '测试-普通版', 0, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (14, 2, 14, 0, '测试流程-普通版', '测试-普通版', 0, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (15, 2, 14, 2, '测试流程-普通版', '测试-普通版', 0, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (22, 2, 2, 3, '测试流程', '测试', 0, '210,4', '2022-05-24 06:43:35', '210,4', '2022-05-24 06:43:35');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (23, 2, 2, 1, '测试流程', '测试', 0, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (24, 2, 2, 4, '测试流程', '测试', 0, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (25, 2, 2, 5, '测试流程', '测试', 0, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (26, 2, 2, 0, '测试流程', '测试', 0, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (27, 2, 2, 2, '测试流程', '测试', 0, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (28, 2, 4, 3, '测试IPG-FULL_TIME流程', '测试FULL_TIME流程', 0, '210,4', '2022-05-25 03:24:45', '210,4', '2022-05-25 03:24:45');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (29, 2, 4, 1, '测试IPG-CONTRACT-流程', '测试', 0, '210,4', '2022-05-25 03:24:50', '210,4', '2022-05-25 03:24:50');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (30, 2, 4, 5, '测试IPG-PAY_ROLL-流程', '测试', 0, '210,4', '2022-05-25 03:24:54', '210,4', '2022-05-25 03:24:54');
INSERT INTO recruitment_process (id, recruitment_process_type, tenant_id, job_type, name, description, status, created_by, created_date, last_modified_by, last_modified_date) VALUES (31, 2, 4, null, '测试招聘流程-No Jobtype', 'Test', 0, '210,4', '2022-05-28 16:44:27', '210,4', '2022-05-28 16:44:27');

-- 初始化recruitment_process_node(流程配置副表)并注入测试数据

create table recruitment_process_node
(
    id                     bigint auto_increment primary key,
    recruitment_process_id bigint                                    not null,
    tenant_id              bigint                                    not null,
    node_type              int                                       not null,
    name                   varchar(255)                              not null,
    description            varchar(500)                              null,
    next_node_id           bigint                                    null,
    created_by             varchar(50)                               not null,
    created_date           timestamp default CURRENT_timestamp not null,
    last_modified_by       varchar(50)                               null,
    last_modified_date     timestamp                              null
);

create index idx_recruitment_process_node_tid
    on recruitment_process_node (tenant_id);

create index idx_recruitment_process_node_trpid
    on recruitment_process_node (recruitment_process_id);

INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (12, 3, 4, 10, 'Submit to job', 'Default', 13, '210,4', '2022-03-16 20:01:16', '210,4', '2022-03-16 20:01:16');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (13, 3, 4, 20, 'Submit to client', 'Default', 14, '210,4', '2022-03-16 20:01:16', '210,4', '2022-03-16 20:01:16');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (14, 3, 4, 30, 'Interview', 'Default', 15, '210,4', '2022-03-16 20:01:16', '210,4', '2022-03-16 20:01:16');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (15, 3, 4, 40, 'Offer', 'Default', 16, '210,4', '2022-03-16 20:01:16', '210,4', '2022-03-16 20:01:16');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (16, 3, 4, 41, 'Offer Accept', 'Default', 17, '210,4', '2022-03-16 20:01:16', '210,4', '2022-03-16 20:01:16');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (17, 3, 4, 60, 'Onboard', 'Default', null, '210,4', '2022-03-16 20:01:16', '210,4', '2022-03-16 20:01:16');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (18, 4, 2, 10, 'Submit to job', 'Default', 19, '79,2', '2022-03-17 20:16:43', '79,2', '2022-03-17 20:16:44');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (19, 4, 2, 20, 'Submit to client', 'Default', 20, '79,2', '2022-03-17 20:16:43', '79,2', '2022-03-17 20:16:44');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (20, 4, 2, 30, 'Interview', 'Default', 21, '79,2', '2022-03-17 20:16:43', '79,2', '2022-03-17 20:16:44');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (21, 4, 2, 40, 'Offer', 'Default', 22, '79,2', '2022-03-17 20:16:43', '79,2', '2022-03-17 20:16:44');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (22, 4, 2, 50, 'Commission', 'Default', 23, '79,2', '2022-03-17 20:16:43', '79,2', '2022-03-17 20:16:44');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (23, 4, 2, 60, 'Onboard', 'Default', null, '79,2', '2022-03-17 20:16:43', '79,2', '2022-03-17 20:16:43');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (30, 6, 14, 10, 'Submit to job', 'Default', 31, '774,14', '2022-04-18 06:02:15', '774,14', '2022-04-18 06:02:15');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (31, 6, 14, 20, 'Submit to client', 'Default', 32, '774,14', '2022-04-18 06:02:15', '774,14', '2022-04-18 06:02:15');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (32, 6, 14, 30, 'Interview', 'Default', 33, '774,14', '2022-04-18 06:02:15', '774,14', '2022-04-18 06:02:15');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (33, 6, 14, 40, 'Offer', 'Default', 34, '774,14', '2022-04-18 06:02:15', '774,14', '2022-04-18 06:02:15');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (34, 6, 14, 50, 'Commission', 'Default', 35, '774,14', '2022-04-18 06:02:15', '774,14', '2022-04-18 06:02:15');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (35, 6, 14, 60, 'Onboard', 'Default', null, '774,14', '2022-04-18 06:02:15', '774,14', '2022-04-18 06:02:15');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (36, 7, 4, 10, 'SUBMIT_TO_JOB', 'Default', 37, '210,4', '2022-05-24 05:38:10', '210,4', '2022-05-24 05:38:10');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (37, 7, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 38, '210,4', '2022-05-24 05:38:10', '210,4', '2022-05-24 05:38:10');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (38, 7, 4, 30, 'INTERVIEW', 'Default', 39, '210,4', '2022-05-24 05:38:10', '210,4', '2022-05-24 05:38:10');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (39, 7, 4, 40, 'OFFER', 'Default', 40, '210,4', '2022-05-24 05:38:10', '210,4', '2022-05-24 05:38:10');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (40, 7, 4, 41, 'OFFER_ACCEPT', 'Default', 41, '210,4', '2022-05-24 05:38:10', '210,4', '2022-05-24 05:38:10');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (41, 7, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 05:38:10', '210,4', '2022-05-24 05:38:10');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (42, 8, 4, 10, 'SUBMIT_TO_JOB', 'Default', 43, '210,4', '2022-05-24 05:38:14', '210,4', '2022-05-24 05:38:14');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (43, 8, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 44, '210,4', '2022-05-24 05:38:14', '210,4', '2022-05-24 05:38:14');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (44, 8, 4, 30, 'INTERVIEW', 'Default', 45, '210,4', '2022-05-24 05:38:14', '210,4', '2022-05-24 05:38:14');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (45, 8, 4, 40, 'OFFER', 'Default', 46, '210,4', '2022-05-24 05:38:14', '210,4', '2022-05-24 05:38:14');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (46, 8, 4, 41, 'OFFER_ACCEPT', 'Default', 47, '210,4', '2022-05-24 05:38:14', '210,4', '2022-05-24 05:38:14');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (47, 8, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 05:38:14', '210,4', '2022-05-24 05:38:14');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (48, 9, 4, 10, 'SUBMIT_TO_JOB', 'Default', 49, '210,4', '2022-05-24 05:38:17', '210,4', '2022-05-24 05:38:17');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (49, 9, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 05:38:17', '210,4', '2022-05-24 05:38:17');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (50, 10, 14, 10, 'SUBMIT_TO_JOB', 'Default', 51, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (51, 10, 14, 20, 'SUBMIT_TO_CLIENT', 'Default', 52, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (52, 10, 14, 30, 'INTERVIEW', 'Default', 53, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (53, 10, 14, 40, 'OFFER', 'Default', 54, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (54, 10, 14, 50, 'COMMISSION', 'Default', 55, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (55, 10, 14, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (56, 11, 14, 10, 'SUBMIT_TO_JOB', 'Default', 57, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (57, 11, 14, 20, 'SUBMIT_TO_CLIENT', 'Default', 58, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (58, 11, 14, 30, 'INTERVIEW', 'Default', 59, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (59, 11, 14, 40, 'OFFER', 'Default', 60, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (60, 11, 14, 50, 'COMMISSION', 'Default', 61, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (61, 11, 14, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (62, 12, 14, 10, 'SUBMIT_TO_JOB', 'Default', 63, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (63, 12, 14, 20, 'SUBMIT_TO_CLIENT', 'Default', 64, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (64, 12, 14, 30, 'INTERVIEW', 'Default', 65, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (65, 12, 14, 40, 'OFFER', 'Default', 66, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (66, 12, 14, 50, 'COMMISSION', 'Default', 67, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (67, 12, 14, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (68, 13, 14, 10, 'SUBMIT_TO_JOB', 'Default', 69, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (69, 13, 14, 20, 'SUBMIT_TO_CLIENT', 'Default', 70, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (70, 13, 14, 30, 'INTERVIEW', 'Default', 71, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (71, 13, 14, 40, 'OFFER', 'Default', 72, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (72, 13, 14, 50, 'COMMISSION', 'Default', 73, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (73, 13, 14, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (74, 14, 14, 10, 'SUBMIT_TO_JOB', 'Default', 75, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (75, 14, 14, 20, 'SUBMIT_TO_CLIENT', 'Default', 76, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (76, 14, 14, 30, 'INTERVIEW', 'Default', 77, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (77, 14, 14, 40, 'OFFER', 'Default', 78, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (78, 14, 14, 50, 'COMMISSION', 'Default', 79, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (79, 14, 14, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (80, 15, 14, 10, 'SUBMIT_TO_JOB', 'Default', 81, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (81, 15, 14, 20, 'SUBMIT_TO_CLIENT', 'Default', 82, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (82, 15, 14, 30, 'INTERVIEW', 'Default', 83, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (83, 15, 14, 40, 'OFFER', 'Default', 84, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (84, 15, 14, 50, 'COMMISSION', 'Default', 85, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (85, 15, 14, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:42:49', '210,4', '2022-05-24 06:42:49');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (86, 16, 4, 10, 'SUBMIT_TO_JOB', 'Default', 87, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (87, 16, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 88, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (88, 16, 4, 30, 'INTERVIEW', 'Default', 89, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (89, 16, 4, 40, 'OFFER', 'Default', 90, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (90, 16, 4, 50, 'COMMISSION', 'Default', 91, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (91, 16, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (92, 17, 4, 10, 'SUBMIT_TO_JOB', 'Default', 93, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (93, 17, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 94, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (94, 17, 4, 30, 'INTERVIEW', 'Default', 95, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (95, 17, 4, 40, 'OFFER', 'Default', 96, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (96, 17, 4, 50, 'COMMISSION', 'Default', 97, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (97, 17, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (98, 18, 4, 10, 'SUBMIT_TO_JOB', 'Default', 99, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (99, 18, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 100, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (100, 18, 4, 30, 'INTERVIEW', 'Default', 101, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (101, 18, 4, 40, 'OFFER', 'Default', 102, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (102, 18, 4, 50, 'COMMISSION', 'Default', 103, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (103, 18, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (104, 19, 4, 10, 'SUBMIT_TO_JOB', 'Default', 105, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (105, 19, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 106, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (106, 19, 4, 30, 'INTERVIEW', 'Default', 107, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (107, 19, 4, 40, 'OFFER', 'Default', 108, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (108, 19, 4, 50, 'COMMISSION', 'Default', 109, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (109, 19, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (110, 20, 4, 10, 'SUBMIT_TO_JOB', 'Default', 111, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (111, 20, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 112, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (112, 20, 4, 30, 'INTERVIEW', 'Default', 113, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (113, 20, 4, 40, 'OFFER', 'Default', 114, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (114, 20, 4, 50, 'COMMISSION', 'Default', 115, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (115, 20, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:19', '210,4', '2022-05-24 06:43:19');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (116, 21, 4, 10, 'SUBMIT_TO_JOB', 'Default', 117, '210,4', '2022-05-24 06:43:20', '210,4', '2022-05-24 06:43:20');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (117, 21, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 118, '210,4', '2022-05-24 06:43:20', '210,4', '2022-05-24 06:43:20');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (118, 21, 4, 30, 'INTERVIEW', 'Default', 119, '210,4', '2022-05-24 06:43:20', '210,4', '2022-05-24 06:43:20');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (119, 21, 4, 40, 'OFFER', 'Default', 120, '210,4', '2022-05-24 06:43:20', '210,4', '2022-05-24 06:43:20');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (120, 21, 4, 50, 'COMMISSION', 'Default', 121, '210,4', '2022-05-24 06:43:20', '210,4', '2022-05-24 06:43:20');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (121, 21, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:20', '210,4', '2022-05-24 06:43:20');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (122, 22, 2, 10, 'SUBMIT_TO_JOB', 'Default', 123, '210,4', '2022-05-24 06:43:35', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (123, 22, 2, 20, 'SUBMIT_TO_CLIENT', 'Default', 124, '210,4', '2022-05-24 06:43:35', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (124, 22, 2, 30, 'INTERVIEW', 'Default', 125, '210,4', '2022-05-24 06:43:35', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (125, 22, 2, 40, 'OFFER', 'Default', 126, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (126, 22, 2, 50, 'COMMISSION', 'Default', 127, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (127, 22, 2, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (128, 23, 2, 10, 'SUBMIT_TO_JOB', 'Default', 129, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (129, 23, 2, 20, 'SUBMIT_TO_CLIENT', 'Default', 130, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (130, 23, 2, 30, 'INTERVIEW', 'Default', 131, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (131, 23, 2, 40, 'OFFER', 'Default', 132, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (132, 23, 2, 50, 'COMMISSION', 'Default', 133, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (133, 23, 2, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (134, 24, 2, 10, 'SUBMIT_TO_JOB', 'Default', 135, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (135, 24, 2, 20, 'SUBMIT_TO_CLIENT', 'Default', 136, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (136, 24, 2, 30, 'INTERVIEW', 'Default', 137, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (137, 24, 2, 40, 'OFFER', 'Default', 138, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (138, 24, 2, 50, 'COMMISSION', 'Default', 139, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (139, 24, 2, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (140, 25, 2, 10, 'SUBMIT_TO_JOB', 'Default', 141, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (141, 25, 2, 20, 'SUBMIT_TO_CLIENT', 'Default', 142, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (142, 25, 2, 30, 'INTERVIEW', 'Default', 143, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (143, 25, 2, 40, 'OFFER', 'Default', 144, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (144, 25, 2, 50, 'COMMISSION', 'Default', 145, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (145, 25, 2, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (146, 26, 2, 10, 'SUBMIT_TO_JOB', 'Default', 147, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (147, 26, 2, 20, 'SUBMIT_TO_CLIENT', 'Default', 148, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (148, 26, 2, 30, 'INTERVIEW', 'Default', 149, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (149, 26, 2, 40, 'OFFER', 'Default', 150, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (150, 26, 2, 50, 'COMMISSION', 'Default', 151, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (151, 26, 2, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (152, 27, 2, 10, 'SUBMIT_TO_JOB', 'Default', 153, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (153, 27, 2, 20, 'SUBMIT_TO_CLIENT', 'Default', 154, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (154, 27, 2, 30, 'INTERVIEW', 'Default', 155, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (155, 27, 2, 40, 'OFFER', 'Default', 156, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (156, 27, 2, 50, 'COMMISSION', 'Default', 157, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (157, 27, 2, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-24 06:43:36', '210,4', '2022-05-24 06:43:36');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (158, 28, 4, 10, 'SUBMIT_TO_JOB', 'Default', 159, '210,4', '2022-05-25 03:24:45', '210,4', '2022-05-25 03:24:45');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (159, 28, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 160, '210,4', '2022-05-25 03:24:45', '210,4', '2022-05-25 03:24:45');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (160, 28, 4, 30, 'INTERVIEW', 'Default', 161, '210,4', '2022-05-25 03:24:45', '210,4', '2022-05-25 03:24:45');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (161, 28, 4, 40, 'OFFER', 'Default', 162, '210,4', '2022-05-25 03:24:45', '210,4', '2022-05-25 03:24:45');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (162, 28, 4, 41, 'OFFER_ACCEPT', 'Default', 163, '210,4', '2022-05-25 03:24:45', '210,4', '2022-05-25 03:24:45');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (163, 28, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-25 03:24:45', '210,4', '2022-05-25 03:24:45');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (164, 29, 4, 10, 'SUBMIT_TO_JOB', 'Default', 165, '210,4', '2022-05-25 03:24:50', '210,4', '2022-05-25 03:24:50');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (165, 29, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 166, '210,4', '2022-05-25 03:24:50', '210,4', '2022-05-25 03:24:50');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (166, 29, 4, 30, 'INTERVIEW', 'Default', 167, '210,4', '2022-05-25 03:24:50', '210,4', '2022-05-25 03:24:50');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (167, 29, 4, 40, 'OFFER', 'Default', 168, '210,4', '2022-05-25 03:24:50', '210,4', '2022-05-25 03:24:50');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (168, 29, 4, 41, 'OFFER_ACCEPT', 'Default', 169, '210,4', '2022-05-25 03:24:50', '210,4', '2022-05-25 03:24:50');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (169, 29, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-25 03:24:50', '210,4', '2022-05-25 03:24:50');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (170, 30, 4, 10, 'SUBMIT_TO_JOB', 'Default', 171, '210,4', '2022-05-25 03:24:54', '210,4', '2022-05-25 03:24:54');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (171, 30, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-25 03:24:54', '210,4', '2022-05-25 03:24:54');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (172, 31, 4, 10, 'SUBMIT_TO_JOB', 'Default', 173, '210,4', '2022-05-28 16:44:27', '210,4', '2022-05-28 16:44:27');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (173, 31, 4, 20, 'SUBMIT_TO_CLIENT', 'Default', 174, '210,4', '2022-05-28 16:44:27', '210,4', '2022-05-28 16:44:27');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (174, 31, 4, 30, 'INTERVIEW', 'Default', 175, '210,4', '2022-05-28 16:44:27', '210,4', '2022-05-28 16:44:27');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (175, 31, 4, 40, 'OFFER', 'Default', 176, '210,4', '2022-05-28 16:44:27', '210,4', '2022-05-28 16:44:27');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (176, 31, 4, 41, 'OFFER_ACCEPT', 'Default', 177, '210,4', '2022-05-28 16:44:27', '210,4', '2022-05-28 16:44:27');
INSERT INTO recruitment_process_node (id, recruitment_process_id, tenant_id, node_type, name, description, next_node_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (177, 31, 4, 60, 'ON_BOARD', 'Default', null, '210,4', '2022-05-28 16:44:27', '210,4', '2022-05-28 16:44:27');


-- 初始化recruitment_process_node_page_section（前端页面UI显示流程配置表）并注入测试数据

create table recruitment_process_node_page_section
(
    id                 bigint auto_increment primary key,
    tenant_id          bigint                                    not null,
    node_type          int                                       not null,
    job_type           int                                       null,
    node_page_section  int                                       not null,
    description        varchar(255)                              null,
    created_by         varchar(50)                               not null,
    created_date       timestamp default CURRENT_timestamp not null,
    last_modified_by   varchar(50)                               null,
    last_modified_date timestamp                              null
);

create index idx_recruitment_process_node_page_section_tid
    on recruitment_process_node_page_section (tenant_id);

INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (96, 4, 20, 1, 2, '默认部分：推荐时间', '210,4', '2022-05-24 02:53:10', '210,4', '2022-05-24 02:53:10');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (97, 4, 20, 1, 411, '备注(必填)', '210,4', '2022-05-24 02:53:10', '210,4', '2022-05-24 02:53:10');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (98, 4, 20, 1, 430, '定制化部分：约定薪资', '210,4', '2022-05-24 02:53:10', '210,4', '2022-05-24 02:53:10');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (99, 4, 20, 1, 410, '定制化部分：KIP用户角色部分', '210,4', '2022-05-24 02:53:10', '210,4', '2022-05-24 02:53:10');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (100, 4, 30, 1, 3, '默认部分：面试时间，面试进展，面试方式', '210,4', '2022-05-24 02:53:12', '210,4', '2022-05-24 02:53:12');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (101, 4, 30, 1, 411, '备注(必填)', '210,4', '2022-05-24 02:53:12', '210,4', '2022-05-24 02:53:12');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (102, 4, 30, 1, 430, '定制化部分：约定薪资', '210,4', '2022-05-24 02:53:12', '210,4', '2022-05-24 02:53:12');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (103, 4, 30, 1, 410, '定制化部分：KIP用户角色部分', '210,4', '2022-05-24 02:53:12', '210,4', '2022-05-24 02:53:12');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (104, 4, 30, 1, 109, '邮件通知客户联系人', '210,4', '2022-05-24 02:53:12', '210,4', '2022-05-24 02:53:12');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (105, 4, 30, 1, 110, '邮件通知候选人', '210,4', '2022-05-24 02:53:12', '210,4', '2022-05-24 02:53:12');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (106, 4, 30, 1, 111, '添加至日历提醒', '210,4', '2022-05-24 02:53:12', '210,4', '2022-05-24 02:53:12');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (107, 4, 40, 1, 4, '默认部分：签订日期，预计入职日期', '210,4', '2022-05-24 02:53:21', '210,4', '2022-05-24 02:53:21');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (108, 4, 40, 1, 411, '备注(必填)', '210,4', '2022-05-24 02:53:21', '210,4', '2022-05-24 02:53:21');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (109, 4, 40, 1, 430, '定制化部分：约定薪资', '210,4', '2022-05-24 02:53:21', '210,4', '2022-05-24 02:53:21');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (110, 4, 40, 1, 410, '定制化部分：KIP用户角色部分', '210,4', '2022-05-24 02:53:21', '210,4', '2022-05-24 02:53:21');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (111, 4, 41, 1, 107, '基本信息：公司名称，职位名称，候选人名称', '210,4', '2022-05-24 02:53:25', '210,4', '2022-05-24 02:53:25');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (112, 4, 41, 1, 412, 'IPG 定制化部分：offer基本信息部分', '210,4', '2022-05-24 02:53:25', '210,4', '2022-05-24 02:53:25');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (113, 4, 41, 1, 411, 'IPG 定制化部分：备注(必填)', '210,4', '2022-05-24 02:53:25', '210,4', '2022-05-24 02:53:25');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (114, 4, 41, 1, 431, 'IPG 定制化部分：Contract收费信息部分', '210,4', '2022-05-24 02:53:25', '210,4', '2022-05-24 02:53:25');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (115, 4, 41, 1, 410, 'IPG 定制化部分：KIP用户角色部分', '210,4', '2022-05-24 02:53:25', '210,4', '2022-05-24 02:53:25');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (116, 4, 60, 1, 107, '基本信息：公司名称，职位名称，候选人名称', '210,4', '2022-05-24 02:53:28', '210,4', '2022-05-24 02:53:28');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (117, 4, 60, 1, 412, 'IPG 定制化部分：offer基本信息部分', '210,4', '2022-05-24 02:53:28', '210,4', '2022-05-24 02:53:28');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (118, 4, 60, 1, 411, '备注(必填)', '210,4', '2022-05-24 02:53:28', '210,4', '2022-05-24 02:53:28');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (119, 4, 60, 1, 431, 'IPG 定制化部分：Contract收费信息部分', '210,4', '2022-05-24 02:53:28', '210,4', '2022-05-24 02:53:28');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (120, 4, 60, 1, 410, 'IPG 定制化部分：KIP用户角色部分', '210,4', '2022-05-24 02:53:28', '210,4', '2022-05-24 02:53:28');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (134, 4, 20, 3, 2, '推荐时间', '210,4', '2022-05-24 02:53:58', '210,4', '2022-05-24 02:53:58');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (135, 4, 20, 3, 411, '备注(必填)', '210,4', '2022-05-24 02:53:58', '210,4', '2022-05-24 02:53:58');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (136, 4, 20, 3, 410, '定制化部分：KIP用户角色部分', '210,4', '2022-05-24 02:53:58', '210,4', '2022-05-24 02:53:58');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (137, 4, 30, 3, 3, '默认部分：面试时间，面试进展，面试方式', '210,4', '2022-05-24 02:54:00', '210,4', '2022-05-24 02:54:00');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (138, 4, 30, 3, 411, '备注(必填)', '210,4', '2022-05-24 02:54:00', '210,4', '2022-05-24 02:54:00');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (139, 4, 30, 3, 410, '定制化部分：KIP用户角色部分', '210,4', '2022-05-24 02:54:00', '210,4', '2022-05-24 02:54:00');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (140, 4, 30, 3, 109, '邮件通知客户联系人', '210,4', '2022-05-24 02:54:00', '210,4', '2022-05-24 02:54:00');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (141, 4, 30, 3, 110, '邮件通知候选人', '210,4', '2022-05-24 02:54:00', '210,4', '2022-05-24 02:54:00');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (142, 4, 40, 3, 4, '默认部分：签订日期，预计入职日期', '210,4', '2022-05-24 02:54:04', '210,4', '2022-05-24 02:54:04');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (143, 4, 40, 3, 411, '备注(必填)', '210,4', '2022-05-24 02:54:04', '210,4', '2022-05-24 02:54:04');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (144, 4, 40, 3, 410, '定制化部分：KIP用户角色部分', '210,4', '2022-05-24 02:54:04', '210,4', '2022-05-24 02:54:04');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (145, 4, 41, 3, 107, '基本信息：公司名称，职位名称，候选人名称', '210,4', '2022-05-24 02:54:06', '210,4', '2022-05-24 02:54:06');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (146, 4, 41, 3, 412, 'IPG offer基本信息部分', '210,4', '2022-05-24 02:54:06', '210,4', '2022-05-24 02:54:06');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (147, 4, 41, 3, 420, 'IPG FTE薪资结构部分', '210,4', '2022-05-24 02:54:06', '210,4', '2022-05-24 02:54:06');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (148, 4, 41, 3, 421, 'IPG FTE收费信息部分', '210,4', '2022-05-24 02:54:06', '210,4', '2022-05-24 02:54:06');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (149, 4, 41, 3, 410, 'IPG KIP用户角色部分', '210,4', '2022-05-24 02:54:06', '210,4', '2022-05-24 02:54:06');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (150, 4, 41, 3, 411, '备注(必填)', '210,4', '2022-05-24 02:54:06', '210,4', '2022-05-24 02:54:06');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (151, 4, 41, 3, 413, 'IPG 生成收费明细', '210,4', '2022-05-24 02:54:06', '210,4', '2022-05-24 02:54:06');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (152, 4, 60, 3, 107, '基本信息：公司名称，职位名称，候选人名称', '210,4', '2022-05-24 02:54:09', '210,4', '2022-05-24 02:54:09');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (153, 4, 60, 3, 412, 'IPG offer基本信息部分', '210,4', '2022-05-24 02:54:09', '210,4', '2022-05-24 02:54:09');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (154, 4, 60, 3, 420, 'FTE薪资结构部分', '210,4', '2022-05-24 02:54:09', '210,4', '2022-05-24 02:54:09');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (155, 4, 60, 3, 421, 'FTE收费信息部分', '210,4', '2022-05-24 02:54:09', '210,4', '2022-05-24 02:54:09');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (156, 4, 60, 3, 410, 'KIP用户角色部分', '210,4', '2022-05-24 02:54:09', '210,4', '2022-05-24 02:54:09');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (157, 4, 60, 3, 411, '备注(必填)', '210,4', '2022-05-24 02:54:09', '210,4', '2022-05-24 02:54:09');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (159, -1, 10, null, 1, '默认部分：选择职位', '210,4', '2022-05-24 02:54:12', '210,4', '2022-05-24 02:54:12');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (160, -1, 10, null, 106, '备注(选填)', '210,4', '2022-05-24 02:54:12', '210,4', '2022-05-24 02:54:12');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (161, -1, 20, null, 2, '默认部分：推荐时间', '210,4', '2022-05-24 02:54:16', '210,4', '2022-05-24 02:54:16');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (162, -1, 20, null, 106, '备注(选填)', '210,4', '2022-05-24 02:54:16', '210,4', '2022-05-24 02:54:16');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (163, -1, 20, null, 100, '默认部分：KIP用户角色部分', '210,4', '2022-05-24 02:54:16', '210,4', '2022-05-24 02:54:16');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (164, -1, 30, null, 3, '默认部分：面试时间，面试进展，面试方式', '210,4', '2022-05-24 02:54:19', '210,4', '2022-05-24 02:54:19');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (165, -1, 30, null, 100, '默认部分：参与者KIP用户角色部分', '210,4', '2022-05-24 02:54:19', '210,4', '2022-05-24 02:54:19');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (166, -1, 30, null, 106, '备注(选填)', '210,4', '2022-05-24 02:54:19', '210,4', '2022-05-24 02:54:19');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (167, -1, 30, null, 109, '邮件通知客户联系人', '210,4', '2022-05-24 02:54:19', '210,4', '2022-05-24 02:54:19');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (168, -1, 30, null, 110, '邮件通知候选人', '210,4', '2022-05-24 02:54:19', '210,4', '2022-05-24 02:54:19');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (169, -1, 40, null, 4, '默认部分：签订日期，预计入职日期', '210,4', '2022-05-24 02:54:22', '210,4', '2022-05-24 02:54:22');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (170, -1, 40, null, 106, '备注(选填)', '210,4', '2022-05-24 02:54:22', '210,4', '2022-05-24 02:54:22');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (171, -1, 40, null, 100, '默认部分：KIP用户角色部分', '210,4', '2022-05-24 02:54:22', '210,4', '2022-05-24 02:54:22');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (172, -1, 40, null, 102, '默认部分：薪资基本信息部分-币种和类型', '210,4', '2022-05-24 02:54:22', '210,4', '2022-05-24 02:54:22');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (173, -1, 40, null, 103, '默认部分：薪资结构部分', '210,4', '2022-05-24 02:54:22', '210,4', '2022-05-24 02:54:22');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (174, -1, 40, null, 104, '默认部分：收费部分', '210,4', '2022-05-24 02:54:22', '210,4', '2022-05-24 02:54:22');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (175, -1, 40, null, 112, '默认部分：生成收费明细', '210,4', '2022-05-24 02:54:22', '210,4', '2022-05-24 02:54:22');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (176, -1, 50, null, 107, '基本信息：公司名称，职位名称，候选人名称', '210,4', '2022-05-24 02:54:24', '210,4', '2022-05-24 02:54:24');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (177, -1, 50, null, 104, '默认部分：收费信息部分', '210,4', '2022-05-24 02:54:24', '210,4', '2022-05-24 02:54:24');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (178, -1, 50, null, 105, '业绩分配部分', '210,4', '2022-05-24 02:54:24', '210,4', '2022-05-24 02:54:24');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (179, -1, 50, null, 106, '备注(选填)', '210,4', '2022-05-24 02:54:24', '210,4', '2022-05-24 02:54:24');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (180, -1, 60, null, 7, '入职时间，试用期结束', '210,4', '2022-05-24 02:54:27', '210,4', '2022-05-24 02:54:27');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (190, 4, 10, 5, 1, '默认部分：选择职位', '210,4', '2022-05-25 05:29:05', '210,4', '2022-05-25 05:29:05');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (191, 4, 10, 5, 106, '备注(选填)', '210,4', '2022-05-25 05:29:05', '210,4', '2022-05-25 05:29:05');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (192, 4, 10, 5, 415, 'IPG-Payroll定制化部分：KIP用户角色部分', '210,4', '2022-05-25 05:29:05', '210,4', '2022-05-25 05:29:05');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (193, 4, 60, 5, 107, '基本信息：公司名称，职位名称，候选人名称', '210,4', '2022-05-25 05:29:11', '210,4', '2022-05-25 05:29:11');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (194, 4, 60, 5, 412, 'IPG 定制化部分：offer基本信息部分', '210,4', '2022-05-25 05:29:11', '210,4', '2022-05-25 05:29:11');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (195, 4, 60, 5, 106, '备注(选填)', '210,4', '2022-05-25 05:29:11', '210,4', '2022-05-25 05:29:11');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (196, 4, 60, 5, 431, 'IPG 定制化部分：Contract收费信息部分', '210,4', '2022-05-25 05:29:11', '210,4', '2022-05-25 05:29:11');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (197, 4, 60, 3, 416, '发票信息', '210,4', '2022-08-10 18:44:09', '210,4', '2022-08-10 18:44:09');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (216, 4, 10, 1, 1, '默认部分：选择职位', '210,4', '2022-09-19 19:40:01.876', '210,4', '2022-09-19 19:40:01.876');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (217, 4, 10, 1, 417, '突出技能', '210,4', '2022-09-19 19:40:01.883', '210,4', '2022-09-19 19:40:01.883');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (218, 4, 10, 1, 418, '推荐建议', '210,4', '2022-09-19 19:40:01.887', '210,4', '2022-09-19 19:40:01.887');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (219, 4, 10, 1, 430, '约定薪资', '210,4', '2022-09-19 19:40:01.892', '210,4', '2022-09-19 19:40:01.892');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (220, 4, 10, 1, 410, 'KIP用户角色', '210,4', '2022-09-19 19:40:01.896', '210,4', '2022-09-19 19:40:01.896');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (221, 4, 10, 3, 1, '默认部分：选择职位', '210,4', '2022-09-19 19:40:48.442', '210,4', '2022-09-19 19:40:48.442');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (222, 4, 10, 3, 417, '突出技能', '210,4', '2022-09-19 19:40:48.447', '210,4', '2022-09-19 19:40:48.447');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (223, 4, 10, 3, 418, '推荐建议', '210,4', '2022-09-19 19:40:48.451', '210,4', '2022-09-19 19:40:48.451');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (224, 4, 10, 3, 430, '约定薪资', '210,4', '2022-09-19 19:40:48.455', '210,4', '2022-09-19 19:40:48.455');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (225, 4, 10, 3, 410, 'KIP用户角色', '210,4', '2022-09-19 19:40:48.459', '210,4', '2022-09-19 19:40:48.459');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (226, 4, 20, 3, 430, '约定薪资', '381,4', '2022-11-12 16:19:43.455', '381,4', '2022-11-12 16:19:43.455');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (227, 4, 30, 3, 430, '约定薪资', '381,4', '2022-11-12 16:19:43.455', '381,4', '2022-11-12 16:19:43.455');
INSERT INTO recruitment_process_node_page_section (id, tenant_id, node_type, job_type, node_page_section, description, created_by, created_date, last_modified_by, last_modified_date) VALUES (228, 4, 40, 3, 430, '约定薪资', '381,4', '2022-11-12 16:19:43.455', '381,4', '2022-11-12 16:19:43.455');


-- talent_recruitment_process_ipg_offer_letter_cost_rate(ipg contract pre-configed cost rate table)

INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (16, 1, 0, 0, '1_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (17, 1, 1, 0, '1_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (18, 1, 2, 0, '1_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (19, 1, 3, 0, '1_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (20, 1, 4, 0, '1_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (21, 1, 0, 1, '1_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (22, 1, 1, 1, '1_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (23, 1, 2, 1, '1_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (24, 1, 3, 1, '1_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (25, 1, 4, 1, '1_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (26, 1, 0, 2, '1_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (27, 1, 1, 2, '1_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (28, 1, 2, 2, '1_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (29, 1, 3, 2, '1_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (30, 1, 4, 2, '1_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (31, 2, 0, 0, '2_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (32, 2, 1, 0, '2_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (33, 2, 2, 0, '2_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (34, 2, 3, 0, '2_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (35, 2, 4, 0, '2_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (36, 2, 0, 1, '2_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (37, 2, 1, 1, '2_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (38, 2, 2, 1, '2_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (39, 2, 3, 1, '2_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (40, 2, 4, 1, '2_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (41, 2, 0, 2, '2_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (42, 2, 1, 2, '2_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (43, 2, 2, 2, '2_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (44, 2, 3, 2, '2_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (45, 2, 4, 2, '2_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (46, 3, 0, 0, '3_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (47, 3, 1, 0, '3_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (48, 3, 2, 0, '3_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (49, 3, 3, 0, '3_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (50, 3, 4, 0, '3_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (51, 3, 0, 1, '3_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (52, 3, 1, 1, '3_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (53, 3, 2, 1, '3_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (54, 3, 3, 1, '3_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (55, 3, 4, 1, '3_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (56, 3, 0, 2, '3_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (57, 3, 1, 2, '3_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (58, 3, 2, 2, '3_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (59, 3, 3, 2, '3_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (60, 3, 4, 2, '3_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (61, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (62, 4, 1, 0, '4_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (63, 4, 2, 0, '4_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (64, 4, 3, 0, '4_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (65, 4, 4, 0, '4_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (66, 4, 0, 1, '4_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (67, 4, 1, 1, '4_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (68, 4, 2, 1, '4_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (69, 4, 3, 1, '4_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (70, 4, 4, 1, '4_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (71, 4, 0, 2, '4_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (72, 4, 1, 2, '4_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (73, 4, 2, 2, '4_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (74, 4, 3, 2, '4_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (75, 4, 4, 2, '4_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (76, 5, 0, 0, '5_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (77, 5, 1, 0, '5_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (78, 5, 2, 0, '5_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (79, 5, 3, 0, '5_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (80, 5, 4, 0, '5_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (81, 5, 0, 1, '5_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (82, 5, 1, 1, '5_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (83, 5, 2, 1, '5_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (84, 5, 3, 1, '5_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (85, 5, 4, 1, '5_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (86, 5, 0, 2, '5_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (87, 5, 1, 2, '5_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (88, 5, 2, 2, '5_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (89, 5, 3, 2, '5_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (90, 5, 4, 2, '5_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (91, 6, 0, 0, '6_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (92, 6, 1, 0, '6_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (93, 6, 2, 0, '6_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (94, 6, 3, 0, '6_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (95, 6, 4, 0, '6_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (96, 6, 0, 1, '6_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (97, 6, 1, 1, '6_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (98, 6, 2, 1, '6_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (99, 6, 3, 1, '6_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (100, 6, 4, 1, '6_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (101, 6, 0, 2, '6_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (102, 6, 1, 2, '6_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (103, 6, 2, 2, '6_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (104, 6, 3, 2, '6_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (105, 6, 4, 2, '6_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (106, 7, 0, 0, '7_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (107, 7, 1, 0, '7_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (108, 7, 2, 0, '7_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (109, 7, 3, 0, '7_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (110, 7, 4, 0, '7_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (111, 7, 0, 1, '7_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (112, 7, 1, 1, '7_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (113, 7, 2, 1, '7_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (114, 7, 3, 1, '7_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (115, 7, 4, 1, '7_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (116, 7, 0, 2, '7_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (117, 7, 1, 2, '7_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (118, 7, 2, 2, '7_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (119, 7, 3, 2, '7_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (120, 7, 4, 2, '7_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (121, 8, 0, 0, '8_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (122, 8, 1, 0, '8_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (123, 8, 2, 0, '8_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (124, 8, 3, 0, '8_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (125, 8, 4, 0, '8_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (126, 8, 0, 1, '8_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (127, 8, 1, 1, '8_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (128, 8, 2, 1, '8_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (129, 8, 3, 1, '8_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (130, 8, 4, 1, '8_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (131, 8, 0, 2, '8_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (132, 8, 1, 2, '8_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (133, 8, 2, 2, '8_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (134, 8, 3, 2, '8_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (135, 8, 4, 2, '8_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (136, 9, 0, 0, '9_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (137, 9, 1, 0, '9_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (138, 9, 2, 0, '9_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (139, 9, 3, 0, '9_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (140, 9, 4, 0, '9_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (141, 9, 0, 1, '9_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (142, 9, 1, 1, '9_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (143, 9, 2, 1, '9_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (144, 9, 3, 1, '9_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (145, 9, 4, 1, '9_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (146, 9, 0, 2, '9_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (147, 9, 1, 2, '9_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (148, 9, 2, 2, '9_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (149, 9, 3, 2, '9_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (150, 9, 4, 2, '9_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (151, 10, 0, 0, '10_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (152, 10, 1, 0, '10_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (153, 10, 2, 0, '10_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (154, 10, 3, 0, '10_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (155, 10, 4, 0, '10_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (156, 10, 0, 1, '10_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (157, 10, 1, 1, '10_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (158, 10, 2, 1, '10_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (159, 10, 3, 1, '10_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (160, 10, 4, 1, '10_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (161, 10, 0, 2, '10_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (162, 10, 1, 2, '10_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (163, 10, 2, 2, '10_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (164, 10, 3, 2, '10_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (165, 10, 4, 2, '10_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (166, 11, 0, 0, '11_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (167, 11, 1, 0, '11_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (168, 11, 2, 0, '11_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (169, 11, 3, 0, '11_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (170, 11, 4, 0, '11_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (171, 11, 0, 1, '11_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (172, 11, 1, 1, '11_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (173, 11, 2, 1, '11_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (174, 11, 3, 1, '11_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (175, 11, 4, 1, '11_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (176, 11, 0, 2, '11_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (177, 11, 1, 2, '11_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (178, 11, 2, 2, '11_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (179, 11, 3, 2, '11_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (180, 11, 4, 2, '11_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (181, 12, 0, 0, '12_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (182, 12, 1, 0, '12_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (183, 12, 2, 0, '12_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (184, 12, 3, 0, '12_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (185, 12, 4, 0, '12_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (186, 12, 0, 1, '12_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (187, 12, 1, 1, '12_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (188, 12, 2, 1, '12_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (189, 12, 3, 1, '12_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (190, 12, 4, 1, '12_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (191, 12, 0, 2, '12_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (192, 12, 1, 2, '12_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (193, 12, 2, 2, '12_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (194, 12, 3, 2, '12_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (195, 12, 4, 2, '12_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (196, 13, 0, 0, '13_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (197, 13, 1, 0, '13_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (198, 13, 2, 0, '13_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (199, 13, 3, 0, '13_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (200, 13, 4, 0, '13_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (201, 13, 0, 1, '13_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (202, 13, 1, 1, '13_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (203, 13, 2, 1, '13_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (204, 13, 3, 1, '13_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (205, 13, 4, 1, '13_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (206, 13, 0, 2, '13_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (207, 13, 1, 2, '13_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (208, 13, 2, 2, '13_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (209, 13, 3, 2, '13_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (210, 13, 4, 2, '13_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (211, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (212, 14, 1, 0, '14_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (213, 14, 2, 0, '14_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (214, 14, 3, 0, '14_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (215, 14, 4, 0, '14_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (216, 14, 0, 1, '14_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (217, 14, 1, 1, '14_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (218, 14, 2, 1, '14_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (219, 14, 3, 1, '14_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (220, 14, 4, 1, '14_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (221, 14, 0, 2, '14_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (222, 14, 1, 2, '14_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (223, 14, 2, 2, '14_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (224, 14, 3, 2, '14_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (225, 14, 4, 2, '14_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (226, 33, 0, 0, '33_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (227, 33, 1, 0, '33_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (228, 33, 2, 0, '33_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (229, 33, 3, 0, '33_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (230, 33, 4, 0, '33_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (231, 33, 0, 1, '33_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (232, 33, 1, 1, '33_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (233, 33, 2, 1, '33_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (234, 33, 3, 1, '33_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (235, 33, 4, 1, '33_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (236, 33, 0, 2, '33_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (237, 33, 1, 2, '33_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (238, 33, 2, 2, '33_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (239, 33, 3, 2, '33_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (240, 33, 4, 2, '33_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (241, 34, 0, 0, '34_USD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (242, 34, 1, 0, '34_CNY_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (243, 34, 2, 0, '34_EUR_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (244, 34, 3, 0, '34_CAD_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (245, 34, 4, 0, '34_GBP_TAX_BURDEN_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (246, 34, 0, 1, '34_USD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (247, 34, 1, 1, '34_CNY_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (248, 34, 2, 1, '34_EUR_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (249, 34, 3, 1, '34_CAD_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (250, 34, 4, 1, '34_GBP_MSP_RATE_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (251, 34, 0, 2, '34_USD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (252, 34, 1, 2, '34_CNY_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (253, 34, 2, 2, '34_EUR_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (254, 34, 3, 2, '34_CAD_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (255, 34, 4, 2, '34_GBP_IMMIGRATION_COST_ZERO_0', '0', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (256, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_DIDI_DRIVER_26', 'Didi Driver: 26%', 26.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (257, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_GOOGLE_1866', 'Google: 18.66%', 18.6600, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (258, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_JD_1_68', 'JD_1: 6.8%', 6.8000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (259, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_JD_2_1448', 'JD_2: 14.48%', 14.4800, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (260, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_C2C_1099_0', 'C2C(1099): 0%', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (261, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_OTHERS_15', 'Others: 15%', 15.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (262, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_CANADIAN_20', 'Canadian: 20%', 20.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (263, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_US_BURDEN_18', 'US Burden Rate: 18% (Pay Rate>$40/hr)', 18.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (264, 4, 0, 1, '4_USD_MSP_RATE_LINKEDIN_2', 'LinkedIn: 2%', 2.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (265, 4, 0, 1, '4_USD_MSP_RATE_ADOBE_225', 'Adobe: 2.25%', 2.2500, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (266, 4, 0, 1, '4_USD_MSP_RATE_GOOGLE_165', 'Google: 1.65%', 1.6500, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (267, 4, 0, 1, '4_USD_MSP_RATE_PINTEREST_3', 'Pinterest: 3%', 3.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (268, 4, 0, 1, '4_USD_MSP_RATE_EQUINIX_2', 'Equinix: 2%', 2.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (269, 4, 0, 2, '4_USD_IMMIGRATION_COST_H1B_INITIAL_FILLING_APPLICATION_6000', 'H1B Initial Filling(Application): $6000', 6000.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (270, 4, 0, 2, '4_USD_IMMIGRATION_COST_H1B_TRANSFER_6000', 'H1B Transfer: $6000', 6000.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (271, 4, 0, 2, '4_USD_IMMIGRATION_COST_GREENCARD_PERM_ONLY_APPLICATION_5500', 'Greencard(PERM Only) Application: $5500', 5500.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (272, 4, 0, 2, '4_USD_IMMIGRATION_COST_H1B_AMENDMENT_3300', 'H1B Amendment: $3300', 3300.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (273, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_DIDI_DRIVER_26', 'Didi Driver: 26%', 26.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (274, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_GOOGLE_1866', 'Google: 18.66%', 18.6600, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (275, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_JD_1_68', 'JD_1: 6.8%', 6.8000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (276, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_JD_2_1448', 'JD_2: 14.48%', 14.4800, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (277, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_C2C_1099_0', 'C2C(1099): 0%', 0.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (278, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_OTHERS_15', 'Others: 15%', 15.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (279, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_CANADIAN_20', 'Canadian: 20%', 20.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (280, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_US_BURDEN_18', 'US Burden: 18%', 18.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (281, 14, 0, 1, '14_USD_MSP_RATE_LINKEDIN_2', 'LinkedIn: 2%', 2.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (282, 14, 0, 1, '14_USD_MSP_RATE_ADOBE_225', 'Adobe: 2.25%', 2.2500, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (283, 14, 0, 1, '14_USD_MSP_RATE_GOOGLE_165', 'Google: 1.65%', 1.6500, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (284, 14, 0, 1, '14_USD_MSP_RATE_PINTEREST_3', 'Pinterest: 3%', 3.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (285, 14, 0, 1, '14_USD_MSP_RATE_EQUINIX_2', 'Equinix: 2%', 2.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (286, 14, 0, 2, '14_USD_IMMIGRATION_COST_H1B_INITIAL_FILLING_APPLICATION_6000', 'H1B Initial Filling(Application): $6000', 6000.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (287, 14, 0, 2, '14_USD_IMMIGRATION_COST_H1B_TRANSFER_6000', 'H1B Transfer: $6000', 6000.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (288, 14, 0, 2, '14_USD_IMMIGRATION_COST_GREENCARD_PERM_ONLY_APPLICATION_5500', 'Greencard(PERM Only) Application: $5500', 5500.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (289, 14, 0, 2, '14_USD_IMMIGRATION_COST_H1B_AMENDMENT_3300', 'H1B Amendment: $3300', 3300.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (290, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_US_BURDEN_25', 'US Burden Rate: 25% (Pay Rate≤$25/hr)', 25.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (291, 4, 0, 0, '4_USD_TAX_BURDEN_RATE_US_BURDEN_21', 'US Burden Rate: 21% ($25/hr<Pay Rate≤$40/hr)', 21.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (292, 4, 3, 0, '4_CAD_TAX_BURDEN_RATE_CANADIAN_20', 'Canadian tax burden rate: 20%', 20.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (293, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_US_BURDEN_25', 'US Burden Rate: 25% (Pay Rate≤$25/hr)', 25.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (294, 14, 0, 0, '14_USD_TAX_BURDEN_RATE_US_BURDEN_21', 'US Burden Rate: 21% ($25/hr<Pay Rate≤$40/hr)', 21.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (295, 14, 3, 0, '14_CAD_TAX_BURDEN_RATE_CANADIAN_20', 'Canadian tax burden rate: 20%', 20.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (296, 129, 0, 0, '129_USD_TAX_BURDEN_RATE_US_BURDEN_25', 'US Burden Rate: 25% (Pay Rate≤$25/hr)', 25.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (297, 129, 0, 0, '129_USD_TAX_BURDEN_RATE_US_BURDEN_21', 'US Burden Rate: 21% ($25/hr<Pay Rate≤$40/hr)', 21.0000, '9999-12-31');
INSERT INTO talent_recruitment_process_ipg_offer_letter_cost_rate (id, tenant_id, currency, rate_type, code, description, value, expire_date) VALUES (298, 129, 3, 0, '129_CAD_TAX_BURDEN_RATE_CANADIAN_20', 'Canadian tax burden rate: 20%', 20.0000, '9999-12-31');



-- 前期准备：
alter table start change application_id talent_recruitment_process_id bigint not null;

SET GLOBAL sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''));

SET sql_mode=(SELECT REPLACE(@@sql_mode, 'ONLY_FULL_GROUP_BY', ''));

set global sql_mode='';

set session sql_mode='';

SET GLOBAL group_concat_max_len=150000;
SET session group_concat_max_len=150000;

/* ============application=========== */
-- 先决条件： 配置好对应的流程（recruitment_process和recruitment_process_node表），将下方流程ID 28，29，30 修改为配置好的产品库（recruitment_process）ID，对应于ipg这个tenant特有的流程（contract/payroll/FTE）

-- For contract
insert into talent_recruitment_process(id, recruitment_process_id, tenant_id, talent_id, job_id, note, created_by, created_date, last_modified_by, last_modified_date)
select a.id, 29, a.tenant_id, a.talent_id, a.job_id, a.memo, a.created_by, a.created_date, a.last_modified_by, a.last_modified_date
from application a left join job j on a.job_id = j.id
where j.job_type=1;
-- For payroll
insert into talent_recruitment_process(id, recruitment_process_id, tenant_id, talent_id, job_id, note, created_by, created_date, last_modified_by, last_modified_date)
select a.id, 30, a.tenant_id, a.talent_id, a.job_id, a.memo, a.created_by, a.created_date, a.last_modified_by, a.last_modified_date
from application a left join job j on a.job_id = j.id
where j.job_type=5;
-- All others
insert into talent_recruitment_process(id, recruitment_process_id, tenant_id, talent_id, job_id, note, created_by, created_date, last_modified_by, last_modified_date)
select a.id, 28, a.tenant_id, a.talent_id, a.job_id, a.memo, a.created_by, a.created_date, a.last_modified_by, a.last_modified_date
from application a left join job j on a.job_id = j.id
where j.job_type not in(1,5);

insert into talent_recruitment_process_node(talent_recruitment_process_id, node_id, node_type, node_status, next_node_id,
                                            created_by, created_date, last_modified_by, last_modified_date)
select trp.id, rpn.id, rpn.node_type, 3, rpn.next_node_id,
       trp.created_by, trp.created_date, trp.last_modified_by, trp.last_modified_date
from talent_recruitment_process trp
         left join recruitment_process_node rpn on trp.recruitment_process_id = rpn.recruitment_process_id;




/* ===========submit_to_job============ */
-- for most applications (except for ipg payrolling application)
insert into talent_recruitment_process_submit_to_job(talent_recruitment_process_id, note, skills, recommend_comments,
                                                     created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id,
       null,
       ap.highlighted_experience,
       GROUP_CONCAT(concat(a.memo, ' ---- ', concat(concat(u.first_name, ' ', u.last_name), ' ', a.created_date))
           SEPARATOR '.\r\n\n') as 'recommend_comments',
        ac.created_by,
       ac.created_date,
       ac.last_modified_by,
       ac.last_modified_date
from activity_unique ac
         left join application ap on ap.id = ac.application_id
         left join activity a on a.application_id = ac.application_id and a.status = ac.status
         left join user u on a.user_id = u.id
where ac.status in (1, 2, 10, 11)
group by ac.application_id;

-- for ipg payrolling application, convert 'offer accepted' (activityStatus = 6) to 'submit to job' (activityStatus = 3)
insert into talent_recruitment_process_submit_to_job(talent_recruitment_process_id, note, skills, recommend_comments,
                                                     created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id,
       null,
       ap.highlighted_experience,
       GROUP_CONCAT(concat(a.memo, ' ---- ', concat(concat(u.first_name, ' ', u.last_name), ' ', a.created_date))
           SEPARATOR '.\r\n\n') as 'recommend_comments',
        ac.created_by,
       ac.created_date,
       ac.last_modified_by,
       ac.last_modified_date
from activity_unique ac
         left join application ap on ap.id = ac.application_id
         left join activity a on a.application_id = ac.application_id and a.status = ac.status
         left join user u on a.user_id = u.id
         left join job j on ac.job_id = j.id
where ac.status = 6 and j.job_type = 5
group by ac.application_id;

-- active most applications
update talent_recruitment_process_node t, application a
set t.node_status=1, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
where t.node_type=10 and a.status in(1,2) and t.talent_recruitment_process_id = a.id;

-- active pay-rolling
update talent_recruitment_process_node t right join (SELECT ap.id, ac.last_modified_date, ac.last_modified_by
    from activity_unique ac
    left join application ap on ap.id = ac.application_id
    left join job j on ac.job_id = j.id
    where ac.status = 6 and j.job_type = 5 and ap.status in (6,7)
    group by ac.application_id) temp on t.talent_recruitment_process_id = temp.id
    set t.node_status=1, t.last_modified_date=temp.last_modified_date, t.last_modified_by=temp.last_modified_by
where t.node_type=10;
-- complete
/*
update talent_recruitment_process_node t, application a
set t.node_status=2, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
 where t.node_type=10 and a.status > 2 and a.status < 13 and t.talent_recruitment_process_id = a.id;
*/

/* ===========submit_to_client============ */
insert into talent_recruitment_process_submit_to_client(talent_recruitment_process_id, submit_time, note, email_tracking_number,
                                                        created_by, created_date, last_modified_by, last_modified_date)
SELECT application_id, a.created_date, GROUP_CONCAT(concat(memo, ' ---- ', concat(concat(u.first_name, ' ', u.last_name), ' ', a.created_date)) SEPARATOR '.\r\n\n') as 'note',
        null, a.created_by, a.created_date, a.last_modified_by, a.last_modified_date
from activity a left join user u on a.user_id = u.id where a.status in (3,12) group by application_id;

-- set created_by, created_date, last_modified_by, last_modified_date
update talent_recruitment_process_submit_to_client trpstc right join
    (select * from activity a where a.id in (SELECT max(a.id) as 'id' from activity a where a.status = 3 group by application_id)) temp on temp.application_id = trpstc.talent_recruitment_process_id
    set trpstc.submit_time = temp.created_date, trpstc.created_by = temp.created_by, trpstc.created_date = temp.created_date, trpstc.last_modified_by = temp.last_modified_by, trpstc.last_modified_date = temp.last_modified_date;

-- active
update talent_recruitment_process_node t, application a
set t.node_status=1, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
where t.node_type=20 and a.status = 3 and t.talent_recruitment_process_id = a.id;
-- complete
/*
update talent_recruitment_process_node t, application a
set t.node_status=2, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
 where t.node_type=20 and a.status > 3 and a.status <  and t.talent_recruitment_process_id = a.id;
*/
/*
update talent_recruitment_process_node n1
    left join talent_recruitment_process_node n2
on n1.id < n2.id
    and n1.talent_recruitment_process_id = n2.talent_recruitment_process_id
    and n2.node_status = 1 and n2.node_type = 20
    set n1.node_status = 2 where n1.node_type < 20;
*/


/*
update talent_recruitment_process_node n1
    inner join (select * from talent_recruitment_process_node n2 where n2.node_status = 1 and n2.node_type = 20) temp
    on temp.talent_recruitment_process_id = n1.talent_recruitment_process_id and n1.id < temp.id
set n1.node_status = 2
where n1.node_type < 20 and n1.node_status = 3;
*/

/* ===========interview============ */
insert into talent_recruitment_process_interview(talent_recruitment_process_id, progress, from_time, to_time, interview_type, time_zone, note,
                                                 created_by, created_date, last_modified_by, last_modified_date)
select a.application_id, ifnull(event_stage, 1) as 'event_stage', ifnull(event_date, a.created_date) 'event_date', ifnull(event_date, a.created_date) 'event_date',
        case event_type when 401 then 0 when 402 then 1 when 403 then 2 else 3 end interview_type, event_time_zone,
    GROUP_CONCAT(concat(memo, ' ---- ', concat(concat(u.first_name, ' ', u.last_name), ' ', a.created_date)) SEPARATOR '.\r\n\n') as 'note',
        a.created_by, a.created_date, a.last_modified_by, a.last_modified_date
from activity a left join user u on a.user_id = u.id where a.status = 4 group by application_id, event_stage;

-- update interview info to latest one
/*
update talent_recruitment_process_interview interview right join
    (select * from activity a where a.id in (select max(a.id) from activity a where a.status = 4 group by application_id, event_stage)) temp on temp.application_id = interview.talent_recruitment_process_id
set interview.from_time = ifnull(temp.event_date, temp.created_date), interview.to_time = ifnull(temp.event_date, temp.created_date),
    interview.interview_type = case temp.event_type when 401 then 0 when 402 then 1 when 403 then 2 else 3 end,
    interview.time_zone = temp.event_time_zone,
    interview.last_modified_by = temp.last_modified_by, interview.last_modified_date = temp.last_modified_date;
*/

update talent_recruitment_process_interview i
    right join
    (WITH ranked_messages AS (SELECT a.*, ROW_NUMBER() OVER (PARTITION BY application_id, event_stage ORDER BY id DESC) AS rn
    FROM activity AS a
    where a.status = 4)
    SELECT id, application_id, ifnull(max(event_date), created_date) as 'event_date', max(event_time_zone) as 'time_zone', ifnull(max(event_type), 404) as 'type', ifnull(event_stage, 1) as 'round', created_by, created_date, last_modified_by, last_modified_date from ranked_messages group by application_id, event_stage) temp on temp.application_id = i.talent_recruitment_process_id and temp.round = i.progress
    set i.from_time = temp.event_date, i.to_time = temp.event_date, i.time_zone = temp.time_zone, i.interview_type = case temp.type when 401 then 0 when 402 then 1 when 403 then 2 else 3 end, i.created_date = temp.created_date, i.created_by = temp.created_by, i.last_modified_date = temp.last_modified_date, i.last_modified_by = temp.last_modified_by;

-- active
update talent_recruitment_process_node t, application a
set t.node_status=1, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
where t.node_type=30 and a.status = 4 and t.talent_recruitment_process_id = a.id;
-- complete
/*
update talent_recruitment_process_node t, application a
set t.node_status=2, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
 where t.node_type=30 and a.status > 4 and t.talent_recruitment_process_id = a.id;
*/
/*
update talent_recruitment_process_node n1
    left join talent_recruitment_process_node n2
on n1.id < n2.id
    and n1.talent_recruitment_process_id = n2.talent_recruitment_process_id
    and n2.node_status = 1 and n2.node_type = 30
    set n1.node_status = 2 where n1.node_type < 30;
*/

/*
update talent_recruitment_process_node n1
    inner join (select * from talent_recruitment_process_node n2 where n2.node_status = 1 and n2.node_type = 30) temp
    on temp.talent_recruitment_process_id = n1.talent_recruitment_process_id and n1.id < temp.id
set n1.node_status = 2
where n1.node_type < 30 and n1.node_status = 3;
*/

/* ===========offer============ */
insert into talent_recruitment_process_offer(talent_recruitment_process_id, signed_date, estimate_onboard_date, note,
                                             created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, date(ac.created_date), date(ac.created_date), GROUP_CONCAT(concat(a.memo, ' ---- ', concat(concat(u.first_name, ' ', u.last_name), ' ', a.created_date))
    SEPARATOR '.\r\n\n') as 'note', ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from activity_unique ac left join activity a on ac.application_id = a.application_id and a.status = ac.status
    left join user u on a.user_id = u.id where ac.status = 5 group by ac.application_id;

-- active
update talent_recruitment_process_node t, application a
set t.node_status=1, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
where t.node_type=40 and a.status = 5 and t.talent_recruitment_process_id = a.id;
-- complete
/*
update talent_recruitment_process_node t, application a
set t.node_status=2, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
 where t.node_type=40 and a.status > 5 and t.talent_recruitment_process_id = a.id;
 */

/*
update talent_recruitment_process_node n1
    left join talent_recruitment_process_node n2
on n1.id < n2.id
    and n1.talent_recruitment_process_id = n2.talent_recruitment_process_id
    and n2.node_status = 1 and n2.node_type = 40
    set n1.node_status = 2 where n1.node_type < 40;
*/

/*
update talent_recruitment_process_node n1
    inner join (select * from talent_recruitment_process_node n2 where n2.node_status = 1 and n2.node_type = 40) temp
    on temp.talent_recruitment_process_id = n1.talent_recruitment_process_id and n1.id < temp.id
set n1.node_status = 2
where n1.node_type < 40 and n1.node_status = 3;
*/


/* ===========agreed_pay_rate============ */
insert into talent_recruitment_process_ipg_agreed_pay_rate(talent_recruitment_process_id, currency, rate_unit_type, agreed_pay_rate,
                                                           created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ac.currency, ac.rate_unit_type, ac.agreed_pay_rate,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_agreed_pay_rate ac;

/* ===========kpi_user============ */
/*
insert into talent_recruitment_process_kpi_user(talent_recruitment_process_id, user_id, user_role, percentage, currency, amount,
                                                created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ac.user_id, ac.user_role, ac.percentage, null, null,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_commission ac;
*/
-- delete dirty data
delete start_commission from start_commission left join start s on start_commission.start_id = s.id where s.id is null;


-- insert data from application kpi user
insert into talent_recruitment_process_kpi_user(talent_recruitment_process_id, user_id, user_role, percentage, currency, amount,
                                                created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ac.user_id, ac.user_role, ac.percentage, null, null,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_commission ac left join start s on ac.application_id = s.talent_recruitment_process_id left join start_commission sc on s.id = sc.start_id where sc.id is null;

-- insert data from start kpi user
insert into talent_recruitment_process_kpi_user(talent_recruitment_process_id, user_id, user_role, percentage, currency, amount,
                                                created_by, created_date, last_modified_by, last_modified_date)
SELECT s.talent_recruitment_process_id, sc.user_id, sc.user_role, sc.percentage, null, null,
       sc.created_by, sc.created_date, sc.last_modified_by, sc.last_modified_date
from start s left join start_commission sc on s.id = sc.start_id where sc.id is not null and s.start_type in (0,4);

/* ===========offer_salary============ */
/*
insert into talent_recruitment_process_offer_salary(talent_recruitment_process_id, currency, rate_unit_type,
                                                    created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ac.currency, ac.rate_unit_type,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_offer_letter ac;
*/
insert into talent_recruitment_process_offer_salary(talent_recruitment_process_id, currency, rate_unit_type,
                                                    created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ifnull(s.currency, ac.currency), COALESCE(sfr.rate_unit_type, scr.rate_unit_type, ac.rate_unit_type),
       ifnull(s.created_by, ac.created_by), ifnull(s.created_date, ac.created_date), ifnull(s.last_modified_by, ac.last_modified_by), ifnull(s.last_modified_date, ac.last_modified_date)
from application_offer_letter ac left join start s on ac.application_id = s.talent_recruitment_process_id and s.start_type = 0
                                 left join start_fte_rate sfr on s.id = sfr.start_id left join start_contract_rate scr on s.id = scr.start_id group by ac.application_id;

/* ===========offer_salary_package============ */
insert into talent_recruitment_process_offer_salary_package(talent_recruitment_process_id, salary_type, amount, need_charge,
                                                            created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, 0 salary_type, ac.salary amount, true need_charge,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_offer_letter ac where ac.position_type not in (1,5)
union all
SELECT ac.application_id, 1 salary_type, ac.retention_bonus amount, true need_charge,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_offer_letter ac where ac.position_type not in (1,5)
union all
SELECT ac.application_id, 2 salary_type, ac.sign_on_bonus amount, true need_charge,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_offer_letter ac where ac.position_type not in (1,5)
union all
SELECT ac.application_id, 3 salary_type, ac.annual_bonus amount, true need_charge,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_offer_letter ac where ac.position_type not in (1,5)
union all
SELECT ac.application_id, 4 salary_type, ac.relocation_package amount, true need_charge,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_offer_letter ac where ac.position_type not in (1,5)
union all
SELECT ac.application_id, 5 salary_type, ac.extra_fee amount, true need_charge,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_offer_letter ac where ac.position_type not in (1,5);

/* ===========offer_fee_charge============ */
/*
insert into talent_recruitment_process_offer_fee_charge(talent_recruitment_process_id, total_billable_amount, fee_type, fee_amount, total_amount,
                                                        created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ac.total_billable_amount, (if (ac.fee_type is null, 2, ac.fee_type)) fee_type,
       (if(ac.fee_type = 1, ac.fee_percentage, ac.total_bill_amount)) fee_amount, ac.total_bill_amount,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_offer_letter ac where ac.position_type not in (1,5);
*/
-- fte fee
insert into talent_recruitment_process_offer_fee_charge(talent_recruitment_process_id, total_billable_amount, fee_type, fee_amount, total_amount,
                                                        created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ifnull(sfr.total_billable_amount, ac.total_billable_amount), COALESCE(sfr.fee_type, ac.fee_type, 2) as 'fee_type',
        (if(ifnull(sfr.fee_type, ac.fee_type) = 1, ifnull(sfr.fee_percentage, ac.fee_percentage)/100, ifnull(sfr.total_bill_amount, ac.total_bill_amount))) fee_amount, ifnull(sfr.total_bill_amount, ac.total_bill_amount),
       ifnull(sfr.created_by, ac.created_by), ifnull(sfr.created_date, ac.created_date), ifnull(ac.last_modified_by ,sfr.last_modified_by), ifnull(ac.last_modified_date, sfr.last_modified_date)
from application_offer_letter ac
         left join start s on ac.application_id = s.talent_recruitment_process_id and s.position_type not in (1,5) and s.start_type <> 5
         left join start_fte_rate sfr on s.id = sfr.start_id
where ac.position_type not in (1,5) group by ac.application_id;


/* ===========ipg_offer_accept============ */
insert into talent_recruitment_process_ipg_offer_accept(talent_recruitment_process_id, note,
                                                        created_by, created_date, last_modified_by, last_modified_date)
SELECT application_id, GROUP_CONCAT(concat(a.memo, ' ---- ', concat(concat(u.first_name, ' ', u.last_name), ' ', a.created_date)) order by a.created_date asc
                    SEPARATOR '.\r\n\n') as 'note',
        a.created_by, a.created_date, a.last_modified_by, a.last_modified_date
from activity a left join user u on a.user_id = u.id left join job j on a.job_id = j.id where a.status = 6 and j.job_type <> 5 group by application_id;

-- active
update talent_recruitment_process_node t, application a
set t.node_status=1, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
where t.node_type=41 and a.status = 6 and t.talent_recruitment_process_id = a.id;
-- complete
/*
update talent_recruitment_process_node t, application a
set t.node_status=2, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
 where t.node_type=41 and a.status > 6 and t.talent_recruitment_process_id = a.id;
*/
/*
update talent_recruitment_process_node n1
    left join talent_recruitment_process_node n2
on n1.id < n2.id
    and n1.talent_recruitment_process_id = n2.talent_recruitment_process_id
    and n2.node_status = 1 and n2.node_type = 41
    set n1.node_status = 2 where n1.node_type < 41;
*/

/*
update talent_recruitment_process_node n1
    inner join (select * from talent_recruitment_process_node n2 where n2.node_status = 1 and n2.node_type = 41) temp
    on temp.talent_recruitment_process_id = n1.talent_recruitment_process_id and n1.id < temp.id
set n1.node_status = 2
where n1.node_type < 41 and n1.node_status = 3;
*/

/* ===========contract_fee_charge============ */
/*
insert into talent_recruitment_process_ipg_contract_fee_charge(talent_recruitment_process_id, final_bill_rate, final_pay_rate,
                                                               tax_burden_rate, msp_rate, immigration_cost, extra_cost,
                                                               estimated_working_hour_per_week, gm,
                                                               created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ac.final_bill_rate, ac.final_pay_rate,
   ifnull(ac.tax_burden_rate, 0), ifnull(ac.msp_rate, 0), ifnull(ac.immigration_cost, 0), ac.extra_cost,
       ac.estimated_working_hour_per_week, ac.total_bill_amount,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from application_offer_letter ac where ac.position_type in (1,5);
*/
insert into talent_recruitment_process_ipg_contract_fee_charge(talent_recruitment_process_id, final_bill_rate, final_pay_rate,
                                                               tax_burden_rate, msp_rate, immigration_cost, extra_cost,
                                                               estimated_working_hour_per_week, gm,
                                                               created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ifnull(scr.final_bill_rate, ac.final_bill_rate),ifnull(scr.final_pay_rate, ac.final_pay_rate),
       COALESCE(scr.tax_burden_rate, ac.tax_burden_rate, 0), COALESCE(scr.msp_rate, ac.msp_rate, 0), COALESCE(scr.immigration_cost, ac.immigration_cost, 0), ifnull(scr.extra_cost, ac.extra_cost),
       ifnull(scr.estimated_working_hour_per_week, ac.estimated_working_hour_per_week), ifnull(scr.total_bill_amount, ac.total_bill_amount),
       ifnull(scr.created_by, ac.created_by), ifnull(scr.created_date, ac.created_date), ifnull(scr.last_modified_by, ac.last_modified_by), ifnull(scr.last_modified_date, ac.last_modified_date)
from application_offer_letter ac
         left join start s on ac.application_id = s.talent_recruitment_process_id and s.position_type in (1,5) and s.start_type = 0
         left join start_contract_rate scr on s.id = scr.start_id
where ac.position_type in (1,5) group by ac.application_id;

/* ===========onboard_date============ */
-- offer accepte
insert into talent_recruitment_process_onboard_date(talent_recruitment_process_id, onboard_date, warranty_end_date,
                                                    end_date, currency, rate_unit_type,
                                                    created_by, created_date, last_modified_by, last_modified_date)
SELECT au.application_id, aol.start_date, aol.warranty_end_date, aol.end_date, aol.currency, aol.rate_unit_type,
       aol.created_by, aol.created_date, aol.last_modified_by, aol.last_modified_date
from activity_unique au left join start s on au.application_id = s.talent_recruitment_process_id
                        left join application_offer_letter aol on aol.application_id = au.application_id where s.id is null and au.status = 6 and aol.id is not null group by au.application_id;

-- start
insert into talent_recruitment_process_onboard_date(talent_recruitment_process_id, onboard_date, warranty_end_date,
                                                    end_date, currency, rate_unit_type,
                                                    created_by, created_date, last_modified_by, last_modified_date)
SELECT au.application_id, s.start_date, s.warranty_end_date, s.end_date, COALESCE(sfr.currency, scr.currency, s.currency, aol.currency), COALESCE(sfr.rate_unit_type, scr.rate_unit_type, aol.rate_unit_type),
       ifnull(s.created_by, au.created_by), ifnull(s.created_date, au.created_date), ifnull(s.last_modified_by, au.last_modified_by), ifnull(s.last_modified_date, au.last_modified_date)
from activity_unique au left join start s on au.application_id = s.talent_recruitment_process_id left join application_offer_letter aol on aol.application_id = au.application_id
                        left join start_fte_rate sfr on s.id = sfr.start_id left join start_contract_rate scr on scr.start_id = s.id
where au.status = 9 and s.id is not null group by s.talent_recruitment_process_id;

/* ===========onboard============ */
insert into talent_recruitment_process_onboard(talent_recruitment_process_id, note,
                                               created_by, created_date, last_modified_by, last_modified_date)
SELECT ac.application_id, ac.memo,
       ac.created_by, ac.created_date, ac.last_modified_by, ac.last_modified_date
from activity_unique ac where ac.status = 9;

-- active
update talent_recruitment_process_node t, application a
set t.node_status=1, t.last_modified_date=a.last_modified_date, t.last_modified_by=a.last_modified_by
where t.node_type=60 and a.status in(9, 90, 91, 92, 94) and t.talent_recruitment_process_id = a.id;

-- complete
/*
update talent_recruitment_process_node n1
    left join talent_recruitment_process_node n2
on n1.id < n2.id
    and n1.talent_recruitment_process_id = n2.talent_recruitment_process_id
    and n2.node_status = 1 and n2.node_type = 60
    set n1.node_status = 2 where n1.node_type < 60;
*/


/*
update talent_recruitment_process_node n1
    inner join (select * from talent_recruitment_process_node n2 where n2.node_status = 1 and n2.node_type = 60) temp
    on temp.talent_recruitment_process_id = n1.talent_recruitment_process_id and n1.id < temp.id
set n1.node_status = 2
where n1.node_type < 60 and n1.node_status = 3;
*/


/* ===========client_info============ */
-- delete dirty data (28 rows) from start_client_info
delete from start_client_info where id in (331,389,587,948,1032,1152,1267,1309,1510,1591,1592,1595,1598,1693,1707,1757,1761,1762,1772,1864,1872,1873,1874,1908,1922,1925,1985,2009);

-- migrate data from start_client_info to talent_recruitment_process_onboard_client_info
insert into talent_recruitment_process_onboard_client_info(talent_recruitment_process_id, client_name, client_division, client_address,
                                                           created_by, created_date, last_modified_by, last_modified_date)
SELECT (select s.talent_recruitment_process_id from start s where s.id = sci.start_id) talent_recruitment_process_id,
       sci.client_name, sci.client_division, sci.client_address,
       sci.created_by, sci.created_date, sci.last_modified_by, sci.last_modified_date
from start_client_info sci
where sci.client_name is not null;


/* ===========eliminate============ */
insert into talent_recruitment_process_eliminate(talent_recruitment_process_id, reason, note,
                                                 created_by, created_date, last_modified_by, last_modified_date)
select a.id, 0, GROUP_CONCAT(concat(a2.memo, ' ---- ', concat(concat(u.first_name, ' ', u.last_name), ' ', a2.created_date)) SEPARATOR '.\r\n\n') as 'note', a2.created_by, a2.created_date, a2.last_modified_by, a2.last_modified_date
from application a left join activity a2 on a.id = a2.application_id and a.status = a2.status left join user u on a2.user_id = u.id where a.status in (7,14,90) group by a.id;

insert into talent_recruitment_process_eliminate(talent_recruitment_process_id, reason, note,
                                                 created_by, created_date, last_modified_by, last_modified_date)
select a.id, 1, GROUP_CONCAT(concat(a2.memo, ' ---- ', concat(concat(u.first_name, ' ', u.last_name), ' ', a2.created_date)) SEPARATOR '.\r\n\n') as 'note', a2.created_by, a2.created_date, a2.last_modified_by, a2.last_modified_date
from application a left join activity a2 on a.id = a2.application_id and a.status = a2.status left join user u on a2.user_id = u.id where a.status=8 group by a.id;

insert into talent_recruitment_process_eliminate(talent_recruitment_process_id, reason, note,
                                                 created_by, created_date, last_modified_by, last_modified_date)
select a.id, 2, GROUP_CONCAT(concat(a2.memo, ' ---- ', concat(concat(u.first_name, ' ', u.last_name), ' ', a2.created_date)) SEPARATOR '.\r\n\n') as 'note', a2.created_by, a2.created_date, a2.last_modified_by, a2.last_modified_date
from application a left join activity a2 on a.id = a2.application_id and a.status = a2.status left join user u on a2.user_id = u.id where a.status=13 group by a.id;




-- TODO
/* 根据talent_recruitment_process_eliminate表中talent_recruitment_process_id，将talent_recruitment_process_node表中note_status=1 (ACTIVE)更改为了note_status=4 (ELIMINATED)
note:对应一个talent_recruitment_process_id，有且仅有一个（除非已经完成了整个流程的recruitment_process）note_type处于active状态 */

update talent_recruitment_process_node t right join talent_recruitment_process_eliminate e on e.talent_recruitment_process_id = t.talent_recruitment_process_id
    set t.node_status = 4 where t.node_status = 1;



-- update for each applicaton, all nodes before ACTIVE(node_status=1) OR ELIMINATED(node_status=4) should update to COMPLETED(node_status=2)
create table if not exists talent_recruitment_process_node_temp
(
    id                            bigint auto_increment primary key,
    talent_recruitment_process_id bigint                                    not null,
    node_id                       bigint                                    not null,
    node_type                     int                                       not null,
    node_status                   int                                       not null
);

create index idx_trp_node_temp_trpid
    on talent_recruitment_process_node (talent_recruitment_process_id);

create index idx_trp_node_temp_node_id
    on talent_recruitment_process_node (node_id);

create index idx_trp_node_temp_node_type
    on talent_recruitment_process_node (node_type);

create index idx_trp_node_temp_node_status
    on talent_recruitment_process_node (node_status);


INSERT INTO talent_recruitment_process_node_temp(id,talent_recruitment_process_id,node_id,node_type,node_status)
SELECT trpn.id, trpn.talent_recruitment_process_id, trpn.node_id, trpn.node_type, trpn.node_status
FROM talent_recruitment_process_node trpn
WHERE trpn.node_status = 1 or trpn.node_status = 4;



update talent_recruitment_process_node n
    inner join talent_recruitment_process_node_temp temp on temp.talent_recruitment_process_id = n.talent_recruitment_process_id and n.node_type < temp.node_type and n.id < temp.id
    set n.node_status = 2
where n.node_status = 3;

