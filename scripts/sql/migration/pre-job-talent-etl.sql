# 为job&talent etl程序执行做准备
DROP TABLE `talent_resume_relation`;
CREATE TABLE `talent_resume_relation` (
                                          `id` bigint NOT NULL AUTO_INCREMENT,
                                          `talent_id` bigint NOT NULL,
                                          `resume_id` bigint NOT NULL,
                                          `tenant_id` bigint NOT NULL,
                                          `file_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                          `source_type` tinyint DEFAULT NULL,
                                          `created_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
                                          `created_date` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
                                          `last_modified_by` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
                                          `last_modified_date` timestamp(3) NULL DEFAULT NULL,
                                          PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `transform_error` (
                                   `id` bigint NOT NULL AUTO_INCREMENT,
                                   `tid` bigint DEFAULT NULL,
                                   `type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                                   `exception` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci,
                                   `created_date` datetime DEFAULT NULL,
                                   `version` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                                   `section` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL,
                                   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;