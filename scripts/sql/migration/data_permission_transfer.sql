-- ------------- user_admin --------------
create table user_admin
(
    id                 bigint auto_increment
        primary key,
    uid                varchar(255)                              null,
    username           varchar(50)                               null,
    email              varchar(100)                              not null,
    password_hash      varchar(60)                               null,
    first_name         varchar(50)                               null,
    last_name          varchar(50)                               null,
    phone              varchar(255)                              null,
    tenant_id          bigint                                    null,
    activated          bit                                       not null,
    lang_key           varchar(5)                                null,
    activation_key     varchar(20)                               null,
    reset_key          varchar(20)                               null,
    reset_date         timestamp(3)                              null,
    created_by         varchar(50)                               not null,
    created_date       timestamp(3) default CURRENT_TIMESTAMP(3) not null,
    last_modified_by   varchar(50)                               null,
    last_modified_date timestamp(3)                              null,
    constraint email
        unique (email),
    constraint uid
        unique (uid),
    constraint username
        unique (username)
);

INSERT INTO user_admin (id, uid, username, email, password_hash, first_name, last_name, phone, tenant_id, activated, lang_key, activation_key, reset_key, created_by, created_date, reset_date, last_modified_by, last_modified_date) VALUES (1, '1,ADMIN', 'PlatformAdmin', '<EMAIL>', '$2a$10$BBoc801Zxamw6wjbDIAV9OTvfjUyKsR58QnN//ZaptZzqEyugW2z2', 'PlatformAdmin', 'Platform', null, -1, true, 'en', null, null, '-1,-1', '2022-09-02 12:17:32', null, '-1,-1', '2022-12-24 11:07:13');


-- ------------- user_admin_role --------------

create table user_admin_role
(
    id      int auto_increment
        primary key,
    user_id bigint null,
    role_id bigint null
);

create index idx_user_role
    on user_admin_role (user_id);

INSERT INTO user_admin_role (id, user_id, role_id) VALUES (1, 1, 2);

-- ------------- permission_table --------------
create table permission_table
(
    id                 bigint auto_increment
        primary key,
    name               varchar(50)                         null,
    user_owner_column  varchar(50)                         null,
    team_owner_column  varchar(50)                         null,
    created_by         varchar(50)                         not null,
    created_date       timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by   varchar(50)                         null,
    last_modified_date timestamp                           null
);

INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (2, 'company', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (3, 'company_address', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (4, 'company_assign_team_member', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (5, 'company_contract', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (6, 'company_contract_signer', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (7, 'company_progress_note', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (8, 'company_project_team', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (9, 'company_project_team_user', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (10, 'company_sales_lead', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (11, 'company_sales_lead_administrator', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (12, 'company_sales_lead_client_contact', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (13, 'company_sales_lead_connect_client_contact', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (14, 'company_sales_lead_service_type', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (15, 'invoice', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (16, 'invoice_activity', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (17, 'invoice_client_credit', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (18, 'invoice_payment_record', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (19, 'job', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (20, 'job_company_contact_relation', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (21, 'job_ipg_relation', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (22, 'job_location', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (23, 'job_note', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (24, 'start', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (25, 'start_address', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (26, 'start_client_info', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (27, 'start_commission', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (28, 'start_contract_rate', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (29, 'start_failed_warranty', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (30, 'start_fte_rate', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (31, 'start_fte_salary_package', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (32, 'start_rate_change', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (33, 'start_termination', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (34, 'talent', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (35, 'talent_contact', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (36, 'talent_current_location', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (37, 'talent_note', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (38, 'talent_ownership', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (39, 'talent_recruitment_process', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (40, 'talent_recruitment_process_commission', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (41, 'talent_recruitment_process_eliminate', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (42, 'talent_recruitment_process_interview', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (43, 'talent_recruitment_process_ipg_agreed_pay_rate', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (44, 'talent_recruitment_process_ipg_contract_fee_charge', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (45, 'talent_recruitment_process_ipg_fte_salary_package', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (46, 'talent_recruitment_process_ipg_offer_accept', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (47, 'talent_recruitment_process_kpi_user', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (48, 'talent_recruitment_process_node', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (49, 'talent_recruitment_process_offer', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (50, 'talent_recruitment_process_offer_fee_charge', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (51, 'talent_recruitment_process_offer_salary', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (52, 'talent_recruitment_process_offer_salary_package', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (53, 'talent_recruitment_process_onboard', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (54, 'talent_recruitment_process_onboard_client_info', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (55, 'talent_recruitment_process_onboard_date', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (56, 'talent_recruitment_process_submit_to_client', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (57, 'talent_recruitment_process_submit_to_job', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (58, 'talent_resume_relation', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (59, 'talent_tracking_note', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (60, 'talent_tracking_record', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_table (id, name, user_owner_column, team_owner_column, created_by, created_date, last_modified_by, last_modified_date) VALUES (61, 'talent_work_authorization_relation', 'puser_id', 'pteam_id', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- ------------- permission_module --------------
create table permission_module
(
    id                 bigint auto_increment
        primary key,
    name               varchar(50)                         null,
    created_by         varchar(50)                         not null,
    created_date       timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by   varchar(50)                         null,
    last_modified_date timestamp                           null
);

INSERT INTO permission_module (id, name, created_by, created_date, last_modified_by, last_modified_date) VALUES (1, 'company', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module (id, name, created_by, created_date, last_modified_by, last_modified_date) VALUES (2, 'job', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module (id, name, created_by, created_date, last_modified_by, last_modified_date) VALUES (3, 'talent', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module (id, name, created_by, created_date, last_modified_by, last_modified_date) VALUES (4, 'finance', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module (id, name, created_by, created_date, last_modified_by, last_modified_date) VALUES (5, 'application', '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- ------------- permission_module_table --------------
create table permission_module_table
(
    id                 bigint auto_increment
        primary key,
    module_id          bigint                              null,
    table_id           bigint                              null,
    created_by         varchar(50)                         not null,
    created_date       timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by   varchar(50)                         null,
    last_modified_date timestamp                           null
);

INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1, 1, 2, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (2, 1, 3, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (3, 1, 4, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (4, 1, 5, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (5, 1, 6, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (6, 1, 7, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (7, 1, 8, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (8, 1, 9, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (9, 1, 10, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (10, 1, 11, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (11, 1, 12, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (12, 1, 13, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (13, 1, 14, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (14, 2, 19, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (15, 2, 20, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (16, 2, 21, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (17, 2, 22, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (18, 2, 23, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (19, 3, 34, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (20, 3, 35, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (21, 3, 36, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (22, 3, 37, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (23, 3, 38, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (24, 3, 58, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (25, 3, 59, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (26, 3, 60, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (27, 3, 61, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (28, 4, 15, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (29, 4, 16, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (30, 4, 17, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (31, 4, 18, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (32, 4, 24, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (33, 4, 25, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (34, 4, 26, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (35, 4, 27, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (36, 4, 28, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (37, 4, 29, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (38, 4, 30, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (39, 4, 31, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (40, 4, 32, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (41, 4, 33, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (42, 5, 39, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (43, 5, 40, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (44, 5, 41, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (45, 5, 2, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (46, 5, 43, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (47, 5, 44, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (48, 5, 45, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (49, 5, 46, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (50, 5, 47, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (51, 5, 48, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (52, 5, 49, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (53, 5, 50, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (54, 5, 51, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (55, 5, 52, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_module_table (id, module_id, table_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (60, 5, 57, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- ------------- permission_tenant_module --------------
create table permission_tenant_module
(
    id                      bigint auto_increment
        primary key,
    tenant_id               bigint                              null,
    module_id               bigint                              null,
    involve_data_permission bit                                 null,
    created_by              varchar(50)                         not null,
    created_date            timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by        varchar(50)                         null,
    last_modified_date      timestamp                           null
);

INSERT INTO permission_tenant_module (id, tenant_id, module_id, involve_data_permission, created_by, created_date, last_modified_by, last_modified_date) VALUES (1, 4, 1, false, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_tenant_module (id, tenant_id, module_id, involve_data_permission, created_by, created_date, last_modified_by, last_modified_date) VALUES (2, 4, 2, true, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_tenant_module (id, tenant_id, module_id, involve_data_permission, created_by, created_date, last_modified_by, last_modified_date) VALUES (3, 4, 3, false, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_tenant_module (id, tenant_id, module_id, involve_data_permission, created_by, created_date, last_modified_by, last_modified_date) VALUES (4, 4, 4, false, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_tenant_module (id, tenant_id, module_id, involve_data_permission, created_by, created_date, last_modified_by, last_modified_date) VALUES (5, 4, 5, false, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- ------------- permission_team --------------
create table permission_team
(
    id                 bigint auto_increment
        primary key,
    name               varchar(50)                         null,
    code               varchar(80)                         null,
    parent_id          bigint                              null,
    tenant_id          bigint                              null,
    level              int                                 null,
    is_leaf            tinyint                             null,
    deleted            tinyint                             null,
    created_by         varchar(50)                         not null,
    created_date       timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by   varchar(50)                         null,
    last_modified_date timestamp                           null,
    constraint code
        unique (code)
);

-- INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (3, '测试权限适配', '3_0001', -1, 3, 0, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (4, 'test', '33_0001', -1, 33, 0, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (14, 'China-Wuhan', '4_00090001', 52, 4, 1, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (15, 'China-Wuhan-Recruiting team', '4_00030001', 14, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (16, 'China-Wuhan-Beijing Support Team', '4_00030002', 14, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (17, 'China-Wuhan-Development Team', '4_00030003', 14, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (18, 'China-Wuhan-HR Team', '4_00030004', 14, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (19, 'China-Wuhan-Sourcering Team', '4_00030005', 14, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (20, 'China-Wuhan-UOFFER Support Team', '4_00030006', 14, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (22, 'China-Wuhan-US AM Support Team', '4_00030007', 14, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (23, 'China-Wuhan-US HR Offshore Team', '4_00030008', 14, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (24, 'China-Wuhan-US Marketing Offshore Team', '4_00030009', 14, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (25, 'US', '4_0004', -1, 4, 0, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (26, 'US-CEO', '4_00040001', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (27, 'US-AM', '4_00040002', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (28, 'US-BD', '4_00040003', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (29, 'US-Data & Technology', '4_00040004', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (30, 'US-Strategy & Engagement', '4_00040005', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (31, 'US-Marketing & Branding', '4_00040006', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (32, 'US-Recruiting', '4_00040007', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (33, 'US-Accounting & HR Service', '4_00040008', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (34, 'US-HR', '4_00040009', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (35, 'US-Legal', '4_00040010', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (36, 'US-Uoffer US', '4_00040011', 25, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (37, 'Singapore', '4_0005', -1, 4, 0, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (38, 'Singapore-Recruiting', '4_00050001', 37, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (39, 'Singapore-Jerry Team', '4_00050002', 37, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (40, 'India', '4_0006', -1, 4, 0, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (41, 'Indian Team', '4_00060001', 40, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (42, 'EU', '4_0007', -1, 4, 0, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (43, 'EU Team', '4_00070001', 42, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (44, 'Canada', '4_0008', -1, 4, 0, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (45, 'Canada-Recruiting', '4_00080001', 44, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (46, 'Canada-HR', '4_00080002', 44, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (47, 'Canada-Account Management', '4_00080003', 44, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (48, 'Canada-Management', '4_00080004', 44, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (49, 'Canada-Marketing', '4_00080005', 44, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (50, 'Canada-Talent Acquisition', '4_00080006', 44, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (52, 'China', '4_0009', -1, 4, 0, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (65, 'China-Kris Team', '4_00090002', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (66, 'China-Jocelyn Team', '4_00090003', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (67, 'China-HR Team', '4_00090004', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (68, 'China-Vera Team', '4_00090005', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (69, 'China-Summer&Kaylee Team', '4_00090006', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (70, 'China-Carol Team', '4_00090007', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (71, 'China-Finance Team', '4_00090008', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (72, 'China-George Team', '4_00090009', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (73, 'China-Robert Team', '4_00090010', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (74, 'China-Linda Team', '4_00090011', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (75, 'China-Coco Team', '4_00090012', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (76, 'China-Cristina Team', '4_00090013', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (77, 'China-Rebecca Team', '4_00090014', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (78, 'China-Uoffer Team', '4_00090015', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (79, 'teste1109', '14_0001', -1, 14, 0, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (87, 'test', '4_000900010010', 14, 4, 2, 1, 0, '322,4', '2022-11-15 01:16:07', '322,4', '2022-11-15 01:16:07');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (80, 'China-Jing Team', '4_00090016', 52, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (81, 'Philippines', '4_0010', -1, 4, 0, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (82, 'Philippines-Recruiting', '4_00100001', 81, 4, 1, 1, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (90, 'Inactive', '4_0011', -1, 4, 0, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
INSERT INTO permission_team (id, name, code, parent_id, tenant_id, level, is_leaf, deleted, created_by, created_date, last_modified_by, last_modified_date) VALUES (91, 'Others', '4_0012', -1, 4, 0, 0, 0, '-1,-1', '2022-11-12 22:58:44', '-1,-1', '2022-11-12 22:58:44');
-- ------------- permission_team_leader --------------
create table permission_team_leader
(
    id                 bigint auto_increment
        primary key,
    team_id            bigint                              null,
    user_id            bigint                              null,
    tenant_id          bigint                              null,
    created_by         varchar(50)                         not null,
    created_date       timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by   varchar(50)                         null,
    last_modified_date timestamp                           null
);


INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1, 17, 17, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (3, 18, 256, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (4, 19, 77, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (5, 20, 458, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (6, 23, 505, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (7, 23, 542, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (8, 26, 17, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (9, 28, 613, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (10, 28, 448, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (11, 29, 380, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (12, 29, 322, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (13, 32, 465, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (14, 32, 24, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (15, 32, 19, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (16, 32, 218, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (17, 32, 493, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (18, 33, 805, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (19, 34, 1034, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (20, 38, 220, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (21, 41, 1149, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (22, 75, 974, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (23, 76, 1004, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (24, 72, 604, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (25, 67, 437, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (26, 66, 457, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (27, 65, 429, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (28, 74, 800, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (29, 77, 1159, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (30, 73, 716, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (31, 69, 499, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (32, 69, 496, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');
INSERT INTO permission_team_leader (id, team_id, user_id, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (33, 68, 474, 4, '-1,-1', '2022-11-12 22:59:08', '-1,-1', '2022-11-12 22:59:08');

-- ------------- permission_user_team --------------
create table permission_user_team
(
    id                 bigint auto_increment
        primary key,
    user_id            bigint                              null,
    team_id            bigint                              null,
    is_primary         tinyint                             null,
    tenant_id          bigint                              null,
    created_by         varchar(50)                         not null,
    created_date       timestamp default CURRENT_TIMESTAMP not null,
    last_modified_by   varchar(50)                         null,
    last_modified_date timestamp                           null
);

create index idx_permission_user_team_user_id_is_primary
    on permission_user_team (user_id asc, is_primary desc);

create index idx_permission_user_team_user_id_team_id
    on permission_user_team (user_id, team_id);


-- INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (3, 83, 3, 0, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
-- INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (5, 844, 4, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
-- INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (6, 850, 4, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (15, 256, 18, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (23, 1051, 18, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (24, 77, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (25, 70, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (33, 430, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (34, 362, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (35, 352, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (36, 260, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (37, 233, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (38, 206, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (39, 201, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (40, 181, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (41, 673, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (42, 674, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (43, 689, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (44, 718, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (45, 719, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (46, 721, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (47, 728, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (48, 767, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (49, 825, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (50, 896, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (51, 960, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (52, 985, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (53, 1055, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (54, 1073, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (55, 1075, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (56, 1085, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (57, 1106, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (58, 1144, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (59, 1162, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (60, 1217, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (61, 1222, 19, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (62, 1020, 22, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (63, 1050, 16, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (64, 1138, 22, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (65, 458, 20, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (66, 984, 20, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (67, 1188, 20, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (68, 1207, 20, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (69, 1209, 20, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (70, 1041, 20, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (71, 348, 24, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (72, 535, 24, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (73, 536, 24, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (75, 729, 24, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (76, 505, 23, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (77, 542, 23, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (78, 690, 23, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (79, 903, 23, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (80, 1113, 23, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (81, 1135, 23, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (82, 1181, 23, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (83, 418, 17, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (85, 17, 26, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (86, 20, 27, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (87, 432, 27, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (88, 445, 27, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (89, 795, 27, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (90, 1039, 27, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (91, 613, 28, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (92, 980, 28, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (93, 693, 28, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (94, 448, 28, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (95, 380, 29, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (96, 322, 29, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (97, 339, 29, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (98, 439, 30, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (99, 545, 30, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (103, 874, 31, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (104, 962, 31, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (105, 24, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (106, 19, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (107, 218, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (109, 493, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (110, 611, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (111, 25, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (112, 684, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (113, 630, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (114, 688, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (115, 782, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (116, 781, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (117, 867, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (118, 942, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (119, 946, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (120, 1235, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (121, 965, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (122, 1022, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (123, 1030, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (124, 1069, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (125, 1078, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (126, 1071, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (127, 1100, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (128, 1139, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (129, 195, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (130, 1177, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (131, 1172, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (132, 1171, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (133, 1184, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (134, 1183, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (135, 1192, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (136, 1208, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (137, 1201, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (138, 1199, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (139, 1200, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (140, 1221, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (141, 1226, 32, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (142, 805, 33, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (143, 810, 33, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (144, 697, 33, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (145, 854, 33, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (146, 884, 33, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (147, 882, 33, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (148, 1034, 34, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (149, 900, 34, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (150, 973, 34, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (151, 1212, 35, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (152, 596, 36, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (153, 1127, 36, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (154, 528, 36, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (155, 883, 36, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (156, 220, 38, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (157, 1180, 38, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (162, 1149, 41, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (163, 1215, 41, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (164, 1202, 41, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (165, 1223, 41, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (166, 1220, 41, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (167, 451, 43, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (168, 641, 43, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (169, 652, 43, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (170, 1068, 43, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (171, 1112, 43, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (177, 647, 46, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (178, 639, 47, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (179, 640, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (180, 659, 48, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (181, 661, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (182, 686, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (183, 699, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (184, 710, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (185, 707, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (186, 726, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (187, 754, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (188, 752, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (189, 794, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (190, 804, 47, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (191, 806, 46, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (192, 807, 49, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (193, 815, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (194, 865, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (195, 866, 47, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (196, 863, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (197, 909, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (198, 910, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (199, 908, 47, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (200, 915, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (201, 934, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (202, 956, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (203, 1008, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (204, 1064, 50, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (205, 1042, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (206, 1040, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (207, 1038, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (208, 1033, 49, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (209, 465, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (210, 1163, 45, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (211, 672, 24, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (212, 279, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (213, 318, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (214, 319, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (215, 763, 79, 0, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (216, 330, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (217, 384, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (218, 429, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (219, 437, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (220, 457, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (221, 462, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (222, 468, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (223, 474, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (224, 496, 69, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (225, 499, 69, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (226, 500, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (227, 503, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (228, 518, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (229, 529, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (230, 531, 70, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (231, 543, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (232, 549, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (233, 550, 71, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (234, 556, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (235, 557, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (236, 558, 70, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (237, 578, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (238, 589, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (239, 598, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (240, 599, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (241, 601, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (242, 602, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (243, 604, 72, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (244, 618, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (245, 629, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (246, 651, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (247, 656, 70, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (248, 670, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (249, 678, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (250, 680, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (251, 703, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (252, 704, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (253, 705, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (254, 716, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (255, 731, 72, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (256, 766, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (257, 769, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (258, 783, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (259, 790, 70, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (260, 800, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (261, 811, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (262, 822, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (263, 852, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (264, 853, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (265, 856, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (266, 871, 69, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (267, 881, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (268, 887, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (269, 892, 72, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (270, 913, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (271, 925, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (272, 927, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (273, 935, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (274, 958, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (275, 974, 75, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (276, 988, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (277, 991, 70, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (281, 996, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (282, 1003, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (283, 1004, 76, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (284, 1011, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (285, 1017, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (286, 1037, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (287, 1044, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (288, 1046, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (289, 1047, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (290, 1052, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (291, 1056, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (292, 1058, 70, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (293, 1060, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (294, 1062, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (295, 1072, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (296, 1079, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (297, 1090, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (298, 1091, 76, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (299, 1096, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (300, 1097, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (301, 1108, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (302, 1117, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (303, 1120, 76, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (304, 1122, 76, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (305, 1126, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (306, 1130, 72, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (307, 1151, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (308, 1152, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (309, 1153, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (310, 1158, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (311, 1159, 77, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (312, 1160, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (313, 1165, 77, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (314, 1166, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (315, 1167, 77, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (316, 1168, 78, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (317, 1169, 77, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (318, 1170, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (319, 1176, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (320, 1178, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (321, 1179, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (322, 1190, 69, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (323, 1191, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (324, 1196, 65, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (325, 1197, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (326, 1198, 70, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (327, 1204, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (328, 1205, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (329, 1206, 74, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (330, 1214, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (331, 1218, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (332, 1219, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (333, 1225, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (334, 1234, 67, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (335, 1237, 73, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (336, 1238, 66, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (337, 1239, 68, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (338, 1240, 70, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (340, 642, 18, 1, 4, '-1,-1', '2022-11-12 23:00:17', '-1,-1', '2022-11-12 23:00:17');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (341, 130, 27, 1, 4, '1,-1', '2022-11-14 19:43:20', '1,-1', '2022-11-14 19:43:20');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (343, 918, 30, 1, 4, '1,-1', '2022-11-15 01:07:37', '1,-1', '2022-11-15 01:07:37');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (344, 391, 38, 1, 4, '1,-1', '2022-11-15 01:08:19', '1,-1', '2022-11-15 01:08:19');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (345, 612, 45, 1, 4, '1,-1', '2022-11-15 01:08:58', '1,-1', '2022-11-15 01:08:58');
INSERT INTO permission_user_team (id, user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (346, 995, 67, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (168, 80, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (933, 82, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (953, 82, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (989, 82, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (990, 82, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');


INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1246, 65, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1252, 77, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (978, 78, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1254, 78, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (893, 78, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (970, 78, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (971, 78, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1241, 68, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1242, 68, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1243, 68, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1248, 68, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1249, 68, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1250, 68, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1251, 68, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1257, 68, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1247, 14, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (607, 43, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1005, 43, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1256, 43, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (450, 36, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (963, 30, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (919, 30, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (964, 34, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (463, 32, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1255, 32, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1224, 32, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (1141, 47, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date) VALUES (997, 47, 1, 4, '1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42');

update user u set u.activated=0 where u.id in (923, 912, 765, 738, 580, 304, 392, 482, 610, 662, 685, 696, 791, 796, 803, 826, 880, 972, 975, 1021, 1026, 1101, 471, 580, 873, 1164, 1173, 733, 823, 951, 977, 1012, 873, 967, 952);

INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date)
select u.id, 90, 1, 4, '-1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42'
from user u
where u.activated=0 and u.id not in (select ut.user_id from permission_user_team ut where ut.is_primary=1);

-- users who are not assigned to any teams
INSERT INTO permission_user_team (user_id, team_id, is_primary, tenant_id, created_by, created_date, last_modified_by, last_modified_date)
select u.id, 91, 1, 4, '-1,-1', '2022-11-15 01:09:42', '1,-1', '2022-11-15 01:09:42'
from user u
where u.id not in (select distinct ut.user_id from permission_user_team ut);

-- ----------- data permission ------------------

-- company module
-- alter table company add column puser_id bigint;
-- alter table company add column pteam_id bigint;
update company c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table company_address add column puser_id bigint;
-- alter table company_address add column pteam_id bigint;
update company_address c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_address c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table company_assign_team_member add column puser_id bigint;
-- alter table company_assign_team_member add column pteam_id bigint;
update company_assign_team_member c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_assign_team_member c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;


-- alter table company_contract add column puser_id bigint;
-- alter table company_contract add column pteam_id bigint;
update company_contract c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_contract c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table company_progress_note add column puser_id bigint;
-- alter table company_progress_note add column pteam_id bigint;
update company_progress_note c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_progress_note c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table company_project_team add column puser_id bigint;
-- alter table company_project_team add column pteam_id bigint;
update company_project_team c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_project_team c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table company_project_team_user add column puser_id bigint;
-- alter table company_project_team_user add column pteam_id bigint;
update company_project_team_user c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_project_team_user c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table company_sales_lead add column puser_id bigint;
-- alter table company_sales_lead add column pteam_id bigint;
update company_sales_lead c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_sales_lead c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table company_sales_lead_administrator add column puser_id bigint;
-- alter table company_sales_lead_administrator add column pteam_id bigint;
update company_sales_lead_administrator c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_sales_lead_administrator c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table company_sales_lead_client_contact add column puser_id bigint;
-- alter table company_sales_lead_client_contact add column pteam_id bigint;
update company_sales_lead_client_contact c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_sales_lead_client_contact c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;


-- alter table company_sales_lead_connect_client_contact add column puser_id bigint;
-- alter table company_sales_lead_connect_client_contact add column pteam_id bigint;
update company_sales_lead_connect_client_contact c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_sales_lead_connect_client_contact c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;


-- alter table company_sales_lead_service_type add column puser_id bigint;
-- alter table company_sales_lead_service_type add column pteam_id bigint;
update company_sales_lead_service_type c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system';
update company_sales_lead_service_type c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;


-- alter table company_service_type add column puser_id bigint;
-- alter table company_service_type add column pteam_id bigint;


-- job module
-- alter table job add column puser_id bigint;
-- alter table job add column pteam_id bigint;

update job c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system' and c.created_by != 'anonymousUser';
update job j
    left join company_sales_lead_administrator am on j.company_id=am.company_id and am.sales_lead_role=0
    set j.puser_id=am.user_id
where j.puser_id is null;
update job c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table job_additional_info add column puser_id bigint;
-- alter table job_additional_info add column pteam_id bigint;

-- alter table job_bool_string add column puser_id bigint;
-- alter table job_bool_string add column pteam_id bigint;


alter table job_company_contact_relation add column puser_id bigint;
alter table job_company_contact_relation add column pteam_id bigint;
update job_company_contact_relation c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system' and c.created_by != 'anonymousUser';
update job_company_contact_relation c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table job_details_report add column puser_id bigint;
-- alter table job_details_report add column pteam_id bigint;


alter table job_ipg_relation add column puser_id bigint;
alter table job_ipg_relation add column pteam_id bigint;
update job_ipg_relation c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system' and c.created_by != 'anonymousUser';
update job_ipg_relation c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table job_job_function_relation add column puser_id bigint;
-- alter table job_job_function_relation add column pteam_id bigint;


alter table job_location add column puser_id bigint;
alter table job_location add column pteam_id bigint;
update job_location c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system' and c.created_by != 'anonymousUser';
update job_location c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table job_minimum_degree_relation add column puser_id bigint;
-- alter table job_minimum_degree_relation add column pteam_id bigint;

alter table job_note add column puser_id bigint;
alter table job_note add column pteam_id bigint;
update job_note c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system' and c.created_by != 'anonymousUser';
update job_note c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table job_preferred_degree_relation add column puser_id bigint;
-- alter table job_preferred_degree_relation add column pteam_id bigint;

-- alter table job_preferred_languages_relation add column puser_id bigint;
-- alter table job_preferred_languages_relation add column pteam_id bigint;

-- alter table job_required_languages_relation add column puser_id bigint;
-- alter table job_required_languages_relation add column pteam_id bigint;

-- talent module
-- alter table talent add column puser_id bigint;
-- alter table talent add column pteam_id bigint;
update talent c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1) where c.created_by != 'system' and c.created_by != 'anonymousUser';
update talent c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table talent_additional_info add column puser_id bigint;
-- alter table talent_additional_info add column pteam_id bigint;

alter table talent_contact add column puser_id bigint;
alter table talent_contact add column pteam_id bigint;
update talent_contact c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_contact c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table talent_current_location add column puser_id bigint;
-- alter table talent_current_location add column pteam_id bigint;
update talent_current_location c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_current_location c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table talent_industry_relation add column puser_id bigint;
-- alter table talent_industry_relation add column pteam_id bigint;

-- alter table talent_job_function_relation add column puser_id bigint;
-- alter table talent_job_function_relation add column pteam_id bigint;

-- alter table talent_language_relation add column puser_id bigint;
-- alter table talent_language_relation add column pteam_id bigint;

alter table talent_note add column puser_id bigint;
alter table talent_note add column pteam_id bigint;
update talent_note c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_note c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_ownership add column puser_id bigint;
alter table talent_ownership add column pteam_id bigint;
update talent_ownership c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_ownership c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_resume_relation add column puser_id bigint;
alter table talent_resume_relation add column pteam_id bigint;
update talent_resume_relation c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_resume_relation c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;


-- application module
alter table talent_recruitment_process add column puser_id bigint;
alter table talent_recruitment_process add column pteam_id bigint;
update talent_recruitment_process c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;


alter table talent_recruitment_process_commission add column puser_id bigint;
alter table talent_recruitment_process_commission add column pteam_id bigint;
update talent_recruitment_process_commission c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_commission c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_eliminate add column puser_id bigint;
alter table talent_recruitment_process_eliminate add column pteam_id bigint;
update talent_recruitment_process_eliminate c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_eliminate c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_interview add column puser_id bigint;
alter table talent_recruitment_process_interview add column pteam_id bigint;
update talent_recruitment_process_interview c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_interview c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_ipg_agreed_pay_rate add column puser_id bigint;
alter table talent_recruitment_process_ipg_agreed_pay_rate add column pteam_id bigint;
update talent_recruitment_process_ipg_agreed_pay_rate c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_ipg_agreed_pay_rate c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_ipg_contract_fee_charge add column puser_id bigint;
alter table talent_recruitment_process_ipg_contract_fee_charge add column pteam_id bigint;
update talent_recruitment_process_ipg_contract_fee_charge c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_ipg_contract_fee_charge c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_ipg_fte_salary_package add column puser_id bigint;
alter table talent_recruitment_process_ipg_fte_salary_package add column pteam_id bigint;
update talent_recruitment_process_ipg_fte_salary_package c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_ipg_fte_salary_package c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_ipg_offer_accept add column puser_id bigint;
alter table talent_recruitment_process_ipg_offer_accept add column pteam_id bigint;
update talent_recruitment_process_ipg_offer_accept c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_ipg_offer_accept c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

-- alter table talent_recruitment_process_ipg_offer_letter_cost_rate add column puser_id bigint;
-- alter table talent_recruitment_process_ipg_offer_letter_cost_rate add column pteam_id bigint;

alter table talent_recruitment_process_kpi_user add column puser_id bigint;
alter table talent_recruitment_process_kpi_user add column pteam_id bigint;
update talent_recruitment_process_kpi_user c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_kpi_user c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_node add column puser_id bigint;
alter table talent_recruitment_process_node add column pteam_id bigint;
update talent_recruitment_process_node c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_node c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_offer add column puser_id bigint;
alter table talent_recruitment_process_offer add column pteam_id bigint;
update talent_recruitment_process_offer c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_offer c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_offer_fee_charge add column puser_id bigint;
alter table talent_recruitment_process_offer_fee_charge add column pteam_id bigint;
update talent_recruitment_process_offer_fee_charge c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_offer_fee_charge c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_offer_salary add column puser_id bigint;
alter table talent_recruitment_process_offer_salary add column pteam_id bigint;
update talent_recruitment_process_offer_salary c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_offer_salary c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_offer_salary_package add column puser_id bigint;
alter table talent_recruitment_process_offer_salary_package add column pteam_id bigint;
update talent_recruitment_process_offer_salary_package c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_offer_salary_package c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_onboard add column puser_id bigint;
alter table talent_recruitment_process_onboard add column pteam_id bigint;
update talent_recruitment_process_onboard c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_onboard c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_onboard_client_info add column puser_id bigint;
alter table talent_recruitment_process_onboard_client_info add column pteam_id bigint;
update talent_recruitment_process_onboard_client_info c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_onboard_client_info c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_onboard_date add column puser_id bigint;
alter table talent_recruitment_process_onboard_date add column pteam_id bigint;
update talent_recruitment_process_onboard_date c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_onboard_date c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_submit_to_client add column puser_id bigint;
alter table talent_recruitment_process_submit_to_client add column pteam_id bigint;
update talent_recruitment_process_submit_to_client c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_submit_to_client c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table talent_recruitment_process_submit_to_job add column puser_id bigint;
alter table talent_recruitment_process_submit_to_job add column pteam_id bigint;
update talent_recruitment_process_submit_to_job c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update talent_recruitment_process_submit_to_job c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;


-- finance module
alter table start add column puser_id bigint;
alter table start add column pteam_id bigint;
update start c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table start_address add column puser_id bigint;
alter table start_address add column pteam_id bigint;
update start_address c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start_address c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table start_client_info add column puser_id bigint;
alter table start_client_info add column pteam_id bigint;
update start_client_info c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start_client_info c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table start_commission add column puser_id bigint;
alter table start_commission add column pteam_id bigint;
update start_commission c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start_commission c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table start_contract_rate add column puser_id bigint;
alter table start_contract_rate add column pteam_id bigint;
update start_contract_rate c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start_contract_rate c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table start_failed_warranty add column puser_id bigint;
alter table start_failed_warranty add column pteam_id bigint;
update start_failed_warranty c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start_failed_warranty c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table start_fte_rate add column puser_id bigint;
alter table start_fte_rate add column pteam_id bigint;
update start_fte_rate c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start_fte_rate c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table start_fte_salary_package add column puser_id bigint;
alter table start_fte_salary_package add column pteam_id bigint;
update start_fte_salary_package c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start_fte_salary_package c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table start_rate_change add column puser_id bigint;
alter table start_rate_change add column pteam_id bigint;
update start_rate_change c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start_rate_change c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table start_termination add column puser_id bigint;
alter table start_termination add column pteam_id bigint;
update start_termination c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update start_termination c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;


alter table invoice add column puser_id bigint;
alter table invoice add column pteam_id bigint;
update invoice c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update invoice c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table invoice_activity add column puser_id bigint;
alter table invoice_activity add column pteam_id bigint;
update invoice_activity c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update invoice_activity c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table invoice_client_credit add column puser_id bigint;
alter table invoice_client_credit add column pteam_id bigint;
update invoice_client_credit c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update invoice_client_credit c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;

alter table invoice_payment_record add column puser_id bigint;
alter table invoice_payment_record add column pteam_id bigint;
update invoice_payment_record c set c.puser_id=SUBSTRING_INDEX(SUBSTRING_INDEX(c.created_by, ',', 1), ' ', -1)
where created_by like '%,%';
update invoice_payment_record c
    left join permission_user_team ut on ut.user_id=c.puser_id
    set c.pteam_id=ut.team_id
where ut.is_primary=1;
