# 此脚本为mvn打包项目，push打包后文件到docker hub，并生成待部署镜像自动部署到staging环境对应服务的脚本
# 使用方法：deploy-to-staging1.sh $DOCKER_HUB_USERNAME $DOCKER_HUB_TOKEN $RANCHER_ACCESS_KEY $RANCHER_SECRET_KEY $COMMIT_MESSAGE $FILES_CHANGED_ARRAY
# 传入参数：
# - $DOCKER_HUB_USERNAME（第一个参数）：为docker hub对应账号登录username
# - $DOCKER_HUB_TOKEN   （第二个参数）：为docker hub对应账号登录password
# - $RANCHER_ACCESS_KEY （第三个参数）：为staging rancher具有部署权限的API ACCESS_KEY
# - $RANCHER_SECRET_KEY （第四个参数）：为staging rancher具有部署权限的API SECRET_KEY
# - $COMMIT_MESSAGE     （第五个参数）：为对应commit的message信息
# - $FILES_CHANGED_ARRAY（第六个参数）：基于git commits的文件改动
# 不要修改这个变量
rancher_url_base="http://rancher.hitalent.us:54321/v2-beta/projects/1a5/services/"
declare -A service_mapping
service_mapping["admin-service"]="1s389"
service_mapping["application-service"]="1s496"
service_mapping["authority-service"]="1s388"
service_mapping["company-service"]="1s391"
service_mapping["finance-service"]="1s475"
service_mapping["gateway-service"]="1s387"
#service_mapping["initiation-service"]="1s484"
service_mapping["job-service"]="1s392"
#service_mapping["location-service"]="1s603"
#service_mapping["parser-service"]="1s406"
service_mapping["report-service"]="1s497"
#service_mapping["store-service"]="1s405"
service_mapping["talent-service"]="1s393"
service_mapping["user-service"]="1s390"
#service_mapping["email-service"]="1s493"
service_mapping["jobdiva-service"]="1s501"
service_mapping["canal-service"]="1s611"
service_mapping["management-service"]="1s561"
service_mapping["common-service"]="1s618"
# 不要修改这个变量（被依赖的项目先进行build）
pre_special_projects=("user-service" "company-service" "job-service")
# 不要修改这个变量（依赖其他项目最多的项目最后build）
post_special_projects=("application-service" "finance-service" "common-service")
skipped_projects=("initiation-service" "email-service" "store-service" "parser-service" "location-service")
# 以下代码都不需要修改
version="v$(date +'%y%m%d%H%M')"
docker_hub_username=$1
docker_hub_password=$2
rancher_username=$3
rancher_password=$4
commit_msg=$5
files_changed=$6
possible_projects=()
deploy_projects=()
common_existed=false
skip_build=false

function deploy() {
  project=$1
  echo "-------start processing ${project}--------------"
  cd $project
  mvn clean package -DskipTests spring-boot:repackage
  if [[ (${deploy_projects[*]} =~ ${project} ) ]]; then
     tag="${docker_hub_username}/apn-v3-staging-${project}:${version}"
     echo "-------start docker section for ${project}--------------"
     docker build -t ${tag} .
     docker login -u ${docker_hub_username} -p "${docker_hub_password}"
     docker push ${tag}
     echo "-------complete docker section for ${project}--------------"
     for key in ${!service_mapping[@]}; do
       if [[ ( ${key} == ${project} ) ]]; then
         deploy_service=${service_mapping[${key}]}
         deploy_url_base=${rancher_url_base}${deploy_service}
         echo "-------prepare for deploying ${key}--------------"
         healthState=$(curl -u "${rancher_username}:${rancher_password}" \
         -X GET \
         -H "Accept: application/json" \
         -H "Content-Type: application/json" \
         "${deploy_url_base}" \
         | jq '.healthState')
         if [[ ( ${healthState} == "unhealthy" ) ]]; then
            echo "-------current health state of service ${key}: ${healthState}--------------"
            curl -u "${rancher_username}:${rancher_password}" \
            -X POST \
            "${deploy_url_base}/?action=rollback"
            sleep 20
         fi
         state=$(curl -u "${rancher_username}:${rancher_password}" \
         -X GET \
         -H "Accept: application/json" \
         -H "Content-Type: application/json" \
         "${deploy_url_base}" \
         | jq '.state')
         echo "-------current state of ${key}: ${state}--------------"
         echo "-------start deploying ${key}--------------"
         launchConfig=$(curl -u "${rancher_username}:${rancher_password}" \
         -X GET \
         -H "Accept: application/json" \
         -H "Content-Type: application/json" \
         "${deploy_url_base}" \
         | jq '.launchConfig')
         old_image=$(echo "$launchConfig" | jq '.imageUuid')
         new_image="\"docker:${tag}\""
         launchConfig=${launchConfig/$old_image/$new_image}
         data="{\"inServiceStrategy\":{\"batchSize\":1,\"intervalMillis\":2000,\"startFirst\":true,\"launchConfig\":${launchConfig},\"secondaryLaunchConfigs\":[]}}"
         data=$(echo "$data" | jq -c .)
         curl -u "${rancher_username}:${rancher_password}" \
         -X POST \
         -H "Accept: application/json" \
         -H "Content-Type: application/json" \
         "${deploy_url_base}/?action=upgrade" \
         -d ${data}
         echo "-------complete image updating for ${key}--------------"
         sleep 10
         state=$(curl -u "${rancher_username}:${rancher_password}" \
         -X GET \
         -H "Accept: application/json" \
         -H "Content-Type: application/json" \
         "${deploy_url_base}" \
         | jq '.state')
         if [[ ( ${state} != "upgraded" ) ]]; then
           sleep 10
           state=$(curl -u "${rancher_username}:${rancher_password}" \
           -X GET \
           -H "Accept: application/json" \
           -H "Content-Type: application/json" \
           "${deploy_url_base}" \
           | jq '.state')
           if [[ ( ${state} != "upgraded" ) ]]; then
             sleep 10
           fi
         fi
         echo "-------try to finish deployment for ${key}--------------"
         curl -u "${rancher_username}:${rancher_password}" \
         -X POST \
         "${deploy_url_base}/?action=finishupgrade"
         echo "-------complete deploying ${key}--------------"
       fi
    done
  fi
  cd ../
  echo "-------complete processing ${project}--------------"
}

echo "-------Commit message: ${commit_msg}--------------"
# 检查commit_msg start with "Merge remote-tracking branch*"或"Merge pull request*"
if [[ ($commit_msg == Merge*) ]] ; then
    skip_build=true
    echo "-------This commit will not be built automatically--------------"
fi

if [[ (false == ${skip_build} ) ]]; then
  # 获取所有服务
  target_dir=$PWD
  for entry in $target_dir/*;
  do
    service_name="$(basename $entry)"
    if [[ ($service_name == *-service) ]]; then
      possible_projects+=(${service_name})
    fi
  done
  # 检查更新的服务
  echo "-------File changes: ${files_changed}--------------"
  for file_changed in ${files_changed}; do
    echo "-------File Changed: ${file_changed}--------------"
    service_changed="$( cut -d '/' -f 1 <<< "$file_changed" )"
    echo "-------Possible service Changed: ${service_changed}--------------"
    if [[ ("common" == ${service_changed} ) ]]; then
      common_existed=true
      echo "-------${service_changed} got changed--------------"
      break;
    elif [[ (${possible_projects[*]} =~ ${service_changed} ) ]]; then
      deploy_projects+=(${service_changed})
      echo "-------${service_changed} got changed--------------"
    fi
  done
  # 检查是否基础服务common被更新
  if [[ (true == ${common_existed} ) ]]; then
    deploy_projects=("${possible_projects[@]}")
    echo "-------${deploy_projects} are going to be deployed--------------"
  fi
  # 开始打包与部署
#  cd common
#  mvn clean install -DskipTests
#  cd ../
  mvn clean install -DskipTests
  for pre_special_project in ${pre_special_projects[@]};
  do
    deploy "${pre_special_project}"
#     echo "-------start processing ${pre_special_project}--------------"
#     cd $pre_special_project
#     mvn clean package -DskipTests spring-boot:repackage
#     if [[ (${deploy_projects[*]} =~ ${pre_special_project} ) ]]; then
#       tag="${docker_hub_username}/apn-v3-staging-${pre_special_project}:${version}"
#       echo "-------start docker section for ${pre_special_project}--------------"
#       docker build -t ${tag} .
#       docker login -u ${docker_hub_username} -p "${docker_hub_password}"
#       docker push ${tag}
#       echo "-------complete docker section for ${pre_special_project}--------------"
#       for key in ${!service_mapping[@]}; do
#          if [[ ( ${key} == ${pre_special_project} ) ]]; then
#            deploy_service=${service_mapping[${key}]}
#            deploy_url_base=${rancher_url_base}${deploy_service}
#            echo "-------prepare for deploying ${key}--------------"
#            healthState=$(curl -u "${rancher_username}:${rancher_password}" \
#            -X GET \
#            -H "Accept: application/json" \
#            -H "Content-Type: application/json" \
#            "${deploy_url_base}" \
#            | jq '.healthState')
#            if [[ ( ${healthState} == "unhealthy" ) ]]; then
#              echo "-------current health state of service ${key}: ${healthState}--------------"
#              curl -u "${rancher_username}:${rancher_password}" \
#              -X POST \
#              "${deploy_url_base}/?action=rollback"
#              sleep 20
#            fi
#            state=$(curl -u "${rancher_username}:${rancher_password}" \
#            -X GET \
#            -H "Accept: application/json" \
#            -H "Content-Type: application/json" \
#            "${deploy_url_base}" \
#            | jq '.state')
#            echo "-------current state of ${key}: ${state}--------------"
#            echo "-------start deploying ${key}--------------"
#            launchConfig=$(curl -u "${rancher_username}:${rancher_password}" \
#            -X GET \
#            -H "Accept: application/json" \
#            -H "Content-Type: application/json" \
#            "${deploy_url_base}" \
#            | jq '.launchConfig')
#            old_image=$(echo "$launchConfig" | jq '.imageUuid')
#            new_image="\"docker:${tag}\""
#            launchConfig=${launchConfig/$old_image/$new_image}
#            data="{\"inServiceStrategy\":{\"batchSize\":1,\"intervalMillis\":2000,\"startFirst\":true,\"launchConfig\":${launchConfig},\"secondaryLaunchConfigs\":[]}}"
#            data=$(echo "$data" | jq -c .)
#            curl -u "${rancher_username}:${rancher_password}" \
#            -X POST \
#            -H "Accept: application/json" \
#            -H "Content-Type: application/json" \
#            "${deploy_url_base}/?action=upgrade" \
#            -d ${data}
#            echo "-------complete image updating for ${key}--------------"
#            sleep 10
#            state=$(curl -u "${rancher_username}:${rancher_password}" \
#            -X GET \
#            -H "Accept: application/json" \
#            -H "Content-Type: application/json" \
#            "${deploy_url_base}" \
#            | jq '.state')
#            if [[ ( ${state} != "upgraded" ) ]]; then
#              sleep 10
#              state=$(curl -u "${rancher_username}:${rancher_password}" \
#              -X GET \
#              -H "Accept: application/json" \
#              -H "Content-Type: application/json" \
#              "${deploy_url_base}" \
#              | jq '.state')
#              if [[ ( ${state} != "upgraded" ) ]]; then
#                sleep 10
#              fi
#            fi
#            echo "-------try to finish deployment for ${key}--------------"
#            curl -u "${rancher_username}:${rancher_password}" \
#            -X POST \
#            "${deploy_url_base}/?action=finishupgrade"
#            echo "-------complete deploying ${key}--------------"
#          fi
#      done
#     fi
#     cd ../
#     echo "-------complete processing ${pre_special_project}--------------"
  done
  for entry in $target_dir/*;
  do
    service_name="$(basename $entry)"
    if [[ ( ${possible_projects[*]} =~ ${service_name} ) && (! ${skipped_projects[*]} =~ ${service_name} )  && (! ${pre_special_projects[*]} =~ ${service_name} ) && (! ${post_special_projects[*]} =~ ${service_name} ) ]]; then
      deploy "${service_name}"
#      echo "-------start processing ${service_name}--------------"
#      cd $service_name
#      mvn clean package -DskipTests spring-boot:repackage
#      if [[ (${deploy_projects[*]} =~ ${service_name} ) ]]; then
#        tag="${docker_hub_username}/apn-v3-staging-${service_name}:${version}"
#        echo "-------start docker section for ${service_name}--------------"
#        docker build -t ${tag} .
#        docker login -u ${docker_hub_username} -p "${docker_hub_password}"
#        docker push ${tag}
#        echo "-------complete docker section for ${service_name}--------------"
#        for key in ${!service_mapping[@]}; do
#           if [[ ( ${key} == ${service_name} ) ]]; then
#             deploy_service=${service_mapping[${key}]}
#             deploy_url_base=${rancher_url_base}${deploy_service}
#             echo "-------prepare for deploying ${key}--------------"
#             healthState=$(curl -u "${rancher_username}:${rancher_password}" \
#             -X GET \
#             -H "Accept: application/json" \
#             -H "Content-Type: application/json" \
#             "${deploy_url_base}" \
#             | jq '.healthState')
#             if [[ ( ${healthState} == "unhealthy" ) ]]; then
#                echo "-------current health state of service ${key}: ${healthState}--------------"
#                curl -u "${rancher_username}:${rancher_password}" \
#                -X POST \
#                "${deploy_url_base}/?action=rollback"
#                sleep 20
#             fi
#             state=$(curl -u "${rancher_username}:${rancher_password}" \
#             -X GET \
#             -H "Accept: application/json" \
#             -H "Content-Type: application/json" \
#             "${deploy_url_base}" \
#             | jq '.state')
#             echo "-------current state of ${key}: ${state}--------------"
#             echo "-------start deploying ${key}--------------"
#             launchConfig=$(curl -u "${rancher_username}:${rancher_password}" \
#             -X GET \
#             -H "Accept: application/json" \
#             -H "Content-Type: application/json" \
#             "${deploy_url_base}" \
#             | jq '.launchConfig')
#             old_image=$(echo "$launchConfig" | jq '.imageUuid')
#             new_image="\"docker:${tag}\""
#             launchConfig=${launchConfig/$old_image/$new_image}
#             data="{\"inServiceStrategy\":{\"batchSize\":1,\"intervalMillis\":2000,\"startFirst\":true,\"launchConfig\":${launchConfig},\"secondaryLaunchConfigs\":[]}}"
#             data=$(echo "$data" | jq -c .)
#             curl -u "${rancher_username}:${rancher_password}" \
#             -X POST \
#             -H "Accept: application/json" \
#             -H "Content-Type: application/json" \
#             "${deploy_url_base}/?action=upgrade" \
#             -d ${data}
#             echo "-------complete image updating for ${key}--------------"
#             sleep 10
#             state=$(curl -u "${rancher_username}:${rancher_password}" \
#             -X GET \
#             -H "Accept: application/json" \
#             -H "Content-Type: application/json" \
#             "${deploy_url_base}" \
#             | jq '.state')
#             if [[ ( ${state} != "upgraded" ) ]]; then
#               sleep 10
#               state=$(curl -u "${rancher_username}:${rancher_password}" \
#               -X GET \
#               -H "Accept: application/json" \
#               -H "Content-Type: application/json" \
#               "${deploy_url_base}" \
#               | jq '.state')
#               if [[ ( ${state} != "upgraded" ) ]]; then
#                 sleep 10
#               fi
#             fi
#             echo "-------try to finish deployment for ${key}--------------"
#             curl -u "${rancher_username}:${rancher_password}" \
#             -X POST \
#             "${deploy_url_base}/?action=finishupgrade"
#             echo "-------complete deploying ${key}--------------"
#           fi
#        done
#      fi
#      cd ../
#      echo "-------complete processing ${service_name}--------------"
    fi
  done
  for post_special_project in ${post_special_projects[@]};
  do
    deploy "${post_special_project}"
  done
fi