#!/opt/homebrew/bin/bash
# 不要修改这个变量
rancher_url_base="https://r2-singapore.hitalentech.com:8443/k8s/clusters/c-mh95k/apis/apps/v1/namespaces/default/deployments/"
declare -A service_mapping
service_mapping["admin-service"]="apnv3-admin"
service_mapping["application-service"]="apnv3-application"
service_mapping["authority-service"]="apnv3-authority"
service_mapping["company-service"]="apnv3-company"
service_mapping["finance-service"]="apnv3-finance"
service_mapping["gateway-service"]="apnv3-gateway"
service_mapping["initiation-service"]="apnv3-initiation"
service_mapping["job-service"]="apnv3-job"
service_mapping["location-service"]="apnv3-location"
service_mapping["parser-service"]="apnv3-parser-srv"
service_mapping["report-service"]="apnv3-report"
service_mapping["store-service"]="apnv3-store"
service_mapping["talent-service"]="apnv3-talent"
service_mapping["user-service"]="apnv3-user"
service_mapping["email-service"]="apnv3-email"
service_mapping["jobdiva-service"]="apnv3-jobdiva"
service_mapping["canal-service"]="apnv3-canal"
service_mapping["management-service"]="apnv3-management"

# 不要修改这个变量（被依赖的项目先进行build）
pre_special_projects=("initiation-service" "user-service" "email-service" "company-service" "job-service")
# 不要修改这个变量（依赖其他项目最多的项目最后build）
post_special_projects=("application-service" "finance-service")
possible_projects=()
# 以下变量需要手动修改成要替换的值
#rancher_access_token=""
#deploy_projects=("initiation-service" "user-service" "email-service" "company-service" "job-service" "admin-service" "authority-service" "location-service" "parser-service" "report-service" "store-service" "talent-service" "jobdiva-service" "canal-service" "application-service" "finance-service")
#version="v2304060526"
rancher_access_token=${替换此变量为rancher-access-token}
deploy_projects=(${替换此变量为要部署的服务名数组})
version=${替换此变量为要部署的image对应的v版本号,如:v2304060526}

data_template="{\"spec\": { \"template\": { \"metadata\": { \"annotations\": { \"kubectl.kubernetes.io/restartedAt\": \"${now}\" }}, \"spec\": { \"\$setElementOrder/containers\": [{ \"name\": \"\$service_name\" }], \"containers\": [ { \"image\": \"\$image\", \"name\": \"\$service_name\" } ] } } } }"
# 获取所有服务
target_dir=$PWD
for entry in $target_dir/*;
do
  service_name="$(basename $entry)"
  if [[ ($service_name == *-service) ]]; then
    possible_projects+=(${service_name})
  fi
done
for pre_special_project in ${pre_special_projects[@]};
do
   echo "-------start processing ${pre_special_project}--------------"
   if [[ (${deploy_projects[*]} =~ ${pre_special_project} ) ]]; then
     tag="${docker_hub_username}/apn-v3-staging2-${pre_special_project}:${version}"
     echo "-------tag ${tag}--------------"
     for key in ${!service_mapping[@]}; do
        if [[ ( ${key} == ${pre_special_project} ) ]]; then
          deploy_service=${service_mapping[${key}]}
          deploy_url_base=${rancher_url_base}${deploy_service}
          data=${data_template/"\$image"/$tag}
          data=${data//"\$service_name"/$deploy_service}
          echo "-------prepare for deploying ${key}--------------"
          curl --location --request GET "${deploy_url_base}" \
          --header "Accept: application/json" \
          --header "Content-Type: application/json" \
          --header "Authorization: Bearer ${rancher_access_token}"
          echo "-------start deploying ${key}--------------"
          curl --location --request PATCH "${deploy_url_base}" \
          --header "Content-Type: application/strategic-merge-patch+json" \
          --header "Authorization: Bearer ${rancher_access_token}" \
          --data-raw "${data}"
          echo "-------complete deploying ${key}--------------"
        fi
    done
   fi
   echo "-------complete processing ${pre_special_project}--------------"
done
for entry in $target_dir/*;
do
  service_name="$(basename $entry)"
  if [[ ( ${possible_projects[*]} =~ ${service_name} ) && (! ${pre_special_projects[*]} =~ ${service_name} ) && (! ${post_special_projects[*]} =~ ${service_name} ) ]]; then
    echo "-------start processing ${service_name}--------------"
    if [[ (${deploy_projects[*]} =~ ${service_name} ) ]]; then
      tag="${docker_hub_username}/apn-v3-staging2-${service_name}:${version}"
      for key in ${!service_mapping[@]}; do
         if [[ ( ${key} == ${service_name} ) ]]; then
           deploy_service=${service_mapping[${key}]}
           deploy_url_base=${rancher_url_base}${deploy_service}
           data=${data_template/"\$image"/$tag}
           data=${data//"\$service_name"/$deploy_service}
           echo "-------prepare for deploying ${key}--------------"
           curl --location --request GET "${deploy_url_base}" \
           --header "Accept: application/json" \
           --header "Content-Type: application/json" \
           --header "Authorization: Bearer ${rancher_access_token}"
           echo "-------start deploying ${key}--------------"
           curl --location --request PATCH "${deploy_url_base}" \
           --header "Content-Type: application/strategic-merge-patch+json" \
           --header "Authorization: Bearer ${rancher_access_token}" \
           --data-raw "${data}"
           echo "-------complete deploying ${key}--------------"
         fi
      done
    fi
    echo "-------complete processing ${service_name}--------------"
  fi
done
for post_special_project in ${post_special_projects[@]};
do
   echo "-------start processing ${post_special_project}--------------"
   if [[ (${deploy_projects[*]} =~ ${post_special_project} ) ]]; then
     tag="${docker_hub_username}/apn-v3-staging2-${post_special_project}:${version}"
     for key in ${!service_mapping[@]}; do
       if [[ ( ${key} == ${post_special_project} ) ]]; then
         deploy_service=${service_mapping[${key}]}
         deploy_url_base=${rancher_url_base}${deploy_service}
         data=${data_template/"\$image"/$tag}
         data=${data//"\$service_name"/$deploy_service}
         echo "-------prepare for deploying ${key}--------------"
         curl --location --request GET "${deploy_url_base}" \
         --header "Accept: application/json" \
         --header "Content-Type: application/json" \
         --header "Authorization: Bearer ${rancher_access_token}"
         echo "-------start deploying ${key}--------------"
         curl --location --request PATCH "${deploy_url_base}" \
         --header "Content-Type: application/strategic-merge-patch+json" \
         --header "Authorization: Bearer ${rancher_access_token}" \
         --data-raw "${data}"
         echo "-------complete deploying ${key}--------------"
       fi
    done
   fi
   echo "-------complete processing ${post_special_project}--------------"
done