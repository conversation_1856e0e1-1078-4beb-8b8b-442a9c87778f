# 待移植v2数据库(源数据)相关信息如下:
v2_database_host="127.0.0.1"
v2_database_port=3306
v2_database_name="apn"
v2_mysql_username="root"
v2_mysql_password=""
# 移植过程中导出数据sql存放路径
mysql_dump_directory="./sql/export"
# 待导入v3数据库(目标数据)相关信息如下:
v3_database_host="127.0.0.1"
v3_database_port=3306
v3_database_name="apnv3"
v3_mysql_username="root"
v3_mysql_password=""
# 待移植v3 staging数据库(源数据)相关信息如下:
v3_staging_database_host="127.0.0.1"
v3_staging_database_port=13306
v3_staging_database_name="apnv3"
v3_staging_mysql_username="root"
v3_staging_mysql_password=""
# redis相关信息如下:
redis_host="127.0.0.1"
redis_port=6379
# s3相关信息如下(供job&talent etl使用):
s3_access_key=""
s3_secret_key=""
s3_url="https://s3-us-west-2.amazonaws.com"
s3_bucket_name="resume-staging"
s3_region="us-west-2"
# aliyun相关信息如下(供job&talent etl使用):
aliyun_access_key=""
aliyun_secret_key=""
aliyun_url="aliyuncs.com"
aliyun_resume_bucket_name="apn-cv-staging"
aliyun_end_point="oss-cn-shenzhen"
# docker相关镜像信息如下:
company_etl_docker_image="minghealtomni/apn-etl:company-etl-v2211161648"
job_talent_etl_docker_image="minghealtomni/apn-etl:job-talent-etl-v2211210927"
application_etl_docker_image="minghealtomni/apn-etl:application-etl-v2212021802"