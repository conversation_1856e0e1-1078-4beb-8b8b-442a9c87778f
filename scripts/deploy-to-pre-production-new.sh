# 此脚本为mvn打包项目，push打包后文件到docker hub，并生成待部署镜像自动部署到pre-production环境对应服务的脚本
# 使用方法：deploy-to-pre-production.sh $DOCKER_HUB_USERNAME $DOCKER_HUB_TOKEN $RANCHER_ACCESS_TOKEN $COMMIT_MESSAGE $FILES_CHANGED_ARRAY
# 传入参数：
# - $DOCKER_HUB_USERNAME （第一个参数）：为docker hub对应账号登录username
# - $DOCKER_HUB_TOKEN    （第二个参数）：为docker hub对应账号登录password
# - $RANCHER_ACCESS_TOKEN（第三个参数）：为production rancher具有部署权限的API ACCESS_TOKEN
# - $COMMIT_MESSAGE      （第四个参数）：为对应commit的message信息
# - $FILES_CHANGED_ARRAY （第五个参数）：基于git commits的文件改动
# 不要修改这个变量
rancher_url_base="https://rancher2.hitalentech.com:8443/k8s/clusters/c-jckqn/apis/apps/v1/namespaces/apnv3/deployments/"
declare -A service_mapping
service_mapping["admin-service"]="apnv3-admin"
service_mapping["application-service"]="apnv3-application"
service_mapping["authority-service"]="apnv3-authority"
service_mapping["company-service"]="apnv3-company"
service_mapping["finance-service"]="apnv3-finance"
service_mapping["gateway-service"]="apnv3-gateway"
service_mapping["job-service"]="apnv3-job"
service_mapping["report-service"]="apnv3-report"
service_mapping["talent-service"]="apnv3-talent"
service_mapping["user-service"]="apnv3-user"
service_mapping["jobdiva-service"]="apnv3-jobdiva"
service_mapping["canal-service"]="apnv3-canal"
service_mapping["management-service"]="apnv3-management"
service_mapping["common-service"]="apnv3-common"
# 不要修改这个变量（被依赖的项目先进行build）
pre_special_projects=("user-service" "company-service" "job-service" "management-service")
# 不要修改这个变量（依赖其他项目最多的项目最后build）
post_special_projects=("application-service" "finance-service" "common-service")
skipped_projects=("initiation-service" "email-service" "store-service" "parser-service" "location-service")
# 以下代码都不需要修改
version="v$(date +'%y%m%d%H%M')"
docker_hub_username=$1
docker_hub_password=$2
rancher_access_token=$3
commit_msg=$4
files_changed=$5
possible_projects=()
deploy_projects=()
common_existed=false
skip_build=true
now="$(date '+%Y-%m-%d %H:%M:%S')"
data_template="{\"spec\": { \"template\": { \"metadata\": { \"annotations\": { \"kubectl.kubernetes.io/restartedAt\": \"${now}\" }}, \"spec\": { \"\$setElementOrder/containers\": [{ \"name\": \"\$service_name\" }], \"containers\": [ { \"image\": \"\$image\", \"name\": \"\$service_name\" } ] } } } }"
prefix_docker_image="apn-v3-prod"
LARK_URL="https://open.larksuite.com/open-apis/bot/v2/hook/01e7cd8b-6452-4967-9f1c-33c55c1ee8fb"

send_lark(){
  msg="$1"
  echo "Send lark notification: ${msg}"
  curl -X POST -H "Content-Type: application/json" -d "{\"msg_type\":\"text\",\"content\":{\"text\":\"$msg\"}}" "${LARK_URL}"
}

build_and_push_docker() {
    local service=$1
    echo "Building Docker Image for ${service}"
    send_lark "start building ${service}"
    local tag="${docker_hub_username}/${prefix_docker_image}-${service}:${version}"
    docker build -t "${tag}" .
    echo "Pushing Docker Image for ${service}"
    docker push "${tag}"
    echo "-------complete docker section for ${service}--------------"
}

deploy_service() {
    local service=$1
    echo "Deploying ${service}"
    send_lark "start deploying ${service}"
    local tag="${docker_hub_username}/${prefix_docker_image}-${service}:${version}"
    local deploy_service=${service_mapping[${service}]}
    local deploy_url_base=${rancher_url_base}${deploy_service}
    local data=${data_template/"\$image"/$tag}
    data=${data//"\$service_name"/$deploy_service}
    curl --location --request PATCH "${deploy_url_base}" \
         --header "Content-Type: application/strategic-merge-patch+json" \
         --header "Authorization: Bearer ${rancher_access_token}" \
         --data-raw "${data}"
    echo "-------complete deploying ${service}--------------"
}


# 登录 Docker
docker login -u "${docker_hub_username}" -p "${docker_hub_password}"


echo "-------Commit message: ${commit_msg}--------------"
# 检查commit_msg start with "Merge remote-tracking branch*"或"Merge pull request*"
if [[ ($commit_msg == Merge*) ]] ; then
    skip_build=false
    echo "-------This commit will be built automatically--------------"
fi
if [[ (false == ${skip_build} ) ]]; then
  # 获取所有服务
  target_dir=$PWD
  for entry in $target_dir/*;
  do
    service_name="$(basename $entry)"
    if [[ ($service_name == *-service) ]]; then
      possible_projects+=(${service_name})
    fi
  done
  # 检查更新的服务
  echo "-------File changes: ${files_changed}--------------"
  for file_changed in ${files_changed}; do
    echo "-------File Changed: ${file_changed}--------------"
    service_changed="$( cut -d '/' -f 1 <<< "$file_changed" )"
    echo "-------Possible service Changed: ${service_changed}--------------"
    if [[ ("common" == ${service_changed} ) ]]; then
        common_existed=true
        echo "-------${service_changed} got changed--------------"
        break
    elif [[ (${possible_projects[*]} =~ ${service_changed} ) && (! ${deploy_projects[*]} =~ ${service_changed} ) ]]; then
        deploy_projects+=(${service_changed})
        echo "-------${service_changed} got changed--------------"
    fi
  done
  # 检查是否基础服务common被更新
  if [[ (true == ${common_existed} ) ]]; then
    deploy_projects=("${possible_projects[@]}")
  fi

  service_names=""
  for each in "${deploy_projects[@]}"
  do
    service_names+=" $each"
  done
  echo "-------${service_names} will be deployed--------------"

  if [ -z "$service_names" ]
  then
        echo "\$service_names is empty"
  else
        send_lark "${service_names} will be deployed"
  fi

  # 确保在出错时停止脚本
  set -e

  # start building
  mvn clean install
  if [ $? -ne 0 ]; then
      echo "err"
      exit 0
  fi
#  cd common
#  mvn clean install
#  cd ../
  for pre_special_project in ${pre_special_projects[@]};
  do
     echo "-------start building ${pre_special_project}--------------"
     cd $pre_special_project
     mvn clean package spring-boot:repackage
     if [[ (${deploy_projects[*]} =~ ${pre_special_project} ) ]]; then
       build_and_push_docker "${pre_special_project}"
#       for key in ${!service_mapping[@]}; do
#          if [[ ( ${key} == ${pre_special_project} ) ]]; then
#            deploy_service ${key}
#          fi
#       done
     fi
     cd ../
     echo "-------complete building ${pre_special_project}--------------"
  done
  for entry in $target_dir/*;
  do
    service_name="$(basename $entry)"
    if [[ ( ${possible_projects[*]} =~ ${service_name} ) && (! ${skipped_projects[*]} =~ ${service_name} )  && (! ${pre_special_projects[*]} =~ ${service_name} ) && (! ${post_special_projects[*]} =~ ${service_name} ) ]]; then
      echo "-------start building ${service_name}--------------"
      cd $service_name
      mvn clean package spring-boot:repackage
      if [[ (${deploy_projects[*]} =~ ${service_name} ) ]]; then
        build_and_push_docker "${service_name}"
#        for key in ${!service_mapping[@]}; do
#           if [[ ( ${key} == ${service_name} ) ]]; then
#              deploy_service ${key}
#           fi
#        done
      fi
      cd ../
      echo "-------complete building ${service_name}--------------"
    fi
  done
  for post_special_project in ${post_special_projects[@]};
  do
     echo "-------start building ${post_special_project}--------------"
     cd $post_special_project
     mvn clean package spring-boot:repackage
     if [[ (${deploy_projects[*]} =~ ${post_special_project} ) ]]; then
       build_and_push_docker "${post_special_project}"
#       for key in ${!service_mapping[@]}; do
#         if [[ ( ${key} == ${post_special_project} ) ]]; then
#            deploy_service ${key}
#         fi
#      done
     fi
     cd ../
     echo "-------complete building ${post_special_project}--------------"
  done

  # start deploying
#  for pre_special_project in ${pre_special_projects[@]};
#  do
#    if [[ (${deploy_projects[*]} =~ ${pre_special_project} ) ]]; then
#      echo "-------start deploying ${pre_special_project}--------------"
#      deploy_service ${pre_special_project}
#      echo "-------complete deploying ${pre_special_project}--------------"
#      sleep 30
#    fi
#  done

  for service_name in ${deploy_projects[@]};
  do
    echo "-------start deploying ${service_name}--------------"
    deploy_service ${service_name}
    echo "-------complete deploying ${service_name}--------------"
    sleep 10
  done

  if [ -z "$service_names" ]
  then
        echo "\$service_names is empty"
  else
        send_lark "Deployment is done. Waiting for starting services up."
  fi

#  for post_special_project in ${post_special_projects[@]};
#  do
#     if [[ (${deploy_projects[*]} =~ ${post_special_project} ) ]]; then
#       echo "-------start deploying ${post_special_project}--------------"
#       deploy_service ${post_special_project}
#       echo "-------complete deploying ${post_special_project}--------------"
#       sleep 30
#     fi
#  done
fi