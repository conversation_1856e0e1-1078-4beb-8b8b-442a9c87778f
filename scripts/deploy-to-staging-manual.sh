#!/opt/homebrew/bin/bash
# 不要修改这个变量
rancher_url_base="http://rancher.hitalent.us:54321/v2-beta/projects/1a5/services/"
declare -A service_mapping
service_mapping["admin-service"]="1s389"
service_mapping["application-service"]="1s496"
service_mapping["authority-service"]="1s388"
service_mapping["company-service"]="1s391"
service_mapping["finance-service"]="1s475"
service_mapping["gateway-service"]="1s387"
service_mapping["initiation-service"]="1s484"
service_mapping["job-service"]="1s392"
service_mapping["location-service"]="1s402"
service_mapping["parser-service"]="1s406"
service_mapping["report-service"]="1s497"
service_mapping["store-service"]="1s405"
service_mapping["talent-service"]="1s393"
service_mapping["user-service"]="1s390"
service_mapping["email-service"]="1s493"
service_mapping["jobdiva-service"]="1s501"
service_mapping["canal-service"]="1s520"
# 不要修改这个变量（被依赖的项目先进行build）
pre_special_projects=("initiation-service" "user-service" "email-service" "company-service" "job-service")
# 不要修改这个变量（依赖其他项目最多的项目最后build）
post_special_projects=("application-service" "finance-service")
possible_projects=()
# 以下变量需要手动修改成要替换的值
#rancher_access_token=""
#deploy_projects=("initiation-service" "user-service" "email-service" "company-service" "job-service" "admin-service" "authority-service" "location-service" "parser-service" "report-service" "store-service" "talent-service" "jobdiva-service" "canal-service" "application-service" "finance-service")
#version="v2304060526"
rancher_access_token=${替换此变量为rancher-access-token}
deploy_projects=(${替换此变量为要部署的服务名数组})
version=${替换此变量为要部署的image对应的v版本号,如:v2304060526}

# 获取所有服务
target_dir=$PWD
for entry in $target_dir/*;
do
  service_name="$(basename $entry)"
  if [[ ($service_name == *-service) ]]; then
    possible_projects+=(${service_name})
  fi
done
# 开始打包与部署
for pre_special_project in ${pre_special_projects[@]};
do
   echo "-------start processing ${pre_special_project}--------------"
   if [[ (${deploy_projects[*]} =~ ${pre_special_project} ) ]]; then
     tag="${docker_hub_username}/apn-v3-staging-${pre_special_project}:${version}"
     for key in ${!service_mapping[@]}; do
        if [[ ( ${key} == ${pre_special_project} ) ]]; then
          deploy_service=${service_mapping[${key}]}
          deploy_url_base=${rancher_url_base}${deploy_service}
          echo "-------start deploying ${key}--------------"
          launchConfig=$(curl -u "${rancher_username}:${rancher_password}" \
          -X GET \
          -H "Accept: application/json" \
          -H "Content-Type: application/json" \
          "${deploy_url_base}" \
          | jq '.launchConfig')
          old_image=$(echo "$launchConfig" | jq '.imageUuid')
          new_image="\"docker:${tag}\""
          launchConfig=${launchConfig/$old_image/$new_image}
          old_hostId=$(echo "$launchConfig" | jq '.requestedHostId')
          new_hostId="\"1h23\""
          launchConfig=${launchConfig/$old_hostId/$new_hostId}
          data="{\"inServiceStrategy\":{\"batchSize\":1,\"intervalMillis\":2000,\"startFirst\":true,\"launchConfig\":${launchConfig},\"secondaryLaunchConfigs\":[]}}"
          data=$(echo "$data" | jq -c .)
          curl -u "${rancher_username}:${rancher_password}" \
          -X POST \
          -H "Accept: application/json" \
          -H "Content-Type: application/json" \
          "${deploy_url_base}/?action=upgrade" \
          -d ${data}
          echo "-------complete image updating for ${key}--------------"
          sleep 10
          state=$(curl -u "${rancher_username}:${rancher_password}" \
          -X GET \
          -H "Accept: application/json" \
          -H "Content-Type: application/json" \
          "${deploy_url_base}" \
          | jq '.state')
          if [[ ( ${state} != "upgraded" ) ]]; then
            sleep 10
            state=$(curl -u "${rancher_username}:${rancher_password}" \
            -X GET \
            -H "Accept: application/json" \
            -H "Content-Type: application/json" \
            "${deploy_url_base}" \
            | jq '.state')
            if [[ ( ${state} != "upgraded" ) ]]; then
              sleep 10
            fi
          fi
          echo "-------try to finish deployment for ${key}--------------"
          curl -u "${rancher_username}:${rancher_password}" \
          -X POST \
          "${deploy_url_base}/?action=finishupgrade"
          echo "-------complete deploying ${key}--------------"
        fi
    done
   fi
   echo "-------complete processing ${pre_special_project}--------------"
done
for entry in $target_dir/*;
do
  service_name="$(basename $entry)"
  if [[ ( ${possible_projects[*]} =~ ${service_name} ) && (! ${pre_special_projects[*]} =~ ${service_name} ) && (! ${post_special_projects[*]} =~ ${service_name} ) ]]; then
    echo "-------start processing ${service_name}--------------"
    if [[ (${deploy_projects[*]} =~ ${service_name} ) ]]; then
      tag="${docker_hub_username}/apn-v3-staging-${service_name}:${version}"
      for key in ${!service_mapping[@]}; do
         if [[ ( ${key} == ${service_name} ) ]]; then
           deploy_service=${service_mapping[${key}]}
           deploy_url_base=${rancher_url_base}${deploy_service}
           echo "-------start deploying ${key}--------------"
           launchConfig=$(curl -u "${rancher_username}:${rancher_password}" \
           -X GET \
           -H "Accept: application/json" \
           -H "Content-Type: application/json" \
           "${deploy_url_base}" \
           | jq '.launchConfig')
           old_image=$(echo "$launchConfig" | jq '.imageUuid')
           new_image="\"docker:${tag}\""
           launchConfig=${launchConfig/$old_image/$new_image}
           old_hostId=$(echo "$launchConfig" | jq '.requestedHostId')
           new_hostId="\"1h23\""
           launchConfig=${launchConfig/$old_hostId/$new_hostId}
           data="{\"inServiceStrategy\":{\"batchSize\":1,\"intervalMillis\":2000,\"startFirst\":true,\"launchConfig\":${launchConfig},\"secondaryLaunchConfigs\":[]}}"
           data=$(echo "$data" | jq -c .)
           curl -u "${rancher_username}:${rancher_password}" \
           -X POST \
           -H "Accept: application/json" \
           -H "Content-Type: application/json" \
           "${deploy_url_base}/?action=upgrade" \
           -d ${data}
           echo "-------complete image updating for ${key}--------------"
           sleep 10
           state=$(curl -u "${rancher_username}:${rancher_password}" \
           -X GET \
           -H "Accept: application/json" \
           -H "Content-Type: application/json" \
           "${deploy_url_base}" \
           | jq '.state')
           if [[ ( ${state} != "upgraded" ) ]]; then
             sleep 10
             state=$(curl -u "${rancher_username}:${rancher_password}" \
             -X GET \
             -H "Accept: application/json" \
             -H "Content-Type: application/json" \
             "${deploy_url_base}" \
             | jq '.state')
             if [[ ( ${state} != "upgraded" ) ]]; then
               sleep 10
             fi
           fi
           echo "-------try to finish deployment for ${key}--------------"
           curl -u "${rancher_username}:${rancher_password}" \
           -X POST \
           "${deploy_url_base}/?action=finishupgrade"
           echo "-------complete deploying ${key}--------------"
         fi
      done
    fi
    echo "-------complete processing ${service_name}--------------"
  fi
done
for post_special_project in ${post_special_projects[@]};
do
   echo "-------start processing ${post_special_project}--------------"
   if [[ (${deploy_projects[*]} =~ ${post_special_project} ) ]]; then
     tag="${docker_hub_username}/apn-v3-staging-${post_special_project}:${version}"
     for key in ${!service_mapping[@]}; do
       if [[ ( ${key} == ${post_special_project} ) ]]; then
         deploy_service=${service_mapping[${key}]}
         deploy_url_base=${rancher_url_base}${deploy_service}
         echo "-------start deploying ${key}--------------"
         launchConfig=$(curl -u "${rancher_username}:${rancher_password}" \
         -X GET \
         -H "Accept: application/json" \
         -H "Content-Type: application/json" \
         "${deploy_url_base}" \
         | jq '.launchConfig')
         old_image=$(echo "$launchConfig" | jq '.imageUuid')
         new_image="\"docker:${tag}\""
         launchConfig=${launchConfig/$old_image/$new_image}
         old_hostId=$(echo "$launchConfig" | jq '.requestedHostId')
         new_hostId="\"1h23\""
         launchConfig=${launchConfig/$old_hostId/$new_hostId}
         data="{\"inServiceStrategy\":{\"batchSize\":1,\"intervalMillis\":2000,\"startFirst\":true,\"launchConfig\":${launchConfig},\"secondaryLaunchConfigs\":[]}}"
         data=$(echo "$data" | jq -c .)
         curl -u "${rancher_username}:${rancher_password}" \
         -X POST \
         -H "Accept: application/json" \
         -H "Content-Type: application/json" \
         "${deploy_url_base}/?action=upgrade" \
         -d ${data}
         echo "-------complete image updating for ${key}--------------"
         sleep 10
         state=$(curl -u "${rancher_username}:${rancher_password}" \
         -X GET \
         -H "Accept: application/json" \
         -H "Content-Type: application/json" \
         "${deploy_url_base}" \
         | jq '.state')
         if [[ ( ${state} != "upgraded" ) ]]; then
           sleep 10
           state=$(curl -u "${rancher_username}:${rancher_password}" \
           -X GET \
           -H "Accept: application/json" \
           -H "Content-Type: application/json" \
           "${deploy_url_base}" \
           | jq '.state')
           if [[ ( ${state} != "upgraded" ) ]]; then
             sleep 10
           fi
         fi
         echo "-------try to finish deployment for ${key}--------------"
         curl -u "${rancher_username}:${rancher_password}" \
         -X POST \
         "${deploy_url_base}/?action=finishupgrade"
         echo "-------complete deploying ${key}--------------"
       fi
    done
   fi
   echo "-------complete processing ${post_special_project}--------------"
done