# 使用方式 ./v2-v3-data-migration.sh v2-v3-data-migration-config.txt
# 读取变量信息
source $1
# dump apnv2的平移表
echo "--------------------Starting apnv2 database dump--------------------"
mysqldump --host=${v2_database_host} --port=${v2_database_port} --default-character-set=utf8 --user=${v2_mysql_username} --password=${v2_mysql_password} --protocol=tcp --column-statistics=FALSE --skip-triggers --single-transaction ${v2_database_name} start_fte_rate timesheet_user_authority onboarding_process_operation_details application user_favorite_talent timesheet_manager assignment_pay_info linkedin_project_talent assignment_pay_rate persistent_audit_evt_data job_ipg_relation skip_submit_to_am_company_user commission_transaction timesheet_talent_assignment start_failed_warranty parse_record us_metro_area_info authority start_commission timesheet_comments start_rate_change invoice_client_credit assignment_bill_info start skip_submit_to_am_company column_preference timesheet_expense_record tenant user_favorite_job job_note application_agreed_pay_rate commission hot_list_user time_sheet_record start_contract_rate time_sheet_user user_job_relation oauth_client_details invoice_activity talent_contact invoice hot_list onboarding_process_history_signatures application_offer_letter talent_note user timesheet_approve_record user_email_domain city_locations_en talent_ownership linkedin_project onboarding_process_histories onboarding_process_approval_details application_commission onboarding_drafts start_address start_client_info timesheet_calculate_state sequence city_locations_cn us_geo_info assignment_location tenant_admin_status event event_user invoice_payment_record onboarding_package_documents user_authority timesheet_search_config time_sheet_expense_week_ending_record linkedin_project_member linkedin_talent column_dict activity activity_unique time_sheet_week_ending_record user_favorite_linkedin_project start_termination onboarding_packages job_location linkedin_talent_contact assignment_timesheet job_contact_relation linkedin_stats onboarding_documents assignment_contribution user_account timesheet_breaktime_record hot_list_talent persistent_audit_event async_record pipeline_column_preference apn_param credit_transaction esstats job_bool_string job_details_report report_user_job_talent address division team team_user currency_rate watch_list talent_tracking_record talent_tracking_note offer_letter_cost_rate search_preference favorite_country email_domain email_blast_attachment email_blast_log email_blast_log_details email_blast_log_mailing_list email_blast_log_transmission email_template mailing_list mailing_list_content > ${mysql_dump_directory}/dump-apn-v2-migration.sql
echo "--------------------Completed apnv2 database dump--------------------"
# 创建 apnv3数据库
echo "--------------------Creating apnv3 database--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} -e "create database ${v3_database_name}";
echo "--------------------Completed apnv3 database creation--------------------"
# 导入apnv2的平移表dump
echo "--------------------Starting apnv2 database restore--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ${mysql_dump_directory}/dump-apn-v2-migration.sql
echo "--------------------Completed apnv2 database restore--------------------"
# 执行apnv2平移后操作
echo "--------------------Starting apnv2 database post execution--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ./sql/migration/post-v2-data-migration.sql
echo "--------------------Completed apnv2 database post execution--------------------"
# dump apnv3 staging的平移表（只需要数据结构，不需要数据）
echo "--------------------Starting apnv3 staging database dump without data--------------------"
mysqldump --host=${v3_staging_database_host} --port=${v3_staging_database_port} --default-character-set=utf8 --user=${v3_staging_mysql_username} --password=${v3_staging_mysql_password} --protocol=tcp --column-statistics=FALSE --skip-triggers --single-transaction --no-data ${v3_staging_database_name}  talent_additional_info company_service_type company_contract talent_job_function_relation company_assign_team_member job_preferred_degree_relation company_sales_lead_service_type company_progress_note job_additional_info talent_current_location company_sales_lead_client_contact resume talent job_job_function_relation talent_resume_relation company_address company_project_team job_minimum_degree_relation company_sales_lead talent_industry_relation talent_language_relation company_contract_signer job_preferred_languages_relation company_sales_lead_administrator company job_required_languages_relation job company_sales_lead_connect_client_contact talent_work_authorization_relation company_project_team_user am_report_talent_job_note > ${mysql_dump_directory}/staging-v3-migration.sql
echo "--------------------Completed apnv3 staging database dump without data--------------------"
# 导入apnv3 staging的平移表dump
echo "--------------------Starting apnv3 database restore without data--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ${mysql_dump_directory}/staging-v3-migration.sql
echo "--------------------Completed apnv3 database restore without data--------------------"
# dump apnv3 staging的平移表（需要数据结构与数据）
#echo "--------------------Starting apnv3 staging database dump with data--------------------"
#mysqldump --host=${v3_staging_database_host} --port=${v3_staging_database_port} --default-character-set=utf8 --user=${v3_staging_mysql_username} --password=${v3_staging_mysql_password} --protocol=tcp --column-statistics=FALSE --skip-triggers --single-transaction ${v3_staging_database_name}  permission_privilege > ${mysql_dump_directory}/staging-v3-migration-with-data.sql
#echo "--------------------Completed apnv3 staging database dump with data--------------------"
# 导入apnv3 staging的平移表dump
#echo "--------------------Starting apnv3 database restore with data--------------------"
#mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ${mysql_dump_directory}/staging-v3-migration-with-data.sql
#echo "--------------------Completed apnv3 database restore with data--------------------"
# 执行company etl准备操作
echo "--------------------Starting company etl preparation--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ./sql/migration/pre-company-etl.sql
echo "--------------------Completed company etl preparation--------------------"
# 执行company etl程序
echo "--------------------Starting company etl execution--------------------"
container_name="company-etl"
if [ ! "$(docker ps -q -f name=${container_name})" ]; then
    if [ "$(docker ps -aq -f status=exited -f name=${container_name})" ]; then
        docker rm ${container_name}
    fi
    docker run --name ${container_name} \
    -e DATASOURCE_PRIMARY_DRIVER=com.mysql.cj.jdbc.Driver \
    -e DATASOURCE_PRIMARY_URL="jdbc:mysql://${v2_database_host}:${v2_database_port}/${v2_database_name}?useUnicode=true&characterEncoding=utf-8" \
    -e DATASOURCE_PRIMARY_USERNAME=${v2_mysql_username} \
    -e DATASOURCE_PRIMARY_PASSWORD=${v2_mysql_password} \
    -e DATASOURCE_SECOND_DRIVER=com.mysql.cj.jdbc.Driver \
    -e DATASOURCE_SECOND_URL="jdbc:mysql://${v3_database_host}:${v3_database_port}/${v3_database_name}?useUnicode=true&characterEncoding=UTF-8" \
    -e DATASOURCE_SECOND_USERNAME=${v3_mysql_username} \
    -e DATASOURCE_SECOND_PASSWORD=${v3_mysql_password} \
    -e REDIS_HOST=${redis_host} \
    -e REDIS_PORT=${redis_port} \
    --platform linux/amd64 \
    -d ${company_etl_docker_image}
fi
echo "--------------------Completed company etl docker launched--------------------"
echo "--------------------Waiting for company etl execution until completed--------------------"
while true
do
	if docker ps | grep -q $container_name
  then
    echo "--------------------${container_name} is still running--------------------"
    sleep 5
  else
    break
  fi
done
echo "--------------------Company etl execution completed--------------------"
# 执行company etl后续操作
echo "--------------------Starting company etl post execution--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ./sql/migration/post-company-etl.sql
echo "--------------------Completed company etl post execution--------------------"
# 执行job-talent etl准备操作
echo "--------------------Starting job&talent etl preparation--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ./sql/migration/pre-job-talent-etl.sql
echo "--------------------Completed job&talent etl preparation--------------------"
# TODO: load talentResume::s3map key to redis
# 执行job talent etl程序
echo "--------------------Starting job&talent etl execution--------------------"
container_name="job-talent-etl"
if [ ! "$(docker ps -q -f name=${container_name})" ]; then
    if [ "$(docker ps -aq -f status=exited -f name=${container_name})" ]; then
        docker rm ${container_name}
    fi
    docker run --name ${container_name} \
    -e DATASOURCE_PRIMARY_DRIVER=com.mysql.cj.jdbc.Driver \
    -e DATASOURCE_PRIMARY_URL="jdbc:mysql://${v2_database_host}:${v2_database_port}/${v2_database_name}?useUnicode=true&characterEncoding=utf-8" \
    -e DATASOURCE_PRIMARY_USERNAME=${v2_mysql_username} \
    -e DATASOURCE_PRIMARY_PASSWORD=${v2_mysql_password} \
    -e DATASOURCE_SECOND_DRIVER=com.mysql.cj.jdbc.Driver \
    -e DATASOURCE_SECOND_URL="jdbc:mysql://${v3_database_host}:${v3_database_port}/${v3_database_name}?useUnicode=true&characterEncoding=UTF-8" \
    -e DATASOURCE_SECOND_USERNAME=${v3_mysql_username} \
    -e DATASOURCE_SECOND_PASSWORD=${v3_mysql_password} \
    -e REDIS_HOST=${redis_host} \
    -e REDIS_PORT=${redis_port} \
    -e S3_ACCESS_KEY=${s3_access_key} \
    -e S3_SECRET_KEY=${s3_secret_key} \
    -e S3_URL=${s3_url} \
    -e S3_BUCKET_NAME=${s3_bucket_name} \
    -e S3_REGION=${s3_region} \
    -e ALIYUN_ACCESS_KEY=${aliyun_access_key} \
    -e ALIYUN_SECRET_KEY=${aliyun_secret_key} \
    -e ALIYUN_URL=${aliyun_url} \
    -e ALIYUN_RESUME_BUCKET_NAME=${aliyun_resume_bucket_name} \
    -e ALIYUN_END_POINT=${aliyun_end_point} \
    --platform linux/amd64 \
    -d ${job_talent_etl_docker_image}
fi
echo "--------------------Completed job&talent etl docker launched--------------------"
echo "--------------------Waiting for job&talent etl execution until completed--------------------"
while true
do
	if docker ps | grep -q $container_name
  then
    echo "--------------------${container_name} is still running--------------------"
    sleep 5
  else
    break
  fi
done
echo "--------------------Job&talent etl execution completed--------------------"
######## 导入apnv3的application相关dump ###########
echo "--------------------Starting application etl preparation--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ./sql/migration/application_data_transfer_1_0.sql
echo "--------------------Completed application etl preparation--------------------"
######## 执行application etl程序 #################
echo "--------------------Starting application etl execution--------------------"
container_name="application-etl"
if [ ! "$(docker ps -q -f name=${container_name})" ]; then
    if [ "$(docker ps -aq -f status=exited -f name=${container_name})" ]; then
        docker rm ${container_name}
    fi
    docker run --name ${container_name} \
    -e APN_MYSQL_HOST=${v3_database_host} \
    -e APN_MYSQL_PORT=${v3_database_port} \
    -e APN_MYSQL_USERNAME=${v3_mysql_username} \
    -e APN_MYSQL_PASSWORD=${v3_mysql_password} \
    -e APN_MYSQL_DATABASE=${v3_database_name} \
    -e STARTSWITH_APPLICATION_ID_FOR_ELIMINATE=0 \
    -e LOG_FILE='/app/logging.yml' \
    --platform linux/amd64 \
    -d ${application_etl_docker_image}
fi
echo "--------------------Completed application etl docker launched--------------------"
echo "--------------------Waiting for application etl execution until completed--------------------"
while true
do
	if docker ps | grep -q $container_name
  then
    echo "--------------------${container_name} is still running--------------------"
    sleep 5
  else
    break
  fi
done
echo "--------------------Application etl execution completed--------------------"
# 执行application etl后续操作
echo "--------------------Starting application etl post execution--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ./sql/migration/post-application-etl.sql
echo "--------------------Completed application etl post execution--------------------"
# dump apnv3 talent_ownership表
echo "--------------------Starting apnv3 talent_ownership table dump--------------------"
mysqldump --host=${v3_database_host} --port=${v3_database_port} --default-character-set=utf8 --user=${v3_mysql_username} --password=${v3_mysql_password} --protocol=tcp --column-statistics=FALSE --skip-triggers --single-transaction ${v3_database_name} talent_ownership | sed 's/talent_ownership/talent_ownership_backup/g' > ${mysql_dump_directory}/dump-apn-v3-talent_ownership.sql
echo "--------------------Completed apnv3 talent_ownership table dump--------------------"
# 执行 data permission etl
echo "--------------------Starting data permission etl execution--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ./sql/migration/data_permission_transfer.sql
echo "--------------------Completed data permission etl execution--------------------"
# restore apnv3 talent_ownership备份表
echo "--------------------Starting apnv3 talent_ownership backup table restore--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ${mysql_dump_directory}/dump-apn-v3-talent_ownership.sql
echo "--------------------Completed apnv3 talent_ownership backup table restore--------------------"
# recover apnv3 talent_ownership表
echo "--------------------Starting apnv3 talent_ownership table recovery--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < /sql/migration/recover-talent-ownership.sql
echo "--------------------Completed apnv3 talent_ownership table recovery--------------------"
# 执行 page permission etl
echo "--------------------Starting page permission etl execution--------------------"
mysql -h ${v3_database_host} -P ${v3_database_port} --protocol=tcp -u ${v3_mysql_username} -p${v3_mysql_password} ${v3_database_name} < ./sql/migration/page_permission_transfer.sql
echo "--------------------Completed page permission etl execution--------------------"

echo "--------------------Job done!!!--------------------"