//package com.altomni.apn.report.repository.v2;
//
//
//import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
//import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
//import com.altomni.apn.common.dto.recruiting.SearchUserDto;
//import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
//import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiDateType;
//import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
//import org.junit.Assert;
//import org.junit.Test;
//
//import java.util.List;
//
//public class RecruitingKpiUserRepositoryV2Test {
//
//    private final RecruitingKpiUserRepositoryV2 testClass = new RecruitingKpiUserRepositoryV2();
//
//    @Test
//    public void testSubmitJobQueryGroupByMonthAndTeam() {
//        RecruitingKpiReportSearchDto searchDto = new RecruitingKpiReportSearchDto();
//        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.MONTH, RecruitingKpiGroupByFieldType.TEAM));
//        searchDto.setTimezone("Pacific/Pago_Pago");
//        searchDto.setStartDate("2025-04-21");
//        searchDto.setEndDate("2025-04-27");
//        searchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
//        searchDto.setDateType(RecruitingKpiDateType.ADD);
//        SearchUserDto searchUserDto = new SearchUserDto();
//        searchUserDto.setUserActiveStatus(true);
//        searchDto.setUser(searchUserDto);
//
//        TeamDataPermissionRespDTO teamDTO = new TeamDataPermissionRespDTO();
//        teamDTO.setSelf(false);
//        teamDTO.setAll(true);
//
//        String sql = testClass.submitToJobQuery(searchDto, teamDTO).toString();
//        System.out.println("SubmitJobQueryGroupByMonthAndTeam sql: " + sql);
//
//        String expectSql = """
//                SELECT
//                  DATE_FORMAT(d.start_of_month, '%Y-%m' ) AS group_by_date,
//                  team_id,
//                  team_name,
//                  count(DISTINCT CASE
//                    WHEN (
//                      node_status = 1
//                      AND node_type = 10
//                    ) THEN talent_recruitment_process_id
//                  END) AS currentCountNum,
//                  COUNT(DISTINCT submit_job_id) AS countNum
//                FROM view_application AS application
//                  JOIN date_dimension AS d
//                    ON d.date = DATE(CONVERT_TZ(submit_job_created_date, 'UTC', 'Pacific/Pago_Pago'))
//                WHERE (
//                  tenant_id = NULL
//                  AND d.date BETWEEN '2025-04-21T11:00Z' AND '2025-04-28T10:59:59Z'
//                  AND (
//                    job_pteam_id IS NOT NULL
//                    OR FALSE
//                  )
//                  AND user_activted = TRUE
//                )
//                GROUP BY
//                  group_by_date,
//                  team_id,
//                  team_name""";
//        Assert.assertEquals(expectSql, sql);
//    }
//
//    @Test
//    public void testSubmitJobQueryGroupByTeamAndUser() {
//        RecruitingKpiReportSearchDto searchDto = new RecruitingKpiReportSearchDto();
//        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.TEAM, RecruitingKpiGroupByFieldType.USER));
//        searchDto.setTimezone("Pacific/Pago_Pago");
//        searchDto.setStartDate("2025-04-21");
//        searchDto.setEndDate("2025-04-27");
//        searchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
//        searchDto.setDateType(RecruitingKpiDateType.ADD);
//        SearchUserDto searchUserDto = new SearchUserDto();
//        searchUserDto.setUserActiveStatus(true);
//        searchDto.setUser(searchUserDto);
//
//        TeamDataPermissionRespDTO teamDTO = new TeamDataPermissionRespDTO();
//        teamDTO.setSelf(false);
//        teamDTO.setAll(true);
//
//        String sql = testClass.submitToJobQuery(searchDto, teamDTO).toString();
//        System.out.println("SubmitJobQueryGroupByTeamAndUser sql: " + sql);
//
//        String expectSql = """
//                SELECT
//                  team_id,
//                  team_name,
//                  user_id,
//                  user_name,
//                  COUNT(DISTINCT IF(node_type = 10 AND node_status = 1, talent_recruitment_process_id, NULL)) AS currentCountNum,
//                  COUNT(DISTINCT submit_job_id) AS countNum
//                FROM view_application AS application
//                WHERE (
//                  tenant_id = NULL
//                  AND submit_job_created_date BETWEEN '2025-04-21T11:00Z' AND '2025-04-28T10:59:59Z'
//                  AND (
//                    job_pteam_id IS NOT NULL
//                    OR FALSE
//                  )
//                  AND user_activted = TRUE
//                )
//                GROUP BY
//                  team_id,
//                  team_name,
//                  user_id,
//                  user_name""";
//
//        Assert.assertEquals(expectSql, sql);
//    }
//
//    @Test
//    public void testSubmitJobQueryGroupByUser() {
//
//        RecruitingKpiReportSearchDto searchDto = new RecruitingKpiReportSearchDto();
//        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.USER));
//        searchDto.setTimezone("Pacific/Pago_Pago");
//        searchDto.setStartDate("2025-04-21");
//        searchDto.setEndDate("2025-04-27");
//        searchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
//        searchDto.setDateType(RecruitingKpiDateType.ADD);
//        SearchUserDto searchUserDto = new SearchUserDto();
//        searchUserDto.setUserActiveStatus(true);
//        searchDto.setUser(searchUserDto);
//
//        TeamDataPermissionRespDTO teamDTO = new TeamDataPermissionRespDTO();
//        teamDTO.setSelf(false);
//        teamDTO.setAll(true);
//
//        String sql = testClass.submitToJobQuery(searchDto, teamDTO).toString();
//        System.out.println("SubmitJobQueryGroupByUser sql: " + sql);
//
//        String expectSql = """
//                SELECT
//                  user_id,
//                  user_name,
//                  COUNT(DISTINCT IF(node_type = 10 AND node_status = 1, talent_recruitment_process_id, NULL)) AS currentCountNum,
//                  COUNT(DISTINCT submit_job_id) AS countNum
//                FROM view_application AS application
//                WHERE (
//                  tenant_id = NULL
//                  AND submit_job_created_date BETWEEN '2025-04-21T11:00Z' AND '2025-04-28T10:59:59Z'
//                  AND (
//                    job_pteam_id IS NOT NULL
//                    OR FALSE
//                  )
//                  AND user_activted = TRUE
//                )
//                GROUP BY
//                  user_id,
//                  user_name""";
//        Assert.assertEquals(expectSql, sql);
//    }
//
//    @Test
//    public void testWithTable() {
//        RecruitingKpiReportSearchDto searchDto = new RecruitingKpiReportSearchDto();
//        searchDto.setGroupByFieldList(List.of(RecruitingKpiGroupByFieldType.USER));
//        searchDto.setTimezone("Pacific/Pago_Pago");
//        searchDto.setStartDate("2025-04-21");
//        searchDto.setEndDate("2025-04-27");
//        searchDto.setApplicationStatusType(RecruitingKpiApplicationStatusType.ALL);
//        searchDto.setDateType(RecruitingKpiDateType.ADD);
//        SearchUserDto searchUserDto = new SearchUserDto();
//        searchUserDto.setUserActiveStatus(true);
//        searchDto.setUser(searchUserDto);
//
//        TeamDataPermissionRespDTO teamDTO = new TeamDataPermissionRespDTO();
//        teamDTO.setSelf(false);
//        teamDTO.setAll(true);
//
//        String s = testClass.applicationOlapQuery(searchDto, teamDTO);
//
//    }
//
//    @Test
//    public void testApplicationView() {
//        String sql = testClass.applicationKpiView();
//        System.out.println(sql);
//    }
//}