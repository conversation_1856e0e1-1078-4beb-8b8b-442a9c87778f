<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="yushan" id="20240821094926">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_application_relation "/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE job_application_relation (
              `id` BIGINT NOT NULL AUTO_INCREMENT,
              `job_id` bigint not null,
              `submit_to_job` INT DEFAULT NULL COMMENT 'submit_to_job总数量',
              `submit_to_client` INT DEFAULT NULL COMMENT 'submitToClient总数量',
              `first_interview` INT DEFAULT NULL COMMENT 'first_interview总数量',
              `second_interview` INT DEFAULT NULL COMMENT 'second_interview总数量',
              `final_interview` INT DEFAULT NULL COMMENT 'final_interview总数量',
              `interview` INT DEFAULT NULL COMMENT 'interview总数量',
              `offer` INT DEFAULT NULL COMMENT 'offer数量',
              `offer_accept` INT DEFAULT NULL COMMENT 'offer_accept数量',
              `onboard` INT DEFAULT NULL COMMENT 'onboard数量',
              PRIMARY KEY ( `id` ) ,
              KEY `idx_jar_job_id` (`job_id`)
            );
        </sql>
    </changeSet>

</databaseChangeLog>
