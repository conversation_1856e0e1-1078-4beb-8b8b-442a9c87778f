<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="yushan" id="20240821095236">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_user_relation"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE `talent_recruitment_process_note` (
               `id` bigint NOT NULL AUTO_INCREMENT,
               `talent_id` bigint not null,
               `tenant_id` bigint NOT NULL,
               `node_id` bigint NOT NULL,
               `note` mediumtext COLLATE utf8mb4_general_ci,
               `talent_recruitment_process_id` bigint NOT NULL,
               `node_type` int NOT NULL,
               `user_id` bigint NOT NULL,
               `created_date` timestamp(3) NULL DEFAULT NULL,
               `created_date_los_angeles` date GENERATED ALWAYS AS (date_format(convert_tz(`created_date`,_utf8mb4'UTC',_utf8mb4'America/Los_Angeles'),_utf8mb4'%Y-%m-%d')) STORED,
               `created_date_shanghai` date GENERATED ALWAYS AS (date_format(convert_tz(`created_date`,_utf8mb4'UTC',_utf8mb4'Asia/Shanghai'),_utf8mb4'%Y-%m-%d')) STORED,
               `created_date_new_york` date GENERATED ALWAYS AS (date_format(convert_tz(`created_date`,_utf8mb4'UTC',_utf8mb4'America/New_York'),_utf8mb4'%Y-%m-%d')) STORED,
               PRIMARY KEY (`id`),
               KEY `idx_trpn_user_id` (`user_id`),
               KEY `idx_trpn_node_type` (`node_type`),
               KEY `idx_trpn_node_id` (`node_id`),
               KEY `idx_trpn_talent_id`(`talent_id`),
               KEY `idx_trpn_tenant_id_created_date_talent_recruitment_process_id` (`tenant_id`,`created_date`,`talent_recruitment_process_id`),
               KEY `idx_trpn_tenant_id_los_angeles_talent_recruitment_process_id` (`tenant_id`,`created_date_los_angeles`,`talent_recruitment_process_id`),
               KEY `idx_trpn_tenant_id_shanghai_talent_recruitment_process_id` (`tenant_id`,`created_date_shanghai`,`talent_recruitment_process_id`),
               KEY `idx_trpn_tenant_id_new_york_talent_recruitment_process_id` (`tenant_id`,`created_date_new_york`,`talent_recruitment_process_id`)
            ) ENGINE=InnoDB AUTO_INCREMENT=294910 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
        </sql>
    </changeSet>

</databaseChangeLog>
