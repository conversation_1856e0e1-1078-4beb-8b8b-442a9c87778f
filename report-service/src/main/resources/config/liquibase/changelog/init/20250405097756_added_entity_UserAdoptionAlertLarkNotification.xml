<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="zerong" id="20250405097756">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="user_adoption_alert_lark_notification"/>
            </not>
        </preConditions>
        <createTable tableName="user_adoption_alert_lark_notification">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="permission_team_ids" type="MEDIUMTEXT">
                <constraints nullable="false"/>
            </column>
            <column name="notified_user_id" type="bigint">
                <constraints nullable="false"/>
            </column>

        </createTable>

        <createIndex indexName="idx_user_adoption_alert_lark_notification_permission_tenant_id" tableName="user_adoption_alert_lark_notification">
            <column name="tenant_id"/>
        </createIndex>

        <createIndex indexName="idx_user_adoption_alert_lark_notification_notified_user_id" tableName="user_adoption_alert_lark_notification">
            <column name="notified_user_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
