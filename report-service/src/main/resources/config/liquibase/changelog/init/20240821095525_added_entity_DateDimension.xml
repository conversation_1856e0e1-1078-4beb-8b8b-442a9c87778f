<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="yushan" id="20240821095525">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_user_relation"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE IF NOT EXISTS date_dimension (
              date DATE PRIMARY KEY,
              year INT,
              month INT,
              day INT,
              day_of_week INT,
              week_of_year INT,
              start_of_week DATE,
              end_of_week DATE,
              quarter INT,
              start_of_month DATE,
              quarter_of_year VARCHAR(6),
            KEY `idx_date_dimension_date`(`date`));
        </sql>
    </changeSet>

</databaseChangeLog>
