<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="yushan" id="20240821094725">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_user_relation"/>
            </not>
        </preConditions>
        <sql>
            CREATE TABLE talent_user_relation (
              id BIGINT NOT NULL AUTO_INCREMENT,
              talent_id BIGINT NOT NULL,
              user_id BIGINT NOT NULL,
              role TINYINT NOT NULL COMMENT '1.create 2.owner 3.share 4.am 5.Recruiter 6.SOURCER 7.DM 8.OWNER 9.AC',
              PRIMARY KEY ( `id` ),
              UNIQUE KEY unidx_tur_talent_id_user_id_role ( talent_id, user_id, role )
            );
        </sql>
    </changeSet>

</databaseChangeLog>
