<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="Zhicong" id="1700017078000">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="report"/>
            </not>
        </preConditions>
        <createTable tableName="report">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="varchar(255)" defaultValue="''">
            </column>
            <column name="dataset_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="view_type" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="config" type="json">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="puser_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="pteam_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="datetime" defaultValueComputed="CURRENT_TIMESTAMP(3)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_date" type="datetime">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_report_tenant_id" tableName="report">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
