-- 流程相关的事实表和维度表关联视图宽表，给流程相关的物化视图提供数据
create view view_application_wide as
(
WITH fact_with_user AS (
    -- 从 KPI User 关联
    SELECT fact.talent_recruitment_process_id,
           fact.node_type,
           fact.node_status,
           fact.node_id,
           fact.progress,
           fact.final_round,
           fact.add_date,
           fact.event_date,
           kpi.user_id,
           IF(kpi.user_role IS NOT NULL, kpi.user_role, 10000) AS user_role
    FROM ods_apn.mv_application_fact fact
             INNER JOIN ods_apn.talent_recruitment_process_kpi_user kpi
                        ON fact.talent_recruitment_process_id = kpi.talent_recruitment_process_id

    UNION ALL

    -- 从 Operator 补充
    SELECT fact.talent_recruitment_process_id,
           fact.node_type,
           fact.node_status,
           fact.node_id,
           fact.progress,
           fact.final_round,
           fact.add_date,
           fact.event_date,
           fact.operator AS user_id,
           10            AS user_role
    FROM ods_apn.mv_application_fact fact)

SELECT dim.tenant_id,
       dim.company_id,
       dim.job_id,
       dim.job_pteam_id,
       dim.talent_recruitment_process_id,
       dim.talent_id,
       dim.ai_score,
       dim.recommend_feedback_id,
       pt.id                                  AS team_id,
       pt.name                                AS team_name,
       fact_with_user.user_id,
       CONCAT(u.first_name, ' ', u.last_name) AS user_name,
       u.activated                            AS user_activated,
       fact_with_user.user_role,
       fact_with_user.node_id,
       fact_with_user.node_type,
       fact_with_user.node_status,
       fact_with_user.progress,
       fact_with_user.final_round,
       fact_with_user.add_date,
       fact_with_user.event_date
FROM fact_with_user
         INNER JOIN ods_apn.mv_application_dimension AS dim
                    ON fact_with_user.talent_recruitment_process_id = dim.talent_recruitment_process_id
         INNER JOIN ods_apn.user AS u
                    ON fact_with_user.user_id = u.id
         INNER JOIN ods_apn.permission_user_team AS put
                    ON u.id = put.user_id AND put.is_primary = 1
         INNER JOIN ods_apn.permission_team AS pt
                    ON put.team_id = pt.id
);






