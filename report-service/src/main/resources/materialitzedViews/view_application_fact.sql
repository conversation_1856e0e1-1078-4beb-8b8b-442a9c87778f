-- 流程相关事实表的视图，主要是把 kpi user 和流程操作者的角色信息合并到一起，方便后续的分析和报表生成。

CREATE VIEW view_application_fact AS
(
WITH t AS (
    SELECT
        fact.talent_recruitment_process_id,
        fact.node_type,
        fact.node_status,
        fact.node_id,
        fact.progress,
        fact.final_round,
        fact.add_date,
        fact.event_date,
        fact.operator,
        kpi.user_id,
        IF(kpi.user_role IS NOT NULL, kpi.user_role, 10000) AS user_role
    FROM
        mv_application_fact fact
            INNER JOIN talent_recruitment_process_kpi_user kpi
                       ON
                           fact.talent_recruitment_process_id = kpi.talent_recruitment_process_id
    GROUP BY
        fact.talent_recruitment_process_id,
        fact.node_type,
        fact.node_status,
        fact.node_id,
        fact.progress,
        fact.final_round,
        fact.add_date,
        fact.event_date,
        fact.operator,
        kpi.user_id,
        kpi.user_role)
SELECT
    t.talent_recruitment_process_id,
    t.node_type,
    t.node_status,
    t.node_id,
    t.progress,
    t.final_round,
    t.add_date,
    t.event_date,
    t.user_id,
    t.user_role
FROM
    t
UNION ALL
SELECT
    DISTINCT t.talent_recruitment_process_id,
             t.node_type,
             t.node_status,
             t.node_id,
             t.progress,
             t.final_round,
             t.add_date,
             t.event_date,
             t.operator AS user_id,
             10 AS user_role
FROM
    t)
;