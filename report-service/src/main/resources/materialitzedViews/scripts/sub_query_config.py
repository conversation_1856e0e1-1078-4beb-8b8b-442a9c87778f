from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field


@dataclass
class SubQueryConfig:
    """子查询配置"""

    name: str  # 子查询名称
    add_date_field: str  # add_date 字段表达式
    event_date_field: str  # event_date 字段表达式
    additional_joins: List[str] = field(default_factory=list)  # 额外的 JOIN 语句
    where_conditions: List[str] = field(default_factory=list)  # WHERE 条件
    group_by_additions: List[str] = field(default_factory=list)  # GROUP BY 额外字段


def generate_sub_query_config() -> List[SubQueryConfig]:
    subqueries = [
        SubQueryConfig(
            name="submit_to_job",
            add_date_field="add_date",
            event_date_field="event_date",
            where_conditions=["application.node_type = 10"],
            additional_joins=[
                """
                left join (
                    select  pn.talent_recruitment_process_id,
                            IF(MIN(pn.add_date) IS NOT NULL, MIN(pn.add_date), MAX(eli.add_date)) as stayed_add_date
                    from mv_application_fact pn
                        left join mv_application_fact eli on eli.talent_recruitment_process_id = pn.talent_recruitment_process_id and eli.node_type=-1
                    where pn.node_type > 10  group by pn.talent_recruitment_process_id
                ) pn on pn.talent_recruitment_process_id = application.talent_recruitment_process_id
                """
            ]
        ),
        SubQueryConfig(
            name="submit_to_client",
            add_date_field="add_date",
            event_date_field="event_date",
            where_conditions=["application.node_type = 20"],
            additional_joins=[
                """
                left join (
                    select  pn.talent_recruitment_process_id,
                            IF(MIN(pn.add_date) IS NOT NULL, MIN(pn.add_date), MAX(eli.add_date)) as stayed_add_date
                    from mv_application_fact pn
                        left join mv_application_fact eli on eli.talent_recruitment_process_id = pn.talent_recruitment_process_id and eli.node_type=-1
                    where pn.node_type > 20  group by pn.talent_recruitment_process_id
                ) pn on pn.talent_recruitment_process_id = application.talent_recruitment_process_id
                """
            ]
        ),
        SubQueryConfig(
            name="interview",
            add_date_field="add_date",
            event_date_field="event_date",
            where_conditions=["node_type = 30"],
            additional_joins=[
                """LEFT OUTER JOIN (SELECT talent_recruitment_process_id AS application_id,
                                           MAX(progress)                 AS progress
                                    FROM mv_application_fact fact
                                    WHERE node_type = 30
                                    GROUP BY talent_recruitment_process_id) AS max_progress_subquery
                                ON application_id = talent_recruitment_process_id"""
            ],
        ),
        SubQueryConfig(
            name="reserve_interview",
            add_date_field="add_date",
            event_date_field="event_date",
            where_conditions=["node_type = 30"],
        ),
        SubQueryConfig(
            name="offer",
            add_date_field="add_date",
            event_date_field="event_date",
            where_conditions=["node_type = 40"],
        ),
        SubQueryConfig(
            name="offer_accept",
            add_date_field="add_date",
            event_date_field="event_date",
            where_conditions=["node_type = 41"],
        ),
        SubQueryConfig(
            name="onboard",
            add_date_field="add_date",
            event_date_field="event_date",
            where_conditions=["node_type = 60"],
        ),
        SubQueryConfig(
            name="eliminate",
            add_date_field="add_date",
            event_date_field="event_date",
            where_conditions=["node_type = -1"],
        ),
    ]
    return subqueries
