#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
StarRocks 物化视图生成器
支持灵活配置子查询、指标、JOIN条件和WHERE条件
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from metrics_config import MetricConfig, get_all_merged_metrics_configs
from sub_query_config import SubQueryConfig, generate_sub_query_config


@dataclass
class ViewConfig:
    """视图配置"""

    view_name: str
    schema_name: str
    base_table: str
    distribution_key: str
    refresh_interval: str
    common_fields: List[str]  # 公共字段
    metrics: List[MetricConfig]  # 所有指标定义
    subqueries: List[SubQueryConfig]  # 子查询配置


class MaterializedViewGenerator:
    """物化视图生成器"""

    def __init__(self, config: ViewConfig):
        self.config = config
        self.metric_map = {m.alias: m for m in config.metrics}

    def generate_subquery(self, subquery_config: SubQueryConfig) -> str:
        """生成单个子查询"""
        lines = []

        # SELECT 子句
        lines.append("(SELECT " + ",".join(self.config.common_fields) + ",")

        # add_date 和 event_date
        lines.append(f"       {subquery_config.add_date_field} AS add_date,")
        lines.append(f"       {subquery_config.event_date_field} AS event_date,")
        # user_roles
        lines.append(f"       BITMAP_AGG(user_role) AS user_roles,")

        # 生成所有指标
        for i, metric in enumerate(self.config.metrics):
            # 如果 metric 的 sub_query 不在 self.config.subqueries 中，则跳过
            if metric.sub_query not in [s.name for s in self.config.subqueries]:
                continue
            if metric.sub_query == subquery_config.name:
                # 这个子查询包含此指标，使用实际表达式
                expr = metric.expression
                alias = metric.alias
            else:
                # 这个子查询不包含此指标，使用默认值
                expr = metric.default_value
                alias = metric.alias

            comma = "," if i < len(self.config.metrics) - 1 else ""
            lines.append(f"       {expr} AS {alias}{comma}")

        # FROM 子句
        lines.append(f" FROM {self.config.base_table} AS application")

        # 额外的 JOIN
        for join in subquery_config.additional_joins:
            lines.append(f"      {join}")

        # WHERE 子句
        if subquery_config.where_conditions:
            lines.append(" WHERE " + " AND ".join(subquery_config.where_conditions))

        # GROUP BY 子句
        group_by_fields = (
            self.config.common_fields
            + ["add_date", "event_date"]
            + subquery_config.group_by_additions
        )
        lines.append(
            " GROUP BY "
            + ",".join([f"          {field}" for field in group_by_fields]).replace(
                "          ", "", 1
            )
        )

        lines.append(")")

        return "\n".join(lines)

    def generate_view_sql(self) -> str:
        """生成完整的物化视图 SQL"""
        lines = []

        # 视图头部
        lines.append("CREATE")
        lines.append(
            f"MATERIALIZED VIEW IF NOT EXISTS {self.config.schema_name}.{self.config.view_name}"
        )
        lines.append(f"       DISTRIBUTED BY HASH (`{self.config.distribution_key}`)")
        lines.append("REFRESH")
        lines.append(f"       ASYNC EVERY (INTERVAL {self.config.refresh_interval})")
        lines.append("ORDER BY (add_date, event_date)")
        # lines.append("PROPERTIES ( 'session.enable_spill'='true' )")
        lines.append("AS")

        # 外层 SELECT
        lines.append(
            "SELECT * "
        )
        lines.append("")
        lines.append("FROM ((")

        # 生成所有子查询
        for i, subquery_config in enumerate(self.config.subqueries):
            if i > 0:
                lines.append("      UNION ALL")

            subquery_sql = self.generate_subquery(subquery_config)
            # 添加缩进
            indented_lines = []
            for line in subquery_sql.split("\n"):
                if line.strip():
                    indented_lines.append("          " + line)
                else:
                    indented_lines.append("")
            lines.extend(indented_lines)

        lines.append(")) AS final_table;")

        return "\n".join(lines)


def create_default_config() -> ViewConfig:
    """创建默认配置（基于现有的 view_application_kpi）"""

    # 定义所有指标
    metrics = get_all_merged_metrics_configs()

    # 定义子查询
    subqueries = generate_sub_query_config()

    return ViewConfig(
        view_name="mv_application_kpi",
        schema_name="ods_apn",
        base_table="view_application_v2",
        distribution_key="user_id",
        refresh_interval="10 MINUTE",
        common_fields=[
            "tenant_id",
            "company_id",
            "job_id",
            "job_pteam_id",
            "team_id",
            "team_name",
            "user_id",
            "user_name",
            "user_activated"
        ],
        metrics=metrics,
        subqueries=subqueries,
    )


def main():
    """主函数"""
    # 创建默认配置
    config = create_default_config()

    # 生成器
    generator = MaterializedViewGenerator(config)

    # 生成 SQL
    sql = generator.generate_view_sql()

    # 输出到文件
    output_file = "generated_view_application_kpi.sql"
    with open(output_file, "w", encoding="utf-8") as f:
        f.write(sql)

    print(f"\n物化视图 SQL 已生成到: {output_file}")


if __name__ == "__main__":
    main()
