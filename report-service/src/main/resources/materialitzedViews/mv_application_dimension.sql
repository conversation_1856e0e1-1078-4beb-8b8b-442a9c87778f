-- 流程相关的维度表

CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_dimension
       DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
AS
SELECT trp.tenant_id,
       c.id                 AS company_id,
       c.full_business_name AS company_name,
       j.id                 AS job_id,
       j.title              AS job_title,
       j.pteam_id           AS job_pteam_id,
       trp.id               AS talent_recruitment_process_id,
       trp.talent_id        AS talent_id,
       trp.ai_score         AS ai_score,
       rf.id                AS recommend_feedback_id
FROM talent_recruitment_process AS trp
         INNER JOIN recruitment_process AS rp ON trp.recruitment_process_id = rp.id
         INNER JOIN job AS j ON trp.job_id = j.id
         INNER JOIN company AS c ON j.company_id = c.id
         LEFT OUTER JOIN (SELECT DISTINCT if((rf.reason = 'UNLOCK_CANDIDATE') OR
                                             (rf.reason = 'ADD_TO_TALENT'),
                                             ct.talent_id,
                                             rf.talent_id) AS talent_id,
                                          rf.id,
                                          rf.job_id
                          FROM ods_apn.job_talent_recommend_feedback AS rf
                                   LEFT OUTER JOIN ods_apn.credit_transaction AS ct
                                                   ON ct.profile_id = rf.talent_id
                          WHERE rf.reason IN
                                ('ADD_TO_POSITION', 'ADD_TO_ASSOCIATION_JOB_FOLDER',
                                 'UNLOCK_CANDIDATE',
                                 'ADD_TO_TALENT')) rf
                         ON (rf.job_id = trp.job_id) AND (rf.talent_id = trp.talent_id);