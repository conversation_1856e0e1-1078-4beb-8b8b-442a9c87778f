-- 流程相关的事实表，把各个节点 union 起来

CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_fact
       DISTRIBUTED BY HASH (`talent_recruitment_process_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
PROPERTIES ( 'session.enable_spill'='true' )
AS
SELECT trpn.talent_recruitment_process_id,
       trpn.node_type,
       trpn.node_status,
       submit_job.id           AS node_id,
       0                       AS progress,
       true                    AS final_round,
       submit_job.created_date AS add_date,
       submit_job.created_date AS event_date,
       submit_job.puser_id     AS operator
FROM talent_recruitment_process_submit_to_job AS submit_job
         INNER JOIN talent_recruitment_process_node AS trpn
                    ON trpn.talent_recruitment_process_id =
                       submit_job.talent_recruitment_process_id AND
                       trpn.node_type = 10
UNION ALL
SELECT trpn.talent_recruitment_process_id,
       trpn.node_type,
       trpn.node_status,
       submit_client.id           AS node_id,
       0                          AS progress,
       true                       AS final_round,
       submit_client.created_date AS add_date,
       submit_client.submit_time  AS event_date,
       submit_client.puser_id     AS operator
FROM talent_recruitment_process_submit_to_client AS submit_client
         INNER JOIN talent_recruitment_process_node AS trpn
                    ON trpn.talent_recruitment_process_id =
                       submit_client.talent_recruitment_process_id AND
                       trpn.node_type = 20
UNION ALL
SELECT trpn.talent_recruitment_process_id,
       trpn.node_type,
       trpn.node_status,
       interview.id           AS node_id,
       interview.progress     AS progress,
       interview.final_round,
       interview.created_date AS add_date,
       convert_tz(interview.from_time, coalesce(interview.time_zone, 'UTC'),
                  'UTC')      AS event_date,
       interview.puser_id     AS operator
FROM talent_recruitment_process_interview AS interview
         INNER JOIN talent_recruitment_process_node AS trpn
                    ON trpn.talent_recruitment_process_id =
                       interview.talent_recruitment_process_id AND
                       trpn.node_type = 30
UNION ALL
SELECT trpn.talent_recruitment_process_id,
       trpn.node_type,
       trpn.node_status,
       offer.id           AS node_id,
       0                  AS progress,
       true               AS final_round,
       offer.created_date AS add_date,
       offer.created_date AS event_date,
       offer.puser_id     AS operator
FROM talent_recruitment_process_offer AS offer
         INNER JOIN talent_recruitment_process_node AS trpn
                    ON trpn.talent_recruitment_process_id =
                       offer.talent_recruitment_process_id AND trpn.node_type = 40
UNION ALL
SELECT trpn.talent_recruitment_process_id,
       trpn.node_type,
       trpn.node_status,
       offer_accept.id           AS node_id,
       0                         AS progress,
       true                      AS final_round,
       offer_accept.created_date AS add_date,
       offer_accept.created_date AS event_date,
       offer_accept.puser_id     AS operator
FROM talent_recruitment_process_ipg_offer_accept AS offer_accept
         INNER JOIN talent_recruitment_process_node AS trpn
                    ON trpn.talent_recruitment_process_id =
                       offer_accept.talent_recruitment_process_id AND
                       trpn.node_type = 41
UNION ALL
SELECT trpn.talent_recruitment_process_id,
       trpn.node_type,
       trpn.node_status,
       onboard.id                AS node_id,
       0                         AS progress,
       true                      AS final_round,
       onboard.created_date      AS add_date,
       onboard_date.onboard_date AS event_date,
       onboard.puser_id          AS operator
FROM talent_recruitment_process_onboard AS onboard
         INNER JOIN talent_recruitment_process_node AS trpn
                    ON trpn.talent_recruitment_process_id =
                       onboard.talent_recruitment_process_id AND
                       trpn.node_type = 60
         INNER JOIN talent_recruitment_process_onboard_date AS onboard_date
                    ON onboard_date.talent_recruitment_process_id =
                       onboard.talent_recruitment_process_id
UNION ALL
SELECT eliminate.talent_recruitment_process_id,
       -1                     AS node_type,
       4                      AS node_status,
       eliminate.id           AS node_id,
       0                      AS progress,
       true                   AS final_round,
       eliminate.created_date AS add_date,
       eliminate.created_date AS event_date,
       eliminate.puser_id     AS operator
FROM talent_recruitment_process_eliminate AS eliminate