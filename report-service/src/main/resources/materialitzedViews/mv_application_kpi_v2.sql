-- 流程相关指标的物化视图，包含流程相关指标的聚合计算, 用于 E1 报表查询

CREATE
MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_application_kpi_v2
       DISTRIBUTED BY HASH (`user_id`)
REFRESH
       ASYNC EVERY (INTERVAL 10 MINUTE)
ORDER BY (add_date, event_date)
AS
SELECT base.tenant_id,
       base.company_id,
       base.job_id,
       base.job_pteam_id,
       hierarchy.parent_team_id AS team_id,
       hierarchy.parent_team_name AS team_name,
       hierarchy.parent_team_parent_id AS team_parent_id,
       hierarchy.parent_team_level AS team_level,
       hierarchy.parent_team_is_leaf AS team_is_leaf,
       base.user_id,
       base.user_name,
       base.user_activated,
       base.add_date,
       base.event_date,
       BITMAP_UNION(base.user_roles)                                        AS user_roles,
       BITMAP_UNION(base.submit_to_job_countNum)                            AS submit_to_job_countNum,
       BITMAP_UNION(base.submit_to_job_current_countNum)                    AS submit_to_job_current_countNum,
       BITMAP_UNION(base.submit_to_job_aiRecommendCountNum)                 AS submit_to_job_aiRecommendCountNum,
       BITMAP_UNION(base.submit_to_job_currentAiRecommendNum)               AS submit_to_job_currentAiRecommendNum,
       BITMAP_UNION(base.submit_to_job_precisionAiRecommendNum)             AS submit_to_job_precisionAiRecommendNum,
       BITMAP_UNION(base.submit_to_job_currentPrecisionAiRecommendNum)      AS submit_to_job_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.submit_to_job_currentStayedOver)                   AS submit_to_job_currentStayedOver,
       BITMAP_UNION(base.submit_to_job_stayedOver)                          AS submit_to_job_stayedOver,
       BITMAP_UNION(base.submit_to_client_countNum)                         AS submit_to_client_countNum,
       BITMAP_UNION(base.submit_to_client_current_countNum)                 AS submit_to_client_current_countNum,
       BITMAP_UNION(base.submit_to_client_aiRecommendCountNum)              AS submit_to_client_aiRecommendCountNum,
       BITMAP_UNION(base.submit_to_client_currentAiRecommendNum)            AS submit_to_client_currentAiRecommendNum,
       BITMAP_UNION(base.submit_to_client_precisionAiRecommendNum)          AS submit_to_client_precisionAiRecommendNum,
       BITMAP_UNION(base.submit_to_client_currentPrecisionAiRecommendNum)   AS submit_to_client_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.submit_to_client_currentStayedOver)                AS submit_to_client_currentStayedOver,
       BITMAP_UNION(base.submit_to_client_stayedOver)                       AS submit_to_client_stayedOver,
       BITMAP_UNION(base.interview1)                                        AS interview1,
       BITMAP_UNION(base.interview2)                                        AS interview2,
       BITMAP_UNION(base.current_interview1)                                AS current_interview1,
       BITMAP_UNION(base.current_interview2)                                AS current_interview2,
       BITMAP_UNION(base.two_or_more_interviews)                            AS two_or_more_interviews,
       BITMAP_UNION(base.current_two_or_more_interviews)                    AS current_two_or_more_interviews,
       BITMAP_UNION(base.interview_final)                                   AS interview_final,
       BITMAP_UNION(base.current_interview_final)                           AS current_interview_final,
       BITMAP_UNION(base.interview_total)                                   AS interview_total,
       BITMAP_UNION(base.current_interview_total)                           AS current_interview_total,
       BITMAP_UNION(base.unique_interview_talents)                          AS unique_interview_talents,
       BITMAP_UNION(base.interview_total_process)                           AS interview_total_process,
       BITMAP_UNION(base.current_interview_total_process)                   AS current_interview_total_process,
       BITMAP_UNION(base.interviewTotalAiRecommendNum)                      AS interviewTotalAiRecommendNum,
       BITMAP_UNION(base.currentInterviewTotalAiRecommendNum)               AS currentInterviewTotalAiRecommendNum,
       BITMAP_UNION(base.interviewTotalProcessAiRecommendNum)               AS interviewTotalProcessAiRecommendNum,
       BITMAP_UNION(base.currentInterviewTotalProcessAiRecommendNum)        AS currentInterviewTotalProcessAiRecommendNum,
       BITMAP_UNION(base.interview1AiRecommendNum)                          AS interview1AiRecommendNum,
       BITMAP_UNION(base.interview2AiRecommendNum)                          AS interview2AiRecommendNum,
       BITMAP_UNION(base.currentInterview1AiRecommendNum)                   AS currentInterview1AiRecommendNum,
       BITMAP_UNION(base.currentInterview2AiRecommendNum)                   AS currentInterview2AiRecommendNum,
       BITMAP_UNION(base.twoOrMoreInterviewsAiRecommendNum)                 AS twoOrMoreInterviewsAiRecommendNum,
       BITMAP_UNION(base.currentTwoOrMoreInterviewsAiRecommendNum)          AS currentTwoOrMoreInterviewsAiRecommendNum,
       BITMAP_UNION(base.interviewFinalAiRecommendNum)                      AS interviewFinalAiRecommendNum,
       BITMAP_UNION(base.currentInterviewFinalAiRecommendNum)               AS currentInterviewFinalAiRecommendNum,
       BITMAP_UNION(base.interviewTotalPrecisionAiRecommendNum)             AS interviewTotalPrecisionAiRecommendNum,
       BITMAP_UNION(base.currentInterviewTotalPrecisionAiRecommendNum)      AS currentInterviewTotalPrecisionAiRecommendNum,
       BITMAP_UNION(base.interviewNumProcessPrecisionAIRecommend)           AS interviewNumProcessPrecisionAIRecommend,
       BITMAP_UNION(base.currentInterviewNumProcessPrecisionAIRecommend)    AS currentInterviewNumProcessPrecisionAIRecommend,
       BITMAP_UNION(base.interview1PrecisionAiRecommendNum)                 AS interview1PrecisionAiRecommendNum,
       BITMAP_UNION(base.currentInterview1PrecisionAiRecommendNum)          AS currentInterview1PrecisionAiRecommendNum,
       BITMAP_UNION(base.interview2PrecisionAiRecommendNum)                 AS interview2PrecisionAiRecommendNum,
       BITMAP_UNION(base.currentInterview2PrecisionAiRecommendNum)          AS currentInterview2PrecisionAiRecommendNum,
       BITMAP_UNION(base.twoOrMoreInterviewsPrecisionAiRecommendNum)        AS twoOrMoreInterviewsPrecisionAiRecommendNum,
       BITMAP_UNION(base.currentTwoOrMoreInterviewsPrecisionAiRecommendNum) AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
       BITMAP_UNION(base.interviewFinalPrecisionAiRecommendNum)             AS interviewFinalPrecisionAiRecommendNum,
       BITMAP_UNION(base.currentInterviewFinalPrecisionAiRecommendNum)      AS currentInterviewFinalPrecisionAiRecommendNum,
       BITMAP_UNION(base.reserve_interview_total)                           AS reserve_interview_total,
       BITMAP_UNION(base.reserve_current_interview_total)                   AS reserve_current_interview_total,
       BITMAP_UNION(base.reserve_interview_aiRecommendCountNum)             AS reserve_interview_aiRecommendCountNum,
       BITMAP_UNION(base.reserve_interview_currentAiRecommendNum)           AS reserve_interview_currentAiRecommendNum,
       BITMAP_UNION(base.reserve_interview_precisionAiRecommendNum)         AS reserve_interview_precisionAiRecommendNum,
       BITMAP_UNION(base.reserve_interview_currentPrecisionAiRecommendNum)  AS reserve_interview_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.offer_countNum)                                    AS offer_countNum,
       BITMAP_UNION(base.offer_current_countNum)                            AS offer_current_countNum,
       BITMAP_UNION(base.offer_aiRecommendCountNum)                         AS offer_aiRecommendCountNum,
       BITMAP_UNION(base.offer_currentAiRecommendNum)                       AS offer_currentAiRecommendNum,
       BITMAP_UNION(base.offer_precisionAiRecommendNum)                     AS offer_precisionAiRecommendNum,
       BITMAP_UNION(base.offer_currentPrecisionAiRecommendNum)              AS offer_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.offer_accept_countNum)                             AS offer_accept_countNum,
       BITMAP_UNION(base.offer_accept_current_countNum)                     AS offer_accept_current_countNum,
       BITMAP_UNION(base.offer_accept_aiRecommendCountNum)                  AS offer_accept_aiRecommendCountNum,
       BITMAP_UNION(base.offer_accept_currentAiRecommendNum)                AS offer_accept_currentAiRecommendNum,
       BITMAP_UNION(base.offer_accept_precisionAiRecommendNum)              AS offer_accept_precisionAiRecommendNum,
       BITMAP_UNION(base.offer_accept_currentPrecisionAiRecommendNum)       AS offer_accept_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.onboard_countNum)                                  AS onboard_countNum,
       BITMAP_UNION(base.onboard_current_countNum)                          AS onboard_current_countNum,
       BITMAP_UNION(base.onboard_aiRecommendCountNum)                       AS onboard_aiRecommendCountNum,
       BITMAP_UNION(base.onboard_currentAiRecommendNum)                     AS onboard_currentAiRecommendNum,
       BITMAP_UNION(base.onboard_precisionAiRecommendNum)                   AS onboard_precisionAiRecommendNum,
       BITMAP_UNION(base.onboard_currentPrecisionAiRecommendNum)            AS onboard_currentPrecisionAiRecommendNum,
       BITMAP_UNION(base.eliminate_countNum)                                AS eliminate_countNum,
       BITMAP_UNION(base.eliminate_current_countNum)                        AS eliminate_current_countNum,
       BITMAP_UNION(base.eliminate_aiRecommendCountNum)                     AS eliminate_aiRecommendCountNum,
       BITMAP_UNION(base.eliminate_currentAiRecommendNum)                   AS eliminate_currentAiRecommendNum,
       BITMAP_UNION(base.eliminate_precisionAiRecommendNum)                 AS eliminate_precisionAiRecommendNum,
       BITMAP_UNION(base.eliminate_currentPrecisionAiRecommendNum)          AS eliminate_currentPrecisionAiRecommendNum

FROM (((SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date                                                                 AS add_date,
               event_date                                                               AS event_date,
               BITMAP_AGG(user_role)                                                    AS user_roles,
               BITMAP_AGG(application.node_id)                                          AS submit_to_job_countNum,
               BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS submit_to_job_current_countNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS submit_to_job_aiRecommendCountNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS submit_to_job_currentAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS submit_to_job_precisionAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_AGG(IF(((`application`.`node_status` = 1) AND (`application`.`node_type` = 10)) AND
                             ((TIMESTAMPDIFF(HOUR, `application`.add_date, now())) > 24), 1, NULL))
                                                                                        AS submit_to_job_currentStayedOver,

               BITMAP_AGG(
                       CASE
                           WHEN pn.stayed_add_date IS NOT NULL THEN
                               IF(TIMESTAMPDIFF(HOUR, application.add_date, pn.stayed_add_date) > 24, 1, NULL)
                           WHEN application.node_status = 1 THEN
                               IF(TIMESTAMPDIFF(HOUR, application.add_date, NOW()) > 24, 1, NULL)
                           END
               )
                                                                                        AS submit_to_job_stayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_client_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_current_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_client_stayedOver,
               BITMAP_EMPTY()                                                           AS interview1,
               BITMAP_EMPTY()                                                           AS interview2,
               BITMAP_EMPTY()                                                           AS current_interview1,
               BITMAP_EMPTY()                                                           AS current_interview2,
               BITMAP_EMPTY()                                                           AS two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS current_two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS interview_final,
               BITMAP_EMPTY()                                                           AS current_interview_final,
               BITMAP_EMPTY()                                                           AS interview_total,
               BITMAP_EMPTY()                                                           AS current_interview_total,
               BITMAP_EMPTY()                                                           AS unique_interview_talents,
               BITMAP_EMPTY()                                                           AS interview_total_process,
               BITMAP_EMPTY()                                                           AS current_interview_total_process,
               BITMAP_EMPTY()                                                           AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_current_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_countNum,
               BITMAP_EMPTY()                                                           AS offer_current_countNum,
               BITMAP_EMPTY()                                                           AS offer_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS offer_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_countNum,
               BITMAP_EMPTY()                                                           AS offer_accept_current_countNum,
               BITMAP_EMPTY()                                                           AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_countNum,
               BITMAP_EMPTY()                                                           AS onboard_current_countNum,
               BITMAP_EMPTY()                                                           AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_current_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_wide AS application

                 left join (select pn.talent_recruitment_process_id,
                                   IF(MIN(pn.add_date) IS NOT NULL, MIN(pn.add_date),
                                      MAX(eli.add_date)) as stayed_add_date
                            from mv_application_fact pn
                                     left join mv_application_fact eli on eli.talent_recruitment_process_id =
                                                                          pn.talent_recruitment_process_id and
                                                                          eli.node_type = -1
                            where pn.node_type > 10
                            group by pn.talent_recruitment_process_id) pn
                           on pn.talent_recruitment_process_id = application.talent_recruitment_process_id

        WHERE application.node_type = 10
        GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
                 add_date, event_date)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date                                                                 AS add_date,
               event_date                                                               AS event_date,
               BITMAP_AGG(user_role)                                                    AS user_roles,
               BITMAP_EMPTY()                                                           AS submit_to_job_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_current_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_job_stayedOver,
               BITMAP_AGG(application.node_id)                                          AS submit_to_client_countNum,
               BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS submit_to_client_current_countNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS submit_to_client_aiRecommendCountNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS submit_to_client_currentAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS submit_to_client_precisionAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_AGG(IF(((`application`.`node_status` = 1) AND (`application`.`node_type` = 20)) AND
                             ((TIMESTAMPDIFF(HOUR, `application`.add_date, now())) > 72), 1, NULL))
                                                                                        AS submit_to_client_currentStayedOver,

               BITMAP_AGG(
                       CASE
                           WHEN pn.stayed_add_date IS NOT NULL THEN
                               IF(TIMESTAMPDIFF(HOUR, application.add_date, pn.stayed_add_date) > 72, 1, NULL)
                           WHEN application.node_status = 1 THEN
                               IF(TIMESTAMPDIFF(HOUR, application.add_date, NOW()) > 72, 1, NULL)
                           END
               )
                                                                                        AS submit_to_client_stayedOver,
               BITMAP_EMPTY()                                                           AS interview1,
               BITMAP_EMPTY()                                                           AS interview2,
               BITMAP_EMPTY()                                                           AS current_interview1,
               BITMAP_EMPTY()                                                           AS current_interview2,
               BITMAP_EMPTY()                                                           AS two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS current_two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS interview_final,
               BITMAP_EMPTY()                                                           AS current_interview_final,
               BITMAP_EMPTY()                                                           AS interview_total,
               BITMAP_EMPTY()                                                           AS current_interview_total,
               BITMAP_EMPTY()                                                           AS unique_interview_talents,
               BITMAP_EMPTY()                                                           AS interview_total_process,
               BITMAP_EMPTY()                                                           AS current_interview_total_process,
               BITMAP_EMPTY()                                                           AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_current_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_countNum,
               BITMAP_EMPTY()                                                           AS offer_current_countNum,
               BITMAP_EMPTY()                                                           AS offer_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS offer_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_countNum,
               BITMAP_EMPTY()                                                           AS offer_accept_current_countNum,
               BITMAP_EMPTY()                                                           AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_countNum,
               BITMAP_EMPTY()                                                           AS onboard_current_countNum,
               BITMAP_EMPTY()                                                           AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_current_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_wide AS application

                 left join (select pn.talent_recruitment_process_id,
                                   IF(MIN(pn.add_date) IS NOT NULL, MIN(pn.add_date),
                                      MAX(eli.add_date)) as stayed_add_date
                            from mv_application_fact pn
                                     left join mv_application_fact eli on eli.talent_recruitment_process_id =
                                                                          pn.talent_recruitment_process_id and
                                                                          eli.node_type = -1
                            where pn.node_type > 20
                            group by pn.talent_recruitment_process_id) pn
                           on pn.talent_recruitment_process_id = application.talent_recruitment_process_id

        WHERE application.node_type = 20
        GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
                 add_date, event_date)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date              AS add_date,
               event_date            AS event_date,
               BITMAP_AGG(user_role) AS user_roles,
               BITMAP_EMPTY()        AS submit_to_job_countNum,
               BITMAP_EMPTY()        AS submit_to_job_current_countNum,
               BITMAP_EMPTY()        AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY()        AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY()        AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY()        AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()        AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY()        AS submit_to_job_stayedOver,
               BITMAP_EMPTY()        AS submit_to_client_countNum,
               BITMAP_EMPTY()        AS submit_to_client_current_countNum,
               BITMAP_EMPTY()        AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY()        AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY()        AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY()        AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()        AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY()        AS submit_to_client_stayedOver,

               BITMAP_AGG(CASE
                              WHEN application.progress = 1 AND node_type = 30
                                  THEN node_id
                   END)              AS interview1,

               BITMAP_AGG(CASE
                              WHEN application.progress = 2
                                  AND node_type = 30
                                  THEN node_id
                   END)              AS interview2,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 1
                                      AND node_status = 1
                                      AND node_type = 30
                                      AND max_progress_subquery.progress = 1
                                  ) THEN node_id
                   END)              AS current_interview1,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 2
                                      AND node_status = 1
                                      AND node_type = 30
                                      AND max_progress_subquery.progress = 2
                                  ) THEN node_id
                   END)              AS current_interview2,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress >= 2
                                      AND node_type = 30
                                      AND final_round <> 1
                                  ) THEN node_id
                   END)              AS two_or_more_interviews,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress >= 2
                                      AND final_round <> 1
                                      AND node_status = 1
                                      AND node_type = 30
                                  ) THEN node_id
                   END)              AS current_two_or_more_interviews,

               BITMAP_AGG(CASE
                              WHEN
                                  final_round = 1
                                      AND node_type = 30
                                  THEN node_id
                   END)              AS interview_final,
               BITMAP_AGG(CASE
                              WHEN (
                                  final_round = 1
                                      AND node_status = 1
                                      AND node_type = 30
                                  ) THEN node_id
                   END)              AS current_interview_final,
               BITMAP_AGG(node_id)   AS interview_total,
               BITMAP_AGG(CASE
                              WHEN (
                                  node_status = 1
                                      AND node_type = 30
                                  ) THEN node_id
                   END)              AS current_interview_total,
               BITMAP_AGG(talent_id) AS unique_interview_talents,

               BITMAP_AGG(talent_recruitment_process_id)
                                     AS interview_total_process,

               BITMAP_AGG(CASE
                              WHEN (
                                  node_status = 1
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS current_interview_total_process,

               BITMAP_AGG(CASE
                              WHEN (
                                  ai_score IS NOT NULL
                                      AND node_type = 30
                                  ) THEN node_id
                   END)              AS interviewTotalAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  ai_score IS NOT NULL
                                      AND node_status = 1
                                      AND node_type = 30
                                  ) THEN node_id
                   END)              AS currentInterviewTotalAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  ai_score IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS interviewTotalProcessAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  ai_score IS NOT NULL
                                      AND node_status = 1
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS currentInterviewTotalProcessAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN
                                  application.progress = 1
                                      AND ai_score IS NOT NULL
                                  THEN talent_recruitment_process_id
                   END)
                                     AS interview1AiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN
                                  application.progress = 2
                                      AND ai_score IS NOT NULL
                                  THEN talent_recruitment_process_id
                   END)
                                     AS interview2AiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 1
                                      AND node_status = 1
                                      AND max_progress_subquery.progress = 1
                                      AND ai_score IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS currentInterview1AiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 2
                                      AND node_status = 1
                                      AND max_progress_subquery.progress = 2
                                      AND ai_score IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS currentInterview2AiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress >= 2
                                      AND final_round <> 1
                                      AND node_type = 30
                                      AND ai_score IS NOT NULL
                                  ) THEN node_id
                   END)              AS twoOrMoreInterviewsAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress >= 2
                                      AND final_round <> 1
                                      AND node_status = 1
                                      AND node_type = 30
                                      AND ai_score IS NOT NULL
                                  ) THEN node_id
                   END)              AS currentTwoOrMoreInterviewsAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN final_round = 1 AND ai_score IS NOT NULL
                                  THEN talent_recruitment_process_id
                   END)
                                     AS interviewFinalAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  final_round = 1
                                      AND node_status = 1
                                      AND ai_score IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS currentInterviewFinalAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN recommend_feedback_id IS NOT NULL AND node_type = 30 THEN node_id
                   END)              AS interviewTotalPrecisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  recommend_feedback_id IS NOT NULL
                                      AND node_status = 1
                                      AND node_type = 30
                                  ) THEN node_id
                   END)              AS currentInterviewTotalPrecisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN recommend_feedback_id IS NOT NULL THEN talent_recruitment_process_id
                   END)
                                     AS interviewNumProcessPrecisionAIRecommend,

               BITMAP_AGG(CASE
                              WHEN (
                                  recommend_feedback_id IS NOT NULL
                                      AND node_status = 1
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS currentInterviewNumProcessPrecisionAIRecommend,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 1
                                      AND recommend_feedback_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS interview1PrecisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 1
                                      AND max_progress_subquery.progress = 1
                                      AND node_status = 1
                                      AND recommend_feedback_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS currentInterview1PrecisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 2
                                      AND recommend_feedback_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS interview2PrecisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress = 2
                                      AND max_progress_subquery.progress = 2
                                      AND node_status = 1
                                      AND recommend_feedback_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS currentInterview2PrecisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress >= 2
                                      AND final_round <> 1
                                      AND node_type = 30
                                      AND recommend_feedback_id IS NOT NULL
                                  ) THEN node_id
                   END)              AS twoOrMoreInterviewsPrecisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.progress >= 2
                                      AND final_round <> 1
                                      AND node_status = 1
                                      AND node_type = 30
                                      AND recommend_feedback_id IS NOT NULL
                                  ) THEN node_id
                   END)              AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  final_round = 1
                                      AND recommend_feedback_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS interviewFinalPrecisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  application.final_round = 1
                                      AND node_status = 1
                                      AND recommend_feedback_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)
                                     AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()        AS reserve_interview_total,
               BITMAP_EMPTY()        AS reserve_current_interview_total,
               BITMAP_EMPTY()        AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY()        AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY()        AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY()        AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()        AS offer_countNum,
               BITMAP_EMPTY()        AS offer_current_countNum,
               BITMAP_EMPTY()        AS offer_aiRecommendCountNum,
               BITMAP_EMPTY()        AS offer_currentAiRecommendNum,
               BITMAP_EMPTY()        AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY()        AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()        AS offer_accept_countNum,
               BITMAP_EMPTY()        AS offer_accept_current_countNum,
               BITMAP_EMPTY()        AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY()        AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY()        AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY()        AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()        AS onboard_countNum,
               BITMAP_EMPTY()        AS onboard_current_countNum,
               BITMAP_EMPTY()        AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY()        AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY()        AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY()        AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()        AS eliminate_countNum,
               BITMAP_EMPTY()        AS eliminate_current_countNum,
               BITMAP_EMPTY()        AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY()        AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY()        AS eliminate_precisionAiRecommendNum,
               BITMAP_EMPTY()        AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_wide AS application
                 LEFT OUTER JOIN (SELECT talent_recruitment_process_id AS application_id,
                                         MAX(progress)                 AS progress
                                  FROM mv_application_fact fact
                                  WHERE node_type = 30
                                  GROUP BY talent_recruitment_process_id) AS max_progress_subquery
                                 ON application_id = talent_recruitment_process_id
        WHERE node_type = 30
        GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
                 add_date, event_date)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date                                                                       AS add_date,
               event_date                                                                     AS event_date,
               BITMAP_AGG(user_role)                                                          AS user_roles,
               BITMAP_EMPTY()                                                                 AS submit_to_job_countNum,
               BITMAP_EMPTY()                                                                 AS submit_to_job_current_countNum,
               BITMAP_EMPTY()                                                                 AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY()                                                                 AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY()                                                                 AS submit_to_job_stayedOver,
               BITMAP_EMPTY()                                                                 AS submit_to_client_countNum,
               BITMAP_EMPTY()                                                                 AS submit_to_client_current_countNum,
               BITMAP_EMPTY()                                                                 AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY()                                                                 AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY()                                                                 AS submit_to_client_stayedOver,
               BITMAP_EMPTY()                                                                 AS interview1,
               BITMAP_EMPTY()                                                                 AS interview2,
               BITMAP_EMPTY()                                                                 AS current_interview1,
               BITMAP_EMPTY()                                                                 AS current_interview2,
               BITMAP_EMPTY()                                                                 AS two_or_more_interviews,
               BITMAP_EMPTY()                                                                 AS current_two_or_more_interviews,
               BITMAP_EMPTY()                                                                 AS interview_final,
               BITMAP_EMPTY()                                                                 AS current_interview_final,
               BITMAP_EMPTY()                                                                 AS interview_total,
               BITMAP_EMPTY()                                                                 AS current_interview_total,
               BITMAP_EMPTY()                                                                 AS unique_interview_talents,
               BITMAP_EMPTY()                                                                 AS interview_total_process,
               BITMAP_EMPTY()                                                                 AS current_interview_total_process,
               BITMAP_EMPTY()                                                                 AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS interview1AiRecommendNum,
               BITMAP_EMPTY()                                                                 AS interview2AiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY()                                                                 AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                                 AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                                 AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_AGG(node_id)                                                            AS reserve_interview_total,
               BITMAP_AGG(IF(node_status = 1, node_id, NULL))                                 AS reserve_current_interview_total,
               BITMAP_AGG(IF((ai_score IS NOT NULL), application.node_id, NULL))              AS reserve_interview_aiRecommendCountNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1), application.node_id,
                             NULL))                                                           AS reserve_interview_currentAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.node_id, NULL)) AS reserve_interview_precisionAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1), application.node_id,
                             NULL))                                                           AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS offer_countNum,
               BITMAP_EMPTY()                                                                 AS offer_current_countNum,
               BITMAP_EMPTY()                                                                 AS offer_aiRecommendCountNum,
               BITMAP_EMPTY()                                                                 AS offer_currentAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS offer_accept_countNum,
               BITMAP_EMPTY()                                                                 AS offer_accept_current_countNum,
               BITMAP_EMPTY()                                                                 AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY()                                                                 AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS onboard_countNum,
               BITMAP_EMPTY()                                                                 AS onboard_current_countNum,
               BITMAP_EMPTY()                                                                 AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY()                                                                 AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS eliminate_countNum,
               BITMAP_EMPTY()                                                                 AS eliminate_current_countNum,
               BITMAP_EMPTY()                                                                 AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY()                                                                 AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS eliminate_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                                 AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_wide AS application
        WHERE node_type = 30
        GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
                 add_date, event_date)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date                                                                 AS add_date,
               event_date                                                               AS event_date,
               BITMAP_AGG(user_role)                                                    AS user_roles,
               BITMAP_EMPTY()                                                           AS submit_to_job_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_current_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_job_stayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_client_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_current_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_client_stayedOver,
               BITMAP_EMPTY()                                                           AS interview1,
               BITMAP_EMPTY()                                                           AS interview2,
               BITMAP_EMPTY()                                                           AS current_interview1,
               BITMAP_EMPTY()                                                           AS current_interview2,
               BITMAP_EMPTY()                                                           AS two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS current_two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS interview_final,
               BITMAP_EMPTY()                                                           AS current_interview_final,
               BITMAP_EMPTY()                                                           AS interview_total,
               BITMAP_EMPTY()                                                           AS current_interview_total,
               BITMAP_EMPTY()                                                           AS unique_interview_talents,
               BITMAP_EMPTY()                                                           AS interview_total_process,
               BITMAP_EMPTY()                                                           AS current_interview_total_process,
               BITMAP_EMPTY()                                                           AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_current_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_AGG(application.node_id)                                          AS offer_countNum,
               BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS offer_current_countNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS offer_aiRecommendCountNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS offer_currentAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS offer_precisionAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_countNum,
               BITMAP_EMPTY()                                                           AS offer_accept_current_countNum,
               BITMAP_EMPTY()                                                           AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_countNum,
               BITMAP_EMPTY()                                                           AS onboard_current_countNum,
               BITMAP_EMPTY()                                                           AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_current_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_wide AS application
        WHERE node_type = 40
        GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
                 add_date, event_date)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date                                                                 AS add_date,
               event_date                                                               AS event_date,
               BITMAP_AGG(user_role)                                                    AS user_roles,
               BITMAP_EMPTY()                                                           AS submit_to_job_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_current_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_job_stayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_client_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_current_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_client_stayedOver,
               BITMAP_EMPTY()                                                           AS interview1,
               BITMAP_EMPTY()                                                           AS interview2,
               BITMAP_EMPTY()                                                           AS current_interview1,
               BITMAP_EMPTY()                                                           AS current_interview2,
               BITMAP_EMPTY()                                                           AS two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS current_two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS interview_final,
               BITMAP_EMPTY()                                                           AS current_interview_final,
               BITMAP_EMPTY()                                                           AS interview_total,
               BITMAP_EMPTY()                                                           AS current_interview_total,
               BITMAP_EMPTY()                                                           AS unique_interview_talents,
               BITMAP_EMPTY()                                                           AS interview_total_process,
               BITMAP_EMPTY()                                                           AS current_interview_total_process,
               BITMAP_EMPTY()                                                           AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_current_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_countNum,
               BITMAP_EMPTY()                                                           AS offer_current_countNum,
               BITMAP_EMPTY()                                                           AS offer_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS offer_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_currentPrecisionAiRecommendNum,
               BITMAP_AGG(application.node_id)                                          AS offer_accept_countNum,
               BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS offer_accept_current_countNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS offer_accept_aiRecommendCountNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS offer_accept_currentAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS offer_accept_precisionAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_countNum,
               BITMAP_EMPTY()                                                           AS onboard_current_countNum,
               BITMAP_EMPTY()                                                           AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_current_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_wide AS application
        WHERE node_type = 41
        GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
                 add_date, event_date)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date                                                                 AS add_date,
               event_date                                                               AS event_date,
               BITMAP_AGG(user_role)                                                    AS user_roles,
               BITMAP_EMPTY()                                                           AS submit_to_job_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_current_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_job_stayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_client_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_current_countNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY()                                                           AS submit_to_client_stayedOver,
               BITMAP_EMPTY()                                                           AS interview1,
               BITMAP_EMPTY()                                                           AS interview2,
               BITMAP_EMPTY()                                                           AS current_interview1,
               BITMAP_EMPTY()                                                           AS current_interview2,
               BITMAP_EMPTY()                                                           AS two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS current_two_or_more_interviews,
               BITMAP_EMPTY()                                                           AS interview_final,
               BITMAP_EMPTY()                                                           AS current_interview_final,
               BITMAP_EMPTY()                                                           AS interview_total,
               BITMAP_EMPTY()                                                           AS current_interview_total,
               BITMAP_EMPTY()                                                           AS unique_interview_talents,
               BITMAP_EMPTY()                                                           AS interview_total_process,
               BITMAP_EMPTY()                                                           AS current_interview_total_process,
               BITMAP_EMPTY()                                                           AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                                                           AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_current_interview_total,
               BITMAP_EMPTY()                                                           AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_countNum,
               BITMAP_EMPTY()                                                           AS offer_current_countNum,
               BITMAP_EMPTY()                                                           AS offer_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS offer_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_countNum,
               BITMAP_EMPTY()                                                           AS offer_accept_current_countNum,
               BITMAP_EMPTY()                                                           AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_AGG(application.node_id)                                          AS onboard_countNum,
               BITMAP_AGG(IF((application.node_status = 1), application.node_id, NULL)) AS onboard_current_countNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS onboard_aiRecommendCountNum,
               BITMAP_AGG(IF((ai_score IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS onboard_currentAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL), application.talent_recruitment_process_id,
                             NULL))                                                     AS onboard_precisionAiRecommendNum,
               BITMAP_AGG(IF((recommend_feedback_id IS NOT NULL AND application.node_status = 1),
                             application.talent_recruitment_process_id,
                             NULL))                                                     AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_current_countNum,
               BITMAP_EMPTY()                                                           AS eliminate_aiRecommendCountNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_precisionAiRecommendNum,
               BITMAP_EMPTY()                                                           AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_wide AS application
        WHERE node_type = 60
        GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
                 add_date, event_date)
       UNION ALL
       (SELECT tenant_id,
               company_id,
               job_id,
               job_pteam_id,
               team_id,
               team_name,
               user_id,
               user_name,
               user_activated,
               add_date                        AS add_date,
               event_date                      AS event_date,
               BITMAP_AGG(user_role)           AS user_roles,
               BITMAP_EMPTY()                  AS submit_to_job_countNum,
               BITMAP_EMPTY()                  AS submit_to_job_current_countNum,
               BITMAP_EMPTY()                  AS submit_to_job_aiRecommendCountNum,
               BITMAP_EMPTY()                  AS submit_to_job_currentAiRecommendNum,
               BITMAP_EMPTY()                  AS submit_to_job_precisionAiRecommendNum,
               BITMAP_EMPTY()                  AS submit_to_job_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS submit_to_job_currentStayedOver,
               BITMAP_EMPTY()                  AS submit_to_job_stayedOver,
               BITMAP_EMPTY()                  AS submit_to_client_countNum,
               BITMAP_EMPTY()                  AS submit_to_client_current_countNum,
               BITMAP_EMPTY()                  AS submit_to_client_aiRecommendCountNum,
               BITMAP_EMPTY()                  AS submit_to_client_currentAiRecommendNum,
               BITMAP_EMPTY()                  AS submit_to_client_precisionAiRecommendNum,
               BITMAP_EMPTY()                  AS submit_to_client_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS submit_to_client_currentStayedOver,
               BITMAP_EMPTY()                  AS submit_to_client_stayedOver,
               BITMAP_EMPTY()                  AS interview1,
               BITMAP_EMPTY()                  AS interview2,
               BITMAP_EMPTY()                  AS current_interview1,
               BITMAP_EMPTY()                  AS current_interview2,
               BITMAP_EMPTY()                  AS two_or_more_interviews,
               BITMAP_EMPTY()                  AS current_two_or_more_interviews,
               BITMAP_EMPTY()                  AS interview_final,
               BITMAP_EMPTY()                  AS current_interview_final,
               BITMAP_EMPTY()                  AS interview_total,
               BITMAP_EMPTY()                  AS current_interview_total,
               BITMAP_EMPTY()                  AS unique_interview_talents,
               BITMAP_EMPTY()                  AS interview_total_process,
               BITMAP_EMPTY()                  AS current_interview_total_process,
               BITMAP_EMPTY()                  AS interviewTotalAiRecommendNum,
               BITMAP_EMPTY()                  AS currentInterviewTotalAiRecommendNum,
               BITMAP_EMPTY()                  AS interviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                  AS currentInterviewTotalProcessAiRecommendNum,
               BITMAP_EMPTY()                  AS interview1AiRecommendNum,
               BITMAP_EMPTY()                  AS interview2AiRecommendNum,
               BITMAP_EMPTY()                  AS currentInterview1AiRecommendNum,
               BITMAP_EMPTY()                  AS currentInterview2AiRecommendNum,
               BITMAP_EMPTY()                  AS twoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                  AS currentTwoOrMoreInterviewsAiRecommendNum,
               BITMAP_EMPTY()                  AS interviewFinalAiRecommendNum,
               BITMAP_EMPTY()                  AS currentInterviewFinalAiRecommendNum,
               BITMAP_EMPTY()                  AS interviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS currentInterviewTotalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS interviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                  AS currentInterviewNumProcessPrecisionAIRecommend,
               BITMAP_EMPTY()                  AS interview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS currentInterview1PrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS interview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS currentInterview2PrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS twoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS currentTwoOrMoreInterviewsPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS interviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS currentInterviewFinalPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS reserve_interview_total,
               BITMAP_EMPTY()                  AS reserve_current_interview_total,
               BITMAP_EMPTY()                  AS reserve_interview_aiRecommendCountNum,
               BITMAP_EMPTY()                  AS reserve_interview_currentAiRecommendNum,
               BITMAP_EMPTY()                  AS reserve_interview_precisionAiRecommendNum,
               BITMAP_EMPTY()                  AS reserve_interview_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS offer_countNum,
               BITMAP_EMPTY()                  AS offer_current_countNum,
               BITMAP_EMPTY()                  AS offer_aiRecommendCountNum,
               BITMAP_EMPTY()                  AS offer_currentAiRecommendNum,
               BITMAP_EMPTY()                  AS offer_precisionAiRecommendNum,
               BITMAP_EMPTY()                  AS offer_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS offer_accept_countNum,
               BITMAP_EMPTY()                  AS offer_accept_current_countNum,
               BITMAP_EMPTY()                  AS offer_accept_aiRecommendCountNum,
               BITMAP_EMPTY()                  AS offer_accept_currentAiRecommendNum,
               BITMAP_EMPTY()                  AS offer_accept_precisionAiRecommendNum,
               BITMAP_EMPTY()                  AS offer_accept_currentPrecisionAiRecommendNum,
               BITMAP_EMPTY()                  AS onboard_countNum,
               BITMAP_EMPTY()                  AS onboard_current_countNum,
               BITMAP_EMPTY()                  AS onboard_aiRecommendCountNum,
               BITMAP_EMPTY()                  AS onboard_currentAiRecommendNum,
               BITMAP_EMPTY()                  AS onboard_precisionAiRecommendNum,
               BITMAP_EMPTY()                  AS onboard_currentPrecisionAiRecommendNum,
               BITMAP_AGG(application.node_id) AS eliminate_countNum,
               BITMAP_AGG(node_id)             AS eliminate_current_countNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  ai_score IS NOT NULL
                                      AND node_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)                        AS eliminate_aiRecommendCountNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  ai_score IS NOT NULL
                                      AND node_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)                        AS eliminate_currentAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  recommend_feedback_id IS NOT NULL
                                      AND node_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)                        AS eliminate_precisionAiRecommendNum,

               BITMAP_AGG(CASE
                              WHEN (
                                  recommend_feedback_id IS NOT NULL
                                      AND node_id IS NOT NULL
                                  ) THEN talent_recruitment_process_id
                   END)                        AS eliminate_currentPrecisionAiRecommendNum
        FROM mv_application_wide AS application
        WHERE node_type = -1
        GROUP BY tenant_id, company_id, job_id, job_pteam_id, team_id, team_name, user_id, user_name, user_activated,
                 add_date, event_date))) AS base
         INNER JOIN ods_apn.mv_team_hierarchy AS hierarchy ON base.team_id = hierarchy.child_team_id
GROUP BY base.tenant_id,
         base.company_id,
         base.job_id,
         base.job_pteam_id,
         hierarchy.parent_team_id,
         hierarchy.parent_team_name,
         hierarchy.parent_team_parent_id,
         hierarchy.parent_team_level,
         hierarchy.parent_team_is_leaf,
         base.user_id,
         base.user_name,
         base.user_activated,
         base.add_date,
         base.event_date;