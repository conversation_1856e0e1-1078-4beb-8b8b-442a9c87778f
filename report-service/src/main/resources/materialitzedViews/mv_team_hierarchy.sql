-- 团队层级关系物化视图
-- 预计算所有团队的父子关系，避免在其他物化视图中重复计算
CREATE MATERIALIZED VIEW IF NOT EXISTS ods_apn.mv_team_hierarchy
    DISTRIBUTED BY HASH (`child_team_id`)
REFRESH
    ASYNC
ORDER BY (child_team_id, parent_team_id)
AS
SELECT 
    child.id AS child_team_id,
    child.name AS child_team_name,
    child.code AS child_team_code,
    parent.id AS parent_team_id,
    parent.name AS parent_team_name,
    parent.code AS parent_team_code,
    parent.parent_id AS parent_team_parent_id,
    parent.level AS parent_team_level,
    parent.is_leaf AS parent_team_is_leaf,
    child.tenant_id
FROM ods_apn.permission_team child
INNER JOIN ods_apn.permission_team parent ON (
    child.code LIKE CONCAT(parent.code, '%')
    OR child.code = parent.code
)
WHERE child.deleted = 0 
AND parent.deleted = 0;
