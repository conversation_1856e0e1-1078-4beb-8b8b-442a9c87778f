package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

public class ReportParamDto implements Serializable {

    private String fromDate;

    private String toDate;

    private JobType jobType;

    private String jobId;

    private String activityId;

    private Integer size = 10000;

    private String company = "all";

    private String recruiterId = "-1";

    private String timeZone = "America/Los_Angeles";

    private String divisionId = "all";

    private String userCountry = "all";

    private String jobCountry = "all";

    private static final String DEFAULT_TIMEZONE = "America/Los_Angeles";

    private static final String ALL = "all";

    public ReportParamDto() {}

    public String getFromDate() {
        return fromDate;
    }

    public ReportParamDto fromDate(String fromDate) {
        this.fromDate = fromDate;
        return this;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public ReportParamDto toDate(String toDate) {
        this.toDate = toDate;
        return this;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public JobType getJobType() {
        return jobType;
    }

    public ReportParamDto jobType(JobType jobType) {
        this.jobType = jobType;
        return this;
    }

    public void setJobType(JobType jobType) {
        this.jobType = jobType;
    }

    public String getJobId() {
        return jobId;
    }

    public ReportParamDto jobId(String jobId) {
        this.jobId = jobId;
        return this;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public String getActivityId() {
        return activityId;
    }

    public ReportParamDto activityId(String activityId) {
        this.activityId = activityId;
        return this;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public String getCompany() {
        return company;
    }

    public ReportParamDto company(String company) {
        this.company = StringUtils.isBlank(company) ? ALL : company;
        return this;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getRecruiterId() {
        return recruiterId;
    }

    public ReportParamDto recruiterId(String recruiterId) {
        this.recruiterId = StringUtils.isBlank(recruiterId) ? "-1" : recruiterId;
        return this;
    }

    public void setRecruiterId(String recruiterId) {
        this.recruiterId = recruiterId;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public ReportParamDto timeZone(String timeZone) {
        this.timeZone = StringUtils.isBlank(timeZone) ? DEFAULT_TIMEZONE : timeZone;
        return this;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getDivisionId() {
        return divisionId;
    }

    public ReportParamDto divisionId(String divisionId) {
        this.divisionId = StringUtils.isBlank(divisionId) ? ALL : divisionId;
        return this;
    }

    public void setDivisionId(String divisionId) {
        this.divisionId = divisionId;
    }

    public String getUserCountry() {
        return userCountry;
    }

    public ReportParamDto userCountry(String userCountry) {
        this.userCountry = StringUtils.isBlank(userCountry) ? ALL : userCountry;
        return this;
    }

    public void setUserCountry(String userCountry) {
        this.userCountry = userCountry;
    }

    public String getJobCountry() {
        return jobCountry;
    }

    public ReportParamDto jobCountry(String jobCountry) {
        this.jobCountry = StringUtils.isBlank(jobCountry) ? ALL : jobCountry;
        return this;
    }

    public void setJobCountry(String jobCountry) {
        this.jobCountry = jobCountry;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    @Override
    public String toString() {
        return "ReportParam{" +
            "fromDate='" + fromDate + '\'' +
            ", toDate='" + toDate + '\'' +
            ", jobType=" + jobType +
            ", jobId='" + jobId + '\'' +
            ", activityId='" + activityId + '\'' +
            ", company='" + company + '\'' +
            ", recruiterId='" + recruiterId + '\'' +
            ", timeZone='" + timeZone + '\'' +
            ", divisionId='" + divisionId + '\'' +
            ", userCountry='" + userCountry + '\'' +
            ", jobCountry='" + jobCountry + '\'' +
            '}';
    }
}
