package com.altomni.apn.report.domain.vo.e5;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailDetailReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<EmailRecipientVO> recipients;

    private String subject;

    private Instant sentTime;

    private Long conversationCount;

    private Long createdBy;

    private Long userId;

}
