package com.altomni.apn.report.dto.voip;

import com.altomni.apn.report.domain.enumeration.VoipDetailReportType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class VoipDetailReportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private LocalDate startTime;

    private LocalDate endTime;

    private String timeZone;

    private Long userId;

    private Set<Long> callResultList;

    private Set<Long> callTypeList;

    private VoipDetailReportType reportType;

    private Long tenantId;

    private Long teamId;

    private List<Long> userIdList;

    private List<Long> teamIdList;

}
