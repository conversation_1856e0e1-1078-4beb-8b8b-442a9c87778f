package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentCategoryType;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentCategoryTypeConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportGpVO {

    @Id
    private Long id;

    private String fullName;

    @Convert(converter = AssignmentCategoryTypeConverter.class)
    private AssignmentCategoryType startEmploymentCategory;

    private String jobTitle;

    private String companyName;

    private LocalDate startDate;

    private LocalDate endDate;

    private BigDecimal billRate;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType billRateUnit;

    private BigDecimal salary;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType salaryUnit;

    private String pecSymbol;

    private String becSymbol;

}
