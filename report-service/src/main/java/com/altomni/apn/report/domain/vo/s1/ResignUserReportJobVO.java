package com.altomni.apn.report.domain.vo.s1;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.job.domain.enumeration.JobPriority;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ResignUserReportJobVO extends AbstractAuditingEntity implements Serializable{

    private static final long serialVersionUID = -8813877969704820785L;

    @Id
    private Long id;

    @ApiModelProperty(value = "The tenant id the job belongs. It is injected from the user entity when creating job. Read only.")
    private Long tenantId;

    @ApiModelProperty(value = "Name of the company publish the job")
    private String company;

    @ApiModelProperty(value = "Name of the company publish the job")
    private Long companyId;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    @ApiModelProperty(value = "Job type", allowableValues = "DIRECT_PLACEMENT, CONTRACT, RIGHT_TO_HIRE, FULL_TIME, PART_TIME")
    private JobType jobType;

    @ApiModelProperty(value = "The date the job is posted to ATS or job boards. Read Only.")
    private Instant postingTime;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
    private JobStatus status;

    @ApiModelProperty(value = "If or not this job is private.")
    private boolean isPrivateJob;
}
