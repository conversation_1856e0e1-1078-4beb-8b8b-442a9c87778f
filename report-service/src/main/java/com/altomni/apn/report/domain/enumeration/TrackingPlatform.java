package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum TrackingPlatform implements ConvertedEnum<Integer> {
    LINKED_IN(0),
    LIE_PIN(1);

    private final Integer dbValue;

    TrackingPlatform(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<TrackingPlatform, Integer> resolver = new ReverseEnumResolver<>(TrackingPlatform.class, TrackingPlatform::toDbValue);

    public static TrackingPlatform fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
