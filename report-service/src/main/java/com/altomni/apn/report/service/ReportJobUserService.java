package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.ReportJobByUserVo;
import com.altomni.apn.report.dto.ReportJobParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;

public interface ReportJobUserService extends ReportJobBaseService{

    List<ReportJobByUserVo> getJobsByUserSourceData(ReportJobParam reportParam) throws ExecutionException, InterruptedException;

    void exportJobsByRecruiterByExcel(ReportJobParam reportParam, HttpServletResponse response) throws ExecutionException, InterruptedException;

}
