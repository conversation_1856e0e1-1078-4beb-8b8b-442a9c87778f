package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.ReportUserJobTalentDTO;
import com.altomni.apn.report.domain.vo.s1.ResignUserReportJobVO;
import com.altomni.apn.report.domain.vo.s1.ResignUserReportTalentVO;
import com.altomni.apn.report.dto.ReportJobParam;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportUserJobTalentService {

    /** get user resign data
     * @param reportParam param
     * @return list of Object
     */
    List<ReportUserJobTalentDTO> getUserResignData(ReportJobParam reportParam);

    List<ResignUserReportJobVO> findAllJobByIds(List<Long> jobIdList);

    List<ResignUserReportTalentVO> findAllTalentByIds(List<Long> talentIdList);
}
