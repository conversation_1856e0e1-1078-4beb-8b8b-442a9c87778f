package com.altomni.apn.report.web.rest.dict;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.service.ReportService;
import com.altomni.apn.report.web.rest.vm.ReportFilterOpVM;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.client.model.ChartDataAdhocMetricSchema;
import org.openapitools.client.model.ChartDataFilter;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
@RequiredArgsConstructor
public class DynamicDictResource {

    private final ReportService reportService;

    @GetMapping("/dict/dynamic-reports/aggregates")
    public ResponseEntity<List<String>> getAggregates() {
        log.info("[APN DynamicReport @{}] REST request to get all aggregates", SecurityUtils.getUserId());
        List<String> aggregates = Arrays.stream(ChartDataAdhocMetricSchema.AggregateEnum.values())
            .map(ChartDataAdhocMetricSchema.AggregateEnum::getValue).toList();
        return ResponseEntity.ok().body(aggregates);
    }


    @GetMapping("/dict/dynamic-reports/filters/ops")
    public ResponseEntity<List<ReportFilterOpVM>> getAllFilterOps(@RequestParam(value = "columnType", required = false) String columnType) {
        log.info("[APN DynamicReport @{}] REST request to get all filter ops", SecurityUtils.getUserId());
        List<ChartDataFilter.OpEnum> result = columnType == null ? List.of(ChartDataFilter.OpEnum.values())
            : reportService.getFilterOpsByDataType(columnType);
        return ResponseEntity.ok().body(result.stream().map(ReportFilterOpVM::fromOpEnum).toList());
    }

}
