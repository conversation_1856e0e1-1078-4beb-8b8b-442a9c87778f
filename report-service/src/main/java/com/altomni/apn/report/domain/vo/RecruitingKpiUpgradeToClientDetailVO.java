package com.altomni.apn.report.domain.vo;

import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Entity
public class RecruitingKpiUpgradeToClientDetailVO implements Serializable {

    @Id
    private Long id;

    private String companyName;

    private String salesLeadName;

    private String bdOwnerName;

    private String amName;

    private String coAmName;

    private Instant createdDate;

    private Instant requestDate;

    private Long openJobCount;

    @Transient
    private List<UserCountryVO> coAmList;
}
