package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiUpgradeToClientDetailExcelVO  implements Serializable {

    @ExcelProperty(value = "Company Name", index = 0)
    private String companyName;

    @ExcelProperty(value = "Job Count", index = 1)
    private Long openJobCount;

    @ExcelProperty(value = "AM", index = 2)
    private String amName;

    @ExcelProperty(value = "Co-Account Manager", index = 3)
    private String coAmName;

    @ExcelProperty(value = "SalesLead Owner", index = 4)
    private String salesLeadName;

    @ExcelProperty(value = "BD Owner", index = 5)
    private String bdOwnerName;

    @ExcelProperty(value = "Created Date", index = 6)
    private String createdDate;

    @ExcelProperty(value = "Upgrade Date", index = 7)
    private String requestDate;

}
