package com.altomni.apn.report.repository;

import com.altomni.apn.report.domain.UserAdoptionAlertLarkNotification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface UserAdoptionAlertLarkNotificationRepository extends JpaRepository<UserAdoptionAlertLarkNotification, Long> {

    List<UserAdoptionAlertLarkNotification> findAll();

}
