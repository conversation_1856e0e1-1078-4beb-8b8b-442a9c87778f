package com.altomni.apn.report.service.recruiting;

import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;

public interface RecruitingKpiByUserService {
    /**
     * 查询职位总数详情接口
     *
     * @param searchDto
     * @return
     */
    Page<RecruitingKpiJobDetailVO> searchJobDetailPage(RecruitingKpiJobDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportJobDetailList(RecruitingKpiJobDetailSearchDto searchDto, HttpServletResponse response);

    Page<RecruitingKpiTalentDetailVO> searchTalentDetailPage(RecruitingKpiTalentDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportTalentDetailList(RecruitingKpiTalentDetailSearchDto searchDto, HttpServletResponse response);

    Page<? extends RecruitingKpiApplicationBaseDetailVO> searchApplicationDetailPage(RecruitingKpiApplicationDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportApplicationDetailList(RecruitingKpiApplicationDetailSearchDto searchDto, HttpServletResponse response, String filename);

    Page<RecruitingKpiApplicationNoteDetailVO> searchApplicationNoteDetailPage(RecruitingKpiApplicationNoteDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportApplicationNoteDetailList(RecruitingKpiApplicationNoteDetailSearchDto searchDto, HttpServletResponse response);

    Page<RecruitingKpiApnProNoteDetailVO> searchApnProNoteDetailPage(RecruitingKpiApnProNoteDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportApnProNoteDetailList(RecruitingKpiApnProNoteDetailSearchDto searchDto, HttpServletResponse response);

    Page<RecruitingKpiTalentNoteDetailVO> searchTalentNoteDetailPage(RecruitingKpiTalentNoteDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportTalentNoteDetailList(RecruitingKpiTalentNoteDetailSearchDto searchDto, HttpServletResponse response, String filename, boolean exportAttendees);

    Page<RecruitingKpiUpgradeToClientDetailVO> searchUpgradeToClientDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, Pageable pageable);

    void exportUpgradeToClientDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, HttpServletResponse response);

    void exportCreateCompanyDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, HttpServletResponse response);


    Page<BDReportKpiUserCompanyDetailVO> searchCreateCompanyDetailList(RecruitingKpiUserCompanyDetailSearchDto searchDto, Pageable pageable);

}
