package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.ExpenseReportVO;
import com.altomni.apn.report.dto.ExpenseReportDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.ExecutionException;

public interface ExpenseReportService {

    Page<ExpenseReportVO> getExpenseReport(ExpenseReportDTO reportDTO, Pageable pageable) throws ExecutionException, InterruptedException;

    void exportExpenseReport(ExpenseReportDTO searchDto, HttpServletResponse response);

}
