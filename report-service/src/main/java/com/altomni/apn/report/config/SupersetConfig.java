package com.altomni.apn.report.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.client.ApiClient;
import org.openapitools.client.api.ChartsApi;
import org.openapitools.client.api.DatasetsApi;
import org.openapitools.client.api.SecurityApi;
import org.openapitools.client.model.ApiV1QueryStopPost200Response;
import org.openapitools.client.model.ApiV1SecurityLoginPost200Response;
import org.openapitools.client.model.ApiV1SecurityLoginPostRequest;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


@Slf4j
@Configuration
@RequiredArgsConstructor
public class SupersetConfig {

    private final ApiClient apiClient = new ApiClient();
    private final SecurityApi securityApi = new SecurityApi(apiClient);

    private final SupersetProperties supersetProperties;

    @Bean
    public ApiClient supersetApiClient() {
        apiClient.setBasePath(supersetProperties.getDomain());
        if (supersetProperties.isEnabled()) {
            startRefreshToken();
        }
        return apiClient;
    }

    @Bean
    public DatasetsApi datasetsApi() {
        return new DatasetsApi(apiClient);
    }

    @Bean
    public ChartsApi chartsApi() {
        return new ChartsApi(apiClient);
    }

    private void startRefreshToken() {
        // 创建一个固定频率的线程池,每隔1小时刷新一次token
        ScheduledExecutorService executorService = Executors.newScheduledThreadPool(1);
        executorService.scheduleAtFixedRate(() -> startRefreshToken(0), 0, 1, TimeUnit.HOURS);
    }

    private void startRefreshToken(int retryCount) {
        ApiV1SecurityLoginPostRequest loginRequest = new ApiV1SecurityLoginPostRequest().username(supersetProperties.getUsername())
            .password(supersetProperties.getPassword()).provider(ApiV1SecurityLoginPostRequest.ProviderEnum.DB).refresh(false);

        try {
            ApiV1SecurityLoginPost200Response tokenResponse = securityApi.apiV1SecurityLoginPost(loginRequest);
            String accessToken = tokenResponse.getAccessToken();
            apiClient.setBearerToken(accessToken);
            var csrfTokenResponse = securityApi.apiV1SecurityCsrfTokenGetWithHttpInfo();

            HttpEntity<MultiValueMap<String, String>> requestEntity = buildMultiValueMapHttpEntity(csrfTokenResponse);

            ResponseEntity<String> responseEntity = apiClient.getRestTemplate().exchange(supersetProperties.getDomain() + "/login/", HttpMethod.POST, requestEntity, String.class);

            HttpHeaders responseHeader = responseEntity.getHeaders();
            Optional.ofNullable(responseHeader.get(HttpHeaders.SET_COOKIE)).flatMap(cookies -> cookies.stream().findFirst())
                .ifPresent(cookie -> apiClient.addDefaultHeader(HttpHeaders.COOKIE, cookie));

        } catch (Exception e) {
            if (retryCount < 3) {
                log.error("[APN Report] refresh Superset token fail, will retry {}", retryCount, e);
                try {
                    TimeUnit.SECONDS.sleep(10);
                } catch (InterruptedException ex) {
                    //ignore
                }
                startRefreshToken(retryCount + 1);
            } else {
                //todo send alert
                log.error("[APN Report] refresh Superset token with max retry fail", e);
            }
        }
    }

    private HttpEntity<MultiValueMap<String, String>> buildMultiValueMapHttpEntity(ResponseEntity<ApiV1QueryStopPost200Response> csrfResponse) {
        String csrfToken = Objects.requireNonNull(csrfResponse.getBody()).getResult();
        Optional<String> cookie = Objects.requireNonNull(csrfResponse.getHeaders().get(HttpHeaders.SET_COOKIE)).stream().findFirst();

        MultiValueMap<String, String> requestBody = new LinkedMultiValueMap<>();
        requestBody.add("csrf_token", csrfToken);
        requestBody.add("username", supersetProperties.getUsername());
        requestBody.add("password", supersetProperties.getPassword());

        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE);
        cookie.ifPresent(ck -> headers.set(HttpHeaders.COOKIE, ck));

        return new HttpEntity<>(requestBody, headers);
    }
}
