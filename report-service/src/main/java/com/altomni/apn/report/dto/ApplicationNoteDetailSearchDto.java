package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationNoteDetailSearchDto {

    private String fullName;

    private Long talentId;

    private String jobTitle;

    private Long jobId;

    private JobStatus jobStatus;

    private ReportTableType workflowStatus;

    private Long createdBy;

    private Long lastModifiedBy;

}
