package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonVO;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Stream;

import static com.altomni.apn.report.domain.vo.RecruitingKpiBaseExcelVo.safeLong;
import static com.altomni.apn.report.domain.vo.RecruitingKpiBaseExcelVo.toPercentage;

@Getter
@Setter
public class RecruitingKpiByUserExcelVo implements RecruitingKpiBaseExcelVo {

    @ExcelProperty(value = "Team")
    private String team;

    @ExcelProperty(value = "User")
    private String user;

    @ExcelProperty(value = "Day")
    private String day;

    @ExcelProperty(value = "Week")
    private String week;

    @ExcelProperty(value = "Month")
    private String month;

    @ExcelProperty(value = "Quarter")
    private String quarter;

    @ExcelProperty(value = "Year")
    private String year;

    @ExcelProperty(value = "Sum of Openings")
    private String openings;

    @ExcelProperty(value = "Created Companies")
    private String createdCompaniesNum;

    @ExcelProperty(value = "Upgrade To Client")
    private String upgradeToClient;

    @ExcelProperty(value = "Created Candidates")
    private String talentNum;

    @ExcelProperty(value = "Submitted to Job")
    private String submitToJobNum;

    @ExcelProperty(value = "Submitted to Client")
    private String submitToClientNum;

    @ExcelProperty(value = "Interview Appointments")
    private String interviewAppointments;

    @ExcelProperty(value = "Interview (First Round)")
    private String firstInterviewNum;

    @ExcelProperty(value = "Interview (Second Round)")
    private String secondInterviewNum;

    @ExcelProperty(value = "Two or More Interviews")
    private String twoOrMoreInterviews;

    @ExcelProperty(value = "Interview (Final Round)")
    private String finalInterviewNum;

    @ExcelProperty(value = "Sum of Interview")
    private String interviewNum;

    @ExcelProperty(value = "Offered")
    private String offerNum;

    @ExcelProperty(value = "Offer Accetped")
    private String offerAcceptNum;

    @ExcelProperty(value = "On Boarded")
    private String onboardNum;

    @ExcelProperty(value = "Eliminated")
    private String eliminateNum;

    @ExcelProperty(value = "AI Candidate Interview Conversion Rate")
    private String aiCandidateInterviewConversionRate;

    @ExcelProperty(value = "AI Candidate Onboarding Rate")
    private String aiCandidateOnboardingRate;

    @ExcelProperty(value = "Notes (Initial Candidate Interview)")
    private String iciNum;

    @ExcelProperty(value = "Notes (Call)")
    private String callNoteNum;

    @ExcelProperty(value = "Notes (Email)")
    private String emailNoteNum;

    @ExcelProperty(value = "Notes (In Person)")
    private String personNoteNum;

    @ExcelProperty(value = "Notes (Video Call)")
    private String videoNoteNum;

    @ExcelProperty(value = "Notes (Others)")
    private String otherNoteNum;

    @ExcelProperty(value = "Notes (Pipeline)")
    private String pipelineNoteNum;

    @ExcelProperty(value = "Notes (APN Pro)")
    private String apnProNoteNum;

    public static RecruitingKpiByUserExcelVo ofSearchResult(RecruitingKpiByUserVO vo, RecruitingKpiReportSearchDto searchDto) {
        RecruitingKpiByUserExcelVo excelVo = new RecruitingKpiByUserExcelVo();
        excelVo.setTeam(vo.getTeamName());
        excelVo.setUser(vo.getUserName());

        // 设置基础指标
        excelVo.setOpenings(safeLong(vo.getOpenings()));
        excelVo.setCreatedCompaniesNum(safeLong(vo.getCreatedCompaniesNum()));
        excelVo.setUpgradeToClient(safeLong(vo.getUpgradeToClient()));
        excelVo.setTalentNum(safeLong(vo.getTalentNum()));
        excelVo.setIciNum(safeLong(vo.getIciNum()));
        excelVo.setCallNoteNum(safeLong(vo.getCallNoteNum()));
        excelVo.setEmailNoteNum(safeLong(vo.getEmailNoteNum()));
        excelVo.setPersonNoteNum(safeLong(vo.getPersonNoteNum()));
        excelVo.setVideoNoteNum(safeLong(vo.getVideoNoteNum()));
        excelVo.setOtherNoteNum(safeLong(vo.getOtherNoteNum()));
        excelVo.setPipelineNoteNum(safeLong(vo.getPipelineNoteNum()));
        excelVo.setApnProNoteNum(safeLong(vo.getApnProNoteNum()));

        // 设置时间相关维度
        excelVo.setDay(vo.getGroupByDate());
        excelVo.setWeek(vo.getGroupByDate());
        excelVo.setMonth(vo.getGroupByDate());
        excelVo.setQuarter(vo.getGroupByDate());
        excelVo.setYear(vo.getGroupByDate());

        // 设置流程相关指标
        excelVo.setProcessMetrics(vo, searchDto);

        // 设置转化率指标
        if (searchDto.getAiTalentType() != null) {
            excelVo.setAiCandidateInterviewConversionRate("%s : %s".formatted(toPercentage(vo.getAiInterviewConversionRate()), toPercentage(vo.getOverallInterviewConversionRate())));
            excelVo.setAiCandidateOnboardingRate("%s : %s".formatted(toPercentage(vo.getAiConversionRate()), toPercentage(vo.getOverallConversionRate())));
        }
        return excelVo;
    }

    @Override
    public List<ProcessMetricConfig> getProcessMetricConfigs(RecruitingKpiCommonVO vo) {
        List<ProcessMetricConfig> processMetricConfigs = List.of(
                // 预约面试数
                new ProcessMetricConfig(
                        this::setInterviewAppointments,
                        vo::getInterviewAppointments, vo::getCurrentInterviewAppointments,
                        vo::getInterviewAppointmentsAIRecommend, vo::getInterviewAppointmentsPrecisionAIRecommend
                ),
                // 二轮及以上面试数
                new ProcessMetricConfig(
                        this::setTwoOrMoreInterviews,
                        vo::getTwoOrMoreInterviews, vo::getCurrentTwoOrMoreInterviews,
                        vo::getTwoOrMoreInterviewsAIRecommend, vo::getTwoOrMoreInterviewsPrecisionAIRecommend
                )
        );
        return Stream.concat(getBaseProcessMetricConfigs(vo).stream(), processMetricConfigs.stream()).toList();
    }

    public static List<String> removeHeaders(RecruitingKpiReportSearchDto searchDto) {
        List<String> aiConversionRateHeader = List.of("aiCandidateInterviewConversionRate", "aiCandidateOnboardingRate");
        List<String> removeHeaders = new ArrayList<>();
        // 如果没有选 ai 推荐，则去掉 ai 相关的列
        if (searchDto.getAiTalentType() == null) {
            removeHeaders.addAll(aiConversionRateHeader);
        }
        List<String> dateCol = List.of("day", "week", "month", "quarter", "year");
        Optional<RecruitingKpiGroupByFieldType> dateDimOpt = searchDto.getGroupByFieldList().stream()
                .filter(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains).findFirst();
        // 如果没有选时间维度，去掉时间列
        if (dateDimOpt.isEmpty()) {
            removeHeaders.addAll(dateCol);
        } else {
            // 如果选择了时间维度，则去掉除选择的时间维度以外的列
            List<String> ignoreCol = dateCol.stream().filter(s -> !s.equalsIgnoreCase(dateDimOpt.get().name())).toList();
            removeHeaders.addAll(ignoreCol);
        }
        return removeHeaders;
    }

}
