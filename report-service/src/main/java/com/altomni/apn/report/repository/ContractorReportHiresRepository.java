package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ContractorHireVo;
import com.altomni.apn.report.dto.ContractorSearchDto;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.altomni.apn.report.domain.constants.ReportHiresConstants.MULTILINGUAL_MIXED_ORDER_COLUMN_LIST;
import static com.altomni.apn.report.domain.constants.ReportHiresConstants.ORDER_COLUMN_LIST;

@Repository
public class ContractorReportHiresRepository extends BaseCustomRepository{

    public List<ContractorHireVo> searchContractorHiresData(ContractorSearchDto searchDto, Pageable pageable) {
        Map<Integer, Object> paramMap = new HashMap<>();
        String whereSql = doContractorHiresWhereSql(searchDto, paramMap);
        String orderSql = doContractorHiresOrderSql(searchDto.getSort());
        String dateSql = constructContractorHiresDateSql(searchDto, whereSql, orderSql, paramMap);
        return doSearchData(dateSql, ContractorHireVo.class, paramMap, pageable);
    }

    public Long searchContractorHiresCount(ContractorSearchDto searchDto) {
        Map<Integer, Object> paramMap = new HashMap<>();
        String whereSql = doContractorHiresWhereSql(searchDto, paramMap);
        String countSql = constructContractorHiresCountSql(whereSql, paramMap);
        return doSearchCount(countSql, paramMap);
    }

    private String doContractorHiresWhereSql(ContractorSearchDto searchDto, Map<Integer, Object> paramMap) {
        StringBuilder whereSql = new StringBuilder();
        paramMap.put(1, searchDto.getFrom().toString());
        paramMap.put(2, searchDto.getTo().toString());
        paramMap.put(3, SecurityUtils.getTenantId());
        whereSql.append(" and s.start_date between ?1 and ?2 ");
        whereSql.append(" and s.tenant_id = ?3 ");
        if (CollUtil.isNotEmpty(searchDto.getAssignmentDivisions())) {
            List<Integer> assignmentDivisionWhereList = new ArrayList<>();
            AtomicReference<Boolean> flag = new AtomicReference<>(false);
            for (String assignmentDivision : searchDto.getAssignmentDivisions()) {
                Arrays.stream(AssignmentDivision.values()).filter(division -> Objects.equals(division.name(), assignmentDivision))
                        .findFirst().ifPresentOrElse(division -> assignmentDivisionWhereList.add(division.toDbValue()), () -> {
                            flag.set(true);
                            whereSql.append(" and (tta.assignment_division is null");
                        });
            }
            if (CollUtil.isNotEmpty(assignmentDivisionWhereList)) {
                paramMap.put(paramMap.size() + 1, assignmentDivisionWhereList);
                if (flag.get()) {
                    whereSql.append(" or tta.assignment_division in ?").append(paramMap.size()).append(")");
                } else {
                    whereSql.append(" and tta.assignment_division in ?").append(paramMap.size());
                }
            } else {
                if (flag.get()) {
                    whereSql.append(" ) ");
                }
            }
        }
        if (ObjectUtil.isNotNull(searchDto.getStatus())) {
            paramMap.put(paramMap.size() + 1, searchDto.getStatus().toDbValue());
            whereSql.append(" and s.start_type = ?").append(paramMap.size());
        }
        return whereSql.toString();
    }

    private String doContractorHiresOrderSql(SearchSortDTO sort) {
        StringBuilder sb = new StringBuilder();
        if (sort != null) {
            String column = StrUtil.toUnderlineCase(sort.getProperty());
            sb.append(" order by CASE WHEN IFNULL( ").append(ORDER_COLUMN_LIST.get(column)).append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            if (MULTILINGUAL_MIXED_ORDER_COLUMN_LIST.containsKey(column)) {
                sb.append("CONVERT( ").append(ORDER_COLUMN_LIST.get(column)).append(" USING gbk) ");
                sb.append(" ").append(sort.getDirection());
            } else {
                sb.append(column);
                sb.append(" ").append(sort.getDirection());
            }
        } else {
            sb.append(" order by CASE WHEN IFNULL( ").append("t.full_name").append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            sb.append("CONVERT( ").append("t.full_name").append(" USING gbk) ");
            sb.append(" ASC ");
        }
        return sb.toString();
    }

    private String constructContractorHiresDateSql(ContractorSearchDto searchDto, String whereSql, String orderSql, Map<Integer, Object> paramMap) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT s.id, t.full_name, (select contact from talent_contact where talent_id = s.talent_id and jhi_type = 2 and status = 0 order by sort asc limit 1) email, s.job_id job_id, j.code job_code, j.title job_title, tta.assignment_division, ")
                .append(" jai.department, cslcct.full_name hiring_manager, (select GROUP_CONCAT(sc.user_full_name) from start_commission sc where sc.start_id = s.id and sc.user_role = 0 group by sc.start_id) am, s.`start_type` start_status, s.start_date start_date, s.end_date end_date, ")
                .append(" IF( sa.id IS NULL, jl.original_loc ->> '$.addressLine', IFNULL( sa.address, sa.original_loc ->> '$.addressLine')) address, ")
                .append(" IF(sa.id is null, jl.original_loc ->> '$.city', IFNULL(sa.city, sa.original_loc ->> '$.city')) city, ")
                .append(" IF(sa.id is null, jl.original_loc ->> '$.province', IFNULL(sa.province, sa.original_loc ->> '$.province')) state, ")
                .append(" IF(sa.id is null, jl.original_loc ->> '$.country', IFNULL(sa.country, sa.original_loc ->> '$.country')) country, ")
                .append(" (select symbol from enum_currency where id = scr.currency) as currency, ").append("'").append(StrUtil.isBlank(searchDto.getTimeZone())? "": searchDto.getTimeZone()).append("'").append(" time_zone, ")
                .append(" trpo.created_date onboard_date, GROUP_CONCAT(recruitersc.user_full_name order by recruitersc.user_id ) recruiter, GROUP_CONCAT(tm.`name` order by recruitersc.user_id ) as recruiter_team, scr.final_bill_rate bill_rate, scr.final_pay_rate pay_rate, ")
                .append(" scr.rate_unit_type bill_rate_unit, scr.rate_unit_type pay_rate_unit,tai.extended_info ->> '$.source' as sourcing_Channel, c.`full_business_name` company, (scr.final_bill_rate - scr.final_pay_rate) hourly_margin, ")
                .append(" if((select count(1) from start a left join talent_recruitment_process_onboard trpoa on trpoa.talent_recruitment_process_id = a.talent_recruitment_process_id where a.talent_id = s.talent_id  and trpoa.created_date < trpo.created_date ) > 0, true, false) as previously_hired, ")
                .append(" ROUND((scr.final_bill_rate - scr.final_pay_rate)/scr.final_bill_rate, 4) as hourly_margin_percent ")
                .append(" FROM start s ")
                .append(" right join talent t on s.talent_id = t.id ")
                .append(" right join job j on s.job_id = j.id ")
                .append(" right join company c on c.id = j.company_id ")
                .append(" left join job_additional_info jai on jai.id = j.additional_info_id ")
                .append(" left join job_company_contact_relation jccr on jccr.job_id = j.id and jccr.contact_category = 1 ")
                .append(" left join company_sales_lead_client_contact cslcc on jccr.client_contact_id = cslcc.id ")
                .append(" left join talent cslcct on cslcct.id = cslcc.talent_id ")
                .append(" left join start_commission recruitersc on recruitersc.start_id = s.id and recruitersc.user_role = 1 ")
                .append(" left join start_address sa on sa.start_id = s.id ")
                .append(" left join permission_user_team put on put.user_id = recruitersc.user_id and put.is_primary = 1 ")
                .append(" left join permission_team tm on tm.id = put.team_id ")
                .append(" LEFT JOIN (select scr.start_id,max(scr.id) id from start_contract_rate scr group by scr.start_id) scrr ON scrr.start_id = s.id ")
                .append(" left join start_contract_rate scr on scr.id = scrr.id ")
                .append(" left join talent_recruitment_process_onboard trpo on trpo.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" LEFT JOIN ( SELECT max( tta.id ) id, tta.talent_recruitment_process_id FROM timesheet_talent_assignment tta GROUP BY tta.talent_recruitment_process_id ) abi ON abi.talent_recruitment_process_id = s.talent_recruitment_process_id ")
                .append(" left join assignment_bill_info tta on tta.assignment_id = abi.id ")
                .append(" left join talent_additional_info tai on t.additional_info_id = tai.id ")
                .append(" LEFT JOIN (SELECT jl.job_id, MIN(jl.id) AS id FROM job_location jl where jl.original_loc is not null GROUP BY jl.job_id ) a ON a.job_id = j.id ")
                .append(" left join job_location jl on jl.id = a.id ")
                .append(" where s.start_type in (0, 1) and s.position_type in (?");
                 paramMap.put(CollUtil.isEmpty(paramMap)? 1: paramMap.size() + 1, Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.PAY_ROLL.toDbValue(), JobType.MSP.toDbValue()));
        sb.append(paramMap.size())
                .append(") and s.status !=-1")
                .append(whereSql)
                .append(" group by s.id ").append(orderSql);
        return sb.toString();
    }

    private String constructContractorHiresCountSql(String whereSql, Map<Integer, Object> paramMap) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select count(1) from (SELECT count(1) ")
                .append(" FROM start s ")
                .append(" right join job j on s.job_id = j.id ")
                .append(" left join (select max(tta.id) id, tta.start_id from timesheet_talent_assignment tta group by tta.start_id) abi on abi.start_id = s.id ")
                .append(" left join assignment_bill_info tta on tta.assignment_id = abi.id ")
                .append(" where s.start_type in (0, 1) and s.position_type in (?");
        paramMap.put(CollUtil.isEmpty(paramMap)? 1: paramMap.size() + 1, Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.PAY_ROLL.toDbValue(), JobType.MSP.toDbValue()));
        sb.append(paramMap.size())
                .append(") and s.status !=-1 ")
                .append(whereSql)
                .append(" group by s.id ) a");
        return sb.toString();
    }
}
