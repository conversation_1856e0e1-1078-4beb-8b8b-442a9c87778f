package com.altomni.apn.report.domain.vo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.report.domain.enumeration.AssignmentDivisionDataConverter;
import com.altomni.apn.report.domain.enumeration.TimeSheetFrequencyTypeDataConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TimeSheetReportWithPendingApproval {

    @Id
    @ExcelIgnore
    private String id;

    @ExcelIgnore
    private Long assignmentId;

    @ExcelProperty(value = "Employee Name", index = 0)
    private String fullName;

    @ExcelIgnore
    private String firstName;

    @ExcelIgnore
    private String lastName;

    @ExcelProperty(value = "Regular Default Hours", index = 10)
    private String regularHours;

    @ExcelProperty(value = "Overtime Hours", index = 16)
    private String overTime;

    @ExcelProperty(value = "Doubletime Hours", index = 17)
    private String doubleTime;

    @ExcelProperty(value = "Total Default Hours", index = 19)
    private String totalHours;

    @ExcelProperty(value = "Holiday Working Hours", index = 18)
    private String holidayHours;

    @ExcelProperty(value = "Date", index = 20)
    private LocalDate workDate;

    @ExcelIgnore
    private LocalDate weekEndingDate;

    @ExcelProperty(value = "Week Ending", index = 21)
    private LocalDate weekEnd;

    @ExcelProperty(value = "Billing Contact", index = 2)
    private String billingContact;

    @ExcelProperty(value = "Job ID", index = 3)
    private Long jobId;

    @ExcelProperty(value = "Job Title", index = 4)
    private String jobTitle;

    @ExcelIgnore
    private String workingCity;

    @ExcelIgnore
    private String workingState;

    @Transient
    @ExcelProperty(value = "State", index = 9)
    private String state;

    public String getState() {
        if (StrUtil.isNotBlank(workingState)) {
            return workingState;
        }
        return workingCountry;
    }

    @ExcelIgnore
    private String workingCountry;

    @Transient
    @ExcelProperty(value = "Working Location", index = 8)
    private String workingLocation;

    @ExcelIgnore
    private String jobCode;

    @ExcelProperty(value = "Assignment Division", index = 5, converter = AssignmentDivisionDataConverter.class)
    @Convert(converter = AssignmentDivisionConverter.class)
    private AssignmentDivision assignmentDivision;

    @ExcelProperty(value = "Email", index = 6)
    private String email;

    @ExcelProperty(value = "Work Phone", index = 7)
    private String phone;

    @ExcelProperty(value = "Approver", index = 26)
    private String approverName;

    @ExcelProperty(value = "Approver Email", index = 27)
    private String approverEmail;

    @ExcelProperty(value = "Approver Phone", index = 28)
    private String approverPhone;

    @ExcelProperty(value = "Corporation", index = 11)
    private String corporation;

    @ExcelProperty(value = "Employee Status", index = 41)
    private String employeeStatus;

    @ExcelIgnore
    private BigDecimal billRate;

    @Transient
    @ExcelProperty(value = "Regular Bill Rate", index = 29)
    private String billRateFormat;

    @ExcelIgnore
    private BigDecimal netBillRate;

    @Transient
    @ExcelProperty(value = "Net Bill", index = 30)
    private String netBillRateFormat;

    @ExcelIgnore
    private String billCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType billTimeUnit;

    @ExcelIgnore
    private BigDecimal payRate;

    @Transient
    @ExcelProperty(value = "Regular Pay Rate", index = 31)
    private String payRateFormat;

    @ExcelIgnore
    private String payCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType payTimeUnit;

    @ExcelIgnore
    private BigDecimal overBillRate;

    @Transient
    @ExcelProperty(value = "Overtime Bill Rate", index = 32)
    private String overBillRateFormat;

    @ExcelIgnore
    private String overBillCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType overBillTimeUnit;

    @Transient
    private String overBillTimeUnitFormat;

    @ExcelIgnore
    private BigDecimal overPayRate;

    @Transient
    @ExcelProperty(value = "Overtime Pay Rate", index = 33)
    private String overPayRateFormat;

    @ExcelIgnore
    private String overPayCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType overPayTimeUnit;

    @Transient
    private String overPayTimeUnitFormat;

    @ExcelIgnore
    private BigDecimal doubleBillRate;

    @Transient
    @ExcelProperty(value = "Doubletime Bill Rate", index = 34)
    private String doubleBillRateFormat;

    @ExcelIgnore
    private String doubleBillCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType doubleBillTimeUnit;

    @Transient
    private String doubleBillTimeUnitFormat;

    @ExcelIgnore
    private BigDecimal doublePayRate;

    @Transient
    @ExcelProperty(value = "Doubletime Pay Rate", index = 35)
    private String doublePayRateFormat;

    @ExcelIgnore
    private String doublePayCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType doublePayTimeUnit;

    @Transient
    private String doublePayTimeUnitFormat;

    @Transient
    @ExcelProperty(value = "Bill Rate Unit", index = 36)
    private String billRateUnitFormat;

    @Transient
    @ExcelProperty(value = "Pay Rate Unit", index = 37)
    private String payRateUnitFormat;

    @ExcelProperty(value = "AM", index = 22)
    private String am;

    @ExcelProperty(value = "DM", index = 23)
    private String dm;

    @ExcelProperty(value = "AC", index = 24)
    private String ac;

    @ExcelProperty(value = "Recruiter", index = 25)
    private String recruiter;

    @ExcelIgnore
    @Convert(converter = TimeSheetStatusConverter.class)
    private TimeSheetStatus status;

    @Transient
    @ExcelIgnore
    private String statusFormat;

    @ExcelProperty(value = "Billing Frequency", index = 38, converter = TimeSheetFrequencyTypeDataConverter.class)
    private TimeSheetFrequencyType billingFrequency;

    @ExcelProperty(value = "Payment Frequency", index = 40, converter = TimeSheetFrequencyTypeDataConverter.class)
    private TimeSheetFrequencyType paymentFrequency;

    @Transient
    @ExcelProperty(value = "Total Reported Hours", index = 39)
    private String totalReportedHours;

    @ExcelProperty(value = "Company", index = 1)
    private String company;

    @ExcelProperty(value = "Charge Number", index = 50)
    private String chargeNumber;

    @ExcelProperty(value = "Tvc Number", index = 51)
    private String tvcNumber;

    @ExcelIgnore
    private String times;

    @Transient
    @ExcelProperty(value = "Time In", index = 12)
    private String timeIn;

    @Transient
    @ExcelProperty(value = "Break Out", index = 13)
    private String breakOut;

    @Transient
    @ExcelProperty(value = "Break In", index = 14)
    private String breakIn;

    @Transient
    @ExcelProperty(value = "Time Out", index = 15)
    private String timeOut;

    @ExcelProperty(value = "Employee Comments", index = 42)
    private String employeeComments;

    @ExcelIgnore
    private String primaryEmail;

    @ExcelIgnore
    private String primaryPhone;

    @ExcelIgnore
    private String managerEmail;

    @ExcelIgnore
    private String managerPhone;

    public String getApproverEmail() {
        return StrUtil.isBlank(approverEmail)
                && StrUtil.isBlank(approverPhone)?
                (StrUtil.isBlank(managerEmail) && StrUtil.isBlank(managerPhone) ? primaryEmail : managerEmail) :
                approverEmail;
    }

    public String getApproverPhone() {
        return StrUtil.isBlank(approverPhone)
                && StrUtil.isBlank(approverEmail) ?
                (StrUtil.isBlank(managerPhone) && StrUtil.isBlank(managerEmail) ? primaryPhone : managerPhone) :
                approverPhone;
    }

    public String getBreakOut() {
        if (StrUtil.isNotBlank(times) && !Objects.equals(times, ",")) {
            List<String> list = Arrays.stream(times.split(",")).collect(Collectors.toList());
            list.remove(0);
            list.remove(list.size() - 1);
            if (CollUtil.isNotEmpty(list)) {
                return IntStream.range(0, list.size())
                        .filter(i -> i % 2 == 0)
                        .mapToObj(list::get)
                        .collect(Collectors.joining(","));
            }
        }
        return breakOut;
    }

    public String getBreakIn() {
        if (StrUtil.isNotBlank(times) && !Objects.equals(times, ",")) {
            List<String> list = Arrays.stream(times.split(",")).collect(Collectors.toList());
            list.remove(0);
            list.remove(list.size() - 1);
            if (CollUtil.isNotEmpty(list)) {
                return IntStream.range(0, list.size())
                        .filter(i -> (i + 1) % 2 == 0)
                        .mapToObj(list::get)
                        .collect(Collectors.joining(","));
            }
        }
        return breakIn;
    }

    public String getTimeIn() {
        if (StrUtil.isNotBlank(times) && !Objects.equals(times, ",")) {
            return times.split(",")[0];
        }
        return timeIn;
    }

    public String getTimeOut() {
        if (StrUtil.isNotBlank(times) && !Objects.equals(times, ",")) {
            return times.split(",")[times.split(",").length - 1];
        }
        return timeOut;
    }

    public String getWorkingLocation() {
        return Stream.of(workingCity, workingState, workingCountry).filter(StrUtil::isNotBlank).collect(Collectors.joining(", "));
    }


    public String getBillRateUnitFormat() {
        return getBillCurrency() + "/" + billTimeUnit.name().substring(0,1).toUpperCase();
    }

    public String getPayRateUnitFormat() {
        return getPayCurrency() + "/" + payTimeUnit.name().substring(0,1).toUpperCase();
    }

    public String getOverBillTimeUnitFormat() {
        return getOverBillCurrency() + "/" + overBillTimeUnit.name().substring(0,1).toUpperCase();
    }

    public String getOverPayTimeUnitFormat() {
        return getOverPayCurrency() + "/" + overPayTimeUnit.name().substring(0,1).toUpperCase();
    }

    public String getDoubleBillTimeUnitFormat() {
        return getDoubleBillCurrency() + "/" + doubleBillTimeUnit.name().substring(0,1).toUpperCase();
    }

    public String getDoublePayTimeUnitFormat() {
        return getDoublePayCurrency() + "/" + doublePayTimeUnit.name().substring(0,1).toUpperCase();
    }

    public String getStatusFormat() {
        if (status != null) {
            return status.getDescription();
        }
        return "";
    }

    public String getEmployeeStatus() {
        if (StrUtil.isBlank(employeeStatus)) {
            return employeeStatus;
        }
        if (!employeeStatus.startsWith("Past")) {
            return getEmploymentCategoryTypeDisplay(employeeStatus);
        } else {
            String[] split = employeeStatus.split("-");
            String employeeStatusStr = split[1];
            return "Past - " + getEmploymentCategoryTypeDisplay(employeeStatusStr);
        }
    }

    private String getEmploymentCategoryTypeDisplay(String employeeStatus) {
        AssignmentCategoryType assignmentCategoryType = AssignmentCategoryType.fromDbValue(Integer.parseInt(employeeStatus));
        return assignmentCategoryType.getDescription();
    }

    public String getBillRateFormat() {
        if (billRate != null) {
            return billRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getPayRateFormat() {
        if (payRate != null) {
            return payRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getOverBillRateFormat() {
        if (overBillRate != null) {
            return overBillRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getOverPayRateFormat() {
        if (overPayRate != null) {
            return overPayRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getDoubleBillRateFormat() {
        if (doubleBillRate != null) {
            return doubleBillRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getDoublePayRateFormat() {
        if (doublePayRate != null) {
            return doublePayRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getNetBillRateFormat() {
        if (netBillRate != null) {
            return netBillRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return netBillRateFormat;
    }

    public String getTotalReportedHours() {
        return totalHours;
    }

    public String getWeekEndingDate() {
        if (ObjectUtil.isNotNull(weekEndingDate)) {
            return weekEndingDate.format(DateTimeFormatter.ofPattern(DateUtil.MM_DD_YYYY));
        }
        return "";
    }

    public String getFullName() {
        return (StrUtil.isBlank(lastName)?"":lastName) + StrUtil.COMMA + StrUtil.SPACE + (StrUtil.isBlank(firstName)?"":firstName);
    }

}
