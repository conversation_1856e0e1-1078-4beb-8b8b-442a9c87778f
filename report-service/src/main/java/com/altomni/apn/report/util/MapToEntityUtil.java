package com.altomni.apn.report.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import lombok.experimental.UtilityClass;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@UtilityClass
public class MapToEntityUtil {

    public <T> List<T> convertEntity(List<Map<String, Object>> mapList, Class<T> clazz) {
        if (CollUtil.isEmpty(mapList)) {
            new ArrayList<>();
        }
        return new JSONArray(mapList).stream().map(JSONUtil::parseObj)
                .map(json -> BeanUtil.mapToBean(json, clazz, true)).collect(Collectors.toList());
    }

}
