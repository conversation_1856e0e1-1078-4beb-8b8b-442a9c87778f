package com.altomni.apn.report.service.voip;

import com.altomni.apn.common.domain.voip.PhoneTranscription;
import com.altomni.apn.common.domain.voip.VoipContact;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.common.dto.voip.VoipReportDTO;
import com.altomni.apn.user.domain.user.Team;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Service Interface for managing {@link Voip}.
 */
@Component
@FeignClient(value = "voip-service")
public interface VoipService {

    @PostMapping("/voip/api/v3/contact/search-by-report")
    ResponseEntity<List<VoipContactDTO>> findAllByVoipReport(@RequestBody VoipReportDTO voipReportDTO);

    @GetMapping("/voip/api/v3/contact/search-by-user-id/{userId}")
    ResponseEntity<List<VoipContactDTO>> findAllByUser(@PathVariable(value = "userId") Long userId);

    @PostMapping("/voip/api/v3/transcription/search-by-phone-call-ids")
    ResponseEntity<List<PhoneTranscription>> findAllPhoneTranscriptionByPhoneCallIds(@RequestBody Set<String> phoneCallIds);

}
