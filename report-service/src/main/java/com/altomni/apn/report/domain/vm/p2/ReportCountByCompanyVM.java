package com.altomni.apn.report.domain.vm.p2;

import com.altomni.apn.report.domain.enumeration.ReportCountType;
import com.altomni.apn.report.domain.enumeration.ReportCountTypeConverter;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
public class ReportCountByCompanyVM implements Serializable {

    @Id
    private Long companyId;

    private int count;

    private String activityIds;

    @Convert(converter = ReportCountTypeConverter.class)
    private ReportCountType type;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public ReportCountType getType() {
        return type;
    }

    public void setType(ReportCountType type) {
        this.type = type;
    }

    public String getActivityIds() {
        return activityIds;
    }

    public void setActivityIds(String activityIds) {
        this.activityIds = activityIds;
    }
}
