package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.domain.dict.EnumCurrency;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collections;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
public class SeaTeamPerformanceVO {
    private Long userId;
    private String userName;
    private Integer newJobs = 0;
    private Integer assignedJobs = 0;
    /**
     * filled 数量，目前已经 onboard 的数量
     */
    private Integer filledJobs = 0;
    /**
     * filledJobs / assignedJobs 百分比
     */
    private Double assignedJobToPlacement = 0.0;

    private Integer submitToClientCount = 0;

    /**
     * 目前已经 interview 数量
     */
    private Integer interviewedCount = 0;

    /**
     * submitToClientCount / interviewedCount 面试率
     */
    private Double clientToInterviewRatio = 0.0;

    /**
     * submit to client 到 onboard 平均时间
     */
    private Double clientToOnboardAvgTime = 0.0;

    /**
     * 第一次面试到 onboard 平均时间
     */
    private Double firstInterviewOnboardAvgTime = 0.0;

    private List<InvoiceAmountVO> invoiceAmount = Collections.emptyList();

    private Integer invoiceStartCount = 0;

    private BigDecimal avgPlacementFee;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InvoiceAmountVO {
        private Integer currency;
        private BigDecimal amount;
    }

    public SeaTeamPerformanceVO calculateAssignedJobToPlacement() {
        if (this.assignedJobs == 0) {
            this.assignedJobToPlacement = 0.0;
            return this;
        }
        BigDecimal filled = new BigDecimal(this.filledJobs.toString());
        BigDecimal assigned = new BigDecimal(this.assignedJobs.toString());
        this.assignedJobToPlacement = filled.divide(assigned, 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).doubleValue();
        return this;
    }

    public SeaTeamPerformanceVO calculateClintToInterviewRatio() {
        if (this.interviewedCount == 0) {
            this.clientToInterviewRatio = 0.0;
            return this;
        }
        BigDecimal submitToClient = new BigDecimal(this.submitToClientCount.toString());
        BigDecimal interviewed = new BigDecimal(this.interviewedCount.toString());
        this.clientToInterviewRatio = interviewed.divide(submitToClient, 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).doubleValue();
        return this;
    }

    public SeaTeamPerformanceVO calculateAvgPlacementFee() {
        // 有多个币种时，不计算
        if (this.invoiceAmount == null || this.invoiceAmount.size() != 1) {
            this.avgPlacementFee = null;
            return this;
        }
        BigDecimal amount = this.invoiceAmount.get(0).getAmount();
        if (this.invoiceStartCount == null || this.invoiceStartCount == 0) {
            this.avgPlacementFee = null;
            return this;
        }
        BigDecimal startCount = new BigDecimal(this.invoiceStartCount.toString());
        this.avgPlacementFee = amount.divide(startCount, 1, RoundingMode.HALF_UP);
        return this;
    }

    public SeaTeamPerformanceVO convertCurrency(Map<Integer, EnumCurrency> currencyMap, Integer displayCurrency) {
        if (displayCurrency == null || this.invoiceAmount == null || this.invoiceAmount.isEmpty()) {
            return this;
        }
        if (currencyMap == null || currencyMap.isEmpty() || !currencyMap.containsKey(displayCurrency)) {
            return this;
        }
        BigDecimal usdAmount = BigDecimal.ZERO;
        for (InvoiceAmountVO invoiceAmountVO : this.invoiceAmount) {
            EnumCurrency currency = currencyMap.get(invoiceAmountVO.getCurrency());
            if (currency == null) {
                continue;
            }
            BigDecimal toUsdRate = new BigDecimal(currency.getToUsdRate().toString());
            // 转换成美元，再按照 displayCurrency 进行转换
            BigDecimal multiply = invoiceAmountVO.getAmount().divide(toUsdRate, 1, RoundingMode.HALF_UP);
            usdAmount = usdAmount.add(multiply);
        }
        BigDecimal displayRate = new BigDecimal(currencyMap.get(displayCurrency).getFromUsdRate().toString());
        BigDecimal convertedAmount = usdAmount.divide(displayRate, 1, RoundingMode.HALF_UP);
        this.invoiceAmount = List.of(new InvoiceAmountVO(displayCurrency, convertedAmount));
        this.avgPlacementFee = convertedAmount.divide(new BigDecimal(invoiceStartCount.toString()), 1, RoundingMode.HALF_UP);
        return this;
    }

}
