package com.altomni.apn.report.dto.p1;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PipelineAnalyticsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String fromDate;

    private String toDate;

    private Long userId;

    private List<Long> teamIds;

    private Long divisionId;

    private UserRole userRole;

    private List<JobType> jobTypes;

    private String jobCountry;

    @JsonIgnore
    private Set<Long> userIds;
}
