package com.altomni.apn.report.dto;

import com.altomni.apn.report.domain.enumeration.TrackingPlatform;
import com.altomni.apn.report.domain.enumeration.TrackingPlatformConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApnProNoteDetailSearchDto {

    private String fullName;

    private Long talentId;

    @Convert(converter = TrackingPlatformConverter.class)
    private TrackingPlatform sourceChannel;

    private Long jobSearchStatus;

    private Long createdBy;

    private Long lastModifiedBy;

}
