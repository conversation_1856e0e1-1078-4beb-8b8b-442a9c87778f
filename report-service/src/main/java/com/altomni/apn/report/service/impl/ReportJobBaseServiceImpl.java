package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.NodeTypeTableEnum;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.enumeration.enums.ReportAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportJobDetailsOpeningsVo;
import com.altomni.apn.report.domain.vo.ReportPipelineDetailsStatusVo;
import com.altomni.apn.report.dto.ReportJobParam;
import com.altomni.apn.report.dto.ReportParamDto;
import com.altomni.apn.report.dto.ReportPipelineDetailsDto;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.ReportJobBaseService;
import com.altomni.apn.report.util.ExcelUtil;
import com.altomni.apn.report.util.ReportConverterUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("reportJobBaseService")
public class ReportJobBaseServiceImpl extends ReportBaseServiceImpl implements ReportJobBaseService {

    static final List<Integer> REQUIRED_JOB = CollUtil.newArrayList(JobType.FULL_TIME.toDbValue()
            , JobType.CONTRACT.toDbValue(), JobType.MSP.toDbValue(), JobType.OTHERS.toDbValue());


    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    @Resource
    private ReportRepository reportRepository;

    @Resource
    @Qualifier("reportJobBaseService")
    private ReportJobBaseServiceImpl self;

    @Override
    public List<ReportJobDetailsOpeningsVo> getActionJobDetailsSourceData(ReportParamDto reportParamDto) {
        log.info("[apn] getActionJobDetailsSourceData start ...... ");
        StopWatch startWatch = new StopWatch("getActionJobDetailsSourceData");
        startWatch.start("createSqlTask");
        checkParam(reportParamDto);
        String jobIds = reportParamDto.getJobId();
        Set<Long> jobIdSet = Arrays.stream(jobIds.split(",")).filter(StrUtil::isNotBlank).map(Long::parseLong).collect(Collectors.toSet());
        jobIdSet = CollUtil.newHashSet(CollUtil.split(jobIdSet, reportParamDto.getSize()).get(0));
        StringBuilder sb = new StringBuilder();
        createQueryJobDetailsSql(sb);
        startWatch.stop();
        log.info("[apn] getActionJobDetailsSourceData sql = [{}]", sb);
        String sql = sb.toString();
        startWatch.start("searchTask");
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, jobIdSet);
        List<ReportJobDetailsOpeningsVo> voList = searchData(sql, ReportJobDetailsOpeningsVo.class, paramMap);
        StringBuilder companySb = new StringBuilder();
        createQueryCompanyDetail(companySb);
        List<Map<String, Object>> mapList = searchDataWithMap(companySb.toString(), paramMap);
        ReportJobDetailsOpeningsVo companyDetail = convertEntity(mapList, ReportJobDetailsOpeningsVo.class).get(0);
        startWatch.stop();
        startWatch.start("convertTask");
        voList.forEach(vo -> convertVo(vo, companyDetail, reportParamDto.getTimeZone()));
        startWatch.stop();
        log.info("[apn] getActionJobDetailsSourceData time = [{} ms] \n {}", startWatch.getTotalTimeMillis(), startWatch.prettyPrint());
        log.info("[apn] getActionJobDetailsSourceData end ...... ");
        return voList;
    }

    private void createQueryCompanyDetail(StringBuilder companySb) {
        companySb.append(" select c.id as company_id, c.full_business_name as company, group_concat(distinct ct.full_name) as hiring_manager, group_concat(distinct pt.name) as division_of_primary_sales, ")
                .append(" group_concat(distinct (case CONCAT(pru.first_name,pru.last_name) regexp '[一-龥]' when 1 then CONCAT(pru.last_name,pru.first_name) ELSE CONCAT(pru.first_name, \" \",pru.last_name) END)) as primary_recruiter, ")
                .append(" group_concat(distinct (case CONCAT(amu.first_name,amu.last_name) regexp '[一-龥]' when 1 then CONCAT(amu.last_name,amu.first_name) ELSE CONCAT(amu.first_name, \" \",amu.last_name) END)) as primary_sales ")
                .append(" from company c ")
                .append(" left join job j on j.company_id = c.id ")
                .append(" left join job_company_contact_relation jccr on jccr.job_id = j.id ")
                .append(" left join company_sales_lead_client_contact cslcc on cslcc.id = jccr.client_contact_id ")
                .append(" left join talent ct on ct.id = cslcc.talent_id")
                .append(" left join company_project_team cpt on cpt.company_id = c.id ")
                .append(" left join company_project_team_user cptu on cptu.team_id = cpt.id and cptu.permission = 16 ")
                .append(" left join user pru on pru.id = cptu.user_id ")
                .append(" left join business_flow_administrator csla ON csla.company_id = c.id AND csla.sales_lead_role = 0 ")
                .append(" left join user amu on amu.id = csla.user_id ")
                .append(" left join permission_user_team put on put.user_id = amu.id and put.is_primary = 1 ")
                .append(" left join permission_team pt on pt.id = put.team_id ")
                .append(" where j.id in ?1 ")
                .append(" group by c.id ");
    }

    @Override
    @ProcessConfidentialTalent
    public List<ReportPipelineDetailsStatusVo> getActionPipelineDetailsSourceData(ReportPipelineDetailsDto reportPipelineDetailsDto) {
        log.info("[apn] getActionPipelineDetailsSourceData start ...... ");
        StopWatch startWatch = new StopWatch("getActionPipelineDetailsSourceData");
        startWatch.start("createSqlTask");
        checkParam(reportPipelineDetailsDto);
        String activityIds = reportPipelineDetailsDto.getActivityId();
        Set<Long> activityIdSet = Arrays.stream(activityIds.split(",")).map(Long::parseLong).collect(Collectors.toSet());
        activityIdSet = CollUtil.newHashSet(CollUtil.split(activityIdSet, reportPipelineDetailsDto.getSize()).get(0));
        StringBuilder sb = new StringBuilder();
        createQueryPipelineDetailsSqlByStatus(sb, reportPipelineDetailsDto.getStatus());
        startWatch.stop();
        startWatch.start("searchTask");
        log.info("[apn] getActionPipelineDetailsSourceData sql = [{}]", sb);
        String sql = sb.toString();
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, activityIdSet);
        List<ReportPipelineDetailsStatusVo> voList = searchData(sql, ReportPipelineDetailsStatusVo.class, paramMap);
        startWatch.stop();
        startWatch.start("convertTask");

        Set<Long> privateJobTeamIds = reportRepository.findPrivateJobTeamIds(SecurityUtils.getTenantId());
        voList.forEach(vo -> convertPipelineDetailVo(vo, reportPipelineDetailsDto, privateJobTeamIds));
        startWatch.stop();
        log.info("[apn] getActionPipelineDetailsSourceData time = [{} ms] \n {} ", startWatch.getTotalTimeMillis(), startWatch.prettyPrint());
        log.info("[apn] getActionPipelineDetailsSourceData end ...... ");
        return voList;
    }

    private void convertPipelineDetailVo(ReportPipelineDetailsStatusVo vo, ReportPipelineDetailsDto reportPipelineDetailsDto, Set<Long> privateJobTeamIds) {
        if (StrUtil.isNotBlank(vo.getJobLocation())) {
            try {
                JSONArray locationsJson = JSONUtil.parseArray(vo.getJobLocation());
                vo.setJobLocation(convertLocation(locationsJson));
            } catch (Exception e) {
                log.info("[apn] convertPipelineDetailVoIsError vo = [{}], message = [{}]", JSONUtil.toJsonStr(vo), ExceptionUtils.getStackTrace(e));
            }
        }
        // 如果是 convert to FTE, 则强制不显示离职，这是特殊需求
        if (BooleanUtils.isTrue(vo.getConvertedToFte())){
            vo.setResigned(Boolean.FALSE);
        }
        if (Objects.equals(vo.getStatus(), NodeStatus.ELIMINATED.toDbValue().toString())) {
            vo.setCurrentStatus(NodeStatus.ELIMINATED.name());
        } else if (StrUtil.isNotBlank(vo.getCurrentStatus())) {
            vo.setCurrentStatus(NodeType.fromDbValue(Integer.parseInt(vo.getCurrentStatus())).name());
        } else {
            vo.setCurrentStatus(NodeType.ON_BOARD.name());
        }
        ReportConverterUtil.convertInstant(vo, reportPipelineDetailsDto.getTimeZone());
        vo.setLastUpdatedBy(CommonUtils.formatFullNameWithBlankCheck(vo.getUuFirstName(), vo.getUuLastName()));
        vo.setSourcer(CommonUtils.formatFullNameWithBlankCheck(vo.getSuFirstName(), vo.getSuLastName()));
        vo.setPrivateJob(privateJobTeamIds.contains(vo.getPteamId()));
    }

    private String convertLocation(JSONArray locationsJson) {
        String location = "";
        if (CollUtil.isNotEmpty(locationsJson)) {
            StringBuilder sb = new StringBuilder();
            sb.append(locationsJson.stream().map(JSONUtil::parseObj)
                    .map(obj -> obj.getStr("location"))
                    .filter(StrUtil::isNotBlank)
                    .collect(Collectors.joining(";")))
                    .append(";")
                    .append(locationsJson.stream()
                            .map(JSONUtil::parseObj).map(obj ->
                                    obj.getStr("city", "") + (StrUtil.isNotBlank(obj.getStr("city", ""))? ",":"") +
                                    obj.getStr("province", "") + (StrUtil.isNotBlank(obj.getStr("province", ""))? ",":"") +
                                    obj.getStr("country", ""))
                            .collect(Collectors.joining(";")));
            if (StrUtil.isNotBlank(sb)) {
                location = Arrays.stream(sb.toString().split(";")).filter(StrUtil::isNotBlank).collect(Collectors.joining("; "));
            }
        }
        return location;
    }

    @Override
    public void exportActionJobDetailsByExcel(ReportParamDto reportParamDto, HttpServletResponse response) {
        List<ReportJobDetailsOpeningsVo> reportJobDetailsOpeningsVoList = getActionJobDetailsSourceData(reportParamDto);
        ExcelUtil.downloadExcel(response, ReportJobDetailsOpeningsVo.class, reportJobDetailsOpeningsVoList, "", "JobReportByDetails.xlsx", true);
    }

    @Override
    public void exportActivityDetailsByExcel(ReportPipelineDetailsDto reportPipelineDetailsDto, HttpServletResponse response) {
        List<ReportPipelineDetailsStatusVo> reportPipelineDetailsStatusVoList = self.getActionPipelineDetailsSourceData(reportPipelineDetailsDto)
                .stream()
                .map(d -> d.setCurrentStatus(BooleanUtils.isTrue(d.getResigned()) ? NodeType.OFF_BOARDED.name() : d.getCurrentStatus())).toList();
        ExcelUtil.downloadExcel(response, getTableHeaders(reportPipelineDetailsDto.getStatus()), convertToMap(reportPipelineDetailsStatusVoList), "", "PipelineReportByDetails.xlsx", true);
    }

    private List<List<Object>> convertToMap(List<ReportPipelineDetailsStatusVo> reportPipelineDetailsStatusVoList) {
        Field[] fieldArray = ReflectUtil.getFields(ReportPipelineDetailsStatusVo.class);
        List<Field> fieldList = Arrays.stream(fieldArray).filter(field -> field.getAnnotation(ExcelProperty.class) != null)
                .sorted((a1, a2) -> {
                    ExcelProperty excelProperty1 = a1.getAnnotation(ExcelProperty.class);
                    ExcelProperty excelProperty2 = a2.getAnnotation(ExcelProperty.class);
                    return Comparator.comparingInt(ExcelProperty::index).compare(excelProperty1, excelProperty2);
                }).collect(Collectors.toList());
        List<List<Object>> mapList = new LinkedList<>();
        reportPipelineDetailsStatusVoList.forEach(vo -> {
            List<Object> list = new LinkedList<>();
            if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                ExcelUtil.maskConfidentialTalentData(vo);
            }
            fieldList.forEach(field -> {
                Object value = ReflectUtil.getFieldValue(vo, field);
                list.add(value);
            });
            mapList.add(list);
        });
        return mapList;
    }

    private List<List<String>> getTableHeaders(NodeType nodeType) {
        Field[] fieldArray = ReflectUtil.getFields(ReportPipelineDetailsStatusVo.class);
        return Arrays.stream(fieldArray).map(field -> field.getAnnotation(ExcelProperty.class))
                .filter(ObjectUtil::isNotNull).sorted(Comparator.comparing(ExcelProperty::index))
                .map(excel -> {
                    String value = excel.value()[0];
                    if (Objects.equals(2, excel.index())) {
                        String headerName = nodeTypeAndNameMap.get(nodeType);
                        if (StrUtil.isNotBlank(headerName)) {
                            value = headerName;
                        }
                    }
                    return CollUtil.newArrayList(value);
                }).collect(Collectors.toList());
    }

    static final Map<NodeType, String> nodeTypeAndNameMap = new HashMap<>(){
        {
            put(NodeType.SUBMIT_TO_JOB, "Date of Submitted To Job");
            put(NodeType.SUBMIT_TO_CLIENT, "Date of Submitted To Client");
            put(NodeType.INTERVIEW, "Date of Updated To Interview");
            put(NodeType.OFFER, "Date of Offered");
            put(NodeType.OFFER_ACCEPT, "Date of Offer Accepted");
            put(NodeType.ON_BOARD, "Date of On boarded");
        }
    };

    private void createQueryPipelineDetailsSqlByStatus(StringBuilder sb, NodeType status) {
        sb.append(" select t.id as id, j.code as job_code, group_concat(distinct ct.full_name) as hiring_manager, t.full_name as talent_name, trpstatus.created_date as activity_created_date, trp.last_modified_date as last_updated_at, ")
                .append(" trpn.node_type as current_status, trpn.node_status as status, c.full_business_name as company, j.title as job_title, j.id as job_id, l.locations as job_location, ")
                .append(" j.status as job_status, j.pteam_id, c.id as company_id, j.id as job_id, trp.id as application_id, ")
                .append(" uu.first_name as uu_first_name, uu.last_name as uu_last_name, ")
                .append(" su.first_name as su_first_name, su.last_name as su_last_name, ")
                .append(" trpe.reason as eliminate_reason, ")
                .append(" if(resign.id is null, false, true) as resigned, ")
                .append(" if(star.id is null, false, true) as converted_to_fte ")
                .append(" from talent t ")
                .append(" inner join talent_recruitment_process trp on trp.talent_id = t.id ")
                .append(" left join talent_recruitment_process_kpi_user trpku on trpku.talent_recruitment_process_id = trp.id and trpku.user_role = 2 ")
                .append(" left join user su on su.id = trpku.user_id ")
                .append(" left join user uu on uu.id = substring_index( trp.last_modified_by, ',', 1 ) ")
                .append(" inner join ");
        if (status == NodeType.INTERVIEW) {
            sb.append(" (select min(a.id), a.created_date, a.talent_recruitment_process_id from ")
                    .append(NodeTypeTableEnum.getTableNameByNodeType(status)).append(" a ")
                    .append(" group by a.talent_recruitment_process_id ) ");
        } else {
            sb.append(NodeTypeTableEnum.getTableNameByNodeType(status));
        }
        sb.append(" trpstatus on trpstatus.talent_recruitment_process_id = trp.id ");
        if(status == NodeType.ON_BOARD){
            sb.append(" INNER JOIN talent_recruitment_process_node trpon ON trpon.talent_recruitment_process_id = trp.id and trpon.node_type=60  and trpon.node_status = 1");
        }
        sb.append(" left join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and trpn.node_status in (1,4)")
                .append(" inner join job j on j.id = trp.job_id ")
                .append(" inner join company c on c.id = j.company_id ")
                .append(" left join job_company_contact_relation jccr on jccr.job_id = j.id ")
                .append(" left join talent_recruitment_process_eliminate trpe on trpe.talent_recruitment_process_id = trp.id ")
                .append(" left join talent_recruitment_process_resignation resign on resign.talent_recruitment_process_id = trp.id ")
                .append(" left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5 ")
                .append(" left join company_sales_lead_client_contact cslcc on cslcc.id = jccr.client_contact_id ")
                .append(" left join talent ct on ct.id = cslcc.talent_id")
                .append(" LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id ")
                .append(" where trp.id in ?1 ")
                .append(" group by trp.id ");
    }

    private void createQueryJobDetailsSql(StringBuilder sb) {
        sb.append(" select c.id as company_id, j.open_time as date_issued, j.title as title, j.id as job_id, j.code as code, j.status as status, jai.extended_info ->> '$.department' as division, ")
                .append(" rp.job_type as type, c.full_business_name as company, '' as hiring_manager , j.start_date as start_date, j.end_date as end_date, ")
                .append(" jai.extended_info ->> '$.salaryRange' as salary_range, jai.extended_info ->> '$.billRange' as bill_range, jai.extended_info ->> '$.payType' as unit_type, ")
                .append(" l.locations as job_locations, jai.extended_info ->> '$.requiredSkills' as required_skills, ")
                .append(" j.openings as opening_count, j.max_submissions as max_submittals, ")
                .append(" '' as division_of_primary_sales, trp.last_modified_date as last_activity_date, '' as primary_recruiter, '' as primary_sales, ")
                .append(" count(distinct trpstc.talent_recruitment_process_id) as submitted_count, count(distinct trpi.talent_recruitment_process_id) as interview_count, count(distinct trpo.talent_recruitment_process_id) as started_count ")
                .append(" from job j ")
                .append(" inner join company c on c.id = j.company_id ")
                .append(" inner join recruitment_process rp on rp.id = j.recruitment_process_id ")
                .append(" left join job_additional_info jai on jai.id = j.additional_info_id ")
                .append(" left join talent_recruitment_process trp on trp.job_id = j.id ")
                .append(" left join talent_recruitment_process_submit_to_client trpstc on trpstc.talent_recruitment_process_id = trp.id ")
                .append(" left join talent_recruitment_process_interview trpi on trpi.talent_recruitment_process_id = trp.id ")
                .append(" left join talent_recruitment_process_onboard trpo on trpo.talent_recruitment_process_id = trp.id ")
                .append(" LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id ")
                .append(" where j.id in ?1 ")
                .append(" group by j.id ");
    }

    private void checkParam(ReportParamDto reportParamDto) {
        if (reportParamDto == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTJOB_CHECKPARAM_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
        if (StrUtil.isBlank(reportParamDto.getJobId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTJOB_CHECKPARAM_JOBNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
    }

    private void checkParam(ReportPipelineDetailsDto reportPipelineDetailsDto) {
        if (reportPipelineDetailsDto == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTJOB_CHECKPARAM_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
        if (StrUtil.isBlank(reportPipelineDetailsDto.getActivityId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTJOB_CHECKPARAM_ACTIVITYIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
    }

    private void convertVo(ReportJobDetailsOpeningsVo vo, ReportJobDetailsOpeningsVo companyDetail, String timeZone) {
        convertExtendedInfo(vo);
        ReportConverterUtil.convertInstant(vo, timeZone);
        vo.setPrimaryRecruiter(companyDetail.getPrimaryRecruiter());
        vo.setPrimarySales(companyDetail.getPrimarySales());
        vo.setDivisionOfPrimarySales(companyDetail.getDivisionOfPrimarySales());
        vo.setHiringManager(companyDetail.getHiringManager());
    }


    private void convertExtendedInfo(ReportJobDetailsOpeningsVo vo) {
        if (StrUtil.isNotEmpty(vo.getSalaryRange())) {
            JSONObject salaryRangeJson = JSONUtil.parseObj(vo.getSalaryRange());
            Float gte = salaryRangeJson.getFloat("gte", 0F);
            Float lte = salaryRangeJson.getFloat("lte", 0F);
            vo.setMinimumPayRate(gte);
            vo.setMaximumPayRate(lte);
            vo.setSalaryRange(null);
        }
        if (StrUtil.isNotEmpty(vo.getBillRange())) {
            JSONObject billRangeJson = JSONUtil.parseObj(vo.getBillRange());
            Float gte = billRangeJson.getFloat("gte", 0F);
            Float lte = billRangeJson.getFloat("lte", 0F);
            vo.setMinimumBillRate(gte);
            vo.setMaximumBillRate(lte);
            vo.setBillRange(null);
        }
        if (StrUtil.isNotBlank(vo.getJobLocations())) {
            try {
                JSONArray locationsJson = JSONUtil.parseArray(vo.getJobLocations());
                vo.setJobLocations(convertLocation(locationsJson));
            } catch (Exception e) {
                log.info("[apn] convertJobDetailVoIsError vo = [{}], message = [{}]", JSONUtil.toJsonStr(vo), ExceptionUtils.getStackTrace(e));
            }
        }

        if (StrUtil.isNotBlank(vo.getRequiredSkills())) {
            JSONArray requiredSkillsArray = JSONUtil.parseArray(vo.getRequiredSkills());
            if (CollUtil.isNotEmpty(requiredSkillsArray)) {
                String skills = requiredSkillsArray.stream().map(obj -> {
                    String skill = "";
                    try {
                        JSONObject jsonObject = JSONUtil.parseObj(obj);
                        skill = jsonObject.getStr("skillName", "");
                    } catch (Exception e) {
                        log.error("skill is error, data = [{}]", obj);
                    }
                    return skill;
                }).filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
                vo.setRequiredSkills(skills);
            }
        }
    }

    public void appendBase(StringBuilder tableSql, ReportJobParam reportParam, Map<Integer, Object> conditionParamMap) {
        tableSql.append(" rp.job_type in ?");
        conditionParamMap.put(conditionParamMap.size() + 1, REQUIRED_JOB);
        tableSql.append(conditionParamMap.size()).append(AND_SYMBOL);
        if (StrUtil.isNotBlank(reportParam.getFromDate()) && StrUtil.isNotBlank(reportParam.getToDate())) {
            String fromDate = DateUtil.formatToDate(reportParam.getFromDate());
            String toDate = DateUtil.formatToDate(reportParam.getToDate());
            conditionParamMap.put(conditionParamMap.size() + 1, fromDate);
            tableSql.append(" j.open_time BETWEEN ?")
                    .append(conditionParamMap.size())
                    .append(AND_SYMBOL).append("?");
            conditionParamMap.put(conditionParamMap.size() + 1, toDate);
            tableSql.append(conditionParamMap.size());
            tableSql.append(AND_SYMBOL);
        }
        if (StrUtil.isNotBlank(reportParam.getJobCountry()) && !Objects.equals(ALL_SELECT, reportParam.getJobCountry())) {
            tableSql.append(" exists ( select 1 from job_location jl where jl.job_id = j.id ");
            conditionParamMap.put(conditionParamMap.size() + 1, reportParam.getJobCountry());
            tableSql.append(" and jl.official_country = ?").append(conditionParamMap.size()).append(" )").append(AND_SYMBOL);
        }
        this.applyDataPermission(reportParam, tableSql, conditionParamMap);
    }

    public void applyDataPermission(ReportJobParam reportParam, StringBuilder tableSql, Map<Integer, Object> conditionParamMap){
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
//        Collection<Long> teamIds = new HashSet<>();
//        if (CollUtil.isNotEmpty(reportParam.getTeamIds())){
//            Collection<Long> intersectionTeamIds = CollUtil.intersection(reportParam.getTeamIds(), teamDataPermission.getNestedTeamIds());
//            teamIds.addAll(intersectionTeamIds);
//        }else {
//            teamIds.addAll(teamDataPermission.getNestedTeamIds());
//        }

//            if (ObjectUtil.equal(DataScope.PERMISSION_SELF.toDbValue(), dataScope)) {
        if (teamDataPermission.getSelf()) {
            conditionParamMap.put(conditionParamMap.size() + 1,
                    reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob()));
            tableSql.append(" (j.puser_id = ").append(SecurityUtils.getUserId()).append(" or j.id in ?").append(conditionParamMap.size()).append(")")
                    .append(AND_SYMBOL);
//            } else if (ObjectUtil.equal(DataScope.PERMISSION_TEAM.toDbValue(), dataScope) || ObjectUtil.equal(DataScope.PERMISSION_EXTRA_TEAM.toDbValue(), dataScope)) {
        } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) { // team data permission
//                tableSql.append(" exists ( select 1 from (")
//                        .append(" select team_id from permission_user_team where user_id = ").append(SecurityUtils.getUserId())
//                        .append(UNION_ALL)
//                        .append(" select team_id from permission_extra_user_team where user_id = ").append(SecurityUtils.getUserId())
//                        .append(UNION_ALL)
//                        .append(" select pert.team_id as team_id from permission_extra_role_team pert left join user_role ur on ur.role_id = pert.role_id where ur.user_id = ").append(SecurityUtils.getUserId())
//                        .append(" ) put where put.team_id = j.pteam_id ")
//                        .append(" ) ").append(AND_SYMBOL);
            Collection<Long> validJobIds;
            if (CollUtil.isEmpty(reportParam.getTeamIds())){
                validJobIds = reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(),
                        teamDataPermission.getTeamIdForPrivateJob());
                conditionParamMap.put(conditionParamMap.size() + 1, teamDataPermission.getNestedTeamIds());
            }else{
                List<Long> privateIdList = reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob());
                Set<Long> jobIdSet = reportRepository.getPrivateJobIdsByTeamIds(reportParam.getTeamIds());
                validJobIds = CollectionUtils.intersection(privateIdList, jobIdSet);
                conditionParamMap.put(conditionParamMap.size() + 1, reportParam.getTeamIds());
            }
            tableSql.append(" (j.pteam_id in ?").append(conditionParamMap.size())
                    .append(" or j.id in ?");
            conditionParamMap.put(conditionParamMap.size() + 1, validJobIds);
            tableSql.append(conditionParamMap.size())
                    .append(")");
            tableSql.append(AND_SYMBOL);
        }else { // ALL data permission
            if (CollUtil.isNotEmpty(reportParam.getTeamIds())){
                Collection<Long> validJobIds = CollectionUtils.intersection(reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(),
                        teamDataPermission.getTeamIdForPrivateJob()), reportRepository.getPrivateJobIdsByTeamIds(reportParam.getTeamIds()));
                conditionParamMap.put(conditionParamMap.size() + 1, reportParam.getTeamIds());
                tableSql.append(" (j.pteam_id in ?").append(conditionParamMap.size())
                        .append(" or j.id in ?");
                conditionParamMap.put(conditionParamMap.size() + 1, validJobIds);
                tableSql.append(conditionParamMap.size());
                tableSql.append(")");
            }else {
                conditionParamMap.put(conditionParamMap.size() + 1, teamDataPermission.getTeamIdForPrivateJob());
                tableSql.append(" (j.pteam_id != ?").append(conditionParamMap.size());
                tableSql.append(" or j.id in ?");
                conditionParamMap.put(conditionParamMap.size() + 1,
                        reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob()));
                tableSql.append(conditionParamMap.size())
                        .append(")");
            }
            tableSql.append(AND_SYMBOL);
        }
    }

}
