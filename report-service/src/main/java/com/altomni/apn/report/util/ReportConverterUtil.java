package com.altomni.apn.report.util;

import cn.hutool.core.util.ReflectUtil;
import lombok.experimental.UtilityClass;

import java.lang.reflect.Field;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Optional;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

@UtilityClass
public class ReportConverterUtil {

    /**
     * instant field to field.getName() + format
     * @param t
     * @param timeZone
     * @param <T>
     */
    public <T> void convertInstant(T t, String timeZone) {
        Field[] fields = ReflectUtil.getFields(t.getClass());
        Arrays.stream(fields).forEach(field -> {
            if (field.getType() == Instant.class) {
                Instant instant = (Instant) ReflectUtil.getFieldValue(t, field);
                Optional.ofNullable(instant).ifPresent(a -> {
                    LocalDateTime localDateTime = LocalDateTime.ofInstant(a, ZoneId.of(timeZone));
                    String instantFormat = DateTimeFormatter.ofPattern(NORM_DATETIME_PATTERN).format(localDateTime);
                    ReflectUtil.setFieldValue(t, field.getName() + "Format", instantFormat);
                });
            }
        });
    }
}
