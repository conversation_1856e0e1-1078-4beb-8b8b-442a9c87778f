package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.report.dto.SeaTeamPerformanceSearchDto;
import lombok.Data;

import java.util.Map;
import java.util.stream.Collectors;

@Data
public class SeaTeamPerformanceExcelVO {
    @ExcelProperty(value = "User Name", index = 0)
    private String userName;

    @ExcelProperty(value = "New Jobs", index = 1)
    private Integer newJobs;

    @ExcelProperty(value = "Assigned Jobs", index = 2)
    private Integer assignedJobs;

    @ExcelProperty(value = "Assigned Jobs Placement", index = 3)
    private String assignedJobsPlacement;

    @ExcelProperty(value = "[Submit to Client] to [Interview] Ratio", index = 4)
    private String clientToInterviewRatio;

    @ExcelProperty(value = "[Submit to Client] to [Onboard] Avg Time", index = 5)
    private String clientToOnboardAvgTime;

    @ExcelProperty(value = "[1st Interview] to [Onboard] Avg Time", index = 6)
    private String firstInterviewOnboardAvgTime;

    @ExcelProperty(value = "Invoice Amount", index = 7)
    private String invoiceAmount;

    @ExcelProperty(value = "Average Placement Fee", index = 8)
    private String avgPlacementFee;


    public static SeaTeamPerformanceExcelVO from(SeaTeamPerformanceSearchDto searchDto, SeaTeamPerformanceVO vo, Map<Integer, EnumCurrency> currencyMap) {
        SeaTeamPerformanceExcelVO excelRow = new SeaTeamPerformanceExcelVO();
        excelRow.setUserName(vo.getUserName());
        excelRow.setNewJobs(vo.getNewJobs());
        excelRow.setAssignedJobs(vo.getAssignedJobs());
        excelRow.setAssignedJobsPlacement(vo.getAssignedJobToPlacement() + "%" + "(" + vo.getFilledJobs() + "/" + vo.getAssignedJobs() + ")");
        excelRow.setClientToInterviewRatio(vo.getClientToInterviewRatio() + "%" + "(" + vo.getInterviewedCount() + "/" + vo.getSubmitToClientCount() + ")");
        excelRow.setClientToOnboardAvgTime(vo.getClientToOnboardAvgTime() + " Days");
        excelRow.setFirstInterviewOnboardAvgTime(vo.getFirstInterviewOnboardAvgTime() + " Days");
        String invoiceAmount = vo.getInvoiceAmount().stream().map(invoiceAmountVO -> {
            EnumCurrency currency = currencyMap.get(invoiceAmountVO.getCurrency());
            return currency.getLabel1() + " " + invoiceAmountVO.getAmount();
        }).collect(Collectors.joining(","));
        excelRow.setInvoiceAmount(invoiceAmount);
        // 用户指定了 currency，在 vo 里面已经计算过了，已经同一了汇率，或者这一行数据只有一个币种, 所以可以计算平均值
        if (searchDto.getCurrency() != null || vo.getInvoiceAmount().size() == 1) {
            if (!vo.getInvoiceAmount().isEmpty()) {
                Integer currency = vo.getInvoiceAmount().get(0).getCurrency();
                EnumCurrency enumCurrency = currencyMap.get(currency);
                if (enumCurrency != null && vo.getAvgPlacementFee() != null) {
                    excelRow.setAvgPlacementFee(enumCurrency.getLabel1() + vo.getAvgPlacementFee());
                }
            }
        }

        return excelRow;
    }
}
