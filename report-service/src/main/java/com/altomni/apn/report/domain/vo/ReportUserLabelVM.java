package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.domain.user.Role;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class ReportUserLabelVM {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String firstName;

    private String lastName;

    private Boolean activated;

    private String email;

    private List<Long> languages;

    private String jobTitle;

    private Long levelOfExperience;




}
