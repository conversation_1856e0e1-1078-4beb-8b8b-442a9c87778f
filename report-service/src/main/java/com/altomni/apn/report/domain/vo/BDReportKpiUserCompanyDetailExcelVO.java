package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BDReportKpiUserCompanyDetailExcelVO {

    @ExcelProperty(value = "Company Name", index = 0)
    private String companyName;

    @ExcelProperty(value = "SalesLead Owner", index = 1)
    private String salesLeadName;

    @ExcelProperty(value = "BD Owner", index = 2)
    private String bdOwnerName;

    @ExcelProperty(value = "AM", index = 3)
    private String amName;

    @ExcelProperty(value = "Co-Account Manager", index = 4)
    private String coAmName;

    @ExcelProperty(value = "Created Date", index = 5)
    private String createdDateStr;
}
