package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The TranscriptionSource enumeration.
 */
public enum VoipDetailReportType implements ConvertedEnum<Integer> {

    TOTAL_CALL(0), //所有拨出的电话
    CONNECT(1), //播出电话中接通的电话
    QUALIFIED_CONNECT(2); //接通并且通话时长超过阈值

    private final int dbValue;

    VoipDetailReportType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<VoipDetailReportType, Integer> resolver =
        new ReverseEnumResolver<>(VoipDetailReportType.class, VoipDetailReportType::toDbValue);

    public static VoipDetailReportType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
