package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.FteBdReportJobDetailVO;
import com.altomni.apn.report.domain.vo.FteBdReportOnboardDetailVO;
import com.altomni.apn.report.domain.vo.FteBdReportVO;
import com.altomni.apn.report.dto.FteBdReportDTO;
import com.altomni.apn.report.dto.FteBdReportDetailDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;

import java.util.List;

public interface ReportCompanyService {
    List<FteBdReportVO> queryFteBdReportList(FteBdReportDTO dto, Pageable pageable, HttpHeaders headers);

    List<FteBdReportJobDetailVO> queryFteBdReportJobDetail(FteBdReportDetailDTO dto, Pageable pageable, HttpHeaders headers);

    List<FteBdReportOnboardDetailVO> queryFteBdReportOnboardDetail(FteBdReportDetailDTO dto, Pageable pageable, HttpHeaders headers);

    List<FteBdReportOnboardDetailVO> queryFteBdReportInvoiceDetail(FteBdReportDetailDTO dto, Pageable pageable, HttpHeaders headers);

    List<FteBdReportOnboardDetailVO> queryFteBdReportPaymentDetail(FteBdReportDetailDTO dto, Pageable pageable, HttpHeaders headers);

}
