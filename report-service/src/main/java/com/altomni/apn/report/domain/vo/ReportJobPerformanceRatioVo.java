package com.altomni.apn.report.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Entity
public class ReportJobPerformanceRatioVo implements Serializable {

    @Id
    private Long id;

    private String companyName;

    private Long totalJobs;

    private Long fillCounts;

    private Long lossCounts;
}
