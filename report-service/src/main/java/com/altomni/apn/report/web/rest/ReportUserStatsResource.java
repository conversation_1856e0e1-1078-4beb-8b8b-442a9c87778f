package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.config.constants.ReportConstants;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.utils.ExcelUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.dto.LinkedinStatsDTO;
import com.altomni.apn.report.service.ReportUserStatsService;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.util.UriUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;


/**
 * REST controller for managing Report.
 */
@RestController
@RequestMapping("/api/v3")
public class ReportUserStatsResource {

    private final Logger log = LoggerFactory.getLogger(ReportUserStatsResource.class);

    @Resource
    private ReportUserStatsService reportUserStatsService;

    private static final String UTF8 = "UTF-8";

    @GetMapping("/user/linkedin-stats")
    @Timed
    public ResponseEntity<List<LinkedinStatsDTO>> getLinkedinStats(
        @RequestParam(value = "from_date", required = false) String fromDate,
        @RequestParam(value = "to_date", required = false) String toDate,
        @RequestParam(value = "type") ContactType contactType,
        @RequestParam(value = "time_zone", required = false) String timeZone) throws UnsupportedEncodingException {
        log.info("[APN: ReportUserStats @{}] REST request to get linkedin stats data. from_date:{}, to_date:{}, type:{}, time_zone:{}", SecurityUtils.getUserId(), fromDate, toDate, contactType, timeZone);
        List<LinkedinStatsDTO> res = reportUserStatsService.getUserLinkedinStats(fromDate, toDate, contactType, UriUtils.decode(timeZone, UTF8));

        return ResponseEntity.ok(res);
    }

    @GetMapping("/user/all-linkedin-stats")
    @Timed
    public void exportLinkedinStats(
        HttpServletResponse response,
        @RequestParam(value = "from_date", required = false) String fromDate,
        @RequestParam(value = "to_date", required = false) String toDate,
        @RequestParam(value = "type") ContactType contactType,
        @RequestParam(value = "time_zone", required = false) String timeZone) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        log.info("[APN: ReportUserStats @{}] REST request to all linkedin stats data. from_date:{}, to_date:{}, type:{}, time_zone:{}", SecurityUtils.getUserId(), fromDate, toDate, contactType, timeZone);
         ExcelUtil.getResource(response,ReportConstants.LINKEDIN_STATS_HEADERS, ReportConstants.LINKEDIN_STATS_FIELDS, reportUserStatsService.getAllUserLinkedinStats(fromDate, toDate, contactType, timeZone), null, ReportConstants.LINKEDIN_STATS_COL, null);
    }

    @GetMapping("/user/es-stats")
    @Timed
    public ResponseEntity<List<LinkedinStatsDTO>> getESStats(
        @RequestParam(value = "from_date", required = false) String fromDate,
        @RequestParam(value = "to_date", required = false) String toDate,
        @RequestParam(value = "tenant_id", required = true) Long tenantId,
        @RequestParam(value = "time_zone", required = false) String timeZone) throws UnsupportedEncodingException {
        log.info("[APN: ReportUserStats @{}] REST request to get linkedin stats data. from_date:{}, to_date:{}, time_zone:{}", SecurityUtils.getUserId(), fromDate, toDate, timeZone);
        List<LinkedinStatsDTO> res = reportUserStatsService.getESStats(fromDate, toDate, UriUtils.decode(timeZone, UTF8), tenantId);

        return ResponseEntity.ok(res);
    }

    @GetMapping("/user/all-es-stats")
    @Timed
    public void exportESStats(
        HttpServletResponse response,
        @RequestParam(value = "from_date", required = false) String fromDate,
        @RequestParam(value = "to_date", required = false) String toDate,
        @RequestParam(value = "tenant_id", required = true) Long tenantId,
        @RequestParam(value = "time_zone", required = false) String timeZone) throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        log.debug("[APN: ReportUserStats @{}] REST request to all linkedin stats data. from_date:{}, to_date:{}, tenant_id:{}, time_zone:{}", SecurityUtils.getUserId(), fromDate, toDate, tenantId, timeZone);
        ExcelUtil.getResource(response, ReportConstants.LINKEDIN_STATS_HEADERS, ReportConstants.LINKEDIN_STATS_FIELDS, reportUserStatsService.getAllESStats(fromDate, toDate, timeZone, tenantId), null, ReportConstants.LINKEDIN_STATS_COL, null);
    }
}
