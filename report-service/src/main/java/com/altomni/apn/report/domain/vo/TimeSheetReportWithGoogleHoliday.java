package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class TimeSheetReportWithGoogleHoliday {


    @ExcelProperty(value = "Holiday Date", index = 0)
    private String holidayDate;

    @ExcelProperty(value = "Employee Name", index = 1)
    private String employeeName;

    @ExcelProperty(value = "Candidate ID", index = 2)
    private String candidateId;

    @ExcelProperty(value = "Job Title", index = 3)
    private String jobTitle;

    @ExcelProperty(value = "Job ID", index = 4)
    private String jobID;

    @ExcelProperty(value = "Hours Recorded", index = 5)
    private String hoursRecorded;


}
