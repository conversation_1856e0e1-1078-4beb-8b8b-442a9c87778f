package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDate;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportCommissionVO {

    @Id
    @ExcelIgnore
    private String id;

    private String fullName;

    private Float regularHours;

    private Float overTime;

    private Float doubleTime;

    private Float totalHours;

    private LocalDate workDate;

    private LocalDate weekEndingDate;

    private LocalDate weekEnd;


}
