package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.s3.CandidateUserDetailSearchVo;
import com.altomni.apn.report.dto.s3.*;
import com.altomni.apn.report.service.impl.ReportBaseServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Repository
public class CandidateUserRepository extends ReportBaseServiceImpl {
    @PersistenceContext
    EntityManager entityManager;

    public List<UserCandidateDetailDTO> getUserCandidates(Long tenantId, String fromDate, String toDate, Long userId, Pageable pageable) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                    SELECT
                        candidate_user.id AS id,
                        candidate_name ,
                        u1.first_name AS c_first_name,
                        u1.last_name AS c_last_name,
                        candidate_user.created_at ,
                        u2.first_name AS l_first_name,
                        u2.last_name AS l_last_name,
                        candidate_user.last_updated_at
                    FROM
                        (
                        SELECT
                            t.id,
                            t.full_name AS candidate_name,
                            t.created_by AS c_user_id,
                            t.created_date as created_at,
                            t.last_modified_by AS l_user_id,
                            t.last_modified_date AS last_updated_at
                        FROM
                            talent t
                WHERE
                		tenant_id = :tenantId
                		and created_by = :createdBy
                            		""");
        // 拼凑时间参数
        boolean needDate = StringUtils.isNotEmpty(fromDate) && StringUtils.isNotEmpty(toDate);
        if (needDate) {
            queryBuilder.append(" AND t.created_date BETWEEN :fromDate AND :toDate ");
        }

        // 分页和排序限制
        String sortClause = getSortClause(pageable.getSort());
        // 当排序里有l_first_name的时候，排序和分页放到关联表外；当没有l_first_name的时候，排序和分页放到talent表内，提高效率，减少关联表的数据量
        boolean containLastModifiedBy = sortClause.contains("l_first_name");
        if (!containLastModifiedBy) {
            queryBuilder.append(sortClause).append(" LIMIT :offset,:pageSize ");
        }
        queryBuilder.append(
                """
                        ) candidate_user
                        LEFT JOIN USER u1 ON c_user_id = u1.uid
                        LEFT JOIN USER u2 ON l_user_id = u2.uid
                        	""");
        if (containLastModifiedBy) {
            queryBuilder.append(sortClause).append(" LIMIT :offset,:pageSize");
        }

        Query query = entityManager.createNativeQuery(queryBuilder.toString(), UserCandidateDetailDTO.class)
                .setParameter("tenantId", tenantId)
                .setParameter("createdBy", userId.toString() + StrUtil.C_COMMA + tenantId)
                .setParameter("offset", pageable.getOffset())
                .setParameter("pageSize", pageable.getPageSize());
        if (needDate) {
            query.setParameter("fromDate", fromDate)
                    .setParameter("toDate", toDate);
        }
        return query.getResultList();

    }

    public List<UserCandidateCountDTO> countUserCandidates(Long tenantId, String fromDate, String toDate, Set<Long> userId) {
        String query = """ 
                    SELECT
                    u.id as id,
                    u.first_name,
                    u.last_name,
                    IFNULL( user_count.count, 0 ) AS created_count
                FROM
                    USER u
                    LEFT JOIN (
                            SELECT
                                     created_by,
                                     COUNT( 1 ) AS count
                            FROM talent t
                            WHERE t.tenant_id = :tenantId
                                        """;
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(query);
        boolean needDate = StringUtils.isNotEmpty(fromDate) && StringUtils.isNotEmpty(toDate);
        if (needDate) {
            queryBuilder.append("AND t.created_date BETWEEN :fromDate AND :toDate ");
        }
        boolean needUserId = CollectionUtils.isNotEmpty(userId);
        if (needUserId) {
            queryBuilder.append("AND created_by  IN ( :createdBy ) ");
        }
        queryBuilder.append("""
                GROUP BY created_by
                """);

        queryBuilder.append("""
                ) user_count ON u.uid = user_count.created_by
                	where u.tenant_id = :tenantId 
                	and u.activated = 1
                	""");
        if (needUserId) {
            queryBuilder.append(" and u.id in ( :userIds ) ");
        }
        queryBuilder.append(" order by created_count desc");
        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString(), UserCandidateCountDTO.class).setParameter("tenantId", tenantId);
        if (needDate) {
            nativeQuery.setParameter("fromDate", fromDate)
                    .setParameter("toDate", toDate);
        }
        if (needUserId) {
            nativeQuery.setParameter("userIds", userId);
            Set<String> createdBy = userId.stream().map(t -> t.toString() + StrUtil.C_COMMA + tenantId).collect(Collectors.toSet());
            nativeQuery.setParameter("createdBy", createdBy);
        }
        return nativeQuery.getResultList();
    }

    private String getSortClause(Sort sort) {
        if (sort == null) {
            return StrUtil.EMPTY;
        }
        List<String> sorts = new ArrayList<>();
        for (Sort.Order order : sort) {
            if ("c_user_name".equals(order.getProperty())) {
                return StrUtil.EMPTY;
            } else if ("candidate_name".equals(order.getProperty())) {
                sorts.add(String.join(StrUtil.SPACE, "CONVERT( candidate_name USING gbk)", order.getDirection().toString()));
            } else if ("l_user_name".equals(order.getProperty())) {
                sorts.add(String.join(StrUtil.SPACE, "CONVERT( l_first_name USING gbk)", order.getDirection().toString()));
                sorts.add(String.join(StrUtil.SPACE, "CONVERT( l_last_name USING gbk)", order.getDirection().toString()));
            } else {
                sorts.add(String.join(StrUtil.SPACE, order.getProperty(), order.getDirection().toString()));
            }
        }
        return "ORDER BY " + org.apache.commons.lang3.StringUtils.join(sorts, StrUtil.C_COMMA);

    }

    public List<UserTalentNoteCountDTO> searchTalentNoteByUserIdAndCreateDate(String fromDate, String toDate, Long tenantId, Set<Long> userIds) {
        String sql = """
                SELECT
                	tn.puser_id AS user_id,
                    SUM(CASE WHEN tn.note_type = 0 THEN 1 ELSE 0 END) AS called_count,
                    SUM(CASE WHEN tn.note_type = 1 THEN 1 ELSE 0 END) AS interview_count,
                    SUM(CASE WHEN tn.note_type = 3 THEN 1 ELSE 0 END) AS emailed_count,
                    SUM(CASE WHEN tn.note_type = 4 THEN 1 ELSE 0 END) AS video_count,
                    SUM(CASE WHEN tn.note_type = 5 THEN 1 ELSE 0 END) AS ici_count
                FROM
                	talent_note tn
                inner join talent t on t.id = tn.talent_id
                WHERE
                	t.tenant_id = :tenantId {} and tn.created_date BETWEEN :fromDate and :toDate
                	group by tn.puser_id
                """;
        if (CollUtil.isNotEmpty(userIds)) {
            sql = StrUtil.format(sql, " and tn.puser_id IN :userIds ");
        } else {
            sql = StrUtil.format(sql, " ");
        }
        Query nativeQuery = entityManager.createNativeQuery(sql, UserTalentNoteCountDTO.class)
                .setParameter("tenantId", tenantId)
                .setParameter("fromDate", fromDate)
                .setParameter("toDate", toDate);
        if (CollUtil.isNotEmpty(userIds)) {
            nativeQuery.setParameter("userIds", userIds);
        }
        return nativeQuery.getResultList();
    }


    public List<UserTalentNoteDetailDTO> searchDetailUserCandidatesNote(CandidateUserDetailSearchVo searchVo, Pageable pageable) {
        StringBuilder sql = new StringBuilder("""
                SELECT
                    tn.id,
                	tn.talent_id,
                	tn.title,
                	tn.note,
                	tn.created_date,
                	t.full_name name
                FROM
                	talent_note tn
                inner join talent t on t.id = tn.talent_id
                WHERE
                	t.tenant_id = :tenantId and tn.puser_id IN :userIds and tn.created_date BETWEEN :fromDate and :toDate
                	and tn.note_type = :noteType
                """);
        String sortStr = constructSort(pageable);
        sql.append("  order by ").append(sortStr).append("  limit :start, :size");
        int start = pageable.getPageSize() * pageable.getPageNumber();
        Query nativeQuery = entityManager.createNativeQuery(sql.toString(), UserTalentNoteDetailDTO.class)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("userIds", CollUtil.newArrayList(searchVo.getDetailUserId()))
                .setParameter("fromDate", searchVo.getFromDate())
                .setParameter("toDate", searchVo.getToDate())
                .setParameter("noteType", searchVo.getNoteType().toDbValue())
                .setParameter("start", start)
                .setParameter("size", pageable.getPageSize());
        return nativeQuery.getResultList();
    }

    private String constructSort(Pageable pageable) {
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (order == null) {
            return "  CONVERT( tn.created_date USING gbk) desc ";
        }
        switch (order.getProperty()) {
            case "candidateName":
                return "  CONVERT( t.full_name USING gbk) " + order.getDirection().name();
            case "createdDate":
                return "  CONVERT( tn.created_date USING gbk) " + order.getDirection().name();
            default:
                throw new IllegalArgumentException("Invalid sort property");
        }
    }
}
