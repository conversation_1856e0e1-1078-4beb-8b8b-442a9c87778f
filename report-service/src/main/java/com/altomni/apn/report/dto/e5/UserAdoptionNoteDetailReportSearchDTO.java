package com.altomni.apn.report.dto.e5;

import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.report.domain.enumeration.UserAdoptionReportSearchType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAdoptionNoteDetailReportSearchDTO extends UserAdoptionDetailReportSearchDTO implements Serializable {

    private String fullName;

    private String title;

    private String note;

    private TalentNoteType noteType;

    private String createdBy;
}
