package com.altomni.apn.report.config.excel;

import cn.hutool.core.convert.ConverterRegistry;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.InterviewType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetFrequencyType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.report.domain.enumeration.RateUnitTypeEnumConverter;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.sql.Date;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;

@Configuration
public class ConverterConfig {

    @PostConstruct
    public void initConverter() {
        ConverterRegistry.getInstance().putCustom(RateUnitType.class, RateUnitTypeEnumConverter.class);
        ConverterRegistry.getInstance().putCustom(Instant.class, (value, defaultValue) -> {
            if (value == null) {
                return null;
            }
            if (value instanceof java.sql.Timestamp timestamp) {
                return timestamp.toInstant();
            } else if (value instanceof java.util.Date date) {
                return date.toInstant();
            }
            return defaultValue;
        });
        ConverterRegistry.getInstance().putCustom(LocalDate.class, (value, defaultValue) -> {
            if (value == null) {
                return null;
            }
            if (value instanceof java.sql.Timestamp timestamp) {
                return timestamp.toLocalDateTime().toLocalDate();
            } else if (value instanceof java.util.Date date) {
                try {
                    return date.toInstant().atZone(ZoneId.of("UTC")).toLocalDate();
                } catch (Exception e) {
                    return ((Date) date).toLocalDate();
                }
            }
            return defaultValue;
        });
        ConverterRegistry.getInstance().putCustom(AssignmentDivision.class, (value, defaultValue) -> {
            if (value != null) {
                return AssignmentDivision.fromDbValue(Integer.parseInt(value + ""));
            }
            return null;
        });
        ConverterRegistry.getInstance().putCustom(TimeSheetStatus.class, (value, defaultValue) -> {
            if (value != null) {
                return TimeSheetStatus.fromDbValue(Integer.parseInt(value + ""));
            }
            return null;
        });
        ConverterRegistry.getInstance().putCustom(TimeSheetFrequencyType.class, (value, defaultValue) -> {
            if (value != null) {
                return TimeSheetFrequencyType.fromDbValue(Integer.parseInt(value + ""));
            }
            return null;
        });
        ConverterRegistry.getInstance().putCustom(JobStatus.class, (value, defaultValue) -> {
            if (value != null) {
                return JobStatus.fromDbValue(Integer.parseInt(value + ""));
            }
            return null;
        });
        ConverterRegistry.getInstance().putCustom(MyCandidateStatusFilter.class, (value, defaultValue) -> {
            if (value != null) {
                return MyCandidateStatusFilter.fromDbValue(Integer.parseInt(value + ""));
            }
            return null;
        });
        ConverterRegistry.getInstance().putCustom(InterviewType.class, (value, defaultValue) -> {
            if (value != null) {
                return InterviewType.fromDbValue(Integer.parseInt(value + ""));
            }
            return null;
        });
        ConverterRegistry.getInstance().putCustom(EliminateReason.class, (value, defaultValue) -> {
            if (value != null) {
                return EliminateReason.fromDbValue(Integer.parseInt(value + ""));
            }
            return null;
        });
        ConverterRegistry.getInstance().putCustom(JobType.class, (value, defaultValue) -> {
            if (value != null) {
                return JobType.fromDbValue(Integer.parseInt(value + ""));
            }
            return null;
        });
    }

}
