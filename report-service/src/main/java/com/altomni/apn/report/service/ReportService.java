package com.altomni.apn.report.service;

import com.altomni.apn.report.dto.ReportDTO;
import org.openapitools.client.model.ApiV1DatasetGet200Response;
import org.openapitools.client.model.ChartDataFilter;
import org.openapitools.client.model.ChartDataResponseResult;
import org.openapitools.client.model.DatasetRestApiGet;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface ReportService {

    Page<ReportDTO> findAllByTenantId(Pageable pageable);

    ReportDTO findOne(Long id);

    ReportDTO create(ReportDTO reportDTO);

    ReportDTO update(Long id, ReportDTO reportDTO);

    void delete(Long id);

    ChartDataResponseResult olapQuery(ReportDTO reportDTO);

    ApiV1DatasetGet200Response findAllDataSets();

    DatasetRestApiGet findOneDataSet(Integer id);

    List<ChartDataFilter.OpEnum> getFilterOpsByDataType(String columnType);

    List<Long> getNestedTeamIds(List<Long> teamIds);

    Long getUserIdFromDataPermission(Long userId);
}
