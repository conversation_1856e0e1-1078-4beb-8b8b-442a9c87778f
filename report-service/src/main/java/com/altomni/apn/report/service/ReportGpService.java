package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.ReportGpSearchDTO;
import com.altomni.apn.report.dto.ReportMonthlyRevenueGpSearchDTO;
import com.altomni.apn.report.dto.ReportQuarterOnboardAndOffboardSearchDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ReportGpService {

    Page<ReportGpWithExcelVO> searchGp(ReportGpSearchDTO reportGpSearchDTO);

    void searchGpWithExcelData(ReportGpSearchDTO reportGpSearchDTO, HttpServletResponse response);

    List<ReportMonthlyRevenueGpVO> searchMonthlyRevenueGpReport(ReportMonthlyRevenueGpSearchDTO reportMonthlyRevenueGpSearchDTO) throws Exception;

    Page<ReportMonthlyRevenueDetailVO> searchMonthlyRevenueDetail(ReportMonthlyRevenueGpSearchDTO reportMonthlyRevenueGpSearchDTO,Pageable pageable);

    void download(HttpServletResponse response,ReportMonthlyRevenueGpSearchDTO dto);

    List<ReportQuarterOnboardAndOffboardVO> searchQuarterlyOnboardingAndOffboardingReport(ReportQuarterOnboardAndOffboardSearchDTO reportQuarterOnboardAndOffboardSearchDTO);

    Page<ReportQuarterlyOnboardingDetailVO> searchQuarterlyOnboardingDetail(ReportQuarterOnboardAndOffboardSearchDTO searchDto, Pageable pageable);

    Page<ReportQuarterlyOffboardingDetailVO> searchQuarterlyOffboardingDetail(ReportQuarterOnboardAndOffboardSearchDTO searchDto, Pageable pageable);

    void quarterlyOffboardingDetailDownload(HttpServletResponse response, ReportQuarterOnboardAndOffboardSearchDTO dto);

    void quarterlyOnboardAndOffboardingDetailDownload(HttpServletResponse response, ReportQuarterOnboardAndOffboardSearchDTO dto);
}
