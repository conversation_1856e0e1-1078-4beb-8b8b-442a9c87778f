package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.ReportJobDetailsOpeningsVo;
import com.altomni.apn.report.domain.vo.ReportPipelineDetailsStatusVo;
import com.altomni.apn.report.dto.ReportParamDto;
import com.altomni.apn.report.dto.ReportPipelineDetailsDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ReportJobBaseService {

    List<ReportJobDetailsOpeningsVo> getActionJobDetailsSourceData(ReportParamDto reportParamDto);

    List<ReportPipelineDetailsStatusVo> getActionPipelineDetailsSourceData(ReportPipelineDetailsDto reportPipelineDetailsDto);

    void exportActionJobDetailsByExcel(ReportParamDto reportParamDto, HttpServletResponse response);

    void exportActivityDetailsByExcel(ReportPipelineDetailsDto reportPipelineDetailsDto, HttpServletResponse response);

}
