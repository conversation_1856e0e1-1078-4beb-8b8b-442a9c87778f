package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.BDActivityReportDetailVO;
import com.altomni.apn.report.domain.vo.BDActivityReportVO;
import com.altomni.apn.report.dto.BDActivitySearchDTO;
import com.altomni.apn.report.dto.BDActivitySearchDeatilDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

public interface BdActivityReportService {

    List<BDActivityReportVO> countBdActivityContactType(BDActivitySearchDTO searchDTO, Pageable pageable, HttpHeaders headers) throws IOException;

    List<BDActivityReportDetailVO> getBdActivityDetail(BDActivitySearchDeatilDTO searchDTO, Pageable pageable, HttpHeaders headers) throws IOEx<PERSON>, ExecutionException, InterruptedException;
}
