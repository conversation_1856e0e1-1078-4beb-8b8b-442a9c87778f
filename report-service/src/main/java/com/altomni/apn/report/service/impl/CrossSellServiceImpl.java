package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.job.domain.enumeration.start.StartStatus;
import com.altomni.apn.job.domain.enumeration.start.StartType;
import com.altomni.apn.report.domain.RevenueDTO;
import com.altomni.apn.report.domain.vo.CrossSellVO;
import com.altomni.apn.report.dto.CompanyServiceTypeDTO;
import com.altomni.apn.report.repository.CrossSellNativeRepository;
import com.altomni.apn.report.service.CrossSellService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.altomni.apn.report.service.impl.ReportBaseServiceImpl.*;

@Slf4j
@Service
public class CrossSellServiceImpl implements CrossSellService {

    @Resource
    CrossSellNativeRepository crossSellNativeRepository;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
    @Override
    public CrossSellVO getRevenueGp(CompanyServiceTypeDTO dto) {
        List<Object[]> contractOfferAcceptedRevenueGP = crossSellNativeRepository.getRevenueGP(dto);
        CrossSellVO result = handleRevenueResult(contractOfferAcceptedRevenueGP);
        return result;
    }

    private CrossSellVO handleRevenueResult(List<Object[]> revenueDTOS) {
        List<RevenueDTO> contractOfferAcceptedRevenueGP = transRevenueDTO(revenueDTOS);
        Set<Integer> currencySet = contractOfferAcceptedRevenueGP.stream().map(RevenueDTO::getCurrency).collect(Collectors.toSet());
        Boolean multiCurrency = CollUtil.isNotEmpty(currencySet) && currencySet.size() > 1;
        BigDecimal revenue = BigDecimal.ZERO;
        BigDecimal gp = BigDecimal.ZERO;
        for (RevenueDTO vo : contractOfferAcceptedRevenueGP) {
            if (Objects.equals(OFFER_ACCEPTED, vo.getType())) {
                if (JobType.CONTRACT.toDbValue().equals(vo.getJobType())) {
                    if (vo.getRateUnitType() != null && vo.getFinalBillRate() != null && vo.getSStartDate() != null && vo.getSEndDate() != null && vo.getEstimatedWorkingHourPerWeek() != null) {
                        BigDecimal tempRevenue = CommonUtils.calculateRevenue(vo.getFinalBillRate(), vo.getRateUnitType(), vo.getSStartDate(), vo.getSEndDate(), vo.getEstimatedWorkingHourPerWeek());
                        BigDecimal tempGp = vo.getTotalBillAmount();
                        if (multiCurrency) {
                            tempRevenue = tempRevenue.multiply(vo.getFromUsdRate());
                            tempGp = tempGp.multiply(vo.getFromUsdRate());
                        }
                        revenue = NumberUtil.add(revenue, tempRevenue);
                        gp = NumberUtil.add(gp, tempGp);
                    } else if (vo.getTotalBillAmount() != null) {
                        BigDecimal tempRevenue = vo.getTotalBillAmount();
                        if (multiCurrency) {
                            tempRevenue = tempRevenue.multiply(vo.getFromUsdRate());
                        }
                        revenue = NumberUtil.add(revenue, tempRevenue);
                        gp = NumberUtil.add(gp, tempRevenue);
                    }
                } else if (JobType.FULL_TIME.toDbValue().equals(vo.getJobType())) {
                    vo.setEndDate(vo.getWarrantyEndDate() != null ? vo.getWarrantyEndDate() : null);
                    BigDecimal tempRevenue = vo.getTotalBillAmount();
                    if (multiCurrency) {
                        tempRevenue = tempRevenue.multiply(vo.getFromUsdRate());
                    }

                    revenue = NumberUtil.add(revenue, tempRevenue);
                    gp = NumberUtil.add(gp, tempRevenue);
                }
            } else if (Objects.equals(ONBOARD, vo.getType())) {
                if (Objects.equals(vo.getStatus(), StartStatus.ACTIVE.toDbValue() + "")
                    || Objects.equals(vo.getStatus(), StartStatus.CONTRACT_TERMINATED.toDbValue() + "")
                    || Objects.equals(vo.getStatus(), StartStatus.CONTRACT_EXTENDED.toDbValue() + "")) {
                    setReportSalesDetailStartStatus(vo);

                    if (JobType.CONTRACT.toDbValue().equals(vo.getJobType())) {
                        if (vo.getRateUnitType() != null && vo.getFinalBillRate() != null && vo.getSStartDate() != null && vo.getSEndDate() != null && vo.getEstimatedWorkingHourPerWeek() != null) {
                            BigDecimal tempRevenue = CommonUtils.calculateRevenue(vo.getFinalBillRate(), vo.getRateUnitType(), vo.getSStartDate(), vo.getSEndDate(), vo.getEstimatedWorkingHourPerWeek());
                            BigDecimal tempGp = vo.getTotalBillAmount();

                            if (multiCurrency) {
                                tempRevenue = tempRevenue.multiply(vo.getFromUsdRate());
                                tempGp = tempGp.multiply(vo.getFromUsdRate());
                            }
                            revenue = NumberUtil.add(revenue, tempRevenue);
                            gp = NumberUtil.add(gp, tempGp);

                        } else if (vo.getTotalBillAmount() != null) {
                            BigDecimal tempRevenue = vo.getTotalBillAmount();
                            if (multiCurrency) {
                                tempRevenue = tempRevenue.multiply(vo.getFromUsdRate());
                            }
                            revenue = NumberUtil.add(revenue, tempRevenue);
                            gp = NumberUtil.add(gp, tempRevenue);

                        }
                    } else if (JobType.FULL_TIME.toDbValue().equals(vo.getJobType())) {
                        vo.setEndDate(vo.getWarrantyEndDate() != null ? vo.getWarrantyEndDate() : null);

                        BigDecimal tempRevenue = vo.getTotalBillAmount();
                        if (multiCurrency) {
                            tempRevenue = tempRevenue.multiply(vo.getFromUsdRate());
                        }

                        revenue = NumberUtil.add(revenue, tempRevenue);
                        gp = NumberUtil.add(gp, tempRevenue);
                    }
                } else if (Objects.equals(vo.getStatus(), StartStatus.FTE_FAIL_WARRANTY.toDbValue() + "")) {
                    vo.setStatus(START_FAIL_WARRANTY);
                    vo.setStartDate(vo.getStartDate() != null ? vo.getStartDate() : null);
                    vo.setEndDate(vo.getWarrantyEndDate() != null ? vo.getWarrantyEndDate() : null);
                    if (vo.getTotalBillAmount() != null) {
                        vo.setEndDate(vo.getWarrantyEndDate() != null ? vo.getWarrantyEndDate() : null);

                        BigDecimal tempRevenue = vo.getTotalBillAmount();
                        if (multiCurrency) {
                            tempRevenue = tempRevenue.multiply(vo.getFromUsdRate());
                        }

                        revenue = NumberUtil.add(revenue, tempRevenue);
                        gp = NumberUtil.add(gp, tempRevenue);
                    }
                }
            }
        }
        CrossSellVO result = new CrossSellVO();
        result.setCurrency(multiCurrency ? 0 : CollUtil.isEmpty(currencySet) ? 0 : currencySet.stream().findFirst().get());
        result.setRevenue(revenue.setScale(2, RoundingMode.HALF_UP));
        result.setGp(gp.setScale(2, RoundingMode.HALF_UP));
        return result;
    }

    private List<RevenueDTO> transRevenueDTO(List<Object[]> contractOfferAcceptedRevenueGP) {
        List<RevenueDTO> revenueDTOList = new ArrayList<>();
        for (Object[] obj : contractOfferAcceptedRevenueGP) {
            RevenueDTO revenueDTO = new RevenueDTO();
            revenueDTO.setId(obj[2] != null ? ((BigInteger) obj[2]).longValue() : null); // 假设 id 是 Long 类型
            revenueDTO.setStatus(obj[0] != null ? obj[0].toString() : null);
            revenueDTO.setStartType(obj[1] != null ? StartType.fromDbValue((Integer) obj[1]) : null);
            revenueDTO.setTotalBillAmount(obj[3] != null ? (BigDecimal) obj[3] : null);
            revenueDTO.setCompanyId(obj[4] != null ? ((Number) obj[4]).longValue() : null);
            revenueDTO.setStartDate(obj[5] != null ? LocalDate.parse(obj[5].toString()) : null);
            revenueDTO.setEndDate(obj[6] != null ? LocalDate.parse(obj[6].toString()) : null);
            revenueDTO.setType(obj[7] != null ? (String) obj[7] : null);
            revenueDTO.setJobType(obj[8] != null ? ((Number) obj[8]).intValue() : null);
            revenueDTO.setFromUsdRate(obj[9] != null ? BigDecimal.valueOf( ((Float) obj[9]).longValue()) : null);
            revenueDTO.setSStartDate(obj[10] != null ? LocalDate.parse(obj[10].toString()) : null);
            revenueDTO.setSEndDate(obj[11] != null ? LocalDate.parse(obj[11].toString()) : null);
            revenueDTO.setWarrantyEndDate(obj[12] != null ? LocalDate.parse(obj[12].toString()) : null);
            revenueDTO.setFinalPayRate(obj[13] != null?(BigDecimal) obj[13]:null);
            revenueDTO.setFinalBillRate(obj[14] != null?(BigDecimal) obj[14]:null);
            revenueDTO.setEstimatedWorkingHourPerWeek(obj[15] != null?(BigDecimal) obj[15]:null);
            revenueDTO.setRateUnitType(obj[16]!=null?RateUnitType.fromDbValue((Integer) obj[16]):null);
            revenueDTO.setCurrency(obj[17] != null ? ((Number) obj[17]).intValue() : null);
            if (null!=obj[18]){
                LocalDateTime dateTime = LocalDateTime.parse(obj[18].toString(), formatter);
                revenueDTO.setOnboardDate(dateTime.toLocalDate());
            }
            revenueDTOList.add(revenueDTO);
        }
        return revenueDTOList;
    }

    private void setReportSalesDetailStartStatus(RevenueDTO vo) {
        if (Objects.equals(StartStatus.CONTRACT_TERMINATED.toDbValue() + "", vo.getStatus())) {
            vo.setStatus(TERMINATION);
        } else if (Objects.equals(StartType.CONTRACT_EXTENSION, vo.getStartType())) {
            vo.setStatus(EXTENTION);
        } else {
            vo.setStatus(ONBOARD);
        }
    }
}
