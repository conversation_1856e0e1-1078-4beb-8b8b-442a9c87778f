package com.altomni.apn.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.DateUtils;
import com.altomni.apn.common.domain.dict.EnumLanguage;
import com.altomni.apn.common.domain.dict.EnumLevelOfExperience;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.user.UserDeliveryCountryRelation;
import com.altomni.apn.common.domain.user.UserDeliveryProcessRelation;
import com.altomni.apn.common.domain.user.UserLanguageRelation;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.ReportUserLabelSearchDTO;
import com.altomni.apn.common.dto.user.SortDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.repository.enums.EnumLanguageRepository;
import com.altomni.apn.common.repository.enums.EnumLevelOfExperienceRepository;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.enumeration.CountryPackageEnum;
import com.altomni.apn.report.service.ReportUserLabelService;
import com.altomni.apn.report.service.application.ApplicationService;
import com.altomni.apn.user.repository.permission.PermissionTeamLeaderRepository;
import com.altomni.apn.user.repository.permission.PermissionTeamRepository;
import com.altomni.apn.user.repository.user.UserDeliveryCountryRelationRepository;
import com.altomni.apn.user.repository.user.UserDeliveryProcessRelationRepository;
import com.altomni.apn.user.repository.user.UserLanguageRelationRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberMemberDetailDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberMemberDetailExcelDTO;
import com.altomni.apn.user.web.rest.vm.user.DeliveryStatVM;
import com.altomni.apn.user.web.rest.vm.user.ParentTeamLeaderVM;
import com.querydsl.core.types.Order;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigInteger;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReportUserLabelServiceImpl implements ReportUserLabelService {

    @Resource
    UserRepository userRepository;
    @Resource
    PermissionTeamRepository permissionTeamRepository;
    @Resource
    PermissionTeamLeaderRepository permissionTeamLeaderRepository;
    @Resource
    UserLanguageRelationRepository userLanguageRelationRepository;
    @Resource
    UserDeliveryProcessRelationRepository userDeliveryProcessRelationRepository;
    @Resource
    UserDeliveryCountryRelationRepository userDeliveryCountryRelationRepository;
    @Resource
    EnumLevelOfExperienceRepository enumLevelOfExperienceRepository;
    @Resource
    EnumLanguageRepository enumLanguageRepository;
    @Resource
    ApplicationService applicationService;
    @Resource
    JPAQueryFactory jpaQueryFactory;
    @Resource
    InitiationService initiationService;
    @Resource(name = "commonThreadPool")
    Executor executor;
    @Resource
    RegionPackageService regionPackageService;

    @Override
    public List<PermissionTeamMemberMemberDetailDTO> searchUserLabelReport(ReportUserLabelSearchDTO reportUserLabelSearchDTO, HttpHeaders headers) {
        initDTO(reportUserLabelSearchDTO);

        // 权限筛选
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        log.info("user label report permission = {}", JSONUtil.toJsonStr(teamDataPermission));
        setPermission(reportUserLabelSearchDTO, teamDataPermission);

        JPAQuery<PermissionTeamMemberMemberDetailDTO> permissionTeamMemberDTOJPAQuery = userRepository.buildLabelUserQuery(jpaQueryFactory, reportUserLabelSearchDTO);
        List<PermissionTeamMemberMemberDetailDTO> users = permissionTeamMemberDTOJPAQuery.fetch();
        if (CollUtil.isEmpty(users) && headers != null){
            headers.set("Pagination-Count", "0");
            return List.of();
        }

        Set<Long> userIds = users.stream().map(PermissionTeamMemberMemberDetailDTO::getId).collect(Collectors.toSet());
        Set<Long> teamIds = users.stream().map(PermissionTeamMemberMemberDetailDTO::getTeamId).collect(Collectors.toSet());

        CompletableFuture<Map<Long, String>> bulkTopTeamFuture = CompletableFuture.supplyAsync(() ->{
            Map<Long, String> teamMap = new HashMap<>();
            permissionTeamRepository.getAllTeamHierarchy(teamIds)
                    .forEach(t ->{
                        teamMap.putIfAbsent(t.get(0, BigInteger.class).longValue(), t.get(1,String.class));
                    });
            return teamMap;
        },executor);


        CompletableFuture<Map<Long, Set<String>>> bulkParentTeamLeaderFuture = CompletableFuture.supplyAsync(() ->{
            List<ParentTeamLeaderVM> result = new ArrayList<>();
            for (Object[] row : permissionTeamLeaderRepository.getParentTeamLeader(teamIds)) {
                Long teamId = ((BigInteger) row[0]).longValue();
                Long leaderTeamId = ((BigInteger) row[1]).longValue();
                Long leaderId = ((BigInteger) row[2]).longValue();
                String firstName = (String) row[3];
                String lastName = (String) row[4];
                String fullName = CommonUtils.formatFullNameWithBlankCheck(firstName, lastName);
                if (StringUtils.isNotBlank(fullName)){
                    ParentTeamLeaderVM info = new ParentTeamLeaderVM(teamId, leaderTeamId, leaderId, firstName, lastName, fullName);
                    result.add(info);
                }
            }
            Map<Long, List<ParentTeamLeaderVM>> map = result.stream().collect(Collectors.groupingBy(ParentTeamLeaderVM::getTeamId));
            map.entrySet().stream().filter(e -> e.getValue().size() == 1 && e.getValue().get(0).getLeaderId().equals(0L)).forEach(t -> {
                ParentTeamLeaderVM leaderVM = t.getValue().get(0);
                Long leaderTeamId = leaderVM.getLeaderTeamId();
                List<ParentTeamLeaderVM> leaderVMS = map.getOrDefault(leaderTeamId, List.of());
                if (CollUtil.isNotEmpty(leaderVMS)){
                    map.put(leaderTeamId, leaderVMS);
                }
            });
            return map.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().stream().map(ParentTeamLeaderVM::getFullName).collect(Collectors.toSet())));


        },executor);

        CompletableFuture<Map<Long, List<Long>>> bulkLanguagesMapFuture = CompletableFuture.supplyAsync(() -> userLanguageRelationRepository.findAllByUserIdIn(userIds).stream().collect(Collectors.groupingBy(UserLanguageRelation::getUserId, Collectors.mapping(UserLanguageRelation::getEnumLanguageId, Collectors.toList()))), executor);

        Map<Integer, CountryPackageEnum> regionMap = regionPackageService.getRegionMap();
        CompletableFuture<Map<Long, List<DeliveryStatVM>>> bulkDeliveryCountryMapFuture = CompletableFuture.supplyAsync(() -> {
                    Map<Long, List<UserDeliveryCountryRelation>> collect = userDeliveryCountryRelationRepository.findAllByUserIdIn(userIds).stream().collect(Collectors.groupingBy(UserDeliveryCountryRelation::getUserId));
                    Map<Long, List<DeliveryStatVM>> result = new HashMap<>();
                    collect.forEach((k, v) -> {
                        Long userId = k;
                        Map<Integer, DeliveryStatVM> map = new HashMap<>();

                        v.forEach(item -> {
                            Integer countryId = item.getEnumCountryId();
                            String name = null;
                            if (regionMap.containsKey(countryId)) {
                                CountryPackageEnum countryPackage = regionMap.get(countryId);
                                countryId = countryPackage.getDbValue();
                                name = countryPackage.name();
                            } else {
                                countryId = CountryPackageEnum.OTHERS.getDbValue();
                                name = CountryPackageEnum.OTHERS.name();
                            }
                            long count = item.getCount() == null ? 0L : item.getCount();
                            if (map.containsKey(countryId)) {
                                DeliveryStatVM deliveryStatVM = map.get(countryId);
                                deliveryStatVM.setCount(deliveryStatVM.getCount() + count);
                            } else {
                                DeliveryStatVM deliveryStatVM = new DeliveryStatVM(countryId.longValue(), count, name);
                                map.put(countryId, deliveryStatVM);
                            }
                        });
                        if (CollUtil.isNotEmpty(map)){
                            result.put(k, new ArrayList<>(map.values()));
                        }
                    });
                    return result;
                }
                , executor);
        CompletableFuture<Map<Long,EnumLanguage>> languageMapFuture = CompletableFuture.supplyAsync(() -> enumLanguageRepository.findAll().stream().filter(t-> StringUtils.isNotBlank(t.getEnDisplay())).collect(Collectors.toMap(EnumLanguage::getId, t->t)), executor);

        CompletableFuture<Map<Long,EnumLevelOfExperience>> levelOfExperienceMapFuture = CompletableFuture.supplyAsync(() -> enumLevelOfExperienceRepository.findAll().stream().filter(t-> StringUtils.isNotBlank(t.getEnDisplay())).collect(Collectors.toMap(EnumLevelOfExperience::getId, t->t)), executor);

        CompletableFuture<Map<Long, List<DeliveryStatVM>>> bulkProcessIdMapFuture = CompletableFuture.supplyAsync(() -> userDeliveryProcessRelationRepository.findAllByUserIdIn(userIds).stream()
                .collect(Collectors.groupingBy(
                        UserDeliveryProcessRelation::getUserId,
                        Collectors.mapping(
                                d -> new DeliveryStatVM(d.getProcessId(), d.getCount()),
                                Collectors.toList()
                        )
                )), executor);

        CompletableFuture.allOf(bulkTopTeamFuture, bulkLanguagesMapFuture, bulkParentTeamLeaderFuture, bulkDeliveryCountryMapFuture, bulkProcessIdMapFuture, languageMapFuture, levelOfExperienceMapFuture)
                .exceptionally(t -> {
                    log.error("Error occurred when fetching user data: ", t);
                    throw new ExternalServiceInterfaceException("Error occurred when fetching user data");
                }).join();
        Map<Long, String> topTeamMap = bulkTopTeamFuture.join();
        Map<Long, List<Long>> languagesMap = bulkLanguagesMapFuture.join();
        Map<Long, Set<String>> teamLeaderMap = bulkParentTeamLeaderFuture.join();
        Map<Long, List<DeliveryStatVM>> deliveryCountryMap = bulkDeliveryCountryMapFuture.join();
        Map<Long, List<DeliveryStatVM>> processIdMap = bulkProcessIdMapFuture.join();
        Map<Long, EnumLanguage> enumLanguageMap = languageMapFuture.join();
        Map<Long, EnumLevelOfExperience> levelOfExperienceMap = levelOfExperienceMapFuture.join();

        List<PermissionTeamMemberMemberDetailDTO> reportResult = users.stream().map(u -> {
            PermissionTeamMemberMemberDetailDTO result = BeanUtil.toBean(u, PermissionTeamMemberMemberDetailDTO.class);
            result.setUserTeam(topTeamMap.getOrDefault(u.getTeamId(), null));
            result.setUserName(CommonUtils.formatFullNameWithBlankCheck(result.getFirstName(),result.getLastName()));
            Set<String> leaderSet = teamLeaderMap.getOrDefault(u.getTeamId(), null);
            if (CollUtil.isNotEmpty(leaderSet)){
                result.setTeamLeader(leaderSet);
            }
            result.setLanguages(languagesMap.getOrDefault(u.getId(), null));
            if (CollUtil.isNotEmpty(result.getLanguages())){
                String languages = result.getLanguages().stream().filter(enumLanguageMap::containsKey).map(t -> reportUserLabelSearchDTO.getSort().getType() == SortType.EN ? enumLanguageMap.get(t).getEnDisplay() : enumLanguageMap.get(t).getCnDisplay()).sorted().collect(Collectors.joining(","));
                result.setLanguagesSort(languages);
            }
            if (null != result.getLevelOfExperience() && levelOfExperienceMap.containsKey(result.getLevelOfExperience())) {
                EnumLevelOfExperience levelOfExperience = levelOfExperienceMap.get(result.getLevelOfExperience());
                result.setLevelOfExperienceSort(reportUserLabelSearchDTO.getSort().getType() == SortType.EN ? levelOfExperience.getEnDisplay(): levelOfExperience.getCnDisplay());
            }
            result.setProcessDeliveryCounts(processIdMap.getOrDefault(u.getId(), null));
            result.setCountryDeliveryCounts(deliveryCountryMap.getOrDefault(u.getId(), null));

            return result;
        }).collect(Collectors.toList());
        if (headers != null){
            headers.set("Pagination-Count", reportResult.size() + "");
        }
        List<PermissionTeamMemberMemberDetailDTO> result = sortList(reportResult, reportUserLabelSearchDTO.getSort());
        return result;
    }


    public List<PermissionTeamMemberMemberDetailDTO> sortList(
            List<PermissionTeamMemberMemberDetailDTO> list,
            SortDTO sort) {

        String field = StringUtils.isNotBlank(sort.getField())? sort.getField() : "active";
        Order order = null != sort.getOrder()? sort.getOrder() : Order.ASC;
        Long id = sort.getId();
        Integer page = sort.getPage();
        Integer size = sort.getSize();


        Comparator<PermissionTeamMemberMemberDetailDTO> comparator = switch (field) {
            case "process" ->
                    // 排序基于 processDeliveryCounts 中的 id
                    Comparator.comparing(dto -> getProcessCount(dto, id), Comparator.nullsLast(Comparator.naturalOrder()));
            case "country" ->
                    // 排序基于 countryDeliveryCounts 中的 id
                    Comparator.comparing(dto -> getCountryCount(dto, id), Comparator.nullsLast(Comparator.naturalOrder()));
            case "userId" -> Comparator.comparing(PermissionTeamMemberMemberDetailDTO::getId, Comparator.nullsLast(Comparator.naturalOrder()));
            case "userName" -> Comparator.comparing(PermissionTeamMemberMemberDetailDTO::getUserName, Comparator.nullsLast(Comparator.naturalOrder()));
            case "jobTitle" -> Comparator.comparing(PermissionTeamMemberMemberDetailDTO::getJobTitle, Comparator.nullsLast(Comparator.naturalOrder()));
            case "email" -> Comparator.comparing(PermissionTeamMemberMemberDetailDTO::getEmail, Comparator.nullsLast(Comparator.naturalOrder()));
            case "userTeam" -> Comparator.comparing(PermissionTeamMemberMemberDetailDTO::getUserTeam, Comparator.nullsLast(Comparator.naturalOrder()));
            case "levelOfExperience" -> Comparator.comparing(PermissionTeamMemberMemberDetailDTO::getLevelOfExperienceSort, Comparator.nullsLast(Comparator.naturalOrder()));
            case "activateDate" -> Comparator.comparing(PermissionTeamMemberMemberDetailDTO::getActivateDate, Comparator.nullsLast(Comparator.naturalOrder()));
            case "languages" -> Comparator.comparing(PermissionTeamMemberMemberDetailDTO::getLanguagesSort, Comparator.nullsLast(Comparator.naturalOrder()));
            case "teamLeader" ->
                    // 排序 teamLeader 字段：按字母顺序升序或降序
                    Comparator.comparing(
                            dto -> String.join(",", dto.getTeamLeader()),
                            Comparator.nullsLast(Comparator.naturalOrder())
                    );
            case "active" -> Comparator
                    .comparing(PermissionTeamMemberMemberDetailDTO::getActivated, Comparator.reverseOrder())  // true before false
                    .thenComparing(PermissionTeamMemberMemberDetailDTO::getId);
            default -> throw new IllegalArgumentException("Unsupported sorting field: " + field);
        };

        // 根据排序顺序决定升序或降序
        if (Order.DESC.equals(order)) {
            comparator = comparator.reversed();
        }
        // 执行排序
        List<PermissionTeamMemberMemberDetailDTO> sortedList = list.stream()
                .sorted(comparator)
                .collect(Collectors.toList());

        // 处理分页
        if (page != null && size != null) {
            int fromIndex = Math.min(page * size, sortedList.size());
            int toIndex = Math.min((page + 1) * size, sortedList.size());
            return sortedList.subList(fromIndex, toIndex);
        }

        return sortedList;
    }

    // 根据流程 id 获取对应的数量（示例方法）
    private Long getProcessCount(PermissionTeamMemberMemberDetailDTO dto, Long processId) {
        if (CollUtil.isEmpty(dto.getProcessDeliveryCounts())){
            return 0L;
        }
        return dto.getProcessDeliveryCounts().stream()
                .filter(stat -> stat.getId().equals(processId))
                .map(DeliveryStatVM::getCount)
                .findFirst()
                .orElse(0L);  // 若找不到返回 0
    }

    // 根据国家 id 获取对应的数量（示例方法）
    private Long getCountryCount(PermissionTeamMemberMemberDetailDTO dto, Long countryId) {
        if (CollUtil.isEmpty(dto.getCountryDeliveryCounts())){
            return 0L;
        }
        return dto.getCountryDeliveryCounts().stream()
                .filter(stat -> stat.getId().equals(countryId))
                .map(DeliveryStatVM::getCount)
                .findFirst()
                .orElse(0L);  // 若找不到返回 0
    }

    private void initDTO(ReportUserLabelSearchDTO reportUserLabelSearchDTO) {
        if (reportUserLabelSearchDTO.getSort() == null){
            reportUserLabelSearchDTO.setSort(new SortDTO());
        }
        if (reportUserLabelSearchDTO.getSort().getType() == null){
            reportUserLabelSearchDTO.getSort().setType(SortType.EN);
        }
    }


    @Override
    public void exportUserLabelReport(ReportUserLabelSearchDTO reportUserLabelSearchDTO, HttpServletResponse response) throws IOException {
        List<PermissionTeamMemberMemberDetailDTO> voList = searchUserLabelReport(reportUserLabelSearchDTO, null);
        if (CollUtil.isEmpty(voList)){
            throw new CustomParameterizedException("no data found");
        }
        Map<Long, String> levelOfExperienceMap = enumLevelOfExperienceRepository.findAll().stream().filter(t-> StringUtils.isNotBlank(t.getEnDisplay())).collect(Collectors.toMap(EnumLevelOfExperience::getId, EnumLevelOfExperience::getEnDisplay));
        List<RecruitmentProcessVO> recruitmentProcessVOS = applicationService.getAllRecruitmentProcess();
        recruitmentProcessVOS = recruitmentProcessVOS.stream().filter(t -> !t.getJobType().equals(JobType.PAY_ROLL)).collect(Collectors.toList());
        Map<Long, String> processMap = recruitmentProcessVOS.stream().collect(Collectors.toMap(RecruitmentProcessVO::getId, RecruitmentProcessVO::getName));
        List<PermissionTeamMemberMemberDetailExcelDTO> excelList = voList.stream().map(t -> PermissionTeamMemberMemberDetailExcelDTO.from(t, levelOfExperienceMap)).collect(Collectors.toList());

        String zoneTime = LocalDateTime.now(ZoneId.of(reportUserLabelSearchDTO.getTimezone())).format(DateTimeFormatter.ofPattern("MM-dd-yyyy_HH_mm"));
        String fileName = "User_Label_Report_" + zoneTime + ".xlsx";
        // 设置响应头
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("charset", "utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);


        // 写入 Excel 文件
        EasyExcel.write(response.getOutputStream())
                .sheet("Member Details")
                .doWrite(exportToExcel(excelList, processMap, reportUserLabelSearchDTO.getTimezone()));

    }

    public static List<List<Object>> exportToExcel(List<PermissionTeamMemberMemberDetailExcelDTO> details, Map<Long, String> processMap, String timezone) throws IOException {
        List<List<Object>> data = new ArrayList<>();
        // 创建表头
        List<Object> header = new ArrayList<>();

        // 添加固定列头
        header.add("User ID");
        header.add("User Name");
        header.add("User Email Address");
        header.add("User Team");
        header.add("User Team Leader");
        header.add("User Job Title");
        header.add("Activate Date");
        header.add("Level of Experience");
        header.add("Language");
        header.addAll(processMap.values());
        for (CountryPackageEnum value : CountryPackageEnum.values()) {
            header.add(value.getDisplay());
        }
        data.add(header);
        for (PermissionTeamMemberMemberDetailExcelDTO detail: details){
            List<Object> row = new ArrayList<>();

            // 填充其他字段
            row.add(detail.getId());
            row.add(detail.getFullName());
            row.add(detail.getEmail());
            row.add(detail.getUserTeam());
            row.add(detail.getTeamLeader());
            row.add(detail.getJobTitle());
            row.add(formatDate(detail.getCreatedDate() , timezone, DateUtils.DATE_FORMAT_10));
            row.add(detail.getLevelOfExperience());
            row.add(detail.getLanguages());

            processMap.keySet().forEach(processId -> {
                row.add(CollUtil.isNotEmpty(detail.getProcessDeliveryCounts()) ? detail.getProcessDeliveryCounts().stream().filter(t -> t.getId().equals(processId) && t.getCount() != null).findFirst().orElse(new DeliveryStatVM(processId, 0L)).getCount() : 0L);
            });
            for (CountryPackageEnum value : CountryPackageEnum.values()) {
                row.add(CollUtil.isNotEmpty(detail.getCountryDeliveryCounts()) ? detail.getCountryDeliveryCounts().stream().filter(t -> value.getDbValue().equals(t.getId().intValue())  && t.getCount() != null).findFirst().orElse(new DeliveryStatVM(null, 0L)).getCount() : 0L);
            }
            data.add(row);
        }
        return data;
    }
    protected static String formatDate(Instant date, String timezone, String timeFormat) {
        return DateTimeFormatter.ofPattern(timeFormat)
                .withZone(ZoneId.of(timezone))
                .format(date);
    }

    private void setPermission(ReportUserLabelSearchDTO reportUserLabelSearchDTO, TeamDataPermissionRespDTO teamDataPermission) {
        if (teamDataPermission == null) {
            return;
        }
        if (teamDataPermission.getSelf()) {
            reportUserLabelSearchDTO.setUserIdList(List.of(SecurityUtils.getUserId()));
            reportUserLabelSearchDTO.setTeamIdList(List.of());
        } else if (teamDataPermission.getAll()) {
        } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
            reportUserLabelSearchDTO.setPermissionTeamIdList(teamDataPermission.getNestedTeamIds());
        }
    }
}
