package com.altomni.apn.report.service;


import com.altomni.apn.report.domain.vo.voip.VoipDetailReportVO;
import com.altomni.apn.report.domain.vo.voip.VoipReportVO;
import com.altomni.apn.common.dto.voip.VoipReportDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportDTO;
import com.altomni.apn.report.dto.voip.VoipDetailReportDTO;
import org.springframework.data.domain.Sort;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface VoipReportService {

    List<VoipReportVO> getVoipReport(VoipReportDTO voipReportDTO, Sort sort) throws ExecutionException, InterruptedException;

    List<VoipReportVO> getVoipReportV2(UserAdoptionReportDTO userAdoptionReportDTO, VoipReportDTO voipReportDTO, Sort sort) throws ExecutionException, InterruptedException;

    List<VoipDetailReportVO> getVoipDetailReport(VoipDetailReportDTO voipDetailReportDTO, Sort sort) throws ExecutionException, InterruptedException;

}
