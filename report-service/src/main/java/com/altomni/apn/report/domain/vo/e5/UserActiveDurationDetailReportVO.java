package com.altomni.apn.report.domain.vo.e5;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.OffsetDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserActiveDurationDetailReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String platform;

    private OffsetDateTime startTime;

    private OffsetDateTime endTime;

    private Long duration;

}
