package com.altomni.apn.report.domain.vo.p2;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.report.domain.vm.p1.ReportCountVM;
import com.altomni.apn.report.domain.vm.p2.ReportCountByCompanyVM;
import com.altomni.apn.report.domain.vo.p1.PipelineAnalyticsVO;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;

@Data
@Entity
public class PipelineAnalyticsByCompanyVO {

    @ExcelIgnore
    @Id
    private Long companyId;

    @ExcelProperty(value = "Company", index = 0)
    private String company;

    @ExcelProperty(value = "Sum of Submitted to Job", index = 1)
    private Integer appliedCount;

    @ExcelIgnore
    private String appliedActivityId;

    @ExcelProperty(value = "Sum of Submitted to Client", index = 2)
    private Integer submittedCount;

    @ExcelIgnore
    private String submittedActivityId;

    @ExcelProperty(value = "Sum of Interview", index = 3)
    private Integer interviewCount;

    @ExcelIgnore
    private String interviewActivityId;

    @ExcelProperty(value = "Sum of Offered", index = 4)
    private Integer offeredCount;

    @ExcelIgnore
    private String offeredActivityId;

    @ExcelProperty(value = "Sum of Offer Accepted", index = 5)
    private Integer offerAcceptedCount;

    @ExcelIgnore
    private String offerAcceptedActivityId;

    @ExcelProperty(value = "Sum of On boarded", index = 6)
    private Integer startedCount;

    @ExcelIgnore
    private String startedActivityId;

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Integer getAppliedCount() {
        return appliedCount;
    }

    public void setAppliedCount(Integer appliedCount) {
        this.appliedCount = appliedCount;
    }

    public String getAppliedActivityId() {
        return appliedActivityId;
    }

    public void setAppliedActivityId(String appliedActivityId) {
        this.appliedActivityId = appliedActivityId;
    }

    public Integer getSubmittedCount() {
        return submittedCount;
    }

    public void setSubmittedCount(Integer submittedCount) {
        this.submittedCount = submittedCount;
    }

    public String getSubmittedActivityId() {
        return submittedActivityId;
    }

    public void setSubmittedActivityId(String submittedActivityId) {
        this.submittedActivityId = submittedActivityId;
    }

    public Integer getInterviewCount() {
        return interviewCount;
    }

    public void setInterviewCount(Integer interviewCount) {
        this.interviewCount = interviewCount;
    }

    public String getInterviewActivityId() {
        return interviewActivityId;
    }

    public void setInterviewActivityId(String interviewActivityId) {
        this.interviewActivityId = interviewActivityId;
    }

    public Integer getOfferedCount() {
        return offeredCount;
    }

    public void setOfferedCount(Integer offeredCount) {
        this.offeredCount = offeredCount;
    }

    public String getOfferedActivityId() {
        return offeredActivityId;
    }

    public void setOfferedActivityId(String offeredActivityId) {
        this.offeredActivityId = offeredActivityId;
    }

    public Integer getOfferAcceptedCount() {
        return offerAcceptedCount;
    }

    public void setOfferAcceptedCount(Integer offerAcceptedCount) {
        this.offerAcceptedCount = offerAcceptedCount;
    }

    public String getOfferAcceptedActivityId() {
        return offerAcceptedActivityId;
    }

    public void setOfferAcceptedActivityId(String offerAcceptedActivityId) {
        this.offerAcceptedActivityId = offerAcceptedActivityId;
    }

    public Integer getStartedCount() {
        return startedCount;
    }

    public void setStartedCount(Integer startedCount) {
        this.startedCount = startedCount;
    }

    public String getStartedActivityId() {
        return startedActivityId;
    }

    public void setStartedActivityId(String startedActivityId) {
        this.startedActivityId = startedActivityId;
    }

    public static PipelineAnalyticsByCompanyVO fromReportCountVM(ReportCountByCompanyVM reportCountByCompanyVM) {
        PipelineAnalyticsByCompanyVO pipelineAnalyticsByCompanyVO = new PipelineAnalyticsByCompanyVO();
        switch (reportCountByCompanyVM.getType()) {
            case SUBMIT_TO_JOB:
                pipelineAnalyticsByCompanyVO.setAppliedCount(reportCountByCompanyVM.getCount());
                pipelineAnalyticsByCompanyVO.setAppliedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case SUBMIT_TO_CLIENT:
                pipelineAnalyticsByCompanyVO.setSubmittedCount(reportCountByCompanyVM.getCount());
                pipelineAnalyticsByCompanyVO.setSubmittedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case INTERVIEW:
                pipelineAnalyticsByCompanyVO.setInterviewCount(reportCountByCompanyVM.getCount());
                pipelineAnalyticsByCompanyVO.setInterviewActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case OFFER:
                pipelineAnalyticsByCompanyVO.setOfferedCount(reportCountByCompanyVM.getCount());
                pipelineAnalyticsByCompanyVO.setOfferedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case OFFER_ACCEPT:
                pipelineAnalyticsByCompanyVO.setOfferAcceptedCount(reportCountByCompanyVM.getCount());
                pipelineAnalyticsByCompanyVO.setOfferAcceptedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case ON_BOARD:
                pipelineAnalyticsByCompanyVO.setStartedCount(reportCountByCompanyVM.getCount());
                pipelineAnalyticsByCompanyVO.setStartedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            default: break;
        }

        pipelineAnalyticsByCompanyVO.setCompanyId(reportCountByCompanyVM.getCompanyId());
        return pipelineAnalyticsByCompanyVO;
    }

    public PipelineAnalyticsByCompanyVO setCountAndIds(ReportCountByCompanyVM reportCountByCompanyVM) {
        switch (reportCountByCompanyVM.getType()) {
            case SUBMIT_TO_JOB:
                this.setAppliedCount(reportCountByCompanyVM.getCount());
                this.setAppliedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case SUBMIT_TO_CLIENT:
                this.setSubmittedCount(reportCountByCompanyVM.getCount());
                this.setSubmittedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case INTERVIEW:
                this.setInterviewCount(reportCountByCompanyVM.getCount());
                this.setInterviewActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case OFFER:
                this.setOfferedCount(reportCountByCompanyVM.getCount());
                this.setOfferedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case OFFER_ACCEPT:
                this.setOfferAcceptedCount(reportCountByCompanyVM.getCount());
                this.setOfferAcceptedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            case ON_BOARD:
                this.setStartedCount(reportCountByCompanyVM.getCount());
                this.setStartedActivityId(reportCountByCompanyVM.getActivityIds());
                break;
            default: break;
        }
        return this;
    }

    public PipelineAnalyticsByCompanyVO() {
        this.appliedCount = 0;
        this.submittedCount = 0;
        this.interviewCount = 0;
        this.offeredCount = 0;
        this.offerAcceptedCount = 0;
        this.startedCount = 0;
    }
}
