package com.altomni.apn.report.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Getter
@Setter
@Component
@ConfigurationProperties(prefix = "spring.datasource.starrocks")
public class StarRocksProperties {

    private boolean enabled;
    private String url;
    private String username;
    private String password;
}
