package com.altomni.apn.report.service.mapper;

import com.altomni.apn.report.domain.Report;
import com.altomni.apn.report.dto.ReportDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.openapitools.client.model.ChartDataQueryObject;

@Mapper(componentModel = "spring",nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS)
public interface ReportMapper {

    ObjectMapper objectMapper = new ObjectMapper();

    @Mapping(target = "config", source = "config", qualifiedByName = "jsonStringToReportConfig")
    ReportDTO toDto(Report entity);

    @Mapping(target = "config", source = "config", qualifiedByName = "reportConfigToJsonString")
    Report toEntity(ReportDTO dto);

    @Named("jsonStringToReportConfig")
    static ChartDataQueryObject jsonStringToReportConfig(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonString, ChartDataQueryObject.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting JSON string to Object", e);
        }
    }

    @Named("reportConfigToJsonString")
    static String reportConfigToJsonString(ChartDataQueryObject dataQueryObject) {
        if (dataQueryObject == null) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(dataQueryObject);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting Object to JSON string", e);
        }


    }

}
