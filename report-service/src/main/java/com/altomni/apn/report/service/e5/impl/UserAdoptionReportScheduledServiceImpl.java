package com.altomni.apn.report.service.e5.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.report.config.env.ApplicationProperties;
import com.altomni.apn.report.domain.UserAdoptionAlertLarkNotification;
import com.altomni.apn.report.repository.UserAdoptionAlertLarkNotificationRepository;
import com.altomni.apn.report.service.e5.UserAdoptionReportScheduledService;
import com.altomni.apn.report.service.e5.UserAdoptionReportService;
import com.altomni.apn.report.service.lark.LarkMessageNotificationService;
import com.altomni.apn.report.service.user.UserService;
import com.nimbusds.oauth2.sdk.util.CollectionUtils;
import com.nimbusds.oauth2.sdk.util.MapUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * Service Implementation for UserAdoptionReportScheduledService.
 */
@Slf4j
@Service
public class UserAdoptionReportScheduledServiceImpl implements UserAdoptionReportScheduledService {

    @Resource
    private UserAdoptionReportService userAdoptionReportService;

    @Resource
    private UserService userService;

    @Resource
    private UserAdoptionAlertLarkNotificationRepository userAdoptionAlertLarkNotificationRepository;

    @Resource
    private LarkMessageNotificationService larkMessageNotificationService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private HttpService httpService;

    @Scheduled(cron = "0 10 0 * * MON", zone = "UTC")
//    @Scheduled(cron = "0 0/15 * * * *")
    public void sendUserAdoptionReportToTeamLeader() {
        List<UserAdoptionAlertLarkNotification> list = userAdoptionAlertLarkNotificationRepository.findAll();
        LoginUtil.simulateLoginWithClient();

        Integer threshold = userAdoptionReportService.getActiveDurationThreshold();

        for (UserAdoptionAlertLarkNotification notification : list) {
            if (CollectionUtils.isEmpty(notification.getTeamIds())) {
                continue;
            }

            Map<Long, Long> belowAverageUsers = userAdoptionReportService.getLastWeekBelowAverageActiveDurationUserByTeamId(notification.getTenantId(), notification.getTeamIds());
            if (MapUtils.isEmpty(belowAverageUsers)) {
                continue;
            }

            Set<Long> userIds = new HashSet<>(belowAverageUsers.keySet());
            userIds.add(notification.getUserId());
            List<UserBriefDTO> users = userService.getAllBriefUserByIds(userIds).getBody();

            Map<Long, UserBriefDTO> userMap = users.stream().collect(Collectors.toMap(UserBriefDTO::getId, user -> user));

            String link = applicationProperties.getReportLinkUrl() + notification.getTeamIds().stream().map(String::valueOf).collect(Collectors.joining(","));

//            sendLarkMsg(belowAverageUsers, userMap, notification.getUserId(), threshold, link);
            sendLarkMsgViaAnycross(belowAverageUsers, userMap, notification.getUserId(), threshold, link, applicationProperties.getLarkCallbackUrl());
        }

    }

    private void sendLarkMsgViaAnycross(Map<Long, Long> belowAverageUsers, Map<Long, UserBriefDTO> userMap, Long teamLeaderId, Integer threshold, String url, String larkLink) {
        String content = buildLarkJsonStr(belowAverageUsers, userMap, teamLeaderId, threshold, url);
//        sendLarkNotification(content, userMap.get(teamLeaderId));
        sendLarkMessageViaAnycross(content, larkLink);
    }

    private void sendLarkMsg(Map<Long, Long> belowAverageUsers, Map<Long, UserBriefDTO> userMap, Long teamLeaderId, Integer threshold, String url) {
        String content = buildLarkMsg(belowAverageUsers, userMap, threshold, url);
        sendLarkNotification(content, userMap.get(teamLeaderId));
    }

    private String buildLarkMsg(Map<Long, Long> belowAverageUsers, Map<Long, UserBriefDTO> userMap, Integer ave, String link) {
        // 1. 计算日期区间
        LocalDate today   = LocalDate.now();
        LocalDate toDate  = today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
        LocalDate fromDate = toDate.minusDays(6);

        // 2. 先按 value（秒）升序排序，取前 10 条
        List<Map.Entry<Long, Long>> top10 = belowAverageUsers.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .limit(10)
                .collect(Collectors.toList());

        // 3. 动态计算每列最大宽度（包括表头）
        String headerName  = "User Name";
        String headerEmail = "Email";
        String headerDur   = "Weekly Active";

        int nameColWidth  = headerName.length();
        int emailColWidth = headerEmail.length();
        int durColWidth   = headerDur.length();

        for (var e : top10) {
            UserBriefDTO u = userMap.get(e.getKey());
            if (u == null) continue;
            nameColWidth  = Math.max(nameColWidth,  u.getFullName().length());
            emailColWidth = Math.max(emailColWidth, u.getEmail().length());
            durColWidth   = Math.max(durColWidth,   (e.getValue() / 60 + " min").length());
        }

        // 4. 构造格式串：每列宽度固定，左对齐，列间一个空格
        String rowFmt = "%-" + nameColWidth + "s "
                + "%-" + emailColWidth + "s "
                + "%-" + durColWidth   + "s%n";

        StringBuilder sb = new StringBuilder();
        sb.append("User Adoption Alert\n")
                .append("Period: ").append(fromDate).append(" - ").append(toDate).append("\n")
                .append("In your team, ").append(belowAverageUsers.size())
                .append(" users have below‑average weekly active duration.\n\n");

        // 5. 输出表头
        sb.append(String.format(rowFmt, headerName, headerEmail, headerDur));

        // 6. 输出分隔线
        sb.append("-".repeat(nameColWidth)).append(" ")
                .append("-".repeat(emailColWidth)).append(" ")
                .append("-".repeat(durColWidth)).append("\n");

        // 7. 输出内容行
        for (var e : top10) {
            UserBriefDTO u     = userMap.get(e.getKey());
            if (u == null) continue;
            String name        = u.getFullName();
            String email       = u.getEmail();
            String durStr      = (e.getValue() / 60) + " min";
            sb.append(String.format(rowFmt, name, email, durStr));
        }

        // 8. 附加信息及链接
        sb.append("\nAdditional Information:\n")
                .append("The average weekly active duration is ").append(ave)
                .append(" minutes, which serves as the alert threshold.\n")
//                .append("Reference activity durations by role: AM - XX minutes, ")
//                .append("Recruiter/Sourcer - XX minutes, other roles - XX minutes.\n\n")
                .append("Action Button: View Details: ").append(link);

        return sb.toString();
    }

    private String buildLarkJsonStr(Map<Long, Long> belowAverageUsers,
                                                  Map<Long, UserBriefDTO> userMap,
                                                  Long teamLeaderId,
                                                  Integer ave,
                                                  String detailsReportUrl) {
        // 1. 计算上一周周一到周日
        LocalDate today   = LocalDate.now();
        LocalDate toDate  = today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
        LocalDate fromDate = toDate.minusDays(6);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.ENGLISH);
        String startDate = fromDate.format(fmt);
        String endDate   = toDate.format(fmt);

        // 2. 按活跃时长升序排序并取前 10
        List<Map.Entry<Long, Long>> sortedList = belowAverageUsers.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .limit(10)
                .collect(Collectors.toList());

        // 3. 构建 JSON 对象
        JSONObject root = new JSONObject();
        root.put("startDate",              startDate);
        root.put("endDate",                endDate);
        root.put("totalCount",             belowAverageUsers.size());
        root.put("averageActiveDuration",  ave);
        root.put("detailsReportUrl",       detailsReportUrl);

        // 收件人邮箱
        String receiverEmail = "";
        UserBriefDTO leader = userMap.get(teamLeaderId);
        if (leader != null && StringUtils.isNotBlank(leader.getEmail())) {
            receiverEmail = leader.getEmail();
        } else {
            log.error("The userId: {} cannot be found when send lark voicemail notification.", teamLeaderId);
        }
        root.put("receiverEmail", receiverEmail);

        // 构建用户列表
        JSONArray userList = new JSONArray();
        for (Map.Entry<Long, Long> entry : sortedList) {
            UserBriefDTO u = userMap.get(entry.getKey());
            if (u == null) continue;
            JSONObject userObj = new JSONObject();
            userObj.put("userName", u.getFullName());
            userObj.put("email",    u.getEmail());
            userObj.put("activeDuration", entry.getValue() / 60);
            userList.add(userObj);
        }
        root.put("userList", userList);

        // 返回格式化后的 JSON 字符串
        return root.toStringPretty();
    }

    private void sendLarkNotification(String content, UserBriefDTO userBriefDTO) {
        LoginUtil.simulateLoginWithClient();
        log.info("send lark voicemail notification to user: {}", userBriefDTO);
        try {
            if(userBriefDTO == null || userBriefDTO.getEmail().isBlank()) log.error("The user: {} cannot be found when send lark voicemail notification.", userBriefDTO);
            else larkMessageNotificationService.sendLarkMessage(content, userBriefDTO.getEmail());
        }
        catch (Exception e) {
            log.error("send lark voicemail notification to user: {} error. Error message: {}, stackTrace: {}", userBriefDTO, e.getMessage(), e.getStackTrace());
        }
    }

    private void sendLarkMessageViaAnycross(String content, String url) {
        try {
            log.info("sendLarkMessageViaAnycross, url = {}, json body ={}",url, content);
            HttpResponse response = httpService.post(url, content);
            if (Objects.equals(response.getCode(), 200)) {
                log.info("sendLarkMessageViaAnycross is success, url = {}, result = {}", url, response.getBody());
            } else {
                log.error("sendLarkMessageViaAnycross is fail url = {}, result = {}", url, response.getBody());
            }
        } catch (Exception e) {
            log.error("sendLarkMessageViaAnycross exception url = {}, result = {}", url, ExceptionUtil.getAllExceptionMsg(e));
        }
    }

}
