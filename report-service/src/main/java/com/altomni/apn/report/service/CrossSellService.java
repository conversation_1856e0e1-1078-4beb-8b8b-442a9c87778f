package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.CrossSellVO;
import com.altomni.apn.report.domain.vo.ReportSalesCompanyVo;
import com.altomni.apn.report.domain.vo.ReportSalesDetailVo;
import com.altomni.apn.report.domain.vo.ReportSalesViewStatsVo;
import com.altomni.apn.report.dto.CompanyServiceTypeDTO;
import com.altomni.apn.report.dto.ReportSalesParamDto;
import com.altomni.apn.report.dto.SaleDetailDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface CrossSellService {

    CrossSellVO getRevenueGp(CompanyServiceTypeDTO dto);
}
