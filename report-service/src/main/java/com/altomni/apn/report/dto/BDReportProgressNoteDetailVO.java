package com.altomni.apn.report.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BDReportProgressNoteDetailVO implements Serializable {

    private Long accountCompanyId;

    private String companyName;

    private List<Long> accountContactId;

    private List<Long> apnTalentId;

    private List<Long> salesLeadContactId;

    private Instant contactTime;

    private String note;

    private String serviceType;

    private String serviceTypeName;

    private String salesLeadName;

    private String bdOwnerName;

    private Long accountBusinessId;

}
