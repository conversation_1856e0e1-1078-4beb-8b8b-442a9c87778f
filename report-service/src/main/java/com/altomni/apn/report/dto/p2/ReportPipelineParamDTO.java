package com.altomni.apn.report.dto.p2;

import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportPipelineParamDTO implements Serializable {

    private static final long serialVersionUID = 6994704573277709468L;

    private String fromDate;

    private String toDate;

    private Long userId;

    private UserRole userRole;

    private String jobCountry;

    @JsonIgnore
    private Set<Long> userIds;

    @JsonIgnore
    private Set<Long> teamIds;

    public static final String ALL = "all";
}
