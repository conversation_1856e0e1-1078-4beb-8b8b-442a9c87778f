package com.altomni.apn.report.dto;

import com.altomni.apn.common.dto.search.SearchSortDTO;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ReportMonthlyRevenueGpSearchDTO {

    private String startDate;

    private String endDate;

    private List<Long> userIdList;

    private List<Long> teamIdList;

    private String timezone;

    private SearchSortDTO sort;

    private Integer size;

    private Integer page;

}
