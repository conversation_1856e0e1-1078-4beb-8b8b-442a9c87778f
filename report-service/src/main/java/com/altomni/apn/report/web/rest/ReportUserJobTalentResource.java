package com.altomni.apn.report.web.rest;

import cn.hutool.core.collection.CollectionUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.ReportAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.config.env.ReportApiPromptProperties;
import com.altomni.apn.report.domain.ReportUserJobTalentDTO;
import com.altomni.apn.report.domain.vo.s1.ResignUserReportJobVO;
import com.altomni.apn.report.domain.vo.s1.ResignUserReportTalentVO;
import com.altomni.apn.report.dto.ReportJobParam;
import com.altomni.apn.report.dto.s1.JobSearchDTO;
import com.altomni.apn.report.dto.s1.TalentSearchDTO;
import com.altomni.apn.report.service.ReportUserJobTalentService;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * REST controller for managing Report.
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3")
public class ReportUserJobTalentResource {

    private final Logger log = LoggerFactory.getLogger(ReportUserJobTalentResource.class);

    private final ReportUserJobTalentService reportUserJobTalentService;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;
    private final ReportApiPromptProperties reportApiPromptProperties;

    public ReportUserJobTalentResource(ReportUserJobTalentService reportUserJobTalentService,
                                       CommonApiMultilingualConfig commonApiMultilingualConfig,
                                       ReportApiPromptProperties reportApiPromptProperties) {
        this.reportUserJobTalentService = reportUserJobTalentService;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.reportApiPromptProperties = reportApiPromptProperties;
    }

    /**
     * get job company source data
     */
    @GetMapping("/resign-user-jobs-talents")
    @Timed
    public ResponseEntity<List<ReportUserJobTalentDTO>> getUserResignData(@RequestParam(value = "teamId", required = false) Long teamId,
                                                                          @RequestParam(value = "divisionId", required = false) Long divisionId) {
        ReportJobParam reportParam = new ReportJobParam().teamId(teamId).divisionId(divisionId);
        log.info("[APN: ReportUserJobTalent @{}] REST request to get user jobs and talents. reportParam:{}", SecurityUtils.getUserId(), reportParam);
        return ResponseEntity.ok(reportUserJobTalentService.getUserResignData(reportParam));
    }

    @PostMapping("/report/jobs/jobIds")
    @NoRepeatSubmit
    public ResponseEntity<List<ResignUserReportJobVO>> getJobsByIds(@Valid @RequestBody JobSearchDTO jobSearchDTO) {
        log.info("[APN: ReportUserJobTalent @{}] REST request to get jobs by ids: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobSearchDTO);
        List<Long> jobIdList = new ArrayList<>();
        try {
            CollectionUtil.addAll(jobIdList, Stream.of(jobSearchDTO.getJobIds().split(",")).map(Long::valueOf).distinct().collect(Collectors.toList()));
        } catch (Exception e) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTUSERJOB_PARAM_EXCEPTION.getKey(),
                    CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }

        List<ResignUserReportJobVO> result = reportUserJobTalentService.findAllJobByIds(jobIdList);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping("/report/talents/talentIds")
    @NoRepeatSubmit
    public ResponseEntity<List<ResignUserReportTalentVO>> getTalentsByIds(@Valid @RequestBody TalentSearchDTO talentSearchDTO) {
        log.info("[APN: ReportUserJobTalent @{}] REST request to get talents by ids: {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), talentSearchDTO);
        List<Long> talentIdList = new ArrayList<>();
        try {
            CollectionUtil.addAll(talentIdList, Stream.of(talentSearchDTO.getTalentIds().split(",")).map(Long::valueOf).distinct().collect(Collectors.toList()));
        } catch (Exception e) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTUSERJOB_PARAM_EXCEPTION.getKey(),
                    CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }

        List<ResignUserReportTalentVO> result = reportUserJobTalentService.findAllTalentByIds(talentIdList);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }


}
