package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.InterviewType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.report.domain.enumeration.PlatformTypeEnum;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ApplicationDetailSearchDto {

    private String fullName;

    private String jobTitle;

    private Long jobId;

    private String jobCode;

    private String companyName;

    private Long companyId;

    private JobStatus jobStatus;

    private ReportTableType workflowStatus;

    private String jobLocation;

    private Long am;

    private Long recruiter;

    private Long sourcer;

    private Long ac;

    private Long dm;

    private Long owner;

    private Long salesLeadOwner;

    private Long bdOwner;

    private Long coAmList;

    private Long lastModifiedBy;

    private Long interviewProgress;

    private InterviewType interviewType;

    private EliminateReason reason;

    private Boolean flexibleLocation;

    private PlatformTypeEnum platform;

    private String name;

    private String stayedOver;

    private String createdBy;

}
