package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.ReportJobByCompanyVo;
import com.altomni.apn.report.dto.ReportJobParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;

public interface ReportJobCompanyService extends ReportJobBaseService {

    List<ReportJobByCompanyVo> getJobsByCompanySourceData(ReportJobParam reportParam) throws ExecutionException, InterruptedException;

    void exportJobByCompanyByExcel(ReportJobParam reportParam, HttpServletResponse response) throws ExecutionException, InterruptedException;

}
