package com.altomni.apn.report.domain.vo.s3;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
public class DormantApplicationVO implements Serializable, AttachConfidentialTalent {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "application id")
    private Long id;

    @ApiModelProperty(value = "application status")
    private ReportTableType status;

    @ApiModelProperty(value = "talent id")
    private Long talentId;

    @ApiModelProperty(value = "talent full name")
    private String fullName;

    @ApiModelProperty(value = "the job id")
    private Long jobId;

    @ApiModelProperty(value = "the job title this talent applied")
    private String jobTitle;

    @ApiModelProperty(value = "Job type", allowableValues = "Direct_Placement, Contract, Right_To_Hire, Full_Time, Part_Time")
    private JobType jobType;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "Open, OnHold, Cancelled, Closed")
    private JobStatus jobStatus;

    @ApiModelProperty(value = "the company id this talent applied")
    private Long companyId;

    @ApiModelProperty(value = "the company name this talent applied")
    private String company;

    @ApiModelProperty(value = "AM user id")
    private Long amId;

    @ApiModelProperty(value = "AM full name")
    private String am;

    @ApiModelProperty(value = "recruiter user id")
    private Long recruiterId;

    @ApiModelProperty(value = "recruiter full name")
    private String recruiter;

    @ApiModelProperty(value = "last update time")
    private Instant lastModifiedDate;

    private Boolean confidentialTalentViewAble;


    private ConfidentialInfoDto confidentialInfo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ReportTableType getStatus() {
        return status;
    }

    public void setStatus(ReportTableType status) {
        this.status = status;
    }

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public JobType getJobType() {
        return jobType;
    }

    public void setJobType(JobType jobType) {
        this.jobType = jobType;
    }

    public JobStatus getJobStatus() {
        return jobStatus;
    }

    public void setJobStatus(JobStatus jobStatus) {
        this.jobStatus = jobStatus;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Long getAmId() {
        return amId;
    }

    public void setAmId(Long amId) {
        this.amId = amId;
    }

    public String getAm() {
        return am;
    }

    public void setAm(String am) {
        this.am = am;
    }

    public Long getRecruiterId() {
        return recruiterId;
    }

    public void setRecruiterId(Long recruiterId) {
        this.recruiterId = recruiterId;
    }

    public String getRecruiter() {
        return recruiter;
    }

    public void setRecruiter(String recruiter) {
        this.recruiter = recruiter;
    }

    public Instant getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Instant lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    @Override
    public Boolean getConfidentialTalentViewAble() {
        return confidentialTalentViewAble;
    }

    @Override
    public void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble) {
        this.confidentialTalentViewAble = confidentialTalentViewAble;
    }

    @Override
    public void encrypt() {
        this.status = null;
        this.fullName = null;
        this.jobId = null;
        this.jobTitle = null;
        this.jobType = null;
        this.jobStatus = null;
        this.companyId = null;
        this.company = null;
        this.amId = null;
        this.am = null;
        this.recruiterId = null;
        this.recruiter = null;
        this.lastModifiedDate = null;
    }

    @Override
    public ConfidentialInfoDto getConfidentialInfo() {
        return confidentialInfo;
    }

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        this.confidentialInfo = confidentialInfo;
    }

}
