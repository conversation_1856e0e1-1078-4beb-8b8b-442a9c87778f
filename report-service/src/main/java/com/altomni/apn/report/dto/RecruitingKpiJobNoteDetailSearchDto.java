package com.altomni.apn.report.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiDetailBaseDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiJobNoteDetailSearchDto extends RecruitingKpiDetailBaseDto {

    private JobNoteDetailSearchDto detail;

    public String getOrderBySql() {
        if (ObjectUtil.isEmpty(getSort())) {
            return " ";
        }
        return switch (getSort().getProperty()) {
            case "jobTitle" -> " order by CONVERT(j.title USING gbk) " + getSort().getDirection();
            case "createdDate" -> " order by jn.created_date " + getSort().getDirection();
            case "lastModifiedDate" -> " order by jn.last_modified_date " + getSort().getDirection();
            default -> "";
        };
    }

    public void appendDetailCondition(StringBuilder sb, Map<String, Object> whereParamMap) {
        if (detail == null) {
            return;
        }
        if (StrUtil.isNotBlank(detail.getJobTitle())) {
            sb.append(" and j.title like :jobTitle ");
            whereParamMap.put("jobTitle", "%" + detail.getJobTitle() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getJobId())) {
            sb.append(" and jn.job_id = :jobId ");
            whereParamMap.put("jobId", detail.getJobId());
        }
        if (ObjectUtil.isNotEmpty(detail.getCreatedBy())) {
            sb.append(" and jn.puser_id = :createdBy ");
            whereParamMap.put("createdBy", detail.getCreatedBy());
        }
        if (ObjectUtil.isNotEmpty(detail.getLastModifiedBy())) {
            sb.append(" and jn.last_update_user_id = :lastModifiedBy ");
            whereParamMap.put("lastModifiedBy", detail.getLastModifiedBy());
        }
    }

}
