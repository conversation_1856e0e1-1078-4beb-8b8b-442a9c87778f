package com.altomni.apn.report.repository;

import com.altomni.apn.report.domain.Report;
import com.altomni.apn.report.domain.vo.TeamIdName;
import com.altomni.apn.report.domain.vo.UserTeam;
import com.altomni.apn.report.dto.UserTeamPariDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public interface ReportRepository extends JpaRepository<Report, Long> {

    //todo maybe ignore config
    Page<Report> findAllByTenantId(Long tenantId, Pageable pageable);

    Optional<Report> findByIdAndTenantId(Long id, Long tenantId);

    @Query(value = "select distinct j.id from job j " +
            "left join user_job_relation ujr on j.id = ujr.job_id and ujr.status = 1 " +
            "where j.pteam_id=:privateJobTeamId and (j.puser_id =:userId or ujr.user_id=:userId)", nativeQuery = true)
    List<Long> getJobIdsForPrivateJob(@Param("userId") Long userId, @Param("privateJobTeamId") Long privateJobTeamId);

    @Query(value = "select distinct j.id from job j " +
            "left join user_job_relation ujr on j.id = ujr.job_id and ujr.status = 1 and ujr.user_id = :userId " +
            "where j.pteam_id= :privateJobTeamId and (j.puser_id != :userId and ujr.user_id IS NULL )", nativeQuery = true)
    List<Long> getJobIdsForPrivateJobInvalid(@Param("userId") Long userId, @Param("privateJobTeamId") Long privateJobTeamId);

    @Query(value = "select j.id from job j " +
            "inner join job_project p on p.id=j.pteam_id " +
            "inner join permission_user_team ut on ut.user_id=j.puser_id  and ut.is_primary=1 " +
            "where ut.team_id in :teamIds", nativeQuery = true)
    Set<Long> getPrivateJobIdsByTeamIds(@Param("teamIds") Collection<Long> teamIds);

    @Query(value = "select j.id from job_project j " +
            "where j.tenant_id =:tenantId", nativeQuery = true)
    Set<Long> findPrivateJobTeamIds(@Param("tenantId") Long tenantId);

    @Query(value = "select user_id as userId, team_id as teamId from permission_user_team where user_id in :userIds", nativeQuery = true)
    List<UserTeamPariDTO> getUserTeamPair(@Param("userIds") Collection<Long> userIds);

    @Query(value = "select user_id as userId, team_id as teamId from permission_user_team where user_id in :userIds and is_primary = 1", nativeQuery = true)
    List<UserTeamPariDTO> getUserTeamPairV2(@Param("userIds") Collection<Long> userIds);

    @Query(value = "SELECT count(1) FROM user_role WHERE user_id = ?1 and role_id = 100 ", nativeQuery = true)
    long existsTenantAdminRoleByUserId(Long userId);


    @Query(value = "select u.id userId, concat(u.first_name, ' ', u.last_name) userName, t.id teamId, t.name teamName from user u " +
            "left join permission_user_team put on u.id = put.user_id " +
            "left join permission_team t on t.id = put.team_id " +
            "where u.activated in :activeStatus and u.tenant_id=:tenantId and (put.is_primary=1 or put.is_primary is null)", nativeQuery = true)
    List<UserTeam> findUsersByTenantId(@Param("tenantId") Long tenantId, @Param("activeStatus") Collection<Boolean> activeStatus);

    @Query(value = "select u.id userId, concat(u.first_name, ' ', u.last_name) userName, t.id teamId, t.name teamName from user u " +
            "left join permission_user_team put on u.id = put.user_id " +
            "left join permission_team t on t.id = put.team_id " +
            "where u.id in :userIds  and u.activated in :activeStatus and u.tenant_id=:tenantId and put.is_primary=1", nativeQuery = true)
    List<UserTeam> findUsersByUserIds(@Param("userIds") Collection<Long> userIds, @Param("tenantId") Long tenantId, @Param("activeStatus") Collection<Boolean> activeStatus);

    @Query(value = "select u.id userId, concat(u.first_name, ' ', u.last_name) userName, t.id teamId, t.name teamName from user u " +
            "left join permission_user_team put on u.id = put.user_id " +
            "left join permission_team t on t.id = put.team_id " +
            "where put.team_id in :teamIds and put.is_primary=1 and u.activated in :activeStatus and u.tenant_id=:tenantId", nativeQuery = true)
    List<UserTeam> findUsersByTeamIds(@Param("teamIds") Collection<Long> teamIds, @Param("tenantId") Long tenantId, @Param("activeStatus") Collection<Boolean> activeStatus);

    @Query(value = "select u.id userId, concat(u.first_name, ' ', u.last_name) userName, t.id teamId, t.name teamName from user u " +
            "left join permission_user_team put on u.id = put.user_id " +
            "left join permission_team t on t.id = put.team_id " +
            "where (u.id in :userIds or put.team_id in :teamIds) and u.activated in :activeStatus and u.tenant_id=:tenantId and put.is_primary=1", nativeQuery = true)
    List<UserTeam> findUsersByUserIdsAndTeamIds(@Param("userIds") Collection<Long> userIds, @Param("teamIds") Collection<Long> teamIds, @Param("tenantId") Long tenantId, @Param("activeStatus") Collection<Boolean> activeStatus);

    @Query(value = "select t.id, t.name, t.level AS teamLevel, t.parent_id AS parentId, t.is_leaf AS isLeaf from permission_team t " +
            "where t.tenant_id=:tenantId and t.deleted=0", nativeQuery = true)
    List<TeamIdName> findTeamsByTenantId(@Param("tenantId") Long tenantId);

    @Query(value = "select t.id, t.name, t.level AS teamLevel, t.parent_id AS parentId, t.is_leaf AS isLeaf from permission_team t " +
            "inner join permission_user_team ut on ut.team_id=t.id " +
            "inner join user u on u.id=ut.user_id " +
            "where t.tenant_id=:tenantId and ut.is_primary=1 and u.id in :userIds and t.deleted=0", nativeQuery = true)
    List<TeamIdName> findTeamsByUserIds(@Param("userIds") Collection<Long> userIds, @Param("tenantId") Long tenantId);

    @Query(value = "select t.id, t.name, t.level AS teamLevel, t.parent_id AS parentId, t.is_leaf AS isLeaf from permission_team t " +
            "where t.tenant_id=:tenantId and t.id in :teamIds and t.deleted=0", nativeQuery = true)
    List<TeamIdName> findTeamsByTeamIds(@Param("teamIds") Collection<Long> teamIds, @Param("tenantId") Long tenantId);

    @Query(value = "select distinct t.id, t.name, t.level AS teamLevel, t.parent_id AS parentId, t.is_leaf AS isLeaf from permission_team t " +
            "left join permission_user_team ut on ut.team_id=t.id " +
            "left join user u on u.id=ut.user_id " +
            "where t.tenant_id=:tenantId and ut.is_primary=1 and (u.id in :userIds or t.id in :teamIds) and t.deleted=0", nativeQuery = true)
    List<TeamIdName> findTeamsByUserIdsAndTeamIds(@Param("userIds") Collection<Long> userIds, @Param("teamIds") Collection<Long> teamIds, @Param("tenantId") Long tenantId);

}
