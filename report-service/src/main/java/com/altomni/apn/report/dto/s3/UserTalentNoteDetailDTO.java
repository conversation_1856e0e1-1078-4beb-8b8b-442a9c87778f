package com.altomni.apn.report.dto.s3;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.Instant;

@Data
@Entity
public class UserTalentNoteDetailDTO implements AttachConfidentialTalent {

    @Id
    private Long id;

    private Long talentId;

    private String title;

    private String note;

    private Instant createdDate;

    private String name;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    @Override
    public void encrypt() {
       this.title = null;
       this.note = null;
       this.name = null;
       this.createdDate = null;
    }
}
