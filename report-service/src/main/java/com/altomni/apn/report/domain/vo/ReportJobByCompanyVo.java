package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
public class ReportJobByCompanyVo implements Serializable {

    @Id
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "Company", index = 0)
    private String name;

    @ExcelProperty(value = "Sum of Openings", index = 1)
    private Long openings = 0L;

    @ExcelIgnore
    private String openingsIds;

    @ExcelProperty(value = "Sum of Submitted to Job", index = 2)
    private Long submitToJobNum = 0L;

    @ExcelProperty(value = "Sum of Submitted to Client", index = 3)
    private Long submitToClientNum = 0L;

    @ExcelProperty(value = "Sum of Interview", index = 4)
    private Long interviewNum = 0L;

    @ExcelProperty(value = "Sum of Offered", index = 5)
    private Long offerNum = 0L;

    @ExcelProperty(value = "Sum of Offer Accepted", index = 6)
    private Long offerAcceptedNum = 0L;

    @ExcelProperty(value = "Sum of On boarded", index = 7)
    private Long onBoardedNum = 0L;

    @ExcelIgnore
    private String activityIds;

    @ExcelIgnore
    private String type;

}
