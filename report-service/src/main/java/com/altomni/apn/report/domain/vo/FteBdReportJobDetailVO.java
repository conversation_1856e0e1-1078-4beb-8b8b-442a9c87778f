package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.Instant;

@Data
@Entity
public class FteBdReportJobDetailVO {

    @Id
    @ExcelIgnore
    @Column(name = "id", nullable = false)
    private Long id;

    private String jobTitle;

    @Convert(converter = JobStatusConverter.class)
    private JobStatus jobStatus;

    private Long countToJob;

    private Long countToClient;

    private Instant lastActivityDate;

    private Instant createdDate;
}
