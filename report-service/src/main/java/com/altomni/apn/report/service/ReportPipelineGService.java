package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.ReportG2ApplicationVo;
import com.altomni.apn.report.domain.vo.ReportG2StatusApplicationVo;
import com.altomni.apn.report.dto.ReportPipelineParamDto;

import java.util.concurrent.ExecutionException;

public interface ReportPipelineGService {

    ReportG2ApplicationVo g2PipelineAnalyticsBySubmitToAM(ReportPipelineParamDto reportParam) throws ExecutionException, InterruptedException;

    ReportG2StatusApplicationVo g4PipelineAnalyticsByUsers(ReportPipelineParamDto reportParam);

}
