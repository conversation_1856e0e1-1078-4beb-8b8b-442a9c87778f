package com.altomni.apn.report.service.impl;

import com.altomni.apn.report.domain.vo.ExpenseReportVO;
import com.altomni.apn.report.dto.ExpenseReportDTO;
import com.altomni.apn.report.repository.ExpenseReportRepository;
import com.altomni.apn.report.service.ExpenseReportService;
import com.altomni.apn.report.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Slf4j
@Service("expenseReportService")
public class ExpenseReportServiceImpl implements ExpenseReportService {

    @Resource
    private ExpenseReportRepository expenseReportRepository;

    /**
     * 统计 timesheet expense 每天,每种类型的报表记录
     * @param reportDTO
     * @param pageable
     * @return
     * @throws ExecutionException
     * @throws InterruptedException
     */
    @Override
    public Page<ExpenseReportVO> getExpenseReport(ExpenseReportDTO reportDTO, Pageable pageable) throws ExecutionException, InterruptedException {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return expenseReportRepository.getExpenseReportCount(reportDTO);
        });
        CompletableFuture<List<ExpenseReportVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return expenseReportRepository.getExpenseReportDate(reportDTO, pageable);
        });
        return new PageImpl<>(dataFuture.get(), Pageable.unpaged(), countFuture.get());
    }

    @Override
    public void exportExpenseReport(ExpenseReportDTO exportExpenseReport, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("exportContractorHires");
        stopWatch.start("1 search ExpenseReport data");
        List<ExpenseReportVO> voList = expenseReportRepository.getExpenseReportDate(exportExpenseReport, null);
        stopWatch.stop();
        stopWatch.start("2 downloadExcel task");
        ExcelUtil.downloadExcelCustom(response, ExpenseReportVO.class, voList, "", "ExpenseReport.xlsx", true);
        stopWatch.stop();
        log.info(" exportContractorHires time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

}
