package com.altomni.apn.report.domain.vo.s3;

import com.altomni.apn.report.dto.s3.UserCandidateCountDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CandidateUserCountVo implements Serializable {

    private static final long serialVersionUID = 6942328753680137864L;

    private List<UserCandidateCountDTO> userCandidateCountList;

    private Integer userCount;

}
