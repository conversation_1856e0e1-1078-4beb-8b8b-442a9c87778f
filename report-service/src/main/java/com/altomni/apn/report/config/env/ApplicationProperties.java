package com.altomni.apn.report.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.sync.threadNum}")
    private Integer threadNum;

    @Value("${application.elasticrecord.only-read-ip-port}")
    private String ipAndPort;

    @Value("${application.crmUrl}")
    private String crmUrl;

    //send lark msg for schedule e5 report
    @Value("${application.larkService.callbackUrl}")
    private String larkCallbackUrl;

    @Value("${application.larkService.authenticationEndPoint}")
    private String larkAuthenticationEndpoint;

    @Value("${application.larkService.sendMessageEndpoint}")
    private String larkSendMessageEndpoint;

    @Value("${application.larkService.messageNotificationService.appId}")
    private String larkSendMessageServiceAppId;

    @Value("${application.larkService.messageNotificationService.appSecret}")
    private String larkSendMessageServiceAppSecret;

    @Value("${application.report.e5.url}")
    private String reportLinkUrl;
}
