package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseType;
import com.altomni.apn.common.domain.enumeration.jobdiva.ExpenseTypeConverter;
import com.altomni.apn.report.domain.enumeration.ExpenseTypeDateConverter;
import com.altomni.apn.report.domain.enumeration.LocalDateDataConverter;
import lombok.Data;

import javax.persistence.*;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@Data
@Entity
public class ExpenseReportVO {
    @Id
    @ExcelIgnore
    @Column(name = "id", nullable = false)
    private Long id;

    @ExcelProperty(value = "FIRSTNAME", index = 0)
    private String firstName;

    @ExcelProperty(value = "LASTNAME", index = 1)
    private String lastName;

    @ExcelProperty(value = "WEEKENDING", index = 2, converter = LocalDateDataConverter.class)
    private LocalDate weekEnd;

    @ExcelProperty(value = "DATE", index = 3, converter = LocalDateDataConverter.class)
    private LocalDate workDate;

    @Convert(converter = ExpenseTypeConverter.class)
    @ExcelProperty(converter = ExpenseTypeDateConverter.class, index = 4, value = "EXPENSE CATEGORY")
    private ExpenseType expenseType;

    @ExcelIgnore
    private Float cost;

    @Transient
    @ExcelProperty(value = "AMOUNT", index = 5)
    private String formatCost;

    @ExcelProperty(value = "APPROVER", index = 6)
    private String approver;

    @ExcelIgnore
    private Instant approvedOn;

    @Transient
    @ExcelProperty(value = "APPROVED ON", index = 7)
    private String formatApprovedOn;

    @ExcelProperty(value = "COMMENTS", index = 8)
    private String comments;

    @ExcelProperty(value = "COMPANY", index = 9)
    private String companyName;

    @ExcelProperty(value = "JOB #", index = 10)
    private String jobCode;

    @ExcelIgnore
    private String timezone;

    @ExcelIgnore
    private String symbol;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFormatCost() {
        return symbol + cost;
    }

    public String getFormatApprovedOn() {
        if (approvedOn != null) {
            return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").withZone(ZoneId.of(timezone)).format(approvedOn);
        }
        return null;
    }

}
