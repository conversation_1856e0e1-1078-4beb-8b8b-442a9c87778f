package com.altomni.apn.report.domain.vm.w1;

import com.altomni.apn.report.domain.enumeration.ReportCountType;
import com.altomni.apn.report.domain.enumeration.ReportCountTypeConverter;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
public class UserCountVM implements Serializable {

    @Id
    private Long userId;

    private int totalCnt;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public int getTotalCnt() {
        return totalCnt;
    }

    public void setTotalCnt(int totalCnt) {
        this.totalCnt = totalCnt;
    }

}
