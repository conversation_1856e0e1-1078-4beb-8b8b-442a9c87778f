package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.UserBriefVO;
import com.altomni.apn.report.domain.vo.p2.PipelineAnalyticsByCompanyVO;
import com.altomni.apn.report.dto.p1.PipelineAnalyticsDTO;
import com.altomni.apn.report.domain.vo.p1.PipelineAnalyticsVO;
import com.altomni.apn.report.dto.p2.ReportPipelineParamDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 *
 * <AUTHOR>
 */
public interface ReportPipelineService {

    List<PipelineAnalyticsVO> p1PipelineAnalyticsByUsers(PipelineAnalyticsDTO pipelineAnalyticsDTO);

    void exportP1PipelineAnalyticsByUsersByExcel(PipelineAnalyticsDTO pipelineAnalyticsDTO, HttpServletResponse response);

    List<PipelineAnalyticsByCompanyVO> p2PipelineAnalyticsByCompany(ReportPipelineParamDTO reportPipelineParamDTO);

    List<UserBriefVO> getP2UserFilter(ReportPipelineParamDTO reportPipelineParam);

    void exportP2PipelineAnalyticsByCompanyByExcel(ReportPipelineParamDTO reportPipelineParamDTO, HttpServletResponse response);

    boolean applyDataPermission(Set<Long> userIdSet, Set<Long> teamIdSet);
}
