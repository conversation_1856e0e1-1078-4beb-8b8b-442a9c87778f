package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.SeaTeamPerformanceDetailVo;
import com.altomni.apn.report.domain.vo.SeaTeamPerformanceVO;
import com.altomni.apn.report.dto.SeaTeamPerformanceSearchDto;
import com.altomni.apn.report.service.SeaTeamPerformanceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/v3/report")
@RequiredArgsConstructor
public class SeaTeamPerformanceReportResource {

    private final SeaTeamPerformanceService seaTeamPerformanceService;

    @PostMapping("/sea-team-performance")
    public ResponseEntity<List<SeaTeamPerformanceVO>> getSeaTeamPerformanceReport(@RequestBody SeaTeamPerformanceSearchDto seaTeamPerformanceSearchDto) {
        log.info("[apn @{}] getSeaTeamPerformanceReport by user", SecurityUtils.getUserId());
        List<SeaTeamPerformanceVO> result = seaTeamPerformanceService.searchSeaTeamPerformance(seaTeamPerformanceSearchDto);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/sea-team-performance/download")
    public void downloadSeaTeamPerformanceReport(@RequestBody SeaTeamPerformanceSearchDto seaTeamPerformanceSearchDto, HttpServletResponse response) {
        log.info("[apn @{}] downloadSeaTeamPerformanceReport by user", SecurityUtils.getUserId());
        seaTeamPerformanceService.downloadSeaTeamPerformanceReport(seaTeamPerformanceSearchDto, response);
    }


    @PostMapping("/sea-team-performance/detail")
    public ResponseEntity<List<SeaTeamPerformanceDetailVo>> getSeaTeamPerformanceDetail(@RequestBody SeaTeamPerformanceSearchDto seaTeamPerformanceSearchDto, @RequestParam(name = "type") String type) {
        log.info("[apn @{}] getSeaTeamPerformanceDetailReport by user", SecurityUtils.getUserId());
        List<SeaTeamPerformanceDetailVo> result = seaTeamPerformanceService.searchSeaTeamPerformanceDetail(seaTeamPerformanceSearchDto, type);
        return ResponseEntity.ok(result);
    }
}
