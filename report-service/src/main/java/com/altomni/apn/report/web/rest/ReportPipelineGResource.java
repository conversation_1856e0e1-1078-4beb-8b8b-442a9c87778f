package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportG2ApplicationVo;
import com.altomni.apn.report.domain.vo.ReportG2StatusApplicationVo;
import com.altomni.apn.report.dto.ReportPipelineParamDto;
import com.altomni.apn.report.service.ReportPipelineGService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ReportPipelineGResource {

    @Resource
    private ReportPipelineGService reportPipelineGService;

    /**
     * G2 Report
     * Pipeline Analytics by Submit to AM
     */
    @PostMapping("/report/g2-pipeline-analytics-by-submit-to-am")
    public ResponseEntity<ReportG2ApplicationVo> g2PipelineAnalyticsBySubmitToAM(@RequestBody ReportPipelineParamDto reportParam) throws ExecutionException, InterruptedException {
        log.info("[APN: ReportPipelineV2 @{}] REST request to get g2 pipeline analytics by submit to AM. --> reportParam:{}", SecurityUtils.getUserId(), reportParam);
        return ResponseEntity.ok(reportPipelineGService.g2PipelineAnalyticsBySubmitToAM(reportParam));
    }

    /**
     * G4 Report
     * Pipeline Analytics
     */
    @PostMapping("/report/g4-pipeline-analytics-by-users")
    public ResponseEntity<ReportG2StatusApplicationVo> g4PipelineAnalyticsByUsers(@RequestBody ReportPipelineParamDto reportParam) {
        log.info("[APN: ReportPipelineV2 @{}] REST request to get g4 pipeline analytics by users --> reportParam:{}", SecurityUtils.getUserId(), reportParam);
        return ResponseEntity.ok(reportPipelineGService.g4PipelineAnalyticsByUsers(reportParam));
    }


}
