package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class ReportCountTypeConverter extends AbstractAttributeConverter<ReportCountType, Integer> {
    public ReportCountTypeConverter() {
        super(ReportCountType::toDbValue, ReportCountType::fromDbValue);
    }

}
