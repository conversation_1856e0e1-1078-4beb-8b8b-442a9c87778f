package com.altomni.apn.report.domain.vm.s3;


import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.Instant;

@Data
@Entity
public class DormantApplicationVM implements Serializable {

    /**
     * application id
     */
    @Id
    private Long id;

    private Long talentId;

    private String fullName;

    private Long jobId;

    private String jobTitle;

    private JobType jobType;

    private JobStatus jobStatus;

    private Long companyId;

    private String company;

    private Long amId;

    private String amFirstName;

    private String amLastName;

    private Long recruiterId;

    private String recruiterFirstName;

    private String recruiterLastName;

    private Instant lastModifiedDate;
}
