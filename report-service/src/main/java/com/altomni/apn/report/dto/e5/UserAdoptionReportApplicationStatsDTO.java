package com.altomni.apn.report.dto.e5;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAdoptionReportApplicationStatsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userId;

    private Long teamId;

    private Long submitToJobCount = 0L;

    private Long interviewCount = 0L;

    private Long uniqueInterviewedTalentCount = 0L;

    private Long onboardTalentCount = 0L;

    private Long apnTalentNoteCount = 0L;

    private String uniqueTalentIds;
}
