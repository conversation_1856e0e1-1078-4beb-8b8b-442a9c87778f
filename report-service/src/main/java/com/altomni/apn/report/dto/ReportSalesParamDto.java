package com.altomni.apn.report.dto;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.auth.LoginInformation;
import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.enumeration.ReportSalesType;
import com.altomni.apn.report.domain.enumeration.ReportSalesTypeConverter;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;

import javax.persistence.Convert;
import java.io.Serializable;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

public class ReportSalesParamDto implements Serializable {

    private static final long serialVersionUID = 6994704573277709468L;

    private Integer country;

    private List<Long> companies;

    private List<Integer> years;

    private List<JobType> jobType;

    @Convert(converter = ReportSalesTypeConverter.class)
    private ReportSalesType type;

    private LocalDate from;

    private LocalDate to;

    private TeamDataPermissionRespDTO teamDataPermission;

    private List<Long> teamIdList = new ArrayList<>();

    private List<Long> userIdList = new ArrayList<>();

    private List<UserRole> userRoleList;

    public List<Long> getTeamIdList() {
        return teamIdList;
    }

    public void setTeamIdList(List<Long> teamIdList) {
        this.teamIdList = teamIdList;
    }

    public List<Long> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<Long> userIdList) {
        this.userIdList = userIdList;
    }

    public List<UserRole> getUserRoleList() {
        if (CollUtil.isEmpty(userIdList) && CollUtil.isEmpty(teamIdList)) {
            return null;
        }
        return userRoleList;
    }

    public void setUserRoleList(List<UserRole> userRoleList) {
        this.userRoleList = userRoleList;
    }

    public TeamDataPermissionRespDTO getTeamDataPermission() {
        return teamDataPermission;
    }

    public void setTeamDataPermission(TeamDataPermissionRespDTO teamDataPermission) {
        this.teamDataPermission = teamDataPermission;
    }

    public ReportSalesParamDto type(ReportSalesType type) {
        this.type = type;
        return this;
    }

    private static final List<Integer> CURRENT_YEAR = Collections.singletonList(Integer.valueOf(DateUtil.currentYear()));

    public ReportSalesType getType() {
        return type;
    }

    public void setType(ReportSalesType type) {
        this.type = type;
    }

    public Integer getCountry() {
        return country;
    }

    public ReportSalesParamDto country(Integer country) {
        this.country = country == null ? CurrencyConstants.USD : country;
        return this;
    }

    public void setCountry(Integer country) {
        this.country = country;
    }

    public List<Long> getCompanies() {
        return companies;
    }

    public ReportSalesParamDto companies(List<Long> companies) {
        this.companies = companies;
        return this;
    }

    public void setCompanies(List<Long> companies) {
        this.companies = companies;
    }

    public ReportSalesParamDto years(List<Integer> years) {
        this.years = CollectionUtils.isEmpty(years) ? CollUtil.newArrayList() : years;
        return this;
    }

    public List<Integer> getYears() {
        return years;
    }

    public void setYears(List<Integer> years) {
        this.years = years;
    }

    public List<JobType> getJobType() {
        if (CollUtil.isEmpty(jobType)) {
            jobType = CollUtil.newArrayList(JobType.FULL_TIME, JobType.CONTRACT, JobType.MSP, JobType.OTHERS);
        }
        return jobType;
    }

    public ReportSalesParamDto jobType(List<JobType> jobType) {
        this.jobType = jobType;
        return this;
    }

    public void setJobType(List<JobType> jobType) {
        this.jobType = jobType;
    }

    public LocalDate getTo() {
        return to;
    }

    public void setTo(LocalDate to) {
        this.to = to;
    }

    public LocalDate getFrom() {
        return from;
    }

    public void setFrom(LocalDate from) {
        this.from = from;
    }

    public Instant mondayFrom() {
        if (from == null) {
            return null;
        }
        LocalDate nearMonday = from.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 获取当前登录用户所在的时区
        return getDateStringByTimezone(nearMonday, true);
    }

    public Instant sundayTo() {
        if (to == null) {
            return null;
        }
        LocalDate nearSunday = to.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        // 获取当前登录用户所在的时区
        return getDateStringByTimezone(nearSunday, false);
    }

    private Instant getDateStringByTimezone(LocalDate nearSunday, boolean isZero) {
        Optional<String> timezoneOptional = SecurityUtils.getCurrentLoginInformation()
                .map(LoginInformation::getTimezone);
        if (timezoneOptional.isPresent()) {
            // 构建指定时区的周一起始时间
            ZonedDateTime zonedDateTime = ZonedDateTime.of(nearSunday, isZero? LocalTime.MIN: LocalTime.MAX, ZoneId.of(timezoneOptional.get()));
            // 将指定时区的时间转换为 UTC 时间
            ZonedDateTime utcDateTime = zonedDateTime.withZoneSameInstant(ZoneId.of("UTC"));
            return utcDateTime.toInstant();
        } else {
            return nearSunday.atStartOfDay().toInstant(ZoneOffset.UTC);
        }
    }

    /**
     * 时间范围验证，最多选择 26 周的数据
     */
    public void validateWeekScope() {
        if (from == null || to == null) {
            return;
        }
        LocalDate mondayFrom = from.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        LocalDate nearSunday = to.with(TemporalAdjusters.nextOrSame(DayOfWeek.SUNDAY));
        long weeksBetween = ChronoUnit.WEEKS.between(mondayFrom, nearSunday);
        if (Math.abs(weeksBetween) > 27) {
            throw new CustomParameterizedException("Invalid week scope, please select a date range within 26 weeks.");
        }
    }

    @Override
    public String toString() {
        return "ReportSalesParam{" +
            "country=" + country +
            ", companies=" + companies +
            ", years=" + years +
            ", jobType=" + jobType +
            '}';
    }
}
