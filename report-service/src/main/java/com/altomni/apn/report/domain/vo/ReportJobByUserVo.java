package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportJobByUserVo implements Serializable {

    @Id
    @ExcelIgnore
    private Long userId;

    @Transient
    @ExcelProperty(value = "AM", index = 0)
    private String userName;

    @ExcelIgnore
    private String uFirstName;

    @ExcelIgnore
    private String uLastName;

    @ExcelProperty(value = "Sum of Openings", index = 1)
    private Long openingCount;

    @ExcelProperty(value = "Sum of Submitted to Job", index = 2)
    private Long appliedCount;

    @ExcelProperty(value = "Sum of Submitted to Client", index = 3)
    private Long submittedCount;

    @ExcelProperty(value = "Sum of Interview", index = 4)
    private Long interviewCount;

    @ExcelProperty(value = "Sum of Offered", index = 5)
    private Long offeredCount;

    @ExcelProperty(value = "Sum of Offer Accepted", index = 6)
    private Long offerAcceptedCount;

    @ExcelProperty(value = "Sum of On boarded", index = 7)
    private Long startedCount;

    @ExcelIgnore
    private String jobIds;

    @ExcelIgnore
    private String activityIds;

    @ExcelIgnore
    private String type;

    @ExcelIgnore
    private String appliedIds;

    @ExcelIgnore
    private String submittedIds;

    @ExcelIgnore
    private String interviewIds;

    @ExcelIgnore
    private String offeredIds;

    @ExcelIgnore
    private String offerAcceptedIds;

    @ExcelIgnore
    private String startedIds;

    @ExcelIgnore
    private String jobOpenings;

}
