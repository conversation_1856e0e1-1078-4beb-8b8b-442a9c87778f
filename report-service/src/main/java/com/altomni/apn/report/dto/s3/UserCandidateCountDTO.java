package com.altomni.apn.report.dto.s3;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.altomni.apn.common.utils.CommonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
@ContentRowHeight(15)
@HeadRowHeight(20)
@ColumnWidth(25)
public class UserCandidateCountDTO implements Serializable {
    private static final long serialVersionUID = 5177800529479974044L;

    @Id
    @ExcelIgnore
    private Long id;

    @JsonIgnore
    @ExcelIgnore
    private String firstName;

    @JsonIgnore
    @ExcelIgnore
    private String lastName;

    @Transient
    @ExcelProperty(value = "User Name", index = 0)
    private String fullName;

    @ExcelProperty(value = "Created Candidates", index = 1)
    private Long createdCount = 0L;

    @Transient
    @ExcelProperty(value = "Modify Candidate", index = 2)
    private Long modifyCount = 0L;

    @Transient
    @ExcelProperty(value = "Called Candidate", index = 3)
    private Long calledCount = 0L;

    @Transient
    @ExcelProperty(value = "Emailed Candidate", index = 4)
    private Long emailedCount = 0L;

    @Transient
    @ExcelProperty(value = "Consultant Interview", index = 5)
    private Long interviewCount = 0L;

    @Transient
    @ExcelProperty(value = "Candidate Video", index = 6)
    private Long videoCount = 0L;

    @Transient
    @ExcelProperty(value = "Initial Candidate Interview", index = 7)
    private Long iciCount = 0L;

    public void constructFullName() {
        setFullName(CommonUtils.formatFullNameWithBlankCheck(firstName, lastName));
    }

    public UserCandidateCountDTO(Long id, String fullName) {
        this.id = id;
        this.fullName = fullName;
        this.createdCount = 0L;
        this.modifyCount = 0L;
        this.calledCount = 0L;
        this.emailedCount = 0L;
        this.interviewCount = 0L;
    }
}
