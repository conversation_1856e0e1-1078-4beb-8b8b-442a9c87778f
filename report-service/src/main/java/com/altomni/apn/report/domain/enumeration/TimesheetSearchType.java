package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum TimesheetSearchType implements ConvertedEnum<Integer> {

    WEEK_ENDING_DATE(0),
    HOURS_WORKED(1),
    APPROVED_DATE(2);

    private final int dbValue;

    TimesheetSearchType(int dbValue) {
        this.dbValue = dbValue;
    }
    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<TimesheetSearchType, Integer> resolver =
            new ReverseEnumResolver<>(TimesheetSearchType.class, TimesheetSearchType::toDbValue);

    public static TimesheetSearchType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
