package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.enumeration.enums.ReportAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.report.config.env.ApplicationProperties;
import com.altomni.apn.report.domain.ReportUserJobTalentDTO;
import com.altomni.apn.report.domain.vm.s1.JobCompanyVM;
import com.altomni.apn.report.domain.vm.s1.TalentInfoVM;
import com.altomni.apn.report.domain.vo.s1.ResignUserReportJobVO;
import com.altomni.apn.report.domain.vo.s1.ResignUserReportTalentVO;
import com.altomni.apn.report.dto.ReportJobParam;
import com.altomni.apn.report.repository.ReportCurrentJobTalentRepository;
import com.altomni.apn.report.repository.ReportUserJobTalentRepository;
import com.altomni.apn.report.service.ReportUserJobTalentService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class ReportUserJobTalentServiceImpl extends ReportBaseServiceImpl implements ReportUserJobTalentService {

    private final Logger log = LoggerFactory.getLogger(ReportUserJobTalentServiceImpl.class);

    private final ReportUserJobTalentRepository reportUserJobTalentRepository;

    private final ReportCurrentJobTalentRepository reportCurrentJobTalentRepository;

    private final Integer QUERY_INTERVAL_LENGTH = 900;

    private final Integer QUERY_PARAMS_FIRST = 1;

    @Resource
    private ApplicationProperties applicationProperties;

    private volatile ExecutorService executorTalentService;

    public ReportUserJobTalentServiceImpl(ReportUserJobTalentRepository reportUserJobTalentRepository,
                                          ReportCurrentJobTalentRepository reportCurrentJobTalentRepository) {
        this.reportUserJobTalentRepository = reportUserJobTalentRepository;
        this.reportCurrentJobTalentRepository = reportCurrentJobTalentRepository;
    }

    /**
     * get AM report source data
     * @param reportParam request param object
     * @return list of ReportJob object
     */
    @Override
    public List<ReportUserJobTalentDTO> getUserResignData(ReportJobParam reportParam) {
        log.info("[APN: ReportUserJobTalent @{}] ========== login user only ", SecurityUtils.getUserId());
        return reportCurrentJobTalentRepository.findByCurrentUser();
    }

    @Override
    public List<ResignUserReportJobVO> findAllJobByIds(List<Long> jobIdList) {
        if (CollectionUtils.isEmpty(jobIdList)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTUSERJOB_FINDALLJOBBYIDS_JOBIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }

        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> paramMap = new HashMap<>(16);
        createQueryJobAndCompanyInfo(sb, paramMap, jobIdList);
        List<JobCompanyVM> jobCompanyVMList = searchEntityList(paramMap, sb.toString(), JobCompanyVM.class);
        Set<Long> privateJobTeamIds = reportRepository.findPrivateJobTeamIds(SecurityUtils.getTenantId());
        return jobCompanyVMList.stream().map(j -> this.toResignUserReportJobVO(j, privateJobTeamIds)).sorted(Comparator.comparing(ResignUserReportJobVO::getId)).collect(Collectors.toList());
    }

    @Override
    @ProcessConfidentialTalent
    public List<ResignUserReportTalentVO> findAllTalentByIds(List<Long> talentIds) {

        List<Long> talentIdList = talentIds.stream().distinct().sorted().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(talentIdList)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTUSERJOB_FINDALLJOBBYIDS_TALENTIDSNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }

        List<ResignUserReportTalentVO> resignUserReportTalentVOList = new CopyOnWriteArrayList<>();
        StringBuffer sb = new StringBuffer();
        createQueryTalentInfo(sb);

        List<List<Long>> groupTalentIdList = new ArrayList<>();
        for (int i = 0 ; i < talentIdList.size() ; i += QUERY_INTERVAL_LENGTH) {
            List<Long> itemIdList = talentIdList.subList(i, Math.min((i + QUERY_INTERVAL_LENGTH), talentIdList.size()));
            groupTalentIdList.add(itemIdList);
        }

        CountDownLatch countDownLatch = new CountDownLatch(groupTalentIdList.size());
        groupTalentIdList.forEach(itemIdList -> getQueryTalentExecutorService().execute(() -> {
            try {
                Map<Integer, Object> paramMap = new ConcurrentHashMap<>(16);
                paramMap.put(QUERY_PARAMS_FIRST, itemIdList);
                List<TalentInfoVM> talentInfoVMList = searchEntityList(paramMap, sb.toString(), TalentInfoVM.class);
                CollectionUtil.addAll(resignUserReportTalentVOList, talentInfoVMList.stream().map(ResignUserReportTalentVO::fromTalentInfoVM).collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("[APN: ReportUserJobTalent] search talents by ids failed = [{}]", ExceptionUtils.getStackTrace(e));
            } finally {
                countDownLatch.countDown();
                log.info("[APN: ReportUserJobTalent] search search talents by ids succeeded, type = {}", itemIdList);
            }
        }));

        try {
            countDownLatch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("[APN: ReportUserJobTalent] companyCountDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
        }

        return resignUserReportTalentVOList.stream().sorted(Comparator.comparing(ResignUserReportTalentVO::getId)).collect(Collectors.toList());
    }

    private ResignUserReportJobVO toResignUserReportJobVO(JobCompanyVM jobCompanyVM, Set<Long> privateJobTeamIds) {
        ResignUserReportJobVO resignUserReportJobDTO = new ResignUserReportJobVO();
        ServiceUtils.myCopyProperties(jobCompanyVM, resignUserReportJobDTO);
        resignUserReportJobDTO.setPrivateJob(privateJobTeamIds.contains(jobCompanyVM.getJobPteamId()));
        return resignUserReportJobDTO;
    }

    private void createQueryJobAndCompanyInfo(StringBuilder sb, Map<Integer, Object> paramMap, List<Long> idList) {
        sb.append("""
                SELECT
                	j.id,
                	j.title,
                	j.tenant_id,
                	rp.job_type,
                	j.STATUS,
                	j.created_by,
                	j.created_date,
                	j.last_modified_by,
                	j.last_modified_date,
                	j.posting_time,
                	j.pteam_id as job_pteam_id,
                	c.id company_id,
                	c.full_business_name company 
                FROM
                	job j
                	INNER JOIN company c ON c.id = j.company_id 
                   inner join recruitment_process rp on rp.id = j.recruitment_process_id 
                WHERE
                	j.id IN ?1
                	AND j.tenant_id = ?2
                	AND j.STATUS IN (0, 1, 2, 5)
                """);

        paramMap.put(1, idList);
        paramMap.put(2, SecurityUtils.getTenantId());
    }

    private void createQueryTalentInfo(StringBuffer sb) {
        sb.append("""
                SELECT
                	t.id,
                    t.tenant_id,
                	t.first_name,
                	t.last_name,
                	t.created_by,
                	t.created_date,
                	t.last_modified_by,
                	t.last_modified_date,
                	info.extended_info ->> '$.experiences[0].title' title, 
                	info.extended_info ->> '$.experiences[0].companyName' company 
                FROM
                	talent t
                	LEFT JOIN talent_additional_info info ON info.id = t.additional_info_id 
                WHERE
                	t.tenant_id = 
                """).append(SecurityUtils.getTenantId()).append("	AND t.id IN ?1 ");
    }

    private <T> List<T> searchEntityList(Map<Integer, Object> paramMap, String selectSql, Class<T> clazz) {
        List<T> entityList = searchData(selectSql, clazz, paramMap);
        if (CollectionUtils.isEmpty(entityList)) {
            return new CopyOnWriteArrayList<>();
        }
        return entityList;
    }

    private ExecutorService getQueryTalentExecutorService() {
        if (executorTalentService == null) {
            synchronized (ReportUserJobTalentServiceImpl.class) {
                if (executorTalentService == null) {
                    executorTalentService = new ThreadPoolExecutor(
                            applicationProperties.getThreadNum(),
                            applicationProperties.getThreadNum() * 2,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-report-query-talent-by-ids", false));
                }
            }
        }
        return executorTalentService;
    }
}
