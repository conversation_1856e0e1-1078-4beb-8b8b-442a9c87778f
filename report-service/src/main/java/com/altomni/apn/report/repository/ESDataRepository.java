package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Collection;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z;
import static com.altomni.apn.report.domain.constants.ReportSortFieldConstants.*;

@Repository
public class ESDataRepository {

    private final Logger log = LoggerFactory.getLogger(ESDataRepository.class);

    @Resource
    RestHighLevelClient restHighLevelClient;

    private static Set<Long> existIndexTenantIdSet = new HashSet<>();
    private final static String TALENT_INDEX = "activity_talent";


    private final static int SIZE = 10000;

    private String talentRecordIndex(Long tenantId) {
        return TALENT_INDEX + "_" + tenantId;
    }

    public boolean indexExists(Long tenantId) {
        if (existIndexTenantIdSet.contains(tenantId)) {
            return true;
        }
        String index = talentRecordIndex(tenantId);
        GetIndexRequest request = new GetIndexRequest(index);
        boolean exists;
        try {
            exists = restHighLevelClient.indices().exists(request, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("APN:indexExists Exception {}", e.getMessage());
            return false;
        }
        if (exists) {
            existIndexTenantIdSet.add(tenantId);
        }
        return exists;
    }


    public SearchResponse countUserCandidate(Long tenantId, String fromDate, String toDate, Collection<Long> uidList) throws IOException {
        //判断当前租户是否存在该ES_index
        if (!indexExists(tenantId)) {
            return null;
        }
        // 聚合查询
        return aggregationQueryForCountUserCandidate(talentRecordIndex(tenantId), fromDate, toDate, uidList);
    }


    public SearchResponse aggregationQueryForCountUserCandidate(String indexName, String fromDate, String toDate, Collection<Long> createdByList) throws IOException {
        SearchRequest request = new SearchRequest(indexName);
        BoolQueryBuilder query = QueryBuilders.boolQuery();

        // 创建日期范围查询
        RangeQueryBuilder dateRangeQuery = QueryBuilders.rangeQuery("createdDate")
                .from(fromDate)
                .to(toDate)
                .format(YYYY_MM_DD_T_HH_MM_SS_Z); // 按照日期格式指定日期范围
        query.must(dateRangeQuery);

        // 创建 createdBy 列表查询
        if (CollUtil.isNotEmpty(createdByList)) {
            TermsQueryBuilder createdByQuery = QueryBuilders.termsQuery("createdBy", createdByList);
            query.must(createdByQuery);
        } else {
            query.must(new RegexpQueryBuilder("createdBy", "\\d+"));
        }
        // 设置 nested 查询条件
//、


        NestedQueryBuilder modifyAggregation = buildModifyNestedQueryBuilder();
        // 创建聚合
        // 添加 Aggregation
        TermsAggregationBuilder createdByAggregation = AggregationBuilders.terms("createdByAggregation")
                .field("createdBy")
                .size(SIZE)
                .subAggregation(AggregationBuilders.filter(MODIFY_AGGREGATION, QueryBuilders.boolQuery().mustNot(modifyAggregation)));
//                .subAggregation(AggregationBuilders.filter(CALL_AGGREGATION, callAggregation))
//                .subAggregation(AggregationBuilders.filter(EMAIL_AGGREGATION, emailAggregation))
//                .subAggregation(AggregationBuilders.filter(INTERVIEW_AGGREGATION, interviewAggregation))
//                .subAggregation(AggregationBuilders.filter(VIDEO_AGGREGATION,videoAggregation));

        // 将查询添加到Request
        request.source().query(query).aggregation(createdByAggregation).size(0);

        return restHighLevelClient.search(request, RequestOptions.DEFAULT);
    }

    public SearchResponse findModificationBySearch(Long tenantId, String fromDate, String toDate, Long detailUserId, Pageable pageable) {
        // 构建 Bool 查询 根据 fromDate,toDate,detailUserId 筛选
        BoolQueryBuilder boolQuery = buildCreatedByAndCreatedDateBoolQueryBuilder(fromDate, toDate, detailUserId);
        // 构建 modify的过滤条件
        boolQuery.filter(QueryBuilders.boolQuery().mustNot(buildModifyNestedQueryBuilder()));
        // 构建根据修改时间排序条件
        SearchSourceBuilder sourceBuilder = buildSortByModifiedDateSourceBuilder(pageable);
        // 将 Bool 查询添加到 SearchSourceBuilder
        sourceBuilder.query(boolQuery);
        // 组装SearchRequest
        SearchRequest request = new SearchRequest(talentRecordIndex(tenantId));
        request.source(sourceBuilder);
        // 执行查询
        return search(request);
    }

    public SearchResponse findChangeRecordByNoteTypeSearch(Long tenantId, String fromDate, String toDate, Long detailUserId, Pageable pageable, TalentNoteType noteType) {
        // 构建 Bool 查询 根据 fromDate,toDate,detailUserId 筛选
        BoolQueryBuilder boolQuery = buildCreatedByAndCreatedDateBoolQueryBuilder(fromDate, toDate, detailUserId);
        // 构建 NoteType 筛选
        boolQuery.must(buildNoteTypeNestedQueryBuilder(noteType));
        // 构建根据修改时间排序条件
        SearchSourceBuilder sourceBuilder = buildSortByModifiedDateSourceBuilder(pageable);
        // 将 Bool 查询添加到 SearchSourceBuilder
        sourceBuilder.query(boolQuery);
        // 组装SearchRequest
        SearchRequest request = new SearchRequest(talentRecordIndex(tenantId));
        request.source(sourceBuilder);
        // 执行查询
        return search(request);
    }

    private SearchResponse search(SearchRequest request) {
        try {
            return restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("[APN: ESDataRepository] search talent record change IOException.");
        }
        return null;
    }

    private NestedQueryBuilder buildNoteTypeNestedQueryBuilder(TalentNoteType noteType) {
        return QueryBuilders.nestedQuery(
                "changeFields",
                QueryBuilders.boolQuery()
                        .must(QueryBuilders.termQuery("changeFields.key", "noteType"))
                        .must(QueryBuilders.multiMatchQuery(noteType.toString(), "changeFields.changedTo", "changeFields.changedTo.keyword")),
                ScoreMode.None
        );

    }

    private NestedQueryBuilder buildModifyNestedQueryBuilder() {
        return QueryBuilders.nestedQuery(
                "changeFields",
                        QueryBuilders.boolQuery()
//                                .should(new MultiMatchQueryBuilder(TalentNoteType.CALL_CANDIDATES + StrUtil.SPACE + TalentNoteType.EMAIL_CANDIDATES + StrUtil.SPACE + TalentNoteType.CONSULTANT_INTERVIEW + StrUtil.SPACE + TalentNoteType.CALL_VIDEO)
//                                        .field("changeFields.changedTo")
//                                        .field("changeFields.changedTo.keyword"))
                                .should(QueryBuilders.boolQuery()
                                        .must(new TermQueryBuilder("changeFields.key", "talent"))
                                        .must(new TermQueryBuilder("changeFields.eventType", "insert"))
                                ),
                ScoreMode.None
        );

    }

    private BoolQueryBuilder buildCreatedByAndCreatedDateBoolQueryBuilder(String fromDate, String toDate, Long detailUserId) {
        // 构建Bool查询
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();

        // 添加term过滤器
        boolQuery.filter(QueryBuilders.termQuery(CREATED_BY, detailUserId));
        // 添加range过滤器
        boolQuery.filter(QueryBuilders.rangeQuery(CREATED_DATE)
                .from(fromDate)
                .to(toDate)
                .format(YYYY_MM_DD_T_HH_MM_SS_Z));
        return boolQuery;
    }

    private SearchSourceBuilder buildSortByModifiedDateSourceBuilder(Pageable pageable) {
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();
        sourceBuilder.size(SIZE);
        Optional<Sort.Order> order = pageable.getSort().stream().findFirst();
        if (order.isPresent()) {
            Sort.Order sort = order.get();
            if (sort.getProperty().equals(SORT_LAST_MODIFIED_DATE)) {
                sourceBuilder.sort(new FieldSortBuilder(CREATED_DATE).order(sort.isAscending() ? SortOrder.ASC : SortOrder.DESC));
            } else if (!sort.getProperty().equals(SORT_CANDIDATE_NAME)){
                sourceBuilder.sort(new FieldSortBuilder(sort.getProperty()).order(sort.isAscending() ? SortOrder.ASC : SortOrder.DESC));
            }
        }
        return sourceBuilder;
    }
}

