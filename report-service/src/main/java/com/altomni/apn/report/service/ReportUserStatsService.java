package com.altomni.apn.report.service;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.report.dto.LinkedinStatsDTO;

import java.util.List;


public interface ReportUserStatsService {

    List<LinkedinStatsDTO> getUserLinkedinStats(String fromDate, String toDate, ContactType type, String timeZone);

    List<Object> getAllUserLinkedinStats(String fromDate, String toDate, ContactType type, String timeZone);

    List<LinkedinStatsDTO> getESStats(String fromDate, String toDate, String timeZone, Long tenantId);

    List<Object> getAllESStats(String fromDate, String toDate, String timeZone, Long tenantId);
}
