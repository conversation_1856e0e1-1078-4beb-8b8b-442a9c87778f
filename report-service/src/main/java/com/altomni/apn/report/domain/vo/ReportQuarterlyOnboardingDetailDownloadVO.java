package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ReportQuarterlyOnboardingDetailDownloadVO {

    @ExcelIgnore
    private String id;

    private String employeeType;

    private Integer number;

    private String recruiter;

    private String clientAccount;

    private String candidateName;

    private String startDay;

    private String weekDay;

    private String hourlyGp;

    private String totalGp;

    private String totalRevenue;

    private String originalBillRate;

    private String originalBillRateCurrency;

    private String originalPayRate;

    private String originalPayRateCurrency;

    private String billRate;

    private String billRateCurrency;

    private String payRate;

    private String payRateCurrency;

    private String notes;

    public void maskConfidentialTalentData() {
       String mask = "***";
       this.employeeType = mask;
       this.recruiter = mask;
       this.clientAccount = mask;
       this.candidateName = mask;
       this.startDay = mask;
       this.weekDay = mask;
       this.hourlyGp = mask;
       this.totalGp = mask;
       this.totalRevenue = mask;
       this.originalBillRate = mask;
       this.originalBillRateCurrency = mask;
       this.originalPayRate = mask;
       this.originalPayRateCurrency = mask;
       this.billRate = mask;
       this.billRateCurrency = mask;
       this.payRate = mask;
       this.payRateCurrency = mask;
       this.notes = mask;
    }
}
