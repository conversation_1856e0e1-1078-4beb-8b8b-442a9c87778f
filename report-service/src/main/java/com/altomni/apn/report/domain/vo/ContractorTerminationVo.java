package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.domain.enumeration.StartTerminationReason;
import com.altomni.apn.common.domain.enumeration.StartTerminationReasonConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivisionConverter;
import com.altomni.apn.report.domain.enumeration.AssignmentDivisionDataConverter;
import com.altomni.apn.report.domain.enumeration.LocalDateDataConverter;
import com.altomni.apn.report.domain.enumeration.StartTerminationReasonDataConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ContractorTerminationVo {

    @Id
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "Candidate Full Name", index = 0)
    private String fullName;

    @ExcelProperty(value = "Email", index = 1)
    private String email;

    @ExcelProperty(value = "Assignment ID", index = 2)
    private Long assignmentId;

    @ExcelProperty(value = "Job ID", index = 3)
    private Long jobId;

    @ExcelProperty(value = "Job Code", index = 4)
    private String jobCode;

    @ExcelProperty(value = "Job Title", index = 5)
    private String jobTitle;

    @ExcelProperty(value = "Assignment Division", index = 6, converter = AssignmentDivisionDataConverter.class)
    @Convert(converter = AssignmentDivisionConverter.class)
    private AssignmentDivision assignmentDivision;

    @ExcelProperty(value = "Company", index = 7)
    private String company;

    @ExcelProperty(value = "Hiring Manager", index = 8)
    private String hiringManager;

    @ExcelProperty(value = "AM", index = 11)
    private String am;

    @ExcelProperty(value = "Address", index = 12)
    private String address;

    @ExcelProperty(value = "City", index = 13)
    private String city;

    @ExcelProperty(value = "State", index = 14)
    private String state;

    @ExcelProperty(value = "Country", index = 15)
    private String country;

    @ExcelProperty(value = "Start Date", index = 16, converter = LocalDateDataConverter.class)
    private LocalDate startDate;

    @ExcelProperty(value = "Est. End Date", index = 17, converter = LocalDateDataConverter.class)
    private LocalDate endDate;

    @ExcelProperty(value = "Recuiter", index = 9)
    private String recruiter;

    @ExcelProperty(value = "Recruiter Team", index = 10)
    private String recruiterTeam;

    @ExcelIgnore
    private BigDecimal billRate;

    @ExcelIgnore
    private BigDecimal payRate;

    @ExcelIgnore
    @Transient
    private String billCurrency;

    @ExcelIgnore
    @Transient
    private String payCurrency;

    @ExcelIgnore
    private String currency;

    @Transient
    @ExcelProperty(value = "Bill Rate", index = 18)
    private String billRateFormat;

    @Transient
    @ExcelProperty(value = "Pay Rate", index = 20)
    private String payRateFormat;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType billRateUnit;

    @Transient
    @ExcelProperty(value = "Bill Rate Unit", index = 19)
    private String billRateUnitFormat;

    @Transient
    @ExcelProperty(value = "Pay Rate Unit", index = 21)
    private String payRateUnitFormat;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType payRateUnit;

    @ExcelProperty(value = "Termination Date", index = 22, converter = LocalDateDataConverter.class)
    private LocalDate terminationDate;

    @ExcelProperty(value = "Terminated By", index = 23)
    private String terminatedBy;

    @ExcelProperty(value = "Termination Reason", index = 24, converter = StartTerminationReasonDataConverter.class)
    @Convert(converter = StartTerminationReasonConverter.class)
    private StartTerminationReason terminationReason;

    @ExcelProperty(value = "Note", index = 25)
    private String note;

    public String getBillCurrency() {
        return currency;
    }

    public String getPayCurrency() {
        return currency;
    }

    public String getBillRateUnitFormat() {
        return getBillCurrency() + "/" + billRateUnit.name().substring(0,1).toLowerCase();
    }

    public String getPayRateUnitFormat() {
        return getPayCurrency() + "/" + payRateUnit.name().substring(0,1).toLowerCase();
    }

    public String getBillRateFormat() {
        if (billRate != null) {
            return billRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getPayRateFormat() {
        if (payRate != null) {
            return payRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

}
