package com.altomni.apn.report.dto.s3;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.activity.ActivityChangeDTO;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.beans.Transient;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserCandidateModificationDetailDTO extends UserCandidateBaseDetailDTO  implements Serializable, AttachConfidentialTalent {

    private static final long serialVersionUID = -6074865844380714141L;

    private Boolean confidentialTalentViewAble;

    private ConfidentialInfoDto confidentialInfo;

    private List<ActivityChangeDTO> changeFields;

    @Override
    public void encrypt() {
       this.changeFields = null;
       this.setName(null);
       this.setCreatedBy(null);
       this.setCreatedDate(null);
       this.setLastModifiedDate(null);
    }
}
