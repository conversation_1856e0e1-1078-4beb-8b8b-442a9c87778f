package com.altomni.apn.report.service.e5;

import com.altomni.apn.report.domain.vo.e5.UserAdoptionReportWithThresholdVO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportDTO;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.ExecutionException;

public interface UserAdoptionReportScheduledService {

    void sendUserAdoptionReportToTeamLeader();

}
