package com.altomni.apn.report.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
public class BDReportKpiUserCompanyInfoVO implements Serializable {

    @Id
    private String id;

    private Long userId;

    private Long companyId;

    private Long companyCount;

    private String groupByDate;
}
