package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.FteBdReportJobDetailVO;
import com.altomni.apn.report.domain.vo.FteBdReportOnboardDetailVO;
import com.altomni.apn.report.domain.vo.FteBdReportVO;
import com.altomni.apn.report.dto.FteBdReportDTO;
import com.altomni.apn.report.dto.FteBdReportDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigInteger;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class ReportCompanyRepository {

    @PersistenceContext
    EntityManager entityManager;


    public List<FteBdReportVO> getFteBdReportList(FteBdReportDTO dto, Pageable pageable, String permission) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT
                	c.id,
                	c.full_business_name AS client_name ,
                	GROUP_CONCAT( DISTINCT CASE WHEN bfa.sales_lead_role = 0 THEN bfa.user_id ELSE NULL END ) AS am,
                	GROUP_CONCAT( DISTINCT CASE WHEN bfa.sales_lead_role = 3 THEN concat(bfa.user_id, '-', bfa.country) ELSE NULL END ) AS co_am,
                 	GROUP_CONCAT( DISTINCT CASE WHEN bfa.sales_lead_role = 1  THEN bfa.user_id ELSE NULL END ) AS sales_lead_owner,
                 	GROUP_CONCAT( DISTINCT CASE WHEN bfa.sales_lead_role = 2 THEN bfa.user_id ELSE NULL END ) AS bd_owner,
                    COALESCE(j.job_count,0) AS job_orders,
                    COALESCE(s.hired_count,0) AS onboard,
                    ROUND(COALESCE(i.total_due_amount,0),2) AS billing,
                    ROUND(COALESCE(i2.total_received_amount,0),2) AS cash_in  FROM
                    company c  
                        INNER JOIN business_flow_administrator bfa ON c.id = bfa.company_id  AND bfa.sales_lead_role in (0,1,2,3)
                       LEFT JOIN (
                       SELECT
                           COUNT( j1.id ) AS job_count,
                           j1.company_id 
                       FROM
                           job j1 
                       WHERE
                           j1.recruitment_process_id in ( SELECT id FROM recruitment_process rp WHERE rp.job_type = :jobType 
                """);
        // Job CreatedDataFilter
        addTenantId(queryBuilder, "rp");
        queryBuilder.append(")");
        addDateRangeFilter(queryBuilder, "j1.created_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append(permission);
        queryBuilder.append("""
                  GROUP BY
                		j1.company_id 
                	) j ON c.id = j.company_id
                	LEFT JOIN (
                	SELECT
                		s1.company_id,
                		COUNT( s1.talent_id ) AS hired_count 
                	FROM
                		`start` s1 
                	WHERE
                		s1.position_type = :jobType 
                """);
        // Start StartDateFilter
        addDateRangeFilter(queryBuilder, "s1.start_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());

        queryBuilder.append("""
                GROUP BY
                		s1.company_id 
                	) s ON s.company_id = c.id
                	LEFT JOIN (
                	SELECT
                        vil.company_id,
                        SUM( CASE WHEN  1=1   """);
        addSingleInvoiceDateRangeFilter(queryBuilder, "vil.invoice_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append("""  
                 THEN vil.due_amount / cr.from_usd_rate_mid ELSE 0 END ) AS total_due_amount,
                SUM( CASE WHEN  1=1 """);
        addSingleInvoiceDateRangeFilter(queryBuilder, "vil.payment_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append("""
                         THEN vil.received_amount / cr.from_usd_rate_mid ELSE 0 END ) AS total_received_amount,
                       vil.currency 
                   FROM
                       view_invoice_list vil
                       LEFT JOIN currency_rate_day cr ON vil.currency = cr.currency_id 
                       AND cr.rate_day = ( SELECT MAX( rate_day ) FROM currency_rate_day WHERE currency_id = vil.currency AND rate_day <= DATE( vil.invoice_date ) ) 
                WHERE vil.invoice_type = 0 AND vil.`STATUS` in (0,1,2,8) """);

        addInvoiceDateRangeFilter(queryBuilder, dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append("""
                  GROUP BY
                	vil.company_id 
                ) i ON i.company_id = c.id 
                LEFT JOIN (
                	SELECT
                		i_t.id,
                		i_t.company_id,
                		SUM( cash.cash_in )  AS total_received_amount
                	FROM
                		invoice i_t
                		LEFT JOIN (
                		SELECT
                			ipr.id,
                			ipr.invoice_id,
                			ipr.payment_date,
                			ipr.paid_amount / cr.from_usd_rate_mid AS cash_in,
                			ipr.currency  
                		FROM
                			invoice_payment_record ipr
                			LEFT JOIN currency_rate_day cr ON ipr.currency = cr.currency_id  
                			AND cr.rate_day = ( SELECT MAX( rate_day ) FROM currency_rate_day WHERE currency_id = ipr.currency AND rate_day <= DATE( ipr.payment_date ) )  
                		WHERE ipr.activated = 1  
                			""");

        addSingleInvoiceDateRangeFilter(queryBuilder, "ipr.payment_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append(""" 
                 		) cash ON cash.invoice_id = i_t.id  
                 		WHERE i_t.invoice_type = 0  
                 	GROUP BY
                 		i_t.company_id  
                 	) i2 ON i2.company_id = c.id  
                WHERE
                	1=1  
                """);
        // addTenantId
        addTenantId(queryBuilder, "c");
        // addCompanyCreatedDate
        addDateRangeFilter(queryBuilder, "c.created_date", dto.getClientCreationStartDate(), dto.getClientCreationEndDate());
        addClientIdsIn(queryBuilder, "c.id", dto.getClientIds());
        addUserIdsFilter(queryBuilder, "ambfa", "amucr", dto.getAmIds(), "0");
        addUserIdsFilter(queryBuilder, "slobfa", "sloucr", dto.getSalesLeadOwnerIds(), "1");
        addUserIdsFilter(queryBuilder, "bdobfa", "bdoucr", dto.getBdOwnerIds(), "2");
        addUserIdsFilter(queryBuilder, "coambfa", "coamucr", dto.getCoAmIds(), "3");
        queryBuilder.append(" GROUP BY c.id");
        queryBuilder.append(getOrderSql(dto.getSort(), " client_name "));
        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString(), FteBdReportVO.class)
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        nativeQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        nativeQuery.setMaxResults(pageable.getPageSize());
        return nativeQuery.getResultList();
    }

    private void addClientIdsIn(StringBuilder queryBuilder, String s, List<Long> clientIds) {
        if (CollUtil.isNotEmpty(clientIds)) {
            queryBuilder.append(" AND ").append(s).append(" IN (").append(StringUtils.join(clientIds, ",")).append(" ) ");
        }
    }

    public List<FteBdReportJobDetailVO> getFteBdReportJobDetail(FteBdReportDetailDTO dto, Pageable pageable, String permission) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT
                	j.title AS job_title,
                	j.id,
                	j.`status` AS job_status,
                	COALESCE ( sub_trpstj.count_trpstj, 0 ) AS count_to_job,
                	COALESCE ( sub_trpstc.count_trpstc, 0 ) AS count_to_client,
                	sub_trp.max_last_modified_date AS last_activity_date,
                	j.created_date 
                FROM
                	job j
                	LEFT JOIN (
                	SELECT
                		trp.job_id,
                		COUNT( trpstj.id ) AS count_trpstj 
                	FROM
                		talent_recruitment_process trp
                		LEFT JOIN talent_recruitment_process_submit_to_job trpstj ON trp.id = trpstj.talent_recruitment_process_id 
                	GROUP BY
                		trp.job_id 
                	) AS sub_trpstj ON j.id = sub_trpstj.job_id
                	LEFT JOIN (
                	SELECT
                		trp.job_id,
                		COUNT( trpstc.id ) AS count_trpstc 
                	FROM
                		talent_recruitment_process trp
                		LEFT JOIN talent_recruitment_process_submit_to_client trpstc ON trp.id = trpstc.talent_recruitment_process_id 
                	GROUP BY
                		trp.job_id 
                	) AS sub_trpstc ON j.id = sub_trpstc.job_id
                	LEFT JOIN ( SELECT job_id, MAX( last_modified_date ) AS max_last_modified_date FROM talent_recruitment_process GROUP BY job_id ) AS sub_trp ON j.id = sub_trp.job_id 
                WHERE
                	j.recruitment_process_id in ( SELECT id FROM recruitment_process rp WHERE rp.job_type = :jobType
                """);
        addTenantId(queryBuilder, "rp");
        queryBuilder.append(" ) AND j.company_id = ").append(dto.getClientId());
        queryBuilder.append(permission);

        addDateRangeFilter(queryBuilder, "j.created_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append(" GROUP BY j.id");
        queryBuilder.append(getOrderSql(dto.getSort(), " job_title "));

        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString(), FteBdReportJobDetailVO.class)
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        nativeQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        nativeQuery.setMaxResults(pageable.getPageSize());
        return nativeQuery.getResultList();
    }

    public List<FteBdReportOnboardDetailVO> getFteBdReportOnboardDetail(FteBdReportDetailDTO dto, Pageable pageable) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT
                    CONCAT(s.id,COALESCE(i.id,'') ) AS id,
                	t.id AS talent_id,
                	t.full_name AS candidate_name,
                	j.id AS job_id,
                	j.title AS job_title,
                	s.start_date AS onboarding_date,
                	GROUP_CONCAT( DISTINCT CASE WHEN trpku.user_role = 1  THEN trpku.user_id ELSE NULL END ) AS recruiter,
                	GROUP_CONCAT( DISTINCT CASE WHEN trpku.user_role = 0  THEN trpku.user_id ELSE NULL END ) AS am,
                	GROUP_CONCAT( DISTINCT CASE WHEN trpku.user_role = 7  THEN concat(trpku.user_id, '-', trpku.country) ELSE NULL END ) AS co_am,
                	i.invoice_date,
                	i.currency,
                	i.currency AS cash_in_currency,
                	ROUND(COALESCE(i.origin_billing,0),2) AS origin_billing,
                	ROUND(COALESCE(i.billing,0),2) AS billing,
                	ROUND(COALESCE(i.origin_cash_in,0),2) AS origin_cash_in,
                	ROUND(COALESCE(i.cash_in,0),2) AS cash_in,
                	ROUND(COALESCE(i.origin_balance,0),2) AS origin_balance,
                	ROUND(COALESCE(i.balance,0),2) AS balance,
                	i.due_date,
                	i.payment_status    
                FROM
                	`start` s
                	INNER JOIN talent t ON s.talent_id = t.id
                	INNER JOIN job j ON s.job_id = j.id
                	INNER JOIN talent_recruitment_process_kpi_user trpku ON s.talent_recruitment_process_id = trpku.talent_recruitment_process_id AND trpku.user_role in (0,1,7)
                	LEFT JOIN (
                	SELECT
                	    vil.id,
                		vil.start_id,
                		vil.invoice_date,
                		vil.currency,
                		vil.due_amount  AS origin_billing,
                		vil.due_amount / cr.from_usd_rate_mid  AS billing,
                		vil.received_amount  AS origin_cash_in,
                		vil.received_amount / cr.from_usd_rate_mid  AS cash_in,
                		vil.balance   AS origin_balance,
                		vil.balance / cr.from_usd_rate_mid   AS balance,
                		vil.due_date,
                		vil.`status` AS payment_status    
                	FROM
                		view_invoice_list vil
                		LEFT JOIN currency_rate_day cr ON vil.currency = cr.currency_id    
                		AND cr.rate_day = ( SELECT MAX( rate_day ) FROM currency_rate_day WHERE currency_id = vil.currency AND rate_day <= DATE( vil.invoice_date ) )    
                	WHERE
                		 vil.invoice_type = 0    
                		AND vil.`STATUS` IN ( 0, 1, 2, 8 )
                		 AND vil.start_id in (SELECT st.id FROM `start` st WHERE st.company_id = 
                		 """ + dto.getClientId() +
                """
                         )
                        	) i ON i.start_id = s.id    
                        	WHERE
                        	j.recruitment_process_id in ( SELECT id FROM recruitment_process rp WHERE rp.job_type = :jobType
                        """);
        addTenantId(queryBuilder, "rp");
        queryBuilder.append(" ) AND s.company_id = ").append(dto.getClientId());
        addDateRangeFilter(queryBuilder, "s.start_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append(" GROUP BY s.id,i.id ");
        queryBuilder.append(getOrderSql(dto.getSort(), " candidate_name "));

        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString(), FteBdReportOnboardDetailVO.class)
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        nativeQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        nativeQuery.setMaxResults(pageable.getPageSize());
        return nativeQuery.getResultList();
    }

    public List<FteBdReportOnboardDetailVO> getFteBdReportInvoiceDetail(FteBdReportDetailDTO dto, Pageable pageable) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT
                    CONCAT(s.id,COALESCE(i.id,'') ) AS id,
                	t.id AS talent_id,
                	t.full_name AS candidate_name,
                	j.id AS job_id,
                	j.title AS job_title,
                	s.start_date AS onboarding_date,
                	GROUP_CONCAT( DISTINCT CASE WHEN trpku.user_role = 1  THEN trpku.user_id ELSE NULL END ) AS recruiter,
                	GROUP_CONCAT( DISTINCT CASE WHEN trpku.user_role = 0  THEN trpku.user_id ELSE NULL END ) AS am,
                	GROUP_CONCAT( DISTINCT CASE WHEN trpku.user_role = 7  THEN concat(trpku.user_id, '-', trpku.country) ELSE NULL END ) AS co_am,
                	i.invoice_date,
                	i.currency,
                	i.currency AS cash_in_currency,
                	ROUND(COALESCE(i.origin_billing,0),2) AS origin_billing,
                	ROUND(COALESCE(i.billing,0),2) AS billing,
                	ROUND(COALESCE(i.origin_cash_in,0),2) AS origin_cash_in,
                	ROUND(COALESCE(i.cash_in,0),2) AS cash_in,
                	ROUND(COALESCE(i.origin_balance,0),2) AS origin_balance,
                	ROUND(COALESCE(i.balance,0),2) AS balance,
                	i.due_date,
                	i.payment_status    
                FROM
                	`start` s
                	INNER JOIN talent t ON s.talent_id = t.id
                	INNER JOIN job j ON s.job_id = j.id
                	LEFT JOIN talent_recruitment_process_kpi_user trpku ON s.talent_recruitment_process_id = trpku.talent_recruitment_process_id
                	INNER JOIN (
                	SELECT
                	    vil.id,
                		vil.start_id,
                		vil.invoice_date,
                		vil.currency,
                		vil.due_amount  AS origin_billing,
                		vil.due_amount / cr.from_usd_rate_mid  AS billing,
                		vil.received_amount  AS origin_cash_in,
                		vil.received_amount / cr.from_usd_rate_mid  AS cash_in,
                		vil.balance   AS origin_balance,
                		vil.balance / cr.from_usd_rate_mid   AS balance,
                		vil.due_date,
                		vil.`status` AS payment_status    
                	FROM
                		view_invoice_list vil
                		LEFT JOIN currency_rate_day cr ON vil.currency = cr.currency_id    
                		AND cr.rate_day = ( SELECT MAX( rate_day ) FROM currency_rate_day WHERE currency_id = vil.currency AND rate_day <= DATE( vil.invoice_date ) )    
                	WHERE
                		 vil.invoice_type = 0    
                		AND vil.`STATUS` IN ( 0, 1, 2, 8 )   
                				 AND vil.start_id in (SELECT st.id FROM `start` st WHERE st.company_id = 
                				 """ + dto.getClientId() + ")");
        addSingleInvoiceDateRangeFilter(queryBuilder, "vil.invoice_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());

        queryBuilder.append(""" 
                	) i ON i.start_id = s.id    
                	WHERE
                	j.recruitment_process_id in ( SELECT id FROM recruitment_process rp WHERE rp.job_type = :jobType
                """);
        addTenantId(queryBuilder, "rp");
        queryBuilder.append(" ) AND s.company_id = ").append(dto.getClientId());

        queryBuilder.append(" GROUP BY s.id,i.id ");
        queryBuilder.append(getOrderSql(dto.getSort(), " candidate_name "));

        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString(), FteBdReportOnboardDetailVO.class)
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        nativeQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        nativeQuery.setMaxResults(pageable.getPageSize());
        return nativeQuery.getResultList();
    }

    public List<FteBdReportOnboardDetailVO> getFteBdReportPaymentDetail(FteBdReportDetailDTO dto, Pageable pageable) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT
                    CONCAT( s.id,i2.cash_id, COALESCE ( i2.payment_date, '' ),i2.currency ) AS id,
                	t.id AS talent_id,
                	t.full_name AS candidate_name,
                	j.id AS job_id,
                	j.title AS job_title,
                	s.start_date AS onboarding_date,
                	GROUP_CONCAT( DISTINCT CASE WHEN trpku.user_role = 1  THEN trpku.user_id ELSE NULL END ) AS recruiter,
                	GROUP_CONCAT( DISTINCT CASE WHEN trpku.user_role = 0  THEN trpku.user_id ELSE NULL END ) AS am,
                	GROUP_CONCAT( DISTINCT CASE WHEN trpku.user_role = 7  THEN concat(trpku.user_id, '-', trpku.country) ELSE NULL END ) AS co_am,
                	i.invoice_date,
                	i.currency,
                	i2.currency AS cash_in_currency,
                	ROUND(COALESCE(i.origin_billing,0),2) AS origin_billing,
                	ROUND(COALESCE(i.billing,0),2) AS billing,
                	ROUND(COALESCE(i2.origin_cash_in,0),2) AS origin_cash_in,
                	ROUND(COALESCE(i2.cash_in,0),2) AS cash_in,
                	ROUND(COALESCE(i.origin_balance,0),2) AS origin_balance,
                	ROUND(COALESCE(i.balance,0),2) AS balance,
                	i.due_date,
                	i.payment_status    
                FROM
                	`start` s
                	INNER JOIN talent t ON s.talent_id = t.id
                	INNER JOIN job j ON s.job_id = j.id
                	LEFT JOIN talent_recruitment_process_kpi_user trpku ON s.talent_recruitment_process_id = trpku.talent_recruitment_process_id
                	RIGHT JOIN (
                 	SELECT
                        cash.id as cash_id,
                 		i_t.id,
                 		i_t.start_id,
                 		cash.payment_date,
                 		cash.currency,
                 		i_t.company_id,
                 		origin_cash_in,
                 		cash.cash_in     
                 	FROM
                 		invoice i_t
                 		RIGHT JOIN (
                 		SELECT
                            GROUP_CONCAT(ipr.id) as id ,
                            ipr.invoice_id,
                 			ipr.payment_date,
                 			ipr.currency,
                 			SUM(ipr.paid_amount) as origin_cash_in,
                 			SUM(ipr.paid_amount / cr.from_usd_rate_mid) AS cash_in
                 		FROM
                 			invoice_payment_record ipr
                 			LEFT JOIN currency_rate_day cr ON ipr.currency = cr.currency_id   
                 			AND cr.rate_day = ( SELECT MAX( rate_day ) FROM currency_rate_day WHERE currency_id = ipr.currency AND rate_day <= DATE( ipr.payment_date ) )   
                 		WHERE
                 			ipr.activated = 1  """);

        addSingleInvoiceDateRangeFilter(queryBuilder, "ipr.payment_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append("""
                         GROUP BY ipr.invoice_id,ipr.payment_date,ipr.currency
                		) cash ON cash.invoice_id = i_t.id
                		WHERE i_t.invoice_type = 0   
                	)i2 ON i2.start_id = s.id          	
                INNER JOIN (
                SELECT
                    vil.id,
                	vil.start_id,
                	vil.invoice_date,
                	vil.currency,
                	vil.due_amount  AS origin_billing,
                	vil.due_amount / cr.from_usd_rate_mid  AS billing,
                	vil.balance   AS origin_balance,
                	vil.balance / cr.from_usd_rate_mid   AS balance,
                	vil.due_date,
                	vil.`status` AS payment_status    
                FROM
                	view_invoice_list vil
                	LEFT JOIN currency_rate_day cr ON vil.currency = cr.currency_id    
                	AND cr.rate_day = ( SELECT MAX( rate_day ) FROM currency_rate_day WHERE currency_id = vil.currency AND rate_day <= DATE( vil.invoice_date ) )    
                WHERE
                	 vil.invoice_type = 0    
                	AND vil.`STATUS` IN ( 0, 1, 2, 8 )
                	AND vil.start_id in (SELECT st.id FROM `start` st WHERE st.company_id = """).append(dto.getClientId()).append(")");
        queryBuilder.append(""" 
                	) i ON i.id = i2.id 
                	WHERE
                	j.recruitment_process_id in ( SELECT id FROM recruitment_process rp WHERE rp.job_type = :jobType
                """);
        addTenantId(queryBuilder, "rp");
        queryBuilder.append(" ) AND s.company_id = ").append(dto.getClientId());

        queryBuilder.append(" GROUP BY s.id,i2.id,i2.payment_date,i2.currency ");
        queryBuilder.append(getOrderSql(dto.getSort(), " candidate_name "));

        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString(), FteBdReportOnboardDetailVO.class)
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        nativeQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        nativeQuery.setMaxResults(pageable.getPageSize());
        return nativeQuery.getResultList();
    }


    public Long countFteBdReportList(FteBdReportDTO dto) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT  COUNT(distinct c.id) from company c
                   INNER JOIN business_flow_administrator bfa ON c.id = bfa.company_id  AND bfa.sales_lead_role in (0,1,2)
                 WHERE
                1=1
                """);
        addTenantId(queryBuilder, "c");
        addDateRangeFilter(queryBuilder, "c.created_date", dto.getClientCreationStartDate(), dto.getClientCreationEndDate());
        addClientIdsIn(queryBuilder, "c.id", dto.getClientIds());
        addUserIdsFilter(queryBuilder, "ambfa", "amucr", dto.getAmIds(), "0");
        addUserIdsFilter(queryBuilder, "slobfa", "sloucr", dto.getSalesLeadOwnerIds(), "1");
        addUserIdsFilter(queryBuilder, "bdobfa", "bdoucr", dto.getBdOwnerIds(), "2");
        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString());
        return ((BigInteger) nativeQuery.getSingleResult()).longValue();
    }


    public Long countFteBdReportJob(FteBdReportDetailDTO dto, String permission) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT COUNT(1) from job j
                	where j.recruitment_process_id in ( SELECT id FROM recruitment_process rp WHERE rp.job_type = :jobType """);
        addTenantId(queryBuilder, "rp");
        queryBuilder.append(" ) AND j.company_id = ").append(dto.getClientId());
        queryBuilder.append(permission);
        addDateRangeFilter(queryBuilder, "j.created_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());

        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString())
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        return ((BigInteger) nativeQuery.getSingleResult()).longValue();
    }


    public Long countFteBdReportOnboard(FteBdReportDetailDTO dto) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT
                	COUNT(1)      
                FROM
                	`start` s
                	INNER JOIN talent t ON s.talent_id = t.id
                	INNER JOIN job j ON s.job_id = j.id
                	LEFT JOIN (
                	SELECT
                		vil.start_id,
                		vil.invoice_date,
                        vil.currency      
                	FROM
                		view_invoice_list vil      
                	WHERE
                		vil.invoice_type = 0      
                		AND vil.`STATUS` IN ( 0, 1, 2, 8 )  
                		AND vil.start_id in (SELECT st.id FROM `start` st WHERE st.company_id = """).append(dto.getClientId()).append(")");
        queryBuilder.append("""
                	) i ON i.start_id = s.id      
                WHERE
                	j.recruitment_process_id in ( SELECT id FROM recruitment_process rp WHERE rp.job_type = :jobType 
                                """);
        addTenantId(queryBuilder, "rp");
        queryBuilder.append(" ) AND j.company_id = ").append(dto.getClientId());
        addDateRangeFilter(queryBuilder, "s.start_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());

        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString())
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        return ((BigInteger) nativeQuery.getSingleResult()).longValue();
    }

    public Long countFteBdReportInvoice(FteBdReportDetailDTO dto) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                
                SELECT
                	COUNT(1)      
                FROM
                	`start` s
                	INNER JOIN talent t ON s.talent_id = t.id
                	INNER JOIN job j ON s.job_id = j.id
                	INNER JOIN (
                	SELECT
                		vil.start_id,
                		vil.invoice_date,
                        vil.currency      
                	FROM
                		view_invoice_list vil      
                	WHERE
                		vil.invoice_type = 0      
                		AND vil.`STATUS` IN ( 0, 1, 2, 8 ) 
                		AND vil.start_id in (SELECT st.id FROM `start` st WHERE st.company_id = """).append(dto.getClientId()).append(")");

        addSingleInvoiceDateRangeFilter(queryBuilder, "vil.invoice_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append("""
                	) i ON i.start_id = s.id      
                WHERE
                	j.recruitment_process_id in ( SELECT id FROM recruitment_process rp WHERE rp.job_type = :jobType 
                                """);
        addTenantId(queryBuilder, "rp");
        queryBuilder.append(" ) AND j.company_id = ").append(dto.getClientId());

        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString())
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        return ((BigInteger) nativeQuery.getSingleResult()).longValue();
    }

    public Long countFteBdReportPayment(FteBdReportDetailDTO dto) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                 SELECT  COUNT(r.id) AS total_count FROM (
                SELECT
                	CONCAT( s.id,i2.cash_id ) AS id    
                FROM
                	`start` s
                	INNER JOIN talent t ON s.talent_id = t.id
                	INNER JOIN job j ON s.job_id = j.id
                	RIGHT JOIN (
                 	SELECT
                        cash.id as cash_id,
                 		i_t.id,
                 		i_t.start_id,
                 		cash.payment_date,
                 		cash.currency,
                 		i_t.company_id
                 	FROM 
                 		invoice i_t
                 		RIGHT JOIN (
                 		SELECT
                 			GROUP_CONCAT(ipr.id) as id ,
                 			ipr.invoice_id,
                 			ipr.payment_date,
                 			ipr.currency
                 		FROM
                 			invoice_payment_record ipr
                 		WHERE ipr.activated = 1  
                 		""");
        addSingleInvoiceDateRangeFilter(queryBuilder, "ipr.payment_date", dto.getDataCoverageStartDate(), dto.getDataCoverageEndDate());
        queryBuilder.append("""
                       GROUP BY ipr.invoice_id,ipr.payment_date,ipr.currency
                      		) cash ON cash.invoice_id = i_t.id
                      		WHERE i_t.invoice_type = 0   
                      	)i2 ON i2.start_id = s.id  
                INNER JOIN (
                      	SELECT
                      		vil.start_id,
                      		vil.invoice_date,
                              vil.currency      
                      	FROM
                      		view_invoice_list vil      
                      	WHERE
                      		vil.invoice_type = 0      
                      		AND vil.`STATUS` IN ( 0, 1, 2, 8 ) 
                      		AND vil.start_id in (SELECT st.id FROM `start` st WHERE st.company_id = """).append(dto.getClientId()).append(")");
        queryBuilder.append("""
                	) i ON i.start_id = s.id      
                WHERE
                	j.recruitment_process_id in ( SELECT id FROM recruitment_process rp WHERE rp.job_type = :jobType 
                                """);
        addTenantId(queryBuilder, "rp");
        queryBuilder.append(" ) AND j.company_id = ").append(dto.getClientId()).append("  GROUP BY s.id,i2.cash_id ) r");

        Query nativeQuery = entityManager.createNativeQuery(queryBuilder.toString())
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        return ((BigInteger) nativeQuery.getSingleResult()).longValue();
    }

    private String getOrderSql(SearchSortDTO sort, String defaultSort) {
        StringBuilder sb = new StringBuilder();
        if (null != sort && StringUtils.isNotEmpty(sort.getProperty())) {
            String order = sort.getProperty();
            String direction = sort.getDirection();
            String column = StrUtil.toUnderlineCase(order);
            sb.append(" order by ");
            if ("client_name".equals(column) || "job_title".equals(column) || "candidate_name".equals(column)) {
                sb.append("CONVERT( ").append(column).append(" USING gbk) ");
                sb.append(" ").append(direction);
            } else if ("payment_status".equals(column)) {
                // 如果排序字段是枚举类型的 status
                sb.append("CASE ")
                        .append(column)
                        .append(" ")
                        .append("WHEN 0 THEN 'PAID' ")
                        .append("WHEN 1 THEN 'UNPAID' ")
                        .append("WHEN 2 THEN 'OVERDUE' ")
                        .append("WHEN 3 THEN 'STARTUP_FEE_PAID_USED' ")
                        .append("WHEN 4 THEN 'STARTUP_FEE_PAID_UNUSED' ")
                        .append("WHEN 6 THEN 'STARTUP_FEE_UNPAID_UNUSED' ")
                        .append("WHEN 7 THEN 'VOID' ")
                        .append("WHEN 8 THEN 'PARTIALLY_PAID' ")
                        .append("END ");
                sb.append(direction);
            } else {
                sb.append(column);
                sb.append(" ").append(direction);
            }
        } else {
            sb.append(" order by " + defaultSort + " asc ");
        }
        return sb.toString();
    }

    private void addTenantId(StringBuilder sb, String tableName) {
        sb.append(" AND ").append(tableName).append(".tenant_id = ").append(SecurityUtils.getTenantId());
    }


    private void addDateRangeFilter(StringBuilder queryBuilder, String dateName, LocalDate dataCoverageStartDate, LocalDate dataCoverageEndDate) {
        if (null != dataCoverageStartDate && null != dataCoverageEndDate) {
            queryBuilder.append(" AND ").append(dateName).append(" BETWEEN '").append(dataCoverageStartDate).append("' AND '").append(dataCoverageEndDate.plusDays(1L)).append("' ");
        }
    }

    private void addSingleInvoiceDateRangeFilter(StringBuilder queryBuilder, String dateName, LocalDate dataCoverageStartDate, LocalDate dataCoverageEndDate) {
        if (null != dataCoverageStartDate && null != dataCoverageEndDate) {
            queryBuilder.append(" AND ").append(dateName).append(" BETWEEN '").append(dataCoverageStartDate).append("' AND '").append(dataCoverageEndDate).append("' ");
        }
    }

    private void addInvoiceDateRangeFilter(StringBuilder queryBuilder, LocalDate dataCoverageStartDate, LocalDate dataCoverageEndDate) {
        if (null != dataCoverageStartDate && null != dataCoverageEndDate) {
            queryBuilder.append(" AND (");
            queryBuilder.append("vil.invoice_date").append(" BETWEEN '").append(dataCoverageStartDate).append("' AND '").append(dataCoverageEndDate).append("' ");
            queryBuilder.append(" OR ");
            queryBuilder.append("vil.payment_date").append(" BETWEEN '").append(dataCoverageStartDate).append("' AND '").append(dataCoverageEndDate).append("' ");
            queryBuilder.append(" ) ");
        }
    }


    private void addUserIdsFilter(StringBuilder sb, String bfaName, String ucrName, List<Long> userIds, String salesLeadRole) {
        if (CollUtil.isNotEmpty(userIds)) {
            String userIdsStr = userIds.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            sb.append(" ").append("""
                    AND EXISTS (
                    	SELECT 1
                    	FROM business_flow_administrator AS 
                    	""").append(bfaName)
                    .append(" WHERE ")
                    .append(bfaName).append(".user_id in (").append(userIdsStr).append(") AND c.id = ").append(bfaName).append(".company_id AND ")
                    .append(bfaName).append(".sales_lead_role =").append(salesLeadRole).append(")");
        }
    }

}
