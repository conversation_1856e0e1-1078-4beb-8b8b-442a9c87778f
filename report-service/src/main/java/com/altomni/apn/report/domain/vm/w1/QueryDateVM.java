package com.altomni.apn.report.domain.vm.w1;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
public class QueryDateVM implements Serializable {

    private String fromDate;

    private String toDate;

    public String getFromDate() {
        return fromDate;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public QueryDateVM(String fromDate, String toDate) {
        this.fromDate = fromDate;
        this.toDate = toDate;
    }
}
