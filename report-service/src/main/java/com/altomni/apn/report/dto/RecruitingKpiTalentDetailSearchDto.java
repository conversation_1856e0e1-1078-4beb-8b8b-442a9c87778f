package com.altomni.apn.report.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiDetailBaseDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiTalentDetailSearchDto extends RecruitingKpiDetailBaseDto {

    private Boolean isCompanyFlag;

    private TalentDetailSearchDto detail;

    public String getOrderBySql() {
        if (ObjectUtil.isEmpty(getSort())) {
            return "";
        }
        return switch (getSort().getProperty()) {
            case "fullName" -> " order by CONVERT( t.full_name USING gbk) " + getSort().getDirection();
            case "jobSearchStatus" -> " order by t.motivation_id " + getSort().getDirection();
            case "createdDate" -> " order by t.created_date " + getSort().getDirection();
            case "lastModifiedDate" -> " order by t.last_modified_date " + getSort().getDirection();
            case "talentNotes" -> " order by count( DISTINCT tn.id ) " + getSort().getDirection();
            default -> "";
        };
    }

    public void appendDetailCondition(StringBuilder sb, Map<String, Object> whereParamMap) {
        if (detail == null) {
           return;
        }
        if (StrUtil.isNotBlank(detail.getFullName())) {
            sb.append(" and t.full_name like :fullName ");
            whereParamMap.put("fullName", "%" + detail.getFullName() + "%");
        }
        if (StrUtil.isNotBlank(detail.getCurrentCompany())) {
            sb.append(" and tai.current_company like :currentCompany ");
            whereParamMap.put("currentCompany", "%" + detail.getCurrentCompany() + "%");
        }
        if (StrUtil.isNotBlank(detail.getCurrentPosition())) {
            sb.append(" and tai.current_position like :currentPosition ");
            whereParamMap.put("currentPosition", "%" + detail.getCurrentPosition() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getJobSearchStatus())) {
            sb.append(" and t.motivation_id = :motivationId ");
            whereParamMap.put("motivationId", detail.getJobSearchStatus());
        }
        if (ObjectUtil.isNotEmpty(detail.getCreatedBy())) {
            sb.append(" and t.puser_id = :createdBy ");
            whereParamMap.put("createdBy", detail.getCreatedBy());
        }
        if (ObjectUtil.isNotEmpty(detail.getLastModifiedBy())) {
            sb.append(" and t.last_update_user_id = :lastModifiedBy ");
            whereParamMap.put("lastModifiedBy", detail.getLastModifiedBy());
        }
    }

}
