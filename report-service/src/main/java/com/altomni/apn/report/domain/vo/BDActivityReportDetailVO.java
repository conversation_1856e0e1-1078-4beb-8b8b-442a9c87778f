package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * BDProgressDetail展示VO
 *
 * <AUTHOR>
 * @date 2024/03/12
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BDActivityReportDetailVO implements Serializable {

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 客户公司id
     */
    private Long accountCompanyId;

    /**
     * 联系时间
     */
    private String contactTime;

    /**
     * 备注
     */
    private String note;

    /**
     * 客户联系人
     */
    private List<String> accountContactId;

    private Map<Long,String> accountContacts;

    /**
     * 服务类型
     */
    private List<Integer> serviceTypeIds;

    /**
     * BD所有者
     */
    private List<Long> bdOwnerIds;

    private List<UserUidNameDTO> bdOwners;

    /**
     * 销售线索负责人
     */
    private List<Long> salesLeadOwnerIds;

    private List<UserUidNameDTO> salesLeadOwners;


    /**
     * 销售线索id
     */
    private Long salesLeadId;


    /**
     * 客户模块销售线索id
     */
    private Long accountBusinessId;


}
