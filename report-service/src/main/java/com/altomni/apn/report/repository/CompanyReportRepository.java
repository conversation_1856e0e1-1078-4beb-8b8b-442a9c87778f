package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportBdTrackingDetailVO;
import com.altomni.apn.report.domain.vo.ReportBdTrackingVO;
import com.altomni.apn.report.dto.BdTrackingDetailDTO;
import com.altomni.apn.report.dto.BdTrackingSearchDTO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.util.List;

@Repository
public class CompanyReportRepository extends BaseCustomRepository{

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    public List<ReportBdTrackingVO> searchBdTrackingReport(BdTrackingSearchDTO dto) {
        String sql = """
                SELECT
                    puser_id user_id,
                    (case CONCAT(u.first_name,u.last_name) regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, " ",u.last_name) END) user_name,
                    MAX(CASE WHEN contact_type = 8 THEN num else 0 END) as call_num,
                    MAX(CASE WHEN contact_type = 8 THEN client_num else 0 END) as call_client_num,
                    MAX(CASE WHEN contact_type = 9 THEN num else 0 END) as email_num,
                    MAX(CASE WHEN contact_type = 9 THEN client_num else 0 END) as email_client_num,
                    MAX(CASE WHEN contact_type = 10 THEN num else 0 END) as new_meeting_num,
                    MAX(CASE WHEN contact_type = 10 THEN client_num else 0 END) as new_meeting_client_num,
                    MAX(CASE WHEN contact_type = 11 THEN num else 0 END) as formal_meeting_num,
                    MAX(CASE WHEN contact_type = 11 THEN client_num else 0 END) as formal_meeting_client_num,
                    MAX(CASE WHEN contact_type = 12 THEN num else 0 END) as lunch_meeting_num,
                    MAX(CASE WHEN contact_type = 12 THEN client_num else 0 END) as lunch_meeting_client_num,
                    MAX(CASE WHEN contact_type = 13 THEN num else 0 END) as ooc_meeting_num,
                    MAX(CASE WHEN contact_type = 13 THEN client_num else 0 END) as ooc_meeting_client_num,
                    MAX(CASE WHEN contact_type = 14 THEN num else 0 END) as opp_pipeline_meeting_num,
                    MAX(CASE WHEN contact_type = 14 THEN client_num else 0 END) as opp_pipeline_meeting_client_num,
                    MAX(CASE WHEN contact_type = 1 THEN num else 0 END) as client_visit_num,
                    MAX(CASE WHEN contact_type = 1 THEN client_num else 0 END) as client_visit_client_num,
                    MAX(CASE WHEN contact_type = 4 THEN num else 0 END) as reference_call_num,
                    MAX(CASE WHEN contact_type = 4 THEN client_num else 0 END) as reference_call_client_num
                FROM (
                    SELECT
                        cpn.puser_id,
                        cpn.contact_type,
                        COUNT(cpn.id) as num,
                        COUNT(DISTINCT cpn.company_id) as client_num
                    FROM
                        company_progress_note cpn
                        INNER JOIN company c ON cpn.company_id = c.id
                        INNER JOIN user u on cpn.puser_id = u.id and u.activated = 1
                    WHERE
                        cpn.contact_type IN (1, 4, 8, 9, 10, 11, 12, 13, 14)
                    	and c.tenant_id = :tenantId and cpn.contact_date BETWEEN :startDate and :endDate
                    	""" + getWhereSql(dto) + """
                    GROUP BY
                        cpn.puser_id,
                        cpn.contact_type
                ) cpn
                    inner join user u on cpn.puser_id = u.id
                    GROUP BY puser_id
                    order by CONVERT(user_name USING gbk) asc
                """;
        Query query = entityManager.createNativeQuery(sql, ReportBdTrackingVO.class)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("startDate", dto.getFrom())
                .setParameter("endDate", dto.getTo());


        if (CollUtil.isNotEmpty(dto.getUserIds())) {
            query.setParameter("userIds", dto.getUserIds());
        }else if (CollUtil.isNotEmpty(dto.getTeamIds())) {
            query.setParameter("teamIds", dto.getTeamIds());
        }

//        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
//        if (Objects.isNull(teamDataPermission)) {
//            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
//        }
//        List<Long> userIds = new ArrayList<>();
//        if (teamDataPermission.getSelf()) {
//            if (CollectionUtils.isEmpty(dto.getUserIds()) || dto.getUserIds().contains(SecurityUtils.getUserId())) {
//                userIds.add(SecurityUtils.getUserId());
//            }
//        }else{
//            userIds = dto.getUserIds();
//        }
        return query.getResultList();
    }

    private String getWhereSql(BdTrackingSearchDTO dto) {
        String sql = "";
        if (CollUtil.isNotEmpty(dto.getUserIds())) {
            sql += " and cpn.puser_id in :userIds ";
        }else if (CollUtil.isNotEmpty(dto.getTeamIds())) {
            sql += " and cpn.pteam_id in :teamIds ";
        }
        return sql;
    }

    public List<ReportBdTrackingDetailVO> getBdTrackingDetailList(BdTrackingDetailDTO dto) {
        String sql = """
                SELECT
                	cpn.id id,
                	c.id company_id,
                	c.full_business_name company_name,
                	cpn.contact_date,
                	cpn.note,
                	cpn.client_contact_id,
                	t.full_name client_contact,
                	group_concat(cslst.service_type_id) service_type_id,
                	GROUP_CONCAT(distinct (case CONCAT(uon.first_name,uon.last_name) regexp '[一-龥]' when 1 then CONCAT(uon.last_name,uon.first_name) ELSE CONCAT(uon.first_name, " ",uon.last_name) END)) sales_lead_owner,
                	GROUP_CONCAT(distinct (case CONCAT(bdu.first_name,bdu.last_name) regexp '[一-龥]' when 1 then CONCAT(bdu.last_name,bdu.first_name) ELSE CONCAT(bdu.first_name, " ",bdu.last_name) END)) sales_lead_bd_owner
                FROM
                	company_progress_note cpn
                	INNER JOIN company c ON c.id = cpn.company_id
                	LEFT JOIN account_business_service_type_relation cslst ON cslst.account_business_id = cpn.sales_lead_id
                	INNER JOIN company_sales_lead_client_contact cslcc ON cslcc.id = cpn.client_contact_id
                	INNER JOIN talent t ON t.id = cslcc.talent_id
                	left join business_flow_administrator conwer on conwer.sales_lead_id = cpn.sales_lead_id and conwer.sales_lead_role = 1
                	left join user uon on uon.id = conwer.user_id
                	left join business_flow_administrator bdower on bdower.sales_lead_id = cpn.sales_lead_id and bdower.sales_lead_role = 2
                	left join user bdu on bdu.id = bdower.user_id
                WHERE
                	cpn.contact_type IN (1, 4, 8, 9, 10, 11, 12, 13, 14)
                    and c.tenant_id = :tenantId and cpn.contact_date BETWEEN :startDate and :endDate
                    and cpn.puser_id = :userId and cpn.contact_type = :contactType
                    """ + getWhereSql(dto) + """
                GROUP BY
                	cpn.id
                	order by CONVERT(company_name USING gbk) asc, cpn.contact_date desc
                """;
        Query query = entityManager.createNativeQuery(sql, ReportBdTrackingDetailVO.class)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("startDate", dto.getFrom())
                .setParameter("endDate", dto.getTo())
                .setParameter("userId", dto.getUserId())
                .setParameter("contactType", dto.getContactType().toDbValue());
        if (CollUtil.isNotEmpty(dto.getTeamIds())) {
            query.setParameter("teamIds", dto.getTeamIds());
        }
        if (CollUtil.isNotEmpty(dto.getUserIds())) {
            query.setParameter("userIds", dto.getUserIds());
        }
        return query.getResultList();
    }
}
