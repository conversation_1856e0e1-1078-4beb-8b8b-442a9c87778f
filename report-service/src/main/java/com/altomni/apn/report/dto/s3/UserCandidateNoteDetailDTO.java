package com.altomni.apn.report.dto.s3;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserCandidateNoteDetailDTO extends UserCandidateBaseDetailDTO implements Serializable{

    private static final long serialVersionUID = 5083550640659521888L;

    private String title;

    private String note;

    public UserCandidateNoteDetailDTO(Long talentId, String name ,String title,String note,String createdDate){
        this.setTalentId(talentId);
        this.setName(name);
        this.title = title;
        this.note = note;
        this.setCreatedDate(createdDate);
    }
}
