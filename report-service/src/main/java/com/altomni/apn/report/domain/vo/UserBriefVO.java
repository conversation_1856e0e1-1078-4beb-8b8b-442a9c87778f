package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.report.domain.vm.p1.UserNameVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;

@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserBriefVO {

    private Long id;

    private String username;

    private String firstName;

    private String lastName;

    private String fullName;

    private String email;

    private String phone;

    private Long tenantId;

    private Integer jobPermission;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getJobPermission() {
        return jobPermission;
    }

    public void setJobPermission(Integer jobPermission) {
        this.jobPermission = jobPermission;
    }

    public static UserBriefVO fromUserNameVM(UserNameVM userNameVM) {
        UserBriefVO userBriefVO = new UserBriefVO();
        ServiceUtils.myCopyProperties(userNameVM, userBriefVO);
        userBriefVO.setFullName(CommonUtils.formatFullName(userNameVM.getFirstName(), userNameVM.getLastName()));
        return userBriefVO;
    }
}
