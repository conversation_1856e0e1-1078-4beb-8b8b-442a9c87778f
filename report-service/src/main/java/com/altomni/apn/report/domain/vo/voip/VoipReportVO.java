package com.altomni.apn.report.domain.vo.voip;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoipReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userId;

    private String userName;

    private Integer totalCalls = 0;

    private Integer connects = 0;

    private Integer qualifiedConnects = 0;

    private Double connectRate = 0d;

    private Integer totalCandidateCalls = 0;

    private Integer totalCandidateReached = 0;

    private Double totalTalkMinutes = 0d;

    private Long teamId;

}
