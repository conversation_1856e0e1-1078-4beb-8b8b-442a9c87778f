package com.altomni.apn.report.dto.s3;

import com.altomni.apn.common.dto.activity.ChangeFieldDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ChangeRecordDTO  implements Serializable {
    private static final long serialVersionUID = 3233962341485645138L;

    private Long id;

    private String name;

    private List<ChangeFieldDTO> details;

    private String createdBy;

    private Instant createdAt;

    private Instant lastModifiedDate;
}
