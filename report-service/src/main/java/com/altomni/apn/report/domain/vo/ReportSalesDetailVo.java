package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivisionConverter;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.domain.enumeration.start.StartType;
import com.altomni.apn.job.domain.enumeration.start.StartTypeConverter;
import com.altomni.apn.report.config.excel.CustomJobTypeNameConverter;
import com.altomni.apn.report.domain.enumeration.AssignmentDivisionDataConverter;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Convert;
import javax.persistence.Transient;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

import static com.altomni.apn.report.domain.enumeration.StartReportStatus.OFFER_ACCEPTED;

@Data
@Accessors(chain = true)
public class ReportSalesDetailVo implements AttachConfidentialTalent {

    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private BigDecimal totalBillAmount;

    @ExcelProperty(value = "GP", index = 20)
    @Transient
    private String totalBillAmountFormat;

    @ExcelProperty(value = "Candidate ID", index = 0)
    private Long talentId;

    @ExcelProperty(value = "Candidate", index = 1)
    private String fullName;

    @ExcelProperty(value = "Job ID", index = 2)
    private Long jobId;

    @ExcelProperty(value = "Job Title", index = 3)
    private String title;

    @ExcelProperty(value = "Work Location", index = 4)
    private String workLocation;

    @ExcelProperty(value = "Company ID", index = 5)
    private Long companyId;

    @ExcelProperty(value = "Company", index = 6)
    private String companyName;

    @Convert(converter = StartTypeConverter.class)
    @ExcelIgnore
    private StartType startType;

    @ExcelProperty(value = "Status", index = 7)
    private String status;

    @ExcelProperty(value = "AM", index = 9)
    private String am;

    @ExcelProperty(value = "Recruiter", index = 10)
    private String recruiter;

    @ExcelProperty(value = "Sourcer", index = 11)
    private String sourcer;

    @ExcelProperty(value = "DM", index = 12)
    private String dm;

    @ExcelProperty(value = "Co-AM", index = 13)
    private String coAm;

    @ExcelProperty(value = "AC", index = 14)
    private String ac;

    @ExcelProperty(value = "Sales Lead Owner", index = 15)
    private String salesLeadOwner;

    @ExcelProperty(value = "BD Owner", index = 16)
    private String bdOwner;

    @ExcelProperty(value = "Candidate Owner", index = 17)
    private String owner;

    @ExcelIgnore
    private LocalDate startDate;

    @ExcelProperty(value = "Start Date", index = 18)
    private String startDateStr;

    @ExcelIgnore
    private LocalDate endDate;

    @ExcelProperty(value = "End Date", index = 19)
    private String endDateStr;

    @ExcelIgnore
    private String type;

    @ExcelIgnore
    private LocalDate sStartDate;

    @ExcelIgnore
    private LocalDate sEndDate;

    @ExcelIgnore
    private LocalDate warrantyEndDate;

    @ExcelIgnore
    private BigDecimal finalPayRate;

    @ExcelIgnore
    private BigDecimal finalBillRate;

    @ExcelIgnore
    private BigDecimal estimatedWorkingHourPerWeek;

    @Convert(converter = JobTypeConverter.class)
    @ExcelProperty(value = "Position Type", index = 8, converter = CustomJobTypeNameConverter.class)
    private JobType jobType;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType rateUnitType;

    @Transient
    @ExcelIgnore
    private BigDecimal revenue;

    @Transient
    @ExcelProperty(value = "Revenue", index = 21)
    private String revenueFormat;

    @Transient
    @ExcelIgnore
    private BigDecimal percentageOfGpRevenue;

    @Transient
    @ExcelProperty(value = "GP/Revenue", index = 22)
    private String percentageOfGpRevenueForStr;

    @ExcelIgnore
    private BigDecimal fromUsdRate;

    @ExcelIgnore
    private Integer currency;

    @ExcelProperty(value = "Job Division", index = 23, converter = AssignmentDivisionDataConverter.class)
    @Convert(converter = AssignmentDivisionConverter.class)
    private AssignmentDivision jobDivision;

    @ExcelIgnore
    private Instant onboardDate;

    @ExcelProperty(value = "Hire Entered Date", index = 24)
    @Transient
    private String onboardDateStr;

    @ExcelProperty(value = "Zipcode", index = 25)
    private String zipCode;

    @ExcelProperty(value = "Recruited by", index = 26)
    private String onboardBy;

    @ExcelIgnore
    private Long jobPteamId;

    @ExcelIgnore
    private boolean isPrivateJob;

    @Transient
    @ExcelIgnore
    private String customTimezone;

    /**
     * true: 已离职， false: 未离职
     */
    @ExcelIgnore
    private Boolean resigned;

    /**
     * true: 是 converted to FTE 流程
     * false: 非 converted to FTE 流程
     */
    @ExcelIgnore
    private Boolean convertedToFte;

    @ExcelIgnore
    private Boolean isNeedUpdateStatusFlag;

    @Transient
    @ExcelIgnore
    private Boolean confidentialTalentViewAble;

    @Transient
    @ExcelIgnore
    private ConfidentialInfoDto confidentialInfo;

    public void setPercentageOfGpRevenue(BigDecimal percentageOfGpRevenue) {
        this.percentageOfGpRevenue = percentageOfGpRevenue;
        if (percentageOfGpRevenue != null) {
            this.percentageOfGpRevenueForStr = percentageOfGpRevenue.multiply(BigDecimal.valueOf(100.0)).setScale(2, RoundingMode.HALF_UP).toPlainString() + "%";
        }
    }

    public String getStartDateStr() {
        return startDate == null? null: startDate.toString();
    }

    public String getEndDateStr() {
        return endDate == null? null: endDate.toString();
    }

    public String getOnboardDateStr() {
        if (onboardDate != null) {
            return onboardDate.atZone(ZoneId.of(customTimezone)).toLocalDateTime().format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD));
        }
        return null;
    }

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        this.confidentialInfo = confidentialInfo;
    }

    @Override
    public void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble) {
        this.confidentialTalentViewAble = confidentialTalentViewAble;
    }

    public void setUpdateStatusFlag() {
        if (StrUtil.isNotBlank(type) && ObjectUtil.isNotEmpty(startDate) && Objects.equals("Offer Accepted", type)) {
            //判断合同是否开始
            SecurityUtils.getCurrentLoginInformation().ifPresent(loginInformation -> {
                String timezone = loginInformation.getTimezone();
                ZonedDateTime zonedDateTime = ZonedDateTime.now(ZoneId.of(timezone));
                isNeedUpdateStatusFlag = startDate.isBefore(zonedDateTime.toLocalDate());
            });
        }
    }

    @Override
    public void encrypt() {
        Field[] fields = ReflectUtil.getFields(ReportSalesDetailVo.class);
        List<String> ignoreField = List.of("talentId", "isPrivateJob", "confidentialTalentViewAble", "confidentialInfo");
        for (Field field : fields) {
           if (ignoreField.contains(field.getName())) {
               continue;
           }
           ReflectUtil.setFieldValue(this, field, null);
        }
    }
}
