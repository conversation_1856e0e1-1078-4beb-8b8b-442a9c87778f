package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiOfferAcceptDetailVO extends RecruitingKpiApplicationBaseDetailVO {

    private LocalDate onboardDate;

    private LocalDate warrantyEndDate;

    @Override
    public void encrypt() {
        super.encrypt();
        this.onboardDate = null;
        this.warrantyEndDate = null;
    }

}