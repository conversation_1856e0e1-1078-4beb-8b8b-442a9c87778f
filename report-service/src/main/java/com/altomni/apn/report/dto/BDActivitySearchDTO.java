package com.altomni.apn.report.dto;

import com.altomni.apn.report.domain.enumeration.ReportUserCandidateSearchType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Set;

import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z;


@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class BDActivitySearchDTO {

    private String fromDate;

    private String toDate;

    private Set<Long> targetIds;

    private ReportUserCandidateSearchType targetType;

    private String timeZone;
}
