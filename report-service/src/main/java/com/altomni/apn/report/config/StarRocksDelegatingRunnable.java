package com.altomni.apn.report.config;

import com.altomni.apn.report.domain.enumeration.DataSourceType;

public class StarRocksDelegatingRunnable implements Runnable{
    private final Runnable delegate;

    public StarRocksDelegatingRunnable(Runnable delegate) {
        this.delegate = delegate;
    }

    @Override
    public void run() {
        try {
            DataSourceHolder.setCurrentDb(DataSourceType.STARROCKS);
            delegate.run();
        } finally {
            DataSourceHolder.clear();
        }
    }
}
