package com.altomni.apn.report.domain.vm.p1;

import com.altomni.apn.report.domain.enumeration.ReportCountType;
import com.altomni.apn.report.domain.enumeration.ReportCountTypeConverter;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
public class ReportCountVM implements Serializable {

    @Id
    private Long userId;

    private String userFirstName;

    private String userLastName;

    private int count;

    private String activityIds;

    @Convert(converter = ReportCountTypeConverter.class)
    private ReportCountType type;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserFirstName() {
        return userFirstName;
    }

    public void setUserFirstName(String userFirstName) {
        this.userFirstName = userFirstName;
    }

    public String getUserLastName() {
        return userLastName;
    }

    public void setUserLastName(String userLastName) {
        this.userLastName = userLastName;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public ReportCountType getType() {
        return type;
    }

    public void setType(ReportCountType type) {
        this.type = type;
    }

    public String getActivityIds() {
        return activityIds;
    }

    public void setActivityIds(String activityIds) {
        this.activityIds = activityIds;
    }

    public ReportCountVM() {
    }

    public ReportCountVM(Long userId, String userFirstName, String userLastName, int count, String activityIds, ReportCountType type) {
        this.userId = userId;
        this.userFirstName = userFirstName;
        this.userLastName = userLastName;
        this.count = count;
        this.activityIds = activityIds;
        this.type = type;
    }
}
