package com.altomni.apn.report.domain.olap;

import com.altomni.apn.report.dto.ReportDTO;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.collections4.CollectionUtils;
import org.openapitools.client.model.*;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * olap 查询上下文
 */
@Data
@Accessors(chain = true)
public class QueryContext {

    private ReportDTO reportDTO;

    private ChartDataQueryObject queryConfig;

    private DatasetRestApiGet dataSet;

    public Integer getDataSetId() {
        return reportDTO.getDatasetId();
    }

    public String getDataSetName() {
        return reportDTO.getName();
    }


    public List<String> provideAllDim() {
        if (queryConfig == null || CollectionUtils.isEmpty(queryConfig.getColumns())) {
            return List.of();
        }
        return queryConfig.getColumns().stream().map(Object::toString).toList();
    }

    public Map<String, DatasetRestApiGetTableColumn> provideColumnMap() {
        if (dataSet == null || CollectionUtils.isEmpty(dataSet.getColumns())) {
            return Map.of();
        }
        return  dataSet.getColumns().stream()
            .collect(Collectors.toMap(DatasetRestApiGetTableColumn::getColumnName, Function.identity(), (o1, o2) -> o1));
    }

    public List<String> provideColumnNames() {
        return provideColumnMap().values().stream().map(DatasetRestApiGetTableColumn::getColumnName).toList();
    }

    public List<ChartDataFilter> provideFilters() {
        if (queryConfig == null || CollectionUtils.isEmpty(queryConfig.getFilters())) {
            return List.of();
        }
        return queryConfig.getFilters();
    }

    public List<Object> provideMetrics() {
        if (queryConfig == null || CollectionUtils.isEmpty(queryConfig.getMetrics())) {
            return List.of();
        }
        return queryConfig.getMetrics();

    }

}
