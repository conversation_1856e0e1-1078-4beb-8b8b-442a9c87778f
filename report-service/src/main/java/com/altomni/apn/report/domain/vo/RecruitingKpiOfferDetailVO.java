package com.altomni.apn.report.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiOfferDetailVO extends RecruitingKpiApplicationBaseDetailVO {

    private LocalDate estimateOnboardDate;

    @Override
    public void encrypt() {
        super.encrypt();
        this.estimateOnboardDate = null;
    }

}