package com.altomni.apn.report.domain.vm.s2;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.Instant;

@Entity
public class DormantJobVM implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty(value = "job id")
    private Long id;

    @ApiModelProperty(value = "the job title this talent applied")
    private String jobTitle;

    @ApiModelProperty(value = "Job type", allowableValues = "Direct_Placement, Contract, Right_To_Hire, Full_Time, Part_Time")
    private JobType jobType;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "Open, OnHold, Cancelled, Closed")
    private JobStatus jobStatus;

    @ApiModelProperty(value = "the company id this talent applied")
    private Long companyId;

    @ApiModelProperty(value = "the company name this talent applied")
    private String company;

    @ApiModelProperty(value = "The date the job is posted to ATS or job boards.")
    private Instant postingTime;

    @ApiModelProperty(value = "AM id")
    private Long amId;

    @ApiModelProperty(value = "AM firstName")
    private String firstName;

    @ApiModelProperty(value = "AM lastName")
    private String lastName;

    @ApiModelProperty(value = "last update time")
    private Instant lastModifiedDate;

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getJobTitle() { return jobTitle; }

    public void setJobTitle(String jobTitle) { this.jobTitle = jobTitle; }

    public JobType getJobType() { return jobType; }

    public void setJobType(JobType jobType) { this.jobType = jobType; }

    public JobStatus getJobStatus() { return jobStatus; }

    public void setJobStatus(JobStatus jobStatus) { this.jobStatus = jobStatus; }

    public Long getCompanyId() { return companyId; }

    public void setCompanyId(Long companyId) { this.companyId = companyId; }

    public String getCompany() { return company; }

    public void setCompany(String company) { this.company = company; }

    public Instant getPostingTime() { return postingTime; }

    public void setPostingTime(Instant postingTime) { this.postingTime = postingTime; }

    public Long getAmId() { return amId; }

    public void setAmId(Long amId) { this.amId = amId; }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Instant getLastModifiedDate() { return lastModifiedDate; }

    public void setLastModifiedDate(Instant lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; }

}
