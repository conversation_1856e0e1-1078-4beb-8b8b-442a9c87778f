package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * Enumeration of NodeType Association Data Table
 */
public enum ReportTableType implements ConvertedEnum<Integer> {

    SUBMIT_TO_JOB(10, 72, "talent_recruitment_process_submit_to_job"),

    SUBMIT_TO_CLIENT(20, 30 * 24,"talent_recruitment_process_submit_to_client"),

    INTERVIEW(30, 30 * 24,"talent_recruitment_process_interview"),

    OFFER(40, null,"talent_recruitment_process_offer"),

    OFFER_ACCEPT(41, null,"talent_recruitment_process_ipg_offer_accept"),

    ON_BOARD(60, null,"talent_recruitment_process_onboard"),

    RESIGNED(100, null,"talent_recruitment_process_onboard"),

    ELIMINATED(-1, null,"talent_recruitment_process_eliminate");

    private final Integer dbValue;

    //hours
    private final Integer dormantTime;

    private final String dbTableName;

    public static final List<Integer> ALL_NODE_TYPES = new ArrayList<>();

    static {
        for (ReportTableType nodeType: ReportTableType.values()) {
            ALL_NODE_TYPES.add(nodeType.toDbValue());
        }
    }

    ReportTableType(Integer dbValue, Integer dormantTime, String dbTableName) {
        this.dbValue = dbValue;
        this.dormantTime = dormantTime;
        this.dbTableName = dbTableName;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDbTableName() {
        return dbTableName;
    }

    public Integer getDormantTime() { return dormantTime; }

    public boolean hasDormantTime() { return dormantTime != null; }


    // static resolving:
    public static final ReverseEnumResolver<ReportTableType, Integer> resolver =
            new ReverseEnumResolver<>(ReportTableType.class, ReportTableType::toDbValue);

    public static ReportTableType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
