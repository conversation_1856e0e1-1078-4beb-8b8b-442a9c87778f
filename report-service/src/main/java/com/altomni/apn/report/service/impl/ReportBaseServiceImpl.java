package com.altomni.apn.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.enumeration.enums.ReportAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SpringUtil;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.report.config.env.ReportApiPromptProperties;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.ReportBaseService;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service("reportBaseService")
public class ReportBaseServiceImpl implements ReportBaseService {

    static final String AND_SYMBOL = " AND ";

    static List<Integer> NO_CHINA_LIST;

    static final List<Integer> CHINA_LIST = CollUtil.newArrayList(CurrencyConstants.CNY);

    static {
        initNoChinaList();
    }

    private static void initNoChinaList() {
        EnumCurrencyService enumCurrencyService = SpringUtil.getBean(EnumCurrencyService.class);
        List<EnumCurrency> allCurrencyList = enumCurrencyService.findAllEnumCurrency();
        NO_CHINA_LIST = allCurrencyList.stream().map(EnumCurrency::getId).filter(id -> !CHINA_LIST.contains(id)).collect(Collectors.toList());
    }

    static final String UNION_ALL = " union all ";

    static final String ALL_SELECT = "all";

    static final List<Long> IPG_COMPANY_IDS = Arrays.asList(30L, 51L, 934L ,10030L, 10051L, 10934L);

    static final String OFFER_ACCEPTED = "Offer Accepted";

    static final String ONBOARD = "Onboard";

    static final String TERMINATION = "Termination";

    static final String EXTENTION = "Extention";

    static final String START_FAIL_WARRANTY = "Failed Warranty";

    static final String JOB_ID = "jobId";
    static final String APPLICATION_TYPE = "applicationId";

    @Resource
    EntityManager entityManager;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ReportApiPromptProperties reportApiPromptProperties;

    @Resource
    protected ReportRepository reportRepository;

    public <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map, null);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchData(queryStr, clazz, paramMap, null);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map, Pageable pageable) {
        entityManager.clear();
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        if (pageable != null) {
            query.setFirstResult((pageable.getPageNumber() <= 0? 0: pageable.getPageNumber() - 1) * pageable.getPageSize());
            query.setMaxResults(pageable.getPageSize());
        }
        return query.getResultList();
    }

    public long doSearchCount(String countQuery, Map<Integer, Object> map) {
        entityManager.clear();
        Query countQ = entityManager.createNativeQuery(countQuery);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(countQ, method, k, v)));
        return Long.parseLong(String.valueOf(countQ.getSingleResult()));
    }

    private Integer checkInList(Map<Integer, Object> map) {
        if (CollUtil.isEmpty(map)) {
            return null;
        }
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORT_CHECKINLIST_CONDITIONLIMIT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
        return keyList.get(0);
    }

    public List<Map<String, Object>> searchDataWithMap(String queryStr, Map<Integer, Object> map) {
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchDataWithMap(queryStr, map);
        } else {
            return doPartitionSearchDataWithMap(key, queryStr, map);
        }
    }

    private List<Map<String, Object>> doPartitionSearchDataWithMap(Integer key, String queryStr, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchDataWithMap(queryStr, paramMap);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public List<Map<String, Object>> doSearchDataWithMap(String queryStr, Map<Integer, Object> map) {
        entityManager.clear();
        Query query = entityManager.createNativeQuery(queryStr);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.getResultList();
    }

    public <T> List<T> convertEntity(List<Map<String, Object>> mapList, Class<T> clazz) {
        if (CollUtil.isEmpty(mapList)) {
            new ArrayList<>();
        }
        return new JSONArray(mapList).stream().map(JSONUtil::parseObj)
                .map(json -> BeanUtil.mapToBean(json, clazz, true)).collect(Collectors.toList());
    }

}
