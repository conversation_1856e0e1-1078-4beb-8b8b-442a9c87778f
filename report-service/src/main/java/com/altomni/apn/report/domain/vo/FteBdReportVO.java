package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.util.List;

@Data
@Entity
public class FteBdReportVO {

    @Id
    @ExcelIgnore
    @Column(name = "id", nullable = false)
    private Long id;

    private String clientName;

    private String am;

    private String coAm;

    @Transient
    List<UserCountryVO> coAmList;

    private String salesLeadOwner;

    private String bdOwner;

    private Long jobOrders;

    private Long onboard;

    private Double billing;

    private Double cashIn;
}
