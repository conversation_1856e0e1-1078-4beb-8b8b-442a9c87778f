package com.altomni.apn.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentCategoryType;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetFrequencyType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.enumeration.enums.ReportAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.FutureExceptionUtil;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.constants.ReportTimeSheetConstants;
import com.altomni.apn.report.domain.enumeration.TimesheetSearchType;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.TimesheetByStatusSearchDto;
import com.altomni.apn.report.service.TimeSheetByStatusWithinAPeriodService;
import com.altomni.apn.report.util.ExcelUtil;
import com.altomni.apn.report.util.MapToEntityUtil;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@RefreshScope
@Service("timeSheetByStatusWithinAPeriodService")
public class TimeSheetByStatusWithinAPeriodServiceImpl extends ReportBaseServiceImpl implements TimeSheetByStatusWithinAPeriodService {

    /**
     * The maximum number of records that can be downloaded
     */
    @Value("${report.download.max:10000}")
    private long downloadDataMaxSize;

    @Value("${report.download.month.partition:3}")
    private int reportDownloadMonthPartition;

    @Value("${report.list.month.partition:3}")
    private int reportListMonthPartition;

    private static final ThreadFactory THREAD_FACTORY = new ThreadFactoryBuilder().setNameFormat("async-t3-report-pool-").build();

    public static final ExecutorService executorService = new ThreadPoolExecutor(Runtime.getRuntime().availableProcessors() * 6,
            Runtime.getRuntime().availableProcessors() * 12, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<>(100000), THREAD_FACTORY);

    @Override
    public Page<TimeSheetReportBaseVo> searchTimeSheetRecordByStatus(TimesheetByStatusSearchDto searchDto, Pageable pageable) throws ExecutionException, InterruptedException {
        checkParam(searchDto);
        return searchPageBySearchDto(searchDto, pageable);
    }

    @Override
    public void exportTimeSheetRecordSearchByStatus(TimesheetByStatusSearchDto searchDto, HttpServletResponse response) {
        checkParam(searchDto);
        //todo 2024-06-18 需要查询十几万的数据,应该会有问题,放开现在,需要用分析性数据库
//        checkLimit(searchDto);
        searchTimesheetRecordBySearchDtoForDownload(searchDto, response);
    }

    /**
     * 检查下载数据量是否超过限制
     * @param searchDto
     */
    private void checkLimit(TimesheetByStatusSearchDto searchDto) {
        long count = searchTimesheetCountForList(searchDto);
        if (count > downloadDataMaxSize) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORT_DOWNLOAD_DATA_MAX_SIZE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
    }

    private <T> void searchTimesheetRecordBySearchDtoForDownload(TimesheetByStatusSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("timeSheet download excel task");
        stopWatch.start("[1] search date for excel task");
        Class clazz;
        String fileName;
        if (ObjectUtil.isNotNull(searchDto.getTimeSheetStatus())) {
            switch (searchDto.getTimeSheetStatus()) {
                case MISSING -> {
                    clazz = TimeSheetReportWithMissing.class;
                    fileName = "timesheets_bystatus_missing";
                }
                case APPROVED -> {
                    clazz = TimeSheetReportWithApproved.class;
                    fileName = "timesheets_bystatus_approved";
                }
                case APPLIED_APPROVE -> {
                    clazz = TimeSheetReportWithPendingApproval.class;
                    fileName = "timesheets_bystatus_pending";
                }
                case REJECTED -> {
                    clazz = TimeSheetReportWithRejected.class;
                    fileName = "timesheets_bystatus_rejected";
                }
                default -> {
                    clazz = TimeSheetReportBaseVo.class;
                    fileName = "timesheets_bystatus_allstatus";
                }
            }
        } else {
            clazz = TimeSheetReportWithout.class;
            fileName = "timesheets_bystatus_allstatus";
        }
        SecurityContext securityContext = SecurityContextHolder.getContext();
        //date 查询范围切片, 防止index 失效
        List<TimesheetByStatusSearchDto> searchDtoList = generateDateRanges(searchDto.getPrimitiveFrom(), searchDto.getPrimitiveTo(), reportDownloadMonthPartition).stream().map(dateRange -> {
            TimesheetByStatusSearchDto timesheetByStatusSearchDto = ObjectUtil.cloneByStream(searchDto);
            timesheetByStatusSearchDto.setFrom(dateRange[0]);
            timesheetByStatusSearchDto.setTo(dateRange[1]);
            return timesheetByStatusSearchDto;
        }).toList();
        if (searchDtoList.size() > 6) {
            //防止数据量过大,导致切片太多,导致db 数过多
            searchDtoList = List.of(searchDto);
        }
        List<CompletableFuture<List<T>>> dataFutureList = new ArrayList<>();
        searchDtoList.forEach(search -> dataFutureList.add(CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            entityManager.clear();
            StopWatch dataListWatch = new StopWatch();
            dataListWatch.start();
            Map<Integer, Object> dataParamMap = new HashMap<>(16);
            String sql = getExcelDataSql(search, dataParamMap);
            List<T> voList = MapToEntityUtil.convertEntity(doSearchDataWithMap(sql, dataParamMap), clazz);
            dataListWatch.stop();
            log.info("[apn @{}] search data for excel download from = {}, to = {}, times = {}", SecurityUtils.getUserId(), search.getFrom(), search.getTo(), dataListWatch.getTotalTimeMillis());
            return voList;
        }, executorService)));
        //如果是 by day 需要 times 详细数据
        List<CompletableFuture<List<TimeSheetReportTimesForDay>>> timesFutureList = new ArrayList<>();
        if (BooleanUtil.isTrue(searchDto.getDisplayHoursPerDayFlag())
                && List.of(TimeSheetStatus.APPLIED_APPROVE,TimeSheetStatus.REJECTED,TimeSheetStatus.APPROVED).contains(searchDto.getTimeSheetStatus())) {
            searchDtoList.forEach(search -> timesFutureList.add(CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(securityContext);
                entityManager.clear();
                StopWatch dataListWatch = new StopWatch();
                dataListWatch.start();
                Map<Integer, Object> dataParamMap = new HashMap<>(16);
                String sql = getTimesByDaySql(search, dataParamMap);
                List<TimeSheetReportTimesForDay> voList = MapToEntityUtil.convertEntity(doSearchDataWithMap(sql, dataParamMap), TimeSheetReportTimesForDay.class);
                dataListWatch.stop();
                log.info("[apn @{}] search times for excel download from = {}, to = {}, times = {}", SecurityUtils.getUserId(), search.getFrom(), search.getTo(), dataListWatch.getTotalTimeMillis());
                return voList;
            }, executorService)));
        }

        List<CompletableFuture<List<TimeSheetCommonDataVo>>> amFutureList = new ArrayList<>();
        searchDtoList.forEach(search -> amFutureList.add(CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            entityManager.clear();
            StopWatch assignmentAmWatch = new StopWatch();
            assignmentAmWatch.start();
            Map<Integer, Object> assignmentParamMap = new HashMap<>(16);
            String assignmentAmSql = getAssignmentAmSql(search, assignmentParamMap);
            List<TimeSheetCommonDataVo> voList = MapToEntityUtil.convertEntity(doSearchDataWithMap(assignmentAmSql, assignmentParamMap), TimeSheetCommonDataVo.class);
            assignmentAmWatch.stop();
            log.info("[apn @{}] search assignmentAm for excel download from = {}, to = {}, times = {}", SecurityUtils.getUserId(), search.getFrom(), search.getTo(), assignmentAmWatch.getTotalTimeMillis());
            return voList;
        }, executorService)));

        List<CompletableFuture<List<TimeSheetCommonDataVo>>> rateFutureList = new ArrayList<>();
        searchDtoList.forEach(search -> rateFutureList.add(CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(securityContext);
            entityManager.clear();
            StopWatch assignmentAmWatch = new StopWatch();
            assignmentAmWatch.start();
            Map<Integer, Object> assignmentParamMap = new HashMap<>(16);
            String assignmentRateSql = getAssignmentRateSql(search, assignmentParamMap);
            List<TimeSheetCommonDataVo> voList = MapToEntityUtil.convertEntity(doSearchDataWithMap(assignmentRateSql, assignmentParamMap), TimeSheetCommonDataVo.class);
            assignmentAmWatch.stop();
            log.info("[apn @{}] search assignmentRate for excel download ,from = {}, to = {}, times = {}", SecurityUtils.getUserId() , search.getFrom(), search.getTo(), assignmentAmWatch.getTotalTimeMillis());
            return voList;
        }, executorService)));
        List<T> voList = new ArrayList<>();
        try {
            CompletableFuture<Void> allAmFuturesDone = CompletableFuture.allOf(amFutureList.toArray(CompletableFuture[]::new)).exceptionally(FutureExceptionUtil::handleFutureException);
            CompletableFuture<Void> allRateFuturesDone = CompletableFuture.allOf(rateFutureList.toArray(CompletableFuture[]::new)).exceptionally(FutureExceptionUtil::handleFutureException);
            //am 和 rate 的数据
            CompletableFuture<List<TimeSheetCommonDataVo>> amFuture = getAllListFuture(amFutureList, allAmFuturesDone);
            CompletableFuture<List<TimeSheetCommonDataVo>> rateFuture = getAllListFuture(rateFutureList, allRateFuturesDone);
            //开始组装 assignment 的公共数据
            CompletableFuture<Map<Long, TimeSheetCommonDataVo>> commonDateFuture = CompletableFuture.allOf(amFuture, rateFuture).thenApplyAsync(voidResult -> {
                //合并两个查询的结果
                Map<Long, TimeSheetCommonDataVo> assignmentAmMap = amFuture.join().stream().collect(Collectors.toMap(TimeSheetCommonDataVo::getAssignmentId, a -> a, (a1, a2) -> a1));
                Map<Long, TimeSheetCommonDataVo> assignmentRateMap = rateFuture.join().stream().collect(Collectors.toMap(TimeSheetCommonDataVo::getAssignmentId, a -> a, (a1, a2) -> a1));
                Map<Long, TimeSheetCommonDataVo> assignmentMap = new HashMap<>();
                Set<Long> assignmentSet = new HashSet<>(assignmentRateMap.keySet());
                assignmentSet.forEach(assignmentId-> {
                    TimeSheetCommonDataVo amVO = assignmentAmMap.get(assignmentId);
                    TimeSheetCommonDataVo rateVO = assignmentRateMap.get(assignmentId);
                    BeanUtil.copyProperties(rateVO, amVO, "email", "phone", "am", "dm", "ac", "recruiter");
                    assignmentMap.put(assignmentId, amVO);
                });
                return assignmentMap;
            }, executorService);
            //具体数据
            CompletableFuture<Void> allDataFuturesDone = CompletableFuture.allOf(dataFutureList.toArray(CompletableFuture[]::new)).exceptionally(FutureExceptionUtil::handleFutureException);
            CompletableFuture<List<T>> dataFuture = allDataFuturesDone.thenApplyAsync(v -> {
                Map<String, T> seen = new HashMap<>(16);
                dataFutureList.stream().map(CompletableFuture::join).flatMap(List::stream).forEach(vo -> {
                    JSONObject jsonObject = JSONUtil.parseObj(vo);
                    seen.putIfAbsent(jsonObject.getStr("id"), vo);
                });
                return new ArrayList<>(seen.values());
            }, executorService);
            voList = dataFuture.get();
            Map<String, String> idTimesMap = new HashMap<>(16);
            if (BooleanUtil.isTrue(searchDto.getDisplayHoursPerDayFlag())
                    && List.of(TimeSheetStatus.APPLIED_APPROVE,TimeSheetStatus.REJECTED,TimeSheetStatus.APPROVED).contains(searchDto.getTimeSheetStatus())) {
                // times 数据
                CompletableFuture<Void> allTimesFuturesDone = CompletableFuture.allOf(timesFutureList.toArray(CompletableFuture[]::new)).exceptionally(FutureExceptionUtil::handleFutureException);
                CompletableFuture<List<TimeSheetReportTimesForDay>> timesFuture = allTimesFuturesDone.thenApplyAsync(v -> {
                    Map<String, TimeSheetReportTimesForDay> seen = new HashMap<>(16);
                    timesFutureList.stream().map(CompletableFuture::join).flatMap(List::stream).forEach(vo -> seen.putIfAbsent(vo.getAssignmentId() + "-" + vo.getWorkDate(), vo));
                    return new ArrayList<>(seen.values());
                }, executorService);
                List<TimeSheetReportTimesForDay> timesList = timesFuture.get();
                if (CollUtil.isNotEmpty(timesList)) {
                    idTimesMap = timesList.stream().collect(Collectors.toMap(time -> time.getAssignmentId() + "-" + time.getWorkDate(), TimeSheetReportTimesForDay::getTimes, (a1, a2) -> a1));
                }
            }
            stopWatch.stop();
            stopWatch.start("[2] 拼装数据");
            Map<Long, TimeSheetCommonDataVo> map = commonDateFuture.get();
            Field[] fields = ReflectUtil.getFields(TimeSheetCommonDataVo.class);
            // 提前获取 idTimesMap 避免在并行流中频繁访问
            Map<String, String> finalIdTimesMap = new ConcurrentHashMap<>(idTimesMap);
            Map<Long, TimeSheetCommonDataVo> finalMap = new ConcurrentHashMap<>(map);
            Field timesField = ReflectUtil.getField(clazz, "times");
            voList.parallelStream().forEach(vo -> {
                JSONObject jsonObject = JSONUtil.parseObj(vo);
                Long assignmentId = jsonObject.getLong("assignmentId");
                String key = assignmentId + "-" + jsonObject.get("workDate");
                if (CollUtil.isNotEmpty(finalIdTimesMap) && finalIdTimesMap.containsKey(key)) {
                    ReflectUtil.setFieldValue(vo, timesField.getName(), finalIdTimesMap.get(key));
                }
                TimeSheetCommonDataVo commonDataVo = finalMap.get(assignmentId);
                for (Field field : fields) {
                    ReflectUtil.setFieldValue(vo, field.getName(), ReflectUtil.getFieldValue(commonDataVo, field));
                }
            });
        } catch (Exception e) {
            if (e instanceof NotFoundException) {
                log.error("There are no records that match the requirements");
                return;
            }
            log.error(" search timeSheet report is error, message = {}", ExceptionUtils.getStackTrace(e));
        }
        stopWatch.stop();
        stopWatch.start("[3] download timeSheet excel task");
        List<String> removeList = new ArrayList<>();
        if (!searchDto.getDisplayHoursPerDayFlag()) {
            removeList.add("workDate");
            removeList.add("timeIn");
            removeList.add("breakOut");
            removeList.add("breakIn");
            removeList.add("timeOut");
        }
        if (!searchDto.getChargeNumberFlag()) {
            removeList.add("chargeNumber");
        }
        if (!searchDto.getTvcNumberFlag()) {
            removeList.add("tvcNumber");
        }

        if (searchDto.getExceptionChecklist()) {
            ZipOutputStream zipstream = null;
            try {

                String downloadName = "timesheet.zip";
                HeaderUtil.setInvoiceHeader(response, downloadName, "application/x-zip-compressed");

                zipstream = new ZipOutputStream(response.getOutputStream());

                String timesheetFileName = fileName + "_" + searchDto.getPrimitiveFrom() + "_" + searchDto.getPrimitiveTo() + ".xlsx";
                ByteArrayOutputStream outputStream = ExcelUtil.writerExcel(ExcelUtil.getTableHeaders(clazz, removeList), ExcelUtil.convertToMap(voList, clazz, removeList), "", timesheetFileName, true);
                ZipEntry entry = new ZipEntry(timesheetFileName);
                zipstream.putNextEntry(entry);
                zipstream.write(outputStream.toByteArray());

                String googleHolidayFileName = "Google_Holiday_Exception_Checklist_Report.xlsx";
                ByteArrayOutputStream googleHolidayBos = searchTimesheetRecordBySearchDtoForDownloadGoogleHoliday(searchDto, googleHolidayFileName);
                zipstream.putNextEntry(new ZipEntry(googleHolidayFileName));
                zipstream.write(googleHolidayBos.toByteArray());

                zipstream.flush();
            } catch (Exception e) {
                log.error("invoice: print error,{}", e);
            } finally {
                try {
                    zipstream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        } else {
            ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(clazz, removeList), ExcelUtil.convertToMap(voList, clazz, removeList), "", fileName + "_" + searchDto.getPrimitiveFrom() + "_" + searchDto.getPrimitiveTo() + ".xlsx", true);
            stopWatch.stop();
            log.info(" exportTimeSheetRecordSearchByStatus time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        }

    }

    private ByteArrayOutputStream searchTimesheetRecordBySearchDtoForDownloadGoogleHoliday(TimesheetByStatusSearchDto searchDto,String fileName){
        searchDto.setDisplayHoursPerDayFlag(true);
        log.info("[downloadGoogleHoliday] start download google holiday,param:{}",JSONUtil.toJsonStr(searchDto));
        Map<Integer, Object> paramMap = new HashMap<>();
        String dataSql = """
                    select
                      concat(REPLACE(unix_timestamp(NOW(3)),'.',''),'-', RAND() * 10000) id,
                      tswer.full_name,
                      tsr.total_hours,
                      tgh.holiday_day,
                      tswer.talent_id,
                      tswer.job_id,
                      tswer.job_title
                    from time_sheet_record_report tsrr 
                    LEFT JOIN time_sheet_record tsr on tsrr.record_id = tsr.id
                    inner join timesheet_talent_assignment tta on tta.id = tsrr.assignment_id
                    INNER JOIN talent t ON t.id = tta.talent_id
                    inner JOIN time_sheet_week_ending_record tswer ON tswer.record_id = tsrr.week_id
                    inner join start s on s.id = tta.start_id
                    left join timesheet_google_holiday tgh on tgh.company_id = tswer.company_id 
                                and DATE_FORMAT(tsrr.work_date,'%Y-%m-%d') = DATE_FORMAT(tgh.holiday_day,'%Y-%m-%d')
                                and tgh.tenant_id={tenantId}
                    {conditionWhereAndOrder}
                    """;
        Map<String, String> map = new HashMap(16);
        map.put("conditionWhereAndOrder", getListConditionWhereAndOrderForDay(searchDto, paramMap, false));
        map.put("tenantId", SecurityUtils.getTenantId() + "");
        dataSql = StrUtil.format(dataSql, map);

        dataSql = "select sum(tab.total_hours) as total_hours,tab.full_name,tab.holiday_day,tab.job_id,tab.job_title,tab.id,talent_id from ("
                + dataSql
                + ") tab GROUP BY tab.full_name,tab.holiday_day,tab.job_id,tab.job_title,tab.id,talent_id";

        Query query = entityManager.createNativeQuery(dataSql, TimeSheetGoogleHolidayDataVo.class);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(paramMap).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        List<TimeSheetGoogleHolidayDataVo> voList = query.getResultList();
        List<TimeSheetReportWithGoogleHoliday> holidays = new ArrayList<>();
        Map<Long,List<TimeSheetGoogleHolidayDataVo>> voMapList = voList.stream().filter(a -> a.getId() != null).collect(Collectors.groupingBy(b -> b.getTalentId()));
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        voMapList.forEach((k, v) -> {

            v.forEach(z -> {
                if (z.getHolidayDay() != null && z.getTotalHours() != null) {
                    TimeSheetReportWithGoogleHoliday bean = new TimeSheetReportWithGoogleHoliday();
                    bean.setHolidayDate(z.getHolidayDay().format(fmt));
                    bean.setHoursRecorded(z.getTotalHours() + "");
                    bean.setCandidateId(z.getTalentId() + "");
                    bean.setJobID(z.getJobId() + "");
                    bean.setJobTitle(z.getJobTitle());
                    bean.setEmployeeName(z.getFullName());
                    holidays.add(bean);
                }
            });
        });
        if (!holidays.isEmpty()) {
            return ExcelUtil.writerExcel(ExcelUtil.getTableHeaders(TimeSheetReportWithGoogleHoliday.class, new ArrayList<>()), ExcelUtil.convertToMap(holidays, TimeSheetReportWithGoogleHoliday.class, new ArrayList<>()), "", fileName, true);
        }
        return null;
    }

    private CompletableFuture<List<TimeSheetCommonDataVo>> getAllListFuture(List<CompletableFuture<List<TimeSheetCommonDataVo>>> futureList, CompletableFuture<Void> allFuturesDone) {
        return allFuturesDone.thenApplyAsync(v -> {
            Map<Long, TimeSheetCommonDataVo> seen = new HashMap<>(16);
            futureList.stream().map(CompletableFuture::join).flatMap(List::stream).forEach(vo -> seen.putIfAbsent(vo.getAssignmentId(), vo));
            return new ArrayList<>(seen.values());
        });
    }

    private String getTimesByDaySql(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> dataParamMap) {
        String sql = """
                SELECT
                  tsr.assignment_id,
                  tsr.work_date,
                  GROUP_CONCAT(tbr.time) times
                FROM
                  timesheet_breaktime_record tbr
                  INNER JOIN time_sheet_record tsr ON tbr.assignment_id = tsr.assignment_id AND tbr.work_date = tsr.work_date
                  INNER JOIN time_sheet_week_ending_record tswer ON tswer.record_id = tsr.week_id
                  INNER JOIN timesheet_talent_assignment tta ON tta.id = tbr.assignment_id
                  INNER JOIN assignment_bill_info abi ON abi.assignment_id = tta.id
                  INNER JOIN assignment_pay_info api ON api.assignment_id = tta.id
                  INNER JOIN assignment_timesheet AT ON AT.assignment_id = tta.id
                {conditionWhere}
                """;
        Map<String, String> map = new HashMap<>(16);
        map.put("conditionWhere", getExcelTimesConditionWhereForDay(searchDto, dataParamMap));
        return StrUtil.format(sql, map);
    }

    private String getExcelTimesConditionWhereForDay(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> paramMap) {
        StringBuilder sbSql = new StringBuilder();
        sbSql.append(" where tta.tenant_id = ").append(SecurityUtils.getTenantId())
                .append(" and tta.status = 1 ")
                .append(" and tta.start_date <= now() ")
                .append(" and tbr.time is not null and tbr.time != '' ");
        paramMap.put(1, searchDto.getFrom());
        paramMap.put(2, searchDto.getTo());
        //search type
        if (searchDto.getType() != null) {
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.APPROVED_DATE)) {
                sbSql.append(" and tswer.approved_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sbSql.append(" and tsr.week_ending_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.HOURS_WORKED)) {
                sbSql.append(" and tsr.work_date BETWEEN ?1 and ?2 ");
            }
        }
        // companyId
        if (ObjectUtil.isNotNull(searchDto.getCompanyId())) {
            paramMap.put(paramMap.size() + 1, searchDto.getCompanyId());
            sbSql.append(" and tswer.company_id = ?").append(paramMap.size());
        }
        // timesheet status
        if (ObjectUtil.isNotNull(searchDto.getTimeSheetStatus())) {
            paramMap.put(paramMap.size() + 1, searchDto.getTimeSheetStatus().toDbValue());
            sbSql.append(" and tsr.status = ?").append(paramMap.size());
        }
        //employment category
        if (CollUtil.isNotEmpty(searchDto.getAssignmentCategoryType())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentCategoryType().stream().map(AssignmentCategoryType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tta.employment_category in ?").append(paramMap.size());
        }
        // billing frequency
        if (CollUtil.isNotEmpty(searchDto.getBillingFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getBillingFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and at.frequency in ?").append(paramMap.size());
        }
        // paymentfrequency
        if (CollUtil.isNotEmpty(searchDto.getPayFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getPayFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and api.frequency in ?").append(paramMap.size());
        }
        // assignment division
        if (CollUtil.isNotEmpty(searchDto.getAssignmentDivision())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentDivision().stream().map(AssignmentDivision::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and abi.assignment_division in ?").append(paramMap.size());
        }
        // group by
        sbSql.append(" group by tsr.assignment_id, tsr.work_date ");
        return sbSql.toString();
    }

    private String getAssignmentRateSql(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> assignmentParamMap) {
        String sql = """
                SELECT
                	tsr.assignment_id,
                	MAX( CASE WHEN apr.pay_type = 2 AND apr.content_type = 0 THEN apr.pay_rate END ) bill_rate,
                	MAX( CASE WHEN apr.pay_type = 2 AND apr.content_type = 0 THEN apr.time_unit END ) bill_time_unit,
                	MAX( CASE WHEN apr.pay_type = 2 AND apr.content_type = 0 THEN ec.symbol END ) AS bill_currency,
                	MAX( CASE WHEN apr.pay_type = 3 AND apr.content_type = 1 THEN apr.pay_rate END ) pay_rate,
                	MAX( CASE WHEN apr.pay_type = 3 AND apr.content_type = 1 THEN apr.time_unit END ) pay_time_unit,
                	MAX( CASE WHEN apr.pay_type = 3 AND apr.content_type = 1 THEN ec.symbol END ) AS pay_currency,
                	MAX( CASE WHEN apr.pay_type = 0 AND apr.content_type = 0 THEN apr.pay_rate END ) over_bill_rate,
                	MAX( CASE WHEN apr.pay_type = 0 AND apr.content_type = 0 THEN apr.time_unit END ) over_bill_time_unit,
                	MAX( CASE WHEN apr.pay_type = 0 AND apr.content_type = 0 THEN ec.symbol END ) AS over_bill_currency,
                	MAX( CASE WHEN apr.pay_type = 0 AND apr.content_type = 1 THEN apr.pay_rate END ) over_pay_rate,
                	MAX( CASE WHEN apr.pay_type = 0 AND apr.content_type = 1 THEN apr.time_unit END ) over_pay_time_unit,
                	MAX( CASE WHEN apr.pay_type = 0 AND apr.content_type = 1 THEN ec.symbol END ) AS over_pay_currency,
                	MAX( CASE WHEN apr.pay_type = 1 AND apr.content_type = 0 THEN apr.pay_rate END ) double_bill_rate,
                	MAX( CASE WHEN apr.pay_type = 1 AND apr.content_type = 0 THEN apr.time_unit END ) double_bill_time_unit,
                	MAX( CASE WHEN apr.pay_type = 1 AND apr.content_type = 0 THEN ec.symbol END ) AS double_bill_currency,
                	MAX( CASE WHEN apr.pay_type = 1 AND apr.content_type = 1 THEN apr.pay_rate END ) double_pay_rate,
                	MAX( CASE WHEN apr.pay_type = 1 AND apr.content_type = 1 THEN apr.time_unit END ) double_pay_time_unit,
                	MAX( CASE WHEN apr.pay_type = 1 AND apr.content_type = 1 THEN ec.symbol END ) AS double_pay_currency
                FROM
                	timesheet_talent_assignment tta
                	INNER JOIN time_sheet_record tsr ON tta.id = tsr.assignment_id
                	INNER JOIN time_sheet_week_ending_record tswer ON tswer.record_id = tsr.week_id
                	LEFT JOIN assignment_pay_rate apr ON apr.assignment_id = tta.id
                	LEFT JOIN enum_currency ec ON ec.id = apr.currency
                {conditionWhere}
                GROUP BY
                	tta.id
                """;
        Map<String, String> map = new HashMap<>(16);
        map.put("conditionWhere", appendWhereBySearchType(searchDto, assignmentParamMap));
        return StrUtil.format(sql, map);
    }


    private String getAssignmentAmSql(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> assignmentParamMap) {
        String sql = """
                SELECT
                	tsr.assignment_id,
                	SUBSTRING_INDEX( GROUP_CONCAT( DISTINCT CASE WHEN tc.jhi_type = 2 THEN tc.contact END ORDER BY sort ASC ), ",", 1 ) email,
                	SUBSTRING_INDEX( GROUP_CONCAT( DISTINCT CASE WHEN tc.jhi_type = 16 THEN tc.contact END ORDER BY sort ASC ), ",", 1 ) phone,
                	GROUP_CONCAT( DISTINCT CASE WHEN ac.user_role = 0 THEN CONCAT( u.first_name, " ", u.last_name ) END ) am,
                	GROUP_CONCAT( DISTINCT CASE WHEN ac.user_role = 3 THEN CONCAT( u.first_name, " ", u.last_name ) END ) dm,
                	GROUP_CONCAT( DISTINCT CASE WHEN ac.user_role = 5 THEN CONCAT( u.first_name, " ", u.last_name ) END ) ac,
                	GROUP_CONCAT( DISTINCT CASE WHEN ac.user_role = 1 THEN CONCAT( u.first_name, " ", u.last_name ) END ) recruiter
                FROM
                	timesheet_talent_assignment tta
                	INNER JOIN time_sheet_record tsr ON tta.id = tsr.assignment_id
                	INNER JOIN time_sheet_week_ending_record tswer ON tswer.record_id = tsr.week_id
                	LEFT JOIN assignment_contribution ac ON ac.assignment_id = tta.id
                	LEFT JOIN USER u ON u.id = ac.user_id
                	LEFT JOIN talent_contact tc ON tc.talent_id = tta.talent_id AND tc.STATUS = 0
                {conditionWhere}
                GROUP BY
                	tta.id
                """;
        Map<String, String> map = new HashMap<>(16);
        map.put("conditionWhere", appendWhereBySearchType(searchDto, assignmentParamMap));
        return StrUtil.format(sql, map);
    }

    private String getExcelDataSql(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> paramMap) {
        String sql;
        if (BooleanUtil.isTrue(searchDto.getDisplayHoursPerDayFlag())) {
            sql = """
                    SELECT
                    concat(tsrr.assignment_id, tsrr.work_date) id,
                    tsrr.assignment_id,
                    tswer.full_name,
                    t.first_name,
                    t.last_name,
                    tsrr.week_ending_date week_ending_date,
                    tswer.company_name company,
                    cslcct.full_name billing_contact,
                    j.id job_id,
                    abi.net_bill_Rate net_bill_rate,
                    tsr.last_modified_date,
                    tswer.last_modified_name last_modified_by,
                    j.title job_title,
                    j.CODE job_code,
                    al.city working_city,
                    al.province working_state,
                    al.country working_country,
                    tswer.assignment_division,
                    {conditionFiled}
                    tswer.approved_date approved_on,
                  IF
                    ( tswer.am_approver_id IS NULL, IF ( tswer.manager_id IS NULL, tswer.primary_manager, tswer.manager ), CONCAT(approverUser.first_name, ' ', approverUser.last_name) ) approver_name,
                    approverUser.email approver_email,
                    approverUser.phone approver_phone,
                    primaryuser.email primary_email,
                    tswer.primary_phone primary_phone,
                    manageruser.email manager_email,
                    tswer.manager_phone manager_phone,
                    api.corporation corporation,
                  IF
                    ( st.id IS NULL, tta.employment_category, concat('Past', "-", tta.employment_category) ) employee_status,
                    api.frequency payment_frequency,
                    AT.frequency billing_frequency,
                    tc.comments employee_comments,
                    tarr.opinion approver_comments,
                    tsrr.status,
                    tsrr.work_date work_date
                  FROM
                    time_sheet_record_report tsrr
                    left join time_sheet_record tsr on tsrr.record_id = tsr.id
                    INNER JOIN time_sheet_week_ending_record tswer ON tswer.record_id = tsrr.week_id
                    INNER JOIN timesheet_talent_assignment tta ON tta.id = tsrr.assignment_id
                    inner join talent t on t.id = tta.talent_id
                    INNER JOIN job j ON j.id = tta.job_id
                    INNER JOIN assignment_location al ON al.assignment_id = tta.id
                    INNER JOIN assignment_bill_info abi ON abi.assignment_id = tta.id
                    INNER JOIN START s ON s.id = tta.start_id
                    INNER JOIN assignment_pay_info api ON api.assignment_id = tta.id
                    INNER JOIN assignment_timesheet AT ON AT.assignment_id = tta.id
                    LEFT JOIN company_sales_lead_client_contact cslcc ON cslcc.id = abi.contact_id
                    LEFT JOIN talent cslcct ON cslcct.id = cslcc.talent_id
                    LEFT JOIN USER approverUser ON approverUser.id = tswer.am_approver_id
                    LEFT JOIN company_sales_lead_client_contact cslccapp ON cslccapp.id = tswer.primary_manager_id
                    LEFT JOIN time_sheet_user primaryuser ON primaryuser.id = cslccapp.approver_id
                    LEFT JOIN company_sales_lead_client_contact manager ON manager.id = tswer.manager_id
                    LEFT JOIN time_sheet_user manageruser ON manageruser.id = manager.approver_id
                    LEFT JOIN start_termination st ON st.start_id = tta.start_id
                    LEFT JOIN timesheet_comments tc ON tc.assignment_id = tswer.assignment_id AND tc.work_date = tswer.week_end AND tc.comments_type = 0
                    LEFT JOIN timesheet_approve_record tarr ON tarr.record_id = tswer.record_id
                    left join timesheet_google_holiday tgh on tgh.company_id = tswer.company_id 
                            and DATE_FORMAT(tsrr.work_date,'%Y-%m-%d') = DATE_FORMAT(tgh.holiday_day,'%Y-%m-%d')
                            and tgh.tenant_id={tenantId}
                    {conditionWhere}
                    """ ;
            Map<String, String> map = new HashMap<>(16);
            map.put("conditionFiled", getExcelConditionField(searchDto));
            map.put("tenantId", SecurityUtils.getTenantId() + "");
            map.put("conditionWhere", getExcelConditionWhereForDay(searchDto, paramMap));
            sql = StrUtil.format(sql, map);
        } else {
            sql = """
                    SELECT
                    tswer.record_id id,
                    tswer.assignment_id assignment_id,
                    tswer.full_name,
                    t.first_name,
                    t.last_name,
                    tswer.company_name company,
                    cslcct.full_name billing_contact,
                    tswer.job_id job_id,
                    abi.net_bill_Rate net_bill_rate,
                    tswer.last_modified_date,
                    tswer.last_modified_name last_modified_by,
                    j.title job_title,
                    j.`code` job_code,
                    al.city working_city,
                    al.province working_state,
                    al.country working_country,
                    tswer.assignment_division,
                    {conditionFiled}
                    tswer.approved_date approved_on,
                  IF
                    ( tswer.am_approver_id IS NULL, IF ( tswer.manager_id IS NULL, tswer.primary_manager, tswer.manager ), CONCAT(approverUser.first_name, ' ', approverUser.last_name) ) approver_name,
                    approverUser.email approver_email,
                    approverUser.phone approver_phone,
                    primaryuser.email primary_email,
                    tswer.primary_phone primary_phone,
                    manageruser.email manager_email,
                    tswer.manager_phone manager_phone,
                    api.corporation corporation,
                  IF
                    ( st.id IS NULL, tta.employment_category, concat('Past', "-", tta.employment_category) ) employee_status,
                    tswer.status,
                    api.frequency payment_frequency,
                    AT.frequency billing_frequency,
                    tc.comments employee_comments,
                    tarr.opinion approver_comments,
                    tswer.work_date work_date
                  FROM
                    {conditionTable}
                    INNER JOIN job j ON j.id = tswer.job_id
                    INNER JOIN timesheet_talent_assignment tta ON tta.id = tswer.assignment_id
                    inner join talent t on t.id = tta.talent_id
                    INNER JOIN assignment_bill_info abi ON abi.assignment_id = tta.id
                    INNER JOIN assignment_location al ON al.assignment_id = tta.id
                    INNER JOIN START s ON s.id = tta.start_id
                    INNER JOIN assignment_pay_info api ON api.assignment_id = tta.id
                    INNER JOIN assignment_timesheet AT ON AT.assignment_id = tta.id
                    LEFT JOIN company_sales_lead_client_contact cslcc ON cslcc.id = abi.contact_id
                    LEFT JOIN talent cslcct ON cslcct.id = cslcc.talent_id
                    LEFT JOIN USER approverUser ON approverUser.id = tswer.am_approver_id
                    LEFT JOIN company_sales_lead_client_contact cslccapp ON cslccapp.id = tswer.primary_manager_id
                    LEFT JOIN time_sheet_user primaryuser ON primaryuser.id = cslccapp.approver_id
                    LEFT JOIN company_sales_lead_client_contact manager ON manager.id = tswer.manager_id
                    LEFT JOIN time_sheet_user manageruser ON manageruser.id = manager.approver_id
                    LEFT JOIN start_termination st ON st.start_id = tta.start_id
                    LEFT JOIN timesheet_comments tc ON tc.assignment_id = tswer.assignment_id AND tc.work_date = tswer.week_end AND tc.comments_type = 0
                    LEFT JOIN timesheet_approve_record tarr ON tarr.record_id = tswer.record_id
                    left join timesheet_google_holiday tgh on tgh.company_id = tswer.company_id 
                            and DATE_FORMAT(tsrr.work_date,'%Y-%m-%d') = DATE_FORMAT(tgh.holiday_day,'%Y-%m-%d')
                            and tgh.tenant_id={tenantId}
                    {conditionWhere}
                    """;
            Map<String, String> map = new HashMap<>(16);
            map.put("conditionFiled", getExcelConditionField(searchDto));
            map.put("conditionTable", getExcelConditionTable(searchDto));
            map.put("tenantId", SecurityUtils.getTenantId() + "");
            map.put("conditionWhere", getExcelConditionWhereForWeek(searchDto, paramMap));
            sql = StrUtil.format(sql, map);
        }
        return sql;
    }

    private String getExcelConditionTable(TimesheetByStatusSearchDto searchDto) {
        if (Objects.equals(searchDto.getType(), TimesheetSearchType.HOURS_WORKED)) {
            return " time_sheet_record_report tsrr " +
                    " left join time_sheet_record tsr on tsrr.record_id = tsr.id " +
                    " inner join time_sheet_week_ending_record tswer on tswer.record_id = tsrr.week_id ";
        } else {
            return " time_sheet_week_ending_record tswer " +
                    " left join time_sheet_record_report tsrr on tswer.record_id = tsrr.week_id " +
                    " left join time_sheet_record tsr on  tsrr.record_id = tsr.id ";
        }
    }

    private String getExcelConditionWhereForWeek(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> paramMap) {
        StringBuilder sbSql = new StringBuilder();
        paramMap.put(1, searchDto.getFrom());
        paramMap.put(2, searchDto.getTo());
        if (Objects.equals(searchDto.getType(), TimesheetSearchType.HOURS_WORKED)) {
            sbSql.append(" where tta.tenant_id = ").append(SecurityUtils.getTenantId())
                    .append(" and tta.status = 1 ")
                    .append(" and tta.start_date <= now() ");
            sbSql.append(" and tsr.work_date BETWEEN ?1 and ?2 ");
        } else {
            sbSql.append(" where tswer.tenant_id = ").append(SecurityUtils.getTenantId())
                    .append(" and tswer.assignment_status = 1 ")
                    .append(" and tswer.start_date <= now() ");
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.APPROVED_DATE)) {
                sbSql.append(" and tswer.approved_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sbSql.append(" and tswer.week_ending_date BETWEEN ?1 and ?2 ");
            }
        }

        // companyId
        if (ObjectUtil.isNotNull(searchDto.getCompanyId())) {
            paramMap.put(paramMap.size() + 1, searchDto.getCompanyId());
            sbSql.append(" and tswer.company_id = ?").append(paramMap.size());
        }
        // timesheet status
        if (ObjectUtil.isNotNull(searchDto.getTimeSheetStatus())) {
            paramMap.put(paramMap.size() + 1, searchDto.getTimeSheetStatus().toDbValue());
            sbSql.append(" and tswer.status = ?").append(paramMap.size());
        }
        //employment category
        if (CollUtil.isNotEmpty(searchDto.getAssignmentCategoryType())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentCategoryType().stream().map(AssignmentCategoryType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tta.employment_category in ?").append(paramMap.size());
        }
        // billing frequency
        if (CollUtil.isNotEmpty(searchDto.getBillingFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getBillingFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and at.frequency in ?").append(paramMap.size());
        }
        // paymentfrequency
        if (CollUtil.isNotEmpty(searchDto.getPayFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getPayFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and api.frequency in ?").append(paramMap.size());
        }
        // assignment division
        if (CollUtil.isNotEmpty(searchDto.getAssignmentDivision())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentDivision().stream().map(AssignmentDivision::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and abi.assignment_division in ?").append(paramMap.size());
        }
        // group by
        if (Objects.equals(searchDto.getType(), TimesheetSearchType.HOURS_WORKED)) {
            sbSql.append(" group by tsrr.assignment_id, tsrr.work_date ");
        } else {
            sbSql.append(" group by tswer.assignment_id, tswer.week_end ");
        }
        return sbSql.toString();
    }

    private String getExcelConditionWhereForDay(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> paramMap) {
        StringBuilder sbSql = new StringBuilder();
        sbSql.append(" where tta.tenant_id = ").append(SecurityUtils.getTenantId())
                .append(" and tta.status = 1 ")
                .append(" and tta.start_date <= now() ");

        paramMap.put(1, searchDto.getFrom());
        paramMap.put(2, searchDto.getTo());
        //search type
        if (searchDto.getType() != null) {
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.APPROVED_DATE)) {
                sbSql.append(" and tswer.approved_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sbSql.append(" and tsrr.week_ending_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.HOURS_WORKED)) {
                sbSql.append(" and tsrr.work_date BETWEEN ?1 and ?2 ");
            }
        }
        // companyId
        if (ObjectUtil.isNotNull(searchDto.getCompanyId())) {
            paramMap.put(paramMap.size() + 1, searchDto.getCompanyId());
            sbSql.append(" and tswer.company_id = ?").append(paramMap.size());
        }
        // timesheet status
        if (ObjectUtil.isNotNull(searchDto.getTimeSheetStatus())) {
            paramMap.put(paramMap.size() + 1, searchDto.getTimeSheetStatus().toDbValue());
            sbSql.append(" and tsrr.status = ?").append(paramMap.size());
        }
        //employment category
        if (CollUtil.isNotEmpty(searchDto.getAssignmentCategoryType())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentCategoryType().stream().map(AssignmentCategoryType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tta.employment_category in ?").append(paramMap.size());
        }
        // billing frequency
        if (CollUtil.isNotEmpty(searchDto.getBillingFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getBillingFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and at.frequency in ?").append(paramMap.size());
        }
        // paymentfrequency
        if (CollUtil.isNotEmpty(searchDto.getPayFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getPayFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and api.frequency in ?").append(paramMap.size());
        }
        // assignment division
        if (CollUtil.isNotEmpty(searchDto.getAssignmentDivision())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentDivision().stream().map(AssignmentDivision::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and abi.assignment_division in ?").append(paramMap.size());
        }
        // group by
        sbSql.append(" group by tsrr.assignment_id, tsrr.work_date ");
        return sbSql.toString();
    }

    private String getExcelConditionField(TimesheetByStatusSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        sb.append("'").append(searchDto.getTimeZone()).append("'").append(" timeZone, ");
        if (Boolean.TRUE.equals(searchDto.getChargeNumberFlag())) {
            sb.append(" s.charge_number, ");
        }
        if (Boolean.TRUE.equals(searchDto.getTvcNumberFlag())) {
            sb.append(" s.tvc_number, ");
        }
        if (BooleanUtil.isTrue(searchDto.getDisplayHoursPerDayFlag())) {
            sb.append("""
                        sum( tsr.regular_hours ) regular_hours,
                        sum( tsr.over_time ) over_time,
                        sum( tsr.double_time ) double_time,
                        (IFNULL(sum( tsr.total_hours ),0) + IFNULL(sum( tgh.hours ),0)) total_hours,
                        sum( tgh.hours )  holiday_hours,
                        """);
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sb.append(" tsrr.week_ending_date week_end, ");
            } else {
                sb.append(" tsrr.week_end week_end, ");
            }
        } else {
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.HOURS_WORKED)) {
                sb.append("""
                        sum( tsr.regular_hours ) regular_hours,
                        sum( tsr.over_time ) over_time,
                        sum( tsr.double_time ) double_time,
                        (IFNULL(sum( tsr.total_hours ),0) + IFNULL(sum( tgh.hours ),0)) total_hours,
                        sum( tgh.hours )  holiday_hours,
                        """);
            } else {
                sb.append("""
                        tswer.regular_hours regular_hours,
                        tswer.over_time over_time,
                        tswer.double_time double_time,
                        (IFNULL(tswer.total_hours,0) + IFNULL(sum( tgh.hours ),0)) total_hours,
                        sum( tgh.hours )  holiday_hours,
                        """);
            }
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sb.append(" tswer.week_ending_date week_end, ");
            } else {
                sb.append(" tswer.week_end week_end, ");
            }
        }
        return sb.toString();
    }

    private Page<TimeSheetReportBaseVo> searchPageBySearchDto(TimesheetByStatusSearchDto searchDto, Pageable pageable) throws ExecutionException, InterruptedException {
        //limit 的时候不做切割防止数据有问题
        List<TimesheetByStatusSearchDto> searchDtoList = generateDateRanges(searchDto.getPrimitiveFrom(), searchDto.getPrimitiveTo(), 10000).stream().map(dateRange -> {
            TimesheetByStatusSearchDto timesheetByStatusSearchDto = ObjectUtil.cloneByStream(searchDto);
            timesheetByStatusSearchDto.setFrom(dateRange[0]);
            timesheetByStatusSearchDto.setTo(dateRange[1]);
            return timesheetByStatusSearchDto;
        }).toList();
       /* if (searchDtoList.size() > 6) {
            //防止数据量过大,导致切片太多,导致db 数过多
            searchDtoList = List.of(searchDto);
        }*/
        SecurityContext context = SecurityContextHolder.getContext();
        List<CompletableFuture<List<TimeSheetReportBaseVo>>> dataFutureList = new ArrayList<>();
        searchDtoList.forEach(search -> dataFutureList.add(CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return searchTimesheetDataForList(search, pageable);
        })));
        List<CompletableFuture<Long>> countFutureList = new ArrayList<>();
        searchDtoList.forEach(search -> countFutureList.add(CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return searchTimesheetCountForList(search);
        })));

        // 动态构建比较器
        Comparator<TimeSheetReportBaseVo> comparator = buildComparator(searchDto.getSort());

        CompletableFuture<List<TimeSheetReportBaseVo>> dataFuture = CompletableFuture.allOf(dataFutureList.toArray(CompletableFuture[]::new))
                .thenApplyAsync(v -> dataFutureList.stream().map(CompletableFuture::join)
                        .flatMap(List::stream)
                        .sorted(comparator)
                        .limit(pageable.getPageSize()).toList());
        CompletableFuture<Long> countFuture = CompletableFuture.allOf(countFutureList.toArray(CompletableFuture[]::new))
                .thenApplyAsync(v -> countFutureList.stream().map(CompletableFuture::join).reduce(Long::sum).orElse(0L));
        return new PageImpl<>(dataFuture.get(), Pageable.unpaged(), countFuture.get());
    }

    private static Comparator<TimeSheetReportBaseVo> buildComparator(SearchSortDTO searchSortDTO) {
        if (searchSortDTO == null || searchSortDTO.getProperty() == null || searchSortDTO.getDirection() == null) {
            searchSortDTO = new SearchSortDTO("ASC", "fullName");
        }
        String property = searchSortDTO.getProperty();
        boolean isAscending = "ASC".equalsIgnoreCase(searchSortDTO.getDirection());
        Method method = ReflectUtil.getMethodByNameIgnoreCase(TimeSheetReportBaseVo.class, "get" + property);

        Function<TimeSheetReportBaseVo, Comparable> getter = entity -> (Comparable) ReflectUtil.invoke(entity, method);

        if (isAscending) {
            return Comparator.comparing(getter, Comparator.nullsLast(Comparator.naturalOrder()));
        } else {
            return Comparator.comparing(getter, Comparator.nullsLast(Comparator.reverseOrder()));
        }
    }


    private long searchTimesheetCountForList(TimesheetByStatusSearchDto searchDto) {
        StopWatch searchCountWatch = new StopWatch();
        searchCountWatch.start();
        Map<Integer, Object> paramMap = new HashMap<>();
        String countSql;
        if (BooleanUtil.isTrue(searchDto.getDisplayHoursPerDayFlag())) {
            countSql = """
                    select count(1) from (select
                    count(1)
                    from time_sheet_record_report tsrr 
                    LEFT JOIN time_sheet_record tsr on tsrr.record_id = tsr.id
                    inner join timesheet_talent_assignment tta on tta.id = tsrr.assignment_id
                    INNER JOIN talent t ON t.id = tta.talent_id
                    inner JOIN time_sheet_week_ending_record tswer ON tswer.record_id = tsrr.week_id
                    {conditionWhereAndOrder}
                    ) a
                    """;
            Map<String, String> map = new HashMap(16);
            map.put("conditionFiled", getListConditionFiled(searchDto));
            map.put("conditionWhereAndOrder", getListConditionWhereAndOrderForDay(searchDto, paramMap, true));
            countSql = StrUtil.format(countSql, map);
        } else {
            countSql = """
                    select count(1) from (select count(1)
                    from time_sheet_record_report tsrr
                    LEFT JOIN time_sheet_record tsr on tsrr.record_id = tsr.id
                    inner join timesheet_talent_assignment tta on tta.id = tsrr.assignment_id
                    INNER JOIN talent t ON t.id = tta.talent_id
                    INNER JOIN time_sheet_week_ending_record tswer ON tswer.record_id = tsrr.week_id
                    {conditionWhereAndOrder}
                    ) a
                    """;
            Map<String, String> map = new HashMap(16);
            map.put("conditionFiled", getListConditionFiled(searchDto));
            map.put("conditionWhereAndOrder", getListConditionWhereAndOrderForWeek(searchDto, paramMap, true));
            countSql = StrUtil.format(countSql, map);
        }
        entityManager.clear();
        Query countQ = entityManager.createNativeQuery(countSql);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(paramMap).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(countQ, method, k, v)));
        Long count = Long.parseLong(String.valueOf(countQ.getSingleResult()));
        searchCountWatch.stop();
        log.info("[apn @{}] search t3 report request to count from = {}, to = {}, time = {}", SecurityUtils.getUserId(), searchDto.getFrom(), searchDto.getTo(),  searchCountWatch.getTotalTimeMillis());
        return count;
    }

    private List<TimeSheetReportBaseVo> searchTimesheetDataForList(TimesheetByStatusSearchDto searchDto, Pageable pageable) {
        StopWatch searchListWatch = new StopWatch();
        searchListWatch.start();
        Map<Integer, Object> paramMap = new HashMap<>();
        String dataSql;
        if (BooleanUtil.isTrue(searchDto.getDisplayHoursPerDayFlag())) {
            //按照每天来处理和按照hours work 来处理的数据,必须是根据每天的work 来查询,无法直接使用聚合表
            dataSql = """
                    select
                    tswer.full_name,
                    tswer.company_name company,
                    tsr.regular_hours  regular_hours, 
                    tsr.over_time over_time, 
                    tsr.double_time double_time, 
                    tsr.total_hours total_hours, 
                    tsrr.week_ending_date week_ending_date,
                    {conditionFiled}
                    tsrr.work_date, 
                    concat(tsrr.assignment_id, tsrr.work_date) id,
                    tgh.hours as holiday_hours
                    from time_sheet_record_report tsrr 
                    LEFT JOIN time_sheet_record tsr on tsrr.record_id = tsr.id
                    inner join timesheet_talent_assignment tta on tta.id = tsrr.assignment_id
                    INNER JOIN talent t ON t.id = tta.talent_id
                    inner JOIN time_sheet_week_ending_record tswer ON tswer.record_id = tsrr.week_id
                    inner join start s on s.id = tta.start_id
                    left join timesheet_google_holiday tgh on tgh.company_id = tswer.company_id 
                                and DATE_FORMAT(tsrr.work_date,'%Y-%m-%d') = DATE_FORMAT(tgh.holiday_day,'%Y-%m-%d')
                                and tgh.tenant_id={tenantId}
                    {conditionWhereAndOrder}
                    """;
            Map<String, String> map = new HashMap(16);
            map.put("conditionFiled", getListConditionFiled(searchDto));
            map.put("conditionWhereAndOrder", getListConditionWhereAndOrderForDay(searchDto, paramMap, false));
            map.put("tenantId", SecurityUtils.getTenantId() + "");
            dataSql = StrUtil.format(dataSql, map);
        } else {
            dataSql = """
                    select
                    tswer.full_name,
                    tswer.company_name company,
                    sum(tsr.regular_hours) regular_hours, 
                    sum(tsr.over_time) over_time, 
                    sum(tsr.double_time) double_time, 
                    sum(tsr.total_hours) total_hours,
                    tsrr.week_ending_date , 
                    {conditionFiled}
                    tsrr.work_date, 
                    concat(tsrr.assignment_id, tsrr.work_date) id,
                    sum(tgh.hours) holiday_hours
                    from time_sheet_record_report tsrr
                    LEFT JOIN time_sheet_record tsr on tsrr.record_id = tsr.id
                    inner join timesheet_talent_assignment tta on tta.id = tsrr.assignment_id
                    INNER JOIN talent t ON t.id = tta.talent_id
                    INNER JOIN time_sheet_week_ending_record tswer ON tswer.record_id = tsrr.week_id
                    inner join start s on s.id = tta.start_id
                    left join timesheet_google_holiday tgh on tgh.company_id = tswer.company_id 
                            and DATE_FORMAT(tsrr.work_date,'%Y-%m-%d') = DATE_FORMAT(tgh.holiday_day,'%Y-%m-%d')
                            and tgh.tenant_id={tenantId}
                    {conditionWhereAndOrder}
                    """;
            Map<String, String> map = new HashMap(16);
            map.put("conditionFiled", getListConditionFiled(searchDto));
            map.put("conditionWhereAndOrder", getListConditionWhereAndOrderForWeek(searchDto, paramMap, false));
            map.put("tenantId", SecurityUtils.getTenantId() + "");
            dataSql = StrUtil.format(dataSql, map);
        }
        Query query = entityManager.createNativeQuery(dataSql, TimeSheetReportBaseVo.class);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(paramMap).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        if (pageable != null) {
            query.setFirstResult((pageable.getPageNumber() <= 0? 0: pageable.getPageNumber() - 1) * pageable.getPageSize());
            query.setMaxResults(pageable.getPageSize());
        }
        List<TimeSheetReportBaseVo> voList = query.getResultList();
        if(!voList.isEmpty()){
            voList.forEach(v->{
                if (v.getHolidayHours() != null) {
                    if (v.getTotalHours() != null) {
                        v.setTotalHours(v.getTotalHours() + v.getHolidayHours().floatValue());
                    } else {
                        v.setTotalHours(v.getHolidayHours().floatValue());
                    }
                } else {
                    v.setHolidayHours(0.0);
                }
            });
        }
        searchListWatch.stop();
        log.info("[apn @{}] search t3 report request to list from = {}, to = {}, time = {}", SecurityUtils.getUserId(), searchDto.getFrom(), searchDto.getTo(), searchListWatch.getTotalTimeMillis());
        return voList;
    }

    private String getListConditionWhereAndOrderForWeek(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> paramMap, boolean isCountFlag) {
        StringBuilder sbSql = new StringBuilder();
        sbSql.append(" where tta.tenant_id = ").append(SecurityUtils.getTenantId())
                .append(" and tta.status = 1 ")
                .append(" and tta.start_date <= now() ");

        paramMap.put(1, searchDto.getFrom());
        paramMap.put(2, searchDto.getTo());
        //search type
        if (searchDto.getType() != null) {
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.HOURS_WORKED)) {
                sbSql.append(" and tsrr.work_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sbSql.append(" and tsrr.week_ending_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.APPROVED_DATE)){
                sbSql.append(" and tswer.approved_date BETWEEN ?1 and ?2 ");
            }
        }
        // companyId
        if (ObjectUtil.isNotNull(searchDto.getCompanyId())) {
            paramMap.put(paramMap.size() + 1, searchDto.getCompanyId());
            sbSql.append(" and tswer.company_id = ?").append(paramMap.size());
        }
        // timesheet status
        if (ObjectUtil.isNotNull(searchDto.getTimeSheetStatus())) {
            paramMap.put(paramMap.size() + 1, searchDto.getTimeSheetStatus().toDbValue());
            sbSql.append(" and tswer.status = ?").append(paramMap.size());
        }
        //employment category
        if (CollUtil.isNotEmpty(searchDto.getAssignmentCategoryType())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentCategoryType().stream().map(AssignmentCategoryType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tta.employment_category in ?").append(paramMap.size());
        }
        // billing frequency
        if (CollUtil.isNotEmpty(searchDto.getBillingFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getBillingFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tswer.billing_frequency in ?").append(paramMap.size());
        }
        // paymentfrequency
        if (CollUtil.isNotEmpty(searchDto.getPayFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getPayFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tswer.payment_frequency in ?").append(paramMap.size());
        }
        // assignment division
        if (CollUtil.isNotEmpty(searchDto.getAssignmentDivision())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentDivision().stream().map(AssignmentDivision::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tswer.assignment_division in ?").append(paramMap.size());
        }
        sbSql.append(" group by tsrr.assignment_id, tsrr.week_end ");
        //order by
        if (BooleanUtil.isFalse(isCountFlag)) {
            sbSql.append(appendOrderBySearchType(searchDto.getSort(), searchDto.getDisplayHoursPerDayFlag()));
        }
        return sbSql.toString();
    }

    private String getListConditionWhereAndOrderForDay(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> paramMap, boolean isCountFlag) {
        StringBuilder sbSql = new StringBuilder();
        sbSql.append(" where tta.tenant_id = ").append(SecurityUtils.getTenantId())
                .append(" and tta.status = 1 ")
                .append(" and tta.start_date <= now() ");

        paramMap.put(1, searchDto.getFrom());
        paramMap.put(2, searchDto.getTo());
        //search type
        if (searchDto.getType() != null) {
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.HOURS_WORKED)) {
                sbSql.append(" and tsrr.work_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sbSql.append(" and tsrr.week_ending_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.APPROVED_DATE)){
                sbSql.append(" and tswer.approved_date BETWEEN ?1 and ?2 ");
            }
        }
        // companyId
        if (ObjectUtil.isNotNull(searchDto.getCompanyId())) {
            paramMap.put(paramMap.size() + 1, searchDto.getCompanyId());
            sbSql.append(" and tswer.company_id = ?").append(paramMap.size());
        }
        // timesheet status
        if (ObjectUtil.isNotNull(searchDto.getTimeSheetStatus())) {
            paramMap.put(paramMap.size() + 1, searchDto.getTimeSheetStatus().toDbValue());
            sbSql.append(" and tsrr.status = ?").append(paramMap.size());
        }
        //employment category
        if (CollUtil.isNotEmpty(searchDto.getAssignmentCategoryType())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentCategoryType().stream().map(AssignmentCategoryType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tta.employment_category in ?").append(paramMap.size());
        }
        // billing frequency
        if (CollUtil.isNotEmpty(searchDto.getBillingFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getBillingFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tswer.billing_frequency in ?").append(paramMap.size());
        }
        // paymentfrequency
        if (CollUtil.isNotEmpty(searchDto.getPayFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getPayFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tswer.payment_frequency in ?").append(paramMap.size());
        }
        // assignment division
        if (CollUtil.isNotEmpty(searchDto.getAssignmentDivision())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentDivision().stream().map(AssignmentDivision::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tswer.assignment_division in ?").append(paramMap.size());
        }
        sbSql.append(" group by tsrr.assignment_id, tsrr.work_date ");
        //order by
        if (BooleanUtil.isFalse(isCountFlag)) {
            sbSql.append(appendOrderBySearchType(searchDto.getSort(), searchDto.getDisplayHoursPerDayFlag()));
        }
        return sbSql.toString();
    }

    private String getListConditionFiled(TimesheetByStatusSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        if (Boolean.TRUE.equals(searchDto.getChargeNumberFlag())) {
            sb.append(" s.charge_number, ");
        }
        if (Boolean.TRUE.equals(searchDto.getTvcNumberFlag())) {
            sb.append(" s.tvc_number, ");
        }
        if (BooleanUtil.isTrue(searchDto.getDisplayHoursPerDayFlag())) {
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sb.append(" tsrr.week_ending_date week_end, ");
            } else {
                sb.append(" tsrr.week_end week_end, ");
            }
        } else {
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sb.append(" tsr.week_ending_date week_end, ");
            } else {
                sb.append(" tsr.week_end week_end, ");
            }
        }
        return sb.toString();
    }

    private String appendWhereBySearchType(TimesheetByStatusSearchDto searchDto, Map<Integer, Object> paramMap) {
        StringBuilder sbSql = new StringBuilder();
        sbSql.append(" where tta.tenant_id = ").append(SecurityUtils.getTenantId())
                .append(" and tta.status = 1 ")
                .append(" and tta.start_date <= now() ");

        paramMap.put(1, searchDto.getFrom());
        paramMap.put(2, searchDto.getTo());
        //search type
        if (searchDto.getType() != null) {
            if (Objects.equals(searchDto.getType(), TimesheetSearchType.HOURS_WORKED)) {
                sbSql.append(" and tsr.work_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.WEEK_ENDING_DATE)) {
                sbSql.append(" and tsr.week_ending_date BETWEEN ?1 and ?2 ");
            } else if (Objects.equals(searchDto.getType(), TimesheetSearchType.APPROVED_DATE)){
                sbSql.append(" and tswer.approved_date BETWEEN ?1 and ?2 ");
            }
        }
        // companyId
        if (ObjectUtil.isNotNull(searchDto.getCompanyId())) {
            paramMap.put(paramMap.size() + 1, searchDto.getCompanyId());
            sbSql.append(" and tswer.company_id = ?").append(paramMap.size());
        }
        // timesheet status
        if (ObjectUtil.isNotNull(searchDto.getTimeSheetStatus())) {
            paramMap.put(paramMap.size() + 1, searchDto.getTimeSheetStatus().toDbValue());
            sbSql.append(" and tsr.status = ?").append(paramMap.size());
        }
        //employment category
        if (CollUtil.isNotEmpty(searchDto.getAssignmentCategoryType())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentCategoryType().stream().map(AssignmentCategoryType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tta.employment_category in ?").append(paramMap.size());
        }
        // billing frequency
        if (CollUtil.isNotEmpty(searchDto.getBillingFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getBillingFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tswer.billing_frequency in ?").append(paramMap.size());
        }
        // paymentfrequency
        if (CollUtil.isNotEmpty(searchDto.getPayFrequency())) {
            paramMap.put(paramMap.size() + 1, searchDto.getPayFrequency().stream().map(TimeSheetFrequencyType::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tswer.payment_frequency in ?").append(paramMap.size());
        }
        // assignment division
        if (CollUtil.isNotEmpty(searchDto.getAssignmentDivision())) {
            paramMap.put(paramMap.size() + 1, searchDto.getAssignmentDivision().stream().map(AssignmentDivision::toDbValue).collect(Collectors.toList()));
            sbSql.append(" and tswer.assignment_division in ?").append(paramMap.size());
        }
        return sbSql.toString();
    }


    private String appendOrderBySearchType(SearchSortDTO sort, Boolean displayHoursPerDayFlag) {
        StringBuilder sb = new StringBuilder();
        if (sort != null) {
            String column = StrUtil.toUnderlineCase(sort.getProperty());
            String sortColumn;
            if (BooleanUtil.isTrue(displayHoursPerDayFlag)) {
                sortColumn = ReportTimeSheetConstants.ORDER_COLUMN_LIST.get(column);
            } else {
                sortColumn = ReportTimeSheetConstants.GROUP_ORDER_COLUMN_LIST.get(column);
            }
            sb.append(" order by CASE WHEN IFNULL( ").append(sortColumn).append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            if (CollUtil.newArrayList("t.full_name").contains(column)) {
                sb.append("CONVERT( ").append(sortColumn).append(" USING gbk) ");
                sb.append(" ").append(sort.getDirection());
            } else {
                sb.append(sortColumn);
                sb.append(" ").append(sort.getDirection());
            }
        } else {
            sb.append(" order by CASE WHEN IFNULL( ").append("tswer.full_name").append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            sb.append(" CONVERT( ").append("tswer.full_name").append(" USING gbk) ");
            sb.append(" ASC ");
        }
        return sb.toString();
    }

    private void checkParam(TimesheetByStatusSearchDto searchDto) {
        if (searchDto.getFrom() == null || searchDto.getTo() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORT_CHECKPARAM_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
        if (searchDto.getType() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORT_TIMESHEET_CHECKPARAM_TYPENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
    }

    /**
     * 根据给定的起始日期、结束日期和月份间隔生成日期范围。
     *
     * @param fromDate       起始日期，ISO 格式（yyyy-MM-dd）
     * @param toDate         结束日期，ISO 格式（yyyy-MM-dd）
     * @param intervalMonths 间隔的月数
     * @return 包含每个范围的开始日期和结束日期的字符串数组列表
     */
    public static List<String[]> generateDateRanges(String fromDate, String toDate, int intervalMonths) {
        List<String[]> dateRanges = new ArrayList<>();

        // 解析输入的日期
        LocalDate startDate = LocalDate.parse(fromDate, DateTimeFormatter.ISO_DATE);
        LocalDate endDate = LocalDate.parse(toDate, DateTimeFormatter.ISO_DATE);

        // 生成日期范围
        while (startDate.isBefore(endDate) || startDate.equals(endDate)) {
            String[] range = new String[2];
            // 格式化开始日期
            range[0] = startDate.format(DateTimeFormatter.ISO_DATE);
            // 计算当前范围的结束日期
            LocalDate nextEndDate = startDate.plusMonths(intervalMonths).minusDays(1);
            if (nextEndDate.isAfter(endDate)) {
                // 格式化结束日期
                range[1] = endDate.format(DateTimeFormatter.ISO_DATE);
            } else {
                // 格式化结束日期
                range[1] = nextEndDate.format(DateTimeFormatter.ISO_DATE);
            }
            // 添加范围到列表
            dateRanges.add(range);
            // 移动到下一个开始日期
            startDate = startDate.plusMonths(intervalMonths);
        }
        return dateRanges;
    }

}


