package com.altomni.apn.report.domain;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.report.domain.enumeration.StringToLongListConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "user_adoption_alert_lark_notification")
public class UserAdoptionAlertLarkNotification implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id", nullable = false, updatable = false)
    private Long tenantId;

    @Column(name = "permission_team_ids")
    @Convert(converter = StringToLongListConverter.class)
    private List<Long> teamIds;

    @Column(name = "notified_user_id")
    private Long userId;

}
