package com.altomni.apn.report.service.company;

import com.altomni.apn.common.aop.cache.CacheControl;
import com.altomni.apn.common.dto.company.SalesLeadClientContactDTO;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactStatusDTO;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectSearchVM;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.time.Instant;
import java.util.List;
import java.util.Set;

/**
 * Service Interface for managing {@link SalesLeadClientContactDTO}.
 */
@Component
@FeignClient(value = "company-service")
public interface CompanyService {

    @PostMapping("/company/api/v3/company/search/bd-report")
    ResponseEntity<List<CompanyProspectVM>> searchAllCompanyForBDReport(@RequestParam(value = "page") int page, @RequestParam(value = "size") int size, @RequestParam(value = "fromDate") Instant fromDate, @RequestParam(value = "toDate") Instant toDate, @RequestBody CompanyProspectSearchVM searchVM);

    @GetMapping("/company/api/v3/company/list")
    ResponseEntity<List<AccountCompanyVO>> getAllClientCompanyList(@RequestParam(value = "active", required = false) AccountCompanyStatus active, @RequestParam(value = "limit", required = false) Integer limit);

    @GetMapping("/company/api/v3/dict/country")
    ResponseEntity<List<EnumDictDTO>> findAllCountry();

    @PostMapping("/company/api/v3/saleslead/client-contacts/talent-client-contact-status/by-contactIds")
    ResponseEntity<List<TalentClientContactRelationDTO>> getTalentIdsByContactIds(@RequestBody Set<Long> contactIds);

    @PostMapping("/company/api/v3/saleslead/client-contacts/talent-client-contact-status/by-talentIds")
    ResponseEntity<List<TalentClientContactRelationDTO>> getContactIdsByTalentIds(@RequestBody Set<Long> talentIds);

    @PostMapping("/company/api/v3/saleslead/client-contacts/talent-client-contact-status")
    ResponseEntity<List<TalentClientContactStatusDTO>> getTalentAsClientContactStatus(@RequestBody Set<Long> talentIds);


}
