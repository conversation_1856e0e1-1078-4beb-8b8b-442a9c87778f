package com.altomni.apn.report.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.report.domain.enumeration.QuarterlyOnboardingType;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.ReportGpSearchDTO;
import com.altomni.apn.report.dto.ReportMonthlyRevenueGpSearchDTO;
import com.altomni.apn.report.dto.ReportQuarterOnboardAndOffboardSearchDTO;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.math.BigInteger;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

@Repository
public class ReportGpRepository extends BaseCustomRepository {

    final List<String> USER_GBK_LIST = CollUtil.newArrayList("full_name", "job_title", "company_name");

    public List<ReportGpWithExcelVO> searchReportGpWithExcelData(ReportGpSearchDTO dto, boolean withExcelData) {
        Class clazz = ReportGpVO.class;
        String sql = """
                SELECT
                	tta.id id,
                	t.full_name,
                    (case when rp.job_type = :jobTypeContract and (s.corp_to_corp = 0 or s.corp_to_corp is null) and (api.employment_category = 0 or api.employment_category is null) then 0
                         when rp.job_type = :jobTypePayroll and (s.corp_to_corp = 0 or s.corp_to_corp is null) and (api.employment_category = 0 or api.employment_category is null) then 1
                         when rp.job_type = :jobTypeContract and (s.corp_to_corp = 1 or api.employment_category = 1) then 2
                         when rp.job_type = :jobTypePayroll and (s.corp_to_corp = 1 or api.employment_category = 1) then 3
                         when rp.job_type = :jobTypeMSP and (s.corp_to_corp = 0 or s.corp_to_corp is null) and (api.employment_category = 0 or api.employment_category is null) then 4
                         when rp.job_type = :jobTypeMSP and (s.corp_to_corp = 1 or api.employment_category = 1) then 5 else null end) start_employment_category,
                	j.title job_title,
                	c.`full_business_name` company_name,
                	tta.start_date,
                	tta.end_date,
                	bill.pay_rate bill_rate,
                	bill.time_unit bill_rate_unit,
                	pay.pay_rate salary,
                	pay.time_unit salary_unit,
                	pec.symbol pec_symbol,
                 bec.symbol bec_symbol
                	""";
        if (withExcelData) {
            clazz = ReportGpWithExcelVO.class;
            sql = sql + """
                       ,
                       (select group_concat((case CONCAT(u.first_name,u.last_name) regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, " ",u.last_name) END) order by ac.id asc) from assignment_contribution ac inner join user u on ac.user_id = u.id where ac.user_role = 0 and ac.assignment_id = tta.id group by ac.assignment_id) am,
                    (select GROUP_CONCAT(FORMAT(ac.percentage,2) order by ac.id asc) from assignment_contribution ac where ac.user_role = 0 and ac.assignment_id = tta.id group by ac.assignment_id) am_contribution,
                    (select group_concat((case CONCAT(u.first_name,u.last_name) regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, " ",u.last_name) END) order by ac.id asc) from assignment_contribution ac inner join user u on ac.user_id = u.id where ac.user_role = 3 and ac.assignment_id = tta.id group by ac.assignment_id) dm,
                    (select GROUP_CONCAT(FORMAT(ac.percentage,2) order by ac.id asc) from assignment_contribution ac where ac.user_role = 3 and ac.assignment_id = tta.id group by ac.assignment_id) dm_contribution,
                    (select group_concat((case CONCAT(u.first_name,u.last_name) regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, " ",u.last_name) END) order by ac.id asc) from assignment_contribution ac inner join user u on ac.user_id = u.id where ac.user_role = 1 and ac.assignment_id = tta.id group by ac.assignment_id) recruiter,
                    (select GROUP_CONCAT(FORMAT(ac.percentage,2) order by ac.id asc) from assignment_contribution ac where ac.user_role = 1 and ac.assignment_id = tta.id group by ac.assignment_id) recruiter_contribution,
                    (select group_concat((case CONCAT(u.first_name,u.last_name) regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, " ",u.last_name) END) order by ac.id asc) from assignment_contribution ac inner join user u on ac.user_id = u.id where ac.user_role = 5 and ac.assignment_id = tta.id group by ac.assignment_id) ac,
                    (select GROUP_CONCAT(FORMAT(ac.percentage,2) order by ac.id asc) from assignment_contribution ac where ac.user_role = 5 and ac.assignment_id = tta.id group by ac.assignment_id) ac_contribution,
                    (select group_concat((case CONCAT(u.first_name,u.last_name) regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, " ",u.last_name) END) order by ac.id asc) from assignment_contribution ac inner join user u on ac.user_id = u.id where ac.user_role = 2 and ac.assignment_id = tta.id group by ac.assignment_id) source,
                    (select GROUP_CONCAT(FORMAT(ac.percentage,2) order by ac.id asc) from assignment_contribution ac where ac.user_role = 2 and ac.assignment_id = tta.id group by ac.assignment_id) source_contribution,
                    (select group_concat((case CONCAT(u.first_name,u.last_name) regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, " ",u.last_name) END) order by ac.id asc) from assignment_contribution ac inner join user u on ac.user_id = u.id where ac.user_role = 4 and ac.assignment_id = tta.id group by ac.assignment_id) owner,
                    (select GROUP_CONCAT(FORMAT(ac.percentage,2) order by ac.id asc) from assignment_contribution ac where ac.user_role = 4 and ac.assignment_id = tta.id group by ac.assignment_id) owner_contribution,
                    (select group_concat((case CONCAT(u.first_name,u.last_name) regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, " ",u.last_name) END) order by ac.id asc) from assignment_contribution ac inner join user u on ac.user_id = u.id where ac.user_role = 9 and ac.assignment_id = tta.id group by ac.assignment_id) sales_lead_owner,
                    (select GROUP_CONCAT(FORMAT(ac.percentage,2) order by ac.id asc) from assignment_contribution ac where ac.user_role = 9 and ac.assignment_id = tta.id group by ac.assignment_id) sales_lead_owner_contribution,
                    (select group_concat((case CONCAT(u.first_name,u.last_name) regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, " ",u.last_name) END) order by ac.id asc) from assignment_contribution ac inner join user u on ac.user_id = u.id where ac.user_role = 8 and ac.assignment_id = tta.id group by ac.assignment_id) bd_owner,
                    (select GROUP_CONCAT(FORMAT(ac.percentage,2) order by ac.id asc) from assignment_contribution ac where ac.user_role = 8 and ac.assignment_id = tta.id group by ac.assignment_id) bd_owner_contribution,
                    (select GROUP_CONCAT(tc.contact order by sort asc) from talent_contact tc where tc.talent_id = t.id and tc.jhi_type = 2 and status = 0 ) emails
                       """;
        }
        sql = sql + """
                FROM
                	timesheet_talent_assignment tta
                 inner join start s on s.id = tta.start_id
                    inner join talent t on t.id = tta.talent_id
                    inner join job j on j.id = tta.job_id
                    inner join company c on c.id = tta.company_id
                    inner join recruitment_process rp on rp.id = j.recruitment_process_id
                    left join assignment_pay_rate pay on pay.assignment_id = tta.id and pay.pay_type = 3 and pay.content_type = 1
                    left join enum_currency pec on pec.id = pay.currency
                 left join assignment_pay_rate bill on bill.assignment_id = tta.id and bill.pay_type = 2 and bill.content_type = 0
                    left join enum_currency bec on bec.id = bill.currency
                    left join assignment_pay_info api on api.assignment_id = tta.id
                	where s.position_type != :jobTypeFTE and s.status !=-1 and s.tenant_id = :tenantId and tta.start_date BETWEEN :from and :to
                """;
        Query query;
        if (withExcelData) {
            query = entityManager.createNativeQuery(sql + " group by tta.id " + getOrderSql(dto.getSort()), clazz);
        } else {
            query = entityManager.createNativeQuery(sql + " group by tta.id " + getOrderSql(dto.getSort()) + " limit :start,:end ", clazz);
            int startOffset = dto.getPage() * dto.getSize();
            query.setParameter("start", startOffset).setParameter("end", dto.getSize());
        }
        query.setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("from", dto.getFrom())
                .setParameter("to", dto.getTo())
                .setParameter("jobTypeContract", JobType.CONTRACT.toDbValue())
                .setParameter("jobTypePayroll", JobType.PAY_ROLL.toDbValue())
                .setParameter("jobTypeMSP", JobType.MSP.toDbValue())
                .setParameter("jobTypeFTE", JobType.FULL_TIME.toDbValue());
        if (clazz == ReportGpVO.class) {
            List<ReportGpVO> voList = query.getResultList();
            return voList.stream().map(vo -> {
                ReportGpWithExcelVO reportGpWithExcelVO = new ReportGpWithExcelVO();
                BeanUtil.copyProperties(vo, reportGpWithExcelVO);
                return reportGpWithExcelVO;
            }).toList();
        }
        return query.getResultList();
    }

    public Long countReportGp(ReportGpSearchDTO dto) {
        String sql = """
                SELECT
                	count(1)
                FROM
                	timesheet_talent_assignment tta
                	inner join start s on s.id = tta.start_id
                    inner join talent t on t.id = tta.talent_id
                    inner join job j on j.id = tta.job_id
                    inner join company c on c.id = tta.company_id
                	inner join recruitment_process rp on rp.id = j.recruitment_process_id
                	where s.position_type != :jobType and s.tenant_id = :tenantId
                	and tta.start_date BETWEEN :from and :to
                """;
        Query countQ = entityManager.createNativeQuery(sql);
        countQ.setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("from", dto.getFrom())
                .setParameter("to", dto.getTo())
                .setParameter("jobType", JobType.FULL_TIME.toDbValue());
        return Long.parseLong(String.valueOf(countQ.getSingleResult()));
    }

    private String getOrderSql(SearchSortDTO sort) {
        StringBuilder sb = new StringBuilder();
        if (sort != null) {
            String column = StrUtil.toUnderlineCase(sort.getProperty());
            if (CollUtil.newArrayList("start_date", "end_date").contains(column)) {
                column = "s." + column;
            }
            sb.append(" order by CASE WHEN IFNULL( ").append(column).append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            if (USER_GBK_LIST.contains(column)) {
                sb.append("CONVERT( ").append(column).append(" USING gbk) ");
                sb.append(" ").append(sort.getDirection());
            } else {
                sb.append(column);
                sb.append(" ").append(sort.getDirection());
            }
        } else {
            sb.append(" order by s.start_date asc ");
        }
        return sb.toString();
    }

    public ConcurrentMap<String, String> countReportMonthlyRevenueGp(ReportMonthlyRevenueGpSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String fteSql = """
                   select DATE_FORMAT(t.start_date,'%Y-%m') as year_month_str,
                   ROUND((case when sfr.currency =0 then sfr.total_bill_amount
                   else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * sfr.total_bill_amount,2)  end) * (sc.percentage/100),2) as user_percentage
                    from vChain t
                   inner join start_commission sc on sc.start_id = t.id
                   inner join start_fte_rate sfr on sfr.start_id = t.id
                   left join currency_rate_day crd on crd.rate_day = DATE_FORMAT(t.start_date,'%Y-%m-%d') and sfr.currency = crd.currency_id
                   left join enum_currency ec on  sfr.currency = ec.id
                   INNER JOIN vLatestChain lc ON t.chain_root = lc.chain_root AND t.job_id = lc.job_id AND t.start_date = lc.max_start_date
                """;

        String whereFteSql = """
                where DATE_FORMAT(t.start_date,'%Y-%m-%d') BETWEEN :startDate AND :endDate
                   and t.position_type = :jobTypeFTE and t.tenant_id=:tenantId
                """;

        String contractSql = """ 
                   select DATE_FORMAT(sfr.start_date,'%Y-%m') as year_month_str,
                   ROUND(
                   #计算hour bill rate
                   (case when sfr.currency =0 then
                   case when sfr.rate_unit_type = 1 then ROUND(sfr.final_bill_rate / 8,10)
                   when sfr.rate_unit_type = 2 then ROUND(sfr.final_bill_rate / sfr.estimated_working_hour_per_week,10)
                   when sfr.rate_unit_type = 3 then ROUND((sfr.final_bill_rate * 12 ) / 2080,10)
                   when sfr.rate_unit_type = 4 then ROUND(sfr.final_bill_rate / 2080,10)
                   else sfr.final_bill_rate end
                   else
                    ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * (case when sfr.rate_unit_type = 1 then ROUND(sfr.final_bill_rate / 8,10)
                   when sfr.rate_unit_type = 2 then ROUND(sfr.final_bill_rate / sfr.estimated_working_hour_per_week,10)
                   when sfr.rate_unit_type = 3 then ROUND((sfr.final_bill_rate * 12 ) / 2080,10)
                   when sfr.rate_unit_type = 4 then ROUND(sfr.final_bill_rate / 2080,10)
                   else sfr.final_bill_rate end ),2) end) * (Calculate_Workdays(sfr.start_date,sfr.end_date) * (sfr.estimated_working_hour_per_week/5)) * (sc.percentage/100),2) as user_percentage
                    from start t
                   inner join start_commission sc on sc.start_id = t.id
                   inner join start_contract_rate sfr on sfr.start_id = t.id
                   left join currency_rate_day crd on crd.rate_day = DATE_FORMAT(sfr.start_date,'%Y-%m-%d') and sfr.currency = crd.currency_id
                   left join enum_currency ec on  sfr.currency = ec.id
                   
                """;
        String whereContractSql= """
                where DATE_FORMAT(sfr.start_date,'%Y-%m-%d') BETWEEN :startDate AND :endDate
                   and t.position_type in (:jobTypes) and t.tenant_id=:tenantId
                """;

        StringBuilder dataSql = new StringBuilder();

        StringBuilder sql = new StringBuilder();
        sql = sql.append(fteSql)
                .append(addPermissionJoinMonthly(dto))
                .append(whereFteSql)
                .append(addWhereSql(dto, teamDataPermission))
                .append(" union all")
                .append(contractSql)
                .append(addPermissionJoinMonthly(dto))
                .append(whereContractSql)
                .append(addWhereSql(dto, teamDataPermission));

        dataSql.append("select tab.year_month_str as yearMonthStr,CAST(ROUND(sum(user_percentage),2) AS CHAR) as total from ( ").append(sql).append(" ) tab GROUP BY tab.year_month_str ").append(addOrderSql(dto));

        return searchMonthlyCountWithJobType(dataSql.toString(), dto, teamDataPermission);
    }

    public ConcurrentMap<String, String> countCashInGPContractorGp(ReportMonthlyRevenueGpSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String contractorSql = """
                   select DATE_FORMAT(t.payment_date,'%Y-%m') as year_month_str,
                  ROUND((case when gi.currency_id =0 then t.payment_amount
                  else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * t.payment_amount,2)  end) * (sc.percentage/100),2) as user_percentage
                  from invoice_record_payment_detail  t
                  inner join t_contractor_invoice ci on ci.id = t.invoice_id
                  inner join t_group_invoice gi on gi.id = t.group_id
                  inner join timesheet_talent_assignment tta on tta.id = ci.assignment_id
                  inner join start s on s.id = tta.start_id
                  inner join start_commission sc on sc.start_id = s.id
                  left join enum_currency ec on  gi.currency_id = ec.id
                  left join currency_rate_day crd on crd.rate_day = DATE_FORMAT(t.payment_date,'%Y-%m-%d') and gi.currency_id = crd.currency_id
                  
                """;
        String whereSql = """
                 where t.status=1 and gi.status=1 and gi.group_invoice_status !=6
                  and DATE_FORMAT(t.payment_date,'%Y-%m-%d') BETWEEN :startDate AND :endDate
                  and s.tenant_id=:tenantId
                """;

        StringBuilder dataSql = new StringBuilder();

        dataSql.append("select tab.year_month_str as yearMonthStr,CAST(ROUND(sum(user_percentage),2) AS CHAR) as total from ( ")
                .append(contractorSql)
                .append(addPermissionJoinMonthly(dto))
                .append(whereSql)
                .append(addWhereSql(dto, teamDataPermission))
                .append(" ) tab GROUP BY tab.year_month_str ")
                .append(addOrderSql(dto));

        return searchMonthlyCount(dataSql.toString(), dto, teamDataPermission);
    }

    public ConcurrentMap<String, String> countCashInGPFTEGp(ReportMonthlyRevenueGpSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String fteSql = """
                select
                DATE_FORMAT(t.payment_date,'%Y-%m') as year_month_str,
                ROUND((case when i.currency =0 then t.paid_amount + case when t.startup_fee_amount is not null then t.startup_fee_amount else 0 end
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * t.paid_amount,2)  end) * (sc.percentage/100),2) as user_percentage
                from invoice_payment_record  t
                inner join invoice i on i.id = t.invoice_id
                inner join start s on s.id = i.start_id
                inner join start_commission sc on sc.start_id = s.id
                left join currency_rate_day crd on crd.rate_day = DATE_FORMAT(t.payment_date,'%Y-%m-%d') and i.currency = crd.currency_id
                left join enum_currency ec on  i.currency = ec.id
                
                """;
        String whereFteSql = """
                where t.activated=1 and i.status !=7 and t.paid_amount>0
                and DATE_FORMAT(t.payment_date,'%Y-%m-%d') BETWEEN :startDate AND :endDate
                and s.tenant_id=:tenantId
                """;

        String invoicingFteSql = """
                  select
                  DATE_FORMAT(CONVERT_TZ(t.payment_date, 'UTC', 'Asia/Shanghai'),'%Y-%m') as year_month_str,
                  ROUND((case when sfr.currency =0 then t.payment_amount
                  else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * t.payment_amount,2)  end) * (sc.percentage/100),2) as user_percentage
                  from invoicing_record_payment_detail  t
                  inner join invoicing_record_payment_info irpi on irpi.id = t.payment_id
                  inner join invoicing_application_info iai on iai.id=irpi.invoicing_id
                  inner join invoicing_candidate_info ici on ici.invoice_application_id = iai.id and ici.status=1
                  inner join start s on s.id = ici.start_id and t.job_id = s.job_id and t.talent_id = s.talent_id
                  inner join start_fte_rate sfr on sfr.start_id = s.id
                  inner join start_commission sc on sc.start_id = s.id
                  left join currency_rate_day crd on crd.rate_day = DATE_FORMAT(CONVERT_TZ(t.payment_date, 'UTC', 'Asia/Shanghai'),'%Y-%m-%d') and sfr.currency = crd.currency_id
                  left join enum_currency ec on  sfr.currency = ec.id
                  
                """;

        String whereInvoicingSql = """
                where t.status=1 and irpi.status=1 and iai.invoicing_application_type=1 and iai.invoicing_status !=8 and iai.status=1 and t.payment_amount>0
                  and DATE_FORMAT(CONVERT_TZ(t.payment_date, 'UTC', 'Asia/Shanghai'),'%Y-%m-%d') BETWEEN :startDate AND :endDate
                  and s.tenant_id=:tenantId
                """;

        StringBuilder sql = new StringBuilder();
        sql = sql.append(fteSql)
                .append(addPermissionJoinMonthly(dto))
                .append(whereFteSql)
                .append(addWhereSql(dto, teamDataPermission))
                .append(" union all ")
                .append(invoicingFteSql)
                .append(addPermissionJoinMonthly(dto))
                .append(whereInvoicingSql)
                .append(addWhereSql(dto, teamDataPermission));

        StringBuilder dataSql = new StringBuilder();

        dataSql.append("select tab.year_month_str as yearMonthStr,CAST(ROUND(sum(user_percentage),2) AS CHAR) as total from ( ")
                .append(sql)
                .append(" ) tab GROUP BY tab.year_month_str ")
                .append(addOrderSql(dto));

        return searchMonthlyCount(dataSql.toString(), dto, teamDataPermission);
    }

    public ConcurrentMap<String, String> countForecastedGPFTE(ReportMonthlyRevenueGpSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String fteSql = """
                select 
                DATE_FORMAT(t.invoice_date,'%Y-%m') as year_month_str,
                ROUND((case when t.currency =0 then t.total_invoice_amount
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * t.total_invoice_amount,2)  end) * (sc.percentage/100),2) as user_percentage
                from invoice t
                inner join start s on s.id = t.start_id
                inner join start_commission sc on sc.start_id = s.id
                left join currency_rate_day crd on DATE_FORMAT(crd.rate_day,'%Y-%m-%d') = DATE_FORMAT(t.invoice_date,'%Y-%m-%d') and t.currency = crd.currency_id
                left join enum_currency ec on  t.currency = ec.id
                
                """;
        String whereFteSql = """
                where t.status !=7
                and DATE_FORMAT(t.invoice_date,'%Y-%m-%d') BETWEEN :startDate AND :endDate
                and s.tenant_id=:tenantId
                """;

        String invoicingFteSql = """
                  
                  select
                  DATE_FORMAT(CONVERT_TZ(t.invoicing_date, 'UTC', 'Asia/Shanghai'),'%Y-%m') as year_month_str,
                  ROUND((case when sfr.currency =0 then ici.amount_received_tax
                  else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * ici.amount_received_tax,2)  end) * (sc.percentage/100),2) as user_percentage
                  from invoicing_application_info t
                  inner join invoicing_candidate_info ici on ici.invoice_application_id = t.id and ici.status=1
                  inner join start s on s.id = ici.start_id
                  inner join start_commission sc on sc.start_id = s.id
                  inner join start_fte_rate sfr on sfr.start_id = s.id
                  left join currency_rate_day crd on crd.rate_day = DATE_FORMAT(CONVERT_TZ(t.invoicing_date, 'UTC', 'Asia/Shanghai'),'%Y-%m-%d') and sfr.currency = crd.currency_id
                  left join enum_currency ec on  sfr.currency = ec.id
                 
                """;

        String whereInvoicingSql= """
                where t.status=1 and t.invoicing_application_type=1 and t.invoicing_status in(2,3,4,5,6,7) and t.void_invoicing is null
                  and DATE_FORMAT(CONVERT_TZ(t.invoicing_date, 'UTC', 'Asia/Shanghai'),'%Y-%m-%d') BETWEEN :startDate AND :endDate
                  and s.tenant_id=:tenantId
                  
                """;

        StringBuilder sql = new StringBuilder();
        sql = sql.append(fteSql)
                .append(addPermissionJoinMonthly(dto))
                .append(whereFteSql)
                .append(addWhereSql(dto, teamDataPermission))
                .append(" union all ")
                .append(invoicingFteSql)
                .append(addPermissionJoinMonthly(dto))
                .append(whereInvoicingSql)
                .append(addWhereSql(dto, teamDataPermission));

        StringBuilder dataSql = new StringBuilder();

        dataSql.append("select tab.year_month_str as yearMonthStr,CAST(ROUND(sum(user_percentage),2) AS CHAR) as total from ( ")
                .append(sql)
                .append(" ) tab GROUP BY tab.year_month_str ")
                .append(addOrderSql(dto));

        return searchMonthlyCount(dataSql.toString(), dto, teamDataPermission);
    }

    private ConcurrentMap<String, String> searchMonthlyCountWithJobType(String sql, ReportMonthlyRevenueGpSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        Query dataQuery = entityManager.createNativeQuery(sql.toString());
        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        dataQuery.setParameter("startDate", dto.getStartDate());
        dataQuery.setParameter("endDate", dto.getEndDate());
        dataQuery.setParameter("jobTypeFTE", JobType.FULL_TIME.toDbValue());
        dataQuery.setParameter("jobTypes", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));

        addMonthlyParam(dataQuery, dto, teamDataPermission);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ReportMonthlyRevenueGpCountVO.class));
        List<ReportMonthlyRevenueGpCountVO> result = dataQuery.getResultList();
        if (!result.isEmpty()) {
            ConcurrentMap<String, String> resultMap = result.stream().filter(x -> null != x.getTotal()).collect(Collectors.toConcurrentMap(ReportMonthlyRevenueGpCountVO::getYearMonthStr, ReportMonthlyRevenueGpCountVO::getTotal));
            return resultMap;
        }
        return new ConcurrentHashMap<>();
    }

    private ConcurrentMap<String, String> searchMonthlyCount(String sql, ReportMonthlyRevenueGpSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        Query dataQuery = entityManager.createNativeQuery(sql.toString());
        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        dataQuery.setParameter("startDate", dto.getStartDate());
        dataQuery.setParameter("endDate", dto.getEndDate());

        addMonthlyParam(dataQuery, dto, teamDataPermission);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ReportMonthlyRevenueGpCountVO.class));
        List<ReportMonthlyRevenueGpCountVO> result = dataQuery.getResultList();
        if (!result.isEmpty()) {
            ConcurrentMap<String, String> resultMap = result.stream().filter(x -> null != x.getTotal()).collect(Collectors.toConcurrentMap(ReportMonthlyRevenueGpCountVO::getYearMonthStr, ReportMonthlyRevenueGpCountVO::getTotal));
            return resultMap;
        }
        return new ConcurrentHashMap<>();
    }

    public ConcurrentMap<String, String> countForecastedGPContractor(ReportMonthlyRevenueGpSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String sql = """
                 
                  select DATE_FORMAT(t.invoice_date,'%Y-%m') as year_month_str,
                   ROUND((case when t.currency_id =0 then ci.total_amount
                   else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * ci.total_amount,2)  end) * (sc.percentage/100),2) as user_percentage
                   from t_group_invoice  t
                   inner join t_group_invoice_record gir on gir.group_invoice_id=t.id
                   inner join t_contractor_invoice ci on ci.id = gir.invoice_id
                   inner join timesheet_talent_assignment tta on tta.id = ci.assignment_id
                   inner join start s on s.id = tta.start_id
                   inner join start_commission sc on sc.start_id = s.id
                   left join currency_rate_day crd on crd.rate_day = DATE_FORMAT(t.invoice_date,'%Y-%m-%d') and t.currency_id = crd.currency_id
                   left join enum_currency ec on  t.currency_id = ec.id
                 
                """;
        String whereSql = """
                where t.status=1 and t.group_invoice_status !=6 and ci.invoice_status!=3
                   and DATE_FORMAT(t.invoice_date,'%Y-%m-%d') BETWEEN :startDate AND :endDate
                   and s.tenant_id=:tenantId
                """;

        StringBuilder dataSql = new StringBuilder();

        dataSql.append("select tab.year_month_str as yearMonthStr,CAST(ROUND(sum(user_percentage),2) AS CHAR) as total from ( ")
                .append(sql)
                .append(addPermissionJoinMonthly(dto))
                .append(whereSql)
                .append(addWhereSql(dto, teamDataPermission))
                .append(" ) tab GROUP BY tab.year_month_str ")
                .append(addOrderSql(dto));

        return searchMonthlyCount(dataSql.toString(), dto, teamDataPermission);
    }


    public Page<ReportMonthlyRevenueDetailVO> selectMonthlyRevenueDetail(ReportMonthlyRevenueGpSearchDTO dto, Pageable pageable, TeamDataPermissionRespDTO teamDataPermission) {

        entityManager.clear();
        String sql = """
                 select CONCAT(t.talent_id,'',t.company_id,'',RAND(********) ) as id,
                 		case when t.position_type = :jobTypeFTE then 'FTE'
                 		else 'Contract' end as jobType,
                 		t.company as clientName,t.company_id as clientId,t.talent_id as candidateId,t.talent_name as candidateName,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=0) as am,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=1) as recruiter,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=2) as sourcer,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=3) as dm,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=5) as ac,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=4) as owners,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=8) as bdOwner,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=9) as salesLeadOwner,
                        (select group_concat(concat(a.user_full_name, '-', a.country ) SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=7) as coAm,
                  sfr.total_bill_amount as placementFeeNumber,
                  case when t.position_type = :jobTypeFTE then CAST(sfr.currency AS CHAR) else CAST(scr.currency AS CHAR) end as currency,
                  scr.final_bill_rate as billRateNumber,scr.final_pay_rate as payRateNumber,
                  case when t.position_type = :jobTypeFTE then CAST(sfr.rate_unit_type AS CHAR) else CAST(scr.rate_unit_type AS CHAR) end as rateUnitType,
                  case when t.position_type = :jobTypeFTE then ec.name else ecc.name end as currencyName,
                  case when t.position_type = :jobTypeFTE then t.start_date else
                      CAST(scr.start_date AS CHAR) end as onboardDate
                  from vChain t
                 inner join start_commission sc on sc.start_id = t.id
                 inner join start_fte_rate sfr on sfr.start_id = t.id
                 left join start_contract_rate scr on scr.start_id = t.id
                 left join enum_currency ec on ec.id =sfr.currency
                 left join enum_currency ecc on ecc.id =scr.currency
                 INNER JOIN vLatestChain lc ON t.chain_root = lc.chain_root AND t.job_id = lc.job_id AND t.start_date = lc.max_start_date
                """;
        String fteWhereSql= """
                where t.start_date BETWEEN :startDate AND :endDate
                 and t.position_type = :jobTypeFTE and t.tenant_id=:tenantId
                """;

        String contractSql = """
                 select CONCAT(t.talent_id,'',t.company_id,'',RAND(********) ) as id,
                 		case when t.position_type = :jobTypeFTE then 'FTE'
                 		else 'Contract' end as jobType,
                 		t.company as clientName,t.company_id as clientId,t.talent_id as candidateId,t.talent_name as candidateName,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=0) as am,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=1) as recruiter,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=2) as sourcer,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=3) as dm,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=5) as ac,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=4) as owners,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=8) as bdOwner,
                        (select group_concat(a.user_full_name SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=9) as salesLeadOwner,
                        (select group_concat(concat(a.user_full_name, '-', a.country ) SEPARATOR ',') from start_commission a where t.id = a.start_id and a.user_role=7) as coAm,
                  sfr.total_bill_amount as placementFeeNumber,
                  case when t.position_type = :jobTypeFTE then CAST(sfr.currency AS CHAR) else CAST(scr.currency AS CHAR) end as currency,
                  scr.final_bill_rate as billRateNumber,scr.final_pay_rate as payRateNumber,
                  case when t.position_type = :jobTypeFTE then CAST(sfr.rate_unit_type AS CHAR) else CAST(scr.rate_unit_type AS CHAR) end as rateUnitType,
                  case when t.position_type = :jobTypeFTE then ec.name else ecc.name end as currencyName,
                  case when t.position_type = :jobTypeFTE then t.start_date else
                      CAST(scr.start_date AS CHAR) end as onboardDate
                  from start t
                 inner join start_commission sc on sc.start_id = t.id
                 inner join start_contract_rate scr on scr.start_id = t.id
                 left join start_fte_rate sfr on sfr.start_id = t.id
                 left join enum_currency ec on ec.id =sfr.currency
                 left join enum_currency ecc on ecc.id =scr.currency
                 
                 
                """;

        String contractWhereSql= """
                where scr.start_date BETWEEN :startDate AND :endDate
                 and t.position_type in (:jobTypes) and t.tenant_id=:tenantId
                """;

        StringBuilder dataSql = new StringBuilder();

        dataSql.append("select id,jobType,clientName,clientId,candidateId,candidateName,am,recruiter,sourcer,dm,ac,owners,salesLeadOwner,bdOwner,coAm,placementFeeNumber,currency,billRateNumber,payRateNumber,rateUnitType,currencyName,onboardDate from (")
                .append(sql)
                .append(addPermissionJoinMonthly(dto))
                .append(fteWhereSql)
                .append(addWhereSql(dto, teamDataPermission))
                .append(" group by t.id,t.position_type ")
                .append(" union all")
                .append(contractSql)
                .append(addPermissionJoinMonthly(dto))
                .append(contractWhereSql)
                .append(addWhereSql(dto, teamDataPermission))
                .append(" group by t.id,t.position_type,scr.id ) tab");

        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (!Objects.isNull(order)) {
            if (order.getProperty().equals("clientName")) {
                dataSql.append(" order by CONVERT( clientName  USING gbk) " + order.getDirection());
            } else if (order.getProperty().equals("candidateName")) {
                dataSql.append(" order by CONVERT( candidateName  USING gbk) " + order.getDirection());
            } else if (order.getProperty().equals("onboardDate")) {
                dataSql.append(" order by onboardDate " + order.getDirection());
            } else {
                dataSql.append(" order by jobType " + order.getDirection());
            }
        } else {
            dataSql.append(" order by onboardDate,CONVERT( candidateName  USING gbk) asc");
        }

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(countSql);

        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        dataQuery.setParameter("jobTypeFTE", JobType.FULL_TIME.toDbValue());
        dataQuery.setParameter("jobTypes", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
        countQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        countQuery.setParameter("jobTypeFTE", JobType.FULL_TIME.toDbValue());
        countQuery.setParameter("jobTypes", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
        dataQuery.setParameter("startDate", dto.getStartDate());
        countQuery.setParameter("startDate", dto.getStartDate());
        dataQuery.setParameter("endDate", dto.getEndDate());
        countQuery.setParameter("endDate", dto.getEndDate());

        if (CollUtil.isNotEmpty(dto.getUserIdList()) && CollUtil.isNotEmpty(dto.getTeamIdList())) {
            dataQuery.setParameter("userIds", dto.getUserIdList());
            countQuery.setParameter("userIds", dto.getUserIdList());
            dataQuery.setParameter("teamIds", dto.getTeamIdList());
            countQuery.setParameter("teamIds", dto.getTeamIdList());
        } else {
            if (null != dto.getUserIdList() && !dto.getUserIdList().isEmpty()) {
                dataQuery.setParameter("userIds", dto.getUserIdList());
                countQuery.setParameter("userIds", dto.getUserIdList());
            }

            if (null != dto.getTeamIdList() && !dto.getTeamIdList().isEmpty()) {
                dataQuery.setParameter("teamIds", dto.getTeamIdList());
                countQuery.setParameter("teamIds", dto.getTeamIdList());
            }
        }

        if (dto.getUserIdList().isEmpty() && dto.getTeamIdList().isEmpty()) {
            if (!SecurityUtils.isAdmin()) {
                if (teamDataPermission.getSelf()) {
                    dataQuery.setParameter("userSelf", SecurityUtils.getUserId());
                    countQuery.setParameter("userSelf", SecurityUtils.getUserId());
                } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                    dataQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());
                    countQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());
                }
            }
        }

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ReportMonthlyRevenueDetailVO.class));
        dataQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        dataQuery.setMaxResults(pageable.getPageSize());

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<ReportMonthlyRevenueDetailVO> reportMonthlyRevenueDetailVOList = dataQuery.getResultList();

        reportMonthlyRevenueDetailVOList.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAm())) {
                List<UserCountryVO> voList = new ArrayList<>();
                Arrays.stream(x.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserName(amCountry[0]);
                    vo.setCountryId(amCountry[1]);
                    voList.add(vo);
                });
                x.setCoAmList(voList);
            }
        });

        return new PageImpl<>(reportMonthlyRevenueDetailVOList, Pageable.unpaged(), total);
    }

    private void addMonthlyParam(Query dataQuery, ReportMonthlyRevenueGpSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        if (CollUtil.isNotEmpty(dto.getUserIdList()) && CollUtil.isNotEmpty(dto.getTeamIdList())) {
            dataQuery.setParameter("userIds", dto.getUserIdList());
            dataQuery.setParameter("teamIds", dto.getTeamIdList());
        } else {
            if (null != dto.getUserIdList() && !dto.getUserIdList().isEmpty()) {
                dataQuery.setParameter("userIds", dto.getUserIdList());
            }

            if (null != dto.getTeamIdList() && !dto.getTeamIdList().isEmpty()) {
                dataQuery.setParameter("teamIds", dto.getTeamIdList());
            }
        }

        if (dto.getUserIdList().isEmpty() && dto.getTeamIdList().isEmpty()) {
            if (!SecurityUtils.isAdmin()) {
                if (teamDataPermission.getSelf()) {
                    dataQuery.setParameter("userSelf", SecurityUtils.getUserId());
                } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                    dataQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());
                }
            }
        }
    }

    private String addPermissionJoinMonthly(ReportMonthlyRevenueGpSearchDTO dto){

        StringBuilder sql = new StringBuilder();
        if ((dto.getUserIdList().isEmpty() && dto.getTeamIdList().isEmpty()) || !dto.getTeamIdList().isEmpty()) {
            sql.append("""
                    inner join permission_user_team put on put.user_id = sc.user_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id 
                    """);
        }
        return sql.toString();

    }

    private String addWhereSql(ReportMonthlyRevenueGpSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        StringBuilder whereSql = new StringBuilder();
        if (dto.getUserIdList().isEmpty() && dto.getTeamIdList().isEmpty()) {
            if (!SecurityUtils.isAdmin()) {
                if (teamDataPermission.getSelf()) {
                    whereSql.append(" and put.user_id =:userSelf");

                } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                    whereSql.append(" and put.team_id in ( :teamId )");
                }
            }
        }

        if (CollUtil.isNotEmpty(dto.getUserIdList()) && CollUtil.isNotEmpty(dto.getTeamIdList())) {
            whereSql.append(" and (put.user_id in (:userIds) or put.team_id in (:teamIds) )");
        } else {
            if (null != dto.getUserIdList() && !dto.getUserIdList().isEmpty()) {
                whereSql.append(" and sc.user_id in (:userIds)");
            }

            if (null != dto.getTeamIdList() && !dto.getTeamIdList().isEmpty()) {
                whereSql.append(" and put.team_id in (:teamIds)");
            }
        }

        return whereSql.toString();
    }

    private String addOrderSql(ReportMonthlyRevenueGpSearchDTO dto) {
        StringBuilder orderBySql = new StringBuilder();
        if (null != dto.getSort()) {
            if (dto.getSort().getDirection().equals("desc")) {
                orderBySql.append("order by year_month_str desc");
            } else {
                orderBySql.append("order by year_month_str asc");
            }
        } else {
            orderBySql.append("order by year_month_str desc");
        }
        return orderBySql.toString();
    }

    public List<ReportQuarterlyOnboardAndOffboardCountVO> countReportQuarterlyOnboardingGp(ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String sql = """
                 
                select DATE_FORMAT(t.start_date,'%Y-%m') as yearMonthStr,
                CAST(
                ROUND(
                (case
                when sfr.rate_unit_type = 1 then ROUND((case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 8,10)
                when sfr.rate_unit_type = 2 then ROUND((case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / sfr.estimated_working_hour_per_week,10)
                when sfr.rate_unit_type = 3 then ROUND(((case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) * 12 ) / 2080,10)
                when sfr.rate_unit_type = 4 then ROUND((case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 2080,10)
                else (case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) end) ,2) AS CHAR ) as total,
                CAST(sfr.id AS CHAR) as startId
                 from start t
                inner join start_commission sc on sc.start_id = t.id
                inner join start_contract_rate sfr on sfr.start_id = t.id
                INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate pay ON pay.CODE = sfr.tax_burden_rate
                INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate bill ON bill.CODE = sfr.msp_rate
                left join currency_rate_day crd on crd.rate_day = t.start_date and sfr.currency = crd.currency_id
                left join enum_currency ec on  sfr.currency = ec.id
                inner join date_dimension d on d.date = t.start_date
                
                """;

        String whereSql = """
                   where sc.user_role=1 and t.start_type=0 and sfr.extend_start_contract_rate_id is null
                and t.position_type in (:jobTypes) and t.tenant_id=:tenantId 
                """;

        StringBuilder dataSql = new StringBuilder();

        dataSql.append(sql)
                .append(addPermissionJoin(dto))
                .append(whereSql).append(" and ")
                .append(addQuarterlyWhereSql(dto))
                .append(addUserWhereSql(dto, teamDataPermission))
                .append(" group by t.id,sc.user_id order by d.date ");

        return searchQuarterOnboardAndOffboardCountVOWithJobType(dataSql.toString(), dto, teamDataPermission);
    }

    private void addQuarterlyParam(Query dataQuery, ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        if (CollUtil.isNotEmpty(dto.getUserIdList()) && CollUtil.isNotEmpty(dto.getTeamIdList())) {
            dataQuery.setParameter("userIds", dto.getUserIdList());
            dataQuery.setParameter("teamIds", dto.getTeamIdList());
        } else {
            if (null != dto.getUserIdList() && !dto.getUserIdList().isEmpty()) {
                dataQuery.setParameter("userIds", dto.getUserIdList());
            }

            if (null != dto.getTeamIdList() && !dto.getTeamIdList().isEmpty()) {
                dataQuery.setParameter("teamIds", dto.getTeamIdList());
            }
        }

        if (dto.getUserIdList().isEmpty() && dto.getTeamIdList().isEmpty()) {
            if (!SecurityUtils.isAdmin()) {
                if (teamDataPermission.getSelf()) {
                    dataQuery.setParameter("userSelf", SecurityUtils.getUserId());
                } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                    dataQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());
                }
            }
        }
    }

    public List<ReportQuarterlyOnboardAndOffboardCountVO> countReportQuarterlyOffboardingGp(ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String sql = """
            
                select DATE_FORMAT(st.termination_date,'%Y-%m') as yearMonthStr,
                CAST(
                ROUND(
                (case
                when sfr.rate_unit_type = 1 then ROUND((case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 8,10)
                when sfr.rate_unit_type = 2 then ROUND((case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / sfr.estimated_working_hour_per_week,10)
                when sfr.rate_unit_type = 3 then ROUND(((case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) * 12 ) / 2080,10)
                when sfr.rate_unit_type = 4 then ROUND((case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 2080,10)
                else (case when sfr.currency =0 then ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( sfr.final_bill_rate - sfr.final_pay_rate - sfr.final_bill_rate * bill.VALUE * 0.01 - sfr.final_pay_rate * pay.VALUE * 0.01 ),2) end) end) ,2) AS CHAR ) as total,
                CAST(sfr.id AS CHAR) as startId
                from start t
                inner join start_commission sc on sc.start_id = t.id
                inner join start_termination st on st.start_id= t.id
                inner join start_contract_rate sfr on sfr.start_id = t.id and st.termination_date = sfr.end_date
                INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate pay ON pay.CODE = sfr.tax_burden_rate
                INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate bill ON bill.CODE = sfr.msp_rate
                inner join date_dimension d on d.date = st.termination_date
                left join currency_rate_day crd on crd.rate_day = st.termination_date and sfr.currency = crd.currency_id
                left join enum_currency ec on  sfr.currency = ec.id
                 
                """;
        String whereSql = """
                where sc.user_role=1
                and t.position_type in (:jobTypes) and t.tenant_id=:tenantId
                """;

        StringBuilder dataSql = new StringBuilder();

        dataSql.append(sql)
                .append(addPermissionJoin(dto))
                .append(whereSql)
                .append(" and ")
                .append(addQuarterlyWhereSql(dto))
                .append(addUserWhereSql(dto, teamDataPermission)).append(" order by d.date ");

        return searchQuarterOnboardAndOffboardCountVOWithJobType(dataSql.toString(), dto, teamDataPermission);
    }

    public List<ReportQuarterlyOnboardAndOffboardCountVO> countQuarterlyForecastedGPContractor(ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String sql = """
                  select yearMonthStr,CAST(ROUND(total,2) AS CHAR) as total,startId from (
                  select yearMonthStr,sum(total) as  total,startId,user_id from (
                  	select DATE_FORMAT(t.invoice_date,'%Y-%m') as yearMonthStr,
                     CAST(ROUND((case when t.currency_id =0 then ci.total_amount
                     else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * ci.total_amount,2)  end),2) AS CHAR) as total,
                     CAST(s.id AS CHAR) as startId
                     ,sc.user_id
                     from t_group_invoice  t
                     inner join t_group_invoice_record gir on gir.group_invoice_id=t.id
                     inner join t_contractor_invoice ci on ci.id = gir.invoice_id
                     inner join timesheet_talent_assignment tta on tta.id = ci.assignment_id
                     inner join start s on s.id = tta.start_id
                     inner join start_commission sc on sc.start_id = s.id
                     left join currency_rate_day crd on crd.rate_day = DATE_FORMAT(t.invoice_date,'%Y-%m-%d') and t.currency_id = crd.currency_id
                     left join enum_currency ec on  t.currency_id = ec.id
                     inner join date_dimension d on d.date = DATE_FORMAT(t.invoice_date,'%Y-%m-%d')
                     
                """;
        String whereSql = """
                where sc.user_role=1 and t.status=1 and t.group_invoice_status !=6 and ci.invoice_status!=3 
                     and s.tenant_id=:tenantId
                """;

        StringBuilder dataSql = new StringBuilder();

        dataSql.append(sql)
                .append(addPermissionJoin(dto))
                .append(whereSql)
                .append(" and ")
                .append(addQuarterlyWhereSql(dto))
                .append(addUserWhereSql(dto, teamDataPermission))
                .append(" order by d.date )  tab group by yearMonthStr,startId,user_id ) xx");

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());
        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        addQuarterlyParam(dataQuery, dto, teamDataPermission);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ReportQuarterlyOnboardAndOffboardCountVO.class));
        List<ReportQuarterlyOnboardAndOffboardCountVO> result = dataQuery.getResultList();
        return result;
    }

    private List<ReportQuarterlyOnboardAndOffboardCountVO> searchQuarterOnboardAndOffboardCountVOWithJobType(String sql, ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermissionRespDTO) {

        Query dataQuery = entityManager.createNativeQuery(sql.toString());
        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        dataQuery.setParameter("jobTypes", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
        addQuarterlyParam(dataQuery, dto, teamDataPermissionRespDTO);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ReportQuarterlyOnboardAndOffboardCountVO.class));
        List<ReportQuarterlyOnboardAndOffboardCountVO> result = dataQuery.getResultList();
        return result;
    }

    public List<ReportQuarterlyOnboardAndOffboardCountVO> countQuarterlyForecastedGPFTE(ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String fteSql = """
                                 
                select DATE_FORMAT(t.start_date,'%Y-%m') as yearMonthStr,
                CAST(ROUND((case when sfr.currency =0 then sfr.total_bill_amount
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * sfr.total_bill_amount,2)  end) * (sc.percentage/100),2) AS CHAR) as total,'-1' as startId
                 from vChain t
                inner join start_commission sc on sc.start_id = t.id
                inner join start_fte_rate sfr on sfr.start_id = t.id
                left join currency_rate_day crd on crd.rate_day = DATE_FORMAT(t.start_date,'%Y-%m-%d') and sfr.currency = crd.currency_id
                left join enum_currency ec on  sfr.currency = ec.id
                inner join date_dimension d on d.date = DATE_FORMAT(t.start_date,'%Y-%m-%d')
                INNER JOIN vLatestChain lc ON t.chain_root = lc.chain_root AND t.job_id = lc.job_id AND t.start_date = lc.max_start_date          
                 """;
        String whereSql = """
                 where t.position_type = :jobType
                 and t.tenant_id=:tenantId
                """;

        StringBuilder dataSql = new StringBuilder();

        dataSql.append(fteSql)
                .append(addPermissionJoin(dto))
                .append(whereSql)
                .append(" and ")
                .append(addQuarterlyWhereSql(dto))
                .append(addUserWhereSql(dto, teamDataPermission));

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());
        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        dataQuery.setParameter("jobType", JobType.FULL_TIME.toDbValue());
        addQuarterlyParam(dataQuery, dto, teamDataPermission);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ReportQuarterlyOnboardAndOffboardCountVO.class));
        List<ReportQuarterlyOnboardAndOffboardCountVO> result = dataQuery.getResultList();
        return result;
    }

    public List<ReportQuarterlyOnboardAndOffboardCountVO> countReportQuarterlyOnboardingRenewalsGp(ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        entityManager.clear();
        String sql = """
               
                select DATE_FORMAT(scr.start_date,'%Y-%m') as yearMonthStr,
                CAST( 
                ROUND(
                (case 
                when scr.rate_unit_type = 1 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) * 
                ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2)  end) / 8,10)
                when scr.rate_unit_type = 2 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) * 
                ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2)  end) / scr.estimated_working_hour_per_week,10)
                when scr.rate_unit_type = 3 then ROUND(((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) * 
                ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2)  end) * 12 ) / 2080,10)
                when scr.rate_unit_type = 4 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) * 
                ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2)  end) / 2080,10)
               else (case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) end) ,2) AS CHAR ) as total,
                CAST(scr.id AS CHAR) as startId
                from start t
                inner join start_contract_rate scr on scr.start_id = t.id
                inner join start_commission sc on sc.start_id = t.id
               INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate pay ON pay.CODE = scr.tax_burden_rate
                INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate bill ON bill.CODE = scr.msp_rate
                inner join date_dimension d on d.date = scr.start_date
                left join currency_rate_day crd on crd.rate_day = scr.start_date and scr.currency = crd.currency_id
                left join enum_currency ec on  scr.currency = ec.id
                 
                """;
        String whereSql = """
                where t.position_type in (:jobTypes) and t.tenant_id=:tenantId and sc.user_role=1
                and ((scr.extend_start_contract_rate_id is not null and t.start_type in (0,1)) or t.start_type=1)
                """;

        StringBuilder dataSql = new StringBuilder();

        dataSql.append(sql)
                .append(addPermissionJoin(dto))
                .append(whereSql)
                .append(" and ")
                .append(addQuarterlyWhereSql(dto))
                .append(addUserWhereSql(dto, teamDataPermission))
                .append(" group by t.id,t.start_type,scr.extend_start_contract_rate_id,sc.user_id ");

        return searchQuarterOnboardAndOffboardCountVOWithJobType(dataSql.toString(), dto, teamDataPermission);
    }

    public Page<ReportQuarterlyOnboardingDetailVO> selectQuarterlyNewHireAndRenewalDetail(ReportQuarterOnboardAndOffboardSearchDTO dto, Pageable pageable, TeamDataPermissionRespDTO teamDataPermission) {

        entityManager.clear();
        String sql = """
                 select CONCAT(tab.candidateId,'',tab.clientId,'',RAND(********) ) as id,
                 recruiter,clientAccount,clientId,candidateId,candidateName,CAST(startDay AS CHAR) as startDay,CAST(weekDay AS CHAR) as weekDay,currency,
                 weekDay as weekDays,startDay as startDays,
                 currencyName,symbol,CAST(ROUND(sum(hourlyGp),2) AS CHAR) as hourlyGp,CAST(ROUND(sum(totalGp),2) AS CHAR) as totalGp,CAST(ROUND(sum(totalRevenue),2) AS CHAR) as totalRevenue
                 from (select DISTINCT scr.id as startId,
                        (select GROUP_CONCAT(DISTINCT s.user_full_name SEPARATOR ',') from start_commission s
                             where s.start_id = t.id and s.user_role=1) recruiter,
                        CAST(t.company AS CHAR) as clientAccount,
                        t.company_id as clientId,
                        t.talent_id as candidateId,
                        t.talent_name as candidateName,
                        t.start_date as startDay,
                        d.week_of_year as weekDay,
                        CAST(scr.currency AS CHAR) as currency,
                        pec.name as currencyName,
                        pec.symbol,
                        CAST(ROUND(
                           (case when scr.rate_unit_type = 1 then ROUND((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) / 8,10)
                           when scr.rate_unit_type = 2 then ROUND((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) / scr.estimated_working_hour_per_week,10)
                           when scr.rate_unit_type = 3 then ROUND(((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) * 12 ) / 2080,10)
                           when scr.rate_unit_type = 4 then ROUND((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) / 2080,10)
                           else (scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) end ),2) AS CHAR) as hourlyGp,
                        CAST(scr.total_bill_amount AS CHAR)  as totalGp,
                        CAST(ROUND(
                           #计算hour bill rate
                            ROUND((case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,10)
                           when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,10)
                           when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,10)
                           when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,10)
                           else scr.final_bill_rate end ),2) * (Calculate_Workdays(scr.start_date,scr.end_date) * (scr.estimated_working_hour_per_week/5)) ,2) AS CHAR) as totalRevenue
                        
                           from start t
                          inner join start_commission sc on sc.start_id = t.id
                          inner join start_contract_rate scr on scr.start_id = t.id
                          inner join date_dimension d on d.date = t.start_date
                          INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate pay ON pay.CODE = scr.tax_burden_rate
                          INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate bill ON bill.CODE = scr.msp_rate
                          left join enum_currency pec on pec.id = scr.currency
                          left join enum_currency ec on  scr.currency = ec.id
                          left join currency_rate_day crd on crd.rate_day = t.start_date and scr.currency = crd.currency_id
                          
                """;
        String whereSql = """
                 where t.position_type in (:jobTypes) and t.tenant_id=:tenantId and sc.user_role=1 and t.start_type=0 and scr.extend_start_contract_rate_id is null
                """;

        StringBuilder dataSql = new StringBuilder();

        if (dto.getType().equals(QuarterlyOnboardingType.NEWHIRES)) {
            dataSql.append(sql)
                    .append(addPermissionJoin(dto))
                    .append(whereSql)
                    .append(" and ")
                    .append(addQuarterlyWhereSql(dto))
                    .append(addUserWhereSql(dto, teamDataPermission))
                    .append(" group by t.id ,sc.user_id")
                    .append(" ) tab GROUP BY  tab.startId,recruiter,clientAccount,clientId,candidateId,candidateName,startDay ");
        } else {
            String whereRenewalsSql = """
                    where t.position_type in (:jobTypes) and t.tenant_id=:tenantId and sc.user_role=1
                    and ((scr.extend_start_contract_rate_id is not null and t.start_type in (0,1)) or t.start_type=1)
                    """;
            dataSql.append("""
                             select CONCAT(tab.candidateId,'',tab.clientId,'',RAND(********) ) as id,
                             recruiter,clientAccount,clientId,candidateId,candidateName,CAST(startDay AS CHAR) as startDay,CAST(weekDay AS CHAR) as weekDay,currency,
                             weekDay as weekDays,startDay as startDays,
                             currencyName,tab.symbol,CAST(ROUND(sum(hourlyGp),2) AS CHAR) as hourlyGp,CAST(ROUND(sum(totalGp),2) AS CHAR) as totalGp,CAST(ROUND(sum(totalRevenue),2) AS CHAR) as totalRevenue,
                             billRate,payRate,originalCurrency,originalBillRate,originalPayRate,
                             CAST(case when currency = originalCurrency then
                                    case when tab.changeOriginalBillRate - tab.billRate =0 then 'no change' else ROUND((tab.changeOriginalBillRate - tab.billRate),2) end
                             else
                                 case when ROUND((changeOriginalBillRate * ecc.to_usd_rate),2) - tab.billRate =0 then 'no change' else ROUND((changeOriginalBillRate * ecc.to_usd_rate) - tab.billRate ,2) end
                             end AS CHAR) as notes
                             from (
                            """)
                    .append(getQuarterlyRenewalsSql())
                    .append(addPermissionJoin(dto))
                    .append(whereRenewalsSql)
                    .append(" and ")
                    .append(addQuarterlyWhereSql(dto))
                    .append(addUserWhereSql(dto, teamDataPermission))
                    .append(" group by t.id,t.start_type,scr.extend_start_contract_rate_id,sc.user_id ")
                    .append(" ) tab left join  enum_currency ecc on  ecc.id = tab.currency GROUP BY  tab.startId,recruiter,clientAccount,clientId,candidateId,candidateName,startDay");
        }


        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (!Objects.isNull(order)) {
            if (order.getProperty().equals("recruiter")) {
                dataSql.append(" order by CONVERT( recruiter  USING gbk) " + order.getDirection());
            } else if (order.getProperty().equals("candidateName")) {
                dataSql.append(" order by CONVERT( candidateName  USING gbk) " + order.getDirection());
            } else if (order.getProperty().equals("clientAccount")) {
                dataSql.append(" order by CONVERT( clientAccount  USING gbk) " + order.getDirection());
            } else if (order.getProperty().equals("startDay")) {
                dataSql.append(" order by startDays " + order.getDirection());
            } else {
                dataSql.append(" order by weekDays " + order.getDirection());
            }
        } else {
            dataSql.append(" order by startDays asc,CONVERT( candidateName  USING gbk) asc");
        }

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(countSql);

        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        dataQuery.setParameter("jobTypes", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
        countQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        countQuery.setParameter("jobTypes", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));

        if (CollUtil.isNotEmpty(dto.getUserIdList()) && CollUtil.isNotEmpty(dto.getTeamIdList())) {
            dataQuery.setParameter("userIds", dto.getUserIdList());
            countQuery.setParameter("userIds", dto.getUserIdList());
            dataQuery.setParameter("teamIds", dto.getTeamIdList());
            countQuery.setParameter("teamIds", dto.getTeamIdList());
        } else {
            if (null != dto.getUserIdList() && !dto.getUserIdList().isEmpty()) {
                dataQuery.setParameter("userIds", dto.getUserIdList());
                countQuery.setParameter("userIds", dto.getUserIdList());
            }

            if (null != dto.getTeamIdList() && !dto.getTeamIdList().isEmpty()) {
                dataQuery.setParameter("teamIds", dto.getTeamIdList());
                countQuery.setParameter("teamIds", dto.getTeamIdList());
            }
        }

        if (dto.getUserIdList().isEmpty() && dto.getTeamIdList().isEmpty()) {
            if (!SecurityUtils.isAdmin()) {
                if (teamDataPermission.getSelf()) {
                    dataQuery.setParameter("userSelf", SecurityUtils.getUserId());
                    countQuery.setParameter("userSelf", SecurityUtils.getUserId());
                } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                    dataQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());
                    countQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());
                }
            }
        }

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ReportQuarterlyOnboardingDetailVO.class));
        dataQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        dataQuery.setMaxResults(pageable.getPageSize());

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<ReportQuarterlyOnboardingDetailVO> reportQuarterlyOnboardingDetailVOS = dataQuery.getResultList();

        ReportQuarterlyOnboardingDetailVO vo = new ReportQuarterlyOnboardingDetailVO();
        vo.setTotal(selectQuarterlyNewHireAndRenewalSum(dto, teamDataPermission));
        reportQuarterlyOnboardingDetailVOS.add(vo);
        return new PageImpl<>(reportQuarterlyOnboardingDetailVOS, Pageable.unpaged(), total);
    }

    private String getQuarterlyRenewalsSql() {
        String sql = """        
                 select DISTINCT scr.id as startId,
                 (select GROUP_CONCAT(DISTINCT s.user_full_name SEPARATOR ',') from start_commission s
                             where s.start_id = t.id and s.user_role=1) recruiter,
                 t.company as clientAccount,
                 t.company_id as clientId,
                 t.talent_id as candidateId,
                 t.talent_name as candidateName,
                 scr.start_date as startDay,
                 d.week_of_year as weekDay,
                 CAST(scr.currency AS CHAR) as currency,
                 pec.name as currencyName,
                 pec.symbol,
                 CAST(ROUND(
                           (case when scr.rate_unit_type = 1 then ROUND((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) / 8,10)
                           when scr.rate_unit_type = 2 then ROUND((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) / scr.estimated_working_hour_per_week,10)
                           when scr.rate_unit_type = 3 then ROUND(((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) * 12 ) / 2080,10)
                           when scr.rate_unit_type = 4 then ROUND((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) / 2080,10)
                           else (scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) end ),2) AS CHAR) as hourlyGp,
                 CAST(scr.total_bill_amount AS CHAR)  as totalGp,
                                 
                 CAST(ROUND(
                     #计算hour bill rate
                      ROUND((case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,10)
                     when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,10)
                     when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,10)
                     when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,10)
                     else scr.final_bill_rate end ),2) * (Calculate_Workdays(scr.start_date,scr.end_date) * (scr.estimated_working_hour_per_week/5)),2) AS CHAR) as totalRevenue,
                                 
                  CAST(case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,2)
                  when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,2)
                  when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,2)
                  when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,2)
                  else scr.final_bill_rate end AS CHAR)  as billRate,
                  CAST(case when scr.rate_unit_type = 1 then ROUND(scr.final_pay_rate / 8,2)
                  when scr.rate_unit_type = 2 then ROUND(scr.final_pay_rate / scr.estimated_working_hour_per_week,2)
                  when scr.rate_unit_type = 3 then ROUND((scr.final_pay_rate * 12 ) / 2080,2)
                  when scr.rate_unit_type = 4 then ROUND(scr.final_pay_rate / 2080,2)
                  else scr.final_pay_rate  end AS CHAR) as payRate,
                  
                  
                               if(scr.extend_start_contract_rate_id is null ,
                               CAST((
                select
                               sfr.currency
                    from  start st
                     inner join start_contract_rate sfr on st.id = sfr.start_id
                                          left join currency_rate_day crdd on crdd.rate_day = sfr.end_date and sfr.currency = crdd.currency_id
                                          left join enum_currency ecc on  sfr.currency = ecc.id
                       where st.id != t.id and st.talent_recruitment_process_id = t.talent_recruitment_process_id and sfr.end_date < scr.start_date
                       order by sfr.end_date desc limit 1
                   ) as CHAR),
                                    
                                      CAST((
                       select
                                              sfr.currency
                    from  start st
                     inner join start_contract_rate sfr on st.id = sfr.start_id
                                          left join currency_rate_day crdd on crdd.rate_day = sfr.end_date and sfr.currency = crdd.currency_id
                                          left join enum_currency ecc on  sfr.currency = ecc.id
                       where sfr.id = scr.extend_start_contract_rate_id
                       order by sfr.end_date desc limit 1
                   )as CHAR) )	AS originalCurrency,
                                 
                                  if(scr.extend_start_contract_rate_id is null ,
                               CAST((
                select
                               case when sfr.rate_unit_type = 1 then ROUND(sfr.final_bill_rate / 8,2)
                                                          when sfr.rate_unit_type = 2 then ROUND(sfr.final_bill_rate / sfr.estimated_working_hour_per_week,2)
                                                          when sfr.rate_unit_type = 3 then ROUND((sfr.final_bill_rate * 12 ) / 2080,2)
                                                          when sfr.rate_unit_type = 4 then ROUND(sfr.final_bill_rate / 2080,2)
                                                          else sfr.final_bill_rate end
                    from  start st
                     inner join start_contract_rate sfr on st.id = sfr.start_id
                                          left join currency_rate_day crdd on crdd.rate_day = sfr.end_date and sfr.currency = crdd.currency_id
                                          left join enum_currency ecc on  sfr.currency = ecc.id
                       where st.id != t.id and st.talent_recruitment_process_id = t.talent_recruitment_process_id and sfr.end_date < scr.start_date
                       order by sfr.end_date desc limit 1
                   ) as CHAR),
                                    
                                      CAST((
                       select
                                            case when sfr.rate_unit_type = 1 then ROUND(sfr.final_bill_rate / 8,2)
                                           when sfr.rate_unit_type = 2 then ROUND(sfr.final_bill_rate / sfr.estimated_working_hour_per_week,2)
                                           when sfr.rate_unit_type = 3 then ROUND((sfr.final_bill_rate * 12 ) / 2080,2)
                                           when sfr.rate_unit_type = 4 then ROUND(sfr.final_bill_rate / 2080,2)
                                           else sfr.final_bill_rate end
                    from  start st
                     inner join start_contract_rate sfr on st.id = sfr.start_id
                                          left join currency_rate_day crdd on crdd.rate_day = sfr.end_date and sfr.currency = crdd.currency_id
                                          left join enum_currency ecc on  sfr.currency = ecc.id
                       where sfr.id = scr.extend_start_contract_rate_id
                       order by sfr.end_date desc limit 1
                   )as CHAR) )	AS originalBillRate,
                 
                   if(scr.extend_start_contract_rate_id is null ,
                   CAST((
                    select
                                   case when sfr.currency =scr.currency then
                                           case when sfr.rate_unit_type = 1 then ROUND(sfr.final_bill_rate / 8,2)
                                           when sfr.rate_unit_type = 2 then ROUND(sfr.final_bill_rate / sfr.estimated_working_hour_per_week,2)
                                           when sfr.rate_unit_type = 3 then ROUND((sfr.final_bill_rate * 12 ) / 2080,2)
                                           when sfr.rate_unit_type = 4 then ROUND(sfr.final_bill_rate / 2080,2)
                                           else sfr.final_bill_rate end
                                       else
                                          ROUND(CAST(1/(case when crdd.from_usd_rate_mid is null then ecc.to_usd_rate else crdd.from_usd_rate_mid end ) AS FLOAT) *
                                           (case when sfr.rate_unit_type = 1 then ROUND(sfr.final_bill_rate / 8,2)
                                           when sfr.rate_unit_type = 2 then ROUND(sfr.final_bill_rate / sfr.estimated_working_hour_per_week,2)
                                           when sfr.rate_unit_type = 3 then ROUND((sfr.final_bill_rate * 12 ) / 2080,2)
                                           when sfr.rate_unit_type = 4 then ROUND(sfr.final_bill_rate / 2080,2)
                                           else sfr.final_bill_rate end ),2) end
                                       as final_bill_rate
                        from  start st
                         inner join start_contract_rate sfr on st.id = sfr.start_id
                                              left join currency_rate_day crdd on crdd.rate_day = sfr.end_date and sfr.currency = crdd.currency_id
                                              left join enum_currency ecc on  sfr.currency = ecc.id
                           where st.id != t.id and st.talent_recruitment_process_id = t.talent_recruitment_process_id and sfr.end_date < scr.start_date
                           order by sfr.end_date desc limit 1
                       ) as CHAR)
                   ,
                       CAST((
                           select
                                                  case when sfr.currency =scr.currency then
                                                           case when sfr.rate_unit_type = 1 then ROUND(sfr.final_bill_rate / 8,2)
                                                           when sfr.rate_unit_type = 2 then ROUND(sfr.final_bill_rate / sfr.estimated_working_hour_per_week,2)
                                                           when sfr.rate_unit_type = 3 then ROUND((sfr.final_bill_rate * 12 ) / 2080,2)
                                                           when sfr.rate_unit_type = 4 then ROUND(sfr.final_bill_rate / 2080,2)
                                                           else sfr.final_bill_rate end
                                                   else
                                                          ROUND(CAST(1/(case when crdd.from_usd_rate_mid is null then ecc.to_usd_rate else crdd.from_usd_rate_mid end ) AS FLOAT) *
                                                           (case when sfr.rate_unit_type = 1 then ROUND(sfr.final_bill_rate / 8,2)
                                                           when sfr.rate_unit_type = 2 then ROUND(sfr.final_bill_rate / sfr.estimated_working_hour_per_week,2)
                                                           when sfr.rate_unit_type = 3 then ROUND((sfr.final_bill_rate * 12 ) / 2080,2)
                                                           when sfr.rate_unit_type = 4 then ROUND(sfr.final_bill_rate / 2080,2)
                                                           else sfr.final_bill_rate end ),2) end 		
                                                  as final_bill_rate
                        from  start st
                         inner join start_contract_rate sfr on st.id = sfr.start_id
                                              left join currency_rate_day crdd on crdd.rate_day = sfr.end_date and sfr.currency = crdd.currency_id
                                              left join enum_currency ecc on  sfr.currency = ecc.id
                           where sfr.id = scr.extend_start_contract_rate_id
                           order by sfr.end_date desc limit 1
                       )as CHAR) )  as changeOriginalBillRate,
                                        
                       if(scr.extend_start_contract_rate_id is null ,
                       CAST((
                        select
                                         
                                                                          case when sfr.rate_unit_type = 1 then ROUND(sfr.final_pay_rate / 8,2)
                                                                          when sfr.rate_unit_type = 2 then ROUND(sfr.final_pay_rate / sfr.estimated_working_hour_per_week,2)
                                                                          when sfr.rate_unit_type = 3 then ROUND((sfr.final_pay_rate * 12 ) / 2080,2)
                                                                          when sfr.rate_unit_type = 4 then ROUND(sfr.final_pay_rate / 2080,2)
                                                                          else sfr.final_pay_rate end
                                         
                                       as final_bill_rate
                        from  start st
                         inner join start_contract_rate sfr on st.id = sfr.start_id
                                              left join currency_rate_day crdd on crdd.rate_day = sfr.end_date and sfr.currency = crdd.currency_id
                                              left join enum_currency ecc on  sfr.currency = ecc.id
                           where st.id != t.id and st.talent_recruitment_process_id = t.talent_recruitment_process_id and sfr.end_date < scr.start_date
                           order by sfr.end_date desc limit 1
                       ) as CHAR)
                   ,
                       CAST((
                           select
                                                
                                                       case when sfr.rate_unit_type = 1 then ROUND(sfr.final_pay_rate / 8,2)
                                                       when sfr.rate_unit_type = 2 then ROUND(sfr.final_pay_rate / sfr.estimated_working_hour_per_week,2)
                                                       when sfr.rate_unit_type = 3 then ROUND((sfr.final_pay_rate * 12 ) / 2080,2)
                                                       when sfr.rate_unit_type = 4 then ROUND(sfr.final_pay_rate / 2080,2)
                                                       else sfr.final_pay_rate  end
                                               
                                                  as final_bill_rate
                        from  start st
                         inner join start_contract_rate sfr on st.id = sfr.start_id
                                              left join currency_rate_day crdd on crdd.rate_day = sfr.end_date and sfr.currency = crdd.currency_id
                                              left join enum_currency ecc on  sfr.currency = ecc.id
                           where sfr.id = scr.extend_start_contract_rate_id
                           order by sfr.end_date desc limit 1
                       )as CHAR) )  as originalPayRate
                  
                 from start t
                 inner join start_contract_rate scr on scr.start_id = t.id
                  INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate pay ON pay.CODE = scr.tax_burden_rate
                  INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate bill ON bill.CODE = scr.msp_rate
                 inner join start_commission sc on sc.start_id = t.id
                 inner join date_dimension d on d.date = scr.start_date
                 left join currency_rate_day crd on crd.rate_day = scr.start_date and scr.currency = crd.currency_id
                 left join enum_currency pec on pec.id = scr.currency
                 left join enum_currency ec on  scr.currency = ec.id
                     
                 """;
        return sql;
    }

    private ReportQuarterlyOnboardingTotalVO selectQuarterlyNewHireAndRenewalSum(ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {

        entityManager.clear();
        String sql = """
                select CAST(ROUND(sum(tab.hourlyGp),2) AS CHAR) as hourlyGpSum,
                 CAST(ROUND(sum(tab.totalGp),2) AS CHAR) as totalGpSum,
                 CAST(ROUND(sum(tab.totalRevenue),2) AS CHAR) as totalRevenueSum from (
                """;

        StringBuilder dataSql = new StringBuilder();

        if (dto.getType().equals(QuarterlyOnboardingType.NEWHIRES)) {
            dataSql.append(sql)
                    .append("""
                            select DISTINCT
                            ROUND(
                            (case
                            when scr.rate_unit_type = 1 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 8,10)
                            when scr.rate_unit_type = 2 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / scr.estimated_working_hour_per_week,10)
                            when scr.rate_unit_type = 3 then ROUND(((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) * 12 ) / 2080,10)
                            when scr.rate_unit_type = 4 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 2080,10)
                            else (case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) end) ,2) as hourlyGp,
                           case when scr.currency =0 then scr.total_bill_amount
                           else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * scr.total_bill_amount,2) end as totalGp,
                                            
                           CAST(ROUND(
                           #计算hour bill rate
                           (case when scr.currency =0 then
                           case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,10)
                           when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,10)
                           when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,10)
                           when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,10)
                           else scr.final_bill_rate end
                           else
                            ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * (case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,10)
                           when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,10)
                           when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,10)
                           when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,10)
                           else scr.final_bill_rate end ),2) end) * (Calculate_Workdays(scr.start_date,scr.end_date) * (scr.estimated_working_hour_per_week/5)),2) AS CHAR) as totalRevenue                 
                                  ,t.id          
                             from start t
                            inner join start_commission sc on sc.start_id = t.id
                            inner join start_contract_rate scr on scr.start_id = t.id
                            INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate pay ON pay.CODE = scr.tax_burden_rate
                            INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate bill ON bill.CODE = scr.msp_rate
                            """)
                    .append(" inner join date_dimension d on d.date = t.start_date ")
                    .append(" left join currency_rate_day crd on crd.rate_day = t.start_date and scr.currency = crd.currency_id ")
                    .append(" left join enum_currency ec on  scr.currency = ec.id ")
                    .append(addPermissionJoin(dto))
                    .append(" where t.position_type in (:jobTypes) and t.tenant_id=:tenantId and sc.user_role=1 and t.start_type=0 and scr.extend_start_contract_rate_id is null ")
                    .append(" and ")
                    .append(addQuarterlyWhereSql(dto))
                    .append(addUserWhereSql(dto, teamDataPermission))
                    .append(" group by t.id,sc.user_id ) tab ");
        } else {
            dataSql.append(sql)
                    .append("""
                            select DISTINCT
                           ROUND(
                            (case
                            when scr.rate_unit_type = 1 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 8,10)
                            when scr.rate_unit_type = 2 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / scr.estimated_working_hour_per_week,10)
                            when scr.rate_unit_type = 3 then ROUND(((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) * 12 ) / 2080,10)
                            when scr.rate_unit_type = 4 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 2080,10)
                            else (case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) end) ,2) as hourlyGp,
                                            
                           case when scr.currency =0 then scr.total_bill_amount
                           else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * scr.total_bill_amount,2) end as totalGp,                
                           
                            CAST(ROUND(
                           #计算hour bill rate
                           (case when scr.currency =0 then
                           case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,10)
                           when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,10)
                           when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,10)
                           when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,10)
                           else scr.final_bill_rate end
                           else
                            ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * (case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,10)
                           when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,10)
                           when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,10)
                           when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,10)
                           else scr.final_bill_rate end ),2) end) * (Calculate_Workdays(scr.start_date,scr.end_date) * (scr.estimated_working_hour_per_week/5)),2) AS CHAR) as totalRevenue
                             ,t.id        
                            from start t
                            inner join start_commission sc on sc.start_id = t.id
                            inner join start_contract_rate scr on scr.start_id = t.id
                            INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate pay ON pay.CODE = scr.tax_burden_rate
                            INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate bill ON bill.CODE = scr.msp_rate
                            """)
                    .append(" inner join date_dimension d on d.date = scr.start_date ")
                    .append(" left join currency_rate_day crd on crd.rate_day = scr.start_date and scr.currency = crd.currency_id ")
                    .append(" left join enum_currency ec on  scr.currency = ec.id ")
                    .append(addPermissionJoin(dto))
                    .append(" where t.position_type in (:jobTypes) and t.tenant_id=:tenantId and sc.user_role=1 and ((scr.extend_start_contract_rate_id is not null and t.start_type in (0,1)) or t.start_type=1) ")
                    .append(" and ")
                    .append(addQuarterlyWhereSql(dto))
                    .append(addUserWhereSql(dto, teamDataPermission))
                    .append(" group by t.id,t.start_type,scr.extend_start_contract_rate_id,sc.user_id ")
                    .append(" ) tab ");
        }

        return searchQuarterlyTotalVO(dataSql.toString(), dto, teamDataPermission);
    }

    public Page<ReportQuarterlyOffboardingDetailVO> selectQuarterlyOffonboardingDetail(ReportQuarterOnboardAndOffboardSearchDTO dto, Pageable pageable, TeamDataPermissionRespDTO teamDataPermission) {

        entityManager.clear();
        String sql = """
                select CONCAT(tab.candidateId,'',tab.clientId,'',RAND(********) ) as id,
                recruiter,clientAccount,clientId,candidateId,candidateName,CAST(endDay AS CHAR) as endDay,CAST(weekDay AS CHAR) as weekDay,currency,
                weekDay as weekDays,endDay as endDays,
                currencyName,symbol,CAST(ROUND(sum(hourlyGp),2) AS CHAR) as hourlyGp,CAST(ROUND(sum(totalGp),2) AS CHAR) as totalGp,CAST(ROUND(sum(totalRevenue),2) AS CHAR) as totalRevenue
                from (select DISTINCT scr.id as startId,
                       (select GROUP_CONCAT(DISTINCT s.user_full_name SEPARATOR ',') from start_commission s
                       where s.start_id = t.id and s.user_role=1) recruiter,
                       t.company as clientAccount,
                       t.company_id as clientId,
                       t.talent_id as candidateId,
                       t.talent_name as candidateName,
                       st.termination_date as endDay,
                       d.week_of_year as weekDay,
                       CAST(scr.currency AS CHAR) as currency,
                       pec.name as currencyName,
                       pec.symbol,
                       CAST(ROUND(
                        case when scr.rate_unit_type = 1 then ROUND((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) / 8,10)
                        when scr.rate_unit_type = 2 then ROUND((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) / scr.estimated_working_hour_per_week,10)
                        when scr.rate_unit_type = 3 then ROUND(((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) * 12 ) / 2080,10)
                        when scr.rate_unit_type = 4 then ROUND((scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) / 2080,10)
                        else (scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ) end ,2) AS CHAR) as hourlyGp,
                       CAST(scr.total_bill_amount AS CHAR)  as totalGp,
                       CAST(ROUND(
                        #计算hour bill rate
                        case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,10)
                        when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,10)
                        when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,10)
                        when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,10)
                        else scr.final_bill_rate end
                          * (Calculate_Workdays(scr.start_date,scr.end_date) * (scr.estimated_working_hour_per_week/5)),2) AS CHAR) as totalRevenue
                                       
                       from start t
                       inner join start_commission sc on sc.start_id = t.id
                       inner join start_termination st on st.start_id= t.id 
                       inner join start_contract_rate scr on scr.start_id = t.id and st.termination_date = scr.end_date
                       INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate pay ON pay.CODE = scr.tax_burden_rate
                       INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate bill ON bill.CODE = scr.msp_rate
                       inner join date_dimension d on d.date = st.termination_date
                       left join enum_currency pec on pec.id = scr.currency
                       left join currency_rate_day crd on crd.rate_day = st.termination_date and scr.currency = crd.currency_id
                       left join enum_currency ec on  t.currency = ec.id
                         
                       """;

        String whereSql = """
                where sc.user_role=1  and t.position_type in (:jobTypes) and t.tenant_id=:tenantId
                """;

        StringBuilder dataSql = new StringBuilder();

        dataSql.append(sql)
                .append(addPermissionJoin(dto))
                .append(whereSql)
                .append(" and ")
                .append(addQuarterlyWhereSql(dto))
                .append(addUserWhereSql(dto, teamDataPermission))
                .append(" group by t.id ,sc.user_id ")
                .append(" ) tab GROUP BY tab.startId,recruiter,clientAccount,clientId,candidateId,candidateName,endDay ");

        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (!Objects.isNull(order)) {
            if (order.getProperty().equals("recruiter")) {
                dataSql.append(" order by CONVERT( recruiter  USING gbk) " + order.getDirection());
            } else if (order.getProperty().equals("candidateName")) {
                dataSql.append(" order by CONVERT( candidateName  USING gbk) " + order.getDirection());
            } else if (order.getProperty().equals("clientAccount")) {
                dataSql.append(" order by CONVERT( clientAccount  USING gbk) " + order.getDirection());
            } else if (order.getProperty().equals("endDay")) {
                dataSql.append(" order by endDays " + order.getDirection());
            } else {
                dataSql.append(" order by weekDays " + order.getDirection());
            }
        } else {
            dataSql.append(" order by endDays asc,CONVERT( candidateName  USING gbk) asc");
        }

        String countSql = "SELECT COUNT(*)  FROM ( " + dataSql.toString() + " ) getcount";

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        Query countQuery = entityManager.createNativeQuery(countSql);

        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        dataQuery.setParameter("jobTypes", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
        countQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        countQuery.setParameter("jobTypes", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));

        if (CollUtil.isNotEmpty(dto.getUserIdList()) && CollUtil.isNotEmpty(dto.getTeamIdList())) {
            dataQuery.setParameter("userIds", dto.getUserIdList());
            countQuery.setParameter("userIds", dto.getUserIdList());
            dataQuery.setParameter("teamIds", dto.getTeamIdList());
            countQuery.setParameter("teamIds", dto.getTeamIdList());
        } else {
            if (null != dto.getUserIdList() && !dto.getUserIdList().isEmpty()) {
                dataQuery.setParameter("userIds", dto.getUserIdList());
                countQuery.setParameter("userIds", dto.getUserIdList());
            }

            if (null != dto.getTeamIdList() && !dto.getTeamIdList().isEmpty()) {
                dataQuery.setParameter("teamIds", dto.getTeamIdList());
                countQuery.setParameter("teamIds", dto.getTeamIdList());
            }
        }


        if (dto.getUserIdList().isEmpty() && dto.getTeamIdList().isEmpty()) {
            if (!SecurityUtils.isAdmin()) {
                if (teamDataPermission.getSelf()) {
                    dataQuery.setParameter("userSelf", SecurityUtils.getUserId());
                    countQuery.setParameter("userSelf", SecurityUtils.getUserId());
                } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                    dataQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());
                    countQuery.setParameter("teamId", teamDataPermission.getNestedTeamIds());
                }
            }
        }

        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ReportQuarterlyOffboardingDetailVO.class));
        dataQuery.setFirstResult((pageable.getPageNumber() <= 0 ? 0 : pageable.getPageNumber() - 1) * pageable.getPageSize());
        dataQuery.setMaxResults(pageable.getPageSize());

        BigInteger count = new BigInteger(String.valueOf(countQuery.getSingleResult()));
        Long total = count.longValue();
        List<ReportQuarterlyOffboardingDetailVO> reportQuarterlyOffboardingDetailVOList = dataQuery.getResultList();

        ReportQuarterlyOffboardingDetailVO vo = new ReportQuarterlyOffboardingDetailVO();
        vo.setTotal(selectQuarterlyOffonboardingSum(dto, teamDataPermission));
        reportQuarterlyOffboardingDetailVOList.add(vo);
        return new PageImpl<>(reportQuarterlyOffboardingDetailVOList, Pageable.unpaged(), total);
    }

    private ReportQuarterlyOnboardingTotalVO selectQuarterlyOffonboardingSum(ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {

        entityManager.clear();
        String sql = """
                select CAST(ROUND(sum(tab.hourlyGp),2) AS CHAR) as hourlyGpSum,
                 CAST(ROUND(sum(tab.totalGp),2) AS CHAR) as totalGpSum,
                 CAST(ROUND(sum(tab.totalRevenue),2) AS CHAR) as totalRevenueSum from (
                     select DISTINCT
                            ROUND(
                            (case
                            when scr.rate_unit_type = 1 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 8,10)
                            when scr.rate_unit_type = 2 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / scr.estimated_working_hour_per_week,10)
                            when scr.rate_unit_type = 3 then ROUND(((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) * 12 ) / 2080,10)
                            when scr.rate_unit_type = 4 then ROUND((case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) / 2080,10)
                            else (case when scr.currency =0 then ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 )
                            else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS DECIMAL(20,10)) *
                            ( scr.final_bill_rate - scr.final_pay_rate - scr.final_bill_rate * bill.VALUE * 0.01 - scr.final_pay_rate * pay.VALUE * 0.01 ),2) end) end) ,2) as hourlyGp,
                                            
                           case when scr.currency =0 then scr.total_bill_amount
                           else ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * scr.total_bill_amount,2) end as totalGp,
                                            
                          CAST(ROUND(
                          #计算hour bill rate
                          (case when scr.currency =0 then
                          case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,10)
                          when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,10)
                          when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,10)
                          when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,10)
                          else scr.final_bill_rate end
                          else
                           ROUND(CAST(1/(case when crd.from_usd_rate_mid is null then ec.to_usd_rate else crd.from_usd_rate_mid end ) AS FLOAT) * (case when scr.rate_unit_type = 1 then ROUND(scr.final_bill_rate / 8,10)
                          when scr.rate_unit_type = 2 then ROUND(scr.final_bill_rate / scr.estimated_working_hour_per_week,10)
                          when scr.rate_unit_type = 3 then ROUND((scr.final_bill_rate * 12 ) / 2080,10)
                          when scr.rate_unit_type = 4 then ROUND(scr.final_bill_rate / 2080,10)
                          else scr.final_bill_rate end ),2) end) * (Calculate_Workdays(scr.start_date,scr.end_date) * (scr.estimated_working_hour_per_week/5)),2) AS CHAR) as totalRevenue
                          ,t.id        
                 from start t
                inner join start_commission sc on sc.start_id = t.id
                inner join start_termination st on st.start_id= t.id
                inner join start_contract_rate scr on scr.start_id = t.id and st.termination_date = scr.end_date
                INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate pay ON pay.CODE = scr.tax_burden_rate
                INNER JOIN talent_recruitment_process_ipg_offer_letter_cost_rate bill ON bill.CODE = scr.msp_rate
                inner join date_dimension d on d.date = st.termination_date
                left join currency_rate_day crd on crd.rate_day = st.termination_date and scr.currency = crd.currency_id
                left join enum_currency ec on  scr.currency = ec.id
                
                """;

        StringBuilder dataSql = new StringBuilder();


        dataSql.append(sql)
                .append(addPermissionJoin(dto))
                .append(" where t.position_type in (:jobTypes) and t.tenant_id=:tenantId and sc.user_role=1 ")
                .append(" and ")
                .append(addQuarterlyWhereSql(dto))
                .append(addUserWhereSql(dto, teamDataPermission))
                .append(" ) tab ");


        return searchQuarterlyTotalVO(dataSql.toString(), dto, teamDataPermission);
    }

    private ReportQuarterlyOnboardingTotalVO searchQuarterlyTotalVO(String sql, ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermissionRespDTO) {
        Query dataQuery = entityManager.createNativeQuery(sql.toString());

        dataQuery.setParameter("tenantId", SecurityUtils.getTenantId());
        dataQuery.setParameter("jobTypes", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
        addQuarterlyParam(dataQuery, dto, teamDataPermissionRespDTO);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(ReportQuarterlyOnboardingTotalVO.class));
        List<ReportQuarterlyOnboardingTotalVO> totalVOList = dataQuery.getResultList();
        if (totalVOList.isEmpty()) {
            return new ReportQuarterlyOnboardingTotalVO();
        }
        return totalVOList.get(0);
    }

    //(d.year = 2024 AND d.quarter = 1) OR (d.year = 2024 AND d.quarter = 2) OR (d.year = 2024 AND d.quarter = 3) OR (d.year = 2024 AND d.quarter = 4)
    private String addQuarterlyWhereSql(ReportQuarterOnboardAndOffboardSearchDTO dto) {
        String[] startParts = dto.getStartDate().split("-");
        String[] endParts = dto.getEndDate().split("-");

        if (startParts.length != 2 || endParts.length != 2) {
            throw new IllegalArgumentException("Invalid date format. Expected format: yyyy-Q");
        }

        int startYear = Integer.parseInt(startParts[0]);
        int startQuarter = Integer.parseInt(startParts[1]);
        int endYear = Integer.parseInt(endParts[0]);
        int endQuarter = Integer.parseInt(endParts[1]);

        List<String> conditions = new ArrayList<>();
        for (int year = startYear; year <= endYear; year++) {
            int startQ = (year == startYear) ? startQuarter : 1;
            int endQ = (year == endYear) ? endQuarter : 4;
            for (int quarter = startQ; quarter <= endQ; quarter++) {
                conditions.add(String.format("d.year = %d AND d.quarter = %d", year, quarter));
            }
        }
        return "(" + String.join(" OR ", conditions) + ")";
    }

    private String addPermissionJoin(ReportQuarterOnboardAndOffboardSearchDTO dto){
        StringBuilder sql = new StringBuilder();
        if ((dto.getUserIdList().isEmpty() && dto.getTeamIdList().isEmpty()) || !dto.getTeamIdList().isEmpty()) {
            sql.append("""
                    inner join permission_user_team put on put.user_id = sc.user_id and put.is_primary = 1
                    inner join permission_team pt on pt.id = put.team_id 
                    """);
        }
        return sql.toString();
    }

    private String addUserWhereSql(ReportQuarterOnboardAndOffboardSearchDTO dto, TeamDataPermissionRespDTO teamDataPermission) {
        StringBuilder whereSql = new StringBuilder();
        if (dto.getUserIdList().isEmpty() && dto.getTeamIdList().isEmpty()) {
            if (!SecurityUtils.isAdmin()) {
                if (teamDataPermission.getSelf()) {
                    whereSql.append(" and put.user_id =:userSelf");

                } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                    whereSql.append(" and put.team_id in ( :teamId )");
                }
            }
        }

        if (CollUtil.isNotEmpty(dto.getUserIdList()) && CollUtil.isNotEmpty(dto.getTeamIdList())) {
            whereSql.append(" and (put.user_id in (:userIds) or put.team_id in (:teamIds))");
        } else {
            if (null != dto.getUserIdList() && !dto.getUserIdList().isEmpty()) {
                whereSql.append(" and sc.user_id in (:userIds)");
            }

            if (null != dto.getTeamIdList() && !dto.getTeamIdList().isEmpty()) {
                whereSql.append(" and put.team_id in (:teamIds)");
            }
        }
        return whereSql.toString();
    }

    public  String[] convertQuarterToDates(String startQuarter, String endQuarter) {
        LocalDate startDate = getQuarterStartDate(startQuarter);
        LocalDate endDate = getQuarterEndDate(endQuarter);

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return new String[]{startDate.format(formatter), endDate.format(formatter)};
    }

    private static LocalDate getQuarterStartDate(String quarter) {
        String[] parts = quarter.split("-");
        int year = Integer.parseInt(parts[0]);
        int q = Integer.parseInt(parts[1]);

        int month = (q - 1) * 3 + 1;
        return LocalDate.of(year, month, 1);
    }

    private static LocalDate getQuarterEndDate(String quarter) {
        String[] parts = quarter.split("-");
        int year = Integer.parseInt(parts[0]);
        int q = Integer.parseInt(parts[1]);

        int month = q * 3;
        return LocalDate.of(year, month, 1).withDayOfMonth(LocalDate.of(year, month, 1).lengthOfMonth());
    }
}