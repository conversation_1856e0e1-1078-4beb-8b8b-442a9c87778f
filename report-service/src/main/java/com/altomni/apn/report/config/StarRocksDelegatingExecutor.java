package com.altomni.apn.report.config;

import com.altomni.apn.common.config.thread.MDCContextRunnable;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.Executor;

public class StarRocksDelegatingExecutor implements Executor {
    private final Executor delegate;

    public StarRocksDelegatingExecutor(Executor delegate) {
        this.delegate = delegate;
    }

    @Override
    public void execute(@NotNull Runnable runnable) {
        // 先包装 MDC 上下文，再包装 StarRocks 数据源切换
        Runnable mdcWrappedRunnable = MDCContextRunnable.wrap(runnable);
        this.delegate.execute(new StarRocksDelegatingRunnable(mdcWrappedRunnable));
    }
}
