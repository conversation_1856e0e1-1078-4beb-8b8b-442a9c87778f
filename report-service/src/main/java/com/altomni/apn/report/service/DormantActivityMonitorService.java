package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.altomni.apn.report.domain.vo.s3.DormantApplicationCountVO;
import com.altomni.apn.report.domain.vo.s3.DormantApplicationVO;
import com.altomni.apn.report.dto.s3.DormantApplicationCountDTO;

import java.util.List;

public interface DormantActivityMonitorService {

    List<DormantApplicationCountVO> countAllDormantApplications(DormantApplicationCountDTO dormantApplicationCountDTO);

    List<DormantApplicationVO> getDormantApplicationsByRecruiterId(Long id, ReportTableType status);
}
