package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.enumeration.permission.DataScope;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.report.domain.DormantJobCountDTO;
import com.altomni.apn.report.domain.DormantJobDTO;
import com.altomni.apn.report.domain.vm.s2.DormantJobCountVM;
import com.altomni.apn.report.domain.vm.s2.DormantJobVM;
import com.altomni.apn.report.service.impl.ReportBaseServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class DormantActivityRepository extends ReportBaseServiceImpl {

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    @Resource
    private ReportRepository reportRepository;

    private static final List<Integer> EXCLUDE_JOB_ID = new ArrayList<>(Arrays.asList(141, 211, 2461, 3274, 3609, 3323, 4758, 95));

    public List<DormantJobCountDTO> countAllDormantJobsByAmId(List<Long> teamIds, Long userId, Long tenantId) {
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, tenantId);
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(" SELECT u.id, u.first_name, u.last_name, GROUP_CONCAT( DISTINCT pt.`name` ) AS 'team', count( DISTINCT j.id ) AS 'count' ")
                .append(" from user u LEFT JOIN permission_user_team put ON put.user_id = u.id AND put.is_primary = 1 ")
                .append(" LEFT JOIN permission_team pt ON put.team_id = pt.id ")
                .append(" LEFT JOIN business_flow_administrator csla ON csla.user_id = u.id AND csla.sales_lead_role in (0,3) ")
                .append(" LEFT JOIN job j ON j.company_id = csla.company_id ")
                .append(" LEFT JOIN ( SELECT job_id, max( last_modified_date ) AS 'last_modified_date' FROM talent_recruitment_process GROUP BY job_id ) time ON time.job_id = j.id ")
                .append(" WHERE j.`status` <= 2 AND j.tenant_id = ?1 ")
                .append(" AND (TIMESTAMPDIFF(DAY,time.last_modified_date,SYSDATE()) > 90 OR time.last_modified_date IS NULL ) ")
                .append(" AND TIMESTAMPDIFF(DAY,j.last_modified_date,SYSDATE()) > 90 ");

//        if (!SecurityUtils.isAdmin()) {
//            int dataScope = getDataScope();
//            Integer dataScopeRole = getRoleDateScope();
//            if (ObjectUtil.isNotEmpty(dataScopeRole)) {
//                dataScope = Math.max(dataScope, dataScopeRole);
//            }
//            if (Objects.equals(DataScope.PERMISSION_SELF.toDbValue(), dataScope)) {
//                stringBuilder.append(" and u.id = ").append(SecurityUtils.getUserId());
//            } else if (Objects.equals(DataScope.PERMISSION_TEAM.toDbValue(), dataScope) || Objects.equals(DataScope.PERMISSION_EXTRA_TEAM.toDbValue(), dataScope)) {
//                stringBuilder.append(" and exists ( select 1 from (")
//                        .append(" select team_id from permission_user_team where user_id = ").append(SecurityUtils.getUserId())
//                        .append(" union all ")
//                        .append(" select team_id from permission_extra_user_team where user_id = ").append(SecurityUtils.getUserId())
//                        .append(" union all ")
//                        .append(" select pert.team_id as team_id from permission_extra_role_team pert left join user_role ur on ur.role_id = pert.role_id where ur.user_id = ").append(SecurityUtils.getUserId())
//                        .append(") a left join permission_user_team p on p.team_id = a.team_id where p.user_id = u.id ) ");
//            }
//        }
//        if (CollUtil.isNotEmpty(teamIds)) {
//            paramMap.put(paramMap.size() + 1, teamIds);
//            stringBuilder.append(" AND pt.id IN ?").append(paramMap.size());
//        }
//        if (ObjectUtil.isNotEmpty(userId)) {
//            paramMap.put(paramMap.size() + 1, userId);
//            stringBuilder.append(" AND u.id = ?").append(paramMap.size());
//        }

        teamIds = this.applyDataPermission(paramMap, stringBuilder, teamIds);

        if (CollUtil.isNotEmpty(teamIds)) {
            paramMap.put(paramMap.size() + 1, teamIds);
            stringBuilder.append(" AND pt.id IN ?").append(paramMap.size());
        }
        if (ObjectUtil.isNotEmpty(userId)) {
            paramMap.put(paramMap.size() + 1, userId);
            stringBuilder.append(" AND u.id = ?").append(paramMap.size());
        }

        stringBuilder.append(" GROUP BY u.id ");
        List<DormantJobCountVM> dormantJobCountList = searchData(stringBuilder.toString(), DormantJobCountVM.class, paramMap);
        return dormantJobCountList.stream().map(this::fromDormantJobCountVM).collect(Collectors.toList());
    }

    public List<Long> applyDataPermission(Map<Integer, Object> paramMap, StringBuilder stringBuilder, List<Long> teamIds){
        if (!SecurityUtils.isAdmin()) {
            TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
            if (CollUtil.isNotEmpty(teamIds)){
                Collection<Long> intersectionTeamIds = CollUtil.intersection(teamIds, teamDataPermission.getNestedTeamIds());
                teamIds.addAll(intersectionTeamIds);
            }else {
                teamIds = Objects.isNull(teamIds) ? new ArrayList<>() : teamIds;
                teamIds.addAll(teamDataPermission.getNestedTeamIds());
            }

            log.info("S2 report: teamDataPermission={}", teamDataPermission);

            if (teamDataPermission.getSelf()) {
                stringBuilder.append(" and u.id = ").append(SecurityUtils.getUserId());
            } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                stringBuilder.append(" and exists ( select 1 from (")
                        .append(" select team_id from permission_user_team where user_id = ").append(SecurityUtils.getUserId())
                        .append(" union all ")
                        .append(" select team_id from permission_extra_user_team where user_id = ").append(SecurityUtils.getUserId())
                        .append(" union all ")
                        .append(" select pert.team_id as team_id from permission_extra_role_team pert left join user_role ur on ur.role_id = pert.role_id where ur.user_id = ").append(SecurityUtils.getUserId())
                        .append(") a left join permission_user_team p on p.team_id = a.team_id where p.user_id = u.id ) ");

                if (teamDataPermission.isPrivateJobPermission() && teamDataPermission.getTeamIdForPrivateJob() != null){
                    paramMap.put(paramMap.size() + 1, teamIds);
                    stringBuilder.append(" AND (j.pteam_id in ?").append(paramMap.size())
                            .append(" or j.id in ?");
                    paramMap.put(paramMap.size() + 1,
                            reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob()));
                    stringBuilder.append(paramMap.size())
                            .append(" or j.puser_id = ").append(SecurityUtils.getUserId())
                            .append(")");
                }else {
                    paramMap.put(paramMap.size() + 1, teamIds);
                    stringBuilder.append(" AND j.pteam_id in ?").append(paramMap.size());
                }
            }else if (!teamDataPermission.isPrivateJobPermission()){
                paramMap.put(paramMap.size() + 1, teamDataPermission.getTeamIdForPrivateJob());
                stringBuilder.append(" j.pteam_id != ?").append(paramMap.size());
            }
        }
        return teamIds;
    }

    private Integer getDataScope() {
        String sql = " select data_scope from user where id = " + SecurityUtils.getUserId();
        Query query = entityManager.createNativeQuery(sql);
        return Integer.parseInt(String.valueOf(query.getSingleResult()));
    }

    public Integer getRoleDateScope() {
        String sql = " select max(r.data_scope) from user_role ur left join role r on ur.role_id = r.id where ur.user_id = " + SecurityUtils.getUserId() + " group by ur.user_id ";
        Query query = entityManager.createNativeQuery(sql);
        return Integer.parseInt(String.valueOf(query.getSingleResult()));
    }

    public List<DormantJobDTO> findAllDormantJobsByAmId(Long tenantId, Long amId) {
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, tenantId);
        paramMap.put(2, amId);
        String query = """
                SELECT  
                 		j.id,  
                 		j.title AS 'job_title',  
                 		rp.job_type,  
                 		j.status AS 'job_status',  
                 		j.company_id,  
                		j.posting_time,  
                		c.full_business_name as company,  
                		u.id AS 'am_id',  
                		u.first_name AS 'first_name',
                		u.last_name AS 'last_name',
                		time.last_modified_date
                		FROM job j 
                        inner join recruitment_process rp on rp.id = j.recruitment_process_id 
                		inner JOIN business_flow_administrator relation ON relation.company_id = j.company_id and relation.sales_lead_role in (0,3) 
                		LEFT JOIN user u ON u.id = relation.user_id  
                		LEFT JOIN (SELECT job_id, max(last_modified_date) as 'last_modified_date' FROM talent_recruitment_process GROUP BY job_id) AS time  ON time.job_id = j.id  
                		LEFT JOIN company c  ON c.id = j.company_id  
                		WHERE  
                			j.status <= 2 
                			AND j.tenant_id = ?1
                			AND u.id = ?2  
                			AND (TIMESTAMPDIFF(DAY, time.last_modified_date, SYSDATE()) > 90 OR time.last_modified_date IS NULL)  
                			AND TIMESTAMPDIFF(DAY, j.last_modified_date, SYSDATE()) > 90
                """;
        StringBuilder stringBuilder = new StringBuilder();
        if (!SecurityUtils.isAdmin()) {
            Integer dataScope = getDataScope();
            Integer dataScopeRole = getRoleDateScope();
            if (ObjectUtil.isNotEmpty(dataScopeRole)) {
                dataScope = Math.max(dataScope, dataScopeRole);
            }
            if (Objects.equals(DataScope.PERMISSION_SELF.toDbValue(), dataScope)) {
                stringBuilder.append(" and u.id = ").append(SecurityUtils.getUserId());
            } else if (Objects.equals(DataScope.PERMISSION_TEAM.toDbValue(), dataScope) || Objects.equals(DataScope.PERMISSION_EXTRA_TEAM.toDbValue(), dataScope)) {
                stringBuilder.append(" and exists ( select 1 from (")
                        .append(" select team_id from permission_user_team where user_id = ").append(SecurityUtils.getUserId())
                        .append(" union all ")
                        .append(" select team_id from permission_extra_user_team where user_id = ").append(SecurityUtils.getUserId())
                        .append(" union all ")
                        .append(" select pert.team_id as team_id from permission_extra_role_team pert left join user_role ur on ur.role_id = pert.role_id where ur.user_id = ").append(SecurityUtils.getUserId())
                        .append(") a left join permission_user_team p on p.team_id = a.team_id where p.user_id = u.id ) ");
            }
        }

        query = query + stringBuilder + "\nGROUP BY j.id";

        List<DormantJobVM> result = searchData(query, DormantJobVM.class, paramMap);

        return result.stream().map(this::fromDormantJobVM).collect(Collectors.toList());
    }

    private DormantJobCountDTO fromDormantJobCountVM(DormantJobCountVM dormantJobCountVM) {
        DormantJobCountDTO dormantJobCountDTO = new DormantJobCountDTO();
        ServiceUtils.myCopyProperties(dormantJobCountVM, dormantJobCountDTO);
        dormantJobCountDTO.setAm(CommonUtils.formatFullName(dormantJobCountVM.getFirstName(), dormantJobCountVM.getLastName()));
        return dormantJobCountDTO;
    }

    private DormantJobDTO fromDormantJobVM(DormantJobVM dormantJobVM) {
        DormantJobDTO dormantJobDTO = new DormantJobDTO();
        ServiceUtils.myCopyProperties(dormantJobVM, dormantJobDTO);
        dormantJobDTO.setAm(CommonUtils.formatFullName(dormantJobVM.getFirstName(), dormantJobVM.getLastName()));
        return dormantJobDTO;
    }
}
