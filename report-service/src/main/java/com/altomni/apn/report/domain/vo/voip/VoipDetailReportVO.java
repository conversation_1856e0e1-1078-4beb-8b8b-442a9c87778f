package com.altomni.apn.report.domain.vo.voip;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoipDetailReportVO implements Serializable, AttachConfidentialTalent {

    private static final long serialVersionUID = 1L;

    private String phoneNumber;

    private Boolean wrongNumber;

    private Long talentId;

    private Long contactId;

    private String talentName;

    private Long jobId;

    private String jobTitle;

    private Instant dialingTime;

    private Double talkMinutes;

    private Long callResult;

    private Long callType;

    private JSONObject AISummary;

    private String note;

    private Boolean privateJob;

    private Long createdBy;

    private String createdByName;

    private Boolean confidentialTalentViewAble;

    private ConfidentialInfoDto confidentialInfo;


    @Override
    public void encrypt() {
       this.phoneNumber = null;
       this.wrongNumber = null;
       this.talentName = null;
       this.jobId = null;
       this.jobTitle = null;
       this.talkMinutes = null;
       this.callResult = null;
       this.callType = null;
       this.AISummary = null;
       this.note = null;
       this.privateJob = null;
       this.createdBy = null;
       this.createdByName = null;
    }


}
