package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilterConverter;
import com.altomni.apn.report.config.excel.CustomMyCandidateStatusFilterConverter;
import com.altomni.apn.report.config.excel.CustomNodeTypeConverter;
import com.altomni.apn.report.domain.enumeration.JobStatusDataConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiApplicationNoteDetailExcelENVO {

    @ExcelIgnore
    private String virtualId;

    @ExcelIgnore
    private String talentRecruitmentProcessId;

    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "Candidate Name", index = 0)
    private String fullName;

    @ExcelIgnore
    private Long talentId;

    @ExcelProperty(value = "Job Title(ID)", index = 1)
    private String jobTitle;

    @ExcelIgnore
    private Long jobId;

    @ExcelProperty(value = "Job Status", index = 2, converter = JobStatusDataConverter.class)
    @Convert(converter = JobStatusConverter.class)
    private JobStatus jobStatus;

//    @ExcelProperty(value = "Workflow Status", index = 3, converter = CustomMyCandidateStatusFilterConverter.class)
//    @Convert(converter = MyCandidateStatusFilterConverter.class)
//    private MyCandidateStatusFilter workflowStatus;

    @ExcelProperty(value = "Workflow Status", index = 3, converter = CustomNodeTypeConverter.class)
    @Convert(converter = NodeTypeConverter.class)
    private NodeType nodeType;

    @ExcelProperty(value = "Pipeline Notes", index = 4)
    private String note;

    @ExcelProperty(value = "Created By", index = 5)
    private String createdBy;

    @ExcelIgnore
    private Instant createdDate;

    @ExcelProperty(value = "Created Date", index = 6)
    private String createdDateFormat;

    @ExcelProperty(value = "Last Updated By", index = 7)
    private String lastModifiedBy;

    @ExcelIgnore
    private Instant lastModifiedDate;

    @ExcelProperty(value = "Last Updated At", index = 8)
    private String lastModifiedDateFormat;

}
