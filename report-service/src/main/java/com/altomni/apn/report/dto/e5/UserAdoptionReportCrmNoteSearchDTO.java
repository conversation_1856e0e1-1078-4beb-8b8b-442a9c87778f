package com.altomni.apn.report.dto.e5;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collection;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAdoptionReportCrmNoteSearchDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String fromDate;

    private String toDate;

    private String targetType = "USER";

    private Collection<Long> targetIds;

    private String timeZone = "UTC";

}
