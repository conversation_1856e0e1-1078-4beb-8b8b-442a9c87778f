package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.dto.voip.VoipReportDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.voip.VoipDetailReportVO;
import com.altomni.apn.report.domain.vo.voip.VoipReportVO;
import com.altomni.apn.report.dto.voip.VoipDetailReportDTO;
import com.altomni.apn.report.service.VoipReportService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * REST controller for managing voip report.
 */
@RestController
@RequestMapping("/api/v3")
public class VoipReportResource {

    private final Logger log = LoggerFactory.getLogger(VoipReportResource.class);

    private static final String ENTITY_NAME = "voipReport";

    private final VoipReportService voipReportService;

    public VoipReportResource(VoipReportService voipReportService) {
        this.voipReportService = voipReportService;
    }

    @PostMapping("/kpi-report-by-voip")
    public ResponseEntity<List<VoipReportVO>> getVoipReport(@RequestBody VoipReportDTO reportDTO, @PageableDefault Pageable pageable) throws Exception {
        log.info("[APN: Voip @{}] REST request to get voip report by : {}", SecurityUtils.getUserId(), reportDTO);
        List<VoipReportVO> report = voipReportService.getVoipReport(reportDTO, pageable.getSort());
        return ResponseEntity.ok().body(report);
    }

    @PostMapping("/kpi-report-by-voip-detail")
    public ResponseEntity<List<VoipDetailReportVO>> getVoipDetailReport(@RequestBody VoipDetailReportDTO voipDetailReportDTO, @PageableDefault Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[APN: Voip @{}] REST request to get voip detail report by userId : {} and reportType : {}", SecurityUtils.getUserId(), voipDetailReportDTO);
        List<VoipDetailReportVO> detailReport = voipReportService.getVoipDetailReport(voipDetailReportDTO, pageable.getSort());
        return ResponseEntity.ok().body(detailReport);
    }



}
