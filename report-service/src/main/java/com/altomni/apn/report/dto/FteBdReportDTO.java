package com.altomni.apn.report.dto;

import com.altomni.apn.common.dto.search.SearchSortDTO;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class FteBdReportDTO {

    private LocalDate clientCreationStartDate;

    private LocalDate clientCreationEndDate;

    private LocalDate dataCoverageStartDate;

    private LocalDate dataCoverageEndDate;

    private List<Long> clientIds;

    private List<Long> salesLeadOwnerIds;

    private List<Long> bdOwnerIds;

    private List<Long> amIds;

    private List<Long> coAmIds;

    private SearchSortDTO sort;

}
