package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import java.math.BigDecimal;

@Data
@Entity
public class TimeSheetCommonDataVo {

    @Id
    private Long assignmentId;

    private String email;

    private String phone;

    private BigDecimal billRate;

    private String billCurrency;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType billTimeUnit;

    private BigDecimal payRate;

    private String payCurrency;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType payTimeUnit;

    private BigDecimal overBillRate;

    private String overBillCurrency;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType overBillTimeUnit;

    private BigDecimal overPayRate;

    private String overPayCurrency;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType overPayTimeUnit;

    private BigDecimal doubleBillRate;

    private String doubleBillCurrency;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType doubleBillTimeUnit;

    private BigDecimal doublePayRate;

    private String doublePayCurrency;

    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType doublePayTimeUnit;

    private String am;

    private String dm;

    private String ac;

    private String recruiter;

}
