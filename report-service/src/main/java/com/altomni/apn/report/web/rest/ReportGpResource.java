package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.dto.invoicing.InvoicingApplicationInfoSearchDTO;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.RecruitingKpiJobDetailSearchDto;
import com.altomni.apn.report.dto.ReportGpSearchDTO;
import com.altomni.apn.report.dto.ReportMonthlyRevenueGpSearchDTO;
import com.altomni.apn.report.dto.ReportQuarterOnboardAndOffboardSearchDTO;
import com.altomni.apn.report.service.ReportGpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3/report/gp")
public class ReportGpResource {

    @Resource
    private ReportGpService reportGpService;

    /**
     * gp report 查询接口
     * @param reportGpSearchDTO
     */
    @RequestMapping("/search")
    public ResponseEntity<List<ReportGpWithExcelVO>> searchGpReport(@RequestBody ReportGpSearchDTO reportGpSearchDTO) {
        log.info("[APN @{}] search pg report, param = {}", SecurityUtils.getUserId(), reportGpSearchDTO);
        Page<ReportGpWithExcelVO> page = reportGpService.searchGp(reportGpSearchDTO);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/report");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * gp report 查询接口
     * @param reportGpSearchDTO
     */
    @RequestMapping("/export")
    public void exportGpReport(@RequestBody ReportGpSearchDTO reportGpSearchDTO, HttpServletResponse response) {
        log.info("[APN @{}] export pg report excel, param = {}", SecurityUtils.getUserId(), reportGpSearchDTO);
        reportGpService.searchGpWithExcelData(reportGpSearchDTO, response);
    }

    /**
     * 5.7 Monthly Revenue and GP Overview
     * @param reportMonthlyRevenueGpSearchDTO
     */
    @RequestMapping("/monthly-revenue-and-gp-overview")
    public ResponseEntity<List<ReportMonthlyRevenueGpVO>> searchMonthlyRevenueGpReport(@RequestBody ReportMonthlyRevenueGpSearchDTO reportMonthlyRevenueGpSearchDTO) throws Exception{
        log.info("[APN @{}] search Monthly Revenue and GP report, param = {}", SecurityUtils.getUserId(), reportMonthlyRevenueGpSearchDTO);
        return ResponseEntity.ok(reportGpService.searchMonthlyRevenueGpReport(reportMonthlyRevenueGpSearchDTO));
    }

    /**
     * Monthly Revenue and GP Overview detail
     * @param searchDto
     * @param pageable
     * @return
     */
    @PostMapping("/monthly-revenue-detail")
    public ResponseEntity<List<ReportMonthlyRevenueDetailVO>> searchMonthlyRevenueDetail(@RequestBody ReportMonthlyRevenueGpSearchDTO searchDto, Pageable pageable) {
        log.info("[apn @{}] searchMonthlyRevenueDetail by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<ReportMonthlyRevenueDetailVO> page = reportGpService.searchMonthlyRevenueDetail(searchDto, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/report/gp/monthly-revenue-detail");
        stopWatch.stop();
        log.info(" searchMonthlyRevenueDetail by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/monthly-revenue-detail-download")
    public ResponseEntity download(HttpServletResponse response,@RequestBody ReportMonthlyRevenueGpSearchDTO dto) {
        log.info("[APN: Invoicing @{}] REST request to download monthlyRevenueDetail", SecurityUtils.getUserId());
        reportGpService.download(response,dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * 5.7 quarterly-onborading-and-offboarding
     * @param reportQuarterOnboardAndOffboardSearchDTO
     */
    @RequestMapping("/quarterly-onborading-and-offboarding")
    public ResponseEntity<List<ReportQuarterOnboardAndOffboardVO>> searchQuarterlyOnboardingAndOffboardingReport(@RequestBody ReportQuarterOnboardAndOffboardSearchDTO reportQuarterOnboardAndOffboardSearchDTO) throws Exception{
        log.info("[APN @{}] search quarterly onboarding and offboarding report, param = {}", SecurityUtils.getUserId(), reportQuarterOnboardAndOffboardSearchDTO);
        return ResponseEntity.ok(reportGpService.searchQuarterlyOnboardingAndOffboardingReport(reportQuarterOnboardAndOffboardSearchDTO));
    }

    /**
     * 5.13 Quarterly Onboarding New Hires And Renewals
     * @param searchDto
     * @param pageable
     * @return
     */
    @PostMapping("/quarterly-onborading-new-hires-renewals")
    public ResponseEntity<List<ReportQuarterlyOnboardingDetailVO>> searchQuarterlyOnboardingDetail(@RequestBody ReportQuarterOnboardAndOffboardSearchDTO searchDto, Pageable pageable) {
        log.info("[apn @{}] searchQuarterlyOnboardingDetail by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<ReportQuarterlyOnboardingDetailVO> page = reportGpService.searchQuarterlyOnboardingDetail(searchDto, pageable);
        for (ReportQuarterlyOnboardingDetailVO vo : page.getContent()) {
            if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                vo.encrypt();
            }
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/report/gp/quarterly-onborading-new-hires-renewals");
        stopWatch.stop();
        log.info(" searchQuarterlyOnboardingDetail by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/quarterly-onborading-new-hires-renewals-download")
    public ResponseEntity searchQuarterlyOnboardAndOffboardingDetailDownload(HttpServletResponse response,@RequestBody ReportQuarterOnboardAndOffboardSearchDTO dto) {
        log.info("[APN: Invoicing @{}] REST request to searchQuarterlyOnboardAndOffboardingDetailDownload download", SecurityUtils.getUserId());
        reportGpService.quarterlyOnboardAndOffboardingDetailDownload(response,dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }

    /**
     * 5.13 Quarterly Onboarding New Hires And Renewals
     * @param searchDto
     * @param pageable
     * @return
     */
    @PostMapping("/quarterly-offborading-detail")
    public ResponseEntity<List<ReportQuarterlyOffboardingDetailVO>> searchQuarterlyOffboardingDetail(@RequestBody ReportQuarterOnboardAndOffboardSearchDTO searchDto, Pageable pageable) {
        log.info("[apn @{}] searchQuarterlyOffboardingDetail by user", SecurityUtils.getUserId());
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        Page<ReportQuarterlyOffboardingDetailVO> page = reportGpService.searchQuarterlyOffboardingDetail(searchDto, pageable);
        for (ReportQuarterlyOffboardingDetailVO vo : page.getContent()) {
            if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                vo.encrypt();
            }
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/report/gp/quarterly-offborading-detail");
        stopWatch.stop();
        log.info(" searchQuarterlyOffboardingDetail by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/quarterly-offborading-detail-download")
    public ResponseEntity searchQuarterlyOffboardingDetailDownload(HttpServletResponse response,@RequestBody ReportQuarterOnboardAndOffboardSearchDTO dto) {
        log.info("[APN: Invoicing @{}] REST request to searchQuarterlyOffboardingDetail download", SecurityUtils.getUserId());
        reportGpService.quarterlyOffboardingDetailDownload(response,dto);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }
}
