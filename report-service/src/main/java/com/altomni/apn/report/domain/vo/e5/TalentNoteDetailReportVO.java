package com.altomni.apn.report.domain.vo.e5;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.report.domain.vo.RecruitingKpiTalentNoteDetailVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.*;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentNoteDetailReportVO extends RecruitingKpiTalentNoteDetailVO implements Serializable {

    private List<Long> attendees;

    public void setAttendees(String additionalInfo) {
        if (StrUtil.isBlank(additionalInfo)) {
            this.attendees = Collections.emptyList();
        }
        try {
            JSONObject jsonObject = JSONUtil.parseObj(additionalInfo);
            JSONArray attendees = jsonObject.getJSONArray("attendees");
            if (attendees == null) {
                this.attendees = Collections.emptyList();
            }
            this.attendees = attendees.toList(Long.class);
        } catch (Exception e) {
            //ignore
        }
    }

}
