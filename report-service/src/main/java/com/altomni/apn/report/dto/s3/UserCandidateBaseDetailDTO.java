package com.altomni.apn.report.dto.s3;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserCandidateBaseDetailDTO implements Serializable{

    private static final long serialVersionUID = 2822963679888990307L;

    private Long talentId;

    private String name;

    private String createdBy;

    private String createdDate;

    private String lastModifiedDate;
}
