package com.altomni.apn.report.domain.vo.e5;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAdoptionReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userId;

    private String userName;

    private Long activationDuration;

    private Integer callCount;

    private Integer uniqueCalledTalentCount;

    private Long noteCount;

    private Integer uniqueNotedTalentCount;

    private Long emailCount;

    private Long uniqueEmailedTalentCount;

    private Long submitToJobCount;

    private Long interviewCount;

    private Long uniqueInterviewedTalentCount;

    private Long onboardTalentCount;

}
