package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.report.domain.vo.ReportJobPerformanceDetailVo;
import com.altomni.apn.report.domain.vo.ReportJobPerformanceRatioVo;
import com.altomni.apn.report.domain.vo.ReportJobPerformanceVo;
import com.altomni.apn.report.dto.ReportJobPerformanceDto;
import com.altomni.apn.report.repository.JobPerformanceRepository;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.ReportJobPerformanceService;
import com.altomni.apn.report.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReportJobPerformanceServiceImpl implements ReportJobPerformanceService {

    @Resource
    private JobPerformanceRepository jobPerformanceRepository;

    @Resource
    private ReportRepository reportRepository;

    @Resource
    EnumCurrencyService enumCurrencyService;

    @Override
    public List<ReportJobPerformanceRatioVo> getJobPerformanceSourceRatioData(ReportJobPerformanceDto dto) {
        return jobPerformanceRepository.searchJobPerformanceRatio(dto);
    }

    @Override
    public List<ReportJobPerformanceDetailVo> getJobPerformanceSourceDetailData(ReportJobPerformanceDto dto, Pageable pageable, HttpHeaders headers) {
        ReportJobPerformanceVo reportJobPerformanceVo = searchJobPerformanceDetail(dto, pageable);
        headers.set("Pagination-Count", reportJobPerformanceVo.getTotal().toString());
        return reportJobPerformanceVo.getDetailList();
    }

    @Override
    public void exportJobPerformanceDetail(ReportJobPerformanceDto dto, Pageable pageable, HttpServletResponse response) {
        //导出excel 最多 10000条
        pageable = PageRequest.of(0, 10000);
        final ReportJobPerformanceVo data = searchJobPerformanceDetail(dto, pageable);
        if (CollUtil.isEmpty(data.getDetailList())) {
            throw new RuntimeException("Cannot Query Any Data.");
        }
        ExcelUtil.downloadExcelCustom(response, ReportJobPerformanceDetailVo.class, data.getDetailList(), "", "Job_Performance_Report" + dto.getPrimitiveFrom() + "_" + dto.getPrimitiveTo() + ".xlsx", true);
    }

    private ReportJobPerformanceVo searchJobPerformanceDetail(ReportJobPerformanceDto dto, Pageable pageable) {
        ReportJobPerformanceVo reportJobPerformanceVo = jobPerformanceRepository.searchJobPerformanceDetail(dto, pageable);
        List<ReportJobPerformanceDetailVo> detailList = reportJobPerformanceVo.getDetailList();
        List<EnumCurrency> enumCurrencyList = enumCurrencyService.findAllEnumCurrency();
        Map<Integer, EnumCurrency> currencyMap = enumCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, a -> a));
        detailList.forEach(detail -> {
            if (ObjectUtil.isNotNull(detail.getCreatedDate())) {
                detail.setCreatedDateFormat(detail.getCreatedDate().atZone(ZoneId.of(dto.getTimeZone())).toLocalDate().toString());
            }
            if (ObjectUtil.isNotNull(detail.getLastNonOpenTime())) {
                detail.setLastNonOpenTimeFormat(detail.getLastNonOpenTime().atZone(ZoneId.of(dto.getTimeZone())).toLocalDate().toString());
            }
            if (null != detail.getCurrency() && currencyMap.containsKey(detail.getCurrency().intValue())) {
                EnumCurrency currency = currencyMap.get(detail.getCurrency().intValue());
                String billRateDisplay;
                if (StringUtils.isNotEmpty(detail.getBillRate())) {
                    RangeDTO billRange = JSONUtil.toBean(detail.getBillRate(), RangeDTO.class);
                    billRateDisplay = generateRateString(billRange.getGte(), billRange.getLte(), detail.getPayType(), currency);
                } else {
                    billRateDisplay = generateRateString(null, null, detail.getPayType(), currency);
                }
                detail.setBillRateDisplay(billRateDisplay);
            }
        });
        return reportJobPerformanceVo;
    }

    private String generateRateString(BigDecimal from, BigDecimal to, String payType, EnumCurrency enumCurrency) {
        StringBuilder sb = new StringBuilder();
        sb.append(enumCurrency.getName());
        if (from != null) {
            sb.append("/").append(enumCurrency.getLabel1()).append(from);
            if (null != to) {
                sb.append("-").append(enumCurrency.getLabel1()).append(to);
            }
        } else if (to != null) {
            sb.append(enumCurrency.getLabel1()).append(to);
        }
        if (null != payType) {
            // 去除字符串两端的双引号
            payType = payType.replaceAll("^\"|\"$", "");
            sb.append("/").append(RateUnitType.valueOf(payType).getDescription());
        }
        return sb.toString();
    }
}
