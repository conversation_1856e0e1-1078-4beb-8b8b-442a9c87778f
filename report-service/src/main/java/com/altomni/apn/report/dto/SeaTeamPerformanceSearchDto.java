package com.altomni.apn.report.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Set;

@Data
public class SeaTeamPerformanceSearchDto {
    private String fromDate;

    private String toDate;

    private Set<Long> teamIds = Collections.emptySet();

    private Set<Long> userIds = Collections.emptySet();

    private String timezone;

    private Sort sort;

    private Integer currency;

    // 内部使用
    private Long searchUserId;

    private Long searchTenantId;

    //设置一下私有职位
    private Set<Long> privateJobIds;


    public String getStartDateUtc() {
        return getUtcByTimeZone(fromDate + " 00:00:00");
    }

    public String getEndDateUtc() {
        return getUtcByTimeZone(toDate + " 23:59:59");
    }

    public String getUtcByTimeZone(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
//        return dateTime.atZone(ZoneId.of(timezone)).withZoneSameInstant(ZoneOffset.UTC).toString();
        return dateTime.toString();
    }

    @Data
    public static class Sort {
        private String property;
        private String direction;
    }

}
