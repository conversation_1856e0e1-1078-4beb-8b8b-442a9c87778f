package com.altomni.apn.report.domain.vo.s3;

import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.report.domain.vm.s3.DormantApplicationCountVM;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
public class DormantApplicationCountVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty(value = "recruiter id")
    private Long id;

    @ApiModelProperty(value = "recruiter name")
    private String recruiter;

    @ApiModelProperty(value = "total number of dormant applications")
    private Integer count;

    @ApiModelProperty(value = "AM's teams")
    private String team;

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getRecruiter() { return recruiter; }

    public void setRecruiter(String am) { this.recruiter = am; }

    public Integer getCount() { return count; }

    public void setCount(Integer count) { this.count = count; }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    @Override
    public String toString() {
        return "DormantApplicationCountDTO{" +
            "id=" + id +
            ", recruiter='" + recruiter + '\'' +
            ", count=" + count +
            ", team='" + team + '\'' +
            '}';
    }

    public static DormantApplicationCountVO fromDormantApplicationCountVM(DormantApplicationCountVM dormantApplicationCountVM) {
        DormantApplicationCountVO dormantApplicationCountVO = new DormantApplicationCountVO();
        ServiceUtils.myCopyProperties(dormantApplicationCountVM, dormantApplicationCountVO);
        dormantApplicationCountVO.setRecruiter(CommonUtils.formatFullName(dormantApplicationCountVM.getRecruiterFirstName(), dormantApplicationCountVM.getRecruiterLastName()));
        return dormantApplicationCountVO;
    }
}
