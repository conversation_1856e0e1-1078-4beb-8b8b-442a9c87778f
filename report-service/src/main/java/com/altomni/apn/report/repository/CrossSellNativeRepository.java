package com.altomni.apn.report.repository;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.RevenueDTO;
import com.altomni.apn.report.dto.CompanyServiceTypeDTO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;

@Repository
public class CrossSellNativeRepository {

    @Resource
    EntityManager entityManager;

    public List<Object[]>  getRevenueGP(CompanyServiceTypeDTO dto){
        String queryContractOfferAccepted = """
        SELECT
            NULL AS `status`,
            NULL AS start_type,
            trp.id AS id,
            trpicfc.gp AS total_bill_amount,
            c.id AS company_id,
            trpod.onboard_date AS start_date,
            trpod.end_date AS end_date,
            'Offer Accepted' AS type,
            rp.job_type AS job_type,
            ec.from_usd_rate,
            trpod.onboard_date AS s_start_date,
            trpod.end_date AS s_end_date,
            trpod.warranty_end_date,
            trpicfc.final_pay_rate,
            trpicfc.final_bill_rate,
            trpicfc.estimated_working_hour_per_week,
            trpod.rate_unit_type,
            trpod.currency currency,
            trpo.created_date onboard_date 
        FROM
            talent_recruitment_process trp
            INNER JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id AND rp.job_type = :jobType
            INNER JOIN talent_recruitment_process_ipg_contract_fee_charge trpicfc ON trp.id = trpicfc.talent_recruitment_process_id
            INNER JOIN talent_recruitment_process_onboard_date trpod ON trpod.talent_recruitment_process_id = trp.id
            LEFT JOIN talent_recruitment_process_onboard trpo ON trpo.talent_recruitment_process_id = trp.id
            INNER JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = trp.id
            INNER JOIN talent t ON t.id = trp.talent_id
            INNER JOIN job j ON j.id = trp.job_id
            INNER JOIN enum_currency ec ON ec.id = trpod.currency
            INNER JOIN company c ON c.id = j.company_id AND c.id = :companyId
        WHERE
            trp.tenant_id = :tenantId
            AND NOT EXISTS ( SELECT 1 FROM START s WHERE s.talent_recruitment_process_id = trp.id )
                """;
        String queryFteOfferAccepted = """
        SELECT
            NULL AS `status`,
            NULL AS start_type,
            trp.id AS id,
            trpicfc.total_amount AS total_bill_amount,
            c.id AS company_id,
            trpod.onboard_date AS start_date,
            trpod.end_date AS end_date,
            'Offer Accepted' AS type,
            rp.job_type AS job_type,
            ec.from_usd_rate,
            trpod.onboard_date AS s_start_date,
            trpod.end_date AS s_end_date,
            trpod.warranty_end_date,
            0.0 AS final_pay_rate,
            0.0 AS final_bill_rate,
            0.0 AS estimated_working_hour_per_week,
            trpod.rate_unit_type,
            trpod.currency currency,
            trpo.created_date onboard_date  
        FROM
            talent_recruitment_process trp
            INNER JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id AND rp.job_type = :jobType
            INNER JOIN talent_recruitment_process_offer_fee_charge trpicfc ON trp.id = trpicfc.talent_recruitment_process_id
            INNER JOIN talent_recruitment_process_onboard_date trpod ON trpod.talent_recruitment_process_id = trp.id
            INNER JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = trp.id
            LEFT JOIN talent_recruitment_process_onboard trpo ON trpo.talent_recruitment_process_id = trp.id
            INNER JOIN talent t ON t.id = trp.talent_id
            INNER JOIN job j ON j.id = trp.job_id
            INNER JOIN enum_currency ec ON ec.id = trpod.currency
            INNER JOIN company c ON c.id = j.company_id  AND c.id = :companyId
        WHERE
            trp.tenant_id = :tenantId 
            AND NOT EXISTS ( SELECT 1 FROM START s WHERE s.talent_recruitment_process_id = trp.id )
                """;

        String queryContractOnboard = """
        SELECT
            s.`status` AS `status`,
            s.start_type,
            s.talent_recruitment_process_id AS id,
            sr.total_bill_amount AS total_bill_amount,
            c.id AS company_id,
            s.start_date AS start_date,
            s.end_date AS end_date,
            'Onboard' AS type,
            s.position_type AS job_type,
            ec.from_usd_rate,
            sr.start_date AS s_start_date,
            sr.end_date AS s_end_date,
            s.warranty_end_date AS warranty_end_date,
            sr.final_pay_rate,
            sr.final_bill_rate,
            sr.estimated_working_hour_per_week,
            sr.rate_unit_type,
            sr.currency currency,
            trpo.created_date onboard_date  
        FROM
            START s
            INNER JOIN talent t ON t.id = s.talent_id
            LEFT JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = s.talent_recruitment_process_id
            LEFT JOIN talent_recruitment_process_onboard trpo ON trpo.talent_recruitment_process_id = s.talent_recruitment_process_id
            INNER JOIN job j ON j.id = s.job_id
            INNER JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
            INNER JOIN company c ON c.id = j.company_id AND c.id = :companyId
            INNER JOIN start_contract_rate sr ON sr.start_id = s.id
            INNER JOIN enum_currency ec ON ec.id = sr.currency
        WHERE
            s.STATUS !=- 1 
            AND s.tenant_id = :tenantId 
                """;

        String queryFteOnboard = """
        SELECT
            s.`status` AS `status`,
            s.start_type,
            s.talent_recruitment_process_id AS id,
            sr.total_bill_amount AS total_bill_amount,
            c.id AS company_id,
            s.start_date AS start_date,
            s.end_date AS end_date,
            'Onboard' AS type,
            s.position_type AS job_type,
            ec.from_usd_rate,
            NULL AS s_start_date,
            NULL AS s_end_date,
            s.warranty_end_date AS warranty_end_date,
            NULL AS final_pay_rate,
            NULL AS final_bill_rate,
            NULL AS estimated_working_hour_per_week,
            sr.rate_unit_type,
            sr.currency currency,
            trpo.created_date onboard_date 
        FROM
            START s
            INNER JOIN talent t ON t.id = s.talent_id
            INNER JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = s.talent_recruitment_process_id
            LEFT JOIN talent_recruitment_process_onboard trpo ON trpo.talent_recruitment_process_id = s.talent_recruitment_process_id
            INNER JOIN job j ON j.id = s.job_id
            INNER JOIN recruitment_process rp ON rp.id = j.recruitment_process_id
            INNER JOIN company c ON c.id = j.company_id AND c.id = :companyId
            INNER JOIN start_fte_rate sr ON sr.start_id = s.id
            INNER JOIN enum_currency ec ON ec.id = sr.currency 
        WHERE
            s.STATUS !=- 1 
            AND s.tenant_id = :tenantId
                """;
        List<String> sql = new ArrayList<>();
        if (dto.getServiceTypes().contains(2)){
            sql.add(queryFteOfferAccepted);
            sql.add(queryFteOnboard);
        }
        if (dto.getServiceTypes().contains(6)){
            sql.add(queryContractOfferAccepted);
            sql.add(queryContractOnboard);
        }
        String query  = String.join(" UNION ALL ", sql);

        Query dataQuery = entityManager.createNativeQuery(query);
        dataQuery.setParameter("companyId", dto.getCompanyId())
                .setParameter("tenantId", SecurityUtils.getTenantId());
        //jobType改为参数传值
        if (dto.getServiceTypes().contains(2)){
            dataQuery.setParameter("jobType", JobType.FULL_TIME.toDbValue());
         }
        if (dto.getServiceTypes().contains(6)){
            dataQuery.setParameter("jobType", JobType.CONTRACT.toDbValue());
         }
        return dataQuery.getResultList();




    }
}
