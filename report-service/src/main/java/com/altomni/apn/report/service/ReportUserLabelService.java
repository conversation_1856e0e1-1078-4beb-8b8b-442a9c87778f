package com.altomni.apn.report.service;

import com.altomni.apn.common.dto.user.ReportUserLabelSearchDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberMemberDetailDTO;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface ReportUserLabelService {
    List<PermissionTeamMemberMemberDetailDTO> searchUserLabelReport(ReportUserLabelSearchDTO reportUserLabelSearchDTO, HttpHeaders headers);

    void exportUserLabelReport(ReportUserLabelSearchDTO reportUserLabelSearchDTO, HttpServletResponse response) throws IOException;
}
