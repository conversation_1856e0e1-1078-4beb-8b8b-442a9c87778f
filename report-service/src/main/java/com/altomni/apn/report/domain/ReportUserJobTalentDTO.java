package com.altomni.apn.report.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A ReportUserJobTalent.
 * <AUTHOR>
 */
@Entity
public class ReportUserJobTalentDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long userId;

    private String userName;

    private Integer talentCount;

    private String talentIds;

    private Integer jobCount;

    private String jobIds;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getTalentCount() {
        return talentCount;
    }

    public void setTalentCount(Integer talentCount) {
        this.talentCount = talentCount;
    }

    public String getTalentIds() {
        return talentIds;
    }

    public void setTalentIds(String talentIds) {
        this.talentIds = talentIds;
    }

    public Integer getJobCount() {
        return jobCount;
    }

    public void setJobCount(Integer jobCount) {
        this.jobCount = jobCount;
    }

    public String getJobIds() {
        return jobIds;
    }

    public void setJobIds(String jobIds) {
        this.jobIds = jobIds;
    }


    @Override
    public String toString() {
        return "ReportUserJobTalentDTO{" +
                "userId=" + userId +
                ", userName='" + userName + '\'' +
                ", talentCount=" + talentCount +
                ", talentIds='" + talentIds + '\'' +
                ", jobCount=" + jobCount +
                ", jobIds='" + jobIds + '\'' +
                '}';
    }
}
