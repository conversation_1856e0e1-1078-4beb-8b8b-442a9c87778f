package com.altomni.apn.report.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.write.builder.ExcelWriterTableBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.WriteTable;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.altomni.apn.report.config.excel.CustomCellWriteHeightConfig;
import com.altomni.apn.report.config.excel.CustomCellWriteWeightConfig;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@UtilityClass
public class ExcelUtil {

    public static final String LAST_INDEX = "LAST";

    public static final String SHEET_NAME = "sheet-1";

    public <T> ByteArrayOutputStream writerExcel(List<List<String>> headers, List<T> dataList, String sheetName, String xlsName, boolean CustomConfig) {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ExcelWriter excelWriter;
        try {
            if (CustomConfig) {
                excelWriter = EasyExcelFactory.write(outputStream)
                        .registerWriteHandler(getCustomCellStyleStrategy(dataList.size()))
                        .build();
            } else {
                excelWriter = EasyExcelFactory.write(outputStream).build();
            }
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, StrUtil.isBlank(sheetName) ? SHEET_NAME : sheetName).build();
            //register all converter
            Set<Class<?>> clazzSet = ClassUtil.scanPackageBySuper("com.altomni.apn", Converter.class);
            ExcelWriterTableBuilder builder = EasyExcelFactory.writerTable().head(headers).needHead(Boolean.TRUE);
            clazzSet.forEach(clazz -> {
                try {
                    Converter<T> converter = (Converter) clazz.newInstance();
                    if (converter.supportJavaTypeKey() == null) {
                        return;
                    }
                    builder.registerConverter(converter);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            WriteTable writeTable = builder.build();
            excelWriter.write(dataList, writeSheet, writeTable);
            excelWriter.finish();
            outputStream.close();
        } catch (Exception e) {
            log.error("[apn] excelUtil flush stream error, message = [{}]", ExceptionUtils.getStackTrace(e));
        }
        return outputStream;
    }


    public <T> void downloadExcel(HttpServletResponse response, List<List<String>> headers, List<T> dataList, String sheetName, String xlsName, boolean CustomConfig) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        ExcelWriter excelWriter;
        setFileDownloadHeader(response, xlsName);
        try (OutputStream outputStream = response.getOutputStream()) {
            if (CustomConfig) {
                excelWriter = EasyExcelFactory.write(outputStream)
                        .registerWriteHandler(getCustomCellStyleStrategy(dataList.size()))
                        .build();
            } else {
                excelWriter = EasyExcelFactory.write(outputStream).build();
            }
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, StrUtil.isBlank(sheetName) ? SHEET_NAME : sheetName).build();
            //register all converter
            Set<Class<?>> clazzSet = ClassUtil.scanPackageBySuper("com.altomni.apn", Converter.class);
            ExcelWriterTableBuilder builder = EasyExcelFactory.writerTable().head(headers).needHead(Boolean.TRUE);
            clazzSet.forEach(clazz -> {
                try {
                    Converter<T> converter = (Converter) clazz.newInstance();
                    if (converter.supportJavaTypeKey() == null) {
                        return;
                    }
                    builder.registerConverter(converter);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
            WriteTable writeTable = builder.build();
            excelWriter.write(dataList, writeSheet, writeTable);
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            log.error("[apn] excelUtil flush stream error, message = [{}]", ExceptionUtils.getStackTrace(e));
        }
        stopWatch.stop();
        log.info("[apn] downloadExcel time = [{} milliseconds] ", stopWatch.getTotalTimeMillis());
    }

    public <T> void downloadExcelCustom(HttpServletResponse response, Class<T> clazz, List<T> dataList, String sheetName, String xlsName, boolean CustomConfig) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        ExcelWriter excelWriter;
        setFileDownloadHeader(response, xlsName);
        try (OutputStream outputStream = response.getOutputStream();) {
            if (CustomConfig) {
                excelWriter = EasyExcelFactory.write(outputStream)
                        .registerWriteHandler(getCustomCellStyleStrategy(dataList.size()))
                        .build();
            } else {
                excelWriter = EasyExcelFactory.write(outputStream).build();
            }
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, StrUtil.isBlank(sheetName) ? SHEET_NAME : sheetName).build();
            WriteTable writeTable = EasyExcelFactory.writerTable().head(clazz).needHead(Boolean.TRUE).build();
            excelWriter.write(dataList, writeSheet, writeTable);
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            log.error("[apn] excelUtil flush stream error, message = [{}]", ExceptionUtils.getStackTrace(e));
        }
        stopWatch.stop();
        log.info("[apn] downloadExcel time = [{} milliseconds] ", stopWatch.getTotalTimeMillis());
    }

    public <T> void downloadExcel(HttpServletResponse response, Class<T> clazz, List<T> dataList, String sheetName, String xlsName, boolean CustomConfig) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        ExcelWriter excelWriter;
        setFileDownloadHeader(response, xlsName);
        try (OutputStream outputStream = response.getOutputStream();) {
            if (CustomConfig) {
                excelWriter = EasyExcelFactory.write(outputStream)
                        .registerWriteHandler(new CustomCellWriteHeightConfig())
                        .registerWriteHandler(new CustomCellWriteWeightConfig())
                        .build();
            } else {
                excelWriter = EasyExcelFactory.write(outputStream).build();
            }
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, StrUtil.isBlank(sheetName) ? SHEET_NAME : sheetName).build();
            WriteTable writeTable = EasyExcelFactory.writerTable().head(clazz).needHead(Boolean.TRUE).build();
            excelWriter.write(dataList, writeSheet, writeTable);
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            log.error("[apn] excelUtil flush stream error, message = [{}]", ExceptionUtils.getStackTrace(e));
        }
        stopWatch.stop();
        log.info("[apn] downloadExcel time = [{} milliseconds] ", stopWatch.getTotalTimeMillis());
    }

    private HorizontalCellStyleStrategy getCustomCellStyleStrategy(int dataSize) {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontName("宋体");
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);

        WriteCellStyle contentStyleCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("宋体");
        contentWriteFont.setFontHeightInPoints((short) 12);
        contentWriteFont.setBold(false);
        List<WriteCellStyle> preList = new ArrayList<>();
        for (int i = 0; i < dataSize - 1; i++) {
            preList.add(contentStyleCellStyle);
        }
        return new HorizontalCellStyleStrategy(headWriteCellStyle, preList);
    }

    public <T> void downloadExcelWithGramdTotal(HttpServletResponse response, Class<T> clazz, List<T> dataList, String sheetName, String xlsName, boolean customConfig) {
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        ExcelWriter excelWriter;
        setFileDownloadHeader(response, xlsName);
        try (OutputStream outputStream = response.getOutputStream();) {
            if (customConfig) {
                excelWriter = EasyExcelFactory.write(outputStream)
                        .registerWriteHandler(new CustomCellWriteHeightConfig())
                        .registerWriteHandler(new CustomCellWriteWeightConfig())
                        .registerWriteHandler(getCellStyleStrategy(dataList.size()))
                        .build();
            } else {
                excelWriter = EasyExcelFactory.write(outputStream).build();
            }
            WriteSheet writeSheet = EasyExcelFactory.writerSheet(0, StrUtil.isBlank(sheetName) ? SHEET_NAME : sheetName).build();
            WriteTable writeTable = EasyExcelFactory.writerTable().head(clazz).needHead(Boolean.TRUE).build();
            excelWriter.write(dataList, writeSheet, writeTable);
            excelWriter.finish();
            outputStream.flush();
        } catch (Exception e) {
            log.error("[apn] excelUtil flush stream error, message = [{}]", ExceptionUtils.getStackTrace(e));
        }
        stopWatch.stop();
        log.info("[apn] downloadExcel time = [{} milliseconds] ", stopWatch.getTotalTimeMillis());
    }

    public void setFileDownloadHeader(HttpServletResponse response, String xlsName) {
        response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Expires", "0");
        response.setHeader("charset", "utf-8");
        response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");
        if (StrUtil.isBlank(xlsName)) {
            response.setHeader("Content-Disposition", "attachment;filename=\"report.xls\"");
        } else {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(xlsName, StandardCharsets.UTF_8));
        }

    }

    private HorizontalCellStyleStrategy getCellStyleStrategy(int dataSize) {

        WriteCellStyle headStyle = new WriteCellStyle();

        WriteCellStyle bodyStyle = new WriteCellStyle();
        bodyStyle.setWriteFont(setFont(12, "宋体", false));
        bodyStyle.setHorizontalAlignment(HorizontalAlignment.LEFT);

        WriteCellStyle lastRow = new WriteCellStyle();

        lastRow.setWriteFont(setFont(12, "宋体", true));
        lastRow.setHorizontalAlignment(HorizontalAlignment.LEFT);

        List<WriteCellStyle> preList = new ArrayList<>();

        for (int i = 0; i < dataSize - 1; i++) {
            preList.add(bodyStyle);
        }

        preList.add(lastRow);

        return new HorizontalCellStyleStrategy(headStyle, preList);
    }


    private WriteFont setFont(int size, String name, boolean condition) {
        WriteFont font = new WriteFont();

        font.setFontHeightInPoints((short) size);

        if (!ObjectUtils.isEmpty(name)) {
            font.setFontName(name);
        }

        font.setBold(condition);
        return font;
    }


    public List<List<String>> getTableHeaders(Class clazz, List<String> removeList) {
        Field[] fieldArray = ReflectUtil.getFields(clazz);
        return Arrays.stream(fieldArray)
                .filter(field -> !removeList.contains(field.getName()))
                .map(field -> field.getAnnotation(ExcelProperty.class))
                .filter(ObjectUtil::isNotNull).sorted(Comparator.comparing(ExcelProperty::index))
                .map(excel -> {
                    String value = excel.value()[0];
                    return CollUtil.newArrayList(value);
                }).collect(Collectors.toList());
    }

    public <T> List<List<Object>> convertToMap(List<T> voList, Class clazz, List<String> removeList) {
        Field[] fieldArray = ReflectUtil.getFields(clazz);
        List<Field> fieldList = Arrays.stream(fieldArray)
                .filter(field -> !removeList.contains(field.getName()))
                .filter(field -> field.getAnnotation(ExcelProperty.class) != null)
                .sorted((a1, a2) -> {
                    ExcelProperty excelProperty1 = a1.getAnnotation(ExcelProperty.class);
                    ExcelProperty excelProperty2 = a2.getAnnotation(ExcelProperty.class);
                    return Comparator.comparingInt(ExcelProperty::index).compare(excelProperty1, excelProperty2);
                }).collect(Collectors.toList());
        List<List<Object>> mapList = new LinkedList<>();
        voList.forEach(vo -> {
            List<Object> list = new LinkedList<>();
            fieldList.forEach(field -> {
                Object value;
                Method method = ReflectUtil.getMethodByNameIgnoreCase(clazz, "get" + field.getName().toLowerCase());
                try {
                    value = method.invoke(vo, null);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
                list.add(value);
            });
            mapList.add(list);
        });
        return mapList;
    }

    public static void maskConfidentialTalentData(Object object) {
        Field[] fields = ReflectUtil.getFields(object.getClass());
        Stream.of(fields)
                .filter(field -> field.getAnnotation(ExcelProperty.class) != null)
                .forEach(field -> {
                    try {
                        field.setAccessible(true);
                        Class<?> type = field.getType();
                        if (type.getName().equalsIgnoreCase("java.lang.String")) {
                            field.set(object, "***");
                        } else {
                            field.set(object, null);
                        }
                    } catch (IllegalAccessException e) {
                        throw new RuntimeException(e);
                    }
                });
    }

}
