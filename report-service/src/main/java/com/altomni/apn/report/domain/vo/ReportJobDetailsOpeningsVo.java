package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.altomni.apn.report.config.excel.CustomFloatConverter;
import com.altomni.apn.report.config.excel.CustomJobStatusConverter;
import com.altomni.apn.report.config.excel.CustomJobTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.Instant;

@Data
@Entity
public class ReportJobDetailsOpeningsVo {

    @Id
    @ExcelProperty(value = "Job Id", index = 2)
    private Long jobId;

    @ExcelIgnore
    private Long companyId;

    @ExcelIgnore
    private Instant dateIssued;

    @Transient
    @ExcelProperty(value = "Date Posted", index = 0)
    private String dateIssuedFormat;

    @ExcelProperty(value = "Job Title", index = 1)
    private String title;

    @ExcelProperty(value = "Client Job Code", index = 3)
    private String code;

    @Convert(converter = JobStatusConverter.class)
    @ExcelProperty(value = "Status", index = 4, converter = CustomJobStatusConverter.class)
    private JobStatus status;

    @ExcelProperty(value = "Division", index = 5)
    private String division;

    @Convert(converter = JobTypeConverter.class)
    @ExcelProperty(value = "Type", index = 6, converter = CustomJobTypeConverter.class)
    private JobType type;

    @ExcelProperty(value = "Company", index = 7)
    private String company;

    @ExcelProperty(value = "Client Contact", index = 8)
    private String hiringManager;

    @ExcelIgnore
    @Transient
    private String extendedInfo;

    @ExcelIgnore
    private Instant startDate;

    @Transient
    @ExcelProperty(value = "Start Date", index = 14)
    private String startDateFormat;

    @ExcelIgnore
    private Instant endDate;

    @Transient
    @ExcelProperty(value = "End Date", index = 15)
    private String endDateFormat;

    @ExcelProperty(value = "Job Openings", index = 17)
    private Long openingCount;

    @ExcelProperty(value = "Maximum Submittals", index = 18)
    private Integer maxSubmittals;

    @ExcelProperty(value = "Skills", index = 19)
    private String requiredSkills;

    @ExcelProperty(value = "Primary Recruiter", index = 20)
    private String primaryRecruiter;

    @ExcelProperty(value = "Primary Sales", index = 21)
    private String primarySales;

    @ExcelProperty(value = "Division of Primary Sales", index = 22)
    private String divisionOfPrimarySales;

    @ExcelIgnore
    private Instant lastActivityDate;

    @Transient
    @ExcelProperty(value = "Last Activity Date", index = 26)
    private String lastActivityDateFormat;

    @ExcelProperty(value = "Sum of Submitted to Client", index = 23)
    private Long submittedCount;

    @ExcelProperty(value = "Sum of Interview", index = 24)
    private Long interviewCount;

    @ExcelProperty(value = "Sum of On boarded", index = 25)
    private Long startedCount;

    @Transient
    @ExcelProperty(value = "Minimum Pay Rate", index = 9, converter = CustomFloatConverter.class)
    private Float minimumPayRate;

    @Transient
    @ExcelProperty(value = "Maximum Pay Rate", index = 10, converter = CustomFloatConverter.class)
    private Float maximumPayRate;

    @Transient
    @ExcelProperty(value = "Minimum Bill Rate", index = 11, converter = CustomFloatConverter.class)
    private Float minimumBillRate;

    @Transient
    @ExcelProperty(value = "Maximum Bill Rate", index = 12, converter = CustomFloatConverter.class)
    private Float maximumBillRate;

    @ExcelProperty(value = "Rate Per", index = 13)
    private String unitType;

    @ExcelProperty(value = "Location", index = 16)
    private String jobLocations;

//    @ExcelIgnore
//    @JsonIgnore
//    private String pruFirstName;
//
//    @ExcelIgnore
//    @JsonIgnore
//    private String pruLastName;
//
//    @ExcelIgnore
//    @JsonIgnore
//    private String amuFirstName;
//
//    @ExcelIgnore
//    @JsonIgnore
//    private String amuLastName;

    @ExcelIgnore
    @JsonIgnore
    private String salaryRange;

    @ExcelIgnore
    @JsonIgnore
    private String billRange;

}
