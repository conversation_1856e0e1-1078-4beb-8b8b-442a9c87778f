package com.altomni.apn.report.domain.vo.voip;

import cn.hutool.json.JSONObject;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class VoipDetailReportExcelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "Candidates Name", index = 0)
    private String talentName;

    @ExcelProperty(value = "Contacts Name", index = 0)
    private String contactName;

    @ExcelProperty(value = "Job Title (ID)", index = 1)
    private String jobTitle;

    @ExcelProperty(value = "Dialing Time", index = 2)
    private String dialingTime;

    @ExcelProperty(value = "Talk Minutes", index = 3)
    private String talkMinutes;

    @ExcelProperty(value = "Call Result", index = 4)
    private String callResult;

    @ExcelProperty(value = "Call Type", index = 5)
    private String callType;

    @ExcelProperty(value = "AI Summary", index = 6)
    private String AISummary;

    @ExcelProperty(value = "Note", index = 7)
    private String note;

}
