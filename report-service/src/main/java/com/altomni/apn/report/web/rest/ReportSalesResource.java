package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportSalesCompanyVo;
import com.altomni.apn.report.domain.vo.ReportSalesDetailVo;
import com.altomni.apn.report.domain.vo.ReportSalesViewStatsVo;
import com.altomni.apn.report.dto.ReportSalesParamDto;
import com.altomni.apn.report.dto.SaleDetailDTO;
import com.altomni.apn.report.service.ReportSalesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ReportSalesResource {

    @Resource
    private ReportSalesService reportSalesService;

    @GetMapping("/report/sales/company/filter")
    public ResponseEntity<List<ReportSalesCompanyVo>> salesCompanyFilter(
            @RequestParam(value = "jobTypes", required = false) List<JobType> jobTypes,
            @RequestParam(value = "country", required = false) Integer currency,
            @RequestParam(value = "years", required = false) List<Integer> years,
            @RequestParam(value = "from", required = false) String from,
            @RequestParam(value = "to", required = false) String to) {
        ReportSalesParamDto reportParam = new ReportSalesParamDto().jobType(jobTypes).country(currency).years(years);
        try {
            if (from != null) {
                reportParam.setFrom(LocalDate.parse(from));
            }
            if (to != null) {
                reportParam.setTo(LocalDate.parse(to));
            }
        } catch (Exception e) {
            log.error("[APN: ReportSales @{}] REST request to get sales company filter. --> Invalid date format. from:{}, to:{}", SecurityUtils.getUserId(), from, to);
        }
        log.info("[APN: ReportSales @{}] REST request to get sales company filter. --> reportParam:{}", SecurityUtils.getUserId(), reportParam);
        return ResponseEntity.ok(reportSalesService.salesCompanyFilter(reportParam));
    }

    @PostMapping("/report/sales-all-by-type")
    public ResponseEntity<List<ReportSalesViewStatsVo>> salesByMonth(@RequestBody ReportSalesParamDto reportSalesParamDto) {
        log.info("[APN: ReportSales @{}] . --> reportParam:{}", SecurityUtils.getUserId(), reportSalesParamDto);
        return ResponseEntity.ok(reportSalesService.searchSales(reportSalesParamDto));
    }

    @PostMapping("/report/sales-by-weekly-new-offer")
    public ResponseEntity<List<ReportSalesViewStatsVo>> salesByWeeklyNewOffer(@RequestBody ReportSalesParamDto reportSalesParamDto) {
        log.info("[APN: ReportSales @{}] REST request to get by weekly new offer. --> reportParam:{}", SecurityUtils.getUserId(), reportSalesParamDto);
        return ResponseEntity.ok(reportSalesService.salesByWeeklyNewOffer(reportSalesParamDto));
    }

    @PostMapping("/report/sales-details")
    public ResponseEntity<List<ReportSalesDetailVo>> getSalesDetails(@RequestBody SaleDetailDTO saleDetailDTO) {
        log.info("[APN: ReportSales @{}] REST request to get sales details. --> applicationIds :{},jobType:{}", SecurityUtils.getUserId(),saleDetailDTO.getApplicationIds(),saleDetailDTO.getJobType());
        return ResponseEntity.ok(reportSalesService.getSalesDetails(saleDetailDTO));
    }

    @PostMapping("/report/sales-details-excel")
    public void salesFteDetailsExcel(HttpServletResponse response, @RequestBody SaleDetailDTO saleDetailDTO) {
        log.info("[APN: ReportSales @{}] REST request to get FTE by month details. --> applicationIds :{}", SecurityUtils.getUserId(), saleDetailDTO.getApplicationIds());
        reportSalesService.exportSalesFteDetailsExcel(response, saleDetailDTO);
    }


}
