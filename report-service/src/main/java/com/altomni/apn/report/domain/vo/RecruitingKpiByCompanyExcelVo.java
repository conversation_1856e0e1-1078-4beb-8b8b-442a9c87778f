package com.altomni.apn.report.domain.vo;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiGroupByFieldType;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.vo.recruiting.KpiReportCompanyInfoVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByCompanyVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonVO;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.report.domain.vo.RecruitingKpiBaseExcelVo.safeLong;
import static com.altomni.apn.report.domain.vo.RecruitingKpiBaseExcelVo.toPercentage;

@Getter
@Setter
public class RecruitingKpiByCompanyExcelVo implements RecruitingKpiBaseExcelVo {

    @ExcelProperty(value = "Company")
    private String company;

    @ExcelProperty(value = "Job")
    private String job;

    @ExcelProperty(value = "Team")
    private String team;

    @ExcelProperty(value = "User")
    private String user;

    @ExcelProperty(value = "Day")
    private String day;

    @ExcelProperty(value = "Week")
    private String week;

    @ExcelProperty(value = "Month")
    private String month;

    @ExcelProperty(value = "Quarter")
    private String quarter;

    @ExcelProperty(value = "Year")
    private String year;

    @ExcelProperty(value = "Industry")
    private String industry;

    @ExcelProperty(value = "Country")
    private String country;

    @ExcelProperty(value = "Client Status")
    private String clientStatus;

    @ExcelProperty(value = "AM")
    private String am;

    @ExcelProperty(value = "Co-Account Manager")
    private String coAm;

    @ExcelProperty(value = "BD Owner")
    private String bdOwner;

    @ExcelProperty(value = "Saleslead Owner")
    private String salesleadOwner;

    @ExcelProperty(value = "Company Created Date")
    private String createDate;

    @ExcelProperty(value = "Request Date")
    private String requestDate;

    @ExcelProperty(value = "Sum of Openings")
    private String openings;

    @ExcelProperty(value = "Contacts")
    private String contacts;

    @ExcelProperty(value = "Assigned User")
    private String assignedUser;

    @ExcelProperty(value = "Job Status")
    private String jobStatus;

    @ExcelProperty(value = "Contract Duration")
    private String contactDuration;

    @ExcelProperty(value = "Start Date")
    private String startDate;

    @ExcelProperty(value = "End Date")
    private String endDate;

    @ExcelProperty(value = "Submitted to Job")
    private String submitToJobNum;

    @ExcelProperty(value = "Submitted to Client")
    private String submitToClientNum;

    @ExcelProperty(value = "Submit to Client(This Week)")
    private String submitToClientThisWeekNum;

    @ExcelProperty(value = "Submit to Client(Last Week)")
    private String submitToClientLastWeekNum;

    @ExcelProperty(value = "Interview (First Round)")
    private String firstInterviewNum;

    @ExcelProperty(value = "Interview (Second Round)")
    private String secondInterviewNum;

    @ExcelProperty(value = "Interview (Final Round)")
    private String finalInterviewNum;

    @ExcelProperty(value = "Sum of Interview")
    private String interviewNum;

    @ExcelProperty(value = "Offered")
    private String offerNum;

    @ExcelProperty(value = "Offer Accetped")
    private String offerAcceptNum;

    @ExcelProperty(value = "On Boarded")
    private String onboardNum;

    @ExcelProperty(value = "Eliminated")
    private String eliminateNum;

    @ExcelProperty(value = "AI Candidate Interview Conversion Rate")
    private String aiCandidateInterviewConversionRate;

    @ExcelProperty(value = "AI Candidate Onboarding Rate")
    private String aiCandidateOnboardingRate;

    @ExcelProperty(value = "Job Notes")
    private String jobNotes;

    @ExcelProperty(value = "Job Currency")
    private String jobCurrency;

    @ExcelProperty(value = "Job Desired Salary Range")
    private String jobDesiredSalaryRange;

    @ExcelProperty(value = "Job Cooperation Status")
    private String jobCooperationStatus;

    @ExcelProperty(value = "BD Progress Notes")
    private String bdProgressNotes;

    @ExcelProperty(value = "Company Notes")
    private String companyNotes;

    @Override
    public void setTwoOrMoreInterviews(String twoOrMoreInterviews) {

    }

    @Override
    public void setInterviewAppointments(String interviewAppointments) {

    }

    public static RecruitingKpiByCompanyExcelVo ofSearchResult(RecruitingKpiByCompanyVO vo, RecruitingKpiReportSearchDto searchDto) {
        RecruitingKpiByCompanyExcelVo excelVo = new RecruitingKpiByCompanyExcelVo();
        excelVo.setCompany(vo.getCompanyName());
        excelVo.setJob(vo.getJobTitle());
        excelVo.setTeam(vo.getTeamName());
        excelVo.setUser(vo.getUserName());

        // 设置基础指标
        excelVo.setOpenings(safeLong(vo.getOpenings()));
        excelVo.setIndustry(vo.getIndustries());
        excelVo.setCountry(vo.getCountry());
        excelVo.setClientStatus(vo.getClientStatus());
        if(CollUtil.isNotEmpty(vo.getAmList())){
            excelVo.setAm(vo.getAmList().stream().map(KpiReportCompanyInfoVO::getUsername).collect(Collectors.joining(" , ")));
        }
        if(CollUtil.isNotEmpty(vo.getCoAmList())){
            excelVo.setCoAm(vo.getCoAmList().stream().map(KpiReportCompanyInfoVO::getUsername).collect(Collectors.joining(" , ")));
        }
        if(CollUtil.isNotEmpty(vo.getBdOwnerList())){
            excelVo.setBdOwner(vo.getBdOwnerList().stream().map(KpiReportCompanyInfoVO::getUsername).collect(Collectors.joining(" , ")));
        }
        if(CollUtil.isNotEmpty(vo.getSalesLeadList())){
            excelVo.setSalesleadOwner(vo.getSalesLeadList().stream().map(KpiReportCompanyInfoVO::getUsername).collect(Collectors.joining(" , ")));
        }
        excelVo.setCreateDate(DateUtil.fromInstantToDate(vo.getCompanyCreatedDate(),searchDto.getTimezone(),"yyyy-MM-dd"));
        excelVo.setRequestDate(DateUtil.fromInstantToDate(vo.getRequestDate(),searchDto.getTimezone(),"yyyy-MM-dd"));
        excelVo.setContacts(vo.getContacts());
        excelVo.setAssignedUser(vo.getAssignedUser());
        excelVo.setJobStatus(null != vo.getJobStatus() ? vo.getJobStatus().name() : null);
        excelVo.setContactDuration(vo.getContractDuration());
        excelVo.setStartDate(vo.getJobStartDate());
        excelVo.setEndDate(vo.getJobEndDate());
        //未转换
        excelVo.setJobCurrency(vo.getJobCurrency());
        excelVo.setJobNotes(safeLong(vo.getJobNoteNum()));
        excelVo.setJobCooperationStatus(vo.getJobCooperationStatus());
        if (StringUtils.isNotBlank(vo.getMinimumPayRate()) && StringUtils.isNotBlank(vo.getMaximumPayRate())) {
            excelVo.setJobDesiredSalaryRange(vo.getMinimumPayRate() + "-" + vo.getMaximumPayRate());
        } else if (StringUtils.isBlank(vo.getMinimumPayRate()) && StringUtils.isBlank(vo.getMaximumPayRate())) {
            excelVo.setJobDesiredSalaryRange("--");
        } else {
            excelVo.setJobDesiredSalaryRange(vo.getMinimumPayRate() + vo.getMaximumPayRate());
        }
        excelVo.setBdProgressNotes(safeLong(vo.getBdReportProgressNoteCount()));
        excelVo.setCompanyNotes(safeLong(vo.getCompanyNoteCount()));


        // 设置时间相关维度
        excelVo.setDay(vo.getGroupByDate());
        excelVo.setWeek(vo.getGroupByDate());
        excelVo.setMonth(vo.getGroupByDate());
        excelVo.setQuarter(vo.getGroupByDate());
        excelVo.setYear(vo.getGroupByDate());

        // 设置流程相关指标
        excelVo.setProcessMetrics(vo, searchDto);

        // 设置转化率指标
        if (searchDto.getAiTalentType() != null) {
            excelVo.setAiCandidateInterviewConversionRate("%s : %s".formatted(toPercentage(vo.getAiInterviewConversionRate()), toPercentage(vo.getOverallInterviewConversionRate())));
            excelVo.setAiCandidateOnboardingRate("%s : %s".formatted(toPercentage(vo.getAiConversionRate()), toPercentage(vo.getOverallConversionRate())));
        }
        return excelVo;
    }

    @Override
    public List<ProcessMetricConfig> getProcessMetricConfigs(RecruitingKpiCommonVO vo) {
        RecruitingKpiByCompanyVO companyVO = (RecruitingKpiByCompanyVO) vo;
        List<ProcessMetricConfig> processMetricConfigs = List.of(
                // 提交至客户本周
                new ProcessMetricConfig(
                        this::setSubmitToClientThisWeekNum,
                        companyVO::getThisWeekCount, companyVO::getThisWeekCurrentCountNum,
                        companyVO::getThisWeekCountAIRecommend, companyVO::getThisWeekCountPrecisionAIRecommend
                ),
                // 提交至客户上周
                new ProcessMetricConfig(
                        this::setSubmitToClientLastWeekNum,
                        companyVO::getLastWeekCount, companyVO::getLastWeekCurrentCountNum,
                        companyVO::getLastWeekCountAIRecommend, companyVO::getLastWeekCountPrecisionAIRecommend
                )
        );
        return Stream.concat(getBaseProcessMetricConfigs(companyVO).stream(), processMetricConfigs.stream()).toList();
    }

    public static List<String> removeHeaders(RecruitingKpiReportSearchDto searchDto) {
        List<String> aiConversionRateHeader = List.of("aiCandidateInterviewConversionRate", "aiCandidateOnboardingRate");
        List<String> removeHeaders = new ArrayList<>();
        // 如果没有选 ai 推荐，则去掉 ai 相关的列
        if (searchDto.getAiTalentType() == null) {
            removeHeaders.addAll(aiConversionRateHeader);
        }
        List<String> dateCol = List.of("day", "week", "month", "quarter", "year");
        Optional<RecruitingKpiGroupByFieldType> dateDimOpt = searchDto.getGroupByFieldList().stream()
                .filter(RecruitingKpiGroupByFieldType.ALL_TIME_LIST::contains).findFirst();
        // 如果没有选时间维度，去掉时间列
        if (dateDimOpt.isEmpty()) {
            removeHeaders.addAll(dateCol);
        } else {
            // 如果选择了时间维度，则去掉除选择的时间维度以外的列
            List<String> ignoreCol = dateCol.stream().filter(s -> !s.equalsIgnoreCase(dateDimOpt.get().name())).toList();
            removeHeaders.addAll(ignoreCol);
        }
        return removeHeaders;
    }

}
