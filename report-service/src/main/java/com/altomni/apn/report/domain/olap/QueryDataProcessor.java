package com.altomni.apn.report.domain.olap;

import org.openapitools.client.model.ChartDataResponseResult;

import java.util.List;

public interface QueryDataProcessor {

    ChartDataResponseResult process(ChartDataResponseResult dataResponse, QueryContext queryContext);

    List<QueryDataProcessor> chain = List.of(new TimeProcessor());

    static ChartDataResponseResult processAll(ChartDataResponseResult dataResponse, QueryContext queryContext) {
        ChartDataResponseResult processedResult = dataResponse;
        for (QueryDataProcessor processor : chain) {
            processedResult = processor.process(processedResult, queryContext);
        }
        return processedResult;
    }
}
