package com.altomni.apn.report.domain.vo.e5;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailDetailReportExcelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "Create By", index = 0)
    private String createdByName;

    @ExcelProperty(value = "Candidates name", index = 1)
    private String candidateNames;

    @ExcelProperty(value = "Contacts name", index = 1)
    private String contactNames;

    @ExcelProperty(value = "Add time", index = 2)
    private String sentTime;

    @ExcelProperty(value = "Subject", index = 3)
    private String subject;

    @ExcelProperty(value = "Email conversations", index = 4)
    private Long conversationCount;

}
