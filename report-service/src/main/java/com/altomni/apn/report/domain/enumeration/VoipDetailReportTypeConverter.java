package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class VoipDetailReportTypeConverter extends AbstractAttributeConverter<VoipDetailReportType, Integer> {
    public VoipDetailReportTypeConverter() {
        super(VoipDetailReportType::toDbValue, VoipDetailReportType::fromDbValue);
    }
}
