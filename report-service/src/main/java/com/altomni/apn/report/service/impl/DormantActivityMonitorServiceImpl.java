package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.altomni.apn.report.domain.vm.s3.DormantApplicationCountVM;
import com.altomni.apn.report.domain.vm.s3.DormantApplicationVM;
import com.altomni.apn.report.domain.vm.s3.TeamNameVM;
import com.altomni.apn.report.domain.vo.s3.DormantApplicationCountVO;
import com.altomni.apn.report.domain.vo.s3.DormantApplicationVO;
import com.altomni.apn.report.dto.s3.DormantApplicationCountDTO;
import com.altomni.apn.report.service.DormantActivityMonitorService;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DormantActivityMonitorServiceImpl extends ReportBaseServiceImpl implements DormantActivityMonitorService {

    private final Logger log = LoggerFactory.getLogger(DormantActivityMonitorServiceImpl.class);

    @Override
    public List<DormantApplicationCountVO> countAllDormantApplications(DormantApplicationCountDTO dormantApplicationCountDTO) {
        Map<Integer, Object> defaultParamMap = new HashMap<>();
        defaultParamMap.put(1, SecurityUtils.getTenantId());
        List<DormantApplicationCountVM>  dormantApplicationCountList = new ArrayList<>();
        if (dormantApplicationCountDTO.getStatus() == ReportTableType.SUBMIT_TO_JOB) {
            List<DormantApplicationCountVM> searchDormantApplicationCountList = partitionCountSearch(dormantApplicationCountDTO, DormantApplicationCountVM.class, defaultParamMap, UserRole.AM);
            CollectionUtil.addAll(dormantApplicationCountList, searchDormantApplicationCountList);
            List<DormantApplicationCountVM> searchDormantApplicationCountListByCoAM = partitionCountSearch(dormantApplicationCountDTO, DormantApplicationCountVM.class, defaultParamMap, UserRole.CO_AM);
            CollectionUtil.addAll(dormantApplicationCountList, searchDormantApplicationCountListByCoAM);
        } else {
            List<DormantApplicationCountVM> searchDormantApplicationCountList = partitionCountSearch(dormantApplicationCountDTO, DormantApplicationCountVM.class, defaultParamMap, UserRole.RECRUITER);
            CollectionUtil.addAll(dormantApplicationCountList, searchDormantApplicationCountList);
        }

        List<Long> userIdList = dormantApplicationCountList.stream().map(DormantApplicationCountVM::getId).distinct().collect(Collectors.toList());

        String searchTeamNameSql = "SELECT GROUP_CONCAT(t.`name`) team, tu.user_id FROM permission_team t LEFT JOIN permission_user_team tu ON t.id = tu.team_id WHERE tu.user_id IN (?1) GROUP BY tu.user_id";
        List<TeamNameVM> teamNameList = searchEntityListByIds(userIdList, TeamNameVM.class, searchTeamNameSql);
        Map<Long, TeamNameVM> teamNameMap = teamNameList.stream().collect(Collectors.toMap(TeamNameVM::getUserId, TeamNameVM -> TeamNameVM));

        List<DormantApplicationCountVO> resultList = new ArrayList<>();
        dormantApplicationCountList.forEach(o -> {
            DormantApplicationCountVO dormantApplicationCountVO = DormantApplicationCountVO.fromDormantApplicationCountVM(o);
            if (teamNameMap.containsKey(o.getId())) {
                TeamNameVM teamName = teamNameMap.get(o.getId());
                dormantApplicationCountVO.setTeam(teamName.getTeam());
            }

            resultList.add(dormantApplicationCountVO);
        });
        return resultList;
    }

    @Override
    @ProcessConfidentialTalent
    public List<DormantApplicationVO> getDormantApplicationsByRecruiterId(Long id, ReportTableType status) {

        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> paramsMap = new HashMap<>();
        List<DormantApplicationVM> dormantApplicationVMList = new ArrayList<>();
        if (status == ReportTableType.SUBMIT_TO_JOB) {
            createQueryAllDormantByUser(sb, id, status, UserRole.AM);
            List<DormantApplicationVM> searchDormantApplicationVMList = searchData(sb.toString(), DormantApplicationVM.class, paramsMap);
            CollectionUtil.addAll(dormantApplicationVMList, searchDormantApplicationVMList);

            StringBuilder sb2 = new StringBuilder();
            createQueryAllDormantByUser(sb2, id, status, UserRole.CO_AM);
            List<DormantApplicationVM> searchDormantApplicationVMListByCOAM = searchData(sb2.toString(), DormantApplicationVM.class, paramsMap);
            CollectionUtil.addAll(dormantApplicationVMList, searchDormantApplicationVMListByCOAM);
        } else {
            createQueryAllDormantByUser(sb, id, status, UserRole.RECRUITER);
            List<DormantApplicationVM> searchDormantApplicationVMList = searchData(sb.toString(), DormantApplicationVM.class, paramsMap);
            CollectionUtil.addAll(dormantApplicationVMList, searchDormantApplicationVMList);
        }
        return dormantApplicationVMList.stream().map(o -> fromDormantApplicationVM(o, status)).collect(Collectors.toList());
    }

    public static DormantApplicationVO fromDormantApplicationVM(DormantApplicationVM dormantApplicationVM, ReportTableType status) {
        DormantApplicationVO dormantApplicationVO = new DormantApplicationVO();
        ServiceUtils.myCopyProperties(dormantApplicationVM, dormantApplicationVO);
        dormantApplicationVO.setAm(CommonUtils.formatFullName(dormantApplicationVM.getAmFirstName(), dormantApplicationVM.getAmLastName()));
        dormantApplicationVO.setRecruiter(CommonUtils.formatFullName(dormantApplicationVM.getRecruiterFirstName(), dormantApplicationVM.getRecruiterLastName()));
        dormantApplicationVO.setStatus(status);
        return dormantApplicationVO;
    }

    private void createQueryAllDormantCountByUser(StringBuilder sb, Map<Integer, Object> paramMap, DormantApplicationCountDTO dormantApplicationCountDTO, UserRole userRole) {
        sb.append("""
                	SELECT 
                	tru.user_id id,
                	u.first_name recruiter_first_name,
                	u.last_name recruiter_last_name,
                	COUNT(DISTINCT trpc.talent_recruitment_process_id) count
                  FROM 
                 """).append(dormantApplicationCountDTO.getStatus().getDbTableName()).append(" trpc " +
                "	INNER JOIN talent_recruitment_process trp ON trp.id = trpc.talent_recruitment_process_id AND TIMESTAMPDIFF(HOUR, trp.last_modified_date, SYSDATE()) > ")
                .append(dormantApplicationCountDTO.getStatus().getDormantTime())
                .append("   INNER JOIN talent_recruitment_process_node trpn ON trpn.talent_recruitment_process_id = trp.id AND trpn.node_status = 1 AND trpn.node_type = ")
                .append(dormantApplicationCountDTO.getStatus().toDbValue())
                .append(StrUtil.SPACE)
                .append(" 	LEFT JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id " +
                "	LEFT JOIN permission_user_team put ON put.user_id = tru.user_id " +
                "	INNER JOIN job j ON j.id = trp.job_id" +
                "	INNER JOIN user u ON u.id = tru.user_id" +
                "	WHERE tru.user_role = ").append(userRole.toDbValue()).append("  " +
                "	AND trp.tenant_id = ?1 " +
                "	AND j.`status` IN (0, 2)");

        setTeamIdParams(sb, paramMap, dormantApplicationCountDTO);

        setUserIdParams(sb, paramMap, dormantApplicationCountDTO.getUserId());

        sb.append("	GROUP BY tru.user_id");
    }

    private void createQueryAllDormantByUser(StringBuilder sb, Long userId, ReportTableType status, UserRole userRole) {
        sb.append("""
                SELECT 
                	DISTINCT trp.id,
                	t.id talent_id,
                	t.full_name full_name,
                	j.id job_id,
                	j.title job_title,
                	rp.job_type,
                	j.status job_status,
                	j.company_id,
                	c.`full_business_name` company,
                	am.id am_id,
                	am.first_name am_first_name,
                	am.last_name am_last_name,
                	tru.user_id recruiter_id,
                	recruiter.first_name recruiter_first_name,
                	recruiter.last_name recruiter_last_name,
                	trp.last_modified_date last_modified_date
                 FROM 
                """)
                .append(status.getDbTableName())
                .append(" trpc INNER JOIN talent_recruitment_process trp ON trp.id = trpc.talent_recruitment_process_id AND TIMESTAMPDIFF(HOUR, trp.last_modified_date, SYSDATE()) > ")
                .append(status.getDormantTime())
                .append(" INNER JOIN talent_recruitment_process_node trpn ON trpn.talent_recruitment_process_id = trp.id AND trpn.node_status = 1 AND trpn.node_type = ")
                .append(status.toDbValue())
                .append(StrUtil.SPACE)
                .append("""
                	LEFT JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id
                	INNER JOIN job j  ON j.id = trp.job_id
                 inner join recruitment_process rp on rp.id = j.recruitment_process_id 
                	INNER JOIN talent t ON t.id = trp.talent_id
                	LEFT JOIN business_flow_administrator relation ON relation.company_id = j.company_id and relation.sales_lead_role in (0,3) and relation.user_id = 
                 """)
                .append(userId)
                .append("""
                    LEFT JOIN `user` am ON am.id = relation.user_id
                	INNER JOIN company c ON c.id = j.company_id
                	LEFT JOIN `user` recruiter ON recruiter.id = tru.user_id
                	WHERE tru.user_role = 
                	""")
                .append(userRole.toDbValue())
                .append("	AND trp.tenant_id = ")
                .append(SecurityUtils.getTenantId())
                .append("	AND j.`status` IN (0,2) AND tru.user_id = ")
                .append(userId)
                .append(" GROUP BY trp.id ");
    }

    private void setTeamIdParams(StringBuilder sb, Map<Integer, Object> paramMap, DormantApplicationCountDTO dormantApplicationCountDTO) {
        if (CollectionUtils.isNotEmpty(dormantApplicationCountDTO.getTeamIds())) {
            sb.append(" AND put.team_id IN ?");
            sb.append(paramMap.size() + 1);
            List<Long> singleTeamIdList = dormantApplicationCountDTO.getTeamIds().stream().distinct().collect(Collectors.toList());
            paramMap.put(paramMap.size() + 1, singleTeamIdList);
        }
    }

    private void setUserIdParams(StringBuilder sb, Map<Integer, Object> paramMap, Long userId) {
        if (ObjectUtil.isNotEmpty(userId)) {
            sb.append("		AND tru.user_id = ?");
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, userId);
        }
    }

    private <T> List<T> partitionCountSearch(DormantApplicationCountDTO dormantApplicationCountDTO, Class<T> clazz, Map<Integer, Object> defaultParamMap, UserRole userRole) {
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> map = new HashMap<>(16);
        map.putAll(defaultParamMap);
        createQueryAllDormantCountByUser(sb, map, dormantApplicationCountDTO, userRole);
        return searchData(sb.toString(), clazz, map);
    }

    private <T> List<T> searchEntityListByIds(List<Long> userIdList, Class<T> clazz, String selectSql) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, userIdList);
        List<T> entityList = searchData(selectSql, clazz, paramMap);
        if (CollectionUtils.isEmpty(entityList)) {
            return new ArrayList<>();
        }
        return entityList;
    }

}
