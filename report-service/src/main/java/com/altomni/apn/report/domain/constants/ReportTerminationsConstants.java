package com.altomni.apn.report.domain.constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface ReportTerminationsConstants {

    Map<String, String> ORDER_COLUMN_LIST = new HashMap<>() {{
        put("full_name", "t.full_name");
        put("job_title", "j.title");
        put("company", "c.full_business_name");
        put("assignment_division", "tta.assignment_division");
        put("am", "(select GROUP_CONCAT(sc.user_full_name) from start_commission sc where sc.start_id = s.id and sc.user_role = 0 group by sc.start_id)");
        put("recruiter", "GROUP_CONCAT(recruitersc.user_full_name order by recruitersc.user_id)");
        put("assignment_id", "abi.id");
        put("job_id", "s.job_id");
        put("job_code", "j.code");
        put("hiring_manager", "cslcct.full_name");
        put("recruiter_team", "GROUP_CONCAT(tm.`name` order by recruitersc.user_id)");
        put("city", "IF(sa.id is null, jl.original_loc ->> '$.city', IFNULL(sa.city, sa.original_loc ->> '$.city'))");
        put("state", "IF(sa.id is null, jl.original_loc ->> '$.province', IFNULL(sa.province, sa.original_loc ->> '$.province'))");
        put("country", "IF(sa.id is null, jl.original_loc ->> '$.country', IFNULL(sa.country, sa.original_loc ->> '$.country'))");
        put("start_date", "s.start_date");
        put("end_date", "s.end_date");
        put("bill_rate", "scr.final_bill_rate");
        put("bill_rate_unit", "scr.rate_unit_type");
        put("pay_rate", "scr.final_pay_rate");
        put("pay_rate_unit", "scr.rate_unit_type");
        put("termination_date", "st.termination_date");
        put("terminated_by", "(case CONCAT(u.first_name,u.last_name)  regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, \" \",u.last_name) END)");
    }};

    List<String> MULTILINGUAL_MIXED_ORDER_COLUMN_LIST = new ArrayList<>() {
        {
            add("full_name");
            add("job_title");
            add("company");
            add("am");
            add("recruiter_team");
            add("hiring_manager");
            add("city");
            add("state");
            add("country");
            add("terminated_by");
        }
    };

}
