package com.altomni.apn.report.service.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.ReportAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.config.env.ReportApiPromptProperties;
import com.altomni.apn.report.domain.vo.ContractorTerminationVo;
import com.altomni.apn.report.dto.ContractorSearchDto;
import com.altomni.apn.report.repository.ContractorReportTerminationsRepository;
import com.altomni.apn.report.service.ContractorReportTerminationsService;
import com.altomni.apn.report.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

@Slf4j
@Service("contractorReportTerminationsService")
public class ContractorReportTerminationsServiceImpl implements ContractorReportTerminationsService {

    @Resource
    private ContractorReportTerminationsRepository contractorReportTerminationsRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ReportApiPromptProperties reportApiPromptProperties;

    @Override
    public Page<ContractorTerminationVo> getContractorTerminations(ContractorSearchDto searchDto, Pageable pageable) throws ExecutionException, InterruptedException {
        checkParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return contractorReportTerminationsRepository.searchContractorTerminationsCount(searchDto);
        });
        CompletableFuture<List<ContractorTerminationVo>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return contractorReportTerminationsRepository.searchContractorTerminationsData(searchDto, pageable);
        });
        return new PageImpl<>(dataFuture.get(), Pageable.unpaged(), countFuture.get());
    }

    @Override
    public void exportContractorTerminations(ContractorSearchDto searchDto, Pageable pageable, HttpServletResponse response) {
        checkParam(searchDto);
        StopWatch stopWatch = new StopWatch("exportContractorTerminations");
        stopWatch.start("1 search ContractorTerminations data");
        List<ContractorTerminationVo> voList = contractorReportTerminationsRepository.searchContractorTerminationsData(searchDto, pageable);
        stopWatch.stop();
        stopWatch.start("2 downloadExcel task");
        ExcelUtil.downloadExcelCustom(response, ContractorTerminationVo.class, voList, "", "contractor_termination_report_" + searchDto.getFrom() + "_" + searchDto.getTo() + ".xlsx", true);
        stopWatch.stop();
        log.info(" exportContractorTerminations time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    private void checkParam(ContractorSearchDto searchDto) {
        if (searchDto.getFrom() == null || searchDto.getTo() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORT_CHECKPARAM_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
    }

}
