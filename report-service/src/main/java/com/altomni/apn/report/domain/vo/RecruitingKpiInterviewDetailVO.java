package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.enumeration.application.InterviewType;
import com.altomni.apn.common.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiInterviewDetailVO extends RecruitingKpiApplicationBaseDetailVO {

    private Long interviewProgress;

    private InterviewType interviewType;

    private String timeZone;

    private Instant fromTime;

//    public String getFromTime() {
//        if (ObjectUtil.isEmpty(fromTime)) {
//            return "";
//        }
//        return fromTime.atZone(ZoneId.of("UTC")).toLocalDateTime().format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_MM_SS));
//    }

    private Instant toTime;

//    public String getToTime() {
//        if (ObjectUtil.isEmpty(toTime)) {
//            return "";
//        }
//        return toTime.atZone(ZoneId.of("UTC")).toLocalDateTime().format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_MM_SS));
//    }

    @Override
    public void encrypt() {
        super.encrypt();
        this.interviewProgress = null;
        this.interviewType = null;
        this.timeZone = null;
        this.fromTime = null;
        this.toTime = null;
    }
}
