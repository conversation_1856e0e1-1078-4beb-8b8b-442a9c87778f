package com.altomni.apn.report.domain.vo.e5;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class TeamAdoptionReportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long teamId;

    private String teamName;

    private Long numberOfUsers;

    private Long averageActiveDuration;

    private Long noUsageUsersCount;

    private Long lowAverageUserCount;

    private Integer callCount;

    private Integer uniqueCalledTalentCount;

    private Long noteCount;

    private Integer uniqueNotedTalentCount;

    private Long emailCount;

    private Long uniqueEmailedTalentCount;

    private Long submitToJobCount;

    private Long interviewCount;

    private Long uniqueInterviewedTalentCount;

    private Long onboardTalentCount;

    private Integer activeDurationThreshold;

}
