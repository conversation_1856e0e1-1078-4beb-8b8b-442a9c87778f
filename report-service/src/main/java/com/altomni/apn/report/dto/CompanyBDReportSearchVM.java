package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.dict.EnumCompanyServiceType;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.company.CompanyType;
import com.altomni.apn.common.domain.enumeration.company.CompanyTypeConverter;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.company.domain.enumeration.business.SalesLeadStatus;

import javax.persistence.Convert;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Set;

public class CompanyBDReportSearchVM {

    private String companyName;

    public List<EnumRelationDTO> industries;

    private SalesLeadStatus salesLeadStatus;

    @Convert(converter = CompanyTypeConverter.class)
    private CompanyType type;

    private Set<UserBriefDTO> owners;

    private Set<UserBriefDTO> bdOwners;

    private Set<Long> teamUserIds;

    private BigDecimal accountProgress;

    private Set<EnumCompanyServiceType> serviceTypes;

    private Set<String> countries;

    private String createdBy;

    private Set<Long> teamIds;

    private Instant fromDate;

    private Instant toDate;

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Set<Long> getTeamUserIds() { return teamUserIds; }

    public void setTeamUserIds(Set<Long> teamUserIds) { this.teamUserIds = teamUserIds; }

    public BigDecimal getAccountProgress() {
        return accountProgress;
    }

    public void setAccountProgress(BigDecimal accountProgress) {
        this.accountProgress = accountProgress;
    }

    public Set<EnumCompanyServiceType> getServiceTypes() {
        return serviceTypes;
    }

    public void setServiceTypes(Set<EnumCompanyServiceType> serviceTypes) {
        this.serviceTypes = serviceTypes;
    }

    public Set<String> getCountries() {
        return countries;
    }

    public void setCountries(Set<String> countries) {
        this.countries = countries;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Set<Long> getTeamIds() { return teamIds; }

    public void setTeamIds(Set<Long> teamIds) { this.teamIds = teamIds; }

    public Instant getFromDate() {
        return fromDate;
    }

    public void setFromDate(Instant fromDate) {
        this.fromDate = fromDate;
    }

    public Instant getToDate() {
        return toDate;
    }

    public void setToDate(Instant toDate) {
        this.toDate = toDate;
    }


    public SalesLeadStatus getSalesLeadStatus() {
        if (salesLeadStatus == null && type != null) {
            if (Objects.equals(CompanyType.POTENTIAL_CLIENT, type)) {
                return SalesLeadStatus.PROSPECT;
            }
            return SalesLeadStatus.CLIENT;
        }
        return salesLeadStatus;
    }


    public void setSalesLeadStatus(SalesLeadStatus salesLeadStatus) {
        this.salesLeadStatus = salesLeadStatus;
    }

    public CompanyType getType() {
        return type;
    }

    public void setType(CompanyType type) {
        this.type = type;
    }

    public List<EnumRelationDTO> getIndustries() {
        return industries;
    }

    public void setIndustries(List<EnumRelationDTO> industries) {
        this.industries = industries;
    }

    public Set<UserBriefDTO> getOwners() {
        return owners;
    }

    public void setOwners(Set<UserBriefDTO> owners) {
        this.owners = owners;
    }

    public Set<UserBriefDTO> getBdOwners() {
        return bdOwners;
    }

    public void setBdOwners(Set<UserBriefDTO> bdOwners) {
        this.bdOwners = bdOwners;
    }
}
