package com.altomni.apn.report.domain;

import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Entity
public class DormantJobCountDTO implements Serializable {

    private static final long serialVersionUID = -1712376179290297440L;

    @Id
    @ApiModelProperty(value = "AM id")
    private Long id;

    @ApiModelProperty(value = "AM name")
    private String am;

    @ApiModelProperty(value = "total number of dormant jobs")
    private Integer count;

    @ApiModelProperty(value = "AM's teams")
    private String team;

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getAm() { return am; }

    public void setAm(String am) { this.am = am; }

    public Integer getCount() { return count; }

    public void setCount(Integer count) { this.count = count; }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

    @Override
    public String toString() {
        return "DormantJobCountDTO{" +
            "id=" + id +
            ", am='" + am + '\'' +
            ", count=" + count +
            ", team='" + team + '\'' +
            '}';
    }
}
