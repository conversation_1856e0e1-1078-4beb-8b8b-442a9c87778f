package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatus;
import com.altomni.apn.finance.domain.enumeration.invoice.InvoiceStatusConverter;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import lombok.Data;

import javax.persistence.*;
import java.time.Instant;
import java.util.List;

@Data
@Entity
public class FteBdReportOnboardDetailVO implements AttachConfidentialTalent {

    @Id
    @ExcelIgnore
    @Column(name = "id", nullable = false)
    private String id;

    private Long talentId;

    private String candidateName;

    private Long jobId;

    private String jobTitle;

    private Instant onboardingDate;

    private String recruiter;

    private String am;

    private String coAm;

    @Transient
    private List<UserCountryVO> coAmList;

    private Instant invoiceDate;

    private Integer currency;

    private Integer cashInCurrency;

    private Double originBilling;

    private Double billing;

    private Double originCashIn;

    private Double cashIn;

    private Double originBalance;

    private Double balance;

    private Instant dueDate;

    @Convert(converter = InvoiceStatusConverter.class)
    private InvoiceStatus paymentStatus;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;


    @Override
    public void encrypt() {
       this. candidateName = null;
       this.jobId = null;
       this.jobTitle = null;
       this.onboardingDate = null;
       this.recruiter = null;
       this.am = null;
       this.coAm = null;
       this.coAmList = null;
       this.invoiceDate = null;
       this.currency = null;
       this.cashInCurrency = null;
       this.originBilling = null;
       this.billing = null;
       this.originCashIn = null;
       this.cashIn = null;
       this.originBalance = null;
       this.balance = null;
       this.dueDate = null;
       this.paymentStatus = null;
    }
}
