package com.altomni.apn.report.service;

import com.altomni.apn.company.web.rest.vm.company.CompanyProspectVM;
import com.altomni.apn.report.domain.vo.ReportBdTrackingDetailVO;
import com.altomni.apn.report.domain.vo.ReportBdTrackingVO;
import com.altomni.apn.report.dto.BdTrackingDetailDTO;
import com.altomni.apn.report.dto.BdTrackingSearchDTO;
import com.altomni.apn.report.dto.CompanyBDReportSearchVM;
import com.altomni.apn.report.dto.ReportJobParam;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface CompanyReportService {

    ResponseEntity<List<CompanyProspectVM>> getBDReport(Long teamId, Pageable pageable);

    ResponseEntity<List<CompanyProspectVM>> getBDReport(CompanyBDReportSearchVM companyBDReportSearchVM, Pageable pageable);

    List<ReportBdTrackingVO> getBdTrackingReport(BdTrackingSearchDTO bdTrackingSearchDTO);

    List<ReportBdTrackingDetailVO> getBdTrackingDetailList(BdTrackingDetailDTO bdTrackingSearchDTO);
}
