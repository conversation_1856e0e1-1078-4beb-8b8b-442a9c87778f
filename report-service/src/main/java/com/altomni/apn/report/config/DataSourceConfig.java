package com.altomni.apn.report.config;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.altomni.apn.report.domain.enumeration.DataSourceType;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.jdbc.DataSourceProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.datasource.lookup.AbstractRoutingDataSource;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Configuration
@EnableConfigurationProperties(StarRocksProperties.class)
public class DataSourceConfig {

    private final StarRocksProperties starRocksProperties;
    private final NacosConfigManager nacosConfigManager;
    private final DataSourceProperties dataSourceProperties;

    public DataSourceConfig(StarRocksProperties starRocksProperties, NacosConfigManager nacosConfigManager, DataSourceProperties dataSourceProperties) {
        this.starRocksProperties = starRocksProperties;
        this.nacosConfigManager = nacosConfigManager;
        this.dataSourceProperties = dataSourceProperties;
    }


    @Bean
    @Primary
    @RefreshScope
    public DataSource dynamicDataSource() {
        Map<Object, Object> dataSourceMap = new HashMap<>();
        HikariDataSource mysqlDataSource = dataSourceProperties.initializeDataSourceBuilder().type(HikariDataSource.class).build();
        dataSourceMap.put(DataSourceType.MYSQL, mysqlDataSource);
        starRocksDataSource().ifPresent(starRocksDataSource -> dataSourceMap.put(DataSourceType.STARROCKS, starRocksDataSource));
        log.info("Dynamic data source is created. Data sources: {}", dataSourceMap.keySet());
        DataSourceRouting dataSourceRouting = new DataSourceRouting();
        dataSourceRouting.setTargetDataSources(dataSourceMap);
        dataSourceRouting.setDefaultTargetDataSource(mysqlDataSource);
        return dataSourceRouting;
    }

    public static class DataSourceRouting extends AbstractRoutingDataSource {
        @Override
        protected Object determineCurrentLookupKey() {
            return DataSourceHolder.getCurrentDb();
        }
    }

    private Optional<DataSource> starRocksDataSource() {
        if (!starRocksProperties.isEnabled()) {
            log.info("StarRocks is not enabled.");
            return Optional.empty();
        }
        log.info("StarRocks is enabled.");
        HikariDataSource starRocksDataSource = dataSourceProperties.initializeDataSourceBuilder()
                .url(starRocksProperties.getUrl())
                .username(starRocksProperties.getUsername())
                .password(starRocksProperties.getPassword())
                .type(HikariDataSource.class)
                .build();
        return Optional.of(starRocksDataSource);
    }

}
