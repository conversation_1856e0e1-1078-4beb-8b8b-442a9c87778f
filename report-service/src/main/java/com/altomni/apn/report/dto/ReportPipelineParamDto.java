package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;

@Data
@NoArgsConstructor
public class ReportPipelineParamDto implements Serializable {

    private static final long serialVersionUID = 6994704573277709468L;

    private String fromDate;

    private String toDate;

    private Long userId;

    private UserRole[] userRoles;

    private Long teamId;

    private Long divisionId;

    private UserRole userRole;

    private List<JobType> jobTypes;

    @JsonIgnore
    private List<Long> userIds;

    private List<Long> teamIds;

    private String jobCountry;

    private static final String ALL = "all";
}
