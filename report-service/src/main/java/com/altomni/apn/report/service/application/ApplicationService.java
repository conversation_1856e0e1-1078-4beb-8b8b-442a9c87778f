package com.altomni.apn.report.service.application;

import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.List;

/**
 * Service Interface for managing.
 */
@Component
@FeignClient(value = "application-service")
public interface ApplicationService {

    @GetMapping("/application/api/v3/recruitment-processes/get-all")
    List<RecruitmentProcessVO> getAllRecruitmentProcess();
}
