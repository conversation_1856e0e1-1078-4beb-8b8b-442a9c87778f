package com.altomni.apn.report.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiDetailBaseDto;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiApnProNoteDetailSearchDto extends RecruitingKpiDetailBaseDto {

    private ApnProNoteDetailSearchDto detail;

    public void appendPermission(StringBuilder sb, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDTO) {
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            sb.append(" and put.user_id = :userId ");
            whereParamMap.put("userId", getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            sb.append(" and put.team_id in :teamIds ");
            whereParamMap.put("teamIds", teamDTO.getNestedTeamIds());
        }
    }

    public String getOrderBySql() {
        if (ObjectUtil.isEmpty(getSort())) {
            return "";
        }
        return switch (getSort().getProperty()) {
            case "fullName" -> " order by CONVERT( t.full_name USING gbk) " + getSort().getDirection();
            case "sourceChannel" -> " order by ttn.tracking_platform " + getSort().getDirection();
            case "jobSearchStatus" -> " order by t.motivation_id " + getSort().getDirection();
            case "createdDate" -> " order by ttn.created_date " + getSort().getDirection();
            case "lastModifiedDate" -> " order by ttn.last_modified_date " + getSort().getDirection();
            default -> "";
        };
    }

    public void appendDetailCondition(StringBuilder sb, Map<String, Object> whereParamMap) {
        if (detail == null) {
            return;
        }
        if (StrUtil.isNotBlank(detail.getFullName())) {
            sb.append(" and t.full_name like :fullName ");
            whereParamMap.put("fullName", "%" + detail.getFullName() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getSourceChannel())) {
            sb.append(" and ttn.tracking_platform = :sourceChannel ");
            whereParamMap.put("sourceChannel", detail.getSourceChannel().toDbValue());
        }
        if (ObjectUtil.isNotEmpty(detail.getTalentId())) {
            sb.append(" and t.id = :talentId ");
            whereParamMap.put("talentId", detail.getTalentId());
        }
        if (ObjectUtil.isNotEmpty(detail.getJobSearchStatus())) {
            sb.append(" and t.motivation_id = :jobSearchStatus ");
            whereParamMap.put("jobSearchStatus", detail.getJobSearchStatus());
        }
        if (ObjectUtil.isNotEmpty(detail.getCreatedBy())) {
            sb.append(" and ttn.user_id = :createdBy ");
            whereParamMap.put("createdBy", detail.getCreatedBy());
        }
        if (ObjectUtil.isNotEmpty(detail.getLastModifiedBy())) {
            sb.append(" and ttn.last_update_user_id = :lastModifiedBy ");
            whereParamMap.put("lastModifiedBy", detail.getLastModifiedBy());
        }
    }

}
