package com.altomni.apn.report.config.audit;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.utils.SecurityUtils;
import org.springframework.data.domain.AuditorAware;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * Implementation of AuditorAware based on Spring Security.
 */
@Component
public class SpringSecurityAuditorAware implements AuditorAware<String> {

    @Override
    public Optional<String> getCurrentAuditor() {
        String userUid = SecurityUtils.getUserUid();
        if (userUid == null || userUid.isEmpty()) {
            return Optional.of(Constants.SYSTEM);
        }
        return Optional.of(userUid);
    }
}
