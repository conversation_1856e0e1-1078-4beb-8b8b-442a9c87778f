package com.altomni.apn.report.domain.constants;

import java.util.HashMap;
import java.util.Map;

public interface ReportHiresConstants {

    Map<String, String> sourcingChannelMap = new HashMap<>(){{
        put("LINKEDIN","LinkedIn" );
        put("DIC<PERSON>","Dice" );
        put("COMMON_POOL","Common Pool" );
        put("CAREER_BUILDER","Career Builder" );
        put("INDEED","Indeed" );
        put("MONSTER","Monster" );
        put("JAZZHR","JazzHR" );
        put("REFERRAL","Referral" );
        put("CAMPUS_RECRUITING","Campus Recruiting" );
        put("MARKETING_EVENT","Marketing Event" );
        put("INTELLIPRO_WEBSITE","IntelliPro Website" );
        put("SOCIAL_MEDIA","Social Media" );
        put("OTHER","Others" );
    }};

    Map<String, String> MULTILINGUAL_MIXED_ORDER_COLUMN_LIST = new HashMap<>() {
        {
            put("full_name", "t.full_name");
            put("job_title", "j.title");
            put("company", "c.full_business_name");
            put("recruiter_team", "GROUP_CONCAT( tm.`name` order by recruitersc.user_id )");
            put("hiring_manager", "cslcct.full_name");
            put("am", "(select GROUP_CONCAT(sc.user_full_name) from start_commission sc where sc.start_id = s.id and sc.user_role = 0 group by sc.start_id)");
            put("department", "jai.department");
            put("city", "IF(sa.id is null, jl.original_loc ->> '$.city', IFNULL(sa.city, sa.original_loc ->> '$.city'))");
            put("state", "IF(sa.id is null, jl.original_loc ->> '$.province', IFNULL(sa.province, sa.original_loc ->> '$.province'))");
            put("country", "IF(sa.id is null, jl.original_loc ->> '$.country', IFNULL(sa.country, sa.original_loc ->> '$.country'))");
            put("sourcing_channel", "tai.extended_info ->> '$.source'");
        }
    };

    Map<String, String> ORDER_COLUMN_LIST = new HashMap<>() {
        {
            put("job_id", "j.id");
            put("assignment_division", "tta.assignment_division");
            put("full_name", "t.full_name");
            put("job_title", "j.title");
            put("company", "c.full_business_name");
            put("recruiter_team", "GROUP_CONCAT(tm.`name` order by recruitersc.user_id )");
            put("hiring_manager", "cslcct.full_name");
            put("am", "(select GROUP_CONCAT(sc.user_full_name) from start_commission sc where sc.start_id = s.id and sc.user_role = 0 group by sc.start_id)");
            put("department", "jai.department");
            put("city", "IF(sa.id is null, jl.original_loc ->> '$.city', IFNULL(sa.city, sa.original_loc ->> '$.city'))");
            put("state", "IF(sa.id is null, jl.original_loc ->> '$.province', IFNULL(sa.province, sa.original_loc ->> '$.province'))");
            put("country", "IF(sa.id is null, jl.original_loc ->> '$.country', IFNULL(sa.country, sa.original_loc ->> '$.country'))");
            put("sourcing_channel", "tai.extended_info ->> '$.source'");
            put("job_code", "j.CODE");
            put("start_status", "s.`start_type`");
            put("start_date", "s.start_date");
            put("end_date", "s.end_date");
            put("onboard_date", "trpo.created_date");
            put("bill_rate", "scr.final_bill_rate");
            put("pay_rate", "scr.final_pay_rate");
            put("bill_rate_unit", "scr.rate_unit_type");
            put("pay_rate_unit", "scr.rate_unit_type");
            put("hourly_margin", "(scr.final_bill_rate - scr.final_pay_rate)");
            put("hourly_margin_percent", "hourly_margin_percent");
        }
    };

}
