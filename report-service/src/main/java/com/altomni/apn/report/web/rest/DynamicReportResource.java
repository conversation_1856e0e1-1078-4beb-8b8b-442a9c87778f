package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.BadRequestAlertException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.dto.ReportDTO;
import com.altomni.apn.report.service.ReportService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.openapitools.client.model.ApiV1DatasetGet200Response;
import org.openapitools.client.model.ChartDataResponseResult;
import org.openapitools.client.model.DatasetRestApiGet;
import org.openapitools.client.model.DatasetRestApiGetList;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/v3")
public class DynamicReportResource {

    private static final String ENTITY_NAME = "Dynamic Report";

    private final ReportService reportService;

    /**
     * get all dataset
     */
    @Timed
    @GetMapping("/dynamic-reports/datasets")
    public ResponseEntity<List<DatasetRestApiGetList>> getDataSets() {
        log.info("[APN: DynamicReport @{}] REST request to get dataSets", SecurityUtils.getUserId());
        ApiV1DatasetGet200Response result = reportService.findAllDataSets();
        return ResponseEntity.ok(result.getResult());

    }

    @Timed
    @GetMapping("/dynamic-reports/datasets/{id}")
    public ResponseEntity<DatasetRestApiGet> getDataSets(@PathVariable("id") Integer id) {
        log.info("[APN: DynamicReport @{}] REST request to get dataSets info", SecurityUtils.getUserId());
        DatasetRestApiGet result = reportService.findOneDataSet(id);
        return ResponseEntity.ok(result);

    }


    @Timed
    @PostMapping("/dynamic-reports/query")
    public ResponseEntity<Object> query(@RequestBody ReportDTO reportDTO) {
        log.info("[APN: DynamicReport @{}] REST request to query", SecurityUtils.getUserId());
        ChartDataResponseResult result = reportService.olapQuery(reportDTO);
        return ResponseEntity.ok(result);

    }

    @Timed
    @GetMapping("/dynamic-reports")
    public ResponseEntity<List<ReportDTO>> getALlReports(@ApiParam Pageable pageable) {
        log.info("[APN: DynamicReport @{}] REST request to get all reports", SecurityUtils.getUserId());
        final Page<ReportDTO> reports = reportService.findAllByTenantId(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(reports, "/api/v3/dynamic-report");
        return ResponseEntity.ok().headers(headers).body(reports.getContent());
    }

    @Timed
    @GetMapping("/dynamic-reports/{id}")
    public ResponseEntity<ReportDTO> getReportById(@PathVariable("id") Long id) {
        log.info("[APN: DynamicReport @{}] REST request to get report by id: {}", SecurityUtils.getUserId(), id);
        return ResponseEntity.ok().body(reportService.findOne(id));
    }

    @Timed
    @NoRepeatSubmit
    @PostMapping("/dynamic-reports")
    public ResponseEntity<ReportDTO> createReport(@RequestBody ReportDTO reportDTO) throws URISyntaxException {
        log.info("[APN: DynamicReport @{}] REST request to create report: {}", SecurityUtils.getUserId(), reportDTO);
        if (reportDTO.getId() != null) {
            throw new BadRequestAlertException("A new report cannot already have an id");
        }
        reportDTO.setTenantId(SecurityUtils.getTenantId());
        ReportDTO result = reportService.create(reportDTO);
        return ResponseEntity.created(new URI("/api/v3/dynamic-reports/" + result.getId()))
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @Timed
    @NoRepeatSubmit
    @PutMapping("/dynamic-reports/{id}")
    public ResponseEntity<ReportDTO> updateReport(@PathVariable("id") Long id, @RequestBody ReportDTO reportDTO) {
        log.info("[APN: DynamicReport @{}] REST request to update report: {}", SecurityUtils.getUserId(), reportDTO);
        return ResponseEntity.ok().body(reportService.update(id, reportDTO));
    }


    @Timed
    @NoRepeatSubmit
    @DeleteMapping("/dynamic-reports/{id}")
    public ResponseEntity<Void> deleteReport(@PathVariable("id") Long id) {
        log.info("[APN: DynamicReport @{}] REST request to delete report by id: {}", SecurityUtils.getUserId(), id);
        reportService.delete(id);
        return ResponseEntity.ok().build();
    }

}
