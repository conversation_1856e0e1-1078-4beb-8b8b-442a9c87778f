package com.altomni.apn.report.dto.s3;

import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class DormantApplicationCountDTO implements Serializable {

    private static final long serialVersionUID = 6994704573277709468L;

    private Long userId;

    private List<Long> teamIds;

    @NotNull
    private ReportTableType status;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public List<Long> getTeamIds() {
        return teamIds;
    }

    public void setTeamIds(List<Long> teamIds) {
        this.teamIds = teamIds;
    }

    public ReportTableType getStatus() {
        return status;
    }

    public void setStatus(ReportTableType status) {
        this.status = status;
    }

    public DormantApplicationCountDTO() {
    }
}
