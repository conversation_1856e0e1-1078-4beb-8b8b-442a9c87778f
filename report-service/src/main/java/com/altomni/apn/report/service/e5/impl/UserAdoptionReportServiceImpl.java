package com.altomni.apn.report.service.e5.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.email.EmailLogOverviewDTO;
import com.altomni.apn.common.dto.email.MailSearchDTO;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.salelead.TalentClientContactRelationDTO;
import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.user.UserActiveDurationStatistic;
import com.altomni.apn.report.domain.enumeration.E5ReportViewType;
import com.altomni.apn.report.domain.vo.e5.*;
import com.altomni.apn.report.domain.vo.p1.PipelineAnalyticsVO;
import com.altomni.apn.common.domain.user.GetLastWeekActiveDurationUserInfoDTO;
import com.altomni.apn.report.dto.UserTeamPariDTO;
import com.altomni.apn.report.dto.e5.*;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.company.CompanyService;
import com.altomni.apn.report.service.e5.UserActiveDurationService;
import com.altomni.apn.report.service.e5.UserAdoptionReportDetailsService;
import com.altomni.apn.report.service.e5.UserAdoptionReportService;
import com.altomni.apn.report.service.email.EmailService;
import com.altomni.apn.report.service.user.UserService;
import com.altomni.apn.report.util.ExcelUtil;
import com.altomni.apn.user.service.dto.permission.PermissionTeamTreeDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.testng.annotations.Test;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.text.Collator;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collector;
import java.util.stream.Collectors;

import static com.altomni.apn.common.config.constants.ReportConstants.GRAND_TOTAL;

/**
 * Service Implementation for UserAdoptionReportServiceImpl.
 */
@Slf4j
@Service
public class UserAdoptionReportServiceImpl implements UserAdoptionReportService {

    private volatile Integer activeDurationThreshold;

    private static final long SECONDS_TO_MINUTES = 60;

    private static final Integer TEAM_DATA_SCOPE = 2;

    private static final Integer ALL_DATA_SCOPE = 99;

    @Resource
    private UserActiveDurationService userActiveDurationService;

    @Resource
    private UserService userService;

    @Resource
    private UserAdoptionReportDetailsService userAdoptionReportDetailsService;

    @Resource
    private CompanyService companyService;

    @Resource
    private EmailService emailService;

    @PostConstruct
    public void init() {
            this.activeDurationThreshold = 582; //default value in minute
    }

    @Override
    public Integer getActiveDurationThreshold() { return activeDurationThreshold; }

    @Override
    public Integer getActiveDurationThresholdV2(LocalDate startTime, LocalDate endTime) {
        long days = ChronoUnit.DAYS.between(startTime, endTime) + 1;
        double a1 = (double)days / 7;
        double a2 = a1 *  activeDurationThreshold;
        int floor = (int) Math.floor(a2);
        return Integer.valueOf(floor);
    }

    private static <T extends Comparable<T>> Comparator<T> getNullHandlingComparator(Sort.Order order) {
        return Comparator.nullsLast(Comparator.naturalOrder());
    }

    private static Comparator<UserAdoptionReportVO> getUserAdoptionReportComparatorFromSort(Sort sort) {
        return sort.stream()
                .map(UserAdoptionReportServiceImpl::getUserAdoptionReportComparator)
                .filter(Objects::nonNull)
                .reduce(Comparator::thenComparing)
                .orElse((u1, u2) -> 0); // 如果没有排序规则，则不变
    }

    private static Comparator<UserAdoptionReportVO> getUserAdoptionReportComparator(Sort.Order order) {
        Comparator<UserAdoptionReportVO> comparator = null;

        switch (order.getProperty().toLowerCase()) {
            case "username" -> {
                Collator collator = Collator.getInstance(Locale.CHINA); // 中文拼音排序
                comparator = Comparator.comparing(
                        UserAdoptionReportVO::getUserName,
                        Comparator.nullsLast(collator)
                );
            }
            case "activationduration" -> comparator = Comparator.comparing(UserAdoptionReportVO::getActivationDuration, getNullHandlingComparator(order));
            case "callcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getCallCount, getNullHandlingComparator(order));
            case "notecount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getNoteCount, getNullHandlingComparator(order));
            case "emailcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getEmailCount, getNullHandlingComparator(order));
            case "submittojobcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getSubmitToJobCount, getNullHandlingComparator(order));
            case "interviewcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getInterviewCount, getNullHandlingComparator(order));
            case "onboardtalentcount" -> comparator = Comparator.comparing(UserAdoptionReportVO::getOnboardTalentCount, getNullHandlingComparator(order));
        }
        return (comparator != null && order.isDescending()) ? comparator.reversed() : comparator;
    }

    private static Comparator<TeamAdoptionReportVO> getTeamAdoptionReportComparatorFromSort(Sort sort) {
        return sort.stream()
                .map(UserAdoptionReportServiceImpl::getTeamAdoptionReportVOComparator)
                .filter(Objects::nonNull)
                .reduce(Comparator::thenComparing)
                .orElse((u1, u2) -> 0); // 如果没有排序规则，则不变
    }

    private static Comparator<TeamAdoptionReportVO> getTeamAdoptionReportVOComparator(Sort.Order order) {
        Comparator<TeamAdoptionReportVO> comparator = null;

        switch (order.getProperty().toLowerCase()) {
            case "teamname" -> {
                Collator collator = Collator.getInstance(Locale.CHINA); // 中文拼音排序
                comparator = Comparator.comparing(
                        TeamAdoptionReportVO::getTeamName,
                        Comparator.nullsLast(collator)
                );
            }
            case "numberofusers" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getNumberOfUsers, getNullHandlingComparator(order));
            case "averageactiveduration" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getAverageActiveDuration, getNullHandlingComparator(order));
            case "nousageuserscount" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getNoUsageUsersCount, getNullHandlingComparator(order));
            case "lowaverageusercount" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getLowAverageUserCount, getNullHandlingComparator(order));
            case "callcount" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getCallCount, getNullHandlingComparator(order));
            case "notecount" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getNoteCount, getNullHandlingComparator(order));
            case "emailcount" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getEmailCount, getNullHandlingComparator(order));
            case "submittojobcount" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getSubmitToJobCount, getNullHandlingComparator(order));
            case "interviewcount" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getInterviewCount, getNullHandlingComparator(order));
            case "onboardtalentcount" -> comparator = Comparator.comparing(TeamAdoptionReportVO::getOnboardTalentCount, getNullHandlingComparator(order));
        }
        return (comparator != null && order.isDescending()) ? comparator.reversed() : comparator;
    }

    @Override
    public Map<Long, Long> getLastWeekBelowAverageActiveDurationUserByTeamId(Long tenantId, List<Long> teamIds) {
        Set<Long> teamIdSet = new HashSet<>(teamIds);
        Set<Long> teamUserIds = userService.getAllActiveTeamUserIdsByPermissionTeamIdIn(teamIdSet).getBody();
        LocalDate today = LocalDate.now();
        LocalDate lastSunday = today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
        LocalDate lastMonday = lastSunday.minusDays(6);
        Map<Long, Long> userDurationTimeMap = userActiveDurationService.getUsersByOnlineDuration(tenantId, lastMonday.toString(), lastSunday.toString(), teamUserIds, getActiveDurationThreshold());
        return userDurationTimeMap;
    }

    public Map<Long, Long> getLastWeekBelowAverageActiveDurationUserByUserId(Long tenantId, List<Long> userIds) {
        LocalDate today = LocalDate.now();
        LocalDate lastSunday = today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
        LocalDate lastMonday = lastSunday.minusDays(6);
        Map<Long, Long> userDurationTimeMap = userActiveDurationService.getUsersByOnlineDuration(tenantId, lastMonday.toString(), lastSunday.toString(), userIds, getActiveDurationThreshold());
        return userDurationTimeMap;
    }

    @Override
    public UserAdoptionReportWithThresholdVO getUserAdoptionReport(UserAdoptionReportDTO userAdoptionReportDTO, Pageable pageable) throws ExecutionException, InterruptedException {
        Sort sort = pageable.getSort();

        if (BooleanUtils.isTrue(userAdoptionReportDTO.getLastWeekActivityBelowAverage())) {
            LocalDate today = LocalDate.now();

            LocalDate lastSunday = today.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY));
            LocalDate lastMonday = lastSunday.minusDays(6);

            userAdoptionReportDTO.setStartTime(lastMonday);
            userAdoptionReportDTO.setEndTime(lastSunday);
            Set<Long> userIds = userAdoptionReportDetailsService.getReallyUserIds(userAdoptionReportDTO);
            if(CollectionUtils.isEmpty(userIds)){
                return new UserAdoptionReportWithThresholdVO();
            }
            Integer threshold = null;
            if (BooleanUtils.isNotTrue(userAdoptionReportDTO.getIgnoreThreshold())) {
                threshold = getActiveDurationThreshold();
            }
            //TODO 先注释掉，本地无法连接mongo，mock数据
            Map<Long, Long> userDurationTimeMap = userActiveDurationService.getUsersByOnlineDuration(SecurityUtils.getTenantId(), userAdoptionReportDTO.getStartTime().toString(), userAdoptionReportDTO.getEndTime().toString(), userIds, threshold);
            //Map<Long, Long> userDurationTimeMap = new HashMap<>();
            Set<Long> belowAverageUserIds = userDurationTimeMap.keySet();

            UserAdoptionReportWithThresholdVO r = new UserAdoptionReportWithThresholdVO();
            r.setUserActiveThreshold(Long.valueOf(getActiveDurationThreshold()));

            if (BooleanUtils.isTrue(userAdoptionReportDTO.getIgnoreThreshold())) {
                Long currentUserActiveDuration = userDurationTimeMap.values().stream().findFirst().orElse(0L) / SECONDS_TO_MINUTES;
                r.setPersonalViewCurrentUserActiveDuration(currentUserActiveDuration);
                if (Long.compare(currentUserActiveDuration, r.getUserActiveThreshold()) >= 0) {
                    r.setUserAdoptionReport(Collections.emptyList());
                    return r;
                }
            }

            if (CollectionUtils.isEmpty(belowAverageUserIds)) {
                r.setUserAdoptionReport(Collections.emptyList());
                return r;
            }

            ConcurrentStopWatch stopWatch = new ConcurrentStopWatch("getUserAdoptionReport");
            stopWatch.start("total task");
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture<Map<Long, int[]>> callReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("findOnboardedNotInvoiced");
                Map<Long, int[]> res = userAdoptionReportDetailsService.getCallReport(userAdoptionReportDTO, belowAverageUserIds);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getCallReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, UserAdoptionReportApplicationStatsDTO>> applicationStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("getApplicationStatsReport");
                Map<Long, UserAdoptionReportApplicationStatsDTO> res = userAdoptionReportDetailsService.getApplicationStatsReport(userAdoptionReportDTO, belowAverageUserIds);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getApplicationStatsReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, UserAdoptionReportEmailStatsDTO>> emailStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("searchSentEmailsByUsers");
                MailSearchDTO mailSearchDTO = new MailSearchDTO();
                mailSearchDTO.setFromDate(userAdoptionReportDTO.getStartTime());
                mailSearchDTO.setToDate(userAdoptionReportDTO.getEndTime());
                mailSearchDTO.setTenantId(SecurityUtils.getTenantId());
                mailSearchDTO.setUserIds(new ArrayList<>(belowAverageUserIds));
                mailSearchDTO.setCustomerId2(List.of("AC", "APN:TALENT", "VOIPNOTICE:TALENT"));
                List<EmailLogOverviewDTO> res = emailService.searchSentEmailsByUsers(mailSearchDTO).getBody();
                stopWatch.stop();
                log.info("[getUserAdoptionReport] searchSentEmailsByUsers time = {}ms ", stopWatch.getTotalTimeMillis());

                if (CollectionUtils.isEmpty(res)) {
                    return Collections.emptyMap();
                }

                Set<Long> contactIds = res.parallelStream()
                        .peek(this::extractTalentIdAndContactId)
                        .flatMap(e -> e.getContactIds().stream())
                        .collect(Collectors.toSet());

                List<TalentClientContactRelationDTO> relation = CollectionUtils.isNotEmpty(contactIds) ? companyService.getTalentIdsByContactIds(contactIds).getBody() : Collections.emptyList();

                Map<Long, Long> contactToTalentMap = !CollectionUtils.isEmpty(relation) ? relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId)) : new HashMap<>();

                return res.parallelStream()
                        // 使用 groupingByConcurrent 进行并发分组，且自定义 Collector 归并每个分组的 EmailLogOverviewDTO
                        .collect(Collectors.groupingByConcurrent(
                                EmailLogOverviewDTO::getUserId,
                                Collector.of(
                                        // 创建累加器：初始化一个新的 UserAdoptionReportEmailStatsDTO
                                        UserAdoptionReportEmailStatsDTO::new,
                                        // 累加器：将每个 EmailLogOverviewDTO 数据合并到累加器中
                                        (stats, dto) -> {
                                            stats.setId(dto.getUserId());
                                            int talentSize = CollectionUtils.size(dto.getTalentIds());
                                            int contactSize =  CollectionUtils.size(dto.getContactIds());
                                            long current = stats.getEmailCount();
                                            stats.setEmailCount(current + talentSize + contactSize);
                                            if (dto.getTalentIds() != null) {
                                                stats.getTalentIds().addAll(dto.getTalentIds());
                                            }
                                            if (dto.getContactIds() != null) {
                                                stats.getContactIds().addAll(dto.getContactIds());
                                            }
                                        },
                                        // 合并器：合并两个累加器
                                        (stats1, stats2) -> {
                                            long count1 = stats1.getEmailCount();
                                            long count2 = stats2.getEmailCount();
                                            stats1.setEmailCount(count1 + count2);
                                            stats1.getTalentIds().addAll(stats2.getTalentIds());
                                            stats1.getContactIds().addAll(stats2.getContactIds());
                                            return stats1;
                                        },
                                        // 在 finisher 中一次性并行处理 contactIds
                                        stats -> {
                                            // 使用并发集合分别收集映射的 talentId 与未映射的 contactId
                                            Set<Long> concurrentMappedTalents = ConcurrentHashMap.newKeySet();
                                            Set<Long> concurrentUnmappedContacts = ConcurrentHashMap.newKeySet();

                                            stats.getContactIds().parallelStream().forEach(contactId -> {
                                                Long mappedTalent = contactToTalentMap.get(contactId);
                                                if (mappedTalent != null) {
                                                    concurrentMappedTalents.add(mappedTalent);
                                                } else {
                                                    concurrentUnmappedContacts.add(contactId);
                                                }
                                            });

                                            // 合并原有的 talentIds 与映射到的 talentIds（自动去重）
                                            Set<Long> mergedTalentIds = new HashSet<>(stats.getTalentIds());
                                            mergedTalentIds.addAll(concurrentMappedTalents);

                                            // uniqueEmailCount = 合并后 talentIds 的数量 + 未映射 contactIds 的数量
                                            stats.setUniqueEmailCount((long) mergedTalentIds.size() + concurrentUnmappedContacts.size());
                                            return stats;
                                        }
                                )
                        ));
            });

            CompletableFuture<Map<Long, UserAdoptionReportCrmNoteStatsDTO>> crmNoteStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("getCrmNoteReport");
                List<UserAdoptionReportCrmNoteStatsDTO> res = userAdoptionReportDetailsService.getCrmNoteReport(userAdoptionReportDTO, belowAverageUserIds);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getCrmNoteReport time = {}ms ", stopWatch.getTotalTimeMillis());

                Set<Long> contactIds = res.parallelStream()
                        .flatMap(a -> a.getContactIds().stream())
                        .collect(Collectors.toSet());

                List<TalentClientContactRelationDTO> relation = companyService.getTalentIdsByContactIds(contactIds).getBody();
                if (Objects.nonNull(relation)) {
                    Map<Long, Long> contactToTalentMap = relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId));
                    res.parallelStream().forEach(item -> {
                        Set<Long> talentIds = item.getContactIds().stream()
                                .map(contactToTalentMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet());

                        item.setTalentIds(talentIds);
                        item.setUniqueContactIdCount(item.getContactIds().size() - talentIds.size());
                    });
                }

                return res.stream().collect(Collectors.toMap(UserAdoptionReportCrmNoteStatsDTO::getId, Function.identity()));
            });

            CompletableFuture.allOf(callReport, applicationStatsReport, emailStatsReport, crmNoteStatsReport)
                    .exceptionally(FutureExceptionUtil::handleFutureException);

            Map<Long, int[]> callReportMap = callReport.join();
            Map<Long, UserAdoptionReportApplicationStatsDTO> applicationStatsReportMap = applicationStatsReport.join();
            Map<Long, UserAdoptionReportEmailStatsDTO> emailStatsReportMap = emailStatsReport.join();
            Map<Long, UserAdoptionReportCrmNoteStatsDTO> crmNoteReportMap = crmNoteStatsReport.join();
            List<UserBriefDTO> usersList = userService.getAllBriefUserByIds(belowAverageUserIds).getBody();
            Map<Long, UserBriefDTO> userMap = usersList.stream().filter(UserBriefDTO::isActivated).collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));
            List<UserAdoptionReportVO> res = new ArrayList<>();
            for (Long userId : belowAverageUserIds) {
                UserAdoptionReportVO userAdoptionReportVO = new UserAdoptionReportVO();
                userAdoptionReportVO.setUserId(userId);
                UserBriefDTO u = userMap.get(userId);
                if (Objects.isNull(u)) {
                    continue;
                }
                userAdoptionReportVO.setUserName(CommonUtils.formatFullName(u.getFirstName(), u.getLastName()));
                userAdoptionReportVO.setActivationDuration(userDurationTimeMap.getOrDefault(userId, 0L) / SECONDS_TO_MINUTES);

                int[] callReportDetails = callReportMap.getOrDefault(userId, new int[]{0, 0});
                userAdoptionReportVO.setCallCount(callReportDetails[0]);
                userAdoptionReportVO.setUniqueCalledTalentCount(callReportDetails[1]);

                //email
                UserAdoptionReportEmailStatsDTO emailStatsDTO = emailStatsReportMap.getOrDefault(userId, new UserAdoptionReportEmailStatsDTO());
                userAdoptionReportVO.setEmailCount(emailStatsDTO.getEmailCount());
                userAdoptionReportVO.setUniqueEmailedTalentCount(emailStatsDTO.getUniqueEmailCount());

                //application
                UserAdoptionReportApplicationStatsDTO applicationStatsDetails = applicationStatsReportMap.getOrDefault(userId, new UserAdoptionReportApplicationStatsDTO());
                userAdoptionReportVO.setSubmitToJobCount(ObjectUtils.firstNonNull(applicationStatsDetails.getSubmitToJobCount(), 0L));
                userAdoptionReportVO.setInterviewCount(ObjectUtils.firstNonNull(applicationStatsDetails.getInterviewCount(), 0L));
                userAdoptionReportVO.setUniqueInterviewedTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getUniqueInterviewedTalentCount(), 0L));
                userAdoptionReportVO.setOnboardTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getOnboardTalentCount(), 0L));

                UserAdoptionReportCrmNoteStatsDTO crmNoteStatsDTO = crmNoteReportMap.getOrDefault(userId, new UserAdoptionReportCrmNoteStatsDTO());
                userAdoptionReportVO.setNoteCount(ObjectUtils.firstNonNull(crmNoteStatsDTO.getNoteCount(), 0L) + ObjectUtils.firstNonNull(applicationStatsDetails.getApnTalentNoteCount(), 0L));
                Set<Long> apnTalentIds = Optional.ofNullable(applicationStatsDetails.getUniqueTalentIds())
                        .filter(StringUtils::isNotBlank)
                        .map(s -> Arrays.stream(s.split(","))
                                .map(String::trim)
                                .filter(StringUtils::isNotBlank)
                                .map(Long::valueOf)
                                .collect(Collectors.toSet()))
                        .orElse(Collections.emptySet());
                Set<Long> uniqueTalentIdSet = SetUtils.union(apnTalentIds, crmNoteStatsDTO.getTalentIds());

                userAdoptionReportVO.setUniqueNotedTalentCount(uniqueTalentIdSet.size() + crmNoteStatsDTO.getUniqueContactIdCount());

                res.add(userAdoptionReportVO);
            }

            if(sort != null) res.sort(getUserAdoptionReportComparatorFromSort(sort));
            r.setUserAdoptionReport(res);
            return r;

        } else {
            Set<Long> ids = userAdoptionReportDetailsService.getReallyUserIds(userAdoptionReportDTO);
            if(CollectionUtils.isEmpty(ids)){
                return new UserAdoptionReportWithThresholdVO();
            }

            ConcurrentStopWatch stopWatch = new ConcurrentStopWatch("getUserAdoptionReport");
            stopWatch.start("total task");
            SecurityContext context = SecurityContextHolder.getContext();


            CompletableFuture<Map<Long, Long>> userActiveDurationReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("userActiveDurationReport");
                //TODO 先注释
                Map<Long, Long> res = userActiveDurationService.getUsersByOnlineDuration(SecurityUtils.getTenantId(), userAdoptionReportDTO.getStartTime().toString(), userAdoptionReportDTO.getEndTime().toString(), ids, null);
                //Map<Long, Long> res = new HashMap<>();
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getUsersByOnlineDurationReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, int[]>> callReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("findOnboardedNotInvoiced");
                Map<Long, int[]> res = userAdoptionReportDetailsService.getCallReport(userAdoptionReportDTO, ids);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getCallReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, UserAdoptionReportApplicationStatsDTO>> applicationStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("getApplicationStatsReport");
                Map<Long, UserAdoptionReportApplicationStatsDTO> res = userAdoptionReportDetailsService.getApplicationStatsReport(userAdoptionReportDTO, ids);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getApplicationStatsReport time = {}ms ", stopWatch.getTotalTimeMillis());
                return res;
            });

            CompletableFuture<Map<Long, UserAdoptionReportEmailStatsDTO>> emailStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("searchSentEmailsByUsers");
                MailSearchDTO mailSearchDTO = new MailSearchDTO();
                mailSearchDTO.setFromDate(userAdoptionReportDTO.getStartTime());
                mailSearchDTO.setToDate(userAdoptionReportDTO.getEndTime());
                mailSearchDTO.setTenantId(SecurityUtils.getTenantId());
                mailSearchDTO.setUserIds(new ArrayList<>(ids));
                mailSearchDTO.setCustomerId2(List.of("AC", "APN:TALENT", "VOIPNOTICE:TALENT"));
                List<EmailLogOverviewDTO> res = emailService.searchSentEmailsByUsers(mailSearchDTO).getBody();
                stopWatch.stop();
                log.info("[getUserAdoptionReport] searchSentEmailsByUsers time = {}ms ", stopWatch.getTotalTimeMillis());

                if (CollectionUtils.isEmpty(res)) {
                    return Collections.emptyMap();
                }

                Set<Long> contactIds = res.parallelStream()
                        .peek(this::extractTalentIdAndContactId)
                        .flatMap(e -> e.getContactIds().stream())
                        .collect(Collectors.toSet());

                List<TalentClientContactRelationDTO> relation = CollectionUtils.isNotEmpty(contactIds) ? companyService.getTalentIdsByContactIds(contactIds).getBody() : Collections.emptyList();

                Map<Long, Long> contactToTalentMap = !CollectionUtils.isEmpty(relation) ? relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId)) : new HashMap<>();

                return res.parallelStream()
                        // 使用 groupingByConcurrent 进行并发分组，且自定义 Collector 归并每个分组的 EmailLogOverviewDTO
                        .collect(Collectors.groupingByConcurrent(
                                EmailLogOverviewDTO::getUserId,
                                Collector.of(
                                        // 创建累加器：初始化一个新的 UserAdoptionReportEmailStatsDTO
                                        UserAdoptionReportEmailStatsDTO::new,
                                        // 累加器：将每个 EmailLogOverviewDTO 数据合并到累加器中
                                        (stats, dto) -> {
                                            stats.setId(dto.getUserId());
                                            int talentSize = CollectionUtils.size(dto.getTalentIds());
                                            int contactSize =  CollectionUtils.size(dto.getContactIds());
                                            long current = stats.getEmailCount();
                                            stats.setEmailCount(current + talentSize + contactSize);
                                            if (dto.getTalentIds() != null) {
                                                stats.getTalentIds().addAll(dto.getTalentIds());
                                            }
                                            if (dto.getContactIds() != null) {
                                                stats.getContactIds().addAll(dto.getContactIds());
                                            }
                                        },
                                        // 合并器：合并两个累加器
                                        (stats1, stats2) -> {
                                            long count1 = stats1.getEmailCount();
                                            long count2 = stats2.getEmailCount();
                                            stats1.setEmailCount(count1 + count2);
                                            stats1.getTalentIds().addAll(stats2.getTalentIds());
                                            stats1.getContactIds().addAll(stats2.getContactIds());
                                            return stats1;
                                        },
                                        // 在 finisher 中一次性并行处理 contactIds
                                        stats -> {
                                            // 使用并发集合分别收集映射的 talentId 与未映射的 contactId
                                            Set<Long> concurrentMappedTalents = ConcurrentHashMap.newKeySet();
                                            Set<Long> concurrentUnmappedContacts = ConcurrentHashMap.newKeySet();

                                            stats.getContactIds().parallelStream().forEach(contactId -> {
                                                Long mappedTalent = contactToTalentMap.get(contactId);
                                                if (mappedTalent != null) {
                                                    concurrentMappedTalents.add(mappedTalent);
                                                } else {
                                                    concurrentUnmappedContacts.add(contactId);
                                                }
                                            });

                                            // 合并原有的 talentIds 与映射到的 talentIds（自动去重）
                                            Set<Long> mergedTalentIds = new HashSet<>(stats.getTalentIds());
                                            mergedTalentIds.addAll(concurrentMappedTalents);

                                            // uniqueEmailCount = 合并后 talentIds 的数量 + 未映射 contactIds 的数量
                                            stats.setUniqueEmailCount((long) mergedTalentIds.size() + concurrentUnmappedContacts.size());
                                            return stats;
                                        }
                                )
                        ));
            });

            CompletableFuture<Map<Long, UserAdoptionReportCrmNoteStatsDTO>> crmNoteStatsReport = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                stopWatch.start("getCrmNoteReport");
                List<UserAdoptionReportCrmNoteStatsDTO> res = userAdoptionReportDetailsService.getCrmNoteReport(userAdoptionReportDTO, ids);
                stopWatch.stop();
                log.info("[getUserAdoptionReport] getCrmNoteReport time = {}ms ", stopWatch.getTotalTimeMillis());

                Set<Long> contactIds = res.parallelStream()
                        .flatMap(a -> a.getContactIds().stream())
                        .collect(Collectors.toSet());

                List<TalentClientContactRelationDTO> relation = companyService.getTalentIdsByContactIds(contactIds).getBody();
                if (Objects.nonNull(relation)) {
                    Map<Long, Long> contactToTalentMap = relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId));
                    res.parallelStream().forEach(item -> {
                        Set<Long> talentIds = item.getContactIds().stream()
                                .map(contactToTalentMap::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toSet());

                        item.setTalentIds(talentIds);
                        item.setUniqueContactIdCount(item.getContactIds().size() - talentIds.size());
                    });
                }

                return res.stream().collect(Collectors.toMap(UserAdoptionReportCrmNoteStatsDTO::getId, Function.identity()));
            });

            CompletableFuture.allOf(userActiveDurationReport, callReport, applicationStatsReport, emailStatsReport, crmNoteStatsReport)
                    .exceptionally(FutureExceptionUtil::handleFutureException);

            Map<Long, Long> userActiveDuractionReportMap = userActiveDurationReport.join();
            Map<Long, int[]> callReportMap = callReport.join();
            Map<Long, UserAdoptionReportApplicationStatsDTO> applicationStatsReportMap = applicationStatsReport.join();
            Map<Long, UserAdoptionReportEmailStatsDTO> emailStatsReportMap = emailStatsReport.join();
            Map<Long, UserAdoptionReportCrmNoteStatsDTO> crmNoteReportMap = crmNoteStatsReport.join();

            List<UserBriefDTO> usersList = userService.getAllBriefUserByIds(ids).getBody();
            Map<Long, UserBriefDTO> userMap = usersList.stream().filter(UserBriefDTO::isActivated).collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));
            List<UserAdoptionReportVO> res = new ArrayList<>();
            for (Long userId : ids) {
                UserAdoptionReportVO userAdoptionReportVO = new UserAdoptionReportVO();
                userAdoptionReportVO.setUserId(userId);
                UserBriefDTO u = userMap.get(userId);
                if (Objects.isNull(u)) {
                    continue;
                }
                userAdoptionReportVO.setUserName(CommonUtils.formatFullName(u.getFirstName(), u.getLastName()));
                userAdoptionReportVO.setActivationDuration(userActiveDuractionReportMap.getOrDefault(userId, 0L) / SECONDS_TO_MINUTES);

                int[] callReportDetails = callReportMap.getOrDefault(userId, new int[]{0, 0});
                userAdoptionReportVO.setCallCount(callReportDetails[0]);
                userAdoptionReportVO.setUniqueCalledTalentCount(callReportDetails[1]);

                //email
                UserAdoptionReportEmailStatsDTO emailStatsDTO = emailStatsReportMap.getOrDefault(userId, new UserAdoptionReportEmailStatsDTO());
                userAdoptionReportVO.setEmailCount(emailStatsDTO.getEmailCount());
                userAdoptionReportVO.setUniqueEmailedTalentCount(emailStatsDTO.getUniqueEmailCount());

                //application
                UserAdoptionReportApplicationStatsDTO applicationStatsDetails = applicationStatsReportMap.getOrDefault(userId, new UserAdoptionReportApplicationStatsDTO());
                userAdoptionReportVO.setSubmitToJobCount(ObjectUtils.firstNonNull(applicationStatsDetails.getSubmitToJobCount(), 0L));
                userAdoptionReportVO.setInterviewCount(ObjectUtils.firstNonNull(applicationStatsDetails.getInterviewCount(), 0L));
                userAdoptionReportVO.setUniqueInterviewedTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getUniqueInterviewedTalentCount(), 0L));
                userAdoptionReportVO.setOnboardTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getOnboardTalentCount(), 0L));

                //note
                UserAdoptionReportCrmNoteStatsDTO crmNoteStatsDTO = crmNoteReportMap.getOrDefault(userId, new UserAdoptionReportCrmNoteStatsDTO());
                userAdoptionReportVO.setNoteCount(ObjectUtils.firstNonNull(crmNoteStatsDTO.getNoteCount(), 0L) + ObjectUtils.firstNonNull(applicationStatsDetails.getApnTalentNoteCount(), 0L));
                Set<Long> apnTalentIds = Optional.ofNullable(applicationStatsDetails.getUniqueTalentIds())
                        .filter(StringUtils::isNotBlank)
                        .map(s -> Arrays.stream(s.split(","))
                                .map(String::trim)
                                .filter(StringUtils::isNotBlank)
                                .map(Long::valueOf)
                                .collect(Collectors.toSet()))
                        .orElse(Collections.emptySet());
                Set<Long> uniqueTalentIdSet = SetUtils.union(apnTalentIds, crmNoteStatsDTO.getTalentIds());
                userAdoptionReportVO.setUniqueNotedTalentCount(uniqueTalentIdSet.size() + crmNoteStatsDTO.getUniqueContactIdCount());

                res.add(userAdoptionReportVO);
            }

            if(sort != null) res.sort(getUserAdoptionReportComparatorFromSort(sort));

            UserAdoptionReportWithThresholdVO r = new UserAdoptionReportWithThresholdVO();
            r.setUserActiveThreshold(Long.valueOf(getActiveDurationThreshold()));
            r.setUserAdoptionReport(res);
            return r;
        }
    }


    @Override
    public List<TeamAdoptionReportVO> getUserAdoptionReportV3ForSum(List<TeamAdoptionReportVO> teamAdoptionReportVOS) throws ExecutionException, InterruptedException {
        List<TeamAdoptionReportVO> res = new ArrayList<>();
        TeamAdoptionReportVO teamAdoptionReportVO = new TeamAdoptionReportVO();
        teamAdoptionReportVO.setNumberOfUsers(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getNumberOfUsers).sum());
        teamAdoptionReportVO.setAverageActiveDuration(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getAverageActiveDuration).sum());
        teamAdoptionReportVO.setNoUsageUsersCount(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getNoUsageUsersCount).sum());
        teamAdoptionReportVO.setLowAverageUserCount(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getLowAverageUserCount).sum());
        teamAdoptionReportVO.setCallCount(teamAdoptionReportVOS.stream().mapToInt(i -> ObjectUtils.defaultIfNull(i.getCallCount(), 0)).sum());
        teamAdoptionReportVO.setUniqueCalledTalentCount(teamAdoptionReportVOS.stream().mapToInt(i -> ObjectUtils.defaultIfNull(i.getUniqueCalledTalentCount(), 0)).sum());
        teamAdoptionReportVO.setEmailCount(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getEmailCount).sum());
        teamAdoptionReportVO.setUniqueEmailedTalentCount(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getUniqueEmailedTalentCount).sum());
        teamAdoptionReportVO.setNoteCount(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getNoteCount).sum());
        teamAdoptionReportVO.setUniqueNotedTalentCount(teamAdoptionReportVOS.stream().mapToInt(TeamAdoptionReportVO::getUniqueNotedTalentCount).sum());
        teamAdoptionReportVO.setSubmitToJobCount(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getSubmitToJobCount).sum());
        teamAdoptionReportVO.setInterviewCount(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getInterviewCount).sum());
        teamAdoptionReportVO.setUniqueInterviewedTalentCount(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getUniqueInterviewedTalentCount).sum());
        teamAdoptionReportVO.setOnboardTalentCount(teamAdoptionReportVOS.stream().mapToLong(TeamAdoptionReportVO::getOnboardTalentCount).sum());
        res.add(teamAdoptionReportVO);
        return res;
    }


    @Override
    public Map<Long, UserAdoptionReportEmailStatsDTO> getEmailCountByTeam(UserAdoptionReportDTO userAdoptionReportDTO,List<TeamInfoVO> teamInfoVOS){
        Set<Long> ids = teamInfoVOS.stream().map(TeamInfoVO::getUserId).collect(Collectors.toSet());
        MailSearchDTO mailSearchDTO = new MailSearchDTO();
        mailSearchDTO.setFromDate(userAdoptionReportDTO.getStartTime());
        mailSearchDTO.setToDate(userAdoptionReportDTO.getEndTime());
        mailSearchDTO.setTenantId(SecurityUtils.getTenantId());
        mailSearchDTO.setUserIds(new ArrayList<>(ids));
        mailSearchDTO.setCustomerId2(List.of("AC", "APN:TALENT", "VOIPNOTICE:TALENT"));
        List<EmailLogOverviewDTO> res = emailService.searchSentEmailsByUsers(mailSearchDTO).getBody();
        if (CollectionUtils.isEmpty(res)) {
            return Collections.emptyMap();
        }

        Set<Long> contactIds = res.parallelStream()
                .peek(this::extractTalentIdAndContactId)
                .flatMap(e -> e.getContactIds().stream())
                .collect(Collectors.toSet());

        List<TalentClientContactRelationDTO> relation = CollectionUtils.isNotEmpty(contactIds) ? companyService.getTalentIdsByContactIds(contactIds).getBody() : Collections.emptyList();

        Map<Long, Long> contactToTalentMap = !CollectionUtils.isEmpty(relation) ? relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId)) : new HashMap<>();
        res.forEach(item -> {
            for(TeamInfoVO teamInfoVO : teamInfoVOS){
                if (teamInfoVO.getUserId().equals(item.getUserId())){
                    item.setTeamId(teamInfoVO.getTeamId());
                }
            }
        });
        return res.parallelStream()
                // 使用 groupingByConcurrent 进行并发分组，且自定义 Collector 归并每个分组的 EmailLogOverviewDTO
                .collect(Collectors.groupingByConcurrent(
                        EmailLogOverviewDTO::getTeamId,
                        Collector.of(
                                // 创建累加器：初始化一个新的 UserAdoptionReportEmailStatsDTO
                                UserAdoptionReportEmailStatsDTO::new,
                                // 累加器：将每个 EmailLogOverviewDTO 数据合并到累加器中
                                (stats, dto) -> {
                                    stats.setId(dto.getTeamId());
                                    int talentSize = CollectionUtils.size(dto.getTalentIds());
                                    int contactSize =  CollectionUtils.size(dto.getContactIds());
                                    long current = stats.getEmailCount();
                                    stats.setEmailCount(current + talentSize + contactSize);
                                    if (dto.getTalentIds() != null) {
                                        stats.getTalentIds().addAll(dto.getTalentIds());
                                    }
                                    if (dto.getContactIds() != null) {
                                        stats.getContactIds().addAll(dto.getContactIds());
                                    }
                                },
                                // 合并器：合并两个累加器
                                (stats1, stats2) -> {
                                    long count1 = stats1.getEmailCount();
                                    long count2 = stats2.getEmailCount();
                                    stats1.setEmailCount(count1 + count2);
                                    stats1.getTalentIds().addAll(stats2.getTalentIds());
                                    stats1.getContactIds().addAll(stats2.getContactIds());
                                    return stats1;
                                },
                                // 在 finisher 中一次性并行处理 contactIds
                                stats -> {
                                    // 使用并发集合分别收集映射的 talentId 与未映射的 contactId
                                    Set<Long> concurrentMappedTalents = ConcurrentHashMap.newKeySet();
                                    Set<Long> concurrentUnmappedContacts = ConcurrentHashMap.newKeySet();

                                    stats.getContactIds().parallelStream().forEach(contactId -> {
                                        Long mappedTalent = contactToTalentMap.get(contactId);
                                        if (mappedTalent != null) {
                                            concurrentMappedTalents.add(mappedTalent);
                                        } else {
                                            concurrentUnmappedContacts.add(contactId);
                                        }
                                    });

                                    // 合并原有的 talentIds 与映射到的 talentIds（自动去重）
                                    Set<Long> mergedTalentIds = new HashSet<>(stats.getTalentIds());
                                    mergedTalentIds.addAll(concurrentMappedTalents);

                                    // uniqueEmailCount = 合并后 talentIds 的数量 + 未映射 contactIds 的数量
                                    stats.setUniqueEmailCount((long) mergedTalentIds.size() + concurrentUnmappedContacts.size());
                                    return stats;
                                }
                        )
                ));
    }

    @Override
    public UserAdoptionReportEmailStatsDTO getEmailCountByForTotal(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> ids) {
        UserAdoptionReportEmailStatsDTO result = new UserAdoptionReportEmailStatsDTO();
        MailSearchDTO mailSearchDTO = new MailSearchDTO();
        mailSearchDTO.setFromDate(userAdoptionReportDTO.getStartTime());
        mailSearchDTO.setToDate(userAdoptionReportDTO.getEndTime());
        mailSearchDTO.setTenantId(SecurityUtils.getTenantId());
        mailSearchDTO.setUserIds(new ArrayList<>(ids));
        mailSearchDTO.setCustomerId2(List.of("AC", "APN:TALENT", "VOIPNOTICE:TALENT"));
        List<EmailLogOverviewDTO> res = emailService.searchSentEmailsByUsers(mailSearchDTO).getBody();
        if (CollectionUtils.isEmpty(res)) {
            return result;
        }

        Set<Long> contactIds = res.parallelStream()
                .peek(this::extractTalentIdAndContactId)
                .flatMap(e -> e.getContactIds().stream())
                .collect(Collectors.toSet());

        List<TalentClientContactRelationDTO> relation = CollectionUtils.isNotEmpty(contactIds) ? companyService.getTalentIdsByContactIds(contactIds).getBody() : Collections.emptyList();

        Map<Long, Long> contactToTalentMap = !CollectionUtils.isEmpty(relation) ? relation.stream().collect(Collectors.toMap(TalentClientContactRelationDTO::getContactId, TalentClientContactRelationDTO::getTalentId)) : new HashMap<>();
        result = res.stream()
                .collect(
                        Collector.of(
                                // 创建累加器：初始化一个新的 UserAdoptionReportEmailStatsDTO
                                UserAdoptionReportEmailStatsDTO::new,
                                // 累加器：将每个 EmailLogOverviewDTO 数据合并到累加器中
                                (stats, dto) -> {
                                    stats.setId(dto.getTeamId());
                                    int talentSize = CollectionUtils.size(dto.getTalentIds());
                                    int contactSize = CollectionUtils.size(dto.getContactIds());
                                    long current = stats.getEmailCount();
                                    stats.setEmailCount(current + talentSize + contactSize);
                                    if (dto.getTalentIds() != null) {
                                        stats.getTalentIds().addAll(dto.getTalentIds());
                                    }
                                    if (dto.getContactIds() != null) {
                                        stats.getContactIds().addAll(dto.getContactIds());
                                    }
                                },
                                // 合并器：合并两个累加器
                                (stats1, stats2) -> {
                                    long count1 = stats1.getEmailCount();
                                    long count2 = stats2.getEmailCount();
                                    stats1.setEmailCount(count1 + count2);
                                    stats1.getTalentIds().addAll(stats2.getTalentIds());
                                    stats1.getContactIds().addAll(stats2.getContactIds());
                                    return stats1;
                                },
                                // 在 finisher 中一次性并行处理 contactIds
                                stats -> {
                                    // 使用并发集合分别收集映射的 talentId 与未映射的 contactId
                                    Set<Long> concurrentMappedTalents = ConcurrentHashMap.newKeySet();
                                    Set<Long> concurrentUnmappedContacts = ConcurrentHashMap.newKeySet();

                                    stats.getContactIds().parallelStream().forEach(contactId -> {
                                        Long mappedTalent = contactToTalentMap.get(contactId);
                                        if (mappedTalent != null) {
                                            concurrentMappedTalents.add(mappedTalent);
                                        } else {
                                            concurrentUnmappedContacts.add(contactId);
                                        }
                                    });

                                    // 合并原有的 talentIds 与映射到的 talentIds（自动去重）
                                    Set<Long> mergedTalentIds = new HashSet<>(stats.getTalentIds());
                                    mergedTalentIds.addAll(concurrentMappedTalents);

                                    // uniqueEmailCount = 合并后 talentIds 的数量 + 未映射 contactIds 的数量
                                    stats.setUniqueEmailCount((long) mergedTalentIds.size() + concurrentUnmappedContacts.size());
                                    return stats;
                                }
                        )
                );
        return result;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class UserActiveDurationVO {
        private Long userId;
        private Long duration;
        private Long teamId;
    }


    @Override
    public List<TeamAdoptionReportVO> getUserAdoptionReportByTeamView(Pageable pageable,UserAdoptionReportDTO userAdoptionReportDTO) throws ExecutionException, InterruptedException{
        Sort sort = pageable.getSort();
        Integer activeDurationThreshold = getActiveDurationThresholdV2(userAdoptionReportDTO.getStartTime(), userAdoptionReportDTO.getEndTime());
        Set<Long> ids = userAdoptionReportDetailsService.getReallyUserIds(userAdoptionReportDTO);
        if(CollectionUtils.isEmpty(ids)){
            return null;
        }
        //根据useids查询主团队的id和name的list
        List<TeamInfoVO> teamInfoVOS = userService.getTeamInfoVOsByUserIds(List.copyOf(ids)).getBody();
        if (teamInfoVOS == null || teamInfoVOS.isEmpty()) {
            return null;
        }
        Map<Long, List<TeamInfoVO>> mapByTeamId = teamInfoVOS.stream().collect(Collectors.groupingBy(TeamInfoVO::getTeamId));
        List<Long> hashTeamUserIds = teamInfoVOS.stream().map(TeamInfoVO::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hashTeamUserIds)){
            return null;
        }
        SecurityContext context = SecurityContextHolder.getContext();
        //异步计算活跃时长
        CompletableFuture<Map<Long, Long>> userActiveDurationReportFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            //TODO 先注释
            Map<Long, Long> res = userActiveDurationService.getUsersByOnlineDuration(SecurityUtils.getTenantId(), userAdoptionReportDTO.getStartTime().toString(), userAdoptionReportDTO.getEndTime().toString(), hashTeamUserIds, null);
//            Map<Long, Long> res = new HashMap<>();
//            res.put(1762L, 1900L);
//            res.put(1154L,390L);
//            res.put(12277L,40L);
//            res.put(12440L,160L);
//            res.put(12527L,0L);
            return res;
        });
        //异步计算call
        CompletableFuture<List<TeamAdoptionReportVO>> callReportForTeamFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            List<TeamAdoptionReportVO> resultForCall = userAdoptionReportDetailsService.getCallReportByTeamView(userAdoptionReportDTO, teamInfoVOS);
            return resultForCall;
        });
        //异步计算crm的note
        CompletableFuture<Map<Long, List<UserAdoptionReportCrmNoteStatsDTO>>> mapCompletableFutureForCrmNote =CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return userAdoptionReportDetailsService.getCrmNoteReportByTeamView(userAdoptionReportDTO, teamInfoVOS);
        });
        //异步计算email
        CompletableFuture<Map<Long, UserAdoptionReportEmailStatsDTO>> emailStatsReportFuture = CompletableFuture.supplyAsync(() -> {
             SecurityContextHolder.setContext(context);
             return getEmailCountByTeam(userAdoptionReportDTO, teamInfoVOS);
        });
        //异步计算流程相关
        CompletableFuture<Map<Long, UserAdoptionReportApplicationStatsDTO>> applicationStatsReportFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            Map<Long, UserAdoptionReportApplicationStatsDTO> res = userAdoptionReportDetailsService.getApplicationStatsReportByTeamView(userAdoptionReportDTO);
            return res;
        });
        //等待所有异步执行完成
        CompletableFuture.allOf(callReportForTeamFuture, mapCompletableFutureForCrmNote,applicationStatsReportFuture,emailStatsReportFuture,userActiveDurationReportFuture).exceptionally(FutureExceptionUtil::handleFutureException);
        Map<Long, UserAdoptionReportApplicationStatsDTO> applicationStatsReportMap = applicationStatsReportFuture.join();
        List<TeamAdoptionReportVO> callResult = callReportForTeamFuture.join();
        Map<Long, List<UserAdoptionReportCrmNoteStatsDTO>> crmNoteResult = mapCompletableFutureForCrmNote.join();
        Map<Long, UserAdoptionReportEmailStatsDTO> emailStatsReportMap = emailStatsReportFuture.join();
        Map<Long, Long> userActiveDurationMap = userActiveDurationReportFuture.join();
        //活跃时长
        Map<Long, List<UserActiveDurationVO>> userActiveDurationMapByTeam = userActiveDurationMap.entrySet().stream().map(entry -> {
            UserActiveDurationVO userActiveDurationVO = new UserActiveDurationVO();
            Long userId = entry.getKey();
            Long duration = entry.getValue();
            TeamInfoVO teamInfoVO1 = teamInfoVOS.stream().filter(teamInfoVO -> Objects.equals(teamInfoVO.getUserId(), userId)).findFirst().get();
            Long teamId = teamInfoVO1.getTeamId();
            userActiveDurationVO.setUserId(userId);
            userActiveDurationVO.setDuration(duration);
            userActiveDurationVO.setTeamId(teamId);
            return userActiveDurationVO;
        }).collect(Collectors.groupingBy(UserActiveDurationVO::getTeamId));

        List<TeamAdoptionReportVO> teamAdoptionReportVOS = mapByTeamId.entrySet().stream().map(entry -> {

            TeamAdoptionReportVO teamAdoptionReportVO = new TeamAdoptionReportVO();
            Long teamId = entry.getKey();
            teamAdoptionReportVO.setTeamId(teamId);
            //application
            UserAdoptionReportApplicationStatsDTO applicationStatsDetails = applicationStatsReportMap.getOrDefault(teamId, new UserAdoptionReportApplicationStatsDTO());
            teamAdoptionReportVO.setTeamName(entry.getValue().get(0).getTeamName());
            teamAdoptionReportVO.setNumberOfUsers((long) entry.getValue().size());
            //平均活跃时长
            List<UserActiveDurationVO> userActiveDurationVOS = userActiveDurationMapByTeam.get(teamId);
            if(CollectionUtils.isEmpty(userActiveDurationVOS)){
                teamAdoptionReportVO.setAverageActiveDuration(0L);
                teamAdoptionReportVO.setNoUsageUsersCount(0L);
                teamAdoptionReportVO.setLowAverageUserCount(0L);
                teamAdoptionReportVO.setActiveDurationThreshold(activeDurationThreshold);
            }else{
                double averageActiveDurationDouble1 = userActiveDurationVOS.stream().mapToLong(i -> i.getDuration() / SECONDS_TO_MINUTES).average().orElse(0);
                long count1 = userActiveDurationVOS.stream().filter(u -> u.getDuration() == 0).count();
                long count2 = userActiveDurationVOS.stream().filter(u -> u.getDuration() / SECONDS_TO_MINUTES < activeDurationThreshold).count();
                teamAdoptionReportVO.setAverageActiveDuration((long)averageActiveDurationDouble1);
                teamAdoptionReportVO.setNoUsageUsersCount(count1);
                teamAdoptionReportVO.setLowAverageUserCount(count2);
                teamAdoptionReportVO.setActiveDurationThreshold(activeDurationThreshold);
            }
            //call
            for (TeamAdoptionReportVO adoptionReportVO : callResult) {
                if (adoptionReportVO.getTeamId().equals(teamId)) {
                    teamAdoptionReportVO.setCallCount(adoptionReportVO.getCallCount());
                    teamAdoptionReportVO.setUniqueCalledTalentCount(adoptionReportVO.getUniqueCalledTalentCount());
                }
            }
            //note
            List<UserAdoptionReportCrmNoteStatsDTO> userAdoptionReportCrmNoteStatsDTOS = crmNoteResult.get(teamId);
            Long crmNoteCount = 0L;
            Integer crmContactIdCountForTeam = 0;
            Set<Long> crmTalentIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(userAdoptionReportCrmNoteStatsDTOS)){
               crmNoteCount = userAdoptionReportCrmNoteStatsDTOS.stream().mapToLong(UserAdoptionReportCrmNoteStatsDTO::getNoteCount).sum();
               crmContactIdCountForTeam = userAdoptionReportCrmNoteStatsDTOS.stream().mapToInt(UserAdoptionReportCrmNoteStatsDTO::getUniqueContactIdCount).sum();
               crmTalentIds = userAdoptionReportCrmNoteStatsDTOS.stream().flatMap(item -> item.getTalentIds().stream()).collect(Collectors.toSet());
            }
            teamAdoptionReportVO.setNoteCount(crmNoteCount + ObjectUtils.firstNonNull(applicationStatsDetails.getApnTalentNoteCount(), 0L));

            Set<Long> apnTalentIds = Optional.ofNullable(applicationStatsDetails.getUniqueTalentIds())
                    .filter(StringUtils::isNotBlank)
                    .map(s -> Arrays.stream(s.split(","))
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .map(Long::valueOf)
                            .collect(Collectors.toSet()))
                    .orElse(Collections.emptySet());
            Set<Long> uniqueTalentIdSet = SetUtils.union(apnTalentIds, crmTalentIds);
            teamAdoptionReportVO.setUniqueNotedTalentCount(uniqueTalentIdSet.size() + crmContactIdCountForTeam);

            UserAdoptionReportEmailStatsDTO userAdoptionReportEmailStatsDTO = ObjectUtils.firstNonNull(emailStatsReportMap.get(teamId), new UserAdoptionReportEmailStatsDTO());
            teamAdoptionReportVO.setEmailCount(ObjectUtils.firstNonNull(userAdoptionReportEmailStatsDTO.getEmailCount(), 0L));
            teamAdoptionReportVO.setUniqueEmailedTalentCount(ObjectUtils.firstNonNull(userAdoptionReportEmailStatsDTO.getUniqueEmailCount(), 0L));

            //流程
            teamAdoptionReportVO.setSubmitToJobCount(ObjectUtils.firstNonNull(applicationStatsDetails.getSubmitToJobCount(), 0L));
            teamAdoptionReportVO.setInterviewCount(ObjectUtils.firstNonNull(applicationStatsDetails.getInterviewCount(), 0L));
            teamAdoptionReportVO.setUniqueInterviewedTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getUniqueInterviewedTalentCount(), 0L));
            teamAdoptionReportVO.setOnboardTalentCount(ObjectUtils.firstNonNull(applicationStatsDetails.getOnboardTalentCount(), 0L));
            return teamAdoptionReportVO;

        }).collect(Collectors.toList());
        //排序
        if(sort != null) {
            teamAdoptionReportVOS.sort(getTeamAdoptionReportComparatorFromSort(sort));
        }
        return teamAdoptionReportVOS;
    }

    public ResponseEntity<?> getResponseForE5(UserAdoptionReportDTO userAdoptionReportDTO, Pageable pageable) throws ExecutionException, InterruptedException {
        if (userAdoptionReportDTO.getViewType() == null || userAdoptionReportDTO.getViewType() == E5ReportViewType.USER){
            UserAdoptionReportWithThresholdVO res = getUserAdoptionReport(userAdoptionReportDTO, pageable);
            return new ResponseEntity<>(res.getUserAdoptionReport(), null, HttpStatus.OK);
        } else if (userAdoptionReportDTO.getViewType() == E5ReportViewType.TEAM) {
            List<TeamAdoptionReportVO> result = getUserAdoptionReportByTeamView(pageable, userAdoptionReportDTO);
            return new ResponseEntity<>(result, null, HttpStatus.OK);
        }
        return new ResponseEntity<>(null, HttpStatus.OK);
    }


    private TeamAndUserAdoptionReportVO getZeroTeamAndUserAdoptionReportVO() {
        TeamAndUserAdoptionReportVO vo = new TeamAndUserAdoptionReportVO();
        vo.setActivationDuration(0L);
        vo.setNumberOfUsers(0L);
        vo.setAverageActiveDuration(0L);
        vo.setNoUsageUsersCount(0L);
        vo.setLowAverageUserCount(0L);
        vo.setCallCount(0);
        vo.setUniqueCalledTalentCount(0);
        vo.setNoteCount(0L);
        vo.setUniqueNotedTalentCount(0);
        vo.setEmailCount(0L);
        vo.setUniqueEmailedTalentCount(0L);
        vo.setSubmitToJobCount(0L);
        vo.setInterviewCount(0L);
        vo.setUniqueInterviewedTalentCount(0L);
        vo.setOnboardTalentCount(0L);
        return vo;
    }

    @Override
    public TeamAndUserAdoptionReportVO  getResponseForE5ForSum(UserAdoptionReportDTO userAdoptionReportDTO) throws ExecutionException, InterruptedException {
        TeamAndUserAdoptionReportVO result = getZeroTeamAndUserAdoptionReportVO();
        Integer activeDurationThreshold = getActiveDurationThresholdV2(userAdoptionReportDTO.getStartTime(), userAdoptionReportDTO.getEndTime());
        result.setActiveDurationThreshold(activeDurationThreshold);

        Set<Long> ids = userAdoptionReportDetailsService.getReallyUserIds(userAdoptionReportDTO);
        if (CollectionUtils.isEmpty(ids)){
            return result;
        }
        //统计数字
        //异步计算活跃时长
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<Map<Long, Long>> userActiveDurationReport = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            //TODO 先注释
            Map<Long, Long> res = userActiveDurationService.getUsersByOnlineDuration(SecurityUtils.getTenantId(), userAdoptionReportDTO.getStartTime().toString(), userAdoptionReportDTO.getEndTime().toString(), ids, null);
//            Map<Long, Long> res = new HashMap<>();
            return res;
        });
        //异步计算call
        CompletableFuture<Map<String, Integer>> callReportForTotalFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            Map<String, Integer> callReportForTotal = userAdoptionReportDetailsService.getCallReportForTotal(userAdoptionReportDTO, ids);
            return callReportForTotal;
        });
        //异步计算crm的note
        CompletableFuture<UserAdoptionReportCrmNoteStatsDTO> mapCompletableFutureForCrmNote =CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return userAdoptionReportDetailsService.getCrmNoteReportForTotal(userAdoptionReportDTO, ids);
        });
        //异步计算email
        CompletableFuture<UserAdoptionReportEmailStatsDTO> emailStatsReportFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return getEmailCountByForTotal(userAdoptionReportDTO, ids);
        });
        //异步计算流程相关
        CompletableFuture<List<RecruitingKpiByUserVO>> applicationStatsReportFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            List<RecruitingKpiByUserVO> applicationStatsReportForTotal = userAdoptionReportDetailsService.getApplicationStatsReportForTotal(userAdoptionReportDTO,ids);
            return applicationStatsReportForTotal;
        });

        //等待所有异步执行完成
        CompletableFuture.allOf(userActiveDurationReport,callReportForTotalFuture, mapCompletableFutureForCrmNote,applicationStatsReportFuture,emailStatsReportFuture).exceptionally(FutureExceptionUtil::handleFutureException);
        List<RecruitingKpiByUserVO> applicationStatsResult = applicationStatsReportFuture.join();
        Map<String, Integer> callReportResult = callReportForTotalFuture.join();
        UserAdoptionReportCrmNoteStatsDTO crmNoteResult = mapCompletableFutureForCrmNote.join();
        Map<Long, Long> userActiveDurationMap = userActiveDurationReport.join();
        UserAdoptionReportEmailStatsDTO emailStatsResult = emailStatsReportFuture.join();

        int numberOfUsers = ids.size();
        result.setNumberOfUsers(Long.valueOf(numberOfUsers));
        if(userActiveDurationMap != null){
            AtomicReference<Long> userActiveDurationSum = new AtomicReference<>(0L);
            AtomicReference<Long> noUsageUsersCount = new AtomicReference<>( 0L);
            AtomicReference<Long> lowAverageUserCount = new AtomicReference<>( 0L);

            userActiveDurationMap.entrySet().forEach(entry -> {
                userActiveDurationSum.set(userActiveDurationSum.get() + entry.getValue() / SECONDS_TO_MINUTES);
                if(entry.getValue() == 0){
                    noUsageUsersCount.getAndSet(noUsageUsersCount.get() + 1);
                }
                if(entry.getValue() / SECONDS_TO_MINUTES < activeDurationThreshold){
                    lowAverageUserCount.getAndSet(lowAverageUserCount.get() + 1);
                }
            });
            if (E5ReportViewType.USER == userAdoptionReportDTO.getViewType()){
                result.setActivationDuration(userActiveDurationSum.get());
            }else {
                result.setAverageActiveDuration(userActiveDurationSum.get() / numberOfUsers);
                result.setNoUsageUsersCount(noUsageUsersCount.get());
                result.setLowAverageUserCount(lowAverageUserCount.get());
            }
        }
        RecruitingKpiByUserVO recruitingKpiByUserVO = new RecruitingKpiByUserVO();
        if (CollectionUtils.isEmpty(applicationStatsResult)){
        }else {
            recruitingKpiByUserVO = applicationStatsResult.get(0);
        }
        result.setSubmitToJobCount(ObjectUtils.firstNonNull(recruitingKpiByUserVO.getSubmitToJobNum(), 0L));
        result.setInterviewCount(ObjectUtils.firstNonNull(recruitingKpiByUserVO.getInterviewNum(), 0L));
        result.setUniqueInterviewedTalentCount(ObjectUtils.firstNonNull(recruitingKpiByUserVO.getUniqueInterviewTalentNum(), 0L));
        result.setOnboardTalentCount(ObjectUtils.firstNonNull(recruitingKpiByUserVO.getOnboardNum(), 0L));

        //note
        Long crmNoteCount = 0L;
        Integer crmContactIdCountForTotal = 0;
        Set<Long> crmTalentIds = new HashSet<>();
        if (crmNoteResult != null){
            crmNoteCount = crmNoteResult.getNoteCount();
            crmContactIdCountForTotal = crmNoteResult.getUniqueContactIdCount();
            crmTalentIds = crmNoteResult.getTalentIds();
        }
        result.setNoteCount(crmNoteCount + ObjectUtils.firstNonNull(recruitingKpiByUserVO.getNoteCount(), 0L));

        Set<Long> apnTalentIds = Optional.ofNullable(recruitingKpiByUserVO.getUniqueTalentIds())
                .filter(StringUtils::isNotBlank)
                .map(s -> Arrays.stream(s.split(","))
                        .map(String::trim)
                        .filter(StringUtils::isNotBlank)
                        .map(Long::valueOf)
                        .collect(Collectors.toSet()))
                .orElse(Collections.emptySet());
        Set<Long> uniqueTalentIdSet = SetUtils.union(apnTalentIds, crmTalentIds);
        result.setUniqueNotedTalentCount(uniqueTalentIdSet.size() + crmContactIdCountForTotal);
        //email
        result.setEmailCount(ObjectUtils.firstNonNull(emailStatsResult.getEmailCount(), 0L));
        result.setUniqueEmailedTalentCount(ObjectUtils.firstNonNull(emailStatsResult.getUniqueEmailCount(), 0L));
        //call
        result.setCallCount(ObjectUtils.firstNonNull(callReportResult.get("callCount"), 0));
        result.setUniqueCalledTalentCount(ObjectUtils.firstNonNull(callReportResult.get("uniqueCalledTalentCount"), 0));
        return result;
    }



    private void extractTalentIdAndContactId(EmailLogOverviewDTO emailLog) {
        String[] parts = StringUtils.split(emailLog.getCustomerId2(), ",");
        List<Long> talentIds = new ArrayList<>(parts.length);
        List<Long> contactIds = new ArrayList<>(parts.length);
        for (String part : parts) {
            part = part.trim();
            if (part.startsWith("APN:TALENT")) {
                talentIds.add(Long.parseLong(part.substring("APN:TALENT".length())));
            } else if (part.startsWith("AC")) {
                contactIds.add(Long.parseLong(part.substring("AC".length())));
            } else if (part.startsWith("VOIPNOTICE:TALENT")){
                talentIds.add(Long.parseLong(part.substring("VOIPNOTICE:TALENT".length())));
            }
        }
        emailLog.setTalentIds(talentIds);
        emailLog.setContactIds(contactIds);
    }


    private void collectUserIds(PermissionTeamTreeDTO teamTree, Set<Long> userIds) {
        if (teamTree == null) {
            return;
        }
        if (teamTree.getData() != null) {
            for (PermissionTeamUserDTO user : teamTree.getData()) {
                if (user != null && user.getId() != null) {
                    userIds.add(user.getId());
                }
            }
        }

        // 递归遍历子节点
        if (teamTree.getChildren() != null) {
            for (PermissionTeamTreeDTO child : teamTree.getChildren()) {
                collectUserIds(child, userIds);
            }
        }
    }

    private Set<Long> checkReportDataPermission(Long userId, Set<Long> allUserIds) throws ExecutionException, InterruptedException {
        if (SecurityUtils.isAdmin()) return allUserIds;
        PermissionUserTeamPermissionVM permissions = userService.getDataPermissionsByUserId(userId).getBody();
        Set<Long> availableUserIds = new HashSet<>();
        availableUserIds.add(userId);
        if(permissions != null) {
            PermissionUserTeamPermissionVM.PermissionDetail permissionDetail = permissions.getReportPermission();
            if(permissionDetail != null) {
                if(permissionDetail.getDataScope().equals(TEAM_DATA_SCOPE)) {
                    List<PermissionTeamTreeDTO> permissionTeamTrees = userService.getTeamsWithPermissionAndUserByType(Module.REPORT, true).getBody();
                    if(permissionTeamTrees != null) {
                        for (PermissionTeamTreeDTO teamTree : permissionTeamTrees) {
                            collectUserIds(teamTree, availableUserIds);
                        }
                    }
                }
                if(permissionDetail.getDataScope().equals(ALL_DATA_SCOPE)) {
                    availableUserIds.addAll(allUserIds);
                }
            }
        }
        return availableUserIds;
    }

    @Autowired
    private InitiationService initiationService;

    @Resource
    private ReportRepository reportRepository;

    private Set<Long> checkReportDataPermissionV2(UserAdoptionReportDTO userAdoptionReportDTO, Set<Long> allUserIds) throws ExecutionException, InterruptedException {
        Set<Long> result = new HashSet<>();
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(userAdoptionReportDTO.getSearchTenantId(), userAdoptionReportDTO.getSearchUserId()).getBody();
        if(teamDataPermission.getSelf()){
            result.add(userAdoptionReportDTO.getSearchUserId());
        }else if(CollectionUtils.isNotEmpty(teamDataPermission.getNestedTeamIds())){
            Set<Long> nestedTeamIds = teamDataPermission.getNestedTeamIds();
            Set<Long> validUserIds = reportRepository.getUserTeamPair(allUserIds)
                    .stream().filter(p -> nestedTeamIds.contains(p.getTeamId())).map(UserTeamPariDTO::getUserId).collect(Collectors.toSet());
            result.addAll(validUserIds);
        }else if(teamDataPermission.getAll()){
            result.addAll(allUserIds);
        }
        return result;
    }


    @Override
    public UserAdoptionReportWithThresholdVO getUserAdoptionReportForDashboard(String view, UserAdoptionReportDTO search) throws ExecutionException, InterruptedException {
        UserAdoptionReportDTO userAdoptionReportDTO = new UserAdoptionReportDTO();
        userAdoptionReportDTO.setLastWeekActivityBelowAverage(true);
        Pageable pageable = PageRequest.of(0, 10, Sort.by("activationDuration").ascending());

        if ("PERSONAL".equalsIgnoreCase(view)) {
            userAdoptionReportDTO.setUserIdList(Collections.singletonList(SecurityUtils.getUserId()));
            userAdoptionReportDTO.setIgnoreThreshold(Boolean.TRUE);
            userAdoptionReportDTO.setTimeZone("UTC");
            return this.getUserAdoptionReport(userAdoptionReportDTO, pageable);
        } else if ("MANAGEMENT".equalsIgnoreCase(view)) {
//            Set<Long> withPermissionUserIds = checkReportDataPermission(SecurityUtils.getUserId(), new HashSet<>()); //if withPermissionUserIds is null, it means current user have permission to view all users
//            userAdoptionReportDTO.setUserIdList(new ArrayList<>(withPermissionUserIds));
            userAdoptionReportDTO.setUserIdList(search.getUserIdList());
            userAdoptionReportDTO.setTeamIdList(search.getTeamIdList());
            userAdoptionReportDTO.setTimeZone("UTC");
            return this.getUserAdoptionReport(userAdoptionReportDTO, pageable);
        }
        throw new CustomParameterizedException("Invalid view param!");
    }

    @Override
    public void exportE5UserAdoptionReportToExcel(UserAdoptionReportDTO userAdoptionReportDTO, HttpServletResponse response) throws ExecutionException, InterruptedException {
        UserAdoptionReportWithThresholdVO vo = this.getUserAdoptionReport(userAdoptionReportDTO, null);
        List<UserAdoptionReportVO> userAdoptionReport = vo.getUserAdoptionReport();
//        setP1ExcelGrandTotal(userAdoptionReport);
        ExcelUtil.downloadExcelWithGramdTotal(response, UserAdoptionReportVO.class, userAdoptionReport, "", "ActivitiesReportByUser.xlsx", true);
    }

    @Override
    public UserActiveDurationStatistic getLastWeekActiveDurationUserInfo(GetLastWeekActiveDurationUserInfoDTO dto) {
        UserActiveDurationStatistic ret = new UserActiveDurationStatistic();
        ret.setAverage(getActiveDurationThreshold());
        Map<Long, Long> lastWeekBelow = new HashMap<>();
        if(dto.getUserIds() == null) {
            lastWeekBelow = SecurityUtils.getTeamId() == null ? new HashMap<>() : getLastWeekBelowAverageActiveDurationUserByTeamId(SecurityUtils.getTenantId(), List.of(SecurityUtils.getTeamId()));
        } else {
            lastWeekBelow = getLastWeekBelowAverageActiveDurationUserByUserId(SecurityUtils.getTenantId(), dto.getUserIds());
        }
        ret.setLastWeekBelowAverageCount(lastWeekBelow.keySet().size());
        ret.setLastWeekBelowAverage(lastWeekBelow);
        return ret;
    }


    private void setP1ExcelGrandTotal(List<PipelineAnalyticsVO> pipelineAnalyticsVOList) {
        Set<Long> appliedProcessIdSet = new HashSet<>();
        Set<Long> submittedProcessIdSet = new HashSet<>();
        Set<Long> updateSubmittedProcessIdSet = new HashSet<>();
        Set<Long> interviewProcessIdSet = new HashSet<>();
        Set<Long> offeredProcessIdSet = new HashSet<>();
        Set<Long> offerAcceptedProcessIdSet = new HashSet<>();
        Set<Long> startedProcessIdSet = new HashSet<>();
        pipelineAnalyticsVOList.forEach(o -> {
//            if (ObjectUtil.isNotEmpty(o.getAppliedActivityId())) {
//                Set<Long> itemAppliedProcessIdSet = Arrays.stream(o.getAppliedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                appliedProcessIdSet.addAll(itemAppliedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getSubmittedActivityId())) {
//                Set<Long> itemSubmittedProcessIdSet = Arrays.stream(o.getSubmittedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                submittedProcessIdSet.addAll(itemSubmittedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getPipelineUpdateSubmittedActivityId())) {
//                Set<Long> itemUpdateSubmittedProcessIdSet = Arrays.stream(o.getPipelineUpdateSubmittedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                updateSubmittedProcessIdSet.addAll(itemUpdateSubmittedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getInterviewActivityId())) {
//                Set<Long> itemInterviewProcessIdSet = Arrays.stream(o.getInterviewActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                interviewProcessIdSet.addAll(itemInterviewProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getOfferedActivityId())) {
//                Set<Long> itemOfferedProcessIdSet = Arrays.stream(o.getOfferedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                offeredProcessIdSet.addAll(itemOfferedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getOfferAcceptedActivityId())) {
//                Set<Long> itemOfferAcceptedProcessIdSet = Arrays.stream(o.getOfferAcceptedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                offerAcceptedProcessIdSet.addAll(itemOfferAcceptedProcessIdSet);
//            }
//            if (ObjectUtil.isNotEmpty(o.getStartedActivityId())) {
//                Set<Long> itemStartedProcessIdSet = Arrays.stream(o.getStartedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
//                startedProcessIdSet.addAll(itemStartedProcessIdSet);
//            }
        });

        PipelineAnalyticsVO grandTotal = new PipelineAnalyticsVO();
        grandTotal.setUserName(GRAND_TOTAL);
        grandTotal.setAppliedCount(appliedProcessIdSet.size());
        grandTotal.setSubmittedCount(submittedProcessIdSet.size());
        grandTotal.setPipelineUpdateSubmittedCount(updateSubmittedProcessIdSet.size());
        grandTotal.setInterviewCount(interviewProcessIdSet.size());
        grandTotal.setOfferedCount(offeredProcessIdSet.size());
        grandTotal.setOfferAcceptedCount(offerAcceptedProcessIdSet.size());
        grandTotal.setStartedCount(startedProcessIdSet.size());
        pipelineAnalyticsVOList.add(grandTotal);
    }

}
