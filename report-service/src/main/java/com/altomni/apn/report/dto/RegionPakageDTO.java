package com.altomni.apn.report.dto;

import com.altomni.apn.report.domain.enumeration.CountryPackageEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RegionPakageDTO implements Serializable {

    private CountryPackageEnum region;

    private Set<Integer> countryIds;

}
