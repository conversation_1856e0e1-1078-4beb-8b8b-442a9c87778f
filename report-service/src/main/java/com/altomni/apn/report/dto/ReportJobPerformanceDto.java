package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;

import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z;

@Data
public class ReportJobPerformanceDto implements Serializable {

    private List<Long> companyIds;

    private String fromDate;

    private String toDate;

    private List<JobType> jobTypes;

    public String getFromDate(){
        return getUtcByTimeZone(fromDate + "T00:00:00Z");
    }

    public String getToDate(){
        return getUtcByTimeZone(toDate + "T23:59:59Z");
    }

    private String timeZone;

    public String getPrimitiveFrom() {
        return fromDate;
    }

    public String getPrimitiveTo() {
        return toDate;
    }

    public String getUtcByTimeZone(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(YYYY_MM_DD_T_HH_MM_SS_Z);
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        return dateTime.atZone(ZoneId.of(timeZone)).withZoneSameInstant(ZoneOffset.UTC).toString();
    }


}
