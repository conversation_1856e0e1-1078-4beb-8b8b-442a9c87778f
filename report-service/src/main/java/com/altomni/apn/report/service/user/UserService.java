package com.altomni.apn.report.service.user;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.PrivilegeName;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.user.domain.user.ApnParam;
import com.altomni.apn.user.domain.user.Team;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamTreeDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * Service Interface for managing {@link Team}.
 */
@Component
@FeignClient(value = "user-service")
public interface UserService {

    @GetMapping("/user/api/v3/teams/{id}")
    ResponseEntity<Team> findTeamById(@PathVariable("id") Long id);

    @PostMapping("/user/api/v3/permissions/teams/users/plain")
    ResponseEntity<List<PermissionTeamMemberDTO>> getPlainTeamMembersByTeamIds(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM);

    @PostMapping("/user/api/v3/permissions/teams/users/plain/with-team")
    ResponseEntity<List<PermissionTeamMemberDTO>> getTeamMembersByTeamIds(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM);

    @PostMapping("/user/api/v3/users/get-user-names-by-id-in-or-tenant")
    ResponseEntity<Map<Long, UserUidNameDTO>> getAllUserNameByIdInOrTenant(@RequestBody Set<Long> ids);

    @GetMapping("/user/api/v3/users/all-brief")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsers();

    @PostMapping("/user/api/v3/users/all-brief-by-ids")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUserByIds(@RequestBody Set<Long> ids);

    @GetMapping("/user/api/v3/users/all-brief-by-tenant-id/{tenantId}")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsersByTenantId(@PathVariable("tenantId") Long tenantId);

    @GetMapping("/user/api/v3/users/brief-by-tenant-id-team-category-id/{tenantId}")
    ResponseEntity<List<UserBriefDTO>> getBriefUsersByTenantIdAndTeamCategoryFilter(@PathVariable("tenantId") Long tenantId);

    @GetMapping("/user/api/v3/users/get-all-user-by-team-id/{teamId}")
    ResponseEntity<List<UserBriefDTO>> findAllActivatedBriefByTeamId(@PathVariable("teamId") Long teamId);

    @PostMapping("/user/api/v3/users/get-team-info")
    ResponseEntity<List<TeamInfoVO>>  getTeamInfoVOsByUserIds(@RequestBody List<Long> userIds);

    @PostMapping("/user/api/v3/users/get-team-info-with-team-category-filter")
    ResponseEntity<List<TeamInfoVO>> getTeamInfoListWithTeamCategoryFilter(@RequestBody List<Long> userIds);

    @PostMapping("/user/api/v3/permissions/teams/users/plain/with-team/active")
    ResponseEntity<List<PermissionTeamUserDTO>> getActiveTeamMembersByTeamIds(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM);

    @GetMapping("/user/api/v3/teams-by-user-id/{userId}")
    ResponseEntity<List<Long>> getTeamsByUserId(@PathVariable("userId") Long userId);

    @PostMapping("/user/api/v3/permissions/teams/users/all-status-team-user-ids")
    ResponseEntity<Set<Long>> getAllTeamUserIdsByPermissionTeamIdIn(@RequestBody Set<Long> teamIds);

    @PostMapping("/user/api/v3/permissions/teams/users/team-user-ids-with-team-category-filter")
    ResponseEntity<Set<Long>> getTeamUserIdsByPermissionTeamIdWithCategoryFilter(@RequestBody Set<Long> teamIds);

    @PostMapping("/user/api/v3/permissions/teams/users/active-team-user-ids")
    ResponseEntity<Set<Long>> getAllActiveTeamUserIdsByPermissionTeamIdIn(@RequestBody Set<Long> teamIds);

    @GetMapping("/user/api/v3/permissions/users/{userId}/data-permissions")
    ResponseEntity<PermissionUserTeamPermissionVM> getDataPermissionsByUserId(@PathVariable("userId") Long userId);

    @GetMapping("/user/api/v3/permissions/teams/tree/with-permission-and-user/{module}/{isPrimary}")
    ResponseEntity<List<PermissionTeamTreeDTO>> getTeamsWithPermissionAndUserByType(@PathVariable("module") Module module, @PathVariable("isPrimary") Boolean isPrimary) throws ExecutionException, InterruptedException;

    @GetMapping("/user/api/v3/users/apnparam/find-by-paramkey")
    ResponseEntity<ApnParam> findByParamKey(@RequestParam("paramKey") String paramKey, @RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status);

    @GetMapping("/user/api/v3/users/all-brief-with-permission/REPORT")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsersWithPermissionForCurrentUserReportPermission();

    @PostMapping("/user/api/v3/permissions/teams/filter-user-ids-by-team-category")
    ResponseEntity<List<Long>> getUserIdFilterByTeamCategory(@RequestBody List<Long> userIds);

}
