package com.altomni.apn.report.domain.vo;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
public class ReportG2StatusApplicationVo implements Serializable {

    @Id
    private Long tenantId;

    private Long appliedCount;

    private Long submittedCount;

    private Long interviewCount;

    private Long offeredCount;

    private Long offerAcceptedCount;

    private Long startedCount;

    private String appliedActivityId;

    private String submittedActivityId;

    private String interviewActivityId;

    private String offeredActivityId;

    private String offerAcceptedActivityId;

    private String startedActivityId;

}
