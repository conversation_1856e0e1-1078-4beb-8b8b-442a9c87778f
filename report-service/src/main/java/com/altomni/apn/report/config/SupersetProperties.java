package com.altomni.apn.report.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "application.superset")
public class SupersetProperties {

    private String domain;
    private String username;
    private String password;
    private Integer datasourceId;
    private boolean enabled = false;

}
