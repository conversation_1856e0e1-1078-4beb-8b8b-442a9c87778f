package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigInteger;

@Data
@Accessors(chain = true)
public class ReportMonthlyRevenueDownloadVO {

    @ExcelIgnore
    private String id;

    @ColumnWidth(30)
    @ExcelProperty(value = "Job Type", index = 0)
    private String jobType;

    @ColumnWidth(30)
    @ExcelProperty(value = "Client Name", index = 2)
    private String company;

    @ExcelIgnore
    private BigInteger companyId;

    @ExcelIgnore
    private BigInteger talentId;

    @ColumnWidth(30)
    @ExcelProperty(value = "Candidate Name", index = 1)
    private String talentName;

    @ColumnWidth(40)
    @ExcelProperty(value = "Onboard Date/ Start Date", index = 3)
    private String onboardDate;

    @ColumnWidth(30)
    @ExcelProperty(value = "AM", index = 4)
    private String am;

    @ColumnWidth(30)
    @ExcelProperty(value = " Recruiter", index = 5)
    private String recruiter;

    @ColumnWidth(30)
    @ExcelProperty(value = "Sourcer", index = 6)
    private String sourcer;

    @ColumnWidth(30)
    @ExcelProperty(value = "DM", index = 8)
    private String dm;

    @ColumnWidth(30)
    @ExcelProperty(value = "AC", index = 7)
    private String ac;

    @ColumnWidth(30)
    @ExcelProperty(value = "Candidate Owner", index = 9)
    private String owners;

    @ColumnWidth(30)
    @ExcelProperty(value = "Sales Lead Owner", index = 10)
    private String salesLeadOwner;

    @ColumnWidth(30)
    @ExcelProperty(value = "BD Owner", index = 11)
    private String bdOwner;

    @ColumnWidth(30)
    @ExcelProperty(value = "Placement Fee Number", index = 12)
    private String totalBillAmount;

    @ColumnWidth(30)
    @ExcelProperty(value = "Placement Fee Currency", index = 13)
    private String currency;

    @ColumnWidth(30)
    @ExcelProperty(value = "Bill Rate Number", index = 14)
    private String billBillRate;

    @ColumnWidth(30)
    @ExcelProperty(value = "Bill Rate Currency", index = 15)
    private String billRateCurrency;

    @ColumnWidth(30)
    @ExcelProperty(value = "Pay Rate Number", index = 16)
    private String finalPayRate;

    @ColumnWidth(30)
    @ExcelProperty(value = "Pay Rate Currency", index = 17)
    private String payRateCurrency;
}
