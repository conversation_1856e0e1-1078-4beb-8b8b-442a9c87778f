package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * view type for report rendering
 */
public enum ReportViewType implements ConvertedEnum<Integer> {

    TABLE(0);


    private final Integer dbValue;

    ReportViewType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<ReportViewType, Integer> resolver =
        new ReverseEnumResolver<>(ReportViewType.class, ReportViewType::toDbValue);

    public static ReportViewType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
