package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.CrossSellVO;
import com.altomni.apn.report.domain.vo.ReportSalesDetailVo;
import com.altomni.apn.report.dto.CompanyServiceTypeDTO;
import com.altomni.apn.report.dto.SaleDetailDTO;
import com.altomni.apn.report.service.CrossSellService;
import com.altomni.apn.report.service.ReportSalesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class CrossSellResource {

    @Resource
    private CrossSellService crossSellService;

    @PostMapping("/get-revenue-gp")
    public ResponseEntity<CrossSellVO> getRevenueGp(@RequestBody CompanyServiceTypeDTO dto) {
        log.info("[APN: CrossSellResource @{}] REST request to get revenue & gp", SecurityUtils.getUserId(), dto);
        return ResponseEntity.ok(crossSellService.getRevenueGp(dto));
    }

}
