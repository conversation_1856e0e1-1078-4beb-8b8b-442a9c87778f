package com.altomni.apn.report.domain;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.enumeration.ReportViewType;
import com.altomni.apn.report.domain.enumeration.ReportViewTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.io.Serializable;

@Getter
@Setter
@Entity
@Table(name = "report")
public class Report extends AbstractPermissionAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @JsonIgnore
    @Column(name = "tenant_id", nullable = false, updatable = false)
    private Long tenantId;

    @Column(name = "name",nullable = false)
    private String name;

    @Column(name = "description")
    private String description;

    @Column(name = "dataset_id",nullable = false)
    private Integer datasetId;

    @Convert(converter = ReportViewTypeConverter.class)
    @Column(name = "view_type",nullable = false)
    private ReportViewType viewType;

    @Column(name = "config", columnDefinition = "json")
    private String config = "{}";


    /**
     * check user permission
     * only super admin and owner can modify
     */
    public void userCanModify() {
        if (SecurityUtils.isSystemAdmin()) {
            return;
        }
        if (!SecurityUtils.getUserId().equals(this.getPermissionUserId())) {
            throw new ForbiddenException("You don't have permission to access this Report.");
        }
    }


}
