package com.altomni.apn.report.domain.vm.s2;

import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Entity
public class DormantJobCountVM implements Serializable {

    private static final long serialVersionUID = -1712376179290297440L;

    @Id
    @ApiModelProperty(value = "AM id")
    private Long id;

    @ApiModelProperty(value = "AM firstName")
    private String firstName;

    @ApiModelProperty(value = "AM lastName")
    private String lastName;

    @ApiModelProperty(value = "total number of dormant jobs")
    private Integer count;

    @ApiModelProperty(value = "AM's teams")
    private String team;

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Integer getCount() { return count; }

    public void setCount(Integer count) { this.count = count; }

    public String getTeam() {
        return team;
    }

    public void setTeam(String team) {
        this.team = team;
    }

}
