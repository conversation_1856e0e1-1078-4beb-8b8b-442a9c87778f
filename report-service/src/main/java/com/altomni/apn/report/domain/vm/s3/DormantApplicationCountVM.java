package com.altomni.apn.report.domain.vm.s3;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@Entity
public class DormantApplicationCountVM implements Serializable {

    /**
     * user id
     */
    @Id
    private Long id;

    private String recruiterFirstName;

    private String recruiterLastName;

    private Integer count;

}
