package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiSubmitToClientDetailExcelENVO {

    @ExcelIgnore
    private Long nodeId;

    @ExcelProperty(value = "Candidate Name", index = 0)
    private String fullName;

    @ExcelIgnore
    private Instant submitDate;

    @ExcelProperty(value = "Date of Submitted To Client", index = 1)
    private String submitDateFormat;

    @ExcelIgnore
    private String jobTitle;

    @ExcelIgnore
    private Long jobId;

    @ExcelProperty(value = "Job Title（ID）", index = 2)
    private String jobTitleAndJobId;

    @ExcelProperty(value = "Stayed over 72 hrs", index = 3)
    private String stayedOverStr;

    @ExcelProperty(value = "Job Code", index = 4)
    private String jobCode;

    @ExcelProperty(value = "Company", index = 5)
    private String companyName;

    @ExcelIgnore
    private Long companyId;

    @ExcelIgnore
    private JobType jobType;

    @ExcelProperty(value = "Job Status", index = 6)
    private JobStatus jobStatus;

    //后端去做 淘汰的兼容
    @ExcelProperty(value = "Workflow Status", index = 7)
    private MyCandidateStatusFilter workflowStatus;

    @ExcelProperty(value = "Pipeline Notes", index = 8)
    private String pipelineNote;

    @ExcelProperty(value = "Job Location", index = 9)
    private String jobLocation;

    @ExcelProperty(value = "Account Manager", index = 10)
    private String am;

    @ExcelProperty(value = "Co-Account Manager", index = 11)
    private String coAm;

    @ExcelProperty(value = "Recruiter", index = 12)
    private String recruiter;

    @ExcelProperty(value = "Sourcer", index = 13)
    private String sourcer;

    @ExcelProperty(value = "AC", index = 14)
    private String ac;

    @ExcelProperty(value = "DM", index = 15)
    private String dm;

    @ExcelProperty(value = "Owner", index = 16)
    private String owner;

    @ExcelProperty(value = "Sales Lead Owner", index = 17)
    private String salesLeadOwner;

    @ExcelProperty(value = "BD Owner", index = 18)
    private String bdOwner;

    @ExcelProperty(value = "Last Updated By", index = 19)
    private String lastModifiedBy;

    @ExcelIgnore
    private Instant lastModifiedDate;

    @ExcelProperty(value = "Last Updated At", index = 20)
    private String lastModifiedDateFormat;

    public String getJobTitleAndJobId() {
        if (jobTitle == null || jobId == null) {
            return null;
        }
        return jobTitle + " (" + jobId + ")";
    }

    public JobStatus getJobStatus() {
        if (jobType == JobType.PAY_ROLL) {
            return null;
        }
        return jobStatus;
    }

}
