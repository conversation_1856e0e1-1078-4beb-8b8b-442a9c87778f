package com.altomni.apn.report.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportQuarterlyOnboardingOverViewVO implements Serializable {

    List<ReportQuarterlyOnboardingDetailVO> voList;

    ReportQuarterlyOnboardingTotalVO total;
}
