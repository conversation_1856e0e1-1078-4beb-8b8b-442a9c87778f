package com.altomni.apn.report.domain.enumeration;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetFrequencyType;

public class TimeSheetFrequencyTypeDataConverter implements Converter<TimeSheetFrequencyType> {

    @Override
    public Class supportJavaTypeKey() {
        return TimeSheetFrequencyType.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }



    @Override
    public WriteCellData<String> convertToExcelData(TimeSheetFrequencyType timeSheetFrequencyType, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(timeSheetFrequencyType.getDescription());
    }

}