package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiEliminateDetailVO extends RecruitingKpiApplicationBaseDetailVO{

    private EliminateReason reason;

    @Override
    public void encrypt() {
        super.encrypt();
        this.reason = null;
    }
}
