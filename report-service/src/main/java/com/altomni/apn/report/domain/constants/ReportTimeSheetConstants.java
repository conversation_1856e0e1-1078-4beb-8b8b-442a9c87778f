package com.altomni.apn.report.domain.constants;

import java.util.HashMap;
import java.util.Map;

public interface ReportTimeSheetConstants {

    Map<String, String> GROUP_ORDER_COLUMN_LIST = new HashMap<>() {
        {
            put("full_name", "tswer.full_name");
            put("regular_hours", "sum(tsr.regular_hours)");
            put("over_time", "sum(tsr.over_time)");
            put("double_time", "sum(tsr.double_time)");
            put("total_hours", "sum(tsr.total_hours)");
            put("week_ending_date", "tsr.week_ending_date");
            put("week_end", "tsr.week_end");
            put("holiday_hours", "tgh.hours");
        }
    };

    Map<String, String> ORDER_COLUMN_LIST = new HashMap<>() {
        {
            put("full_name", "tswer.full_name");
            put("regular_hours", "tsr.regular_hours");
            put("over_time", "tsr.over_time");
            put("double_time", "tsr.double_time");
            put("total_hours", "tsr.total_hours");
            put("work_date", "tsrr.work_date");
            put("week_ending_date", "tsrr.week_ending_date");
            put("week_end", "tsrr.week_end");
            put("holiday_hours", "tgh.hours");
        }
    };

}
