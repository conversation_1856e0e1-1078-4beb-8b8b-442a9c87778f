package com.altomni.apn.report.service.talent;

import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import com.altomni.apn.common.dto.talent.TalentNoteDTO;
import com.altomni.apn.common.vo.talent.TalentBriefVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

/**
 * Service Interface for managing {@link com.altomni.apn.common.domain.talent.TalentV3}.
 */
@Component
@FeignClient(value = "talent-service")
public interface TalentService {

    @PostMapping("/talent/api/v3/talents/get-by-ids-without-entity")
    ResponseEntity<List<TalentBriefDTO>> getTalentsByIdsWithoutEntity(@RequestBody Set<Long> ids);

    @PostMapping("/talent/api/v3/talent-call-candidate-notes/voip-phone-call-ids")
    ResponseEntity<List<TalentNoteDTO>> getAllCallCandidateNotesByVoipPhoneCallIds(@RequestBody List<String> phoneCallIds);

    @PostMapping("/talent/api/v3/brief-talents")
    ResponseEntity<Set<TalentBriefVO>> findBriefTalentsByTalentIds(@RequestBody Set<Long> talentIds);
}
