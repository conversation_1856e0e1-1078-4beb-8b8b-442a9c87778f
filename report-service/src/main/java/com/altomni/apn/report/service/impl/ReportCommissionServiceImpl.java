package com.altomni.apn.report.service.impl;

import com.altomni.apn.report.domain.vo.ReportCommissionVO;
import com.altomni.apn.report.domain.vo.ReportCommissionWithExcelDataVO;
import com.altomni.apn.report.dto.ReportCommissionSearchDTO;
import com.altomni.apn.report.repository.ReportCommissionRepository;
import com.altomni.apn.report.service.ReportCommissionService;
import com.altomni.apn.report.util.ExcelUtil;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Service("reportCommissionService")
public class ReportCommissionServiceImpl implements ReportCommissionService {

    @Resource
    private ReportCommissionRepository reportCommissionRepository;

    @Override
    public Page<ReportCommissionVO> searchCommissionReport(ReportCommissionSearchDTO dto) {
        List<ReportCommissionVO> dataList = reportCommissionRepository.searchCommissionReportBaseData(dto);
        Long count = reportCommissionRepository.countCommissionReportBaseData(dto);
        return new PageImpl<>(dataList, Pageable.unpaged(), count);
    }

    @Override
    public void exportCommissionReport(ReportCommissionSearchDTO dto, HttpServletResponse response) {
        List<ReportCommissionWithExcelDataVO> voList = reportCommissionRepository.searchCommissionReportData(dto);
        ExcelUtil.downloadExcelCustom(response, ReportCommissionWithExcelDataVO.class, voList, "", "Commission_Report_" + dto.getFrom() + "_" + dto.getTo() + ".xlsx", true);
    }

}
