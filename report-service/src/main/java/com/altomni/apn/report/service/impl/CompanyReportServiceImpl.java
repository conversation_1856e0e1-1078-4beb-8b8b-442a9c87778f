package com.altomni.apn.report.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectSearchVM;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectVM;
import com.altomni.apn.report.domain.vo.ReportBdTrackingDetailVO;
import com.altomni.apn.report.domain.vo.ReportBdTrackingVO;
import com.altomni.apn.report.dto.BdTrackingDetailDTO;
import com.altomni.apn.report.dto.BdTrackingSearchDTO;
import com.altomni.apn.report.dto.CompanyBDReportSearchVM;
import com.altomni.apn.report.dto.UserTeamPariDTO;
import com.altomni.apn.report.repository.CompanyReportRepository;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.CompanyReportService;
import com.altomni.apn.report.service.ReportService;
import com.altomni.apn.report.service.company.CompanyService;
import com.altomni.apn.report.service.user.UserService;
import com.altomni.apn.user.domain.user.Team;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.service.dto.user.UserInTeam;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class CompanyReportServiceImpl implements CompanyReportService {

    private final Logger log = LoggerFactory.getLogger(CompanyReportServiceImpl.class);

    @Resource
    private CompanyService companyService;

    @Resource
    private UserService userService;

    @Resource
    private CompanyReportRepository companyReportRepository;

    @Resource
    private ReportService reportService;

    @Resource
    private ReportRepository reportRepository;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    @Override
    public ResponseEntity<List<CompanyProspectVM>> getBDReport(Long teamId, Pageable pageable) {
        Set<Long> teamUserIds = new HashSet<>();
        if (Objects.nonNull(teamId)) {
            Team team = userService.findTeamById(teamId).getBody();
            if (ObjectUtil.isNotEmpty(team) && team.getTenantId().equals(SecurityUtils.getTenantId()) && CollectionUtils.isEmpty(team.users)) {
                teamUserIds.addAll(team.users.stream().map(UserInTeam::getId).collect(Collectors.toList()));
            }
        }

        CompanyProspectSearchVM searchVM = new CompanyProspectSearchVM();
        searchVM.setTeamUserIds(teamUserIds);
//        ResponseEntity<List<CompanyProspectVM>> response = companyService.searchAllCompanyProspectsForBDReport(pageable.getPageNumber(), pageable.getPageSize(), searchVM);
//        return response;
        return null;
    }

    @Override
    public ResponseEntity<List<CompanyProspectVM>> getBDReport(CompanyBDReportSearchVM companyBDReportSearchVM, Pageable pageable) {
        StopWatch stopWatch = new StopWatch("getBDReportTask");
        stopWatch.start("userTask");
        log.debug("TeamIds= {}", companyBDReportSearchVM.getTeamIds());
        boolean isValid = this.applyDataPermission(companyBDReportSearchVM);
        log.debug("companyBDReportSearchVM.userIds = {}", companyBDReportSearchVM.getTeamUserIds());
        if (!isValid){
            return ResponseEntity.ok(null);
        }

//        if (CollectionUtils.isEmpty(companyBDReportSearchVM.getTeamUserIds()) && CollectionUtils.isNotEmpty(companyBDReportSearchVM.getTeamIds())) {
//            PermissionTeamMemberSearchVM permissionTeamMemberSearchVM = new PermissionTeamMemberSearchVM();
//            permissionTeamMemberSearchVM.setTeamIds(companyBDReportSearchVM.getTeamIds());
//            List<PermissionTeamMemberDTO> teamMembers = userService.getPlainTeamMembersByTeamIds(permissionTeamMemberSearchVM).getBody();
//            if (CollectionUtils.isNotEmpty(teamMembers)) {
//                TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
//                if (Objects.isNull(teamDataPermission)) {
//                    teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
//                }
//                if (teamDataPermission.getSelf()) {
//                    Long userId = SecurityUtils.getUserId();
//                    companyBDReportSearchVM.setTeamUserIds(teamMembers.stream().map(PermissionTeamMemberDTO::getId).filter(uId -> uId.equals(userId)).collect(Collectors.toSet()));
//                }else{
//                    companyBDReportSearchVM.setTeamUserIds(teamMembers.stream().map(PermissionTeamMemberDTO::getId).collect(Collectors.toSet()));
//                }
//
//            } else {
//                return ResponseEntity.ok(null);
//            }
//        }


        CompanyProspectSearchVM searchVM = new CompanyProspectSearchVM();
        ServiceUtils.myCopyProperties(companyBDReportSearchVM, searchVM);
        stopWatch.stop();
        stopWatch.start("companyTask");
        ResponseEntity<List<CompanyProspectVM>> response = companyService.searchAllCompanyForBDReport(pageable.getPageNumber(), pageable.getPageSize(), companyBDReportSearchVM.getFromDate(), companyBDReportSearchVM.getToDate(), searchVM);
        stopWatch.stop();
        log.info("reportTask time = {}ms \n {}", stopWatch.getTotalTimeSeconds(), stopWatch.prettyPrint());
        return response;
    }

    private boolean applyDataPermission(CompanyBDReportSearchVM companyBDReportSearchVM){
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        boolean hasValidUsers = true;

        if (teamDataPermission.getSelf()) {
            Set<Long> userIds = new HashSet<>();
            userIds.add(SecurityUtils.getUserId());
            if (CollectionUtils.isNotEmpty(companyBDReportSearchVM.getTeamUserIds())){
                userIds = SetUtils.intersection(userIds, companyBDReportSearchVM.getTeamUserIds());
            }else if (CollectionUtils.isNotEmpty(companyBDReportSearchVM.getTeamIds())
                    && CollectionUtils.isEmpty(SetUtils.intersection(companyBDReportSearchVM.getTeamIds(), teamDataPermission.getNestedTeamIds()))) {
                // only select teams
                hasValidUsers = false;
            }
            if (CollectionUtils.isEmpty(userIds)){
                hasValidUsers = false;
            }
            companyBDReportSearchVM.setTeamUserIds(userIds);
        } else if (teamDataPermission.getAll()) {
            if (CollectionUtils.isEmpty(companyBDReportSearchVM.getTeamUserIds()) && CollectionUtils.isNotEmpty(companyBDReportSearchVM.getTeamIds())) {
                // only select teams
                hasValidUsers = this.hasValidUserIds(companyBDReportSearchVM, companyBDReportSearchVM.getTeamIds());

            }
        } else if (CollectionUtils.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            if (CollectionUtils.isNotEmpty(companyBDReportSearchVM.getTeamUserIds())){
                // select users
                Set<Long> nestedTeamIds = teamDataPermission.getNestedTeamIds();
                Set<Long> validUserIds = reportRepository.getUserTeamPair(companyBDReportSearchVM.getTeamUserIds())
                        .stream().filter(p -> nestedTeamIds.contains(p.getTeamId())).map(UserTeamPariDTO::getUserId).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(validUserIds)){
                    hasValidUsers = false;
                }
            } else if (CollectionUtils.isNotEmpty(companyBDReportSearchVM.getTeamIds())) {
                // only select teams
                Set<Long> teamIds = SetUtils.intersection(companyBDReportSearchVM.getTeamIds(), teamDataPermission.getNestedTeamIds());
                hasValidUsers = this.hasValidUserIds(companyBDReportSearchVM, teamIds);
            }else{
                // no teams, users selected
                hasValidUsers = this.hasValidUserIds(companyBDReportSearchVM, teamDataPermission.getNestedTeamIds());
            }
        }else {
            hasValidUsers = false;
        }
        return hasValidUsers;
    }

    private boolean hasValidUserIds(CompanyBDReportSearchVM companyBDReportSearchVM, Set<Long> teamIds){
        PermissionTeamMemberSearchVM searchVM = new PermissionTeamMemberSearchVM();
        searchVM.setTeamIds(teamIds);
        Set<Long> userIdsByTeams = Objects.requireNonNull(userService.getPlainTeamMembersByTeamIds(searchVM).getBody())
                .stream().map(PermissionTeamMemberDTO::getId).collect(Collectors.toSet());
        companyBDReportSearchVM.setTeamUserIds(userIdsByTeams);
        return CollectionUtils.isNotEmpty(userIdsByTeams);
    }


    private boolean applyDataPermission(BdTrackingSearchDTO dto){
        boolean hasValidData = true;
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        if (teamDataPermission.getSelf()) {
            Set<Long> userIds = new HashSet<>();
            userIds.add(SecurityUtils.getUserId());
            if (CollectionUtils.isNotEmpty(dto.getUserIds())){
                userIds = SetUtils.intersection(userIds, new HashSet<>(dto.getUserIds()));
            }else if (CollectionUtils.isNotEmpty(dto.getTeamIds())
                    && CollectionUtils.isEmpty(SetUtils.intersection(new HashSet<>(dto.getTeamIds()), teamDataPermission.getNestedTeamIds()))) {
                // only select teams
                hasValidData = false;
            }
            if (CollectionUtils.isEmpty(userIds)){
                hasValidData = false;
            }
            dto.setUserIds(new ArrayList<>(userIds));
        } else if (teamDataPermission.getAll()) {
            // PASS
        } else if (CollectionUtils.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            if (CollectionUtils.isNotEmpty(dto.getUserIds())){
                // select users
                Set<Long> nestedTeamIds = teamDataPermission.getNestedTeamIds();
                Set<Long> validUserIds = reportRepository.getUserTeamPair(dto.getUserIds())
                        .stream().filter(p -> nestedTeamIds.contains(p.getTeamId())).map(UserTeamPariDTO::getUserId).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(validUserIds)){
                    hasValidData = false;
                }
            } else if (CollectionUtils.isNotEmpty(dto.getTeamIds())) {
                // only select teams
                Set<Long> teamIds = SetUtils.intersection(new HashSet<>(dto.getTeamIds()), teamDataPermission.getNestedTeamIds());
                dto.setTeamIds(new ArrayList<>(teamIds));
            }else{
                // no teams, users selected
                dto.setTeamIds(new ArrayList<>(teamDataPermission.getNestedTeamIds()));
            }
        }else {
            hasValidData = false;
        }
        return hasValidData;
    }

    @Override
    public List<ReportBdTrackingVO> getBdTrackingReport(BdTrackingSearchDTO bdTrackingSearchDTO) {
        boolean hasValidData = this.applyDataPermission(bdTrackingSearchDTO);
        log.debug("hasValidData: {}", hasValidData);
        log.debug("user ids: {}", bdTrackingSearchDTO.getUserIds());
        log.debug("user ids: {}", bdTrackingSearchDTO.getTeamIds());
        if (Boolean.FALSE.equals(hasValidData)){
            return new ArrayList<>();
        }
        return companyReportRepository.searchBdTrackingReport(bdTrackingSearchDTO);
    }

    @Override
    public List<ReportBdTrackingDetailVO> getBdTrackingDetailList(BdTrackingDetailDTO bdTrackingDetailDTO) {
        return companyReportRepository.getBdTrackingDetailList(bdTrackingDetailDTO);
    }

}
