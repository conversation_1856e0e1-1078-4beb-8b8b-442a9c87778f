package com.altomni.apn.report.dto.e5;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CrmBDActivitySearchDetailDTO {

    private String fromDate;

    private String toDate;

    private Long userId;

    private Integer followUpContactTypeId;

    private List<String> businessTypes;

    private String timeZone;

    private List<Long> userIdList;

}
