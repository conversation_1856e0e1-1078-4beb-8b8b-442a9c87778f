package com.altomni.apn.report.web.rest.vm;

import lombok.Data;
import org.openapitools.client.model.ChartDataFilter;

@Data
public class ReportFilterOpVM {
    private String op;
    private boolean requireValue = true;


    public ReportFilterOpVM(ChartDataFilter.OpEnum op) {
        this.op = op.getValue();
    }


    public static ReportFilterOpVM fromOpEnum(ChartDataFilter.OpEnum op) {
        ReportFilterOpVM reportFilterOpVM = new ReportFilterOpVM(op);
        reportFilterOpVM.requireValue = op.getRequireValue();
        return reportFilterOpVM;
    }


}
