package com.altomni.apn.report.web.rest;


import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.UserBriefVO;
import com.altomni.apn.report.domain.vo.p2.PipelineAnalyticsByCompanyVO;
import com.altomni.apn.report.dto.p1.PipelineAnalyticsDTO;
import com.altomni.apn.report.dto.p2.ReportPipelineParamDTO;
import com.altomni.apn.report.service.ReportPipelineService;
import com.altomni.apn.report.domain.vo.p1.PipelineAnalyticsVO;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

/**
 * REST controller for pipeline Report.
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3")
public class ReportPipelineResource {

    private final Logger log = LoggerFactory.getLogger(ReportPipelineResource.class);

    @Resource
    private ReportPipelineService reportPipelineService;

    /** P1 Report
     * Pipeline Analytics
     */
    @PostMapping("/report/p1-pipeline-analytics-by-users")
    @Timed
    public ResponseEntity<List<PipelineAnalyticsVO>> p1PipelineAnalyticsByUsers(@RequestBody PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        log.info("[APN: ReportPipelineV3 @{}] REST request to get p1 pipeline analytics by users. reportParam:{}", SecurityUtils.getUserId(), pipelineAnalyticsDTO);
        return ResponseEntity.ok(reportPipelineService.p1PipelineAnalyticsByUsers(pipelineAnalyticsDTO));
    }

     /** P1 Report
     * Pipeline Analytics
     */
    @PostMapping("/report/p1-pipeline-analytics-by-users-excel")
    @Timed
    public void exportP1PipelineAnalyticsByUsersByExcel(@Valid @RequestBody PipelineAnalyticsDTO pipelineAnalyticsDTO, HttpServletResponse response) {
        log.info("[APN: ReportPipelineV3 @{}] REST request to download p1 pipeline analytics by users. reportParam:{}", SecurityUtils.getUserId(), pipelineAnalyticsDTO);
        reportPipelineService.exportP1PipelineAnalyticsByUsersByExcel(pipelineAnalyticsDTO, response);
    }

    @GetMapping("/report/p2-pipeline-analytics-by-company")
    @Timed
    public ResponseEntity<List<PipelineAnalyticsByCompanyVO>> p2PipelineAnalyticsByCompany(
            @RequestParam(value = "fromDate") String fromDate,
            @RequestParam(value = "toDate") String toDate,
            @RequestParam(value = "userRole", required = false) UserRole userRole,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "jobCountry", required = false) String jobCountry) {
        ReportPipelineParamDTO reportPipelineParamDTO = new ReportPipelineParamDTO().setFromDate(fromDate).setToDate(toDate).setUserId(userId).setUserRole(userRole).setJobCountry(jobCountry);
        log.info("[APN: ReportPipelineV3 @{}] REST request to get p2 pipeline analytics by company report. reportParam:{}", SecurityUtils.getUserId(), reportPipelineParamDTO);
        return ResponseEntity.ok(reportPipelineService.p2PipelineAnalyticsByCompany(reportPipelineParamDTO));
    }

    @GetMapping("/report/p2-pipeline-analytics-by-company-excel")
    @Timed
    public void p2PipelineAnalyticsByCompanyByExcel(
            HttpServletResponse response,
            @RequestParam(value = "fromDate") String fromDate,
            @RequestParam(value = "toDate") String toDate,
            @RequestParam(value = "userRole", required = false) UserRole userRole,
            @RequestParam(value = "userId", required = false) Long userId,
            @RequestParam(value = "jobCountry", required = false) String jobCountry)
            throws NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        ReportPipelineParamDTO reportPipelineParamDTO = new ReportPipelineParamDTO().setFromDate(fromDate).setToDate(toDate).setUserId(userId).setUserRole(userRole).setJobCountry(jobCountry);
        log.info("[APN: ReportPipelineV3 @{}] REST request to download p2 pipeline analytics by company report by excel. reportParam:{}", SecurityUtils.getUserId(), reportPipelineParamDTO);
        reportPipelineService.exportP2PipelineAnalyticsByCompanyByExcel(reportPipelineParamDTO, response);
    }

    @GetMapping("/report/p2-pipeline-analytics-by-company/user/filter")
    @Timed
    public ResponseEntity<List<UserBriefVO>> getP2UserFilter(
            @RequestParam(value = "fromDate") String fromDate,
            @RequestParam(value = "toDate") String toDate,
            @RequestParam(value = "jobCountry", required = false) String jobCountry) {
        ReportPipelineParamDTO reportPipelineParam = new ReportPipelineParamDTO().setFromDate(fromDate).setToDate(toDate).setJobCountry(jobCountry);
        log.info("[APN: ReportPipelineV3 @{}] REST request to get p2 user filter. --> reportParam:{}", SecurityUtils.getUserId(), reportPipelineParam);
        return ResponseEntity.ok(reportPipelineService.getP2UserFilter(reportPipelineParam));
    }
}
