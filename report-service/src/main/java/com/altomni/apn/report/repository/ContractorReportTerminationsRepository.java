package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ContractorTerminationVo;
import com.altomni.apn.report.dto.ContractorSearchDto;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.altomni.apn.report.domain.constants.ReportTerminationsConstants.MULTILINGUAL_MIXED_ORDER_COLUMN_LIST;
import static com.altomni.apn.report.domain.constants.ReportTerminationsConstants.ORDER_COLUMN_LIST;

@Repository
public class ContractorReportTerminationsRepository extends BaseCustomRepository{

    public List<ContractorTerminationVo> searchContractorTerminationsData(ContractorSearchDto searchDto, Pageable pageable) {
        Map<Integer, Object> paramMap = new HashMap<>(16);
        String whereSql = doContractorTerminationsWhereSql(searchDto, paramMap);
        String orderSql = doContractorTerminationsOrderSql(searchDto.getSort());
        String dateSql = constructContractorTerminationsDateSql(searchDto, whereSql, orderSql, paramMap);
        return doSearchData(dateSql, ContractorTerminationVo.class, paramMap, pageable);
    }

    public Long searchContractorTerminationsCount(ContractorSearchDto searchDto) {
        Map<Integer, Object> paramMap = new HashMap<>(16);
        String whereSql = doContractorTerminationsWhereSql(searchDto, paramMap);
        String countSql = constructContractorTerminationsCountSql(whereSql, paramMap);
        return doSearchCount(countSql, paramMap);
    }

    private String constructContractorTerminationsCountSql(String whereSql, Map<Integer, Object> paramMap) {
        paramMap.put(CollUtil.isEmpty(paramMap)? 1: paramMap.size() + 1, Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.PAY_ROLL.toDbValue(), JobType.MSP.toDbValue()));
        return """
                select count(1) from (SELECT count(1)
                FROM START s
                RIGHT JOIN start_termination st ON st.start_id = s.id
                RIGHT JOIN talent t ON t.id = s.talent_id
                right join job j on j.id = s.job_id
                left join (select max(tta.id) id, tta.start_id from timesheet_talent_assignment tta group by tta.start_id) abi on abi.start_id = s.id
                left join assignment_bill_info tta on tta.assignment_id = abi.id
                WHERE s.position_type in 
                """ + "(?" + paramMap.size() + ")" + whereSql + " group by s.id ) a ";
    }

    private String constructContractorTerminationsDateSql(ContractorSearchDto searchDto, String whereSql, String orderSql, Map<Integer, Object> paramMap) {
        paramMap.put(CollUtil.isEmpty(paramMap)? 1: paramMap.size() + 1, Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.PAY_ROLL.toDbValue(), JobType.MSP.toDbValue()));
        return """
                SELECT s.id, s.job_id, t.full_name, j.title job_title, c.full_business_name company, tta.assignment_division, (select contact from talent_contact where talent_id = s.talent_id and jhi_type = 2 and status = 0 order by sort asc limit 1) email,
                (select GROUP_CONCAT(sc.user_full_name) from start_commission sc where sc.start_id = s.id and sc.user_role = 0 group by sc.start_id) am, GROUP_CONCAT( recruitersc.user_full_name order by recruitersc.user_id) recruiter, abi.id assignment_id, j.code job_code, cslcct.full_name hiring_manager, GROUP_CONCAT( tm.`name` order by recruitersc.user_id) as recruiter_team,
                s.`status` start_status, s.start_date start_date, s.end_date end_date, st.termination_date, st.note note,
                IF( sa.id IS NULL, jl.original_loc ->> '$.addressLine', IFNULL( sa.address, sa.original_loc ->> '$.addressLine')) address,
                IF(sa.id is null, jl.original_loc ->> '$.city', IFNULL(sa.city, sa.original_loc ->> '$.city')) city,
                IF(sa.id is null, jl.original_loc ->> '$.province', IFNULL(sa.province, sa.original_loc ->> '$.province')) state,
                IF(sa.id is null, jl.original_loc ->> '$.country', IFNULL(sa.country, sa.original_loc ->> '$.country')) country,
                (select symbol from enum_currency where id = scr.currency) as currency,""" + "'" + searchDto.getTimeZone() + "' time_zone, "   + """
                scr.final_bill_rate bill_rate, scr.final_pay_rate pay_rate, scr.rate_unit_type bill_rate_unit, scr.rate_unit_type pay_rate_unit, st.reason termination_reason,
                (case CONCAT(u.first_name,u.last_name)  regexp '[一-龥]' when 1 then CONCAT(u.last_name,u.first_name) ELSE CONCAT(u.first_name, \\" \\",u.last_name) END) terminated_by
                FROM START s
                RIGHT JOIN start_termination st ON st.start_id = s.id
                RIGHT JOIN talent t ON t.id = s.talent_id
                right join job j on j.id = s.job_id 
                right join company c on c.id = j.company_id
                LEFT JOIN ( SELECT max( tta.id ) id, tta.talent_recruitment_process_id FROM timesheet_talent_assignment tta GROUP BY tta.talent_recruitment_process_id ) abi ON abi.talent_recruitment_process_id = s.talent_recruitment_process_id
                left join assignment_bill_info tta on tta.assignment_id = abi.id
                left join start_commission recruitersc on recruitersc.start_id = s.id and recruitersc.user_role = 1
                left join permission_user_team put on put.user_id = recruitersc.user_id and put.is_primary = 1
                left join permission_team tm on tm.id = put.team_id 
                left join job_company_contact_relation jccr on jccr.job_id = j.id and jccr.contact_category = 1
                left join company_sales_lead_client_contact cslcc on jccr.client_contact_id = cslcc.id
                left join talent cslcct on cslcct.id = cslcc.talent_id
                left join start_address sa on sa.start_id = s.id
                LEFT JOIN (select scr.start_id,max(scr.id) id from start_contract_rate scr group by scr.start_id) scrr ON scrr.start_id = s.id
                left join start_contract_rate scr on scr.id = scrr.id
                left join user u on u.id = st.created_by
                LEFT JOIN (SELECT jl.job_id, MIN(jl.id) AS id FROM job_location jl where jl.original_loc is not null GROUP BY jl.job_id ) a ON a.job_id = j.id
                left join job_location jl on jl.id = a.id
                WHERE s.position_type in 
                """ + "(?" + paramMap.size() + ") " +  whereSql + " group by s.id " + orderSql;
    }

    private String doContractorTerminationsOrderSql(SearchSortDTO sort) {
        StringBuilder sb = new StringBuilder();
        if (sort != null) {
            String column = StrUtil.toUnderlineCase(sort.getProperty());
            sb.append(" order by CASE WHEN IFNULL( ").append(ORDER_COLUMN_LIST.get(column)).append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            if (MULTILINGUAL_MIXED_ORDER_COLUMN_LIST.contains(column)) {
                sb.append("CONVERT( ").append(ORDER_COLUMN_LIST.get(column)).append(" USING gbk) ");
                sb.append(" ").append(sort.getDirection());
            } else {
                sb.append(column);
                sb.append(" ").append(sort.getDirection());
            }
        } else {
            sb.append(" order by CASE WHEN IFNULL( ").append("t.full_name").append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            sb.append(" CONVERT( ").append("t.full_name").append(" USING gbk) ");
            sb.append(" ASC ");
        }
        return sb.toString();
    }

    private String doContractorTerminationsWhereSql(ContractorSearchDto searchDto, Map<Integer, Object> paramMap) {
        StringBuilder whereSql = new StringBuilder();
        paramMap.put(1, searchDto.getFrom().toString());
        paramMap.put(2, searchDto.getTo().toString());
        paramMap.put(3, SecurityUtils.getTenantId());
        whereSql.append(" and st.termination_date between ?1 and ?2 ");
        whereSql.append(" and s.tenant_id = ?3 ");
        if (CollUtil.isNotEmpty(searchDto.getAssignmentDivisions())) {
            List<Integer> assignmentDivisionWhereList = new ArrayList<>();
            AtomicReference<Boolean> flag = new AtomicReference<>(false);
            for (String assignmentDivision : searchDto.getAssignmentDivisions()) {
                Arrays.stream(AssignmentDivision.values()).filter(division -> Objects.equals(division.name(), assignmentDivision))
                        .findFirst().ifPresentOrElse(division -> assignmentDivisionWhereList.add(division.toDbValue()), () -> {
                            flag.set(true);
                            whereSql.append(" and (tta.assignment_division is null");
                        });
            }
            if (CollUtil.isNotEmpty(assignmentDivisionWhereList)) {
                paramMap.put(paramMap.size() + 1, assignmentDivisionWhereList);
                if (flag.get()) {
                    whereSql.append(" or tta.assignment_division in ?").append(paramMap.size()).append(")");
                } else {
                    whereSql.append(" and tta.assignment_division in ?").append(paramMap.size());
                }
            } else {
                if (flag.get()) {
                    whereSql.append(" ) ");
                }
            }
        }
        if (ObjectUtil.isNotNull(searchDto.getStatus())) {
            paramMap.put(paramMap.size() + 1, searchDto.getStatus().toDbValue());
            whereSql.append(" and s.start_type = ?").append(paramMap.size());
        }
        //最加 exists 判断 talent 是否存在处于 active 的流程,如果存在则不计入统计
        paramMap.put(CollUtil.isEmpty(paramMap)? 1: paramMap.size() + 1, Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.PAY_ROLL.toDbValue()));
        whereSql.append("""
                 and not exists(
                select 1 from start ss
                where ss.talent_id = t.id and ss.tenant_id = t.tenant_id and ss.position_type in 
                """ + "(?" + paramMap.size() + ")" );
        whereSql.append("""
                and ss.talent_recruitment_process_id != s.talent_recruitment_process_id
                GROUP BY ss.talent_recruitment_process_id
                HAVING COUNT(*) = SUM(CASE WHEN ss.status IN (0,10) THEN 1 ELSE 0 END)
                )
                """);
        return whereSql.toString();
    }

}
