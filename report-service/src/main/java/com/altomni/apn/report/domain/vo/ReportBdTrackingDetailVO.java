package com.altomni.apn.report.domain.vo;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.Instant;

@Data
@Entity
public class ReportBdTrackingDetailVO {

    @Id
    private Long id;

    private Long companyId;

    private String companyName;

    private Instant contactDate;

    private String note;

    private Long clientContactId;

    private String clientContact;

    private String serviceTypeId;

    private String salesLeadOwner;

    private String salesLeadBdOwner;

}
