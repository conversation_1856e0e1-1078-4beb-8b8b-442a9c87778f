package com.altomni.apn.report.domain.vo;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;

@Data
@Entity
public class ReportBdTrackingVO {

    @Id
    private Long userId;

    private String userName;

    private Long callNum;

    private Long callClientNum;

    private Long emailNum;

    private Long emailClientNum;

    private Long newMeetingNum;

    private Long newMeetingClientNum;

    private Long formalMeetingNum;

    private Long formalMeetingClientNum;

    private Long lunchMeetingNum;

    private Long lunchMeetingClientNum;

    private Long oocMeetingNum;

    private Long oocMeetingClientNum;

    private Long oppPipelineMeetingNum;

    private Long oppPipelineMeetingClientNum;

    private Long clientVisitNum;

    private Long clientVisitClientNum;

    private Long referenceCallNum;

    private Long referenceCallClientNum;

}
