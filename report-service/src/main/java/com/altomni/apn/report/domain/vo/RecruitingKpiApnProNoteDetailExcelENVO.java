package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.report.config.excel.CustomTrackingPlatformConverter;
import com.altomni.apn.report.domain.enumeration.TrackingPlatform;
import com.altomni.apn.report.domain.enumeration.TrackingPlatformConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiApnProNoteDetailExcelENVO {

    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "Candidate Name", index = 0)
    private String fullName;

    @ExcelIgnore
    private Long talentId;

    @ExcelProperty(value = "Source Channel", index = 1, converter = CustomTrackingPlatformConverter.class)
    @Convert(converter = TrackingPlatformConverter.class)
    private TrackingPlatform sourceChannel;

    @ExcelProperty(value = "Notes", index = 2)
    private String note;

    @ExcelIgnore
    private Long jobSearchStatus;

    @ExcelProperty(value = "Job Search Status", index = 3)
    private String jobSearchStatusDisplay;

    @ExcelProperty(value = "Created By", index = 4)
    private String createdBy;

    @ExcelProperty(value = "Created Time", index = 5)
    private String lastModifiedBy;

    @ExcelIgnore
    private Instant createdDate;

    @ExcelIgnore
    private Instant lastModifiedDate;

    @ExcelIgnore
//    @ExcelProperty(value = "创建时间", index = 6)
    private String createdDateFormat;

    @ExcelIgnore
//    @ExcelProperty(value = "最近修改时间", index = 7)
    private String lastModifiedDateFormat;

}
