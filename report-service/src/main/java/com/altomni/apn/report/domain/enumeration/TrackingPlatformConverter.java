package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

/**
 * <AUTHOR>
 */
@Converter
public class TrackingPlatformConverter extends AbstractAttributeConverter<TrackingPlatform, Integer> {
    public TrackingPlatformConverter() {
        super(TrackingPlatform::toDbValue, TrackingPlatform::fromDbValue);
    }
}
