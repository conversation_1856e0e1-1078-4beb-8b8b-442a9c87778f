package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SqlUtil;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class BaseCustomRepository {

    static final String AND_SYMBOL = " AND ";

    static final String ALL_SELECT = "all";

    static final String UNION_ALL = " union all ";
    @Resource
    EntityManager entityManager;

    /**
     * 查询接口，返回泛型数据
     * @param queryStr
     * @param clazz
     * @param map
     * @return
     * @param <T>
     */
    public <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map, null);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    /**
     * 查询条件存在 in 大于1000 的条件，分片查询
     * @param key
     * @param queryStr
     * @param clazz
     * @param map
     * @return
     * @param <T>
     */
    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = new HashMap<>(map);
            paramMap.put(key, values);
            return doSearchData(queryStr, clazz, paramMap, null);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 真正的查询接口，支持分页
     * @param queryStr
     * @param clazz
     * @param map
     * @param pageable
     * @return
     * @param <T>
     */
    public <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map, Pageable pageable) {
        entityManager.clear();
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        if (pageable != null) {
            query.setFirstResult((pageable.getPageNumber() <= 0? 0: pageable.getPageNumber() - 1) * pageable.getPageSize());
            query.setMaxResults(pageable.getPageSize());
        }
        return query.getResultList();
    }

    /**
     * count 的查询方法
     * @param countQuery
     * @param map
     * @return
     */
    public long doSearchCount(String countQuery, Map<Integer, Object> map) {
        entityManager.clear();
        Query countQ = entityManager.createNativeQuery(countQuery);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(countQ, method, k, v)));
        return Long.parseLong(String.valueOf(countQ.getSingleResult()));
    }

    /**
     * 检查入参是否存在in list 的情况，并且只能有一个
     * @param map
     * @return
     */
    private Integer checkInList(Map<Integer, Object> map) {
        if (CollUtil.isEmpty(map)) {
            return null;
        }
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new RuntimeException("query sql in condition list > 1000 more than 1");
        }
        return keyList.get(0);
    }

    /**
     * 返回 map 的查询接口
     * @param queryStr
     * @param map
     * @return
     */
    public List<Map<String, Object>> searchDataWithMap(String queryStr, Map<Integer, Object> map) {
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchDataWithMap(queryStr, map);
        } else {
            return doPartitionSearchDataWithMap(key, queryStr, map);
        }
    }

    /**
     * 返回map 的分片接口
     * @param key
     * @param queryStr
     * @param map
     * @return
     */
    private List<Map<String, Object>> doPartitionSearchDataWithMap(Integer key, String queryStr, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchDataWithMap(queryStr, paramMap);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }

    /**
     * 返回map 的查询接口
     * @param queryStr
     * @param map
     * @return
     */
    public List<Map<String, Object>> doSearchDataWithMap(String queryStr, Map<Integer, Object> map) {
        entityManager.clear();
        Query query = entityManager.createNativeQuery(queryStr);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.getResultList();
    }

    /**
     * 返回map 的查询接口
     * @param queryStr
     * @param map
     * @return
     */
    public List<Map<String, Object>> doSearchDataWithNameMap(String queryStr, Map<String, Object> map) {
        entityManager.clear();
        Query query = entityManager.createNativeQuery(queryStr);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", String.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        query.unwrap(SQLQuery.class).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        return query.getResultList();
    }

    public Integer getDataScope() {
        String sql = " select data_scope from user where id = " + SecurityUtils.getUserId();
        Query query = entityManager.createNativeQuery(sql);
        return Integer.parseInt(String.valueOf(query.getSingleResult()));
    }

    public Integer getRoleDateScope() {
        String sql = " select max(r.data_scope) from user_role ur left join role r on ur.role_id = r.id where ur.user_id = " + SecurityUtils.getUserId() + " group by ur.user_id ";
        Query query = entityManager.createNativeQuery(sql);
        return Integer.parseInt(String.valueOf(query.getSingleResult()));
    }

}
