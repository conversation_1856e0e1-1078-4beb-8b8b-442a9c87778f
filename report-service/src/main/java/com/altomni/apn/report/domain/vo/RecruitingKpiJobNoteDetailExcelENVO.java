package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiJobNoteDetailExcelENVO {

    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private Long jobId;

    @ExcelIgnore
    private String jobTitle;

    @ExcelProperty(value = "Job Title(ID)", index = 0)
    private String jobTitleAndJobId;

    @ExcelProperty(value = "Note", index = 1)
    private String note;

    @ExcelProperty(value = "Created By", index = 2)
    private String createdBy;

    @ExcelProperty(value = "Last Updated By", index = 3)
    private String lastModifiedBy;

    @ExcelIgnore
    private Instant createdDate;

    @ExcelProperty(value = "Created Time", index = 4)
    private String createdDateFormat;

    @ExcelIgnore
    private Instant lastModifiedDate;

    @ExcelProperty(value = "Last Updated At", index = 5)
    private String lastModifiedDateFormat;

    public String getJobTitleAndJobId() {
        if (jobTitle == null || jobId == null) {
            return null;
        }
        return jobTitle + " (" + jobId + ")";
    }

    public String getNote() {
        if (StrUtil.isNotBlank(note)) {
            return HtmlUtil.cleanHtmlTag(note);
        }
        return note;
    }

}
