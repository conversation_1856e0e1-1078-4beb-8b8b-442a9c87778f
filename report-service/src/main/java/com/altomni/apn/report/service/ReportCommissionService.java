package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.ReportCommissionVO;
import com.altomni.apn.report.dto.ReportCommissionSearchDTO;
import org.springframework.data.domain.Page;

import javax.servlet.http.HttpServletResponse;

public interface ReportCommissionService {
    void exportCommissionReport(ReportCommissionSearchDTO dto, HttpServletResponse response);

    Page<ReportCommissionVO> searchCommissionReport(ReportCommissionSearchDTO dto);

}
