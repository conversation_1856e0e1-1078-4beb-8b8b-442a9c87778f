package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiTalentDetailVO implements Serializable, AttachConfidentialTalent {

    @Id
    private Long talentId;

    private Long talentNotes;

    private String noteType;

    private String fullName;

    private String currentCompany;

    private String currentPosition;

    private Integer jobSearchStatus;

    private Long createdBy;

    private Instant createdDate;

    private Long lastModifiedBy;

    private Instant lastModifiedDate;

    private Boolean isContact;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    @Transient
    private Boolean confidentialTalentViewAble;


    @Override
    public void encrypt() {
        this.talentNotes = null;
        this.noteType = null;
        this.fullName = null;
        this.currentCompany = null;
        this.currentPosition = null;
        this.jobSearchStatus = null;
        this.createdBy = null;
        this.createdDate = null;
        this.lastModifiedBy = null;
        this.lastModifiedDate = null;
        this.isContact = null;
    }
}
