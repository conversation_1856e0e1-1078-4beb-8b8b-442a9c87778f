package com.altomni.apn.report.dto;

import cn.hutool.core.collection.CollectionUtil;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.report.domain.vm.w1.UserCountVM;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.List;

@Data
public class LinkedinStatsDTO {

    @JsonIgnore
    private Long userId;

    private String username;

    private int totalCnt;

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public int getTotalCnt() {
        return totalCnt;
    }

    public void setTotalCnt(int totalCnt) {
        this.totalCnt = totalCnt;
    }

    public static List<LinkedinStatsDTO> castEntities(List<Object[]> dbData) {
        List<LinkedinStatsDTO> res = new ArrayList<>();

        if (dbData == null) {
            return res;
        }

        for (Object[] obs : dbData) {
            LinkedinStatsDTO dto = new LinkedinStatsDTO();
            dto.setUsername((String) obs[0]);
            dto.setTotalCnt(((BigInteger) obs[1]).intValue());
            res.add(dto);
        }

        return res;
    }

    public static List<LinkedinStatsDTO> fromUserCountVMList(List<UserCountVM> userCountVMList) {
        List<LinkedinStatsDTO> res = new ArrayList<>();

        if (CollectionUtil.isEmpty(userCountVMList)) {
            return res;
        }

        for (UserCountVM userCountVM : userCountVMList) {
            LinkedinStatsDTO linkedinStatsDTO = new LinkedinStatsDTO();
            ServiceUtils.myCopyProperties(userCountVM, linkedinStatsDTO);
            res.add(linkedinStatsDTO);
        }

        return res;
    }
}
