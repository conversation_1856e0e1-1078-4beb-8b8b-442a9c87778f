package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * quarterly onboarding
 */
public enum QuarterlyOnboardingType implements ConvertedEnum<Integer> {

    NEWHIRES(0),
    RENEWALS(1);



    private final Integer dbValue;

    QuarterlyOnboardingType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<QuarterlyOnboardingType, Integer> resolver =
        new ReverseEnumResolver<>(QuarterlyOnboardingType.class, QuarterlyOnboardingType::toDbValue);

    public static QuarterlyOnboardingType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
