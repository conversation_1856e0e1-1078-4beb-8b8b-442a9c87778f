package com.altomni.apn.report.domain;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.job.domain.enumeration.start.StartType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Convert;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RevenueDTO implements Serializable {
    private Long id;
    private String status;
    private StartType startType;
    private BigDecimal totalBillAmount;
    private Long companyId;
    private LocalDate startDate;
    private LocalDate endDate;
    private String type;
    private Integer jobType;
    private BigDecimal fromUsdRate;
    private LocalDate sStartDate;
    private LocalDate sEndDate;
    private LocalDate warrantyEndDate;
    private BigDecimal finalPayRate;
    private BigDecimal finalBillRate;
    private BigDecimal estimatedWorkingHourPerWeek;
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType rateUnitType;
    private Integer currency;
    private LocalDate onboardDate;


}
