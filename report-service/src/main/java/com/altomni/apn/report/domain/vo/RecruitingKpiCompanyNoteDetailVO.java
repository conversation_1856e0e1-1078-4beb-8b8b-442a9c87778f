package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.Instant;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiCompanyNoteDetailVO {
    @Id
    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private Long companyId;

    @ExcelProperty(index = 0, value = "Company Name")
    private String companyName;

    @ExcelIgnore
    private String contactInfo;

    @ExcelProperty(index = 1, value = "Contact Name")
    private String contactName;

    @ExcelIgnore
    private Instant contactDate;

    @ExcelIgnore
    private Long createdById;

    @ExcelIgnore
    private String contactId;

    @JsonIgnore
    @Transient
    @ExcelProperty(index = 2, value = "Contact Date")
    private String contactDateFormat;

    @ExcelProperty(index = 3, value = "Note")
    private String note;

}
