package com.altomni.apn.report.dto;

import com.altomni.apn.common.errors.BadRequestAlertException;
import com.altomni.apn.report.domain.enumeration.ReportViewType;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.openapitools.client.model.ChartDataQueryObject;
import org.springframework.util.StringUtils;

import java.io.Serializable;

@Getter
@Setter
public class ReportDTO implements Serializable {

    private Long id;
    private Long tenantId;
    private String name;
    private String description;
    private Integer datasetId;
    private ReportViewType viewType;
    private ChartDataQueryObject config;


    public void validate() {
        if (!StringUtils.hasText(name)) {
            throw new BadRequestAlertException("name cannot be empty");
        }
        if (viewType == null) {
            throw new BadRequestAlertException("A new report cannot already have an viewType");
        }
        if (datasetId == null) {
            throw new BadRequestAlertException("A new report cannot already have an datasetId");
        }

    }

}
