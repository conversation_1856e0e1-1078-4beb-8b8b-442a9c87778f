package com.altomni.apn.report.dto;

import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.report.domain.enumeration.StartReportStatus;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
public class ContractorSearchDto {

    private LocalDate from;

    private LocalDate to;

    private List<String> assignmentDivisions;

    private StartReportStatus status;

    private SearchSortDTO sort;

    private String timeZone;

}
