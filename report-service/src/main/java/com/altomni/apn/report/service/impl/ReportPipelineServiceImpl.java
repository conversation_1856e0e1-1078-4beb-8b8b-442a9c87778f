package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.ReportAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SqlUtil;
import com.altomni.apn.common.domain.enumeration.company.AccountCompanyStatus;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.report.config.env.ApplicationProperties;
import com.altomni.apn.report.config.env.ReportApiPromptProperties;
import com.altomni.apn.report.domain.enumeration.ReportCountType;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.altomni.apn.report.domain.vm.p1.ReportCountVM;
import com.altomni.apn.report.domain.vm.p1.UserIdVM;
import com.altomni.apn.report.domain.vm.p1.UserNameVM;
import com.altomni.apn.report.domain.vm.p2.CompanyNameVM;
import com.altomni.apn.report.domain.vm.p2.ReportCountByCompanyVM;
import com.altomni.apn.report.domain.vo.UserBriefVO;
import com.altomni.apn.report.domain.vo.p1.PipelineAnalyticsVO;
import com.altomni.apn.report.domain.vo.p2.PipelineAnalyticsByCompanyVO;
import com.altomni.apn.report.dto.UserTeamPariDTO;
import com.altomni.apn.report.dto.p1.PipelineAnalyticsDTO;
import com.altomni.apn.report.dto.p2.ReportPipelineParamDTO;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.ReportPipelineService;
import com.altomni.apn.report.service.ReportService;
import com.altomni.apn.report.service.company.CompanyService;
import com.altomni.apn.report.service.user.UserService;
import com.altomni.apn.report.util.ExcelUtil;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
public class ReportPipelineServiceImpl implements ReportPipelineService {

    private final Logger log = LoggerFactory.getLogger(ReportPipelineServiceImpl.class);

    private final static List<Integer> ALL_JOB_TYPES = Arrays.asList(JobType.FULL_TIME.toDbValue(), JobType.CONTRACT.toDbValue(), JobType.PAY_ROLL.toDbValue(), JobType.MSP.toDbValue(), JobType.OTHERS.toDbValue());

    @PersistenceContext
    EntityManager entityManager;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ReportApiPromptProperties reportApiPromptProperties;

    private volatile ExecutorService executorUserService;

    private volatile ExecutorService executorCompanyService;

    private final static List<ReportCountType> COUNT_BY_USER_TYPE_LIST = Arrays.asList(ReportCountType.SUBMIT_TO_JOB, ReportCountType.SUBMIT_TO_CLIENT, ReportCountType.PIPELINE_UPDATE_SUBMITED
    , ReportCountType.INTERVIEW, ReportCountType.OFFER, ReportCountType.OFFER_ACCEPT, ReportCountType.ON_BOARD);

    private final static List<ReportTableType> COUNT_BY_COMPANY_TYPE_LIST = Arrays.asList(ReportTableType.SUBMIT_TO_JOB, ReportTableType.SUBMIT_TO_CLIENT
            , ReportTableType.INTERVIEW, ReportTableType.OFFER, ReportTableType.OFFER_ACCEPT, ReportTableType.ON_BOARD);

    private final static String GRAND_TOTAL = "Grand Total";

    private final static String SEPARATOR_COMMA = ",";

    @Resource
    private UserService userService;

    @Resource
    private CompanyService companyService;

    @Resource
    private ReportService reportService;

    @Resource
    private ReportRepository reportRepository;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    @Override
    public List<PipelineAnalyticsVO> p1PipelineAnalyticsByUsers(PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        if (pipelineAnalyticsDTO.getUserRole() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORTPIPELINE_P1PIPELINEANALYTICSBYUSERS_USERROLENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }

//        List<Long> nestedTeamIds = this.reportService.getNestedTeamIds(pipelineAnalyticsDTO.getTeamIds());
//        pipelineAnalyticsDTO.setTeamIds(nestedTeamIds);
//        pipelineAnalyticsDTO.setTeamIds(nestedTeamIds);

        Set<Long> userIds = new HashSet<>();
        Set<Long> teamIds = new HashSet<>();
        if (Objects.nonNull(pipelineAnalyticsDTO.getUserId())){
            userIds.add(pipelineAnalyticsDTO.getUserId());
        }
        if (CollectionUtils.isNotEmpty(pipelineAnalyticsDTO.getTeamIds())){
            teamIds.addAll(pipelineAnalyticsDTO.getTeamIds());
        }
        boolean isValid = this.applyDataPermission(userIds, teamIds);
        log.debug("userIds = {}", userIds);
        log.debug("teamIds = {}", teamIds);
        log.debug("isValid = {}", isValid);
        if (!isValid){
            return new ArrayList<>();
        }
        pipelineAnalyticsDTO.setUserIds(userIds);
        pipelineAnalyticsDTO.setTeamIds(new ArrayList<>(teamIds));

        List<Integer> jobTypes = CollectionUtils.isNotEmpty(pipelineAnalyticsDTO.getJobTypes()) ?
                pipelineAnalyticsDTO.getJobTypes().stream().map(JobType::toDbValue).collect(Collectors.toList()) : ALL_JOB_TYPES;
        Map<Integer, Object> defaultParamMap = new ConcurrentHashMap<>(16);
        defaultParamMap.put(1,pipelineAnalyticsDTO.getFromDate());
        defaultParamMap.put(2,pipelineAnalyticsDTO.getToDate());
        defaultParamMap.put(3,SecurityUtils.getTenantId());
        defaultParamMap.put(4,pipelineAnalyticsDTO.getUserRole().toDbValue());
        defaultParamMap.put(5,jobTypes);

        CountDownLatch countDownLatch = new CountDownLatch(COUNT_BY_USER_TYPE_LIST.size());
        List<ReportCountVM> totalCountList = new CopyOnWriteArrayList<>();

        COUNT_BY_USER_TYPE_LIST.forEach(type -> getCountByUserExecutorService().execute(() -> {
                try {
                    List<ReportCountVM> itemCountList = partitionCountSearch(pipelineAnalyticsDTO, ReportCountVM.class, defaultParamMap, type);
                    CollectionUtil.addAll(totalCountList, itemCountList);
                } catch (Exception e) {
                    log.error("[APN: ReportPipelineV3] search count by user failed = [{}]", ExceptionUtils.getStackTrace(e));
                } finally {
                    countDownLatch.countDown();
                    log.info("[APN: ReportPipelineV3] search count by user succeeded, type = {}", type);
                }
            })
        );

        try {
            countDownLatch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("[APN: ReportPipelineV3] userCountDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
        }

        Map<Long, PipelineAnalyticsVO> resultMap = new HashMap<>();
        totalCountList.forEach(o -> {
            if (resultMap.containsKey(o.getUserId())) {
                resultMap.get(o.getUserId()).setCountAndIds(o);
            } else {
                PipelineAnalyticsVO pipelineAnalyticsVO = PipelineAnalyticsVO.fromReportCountVM(o);
                pipelineAnalyticsVO.setUserName(CommonUtils.formatFullNameWithBlankCheck(o.getUserFirstName(), o.getUserLastName()));
                resultMap.put(o.getUserId(), pipelineAnalyticsVO);
            }
        });

        List<PipelineAnalyticsVO> result = new ArrayList<>(resultMap.values()).stream().sorted(Comparator.comparing(PipelineAnalyticsVO::getUserId)).collect(Collectors.toList());

        List<UserBriefDTO> allUserList = userService.getAllBriefUsers().getBody();
        Set<Long> achievementUserIds = result.stream().map(PipelineAnalyticsVO::getUserId).collect(Collectors.toSet());
        if (ObjectUtil.isEmpty(pipelineAnalyticsDTO.getUserId()) && CollUtil.isEmpty(pipelineAnalyticsDTO.getTeamIds())) {
            if (allUserList != null && CollUtil.isNotEmpty(allUserList)) {
                Map<Long, UserBriefDTO> activatedWithoutAchievementUserMap = allUserList.stream().filter(o -> o.isActivated() && !achievementUserIds.contains(o.getId())).collect(Collectors.toMap(UserBriefDTO::getId, o -> o));
                activatedWithoutAchievementUserMap.forEach((key, value) -> {
                    PipelineAnalyticsVO pipelineAnalyticsVO = new PipelineAnalyticsVO();
                    pipelineAnalyticsVO.setUserId(key);
                    pipelineAnalyticsVO.setUserName(CommonUtils.formatFullNameWithBlankCheck(value.getFirstName(), value.getLastName()));
                    result.add(pipelineAnalyticsVO);
                });
            }
        } else {
            List<Long> replenishUserIds = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(pipelineAnalyticsDTO.getUserId())) {
                replenishUserIds.add(pipelineAnalyticsDTO.getUserId());
            } else if (CollUtil.isNotEmpty(pipelineAnalyticsDTO.getTeamIds())) {
                replenishUserIds.addAll(findUserIdByTeamIds(pipelineAnalyticsDTO.getTeamIds()));
            }
            if (allUserList != null && CollUtil.isNotEmpty(allUserList)) {
                Map<Long, UserBriefDTO> activatedWithoutAchievementUserMap = allUserList.stream().filter(o -> o.isActivated() && !achievementUserIds.contains(o.getId()) && replenishUserIds.contains(o.getId())).collect(Collectors.toMap(UserBriefDTO::getId, o -> o));
                activatedWithoutAchievementUserMap.forEach((key, value) -> {
                    PipelineAnalyticsVO pipelineAnalyticsVO = new PipelineAnalyticsVO();
                    pipelineAnalyticsVO.setUserId(key);
                    pipelineAnalyticsVO.setUserName(CommonUtils.formatFullNameWithBlankCheck(value.getFirstName(), value.getLastName()));
                    result.add(pipelineAnalyticsVO);
                });
            }
        }

        return result;
    }

    @Override
    public boolean applyDataPermission(Set<Long> userIdSet, Set<Long> teamIdSet){
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        boolean hasValidUsers = true;

        if (teamDataPermission.getSelf()) {
            Set<Long> userIds = new HashSet<>();
            userIds.add(SecurityUtils.getUserId());
            if (CollectionUtils.isNotEmpty(userIdSet)){
                userIds = SetUtils.intersection(userIds, userIdSet);
            }else if (CollectionUtils.isNotEmpty(teamIdSet)
                    && CollectionUtils.isEmpty(SetUtils.intersection(teamIdSet, teamDataPermission.getNestedTeamIds()))) {
                // only select teams
                hasValidUsers = false;
            }
            if (CollectionUtils.isEmpty(userIds)){
                hasValidUsers = false;
            }
            userIdSet.addAll(userIds);
        } else if (teamDataPermission.getAll()) {
            if (CollectionUtils.isEmpty(userIdSet) && CollectionUtils.isNotEmpty(teamIdSet)) {
                // only select teams
                hasValidUsers = this.hasValidUserIds(userIdSet, teamIdSet);
            }
        } else if (CollectionUtils.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            if (CollectionUtils.isNotEmpty(userIdSet)){
                // select users
                Set<Long> nestedTeamIds = teamDataPermission.getNestedTeamIds();
                Set<Long> validUserIds = reportRepository.getUserTeamPair(userIdSet)
                        .stream().filter(p -> nestedTeamIds.contains(p.getTeamId())).map(UserTeamPariDTO::getUserId).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(validUserIds)){
                    hasValidUsers = false;
                }
            } else if (CollectionUtils.isNotEmpty(teamIdSet)) {
                // only select teams
                hasValidUsers = this.hasValidUserIds(userIdSet, teamIdSet);
            }else{
                // no teams, users selected
                teamIdSet.clear();
                teamIdSet.addAll(teamDataPermission.getNestedTeamIds());
                hasValidUsers = this.hasValidUserIds(userIdSet, teamDataPermission.getNestedTeamIds());
            }
        }else {
            hasValidUsers = false;
        }
        return hasValidUsers;
    }

    private boolean hasValidUserIds(Set<Long> userIds, Set<Long> teamIds){
        PermissionTeamMemberSearchVM searchVM = new PermissionTeamMemberSearchVM();
        searchVM.setTeamIds(teamIds);
        Set<Long> userIdsByTeams = Objects.requireNonNull(userService.getPlainTeamMembersByTeamIds(searchVM).getBody())
                .stream().map(PermissionTeamMemberDTO::getId).collect(Collectors.toSet());
        userIds.addAll(userIdsByTeams);
        return CollectionUtils.isNotEmpty(userIdsByTeams);
    }

    @Override
    public void exportP1PipelineAnalyticsByUsersByExcel(PipelineAnalyticsDTO pipelineAnalyticsDTO, HttpServletResponse response) {
        List<PipelineAnalyticsVO> pipelineAnalyticsVOList = p1PipelineAnalyticsByUsers(pipelineAnalyticsDTO);
        setP1ExcelGrandTotal(pipelineAnalyticsVOList);
        ExcelUtil.downloadExcelWithGramdTotal(response, PipelineAnalyticsVO.class, pipelineAnalyticsVOList, "", "ActivitiesReportByUser.xlsx", true);
    }

    @Override
    public List<PipelineAnalyticsByCompanyVO> p2PipelineAnalyticsByCompany(ReportPipelineParamDTO reportPipelineParamDTO) {
        Set<Long> userIds = new HashSet<>();
        Set<Long> teamIds = new HashSet<>();
        if (Objects.nonNull(reportPipelineParamDTO.getUserId())){
            userIds.add(reportPipelineParamDTO.getUserId());
        }
        if (CollectionUtils.isNotEmpty(reportPipelineParamDTO.getTeamIds())){
            teamIds.addAll(reportPipelineParamDTO.getTeamIds());
        }
        boolean isValid = this.applyDataPermission(userIds, teamIds);
        log.debug("userIds = {}", userIds);
        log.debug("teamIds = {}", teamIds);
        log.debug("isValid = {}", isValid);
        if (!isValid){
            return new ArrayList<>();
        }
        reportPipelineParamDTO.setUserIds(userIds);
        reportPipelineParamDTO.setTeamIds(teamIds);


        Map<Integer, Object> defaultParamMap = new ConcurrentHashMap<>(16);
        defaultParamMap.put(1,reportPipelineParamDTO.getFromDate());
        defaultParamMap.put(2,reportPipelineParamDTO.getToDate());
        defaultParamMap.put(3,SecurityUtils.getTenantId());

        CountDownLatch countDownLatch = new CountDownLatch(COUNT_BY_COMPANY_TYPE_LIST.size());
        List<ReportCountByCompanyVM> totalCountList = new CopyOnWriteArrayList<>();

        COUNT_BY_COMPANY_TYPE_LIST.forEach(type -> getCountByCompanyExecutorService().execute(() -> {
                    try {
                        List<ReportCountByCompanyVM> itemCountList = partitionCountSearch(reportPipelineParamDTO, ReportCountByCompanyVM.class, defaultParamMap, type);
                        CollectionUtil.addAll(totalCountList, itemCountList);
                    } catch (Exception e) {
                        log.error("[APN: ReportPipelineV3] search count by company failed = [{}]", ExceptionUtils.getStackTrace(e));
                    } finally {
                        countDownLatch.countDown();
                        log.info("[APN: ReportPipelineV3] search count by company succeeded, type = {}", type);
                    }
                })
        );

        try {
            countDownLatch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("[APN: ReportPipelineV3] companyCountDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
        }

        List<Long> companyIdList = totalCountList.stream().filter(ObjectUtil::isNotEmpty).map(ReportCountByCompanyVM::getCompanyId).distinct().collect(Collectors.toList());
        List<CompanyNameVM> companyNameList = searchCompanyNameByIds(companyIdList);
        Map<Long, CompanyNameVM> companyNameMap = companyNameList.stream().collect(Collectors.toMap(CompanyNameVM::getId, CompanyNameVM -> CompanyNameVM));

        Map<Long, PipelineAnalyticsByCompanyVO> resultMap = new HashMap<>();
        totalCountList.stream().filter(ObjectUtil::isNotEmpty).forEach(o -> {
            if (resultMap.containsKey(o.getCompanyId())) {
                resultMap.get(o.getCompanyId()).setCountAndIds(o);
                if (companyNameMap.containsKey(o.getCompanyId())) {
                    CompanyNameVM companyNameVM = companyNameMap.get(o.getCompanyId());
                    resultMap.get(o.getCompanyId()).setCompany(companyNameVM.getCompanyName());
                }
            } else {
                PipelineAnalyticsByCompanyVO pipelineAnalyticsByCompanyVO = PipelineAnalyticsByCompanyVO.fromReportCountVM(o);
                if (companyNameMap.containsKey(o.getCompanyId())) {
                    CompanyNameVM companyNameVM = companyNameMap.get(o.getCompanyId());
                    pipelineAnalyticsByCompanyVO.setCompany(companyNameVM.getCompanyName());
                }
                resultMap.put(o.getCompanyId(), pipelineAnalyticsByCompanyVO);
            }
        });

        List<PipelineAnalyticsByCompanyVO> result = new ArrayList<>(resultMap.values()).stream().sorted(Comparator.comparing(PipelineAnalyticsByCompanyVO::getCompanyId)).collect(Collectors.toList());

        Set<Long> achievementCompanyIds = result.stream().map(PipelineAnalyticsByCompanyVO::getCompanyId).collect(Collectors.toSet());

        List<AccountCompanyVO> allCompanyList = companyService.getAllClientCompanyList(AccountCompanyStatus.ACTIVE, null).getBody();
        if (allCompanyList != null && CollUtil.isNotEmpty(allCompanyList)) {
            Map<Long, AccountCompanyVO> activatedWithoutAchievementCompanyMap = allCompanyList.stream().filter(o -> !achievementCompanyIds.contains(o.getId())).collect(Collectors.toMap(AccountCompanyVO::getId, o -> o));
            activatedWithoutAchievementCompanyMap.forEach((key, value) -> {
                PipelineAnalyticsByCompanyVO pipelineAnalyticsByCompanyVO = new PipelineAnalyticsByCompanyVO();
                pipelineAnalyticsByCompanyVO.setCompanyId(key);
                pipelineAnalyticsByCompanyVO.setCompany(value.getFullBusinessName());
                result.add(pipelineAnalyticsByCompanyVO);
            });
        }

        return result;
    }


    @Override
    public List<UserBriefVO> getP2UserFilter(ReportPipelineParamDTO reportPipelineParam) {
        Map<Integer, Object> defaultParamMap = new HashMap<>();
        defaultParamMap.put(1,reportPipelineParam.getFromDate());
        defaultParamMap.put(2,reportPipelineParam.getToDate());
        defaultParamMap.put(3,SecurityUtils.getTenantId());

        List<Long> userIdList = partitionCountSearch(reportPipelineParam, defaultParamMap);
        List<UserNameVM> userNameList = searchUserNameByIds(userIdList);
        return userNameList.stream().map(UserBriefVO::fromUserNameVM).collect(Collectors.toList());
    }

    @Override
    public void exportP2PipelineAnalyticsByCompanyByExcel(ReportPipelineParamDTO reportPipelineParamDTO, HttpServletResponse response) {
        List<PipelineAnalyticsByCompanyVO> p2PipelineAnalyticsVOList = p2PipelineAnalyticsByCompany(reportPipelineParamDTO);
        setP2ExcelGrandTotal(p2PipelineAnalyticsVOList);
        ExcelUtil.downloadExcelWithGramdTotal(response, PipelineAnalyticsByCompanyVO.class, p2PipelineAnalyticsVOList, "", "PipelineReportByCompany.xlsx", true);
    }

    private void setP1ExcelGrandTotal(List<PipelineAnalyticsVO> pipelineAnalyticsVOList) {
        Set<Long> appliedProcessIdSet = new HashSet<>();
        Set<Long> submittedProcessIdSet = new HashSet<>();
        Set<Long> updateSubmittedProcessIdSet = new HashSet<>();
        Set<Long> interviewProcessIdSet = new HashSet<>();
        Set<Long> offeredProcessIdSet = new HashSet<>();
        Set<Long> offerAcceptedProcessIdSet = new HashSet<>();
        Set<Long> startedProcessIdSet = new HashSet<>();
        pipelineAnalyticsVOList.forEach(o -> {
            if (ObjectUtil.isNotEmpty(o.getAppliedActivityId())) {
                Set<Long> itemAppliedProcessIdSet = Arrays.stream(o.getAppliedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
                appliedProcessIdSet.addAll(itemAppliedProcessIdSet);
            }
            if (ObjectUtil.isNotEmpty(o.getSubmittedActivityId())) {
                Set<Long> itemSubmittedProcessIdSet = Arrays.stream(o.getSubmittedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
                submittedProcessIdSet.addAll(itemSubmittedProcessIdSet);
            }
            if (ObjectUtil.isNotEmpty(o.getPipelineUpdateSubmittedActivityId())) {
                Set<Long> itemUpdateSubmittedProcessIdSet = Arrays.stream(o.getPipelineUpdateSubmittedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
                updateSubmittedProcessIdSet.addAll(itemUpdateSubmittedProcessIdSet);
            }
            if (ObjectUtil.isNotEmpty(o.getInterviewActivityId())) {
                Set<Long> itemInterviewProcessIdSet = Arrays.stream(o.getInterviewActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
                interviewProcessIdSet.addAll(itemInterviewProcessIdSet);
            }
            if (ObjectUtil.isNotEmpty(o.getOfferedActivityId())) {
                Set<Long> itemOfferedProcessIdSet = Arrays.stream(o.getOfferedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
                offeredProcessIdSet.addAll(itemOfferedProcessIdSet);
            }
            if (ObjectUtil.isNotEmpty(o.getOfferAcceptedActivityId())) {
                Set<Long> itemOfferAcceptedProcessIdSet = Arrays.stream(o.getOfferAcceptedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
                offerAcceptedProcessIdSet.addAll(itemOfferAcceptedProcessIdSet);
            }
            if (ObjectUtil.isNotEmpty(o.getStartedActivityId())) {
                Set<Long> itemStartedProcessIdSet = Arrays.stream(o.getStartedActivityId().split(SEPARATOR_COMMA)).map(Long::parseLong).collect(Collectors.toSet());
                startedProcessIdSet.addAll(itemStartedProcessIdSet);
            }
        });

        PipelineAnalyticsVO grandTotal = new PipelineAnalyticsVO();
        grandTotal.setUserName(GRAND_TOTAL);
        grandTotal.setAppliedCount(appliedProcessIdSet.size());
        grandTotal.setSubmittedCount(submittedProcessIdSet.size());
        grandTotal.setPipelineUpdateSubmittedCount(updateSubmittedProcessIdSet.size());
        grandTotal.setInterviewCount(interviewProcessIdSet.size());
        grandTotal.setOfferedCount(offeredProcessIdSet.size());
        grandTotal.setOfferAcceptedCount(offerAcceptedProcessIdSet.size());
        grandTotal.setStartedCount(startedProcessIdSet.size());
        pipelineAnalyticsVOList.add(grandTotal);
    }

    private void setP2ExcelGrandTotal(List<PipelineAnalyticsByCompanyVO> p2PipelineAnalyticsVOList) {
        PipelineAnalyticsByCompanyVO grandTotal = new PipelineAnalyticsByCompanyVO();
        grandTotal.setCompany(GRAND_TOTAL);
        p2PipelineAnalyticsVOList.forEach(o -> {
            grandTotal.setAppliedCount(grandTotal.getAppliedCount() + o.getAppliedCount());
            grandTotal.setSubmittedCount(grandTotal.getSubmittedCount() + o.getSubmittedCount());
            grandTotal.setInterviewCount(grandTotal.getInterviewCount() + o.getInterviewCount());
            grandTotal.setOfferedCount(grandTotal.getOfferedCount() + o.getOfferedCount());
            grandTotal.setOfferAcceptedCount(grandTotal.getOfferAcceptedCount() + o.getOfferAcceptedCount());
            grandTotal.setStartedCount(grandTotal.getStartedCount() + o.getStartedCount());
        });
        p2PipelineAnalyticsVOList.add(grandTotal);
    }

    private List<CompanyNameVM> searchCompanyNameByIds(List<Long> companyIdList) {
        if (CollectionUtils.isEmpty(companyIdList)) {
            return new ArrayList<>();
        }

        String selectSql ="SELECT id, full_business_name as company_name FROM company WHERE id IN (?1)";
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, companyIdList);
        List<CompanyNameVM> companyNameList = searchData(selectSql, CompanyNameVM.class, paramMap);
        if (CollectionUtils.isEmpty(companyNameList)) {
            return new ArrayList<>();
        }
        return companyNameList;
    }

    private List<UserNameVM> searchUserNameByIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        String selectSql ="SELECT id, first_name, last_name FROM `user` WHERE id IN (?1)";
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, userIdList);
        List<UserNameVM> userNameList =  searchData(selectSql, UserNameVM.class, paramMap);
        if (CollectionUtils.isEmpty(userNameList)) {
            return new ArrayList<>();
        }
        return userNameList;
    }

    private void createQueryP2UserId(StringBuilder sb, Map<Integer, Object> paramMap, ReportPipelineParamDTO reportPipelineParam) {
        sb.append("""
                SELECT DISTINCT trpu.user_id FROM talent_recruitment_process_kpi_user trpu
                	LEFT JOIN talent_recruitment_process trp ON trp.id = trpu.talent_recruitment_process_id
                	LEFT JOIN talent_recruitment_process_submit_to_job trpj ON trpj.talent_recruitment_process_id = trp.id
                    LEFT JOIN user u on u.id = trpu.user_id
                    LEFT JOIN permission_user_team ut on ut.user_id = u.id
                	WHERE trp.tenant_id = ?3 and u.activated = 1
                	AND trpj.created_date BETWEEN ?1 AND ?2
                """);

        setCountryForCompanyQuery(sb, paramMap, reportPipelineParam.getJobCountry());
        this.filterByDataPermission(sb, paramMap);
    }

    private void createQueryCountByCompany(StringBuilder sb, Map<Integer, Object> paramMap, ReportPipelineParamDTO reportPipelineParamDTO, ReportTableType type) {
        sb.append("SELECT j.company_id, GROUP_CONCAT(distinct au.talent_recruitment_process_id) activity_ids, COUNT(distinct au.talent_recruitment_process_id) count, ");
        sb.append(type.toDbValue()).append(" type	FROM ").append(type.getDbTableName());
        sb.append(" au  ");
        sb.append(" LEFT JOIN talent_recruitment_process trp ON trp.id = au.talent_recruitment_process_id   ");
        if (type.equals(ReportTableType.ON_BOARD)) {
            sb.append(" INNER JOIN talent_recruitment_process_node trpon ON trpon.talent_recruitment_process_id = trp.id and trpon.node_type=60  and trpon.node_status = 1  ");
        }
        sb.append("""
                	LEFT JOIN job j ON j.id = trp.job_id
                	WHERE trp.tenant_id = ?3
                	AND EXISTS (
                		SELECT 1 FROM talent_recruitment_process_submit_to_job apply 
                		WHERE apply.talent_recruitment_process_id = au.talent_recruitment_process_id
                		AND apply.created_date BETWEEN ?1 AND ?2
                		)
                	AND EXISTS (
                		SELECT 1 FROM talent_recruitment_process_kpi_user tru  
                       inner join user u on u.id = tru.user_id 
                	    inner JOIN permission_user_team ut ON ut.user_id = tru.user_id 
                		WHERE tru.talent_recruitment_process_id = au.talent_recruitment_process_id and u.activated = 1
                """);

        setUserRoleForCompanyQuery(sb, paramMap, reportPipelineParamDTO.getUserRole());

        setUserIdForCompanyQuery(sb, paramMap, reportPipelineParamDTO.getUserIds());

        setTeamIdsForCompanyQuery(sb, paramMap, reportPipelineParamDTO.getTeamIds());

        setLocationForCompanyQuery(sb, paramMap, reportPipelineParamDTO.getJobCountry());

        sb.append("	) GROUP BY j.company_id");
    }

    private void setUserIdForCompanyQuery(StringBuilder sb, Map<Integer, Object> paramMap, Set<Long> userIds) {
        if (CollectionUtils.isNotEmpty(userIds)){
            sb.append("		AND tru.user_id in ?");
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, userIds);
        }
    }

    private void setTeamIdsForCompanyQuery(StringBuilder sb, Map<Integer, Object> paramMap, Set<Long> teamIds) {
        if (ObjectUtil.isNotEmpty(teamIds)) {
            sb.append("		AND ut.team_id in ?");
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, teamIds);
        }
    }

    private void setUserRoleForCompanyQuery(StringBuilder sb, Map<Integer, Object> paramMap, UserRole userRole) {
        if (userRole != null) {
            sb.append("		AND tru.user_role = ?");
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, userRole.toDbValue());
        }
    }

    private void setCountryForCompanyQuery(StringBuilder sb, Map<Integer, Object> paramMap, String jobCountry) {
        if (ObjectUtil.isNotEmpty(jobCountry) && !ReportPipelineParamDTO.ALL.equals(jobCountry)) {
            sb.append("	AND EXISTS (SELECT 1 FROM job_location jl WHERE jl.job_id = trp.job_id AND jl.official_country = ?");
            sb.append(paramMap.size() + 1).append(")");
            paramMap.put(paramMap.size() + 1, jobCountry);
        }
    }

    private void filterByDataPermission(StringBuilder sb, Map<Integer, Object> paramMap) {
        List<Long> nestedTeamIds = reportService.getNestedTeamIds(new ArrayList<>());
        if (CollectionUtils.isNotEmpty(nestedTeamIds)) {
            sb.append("	AND ut.team_id in ?");
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, nestedTeamIds);
        }
    }

    private void setLocationForCompanyQuery(StringBuilder sb, Map<Integer, Object> paramMap, String jobCountry) {
        if (ObjectUtil.isNotEmpty(jobCountry) && !ReportPipelineParamDTO.ALL.equals(jobCountry)) {
            sb.append("		) \n" +
                    "	AND EXISTS (\n" +
                    "		SELECT 1 FROM job_location jl WHERE jl.job_id = trp.job_id AND jl.official_country = ?");
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, jobCountry);
        }
    }

    private void createQuerySubmitToJobCount(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        sb.append("""
                SELECT
                	u.id user_id,
                	u.first_name user_first_name,
                	u.last_name user_last_name,
                	GROUP_CONCAT( DISTINCT trp.id) activity_ids,
                	COUNT( DISTINCT trp.id ) count,
                	10 type 
                FROM
                	talent_recruitment_process_submit_to_job trj
                	INNER JOIN talent_recruitment_process trp ON trp.id = trj.talent_recruitment_process_id
                	INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id
                	LEFT JOIN user u ON u.id = tru.user_id
                	LEFT JOIN job j ON j.id = trp.job_id 
                   left join recruitment_process rp on rp.id = trp.recruitment_process_id 
                WHERE
                	tru.user_role = ?4 
                	AND trp.tenant_id = ?3
                	AND u.activated = 1 
                	AND trj.created_date BETWEEN ?1 AND ?2
                	AND rp.job_type IN ?5
                """);

        setTeamIdParams(sb, paramMap, pipelineAnalyticsDTO);

        setUserIdParams(sb, paramMap, pipelineAnalyticsDTO);

        sb.append(" GROUP BY u.id");
    }

    private void createQuerySubmitToClientCount(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        sb.append("""
                SELECT
                	u.id user_id,
                	u.first_name user_first_name,
                	u.last_name user_last_name,
                	GROUP_CONCAT( DISTINCT trp.id ) activity_ids,
                	COUNT( DISTINCT trp.id ) count, 
                	20 type 
                FROM
                	talent_recruitment_process_submit_to_client trs
                	INNER JOIN talent_recruitment_process trp ON trp.id = trs.talent_recruitment_process_id
                	INNER JOIN talent_recruitment_process_submit_to_job trj ON trj.talent_recruitment_process_id = trp.id
                	INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id
                	LEFT JOIN `user` u ON u.id = tru.user_id 
                	LEFT JOIN job j ON j.id = trp.job_id
                   left join recruitment_process rp on rp.id = trp.recruitment_process_id 
                WHERE
                	tru.user_role = ?4 
                	AND trp.tenant_id = ?3
                	AND u.activated = 1 
                	AND trj.created_date BETWEEN ?1 AND ?2
                	AND rp.job_type IN ?5
                """);

        setTeamIdParams(sb, paramMap, pipelineAnalyticsDTO);

        setUserIdParams(sb, paramMap, pipelineAnalyticsDTO);

        sb.append(" GROUP BY u.id");
    }

    private void createQueryUpdateSubmittedCount(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        sb.append("""
                SELECT
                	u.id user_id,
                	u.first_name user_first_name,
                	u.last_name user_last_name,
                	GROUP_CONCAT( DISTINCT trp.id) activity_ids,
                	COUNT( DISTINCT trp.id) count, 
                	21 type 
                FROM
                	talent_recruitment_process_submit_to_client trs
                	INNER JOIN talent_recruitment_process trp ON trp.id = trs.talent_recruitment_process_id
                	INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id
                	LEFT JOIN `user` u ON u.id = tru.user_id 
                	LEFT JOIN job j ON j.id = trp.job_id
                   left join recruitment_process rp on rp.id = trp.recruitment_process_id 
                WHERE
                	tru.user_role = ?4 
                	AND trp.tenant_id = ?3
                	AND trs.created_date BETWEEN ?1 AND ?2 
                	AND u.activated = 1 
                	AND rp.job_type IN ?5
                """);

        setTeamIdParams(sb, paramMap, pipelineAnalyticsDTO);

        setUserIdParams(sb, paramMap, pipelineAnalyticsDTO);

        sb.append(" GROUP BY u.id");
    }

    private void createQueryInterviewCount(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
       sb.append("""
               SELECT
               		u.id user_id,
               		u.first_name user_first_name,
               		u.last_name user_last_name,
               		GROUP_CONCAT( DISTINCT trp.id ) activity_ids,
               		COUNT( DISTINCT trp.id ) count, 
               	30 type 
               	FROM
               	talent_recruitment_process_interview tri
               	INNER JOIN talent_recruitment_process trp ON trp.id = tri.talent_recruitment_process_id
               	INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id
               	LEFT JOIN `user` u ON u.id = tru.user_id 
               	LEFT JOIN job j ON j.id = trp.job_id
                  left join recruitment_process rp on rp.id = trp.recruitment_process_id 
               	WHERE
               		tru.user_role = ?4 
               	    AND trp.tenant_id = ?3
               		AND tri.created_date BETWEEN ?1 AND ?2
               		AND u.activated = 1 
               		AND rp.job_type IN ?5
               """);

        setTeamIdParams(sb, paramMap, pipelineAnalyticsDTO);

        setUserIdParams(sb, paramMap, pipelineAnalyticsDTO);

        sb.append(" GROUP BY u.id");
    }

    private void createQueryOfferedCount(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
       sb.append("""
               SELECT
               		u.id user_id,
               		u.first_name user_first_name,
               		u.last_name user_last_name,
               		GROUP_CONCAT( DISTINCT trp.id ) activity_ids,
               		COUNT( DISTINCT trp.id ) count, 
               	40 type 
               	FROM
               	talent_recruitment_process_offer tro
               	INNER JOIN talent_recruitment_process trp ON trp.id = tro.talent_recruitment_process_id
               	INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id
               	LEFT JOIN `user` u ON u.id = tru.user_id
               	LEFT JOIN job j ON j.id = trp.job_id
                   left join recruitment_process rp on rp.id = trp.recruitment_process_id 
               	WHERE
               		tru.user_role = ?4 
               	    AND trp.tenant_id = ?3
               		AND tro.created_date BETWEEN ?1 AND ?2
               		AND u.activated = 1 
               		AND rp.job_type IN ?5 
               """);

        setTeamIdParams(sb, paramMap, pipelineAnalyticsDTO);

        setUserIdParams(sb, paramMap, pipelineAnalyticsDTO);

        sb.append(" GROUP BY u.id");
    }

    private void createQueryOfferedAcceptedCount(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        sb.append("""
                SELECT
                		u.id user_id,
                		u.first_name user_first_name,
                		u.last_name user_last_name,
                		GROUP_CONCAT( DISTINCT trp.id ) activity_ids,
                		COUNT( DISTINCT trp.id ) count, 
                	41 type 
                	FROM
                	talent_recruitment_process_ipg_offer_accept troac
                	INNER JOIN talent_recruitment_process trp ON trp.id = troac.talent_recruitment_process_id
                	INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id
                	LEFT JOIN `user` u ON u.id = tru.user_id 
                	LEFT JOIN job j ON j.id = trp.job_id
                   left join recruitment_process rp on rp.id = trp.recruitment_process_id 
                	WHERE
                		tru.user_role = ?4 
                	    AND trp.tenant_id = ?3
                		AND troac.created_date BETWEEN ?1 AND ?2
                		AND u.activated = 1 
                		AND rp.job_type IN ?5
                """);

        setTeamIdParams(sb, paramMap, pipelineAnalyticsDTO);

        setUserIdParams(sb, paramMap, pipelineAnalyticsDTO);

        sb.append(" GROUP BY u.id");
    }

    private void createQueryOnboardCount(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        sb.append("""
                SELECT
                		u.id user_id,
                		u.first_name user_first_name,
                		u.last_name user_last_name,
                		GROUP_CONCAT( DISTINCT trp.id ) activity_ids,
                		COUNT( DISTINCT trp.id ) count, 
                	60 type 
                	FROM
                	talent_recruitment_process_onboard trobd
                	INNER JOIN talent_recruitment_process trp ON trp.id = trobd.talent_recruitment_process_id
                   INNER JOIN talent_recruitment_process_node trpon ON trpon.talent_recruitment_process_id = trp.id and trpon.node_type=60  and trpon.node_status = 1
                	INNER JOIN talent_recruitment_process_kpi_user tru ON tru.talent_recruitment_process_id = trp.id
                	LEFT JOIN `user` u ON u.id = tru.user_id 
                	LEFT JOIN job j ON j.id = trp.job_id
                   left join recruitment_process rp on rp.id = trp.recruitment_process_id 
                	WHERE
                		tru.user_role = ?4 
                		AND trp.tenant_id = ?3
                		AND trobd.created_date BETWEEN ?1 AND ?2  
                		AND u.activated = 1 
                		AND rp.job_type IN ?5
                """);

        setTeamIdParams(sb, paramMap, pipelineAnalyticsDTO);

        setUserIdParams(sb, paramMap, pipelineAnalyticsDTO);

        sb.append(" GROUP BY u.id");
    }

    private List<Long> findUserIdByTeamIds(List<Long> teamIds) {
        String sql = "SELECT DISTINCT user_id FROM permission_user_team WHERE is_primary=1 AND team_id IN ?1";
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, teamIds);
        List<UserIdVM> userIdVMList = searchData(sql, UserIdVM.class, paramMap);
        return userIdVMList.stream().map(UserIdVM::getUserId).collect(Collectors.toList());
    }

    private void setTeamIdParams(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        if (CollectionUtils.isNotEmpty(pipelineAnalyticsDTO.getTeamIds())) {
            sb.append(" AND EXISTS(SELECT 1 FROM permission_user_team WHERE user_id = tru.user_id AND team_id IN ?");
            sb.append(paramMap.size() + 1).append(")");
            List<Long> singleTeamIdList = pipelineAnalyticsDTO.getTeamIds().stream().distinct().collect(Collectors.toList());
            paramMap.put(paramMap.size() + 1, singleTeamIdList);
        }
    }

    private void setUserIdParams(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        if (CollectionUtils.isNotEmpty(pipelineAnalyticsDTO.getUserIds())){
            sb.append(" AND u.id in ?");
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, pipelineAnalyticsDTO.getUserIds());
        }
    }

    private void setDivisionIdParams(StringBuilder sb, Map<Integer, Object> paramMap, PipelineAnalyticsDTO pipelineAnalyticsDTO) {
        if (ObjectUtil.isNotEmpty(pipelineAnalyticsDTO.getDivisionId())) {
            sb.append(" AND u.division_id = ?");
            sb.append(paramMap.size() + 1);
            paramMap.put(paramMap.size() + 1, pipelineAnalyticsDTO.getDivisionId());
        }
    }

    private <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        entityManager.clear();
        Integer key = checkInList(map);
        if (key == null) {
            return doSearchData(queryStr, clazz, map);
        } else {
            return doPartitionSearchData(key, queryStr, clazz, map);
        }
    }

    private <T> List<T> searchData(String queryStr, Map<Integer, Object> map) {
        entityManager.clear();
        Query query = entityManager.createNativeQuery(queryStr);
        map.forEach((k, v) -> query.setParameter(k, v));
        return query.getResultList();
    }

    private <T> List<T> doPartitionSearchData(Integer key, String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        return CollUtil.split((Collection<?>) map.get(key), SqlUtil.PARTITION_COUNT_999).parallelStream().map(values -> {
            Map<Integer, Object> paramMap = ObjectUtil.cloneByStream(map);
            paramMap.put(key, values);
            return doSearchData(queryStr, clazz, paramMap);
        }).flatMap(Collection::stream).collect(Collectors.toList());
    }


    private <T> List<T> doSearchData(String queryStr, Class<T> clazz, Map<Integer, Object> map) {
        Query query = entityManager.createNativeQuery(queryStr, clazz);
        Method method = ReflectUtil.getMethod(Query.class, "setParameter", Integer.class, Object.class);
        Optional.ofNullable(map).ifPresent(m -> m.forEach((k, v) -> ReflectUtil.invoke(query, method, k, v)));
        return query.getResultList();
    }

    private Integer checkInList(Map<Integer, Object> map) {
        List<Integer> keyList = map.keySet().stream().filter(k -> (map.get(k) instanceof Collection && ((Collection<?>) map.get(k)).size() > SqlUtil.PARTITION_COUNT_999)).collect(Collectors.toList());
        if (CollUtil.isEmpty(keyList)) {
            return null;
        }
        if (keyList.size() > 1) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORT_CHECKINLIST_CONDITIONLIMIT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }
        return keyList.get(0);
    }

    private <T> List<T> partitionCountSearch(PipelineAnalyticsDTO pipelineAnalyticsDTO, Class<T> clazz, Map<Integer, Object> defaultParamMap, ReportCountType type) {
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> map = new HashMap<>(16);
        map.putAll(defaultParamMap);
        switch (type) {
            case SUBMIT_TO_JOB: createQuerySubmitToJobCount(sb, map, pipelineAnalyticsDTO); break;
            case SUBMIT_TO_CLIENT: createQuerySubmitToClientCount(sb, map, pipelineAnalyticsDTO); break;
            case INTERVIEW: createQueryInterviewCount(sb, map, pipelineAnalyticsDTO); break;
            case PIPELINE_UPDATE_SUBMITED: createQueryUpdateSubmittedCount(sb, map, pipelineAnalyticsDTO); break;
            case OFFER: createQueryOfferedCount(sb, map, pipelineAnalyticsDTO); break;
            case OFFER_ACCEPT: createQueryOfferedAcceptedCount(sb, map, pipelineAnalyticsDTO); break;
            case ON_BOARD: createQueryOnboardCount(sb, map, pipelineAnalyticsDTO); break;
            default: log.error("[APN: ReportPipelineService @{}] Failed to query data : {}", SecurityUtils.getUserId(), pipelineAnalyticsDTO); break;
        }
        return searchData(sb.toString(), clazz, map);
    }

    private <T> List<T> partitionCountSearch(ReportPipelineParamDTO reportPipelineParamDTO, Class<T> clazz, Map<Integer, Object> defaultParamMap, ReportTableType type) {
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> map = new HashMap<>(16);
        map.putAll(defaultParamMap);
        createQueryCountByCompany(sb, map, reportPipelineParamDTO, type);
        return searchData(sb.toString(), clazz, map);
    }

    private <T> List<T> partitionCountSearch(ReportPipelineParamDTO reportPipelineParamDTO, Map<Integer, Object> defaultParamMap) {
        StringBuilder sb = new StringBuilder();
        Map<Integer, Object> map = new HashMap<>(16);
        map.putAll(defaultParamMap);
        createQueryP2UserId(sb, map, reportPipelineParamDTO);
        return searchData(sb.toString(), map);
    }

    private ExecutorService getCountByUserExecutorService() {
        if (executorUserService == null) {
            synchronized (ReportPipelineServiceImpl.class) {
                if (executorUserService == null) {
                    executorUserService = new ThreadPoolExecutor(
                            applicationProperties.getThreadNum(),
                            applicationProperties.getThreadNum() * 2,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-report-search-count-by-user", false));
                }
            }
        }
        return executorUserService;
    }

    private ExecutorService getCountByCompanyExecutorService() {
        if (executorCompanyService == null) {
            synchronized (ReportPipelineServiceImpl.class) {
                if (executorCompanyService == null) {
                    executorCompanyService = new ThreadPoolExecutor(
                            applicationProperties.getThreadNum(),
                            applicationProperties.getThreadNum() * 2,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-report-search-count-by-company", false));
                }
            }
        }
        return executorCompanyService;
    }
}
