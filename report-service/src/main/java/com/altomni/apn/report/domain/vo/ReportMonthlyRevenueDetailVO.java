package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.List;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportMonthlyRevenueDetailVO implements Serializable, AttachConfidentialTalent {

    @Id
    private String id;

    private String jobType;

    private String clientName;

    private BigInteger clientId;

    private BigInteger candidateId;

    private String candidateName;

    private String am;

    private String recruiter;

    private String sourcer;

    private String dm;

    private String ac;

    private String owners;

    private String salesLeadOwner;

    private String bdOwner;

    private String currency;

    private BigDecimal billRateNumber;

    private BigDecimal payRateNumber;

    private BigDecimal placementFeeNumber;

    private String rateUnitType;

    private String currencyName;

    private String onboardDate;

    private String coAm;

    @Transient
    private List<UserCountryVO> coAmList;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    @Override
    public Long getTalentId() {
        return candidateId.longValue();
    }

    @Override
    public void encrypt() {
        this.jobType = null;
        this.clientName = null;
        this.clientId = null;
        this.candidateName = null;
        this.am = null;
        this.recruiter = null;
        this.sourcer = null;
        this.currency = null;
        this.billRateNumber = null;
        this.payRateNumber = null;
        this.rateUnitType = null;
        this.currencyName = null;
        this.onboardDate = null;
    }
}
