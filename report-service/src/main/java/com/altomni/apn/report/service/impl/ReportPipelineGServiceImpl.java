package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.NodeTypeTableEnum;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportG2ApplicationVo;
import com.altomni.apn.report.domain.vo.ReportG2StatusApplicationVo;
import com.altomni.apn.report.dto.ReportPipelineParamDto;
import com.altomni.apn.report.service.ReportPipelineGService;
import com.altomni.apn.report.service.ReportPipelineService;
import com.altomni.apn.report.service.ReportService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Service("reportPipelineGService")
public class ReportPipelineGServiceImpl extends ReportBaseServiceImpl implements ReportPipelineGService {

    static final String existsKpiUserTable = " EXISTS (select 1 from talent_recruitment_process_kpi_user trpku ";
    static final String whereKpiAndApplicationId = " where trpstj.talent_recruitment_process_id = trpku.talent_recruitment_process_id and ";

    @Resource
    private ReportService reportService;

    @Resource
    private ReportPipelineService reportPipelineService;

    @Override
    public ReportG2ApplicationVo g2PipelineAnalyticsBySubmitToAM(ReportPipelineParamDto reportParam) throws ExecutionException, InterruptedException {
        log.info("[apn] g2PipelineAnalyticsBySubmitToAM start ......");
        StopWatch stopWatch = new StopWatch("g2PipelineAnalyticsBySubmitToAMTask");
        stopWatch.start("searchSqlTask");
        ReportG2ApplicationVo vo = new ReportG2ApplicationVo();
        SecurityContext context = SecurityContextHolder.getContext();

//        reportParam.setTeamIds(reportService.getNestedTeamIds(reportParam.getTeamIds()));

        boolean hasValidData = this.applyPermission(reportParam);
        if (!hasValidData){
            return vo;
        }

        CompletableFuture<ReportG2StatusApplicationVo> cf1 = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return doHandlerAllStatus(reportParam);
        });
        CompletableFuture<ReportG2StatusApplicationVo> cf2 = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return doHandlerCurrentStatus(reportParam);
        });
        vo.setApplicationAllStatus(new JSONObject(cf1.get()));
        vo.setApplicationCurrentStatus(new JSONObject(cf2.get()));
        stopWatch.stop();
        log.info("[apn] g2PipelineAnalyticsBySubmitToAM time = [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        log.info("[apn] g2PipelineAnalyticsBySubmitToAM end ......");
        return vo;
    }

    @Override
    public ReportG2StatusApplicationVo g4PipelineAnalyticsByUsers(ReportPipelineParamDto reportParam) {
        log.info("[apn] g4PipelineAnalyticsByUsers start ......");
        StopWatch currentWatch = new StopWatch("g4PipelineAnalyticsByUsers");
        currentWatch.start("searchG4PipelineTask");
//        reportParam.setTeamIds(reportService.getNestedTeamIds(reportParam.getTeamIds()));
        if (!this.applyPermission(reportParam)){
            return new ReportG2StatusApplicationVo();
        }
        List<Map<String, Object>> mapList = searchCurrentStatus(reportParam);
        currentWatch.stop();
        currentWatch.start("convertEntityTask");
        List<ReportG2StatusApplicationVo> voList = convertEntity(mapList);
        currentWatch.stop();
        log.info("[apn] g4PipelineAnalyticsByUsers end ......");
        return voList.get(0);
    }

    private boolean applyPermission(ReportPipelineParamDto reportParam){
        Set<Long> userIds = new HashSet<>();
        Set<Long> teamIds = new HashSet<>();
        if (Objects.nonNull(reportParam.getUserId())){
            userIds.add(reportParam.getUserId());
        }
        if (CollectionUtils.isNotEmpty(reportParam.getTeamIds())){
            teamIds.addAll(reportParam.getTeamIds());
        }
        boolean isValid = reportPipelineService.applyDataPermission(userIds, teamIds);
        log.debug("isValid = {}", isValid);
        log.debug("userIds = {}", userIds);
        log.debug("teamIds = {}", teamIds);
        reportParam.setUserIds(new ArrayList<>(userIds));
        reportParam.setTeamIds(new ArrayList<>(teamIds));
        return isValid;
    }

    private ReportG2StatusApplicationVo doHandlerCurrentStatus(ReportPipelineParamDto reportParam) {
        StopWatch currentWatch = new StopWatch("doHandlerCurrentStatusTask");
        ReportG2StatusApplicationVo vo = new ReportG2StatusApplicationVo();
        currentWatch.start("searchCurrentStatusTask");
        List<Map<String, Object>> mapList = searchCurrentStatusBySubmitToJob(reportParam);
        currentWatch.stop();
        currentWatch.start("convertEntityTask");
        if (CollUtil.isNotEmpty(mapList)) {
            List<ReportG2StatusApplicationVo> voList = convertEntity(mapList);
            vo = voList.get(0);
        }
        currentWatch.stop();
        log.info("[apn] g2DoHandlerAllStatus time = [{} ms] \n {} ", currentWatch.getTotalTimeMillis(), currentWatch.prettyPrint());
        return vo;
    }

    private List<ReportG2StatusApplicationVo> convertEntity(List<Map<String, Object>> resultMap) {
        ReportG2StatusApplicationVo vo = new ReportG2StatusApplicationVo();
        JSONArray jsonArray = JSONUtil.parseArray(resultMap);
        if (Objects.equals(jsonArray.stream().map(JSONUtil::parseObj).filter(json -> Objects.equals(0L, json.getLong("count", 0L))).count(), 6L)) {
            return CollUtil.newArrayList(vo);
        }
        jsonArray.stream().map(JSONUtil::parseObj).forEach(json -> {
            if (NodeTypeTableEnum.SUBMIT_TO_JOB.name().equals(json.getStr("type"))) {
                vo.setAppliedCount(json.getLong("count", 0L));
                vo.setAppliedActivityId(json.getStr("ids", null));
            }
            if (NodeTypeTableEnum.SUBMIT_TO_CLIENT.name().equals(json.getStr("type"))) {
                vo.setSubmittedCount(json.getLong("count", 0L));
                vo.setSubmittedActivityId(json.getStr("ids", null));
            }
            if (NodeTypeTableEnum.INTERVIEW.name().equals(json.getStr("type"))) {
                vo.setInterviewCount(json.getLong("count", 0L));
                vo.setInterviewActivityId(json.getStr("ids", null));
            }
            if (NodeTypeTableEnum.OFFER.name().equals(json.getStr("type"))) {
                vo.setOfferedCount(json.getLong("count", 0L));
                vo.setOfferedActivityId(json.getStr("ids", null));
            }
            if (NodeTypeTableEnum.OFFER_ACCEPT.name().equals(json.getStr("type"))) {
                vo.setOfferAcceptedCount(json.getLong("count", 0L));
                vo.setOfferAcceptedActivityId(json.getStr("ids", null));
            }
            if (NodeTypeTableEnum.ON_BOARD.name().equals(json.getStr("type"))) {
                vo.setStartedCount(json.getLong("count", 0L));
                vo.setStartedActivityId(json.getStr("ids", null));
            }
        });
        return CollUtil.newArrayList(vo);
    }

    private List<Map<String, Object>> searchCurrentStatusBySubmitToJob(ReportPipelineParamDto reportParam) {
        Long tenantId = SecurityUtils.getTenantId();
        return Arrays.stream(NodeTypeTableEnum.values()).filter(e -> !Objects.equals(NodeTypeTableEnum.COMMISSION, e)).parallel().map(nodeTypeTable -> {
                Map<Integer, Object> conditionParamMap = new HashMap<>(16);
                StringBuilder oneSb = new StringBuilder();
                oneSb.append("select count(DISTINCT trp.id) as count, GROUP_CONCAT(DISTINCT trp.id) as ids, '").append(nodeTypeTable.name()).append("' as type ")
                        .append(" from talent_recruitment_process trp ")
                        .append(" inner join ").append(nodeTypeTable.getTableName()).append(" target on target.talent_recruitment_process_id = trp.id ")
                        .append(" inner join talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = target.talent_recruitment_process_id ")
                        .append(" and trpn.node_status = 1 and trpn.node_type = ").append(nodeTypeTable.getNodeType().toDbValue())
                        .append(" inner join talent_recruitment_process_submit_to_job trpstj on trpstj.talent_recruitment_process_id = trp.id ")
                        .append(" inner join talent_recruitment_process_kpi_user kk on kk.talent_recruitment_process_id = trp.id ")
                        .append(" inner join user kkuser on kkuser.id = kk.user_id and kkuser.activated = 1 ")
                        .append(" where trp.tenant_id =?");
                conditionParamMap.put(CollUtil.isEmpty(conditionParamMap)? 1: conditionParamMap.size() + 1, tenantId);
                oneSb.append(conditionParamMap.size()).append(AND_SYMBOL)
                        .append(" exists (select 1 from recruitment_process rp where rp.id = trp.recruitment_process_id and rp.job_type in (?");
                conditionParamMap.put(conditionParamMap.size() + 1, Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.OTHERS.toDbValue(), JobType.MSP.toDbValue()));
                oneSb.append(conditionParamMap.size());
                oneSb.append(" )) ");
                oneSb.append(AND_SYMBOL);
                String sql = appendCurrentConditionSql(reportParam, oneSb, conditionParamMap);
                return searchDataWithMap(sql, conditionParamMap);
        }).flatMap(Collection::parallelStream).collect(Collectors.toList());
    }

    private List<Map<String, Object>> searchCurrentStatus(ReportPipelineParamDto reportParam) {
        Long tenantId = SecurityUtils.getTenantId();
        return Arrays.stream(NodeTypeTableEnum.values()).filter(e -> !Objects.equals(NodeTypeTableEnum.COMMISSION, e)).parallel().map(nodeTypeTable -> {
            StringBuilder oneSb = new StringBuilder();
            oneSb.append("select count(distinct trpstj.talent_recruitment_process_id) as count, GROUP_CONCAT(distinct trpstj.talent_recruitment_process_id) as ids, '").append(nodeTypeTable.name()).append("' as type ")
                    .append(" from ").append(nodeTypeTable.getTableName()).append(" trpstj ").append(" left join talent_recruitment_process trp on trp.id = trpstj.talent_recruitment_process_id ")
                    .append(" INNER JOIN talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trpstj.talent_recruitment_process_id ")
                    .append(" inner join talent_recruitment_process_kpi_user kk on kk.talent_recruitment_process_id = trpn.talent_recruitment_process_id ")
                    .append(" inner join user kkuser on kkuser.id = kk.user_id and kkuser.activated = 1 ")
                    .append(" and trpn.node_status = 1 and trpn.node_type = ").append(nodeTypeTable.getNodeType().toDbValue())
                    .append(" where trp.tenant_id = ?1 ").append(AND_SYMBOL);
            Map<Integer, Object> conditionParamMap = new HashMap<>(16);
            conditionParamMap.put(1, tenantId);
            String sql = appendCurrentConditionSql(reportParam, oneSb, conditionParamMap);
            return searchDataWithMap(sql, conditionParamMap);
        }).flatMap(Collection::parallelStream).collect(Collectors.toList());
    }

    private String appendCurrentConditionSql(ReportPipelineParamDto reportParam, StringBuilder oneSb, Map<Integer, Object> conditionParamMap) {
        doAppendConditionSql(reportParam, oneSb, conditionParamMap);
        return StrUtil.subBefore(oneSb, AND_SYMBOL, true);
    }

    private ReportG2StatusApplicationVo doHandlerAllStatus(ReportPipelineParamDto reportParam) {
        log.info("[apn] g2DoHandlerAllStatus createAllStatusSql");
        ReportG2StatusApplicationVo vo = new ReportG2StatusApplicationVo();
        StopWatch doHandlerAllStatusWatch = new StopWatch("doHandlerAllStatusTask");
        doHandlerAllStatusWatch.start("createAllStatusSqlTask");
        Map<Integer, Object> conditionParamMap = new HashMap<>(16);
        StringBuilder sb = new StringBuilder();
        createAllStatusSql(sb, reportParam, conditionParamMap);
        doHandlerAllStatusWatch.stop();
        doHandlerAllStatusWatch.start("searchTask");
        String sql = sb.toString();
        log.info("[apn] g2DoHandlerAllStatus sql = [{}] ", sql);
        List<ReportG2StatusApplicationVo> allStatusApplicationVoList = searchData(sql, ReportG2StatusApplicationVo.class, conditionParamMap);
        if (CollUtil.isNotEmpty(allStatusApplicationVoList)) {
            vo = allStatusApplicationVoList.get(0);
        }
        doHandlerAllStatusWatch.stop();
        log.info("[apn] g2DoHandlerAllStatus time = [{} ms] \n {} ", doHandlerAllStatusWatch.getTotalTimeMillis(), doHandlerAllStatusWatch.prettyPrint());
        return vo;
    }

    private void createAllStatusSql(StringBuilder sb, ReportPipelineParamDto reportParam,  Map<Integer, Object> conditionParamMap) {
        StringBuilder conditionSql = new StringBuilder();
        StringBuilder tableSql = new StringBuilder();
        sb.append(" select trp.tenant_id as tenant_id, count(DISTINCT trpstj.talent_recruitment_process_id) as applied_count, count(DISTINCT trpstc.talent_recruitment_process_id) as submitted_count, count(distinct trpi.talent_recruitment_process_id) as interview_count, ")
                .append(" count(DISTINCT trpo.talent_recruitment_process_id) as offered_count, count(DISTINCT trpioa.talent_recruitment_process_id) as offer_accepted_count, count(DISTINCT trpon.talent_recruitment_process_id) as started_count, ")
                .append(" GROUP_CONCAT(distinct trpstj.talent_recruitment_process_id) as applied_activity_id, GROUP_CONCAT(distinct trpstc.talent_recruitment_process_id) as submitted_activity_id, ")
                .append(" GROUP_CONCAT(distinct trpi.talent_recruitment_process_id) as interview_activity_id, GROUP_CONCAT(distinct trpo.talent_recruitment_process_id) as offered_activity_id, ")
                .append(" GROUP_CONCAT(distinct trpioa.talent_recruitment_process_id) as offer_accepted_activity_id, GROUP_CONCAT(distinct trpon.talent_recruitment_process_id) as started_activity_id ");
        tableSql.append(" from talent_recruitment_process_submit_to_job trpstj ")
                .append(" inner join talent_recruitment_process_kpi_user kk on kk.talent_recruitment_process_id = trpstj.talent_recruitment_process_id ")
                .append(" inner join user kkuser on kkuser.id = kk.user_id and kkuser.activated = 1 ")
                .append(" LEFT JOIN talent_recruitment_process trp on trp.id = trpstj.talent_recruitment_process_id ")
                .append(" LEFT JOIN job j on j.id = trp.job_id ")
                .append(" LEFT JOIN recruitment_process rp on rp.id = j.recruitment_process_id ")
                .append(" LEFT JOIN talent_recruitment_process_submit_to_client trpstc ON trpstj.talent_recruitment_process_id = trpstc.talent_recruitment_process_id ")
                .append(" LEFT JOIN talent_recruitment_process_interview trpi ON trpstj.talent_recruitment_process_id = trpi.talent_recruitment_process_id ")
                .append(" LEFT JOIN talent_recruitment_process_offer trpo ON trpstj.talent_recruitment_process_id = trpo.talent_recruitment_process_id ")
                .append(" LEFT JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpstj.talent_recruitment_process_id = trpioa.talent_recruitment_process_id ")
                .append(" LEFT JOIN talent_recruitment_process_node trpon ON trpon.talent_recruitment_process_id = trpstj.talent_recruitment_process_id and trpon.node_type=60  and trpon.node_status = 1");
        conditionSql.append(" where trp.tenant_id = ?1 AND rp.job_type in (?2) AND ");
        conditionParamMap.put(1, SecurityUtils.getTenantId());
        conditionParamMap.put(2, Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.OTHERS.toDbValue(), JobType.MSP.toDbValue()));
        appendAllStatusConditionSql(sb, tableSql, conditionSql, conditionParamMap, reportParam);
    }

    private void appendAllStatusConditionSql(StringBuilder sb, StringBuilder tableSql, StringBuilder conditionSql, Map<Integer, Object> conditionParamMap, ReportPipelineParamDto reportParam) {
        doAppendConditionSql(reportParam, conditionSql, conditionParamMap);
        String resultSql = StrUtil.subBefore(conditionSql, AND_SYMBOL, true);
        sb.append(tableSql).append(resultSql);
    }

    private void doAppendConditionSql(ReportPipelineParamDto reportParam, StringBuilder oneSb, Map<Integer, Object> conditionParamMap) {
        if (StrUtil.isNotBlank(reportParam.getFromDate()) && StrUtil.isNotBlank(reportParam.getToDate())) {
            String fromDate = DateUtil.formatToDate(reportParam.getFromDate());
            String toDate = DateUtil.formatToDate(reportParam.getToDate());
            conditionParamMap.put(conditionParamMap.size() + 1, fromDate);
            oneSb.append(" trpstj.created_date BETWEEN ?")
                    .append(conditionParamMap.size())
                    .append(AND_SYMBOL).append("?");
            conditionParamMap.put(conditionParamMap.size() + 1, toDate);
            oneSb.append(conditionParamMap.size()).append(AND_SYMBOL);
        }

        if (CollUtil.isNotEmpty(reportParam.getTeamIds())) {
            oneSb.append(existsKpiUserTable)
                    .append(" inner join permission_user_team tu on tu.user_id = trpku.user_id ")
                    .append(" inner join user u on u.id = tu.user_id ")
                    .append(whereKpiAndApplicationId)
                    .append(" tu.team_id in ?");
            conditionParamMap.put(conditionParamMap.size() + 1, reportParam.getTeamIds());
            oneSb.append(conditionParamMap.size()).append(" ) ").append(AND_SYMBOL);
        }
//        Long userId = this.reportService.getUserIdFromDataPermission(reportParam.getUserId());

        if (CollectionUtils.isNotEmpty(reportParam.getUserIds())){
            oneSb.append(existsKpiUserTable)
                    .append(whereKpiAndApplicationId)
                    .append(" trpku.user_id in ?");
            conditionParamMap.put(conditionParamMap.size() + 1, reportParam.getUserIds());
            oneSb.append(conditionParamMap.size());
            if (Objects.nonNull(reportParam.getUserRoles()) && reportParam.getUserRoles().length > 0){
                oneSb.append(AND_SYMBOL).append(" trpku.user_role in ?");
                conditionParamMap.put(conditionParamMap.size() + 1, Arrays.stream(reportParam.getUserRoles()).map(UserRole::toDbValue).collect(Collectors.toList()));
                oneSb.append(conditionParamMap.size());
            }
            oneSb.append(" ) ").append(AND_SYMBOL);
        }
    }

}
