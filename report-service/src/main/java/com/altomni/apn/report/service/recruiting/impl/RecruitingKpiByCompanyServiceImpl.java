package com.altomni.apn.report.service.recruiting.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.domain.dict.EnumMotivation;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.*;
import com.altomni.apn.report.repository.RecruitingKpiCompanyExtendRepository;
import com.altomni.apn.report.repository.ReportCompanyRepository;
import com.altomni.apn.report.service.company.CompanyService;
import com.altomni.apn.report.service.recruiting.RecruitingKpiByCompanyService;
import com.altomni.apn.report.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Slf4j
@Service("recruitingKpiByCompanyService")
public class RecruitingKpiByCompanyServiceImpl extends RecruitingKpiBaseServiceImpl implements RecruitingKpiByCompanyService {

    @Resource
    private RecruitingKpiCompanyExtendRepository recruitingKpiCompanyExtendRepository;

    @Resource
    private CompanyService companyService;

    @Resource
    private RecruitingKpiByCompanyService self;

    @Override
    public Page<RecruitingKpiJobDetailVO> searchJobDetailPage(RecruitingKpiJobDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        //获取是有职位
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        //只有传jobId 的时候才使用,唯一的情况就是使用了 groupBy job 的时候
        CompletableFuture<List<RecruitingKpiJobDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiCompanyExtendRepository.searchJobDetailList(searchDto, pageable);
        }, executorService);

        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiCompanyExtendRepository.searchJobDetailCount(searchDto);
            }, executorService);
        }
        List<RecruitingKpiJobDetailVO> voList = dataFuture.join();
        if (teamDTO != null) {
            voList.forEach(vo -> {
                boolean isPrivateJob = Objects.equals(teamDTO.getTeamIdForPrivateJob(), vo.getPteamId());
                // 职位是否是私有职位
                vo.setPrivateJob(isPrivateJob);
                // 私有职位是否可见
                if (isPrivateJob) {
                    vo.setPrivateJobViewable(searchDto.getPrivateJobIds().contains(vo.getJobId()));
                }
                // 不可见私有职位，把关键信息隐藏
                if (isPrivateJob && !vo.getPrivateJobViewable()) {
                    vo.encrypt();
                }
            });
        }
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportJobDetailList(RecruitingKpiJobDetailSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("exportJobDetailList");
        stopWatch.start("1. search data");
        List<RecruitingKpiJobDetailVO> voList = searchJobDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        List<RecruitingKpiJobDetailENVO> list = new ArrayList<>();
        voList.forEach(vo -> {
            try {
                RecruitingKpiJobDetailENVO detailVo = new RecruitingKpiJobDetailENVO();
                BeanUtil.copyProperties(vo, detailVo, true);
                if (ObjectUtil.isNotEmpty(vo.getPostingDate()) && StrUtil.isNotBlank(searchDto.getTimezone())) {
                    ReflectUtil.setFieldValue(detailVo, "postTingTimeFormat", formatDate(vo.getPostingDate(), searchDto.getTimezone(), "yyyy-MM-dd"));
                }
                if (ObjectUtil.isNotEmpty(vo.getOpenDate()) && StrUtil.isNotBlank(searchDto.getTimezone())) {
                    ReflectUtil.setFieldValue(detailVo, "openTimeFormat", formatDate(vo.getOpenDate(), searchDto.getTimezone(), "yyyy-MM-dd"));
                }
                if (ObjectUtil.isNotEmpty(vo.getTenantWebsitePostingDate()) && StrUtil.isNotBlank(searchDto.getTimezone())) {
                    ReflectUtil.setFieldValue(detailVo, "tenantWebsitePostingDateFormat", formatDate(vo.getTenantWebsitePostingDate(), searchDto.getTimezone(), "yyyy-MM-dd"));
                }
                if (vo.getContractDuration() != null){
                    detailVo.setContractDuration(vo.getContractDuration() + " Days");
                }
                if (vo.isPrivateJob() && !vo.getPrivateJobViewable()) {
                    vo.maskConfidentialTalentData();
                }
                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportJobDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, RecruitingKpiJobDetailENVO.class, list, "",  "JobDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportJobDetailList by company time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @ProcessConfidentialTalent
    @Override
    public Page<RecruitingKpiTalentDetailVO> searchTalentDetailPage(RecruitingKpiTalentDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<RecruitingKpiTalentDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiCompanyExtendRepository.searchTalentDetailList(searchDto, pageable);
        }, executorService);
        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiCompanyExtendRepository.searchTalentDetailCount(searchDto);
            }, executorService);
        }
        List<RecruitingKpiTalentDetailVO> voList = dataFuture.join();
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportTalentDetailList(RecruitingKpiTalentDetailSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("exportTalentDetailListByCompany");
        stopWatch.start("1. search data");
        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        Map<Integer, EnumMotivation> enumMotivationMap = enumMotivationService.findAllEnumMotivation().stream().collect(Collectors.toMap(EnumMotivation::getId, a -> a));
        List<RecruitingKpiTalentDetailVO> voList = self.searchTalentDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        List<RecruitingKpiTalentDetailENVO> list = new ArrayList<>();
        Map<Long, UserBriefDTO> map = userMapFuture.join();
        voList.forEach(vo -> {
            try {
                RecruitingKpiTalentDetailENVO detailVo = new RecruitingKpiTalentDetailENVO();
                BeanUtil.copyProperties(vo, detailVo, true);
                setCreatedAndLastModified(vo.getCreatedBy(), vo.getCreatedDate(), vo.getLastModifiedBy(), vo.getLastModifiedDate(), searchDto.getTimezone(), detailVo, map);
                setJobSearchStatusDisplay(enumMotivationMap, vo.getJobSearchStatus(), detailVo, searchDto.getType());
                if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                    ExcelUtil.maskConfidentialTalentData(detailVo);
                }
                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportTalentDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, RecruitingKpiTalentDetailENVO.class, list, "",  "talentDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportTalentDetailList by company time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @ProcessConfidentialTalent
    @Override
    public Page<? extends RecruitingKpiApplicationBaseDetailVO> searchApplicationDetailPage(RecruitingKpiApplicationDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<? extends RecruitingKpiApplicationBaseDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiCompanyExtendRepository.searchApplicationDetailList(searchDto, pageable);
        }, executorService);
        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiCompanyExtendRepository.searchApplicationDetailCount(searchDto);
            }, executorService);
        }
        List<? extends RecruitingKpiApplicationBaseDetailVO> voList = dataFuture.join();
        voList.parallelStream().forEach(vo -> {
            if (searchDto.getPermissionRespDTO() != null && Objects.equals(searchDto.getPermissionRespDTO().getTeamIdForPrivateJob(), vo.getPteamId())) {
                vo.setPrivateJob(true);
            }
        });
        if (teamDTO != null) {
            voList.forEach(vo -> {
                boolean isPrivateJob = Objects.equals(teamDTO.getTeamIdForPrivateJob(), vo.getPteamId());
                // 职位是否是私有职位
                vo.setPrivateJob(isPrivateJob);
                // 私有职位是否可见
                if (isPrivateJob) {
                    vo.setPrivateJobViewable(searchDto.getPrivateJobIds().contains(vo.getJobId()));
                }
                // 不可见私有职位，把关键信息隐藏
                if (isPrivateJob && !vo.getPrivateJobViewable()) {
                    vo.encryptPrivateJob();
                }
            });
        }
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportApplicationDetailList(RecruitingKpiApplicationDetailSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("exportApplicationDetailListByCompany");
        stopWatch.start("1. search data");
        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        List<? extends RecruitingKpiApplicationBaseDetailVO> voList = self.searchApplicationDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        Map<Long, UserBriefDTO> map = userMapFuture.join();
        Class<?> clazz = APPLICATION_EXCEL_CLASS_MAP.get(searchDto.getReportApplicationStatus());
        List<Object> list = new ArrayList<>();

        List<EnumDictDTO> countryList = companyService.findAllCountry().getBody();
        Map<String, String> countryMap = countryList.stream().collect(Collectors.toMap(EnumDictDTO::getId, EnumDictDTO::getName));

        List<UserBriefDTO> allUserList = userService.getAllBriefUsers().getBody();
        Map<Long, String> userMap = allUserList.stream().collect(Collectors.toMap(UserBriefDTO::getId, UserBriefDTO::getFullName));
        voList.forEach(vo -> {
            try {
                Object o = clazz.newInstance();
                vo.setAm(getUserNameByMap(vo.getAm(), map));
                vo.setRecruiter(getUserNameByMap(vo.getRecruiter(), map));
                vo.setSourcer(getUserNameByMap(vo.getSourcer(), map));
                vo.setAc(getUserNameByMap(vo.getAc(), map));
                vo.setDm(getUserNameByMap(vo.getDm(), map));
                vo.setOwner(getUserNameByMap(vo.getOwner(), map));
                vo.setSalesLeadOwner(getUserNameByMap(vo.getSalesLeadOwner(), map));
                vo.setBdOwner(getUserNameByMap(vo.getBdOwner(), map));
                vo.setLastModifiedBy(getUserNameByMap(vo.getLastModifiedBy(), map));

                if (BooleanUtils.isTrue(vo.getResigned())){
                    vo.setWorkflowStatus(MyCandidateStatusFilter.OFF_BOARDED);
                }

                if (CollUtil.isNotEmpty(vo.getCoAmList())) {
                    List<UserCountryVO> coAmList = new ArrayList<>();
                    vo.getCoAmList().forEach(v -> {
                        if (userMap.containsKey(v.getUserId())) {
                            UserCountryVO countryVO = new UserCountryVO();
                            countryVO.setUserName(userMap.get(v.getUserId()));
                            countryVO.setUserId(v.getUserId());
                            if (countryMap.containsKey(v.getCountryId().toString())) {
                                countryVO.setCountryName(countryMap.get(v.getCountryId().toString()));
                            }
                            coAmList.add(countryVO);
                        }
                    });
                    if (!coAmList.isEmpty()) {
                        List<String> coAmStr = new ArrayList<>();
                        Map<Long, List<UserCountryVO>> userCountryMap = coAmList.stream().collect(Collectors.groupingBy(b -> b.getUserId()));
                        for (Map.Entry<Long, List<UserCountryVO>> entry : userCountryMap.entrySet()) {
                            List<UserCountryVO> userCountryVOList = entry.getValue();
                            List<String> country = new ArrayList<>();
                            userCountryVOList.forEach(v -> {
                                if (country.isEmpty()) {
                                    country.add(v.getCountryName());
                                } else {
                                    country.add("," + v.getCountryName());
                                }
                            });
                            if (country.isEmpty()) {
                                coAmStr.add(userCountryVOList.get(0).getUserName());
                            } else {
                                coAmStr.add(userCountryVOList.get(0).getUserName() + "(" + String.join(StrUtil.COMMA, country) + ")");
                            }
                        }
                        if (!coAmStr.isEmpty()) {
                            vo.setCoAm(String.join(StrUtil.COMMA, coAmStr));
                        }
                    }
                }

                BeanUtil.copyProperties(vo, o);
                if (vo.getSubmitDate() != null) {
                    ReflectUtil.setFieldValue(o, "submitDateFormat",  formatDate(vo.getSubmitDate(), searchDto.getTimezone(), "yyyy-MM-dd"));
                }
                if (vo.getLastModifiedDate() != null) {
                    ReflectUtil.setFieldValue(o, "lastModifiedDateFormat", formatDate(vo.getLastModifiedDate(), searchDto.getTimezone(), "yyyy-MM-dd HH:mm:ss"));
                }
                if (vo.getConfidentialTalentViewAble() != null && !vo.getConfidentialTalentViewAble()) {
                    ExcelUtil.maskConfidentialTalentData(o);
                }
                if (vo.isPrivateJob() && !vo.getPrivateJobViewable()) {
                    ReflectUtil.invoke(o, "setCompanyName", "***");
                }
                list.add(o);
            } catch (Exception e) {
                log.info("exportApplicationDetailListByCompany error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, ExcelUtil.getTableHeaders(clazz, new ArrayList<>()), ExcelUtil.convertToMap(list, clazz, new ArrayList<>()), "",  "applicationDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportApplicationDetailList by company time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @Override
    public Page<RecruitingKpiJobNoteDetailVO> searchJobNoteDetailPage(RecruitingKpiJobNoteDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag) {
        TeamDataPermissionRespDTO teamDTO = getPermissionDTOAndSetCommonParam(searchDto);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<RecruitingKpiJobNoteDetailVO>> dataFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return recruitingKpiCompanyExtendRepository.searchJobNoteDetailList(searchDto, pageable, teamDTO);
        }, executorService);

        CompletableFuture<Long> countFuture = CompletableFuture.supplyAsync(() -> 0L);
        if (withCountFlag) {
            countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return recruitingKpiCompanyExtendRepository.searchJobNoteDetailCount(searchDto, teamDTO);
            }, executorService);
        }
        List<RecruitingKpiJobNoteDetailVO> voList = dataFuture.join();
        if (withCountFlag) {
            voList.parallelStream().forEach(vo -> {
                boolean isPrivateJob = Objects.equals(teamDTO.getTeamIdForPrivateJob(), vo.getPteamId());
                // 职位是否是私有职位
                vo.setPrivateJob(isPrivateJob);
                // 私有职位是否可见
                if (isPrivateJob) {
                    vo.setPrivateJobViewable(searchDto.getPrivateJobIds().contains(vo.getJobId()));
                }
                // 不可见私有职位，把关键信息隐藏
                if (isPrivateJob && !vo.getPrivateJobViewable()) {
                    vo.encrypt();
                }
            });
        }
        return new PageImpl<>(voList, Pageable.unpaged(), countFuture.join());
    }

    @Override
    public void exportJobNoteDetailList(RecruitingKpiJobNoteDetailSearchDto searchDto, HttpServletResponse response) {
        StopWatch stopWatch = new StopWatch("exportJobNoteDetailList");
        stopWatch.start("1. search data");
        CompletableFuture<Map<Long, UserBriefDTO>> userMapFuture = getUserMapFuture();
        List<RecruitingKpiJobNoteDetailVO> voList = searchJobNoteDetailPage(searchDto, null, false).getContent();
        stopWatch.stop();
        stopWatch.start("2. 实体转换");
        Map<Long, UserBriefDTO> userMap = userMapFuture.join();
        List<RecruitingKpiJobNoteDetailExcelENVO> list = new ArrayList<>();
        voList.forEach(vo -> {
            try {
                RecruitingKpiJobNoteDetailExcelENVO detailVo = new RecruitingKpiJobNoteDetailExcelENVO();
                BeanUtil.copyProperties(vo, detailVo, true);
                setCreatedAndLastModified(vo.getCreatedBy(), vo.getCreatedDate(), vo.getLastModifiedBy(), vo.getLastModifiedDate(), searchDto.getTimezone(), detailVo, userMap);
                if (vo.isPrivateJob() && !vo.getPrivateJobViewable()) {
                    vo.maskConfidentialTalentData();
                }
                list.add(detailVo);
            } catch (Exception e) {
                log.error("exportJobNoteDetailList error {} ", ExceptionUtil.getAllExceptionMsg(e));
            }
        });
        stopWatch.stop();
        stopWatch.start("3. 输出 excel");
        ExcelUtil.downloadExcel(response, RecruitingKpiJobNoteDetailExcelENVO.class, list, "",  "jobNoteDetail.xlsx", false);
        stopWatch.stop();
        log.info(" exportJobNoteDetailList by user time = {} ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
    }

    @Override
    public List<String> getKpiReportCountry() {
        return recruitingKpiCompanyExtendRepository.getKpiReportCountry(SecurityUtils.getTenantId());
    }

    @Override
    public Page<RecruitingKpiCompanyNoteDetailVO> searchCompanyNoteDetailPage(RecruitingKpiCompanyNoteDetailSearchDto searchDto, Pageable pageable) {
        List<RecruitingKpiCompanyNoteDetailVO> voList = recruitingKpiCompanyExtendRepository.searchCompanyNoteDetailList(searchDto, pageable);
        Long count = recruitingKpiCompanyExtendRepository.searchCompanyNoteDetailCount(searchDto);
        return new PageImpl<>(voList, Pageable.unpaged(), count);
    }

    @Override
    public void exportCompanyNoteDetailList(RecruitingKpiCompanyNoteDetailSearchDto searchDto, HttpServletResponse response) {
        List<RecruitingKpiCompanyNoteDetailVO> voList = recruitingKpiCompanyExtendRepository.searchCompanyNoteDetailList(searchDto, null);
        for (RecruitingKpiCompanyNoteDetailVO vo : voList) {
            vo.setNote(vo.getNote().replaceAll("<[^>]+>", ""));
            if (null != vo.getContactDate()) {
                vo.setContactDateFormat(DateUtil.fromInstantToDate(vo.getContactDate()));
            }
        }
        ExcelUtil.downloadExcel(response, RecruitingKpiCompanyNoteDetailVO.class, voList, "", "companyNoteDetail.xlsx", false);
    }

}
