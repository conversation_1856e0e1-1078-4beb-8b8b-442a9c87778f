package com.altomni.apn.report.domain.vo;

import com.altomni.apn.report.dto.BDCountDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BDActivityReportVO implements Serializable {

    private String username;

    private Long id;

    private List<BDCountDTO> bdCountDTOList;

    public BDActivityReportVO(String username, Long id) {
        this.username = username;
        this.id = id;
    }
}
