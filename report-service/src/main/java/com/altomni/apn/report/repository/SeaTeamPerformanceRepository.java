package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.SeaTeamPerformanceVO;
import com.altomni.apn.report.dto.SeaTeamPerformanceSearchDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class SeaTeamPerformanceRepository extends BaseCustomRepository {

    public Map<Long, Integer> queryNewJobsCount(SeaTeamPerformanceSearchDto dto, TeamDataPermissionRespDTO permissionDTO) {
        StopWatch stopWatch = new StopWatch("searchNewJobCountTask");
        stopWatch.start();
        String sql = """
                SELECT put.user_id, count(distinct j.id) AS newJobs
                FROM job j
                         INNER JOIN job_user_relation jul ON jul.job_id = j.id
                         INNER JOIN permission_user_team put ON put.user_id = jul.user_id and put.is_primary = 1
                         INNER JOIN permission_team pt ON pt.id = put.team_id
                         INNER JOIN user u ON u.id = jul.user_id
                WHERE j.tenant_id = :tenantId
                  AND u.activated = 1
                  AND %s BETWEEN :startDate AND :endDate
                  AND jul.role = 1
                  {dataPermission}
                GROUP BY put.user_id;
                """.formatted(convertTz(dto.getTimezone(), "j.created_date"));
        Map<String, Object> param = buildParamMap(dto);

        String formatSql = formatDataPermissionSql(dto, permissionDTO, sql, param);
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatSql, param);
        stopWatch.stop();
        log.info("searchNewJobCountTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapResult(mapList, "newJobs");
    }

    public List<Map<String, Object>> queryNewJobsDetail(SeaTeamPerformanceSearchDto dto, TeamDataPermissionRespDTO permissionDTO) {
        StopWatch stopWatch = new StopWatch("searchNewJobDetailTask");
        stopWatch.start();
        String sql = """
                SELECT
                       j.id                                                                   AS jobId,
                       j.title                                                                AS jobTitle,
                       j.pteam_id                                                             AS jobPTeamId,
                       c.id                                                                   AS companyId,
                       c.full_business_name                                                   AS companyName,
                       CASE j.status
                           WHEN 0 THEN 'Open'
                           WHEN 2 THEN 'ON Hold'
                           WHEN 4 THEN 'Closed'
                           WHEN 5 THEN 'Filled'
                           WHEN 9 THEN 'Lost'
                           ELSE 'Other' END                                                   AS jobStatus,
                       CASE rp.job_type
                           WHEN 3 THEN 'General Recruiting (FTE)'
                           WHEN 1 THEN 'General Staffing (Contract)'
                           WHEN 9 THEN 'MSP'
                           WHEN 5 THEN 'Payrolling' END                                       AS jobType,
                       j.openings                                                             AS jobOpenings
                FROM job j
                         INNER JOIN job_user_relation jul on jul.job_id = j.id
                         INNER JOIN permission_user_team put ON put.user_id = jul.user_id and put.is_primary = 1
                         INNER JOIN permission_team pt ON pt.id = put.team_id
                         INNER JOIN company c ON j.company_id = c.id
                         INNER JOIN recruitment_process rp on j.recruitment_process_id = rp.id
                         INNER JOIN user u ON u.id = jul.user_id
                WHERE j.tenant_id = :tenantId
                   AND %s BETWEEN :startDate AND :endDate
                   AND u.activated = 1
                   AND jul.role = 1
                   {dataPermission}
                """.formatted(convertTz(dto.getTimezone(), "j.created_date"));
        Map<String, Object> param = buildParamMap(dto);

        String formatSql = formatDataPermissionSql(dto, permissionDTO, sql, param);
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatSql, param);
        stopWatch.stop();
        log.info("searchNewJobDetailTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapList;
    }

    public Map<Long, Integer> queryAssignedJobsCount(SeaTeamPerformanceSearchDto dto, TeamDataPermissionRespDTO permissionDTO) {
        StopWatch stopWatch = new StopWatch("searchAssignedJobCountTask");
        stopWatch.start();
        String sql = """
                SELECT put.user_id, count(distinct j.id) AS assignedJobs
                FROM job j
                         INNER JOIN user_job_relation ujl ON ujl.job_id = j.id AND ujl.status = 1
                         INNER JOIN job_user_relation jul on jul.job_id = j.id AND jul.user_id = ujl.user_id
                         INNER JOIN permission_user_team put ON put.user_id = ujl.user_id and put.is_primary = 1
                         INNER JOIN permission_team pt ON pt.id = put.team_id
                         INNER JOIN user u ON u.id = ujl.user_id
                WHERE j.tenant_id = :tenantId
                  AND %s BETWEEN :startDate AND :endDate
                  AND u.activated = 1
                  AND jul.role = 2
                  {dataPermission}
                GROUP BY put.user_id;
                """.formatted(convertTz(dto.getTimezone(), "ujl.created_date"));
        Map<String, Object> param = buildParamMap(dto);
        String formatSql = formatDataPermissionSql(dto, permissionDTO, sql, param);
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatSql, param);
        stopWatch.stop();
        log.info("searchAssignedJobCountTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapResult(mapList, "assignedJobs");
    }

    public List<Map<String, Object>> queryAssignedJobsDetail(SeaTeamPerformanceSearchDto dto, TeamDataPermissionRespDTO permissionDTO) {
        StopWatch stopWatch = new StopWatch("searchAssignedJobDetailTask");
        String timezoneField = convertTz(dto.getTimezone(), "ujl.created_date");
        stopWatch.start();
        String sql = """
                SELECT j.id                                                                   AS jobId,
                       j.title                                                                AS jobTitle,
                       j.pteam_id                                                             AS jobPTeamId,
                       c.id                                                                   AS companyId,
                       c.full_business_name                                                   AS companyName,
                       CASE j.status
                          WHEN 0 THEN 'Open'
                          WHEN 2 THEN 'ON Hold'
                          WHEN 4 THEN 'Closed'
                          WHEN 5 THEN 'Filled'
                          WHEN 9 THEN 'Lost'
                          ELSE 'Other' END                                                    AS jobStatus,
                       CASE rp.job_type
                           WHEN 3 THEN 'General Recruiting (FTE)'
                           WHEN 1 THEN 'General Staffing (Contract)'
                           WHEN 9 THEN 'MSP'
                           WHEN 5 THEN 'Payrolling' END                                       AS jobType,
                       j.openings                                                             AS jobOpenings,
                       DATE(%s)                                                               AS assignedDate
                FROM job j
                          INNER JOIN user_job_relation ujl ON ujl.job_id = j.id AND ujl.status = 1
                          INNER JOIN job_user_relation jul on jul.job_id = j.id and jul.user_id = ujl.user_id
                          INNER JOIN permission_user_team put ON put.user_id = ujl.user_id and put.is_primary = 1
                          INNER JOIN permission_team pt ON pt.id = put.team_id
                          INNER JOIN company c ON j.company_id = c.id
                          INNER JOIN recruitment_process rp on j.recruitment_process_id = rp.id
                          INNER JOIN user u ON u.id = jul.user_id
                WHERE j.tenant_id = :tenantId
                  AND jul.role = 2
                  AND u.activated = 1
                  AND %s BETWEEN :startDate AND :endDate
                  {dataPermission}
                """.formatted(timezoneField, timezoneField);
        Map<String, Object> param = buildParamMap(dto);
        String formatSql = formatDataPermissionSql(dto, permissionDTO, sql, param);
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatSql, param);
        stopWatch.stop();
        log.info("searchAssignedJobDetailTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapList;
    }

    public Map<Long, Integer> queryFilledCount(SeaTeamPerformanceSearchDto dto, TeamDataPermissionRespDTO permissionDTO) {
        StopWatch stopWatch = new StopWatch("searchFilledCountTask");
        stopWatch.start();
        String sql = """
                SELECT jul.user_id, COUNT(distinct ujl.job_id) AS filledCount
                FROM job j
                         INNER JOIN job_user_relation jul ON jul.job_id = j.id
                         INNER JOIN user_job_relation ujl ON ujl.job_id = j.id AND ujl.status = 1
                         INNER JOIN permission_user_team put ON put.user_id = ujl.user_id and put.is_primary = 1
                         INNER JOIN permission_team pt ON pt.id = put.team_id
                         INNER JOIN user u ON u.id = ujl.user_id
                         INNER JOIN talent_recruitment_process trp ON trp.job_id = j.id
                         INNER JOIN talent_recruitment_process_kpi_user trpku
                                    ON trp.id = trpku.talent_recruitment_process_id AND trpku.user_role = 1 AND trpku.user_id = u.id
                         INNER JOIN talent_recruitment_process_onboard trpo
                                    ON trpo.talent_recruitment_process_id = trp.id
                         INNER JOIN talent_recruitment_process_onboard_date trpod
                                             ON trp.id = trpod.talent_recruitment_process_id
                         INNER JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id
                                           AND trpn.node_status != 4 AND trpn.node_type = 60
                WHERE j.tenant_id = :tenantId
                  AND jul.role = 2
                  AND u.activated = 1
                  AND %s BETWEEN :startDate AND :endDate
                  AND trpod.onboard_date > ujl.created_date
                  {dataPermission}
                GROUP BY put.user_id;
                """.formatted(convertTz(dto.getTimezone(), "ujl.created_date"));
        Map<String, Object> param = buildParamMap(dto);
        String formatSql = formatDataPermissionSql(dto, permissionDTO, sql, param);
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatSql, param);
        stopWatch.stop();
        log.info("searchFilledCountTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapResult(mapList, "filledCount");
    }

    private String filterByTeamIds(Set<Long> teamIds) {
        if (CollUtil.isEmpty(teamIds)) {
            return "";
        }
        return " AND EXISTS(SELECT 1 FROM permission_user_team WHERE user_id = trpku.user_id AND team_id IN :teamIds)";
    }

    private String filterByUserIds(Set<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return "";
        }
        return " AND trpku.user_id IN :userIds";
    }

    public Map<Long, Integer> querySubmitToClientCount(SeaTeamPerformanceSearchDto dto, Set<Long> userIds, Set<Long> teamIds) {
        StopWatch stopWatch = new StopWatch("searchSubmitToClientCountTask");
        stopWatch.start();

        String sql = """
                SELECT
                    trpku.user_id,
                    COUNT(DISTINCT trp.id) as submitToClientCount
                FROM talent_recruitment_process_submit_to_client trpstc
                        INNER JOIN talent_recruitment_process trp ON trp.id = trpstc.talent_recruitment_process_id
                        INNER JOIN talent_recruitment_process_submit_to_job trpstj on trp.id = trpstj.talent_recruitment_process_id
                                INNER JOIN talent_recruitment_process_kpi_user trpku ON trpku.talent_recruitment_process_id = trp.id
                                LEFT JOIN `user` u ON u.id = trpku.user_id
                                LEFT JOIN job j ON j.id = trp.job_id
                WHERE trp.tenant_id = :tenantId
                    AND trpku.user_role = 1
                    AND u.activated = 1
                    AND %s BETWEEN :startDate AND :endDate
                    %s
                    %s
                    GROUP BY trpku.user_id
                """.formatted(convertTz(dto.getTimezone(), "trpstc.submit_time"), filterByTeamIds(teamIds), filterByUserIds(userIds));

        Map<String, Object> param = buildParamMap(dto);
        if (!userIds.isEmpty()) {
            param.put("userIds", userIds);
        }
        if (!teamIds.isEmpty()) {
            param.put("teamIds", teamIds);
        }
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(sql, param);
        stopWatch.stop();
        log.info("searchSubmitToClientCountTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapResult(mapList, "submitToClientCount");
    }

    public Map<Long, Integer> queryInterviewedCount(SeaTeamPerformanceSearchDto dto, Set<Long> userIds, Set<Long> teamIds) {
        StopWatch stopWatch = new StopWatch("searchInterviewedCountTask");
        stopWatch.start();
        String sql = """
                 SELECT
                     trpku.user_id,
                     COUNT(DISTINCT trp.id) as interviewedCount
                 FROM talent_recruitment_process_interview trpi
                     INNER JOIN talent_recruitment_process trp ON trpi.talent_recruitment_process_id = trp.id
                     INNER JOIN talent_recruitment_process_submit_to_client trpstc ON trp.id = trpstc.talent_recruitment_process_id
                     INNER JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id
                     LEFT JOIN `user` u ON u.id = trpku.user_id
                     LEFT JOIN job j ON j.id = trp.job_id
                WHERE trp.tenant_id = :tenantId
                     AND trpku.user_role = 1
                     AND u.activated = 1
                     AND %s BETWEEN :startDate AND :endDate
                     %s
                     %s
                GROUP BY trpku.user_id
                """.formatted(convertTz(dto.getTimezone(), "trpstc.submit_time"), filterByTeamIds(teamIds), filterByUserIds(userIds));
        Map<String, Object> param = buildParamMap(dto);
        if (!userIds.isEmpty()) {
            param.put("userIds", userIds);
        }
        if (!teamIds.isEmpty()) {
            param.put("teamIds", teamIds);
        }
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(sql, param);
        stopWatch.stop();
        log.info("searchInterviewedCountTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapResult(mapList, "interviewedCount");
    }

    public Map<Long, Double> queryClientToOnboardAvgTime(SeaTeamPerformanceSearchDto dto, Set<Long> userIds, Set<Long> teamIds) {
        StopWatch stopWatch = new StopWatch("searchClientToOnboardAvgTimeTask");
        stopWatch.start();
        String submitTimeField = convertTz(dto.getTimezone(), "trpstc.submit_time");
        String startDateField = convertTz(dto.getTimezone(), "s.start_date");
        String sql = """
                SELECT trpku.user_id,
                       ROUND(AVG(DATEDIFF(%s, DATE(%s))), 1) AS clientToOnboardAvgTime
                FROM talent_recruitment_process_onboard trpo
                         INNER JOIN start s on trpo.talent_recruitment_process_id = s.talent_recruitment_process_id
                         INNER JOIN talent_recruitment_process trp ON trpo.talent_recruitment_process_id = trp.id
                         INNER JOIN talent_recruitment_process_submit_to_client trpstc ON trp.id = trpstc.talent_recruitment_process_id
                         INNER JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id
                         INNER JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id
                                    AND trpn.node_status != 4 AND trpn.node_type = 60
                         LEFT JOIN `user` u ON u.id = trpku.user_id
                         LEFT JOIN job j ON j.id = trp.job_id
                WHERE trp.tenant_id = :tenantId
                  AND %s BETWEEN :startDate AND :endDate
                  AND trpku.user_role = 1
                  AND u.activated = 1
                    %s
                    %s
                GROUP BY trpku.user_id
                """.formatted(startDateField, submitTimeField, submitTimeField, filterByTeamIds(teamIds), filterByUserIds(userIds));
        Map<String, Object> param = buildParamMap(dto);
        if (!userIds.isEmpty()) {
            param.put("userIds", userIds);
        }
        if (!teamIds.isEmpty()) {
            param.put("teamIds", teamIds);
        }
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(sql, param);
        stopWatch.stop();
        log.info("searchClientToOnboardAvgTimeTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        if (CollUtil.isEmpty(mapList)) {
            return Map.of();
        }
        Map<Long, Double> resultMap = new HashMap<>(mapList.size());
        for (Map<String, Object> mapItem : mapList) {
            resultMap.put(Long.parseLong(mapItem.get("user_id").toString()), Double.parseDouble(mapItem.get("clientToOnboardAvgTime").toString()));
        }
        return resultMap;
    }

    public List<Map<String, Object>> queryClientToOnboardDetail(SeaTeamPerformanceSearchDto dto, Set<Long> userIds, Set<Long> teamIds) {
        StopWatch stopWatch = new StopWatch("searchClientToOnboardDetailTask");
        stopWatch.start();
        String timezone = dto.getTimezone();
        String submitTimeField = convertTz(timezone, "trpstc.submit_time");
        String startDateField = convertTz(timezone, "s.start_date");
        String sql = """
                SELECT trp.talent_id                                          AS candidateId,
                       IF(CONCAT(t.first_name, t.last_name) regexp '[一-龥]' = 1, CONCAT(t.last_name, t.first_name),
                          CONCAT(t.first_name, ' ', t.last_name))             AS candidateName,
                       j.id                                                   AS jobId,
                       j.title                                                AS jobTitle,
                       j.pteam_id                                             AS jobPTeamId,
                       c.id                                                   AS companyId,
                       c.full_business_name                                   AS companyName,
                       DATE(%s)                                               AS submitToClientOn,
                       DATE(%s)                                               AS startDate,
                       DATEDIFF(%s, DATE(%s))                                 AS totalDays
                
                FROM talent_recruitment_process_onboard trpo
                         INNER JOIN talent_recruitment_process_onboard_date trpod
                                    ON trpo.talent_recruitment_process_id = trpod.talent_recruitment_process_id
                         INNER JOIN talent_recruitment_process trp ON trpo.talent_recruitment_process_id = trp.id
                         INNER JOIN talent_recruitment_process_submit_to_client trpstc ON trp.id = trpstc.talent_recruitment_process_id
                         INNER JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id
                         INNER JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id
                                    AND trpn.node_status != 4 AND trpn.node_type = 60
                         INNER JOIN start s ON s.talent_recruitment_process_id = trp.id
                         LEFT JOIN `user` u ON u.id = trpku.user_id
                         LEFT JOIN job j ON j.id = trp.job_id
                         LEFT JOIN company c ON j.company_id = c.id
                         LEFT JOIN talent t ON trp.talent_id = t.id
                WHERE trp.tenant_id = :tenantId
                  AND %s BETWEEN :startDate AND :endDate
                  AND trpku.user_role = 1
                  AND u.activated = 1
                  %s
                  %s
                """.formatted(submitTimeField, startDateField, startDateField, submitTimeField, submitTimeField, filterByTeamIds(teamIds), filterByUserIds(userIds));
        Map<String, Object> param = buildParamMap(dto);
        if (!userIds.isEmpty()) {
            param.put("userIds", userIds);
        }
        if (!teamIds.isEmpty()) {
            param.put("teamIds", teamIds);
        }
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(sql, param);
        stopWatch.stop();
        log.info("searchClientToOnboardDetailTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapList;
    }

    public Map<Long, Double> queryFirstInterviewToOnboardAvgTime(SeaTeamPerformanceSearchDto dto, Set<Long> userIds, Set<Long> teamIds) {
        StopWatch stopWatch = new StopWatch("searchFirstInterviewToOnboardAvgTimeTask");
        stopWatch.start();
        String timezoneField = convertTz(dto.getTimezone(), "trpi.from_time");
        String filterByTeam = filterByTeamIds(teamIds);
        String filterByUser = filterByUserIds(userIds);
        String firstInterviewField = convertTz(dto.getTimezone(), "fit.interview_time");
        String startDateField = convertTz(dto.getTimezone(), "ot.start_date");
        String sql = """
                WITH first_interview_time as (SELECT trpku.user_id       AS user_id,
                                                     trp.id              AS process_id,
                                                     MIN(trpi.from_time) AS interview_time
                                              FROM talent_recruitment_process_onboard trpo
                                                       INNER JOIN talent_recruitment_process trp
                                                                  ON trpo.talent_recruitment_process_id = trp.id
                                                       INNER JOIN talent_recruitment_process_interview trpi
                                                                  ON trp.id = trpi.talent_recruitment_process_id
                                                       INNER JOIN talent_recruitment_process_kpi_user trpku
                                                                  ON trp.id = trpku.talent_recruitment_process_id
                                                       INNER JOIN talent_recruitment_process_node trpn
                                                                  ON trp.id = trpn.talent_recruitment_process_id
                                                                      AND trpn.node_status != 4 AND trpn.node_type = 60
                
                                              WHERE trp.tenant_id = :tenantId
                                                AND trpku.user_role = 1
                                                %s
                                                %s
                                              GROUP BY trpku.user_id, trp.id),
                
                -- 目前已经到达入职状态的 真正入职时间
                     onboard_time as (SELECT trpku.user_id AS user_id, trp.id AS process_id, s.start_date
                                      FROM talent_recruitment_process_onboard trpo
                                               INNER JOIN talent_recruitment_process trp
                                                          ON trpo.talent_recruitment_process_id = trp.id
                                               INNER JOIN talent_recruitment_process_interview trpi 
                                                          ON trp.id = trpi.talent_recruitment_process_id
                                               INNER JOIN talent_recruitment_process_kpi_user trpku
                                                          ON trp.id = trpku.talent_recruitment_process_id
                                               INNER JOIN talent_recruitment_process_node trpn
                                                          ON trp.id = trpn.talent_recruitment_process_id
                                                              AND trpn.node_status != 4 AND trpn.node_type = 60
                                               INNER JOIN start s on s.talent_recruitment_process_id = trp.id
                
                                      WHERE trp.tenant_id = :tenantId
                                        AND trpku.user_role = 1
                                        %s
                                        %s
                                      GROUP BY trpku.user_id, trp.id)
                
                SELECT ot.user_id,
                       ROUND(AVG(DATEDIFF(%s, DATE(%s))), 1) AS firstInterviewToOnboardAvgTime
                FROM first_interview_time fit
                         INNER JOIN onboard_time ot ON fit.process_id = ot.process_id
                
                         INNER JOIN user u ON u.id = ot.user_id
                WHERE u.activated = 1
                     AND %s BETWEEN :startDate AND :endDate
                GROUP BY fit.user_id
                """.formatted(filterByTeam, filterByUser, filterByTeam, filterByUser, startDateField, firstInterviewField, firstInterviewField);
        Map<String, Object> param = buildParamMap(dto);
        if (!userIds.isEmpty()) {
            param.put("userIds", userIds);
        }
        if (!teamIds.isEmpty()) {
            param.put("teamIds", teamIds);
        }
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(sql, param);
        stopWatch.stop();
        log.info("searchFirstInterviewToOnboardAvgTimeTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        if (CollUtil.isEmpty(mapList)) {
            return Map.of();
        }
        Map<Long, Double> resultMap = new HashMap<>(mapList.size());
        for (Map<String, Object> mapItem : mapList) {
            resultMap.put(Long.parseLong(mapItem.get("user_id").toString()), Double.parseDouble(mapItem.get("firstInterviewToOnboardAvgTime").toString()));
        }
        return resultMap;
    }

    public List<Map<String, Object>> queryFirstInterviewToOnboardDetail(SeaTeamPerformanceSearchDto dto, Set<Long> userIds, Set<Long> teamIds) {
        StopWatch stopWatch = new StopWatch("searchFirstInterviewToOnboardDetailTask");
        stopWatch.start();
        String filterByTeam = filterByTeamIds(teamIds);
        String filterByUser = filterByUserIds(userIds);
        String fitField = convertTz(dto.getTimezone(), "fit.interview_time");
        String otField = convertTz(dto.getTimezone(), "ot.start_date");
        String sql = """
                WITH first_interview_time as (SELECT trpku.user_id       AS user_id,
                                                     trp.id              AS process_id,
                                                     MIN(trpi.from_time) AS interview_time
                                              FROM talent_recruitment_process_onboard trpo
                                                       INNER JOIN talent_recruitment_process trp
                                                                  ON trpo.talent_recruitment_process_id = trp.id
                                                       INNER JOIN talent_recruitment_process_interview trpi
                                                                  ON trp.id = trpi.talent_recruitment_process_id
                                                       INNER JOIN talent_recruitment_process_kpi_user trpku
                                                                  ON trp.id = trpku.talent_recruitment_process_id
                                                       INNER JOIN talent_recruitment_process_node trpn
                                                                  ON trp.id = trpn.talent_recruitment_process_id
                                                                      AND trpn.node_status != 4 AND trpn.node_type = 60
                
                                              WHERE trp.tenant_id = :tenantId
                                                AND trpku.user_role = 1
                                                %s
                                                %s
                                              GROUP BY trpku.user_id, trp.id),
                
                -- 目前已经到达入职状态的 真正入职时间
                     onboard_time as (SELECT trpku.user_id AS user_id, trp.id AS process_id, s.start_date
                                      FROM talent_recruitment_process_onboard trpo
                                               INNER JOIN talent_recruitment_process trp
                                                          ON trpo.talent_recruitment_process_id = trp.id
                                               INNER JOIN talent_recruitment_process_interview trpi
                                                          ON trp.id = trpi.talent_recruitment_process_id
                                               INNER JOIN talent_recruitment_process_kpi_user trpku
                                                          ON trp.id = trpku.talent_recruitment_process_id
                                               INNER JOIN talent_recruitment_process_node trpn
                                                          ON trp.id = trpn.talent_recruitment_process_id
                                                              AND trpn.node_status != 4 AND trpn.node_type = 60
                                               INNER JOIN start s on s.talent_recruitment_process_id = trp.id
                                      WHERE trp.tenant_id = :tenantId
                                        AND trpku.user_role = 1
                                        %s
                                        %s
                                      GROUP BY trpku.user_id, trp.id)
                
                SELECT trp.talent_id                                       AS candidateId,
                       IF(CONCAT(t.first_name, t.last_name) regexp '[一-龥]' = 1, CONCAT(t.last_name, t.first_name),
                          CONCAT(t.first_name, ' ', t.last_name))          AS candidateName,
                       j.id                                                AS jobId,
                       j.title                                             AS jobTitle,
                       j.pteam_id                                          AS jobPTeamId,
                       c.id                                                AS companyId,
                       c.full_business_name                                AS companyName,
                       DATE(%s)                                            AS firstInterviewOn,
                       DATE(%s)                                            AS startDate,
                       DATEDIFF(%s, DATE(%s))   AS totalDays
                FROM first_interview_time fit
                         INNER JOIN onboard_time ot ON fit.process_id = ot.process_id
                         INNER JOIN talent_recruitment_process trp ON trp.id = fit.process_id
                         INNER JOIN user u on fit.user_id = u.id
                         LEFT JOIN job j ON j.id = trp.job_id
                         LEFT JOIN company c ON j.company_id = c.id
                         LEFT JOIN talent t on trp.talent_id = t.id
                         LEFT JOIN start s on trp.id = s.talent_recruitment_process_id
                WHERE u.activated = 1
                    AND %s BETWEEN :startDate AND :endDate
                """.formatted(filterByTeam, filterByUser, filterByTeam, filterByUser,
                fitField, otField, otField, fitField, fitField);
        Map<String, Object> param = buildParamMap(dto);
        if (!userIds.isEmpty()) {
            param.put("userIds", userIds);
        }
        if (!teamIds.isEmpty()) {
            param.put("teamIds", teamIds);
        }
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(sql, param);
        stopWatch.stop();
        log.info("searchFirstInterviewToOnboardAvgTimeTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapList;
    }

    public Map<Long, List<SeaTeamPerformanceVO.InvoiceAmountVO>> queryInvoiceAmount(SeaTeamPerformanceSearchDto dto, Set<Long> userIds, Set<Long> teamIds) {
        StopWatch stopWatch = new StopWatch("searchInvoiceAmountTask");
        stopWatch.start();
        String invoiceDateField = convertTz(dto.getTimezone(), "i.invoice_date");
        Map<String, Object> param = buildParamMap(dto);
        String filterByTeam = "";
        String filterByUser = "";
        if (!userIds.isEmpty()) {
            param.put("userIds", userIds);
            filterByUser = " AND sc.user_id IN :userIds ";
        }
        if (!teamIds.isEmpty()) {
            param.put("teamIds", teamIds);
            filterByTeam = " AND EXISTS(SELECT 1 FROM permission_user_team WHERE user_id = sc.user_id AND team_id IN :teamIds) ";
        }
        String sql = """
                SELECT user_id,
                       currency,
                       SUM(invoiceAmount) AS invoiceAmount
                FROM (
                    (SELECT u.id AS user_id, i.currency, SUM(i.due_amount) as invoiceAmount
                    from view_invoice_list i
                             INNER JOIN start s ON s.id = i.start_id
                             INNER JOIN start_commission sc ON s.id = sc.start_id
                             INNER JOIN user u ON sc.user_id = u.id
                    WHERE i.tenant_id = :tenantId
                      AND sc.user_role = 1
                      AND {invoiceDateField} between :startDate and :endDate
                      AND i.invoice_type = 0
                      AND i.status != 7
                      AND u.activated = 1
                      {filterByTeam}
                      {filterByUser}
                    group by u.id, i.currency)
                UNION ALL
                    (SELECT sc.user_id,
                                  invoice_currency.currency_type as currency,
                                  SUM(i.total_amount)            AS invoiceAmount
                    FROM t_contractor_invoice i
                             INNER JOIN timesheet_talent_assignment tta ON i.assignment_id = tta.id
                             INNER JOIN start s ON tta.start_id = s.id
                             INNER JOIN start_commission sc ON s.id = sc.start_id
                             INNER JOIN user u ON sc.user_id = u.id
                             INNER JOIN (SELECT invoice_id, currency_type
                                         from t_invoice_timesheet_info
                                         group by invoice_id, currency_type) AS invoice_currency
                                        ON invoice_currency.invoice_id = i.id
                    WHERE i.tenant_id = :tenantId
                      AND u.activated = 1
                      AND sc.user_role = 1
                      AND {invoiceDateField} between :startDate and :endDate
                      AND i.invoice_type = 2
                      AND i.invoice_status != 3
                      {filterByTeam}
                      {filterByUser}
                    GROUP BY sc.user_id, invoice_currency.currency_type)
                ) AS union_table
                GROUP BY user_id, currency;
                """;
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put("invoiceDateField", invoiceDateField);
        formatMap.put("filterByTeam", filterByTeam);
        formatMap.put("filterByUser", filterByUser);
        String formatedSql = StrUtil.format(sql, formatMap);
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatedSql, param);
        stopWatch.stop();
        log.info("searchInvoiceAmountTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        if (CollUtil.isEmpty(mapList)) {
            return Map.of();
        }
        Map<Long, List<SeaTeamPerformanceVO.InvoiceAmountVO>> resultMap = new HashMap<>(mapList.size());
        for (Map<String, Object> mapItem : mapList) {
            long userId = Long.parseLong(mapItem.get("user_id").toString());
            Integer currency = Integer.parseInt(mapItem.get("currency").toString());
            String amount = mapItem.get("invoiceAmount").toString();
            resultMap.computeIfAbsent(userId, k -> new ArrayList<>());
            List<SeaTeamPerformanceVO.InvoiceAmountVO> invoiceAmountVOList = resultMap.get(userId);
            SeaTeamPerformanceVO.InvoiceAmountVO invoiceAmountVO = new SeaTeamPerformanceVO.InvoiceAmountVO();
            invoiceAmountVO.setCurrency(currency);
            invoiceAmountVO.setAmount(new BigDecimal(amount));
            invoiceAmountVOList.add(invoiceAmountVO);
        }
        return resultMap;
    }

    public List<Map<String, Object>> queryInvoiceAmountDetail(SeaTeamPerformanceSearchDto searchDto, Set<Long> userIds, Set<Long> teamIds) {
        StopWatch stopWatch = new StopWatch("searchInvoiceAmountDetailTask");
        stopWatch.start();
        String invoiceDateField = convertTz(searchDto.getTimezone(), "i.invoice_date");
        Map<String, Object> param = buildParamMap(searchDto);
        String filterByTeam = "";
        String filterByUser = "";
        if (!userIds.isEmpty()) {
            param.put("userIds", userIds);
            filterByUser = " AND sc.user_id IN :userIds ";
        }
        if (!teamIds.isEmpty()) {
            param.put("teamIds", teamIds);
            filterByTeam = " AND EXISTS(SELECT 1 FROM permission_user_team WHERE user_id = sc.user_id AND team_id IN :teamIds) ";
        }
        String sql = """
                SELECT *
                FROM ((SELECT i.talent_id                                                                                                  AS candidateId,
                              IF(CONCAT(t.first_name, t.last_name) regexp '[一-龥]' = 1, CONCAT(t.last_name, t.first_name),
                                 CONCAT(t.first_name, ' ', t.last_name))                                                                   AS candidateName,
                              j.id                                                                                                         AS jobId,
                              j.title                                                                                                      AS jobTitle,
                              j.pteam_id                                                                                                   AS jobPTeamId,
                              c.id                                                                                                         AS companyId,
                              c.full_business_name                                                                                         AS companyName,
                              i.currency                                                                                                   AS currency,
                              i.due_amount                                                                                                 AS invoiceAmount,
                              DATE({invoiceDateField})                                                                                     AS invoiceDate,
                              CASE i.status
                                  WHEN 0 THEN 'Paid'
                                  WHEN 1 THEN 'Unpaid'
                                  WHEN 2 THEN 'Overdue'
                                  WHEN 8
                                      THEN 'Partially_Paid' END                                                                            AS paymentStatus,
                              i.am                                                                                                         AS AM
                       FROM view_invoice_list i
                                INNER JOIN start s ON S.id = i.start_id
                                INNER JOIN start_commission sc ON S.id = sc.start_id
                                INNER JOIN user u ON Sc.user_id = u.id
                                LEFT JOIN talent t ON i.talent_id = t.id
                                LEFT JOIN job j ON j.id = i.job_id
                                LEFT JOIN company c ON J.company_id = c.id
                       WHERE i.tenant_id = :tenantId
                         AND sc.user_role = 1
                         AND {invoiceDateField} between :startDate and :endDate
                         AND i.invoice_type = 0
                         AND i.status != 7
                         AND u.activated = 1
                         {filterByTeam}
                         {filterByUser}
                       )
                UNION ALL
                      (SELECT i.talent_id                                                 AS candidateId,
                              IF(CONCAT(t.first_name, t.last_name) regexp '[一-龥]' = 1, CONCAT(t.last_name, t.first_name),
                                 CONCAT(t.first_name, ' ', t.last_name))                  AS candidateName,
                              j.id                                                        AS jobId,
                              j.title                                                     AS jobTitle,
                              j.pteam_id                                                  AS jobPTeamId,
                              c.id                                                        AS companyId,
                              c.full_business_name                                        AS companyName,
                              invoice_currency.currency_type                              AS currency,
                              i.total_amount                                              AS invoiceAmount,
                              DATE({invoiceDateField})                                    AS invoiceDate,
                              ''                                                          AS paymentStatus,
                              (select group_concat(distinct (IF(regexp_like(concat(`u`.`first_name`, `u`.`last_name`), '[一-龥]') = 1,
                                                                concat(`u`.`last_name`, `u`.`first_name`),
                                                                concat(`u`.`first_name`, ' ', `u`.`last_name`))) separator ',')
                               from (`start_commission` `sc` join `user` `u` on ((`u`.`id` = `sc`.`user_id`)))
                               where ((`sc`.`start_id` = s.id) and (`sc`.`user_role` = 0))
                               group by `sc`.`start_id`)                                  AS AM
                       FROM t_contractor_invoice i
                                INNER JOIN timesheet_talent_assignment tta ON i.assignment_id = tta.id
                                INNER JOIN start s ON tta.start_id = s.id
                                INNER JOIN start_commission sc ON s.id = sc.start_id
                                INNER JOIN user u ON sc.user_id = u.id
                                INNER JOIN (SELECT invoice_id, currency_type
                                            from t_invoice_timesheet_info
                                            group by invoice_id, currency_type) AS invoice_currency
                                           ON invoice_currency.invoice_id = i.id
                                LEFT JOIN talent t ON i.talent_id = t.id
                                LEFT JOIN job j ON j.id = i.job_id
                                LEFT JOIN company c ON J.company_id = c.id
                       WHERE i.tenant_id = :tenantId
                         AND u.activated = 1
                         AND sc.user_role = 1
                         AND {invoiceDateField} between :startDate and :endDate
                         AND i.invoice_type = 2
                         AND i.invoice_status != 3
                         {filterByTeam}
                         {filterByUser}
                       )
                ) AS union_table
                GROUP BY candidateId, jobId, jobTitle, jobPTeamId, companyId, companyName, currency, invoiceAmount, invoiceDate, paymentStatus, AM
                """;
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put("invoiceDateField", invoiceDateField);
        formatMap.put("filterByTeam", filterByTeam);
        formatMap.put("filterByUser", filterByUser);
        String formatedSql = StrUtil.format(sql, formatMap);
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatedSql, param);
        stopWatch.stop();
        log.info("searchInvoiceAmountDetailTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapList;
    }


    public Map<Long, Integer> queryInvoiceStartCount(SeaTeamPerformanceSearchDto dto, Set<Long> userIds, Set<Long> teamIds) {
        StopWatch stopWatch = new StopWatch("searchInvoiceStartCountTask");
        stopWatch.start();
        Map<String, Object> param = buildParamMap(dto);
        String filterByTeam = "";
        String filterByUser = "";
        if (!userIds.isEmpty()) {
            param.put("userIds", userIds);
            filterByUser = " AND sc.user_id IN :userIds ";
        }
        if (!teamIds.isEmpty()) {
            param.put("teamIds", teamIds);
            filterByTeam = " AND EXISTS(SELECT 1 FROM permission_user_team WHERE user_id = sc.user_id AND team_id IN :teamIds) ";
        }
        String sql = """
                SELECT user_id,
                       COUNT(DISTINCT start_id) AS start_count
                FROM ((SELECT u.id AS user_id,
                              s.id AS start_id
                       FROM view_invoice_list i
                                INNER JOIN start s ON s.id = i.start_id
                                INNER JOIN start_commission sc ON s.id = sc.start_id
                                INNER JOIN user u ON sc.user_id = u.id
                       WHERE i.tenant_id = :tenantId
                         AND sc.user_role = 1
                         AND {invoiceDateField} BETWEEN :startDate AND :endDate
                         AND i.invoice_type = 0
                         AND i.status != 7
                         AND u.activated = 1
                         {filterByTeam}
                         {filterByUser}
                       GROUP BY sc.user_id, s.id)
                UNION ALL
                       (SELECT sc.user_id AS user_id,
                               s.id AS start_id
                       FROM t_contractor_invoice i
                                INNER JOIN timesheet_talent_assignment tta ON i.assignment_id = tta.id
                                INNER JOIN start s ON tta.start_id = s.id
                                INNER JOIN start_commission sc ON s.id = sc.start_id
                                INNER JOIN user u ON sc.user_id = u.id
                       WHERE i.tenant_id = :tenantId
                         AND u.activated = 1
                         AND sc.user_role = 1
                         AND {invoiceDateField} BETWEEN :startDate AND :endDate
                         AND i.invoice_type = 2
                         AND i.invoice_status != 3
                         {filterByTeam}
                         {filterByUser}
                       GROUP BY sc.user_id, s.id)
                ) AS union_table
                GROUP BY user_id
                """;
        Map<String, String> formatMap = new HashMap<>();
        formatMap.put("invoiceDateField", convertTz(dto.getTimezone(), "i.invoice_date"));
        formatMap.put("filterByTeam", filterByTeam);
        formatMap.put("filterByUser", filterByUser);
        String formatedSql = StrUtil.format(sql, formatMap);
        List<Map<String, Object>> mapList = doSearchDataWithNameMap(formatedSql, param);
        stopWatch.stop();
        log.info("searchInvoicedStartCountTask cost time: {} ms", stopWatch.getTotalTimeMillis());
        return mapResult(mapList, "start_count");
    }

    private Map<String, Object> buildParamMap(SeaTeamPerformanceSearchDto searchDto) {
        Map<String, Object> param = new HashMap<>(16);
        param.put("tenantId", searchDto.getSearchTenantId());
        param.put("startDate", searchDto.getStartDateUtc());
        param.put("endDate", searchDto.getEndDateUtc());
        return param;
    }

    private String formatDataPermissionSql(SeaTeamPerformanceSearchDto searchDto, TeamDataPermissionRespDTO permissionDTO, String sql, Map<String, Object> param) {
        Map<String, String> map = new HashMap<>(16);
        StringBuilder sb = new StringBuilder();
        if (!searchDto.getUserIds().isEmpty()) {
            sb.append(" AND put.user_id IN :userIds ");
            param.put("userIds", searchDto.getUserIds());
        }
        if (!searchDto.getTeamIds().isEmpty()) {
            sb.append(" AND put.team_id IN :teamIds ");
            param.put("teamIds", searchDto.getTeamIds());
        }
        appendJobDataPermissionForJobOrDetail(sb, searchDto, param, permissionDTO);
        map.put("dataPermission", sb.toString());
        return StrUtil.format(sql, map);
    }

    private Map<Long, Integer> mapResult(List<Map<String, Object>> mapList, String metricName) {
        if (CollUtil.isEmpty(mapList)) {
            return Map.of();
        }
        Map<Long, Integer> resultMap = new HashMap<>(mapList.size());
        for (Map<String, Object> mapItem : mapList) {
            resultMap.put(Long.parseLong(mapItem.get("user_id").toString()), Integer.parseInt(mapItem.get(metricName).toString()));
        }
        return resultMap;
    }

    private String convertTz(String timezone, String column) {
        return " CONVERT_TZ(" + column + ", 'UTC', '" + timezone + "')";
    }


    private void appendJobDataPermissionForJobOrDetail(StringBuilder sb, SeaTeamPerformanceSearchDto searchDto, Map<String, Object> param, TeamDataPermissionRespDTO teamDTO) {
        // 超级管理员和租户管理员不过滤数据权限
        if (SecurityUtils.isAdmin() || SecurityUtils.isSystemAdmin()) {
            return;
        }
        //权限
        Set<Long> privateJobIds = searchDto.getPrivateJobIds();
        param.put("privateJobIds", privateJobIds == null ? Collections.emptySet() : privateJobIds);
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            //仅仅自己
            sb.append(" and (put.user_id = :puserId or j.id in :privateJobIds ) ");
            param.put("puserId", searchDto.getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            //团队
            //获取查询团队和权限团队的交集
            sb.append(" and (put.team_id in :teamIds or j.id in :privateJobIds ) ");
            param.put("teamIds", teamDTO.getNestedTeamIds());
        } else {
            //全部
            if (teamDTO.getTeamIdForPrivateJob() != null) {
                sb.append(" AND (j.pteam_id != :teamIdForPrivateJob or j.id in :privateJobIds ) ");
                param.put("teamIdForPrivateJob", teamDTO.getTeamIdForPrivateJob());
            } else {
                sb.append(" AND (j.pteam_id is not null or j.id in :privateJobIds) ");
            }
        }
    }


}
