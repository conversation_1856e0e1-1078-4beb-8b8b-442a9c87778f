package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.config.env.ApplicationProperties;
import com.altomni.apn.report.domain.vm.p1.UserNameVM;
import com.altomni.apn.report.domain.vm.w1.QueryDateVM;
import com.altomni.apn.report.domain.vm.w1.UserCountVM;
import com.altomni.apn.report.dto.LinkedinStatsDTO;
import com.altomni.apn.report.repository.ESStatsRepository;
import com.altomni.apn.report.repository.LinkedinStatsRepository;
import com.altomni.apn.report.service.ReportUserStatsService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z;

@Service
public class ReportUserStatsServiceImpl extends ReportBaseServiceImpl implements ReportUserStatsService {

    private final Logger log = LoggerFactory.getLogger(ReportUserStatsService.class);

    private LinkedinStatsRepository linkedinStatsRepository;

    private ESStatsRepository esStatsRepository;

    private static final String UTC = "UTC";

    private static final Integer QUERY_GROUP_SIZE = 100;

    @Resource
    private ApplicationProperties applicationProperties;

    private volatile ExecutorService executorUserCountService;

    public ReportUserStatsServiceImpl(LinkedinStatsRepository linkedinStatsRepository, ESStatsRepository esStatsRepository) {
        this.linkedinStatsRepository = linkedinStatsRepository;
        this.esStatsRepository = esStatsRepository;
    }

    @Override
    public List<LinkedinStatsDTO> getUserLinkedinStats(String fromDate, String toDate, ContactType type, String timeZone) {
        String formattedFromDate = "";
        String formattedToDate = "";
        if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
            formattedFromDate = DateUtil.utcTimeToNormalTime(fromDate);
            formattedToDate = DateUtil.utcTimeToNormalTime(toDate);
        } else {
            formattedFromDate = DateUtil.getFirstDayOfCurrentWeek();
            formattedToDate = DateUtil.currentTime();
        }

        List<QueryDateVM> queryDateList = getQueryTimeInterval(fromDate, toDate);
        StringBuilder stringBuilder = new StringBuilder();
        createQueryUserCount(stringBuilder);
        String querySql = stringBuilder.toString();

        Long tenantId = SecurityUtils.getTenantId();
        CountDownLatch countDownLatch = new CountDownLatch(queryDateList.size());
        List<UserCountVM> totalUserCountList = new CopyOnWriteArrayList<>();
        queryDateList.forEach(item -> getQueryUserCountExecutorService().execute(() -> {
            try {
                Map<Integer, Object> paramMap = new ConcurrentHashMap<>(16);
                paramMap.put(1, tenantId);
                paramMap.put(2, type.toDbValue());
                paramMap.put(3, item.getFromDate());
                paramMap.put(4, item.getToDate());
                List<UserCountVM> userCountVMList = searchData(querySql, UserCountVM.class, paramMap);
                CollectionUtil.addAll(totalUserCountList, userCountVMList);
            } catch (Exception e) {
                log.error("[APN: ReportUserStats] search user count failed = [{}]", ExceptionUtils.getStackTrace(e));
            } finally {
                countDownLatch.countDown();
                log.info("[APN: ReportUserStats] search user count succeeded, type = {}", item);
            }
        }));

        try {
            countDownLatch.await(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("[APN: ReportUserStats] companyCountDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
        }

        Map<Long, UserCountVM> userCountMap = new HashMap<>();
        totalUserCountList.forEach(o -> {
            if (userCountMap.containsKey(o.getUserId())) {
                UserCountVM userCountVM = userCountMap.get(o.getUserId());
                userCountVM.setTotalCnt((userCountVM.getTotalCnt() + o.getTotalCnt()));
            } else {
                userCountMap.put(o.getUserId(), o);
            }
        });

        List<UserCountVM> resultUserCountList = new ArrayList<>(userCountMap.values());

        List<Long> userIdList = resultUserCountList.stream().map(UserCountVM::getUserId).distinct().collect(Collectors.toList());
        List<UserNameVM> userNameList = searchUserNameByIds(userIdList);
        Map<Long, UserNameVM> userNameMap = userNameList.stream().collect(Collectors.toMap(UserNameVM::getId, UserNameVM -> UserNameVM));

        List<LinkedinStatsDTO> resultList = LinkedinStatsDTO.fromUserCountVMList(resultUserCountList);
        resultList.forEach(o -> {
            if (userNameMap.containsKey(o.getUserId())) {
                UserNameVM userName = userNameMap.get(o.getUserId());
                o.setUsername(CommonUtils.formatFullName(userName.getFirstName(), userName.getLastName()));
            }
        });

        return resultList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getUsername())).sorted(Comparator.comparing(LinkedinStatsDTO::getUserId)).collect(Collectors.toList());
    }

    @Override
    public List<Object> getAllUserLinkedinStats(String fromDate, String toDate, ContactType type, String timeZone) {
        String formattedFromDate = "";
        String formattedToDate = "";
        if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
            formattedFromDate = DateUtil.utcTimeToNormalTime(fromDate);
            formattedToDate = DateUtil.utcTimeToNormalTime(toDate);
        } else {
            formattedFromDate = DateUtil.getFirstDayOfCurrentWeek();
            formattedToDate = DateUtil.currentTime();
        }

        List<Object[]> data = linkedinStatsRepository.getReportByTypeAndCreatedDateBetween(SecurityUtils.getTenantId(), type.toDbValue(), formattedFromDate, formattedToDate);

        return new ArrayList<>(LinkedinStatsDTO.castEntities(data));
    }

    @Override
    public List<LinkedinStatsDTO> getESStats(String fromDate, String toDate, String timeZone, Long tenantId) {
        String formattedFromDate = "";
        String formattedToDate = "";
        if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
            formattedFromDate = DateUtil.utcTimeToNormalTime(fromDate);
            formattedToDate = DateUtil.utcTimeToNormalTime(toDate);
        } else {
            formattedFromDate = DateUtil.getFirstDayOfCurrentWeek();
            formattedToDate = DateUtil.currentTime();
        }

        List<Object[]> data =  esStatsRepository.getReportByTenantIdAndCreatedDateBetween(tenantId, formattedFromDate, formattedToDate);

        return LinkedinStatsDTO.castEntities(data);
    }

    @Override
    public List<Object> getAllESStats(String fromDate, String toDate, String timeZone, Long tenantId) {
        String formattedFromDate = "";
        String formattedToDate = "";
        if (StringUtils.isNotBlank(fromDate) && StringUtils.isNotBlank(toDate)) {
            formattedFromDate = DateUtil.utcTimeToNormalTime(fromDate);
            formattedToDate = DateUtil.utcTimeToNormalTime(toDate);
        } else {
            formattedFromDate = DateUtil.getFirstDayOfCurrentWeek();
            formattedToDate = DateUtil.currentTime();
        }

        List<Object[]> data = esStatsRepository.getReportByTenantIdAndCreatedDateBetween(tenantId, formattedFromDate, formattedToDate);

        return new ArrayList<>(LinkedinStatsDTO.castEntities(data));
    }

    private void createQueryUserCount(StringBuilder stringBuilder) {
        stringBuilder.append("SELECT COUNT(l.id) total_cnt, l.user_id FROM linkedin_stats l WHERE \n" +
                "	l.tenant_id = ?1\n" +
                "	AND l.type = ?2 \n" +
                "	AND l.created_date BETWEEN ?3 AND ?4\n" +
                "	GROUP BY l.user_id");
    }


    private List<UserNameVM> searchUserNameByIds(List<Long> userIdList) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return new ArrayList<>();
        }

        String selectSql ="SELECT id, first_name, last_name FROM `user` WHERE id IN (?1)";
        Map<Integer, Object> paramMap = new HashMap<>(16);
        paramMap.put(1, userIdList);
        List<UserNameVM> userNameList =  searchData(selectSql, UserNameVM.class, paramMap);
        if (CollectionUtils.isEmpty(userNameList)) {
            return new ArrayList<>();
        }
        return userNameList;
    }

    private List<QueryDateVM> getQueryTimeInterval(String fromDate, String toDate) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(YYYY_MM_DD_T_HH_MM_SS_Z);
        dateFormat.setTimeZone(TimeZone.getTimeZone(UTC));
        List<QueryDateVM> queryDateList = new ArrayList<>();
        Instant fromInstant = Instant.parse(fromDate);
        Instant toInstant = Instant.parse(toDate);
        for (Instant instant = toInstant ; instant.isAfter(fromInstant) ; instant = instant.minusMillis(TimeUnit.DAYS.toMillis(QUERY_GROUP_SIZE))) {
            Instant itemFromInstant = fromInstant.isAfter(instant.minusMillis(TimeUnit.DAYS.toMillis(QUERY_GROUP_SIZE))) ? fromInstant : instant.minusMillis(TimeUnit.DAYS.toMillis(QUERY_GROUP_SIZE));
            QueryDateVM queryDate = new QueryDateVM(dateFormat.format(Date.from(itemFromInstant)), dateFormat.format(Date.from(instant)));
            queryDateList.add(queryDate);
        }
        return queryDateList;
    }

    private ExecutorService getQueryUserCountExecutorService() {
        if (executorUserCountService == null) {
            synchronized (ReportUserStatsService.class) {
                if (executorUserCountService == null) {
                    executorUserCountService = new ThreadPoolExecutor(
                            applicationProperties.getThreadNum(),
                            applicationProperties.getThreadNum() * 2,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-report-query-user-count", false));
                }
            }
        }
        return executorUserCountService;
    }

}
