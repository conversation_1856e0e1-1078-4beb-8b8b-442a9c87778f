package com.altomni.apn.report.dto.s3;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.utils.CommonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@Entity
public class UserCandidateDetailDTO implements Serializable, AttachConfidentialTalent {

    private static final long serialVersionUID = 4633846105382434451L;

    @Id
    private Long id;

    private String candidateName;

    @JsonIgnore
    private String cFirstName;

    @JsonIgnore
    private String cLastName;

    @Transient
    private String createdBy;

    private Instant createdAt;

    @JsonIgnore
    private String lFirstName;

    @JsonIgnore
    private String lLastName;

    @Transient
    private String lastUpdatedBy;

    private Instant lastUpdatedAt;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    public void constructFullName() {
        setCreatedBy(CommonUtils.formatFullNameWithBlankCheck(cFirstName, cLastName));
        setLastUpdatedBy(CommonUtils.formatFullNameWithBlankCheck(lFirstName, lLastName));
    }

    @Override
    public Long getTalentId() {
        return id;
    }

    @Override
    public void encrypt() {
        this.candidateName = null;
        this.cFirstName = null;
        this.cLastName = null;
        this.lFirstName = null;
        this.lLastName = null;
        this.confidentialInfo = null;
        this.lastUpdatedBy = null;
        this.lastUpdatedAt = null;
        this.createdBy = null;
        this.createdAt = null;
    }
}
