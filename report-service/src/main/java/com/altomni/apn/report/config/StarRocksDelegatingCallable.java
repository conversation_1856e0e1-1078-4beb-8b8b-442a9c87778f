package com.altomni.apn.report.config;

import com.altomni.apn.report.domain.enumeration.DataSourceType;

import java.util.concurrent.Callable;

public class StarRocksDelegatingCallable<T> implements Callable<T> {
    private final Callable<T> delegate;

    public StarRocksDelegatingCallable(Callable<T> delegate) {
        this.delegate = delegate;
    }

    @Override
    public T call() throws Exception {
        try {
            DataSourceHolder.setCurrentDb(DataSourceType.STARROCKS);
            return this.delegate.call();
        } finally {
            DataSourceHolder.clear();
        }
    }
}
