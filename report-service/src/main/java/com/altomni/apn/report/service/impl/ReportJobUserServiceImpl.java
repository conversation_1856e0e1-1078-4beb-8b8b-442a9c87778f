package com.altomni.apn.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.enumeration.permission.DataScope;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportJobByUserVo;
import com.altomni.apn.report.dto.ReportJobParam;
import com.altomni.apn.report.service.ReportJobUserService;
import com.altomni.apn.report.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Service("reportJobUserService")
public class ReportJobUserServiceImpl extends ReportJobBaseServiceImpl implements ReportJobUserService {

    @Override
    public List<ReportJobByUserVo> getJobsByUserSourceData(ReportJobParam reportParam) throws ExecutionException, InterruptedException {
        log.info("[apn] getJobsByUserSourceData start ...... ");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<Map<String, Object>> mapList = searchJobByUserSql(reportParam);
        List<ReportJobByUserVo> reportJobByCompanyVoList = convertReportJobByUserVoEntity(mapList);
        reportJobByCompanyVoList.forEach(vo -> vo.setUserName(CommonUtils.formatFullNameWithBlankCheck(vo.getUFirstName(), vo.getULastName())));
        addGrandTotal(reportJobByCompanyVoList);
        stopWatch.stop();
        log.info("[apn] getJobsByUserSourceData time = [{} ms] ", stopWatch.getTotalTimeMillis());
        log.info("[apn] getJobsByUserSourceData end ...... ");
        return reportJobByCompanyVoList;
    }

    private List<Map<String, Object>> searchJobByUserSql(ReportJobParam reportParam) throws ExecutionException, InterruptedException {
        List<Map<String, Object>> resultList = new ArrayList<>();
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<Map<String, Object>>> c1 = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            Map<Integer, Object> conditionParamMap = new HashMap<>(16);
            StringBuilder applicationSql = new StringBuilder();
            createJobByUserApplicationSql(applicationSql, reportParam, conditionParamMap);
            log.info("[apn] applicationSql = [{}]", applicationSql);
            List<Map<String, Object>> list = searchDataWithMap(applicationSql.toString(), conditionParamMap);
            stopWatch.stop();
            log.info("[apn] applicationSqlTask time [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return list;
        });
        CompletableFuture<List<Map<String, Object>>> c2 = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start();
            StringBuilder jobSql = new StringBuilder();
            Map<Integer, Object> conditionParamMap = new HashMap<>(16);
            createJobByUserJobSql(jobSql, reportParam, conditionParamMap);
            log.info("[apn] jobByUserSql = [{}]", jobSql);
            List<Map<String, Object>> list = searchDataWithMap(jobSql.toString(), conditionParamMap);
            stopWatch.stop();
            log.info("[apn] jobByUserSqlTask time [{} ms] \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return list;
        });
        resultList.addAll(c1.get());
        resultList.addAll(c2.get());
        return resultList;
    }

    private List<ReportJobByUserVo> convertReportJobByUserVoEntity(List<Map<String, Object>> mapList) {
        List<ReportJobByUserVo> reportJobByCompanyVoList = convertEntity(mapList, ReportJobByUserVo.class);
        List<ReportJobByUserVo> resultList = new ArrayList<>();
        reportJobByCompanyVoList = reportJobByCompanyVoList.stream().filter(vo -> ObjectUtil.isNotNull(vo.getUserId())).collect(Collectors.toList());
        Map<Long, List<ReportJobByUserVo>> longListMap = reportJobByCompanyVoList.stream().collect(Collectors.groupingBy(ReportJobByUserVo::getUserId));
        longListMap.forEach((k,v) -> {
            List<ReportJobByUserVo> applicationVoList = v.stream().filter(vo -> Objects.equals(vo.getType(), APPLICATION_TYPE)).collect(Collectors.toList());
            v.stream().filter(vo -> Objects.equals(vo.getType(), JOB_ID)).findFirst().ifPresent(job -> {
                ReportJobByUserVo jobVo = new ReportJobByUserVo();
                BeanUtil.copyProperties(job, jobVo);
                if (CollUtil.isNotEmpty(applicationVoList)) {
                    String[] ignore = new String[]{"jobIds", "openingCount", "jobOpenings"};
                    BeanUtil.copyProperties(applicationVoList.get(0), jobVo, ignore);
                }
                resultList.add(jobVo);
            });
        });
        return resultList;
    }

    @Override
    public void exportJobsByRecruiterByExcel(ReportJobParam reportParam, HttpServletResponse response) throws ExecutionException, InterruptedException {
        List<ReportJobByUserVo> reportJobByUserVoList = getJobsByUserSourceData(reportParam);
        ExcelUtil.downloadExcel(response, ReportJobByUserVo.class, reportJobByUserVoList, "", "", true);
    }

    private void addGrandTotal(List<ReportJobByUserVo> reportJobByUserVoList) {
        ReportJobByUserVo reportJobByUserVo = new ReportJobByUserVo();
        reportJobByUserVo.setUserName("Grand Total");
        if (CollUtil.isNotEmpty(reportJobByUserVoList)) {
            reportJobByUserVo.setOpeningCount(reportJobByUserVoList.stream().filter(vo -> StrUtil.isNotBlank(vo.getJobOpenings()))
                    .flatMap(vo -> Arrays.stream(vo.getJobOpenings().split(","))).distinct().mapToLong(a -> Long.parseLong(a.split("_")[1])).sum());
            reportJobByUserVo.setAppliedCount(reportJobByUserVoList.stream().filter(vo -> StrUtil.isNotBlank(vo.getAppliedIds())).flatMap(vo -> Arrays.stream(vo.getAppliedIds().split(","))).distinct().count());
            reportJobByUserVo.setSubmittedCount(reportJobByUserVoList.stream().filter(vo -> StrUtil.isNotBlank(vo.getSubmittedIds())).flatMap(vo -> Arrays.stream(vo.getSubmittedIds().split(","))).distinct().count());
            reportJobByUserVo.setInterviewCount(reportJobByUserVoList.stream().filter(vo -> StrUtil.isNotBlank(vo.getInterviewIds())).flatMap(vo -> Arrays.stream(vo.getInterviewIds().split(","))).distinct().count());
            reportJobByUserVo.setOfferedCount(reportJobByUserVoList.stream().filter(vo -> StrUtil.isNotBlank(vo.getOfferedIds())).flatMap(vo -> Arrays.stream(vo.getOfferedIds().split(","))).distinct().count());
            reportJobByUserVo.setOfferAcceptedCount(reportJobByUserVoList.stream().filter(vo -> StrUtil.isNotBlank(vo.getOfferAcceptedIds())).flatMap(vo -> Arrays.stream(vo.getOfferAcceptedIds().split(","))).distinct().count());
            reportJobByUserVo.setStartedCount(reportJobByUserVoList.stream().filter(vo -> StrUtil.isNotBlank(vo.getStartedIds())).flatMap(vo -> Arrays.stream(vo.getStartedIds().split(","))).distinct().count());
        }
        reportJobByUserVoList.forEach(vo -> {
            vo.setJobOpenings(null);
            vo.setAppliedIds(null);
            vo.setSubmittedIds(null);
            vo.setInterviewIds(null);
            vo.setOfferedIds(null);
            vo.setOfferAcceptedIds(null);
            vo.setStartedIds(null);
        });
        reportJobByUserVoList.add(reportJobByUserVo);
    }

    private void createJobByUserSql(StringBuilder sb, ReportJobParam reportParam, Map<Integer, Object> conditionParamMap) {
        StringBuilder applicationSql = new StringBuilder();
        createJobByUserApplicationSql(applicationSql, reportParam, conditionParamMap);
        StringBuilder jobSql = new StringBuilder();
        createJobByUserJobSql(jobSql, reportParam, conditionParamMap);
        sb.append(applicationSql).append(UNION_ALL).append(jobSql);
    }

    private void createJobByUserJobSql(StringBuilder sb, ReportJobParam reportParam, Map<Integer, Object> conditionParamMap) {
        StringBuilder sqlSb = new StringBuilder();
        StringBuilder jobSqlSb = new StringBuilder();
        jobSqlSb.append(" select u.id as user_id, u.first_name as u_first_name, u.last_name as u_last_name, j.id as job_id, j.openings, group_concat(j.id,'_',j.openings) as job_openings ")
                .append(" from user u ")
                .append(" inner join business_flow_administrator csla on csla.user_id = u.id and csla.sales_lead_role = 0 ")
                .append(" inner join job j on j.company_id = csla.company_id ")
                .append(" inner join recruitment_process rp on rp.id = j.recruitment_process_id ")
                .append(" where j.tenant_id = ?");
        conditionParamMap.put(conditionParamMap.size() + 1, SecurityUtils.getTenantId());
        jobSqlSb.append(conditionParamMap.size()).append(AND_SYMBOL);
        appendConditionSql(sqlSb, jobSqlSb, reportParam, conditionParamMap);
        sqlSb.append(" group by u.id,u.first_name, u.last_name, j.id, j.openings ");
        sb.append(" select a.u_first_name as u_first_name, a.u_last_name as u_last_name, a.user_id as user_id, sum(a.openings) as opening_count, ")
                .append(" 0 as applied_count, 0 submitted_count, 0 as interview_count, 0 as offered_count, ")
                .append(" 0 as offer_accepted_count, 0 as started_count, GROUP_CONCAT(a.job_id) as job_ids, '' as activity_ids, ")
                .append(" '' as applied_ids, '' as submitted_ids, '' as interview_ids, '' as offered_ids, ")
                .append(" '' as offer_accepted_ids, '' as started_ids, GROUP_CONCAT( a.job_openings ) AS job_openings, 'jobId' as type ")
                .append(" from ( ").append(sqlSb).append(" ) a group by a.user_id, a.u_first_name, a.u_last_name ");
    }

    private void createJobByUserApplicationSql(StringBuilder applicationSql, ReportJobParam reportParam, Map<Integer, Object> conditionParamMap) {
        StringBuilder sqlSb = new StringBuilder();
        sqlSb.append(" select u.first_name as u_first_name, u.last_name as u_last_name , ")
                .append(" u.id as user_id, 0 as opening_count, count(distinct trpstj.talent_recruitment_process_id) as applied_count, ")
                .append(" count(distinct trpstc.talent_recruitment_process_id) as submitted_count, count(distinct trpi.talent_recruitment_process_id) as interview_count, ")
                .append(" count(distinct trpo.talent_recruitment_process_id) as offered_count, count(distinct trpioa.talent_recruitment_process_id) as offer_accepted_count, count(distinct trpon.talent_recruitment_process_id) as started_count,  ")
                .append(" '' as job_ids, GROUP_CONCAT(DISTINCT trp.id) as activity_ids, ")
                .append(" GROUP_CONCAT(DISTINCT trpstj.talent_recruitment_process_id) as applied_ids, ")
                .append(" GROUP_CONCAT(DISTINCT trpstc.talent_recruitment_process_id) as submitted_ids, ")
                .append(" GROUP_CONCAT(DISTINCT trpi.talent_recruitment_process_id) as interview_ids, ")
                .append(" GROUP_CONCAT(DISTINCT trpo.talent_recruitment_process_id) as offered_ids, ")
                .append(" GROUP_CONCAT(DISTINCT trpioa.talent_recruitment_process_id) as offer_accepted_ids, ")
                .append(" GROUP_CONCAT(DISTINCT trpon.talent_recruitment_process_id) as started_ids, '' AS job_openings,")
                .append(" 'applicationId' as type ")
                .append(" from user u ")
                .append(" inner join business_flow_administrator csla on csla.user_id = u.id and csla.sales_lead_role = 0 ")
                .append(" inner join job j on j.company_id = csla.company_id ")
                .append(" INNER JOIN recruitment_process rp on rp.id = j.recruitment_process_id ")
                .append(" left join talent_recruitment_process trp on trp.job_id = j.id ")
                .append(" left join talent_recruitment_process_submit_to_job trpstj on trpstj.talent_recruitment_process_id = trp.id ")
                .append(" left join talent_recruitment_process_submit_to_client trpstc on trpstc.talent_recruitment_process_id = trp.id ")
                .append(" left join talent_recruitment_process_interview trpi on trpi.talent_recruitment_process_id = trp.id ")
                .append(" LEFT JOIN talent_recruitment_process_offer trpo ON trpo.talent_recruitment_process_id = trp.id ")
                .append(" LEFT JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = trp.id ")
                .append(" LEFT JOIN talent_recruitment_process_node trpon ON trpon.talent_recruitment_process_id = trp.id and trpon.node_type=60  and trpon.node_status = 1 ");
        sqlSb.append(" where j.tenant_id = ?1 and ");
        conditionParamMap.put(1, SecurityUtils.getTenantId());
        appendConditionSql(applicationSql, sqlSb, reportParam, conditionParamMap);
        applicationSql.append(" group by u.id,  u.first_name, u.last_name");
    }

    private void appendConditionSql(StringBuilder sb, StringBuilder tableSql, ReportJobParam reportParam, Map<Integer, Object> conditionParamMap) {
        super.appendBase(tableSql, reportParam, conditionParamMap);
//        if (CollUtil.isNotEmpty(reportParam.getTeamIds())) {
//            tableSql.append(" j.pteam_id in ?");
//            conditionParamMap.put(conditionParamMap.size() + 1, reportParam.getTeamIds());
//            tableSql.append(conditionParamMap.size()).append(AND_SYMBOL);
//        }
//        if (!SecurityUtils.isAdmin()) {
//            Integer dataScope = getDataScope();
//            Integer dataScopeRole = getRoleDateScope();
//            if (ObjectUtil.isNotEmpty(dataScopeRole)) {
//                dataScope = Math.max(dataScope, dataScopeRole);
//            }
//            if (ObjectUtil.equal(DataScope.PERMISSION_SELF.toDbValue(), dataScope)) {
//                tableSql.append(" u.id = ").append(SecurityUtils.getUserId()).append(AND_SYMBOL);
//            } else if (ObjectUtil.equal(DataScope.PERMISSION_TEAM.toDbValue(), dataScope) || ObjectUtil.equal(DataScope.PERMISSION_EXTRA_TEAM.toDbValue(), dataScope)) {
//                tableSql.append(" exists ( select 1 from (")
//                        .append(" select team_id from permission_user_team where user_id = ").append(SecurityUtils.getUserId())
//                        .append(UNION_ALL)
//                        .append(" select team_id from permission_extra_user_team where user_id = ").append(SecurityUtils.getUserId())
//                        .append(UNION_ALL)
//                        .append(" select pert.team_id as team_id from permission_extra_role_team pert left join user_role ur on ur.role_id = pert.role_id where ur.user_id = ").append(SecurityUtils.getUserId())
//                        .append(" ) put left join permission_user_team p on put.team_id = p.team_id where p.user_id = u.id ")
//                        .append(" ) ").append(AND_SYMBOL);
//            }
//        }
        //Concatenating the final sql
        sb.append(StrUtil.subBefore(tableSql, AND_SYMBOL, true));
    }

}
