package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum StartReportStatus implements ConvertedEnum<Integer> {

    OFFER_ACCEPTED(0, "Offer accepted"),
    OFFER_EXTENDED(1, "Offer extended");

    private final int dbValue;
    private final String name;

    StartReportStatus(int dbValue, String name) {
        this.dbValue = dbValue;
        this.name = name;
    }
    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }

    // static resolving:
    public static final ReverseEnumResolver<StartReportStatus, Integer> resolver =
            new ReverseEnumResolver<>(StartReportStatus.class, StartReportStatus::toDbValue);

    public static StartReportStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
