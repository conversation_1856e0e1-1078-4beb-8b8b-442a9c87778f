package com.altomni.apn.report.domain.enumeration;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;

public class JobStatusDataConverter implements Converter<JobStatus> {

    @Override
    public Class supportJavaTypeKey() {
        return JobStatus.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }


    @Override
    public WriteCellData<String> convertToExcelData(JobStatus jobStatus, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (jobStatus == null) {
            return new WriteCellData<>();
        }
        return new WriteCellData<>(jobStatus.name());
    }

}