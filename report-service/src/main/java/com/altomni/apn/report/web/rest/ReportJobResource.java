package com.altomni.apn.report.web.rest;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.ReportJobParam;
import com.altomni.apn.report.dto.ReportJobPerformanceDto;
import com.altomni.apn.report.dto.ReportParamDto;
import com.altomni.apn.report.dto.ReportPipelineDetailsDto;
import com.altomni.apn.report.service.ReportJobCompanyService;
import com.altomni.apn.report.service.ReportJobPerformanceService;
import com.altomni.apn.report.service.ReportJobUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ReportJobResource {

    @Resource(name = "reportJobService")
    private ReportJobCompanyService reportJobCompanyService;

    @Resource
    private ReportJobUserService reportJobUserService;

    @Resource
    private ReportJobPerformanceService reportJobPerformanceService;

    /**
     * get job company source data
     */
    @PostMapping("/report/job-company")
    public ResponseEntity<List<ReportJobByCompanyVo>> getJobsByCompanySourceData(@RequestBody ReportJobParam reportParam) throws ExecutionException, InterruptedException {
        log.info("[APN: ReportJobV2 @{}] REST request to get jobs by company source data. reportParam:[{}]", SecurityUtils.getUserId(), JSONUtil.toJsonStr(reportParam));
        return ResponseEntity.ok(reportJobCompanyService.getJobsByCompanySourceData(reportParam));
    }

    /**
     * get job details source data
     *
     * @return get source data from db to return.
     */
    @PostMapping("/report/job/details")
    public ResponseEntity<List<ReportJobDetailsOpeningsVo>> getActionJobDetailsSourceData(@RequestBody ReportParamDto reportParamDto) {
        log.info("[APN: ReportJob @{}] REST request to get job details source data. reportParam:{}", SecurityUtils.getUserId(), reportParamDto);
        return ResponseEntity.ok(reportJobCompanyService.getActionJobDetailsSourceData(reportParamDto));
    }

    /**
     * get job details source data
     *
     * @return get source data from db to return.
     */
    @PostMapping("/report/pipeline/details")
    public ResponseEntity<List<ReportPipelineDetailsStatusVo>> getActionPipelineDetailsSourceData(@RequestBody ReportPipelineDetailsDto reportPipelineDetailsDto) {
        log.info("[APN: ReportJob @{}] REST request to get job details source data. reportParam:{}", SecurityUtils.getUserId(), reportPipelineDetailsDto);
        return ResponseEntity.ok(reportJobCompanyService.getActionPipelineDetailsSourceData(reportPipelineDetailsDto));
    }

    @PostMapping("/report/job-company-excel")
    public void exportJobByCompanyByExcel(@RequestBody ReportJobParam reportParam, HttpServletResponse response) throws ExecutionException, InterruptedException {
        log.info("[APN: ReportJobV2 @{}] REST request to download jobs by company source data. reportParam:[{}]", SecurityUtils.getUserId(), JSONUtil.toJsonStr(reportParam));
        reportJobCompanyService.exportJobByCompanyByExcel(reportParam, response);
    }

    /**
     * export job details source data
     *
     * @return get source data from db to return.
     */
    @PostMapping("/report/job/details-excel")
    public void exportActionJobDetailsByExcel(@RequestBody ReportParamDto reportParamDto, HttpServletResponse response) {
        log.info("[APN: ReportJob @{}] REST request to download job details source data. reportParam:{}", SecurityUtils.getUserId(), reportParamDto);
        reportJobCompanyService.exportActionJobDetailsByExcel(reportParamDto, response);
    }

    /**
     * export job details source data
     *
     * @return export source data from db to return.
     */
    @PostMapping("/report/pipeline/details-excel")
    public void exportActivityDetailsByExcel(@RequestBody ReportPipelineDetailsDto reportPipelineDetailsDto, HttpServletResponse response) {
        log.info("[APN: ReportJob @{}] REST request to download job details source data. reportParam:{}", SecurityUtils.getUserId(), reportPipelineDetailsDto);
        reportJobCompanyService.exportActivityDetailsByExcel(reportPipelineDetailsDto, response);
    }


    /**
     * get jobs by AM source data
     */
    @PostMapping("/report/job-user")
    public ResponseEntity<List<ReportJobByUserVo>> getJobsByUserSourceData(@RequestBody ReportJobParam reportParam) throws ExecutionException, InterruptedException {
        log.info("[APN: ReportJobV2 @{}] REST request to get job by user source data. reportParam:{}", SecurityUtils.getUserId(), reportParam);
        return ResponseEntity.ok(reportJobUserService.getJobsByUserSourceData(reportParam));
    }

    /**
     * GET  /report/job/user-excel : get recruiter report with excel.
     *
     * @return get source data from db and make excel byte resource to return.
     */
    @PostMapping("/report/job-user-excel")
    public void exportJobsByRecruiterByExcel(HttpServletResponse response, @RequestBody ReportJobParam reportParam) throws ExecutionException, InterruptedException {
        log.info("[APN] REST request to download job user by excel. reportParam:{}", reportParam);
        reportJobUserService.exportJobsByRecruiterByExcel(reportParam, response);
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }

    @PostMapping("/report/job-performance-ratio")
    public ResponseEntity<List<ReportJobPerformanceRatioVo>> getJobPerformanceSourceRatioData(@RequestBody ReportJobPerformanceDto dto) {
        log.info("[APN: ReportJobPerformanceVo  @{}] REST request to query job performance ratio. RequestParam:{}", SecurityUtils.getUserId(), dto);
        return ResponseEntity.ok(reportJobPerformanceService.getJobPerformanceSourceRatioData(dto));
    }

    @PostMapping("/report/job-performance-detail")
    public ResponseEntity<List<ReportJobPerformanceDetailVo>> getJobPerformanceSourceDetailData(@RequestBody ReportJobPerformanceDto dto, @PageableDefault Pageable pageable) {
        log.info("[APN: ReportJobPerformanceVo  @{}] REST request to query job performance detail. RequestParam:{}", SecurityUtils.getUserId(), dto);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<ReportJobPerformanceDetailVo> resultList = reportJobPerformanceService.getJobPerformanceSourceDetailData(dto, pageable, headers);
        return new ResponseEntity<>(resultList, headers, HttpStatus.OK);
    }

    @PostMapping("/export/job-performance-detail")
    @NoRepeatSubmit
    public void exportJobPerformanceDetail(@RequestBody ReportJobPerformanceDto dto, @PageableDefault Pageable pageable, HttpServletResponse response) {
        log.info("[APN: ReportJobPerformance @{}] REST request to export job performance detail. RequestParam:{} ", SecurityUtils.getUserId(), dto);
        reportJobPerformanceService.exportJobPerformanceDetail(dto, pageable, response);
    }
}
