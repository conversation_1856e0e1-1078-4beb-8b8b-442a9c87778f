package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.config.SupersetProperties;
import com.altomni.apn.report.domain.Report;
import com.altomni.apn.report.domain.olap.ChartQueryBuilder;
import com.altomni.apn.report.domain.olap.QueryContext;
import com.altomni.apn.report.domain.olap.QueryDataProcessor;
import com.altomni.apn.report.dto.ReportDTO;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.ReportService;
import com.altomni.apn.report.service.mapper.ReportMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.openapitools.client.api.ChartsApi;
import org.openapitools.client.api.DatasetsApi;
import org.openapitools.client.model.*;
import org.openapitools.client.model.ChartDataFilter.OpEnum;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.util.*;
import java.util.function.Function;

@Slf4j
@Service
@RequiredArgsConstructor
public class ReportServiceImpl implements ReportService {

    private final ReportRepository reportRepository;
    private final ReportMapper reportMapper;
    private final DatasetsApi datasetsApi;
    private final ChartsApi chartsApi;
    private final SupersetProperties supersetProperties;
    private final EntityManager entityManager;
    private final CachePermission cachePermission;
    private final InitiationService initiationService;

    @Override
    public ApiV1DatasetGet200Response findAllDataSets() {
        GetListSchema queryParam = new GetListSchema();
        GetListSchemaFiltersInner filter = new GetListSchemaFiltersInner();
        filter.col("database").opr("rel_o_m").value(supersetProperties.getDatasourceId());
        queryParam.addFiltersItem(filter);

        try {
            return datasetsApi.apiV1DatasetGet(queryParam);
        } catch (Exception e) {
            log.error("[APN: Report] Superset get dataSets error", e);
            throw new ExternalServiceInterfaceException("Superset get dataSets error");
        }
    }

    @Override
    public DatasetRestApiGet findOneDataSet(Integer id) {
        try {
            ApiV1DatasetPkGet200Response dataSetResponse = datasetsApi.apiV1DatasetPkGet(id, null);
            return dataSetResponse.getResult();
        } catch (HttpClientErrorException.NotFound e) {
            throw new CustomParameterizedException("Dataset not found");
        } catch (Exception e) {
            log.error("[APN: Report] Superset get dataset error", e);
            throw new ExternalServiceInterfaceException("Superset get dataset error");
        }
    }

    @Override
    public List<OpEnum> getFilterOpsByDataType(String columnType) {
        List<OpEnum> ops;

        if (columnType.contains("INT")) {
            ops = Arrays.stream(OpEnum.values()).filter(op -> !OpEnum.withoutnumber().contains(op)).toList();
        } else if (columnType.contains("CHAR")) {
            ops = Arrays.stream(OpEnum.values()).filter(op -> !OpEnum.withoutString().contains(op)).toList();
        } else if (columnType.contains("TIME")) {
            ops = Arrays.stream(OpEnum.values()).filter(op -> !OpEnum.withoutTime().contains(op)).toList();
        } else {
            ops = Arrays.stream(OpEnum.values()).toList();
        }

        return ops;
    }


    @Override
    public Page<ReportDTO> findAllByTenantId(Pageable pageable) {
        Page<Report> reports = reportRepository.findAllByTenantId(SecurityUtils.getTenantId(), pageable);
        return reports.map(reportMapper::toDto);
    }

    @Override
    public ReportDTO findOne(Long id) {
        Report report = reportRepository.findByIdAndTenantId(id, SecurityUtils.getTenantId())
            .orElseThrow(() -> new CustomParameterizedException("Report not found"));
        return reportMapper.toDto(report);
    }

    @Override
    public ReportDTO create(ReportDTO reportDTO) {
        reportDTO.validate();
        Report report = reportRepository.save(reportMapper.toEntity(reportDTO));
        return reportMapper.toDto(report);
    }

    @Override
    public ReportDTO update(Long id, ReportDTO reportDTO) {
        reportDTO.validate();

        Report report = reportRepository.findByIdAndTenantId(id, SecurityUtils.getTenantId())
            .orElseThrow(() -> new CustomParameterizedException("Report not found"));
        report.userCanModify();

        report.setName(reportDTO.getName());
        report.setDescription(reportDTO.getDescription());
        report.setViewType(reportDTO.getViewType());
        report.setDatasetId(reportDTO.getDatasetId());
        if (reportDTO.getConfig() != null) {
            report.setConfig(ReportMapper.reportConfigToJsonString(reportDTO.getConfig()));
        }
        reportRepository.save(report);

        return reportMapper.toDto(report);
    }

    @Override
    public void delete(Long id) {
        Report report = reportRepository.findByIdAndTenantId(id, SecurityUtils.getTenantId())
            .orElseThrow(() -> new CustomParameterizedException("Report not found"));
        report.userCanModify();

        reportRepository.delete(report);
    }

    @Override
    public ChartDataResponseResult olapQuery(ReportDTO reportDTO) {
//        reportRepository.findByIdAndTenantId(reportDTO.getId(), SecurityUtils.getTenantId())
//            .orElseThrow(() -> new NotFoundException("Report not found"));


        DatasetRestApiGet dataSet = findOneDataSet(reportDTO.getDatasetId());
        QueryContext queryContext = new QueryContext().setReportDTO(reportDTO).setQueryConfig(reportDTO.getConfig())
            .setDataSet(dataSet);

        ChartDataQueryContextSchema chartQuery = ChartQueryBuilder.withContext(queryContext)
            .dataScopeFetcher(makeFetchDataScope())
            .teamIdsFetcher(makeFetchAuthedTeamId())
            .buildQuery();

        try {
            ChartDataResponseSchema chartDataResponse = chartsApi.apiV1ChartDataPost(chartQuery);
            if (CollectionUtils.isEmpty(chartDataResponse.getResult())) {
                return new ChartDataResponseResult();
            }
            ChartDataResponseResult result = chartDataResponse.getResult().stream().findFirst().get();
            log.info("[APN Report] Query SQL: {}", result.getQuery());
            return QueryDataProcessor.processAll(result, queryContext);
        } catch (HttpClientErrorException e) {
            log.error("[APN: Report] Superset query chart data param error", e);
            throw new CustomParameterizedException(e.getMessage());
        } catch (Exception e) {
            log.error("[APN: Report] Superset query chart data error", e);
            throw new ExternalServiceInterfaceException("Superset query chart data error");
        }

    }

    private Function<Long, Integer> makeFetchDataScope() {
        return userId -> {
            String dataScopeSql = " select data_scope from user where id = " + userId;
            Query dataScopeQuery = entityManager.createNativeQuery(dataScopeSql);
            int dataScope = Integer.parseInt(String.valueOf(dataScopeQuery.getSingleResult()));
            String dataScopeRoleSql = " select max(r.data_scope) from user_role ur left join role r on ur.role_id = r.id where ur.user_id = " + userId + " group by ur.user_id ";
            Query dataScopeRoleQuery = entityManager.createNativeQuery(dataScopeRoleSql);
            int dataScopeRole = Integer.parseInt(String.valueOf(dataScopeRoleQuery.getSingleResult()));

            return Math.max(dataScope, dataScopeRole);
        };
    }

    private Function<Long, List<Long>> makeFetchAuthedTeamId() {
        return userId -> {
            String sql = """
                    select team_id from permission_user_team where user_id = %s
                    union all
                    select team_id from permission_extra_user_team where user_id = %s
                    union all
                    select pert.team_id as team_id from permission_extra_role_team pert
                        left join user_role ur on ur.role_id = pert.role_id where ur.user_id = %s
                """.formatted(userId, userId, userId);
            Query query = entityManager.createNativeQuery(sql);
            return query.getResultList();
        };
    }

    @Override
    public List<Long> getNestedTeamIds(List<Long> teamIds) {
        log.debug("getNestedTeamIds -> teamIds {}", teamIds);
        if (!SecurityUtils.isAdmin()) {
            TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
            if (CollUtil.isNotEmpty(teamIds)) {
                teamIds = CollUtil.intersection(teamIds, teamDataPermission.getNestedTeamIds()).stream().toList();
            } else {
                teamIds = Objects.isNull(teamIds) ? new ArrayList<>() : teamIds;
                teamIds.addAll(teamDataPermission.getNestedTeamIds());
                log.debug("teamDataPermission.getNestedTeamIds()={}", teamDataPermission.getNestedTeamIds());
            }
        }
        return teamIds;
    }

    @Override
    public Long getUserIdFromDataPermission(Long userId) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        Long uId = -1L;
        if (teamDataPermission.getSelf()) {
            if (ObjectUtil.isEmpty(userId) || SecurityUtils.getUserId().equals(userId)) {
                uId = SecurityUtils.getUserId();
            }
        }else{
            uId = userId;
        }
        return uId;
    }
}
