package com.altomni.apn.report.domain.enumeration;

import cn.hutool.core.convert.Converter;
import com.altomni.apn.common.domain.enumeration.RateUnitType;

public class RateUnitTypeEnumConverter implements Converter<RateUnitType> {
    @Override
    public RateUnitType convert(Object value, RateUnitType defaultValue) throws IllegalArgumentException {
        if (value != null) {
            return RateUnitType.fromDbValue(Integer.parseInt(value + ""));
        }
        return null;
    }
}