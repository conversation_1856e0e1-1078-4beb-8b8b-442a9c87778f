package com.altomni.apn.report.domain.vo.s1;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.report.domain.vm.s1.TalentInfoVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Id;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ResignUserReportTalentVO extends AbstractAuditingEntity implements Serializable, AttachConfidentialTalent {

    private static final long serialVersionUID = -8813877969704820785L;

    @Id
    private Long id;

    @ApiModelProperty(value = "The tenant id the talent belongs.")
    private Long tenantId;

    @ApiModelProperty(value = "first name. required in US.")
    private String firstName;

    @ApiModelProperty(value = "last name. required in CN.")
    private String lastName;

    @ApiModelProperty(value = "full name. Either firstName & lastName (in US) or full name (in CN) are required. Either way, " +
            "the full name can not be null.")
    private String fullName;

    @ApiModelProperty(value = "talent's current job title")
    private String title;

    @ApiModelProperty(value = "talents' current job's company name")
    private String company;

    private Boolean confidentialTalentViewAble;

    private ConfidentialInfoDto confidentialInfo;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    @Override
    public Long getTalentId() {
        return id;
    }

    @Override
    public ConfidentialInfoDto getConfidentialInfo() {
        return confidentialInfo;
    }

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        this.confidentialInfo = confidentialInfo;
    }

    @Override
    public Boolean getConfidentialTalentViewAble() {
        return confidentialTalentViewAble;
    }

    @Override
    public void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble) {
        this.confidentialTalentViewAble = confidentialTalentViewAble;
    }

    @Override
    public void encrypt() {
        this.firstName = null;
        this.lastName = null;
        this.fullName = null;
        this.title = null;
        this.company = null;
    }

    public static ResignUserReportTalentVO fromTalentInfoVM(TalentInfoVM talentInfoVM) {
        ResignUserReportTalentVO resignUserReportTalentVO = new ResignUserReportTalentVO();
        ServiceUtils.myCopyProperties(talentInfoVM, resignUserReportTalentVO);
        resignUserReportTalentVO.setFullName(CommonUtils.formatFullName(talentInfoVM.getFirstName(), talentInfoVM.getLastName()));
        return resignUserReportTalentVO;
    }
}
