package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportCommissionVO;
import com.altomni.apn.report.domain.vo.ReportCommissionWithExcelDataVO;
import com.altomni.apn.report.dto.ReportCommissionSearchDTO;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

@Repository
public class ReportCommissionRepository extends BaseCustomRepository{

    public List<ReportCommissionVO> searchCommissionReportBaseData(ReportCommissionSearchDTO dto) {
        String sql = """
                SELECT
                	tsr.id,
                	t.full_name ,
                	tsr.regular_hours,
                	tsr.over_time,
                	tsr.double_time,
                	tsr.total_hours,
                	tsr.work_date,
                	tsr.work_date week_ending_date,
                	tsr.week_end week_end
                FROM
                	time_sheet_week_ending_record tsr
                	inner join talent t on t.id = tsr.talent_id
                WHERE
                    tsr.tenant_id = :tenantId
                    AND tsr.work_date BETWEEN :from AND :to
                """ + getOrderSql(dto.getSort());
        int startOffset = dto.getPage() * dto.getSize();
        return entityManager.createNativeQuery(sql + "  limit :start,:end ", ReportCommissionVO.class)
                .setParameter("from", dto.getFrom()).setParameter("to", dto.getTo())
                .setParameter("start", startOffset).setParameter("end", dto.getSize())
                .setParameter("tenantId", SecurityUtils.getTenantId()).getResultList();
    }

    public Long countCommissionReportBaseData(ReportCommissionSearchDTO dto) {
        String sql = """
                SELECT
                	count(tsr.id)
                FROM
                	time_sheet_week_ending_record tsr
                WHERE
                    tsr.tenant_id = :tenantId
                    AND tsr.work_date BETWEEN :from AND :to
                """;
        Query countQ = entityManager.createNativeQuery(sql)
                .setParameter("from", dto.getFrom()).setParameter("to", dto.getTo())
                .setParameter("tenantId", SecurityUtils.getTenantId());
        return Long.parseLong(String.valueOf(countQ.getSingleResult()));
    }

    public List<ReportCommissionWithExcelDataVO> searchCommissionReportData(ReportCommissionSearchDTO dto) {
        String sql = """
                SELECT
                tsr.id,
                tsr.assignment_id,
                t.full_name,
                t.first_name,
                t.last_name,
                tsr.week_ending_date week_ending_date,
                tsr.week_end week_end,
                c.`full_business_name` company,
                cslcct.full_name billing_contact,
                j.id job_id,
                abi.net_bill_Rate net_bill_Rate,
                tsr.last_modified_date,
                CASE WHEN tsr.last_modified_by LIKE '%\\_%' THEN(SELECT t.full_name FROM talent t WHERE t.id = SUBSTRING_INDEX( tsr.last_modified_by, '_', 1 )) WHEN tsr.last_modified_by LIKE '%\\,%' THEN(SELECT(CASE CONCAT( u.first_name, u.last_name ) REGEXP '[一-龥]' WHEN 1 THEN CONCAT( u.last_name, u.first_name ) ELSE CONCAT( u.first_name, " ", u.last_name ) END ) FROM USER u WHERE u.id = SUBSTRING_INDEX( tsr.last_modified_by, ',', 1 )) ELSE 'system' END last_modified_by,
                j.title job_title,
                j.`code` job_code,
                al.city working_city,
                al.province working_state,
                al.country working_country,
                abi.assignment_division,
                s.charge_number,
                s.tvc_number,
                (select GROUP_CONCAT((case CONCAT(t.first_name,t.last_name)  regexp '[一-龥]' when 1 then CONCAT(t.last_name,t.first_name) ELSE CONCAT(t.first_name, ' ',t.last_name) END)) from assignment_contribution ac left join user t on t.id = ac.user_id where ac.assignment_id = tta.id and ac.user_role = 0 group by tsr.assignment_id) am,
                (select GROUP_CONCAT((case CONCAT(t.first_name,t.last_name)  regexp '[一-龥]' when 1 then CONCAT(t.last_name,t.first_name) ELSE CONCAT(t.first_name, ' ',t.last_name) END)) from assignment_contribution ac left join user t on t.id = ac.user_id where ac.assignment_id = tta.id and ac.user_role = 3 group by tsr.assignment_id) dm,
                (select GROUP_CONCAT((case CONCAT(t.first_name,t.last_name)  regexp '[一-龥]' when 1 then CONCAT(t.last_name,t.first_name) ELSE CONCAT(t.first_name, ' ',t.last_name) END)) from assignment_contribution ac left join user t on t.id = ac.user_id where ac.assignment_id = tta.id and ac.user_role = 5 group by tsr.assignment_id) ac,
                (select GROUP_CONCAT((case CONCAT(t.first_name,t.last_name)  regexp '[一-龥]' when 1 then CONCAT(t.last_name,t.first_name) ELSE CONCAT(t.first_name, ' ',t.last_name) END)) from assignment_contribution ac left join user t on t.id = ac.user_id where ac.assignment_id = tta.id and ac.user_role = 1 group by tsr.assignment_id) recruiter,
                (select GROUP_CONCAT((case CONCAT(t.first_name,t.last_name)  regexp '[一-龥]' when 1 then CONCAT(t.last_name,t.first_name) ELSE CONCAT(t.first_name, ' ',t.last_name) END)) from assignment_contribution ac left join user t on t.id = ac.user_id where ac.assignment_id = tta.id and ac.user_role = 6 group by tsr.assignment_id) primary_recruiter,
                (select GROUP_CONCAT((case CONCAT(t.first_name,t.last_name)  regexp '[一-龥]' when 1 then CONCAT(t.last_name,t.first_name) ELSE CONCAT(t.first_name, ' ',t.last_name) END)) from assignment_contribution ac left join user t on t.id = ac.user_id where ac.assignment_id = tta.id and ac.user_role = 2 group by tsr.assignment_id) sourcer,
                (select GROUP_CONCAT((case CONCAT(t.first_name,t.last_name)  regexp '[一-龥]' when 1 then CONCAT(t.last_name,t.first_name) ELSE CONCAT(t.first_name, ' ',t.last_name) END)) from assignment_contribution ac left join user t on t.id = ac.user_id where ac.assignment_id = tta.id and ac.user_role = 4 group by tsr.assignment_id) owner,
                (select GROUP_CONCAT((case CONCAT(t.first_name,t.last_name)  regexp '[一-龥]' when 1 then CONCAT(t.last_name,t.first_name) ELSE CONCAT(t.first_name, ' ',t.last_name) END)) from assignment_contribution ac left join user t on t.id = ac.user_id where ac.assignment_id = tta.id and ac.user_role = 8 group by tsr.assignment_id) bd_owner,
                (select GROUP_CONCAT((case CONCAT(t.first_name,t.last_name)  regexp '[一-龥]' when 1 then CONCAT(t.last_name,t.first_name) ELSE CONCAT(t.first_name, ' ',t.last_name) END)) from assignment_contribution ac left join user t on t.id = ac.user_id where ac.assignment_id = tta.id and ac.user_role = 9 group by tsr.assignment_id) sales_lead_owner,
                (select GROUP_CONCAT(ac.percentage) from assignment_contribution ac  where ac.assignment_id = tta.id and ac.user_role = 0 group by tsr.assignment_id) am_contribution,
                (select GROUP_CONCAT(ac.percentage) from assignment_contribution ac  where ac.assignment_id = tta.id and ac.user_role = 3 group by tsr.assignment_id) dm_contribution,
                (select GROUP_CONCAT(ac.percentage) from assignment_contribution ac  where ac.assignment_id = tta.id and ac.user_role = 5 group by tsr.assignment_id) ac_contribution,
                (select GROUP_CONCAT(ac.percentage) from assignment_contribution ac  where ac.assignment_id = tta.id and ac.user_role = 1 group by tsr.assignment_id) recruiter_contribution,
                (select GROUP_CONCAT(ac.percentage) from assignment_contribution ac  where ac.assignment_id = tta.id and ac.user_role = 6 group by tsr.assignment_id) primary_recruiter_contribution,
                (select GROUP_CONCAT(ac.percentage) from assignment_contribution ac  where ac.assignment_id = tta.id and ac.user_role = 2 group by tsr.assignment_id) sourcer_contribution,
                (select GROUP_CONCAT(ac.percentage) from assignment_contribution ac  where ac.assignment_id = tta.id and ac.user_role = 4 group by tsr.assignment_id) owner_contribution,
                (select GROUP_CONCAT(ac.percentage) from assignment_contribution ac  where ac.assignment_id = tta.id and ac.user_role = 8 group by tsr.assignment_id) bd_owner_contribution,
                (select GROUP_CONCAT(ac.percentage) from assignment_contribution ac  where ac.assignment_id = tta.id and ac.user_role = 9 group by tsr.assignment_id) sales_lead_owner_contribution,
                ( SELECT contact FROM talent_contact WHERE talent_id = tta.talent_id AND jhi_type = 2 AND STATUS = 0 ORDER BY sort ASC LIMIT 1 ) email,
                ( SELECT contact FROM talent_contact WHERE talent_id = tta.talent_id AND jhi_type = 16 AND STATUS = 0 ORDER BY sort ASC LIMIT 1 ) phone,
                tsr.approved_date approved_on,
                IF( tsr.am_approver_id IS NULL, IF ( tsr.manager_id IS NULL, cslccappt.full_name, tsr.manager ), tsr.am_approver ) approver_name,
                approverUser.email approver_email,
                approverUser.phone approver_phone,
                primaryuser.email primary_email,
                (SELECT contact FROM talent_contact WHERE talent_id = cslccapp.talent_id AND jhi_type = 16 AND STATUS = 0 ORDER BY sort ASC LIMIT 1 ) primary_phone,
                manageruser.email manager_email,
                (SELECT contact FROM talent_contact WHERE talent_id = manager.talent_id AND jhi_type = 16 AND STATUS = 0 ORDER BY sort ASC LIMIT 1 ) manager_phone,
                api.corporation corporation,
                IF( st.id IS NULL, tta.employment_category, concat('Past', "-", tta.employment_category) ) employee_status,
                uu.custom_timezone time_zone,
                aprOverBill.pay_rate over_bill_rate,
                aprOverBill.time_unit over_bill_time_unit,
                ecOverBill.symbol over_bill_currency,
                aprOverPay.pay_rate over_pay_rate,
                aprOverPay.time_unit over_pay_time_unit,
                ecOverPay.symbol over_pay_currency,
                aprDoubleBill.pay_rate double_bill_rate,
                aprDoubleBill.time_unit double_bill_time_unit,
                ecDoubleBill.symbol double_bill_currency,
                aprDoublePay.pay_rate double_pay_rate,
                aprDoublePay.time_unit double_pay_time_unit,
                ecDoublePay.symbol double_pay_currency,
                aprBill.pay_rate bill_rate,
                aprBill.time_unit bill_time_unit,
                ecBill.symbol bill_currency,
                aprPay.pay_rate pay_rate,
                aprPay.time_unit pay_time_unit,
                ecPay.symbol pay_currency,
                tsr.`status`,
                api.frequency payment_frequency,
                AT.frequency billing_frequency,
                tc.comments employee_comments,
                tarr.opinion approver_comments,
                tta.start_date,
                tta.end_date,
                tsr.regular_hours regular_hours,
                tsr.over_time  over_time,
                tsr.double_time double_time,
                tsr.total_hours total_hours,
                tsr.work_date work_date
              FROM
                time_sheet_week_ending_record tsr
                INNER JOIN timesheet_talent_assignment tta ON tta.id = tsr.assignment_id
                LEFT JOIN timesheet_comments tc ON tc.assignment_id = tsr.assignment_id AND tc.work_date = tsr.work_date AND tc.comments_type = 0
                INNER JOIN talent t ON t.id = tta.talent_id
                INNER JOIN company c ON c.id = tta.company_id
                INNER JOIN job j ON j.id = tta.job_id
                INNER JOIN START s ON s.id = tta.start_id
                INNER JOIN assignment_location al ON al.assignment_id = tta.id
                INNER JOIN assignment_bill_info abi ON abi.assignment_id = tta.id
                INNER JOIN assignment_pay_info api ON api.assignment_id = tta.id
                LEFT JOIN company_sales_lead_client_contact cslcc ON cslcc.id = abi.contact_id
                LEFT JOIN talent cslcct ON cslcct.id = cslcc.talent_id
                LEFT JOIN timesheet_manager tm ON tm.assignment_id = tta.id AND tm.role = 0
                LEFT JOIN company_sales_lead_client_contact cslccapp ON cslccapp.id = tm.client_id
                LEFT JOIN talent cslccappt ON cslccappt.id = cslccapp.talent_id
                LEFT JOIN time_sheet_user primaryuser ON primaryuser.id = cslccapp.approver_id
                LEFT JOIN start_termination st ON st.start_id = tta.start_id
                LEFT JOIN assignment_timesheet AT ON AT.assignment_id = tta.id
                LEFT JOIN timesheet_approve_record tarr ON tarr.record_id = tsr.record_id
                LEFT JOIN user approverUser ON approverUser.id = tsr.am_approver_id
                left join user uu on uu.id = :userId
                LEFT JOIN company_sales_lead_client_contact manager ON manager.id = tsr.manager_id
                LEFT JOIN time_sheet_user manageruser ON manageruser.id = manager.approver_id
                LEFT JOIN talent managert ON managert.id = manager.talent_id
                LEFT JOIN assignment_pay_rate aprBill ON aprBill.assignment_id = tta.id AND aprBill.pay_type = 2 AND aprBill.content_type = 0
                LEFT JOIN enum_currency ecBill ON ecBill.id = aprBill.currency
                LEFT JOIN assignment_pay_rate aprPay ON aprPay.assignment_id = tta.id AND aprPay.pay_type = 3 AND aprPay.content_type = 1
                LEFT JOIN enum_currency ecPay ON ecPay.id = aprPay.currency
                LEFT JOIN assignment_pay_rate aprOverBill ON aprOverBill.assignment_id = tta.id AND aprOverBill.pay_type = 0 AND aprOverBill.content_type = 0
                LEFT JOIN enum_currency ecOverBill ON ecOverBill.id = aprOverBill.currency
                LEFT JOIN assignment_pay_rate aprOverPay ON aprOverPay.assignment_id = tta.id AND aprOverPay.pay_type = 0	AND aprOverPay.content_type = 1
                LEFT JOIN enum_currency ecOverPay ON ecOverPay.id = aprOverPay.currency
                LEFT JOIN assignment_pay_rate aprDoubleBill ON aprDoubleBill.assignment_id = tta.id AND aprDoubleBill.pay_type = 1 AND aprDoubleBill.content_type = 0
                LEFT JOIN enum_currency ecDoubleBill ON ecDoubleBill.id = aprDoubleBill.currency
                LEFT JOIN assignment_pay_rate aprDoublePay ON aprDoublePay.assignment_id = tta.id AND aprDoublePay.pay_type = 1 AND aprDoublePay.content_type = 1
                LEFT JOIN enum_currency ecDoublePay ON ecDoublePay.id = aprDoublePay.currency
              WHERE
                tsr.tenant_id = :tenantId
                AND tsr.work_date BETWEEN :from AND :to
              GROUP BY
                tsr.assignment_id,tsr.work_date
                """+ getOrderSql(dto.getSort());
        return entityManager.createNativeQuery(sql, ReportCommissionWithExcelDataVO.class)
                .setParameter("userId", SecurityUtils.getUserId())
                .setParameter("from", dto.getFrom()).setParameter("to", dto.getTo())
                .setParameter("tenantId", SecurityUtils.getTenantId()).getResultList();
    }

    private String getOrderSql(SearchSortDTO sort) {
        StringBuilder sb = new StringBuilder();
        if (sort != null) {
            String column = StrUtil.toUnderlineCase(sort.getProperty());
            if (CollUtil.newArrayList("full_name").contains(column)) {
                column = "t." + column;
            }
            sb.append(" order by CASE WHEN IFNULL( ").append(column).append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            if (CollUtil.newArrayList("t.full_name").contains(column)) {
                sb.append("CONVERT( ").append(column).append(" USING gbk) ");
                sb.append(" ").append(sort.getDirection());
            } else {
                sb.append(column);
                sb.append(" ").append(sort.getDirection());
            }
        } else {
            sb.append(" order by tsr.work_date ASC ");
        }
        return sb.toString();
    }
    
}
