package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.enumeration.ReportUserCandidateSearchType;
import com.altomni.apn.report.domain.vo.BDActivityReportDetailVO;
import com.altomni.apn.report.domain.vo.BDActivityReportVO;
import com.altomni.apn.report.dto.BDActivitySearchDTO;
import com.altomni.apn.report.dto.BDActivitySearchDeatilDTO;
import com.altomni.apn.report.dto.UserTeamPariDTO;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.BdActivityReportService;
import com.altomni.apn.report.service.user.UserService;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BdActivityReportServiceImpl implements BdActivityReportService {

    @Resource
    private UserService userService;
    @Resource
    private CachePermission cachePermission;
    @Resource
    private InitiationService initiationService;
    @Resource
    private ReportRepository reportRepository;
    @Resource
    private HttpService httpService;

    @Value("${application.crmUrl}")
    private String crmUrl;

    private final static String BD_ACTIVITY_REPORT_LIST_URL = "/report/api/v1/bd-activity-report/list";

    private final static String BD_ACTIVITY_REPORT_DETAIL_URL = "/report/api/v1/bd-activity-report/detail";


    @Override
    public List<BDActivityReportVO> countBdActivityContactType(BDActivitySearchDTO searchDTO, Pageable pageable, HttpHeaders headers) throws IOException {

        Set<Long> userIds = new HashSet<>();
        if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(searchDTO.getTargetIds())) {
            userIds = getIdsFromSearchVo(searchDTO);
            if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(userIds)) {
                return null;
            }
        } else {
            userIds = getAllIds();
        }

        searchDTO.setTargetIds(userIds);
        searchDTO.setTargetType(ReportUserCandidateSearchType.USER);
        //调用CRM 接口
        return callBdActivityUrl(searchDTO, pageable);
    }

    @Override
    public List<BDActivityReportDetailVO> getBdActivityDetail(BDActivitySearchDeatilDTO searchDTO, Pageable pageable, HttpHeaders headers) throws IOException, ExecutionException, InterruptedException {
        Long crmUserId = searchDTO.getUserId();
        searchDTO.setUserId(crmUserId);
        List<BDActivityReportDetailVO> detailList = callBdActivityDetailUrl(searchDTO, pageable);
        Set<Long> userIds = new HashSet<>();
        detailList.forEach(t -> {
            if (CollUtil.isNotEmpty(t.getSalesLeadOwnerIds())) {
                userIds.addAll(t.getSalesLeadOwnerIds());
            }
            if (CollUtil.isNotEmpty(t.getBdOwnerIds())) {
                userIds.addAll(t.getBdOwnerIds());
            }
        });
        if (CollUtil.isNotEmpty(userIds)) {
            Map<Long, UserUidNameDTO> userNameMap = userService.getAllUserNameByIdInOrTenant(userIds).getBody();
            if (null != userNameMap) {
                detailList.forEach(t -> {
                    if (CollUtil.isNotEmpty(t.getSalesLeadOwnerIds())) {
                        t.setSalesLeadOwners(t.getSalesLeadOwnerIds().stream().map(slo -> {
                            return userNameMap.getOrDefault(slo, null);
                        }).collect(Collectors.toList()));
                    }
                    if (CollUtil.isNotEmpty(t.getBdOwnerIds())) {
                        t.setBdOwners(t.getBdOwnerIds().stream().map(bo -> {
                            return userNameMap.getOrDefault(bo, null);
                        }).collect(Collectors.toList()));
                    }
                });
            }
        }

        return detailList;
    }

    private Set<Long> getAllIds() {
        return userService.getAllBriefUsers().getBody().stream().map(UserBriefDTO::getId).collect(Collectors.toSet());
    }

    private Set<Long> getIdsFromSearchVo(BDActivitySearchDTO searchDTO) {
        // 查询条件包含 TeamIds ,根据 TeamIds 获取 userIds
        if (ReportUserCandidateSearchType.TEAM.equals(searchDTO.getTargetType())) {
            // 根据 teamIds 查询 userIds
            PermissionTeamMemberSearchVM permissionTeamMemberSearchVM = new PermissionTeamMemberSearchVM();
            permissionTeamMemberSearchVM.setTeamIds(searchDTO.getTargetIds());
            // 调用userService 根据TeamIds获取User
            List<PermissionTeamUserDTO> activeUsers = userService.getActiveTeamMembersByTeamIds(permissionTeamMemberSearchVM).getBody();
            if (com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(activeUsers)) {
                return null;
            } else {
                return activeUsers.stream().map(PermissionTeamUserDTO::getId).collect(Collectors.toSet());
            }
        } else if (ReportUserCandidateSearchType.USER.equals(searchDTO.getTargetType())) {
            return searchDTO.getTargetIds();
        }
        return null;
    }

    public Headers getRequestHeaders() {
        Map<String, String> headersBuilder = new HashMap<>();
        String currentUserToken = SecurityUtils.getCurrentUserToken();
        headersBuilder.put("Authorization", "Bearer " + currentUserToken);
        return Headers.of(headersBuilder);
    }

    public String getBdActivityListUrl(Pageable pageable) {
        return getBaseUrl(BD_ACTIVITY_REPORT_LIST_URL, pageable);

    }

    public String getBdActivityDetailUrl(Pageable pageable) {
        return getBaseUrl(BD_ACTIVITY_REPORT_DETAIL_URL, pageable);
    }

    public String getBaseUrl(String extendUrl, Pageable pageable) {
        String url = crmUrl + extendUrl;
        if (ObjectUtil.isNotEmpty(pageable)) {
            url = PaginationUtil.addUrlFromPageable(pageable, url);
        }
        return url;
    }

    public List<BDActivityReportVO> callBdActivityUrl(BDActivitySearchDTO searchDTO, Pageable pageable) throws IOException {
        String groupParamStr = JSON.toJSONString(searchDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(getBdActivityListUrl(pageable), getRequestHeaders(), groupParamStr);
        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[BdActivityService: BdActivityServiceImpl] query BdActivity error, response code: {}, response message: {}", response.getCode(), response.getBody());
                throw new IOException("Call CRM BdActivityList Failed.");
            } else {
                return JSONUtil.toList(JSONUtil.parseArray(response.getBody()), BDActivityReportVO.class);
            }
        } else {
            log.error("[BdActivityService: BdActivityServiceImpl @{}] query BdActivity error and response is null", SecurityUtils.getUserId());
            throw new IOException("Call CRM BdActivityList Failed.");
        }
    }

    public List<BDActivityReportDetailVO> callBdActivityDetailUrl(BDActivitySearchDeatilDTO searchDTO, Pageable pageable) throws IOException {
        String groupParamStr = JSON.toJSONString(searchDTO, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(getBdActivityDetailUrl(pageable), getRequestHeaders(), groupParamStr);
        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[BdActivityService: BdActivityServiceImpl] query BdActivity error, response code: {}, response message: {}", response.getCode(), response.getBody());
                throw new IOException("Call CRM BdActivityList Failed.");
            } else {
                return JSONUtil.toList(JSONUtil.parseArray(response.getBody()), BDActivityReportDetailVO.class);
            }
        } else {
            log.error("[BdActivityService: BdActivityServiceImpl @{}] query BdActivity error and response is null", SecurityUtils.getUserId());
            throw new IOException("Call CRM BdActivityList Failed.");
        }
    }

    private boolean hasValidUserIds(Set<Long> userIds, Set<Long> teamIds) {
        PermissionTeamMemberSearchVM searchVM = new PermissionTeamMemberSearchVM();
        searchVM.setTeamIds(teamIds);
        Set<Long> userIdsByTeams = Objects.requireNonNull(userService.getPlainTeamMembersByTeamIds(searchVM).getBody())
                .stream().map(PermissionTeamMemberDTO::getId).collect(Collectors.toSet());
        userIds.addAll(userIdsByTeams);
        return CollectionUtils.isNotEmpty(userIdsByTeams);
    }

}
