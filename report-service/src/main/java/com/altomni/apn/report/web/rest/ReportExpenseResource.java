package com.altomni.apn.report.web.rest;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ContractorTerminationVo;
import com.altomni.apn.report.domain.vo.ExpenseReportVO;
import com.altomni.apn.report.dto.ContractorSearchDto;
import com.altomni.apn.report.dto.ExpenseReportDTO;
import com.altomni.apn.report.service.ExpenseReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ReportExpenseResource {

    @Resource
    private ExpenseReportService expenseReportService;

    @PostMapping("/report/expense-report")
    public ResponseEntity<List<ExpenseReportVO>> getContractorTerminations(@RequestBody ExpenseReportDTO expenseReportDTO, Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[APN: expense report @{}] REST request to get contractor expense report by searchDto:[{}]", SecurityUtils.getUserId(), JSONUtil.toJsonStr(expenseReportDTO));
        StopWatch stopWatch = new StopWatch("getContractorTerminations");
        stopWatch.start();
        Page<ExpenseReportVO> page = expenseReportService.getExpenseReport(expenseReportDTO, pageable);
        stopWatch.stop();
        log.info("[APN: expense report @{}] time = {}ms \n {}",SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/report");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/report/expense-report-excel")
    public void exportContractorTerminations(@RequestBody ExpenseReportDTO searchDto, HttpServletResponse response) {
        log.info("[APN: expense report @{}] REST request to get contractor expense report excel by searchDto:[{}]", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        expenseReportService.exportExpenseReport(searchDto, response);
    }


}
