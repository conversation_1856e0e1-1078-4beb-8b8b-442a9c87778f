package com.altomni.apn.report.domain.vm.s1;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.Instant;

@Entity
@Data
public class JobCompanyVM extends AbstractAuditingEntity {

    @Id
    private Long id;

    private String title;

    private Long tenantId;

    private Long companyId;

    private JobType jobType;

    private JobStatus status;

    private String company;

    private Instant postingTime;

    private Long jobPteamId;

}
