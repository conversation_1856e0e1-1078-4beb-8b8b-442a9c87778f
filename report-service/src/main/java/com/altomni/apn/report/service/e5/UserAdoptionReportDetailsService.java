package com.altomni.apn.report.service.e5;

import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.vo.RecruitingKpiApplicationBaseDetailVO;
import com.altomni.apn.report.domain.vo.e5.EmailDetailReportVO;
import com.altomni.apn.report.domain.vo.e5.TeamAdoptionReportVO;
import com.altomni.apn.report.domain.vo.voip.VoipDetailReportVO;
import com.altomni.apn.report.dto.e5.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

public interface UserAdoptionReportDetailsService {

    Map<Long, int[]> getCallReport(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds);

    Set<Long> getReallyUserIds(UserAdoptionReportDTO userAdoptionReportDTO);

    List<TeamAdoptionReportVO> getCallReportByTeamView(UserAdoptionReportDTO userAdoptionReportDTO, List<TeamInfoVO> teamInfoVOS);

    Map<String,Integer> getCallReportForTotal(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds);

    List<VoipDetailReportVO> getCallDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable);

    void downloadCallDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response);

    Map<Long, UserAdoptionReportApplicationStatsDTO> getApplicationStatsReport(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds);

    Map<Long, UserAdoptionReportApplicationStatsDTO> getApplicationStatsReportByTeamView(UserAdoptionReportDTO userAdoptionReportDTO);

    List<RecruitingKpiByUserVO> getApplicationStatsReportForTotal(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds);

    Page<? extends RecruitingKpiApplicationBaseDetailVO> getApplicationDetailsReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, ReportApplicationStatus applicationDetailReportType, Pageable pageable);

    void downloadApplicationDetailsReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, ReportApplicationStatus applicationDetailReportType, HttpServletResponse response);

    Page<?> getNoteDetailReport(UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable);

    void downloadNoteDetailReport(UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response);

    void downloadNoteDetailReportV2(UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response, Pageable pageable);

    List<EmailDetailReportVO> getEmailDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable);

    void downloadEmailDetailReport(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response);

    void downloadEmailDetailReportV2(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response,Pageable pageable);

    List<UserAdoptionReportCrmNoteStatsDTO> getCrmNoteReport(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds);

    Map<Long, List<UserAdoptionReportCrmNoteStatsDTO>> getCrmNoteReportByTeamView(UserAdoptionReportDTO userAdoptionReportDTO, List<TeamInfoVO> teamInfoVOS);

    UserAdoptionReportCrmNoteStatsDTO getCrmNoteReportForTotal(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> userIds);

}
