package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentCategoryType;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentCategoryTypeConverter;
import com.altomni.apn.report.domain.enumeration.AssignmentCategoryTypeDataConverter;
import com.altomni.apn.report.domain.enumeration.LocalDateDataConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportGpWithExcelVO {

    @Id
    @ExcelIgnore
    private Long id;

    @ExcelProperty(value = "Employee Name", index = 0)
    private String fullName;

    @ExcelProperty(value = "Employment Category", index = 1, converter = AssignmentCategoryTypeDataConverter.class)
    @Convert(converter = AssignmentCategoryTypeConverter.class)
    private AssignmentCategoryType startEmploymentCategory;

    @ExcelProperty(value = "Job Title", index = 2)
    private String jobTitle;

    @ExcelProperty(value = "Job Company", index = 3)
    private String companyName;

    @ExcelProperty(value = "Start Date", index = 4, converter = LocalDateDataConverter.class)
    private LocalDate startDate;

    @ExcelProperty(value = "End Date", index = 5, converter = LocalDateDataConverter.class)
    private LocalDate endDate;

    @ExcelIgnore
    private BigDecimal billRate;

    @Transient
    @ExcelProperty(value = "Bill Rate", index = 6)
    private String billRateStr;

    public String getBillRateStr() {
        if (billRate == null) {
            return "";
        }
        return billRate.setScale(2, RoundingMode.DOWN).toPlainString();
    }

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType billRateUnit;

    @Transient
    @ExcelProperty(value = "Bill Rate Unit", index = 7)
    private String billRateUnitFormat;

    public String getBillRateUnitFormat() {
        if (ObjectUtil.isEmpty(billRateUnit)) {
            return "";
        }
        return becSymbol + "/" + billRateUnit.name().substring(0, 1).toUpperCase();
    }

    @ExcelIgnore
    private BigDecimal salary;

    @Transient
    @ExcelProperty(value = "Salary", index = 8)
    private String salaryStr;

    public String getSalaryStr() {
        if (salary == null) {
            return "";
        }
        return salary.setScale(2, RoundingMode.DOWN).toPlainString();
    }

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType salaryUnit;

    @Transient
    @ExcelProperty(value = "Salary Unit", index = 9)
    private String salaryUnitFormat;

    public String getSalaryUnitFormat() {
        if (ObjectUtil.isEmpty(salaryUnit)) {
            return "";
        }
        return pecSymbol + "/" + salaryUnit.name().substring(0, 1).toUpperCase();
    }


    @ExcelProperty(value = "AM", index = 10)
    private String am;

    @ExcelProperty(value = "AM Contribution Split %", index = 11)
    private String amContribution;

    @ExcelProperty(value = "DM", index = 12)
    private String dm;

    @ExcelProperty(value = "DM Contribution Split %", index = 13)
    private String dmContribution;

    @ExcelProperty(value = "Recruiter", index = 14)
    private String recruiter;

    @ExcelProperty(value = "Recruiter Contribution Split %", index = 15)
    private String recruiterContribution;

    @ExcelProperty(value = "AC", index = 16)
    private String ac;

    @ExcelProperty(value = "AC Contribution Split %", index = 17)
    private String acContribution;

    @ExcelProperty(value = "Sourcer", index = 18)
    private String source;

    @ExcelProperty(value = "Sourcer Contribution Split %", index = 19)
    private String sourceContribution;

    @ExcelProperty(value = "Owner", index = 20)
    private String owner;

    @ExcelProperty(value = "Owner Contribution Split %", index = 21)
    private String ownerContribution;

    @ExcelProperty(value = "Sales Lead Owner", index = 22)
    private String salesLeadOwner;

    @ExcelProperty(value = "Sales Lead Owner Contribution Split %", index = 23)
    private String salesLeadOwnerContribution;

    @ExcelProperty(value = "BD Owner", index = 24)
    private String bdOwner;

    @ExcelProperty(value = "BD Owner Contribution Split %", index = 25)
    private String bdOwnerContribution;

    @ExcelIgnore
    private String pecSymbol;

    @ExcelIgnore
    private String becSymbol;

    @ExcelProperty(value = "Email Address", index = 26)
    private String emails;

}
