package com.altomni.apn.report.web.rest;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.TimeSheetReportBaseVo;
import com.altomni.apn.report.dto.TimesheetByStatusSearchDto;
import com.altomni.apn.report.service.TimeSheetByStatusWithinAPeriodService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class TimeSheetStatsResource {

    @Resource
    private TimeSheetByStatusWithinAPeriodService timeSheetByStatusWithinAPeriodService;

    @PostMapping("/report/timesheet-within-a-period")
    public ResponseEntity<List<TimeSheetReportBaseVo>> getTimesheetWithinAPeriod(@RequestBody TimesheetByStatusSearchDto searchDto, Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[APN: ReportJobV2 @{}] REST request to get timesheet within a period by searchDto:[{}]", JSONUtil.toJsonStr(searchDto), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("timesheet-report");
        stopWatch.start();
        Page<TimeSheetReportBaseVo>  page = timeSheetByStatusWithinAPeriodService.searchTimeSheetRecordByStatus(searchDto, pageable);
        stopWatch.stop();
        log.info(" getTimesheetWithinAPeriod time = {}ms= \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/report");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/report/timesheet-within-a-period-excel")
    public void exportContractorHires(@RequestBody TimesheetByStatusSearchDto searchDto, HttpServletResponse response) {
        log.info("[APN: ReportJobV2 @{}] REST request to get contractor hires excel by searchDto:[{}]", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        timeSheetByStatusWithinAPeriodService.exportTimeSheetRecordSearchByStatus(searchDto, response);
    }


}
