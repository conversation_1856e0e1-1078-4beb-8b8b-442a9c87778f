package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.LinkedinStats;
import com.altomni.apn.report.service.LinkedinStatsService;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing LinkedinStats.
 */
@RestController
@RequestMapping("/api/v3")
public class LinkedinStatsResource {

    private final Logger log = LoggerFactory.getLogger(LinkedinStatsResource.class);

    private static final String ENTITY_NAME = "linkedinStats";

    private final LinkedinStatsService linkedinStatsService;

    public LinkedinStatsResource(LinkedinStatsService linkedinStatsService) {
        this.linkedinStatsService = linkedinStatsService;
    }

    /**
     * POST  /linkedin-stats : Create a new linkedinStats.
     *
     * @param linkedinStats the linkedinStats to create
     * @return the ResponseEntity with status 201 (Created) and with body the new linkedinStats, or with status 400 (Bad Request) if the linkedinStats has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/linkedin-stats")
    @Timed
    public ResponseEntity<LinkedinStats> createLinkedinStats(@RequestBody LinkedinStats linkedinStats) throws URISyntaxException {
        log.info("[APN: LinkedinStats @{}] REST request to save LinkedinStats : {}", SecurityUtils.getUserId(), linkedinStats);
        if (linkedinStats.getId() != null) {
            return ResponseEntity.badRequest().headers(HeaderUtil.createFailureAlert(ENTITY_NAME, "idexists", "A new linkedinStats cannot already have an ID")).body(null);
        }
        if (linkedinStats.getUserId() == null) {
            linkedinStats.setUserId(SecurityUtils.getUserId());
        }
        if (linkedinStats.getTenantId() == null) {
            linkedinStats.setTenantId(SecurityUtils.getTenantId());
        }
        if (linkedinStats.getType() == null) {
            linkedinStats.setType(ContactType.LINKEDIN);
        }
        linkedinStats.setCount(1);
        LinkedinStats result = linkedinStatsService.save(linkedinStats);
        return ResponseEntity.created(new URI("/api/linkedin-stats/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * PUT  /linkedin-stats : Updates an existing linkedinStats.
     *
     * @param linkedinStats the linkedinStats to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated linkedinStats,
     * or with status 400 (Bad Request) if the linkedinStats is not valid,
     * or with status 500 (Internal Server Error) if the linkedinStats couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/linkedin-stats/{id}")
    @Timed
    public ResponseEntity<LinkedinStats> updateLinkedinStats(@PathVariable Long id, @RequestBody LinkedinStats linkedinStats) throws URISyntaxException {
        log.info("[APN: LinkedinStats @{}] REST request to update LinkedinStats : {}", SecurityUtils.getUserId(), linkedinStats);
        if (linkedinStats.getId() == null) {
            linkedinStats.setId(id);
        }
        LinkedinStats result = linkedinStatsService.save(linkedinStats);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, linkedinStats.getId().toString()))
            .body(result);
    }

}
