package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.time.LocalDate;

@Data
@Entity
public class TimeSheetReportBaseVo {

    @Id
    @ExcelIgnore
    private String id;

    private String fullName;

    private Float regularHours;

    private Float overTime;

    private Float doubleTime;

    private Float totalHours;

    private LocalDate workDate;

    private LocalDate weekEndingDate;

    private LocalDate weekEnd;

    private Double holidayHours;

}
