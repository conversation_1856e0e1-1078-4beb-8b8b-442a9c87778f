package com.altomni.apn.report.util;

import java.util.Collections;
import java.util.List;

import static java.util.Arrays.asList;

public class ReportUtil {

    public static final List<String> REPORT_QUARTERLY_ONBOARDING_DOCUMENTS_HEADERS =
            Collections.unmodifiableList(asList("Employee Type", "No.",
                    "Recruiter",
                    "Candidate Name",
                    "Client Account",
                    "Start Day",
                    "Week Day",
                    "Hourly Gp",
                    "Total Gp",
                    "Total Revenue",
                    "Bill Rate",
                    "",
                    "Pay Rate",
                    "",
                    "Bill Rate",
                    "",
                    "Pay Rate",
                    "",
                    "notes"
            ));

    public static final List<String> REPORT_QUARTERLY_OFFBOARDING_DOCUMENTS_HEADERS =
            Collections.unmodifiableList(asList("Employee Type", "No.",
                    "Recruiter",
                    "Candidate Name",
                    "Client Account",
                    "End Day",
                    "Week Day",
                    "Hourly Gp",
                    "Total Gp",
                    "Total Revenue"
            ));

    public static final List<String> REPORT_QUARTERLY_ONBOARDING_DOCUMENTS_FIELDS =
            Collections.unmodifiableList(asList("employeeType", "number",
                    "recruiter",
                    "clientAccount",
                    "candidateName",
                    "startDay",
                    "weekDay",
                    "hourlyGp",
                    "totalGp",
                    "totalRevenue",
                    "originalBillRate",
                    "originalBillRateCurrency",
                    "originalPayRate",
                    "originalPayRateCurrency",
                    "billRate",
                    "billRateCurrency",
                    "payRate",
                    "payRateCurrency",
                    "notes"
            ));

    public static final List<String> REPORT_QUARTERLY_OFFBOARDING_DOCUMENTS_FIELDS =
            Collections.unmodifiableList(asList("employeeType", "number",
                    "recruiter",
                    "candidateName",
                    "clientAccount",
                    "endDay",
                    "weekDay",
                    "hourlyGp",
                    "totalGp",
                    "totalRevenue"
            ));
}
