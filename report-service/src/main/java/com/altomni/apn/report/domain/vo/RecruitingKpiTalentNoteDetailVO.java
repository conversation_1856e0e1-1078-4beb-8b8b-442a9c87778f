package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.NotePriority;
import com.altomni.apn.common.domain.enumeration.NotePriorityConverter;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteTypeConverter;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;
import java.util.Collections;
import java.util.List;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiTalentNoteDetailVO implements Serializable, AttachConfidentialTalent {

    @Id
    private String id;

    private String fullName;

    private Long talentId;

    private String additionalInfo;

    private String title;

    @Convert(converter = NotePriorityConverter.class)
    private NotePriority priority;

    @Convert(converter = TalentNoteTypeConverter.class)
    private TalentNoteType noteType;

    private String note;

    private Integer jobSearchStatus;

    private Long createdBy;

    private Long lastModifiedBy;

    private Instant createdDate;

    private Instant lastModifiedDate;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    public String getAdditionalInfo() {
        if (StrUtil.isBlank(additionalInfo)) {
            return "";
        }
        try {
            JSONObject jsonObject = JSONUtil.parseObj(additionalInfo);
            return jsonObject.getStr("otherContactInfo");
        } catch (Exception e) {
            //非标准格式忽略
        }
        return "";
    }

    public String getOriginalAdditionalInfoString() {
        return this.additionalInfo;
    }

    public List<Long> getAttendeeIds() {
        if (StrUtil.isBlank(additionalInfo)) {
            return Collections.emptyList();
        }
        try {
            JSONObject jsonObject = JSONUtil.parseObj(additionalInfo);
            JSONArray attendees = jsonObject.getJSONArray("attendees");
            if (attendees == null) {
                return Collections.emptyList();
            }
            return attendees.toList(Long.class);
        } catch (Exception e) {
            //ignore
        }
        return Collections.emptyList();
    }

    @Override
    public void encrypt() {
        this.fullName = null;
        this.additionalInfo = null;
        this.title = null;
        this.priority = null;
        this.noteType = null;
        this.note = null;
        this.jobSearchStatus = null;
        this.createdBy = null;
        this.lastModifiedBy = null;
        this.createdDate = null;
        this.lastModifiedDate = null;
    }
}
