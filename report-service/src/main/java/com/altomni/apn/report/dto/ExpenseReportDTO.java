package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

@Data
public class ExpenseReportDTO {

    private LocalDate startDate;

    private LocalDate endDate;

    private Instant approvedStartDate;

    private Instant approvedEndDate;

    private List<AssignmentDivision> assignmentDivisionList;

    private Long companyId;

    private SearchSortDTO sort;

}
