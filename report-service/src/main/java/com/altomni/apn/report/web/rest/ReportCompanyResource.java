package com.altomni.apn.report.web.rest;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.FteBdReportJobDetailVO;
import com.altomni.apn.report.domain.vo.FteBdReportOnboardDetailVO;
import com.altomni.apn.report.domain.vo.FteBdReportVO;
import com.altomni.apn.report.dto.FteBdReportDTO;
import com.altomni.apn.report.dto.FteBdReportDetailDTO;
import com.altomni.apn.report.service.ReportCompanyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3/fte-bd")
public class ReportCompanyResource {

    @Resource
    ReportCompanyService reportCompanyService;

    @PostMapping("/list")
    public ResponseEntity<List<FteBdReportVO>> queryFteBdReportList(@RequestBody FteBdReportDTO dto, @PageableDefault Pageable pageable){
        log.info("[APN @{}] search fte bd report, param = {}", SecurityUtils.getUserId(), dto);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<FteBdReportVO> result = reportCompanyService.queryFteBdReportList(dto,pageable,headers);
        return ResponseEntity.ok().headers(headers).body(result);
    }

    @PostMapping("/job-detail")
    public ResponseEntity<List<FteBdReportJobDetailVO>> queryFteBdReportJobDetail(@RequestBody FteBdReportDetailDTO dto, @PageableDefault Pageable pageable){
        log.info("[APN @{}] search fte bd report job detail, param = {}", SecurityUtils.getUserId(), dto);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<FteBdReportJobDetailVO> result = reportCompanyService.queryFteBdReportJobDetail(dto,pageable,headers);
        return ResponseEntity.ok().headers(headers).body(result);
    }

    @ProcessConfidentialTalent
    @PostMapping("/onboard-detail")
    public ResponseEntity<List<FteBdReportOnboardDetailVO>> queryFteBdReportOnboardDetail(@RequestBody FteBdReportDetailDTO dto, @PageableDefault Pageable pageable){
        log.info("[APN @{}] search fte bd report onboard detail, param = {}", SecurityUtils.getUserId(), dto);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<FteBdReportOnboardDetailVO> result = reportCompanyService.queryFteBdReportOnboardDetail(dto,pageable,headers);
        return ResponseEntity.ok().headers(headers).body(result);
    }

    @ProcessConfidentialTalent
    @PostMapping("/invoice-detail")
    public ResponseEntity<List<FteBdReportOnboardDetailVO>> queryFteBdReportInvoiceDetail(@RequestBody FteBdReportDetailDTO dto, @PageableDefault Pageable pageable){
        log.info("[APN @{}] search fte bd report invoice detail, param = {}", SecurityUtils.getUserId(), dto);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<FteBdReportOnboardDetailVO> result = reportCompanyService.queryFteBdReportInvoiceDetail(dto,pageable,headers);
        return ResponseEntity.ok().headers(headers).body(result);
    }

    @ProcessConfidentialTalent
    @PostMapping("/payment-detail")
    public ResponseEntity<List<FteBdReportOnboardDetailVO>> queryFteBdReportPaymentDetail(@RequestBody FteBdReportDetailDTO dto, @PageableDefault Pageable pageable){
        log.info("[APN @{}] search fte bd report payment detail, param = {}", SecurityUtils.getUserId(), dto);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<FteBdReportOnboardDetailVO> result = reportCompanyService.queryFteBdReportPaymentDetail(dto,pageable,headers);
        return ResponseEntity.ok().headers(headers).body(result);
    }
}
