package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.ReportJobPerformanceDetailVo;
import com.altomni.apn.report.domain.vo.ReportJobPerformanceRatioVo;
import com.altomni.apn.report.domain.vo.ReportJobPerformanceVo;
import com.altomni.apn.report.dto.ReportJobPerformanceDto;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ReportJobPerformanceService {
    List<ReportJobPerformanceRatioVo> getJobPerformanceSourceRatioData(ReportJobPerformanceDto dto);

    List<ReportJobPerformanceDetailVo> getJobPerformanceSourceDetailData(ReportJobPerformanceDto dto, Pageable pageable, HttpHeaders headers);

    void exportJobPerformanceDetail(ReportJobPerformanceDto dto, Pageable pageable, HttpServletResponse response);
}
