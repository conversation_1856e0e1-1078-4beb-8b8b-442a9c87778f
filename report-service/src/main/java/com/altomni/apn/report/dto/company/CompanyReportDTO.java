package com.altomni.apn.report.dto.company;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@AllArgsConstructor
@ApiModel(description = "Dto for company")
@NoArgsConstructor
@Data
public class CompanyReportDTO implements Serializable {


    @ApiModelProperty(value = "The id for company.")
    public Long id;

    @ApiModelProperty(value = "The name for company.")
    public String name;

    @ApiModelProperty(value = "The active for company.")
    public Boolean active;

    @ApiModelProperty(value = "The id for tenant.")
    public Long tenantId;

}
