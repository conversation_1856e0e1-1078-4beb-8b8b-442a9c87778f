package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import com.altomni.apn.common.domain.enumeration.Currency;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.report.config.excel.CustomJobStatusConverter;
import com.altomni.apn.report.domain.enumeration.LocalDateDataConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.time.LocalDate;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Entity
@ContentRowHeight(15)
@HeadRowHeight(20)
@ColumnWidth(25)
public class ReportJobPerformanceDetailVo implements Serializable {

    @Id
    @ExcelIgnore
    private Long id;

    @ExcelIgnore
    private Long companyId;

    @ExcelProperty(value = "Company Name", index = 0)
    private String companyName;

    @ExcelProperty(value = "Title", index = 1)
    private String title;

    @ExcelIgnore
    private Long currency;

    @ExcelIgnore
    private String billRate;

    @ExcelIgnore
    private String payType;

    @ExcelIgnore
    private Instant createdDate;

    @Transient
    @ExcelProperty(value = "Job Created Date", index = 3)
    private String createdDateFormat;

    @ExcelProperty(value = "No. of Submittals", index = 4)
    private Long submitJobCount;

    @ExcelProperty(value = "Job Status", index = 5, converter = CustomJobStatusConverter.class)
    @Convert(converter = JobStatusConverter.class)
    private JobStatus status;

    @ExcelIgnore
    private Instant lastNonOpenTime;

    @Transient
    @ExcelProperty(value = "Job Close Date", index = 6)
    private String lastNonOpenTimeFormat;

    @Transient
    @ExcelProperty(value = "Bill Rate", index = 2)
    private String billRateDisplay;

    @ExcelIgnore
    @Transient
    private Boolean isPrivateJob;

}
