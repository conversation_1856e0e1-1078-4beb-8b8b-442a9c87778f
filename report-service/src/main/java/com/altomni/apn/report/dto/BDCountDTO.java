package com.altomni.apn.report.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BDCountDTO implements Serializable {

    private Integer followUpContactTypeId;

    private Long noteCounts;

    private Long companyCounts;

    public BDCountDTO(Integer followUpContactTypeId, Long noteCounts, Long companyCounts) {
        this.followUpContactTypeId = followUpContactTypeId;
        this.noteCounts = noteCounts;
        this.companyCounts = companyCounts;
    }
}
