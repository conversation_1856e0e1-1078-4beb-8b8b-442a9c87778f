package com.altomni.apn.report.web.rest;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ContractorHireVo;
import com.altomni.apn.report.domain.vo.ContractorTerminationVo;
import com.altomni.apn.report.dto.ContractorSearchDto;
import com.altomni.apn.report.service.ContractorReportHiresService;
import com.altomni.apn.report.service.ContractorReportTerminationsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ContractorReportResource {

    @Resource
    private ContractorReportHiresService contractorReportHiresService;

    @Resource
    private ContractorReportTerminationsService contractorReportTerminationsService;


    @PostMapping("/report/contractor-hires")
    public ResponseEntity<List<ContractorHireVo>> getContractorHires(@RequestBody ContractorSearchDto searchDto, Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[APN: ReportJobV2 @{}] REST request to get contractor hires by searchDto:[{}]", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("getContractorHires");
        stopWatch.start();
        Page<ContractorHireVo> page = contractorReportHiresService.getContractorHires(searchDto, pageable);
        stopWatch.stop();
        log.info(" getContractorHires time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/report");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/report/contractor-hires-excel")
    public void exportContractorHires(@RequestBody ContractorSearchDto searchDto, Pageable pageable, HttpServletResponse response) {
        log.info("[APN: ReportJobV2 @{}] REST request to get contractor hires excel by searchDto:[{}]", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        contractorReportHiresService.exportContractorHires(searchDto, null, response);
    }


    @PostMapping("/report/contractor-terminations")
    public ResponseEntity<List<ContractorTerminationVo>> getContractorTerminations(@RequestBody ContractorSearchDto searchDto, Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[APN: ReportJobV2 @{}] REST request to get contractor terminations by searchDto:[{}]", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        StopWatch stopWatch = new StopWatch("getContractorTerminations");
        stopWatch.start();
        Page<ContractorTerminationVo> page = contractorReportTerminationsService.getContractorTerminations(searchDto, pageable);
        stopWatch.stop();
        log.info(" getContractorTerminations time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/report");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/report/contractor-terminations-excel")
    public void exportContractorTerminations(@RequestBody ContractorSearchDto searchDto, Pageable pageable, HttpServletResponse response) {
        log.info("[APN: ReportJobV2 @{}] REST request to get contractor terminations excel by searchDto:[{}]", SecurityUtils.getUserId(), JSONUtil.toJsonStr(searchDto));
        contractorReportTerminationsService.exportContractorTerminations(searchDto, null, response);
    }


}
