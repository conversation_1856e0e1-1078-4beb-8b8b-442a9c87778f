package com.altomni.apn.report.web.rest;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.web.rest.vm.company.CompanyProspectVM;
import com.altomni.apn.report.domain.vo.BDActivityReportDetailVO;
import com.altomni.apn.report.domain.vo.BDActivityReportVO;
import com.altomni.apn.report.domain.vo.ReportBdTrackingDetailVO;
import com.altomni.apn.report.domain.vo.ReportBdTrackingVO;
import com.altomni.apn.report.dto.*;
import com.altomni.apn.report.service.BdActivityReportService;
import com.altomni.apn.report.service.CompanyReportService;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * REST controller for managing Report.
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3")
public class BDReportResource {

    private final Logger log = LoggerFactory.getLogger(BDReportResource.class);

    @Resource
    private CompanyReportService companyReportService;

    @Resource
    private BdActivityReportService bdActivityReportService;

    /**
     * GET  /companies/db : get company bd report.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of companies in body
     */
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "teamId", value = "teamId, the team id of the owner.", required = true, paramType = "query")})
//    @GetMapping("/companies/bd")
//    @Timed
//    public ResponseEntity<List<CompanyProspectVM>> getBDReport(@RequestParam(required = false) Long teamId, Pageable pageable){
//        log.info("[APN: Company @{}] REST request to get all Company detail with # open job & # contacts", SecurityUtils.getUserId());
//        ResponseEntity<List<CompanyProspectVM>> response = companyReportService.getBDReport(teamId, pageable);
//        return new ResponseEntity<>(response.getBody(), response.getHeaders(), response.getStatusCode());
//    }

    
    @ApiImplicitParams({
            @ApiImplicitParam(name = "teamId", value = "teamId, the team id of the owner.", required = true, paramType = "query")})
    @PostMapping("/companies/bd")
    @Timed
    public ResponseEntity<List<CompanyProspectVM>> getBDReport(@RequestBody CompanyBDReportSearchVM companyBDReportSearchVM, Pageable pageable){
        log.info("[APN: Company @{}] REST request to get all Company detail with # open job & # contacts.", SecurityUtils.getUserId());
        ResponseEntity<List<CompanyProspectVM>> response = companyReportService.getBDReport(companyBDReportSearchVM, pageable);
        return new ResponseEntity<>(response.getBody(), response.getHeaders(), response.getStatusCode());
    }

    @PostMapping("/company/bd/tracking")
    public ResponseEntity<List<ReportBdTrackingVO>> getBdTrackingReport(@RequestBody BdTrackingSearchDTO bdTrackingSearchDTO) {
        log.info("[apn: @{}] search bd tracking report, param = {} ", SecurityUtils.getUserId(), bdTrackingSearchDTO);
        List<ReportBdTrackingVO> reportBdTrackingVOList = companyReportService.getBdTrackingReport(bdTrackingSearchDTO);
        return ResponseEntity.ok(reportBdTrackingVOList);
    }

    @PostMapping("/company/bd/tracking/detail-list")
    public ResponseEntity<List<ReportBdTrackingDetailVO>> getBdTrackingDetailList(@RequestBody BdTrackingDetailDTO bdTrackingSearchDTO) {
        log.info("[apn: @{}] search bd tracking detail list report, param = {} ", SecurityUtils.getUserId(), bdTrackingSearchDTO);
        List<ReportBdTrackingDetailVO> reportBdTrackingVOList = companyReportService.getBdTrackingDetailList(bdTrackingSearchDTO);
        return ResponseEntity.ok(reportBdTrackingVOList);
    }

    @PostMapping("/company/bd/tracking/list")
    @NoRepeatSubmit
    public ResponseEntity<List<BDActivityReportVO>> bdActivityList(@RequestBody BDActivitySearchDTO searchDTO, @PageableDefault Pageable pageable) throws IOException {
        log.info("[CRM: BdActivityReportResource @{}] REST request to count bd-activity by userIds: {}", SecurityUtils.getUserId(), searchDTO);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<BDActivityReportVO> result = bdActivityReportService.countBdActivityContactType(searchDTO, pageable,headers);
        return new ResponseEntity<>(result, headers, HttpStatus.OK);
    }


    @PostMapping("/company/bd/tracking/detail")
    @NoRepeatSubmit
    public ResponseEntity<List<BDActivityReportDetailVO>> bdActivityDetail(@RequestBody BDActivitySearchDeatilDTO searchDTO, @PageableDefault Pageable pageable) throws IOException, ExecutionException, InterruptedException {
        log.info("[apn: @{}] search bd tracking detail list report, param = {} ", SecurityUtils.getUserId(), searchDTO);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<BDActivityReportDetailVO> result = bdActivityReportService.getBdActivityDetail(searchDTO, pageable, headers);
        return new ResponseEntity<>(result, headers, HttpStatus.OK);

    }

}
