package com.altomni.apn.report.config;

import com.altomni.apn.report.domain.enumeration.DataSourceType;

public class DataSourceHolder {

    private DataSourceHolder() {}

    private static final ThreadLocal<DataSourceType> dbContextHolder = new ThreadLocal<>();
    public static void setCurrentDb(DataSourceType dbType) {
        dbContextHolder.set(dbType);
    }
    public static DataSourceType getCurrentDb() {
        return dbContextHolder.get();
    }
    public static void clear() {
        dbContextHolder.remove();
    }
}
