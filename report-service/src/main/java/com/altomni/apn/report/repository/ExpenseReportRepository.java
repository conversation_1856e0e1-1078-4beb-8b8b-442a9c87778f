package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ExpenseReportVO;
import com.altomni.apn.report.dto.ExpenseReportDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

@Repository
public class ExpenseReportRepository extends BaseCustomRepository {

    /**
     * 统计 expense report 报表sql
     * @param reportDTO
     * @param pageable
     * @return
     */
    public List<ExpenseReportVO> getExpenseReportDate(ExpenseReportDTO reportDTO, Pageable pageable) {
        String dataSql = """
                SELECT
                    ter.id,
                	t.first_name,
                	t.last_name,
                	ter.week_ending_date week_end,
                	ter.work_date,
                	ter.expense_type,
                	ter.cost,
                	if(tsewer.am_approver is null, tsewer.manager, tsewer.am_approver) approver,
                	tsewer.approved_date approved_on,
                	tc.comments,
                	c.full_business_name company_name,
                	j.CODE job_code,
                	ec.symbol,
                	u.custom_timezone timezone
                FROM
                	timesheet_expense_record ter
                	inner join time_sheet_expense_week_ending_record tsewer on tsewer.assignment_id = ter.assignment_id and tsewer.week_end = ter.week_end and tsewer.expense_index = ter.expense_index
                	INNER JOIN talent t ON t.id = ter.talent_id
                	INNER JOIN timesheet_talent_assignment tta ON tta.id = ter.assignment_id
                	INNER JOIN assignment_bill_info abi on abi.assignment_id = tta.id
                	INNER JOIN job j ON j.id = tta.job_id
                	INNER JOIN company c ON c.id = j.company_id
                	INNER JOIN assignment_pay_rate apr ON apr.assignment_id = ter.assignment_id AND apr.content_type = 1 AND apr.pay_type = 3
                	INNER JOIN enum_currency ec on ec.id = apr.currency
                	INNER JOIN user u on u.id = :userId
                	LEFT JOIN timesheet_comments tc on tc.assignment_id = ter.assignment_id and tc.work_date = ter.week_end and tc.comments_type = 1 and tc.record_index = tsewer.expense_index
                WHERE
                	ter.status = 4 and tta.status = 1 and ter.cost IS NOT NULL and ter.cost > 0
                """ + getWhereSql(reportDTO) + getOrderSql(reportDTO);
        Query dataQuery = entityManager.createNativeQuery(dataSql, ExpenseReportVO.class);
        //设置参数
        setParameter(dataQuery, reportDTO);
        //设置分页参数
        if (pageable != null) {
            dataQuery.setFirstResult((pageable.getPageNumber() <= 0? 0: pageable.getPageNumber() - 1) * pageable.getPageSize());
            dataQuery.setMaxResults(pageable.getPageSize());
        }
        return dataQuery.getResultList();
    }

    private String getOrderSql(ExpenseReportDTO reportDTO) {
        StringBuilder sb = new StringBuilder();
        SearchSortDTO sort = reportDTO.getSort();
        if (sort != null) {
            //中文需要特殊处理
            List<String> chineseList = CollUtil.newArrayList("first_name", "last_name", "company_name", "company_name", "comments", "approver", "job_code");
            String column = StrUtil.toUnderlineCase(sort.getProperty());
            if (chineseList.contains(column)) {
                List<String> doubleColumnName = CollUtil.newArrayList("first_name", "last_name");
                if (doubleColumnName.contains(column)) {
                    //表 talent和user 的都存在first_name 和 last_name 字段,会导致sql报错所有需要使用  t.first_name, t.last_name
                    column = "t." + column;
                }
                sb.append(" order by CASE WHEN IFNULL( ").append(column).append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
                sb.append("CONVERT( ").append(column).append(" USING gbk) ");
                sb.append(" ").append(sort.getDirection());
            } else {
                sb.append(" order by ");
                sb.append(column);
                sb.append(" ").append(sort.getDirection());
            }
        } else {
            sb.append(" order by CASE WHEN IFNULL( ").append("t.first_name").append(", '' )= '' THEN 0 ELSE 1 END DESC ,");
            sb.append("CONVERT( ").append("t.first_name").append(" USING gbk) ");
            sb.append(" ASC ");
        }
        return sb.toString();
    }

    public Long getExpenseReportCount(ExpenseReportDTO reportDTO) {
        String countSql = """
                SELECT
                	count(1)
                FROM
                	timesheet_expense_record ter
                	inner join time_sheet_expense_week_ending_record tsewer on tsewer.assignment_id = ter.assignment_id and tsewer.week_end = ter.week_end and tsewer.expense_index = ter.expense_index
                	INNER JOIN timesheet_talent_assignment tta ON tta.id = ter.assignment_id
                	INNER JOIN assignment_bill_info abi on abi.assignment_id = tta.id
                	INNER JOIN job j ON j.id = tta.job_id
                	INNER JOIN company c ON c.id = j.company_id
                	INNER JOIN user u on u.id = :userId
                WHERE
                	ter.status = 4 and tta.status = 1 and ter.cost IS NOT NULL and ter.cost > 0
                """ + getWhereSql(reportDTO);
        Query countQuery = entityManager.createNativeQuery(countSql);
        setParameter(countQuery, reportDTO);
        return Long.parseLong(String.valueOf(countQuery.getSingleResult()));
    }

    private void setParameter(Query query, ExpenseReportDTO reportDTO) {
        query.setParameter("userId", SecurityUtils.getUserId());
        if (ObjectUtil.isNotEmpty(reportDTO.getStartDate()) && ObjectUtil.isNotEmpty(reportDTO.getEndDate())) {
            query.setParameter("startDate", reportDTO.getStartDate())
                    .setParameter("endDate", reportDTO.getEndDate());
        }
        if (ObjectUtil.isNotEmpty(reportDTO.getApprovedStartDate()) && ObjectUtil.isNotEmpty(reportDTO.getApprovedEndDate())) {
            query.setParameter("approvedStartDate", reportDTO.getApprovedStartDate())
                    .setParameter("approvedEndDate", reportDTO.getApprovedEndDate());
        }
        if (CollUtil.isNotEmpty(reportDTO.getAssignmentDivisionList())) {
            query.setParameter("assignmentDivisionList", reportDTO.getAssignmentDivisionList().stream().map(AssignmentDivision::toDbValue).toList());
        }
        if (ObjectUtil.isNotEmpty(reportDTO.getCompanyId())) {
            query.setParameter("companyId", reportDTO.getCompanyId());
        }
    }

    private String getWhereSql(ExpenseReportDTO reportDTO) {
        StringBuilder sb = new StringBuilder();
        if (ObjectUtil.isNotEmpty(reportDTO.getStartDate()) && ObjectUtil.isNotEmpty(reportDTO.getEndDate())) {
            sb.append(" and ter.week_ending_date BETWEEN :startDate AND :endDate ");
        }
        if (ObjectUtil.isNotEmpty(reportDTO.getApprovedStartDate()) && ObjectUtil.isNotEmpty(reportDTO.getApprovedEndDate())) {
            sb.append(" and tsewer.approved_date BETWEEN :approvedStartDate AND :approvedEndDate ");
        }
        if (CollUtil.isNotEmpty(reportDTO.getAssignmentDivisionList())) {
            sb.append(" and abi.assignment_division in :assignmentDivisionList ");
        }
        if (ObjectUtil.isNotEmpty(reportDTO.getCompanyId())) {
            sb.append(" and c.id = :companyId ");
        }
        return sb.toString();
    }
}
