package com.altomni.apn.report.service.e5;

import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.vo.e5.UserActiveDurationDetailReportVO;
import com.altomni.apn.report.dto.e5.UserAdoptionDetailReportSearchDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.Map;

public interface UserActiveDurationService {

    Map<Long, Long> getUsersByOnlineDuration(Long tenantId, String startDate, String endDate, Collection<Long> userIds, Integer durationThresholdInMinute);

    Map<Long, Long> getTopUsersByOnlineDuration(Long tenantId, String startDate, String endDate);

    Page<UserActiveDurationDetailReportVO> getUserOnlineIntervalsPaginated(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, Pageable pageable);

    void downloadUserOnlineIntervalsDetails(UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response);

}
