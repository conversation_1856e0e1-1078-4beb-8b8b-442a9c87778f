package com.altomni.apn.report.web.rest;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.user.UserActiveDurationStatistic;
import com.altomni.apn.report.domain.enumeration.E5ReportViewType;
import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.vo.RecruitingKpiApplicationBaseDetailVO;
import com.altomni.apn.report.domain.vo.e5.*;
import com.altomni.apn.report.domain.vo.voip.VoipDetailReportVO;
import com.altomni.apn.common.domain.user.GetLastWeekActiveDurationUserInfoDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionDetailReportSearchDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionNoteDetailReportSearchDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportDTO;
import com.altomni.apn.report.service.e5.UserActiveDurationService;
import com.altomni.apn.report.service.e5.UserAdoptionReportDetailsService;
import com.altomni.apn.report.service.e5.UserAdoptionReportScheduledService;
import com.altomni.apn.report.service.e5.UserAdoptionReportService;
import io.micrometer.core.annotation.Timed;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.*;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * REST controller for user-adoption Report.
 */
@RestController
@RequestMapping("/api/v3")
public class UserAdoptionReportResource {

    private final Logger log = LoggerFactory.getLogger(UserAdoptionReportResource.class);

    @Resource
    private UserAdoptionReportService userAdoptionReportService;

    @Resource
    private UserActiveDurationService userActiveDurationService;

    @Resource
    private UserAdoptionReportDetailsService userAdoptionReportDetailsService;

    @Resource
    private UserAdoptionReportScheduledService userAdoptionReportScheduledService;

    @PostMapping("/user-adoption-report/dashboard")
    @Timed
    public ResponseEntity<List<UserAdoptionReportVO>> e5UserAdoptionReportDashboard(@RequestParam("view") String view, @RequestBody UserAdoptionReportDTO userAdoptionReportDTO) throws ExecutionException, InterruptedException {
        log.info("[UserAdoptionReport: e5UserAdoptionReportByUsers @{}] REST request to get e5 user adoption report by users, view:{}", SecurityUtils.getUserId(), view);
        UserAdoptionReportWithThresholdVO vo = userAdoptionReportService.getUserAdoptionReportForDashboard(view, userAdoptionReportDTO);

        List<UserAdoptionReportVO> res = vo.getUserAdoptionReport();
        Pageable pageable = PageRequest.of(0, 10, Sort.by("activationDuration").ascending());
        int total = CollectionUtils.size(res);
        Page<UserAdoptionReportVO> page = new PageImpl<>(res.subList(0, Math.min(total, 10)), pageable, total);

        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/user-adoption-report/dashboard");
        HeaderUtil.generateUserActiveAlertHttpHeaders(headers, vo.getUserActiveThreshold(), vo.getPersonalViewCurrentUserActiveDuration());
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/user-adoption-report")
    @Timed
    public ResponseEntity<?> e5UserAdoptionReportByUsers(@RequestBody UserAdoptionReportDTO userAdoptionReportDTO, @PageableDefault Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[UserAdoptionReport: e5UserAdoptionReportByUsers @{}] REST request to get e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        try{
            return userAdoptionReportService.getResponseForE5(userAdoptionReportDTO, pageable);
        }catch (Exception e){
            log.info("UserAdoptionReportResource e5UserAdoptionReportByUsers Exception e:{}",e);
            return new ResponseEntity<>("接口异常", HttpStatus.OK);
        }
    }

    @PostMapping("/user-adoption-report/for-sum")
    @Timed
    public ResponseEntity<?> e5UserAdoptionReportByUsersForSum(@RequestBody UserAdoptionReportDTO userAdoptionReportDTO) throws ExecutionException, InterruptedException {
        try{
            TeamAndUserAdoptionReportVO responseForE5ForSum = userAdoptionReportService.getResponseForE5ForSum(userAdoptionReportDTO);
            return ResponseEntity.ok(responseForE5ForSum);
        }catch (Exception e){
            log.info("UserAdoptionReportResource e5UserAdoptionReportByUsersForSum Exception e:{}",e);
            return new ResponseEntity<>("接口异常", HttpStatus.OK);
        }
    }

    @PostMapping("/user-adoption-report/for-xxl-job")
    @Timed
    public ResponseEntity<?> e5UserAdoptionReportByUsersForXxlJob(@RequestBody UserAdoptionReportDTO userAdoptionReportDTO) throws ExecutionException, InterruptedException {
        try{
            log.info("UserAdoptionReportResource e5UserAdoptionReportByUsersForXxlJob userId:{},userAdoptionReportDTO:{}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(userAdoptionReportDTO));
            LoginUtil.simulateLoginWithUser(userAdoptionReportDTO.getSearchUserId());
            TeamAndUserAdoptionReportVO responseForE5ForSum = userAdoptionReportService.getResponseForE5ForSum(userAdoptionReportDTO);
            return ResponseEntity.ok(responseForE5ForSum);
        }catch (Exception e){
            log.info("UserAdoptionReportResource e5UserAdoptionReportByUsersForXxlJob Exception e:{}",e);
            return new ResponseEntity<>("接口异常", HttpStatus.OK);
        }
    }

    @PostMapping("/last-week-active-duration-user-info")
    public ResponseEntity<UserActiveDurationStatistic> getLastWeekActiveDurationUserInfo(@RequestBody GetLastWeekActiveDurationUserInfoDTO dto) {
        log.info("[UserAdoptionReport: e5UserAdoptionReportByUsers @{}] REST request to get last week active duration user info, dto:{}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(dto));
        return new ResponseEntity<>(userAdoptionReportService.getLastWeekActiveDurationUserInfo(dto), HttpStatus.OK);
    }

//    @PostMapping("/user-adoption-report/download-excel")
//    @Timed
//    public void exportP1PipelineAnalyticsByUsersByExcel(@Valid @RequestBody UserAdoptionReportDTO userAdoptionReportDTO, HttpServletResponse response) throws ExecutionException, InterruptedException {
//        log.info("[UserAdoptionReport: e5UserAdoptionReportByUsers @{}] REST request to download e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
//        userAdoptionReportService.exportE5UserAdoptionReportToExcel(userAdoptionReportDTO, response);
//    }

    @PostMapping("/user-adoption-report/user-active-duration")
    @Timed
    public ResponseEntity<List<UserActiveDurationDetailReportVO>> e5UserActiveDurationReportDetails(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, @PageableDefault Pageable pageable) {
        log.info("[UserAdoptionReport: e5UserActiveDurationReportDetails @{}] REST request to get e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        Page<UserActiveDurationDetailReportVO> page = userActiveDurationService.getUserOnlineIntervalsPaginated(userAdoptionReportDTO, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/user-adoption-report/user-active-duration");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/user-adoption-report/user-active-duration/download-excel")
    @Timed
    public void downloadE5UserActiveDurationReportDetails(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        log.info("[UserAdoptionReport: downloadE5UserActiveDurationReportDetails @{}] REST request to download e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        userActiveDurationService.downloadUserOnlineIntervalsDetails(userAdoptionReportDTO, response);
    }

    @PostMapping("/user-adoption-report/calls")
    @Timed
    public ResponseEntity<?> e5UserCallsReportDetails(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, @PageableDefault Pageable pageable) {
        log.info("[UserAdoptionReport: e5UserActiveDurationReportDetails @{}] REST request to get e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        try {
            return ResponseEntity.ok(userAdoptionReportDetailsService.getCallDetailReport(userAdoptionReportDTO, pageable));
        }catch (Exception e){
            log.info("UserAdoptionReportResource e5UserCallsReportDetails Exception e:{}",e);
            return new ResponseEntity<>("接口异常", HttpStatus.OK);
        }
    }

    @PostMapping("/user-adoption-report/calls/download-excel")
    @Timed
    public void downloadE5UserCallsReportDetails(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        log.info("[UserAdoptionReport: downloadE5UserCallsReportDetails @{}] REST request to download e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        userAdoptionReportDetailsService.downloadCallDetailReport(userAdoptionReportDTO, response);
    }

    @PostMapping("/user-adoption-report/notes")
    @Timed
    public ResponseEntity<List<?>> e5UserNotesReportDetails(@RequestBody UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, @PageableDefault Pageable pageable) {
        log.info("[UserAdoptionReport: e5UserActiveDurationReportDetails @{}] REST request to get e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        Page<?> page = userAdoptionReportDetailsService.getNoteDetailReport(userAdoptionReportDTO, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/recruiting");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/user-adoption-report/notes/download-excel")
    @Timed
    public void downloadE5UserNotesReportDetails(@RequestBody UserAdoptionNoteDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response,@PageableDefault Pageable pageable) {
        userAdoptionReportDetailsService.downloadNoteDetailReportV2(userAdoptionReportDTO, response,pageable);
    }

    @PostMapping("/user-adoption-report/emails")
    @Timed
    public ResponseEntity<List<EmailDetailReportVO>> e5UserEmailsReportDetails(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO,@PageableDefault Pageable pageable) {
        log.info("[UserAdoptionReport: e5UserActiveDurationReportDetails @{}] REST request to get e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        List<EmailDetailReportVO> res = userAdoptionReportDetailsService.getEmailDetailReport(userAdoptionReportDTO,pageable);
        return ResponseEntity.ok(res);
    }

    @PostMapping("/user-adoption-report/emails/download-excel")
    @Timed
    public void downloadE5UserEmailsReportDetails(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        log.info("[UserAdoptionReport: downloadE5UserEmailsReportDetails @{}] REST request to download e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        userAdoptionReportDetailsService.downloadEmailDetailReport(userAdoptionReportDTO, response);
    }

    @PostMapping("/user-adoption-report/submit-to-job")
    @Timed
    public ResponseEntity<List<? extends RecruitingKpiApplicationBaseDetailVO>> e5UserSubmitToJobDetailReport(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, @PageableDefault Pageable pageable) {
        log.info("[UserAdoptionReport: e5UserSubmitToJobDetailReport @{}] REST request to get e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        Page<? extends RecruitingKpiApplicationBaseDetailVO> page = userAdoptionReportDetailsService.getApplicationDetailsReport(userAdoptionReportDTO, ReportApplicationStatus.SUBMIT_TO_JOB, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/user-adoption-report/submit-to-job");

        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/user-adoption-report/submit-to-job/download-excel")
    @Timed
    public void downloadE5UserSubmitToJobDetailReport(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        log.info("[UserAdoptionReport: downloadE5UserSubmitToJobDetailReport @{}] REST request to download e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        userAdoptionReportDetailsService.downloadApplicationDetailsReport(userAdoptionReportDTO, ReportApplicationStatus.SUBMIT_TO_JOB, response);
    }

    @PostMapping("/user-adoption-report/interview")
    @Timed
    public ResponseEntity<List<? extends RecruitingKpiApplicationBaseDetailVO>> e5UserInterviewDetailReport(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, @PageableDefault Pageable pageable) {
        log.info("[UserAdoptionReport: e5UserSubmitToJobDetailReport @{}] REST request to get e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        Page<? extends RecruitingKpiApplicationBaseDetailVO> page = userAdoptionReportDetailsService.getApplicationDetailsReport(userAdoptionReportDTO, ReportApplicationStatus.INTERVIEW, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/user-adoption-report/interview");

        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/user-adoption-report/interview/download-excel")
    @Timed
    public void downloadE5UserInterviewDetailReport(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        log.info("[UserAdoptionReport: downloadE5UserInterviewDetailReport @{}] REST request to download e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        userAdoptionReportDetailsService.downloadApplicationDetailsReport(userAdoptionReportDTO, ReportApplicationStatus.INTERVIEW, response);
    }

    @PostMapping("/user-adoption-report/onboard")
    @Timed
    public ResponseEntity<List<? extends RecruitingKpiApplicationBaseDetailVO>> e5UserOnboardDetailReport(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, @PageableDefault Pageable pageable) {
        log.info("[UserAdoptionReport: e5UserSubmitToJobDetailReport @{}] REST request to get e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        Page<? extends RecruitingKpiApplicationBaseDetailVO> page = userAdoptionReportDetailsService.getApplicationDetailsReport(userAdoptionReportDTO, ReportApplicationStatus.ON_BOARD, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/user-adoption-report/onboard");

        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    @PostMapping("/user-adoption-report/onboard/download-excel")
    @Timed
    public void downloadE5UserOnboardDetailReport(@RequestBody UserAdoptionDetailReportSearchDTO userAdoptionReportDTO, HttpServletResponse response) {
        log.info("[UserAdoptionReport: downloadE5UserOnboardDetailReport @{}] REST request to download e5 user adoption report by users, reportParam:{}", SecurityUtils.getUserId(), userAdoptionReportDTO);
        userAdoptionReportDetailsService.downloadApplicationDetailsReport(userAdoptionReportDTO, ReportApplicationStatus.ON_BOARD, response);
    }

    @PostMapping("/user-adoption-report/send-lark")
    @Timed
    public ResponseEntity<Void> sendUserAdoptionAlertToLark() throws ExecutionException, InterruptedException {
        log.info("[UserAdoptionReport: sendUserAdoptionAlertToLark @{}] REST request to send e5 user adoption report alert to lark", SecurityUtils.getUserId());
        userAdoptionReportScheduledService.sendUserAdoptionReportToTeamLeader();
        return ResponseEntity.ok().build();
    }
}
