package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.altomni.apn.common.dto.activity.ActivityChangeDTO;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.enumeration.ReportUserCandidateSearchType;
import com.altomni.apn.report.domain.vo.s3.CandidateUserCountSearchVo;
import com.altomni.apn.report.domain.vo.s3.CandidateUserDetailSearchVo;
import com.altomni.apn.report.dto.UserTeamPariDTO;
import com.altomni.apn.report.dto.s3.*;
import com.altomni.apn.report.repository.CandidateUserRepository;
import com.altomni.apn.report.repository.ESDataRepository;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.ReportCandidateUserService;
import com.altomni.apn.report.service.ReportService;
import com.altomni.apn.report.service.talent.TalentService;
import com.altomni.apn.report.service.user.UserService;
import com.altomni.apn.report.util.ExcelUtil;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.bucket.filter.ParsedFilter;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedStringTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.Collator;
import java.time.OffsetDateTime;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.altomni.apn.report.domain.constants.ReportSortFieldConstants.*;

/**
 * Candidate Creation and Modification Report Service
 *
 * <AUTHOR>
 * @date 2023/08/11
 */
@Service
public class ReportUserCandidateServiceImpl implements ReportCandidateUserService {

    private final Logger log = LoggerFactory.getLogger(ReportUserCandidateServiceImpl.class);

    @Resource
    private CandidateUserRepository candidateUserRepository;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    @Resource
    private UserService userService;
    @Resource
    private TalentService talentService;
    @Resource
    private ESDataRepository esDataRepository;

    @Resource
    private ReportService reportService;

    @Resource
    private ReportRepository reportRepository;

    @Override
    public ResponseEntity<List<UserCandidateCountDTO>> countCandidateReport(CandidateUserCountSearchVo searchVo) throws IOException {
        checkDateTimeParam(searchVo.getFromDate());
        checkDateTimeParam(searchVo.getToDate());


//        Set<Long> userIds = new HashSet<>();
//        if (CollectionUtils.isEmpty(searchVo.getTargetIds())) {
//            searchVo.setTargetType(ReportUserCandidateSearchType.TEAM);
//            List<Long> nestedTeamIds = reportService.getNestedTeamIds(new ArrayList<>());
//            searchVo.setTargetIds(new HashSet<>(nestedTeamIds));
//        }else if (searchVo.getTargetType() == ReportUserCandidateSearchType.TEAM){
//            List<Long> nestedTeamIds = reportService.getNestedTeamIds(new ArrayList<>(searchVo.getTargetIds()));
//            searchVo.setTargetIds(new HashSet<>(nestedTeamIds));
//        }
//        if (CollectionUtils.isNotEmpty(searchVo.getTargetIds())) {
//            userIds = getUserIdsFromSearchVo(searchVo);
//            if (CollectionUtils.isEmpty(userIds)) {
//                return ResponseEntity.ok().build();
//            }
//        }

//        TeamDataPermissionRespDTO teamDataPermission = cachePermission.getTeamDataPermissionFromCacheOnly(SecurityUtils.getUserId());
//        if (Objects.isNull(teamDataPermission)) {
//            teamDataPermission = initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
//        }
//        Set<Long> uIds = new HashSet<>();
//        if (teamDataPermission.getSelf()) {
//            if (CollectionUtils.isEmpty(userIds) || userIds.contains(SecurityUtils.getUserId())) {
//                uIds.add(SecurityUtils.getUserId());
//            }
//        }else{
//            uIds = userIds;
//        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> teamIds = new HashSet<>();
        if (searchVo.getTargetType() == ReportUserCandidateSearchType.USER) {
            userIds = searchVo.getTargetIds();
        } else if (searchVo.getTargetType() == ReportUserCandidateSearchType.TEAM) {
            teamIds = searchVo.getTargetIds();
        }
        boolean isValid = this.applyDataPermission(userIds, teamIds);
        log.debug("userIds = {}", userIds);
        log.debug("teamIds = {}", teamIds);
        if (!isValid) {
            return ResponseEntity.ok().build();
        }

        Long tenantId = SecurityUtils.getTenantId();
        final Set<Long> finalUserIds = userIds;
        log.info("[APN: UserCandidateSearch ] REST request to count candidates");
        final Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<List<UserCandidateCountDTO>> creationCountsFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            List<UserCandidateCountDTO> creationCounts = candidateUserRepository.countUserCandidates(tenantId, searchVo.getFromDate(), searchVo.getToDate(), finalUserIds);
            log.info("[APN: UserCandidateSearch ] REST request to get es Data");

            creationCounts.forEach(UserCandidateCountDTO::constructFullName);
            log.info("[APN: UserCandidateSearch ] REST request to setFullName");

            return creationCounts;
        });

        // 根据查询条件获取的user所修改candidate的count
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<Map<Long, UserTalentNoteCountDTO>> dbSearchTalentNoteByUserIdAndCreateDateFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            log.info("[APN: UserCandidateSearch ] REST request to dbSearchTalentNoteByUserIdAndCreateDate");
            Map<Long, UserTalentNoteCountDTO> map = dbSearchTalentNoteByUserIdAndCreateDate(searchVo.getFromDate(), searchVo.getToDate(), tenantId, finalUserIds);
            log.info("[APN: UserCandidateSearch ] REST request to get result of dbSearchTalentNoteByUserIdAndCreateDate");
            return map;
        });
        CompletableFuture<SearchResponse> searchResponseCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            try {
                log.info("[APN: UserCandidateSearch ] REST request to countUserCandidate");
                SearchResponse searchResponse = esDataRepository.countUserCandidate(tenantId, searchVo.getFromDate(), searchVo.getToDate(), finalUserIds);
                log.info("[APN: UserCandidateSearch ] REST request to get result of  countUserCandidate");
                return searchResponse;
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        });
        try {
            SearchResponse searchResponse = searchResponseCompletableFuture.get();
            log.info("[APN: UserCandidateSearch ] REST request to transResponseToCountDTO");
            List<UserCandidateCountDTO> modificationCounts = transResponseToCountDTO(searchResponse);
            Map<Long, UserCandidateCountDTO> modificationMap = modificationCounts.stream().collect(Collectors.toMap(UserCandidateCountDTO::getId, t -> t));
            Map<Long, UserTalentNoteCountDTO> userTalentNoteCountMap = dbSearchTalentNoteByUserIdAndCreateDateFuture.get();
            log.info("[APN: UserCandidateSearch ] REST request to setCount");
            if (CollectionUtils.isNotEmpty(creationCountsFuture.get())) {
                creationCountsFuture.get().forEach(u -> {
                    UserCandidateCountDTO modifyCounts = modificationMap.get(u.getId());
                    UserTalentNoteCountDTO userTalentNoteCountDTO = userTalentNoteCountMap.get(u.getId());
                    if (ObjectUtil.isNotNull(modifyCounts)) {
                        u.setModifyCount(modifyCounts.getModifyCount());
                    }
                    if (ObjectUtil.isNotNull(userTalentNoteCountDTO)) {
                        u.setCalledCount(userTalentNoteCountDTO.getCalledCount());
                        u.setEmailedCount(userTalentNoteCountDTO.getEmailedCount());
                        u.setInterviewCount(userTalentNoteCountDTO.getInterviewCount());
                        u.setVideoCount(userTalentNoteCountDTO.getVideoCount());
                        u.setIciCount(userTalentNoteCountDTO.getIciCount());
                    }
                });
            }
            log.info("[APN: UserCandidateSearch ] REST Response Result");
            return ResponseEntity.ok(creationCountsFuture.get());
        } catch (InterruptedException | ExecutionException e) {
            log.error("[CountCandidateReport Error]", e);
            throw new CustomParameterizedException("Internet error, try later please!");
        }
    }

    private Map<Long, UserTalentNoteCountDTO> dbSearchTalentNoteByUserIdAndCreateDate(String fromDate, String toDate, Long tenantId, Set<Long> userIds) {
        List<UserTalentNoteCountDTO> userTalentNoteCountDTOList = candidateUserRepository.searchTalentNoteByUserIdAndCreateDate(fromDate, toDate, tenantId, userIds);
        return userTalentNoteCountDTOList.stream().collect(Collectors.toMap(UserTalentNoteCountDTO::getUserId, t -> t));
    }


    @Override
    public void exportCountCandidateReport(CandidateUserCountSearchVo searchVo, HttpServletResponse response) {
        List<UserCandidateCountDTO> data = new ArrayList<>();
        try {
            final ResponseEntity<List<UserCandidateCountDTO>> listResponseEntity = countCandidateReport(searchVo);
            if (listResponseEntity.hasBody()) {
                data = listResponseEntity.getBody();
            }
        } catch (IOException e) {
            log.error("[ReportCandidateUserService.query] Query Report Error : {} ", ExceptionUtils.getStackTrace(e));
        }
        if (CollUtil.isEmpty(data)) {
            throw new CustomParameterizedException("Cannot Query Any Data.");
        }
        ExcelUtil.downloadExcelCustom(response, UserCandidateCountDTO.class, data, "", "User_Candidate_Report.xlsx", true);
    }

    private boolean applyDataPermission(Set<Long> userIdSet, Set<Long> teamIdSet) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        boolean hasValidUsers = true;

        if (teamDataPermission.getSelf()) {
            Set<Long> userIds = new HashSet<>();
            userIds.add(SecurityUtils.getUserId());
            if (CollectionUtils.isNotEmpty(userIdSet)) {
                userIds = SetUtils.intersection(userIds, userIdSet);
            } else if (CollectionUtils.isNotEmpty(teamIdSet)
                       && CollectionUtils.isEmpty(SetUtils.intersection(teamIdSet, teamDataPermission.getNestedTeamIds()))) {
                // only select teams
                hasValidUsers = false;
            }
            if (CollectionUtils.isEmpty(userIds)) {
                hasValidUsers = false;
            }
            userIdSet.addAll(userIds);
        } else if (teamDataPermission.getAll()) {
            if (CollectionUtils.isEmpty(userIdSet) && CollectionUtils.isNotEmpty(teamIdSet)) {
                // only select teams
                hasValidUsers = this.hasValidUserIds(userIdSet, teamIdSet);
            }
        } else if (CollectionUtils.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            if (CollectionUtils.isNotEmpty(userIdSet)) {
                // select users
                Set<Long> nestedTeamIds = teamDataPermission.getNestedTeamIds();
                Set<Long> validUserIds = reportRepository.getUserTeamPair(userIdSet)
                        .stream().filter(p -> nestedTeamIds.contains(p.getTeamId())).map(UserTeamPariDTO::getUserId).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(validUserIds)) {
                    hasValidUsers = false;
                }
            } else if (CollectionUtils.isNotEmpty(teamIdSet)) {
                // only select teams
                Set<Long> teamIds = SetUtils.intersection(teamIdSet, teamDataPermission.getNestedTeamIds());
                hasValidUsers = this.hasValidUserIds(userIdSet, teamIds);
            } else {
                // no teams, users selected
                hasValidUsers = this.hasValidUserIds(userIdSet, teamDataPermission.getNestedTeamIds());
            }
        } else {
            hasValidUsers = false;
        }
        return hasValidUsers;
    }

    private boolean hasValidUserIds(Set<Long> userIds, Set<Long> teamIds) {
        PermissionTeamMemberSearchVM searchVM = new PermissionTeamMemberSearchVM();
        searchVM.setTeamIds(teamIds);
        Set<Long> userIdsByTeams = Objects.requireNonNull(userService.getPlainTeamMembersByTeamIds(searchVM).getBody())
                .stream().map(PermissionTeamMemberDTO::getId).collect(Collectors.toSet());
        userIds.addAll(userIdsByTeams);
        return CollectionUtils.isNotEmpty(userIdsByTeams);
    }

    private List<UserCandidateCountDTO> transResponseToCountDTO(SearchResponse searchResponse) {
        ParsedStringTerms aggregation = searchResponse.getAggregations().get("createdByAggregation");
        if (aggregation == null) {
            return null;
        }
        return parseSearchResponseToCountDTO(aggregation);
    }


    /**
     * 解析 response 里的 parsedStringTerms 为 UserCandidateCountDTO
     *
     * @param aggregation
     * @return {@link List}<{@link UserCandidateCountDTO}>
     */
    private List<UserCandidateCountDTO> parseSearchResponseToCountDTO(ParsedStringTerms aggregation) {
        List<UserCandidateCountDTO> userCandidateCountDTOList = new ArrayList<>();
        for (Terms.Bucket bucket : aggregation.getBuckets()) {
            UserCandidateCountDTO dto = new UserCandidateCountDTO();

            dto.setId(Long.parseLong(bucket.getKeyAsString()));

            ParsedFilter creationResult = bucket.getAggregations().get(CREATION_AGGREGATION);
            if (creationResult != null) {
                dto.setCreatedCount(creationResult.getDocCount());
            }

            ParsedFilter modifyResult = bucket.getAggregations().get(MODIFY_AGGREGATION);
            if (modifyResult != null) {
                dto.setModifyCount(modifyResult.getDocCount());
            }

            ParsedFilter callResult = bucket.getAggregations().get(CALL_AGGREGATION);
            if (callResult != null) {
                dto.setCalledCount(callResult.getDocCount());
            }

            ParsedFilter emailResult = bucket.getAggregations().get(EMAIL_AGGREGATION);
            if (emailResult != null) {
                dto.setEmailedCount(emailResult.getDocCount());
            }

            ParsedFilter interviewResult = bucket.getAggregations().get(INTERVIEW_AGGREGATION);
            if (interviewResult != null) {
                dto.setInterviewCount(interviewResult.getDocCount());
            }

            ParsedFilter videoResult = bucket.getAggregations().get(VIDEO_AGGREGATION);
            if (interviewResult != null) {
                dto.setVideoCount(videoResult.getDocCount());
            }
            userCandidateCountDTOList.add(dto);
        }
        return userCandidateCountDTOList;
    }

    private Set<Long> getUserIdsFromSearchVo(CandidateUserCountSearchVo searchVo) {
        // 查询条件包含 TeamIds ,根据 TeamIds 获取 userIds
        if (ReportUserCandidateSearchType.TEAM.equals(searchVo.getTargetType())) {
            // 根据 teamIds 查询 userIds
            PermissionTeamMemberSearchVM permissionTeamMemberSearchVM = new PermissionTeamMemberSearchVM();
            permissionTeamMemberSearchVM.setTeamIds(searchVo.getTargetIds());
            // 调用userService 根据TeamIds获取User
            List<PermissionTeamMemberDTO> teamMembers = userService.getTeamMembersByTeamIds(permissionTeamMemberSearchVM).getBody();
            if (CollUtil.isEmpty(teamMembers)) {
                return null;
            }
            List<PermissionTeamMemberDTO> activeUsers = teamMembers.stream().filter(PermissionTeamMemberDTO::getActivated).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(activeUsers)) {
                return null;
            } else {
                // 只取PrimaryTeam的User集合
                List<PermissionTeamMemberDTO> primaryTeamMembers = activeUsers.stream().filter(t -> null != t.getIsPrimaryTeam() && t.getIsPrimaryTeam()).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(primaryTeamMembers)) {
                    return null;
                }
                return primaryTeamMembers.stream().map(PermissionTeamMemberDTO::getId).collect(Collectors.toSet());
            }
        } else if (ReportUserCandidateSearchType.USER.equals(searchVo.getTargetType())) {
            return searchVo.getTargetIds();
        }
        return null;
    }


    /**
     * 根据查询条件，查询某一位用户创建候选人的简单信息
     *
     * @param searchVo
     * @return {@link ResponseEntity}<{@link CandidateUserDetailSearchVo}>
     */
    @Override
    public ResponseEntity<List<UserCandidateDetailDTO>> detailUserCandidatesCreation(CandidateUserDetailSearchVo searchVo, Pageable pageable) {
        checkDetailParam(searchVo);
        Long tenantId = SecurityUtils.getTenantId();
        List<UserCandidateDetailDTO> userCandidateDetails = candidateUserRepository.getUserCandidates(tenantId, searchVo.getFromDate(), searchVo.getToDate(), searchVo.getDetailUserId(), pageable);
        userCandidateDetails.forEach(UserCandidateDetailDTO::constructFullName);
        return ResponseEntity.ok(userCandidateDetails);
    }


    /**
     * 根据查询条件，查询某一位用户修改候选人的简单信息
     *
     * @param searchVo
     * @return {@link ResponseEntity}<{@link CandidateUserDetailSearchVo}>
     */
    @Override
    public ResponseEntity<List<UserCandidateModificationDetailDTO>> detailUserCandidatesModification(CandidateUserDetailSearchVo searchVo, Pageable pageable) {
        checkDetailParam(searchVo);
        Long tenantId = SecurityUtils.getTenantId();
        log.info("[APN: UserCandidateSearch @{}] REST request to detailUserCandidatesModification  findModificationBySearch");
        SearchResponse modificationBySearch = esDataRepository.findModificationBySearch(tenantId, searchVo.getFromDate(), searchVo.getToDate(), searchVo.getDetailUserId(), pageable);
        log.info("[APN: UserCandidateSearch @{}] REST request to detailUserCandidatesModification transResponseToDetailDTO");
        List<UserCandidateModificationDetailDTO> detailDTOList = transResponseToDetailDTO(modificationBySearch);
        log.info("[APN: UserCandidateSearch @{}] REST request to detailUserCandidatesModification getDetailVoWithPageable");
        List<UserCandidateModificationDetailDTO> result = getDetailVoWithPageable(detailDTOList, pageable);
        log.info("[APN: UserCandidateSearch @{}] REST request to detailUserCandidatesModification handleKeyName");
        handleKeyName(result);
        return ResponseEntity.ok(result);
    }

//    /**
//     * 根据查询条件，查询某一位用户修改候选人的Note信息
//     *
//     * @param searchVo
//     * @return {@link ResponseEntity}<{@link CandidateUserDetailSearchVo}>
//     */
//    @Override
//    public ResponseEntity<List<UserCandidateNoteDetailDTO>> detailUserCandidatesNote(CandidateUserDetailSearchVo searchVo, Pageable pageable) {
//        checkNoteDetailParam(searchVo);
//        Long tenantId = SecurityUtils.getTenantId();
//        SearchResponse modificationBySearch = esDataRepository.findChangeRecordByNoteTypeSearch(tenantId, searchVo.getFromDate(), searchVo.getToDate(), searchVo.getDetailUserId(), pageable, searchVo.getNoteType());
//        List<UserCandidateNoteDetailDTO> detailDTOList = transResponseToNoteDetailDTO(modificationBySearch);
//        List<UserCandidateNoteDetailDTO> result = getDetailVoWithPageable(detailDTOList, pageable);
//        return ResponseEntity.ok(result);
//    }

    /**
     * 根据查询条件，查询某一位用户修改候选人的Note信息
     *
     * @param searchVo
     * @return {@link ResponseEntity}<{@link UserTalentNoteDetailDTO}>
     */
    @Override
    public ResponseEntity<List<UserTalentNoteDetailDTO>> detailUserCandidatesNote(CandidateUserDetailSearchVo searchVo, Pageable pageable) {
        checkNoteDetailParam(searchVo);
        return ResponseEntity.ok(candidateUserRepository.searchDetailUserCandidatesNote(searchVo, pageable));
    }

    private void handleKeyName(List<UserCandidateModificationDetailDTO> result) {
        if (CollUtil.isNotEmpty(result)) {
            result.forEach(t ->
                    {
                        for (ActivityChangeDTO d : t.getChangeFields()) {
                            if (StrUtil.isUpperCase(d.getKey())) {
                                d.setKey(StrUtil.toCamelCase(d.getKey().toLowerCase()));
                            }
                            if (StringUtils.isNotBlank(d.getChangedFrom())) {
                                d.setChangedFrom(HtmlUtil.cleanHtmlTag(d.getChangedFrom()));
                            }
                            if (StringUtils.isNotBlank(d.getChangedTo())) {
                                d.setChangedTo(HtmlUtil.cleanHtmlTag(d.getChangedTo()));
                            }
                        }
                    }
            );
        }
    }

    private <T extends UserCandidateBaseDetailDTO> List<T> getDetailVoWithPageable(List<T> detailDTOList, Pageable pageable) {
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        int fromIndex = pageable.getPageNumber() * pageable.getPageSize();
        if (fromIndex >= detailDTOList.size()) {
            return null;
        }
        int toIndex = Math.min(fromIndex + pageable.getPageSize(), detailDTOList.size());
        if (null == order || StringUtils.isEmpty(order.getProperty())) {
            return detailDTOList.subList(fromIndex, toIndex);
        }
        AtomicInteger condition = new AtomicInteger();
        return detailDTOList.stream().sorted((u1, u2) -> {
            switch (order.getProperty()) {
                case SORT_CANDIDATE_NAME:
                    condition.set(sortByMixedString(u1.getName(), u2.getName()));
                    return order.isAscending() ? condition.get() : -condition.get();
                case CREATED_DATE:
                    condition.set(u1.getCreatedDate().compareTo(u2.getCreatedDate()));
                    return order.isAscending() ? condition.get() : -condition.get();
                case CREATED_BY:
                    condition.set(sortByMixedString(u1.getCreatedBy(), u2.getCreatedBy()));
                    return order.isAscending() ? condition.get() : -condition.get();
                default:
                    return 0;
            }
        }).collect(Collectors.toList()).subList(fromIndex, toIndex);
    }


    public Integer sortByMixedString(String name1, String name2) {
        if (StringUtils.isEmpty(name1)) {
            name1 = "";
        }
        if (StringUtils.isEmpty(name2)) {
            name2 = "";
        }
        Collator collator = Collator.getInstance(Locale.CHINA);
        return collator.compare(name1, name2);
    }

    private List<UserCandidateModificationDetailDTO> transResponseToDetailDTO(SearchResponse modificationBySearch) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SearchHits hits = modificationBySearch.getHits();

        List<UserCandidateModificationDetailDTO> detailDTOList = Arrays.stream(hits.getHits()).map(t -> {
            try {
                return objectMapper.readValue(t.getSourceAsString(), UserCandidateModificationDetailDTO.class);
            } catch (JsonProcessingException e) {
                log.error("[TransResponseToCountDTO] ReadValue Exception.");
                return null;
            }
        }).collect(Collectors.toList());

        // 查询Talent名字以及创建日期等信息放入Map里
        Set<Long> talentIds = detailDTOList.stream().map(UserCandidateModificationDetailDTO::getTalentId).collect(Collectors.toSet());
        Map<Long, TalentBriefDTO> talentMap = Objects.requireNonNull(talentService.getTalentsByIdsWithoutEntity(talentIds).getBody()).stream().collect(Collectors.toMap(TalentBriefDTO::getTalentId, t -> t));

        detailDTOList.forEach(t -> {
            TalentBriefDTO talentBriefDTO = talentMap.get(t.getTalentId());
            t.setLastModifiedDate(t.getCreatedDate());
            if (ObjectUtil.isNotNull(talentBriefDTO)) {
                t.setName(talentBriefDTO.getFullName());
                t.setCreatedDate(String.valueOf(talentBriefDTO.getCreatedAt()));
                t.setCreatedBy(talentBriefDTO.getCreateUserName());
            }
        });
        return detailDTOList;
    }

    private List<UserCandidateNoteDetailDTO> transResponseToNoteDetailDTO(SearchResponse modificationBySearch) {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SearchHits hits = modificationBySearch.getHits();

        List<UserCandidateModificationDetailDTO> detailDTOList = Arrays.stream(hits.getHits()).map(t -> {
            try {
                return objectMapper.readValue(t.getSourceAsString(), UserCandidateModificationDetailDTO.class);
            } catch (JsonProcessingException e) {
                log.error("[TransResponseToCountDTO] ReadValue Exception.");
                return null;
            }
        }).collect(Collectors.toList());

        // 查询Talent名字以及创建日期等信息放入Map里
        Set<Long> talentIds = detailDTOList.stream().map(UserCandidateModificationDetailDTO::getTalentId).collect(Collectors.toSet());
        Map<Long, TalentBriefDTO> talentMap = Objects.requireNonNull(talentService.getTalentsByIdsWithoutEntity(talentIds).getBody()).stream().collect(Collectors.toMap(TalentBriefDTO::getTalentId, t -> t));
        List<UserCandidateNoteDetailDTO> result = detailDTOList.stream().map(t -> {
            TalentBriefDTO talentBriefDTO = talentMap.get(t.getTalentId());
            Map<String, String> changeFieldsMap = t.getChangeFields().stream().collect(Collectors.toMap(ActivityChangeDTO::getKey, ActivityChangeDTO::getChangedTo));
            String note = changeFieldsMap.get("note");
            return new UserCandidateNoteDetailDTO(t.getTalentId(),
                    ObjectUtil.isNotNull(talentBriefDTO) ? talentBriefDTO.getFullName() : null,
                    changeFieldsMap.get("title"),
                    null != note ? note : "",
                    t.getCreatedDate());
        }).collect(Collectors.toList());
        return result.stream().filter(t -> StringUtils.isNotEmpty(t.getNote())).collect(Collectors.toList());
    }

    /**
     * 检查日期时间参数
     *
     * @param date 日期
     */
    private void checkDateTimeParam(String date) {
        if (StringUtils.isNotEmpty(date)) {
            try {
                OffsetDateTime.parse(date);
            } catch (DateTimeParseException e) {
                throw new CustomParameterizedException(date + " format wrong.");
            }
        }
    }

    /**
     * 检查查询详情接口vo传参规范
     *
     * @param searchVo
     */
    private void checkDetailParam(CandidateUserDetailSearchVo searchVo) {
        if (searchVo == null) {
            throw new CustomParameterizedException("param is null");
        }
        if (null == searchVo.getDetailUserId()) {
            throw new CustomParameterizedException("detailUserId is null");
        }
        checkDateTimeParam(searchVo.getFromDate());
        checkDateTimeParam(searchVo.getToDate());
    }

    private void checkNoteDetailParam(CandidateUserDetailSearchVo searchVo) {
        checkDetailParam(searchVo);
        if (null == searchVo.getNoteType()) {
            throw new CustomParameterizedException("noteType is null");
        }
    }
}
