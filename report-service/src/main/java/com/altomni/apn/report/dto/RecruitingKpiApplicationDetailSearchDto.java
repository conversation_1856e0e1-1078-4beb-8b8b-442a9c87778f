package com.altomni.apn.report.dto;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiDetailBaseDto;
import com.altomni.apn.common.enumeration.recruiting.RecruitingKpiApplicationStatusType;
import com.altomni.apn.report.domain.enumeration.ReportApplicationStatus;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.altomni.apn.report.domain.vo.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiApplicationDetailSearchDto extends RecruitingKpiDetailBaseDto {

    private ReportApplicationStatus reportApplicationStatus;

    private ApplicationDetailSearchDto detail;

    public Class<? extends RecruitingKpiApplicationBaseDetailVO> getApplicationDetailClass() {
        Class<? extends RecruitingKpiApplicationBaseDetailVO> clazz;
        switch (reportApplicationStatus) {
            case INTERVIEW, INTERVIEW_FIRST, INTERVIEW_SECOND,INTERVIEW_APPOINTMENTS,TWO_OR_MORE_INTERVIEW, INTERVIEW_FINAL -> clazz = RecruitingKpiInterviewDetailVO.class;
            case OFFER -> clazz = RecruitingKpiOfferDetailVO.class;
            case OFFER_ACCEPT -> clazz = RecruitingKpiOfferAcceptDetailVO.class;
            case ON_BOARD -> clazz = RecruitingKpiOnboardDetailVO.class;
            case ELIMINATED -> clazz = RecruitingKpiEliminateDetailVO.class;
            default -> clazz = RecruitingKpiApplicationBaseDetailVO.class;
        }
        return clazz;
    }

    public String getOrderBy() {
        if (ObjectUtil.isEmpty(getSort())) {
            return "";
        }
        return switch (getSort().getProperty()) {
            case "fullName" -> " order by CONVERT( t.full_name USING gbk) " + getSort().getDirection();
            case "submitDate" -> " order by submit_date " + getSort().getDirection();
            case "companyName" -> " order by CONVERT( company_name USING gbk) " + getSort().getDirection();
            case "jobStatus" -> " order by job_status " + getSort().getDirection();
            case "workflowStatus" -> " order by workflow_status " + getSort().getDirection();
            case "jobLocation" -> " order by CONVERT( GROUP_CONCAT( distinct jl.format_location SEPARATOR \";\") USING gbk) " + getSort().getDirection();
            case "lastModifiedDate" -> " order by node.last_modified_date " + getSort().getDirection();
            case "interviewProgress" -> " order by interview_progress " + getSort().getDirection();
            case "interviewType" -> " order by interview_type " + getSort().getDirection();
            case "fromTime" -> " order by node.from_time_utc " + getSort().getDirection();
            case "warrantyEndDate" -> " order by (case when trpod.end_date is null then trpod.warranty_end_date else trpod.end_date end) " + getSort().getDirection();
            case "onboardDate" -> " order by trpod.onboard_date " + getSort().getDirection();
            case "estimateOnboardDate" -> " order by node.estimate_onboard_date " + getSort().getDirection();
            default -> "";
        };
    }

    public String getFromTables(ReportApplicationStatus reportApplicationStatus) {
        String table;
        switch (reportApplicationStatus) {
            case SUBMIT_TO_JOB -> table = ReportTableType.SUBMIT_TO_JOB.getDbTableName();
            case SUBMIT_TO_CLIENT -> table = ReportTableType.SUBMIT_TO_CLIENT.getDbTableName();
            case INTERVIEW,INTERVIEW_FIRST,INTERVIEW_SECOND,INTERVIEW_FINAL,TWO_OR_MORE_INTERVIEW,INTERVIEW_APPOINTMENTS -> table = ReportTableType.INTERVIEW.getDbTableName();
            case OFFER -> table = ReportTableType.OFFER.getDbTableName();
            case OFFER_ACCEPT -> table = ReportTableType.OFFER_ACCEPT.getDbTableName();
            case ON_BOARD -> table = ReportTableType.ON_BOARD.getDbTableName();
            case ELIMINATED -> table = ReportTableType.ELIMINATED.getDbTableName();
            default -> table = " ";
        }
        return table;
    }

    private void addStayedOverFieldsSubmitToClient(StringBuilder sb,RecruitingKpiApplicationDetailSearchDto searchDto,Integer hour){
        sb.append(" node.note pipeline_note, node.submit_time submit_date, 20 node_type,");
        if (searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT) {
            sb.append(" CASE WHEN TIMESTAMPDIFF(HOUR, node.created_date, " +
                    "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                    "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                    "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                    ") > ").append(hour).append(" THEN 1 ELSE 0 END AS stayed_over, ");
        }else{
            sb.append(" CASE WHEN TIMESTAMPDIFF(HOUR, node.created_date, " +
                    " if((trpn.node_status = 1 or trpn.node_status = 4) and trpn.node_type=20," +
                    "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                    "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                    "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                    "," +
                    "(select case when pn.node_type=20 then sc.created_date\n" +
                    "when pn.node_type=30 then vi.created_date \n" +
                    "when pn.node_type=40 then er.created_date\n" +
                    "when pn.node_type=41 then ofa.created_date \n" +
                    "when pn.node_type=60 then bo.created_date else now() end as createTime from talent_recruitment_process_node pn \n" +
                    " left join talent_recruitment_process_submit_to_client sc on sc.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                    " left join talent_recruitment_process_interview vi on vi.talent_recruitment_process_id = pn.talent_recruitment_process_id and vi.progress=1\n" +
                    " left join talent_recruitment_process_offer er on er.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                    " left join talent_recruitment_process_ipg_offer_accept ofa on ofa.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                    " left join talent_recruitment_process_onboard bo on bo.talent_recruitment_process_id = pn.talent_recruitment_process_id\t\t \n" +
                    "where pn.talent_recruitment_process_id=node.talent_recruitment_process_id and node_type > 20  order by node_type asc limit 1))) > ").append(hour).append(" THEN 1 ELSE 0 END AS stayed_over, ");
        }

    }

    private void addStayedOverFields(StringBuilder sb,RecruitingKpiApplicationDetailSearchDto searchDto,Integer hour){
        sb.append(" node.recommend_comments pipeline_note, node.created_date submit_date, 10 node_type, ");
        if (searchDto.getApplicationStatusType() == RecruitingKpiApplicationStatusType.CURRENT) {
            sb.append(" CASE WHEN TIMESTAMPDIFF(HOUR, node.created_date, " +
                    "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                    "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                    "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                    ") > ").append(hour).append(" THEN 1 ELSE 0 END AS stayed_over, ");
        }else{
            sb.append(" CASE WHEN TIMESTAMPDIFF(HOUR, node.created_date, " +
                    " if((trpn.node_status = 1 or trpn.node_status = 4) and trpn.node_type=10," +
                    "(select case when max(pe.created_date) is null then now() else pe.created_date end as x  from talent_recruitment_process_node zz \n" +
                    "\t\t left join talent_recruitment_process_eliminate pe on pe.talent_recruitment_process_id = zz.talent_recruitment_process_id\n" +
                    "\t\t where zz.talent_recruitment_process_id=node.talent_recruitment_process_id group by pe.created_date)" +
                    ",(select case when pn.node_type=20 then sc.created_date\n" +
                    "when pn.node_type=30 then vi.created_date \n" +
                    "when pn.node_type=40 then er.created_date\n" +
                    "when pn.node_type=41 then ofa.created_date \n" +
                    "when pn.node_type=60 then bo.created_date else now() end as createTime from talent_recruitment_process_node pn \n" +
                    " left join talent_recruitment_process_submit_to_client sc on sc.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                    " left join talent_recruitment_process_interview vi on vi.talent_recruitment_process_id = pn.talent_recruitment_process_id and vi.progress=1\n" +
                    " left join talent_recruitment_process_offer er on er.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                    " left join talent_recruitment_process_ipg_offer_accept ofa on ofa.talent_recruitment_process_id = pn.talent_recruitment_process_id\n" +
                    " left join talent_recruitment_process_onboard bo on bo.talent_recruitment_process_id = pn.talent_recruitment_process_id\t\t \n" +
                    "where pn.talent_recruitment_process_id=node.talent_recruitment_process_id and node_type > 10  order by node_type asc limit 1))) > ").append(hour).append(" THEN 1 ELSE 0 END AS stayed_over, ");
        }

    }

    public String getSelectFields(RecruitingKpiApplicationDetailSearchDto searchDto) {
        StringBuilder sb = new StringBuilder();
        switch (searchDto.getReportApplicationStatus()) {
            case SUBMIT_TO_JOB -> addStayedOverFields(sb,searchDto,24);
            case SUBMIT_TO_CLIENT -> addStayedOverFieldsSubmitToClient(sb,searchDto,72);
            case INTERVIEW_FIRST, INTERVIEW_SECOND, INTERVIEW_FINAL,TWO_OR_MORE_INTERVIEW,INTERVIEW_APPOINTMENTS, INTERVIEW -> sb.append("""
                    node.note pipeline_note,
                    node.created_date submit_date,
                    max(trpi.progress) interview_progress,
                    node.interview_type interview_type,
                    node.time_zone time_zone,
                    node.from_time from_time,
                    node.to_time to_time,
                    30 node_type,
                    """);
            case OFFER -> sb.append("""
                    node.note pipeline_note,
                    node.created_date submit_date,
                    node.estimate_onboard_date estimate_onboard_date,
                    40 node_type,
                    """);
            case OFFER_ACCEPT ->sb.append("""
                    node.note pipeline_note,
                    node.created_date submit_date,
                    trpod.onboard_date onboard_date,
                    case when trpod.end_date is null then trpod.warranty_end_date else trpod.end_date end as warranty_end_date,
                    41 node_type,
                    """);
            case ON_BOARD -> sb.append("""
                    node.note pipeline_note,
                    node.created_date submit_date,
                    trpod.onboard_date onboard_date,
                    60 node_type,
                    case when trpod.end_date is null then trpod.warranty_end_date else trpod.end_date end as warranty_end_date,
                    """);
            case ELIMINATED -> sb.append("""
                    node.note pipeline_note,
                    node.created_date submit_date,
                    node.reason reason,
                    -1 node_type,
                    """);
            default -> throw new IllegalArgumentException("Invalid ReportApplicationStatus");
        }
        return sb.toString();
    }

    public void appendDetailCondition(StringBuilder sb, Map<String, Object> whereParamMap) {
        if (detail == null) {
            return;
        }
        if (StrUtil.isNotBlank(detail.getFullName())) {
            sb.append(" and t.full_name like :fullName ");
            whereParamMap.put("fullName", "%" + detail.getFullName() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getJobId())) {
            sb.append(" and j.id = :jobId ");
            whereParamMap.put("jobId", detail.getJobId());
        }
        if (ObjectUtil.isNotEmpty(detail.getJobCode())) {
            sb.append(" and j.code like :jobCode ");
            whereParamMap.put("jobCode", "%" + detail.getJobCode() + "%");
        }
        if (StrUtil.isNotBlank(detail.getJobTitle())) {
            sb.append(" and j.title like :jobTitle ");
            whereParamMap.put("jobTitle", "%" + detail.getJobTitle() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getCompanyId())) {
            sb.append(" and c.id = :companyId ");
            whereParamMap.put("companyId", detail.getCompanyId());
        }
        if (StrUtil.isNotBlank(detail.getCompanyName())) {
            sb.append(" and c.full_business_name like :companyName ");
            whereParamMap.put("companyName", "%" + detail.getCompanyName() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getJobStatus())) {
            sb.append(" and j.status = :jobStatus ");
            whereParamMap.put("jobStatus", detail.getJobStatus().toDbValue());
        }
        appendWorkflowStatus(sb, detail.getWorkflowStatus());
        if (ObjectUtil.isNotEmpty(detail.getFlexibleLocation())) {
            sb.append(" and j.flexible_location = :flexibleLocation ");
            whereParamMap.put("flexibleLocation", detail.getFlexibleLocation());
        }
        if (StrUtil.isNotBlank(detail.getJobLocation())) {
            sb.append(" and jl.format_location like :jobLocation ");
            whereParamMap.put("jobLocation", "%" + detail.getJobLocation() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getAm())) {
            sb.append(" and exists (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = am.talent_recruitment_process_id AND kk.user_role = 0 and kk.user_id = :am ) ");
            whereParamMap.put("am", detail.getAm());
        }
        if (ObjectUtil.isNotEmpty(detail.getCoAmList())) {
            sb.append(" and exists (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = coam.talent_recruitment_process_id AND kk.user_role = 7 and kk.user_id = :coAm ) ");
            whereParamMap.put("coAm", detail.getCoAmList());
        }
        if (ObjectUtil.isNotEmpty(detail.getRecruiter())) {
            sb.append(" and exists (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = re.talent_recruitment_process_id AND kk.user_role = 1 and kk.user_id = :recruiter ) ");
            whereParamMap.put("recruiter", detail.getRecruiter());
        }
        if (ObjectUtil.isNotEmpty(detail.getSourcer())) {
            sb.append(" and exists (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = so.talent_recruitment_process_id AND kk.user_role = 2 and kk.user_id = :sourcer ) ");
            whereParamMap.put("sourcer", detail.getSourcer());
        }
        if (ObjectUtil.isNotEmpty(detail.getAc())) {
            sb.append(" and exists (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = ac.talent_recruitment_process_id AND kk.user_role = 5 and kk.user_id = :ac ) ");
            whereParamMap.put("ac", detail.getAc());
        }
        if (ObjectUtil.isNotEmpty(detail.getDm())) {
            sb.append(" and exists (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = dm.talent_recruitment_process_id AND kk.user_role = 3 and kk.user_id = :dm ) ");
            whereParamMap.put("dm", detail.getDm());
        }
        if (ObjectUtil.isNotEmpty(detail.getOwner())) {
            sb.append(" and exists (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = owner.talent_recruitment_process_id AND kk.user_role = 4 and kk.user_id = :owner ) ");
            whereParamMap.put("owner", detail.getOwner());
        }
        if (ObjectUtil.isNotEmpty(detail.getSalesLeadOwner())) {
            sb.append(" and exists (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = salesleadowner.talent_recruitment_process_id AND kk.user_role = 9 and kk.user_id = :salesLeadOwner ) ");
            whereParamMap.put("salesLeadOwner", detail.getSalesLeadOwner());
        }
        if (ObjectUtil.isNotEmpty(detail.getBdOwner())) {
            sb.append(" and exists (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = bdowner.talent_recruitment_process_id AND kk.user_role = 8 and kk.user_id = :bdOwner ) ");
            whereParamMap.put("bdOwner", detail.getBdOwner());
        }
        if (ObjectUtil.isNotEmpty(detail.getLastModifiedBy())) {
            sb.append(" and node.last_update_user_id = :lastModifiedBy ");
            whereParamMap.put("lastModifiedBy", detail.getLastModifiedBy());
        }
        if (ObjectUtil.isNotEmpty(detail.getInterviewProgress())) {
            sb.append("""
                    and exists(
                    select 1 
                    from talent_recruitment_process_interview ti
                    where ti.talent_recruitment_process_id = trp.id group by ti.talent_recruitment_process_id having max(progress) = :interviewProgress
                    )                                     
                    """);
            whereParamMap.put("interviewProgress", detail.getInterviewProgress());
        }
        if (ObjectUtil.isNotEmpty(detail.getInterviewType())) {
            sb.append(" and node.interview_type = :interviewType ");
            whereParamMap.put("interviewType", detail.getInterviewType().toDbValue());
        }
    }

    private void appendWorkflowStatus(StringBuilder sb, ReportTableType workflowStatus) {
        if (ObjectUtil.isEmpty(workflowStatus)) {
            return;
        }
        switch (workflowStatus) {
            case ELIMINATED -> sb.append(" and trpn.node_status = 4 ");
            case SUBMIT_TO_JOB -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 10 ");
            case SUBMIT_TO_CLIENT -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 20 ");
            case INTERVIEW -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 30 ");
            case OFFER -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 40 ");
            case OFFER_ACCEPT -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 41 ");
            case ON_BOARD -> sb.append(" and trpn.node_status = 1 and trpn.node_type = 60 ");
            case RESIGNED -> sb.append(" and resign.id is not null ");
            default -> sb.append(" ");
        }
    }

}
