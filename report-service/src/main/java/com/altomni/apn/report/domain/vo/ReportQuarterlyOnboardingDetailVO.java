package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Date;
import java.sql.Timestamp;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportQuarterlyOnboardingDetailVO implements Serializable, AttachConfidentialTalent {

    private String id;

    private String recruiter;

    private String clientAccount;

    private BigInteger clientId;

    private BigInteger candidateId;

    private String candidateName;

    private String startDay;

    private String weekDay;

    private String hourlyGp;

    private String totalGp;

    private String totalRevenue;

    private String originalBillRate;

    private String originalPayRate;

    private String originalCurrency;

    private String billRate;

    private String payRate;

    private String notes;

    private String currency;

    private String currencyName;

    private String symbol;

    @JsonIgnore
    private Integer weekDays;

    @JsonIgnore
    private Date startDays;

    private ReportQuarterlyOnboardingTotalVO total;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;


    @Override
    public Long getTalentId() {
        if (candidateId == null) {
            return null;
        }
        return candidateId.longValue();
    }

    @Override
    public void encrypt() {
        this.recruiter = null;
        this.clientAccount = null;
        this.clientId = null;
        this.candidateName = null;
        this.startDay = null;
        this.weekDay = null;
        this.hourlyGp = null;
        this.totalGp = null;
        this.totalRevenue = null;
        this.currency = null;
        this.currencyName = null;
        this.originalBillRate = null;
        this.originalPayRate = null;
        this.originalCurrency = null;
        this.billRate = null;
        this.payRate = null;
        this.notes = null;
        this.symbol = null;
        this.total = null;
    }
}
