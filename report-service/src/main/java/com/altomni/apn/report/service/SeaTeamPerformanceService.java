package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.SeaTeamPerformanceDetailVo;
import com.altomni.apn.report.domain.vo.SeaTeamPerformanceVO;
import com.altomni.apn.report.dto.SeaTeamPerformanceSearchDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface SeaTeamPerformanceService {
    List<SeaTeamPerformanceVO> searchSeaTeamPerformance(SeaTeamPerformanceSearchDto searchDto);

    List<SeaTeamPerformanceDetailVo> searchSeaTeamPerformanceDetail(SeaTeamPerformanceSearchDto searchDto, String type);

    void downloadSeaTeamPerformanceReport(SeaTeamPerformanceSearchDto searchDto, HttpServletResponse response);
}
