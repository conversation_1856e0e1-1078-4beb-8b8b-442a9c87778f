package com.altomni.apn.report.domain.vo.s3;

import com.altomni.apn.report.domain.enumeration.ReportUserCandidateSearchType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.Set;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CandidateUserCountSearchVo implements Serializable {

    private static final long serialVersionUID = 6753277428370187257L;

    private String fromDate;

    private String toDate;

    private Set<Long> targetIds;

    private ReportUserCandidateSearchType targetType;
    }
