package com.altomni.apn.report.domain.olap;

import org.apache.commons.collections4.CollectionUtils;
import org.openapitools.client.model.ChartDataResponseResult;
import org.openapitools.client.model.DatasetRestApiGetTableColumn;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * olap 查询返回的数据进行二次加工
 */
public class TimeProcessor implements QueryDataProcessor {

   private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    public ChartDataResponseResult process(ChartDataResponseResult dataResponse, QueryContext queryContext) {
        if (CollectionUtils.isEmpty(queryContext.getQueryConfig().getColumns())) {
            return dataResponse;
        }

        List<String> dims = queryContext.provideAllDim();
        Map<String, DatasetRestApiGetTableColumn> columnMap = queryContext.provideColumnMap();

        Set<String> timeDim = dims.stream().filter(columnMap::containsKey)
            .filter(dim -> {
                DatasetRestApiGetTableColumn column = columnMap.get(dim);
                return column.getType() != null && column.getType().contains("TIME");
            }).collect(Collectors.toSet());

        List<Object> processedData = dataResponse.getData().stream().filter(data -> data instanceof LinkedHashMap<?, ?>)
            .map(data -> (LinkedHashMap) data)
            .map(data -> {
                data.keySet().stream().filter(timeKey -> data.get(timeKey) != null && timeDim.contains(timeKey)).forEach(timeKey -> {
                    Double timestamp = Double.parseDouble(data.get(timeKey).toString());
                    Instant instant = Instant.ofEpochMilli(timestamp.longValue());
                    ZonedDateTime dateTime = instant.atZone(ZoneId.systemDefault());
                    String formattedDateTime = dateTime.format(formatter);
                    data.put(timeKey, formattedDateTime);
                });
                return (Object) data;
            }).toList();

        dataResponse.setData(processedData);
        return dataResponse;
    }


}
