package com.altomni.apn.report.util;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Entities;
import org.jsoup.safety.Safelist;

public final class HtmlUtils {

    private HtmlUtils() {
        // 工具类不允许实例化
    }

    /**
     * 将 HTML 转成纯文本：
     * 1. 清除所有标签，只保留 <p> 和 <br>；
     * 2. 把 <p> 和 <br> 转成换行符；
     * 3. 还原 HTML 实体（如&nbsp;、&amp;）。
     *
     * @param html 原始 HTML 字符串
     * @return 转换后的纯文本，已替换换行
     */
    public static String htmlToPlainText(String html) {
        if (html == null || html.isEmpty()) {
            return "";
        }

        // 1. 清除所有标签，只保留 <p> 和 <br>
        String cleaned = Jsoup.clean(
                html,
                "",                                    // baseUri，不解析链接
                Safelist.none()
                        .addTags("p", "br"),               // 仅保留 p/br
                new Document.OutputSettings().prettyPrint(false) // 不自动格式化
        );

        // 2. 还原 HTML 实体
        String unescaped = Entities.unescape(cleaned);

        // 3. 把保留的标签换成换行
        //    (?i) 表示不区分大小写
        String withBreaks = unescaped
                .replaceAll("(?i)<br\\s*/?>", "\n")
                .replaceAll("(?i)</p>", "\n")
                .replaceAll("(?i)<p[^>]*>", "");     // 去掉 <p> 开始标签

        // 4. 去除多余首尾空白和重复换行（可选）
        //    下面这行代码会把 2 个及以上的连续换行合并成一个，并去掉首尾空行
        withBreaks = withBreaks
                .replaceAll("\\s*\\n+\\s*", "\n")
                .trim();

        return withBreaks;
    }
}
