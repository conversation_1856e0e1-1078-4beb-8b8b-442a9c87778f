package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.TimeSheetReportBaseVo;
import com.altomni.apn.report.dto.TimesheetByStatusSearchDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.ExecutionException;

public interface TimeSheetByStatusWithinAPeriodService {

    Page<TimeSheetReportBaseVo> searchTimeSheetRecordByStatus(TimesheetByStatusSearchDto searchDto, Pageable pageable) throws ExecutionException, InterruptedException;

    void exportTimeSheetRecordSearchByStatus(TimesheetByStatusSearchDto searchDto, HttpServletResponse response);

}
