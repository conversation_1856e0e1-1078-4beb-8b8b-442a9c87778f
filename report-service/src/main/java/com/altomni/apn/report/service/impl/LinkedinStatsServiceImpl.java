package com.altomni.apn.report.service.impl;

import com.altomni.apn.report.domain.LinkedinStats;
import com.altomni.apn.report.repository.LinkedinStatsRepository;
import com.altomni.apn.report.service.LinkedinStatsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service Implementation for managing LinkednStats.
 */
@Service
@Transactional
public class LinkedinStatsServiceImpl implements LinkedinStatsService {

    private final Logger log = LoggerFactory.getLogger(LinkedinStatsServiceImpl.class);

    private final LinkedinStatsRepository linkedinStatsRepository;


    public LinkedinStatsServiceImpl(LinkedinStatsRepository linkedinStatsRepository) {
        this.linkedinStatsRepository = linkedinStatsRepository;
    }

    /**
     * Save a linkedinStats.
     *
     * @param linkedinStats the entity to save
     * @return the persisted entity
     */
    @Override
    public LinkedinStats save(LinkedinStats linkedinStats) {
        log.debug("Request to save LinkedinStats : {}", linkedinStats);
        synchronized (this) {
            LinkedinStats stats = linkedinStatsRepository.findFirstByUserIdAndLinkedinAndTypeOrderById(linkedinStats.getUserId(), linkedinStats.getLinkedin(), linkedinStats.getType());
            if (stats != null) {
                stats.setCount(stats.getCount() + 1);
                return linkedinStatsRepository.save(stats);
            } else {
                return linkedinStatsRepository.save(linkedinStats);
            }
        }
    }


}
