package com.altomni.apn.report.web.rest;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.dto.user.ReportUserLabelSearchDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportUserLabelVM;
import com.altomni.apn.report.service.ReportUserLabelService;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberMemberDetailDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/v3")
@Slf4j
public class ReportUserLabelResource {

    @Resource
    ReportUserLabelService reportUserLabelService;


    @PostMapping("/label/search")
    public ResponseEntity<List<PermissionTeamMemberMemberDetailDTO>> searchUserLabelReport(@RequestBody ReportUserLabelSearchDTO reportUserLabelSearchDTO){
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("Pagination-Count"));
        List<PermissionTeamMemberMemberDetailDTO> result = reportUserLabelService.searchUserLabelReport(reportUserLabelSearchDTO, headers);
        return new ResponseEntity<>(result, headers, HttpStatus.OK);
    }

    @PostMapping("/label/search/export")
    public void exportCountCandidateReport(@RequestBody ReportUserLabelSearchDTO reportUserLabelSearchDTO, HttpServletResponse response) throws IOException {
        log.info("[APN: ReportUserTalent @{}] REST request to export user label search by dto: {}", SecurityUtils.getUserId(), reportUserLabelSearchDTO);
        reportUserLabelService.exportUserLabelReport(reportUserLabelSearchDTO,response);
    }

}
