package com.altomni.apn.report.domain.vo.e5;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserActiveDurationDetailReportExcelVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @ExcelProperty(value = "Login platform", index = 0)
    private String platform;

    @ExcelProperty(value = "Active time", index = 1)
    private String activeTimeRange;

    @ExcelProperty(value = "Duration", index = 2)
    private Long duration;

    public UserActiveDurationDetailReportExcelVO(UserActiveDurationDetailReportVO vo, ZoneId zoneId) {
        this.platform = vo.getPlatform();
        this.duration = vo.getDuration();
//        this.activeTimeRange = vo.getStartTime().toLocalDateTime().format(formatter) + " —— " + vo.getEndTime().toLocalDateTime().format(formatter);
        String start = vo.getStartTime().atZoneSameInstant(zoneId).toLocalDateTime().format(formatter);
        String end = vo.getEndTime().atZoneSameInstant(zoneId).toLocalDateTime().format(formatter);
        this.activeTimeRange = start + " —— " + end;
    }

}
