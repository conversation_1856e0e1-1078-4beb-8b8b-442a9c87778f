package com.altomni.apn.report.service.job;

import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.voip.PhoneTranscription;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.dto.voip.VoipContactDTO;
import com.altomni.apn.common.dto.voip.VoipReportDTO;
import io.micrometer.core.annotation.Timed;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Set;

/**
 * Service Interface for managing {@link Job}.
 */
@Component
@FeignClient(value = "job-service")
public interface JobService {

    @PostMapping(value = "/job/api/v3/brief-jobs/ids")
    ResponseEntity<List<JobBriefDTO>> getBriefJobListByIds(@RequestBody List<Long> ids);

    @PostMapping("/job/api/v3/get-private-job-ids")
    Set<Long> findPrivateJobIds(List<Long> jobIds);

    @GetMapping("/job/api/v3/dict/currency/all")
    ResponseEntity<List<EnumCurrency>> findAllEnumCurrency();
}
