package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.dto.recruiting.RecruitingKpiReportSearchDto;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiByUserVO;
import com.altomni.apn.common.vo.recruiting.RecruitingKpiCommonVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Supplier;

public interface RecruitingKpiBaseExcelVo {

    void setSubmitToJobNum(String submitToJobNum);

    void setSubmitToClientNum(String submitToClientNum);

    void setInterviewAppointments(String interviewAppointments);

    void setFirstInterviewNum(String firstInterviewNum);

    void setSecondInterviewNum(String secondInterviewNum);

    void setTwoOrMoreInterviews(String twoOrMoreInterviews);

    void setFinalInterviewNum(String finalInterviewNum);

    void setInterviewNum(String interviewNum);

    void setOfferNum(String offerNum);

    void setOfferAcceptNum(String offerAcceptNum);

    void setOnboardNum(String onboardNum);

    void setEliminateNum(String eliminateNum);


    static String toPercentage(String rate) {
        if (rate == null || rate.isBlank()) {
            return "0%";
        }
        BigDecimal rateDecimal = new BigDecimal(rate);
        // 将转换率乘以 100 转换为百分比
        BigDecimal percentage = rateDecimal.multiply(new BigDecimal("100"));
        // 设置小数点后两位并四舍五入
        percentage = percentage.setScale(2, RoundingMode.HALF_UP);
        return percentage + "%";
    }

    static String safeLong(Long num) {
        return Optional.ofNullable(num).orElse(0L).toString();
    }

    /**
     * 设置流程相关指标
     */
    default void setProcessMetrics(RecruitingKpiCommonVO vo, RecruitingKpiReportSearchDto searchDto) {
        // 定义流程指标配置
        List<ProcessMetricConfig> configs = getProcessMetricConfigs(vo);

        // 批量设置所有流程指标
        for (ProcessMetricConfig config : configs) {
            setProcessMetric(config, searchDto);
        }
    }

    List<ProcessMetricConfig> getProcessMetricConfigs(RecruitingKpiCommonVO vo);

    default List<ProcessMetricConfig> getBaseProcessMetricConfigs(RecruitingKpiCommonVO vo) {
        return List.of(
                new ProcessMetricConfig(
                        this::setSubmitToJobNum,
                        vo::getSubmitToJobNum, vo::getSubmitToJobCurrentNum,
                        vo::getSubmitToJobNumAIRecommend, vo::getSubmitToJobNumPrecisionAIRecommend
                ),
                new ProcessMetricConfig(
                        this::setSubmitToClientNum,
                        vo::getSubmitToClientNum, vo::getSubmitToClientCurrentNum,
                        vo::getSubmitToClientNumAIRecommend, vo::getSubmitToClientNumPrecisionAIRecommend
                ),

                new ProcessMetricConfig(
                        this::setFirstInterviewNum,
                        vo::getFirstInterviewNum, vo::getCurrentFirstInterviewNum,
                        vo::getFirstInterviewNumAIRecommend, vo::getFirstInterviewNumPrecisionAIRecommend
                ),
                new ProcessMetricConfig(
                        this::setSecondInterviewNum,
                        vo::getSecondInterviewNum, vo::getCurrentSecondInterviewNum,
                        vo::getSecondInterviewNumAIRecommend, vo::getSecondInterviewNumPrecisionAIRecommend
                ),

                new ProcessMetricConfig(
                        this::setFinalInterviewNum,
                        vo::getFinalInterviewNum, vo::getCurrentFinalInterviewNum,
                        vo::getFinalInterviewNumAIRecommend, vo::getFinalInterviewNumPrecisionAIRecommend
                ),
                new ProcessMetricConfig(
                        this::setInterviewNum,
                        vo::getInterviewNum, vo::getCurrentInterviewNum,
                        vo::getInterviewNumAIRecommend, vo::getInterviewNumPrecisionAIRecommend
                ),
                new ProcessMetricConfig(
                        this::setOfferNum,
                        vo::getOfferNum, vo::getOfferCurrentNum,
                        vo::getOfferNumAIRecommend, vo::getOfferNumPrecisionAIRecommend
                ),
                new ProcessMetricConfig(
                        this::setOfferAcceptNum,
                        vo::getOfferAcceptNum, vo::getOfferAcceptCurrentNum,
                        vo::getOfferAcceptNumAIRecommend, vo::getOfferAcceptNumPrecisionAIRecommend
                ),
                new ProcessMetricConfig(
                        this::setOnboardNum,
                        vo::getOnboardNum, vo::getOnboardCurrentNum,
                        vo::getOnboardNumAIRecommend, vo::getOnboardNumPrecisionAIRecommend
                ),
                new ProcessMetricConfig(
                        this::setEliminateNum,
                        vo::getEliminateNum, vo::getEliminateCurrentNum,
                        vo::getEliminateNumAIRecommend, vo::getEliminateNumPrecisionAIRecommend
                ));
    }


    /**
     * 设置单个流程指标
     */
    static void setProcessMetric(ProcessMetricConfig config, RecruitingKpiReportSearchDto searchDto) {
        String result = switch (searchDto.getApplicationStatusType()) {
            case ALL -> {
                if (searchDto.getAiTalentType() == null) {
                    yield safeLong(config.allValueSupplier.get());
                }
                yield switch (searchDto.getAiTalentType()) {
                    case TALENT_COUNT -> formatWithAI(
                            config.allValueSupplier.get(),
                            config.aiRecommendSupplier.get()
                    );
                    case APPLICATION_COUNT -> formatWithAI(
                            config.allValueSupplier.get(),
                            config.precisionAiRecommendSupplier.get()
                    );
                };
            }
            case CURRENT -> {
                if (searchDto.getAiTalentType() == null) {
                    yield safeLong(config.currentValueSupplier.get());
                }
                yield switch (searchDto.getAiTalentType()) {
                    case TALENT_COUNT -> formatWithAI(
                            config.currentValueSupplier.get(),
                            config.aiRecommendSupplier.get()
                    );
                    case APPLICATION_COUNT -> formatWithAI(
                            config.currentValueSupplier.get(),
                            config.precisionAiRecommendSupplier.get()
                    );
                };
            }
        };

        config.setter.accept(result);
    }

    /**
     * 格式化带AI推荐的数值
     */
    static String formatWithAI(Long value, Long aiValue) {
        return "%s（%s）".formatted(safeLong(value), safeLong(aiValue));
    }

    /**
     * 流程指标配置类
     */
    record ProcessMetricConfig(Consumer<String> setter, Supplier<Long> allValueSupplier,
                               Supplier<Long> currentValueSupplier, Supplier<Long> aiRecommendSupplier,
                               Supplier<Long> precisionAiRecommendSupplier) {
    }
}
