package com.altomni.apn.report.domain.olap;


import com.altomni.apn.common.enumeration.permission.DataScope;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import org.openapitools.client.model.*;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import static org.openapitools.client.model.ChartDataAdhocMetricSchema.*;
import static org.openapitools.client.model.ChartDataColumn.JSON_PROPERTY_COLUMN_NAME;
import static org.openapitools.client.model.ChartDataQueryObjectExtras.TimeGrainSqlaEnum;


/**
 * olap 查询建造者，字段校验，权限处理，默认值设置等
 */
public class ChartQueryBuilder {

    private static final String TENANT_ID = "tenant_id";
    private static final String PERMISSION_USER_ID = "puser_id";

    private static final String PERMISSION_TEAM_ID = "pteam_id";

    private QueryContext queryContext;

    private Function<Long, Integer> fetchDataScope;

    private Function<Long, List<Long>> fetchAuthedTeamIds;

    public static ChartQueryBuilder withContext(QueryContext queryContext) {
        ChartQueryBuilder builder = new ChartQueryBuilder();
        builder.queryContext = queryContext;
        return builder;
    }

    public ChartQueryBuilder dataScopeFetcher(Function<Long, Integer> fetchDataScope) {
        this.fetchDataScope = fetchDataScope;
        return this;
    }

    public ChartQueryBuilder teamIdsFetcher(Function<Long, List<Long>> fetchTeamIds) {
        this.fetchAuthedTeamIds = fetchTeamIds;
        return this;
    }

    public ChartDataQueryContextSchema buildQuery() {
        validateColumns();
        makePermissionFilter();

        // 时间默认是秒级别
        ChartDataQueryObject queryConfig = queryContext.getQueryConfig();
        queryConfig.setExtras(new ChartDataQueryObjectExtras().timeGrainSqla(TimeGrainSqlaEnum.PT1S));
        queryConfig.setRowLimit(1000);

        ChartDataQueryContextSchema chartQuery = new ChartDataQueryContextSchema();
        chartQuery.setQueries(List.of(queryConfig));
        chartQuery.setForce(true);
        chartQuery.setDatasource(new ChartDataDatasource().id(queryContext.getDataSetId()).type(ChartDataDatasource.TypeEnum.TABLE));
        chartQuery.setResultFormat(ChartDataQueryContextSchema.ResultFormatEnum.JSON);
        chartQuery.setResultType(ChartDataQueryContextSchema.ResultTypeEnum.FULL);

        return chartQuery;
    }


    private void validateColumns() {
        validateDimensions();
        validateMetrics();
        validateFilter();
    }

    private void validateDimensions() {
        List<String> dims = queryContext.provideAllDim();
        if (CollectionUtils.isEmpty(dims)) {
            return;
        }
        List<String> dataSetColumns = queryContext.provideColumnMap().values().stream()
            .map(DatasetRestApiGetTableColumn::getColumnName).toList();
        dims.forEach(dim -> {
            if (!dataSetColumns.contains(dim)) {
                throw new CustomParameterizedException("[APN Report] Dimension {} not found in dataset {}", dim, queryContext.getDataSetName())
                ;
            }
        });
    }

    private void validateMetrics() {
        if (CollectionUtils.isEmpty(queryContext.getQueryConfig().getMetrics())) {
            return;
        }
        List<String> dataSetColumns = queryContext.provideColumnNames();

        List<Object> metrics = new ArrayList<>();
        queryContext.provideMetrics().forEach(metric -> {
            if (metric instanceof LinkedHashMap map) {
                // 有 metric_name 的，是数据集内置的指标
                Object metricName = map.get("metric_name");
                if (metricName != null) {
                    metrics.add(metricName);
                    return;
                }

                Object columnObj = map.get(JSON_PROPERTY_COLUMN);

                if (Objects.isNull(columnObj) || (columnObj instanceof LinkedHashMap<?, ?> column
                    && !dataSetColumns.contains(column.get(JSON_PROPERTY_COLUMN_NAME)))) {
                    throw new CustomParameterizedException("[APN Report] Metric column {} not found in dataset {}",
                        map.get(JSON_PROPERTY_LABEL).toString(), queryContext.getDataSetName());
                }

                map.put(JSON_PROPERTY_EXPRESSION_TYPE, ExpressionTypeEnum.SIMPLE);
                metrics.add(map);
            }
        });
        queryContext.getQueryConfig().setMetrics(metrics);
    }

    private void validateFilter() {
        if (CollectionUtils.isEmpty(queryContext.provideFilters())) {
            return;
        }
        List<String> dataSetColumns = queryContext.provideColumnNames();
        queryContext.provideFilters().forEach(filter -> {
            if (filter.getCol() == null || !dataSetColumns.contains(filter.getCol().toString())) {
                throw new CustomParameterizedException("[APN Report] Metric column {} not found in dataset {}",
                    filter.getCol().toString(), queryContext.getDataSetName());
            }
        });

    }

    private void makePermissionFilter() {
        List<String> columnNames = queryContext.provideColumnNames();

        List<String> tenantIdColumn = columnNames.stream().filter(columnName -> columnName.endsWith(TENANT_ID)).toList();

        List<ChartDataFilter> filters = CollectionUtils.isEmpty(queryContext.provideFilters()) ? new ArrayList<>()
            : new ArrayList<>(queryContext.provideFilters());

        tenantIdColumn.stream().map(name -> {
            ChartDataFilter filter = new ChartDataFilter();
            filter.setCol(name);
            filter.setOp(ChartDataFilter.OpEnum.EQUAL);
            filter.setVal(SecurityUtils.getTenantId());
            return filter;
        }).forEach(filters::add);

        if (SecurityUtils.isAdmin()) {
            queryContext.getQueryConfig().setFilters(filters);
            return;
        }

        Integer dataScope = fetchDataScope.apply(SecurityUtils.getUserId());

        if (DataScope.PERMISSION_SELF.toDbValue().equals(dataScope)) {
            columnNames.stream().filter(columnName -> columnName.endsWith(PERMISSION_USER_ID)).findFirst().ifPresent(name -> {
                ChartDataFilter filter = new ChartDataFilter();
                filter.setCol(name);
                filter.setOp(ChartDataFilter.OpEnum.EQUAL);
                filter.setVal(SecurityUtils.getUserId());
                filters.add(filter);
            });
        } else if (DataScope.PERMISSION_TEAM.toDbValue().equals(dataScope)) {
            List<Long> authedTeamIds = fetchAuthedTeamIds.apply(SecurityUtils.getUserId());
            columnNames.stream().filter(columnName -> columnName.endsWith(PERMISSION_TEAM_ID)).forEach(name -> {
                ChartDataFilter filter = new ChartDataFilter();
                filter.setCol(name);
                filter.setOp(ChartDataFilter.OpEnum.IN);
                filter.setVal(authedTeamIds);
                filters.add(filter);
            });
        }

        queryContext.getQueryConfig().setFilters(filters);
    }


}
