package com.altomni.apn.report.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.ContactTypeConverter;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * A LinkedinStats.
 */
@Entity
@Table(name = "linkedin_stats")
public class LinkedinStats extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "linkedin")
    @NotNull
    private String linkedin;

    @Column(name = "type")
    @Convert(converter = ContactTypeConverter.class)
    private ContactType type;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "count")
    private Integer count;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public LinkedinStats userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getLinkedin() {
        return linkedin;
    }

    public LinkedinStats linkedin(String linkedin) {
        this.linkedin = linkedin;
        return this;
    }

    public void setLinkedin(String linkedin) {
        this.linkedin = linkedin;
    }

    public ContactType getType() {
        return type;
    }

    public LinkedinStats type(ContactType type) {
        this.type = type;
        return this;
    }

    public void setType(ContactType type) {
        this.type = type;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public LinkedinStats tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getCount() {
        return count;
    }

    public LinkedinStats count(Integer count) {
        this.count = count;
        return this;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LinkedinStats linkedinStats = (LinkedinStats) o;
        if (linkedinStats.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), linkedinStats.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "LinkedinStats{" +
            "id=" + id +
            ", userId=" + userId +
            ", linkedin='" + linkedin + '\'' +
            ", type=" + type +
            ", tenantId=" + tenantId +
            ", count=" + count +
            '}';
    }
}
