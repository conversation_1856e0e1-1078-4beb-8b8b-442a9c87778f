package com.altomni.apn.report.dto.e5;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAdoptionReportEmailStatsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long emailCount = 0L;

    private Set<Long> contactIds = new HashSet<>();

    private Set<Long> talentIds = new HashSet<>();

    private Long uniqueEmailCount = 0L;

}
