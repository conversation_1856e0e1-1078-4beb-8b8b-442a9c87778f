package com.altomni.apn.report.domain.enumeration;

public enum CountryPackageEnum {
    EU(-1, "EU Delivery"),
    SEA(-2, "SEA Delivery"),
    US(1, "US Delivery"),
    CA(2, "Canada Delivery"),
    CN(7, "China Delivery"),
    UK(3, "UK Delivery"),
    NL(115, "Netherland Delivery"),
    IN(74, "India Delivery"),
    JP(5, "Japan Delivery"),
    OTHERS(-3, "Others");

    private final Integer dbValue;

    private final String display;


    CountryPackageEnum(int dbValue, String display) {
        this.dbValue = dbValue;
        this.display = display;
    }

    public Integer getDbValue() {
        return dbValue;
    }

    public String getDisplay() {
        return display;
    }

    public static CountryPackageEnum getEnum(Integer dbValue) {
        for (CountryPackageEnum e : CountryPackageEnum.values()) {
            if (e.getDbValue().equals(dbValue)) {
                return e;
            }
        }
        return null;
    }
}
