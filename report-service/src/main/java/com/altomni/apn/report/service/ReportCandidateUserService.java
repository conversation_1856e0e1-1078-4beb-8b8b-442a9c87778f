package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.s3.CandidateUserCountSearchVo;
import com.altomni.apn.report.domain.vo.s3.CandidateUserDetailSearchVo;
import com.altomni.apn.report.dto.s3.*;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

public interface ReportCandidateUserService {

    ResponseEntity<List<UserCandidateCountDTO>> countCandidateReport(CandidateUserCountSearchVo searchVo) throws IOException;

    ResponseEntity<List<UserCandidateDetailDTO>> detailUserCandidatesCreation(CandidateUserDetailSearchVo searchVo, Pageable pageable);

    ResponseEntity<List<UserCandidateModificationDetailDTO>> detailUserCandidatesModification(CandidateUserDetailSearchVo searchVo, Pageable pageable);

    ResponseEntity<List<UserTalentNoteDetailDTO>> detailUserCandidatesNote(CandidateUserDetailSearchVo searchVo, Pageable pageable);

    void exportCountCandidateReport(CandidateUserCountSearchVo searchVo,HttpServletResponse response);
}
