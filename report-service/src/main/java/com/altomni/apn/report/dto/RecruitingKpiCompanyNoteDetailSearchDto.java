package com.altomni.apn.report.dto;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiDetailBaseDto;
import lombok.Data;

@Data
public class RecruitingKpiCompanyNoteDetailSearchDto extends RecruitingKpiDetailBaseDto {

    public String getOrderBySql() {
        if (ObjectUtil.isEmpty(getSort())) {
            return "";
        }
        return switch (getSort().getProperty()) {
            case "companyName" -> " order by CONVERT( c.full_business_name USING gbk) " + getSort().getDirection();
            case "contactDate" -> " order by ccn.last_contact_date " + getSort().getDirection();
            default -> "";
        };
    }

}
