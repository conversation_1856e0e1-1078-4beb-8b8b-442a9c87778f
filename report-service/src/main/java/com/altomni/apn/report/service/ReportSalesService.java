package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.ReportSalesCompanyVo;
import com.altomni.apn.report.domain.vo.ReportSalesDetailVo;
import com.altomni.apn.report.domain.vo.ReportSalesViewStatsVo;
import com.altomni.apn.report.dto.ReportSalesParamDto;
import com.altomni.apn.report.dto.SaleDetailDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface ReportSalesService {

    List<ReportSalesCompanyVo> salesCompanyFilter(ReportSalesParamDto reportParam);

    List<ReportSalesViewStatsVo> searchSales(ReportSalesParamDto reportParam);

    List<ReportSalesViewStatsVo> salesByWeeklyNewOffer(ReportSalesParamDto reportSalesParamDto);

    List<ReportSalesDetailVo> getSalesDetails(SaleDetailDTO saleDetailDTO);

    void exportSalesFteDetailsExcel(HttpServletResponse response, SaleDetailDTO saleDetailDTO);

}
