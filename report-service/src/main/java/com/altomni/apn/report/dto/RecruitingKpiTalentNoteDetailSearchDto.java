package com.altomni.apn.report.dto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiDetailBaseDto;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiTalentNoteDetailSearchDto extends RecruitingKpiDetailBaseDto {

    private TalentNoteType talentNoteType;

    private TalentNoteDetailSearchDto detail;

    public String getOrderBySql() {
        if (ObjectUtil.isEmpty(getSort())) {
            return " ";
        }
        return switch (getSort().getProperty()) {
            case "fullName" -> " order by CONVERT( t.full_name USING gbk) " + getSort().getDirection();
            case "title" -> " order by CONVERT( tn.title USING gbk) " + getSort().getDirection();
            case "jobSearchStatus" -> " order by t.motivation_id  " + getSort().getDirection();
            case "createdDate" -> " order by tn.created_date " + getSort().getDirection();
            case "lastModifiedDate" -> " order by tn.last_modified_date " + getSort().getDirection();
            case "priority" -> " order by tn.priority " + getSort().getDirection();
            case "noteType" -> " order by tn.note_type " + getSort().getDirection();
            default -> "";
        };
    }

    public void appendPermissionData(StringBuilder sb, Map<String, Object> whereParamMap, TeamDataPermissionRespDTO teamDTO) {
        if (BooleanUtil.isTrue(teamDTO.getSelf())) {
            sb.append(" and tn.puser_id = :userId ");
            whereParamMap.put("userId", getSearchUserId());
        } else if (CollUtil.isNotEmpty(teamDTO.getNestedTeamIds())) {
            sb.append("and put.team_id in :teamIds ");
            whereParamMap.put("teamIds", teamDTO.getNestedTeamIds());
        }
    }

    public void appendDetailCondition(StringBuilder sb, Map<String, Object> whereParamMap) {
        if (ObjectUtil.isNotEmpty(detail)) {
            if (StrUtil.isNotBlank(detail.getFullName())) {
                sb.append(" and t.full_name like :fullName ");
                whereParamMap.put("fullName", "%" + detail.getFullName() + "%");
            }
            if (ObjectUtil.isNotEmpty(detail.getTalentId())) {
                sb.append(" and t.id = :talentId ");
                whereParamMap.put("talentId", detail.getTalentId());
            }
            if (StrUtil.isNotBlank(detail.getTitle())) {
                sb.append(" and tn.title like :title ");
                whereParamMap.put("title", "%" + detail.getTitle() + "%");
            }
            if (StrUtil.isNotBlank(detail.getNote())) {
                sb.append(" and tn.note like :note ");
                whereParamMap.put("note", "%" + detail.getNote() + "%");
            }
            if (ObjectUtil.isNotEmpty(detail.getJobSearchStatus())) {
                sb.append(" and t.motivation_id = :jobSearchStatus ");
                whereParamMap.put("jobSearchStatus", detail.getJobSearchStatus());
            }
            if (ObjectUtil.isNotEmpty(detail.getCreatedBy())) {
                sb.append(" and tn.puser_id = :createdBy ");
                whereParamMap.put("createdBy", detail.getCreatedBy());
            }
            if (ObjectUtil.isNotEmpty(detail.getLastModifiedBy())) {
                sb.append(" and tn.last_update_user_id = :lastModifiedBy ");
                whereParamMap.put("lastModifiedBy", detail.getLastModifiedBy());
            }
        }
    }

}