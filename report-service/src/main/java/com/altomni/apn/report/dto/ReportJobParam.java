package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import org.apache.commons.lang3.StringUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.Collection;
import java.util.Set;

public class ReportJobParam implements Serializable {

    private static final long serialVersionUID = -735396579460625014L;

    private String fromDate;

    private String toDate;

    private String timeZone = "America/Los_Angeles";

    private String jobCountry = "all";

    private Long teamId;

    private Collection<Long> teamIds;

    private Long divisionId;

    private Boolean allOpenJobs = Boolean.FALSE;

    private JobType jobType;

    private String jobId;

    @JsonIgnore
    private Long privateJobTeamId;

    @JsonIgnore
    private boolean allDataPermission;

    private static final String DEFAULT_TIMEZONE = "America/Los_Angeles";

    private static final String ALL = "all";

    public ReportJobParam() {}

    public String getFromDate() {
        return fromDate;
    }

    public ReportJobParam fromDate(String fromDate) {
        this.fromDate = fromDate;
        return this;
    }

    public void setFromDate(String fromDate) {
        this.fromDate = fromDate;
    }

    public String getToDate() {
        return toDate;
    }

    public ReportJobParam toDate(String toDate) {
        this.toDate = toDate;
        return this;
    }

    public void setToDate(String toDate) {
        this.toDate = toDate;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public ReportJobParam timeZone(String timeZone) {
        this.timeZone = StringUtils.isBlank(timeZone) ? DEFAULT_TIMEZONE : timeZone;
        return this;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public Long getDivisionId() {
        return divisionId;
    }

    public ReportJobParam divisionId(Long divisionId) {
        this.divisionId = divisionId;
        return this;
    }

    public void setDivisionId(Long divisionId) {
        this.divisionId = divisionId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public ReportJobParam teamId(Long teamId) {
        this.teamId = teamId;
        return this;
    }

    public ReportJobParam teamIds(Set<Long> teamIds) {
        this.teamIds = teamIds;
        return this;
    }

    public Collection<Long> getTeamIds() {
        return teamIds;
    }

    public void setTeamIds(Collection<Long> teamIds) {
        this.teamIds = teamIds;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public String getJobCountry() {
        return jobCountry;
    }

    public ReportJobParam jobCountry(String jobCountry) {
        this.jobCountry = StringUtils.isBlank(jobCountry) ? ALL : jobCountry;
        return this;
    }

    public void setJobCountry(String jobCountry) {
        this.jobCountry = jobCountry;
    }

    public Boolean getAllOpenJobs() {
        return allOpenJobs;
    }

    public ReportJobParam allOpenJobs(Boolean allOpenJobs) {
        this.allOpenJobs = allOpenJobs == null ? Boolean.FALSE : allOpenJobs;
        return this;
    }

    public void setAllOpenJobs(Boolean allOpenJobs) {
        this.allOpenJobs = allOpenJobs;
    }

    public JobType getJobType() {
        return jobType;
    }

    public ReportJobParam jobType(JobType jobType) {
        this.jobType = jobType;
        return this;
    }

    public void setJobType(JobType jobType) {
        this.jobType = jobType;
    }


    public String getJobId() {
        return jobId;
    }

    public ReportJobParam jobId(String jobId) {
        this.jobId = jobId;
        return this;
    }

    public void setJobId(String jobId) {
        this.jobId = jobId;
    }

    public Long getPrivateJobTeamId() {
        return privateJobTeamId;
    }

    public void setPrivateJobTeamId(Long privateJobTeamId) {
        this.privateJobTeamId = privateJobTeamId;
    }

    public boolean isAllDataPermission() {
        return allDataPermission;
    }

    public void setAllDataPermission(boolean allDataPermission) {
        this.allDataPermission = allDataPermission;
    }

    @Override
    public String toString() {
        return "ReportJobParam{" +
                "fromDate='" + fromDate + '\'' +
                ", toDate='" + toDate + '\'' +
                ", timeZone='" + timeZone + '\'' +
                ", jobCountry='" + jobCountry + '\'' +
                ", teamId=" + teamId +
                ", teamIds=" + teamIds +
                ", divisionId=" + divisionId +
                ", allOpenJobs=" + allOpenJobs +
                ", jobType=" + jobType +
                ", jobId='" + jobId + '\'' +
                ", privateJobTeamId=" + privateJobTeamId +
                ", hasAllDataPermission=" + allDataPermission +
                '}';
    }
}
