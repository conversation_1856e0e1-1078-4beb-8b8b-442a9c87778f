package com.altomni.apn.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportJobByCompanyVo;
import com.altomni.apn.report.dto.ReportJobParam;
import com.altomni.apn.report.service.ReportJobCompanyService;
import com.altomni.apn.report.util.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Service("reportJobService")
public class ReportJobCompanyServiceImpl extends ReportJobBaseServiceImpl implements ReportJobCompanyService {

    @Override
    public List<ReportJobByCompanyVo> getJobsByCompanySourceData(ReportJobParam reportParam) throws ExecutionException, InterruptedException {
        log.info("[apn] getJobsByCompanySourceData start ...... ");
        StopWatch startWatch = new StopWatch();
        startWatch.start("getJobsByCompanySourceDataTask");
        List<Map<String, Object>> mapList = this.searchJobByCompany(reportParam);
        List<ReportJobByCompanyVo> reportJobByCompanyVoList = this.convertReportJobByCompanyVoEntity(mapList);
        this.addGrandTotal(reportJobByCompanyVoList);
        startWatch.stop();
        log.info("[apn] getJobsByCompanySourceData time = [{} ms] ", startWatch.getTotalTimeMillis());
        log.info("[apn] getJobsByCompanySourceData end ...... ");
        return reportJobByCompanyVoList;
    }

    private List<ReportJobByCompanyVo> convertReportJobByCompanyVoEntity(List<Map<String, Object>> mapList) {
        log.info("[apn] convertReportJobByCompanyVoEntity mapList = {}", mapList.size());
        List<ReportJobByCompanyVo> reportJobByCompanyVoList = convertEntity(mapList, ReportJobByCompanyVo.class);
        log.info("[apn] reportJobByCompanyVoList size = {}", reportJobByCompanyVoList.size());
        List<ReportJobByCompanyVo> resultList = new ArrayList<>();
        reportJobByCompanyVoList = reportJobByCompanyVoList.stream().filter(vo -> ObjectUtil.isNotNull(vo.getId())).collect(Collectors.toList());
        Map<Long, List<ReportJobByCompanyVo>> longListMap = reportJobByCompanyVoList.stream().collect(Collectors.groupingBy(ReportJobByCompanyVo::getId));
        longListMap.forEach((k,v) -> {
            List<ReportJobByCompanyVo> applicationVoList = v.stream().filter(vo -> Objects.equals(vo.getType(), APPLICATION_TYPE)).collect(Collectors.toList());
            v.stream().filter(vo -> Objects.equals(vo.getType(), JOB_ID)).findFirst().ifPresent(job -> {
                ReportJobByCompanyVo jobVo = new ReportJobByCompanyVo();
                BeanUtil.copyProperties(job, jobVo);
                if (CollUtil.isNotEmpty(applicationVoList)) {
                    String[] ignore = new String[]{"openings", "openingsIds"};
                    BeanUtil.copyProperties(applicationVoList.get(0), jobVo, ignore);
                }
                resultList.add(jobVo);
            });
        });
        return resultList;
    }

    private void addGrandTotal(List<ReportJobByCompanyVo> reportJobByCompanyVoList) {
        ReportJobByCompanyVo reportJobByCompanyVo = new ReportJobByCompanyVo();
        reportJobByCompanyVo.setName("Grand Total");
        if (CollUtil.isNotEmpty(reportJobByCompanyVoList)) {
            reportJobByCompanyVo.setOpenings(reportJobByCompanyVoList.stream().mapToLong(ReportJobByCompanyVo::getOpenings).sum());
            reportJobByCompanyVo.setSubmitToJobNum(reportJobByCompanyVoList.stream().mapToLong(ReportJobByCompanyVo::getSubmitToJobNum).sum());
            reportJobByCompanyVo.setSubmitToClientNum(reportJobByCompanyVoList.stream().mapToLong(ReportJobByCompanyVo::getSubmitToClientNum).sum());
            reportJobByCompanyVo.setInterviewNum(reportJobByCompanyVoList.stream().mapToLong(ReportJobByCompanyVo::getInterviewNum).sum());
            reportJobByCompanyVo.setOfferNum(reportJobByCompanyVoList.stream().mapToLong(ReportJobByCompanyVo::getOfferNum).sum());
            reportJobByCompanyVo.setOfferAcceptedNum(reportJobByCompanyVoList.stream().mapToLong(ReportJobByCompanyVo::getOfferAcceptedNum).sum());
            reportJobByCompanyVo.setOnBoardedNum(reportJobByCompanyVoList.stream().mapToLong(ReportJobByCompanyVo::getOnBoardedNum).sum());
        }
        reportJobByCompanyVoList.add(reportJobByCompanyVo);
    }

    @Override
    public void exportJobByCompanyByExcel(ReportJobParam reportParam, HttpServletResponse response) throws ExecutionException, InterruptedException {
        List<ReportJobByCompanyVo> reportJobByCompanyVoList = getJobsByCompanySourceData(reportParam);
        ExcelUtil.downloadExcel(response, ReportJobByCompanyVo.class, reportJobByCompanyVoList, "", "JobReportByCompany.xlsx", true);
    }

    private List<Map<String, Object>> searchJobByCompany(ReportJobParam reportParam) throws ExecutionException, InterruptedException {
        List<Map<String, Object>> mapList = new CopyOnWriteArrayList<>();
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<Map<String, Object>>> cf1 = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("applicationTask");
            StringBuilder applicationSql = new StringBuilder();
            Map<Integer, Object> applicationParamMap = new HashMap<>(16);
            this.createApplicationSql(applicationSql, reportParam, applicationParamMap);
            List<Map<String, Object>> mapList1 = searchDataWithMap(applicationSql.toString(), applicationParamMap);
            stopWatch.stop();
            log.info("[apn] searchJobByCompanyApplicationTask param = {}, time [{} ms] \n {}", JSONUtil.toJsonStr(applicationParamMap), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return mapList1;
        });
        CompletableFuture<List<Map<String, Object>>> cf2 = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            StopWatch stopWatch = new StopWatch();
            stopWatch.start("jobTask");
            StringBuilder jobSql = new StringBuilder();
            Map<Integer, Object> jobParamMap = new HashMap<>(16);
            createJobSql(jobSql, reportParam, jobParamMap);
            List<Map<String, Object>> mapList2 = searchDataWithMap(jobSql.toString(), jobParamMap);
            stopWatch.stop();
            log.info("[apn] searchJobByCompanyJobTask param = {}, time [{} ms] \n {}", JSONUtil.toJsonStr(jobParamMap), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return mapList2;
        });
        mapList.addAll(cf1.get());
        mapList.addAll(cf2.get());
        return mapList;
    }

    private void createJobSql(StringBuilder sb, ReportJobParam reportParam, Map<Integer, Object> conditionParamMap) {
        // condition sql
        StringBuilder sqlSb = new StringBuilder();
        StringBuilder jobSqlSb = new StringBuilder();
        jobSqlSb.append(" select c.id, c.full_business_name as name, j.id as job_id, j.openings as openings ")
                .append(" from company c inner join job j on j.company_id = c.id ")
                .append(" inner join recruitment_process rp on rp.id = j.recruitment_process_id ");
        jobSqlSb.append(" where j.tenant_id = ?1 ").append(AND_SYMBOL);
        conditionParamMap.put(1, SecurityUtils.getTenantId());
        appendConditionSql(sqlSb, jobSqlSb, reportParam, conditionParamMap);
        sqlSb.append(" group by c.id, j.id");
        sb.append(" select a.id, a.name, sum(a.openings) as openings, GROUP_CONCAT(a.job_id) as openings_ids,  ")
                .append(" 0 as submit_to_job_num, '' as activity_ids, 0 as submit_to_client_num, 0 as interview_num, ")
                .append(" 0 as offer_num, 0 as offer_accepted_num, 0 as on_boarded_num, ")
                .append(" 'jobId' as type ")
                .append(" from ( ").append(sqlSb).append(" ) a group by a.id");
    }

    private void createApplicationSql(StringBuilder sb, ReportJobParam reportParam, Map<Integer, Object> conditionParamMap) {
        StringBuilder applicationSqlSb = new StringBuilder();
        //select sql
        applicationSqlSb.append(" SELECT c.id, c.full_business_name as name, ")
                .append(" 0 AS openings, ")
                .append(" '' as openings_ids, ")
                .append(" count( DISTINCT trpstb.talent_recruitment_process_id ) AS submit_to_job_num, ")
                .append(" GROUP_CONCAT(DISTINCT trp.id) as activity_ids, ")
                .append(" count( DISTINCT trpstc.talent_recruitment_process_id ) AS submit_to_client_num, ")
                .append(" count( DISTINCT trpi.talent_recruitment_process_id) as interview_num, ")
                .append(" count( DISTINCT trpo.talent_recruitment_process_id) as offer_num, ")
                .append(" count( DISTINCT trpioa.talent_recruitment_process_id) as offer_accepted_num, ")
                .append(" count( DISTINCT trpon.talent_recruitment_process_id) as on_boarded_num, ")
                .append(" 'applicationId' as type ")
                .append(" FROM talent_recruitment_process trp ")
                .append(" INNER JOIN job j ON j.id = trp.job_id ")
                .append(" INNER JOIN company c ON c.id = j.company_id ")
                .append(" INNER JOIN recruitment_process rp on rp.id = trp.recruitment_process_id ")
                .append(" LEFT JOIN talent_recruitment_process_submit_to_job trpstb ON trpstb.talent_recruitment_process_id = trp.id ")
                .append(" LEFT JOIN talent_recruitment_process_submit_to_client trpstc ON trpstc.talent_recruitment_process_id = trp.id ")
                .append(" LEFT JOIN talent_recruitment_process_offer trpo ON trpo.talent_recruitment_process_id = trp.id ")
                .append(" LEFT JOIN talent_recruitment_process_ipg_offer_accept trpioa ON trpioa.talent_recruitment_process_id = trp.id ")
                .append(" LEFT JOIN talent_recruitment_process_node trpon ON trpon.talent_recruitment_process_id = trp.id and trpon.node_type=60  and trpon.node_status = 1 ")
                .append(" LEFT JOIN talent_recruitment_process_interview trpi ON trpi.talent_recruitment_process_id = trp.id ");
        applicationSqlSb.append(" where j.tenant_id = ?1 AND ");
        conditionParamMap.put(1, SecurityUtils.getTenantId());
        appendConditionSql(sb, applicationSqlSb, reportParam, conditionParamMap);
        sb.append(" group by c.id ");
    }

    private void appendConditionSql(StringBuilder sb, StringBuilder tableSql, ReportJobParam reportParam, Map<Integer, Object> conditionParamMap) {
        super.appendBase(tableSql, reportParam, conditionParamMap);
        if (reportParam.getAllOpenJobs()) {
            tableSql.append(" j.status IN (0, 1, 2) ");
            tableSql.append(AND_SYMBOL);
        }
//        if (CollUtil.isNotEmpty(reportParam.getTeamIds())) {
//            tableSql.append(" j.pteam_id in ?");
//            conditionParamMap.put(conditionParamMap.size() + 1, reportParam.getTeamIds());
//            tableSql.append(conditionParamMap.size()).append(AND_SYMBOL);
//        }else {
//            tableSql.append(" j.puser_id in ?");
//            conditionParamMap.put(conditionParamMap.size() + 1, SecurityUtils.getUserId());
//            tableSql.append(conditionParamMap.size()).append(AND_SYMBOL);
//        }
        //Concatenating the final sql
        sb.append(StrUtil.subBefore(tableSql, AND_SYMBOL, true));
    }

}
