package com.altomni.apn.report.service;

import com.altomni.apn.report.domain.vo.ContractorHireVo;
import com.altomni.apn.report.dto.ContractorSearchDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.concurrent.ExecutionException;

public interface ContractorReportHiresService {

    Page<ContractorHireVo> getContractorHires(ContractorSearchDto searchDto, Pageable pageable) throws ExecutionException, InterruptedException;

    void exportContractorHires(ContractorSearchDto searchDto, Pageable pageable, HttpServletResponse response);
}
