package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

public class ReportPipelineDetailsDto implements Serializable {

    private static final long serialVersionUID = -5929723713720150744L;

    private String activityId;

    private NodeType status;

    private Integer size = 10000;

    private String timeZone = "America/Los_Angeles";

    private static final String ALL = "all";

    private static final String DEFAULT_TIMEZONE = "America/Los_Angeles";

    public ReportPipelineDetailsDto() {}

    public String getActivityId() {
        return activityId;
    }

    public ReportPipelineDetailsDto activityId(String activityId) {
        this.activityId = activityId;
        return this;
    }

    public void setActivityId(String activityId) {
        this.activityId = activityId;
    }

    public NodeType getStatus() {
        return status;
    }

    public void setStatus(NodeType status) {
        this.status = status;
    }

    public ReportPipelineDetailsDto status(NodeType status) {
        this.status = status;
        return this;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public ReportPipelineDetailsDto timeZone(String timeZone) {
        this.timeZone = StringUtils.isBlank(timeZone) ? DEFAULT_TIMEZONE : timeZone;
        return this;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
