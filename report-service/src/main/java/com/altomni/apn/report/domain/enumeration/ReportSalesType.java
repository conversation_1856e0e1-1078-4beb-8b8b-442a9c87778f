package com.altomni.apn.report.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum ReportSalesType implements ConvertedEnum<Integer> {

    YEAR(0),
    QUARTER(1),
    MONTH(2),
    WEEK(3);

    private final Integer dbValue;

    ReportSalesType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ReportSalesType, Integer> resolver =
            new ReverseEnumResolver<>(ReportSalesType.class, ReportSalesType::toDbValue);

    public static ReportSalesType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
