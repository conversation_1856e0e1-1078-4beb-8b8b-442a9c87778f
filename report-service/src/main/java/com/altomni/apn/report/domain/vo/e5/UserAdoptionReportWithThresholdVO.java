package com.altomni.apn.report.domain.vo.e5;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class UserAdoptionReportWithThresholdVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long userActiveThreshold;

    private Long personalViewCurrentUserActiveDuration;

    private List<UserAdoptionReportVO> userAdoptionReport = Collections.emptyList();
}
