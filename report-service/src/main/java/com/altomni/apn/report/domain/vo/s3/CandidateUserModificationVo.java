package com.altomni.apn.report.domain.vo.s3;

import com.altomni.apn.report.dto.s3.UserCandidateDetailDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CandidateUserModificationVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<UserCandidateDetailDTO> userCandidateDetailList;

    private Integer detailCount;
}
