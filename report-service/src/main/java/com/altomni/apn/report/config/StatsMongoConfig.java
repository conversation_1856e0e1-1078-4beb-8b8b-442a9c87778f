package com.altomni.apn.report.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;

@Configuration
public class StatsMongoConfig {

    @Value("${spring.data.statsMongo.uri}")
    private String statsMongoUri;

//    @Bean(name = "statsMongoTemplate")
//    public MongoTemplate statsMongoTemplate() {
//        System.out.println(statsMongoUri);
//        MongoClient mongoClient = MongoClients.create(statsMongoUri);
//        return new MongoTemplate(mongoClient, "statistic");
//    }

    @Bean(name = "statsMongoTemplate")
    public MongoTemplate statsMongoTemplate() {
        ConnectionString connectionString = new ConnectionString(statsMongoUri);
        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(connectionString)
                .build();
        MongoClient mongoClient = MongoClients.create(settings);

        String databaseName = connectionString.getDatabase();
        if (databaseName == null) {
            throw new IllegalArgumentException("Database name must be specified in URI");
        }

        return new MongoTemplate(mongoClient, databaseName);
    }

}
