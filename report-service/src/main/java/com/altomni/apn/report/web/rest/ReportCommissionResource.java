package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportCommissionVO;
import com.altomni.apn.report.dto.ReportCommissionSearchDTO;
import com.altomni.apn.report.service.ReportCommissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3/report/commission")
public class ReportCommissionResource {

    @Resource
    private ReportCommissionService reportCommissionService;

    @PostMapping("/export")
    public void exportCommissionReport(@RequestBody ReportCommissionSearchDTO dto, HttpServletResponse response) {
        log.info("[APN @{}] export commission report, parma = {}", SecurityUtils.getUserId(), dto);
        reportCommissionService.exportCommissionReport(dto, response);
    }

    @PostMapping("/search")
    public ResponseEntity<List<ReportCommissionVO>> searchCommissionReport(@RequestBody ReportCommissionSearchDTO dto) {
        log.info("[APN @{}] search commission report, parma = {}", SecurityUtils.getUserId(), dto);
        StopWatch stopWatch = new StopWatch("commission-report");
        stopWatch.start();
        Page<ReportCommissionVO>  page = reportCommissionService.searchCommissionReport(dto);
        stopWatch.stop();
        log.info(" searchCommissionReport time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/commision");
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

}
