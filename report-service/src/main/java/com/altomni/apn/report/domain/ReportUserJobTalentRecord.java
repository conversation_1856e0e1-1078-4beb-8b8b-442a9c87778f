package com.altomni.apn.report.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A ReportUserJobTalent.
 * <AUTHOR>
 */
@Entity
@Table(name = "report_user_job_talent")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportUserJobTalentRecord implements Serializable {

    private static final long serialVersionUID = 1L;
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @JsonIgnore
    private Long id;

    @JsonIgnore
    @Column(name = "batch_id")
    private String batchId;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "user_name")
    private String userName;

    @Column(name = "talent_count")
    private Integer talentCount;

    @Column(name = "talent_id")
    private String talentIds;

    @Column(name = "job_count")
    private Integer jobCount;

    @Column(name = "job_id")
    private String jobIds;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchId() {
        return batchId;
    }

    public void setBatchId(String batchId) {
        this.batchId = batchId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getTalentCount() {
        return talentCount;
    }

    public void setTalentCount(Integer talentCount) {
        this.talentCount = talentCount;
    }

    public String getTalentIds() {
        return talentIds;
    }

    public void setTalentIds(String talentIds) {
        this.talentIds = talentIds;
    }

    public Integer getJobCount() {
        return jobCount;
    }

    public void setJobCount(Integer jobCount) {
        this.jobCount = jobCount;
    }

    public String getJobIds() {
        return jobIds;
    }

    public void setJobIds(String jobIds) {
        this.jobIds = jobIds;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ReportUserJobTalentRecord reportUserJobTalent = (ReportUserJobTalentRecord) o;
        if (reportUserJobTalent.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), reportUserJobTalent.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "ReportUserJobTalentRecord{" +
                "id=" + id +
                ", batchId='" + batchId + '\'' +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", talentCount=" + talentCount +
                ", talentIds='" + talentIds + '\'' +
                ", jobCount=" + jobCount +
                ", jobIds='" + jobIds + '\'' +
                '}';
    }
}
