package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.ReportAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.config.env.ReportApiPromptProperties;
import com.altomni.apn.report.domain.DormantJobCountDTO;
import com.altomni.apn.report.domain.DormantJobDTO;
import com.altomni.apn.report.domain.enumeration.ReportTableType;
import com.altomni.apn.report.domain.vo.s3.DormantApplicationCountVO;
import com.altomni.apn.report.domain.vo.s3.DormantApplicationVO;
import com.altomni.apn.report.dto.s2.DormantApplicationJobCountDTO;
import com.altomni.apn.report.dto.s3.DormantApplicationCountDTO;
import com.altomni.apn.report.repository.DormantActivityRepository;
import com.altomni.apn.report.service.DormantActivityMonitorService;
import io.micrometer.core.annotation.Timed;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;


/**
 * REST controller for managing Report.
 */
@RestController
@RequestMapping("/api/v3")
public class DormantActivityMonitorResource {

    private final Logger log = LoggerFactory.getLogger(DormantActivityMonitorResource.class);

    private final DormantActivityRepository dormantActivityRepository;

    private final DormantActivityMonitorService dormantActivityMonitorService;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;
    private final ReportApiPromptProperties reportApiPromptProperties;

    public DormantActivityMonitorResource(DormantActivityRepository dormantActivityRepository,
                                          DormantActivityMonitorService dormantActivityMonitorService,
                                          CommonApiMultilingualConfig commonApiMultilingualConfig,
                                          ReportApiPromptProperties reportApiPromptProperties) {
        this.dormantActivityRepository = dormantActivityRepository;
        this.dormantActivityMonitorService = dormantActivityMonitorService;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.reportApiPromptProperties = reportApiPromptProperties;
    }

    @PostMapping("/dormant/jobs")
    @Timed
    public ResponseEntity<List<DormantJobCountDTO>> countAllDormantJobs(@RequestBody DormantApplicationJobCountDTO dormantApplicationJobCountDTO) {
        log.info("[APN: DormantActivityMonitor @{}] REST request to count all dormant jobs group by AM, params:{}", SecurityUtils.getUserId(), dormantApplicationJobCountDTO);
        List<DormantJobCountDTO> res = dormantActivityRepository.countAllDormantJobsByAmId(dormantApplicationJobCountDTO.getTeamIds(), dormantApplicationJobCountDTO.getUserId(), SecurityUtils.getTenantId());
        return ResponseEntity.ok(res);
    }

    @GetMapping("/dormant/jobs/{id}")
    @Timed
    public ResponseEntity<List<DormantJobDTO>> getDormantJobsByAmId(@PathVariable Long id) {
        log.info("[APN: DormantActivityMonitor @{}] REST request to get all dormant jobs associated to AM : {}}", SecurityUtils.getUserId(), id);
        List<DormantJobDTO> res = dormantActivityRepository.findAllDormantJobsByAmId(SecurityUtils.getTenantId(), id);
        return ResponseEntity.ok(res);
    }

    @PostMapping("/report/dormant/applications")
    @Timed
    public ResponseEntity<List<DormantApplicationCountVO>> countAllDormantApplications(@Valid @RequestBody DormantApplicationCountDTO dormantApplicationCountDTO) {
        log.info("[APN: DormantActivityMonitor @{}] REST request to count all dormant applications group by recruiter in param : {}.", SecurityUtils.getUserId(), dormantApplicationCountDTO);

        if(!dormantApplicationCountDTO.getStatus().hasDormantTime()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORT_COUNTALLDORMANTAPPLICATIONS_ILLEGALVALUE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }

        List<DormantApplicationCountVO> res = dormantActivityMonitorService.countAllDormantApplications(dormantApplicationCountDTO);
        return ResponseEntity.ok(res);
    }

    @GetMapping("/report/dormant/applications/{id}")
    @Timed
    public ResponseEntity<List<DormantApplicationVO>> getDormantApplicationsByRecruiterId(@PathVariable Long id, @RequestParam(value = "status") ReportTableType status) {
        log.info("[APN: DormantActivityMonitor @{}] REST request to get all dormant applications associated to recruiter : {} in status : {}}", SecurityUtils.getUserId(), id, status);

        if(!status.hasDormantTime()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ReportAPIMultilingualEnum.REPORT_COUNTALLDORMANTAPPLICATIONS_ILLEGALVALUE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),reportApiPromptProperties.getReportService()));
        }

        List<DormantApplicationVO> res = dormantActivityMonitorService.getDormantApplicationsByRecruiterId(id, status);
        return ResponseEntity.ok(res);
    }
}
