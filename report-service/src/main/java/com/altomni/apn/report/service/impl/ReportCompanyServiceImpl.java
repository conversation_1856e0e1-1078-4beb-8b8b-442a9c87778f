package com.altomni.apn.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.report.domain.vo.FteBdReportJobDetailVO;
import com.altomni.apn.report.domain.vo.FteBdReportOnboardDetailVO;
import com.altomni.apn.report.domain.vo.FteBdReportVO;
import com.altomni.apn.report.dto.FteBdReportDTO;
import com.altomni.apn.report.dto.FteBdReportDetailDTO;
import com.altomni.apn.report.repository.ReportCompanyRepository;
import com.altomni.apn.report.repository.ReportRepository;
import com.altomni.apn.report.service.ReportCompanyService;
import com.altomni.apn.report.service.user.UserService;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ReportCompanyServiceImpl implements ReportCompanyService {

    @Resource
    ReportCompanyRepository reportCompanyRepository;
    @Resource
    UserService userService;
    @Resource
    ReportRepository reportRepository;
    @Resource
    InitiationService initiationService;
    @Resource
    CachePermission cachePermission;

    @Override
    public List<FteBdReportVO> queryFteBdReportList(FteBdReportDTO dto, Pageable pageable, HttpHeaders headers) {
        final Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<Long> fteBdReportListCount = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return reportCompanyRepository.countFteBdReportList(dto);
        });
        List<FteBdReportVO> result = reportCompanyRepository.getFteBdReportList(dto, pageable, applyDataPermission("j1"));
        try {
            final Long count = fteBdReportListCount.get();
            headers.set("Pagination-Count", count.toString());
        } catch (InterruptedException | ExecutionException e) {
            log.error("[FteBdReportService Error]", e);
            throw new CustomParameterizedException("Internet error, try later please!");
        }
        result.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAm())) {
                List<UserCountryVO> userCountryList = new ArrayList<>();
                Arrays.stream(x.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserId(Long.valueOf(amCountry[0]));
                    vo.setCountryId(amCountry[1]);
                    userCountryList.add(vo);
                });
                x.setCoAmList(userCountryList);
            }
        });
        return result;
    }

    @Override
    public List<FteBdReportJobDetailVO> queryFteBdReportJobDetail(FteBdReportDetailDTO dto, Pageable pageable, HttpHeaders headers) {
        final Authentication authentication = SecurityUtils.getAuthentication();
        final String permission = applyDataPermission("j");
        CompletableFuture<Long> fteBdReportJobCount = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return reportCompanyRepository.countFteBdReportJob(dto, permission);
        });
        List<FteBdReportJobDetailVO> result = reportCompanyRepository.getFteBdReportJobDetail(dto, pageable, permission);
        try {
            final Long count = fteBdReportJobCount.get();
            headers.set("Pagination-Count", count.toString());
        } catch (InterruptedException | ExecutionException e) {
            log.error("[FteBdReportService Error]", e);
            throw new CustomParameterizedException("Internet error, try later please!");
        }
        return result;
    }

    @Override
    public List<FteBdReportOnboardDetailVO> queryFteBdReportOnboardDetail(FteBdReportDetailDTO dto, Pageable pageable, HttpHeaders headers) {
        final Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<Long> fteBdReportOnboardCount = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return reportCompanyRepository.countFteBdReportOnboard(dto);
        });
        List<FteBdReportOnboardDetailVO> result = reportCompanyRepository.getFteBdReportOnboardDetail(dto, pageable);
        try {
            final Long count = fteBdReportOnboardCount.get();
            headers.set("Pagination-Count", count.toString());
        } catch (InterruptedException | ExecutionException e) {
            log.error("[FteBdReportService Error]", e);
            throw new CustomParameterizedException("Internet error, try later please!");
        }
        result.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAm())) {
                List<UserCountryVO> userCountryList = new ArrayList<>();
                Arrays.stream(x.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserId(Long.valueOf(amCountry[0]));
                    vo.setCountryId(amCountry[1]);
                    userCountryList.add(vo);
                });
                x.setCoAmList(userCountryList);
            }
        });
        return result;
    }

    @Override
    public List<FteBdReportOnboardDetailVO> queryFteBdReportInvoiceDetail(FteBdReportDetailDTO dto, Pageable pageable, HttpHeaders headers) {
        final Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<Long> fteBdReportOnboardCount = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return reportCompanyRepository.countFteBdReportInvoice(dto);
        });
        List<FteBdReportOnboardDetailVO> result = reportCompanyRepository.getFteBdReportInvoiceDetail(dto, pageable);
        try {
            final Long count = fteBdReportOnboardCount.get();
            headers.set("Pagination-Count", count.toString());
        } catch (InterruptedException | ExecutionException e) {
            log.error("[FteBdReportService Error]", e);
            throw new CustomParameterizedException("Internet error, try later please!");
        }
        result.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAm())) {
                List<UserCountryVO> userCountryList = new ArrayList<>();
                Arrays.stream(x.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserId(Long.valueOf(amCountry[0]));
                    vo.setCountryId(amCountry[1]);
                    userCountryList.add(vo);
                });
                x.setCoAmList(userCountryList);
            }
        });
        return result;
    }

    @Override
    public List<FteBdReportOnboardDetailVO> queryFteBdReportPaymentDetail(FteBdReportDetailDTO dto, Pageable pageable, HttpHeaders headers) {
        final Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<Long> fteBdReportOnboardCount = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return reportCompanyRepository.countFteBdReportPayment(dto);
        });
        List<FteBdReportOnboardDetailVO> result = reportCompanyRepository.getFteBdReportPaymentDetail(dto, pageable);
        try {
            final Long count = fteBdReportOnboardCount.get();
            headers.set("Pagination-Count", count.toString());
        } catch (InterruptedException | ExecutionException e) {
            log.error("[FteBdReportService Error]", e);
            throw new CustomParameterizedException("Internet error, try later please!");
        }
        result.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAm())) {
                List<UserCountryVO> userCountryList = new ArrayList<>();
                Arrays.stream(x.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserId(Long.valueOf(amCountry[0]));
                    vo.setCountryId(amCountry[1]);
                    userCountryList.add(vo);
                });
                x.setCoAmList(userCountryList);
            }
        });
        return result;
    }

    private String applyDataPermission(String tableName) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();

        StringBuilder sb = new StringBuilder();
        if (teamDataPermission.getSelf()) {
            sb.append(" AND (").append(tableName).append(".puser_id = ").append(SecurityUtils.getUserId());
            List<Long> idsForPrivateJob = reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob());
            if (CollUtil.isNotEmpty(idsForPrivateJob)) {
                sb.append(" or ").append(tableName).append(".id in (").append(StringUtils.join(idsForPrivateJob, ",")).append(")");
            }
            sb.append(")");
        } else if (CollectionUtils.isNotEmpty(teamDataPermission.getReadableTeamIds())) {
            List<Long> idsForPrivateJob = reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob());
            List<Long> idsForJobInvalid = reportRepository.getJobIdsForPrivateJobInvalid(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob());
            sb.append(" AND (").append(tableName).append(".pteam_id in (").append(StringUtils.join(teamDataPermission.getReadableTeamIds(), ",")).append(")");
            if (CollUtil.isNotEmpty(idsForPrivateJob)) {
                sb.append(" or ").append(tableName).append(".id in (").append(StringUtils.join(idsForPrivateJob, ",")).append(")");
            }
            sb.append(" )");
            if (CollUtil.isNotEmpty(idsForJobInvalid)) {
                sb.append(" AND ").append(tableName).append(".id not in (").append(StringUtils.join(idsForJobInvalid, ",")).append(")");
            }
        }else if (teamDataPermission.getAll()){
            List<Long> idsForJobInvalid = reportRepository.getJobIdsForPrivateJobInvalid(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob());
            if (CollUtil.isNotEmpty(idsForJobInvalid)) {
                sb.append(" AND ").append(tableName).append(".id not in (").append(StringUtils.join(idsForJobInvalid, ",")).append(")");
            }
        }
        return sb.toString();
    }

    private boolean hasValidUserIds(Set<Long> userIds, Set<Long> teamIds) {
        PermissionTeamMemberSearchVM searchVM = new PermissionTeamMemberSearchVM();
        searchVM.setTeamIds(teamIds);
        Set<Long> userIdsByTeams = Objects.requireNonNull(userService.getPlainTeamMembersByTeamIds(searchVM).getBody())
                .stream().map(PermissionTeamMemberDTO::getId).collect(Collectors.toSet());
        userIds.addAll(userIdsByTeams);
        return CollectionUtils.isNotEmpty(userIdsByTeams);
    }
}
