package com.altomni.apn.report.repository;

import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.report.domain.LinkedinStats;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the LinkedinStats entity.
 */
@Repository
public interface LinkedinStatsRepository extends JpaRepository<LinkedinStats, Long> {

    LinkedinStats findFirstByUserIdAndLinkedinAndTypeOrderById(Long userId, String linkedin, ContactType contactType);

    @Query(value = "SELECT CONCAT_WS('', u.first_name, ' ', u.last_name) as username, COUNT(l.linkedin) as totalCnt from linkedin_stats l INNER JOIN user u ON u.id = l.user_id AND l.tenant_id = ?1 AND l.type = ?2 AND l.created_date >= ?3 AND l.created_date <= ?4 GROUP BY l.user_id", nativeQuery = true)
    List<Object[]> getReportByTypeAndCreatedDateBetween(Long tenantId, Integer type, String fromDate, String endDate);
}
