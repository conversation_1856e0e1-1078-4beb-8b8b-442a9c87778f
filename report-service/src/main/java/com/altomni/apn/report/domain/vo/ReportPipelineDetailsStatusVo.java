package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.report.config.excel.CustomJobStatusConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.time.Instant;

@Data
@Entity
@Accessors(chain = true)
public class ReportPipelineDetailsStatusVo implements AttachConfidentialTalent {

    @ExcelIgnore
    private Long id;

    @Id
    @ExcelIgnore
    private Long applicationId;

    @ExcelIgnore
    private Long companyId;

    @ExcelProperty(value = "Candidate", index = 0)
    private String talentName;

    @ExcelIgnore
    private Instant activityCreatedDate;

    @Transient
    @ExcelProperty(value = "Date of Submitted To Client", index = 2)
    private String activityCreatedDateFormat;

    @ExcelIgnore
    private Instant lastUpdatedAt;

    @Transient
    @ExcelProperty(value = "Last Updated At", index = 10)
    private String lastUpdatedAtFormat;

    @ExcelProperty(value = "Current Status", index = 3)
    private String currentStatus;

    @ExcelIgnore
    private String status;

    @ExcelProperty(value = "Company", index = 4)
    private String company;

    @ExcelProperty(value = "Job Title", index = 5)
    private String jobTitle;

    @ExcelProperty(value = "Job Id", index = 6)
    private Long jobId;

    @Convert(converter = JobStatusConverter.class)
    @ExcelProperty(value = "Job Status", index = 7, converter = CustomJobStatusConverter.class)
    private JobStatus jobStatus;

    @ExcelProperty(value = "Job Location", index = 8)
    private String jobLocation;

    @Transient
    @ExcelProperty(value = "Last Updated By", index = 9)
    private String lastUpdatedBy;

    @Transient
    @ExcelProperty(value = "Sourcer", index = 1)
    private String sourcer;

    @ExcelIgnore
    @JsonIgnore
    private String uuFirstName;

    @ExcelIgnore
    @JsonIgnore
    private String uuLastName;

    @ExcelIgnore
    @JsonIgnore
    private String suFirstName;

    @ExcelIgnore
    @JsonIgnore
    private String suLastName;

    @ExcelIgnore
    private String jobCode;

    @ExcelIgnore
    private String hiringManager;

    @ExcelIgnore
    private EliminateReason eliminateReason;

    @ExcelIgnore
    private Long pteamId;

    @ExcelIgnore
    @Transient
    private boolean isPrivateJob;

    /**
     * true: 已离职， false: 未离职
     */
    @ExcelIgnore
    private Boolean resigned;

    /**
     * true: 是 converted to FTE 流程
     * false: 非 converted to FTE 流程
     */
    @ExcelIgnore
    private Boolean convertedToFte;

    @Transient
    @ExcelIgnore
    private Boolean confidentialTalentViewAble;

    @Transient
    @ExcelIgnore
    private ConfidentialInfoDto confidentialInfo;


    @Override
    public Long getTalentId() {
        return id;
    }

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        this.confidentialInfo = confidentialInfo;
    }

    @Override
    public void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble) {
        this.confidentialTalentViewAble = confidentialTalentViewAble;
    }

    @Override
    public void encrypt() {
        this.applicationId = null;
        this.talentName = null;
        this.activityCreatedDate = null;
        this.activityCreatedDateFormat = null;
        this.lastUpdatedAt = null;
        this.lastUpdatedAtFormat = null;
        this.currentStatus = null;
        this.status = null;
        this.company = null;
        this.jobTitle = null;
        this.jobId = null;
        this.jobStatus = null;
        this.jobLocation = null;
        this.lastUpdatedBy = null;
        this.sourcer = null;
        this.companyId = null;
        this.uuFirstName = null;
        this.uuLastName = null;
        this.suFirstName = null;
        this.suLastName = null;
        this.jobCode = null;
        this.eliminateReason = null;
        this.hiringManager = null;
        this.pteamId = null;
        this.resigned = null;
        this.convertedToFte = null;

    }
}
