package com.altomni.apn.report.service.recruiting;

import com.altomni.apn.report.domain.vo.*;
import com.altomni.apn.report.dto.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface RecruitingKpiByCompanyService {

    Page<RecruitingKpiJobDetailVO> searchJobDetailPage(RecruitingKpiJobDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportJobDetailList(RecruitingKpiJobDetailSearchDto searchDto, HttpServletResponse response);

    Page<RecruitingKpiTalentDetailVO> searchTalentDetailPage(RecruitingKpiTalentDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportTalentDetailList(RecruitingKpiTalentDetailSearchDto searchDto, HttpServletResponse response);

    Page<? extends RecruitingKpiApplicationBaseDetailVO> searchApplicationDetailPage(RecruitingKpiApplicationDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportApplicationDetailList(RecruitingKpiApplicationDetailSearchDto searchDto, HttpServletResponse response);

    Page<RecruitingKpiJobNoteDetailVO> searchJobNoteDetailPage(RecruitingKpiJobNoteDetailSearchDto searchDto, Pageable pageable, boolean withCountFlag);

    void exportJobNoteDetailList(RecruitingKpiJobNoteDetailSearchDto searchDto, HttpServletResponse response);

    List<String> getKpiReportCountry();

    Page<RecruitingKpiCompanyNoteDetailVO> searchCompanyNoteDetailPage(RecruitingKpiCompanyNoteDetailSearchDto searchDto, Pageable pageable);

    void exportCompanyNoteDetailList(RecruitingKpiCompanyNoteDetailSearchDto searchDto, HttpServletResponse response);
}
