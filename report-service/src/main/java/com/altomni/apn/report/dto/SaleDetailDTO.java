package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.report.domain.enumeration.ReportSalesType;
import com.altomni.apn.report.domain.enumeration.ReportSalesTypeConverter;
import lombok.Data;

import javax.persistence.Convert;
import java.io.Serializable;
import java.util.List;

@Data
public class SaleDetailDTO implements Serializable {
    private List<Long> applicationIds;
    private List<JobType> jobType;
    @Convert(converter = ReportSalesTypeConverter.class)
    private ReportSalesType type;
    private String typeValue;
    private Integer country;
    private Long year;
    private List<Integer> years;
    private Boolean isNeedUpdateStatusFlag;
}
