package com.altomni.apn.report.domain.vo.s3;

import com.altomni.apn.common.domain.enumeration.talent.TalentNoteType;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CandidateUserDetailSearchVo implements Serializable {

    private static final long serialVersionUID = 5236236962712498048L;

    private String fromDate;

    private String toDate;

    private Long detailUserId;

    private TalentNoteType noteType;

}
