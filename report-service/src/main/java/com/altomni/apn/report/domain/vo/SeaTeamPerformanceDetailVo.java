package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;

import java.util.HashMap;
import java.util.Map;

@JsonInclude(JsonInclude.Include.NON_NULL)
public class SeaTeamPerformanceDetailVo extends HashMap<String, Object> implements AttachConfidentialTalent {

    public SeaTeamPerformanceDetailVo(Map<String, Object> map) {
       super(map);
    }

    @Override
    public Long getTalentId() {
        return get("candidateId") == null? null : Long.parseLong(get("candidateId").toString());
    }

    @Override
    public ConfidentialInfoDto getConfidentialInfo() {
        return (ConfidentialInfoDto) get("confidentialInfo");
    }

    @Override
    public void setConfidentialInfo(ConfidentialInfoDto confidentialInfo) {
        put("confidentialInfo", confidentialInfo);
    }

    @Override
    public Boolean getConfidentialTalentViewAble() {
        return (Boolean) get("confidentialTalentViewAble");
    }

    @Override
    public void setConfidentialTalentViewAble(Boolean confidentialTalentViewAble) {
        put("confidentialTalentViewAble", confidentialTalentViewAble);
    }

    @Override
    public void encrypt() {
        Long talentId = this.getTalentId();
        this.clear();
        this.put("candidateId", talentId);
        this.put("confidentialTalentViewAble", false);
    }
}
