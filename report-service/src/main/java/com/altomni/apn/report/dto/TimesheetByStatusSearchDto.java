package com.altomni.apn.report.dto;

import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentCategoryType;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentDivision;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetFrequencyType;
import com.altomni.apn.common.domain.enumeration.jobdiva.TimeSheetStatus;
import com.altomni.apn.common.dto.search.SearchSortDTO;
import com.altomni.apn.report.domain.enumeration.TimesheetSearchType;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;

@Data
public class TimesheetByStatusSearchDto implements Serializable {

    private String from;

    private String to;

    private TimesheetSearchType type;

    private Long companyId;

    private TimeSheetStatus timeSheetStatus;

    private Boolean displayHoursPerDayFlag = false;

    private Boolean chargeNumberFlag = false;

    private Boolean tvcNumberFlag = false;

    private Boolean exceptionChecklist = false;

    private List<AssignmentCategoryType> assignmentCategoryType;

    private List<TimeSheetFrequencyType> billingFrequency;

    private List<TimeSheetFrequencyType> payFrequency;

    private List<AssignmentDivision> assignmentDivision;

    private SearchSortDTO sort;

    private String timeZone;

    public String getFrom() {
        if (Objects.equals(type, TimesheetSearchType.APPROVED_DATE)) {
            return getUtcByTimeZone(from + " 00:00:00");
        }
        return from;
    }

    public String getTo() {
        if (Objects.equals(type, TimesheetSearchType.APPROVED_DATE)) {
            return getUtcByTimeZone(to + " 23:59:59");
        }
        return to;
    }

    public String getPrimitiveFrom() {
        return from;
    }



    public String getPrimitiveTo() {
        return to;
    }


    public String getUtcByTimeZone(String time) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime dateTime = LocalDateTime.parse(time, formatter);
        return dateTime.atZone(ZoneId.of(timeZone)).withZoneSameInstant(ZoneOffset.UTC).toString();
    }

}
