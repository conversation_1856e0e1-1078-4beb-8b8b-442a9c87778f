package com.altomni.apn.report.domain.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.ArrayList;
import java.util.List;

/**
 * The NodeType enumeration.
 */
public enum ReportCountType implements ConvertedEnum<Integer> {

    SUBMIT_TO_JOB(10),

    SUBMIT_TO_CLIENT(20),

    PIPELINE_UPDATE_SUBMITED(21),

    INTERVIEW(30),

    OFFER(40),

    OFFER_ACCEPT(41),

    COMMISSION(50),

    ON_BOARD(60);

    private final Integer dbValue;

    public static final List<Integer> ALL_NODE_TYPES = new ArrayList<>();

    static {
        for (ReportCountType nodeType: ReportCountType.values()) {
            ALL_NODE_TYPES.add(nodeType.toDbValue());
        }
    }

    ReportCountType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<ReportCountType, Integer> resolver =
        new ReverseEnumResolver<>(ReportCountType.class, ReportCountType::toDbValue);

    public static ReportCountType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
