package com.altomni.apn.report.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.report.domain.enumeration.CountryPackageEnum;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class RegionPackageService {

    @Value("${region:x}")
    private String regionDeliveryJson;

    private Map<Integer, CountryPackageEnum> map;

    public Map<Integer, CountryPackageEnum> getRegionMap() {

        if (map == null || map.isEmpty()){
            synchronized (this) {
                if (map == null || map.isEmpty()) {
                    map = new HashMap<>();
                    // Step 1: Parse the JSON into a Map<String, List<Integer>>
                    ObjectMapper objectMapper = new ObjectMapper();
                    Map<String, List<Integer>> regionMap = null;
                    try {
                        regionMap = objectMapper.readValue(regionDeliveryJson, Map.class);
                    } catch (JsonProcessingException e) {
                        throw new CustomParameterizedException(500, "Trans json data of region Error.", null);
                    }

                    for (Map.Entry<String, List<Integer>> entry : regionMap.entrySet()) {
                        CountryPackageEnum countryPackageEnum = CountryPackageEnum.valueOf(entry.getKey());
                        List<Integer> numbers = entry.getValue();
                        for (Integer number : numbers) {
                            map.put(number, countryPackageEnum);
                        }
                    }
                }
            }
        }
        return map;
    }



}
