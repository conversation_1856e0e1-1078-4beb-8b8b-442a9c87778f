package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.ReportJobPerformanceDetailVo;
import com.altomni.apn.report.domain.vo.ReportJobPerformanceRatioVo;
import com.altomni.apn.report.domain.vo.ReportJobPerformanceVo;
import com.altomni.apn.report.dto.ReportJobPerformanceDto;
import com.altomni.apn.report.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Repository
public class JobPerformanceRepository {


    @Resource
    private InitiationService initiationService;

    @Resource
    private ReportRepository reportRepository;

    @PersistenceContext
    EntityManager entityManager;

    public List<ReportJobPerformanceRatioVo> searchJobPerformanceRatio(ReportJobPerformanceDto dto) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT 
                    c.id AS id, 
                	c.full_business_name AS company_name,
                    COUNT(j.id) AS total_jobs, 
                    SUM(CASE WHEN j.status = 5 THEN 1 ELSE 0 END) AS fill_counts, 
                    SUM(CASE WHEN j.status = 9 THEN 1 ELSE 0 END) AS loss_counts
                	FROM 
                    job j
                RIGHT JOIN 
                    company c ON j.company_id = c.id 
                INNER JOIN recruitment_process rp on rp.id = j.recruitment_process_id     
                WHERE j.tenant_id = :tenantId and rp.job_type in (:jobType)
                 """);

        StringBuilder totalBuilder = new StringBuilder();
        totalBuilder.append("""
                SELECT  
                0 as id,
                'Overall' as company_name,
                COUNT(j.id) AS total_jobs,
                SUM(CASE WHEN j.status = 5 THEN 1 ELSE 0 END) AS fill_counts,
                SUM(CASE WHEN j.status = 9 THEN 1 ELSE 0 END) AS loss_counts  
                FROM job j
                INNER JOIN recruitment_process rp on rp.id = j.recruitment_process_id 
                WHERE j.tenant_id = :tenantId and rp.job_type in (:jobType)
                """);
        buildJobPerformanceRatio(dto, queryBuilder, teamDataPermission);
        buildJobPerformanceRatio(dto, totalBuilder, teamDataPermission);

        List<Long> privateJobIds = reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob());
        queryBuilder.append("  GROUP BY c.id ORDER BY total_jobs DESC ");

        SecurityContext context = SecurityContextHolder.getContext();
        TeamDataPermissionRespDTO finalTeamDataPermission = teamDataPermission;
        CompletableFuture<List<ReportJobPerformanceRatioVo>> totalQueryResultListFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            Query totalQuery = entityManager.createNativeQuery(totalBuilder.toString(), ReportJobPerformanceRatioVo.class)
                    .setParameter("tenantId", SecurityUtils.getTenantId())
                    .setParameter("jobType", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
            setParameter(dto, privateJobIds, finalTeamDataPermission, totalQuery);
            return totalQuery.getResultList();
        });
        CompletableFuture<List<ReportJobPerformanceRatioVo>> detailListFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            Query query = entityManager.createNativeQuery(queryBuilder.toString(), ReportJobPerformanceRatioVo.class)
                    .setParameter("tenantId", SecurityUtils.getTenantId())
                    .setParameter("jobType", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
            setParameter(dto, privateJobIds, finalTeamDataPermission, query);
            return query.getResultList();
        });
        CompletableFuture.allOf(totalQueryResultListFuture, detailListFuture)
                .exceptionally(t -> {
                    throw new ExternalServiceInterfaceException("Error occurred when fetching Job Performance Ratio");
                }).join();
        List<ReportJobPerformanceRatioVo> totalQueryResultList = totalQueryResultListFuture.join();
        List<ReportJobPerformanceRatioVo> resultList = detailListFuture.join();
        totalQueryResultList.addAll(resultList);
        return totalQueryResultList;
    }

    private void buildJobPerformanceRatio(ReportJobPerformanceDto dto, StringBuilder queryBuilder, TeamDataPermissionRespDTO teamDataPermission) {
        boolean companyCondition = CollUtil.isNotEmpty(dto.getCompanyIds());
        boolean createdDateCondition = StringUtils.isNotEmpty(dto.getFromDate()) && StringUtils.isNotEmpty(dto.getToDate());
        boolean jobTypeCondition = CollUtil.isNotEmpty(dto.getJobTypes());
        if (companyCondition) {
            queryBuilder.append("  AND j.company_id IN (:companyIds) ");
        }
        if (createdDateCondition) {
            queryBuilder.append("  AND j.created_date BETWEEN :fromDate AND :toDate ");
        }
        if (jobTypeCondition) {
            queryBuilder.append("  AND rp.job_type IN (:jobTypes) ");
        }
        if (Boolean.TRUE.equals(teamDataPermission.getSelf())) {
            queryBuilder.append("  AND (j.puser_id IN :userIds or j.id in :privateJobIds) ");
        } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
            queryBuilder.append("  AND (j.pteam_id IN :teamIds or j.id in :privateJobIds) ");
        } else {
            queryBuilder.append("  AND (j.pteam_id != :teamIdForPrivateJob or j.id in :privateJobIds) ");
        }
    }

    private void setParameter(ReportJobPerformanceDto dto, List<Long> privateJobIds, TeamDataPermissionRespDTO teamDataPermission, Query query) {
        boolean companyCondition = CollUtil.isNotEmpty(dto.getCompanyIds());
        boolean createdDateCondition = StringUtils.isNotEmpty(dto.getFromDate()) && StringUtils.isNotEmpty(dto.getToDate());
        boolean jobTypeCondition = CollUtil.isNotEmpty(dto.getJobTypes());
        if (companyCondition) {
            query.setParameter("companyIds", dto.getCompanyIds());
        }
        if (createdDateCondition) {
            query.setParameter("fromDate", dto.getFromDate());
            query.setParameter("toDate", dto.getToDate());
        }
        if (jobTypeCondition) {
            final List<Integer> jobTypes = dto.getJobTypes().stream().map(JobType::toDbValue).toList();
            query.setParameter("jobTypes", jobTypes);
        }
        if (Boolean.TRUE.equals(teamDataPermission.getSelf())) {
            query.setParameter("userIds", CollUtil.newArrayList(SecurityUtils.getUserId()));
        } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
            query.setParameter("teamIds", teamDataPermission.getNestedTeamIds());
        } else {
            query.setParameter("teamIdForPrivateJob", teamDataPermission.getTeamIdForPrivateJob());
        }
        query.setParameter("privateJobIds", privateJobIds);
    }

    public ReportJobPerformanceVo searchJobPerformanceDetail(ReportJobPerformanceDto dto, Pageable pageable) {
        Set<Long> userIds = new HashSet<>();
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append("""
                SELECT
                	j.id,
                	j.company_id,
                	c.`full_business_name` AS 'company_name',
                	j.title,
                	j.currency,
                	JSON_EXTRACT( jai.extended_info, '$.billRange' ) AS 'bill_rate',
                	JSON_EXTRACT( jai.extended_info,'$.payType' ) AS 'pay_type',
                	j.created_date,
                	count(trp.talent_id) as submit_job_count,
                	j.`status`,
                	j.last_non_open_time
                FROM
                	job j
                	INNER JOIN company c ON j.company_id = c.id
                	LEFT JOIN job_additional_info jai ON j.additional_info_id = jai.id  
                	LEFT JOIN talent_recruitment_process trp on trp.job_id = j.id  
                	LEFT JOIN talent_recruitment_process_node trpn on trpn.talent_recruitment_process_id = trp.id and  trpn.node_status in (1) and trpn.node_type = 10
                    INNER JOIN recruitment_process rp on rp.id = j.recruitment_process_id 
                	WHERE j.tenant_id = :tenantId and rp.job_type in (:jobType)
                	""");

        StringBuilder countBuilder = new StringBuilder();

        countBuilder.append("""
                    SELECT COUNT(1) FROM job j
                    INNER JOIN recruitment_process rp on rp.id = j.recruitment_process_id
                    where j.tenant_id = :tenantId and rp.job_type in (:jobType)
                """);

        buildJobPerformanceRatio(dto, queryBuilder, teamDataPermission);
        buildJobPerformanceRatio(dto, countBuilder, teamDataPermission);

        List<Long> privateJobIds = reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob());
        String sort = constructSort(pageable);
        queryBuilder.append("  GROUP BY j.id ORDER BY ").append(sort).append(" LIMIT :offset,:pageSize");

        SecurityContext context = SecurityContextHolder.getContext();
        TeamDataPermissionRespDTO finalTeamDataPermission = teamDataPermission;
        CompletableFuture<BigInteger> totalFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            Query count = entityManager.createNativeQuery(countBuilder.toString())
                    .setParameter("tenantId", SecurityUtils.getTenantId())
                    .setParameter("jobType", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
            setParameter(dto, privateJobIds, finalTeamDataPermission, count);
            return (BigInteger) count.getSingleResult();
        });
        CompletableFuture<List<ReportJobPerformanceDetailVo>> detailListFuture = CompletableFuture.supplyAsync(() ->{
            SecurityContextHolder.setContext(context);
            Query query = entityManager.createNativeQuery(queryBuilder.toString(), ReportJobPerformanceDetailVo.class)
                    .setParameter("tenantId", SecurityUtils.getTenantId())
                    .setParameter("offset", pageable.getOffset())
                    .setParameter("pageSize", pageable.getPageSize())
                    .setParameter("jobType", Arrays.asList(JobType.CONTRACT.toDbValue(), JobType.FULL_TIME.toDbValue(), JobType.MSP.toDbValue()));
            setParameter(dto, privateJobIds, finalTeamDataPermission, query);
            return query.getResultList();
        } );
        CompletableFuture.allOf(totalFuture, detailListFuture)
                .exceptionally(t -> {
                    throw new ExternalServiceInterfaceException("Error occurred when fetching Job Performance Ratio");
                }).join();
        BigInteger total = totalFuture.join();
        List<ReportJobPerformanceDetailVo> detailList = detailListFuture.join();
        if (CollUtil.isNotEmpty(detailList)) {
            detailList.forEach(detail -> detail.setIsPrivateJob(privateJobIds.contains(detail.getId())));
        }
        ReportJobPerformanceVo result = new ReportJobPerformanceVo();
        result.setTotal(total.longValue());
        result.setDetailList(detailList);
        return result;
    }

    private String constructSort(Pageable pageable) {
        Sort.Order order = pageable.getSort().get().findFirst().orElse(null);
        if (order == null) {
            return "submit_job_count desc";
        }
        if ("status".equals(order.getProperty())){
            if (order.isAscending()){
                return "CASE j.`status` WHEN 0 THEN 0 WHEN 5 THEN 1 WHEN 2 THEN 2 WHEN 9 THEN 3 WHEN 4 THEN 4 ELSE 999 END ";
            }else{
                return "CASE j.`status` WHEN 0 THEN 4 WHEN 5 THEN 3 WHEN 2 THEN 2 WHEN 9 THEN 1 WHEN 4 THEN 0 ELSE 999 END ";
            }
        }
        // 当传时间的时候，为null的时间永远放在最下面
        if ("lastNonOpenTime".equals(order.getProperty())){
            return " last_non_open_time IS NULL,"+StrUtil.toUnderlineCase(order.getProperty()) + StrUtil.SPACE + order.getDirection().name();
        }
        // 将驼峰转为小写下划线字符串返回
        return StrUtil.toUnderlineCase(order.getProperty()) + StrUtil.SPACE + order.getDirection().name();
    }


}
