package com.altomni.apn.report.dto;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.dto.recruiting.RecruitingKpiDetailBaseDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiJobDetailSearchDto extends RecruitingKpiDetailBaseDto {

    private JobDetailSearchDto detail;

    public String getOrderBySql() {
        if (ObjectUtil.isEmpty(getSort())) {
            return "";
        }
        return switch (getSort().getProperty()) {
            case "postingDate":
                yield " order by j.posting_time " + getSort().getDirection();
            case "openDate":
                yield " order by j.open_time " + getSort().getDirection();
            case "tenantWebsitePostingDate":
                yield " order by website.last_modified_date " + getSort().getDirection();
            case "jobStatus":
                yield " order by j.status " + getSort().getDirection();
            case "recruitmentProcessId":
                yield " order by CONVERT( rp.name USING gbk) " + getSort().getDirection();
            case "minimumPayRate":
                yield " order by IF(minimum_pay_rate IS NULL, 1, 0), minimum_pay_rate " + getSort().getDirection();
            case "maximumPayRate":
                yield " order by IF(maximum_pay_rate IS NULL, 1, 0), maximum_pay_rate " + getSort().getDirection();
            case "minimumBillRate":
                yield " order by IF(minimum_bill_rate IS NULL, 1, 0), minimum_bill_rate " + getSort().getDirection();
            case "maximumBillRate":
                yield " order by IF(maximum_bill_rate IS NULL, 1, 0), maximum_bill_rate " + getSort().getDirection();
            case "startDate":
                yield " order by j.start_date " + getSort().getDirection();
            case "endDate":
                yield " order by j.end_date " + getSort().getDirection();
            case "contractDuration":
                yield " order by j.contract_duration " + getSort().getDirection();
            case "jobLocation":
                yield " order by CONVERT( GROUP_CONCAT(distinct jl.format_location SEPARATOR \";\") USING gbk) " + getSort().getDirection();
            case "submitToClientNum":
                yield " order by submit_to_client_num " + getSort().getDirection();
            case "firstInterviewNum":
                yield " order by first_interview_num " + getSort().getDirection();
            case "secondInterviewNum":
                yield " order by second_interview_num " + getSort().getDirection();
            case "finalInterviewNum":
                yield " order by final_interview_num " + getSort().getDirection();
            case "interviewNum":
                yield " order by interview_num " + getSort().getDirection();
            case "offerNum":
                yield " order by offer_num " + getSort().getDirection();
            case "onboardNum":
                yield " order by onboard_num " + getSort().getDirection();
            default: yield " ";
        };
    }

    public void appendDetailCondition(StringBuilder sb, Map<String, Object> whereParamMap) {
        if (detail == null) {
            return;
        }
        if (ObjectUtil.isNotEmpty(detail.getJobId())) {
            sb.append(" and j.id = :jobId ");
            whereParamMap.put("jobId", detail.getJobId());
        }
        if (ObjectUtil.isNotEmpty(detail.getCompanyId())) {
            sb.append(" and c.id = :companyId ");
            whereParamMap.put("companyId", detail.getCompanyId());
        }
        if (StrUtil.isNotBlank(detail.getJobTitle())) {
            sb.append(" and j.title like :jobTitle ");
            whereParamMap.put("jobTitle", "%" + detail.getJobTitle() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getJobCode())) {
            sb.append(" and j.code like :jobCode ");
            whereParamMap.put("jobCode", "%" + detail.getJobCode() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getJobStatus())) {
            sb.append(" and j.status = :jobStatus");
            whereParamMap.put("jobStatus", detail.getJobStatus().toDbValue());
        }
        if (ObjectUtil.isNotEmpty(detail.getRecruitmentProcessId())) {
            sb.append(" and rp.id = :recruitmentProcessId ");
            whereParamMap.put("recruitmentProcessId", detail.getRecruitmentProcessId());
        }
        if (StrUtil.isNotBlank(detail.getClientContact())) {
            sb.append(" and tcslcc.full_name like :clientContact ");
            whereParamMap.put("clientContact", "%" + detail.getClientContact() + "%");
        }
        if (ObjectUtil.isNotEmpty(detail.getCreatedBy())) {
            sb.append(" and j.puser_id = :createdBy");
            whereParamMap.put("createdBy", detail.getCreatedBy());
        }
        if (ObjectUtil.isNotEmpty(detail.getAssignedUser())) {
            sb.append(" and ujr.user_id = :assignedUser ");
            whereParamMap.put("assignedUser", detail.getAssignedUser());
        }
        if (ObjectUtil.isNotEmpty(detail.getFlexibleLocation())) {
            sb.append(" and j.flexible_location = :flexibleLocation ");
            whereParamMap.put("flexibleLocation", detail.getFlexibleLocation());
        }
        if (StrUtil.isNotBlank(detail.getJobLocation())) {
            sb.append(" and jl.format_location like :location ");
            whereParamMap.put("location", "%" + detail.getJobLocation() + "%");
        }
        if (StrUtil.isNotBlank(detail.getSkills())) {
            sb.append(" and JSON_SEARCH(LOWER(JSON_UNQUOTE(JSON_EXTRACT(jai.extended_info, '$.requiredSkills[*].skillName'))), 'one', :skills) IS NOT NULL ");
            whereParamMap.put("skills", "%" + detail.getSkills() + "%");
        }
    }

}
