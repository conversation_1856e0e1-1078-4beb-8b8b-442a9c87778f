package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RecruitingKpiTalentDetailENVO implements Serializable {

    @ExcelIgnore
    private Long talentId;

    @ExcelProperty(value = "Notes", index = 0)
    private Long talentNotes;

    @ExcelProperty(value = "Candidate Name", index = 1)
    private String fullName;

    @ExcelProperty(value = "Current Company", index = 2)
    private String currentCompany;

    @ExcelProperty(value = "Current Position", index = 3)
    private String currentPosition;

    @ExcelIgnore
    private Integer jobSearchStatus;

    @ExcelProperty(value = "Job Search Status", index = 4)
    private String jobSearchStatusDisplay;

    @ExcelProperty(value = "Created by", index = 5)
    private String createdBy;

    @ExcelIgnore
    private Instant createdDate;

    @ExcelProperty(value = "Created at", index = 6)
    private String createdDateFormat;


    @ExcelProperty(value = "Last Updated by", index = 7)
    private String lastModifiedBy;

    @ExcelIgnore
    private Instant lastModifiedDate;

    @ExcelProperty(value = "Last Modified at", index = 8)
    private String lastModifiedDateFormat;

}