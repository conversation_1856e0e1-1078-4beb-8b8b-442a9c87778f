package com.altomni.apn.report.web.rest;

import com.altomni.apn.common.aop.confidential.ProcessConfidentialTalent;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.vo.s3.CandidateUserCountSearchVo;
import com.altomni.apn.report.domain.vo.s3.CandidateUserDetailSearchVo;
import com.altomni.apn.report.dto.s3.*;
import com.altomni.apn.report.service.ReportCandidateUserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/v3")
public class ReportUserCandidateResource {

    private final Logger log = LoggerFactory.getLogger(ReportUserCandidateResource.class);

    @Resource
    private ReportCandidateUserService reportCandidateUserService;

    /**
     * 根据查询条件,统计用户创建候选人的个数、修改候选人次数、电话候选人次数（来源Note数据）、发邮件候选人次数（来源Note数据）、面试候选人次数（来源Note数据）
     *
     * @param searchVo
     * @return {@link ResponseEntity}<{@link CandidateUserDetailSearchVo}>
     */
    @PostMapping("/report/talents/countUserCandidates")
    @NoRepeatSubmit
    public ResponseEntity<List<UserCandidateCountDTO>> countCandidateReport(@RequestBody CandidateUserCountSearchVo searchVo) throws IOException {
        log.info("[APN: ReportUserTalent @{}] REST request to count candidates by userIds: {}", SecurityUtils.getUserId(), searchVo);
        return reportCandidateUserService.countCandidateReport(searchVo);
    }

    @PostMapping("/export/talents/countUserCandidates")
    @NoRepeatSubmit
    public void exportCountCandidateReport(@RequestBody CandidateUserCountSearchVo searchVo, HttpServletResponse response)  {
        log.info("[APN: ReportUserTalent @{}] REST request to export count candidates by userIds: {}", SecurityUtils.getUserId(), searchVo);
        reportCandidateUserService.exportCountCandidateReport(searchVo,response);
    }

    /**
     * 根据查询条件，查询某一位用户创建候选人的详情信息
     *
     * @param searchVo
     * @return {@link ResponseEntity}<{@link CandidateUserDetailSearchVo}>
     */
    @PostMapping("/report/talents/detailUserCandidatesCreation")
    @NoRepeatSubmit
    @ProcessConfidentialTalent
    public ResponseEntity<List<UserCandidateDetailDTO>> detailUserCandidatesCreation(@RequestBody CandidateUserDetailSearchVo searchVo, @PageableDefault Pageable pageable) {
        log.info("[APN: ReportUserTalent @{}] REST request to query creation information the user has created multiple candidates: {}", SecurityUtils.getUserId(), searchVo);
        return reportCandidateUserService.detailUserCandidatesCreation(searchVo, pageable);
    }

    /**
     * 根据查询条件，查询某一位用户修改候选人的详情信息
     *
     * @param searchVo
     * @param pageable
     * @return {@link ResponseEntity}<{@link List}<{@link UserCandidateModificationDetailDTO}>>
     */
    @PostMapping("/report/talents/detailUserCandidatesModification")
    @NoRepeatSubmit
    @ProcessConfidentialTalent
    public ResponseEntity<List<UserCandidateModificationDetailDTO>> detailUserCandidatesModification(@RequestBody CandidateUserDetailSearchVo searchVo, @PageableDefault Pageable pageable) {
        log.info("[APN: ReportUserTalent @{}] REST request to query modification information, the user has modified multiple candidates: {}", SecurityUtils.getUserId(), searchVo);
        return reportCandidateUserService.detailUserCandidatesModification(searchVo, pageable);
    }

    /**
     * 根据查询条件，查询某一位用户创建、修改候选人的note详情信息
     *
     * @param searchVo
     * @param pageable
     * @return {@link ResponseEntity}<{@link List}<{@link UserCandidateModificationDetailDTO}>>
     */
    @PostMapping("/report/talents/detailUserCandidatesNote")
    @NoRepeatSubmit
    @ProcessConfidentialTalent
    public ResponseEntity<List<UserTalentNoteDetailDTO>> detailUserCandidatesNote(@RequestBody CandidateUserDetailSearchVo searchVo, @PageableDefault Pageable pageable) {
        log.info("[APN: ReportUserTalent @{}] REST request to query  note information  the user has modified multiple candidates: {}", SecurityUtils.getUserId(), searchVo);
        return reportCandidateUserService.detailUserCandidatesNote(searchVo, pageable);
    }


}