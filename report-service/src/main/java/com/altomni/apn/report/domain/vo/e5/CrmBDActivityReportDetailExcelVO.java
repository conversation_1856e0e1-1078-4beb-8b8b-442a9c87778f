package com.altomni.apn.report.domain.vo.e5;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CrmBDActivityReportDetailExcelVO implements Serializable {

    @ExcelProperty(value = "Create By", index = 0)
    private String createdByName;

    /**
     * 公司名称
     */
    @ExcelProperty(value = "Company Name", index = 1)
    private String companyName;

    /**
     * 阶段
     */
    @ExcelProperty(value = "Stage", index = 2)
    private String stage;

    /**
     * 客户联系人
     */
//    private List<String> accountContactId;
//
//    private Map<Long,String> accountContacts;
//
//    private Map<Long,String> leadContacts;

    @ExcelProperty(value = "Client Contact", index = 3)
    private String clientContact;


    /**
     * 联系时间
     */
    @ExcelProperty(value = "Contact Time", index = 4)
    private String contactTime;

    /**
     * 备注
     */
    @ExcelProperty(value = "Note", index = 5)
    private String note;


    /**
     * 服务类型
     */
//    private List<Integer> serviceTypeIds;

    @ExcelProperty(value = "Service Type", index = 6)
    private String serviceType;

    /**
     * 销售线索负责人
     */
//    private List<Long> salesLeadOwnerIds;

    @ExcelProperty(value = "Sales Lead Owner", index = 7)
    private String salesLeadOwners;

    /**
     * BD所有者
     */
//    private List<Long> bdOwnerIds;

    @ExcelProperty(value = "BD Owner", index = 8)
    private String bdOwners;

}
