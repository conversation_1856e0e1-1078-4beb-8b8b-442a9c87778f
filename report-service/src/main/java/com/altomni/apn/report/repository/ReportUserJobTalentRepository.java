package com.altomni.apn.report.repository;

import com.altomni.apn.report.domain.ReportUserJobTalentRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the ReportUserJobTalent entity.
 * <AUTHOR>
 */
@Repository
public interface ReportUserJobTalentRepository extends JpaRepository<ReportUserJobTalentRecord, Long> {

    List<ReportUserJobTalentRecord> findAllByBatchId(String batchId);

    @Query(value = "delete from report_user_job_talent where batch_id = ?1", nativeQuery = true)
    void deleteByBatchId(String batchId);

}
