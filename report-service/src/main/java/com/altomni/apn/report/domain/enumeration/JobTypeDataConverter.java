package com.altomni.apn.report.domain.enumeration;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.altomni.apn.common.domain.enumeration.job.JobType;

public class JobTypeDataConverter implements Converter<JobType> {

    @Override
    public Class supportJavaTypeKey() {
        return JobType.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }



    @Override
    public WriteCellData<String> convertToExcelData(JobType jobType, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (jobType == null) {
            return new WriteCellData<>();
        }
        return new WriteCellData<>(jobType.getName());
    }

}