package com.altomni.apn.report.domain.enumeration;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.altomni.apn.common.domain.enumeration.jobdiva.AssignmentCategoryType;

public class AssignmentCategoryTypeDataConverter implements Converter<AssignmentCategoryType> {

    @Override
    public Class supportJavaTypeKey() {
        return AssignmentCategoryType.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }



    @Override
    public WriteCellData<String> convertToExcelData(AssignmentCategoryType categoryType, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        if (categoryType == null) {
            return new WriteCellData<>();
        }
        return new WriteCellData<>(categoryType.getDescription());
    }

}
