package com.altomni.apn.report.service.email;

import com.altomni.apn.common.dto.email.EmailLogOverviewDTO;
import com.altomni.apn.common.dto.email.MailSearchDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(value = "common-service")
public interface EmailService {

    @PostMapping("/common/api/v3/mail/search-sent-count-replied")
    ResponseEntity<List<EmailLogOverviewDTO>> searchSentEmailsAndCountReplied(@RequestBody MailSearchDTO mailSearchDTO);

    @PostMapping("/common/api/v3/mail/search-sent/by-users")
    ResponseEntity<List<EmailLogOverviewDTO>> searchSentEmailsByUsers(@RequestBody MailSearchDTO mailSearchDTO);

}
