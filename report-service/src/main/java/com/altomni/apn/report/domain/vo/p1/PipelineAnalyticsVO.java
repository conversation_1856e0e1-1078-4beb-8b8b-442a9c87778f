package com.altomni.apn.report.domain.vo.p1;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.report.domain.vm.p1.ReportCountVM;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;

@Data
@Entity
public class PipelineAnalyticsVO {

    @ExcelIgnore
    @Id
    private Long userId;

    @ExcelProperty(value = "User Name", index = 0)
    private String userName;

    @ExcelProperty(value = "Sum of Submitted to Job", index = 1)
    private Integer appliedCount;

    @ExcelIgnore
    private String appliedActivityId;

    @ExcelProperty(value = "Submitted to Client", index = 2)
    private Integer submittedCount;

    @ExcelIgnore
    private String submittedActivityId;

    @ExcelProperty(value = "Sum of Submitted to Client", index = 3)
    private Integer pipelineUpdateSubmittedCount;

    @ExcelIgnore
    private String pipelineUpdateSubmittedActivityId;

    @ExcelProperty(value = "Sum of Interview", index = 4)
    private Integer interviewCount;

    @ExcelIgnore
    private String interviewActivityId;

    @ExcelProperty(value = "Sum of Offered", index = 5)
    private Integer offeredCount;

    @ExcelIgnore
    private String offeredActivityId;

    @ExcelProperty(value = "Sum of Offer Accepted", index = 6)
    private Integer offerAcceptedCount;

    @ExcelIgnore
    private String offerAcceptedActivityId;

    @ExcelProperty(value = "Sum of On boarded", index = 7)
    private Integer startedCount;

    @ExcelIgnore
    private String startedActivityId;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Integer getAppliedCount() {
        return appliedCount;
    }

    public void setAppliedCount(Integer appliedCount) {
        this.appliedCount = appliedCount;
    }

    public String getAppliedActivityId() {
        return appliedActivityId;
    }

    public void setAppliedActivityId(String appliedActivityId) {
        this.appliedActivityId = appliedActivityId;
    }

    public Integer getSubmittedCount() {
        return submittedCount;
    }

    public void setSubmittedCount(Integer submittedCount) {
        this.submittedCount = submittedCount;
    }

    public String getSubmittedActivityId() {
        return submittedActivityId;
    }

    public void setSubmittedActivityId(String submittedActivityId) {
        this.submittedActivityId = submittedActivityId;
    }

    public Integer getPipelineUpdateSubmittedCount() {
        return pipelineUpdateSubmittedCount;
    }

    public void setPipelineUpdateSubmittedCount(Integer pipelineUpdateSubmittedCount) {
        this.pipelineUpdateSubmittedCount = pipelineUpdateSubmittedCount;
    }

    public String getPipelineUpdateSubmittedActivityId() {
        return pipelineUpdateSubmittedActivityId;
    }

    public void setPipelineUpdateSubmittedActivityId(String pipelineUpdateSubmittedActivityId) {
        this.pipelineUpdateSubmittedActivityId = pipelineUpdateSubmittedActivityId;
    }

    public Integer getInterviewCount() {
        return interviewCount;
    }

    public void setInterviewCount(Integer interviewCount) {
        this.interviewCount = interviewCount;
    }

    public String getInterviewActivityId() {
        return interviewActivityId;
    }

    public void setInterviewActivityId(String interviewActivityId) {
        this.interviewActivityId = interviewActivityId;
    }

    public Integer getOfferedCount() {
        return offeredCount;
    }

    public void setOfferedCount(Integer offeredCount) {
        this.offeredCount = offeredCount;
    }

    public String getOfferedActivityId() {
        return offeredActivityId;
    }

    public void setOfferedActivityId(String offeredActivityId) {
        this.offeredActivityId = offeredActivityId;
    }

    public Integer getOfferAcceptedCount() {
        return offerAcceptedCount;
    }

    public void setOfferAcceptedCount(Integer offerAcceptedCount) {
        this.offerAcceptedCount = offerAcceptedCount;
    }

    public String getOfferAcceptedActivityId() {
        return offerAcceptedActivityId;
    }

    public void setOfferAcceptedActivityId(String offerAcceptedActivityId) {
        this.offerAcceptedActivityId = offerAcceptedActivityId;
    }

    public Integer getStartedCount() {
        return startedCount;
    }

    public void setStartedCount(Integer startedCount) {
        this.startedCount = startedCount;
    }

    public String getStartedActivityId() {
        return startedActivityId;
    }

    public void setStartedActivityId(String startedActivityId) {
        this.startedActivityId = startedActivityId;
    }

    public static PipelineAnalyticsVO fromReportCountVM(ReportCountVM reportCountVM) {
        PipelineAnalyticsVO pipelineAnalyticsVO = new PipelineAnalyticsVO();
        switch (reportCountVM.getType()) {
            case SUBMIT_TO_JOB:
                pipelineAnalyticsVO.setAppliedCount(reportCountVM.getCount());
                pipelineAnalyticsVO.setAppliedActivityId(reportCountVM.getActivityIds());
                break;
            case SUBMIT_TO_CLIENT:
                pipelineAnalyticsVO.setSubmittedCount(reportCountVM.getCount());
                pipelineAnalyticsVO.setSubmittedActivityId(reportCountVM.getActivityIds());
                break;
            case INTERVIEW:
                pipelineAnalyticsVO.setInterviewCount(reportCountVM.getCount());
                pipelineAnalyticsVO.setInterviewActivityId(reportCountVM.getActivityIds());
                break;
            case PIPELINE_UPDATE_SUBMITED:
                pipelineAnalyticsVO.setPipelineUpdateSubmittedCount(reportCountVM.getCount());
                pipelineAnalyticsVO.setPipelineUpdateSubmittedActivityId(reportCountVM.getActivityIds());
                break;
            case OFFER:
                pipelineAnalyticsVO.setOfferedCount(reportCountVM.getCount());
                pipelineAnalyticsVO.setOfferedActivityId(reportCountVM.getActivityIds());
                break;
            case OFFER_ACCEPT:
                pipelineAnalyticsVO.setOfferAcceptedCount(reportCountVM.getCount());
                pipelineAnalyticsVO.setOfferAcceptedActivityId(reportCountVM.getActivityIds());
                break;
            case ON_BOARD:
                pipelineAnalyticsVO.setStartedCount(reportCountVM.getCount());
                pipelineAnalyticsVO.setStartedActivityId(reportCountVM.getActivityIds());
                break;
            default: break;
        }

        pipelineAnalyticsVO.setUserId(reportCountVM.getUserId());
        return pipelineAnalyticsVO;
    }

    public PipelineAnalyticsVO setCountAndIds(ReportCountVM reportCountVM) {
        switch (reportCountVM.getType()) {
            case SUBMIT_TO_JOB:
                this.setAppliedCount(reportCountVM.getCount());
                this.setAppliedActivityId(reportCountVM.getActivityIds());
                break;
            case SUBMIT_TO_CLIENT:
                this.setSubmittedCount(reportCountVM.getCount());
                this.setSubmittedActivityId(reportCountVM.getActivityIds());
                break;
            case INTERVIEW:
                this.setInterviewCount(reportCountVM.getCount());
                this.setInterviewActivityId(reportCountVM.getActivityIds());
                break;
            case PIPELINE_UPDATE_SUBMITED:
                this.setPipelineUpdateSubmittedCount(reportCountVM.getCount());
                this.setPipelineUpdateSubmittedActivityId(reportCountVM.getActivityIds());
                break;
            case OFFER:
                this.setOfferedCount(reportCountVM.getCount());
                this.setOfferedActivityId(reportCountVM.getActivityIds());
                break;
            case OFFER_ACCEPT:
                this.setOfferAcceptedCount(reportCountVM.getCount());
                this.setOfferAcceptedActivityId(reportCountVM.getActivityIds());
                break;
            case ON_BOARD:
                this.setStartedCount(reportCountVM.getCount());
                this.setStartedActivityId(reportCountVM.getActivityIds());
                break;
            default: break;
        }
        return this;
    }

    public PipelineAnalyticsVO() {
        this.appliedCount = 0;
        this.submittedCount = 0;
        this.pipelineUpdateSubmittedCount = 0;
        this.interviewCount = 0;
        this.offeredCount = 0;
        this.offerAcceptedCount = 0;
        this.startedCount = 0;
    }
}
