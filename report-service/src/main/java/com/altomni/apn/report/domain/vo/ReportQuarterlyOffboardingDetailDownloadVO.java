package com.altomni.apn.report.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigInteger;

@Data
@Accessors(chain = true)
public class ReportQuarterlyOffboardingDetailDownloadVO {

    @ExcelIgnore
    private String id;

    private String employeeType;

    private Integer number;

    private String recruiter;

    private String clientAccount;

    private String candidateName;

    private String endDay;

    private String weekDay;

    private String hourlyGp;

    private String totalGp;

    private String totalRevenue;

    public void maskConfidentialTalentData() {
        String mask = "***";
        this.employeeType = mask;
        this.recruiter = mask;
        this.clientAccount = mask;
        this.candidateName = mask;
        this.endDay = mask;
        this.weekDay = mask;
        this.hourlyGp = mask;
        this.totalGp = mask;
        this.totalRevenue = mask;
    }

}
