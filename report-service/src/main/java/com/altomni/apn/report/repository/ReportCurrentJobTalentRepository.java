package com.altomni.apn.report.repository;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.report.domain.ReportUserJobTalentDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Repository
public class ReportCurrentJobTalentRepository {

    @PersistenceContext
    EntityManager entityManager;

    @Resource
    private InitiationService initiationService;

    @Resource
    private ReportRepository reportRepository;



    public List<ReportUserJobTalentDTO> findByCurrentUser() {
        Long tenantId = SecurityUtils.getTenantId();
        StringBuilder jobConditionSqlSb = new StringBuilder();
        StringBuilder conditionSqlSb = new StringBuilder();
        this.applyDataPermission(jobConditionSqlSb, conditionSqlSb);

        String query = "SELECT u.id                            AS 'user_id', " +
                "       CONCAT(u.first_name, ' ', u.last_name) AS 'user_name', " +
                "       (SELECT COUNT(DISTINCT a.talent_id) " +
                "        FROM talent_recruitment_process a " +
                "                 LEFT JOIN talent_recruitment_process_kpi_user ac ON a.id = ac.talent_recruitment_process_id " +
                "        WHERE a.tenant_id = " + tenantId +
                "          AND ac.user_id = u.id)  AS 'talent_count', " +
                "       (SELECT GROUP_CONCAT(DISTINCT a.talent_id) " +
                "        FROM talent_recruitment_process a " +
                "                 LEFT JOIN talent_recruitment_process_kpi_user ac ON a.id = ac.talent_recruitment_process_id " +
                "        WHERE a.tenant_id = " + tenantId +
                "          AND ac.user_id = u.id)    AS 'talent_ids', " +
                "       (SELECT COUNT(DISTINCT j.id) " +
                "        FROM job j " +
                "                 INNER JOIN business_flow_administrator ujr ON ujr.company_id = j.company_id AND ujr.sales_lead_role = 0 AND j.tenant_id = " + tenantId +
                "            AND ujr.user_id = u.id " +
                "        WHERE j.status IN (0, 1, 2, 5) " + jobConditionSqlSb + ") AS 'job_count', " +
                "       (SELECT GROUP_CONCAT(DISTINCT j.id) END " +
                "        FROM business_flow_administrator ujr " +
                "                 INNER JOIN job j ON ujr.company_id = j.company_id AND j.tenant_id = " + tenantId +
                "        WHERE ujr.sales_lead_role = 0 " +
                "            AND ujr.user_id = u.id " +
                "          AND j.status IN (0, 1, 2, 5) " + jobConditionSqlSb + ")  AS 'job_ids' " +
                " FROM user u " +
                "Where u.tenant_id = " + tenantId;
        List result = entityManager.createNativeQuery(query + conditionSqlSb, ReportUserJobTalentDTO.class)
                .getResultList();
        return result;
    }

    public void applyDataPermission(StringBuilder jobConditionSqlSb, StringBuilder conditionSqlSb){
        if (!SecurityUtils.isAdmin()) {
            Long userId = SecurityUtils.getUserId();
            TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
            log.info("S1 report: teamDataPermission={}", teamDataPermission);
            if (teamDataPermission.getSelf()) {
                jobConditionSqlSb.append(" and j.puser_id = ").append(userId);
                conditionSqlSb.append(" and u.id = ").append(userId);
            } else if (CollUtil.isNotEmpty(teamDataPermission.getNestedTeamIds())) {
                String teamIds = teamDataPermission.getNestedTeamIds().stream().map(String::valueOf).collect(Collectors.joining(","));
                conditionSqlSb.append(" and exists ( select 1 from permission_user_team put where put.user_id = u.id and put.team_id in ")
                        .append("(").append(teamIds).append(")").append(")");

                if (teamDataPermission.isPrivateJobPermission() && teamDataPermission.getTeamIdForPrivateJob() != null){
                    List<Long> jobIds = reportRepository.getJobIdsForPrivateJob(SecurityUtils.getUserId(), teamDataPermission.getTeamIdForPrivateJob());
                    String privateJobIds = Optional.ofNullable(jobIds)
                            .filter(ids -> !ids.isEmpty())
                            .map(ids -> ids.stream()
                                    .map(String::valueOf)
                                    .collect(Collectors.joining(",")))
                            .orElse(null);
                    jobConditionSqlSb.append(" and (j.pteam_id in ").append("(").append(teamIds).append(") ")
                            .append(" or j.id in (").append(privateJobIds).append(")")
                            .append(" or j.puser_id = ").append(SecurityUtils.getUserId())
                            .append(")");
                }else {
                    jobConditionSqlSb.append(" and j.pteam_id in ").append("(").append(teamIds).append(") ");
                }
            }else if (!teamDataPermission.isPrivateJobPermission()){
                jobConditionSqlSb.append(" and j.pteam_id != ").append(teamDataPermission.getTeamIdForPrivateJob());
            }
        }
    }


}
