package com.altomni.apn.report.config.excel;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.altomni.apn.common.domain.enumeration.job.JobType;

public class CustomJobTypeConverter implements Converter<JobType> {
    @Override
    public Class supportJavaTypeKey() {
        return JobType.class;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return CellDataTypeEnum.STRING;
    }



    @Override
    public WriteCellData<String> convertToExcelData(JobType value, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return new WriteCellData<>(value.name());
    }
}
