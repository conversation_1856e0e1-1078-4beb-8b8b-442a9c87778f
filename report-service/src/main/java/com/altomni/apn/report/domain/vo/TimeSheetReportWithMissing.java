package com.altomni.apn.report.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.report.domain.enumeration.AssignmentDivisionDataConverter;
import com.altomni.apn.report.domain.enumeration.LocalDateDataConverter;
import com.altomni.apn.report.domain.enumeration.TimeSheetFrequencyTypeDataConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

@Data
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TimeSheetReportWithMissing {

    @Id
    @ExcelIgnore
    private String id;

    @ExcelIgnore
    private Long assignmentId;

    @ExcelProperty(value = "Employee Name", index = 0)
    private String fullName;

    @ExcelIgnore
    private String regularHours;

    @ExcelIgnore
    private String overTime;

    @ExcelIgnore
    private String doubleTime;

    @ExcelIgnore
    private String totalHours;

    @ExcelIgnore
    private String holidayHours;

    @ExcelIgnore
    private LocalDate workDate;

    @ExcelIgnore
    private LocalDate weekEndingDate;

    @ExcelProperty(value = "Week Ending", index = 12, converter = LocalDateDataConverter.class)
    private LocalDate weekEnd;

    @ExcelProperty(value = "Billing Contact", index = 2)
    private String billingContact;

    @ExcelProperty(value = "Job Code", index = 3)
    private String jobCode;

    @ExcelProperty(value = "Assignment Division", index = 4, converter = AssignmentDivisionDataConverter.class)
    @Convert(converter = AssignmentDivisionConverter.class)
    private AssignmentDivision assignmentDivision;

    @ExcelProperty(value = "Email", index = 5)
    private String email;

    @ExcelProperty(value = "Work Phone", index = 6)
    private String phone;

    @ExcelProperty(value = "Approver", index = 7)
    private String approverName;

    @ExcelProperty(value = "Approver Email", index = 8)
    private String approverEmail;

    @ExcelProperty(value = "Approver Phone", index = 9)
    private String approverPhone;

    @ExcelProperty(value = "Corporation", index = 10)
    private String corporation;

    @ExcelProperty(value = "Employee Status", index = 11)
    private String employeeStatus;

    @ExcelIgnore
    private String primaryEmail;

    @ExcelIgnore
    private String primaryPhone;

    @ExcelIgnore
    private String managerEmail;

    @ExcelIgnore
    private String managerPhone;

    public String getApproverEmail() {
        return StrUtil.isBlank(approverEmail)
                && StrUtil.isBlank(approverPhone)?
                (StrUtil.isBlank(managerEmail) && StrUtil.isBlank(managerPhone) ? primaryEmail : managerEmail) :
                approverEmail;
    }

    public String getApproverPhone() {
        return StrUtil.isBlank(approverPhone)
                && StrUtil.isBlank(approverEmail) ?
                (StrUtil.isBlank(managerPhone) && StrUtil.isBlank(managerEmail) ? primaryPhone : managerPhone) :
                approverPhone;
    }

    @ExcelIgnore
    private BigDecimal billRate;

    @Transient
    @ExcelProperty(value = "Regular Bill Rate", index = 13)
    private String billRateFormat;

    @ExcelIgnore
    private String billCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType billTimeUnit;

    @ExcelIgnore
    private BigDecimal payRate;

    @Transient
    @ExcelProperty(value = "Regular Pay Rate", index = 14)
    private String payRateFormat;

    @ExcelIgnore
    private String payCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType payTimeUnit;

    @ExcelIgnore
    private BigDecimal overBillRate;

    @Transient
    @ExcelProperty(value = "Overtime Bill Rate", index = 15)
    private String overBillRateFormat;

    @ExcelIgnore
    private String overBillCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType overBillTimeUnit;

    @ExcelIgnore
    private BigDecimal overPayRate;

    @Transient
    @ExcelProperty(value = "Overtime Pay Rate", index = 16)
    private String overPayRateFormat;

    @ExcelIgnore
    private String overPayCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType overPayTimeUnit;

    @ExcelIgnore
    private BigDecimal doubleBillRate;

    @Transient
    @ExcelProperty(value = "Doubletime Bill Rate", index = 17)
    private String doubleBillRateFormat;

    @ExcelIgnore
    private String doubleBillCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType doubleBillTimeUnit;

    @ExcelIgnore
    private BigDecimal doublePayRate;

    @Transient
    @ExcelProperty(value = "Doubletime Pay Rate", index = 18)
    private String doublePayRateFormat;

    @ExcelIgnore
    private String doublePayCurrency;

    @ExcelIgnore
    @Convert(converter = RateUnitTypeConverter.class)
    private RateUnitType doublePayTimeUnit;

    @Transient
    @ExcelProperty(value = "Bill Rate Unit", index = 19)
    private String billRateUnitFormat;

    @Transient
    @ExcelProperty(value = "Pay Rate Unit", index = 20)
    private String payRateUnitFormat;

    @ExcelProperty(value = "AM", index = 21)
    private String am;

    @ExcelProperty(value = "DM", index = 22)
    private String dm;

    @ExcelProperty(value = "AC", index = 23)
    private String ac;

    @ExcelProperty(value = "Recruiter", index = 24)
    private String recruiter;

    @ExcelIgnore
    @Convert(converter = TimeSheetStatusConverter.class)
    private TimeSheetStatus status;

    @Transient
    @ExcelProperty(value = "Status", index = 25)
    private String statusFormat;

    @ExcelProperty(value = "Billing Frequency", index = 26, converter = TimeSheetFrequencyTypeDataConverter.class)
    private TimeSheetFrequencyType billingFrequency;

    @ExcelProperty(value = "Payment Frequency", index = 27, converter = TimeSheetFrequencyTypeDataConverter.class)
    private TimeSheetFrequencyType paymentFrequency;

    @ExcelProperty(value = "Company", index = 1)
    private String company;

    @ExcelProperty(value = "Charge Number", index = 50)
    private String chargeNumber;

    @ExcelProperty(value = "Tvc Number", index = 51)
    private String tvcNumber;

    @ExcelIgnore
    private String firstName;

    @ExcelIgnore
    private String lastName;

    public String getFullName() {
        return (StrUtil.isBlank(lastName)?"":lastName) + StrUtil.COMMA + StrUtil.SPACE + (StrUtil.isBlank(firstName)?"":firstName);
    }

    public String getWeekEndingDate() {
        if (ObjectUtil.isNotNull(weekEndingDate)) {
            return weekEndingDate.format(DateTimeFormatter.ofPattern(DateUtil.MM_DD_YYYY));
        }
        return "";
    }
    public String getBillRateUnitFormat() {
        return getBillCurrency() + "/" + billTimeUnit.name().substring(0,1).toUpperCase();
    }

    public String getPayRateUnitFormat() {
        return getPayCurrency() + "/" + payTimeUnit.name().substring(0,1).toUpperCase();
    }

    public String getStatusFormat() {
        if (status != null) {
            return status.getDescription();
        }
        return "";
    }

    public String getEmployeeStatus() {
        if (StrUtil.isBlank(employeeStatus)) {
            return employeeStatus;
        }
        if (!employeeStatus.startsWith("Past")) {
            return getEmploymentCategoryTypeDisplay(employeeStatus);
        } else {
            String[] split = employeeStatus.split("-");
            String employeeStatusStr = split[1];
            return "Past - " + getEmploymentCategoryTypeDisplay(employeeStatusStr);
        }
    }

    private String getEmploymentCategoryTypeDisplay(String employeeStatus) {
        AssignmentCategoryType assignmentCategoryType = AssignmentCategoryType.fromDbValue(Integer.parseInt(employeeStatus));
        return assignmentCategoryType.getDescription();
    }

    public String getBillRateFormat() {
        if (billRate != null) {
            return billRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getPayRateFormat() {
        if (payRate != null) {
            return payRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getOverBillRateFormat() {
        if (overBillRate != null) {
            return overBillRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getOverPayRateFormat() {
        if (overPayRate != null) {
            return overPayRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getDoubleBillRateFormat() {
        if (doubleBillRate != null) {
            return doubleBillRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }

    public String getDoublePayRateFormat() {
        if (doublePayRate != null) {
            return doublePayRate.setScale(2, RoundingMode.DOWN).toPlainString();
        }
        return null;
    }
}
