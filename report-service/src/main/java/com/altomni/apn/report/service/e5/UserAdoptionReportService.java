package com.altomni.apn.report.service.e5;

import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.vo.user.UserActiveDurationStatistic;
import com.altomni.apn.report.domain.vo.e5.TeamAdoptionReportVO;
import com.altomni.apn.report.domain.vo.e5.TeamAndUserAdoptionReportVO;
import com.altomni.apn.report.domain.vo.e5.UserAdoptionReportVO;
import com.altomni.apn.report.domain.vo.e5.UserAdoptionReportWithThresholdVO;
import com.altomni.apn.common.domain.user.GetLastWeekActiveDurationUserInfoDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportDTO;
import com.altomni.apn.report.dto.e5.UserAdoptionReportEmailStatsDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;

import javax.servlet.http.HttpServletResponse;
import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public interface UserAdoptionReportService {

    Integer getActiveDurationThreshold();

    Integer getActiveDurationThresholdV2(LocalDate startTime, LocalDate endTime);

    Map<Long, Long> getLastWeekBelowAverageActiveDurationUserByTeamId(Long tenantId, List<Long> teamId);

    UserAdoptionReportWithThresholdVO getUserAdoptionReport(UserAdoptionReportDTO userAdoptionReportDTO, Pageable pageable) throws ExecutionException, InterruptedException;

    List<TeamAdoptionReportVO> getUserAdoptionReportV3ForSum(List<TeamAdoptionReportVO> userAdoptionReportList) throws ExecutionException, InterruptedException;

    Map<Long, UserAdoptionReportEmailStatsDTO> getEmailCountByTeam(UserAdoptionReportDTO userAdoptionReportDTO, List<TeamInfoVO> teamInfoVOS);

    UserAdoptionReportEmailStatsDTO getEmailCountByForTotal(UserAdoptionReportDTO userAdoptionReportDTO, Collection<Long> ids);

    List<TeamAdoptionReportVO> getUserAdoptionReportByTeamView(Pageable pageable,UserAdoptionReportDTO userAdoptionReportDTO) throws ExecutionException, InterruptedException;

    ResponseEntity<?> getResponseForE5(UserAdoptionReportDTO userAdoptionReportDTO, Pageable pageable) throws ExecutionException, InterruptedException;

    TeamAndUserAdoptionReportVO getResponseForE5ForSum(UserAdoptionReportDTO userAdoptionReportDTO) throws ExecutionException, InterruptedException;

    UserAdoptionReportWithThresholdVO getUserAdoptionReportForDashboard(String view, UserAdoptionReportDTO userAdoptionReportDTO) throws ExecutionException, InterruptedException;

    void exportE5UserAdoptionReportToExcel(UserAdoptionReportDTO userAdoptionReportDTO, HttpServletResponse response) throws ExecutionException, InterruptedException;

    UserActiveDurationStatistic getLastWeekActiveDurationUserInfo(GetLastWeekActiveDurationUserInfoDTO dto);
}
