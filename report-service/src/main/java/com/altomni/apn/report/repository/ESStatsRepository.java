package com.altomni.apn.report.repository;

import com.altomni.apn.report.domain.ESStats;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the ESStats entity.
 */
@Repository
public interface ESStatsRepository extends JpaRepository<ESStats, Long> {

    @Query(value = "SELECT CONCAT_WS('', u.first_name, ' ', u.last_name) as username, COUNT(e.search_str) as totalCnt from esstats e INNER JOIN user u ON u.id = e.user_id AND e.tenant_id = ?1 AND e.created_date >= ?2 AND e.created_date <= ?3 GROUP BY e.user_id", nativeQuery = true)
    List<Object[]> getReportByTenantIdAndCreatedDateBetween(Long tenantId, String fromDate, String endDate);
}
