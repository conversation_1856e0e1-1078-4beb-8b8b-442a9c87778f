package com.altomni.apn.report.service;

import cn.hutool.core.thread.ThreadUtil;
import com.altomni.apn.job.service.job.impl.JobSyncServiceImpl;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public interface ReportBaseService {

    <T> List<T> searchData(String queryStr, Class<T> clazz, Map<Integer, Object> map);

}
