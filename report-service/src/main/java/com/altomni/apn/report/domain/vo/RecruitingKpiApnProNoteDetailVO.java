package com.altomni.apn.report.domain.vo;

import com.altomni.apn.common.aop.confidential.AttachConfidentialTalent;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.report.domain.enumeration.TrackingPlatform;
import com.altomni.apn.report.domain.enumeration.TrackingPlatformConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;

@Data
@Entity
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitingKpiApnProNoteDetailVO implements Serializable, AttachConfidentialTalent {

    @Id
    private Long id;

    private String fullName;

    private Long talentId;

    @Convert(converter = TrackingPlatformConverter.class)
    private TrackingPlatform sourceChannel;

    private String note;

    private Integer jobSearchStatus;

    private Long createdBy;

    private Long lastModifiedBy;

    private Instant createdDate;

    private Instant lastModifiedDate;

    @Transient
    private Boolean confidentialTalentViewAble;

    @Transient
    private ConfidentialInfoDto confidentialInfo;

    @Override
    public void encrypt() {
        this.fullName = null;
        this.sourceChannel = null;
        this.note = null;
        this.jobSearchStatus = null;
        this.createdBy = null;
        this.lastModifiedBy = null;
        this.createdDate = null;
        this.lastModifiedDate = null;
    }
}
