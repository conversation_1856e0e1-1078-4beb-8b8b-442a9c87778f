package com.altomni.apn.report;

import com.altomni.apn.common.config.*;
import com.altomni.apn.common.config.env.CommonApplicationProperties;
import com.altomni.apn.common.config.loadbalancer.LoadBalanceConfiguration;
import com.altomni.apn.common.datapermission.config.DataPermissionAutoConfiguration;
import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.errors.ExceptionTranslator;
import com.altomni.apn.common.interceptor.CustomResponseBodyAdviceAdapter;
import com.altomni.apn.common.interceptor.FeignClientInterceptor;
import com.altomni.apn.common.interceptor.SecurityDataLevelInterceptor;
import com.altomni.apn.common.interceptor.SecurityObjectLevelInterceptor;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CachedFeignSsoUserMapping;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.service.enums.EnumMotivationService;
import com.altomni.apn.common.service.http.impl.HttpServiceImpl;
import com.altomni.apn.common.service.log.impl.LoggingServiceImpl;
import com.altomni.apn.common.service.timezone.impl.EnumTimezoneServiceImpl;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SpringUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.boot.autoconfigure.data.mongo.MongoDataAutoConfiguration;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@EnableScheduling
@EnableJpaRepositories("com.altomni.apn.*.repository")
@EntityScan({"com.altomni.apn.*.domain", "com.altomni.apn.report.dto.*", "com.altomni.apn.common.vo.*"})
@EnableFeignClients(basePackages = {"com.altomni.apn"})
@SpringBootApplication(exclude = {RabbitAutoConfiguration.class, MongoAutoConfiguration.class, MongoDataAutoConfiguration.class})
@EnableDiscoveryClient
@EnableAsync
@LoadBalancerClients(defaultConfiguration = LoadBalanceConfiguration.class)
@Import({ExceptionTranslator.class,
        CacheConfig.class,
        SpringUtil.class,
        EnumCommonService.class,
        EnumCurrencyService.class,
        EnumMotivationService.class,
        EnumTimezoneServiceImpl.class,
        PublicBeanInjection.class,
        HttpServiceImpl.class,
        DataPermissionAutoConfiguration.class,
        CachePermission.class,
        CachedFeignSsoUserMapping.class,
        CommonRedisService.class,
        CommonApplicationProperties.class,
        TeamDataPermissionRule.class,
        FeignClientInterceptor.class,
        WebMvcConfig.class,
        SecurityObjectLevelInterceptor.class,
        SecurityDataLevelInterceptor.class,
        CustomResponseBodyAdviceAdapter.class,
        LoggingServiceImpl.class,
        SecurityUtils.class,
        GlobalCacheConfig.class,
        AppInit.class,
        CommonApiMultilingualConfig.class,
        ThreadPoolConfig.class})
public class ReportApp {
    public static void main(String[] args) {
        SpringApplication.run(ReportApp.class, args);
    }
}

