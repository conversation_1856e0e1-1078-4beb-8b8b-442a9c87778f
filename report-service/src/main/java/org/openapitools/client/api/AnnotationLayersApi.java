package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.*;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class AnnotationLayersApi {
    private ApiClient apiClient;

    public AnnotationLayersApi() {
        this(new ApiClient());
    }

    public AnnotationLayersApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Delete multiple annotation layers in a bulk operation
     *
     * <p><b>200</b> - CSS templates bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1AnnotationLayerDelete(List<Integer> q) throws RestClientException {
        return apiV1AnnotationLayerDeleteWithHttpInfo(q).getBody();
    }

    /**
     * Delete multiple annotation layers in a bulk operation
     *
     * <p><b>200</b> - CSS templates bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1AnnotationLayerDeleteWithHttpInfo(List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/", HttpMethod.DELETE, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a list of annotation layers
     * Gets a list of annotation layers, use Rison or JSON query parameters for filtering, sorting, pagination and for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AnnotationLayerGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerGet200Response apiV1AnnotationLayerGet(GetListSchema q) throws RestClientException {
        return apiV1AnnotationLayerGetWithHttpInfo(q).getBody();
    }

    /**
     * Get a list of annotation layers
     * Gets a list of annotation layers, use Rison or JSON query parameters for filtering, sorting, pagination and for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerGet200Response> apiV1AnnotationLayerGetWithHttpInfo(GetListSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AnnotationLayerInfoGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerInfoGet200Response apiV1AnnotationLayerInfoGet(GetInfoSchema q) throws RestClientException {
        return apiV1AnnotationLayerInfoGetWithHttpInfo(q).getBody();
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerInfoGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerInfoGet200Response> apiV1AnnotationLayerInfoGetWithHttpInfo(GetInfoSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/_info", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete annotation layer
     *
     * <p><b>200</b> - Item deleted
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk           The annotation layer pk for this annotation (required)
     * @param annotationId The annotation pk for this annotation (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1AnnotationLayerPkAnnotationAnnotationIdDelete(Integer pk, Integer annotationId) throws RestClientException {
        return apiV1AnnotationLayerPkAnnotationAnnotationIdDeleteWithHttpInfo(pk, annotationId).getBody();
    }

    /**
     * Delete annotation layer
     *
     * <p><b>200</b> - Item deleted
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk           The annotation layer pk for this annotation (required)
     * @param annotationId The annotation pk for this annotation (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1AnnotationLayerPkAnnotationAnnotationIdDeleteWithHttpInfo(Integer pk, Integer annotationId) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1AnnotationLayerPkAnnotationAnnotationIdDelete");
        }

        // verify the required parameter 'annotationId' is set
        if (annotationId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'annotationId' when calling apiV1AnnotationLayerPkAnnotationAnnotationIdDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);
        uriVariables.put("annotation_id", annotationId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/{pk}/annotation/{annotation_id}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get an annotation layer
     *
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk           The annotation layer pk for this annotation (required)
     * @param annotationId The annotation pk (required)
     * @param q            (optional)
     * @return ApiV1AnnotationLayerPkAnnotationAnnotationIdGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerPkAnnotationAnnotationIdGet200Response apiV1AnnotationLayerPkAnnotationAnnotationIdGet(Integer pk, Integer annotationId, GetItemSchema q) throws RestClientException {
        return apiV1AnnotationLayerPkAnnotationAnnotationIdGetWithHttpInfo(pk, annotationId, q).getBody();
    }

    /**
     * Get an annotation layer
     *
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk           The annotation layer pk for this annotation (required)
     * @param annotationId The annotation pk (required)
     * @param q            (optional)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerPkAnnotationAnnotationIdGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerPkAnnotationAnnotationIdGet200Response> apiV1AnnotationLayerPkAnnotationAnnotationIdGetWithHttpInfo(Integer pk, Integer annotationId, GetItemSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1AnnotationLayerPkAnnotationAnnotationIdGet");
        }

        // verify the required parameter 'annotationId' is set
        if (annotationId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'annotationId' when calling apiV1AnnotationLayerPkAnnotationAnnotationIdGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);
        uriVariables.put("annotation_id", annotationId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerPkAnnotationAnnotationIdGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerPkAnnotationAnnotationIdGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/{pk}/annotation/{annotation_id}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Update an annotation layer
     *
     * <p><b>200</b> - Annotation changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk                   The annotation layer pk for this annotation (required)
     * @param annotationId         The annotation pk for this annotation (required)
     * @param annotationRestApiPut Annotation schema (required)
     * @return ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response apiV1AnnotationLayerPkAnnotationAnnotationIdPut(Integer pk, Integer annotationId, AnnotationRestApiPut annotationRestApiPut) throws RestClientException {
        return apiV1AnnotationLayerPkAnnotationAnnotationIdPutWithHttpInfo(pk, annotationId, annotationRestApiPut).getBody();
    }

    /**
     * Update an annotation layer
     *
     * <p><b>200</b> - Annotation changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk                   The annotation layer pk for this annotation (required)
     * @param annotationId         The annotation pk for this annotation (required)
     * @param annotationRestApiPut Annotation schema (required)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response> apiV1AnnotationLayerPkAnnotationAnnotationIdPutWithHttpInfo(Integer pk, Integer annotationId, AnnotationRestApiPut annotationRestApiPut) throws RestClientException {
        Object localVarPostBody = annotationRestApiPut;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1AnnotationLayerPkAnnotationAnnotationIdPut");
        }

        // verify the required parameter 'annotationId' is set
        if (annotationId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'annotationId' when calling apiV1AnnotationLayerPkAnnotationAnnotationIdPut");
        }

        // verify the required parameter 'annotationRestApiPut' is set
        if (annotationRestApiPut == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'annotationRestApiPut' when calling apiV1AnnotationLayerPkAnnotationAnnotationIdPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);
        uriVariables.put("annotation_id", annotationId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/{pk}/annotation/{annotation_id}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Bulk delete annotation layers
     *
     * <p><b>200</b> - Annotations bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk The annotation layer pk for this annotation (required)
     * @param q  (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1AnnotationLayerPkAnnotationDelete(Integer pk, List<Integer> q) throws RestClientException {
        return apiV1AnnotationLayerPkAnnotationDeleteWithHttpInfo(pk, q).getBody();
    }

    /**
     * Bulk delete annotation layers
     *
     * <p><b>200</b> - Annotations bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk The annotation layer pk for this annotation (required)
     * @param q  (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1AnnotationLayerPkAnnotationDeleteWithHttpInfo(Integer pk, List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1AnnotationLayerPkAnnotationDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/{pk}/annotation/", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a list of annotation layers
     * Gets a list of annotation layers, use Rison or JSON query parameters for filtering, sorting, pagination and for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Annotations
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk The annotation layer id for this annotation (required)
     * @param q  (optional)
     * @return ApiV1AnnotationLayerPkAnnotationGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerPkAnnotationGet200Response apiV1AnnotationLayerPkAnnotationGet(Integer pk, GetListSchema q) throws RestClientException {
        return apiV1AnnotationLayerPkAnnotationGetWithHttpInfo(pk, q).getBody();
    }

    /**
     * Get a list of annotation layers
     * Gets a list of annotation layers, use Rison or JSON query parameters for filtering, sorting, pagination and for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Annotations
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk The annotation layer id for this annotation (required)
     * @param q  (optional)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerPkAnnotationGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerPkAnnotationGet200Response> apiV1AnnotationLayerPkAnnotationGetWithHttpInfo(Integer pk, GetListSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1AnnotationLayerPkAnnotationGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerPkAnnotationGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerPkAnnotationGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/{pk}/annotation/", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create an annotation layer
     *
     * <p><b>201</b> - Annotation added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk                    The annotation layer pk for this annotation (required)
     * @param annotationRestApiPost Annotation schema (required)
     * @return ApiV1AnnotationLayerPkAnnotationPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerPkAnnotationPost201Response apiV1AnnotationLayerPkAnnotationPost(Integer pk, AnnotationRestApiPost annotationRestApiPost) throws RestClientException {
        return apiV1AnnotationLayerPkAnnotationPostWithHttpInfo(pk, annotationRestApiPost).getBody();
    }

    /**
     * Create an annotation layer
     *
     * <p><b>201</b> - Annotation added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk                    The annotation layer pk for this annotation (required)
     * @param annotationRestApiPost Annotation schema (required)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerPkAnnotationPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerPkAnnotationPost201Response> apiV1AnnotationLayerPkAnnotationPostWithHttpInfo(Integer pk, AnnotationRestApiPost annotationRestApiPost) throws RestClientException {
        Object localVarPostBody = annotationRestApiPost;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1AnnotationLayerPkAnnotationPost");
        }

        // verify the required parameter 'annotationRestApiPost' is set
        if (annotationRestApiPost == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'annotationRestApiPost' when calling apiV1AnnotationLayerPkAnnotationPost");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerPkAnnotationPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerPkAnnotationPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/{pk}/annotation/", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete annotation layer
     *
     * <p><b>200</b> - Item deleted
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk The annotation layer pk for this annotation (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1AnnotationLayerPkDelete(Integer pk) throws RestClientException {
        return apiV1AnnotationLayerPkDeleteWithHttpInfo(pk).getBody();
    }

    /**
     * Delete annotation layer
     *
     * <p><b>200</b> - Item deleted
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk The annotation layer pk for this annotation (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1AnnotationLayerPkDeleteWithHttpInfo(Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1AnnotationLayerPkDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/{pk}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get an annotation layer
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ApiV1AnnotationLayerPkGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerPkGet200Response apiV1AnnotationLayerPkGet(Integer pk, GetItemSchema q) throws RestClientException {
        return apiV1AnnotationLayerPkGetWithHttpInfo(pk, q).getBody();
    }

    /**
     * Get an annotation layer
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerPkGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerPkGet200Response> apiV1AnnotationLayerPkGetWithHttpInfo(Integer pk, GetItemSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1AnnotationLayerPkGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerPkGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerPkGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/{pk}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Update an annotation layer
     *
     * <p><b>200</b> - Annotation changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk                        The annotation layer pk for this annotation (required)
     * @param annotationLayerRestApiPut Annotation schema (required)
     * @return ApiV1AnnotationLayerPkPut200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerPkPut200Response apiV1AnnotationLayerPkPut(Integer pk, AnnotationLayerRestApiPut annotationLayerRestApiPut) throws RestClientException {
        return apiV1AnnotationLayerPkPutWithHttpInfo(pk, annotationLayerRestApiPut).getBody();
    }

    /**
     * Update an annotation layer
     *
     * <p><b>200</b> - Annotation changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk                        The annotation layer pk for this annotation (required)
     * @param annotationLayerRestApiPut Annotation schema (required)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerPkPut200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerPkPut200Response> apiV1AnnotationLayerPkPutWithHttpInfo(Integer pk, AnnotationLayerRestApiPut annotationLayerRestApiPut) throws RestClientException {
        Object localVarPostBody = annotationLayerRestApiPut;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1AnnotationLayerPkPut");
        }

        // verify the required parameter 'annotationLayerRestApiPut' is set
        if (annotationLayerRestApiPut == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'annotationLayerRestApiPut' when calling apiV1AnnotationLayerPkPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerPkPut200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerPkPut200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/{pk}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create an annotation layer
     *
     * <p><b>201</b> - Annotation added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param annotationLayerRestApiPost Annotation Layer schema (required)
     * @return ApiV1AnnotationLayerPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerPost201Response apiV1AnnotationLayerPost(AnnotationLayerRestApiPost annotationLayerRestApiPost) throws RestClientException {
        return apiV1AnnotationLayerPostWithHttpInfo(annotationLayerRestApiPost).getBody();
    }

    /**
     * Create an annotation layer
     *
     * <p><b>201</b> - Annotation added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param annotationLayerRestApiPost Annotation Layer schema (required)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerPost201Response> apiV1AnnotationLayerPostWithHttpInfo(AnnotationLayerRestApiPost annotationLayerRestApiPost) throws RestClientException {
        Object localVarPostBody = annotationLayerRestApiPost;

        // verify the required parameter 'annotationLayerRestApiPost' is set
        if (annotationLayerRestApiPost == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'annotationLayerRestApiPost' when calling apiV1AnnotationLayerPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return RelatedResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RelatedResponseSchema apiV1AnnotationLayerRelatedColumnNameGet(String columnName, GetRelatedSchema q) throws RestClientException {
        return apiV1AnnotationLayerRelatedColumnNameGetWithHttpInfo(columnName, q).getBody();
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return ResponseEntity&lt;RelatedResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RelatedResponseSchema> apiV1AnnotationLayerRelatedColumnNameGetWithHttpInfo(String columnName, GetRelatedSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1AnnotationLayerRelatedColumnNameGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<RelatedResponseSchema> localReturnType = new ParameterizedTypeReference<RelatedResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/annotation_layer/related/{column_name}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
