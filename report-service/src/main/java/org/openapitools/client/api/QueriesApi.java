package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.io.File;
import java.util.*;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class QueriesApi {
    private ApiClient apiClient;

    public QueriesApi() {
        this(new ApiClient());
    }

    public QueriesApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get distinct values from field data
     *
     * <p><b>200</b> - Distinct field data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return DistincResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public DistincResponseSchema apiV1QueryDistinctColumnNameGet(String columnName, GetRelatedSchema q) throws RestClientException {
        return apiV1QueryDistinctColumnNameGetWithHttpInfo(columnName, q).getBody();
    }

    /**
     * Get distinct values from field data
     *
     * <p><b>200</b> - Distinct field data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return ResponseEntity&lt;DistincResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<DistincResponseSchema> apiV1QueryDistinctColumnNameGetWithHttpInfo(String columnName, GetRelatedSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1QueryDistinctColumnNameGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<DistincResponseSchema> localReturnType = new ParameterizedTypeReference<DistincResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/query/distinct/{column_name}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a list of queries
     * Gets a list of queries, use Rison or JSON query parameters for filtering, sorting, pagination and  for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1QueryGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1QueryGet200Response apiV1QueryGet(GetListSchema q) throws RestClientException {
        return apiV1QueryGetWithHttpInfo(q).getBody();
    }

    /**
     * Get a list of queries
     * Gets a list of queries, use Rison or JSON query parameters for filtering, sorting, pagination and  for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1QueryGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1QueryGet200Response> apiV1QueryGetWithHttpInfo(GetListSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1QueryGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1QueryGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/query/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get query detail information
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ApiV1QueryPkGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1QueryPkGet200Response apiV1QueryPkGet(Integer pk, GetItemSchema q) throws RestClientException {
        return apiV1QueryPkGetWithHttpInfo(pk, q).getBody();
    }

    /**
     * Get query detail information
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ResponseEntity&lt;ApiV1QueryPkGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1QueryPkGet200Response> apiV1QueryPkGetWithHttpInfo(Integer pk, GetItemSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1QueryPkGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1QueryPkGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1QueryPkGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/query/{pk}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return RelatedResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RelatedResponseSchema apiV1QueryRelatedColumnNameGet(String columnName, GetRelatedSchema q) throws RestClientException {
        return apiV1QueryRelatedColumnNameGetWithHttpInfo(columnName, q).getBody();
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return ResponseEntity&lt;RelatedResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RelatedResponseSchema> apiV1QueryRelatedColumnNameGetWithHttpInfo(String columnName, GetRelatedSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1QueryRelatedColumnNameGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<RelatedResponseSchema> localReturnType = new ParameterizedTypeReference<RelatedResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/query/related/{column_name}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Manually stop a query with client_id
     *
     * <p><b>200</b> - Query stopped
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param stopQuerySchema Stop query schema (required)
     * @return ApiV1QueryStopPost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1QueryStopPost200Response apiV1QueryStopPost(StopQuerySchema stopQuerySchema) throws RestClientException {
        return apiV1QueryStopPostWithHttpInfo(stopQuerySchema).getBody();
    }

    /**
     * Manually stop a query with client_id
     *
     * <p><b>200</b> - Query stopped
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param stopQuerySchema Stop query schema (required)
     * @return ResponseEntity&lt;ApiV1QueryStopPost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1QueryStopPost200Response> apiV1QueryStopPostWithHttpInfo(StopQuerySchema stopQuerySchema) throws RestClientException {
        Object localVarPostBody = stopQuerySchema;

        // verify the required parameter 'stopQuerySchema' is set
        if (stopQuerySchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'stopQuerySchema' when calling apiV1QueryStopPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1QueryStopPost200Response> localReturnType = new ParameterizedTypeReference<ApiV1QueryStopPost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/query/stop", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a list of queries that changed after last_updated_ms
     *
     * <p><b>200</b> - Queries list
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1QueryUpdatedSinceGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1QueryUpdatedSinceGet200Response apiV1QueryUpdatedSinceGet(QueriesGetUpdatedSinceSchema q) throws RestClientException {
        return apiV1QueryUpdatedSinceGetWithHttpInfo(q).getBody();
    }

    /**
     * Get a list of queries that changed after last_updated_ms
     *
     * <p><b>200</b> - Queries list
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1QueryUpdatedSinceGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1QueryUpdatedSinceGet200Response> apiV1QueryUpdatedSinceGetWithHttpInfo(QueriesGetUpdatedSinceSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1QueryUpdatedSinceGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1QueryUpdatedSinceGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/query/updated_since", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Bulk delete saved queries
     *
     * <p><b>200</b> - Saved queries bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1SavedQueryDelete(List<Integer> q) throws RestClientException {
        return apiV1SavedQueryDeleteWithHttpInfo(q).getBody();
    }

    /**
     * Bulk delete saved queries
     *
     * <p><b>200</b> - Saved queries bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1SavedQueryDeleteWithHttpInfo(List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/", HttpMethod.DELETE, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get distinct values from field data
     *
     * <p><b>200</b> - Distinct field data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return DistincResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public DistincResponseSchema apiV1SavedQueryDistinctColumnNameGet(String columnName, GetRelatedSchema q) throws RestClientException {
        return apiV1SavedQueryDistinctColumnNameGetWithHttpInfo(columnName, q).getBody();
    }

    /**
     * Get distinct values from field data
     *
     * <p><b>200</b> - Distinct field data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return ResponseEntity&lt;DistincResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<DistincResponseSchema> apiV1SavedQueryDistinctColumnNameGetWithHttpInfo(String columnName, GetRelatedSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1SavedQueryDistinctColumnNameGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<DistincResponseSchema> localReturnType = new ParameterizedTypeReference<DistincResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/distinct/{column_name}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Download multiple saved queries as YAML files
     *
     * <p><b>200</b> - A zip file with saved query(ies) and database(s) as YAML
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return File
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public File apiV1SavedQueryExportGet(List<Integer> q) throws RestClientException {
        return apiV1SavedQueryExportGetWithHttpInfo(q).getBody();
    }

    /**
     * Download multiple saved queries as YAML files
     *
     * <p><b>200</b> - A zip file with saved query(ies) and database(s) as YAML
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;File&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<File> apiV1SavedQueryExportGetWithHttpInfo(List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "application/zip", "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<File> localReturnType = new ParameterizedTypeReference<File>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/export/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a list of saved queries
     * Gets a list of saved queries, use Rison or JSON query parameters for filtering, sorting, pagination and for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1SavedQueryGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1SavedQueryGet200Response apiV1SavedQueryGet(GetListSchema q) throws RestClientException {
        return apiV1SavedQueryGetWithHttpInfo(q).getBody();
    }

    /**
     * Get a list of saved queries
     * Gets a list of saved queries, use Rison or JSON query parameters for filtering, sorting, pagination and for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1SavedQueryGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1SavedQueryGet200Response> apiV1SavedQueryGetWithHttpInfo(GetListSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1SavedQueryGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1SavedQueryGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Import saved queries with associated databases
     *
     * <p><b>200</b> - Saved Query import result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formData                     upload file (ZIP) (optional)
     * @param overwrite                    overwrite existing saved queries? (optional)
     * @param passwords                    JSON map of passwords for each featured database in the ZIP file. If the ZIP includes a database config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPasswords           JSON map of passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeyPasswords JSON map of private_key_passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeys         JSON map of private_keys for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key\\\&quot;}&#x60;. (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1SavedQueryImportPost(File formData, Boolean overwrite, String passwords, String sshTunnelPasswords, String sshTunnelPrivateKeyPasswords, String sshTunnelPrivateKeys) throws RestClientException {
        return apiV1SavedQueryImportPostWithHttpInfo(formData, overwrite, passwords, sshTunnelPasswords, sshTunnelPrivateKeyPasswords, sshTunnelPrivateKeys).getBody();
    }

    /**
     * Import saved queries with associated databases
     *
     * <p><b>200</b> - Saved Query import result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formData                     upload file (ZIP) (optional)
     * @param overwrite                    overwrite existing saved queries? (optional)
     * @param passwords                    JSON map of passwords for each featured database in the ZIP file. If the ZIP includes a database config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPasswords           JSON map of passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeyPasswords JSON map of private_key_passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeys         JSON map of private_keys for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key\\\&quot;}&#x60;. (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1SavedQueryImportPostWithHttpInfo(File formData, Boolean overwrite, String passwords, String sshTunnelPasswords, String sshTunnelPrivateKeyPasswords, String sshTunnelPrivateKeys) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (formData != null)
            localVarFormParams.add("formData", new FileSystemResource(formData));
        if (overwrite != null)
            localVarFormParams.add("overwrite", overwrite);
        if (passwords != null)
            localVarFormParams.add("passwords", passwords);
        if (sshTunnelPasswords != null)
            localVarFormParams.add("ssh_tunnel_passwords", sshTunnelPasswords);
        if (sshTunnelPrivateKeyPasswords != null)
            localVarFormParams.add("ssh_tunnel_private_key_passwords", sshTunnelPrivateKeyPasswords);
        if (sshTunnelPrivateKeys != null)
            localVarFormParams.add("ssh_tunnel_private_keys", sshTunnelPrivateKeys);

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/import/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AnnotationLayerInfoGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerInfoGet200Response apiV1SavedQueryInfoGet(GetInfoSchema q) throws RestClientException {
        return apiV1SavedQueryInfoGetWithHttpInfo(q).getBody();
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerInfoGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerInfoGet200Response> apiV1SavedQueryInfoGetWithHttpInfo(GetInfoSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/_info", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete a saved query
     *
     * <p><b>200</b> - Item deleted
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1SavedQueryPkDelete(Integer pk) throws RestClientException {
        return apiV1SavedQueryPkDeleteWithHttpInfo(pk).getBody();
    }

    /**
     * Delete a saved query
     *
     * <p><b>200</b> - Item deleted
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1SavedQueryPkDeleteWithHttpInfo(Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1SavedQueryPkDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/{pk}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a saved query
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ApiV1SavedQueryPkGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1SavedQueryPkGet200Response apiV1SavedQueryPkGet(Integer pk, GetItemSchema q) throws RestClientException {
        return apiV1SavedQueryPkGetWithHttpInfo(pk, q).getBody();
    }

    /**
     * Get a saved query
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ResponseEntity&lt;ApiV1SavedQueryPkGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1SavedQueryPkGet200Response> apiV1SavedQueryPkGetWithHttpInfo(Integer pk, GetItemSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1SavedQueryPkGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1SavedQueryPkGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1SavedQueryPkGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/{pk}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Update a saved query
     *
     * <p><b>200</b> - Item changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                   (required)
     * @param savedQueryRestApiPut Model schema (required)
     * @return ApiV1SavedQueryPkPut200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1SavedQueryPkPut200Response apiV1SavedQueryPkPut(Integer pk, SavedQueryRestApiPut savedQueryRestApiPut) throws RestClientException {
        return apiV1SavedQueryPkPutWithHttpInfo(pk, savedQueryRestApiPut).getBody();
    }

    /**
     * Update a saved query
     *
     * <p><b>200</b> - Item changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                   (required)
     * @param savedQueryRestApiPut Model schema (required)
     * @return ResponseEntity&lt;ApiV1SavedQueryPkPut200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1SavedQueryPkPut200Response> apiV1SavedQueryPkPutWithHttpInfo(Integer pk, SavedQueryRestApiPut savedQueryRestApiPut) throws RestClientException {
        Object localVarPostBody = savedQueryRestApiPut;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1SavedQueryPkPut");
        }

        // verify the required parameter 'savedQueryRestApiPut' is set
        if (savedQueryRestApiPut == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'savedQueryRestApiPut' when calling apiV1SavedQueryPkPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1SavedQueryPkPut200Response> localReturnType = new ParameterizedTypeReference<ApiV1SavedQueryPkPut200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/{pk}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create a saved query
     *
     * <p><b>201</b> - Item inserted
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param savedQueryRestApiPost Model schema (required)
     * @return ApiV1SavedQueryPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1SavedQueryPost201Response apiV1SavedQueryPost(SavedQueryRestApiPost savedQueryRestApiPost) throws RestClientException {
        return apiV1SavedQueryPostWithHttpInfo(savedQueryRestApiPost).getBody();
    }

    /**
     * Create a saved query
     *
     * <p><b>201</b> - Item inserted
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param savedQueryRestApiPost Model schema (required)
     * @return ResponseEntity&lt;ApiV1SavedQueryPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1SavedQueryPost201Response> apiV1SavedQueryPostWithHttpInfo(SavedQueryRestApiPost savedQueryRestApiPost) throws RestClientException {
        Object localVarPostBody = savedQueryRestApiPost;

        // verify the required parameter 'savedQueryRestApiPost' is set
        if (savedQueryRestApiPost == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'savedQueryRestApiPost' when calling apiV1SavedQueryPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1SavedQueryPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1SavedQueryPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return RelatedResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RelatedResponseSchema apiV1SavedQueryRelatedColumnNameGet(String columnName, GetRelatedSchema q) throws RestClientException {
        return apiV1SavedQueryRelatedColumnNameGetWithHttpInfo(columnName, q).getBody();
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return ResponseEntity&lt;RelatedResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RelatedResponseSchema> apiV1SavedQueryRelatedColumnNameGetWithHttpInfo(String columnName, GetRelatedSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1SavedQueryRelatedColumnNameGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<RelatedResponseSchema> localReturnType = new ParameterizedTypeReference<RelatedResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/saved_query/related/{column_name}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
