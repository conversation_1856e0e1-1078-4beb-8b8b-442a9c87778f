package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.ApiV1DashboardPermalinkKeyGet200Response;
import org.openapitools.client.model.ApiV1DashboardPkPermalinkPost201Response;
import org.openapitools.client.model.ExplorePermalinkStateSchema;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class ExplorePermanentLinkApi {
    private ApiClient apiClient;

    public ExplorePermanentLinkApi() {
        this(new ApiClient());
    }

    public ExplorePermanentLinkApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get chart&#39;s permanent link state
     *
     * <p><b>200</b> - Returns the stored form_data.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param key (required)
     * @return ApiV1DashboardPermalinkKeyGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardPermalinkKeyGet200Response apiV1ExplorePermalinkKeyGet(String key) throws RestClientException {
        return apiV1ExplorePermalinkKeyGetWithHttpInfo(key).getBody();
    }

    /**
     * Get chart&#39;s permanent link state
     *
     * <p><b>200</b> - Returns the stored form_data.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param key (required)
     * @return ResponseEntity&lt;ApiV1DashboardPermalinkKeyGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardPermalinkKeyGet200Response> apiV1ExplorePermalinkKeyGetWithHttpInfo(String key) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'key' is set
        if (key == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'key' when calling apiV1ExplorePermalinkKeyGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("key", key);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardPermalinkKeyGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardPermalinkKeyGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/explore/permalink/{key}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create a new permanent link
     *
     * <p><b>201</b> - The permanent link was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param explorePermalinkStateSchema (required)
     * @return ApiV1DashboardPkPermalinkPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardPkPermalinkPost201Response apiV1ExplorePermalinkPost(ExplorePermalinkStateSchema explorePermalinkStateSchema) throws RestClientException {
        return apiV1ExplorePermalinkPostWithHttpInfo(explorePermalinkStateSchema).getBody();
    }

    /**
     * Create a new permanent link
     *
     * <p><b>201</b> - The permanent link was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param explorePermalinkStateSchema (required)
     * @return ResponseEntity&lt;ApiV1DashboardPkPermalinkPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardPkPermalinkPost201Response> apiV1ExplorePermalinkPostWithHttpInfo(ExplorePermalinkStateSchema explorePermalinkStateSchema) throws RestClientException {
        Object localVarPostBody = explorePermalinkStateSchema;

        // verify the required parameter 'explorePermalinkStateSchema' is set
        if (explorePermalinkStateSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'explorePermalinkStateSchema' when calling apiV1ExplorePermalinkPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardPkPermalinkPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardPkPermalinkPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/explore/permalink", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
