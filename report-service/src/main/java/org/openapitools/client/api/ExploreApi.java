package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.ExploreContextSchema;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.List;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class ExploreApi {
    private ApiClient apiClient;

    public ExploreApi() {
        this(new ApiClient());
    }

    public ExploreApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Assemble Explore related information in a single endpoint
     * Assembles Explore related information (form_data, slice, dataset) in a single endpoint.&lt;br/&gt;&lt;br/&gt; The information can be assembled from:&lt;br/&gt; - The cache using a form_data_key&lt;br/&gt; - The metadata database using a permalink_key&lt;br/&gt; - Build from scratch using dataset or slice identifiers.
     * <p><b>200</b> - Returns the initial context.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formDataKey    (optional)
     * @param permalinkKey   (optional)
     * @param sliceId        (optional)
     * @param datasourceId   (optional)
     * @param datasourceType (optional)
     * @return ExploreContextSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ExploreContextSchema apiV1ExploreGet(String formDataKey, String permalinkKey, Integer sliceId, Integer datasourceId, String datasourceType) throws RestClientException {
        return apiV1ExploreGetWithHttpInfo(formDataKey, permalinkKey, sliceId, datasourceId, datasourceType).getBody();
    }

    /**
     * Assemble Explore related information in a single endpoint
     * Assembles Explore related information (form_data, slice, dataset) in a single endpoint.&lt;br/&gt;&lt;br/&gt; The information can be assembled from:&lt;br/&gt; - The cache using a form_data_key&lt;br/&gt; - The metadata database using a permalink_key&lt;br/&gt; - Build from scratch using dataset or slice identifiers.
     * <p><b>200</b> - Returns the initial context.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formDataKey    (optional)
     * @param permalinkKey   (optional)
     * @param sliceId        (optional)
     * @param datasourceId   (optional)
     * @param datasourceType (optional)
     * @return ResponseEntity&lt;ExploreContextSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ExploreContextSchema> apiV1ExploreGetWithHttpInfo(String formDataKey, String permalinkKey, Integer sliceId, Integer datasourceId, String datasourceType) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "form_data_key", formDataKey));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "permalink_key", permalinkKey));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "slice_id", sliceId));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "datasource_id", datasourceId));
        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "datasource_type", datasourceType));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ExploreContextSchema> localReturnType = new ParameterizedTypeReference<ExploreContextSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/explore/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
