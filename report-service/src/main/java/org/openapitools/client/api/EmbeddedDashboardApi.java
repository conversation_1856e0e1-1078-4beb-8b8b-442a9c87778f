package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.ApiV1DashboardIdOrSlugEmbeddedGet200Response;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class EmbeddedDashboardApi {
    private ApiClient apiClient;

    public EmbeddedDashboardApi() {
        this(new ApiClient());
    }

    public EmbeddedDashboardApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get a report schedule log
     *
     * <p><b>200</b> - Result contains the embedded dashboard configuration
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param uuid The embedded configuration uuid (required)
     * @return ApiV1DashboardIdOrSlugEmbeddedGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardIdOrSlugEmbeddedGet200Response apiV1EmbeddedDashboardUuidGet(String uuid) throws RestClientException {
        return apiV1EmbeddedDashboardUuidGetWithHttpInfo(uuid).getBody();
    }

    /**
     * Get a report schedule log
     *
     * <p><b>200</b> - Result contains the embedded dashboard configuration
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param uuid The embedded configuration uuid (required)
     * @return ResponseEntity&lt;ApiV1DashboardIdOrSlugEmbeddedGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardIdOrSlugEmbeddedGet200Response> apiV1EmbeddedDashboardUuidGetWithHttpInfo(String uuid) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'uuid' is set
        if (uuid == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'uuid' when calling apiV1EmbeddedDashboardUuidGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("uuid", uuid);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardIdOrSlugEmbeddedGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardIdOrSlugEmbeddedGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/embedded_dashboard/{uuid}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
