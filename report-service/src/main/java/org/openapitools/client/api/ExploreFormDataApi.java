package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class ExploreFormDataApi {
    private ApiClient apiClient;

    public ExploreFormDataApi() {
        this(new ApiClient());
    }

    public ExploreFormDataApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Delete a form_data
     *
     * <p><b>200</b> - Deleted the stored form_data.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param key The form_data key. (required)
     * @return ApiV1DashboardPkFilterStateKeyDelete200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardPkFilterStateKeyDelete200Response apiV1ExploreFormDataKeyDelete(String key) throws RestClientException {
        return apiV1ExploreFormDataKeyDeleteWithHttpInfo(key).getBody();
    }

    /**
     * Delete a form_data
     *
     * <p><b>200</b> - Deleted the stored form_data.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param key The form_data key. (required)
     * @return ResponseEntity&lt;ApiV1DashboardPkFilterStateKeyDelete200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardPkFilterStateKeyDelete200Response> apiV1ExploreFormDataKeyDeleteWithHttpInfo(String key) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'key' is set
        if (key == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'key' when calling apiV1ExploreFormDataKeyDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("key", key);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardPkFilterStateKeyDelete200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardPkFilterStateKeyDelete200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/explore/form_data/{key}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a form_data
     *
     * <p><b>200</b> - Returns the stored form_data.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param key (required)
     * @return ApiV1ExploreFormDataKeyGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1ExploreFormDataKeyGet200Response apiV1ExploreFormDataKeyGet(String key) throws RestClientException {
        return apiV1ExploreFormDataKeyGetWithHttpInfo(key).getBody();
    }

    /**
     * Get a form_data
     *
     * <p><b>200</b> - Returns the stored form_data.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param key (required)
     * @return ResponseEntity&lt;ApiV1ExploreFormDataKeyGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1ExploreFormDataKeyGet200Response> apiV1ExploreFormDataKeyGetWithHttpInfo(String key) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'key' is set
        if (key == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'key' when calling apiV1ExploreFormDataKeyGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("key", key);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1ExploreFormDataKeyGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1ExploreFormDataKeyGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/explore/form_data/{key}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Update an existing form_data
     *
     * <p><b>200</b> - The form_data was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param key               (required)
     * @param formDataPutSchema (required)
     * @param tabId             (optional)
     * @return ApiV1ExploreFormDataPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1ExploreFormDataPost201Response apiV1ExploreFormDataKeyPut(String key, FormDataPutSchema formDataPutSchema, Integer tabId) throws RestClientException {
        return apiV1ExploreFormDataKeyPutWithHttpInfo(key, formDataPutSchema, tabId).getBody();
    }

    /**
     * Update an existing form_data
     *
     * <p><b>200</b> - The form_data was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param key               (required)
     * @param formDataPutSchema (required)
     * @param tabId             (optional)
     * @return ResponseEntity&lt;ApiV1ExploreFormDataPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1ExploreFormDataPost201Response> apiV1ExploreFormDataKeyPutWithHttpInfo(String key, FormDataPutSchema formDataPutSchema, Integer tabId) throws RestClientException {
        Object localVarPostBody = formDataPutSchema;

        // verify the required parameter 'key' is set
        if (key == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'key' when calling apiV1ExploreFormDataKeyPut");
        }

        // verify the required parameter 'formDataPutSchema' is set
        if (formDataPutSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'formDataPutSchema' when calling apiV1ExploreFormDataKeyPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("key", key);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "tab_id", tabId));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1ExploreFormDataPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1ExploreFormDataPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/explore/form_data/{key}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create a new form_data
     *
     * <p><b>201</b> - The form_data was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formDataPostSchema (required)
     * @param tabId              (optional)
     * @return ApiV1ExploreFormDataPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1ExploreFormDataPost201Response apiV1ExploreFormDataPost(FormDataPostSchema formDataPostSchema, Integer tabId) throws RestClientException {
        return apiV1ExploreFormDataPostWithHttpInfo(formDataPostSchema, tabId).getBody();
    }

    /**
     * Create a new form_data
     *
     * <p><b>201</b> - The form_data was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formDataPostSchema (required)
     * @param tabId              (optional)
     * @return ResponseEntity&lt;ApiV1ExploreFormDataPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1ExploreFormDataPost201Response> apiV1ExploreFormDataPostWithHttpInfo(FormDataPostSchema formDataPostSchema, Integer tabId) throws RestClientException {
        Object localVarPostBody = formDataPostSchema;

        // verify the required parameter 'formDataPostSchema' is set
        if (formDataPostSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'formDataPostSchema' when calling apiV1ExploreFormDataPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "tab_id", tabId));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1ExploreFormDataPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1ExploreFormDataPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/explore/form_data", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
