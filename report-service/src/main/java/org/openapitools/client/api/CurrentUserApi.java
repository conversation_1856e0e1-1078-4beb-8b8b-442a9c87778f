package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.ApiV1MeGet200Response;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.List;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class CurrentUserApi {
    private ApiClient apiClient;

    public CurrentUserApi() {
        this(new ApiClient());
    }

    public CurrentUserApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get the user object
     * Gets the user object corresponding to the agent making the request, or returns a 401 error if the user is unauthenticated.
     * <p><b>200</b> - The current user
     * <p><b>401</b> - Unauthorized
     *
     * @return ApiV1MeGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1MeGet200Response apiV1MeGet() throws RestClientException {
        return apiV1MeGetWithHttpInfo().getBody();
    }

    /**
     * Get the user object
     * Gets the user object corresponding to the agent making the request, or returns a 401 error if the user is unauthenticated.
     * <p><b>200</b> - The current user
     * <p><b>401</b> - Unauthorized
     *
     * @return ResponseEntity&lt;ApiV1MeGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1MeGet200Response> apiV1MeGetWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{};

        ParameterizedTypeReference<ApiV1MeGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1MeGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/me/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get the user roles
     * Gets the user roles corresponding to the agent making the request, or returns a 401 error if the user is unauthenticated.
     * <p><b>200</b> - The current user
     * <p><b>401</b> - Unauthorized
     *
     * @return ApiV1MeGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1MeGet200Response apiV1MeRolesGet() throws RestClientException {
        return apiV1MeRolesGetWithHttpInfo().getBody();
    }

    /**
     * Get the user roles
     * Gets the user roles corresponding to the agent making the request, or returns a 401 error if the user is unauthenticated.
     * <p><b>200</b> - The current user
     * <p><b>401</b> - Unauthorized
     *
     * @return ResponseEntity&lt;ApiV1MeGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1MeGet200Response> apiV1MeRolesGetWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{};

        ParameterizedTypeReference<ApiV1MeGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1MeGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/me/roles/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
