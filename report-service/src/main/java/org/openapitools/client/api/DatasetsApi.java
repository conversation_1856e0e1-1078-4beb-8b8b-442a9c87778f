package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.io.File;
import java.util.*;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class DatasetsApi {
    private ApiClient apiClient;

    public DatasetsApi() {
        this(new ApiClient());
    }

    public DatasetsApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Bulk delete datasets
     *
     * <p><b>200</b> - Dataset bulk delete
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DatasetDelete(List<Integer> q) throws RestClientException {
        return apiV1DatasetDeleteWithHttpInfo(q).getBody();
    }

    /**
     * Bulk delete datasets
     *
     * <p><b>200</b> - Dataset bulk delete
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DatasetDeleteWithHttpInfo(List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/", HttpMethod.DELETE, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get distinct values from field data
     *
     * <p><b>200</b> - Distinct field data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return DistincResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public DistincResponseSchema apiV1DatasetDistinctColumnNameGet(String columnName, GetRelatedSchema q) throws RestClientException {
        return apiV1DatasetDistinctColumnNameGetWithHttpInfo(columnName, q).getBody();
    }

    /**
     * Get distinct values from field data
     *
     * <p><b>200</b> - Distinct field data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return ResponseEntity&lt;DistincResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<DistincResponseSchema> apiV1DatasetDistinctColumnNameGetWithHttpInfo(String columnName, GetRelatedSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1DatasetDistinctColumnNameGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<DistincResponseSchema> localReturnType = new ParameterizedTypeReference<DistincResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/distinct/{column_name}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Duplicate a dataset
     *
     * <p><b>201</b> - Dataset duplicated
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param datasetDuplicateSchema Dataset schema (required)
     * @return ApiV1DatasetDuplicatePost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DatasetDuplicatePost201Response apiV1DatasetDuplicatePost(DatasetDuplicateSchema datasetDuplicateSchema) throws RestClientException {
        return apiV1DatasetDuplicatePostWithHttpInfo(datasetDuplicateSchema).getBody();
    }

    /**
     * Duplicate a dataset
     *
     * <p><b>201</b> - Dataset duplicated
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param datasetDuplicateSchema Dataset schema (required)
     * @return ResponseEntity&lt;ApiV1DatasetDuplicatePost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DatasetDuplicatePost201Response> apiV1DatasetDuplicatePostWithHttpInfo(DatasetDuplicateSchema datasetDuplicateSchema) throws RestClientException {
        Object localVarPostBody = datasetDuplicateSchema;

        // verify the required parameter 'datasetDuplicateSchema' is set
        if (datasetDuplicateSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'datasetDuplicateSchema' when calling apiV1DatasetDuplicatePost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DatasetDuplicatePost201Response> localReturnType = new ParameterizedTypeReference<ApiV1DatasetDuplicatePost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/duplicate", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Download multiple datasets as YAML files
     *
     * <p><b>200</b> - Dataset export
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return String
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public String apiV1DatasetExportGet(List<Integer> q) throws RestClientException {
        return apiV1DatasetExportGetWithHttpInfo(q).getBody();
    }

    /**
     * Download multiple datasets as YAML files
     *
     * <p><b>200</b> - Dataset export
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;String&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<String> apiV1DatasetExportGetWithHttpInfo(List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "text/plain", "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<String> localReturnType = new ParameterizedTypeReference<String>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/export/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a list of datasets
     * Gets a list of datasets, use Rison or JSON query parameters for filtering, sorting, pagination and  for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1DatasetGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DatasetGet200Response apiV1DatasetGet(GetListSchema q) throws RestClientException {
        return apiV1DatasetGetWithHttpInfo(q).getBody();
    }

    /**
     * Get a list of datasets
     * Gets a list of datasets, use Rison or JSON query parameters for filtering, sorting, pagination and  for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1DatasetGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DatasetGet200Response> apiV1DatasetGetWithHttpInfo(GetListSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DatasetGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DatasetGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Retrieve a table by name, or create it if it does not exist
     *
     * <p><b>200</b> - The ID of the table
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param getOrCreateDatasetSchema (required)
     * @return ApiV1DatasetGetOrCreatePost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DatasetGetOrCreatePost200Response apiV1DatasetGetOrCreatePost(GetOrCreateDatasetSchema getOrCreateDatasetSchema) throws RestClientException {
        return apiV1DatasetGetOrCreatePostWithHttpInfo(getOrCreateDatasetSchema).getBody();
    }

    /**
     * Retrieve a table by name, or create it if it does not exist
     *
     * <p><b>200</b> - The ID of the table
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param getOrCreateDatasetSchema (required)
     * @return ResponseEntity&lt;ApiV1DatasetGetOrCreatePost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DatasetGetOrCreatePost200Response> apiV1DatasetGetOrCreatePostWithHttpInfo(GetOrCreateDatasetSchema getOrCreateDatasetSchema) throws RestClientException {
        Object localVarPostBody = getOrCreateDatasetSchema;

        // verify the required parameter 'getOrCreateDatasetSchema' is set
        if (getOrCreateDatasetSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'getOrCreateDatasetSchema' when calling apiV1DatasetGetOrCreatePost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DatasetGetOrCreatePost200Response> localReturnType = new ParameterizedTypeReference<ApiV1DatasetGetOrCreatePost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/get_or_create/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Import dataset(s) with associated databases
     *
     * <p><b>200</b> - Dataset import result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formData                     upload file (ZIP or YAML) (optional)
     * @param overwrite                    overwrite existing datasets? (optional)
     * @param passwords                    JSON map of passwords for each featured database in the ZIP file. If the ZIP includes a database config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPasswords           JSON map of passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeyPasswords JSON map of private_key_passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeys         JSON map of private_keys for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key\\\&quot;}&#x60;. (optional)
     * @param syncColumns                  sync columns? (optional)
     * @param syncMetrics                  sync metrics? (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DatasetImportPost(File formData, Boolean overwrite, String passwords, String sshTunnelPasswords, String sshTunnelPrivateKeyPasswords, String sshTunnelPrivateKeys, Boolean syncColumns, Boolean syncMetrics) throws RestClientException {
        return apiV1DatasetImportPostWithHttpInfo(formData, overwrite, passwords, sshTunnelPasswords, sshTunnelPrivateKeyPasswords, sshTunnelPrivateKeys, syncColumns, syncMetrics).getBody();
    }

    /**
     * Import dataset(s) with associated databases
     *
     * <p><b>200</b> - Dataset import result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formData                     upload file (ZIP or YAML) (optional)
     * @param overwrite                    overwrite existing datasets? (optional)
     * @param passwords                    JSON map of passwords for each featured database in the ZIP file. If the ZIP includes a database config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPasswords           JSON map of passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeyPasswords JSON map of private_key_passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeys         JSON map of private_keys for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key\\\&quot;}&#x60;. (optional)
     * @param syncColumns                  sync columns? (optional)
     * @param syncMetrics                  sync metrics? (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DatasetImportPostWithHttpInfo(File formData, Boolean overwrite, String passwords, String sshTunnelPasswords, String sshTunnelPrivateKeyPasswords, String sshTunnelPrivateKeys, Boolean syncColumns, Boolean syncMetrics) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (formData != null)
            localVarFormParams.add("formData", new FileSystemResource(formData));
        if (overwrite != null)
            localVarFormParams.add("overwrite", overwrite);
        if (passwords != null)
            localVarFormParams.add("passwords", passwords);
        if (sshTunnelPasswords != null)
            localVarFormParams.add("ssh_tunnel_passwords", sshTunnelPasswords);
        if (sshTunnelPrivateKeyPasswords != null)
            localVarFormParams.add("ssh_tunnel_private_key_passwords", sshTunnelPrivateKeyPasswords);
        if (sshTunnelPrivateKeys != null)
            localVarFormParams.add("ssh_tunnel_private_keys", sshTunnelPrivateKeys);
        if (syncColumns != null)
            localVarFormParams.add("sync_columns", syncColumns);
        if (syncMetrics != null)
            localVarFormParams.add("sync_metrics", syncMetrics);

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/import/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AnnotationLayerInfoGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerInfoGet200Response apiV1DatasetInfoGet(GetInfoSchema q) throws RestClientException {
        return apiV1DatasetInfoGetWithHttpInfo(q).getBody();
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerInfoGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerInfoGet200Response> apiV1DatasetInfoGetWithHttpInfo(GetInfoSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/_info", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete a dataset column
     *
     * <p><b>200</b> - Column deleted
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk       The dataset pk for this column (required)
     * @param columnId The column id for this dataset (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DatasetPkColumnColumnIdDelete(Integer pk, Integer columnId) throws RestClientException {
        return apiV1DatasetPkColumnColumnIdDeleteWithHttpInfo(pk, columnId).getBody();
    }

    /**
     * Delete a dataset column
     *
     * <p><b>200</b> - Column deleted
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk       The dataset pk for this column (required)
     * @param columnId The column id for this dataset (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DatasetPkColumnColumnIdDeleteWithHttpInfo(Integer pk, Integer columnId) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DatasetPkColumnColumnIdDelete");
        }

        // verify the required parameter 'columnId' is set
        if (columnId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnId' when calling apiV1DatasetPkColumnColumnIdDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);
        uriVariables.put("column_id", columnId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/{pk}/column/{column_id}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete a dataset
     *
     * <p><b>200</b> - Dataset delete
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DatasetPkDelete(Integer pk) throws RestClientException {
        return apiV1DatasetPkDeleteWithHttpInfo(pk).getBody();
    }

    /**
     * Delete a dataset
     *
     * <p><b>200</b> - Dataset delete
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DatasetPkDeleteWithHttpInfo(Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DatasetPkDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/{pk}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a dataset detail information
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ApiV1DatasetPkGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DatasetPkGet200Response apiV1DatasetPkGet(Integer pk, GetItemSchema q) throws RestClientException {
        return apiV1DatasetPkGetWithHttpInfo(pk, q).getBody();
    }

    /**
     * Get a dataset detail information
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ResponseEntity&lt;ApiV1DatasetPkGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DatasetPkGet200Response> apiV1DatasetPkGetWithHttpInfo(Integer pk, GetItemSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DatasetPkGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DatasetPkGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DatasetPkGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/{pk}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete a dataset metric
     *
     * <p><b>200</b> - Metric deleted
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk       The dataset pk for this column (required)
     * @param metricId The metric id for this dataset (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DatasetPkMetricMetricIdDelete(Integer pk, Integer metricId) throws RestClientException {
        return apiV1DatasetPkMetricMetricIdDeleteWithHttpInfo(pk, metricId).getBody();
    }

    /**
     * Delete a dataset metric
     *
     * <p><b>200</b> - Metric deleted
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk       The dataset pk for this column (required)
     * @param metricId The metric id for this dataset (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DatasetPkMetricMetricIdDeleteWithHttpInfo(Integer pk, Integer metricId) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DatasetPkMetricMetricIdDelete");
        }

        // verify the required parameter 'metricId' is set
        if (metricId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'metricId' when calling apiV1DatasetPkMetricMetricIdDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);
        uriVariables.put("metric_id", metricId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/{pk}/metric/{metric_id}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Update a dataset
     *
     * <p><b>200</b> - Dataset changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                (required)
     * @param datasetRestApiPut Dataset schema (required)
     * @param overrideColumns   (optional)
     * @return ApiV1DatasetPkPut200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DatasetPkPut200Response apiV1DatasetPkPut(Integer pk, DatasetRestApiPut datasetRestApiPut, Boolean overrideColumns) throws RestClientException {
        return apiV1DatasetPkPutWithHttpInfo(pk, datasetRestApiPut, overrideColumns).getBody();
    }

    /**
     * Update a dataset
     *
     * <p><b>200</b> - Dataset changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                (required)
     * @param datasetRestApiPut Dataset schema (required)
     * @param overrideColumns   (optional)
     * @return ResponseEntity&lt;ApiV1DatasetPkPut200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DatasetPkPut200Response> apiV1DatasetPkPutWithHttpInfo(Integer pk, DatasetRestApiPut datasetRestApiPut, Boolean overrideColumns) throws RestClientException {
        Object localVarPostBody = datasetRestApiPut;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DatasetPkPut");
        }

        // verify the required parameter 'datasetRestApiPut' is set
        if (datasetRestApiPut == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'datasetRestApiPut' when calling apiV1DatasetPkPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "override_columns", overrideColumns));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DatasetPkPut200Response> localReturnType = new ParameterizedTypeReference<ApiV1DatasetPkPut200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/{pk}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Refresh and update columns of a dataset
     *
     * <p><b>200</b> - Dataset delete
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DatasetPkRefreshPut(Integer pk) throws RestClientException {
        return apiV1DatasetPkRefreshPutWithHttpInfo(pk).getBody();
    }

    /**
     * Refresh and update columns of a dataset
     *
     * <p><b>200</b> - Dataset delete
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DatasetPkRefreshPutWithHttpInfo(Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DatasetPkRefreshPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/{pk}/refresh", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get charts and dashboards count associated to a dataset
     *
     * <p><b>200</b> - Query result
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return DatasetRelatedObjectsResponse
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public DatasetRelatedObjectsResponse apiV1DatasetPkRelatedObjectsGet(Integer pk) throws RestClientException {
        return apiV1DatasetPkRelatedObjectsGetWithHttpInfo(pk).getBody();
    }

    /**
     * Get charts and dashboards count associated to a dataset
     *
     * <p><b>200</b> - Query result
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ResponseEntity&lt;DatasetRelatedObjectsResponse&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<DatasetRelatedObjectsResponse> apiV1DatasetPkRelatedObjectsGetWithHttpInfo(Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DatasetPkRelatedObjectsGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<DatasetRelatedObjectsResponse> localReturnType = new ParameterizedTypeReference<DatasetRelatedObjectsResponse>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/{pk}/related_objects", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create a new dataset
     *
     * <p><b>201</b> - Dataset added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param datasetRestApiPost Dataset schema (required)
     * @return ApiV1DatasetPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DatasetPost201Response apiV1DatasetPost(DatasetRestApiPost datasetRestApiPost) throws RestClientException {
        return apiV1DatasetPostWithHttpInfo(datasetRestApiPost).getBody();
    }

    /**
     * Create a new dataset
     *
     * <p><b>201</b> - Dataset added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param datasetRestApiPost Dataset schema (required)
     * @return ResponseEntity&lt;ApiV1DatasetPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DatasetPost201Response> apiV1DatasetPostWithHttpInfo(DatasetRestApiPost datasetRestApiPost) throws RestClientException {
        Object localVarPostBody = datasetRestApiPost;

        // verify the required parameter 'datasetRestApiPost' is set
        if (datasetRestApiPost == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'datasetRestApiPost' when calling apiV1DatasetPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DatasetPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1DatasetPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return RelatedResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RelatedResponseSchema apiV1DatasetRelatedColumnNameGet(String columnName, GetRelatedSchema q) throws RestClientException {
        return apiV1DatasetRelatedColumnNameGetWithHttpInfo(columnName, q).getBody();
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return ResponseEntity&lt;RelatedResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RelatedResponseSchema> apiV1DatasetRelatedColumnNameGetWithHttpInfo(String columnName, GetRelatedSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1DatasetRelatedColumnNameGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<RelatedResponseSchema> localReturnType = new ParameterizedTypeReference<RelatedResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/related/{column_name}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Warm up the cache for each chart powered by the given table
     * Warms up the cache for the table. Note for slices a force refresh occurs. In terms of the &#x60;extra_filters&#x60; these can be obtained from records in the JSON encoded &#x60;logs.json&#x60; column associated with the &#x60;explore_json&#x60; action.
     * <p><b>200</b> - Each chart&#39;s warmup status
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param datasetCacheWarmUpRequestSchema Identifies the database and table to warm up cache for, and any additional dashboard or filter context to use. (required)
     * @return DatasetCacheWarmUpResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public DatasetCacheWarmUpResponseSchema apiV1DatasetWarmUpCachePut(DatasetCacheWarmUpRequestSchema datasetCacheWarmUpRequestSchema) throws RestClientException {
        return apiV1DatasetWarmUpCachePutWithHttpInfo(datasetCacheWarmUpRequestSchema).getBody();
    }

    /**
     * Warm up the cache for each chart powered by the given table
     * Warms up the cache for the table. Note for slices a force refresh occurs. In terms of the &#x60;extra_filters&#x60; these can be obtained from records in the JSON encoded &#x60;logs.json&#x60; column associated with the &#x60;explore_json&#x60; action.
     * <p><b>200</b> - Each chart&#39;s warmup status
     * <p><b>400</b> - Bad request
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param datasetCacheWarmUpRequestSchema Identifies the database and table to warm up cache for, and any additional dashboard or filter context to use. (required)
     * @return ResponseEntity&lt;DatasetCacheWarmUpResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<DatasetCacheWarmUpResponseSchema> apiV1DatasetWarmUpCachePutWithHttpInfo(DatasetCacheWarmUpRequestSchema datasetCacheWarmUpRequestSchema) throws RestClientException {
        Object localVarPostBody = datasetCacheWarmUpRequestSchema;

        // verify the required parameter 'datasetCacheWarmUpRequestSchema' is set
        if (datasetCacheWarmUpRequestSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'datasetCacheWarmUpRequestSchema' when calling apiV1DatasetWarmUpCachePut");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<DatasetCacheWarmUpResponseSchema> localReturnType = new ParameterizedTypeReference<DatasetCacheWarmUpResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/dataset/warm_up_cache", HttpMethod.PUT, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
