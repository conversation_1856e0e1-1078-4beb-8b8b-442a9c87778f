package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.CacheInvalidationRequestSchema;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.List;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class CacheRestApiApi {
    private ApiClient apiClient;

    public CacheRestApiApi() {
        this(new ApiClient());
    }

    public CacheRestApiApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Invalidate cache records and remove the database records
     * Takes a list of datasources, finds and invalidates the associated cache records and removes the database records.
     * <p><b>201</b> - cache was successfully invalidated
     * <p><b>400</b> - Bad request
     * <p><b>500</b> - Fatal error
     *
     * @param cacheInvalidationRequestSchema A list of datasources uuid or the tuples of database and datasource names (required)
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public void apiV1CachekeyInvalidatePost(CacheInvalidationRequestSchema cacheInvalidationRequestSchema) throws RestClientException {
        apiV1CachekeyInvalidatePostWithHttpInfo(cacheInvalidationRequestSchema);
    }

    /**
     * Invalidate cache records and remove the database records
     * Takes a list of datasources, finds and invalidates the associated cache records and removes the database records.
     * <p><b>201</b> - cache was successfully invalidated
     * <p><b>400</b> - Bad request
     * <p><b>500</b> - Fatal error
     *
     * @param cacheInvalidationRequestSchema A list of datasources uuid or the tuples of database and datasource names (required)
     * @return ResponseEntity&lt;Void&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<Void> apiV1CachekeyInvalidatePostWithHttpInfo(CacheInvalidationRequestSchema cacheInvalidationRequestSchema) throws RestClientException {
        Object localVarPostBody = cacheInvalidationRequestSchema;

        // verify the required parameter 'cacheInvalidationRequestSchema' is set
        if (cacheInvalidationRequestSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'cacheInvalidationRequestSchema' when calling apiV1CachekeyInvalidatePost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<Void> localReturnType = new ParameterizedTypeReference<Void>() {
        };
        return apiClient.invokeAPI("/api/v1/cachekey/invalidate", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
