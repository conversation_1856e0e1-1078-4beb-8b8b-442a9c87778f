package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.ApiV1AsyncEventGet200Response;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.List;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class AsyncEventsRestApiApi {
    private ApiClient apiClient;

    public AsyncEventsRestApiApi() {
        this(new ApiClient());
    }

    public AsyncEventsRestApiApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Read off of the Redis events stream
     * Reads off of the Redis events stream, using the user&#39;s JWT token and optional query params for last event received.
     * <p><b>200</b> - Async event results
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param lastId Last ID received by the client (optional)
     * @return ApiV1AsyncEventGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AsyncEventGet200Response apiV1AsyncEventGet(String lastId) throws RestClientException {
        return apiV1AsyncEventGetWithHttpInfo(lastId).getBody();
    }

    /**
     * Read off of the Redis events stream
     * Reads off of the Redis events stream, using the user&#39;s JWT token and optional query params for last event received.
     * <p><b>200</b> - Async event results
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param lastId Last ID received by the client (optional)
     * @return ResponseEntity&lt;ApiV1AsyncEventGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AsyncEventGet200Response> apiV1AsyncEventGetWithHttpInfo(String lastId) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "last_id", lastId));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AsyncEventGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AsyncEventGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/async_event/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
