package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200Response;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class DatasourcesApi {
    private ApiClient apiClient;

    public DatasourcesApi() {
        this(new ApiClient());
    }

    public DatasourcesApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get possible values for a datasource column
     *
     * <p><b>200</b> - A List of distinct values for the column
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param datasourceType The type of datasource (required)
     * @param datasourceId   The id of the datasource (required)
     * @param columnName     The name of the column to get values for (required)
     * @return ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200Response apiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet(String datasourceType, Integer datasourceId, String columnName) throws RestClientException {
        return apiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGetWithHttpInfo(datasourceType, datasourceId, columnName).getBody();
    }

    /**
     * Get possible values for a datasource column
     *
     * <p><b>200</b> - A List of distinct values for the column
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param datasourceType The type of datasource (required)
     * @param datasourceId   The id of the datasource (required)
     * @param columnName     The name of the column to get values for (required)
     * @return ResponseEntity&lt;ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200Response> apiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGetWithHttpInfo(String datasourceType, Integer datasourceId, String columnName) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'datasourceType' is set
        if (datasourceType == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'datasourceType' when calling apiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet");
        }

        // verify the required parameter 'datasourceId' is set
        if (datasourceId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'datasourceId' when calling apiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet");
        }

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("datasource_type", datasourceType);
        uriVariables.put("datasource_id", datasourceId);
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/datasource/{datasource_type}/{datasource_id}/column/{column_name}/values/", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
