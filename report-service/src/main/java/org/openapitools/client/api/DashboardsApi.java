package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.io.File;
import java.util.*;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class DashboardsApi {
    private ApiClient apiClient;

    public DashboardsApi() {
        this(new ApiClient());
    }

    public DashboardsApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Bulk delete dashboards
     *
     * <p><b>200</b> - Dashboard bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DashboardDelete(List<Integer> q) throws RestClientException {
        return apiV1DashboardDeleteWithHttpInfo(q).getBody();
    }

    /**
     * Bulk delete dashboards
     *
     * <p><b>200</b> - Dashboard bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DashboardDeleteWithHttpInfo(List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/", HttpMethod.DELETE, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Download multiple dashboards as YAML files
     *
     * <p><b>200</b> - Dashboard export
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return String
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public String apiV1DashboardExportGet(List<Integer> q) throws RestClientException {
        return apiV1DashboardExportGetWithHttpInfo(q).getBody();
    }

    /**
     * Download multiple dashboards as YAML files
     *
     * <p><b>200</b> - Dashboard export
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;String&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<String> apiV1DashboardExportGetWithHttpInfo(List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "text/plain", "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<String> localReturnType = new ParameterizedTypeReference<String>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/export/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Check favorited dashboards for current user
     *
     * <p><b>200</b> - None
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return GetFavStarIdsSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public GetFavStarIdsSchema apiV1DashboardFavoriteStatusGet(List<Integer> q) throws RestClientException {
        return apiV1DashboardFavoriteStatusGetWithHttpInfo(q).getBody();
    }

    /**
     * Check favorited dashboards for current user
     *
     * <p><b>200</b> - None
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;GetFavStarIdsSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<GetFavStarIdsSchema> apiV1DashboardFavoriteStatusGetWithHttpInfo(List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<GetFavStarIdsSchema> localReturnType = new ParameterizedTypeReference<GetFavStarIdsSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/favorite_status/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a list of dashboards
     * Gets a list of dashboards, use Rison or JSON query parameters for filtering, sorting, pagination and  for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1DashboardGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardGet200Response apiV1DashboardGet(GetListSchema q) throws RestClientException {
        return apiV1DashboardGetWithHttpInfo(q).getBody();
    }

    /**
     * Get a list of dashboards
     * Gets a list of dashboards, use Rison or JSON query parameters for filtering, sorting, pagination and  for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1DashboardGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardGet200Response> apiV1DashboardGetWithHttpInfo(GetListSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a dashboard&#39;s chart definitions.
     *
     * <p><b>200</b> - Dashboard chart definitions
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     *
     * @param idOrSlug (required)
     * @return ApiV1DashboardIdOrSlugChartsGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardIdOrSlugChartsGet200Response apiV1DashboardIdOrSlugChartsGet(String idOrSlug) throws RestClientException {
        return apiV1DashboardIdOrSlugChartsGetWithHttpInfo(idOrSlug).getBody();
    }

    /**
     * Get a dashboard&#39;s chart definitions.
     *
     * <p><b>200</b> - Dashboard chart definitions
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     *
     * @param idOrSlug (required)
     * @return ResponseEntity&lt;ApiV1DashboardIdOrSlugChartsGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardIdOrSlugChartsGet200Response> apiV1DashboardIdOrSlugChartsGetWithHttpInfo(String idOrSlug) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'idOrSlug' is set
        if (idOrSlug == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'idOrSlug' when calling apiV1DashboardIdOrSlugChartsGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id_or_slug", idOrSlug);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardIdOrSlugChartsGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardIdOrSlugChartsGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{id_or_slug}/charts", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create a copy of an existing dashboard
     *
     * <p><b>200</b> - Id of new dashboard and last modified time
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug            The dashboard id or slug (required)
     * @param dashboardCopySchema (required)
     * @return ApiV1DashboardIdOrSlugCopyPost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardIdOrSlugCopyPost200Response apiV1DashboardIdOrSlugCopyPost(String idOrSlug, DashboardCopySchema dashboardCopySchema) throws RestClientException {
        return apiV1DashboardIdOrSlugCopyPostWithHttpInfo(idOrSlug, dashboardCopySchema).getBody();
    }

    /**
     * Create a copy of an existing dashboard
     *
     * <p><b>200</b> - Id of new dashboard and last modified time
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug            The dashboard id or slug (required)
     * @param dashboardCopySchema (required)
     * @return ResponseEntity&lt;ApiV1DashboardIdOrSlugCopyPost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardIdOrSlugCopyPost200Response> apiV1DashboardIdOrSlugCopyPostWithHttpInfo(String idOrSlug, DashboardCopySchema dashboardCopySchema) throws RestClientException {
        Object localVarPostBody = dashboardCopySchema;

        // verify the required parameter 'idOrSlug' is set
        if (idOrSlug == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'idOrSlug' when calling apiV1DashboardIdOrSlugCopyPost");
        }

        // verify the required parameter 'dashboardCopySchema' is set
        if (dashboardCopySchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'dashboardCopySchema' when calling apiV1DashboardIdOrSlugCopyPost");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id_or_slug", idOrSlug);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardIdOrSlugCopyPost200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardIdOrSlugCopyPost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{id_or_slug}/copy/", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get dashboard&#39;s datasets
     * Returns a list of a dashboard&#39;s datasets. Each dataset includes only the information necessary to render the dashboard&#39;s charts.
     * <p><b>200</b> - Dashboard dataset definitions
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     *
     * @param idOrSlug Either the id of the dashboard, or its slug (required)
     * @return ApiV1DashboardIdOrSlugDatasetsGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardIdOrSlugDatasetsGet200Response apiV1DashboardIdOrSlugDatasetsGet(String idOrSlug) throws RestClientException {
        return apiV1DashboardIdOrSlugDatasetsGetWithHttpInfo(idOrSlug).getBody();
    }

    /**
     * Get dashboard&#39;s datasets
     * Returns a list of a dashboard&#39;s datasets. Each dataset includes only the information necessary to render the dashboard&#39;s charts.
     * <p><b>200</b> - Dashboard dataset definitions
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     *
     * @param idOrSlug Either the id of the dashboard, or its slug (required)
     * @return ResponseEntity&lt;ApiV1DashboardIdOrSlugDatasetsGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardIdOrSlugDatasetsGet200Response> apiV1DashboardIdOrSlugDatasetsGetWithHttpInfo(String idOrSlug) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'idOrSlug' is set
        if (idOrSlug == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'idOrSlug' when calling apiV1DashboardIdOrSlugDatasetsGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id_or_slug", idOrSlug);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardIdOrSlugDatasetsGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardIdOrSlugDatasetsGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{id_or_slug}/datasets", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete a dashboard&#39;s embedded configuration
     *
     * <p><b>200</b> - Successfully removed the configuration
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug The dashboard id or slug (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DashboardIdOrSlugEmbeddedDelete(String idOrSlug) throws RestClientException {
        return apiV1DashboardIdOrSlugEmbeddedDeleteWithHttpInfo(idOrSlug).getBody();
    }

    /**
     * Delete a dashboard&#39;s embedded configuration
     *
     * <p><b>200</b> - Successfully removed the configuration
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug The dashboard id or slug (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DashboardIdOrSlugEmbeddedDeleteWithHttpInfo(String idOrSlug) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'idOrSlug' is set
        if (idOrSlug == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'idOrSlug' when calling apiV1DashboardIdOrSlugEmbeddedDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id_or_slug", idOrSlug);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{id_or_slug}/embedded", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get the dashboard&#39;s embedded configuration
     *
     * <p><b>200</b> - Result contains the embedded dashboard config
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug The dashboard id or slug (required)
     * @return ApiV1DashboardIdOrSlugEmbeddedGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardIdOrSlugEmbeddedGet200Response apiV1DashboardIdOrSlugEmbeddedGet(String idOrSlug) throws RestClientException {
        return apiV1DashboardIdOrSlugEmbeddedGetWithHttpInfo(idOrSlug).getBody();
    }

    /**
     * Get the dashboard&#39;s embedded configuration
     *
     * <p><b>200</b> - Result contains the embedded dashboard config
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug The dashboard id or slug (required)
     * @return ResponseEntity&lt;ApiV1DashboardIdOrSlugEmbeddedGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardIdOrSlugEmbeddedGet200Response> apiV1DashboardIdOrSlugEmbeddedGetWithHttpInfo(String idOrSlug) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'idOrSlug' is set
        if (idOrSlug == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'idOrSlug' when calling apiV1DashboardIdOrSlugEmbeddedGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id_or_slug", idOrSlug);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardIdOrSlugEmbeddedGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardIdOrSlugEmbeddedGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{id_or_slug}/embedded", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Set a dashboard&#39;s embedded configuration
     *
     * <p><b>200</b> - Successfully set the configuration
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug                The dashboard id or slug (required)
     * @param embeddedDashboardConfig The embedded configuration to set (required)
     * @return ApiV1DashboardIdOrSlugEmbeddedGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardIdOrSlugEmbeddedGet200Response apiV1DashboardIdOrSlugEmbeddedPost(String idOrSlug, EmbeddedDashboardConfig embeddedDashboardConfig) throws RestClientException {
        return apiV1DashboardIdOrSlugEmbeddedPostWithHttpInfo(idOrSlug, embeddedDashboardConfig).getBody();
    }

    /**
     * Set a dashboard&#39;s embedded configuration
     *
     * <p><b>200</b> - Successfully set the configuration
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug                The dashboard id or slug (required)
     * @param embeddedDashboardConfig The embedded configuration to set (required)
     * @return ResponseEntity&lt;ApiV1DashboardIdOrSlugEmbeddedGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardIdOrSlugEmbeddedGet200Response> apiV1DashboardIdOrSlugEmbeddedPostWithHttpInfo(String idOrSlug, EmbeddedDashboardConfig embeddedDashboardConfig) throws RestClientException {
        Object localVarPostBody = embeddedDashboardConfig;

        // verify the required parameter 'idOrSlug' is set
        if (idOrSlug == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'idOrSlug' when calling apiV1DashboardIdOrSlugEmbeddedPost");
        }

        // verify the required parameter 'embeddedDashboardConfig' is set
        if (embeddedDashboardConfig == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'embeddedDashboardConfig' when calling apiV1DashboardIdOrSlugEmbeddedPost");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id_or_slug", idOrSlug);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardIdOrSlugEmbeddedGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardIdOrSlugEmbeddedGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{id_or_slug}/embedded", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Sets a dashboard&#39;s embedded configuration.
     * <p><b>200</b> - Successfully set the configuration
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug                The dashboard id or slug (required)
     * @param embeddedDashboardConfig The embedded configuration to set (required)
     * @return ApiV1DashboardIdOrSlugEmbeddedGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardIdOrSlugEmbeddedGet200Response apiV1DashboardIdOrSlugEmbeddedPut(String idOrSlug, EmbeddedDashboardConfig embeddedDashboardConfig) throws RestClientException {
        return apiV1DashboardIdOrSlugEmbeddedPutWithHttpInfo(idOrSlug, embeddedDashboardConfig).getBody();
    }

    /**
     * Sets a dashboard&#39;s embedded configuration.
     * <p><b>200</b> - Successfully set the configuration
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param idOrSlug                The dashboard id or slug (required)
     * @param embeddedDashboardConfig The embedded configuration to set (required)
     * @return ResponseEntity&lt;ApiV1DashboardIdOrSlugEmbeddedGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardIdOrSlugEmbeddedGet200Response> apiV1DashboardIdOrSlugEmbeddedPutWithHttpInfo(String idOrSlug, EmbeddedDashboardConfig embeddedDashboardConfig) throws RestClientException {
        Object localVarPostBody = embeddedDashboardConfig;

        // verify the required parameter 'idOrSlug' is set
        if (idOrSlug == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'idOrSlug' when calling apiV1DashboardIdOrSlugEmbeddedPut");
        }

        // verify the required parameter 'embeddedDashboardConfig' is set
        if (embeddedDashboardConfig == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'embeddedDashboardConfig' when calling apiV1DashboardIdOrSlugEmbeddedPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id_or_slug", idOrSlug);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardIdOrSlugEmbeddedGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardIdOrSlugEmbeddedGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{id_or_slug}/embedded", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a dashboard detail information
     *
     * <p><b>200</b> - Dashboard
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     *
     * @param idOrSlug Either the id of the dashboard, or its slug (required)
     * @return ApiV1DashboardIdOrSlugGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardIdOrSlugGet200Response apiV1DashboardIdOrSlugGet(String idOrSlug) throws RestClientException {
        return apiV1DashboardIdOrSlugGetWithHttpInfo(idOrSlug).getBody();
    }

    /**
     * Get a dashboard detail information
     *
     * <p><b>200</b> - Dashboard
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     *
     * @param idOrSlug Either the id of the dashboard, or its slug (required)
     * @return ResponseEntity&lt;ApiV1DashboardIdOrSlugGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardIdOrSlugGet200Response> apiV1DashboardIdOrSlugGetWithHttpInfo(String idOrSlug) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'idOrSlug' is set
        if (idOrSlug == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'idOrSlug' when calling apiV1DashboardIdOrSlugGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("id_or_slug", idOrSlug);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardIdOrSlugGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardIdOrSlugGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{id_or_slug}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Import dashboard(s) with associated charts/datasets/databases
     *
     * <p><b>200</b> - Dashboard import result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formData                     upload file (ZIP or JSON) (optional)
     * @param overwrite                    overwrite existing dashboards? (optional)
     * @param passwords                    JSON map of passwords for each featured database in the ZIP file. If the ZIP includes a database config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPasswords           JSON map of passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeyPasswords JSON map of private_key_passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeys         JSON map of private_keys for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key\\\&quot;}&#x60;. (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DashboardImportPost(File formData, Boolean overwrite, String passwords, String sshTunnelPasswords, String sshTunnelPrivateKeyPasswords, String sshTunnelPrivateKeys) throws RestClientException {
        return apiV1DashboardImportPostWithHttpInfo(formData, overwrite, passwords, sshTunnelPasswords, sshTunnelPrivateKeyPasswords, sshTunnelPrivateKeys).getBody();
    }

    /**
     * Import dashboard(s) with associated charts/datasets/databases
     *
     * <p><b>200</b> - Dashboard import result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param formData                     upload file (ZIP or JSON) (optional)
     * @param overwrite                    overwrite existing dashboards? (optional)
     * @param passwords                    JSON map of passwords for each featured database in the ZIP file. If the ZIP includes a database config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPasswords           JSON map of passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeyPasswords JSON map of private_key_passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeys         JSON map of private_keys for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key\\\&quot;}&#x60;. (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DashboardImportPostWithHttpInfo(File formData, Boolean overwrite, String passwords, String sshTunnelPasswords, String sshTunnelPrivateKeyPasswords, String sshTunnelPrivateKeys) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (formData != null)
            localVarFormParams.add("formData", new FileSystemResource(formData));
        if (overwrite != null)
            localVarFormParams.add("overwrite", overwrite);
        if (passwords != null)
            localVarFormParams.add("passwords", passwords);
        if (sshTunnelPasswords != null)
            localVarFormParams.add("ssh_tunnel_passwords", sshTunnelPasswords);
        if (sshTunnelPrivateKeyPasswords != null)
            localVarFormParams.add("ssh_tunnel_private_key_passwords", sshTunnelPrivateKeyPasswords);
        if (sshTunnelPrivateKeys != null)
            localVarFormParams.add("ssh_tunnel_private_keys", sshTunnelPrivateKeys);

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/import/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AnnotationLayerInfoGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerInfoGet200Response apiV1DashboardInfoGet(GetInfoSchema q) throws RestClientException {
        return apiV1DashboardInfoGetWithHttpInfo(q).getBody();
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerInfoGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerInfoGet200Response> apiV1DashboardInfoGetWithHttpInfo(GetInfoSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/_info", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete a dashboard
     *
     * <p><b>200</b> - Dashboard deleted
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DashboardPkDelete(Integer pk) throws RestClientException {
        return apiV1DashboardPkDeleteWithHttpInfo(pk).getBody();
    }

    /**
     * Delete a dashboard
     *
     * <p><b>200</b> - Dashboard deleted
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DashboardPkDeleteWithHttpInfo(Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardPkDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{pk}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Remove the dashboard from the user favorite list
     *
     * <p><b>200</b> - Dashboard removed from favorites
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ApiV1ChartPkFavoritesPost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1ChartPkFavoritesPost200Response apiV1DashboardPkFavoritesDelete(Integer pk) throws RestClientException {
        return apiV1DashboardPkFavoritesDeleteWithHttpInfo(pk).getBody();
    }

    /**
     * Remove the dashboard from the user favorite list
     *
     * <p><b>200</b> - Dashboard removed from favorites
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ResponseEntity&lt;ApiV1ChartPkFavoritesPost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1ChartPkFavoritesPost200Response> apiV1DashboardPkFavoritesDeleteWithHttpInfo(Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardPkFavoritesDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1ChartPkFavoritesPost200Response> localReturnType = new ParameterizedTypeReference<ApiV1ChartPkFavoritesPost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{pk}/favorites/", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Mark the dashboard as favorite for the current user
     *
     * <p><b>200</b> - Dashboard added to favorites
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ApiV1ChartPkFavoritesPost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1ChartPkFavoritesPost200Response apiV1DashboardPkFavoritesPost(Integer pk) throws RestClientException {
        return apiV1DashboardPkFavoritesPostWithHttpInfo(pk).getBody();
    }

    /**
     * Mark the dashboard as favorite for the current user
     *
     * <p><b>200</b> - Dashboard added to favorites
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ResponseEntity&lt;ApiV1ChartPkFavoritesPost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1ChartPkFavoritesPost200Response> apiV1DashboardPkFavoritesPostWithHttpInfo(Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardPkFavoritesPost");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1ChartPkFavoritesPost200Response> localReturnType = new ParameterizedTypeReference<ApiV1ChartPkFavoritesPost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{pk}/favorites/", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Update a dashboard
     *
     * <p><b>200</b> - Dashboard changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                  (required)
     * @param dashboardRestApiPut Dashboard schema (required)
     * @return ApiV1DashboardPkPut200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardPkPut200Response apiV1DashboardPkPut(Integer pk, DashboardRestApiPut dashboardRestApiPut) throws RestClientException {
        return apiV1DashboardPkPutWithHttpInfo(pk, dashboardRestApiPut).getBody();
    }

    /**
     * Update a dashboard
     *
     * <p><b>200</b> - Dashboard changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                  (required)
     * @param dashboardRestApiPut Dashboard schema (required)
     * @return ResponseEntity&lt;ApiV1DashboardPkPut200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardPkPut200Response> apiV1DashboardPkPutWithHttpInfo(Integer pk, DashboardRestApiPut dashboardRestApiPut) throws RestClientException {
        Object localVarPostBody = dashboardRestApiPut;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardPkPut");
        }

        // verify the required parameter 'dashboardRestApiPut' is set
        if (dashboardRestApiPut == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'dashboardRestApiPut' when calling apiV1DashboardPkPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardPkPut200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardPkPut200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{pk}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get dashboard&#39;s thumbnail
     * Computes async or get already computed dashboard thumbnail from cache.
     * <p><b>200</b> - Dashboard thumbnail image
     * <p><b>202</b> - Thumbnail does not exist on cache, fired async to compute
     * <p><b>302</b> - Redirects to the current digest
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk     (required)
     * @param digest A hex digest that makes this dashboard unique (required)
     * @param q      (optional)
     * @return File
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public File apiV1DashboardPkThumbnailDigestGet(Integer pk, String digest, ThumbnailQuerySchema q) throws RestClientException {
        return apiV1DashboardPkThumbnailDigestGetWithHttpInfo(pk, digest, q).getBody();
    }

    /**
     * Get dashboard&#39;s thumbnail
     * Computes async or get already computed dashboard thumbnail from cache.
     * <p><b>200</b> - Dashboard thumbnail image
     * <p><b>202</b> - Thumbnail does not exist on cache, fired async to compute
     * <p><b>302</b> - Redirects to the current digest
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk     (required)
     * @param digest A hex digest that makes this dashboard unique (required)
     * @param q      (optional)
     * @return ResponseEntity&lt;File&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<File> apiV1DashboardPkThumbnailDigestGetWithHttpInfo(Integer pk, String digest, ThumbnailQuerySchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardPkThumbnailDigestGet");
        }

        // verify the required parameter 'digest' is set
        if (digest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'digest' when calling apiV1DashboardPkThumbnailDigestGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);
        uriVariables.put("digest", digest);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "image/*", "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<File> localReturnType = new ParameterizedTypeReference<File>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{pk}/thumbnail/{digest}/", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create a new dashboard
     *
     * <p><b>201</b> - Dashboard added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param dashboardRestApiPost Dashboard schema (required)
     * @return ApiV1DashboardPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardPost201Response apiV1DashboardPost(DashboardRestApiPost dashboardRestApiPost) throws RestClientException {
        return apiV1DashboardPostWithHttpInfo(dashboardRestApiPost).getBody();
    }

    /**
     * Create a new dashboard
     *
     * <p><b>201</b> - Dashboard added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param dashboardRestApiPost Dashboard schema (required)
     * @return ResponseEntity&lt;ApiV1DashboardPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardPost201Response> apiV1DashboardPostWithHttpInfo(DashboardRestApiPost dashboardRestApiPost) throws RestClientException {
        Object localVarPostBody = dashboardRestApiPost;

        // verify the required parameter 'dashboardRestApiPost' is set
        if (dashboardRestApiPost == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'dashboardRestApiPost' when calling apiV1DashboardPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get related fields data
     * Get a list of all possible owners for a dashboard.
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return RelatedResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RelatedResponseSchema apiV1DashboardRelatedColumnNameGet(String columnName, GetRelatedSchema q) throws RestClientException {
        return apiV1DashboardRelatedColumnNameGetWithHttpInfo(columnName, q).getBody();
    }

    /**
     * Get related fields data
     * Get a list of all possible owners for a dashboard.
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return ResponseEntity&lt;RelatedResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RelatedResponseSchema> apiV1DashboardRelatedColumnNameGetWithHttpInfo(String columnName, GetRelatedSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1DashboardRelatedColumnNameGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<RelatedResponseSchema> localReturnType = new ParameterizedTypeReference<RelatedResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/related/{column_name}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
