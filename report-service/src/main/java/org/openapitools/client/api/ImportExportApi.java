package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.ApiV1AdvancedDataTypeConvertGet400Response;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.FileSystemResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;

import java.io.File;
import java.util.Collections;
import java.util.List;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class ImportExportApi {
    private ApiClient apiClient;

    public ImportExportApi() {
        this(new ApiClient());
    }

    public ImportExportApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Export all assets
     * Gets a ZIP file with all the Superset assets (databases, datasets, charts, dashboards, saved queries) as YAML files.
     * <p><b>200</b> - ZIP file
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @return File
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public File apiV1AssetsExportGet() throws RestClientException {
        return apiV1AssetsExportGetWithHttpInfo().getBody();
    }

    /**
     * Export all assets
     * Gets a ZIP file with all the Superset assets (databases, datasets, charts, dashboards, saved queries) as YAML files.
     * <p><b>200</b> - ZIP file
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @return ResponseEntity&lt;File&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<File> apiV1AssetsExportGetWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/zip", "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<File> localReturnType = new ParameterizedTypeReference<File>() {
        };
        return apiClient.invokeAPI("/api/v1/assets/export/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Import multiple assets
     *
     * <p><b>200</b> - Assets import result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param bundle                       upload file (ZIP or JSON) (optional)
     * @param passwords                    JSON map of passwords for each featured database in the ZIP file. If the ZIP includes a database config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPasswords           JSON map of passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeyPasswords JSON map of private_key_passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeys         JSON map of private_keys for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key\\\&quot;}&#x60;. (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1AssetsImportPost(File bundle, String passwords, String sshTunnelPasswords, String sshTunnelPrivateKeyPasswords, String sshTunnelPrivateKeys) throws RestClientException {
        return apiV1AssetsImportPostWithHttpInfo(bundle, passwords, sshTunnelPasswords, sshTunnelPrivateKeyPasswords, sshTunnelPrivateKeys).getBody();
    }

    /**
     * Import multiple assets
     *
     * <p><b>200</b> - Assets import result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param bundle                       upload file (ZIP or JSON) (optional)
     * @param passwords                    JSON map of passwords for each featured database in the ZIP file. If the ZIP includes a database config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPasswords           JSON map of passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the password should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeyPasswords JSON map of private_key_passwords for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key_password\\\&quot;}&#x60;. (optional)
     * @param sshTunnelPrivateKeys         JSON map of private_keys for each ssh_tunnel associated to a featured database in the ZIP file. If the ZIP includes a ssh_tunnel config in the path &#x60;databases/MyDatabase.yaml&#x60;, the private_key should be provided in the following format: &#x60;{\\\&quot;databases/MyDatabase.yaml\\\&quot;: \\\&quot;my_private_key\\\&quot;}&#x60;. (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1AssetsImportPostWithHttpInfo(File bundle, String passwords, String sshTunnelPasswords, String sshTunnelPrivateKeyPasswords, String sshTunnelPrivateKeys) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        if (bundle != null)
            localVarFormParams.add("bundle", new FileSystemResource(bundle));
        if (passwords != null)
            localVarFormParams.add("passwords", passwords);
        if (sshTunnelPasswords != null)
            localVarFormParams.add("ssh_tunnel_passwords", sshTunnelPasswords);
        if (sshTunnelPrivateKeyPasswords != null)
            localVarFormParams.add("ssh_tunnel_private_key_passwords", sshTunnelPrivateKeyPasswords);
        if (sshTunnelPrivateKeys != null)
            localVarFormParams.add("ssh_tunnel_private_keys", sshTunnelPrivateKeys);

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "multipart/form-data"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/assets/import/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
