package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.ApiV1AvailableDomainsGet200Response;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.List;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class AvailableDomainsApi {
    private ApiClient apiClient;

    public AvailableDomainsApi() {
        this(new ApiClient());
    }

    public AvailableDomainsApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get all available domains
     *
     * <p><b>200</b> - a list of available domains
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     *
     * @return ApiV1AvailableDomainsGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AvailableDomainsGet200Response apiV1AvailableDomainsGet() throws RestClientException {
        return apiV1AvailableDomainsGetWithHttpInfo().getBody();
    }

    /**
     * Get all available domains
     *
     * <p><b>200</b> - a list of available domains
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     *
     * @return ResponseEntity&lt;ApiV1AvailableDomainsGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AvailableDomainsGet200Response> apiV1AvailableDomainsGetWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AvailableDomainsGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AvailableDomainsGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/available_domains/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
