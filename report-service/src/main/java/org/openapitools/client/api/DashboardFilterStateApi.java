package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class DashboardFilterStateApi {
    private ApiClient apiClient;

    public DashboardFilterStateApi() {
        this(new ApiClient());
    }

    public DashboardFilterStateApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Delete a dashboard&#39;s filter state value
     *
     * <p><b>200</b> - Deleted the stored value.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk  (required)
     * @param key The value key. (required)
     * @return ApiV1DashboardPkFilterStateKeyDelete200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardPkFilterStateKeyDelete200Response apiV1DashboardPkFilterStateKeyDelete(Integer pk, String key) throws RestClientException {
        return apiV1DashboardPkFilterStateKeyDeleteWithHttpInfo(pk, key).getBody();
    }

    /**
     * Delete a dashboard&#39;s filter state value
     *
     * <p><b>200</b> - Deleted the stored value.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk  (required)
     * @param key The value key. (required)
     * @return ResponseEntity&lt;ApiV1DashboardPkFilterStateKeyDelete200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardPkFilterStateKeyDelete200Response> apiV1DashboardPkFilterStateKeyDeleteWithHttpInfo(Integer pk, String key) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardPkFilterStateKeyDelete");
        }

        // verify the required parameter 'key' is set
        if (key == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'key' when calling apiV1DashboardPkFilterStateKeyDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);
        uriVariables.put("key", key);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardPkFilterStateKeyDelete200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardPkFilterStateKeyDelete200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{pk}/filter_state/{key}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a dashboard&#39;s filter state value
     *
     * <p><b>200</b> - Returns the stored value.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk  (required)
     * @param key (required)
     * @return ApiV1DashboardPkFilterStateKeyGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardPkFilterStateKeyGet200Response apiV1DashboardPkFilterStateKeyGet(Integer pk, String key) throws RestClientException {
        return apiV1DashboardPkFilterStateKeyGetWithHttpInfo(pk, key).getBody();
    }

    /**
     * Get a dashboard&#39;s filter state value
     *
     * <p><b>200</b> - Returns the stored value.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk  (required)
     * @param key (required)
     * @return ResponseEntity&lt;ApiV1DashboardPkFilterStateKeyGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardPkFilterStateKeyGet200Response> apiV1DashboardPkFilterStateKeyGetWithHttpInfo(Integer pk, String key) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardPkFilterStateKeyGet");
        }

        // verify the required parameter 'key' is set
        if (key == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'key' when calling apiV1DashboardPkFilterStateKeyGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);
        uriVariables.put("key", key);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardPkFilterStateKeyGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardPkFilterStateKeyGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{pk}/filter_state/{key}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Update a dashboard&#39;s filter state value
     *
     * <p><b>200</b> - The value was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                      (required)
     * @param key                     (required)
     * @param temporaryCachePutSchema (required)
     * @param tabId                   (optional)
     * @return ApiV1DashboardPkFilterStatePost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardPkFilterStatePost201Response apiV1DashboardPkFilterStateKeyPut(Integer pk, String key, TemporaryCachePutSchema temporaryCachePutSchema, Integer tabId) throws RestClientException {
        return apiV1DashboardPkFilterStateKeyPutWithHttpInfo(pk, key, temporaryCachePutSchema, tabId).getBody();
    }

    /**
     * Update a dashboard&#39;s filter state value
     *
     * <p><b>200</b> - The value was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                      (required)
     * @param key                     (required)
     * @param temporaryCachePutSchema (required)
     * @param tabId                   (optional)
     * @return ResponseEntity&lt;ApiV1DashboardPkFilterStatePost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardPkFilterStatePost201Response> apiV1DashboardPkFilterStateKeyPutWithHttpInfo(Integer pk, String key, TemporaryCachePutSchema temporaryCachePutSchema, Integer tabId) throws RestClientException {
        Object localVarPostBody = temporaryCachePutSchema;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardPkFilterStateKeyPut");
        }

        // verify the required parameter 'key' is set
        if (key == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'key' when calling apiV1DashboardPkFilterStateKeyPut");
        }

        // verify the required parameter 'temporaryCachePutSchema' is set
        if (temporaryCachePutSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'temporaryCachePutSchema' when calling apiV1DashboardPkFilterStateKeyPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);
        uriVariables.put("key", key);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "tab_id", tabId));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardPkFilterStatePost201Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardPkFilterStatePost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{pk}/filter_state/{key}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create a dashboard&#39;s filter state
     *
     * <p><b>201</b> - The value was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                       (required)
     * @param temporaryCachePostSchema (required)
     * @param tabId                    (optional)
     * @return ApiV1DashboardPkFilterStatePost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardPkFilterStatePost201Response apiV1DashboardPkFilterStatePost(Integer pk, TemporaryCachePostSchema temporaryCachePostSchema, Integer tabId) throws RestClientException {
        return apiV1DashboardPkFilterStatePostWithHttpInfo(pk, temporaryCachePostSchema, tabId).getBody();
    }

    /**
     * Create a dashboard&#39;s filter state
     *
     * <p><b>201</b> - The value was stored successfully.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk                       (required)
     * @param temporaryCachePostSchema (required)
     * @param tabId                    (optional)
     * @return ResponseEntity&lt;ApiV1DashboardPkFilterStatePost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardPkFilterStatePost201Response> apiV1DashboardPkFilterStatePostWithHttpInfo(Integer pk, TemporaryCachePostSchema temporaryCachePostSchema, Integer tabId) throws RestClientException {
        Object localVarPostBody = temporaryCachePostSchema;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardPkFilterStatePost");
        }

        // verify the required parameter 'temporaryCachePostSchema' is set
        if (temporaryCachePostSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'temporaryCachePostSchema' when calling apiV1DashboardPkFilterStatePost");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "tab_id", tabId));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardPkFilterStatePost201Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardPkFilterStatePost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{pk}/filter_state", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
