package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class FilterSetRestApiApi {
    private ApiClient apiClient;

    public FilterSetRestApiApi() {
        this(new ApiClient());
    }

    public FilterSetRestApiApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get a dashboard&#39;s list of filter sets
     *
     * <p><b>200</b> - FilterSets
     * <p><b>302</b> - Redirects to the current digest
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     *
     * @param dashboardId The id of the dashboard (required)
     * @return List&lt;ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public List<ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner> apiV1DashboardDashboardIdFiltersetsGet(Integer dashboardId) throws RestClientException {
        return apiV1DashboardDashboardIdFiltersetsGetWithHttpInfo(dashboardId).getBody();
    }

    /**
     * Get a dashboard&#39;s list of filter sets
     *
     * <p><b>200</b> - FilterSets
     * <p><b>302</b> - Redirects to the current digest
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     *
     * @param dashboardId The id of the dashboard (required)
     * @return ResponseEntity&lt;List&lt;ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner&gt;&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<List<ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner>> apiV1DashboardDashboardIdFiltersetsGetWithHttpInfo(Integer dashboardId) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'dashboardId' is set
        if (dashboardId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'dashboardId' when calling apiV1DashboardDashboardIdFiltersetsGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("dashboard_id", dashboardId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<List<ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner>> localReturnType = new ParameterizedTypeReference<List<ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner>>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{dashboard_id}/filtersets", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete a dashboard&#39;s filter set
     *
     * <p><b>200</b> - Filter set deleted
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param dashboardId (required)
     * @param pk          (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1DashboardDashboardIdFiltersetsPkDelete(Integer dashboardId, Integer pk) throws RestClientException {
        return apiV1DashboardDashboardIdFiltersetsPkDeleteWithHttpInfo(dashboardId, pk).getBody();
    }

    /**
     * Delete a dashboard&#39;s filter set
     *
     * <p><b>200</b> - Filter set deleted
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param dashboardId (required)
     * @param pk          (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1DashboardDashboardIdFiltersetsPkDeleteWithHttpInfo(Integer dashboardId, Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'dashboardId' is set
        if (dashboardId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'dashboardId' when calling apiV1DashboardDashboardIdFiltersetsPkDelete");
        }

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardDashboardIdFiltersetsPkDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("dashboard_id", dashboardId);
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{dashboard_id}/filtersets/{pk}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Update a dashboard&#39;s filter set
     *
     * <p><b>200</b> - Filter set changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param dashboardId         (required)
     * @param pk                  (required)
     * @param filterSetRestApiPut Filter set schema (required)
     * @return ApiV1DashboardDashboardIdFiltersetsPkPut200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardDashboardIdFiltersetsPkPut200Response apiV1DashboardDashboardIdFiltersetsPkPut(Integer dashboardId, Integer pk, FilterSetRestApiPut filterSetRestApiPut) throws RestClientException {
        return apiV1DashboardDashboardIdFiltersetsPkPutWithHttpInfo(dashboardId, pk, filterSetRestApiPut).getBody();
    }

    /**
     * Update a dashboard&#39;s filter set
     *
     * <p><b>200</b> - Filter set changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param dashboardId         (required)
     * @param pk                  (required)
     * @param filterSetRestApiPut Filter set schema (required)
     * @return ResponseEntity&lt;ApiV1DashboardDashboardIdFiltersetsPkPut200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardDashboardIdFiltersetsPkPut200Response> apiV1DashboardDashboardIdFiltersetsPkPutWithHttpInfo(Integer dashboardId, Integer pk, FilterSetRestApiPut filterSetRestApiPut) throws RestClientException {
        Object localVarPostBody = filterSetRestApiPut;

        // verify the required parameter 'dashboardId' is set
        if (dashboardId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'dashboardId' when calling apiV1DashboardDashboardIdFiltersetsPkPut");
        }

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1DashboardDashboardIdFiltersetsPkPut");
        }

        // verify the required parameter 'filterSetRestApiPut' is set
        if (filterSetRestApiPut == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'filterSetRestApiPut' when calling apiV1DashboardDashboardIdFiltersetsPkPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("dashboard_id", dashboardId);
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardDashboardIdFiltersetsPkPut200Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardDashboardIdFiltersetsPkPut200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{dashboard_id}/filtersets/{pk}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create a new dashboard&#39;s filter set
     *
     * <p><b>201</b> - Filter set added
     * <p><b>302</b> - Redirects to the current digest
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param dashboardId          The id of the dashboard (required)
     * @param filterSetRestApiPost Filter set schema (required)
     * @return ApiV1DashboardDashboardIdFiltersetsPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1DashboardDashboardIdFiltersetsPost201Response apiV1DashboardDashboardIdFiltersetsPost(Integer dashboardId, FilterSetRestApiPost filterSetRestApiPost) throws RestClientException {
        return apiV1DashboardDashboardIdFiltersetsPostWithHttpInfo(dashboardId, filterSetRestApiPost).getBody();
    }

    /**
     * Create a new dashboard&#39;s filter set
     *
     * <p><b>201</b> - Filter set added
     * <p><b>302</b> - Redirects to the current digest
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param dashboardId          The id of the dashboard (required)
     * @param filterSetRestApiPost Filter set schema (required)
     * @return ResponseEntity&lt;ApiV1DashboardDashboardIdFiltersetsPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1DashboardDashboardIdFiltersetsPost201Response> apiV1DashboardDashboardIdFiltersetsPostWithHttpInfo(Integer dashboardId, FilterSetRestApiPost filterSetRestApiPost) throws RestClientException {
        Object localVarPostBody = filterSetRestApiPost;

        // verify the required parameter 'dashboardId' is set
        if (dashboardId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'dashboardId' when calling apiV1DashboardDashboardIdFiltersetsPost");
        }

        // verify the required parameter 'filterSetRestApiPost' is set
        if (filterSetRestApiPost == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'filterSetRestApiPost' when calling apiV1DashboardDashboardIdFiltersetsPost");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("dashboard_id", dashboardId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1DashboardDashboardIdFiltersetsPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1DashboardDashboardIdFiltersetsPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/dashboard/{dashboard_id}/filtersets", HttpMethod.POST, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
