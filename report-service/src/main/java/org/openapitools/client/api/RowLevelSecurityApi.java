package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.*;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class RowLevelSecurityApi {
    private ApiClient apiClient;

    public RowLevelSecurityApi() {
        this(new ApiClient());
    }

    public RowLevelSecurityApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Bulk delete RLS rules
     *
     * <p><b>200</b> - RLS Rule bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1RowlevelsecurityDelete(List<Integer> q) throws RestClientException {
        return apiV1RowlevelsecurityDeleteWithHttpInfo(q).getBody();
    }

    /**
     * Bulk delete RLS rules
     *
     * <p><b>200</b> - RLS Rule bulk delete
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1RowlevelsecurityDeleteWithHttpInfo(List<Integer> q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(ApiClient.CollectionFormat.valueOf("csv".toUpperCase(Locale.ROOT)), "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/rowlevelsecurity/", HttpMethod.DELETE, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a list of RLS
     * Gets a list of RLS, use Rison or JSON query parameters for filtering, sorting, pagination and for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1RowlevelsecurityGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1RowlevelsecurityGet200Response apiV1RowlevelsecurityGet(GetListSchema q) throws RestClientException {
        return apiV1RowlevelsecurityGetWithHttpInfo(q).getBody();
    }

    /**
     * Get a list of RLS
     * Gets a list of RLS, use Rison or JSON query parameters for filtering, sorting, pagination and for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1RowlevelsecurityGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1RowlevelsecurityGet200Response> apiV1RowlevelsecurityGetWithHttpInfo(GetListSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1RowlevelsecurityGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1RowlevelsecurityGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/rowlevelsecurity/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1AnnotationLayerInfoGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AnnotationLayerInfoGet200Response apiV1RowlevelsecurityInfoGet(GetInfoSchema q) throws RestClientException {
        return apiV1RowlevelsecurityInfoGetWithHttpInfo(q).getBody();
    }

    /**
     * Get metadata information about this API resource
     * Get metadata information about this API resource
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1AnnotationLayerInfoGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AnnotationLayerInfoGet200Response> apiV1RowlevelsecurityInfoGetWithHttpInfo(GetInfoSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AnnotationLayerInfoGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/rowlevelsecurity/_info", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Delete an RLS
     *
     * <p><b>200</b> - Item deleted
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ApiV1AdvancedDataTypeConvertGet400Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeConvertGet400Response apiV1RowlevelsecurityPkDelete(Integer pk) throws RestClientException {
        return apiV1RowlevelsecurityPkDeleteWithHttpInfo(pk).getBody();
    }

    /**
     * Delete an RLS
     *
     * <p><b>200</b> - Item deleted
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeConvertGet400Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeConvertGet400Response> apiV1RowlevelsecurityPkDeleteWithHttpInfo(Integer pk) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1RowlevelsecurityPkDelete");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeConvertGet400Response>() {
        };
        return apiClient.invokeAPI("/api/v1/rowlevelsecurity/{pk}", HttpMethod.DELETE, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get an RLS
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ApiV1RowlevelsecurityPkGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1RowlevelsecurityPkGet200Response apiV1RowlevelsecurityPkGet(Integer pk, GetItemSchema q) throws RestClientException {
        return apiV1RowlevelsecurityPkGetWithHttpInfo(pk, q).getBody();
    }

    /**
     * Get an RLS
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ResponseEntity&lt;ApiV1RowlevelsecurityPkGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1RowlevelsecurityPkGet200Response> apiV1RowlevelsecurityPkGetWithHttpInfo(Integer pk, GetItemSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1RowlevelsecurityPkGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1RowlevelsecurityPkGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1RowlevelsecurityPkGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/rowlevelsecurity/{pk}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Update an RLS rule
     *
     * <p><b>200</b> - Rule changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk            The Rule pk (required)
     * @param rlSRestApiPut RLS schema (required)
     * @return ApiV1RowlevelsecurityPkPut200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1RowlevelsecurityPkPut200Response apiV1RowlevelsecurityPkPut(Integer pk, RLSRestApiPut rlSRestApiPut) throws RestClientException {
        return apiV1RowlevelsecurityPkPutWithHttpInfo(pk, rlSRestApiPut).getBody();
    }

    /**
     * Update an RLS rule
     *
     * <p><b>200</b> - Rule changed
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk            The Rule pk (required)
     * @param rlSRestApiPut RLS schema (required)
     * @return ResponseEntity&lt;ApiV1RowlevelsecurityPkPut200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1RowlevelsecurityPkPut200Response> apiV1RowlevelsecurityPkPutWithHttpInfo(Integer pk, RLSRestApiPut rlSRestApiPut) throws RestClientException {
        Object localVarPostBody = rlSRestApiPut;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1RowlevelsecurityPkPut");
        }

        // verify the required parameter 'rlSRestApiPut' is set
        if (rlSRestApiPut == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'rlSRestApiPut' when calling apiV1RowlevelsecurityPkPut");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1RowlevelsecurityPkPut200Response> localReturnType = new ParameterizedTypeReference<ApiV1RowlevelsecurityPkPut200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/rowlevelsecurity/{pk}", HttpMethod.PUT, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Create a new RLS rule
     *
     * <p><b>201</b> - RLS Rule added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param rlSRestApiPost RLS schema (required)
     * @return ApiV1RowlevelsecurityPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1RowlevelsecurityPost201Response apiV1RowlevelsecurityPost(RLSRestApiPost rlSRestApiPost) throws RestClientException {
        return apiV1RowlevelsecurityPostWithHttpInfo(rlSRestApiPost).getBody();
    }

    /**
     * Create a new RLS rule
     *
     * <p><b>201</b> - RLS Rule added
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param rlSRestApiPost RLS schema (required)
     * @return ResponseEntity&lt;ApiV1RowlevelsecurityPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1RowlevelsecurityPost201Response> apiV1RowlevelsecurityPostWithHttpInfo(RLSRestApiPost rlSRestApiPost) throws RestClientException {
        Object localVarPostBody = rlSRestApiPost;

        // verify the required parameter 'rlSRestApiPost' is set
        if (rlSRestApiPost == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'rlSRestApiPost' when calling apiV1RowlevelsecurityPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1RowlevelsecurityPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1RowlevelsecurityPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/rowlevelsecurity/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return RelatedResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RelatedResponseSchema apiV1RowlevelsecurityRelatedColumnNameGet(String columnName, GetRelatedSchema q) throws RestClientException {
        return apiV1RowlevelsecurityRelatedColumnNameGetWithHttpInfo(columnName, q).getBody();
    }

    /**
     * Get related fields data
     *
     * <p><b>200</b> - Related column data
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param columnName (required)
     * @param q          (optional)
     * @return ResponseEntity&lt;RelatedResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RelatedResponseSchema> apiV1RowlevelsecurityRelatedColumnNameGetWithHttpInfo(String columnName, GetRelatedSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'columnName' is set
        if (columnName == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'columnName' when calling apiV1RowlevelsecurityRelatedColumnNameGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("column_name", columnName);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<RelatedResponseSchema> localReturnType = new ParameterizedTypeReference<RelatedResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/rowlevelsecurity/related/{column_name}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
