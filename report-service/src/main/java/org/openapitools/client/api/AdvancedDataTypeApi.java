package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.AdvancedDataTypeConvertSchema;
import org.openapitools.client.model.AdvancedDataTypeSchema;
import org.openapitools.client.model.ApiV1AdvancedDataTypeTypesGet200Response;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.List;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class AdvancedDataTypeApi {
    private ApiClient apiClient;

    public AdvancedDataTypeApi() {
        this(new ApiClient());
    }

    public AdvancedDataTypeApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Return an AdvancedDataTypeResponse
     * Returns an AdvancedDataTypeResponse object populated with the passed in args.
     * <p><b>200</b> - AdvancedDataTypeResponse object has been returned.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return AdvancedDataTypeSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public AdvancedDataTypeSchema apiV1AdvancedDataTypeConvertGet(AdvancedDataTypeConvertSchema q) throws RestClientException {
        return apiV1AdvancedDataTypeConvertGetWithHttpInfo(q).getBody();
    }

    /**
     * Return an AdvancedDataTypeResponse
     * Returns an AdvancedDataTypeResponse object populated with the passed in args.
     * <p><b>200</b> - AdvancedDataTypeResponse object has been returned.
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;AdvancedDataTypeSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<AdvancedDataTypeSchema> apiV1AdvancedDataTypeConvertGetWithHttpInfo(AdvancedDataTypeConvertSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<AdvancedDataTypeSchema> localReturnType = new ParameterizedTypeReference<AdvancedDataTypeSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/advanced_data_type/convert", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Return a list of available advanced data types
     *
     * <p><b>200</b> - a successful return of the available advanced data types has taken place.
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @return ApiV1AdvancedDataTypeTypesGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1AdvancedDataTypeTypesGet200Response apiV1AdvancedDataTypeTypesGet() throws RestClientException {
        return apiV1AdvancedDataTypeTypesGetWithHttpInfo().getBody();
    }

    /**
     * Return a list of available advanced data types
     *
     * <p><b>200</b> - a successful return of the available advanced data types has taken place.
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @return ResponseEntity&lt;ApiV1AdvancedDataTypeTypesGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1AdvancedDataTypeTypesGet200Response> apiV1AdvancedDataTypeTypesGetWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1AdvancedDataTypeTypesGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1AdvancedDataTypeTypesGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/advanced_data_type/types", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
