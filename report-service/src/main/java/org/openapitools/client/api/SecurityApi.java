package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.List;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class SecurityApi {
    private ApiClient apiClient;

    public SecurityApi() {
        this(new ApiClient());
    }

    public SecurityApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get the CSRF token
     *
     * <p><b>200</b> - Result contains the CSRF token
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @return ApiV1QueryStopPost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1QueryStopPost200Response apiV1SecurityCsrfTokenGet() throws RestClientException {
        return apiV1SecurityCsrfTokenGetWithHttpInfo().getBody();
    }

    /**
     * Get the CSRF token
     *
     * <p><b>200</b> - Result contains the CSRF token
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @return ResponseEntity&lt;ApiV1QueryStopPost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1QueryStopPost200Response> apiV1SecurityCsrfTokenGetWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1QueryStopPost200Response> localReturnType = new ParameterizedTypeReference<ApiV1QueryStopPost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/security/csrf_token/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a guest token
     *
     * <p><b>200</b> - Result contains the guest token
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param guestTokenCreate Parameters for the guest token (required)
     * @return ApiV1SecurityGuestTokenPost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1SecurityGuestTokenPost200Response apiV1SecurityGuestTokenPost(GuestTokenCreate guestTokenCreate) throws RestClientException {
        return apiV1SecurityGuestTokenPostWithHttpInfo(guestTokenCreate).getBody();
    }

    /**
     * Get a guest token
     *
     * <p><b>200</b> - Result contains the guest token
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param guestTokenCreate Parameters for the guest token (required)
     * @return ResponseEntity&lt;ApiV1SecurityGuestTokenPost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1SecurityGuestTokenPost200Response> apiV1SecurityGuestTokenPostWithHttpInfo(GuestTokenCreate guestTokenCreate) throws RestClientException {
        Object localVarPostBody = guestTokenCreate;

        // verify the required parameter 'guestTokenCreate' is set
        if (guestTokenCreate == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'guestTokenCreate' when calling apiV1SecurityGuestTokenPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1SecurityGuestTokenPost200Response> localReturnType = new ParameterizedTypeReference<ApiV1SecurityGuestTokenPost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/security/guest_token/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Authenticate and get a JWT access and refresh token
     * <p><b>200</b> - Authentication Successful
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param apiV1SecurityLoginPostRequest (required)
     * @return ApiV1SecurityLoginPost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1SecurityLoginPost200Response apiV1SecurityLoginPost(ApiV1SecurityLoginPostRequest apiV1SecurityLoginPostRequest) throws RestClientException {
        return apiV1SecurityLoginPostWithHttpInfo(apiV1SecurityLoginPostRequest).getBody();
    }

    /**
     * Authenticate and get a JWT access and refresh token
     * <p><b>200</b> - Authentication Successful
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @param apiV1SecurityLoginPostRequest (required)
     * @return ResponseEntity&lt;ApiV1SecurityLoginPost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1SecurityLoginPost200Response> apiV1SecurityLoginPostWithHttpInfo(ApiV1SecurityLoginPostRequest apiV1SecurityLoginPostRequest) throws RestClientException {
        Object localVarPostBody = apiV1SecurityLoginPostRequest;

        // verify the required parameter 'apiV1SecurityLoginPostRequest' is set
        if (apiV1SecurityLoginPostRequest == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'apiV1SecurityLoginPostRequest' when calling apiV1SecurityLoginPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{};

        ParameterizedTypeReference<ApiV1SecurityLoginPost200Response> localReturnType = new ParameterizedTypeReference<ApiV1SecurityLoginPost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/security/login", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Use the refresh token to get a new JWT access token
     * <p><b>200</b> - Refresh Successful
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @return ApiV1SecurityRefreshPost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1SecurityRefreshPost200Response apiV1SecurityRefreshPost() throws RestClientException {
        return apiV1SecurityRefreshPostWithHttpInfo().getBody();
    }

    /**
     * Use the refresh token to get a new JWT access token
     * <p><b>200</b> - Refresh Successful
     * <p><b>401</b> - Unauthorized
     * <p><b>500</b> - Fatal error
     *
     * @return ResponseEntity&lt;ApiV1SecurityRefreshPost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1SecurityRefreshPost200Response> apiV1SecurityRefreshPostWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt_refresh"};

        ParameterizedTypeReference<ApiV1SecurityRefreshPost200Response> localReturnType = new ParameterizedTypeReference<ApiV1SecurityRefreshPost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/security/refresh", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
