package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.ApiV1MenuGet200Response;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.List;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class MenuApi {
    private ApiClient apiClient;

    public MenuApi() {
        this(new ApiClient());
    }

    public MenuApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get the menu data structure. Returns a forest like structure with the menu the user has access to
     * <p><b>200</b> - Get menu data
     * <p><b>401</b> - Unauthorized
     *
     * @return ApiV1MenuGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1MenuGet200Response apiV1MenuGet() throws RestClientException {
        return apiV1MenuGetWithHttpInfo().getBody();
    }

    /**
     * Get the menu data structure. Returns a forest like structure with the menu the user has access to
     * <p><b>200</b> - Get menu data
     * <p><b>401</b> - Unauthorized
     *
     * @return ResponseEntity&lt;ApiV1MenuGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1MenuGet200Response> apiV1MenuGetWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1MenuGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1MenuGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/menu/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
