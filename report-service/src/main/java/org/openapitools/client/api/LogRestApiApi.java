package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class LogRestApiApi {
    private ApiClient apiClient;

    public LogRestApiApi() {
        this(new ApiClient());
    }

    public LogRestApiApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Get a list of logs
     * Gets a list of logs, use Rison or JSON query parameters for filtering, sorting, pagination and  for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ApiV1LogGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1LogGet200Response apiV1LogGet(GetListSchema q) throws RestClientException {
        return apiV1LogGetWithHttpInfo(q).getBody();
    }

    /**
     * Get a list of logs
     * Gets a list of logs, use Rison or JSON query parameters for filtering, sorting, pagination and  for selecting specific columns and metadata.
     * <p><b>200</b> - Items from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;ApiV1LogGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1LogGet200Response> apiV1LogGetWithHttpInfo(GetListSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1LogGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1LogGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/log/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get a log detail information
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ApiV1LogPkGet200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1LogPkGet200Response apiV1LogPkGet(Integer pk, GetItemSchema q) throws RestClientException {
        return apiV1LogPkGetWithHttpInfo(pk, q).getBody();
    }

    /**
     * Get a log detail information
     * Get an item model
     * <p><b>200</b> - Item from Model
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>404</b> - Not found
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param pk (required)
     * @param q  (optional)
     * @return ResponseEntity&lt;ApiV1LogPkGet200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1LogPkGet200Response> apiV1LogPkGetWithHttpInfo(Integer pk, GetItemSchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'pk' is set
        if (pk == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'pk' when calling apiV1LogPkGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("pk", pk);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1LogPkGet200Response> localReturnType = new ParameterizedTypeReference<ApiV1LogPkGet200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/log/{pk}", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * <p><b>201</b> - Item inserted
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param logRestApiPost Model schema (required)
     * @return ApiV1LogPost201Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1LogPost201Response apiV1LogPost(LogRestApiPost logRestApiPost) throws RestClientException {
        return apiV1LogPostWithHttpInfo(logRestApiPost).getBody();
    }

    /**
     * <p><b>201</b> - Item inserted
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>422</b> - Could not process entity
     * <p><b>500</b> - Fatal error
     *
     * @param logRestApiPost Model schema (required)
     * @return ResponseEntity&lt;ApiV1LogPost201Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1LogPost201Response> apiV1LogPostWithHttpInfo(LogRestApiPost logRestApiPost) throws RestClientException {
        Object localVarPostBody = logRestApiPost;

        // verify the required parameter 'logRestApiPost' is set
        if (logRestApiPost == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'logRestApiPost' when calling apiV1LogPost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1LogPost201Response> localReturnType = new ParameterizedTypeReference<ApiV1LogPost201Response>() {
        };
        return apiClient.invokeAPI("/api/v1/log/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get recent activity data for a user
     *
     * <p><b>200</b> - A List of recent activity objects
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>500</b> - Fatal error
     *
     * @param userId The id of the user (required)
     * @param q      (optional)
     * @return RecentActivityResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public RecentActivityResponseSchema apiV1LogRecentActivityGet(Integer userId, GetRecentActivitySchema q) throws RestClientException {
        return apiV1LogRecentActivityGetWithHttpInfo(userId, q).getBody();
    }

    /**
     * Get recent activity data for a user
     *
     * <p><b>200</b> - A List of recent activity objects
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>500</b> - Fatal error
     *
     * @param userId The id of the user (required)
     * @param q      (optional)
     * @return ResponseEntity&lt;RecentActivityResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<RecentActivityResponseSchema> apiV1LogRecentActivityGetWithHttpInfo(Integer userId, GetRecentActivitySchema q) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'userId' is set
        if (userId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'userId' when calling apiV1LogRecentActivityGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("user_id", userId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<RecentActivityResponseSchema> localReturnType = new ParameterizedTypeReference<RecentActivityResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/log/recent_activity/", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
