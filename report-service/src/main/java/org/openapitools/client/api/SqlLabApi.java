package org.openapitools.client.api;

import org.openapitools.client.ApiClient;
import org.openapitools.client.model.*;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestClientException;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-01T17:57:03.240786+08:00[Asia/Shanghai]")
public class SqlLabApi {
    private ApiClient apiClient;

    public SqlLabApi() {
        this(new ApiClient());
    }

    public SqlLabApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /**
     * Estimate the SQL query execution cost
     *
     * <p><b>200</b> - Query estimation result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>500</b> - Fatal error
     *
     * @param estimateQueryCostSchema SQL query and params (required)
     * @return ApiV1ChartPkFavoritesPost200Response
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ApiV1ChartPkFavoritesPost200Response apiV1SqllabEstimatePost(EstimateQueryCostSchema estimateQueryCostSchema) throws RestClientException {
        return apiV1SqllabEstimatePostWithHttpInfo(estimateQueryCostSchema).getBody();
    }

    /**
     * Estimate the SQL query execution cost
     *
     * <p><b>200</b> - Query estimation result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>500</b> - Fatal error
     *
     * @param estimateQueryCostSchema SQL query and params (required)
     * @return ResponseEntity&lt;ApiV1ChartPkFavoritesPost200Response&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<ApiV1ChartPkFavoritesPost200Response> apiV1SqllabEstimatePostWithHttpInfo(EstimateQueryCostSchema estimateQueryCostSchema) throws RestClientException {
        Object localVarPostBody = estimateQueryCostSchema;

        // verify the required parameter 'estimateQueryCostSchema' is set
        if (estimateQueryCostSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'estimateQueryCostSchema' when calling apiV1SqllabEstimatePost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<ApiV1ChartPkFavoritesPost200Response> localReturnType = new ParameterizedTypeReference<ApiV1ChartPkFavoritesPost200Response>() {
        };
        return apiClient.invokeAPI("/api/v1/sqllab/estimate/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Execute a SQL query
     *
     * <p><b>200</b> - Query execution result
     * <p><b>202</b> - Query execution result, query still running
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param executePayloadSchema SQL query and params (required)
     * @return QueryExecutionResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public QueryExecutionResponseSchema apiV1SqllabExecutePost(ExecutePayloadSchema executePayloadSchema) throws RestClientException {
        return apiV1SqllabExecutePostWithHttpInfo(executePayloadSchema).getBody();
    }

    /**
     * Execute a SQL query
     *
     * <p><b>200</b> - Query execution result
     * <p><b>202</b> - Query execution result, query still running
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param executePayloadSchema SQL query and params (required)
     * @return ResponseEntity&lt;QueryExecutionResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<QueryExecutionResponseSchema> apiV1SqllabExecutePostWithHttpInfo(ExecutePayloadSchema executePayloadSchema) throws RestClientException {
        Object localVarPostBody = executePayloadSchema;

        // verify the required parameter 'executePayloadSchema' is set
        if (executePayloadSchema == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'executePayloadSchema' when calling apiV1SqllabExecutePost");
        }


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {
            "application/json"
        };
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<QueryExecutionResponseSchema> localReturnType = new ParameterizedTypeReference<QueryExecutionResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/sqllab/execute/", HttpMethod.POST, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Export the SQL query results to a CSV
     *
     * <p><b>200</b> - SQL query results
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param clientId The SQL query result identifier (required)
     * @return String
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public String apiV1SqllabExportClientIdGet(Integer clientId) throws RestClientException {
        return apiV1SqllabExportClientIdGetWithHttpInfo(clientId).getBody();
    }

    /**
     * Export the SQL query results to a CSV
     *
     * <p><b>200</b> - SQL query results
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>500</b> - Fatal error
     *
     * @param clientId The SQL query result identifier (required)
     * @return ResponseEntity&lt;String&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<String> apiV1SqllabExportClientIdGetWithHttpInfo(Integer clientId) throws RestClientException {
        Object localVarPostBody = null;

        // verify the required parameter 'clientId' is set
        if (clientId == null) {
            throw new HttpClientErrorException(HttpStatus.BAD_REQUEST, "Missing the required parameter 'clientId' when calling apiV1SqllabExportClientIdGet");
        }

        // create path and map variables
        final Map<String, Object> uriVariables = new HashMap<String, Object>();
        uriVariables.put("client_id", clientId);

        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "text/csv", "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<String> localReturnType = new ParameterizedTypeReference<String>() {
        };
        return apiClient.invokeAPI("/api/v1/sqllab/export/{client_id}/", HttpMethod.GET, uriVariables, localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get the bootstrap data for SqlLab page
     * Assembles SQLLab bootstrap data (active_tab, databases, queries, tab_state_ids) in a single endpoint. The data can be assembled from the current user&#39;s id.
     * <p><b>200</b> - Returns the initial bootstrap data for SqlLab
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>500</b> - Fatal error
     *
     * @return SQLLabBootstrapSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public SQLLabBootstrapSchema apiV1SqllabGet() throws RestClientException {
        return apiV1SqllabGetWithHttpInfo().getBody();
    }

    /**
     * Get the bootstrap data for SqlLab page
     * Assembles SQLLab bootstrap data (active_tab, databases, queries, tab_state_ids) in a single endpoint. The data can be assembled from the current user&#39;s id.
     * <p><b>200</b> - Returns the initial bootstrap data for SqlLab
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>500</b> - Fatal error
     *
     * @return ResponseEntity&lt;SQLLabBootstrapSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<SQLLabBootstrapSchema> apiV1SqllabGetWithHttpInfo() throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<SQLLabBootstrapSchema> localReturnType = new ParameterizedTypeReference<SQLLabBootstrapSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/sqllab/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }

    /**
     * Get the result of a SQL query execution
     *
     * <p><b>200</b> - SQL query execution result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>410</b> - Gone
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return QueryExecutionResponseSchema
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public QueryExecutionResponseSchema apiV1SqllabResultsGet(SqlLabGetResultsSchema q) throws RestClientException {
        return apiV1SqllabResultsGetWithHttpInfo(q).getBody();
    }

    /**
     * Get the result of a SQL query execution
     *
     * <p><b>200</b> - SQL query execution result
     * <p><b>400</b> - Bad request
     * <p><b>401</b> - Unauthorized
     * <p><b>403</b> - Forbidden
     * <p><b>404</b> - Not found
     * <p><b>410</b> - Gone
     * <p><b>500</b> - Fatal error
     *
     * @param q (optional)
     * @return ResponseEntity&lt;QueryExecutionResponseSchema&gt;
     * @throws RestClientException if an error occurs while attempting to invoke the API
     */
    public ResponseEntity<QueryExecutionResponseSchema> apiV1SqllabResultsGetWithHttpInfo(SqlLabGetResultsSchema q) throws RestClientException {
        Object localVarPostBody = null;


        final MultiValueMap<String, String> localVarQueryParams = new LinkedMultiValueMap<String, String>();
        final HttpHeaders localVarHeaderParams = new HttpHeaders();
        final MultiValueMap<String, String> localVarCookieParams = new LinkedMultiValueMap<String, String>();
        final MultiValueMap<String, Object> localVarFormParams = new LinkedMultiValueMap<String, Object>();

        localVarQueryParams.putAll(apiClient.parameterToMultiValueMap(null, "q", q));


        final String[] localVarAccepts = {
            "application/json"
        };
        final List<MediaType> localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);
        final String[] localVarContentTypes = {};
        final MediaType localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);

        String[] localVarAuthNames = new String[]{"jwt"};

        ParameterizedTypeReference<QueryExecutionResponseSchema> localReturnType = new ParameterizedTypeReference<QueryExecutionResponseSchema>() {
        };
        return apiClient.invokeAPI("/api/v1/sqllab/results/", HttpMethod.GET, Collections.<String, Object>emptyMap(), localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, localReturnType);
    }
}
