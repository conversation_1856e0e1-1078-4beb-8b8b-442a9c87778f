/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * EstimateQueryCostSchema
 */
@JsonPropertyOrder({
    EstimateQueryCostSchema.JSON_PROPERTY_DATABASE_ID,
    EstimateQueryCostSchema.JSON_PROPERTY_SCHEMA,
    EstimateQueryCostSchema.JSON_PROPERTY_SQL,
    EstimateQueryCostSchema.JSON_PROPERTY_TEMPLATE_PARAMS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class EstimateQueryCostSchema {
    public static final String JSON_PROPERTY_DATABASE_ID = "database_id";
    private Integer databaseId;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TEMPLATE_PARAMS = "template_params";
    private Object templateParams;

    public EstimateQueryCostSchema() {
    }

    public EstimateQueryCostSchema databaseId(Integer databaseId) {

        this.databaseId = databaseId;
        return this;
    }

    /**
     * The database id
     *
     * @return databaseId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getDatabaseId() {
        return databaseId;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabaseId(Integer databaseId) {
        this.databaseId = databaseId;
    }


    public EstimateQueryCostSchema schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * The database schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public EstimateQueryCostSchema sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * The SQL query to estimate
     *
     * @return sql
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public EstimateQueryCostSchema templateParams(Object templateParams) {

        this.templateParams = templateParams;
        return this;
    }

    /**
     * The SQL query template params
     *
     * @return templateParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getTemplateParams() {
        return templateParams;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateParams(Object templateParams) {
        this.templateParams = templateParams;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EstimateQueryCostSchema estimateQueryCostSchema = (EstimateQueryCostSchema) o;
        return Objects.equals(this.databaseId, estimateQueryCostSchema.databaseId) &&
            Objects.equals(this.schema, estimateQueryCostSchema.schema) &&
            Objects.equals(this.sql, estimateQueryCostSchema.sql) &&
            Objects.equals(this.templateParams, estimateQueryCostSchema.templateParams);
    }

    @Override
    public int hashCode() {
        return Objects.hash(databaseId, schema, sql, templateParams);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class EstimateQueryCostSchema {\n");
        sb.append("    databaseId: ").append(toIndentedString(databaseId)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    templateParams: ").append(toIndentedString(templateParams)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

