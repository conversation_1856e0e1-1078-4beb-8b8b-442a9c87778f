/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * RecentActivitySchema
 */
@JsonPropertyOrder({
    RecentActivitySchema.JSON_PROPERTY_ACTION,
    RecentActivitySchema.JSON_PROPERTY_ITEM_TITLE,
    RecentActivitySchema.JSON_PROPERTY_ITEM_TYPE,
    RecentActivitySchema.JSON_PROPERTY_ITEM_URL,
    RecentActivitySchema.JSON_PROPERTY_TIME,
    RecentActivitySchema.JSON_PROPERTY_TIME_DELTA_HUMANIZED
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class RecentActivitySchema {
    public static final String JSON_PROPERTY_ACTION = "action";
    private String action;

    public static final String JSON_PROPERTY_ITEM_TITLE = "item_title";
    private String itemTitle;

    public static final String JSON_PROPERTY_ITEM_TYPE = "item_type";
    private String itemType;

    public static final String JSON_PROPERTY_ITEM_URL = "item_url";
    private String itemUrl;

    public static final String JSON_PROPERTY_TIME = "time";
    private BigDecimal time;

    public static final String JSON_PROPERTY_TIME_DELTA_HUMANIZED = "time_delta_humanized";
    private String timeDeltaHumanized;

    public RecentActivitySchema() {
    }

    public RecentActivitySchema action(String action) {

        this.action = action;
        return this;
    }

    /**
     * Action taken describing type of activity
     *
     * @return action
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getAction() {
        return action;
    }


    @JsonProperty(JSON_PROPERTY_ACTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAction(String action) {
        this.action = action;
    }


    public RecentActivitySchema itemTitle(String itemTitle) {

        this.itemTitle = itemTitle;
        return this;
    }

    /**
     * Title of item
     *
     * @return itemTitle
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ITEM_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getItemTitle() {
        return itemTitle;
    }


    @JsonProperty(JSON_PROPERTY_ITEM_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setItemTitle(String itemTitle) {
        this.itemTitle = itemTitle;
    }


    public RecentActivitySchema itemType(String itemType) {

        this.itemType = itemType;
        return this;
    }

    /**
     * Type of item, e.g. slice or dashboard
     *
     * @return itemType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ITEM_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getItemType() {
        return itemType;
    }


    @JsonProperty(JSON_PROPERTY_ITEM_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setItemType(String itemType) {
        this.itemType = itemType;
    }


    public RecentActivitySchema itemUrl(String itemUrl) {

        this.itemUrl = itemUrl;
        return this;
    }

    /**
     * URL to item
     *
     * @return itemUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ITEM_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getItemUrl() {
        return itemUrl;
    }


    @JsonProperty(JSON_PROPERTY_ITEM_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setItemUrl(String itemUrl) {
        this.itemUrl = itemUrl;
    }


    public RecentActivitySchema time(BigDecimal time) {

        this.time = time;
        return this;
    }

    /**
     * Time of activity, in epoch milliseconds
     *
     * @return time
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getTime() {
        return time;
    }


    @JsonProperty(JSON_PROPERTY_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTime(BigDecimal time) {
        this.time = time;
    }


    public RecentActivitySchema timeDeltaHumanized(String timeDeltaHumanized) {

        this.timeDeltaHumanized = timeDeltaHumanized;
        return this;
    }

    /**
     * Human-readable description of how long ago activity took place.
     *
     * @return timeDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTimeDeltaHumanized() {
        return timeDeltaHumanized;
    }


    @JsonProperty(JSON_PROPERTY_TIME_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeDeltaHumanized(String timeDeltaHumanized) {
        this.timeDeltaHumanized = timeDeltaHumanized;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RecentActivitySchema recentActivitySchema = (RecentActivitySchema) o;
        return Objects.equals(this.action, recentActivitySchema.action) &&
            Objects.equals(this.itemTitle, recentActivitySchema.itemTitle) &&
            Objects.equals(this.itemType, recentActivitySchema.itemType) &&
            Objects.equals(this.itemUrl, recentActivitySchema.itemUrl) &&
            Objects.equals(this.time, recentActivitySchema.time) &&
            Objects.equals(this.timeDeltaHumanized, recentActivitySchema.timeDeltaHumanized);
    }

    @Override
    public int hashCode() {
        return Objects.hash(action, itemTitle, itemType, itemUrl, time, timeDeltaHumanized);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RecentActivitySchema {\n");
        sb.append("    action: ").append(toIndentedString(action)).append("\n");
        sb.append("    itemTitle: ").append(toIndentedString(itemTitle)).append("\n");
        sb.append("    itemType: ").append(toIndentedString(itemType)).append("\n");
        sb.append("    itemUrl: ").append(toIndentedString(itemUrl)).append("\n");
        sb.append("    time: ").append(toIndentedString(time)).append("\n");
        sb.append("    timeDeltaHumanized: ").append(toIndentedString(timeDeltaHumanized)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

