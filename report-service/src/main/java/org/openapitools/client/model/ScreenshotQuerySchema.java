/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ScreenshotQuerySchema
 */
@JsonPropertyOrder({
    ScreenshotQuerySchema.JSON_PROPERTY_FORCE,
    ScreenshotQuerySchema.JSON_PROPERTY_THUMB_SIZE,
    ScreenshotQuerySchema.JSON_PROPERTY_WINDOW_SIZE
})
@JsonTypeName("screenshot_query_schema")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ScreenshotQuerySchema {
    public static final String JSON_PROPERTY_FORCE = "force";
    private Boolean force;

    public static final String JSON_PROPERTY_THUMB_SIZE = "thumb_size";
    private List<Integer> thumbSize;

    public static final String JSON_PROPERTY_WINDOW_SIZE = "window_size";
    private List<Integer> windowSize;

    public ScreenshotQuerySchema() {
    }

    public ScreenshotQuerySchema force(Boolean force) {

        this.force = force;
        return this;
    }

    /**
     * Get force
     *
     * @return force
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getForce() {
        return force;
    }


    @JsonProperty(JSON_PROPERTY_FORCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setForce(Boolean force) {
        this.force = force;
    }


    public ScreenshotQuerySchema thumbSize(List<Integer> thumbSize) {

        this.thumbSize = thumbSize;
        return this;
    }

    public ScreenshotQuerySchema addThumbSizeItem(Integer thumbSizeItem) {
        if (this.thumbSize == null) {
            this.thumbSize = new ArrayList<>();
        }
        this.thumbSize.add(thumbSizeItem);
        return this;
    }

    /**
     * Get thumbSize
     *
     * @return thumbSize
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_THUMB_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getThumbSize() {
        return thumbSize;
    }


    @JsonProperty(JSON_PROPERTY_THUMB_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setThumbSize(List<Integer> thumbSize) {
        this.thumbSize = thumbSize;
    }


    public ScreenshotQuerySchema windowSize(List<Integer> windowSize) {

        this.windowSize = windowSize;
        return this;
    }

    public ScreenshotQuerySchema addWindowSizeItem(Integer windowSizeItem) {
        if (this.windowSize == null) {
            this.windowSize = new ArrayList<>();
        }
        this.windowSize.add(windowSizeItem);
        return this;
    }

    /**
     * Get windowSize
     *
     * @return windowSize
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_WINDOW_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getWindowSize() {
        return windowSize;
    }


    @JsonProperty(JSON_PROPERTY_WINDOW_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setWindowSize(List<Integer> windowSize) {
        this.windowSize = windowSize;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ScreenshotQuerySchema screenshotQuerySchema = (ScreenshotQuerySchema) o;
        return Objects.equals(this.force, screenshotQuerySchema.force) &&
            Objects.equals(this.thumbSize, screenshotQuerySchema.thumbSize) &&
            Objects.equals(this.windowSize, screenshotQuerySchema.windowSize);
    }

    @Override
    public int hashCode() {
        return Objects.hash(force, thumbSize, windowSize);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ScreenshotQuerySchema {\n");
        sb.append("    force: ").append(toIndentedString(force)).append("\n");
        sb.append("    thumbSize: ").append(toIndentedString(thumbSize)).append("\n");
        sb.append("    windowSize: ").append(toIndentedString(windowSize)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

