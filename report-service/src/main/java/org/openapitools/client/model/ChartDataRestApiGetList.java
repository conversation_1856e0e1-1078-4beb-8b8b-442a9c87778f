/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * ChartDataRestApiGetList
 */
@JsonPropertyOrder({
    ChartDataRestApiGetList.JSON_PROPERTY_CACHE_TIMEOUT,
    ChartDataRestApiGetList.JSON_PROPERTY_CERTIFICATION_DETAILS,
    ChartDataRestApiGetList.JSON_PROPERTY_CERTIFIED_BY,
    ChartDataRestApiGetList.JSON_PROPERTY_CHANGED_BY,
    ChartDataRestApiGetList.JSON_PROPERTY_CHANGED_BY_NAME,
    ChartDataRestApiGetList.J<PERSON><PERSON>_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    ChartDataRestApiGetList.JSON_PROPERTY_CHANGED_ON_DTTM,
    ChartDataRestApiGetList.JSON_PROPERTY_CHANGED_ON_UTC,
    ChartDataRestApiGetList.JSON_PROPERTY_CREATED_BY,
    ChartDataRestApiGetList.JSON_PROPERTY_CREATED_BY_NAME,
    ChartDataRestApiGetList.JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED,
    ChartDataRestApiGetList.JSON_PROPERTY_DASHBOARDS,
    ChartDataRestApiGetList.JSON_PROPERTY_DATASOURCE_ID,
    ChartDataRestApiGetList.JSON_PROPERTY_DATASOURCE_NAME_TEXT,
    ChartDataRestApiGetList.JSON_PROPERTY_DATASOURCE_TYPE,
    ChartDataRestApiGetList.JSON_PROPERTY_DATASOURCE_URL,
    ChartDataRestApiGetList.JSON_PROPERTY_DESCRIPTION,
    ChartDataRestApiGetList.JSON_PROPERTY_DESCRIPTION_MARKEDDOWN,
    ChartDataRestApiGetList.JSON_PROPERTY_EDIT_URL,
    ChartDataRestApiGetList.JSON_PROPERTY_FORM_DATA,
    ChartDataRestApiGetList.JSON_PROPERTY_ID,
    ChartDataRestApiGetList.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    ChartDataRestApiGetList.JSON_PROPERTY_LAST_SAVED_AT,
    ChartDataRestApiGetList.JSON_PROPERTY_LAST_SAVED_BY,
    ChartDataRestApiGetList.JSON_PROPERTY_OWNERS,
    ChartDataRestApiGetList.JSON_PROPERTY_PARAMS,
    ChartDataRestApiGetList.JSON_PROPERTY_SLICE_NAME,
    ChartDataRestApiGetList.JSON_PROPERTY_SLICE_URL,
    ChartDataRestApiGetList.JSON_PROPERTY_TABLE,
    ChartDataRestApiGetList.JSON_PROPERTY_TAGS,
    ChartDataRestApiGetList.JSON_PROPERTY_THUMBNAIL_URL,
    ChartDataRestApiGetList.JSON_PROPERTY_URL,
    ChartDataRestApiGetList.JSON_PROPERTY_VIZ_TYPE
})
@JsonTypeName("ChartDataRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataRestApiGetList {
    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private ChartDataRestApiGetListUser1 changedBy;

    public static final String JSON_PROPERTY_CHANGED_BY_NAME = "changed_by_name";
    private Object changedByName = null;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CHANGED_ON_DTTM = "changed_on_dttm";
    private Object changedOnDttm = null;

    public static final String JSON_PROPERTY_CHANGED_ON_UTC = "changed_on_utc";
    private Object changedOnUtc = null;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private ChartDataRestApiGetListUser2 createdBy;

    public static final String JSON_PROPERTY_CREATED_BY_NAME = "created_by_name";
    private Object createdByName = null;

    public static final String JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED = "created_on_delta_humanized";
    private Object createdOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_DASHBOARDS = "dashboards";
    private ChartDataRestApiGetListDashboard dashboards;

    public static final String JSON_PROPERTY_DATASOURCE_ID = "datasource_id";
    private Integer datasourceId;

    public static final String JSON_PROPERTY_DATASOURCE_NAME_TEXT = "datasource_name_text";
    private Object datasourceNameText = null;

    public static final String JSON_PROPERTY_DATASOURCE_TYPE = "datasource_type";
    private String datasourceType;

    public static final String JSON_PROPERTY_DATASOURCE_URL = "datasource_url";
    private Object datasourceUrl = null;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_DESCRIPTION_MARKEDDOWN = "description_markeddown";
    private Object descriptionMarkeddown = null;

    public static final String JSON_PROPERTY_EDIT_URL = "edit_url";
    private Object editUrl = null;

    public static final String JSON_PROPERTY_FORM_DATA = "form_data";
    private Object formData = null;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_LAST_SAVED_AT = "last_saved_at";
    private OffsetDateTime lastSavedAt;

    public static final String JSON_PROPERTY_LAST_SAVED_BY = "last_saved_by";
    private ChartDataRestApiGetListUser lastSavedBy;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private ChartDataRestApiGetListUser3 owners;

    public static final String JSON_PROPERTY_PARAMS = "params";
    private String params;

    public static final String JSON_PROPERTY_SLICE_NAME = "slice_name";
    private String sliceName;

    public static final String JSON_PROPERTY_SLICE_URL = "slice_url";
    private Object sliceUrl = null;

    public static final String JSON_PROPERTY_TABLE = "table";
    private ChartDataRestApiGetListSqlaTable table;

    public static final String JSON_PROPERTY_TAGS = "tags";
    private ChartDataRestApiGetListTag tags;

    public static final String JSON_PROPERTY_THUMBNAIL_URL = "thumbnail_url";
    private Object thumbnailUrl = null;

    public static final String JSON_PROPERTY_URL = "url";
    private Object url = null;

    public static final String JSON_PROPERTY_VIZ_TYPE = "viz_type";
    private String vizType;

    public ChartDataRestApiGetList() {
    }

    @JsonCreator
    public ChartDataRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_BY_NAME) Object changedByName,
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DTTM) Object changedOnDttm,
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_UTC) Object changedOnUtc,
        @JsonProperty(JSON_PROPERTY_CREATED_BY_NAME) Object createdByName,
        @JsonProperty(JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED) Object createdOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME_TEXT) Object datasourceNameText,
        @JsonProperty(JSON_PROPERTY_DATASOURCE_URL) Object datasourceUrl,
        @JsonProperty(JSON_PROPERTY_DESCRIPTION_MARKEDDOWN) Object descriptionMarkeddown,
        @JsonProperty(JSON_PROPERTY_EDIT_URL) Object editUrl,
        @JsonProperty(JSON_PROPERTY_FORM_DATA) Object formData,
        @JsonProperty(JSON_PROPERTY_SLICE_URL) Object sliceUrl,
        @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL) Object thumbnailUrl,
        @JsonProperty(JSON_PROPERTY_URL) Object url
    ) {
        this();
        this.changedByName = changedByName;
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.changedOnDttm = changedOnDttm;
        this.changedOnUtc = changedOnUtc;
        this.createdByName = createdByName;
        this.createdOnDeltaHumanized = createdOnDeltaHumanized;
        this.datasourceNameText = datasourceNameText;
        this.datasourceUrl = datasourceUrl;
        this.descriptionMarkeddown = descriptionMarkeddown;
        this.editUrl = editUrl;
        this.formData = formData;
        this.sliceUrl = sliceUrl;
        this.thumbnailUrl = thumbnailUrl;
        this.url = url;
    }

    public ChartDataRestApiGetList cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Get cacheTimeout
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public ChartDataRestApiGetList certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Get certificationDetails
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public ChartDataRestApiGetList certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Get certifiedBy
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    public ChartDataRestApiGetList changedBy(ChartDataRestApiGetListUser1 changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetListUser1 getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(ChartDataRestApiGetListUser1 changedBy) {
        this.changedBy = changedBy;
    }


    /**
     * Get changedByName
     *
     * @return changedByName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedByName() {
        return changedByName;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    /**
     * Get changedOnDttm
     *
     * @return changedOnDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDttm() {
        return changedOnDttm;
    }


    /**
     * Get changedOnUtc
     *
     * @return changedOnUtc
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_UTC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnUtc() {
        return changedOnUtc;
    }


    public ChartDataRestApiGetList createdBy(ChartDataRestApiGetListUser2 createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetListUser2 getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(ChartDataRestApiGetListUser2 createdBy) {
        this.createdBy = createdBy;
    }


    /**
     * Get createdByName
     *
     * @return createdByName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getCreatedByName() {
        return createdByName;
    }


    /**
     * Get createdOnDeltaHumanized
     *
     * @return createdOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getCreatedOnDeltaHumanized() {
        return createdOnDeltaHumanized;
    }


    public ChartDataRestApiGetList dashboards(ChartDataRestApiGetListDashboard dashboards) {

        this.dashboards = dashboards;
        return this;
    }

    /**
     * Get dashboards
     *
     * @return dashboards
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetListDashboard getDashboards() {
        return dashboards;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboards(ChartDataRestApiGetListDashboard dashboards) {
        this.dashboards = dashboards;
    }


    public ChartDataRestApiGetList datasourceId(Integer datasourceId) {

        this.datasourceId = datasourceId;
        return this;
    }

    /**
     * Get datasourceId
     *
     * @return datasourceId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDatasourceId() {
        return datasourceId;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceId(Integer datasourceId) {
        this.datasourceId = datasourceId;
    }


    /**
     * Get datasourceNameText
     *
     * @return datasourceNameText
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME_TEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDatasourceNameText() {
        return datasourceNameText;
    }


    public ChartDataRestApiGetList datasourceType(String datasourceType) {

        this.datasourceType = datasourceType;
        return this;
    }

    /**
     * Get datasourceType
     *
     * @return datasourceType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatasourceType() {
        return datasourceType;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceType(String datasourceType) {
        this.datasourceType = datasourceType;
    }


    /**
     * Get datasourceUrl
     *
     * @return datasourceUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDatasourceUrl() {
        return datasourceUrl;
    }


    public ChartDataRestApiGetList description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    /**
     * Get descriptionMarkeddown
     *
     * @return descriptionMarkeddown
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION_MARKEDDOWN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDescriptionMarkeddown() {
        return descriptionMarkeddown;
    }


    /**
     * Get editUrl
     *
     * @return editUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EDIT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getEditUrl() {
        return editUrl;
    }


    /**
     * Get formData
     *
     * @return formData
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getFormData() {
        return formData;
    }


    public ChartDataRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ChartDataRestApiGetList isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public ChartDataRestApiGetList lastSavedAt(OffsetDateTime lastSavedAt) {

        this.lastSavedAt = lastSavedAt;
        return this;
    }

    /**
     * Get lastSavedAt
     *
     * @return lastSavedAt
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_SAVED_AT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getLastSavedAt() {
        return lastSavedAt;
    }


    @JsonProperty(JSON_PROPERTY_LAST_SAVED_AT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastSavedAt(OffsetDateTime lastSavedAt) {
        this.lastSavedAt = lastSavedAt;
    }


    public ChartDataRestApiGetList lastSavedBy(ChartDataRestApiGetListUser lastSavedBy) {

        this.lastSavedBy = lastSavedBy;
        return this;
    }

    /**
     * Get lastSavedBy
     *
     * @return lastSavedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_SAVED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetListUser getLastSavedBy() {
        return lastSavedBy;
    }


    @JsonProperty(JSON_PROPERTY_LAST_SAVED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastSavedBy(ChartDataRestApiGetListUser lastSavedBy) {
        this.lastSavedBy = lastSavedBy;
    }


    public ChartDataRestApiGetList owners(ChartDataRestApiGetListUser3 owners) {

        this.owners = owners;
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetListUser3 getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(ChartDataRestApiGetListUser3 owners) {
        this.owners = owners;
    }


    public ChartDataRestApiGetList params(String params) {

        this.params = params;
        return this;
    }

    /**
     * Get params
     *
     * @return params
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getParams() {
        return params;
    }


    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setParams(String params) {
        this.params = params;
    }


    public ChartDataRestApiGetList sliceName(String sliceName) {

        this.sliceName = sliceName;
        return this;
    }

    /**
     * Get sliceName
     *
     * @return sliceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSliceName() {
        return sliceName;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceName(String sliceName) {
        this.sliceName = sliceName;
    }


    /**
     * Get sliceUrl
     *
     * @return sliceUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getSliceUrl() {
        return sliceUrl;
    }


    public ChartDataRestApiGetList table(ChartDataRestApiGetListSqlaTable table) {

        this.table = table;
        return this;
    }

    /**
     * Get table
     *
     * @return table
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetListSqlaTable getTable() {
        return table;
    }


    @JsonProperty(JSON_PROPERTY_TABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTable(ChartDataRestApiGetListSqlaTable table) {
        this.table = table;
    }


    public ChartDataRestApiGetList tags(ChartDataRestApiGetListTag tags) {

        this.tags = tags;
        return this;
    }

    /**
     * Get tags
     *
     * @return tags
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetListTag getTags() {
        return tags;
    }


    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTags(ChartDataRestApiGetListTag tags) {
        this.tags = tags;
    }


    /**
     * Get thumbnailUrl
     *
     * @return thumbnailUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getThumbnailUrl() {
        return thumbnailUrl;
    }


    /**
     * Get url
     *
     * @return url
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getUrl() {
        return url;
    }


    public ChartDataRestApiGetList vizType(String vizType) {

        this.vizType = vizType;
        return this;
    }

    /**
     * Get vizType
     *
     * @return vizType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVizType() {
        return vizType;
    }


    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVizType(String vizType) {
        this.vizType = vizType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataRestApiGetList chartDataRestApiGetList = (ChartDataRestApiGetList) o;
        return Objects.equals(this.cacheTimeout, chartDataRestApiGetList.cacheTimeout) &&
            Objects.equals(this.certificationDetails, chartDataRestApiGetList.certificationDetails) &&
            Objects.equals(this.certifiedBy, chartDataRestApiGetList.certifiedBy) &&
            Objects.equals(this.changedBy, chartDataRestApiGetList.changedBy) &&
            Objects.equals(this.changedByName, chartDataRestApiGetList.changedByName) &&
            Objects.equals(this.changedOnDeltaHumanized, chartDataRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.changedOnDttm, chartDataRestApiGetList.changedOnDttm) &&
            Objects.equals(this.changedOnUtc, chartDataRestApiGetList.changedOnUtc) &&
            Objects.equals(this.createdBy, chartDataRestApiGetList.createdBy) &&
            Objects.equals(this.createdByName, chartDataRestApiGetList.createdByName) &&
            Objects.equals(this.createdOnDeltaHumanized, chartDataRestApiGetList.createdOnDeltaHumanized) &&
            Objects.equals(this.dashboards, chartDataRestApiGetList.dashboards) &&
            Objects.equals(this.datasourceId, chartDataRestApiGetList.datasourceId) &&
            Objects.equals(this.datasourceNameText, chartDataRestApiGetList.datasourceNameText) &&
            Objects.equals(this.datasourceType, chartDataRestApiGetList.datasourceType) &&
            Objects.equals(this.datasourceUrl, chartDataRestApiGetList.datasourceUrl) &&
            Objects.equals(this.description, chartDataRestApiGetList.description) &&
            Objects.equals(this.descriptionMarkeddown, chartDataRestApiGetList.descriptionMarkeddown) &&
            Objects.equals(this.editUrl, chartDataRestApiGetList.editUrl) &&
            Objects.equals(this.formData, chartDataRestApiGetList.formData) &&
            Objects.equals(this.id, chartDataRestApiGetList.id) &&
            Objects.equals(this.isManagedExternally, chartDataRestApiGetList.isManagedExternally) &&
            Objects.equals(this.lastSavedAt, chartDataRestApiGetList.lastSavedAt) &&
            Objects.equals(this.lastSavedBy, chartDataRestApiGetList.lastSavedBy) &&
            Objects.equals(this.owners, chartDataRestApiGetList.owners) &&
            Objects.equals(this.params, chartDataRestApiGetList.params) &&
            Objects.equals(this.sliceName, chartDataRestApiGetList.sliceName) &&
            Objects.equals(this.sliceUrl, chartDataRestApiGetList.sliceUrl) &&
            Objects.equals(this.table, chartDataRestApiGetList.table) &&
            Objects.equals(this.tags, chartDataRestApiGetList.tags) &&
            Objects.equals(this.thumbnailUrl, chartDataRestApiGetList.thumbnailUrl) &&
            Objects.equals(this.url, chartDataRestApiGetList.url) &&
            Objects.equals(this.vizType, chartDataRestApiGetList.vizType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cacheTimeout, certificationDetails, certifiedBy, changedBy, changedByName, changedOnDeltaHumanized, changedOnDttm, changedOnUtc, createdBy, createdByName, createdOnDeltaHumanized, dashboards, datasourceId, datasourceNameText, datasourceType, datasourceUrl, description, descriptionMarkeddown, editUrl, formData, id, isManagedExternally, lastSavedAt, lastSavedBy, owners, params, sliceName, sliceUrl, table, tags, thumbnailUrl, url, vizType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataRestApiGetList {\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedByName: ").append(toIndentedString(changedByName)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    changedOnDttm: ").append(toIndentedString(changedOnDttm)).append("\n");
        sb.append("    changedOnUtc: ").append(toIndentedString(changedOnUtc)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    createdByName: ").append(toIndentedString(createdByName)).append("\n");
        sb.append("    createdOnDeltaHumanized: ").append(toIndentedString(createdOnDeltaHumanized)).append("\n");
        sb.append("    dashboards: ").append(toIndentedString(dashboards)).append("\n");
        sb.append("    datasourceId: ").append(toIndentedString(datasourceId)).append("\n");
        sb.append("    datasourceNameText: ").append(toIndentedString(datasourceNameText)).append("\n");
        sb.append("    datasourceType: ").append(toIndentedString(datasourceType)).append("\n");
        sb.append("    datasourceUrl: ").append(toIndentedString(datasourceUrl)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    descriptionMarkeddown: ").append(toIndentedString(descriptionMarkeddown)).append("\n");
        sb.append("    editUrl: ").append(toIndentedString(editUrl)).append("\n");
        sb.append("    formData: ").append(toIndentedString(formData)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    lastSavedAt: ").append(toIndentedString(lastSavedAt)).append("\n");
        sb.append("    lastSavedBy: ").append(toIndentedString(lastSavedBy)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    params: ").append(toIndentedString(params)).append("\n");
        sb.append("    sliceName: ").append(toIndentedString(sliceName)).append("\n");
        sb.append("    sliceUrl: ").append(toIndentedString(sliceUrl)).append("\n");
        sb.append("    table: ").append(toIndentedString(table)).append("\n");
        sb.append("    tags: ").append(toIndentedString(tags)).append("\n");
        sb.append("    thumbnailUrl: ").append(toIndentedString(thumbnailUrl)).append("\n");
        sb.append("    url: ").append(toIndentedString(url)).append("\n");
        sb.append("    vizType: ").append(toIndentedString(vizType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

