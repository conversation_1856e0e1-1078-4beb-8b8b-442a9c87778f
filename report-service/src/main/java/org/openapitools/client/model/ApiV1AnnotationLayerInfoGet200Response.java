/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1AnnotationLayerInfoGet200Response
 */
@JsonPropertyOrder({
    ApiV1AnnotationLayerInfoGet200Response.JSON_PROPERTY_ADD_COLUMNS,
    ApiV1AnnotationLayerInfoGet200Response.JSON_PROPERTY_EDIT_COLUMNS,
    ApiV1AnnotationLayerInfoGet200Response.JSON_PROPERTY_FILTERS,
    ApiV1AnnotationLayerInfoGet200Response.JSON_PROPERTY_PERMISSIONS
})
@JsonTypeName("_api_v1_annotation_layer__info_get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1AnnotationLayerInfoGet200Response {
    public static final String JSON_PROPERTY_ADD_COLUMNS = "add_columns";
    private Object addColumns;

    public static final String JSON_PROPERTY_EDIT_COLUMNS = "edit_columns";
    private Object editColumns;

    public static final String JSON_PROPERTY_FILTERS = "filters";
    private ApiV1AnnotationLayerInfoGet200ResponseFilters filters;

    public static final String JSON_PROPERTY_PERMISSIONS = "permissions";
    private List<String> permissions;

    public ApiV1AnnotationLayerInfoGet200Response() {
    }

    public ApiV1AnnotationLayerInfoGet200Response addColumns(Object addColumns) {

        this.addColumns = addColumns;
        return this;
    }

    /**
     * Get addColumns
     *
     * @return addColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ADD_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getAddColumns() {
        return addColumns;
    }


    @JsonProperty(JSON_PROPERTY_ADD_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAddColumns(Object addColumns) {
        this.addColumns = addColumns;
    }


    public ApiV1AnnotationLayerInfoGet200Response editColumns(Object editColumns) {

        this.editColumns = editColumns;
        return this;
    }

    /**
     * Get editColumns
     *
     * @return editColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EDIT_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getEditColumns() {
        return editColumns;
    }


    @JsonProperty(JSON_PROPERTY_EDIT_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEditColumns(Object editColumns) {
        this.editColumns = editColumns;
    }


    public ApiV1AnnotationLayerInfoGet200Response filters(ApiV1AnnotationLayerInfoGet200ResponseFilters filters) {

        this.filters = filters;
        return this;
    }

    /**
     * Get filters
     *
     * @return filters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ApiV1AnnotationLayerInfoGet200ResponseFilters getFilters() {
        return filters;
    }


    @JsonProperty(JSON_PROPERTY_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilters(ApiV1AnnotationLayerInfoGet200ResponseFilters filters) {
        this.filters = filters;
    }


    public ApiV1AnnotationLayerInfoGet200Response permissions(List<String> permissions) {

        this.permissions = permissions;
        return this;
    }

    public ApiV1AnnotationLayerInfoGet200Response addPermissionsItem(String permissionsItem) {
        if (this.permissions == null) {
            this.permissions = new ArrayList<>();
        }
        this.permissions.add(permissionsItem);
        return this;
    }

    /**
     * The user permissions for this API resource
     *
     * @return permissions
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PERMISSIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getPermissions() {
        return permissions;
    }


    @JsonProperty(JSON_PROPERTY_PERMISSIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPermissions(List<String> permissions) {
        this.permissions = permissions;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1AnnotationLayerInfoGet200Response apiV1AnnotationLayerInfoGet200Response = (ApiV1AnnotationLayerInfoGet200Response) o;
        return Objects.equals(this.addColumns, apiV1AnnotationLayerInfoGet200Response.addColumns) &&
            Objects.equals(this.editColumns, apiV1AnnotationLayerInfoGet200Response.editColumns) &&
            Objects.equals(this.filters, apiV1AnnotationLayerInfoGet200Response.filters) &&
            Objects.equals(this.permissions, apiV1AnnotationLayerInfoGet200Response.permissions);
    }

    @Override
    public int hashCode() {
        return Objects.hash(addColumns, editColumns, filters, permissions);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1AnnotationLayerInfoGet200Response {\n");
        sb.append("    addColumns: ").append(toIndentedString(addColumns)).append("\n");
        sb.append("    editColumns: ").append(toIndentedString(editColumns)).append("\n");
        sb.append("    filters: ").append(toIndentedString(filters)).append("\n");
        sb.append("    permissions: ").append(toIndentedString(permissions)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

