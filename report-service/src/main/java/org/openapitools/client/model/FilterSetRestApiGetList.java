/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * FilterSetRestApiGetList
 */
@JsonPropertyOrder({
    FilterSetRestApiGetList.JSON_PROPERTY_CHANGED_BY_FK,
    FilterSetRestApiGetList.JSON_PROPERTY_CHANGED_ON,
    FilterSetRestApiGetList.JSON_PROPERTY_CREATED_BY_FK,
    FilterSetRestApiGetList.JSON_PROPERTY_CREATED_ON,
    FilterSetRestApiGetList.JSON_PROPERTY_DASHBOARD_ID,
    FilterSetRestApiGetList.J<PERSON><PERSON>_PROPERTY_DESCRIPTION,
    FilterSetRestApiGetList.JSON_PROPERTY_ID,
    FilterSetRestApiGetList.JSON_PROPERTY_NAME,
    FilterSetRestApiGetList.JSON_PROPERTY_OWNER_ID,
    FilterSetRestApiGetList.JSON_PROPERTY_OWNER_TYPE,
    FilterSetRestApiGetList.JSON_PROPERTY_PARAMS
})
@JsonTypeName("FilterSetRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class FilterSetRestApiGetList {
    public static final String JSON_PROPERTY_CHANGED_BY_FK = "changed_by_fk";
    private Integer changedByFk;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CREATED_BY_FK = "created_by_fk";
    private Integer createdByFk;

    public static final String JSON_PROPERTY_CREATED_ON = "created_on";
    private OffsetDateTime createdOn;

    public static final String JSON_PROPERTY_DASHBOARD_ID = "dashboard_id";
    private Integer dashboardId;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OWNER_ID = "owner_id";
    private Integer ownerId;

    public static final String JSON_PROPERTY_OWNER_TYPE = "owner_type";
    private String ownerType;

    public static final String JSON_PROPERTY_PARAMS = "params";
    private Object params = null;

    public FilterSetRestApiGetList() {
    }

    @JsonCreator
    public FilterSetRestApiGetList(
        @JsonProperty(JSON_PROPERTY_PARAMS) Object params
    ) {
        this();
        this.params = params;
    }

    public FilterSetRestApiGetList changedByFk(Integer changedByFk) {

        this.changedByFk = changedByFk;
        return this;
    }

    /**
     * Get changedByFk
     *
     * @return changedByFk
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY_FK)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getChangedByFk() {
        return changedByFk;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY_FK)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedByFk(Integer changedByFk) {
        this.changedByFk = changedByFk;
    }


    public FilterSetRestApiGetList changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public FilterSetRestApiGetList createdByFk(Integer createdByFk) {

        this.createdByFk = createdByFk;
        return this;
    }

    /**
     * Get createdByFk
     *
     * @return createdByFk
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY_FK)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCreatedByFk() {
        return createdByFk;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY_FK)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedByFk(Integer createdByFk) {
        this.createdByFk = createdByFk;
    }


    public FilterSetRestApiGetList createdOn(OffsetDateTime createdOn) {

        this.createdOn = createdOn;
        return this;
    }

    /**
     * Get createdOn
     *
     * @return createdOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getCreatedOn() {
        return createdOn;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedOn(OffsetDateTime createdOn) {
        this.createdOn = createdOn;
    }


    public FilterSetRestApiGetList dashboardId(Integer dashboardId) {

        this.dashboardId = dashboardId;
        return this;
    }

    /**
     * Get dashboardId
     *
     * @return dashboardId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDashboardId() {
        return dashboardId;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardId(Integer dashboardId) {
        this.dashboardId = dashboardId;
    }


    public FilterSetRestApiGetList description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public FilterSetRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public FilterSetRestApiGetList name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setName(String name) {
        this.name = name;
    }


    public FilterSetRestApiGetList ownerId(Integer ownerId) {

        this.ownerId = ownerId;
        return this;
    }

    /**
     * Get ownerId
     *
     * @return ownerId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_OWNER_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getOwnerId() {
        return ownerId;
    }


    @JsonProperty(JSON_PROPERTY_OWNER_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }


    public FilterSetRestApiGetList ownerType(String ownerType) {

        this.ownerType = ownerType;
        return this;
    }

    /**
     * Get ownerType
     *
     * @return ownerType
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_OWNER_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getOwnerType() {
        return ownerType;
    }


    @JsonProperty(JSON_PROPERTY_OWNER_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setOwnerType(String ownerType) {
        this.ownerType = ownerType;
    }


    /**
     * Get params
     *
     * @return params
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getParams() {
        return params;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FilterSetRestApiGetList filterSetRestApiGetList = (FilterSetRestApiGetList) o;
        return Objects.equals(this.changedByFk, filterSetRestApiGetList.changedByFk) &&
            Objects.equals(this.changedOn, filterSetRestApiGetList.changedOn) &&
            Objects.equals(this.createdByFk, filterSetRestApiGetList.createdByFk) &&
            Objects.equals(this.createdOn, filterSetRestApiGetList.createdOn) &&
            Objects.equals(this.dashboardId, filterSetRestApiGetList.dashboardId) &&
            Objects.equals(this.description, filterSetRestApiGetList.description) &&
            Objects.equals(this.id, filterSetRestApiGetList.id) &&
            Objects.equals(this.name, filterSetRestApiGetList.name) &&
            Objects.equals(this.ownerId, filterSetRestApiGetList.ownerId) &&
            Objects.equals(this.ownerType, filterSetRestApiGetList.ownerType) &&
            Objects.equals(this.params, filterSetRestApiGetList.params);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedByFk, changedOn, createdByFk, createdOn, dashboardId, description, id, name, ownerId, ownerType, params);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class FilterSetRestApiGetList {\n");
        sb.append("    changedByFk: ").append(toIndentedString(changedByFk)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    createdByFk: ").append(toIndentedString(createdByFk)).append("\n");
        sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
        sb.append("    dashboardId: ").append(toIndentedString(dashboardId)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    ownerId: ").append(toIndentedString(ownerId)).append("\n");
        sb.append("    ownerType: ").append(toIndentedString(ownerType)).append("\n");
        sb.append("    params: ").append(toIndentedString(params)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

