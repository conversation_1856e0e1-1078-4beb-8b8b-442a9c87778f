/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * QueryExecutionResponseSchema
 */
@JsonPropertyOrder({
    QueryExecutionResponseSchema.JSON_PROPERTY_COLUMNS,
    QueryExecutionResponseSchema.JSON_PROPERTY_DATA,
    QueryExecutionResponseSchema.JSON_PROPERTY_EXPANDED_COLUMNS,
    QueryExecutionResponseSchema.JSON_PROPERTY_QUERY,
    QueryExecutionResponseSchema.JSON_PROPERTY_QUERY_ID,
    QueryExecutionResponseSchema.JSON_PROPERTY_SELECTED_COLUMNS,
    QueryExecutionResponseSchema.JSON_PROPERTY_STATUS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class QueryExecutionResponseSchema {
    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<Object> columns;

    public static final String JSON_PROPERTY_DATA = "data";
    private List<Object> data;

    public static final String JSON_PROPERTY_EXPANDED_COLUMNS = "expanded_columns";
    private List<Object> expandedColumns;

    public static final String JSON_PROPERTY_QUERY = "query";
    private QueryResult query;

    public static final String JSON_PROPERTY_QUERY_ID = "query_id";
    private Integer queryId;

    public static final String JSON_PROPERTY_SELECTED_COLUMNS = "selected_columns";
    private List<Object> selectedColumns;

    public static final String JSON_PROPERTY_STATUS = "status";
    private String status;

    public QueryExecutionResponseSchema() {
    }

    public QueryExecutionResponseSchema columns(List<Object> columns) {

        this.columns = columns;
        return this;
    }

    public QueryExecutionResponseSchema addColumnsItem(Object columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * Get columns
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<Object> columns) {
        this.columns = columns;
    }


    public QueryExecutionResponseSchema data(List<Object> data) {

        this.data = data;
        return this;
    }

    public QueryExecutionResponseSchema addDataItem(Object dataItem) {
        if (this.data == null) {
            this.data = new ArrayList<>();
        }
        this.data.add(dataItem);
        return this;
    }

    /**
     * Get data
     *
     * @return data
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getData() {
        return data;
    }


    @JsonProperty(JSON_PROPERTY_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setData(List<Object> data) {
        this.data = data;
    }


    public QueryExecutionResponseSchema expandedColumns(List<Object> expandedColumns) {

        this.expandedColumns = expandedColumns;
        return this;
    }

    public QueryExecutionResponseSchema addExpandedColumnsItem(Object expandedColumnsItem) {
        if (this.expandedColumns == null) {
            this.expandedColumns = new ArrayList<>();
        }
        this.expandedColumns.add(expandedColumnsItem);
        return this;
    }

    /**
     * Get expandedColumns
     *
     * @return expandedColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPANDED_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getExpandedColumns() {
        return expandedColumns;
    }


    @JsonProperty(JSON_PROPERTY_EXPANDED_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExpandedColumns(List<Object> expandedColumns) {
        this.expandedColumns = expandedColumns;
    }


    public QueryExecutionResponseSchema query(QueryResult query) {

        this.query = query;
        return this;
    }

    /**
     * Get query
     *
     * @return query
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public QueryResult getQuery() {
        return query;
    }


    @JsonProperty(JSON_PROPERTY_QUERY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQuery(QueryResult query) {
        this.query = query;
    }


    public QueryExecutionResponseSchema queryId(Integer queryId) {

        this.queryId = queryId;
        return this;
    }

    /**
     * Get queryId
     *
     * @return queryId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getQueryId() {
        return queryId;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryId(Integer queryId) {
        this.queryId = queryId;
    }


    public QueryExecutionResponseSchema selectedColumns(List<Object> selectedColumns) {

        this.selectedColumns = selectedColumns;
        return this;
    }

    public QueryExecutionResponseSchema addSelectedColumnsItem(Object selectedColumnsItem) {
        if (this.selectedColumns == null) {
            this.selectedColumns = new ArrayList<>();
        }
        this.selectedColumns.add(selectedColumnsItem);
        return this;
    }

    /**
     * Get selectedColumns
     *
     * @return selectedColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SELECTED_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getSelectedColumns() {
        return selectedColumns;
    }


    @JsonProperty(JSON_PROPERTY_SELECTED_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSelectedColumns(List<Object> selectedColumns) {
        this.selectedColumns = selectedColumns;
    }


    public QueryExecutionResponseSchema status(String status) {

        this.status = status;
        return this;
    }

    /**
     * Get status
     *
     * @return status
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getStatus() {
        return status;
    }


    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        QueryExecutionResponseSchema queryExecutionResponseSchema = (QueryExecutionResponseSchema) o;
        return Objects.equals(this.columns, queryExecutionResponseSchema.columns) &&
            Objects.equals(this.data, queryExecutionResponseSchema.data) &&
            Objects.equals(this.expandedColumns, queryExecutionResponseSchema.expandedColumns) &&
            Objects.equals(this.query, queryExecutionResponseSchema.query) &&
            Objects.equals(this.queryId, queryExecutionResponseSchema.queryId) &&
            Objects.equals(this.selectedColumns, queryExecutionResponseSchema.selectedColumns) &&
            Objects.equals(this.status, queryExecutionResponseSchema.status);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columns, data, expandedColumns, query, queryId, selectedColumns, status);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class QueryExecutionResponseSchema {\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    data: ").append(toIndentedString(data)).append("\n");
        sb.append("    expandedColumns: ").append(toIndentedString(expandedColumns)).append("\n");
        sb.append("    query: ").append(toIndentedString(query)).append("\n");
        sb.append("    queryId: ").append(toIndentedString(queryId)).append("\n");
        sb.append("    selectedColumns: ").append(toIndentedString(selectedColumns)).append("\n");
        sb.append("    status: ").append(toIndentedString(status)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

