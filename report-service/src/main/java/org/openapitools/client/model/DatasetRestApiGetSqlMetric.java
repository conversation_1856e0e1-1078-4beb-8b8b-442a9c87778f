/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.time.LocalDateTime;
import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * DatasetRestApiGetSqlMetric
 */
@JsonPropertyOrder({
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_CHANGED_ON,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_CREATED_ON,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_CURRENCY,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_D3FORMAT,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_DESCRIPTION,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_EXPRESSION,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_EXTRA,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_ID,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_METRIC_NAME,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_METRIC_TYPE,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_VERBOSE_NAME,
    DatasetRestApiGetSqlMetric.JSON_PROPERTY_WARNING_TEXT
})
@JsonTypeName("DatasetRestApi.get.SqlMetric")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetRestApiGetSqlMetric {
    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private LocalDateTime changedOn;

    public static final String JSON_PROPERTY_CREATED_ON = "created_on";
    private LocalDateTime createdOn;

    public static final String JSON_PROPERTY_CURRENCY = "currency";
    private String currency;

    public static final String JSON_PROPERTY_D3FORMAT = "d3format";
    private String d3format;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXPRESSION = "expression";
    private String expression;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private String extra;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_METRIC_NAME = "metric_name";
    private String metricName;

    public static final String JSON_PROPERTY_METRIC_TYPE = "metric_type";
    private String metricType;

    public static final String JSON_PROPERTY_VERBOSE_NAME = "verbose_name";
    private String verboseName;

    public static final String JSON_PROPERTY_WARNING_TEXT = "warning_text";
    private String warningText;

    public DatasetRestApiGetSqlMetric() {
    }

    public DatasetRestApiGetSqlMetric changedOn(LocalDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public LocalDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(LocalDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public DatasetRestApiGetSqlMetric createdOn(LocalDateTime createdOn) {

        this.createdOn = createdOn;
        return this;
    }

    /**
     * Get createdOn
     *
     * @return createdOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public LocalDateTime getCreatedOn() {
        return createdOn;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
    }


    public DatasetRestApiGetSqlMetric currency(String currency) {

        this.currency = currency;
        return this;
    }

    /**
     * Get currency
     *
     * @return currency
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CURRENCY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCurrency() {
        return currency;
    }


    @JsonProperty(JSON_PROPERTY_CURRENCY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCurrency(String currency) {
        this.currency = currency;
    }


    public DatasetRestApiGetSqlMetric d3format(String d3format) {

        this.d3format = d3format;
        return this;
    }

    /**
     * Get d3format
     *
     * @return d3format
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_D3FORMAT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getD3format() {
        return d3format;
    }


    @JsonProperty(JSON_PROPERTY_D3FORMAT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setD3format(String d3format) {
        this.d3format = d3format;
    }


    public DatasetRestApiGetSqlMetric description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public DatasetRestApiGetSqlMetric expression(String expression) {

        this.expression = expression;
        return this;
    }

    /**
     * Get expression
     *
     * @return expression
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_EXPRESSION)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getExpression() {
        return expression;
    }


    @JsonProperty(JSON_PROPERTY_EXPRESSION)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setExpression(String expression) {
        this.expression = expression;
    }


    public DatasetRestApiGetSqlMetric extra(String extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(String extra) {
        this.extra = extra;
    }


    public DatasetRestApiGetSqlMetric id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DatasetRestApiGetSqlMetric metricName(String metricName) {

        this.metricName = metricName;
        return this;
    }

    /**
     * Get metricName
     *
     * @return metricName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_METRIC_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getMetricName() {
        return metricName;
    }


    @JsonProperty(JSON_PROPERTY_METRIC_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setMetricName(String metricName) {
        this.metricName = metricName;
    }


    public DatasetRestApiGetSqlMetric metricType(String metricType) {

        this.metricType = metricType;
        return this;
    }

    /**
     * Get metricType
     *
     * @return metricType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METRIC_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMetricType() {
        return metricType;
    }


    @JsonProperty(JSON_PROPERTY_METRIC_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMetricType(String metricType) {
        this.metricType = metricType;
    }


    public DatasetRestApiGetSqlMetric verboseName(String verboseName) {

        this.verboseName = verboseName;
        return this;
    }

    /**
     * Get verboseName
     *
     * @return verboseName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VERBOSE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVerboseName() {
        return verboseName;
    }


    @JsonProperty(JSON_PROPERTY_VERBOSE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVerboseName(String verboseName) {
        this.verboseName = verboseName;
    }


    public DatasetRestApiGetSqlMetric warningText(String warningText) {

        this.warningText = warningText;
        return this;
    }

    /**
     * Get warningText
     *
     * @return warningText
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_WARNING_TEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getWarningText() {
        return warningText;
    }


    @JsonProperty(JSON_PROPERTY_WARNING_TEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setWarningText(String warningText) {
        this.warningText = warningText;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetRestApiGetSqlMetric datasetRestApiGetSqlMetric = (DatasetRestApiGetSqlMetric) o;
        return Objects.equals(this.changedOn, datasetRestApiGetSqlMetric.changedOn) &&
            Objects.equals(this.createdOn, datasetRestApiGetSqlMetric.createdOn) &&
            Objects.equals(this.currency, datasetRestApiGetSqlMetric.currency) &&
            Objects.equals(this.d3format, datasetRestApiGetSqlMetric.d3format) &&
            Objects.equals(this.description, datasetRestApiGetSqlMetric.description) &&
            Objects.equals(this.expression, datasetRestApiGetSqlMetric.expression) &&
            Objects.equals(this.extra, datasetRestApiGetSqlMetric.extra) &&
            Objects.equals(this.id, datasetRestApiGetSqlMetric.id) &&
            Objects.equals(this.metricName, datasetRestApiGetSqlMetric.metricName) &&
            Objects.equals(this.metricType, datasetRestApiGetSqlMetric.metricType) &&
            Objects.equals(this.verboseName, datasetRestApiGetSqlMetric.verboseName) &&
            Objects.equals(this.warningText, datasetRestApiGetSqlMetric.warningText);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedOn, createdOn, currency, d3format, description, expression, extra, id, metricName, metricType, verboseName, warningText);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetRestApiGetSqlMetric {\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
        sb.append("    currency: ").append(toIndentedString(currency)).append("\n");
        sb.append("    d3format: ").append(toIndentedString(d3format)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    expression: ").append(toIndentedString(expression)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    metricName: ").append(toIndentedString(metricName)).append("\n");
        sb.append("    metricType: ").append(toIndentedString(metricType)).append("\n");
        sb.append("    verboseName: ").append(toIndentedString(verboseName)).append("\n");
        sb.append("    warningText: ").append(toIndentedString(warningText)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

