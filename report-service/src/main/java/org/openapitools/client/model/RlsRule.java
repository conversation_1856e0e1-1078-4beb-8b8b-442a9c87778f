/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * RlsRule
 */
@JsonPropertyOrder({
    RlsRule.JSON_PROPERTY_CLAUSE,
    RlsRule.JSON_PROPERTY_DATASET
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class RlsRule {
    public static final String JSON_PROPERTY_CLAUSE = "clause";
    private String clause;

    public static final String JSON_PROPERTY_DATASET = "dataset";
    private Integer dataset;

    public RlsRule() {
    }

    public RlsRule clause(String clause) {

        this.clause = clause;
        return this;
    }

    /**
     * Get clause
     *
     * @return clause
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_CLAUSE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getClause() {
        return clause;
    }


    @JsonProperty(JSON_PROPERTY_CLAUSE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setClause(String clause) {
        this.clause = clause;
    }


    public RlsRule dataset(Integer dataset) {

        this.dataset = dataset;
        return this;
    }

    /**
     * Get dataset
     *
     * @return dataset
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDataset() {
        return dataset;
    }


    @JsonProperty(JSON_PROPERTY_DATASET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDataset(Integer dataset) {
        this.dataset = dataset;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RlsRule rlsRule = (RlsRule) o;
        return Objects.equals(this.clause, rlsRule.clause) &&
            Objects.equals(this.dataset, rlsRule.dataset);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clause, dataset);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RlsRule {\n");
        sb.append("    clause: ").append(toIndentedString(clause)).append("\n");
        sb.append("    dataset: ").append(toIndentedString(dataset)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

