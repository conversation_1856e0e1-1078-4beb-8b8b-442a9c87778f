/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * DatasetRelatedCharts
 */
@JsonPropertyOrder({
    DatasetRelatedCharts.JSON_PROPERTY_COUNT,
    DatasetRelatedCharts.JSON_PROPERTY_RESULT
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetRelatedCharts {
    public static final String JSON_PROPERTY_COUNT = "count";
    private Integer count;

    public static final String JSON_PROPERTY_RESULT = "result";
    private List<DatasetRelatedChart> result;

    public DatasetRelatedCharts() {
    }

    public DatasetRelatedCharts count(Integer count) {

        this.count = count;
        return this;
    }

    /**
     * Chart count
     *
     * @return count
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCount() {
        return count;
    }


    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCount(Integer count) {
        this.count = count;
    }


    public DatasetRelatedCharts result(List<DatasetRelatedChart> result) {

        this.result = result;
        return this;
    }

    public DatasetRelatedCharts addResultItem(DatasetRelatedChart resultItem) {
        if (this.result == null) {
            this.result = new ArrayList<>();
        }
        this.result.add(resultItem);
        return this;
    }

    /**
     * A list of dashboards
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<DatasetRelatedChart> getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(List<DatasetRelatedChart> result) {
        this.result = result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetRelatedCharts datasetRelatedCharts = (DatasetRelatedCharts) o;
        return Objects.equals(this.count, datasetRelatedCharts.count) &&
            Objects.equals(this.result, datasetRelatedCharts.result);
    }

    @Override
    public int hashCode() {
        return Objects.hash(count, result);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetRelatedCharts {\n");
        sb.append("    count: ").append(toIndentedString(count)).append("\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

