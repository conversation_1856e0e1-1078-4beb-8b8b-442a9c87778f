/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.*;

/**
 * SQLLabBootstrapSchema
 */
@JsonPropertyOrder({
    SQLLabBootstrapSchema.JSON_PROPERTY_ACTIVE_TAB,
    SQLLabBootstrapSchema.JSON_PROPERTY_DATABASES,
    SQLLabBootstrapSchema.JSON_PROPERTY_QUERIES,
    SQLLabBootstrapSchema.JSON_PROPERTY_TAB_STATE_IDS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class SQLLabBootstrapSchema {
    public static final String JSON_PROPERTY_ACTIVE_TAB = "active_tab";
    private TabState activeTab;

    public static final String JSON_PROPERTY_DATABASES = "databases";
    private Map<String, ImportV1Database> databases = new HashMap<>();

    public static final String JSON_PROPERTY_QUERIES = "queries";
    private Map<String, QueryResult> queries = new HashMap<>();

    public static final String JSON_PROPERTY_TAB_STATE_IDS = "tab_state_ids";
    private List<String> tabStateIds;

    public SQLLabBootstrapSchema() {
    }

    public SQLLabBootstrapSchema activeTab(TabState activeTab) {

        this.activeTab = activeTab;
        return this;
    }

    /**
     * Get activeTab
     *
     * @return activeTab
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTIVE_TAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public TabState getActiveTab() {
        return activeTab;
    }


    @JsonProperty(JSON_PROPERTY_ACTIVE_TAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setActiveTab(TabState activeTab) {
        this.activeTab = activeTab;
    }


    public SQLLabBootstrapSchema databases(Map<String, ImportV1Database> databases) {

        this.databases = databases;
        return this;
    }

    public SQLLabBootstrapSchema putDatabasesItem(String key, ImportV1Database databasesItem) {
        if (this.databases == null) {
            this.databases = new HashMap<>();
        }
        this.databases.put(key, databasesItem);
        return this;
    }

    /**
     * Get databases
     *
     * @return databases
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Map<String, ImportV1Database> getDatabases() {
        return databases;
    }


    @JsonProperty(JSON_PROPERTY_DATABASES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabases(Map<String, ImportV1Database> databases) {
        this.databases = databases;
    }


    public SQLLabBootstrapSchema queries(Map<String, QueryResult> queries) {

        this.queries = queries;
        return this;
    }

    public SQLLabBootstrapSchema putQueriesItem(String key, QueryResult queriesItem) {
        if (this.queries == null) {
            this.queries = new HashMap<>();
        }
        this.queries.put(key, queriesItem);
        return this;
    }

    /**
     * Get queries
     *
     * @return queries
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERIES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Map<String, QueryResult> getQueries() {
        return queries;
    }


    @JsonProperty(JSON_PROPERTY_QUERIES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueries(Map<String, QueryResult> queries) {
        this.queries = queries;
    }


    public SQLLabBootstrapSchema tabStateIds(List<String> tabStateIds) {

        this.tabStateIds = tabStateIds;
        return this;
    }

    public SQLLabBootstrapSchema addTabStateIdsItem(String tabStateIdsItem) {
        if (this.tabStateIds == null) {
            this.tabStateIds = new ArrayList<>();
        }
        this.tabStateIds.add(tabStateIdsItem);
        return this;
    }

    /**
     * Get tabStateIds
     *
     * @return tabStateIds
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAB_STATE_IDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getTabStateIds() {
        return tabStateIds;
    }


    @JsonProperty(JSON_PROPERTY_TAB_STATE_IDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTabStateIds(List<String> tabStateIds) {
        this.tabStateIds = tabStateIds;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SQLLabBootstrapSchema sqLLabBootstrapSchema = (SQLLabBootstrapSchema) o;
        return Objects.equals(this.activeTab, sqLLabBootstrapSchema.activeTab) &&
            Objects.equals(this.databases, sqLLabBootstrapSchema.databases) &&
            Objects.equals(this.queries, sqLLabBootstrapSchema.queries) &&
            Objects.equals(this.tabStateIds, sqLLabBootstrapSchema.tabStateIds);
    }

    @Override
    public int hashCode() {
        return Objects.hash(activeTab, databases, queries, tabStateIds);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class SQLLabBootstrapSchema {\n");
        sb.append("    activeTab: ").append(toIndentedString(activeTab)).append("\n");
        sb.append("    databases: ").append(toIndentedString(databases)).append("\n");
        sb.append("    queries: ").append(toIndentedString(queries)).append("\n");
        sb.append("    tabStateIds: ").append(toIndentedString(tabStateIds)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

