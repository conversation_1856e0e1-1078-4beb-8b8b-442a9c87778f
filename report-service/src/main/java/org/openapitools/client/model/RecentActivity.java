/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * RecentActivity
 */
@JsonPropertyOrder({
    RecentActivity.JSON_PROPERTY_ACTION,
    RecentActivity.JSON_PROPERTY_ITEM_TITLE,
    RecentActivity.JSON_PROPERTY_ITEM_TYPE,
    RecentActivity.JSON_PROPERTY_ITEM_URL,
    RecentActivity.JSON_PROPERTY_TIME,
    RecentActivity.JSON_PROPERTY_TIME_DELTA_HUMANIZED
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class RecentActivity {
    public static final String JSON_PROPERTY_ACTION = "action";
    private String action;

    public static final String JSON_PROPERTY_ITEM_TITLE = "item_title";
    private String itemTitle;

    public static final String JSON_PROPERTY_ITEM_TYPE = "item_type";
    private String itemType;

    public static final String JSON_PROPERTY_ITEM_URL = "item_url";
    private String itemUrl;

    public static final String JSON_PROPERTY_TIME = "time";
    private BigDecimal time;

    public static final String JSON_PROPERTY_TIME_DELTA_HUMANIZED = "time_delta_humanized";
    private String timeDeltaHumanized;

    public RecentActivity() {
    }

    public RecentActivity action(String action) {

        this.action = action;
        return this;
    }

    /**
     * Action taken describing type of activity
     *
     * @return action
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getAction() {
        return action;
    }


    @JsonProperty(JSON_PROPERTY_ACTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAction(String action) {
        this.action = action;
    }


    public RecentActivity itemTitle(String itemTitle) {

        this.itemTitle = itemTitle;
        return this;
    }

    /**
     * Title of item
     *
     * @return itemTitle
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ITEM_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getItemTitle() {
        return itemTitle;
    }


    @JsonProperty(JSON_PROPERTY_ITEM_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setItemTitle(String itemTitle) {
        this.itemTitle = itemTitle;
    }


    public RecentActivity itemType(String itemType) {

        this.itemType = itemType;
        return this;
    }

    /**
     * Type of item, e.g. slice or dashboard
     *
     * @return itemType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ITEM_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getItemType() {
        return itemType;
    }


    @JsonProperty(JSON_PROPERTY_ITEM_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setItemType(String itemType) {
        this.itemType = itemType;
    }


    public RecentActivity itemUrl(String itemUrl) {

        this.itemUrl = itemUrl;
        return this;
    }

    /**
     * URL to item
     *
     * @return itemUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ITEM_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getItemUrl() {
        return itemUrl;
    }


    @JsonProperty(JSON_PROPERTY_ITEM_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setItemUrl(String itemUrl) {
        this.itemUrl = itemUrl;
    }


    public RecentActivity time(BigDecimal time) {

        this.time = time;
        return this;
    }

    /**
     * Time of activity, in epoch milliseconds
     *
     * @return time
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getTime() {
        return time;
    }


    @JsonProperty(JSON_PROPERTY_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTime(BigDecimal time) {
        this.time = time;
    }


    public RecentActivity timeDeltaHumanized(String timeDeltaHumanized) {

        this.timeDeltaHumanized = timeDeltaHumanized;
        return this;
    }

    /**
     * Human-readable description of how long ago activity took place.
     *
     * @return timeDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTimeDeltaHumanized() {
        return timeDeltaHumanized;
    }


    @JsonProperty(JSON_PROPERTY_TIME_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeDeltaHumanized(String timeDeltaHumanized) {
        this.timeDeltaHumanized = timeDeltaHumanized;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RecentActivity recentActivity = (RecentActivity) o;
        return Objects.equals(this.action, recentActivity.action) &&
            Objects.equals(this.itemTitle, recentActivity.itemTitle) &&
            Objects.equals(this.itemType, recentActivity.itemType) &&
            Objects.equals(this.itemUrl, recentActivity.itemUrl) &&
            Objects.equals(this.time, recentActivity.time) &&
            Objects.equals(this.timeDeltaHumanized, recentActivity.timeDeltaHumanized);
    }

    @Override
    public int hashCode() {
        return Objects.hash(action, itemTitle, itemType, itemUrl, time, timeDeltaHumanized);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RecentActivity {\n");
        sb.append("    action: ").append(toIndentedString(action)).append("\n");
        sb.append("    itemTitle: ").append(toIndentedString(itemTitle)).append("\n");
        sb.append("    itemType: ").append(toIndentedString(itemType)).append("\n");
        sb.append("    itemUrl: ").append(toIndentedString(itemUrl)).append("\n");
        sb.append("    time: ").append(toIndentedString(time)).append("\n");
        sb.append("    timeDeltaHumanized: ").append(toIndentedString(timeDeltaHumanized)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

