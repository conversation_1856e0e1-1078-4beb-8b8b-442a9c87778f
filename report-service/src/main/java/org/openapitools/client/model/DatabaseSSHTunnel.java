/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * DatabaseSSHTunnel
 */
@JsonPropertyOrder({
    DatabaseSSHTunnel.JSON_PROPERTY_ID,
    DatabaseSSHTunnel.JSON_PROPERTY_PASSWORD,
    DatabaseSSHTunnel.JSON_PROPERTY_PRIVATE_KEY,
    DatabaseSSHTunnel.JSON_PROPERTY_PRIVATE_KEY_PASSWORD,
    DatabaseSSHTunnel.JSON_PROPERTY_SERVER_ADDRESS,
    DatabaseSSHTunnel.JSON_PROPERTY_SERVER_PORT,
    DatabaseSSHTunnel.JSON_PROPERTY_USERNAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseSSHTunnel {
    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_PASSWORD = "password";
    private String password;

    public static final String JSON_PROPERTY_PRIVATE_KEY = "private_key";
    private String privateKey;

    public static final String JSON_PROPERTY_PRIVATE_KEY_PASSWORD = "private_key_password";
    private String privateKeyPassword;

    public static final String JSON_PROPERTY_SERVER_ADDRESS = "server_address";
    private String serverAddress;

    public static final String JSON_PROPERTY_SERVER_PORT = "server_port";
    private Integer serverPort;

    public static final String JSON_PROPERTY_USERNAME = "username";
    private String username;

    public DatabaseSSHTunnel() {
    }

    public DatabaseSSHTunnel id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * SSH Tunnel ID (for updates)
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DatabaseSSHTunnel password(String password) {

        this.password = password;
        return this;
    }

    /**
     * Get password
     *
     * @return password
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPassword() {
        return password;
    }


    @JsonProperty(JSON_PROPERTY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPassword(String password) {
        this.password = password;
    }


    public DatabaseSSHTunnel privateKey(String privateKey) {

        this.privateKey = privateKey;
        return this;
    }

    /**
     * Get privateKey
     *
     * @return privateKey
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PRIVATE_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPrivateKey() {
        return privateKey;
    }


    @JsonProperty(JSON_PROPERTY_PRIVATE_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }


    public DatabaseSSHTunnel privateKeyPassword(String privateKeyPassword) {

        this.privateKeyPassword = privateKeyPassword;
        return this;
    }

    /**
     * Get privateKeyPassword
     *
     * @return privateKeyPassword
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PRIVATE_KEY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPrivateKeyPassword() {
        return privateKeyPassword;
    }


    @JsonProperty(JSON_PROPERTY_PRIVATE_KEY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPrivateKeyPassword(String privateKeyPassword) {
        this.privateKeyPassword = privateKeyPassword;
    }


    public DatabaseSSHTunnel serverAddress(String serverAddress) {

        this.serverAddress = serverAddress;
        return this;
    }

    /**
     * Get serverAddress
     *
     * @return serverAddress
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SERVER_ADDRESS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getServerAddress() {
        return serverAddress;
    }


    @JsonProperty(JSON_PROPERTY_SERVER_ADDRESS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setServerAddress(String serverAddress) {
        this.serverAddress = serverAddress;
    }


    public DatabaseSSHTunnel serverPort(Integer serverPort) {

        this.serverPort = serverPort;
        return this;
    }

    /**
     * Get serverPort
     *
     * @return serverPort
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SERVER_PORT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getServerPort() {
        return serverPort;
    }


    @JsonProperty(JSON_PROPERTY_SERVER_PORT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setServerPort(Integer serverPort) {
        this.serverPort = serverPort;
    }


    public DatabaseSSHTunnel username(String username) {

        this.username = username;
        return this;
    }

    /**
     * Get username
     *
     * @return username
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUsername() {
        return username;
    }


    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseSSHTunnel databaseSSHTunnel = (DatabaseSSHTunnel) o;
        return Objects.equals(this.id, databaseSSHTunnel.id) &&
            Objects.equals(this.password, databaseSSHTunnel.password) &&
            Objects.equals(this.privateKey, databaseSSHTunnel.privateKey) &&
            Objects.equals(this.privateKeyPassword, databaseSSHTunnel.privateKeyPassword) &&
            Objects.equals(this.serverAddress, databaseSSHTunnel.serverAddress) &&
            Objects.equals(this.serverPort, databaseSSHTunnel.serverPort) &&
            Objects.equals(this.username, databaseSSHTunnel.username);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, password, privateKey, privateKeyPassword, serverAddress, serverPort, username);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseSSHTunnel {\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    password: ").append(toIndentedString(password)).append("\n");
        sb.append("    privateKey: ").append(toIndentedString(privateKey)).append("\n");
        sb.append("    privateKeyPassword: ").append(toIndentedString(privateKeyPassword)).append("\n");
        sb.append("    serverAddress: ").append(toIndentedString(serverAddress)).append("\n");
        sb.append("    serverPort: ").append(toIndentedString(serverPort)).append("\n");
        sb.append("    username: ").append(toIndentedString(username)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

