/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * GetRelatedSchema
 */
@JsonPropertyOrder({
    GetRelatedSchema.JSON_PROPERTY_FILTER,
    GetRelatedSchema.JSON_PROPERTY_INCLUDE_IDS,
    GetRelatedSchema.JSON_PROPERTY_PAGE,
    GetRelatedSchema.JSON_PROPERTY_PAGE_SIZE
})
@JsonTypeName("get_related_schema")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class GetRelatedSchema {
    public static final String JSON_PROPERTY_FILTER = "filter";
    private String filter;

    public static final String JSON_PROPERTY_INCLUDE_IDS = "include_ids";
    private List<Integer> includeIds;

    public static final String JSON_PROPERTY_PAGE = "page";
    private Integer page;

    public static final String JSON_PROPERTY_PAGE_SIZE = "page_size";
    private Integer pageSize;

    public GetRelatedSchema() {
    }

    public GetRelatedSchema filter(String filter) {

        this.filter = filter;
        return this;
    }

    /**
     * Get filter
     *
     * @return filter
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getFilter() {
        return filter;
    }


    @JsonProperty(JSON_PROPERTY_FILTER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilter(String filter) {
        this.filter = filter;
    }


    public GetRelatedSchema includeIds(List<Integer> includeIds) {

        this.includeIds = includeIds;
        return this;
    }

    public GetRelatedSchema addIncludeIdsItem(Integer includeIdsItem) {
        if (this.includeIds == null) {
            this.includeIds = new ArrayList<>();
        }
        this.includeIds.add(includeIdsItem);
        return this;
    }

    /**
     * Get includeIds
     *
     * @return includeIds
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_INCLUDE_IDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getIncludeIds() {
        return includeIds;
    }


    @JsonProperty(JSON_PROPERTY_INCLUDE_IDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIncludeIds(List<Integer> includeIds) {
        this.includeIds = includeIds;
    }


    public GetRelatedSchema page(Integer page) {

        this.page = page;
        return this;
    }

    /**
     * Get page
     *
     * @return page
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getPage() {
        return page;
    }


    @JsonProperty(JSON_PROPERTY_PAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPage(Integer page) {
        this.page = page;
    }


    public GetRelatedSchema pageSize(Integer pageSize) {

        this.pageSize = pageSize;
        return this;
    }

    /**
     * Get pageSize
     *
     * @return pageSize
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getPageSize() {
        return pageSize;
    }


    @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetRelatedSchema getRelatedSchema = (GetRelatedSchema) o;
        return Objects.equals(this.filter, getRelatedSchema.filter) &&
            Objects.equals(this.includeIds, getRelatedSchema.includeIds) &&
            Objects.equals(this.page, getRelatedSchema.page) &&
            Objects.equals(this.pageSize, getRelatedSchema.pageSize);
    }

    @Override
    public int hashCode() {
        return Objects.hash(filter, includeIds, page, pageSize);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class GetRelatedSchema {\n");
        sb.append("    filter: ").append(toIndentedString(filter)).append("\n");
        sb.append("    includeIds: ").append(toIndentedString(includeIds)).append("\n");
        sb.append("    page: ").append(toIndentedString(page)).append("\n");
        sb.append("    pageSize: ").append(toIndentedString(pageSize)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

