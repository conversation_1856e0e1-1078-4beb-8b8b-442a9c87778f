/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.*;

/**
 * ChartDataQueryObject
 */
@JsonPropertyOrder({
    ChartDataQueryObject.JSON_PROPERTY_ANNOTATION_LAYERS,
    ChartDataQueryObject.JSON_PROPERTY_APPLIED_TIME_EXTRAS,
    ChartDataQueryObject.JSON_PROPERTY_APPLY_FETCH_VALUES_PREDICATE,
    ChartDataQueryObject.JSON_PROPERTY_COLUMNS,
    ChartDataQueryObject.JSON_PROPERTY_DATASOURCE,
    ChartDataQueryObject.JSON_PROPERTY_EXTRAS,
    ChartDataQueryObject.JSON_PROPERTY_FILTERS,
    ChartDataQueryObject.JSON_PROPERTY_GRANULARITY,
    ChartDataQueryObject.JSON_PROPERTY_GRANULARITY_SQLA,
    ChartDataQueryObject.JSON_PROPERTY_GROUPBY,
    ChartDataQueryObject.JSON_PROPERTY_HAVING,
    ChartDataQueryObject.JSON_PROPERTY_IS_ROWCOUNT,
    ChartDataQueryObject.JSON_PROPERTY_IS_TIMESERIES,
    ChartDataQueryObject.JSON_PROPERTY_METRICS,
    ChartDataQueryObject.JSON_PROPERTY_ORDER_DESC,
    ChartDataQueryObject.JSON_PROPERTY_ORDERBY,
    ChartDataQueryObject.JSON_PROPERTY_POST_PROCESSING,
    ChartDataQueryObject.JSON_PROPERTY_RESULT_TYPE,
    ChartDataQueryObject.JSON_PROPERTY_ROW_LIMIT,
    ChartDataQueryObject.JSON_PROPERTY_ROW_OFFSET,
    ChartDataQueryObject.JSON_PROPERTY_SERIES_COLUMNS,
    ChartDataQueryObject.JSON_PROPERTY_SERIES_LIMIT,
    ChartDataQueryObject.JSON_PROPERTY_SERIES_LIMIT_METRIC,
    ChartDataQueryObject.JSON_PROPERTY_TIME_OFFSETS,
    ChartDataQueryObject.JSON_PROPERTY_TIME_RANGE,
    ChartDataQueryObject.JSON_PROPERTY_TIME_SHIFT,
    ChartDataQueryObject.JSON_PROPERTY_TIMESERIES_LIMIT,
    ChartDataQueryObject.JSON_PROPERTY_TIMESERIES_LIMIT_METRIC,
    ChartDataQueryObject.JSON_PROPERTY_URL_PARAMS,
    ChartDataQueryObject.JSON_PROPERTY_WHERE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataQueryObject {
    public static final String JSON_PROPERTY_ANNOTATION_LAYERS = "annotation_layers";
    private List<AnnotationLayer> annotationLayers;

    public static final String JSON_PROPERTY_APPLIED_TIME_EXTRAS = "applied_time_extras";
    private Object appliedTimeExtras;

    public static final String JSON_PROPERTY_APPLY_FETCH_VALUES_PREDICATE = "apply_fetch_values_predicate";
    private Boolean applyFetchValuesPredicate;

    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<Object> columns;

    public static final String JSON_PROPERTY_DATASOURCE = "datasource";
    private ChartDataDatasource datasource;

    public static final String JSON_PROPERTY_EXTRAS = "extras";
    private ChartDataQueryObjectExtras extras;

    public static final String JSON_PROPERTY_FILTERS = "filters";
    private List<ChartDataFilter> filters;

    public static final String JSON_PROPERTY_GRANULARITY = "granularity";
    private String granularity;

    public static final String JSON_PROPERTY_GRANULARITY_SQLA = "granularity_sqla";
    private String granularitySqla;

    public static final String JSON_PROPERTY_GROUPBY = "groupby";
    private List<Object> groupby;

    public static final String JSON_PROPERTY_HAVING = "having";
    private String having;

    public static final String JSON_PROPERTY_IS_ROWCOUNT = "is_rowcount";
    private Boolean isRowcount;

    public static final String JSON_PROPERTY_IS_TIMESERIES = "is_timeseries";
    private Boolean isTimeseries;

    public static final String JSON_PROPERTY_METRICS = "metrics";
    /**
     * ChartDataAdhocMetricSchema or String
     */
    private List<Object> metrics;

    public static final String JSON_PROPERTY_ORDER_DESC = "order_desc";
    private Boolean orderDesc;

    public static final String JSON_PROPERTY_ORDERBY = "orderby";
    private List<Object> orderby;

    public static final String JSON_PROPERTY_POST_PROCESSING = "post_processing";
    private List<ChartDataPostProcessingOperation> postProcessing;

    /**
     * Gets or Sets resultType
     */
    public enum ResultTypeEnum {
        COLUMNS("columns"),

        FULL("full"),

        QUERY("query"),

        RESULTS("results"),

        SAMPLES("samples"),

        TIMEGRAINS("timegrains"),

        POST_PROCESSED("post_processed"),

        DRILL_DETAIL("drill_detail");

        private String value;

        ResultTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static ResultTypeEnum fromValue(String value) {
            for (ResultTypeEnum b : ResultTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            return null;
        }
    }

    public static final String JSON_PROPERTY_RESULT_TYPE = "result_type";
    private ResultTypeEnum resultType;

    public static final String JSON_PROPERTY_ROW_LIMIT = "row_limit";
    private Integer rowLimit;

    public static final String JSON_PROPERTY_ROW_OFFSET = "row_offset";
    private Integer rowOffset;

    public static final String JSON_PROPERTY_SERIES_COLUMNS = "series_columns";
    private List<Object> seriesColumns;

    public static final String JSON_PROPERTY_SERIES_LIMIT = "series_limit";
    private Integer seriesLimit;

    public static final String JSON_PROPERTY_SERIES_LIMIT_METRIC = "series_limit_metric";
    private Object seriesLimitMetric = null;

    public static final String JSON_PROPERTY_TIME_OFFSETS = "time_offsets";
    private List<String> timeOffsets;

    public static final String JSON_PROPERTY_TIME_RANGE = "time_range";
    private String timeRange;

    public static final String JSON_PROPERTY_TIME_SHIFT = "time_shift";
    private String timeShift;

    public static final String JSON_PROPERTY_TIMESERIES_LIMIT = "timeseries_limit";
    private Integer timeseriesLimit;

    public static final String JSON_PROPERTY_TIMESERIES_LIMIT_METRIC = "timeseries_limit_metric";
    private Object timeseriesLimitMetric = null;

    public static final String JSON_PROPERTY_URL_PARAMS = "url_params";
    private Map<String, String> urlParams;

    public static final String JSON_PROPERTY_WHERE = "where";
    private String where;

    public ChartDataQueryObject() {
    }

    public ChartDataQueryObject annotationLayers(List<AnnotationLayer> annotationLayers) {

        this.annotationLayers = annotationLayers;
        return this;
    }

    public ChartDataQueryObject addAnnotationLayersItem(AnnotationLayer annotationLayersItem) {
        if (this.annotationLayers == null) {
            this.annotationLayers = new ArrayList<>();
        }
        this.annotationLayers.add(annotationLayersItem);
        return this;
    }

    /**
     * Annotation layers to apply to chart
     *
     * @return annotationLayers
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ANNOTATION_LAYERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<AnnotationLayer> getAnnotationLayers() {
        return annotationLayers;
    }


    @JsonProperty(JSON_PROPERTY_ANNOTATION_LAYERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAnnotationLayers(List<AnnotationLayer> annotationLayers) {
        this.annotationLayers = annotationLayers;
    }


    public ChartDataQueryObject appliedTimeExtras(Object appliedTimeExtras) {

        this.appliedTimeExtras = appliedTimeExtras;
        return this;
    }

    /**
     * A mapping of temporal extras that have been applied to the query
     *
     * @return appliedTimeExtras
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_APPLIED_TIME_EXTRAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getAppliedTimeExtras() {
        return appliedTimeExtras;
    }


    @JsonProperty(JSON_PROPERTY_APPLIED_TIME_EXTRAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAppliedTimeExtras(Object appliedTimeExtras) {
        this.appliedTimeExtras = appliedTimeExtras;
    }


    public ChartDataQueryObject applyFetchValuesPredicate(Boolean applyFetchValuesPredicate) {

        this.applyFetchValuesPredicate = applyFetchValuesPredicate;
        return this;
    }

    /**
     * Add fetch values predicate (where clause) to query if defined in datasource
     *
     * @return applyFetchValuesPredicate
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_APPLY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getApplyFetchValuesPredicate() {
        return applyFetchValuesPredicate;
    }


    @JsonProperty(JSON_PROPERTY_APPLY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setApplyFetchValuesPredicate(Boolean applyFetchValuesPredicate) {
        this.applyFetchValuesPredicate = applyFetchValuesPredicate;
    }


    public ChartDataQueryObject columns(List<Object> columns) {

        this.columns = columns;
        return this;
    }

    public ChartDataQueryObject addColumnsItem(Object columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * Columns which to select in the query.
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<Object> columns) {
        this.columns = columns;
    }


    public ChartDataQueryObject datasource(ChartDataDatasource datasource) {

        this.datasource = datasource;
        return this;
    }

    /**
     * Get datasource
     *
     * @return datasource
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataDatasource getDatasource() {
        return datasource;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasource(ChartDataDatasource datasource) {
        this.datasource = datasource;
    }


    public ChartDataQueryObject extras(ChartDataQueryObjectExtras extras) {

        this.extras = extras;
        return this;
    }

    /**
     * Get extras
     *
     * @return extras
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataQueryObjectExtras getExtras() {
        return extras;
    }


    @JsonProperty(JSON_PROPERTY_EXTRAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtras(ChartDataQueryObjectExtras extras) {
        this.extras = extras;
    }


    public ChartDataQueryObject filters(List<ChartDataFilter> filters) {

        this.filters = filters;
        return this;
    }

    public ChartDataQueryObject addFiltersItem(ChartDataFilter filtersItem) {
        if (this.filters == null) {
            this.filters = new ArrayList<>();
        }
        this.filters.add(filtersItem);
        return this;
    }

    /**
     * Get filters
     *
     * @return filters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<ChartDataFilter> getFilters() {
        return filters;
    }


    @JsonProperty(JSON_PROPERTY_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilters(List<ChartDataFilter> filters) {
        this.filters = filters;
    }


    public ChartDataQueryObject granularity(String granularity) {

        this.granularity = granularity;
        return this;
    }

    /**
     * Name of temporal column used for time filtering.
     *
     * @return granularity
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GRANULARITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getGranularity() {
        return granularity;
    }


    @JsonProperty(JSON_PROPERTY_GRANULARITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGranularity(String granularity) {
        this.granularity = granularity;
    }


    public ChartDataQueryObject granularitySqla(String granularitySqla) {

        this.granularitySqla = granularitySqla;
        return this;
    }

    /**
     * Name of temporal column used for time filtering for SQL datasources. This field is deprecated, use &#x60;granularity&#x60; instead.
     *
     * @return granularitySqla
     * @deprecated
     **/
    @Deprecated
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GRANULARITY_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getGranularitySqla() {
        return granularitySqla;
    }


    @JsonProperty(JSON_PROPERTY_GRANULARITY_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGranularitySqla(String granularitySqla) {
        this.granularitySqla = granularitySqla;
    }


    public ChartDataQueryObject groupby(List<Object> groupby) {

        this.groupby = groupby;
        return this;
    }

    public ChartDataQueryObject addGroupbyItem(Object groupbyItem) {
        if (this.groupby == null) {
            this.groupby = new ArrayList<>();
        }
        this.groupby.add(groupbyItem);
        return this;
    }

    /**
     * Columns by which to group the query. This field is deprecated, use &#x60;columns&#x60; instead.
     *
     * @return groupby
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GROUPBY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getGroupby() {
        return groupby;
    }


    @JsonProperty(JSON_PROPERTY_GROUPBY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGroupby(List<Object> groupby) {
        this.groupby = groupby;
    }


    public ChartDataQueryObject having(String having) {

        this.having = having;
        return this;
    }

    /**
     * HAVING clause to be added to aggregate queries using AND operator. This field is deprecated and should be passed to &#x60;extras&#x60;.
     *
     * @return having
     * @deprecated
     **/
    @Deprecated
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_HAVING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getHaving() {
        return having;
    }


    @JsonProperty(JSON_PROPERTY_HAVING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setHaving(String having) {
        this.having = having;
    }


    public ChartDataQueryObject isRowcount(Boolean isRowcount) {

        this.isRowcount = isRowcount;
        return this;
    }

    /**
     * Should the rowcount of the actual query be returned
     *
     * @return isRowcount
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_ROWCOUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsRowcount() {
        return isRowcount;
    }


    @JsonProperty(JSON_PROPERTY_IS_ROWCOUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsRowcount(Boolean isRowcount) {
        this.isRowcount = isRowcount;
    }


    public ChartDataQueryObject isTimeseries(Boolean isTimeseries) {

        this.isTimeseries = isTimeseries;
        return this;
    }

    /**
     * Is the &#x60;query_object&#x60; a timeseries.
     *
     * @return isTimeseries
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_TIMESERIES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsTimeseries() {
        return isTimeseries;
    }


    @JsonProperty(JSON_PROPERTY_IS_TIMESERIES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsTimeseries(Boolean isTimeseries) {
        this.isTimeseries = isTimeseries;
    }


    public ChartDataQueryObject metrics(List<Object> metrics) {

        this.metrics = metrics;
        return this;
    }

    public ChartDataQueryObject addMetricsItem(Object metricsItem) {
        if (this.metrics == null) {
            this.metrics = new ArrayList<>();
        }
        this.metrics.add(metricsItem);
        return this;
    }

    /**
     * Aggregate expressions. Metrics can be passed as both references to datasource metrics (strings), or ad-hoc metricswhich are defined only within the query object. See &#x60;ChartDataAdhocMetricSchema&#x60; for the structure of ad-hoc metrics.
     *
     * @return metrics
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getMetrics() {
        return metrics;
    }


    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMetrics(List<Object> metrics) {
        this.metrics = metrics;
    }


    public ChartDataQueryObject orderDesc(Boolean orderDesc) {

        this.orderDesc = orderDesc;
        return this;
    }

    /**
     * Reverse order. Default: &#x60;false&#x60;
     *
     * @return orderDesc
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ORDER_DESC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getOrderDesc() {
        return orderDesc;
    }


    @JsonProperty(JSON_PROPERTY_ORDER_DESC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOrderDesc(Boolean orderDesc) {
        this.orderDesc = orderDesc;
    }


    public ChartDataQueryObject orderby(List<Object> orderby) {

        this.orderby = orderby;
        return this;
    }

    public ChartDataQueryObject addOrderbyItem(Object orderbyItem) {
        if (this.orderby == null) {
            this.orderby = new ArrayList<>();
        }
        this.orderby.add(orderbyItem);
        return this;
    }

    /**
     * Expects a list of lists where the first element is the column name which to sort by, and the second element is a boolean.
     *
     * @return orderby
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ORDERBY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getOrderby() {
        return orderby;
    }


    @JsonProperty(JSON_PROPERTY_ORDERBY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOrderby(List<Object> orderby) {
        this.orderby = orderby;
    }


    public ChartDataQueryObject postProcessing(List<ChartDataPostProcessingOperation> postProcessing) {

        this.postProcessing = postProcessing;
        return this;
    }

    public ChartDataQueryObject addPostProcessingItem(ChartDataPostProcessingOperation postProcessingItem) {
        if (this.postProcessing == null) {
            this.postProcessing = new ArrayList<>();
        }
        this.postProcessing.add(postProcessingItem);
        return this;
    }

    /**
     * Post processing operations to be applied to the result set. Operations are applied to the result set in sequential order.
     *
     * @return postProcessing
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_POST_PROCESSING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<ChartDataPostProcessingOperation> getPostProcessing() {
        return postProcessing;
    }


    @JsonProperty(JSON_PROPERTY_POST_PROCESSING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPostProcessing(List<ChartDataPostProcessingOperation> postProcessing) {
        this.postProcessing = postProcessing;
    }


    public ChartDataQueryObject resultType(ResultTypeEnum resultType) {

        this.resultType = resultType;
        return this;
    }

    /**
     * Get resultType
     *
     * @return resultType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ResultTypeEnum getResultType() {
        return resultType;
    }


    @JsonProperty(JSON_PROPERTY_RESULT_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResultType(ResultTypeEnum resultType) {
        this.resultType = resultType;
    }


    public ChartDataQueryObject rowLimit(Integer rowLimit) {

        this.rowLimit = rowLimit;
        return this;
    }

    /**
     * Maximum row count (0&#x3D;disabled). Default: &#x60;config[\&quot;ROW_LIMIT\&quot;]&#x60;
     * minimum: 0
     *
     * @return rowLimit
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROW_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getRowLimit() {
        return rowLimit;
    }


    @JsonProperty(JSON_PROPERTY_ROW_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRowLimit(Integer rowLimit) {
        this.rowLimit = rowLimit;
    }


    public ChartDataQueryObject rowOffset(Integer rowOffset) {

        this.rowOffset = rowOffset;
        return this;
    }

    /**
     * Number of rows to skip. Default: &#x60;0&#x60;
     * minimum: 0
     *
     * @return rowOffset
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROW_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getRowOffset() {
        return rowOffset;
    }


    @JsonProperty(JSON_PROPERTY_ROW_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRowOffset(Integer rowOffset) {
        this.rowOffset = rowOffset;
    }


    public ChartDataQueryObject seriesColumns(List<Object> seriesColumns) {

        this.seriesColumns = seriesColumns;
        return this;
    }

    public ChartDataQueryObject addSeriesColumnsItem(Object seriesColumnsItem) {
        if (this.seriesColumns == null) {
            this.seriesColumns = new ArrayList<>();
        }
        this.seriesColumns.add(seriesColumnsItem);
        return this;
    }

    /**
     * Columns to use when limiting series count. All columns must be present in the &#x60;columns&#x60; property. Requires &#x60;series_limit&#x60; and &#x60;series_limit_metric&#x60; to be set.
     *
     * @return seriesColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SERIES_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getSeriesColumns() {
        return seriesColumns;
    }


    @JsonProperty(JSON_PROPERTY_SERIES_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSeriesColumns(List<Object> seriesColumns) {
        this.seriesColumns = seriesColumns;
    }


    public ChartDataQueryObject seriesLimit(Integer seriesLimit) {

        this.seriesLimit = seriesLimit;
        return this;
    }

    /**
     * Maximum number of series. Requires &#x60;series&#x60; and &#x60;series_limit_metric&#x60; to be set.
     *
     * @return seriesLimit
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SERIES_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getSeriesLimit() {
        return seriesLimit;
    }


    @JsonProperty(JSON_PROPERTY_SERIES_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSeriesLimit(Integer seriesLimit) {
        this.seriesLimit = seriesLimit;
    }


    public ChartDataQueryObject seriesLimitMetric(Object seriesLimitMetric) {

        this.seriesLimitMetric = seriesLimitMetric;
        return this;
    }

    /**
     * Metric used to limit timeseries queries by. Requires &#x60;series&#x60; and &#x60;series_limit&#x60; to be set.
     *
     * @return seriesLimitMetric
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SERIES_LIMIT_METRIC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getSeriesLimitMetric() {
        return seriesLimitMetric;
    }


    @JsonProperty(JSON_PROPERTY_SERIES_LIMIT_METRIC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSeriesLimitMetric(Object seriesLimitMetric) {
        this.seriesLimitMetric = seriesLimitMetric;
    }


    public ChartDataQueryObject timeOffsets(List<String> timeOffsets) {

        this.timeOffsets = timeOffsets;
        return this;
    }

    public ChartDataQueryObject addTimeOffsetsItem(String timeOffsetsItem) {
        if (this.timeOffsets == null) {
            this.timeOffsets = new ArrayList<>();
        }
        this.timeOffsets.add(timeOffsetsItem);
        return this;
    }

    /**
     * Get timeOffsets
     *
     * @return timeOffsets
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_OFFSETS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getTimeOffsets() {
        return timeOffsets;
    }


    @JsonProperty(JSON_PROPERTY_TIME_OFFSETS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeOffsets(List<String> timeOffsets) {
        this.timeOffsets = timeOffsets;
    }


    public ChartDataQueryObject timeRange(String timeRange) {

        this.timeRange = timeRange;
        return this;
    }

    /**
     * A time rage, either expressed as a colon separated string &#x60;since : until&#x60; or human readable freeform. Valid formats for &#x60;since&#x60; and &#x60;until&#x60; are:  - ISO 8601 - X days/years/hours/day/year/weeks - X days/years/hours/day/year/weeks ago - X days/years/hours/day/year/weeks from now  Additionally, the following freeform can be used:  - Last day - Last week - Last month - Last quarter - Last year - No filter - Last X seconds/minutes/hours/days/weeks/months/years - Next X seconds/minutes/hours/days/weeks/months/years
     *
     * @return timeRange
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_RANGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTimeRange() {
        return timeRange;
    }


    @JsonProperty(JSON_PROPERTY_TIME_RANGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
    }


    public ChartDataQueryObject timeShift(String timeShift) {

        this.timeShift = timeShift;
        return this;
    }

    /**
     * A human-readable date/time string. Please refer to [parsdatetime](https://github.com/bear/parsedatetime) documentation for details on valid values.
     *
     * @return timeShift
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_SHIFT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTimeShift() {
        return timeShift;
    }


    @JsonProperty(JSON_PROPERTY_TIME_SHIFT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeShift(String timeShift) {
        this.timeShift = timeShift;
    }


    public ChartDataQueryObject timeseriesLimit(Integer timeseriesLimit) {

        this.timeseriesLimit = timeseriesLimit;
        return this;
    }

    /**
     * Maximum row count for timeseries queries. This field is deprecated, use &#x60;series_limit&#x60; instead.Default: &#x60;0&#x60;
     *
     * @return timeseriesLimit
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIMESERIES_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getTimeseriesLimit() {
        return timeseriesLimit;
    }


    @JsonProperty(JSON_PROPERTY_TIMESERIES_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeseriesLimit(Integer timeseriesLimit) {
        this.timeseriesLimit = timeseriesLimit;
    }


    public ChartDataQueryObject timeseriesLimitMetric(Object timeseriesLimitMetric) {

        this.timeseriesLimitMetric = timeseriesLimitMetric;
        return this;
    }

    /**
     * Metric used to limit timeseries queries by. This field is deprecated, use &#x60;series_limit_metric&#x60; instead.
     *
     * @return timeseriesLimitMetric
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIMESERIES_LIMIT_METRIC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getTimeseriesLimitMetric() {
        return timeseriesLimitMetric;
    }


    @JsonProperty(JSON_PROPERTY_TIMESERIES_LIMIT_METRIC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeseriesLimitMetric(Object timeseriesLimitMetric) {
        this.timeseriesLimitMetric = timeseriesLimitMetric;
    }


    public ChartDataQueryObject urlParams(Map<String, String> urlParams) {

        this.urlParams = urlParams;
        return this;
    }

    public ChartDataQueryObject putUrlParamsItem(String key, String urlParamsItem) {
        if (this.urlParams == null) {
            this.urlParams = new HashMap<>();
        }
        this.urlParams.put(key, urlParamsItem);
        return this;
    }

    /**
     * Optional query parameters passed to a dashboard or Explore  view
     *
     * @return urlParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Map<String, String> getUrlParams() {
        return urlParams;
    }


    @JsonProperty(JSON_PROPERTY_URL_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUrlParams(Map<String, String> urlParams) {
        this.urlParams = urlParams;
    }


    public ChartDataQueryObject where(String where) {

        this.where = where;
        return this;
    }

    /**
     * WHERE clause to be added to queries using AND operator.This field is deprecated and should be passed to &#x60;extras&#x60;.
     *
     * @return where
     * @deprecated
     **/
    @Deprecated
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_WHERE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getWhere() {
        return where;
    }


    @JsonProperty(JSON_PROPERTY_WHERE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setWhere(String where) {
        this.where = where;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataQueryObject chartDataQueryObject = (ChartDataQueryObject) o;
        return Objects.equals(this.annotationLayers, chartDataQueryObject.annotationLayers) &&
            Objects.equals(this.appliedTimeExtras, chartDataQueryObject.appliedTimeExtras) &&
            Objects.equals(this.applyFetchValuesPredicate, chartDataQueryObject.applyFetchValuesPredicate) &&
            Objects.equals(this.columns, chartDataQueryObject.columns) &&
            Objects.equals(this.datasource, chartDataQueryObject.datasource) &&
            Objects.equals(this.extras, chartDataQueryObject.extras) &&
            Objects.equals(this.filters, chartDataQueryObject.filters) &&
            Objects.equals(this.granularity, chartDataQueryObject.granularity) &&
            Objects.equals(this.granularitySqla, chartDataQueryObject.granularitySqla) &&
            Objects.equals(this.groupby, chartDataQueryObject.groupby) &&
            Objects.equals(this.having, chartDataQueryObject.having) &&
            Objects.equals(this.isRowcount, chartDataQueryObject.isRowcount) &&
            Objects.equals(this.isTimeseries, chartDataQueryObject.isTimeseries) &&
            Objects.equals(this.metrics, chartDataQueryObject.metrics) &&
            Objects.equals(this.orderDesc, chartDataQueryObject.orderDesc) &&
            Objects.equals(this.orderby, chartDataQueryObject.orderby) &&
            Objects.equals(this.postProcessing, chartDataQueryObject.postProcessing) &&
            Objects.equals(this.resultType, chartDataQueryObject.resultType) &&
            Objects.equals(this.rowLimit, chartDataQueryObject.rowLimit) &&
            Objects.equals(this.rowOffset, chartDataQueryObject.rowOffset) &&
            Objects.equals(this.seriesColumns, chartDataQueryObject.seriesColumns) &&
            Objects.equals(this.seriesLimit, chartDataQueryObject.seriesLimit) &&
            Objects.equals(this.seriesLimitMetric, chartDataQueryObject.seriesLimitMetric) &&
            Objects.equals(this.timeOffsets, chartDataQueryObject.timeOffsets) &&
            Objects.equals(this.timeRange, chartDataQueryObject.timeRange) &&
            Objects.equals(this.timeShift, chartDataQueryObject.timeShift) &&
            Objects.equals(this.timeseriesLimit, chartDataQueryObject.timeseriesLimit) &&
            Objects.equals(this.timeseriesLimitMetric, chartDataQueryObject.timeseriesLimitMetric) &&
            Objects.equals(this.urlParams, chartDataQueryObject.urlParams) &&
            Objects.equals(this.where, chartDataQueryObject.where);
    }

    @Override
    public int hashCode() {
        return Objects.hash(annotationLayers, appliedTimeExtras, applyFetchValuesPredicate, columns, datasource, extras, filters, granularity, granularitySqla, groupby, having, isRowcount, isTimeseries, metrics, orderDesc, orderby, postProcessing, resultType, rowLimit, rowOffset, seriesColumns, seriesLimit, seriesLimitMetric, timeOffsets, timeRange, timeShift, timeseriesLimit, timeseriesLimitMetric, urlParams, where);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataQueryObject {\n");
        sb.append("    annotationLayers: ").append(toIndentedString(annotationLayers)).append("\n");
        sb.append("    appliedTimeExtras: ").append(toIndentedString(appliedTimeExtras)).append("\n");
        sb.append("    applyFetchValuesPredicate: ").append(toIndentedString(applyFetchValuesPredicate)).append("\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    datasource: ").append(toIndentedString(datasource)).append("\n");
        sb.append("    extras: ").append(toIndentedString(extras)).append("\n");
        sb.append("    filters: ").append(toIndentedString(filters)).append("\n");
        sb.append("    granularity: ").append(toIndentedString(granularity)).append("\n");
        sb.append("    granularitySqla: ").append(toIndentedString(granularitySqla)).append("\n");
        sb.append("    groupby: ").append(toIndentedString(groupby)).append("\n");
        sb.append("    having: ").append(toIndentedString(having)).append("\n");
        sb.append("    isRowcount: ").append(toIndentedString(isRowcount)).append("\n");
        sb.append("    isTimeseries: ").append(toIndentedString(isTimeseries)).append("\n");
        sb.append("    metrics: ").append(toIndentedString(metrics)).append("\n");
        sb.append("    orderDesc: ").append(toIndentedString(orderDesc)).append("\n");
        sb.append("    orderby: ").append(toIndentedString(orderby)).append("\n");
        sb.append("    postProcessing: ").append(toIndentedString(postProcessing)).append("\n");
        sb.append("    resultType: ").append(toIndentedString(resultType)).append("\n");
        sb.append("    rowLimit: ").append(toIndentedString(rowLimit)).append("\n");
        sb.append("    rowOffset: ").append(toIndentedString(rowOffset)).append("\n");
        sb.append("    seriesColumns: ").append(toIndentedString(seriesColumns)).append("\n");
        sb.append("    seriesLimit: ").append(toIndentedString(seriesLimit)).append("\n");
        sb.append("    seriesLimitMetric: ").append(toIndentedString(seriesLimitMetric)).append("\n");
        sb.append("    timeOffsets: ").append(toIndentedString(timeOffsets)).append("\n");
        sb.append("    timeRange: ").append(toIndentedString(timeRange)).append("\n");
        sb.append("    timeShift: ").append(toIndentedString(timeShift)).append("\n");
        sb.append("    timeseriesLimit: ").append(toIndentedString(timeseriesLimit)).append("\n");
        sb.append("    timeseriesLimitMetric: ").append(toIndentedString(timeseriesLimitMetric)).append("\n");
        sb.append("    urlParams: ").append(toIndentedString(urlParams)).append("\n");
        sb.append("    where: ").append(toIndentedString(where)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

