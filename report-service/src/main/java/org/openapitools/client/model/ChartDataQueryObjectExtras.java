/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * Extra parameters to add to the query.
 */
@JsonPropertyOrder({
    ChartDataQueryObjectExtras.JSON_PROPERTY_HAVING,
    ChartDataQueryObjectExtras.JSON_PROPERTY_RELATIVE_END,
    ChartDataQueryObjectExtras.JSON_PROPERTY_RELATIVE_START,
    ChartDataQueryObjectExtras.JSON_PROPERTY_TIME_GRAIN_SQLA,
    ChartDataQueryObjectExtras.JSON_PROPERTY_WHERE
})
@JsonTypeName("ChartDataQueryObject_extras")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataQueryObjectExtras {
    public static final String JSON_PROPERTY_HAVING = "having";
    private String having;

    /**
     * End time for relative time deltas. Default: &#x60;config[\&quot;DEFAULT_RELATIVE_START_TIME\&quot;]&#x60;
     */
    public enum RelativeEndEnum {
        TODAY("today"),

        NOW("now");

        private String value;

        RelativeEndEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static RelativeEndEnum fromValue(String value) {
            for (RelativeEndEnum b : RelativeEndEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_RELATIVE_END = "relative_end";
    private RelativeEndEnum relativeEnd;

    /**
     * Start time for relative time deltas. Default: &#x60;config[\&quot;DEFAULT_RELATIVE_START_TIME\&quot;]&#x60;
     */
    public enum RelativeStartEnum {
        TODAY("today"),

        NOW("now");

        private String value;

        RelativeStartEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static RelativeStartEnum fromValue(String value) {
            for (RelativeStartEnum b : RelativeStartEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_RELATIVE_START = "relative_start";
    private RelativeStartEnum relativeStart;

    /**
     * To what level of granularity should the temporal column be aggregated. Supports [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Durations) durations.
     */
    public enum TimeGrainSqlaEnum {
        PT1S("PT1S"),

        PT5S("PT5S"),

        PT30S("PT30S"),

        PT1M("PT1M"),

        PT5M("PT5M"),

        PT10M("PT10M"),

        PT15M("PT15M"),

        PT30M("PT30M"),

        PT1H("PT1H"),

        PT6H("PT6H"),

        P1D("P1D"),

        P1W("P1W"),

        P1M("P1M"),

        P3M("P3M"),

        P1Y("P1Y"),

        _1969_12_28T00_00_00Z_P1W("1969-12-28T00:00:00Z/P1W"),

        _1969_12_29T00_00_00Z_P1W("1969-12-29T00:00:00Z/P1W"),

        P1W_1970_01_03T00_00_00Z("P1W/1970-01-03T00:00:00Z"),

        P1W_1970_01_04T00_00_00Z("P1W/1970-01-04T00:00:00Z");

        private String value;

        TimeGrainSqlaEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static TimeGrainSqlaEnum fromValue(String value) {
            for (TimeGrainSqlaEnum b : TimeGrainSqlaEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            return null;
        }
    }

    public static final String JSON_PROPERTY_TIME_GRAIN_SQLA = "time_grain_sqla";
    private TimeGrainSqlaEnum timeGrainSqla;

    public static final String JSON_PROPERTY_WHERE = "where";
    private String where;

    public ChartDataQueryObjectExtras() {
    }

    public ChartDataQueryObjectExtras having(String having) {

        this.having = having;
        return this;
    }

    /**
     * HAVING clause to be added to aggregate queries using AND operator.
     *
     * @return having
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_HAVING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getHaving() {
        return having;
    }


    @JsonProperty(JSON_PROPERTY_HAVING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setHaving(String having) {
        this.having = having;
    }


    public ChartDataQueryObjectExtras relativeEnd(RelativeEndEnum relativeEnd) {

        this.relativeEnd = relativeEnd;
        return this;
    }

    /**
     * End time for relative time deltas. Default: &#x60;config[\&quot;DEFAULT_RELATIVE_START_TIME\&quot;]&#x60;
     *
     * @return relativeEnd
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RELATIVE_END)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public RelativeEndEnum getRelativeEnd() {
        return relativeEnd;
    }


    @JsonProperty(JSON_PROPERTY_RELATIVE_END)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRelativeEnd(RelativeEndEnum relativeEnd) {
        this.relativeEnd = relativeEnd;
    }


    public ChartDataQueryObjectExtras relativeStart(RelativeStartEnum relativeStart) {

        this.relativeStart = relativeStart;
        return this;
    }

    /**
     * Start time for relative time deltas. Default: &#x60;config[\&quot;DEFAULT_RELATIVE_START_TIME\&quot;]&#x60;
     *
     * @return relativeStart
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RELATIVE_START)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public RelativeStartEnum getRelativeStart() {
        return relativeStart;
    }


    @JsonProperty(JSON_PROPERTY_RELATIVE_START)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRelativeStart(RelativeStartEnum relativeStart) {
        this.relativeStart = relativeStart;
    }


    public ChartDataQueryObjectExtras timeGrainSqla(TimeGrainSqlaEnum timeGrainSqla) {

        this.timeGrainSqla = timeGrainSqla;
        return this;
    }

    /**
     * To what level of granularity should the temporal column be aggregated. Supports [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Durations) durations.
     *
     * @return timeGrainSqla
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_GRAIN_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public TimeGrainSqlaEnum getTimeGrainSqla() {
        return timeGrainSqla;
    }


    @JsonProperty(JSON_PROPERTY_TIME_GRAIN_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeGrainSqla(TimeGrainSqlaEnum timeGrainSqla) {
        this.timeGrainSqla = timeGrainSqla;
    }


    public ChartDataQueryObjectExtras where(String where) {

        this.where = where;
        return this;
    }

    /**
     * WHERE clause to be added to queries using AND operator.
     *
     * @return where
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_WHERE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getWhere() {
        return where;
    }


    @JsonProperty(JSON_PROPERTY_WHERE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setWhere(String where) {
        this.where = where;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataQueryObjectExtras chartDataQueryObjectExtras = (ChartDataQueryObjectExtras) o;
        return Objects.equals(this.having, chartDataQueryObjectExtras.having) &&
            Objects.equals(this.relativeEnd, chartDataQueryObjectExtras.relativeEnd) &&
            Objects.equals(this.relativeStart, chartDataQueryObjectExtras.relativeStart) &&
            Objects.equals(this.timeGrainSqla, chartDataQueryObjectExtras.timeGrainSqla) &&
            Objects.equals(this.where, chartDataQueryObjectExtras.where);
    }

    @Override
    public int hashCode() {
        return Objects.hash(having, relativeEnd, relativeStart, timeGrainSqla, where);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataQueryObjectExtras {\n");
        sb.append("    having: ").append(toIndentedString(having)).append("\n");
        sb.append("    relativeEnd: ").append(toIndentedString(relativeEnd)).append("\n");
        sb.append("    relativeStart: ").append(toIndentedString(relativeStart)).append("\n");
        sb.append("    timeGrainSqla: ").append(toIndentedString(timeGrainSqla)).append("\n");
        sb.append("    where: ").append(toIndentedString(where)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

