/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1AnnotationLayerGet200ResponseDescriptionColumns
 */
@JsonPropertyOrder({
    ApiV1AnnotationLayerGet200ResponseDescriptionColumns.JSON_PROPERTY_COLUMN_NAME
})
@JsonTypeName("_api_v1_annotation_layer__get_200_response_description_columns")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1AnnotationLayerGet200ResponseDescriptionColumns {
    public static final String JSON_PROPERTY_COLUMN_NAME = "column_name";
    private String columnName;

    public ApiV1AnnotationLayerGet200ResponseDescriptionColumns() {
    }

    public ApiV1AnnotationLayerGet200ResponseDescriptionColumns columnName(String columnName) {

        this.columnName = columnName;
        return this;
    }

    /**
     * The description for the column name. Will be translated by babel
     *
     * @return columnName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getColumnName() {
        return columnName;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1AnnotationLayerGet200ResponseDescriptionColumns apiV1AnnotationLayerGet200ResponseDescriptionColumns = (ApiV1AnnotationLayerGet200ResponseDescriptionColumns) o;
        return Objects.equals(this.columnName, apiV1AnnotationLayerGet200ResponseDescriptionColumns.columnName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columnName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1AnnotationLayerGet200ResponseDescriptionColumns {\n");
        sb.append("    columnName: ").append(toIndentedString(columnName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

