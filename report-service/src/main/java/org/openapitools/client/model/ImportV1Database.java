/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;
import java.util.UUID;

/**
 * ImportV1Database
 */
@JsonPropertyOrder({
    ImportV1Database.JSON_PROPERTY_ALLOW_CSV_UPLOAD,
    ImportV1Database.JSON_PROPERTY_ALLOW_CTAS,
    ImportV1Database.JSON_PROPERTY_ALLOW_CVAS,
    ImportV1Database.JSON_PROPERTY_ALLOW_DML,
    ImportV1Database.JSON_PROPERTY_ALLOW_RUN_ASYNC,
    ImportV1Database.JSON_PROPERTY_CACHE_TIMEOUT,
    ImportV1Database.JSON_PROPERTY_DATABASE_NAME,
    ImportV1Database.JSON_PROPERTY_EXPOSE_IN_SQLLAB,
    ImportV1Database.JSON_PROPERTY_EXTERNAL_URL,
    ImportV1Database.JSON_PROPERTY_EXTRA,
    ImportV1Database.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    ImportV1Database.JSON_PROPERTY_PASSWORD,
    ImportV1Database.JSON_PROPERTY_SQLALCHEMY_URI,
    ImportV1Database.JSON_PROPERTY_SSH_TUNNEL,
    ImportV1Database.JSON_PROPERTY_UUID,
    ImportV1Database.JSON_PROPERTY_VERSION
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ImportV1Database {
    public static final String JSON_PROPERTY_ALLOW_CSV_UPLOAD = "allow_csv_upload";
    private Boolean allowCsvUpload;

    public static final String JSON_PROPERTY_ALLOW_CTAS = "allow_ctas";
    private Boolean allowCtas;

    public static final String JSON_PROPERTY_ALLOW_CVAS = "allow_cvas";
    private Boolean allowCvas;

    public static final String JSON_PROPERTY_ALLOW_DML = "allow_dml";
    private Boolean allowDml;

    public static final String JSON_PROPERTY_ALLOW_RUN_ASYNC = "allow_run_async";
    private Boolean allowRunAsync;

    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_DATABASE_NAME = "database_name";
    private String databaseName;

    public static final String JSON_PROPERTY_EXPOSE_IN_SQLLAB = "expose_in_sqllab";
    private Boolean exposeInSqllab;

    public static final String JSON_PROPERTY_EXTERNAL_URL = "external_url";
    private String externalUrl;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private ImportV1DatabaseExtra extra;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_PASSWORD = "password";
    private String password;

    public static final String JSON_PROPERTY_SQLALCHEMY_URI = "sqlalchemy_uri";
    private String sqlalchemyUri;

    public static final String JSON_PROPERTY_SSH_TUNNEL = "ssh_tunnel";
    private DatabaseSSHTunnel sshTunnel;

    public static final String JSON_PROPERTY_UUID = "uuid";
    private UUID uuid;

    public static final String JSON_PROPERTY_VERSION = "version";
    private String version;

    public ImportV1Database() {
    }

    public ImportV1Database allowCsvUpload(Boolean allowCsvUpload) {

        this.allowCsvUpload = allowCsvUpload;
        return this;
    }

    /**
     * Get allowCsvUpload
     *
     * @return allowCsvUpload
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_CSV_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowCsvUpload() {
        return allowCsvUpload;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_CSV_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowCsvUpload(Boolean allowCsvUpload) {
        this.allowCsvUpload = allowCsvUpload;
    }


    public ImportV1Database allowCtas(Boolean allowCtas) {

        this.allowCtas = allowCtas;
        return this;
    }

    /**
     * Get allowCtas
     *
     * @return allowCtas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowCtas() {
        return allowCtas;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowCtas(Boolean allowCtas) {
        this.allowCtas = allowCtas;
    }


    public ImportV1Database allowCvas(Boolean allowCvas) {

        this.allowCvas = allowCvas;
        return this;
    }

    /**
     * Get allowCvas
     *
     * @return allowCvas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_CVAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowCvas() {
        return allowCvas;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_CVAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowCvas(Boolean allowCvas) {
        this.allowCvas = allowCvas;
    }


    public ImportV1Database allowDml(Boolean allowDml) {

        this.allowDml = allowDml;
        return this;
    }

    /**
     * Get allowDml
     *
     * @return allowDml
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_DML)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowDml() {
        return allowDml;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_DML)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowDml(Boolean allowDml) {
        this.allowDml = allowDml;
    }


    public ImportV1Database allowRunAsync(Boolean allowRunAsync) {

        this.allowRunAsync = allowRunAsync;
        return this;
    }

    /**
     * Get allowRunAsync
     *
     * @return allowRunAsync
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowRunAsync() {
        return allowRunAsync;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowRunAsync(Boolean allowRunAsync) {
        this.allowRunAsync = allowRunAsync;
    }


    public ImportV1Database cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Get cacheTimeout
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public ImportV1Database databaseName(String databaseName) {

        this.databaseName = databaseName;
        return this;
    }

    /**
     * Get databaseName
     *
     * @return databaseName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getDatabaseName() {
        return databaseName;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }


    public ImportV1Database exposeInSqllab(Boolean exposeInSqllab) {

        this.exposeInSqllab = exposeInSqllab;
        return this;
    }

    /**
     * Get exposeInSqllab
     *
     * @return exposeInSqllab
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPOSE_IN_SQLLAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getExposeInSqllab() {
        return exposeInSqllab;
    }


    @JsonProperty(JSON_PROPERTY_EXPOSE_IN_SQLLAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExposeInSqllab(Boolean exposeInSqllab) {
        this.exposeInSqllab = exposeInSqllab;
    }


    public ImportV1Database externalUrl(String externalUrl) {

        this.externalUrl = externalUrl;
        return this;
    }

    /**
     * Get externalUrl
     *
     * @return externalUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExternalUrl() {
        return externalUrl;
    }


    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExternalUrl(String externalUrl) {
        this.externalUrl = externalUrl;
    }


    public ImportV1Database extra(ImportV1DatabaseExtra extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ImportV1DatabaseExtra getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(ImportV1DatabaseExtra extra) {
        this.extra = extra;
    }


    public ImportV1Database isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public ImportV1Database password(String password) {

        this.password = password;
        return this;
    }

    /**
     * Get password
     *
     * @return password
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPassword() {
        return password;
    }


    @JsonProperty(JSON_PROPERTY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPassword(String password) {
        this.password = password;
    }


    public ImportV1Database sqlalchemyUri(String sqlalchemyUri) {

        this.sqlalchemyUri = sqlalchemyUri;
        return this;
    }

    /**
     * Get sqlalchemyUri
     *
     * @return sqlalchemyUri
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_SQLALCHEMY_URI)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getSqlalchemyUri() {
        return sqlalchemyUri;
    }


    @JsonProperty(JSON_PROPERTY_SQLALCHEMY_URI)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setSqlalchemyUri(String sqlalchemyUri) {
        this.sqlalchemyUri = sqlalchemyUri;
    }


    public ImportV1Database sshTunnel(DatabaseSSHTunnel sshTunnel) {

        this.sshTunnel = sshTunnel;
        return this;
    }

    /**
     * Get sshTunnel
     *
     * @return sshTunnel
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SSH_TUNNEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DatabaseSSHTunnel getSshTunnel() {
        return sshTunnel;
    }


    @JsonProperty(JSON_PROPERTY_SSH_TUNNEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSshTunnel(DatabaseSSHTunnel sshTunnel) {
        this.sshTunnel = sshTunnel;
    }


    public ImportV1Database uuid(UUID uuid) {

        this.uuid = uuid;
        return this;
    }

    /**
     * Get uuid
     *
     * @return uuid
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public UUID getUuid() {
        return uuid;
    }


    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setUuid(UUID uuid) {
        this.uuid = uuid;
    }


    public ImportV1Database version(String version) {

        this.version = version;
        return this;
    }

    /**
     * Get version
     *
     * @return version
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_VERSION)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getVersion() {
        return version;
    }


    @JsonProperty(JSON_PROPERTY_VERSION)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setVersion(String version) {
        this.version = version;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ImportV1Database importV1Database = (ImportV1Database) o;
        return Objects.equals(this.allowCsvUpload, importV1Database.allowCsvUpload) &&
            Objects.equals(this.allowCtas, importV1Database.allowCtas) &&
            Objects.equals(this.allowCvas, importV1Database.allowCvas) &&
            Objects.equals(this.allowDml, importV1Database.allowDml) &&
            Objects.equals(this.allowRunAsync, importV1Database.allowRunAsync) &&
            Objects.equals(this.cacheTimeout, importV1Database.cacheTimeout) &&
            Objects.equals(this.databaseName, importV1Database.databaseName) &&
            Objects.equals(this.exposeInSqllab, importV1Database.exposeInSqllab) &&
            Objects.equals(this.externalUrl, importV1Database.externalUrl) &&
            Objects.equals(this.extra, importV1Database.extra) &&
            Objects.equals(this.isManagedExternally, importV1Database.isManagedExternally) &&
            Objects.equals(this.password, importV1Database.password) &&
            Objects.equals(this.sqlalchemyUri, importV1Database.sqlalchemyUri) &&
            Objects.equals(this.sshTunnel, importV1Database.sshTunnel) &&
            Objects.equals(this.uuid, importV1Database.uuid) &&
            Objects.equals(this.version, importV1Database.version);
    }

    @Override
    public int hashCode() {
        return Objects.hash(allowCsvUpload, allowCtas, allowCvas, allowDml, allowRunAsync, cacheTimeout, databaseName, exposeInSqllab, externalUrl, extra, isManagedExternally, password, sqlalchemyUri, sshTunnel, uuid, version);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ImportV1Database {\n");
        sb.append("    allowCsvUpload: ").append(toIndentedString(allowCsvUpload)).append("\n");
        sb.append("    allowCtas: ").append(toIndentedString(allowCtas)).append("\n");
        sb.append("    allowCvas: ").append(toIndentedString(allowCvas)).append("\n");
        sb.append("    allowDml: ").append(toIndentedString(allowDml)).append("\n");
        sb.append("    allowRunAsync: ").append(toIndentedString(allowRunAsync)).append("\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    databaseName: ").append(toIndentedString(databaseName)).append("\n");
        sb.append("    exposeInSqllab: ").append(toIndentedString(exposeInSqllab)).append("\n");
        sb.append("    externalUrl: ").append(toIndentedString(externalUrl)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    password: ").append(toIndentedString(password)).append("\n");
        sb.append("    sqlalchemyUri: ").append(toIndentedString(sqlalchemyUri)).append("\n");
        sb.append("    sshTunnel: ").append(toIndentedString(sshTunnel)).append("\n");
        sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
        sb.append("    version: ").append(toIndentedString(version)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

