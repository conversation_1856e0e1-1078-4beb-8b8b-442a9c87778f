/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * ChartDataPostProcessingOperation
 */
@JsonPropertyOrder({
    ChartDataPostProcessingOperation.JSON_PROPERTY_OPERATION,
    ChartDataPostProcessingOperation.JSON_PROPERTY_OPTIONS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataPostProcessingOperation {
    /**
     * Post processing operation type
     */
    public enum OperationEnum {
        AGGREGATE("aggregate"),

        BOXPLOT("boxplot"),

        COMPARE("compare"),

        CONTRIBUTION("contribution"),

        CUM("cum"),

        DIFF("diff"),

        ESCAPE_SEPARATOR("escape_separator"),

        FLATTEN("flatten"),

        GEODETIC_PARSE("geodetic_parse"),

        GEOHASH_DECODE("geohash_decode"),

        GEOHASH_ENCODE("geohash_encode"),

        PIVOT("pivot"),

        PROPHET("prophet"),

        RENAME("rename"),

        RESAMPLE("resample"),

        ROLLING("rolling"),

        SELECT("select"),

        SORT("sort"),

        UNESCAPE_SEPARATOR("unescape_separator");

        private String value;

        OperationEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static OperationEnum fromValue(String value) {
            for (OperationEnum b : OperationEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_OPERATION = "operation";
    private OperationEnum operation;

    public static final String JSON_PROPERTY_OPTIONS = "options";
    private Object options;

    public ChartDataPostProcessingOperation() {
    }

    public ChartDataPostProcessingOperation operation(OperationEnum operation) {

        this.operation = operation;
        return this;
    }

    /**
     * Post processing operation type
     *
     * @return operation
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_OPERATION)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public OperationEnum getOperation() {
        return operation;
    }


    @JsonProperty(JSON_PROPERTY_OPERATION)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setOperation(OperationEnum operation) {
        this.operation = operation;
    }


    public ChartDataPostProcessingOperation options(Object options) {

        this.options = options;
        return this;
    }

    /**
     * Options specifying how to perform the operation. Please refer to the respective post processing operation option schemas. For example, &#x60;ChartDataPostProcessingOperationOptions&#x60; specifies the required options for the pivot operation.
     *
     * @return options
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OPTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getOptions() {
        return options;
    }


    @JsonProperty(JSON_PROPERTY_OPTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOptions(Object options) {
        this.options = options;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataPostProcessingOperation chartDataPostProcessingOperation = (ChartDataPostProcessingOperation) o;
        return Objects.equals(this.operation, chartDataPostProcessingOperation.operation) &&
            Objects.equals(this.options, chartDataPostProcessingOperation.options);
    }

    @Override
    public int hashCode() {
        return Objects.hash(operation, options);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataPostProcessingOperation {\n");
        sb.append("    operation: ").append(toIndentedString(operation)).append("\n");
        sb.append("    options: ").append(toIndentedString(options)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

