/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ExploreContextSchema
 */
@JsonPropertyOrder({
    ExploreContextSchema.JSON_PROPERTY_DATASET,
    ExploreContextSchema.JSON_PROPERTY_FORM_DATA,
    ExploreContextSchema.JSON_PROPERTY_MESSAGE,
    ExploreContextSchema.JSON_PROPERTY_SLICE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ExploreContextSchema {
    public static final String JSON_PROPERTY_DATASET = "dataset";
    private Dataset dataset;

    public static final String JSON_PROPERTY_FORM_DATA = "form_data";
    private Object formData;

    public static final String JSON_PROPERTY_MESSAGE = "message";
    private String message;

    public static final String JSON_PROPERTY_SLICE = "slice";
    private Slice slice;

    public ExploreContextSchema() {
    }

    public ExploreContextSchema dataset(Dataset dataset) {

        this.dataset = dataset;
        return this;
    }

    /**
     * Get dataset
     *
     * @return dataset
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Dataset getDataset() {
        return dataset;
    }


    @JsonProperty(JSON_PROPERTY_DATASET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDataset(Dataset dataset) {
        this.dataset = dataset;
    }


    public ExploreContextSchema formData(Object formData) {

        this.formData = formData;
        return this;
    }

    /**
     * Form data from the Explore controls used to form the chart&#39;s data query.
     *
     * @return formData
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getFormData() {
        return formData;
    }


    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFormData(Object formData) {
        this.formData = formData;
    }


    public ExploreContextSchema message(String message) {

        this.message = message;
        return this;
    }

    /**
     * Any message related to the processed request.
     *
     * @return message
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMessage() {
        return message;
    }


    @JsonProperty(JSON_PROPERTY_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMessage(String message) {
        this.message = message;
    }


    public ExploreContextSchema slice(Slice slice) {

        this.slice = slice;
        return this;
    }

    /**
     * Get slice
     *
     * @return slice
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Slice getSlice() {
        return slice;
    }


    @JsonProperty(JSON_PROPERTY_SLICE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSlice(Slice slice) {
        this.slice = slice;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ExploreContextSchema exploreContextSchema = (ExploreContextSchema) o;
        return Objects.equals(this.dataset, exploreContextSchema.dataset) &&
            Objects.equals(this.formData, exploreContextSchema.formData) &&
            Objects.equals(this.message, exploreContextSchema.message) &&
            Objects.equals(this.slice, exploreContextSchema.slice);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dataset, formData, message, slice);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ExploreContextSchema {\n");
        sb.append("    dataset: ").append(toIndentedString(dataset)).append("\n");
        sb.append("    formData: ").append(toIndentedString(formData)).append("\n");
        sb.append("    message: ").append(toIndentedString(message)).append("\n");
        sb.append("    slice: ").append(toIndentedString(slice)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

