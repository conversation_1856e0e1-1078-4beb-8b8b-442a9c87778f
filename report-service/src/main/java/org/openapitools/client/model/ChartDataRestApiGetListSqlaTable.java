/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ChartDataRestApiGetListSqlaTable
 */
@JsonPropertyOrder({
    ChartDataRestApiGetListSqlaTable.JSON_PROPERTY_DEFAULT_ENDPOINT,
    ChartDataRestApiGetListSqlaTable.JSON_PROPERTY_TABLE_NAME
})
@JsonTypeName("ChartDataRestApi.get_list.SqlaTable")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataRestApiGetListSqlaTable {
    public static final String JSON_PROPERTY_DEFAULT_ENDPOINT = "default_endpoint";
    private String defaultEndpoint;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public ChartDataRestApiGetListSqlaTable() {
    }

    public ChartDataRestApiGetListSqlaTable defaultEndpoint(String defaultEndpoint) {

        this.defaultEndpoint = defaultEndpoint;
        return this;
    }

    /**
     * Get defaultEndpoint
     *
     * @return defaultEndpoint
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDefaultEndpoint() {
        return defaultEndpoint;
    }


    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDefaultEndpoint(String defaultEndpoint) {
        this.defaultEndpoint = defaultEndpoint;
    }


    public ChartDataRestApiGetListSqlaTable tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * Get tableName
     *
     * @return tableName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataRestApiGetListSqlaTable chartDataRestApiGetListSqlaTable = (ChartDataRestApiGetListSqlaTable) o;
        return Objects.equals(this.defaultEndpoint, chartDataRestApiGetListSqlaTable.defaultEndpoint) &&
            Objects.equals(this.tableName, chartDataRestApiGetListSqlaTable.tableName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(defaultEndpoint, tableName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataRestApiGetListSqlaTable {\n");
        sb.append("    defaultEndpoint: ").append(toIndentedString(defaultEndpoint)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

