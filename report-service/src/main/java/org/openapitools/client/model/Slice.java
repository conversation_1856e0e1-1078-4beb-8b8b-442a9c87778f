/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Slice
 */
@JsonPropertyOrder({
    Slice.JSON_PROPERTY_CACHE_TIMEOUT,
    Slice.JSON_PROPERTY_CERTIFICATION_DETAILS,
    Slice.JSON_PROPERTY_CERTIFIED_BY,
    Slice.JSON_PROPERTY_CHANGED_ON,
    Slice.JSON_PROPERTY_CHANGED_ON_HUMANIZED,
    Slice.JSO<PERSON>_PROPERTY_DATASOURCE,
    Slice.JSON_PROPERTY_DESCRIPTION,
    Slice.JSON_PROPERTY_DESCRIPTION_MARKEDDOWN,
    Slice.JSON_PROPERTY_EDIT_URL,
    Slice.JSON_PROPERTY_FORM_DATA,
    Slice.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    Slice.JSON_PROPERTY_MODIFIED,
    Slice.JSON_PROPERTY_OWNERS,
    Slice.JSON_PROPERTY_QUERY_CONTEXT,
    Slice.JSON_PROPERTY_SLICE_ID,
    Slice.JSON_PROPERTY_SLICE_NAME,
    Slice.JSON_PROPERTY_SLICE_URL
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class Slice {
    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CHANGED_ON_HUMANIZED = "changed_on_humanized";
    private String changedOnHumanized;

    public static final String JSON_PROPERTY_DATASOURCE = "datasource";
    private String datasource;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_DESCRIPTION_MARKEDDOWN = "description_markeddown";
    private String descriptionMarkeddown;

    public static final String JSON_PROPERTY_EDIT_URL = "edit_url";
    private String editUrl;

    public static final String JSON_PROPERTY_FORM_DATA = "form_data";
    private Object formData;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_MODIFIED = "modified";
    private String modified;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<Integer> owners;

    public static final String JSON_PROPERTY_QUERY_CONTEXT = "query_context";
    private Object queryContext;

    public static final String JSON_PROPERTY_SLICE_ID = "slice_id";
    private Integer sliceId;

    public static final String JSON_PROPERTY_SLICE_NAME = "slice_name";
    private String sliceName;

    public static final String JSON_PROPERTY_SLICE_URL = "slice_url";
    private String sliceUrl;

    public Slice() {
    }

    public Slice cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Duration (in seconds) of the caching timeout for this chart.
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public Slice certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Details of the certification.
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public Slice certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Person or group that has certified this dashboard.
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    public Slice changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Timestamp of the last modification.
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public Slice changedOnHumanized(String changedOnHumanized) {

        this.changedOnHumanized = changedOnHumanized;
        return this;
    }

    /**
     * Timestamp of the last modification in human readable form.
     *
     * @return changedOnHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getChangedOnHumanized() {
        return changedOnHumanized;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOnHumanized(String changedOnHumanized) {
        this.changedOnHumanized = changedOnHumanized;
    }


    public Slice datasource(String datasource) {

        this.datasource = datasource;
        return this;
    }

    /**
     * Datasource identifier.
     *
     * @return datasource
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatasource() {
        return datasource;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasource(String datasource) {
        this.datasource = datasource;
    }


    public Slice description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Slice description.
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public Slice descriptionMarkeddown(String descriptionMarkeddown) {

        this.descriptionMarkeddown = descriptionMarkeddown;
        return this;
    }

    /**
     * Sanitized HTML version of the chart description.
     *
     * @return descriptionMarkeddown
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION_MARKEDDOWN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescriptionMarkeddown() {
        return descriptionMarkeddown;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION_MARKEDDOWN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescriptionMarkeddown(String descriptionMarkeddown) {
        this.descriptionMarkeddown = descriptionMarkeddown;
    }


    public Slice editUrl(String editUrl) {

        this.editUrl = editUrl;
        return this;
    }

    /**
     * The URL for editing the slice.
     *
     * @return editUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EDIT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getEditUrl() {
        return editUrl;
    }


    @JsonProperty(JSON_PROPERTY_EDIT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEditUrl(String editUrl) {
        this.editUrl = editUrl;
    }


    public Slice formData(Object formData) {

        this.formData = formData;
        return this;
    }

    /**
     * Form data associated with the slice.
     *
     * @return formData
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getFormData() {
        return formData;
    }


    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFormData(Object formData) {
        this.formData = formData;
    }


    public Slice isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * If the chart is managed outside externally.
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public Slice modified(String modified) {

        this.modified = modified;
        return this;
    }

    /**
     * Last modification in human readable form.
     *
     * @return modified
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MODIFIED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getModified() {
        return modified;
    }


    @JsonProperty(JSON_PROPERTY_MODIFIED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setModified(String modified) {
        this.modified = modified;
    }


    public Slice owners(List<Integer> owners) {

        this.owners = owners;
        return this;
    }

    public Slice addOwnersItem(Integer ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * Owners identifiers.
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<Integer> owners) {
        this.owners = owners;
    }


    public Slice queryContext(Object queryContext) {

        this.queryContext = queryContext;
        return this;
    }

    /**
     * The context associated with the query.
     *
     * @return queryContext
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getQueryContext() {
        return queryContext;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryContext(Object queryContext) {
        this.queryContext = queryContext;
    }


    public Slice sliceId(Integer sliceId) {

        this.sliceId = sliceId;
        return this;
    }

    /**
     * The slice ID.
     *
     * @return sliceId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getSliceId() {
        return sliceId;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceId(Integer sliceId) {
        this.sliceId = sliceId;
    }


    public Slice sliceName(String sliceName) {

        this.sliceName = sliceName;
        return this;
    }

    /**
     * The slice name.
     *
     * @return sliceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSliceName() {
        return sliceName;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceName(String sliceName) {
        this.sliceName = sliceName;
    }


    public Slice sliceUrl(String sliceUrl) {

        this.sliceUrl = sliceUrl;
        return this;
    }

    /**
     * The slice URL.
     *
     * @return sliceUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSliceUrl() {
        return sliceUrl;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceUrl(String sliceUrl) {
        this.sliceUrl = sliceUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Slice slice = (Slice) o;
        return Objects.equals(this.cacheTimeout, slice.cacheTimeout) &&
            Objects.equals(this.certificationDetails, slice.certificationDetails) &&
            Objects.equals(this.certifiedBy, slice.certifiedBy) &&
            Objects.equals(this.changedOn, slice.changedOn) &&
            Objects.equals(this.changedOnHumanized, slice.changedOnHumanized) &&
            Objects.equals(this.datasource, slice.datasource) &&
            Objects.equals(this.description, slice.description) &&
            Objects.equals(this.descriptionMarkeddown, slice.descriptionMarkeddown) &&
            Objects.equals(this.editUrl, slice.editUrl) &&
            Objects.equals(this.formData, slice.formData) &&
            Objects.equals(this.isManagedExternally, slice.isManagedExternally) &&
            Objects.equals(this.modified, slice.modified) &&
            Objects.equals(this.owners, slice.owners) &&
            Objects.equals(this.queryContext, slice.queryContext) &&
            Objects.equals(this.sliceId, slice.sliceId) &&
            Objects.equals(this.sliceName, slice.sliceName) &&
            Objects.equals(this.sliceUrl, slice.sliceUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cacheTimeout, certificationDetails, certifiedBy, changedOn, changedOnHumanized, datasource, description, descriptionMarkeddown, editUrl, formData, isManagedExternally, modified, owners, queryContext, sliceId, sliceName, sliceUrl);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class Slice {\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    changedOnHumanized: ").append(toIndentedString(changedOnHumanized)).append("\n");
        sb.append("    datasource: ").append(toIndentedString(datasource)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    descriptionMarkeddown: ").append(toIndentedString(descriptionMarkeddown)).append("\n");
        sb.append("    editUrl: ").append(toIndentedString(editUrl)).append("\n");
        sb.append("    formData: ").append(toIndentedString(formData)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    modified: ").append(toIndentedString(modified)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    queryContext: ").append(toIndentedString(queryContext)).append("\n");
        sb.append("    sliceId: ").append(toIndentedString(sliceId)).append("\n");
        sb.append("    sliceName: ").append(toIndentedString(sliceName)).append("\n");
        sb.append("    sliceUrl: ").append(toIndentedString(sliceUrl)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

