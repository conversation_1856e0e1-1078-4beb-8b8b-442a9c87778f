/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * FormDataPutSchema
 */
@JsonPropertyOrder({
    FormDataPutSchema.JSON_PROPERTY_CHART_ID,
    FormDataPutSchema.JSON_PROPERTY_DATASOURCE_ID,
    FormDataPutSchema.JSON_PROPERTY_DATASOURCE_TYPE,
    FormDataPutSchema.JSON_PROPERTY_FORM_DATA
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class FormDataPutSchema {
    public static final String JSON_PROPERTY_CHART_ID = "chart_id";
    private Integer chartId;

    public static final String JSON_PROPERTY_DATASOURCE_ID = "datasource_id";
    private Integer datasourceId;

    /**
     * The datasource type
     */
    public enum DatasourceTypeEnum {
        SL_TABLE("sl_table"),

        TABLE("table"),

        DATASET("dataset"),

        QUERY("query"),

        SAVED_QUERY("saved_query"),

        VIEW("view");

        private String value;

        DatasourceTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static DatasourceTypeEnum fromValue(String value) {
            for (DatasourceTypeEnum b : DatasourceTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_DATASOURCE_TYPE = "datasource_type";
    private DatasourceTypeEnum datasourceType;

    public static final String JSON_PROPERTY_FORM_DATA = "form_data";
    private String formData;

    public FormDataPutSchema() {
    }

    public FormDataPutSchema chartId(Integer chartId) {

        this.chartId = chartId;
        return this;
    }

    /**
     * The chart ID
     *
     * @return chartId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHART_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getChartId() {
        return chartId;
    }


    @JsonProperty(JSON_PROPERTY_CHART_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChartId(Integer chartId) {
        this.chartId = chartId;
    }


    public FormDataPutSchema datasourceId(Integer datasourceId) {

        this.datasourceId = datasourceId;
        return this;
    }

    /**
     * The datasource ID
     *
     * @return datasourceId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getDatasourceId() {
        return datasourceId;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatasourceId(Integer datasourceId) {
        this.datasourceId = datasourceId;
    }


    public FormDataPutSchema datasourceType(DatasourceTypeEnum datasourceType) {

        this.datasourceType = datasourceType;
        return this;
    }

    /**
     * The datasource type
     *
     * @return datasourceType
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public DatasourceTypeEnum getDatasourceType() {
        return datasourceType;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatasourceType(DatasourceTypeEnum datasourceType) {
        this.datasourceType = datasourceType;
    }


    public FormDataPutSchema formData(String formData) {

        this.formData = formData;
        return this;
    }

    /**
     * Any type of JSON supported text.
     *
     * @return formData
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getFormData() {
        return formData;
    }


    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setFormData(String formData) {
        this.formData = formData;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FormDataPutSchema formDataPutSchema = (FormDataPutSchema) o;
        return Objects.equals(this.chartId, formDataPutSchema.chartId) &&
            Objects.equals(this.datasourceId, formDataPutSchema.datasourceId) &&
            Objects.equals(this.datasourceType, formDataPutSchema.datasourceType) &&
            Objects.equals(this.formData, formDataPutSchema.formData);
    }

    @Override
    public int hashCode() {
        return Objects.hash(chartId, datasourceId, datasourceType, formData);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class FormDataPutSchema {\n");
        sb.append("    chartId: ").append(toIndentedString(chartId)).append("\n");
        sb.append("    datasourceId: ").append(toIndentedString(datasourceId)).append("\n");
        sb.append("    datasourceType: ").append(toIndentedString(datasourceType)).append("\n");
        sb.append("    formData: ").append(toIndentedString(formData)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

