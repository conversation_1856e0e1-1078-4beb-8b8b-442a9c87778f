/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1MenuGet200ResponseResultInner
 */
@JsonPropertyOrder({
    ApiV1MenuGet200ResponseResultInner.JSON_PROPERTY_CHILDS,
    ApiV1MenuGet200ResponseResultInner.JSON_PROPERTY_ICON,
    ApiV1MenuGet200ResponseResultInner.JSON_PROPERTY_LABEL,
    ApiV1MenuGet200ResponseResultInner.JSON_PROPERTY_NAME,
    ApiV1MenuGet200ResponseResultInner.JSON_PROPERTY_URL
})
@JsonTypeName("_api_v1_menu__get_200_response_result_inner")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1MenuGet200ResponseResultInner {
    public static final String JSON_PROPERTY_CHILDS = "childs";
    private List<Object> childs;

    public static final String JSON_PROPERTY_ICON = "icon";
    private String icon;

    public static final String JSON_PROPERTY_LABEL = "label";
    private String label;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_URL = "url";
    private String url;

    public ApiV1MenuGet200ResponseResultInner() {
    }

    public ApiV1MenuGet200ResponseResultInner childs(List<Object> childs) {

        this.childs = childs;
        return this;
    }

    public ApiV1MenuGet200ResponseResultInner addChildsItem(Object childsItem) {
        if (this.childs == null) {
            this.childs = new ArrayList<>();
        }
        this.childs.add(childsItem);
        return this;
    }

    /**
     * Get childs
     *
     * @return childs
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHILDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getChilds() {
        return childs;
    }


    @JsonProperty(JSON_PROPERTY_CHILDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChilds(List<Object> childs) {
        this.childs = childs;
    }


    public ApiV1MenuGet200ResponseResultInner icon(String icon) {

        this.icon = icon;
        return this;
    }

    /**
     * Icon name to show for this menu item
     *
     * @return icon
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ICON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getIcon() {
        return icon;
    }


    @JsonProperty(JSON_PROPERTY_ICON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIcon(String icon) {
        this.icon = icon;
    }


    public ApiV1MenuGet200ResponseResultInner label(String label) {

        this.label = label;
        return this;
    }

    /**
     * Pretty name for the menu item
     *
     * @return label
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLabel() {
        return label;
    }


    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLabel(String label) {
        this.label = label;
    }


    public ApiV1MenuGet200ResponseResultInner name(String name) {

        this.name = name;
        return this;
    }

    /**
     * The internal menu item name, maps to permission_name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public ApiV1MenuGet200ResponseResultInner url(String url) {

        this.url = url;
        return this;
    }

    /**
     * The URL for the menu item
     *
     * @return url
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUrl() {
        return url;
    }


    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1MenuGet200ResponseResultInner apiV1MenuGet200ResponseResultInner = (ApiV1MenuGet200ResponseResultInner) o;
        return Objects.equals(this.childs, apiV1MenuGet200ResponseResultInner.childs) &&
            Objects.equals(this.icon, apiV1MenuGet200ResponseResultInner.icon) &&
            Objects.equals(this.label, apiV1MenuGet200ResponseResultInner.label) &&
            Objects.equals(this.name, apiV1MenuGet200ResponseResultInner.name) &&
            Objects.equals(this.url, apiV1MenuGet200ResponseResultInner.url);
    }

    @Override
    public int hashCode() {
        return Objects.hash(childs, icon, label, name, url);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1MenuGet200ResponseResultInner {\n");
        sb.append("    childs: ").append(toIndentedString(childs)).append("\n");
        sb.append("    icon: ").append(toIndentedString(icon)).append("\n");
        sb.append("    label: ").append(toIndentedString(label)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    url: ").append(toIndentedString(url)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

