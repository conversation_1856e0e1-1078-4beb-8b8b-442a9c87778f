/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * StopQuerySchema
 */
@JsonPropertyOrder({
    StopQuerySchema.JSON_PROPERTY_CLIENT_ID
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class StopQuerySchema {
    public static final String JSON_PROPERTY_CLIENT_ID = "client_id";
    private String clientId;

    public StopQuerySchema() {
    }

    public StopQuerySchema clientId(String clientId) {

        this.clientId = clientId;
        return this;
    }

    /**
     * Get clientId
     *
     * @return clientId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CLIENT_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getClientId() {
        return clientId;
    }


    @JsonProperty(JSON_PROPERTY_CLIENT_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setClientId(String clientId) {
        this.clientId = clientId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StopQuerySchema stopQuerySchema = (StopQuerySchema) o;
        return Objects.equals(this.clientId, stopQuerySchema.clientId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clientId);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class StopQuerySchema {\n");
        sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

