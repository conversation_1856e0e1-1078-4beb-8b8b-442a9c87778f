/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * DashboardGetResponseSchema
 */
@JsonPropertyOrder({
    DashboardGetResponseSchema.JSON_PROPERTY_CERTIFICATION_DETAILS,
    DashboardGetResponseSchema.JSON_PROPERTY_CERTIFIED_BY,
    DashboardGetResponseSchema.JSON_PROPERTY_CHANGED_BY,
    DashboardGetResponseSchema.JSON_PROPERTY_CHANGED_BY_NAME,
    DashboardGetResponseSchema.JSON_PROPERTY_CHANGED_ON,
    DashboardGetResponseSchema.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    DashboardGetResponseSchema.JSON_PROPERTY_CHARTS,
    DashboardGetResponseSchema.JSON_PROPERTY_CSS,
    DashboardGetResponseSchema.JSON_PROPERTY_DASHBOARD_TITLE,
    DashboardGetResponseSchema.JSON_PROPERTY_ID,
    DashboardGetResponseSchema.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    DashboardGetResponseSchema.JSON_PROPERTY_JSON_METADATA,
    DashboardGetResponseSchema.JSON_PROPERTY_OWNERS,
    DashboardGetResponseSchema.JSON_PROPERTY_POSITION_JSON,
    DashboardGetResponseSchema.JSON_PROPERTY_PUBLISHED,
    DashboardGetResponseSchema.JSON_PROPERTY_ROLES,
    DashboardGetResponseSchema.JSON_PROPERTY_SLUG,
    DashboardGetResponseSchema.JSON_PROPERTY_TAGS,
    DashboardGetResponseSchema.JSON_PROPERTY_THUMBNAIL_URL,
    DashboardGetResponseSchema.JSON_PROPERTY_URL
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DashboardGetResponseSchema {
    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private User changedBy;

    public static final String JSON_PROPERTY_CHANGED_BY_NAME = "changed_by_name";
    private String changedByName;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private String changedOnDeltaHumanized;

    public static final String JSON_PROPERTY_CHARTS = "charts";
    private List<String> charts;

    public static final String JSON_PROPERTY_CSS = "css";
    private String css;

    public static final String JSON_PROPERTY_DASHBOARD_TITLE = "dashboard_title";
    private String dashboardTitle;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_JSON_METADATA = "json_metadata";
    private String jsonMetadata;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<User> owners;

    public static final String JSON_PROPERTY_POSITION_JSON = "position_json";
    private String positionJson;

    public static final String JSON_PROPERTY_PUBLISHED = "published";
    private Boolean published;

    public static final String JSON_PROPERTY_ROLES = "roles";
    private List<Roles> roles;

    public static final String JSON_PROPERTY_SLUG = "slug";
    private String slug;

    public static final String JSON_PROPERTY_TAGS = "tags";
    private List<Tag1> tags;

    public static final String JSON_PROPERTY_THUMBNAIL_URL = "thumbnail_url";
    private String thumbnailUrl;

    public static final String JSON_PROPERTY_URL = "url";
    private String url;

    public DashboardGetResponseSchema() {
    }

    public DashboardGetResponseSchema certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Details of the certification
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public DashboardGetResponseSchema certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Person or group that has certified this dashboard
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    public DashboardGetResponseSchema changedBy(User changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public User getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(User changedBy) {
        this.changedBy = changedBy;
    }


    public DashboardGetResponseSchema changedByName(String changedByName) {

        this.changedByName = changedByName;
        return this;
    }

    /**
     * Get changedByName
     *
     * @return changedByName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getChangedByName() {
        return changedByName;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedByName(String changedByName) {
        this.changedByName = changedByName;
    }


    public DashboardGetResponseSchema changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public DashboardGetResponseSchema changedOnDeltaHumanized(String changedOnDeltaHumanized) {

        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        return this;
    }

    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOnDeltaHumanized(String changedOnDeltaHumanized) {
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
    }


    public DashboardGetResponseSchema charts(List<String> charts) {

        this.charts = charts;
        return this;
    }

    public DashboardGetResponseSchema addChartsItem(String chartsItem) {
        if (this.charts == null) {
            this.charts = new ArrayList<>();
        }
        this.charts.add(chartsItem);
        return this;
    }

    /**
     * Get charts
     *
     * @return charts
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHARTS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getCharts() {
        return charts;
    }


    @JsonProperty(JSON_PROPERTY_CHARTS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCharts(List<String> charts) {
        this.charts = charts;
    }


    public DashboardGetResponseSchema css(String css) {

        this.css = css;
        return this;
    }

    /**
     * Override CSS for the dashboard.
     *
     * @return css
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCss() {
        return css;
    }


    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCss(String css) {
        this.css = css;
    }


    public DashboardGetResponseSchema dashboardTitle(String dashboardTitle) {

        this.dashboardTitle = dashboardTitle;
        return this;
    }

    /**
     * A title for the dashboard.
     *
     * @return dashboardTitle
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDashboardTitle() {
        return dashboardTitle;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardTitle(String dashboardTitle) {
        this.dashboardTitle = dashboardTitle;
    }


    public DashboardGetResponseSchema id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DashboardGetResponseSchema isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public DashboardGetResponseSchema jsonMetadata(String jsonMetadata) {

        this.jsonMetadata = jsonMetadata;
        return this;
    }

    /**
     * This JSON object is generated dynamically when clicking the save or overwrite button in the dashboard view. It is exposed here for reference and for power users who may want to alter  specific parameters.
     *
     * @return jsonMetadata
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJsonMetadata() {
        return jsonMetadata;
    }


    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJsonMetadata(String jsonMetadata) {
        this.jsonMetadata = jsonMetadata;
    }


    public DashboardGetResponseSchema owners(List<User> owners) {

        this.owners = owners;
        return this;
    }

    public DashboardGetResponseSchema addOwnersItem(User ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<User> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<User> owners) {
        this.owners = owners;
    }


    public DashboardGetResponseSchema positionJson(String positionJson) {

        this.positionJson = positionJson;
        return this;
    }

    /**
     * This json object describes the positioning of the widgets in the dashboard. It is dynamically generated when adjusting the widgets size and positions by using drag &amp; drop in the dashboard view
     *
     * @return positionJson
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_POSITION_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPositionJson() {
        return positionJson;
    }


    @JsonProperty(JSON_PROPERTY_POSITION_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPositionJson(String positionJson) {
        this.positionJson = positionJson;
    }


    public DashboardGetResponseSchema published(Boolean published) {

        this.published = published;
        return this;
    }

    /**
     * Get published
     *
     * @return published
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PUBLISHED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getPublished() {
        return published;
    }


    @JsonProperty(JSON_PROPERTY_PUBLISHED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPublished(Boolean published) {
        this.published = published;
    }


    public DashboardGetResponseSchema roles(List<Roles> roles) {

        this.roles = roles;
        return this;
    }

    public DashboardGetResponseSchema addRolesItem(Roles rolesItem) {
        if (this.roles == null) {
            this.roles = new ArrayList<>();
        }
        this.roles.add(rolesItem);
        return this;
    }

    /**
     * Get roles
     *
     * @return roles
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Roles> getRoles() {
        return roles;
    }


    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRoles(List<Roles> roles) {
        this.roles = roles;
    }


    public DashboardGetResponseSchema slug(String slug) {

        this.slug = slug;
        return this;
    }

    /**
     * Get slug
     *
     * @return slug
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLUG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSlug() {
        return slug;
    }


    @JsonProperty(JSON_PROPERTY_SLUG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSlug(String slug) {
        this.slug = slug;
    }


    public DashboardGetResponseSchema tags(List<Tag1> tags) {

        this.tags = tags;
        return this;
    }

    public DashboardGetResponseSchema addTagsItem(Tag1 tagsItem) {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }
        this.tags.add(tagsItem);
        return this;
    }

    /**
     * Get tags
     *
     * @return tags
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Tag1> getTags() {
        return tags;
    }


    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTags(List<Tag1> tags) {
        this.tags = tags;
    }


    public DashboardGetResponseSchema thumbnailUrl(String thumbnailUrl) {

        this.thumbnailUrl = thumbnailUrl;
        return this;
    }

    /**
     * Get thumbnailUrl
     *
     * @return thumbnailUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getThumbnailUrl() {
        return thumbnailUrl;
    }


    @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }


    public DashboardGetResponseSchema url(String url) {

        this.url = url;
        return this;
    }

    /**
     * Get url
     *
     * @return url
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUrl() {
        return url;
    }


    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DashboardGetResponseSchema dashboardGetResponseSchema = (DashboardGetResponseSchema) o;
        return Objects.equals(this.certificationDetails, dashboardGetResponseSchema.certificationDetails) &&
            Objects.equals(this.certifiedBy, dashboardGetResponseSchema.certifiedBy) &&
            Objects.equals(this.changedBy, dashboardGetResponseSchema.changedBy) &&
            Objects.equals(this.changedByName, dashboardGetResponseSchema.changedByName) &&
            Objects.equals(this.changedOn, dashboardGetResponseSchema.changedOn) &&
            Objects.equals(this.changedOnDeltaHumanized, dashboardGetResponseSchema.changedOnDeltaHumanized) &&
            Objects.equals(this.charts, dashboardGetResponseSchema.charts) &&
            Objects.equals(this.css, dashboardGetResponseSchema.css) &&
            Objects.equals(this.dashboardTitle, dashboardGetResponseSchema.dashboardTitle) &&
            Objects.equals(this.id, dashboardGetResponseSchema.id) &&
            Objects.equals(this.isManagedExternally, dashboardGetResponseSchema.isManagedExternally) &&
            Objects.equals(this.jsonMetadata, dashboardGetResponseSchema.jsonMetadata) &&
            Objects.equals(this.owners, dashboardGetResponseSchema.owners) &&
            Objects.equals(this.positionJson, dashboardGetResponseSchema.positionJson) &&
            Objects.equals(this.published, dashboardGetResponseSchema.published) &&
            Objects.equals(this.roles, dashboardGetResponseSchema.roles) &&
            Objects.equals(this.slug, dashboardGetResponseSchema.slug) &&
            Objects.equals(this.tags, dashboardGetResponseSchema.tags) &&
            Objects.equals(this.thumbnailUrl, dashboardGetResponseSchema.thumbnailUrl) &&
            Objects.equals(this.url, dashboardGetResponseSchema.url);
    }

    @Override
    public int hashCode() {
        return Objects.hash(certificationDetails, certifiedBy, changedBy, changedByName, changedOn, changedOnDeltaHumanized, charts, css, dashboardTitle, id, isManagedExternally, jsonMetadata, owners, positionJson, published, roles, slug, tags, thumbnailUrl, url);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DashboardGetResponseSchema {\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedByName: ").append(toIndentedString(changedByName)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    charts: ").append(toIndentedString(charts)).append("\n");
        sb.append("    css: ").append(toIndentedString(css)).append("\n");
        sb.append("    dashboardTitle: ").append(toIndentedString(dashboardTitle)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    jsonMetadata: ").append(toIndentedString(jsonMetadata)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    positionJson: ").append(toIndentedString(positionJson)).append("\n");
        sb.append("    published: ").append(toIndentedString(published)).append("\n");
        sb.append("    roles: ").append(toIndentedString(roles)).append("\n");
        sb.append("    slug: ").append(toIndentedString(slug)).append("\n");
        sb.append("    tags: ").append(toIndentedString(tags)).append("\n");
        sb.append("    thumbnailUrl: ").append(toIndentedString(thumbnailUrl)).append("\n");
        sb.append("    url: ").append(toIndentedString(url)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

