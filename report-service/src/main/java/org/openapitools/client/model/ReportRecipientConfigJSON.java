/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ReportRecipientConfigJSON
 */
@JsonPropertyOrder({
    ReportRecipientConfigJSON.JSON_PROPERTY_TARGET
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ReportRecipientConfigJSON {
    public static final String JSON_PROPERTY_TARGET = "target";
    private String target;

    public ReportRecipientConfigJSON() {
    }

    public ReportRecipientConfigJSON target(String target) {

        this.target = target;
        return this;
    }

    /**
     * Get target
     *
     * @return target
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TARGET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTarget() {
        return target;
    }


    @JsonProperty(JSON_PROPERTY_TARGET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTarget(String target) {
        this.target = target;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ReportRecipientConfigJSON reportRecipientConfigJSON = (ReportRecipientConfigJSON) o;
        return Objects.equals(this.target, reportRecipientConfigJSON.target);
    }

    @Override
    public int hashCode() {
        return Objects.hash(target);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ReportRecipientConfigJSON {\n");
        sb.append("    target: ").append(toIndentedString(target)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

