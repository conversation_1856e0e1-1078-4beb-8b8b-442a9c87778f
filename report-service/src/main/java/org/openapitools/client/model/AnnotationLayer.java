/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.math.BigDecimal;
import java.util.*;

/**
 * AnnotationLayer
 */
@JsonPropertyOrder({
    AnnotationLayer.JSON_PROPERTY_ANNOTATION_TYPE,
    AnnotationLayer.JSON_PROPERTY_COLOR,
    AnnotationLayer.JSON_PROPERTY_DESCRIPTION_COLUMNS,
    AnnotationLayer.JSON_PROPERTY_HIDE_LINE,
    AnnotationLayer.JSON_PROPERTY_INTERVAL_END_COLUMN,
    AnnotationLayer.JSON_PROPERTY_NAME,
    AnnotationLayer.J<PERSON><PERSON>_PROPERTY_OPACITY,
    AnnotationLayer.J<PERSON><PERSON>_PROPERTY_OVERRIDES,
    AnnotationLayer.JSO<PERSON>_PROPERTY_SHOW,
    AnnotationLayer.JSO<PERSON>_PROPERTY_SHOW_LABEL,
    AnnotationLayer.JSON_PROPERTY_SHOW_MARKERS,
    AnnotationLayer.JSON_PROPERTY_SOURCE_TYPE,
    AnnotationLayer.JSON_PROPERTY_STYLE,
    AnnotationLayer.JSON_PROPERTY_TIME_COLUMN,
    AnnotationLayer.JSON_PROPERTY_TITLE_COLUMN,
    AnnotationLayer.JSON_PROPERTY_VALUE,
    AnnotationLayer.JSON_PROPERTY_WIDTH
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class AnnotationLayer {
    /**
     * Type of annotation layer
     */
    public enum AnnotationTypeEnum {
        FORMULA("FORMULA"),

        INTERVAL("INTERVAL"),

        EVENT("EVENT"),

        TIME_SERIES("TIME_SERIES");

        private String value;

        AnnotationTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static AnnotationTypeEnum fromValue(String value) {
            for (AnnotationTypeEnum b : AnnotationTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_ANNOTATION_TYPE = "annotationType";
    private AnnotationTypeEnum annotationType;

    public static final String JSON_PROPERTY_COLOR = "color";
    private String color;

    public static final String JSON_PROPERTY_DESCRIPTION_COLUMNS = "descriptionColumns";
    private List<String> descriptionColumns;

    public static final String JSON_PROPERTY_HIDE_LINE = "hideLine";
    private Boolean hideLine;

    public static final String JSON_PROPERTY_INTERVAL_END_COLUMN = "intervalEndColumn";
    private String intervalEndColumn;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    /**
     * Opacity of layer
     */
    public enum OpacityEnum {
        EMPTY(""),

        OPACITYLOW("opacityLow"),

        OPACITYMEDIUM("opacityMedium"),

        OPACITYHIGH("opacityHigh");

        private String value;

        OpacityEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static OpacityEnum fromValue(String value) {
            for (OpacityEnum b : OpacityEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            return null;
        }
    }

    public static final String JSON_PROPERTY_OPACITY = "opacity";
    private OpacityEnum opacity;

    public static final String JSON_PROPERTY_OVERRIDES = "overrides";
    private Map<String, Object> overrides;

    public static final String JSON_PROPERTY_SHOW = "show";
    private Boolean show;

    public static final String JSON_PROPERTY_SHOW_LABEL = "showLabel";
    private Boolean showLabel;

    public static final String JSON_PROPERTY_SHOW_MARKERS = "showMarkers";
    private Boolean showMarkers;

    /**
     * Type of source for annotation data
     */
    public enum SourceTypeEnum {
        EMPTY(""),

        LINE("line"),

        NATIVE("NATIVE"),

        TABLE("table");

        private String value;

        SourceTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static SourceTypeEnum fromValue(String value) {
            for (SourceTypeEnum b : SourceTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_SOURCE_TYPE = "sourceType";
    private SourceTypeEnum sourceType;

    /**
     * Line style. Only applies to time-series annotations
     */
    public enum StyleEnum {
        DASHED("dashed"),

        DOTTED("dotted"),

        SOLID("solid"),

        LONGDASHED("longDashed");

        private String value;

        StyleEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static StyleEnum fromValue(String value) {
            for (StyleEnum b : StyleEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_STYLE = "style";
    private StyleEnum style;

    public static final String JSON_PROPERTY_TIME_COLUMN = "timeColumn";
    private String timeColumn;

    public static final String JSON_PROPERTY_TITLE_COLUMN = "titleColumn";
    private String titleColumn;

    public static final String JSON_PROPERTY_VALUE = "value";
    private Object value = null;

    public static final String JSON_PROPERTY_WIDTH = "width";
    private BigDecimal width;

    public AnnotationLayer() {
    }

    public AnnotationLayer annotationType(AnnotationTypeEnum annotationType) {

        this.annotationType = annotationType;
        return this;
    }

    /**
     * Type of annotation layer
     *
     * @return annotationType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ANNOTATION_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public AnnotationTypeEnum getAnnotationType() {
        return annotationType;
    }


    @JsonProperty(JSON_PROPERTY_ANNOTATION_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAnnotationType(AnnotationTypeEnum annotationType) {
        this.annotationType = annotationType;
    }


    public AnnotationLayer color(String color) {

        this.color = color;
        return this;
    }

    /**
     * Layer color
     *
     * @return color
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getColor() {
        return color;
    }


    @JsonProperty(JSON_PROPERTY_COLOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColor(String color) {
        this.color = color;
    }


    public AnnotationLayer descriptionColumns(List<String> descriptionColumns) {

        this.descriptionColumns = descriptionColumns;
        return this;
    }

    public AnnotationLayer addDescriptionColumnsItem(String descriptionColumnsItem) {
        if (this.descriptionColumns == null) {
            this.descriptionColumns = new ArrayList<>();
        }
        this.descriptionColumns.add(descriptionColumnsItem);
        return this;
    }

    /**
     * Columns to use as the description. If none are provided, all will be shown.
     *
     * @return descriptionColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getDescriptionColumns() {
        return descriptionColumns;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescriptionColumns(List<String> descriptionColumns) {
        this.descriptionColumns = descriptionColumns;
    }


    public AnnotationLayer hideLine(Boolean hideLine) {

        this.hideLine = hideLine;
        return this;
    }

    /**
     * Should line be hidden. Only applies to line annotations
     *
     * @return hideLine
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_HIDE_LINE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getHideLine() {
        return hideLine;
    }


    @JsonProperty(JSON_PROPERTY_HIDE_LINE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setHideLine(Boolean hideLine) {
        this.hideLine = hideLine;
    }


    public AnnotationLayer intervalEndColumn(String intervalEndColumn) {

        this.intervalEndColumn = intervalEndColumn;
        return this;
    }

    /**
     * Column containing end of interval. Only applies to interval layers
     *
     * @return intervalEndColumn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_INTERVAL_END_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getIntervalEndColumn() {
        return intervalEndColumn;
    }


    @JsonProperty(JSON_PROPERTY_INTERVAL_END_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIntervalEndColumn(String intervalEndColumn) {
        this.intervalEndColumn = intervalEndColumn;
    }


    public AnnotationLayer name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Name of layer
     *
     * @return name
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setName(String name) {
        this.name = name;
    }


    public AnnotationLayer opacity(OpacityEnum opacity) {

        this.opacity = opacity;
        return this;
    }

    /**
     * Opacity of layer
     *
     * @return opacity
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OPACITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OpacityEnum getOpacity() {
        return opacity;
    }


    @JsonProperty(JSON_PROPERTY_OPACITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOpacity(OpacityEnum opacity) {
        this.opacity = opacity;
    }


    public AnnotationLayer overrides(Map<String, Object> overrides) {

        this.overrides = overrides;
        return this;
    }

    public AnnotationLayer putOverridesItem(String key, Object overridesItem) {
        if (this.overrides == null) {
            this.overrides = new HashMap<>();
        }
        this.overrides.put(key, overridesItem);
        return this;
    }

    /**
     * which properties should be overridable
     *
     * @return overrides
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OVERRIDES)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)

    public Map<String, Object> getOverrides() {
        return overrides;
    }


    @JsonProperty(JSON_PROPERTY_OVERRIDES)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)
    public void setOverrides(Map<String, Object> overrides) {
        this.overrides = overrides;
    }


    public AnnotationLayer show(Boolean show) {

        this.show = show;
        return this;
    }

    /**
     * Should the layer be shown
     *
     * @return show
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_SHOW)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Boolean getShow() {
        return show;
    }


    @JsonProperty(JSON_PROPERTY_SHOW)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setShow(Boolean show) {
        this.show = show;
    }


    public AnnotationLayer showLabel(Boolean showLabel) {

        this.showLabel = showLabel;
        return this;
    }

    /**
     * Should the label always be shown
     *
     * @return showLabel
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SHOW_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getShowLabel() {
        return showLabel;
    }


    @JsonProperty(JSON_PROPERTY_SHOW_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setShowLabel(Boolean showLabel) {
        this.showLabel = showLabel;
    }


    public AnnotationLayer showMarkers(Boolean showMarkers) {

        this.showMarkers = showMarkers;
        return this;
    }

    /**
     * Should markers be shown. Only applies to line annotations.
     *
     * @return showMarkers
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_SHOW_MARKERS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Boolean getShowMarkers() {
        return showMarkers;
    }


    @JsonProperty(JSON_PROPERTY_SHOW_MARKERS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setShowMarkers(Boolean showMarkers) {
        this.showMarkers = showMarkers;
    }


    public AnnotationLayer sourceType(SourceTypeEnum sourceType) {

        this.sourceType = sourceType;
        return this;
    }

    /**
     * Type of source for annotation data
     *
     * @return sourceType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public SourceTypeEnum getSourceType() {
        return sourceType;
    }


    @JsonProperty(JSON_PROPERTY_SOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSourceType(SourceTypeEnum sourceType) {
        this.sourceType = sourceType;
    }


    public AnnotationLayer style(StyleEnum style) {

        this.style = style;
        return this;
    }

    /**
     * Line style. Only applies to time-series annotations
     *
     * @return style
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STYLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public StyleEnum getStyle() {
        return style;
    }


    @JsonProperty(JSON_PROPERTY_STYLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStyle(StyleEnum style) {
        this.style = style;
    }


    public AnnotationLayer timeColumn(String timeColumn) {

        this.timeColumn = timeColumn;
        return this;
    }

    /**
     * Column with event date or interval start date
     *
     * @return timeColumn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTimeColumn() {
        return timeColumn;
    }


    @JsonProperty(JSON_PROPERTY_TIME_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeColumn(String timeColumn) {
        this.timeColumn = timeColumn;
    }


    public AnnotationLayer titleColumn(String titleColumn) {

        this.titleColumn = titleColumn;
        return this;
    }

    /**
     * Column with title
     *
     * @return titleColumn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TITLE_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTitleColumn() {
        return titleColumn;
    }


    @JsonProperty(JSON_PROPERTY_TITLE_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTitleColumn(String titleColumn) {
        this.titleColumn = titleColumn;
    }


    public AnnotationLayer value(Object value) {

        this.value = value;
        return this;
    }

    /**
     * For formula annotations, this contains the formula. For other types, this is the primary key of the source object.
     *
     * @return value
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Object getValue() {
        return value;
    }


    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setValue(Object value) {
        this.value = value;
    }


    public AnnotationLayer width(BigDecimal width) {

        this.width = width;
        return this;
    }

    /**
     * Width of annotation line
     * minimum: 0.0
     *
     * @return width
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_WIDTH)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getWidth() {
        return width;
    }


    @JsonProperty(JSON_PROPERTY_WIDTH)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setWidth(BigDecimal width) {
        this.width = width;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AnnotationLayer annotationLayer = (AnnotationLayer) o;
        return Objects.equals(this.annotationType, annotationLayer.annotationType) &&
            Objects.equals(this.color, annotationLayer.color) &&
            Objects.equals(this.descriptionColumns, annotationLayer.descriptionColumns) &&
            Objects.equals(this.hideLine, annotationLayer.hideLine) &&
            Objects.equals(this.intervalEndColumn, annotationLayer.intervalEndColumn) &&
            Objects.equals(this.name, annotationLayer.name) &&
            Objects.equals(this.opacity, annotationLayer.opacity) &&
            Objects.equals(this.overrides, annotationLayer.overrides) &&
            Objects.equals(this.show, annotationLayer.show) &&
            Objects.equals(this.showLabel, annotationLayer.showLabel) &&
            Objects.equals(this.showMarkers, annotationLayer.showMarkers) &&
            Objects.equals(this.sourceType, annotationLayer.sourceType) &&
            Objects.equals(this.style, annotationLayer.style) &&
            Objects.equals(this.timeColumn, annotationLayer.timeColumn) &&
            Objects.equals(this.titleColumn, annotationLayer.titleColumn) &&
            Objects.equals(this.value, annotationLayer.value) &&
            Objects.equals(this.width, annotationLayer.width);
    }

    @Override
    public int hashCode() {
        return Objects.hash(annotationType, color, descriptionColumns, hideLine, intervalEndColumn, name, opacity, overrides, show, showLabel, showMarkers, sourceType, style, timeColumn, titleColumn, value, width);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class AnnotationLayer {\n");
        sb.append("    annotationType: ").append(toIndentedString(annotationType)).append("\n");
        sb.append("    color: ").append(toIndentedString(color)).append("\n");
        sb.append("    descriptionColumns: ").append(toIndentedString(descriptionColumns)).append("\n");
        sb.append("    hideLine: ").append(toIndentedString(hideLine)).append("\n");
        sb.append("    intervalEndColumn: ").append(toIndentedString(intervalEndColumn)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    opacity: ").append(toIndentedString(opacity)).append("\n");
        sb.append("    overrides: ").append(toIndentedString(overrides)).append("\n");
        sb.append("    show: ").append(toIndentedString(show)).append("\n");
        sb.append("    showLabel: ").append(toIndentedString(showLabel)).append("\n");
        sb.append("    showMarkers: ").append(toIndentedString(showMarkers)).append("\n");
        sb.append("    sourceType: ").append(toIndentedString(sourceType)).append("\n");
        sb.append("    style: ").append(toIndentedString(style)).append("\n");
        sb.append("    timeColumn: ").append(toIndentedString(timeColumn)).append("\n");
        sb.append("    titleColumn: ").append(toIndentedString(titleColumn)).append("\n");
        sb.append("    value: ").append(toIndentedString(value)).append("\n");
        sb.append("    width: ").append(toIndentedString(width)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

