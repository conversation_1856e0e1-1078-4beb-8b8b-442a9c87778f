/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1AnnotationLayerGet200Response
 */
@JsonPropertyOrder({
    ApiV1AnnotationLayerGet200Response.JSON_PROPERTY_COUNT,
    ApiV1AnnotationLayerGet200Response.JSON_PROPERTY_DESCRIPTION_COLUMNS,
    ApiV1AnnotationLayerGet200Response.JSON_PROPERTY_IDS,
    ApiV1AnnotationLayerGet200Response.JSON_PROPERTY_LABEL_COLUMNS,
    ApiV1AnnotationLayerGet200Response.JSON_PROPERTY_LIST_COLUMNS,
    ApiV1AnnotationLayerGet200Response.JSON_PROPERTY_LIST_TITLE,
    ApiV1AnnotationLayerGet200Response.JSON_PROPERTY_ORDER_COLUMNS,
    ApiV1AnnotationLayerGet200Response.JSON_PROPERTY_RESULT
})
@JsonTypeName("_api_v1_annotation_layer__get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1AnnotationLayerGet200Response {
    public static final String JSON_PROPERTY_COUNT = "count";
    private BigDecimal count;

    public static final String JSON_PROPERTY_DESCRIPTION_COLUMNS = "description_columns";
    private ApiV1AnnotationLayerGet200ResponseDescriptionColumns descriptionColumns;

    public static final String JSON_PROPERTY_IDS = "ids";
    private List<String> ids;

    public static final String JSON_PROPERTY_LABEL_COLUMNS = "label_columns";
    private ApiV1AnnotationLayerGet200ResponseLabelColumns labelColumns;

    public static final String JSON_PROPERTY_LIST_COLUMNS = "list_columns";
    private List<String> listColumns;

    public static final String JSON_PROPERTY_LIST_TITLE = "list_title";
    private String listTitle;

    public static final String JSON_PROPERTY_ORDER_COLUMNS = "order_columns";
    private List<String> orderColumns;

    public static final String JSON_PROPERTY_RESULT = "result";
    private List<AnnotationLayerRestApiGetList> result;

    public ApiV1AnnotationLayerGet200Response() {
    }

    public ApiV1AnnotationLayerGet200Response count(BigDecimal count) {

        this.count = count;
        return this;
    }

    /**
     * The total record count on the backend
     *
     * @return count
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getCount() {
        return count;
    }


    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCount(BigDecimal count) {
        this.count = count;
    }


    public ApiV1AnnotationLayerGet200Response descriptionColumns(ApiV1AnnotationLayerGet200ResponseDescriptionColumns descriptionColumns) {

        this.descriptionColumns = descriptionColumns;
        return this;
    }

    /**
     * Get descriptionColumns
     *
     * @return descriptionColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ApiV1AnnotationLayerGet200ResponseDescriptionColumns getDescriptionColumns() {
        return descriptionColumns;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescriptionColumns(ApiV1AnnotationLayerGet200ResponseDescriptionColumns descriptionColumns) {
        this.descriptionColumns = descriptionColumns;
    }


    public ApiV1AnnotationLayerGet200Response ids(List<String> ids) {

        this.ids = ids;
        return this;
    }

    public ApiV1AnnotationLayerGet200Response addIdsItem(String idsItem) {
        if (this.ids == null) {
            this.ids = new ArrayList<>();
        }
        this.ids.add(idsItem);
        return this;
    }

    /**
     * A list of item ids, useful when you don&#39;t know the column id
     *
     * @return ids
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getIds() {
        return ids;
    }


    @JsonProperty(JSON_PROPERTY_IDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIds(List<String> ids) {
        this.ids = ids;
    }


    public ApiV1AnnotationLayerGet200Response labelColumns(ApiV1AnnotationLayerGet200ResponseLabelColumns labelColumns) {

        this.labelColumns = labelColumns;
        return this;
    }

    /**
     * Get labelColumns
     *
     * @return labelColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LABEL_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ApiV1AnnotationLayerGet200ResponseLabelColumns getLabelColumns() {
        return labelColumns;
    }


    @JsonProperty(JSON_PROPERTY_LABEL_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLabelColumns(ApiV1AnnotationLayerGet200ResponseLabelColumns labelColumns) {
        this.labelColumns = labelColumns;
    }


    public ApiV1AnnotationLayerGet200Response listColumns(List<String> listColumns) {

        this.listColumns = listColumns;
        return this;
    }

    public ApiV1AnnotationLayerGet200Response addListColumnsItem(String listColumnsItem) {
        if (this.listColumns == null) {
            this.listColumns = new ArrayList<>();
        }
        this.listColumns.add(listColumnsItem);
        return this;
    }

    /**
     * A list of columns
     *
     * @return listColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LIST_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getListColumns() {
        return listColumns;
    }


    @JsonProperty(JSON_PROPERTY_LIST_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setListColumns(List<String> listColumns) {
        this.listColumns = listColumns;
    }


    public ApiV1AnnotationLayerGet200Response listTitle(String listTitle) {

        this.listTitle = listTitle;
        return this;
    }

    /**
     * A title to render. Will be translated by babel
     *
     * @return listTitle
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LIST_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getListTitle() {
        return listTitle;
    }


    @JsonProperty(JSON_PROPERTY_LIST_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setListTitle(String listTitle) {
        this.listTitle = listTitle;
    }


    public ApiV1AnnotationLayerGet200Response orderColumns(List<String> orderColumns) {

        this.orderColumns = orderColumns;
        return this;
    }

    public ApiV1AnnotationLayerGet200Response addOrderColumnsItem(String orderColumnsItem) {
        if (this.orderColumns == null) {
            this.orderColumns = new ArrayList<>();
        }
        this.orderColumns.add(orderColumnsItem);
        return this;
    }

    /**
     * A list of allowed columns to sort
     *
     * @return orderColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ORDER_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getOrderColumns() {
        return orderColumns;
    }


    @JsonProperty(JSON_PROPERTY_ORDER_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOrderColumns(List<String> orderColumns) {
        this.orderColumns = orderColumns;
    }


    public ApiV1AnnotationLayerGet200Response result(List<AnnotationLayerRestApiGetList> result) {

        this.result = result;
        return this;
    }

    public ApiV1AnnotationLayerGet200Response addResultItem(AnnotationLayerRestApiGetList resultItem) {
        if (this.result == null) {
            this.result = new ArrayList<>();
        }
        this.result.add(resultItem);
        return this;
    }

    /**
     * The result from the get list query
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<AnnotationLayerRestApiGetList> getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(List<AnnotationLayerRestApiGetList> result) {
        this.result = result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1AnnotationLayerGet200Response apiV1AnnotationLayerGet200Response = (ApiV1AnnotationLayerGet200Response) o;
        return Objects.equals(this.count, apiV1AnnotationLayerGet200Response.count) &&
            Objects.equals(this.descriptionColumns, apiV1AnnotationLayerGet200Response.descriptionColumns) &&
            Objects.equals(this.ids, apiV1AnnotationLayerGet200Response.ids) &&
            Objects.equals(this.labelColumns, apiV1AnnotationLayerGet200Response.labelColumns) &&
            Objects.equals(this.listColumns, apiV1AnnotationLayerGet200Response.listColumns) &&
            Objects.equals(this.listTitle, apiV1AnnotationLayerGet200Response.listTitle) &&
            Objects.equals(this.orderColumns, apiV1AnnotationLayerGet200Response.orderColumns) &&
            Objects.equals(this.result, apiV1AnnotationLayerGet200Response.result);
    }

    @Override
    public int hashCode() {
        return Objects.hash(count, descriptionColumns, ids, labelColumns, listColumns, listTitle, orderColumns, result);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1AnnotationLayerGet200Response {\n");
        sb.append("    count: ").append(toIndentedString(count)).append("\n");
        sb.append("    descriptionColumns: ").append(toIndentedString(descriptionColumns)).append("\n");
        sb.append("    ids: ").append(toIndentedString(ids)).append("\n");
        sb.append("    labelColumns: ").append(toIndentedString(labelColumns)).append("\n");
        sb.append("    listColumns: ").append(toIndentedString(listColumns)).append("\n");
        sb.append("    listTitle: ").append(toIndentedString(listTitle)).append("\n");
        sb.append("    orderColumns: ").append(toIndentedString(orderColumns)).append("\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

