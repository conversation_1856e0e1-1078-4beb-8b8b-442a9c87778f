/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * Datasource
 */
@JsonPropertyOrder({
    Datasource.JSON_PROPERTY_DATABASE_NAME,
    Datasource.JSON_PROPERTY_DATASOURCE_NAME,
    Datasource.JSON_PROPERTY_DATASOURCE_TYPE,
    Datasource.JSON_PROPERTY_SCHEMA
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class Datasource {
    public static final String JSON_PROPERTY_DATABASE_NAME = "database_name";
    private String databaseName;

    public static final String JSON_PROPERTY_DATASOURCE_NAME = "datasource_name";
    private String datasourceName;

    /**
     * The type of dataset/datasource identified on &#x60;datasource_id&#x60;.
     */
    public enum DatasourceTypeEnum {
        SL_TABLE("sl_table"),

        TABLE("table"),

        DATASET("dataset"),

        QUERY("query"),

        SAVED_QUERY("saved_query"),

        VIEW("view");

        private String value;

        DatasourceTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static DatasourceTypeEnum fromValue(String value) {
            for (DatasourceTypeEnum b : DatasourceTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_DATASOURCE_TYPE = "datasource_type";
    private DatasourceTypeEnum datasourceType;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public Datasource() {
    }

    public Datasource databaseName(String databaseName) {

        this.databaseName = databaseName;
        return this;
    }

    /**
     * Datasource name
     *
     * @return databaseName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatabaseName() {
        return databaseName;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }


    public Datasource datasourceName(String datasourceName) {

        this.datasourceName = datasourceName;
        return this;
    }

    /**
     * The datasource name.
     *
     * @return datasourceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatasourceName() {
        return datasourceName;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }


    public Datasource datasourceType(DatasourceTypeEnum datasourceType) {

        this.datasourceType = datasourceType;
        return this;
    }

    /**
     * The type of dataset/datasource identified on &#x60;datasource_id&#x60;.
     *
     * @return datasourceType
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public DatasourceTypeEnum getDatasourceType() {
        return datasourceType;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatasourceType(DatasourceTypeEnum datasourceType) {
        this.datasourceType = datasourceType;
    }


    public Datasource schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Datasource schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Datasource datasource = (Datasource) o;
        return Objects.equals(this.databaseName, datasource.databaseName) &&
            Objects.equals(this.datasourceName, datasource.datasourceName) &&
            Objects.equals(this.datasourceType, datasource.datasourceType) &&
            Objects.equals(this.schema, datasource.schema);
    }

    @Override
    public int hashCode() {
        return Objects.hash(databaseName, datasourceName, datasourceType, schema);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class Datasource {\n");
        sb.append("    databaseName: ").append(toIndentedString(databaseName)).append("\n");
        sb.append("    datasourceName: ").append(toIndentedString(datasourceName)).append("\n");
        sb.append("    datasourceType: ").append(toIndentedString(datasourceType)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

