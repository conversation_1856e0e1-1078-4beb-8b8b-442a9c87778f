/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * Database
 */
@JsonPropertyOrder({
    Database.JSON_PROPERTY_ALLOWS_COST_ESTIMATE,
    Database.JSON_PROPERTY_ALLOWS_SUBQUERY,
    Database.JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE,
    Database.JSON_PROPERTY_BACKEND,
    Database.JSON_PROPERTY_DISABLE_DATA_PREVIEW,
    Database.JSON_PROPERTY_EXPLORE_DATABASE_ID,
    Database.JSON_PROPERTY_ID,
    Database.JSON_PROPERTY_NAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class Database {
    public static final String JSON_PROPERTY_ALLOWS_COST_ESTIMATE = "allows_cost_estimate";
    private Boolean allowsCostEstimate;

    public static final String JSON_PROPERTY_ALLOWS_SUBQUERY = "allows_subquery";
    private Boolean allowsSubquery;

    public static final String JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE = "allows_virtual_table_explore";
    private Boolean allowsVirtualTableExplore;

    public static final String JSON_PROPERTY_BACKEND = "backend";
    private String backend;

    public static final String JSON_PROPERTY_DISABLE_DATA_PREVIEW = "disable_data_preview";
    private Boolean disableDataPreview;

    public static final String JSON_PROPERTY_EXPLORE_DATABASE_ID = "explore_database_id";
    private Integer exploreDatabaseId;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public Database() {
    }

    public Database allowsCostEstimate(Boolean allowsCostEstimate) {

        this.allowsCostEstimate = allowsCostEstimate;
        return this;
    }

    /**
     * Get allowsCostEstimate
     *
     * @return allowsCostEstimate
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOWS_COST_ESTIMATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowsCostEstimate() {
        return allowsCostEstimate;
    }


    @JsonProperty(JSON_PROPERTY_ALLOWS_COST_ESTIMATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowsCostEstimate(Boolean allowsCostEstimate) {
        this.allowsCostEstimate = allowsCostEstimate;
    }


    public Database allowsSubquery(Boolean allowsSubquery) {

        this.allowsSubquery = allowsSubquery;
        return this;
    }

    /**
     * Get allowsSubquery
     *
     * @return allowsSubquery
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOWS_SUBQUERY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowsSubquery() {
        return allowsSubquery;
    }


    @JsonProperty(JSON_PROPERTY_ALLOWS_SUBQUERY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowsSubquery(Boolean allowsSubquery) {
        this.allowsSubquery = allowsSubquery;
    }


    public Database allowsVirtualTableExplore(Boolean allowsVirtualTableExplore) {

        this.allowsVirtualTableExplore = allowsVirtualTableExplore;
        return this;
    }

    /**
     * Get allowsVirtualTableExplore
     *
     * @return allowsVirtualTableExplore
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowsVirtualTableExplore() {
        return allowsVirtualTableExplore;
    }


    @JsonProperty(JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowsVirtualTableExplore(Boolean allowsVirtualTableExplore) {
        this.allowsVirtualTableExplore = allowsVirtualTableExplore;
    }


    public Database backend(String backend) {

        this.backend = backend;
        return this;
    }

    /**
     * Get backend
     *
     * @return backend
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_BACKEND)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getBackend() {
        return backend;
    }


    @JsonProperty(JSON_PROPERTY_BACKEND)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setBackend(String backend) {
        this.backend = backend;
    }


    public Database disableDataPreview(Boolean disableDataPreview) {

        this.disableDataPreview = disableDataPreview;
        return this;
    }

    /**
     * Get disableDataPreview
     *
     * @return disableDataPreview
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DISABLE_DATA_PREVIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getDisableDataPreview() {
        return disableDataPreview;
    }


    @JsonProperty(JSON_PROPERTY_DISABLE_DATA_PREVIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDisableDataPreview(Boolean disableDataPreview) {
        this.disableDataPreview = disableDataPreview;
    }


    public Database exploreDatabaseId(Integer exploreDatabaseId) {

        this.exploreDatabaseId = exploreDatabaseId;
        return this;
    }

    /**
     * Get exploreDatabaseId
     *
     * @return exploreDatabaseId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPLORE_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getExploreDatabaseId() {
        return exploreDatabaseId;
    }


    @JsonProperty(JSON_PROPERTY_EXPLORE_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExploreDatabaseId(Integer exploreDatabaseId) {
        this.exploreDatabaseId = exploreDatabaseId;
    }


    public Database id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public Database name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Database database = (Database) o;
        return Objects.equals(this.allowsCostEstimate, database.allowsCostEstimate) &&
            Objects.equals(this.allowsSubquery, database.allowsSubquery) &&
            Objects.equals(this.allowsVirtualTableExplore, database.allowsVirtualTableExplore) &&
            Objects.equals(this.backend, database.backend) &&
            Objects.equals(this.disableDataPreview, database.disableDataPreview) &&
            Objects.equals(this.exploreDatabaseId, database.exploreDatabaseId) &&
            Objects.equals(this.id, database.id) &&
            Objects.equals(this.name, database.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(allowsCostEstimate, allowsSubquery, allowsVirtualTableExplore, backend, disableDataPreview, exploreDatabaseId, id, name);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class Database {\n");
        sb.append("    allowsCostEstimate: ").append(toIndentedString(allowsCostEstimate)).append("\n");
        sb.append("    allowsSubquery: ").append(toIndentedString(allowsSubquery)).append("\n");
        sb.append("    allowsVirtualTableExplore: ").append(toIndentedString(allowsVirtualTableExplore)).append("\n");
        sb.append("    backend: ").append(toIndentedString(backend)).append("\n");
        sb.append("    disableDataPreview: ").append(toIndentedString(disableDataPreview)).append("\n");
        sb.append("    exploreDatabaseId: ").append(toIndentedString(exploreDatabaseId)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

