/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1AsyncEventGet200ResponseResultInner
 */
@JsonPropertyOrder({
    ApiV1AsyncEventGet200ResponseResultInner.JSON_PROPERTY_CHANNEL_ID,
    ApiV1AsyncEventGet200ResponseResultInner.JSON_PROPERTY_ERRORS,
    ApiV1AsyncEventGet200ResponseResultInner.JSON_PROPERTY_ID,
    ApiV1AsyncEventGet200ResponseResultInner.JSON_PROPERTY_JOB_ID,
    ApiV1AsyncEventGet200ResponseResultInner.JSON_PROPERTY_RESULT_URL,
    ApiV1AsyncEventGet200ResponseResultInner.JSON_PROPERTY_STATUS,
    ApiV1AsyncEventGet200ResponseResultInner.JSON_PROPERTY_USER_ID
})
@JsonTypeName("_api_v1_async_event__get_200_response_result_inner")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1AsyncEventGet200ResponseResultInner {
    public static final String JSON_PROPERTY_CHANNEL_ID = "channel_id";
    private String channelId;

    public static final String JSON_PROPERTY_ERRORS = "errors";
    private List<Object> errors;

    public static final String JSON_PROPERTY_ID = "id";
    private String id;

    public static final String JSON_PROPERTY_JOB_ID = "job_id";
    private String jobId;

    public static final String JSON_PROPERTY_RESULT_URL = "result_url";
    private String resultUrl;

    public static final String JSON_PROPERTY_STATUS = "status";
    private String status;

    public static final String JSON_PROPERTY_USER_ID = "user_id";
    private Integer userId;

    public ApiV1AsyncEventGet200ResponseResultInner() {
    }

    public ApiV1AsyncEventGet200ResponseResultInner channelId(String channelId) {

        this.channelId = channelId;
        return this;
    }

    /**
     * Get channelId
     *
     * @return channelId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANNEL_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getChannelId() {
        return channelId;
    }


    @JsonProperty(JSON_PROPERTY_CHANNEL_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }


    public ApiV1AsyncEventGet200ResponseResultInner errors(List<Object> errors) {

        this.errors = errors;
        return this;
    }

    public ApiV1AsyncEventGet200ResponseResultInner addErrorsItem(Object errorsItem) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        this.errors.add(errorsItem);
        return this;
    }

    /**
     * Get errors
     *
     * @return errors
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ERRORS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getErrors() {
        return errors;
    }


    @JsonProperty(JSON_PROPERTY_ERRORS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setErrors(List<Object> errors) {
        this.errors = errors;
    }


    public ApiV1AsyncEventGet200ResponseResultInner id(String id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(String id) {
        this.id = id;
    }


    public ApiV1AsyncEventGet200ResponseResultInner jobId(String jobId) {

        this.jobId = jobId;
        return this;
    }

    /**
     * Get jobId
     *
     * @return jobId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JOB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJobId() {
        return jobId;
    }


    @JsonProperty(JSON_PROPERTY_JOB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJobId(String jobId) {
        this.jobId = jobId;
    }


    public ApiV1AsyncEventGet200ResponseResultInner resultUrl(String resultUrl) {

        this.resultUrl = resultUrl;
        return this;
    }

    /**
     * Get resultUrl
     *
     * @return resultUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getResultUrl() {
        return resultUrl;
    }


    @JsonProperty(JSON_PROPERTY_RESULT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResultUrl(String resultUrl) {
        this.resultUrl = resultUrl;
    }


    public ApiV1AsyncEventGet200ResponseResultInner status(String status) {

        this.status = status;
        return this;
    }

    /**
     * Get status
     *
     * @return status
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getStatus() {
        return status;
    }


    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStatus(String status) {
        this.status = status;
    }


    public ApiV1AsyncEventGet200ResponseResultInner userId(Integer userId) {

        this.userId = userId;
        return this;
    }

    /**
     * Get userId
     *
     * @return userId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getUserId() {
        return userId;
    }


    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1AsyncEventGet200ResponseResultInner apiV1AsyncEventGet200ResponseResultInner = (ApiV1AsyncEventGet200ResponseResultInner) o;
        return Objects.equals(this.channelId, apiV1AsyncEventGet200ResponseResultInner.channelId) &&
            Objects.equals(this.errors, apiV1AsyncEventGet200ResponseResultInner.errors) &&
            Objects.equals(this.id, apiV1AsyncEventGet200ResponseResultInner.id) &&
            Objects.equals(this.jobId, apiV1AsyncEventGet200ResponseResultInner.jobId) &&
            Objects.equals(this.resultUrl, apiV1AsyncEventGet200ResponseResultInner.resultUrl) &&
            Objects.equals(this.status, apiV1AsyncEventGet200ResponseResultInner.status) &&
            Objects.equals(this.userId, apiV1AsyncEventGet200ResponseResultInner.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(channelId, errors, id, jobId, resultUrl, status, userId);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1AsyncEventGet200ResponseResultInner {\n");
        sb.append("    channelId: ").append(toIndentedString(channelId)).append("\n");
        sb.append("    errors: ").append(toIndentedString(errors)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    jobId: ").append(toIndentedString(jobId)).append("\n");
        sb.append("    resultUrl: ").append(toIndentedString(resultUrl)).append("\n");
        sb.append("    status: ").append(toIndentedString(status)).append("\n");
        sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

