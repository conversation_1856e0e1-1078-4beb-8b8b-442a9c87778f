/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1TagObjectTypeObjectIdPostRequest
 */
@JsonPropertyOrder({
    ApiV1TagObjectTypeObjectIdPostRequest.JSON_PROPERTY_TAGS
})
@JsonTypeName("_api_v1_tag__object_type___object_id___post_request")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1TagObjectTypeObjectIdPostRequest {
    public static final String JSON_PROPERTY_TAGS = "tags";
    private List<String> tags;

    public ApiV1TagObjectTypeObjectIdPostRequest() {
    }

    public ApiV1TagObjectTypeObjectIdPostRequest tags(List<String> tags) {

        this.tags = tags;
        return this;
    }

    public ApiV1TagObjectTypeObjectIdPostRequest addTagsItem(String tagsItem) {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }
        this.tags.add(tagsItem);
        return this;
    }

    /**
     * list of tag names to add to object
     *
     * @return tags
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getTags() {
        return tags;
    }


    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1TagObjectTypeObjectIdPostRequest apiV1TagObjectTypeObjectIdPostRequest = (ApiV1TagObjectTypeObjectIdPostRequest) o;
        return Objects.equals(this.tags, apiV1TagObjectTypeObjectIdPostRequest.tags);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tags);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1TagObjectTypeObjectIdPostRequest {\n");
        sb.append("    tags: ").append(toIndentedString(tags)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

