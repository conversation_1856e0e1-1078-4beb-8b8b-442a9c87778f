/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * ApiV1SqllabResultsGet410ResponseErrorsInner
 */
@JsonPropertyOrder({
    ApiV1SqllabResultsGet410ResponseErrorsInner.JSON_PROPERTY_ERROR_TYPE,
    ApiV1SqllabResultsGet410ResponseErrorsInner.JSON_PROPERTY_EXTRA,
    ApiV1SqllabResultsGet410ResponseErrorsInner.JSON_PROPERTY_LEVEL,
    ApiV1SqllabResultsGet410ResponseErrorsInner.JSON_PROPERTY_MESSAGE
})
@JsonTypeName("_api_v1_sqllab_results__get_410_response_errors_inner")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1SqllabResultsGet410ResponseErrorsInner {
    /**
     * Gets or Sets errorType
     */
    public enum ErrorTypeEnum {
        FRONTEND_CSRF_ERROR("FRONTEND_CSRF_ERROR"),

        FRONTEND_NETWORK_ERROR("FRONTEND_NETWORK_ERROR"),

        FRONTEND_TIMEOUT_ERROR("FRONTEND_TIMEOUT_ERROR"),

        GENERIC_DB_ENGINE_ERROR("GENERIC_DB_ENGINE_ERROR"),

        COLUMN_DOES_NOT_EXIST_ERROR("COLUMN_DOES_NOT_EXIST_ERROR"),

        TABLE_DOES_NOT_EXIST_ERROR("TABLE_DOES_NOT_EXIST_ERROR"),

        SCHEMA_DOES_NOT_EXIST_ERROR("SCHEMA_DOES_NOT_EXIST_ERROR"),

        CONNECTION_INVALID_USERNAME_ERROR("CONNECTION_INVALID_USERNAME_ERROR"),

        CONNECTION_INVALID_PASSWORD_ERROR("CONNECTION_INVALID_PASSWORD_ERROR"),

        CONNECTION_INVALID_HOSTNAME_ERROR("CONNECTION_INVALID_HOSTNAME_ERROR"),

        CONNECTION_PORT_CLOSED_ERROR("CONNECTION_PORT_CLOSED_ERROR"),

        CONNECTION_INVALID_PORT_ERROR("CONNECTION_INVALID_PORT_ERROR"),

        CONNECTION_HOST_DOWN_ERROR("CONNECTION_HOST_DOWN_ERROR"),

        CONNECTION_ACCESS_DENIED_ERROR("CONNECTION_ACCESS_DENIED_ERROR"),

        CONNECTION_UNKNOWN_DATABASE_ERROR("CONNECTION_UNKNOWN_DATABASE_ERROR"),

        CONNECTION_DATABASE_PERMISSIONS_ERROR("CONNECTION_DATABASE_PERMISSIONS_ERROR"),

        CONNECTION_MISSING_PARAMETERS_ERROR("CONNECTION_MISSING_PARAMETERS_ERROR"),

        OBJECT_DOES_NOT_EXIST_ERROR("OBJECT_DOES_NOT_EXIST_ERROR"),

        SYNTAX_ERROR("SYNTAX_ERROR"),

        CONNECTION_DATABASE_TIMEOUT("CONNECTION_DATABASE_TIMEOUT"),

        VIZ_GET_DF_ERROR("VIZ_GET_DF_ERROR"),

        UNKNOWN_DATASOURCE_TYPE_ERROR("UNKNOWN_DATASOURCE_TYPE_ERROR"),

        FAILED_FETCHING_DATASOURCE_INFO_ERROR("FAILED_FETCHING_DATASOURCE_INFO_ERROR"),

        TABLE_SECURITY_ACCESS_ERROR("TABLE_SECURITY_ACCESS_ERROR"),

        DATASOURCE_SECURITY_ACCESS_ERROR("DATASOURCE_SECURITY_ACCESS_ERROR"),

        DATABASE_SECURITY_ACCESS_ERROR("DATABASE_SECURITY_ACCESS_ERROR"),

        QUERY_SECURITY_ACCESS_ERROR("QUERY_SECURITY_ACCESS_ERROR"),

        MISSING_OWNERSHIP_ERROR("MISSING_OWNERSHIP_ERROR"),

        USER_ACTIVITY_SECURITY_ACCESS_ERROR("USER_ACTIVITY_SECURITY_ACCESS_ERROR"),

        DASHBOARD_SECURITY_ACCESS_ERROR("DASHBOARD_SECURITY_ACCESS_ERROR"),

        BACKEND_TIMEOUT_ERROR("BACKEND_TIMEOUT_ERROR"),

        DATABASE_NOT_FOUND_ERROR("DATABASE_NOT_FOUND_ERROR"),

        MISSING_TEMPLATE_PARAMS_ERROR("MISSING_TEMPLATE_PARAMS_ERROR"),

        INVALID_TEMPLATE_PARAMS_ERROR("INVALID_TEMPLATE_PARAMS_ERROR"),

        RESULTS_BACKEND_NOT_CONFIGURED_ERROR("RESULTS_BACKEND_NOT_CONFIGURED_ERROR"),

        DML_NOT_ALLOWED_ERROR("DML_NOT_ALLOWED_ERROR"),

        INVALID_CTAS_QUERY_ERROR("INVALID_CTAS_QUERY_ERROR"),

        INVALID_CVAS_QUERY_ERROR("INVALID_CVAS_QUERY_ERROR"),

        SQLLAB_TIMEOUT_ERROR("SQLLAB_TIMEOUT_ERROR"),

        RESULTS_BACKEND_ERROR("RESULTS_BACKEND_ERROR"),

        ASYNC_WORKERS_ERROR("ASYNC_WORKERS_ERROR"),

        ADHOC_SUBQUERY_NOT_ALLOWED_ERROR("ADHOC_SUBQUERY_NOT_ALLOWED_ERROR"),

        GENERIC_COMMAND_ERROR("GENERIC_COMMAND_ERROR"),

        GENERIC_BACKEND_ERROR("GENERIC_BACKEND_ERROR"),

        INVALID_PAYLOAD_FORMAT_ERROR("INVALID_PAYLOAD_FORMAT_ERROR"),

        INVALID_PAYLOAD_SCHEMA_ERROR("INVALID_PAYLOAD_SCHEMA_ERROR"),

        MARSHMALLOW_ERROR("MARSHMALLOW_ERROR"),

        REPORT_NOTIFICATION_ERROR("REPORT_NOTIFICATION_ERROR");

        private String value;

        ErrorTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static ErrorTypeEnum fromValue(String value) {
            for (ErrorTypeEnum b : ErrorTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_ERROR_TYPE = "error_type";
    private ErrorTypeEnum errorType;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private Object extra;

    /**
     * Gets or Sets level
     */
    public enum LevelEnum {
        INFO("info"),

        WARNING("warning"),

        ERROR("error");

        private String value;

        LevelEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static LevelEnum fromValue(String value) {
            for (LevelEnum b : LevelEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_LEVEL = "level";
    private LevelEnum level;

    public static final String JSON_PROPERTY_MESSAGE = "message";
    private String message;

    public ApiV1SqllabResultsGet410ResponseErrorsInner() {
    }

    public ApiV1SqllabResultsGet410ResponseErrorsInner errorType(ErrorTypeEnum errorType) {

        this.errorType = errorType;
        return this;
    }

    /**
     * Get errorType
     *
     * @return errorType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ERROR_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ErrorTypeEnum getErrorType() {
        return errorType;
    }


    @JsonProperty(JSON_PROPERTY_ERROR_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setErrorType(ErrorTypeEnum errorType) {
        this.errorType = errorType;
    }


    public ApiV1SqllabResultsGet410ResponseErrorsInner extra(Object extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(Object extra) {
        this.extra = extra;
    }


    public ApiV1SqllabResultsGet410ResponseErrorsInner level(LevelEnum level) {

        this.level = level;
        return this;
    }

    /**
     * Get level
     *
     * @return level
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LEVEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public LevelEnum getLevel() {
        return level;
    }


    @JsonProperty(JSON_PROPERTY_LEVEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLevel(LevelEnum level) {
        this.level = level;
    }


    public ApiV1SqllabResultsGet410ResponseErrorsInner message(String message) {

        this.message = message;
        return this;
    }

    /**
     * Get message
     *
     * @return message
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMessage() {
        return message;
    }


    @JsonProperty(JSON_PROPERTY_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1SqllabResultsGet410ResponseErrorsInner apiV1SqllabResultsGet410ResponseErrorsInner = (ApiV1SqllabResultsGet410ResponseErrorsInner) o;
        return Objects.equals(this.errorType, apiV1SqllabResultsGet410ResponseErrorsInner.errorType) &&
            Objects.equals(this.extra, apiV1SqllabResultsGet410ResponseErrorsInner.extra) &&
            Objects.equals(this.level, apiV1SqllabResultsGet410ResponseErrorsInner.level) &&
            Objects.equals(this.message, apiV1SqllabResultsGet410ResponseErrorsInner.message);
    }

    @Override
    public int hashCode() {
        return Objects.hash(errorType, extra, level, message);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1SqllabResultsGet410ResponseErrorsInner {\n");
        sb.append("    errorType: ").append(toIndentedString(errorType)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    level: ").append(toIndentedString(level)).append("\n");
        sb.append("    message: ").append(toIndentedString(message)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

