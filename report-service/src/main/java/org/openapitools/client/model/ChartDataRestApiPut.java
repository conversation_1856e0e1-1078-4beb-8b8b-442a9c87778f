/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ChartDataRestApiPut
 */
@JsonPropertyOrder({
    ChartDataRestApiPut.JSON_PROPERTY_CACHE_TIMEOUT,
    ChartDataRestApiPut.JSON_PROPERTY_CERTIFICATION_DETAILS,
    ChartDataRestApiPut.JSON_PROPERTY_CERTIFIED_BY,
    ChartDataRestApiPut.JSON_PROPERTY_DASHBOARDS,
    ChartDataRestApiPut.JSON_PROPERTY_DATASOURCE_ID,
    ChartDataRestApiPut.JSON_PROPERTY_DATASOURCE_TYPE,
    ChartDataRestApiPut.JSO<PERSON>_PROPERTY_DESCRIPTION,
    ChartDataRestApiPut.JSON_PROPERTY_EXTERNAL_URL,
    ChartDataRestApiPut.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    ChartDataRestApiPut.JSON_PROPERTY_OWNERS,
    ChartDataRestApiPut.JSON_PROPERTY_PARAMS,
    ChartDataRestApiPut.JSON_PROPERTY_QUERY_CONTEXT,
    ChartDataRestApiPut.JSON_PROPERTY_QUERY_CONTEXT_GENERATION,
    ChartDataRestApiPut.JSON_PROPERTY_SLICE_NAME,
    ChartDataRestApiPut.JSON_PROPERTY_TAGS,
    ChartDataRestApiPut.JSON_PROPERTY_VIZ_TYPE
})
@JsonTypeName("ChartDataRestApi.put")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataRestApiPut {
    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_DASHBOARDS = "dashboards";
    private List<Integer> dashboards;

    public static final String JSON_PROPERTY_DATASOURCE_ID = "datasource_id";
    private Integer datasourceId;

    /**
     * The type of dataset/datasource identified on &#x60;datasource_id&#x60;.
     */
    public enum DatasourceTypeEnum {
        SL_TABLE("sl_table"),

        TABLE("table"),

        DATASET("dataset"),

        QUERY("query"),

        SAVED_QUERY("saved_query"),

        VIEW("view");

        private String value;

        DatasourceTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static DatasourceTypeEnum fromValue(String value) {
            for (DatasourceTypeEnum b : DatasourceTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            return null;
        }
    }

    public static final String JSON_PROPERTY_DATASOURCE_TYPE = "datasource_type";
    private DatasourceTypeEnum datasourceType;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXTERNAL_URL = "external_url";
    private String externalUrl;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<Integer> owners;

    public static final String JSON_PROPERTY_PARAMS = "params";
    private String params;

    public static final String JSON_PROPERTY_QUERY_CONTEXT = "query_context";
    private String queryContext;

    public static final String JSON_PROPERTY_QUERY_CONTEXT_GENERATION = "query_context_generation";
    private Boolean queryContextGeneration;

    public static final String JSON_PROPERTY_SLICE_NAME = "slice_name";
    private String sliceName;

    public static final String JSON_PROPERTY_TAGS = "tags";
    private List<Tag> tags;

    public static final String JSON_PROPERTY_VIZ_TYPE = "viz_type";
    private String vizType;

    public ChartDataRestApiPut() {
    }

    public ChartDataRestApiPut cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Duration (in seconds) of the caching timeout for this chart. Note this defaults to the datasource/table timeout if undefined.
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public ChartDataRestApiPut certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Details of the certification
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public ChartDataRestApiPut certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Person or group that has certified this chart
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    public ChartDataRestApiPut dashboards(List<Integer> dashboards) {

        this.dashboards = dashboards;
        return this;
    }

    public ChartDataRestApiPut addDashboardsItem(Integer dashboardsItem) {
        if (this.dashboards == null) {
            this.dashboards = new ArrayList<>();
        }
        this.dashboards.add(dashboardsItem);
        return this;
    }

    /**
     * Get dashboards
     *
     * @return dashboards
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getDashboards() {
        return dashboards;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboards(List<Integer> dashboards) {
        this.dashboards = dashboards;
    }


    public ChartDataRestApiPut datasourceId(Integer datasourceId) {

        this.datasourceId = datasourceId;
        return this;
    }

    /**
     * The id of the dataset/datasource this new chart will use. A complete datasource identification needs &#x60;datasource_id&#x60; and &#x60;datasource_type&#x60;.
     *
     * @return datasourceId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDatasourceId() {
        return datasourceId;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceId(Integer datasourceId) {
        this.datasourceId = datasourceId;
    }


    public ChartDataRestApiPut datasourceType(DatasourceTypeEnum datasourceType) {

        this.datasourceType = datasourceType;
        return this;
    }

    /**
     * The type of dataset/datasource identified on &#x60;datasource_id&#x60;.
     *
     * @return datasourceType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DatasourceTypeEnum getDatasourceType() {
        return datasourceType;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceType(DatasourceTypeEnum datasourceType) {
        this.datasourceType = datasourceType;
    }


    public ChartDataRestApiPut description(String description) {

        this.description = description;
        return this;
    }

    /**
     * A description of the chart propose.
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public ChartDataRestApiPut externalUrl(String externalUrl) {

        this.externalUrl = externalUrl;
        return this;
    }

    /**
     * Get externalUrl
     *
     * @return externalUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExternalUrl() {
        return externalUrl;
    }


    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExternalUrl(String externalUrl) {
        this.externalUrl = externalUrl;
    }


    public ChartDataRestApiPut isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public ChartDataRestApiPut owners(List<Integer> owners) {

        this.owners = owners;
        return this;
    }

    public ChartDataRestApiPut addOwnersItem(Integer ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<Integer> owners) {
        this.owners = owners;
    }


    public ChartDataRestApiPut params(String params) {

        this.params = params;
        return this;
    }

    /**
     * Parameters are generated dynamically when clicking the save or overwrite button in the explore view. This JSON object for power users who may want to alter specific parameters.
     *
     * @return params
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getParams() {
        return params;
    }


    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setParams(String params) {
        this.params = params;
    }


    public ChartDataRestApiPut queryContext(String queryContext) {

        this.queryContext = queryContext;
        return this;
    }

    /**
     * The query context represents the queries that need to run in order to generate the data the visualization, and in what format the data should be returned.
     *
     * @return queryContext
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getQueryContext() {
        return queryContext;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryContext(String queryContext) {
        this.queryContext = queryContext;
    }


    public ChartDataRestApiPut queryContextGeneration(Boolean queryContextGeneration) {

        this.queryContextGeneration = queryContextGeneration;
        return this;
    }

    /**
     * The query context generation represents whether the query_contextis user generated or not so that it does not update user modifiedstate.
     *
     * @return queryContextGeneration
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT_GENERATION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getQueryContextGeneration() {
        return queryContextGeneration;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT_GENERATION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryContextGeneration(Boolean queryContextGeneration) {
        this.queryContextGeneration = queryContextGeneration;
    }


    public ChartDataRestApiPut sliceName(String sliceName) {

        this.sliceName = sliceName;
        return this;
    }

    /**
     * The name of the chart.
     *
     * @return sliceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSliceName() {
        return sliceName;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceName(String sliceName) {
        this.sliceName = sliceName;
    }


    public ChartDataRestApiPut tags(List<Tag> tags) {

        this.tags = tags;
        return this;
    }

    public ChartDataRestApiPut addTagsItem(Tag tagsItem) {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }
        this.tags.add(tagsItem);
        return this;
    }

    /**
     * Get tags
     *
     * @return tags
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Tag> getTags() {
        return tags;
    }


    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTags(List<Tag> tags) {
        this.tags = tags;
    }


    public ChartDataRestApiPut vizType(String vizType) {

        this.vizType = vizType;
        return this;
    }

    /**
     * The type of chart visualization used.
     *
     * @return vizType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVizType() {
        return vizType;
    }


    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVizType(String vizType) {
        this.vizType = vizType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataRestApiPut chartDataRestApiPut = (ChartDataRestApiPut) o;
        return Objects.equals(this.cacheTimeout, chartDataRestApiPut.cacheTimeout) &&
            Objects.equals(this.certificationDetails, chartDataRestApiPut.certificationDetails) &&
            Objects.equals(this.certifiedBy, chartDataRestApiPut.certifiedBy) &&
            Objects.equals(this.dashboards, chartDataRestApiPut.dashboards) &&
            Objects.equals(this.datasourceId, chartDataRestApiPut.datasourceId) &&
            Objects.equals(this.datasourceType, chartDataRestApiPut.datasourceType) &&
            Objects.equals(this.description, chartDataRestApiPut.description) &&
            Objects.equals(this.externalUrl, chartDataRestApiPut.externalUrl) &&
            Objects.equals(this.isManagedExternally, chartDataRestApiPut.isManagedExternally) &&
            Objects.equals(this.owners, chartDataRestApiPut.owners) &&
            Objects.equals(this.params, chartDataRestApiPut.params) &&
            Objects.equals(this.queryContext, chartDataRestApiPut.queryContext) &&
            Objects.equals(this.queryContextGeneration, chartDataRestApiPut.queryContextGeneration) &&
            Objects.equals(this.sliceName, chartDataRestApiPut.sliceName) &&
            Objects.equals(this.tags, chartDataRestApiPut.tags) &&
            Objects.equals(this.vizType, chartDataRestApiPut.vizType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cacheTimeout, certificationDetails, certifiedBy, dashboards, datasourceId, datasourceType, description, externalUrl, isManagedExternally, owners, params, queryContext, queryContextGeneration, sliceName, tags, vizType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataRestApiPut {\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    dashboards: ").append(toIndentedString(dashboards)).append("\n");
        sb.append("    datasourceId: ").append(toIndentedString(datasourceId)).append("\n");
        sb.append("    datasourceType: ").append(toIndentedString(datasourceType)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    externalUrl: ").append(toIndentedString(externalUrl)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    params: ").append(toIndentedString(params)).append("\n");
        sb.append("    queryContext: ").append(toIndentedString(queryContext)).append("\n");
        sb.append("    queryContextGeneration: ").append(toIndentedString(queryContextGeneration)).append("\n");
        sb.append("    sliceName: ").append(toIndentedString(sliceName)).append("\n");
        sb.append("    tags: ").append(toIndentedString(tags)).append("\n");
        sb.append("    vizType: ").append(toIndentedString(vizType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

