/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * RLSRestApiPut
 */
@JsonPropertyOrder({
    RLSRestApiPut.JSON_PROPERTY_CLAUSE,
    RLSRestApiPut.JSON_PROPERTY_DESCRIPTION,
    RLSRestApiPut.JSON_PROPERTY_FILTER_TYPE,
    RLSRestApiPut.JSON_PROPERTY_GROUP_KEY,
    RLSRestApiPut.JSON_PROPERTY_NAME,
    RLSRestApiPut.JSON_PROPERTY_ROLES,
    RLSRestApiPut.JSON_PROPERTY_TABLES
})
@JsonTypeName("RLSRestApi.put")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class RLSRestApiPut {
    public static final String JSON_PROPERTY_CLAUSE = "clause";
    private String clause;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    /**
     * filter_type_description
     */
    public enum FilterTypeEnum {
        REGULAR("Regular"),

        BASE("Base");

        private String value;

        FilterTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static FilterTypeEnum fromValue(String value) {
            for (FilterTypeEnum b : FilterTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_FILTER_TYPE = "filter_type";
    private FilterTypeEnum filterType;

    public static final String JSON_PROPERTY_GROUP_KEY = "group_key";
    private String groupKey;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_ROLES = "roles";
    private List<Integer> roles;

    public static final String JSON_PROPERTY_TABLES = "tables";
    private List<Integer> tables;

    public RLSRestApiPut() {
    }

    public RLSRestApiPut clause(String clause) {

        this.clause = clause;
        return this;
    }

    /**
     * clause_description
     *
     * @return clause
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CLAUSE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getClause() {
        return clause;
    }


    @JsonProperty(JSON_PROPERTY_CLAUSE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setClause(String clause) {
        this.clause = clause;
    }


    public RLSRestApiPut description(String description) {

        this.description = description;
        return this;
    }

    /**
     * description_description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public RLSRestApiPut filterType(FilterTypeEnum filterType) {

        this.filterType = filterType;
        return this;
    }

    /**
     * filter_type_description
     *
     * @return filterType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public FilterTypeEnum getFilterType() {
        return filterType;
    }


    @JsonProperty(JSON_PROPERTY_FILTER_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterType(FilterTypeEnum filterType) {
        this.filterType = filterType;
    }


    public RLSRestApiPut groupKey(String groupKey) {

        this.groupKey = groupKey;
        return this;
    }

    /**
     * group_key_description
     *
     * @return groupKey
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GROUP_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getGroupKey() {
        return groupKey;
    }


    @JsonProperty(JSON_PROPERTY_GROUP_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGroupKey(String groupKey) {
        this.groupKey = groupKey;
    }


    public RLSRestApiPut name(String name) {

        this.name = name;
        return this;
    }

    /**
     * name_description
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public RLSRestApiPut roles(List<Integer> roles) {

        this.roles = roles;
        return this;
    }

    public RLSRestApiPut addRolesItem(Integer rolesItem) {
        if (this.roles == null) {
            this.roles = new ArrayList<>();
        }
        this.roles.add(rolesItem);
        return this;
    }

    /**
     * roles_description
     *
     * @return roles
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getRoles() {
        return roles;
    }


    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRoles(List<Integer> roles) {
        this.roles = roles;
    }


    public RLSRestApiPut tables(List<Integer> tables) {

        this.tables = tables;
        return this;
    }

    public RLSRestApiPut addTablesItem(Integer tablesItem) {
        if (this.tables == null) {
            this.tables = new ArrayList<>();
        }
        this.tables.add(tablesItem);
        return this;
    }

    /**
     * tables_description
     *
     * @return tables
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getTables() {
        return tables;
    }


    @JsonProperty(JSON_PROPERTY_TABLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTables(List<Integer> tables) {
        this.tables = tables;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RLSRestApiPut rlSRestApiPut = (RLSRestApiPut) o;
        return Objects.equals(this.clause, rlSRestApiPut.clause) &&
            Objects.equals(this.description, rlSRestApiPut.description) &&
            Objects.equals(this.filterType, rlSRestApiPut.filterType) &&
            Objects.equals(this.groupKey, rlSRestApiPut.groupKey) &&
            Objects.equals(this.name, rlSRestApiPut.name) &&
            Objects.equals(this.roles, rlSRestApiPut.roles) &&
            Objects.equals(this.tables, rlSRestApiPut.tables);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clause, description, filterType, groupKey, name, roles, tables);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RLSRestApiPut {\n");
        sb.append("    clause: ").append(toIndentedString(clause)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    filterType: ").append(toIndentedString(filterType)).append("\n");
        sb.append("    groupKey: ").append(toIndentedString(groupKey)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    roles: ").append(toIndentedString(roles)).append("\n");
        sb.append("    tables: ").append(toIndentedString(tables)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

