/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * DatabaseTablesQuerySchema
 */
@JsonPropertyOrder({
    DatabaseTablesQuerySchema.JSON_PROPERTY_FORCE,
    DatabaseTablesQuerySchema.JSON_PROPERTY_SCHEMA_NAME
})
@JsonTypeName("database_tables_query_schema")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseTablesQuerySchema {
    public static final String JSON_PROPERTY_FORCE = "force";
    private Boolean force;

    public static final String JSON_PROPERTY_SCHEMA_NAME = "schema_name";
    private String schemaName;

    public DatabaseTablesQuerySchema() {
    }

    public DatabaseTablesQuerySchema force(Boolean force) {

        this.force = force;
        return this;
    }

    /**
     * Get force
     *
     * @return force
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getForce() {
        return force;
    }


    @JsonProperty(JSON_PROPERTY_FORCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setForce(Boolean force) {
        this.force = force;
    }


    public DatabaseTablesQuerySchema schemaName(String schemaName) {

        this.schemaName = schemaName;
        return this;
    }

    /**
     * Get schemaName
     *
     * @return schemaName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_SCHEMA_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getSchemaName() {
        return schemaName;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseTablesQuerySchema databaseTablesQuerySchema = (DatabaseTablesQuerySchema) o;
        return Objects.equals(this.force, databaseTablesQuerySchema.force) &&
            Objects.equals(this.schemaName, databaseTablesQuerySchema.schemaName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(force, schemaName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseTablesQuerySchema {\n");
        sb.append("    force: ").append(toIndentedString(force)).append("\n");
        sb.append("    schemaName: ").append(toIndentedString(schemaName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

