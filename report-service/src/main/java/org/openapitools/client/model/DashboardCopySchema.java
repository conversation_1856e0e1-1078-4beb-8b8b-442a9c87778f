/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * DashboardCopySchema
 */
@JsonPropertyOrder({
    DashboardCopySchema.JSON_PROPERTY_CSS,
    DashboardCopySchema.JSON_PROPERTY_DASHBOARD_TITLE,
    DashboardCopySchema.JSON_PROPERTY_DUPLICATE_SLICES,
    DashboardCopySchema.JSON_PROPERTY_JSON_METADATA
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DashboardCopySchema {
    public static final String JSON_PROPERTY_CSS = "css";
    private String css;

    public static final String JSON_PROPERTY_DASHBOARD_TITLE = "dashboard_title";
    private String dashboardTitle;

    public static final String JSON_PROPERTY_DUPLICATE_SLICES = "duplicate_slices";
    private Boolean duplicateSlices;

    public static final String JSON_PROPERTY_JSON_METADATA = "json_metadata";
    private String jsonMetadata;

    public DashboardCopySchema() {
    }

    public DashboardCopySchema css(String css) {

        this.css = css;
        return this;
    }

    /**
     * Override CSS for the dashboard.
     *
     * @return css
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCss() {
        return css;
    }


    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCss(String css) {
        this.css = css;
    }


    public DashboardCopySchema dashboardTitle(String dashboardTitle) {

        this.dashboardTitle = dashboardTitle;
        return this;
    }

    /**
     * A title for the dashboard.
     *
     * @return dashboardTitle
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDashboardTitle() {
        return dashboardTitle;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardTitle(String dashboardTitle) {
        this.dashboardTitle = dashboardTitle;
    }


    public DashboardCopySchema duplicateSlices(Boolean duplicateSlices) {

        this.duplicateSlices = duplicateSlices;
        return this;
    }

    /**
     * Whether or not to also copy all charts on the dashboard
     *
     * @return duplicateSlices
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DUPLICATE_SLICES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getDuplicateSlices() {
        return duplicateSlices;
    }


    @JsonProperty(JSON_PROPERTY_DUPLICATE_SLICES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDuplicateSlices(Boolean duplicateSlices) {
        this.duplicateSlices = duplicateSlices;
    }


    public DashboardCopySchema jsonMetadata(String jsonMetadata) {

        this.jsonMetadata = jsonMetadata;
        return this;
    }

    /**
     * This JSON object is generated dynamically when clicking the save or overwrite button in the dashboard view. It is exposed here for reference and for power users who may want to alter  specific parameters.
     *
     * @return jsonMetadata
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getJsonMetadata() {
        return jsonMetadata;
    }


    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setJsonMetadata(String jsonMetadata) {
        this.jsonMetadata = jsonMetadata;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DashboardCopySchema dashboardCopySchema = (DashboardCopySchema) o;
        return Objects.equals(this.css, dashboardCopySchema.css) &&
            Objects.equals(this.dashboardTitle, dashboardCopySchema.dashboardTitle) &&
            Objects.equals(this.duplicateSlices, dashboardCopySchema.duplicateSlices) &&
            Objects.equals(this.jsonMetadata, dashboardCopySchema.jsonMetadata);
    }

    @Override
    public int hashCode() {
        return Objects.hash(css, dashboardTitle, duplicateSlices, jsonMetadata);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DashboardCopySchema {\n");
        sb.append("    css: ").append(toIndentedString(css)).append("\n");
        sb.append("    dashboardTitle: ").append(toIndentedString(dashboardTitle)).append("\n");
        sb.append("    duplicateSlices: ").append(toIndentedString(duplicateSlices)).append("\n");
        sb.append("    jsonMetadata: ").append(toIndentedString(jsonMetadata)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

