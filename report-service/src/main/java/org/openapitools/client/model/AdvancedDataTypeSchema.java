/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * AdvancedDataTypeSchema
 */
@JsonPropertyOrder({
    AdvancedDataTypeSchema.JSON_PROPERTY_DISPLAY_VALUE,
    AdvancedDataTypeSchema.JSON_PROPERTY_ERROR_MESSAGE,
    AdvancedDataTypeSchema.JSON_PROPERTY_VALID_FILTER_OPERATORS,
    AdvancedDataTypeSchema.JSON_PROPERTY_VALUES
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class AdvancedDataTypeSchema {
    public static final String JSON_PROPERTY_DISPLAY_VALUE = "display_value";
    private String displayValue;

    public static final String JSON_PROPERTY_ERROR_MESSAGE = "error_message";
    private String errorMessage;

    public static final String JSON_PROPERTY_VALID_FILTER_OPERATORS = "valid_filter_operators";
    private List<String> validFilterOperators;

    public static final String JSON_PROPERTY_VALUES = "values";
    private List<String> values;

    public AdvancedDataTypeSchema() {
    }

    public AdvancedDataTypeSchema displayValue(String displayValue) {

        this.displayValue = displayValue;
        return this;
    }

    /**
     * The string representation of the parsed values
     *
     * @return displayValue
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DISPLAY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDisplayValue() {
        return displayValue;
    }


    @JsonProperty(JSON_PROPERTY_DISPLAY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDisplayValue(String displayValue) {
        this.displayValue = displayValue;
    }


    public AdvancedDataTypeSchema errorMessage(String errorMessage) {

        this.errorMessage = errorMessage;
        return this;
    }

    /**
     * Get errorMessage
     *
     * @return errorMessage
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getErrorMessage() {
        return errorMessage;
    }


    @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }


    public AdvancedDataTypeSchema validFilterOperators(List<String> validFilterOperators) {

        this.validFilterOperators = validFilterOperators;
        return this;
    }

    public AdvancedDataTypeSchema addValidFilterOperatorsItem(String validFilterOperatorsItem) {
        if (this.validFilterOperators == null) {
            this.validFilterOperators = new ArrayList<>();
        }
        this.validFilterOperators.add(validFilterOperatorsItem);
        return this;
    }

    /**
     * Get validFilterOperators
     *
     * @return validFilterOperators
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALID_FILTER_OPERATORS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getValidFilterOperators() {
        return validFilterOperators;
    }


    @JsonProperty(JSON_PROPERTY_VALID_FILTER_OPERATORS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValidFilterOperators(List<String> validFilterOperators) {
        this.validFilterOperators = validFilterOperators;
    }


    public AdvancedDataTypeSchema values(List<String> values) {

        this.values = values;
        return this;
    }

    public AdvancedDataTypeSchema addValuesItem(String valuesItem) {
        if (this.values == null) {
            this.values = new ArrayList<>();
        }
        this.values.add(valuesItem);
        return this;
    }

    /**
     * Get values
     *
     * @return values
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALUES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getValues() {
        return values;
    }


    @JsonProperty(JSON_PROPERTY_VALUES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValues(List<String> values) {
        this.values = values;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AdvancedDataTypeSchema advancedDataTypeSchema = (AdvancedDataTypeSchema) o;
        return Objects.equals(this.displayValue, advancedDataTypeSchema.displayValue) &&
            Objects.equals(this.errorMessage, advancedDataTypeSchema.errorMessage) &&
            Objects.equals(this.validFilterOperators, advancedDataTypeSchema.validFilterOperators) &&
            Objects.equals(this.values, advancedDataTypeSchema.values);
    }

    @Override
    public int hashCode() {
        return Objects.hash(displayValue, errorMessage, validFilterOperators, values);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class AdvancedDataTypeSchema {\n");
        sb.append("    displayValue: ").append(toIndentedString(displayValue)).append("\n");
        sb.append("    errorMessage: ").append(toIndentedString(errorMessage)).append("\n");
        sb.append("    validFilterOperators: ").append(toIndentedString(validFilterOperators)).append("\n");
        sb.append("    values: ").append(toIndentedString(values)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

