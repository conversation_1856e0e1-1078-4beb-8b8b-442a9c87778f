/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartDataGeohashEncodeOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataGeohashEncodeOptionsSchema.JSON_PROPERTY_GEOHASH,
    ChartDataGeohashEncodeOptionsSchema.JSON_PROPERTY_LATITUDE,
    ChartDataGeohashEncodeOptionsSchema.JSON_PROPERTY_LONGITUDE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataGeohashEncodeOptionsSchema {
    public static final String JSON_PROPERTY_GEOHASH = "geohash";
    private String geohash;

    public static final String JSON_PROPERTY_LATITUDE = "latitude";
    private String latitude;

    public static final String JSON_PROPERTY_LONGITUDE = "longitude";
    private String longitude;

    public ChartDataGeohashEncodeOptionsSchema() {
    }

    public ChartDataGeohashEncodeOptionsSchema geohash(String geohash) {

        this.geohash = geohash;
        return this;
    }

    /**
     * Name of target column for encoded geohash string
     *
     * @return geohash
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_GEOHASH)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getGeohash() {
        return geohash;
    }


    @JsonProperty(JSON_PROPERTY_GEOHASH)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setGeohash(String geohash) {
        this.geohash = geohash;
    }


    public ChartDataGeohashEncodeOptionsSchema latitude(String latitude) {

        this.latitude = latitude;
        return this;
    }

    /**
     * Name of source latitude column
     *
     * @return latitude
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_LATITUDE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getLatitude() {
        return latitude;
    }


    @JsonProperty(JSON_PROPERTY_LATITUDE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }


    public ChartDataGeohashEncodeOptionsSchema longitude(String longitude) {

        this.longitude = longitude;
        return this;
    }

    /**
     * Name of source longitude column
     *
     * @return longitude
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_LONGITUDE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getLongitude() {
        return longitude;
    }


    @JsonProperty(JSON_PROPERTY_LONGITUDE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataGeohashEncodeOptionsSchema chartDataGeohashEncodeOptionsSchema = (ChartDataGeohashEncodeOptionsSchema) o;
        return Objects.equals(this.geohash, chartDataGeohashEncodeOptionsSchema.geohash) &&
            Objects.equals(this.latitude, chartDataGeohashEncodeOptionsSchema.latitude) &&
            Objects.equals(this.longitude, chartDataGeohashEncodeOptionsSchema.longitude);
    }

    @Override
    public int hashCode() {
        return Objects.hash(geohash, latitude, longitude);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataGeohashEncodeOptionsSchema {\n");
        sb.append("    geohash: ").append(toIndentedString(geohash)).append("\n");
        sb.append("    latitude: ").append(toIndentedString(latitude)).append("\n");
        sb.append("    longitude: ").append(toIndentedString(longitude)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

