/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * DatasetRelatedObjectsResponse
 */
@JsonPropertyOrder({
    DatasetRelatedObjectsResponse.JSON_PROPERTY_CHARTS,
    DatasetRelatedObjectsResponse.JSON_PROPERTY_DASHBOARDS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetRelatedObjectsResponse {
    public static final String JSON_PROPERTY_CHARTS = "charts";
    private DatasetRelatedCharts charts;

    public static final String JSON_PROPERTY_DASHBOARDS = "dashboards";
    private DatasetRelatedDashboards dashboards;

    public DatasetRelatedObjectsResponse() {
    }

    public DatasetRelatedObjectsResponse charts(DatasetRelatedCharts charts) {

        this.charts = charts;
        return this;
    }

    /**
     * Get charts
     *
     * @return charts
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHARTS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DatasetRelatedCharts getCharts() {
        return charts;
    }


    @JsonProperty(JSON_PROPERTY_CHARTS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCharts(DatasetRelatedCharts charts) {
        this.charts = charts;
    }


    public DatasetRelatedObjectsResponse dashboards(DatasetRelatedDashboards dashboards) {

        this.dashboards = dashboards;
        return this;
    }

    /**
     * Get dashboards
     *
     * @return dashboards
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DatasetRelatedDashboards getDashboards() {
        return dashboards;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboards(DatasetRelatedDashboards dashboards) {
        this.dashboards = dashboards;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetRelatedObjectsResponse datasetRelatedObjectsResponse = (DatasetRelatedObjectsResponse) o;
        return Objects.equals(this.charts, datasetRelatedObjectsResponse.charts) &&
            Objects.equals(this.dashboards, datasetRelatedObjectsResponse.dashboards);
    }

    @Override
    public int hashCode() {
        return Objects.hash(charts, dashboards);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetRelatedObjectsResponse {\n");
        sb.append("    charts: ").append(toIndentedString(charts)).append("\n");
        sb.append("    dashboards: ").append(toIndentedString(dashboards)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

