/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ExecutePayloadSchema
 */
@JsonPropertyOrder({
    ExecutePayloadSchema.JSON_PROPERTY_CLIENT_ID,
    ExecutePayloadSchema.JSON_PROPERTY_CTAS_METHOD,
    ExecutePayloadSchema.JSON_PROPERTY_DATABASE_ID,
    ExecutePayloadSchema.JSON_PROPERTY_EXPAND_DATA,
    ExecutePayloadSchema.JSON_PROPERTY_JSON,
    ExecutePayloadSchema.JSON_PROPERTY_QUERY_LIMIT,
    ExecutePayloadSchema.JSON_PROPERTY_RUN_ASYNC,
    ExecutePayloadSchema.JSON_PROPERTY_SCHEMA,
    ExecutePayloadSchema.JSON_PROPERTY_SELECT_AS_CTA,
    ExecutePayloadSchema.JSON_PROPERTY_SQL,
    ExecutePayloadSchema.JSON_PROPERTY_SQL_EDITOR_ID,
    ExecutePayloadSchema.JSON_PROPERTY_TAB,
    ExecutePayloadSchema.JSON_PROPERTY_TEMPLATE_PARAMS,
    ExecutePayloadSchema.JSON_PROPERTY_TMP_TABLE_NAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ExecutePayloadSchema {
    public static final String JSON_PROPERTY_CLIENT_ID = "client_id";
    private String clientId;

    public static final String JSON_PROPERTY_CTAS_METHOD = "ctas_method";
    private String ctasMethod;

    public static final String JSON_PROPERTY_DATABASE_ID = "database_id";
    private Integer databaseId;

    public static final String JSON_PROPERTY_EXPAND_DATA = "expand_data";
    private Boolean expandData;

    public static final String JSON_PROPERTY_JSON = "json";
    private Boolean json;

    public static final String JSON_PROPERTY_QUERY_LIMIT = "queryLimit";
    private Integer queryLimit;

    public static final String JSON_PROPERTY_RUN_ASYNC = "runAsync";
    private Boolean runAsync;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SELECT_AS_CTA = "select_as_cta";
    private Boolean selectAsCta;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_SQL_EDITOR_ID = "sql_editor_id";
    private String sqlEditorId;

    public static final String JSON_PROPERTY_TAB = "tab";
    private String tab;

    public static final String JSON_PROPERTY_TEMPLATE_PARAMS = "templateParams";
    private String templateParams;

    public static final String JSON_PROPERTY_TMP_TABLE_NAME = "tmp_table_name";
    private String tmpTableName;

    public ExecutePayloadSchema() {
    }

    public ExecutePayloadSchema clientId(String clientId) {

        this.clientId = clientId;
        return this;
    }

    /**
     * Get clientId
     *
     * @return clientId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CLIENT_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getClientId() {
        return clientId;
    }


    @JsonProperty(JSON_PROPERTY_CLIENT_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setClientId(String clientId) {
        this.clientId = clientId;
    }


    public ExecutePayloadSchema ctasMethod(String ctasMethod) {

        this.ctasMethod = ctasMethod;
        return this;
    }

    /**
     * Get ctasMethod
     *
     * @return ctasMethod
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CTAS_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCtasMethod() {
        return ctasMethod;
    }


    @JsonProperty(JSON_PROPERTY_CTAS_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCtasMethod(String ctasMethod) {
        this.ctasMethod = ctasMethod;
    }


    public ExecutePayloadSchema databaseId(Integer databaseId) {

        this.databaseId = databaseId;
        return this;
    }

    /**
     * Get databaseId
     *
     * @return databaseId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getDatabaseId() {
        return databaseId;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabaseId(Integer databaseId) {
        this.databaseId = databaseId;
    }


    public ExecutePayloadSchema expandData(Boolean expandData) {

        this.expandData = expandData;
        return this;
    }

    /**
     * Get expandData
     *
     * @return expandData
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPAND_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getExpandData() {
        return expandData;
    }


    @JsonProperty(JSON_PROPERTY_EXPAND_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExpandData(Boolean expandData) {
        this.expandData = expandData;
    }


    public ExecutePayloadSchema json(Boolean json) {

        this.json = json;
        return this;
    }

    /**
     * Get json
     *
     * @return json
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getJson() {
        return json;
    }


    @JsonProperty(JSON_PROPERTY_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJson(Boolean json) {
        this.json = json;
    }


    public ExecutePayloadSchema queryLimit(Integer queryLimit) {

        this.queryLimit = queryLimit;
        return this;
    }

    /**
     * Get queryLimit
     *
     * @return queryLimit
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getQueryLimit() {
        return queryLimit;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryLimit(Integer queryLimit) {
        this.queryLimit = queryLimit;
    }


    public ExecutePayloadSchema runAsync(Boolean runAsync) {

        this.runAsync = runAsync;
        return this;
    }

    /**
     * Get runAsync
     *
     * @return runAsync
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getRunAsync() {
        return runAsync;
    }


    @JsonProperty(JSON_PROPERTY_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRunAsync(Boolean runAsync) {
        this.runAsync = runAsync;
    }


    public ExecutePayloadSchema schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public ExecutePayloadSchema selectAsCta(Boolean selectAsCta) {

        this.selectAsCta = selectAsCta;
        return this;
    }

    /**
     * Get selectAsCta
     *
     * @return selectAsCta
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SELECT_AS_CTA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getSelectAsCta() {
        return selectAsCta;
    }


    @JsonProperty(JSON_PROPERTY_SELECT_AS_CTA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSelectAsCta(Boolean selectAsCta) {
        this.selectAsCta = selectAsCta;
    }


    public ExecutePayloadSchema sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public ExecutePayloadSchema sqlEditorId(String sqlEditorId) {

        this.sqlEditorId = sqlEditorId;
        return this;
    }

    /**
     * Get sqlEditorId
     *
     * @return sqlEditorId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL_EDITOR_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSqlEditorId() {
        return sqlEditorId;
    }


    @JsonProperty(JSON_PROPERTY_SQL_EDITOR_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSqlEditorId(String sqlEditorId) {
        this.sqlEditorId = sqlEditorId;
    }


    public ExecutePayloadSchema tab(String tab) {

        this.tab = tab;
        return this;
    }

    /**
     * Get tab
     *
     * @return tab
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTab() {
        return tab;
    }


    @JsonProperty(JSON_PROPERTY_TAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTab(String tab) {
        this.tab = tab;
    }


    public ExecutePayloadSchema templateParams(String templateParams) {

        this.templateParams = templateParams;
        return this;
    }

    /**
     * Get templateParams
     *
     * @return templateParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTemplateParams() {
        return templateParams;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams;
    }


    public ExecutePayloadSchema tmpTableName(String tmpTableName) {

        this.tmpTableName = tmpTableName;
        return this;
    }

    /**
     * Get tmpTableName
     *
     * @return tmpTableName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TMP_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTmpTableName() {
        return tmpTableName;
    }


    @JsonProperty(JSON_PROPERTY_TMP_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTmpTableName(String tmpTableName) {
        this.tmpTableName = tmpTableName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ExecutePayloadSchema executePayloadSchema = (ExecutePayloadSchema) o;
        return Objects.equals(this.clientId, executePayloadSchema.clientId) &&
            Objects.equals(this.ctasMethod, executePayloadSchema.ctasMethod) &&
            Objects.equals(this.databaseId, executePayloadSchema.databaseId) &&
            Objects.equals(this.expandData, executePayloadSchema.expandData) &&
            Objects.equals(this.json, executePayloadSchema.json) &&
            Objects.equals(this.queryLimit, executePayloadSchema.queryLimit) &&
            Objects.equals(this.runAsync, executePayloadSchema.runAsync) &&
            Objects.equals(this.schema, executePayloadSchema.schema) &&
            Objects.equals(this.selectAsCta, executePayloadSchema.selectAsCta) &&
            Objects.equals(this.sql, executePayloadSchema.sql) &&
            Objects.equals(this.sqlEditorId, executePayloadSchema.sqlEditorId) &&
            Objects.equals(this.tab, executePayloadSchema.tab) &&
            Objects.equals(this.templateParams, executePayloadSchema.templateParams) &&
            Objects.equals(this.tmpTableName, executePayloadSchema.tmpTableName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clientId, ctasMethod, databaseId, expandData, json, queryLimit, runAsync, schema, selectAsCta, sql, sqlEditorId, tab, templateParams, tmpTableName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ExecutePayloadSchema {\n");
        sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
        sb.append("    ctasMethod: ").append(toIndentedString(ctasMethod)).append("\n");
        sb.append("    databaseId: ").append(toIndentedString(databaseId)).append("\n");
        sb.append("    expandData: ").append(toIndentedString(expandData)).append("\n");
        sb.append("    json: ").append(toIndentedString(json)).append("\n");
        sb.append("    queryLimit: ").append(toIndentedString(queryLimit)).append("\n");
        sb.append("    runAsync: ").append(toIndentedString(runAsync)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    selectAsCta: ").append(toIndentedString(selectAsCta)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    sqlEditorId: ").append(toIndentedString(sqlEditorId)).append("\n");
        sb.append("    tab: ").append(toIndentedString(tab)).append("\n");
        sb.append("    templateParams: ").append(toIndentedString(templateParams)).append("\n");
        sb.append("    tmpTableName: ").append(toIndentedString(tmpTableName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

