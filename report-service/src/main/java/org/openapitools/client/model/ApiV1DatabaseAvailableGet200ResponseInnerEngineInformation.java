/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * Dict with public properties form the DB Engine
 */
@JsonPropertyOrder({
    ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation.JSON_PROPERTY_DISABLE_SSH_TUNNELING,
    ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation.JSON_PROPERTY_SUPPORTS_FILE_UPLOAD
})
@JsonTypeName("_api_v1_database_available__get_200_response_inner_engine_information")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation {
    public static final String JSON_PROPERTY_DISABLE_SSH_TUNNELING = "disable_ssh_tunneling";
    private Boolean disableSshTunneling;

    public static final String JSON_PROPERTY_SUPPORTS_FILE_UPLOAD = "supports_file_upload";
    private Boolean supportsFileUpload;

    public ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation() {
    }

    public ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation disableSshTunneling(Boolean disableSshTunneling) {

        this.disableSshTunneling = disableSshTunneling;
        return this;
    }

    /**
     * Whether the engine supports SSH Tunnels
     *
     * @return disableSshTunneling
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DISABLE_SSH_TUNNELING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getDisableSshTunneling() {
        return disableSshTunneling;
    }


    @JsonProperty(JSON_PROPERTY_DISABLE_SSH_TUNNELING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDisableSshTunneling(Boolean disableSshTunneling) {
        this.disableSshTunneling = disableSshTunneling;
    }


    public ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation supportsFileUpload(Boolean supportsFileUpload) {

        this.supportsFileUpload = supportsFileUpload;
        return this;
    }

    /**
     * Whether the engine supports file uploads
     *
     * @return supportsFileUpload
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SUPPORTS_FILE_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getSupportsFileUpload() {
        return supportsFileUpload;
    }


    @JsonProperty(JSON_PROPERTY_SUPPORTS_FILE_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSupportsFileUpload(Boolean supportsFileUpload) {
        this.supportsFileUpload = supportsFileUpload;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation apiV1DatabaseAvailableGet200ResponseInnerEngineInformation = (ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation) o;
        return Objects.equals(this.disableSshTunneling, apiV1DatabaseAvailableGet200ResponseInnerEngineInformation.disableSshTunneling) &&
            Objects.equals(this.supportsFileUpload, apiV1DatabaseAvailableGet200ResponseInnerEngineInformation.supportsFileUpload);
    }

    @Override
    public int hashCode() {
        return Objects.hash(disableSshTunneling, supportsFileUpload);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation {\n");
        sb.append("    disableSshTunneling: ").append(toIndentedString(disableSshTunneling)).append("\n");
        sb.append("    supportsFileUpload: ").append(toIndentedString(supportsFileUpload)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

