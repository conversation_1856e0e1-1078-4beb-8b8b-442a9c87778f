/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartDataGeodeticParseOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataGeodeticParseOptionsSchema.JSON_PROPERTY_ALTITUDE,
    ChartDataGeodeticParseOptionsSchema.JSON_PROPERTY_GEODETIC,
    ChartDataGeodeticParseOptionsSchema.JSON_PROPERTY_LATITUDE,
    ChartDataGeodeticParseOptionsSchema.JSON_PROPERTY_LONGITUDE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataGeodeticParseOptionsSchema {
    public static final String JSON_PROPERTY_ALTITUDE = "altitude";
    private String altitude;

    public static final String JSON_PROPERTY_GEODETIC = "geodetic";
    private String geodetic;

    public static final String JSON_PROPERTY_LATITUDE = "latitude";
    private String latitude;

    public static final String JSON_PROPERTY_LONGITUDE = "longitude";
    private String longitude;

    public ChartDataGeodeticParseOptionsSchema() {
    }

    public ChartDataGeodeticParseOptionsSchema altitude(String altitude) {

        this.altitude = altitude;
        return this;
    }

    /**
     * Name of target column for decoded altitude. If omitted, altitude information in geodetic string is ignored.
     *
     * @return altitude
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALTITUDE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getAltitude() {
        return altitude;
    }


    @JsonProperty(JSON_PROPERTY_ALTITUDE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAltitude(String altitude) {
        this.altitude = altitude;
    }


    public ChartDataGeodeticParseOptionsSchema geodetic(String geodetic) {

        this.geodetic = geodetic;
        return this;
    }

    /**
     * Name of source column containing geodetic point strings
     *
     * @return geodetic
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_GEODETIC)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getGeodetic() {
        return geodetic;
    }


    @JsonProperty(JSON_PROPERTY_GEODETIC)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setGeodetic(String geodetic) {
        this.geodetic = geodetic;
    }


    public ChartDataGeodeticParseOptionsSchema latitude(String latitude) {

        this.latitude = latitude;
        return this;
    }

    /**
     * Name of target column for decoded latitude
     *
     * @return latitude
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_LATITUDE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getLatitude() {
        return latitude;
    }


    @JsonProperty(JSON_PROPERTY_LATITUDE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }


    public ChartDataGeodeticParseOptionsSchema longitude(String longitude) {

        this.longitude = longitude;
        return this;
    }

    /**
     * Name of target column for decoded longitude
     *
     * @return longitude
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_LONGITUDE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getLongitude() {
        return longitude;
    }


    @JsonProperty(JSON_PROPERTY_LONGITUDE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataGeodeticParseOptionsSchema chartDataGeodeticParseOptionsSchema = (ChartDataGeodeticParseOptionsSchema) o;
        return Objects.equals(this.altitude, chartDataGeodeticParseOptionsSchema.altitude) &&
            Objects.equals(this.geodetic, chartDataGeodeticParseOptionsSchema.geodetic) &&
            Objects.equals(this.latitude, chartDataGeodeticParseOptionsSchema.latitude) &&
            Objects.equals(this.longitude, chartDataGeodeticParseOptionsSchema.longitude);
    }

    @Override
    public int hashCode() {
        return Objects.hash(altitude, geodetic, latitude, longitude);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataGeodeticParseOptionsSchema {\n");
        sb.append("    altitude: ").append(toIndentedString(altitude)).append("\n");
        sb.append("    geodetic: ").append(toIndentedString(geodetic)).append("\n");
        sb.append("    latitude: ").append(toIndentedString(latitude)).append("\n");
        sb.append("    longitude: ").append(toIndentedString(longitude)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

