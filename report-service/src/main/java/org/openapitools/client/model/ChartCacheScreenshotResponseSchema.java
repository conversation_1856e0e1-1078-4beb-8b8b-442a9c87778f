/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartCacheScreenshotResponseSchema
 */
@JsonPropertyOrder({
    ChartCacheScreenshotResponseSchema.JSON_PROPERTY_CACHE_KEY,
    ChartCacheScreenshotResponseSchema.JSON_PROPERTY_CHART_URL,
    ChartCacheScreenshotResponseSchema.JSON_PROPERTY_IMAGE_URL
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartCacheScreenshotResponseSchema {
    public static final String JSON_PROPERTY_CACHE_KEY = "cache_key";
    private String cacheKey;

    public static final String JSON_PROPERTY_CHART_URL = "chart_url";
    private String chartUrl;

    public static final String JSON_PROPERTY_IMAGE_URL = "image_url";
    private String imageUrl;

    public ChartCacheScreenshotResponseSchema() {
    }

    public ChartCacheScreenshotResponseSchema cacheKey(String cacheKey) {

        this.cacheKey = cacheKey;
        return this;
    }

    /**
     * The cache key
     *
     * @return cacheKey
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCacheKey() {
        return cacheKey;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheKey(String cacheKey) {
        this.cacheKey = cacheKey;
    }


    public ChartCacheScreenshotResponseSchema chartUrl(String chartUrl) {

        this.chartUrl = chartUrl;
        return this;
    }

    /**
     * The url to render the chart
     *
     * @return chartUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHART_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getChartUrl() {
        return chartUrl;
    }


    @JsonProperty(JSON_PROPERTY_CHART_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChartUrl(String chartUrl) {
        this.chartUrl = chartUrl;
    }


    public ChartCacheScreenshotResponseSchema imageUrl(String imageUrl) {

        this.imageUrl = imageUrl;
        return this;
    }

    /**
     * The url to fetch the screenshot
     *
     * @return imageUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IMAGE_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getImageUrl() {
        return imageUrl;
    }


    @JsonProperty(JSON_PROPERTY_IMAGE_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartCacheScreenshotResponseSchema chartCacheScreenshotResponseSchema = (ChartCacheScreenshotResponseSchema) o;
        return Objects.equals(this.cacheKey, chartCacheScreenshotResponseSchema.cacheKey) &&
            Objects.equals(this.chartUrl, chartCacheScreenshotResponseSchema.chartUrl) &&
            Objects.equals(this.imageUrl, chartCacheScreenshotResponseSchema.imageUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cacheKey, chartUrl, imageUrl);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartCacheScreenshotResponseSchema {\n");
        sb.append("    cacheKey: ").append(toIndentedString(cacheKey)).append("\n");
        sb.append("    chartUrl: ").append(toIndentedString(chartUrl)).append("\n");
        sb.append("    imageUrl: ").append(toIndentedString(imageUrl)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

