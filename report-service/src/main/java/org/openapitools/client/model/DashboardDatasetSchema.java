/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.*;

/**
 * DashboardDatasetSchema
 */
@JsonPropertyOrder({
    DashboardDatasetSchema.JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM,
    DashboardDatasetSchema.JSON_PROPERTY_CACHE_TIMEOUT,
    DashboardDatasetSchema.JSON_PROPERTY_COLUMN_FORMATS,
    DashboardDatasetSchema.JSON_PROPERTY_COLUMN_TYPES,
    DashboardDatasetSchema.JSON_PROPERTY_COLUMNS,
    DashboardDatasetSchema.JSON_PROPERTY_CURRENCY_FORMATS,
    DashboardDatasetSchema.JSON_PROPERTY_DATABASE,
    DashboardDatasetSchema.JSON_PROPERTY_DATASOURCE_NAME,
    DashboardDatasetSchema.JSON_PROPERTY_DEFAULT_ENDPOINT,
    DashboardDatasetSchema.JSON_PROPERTY_EDIT_URL,
    DashboardDatasetSchema.JSON_PROPERTY_FETCH_VALUES_PREDICATE,
    DashboardDatasetSchema.JSON_PROPERTY_FILTER_SELECT,
    DashboardDatasetSchema.JSON_PROPERTY_FILTER_SELECT_ENABLED,
    DashboardDatasetSchema.JSON_PROPERTY_GRANULARITY_SQLA,
    DashboardDatasetSchema.JSON_PROPERTY_HEALTH_CHECK_MESSAGE,
    DashboardDatasetSchema.JSON_PROPERTY_ID,
    DashboardDatasetSchema.JSON_PROPERTY_IS_SQLLAB_VIEW,
    DashboardDatasetSchema.JSON_PROPERTY_MAIN_DTTM_COL,
    DashboardDatasetSchema.JSON_PROPERTY_METRICS,
    DashboardDatasetSchema.JSON_PROPERTY_NAME,
    DashboardDatasetSchema.JSON_PROPERTY_NORMALIZE_COLUMNS,
    DashboardDatasetSchema.JSON_PROPERTY_OFFSET,
    DashboardDatasetSchema.JSON_PROPERTY_ORDER_BY_CHOICES,
    DashboardDatasetSchema.JSON_PROPERTY_OWNERS,
    DashboardDatasetSchema.JSON_PROPERTY_PARAMS,
    DashboardDatasetSchema.JSON_PROPERTY_PERM,
    DashboardDatasetSchema.JSON_PROPERTY_SCHEMA,
    DashboardDatasetSchema.JSON_PROPERTY_SELECT_STAR,
    DashboardDatasetSchema.JSON_PROPERTY_SQL,
    DashboardDatasetSchema.JSON_PROPERTY_TABLE_NAME,
    DashboardDatasetSchema.JSON_PROPERTY_TEMPLATE_PARAMS,
    DashboardDatasetSchema.JSON_PROPERTY_TIME_GRAIN_SQLA,
    DashboardDatasetSchema.JSON_PROPERTY_TYPE,
    DashboardDatasetSchema.JSON_PROPERTY_UID,
    DashboardDatasetSchema.JSON_PROPERTY_VERBOSE_MAP
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DashboardDatasetSchema {
    public static final String JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM = "always_filter_main_dttm";
    private Boolean alwaysFilterMainDttm;

    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_COLUMN_FORMATS = "column_formats";
    private Object columnFormats;

    public static final String JSON_PROPERTY_COLUMN_TYPES = "column_types";
    private List<Integer> columnTypes;

    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<Object> columns;

    public static final String JSON_PROPERTY_CURRENCY_FORMATS = "currency_formats";
    private Object currencyFormats;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private Database database;

    public static final String JSON_PROPERTY_DATASOURCE_NAME = "datasource_name";
    private String datasourceName;

    public static final String JSON_PROPERTY_DEFAULT_ENDPOINT = "default_endpoint";
    private String defaultEndpoint;

    public static final String JSON_PROPERTY_EDIT_URL = "edit_url";
    private String editUrl;

    public static final String JSON_PROPERTY_FETCH_VALUES_PREDICATE = "fetch_values_predicate";
    private String fetchValuesPredicate;

    public static final String JSON_PROPERTY_FILTER_SELECT = "filter_select";
    private Boolean filterSelect;

    public static final String JSON_PROPERTY_FILTER_SELECT_ENABLED = "filter_select_enabled";
    private Boolean filterSelectEnabled;

    public static final String JSON_PROPERTY_GRANULARITY_SQLA = "granularity_sqla";
    private List<List<String>> granularitySqla;

    public static final String JSON_PROPERTY_HEALTH_CHECK_MESSAGE = "health_check_message";
    private String healthCheckMessage;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_SQLLAB_VIEW = "is_sqllab_view";
    private Boolean isSqllabView;

    public static final String JSON_PROPERTY_MAIN_DTTM_COL = "main_dttm_col";
    private String mainDttmCol;

    public static final String JSON_PROPERTY_METRICS = "metrics";
    private List<Object> metrics;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_NORMALIZE_COLUMNS = "normalize_columns";
    private Boolean normalizeColumns;

    public static final String JSON_PROPERTY_OFFSET = "offset";
    private Integer offset;

    public static final String JSON_PROPERTY_ORDER_BY_CHOICES = "order_by_choices";
    private List<List<String>> orderByChoices;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<Object> owners;

    public static final String JSON_PROPERTY_PARAMS = "params";
    private String params;

    public static final String JSON_PROPERTY_PERM = "perm";
    private String perm;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SELECT_STAR = "select_star";
    private String selectStar;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public static final String JSON_PROPERTY_TEMPLATE_PARAMS = "template_params";
    private String templateParams;

    public static final String JSON_PROPERTY_TIME_GRAIN_SQLA = "time_grain_sqla";
    private List<List<String>> timeGrainSqla;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public static final String JSON_PROPERTY_UID = "uid";
    private String uid;

    public static final String JSON_PROPERTY_VERBOSE_MAP = "verbose_map";
    private Map<String, String> verboseMap = new HashMap<>();

    public DashboardDatasetSchema() {
    }

    public DashboardDatasetSchema alwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {

        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
        return this;
    }

    /**
     * Get alwaysFilterMainDttm
     *
     * @return alwaysFilterMainDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAlwaysFilterMainDttm() {
        return alwaysFilterMainDttm;
    }


    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAlwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {
        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
    }


    public DashboardDatasetSchema cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Get cacheTimeout
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public DashboardDatasetSchema columnFormats(Object columnFormats) {

        this.columnFormats = columnFormats;
        return this;
    }

    /**
     * Get columnFormats
     *
     * @return columnFormats
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getColumnFormats() {
        return columnFormats;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnFormats(Object columnFormats) {
        this.columnFormats = columnFormats;
    }


    public DashboardDatasetSchema columnTypes(List<Integer> columnTypes) {

        this.columnTypes = columnTypes;
        return this;
    }

    public DashboardDatasetSchema addColumnTypesItem(Integer columnTypesItem) {
        if (this.columnTypes == null) {
            this.columnTypes = new ArrayList<>();
        }
        this.columnTypes.add(columnTypesItem);
        return this;
    }

    /**
     * Get columnTypes
     *
     * @return columnTypes
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_TYPES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getColumnTypes() {
        return columnTypes;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_TYPES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnTypes(List<Integer> columnTypes) {
        this.columnTypes = columnTypes;
    }


    public DashboardDatasetSchema columns(List<Object> columns) {

        this.columns = columns;
        return this;
    }

    public DashboardDatasetSchema addColumnsItem(Object columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * Get columns
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<Object> columns) {
        this.columns = columns;
    }


    public DashboardDatasetSchema currencyFormats(Object currencyFormats) {

        this.currencyFormats = currencyFormats;
        return this;
    }

    /**
     * Get currencyFormats
     *
     * @return currencyFormats
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CURRENCY_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getCurrencyFormats() {
        return currencyFormats;
    }


    @JsonProperty(JSON_PROPERTY_CURRENCY_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCurrencyFormats(Object currencyFormats) {
        this.currencyFormats = currencyFormats;
    }


    public DashboardDatasetSchema database(Database database) {

        this.database = database;
        return this;
    }

    /**
     * Get database
     *
     * @return database
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Database getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabase(Database database) {
        this.database = database;
    }


    public DashboardDatasetSchema datasourceName(String datasourceName) {

        this.datasourceName = datasourceName;
        return this;
    }

    /**
     * Get datasourceName
     *
     * @return datasourceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatasourceName() {
        return datasourceName;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }


    public DashboardDatasetSchema defaultEndpoint(String defaultEndpoint) {

        this.defaultEndpoint = defaultEndpoint;
        return this;
    }

    /**
     * Get defaultEndpoint
     *
     * @return defaultEndpoint
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDefaultEndpoint() {
        return defaultEndpoint;
    }


    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDefaultEndpoint(String defaultEndpoint) {
        this.defaultEndpoint = defaultEndpoint;
    }


    public DashboardDatasetSchema editUrl(String editUrl) {

        this.editUrl = editUrl;
        return this;
    }

    /**
     * Get editUrl
     *
     * @return editUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EDIT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getEditUrl() {
        return editUrl;
    }


    @JsonProperty(JSON_PROPERTY_EDIT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEditUrl(String editUrl) {
        this.editUrl = editUrl;
    }


    public DashboardDatasetSchema fetchValuesPredicate(String fetchValuesPredicate) {

        this.fetchValuesPredicate = fetchValuesPredicate;
        return this;
    }

    /**
     * Get fetchValuesPredicate
     *
     * @return fetchValuesPredicate
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getFetchValuesPredicate() {
        return fetchValuesPredicate;
    }


    @JsonProperty(JSON_PROPERTY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFetchValuesPredicate(String fetchValuesPredicate) {
        this.fetchValuesPredicate = fetchValuesPredicate;
    }


    public DashboardDatasetSchema filterSelect(Boolean filterSelect) {

        this.filterSelect = filterSelect;
        return this;
    }

    /**
     * Get filterSelect
     *
     * @return filterSelect
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER_SELECT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getFilterSelect() {
        return filterSelect;
    }


    @JsonProperty(JSON_PROPERTY_FILTER_SELECT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterSelect(Boolean filterSelect) {
        this.filterSelect = filterSelect;
    }


    public DashboardDatasetSchema filterSelectEnabled(Boolean filterSelectEnabled) {

        this.filterSelectEnabled = filterSelectEnabled;
        return this;
    }

    /**
     * Get filterSelectEnabled
     *
     * @return filterSelectEnabled
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER_SELECT_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getFilterSelectEnabled() {
        return filterSelectEnabled;
    }


    @JsonProperty(JSON_PROPERTY_FILTER_SELECT_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterSelectEnabled(Boolean filterSelectEnabled) {
        this.filterSelectEnabled = filterSelectEnabled;
    }


    public DashboardDatasetSchema granularitySqla(List<List<String>> granularitySqla) {

        this.granularitySqla = granularitySqla;
        return this;
    }

    public DashboardDatasetSchema addGranularitySqlaItem(List<String> granularitySqlaItem) {
        if (this.granularitySqla == null) {
            this.granularitySqla = new ArrayList<>();
        }
        this.granularitySqla.add(granularitySqlaItem);
        return this;
    }

    /**
     * Get granularitySqla
     *
     * @return granularitySqla
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GRANULARITY_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<List<String>> getGranularitySqla() {
        return granularitySqla;
    }


    @JsonProperty(JSON_PROPERTY_GRANULARITY_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGranularitySqla(List<List<String>> granularitySqla) {
        this.granularitySqla = granularitySqla;
    }


    public DashboardDatasetSchema healthCheckMessage(String healthCheckMessage) {

        this.healthCheckMessage = healthCheckMessage;
        return this;
    }

    /**
     * Get healthCheckMessage
     *
     * @return healthCheckMessage
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_HEALTH_CHECK_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getHealthCheckMessage() {
        return healthCheckMessage;
    }


    @JsonProperty(JSON_PROPERTY_HEALTH_CHECK_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setHealthCheckMessage(String healthCheckMessage) {
        this.healthCheckMessage = healthCheckMessage;
    }


    public DashboardDatasetSchema id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DashboardDatasetSchema isSqllabView(Boolean isSqllabView) {

        this.isSqllabView = isSqllabView;
        return this;
    }

    /**
     * Get isSqllabView
     *
     * @return isSqllabView
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_SQLLAB_VIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsSqllabView() {
        return isSqllabView;
    }


    @JsonProperty(JSON_PROPERTY_IS_SQLLAB_VIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsSqllabView(Boolean isSqllabView) {
        this.isSqllabView = isSqllabView;
    }


    public DashboardDatasetSchema mainDttmCol(String mainDttmCol) {

        this.mainDttmCol = mainDttmCol;
        return this;
    }

    /**
     * Get mainDttmCol
     *
     * @return mainDttmCol
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MAIN_DTTM_COL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMainDttmCol() {
        return mainDttmCol;
    }


    @JsonProperty(JSON_PROPERTY_MAIN_DTTM_COL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMainDttmCol(String mainDttmCol) {
        this.mainDttmCol = mainDttmCol;
    }


    public DashboardDatasetSchema metrics(List<Object> metrics) {

        this.metrics = metrics;
        return this;
    }

    public DashboardDatasetSchema addMetricsItem(Object metricsItem) {
        if (this.metrics == null) {
            this.metrics = new ArrayList<>();
        }
        this.metrics.add(metricsItem);
        return this;
    }

    /**
     * Get metrics
     *
     * @return metrics
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getMetrics() {
        return metrics;
    }


    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMetrics(List<Object> metrics) {
        this.metrics = metrics;
    }


    public DashboardDatasetSchema name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public DashboardDatasetSchema normalizeColumns(Boolean normalizeColumns) {

        this.normalizeColumns = normalizeColumns;
        return this;
    }

    /**
     * Get normalizeColumns
     *
     * @return normalizeColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getNormalizeColumns() {
        return normalizeColumns;
    }


    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setNormalizeColumns(Boolean normalizeColumns) {
        this.normalizeColumns = normalizeColumns;
    }


    public DashboardDatasetSchema offset(Integer offset) {

        this.offset = offset;
        return this;
    }

    /**
     * Get offset
     *
     * @return offset
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getOffset() {
        return offset;
    }


    @JsonProperty(JSON_PROPERTY_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOffset(Integer offset) {
        this.offset = offset;
    }


    public DashboardDatasetSchema orderByChoices(List<List<String>> orderByChoices) {

        this.orderByChoices = orderByChoices;
        return this;
    }

    public DashboardDatasetSchema addOrderByChoicesItem(List<String> orderByChoicesItem) {
        if (this.orderByChoices == null) {
            this.orderByChoices = new ArrayList<>();
        }
        this.orderByChoices.add(orderByChoicesItem);
        return this;
    }

    /**
     * Get orderByChoices
     *
     * @return orderByChoices
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ORDER_BY_CHOICES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<List<String>> getOrderByChoices() {
        return orderByChoices;
    }


    @JsonProperty(JSON_PROPERTY_ORDER_BY_CHOICES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOrderByChoices(List<List<String>> orderByChoices) {
        this.orderByChoices = orderByChoices;
    }


    public DashboardDatasetSchema owners(List<Object> owners) {

        this.owners = owners;
        return this;
    }

    public DashboardDatasetSchema addOwnersItem(Object ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<Object> owners) {
        this.owners = owners;
    }


    public DashboardDatasetSchema params(String params) {

        this.params = params;
        return this;
    }

    /**
     * Get params
     *
     * @return params
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getParams() {
        return params;
    }


    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setParams(String params) {
        this.params = params;
    }


    public DashboardDatasetSchema perm(String perm) {

        this.perm = perm;
        return this;
    }

    /**
     * Get perm
     *
     * @return perm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PERM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPerm() {
        return perm;
    }


    @JsonProperty(JSON_PROPERTY_PERM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPerm(String perm) {
        this.perm = perm;
    }


    public DashboardDatasetSchema schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public DashboardDatasetSchema selectStar(String selectStar) {

        this.selectStar = selectStar;
        return this;
    }

    /**
     * Get selectStar
     *
     * @return selectStar
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SELECT_STAR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSelectStar() {
        return selectStar;
    }


    @JsonProperty(JSON_PROPERTY_SELECT_STAR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSelectStar(String selectStar) {
        this.selectStar = selectStar;
    }


    public DashboardDatasetSchema sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public DashboardDatasetSchema tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * Get tableName
     *
     * @return tableName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }


    public DashboardDatasetSchema templateParams(String templateParams) {

        this.templateParams = templateParams;
        return this;
    }

    /**
     * Get templateParams
     *
     * @return templateParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTemplateParams() {
        return templateParams;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams;
    }


    public DashboardDatasetSchema timeGrainSqla(List<List<String>> timeGrainSqla) {

        this.timeGrainSqla = timeGrainSqla;
        return this;
    }

    public DashboardDatasetSchema addTimeGrainSqlaItem(List<String> timeGrainSqlaItem) {
        if (this.timeGrainSqla == null) {
            this.timeGrainSqla = new ArrayList<>();
        }
        this.timeGrainSqla.add(timeGrainSqlaItem);
        return this;
    }

    /**
     * Get timeGrainSqla
     *
     * @return timeGrainSqla
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_GRAIN_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<List<String>> getTimeGrainSqla() {
        return timeGrainSqla;
    }


    @JsonProperty(JSON_PROPERTY_TIME_GRAIN_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeGrainSqla(List<List<String>> timeGrainSqla) {
        this.timeGrainSqla = timeGrainSqla;
    }


    public DashboardDatasetSchema type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Get type
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(String type) {
        this.type = type;
    }


    public DashboardDatasetSchema uid(String uid) {

        this.uid = uid;
        return this;
    }

    /**
     * Get uid
     *
     * @return uid
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_UID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUid() {
        return uid;
    }


    @JsonProperty(JSON_PROPERTY_UID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUid(String uid) {
        this.uid = uid;
    }


    public DashboardDatasetSchema verboseMap(Map<String, String> verboseMap) {

        this.verboseMap = verboseMap;
        return this;
    }

    public DashboardDatasetSchema putVerboseMapItem(String key, String verboseMapItem) {
        if (this.verboseMap == null) {
            this.verboseMap = new HashMap<>();
        }
        this.verboseMap.put(key, verboseMapItem);
        return this;
    }

    /**
     * Get verboseMap
     *
     * @return verboseMap
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VERBOSE_MAP)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Map<String, String> getVerboseMap() {
        return verboseMap;
    }


    @JsonProperty(JSON_PROPERTY_VERBOSE_MAP)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVerboseMap(Map<String, String> verboseMap) {
        this.verboseMap = verboseMap;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DashboardDatasetSchema dashboardDatasetSchema = (DashboardDatasetSchema) o;
        return Objects.equals(this.alwaysFilterMainDttm, dashboardDatasetSchema.alwaysFilterMainDttm) &&
            Objects.equals(this.cacheTimeout, dashboardDatasetSchema.cacheTimeout) &&
            Objects.equals(this.columnFormats, dashboardDatasetSchema.columnFormats) &&
            Objects.equals(this.columnTypes, dashboardDatasetSchema.columnTypes) &&
            Objects.equals(this.columns, dashboardDatasetSchema.columns) &&
            Objects.equals(this.currencyFormats, dashboardDatasetSchema.currencyFormats) &&
            Objects.equals(this.database, dashboardDatasetSchema.database) &&
            Objects.equals(this.datasourceName, dashboardDatasetSchema.datasourceName) &&
            Objects.equals(this.defaultEndpoint, dashboardDatasetSchema.defaultEndpoint) &&
            Objects.equals(this.editUrl, dashboardDatasetSchema.editUrl) &&
            Objects.equals(this.fetchValuesPredicate, dashboardDatasetSchema.fetchValuesPredicate) &&
            Objects.equals(this.filterSelect, dashboardDatasetSchema.filterSelect) &&
            Objects.equals(this.filterSelectEnabled, dashboardDatasetSchema.filterSelectEnabled) &&
            Objects.equals(this.granularitySqla, dashboardDatasetSchema.granularitySqla) &&
            Objects.equals(this.healthCheckMessage, dashboardDatasetSchema.healthCheckMessage) &&
            Objects.equals(this.id, dashboardDatasetSchema.id) &&
            Objects.equals(this.isSqllabView, dashboardDatasetSchema.isSqllabView) &&
            Objects.equals(this.mainDttmCol, dashboardDatasetSchema.mainDttmCol) &&
            Objects.equals(this.metrics, dashboardDatasetSchema.metrics) &&
            Objects.equals(this.name, dashboardDatasetSchema.name) &&
            Objects.equals(this.normalizeColumns, dashboardDatasetSchema.normalizeColumns) &&
            Objects.equals(this.offset, dashboardDatasetSchema.offset) &&
            Objects.equals(this.orderByChoices, dashboardDatasetSchema.orderByChoices) &&
            Objects.equals(this.owners, dashboardDatasetSchema.owners) &&
            Objects.equals(this.params, dashboardDatasetSchema.params) &&
            Objects.equals(this.perm, dashboardDatasetSchema.perm) &&
            Objects.equals(this.schema, dashboardDatasetSchema.schema) &&
            Objects.equals(this.selectStar, dashboardDatasetSchema.selectStar) &&
            Objects.equals(this.sql, dashboardDatasetSchema.sql) &&
            Objects.equals(this.tableName, dashboardDatasetSchema.tableName) &&
            Objects.equals(this.templateParams, dashboardDatasetSchema.templateParams) &&
            Objects.equals(this.timeGrainSqla, dashboardDatasetSchema.timeGrainSqla) &&
            Objects.equals(this.type, dashboardDatasetSchema.type) &&
            Objects.equals(this.uid, dashboardDatasetSchema.uid) &&
            Objects.equals(this.verboseMap, dashboardDatasetSchema.verboseMap);
    }

    @Override
    public int hashCode() {
        return Objects.hash(alwaysFilterMainDttm, cacheTimeout, columnFormats, columnTypes, columns, currencyFormats, database, datasourceName, defaultEndpoint, editUrl, fetchValuesPredicate, filterSelect, filterSelectEnabled, granularitySqla, healthCheckMessage, id, isSqllabView, mainDttmCol, metrics, name, normalizeColumns, offset, orderByChoices, owners, params, perm, schema, selectStar, sql, tableName, templateParams, timeGrainSqla, type, uid, verboseMap);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DashboardDatasetSchema {\n");
        sb.append("    alwaysFilterMainDttm: ").append(toIndentedString(alwaysFilterMainDttm)).append("\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    columnFormats: ").append(toIndentedString(columnFormats)).append("\n");
        sb.append("    columnTypes: ").append(toIndentedString(columnTypes)).append("\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    currencyFormats: ").append(toIndentedString(currencyFormats)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    datasourceName: ").append(toIndentedString(datasourceName)).append("\n");
        sb.append("    defaultEndpoint: ").append(toIndentedString(defaultEndpoint)).append("\n");
        sb.append("    editUrl: ").append(toIndentedString(editUrl)).append("\n");
        sb.append("    fetchValuesPredicate: ").append(toIndentedString(fetchValuesPredicate)).append("\n");
        sb.append("    filterSelect: ").append(toIndentedString(filterSelect)).append("\n");
        sb.append("    filterSelectEnabled: ").append(toIndentedString(filterSelectEnabled)).append("\n");
        sb.append("    granularitySqla: ").append(toIndentedString(granularitySqla)).append("\n");
        sb.append("    healthCheckMessage: ").append(toIndentedString(healthCheckMessage)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isSqllabView: ").append(toIndentedString(isSqllabView)).append("\n");
        sb.append("    mainDttmCol: ").append(toIndentedString(mainDttmCol)).append("\n");
        sb.append("    metrics: ").append(toIndentedString(metrics)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    normalizeColumns: ").append(toIndentedString(normalizeColumns)).append("\n");
        sb.append("    offset: ").append(toIndentedString(offset)).append("\n");
        sb.append("    orderByChoices: ").append(toIndentedString(orderByChoices)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    params: ").append(toIndentedString(params)).append("\n");
        sb.append("    perm: ").append(toIndentedString(perm)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    selectStar: ").append(toIndentedString(selectStar)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("    templateParams: ").append(toIndentedString(templateParams)).append("\n");
        sb.append("    timeGrainSqla: ").append(toIndentedString(timeGrainSqla)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
        sb.append("    verboseMap: ").append(toIndentedString(verboseMap)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

