/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * LogRestApiGet
 */
@JsonPropertyOrder({
    LogRestApiGet.JSON_PROPERTY_ACTION,
    LogRestApiGet.JSON_PROPERTY_DASHBOARD_ID,
    LogRestApiGet.JSON_PROPERTY_DTTM,
    LogRestApiGet.JSON_PROPERTY_DURATION_MS,
    LogRestApiGet.JSON_PROPERTY_JSON,
    LogRestApiGet.JSON_PROPERTY_REFERRER,
    LogRestApiGet.JSON_PROPERTY_SLICE_ID,
    LogRestApiGet.JSON_PROPERTY_USER,
    LogRestApiGet.JSON_PROPERTY_USER_ID
})
@JsonTypeName("LogRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class LogRestApiGet {
    public static final String JSON_PROPERTY_ACTION = "action";
    private String action;

    public static final String JSON_PROPERTY_DASHBOARD_ID = "dashboard_id";
    private Integer dashboardId;

    public static final String JSON_PROPERTY_DTTM = "dttm";
    private OffsetDateTime dttm;

    public static final String JSON_PROPERTY_DURATION_MS = "duration_ms";
    private Integer durationMs;

    public static final String JSON_PROPERTY_JSON = "json";
    private String json;

    public static final String JSON_PROPERTY_REFERRER = "referrer";
    private String referrer;

    public static final String JSON_PROPERTY_SLICE_ID = "slice_id";
    private Integer sliceId;

    public static final String JSON_PROPERTY_USER = "user";
    private LogRestApiGetUser user;

    public static final String JSON_PROPERTY_USER_ID = "user_id";
    private Integer userId;

    public LogRestApiGet() {
    }

    public LogRestApiGet action(String action) {

        this.action = action;
        return this;
    }

    /**
     * Get action
     *
     * @return action
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getAction() {
        return action;
    }


    @JsonProperty(JSON_PROPERTY_ACTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAction(String action) {
        this.action = action;
    }


    public LogRestApiGet dashboardId(Integer dashboardId) {

        this.dashboardId = dashboardId;
        return this;
    }

    /**
     * Get dashboardId
     *
     * @return dashboardId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDashboardId() {
        return dashboardId;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardId(Integer dashboardId) {
        this.dashboardId = dashboardId;
    }


    public LogRestApiGet dttm(OffsetDateTime dttm) {

        this.dttm = dttm;
        return this;
    }

    /**
     * Get dttm
     *
     * @return dttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getDttm() {
        return dttm;
    }


    @JsonProperty(JSON_PROPERTY_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDttm(OffsetDateTime dttm) {
        this.dttm = dttm;
    }


    public LogRestApiGet durationMs(Integer durationMs) {

        this.durationMs = durationMs;
        return this;
    }

    /**
     * Get durationMs
     *
     * @return durationMs
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DURATION_MS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDurationMs() {
        return durationMs;
    }


    @JsonProperty(JSON_PROPERTY_DURATION_MS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDurationMs(Integer durationMs) {
        this.durationMs = durationMs;
    }


    public LogRestApiGet json(String json) {

        this.json = json;
        return this;
    }

    /**
     * Get json
     *
     * @return json
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJson() {
        return json;
    }


    @JsonProperty(JSON_PROPERTY_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJson(String json) {
        this.json = json;
    }


    public LogRestApiGet referrer(String referrer) {

        this.referrer = referrer;
        return this;
    }

    /**
     * Get referrer
     *
     * @return referrer
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_REFERRER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getReferrer() {
        return referrer;
    }


    @JsonProperty(JSON_PROPERTY_REFERRER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setReferrer(String referrer) {
        this.referrer = referrer;
    }


    public LogRestApiGet sliceId(Integer sliceId) {

        this.sliceId = sliceId;
        return this;
    }

    /**
     * Get sliceId
     *
     * @return sliceId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getSliceId() {
        return sliceId;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceId(Integer sliceId) {
        this.sliceId = sliceId;
    }


    public LogRestApiGet user(LogRestApiGetUser user) {

        this.user = user;
        return this;
    }

    /**
     * Get user
     *
     * @return user
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public LogRestApiGetUser getUser() {
        return user;
    }


    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUser(LogRestApiGetUser user) {
        this.user = user;
    }


    public LogRestApiGet userId(Integer userId) {

        this.userId = userId;
        return this;
    }

    /**
     * Get userId
     *
     * @return userId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getUserId() {
        return userId;
    }


    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogRestApiGet logRestApiGet = (LogRestApiGet) o;
        return Objects.equals(this.action, logRestApiGet.action) &&
            Objects.equals(this.dashboardId, logRestApiGet.dashboardId) &&
            Objects.equals(this.dttm, logRestApiGet.dttm) &&
            Objects.equals(this.durationMs, logRestApiGet.durationMs) &&
            Objects.equals(this.json, logRestApiGet.json) &&
            Objects.equals(this.referrer, logRestApiGet.referrer) &&
            Objects.equals(this.sliceId, logRestApiGet.sliceId) &&
            Objects.equals(this.user, logRestApiGet.user) &&
            Objects.equals(this.userId, logRestApiGet.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(action, dashboardId, dttm, durationMs, json, referrer, sliceId, user, userId);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class LogRestApiGet {\n");
        sb.append("    action: ").append(toIndentedString(action)).append("\n");
        sb.append("    dashboardId: ").append(toIndentedString(dashboardId)).append("\n");
        sb.append("    dttm: ").append(toIndentedString(dttm)).append("\n");
        sb.append("    durationMs: ").append(toIndentedString(durationMs)).append("\n");
        sb.append("    json: ").append(toIndentedString(json)).append("\n");
        sb.append("    referrer: ").append(toIndentedString(referrer)).append("\n");
        sb.append("    sliceId: ").append(toIndentedString(sliceId)).append("\n");
        sb.append("    user: ").append(toIndentedString(user)).append("\n");
        sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

