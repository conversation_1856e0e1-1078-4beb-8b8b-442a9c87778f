/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Map;
import java.util.Objects;

/**
 * ApiV1AnnotationLayerGet200ResponseLabelColumns
 */
@JsonPropertyOrder({
    ApiV1AnnotationLayerGet200ResponseLabelColumns.JSON_PROPERTY_COLUMN_NAME
})
@JsonTypeName("_api_v1_annotation_layer__get_200_response_label_columns")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1AnnotationLayerGet200ResponseLabelColumns {
    public static final String JSON_PROPERTY_COLUMN_NAME = "column_name";
    private Map<String, String> columnName;

    public ApiV1AnnotationLayerGet200ResponseLabelColumns() {
    }

    public ApiV1AnnotationLayerGet200ResponseLabelColumns columnName(Map<String, String> columnName) {

        this.columnName = columnName;
        return this;
    }

    /**
     * The label for the column name. Will be translated by babel
     *
     * @return columnName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Map<String, String> getColumnName() {
        return columnName;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnName(Map<String, String> columnName) {
        this.columnName = columnName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1AnnotationLayerGet200ResponseLabelColumns apiV1AnnotationLayerGet200ResponseLabelColumns = (ApiV1AnnotationLayerGet200ResponseLabelColumns) o;
        return Objects.equals(this.columnName, apiV1AnnotationLayerGet200ResponseLabelColumns.columnName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columnName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1AnnotationLayerGet200ResponseLabelColumns {\n");
        sb.append("    columnName: ").append(toIndentedString(columnName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

