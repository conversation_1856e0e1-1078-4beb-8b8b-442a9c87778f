/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * ChartDataAdhocMetricSchema
 */
@JsonPropertyOrder({
    ChartDataAdhocMetricSchema.JSON_PROPERTY_AGGREGATE,
    ChartDataAdhocMetricSchema.JSON_PROPERTY_COLUMN,
    ChartDataAdhocMetricSchema.JSON_PROPERTY_EXPRESSION_TYPE,
    ChartDataAdhocMetricSchema.JSON_PROPERTY_HAS_CUSTOM_LABEL,
    ChartDataAdhocMetricSchema.JSON_PROPERTY_IS_EXTRA,
    ChartDataAdhocMetricSchema.JSON_PROPERTY_LABEL,
    ChartDataAdhocMetricSchema.JSON_PROPERTY_OPTION_NAME,
    ChartDataAdhocMetricSchema.JSON_PROPERTY_SQL_EXPRESSION,
    ChartDataAdhocMetricSchema.JSON_PROPERTY_TIME_GRAIN
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataAdhocMetricSchema {
    /**
     * Aggregation operator.Only required for simple expression types.
     */
    public enum AggregateEnum {
        AVG("AVG"),

        COUNT("COUNT"),

        COUNT_DISTINCT("COUNT_DISTINCT"),

        MAX("MAX"),

        MIN("MIN"),

        SUM("SUM");

        private String value;

        AggregateEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static AggregateEnum fromValue(String value) {
            for (AggregateEnum b : AggregateEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_AGGREGATE = "aggregate";
    private AggregateEnum aggregate;

    public static final String JSON_PROPERTY_COLUMN = "column";
    private ChartDataColumn column;

    /**
     * Simple or SQL metric
     */
    public enum ExpressionTypeEnum {
        SIMPLE("SIMPLE"),

        SQL("SQL");

        private String value;

        ExpressionTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static ExpressionTypeEnum fromValue(String value) {
            for (ExpressionTypeEnum b : ExpressionTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_EXPRESSION_TYPE = "expressionType";
    private ExpressionTypeEnum expressionType;

    public static final String JSON_PROPERTY_HAS_CUSTOM_LABEL = "hasCustomLabel";
    private Boolean hasCustomLabel;

    public static final String JSON_PROPERTY_IS_EXTRA = "isExtra";
    private Boolean isExtra;

    public static final String JSON_PROPERTY_LABEL = "label";
    private String label;

    public static final String JSON_PROPERTY_OPTION_NAME = "optionName";
    private String optionName;

    public static final String JSON_PROPERTY_SQL_EXPRESSION = "sqlExpression";
    private String sqlExpression;

    public static final String JSON_PROPERTY_TIME_GRAIN = "timeGrain";
    private String timeGrain;

    public ChartDataAdhocMetricSchema() {
    }

    public ChartDataAdhocMetricSchema aggregate(AggregateEnum aggregate) {

        this.aggregate = aggregate;
        return this;
    }

    /**
     * Aggregation operator.Only required for simple expression types.
     *
     * @return aggregate
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_AGGREGATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public AggregateEnum getAggregate() {
        return aggregate;
    }


    @JsonProperty(JSON_PROPERTY_AGGREGATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAggregate(AggregateEnum aggregate) {
        this.aggregate = aggregate;
    }


    public ChartDataAdhocMetricSchema column(ChartDataColumn column) {

        this.column = column;
        return this;
    }

    /**
     * Get column
     *
     * @return column
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataColumn getColumn() {
        return column;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumn(ChartDataColumn column) {
        this.column = column;
    }


    public ChartDataAdhocMetricSchema expressionType(ExpressionTypeEnum expressionType) {

        this.expressionType = expressionType;
        return this;
    }

    /**
     * Simple or SQL metric
     *
     * @return expressionType
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_EXPRESSION_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public ExpressionTypeEnum getExpressionType() {
        return expressionType;
    }


    @JsonProperty(JSON_PROPERTY_EXPRESSION_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setExpressionType(ExpressionTypeEnum expressionType) {
        this.expressionType = expressionType;
    }


    public ChartDataAdhocMetricSchema hasCustomLabel(Boolean hasCustomLabel) {

        this.hasCustomLabel = hasCustomLabel;
        return this;
    }

    /**
     * When false, the label will be automatically generated based on the aggregate expression. When true, a custom label has to be specified.
     *
     * @return hasCustomLabel
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_HAS_CUSTOM_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getHasCustomLabel() {
        return hasCustomLabel;
    }


    @JsonProperty(JSON_PROPERTY_HAS_CUSTOM_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setHasCustomLabel(Boolean hasCustomLabel) {
        this.hasCustomLabel = hasCustomLabel;
    }


    public ChartDataAdhocMetricSchema isExtra(Boolean isExtra) {

        this.isExtra = isExtra;
        return this;
    }

    /**
     * Indicates if the filter has been added by a filter component as opposed to being a part of the original query.
     *
     * @return isExtra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsExtra() {
        return isExtra;
    }


    @JsonProperty(JSON_PROPERTY_IS_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsExtra(Boolean isExtra) {
        this.isExtra = isExtra;
    }


    public ChartDataAdhocMetricSchema label(String label) {

        this.label = label;
        return this;
    }

    /**
     * Label for the metric. Is automatically generated unlesshasCustomLabel is true, in which case label must be defined.
     *
     * @return label
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLabel() {
        return label;
    }


    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLabel(String label) {
        this.label = label;
    }


    public ChartDataAdhocMetricSchema optionName(String optionName) {

        this.optionName = optionName;
        return this;
    }

    /**
     * Unique identifier. Can be any string value, as long as all metrics have a unique identifier. If undefined, a random namewill be generated.
     *
     * @return optionName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OPTION_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getOptionName() {
        return optionName;
    }


    @JsonProperty(JSON_PROPERTY_OPTION_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOptionName(String optionName) {
        this.optionName = optionName;
    }


    public ChartDataAdhocMetricSchema sqlExpression(String sqlExpression) {

        this.sqlExpression = sqlExpression;
        return this;
    }

    /**
     * The metric as defined by a SQL aggregate expression. Only required for SQL expression type.
     *
     * @return sqlExpression
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL_EXPRESSION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSqlExpression() {
        return sqlExpression;
    }


    @JsonProperty(JSON_PROPERTY_SQL_EXPRESSION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSqlExpression(String sqlExpression) {
        this.sqlExpression = sqlExpression;
    }


    public ChartDataAdhocMetricSchema timeGrain(String timeGrain) {

        this.timeGrain = timeGrain;
        return this;
    }

    /**
     * Optional time grain for temporal filters
     *
     * @return timeGrain
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_GRAIN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTimeGrain() {
        return timeGrain;
    }


    @JsonProperty(JSON_PROPERTY_TIME_GRAIN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeGrain(String timeGrain) {
        this.timeGrain = timeGrain;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataAdhocMetricSchema chartDataAdhocMetricSchema = (ChartDataAdhocMetricSchema) o;
        return Objects.equals(this.aggregate, chartDataAdhocMetricSchema.aggregate) &&
            Objects.equals(this.column, chartDataAdhocMetricSchema.column) &&
            Objects.equals(this.expressionType, chartDataAdhocMetricSchema.expressionType) &&
            Objects.equals(this.hasCustomLabel, chartDataAdhocMetricSchema.hasCustomLabel) &&
            Objects.equals(this.isExtra, chartDataAdhocMetricSchema.isExtra) &&
            Objects.equals(this.label, chartDataAdhocMetricSchema.label) &&
            Objects.equals(this.optionName, chartDataAdhocMetricSchema.optionName) &&
            Objects.equals(this.sqlExpression, chartDataAdhocMetricSchema.sqlExpression) &&
            Objects.equals(this.timeGrain, chartDataAdhocMetricSchema.timeGrain);
    }

    @Override
    public int hashCode() {
        return Objects.hash(aggregate, column, expressionType, hasCustomLabel, isExtra, label, optionName, sqlExpression, timeGrain);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataAdhocMetricSchema {\n");
        sb.append("    aggregate: ").append(toIndentedString(aggregate)).append("\n");
        sb.append("    column: ").append(toIndentedString(column)).append("\n");
        sb.append("    expressionType: ").append(toIndentedString(expressionType)).append("\n");
        sb.append("    hasCustomLabel: ").append(toIndentedString(hasCustomLabel)).append("\n");
        sb.append("    isExtra: ").append(toIndentedString(isExtra)).append("\n");
        sb.append("    label: ").append(toIndentedString(label)).append("\n");
        sb.append("    optionName: ").append(toIndentedString(optionName)).append("\n");
        sb.append("    sqlExpression: ").append(toIndentedString(sqlExpression)).append("\n");
        sb.append("    timeGrain: ").append(toIndentedString(timeGrain)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

