/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1AnnotationLayerPkAnnotationGet200Response
 */
@JsonPropertyOrder({
    ApiV1AnnotationLayerPkAnnotationGet200Response.JSON_PROPERTY_COUNT,
    ApiV1AnnotationLayerPkAnnotationGet200Response.JSON_PROPERTY_IDS,
    ApiV1AnnotationLayerPkAnnotationGet200Response.JSON_PROPERTY_RESULT
})
@JsonTypeName("_api_v1_annotation_layer__pk__annotation__get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1AnnotationLayerPkAnnotationGet200Response {
    public static final String JSON_PROPERTY_COUNT = "count";
    private BigDecimal count;

    public static final String JSON_PROPERTY_IDS = "ids";
    private List<String> ids;

    public static final String JSON_PROPERTY_RESULT = "result";
    private List<AnnotationRestApiGetList> result;

    public ApiV1AnnotationLayerPkAnnotationGet200Response() {
    }

    public ApiV1AnnotationLayerPkAnnotationGet200Response count(BigDecimal count) {

        this.count = count;
        return this;
    }

    /**
     * The total record count on the backend
     *
     * @return count
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getCount() {
        return count;
    }


    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCount(BigDecimal count) {
        this.count = count;
    }


    public ApiV1AnnotationLayerPkAnnotationGet200Response ids(List<String> ids) {

        this.ids = ids;
        return this;
    }

    public ApiV1AnnotationLayerPkAnnotationGet200Response addIdsItem(String idsItem) {
        if (this.ids == null) {
            this.ids = new ArrayList<>();
        }
        this.ids.add(idsItem);
        return this;
    }

    /**
     * A list of annotation ids
     *
     * @return ids
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getIds() {
        return ids;
    }


    @JsonProperty(JSON_PROPERTY_IDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIds(List<String> ids) {
        this.ids = ids;
    }


    public ApiV1AnnotationLayerPkAnnotationGet200Response result(List<AnnotationRestApiGetList> result) {

        this.result = result;
        return this;
    }

    public ApiV1AnnotationLayerPkAnnotationGet200Response addResultItem(AnnotationRestApiGetList resultItem) {
        if (this.result == null) {
            this.result = new ArrayList<>();
        }
        this.result.add(resultItem);
        return this;
    }

    /**
     * The result from the get list query
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<AnnotationRestApiGetList> getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(List<AnnotationRestApiGetList> result) {
        this.result = result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1AnnotationLayerPkAnnotationGet200Response apiV1AnnotationLayerPkAnnotationGet200Response = (ApiV1AnnotationLayerPkAnnotationGet200Response) o;
        return Objects.equals(this.count, apiV1AnnotationLayerPkAnnotationGet200Response.count) &&
            Objects.equals(this.ids, apiV1AnnotationLayerPkAnnotationGet200Response.ids) &&
            Objects.equals(this.result, apiV1AnnotationLayerPkAnnotationGet200Response.result);
    }

    @Override
    public int hashCode() {
        return Objects.hash(count, ids, result);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1AnnotationLayerPkAnnotationGet200Response {\n");
        sb.append("    count: ").append(toIndentedString(count)).append("\n");
        sb.append("    ids: ").append(toIndentedString(ids)).append("\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

