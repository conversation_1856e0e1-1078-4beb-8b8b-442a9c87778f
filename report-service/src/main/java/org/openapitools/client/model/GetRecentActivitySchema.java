/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * GetRecentActivitySchema
 */
@JsonPropertyOrder({
    GetRecentActivitySchema.JSON_PROPERTY_ACTIONS,
    GetRecentActivitySchema.JSON_PROPERTY_DISTINCT,
    GetRecentActivitySchema.JSON_PROPERTY_PAGE,
    GetRecentActivitySchema.JSON_PROPERTY_PAGE_SIZE
})
@JsonTypeName("get_recent_activity_schema")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class GetRecentActivitySchema {
    public static final String JSON_PROPERTY_ACTIONS = "actions";
    private List<String> actions;

    public static final String JSON_PROPERTY_DISTINCT = "distinct";
    private Boolean distinct;

    public static final String JSON_PROPERTY_PAGE = "page";
    private BigDecimal page;

    public static final String JSON_PROPERTY_PAGE_SIZE = "page_size";
    private BigDecimal pageSize;

    public GetRecentActivitySchema() {
    }

    public GetRecentActivitySchema actions(List<String> actions) {

        this.actions = actions;
        return this;
    }

    public GetRecentActivitySchema addActionsItem(String actionsItem) {
        if (this.actions == null) {
            this.actions = new ArrayList<>();
        }
        this.actions.add(actionsItem);
        return this;
    }

    /**
     * Get actions
     *
     * @return actions
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getActions() {
        return actions;
    }


    @JsonProperty(JSON_PROPERTY_ACTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setActions(List<String> actions) {
        this.actions = actions;
    }


    public GetRecentActivitySchema distinct(Boolean distinct) {

        this.distinct = distinct;
        return this;
    }

    /**
     * Get distinct
     *
     * @return distinct
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DISTINCT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getDistinct() {
        return distinct;
    }


    @JsonProperty(JSON_PROPERTY_DISTINCT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDistinct(Boolean distinct) {
        this.distinct = distinct;
    }


    public GetRecentActivitySchema page(BigDecimal page) {

        this.page = page;
        return this;
    }

    /**
     * Get page
     *
     * @return page
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getPage() {
        return page;
    }


    @JsonProperty(JSON_PROPERTY_PAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPage(BigDecimal page) {
        this.page = page;
    }


    public GetRecentActivitySchema pageSize(BigDecimal pageSize) {

        this.pageSize = pageSize;
        return this;
    }

    /**
     * Get pageSize
     *
     * @return pageSize
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getPageSize() {
        return pageSize;
    }


    @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPageSize(BigDecimal pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetRecentActivitySchema getRecentActivitySchema = (GetRecentActivitySchema) o;
        return Objects.equals(this.actions, getRecentActivitySchema.actions) &&
            Objects.equals(this.distinct, getRecentActivitySchema.distinct) &&
            Objects.equals(this.page, getRecentActivitySchema.page) &&
            Objects.equals(this.pageSize, getRecentActivitySchema.pageSize);
    }

    @Override
    public int hashCode() {
        return Objects.hash(actions, distinct, page, pageSize);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class GetRecentActivitySchema {\n");
        sb.append("    actions: ").append(toIndentedString(actions)).append("\n");
        sb.append("    distinct: ").append(toIndentedString(distinct)).append("\n");
        sb.append("    page: ").append(toIndentedString(page)).append("\n");
        sb.append("    pageSize: ").append(toIndentedString(pageSize)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

