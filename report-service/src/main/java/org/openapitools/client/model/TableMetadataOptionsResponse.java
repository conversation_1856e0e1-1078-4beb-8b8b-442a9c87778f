/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * TableMetadataOptionsResponse
 */
@JsonPropertyOrder({
    TableMetadataOptionsResponse.JSON_PROPERTY_DEFERRABLE,
    TableMetadataOptionsResponse.JSON_PROPERTY_INITIALLY,
    TableMetadataOptionsResponse.JSON_PROPERTY_MATCH,
    TableMetadataOptionsResponse.JSON_PROPERTY_ONDELETE,
    TableMetadataOptionsResponse.JSON_PROPERTY_ONUPDATE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TableMetadataOptionsResponse {
    public static final String JSON_PROPERTY_DEFERRABLE = "deferrable";
    private Boolean deferrable;

    public static final String JSON_PROPERTY_INITIALLY = "initially";
    private Boolean initially;

    public static final String JSON_PROPERTY_MATCH = "match";
    private Boolean match;

    public static final String JSON_PROPERTY_ONDELETE = "ondelete";
    private Boolean ondelete;

    public static final String JSON_PROPERTY_ONUPDATE = "onupdate";
    private Boolean onupdate;

    public TableMetadataOptionsResponse() {
    }

    public TableMetadataOptionsResponse deferrable(Boolean deferrable) {

        this.deferrable = deferrable;
        return this;
    }

    /**
     * Get deferrable
     *
     * @return deferrable
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DEFERRABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getDeferrable() {
        return deferrable;
    }


    @JsonProperty(JSON_PROPERTY_DEFERRABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDeferrable(Boolean deferrable) {
        this.deferrable = deferrable;
    }


    public TableMetadataOptionsResponse initially(Boolean initially) {

        this.initially = initially;
        return this;
    }

    /**
     * Get initially
     *
     * @return initially
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_INITIALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getInitially() {
        return initially;
    }


    @JsonProperty(JSON_PROPERTY_INITIALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setInitially(Boolean initially) {
        this.initially = initially;
    }


    public TableMetadataOptionsResponse match(Boolean match) {

        this.match = match;
        return this;
    }

    /**
     * Get match
     *
     * @return match
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MATCH)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getMatch() {
        return match;
    }


    @JsonProperty(JSON_PROPERTY_MATCH)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMatch(Boolean match) {
        this.match = match;
    }


    public TableMetadataOptionsResponse ondelete(Boolean ondelete) {

        this.ondelete = ondelete;
        return this;
    }

    /**
     * Get ondelete
     *
     * @return ondelete
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ONDELETE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getOndelete() {
        return ondelete;
    }


    @JsonProperty(JSON_PROPERTY_ONDELETE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOndelete(Boolean ondelete) {
        this.ondelete = ondelete;
    }


    public TableMetadataOptionsResponse onupdate(Boolean onupdate) {

        this.onupdate = onupdate;
        return this;
    }

    /**
     * Get onupdate
     *
     * @return onupdate
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ONUPDATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getOnupdate() {
        return onupdate;
    }


    @JsonProperty(JSON_PROPERTY_ONUPDATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOnupdate(Boolean onupdate) {
        this.onupdate = onupdate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TableMetadataOptionsResponse tableMetadataOptionsResponse = (TableMetadataOptionsResponse) o;
        return Objects.equals(this.deferrable, tableMetadataOptionsResponse.deferrable) &&
            Objects.equals(this.initially, tableMetadataOptionsResponse.initially) &&
            Objects.equals(this.match, tableMetadataOptionsResponse.match) &&
            Objects.equals(this.ondelete, tableMetadataOptionsResponse.ondelete) &&
            Objects.equals(this.onupdate, tableMetadataOptionsResponse.onupdate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(deferrable, initially, match, ondelete, onupdate);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TableMetadataOptionsResponse {\n");
        sb.append("    deferrable: ").append(toIndentedString(deferrable)).append("\n");
        sb.append("    initially: ").append(toIndentedString(initially)).append("\n");
        sb.append("    match: ").append(toIndentedString(match)).append("\n");
        sb.append("    ondelete: ").append(toIndentedString(ondelete)).append("\n");
        sb.append("    onupdate: ").append(toIndentedString(onupdate)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

