/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartCacheWarmUpRequestSchema
 */
@JsonPropertyOrder({
    ChartCacheWarmUpRequestSchema.JSON_PROPERTY_CHART_ID,
    ChartCacheWarmUpRequestSchema.JSON_PROPERTY_DASHBOARD_ID,
    ChartCacheWarmUpRequestSchema.JSON_PROPERTY_EXTRA_FILTERS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartCacheWarmUpRequestSchema {
    public static final String JSON_PROPERTY_CHART_ID = "chart_id";
    private Integer chartId;

    public static final String JSON_PROPERTY_DASHBOARD_ID = "dashboard_id";
    private Integer dashboardId;

    public static final String JSON_PROPERTY_EXTRA_FILTERS = "extra_filters";
    private String extraFilters;

    public ChartCacheWarmUpRequestSchema() {
    }

    public ChartCacheWarmUpRequestSchema chartId(Integer chartId) {

        this.chartId = chartId;
        return this;
    }

    /**
     * The ID of the chart to warm up cache for
     *
     * @return chartId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_CHART_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getChartId() {
        return chartId;
    }


    @JsonProperty(JSON_PROPERTY_CHART_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setChartId(Integer chartId) {
        this.chartId = chartId;
    }


    public ChartCacheWarmUpRequestSchema dashboardId(Integer dashboardId) {

        this.dashboardId = dashboardId;
        return this;
    }

    /**
     * The ID of the dashboard to get filters for when warming cache
     *
     * @return dashboardId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDashboardId() {
        return dashboardId;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardId(Integer dashboardId) {
        this.dashboardId = dashboardId;
    }


    public ChartCacheWarmUpRequestSchema extraFilters(String extraFilters) {

        this.extraFilters = extraFilters;
        return this;
    }

    /**
     * Extra filters to apply when warming up cache
     *
     * @return extraFilters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtraFilters() {
        return extraFilters;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtraFilters(String extraFilters) {
        this.extraFilters = extraFilters;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartCacheWarmUpRequestSchema chartCacheWarmUpRequestSchema = (ChartCacheWarmUpRequestSchema) o;
        return Objects.equals(this.chartId, chartCacheWarmUpRequestSchema.chartId) &&
            Objects.equals(this.dashboardId, chartCacheWarmUpRequestSchema.dashboardId) &&
            Objects.equals(this.extraFilters, chartCacheWarmUpRequestSchema.extraFilters);
    }

    @Override
    public int hashCode() {
        return Objects.hash(chartId, dashboardId, extraFilters);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartCacheWarmUpRequestSchema {\n");
        sb.append("    chartId: ").append(toIndentedString(chartId)).append("\n");
        sb.append("    dashboardId: ").append(toIndentedString(dashboardId)).append("\n");
        sb.append("    extraFilters: ").append(toIndentedString(extraFilters)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

