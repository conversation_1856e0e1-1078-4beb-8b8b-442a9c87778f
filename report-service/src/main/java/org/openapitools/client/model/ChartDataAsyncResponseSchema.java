/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartDataAsyncResponseSchema
 */
@JsonPropertyOrder({
    ChartDataAsyncResponseSchema.JSON_PROPERTY_CHANNEL_ID,
    ChartDataAsyncResponseSchema.JSON_PROPERTY_JOB_ID,
    ChartDataAsyncResponseSchema.JSON_PROPERTY_RESULT_URL,
    ChartDataAsyncResponseSchema.JSON_PROPERTY_STATUS,
    ChartDataAsyncResponseSchema.JSON_PROPERTY_USER_ID
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataAsyncResponseSchema {
    public static final String JSON_PROPERTY_CHANNEL_ID = "channel_id";
    private String channelId;

    public static final String JSON_PROPERTY_JOB_ID = "job_id";
    private String jobId;

    public static final String JSON_PROPERTY_RESULT_URL = "result_url";
    private String resultUrl;

    public static final String JSON_PROPERTY_STATUS = "status";
    private String status;

    public static final String JSON_PROPERTY_USER_ID = "user_id";
    private String userId;

    public ChartDataAsyncResponseSchema() {
    }

    public ChartDataAsyncResponseSchema channelId(String channelId) {

        this.channelId = channelId;
        return this;
    }

    /**
     * Unique session async channel ID
     *
     * @return channelId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANNEL_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getChannelId() {
        return channelId;
    }


    @JsonProperty(JSON_PROPERTY_CHANNEL_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }


    public ChartDataAsyncResponseSchema jobId(String jobId) {

        this.jobId = jobId;
        return this;
    }

    /**
     * Unique async job ID
     *
     * @return jobId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JOB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJobId() {
        return jobId;
    }


    @JsonProperty(JSON_PROPERTY_JOB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJobId(String jobId) {
        this.jobId = jobId;
    }


    public ChartDataAsyncResponseSchema resultUrl(String resultUrl) {

        this.resultUrl = resultUrl;
        return this;
    }

    /**
     * Unique result URL for fetching async query data
     *
     * @return resultUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getResultUrl() {
        return resultUrl;
    }


    @JsonProperty(JSON_PROPERTY_RESULT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResultUrl(String resultUrl) {
        this.resultUrl = resultUrl;
    }


    public ChartDataAsyncResponseSchema status(String status) {

        this.status = status;
        return this;
    }

    /**
     * Status value for async job
     *
     * @return status
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getStatus() {
        return status;
    }


    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStatus(String status) {
        this.status = status;
    }


    public ChartDataAsyncResponseSchema userId(String userId) {

        this.userId = userId;
        return this;
    }

    /**
     * Requesting user ID
     *
     * @return userId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUserId() {
        return userId;
    }


    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUserId(String userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataAsyncResponseSchema chartDataAsyncResponseSchema = (ChartDataAsyncResponseSchema) o;
        return Objects.equals(this.channelId, chartDataAsyncResponseSchema.channelId) &&
            Objects.equals(this.jobId, chartDataAsyncResponseSchema.jobId) &&
            Objects.equals(this.resultUrl, chartDataAsyncResponseSchema.resultUrl) &&
            Objects.equals(this.status, chartDataAsyncResponseSchema.status) &&
            Objects.equals(this.userId, chartDataAsyncResponseSchema.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(channelId, jobId, resultUrl, status, userId);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataAsyncResponseSchema {\n");
        sb.append("    channelId: ").append(toIndentedString(channelId)).append("\n");
        sb.append("    jobId: ").append(toIndentedString(jobId)).append("\n");
        sb.append("    resultUrl: ").append(toIndentedString(resultUrl)).append("\n");
        sb.append("    status: ").append(toIndentedString(status)).append("\n");
        sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

