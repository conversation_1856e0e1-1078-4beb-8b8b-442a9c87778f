/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * DatasetRelatedChart
 */
@JsonPropertyOrder({
    DatasetRelatedChart.JSON_PROPERTY_ID,
    DatasetRelatedChart.JSON_PROPERTY_SLICE_NAME,
    DatasetRelatedChart.JSON_PROPERTY_VIZ_TYPE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetRelatedChart {
    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_SLICE_NAME = "slice_name";
    private String sliceName;

    public static final String JSON_PROPERTY_VIZ_TYPE = "viz_type";
    private String vizType;

    public DatasetRelatedChart() {
    }

    public DatasetRelatedChart id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DatasetRelatedChart sliceName(String sliceName) {

        this.sliceName = sliceName;
        return this;
    }

    /**
     * Get sliceName
     *
     * @return sliceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSliceName() {
        return sliceName;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceName(String sliceName) {
        this.sliceName = sliceName;
    }


    public DatasetRelatedChart vizType(String vizType) {

        this.vizType = vizType;
        return this;
    }

    /**
     * Get vizType
     *
     * @return vizType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVizType() {
        return vizType;
    }


    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVizType(String vizType) {
        this.vizType = vizType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetRelatedChart datasetRelatedChart = (DatasetRelatedChart) o;
        return Objects.equals(this.id, datasetRelatedChart.id) &&
            Objects.equals(this.sliceName, datasetRelatedChart.sliceName) &&
            Objects.equals(this.vizType, datasetRelatedChart.vizType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, sliceName, vizType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetRelatedChart {\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    sliceName: ").append(toIndentedString(sliceName)).append("\n");
        sb.append("    vizType: ").append(toIndentedString(vizType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

