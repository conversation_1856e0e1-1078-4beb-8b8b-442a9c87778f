/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1AnnotationLayerInfoGet200ResponseFilters
 */
@JsonPropertyOrder({
    ApiV1AnnotationLayerInfoGet200ResponseFilters.JSON_PROPERTY_COLUMN_NAME
})
@JsonTypeName("_api_v1_annotation_layer__info_get_200_response_filters")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1AnnotationLayerInfoGet200ResponseFilters {
    public static final String JSON_PROPERTY_COLUMN_NAME = "column_name";
    private List<ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner> columnName;

    public ApiV1AnnotationLayerInfoGet200ResponseFilters() {
    }

    public ApiV1AnnotationLayerInfoGet200ResponseFilters columnName(List<ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner> columnName) {

        this.columnName = columnName;
        return this;
    }

    public ApiV1AnnotationLayerInfoGet200ResponseFilters addColumnNameItem(ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner columnNameItem) {
        if (this.columnName == null) {
            this.columnName = new ArrayList<>();
        }
        this.columnName.add(columnNameItem);
        return this;
    }

    /**
     * Get columnName
     *
     * @return columnName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner> getColumnName() {
        return columnName;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnName(List<ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner> columnName) {
        this.columnName = columnName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1AnnotationLayerInfoGet200ResponseFilters apiV1AnnotationLayerInfoGet200ResponseFilters = (ApiV1AnnotationLayerInfoGet200ResponseFilters) o;
        return Objects.equals(this.columnName, apiV1AnnotationLayerInfoGet200ResponseFilters.columnName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columnName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1AnnotationLayerInfoGet200ResponseFilters {\n");
        sb.append("    columnName: ").append(toIndentedString(columnName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

