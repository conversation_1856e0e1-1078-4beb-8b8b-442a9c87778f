/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * QueryRestApiGetList
 */
@JsonPropertyOrder({
    QueryRestApiGetList.JSON_PROPERTY_CHANGED_ON,
    QueryRestApiGetList.JSO<PERSON>_PROPERTY_DATABASE,
    QueryRestApiGetList.JSON_PROPERTY_END_TIME,
    QueryRestApiGetList.JSON_PROPERTY_EXECUTED_SQL,
    QueryRestApiGetList.JSON_PROPERTY_ID,
    QueryRestApiGetList.JSON_PROPERTY_ROWS,
    QueryRestApiGetList.JSO<PERSON>_PROPERTY_SCHEMA,
    QueryRestApiGetList.JSON_PROPERTY_SQL,
    QueryRestApiGetList.JSON_PROPERTY_SQL_TABLES,
    QueryRestApiGetList.JSON_PROPERTY_START_TIME,
    QueryRestApiGetList.JSON_PROPERTY_STATUS,
    QueryRestApiGetList.JSON_PROPERTY_TAB_NAME,
    QueryRestApiGetList.JSON_PROPERTY_TMP_TABLE_NAME,
    QueryRestApiGetList.JSON_PROPERTY_TRACKING_URL,
    QueryRestApiGetList.JSON_PROPERTY_USER
})
@JsonTypeName("QueryRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class QueryRestApiGetList {
    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private Database1 database;

    public static final String JSON_PROPERTY_END_TIME = "end_time";
    private BigDecimal endTime;

    public static final String JSON_PROPERTY_EXECUTED_SQL = "executed_sql";
    private String executedSql;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_ROWS = "rows";
    private Integer rows;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_SQL_TABLES = "sql_tables";
    private Object sqlTables = null;

    public static final String JSON_PROPERTY_START_TIME = "start_time";
    private BigDecimal startTime;

    public static final String JSON_PROPERTY_STATUS = "status";
    private String status;

    public static final String JSON_PROPERTY_TAB_NAME = "tab_name";
    private String tabName;

    public static final String JSON_PROPERTY_TMP_TABLE_NAME = "tmp_table_name";
    private String tmpTableName;

    public static final String JSON_PROPERTY_TRACKING_URL = "tracking_url";
    private String trackingUrl;

    public static final String JSON_PROPERTY_USER = "user";
    private User user;

    public QueryRestApiGetList() {
    }

    @JsonCreator
    public QueryRestApiGetList(
        @JsonProperty(JSON_PROPERTY_SQL_TABLES) Object sqlTables
    ) {
        this();
        this.sqlTables = sqlTables;
    }

    public QueryRestApiGetList changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public QueryRestApiGetList database(Database1 database) {

        this.database = database;
        return this;
    }

    /**
     * Get database
     *
     * @return database
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Database1 getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabase(Database1 database) {
        this.database = database;
    }


    public QueryRestApiGetList endTime(BigDecimal endTime) {

        this.endTime = endTime;
        return this;
    }

    /**
     * Get endTime
     *
     * @return endTime
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_END_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getEndTime() {
        return endTime;
    }


    @JsonProperty(JSON_PROPERTY_END_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEndTime(BigDecimal endTime) {
        this.endTime = endTime;
    }


    public QueryRestApiGetList executedSql(String executedSql) {

        this.executedSql = executedSql;
        return this;
    }

    /**
     * Get executedSql
     *
     * @return executedSql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXECUTED_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExecutedSql() {
        return executedSql;
    }


    @JsonProperty(JSON_PROPERTY_EXECUTED_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExecutedSql(String executedSql) {
        this.executedSql = executedSql;
    }


    public QueryRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public QueryRestApiGetList rows(Integer rows) {

        this.rows = rows;
        return this;
    }

    /**
     * Get rows
     *
     * @return rows
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROWS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getRows() {
        return rows;
    }


    @JsonProperty(JSON_PROPERTY_ROWS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRows(Integer rows) {
        this.rows = rows;
    }


    public QueryRestApiGetList schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public QueryRestApiGetList sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    /**
     * Get sqlTables
     *
     * @return sqlTables
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL_TABLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getSqlTables() {
        return sqlTables;
    }


    public QueryRestApiGetList startTime(BigDecimal startTime) {

        this.startTime = startTime;
        return this;
    }

    /**
     * Get startTime
     *
     * @return startTime
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_START_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getStartTime() {
        return startTime;
    }


    @JsonProperty(JSON_PROPERTY_START_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStartTime(BigDecimal startTime) {
        this.startTime = startTime;
    }


    public QueryRestApiGetList status(String status) {

        this.status = status;
        return this;
    }

    /**
     * Get status
     *
     * @return status
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getStatus() {
        return status;
    }


    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStatus(String status) {
        this.status = status;
    }


    public QueryRestApiGetList tabName(String tabName) {

        this.tabName = tabName;
        return this;
    }

    /**
     * Get tabName
     *
     * @return tabName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAB_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTabName() {
        return tabName;
    }


    @JsonProperty(JSON_PROPERTY_TAB_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTabName(String tabName) {
        this.tabName = tabName;
    }


    public QueryRestApiGetList tmpTableName(String tmpTableName) {

        this.tmpTableName = tmpTableName;
        return this;
    }

    /**
     * Get tmpTableName
     *
     * @return tmpTableName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TMP_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTmpTableName() {
        return tmpTableName;
    }


    @JsonProperty(JSON_PROPERTY_TMP_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTmpTableName(String tmpTableName) {
        this.tmpTableName = tmpTableName;
    }


    public QueryRestApiGetList trackingUrl(String trackingUrl) {

        this.trackingUrl = trackingUrl;
        return this;
    }

    /**
     * Get trackingUrl
     *
     * @return trackingUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TRACKING_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTrackingUrl() {
        return trackingUrl;
    }


    @JsonProperty(JSON_PROPERTY_TRACKING_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTrackingUrl(String trackingUrl) {
        this.trackingUrl = trackingUrl;
    }


    public QueryRestApiGetList user(User user) {

        this.user = user;
        return this;
    }

    /**
     * Get user
     *
     * @return user
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public User getUser() {
        return user;
    }


    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUser(User user) {
        this.user = user;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        QueryRestApiGetList queryRestApiGetList = (QueryRestApiGetList) o;
        return Objects.equals(this.changedOn, queryRestApiGetList.changedOn) &&
            Objects.equals(this.database, queryRestApiGetList.database) &&
            Objects.equals(this.endTime, queryRestApiGetList.endTime) &&
            Objects.equals(this.executedSql, queryRestApiGetList.executedSql) &&
            Objects.equals(this.id, queryRestApiGetList.id) &&
            Objects.equals(this.rows, queryRestApiGetList.rows) &&
            Objects.equals(this.schema, queryRestApiGetList.schema) &&
            Objects.equals(this.sql, queryRestApiGetList.sql) &&
            Objects.equals(this.sqlTables, queryRestApiGetList.sqlTables) &&
            Objects.equals(this.startTime, queryRestApiGetList.startTime) &&
            Objects.equals(this.status, queryRestApiGetList.status) &&
            Objects.equals(this.tabName, queryRestApiGetList.tabName) &&
            Objects.equals(this.tmpTableName, queryRestApiGetList.tmpTableName) &&
            Objects.equals(this.trackingUrl, queryRestApiGetList.trackingUrl) &&
            Objects.equals(this.user, queryRestApiGetList.user);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedOn, database, endTime, executedSql, id, rows, schema, sql, sqlTables, startTime, status, tabName, tmpTableName, trackingUrl, user);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class QueryRestApiGetList {\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    endTime: ").append(toIndentedString(endTime)).append("\n");
        sb.append("    executedSql: ").append(toIndentedString(executedSql)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    rows: ").append(toIndentedString(rows)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    sqlTables: ").append(toIndentedString(sqlTables)).append("\n");
        sb.append("    startTime: ").append(toIndentedString(startTime)).append("\n");
        sb.append("    status: ").append(toIndentedString(status)).append("\n");
        sb.append("    tabName: ").append(toIndentedString(tabName)).append("\n");
        sb.append("    tmpTableName: ").append(toIndentedString(tmpTableName)).append("\n");
        sb.append("    trackingUrl: ").append(toIndentedString(trackingUrl)).append("\n");
        sb.append("    user: ").append(toIndentedString(user)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

