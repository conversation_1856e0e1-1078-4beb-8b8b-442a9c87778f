/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * AnnotationRestApiGetList
 */
@JsonPropertyOrder({
    AnnotationRestApiGetList.JSON_PROPERTY_CHANGED_BY,
    AnnotationRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    AnnotationRestApiGetList.JSON_PROPERTY_CREATED_BY,
    AnnotationRestApiGetList.JSON_PROPERTY_END_DTTM,
    AnnotationRestApiGetList.JSON_PROPERTY_ID,
    AnnotationRestApiGetList.JSON_PROPERTY_LONG_DESCR,
    AnnotationRestApiGetList.JSO<PERSON>_PROPERTY_SHORT_DESCR,
    AnnotationRestApiGetList.JSON_PROPERTY_START_DTTM
})
@JsonTypeName("AnnotationRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class AnnotationRestApiGetList {
    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private AnnotationRestApiGetListUser1 changedBy;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private AnnotationRestApiGetListUser createdBy;

    public static final String JSON_PROPERTY_END_DTTM = "end_dttm";
    private OffsetDateTime endDttm;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_LONG_DESCR = "long_descr";
    private String longDescr;

    public static final String JSON_PROPERTY_SHORT_DESCR = "short_descr";
    private String shortDescr;

    public static final String JSON_PROPERTY_START_DTTM = "start_dttm";
    private OffsetDateTime startDttm;

    public AnnotationRestApiGetList() {
    }

    @JsonCreator
    public AnnotationRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
    }

    public AnnotationRestApiGetList changedBy(AnnotationRestApiGetListUser1 changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public AnnotationRestApiGetListUser1 getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(AnnotationRestApiGetListUser1 changedBy) {
        this.changedBy = changedBy;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public AnnotationRestApiGetList createdBy(AnnotationRestApiGetListUser createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public AnnotationRestApiGetListUser getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(AnnotationRestApiGetListUser createdBy) {
        this.createdBy = createdBy;
    }


    public AnnotationRestApiGetList endDttm(OffsetDateTime endDttm) {

        this.endDttm = endDttm;
        return this;
    }

    /**
     * Get endDttm
     *
     * @return endDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getEndDttm() {
        return endDttm;
    }


    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEndDttm(OffsetDateTime endDttm) {
        this.endDttm = endDttm;
    }


    public AnnotationRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public AnnotationRestApiGetList longDescr(String longDescr) {

        this.longDescr = longDescr;
        return this;
    }

    /**
     * Get longDescr
     *
     * @return longDescr
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LONG_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLongDescr() {
        return longDescr;
    }


    @JsonProperty(JSON_PROPERTY_LONG_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLongDescr(String longDescr) {
        this.longDescr = longDescr;
    }


    public AnnotationRestApiGetList shortDescr(String shortDescr) {

        this.shortDescr = shortDescr;
        return this;
    }

    /**
     * Get shortDescr
     *
     * @return shortDescr
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SHORT_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getShortDescr() {
        return shortDescr;
    }


    @JsonProperty(JSON_PROPERTY_SHORT_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setShortDescr(String shortDescr) {
        this.shortDescr = shortDescr;
    }


    public AnnotationRestApiGetList startDttm(OffsetDateTime startDttm) {

        this.startDttm = startDttm;
        return this;
    }

    /**
     * Get startDttm
     *
     * @return startDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getStartDttm() {
        return startDttm;
    }


    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStartDttm(OffsetDateTime startDttm) {
        this.startDttm = startDttm;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AnnotationRestApiGetList annotationRestApiGetList = (AnnotationRestApiGetList) o;
        return Objects.equals(this.changedBy, annotationRestApiGetList.changedBy) &&
            Objects.equals(this.changedOnDeltaHumanized, annotationRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.createdBy, annotationRestApiGetList.createdBy) &&
            Objects.equals(this.endDttm, annotationRestApiGetList.endDttm) &&
            Objects.equals(this.id, annotationRestApiGetList.id) &&
            Objects.equals(this.longDescr, annotationRestApiGetList.longDescr) &&
            Objects.equals(this.shortDescr, annotationRestApiGetList.shortDescr) &&
            Objects.equals(this.startDttm, annotationRestApiGetList.startDttm);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedBy, changedOnDeltaHumanized, createdBy, endDttm, id, longDescr, shortDescr, startDttm);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class AnnotationRestApiGetList {\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    endDttm: ").append(toIndentedString(endDttm)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    longDescr: ").append(toIndentedString(longDescr)).append("\n");
        sb.append("    shortDescr: ").append(toIndentedString(shortDescr)).append("\n");
        sb.append("    startDttm: ").append(toIndentedString(startDttm)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

