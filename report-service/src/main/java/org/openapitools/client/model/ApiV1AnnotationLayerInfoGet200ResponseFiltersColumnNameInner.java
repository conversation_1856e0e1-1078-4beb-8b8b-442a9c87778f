/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner
 */
@JsonPropertyOrder({
    ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner.JSON_PROPERTY_NAME,
    ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner.JSON_PROPERTY_OPERATOR
})
@JsonTypeName("_api_v1_annotation_layer__info_get_200_response_filters_column_name_inner")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner {
    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OPERATOR = "operator";
    private String operator;

    public ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner() {
    }

    public ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner name(String name) {

        this.name = name;
        return this;
    }

    /**
     * The filter name. Will be translated by babel
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner operator(String operator) {

        this.operator = operator;
        return this;
    }

    /**
     * The filter operation key to use on list filters
     *
     * @return operator
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OPERATOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getOperator() {
        return operator;
    }


    @JsonProperty(JSON_PROPERTY_OPERATOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOperator(String operator) {
        this.operator = operator;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner apiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner = (ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner) o;
        return Objects.equals(this.name, apiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner.name) &&
            Objects.equals(this.operator, apiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner.operator);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, operator);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1AnnotationLayerInfoGet200ResponseFiltersColumnNameInner {\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    operator: ").append(toIndentedString(operator)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

