/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * DatasetDuplicateSchema
 */
@JsonPropertyOrder({
    DatasetDuplicateSchema.JSON_PROPERTY_BASE_MODEL_ID,
    DatasetDuplicateSchema.JSON_PROPERTY_TABLE_NAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetDuplicateSchema {
    public static final String JSON_PROPERTY_BASE_MODEL_ID = "base_model_id";
    private Integer baseModelId;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public DatasetDuplicateSchema() {
    }

    public DatasetDuplicateSchema baseModelId(Integer baseModelId) {

        this.baseModelId = baseModelId;
        return this;
    }

    /**
     * Get baseModelId
     *
     * @return baseModelId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_BASE_MODEL_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getBaseModelId() {
        return baseModelId;
    }


    @JsonProperty(JSON_PROPERTY_BASE_MODEL_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setBaseModelId(Integer baseModelId) {
        this.baseModelId = baseModelId;
    }


    public DatasetDuplicateSchema tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * Get tableName
     *
     * @return tableName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetDuplicateSchema datasetDuplicateSchema = (DatasetDuplicateSchema) o;
        return Objects.equals(this.baseModelId, datasetDuplicateSchema.baseModelId) &&
            Objects.equals(this.tableName, datasetDuplicateSchema.tableName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(baseModelId, tableName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetDuplicateSchema {\n");
        sb.append("    baseModelId: ").append(toIndentedString(baseModelId)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

