/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;
import java.util.UUID;

/**
 * DatasetColumnsPut
 */
@JsonPropertyOrder({
    DatasetColumnsPut.JSON_PROPERTY_ADVANCED_DATA_TYPE,
    DatasetColumnsPut.JSON_PROPERTY_COLUMN_NAME,
    DatasetColumnsPut.JSON_PROPERTY_DESCRIPTION,
    DatasetColumnsPut.JSON_PROPERTY_EXPRESSION,
    DatasetColumnsPut.JSON_PROPERTY_EXTRA,
    DatasetColumnsPut.JSON_PROPERTY_FILTERABLE,
    DatasetColumnsPut.JSON_PROPERTY_GROUPBY,
    DatasetColumnsPut.JSON_PROPERTY_ID,
    DatasetColumnsPut.JSON_PROPERTY_IS_ACTIVE,
    DatasetColumnsPut.JSON_PROPERTY_IS_DTTM,
    DatasetColumnsPut.JSON_PROPERTY_PYTHON_DATE_FORMAT,
    DatasetColumnsPut.JSON_PROPERTY_TYPE,
    DatasetColumnsPut.JSON_PROPERTY_UUID,
    DatasetColumnsPut.JSON_PROPERTY_VERBOSE_NAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetColumnsPut {
    public static final String JSON_PROPERTY_ADVANCED_DATA_TYPE = "advanced_data_type";
    private String advancedDataType;

    public static final String JSON_PROPERTY_COLUMN_NAME = "column_name";
    private String columnName;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXPRESSION = "expression";
    private String expression;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private String extra;

    public static final String JSON_PROPERTY_FILTERABLE = "filterable";
    private Boolean filterable;

    public static final String JSON_PROPERTY_GROUPBY = "groupby";
    private Boolean groupby;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_ACTIVE = "is_active";
    private Boolean isActive;

    public static final String JSON_PROPERTY_IS_DTTM = "is_dttm";
    private Boolean isDttm;

    public static final String JSON_PROPERTY_PYTHON_DATE_FORMAT = "python_date_format";
    private String pythonDateFormat;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public static final String JSON_PROPERTY_UUID = "uuid";
    private UUID uuid;

    public static final String JSON_PROPERTY_VERBOSE_NAME = "verbose_name";
    private String verboseName;

    public DatasetColumnsPut() {
    }

    public DatasetColumnsPut advancedDataType(String advancedDataType) {

        this.advancedDataType = advancedDataType;
        return this;
    }

    /**
     * Get advancedDataType
     *
     * @return advancedDataType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ADVANCED_DATA_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getAdvancedDataType() {
        return advancedDataType;
    }


    @JsonProperty(JSON_PROPERTY_ADVANCED_DATA_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAdvancedDataType(String advancedDataType) {
        this.advancedDataType = advancedDataType;
    }


    public DatasetColumnsPut columnName(String columnName) {

        this.columnName = columnName;
        return this;
    }

    /**
     * Get columnName
     *
     * @return columnName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getColumnName() {
        return columnName;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }


    public DatasetColumnsPut description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public DatasetColumnsPut expression(String expression) {

        this.expression = expression;
        return this;
    }

    /**
     * Get expression
     *
     * @return expression
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPRESSION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExpression() {
        return expression;
    }


    @JsonProperty(JSON_PROPERTY_EXPRESSION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExpression(String expression) {
        this.expression = expression;
    }


    public DatasetColumnsPut extra(String extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(String extra) {
        this.extra = extra;
    }


    public DatasetColumnsPut filterable(Boolean filterable) {

        this.filterable = filterable;
        return this;
    }

    /**
     * Get filterable
     *
     * @return filterable
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTERABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getFilterable() {
        return filterable;
    }


    @JsonProperty(JSON_PROPERTY_FILTERABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterable(Boolean filterable) {
        this.filterable = filterable;
    }


    public DatasetColumnsPut groupby(Boolean groupby) {

        this.groupby = groupby;
        return this;
    }

    /**
     * Get groupby
     *
     * @return groupby
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GROUPBY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getGroupby() {
        return groupby;
    }


    @JsonProperty(JSON_PROPERTY_GROUPBY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGroupby(Boolean groupby) {
        this.groupby = groupby;
    }


    public DatasetColumnsPut id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DatasetColumnsPut isActive(Boolean isActive) {

        this.isActive = isActive;
        return this;
    }

    /**
     * Get isActive
     *
     * @return isActive
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsActive() {
        return isActive;
    }


    @JsonProperty(JSON_PROPERTY_IS_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }


    public DatasetColumnsPut isDttm(Boolean isDttm) {

        this.isDttm = isDttm;
        return this;
    }

    /**
     * Get isDttm
     *
     * @return isDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsDttm() {
        return isDttm;
    }


    @JsonProperty(JSON_PROPERTY_IS_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsDttm(Boolean isDttm) {
        this.isDttm = isDttm;
    }


    public DatasetColumnsPut pythonDateFormat(String pythonDateFormat) {

        this.pythonDateFormat = pythonDateFormat;
        return this;
    }

    /**
     * Get pythonDateFormat
     *
     * @return pythonDateFormat
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PYTHON_DATE_FORMAT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPythonDateFormat() {
        return pythonDateFormat;
    }


    @JsonProperty(JSON_PROPERTY_PYTHON_DATE_FORMAT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPythonDateFormat(String pythonDateFormat) {
        this.pythonDateFormat = pythonDateFormat;
    }


    public DatasetColumnsPut type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Get type
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(String type) {
        this.type = type;
    }


    public DatasetColumnsPut uuid(UUID uuid) {

        this.uuid = uuid;
        return this;
    }

    /**
     * Get uuid
     *
     * @return uuid
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public UUID getUuid() {
        return uuid;
    }


    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUuid(UUID uuid) {
        this.uuid = uuid;
    }


    public DatasetColumnsPut verboseName(String verboseName) {

        this.verboseName = verboseName;
        return this;
    }

    /**
     * Get verboseName
     *
     * @return verboseName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VERBOSE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVerboseName() {
        return verboseName;
    }


    @JsonProperty(JSON_PROPERTY_VERBOSE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVerboseName(String verboseName) {
        this.verboseName = verboseName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetColumnsPut datasetColumnsPut = (DatasetColumnsPut) o;
        return Objects.equals(this.advancedDataType, datasetColumnsPut.advancedDataType) &&
            Objects.equals(this.columnName, datasetColumnsPut.columnName) &&
            Objects.equals(this.description, datasetColumnsPut.description) &&
            Objects.equals(this.expression, datasetColumnsPut.expression) &&
            Objects.equals(this.extra, datasetColumnsPut.extra) &&
            Objects.equals(this.filterable, datasetColumnsPut.filterable) &&
            Objects.equals(this.groupby, datasetColumnsPut.groupby) &&
            Objects.equals(this.id, datasetColumnsPut.id) &&
            Objects.equals(this.isActive, datasetColumnsPut.isActive) &&
            Objects.equals(this.isDttm, datasetColumnsPut.isDttm) &&
            Objects.equals(this.pythonDateFormat, datasetColumnsPut.pythonDateFormat) &&
            Objects.equals(this.type, datasetColumnsPut.type) &&
            Objects.equals(this.uuid, datasetColumnsPut.uuid) &&
            Objects.equals(this.verboseName, datasetColumnsPut.verboseName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(advancedDataType, columnName, description, expression, extra, filterable, groupby, id, isActive, isDttm, pythonDateFormat, type, uuid, verboseName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetColumnsPut {\n");
        sb.append("    advancedDataType: ").append(toIndentedString(advancedDataType)).append("\n");
        sb.append("    columnName: ").append(toIndentedString(columnName)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    expression: ").append(toIndentedString(expression)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    filterable: ").append(toIndentedString(filterable)).append("\n");
        sb.append("    groupby: ").append(toIndentedString(groupby)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isActive: ").append(toIndentedString(isActive)).append("\n");
        sb.append("    isDttm: ").append(toIndentedString(isDttm)).append("\n");
        sb.append("    pythonDateFormat: ").append(toIndentedString(pythonDateFormat)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
        sb.append("    verboseName: ").append(toIndentedString(verboseName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

