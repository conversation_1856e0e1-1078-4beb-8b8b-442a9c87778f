/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * DashboardPermalinkStateSchema
 */
@JsonPropertyOrder({
    DashboardPermalinkStateSchema.JSON_PROPERTY_ACTIVE_TABS,
    DashboardPermalinkStateSchema.JSON_PROPERTY_ANCHOR,
    DashboardPermalinkStateSchema.JSON_PROPERTY_DATA_MASK,
    DashboardPermalinkStateSchema.JSON_PROPERTY_URL_PARAMS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DashboardPermalinkStateSchema {
    public static final String JSON_PROPERTY_ACTIVE_TABS = "activeTabs";
    private List<String> activeTabs;

    public static final String JSON_PROPERTY_ANCHOR = "anchor";
    private String anchor;

    public static final String JSON_PROPERTY_DATA_MASK = "dataMask";
    private Object dataMask;

    public static final String JSON_PROPERTY_URL_PARAMS = "urlParams";
    private List<Object> urlParams;

    public DashboardPermalinkStateSchema() {
    }

    public DashboardPermalinkStateSchema activeTabs(List<String> activeTabs) {

        this.activeTabs = activeTabs;
        return this;
    }

    public DashboardPermalinkStateSchema addActiveTabsItem(String activeTabsItem) {
        if (this.activeTabs == null) {
            this.activeTabs = new ArrayList<>();
        }
        this.activeTabs.add(activeTabsItem);
        return this;
    }

    /**
     * Current active dashboard tabs
     *
     * @return activeTabs
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTIVE_TABS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getActiveTabs() {
        return activeTabs;
    }


    @JsonProperty(JSON_PROPERTY_ACTIVE_TABS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setActiveTabs(List<String> activeTabs) {
        this.activeTabs = activeTabs;
    }


    public DashboardPermalinkStateSchema anchor(String anchor) {

        this.anchor = anchor;
        return this;
    }

    /**
     * Optional anchor link added to url hash
     *
     * @return anchor
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ANCHOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getAnchor() {
        return anchor;
    }


    @JsonProperty(JSON_PROPERTY_ANCHOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAnchor(String anchor) {
        this.anchor = anchor;
    }


    public DashboardPermalinkStateSchema dataMask(Object dataMask) {

        this.dataMask = dataMask;
        return this;
    }

    /**
     * Data mask used for native filter state
     *
     * @return dataMask
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATA_MASK)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDataMask() {
        return dataMask;
    }


    @JsonProperty(JSON_PROPERTY_DATA_MASK)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDataMask(Object dataMask) {
        this.dataMask = dataMask;
    }


    public DashboardPermalinkStateSchema urlParams(List<Object> urlParams) {

        this.urlParams = urlParams;
        return this;
    }

    public DashboardPermalinkStateSchema addUrlParamsItem(Object urlParamsItem) {
        if (this.urlParams == null) {
            this.urlParams = new ArrayList<>();
        }
        this.urlParams.add(urlParamsItem);
        return this;
    }

    /**
     * URL Parameters
     *
     * @return urlParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getUrlParams() {
        return urlParams;
    }


    @JsonProperty(JSON_PROPERTY_URL_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUrlParams(List<Object> urlParams) {
        this.urlParams = urlParams;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DashboardPermalinkStateSchema dashboardPermalinkStateSchema = (DashboardPermalinkStateSchema) o;
        return Objects.equals(this.activeTabs, dashboardPermalinkStateSchema.activeTabs) &&
            Objects.equals(this.anchor, dashboardPermalinkStateSchema.anchor) &&
            Objects.equals(this.dataMask, dashboardPermalinkStateSchema.dataMask) &&
            Objects.equals(this.urlParams, dashboardPermalinkStateSchema.urlParams);
    }

    @Override
    public int hashCode() {
        return Objects.hash(activeTabs, anchor, dataMask, urlParams);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DashboardPermalinkStateSchema {\n");
        sb.append("    activeTabs: ").append(toIndentedString(activeTabs)).append("\n");
        sb.append("    anchor: ").append(toIndentedString(anchor)).append("\n");
        sb.append("    dataMask: ").append(toIndentedString(dataMask)).append("\n");
        sb.append("    urlParams: ").append(toIndentedString(urlParams)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

