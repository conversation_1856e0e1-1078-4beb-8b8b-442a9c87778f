/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ExplorePermalinkStateSchema
 */
@JsonPropertyOrder({
    ExplorePermalinkStateSchema.JSON_PROPERTY_FORM_DATA,
    ExplorePermalinkStateSchema.JSON_PROPERTY_URL_PARAMS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ExplorePermalinkStateSchema {
    public static final String JSON_PROPERTY_FORM_DATA = "formData";
    private Object formData;

    public static final String JSON_PROPERTY_URL_PARAMS = "urlParams";
    private List<Object> urlParams;

    public ExplorePermalinkStateSchema() {
    }

    public ExplorePermalinkStateSchema formData(Object formData) {

        this.formData = formData;
        return this;
    }

    /**
     * Chart form data
     *
     * @return formData
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Object getFormData() {
        return formData;
    }


    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setFormData(Object formData) {
        this.formData = formData;
    }


    public ExplorePermalinkStateSchema urlParams(List<Object> urlParams) {

        this.urlParams = urlParams;
        return this;
    }

    public ExplorePermalinkStateSchema addUrlParamsItem(Object urlParamsItem) {
        if (this.urlParams == null) {
            this.urlParams = new ArrayList<>();
        }
        this.urlParams.add(urlParamsItem);
        return this;
    }

    /**
     * URL Parameters
     *
     * @return urlParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getUrlParams() {
        return urlParams;
    }


    @JsonProperty(JSON_PROPERTY_URL_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUrlParams(List<Object> urlParams) {
        this.urlParams = urlParams;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ExplorePermalinkStateSchema explorePermalinkStateSchema = (ExplorePermalinkStateSchema) o;
        return Objects.equals(this.formData, explorePermalinkStateSchema.formData) &&
            Objects.equals(this.urlParams, explorePermalinkStateSchema.urlParams);
    }

    @Override
    public int hashCode() {
        return Objects.hash(formData, urlParams);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ExplorePermalinkStateSchema {\n");
        sb.append("    formData: ").append(toIndentedString(formData)).append("\n");
        sb.append("    urlParams: ").append(toIndentedString(urlParams)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

