/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ChartDataPivotOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataPivotOptionsSchema.JSON_PROPERTY_AGGREGATES,
    ChartDataPivotOptionsSchema.JSON_PROPERTY_COLUMN_FILL_VALUE,
    ChartDataPivotOptionsSchema.JSON_PROPERTY_COLUMNS,
    ChartDataPivotOptionsSchema.JSON_PROPERTY_DROP_MISSING_COLUMNS,
    ChartDataPivotOptionsSchema.JSON_PROPERTY_MARGINAL_DISTRIBUTION_NAME,
    ChartDataPivotOptionsSchema.JSON_PROPERTY_MARGINAL_DISTRIBUTIONS,
    ChartDataPivotOptionsSchema.JSON_PROPERTY_METRIC_FILL_VALUE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataPivotOptionsSchema {
    public static final String JSON_PROPERTY_AGGREGATES = "aggregates";
    private Object aggregates;

    public static final String JSON_PROPERTY_COLUMN_FILL_VALUE = "column_fill_value";
    private String columnFillValue;

    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<String> columns;

    public static final String JSON_PROPERTY_DROP_MISSING_COLUMNS = "drop_missing_columns";
    private Boolean dropMissingColumns;

    public static final String JSON_PROPERTY_MARGINAL_DISTRIBUTION_NAME = "marginal_distribution_name";
    private String marginalDistributionName;

    public static final String JSON_PROPERTY_MARGINAL_DISTRIBUTIONS = "marginal_distributions";
    private Boolean marginalDistributions;

    public static final String JSON_PROPERTY_METRIC_FILL_VALUE = "metric_fill_value";
    private BigDecimal metricFillValue;

    public ChartDataPivotOptionsSchema() {
    }

    public ChartDataPivotOptionsSchema aggregates(Object aggregates) {

        this.aggregates = aggregates;
        return this;
    }

    /**
     * The keys are the name of the aggregate column to be created, and the values specify the details of how to apply the aggregation. If an operator requires additional options, these can be passed here to be unpacked in the operator call. The following numpy operators are supported: average, argmin, argmax, cumsum, cumprod, max, mean, median, nansum, nanmin, nanmax, nanmean, nanmedian, min, percentile, prod, product, std, sum, var. Any options required by the operator can be passed to the &#x60;options&#x60; object.  In the example, a new column &#x60;first_quantile&#x60; is created based on values in the column &#x60;my_col&#x60; using the &#x60;percentile&#x60; operator with the &#x60;q&#x3D;0.25&#x60; parameter.
     *
     * @return aggregates
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_AGGREGATES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getAggregates() {
        return aggregates;
    }


    @JsonProperty(JSON_PROPERTY_AGGREGATES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAggregates(Object aggregates) {
        this.aggregates = aggregates;
    }


    public ChartDataPivotOptionsSchema columnFillValue(String columnFillValue) {

        this.columnFillValue = columnFillValue;
        return this;
    }

    /**
     * Value to replace missing pivot columns names with.
     *
     * @return columnFillValue
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_FILL_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getColumnFillValue() {
        return columnFillValue;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_FILL_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnFillValue(String columnFillValue) {
        this.columnFillValue = columnFillValue;
    }


    public ChartDataPivotOptionsSchema columns(List<String> columns) {

        this.columns = columns;
        return this;
    }

    public ChartDataPivotOptionsSchema addColumnsItem(String columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * Columns to group by on the table columns
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<String> columns) {
        this.columns = columns;
    }


    public ChartDataPivotOptionsSchema dropMissingColumns(Boolean dropMissingColumns) {

        this.dropMissingColumns = dropMissingColumns;
        return this;
    }

    /**
     * Do not include columns whose entries are all missing (default: &#x60;true&#x60;).
     *
     * @return dropMissingColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DROP_MISSING_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getDropMissingColumns() {
        return dropMissingColumns;
    }


    @JsonProperty(JSON_PROPERTY_DROP_MISSING_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDropMissingColumns(Boolean dropMissingColumns) {
        this.dropMissingColumns = dropMissingColumns;
    }


    public ChartDataPivotOptionsSchema marginalDistributionName(String marginalDistributionName) {

        this.marginalDistributionName = marginalDistributionName;
        return this;
    }

    /**
     * Name of marginal distribution row/column. (default: &#x60;All&#x60;)
     *
     * @return marginalDistributionName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MARGINAL_DISTRIBUTION_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMarginalDistributionName() {
        return marginalDistributionName;
    }


    @JsonProperty(JSON_PROPERTY_MARGINAL_DISTRIBUTION_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMarginalDistributionName(String marginalDistributionName) {
        this.marginalDistributionName = marginalDistributionName;
    }


    public ChartDataPivotOptionsSchema marginalDistributions(Boolean marginalDistributions) {

        this.marginalDistributions = marginalDistributions;
        return this;
    }

    /**
     * Add totals for row/column. (default: &#x60;false&#x60;)
     *
     * @return marginalDistributions
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MARGINAL_DISTRIBUTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getMarginalDistributions() {
        return marginalDistributions;
    }


    @JsonProperty(JSON_PROPERTY_MARGINAL_DISTRIBUTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMarginalDistributions(Boolean marginalDistributions) {
        this.marginalDistributions = marginalDistributions;
    }


    public ChartDataPivotOptionsSchema metricFillValue(BigDecimal metricFillValue) {

        this.metricFillValue = metricFillValue;
        return this;
    }

    /**
     * Value to replace missing values with in aggregate calculations.
     *
     * @return metricFillValue
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METRIC_FILL_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getMetricFillValue() {
        return metricFillValue;
    }


    @JsonProperty(JSON_PROPERTY_METRIC_FILL_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMetricFillValue(BigDecimal metricFillValue) {
        this.metricFillValue = metricFillValue;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataPivotOptionsSchema chartDataPivotOptionsSchema = (ChartDataPivotOptionsSchema) o;
        return Objects.equals(this.aggregates, chartDataPivotOptionsSchema.aggregates) &&
            Objects.equals(this.columnFillValue, chartDataPivotOptionsSchema.columnFillValue) &&
            Objects.equals(this.columns, chartDataPivotOptionsSchema.columns) &&
            Objects.equals(this.dropMissingColumns, chartDataPivotOptionsSchema.dropMissingColumns) &&
            Objects.equals(this.marginalDistributionName, chartDataPivotOptionsSchema.marginalDistributionName) &&
            Objects.equals(this.marginalDistributions, chartDataPivotOptionsSchema.marginalDistributions) &&
            Objects.equals(this.metricFillValue, chartDataPivotOptionsSchema.metricFillValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(aggregates, columnFillValue, columns, dropMissingColumns, marginalDistributionName, marginalDistributions, metricFillValue);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataPivotOptionsSchema {\n");
        sb.append("    aggregates: ").append(toIndentedString(aggregates)).append("\n");
        sb.append("    columnFillValue: ").append(toIndentedString(columnFillValue)).append("\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    dropMissingColumns: ").append(toIndentedString(dropMissingColumns)).append("\n");
        sb.append("    marginalDistributionName: ").append(toIndentedString(marginalDistributionName)).append("\n");
        sb.append("    marginalDistributions: ").append(toIndentedString(marginalDistributions)).append("\n");
        sb.append("    metricFillValue: ").append(toIndentedString(metricFillValue)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

