/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * CssTemplateRestApiGetList
 */
@JsonPropertyOrder({
    CssTemplateRestApiGetList.JSON_PROPERTY_CHANGED_BY,
    CssTemplateRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    CssTemplateRestApiGetList.JSON_PROPERTY_CREATED_BY,
    CssTemplateRestApiGetList.JSON_PROPERTY_CREATED_ON,
    CssTemplateRestApiGetList.JSON_PROPERTY_CSS,
    CssTemplateRestApiGetList.JSO<PERSON>_PROPERTY_ID,
    CssTemplateRestApiGetList.JSON_PROPERTY_TEMPLATE_NAME
})
@JsonTypeName("CssTemplateRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class CssTemplateRestApiGetList {
    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private CssTemplateRestApiGetListUser1 changedBy;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private CssTemplateRestApiGetListUser createdBy;

    public static final String JSON_PROPERTY_CREATED_ON = "created_on";
    private OffsetDateTime createdOn;

    public static final String JSON_PROPERTY_CSS = "css";
    private String css;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_TEMPLATE_NAME = "template_name";
    private String templateName;

    public CssTemplateRestApiGetList() {
    }

    @JsonCreator
    public CssTemplateRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
    }

    public CssTemplateRestApiGetList changedBy(CssTemplateRestApiGetListUser1 changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public CssTemplateRestApiGetListUser1 getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(CssTemplateRestApiGetListUser1 changedBy) {
        this.changedBy = changedBy;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public CssTemplateRestApiGetList createdBy(CssTemplateRestApiGetListUser createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public CssTemplateRestApiGetListUser getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(CssTemplateRestApiGetListUser createdBy) {
        this.createdBy = createdBy;
    }


    public CssTemplateRestApiGetList createdOn(OffsetDateTime createdOn) {

        this.createdOn = createdOn;
        return this;
    }

    /**
     * Get createdOn
     *
     * @return createdOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getCreatedOn() {
        return createdOn;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedOn(OffsetDateTime createdOn) {
        this.createdOn = createdOn;
    }


    public CssTemplateRestApiGetList css(String css) {

        this.css = css;
        return this;
    }

    /**
     * Get css
     *
     * @return css
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCss() {
        return css;
    }


    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCss(String css) {
        this.css = css;
    }


    public CssTemplateRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public CssTemplateRestApiGetList templateName(String templateName) {

        this.templateName = templateName;
        return this;
    }

    /**
     * Get templateName
     *
     * @return templateName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTemplateName() {
        return templateName;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CssTemplateRestApiGetList cssTemplateRestApiGetList = (CssTemplateRestApiGetList) o;
        return Objects.equals(this.changedBy, cssTemplateRestApiGetList.changedBy) &&
            Objects.equals(this.changedOnDeltaHumanized, cssTemplateRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.createdBy, cssTemplateRestApiGetList.createdBy) &&
            Objects.equals(this.createdOn, cssTemplateRestApiGetList.createdOn) &&
            Objects.equals(this.css, cssTemplateRestApiGetList.css) &&
            Objects.equals(this.id, cssTemplateRestApiGetList.id) &&
            Objects.equals(this.templateName, cssTemplateRestApiGetList.templateName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedBy, changedOnDeltaHumanized, createdBy, createdOn, css, id, templateName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class CssTemplateRestApiGetList {\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
        sb.append("    css: ").append(toIndentedString(css)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    templateName: ").append(toIndentedString(templateName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

