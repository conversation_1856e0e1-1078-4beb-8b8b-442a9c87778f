/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TableMetadataResponseSchema
 */
@JsonPropertyOrder({
    TableMetadataResponseSchema.JSON_PROPERTY_COLUMNS,
    TableMetadataResponseSchema.JSON_PROPERTY_FOREIGN_KEYS,
    TableMetadataResponseSchema.JSON_PROPERTY_INDEXES,
    TableMetadataResponseSchema.JSON_PROPERTY_NAME,
    TableMetadataResponseSchema.JSON_PROPERTY_PRIMARY_KEY,
    TableMetadataResponseSchema.JSON_PROPERTY_SELECT_STAR
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TableMetadataResponseSchema {
    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<TableMetadataColumnsResponse> columns;

    public static final String JSON_PROPERTY_FOREIGN_KEYS = "foreignKeys";
    private List<TableMetadataForeignKeysIndexesResponse> foreignKeys;

    public static final String JSON_PROPERTY_INDEXES = "indexes";
    private List<TableMetadataForeignKeysIndexesResponse> indexes;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_PRIMARY_KEY = "primaryKey";
    private TableMetadataPrimaryKeyResponse primaryKey;

    public static final String JSON_PROPERTY_SELECT_STAR = "selectStar";
    private String selectStar;

    public TableMetadataResponseSchema() {
    }

    public TableMetadataResponseSchema columns(List<TableMetadataColumnsResponse> columns) {

        this.columns = columns;
        return this;
    }

    public TableMetadataResponseSchema addColumnsItem(TableMetadataColumnsResponse columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * A list of columns and their metadata
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<TableMetadataColumnsResponse> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<TableMetadataColumnsResponse> columns) {
        this.columns = columns;
    }


    public TableMetadataResponseSchema foreignKeys(List<TableMetadataForeignKeysIndexesResponse> foreignKeys) {

        this.foreignKeys = foreignKeys;
        return this;
    }

    public TableMetadataResponseSchema addForeignKeysItem(TableMetadataForeignKeysIndexesResponse foreignKeysItem) {
        if (this.foreignKeys == null) {
            this.foreignKeys = new ArrayList<>();
        }
        this.foreignKeys.add(foreignKeysItem);
        return this;
    }

    /**
     * A list of foreign keys and their metadata
     *
     * @return foreignKeys
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FOREIGN_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<TableMetadataForeignKeysIndexesResponse> getForeignKeys() {
        return foreignKeys;
    }


    @JsonProperty(JSON_PROPERTY_FOREIGN_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setForeignKeys(List<TableMetadataForeignKeysIndexesResponse> foreignKeys) {
        this.foreignKeys = foreignKeys;
    }


    public TableMetadataResponseSchema indexes(List<TableMetadataForeignKeysIndexesResponse> indexes) {

        this.indexes = indexes;
        return this;
    }

    public TableMetadataResponseSchema addIndexesItem(TableMetadataForeignKeysIndexesResponse indexesItem) {
        if (this.indexes == null) {
            this.indexes = new ArrayList<>();
        }
        this.indexes.add(indexesItem);
        return this;
    }

    /**
     * A list of indexes and their metadata
     *
     * @return indexes
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_INDEXES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<TableMetadataForeignKeysIndexesResponse> getIndexes() {
        return indexes;
    }


    @JsonProperty(JSON_PROPERTY_INDEXES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIndexes(List<TableMetadataForeignKeysIndexesResponse> indexes) {
        this.indexes = indexes;
    }


    public TableMetadataResponseSchema name(String name) {

        this.name = name;
        return this;
    }

    /**
     * The name of the table
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public TableMetadataResponseSchema primaryKey(TableMetadataPrimaryKeyResponse primaryKey) {

        this.primaryKey = primaryKey;
        return this;
    }

    /**
     * Get primaryKey
     *
     * @return primaryKey
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PRIMARY_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public TableMetadataPrimaryKeyResponse getPrimaryKey() {
        return primaryKey;
    }


    @JsonProperty(JSON_PROPERTY_PRIMARY_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPrimaryKey(TableMetadataPrimaryKeyResponse primaryKey) {
        this.primaryKey = primaryKey;
    }


    public TableMetadataResponseSchema selectStar(String selectStar) {

        this.selectStar = selectStar;
        return this;
    }

    /**
     * SQL select star
     *
     * @return selectStar
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SELECT_STAR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSelectStar() {
        return selectStar;
    }


    @JsonProperty(JSON_PROPERTY_SELECT_STAR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSelectStar(String selectStar) {
        this.selectStar = selectStar;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TableMetadataResponseSchema tableMetadataResponseSchema = (TableMetadataResponseSchema) o;
        return Objects.equals(this.columns, tableMetadataResponseSchema.columns) &&
            Objects.equals(this.foreignKeys, tableMetadataResponseSchema.foreignKeys) &&
            Objects.equals(this.indexes, tableMetadataResponseSchema.indexes) &&
            Objects.equals(this.name, tableMetadataResponseSchema.name) &&
            Objects.equals(this.primaryKey, tableMetadataResponseSchema.primaryKey) &&
            Objects.equals(this.selectStar, tableMetadataResponseSchema.selectStar);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columns, foreignKeys, indexes, name, primaryKey, selectStar);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TableMetadataResponseSchema {\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    foreignKeys: ").append(toIndentedString(foreignKeys)).append("\n");
        sb.append("    indexes: ").append(toIndentedString(indexes)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    primaryKey: ").append(toIndentedString(primaryKey)).append("\n");
        sb.append("    selectStar: ").append(toIndentedString(selectStar)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

