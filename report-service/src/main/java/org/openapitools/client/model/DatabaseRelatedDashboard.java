/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * DatabaseRelatedDashboard
 */
@JsonPropertyOrder({
    DatabaseRelatedDashboard.JSON_PROPERTY_ID,
    DatabaseRelatedDashboard.JSON_PROPERTY_JSON_METADATA,
    DatabaseRelatedDashboard.JSON_PROPERTY_SLUG,
    DatabaseRelatedDashboard.JSON_PROPERTY_TITLE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseRelatedDashboard {
    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_JSON_METADATA = "json_metadata";
    private Object jsonMetadata;

    public static final String JSON_PROPERTY_SLUG = "slug";
    private String slug;

    public static final String JSON_PROPERTY_TITLE = "title";
    private String title;

    public DatabaseRelatedDashboard() {
    }

    public DatabaseRelatedDashboard id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DatabaseRelatedDashboard jsonMetadata(Object jsonMetadata) {

        this.jsonMetadata = jsonMetadata;
        return this;
    }

    /**
     * Get jsonMetadata
     *
     * @return jsonMetadata
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getJsonMetadata() {
        return jsonMetadata;
    }


    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJsonMetadata(Object jsonMetadata) {
        this.jsonMetadata = jsonMetadata;
    }


    public DatabaseRelatedDashboard slug(String slug) {

        this.slug = slug;
        return this;
    }

    /**
     * Get slug
     *
     * @return slug
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLUG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSlug() {
        return slug;
    }


    @JsonProperty(JSON_PROPERTY_SLUG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSlug(String slug) {
        this.slug = slug;
    }


    public DatabaseRelatedDashboard title(String title) {

        this.title = title;
        return this;
    }

    /**
     * Get title
     *
     * @return title
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTitle() {
        return title;
    }


    @JsonProperty(JSON_PROPERTY_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTitle(String title) {
        this.title = title;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseRelatedDashboard databaseRelatedDashboard = (DatabaseRelatedDashboard) o;
        return Objects.equals(this.id, databaseRelatedDashboard.id) &&
            Objects.equals(this.jsonMetadata, databaseRelatedDashboard.jsonMetadata) &&
            Objects.equals(this.slug, databaseRelatedDashboard.slug) &&
            Objects.equals(this.title, databaseRelatedDashboard.title);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, jsonMetadata, slug, title);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseRelatedDashboard {\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    jsonMetadata: ").append(toIndentedString(jsonMetadata)).append("\n");
        sb.append("    slug: ").append(toIndentedString(slug)).append("\n");
        sb.append("    title: ").append(toIndentedString(title)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

