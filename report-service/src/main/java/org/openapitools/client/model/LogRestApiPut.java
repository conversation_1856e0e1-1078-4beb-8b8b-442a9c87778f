/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * LogRestApiPut
 */
@JsonPropertyOrder({
    LogRestApiPut.JSON_PROPERTY_ACTION,
    LogRestApiPut.JSON_PROPERTY_DTTM,
    LogRestApiPut.JSON_PROPERTY_JSON,
    LogRestApiPut.JSON_PROPERTY_USER
})
@JsonTypeName("LogRestApi.put")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class LogRestApiPut {
    public static final String JSON_PROPERTY_ACTION = "action";
    private String action;

    public static final String JSON_PROPERTY_DTTM = "dttm";
    private OffsetDateTime dttm;

    public static final String JSON_PROPERTY_JSON = "json";
    private String json;

    public static final String JSON_PROPERTY_USER = "user";
    private Object user = null;

    public LogRestApiPut() {
    }

    public LogRestApiPut action(String action) {

        this.action = action;
        return this;
    }

    /**
     * Get action
     *
     * @return action
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getAction() {
        return action;
    }


    @JsonProperty(JSON_PROPERTY_ACTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAction(String action) {
        this.action = action;
    }


    public LogRestApiPut dttm(OffsetDateTime dttm) {

        this.dttm = dttm;
        return this;
    }

    /**
     * Get dttm
     *
     * @return dttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getDttm() {
        return dttm;
    }


    @JsonProperty(JSON_PROPERTY_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDttm(OffsetDateTime dttm) {
        this.dttm = dttm;
    }


    public LogRestApiPut json(String json) {

        this.json = json;
        return this;
    }

    /**
     * Get json
     *
     * @return json
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJson() {
        return json;
    }


    @JsonProperty(JSON_PROPERTY_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJson(String json) {
        this.json = json;
    }


    public LogRestApiPut user(Object user) {

        this.user = user;
        return this;
    }

    /**
     * Get user
     *
     * @return user
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getUser() {
        return user;
    }


    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUser(Object user) {
        this.user = user;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        LogRestApiPut logRestApiPut = (LogRestApiPut) o;
        return Objects.equals(this.action, logRestApiPut.action) &&
            Objects.equals(this.dttm, logRestApiPut.dttm) &&
            Objects.equals(this.json, logRestApiPut.json) &&
            Objects.equals(this.user, logRestApiPut.user);
    }

    @Override
    public int hashCode() {
        return Objects.hash(action, dttm, json, user);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class LogRestApiPut {\n");
        sb.append("    action: ").append(toIndentedString(action)).append("\n");
        sb.append("    dttm: ").append(toIndentedString(dttm)).append("\n");
        sb.append("    json: ").append(toIndentedString(json)).append("\n");
        sb.append("    user: ").append(toIndentedString(user)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

