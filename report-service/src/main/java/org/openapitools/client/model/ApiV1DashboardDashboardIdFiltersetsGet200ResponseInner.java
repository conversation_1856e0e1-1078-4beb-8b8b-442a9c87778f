/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner
 */
@JsonPropertyOrder({
    ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner.JSON_PROPERTY_DESCRIPTION,
    ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner.JSON_PROPERTY_JSON_METADATA,
    ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner.JSON_PROPERTY_NAME,
    ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner.JSON_PROPERTY_OWNER_ID,
    ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner.JSON_PROPERTY_OWNER_TYPE,
    ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner.JSON_PROPERTY_PARAMETERS
})
@JsonTypeName("_api_v1_dashboard__dashboard_id__filtersets_get_200_response_inner")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner {
    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_JSON_METADATA = "json_metadata";
    private String jsonMetadata;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OWNER_ID = "owner_id";
    private Integer ownerId;

    public static final String JSON_PROPERTY_OWNER_TYPE = "owner_type";
    private Integer ownerType;

    public static final String JSON_PROPERTY_PARAMETERS = "parameters";
    private Object parameters = null;

    public ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner() {
    }

    public ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner description(String description) {

        this.description = description;
        return this;
    }

    /**
     * A description field of the filter set
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner jsonMetadata(String jsonMetadata) {

        this.jsonMetadata = jsonMetadata;
        return this;
    }

    /**
     * metadata of the filter set
     *
     * @return jsonMetadata
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJsonMetadata() {
        return jsonMetadata;
    }


    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJsonMetadata(String jsonMetadata) {
        this.jsonMetadata = jsonMetadata;
    }


    public ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Name of the Filter set
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner ownerId(Integer ownerId) {

        this.ownerId = ownerId;
        return this;
    }

    /**
     * A description field of the filter set
     *
     * @return ownerId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getOwnerId() {
        return ownerId;
    }


    @JsonProperty(JSON_PROPERTY_OWNER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }


    public ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner ownerType(Integer ownerType) {

        this.ownerType = ownerType;
        return this;
    }

    /**
     * the Type of the owner ( Dashboard/User)
     *
     * @return ownerType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNER_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getOwnerType() {
        return ownerType;
    }


    @JsonProperty(JSON_PROPERTY_OWNER_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwnerType(Integer ownerType) {
        this.ownerType = ownerType;
    }


    public ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner parameters(Object parameters) {

        this.parameters = parameters;
        return this;
    }

    /**
     * JSON schema defining the needed parameters
     *
     * @return parameters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMETERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getParameters() {
        return parameters;
    }


    @JsonProperty(JSON_PROPERTY_PARAMETERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setParameters(Object parameters) {
        this.parameters = parameters;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner apiV1DashboardDashboardIdFiltersetsGet200ResponseInner = (ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner) o;
        return Objects.equals(this.description, apiV1DashboardDashboardIdFiltersetsGet200ResponseInner.description) &&
            Objects.equals(this.jsonMetadata, apiV1DashboardDashboardIdFiltersetsGet200ResponseInner.jsonMetadata) &&
            Objects.equals(this.name, apiV1DashboardDashboardIdFiltersetsGet200ResponseInner.name) &&
            Objects.equals(this.ownerId, apiV1DashboardDashboardIdFiltersetsGet200ResponseInner.ownerId) &&
            Objects.equals(this.ownerType, apiV1DashboardDashboardIdFiltersetsGet200ResponseInner.ownerType) &&
            Objects.equals(this.parameters, apiV1DashboardDashboardIdFiltersetsGet200ResponseInner.parameters);
    }

    @Override
    public int hashCode() {
        return Objects.hash(description, jsonMetadata, name, ownerId, ownerType, parameters);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DashboardDashboardIdFiltersetsGet200ResponseInner {\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    jsonMetadata: ").append(toIndentedString(jsonMetadata)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    ownerId: ").append(toIndentedString(ownerId)).append("\n");
        sb.append("    ownerType: ").append(toIndentedString(ownerType)).append("\n");
        sb.append("    parameters: ").append(toIndentedString(parameters)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

