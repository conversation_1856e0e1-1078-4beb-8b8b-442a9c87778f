/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * ChartRestApiGet
 */
@JsonPropertyOrder({
    ChartRestApiGet.JSON_PROPERTY_CACHE_TIMEOUT,
    ChartRestApiGet.JSON_PROPERTY_CERTIFICATION_DETAILS,
    ChartRestApiGet.JSON_PROPERTY_CERTIFIED_BY,
    ChartRestApiGet.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    ChartRestApiGet.JSON_PROPERTY_DASHBOARDS,
    ChartRestApiGet.JSON_PROPERTY_DESCRIPTION,
    ChartRestApiGet.JSON_PROPERTY_ID,
    ChartRestApiGet.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    ChartRestApiGet.JSON_PROPERTY_OWNERS,
    ChartRestApiGet.JSON_PROPERTY_PARAMS,
    ChartRestApiGet.JSON_PROPERTY_QUERY_CONTEXT,
    ChartRestApiGet.JSON_PROPERTY_SLICE_NAME,
    ChartRestApiGet.JSON_PROPERTY_TAGS,
    ChartRestApiGet.JSON_PROPERTY_THUMBNAIL_URL,
    ChartRestApiGet.JSON_PROPERTY_URL,
    ChartRestApiGet.JSON_PROPERTY_VIZ_TYPE
})
@JsonTypeName("ChartRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartRestApiGet {
    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_DASHBOARDS = "dashboards";
    private ChartRestApiGetDashboard dashboards;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private ChartRestApiGetUser owners;

    public static final String JSON_PROPERTY_PARAMS = "params";
    private String params;

    public static final String JSON_PROPERTY_QUERY_CONTEXT = "query_context";
    private String queryContext;

    public static final String JSON_PROPERTY_SLICE_NAME = "slice_name";
    private String sliceName;

    public static final String JSON_PROPERTY_TAGS = "tags";
    private ChartRestApiGetTag tags;

    public static final String JSON_PROPERTY_THUMBNAIL_URL = "thumbnail_url";
    private Object thumbnailUrl = null;

    public static final String JSON_PROPERTY_URL = "url";
    private Object url = null;

    public static final String JSON_PROPERTY_VIZ_TYPE = "viz_type";
    private String vizType;

    public ChartRestApiGet() {
    }

    @JsonCreator
    public ChartRestApiGet(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL) Object thumbnailUrl,
        @JsonProperty(JSON_PROPERTY_URL) Object url
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.thumbnailUrl = thumbnailUrl;
        this.url = url;
    }

    public ChartRestApiGet cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Get cacheTimeout
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public ChartRestApiGet certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Get certificationDetails
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public ChartRestApiGet certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Get certifiedBy
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public ChartRestApiGet dashboards(ChartRestApiGetDashboard dashboards) {

        this.dashboards = dashboards;
        return this;
    }

    /**
     * Get dashboards
     *
     * @return dashboards
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartRestApiGetDashboard getDashboards() {
        return dashboards;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboards(ChartRestApiGetDashboard dashboards) {
        this.dashboards = dashboards;
    }


    public ChartRestApiGet description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public ChartRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ChartRestApiGet isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public ChartRestApiGet owners(ChartRestApiGetUser owners) {

        this.owners = owners;
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartRestApiGetUser getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(ChartRestApiGetUser owners) {
        this.owners = owners;
    }


    public ChartRestApiGet params(String params) {

        this.params = params;
        return this;
    }

    /**
     * Get params
     *
     * @return params
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getParams() {
        return params;
    }


    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setParams(String params) {
        this.params = params;
    }


    public ChartRestApiGet queryContext(String queryContext) {

        this.queryContext = queryContext;
        return this;
    }

    /**
     * Get queryContext
     *
     * @return queryContext
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getQueryContext() {
        return queryContext;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryContext(String queryContext) {
        this.queryContext = queryContext;
    }


    public ChartRestApiGet sliceName(String sliceName) {

        this.sliceName = sliceName;
        return this;
    }

    /**
     * Get sliceName
     *
     * @return sliceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSliceName() {
        return sliceName;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceName(String sliceName) {
        this.sliceName = sliceName;
    }


    public ChartRestApiGet tags(ChartRestApiGetTag tags) {

        this.tags = tags;
        return this;
    }

    /**
     * Get tags
     *
     * @return tags
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartRestApiGetTag getTags() {
        return tags;
    }


    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTags(ChartRestApiGetTag tags) {
        this.tags = tags;
    }


    /**
     * Get thumbnailUrl
     *
     * @return thumbnailUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getThumbnailUrl() {
        return thumbnailUrl;
    }


    /**
     * Get url
     *
     * @return url
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getUrl() {
        return url;
    }


    public ChartRestApiGet vizType(String vizType) {

        this.vizType = vizType;
        return this;
    }

    /**
     * Get vizType
     *
     * @return vizType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVizType() {
        return vizType;
    }


    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVizType(String vizType) {
        this.vizType = vizType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartRestApiGet chartRestApiGet = (ChartRestApiGet) o;
        return Objects.equals(this.cacheTimeout, chartRestApiGet.cacheTimeout) &&
            Objects.equals(this.certificationDetails, chartRestApiGet.certificationDetails) &&
            Objects.equals(this.certifiedBy, chartRestApiGet.certifiedBy) &&
            Objects.equals(this.changedOnDeltaHumanized, chartRestApiGet.changedOnDeltaHumanized) &&
            Objects.equals(this.dashboards, chartRestApiGet.dashboards) &&
            Objects.equals(this.description, chartRestApiGet.description) &&
            Objects.equals(this.id, chartRestApiGet.id) &&
            Objects.equals(this.isManagedExternally, chartRestApiGet.isManagedExternally) &&
            Objects.equals(this.owners, chartRestApiGet.owners) &&
            Objects.equals(this.params, chartRestApiGet.params) &&
            Objects.equals(this.queryContext, chartRestApiGet.queryContext) &&
            Objects.equals(this.sliceName, chartRestApiGet.sliceName) &&
            Objects.equals(this.tags, chartRestApiGet.tags) &&
            Objects.equals(this.thumbnailUrl, chartRestApiGet.thumbnailUrl) &&
            Objects.equals(this.url, chartRestApiGet.url) &&
            Objects.equals(this.vizType, chartRestApiGet.vizType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cacheTimeout, certificationDetails, certifiedBy, changedOnDeltaHumanized, dashboards, description, id, isManagedExternally, owners, params, queryContext, sliceName, tags, thumbnailUrl, url, vizType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartRestApiGet {\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    dashboards: ").append(toIndentedString(dashboards)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    params: ").append(toIndentedString(params)).append("\n");
        sb.append("    queryContext: ").append(toIndentedString(queryContext)).append("\n");
        sb.append("    sliceName: ").append(toIndentedString(sliceName)).append("\n");
        sb.append("    tags: ").append(toIndentedString(tags)).append("\n");
        sb.append("    thumbnailUrl: ").append(toIndentedString(thumbnailUrl)).append("\n");
        sb.append("    url: ").append(toIndentedString(url)).append("\n");
        sb.append("    vizType: ").append(toIndentedString(vizType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

