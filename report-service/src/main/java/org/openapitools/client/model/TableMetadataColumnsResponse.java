/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TableMetadataColumnsResponse
 */
@JsonPropertyOrder({
    TableMetadataColumnsResponse.JSON_PROPERTY_DUPLICATES_CONSTRAINT,
    TableMetadataColumnsResponse.JSON_PROPERTY_KEYS,
    TableMetadataColumnsResponse.JSON_PROPERTY_LONG_TYPE,
    TableMetadataColumnsResponse.JSON_PROPERTY_NAME,
    TableMetadataColumnsResponse.JSON_PROPERTY_TYPE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TableMetadataColumnsResponse {
    public static final String JSON_PROPERTY_DUPLICATES_CONSTRAINT = "duplicates_constraint";
    private String duplicatesConstraint;

    public static final String JSON_PROPERTY_KEYS = "keys";
    private List<String> keys;

    public static final String JSON_PROPERTY_LONG_TYPE = "longType";
    private String longType;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public TableMetadataColumnsResponse() {
    }

    public TableMetadataColumnsResponse duplicatesConstraint(String duplicatesConstraint) {

        this.duplicatesConstraint = duplicatesConstraint;
        return this;
    }

    /**
     * Get duplicatesConstraint
     *
     * @return duplicatesConstraint
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DUPLICATES_CONSTRAINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDuplicatesConstraint() {
        return duplicatesConstraint;
    }


    @JsonProperty(JSON_PROPERTY_DUPLICATES_CONSTRAINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDuplicatesConstraint(String duplicatesConstraint) {
        this.duplicatesConstraint = duplicatesConstraint;
    }


    public TableMetadataColumnsResponse keys(List<String> keys) {

        this.keys = keys;
        return this;
    }

    public TableMetadataColumnsResponse addKeysItem(String keysItem) {
        if (this.keys == null) {
            this.keys = new ArrayList<>();
        }
        this.keys.add(keysItem);
        return this;
    }

    /**
     * @return keys
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getKeys() {
        return keys;
    }


    @JsonProperty(JSON_PROPERTY_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setKeys(List<String> keys) {
        this.keys = keys;
    }


    public TableMetadataColumnsResponse longType(String longType) {

        this.longType = longType;
        return this;
    }

    /**
     * The actual backend long type for the column
     *
     * @return longType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LONG_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLongType() {
        return longType;
    }


    @JsonProperty(JSON_PROPERTY_LONG_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLongType(String longType) {
        this.longType = longType;
    }


    public TableMetadataColumnsResponse name(String name) {

        this.name = name;
        return this;
    }

    /**
     * The column name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public TableMetadataColumnsResponse type(String type) {

        this.type = type;
        return this;
    }

    /**
     * The column type
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TableMetadataColumnsResponse tableMetadataColumnsResponse = (TableMetadataColumnsResponse) o;
        return Objects.equals(this.duplicatesConstraint, tableMetadataColumnsResponse.duplicatesConstraint) &&
            Objects.equals(this.keys, tableMetadataColumnsResponse.keys) &&
            Objects.equals(this.longType, tableMetadataColumnsResponse.longType) &&
            Objects.equals(this.name, tableMetadataColumnsResponse.name) &&
            Objects.equals(this.type, tableMetadataColumnsResponse.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(duplicatesConstraint, keys, longType, name, type);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TableMetadataColumnsResponse {\n");
        sb.append("    duplicatesConstraint: ").append(toIndentedString(duplicatesConstraint)).append("\n");
        sb.append("    keys: ").append(toIndentedString(keys)).append("\n");
        sb.append("    longType: ").append(toIndentedString(longType)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

