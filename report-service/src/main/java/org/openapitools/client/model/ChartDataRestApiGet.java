/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * ChartDataRestApiGet
 */
@JsonPropertyOrder({
    ChartDataRestApiGet.JSON_PROPERTY_CACHE_TIMEOUT,
    ChartDataRestApiGet.JSON_PROPERTY_CERTIFICATION_DETAILS,
    ChartDataRestApiGet.JSON_PROPERTY_CERTIFIED_BY,
    ChartDataRestApiGet.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    ChartDataRestApiGet.JSON_PROPERTY_DASHBOARDS,
    ChartDataRestApiGet.JSON_PROPERTY_DESCRIPTION,
    ChartDataRestApiGet.J<PERSON><PERSON>_PROPERTY_ID,
    ChartDataRestApiGet.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    ChartDataRestApiGet.JSON_PROPERTY_OWNERS,
    ChartDataRestApiGet.JSON_PROPERTY_PARAMS,
    ChartDataRestApiGet.JSON_PROPERTY_QUERY_CONTEXT,
    ChartDataRestApiGet.JSON_PROPERTY_SLICE_NAME,
    ChartDataRestApiGet.JSON_PROPERTY_TAGS,
    ChartDataRestApiGet.JSON_PROPERTY_THUMBNAIL_URL,
    ChartDataRestApiGet.JSON_PROPERTY_URL,
    ChartDataRestApiGet.JSON_PROPERTY_VIZ_TYPE
})
@JsonTypeName("ChartDataRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataRestApiGet {
    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_DASHBOARDS = "dashboards";
    private ChartDataRestApiGetDashboard dashboards;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private ChartDataRestApiGetUser owners;

    public static final String JSON_PROPERTY_PARAMS = "params";
    private String params;

    public static final String JSON_PROPERTY_QUERY_CONTEXT = "query_context";
    private String queryContext;

    public static final String JSON_PROPERTY_SLICE_NAME = "slice_name";
    private String sliceName;

    public static final String JSON_PROPERTY_TAGS = "tags";
    private ChartDataRestApiGetTag tags;

    public static final String JSON_PROPERTY_THUMBNAIL_URL = "thumbnail_url";
    private Object thumbnailUrl = null;

    public static final String JSON_PROPERTY_URL = "url";
    private Object url = null;

    public static final String JSON_PROPERTY_VIZ_TYPE = "viz_type";
    private String vizType;

    public ChartDataRestApiGet() {
    }

    @JsonCreator
    public ChartDataRestApiGet(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL) Object thumbnailUrl,
        @JsonProperty(JSON_PROPERTY_URL) Object url
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.thumbnailUrl = thumbnailUrl;
        this.url = url;
    }

    public ChartDataRestApiGet cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Get cacheTimeout
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public ChartDataRestApiGet certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Get certificationDetails
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public ChartDataRestApiGet certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Get certifiedBy
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public ChartDataRestApiGet dashboards(ChartDataRestApiGetDashboard dashboards) {

        this.dashboards = dashboards;
        return this;
    }

    /**
     * Get dashboards
     *
     * @return dashboards
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetDashboard getDashboards() {
        return dashboards;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboards(ChartDataRestApiGetDashboard dashboards) {
        this.dashboards = dashboards;
    }


    public ChartDataRestApiGet description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public ChartDataRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ChartDataRestApiGet isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public ChartDataRestApiGet owners(ChartDataRestApiGetUser owners) {

        this.owners = owners;
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetUser getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(ChartDataRestApiGetUser owners) {
        this.owners = owners;
    }


    public ChartDataRestApiGet params(String params) {

        this.params = params;
        return this;
    }

    /**
     * Get params
     *
     * @return params
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getParams() {
        return params;
    }


    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setParams(String params) {
        this.params = params;
    }


    public ChartDataRestApiGet queryContext(String queryContext) {

        this.queryContext = queryContext;
        return this;
    }

    /**
     * Get queryContext
     *
     * @return queryContext
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getQueryContext() {
        return queryContext;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryContext(String queryContext) {
        this.queryContext = queryContext;
    }


    public ChartDataRestApiGet sliceName(String sliceName) {

        this.sliceName = sliceName;
        return this;
    }

    /**
     * Get sliceName
     *
     * @return sliceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSliceName() {
        return sliceName;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceName(String sliceName) {
        this.sliceName = sliceName;
    }


    public ChartDataRestApiGet tags(ChartDataRestApiGetTag tags) {

        this.tags = tags;
        return this;
    }

    /**
     * Get tags
     *
     * @return tags
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataRestApiGetTag getTags() {
        return tags;
    }


    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTags(ChartDataRestApiGetTag tags) {
        this.tags = tags;
    }


    /**
     * Get thumbnailUrl
     *
     * @return thumbnailUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getThumbnailUrl() {
        return thumbnailUrl;
    }


    /**
     * Get url
     *
     * @return url
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getUrl() {
        return url;
    }


    public ChartDataRestApiGet vizType(String vizType) {

        this.vizType = vizType;
        return this;
    }

    /**
     * Get vizType
     *
     * @return vizType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVizType() {
        return vizType;
    }


    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVizType(String vizType) {
        this.vizType = vizType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataRestApiGet chartDataRestApiGet = (ChartDataRestApiGet) o;
        return Objects.equals(this.cacheTimeout, chartDataRestApiGet.cacheTimeout) &&
            Objects.equals(this.certificationDetails, chartDataRestApiGet.certificationDetails) &&
            Objects.equals(this.certifiedBy, chartDataRestApiGet.certifiedBy) &&
            Objects.equals(this.changedOnDeltaHumanized, chartDataRestApiGet.changedOnDeltaHumanized) &&
            Objects.equals(this.dashboards, chartDataRestApiGet.dashboards) &&
            Objects.equals(this.description, chartDataRestApiGet.description) &&
            Objects.equals(this.id, chartDataRestApiGet.id) &&
            Objects.equals(this.isManagedExternally, chartDataRestApiGet.isManagedExternally) &&
            Objects.equals(this.owners, chartDataRestApiGet.owners) &&
            Objects.equals(this.params, chartDataRestApiGet.params) &&
            Objects.equals(this.queryContext, chartDataRestApiGet.queryContext) &&
            Objects.equals(this.sliceName, chartDataRestApiGet.sliceName) &&
            Objects.equals(this.tags, chartDataRestApiGet.tags) &&
            Objects.equals(this.thumbnailUrl, chartDataRestApiGet.thumbnailUrl) &&
            Objects.equals(this.url, chartDataRestApiGet.url) &&
            Objects.equals(this.vizType, chartDataRestApiGet.vizType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cacheTimeout, certificationDetails, certifiedBy, changedOnDeltaHumanized, dashboards, description, id, isManagedExternally, owners, params, queryContext, sliceName, tags, thumbnailUrl, url, vizType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataRestApiGet {\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    dashboards: ").append(toIndentedString(dashboards)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    params: ").append(toIndentedString(params)).append("\n");
        sb.append("    queryContext: ").append(toIndentedString(queryContext)).append("\n");
        sb.append("    sliceName: ").append(toIndentedString(sliceName)).append("\n");
        sb.append("    tags: ").append(toIndentedString(tags)).append("\n");
        sb.append("    thumbnailUrl: ").append(toIndentedString(thumbnailUrl)).append("\n");
        sb.append("    url: ").append(toIndentedString(url)).append("\n");
        sb.append("    vizType: ").append(toIndentedString(vizType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

