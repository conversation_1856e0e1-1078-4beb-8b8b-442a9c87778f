/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * User2
 */
@JsonPropertyOrder({
    User2.JSON_PROPERTY_FIRST_NAME,
    User2.JSON_PROPERTY_LAST_NAME,
    User2.JSON_PROPERTY_USERNAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class User2 {
    public static final String JSON_PROPERTY_FIRST_NAME = "first_name";
    private String firstName;

    public static final String JSON_PROPERTY_LAST_NAME = "last_name";
    private String lastName;

    public static final String JSON_PROPERTY_USERNAME = "username";
    private String username;

    public User2() {
    }

    public User2 firstName(String firstName) {

        this.firstName = firstName;
        return this;
    }

    /**
     * Get firstName
     *
     * @return firstName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FIRST_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getFirstName() {
        return firstName;
    }


    @JsonProperty(JSON_PROPERTY_FIRST_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }


    public User2 lastName(String lastName) {

        this.lastName = lastName;
        return this;
    }

    /**
     * Get lastName
     *
     * @return lastName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLastName() {
        return lastName;
    }


    @JsonProperty(JSON_PROPERTY_LAST_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }


    public User2 username(String username) {

        this.username = username;
        return this;
    }

    /**
     * Get username
     *
     * @return username
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUsername() {
        return username;
    }


    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        User2 user2 = (User2) o;
        return Objects.equals(this.firstName, user2.firstName) &&
            Objects.equals(this.lastName, user2.lastName) &&
            Objects.equals(this.username, user2.username);
    }

    @Override
    public int hashCode() {
        return Objects.hash(firstName, lastName, username);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class User2 {\n");
        sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
        sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
        sb.append("    username: ").append(toIndentedString(username)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

