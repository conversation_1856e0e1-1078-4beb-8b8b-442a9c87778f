/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * DatabaseTablesResponse
 */
@JsonPropertyOrder({
    DatabaseTablesResponse.JSON_PROPERTY_EXTRA,
    DatabaseTablesResponse.JSON_PROPERTY_TYPE,
    DatabaseTablesResponse.JSON_PROPERTY_VALUE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseTablesResponse {
    public static final String JSON_PROPERTY_EXTRA = "extra";
    private Object extra;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public static final String JSON_PROPERTY_VALUE = "value";
    private String value;

    public DatabaseTablesResponse() {
    }

    public DatabaseTablesResponse extra(Object extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Extra data used to specify column metadata
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(Object extra) {
        this.extra = extra;
    }


    public DatabaseTablesResponse type(String type) {

        this.type = type;
        return this;
    }

    /**
     * table or view
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(String type) {
        this.type = type;
    }


    public DatabaseTablesResponse value(String value) {

        this.value = value;
        return this;
    }

    /**
     * The table or view name
     *
     * @return value
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getValue() {
        return value;
    }


    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseTablesResponse databaseTablesResponse = (DatabaseTablesResponse) o;
        return Objects.equals(this.extra, databaseTablesResponse.extra) &&
            Objects.equals(this.type, databaseTablesResponse.type) &&
            Objects.equals(this.value, databaseTablesResponse.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(extra, type, value);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseTablesResponse {\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("    value: ").append(toIndentedString(value)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

