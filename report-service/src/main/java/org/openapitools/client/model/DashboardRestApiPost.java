/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * DashboardRestApiPost
 */
@JsonPropertyOrder({
    DashboardRestApiPost.JSON_PROPERTY_CERTIFICATION_DETAILS,
    DashboardRestApiPost.JSON_PROPERTY_CERTIFIED_BY,
    DashboardRestApiPost.JSON_PROPERTY_CSS,
    DashboardRestApiPost.JSON_PROPERTY_DASHBOARD_TITLE,
    DashboardRestApiPost.JSON_PROPERTY_EXTERNAL_URL,
    DashboardRestApiPost.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    DashboardRestApiPost.JSON_PROPERTY_JSON_METADATA,
    DashboardRestApiPost.JSON_PROPERTY_OWNERS,
    DashboardRestApiPost.JSON_PROPERTY_POSITION_JSON,
    DashboardRestApiPost.JSON_PROPERTY_PUBLISHED,
    DashboardRestApiPost.JSON_PROPERTY_ROLES,
    DashboardRestApiPost.JSON_PROPERTY_SLUG
})
@JsonTypeName("DashboardRestApi.post")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DashboardRestApiPost {
    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_CSS = "css";
    private String css;

    public static final String JSON_PROPERTY_DASHBOARD_TITLE = "dashboard_title";
    private String dashboardTitle;

    public static final String JSON_PROPERTY_EXTERNAL_URL = "external_url";
    private String externalUrl;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_JSON_METADATA = "json_metadata";
    private String jsonMetadata;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<Integer> owners;

    public static final String JSON_PROPERTY_POSITION_JSON = "position_json";
    private String positionJson;

    public static final String JSON_PROPERTY_PUBLISHED = "published";
    private Boolean published;

    public static final String JSON_PROPERTY_ROLES = "roles";
    private List<Integer> roles;

    public static final String JSON_PROPERTY_SLUG = "slug";
    private String slug;

    public DashboardRestApiPost() {
    }

    public DashboardRestApiPost certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Details of the certification
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public DashboardRestApiPost certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Person or group that has certified this dashboard
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    public DashboardRestApiPost css(String css) {

        this.css = css;
        return this;
    }

    /**
     * Override CSS for the dashboard.
     *
     * @return css
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCss() {
        return css;
    }


    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCss(String css) {
        this.css = css;
    }


    public DashboardRestApiPost dashboardTitle(String dashboardTitle) {

        this.dashboardTitle = dashboardTitle;
        return this;
    }

    /**
     * A title for the dashboard.
     *
     * @return dashboardTitle
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDashboardTitle() {
        return dashboardTitle;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardTitle(String dashboardTitle) {
        this.dashboardTitle = dashboardTitle;
    }


    public DashboardRestApiPost externalUrl(String externalUrl) {

        this.externalUrl = externalUrl;
        return this;
    }

    /**
     * Get externalUrl
     *
     * @return externalUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExternalUrl() {
        return externalUrl;
    }


    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExternalUrl(String externalUrl) {
        this.externalUrl = externalUrl;
    }


    public DashboardRestApiPost isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public DashboardRestApiPost jsonMetadata(String jsonMetadata) {

        this.jsonMetadata = jsonMetadata;
        return this;
    }

    /**
     * This JSON object is generated dynamically when clicking the save or overwrite button in the dashboard view. It is exposed here for reference and for power users who may want to alter  specific parameters.
     *
     * @return jsonMetadata
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJsonMetadata() {
        return jsonMetadata;
    }


    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJsonMetadata(String jsonMetadata) {
        this.jsonMetadata = jsonMetadata;
    }


    public DashboardRestApiPost owners(List<Integer> owners) {

        this.owners = owners;
        return this;
    }

    public DashboardRestApiPost addOwnersItem(Integer ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<Integer> owners) {
        this.owners = owners;
    }


    public DashboardRestApiPost positionJson(String positionJson) {

        this.positionJson = positionJson;
        return this;
    }

    /**
     * This json object describes the positioning of the widgets in the dashboard. It is dynamically generated when adjusting the widgets size and positions by using drag &amp; drop in the dashboard view
     *
     * @return positionJson
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_POSITION_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPositionJson() {
        return positionJson;
    }


    @JsonProperty(JSON_PROPERTY_POSITION_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPositionJson(String positionJson) {
        this.positionJson = positionJson;
    }


    public DashboardRestApiPost published(Boolean published) {

        this.published = published;
        return this;
    }

    /**
     * Determines whether or not this dashboard is visible in the list of all dashboards.
     *
     * @return published
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PUBLISHED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getPublished() {
        return published;
    }


    @JsonProperty(JSON_PROPERTY_PUBLISHED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPublished(Boolean published) {
        this.published = published;
    }


    public DashboardRestApiPost roles(List<Integer> roles) {

        this.roles = roles;
        return this;
    }

    public DashboardRestApiPost addRolesItem(Integer rolesItem) {
        if (this.roles == null) {
            this.roles = new ArrayList<>();
        }
        this.roles.add(rolesItem);
        return this;
    }

    /**
     * Get roles
     *
     * @return roles
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getRoles() {
        return roles;
    }


    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRoles(List<Integer> roles) {
        this.roles = roles;
    }


    public DashboardRestApiPost slug(String slug) {

        this.slug = slug;
        return this;
    }

    /**
     * Unique identifying part for the web address of the dashboard.
     *
     * @return slug
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLUG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSlug() {
        return slug;
    }


    @JsonProperty(JSON_PROPERTY_SLUG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSlug(String slug) {
        this.slug = slug;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DashboardRestApiPost dashboardRestApiPost = (DashboardRestApiPost) o;
        return Objects.equals(this.certificationDetails, dashboardRestApiPost.certificationDetails) &&
            Objects.equals(this.certifiedBy, dashboardRestApiPost.certifiedBy) &&
            Objects.equals(this.css, dashboardRestApiPost.css) &&
            Objects.equals(this.dashboardTitle, dashboardRestApiPost.dashboardTitle) &&
            Objects.equals(this.externalUrl, dashboardRestApiPost.externalUrl) &&
            Objects.equals(this.isManagedExternally, dashboardRestApiPost.isManagedExternally) &&
            Objects.equals(this.jsonMetadata, dashboardRestApiPost.jsonMetadata) &&
            Objects.equals(this.owners, dashboardRestApiPost.owners) &&
            Objects.equals(this.positionJson, dashboardRestApiPost.positionJson) &&
            Objects.equals(this.published, dashboardRestApiPost.published) &&
            Objects.equals(this.roles, dashboardRestApiPost.roles) &&
            Objects.equals(this.slug, dashboardRestApiPost.slug);
    }

    @Override
    public int hashCode() {
        return Objects.hash(certificationDetails, certifiedBy, css, dashboardTitle, externalUrl, isManagedExternally, jsonMetadata, owners, positionJson, published, roles, slug);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DashboardRestApiPost {\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    css: ").append(toIndentedString(css)).append("\n");
        sb.append("    dashboardTitle: ").append(toIndentedString(dashboardTitle)).append("\n");
        sb.append("    externalUrl: ").append(toIndentedString(externalUrl)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    jsonMetadata: ").append(toIndentedString(jsonMetadata)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    positionJson: ").append(toIndentedString(positionJson)).append("\n");
        sb.append("    published: ").append(toIndentedString(published)).append("\n");
        sb.append("    roles: ").append(toIndentedString(roles)).append("\n");
        sb.append("    slug: ").append(toIndentedString(slug)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

