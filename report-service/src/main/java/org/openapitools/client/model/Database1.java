/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * Database1
 */
@JsonPropertyOrder({
    Database1.JSON_PROPERTY_DATABASE_NAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class Database1 {
    public static final String JSON_PROPERTY_DATABASE_NAME = "database_name";
    private String databaseName;

    public Database1() {
    }

    public Database1 databaseName(String databaseName) {

        this.databaseName = databaseName;
        return this;
    }

    /**
     * Get databaseName
     *
     * @return databaseName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatabaseName() {
        return databaseName;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Database1 database1 = (Database1) o;
        return Objects.equals(this.databaseName, database1.databaseName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(databaseName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class Database1 {\n");
        sb.append("    databaseName: ").append(toIndentedString(databaseName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

