/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ChartRestApiGetListUser
 */
@JsonPropertyOrder({
    ChartRestApiGetListUser.JSON_PROPERTY_FIRST_NAME,
    ChartRestApiGetListUser.JSON_PROPERTY_ID,
    ChartRestApiGetListUser.JSON_PROPERTY_LAST_NAME
})
@JsonTypeName("ChartRestApi.get_list.User")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartRestApiGetListUser {
    public static final String JSON_PROPERTY_FIRST_NAME = "first_name";
    private String firstName;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_LAST_NAME = "last_name";
    private String lastName;

    public ChartRestApiGetListUser() {
    }

    public ChartRestApiGetListUser firstName(String firstName) {

        this.firstName = firstName;
        return this;
    }

    /**
     * Get firstName
     *
     * @return firstName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_FIRST_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getFirstName() {
        return firstName;
    }


    @JsonProperty(JSON_PROPERTY_FIRST_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }


    public ChartRestApiGetListUser id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ChartRestApiGetListUser lastName(String lastName) {

        this.lastName = lastName;
        return this;
    }

    /**
     * Get lastName
     *
     * @return lastName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_LAST_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getLastName() {
        return lastName;
    }


    @JsonProperty(JSON_PROPERTY_LAST_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartRestApiGetListUser chartRestApiGetListUser = (ChartRestApiGetListUser) o;
        return Objects.equals(this.firstName, chartRestApiGetListUser.firstName) &&
            Objects.equals(this.id, chartRestApiGetListUser.id) &&
            Objects.equals(this.lastName, chartRestApiGetListUser.lastName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(firstName, id, lastName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartRestApiGetListUser {\n");
        sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

