/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1DashboardPermalinkKeyGet200Response
 */
@JsonPropertyOrder({
    ApiV1DashboardPermalinkKeyGet200Response.JSON_PROPERTY_STATE
})
@JsonTypeName("_api_v1_dashboard_permalink__key__get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DashboardPermalinkKeyGet200Response {
    public static final String JSON_PROPERTY_STATE = "state";
    private Object state;

    public ApiV1DashboardPermalinkKeyGet200Response() {
    }

    public ApiV1DashboardPermalinkKeyGet200Response state(Object state) {

        this.state = state;
        return this;
    }

    /**
     * The stored state
     *
     * @return state
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getState() {
        return state;
    }


    @JsonProperty(JSON_PROPERTY_STATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setState(Object state) {
        this.state = state;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DashboardPermalinkKeyGet200Response apiV1DashboardPermalinkKeyGet200Response = (ApiV1DashboardPermalinkKeyGet200Response) o;
        return Objects.equals(this.state, apiV1DashboardPermalinkKeyGet200Response.state);
    }

    @Override
    public int hashCode() {
        return Objects.hash(state);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DashboardPermalinkKeyGet200Response {\n");
        sb.append("    state: ").append(toIndentedString(state)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

