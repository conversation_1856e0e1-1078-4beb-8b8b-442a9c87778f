/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * SavedQueryRestApiGetListDatabase
 */
@JsonPropertyOrder({
    SavedQueryRestApiGetListDatabase.JSON_PROPERTY_DATABASE_NAME,
    SavedQueryRestApiGetListDatabase.JSON_PROPERTY_ID
})
@JsonTypeName("SavedQueryRestApi.get_list.Database")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class SavedQueryRestApiGetListDatabase {
    public static final String JSON_PROPERTY_DATABASE_NAME = "database_name";
    private String databaseName;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public SavedQueryRestApiGetListDatabase() {
    }

    public SavedQueryRestApiGetListDatabase databaseName(String databaseName) {

        this.databaseName = databaseName;
        return this;
    }

    /**
     * Get databaseName
     *
     * @return databaseName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getDatabaseName() {
        return databaseName;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }


    public SavedQueryRestApiGetListDatabase id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SavedQueryRestApiGetListDatabase savedQueryRestApiGetListDatabase = (SavedQueryRestApiGetListDatabase) o;
        return Objects.equals(this.databaseName, savedQueryRestApiGetListDatabase.databaseName) &&
            Objects.equals(this.id, savedQueryRestApiGetListDatabase.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(databaseName, id);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class SavedQueryRestApiGetListDatabase {\n");
        sb.append("    databaseName: ").append(toIndentedString(databaseName)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

