/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * SavedQueryRestApiGetList
 */
@JsonPropertyOrder({
    SavedQueryRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    SavedQueryRestApiGetList.JSON_PROPERTY_CREATED_BY,
    SavedQueryRestApiGetList.JSON_PROPERTY_CREATED_ON,
    SavedQueryRestApiGetList.JSON_PROPERTY_DATABASE,
    SavedQueryRestApiGetList.JSON_PROPERTY_DB_ID,
    SavedQueryRestApiGetList.JSO<PERSON>_PROPERTY_DESCRIPTION,
    SavedQueryRestApiGetList.JSO<PERSON>_PROPERTY_EXTRA,
    SavedQueryRestApiGetList.JSON_PROPERTY_ID,
    SavedQueryRestApiGetList.JSON_PROPERTY_LABEL,
    SavedQueryRestApiGetList.JSON_PROPERTY_LAST_RUN_DELTA_HUMANIZED,
    SavedQueryRestApiGetList.JSON_PROPERTY_ROWS,
    SavedQueryRestApiGetList.JSON_PROPERTY_SCHEMA,
    SavedQueryRestApiGetList.JSON_PROPERTY_SQL,
    SavedQueryRestApiGetList.JSON_PROPERTY_SQL_TABLES
})
@JsonTypeName("SavedQueryRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class SavedQueryRestApiGetList {
    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private SavedQueryRestApiGetListUser createdBy;

    public static final String JSON_PROPERTY_CREATED_ON = "created_on";
    private OffsetDateTime createdOn;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private SavedQueryRestApiGetListDatabase database;

    public static final String JSON_PROPERTY_DB_ID = "db_id";
    private Integer dbId;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private Object extra = null;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_LABEL = "label";
    private String label;

    public static final String JSON_PROPERTY_LAST_RUN_DELTA_HUMANIZED = "last_run_delta_humanized";
    private Object lastRunDeltaHumanized = null;

    public static final String JSON_PROPERTY_ROWS = "rows";
    private Integer rows;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_SQL_TABLES = "sql_tables";
    private Object sqlTables = null;

    public SavedQueryRestApiGetList() {
    }

    @JsonCreator
    public SavedQueryRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_EXTRA) Object extra,
        @JsonProperty(JSON_PROPERTY_LAST_RUN_DELTA_HUMANIZED) Object lastRunDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_SQL_TABLES) Object sqlTables
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.extra = extra;
        this.lastRunDeltaHumanized = lastRunDeltaHumanized;
        this.sqlTables = sqlTables;
    }

    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public SavedQueryRestApiGetList createdBy(SavedQueryRestApiGetListUser createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public SavedQueryRestApiGetListUser getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(SavedQueryRestApiGetListUser createdBy) {
        this.createdBy = createdBy;
    }


    public SavedQueryRestApiGetList createdOn(OffsetDateTime createdOn) {

        this.createdOn = createdOn;
        return this;
    }

    /**
     * Get createdOn
     *
     * @return createdOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getCreatedOn() {
        return createdOn;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedOn(OffsetDateTime createdOn) {
        this.createdOn = createdOn;
    }


    public SavedQueryRestApiGetList database(SavedQueryRestApiGetListDatabase database) {

        this.database = database;
        return this;
    }

    /**
     * Get database
     *
     * @return database
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public SavedQueryRestApiGetListDatabase getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabase(SavedQueryRestApiGetListDatabase database) {
        this.database = database;
    }


    public SavedQueryRestApiGetList dbId(Integer dbId) {

        this.dbId = dbId;
        return this;
    }

    /**
     * Get dbId
     *
     * @return dbId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDbId() {
        return dbId;
    }


    @JsonProperty(JSON_PROPERTY_DB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDbId(Integer dbId) {
        this.dbId = dbId;
    }


    public SavedQueryRestApiGetList description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExtra() {
        return extra;
    }


    public SavedQueryRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public SavedQueryRestApiGetList label(String label) {

        this.label = label;
        return this;
    }

    /**
     * Get label
     *
     * @return label
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLabel() {
        return label;
    }


    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLabel(String label) {
        this.label = label;
    }


    /**
     * Get lastRunDeltaHumanized
     *
     * @return lastRunDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_RUN_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getLastRunDeltaHumanized() {
        return lastRunDeltaHumanized;
    }


    public SavedQueryRestApiGetList rows(Integer rows) {

        this.rows = rows;
        return this;
    }

    /**
     * Get rows
     *
     * @return rows
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROWS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getRows() {
        return rows;
    }


    @JsonProperty(JSON_PROPERTY_ROWS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRows(Integer rows) {
        this.rows = rows;
    }


    public SavedQueryRestApiGetList schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public SavedQueryRestApiGetList sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    /**
     * Get sqlTables
     *
     * @return sqlTables
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL_TABLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getSqlTables() {
        return sqlTables;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SavedQueryRestApiGetList savedQueryRestApiGetList = (SavedQueryRestApiGetList) o;
        return Objects.equals(this.changedOnDeltaHumanized, savedQueryRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.createdBy, savedQueryRestApiGetList.createdBy) &&
            Objects.equals(this.createdOn, savedQueryRestApiGetList.createdOn) &&
            Objects.equals(this.database, savedQueryRestApiGetList.database) &&
            Objects.equals(this.dbId, savedQueryRestApiGetList.dbId) &&
            Objects.equals(this.description, savedQueryRestApiGetList.description) &&
            Objects.equals(this.extra, savedQueryRestApiGetList.extra) &&
            Objects.equals(this.id, savedQueryRestApiGetList.id) &&
            Objects.equals(this.label, savedQueryRestApiGetList.label) &&
            Objects.equals(this.lastRunDeltaHumanized, savedQueryRestApiGetList.lastRunDeltaHumanized) &&
            Objects.equals(this.rows, savedQueryRestApiGetList.rows) &&
            Objects.equals(this.schema, savedQueryRestApiGetList.schema) &&
            Objects.equals(this.sql, savedQueryRestApiGetList.sql) &&
            Objects.equals(this.sqlTables, savedQueryRestApiGetList.sqlTables);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedOnDeltaHumanized, createdBy, createdOn, database, dbId, description, extra, id, label, lastRunDeltaHumanized, rows, schema, sql, sqlTables);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class SavedQueryRestApiGetList {\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    dbId: ").append(toIndentedString(dbId)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    label: ").append(toIndentedString(label)).append("\n");
        sb.append("    lastRunDeltaHumanized: ").append(toIndentedString(lastRunDeltaHumanized)).append("\n");
        sb.append("    rows: ").append(toIndentedString(rows)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    sqlTables: ").append(toIndentedString(sqlTables)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

