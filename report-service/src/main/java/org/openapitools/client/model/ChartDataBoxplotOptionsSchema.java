/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ChartDataBoxplotOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataBoxplotOptionsSchema.JSON_PROPERTY_GROUPBY,
    ChartDataBoxplotOptionsSchema.JSON_PROPERTY_METRICS,
    ChartDataBoxplotOptionsSchema.JSON_PROPERTY_PERCENTILES,
    ChartDataBoxplotOptionsSchema.JSON_PROPERTY_WHISKER_TYPE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataBoxplotOptionsSchema {
    public static final String JSON_PROPERTY_GROUPBY = "groupby";
    private List<String> groupby;

    public static final String JSON_PROPERTY_METRICS = "metrics";
    private List<Object> metrics;

    public static final String JSON_PROPERTY_PERCENTILES = "percentiles";
    private Object percentiles = null;

    /**
     * Whisker type. Any numpy function will work.
     */
    public enum WhiskerTypeEnum {
        TUKEY("tukey"),

        MIN_MAX("min/max"),

        PERCENTILE("percentile");

        private String value;

        WhiskerTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static WhiskerTypeEnum fromValue(String value) {
            for (WhiskerTypeEnum b : WhiskerTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_WHISKER_TYPE = "whisker_type";
    private WhiskerTypeEnum whiskerType;

    public ChartDataBoxplotOptionsSchema() {
    }

    public ChartDataBoxplotOptionsSchema groupby(List<String> groupby) {

        this.groupby = groupby;
        return this;
    }

    public ChartDataBoxplotOptionsSchema addGroupbyItem(String groupbyItem) {
        if (this.groupby == null) {
            this.groupby = new ArrayList<>();
        }
        this.groupby.add(groupbyItem);
        return this;
    }

    /**
     * Get groupby
     *
     * @return groupby
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GROUPBY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getGroupby() {
        return groupby;
    }


    @JsonProperty(JSON_PROPERTY_GROUPBY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGroupby(List<String> groupby) {
        this.groupby = groupby;
    }


    public ChartDataBoxplotOptionsSchema metrics(List<Object> metrics) {

        this.metrics = metrics;
        return this;
    }

    public ChartDataBoxplotOptionsSchema addMetricsItem(Object metricsItem) {
        if (this.metrics == null) {
            this.metrics = new ArrayList<>();
        }
        this.metrics.add(metricsItem);
        return this;
    }

    /**
     * Aggregate expressions. Metrics can be passed as both references to datasource metrics (strings), or ad-hoc metricswhich are defined only within the query object. See &#x60;ChartDataAdhocMetricSchema&#x60; for the structure of ad-hoc metrics. When metrics is undefined or null, the query is executed without a groupby. However, when metrics is an array (length &gt;&#x3D; 0), a groupby clause is added to the query.
     *
     * @return metrics
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getMetrics() {
        return metrics;
    }


    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMetrics(List<Object> metrics) {
        this.metrics = metrics;
    }


    public ChartDataBoxplotOptionsSchema percentiles(Object percentiles) {

        this.percentiles = percentiles;
        return this;
    }

    /**
     * Upper and lower percentiles for percentile whisker type.
     *
     * @return percentiles
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PERCENTILES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getPercentiles() {
        return percentiles;
    }


    @JsonProperty(JSON_PROPERTY_PERCENTILES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPercentiles(Object percentiles) {
        this.percentiles = percentiles;
    }


    public ChartDataBoxplotOptionsSchema whiskerType(WhiskerTypeEnum whiskerType) {

        this.whiskerType = whiskerType;
        return this;
    }

    /**
     * Whisker type. Any numpy function will work.
     *
     * @return whiskerType
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_WHISKER_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public WhiskerTypeEnum getWhiskerType() {
        return whiskerType;
    }


    @JsonProperty(JSON_PROPERTY_WHISKER_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setWhiskerType(WhiskerTypeEnum whiskerType) {
        this.whiskerType = whiskerType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataBoxplotOptionsSchema chartDataBoxplotOptionsSchema = (ChartDataBoxplotOptionsSchema) o;
        return Objects.equals(this.groupby, chartDataBoxplotOptionsSchema.groupby) &&
            Objects.equals(this.metrics, chartDataBoxplotOptionsSchema.metrics) &&
            Objects.equals(this.percentiles, chartDataBoxplotOptionsSchema.percentiles) &&
            Objects.equals(this.whiskerType, chartDataBoxplotOptionsSchema.whiskerType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(groupby, metrics, percentiles, whiskerType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataBoxplotOptionsSchema {\n");
        sb.append("    groupby: ").append(toIndentedString(groupby)).append("\n");
        sb.append("    metrics: ").append(toIndentedString(metrics)).append("\n");
        sb.append("    percentiles: ").append(toIndentedString(percentiles)).append("\n");
        sb.append("    whiskerType: ").append(toIndentedString(whiskerType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

