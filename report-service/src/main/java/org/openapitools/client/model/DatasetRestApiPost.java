/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * DatasetRestApiPost
 */
@JsonPropertyOrder({
    DatasetRestApiPost.JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM,
    DatasetRestApiPost.JSON_PROPERTY_DATABASE,
    DatasetRestApiPost.JSON_PROPERTY_EXTERNAL_URL,
    DatasetRestApiPost.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    DatasetRestApiPost.JSON_PROPERTY_NORMALIZE_COLUMNS,
    DatasetRestApiPost.JSON_PROPERTY_OWNERS,
    DatasetRestApiPost.JSON_PROPERTY_SCHEMA,
    DatasetRestApiPost.JSON_PROPERTY_SQL,
    DatasetRestApiPost.JSON_PROPERTY_TABLE_NAME
})
@JsonTypeName("DatasetRestApi.post")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetRestApiPost {
    public static final String JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM = "always_filter_main_dttm";
    private Boolean alwaysFilterMainDttm = false;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private Integer database;

    public static final String JSON_PROPERTY_EXTERNAL_URL = "external_url";
    private String externalUrl;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_NORMALIZE_COLUMNS = "normalize_columns";
    private Boolean normalizeColumns = false;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<Integer> owners;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public DatasetRestApiPost() {
    }

    public DatasetRestApiPost alwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {

        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
        return this;
    }

    /**
     * Get alwaysFilterMainDttm
     *
     * @return alwaysFilterMainDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAlwaysFilterMainDttm() {
        return alwaysFilterMainDttm;
    }


    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAlwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {
        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
    }


    public DatasetRestApiPost database(Integer database) {

        this.database = database;
        return this;
    }

    /**
     * Get database
     *
     * @return database
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabase(Integer database) {
        this.database = database;
    }


    public DatasetRestApiPost externalUrl(String externalUrl) {

        this.externalUrl = externalUrl;
        return this;
    }

    /**
     * Get externalUrl
     *
     * @return externalUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExternalUrl() {
        return externalUrl;
    }


    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExternalUrl(String externalUrl) {
        this.externalUrl = externalUrl;
    }


    public DatasetRestApiPost isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public DatasetRestApiPost normalizeColumns(Boolean normalizeColumns) {

        this.normalizeColumns = normalizeColumns;
        return this;
    }

    /**
     * Get normalizeColumns
     *
     * @return normalizeColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getNormalizeColumns() {
        return normalizeColumns;
    }


    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setNormalizeColumns(Boolean normalizeColumns) {
        this.normalizeColumns = normalizeColumns;
    }


    public DatasetRestApiPost owners(List<Integer> owners) {

        this.owners = owners;
        return this;
    }

    public DatasetRestApiPost addOwnersItem(Integer ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<Integer> owners) {
        this.owners = owners;
    }


    public DatasetRestApiPost schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public DatasetRestApiPost sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public DatasetRestApiPost tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * Get tableName
     *
     * @return tableName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetRestApiPost datasetRestApiPost = (DatasetRestApiPost) o;
        return Objects.equals(this.alwaysFilterMainDttm, datasetRestApiPost.alwaysFilterMainDttm) &&
            Objects.equals(this.database, datasetRestApiPost.database) &&
            Objects.equals(this.externalUrl, datasetRestApiPost.externalUrl) &&
            Objects.equals(this.isManagedExternally, datasetRestApiPost.isManagedExternally) &&
            Objects.equals(this.normalizeColumns, datasetRestApiPost.normalizeColumns) &&
            Objects.equals(this.owners, datasetRestApiPost.owners) &&
            Objects.equals(this.schema, datasetRestApiPost.schema) &&
            Objects.equals(this.sql, datasetRestApiPost.sql) &&
            Objects.equals(this.tableName, datasetRestApiPost.tableName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(alwaysFilterMainDttm, database, externalUrl, isManagedExternally, normalizeColumns, owners, schema, sql, tableName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetRestApiPost {\n");
        sb.append("    alwaysFilterMainDttm: ").append(toIndentedString(alwaysFilterMainDttm)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    externalUrl: ").append(toIndentedString(externalUrl)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    normalizeColumns: ").append(toIndentedString(normalizeColumns)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

