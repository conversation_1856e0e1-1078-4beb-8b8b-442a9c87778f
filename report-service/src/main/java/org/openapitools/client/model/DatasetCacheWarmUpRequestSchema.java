/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * DatasetCacheWarmUpRequestSchema
 */
@JsonPropertyOrder({
    DatasetCacheWarmUpRequestSchema.JSON_PROPERTY_DASHBOARD_ID,
    DatasetCacheWarmUpRequestSchema.JSON_PROPERTY_DB_NAME,
    DatasetCacheWarmUpRequestSchema.JSON_PROPERTY_EXTRA_FILTERS,
    DatasetCacheWarmUpRequestSchema.JSON_PROPERTY_TABLE_NAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetCacheWarmUpRequestSchema {
    public static final String JSON_PROPERTY_DASHBOARD_ID = "dashboard_id";
    private Integer dashboardId;

    public static final String JSON_PROPERTY_DB_NAME = "db_name";
    private String dbName;

    public static final String JSON_PROPERTY_EXTRA_FILTERS = "extra_filters";
    private String extraFilters;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public DatasetCacheWarmUpRequestSchema() {
    }

    public DatasetCacheWarmUpRequestSchema dashboardId(Integer dashboardId) {

        this.dashboardId = dashboardId;
        return this;
    }

    /**
     * The ID of the dashboard to get filters for when warming cache
     *
     * @return dashboardId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDashboardId() {
        return dashboardId;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardId(Integer dashboardId) {
        this.dashboardId = dashboardId;
    }


    public DatasetCacheWarmUpRequestSchema dbName(String dbName) {

        this.dbName = dbName;
        return this;
    }

    /**
     * The name of the database where the table is located
     *
     * @return dbName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DB_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getDbName() {
        return dbName;
    }


    @JsonProperty(JSON_PROPERTY_DB_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDbName(String dbName) {
        this.dbName = dbName;
    }


    public DatasetCacheWarmUpRequestSchema extraFilters(String extraFilters) {

        this.extraFilters = extraFilters;
        return this;
    }

    /**
     * Extra filters to apply when warming up cache
     *
     * @return extraFilters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtraFilters() {
        return extraFilters;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtraFilters(String extraFilters) {
        this.extraFilters = extraFilters;
    }


    public DatasetCacheWarmUpRequestSchema tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * The name of the table to warm up cache for
     *
     * @return tableName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetCacheWarmUpRequestSchema datasetCacheWarmUpRequestSchema = (DatasetCacheWarmUpRequestSchema) o;
        return Objects.equals(this.dashboardId, datasetCacheWarmUpRequestSchema.dashboardId) &&
            Objects.equals(this.dbName, datasetCacheWarmUpRequestSchema.dbName) &&
            Objects.equals(this.extraFilters, datasetCacheWarmUpRequestSchema.extraFilters) &&
            Objects.equals(this.tableName, datasetCacheWarmUpRequestSchema.tableName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dashboardId, dbName, extraFilters, tableName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetCacheWarmUpRequestSchema {\n");
        sb.append("    dashboardId: ").append(toIndentedString(dashboardId)).append("\n");
        sb.append("    dbName: ").append(toIndentedString(dbName)).append("\n");
        sb.append("    extraFilters: ").append(toIndentedString(extraFilters)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

