/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * RLSRestApiGetList
 */
@JsonPropertyOrder({
    RLSRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    RLSRestApiGetList.JSON_PROPERTY_CLAUSE,
    RLSRestApiGetList.JSON_PROPERTY_DESCRIPTION,
    RLSRestApiGetList.JSON_PROPERTY_FILTER_TYPE,
    RLSRestApiGetList.JSON_PROPERTY_GROUP_KEY,
    RLSRestApiGetList.JSO<PERSON>_PROPERTY_ID,
    RLSRestApiGetList.JSO<PERSON>_PROPERTY_NAME,
    RLSRestApiGetList.JSON_PROPERTY_ROLES,
    RLSRestApiGetList.JSON_PROPERTY_TABLES
})
@JsonTypeName("RLSRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class RLSRestApiGetList {
    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CLAUSE = "clause";
    private String clause;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    /**
     * filter_type_description
     */
    public enum FilterTypeEnum {
        REGULAR("Regular"),

        BASE("Base");

        private String value;

        FilterTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static FilterTypeEnum fromValue(String value) {
            for (FilterTypeEnum b : FilterTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_FILTER_TYPE = "filter_type";
    private FilterTypeEnum filterType;

    public static final String JSON_PROPERTY_GROUP_KEY = "group_key";
    private String groupKey;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_ROLES = "roles";
    private List<Roles1> roles;

    public static final String JSON_PROPERTY_TABLES = "tables";
    private List<Tables> tables;

    public RLSRestApiGetList() {
    }

    @JsonCreator
    public RLSRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
    }

    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public RLSRestApiGetList clause(String clause) {

        this.clause = clause;
        return this;
    }

    /**
     * clause_description
     *
     * @return clause
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CLAUSE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getClause() {
        return clause;
    }


    @JsonProperty(JSON_PROPERTY_CLAUSE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setClause(String clause) {
        this.clause = clause;
    }


    public RLSRestApiGetList description(String description) {

        this.description = description;
        return this;
    }

    /**
     * description_description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public RLSRestApiGetList filterType(FilterTypeEnum filterType) {

        this.filterType = filterType;
        return this;
    }

    /**
     * filter_type_description
     *
     * @return filterType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public FilterTypeEnum getFilterType() {
        return filterType;
    }


    @JsonProperty(JSON_PROPERTY_FILTER_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterType(FilterTypeEnum filterType) {
        this.filterType = filterType;
    }


    public RLSRestApiGetList groupKey(String groupKey) {

        this.groupKey = groupKey;
        return this;
    }

    /**
     * group_key_description
     *
     * @return groupKey
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GROUP_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getGroupKey() {
        return groupKey;
    }


    @JsonProperty(JSON_PROPERTY_GROUP_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGroupKey(String groupKey) {
        this.groupKey = groupKey;
    }


    public RLSRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * id_description
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public RLSRestApiGetList name(String name) {

        this.name = name;
        return this;
    }

    /**
     * name_description
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public RLSRestApiGetList roles(List<Roles1> roles) {

        this.roles = roles;
        return this;
    }

    public RLSRestApiGetList addRolesItem(Roles1 rolesItem) {
        if (this.roles == null) {
            this.roles = new ArrayList<>();
        }
        this.roles.add(rolesItem);
        return this;
    }

    /**
     * Get roles
     *
     * @return roles
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Roles1> getRoles() {
        return roles;
    }


    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRoles(List<Roles1> roles) {
        this.roles = roles;
    }


    public RLSRestApiGetList tables(List<Tables> tables) {

        this.tables = tables;
        return this;
    }

    public RLSRestApiGetList addTablesItem(Tables tablesItem) {
        if (this.tables == null) {
            this.tables = new ArrayList<>();
        }
        this.tables.add(tablesItem);
        return this;
    }

    /**
     * Get tables
     *
     * @return tables
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Tables> getTables() {
        return tables;
    }


    @JsonProperty(JSON_PROPERTY_TABLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTables(List<Tables> tables) {
        this.tables = tables;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RLSRestApiGetList rlSRestApiGetList = (RLSRestApiGetList) o;
        return Objects.equals(this.changedOnDeltaHumanized, rlSRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.clause, rlSRestApiGetList.clause) &&
            Objects.equals(this.description, rlSRestApiGetList.description) &&
            Objects.equals(this.filterType, rlSRestApiGetList.filterType) &&
            Objects.equals(this.groupKey, rlSRestApiGetList.groupKey) &&
            Objects.equals(this.id, rlSRestApiGetList.id) &&
            Objects.equals(this.name, rlSRestApiGetList.name) &&
            Objects.equals(this.roles, rlSRestApiGetList.roles) &&
            Objects.equals(this.tables, rlSRestApiGetList.tables);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedOnDeltaHumanized, clause, description, filterType, groupKey, id, name, roles, tables);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RLSRestApiGetList {\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    clause: ").append(toIndentedString(clause)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    filterType: ").append(toIndentedString(filterType)).append("\n");
        sb.append("    groupKey: ").append(toIndentedString(groupKey)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    roles: ").append(toIndentedString(roles)).append("\n");
        sb.append("    tables: ").append(toIndentedString(tables)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

