/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * DatabaseFunctionNamesResponse
 */
@JsonPropertyOrder({
    DatabaseFunctionNamesResponse.JSON_PROPERTY_FUNCTION_NAMES
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseFunctionNamesResponse {
    public static final String JSON_PROPERTY_FUNCTION_NAMES = "function_names";
    private List<String> functionNames;

    public DatabaseFunctionNamesResponse() {
    }

    public DatabaseFunctionNamesResponse functionNames(List<String> functionNames) {

        this.functionNames = functionNames;
        return this;
    }

    public DatabaseFunctionNamesResponse addFunctionNamesItem(String functionNamesItem) {
        if (this.functionNames == null) {
            this.functionNames = new ArrayList<>();
        }
        this.functionNames.add(functionNamesItem);
        return this;
    }

    /**
     * Get functionNames
     *
     * @return functionNames
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FUNCTION_NAMES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getFunctionNames() {
        return functionNames;
    }


    @JsonProperty(JSON_PROPERTY_FUNCTION_NAMES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFunctionNames(List<String> functionNames) {
        this.functionNames = functionNames;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseFunctionNamesResponse databaseFunctionNamesResponse = (DatabaseFunctionNamesResponse) o;
        return Objects.equals(this.functionNames, databaseFunctionNamesResponse.functionNames);
    }

    @Override
    public int hashCode() {
        return Objects.hash(functionNames);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseFunctionNamesResponse {\n");
        sb.append("    functionNames: ").append(toIndentedString(functionNames)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

