/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1SavedQueryPkGet200Response
 */
@JsonPropertyOrder({
    ApiV1SavedQueryPkGet200Response.JSON_PROPERTY_DESCRIPTION_COLUMNS,
    ApiV1SavedQueryPkGet200Response.JSON_PROPERTY_ID,
    ApiV1SavedQueryPkGet200Response.JSON_PROPERTY_LABEL_COLUMNS,
    ApiV1SavedQueryPkGet200Response.JSON_PROPERTY_RESULT,
    ApiV1SavedQueryPkGet200Response.JSON_PROPERTY_SHOW_COLUMNS,
    ApiV1SavedQueryPkGet200Response.JSON_PROPERTY_SHOW_TITLE
})
@JsonTypeName("_api_v1_saved_query__pk__get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1SavedQueryPkGet200Response {
    public static final String JSON_PROPERTY_DESCRIPTION_COLUMNS = "description_columns";
    private ApiV1AnnotationLayerGet200ResponseDescriptionColumns descriptionColumns;

    public static final String JSON_PROPERTY_ID = "id";
    private String id;

    public static final String JSON_PROPERTY_LABEL_COLUMNS = "label_columns";
    private ApiV1AnnotationLayerGet200ResponseLabelColumns labelColumns;

    public static final String JSON_PROPERTY_RESULT = "result";
    private SavedQueryRestApiGet result;

    public static final String JSON_PROPERTY_SHOW_COLUMNS = "show_columns";
    private List<String> showColumns;

    public static final String JSON_PROPERTY_SHOW_TITLE = "show_title";
    private String showTitle;

    public ApiV1SavedQueryPkGet200Response() {
    }

    public ApiV1SavedQueryPkGet200Response descriptionColumns(ApiV1AnnotationLayerGet200ResponseDescriptionColumns descriptionColumns) {

        this.descriptionColumns = descriptionColumns;
        return this;
    }

    /**
     * Get descriptionColumns
     *
     * @return descriptionColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ApiV1AnnotationLayerGet200ResponseDescriptionColumns getDescriptionColumns() {
        return descriptionColumns;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescriptionColumns(ApiV1AnnotationLayerGet200ResponseDescriptionColumns descriptionColumns) {
        this.descriptionColumns = descriptionColumns;
    }


    public ApiV1SavedQueryPkGet200Response id(String id) {

        this.id = id;
        return this;
    }

    /**
     * The item id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(String id) {
        this.id = id;
    }


    public ApiV1SavedQueryPkGet200Response labelColumns(ApiV1AnnotationLayerGet200ResponseLabelColumns labelColumns) {

        this.labelColumns = labelColumns;
        return this;
    }

    /**
     * Get labelColumns
     *
     * @return labelColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LABEL_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ApiV1AnnotationLayerGet200ResponseLabelColumns getLabelColumns() {
        return labelColumns;
    }


    @JsonProperty(JSON_PROPERTY_LABEL_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLabelColumns(ApiV1AnnotationLayerGet200ResponseLabelColumns labelColumns) {
        this.labelColumns = labelColumns;
    }


    public ApiV1SavedQueryPkGet200Response result(SavedQueryRestApiGet result) {

        this.result = result;
        return this;
    }

    /**
     * Get result
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public SavedQueryRestApiGet getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(SavedQueryRestApiGet result) {
        this.result = result;
    }


    public ApiV1SavedQueryPkGet200Response showColumns(List<String> showColumns) {

        this.showColumns = showColumns;
        return this;
    }

    public ApiV1SavedQueryPkGet200Response addShowColumnsItem(String showColumnsItem) {
        if (this.showColumns == null) {
            this.showColumns = new ArrayList<>();
        }
        this.showColumns.add(showColumnsItem);
        return this;
    }

    /**
     * A list of columns
     *
     * @return showColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SHOW_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getShowColumns() {
        return showColumns;
    }


    @JsonProperty(JSON_PROPERTY_SHOW_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setShowColumns(List<String> showColumns) {
        this.showColumns = showColumns;
    }


    public ApiV1SavedQueryPkGet200Response showTitle(String showTitle) {

        this.showTitle = showTitle;
        return this;
    }

    /**
     * A title to render. Will be translated by babel
     *
     * @return showTitle
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SHOW_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getShowTitle() {
        return showTitle;
    }


    @JsonProperty(JSON_PROPERTY_SHOW_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setShowTitle(String showTitle) {
        this.showTitle = showTitle;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1SavedQueryPkGet200Response apiV1SavedQueryPkGet200Response = (ApiV1SavedQueryPkGet200Response) o;
        return Objects.equals(this.descriptionColumns, apiV1SavedQueryPkGet200Response.descriptionColumns) &&
            Objects.equals(this.id, apiV1SavedQueryPkGet200Response.id) &&
            Objects.equals(this.labelColumns, apiV1SavedQueryPkGet200Response.labelColumns) &&
            Objects.equals(this.result, apiV1SavedQueryPkGet200Response.result) &&
            Objects.equals(this.showColumns, apiV1SavedQueryPkGet200Response.showColumns) &&
            Objects.equals(this.showTitle, apiV1SavedQueryPkGet200Response.showTitle);
    }

    @Override
    public int hashCode() {
        return Objects.hash(descriptionColumns, id, labelColumns, result, showColumns, showTitle);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1SavedQueryPkGet200Response {\n");
        sb.append("    descriptionColumns: ").append(toIndentedString(descriptionColumns)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    labelColumns: ").append(toIndentedString(labelColumns)).append("\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("    showColumns: ").append(toIndentedString(showColumns)).append("\n");
        sb.append("    showTitle: ").append(toIndentedString(showTitle)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

