/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * DashboardRestApiGetList
 */
@JsonPropertyOrder({
    DashboardRestApiGetList.JSON_PROPERTY_CERTIFICATION_DETAILS,
    DashboardRestApiGetList.JSON_PROPERTY_CERTIFIED_BY,
    DashboardRestApiGetList.JSON_PROPERTY_CHANGED_BY,
    DashboardRestApiGetList.JSON_PROPERTY_CHANGED_BY_NAME,
    DashboardRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    DashboardRestApiGetList.JSON_PROPERTY_CHANGED_ON_UTC,
    DashboardRestApiGetList.JSO<PERSON>_PROPERTY_CREATED_BY,
    DashboardRestApiGetList.JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED,
    DashboardRestApiGetList.JSON_PROPERTY_CSS,
    DashboardRestApiGetList.JSON_PROPERTY_DASHBOARD_TITLE,
    DashboardRestApiGetList.JSON_PROPERTY_ID,
    DashboardRestApiGetList.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    DashboardRestApiGetList.JSON_PROPERTY_JSON_METADATA,
    DashboardRestApiGetList.JSON_PROPERTY_OWNERS,
    DashboardRestApiGetList.JSON_PROPERTY_POSITION_JSON,
    DashboardRestApiGetList.JSON_PROPERTY_PUBLISHED,
    DashboardRestApiGetList.JSON_PROPERTY_ROLES,
    DashboardRestApiGetList.JSON_PROPERTY_SLUG,
    DashboardRestApiGetList.JSON_PROPERTY_STATUS,
    DashboardRestApiGetList.JSON_PROPERTY_TAGS,
    DashboardRestApiGetList.JSON_PROPERTY_THUMBNAIL_URL,
    DashboardRestApiGetList.JSON_PROPERTY_URL
})
@JsonTypeName("DashboardRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DashboardRestApiGetList {
    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private DashboardRestApiGetListUser changedBy;

    public static final String JSON_PROPERTY_CHANGED_BY_NAME = "changed_by_name";
    private Object changedByName = null;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CHANGED_ON_UTC = "changed_on_utc";
    private Object changedOnUtc = null;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private DashboardRestApiGetListUser1 createdBy;

    public static final String JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED = "created_on_delta_humanized";
    private Object createdOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CSS = "css";
    private String css;

    public static final String JSON_PROPERTY_DASHBOARD_TITLE = "dashboard_title";
    private String dashboardTitle;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_JSON_METADATA = "json_metadata";
    private String jsonMetadata;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private DashboardRestApiGetListUser2 owners;

    public static final String JSON_PROPERTY_POSITION_JSON = "position_json";
    private String positionJson;

    public static final String JSON_PROPERTY_PUBLISHED = "published";
    private Boolean published;

    public static final String JSON_PROPERTY_ROLES = "roles";
    private DashboardRestApiGetListRole roles;

    public static final String JSON_PROPERTY_SLUG = "slug";
    private String slug;

    public static final String JSON_PROPERTY_STATUS = "status";
    private Object status = null;

    public static final String JSON_PROPERTY_TAGS = "tags";
    private DashboardRestApiGetListTag tags;

    public static final String JSON_PROPERTY_THUMBNAIL_URL = "thumbnail_url";
    private Object thumbnailUrl = null;

    public static final String JSON_PROPERTY_URL = "url";
    private Object url = null;

    public DashboardRestApiGetList() {
    }

    @JsonCreator
    public DashboardRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_BY_NAME) Object changedByName,
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_UTC) Object changedOnUtc,
        @JsonProperty(JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED) Object createdOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_STATUS) Object status,
        @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL) Object thumbnailUrl,
        @JsonProperty(JSON_PROPERTY_URL) Object url
    ) {
        this();
        this.changedByName = changedByName;
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.changedOnUtc = changedOnUtc;
        this.createdOnDeltaHumanized = createdOnDeltaHumanized;
        this.status = status;
        this.thumbnailUrl = thumbnailUrl;
        this.url = url;
    }

    public DashboardRestApiGetList certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Get certificationDetails
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public DashboardRestApiGetList certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Get certifiedBy
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    public DashboardRestApiGetList changedBy(DashboardRestApiGetListUser changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DashboardRestApiGetListUser getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(DashboardRestApiGetListUser changedBy) {
        this.changedBy = changedBy;
    }


    /**
     * Get changedByName
     *
     * @return changedByName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedByName() {
        return changedByName;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    /**
     * Get changedOnUtc
     *
     * @return changedOnUtc
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_UTC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnUtc() {
        return changedOnUtc;
    }


    public DashboardRestApiGetList createdBy(DashboardRestApiGetListUser1 createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DashboardRestApiGetListUser1 getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(DashboardRestApiGetListUser1 createdBy) {
        this.createdBy = createdBy;
    }


    /**
     * Get createdOnDeltaHumanized
     *
     * @return createdOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getCreatedOnDeltaHumanized() {
        return createdOnDeltaHumanized;
    }


    public DashboardRestApiGetList css(String css) {

        this.css = css;
        return this;
    }

    /**
     * Get css
     *
     * @return css
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCss() {
        return css;
    }


    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCss(String css) {
        this.css = css;
    }


    public DashboardRestApiGetList dashboardTitle(String dashboardTitle) {

        this.dashboardTitle = dashboardTitle;
        return this;
    }

    /**
     * Get dashboardTitle
     *
     * @return dashboardTitle
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDashboardTitle() {
        return dashboardTitle;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardTitle(String dashboardTitle) {
        this.dashboardTitle = dashboardTitle;
    }


    public DashboardRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DashboardRestApiGetList isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public DashboardRestApiGetList jsonMetadata(String jsonMetadata) {

        this.jsonMetadata = jsonMetadata;
        return this;
    }

    /**
     * Get jsonMetadata
     *
     * @return jsonMetadata
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJsonMetadata() {
        return jsonMetadata;
    }


    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJsonMetadata(String jsonMetadata) {
        this.jsonMetadata = jsonMetadata;
    }


    public DashboardRestApiGetList owners(DashboardRestApiGetListUser2 owners) {

        this.owners = owners;
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DashboardRestApiGetListUser2 getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(DashboardRestApiGetListUser2 owners) {
        this.owners = owners;
    }


    public DashboardRestApiGetList positionJson(String positionJson) {

        this.positionJson = positionJson;
        return this;
    }

    /**
     * Get positionJson
     *
     * @return positionJson
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_POSITION_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPositionJson() {
        return positionJson;
    }


    @JsonProperty(JSON_PROPERTY_POSITION_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPositionJson(String positionJson) {
        this.positionJson = positionJson;
    }


    public DashboardRestApiGetList published(Boolean published) {

        this.published = published;
        return this;
    }

    /**
     * Get published
     *
     * @return published
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PUBLISHED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getPublished() {
        return published;
    }


    @JsonProperty(JSON_PROPERTY_PUBLISHED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPublished(Boolean published) {
        this.published = published;
    }


    public DashboardRestApiGetList roles(DashboardRestApiGetListRole roles) {

        this.roles = roles;
        return this;
    }

    /**
     * Get roles
     *
     * @return roles
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DashboardRestApiGetListRole getRoles() {
        return roles;
    }


    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRoles(DashboardRestApiGetListRole roles) {
        this.roles = roles;
    }


    public DashboardRestApiGetList slug(String slug) {

        this.slug = slug;
        return this;
    }

    /**
     * Get slug
     *
     * @return slug
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLUG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSlug() {
        return slug;
    }


    @JsonProperty(JSON_PROPERTY_SLUG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSlug(String slug) {
        this.slug = slug;
    }


    /**
     * Get status
     *
     * @return status
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getStatus() {
        return status;
    }


    public DashboardRestApiGetList tags(DashboardRestApiGetListTag tags) {

        this.tags = tags;
        return this;
    }

    /**
     * Get tags
     *
     * @return tags
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DashboardRestApiGetListTag getTags() {
        return tags;
    }


    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTags(DashboardRestApiGetListTag tags) {
        this.tags = tags;
    }


    /**
     * Get thumbnailUrl
     *
     * @return thumbnailUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_THUMBNAIL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getThumbnailUrl() {
        return thumbnailUrl;
    }


    /**
     * Get url
     *
     * @return url
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getUrl() {
        return url;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DashboardRestApiGetList dashboardRestApiGetList = (DashboardRestApiGetList) o;
        return Objects.equals(this.certificationDetails, dashboardRestApiGetList.certificationDetails) &&
            Objects.equals(this.certifiedBy, dashboardRestApiGetList.certifiedBy) &&
            Objects.equals(this.changedBy, dashboardRestApiGetList.changedBy) &&
            Objects.equals(this.changedByName, dashboardRestApiGetList.changedByName) &&
            Objects.equals(this.changedOnDeltaHumanized, dashboardRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.changedOnUtc, dashboardRestApiGetList.changedOnUtc) &&
            Objects.equals(this.createdBy, dashboardRestApiGetList.createdBy) &&
            Objects.equals(this.createdOnDeltaHumanized, dashboardRestApiGetList.createdOnDeltaHumanized) &&
            Objects.equals(this.css, dashboardRestApiGetList.css) &&
            Objects.equals(this.dashboardTitle, dashboardRestApiGetList.dashboardTitle) &&
            Objects.equals(this.id, dashboardRestApiGetList.id) &&
            Objects.equals(this.isManagedExternally, dashboardRestApiGetList.isManagedExternally) &&
            Objects.equals(this.jsonMetadata, dashboardRestApiGetList.jsonMetadata) &&
            Objects.equals(this.owners, dashboardRestApiGetList.owners) &&
            Objects.equals(this.positionJson, dashboardRestApiGetList.positionJson) &&
            Objects.equals(this.published, dashboardRestApiGetList.published) &&
            Objects.equals(this.roles, dashboardRestApiGetList.roles) &&
            Objects.equals(this.slug, dashboardRestApiGetList.slug) &&
            Objects.equals(this.status, dashboardRestApiGetList.status) &&
            Objects.equals(this.tags, dashboardRestApiGetList.tags) &&
            Objects.equals(this.thumbnailUrl, dashboardRestApiGetList.thumbnailUrl) &&
            Objects.equals(this.url, dashboardRestApiGetList.url);
    }

    @Override
    public int hashCode() {
        return Objects.hash(certificationDetails, certifiedBy, changedBy, changedByName, changedOnDeltaHumanized, changedOnUtc, createdBy, createdOnDeltaHumanized, css, dashboardTitle, id, isManagedExternally, jsonMetadata, owners, positionJson, published, roles, slug, status, tags, thumbnailUrl, url);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DashboardRestApiGetList {\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedByName: ").append(toIndentedString(changedByName)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    changedOnUtc: ").append(toIndentedString(changedOnUtc)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    createdOnDeltaHumanized: ").append(toIndentedString(createdOnDeltaHumanized)).append("\n");
        sb.append("    css: ").append(toIndentedString(css)).append("\n");
        sb.append("    dashboardTitle: ").append(toIndentedString(dashboardTitle)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    jsonMetadata: ").append(toIndentedString(jsonMetadata)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    positionJson: ").append(toIndentedString(positionJson)).append("\n");
        sb.append("    published: ").append(toIndentedString(published)).append("\n");
        sb.append("    roles: ").append(toIndentedString(roles)).append("\n");
        sb.append("    slug: ").append(toIndentedString(slug)).append("\n");
        sb.append("    status: ").append(toIndentedString(status)).append("\n");
        sb.append("    tags: ").append(toIndentedString(tags)).append("\n");
        sb.append("    thumbnailUrl: ").append(toIndentedString(thumbnailUrl)).append("\n");
        sb.append("    url: ").append(toIndentedString(url)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

