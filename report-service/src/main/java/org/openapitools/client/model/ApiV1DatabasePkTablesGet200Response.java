/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1DatabasePkTablesGet200Response
 */
@JsonPropertyOrder({
    ApiV1DatabasePkTablesGet200Response.JSON_PROPERTY_COUNT,
    ApiV1DatabasePkTablesGet200Response.JSON_PROPERTY_RESULT
})
@JsonTypeName("_api_v1_database__pk__tables__get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DatabasePkTablesGet200Response {
    public static final String JSON_PROPERTY_COUNT = "count";
    private Integer count;

    public static final String JSON_PROPERTY_RESULT = "result";
    private List<DatabaseTablesResponse> result;

    public ApiV1DatabasePkTablesGet200Response() {
    }

    public ApiV1DatabasePkTablesGet200Response count(Integer count) {

        this.count = count;
        return this;
    }

    /**
     * Get count
     *
     * @return count
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCount() {
        return count;
    }


    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCount(Integer count) {
        this.count = count;
    }


    public ApiV1DatabasePkTablesGet200Response result(List<DatabaseTablesResponse> result) {

        this.result = result;
        return this;
    }

    public ApiV1DatabasePkTablesGet200Response addResultItem(DatabaseTablesResponse resultItem) {
        if (this.result == null) {
            this.result = new ArrayList<>();
        }
        this.result.add(resultItem);
        return this;
    }

    /**
     * A List of tables for given database
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<DatabaseTablesResponse> getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(List<DatabaseTablesResponse> result) {
        this.result = result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DatabasePkTablesGet200Response apiV1DatabasePkTablesGet200Response = (ApiV1DatabasePkTablesGet200Response) o;
        return Objects.equals(this.count, apiV1DatabasePkTablesGet200Response.count) &&
            Objects.equals(this.result, apiV1DatabasePkTablesGet200Response.result);
    }

    @Override
    public int hashCode() {
        return Objects.hash(count, result);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DatabasePkTablesGet200Response {\n");
        sb.append("    count: ").append(toIndentedString(count)).append("\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

