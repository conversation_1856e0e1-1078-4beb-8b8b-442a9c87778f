/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * TagRestApiGetUser
 */
@JsonPropertyOrder({
    TagRestApiGetUser.JSON_PROPERTY_ACTIVE,
    TagRestApiGetUser.JSON_PROPERTY_CHANGED_ON,
    TagRestApiGetUser.JSON_PROPERTY_CREATED_ON,
    TagRestApiGetUser.JSON_PROPERTY_EMAIL,
    TagRestApiGetUser.JSON_PROPERTY_FAIL_LOGIN_COUNT,
    TagRestApiGetUser.JSON_PROPERTY_FIRST_NAME,
    TagRestApiGetUser.JSON_PROPERTY_ID,
    TagRestApiGetUser.JSON_PROPERTY_LAST_LOGIN,
    TagRestApiGetUser.JSON_PROPERTY_LAST_NAME,
    TagRestApiGetUser.JSON_PROPERTY_LOGIN_COUNT,
    TagRestApiGetUser.JSON_PROPERTY_PASSWORD,
    TagRestApiGetUser.JSON_PROPERTY_USERNAME
})
@JsonTypeName("TagRestApi.get.User")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TagRestApiGetUser {
    public static final String JSON_PROPERTY_ACTIVE = "active";
    private Boolean active;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CREATED_ON = "created_on";
    private OffsetDateTime createdOn;

    public static final String JSON_PROPERTY_EMAIL = "email";
    private String email;

    public static final String JSON_PROPERTY_FAIL_LOGIN_COUNT = "fail_login_count";
    private Integer failLoginCount;

    public static final String JSON_PROPERTY_FIRST_NAME = "first_name";
    private String firstName;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_LAST_LOGIN = "last_login";
    private OffsetDateTime lastLogin;

    public static final String JSON_PROPERTY_LAST_NAME = "last_name";
    private String lastName;

    public static final String JSON_PROPERTY_LOGIN_COUNT = "login_count";
    private Integer loginCount;

    public static final String JSON_PROPERTY_PASSWORD = "password";
    private String password;

    public static final String JSON_PROPERTY_USERNAME = "username";
    private String username;

    public TagRestApiGetUser() {
    }

    public TagRestApiGetUser active(Boolean active) {

        this.active = active;
        return this;
    }

    /**
     * Get active
     *
     * @return active
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getActive() {
        return active;
    }


    @JsonProperty(JSON_PROPERTY_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setActive(Boolean active) {
        this.active = active;
    }


    public TagRestApiGetUser changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public TagRestApiGetUser createdOn(OffsetDateTime createdOn) {

        this.createdOn = createdOn;
        return this;
    }

    /**
     * Get createdOn
     *
     * @return createdOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getCreatedOn() {
        return createdOn;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedOn(OffsetDateTime createdOn) {
        this.createdOn = createdOn;
    }


    public TagRestApiGetUser email(String email) {

        this.email = email;
        return this;
    }

    /**
     * Get email
     *
     * @return email
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_EMAIL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getEmail() {
        return email;
    }


    @JsonProperty(JSON_PROPERTY_EMAIL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setEmail(String email) {
        this.email = email;
    }


    public TagRestApiGetUser failLoginCount(Integer failLoginCount) {

        this.failLoginCount = failLoginCount;
        return this;
    }

    /**
     * Get failLoginCount
     *
     * @return failLoginCount
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FAIL_LOGIN_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getFailLoginCount() {
        return failLoginCount;
    }


    @JsonProperty(JSON_PROPERTY_FAIL_LOGIN_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFailLoginCount(Integer failLoginCount) {
        this.failLoginCount = failLoginCount;
    }


    public TagRestApiGetUser firstName(String firstName) {

        this.firstName = firstName;
        return this;
    }

    /**
     * Get firstName
     *
     * @return firstName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_FIRST_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getFirstName() {
        return firstName;
    }


    @JsonProperty(JSON_PROPERTY_FIRST_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }


    public TagRestApiGetUser id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public TagRestApiGetUser lastLogin(OffsetDateTime lastLogin) {

        this.lastLogin = lastLogin;
        return this;
    }

    /**
     * Get lastLogin
     *
     * @return lastLogin
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_LOGIN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getLastLogin() {
        return lastLogin;
    }


    @JsonProperty(JSON_PROPERTY_LAST_LOGIN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastLogin(OffsetDateTime lastLogin) {
        this.lastLogin = lastLogin;
    }


    public TagRestApiGetUser lastName(String lastName) {

        this.lastName = lastName;
        return this;
    }

    /**
     * Get lastName
     *
     * @return lastName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_LAST_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getLastName() {
        return lastName;
    }


    @JsonProperty(JSON_PROPERTY_LAST_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }


    public TagRestApiGetUser loginCount(Integer loginCount) {

        this.loginCount = loginCount;
        return this;
    }

    /**
     * Get loginCount
     *
     * @return loginCount
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LOGIN_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getLoginCount() {
        return loginCount;
    }


    @JsonProperty(JSON_PROPERTY_LOGIN_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLoginCount(Integer loginCount) {
        this.loginCount = loginCount;
    }


    public TagRestApiGetUser password(String password) {

        this.password = password;
        return this;
    }

    /**
     * Get password
     *
     * @return password
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPassword() {
        return password;
    }


    @JsonProperty(JSON_PROPERTY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPassword(String password) {
        this.password = password;
    }


    public TagRestApiGetUser username(String username) {

        this.username = username;
        return this;
    }

    /**
     * Get username
     *
     * @return username
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getUsername() {
        return username;
    }


    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TagRestApiGetUser tagRestApiGetUser = (TagRestApiGetUser) o;
        return Objects.equals(this.active, tagRestApiGetUser.active) &&
            Objects.equals(this.changedOn, tagRestApiGetUser.changedOn) &&
            Objects.equals(this.createdOn, tagRestApiGetUser.createdOn) &&
            Objects.equals(this.email, tagRestApiGetUser.email) &&
            Objects.equals(this.failLoginCount, tagRestApiGetUser.failLoginCount) &&
            Objects.equals(this.firstName, tagRestApiGetUser.firstName) &&
            Objects.equals(this.id, tagRestApiGetUser.id) &&
            Objects.equals(this.lastLogin, tagRestApiGetUser.lastLogin) &&
            Objects.equals(this.lastName, tagRestApiGetUser.lastName) &&
            Objects.equals(this.loginCount, tagRestApiGetUser.loginCount) &&
            Objects.equals(this.password, tagRestApiGetUser.password) &&
            Objects.equals(this.username, tagRestApiGetUser.username);
    }

    @Override
    public int hashCode() {
        return Objects.hash(active, changedOn, createdOn, email, failLoginCount, firstName, id, lastLogin, lastName, loginCount, password, username);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TagRestApiGetUser {\n");
        sb.append("    active: ").append(toIndentedString(active)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
        sb.append("    email: ").append(toIndentedString(email)).append("\n");
        sb.append("    failLoginCount: ").append(toIndentedString(failLoginCount)).append("\n");
        sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    lastLogin: ").append(toIndentedString(lastLogin)).append("\n");
        sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
        sb.append("    loginCount: ").append(toIndentedString(loginCount)).append("\n");
        sb.append("    password: ").append(toIndentedString(password)).append("\n");
        sb.append("    username: ").append(toIndentedString(username)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

