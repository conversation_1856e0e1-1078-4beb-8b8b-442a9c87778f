/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1MenuGet200Response
 */
@JsonPropertyOrder({
    ApiV1MenuGet200Response.JSON_PROPERTY_RESULT
})
@JsonTypeName("_api_v1_menu__get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1MenuGet200Response {
    public static final String JSON_PROPERTY_RESULT = "result";
    private List<ApiV1MenuGet200ResponseResultInner> result;

    public ApiV1MenuGet200Response() {
    }

    public ApiV1MenuGet200Response result(List<ApiV1MenuGet200ResponseResultInner> result) {

        this.result = result;
        return this;
    }

    public ApiV1MenuGet200Response addResultItem(ApiV1MenuGet200ResponseResultInner resultItem) {
        if (this.result == null) {
            this.result = new ArrayList<>();
        }
        this.result.add(resultItem);
        return this;
    }

    /**
     * Menu items in a forest like data structure
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<ApiV1MenuGet200ResponseResultInner> getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(List<ApiV1MenuGet200ResponseResultInner> result) {
        this.result = result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1MenuGet200Response apiV1MenuGet200Response = (ApiV1MenuGet200Response) o;
        return Objects.equals(this.result, apiV1MenuGet200Response.result);
    }

    @Override
    public int hashCode() {
        return Objects.hash(result);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1MenuGet200Response {\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

