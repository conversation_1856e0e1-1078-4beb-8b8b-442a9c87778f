/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * ReportRecipient
 */
@JsonPropertyOrder({
    ReportRecipient.JSON_PROPERTY_RECIPIENT_CONFIG_JSON,
    ReportRecipient.JSON_PROPERTY_TYPE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ReportRecipient {
    public static final String JSON_PROPERTY_RECIPIENT_CONFIG_JSON = "recipient_config_json";
    private ReportRecipientConfigJSON recipientConfigJson;

    /**
     * The recipient type, check spec for valid options
     */
    public enum TypeEnum {
        EMAIL("Email"),

        SLACK("Slack");

        private String value;

        TypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static TypeEnum fromValue(String value) {
            for (TypeEnum b : TypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_TYPE = "type";
    private TypeEnum type;

    public ReportRecipient() {
    }

    public ReportRecipient recipientConfigJson(ReportRecipientConfigJSON recipientConfigJson) {

        this.recipientConfigJson = recipientConfigJson;
        return this;
    }

    /**
     * Get recipientConfigJson
     *
     * @return recipientConfigJson
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RECIPIENT_CONFIG_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ReportRecipientConfigJSON getRecipientConfigJson() {
        return recipientConfigJson;
    }


    @JsonProperty(JSON_PROPERTY_RECIPIENT_CONFIG_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRecipientConfigJson(ReportRecipientConfigJSON recipientConfigJson) {
        this.recipientConfigJson = recipientConfigJson;
    }


    public ReportRecipient type(TypeEnum type) {

        this.type = type;
        return this;
    }

    /**
     * The recipient type, check spec for valid options
     *
     * @return type
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public TypeEnum getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setType(TypeEnum type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ReportRecipient reportRecipient = (ReportRecipient) o;
        return Objects.equals(this.recipientConfigJson, reportRecipient.recipientConfigJson) &&
            Objects.equals(this.type, reportRecipient.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(recipientConfigJson, type);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ReportRecipient {\n");
        sb.append("    recipientConfigJson: ").append(toIndentedString(recipientConfigJson)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

