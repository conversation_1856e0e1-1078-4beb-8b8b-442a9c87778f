/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1SecurityLoginPost200Response
 */
@JsonPropertyOrder({
    ApiV1SecurityLoginPost200Response.JSON_PROPERTY_ACCESS_TOKEN,
    ApiV1SecurityLoginPost200Response.JSON_PROPERTY_REFRESH_TOKEN
})
@JsonTypeName("_api_v1_security_login_post_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1SecurityLoginPost200Response {
    public static final String JSON_PROPERTY_ACCESS_TOKEN = "access_token";
    private String accessToken;

    public static final String JSON_PROPERTY_REFRESH_TOKEN = "refresh_token";
    private String refreshToken;

    public ApiV1SecurityLoginPost200Response() {
    }

    public ApiV1SecurityLoginPost200Response accessToken(String accessToken) {

        this.accessToken = accessToken;
        return this;
    }

    /**
     * Get accessToken
     *
     * @return accessToken
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACCESS_TOKEN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getAccessToken() {
        return accessToken;
    }


    @JsonProperty(JSON_PROPERTY_ACCESS_TOKEN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }


    public ApiV1SecurityLoginPost200Response refreshToken(String refreshToken) {

        this.refreshToken = refreshToken;
        return this;
    }

    /**
     * Get refreshToken
     *
     * @return refreshToken
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_REFRESH_TOKEN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getRefreshToken() {
        return refreshToken;
    }


    @JsonProperty(JSON_PROPERTY_REFRESH_TOKEN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1SecurityLoginPost200Response apiV1SecurityLoginPost200Response = (ApiV1SecurityLoginPost200Response) o;
        return Objects.equals(this.accessToken, apiV1SecurityLoginPost200Response.accessToken) &&
            Objects.equals(this.refreshToken, apiV1SecurityLoginPost200Response.refreshToken);
    }

    @Override
    public int hashCode() {
        return Objects.hash(accessToken, refreshToken);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1SecurityLoginPost200Response {\n");
        sb.append("    accessToken: ").append(toIndentedString(accessToken)).append("\n");
        sb.append("    refreshToken: ").append(toIndentedString(refreshToken)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

