/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * ApiV1DashboardPkPut200Response
 */
@JsonPropertyOrder({
    ApiV1DashboardPkPut200Response.JSON_PROPERTY_ID,
    ApiV1DashboardPkPut200Response.JSON_PROPERTY_LAST_MODIFIED_TIME,
    ApiV1DashboardPkPut200Response.JSON_PROPERTY_RESULT
})
@JsonTypeName("_api_v1_dashboard__pk__put_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DashboardPkPut200Response {
    public static final String JSON_PROPERTY_ID = "id";
    private BigDecimal id;

    public static final String JSON_PROPERTY_LAST_MODIFIED_TIME = "last_modified_time";
    private BigDecimal lastModifiedTime;

    public static final String JSON_PROPERTY_RESULT = "result";
    private DashboardRestApiPut result;

    public ApiV1DashboardPkPut200Response() {
    }

    public ApiV1DashboardPkPut200Response id(BigDecimal id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(BigDecimal id) {
        this.id = id;
    }


    public ApiV1DashboardPkPut200Response lastModifiedTime(BigDecimal lastModifiedTime) {

        this.lastModifiedTime = lastModifiedTime;
        return this;
    }

    /**
     * Get lastModifiedTime
     *
     * @return lastModifiedTime
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getLastModifiedTime() {
        return lastModifiedTime;
    }


    @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastModifiedTime(BigDecimal lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }


    public ApiV1DashboardPkPut200Response result(DashboardRestApiPut result) {

        this.result = result;
        return this;
    }

    /**
     * Get result
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DashboardRestApiPut getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(DashboardRestApiPut result) {
        this.result = result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DashboardPkPut200Response apiV1DashboardPkPut200Response = (ApiV1DashboardPkPut200Response) o;
        return Objects.equals(this.id, apiV1DashboardPkPut200Response.id) &&
            Objects.equals(this.lastModifiedTime, apiV1DashboardPkPut200Response.lastModifiedTime) &&
            Objects.equals(this.result, apiV1DashboardPkPut200Response.result);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, lastModifiedTime, result);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DashboardPkPut200Response {\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    lastModifiedTime: ").append(toIndentedString(lastModifiedTime)).append("\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

