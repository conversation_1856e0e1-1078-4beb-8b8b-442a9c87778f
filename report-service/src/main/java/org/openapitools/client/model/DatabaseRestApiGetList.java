/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * DatabaseRestApiGetList
 */
@JsonPropertyOrder({
    DatabaseRestApiGetList.JSON_PROPERTY_ALLOW_CTAS,
    DatabaseRestApiGetList.JSON_PROPERTY_ALLOW_CVAS,
    DatabaseRestApiGetList.JSON_PROPERTY_ALLOW_DML,
    DatabaseRestApiGetList.JSON_PROPERTY_ALLOW_FILE_UPLOAD,
    DatabaseRestApiGetList.JSON_PROPERTY_ALLOW_RUN_ASYNC,
    DatabaseRestApiGetList.JSON_PROPERTY_ALLOWS_COST_ESTIMATE,
    DatabaseRestApiGetList.JSON_PROPERTY_ALLOWS_SUBQUERY,
    DatabaseRestApiGetList.JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE,
    DatabaseRestApiGetList.JSON_PROPERTY_BACKEND,
    DatabaseRestApiGetList.JSON_PROPERTY_CHANGED_ON,
    DatabaseRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    DatabaseRestApiGetList.JSON_PROPERTY_CREATED_BY,
    DatabaseRestApiGetList.JSON_PROPERTY_DATABASE_NAME,
    DatabaseRestApiGetList.JSON_PROPERTY_DISABLE_DATA_PREVIEW,
    DatabaseRestApiGetList.JSON_PROPERTY_ENGINE_INFORMATION,
    DatabaseRestApiGetList.JSON_PROPERTY_EXPLORE_DATABASE_ID,
    DatabaseRestApiGetList.JSON_PROPERTY_EXPOSE_IN_SQLLAB,
    DatabaseRestApiGetList.JSON_PROPERTY_EXTRA,
    DatabaseRestApiGetList.JSON_PROPERTY_FORCE_CTAS_SCHEMA,
    DatabaseRestApiGetList.JSON_PROPERTY_ID,
    DatabaseRestApiGetList.JSON_PROPERTY_UUID
})
@JsonTypeName("DatabaseRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseRestApiGetList {
    public static final String JSON_PROPERTY_ALLOW_CTAS = "allow_ctas";
    private Boolean allowCtas;

    public static final String JSON_PROPERTY_ALLOW_CVAS = "allow_cvas";
    private Boolean allowCvas;

    public static final String JSON_PROPERTY_ALLOW_DML = "allow_dml";
    private Boolean allowDml;

    public static final String JSON_PROPERTY_ALLOW_FILE_UPLOAD = "allow_file_upload";
    private Boolean allowFileUpload;

    public static final String JSON_PROPERTY_ALLOW_RUN_ASYNC = "allow_run_async";
    private Boolean allowRunAsync;

    public static final String JSON_PROPERTY_ALLOWS_COST_ESTIMATE = "allows_cost_estimate";
    private Object allowsCostEstimate = null;

    public static final String JSON_PROPERTY_ALLOWS_SUBQUERY = "allows_subquery";
    private Object allowsSubquery = null;

    public static final String JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE = "allows_virtual_table_explore";
    private Object allowsVirtualTableExplore = null;

    public static final String JSON_PROPERTY_BACKEND = "backend";
    private Object backend = null;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private DatabaseRestApiGetListUser createdBy;

    public static final String JSON_PROPERTY_DATABASE_NAME = "database_name";
    private String databaseName;

    public static final String JSON_PROPERTY_DISABLE_DATA_PREVIEW = "disable_data_preview";
    private Object disableDataPreview = null;

    public static final String JSON_PROPERTY_ENGINE_INFORMATION = "engine_information";
    private Object engineInformation = null;

    public static final String JSON_PROPERTY_EXPLORE_DATABASE_ID = "explore_database_id";
    private Object exploreDatabaseId = null;

    public static final String JSON_PROPERTY_EXPOSE_IN_SQLLAB = "expose_in_sqllab";
    private Boolean exposeInSqllab;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private String extra;

    public static final String JSON_PROPERTY_FORCE_CTAS_SCHEMA = "force_ctas_schema";
    private String forceCtasSchema;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_UUID = "uuid";
    private UUID uuid;

    public DatabaseRestApiGetList() {
    }

    @JsonCreator
    public DatabaseRestApiGetList(
        @JsonProperty(JSON_PROPERTY_ALLOWS_COST_ESTIMATE) Object allowsCostEstimate,
        @JsonProperty(JSON_PROPERTY_ALLOWS_SUBQUERY) Object allowsSubquery,
        @JsonProperty(JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE) Object allowsVirtualTableExplore,
        @JsonProperty(JSON_PROPERTY_BACKEND) Object backend,
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_DISABLE_DATA_PREVIEW) Object disableDataPreview,
        @JsonProperty(JSON_PROPERTY_ENGINE_INFORMATION) Object engineInformation,
        @JsonProperty(JSON_PROPERTY_EXPLORE_DATABASE_ID) Object exploreDatabaseId
    ) {
        this();
        this.allowsCostEstimate = allowsCostEstimate;
        this.allowsSubquery = allowsSubquery;
        this.allowsVirtualTableExplore = allowsVirtualTableExplore;
        this.backend = backend;
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.disableDataPreview = disableDataPreview;
        this.engineInformation = engineInformation;
        this.exploreDatabaseId = exploreDatabaseId;
    }

    public DatabaseRestApiGetList allowCtas(Boolean allowCtas) {

        this.allowCtas = allowCtas;
        return this;
    }

    /**
     * Get allowCtas
     *
     * @return allowCtas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowCtas() {
        return allowCtas;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowCtas(Boolean allowCtas) {
        this.allowCtas = allowCtas;
    }


    public DatabaseRestApiGetList allowCvas(Boolean allowCvas) {

        this.allowCvas = allowCvas;
        return this;
    }

    /**
     * Get allowCvas
     *
     * @return allowCvas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_CVAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowCvas() {
        return allowCvas;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_CVAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowCvas(Boolean allowCvas) {
        this.allowCvas = allowCvas;
    }


    public DatabaseRestApiGetList allowDml(Boolean allowDml) {

        this.allowDml = allowDml;
        return this;
    }

    /**
     * Get allowDml
     *
     * @return allowDml
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_DML)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowDml() {
        return allowDml;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_DML)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowDml(Boolean allowDml) {
        this.allowDml = allowDml;
    }


    public DatabaseRestApiGetList allowFileUpload(Boolean allowFileUpload) {

        this.allowFileUpload = allowFileUpload;
        return this;
    }

    /**
     * Get allowFileUpload
     *
     * @return allowFileUpload
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_FILE_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowFileUpload() {
        return allowFileUpload;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_FILE_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowFileUpload(Boolean allowFileUpload) {
        this.allowFileUpload = allowFileUpload;
    }


    public DatabaseRestApiGetList allowRunAsync(Boolean allowRunAsync) {

        this.allowRunAsync = allowRunAsync;
        return this;
    }

    /**
     * Get allowRunAsync
     *
     * @return allowRunAsync
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowRunAsync() {
        return allowRunAsync;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowRunAsync(Boolean allowRunAsync) {
        this.allowRunAsync = allowRunAsync;
    }


    /**
     * Get allowsCostEstimate
     *
     * @return allowsCostEstimate
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOWS_COST_ESTIMATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getAllowsCostEstimate() {
        return allowsCostEstimate;
    }


    /**
     * Get allowsSubquery
     *
     * @return allowsSubquery
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOWS_SUBQUERY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getAllowsSubquery() {
        return allowsSubquery;
    }


    /**
     * Get allowsVirtualTableExplore
     *
     * @return allowsVirtualTableExplore
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getAllowsVirtualTableExplore() {
        return allowsVirtualTableExplore;
    }


    /**
     * Get backend
     *
     * @return backend
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_BACKEND)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getBackend() {
        return backend;
    }


    public DatabaseRestApiGetList changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public DatabaseRestApiGetList createdBy(DatabaseRestApiGetListUser createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DatabaseRestApiGetListUser getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(DatabaseRestApiGetListUser createdBy) {
        this.createdBy = createdBy;
    }


    public DatabaseRestApiGetList databaseName(String databaseName) {

        this.databaseName = databaseName;
        return this;
    }

    /**
     * Get databaseName
     *
     * @return databaseName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getDatabaseName() {
        return databaseName;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }


    /**
     * Get disableDataPreview
     *
     * @return disableDataPreview
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DISABLE_DATA_PREVIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDisableDataPreview() {
        return disableDataPreview;
    }


    /**
     * Get engineInformation
     *
     * @return engineInformation
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ENGINE_INFORMATION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getEngineInformation() {
        return engineInformation;
    }


    /**
     * Get exploreDatabaseId
     *
     * @return exploreDatabaseId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPLORE_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExploreDatabaseId() {
        return exploreDatabaseId;
    }


    public DatabaseRestApiGetList exposeInSqllab(Boolean exposeInSqllab) {

        this.exposeInSqllab = exposeInSqllab;
        return this;
    }

    /**
     * Get exposeInSqllab
     *
     * @return exposeInSqllab
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPOSE_IN_SQLLAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getExposeInSqllab() {
        return exposeInSqllab;
    }


    @JsonProperty(JSON_PROPERTY_EXPOSE_IN_SQLLAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExposeInSqllab(Boolean exposeInSqllab) {
        this.exposeInSqllab = exposeInSqllab;
    }


    public DatabaseRestApiGetList extra(String extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(String extra) {
        this.extra = extra;
    }


    public DatabaseRestApiGetList forceCtasSchema(String forceCtasSchema) {

        this.forceCtasSchema = forceCtasSchema;
        return this;
    }

    /**
     * Get forceCtasSchema
     *
     * @return forceCtasSchema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORCE_CTAS_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getForceCtasSchema() {
        return forceCtasSchema;
    }


    @JsonProperty(JSON_PROPERTY_FORCE_CTAS_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setForceCtasSchema(String forceCtasSchema) {
        this.forceCtasSchema = forceCtasSchema;
    }


    public DatabaseRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DatabaseRestApiGetList uuid(UUID uuid) {

        this.uuid = uuid;
        return this;
    }

    /**
     * Get uuid
     *
     * @return uuid
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public UUID getUuid() {
        return uuid;
    }


    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUuid(UUID uuid) {
        this.uuid = uuid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseRestApiGetList databaseRestApiGetList = (DatabaseRestApiGetList) o;
        return Objects.equals(this.allowCtas, databaseRestApiGetList.allowCtas) &&
            Objects.equals(this.allowCvas, databaseRestApiGetList.allowCvas) &&
            Objects.equals(this.allowDml, databaseRestApiGetList.allowDml) &&
            Objects.equals(this.allowFileUpload, databaseRestApiGetList.allowFileUpload) &&
            Objects.equals(this.allowRunAsync, databaseRestApiGetList.allowRunAsync) &&
            Objects.equals(this.allowsCostEstimate, databaseRestApiGetList.allowsCostEstimate) &&
            Objects.equals(this.allowsSubquery, databaseRestApiGetList.allowsSubquery) &&
            Objects.equals(this.allowsVirtualTableExplore, databaseRestApiGetList.allowsVirtualTableExplore) &&
            Objects.equals(this.backend, databaseRestApiGetList.backend) &&
            Objects.equals(this.changedOn, databaseRestApiGetList.changedOn) &&
            Objects.equals(this.changedOnDeltaHumanized, databaseRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.createdBy, databaseRestApiGetList.createdBy) &&
            Objects.equals(this.databaseName, databaseRestApiGetList.databaseName) &&
            Objects.equals(this.disableDataPreview, databaseRestApiGetList.disableDataPreview) &&
            Objects.equals(this.engineInformation, databaseRestApiGetList.engineInformation) &&
            Objects.equals(this.exploreDatabaseId, databaseRestApiGetList.exploreDatabaseId) &&
            Objects.equals(this.exposeInSqllab, databaseRestApiGetList.exposeInSqllab) &&
            Objects.equals(this.extra, databaseRestApiGetList.extra) &&
            Objects.equals(this.forceCtasSchema, databaseRestApiGetList.forceCtasSchema) &&
            Objects.equals(this.id, databaseRestApiGetList.id) &&
            Objects.equals(this.uuid, databaseRestApiGetList.uuid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(allowCtas, allowCvas, allowDml, allowFileUpload, allowRunAsync, allowsCostEstimate, allowsSubquery, allowsVirtualTableExplore, backend, changedOn, changedOnDeltaHumanized, createdBy, databaseName, disableDataPreview, engineInformation, exploreDatabaseId, exposeInSqllab, extra, forceCtasSchema, id, uuid);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseRestApiGetList {\n");
        sb.append("    allowCtas: ").append(toIndentedString(allowCtas)).append("\n");
        sb.append("    allowCvas: ").append(toIndentedString(allowCvas)).append("\n");
        sb.append("    allowDml: ").append(toIndentedString(allowDml)).append("\n");
        sb.append("    allowFileUpload: ").append(toIndentedString(allowFileUpload)).append("\n");
        sb.append("    allowRunAsync: ").append(toIndentedString(allowRunAsync)).append("\n");
        sb.append("    allowsCostEstimate: ").append(toIndentedString(allowsCostEstimate)).append("\n");
        sb.append("    allowsSubquery: ").append(toIndentedString(allowsSubquery)).append("\n");
        sb.append("    allowsVirtualTableExplore: ").append(toIndentedString(allowsVirtualTableExplore)).append("\n");
        sb.append("    backend: ").append(toIndentedString(backend)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    databaseName: ").append(toIndentedString(databaseName)).append("\n");
        sb.append("    disableDataPreview: ").append(toIndentedString(disableDataPreview)).append("\n");
        sb.append("    engineInformation: ").append(toIndentedString(engineInformation)).append("\n");
        sb.append("    exploreDatabaseId: ").append(toIndentedString(exploreDatabaseId)).append("\n");
        sb.append("    exposeInSqllab: ").append(toIndentedString(exposeInSqllab)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    forceCtasSchema: ").append(toIndentedString(forceCtasSchema)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

