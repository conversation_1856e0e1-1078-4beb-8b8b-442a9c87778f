/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TableMetadataPrimaryKeyResponse
 */
@JsonPropertyOrder({
    TableMetadataPrimaryKeyResponse.JSON_PROPERTY_COLUMN_NAMES,
    TableMetadataPrimaryKeyResponse.JSON_PROPERTY_NAME,
    TableMetadataPrimaryKeyResponse.JSON_PROPERTY_TYPE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TableMetadataPrimaryKeyResponse {
    public static final String JSON_PROPERTY_COLUMN_NAMES = "column_names";
    private List<String> columnNames;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public TableMetadataPrimaryKeyResponse() {
    }

    public TableMetadataPrimaryKeyResponse columnNames(List<String> columnNames) {

        this.columnNames = columnNames;
        return this;
    }

    public TableMetadataPrimaryKeyResponse addColumnNamesItem(String columnNamesItem) {
        if (this.columnNames == null) {
            this.columnNames = new ArrayList<>();
        }
        this.columnNames.add(columnNamesItem);
        return this;
    }

    /**
     * Get columnNames
     *
     * @return columnNames
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_NAMES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getColumnNames() {
        return columnNames;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_NAMES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnNames(List<String> columnNames) {
        this.columnNames = columnNames;
    }


    public TableMetadataPrimaryKeyResponse name(String name) {

        this.name = name;
        return this;
    }

    /**
     * The primary key index name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public TableMetadataPrimaryKeyResponse type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Get type
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TableMetadataPrimaryKeyResponse tableMetadataPrimaryKeyResponse = (TableMetadataPrimaryKeyResponse) o;
        return Objects.equals(this.columnNames, tableMetadataPrimaryKeyResponse.columnNames) &&
            Objects.equals(this.name, tableMetadataPrimaryKeyResponse.name) &&
            Objects.equals(this.type, tableMetadataPrimaryKeyResponse.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columnNames, name, type);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TableMetadataPrimaryKeyResponse {\n");
        sb.append("    columnNames: ").append(toIndentedString(columnNames)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

