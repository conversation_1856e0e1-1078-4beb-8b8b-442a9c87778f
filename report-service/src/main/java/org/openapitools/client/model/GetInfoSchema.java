/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.*;

/**
 * GetInfoSchema
 */
@JsonPropertyOrder({
    GetInfoSchema.JSON_PROPERTY_ADD_COLUMNS,
    GetInfoSchema.JSON_PROPERTY_EDIT_COLUMNS,
    GetInfoSchema.JSON_PROPERTY_KEYS
})
@JsonTypeName("get_info_schema")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class GetInfoSchema {
    public static final String JSON_PROPERTY_ADD_COLUMNS = "add_columns";
    private Map<String, GetInfoSchemaAddColumnsValue> addColumns = new HashMap<>();

    public static final String JSON_PROPERTY_EDIT_COLUMNS = "edit_columns";
    private Map<String, GetInfoSchemaAddColumnsValue> editColumns = new HashMap<>();

    /**
     * Gets or Sets keys
     */
    public enum KeysEnum {
        ADD_COLUMNS("add_columns"),

        EDIT_COLUMNS("edit_columns"),

        FILTERS("filters"),

        PERMISSIONS("permissions"),

        ADD_TITLE("add_title"),

        EDIT_TITLE("edit_title"),

        NONE("none");

        private String value;

        KeysEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static KeysEnum fromValue(String value) {
            for (KeysEnum b : KeysEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_KEYS = "keys";
    private List<KeysEnum> keys;

    public GetInfoSchema() {
    }

    public GetInfoSchema addColumns(Map<String, GetInfoSchemaAddColumnsValue> addColumns) {

        this.addColumns = addColumns;
        return this;
    }

    public GetInfoSchema putAddColumnsItem(String key, GetInfoSchemaAddColumnsValue addColumnsItem) {
        if (this.addColumns == null) {
            this.addColumns = new HashMap<>();
        }
        this.addColumns.put(key, addColumnsItem);
        return this;
    }

    /**
     * Get addColumns
     *
     * @return addColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ADD_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Map<String, GetInfoSchemaAddColumnsValue> getAddColumns() {
        return addColumns;
    }


    @JsonProperty(JSON_PROPERTY_ADD_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAddColumns(Map<String, GetInfoSchemaAddColumnsValue> addColumns) {
        this.addColumns = addColumns;
    }


    public GetInfoSchema editColumns(Map<String, GetInfoSchemaAddColumnsValue> editColumns) {

        this.editColumns = editColumns;
        return this;
    }

    public GetInfoSchema putEditColumnsItem(String key, GetInfoSchemaAddColumnsValue editColumnsItem) {
        if (this.editColumns == null) {
            this.editColumns = new HashMap<>();
        }
        this.editColumns.put(key, editColumnsItem);
        return this;
    }

    /**
     * Get editColumns
     *
     * @return editColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EDIT_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Map<String, GetInfoSchemaAddColumnsValue> getEditColumns() {
        return editColumns;
    }


    @JsonProperty(JSON_PROPERTY_EDIT_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEditColumns(Map<String, GetInfoSchemaAddColumnsValue> editColumns) {
        this.editColumns = editColumns;
    }


    public GetInfoSchema keys(List<KeysEnum> keys) {

        this.keys = keys;
        return this;
    }

    public GetInfoSchema addKeysItem(KeysEnum keysItem) {
        if (this.keys == null) {
            this.keys = new ArrayList<>();
        }
        this.keys.add(keysItem);
        return this;
    }

    /**
     * Get keys
     *
     * @return keys
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<KeysEnum> getKeys() {
        return keys;
    }


    @JsonProperty(JSON_PROPERTY_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setKeys(List<KeysEnum> keys) {
        this.keys = keys;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetInfoSchema getInfoSchema = (GetInfoSchema) o;
        return Objects.equals(this.addColumns, getInfoSchema.addColumns) &&
            Objects.equals(this.editColumns, getInfoSchema.editColumns) &&
            Objects.equals(this.keys, getInfoSchema.keys);
    }

    @Override
    public int hashCode() {
        return Objects.hash(addColumns, editColumns, keys);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class GetInfoSchema {\n");
        sb.append("    addColumns: ").append(toIndentedString(addColumns)).append("\n");
        sb.append("    editColumns: ").append(toIndentedString(editColumns)).append("\n");
        sb.append("    keys: ").append(toIndentedString(keys)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

