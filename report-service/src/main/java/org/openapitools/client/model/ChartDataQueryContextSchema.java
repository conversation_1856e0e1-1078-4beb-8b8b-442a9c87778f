/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ChartDataQueryContextSchema
 */
@JsonPropertyOrder({
    ChartDataQueryContextSchema.JSON_PROPERTY_CUSTOM_CACHE_TIMEOUT,
    ChartDataQueryContextSchema.JSON_PROPERTY_DATASOURCE,
    ChartDataQueryContextSchema.JSON_PROPERTY_FORCE,
    ChartDataQueryContextSchema.JSON_PROPERTY_FORM_DATA,
    ChartDataQueryContextSchema.JSON_PROPERTY_QUERIES,
    ChartDataQueryContextSchema.JSON_PROPERTY_RESULT_FORMAT,
    ChartDataQueryContextSchema.JSON_PROPERTY_RESULT_TYPE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataQueryContextSchema {
    public static final String JSON_PROPERTY_CUSTOM_CACHE_TIMEOUT = "custom_cache_timeout";
    private Integer customCacheTimeout;

    public static final String JSON_PROPERTY_DATASOURCE = "datasource";
    private ChartDataDatasource datasource;

    public static final String JSON_PROPERTY_FORCE = "force";
    private Boolean force;

    public static final String JSON_PROPERTY_FORM_DATA = "form_data";
    private Object formData = null;

    public static final String JSON_PROPERTY_QUERIES = "queries";
    private List<ChartDataQueryObject> queries;

    /**
     * Gets or Sets resultFormat
     */
    public enum ResultFormatEnum {
        CSV("csv"),

        JSON("json"),

        XLSX("xlsx");

        private String value;

        ResultFormatEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static ResultFormatEnum fromValue(String value) {
            for (ResultFormatEnum b : ResultFormatEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_RESULT_FORMAT = "result_format";
    private ResultFormatEnum resultFormat;

    /**
     * Gets or Sets resultType
     */
    public enum ResultTypeEnum {
        COLUMNS("columns"),

        FULL("full"),

        QUERY("query"),

        RESULTS("results"),

        SAMPLES("samples"),

        TIMEGRAINS("timegrains"),

        POST_PROCESSED("post_processed"),

        DRILL_DETAIL("drill_detail");

        private String value;

        ResultTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static ResultTypeEnum fromValue(String value) {
            for (ResultTypeEnum b : ResultTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_RESULT_TYPE = "result_type";
    private ResultTypeEnum resultType;

    public ChartDataQueryContextSchema() {
    }

    public ChartDataQueryContextSchema customCacheTimeout(Integer customCacheTimeout) {

        this.customCacheTimeout = customCacheTimeout;
        return this;
    }

    /**
     * Override the default cache timeout
     *
     * @return customCacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CUSTOM_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCustomCacheTimeout() {
        return customCacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CUSTOM_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCustomCacheTimeout(Integer customCacheTimeout) {
        this.customCacheTimeout = customCacheTimeout;
    }


    public ChartDataQueryContextSchema datasource(ChartDataDatasource datasource) {

        this.datasource = datasource;
        return this;
    }

    /**
     * Get datasource
     *
     * @return datasource
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartDataDatasource getDatasource() {
        return datasource;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasource(ChartDataDatasource datasource) {
        this.datasource = datasource;
    }


    public ChartDataQueryContextSchema force(Boolean force) {

        this.force = force;
        return this;
    }

    /**
     * Should the queries be forced to load from the source. Default: &#x60;false&#x60;
     *
     * @return force
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getForce() {
        return force;
    }


    @JsonProperty(JSON_PROPERTY_FORCE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setForce(Boolean force) {
        this.force = force;
    }


    public ChartDataQueryContextSchema formData(Object formData) {

        this.formData = formData;
        return this;
    }

    /**
     * Get formData
     *
     * @return formData
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getFormData() {
        return formData;
    }


    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFormData(Object formData) {
        this.formData = formData;
    }


    public ChartDataQueryContextSchema queries(List<ChartDataQueryObject> queries) {

        this.queries = queries;
        return this;
    }

    public ChartDataQueryContextSchema addQueriesItem(ChartDataQueryObject queriesItem) {
        if (this.queries == null) {
            this.queries = new ArrayList<>();
        }
        this.queries.add(queriesItem);
        return this;
    }

    /**
     * Get queries
     *
     * @return queries
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERIES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<ChartDataQueryObject> getQueries() {
        return queries;
    }


    @JsonProperty(JSON_PROPERTY_QUERIES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueries(List<ChartDataQueryObject> queries) {
        this.queries = queries;
    }


    public ChartDataQueryContextSchema resultFormat(ResultFormatEnum resultFormat) {

        this.resultFormat = resultFormat;
        return this;
    }

    /**
     * Get resultFormat
     *
     * @return resultFormat
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT_FORMAT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ResultFormatEnum getResultFormat() {
        return resultFormat;
    }


    @JsonProperty(JSON_PROPERTY_RESULT_FORMAT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResultFormat(ResultFormatEnum resultFormat) {
        this.resultFormat = resultFormat;
    }


    public ChartDataQueryContextSchema resultType(ResultTypeEnum resultType) {

        this.resultType = resultType;
        return this;
    }

    /**
     * Get resultType
     *
     * @return resultType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ResultTypeEnum getResultType() {
        return resultType;
    }


    @JsonProperty(JSON_PROPERTY_RESULT_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResultType(ResultTypeEnum resultType) {
        this.resultType = resultType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataQueryContextSchema chartDataQueryContextSchema = (ChartDataQueryContextSchema) o;
        return Objects.equals(this.customCacheTimeout, chartDataQueryContextSchema.customCacheTimeout) &&
            Objects.equals(this.datasource, chartDataQueryContextSchema.datasource) &&
            Objects.equals(this.force, chartDataQueryContextSchema.force) &&
            Objects.equals(this.formData, chartDataQueryContextSchema.formData) &&
            Objects.equals(this.queries, chartDataQueryContextSchema.queries) &&
            Objects.equals(this.resultFormat, chartDataQueryContextSchema.resultFormat) &&
            Objects.equals(this.resultType, chartDataQueryContextSchema.resultType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(customCacheTimeout, datasource, force, formData, queries, resultFormat, resultType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataQueryContextSchema {\n");
        sb.append("    customCacheTimeout: ").append(toIndentedString(customCacheTimeout)).append("\n");
        sb.append("    datasource: ").append(toIndentedString(datasource)).append("\n");
        sb.append("    force: ").append(toIndentedString(force)).append("\n");
        sb.append("    formData: ").append(toIndentedString(formData)).append("\n");
        sb.append("    queries: ").append(toIndentedString(queries)).append("\n");
        sb.append("    resultFormat: ").append(toIndentedString(resultFormat)).append("\n");
        sb.append("    resultType: ").append(toIndentedString(resultType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

