/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * ChartDataFilter
 */
@JsonPropertyOrder({
    ChartDataFilter.JSON_PROPERTY_COL,
    ChartDataFilter.JSON_PROPERTY_GRAIN,
    ChartDataFilter.JSON_PROPERTY_IS_EXTRA,
    ChartDataFilter.JSON_PROPERTY_OP,
    ChartDataFilter.JSON_PROPERTY_VAL
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataFilter {
    public static final String JSON_PROPERTY_COL = "col";
    private Object col = null;

    public static final String JSON_PROPERTY_GRAIN = "grain";
    private String grain;

    public static final String JSON_PROPERTY_IS_EXTRA = "isExtra";
    private Boolean isExtra;

    /**
     * The comparison operator.
     */
    public enum OpEnum {
        EQUAL("==", true),

        NOT_EQUAL("!=", true),

        GREATER_THAN(">", true),

        LESS_THAN("<", true),

        GREATER_THAN_OR_EQUAL_TO(">=", true),

        LESS_THAN_OR_EQUAL_TO("<=", true),

        LIKE("LIKE", true),

        ILIKE("ILIKE", true),

        IS_NULL("IS NULL", false),

        IS_NOT_NULL("IS NOT NULL", false),

        IN("IN", true),

        NOT_IN("NOT IN", true),

        IS_TRUE("IS TRUE", false),

        IS_FALSE("IS FALSE", false),

        TEMPORAL_RANGE("TEMPORAL_RANGE", true);

        private String value;

        private Boolean requireValue;

        OpEnum(String value, Boolean requireValue) {
            this.value = value;
            this.requireValue = requireValue;
        }

        public static List<OpEnum> withoutnumber() {
            return List.of(LIKE, ILIKE, IS_TRUE, IS_FALSE, TEMPORAL_RANGE);
        }

        public static List<OpEnum> withoutString() {
            return List.of(IS_TRUE, IS_FALSE, TEMPORAL_RANGE);
        }

        /**
         * time only can be TEMPORAL_RANGE
         */
        public static List<OpEnum> withoutTime() {
            return Arrays.stream(values()).filter(opEnum -> !opEnum.equals(TEMPORAL_RANGE)).toList();
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        public boolean getRequireValue() {
            return requireValue;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static OpEnum fromValue(String value) {
            for (OpEnum b : OpEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_OP = "op";
    private OpEnum op;

    public static final String JSON_PROPERTY_VAL = "val";
    private Object val = null;

    public ChartDataFilter() {
    }

    public ChartDataFilter col(Object col) {

        this.col = col;
        return this;
    }

    /**
     * The column to filter by. Can be either a string (physical or saved expression) or an object (adhoc column)
     *
     * @return col
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Object getCol() {
        return col;
    }


    @JsonProperty(JSON_PROPERTY_COL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setCol(Object col) {
        this.col = col;
    }


    public ChartDataFilter grain(String grain) {

        this.grain = grain;
        return this;
    }

    /**
     * Optional time grain for temporal filters
     *
     * @return grain
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GRAIN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getGrain() {
        return grain;
    }


    @JsonProperty(JSON_PROPERTY_GRAIN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGrain(String grain) {
        this.grain = grain;
    }


    public ChartDataFilter isExtra(Boolean isExtra) {

        this.isExtra = isExtra;
        return this;
    }

    /**
     * Indicates if the filter has been added by a filter component as opposed to being a part of the original query.
     *
     * @return isExtra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsExtra() {
        return isExtra;
    }


    @JsonProperty(JSON_PROPERTY_IS_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsExtra(Boolean isExtra) {
        this.isExtra = isExtra;
    }


    public ChartDataFilter op(OpEnum op) {

        this.op = op;
        return this;
    }

    /**
     * The comparison operator.
     *
     * @return op
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_OP)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public OpEnum getOp() {
        return op;
    }


    @JsonProperty(JSON_PROPERTY_OP)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setOp(OpEnum op) {
        this.op = op;
    }


    public ChartDataFilter val(Object val) {

        this.val = val;
        return this;
    }

    /**
     * The value or values to compare against. Can be a string, integer, decimal, None or list, depending on the operator.
     *
     * @return val
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VAL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getVal() {
        return val;
    }


    @JsonProperty(JSON_PROPERTY_VAL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVal(Object val) {
        this.val = val;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataFilter chartDataFilter = (ChartDataFilter) o;
        return Objects.equals(this.col, chartDataFilter.col) &&
            Objects.equals(this.grain, chartDataFilter.grain) &&
            Objects.equals(this.isExtra, chartDataFilter.isExtra) &&
            Objects.equals(this.op, chartDataFilter.op) &&
            Objects.equals(this.val, chartDataFilter.val);
    }

    @Override
    public int hashCode() {
        return Objects.hash(col, grain, isExtra, op, val);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataFilter {\n");
        sb.append("    col: ").append(toIndentedString(col)).append("\n");
        sb.append("    grain: ").append(toIndentedString(grain)).append("\n");
        sb.append("    isExtra: ").append(toIndentedString(isExtra)).append("\n");
        sb.append("    op: ").append(toIndentedString(op)).append("\n");
        sb.append("    val: ").append(toIndentedString(val)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

