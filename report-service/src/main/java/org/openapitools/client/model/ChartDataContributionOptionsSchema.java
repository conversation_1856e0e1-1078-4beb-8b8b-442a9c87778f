/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * ChartDataContributionOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataContributionOptionsSchema.JSON_PROPERTY_ORIENTATION
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataContributionOptionsSchema {
    /**
     * Should cell values be calculated across the row or column.
     */
    public enum OrientationEnum {
        ROW("row"),

        COLUMN("column");

        private String value;

        OrientationEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static OrientationEnum fromValue(String value) {
            for (OrientationEnum b : OrientationEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_ORIENTATION = "orientation";
    private OrientationEnum orientation;

    public ChartDataContributionOptionsSchema() {
    }

    public ChartDataContributionOptionsSchema orientation(OrientationEnum orientation) {

        this.orientation = orientation;
        return this;
    }

    /**
     * Should cell values be calculated across the row or column.
     *
     * @return orientation
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_ORIENTATION)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public OrientationEnum getOrientation() {
        return orientation;
    }


    @JsonProperty(JSON_PROPERTY_ORIENTATION)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setOrientation(OrientationEnum orientation) {
        this.orientation = orientation;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataContributionOptionsSchema chartDataContributionOptionsSchema = (ChartDataContributionOptionsSchema) o;
        return Objects.equals(this.orientation, chartDataContributionOptionsSchema.orientation);
    }

    @Override
    public int hashCode() {
        return Objects.hash(orientation);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataContributionOptionsSchema {\n");
        sb.append("    orientation: ").append(toIndentedString(orientation)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

