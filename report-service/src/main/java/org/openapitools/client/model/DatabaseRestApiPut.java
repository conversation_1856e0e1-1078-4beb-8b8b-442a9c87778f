/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * DatabaseRestApiPut
 */
@JsonPropertyOrder({
    DatabaseRestApiPut.JSON_PROPERTY_ALLOW_CTAS,
    DatabaseRestApiPut.JSON_PROPERTY_ALLOW_CVAS,
    DatabaseRestApiPut.JSON_PROPERTY_ALLOW_DML,
    DatabaseRestApiPut.JSON_PROPERTY_ALLOW_FILE_UPLOAD,
    DatabaseRestApiPut.JSON_PROPERTY_ALLOW_RUN_ASYNC,
    DatabaseRestApiPut.JSON_PROPERTY_CACHE_TIMEOUT,
    DatabaseRestApiPut.JSON_PROPERTY_CONFIGURATION_METHOD,
    DatabaseRestApiPut.JSON_PROPERTY_DATABASE_NAME,
    DatabaseRestApiPut.JSON_PROPERTY_DRIVER,
    DatabaseRestApiPut.JSON_PROPERTY_ENGINE,
    DatabaseRestApiPut.JSON_PROPERTY_EXPOSE_IN_SQLLAB,
    DatabaseRestApiPut.JSON_PROPERTY_EXTERNAL_URL,
    DatabaseRestApiPut.JSON_PROPERTY_EXTRA,
    DatabaseRestApiPut.JSON_PROPERTY_FORCE_CTAS_SCHEMA,
    DatabaseRestApiPut.JSON_PROPERTY_IMPERSONATE_USER,
    DatabaseRestApiPut.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    DatabaseRestApiPut.JSON_PROPERTY_MASKED_ENCRYPTED_EXTRA,
    DatabaseRestApiPut.JSON_PROPERTY_PARAMETERS,
    DatabaseRestApiPut.JSON_PROPERTY_SERVER_CERT,
    DatabaseRestApiPut.JSON_PROPERTY_SQLALCHEMY_URI,
    DatabaseRestApiPut.JSON_PROPERTY_SSH_TUNNEL,
    DatabaseRestApiPut.JSON_PROPERTY_UUID
})
@JsonTypeName("DatabaseRestApi.put")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseRestApiPut {
    public static final String JSON_PROPERTY_ALLOW_CTAS = "allow_ctas";
    private Boolean allowCtas;

    public static final String JSON_PROPERTY_ALLOW_CVAS = "allow_cvas";
    private Boolean allowCvas;

    public static final String JSON_PROPERTY_ALLOW_DML = "allow_dml";
    private Boolean allowDml;

    public static final String JSON_PROPERTY_ALLOW_FILE_UPLOAD = "allow_file_upload";
    private Boolean allowFileUpload;

    public static final String JSON_PROPERTY_ALLOW_RUN_ASYNC = "allow_run_async";
    private Boolean allowRunAsync;

    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    /**
     * Configuration_method is used on the frontend to inform the backend whether to explode parameters or to provide only a sqlalchemy_uri.
     */
    public enum ConfigurationMethodEnum {
        SQLALCHEMY_FORM("sqlalchemy_form"),

        DYNAMIC_FORM("dynamic_form");

        private String value;

        ConfigurationMethodEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static ConfigurationMethodEnum fromValue(String value) {
            for (ConfigurationMethodEnum b : ConfigurationMethodEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_CONFIGURATION_METHOD = "configuration_method";
    private ConfigurationMethodEnum configurationMethod = ConfigurationMethodEnum.SQLALCHEMY_FORM;

    public static final String JSON_PROPERTY_DATABASE_NAME = "database_name";
    private String databaseName;

    public static final String JSON_PROPERTY_DRIVER = "driver";
    private String driver;

    public static final String JSON_PROPERTY_ENGINE = "engine";
    private String engine;

    public static final String JSON_PROPERTY_EXPOSE_IN_SQLLAB = "expose_in_sqllab";
    private Boolean exposeInSqllab;

    public static final String JSON_PROPERTY_EXTERNAL_URL = "external_url";
    private String externalUrl;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private String extra;

    public static final String JSON_PROPERTY_FORCE_CTAS_SCHEMA = "force_ctas_schema";
    private String forceCtasSchema;

    public static final String JSON_PROPERTY_IMPERSONATE_USER = "impersonate_user";
    private Boolean impersonateUser;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_MASKED_ENCRYPTED_EXTRA = "masked_encrypted_extra";
    private String maskedEncryptedExtra;

    public static final String JSON_PROPERTY_PARAMETERS = "parameters";
    private Map<String, Object> parameters = new HashMap<>();

    public static final String JSON_PROPERTY_SERVER_CERT = "server_cert";
    private String serverCert;

    public static final String JSON_PROPERTY_SQLALCHEMY_URI = "sqlalchemy_uri";
    private String sqlalchemyUri;

    public static final String JSON_PROPERTY_SSH_TUNNEL = "ssh_tunnel";
    private DatabaseSSHTunnel sshTunnel;

    public static final String JSON_PROPERTY_UUID = "uuid";
    private String uuid;

    public DatabaseRestApiPut() {
    }

    public DatabaseRestApiPut allowCtas(Boolean allowCtas) {

        this.allowCtas = allowCtas;
        return this;
    }

    /**
     * Allow CREATE TABLE AS option in SQL Lab
     *
     * @return allowCtas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowCtas() {
        return allowCtas;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowCtas(Boolean allowCtas) {
        this.allowCtas = allowCtas;
    }


    public DatabaseRestApiPut allowCvas(Boolean allowCvas) {

        this.allowCvas = allowCvas;
        return this;
    }

    /**
     * Allow CREATE VIEW AS option in SQL Lab
     *
     * @return allowCvas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_CVAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowCvas() {
        return allowCvas;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_CVAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowCvas(Boolean allowCvas) {
        this.allowCvas = allowCvas;
    }


    public DatabaseRestApiPut allowDml(Boolean allowDml) {

        this.allowDml = allowDml;
        return this;
    }

    /**
     * Allow users to run non-SELECT statements (UPDATE, DELETE, CREATE, ...) in SQL Lab
     *
     * @return allowDml
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_DML)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowDml() {
        return allowDml;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_DML)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowDml(Boolean allowDml) {
        this.allowDml = allowDml;
    }


    public DatabaseRestApiPut allowFileUpload(Boolean allowFileUpload) {

        this.allowFileUpload = allowFileUpload;
        return this;
    }

    /**
     * Allow to upload CSV file data into this databaseIf selected, please set the schemas allowed for csv upload in Extra.
     *
     * @return allowFileUpload
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_FILE_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowFileUpload() {
        return allowFileUpload;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_FILE_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowFileUpload(Boolean allowFileUpload) {
        this.allowFileUpload = allowFileUpload;
    }


    public DatabaseRestApiPut allowRunAsync(Boolean allowRunAsync) {

        this.allowRunAsync = allowRunAsync;
        return this;
    }

    /**
     * Operate the database in asynchronous mode, meaning that the queries are executed on remote workers as opposed to on the web server itself. This assumes that you have a Celery worker setup as well as a results backend. Refer to the installation docs for more information.
     *
     * @return allowRunAsync
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowRunAsync() {
        return allowRunAsync;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowRunAsync(Boolean allowRunAsync) {
        this.allowRunAsync = allowRunAsync;
    }


    public DatabaseRestApiPut cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Duration (in seconds) of the caching timeout for charts of this database. A timeout of 0 indicates that the cache never expires. Note this defaults to the global timeout if undefined.
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public DatabaseRestApiPut configurationMethod(ConfigurationMethodEnum configurationMethod) {

        this.configurationMethod = configurationMethod;
        return this;
    }

    /**
     * Configuration_method is used on the frontend to inform the backend whether to explode parameters or to provide only a sqlalchemy_uri.
     *
     * @return configurationMethod
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CONFIGURATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ConfigurationMethodEnum getConfigurationMethod() {
        return configurationMethod;
    }


    @JsonProperty(JSON_PROPERTY_CONFIGURATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setConfigurationMethod(ConfigurationMethodEnum configurationMethod) {
        this.configurationMethod = configurationMethod;
    }


    public DatabaseRestApiPut databaseName(String databaseName) {

        this.databaseName = databaseName;
        return this;
    }

    /**
     * A database name to identify this connection.
     *
     * @return databaseName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatabaseName() {
        return databaseName;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }


    public DatabaseRestApiPut driver(String driver) {

        this.driver = driver;
        return this;
    }

    /**
     * SQLAlchemy driver to use
     *
     * @return driver
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DRIVER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDriver() {
        return driver;
    }


    @JsonProperty(JSON_PROPERTY_DRIVER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDriver(String driver) {
        this.driver = driver;
    }


    public DatabaseRestApiPut engine(String engine) {

        this.engine = engine;
        return this;
    }

    /**
     * SQLAlchemy engine to use
     *
     * @return engine
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ENGINE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getEngine() {
        return engine;
    }


    @JsonProperty(JSON_PROPERTY_ENGINE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEngine(String engine) {
        this.engine = engine;
    }


    public DatabaseRestApiPut exposeInSqllab(Boolean exposeInSqllab) {

        this.exposeInSqllab = exposeInSqllab;
        return this;
    }

    /**
     * Expose this database to SQLLab
     *
     * @return exposeInSqllab
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPOSE_IN_SQLLAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getExposeInSqllab() {
        return exposeInSqllab;
    }


    @JsonProperty(JSON_PROPERTY_EXPOSE_IN_SQLLAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExposeInSqllab(Boolean exposeInSqllab) {
        this.exposeInSqllab = exposeInSqllab;
    }


    public DatabaseRestApiPut externalUrl(String externalUrl) {

        this.externalUrl = externalUrl;
        return this;
    }

    /**
     * Get externalUrl
     *
     * @return externalUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExternalUrl() {
        return externalUrl;
    }


    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExternalUrl(String externalUrl) {
        this.externalUrl = externalUrl;
    }


    public DatabaseRestApiPut extra(String extra) {

        this.extra = extra;
        return this;
    }

    /**
     * &lt;p&gt;JSON string containing extra configuration elements.&lt;br&gt;1. The &lt;code&gt;engine_params&lt;/code&gt; object gets unpacked into the &lt;a href&#x3D;\&quot;https://docs.sqlalchemy.org/en/latest/core/engines.html#sqlalchemy.create_engine\&quot; rel&#x3D;\&quot;noopener noreferrer\&quot;&gt;sqlalchemy.create_engine&lt;/a&gt; call, while the &lt;code&gt;metadata_params&lt;/code&gt; gets unpacked into the &lt;a href&#x3D;\&quot;https://docs.sqlalchemy.org/en/rel_1_0/core/metadata.html#sqlalchemy.schema.MetaData\&quot; rel&#x3D;\&quot;noopener noreferrer\&quot;&gt;sqlalchemy.MetaData&lt;/a&gt; call.&lt;br&gt;2. The &lt;code&gt;metadata_cache_timeout&lt;/code&gt; is a cache timeout setting in seconds for metadata fetch of this database. Specify it as &lt;strong&gt;\&quot;metadata_cache_timeout\&quot;: {\&quot;schema_cache_timeout\&quot;: 600, \&quot;table_cache_timeout\&quot;: 600}&lt;/strong&gt;. If unset, cache will not be enabled for the functionality. A timeout of 0 indicates that the cache never expires.&lt;br&gt;3. The &lt;code&gt;schemas_allowed_for_file_upload&lt;/code&gt; is a comma separated list of schemas that CSVs are allowed to upload to. Specify it as &lt;strong&gt;\&quot;schemas_allowed_for_file_upload\&quot;: [\&quot;public\&quot;, \&quot;csv_upload\&quot;]&lt;/strong&gt;. If database flavor does not support schema or any schema is allowed to be accessed, just leave the list empty&lt;br&gt;4. The &lt;code&gt;version&lt;/code&gt; field is a string specifying the this db&#39;s version. This should be used with Presto DBs so that the syntax is correct&lt;br&gt;5. The &lt;code&gt;allows_virtual_table_explore&lt;/code&gt; field is a boolean specifying whether or not the Explore button in SQL Lab results is shown.&lt;br&gt;6. The &lt;code&gt;disable_data_preview&lt;/code&gt; field is a boolean specifying whether or not data preview queries will be run when fetching table metadata in SQL Lab.&lt;/p&gt;
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(String extra) {
        this.extra = extra;
    }


    public DatabaseRestApiPut forceCtasSchema(String forceCtasSchema) {

        this.forceCtasSchema = forceCtasSchema;
        return this;
    }

    /**
     * When allowing CREATE TABLE AS option in SQL Lab, this option forces the table to be created in this schema
     *
     * @return forceCtasSchema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORCE_CTAS_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getForceCtasSchema() {
        return forceCtasSchema;
    }


    @JsonProperty(JSON_PROPERTY_FORCE_CTAS_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setForceCtasSchema(String forceCtasSchema) {
        this.forceCtasSchema = forceCtasSchema;
    }


    public DatabaseRestApiPut impersonateUser(Boolean impersonateUser) {

        this.impersonateUser = impersonateUser;
        return this;
    }

    /**
     * If Presto, all the queries in SQL Lab are going to be executed as the currently logged on user who must have permission to run them.&lt;br/&gt;If Hive and hive.server2.enable.doAs is enabled, will run the queries as service account, but impersonate the currently logged on user via hive.server2.proxy.user property.
     *
     * @return impersonateUser
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IMPERSONATE_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getImpersonateUser() {
        return impersonateUser;
    }


    @JsonProperty(JSON_PROPERTY_IMPERSONATE_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setImpersonateUser(Boolean impersonateUser) {
        this.impersonateUser = impersonateUser;
    }


    public DatabaseRestApiPut isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public DatabaseRestApiPut maskedEncryptedExtra(String maskedEncryptedExtra) {

        this.maskedEncryptedExtra = maskedEncryptedExtra;
        return this;
    }

    /**
     * &lt;p&gt;JSON string containing additional connection configuration.&lt;br&gt;This is used to provide connection information for systems like Hive, Presto, and BigQuery, which do not conform to the username:password syntax normally used by SQLAlchemy.&lt;/p&gt;
     *
     * @return maskedEncryptedExtra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MASKED_ENCRYPTED_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMaskedEncryptedExtra() {
        return maskedEncryptedExtra;
    }


    @JsonProperty(JSON_PROPERTY_MASKED_ENCRYPTED_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMaskedEncryptedExtra(String maskedEncryptedExtra) {
        this.maskedEncryptedExtra = maskedEncryptedExtra;
    }


    public DatabaseRestApiPut parameters(Map<String, Object> parameters) {

        this.parameters = parameters;
        return this;
    }

    public DatabaseRestApiPut putParametersItem(String key, Object parametersItem) {
        if (this.parameters == null) {
            this.parameters = new HashMap<>();
        }
        this.parameters.put(key, parametersItem);
        return this;
    }

    /**
     * DB-specific parameters for configuration
     *
     * @return parameters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMETERS)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)

    public Map<String, Object> getParameters() {
        return parameters;
    }


    @JsonProperty(JSON_PROPERTY_PARAMETERS)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }


    public DatabaseRestApiPut serverCert(String serverCert) {

        this.serverCert = serverCert;
        return this;
    }

    /**
     * &lt;p&gt;Optional CA_BUNDLE contents to validate HTTPS requests. Only available on certain database engines.&lt;/p&gt;
     *
     * @return serverCert
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SERVER_CERT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getServerCert() {
        return serverCert;
    }


    @JsonProperty(JSON_PROPERTY_SERVER_CERT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setServerCert(String serverCert) {
        this.serverCert = serverCert;
    }


    public DatabaseRestApiPut sqlalchemyUri(String sqlalchemyUri) {

        this.sqlalchemyUri = sqlalchemyUri;
        return this;
    }

    /**
     * &lt;p&gt;Refer to the &lt;a href&#x3D;\&quot;https://docs.sqlalchemy.org/en/rel_1_2/core/engines.html#database-urls\&quot; rel&#x3D;\&quot;noopener noreferrer\&quot;&gt;SqlAlchemy docs&lt;/a&gt; for more information on how to structure your URI.&lt;/p&gt;
     *
     * @return sqlalchemyUri
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQLALCHEMY_URI)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSqlalchemyUri() {
        return sqlalchemyUri;
    }


    @JsonProperty(JSON_PROPERTY_SQLALCHEMY_URI)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSqlalchemyUri(String sqlalchemyUri) {
        this.sqlalchemyUri = sqlalchemyUri;
    }


    public DatabaseRestApiPut sshTunnel(DatabaseSSHTunnel sshTunnel) {

        this.sshTunnel = sshTunnel;
        return this;
    }

    /**
     * Get sshTunnel
     *
     * @return sshTunnel
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SSH_TUNNEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DatabaseSSHTunnel getSshTunnel() {
        return sshTunnel;
    }


    @JsonProperty(JSON_PROPERTY_SSH_TUNNEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSshTunnel(DatabaseSSHTunnel sshTunnel) {
        this.sshTunnel = sshTunnel;
    }


    public DatabaseRestApiPut uuid(String uuid) {

        this.uuid = uuid;
        return this;
    }

    /**
     * Get uuid
     *
     * @return uuid
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUuid() {
        return uuid;
    }


    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseRestApiPut databaseRestApiPut = (DatabaseRestApiPut) o;
        return Objects.equals(this.allowCtas, databaseRestApiPut.allowCtas) &&
            Objects.equals(this.allowCvas, databaseRestApiPut.allowCvas) &&
            Objects.equals(this.allowDml, databaseRestApiPut.allowDml) &&
            Objects.equals(this.allowFileUpload, databaseRestApiPut.allowFileUpload) &&
            Objects.equals(this.allowRunAsync, databaseRestApiPut.allowRunAsync) &&
            Objects.equals(this.cacheTimeout, databaseRestApiPut.cacheTimeout) &&
            Objects.equals(this.configurationMethod, databaseRestApiPut.configurationMethod) &&
            Objects.equals(this.databaseName, databaseRestApiPut.databaseName) &&
            Objects.equals(this.driver, databaseRestApiPut.driver) &&
            Objects.equals(this.engine, databaseRestApiPut.engine) &&
            Objects.equals(this.exposeInSqllab, databaseRestApiPut.exposeInSqllab) &&
            Objects.equals(this.externalUrl, databaseRestApiPut.externalUrl) &&
            Objects.equals(this.extra, databaseRestApiPut.extra) &&
            Objects.equals(this.forceCtasSchema, databaseRestApiPut.forceCtasSchema) &&
            Objects.equals(this.impersonateUser, databaseRestApiPut.impersonateUser) &&
            Objects.equals(this.isManagedExternally, databaseRestApiPut.isManagedExternally) &&
            Objects.equals(this.maskedEncryptedExtra, databaseRestApiPut.maskedEncryptedExtra) &&
            Objects.equals(this.parameters, databaseRestApiPut.parameters) &&
            Objects.equals(this.serverCert, databaseRestApiPut.serverCert) &&
            Objects.equals(this.sqlalchemyUri, databaseRestApiPut.sqlalchemyUri) &&
            Objects.equals(this.sshTunnel, databaseRestApiPut.sshTunnel) &&
            Objects.equals(this.uuid, databaseRestApiPut.uuid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(allowCtas, allowCvas, allowDml, allowFileUpload, allowRunAsync, cacheTimeout, configurationMethod, databaseName, driver, engine, exposeInSqllab, externalUrl, extra, forceCtasSchema, impersonateUser, isManagedExternally, maskedEncryptedExtra, parameters, serverCert, sqlalchemyUri, sshTunnel, uuid);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseRestApiPut {\n");
        sb.append("    allowCtas: ").append(toIndentedString(allowCtas)).append("\n");
        sb.append("    allowCvas: ").append(toIndentedString(allowCvas)).append("\n");
        sb.append("    allowDml: ").append(toIndentedString(allowDml)).append("\n");
        sb.append("    allowFileUpload: ").append(toIndentedString(allowFileUpload)).append("\n");
        sb.append("    allowRunAsync: ").append(toIndentedString(allowRunAsync)).append("\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    configurationMethod: ").append(toIndentedString(configurationMethod)).append("\n");
        sb.append("    databaseName: ").append(toIndentedString(databaseName)).append("\n");
        sb.append("    driver: ").append(toIndentedString(driver)).append("\n");
        sb.append("    engine: ").append(toIndentedString(engine)).append("\n");
        sb.append("    exposeInSqllab: ").append(toIndentedString(exposeInSqllab)).append("\n");
        sb.append("    externalUrl: ").append(toIndentedString(externalUrl)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    forceCtasSchema: ").append(toIndentedString(forceCtasSchema)).append("\n");
        sb.append("    impersonateUser: ").append(toIndentedString(impersonateUser)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    maskedEncryptedExtra: ").append(toIndentedString(maskedEncryptedExtra)).append("\n");
        sb.append("    parameters: ").append(toIndentedString(parameters)).append("\n");
        sb.append("    serverCert: ").append(toIndentedString(serverCert)).append("\n");
        sb.append("    sqlalchemyUri: ").append(toIndentedString(sqlalchemyUri)).append("\n");
        sb.append("    sshTunnel: ").append(toIndentedString(sshTunnel)).append("\n");
        sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

