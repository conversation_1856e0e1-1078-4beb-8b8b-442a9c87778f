/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;
import java.util.UUID;

/**
 * DatabaseRestApiGet
 */
@JsonPropertyOrder({
    DatabaseRestApiGet.JSON_PROPERTY_ALLOW_CTAS,
    DatabaseRestApiGet.JSON_PROPERTY_ALLOW_CVAS,
    DatabaseRestApiGet.JSON_PROPERTY_ALLOW_DML,
    DatabaseRestApiGet.JSON_PROPERTY_ALLOW_FILE_UPLOAD,
    DatabaseRestApiGet.JSON_PROPERTY_ALLOW_RUN_ASYNC,
    DatabaseRestApiGet.JSON_PROPERTY_BACKEND,
    DatabaseRestApiGet.JSON_PROPERTY_CACHE_TIMEOUT,
    DatabaseRestApiGet.JSON_PROPERTY_CONFIGURATION_METHOD,
    DatabaseRestApiGet.JSON_PROPERTY_DATABASE_NAME,
    DatabaseRestApiGet.JSON_PROPERTY_DRIVER,
    DatabaseRestApiGet.JSON_PROPERTY_ENGINE_INFORMATION,
    DatabaseRestApiGet.JSON_PROPERTY_EXPOSE_IN_SQLLAB,
    DatabaseRestApiGet.JSON_PROPERTY_FORCE_CTAS_SCHEMA,
    DatabaseRestApiGet.JSON_PROPERTY_ID,
    DatabaseRestApiGet.JSON_PROPERTY_IMPERSONATE_USER,
    DatabaseRestApiGet.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    DatabaseRestApiGet.JSON_PROPERTY_UUID
})
@JsonTypeName("DatabaseRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseRestApiGet {
    public static final String JSON_PROPERTY_ALLOW_CTAS = "allow_ctas";
    private Boolean allowCtas;

    public static final String JSON_PROPERTY_ALLOW_CVAS = "allow_cvas";
    private Boolean allowCvas;

    public static final String JSON_PROPERTY_ALLOW_DML = "allow_dml";
    private Boolean allowDml;

    public static final String JSON_PROPERTY_ALLOW_FILE_UPLOAD = "allow_file_upload";
    private Boolean allowFileUpload;

    public static final String JSON_PROPERTY_ALLOW_RUN_ASYNC = "allow_run_async";
    private Boolean allowRunAsync;

    public static final String JSON_PROPERTY_BACKEND = "backend";
    private Object backend = null;

    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CONFIGURATION_METHOD = "configuration_method";
    private String configurationMethod;

    public static final String JSON_PROPERTY_DATABASE_NAME = "database_name";
    private String databaseName;

    public static final String JSON_PROPERTY_DRIVER = "driver";
    private Object driver = null;

    public static final String JSON_PROPERTY_ENGINE_INFORMATION = "engine_information";
    private Object engineInformation = null;

    public static final String JSON_PROPERTY_EXPOSE_IN_SQLLAB = "expose_in_sqllab";
    private Boolean exposeInSqllab;

    public static final String JSON_PROPERTY_FORCE_CTAS_SCHEMA = "force_ctas_schema";
    private String forceCtasSchema;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IMPERSONATE_USER = "impersonate_user";
    private Boolean impersonateUser;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_UUID = "uuid";
    private UUID uuid;

    public DatabaseRestApiGet() {
    }

    @JsonCreator
    public DatabaseRestApiGet(
        @JsonProperty(JSON_PROPERTY_BACKEND) Object backend,
        @JsonProperty(JSON_PROPERTY_DRIVER) Object driver,
        @JsonProperty(JSON_PROPERTY_ENGINE_INFORMATION) Object engineInformation
    ) {
        this();
        this.backend = backend;
        this.driver = driver;
        this.engineInformation = engineInformation;
    }

    public DatabaseRestApiGet allowCtas(Boolean allowCtas) {

        this.allowCtas = allowCtas;
        return this;
    }

    /**
     * Get allowCtas
     *
     * @return allowCtas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowCtas() {
        return allowCtas;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowCtas(Boolean allowCtas) {
        this.allowCtas = allowCtas;
    }


    public DatabaseRestApiGet allowCvas(Boolean allowCvas) {

        this.allowCvas = allowCvas;
        return this;
    }

    /**
     * Get allowCvas
     *
     * @return allowCvas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_CVAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowCvas() {
        return allowCvas;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_CVAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowCvas(Boolean allowCvas) {
        this.allowCvas = allowCvas;
    }


    public DatabaseRestApiGet allowDml(Boolean allowDml) {

        this.allowDml = allowDml;
        return this;
    }

    /**
     * Get allowDml
     *
     * @return allowDml
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_DML)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowDml() {
        return allowDml;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_DML)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowDml(Boolean allowDml) {
        this.allowDml = allowDml;
    }


    public DatabaseRestApiGet allowFileUpload(Boolean allowFileUpload) {

        this.allowFileUpload = allowFileUpload;
        return this;
    }

    /**
     * Get allowFileUpload
     *
     * @return allowFileUpload
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_FILE_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowFileUpload() {
        return allowFileUpload;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_FILE_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowFileUpload(Boolean allowFileUpload) {
        this.allowFileUpload = allowFileUpload;
    }


    public DatabaseRestApiGet allowRunAsync(Boolean allowRunAsync) {

        this.allowRunAsync = allowRunAsync;
        return this;
    }

    /**
     * Get allowRunAsync
     *
     * @return allowRunAsync
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOW_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowRunAsync() {
        return allowRunAsync;
    }


    @JsonProperty(JSON_PROPERTY_ALLOW_RUN_ASYNC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowRunAsync(Boolean allowRunAsync) {
        this.allowRunAsync = allowRunAsync;
    }


    /**
     * Get backend
     *
     * @return backend
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_BACKEND)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getBackend() {
        return backend;
    }


    public DatabaseRestApiGet cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Get cacheTimeout
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public DatabaseRestApiGet configurationMethod(String configurationMethod) {

        this.configurationMethod = configurationMethod;
        return this;
    }

    /**
     * Get configurationMethod
     *
     * @return configurationMethod
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CONFIGURATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getConfigurationMethod() {
        return configurationMethod;
    }


    @JsonProperty(JSON_PROPERTY_CONFIGURATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setConfigurationMethod(String configurationMethod) {
        this.configurationMethod = configurationMethod;
    }


    public DatabaseRestApiGet databaseName(String databaseName) {

        this.databaseName = databaseName;
        return this;
    }

    /**
     * Get databaseName
     *
     * @return databaseName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getDatabaseName() {
        return databaseName;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }


    /**
     * Get driver
     *
     * @return driver
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DRIVER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDriver() {
        return driver;
    }


    /**
     * Get engineInformation
     *
     * @return engineInformation
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ENGINE_INFORMATION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getEngineInformation() {
        return engineInformation;
    }


    public DatabaseRestApiGet exposeInSqllab(Boolean exposeInSqllab) {

        this.exposeInSqllab = exposeInSqllab;
        return this;
    }

    /**
     * Get exposeInSqllab
     *
     * @return exposeInSqllab
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPOSE_IN_SQLLAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getExposeInSqllab() {
        return exposeInSqllab;
    }


    @JsonProperty(JSON_PROPERTY_EXPOSE_IN_SQLLAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExposeInSqllab(Boolean exposeInSqllab) {
        this.exposeInSqllab = exposeInSqllab;
    }


    public DatabaseRestApiGet forceCtasSchema(String forceCtasSchema) {

        this.forceCtasSchema = forceCtasSchema;
        return this;
    }

    /**
     * Get forceCtasSchema
     *
     * @return forceCtasSchema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORCE_CTAS_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getForceCtasSchema() {
        return forceCtasSchema;
    }


    @JsonProperty(JSON_PROPERTY_FORCE_CTAS_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setForceCtasSchema(String forceCtasSchema) {
        this.forceCtasSchema = forceCtasSchema;
    }


    public DatabaseRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DatabaseRestApiGet impersonateUser(Boolean impersonateUser) {

        this.impersonateUser = impersonateUser;
        return this;
    }

    /**
     * Get impersonateUser
     *
     * @return impersonateUser
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IMPERSONATE_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getImpersonateUser() {
        return impersonateUser;
    }


    @JsonProperty(JSON_PROPERTY_IMPERSONATE_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setImpersonateUser(Boolean impersonateUser) {
        this.impersonateUser = impersonateUser;
    }


    public DatabaseRestApiGet isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public DatabaseRestApiGet uuid(UUID uuid) {

        this.uuid = uuid;
        return this;
    }

    /**
     * Get uuid
     *
     * @return uuid
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public UUID getUuid() {
        return uuid;
    }


    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUuid(UUID uuid) {
        this.uuid = uuid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseRestApiGet databaseRestApiGet = (DatabaseRestApiGet) o;
        return Objects.equals(this.allowCtas, databaseRestApiGet.allowCtas) &&
            Objects.equals(this.allowCvas, databaseRestApiGet.allowCvas) &&
            Objects.equals(this.allowDml, databaseRestApiGet.allowDml) &&
            Objects.equals(this.allowFileUpload, databaseRestApiGet.allowFileUpload) &&
            Objects.equals(this.allowRunAsync, databaseRestApiGet.allowRunAsync) &&
            Objects.equals(this.backend, databaseRestApiGet.backend) &&
            Objects.equals(this.cacheTimeout, databaseRestApiGet.cacheTimeout) &&
            Objects.equals(this.configurationMethod, databaseRestApiGet.configurationMethod) &&
            Objects.equals(this.databaseName, databaseRestApiGet.databaseName) &&
            Objects.equals(this.driver, databaseRestApiGet.driver) &&
            Objects.equals(this.engineInformation, databaseRestApiGet.engineInformation) &&
            Objects.equals(this.exposeInSqllab, databaseRestApiGet.exposeInSqllab) &&
            Objects.equals(this.forceCtasSchema, databaseRestApiGet.forceCtasSchema) &&
            Objects.equals(this.id, databaseRestApiGet.id) &&
            Objects.equals(this.impersonateUser, databaseRestApiGet.impersonateUser) &&
            Objects.equals(this.isManagedExternally, databaseRestApiGet.isManagedExternally) &&
            Objects.equals(this.uuid, databaseRestApiGet.uuid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(allowCtas, allowCvas, allowDml, allowFileUpload, allowRunAsync, backend, cacheTimeout, configurationMethod, databaseName, driver, engineInformation, exposeInSqllab, forceCtasSchema, id, impersonateUser, isManagedExternally, uuid);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseRestApiGet {\n");
        sb.append("    allowCtas: ").append(toIndentedString(allowCtas)).append("\n");
        sb.append("    allowCvas: ").append(toIndentedString(allowCvas)).append("\n");
        sb.append("    allowDml: ").append(toIndentedString(allowDml)).append("\n");
        sb.append("    allowFileUpload: ").append(toIndentedString(allowFileUpload)).append("\n");
        sb.append("    allowRunAsync: ").append(toIndentedString(allowRunAsync)).append("\n");
        sb.append("    backend: ").append(toIndentedString(backend)).append("\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    configurationMethod: ").append(toIndentedString(configurationMethod)).append("\n");
        sb.append("    databaseName: ").append(toIndentedString(databaseName)).append("\n");
        sb.append("    driver: ").append(toIndentedString(driver)).append("\n");
        sb.append("    engineInformation: ").append(toIndentedString(engineInformation)).append("\n");
        sb.append("    exposeInSqllab: ").append(toIndentedString(exposeInSqllab)).append("\n");
        sb.append("    forceCtasSchema: ").append(toIndentedString(forceCtasSchema)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    impersonateUser: ").append(toIndentedString(impersonateUser)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

