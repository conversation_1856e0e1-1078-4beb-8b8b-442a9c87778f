/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * FilterSetRestApiPost
 */
@JsonPropertyOrder({
    FilterSetRestApiPost.JSON_PROPERTY_DESCRIPTION,
    FilterSetRestApiPost.JSON_PROPERTY_JSON_METADATA,
    FilterSetRestApiPost.JSON_PROPERTY_NAME,
    FilterSetRestApiPost.JSON_PROPERTY_OWNER_ID,
    FilterSetRestApiPost.JSON_PROPERTY_OWNER_TYPE
})
@JsonTypeName("FilterSetRestApi.post")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class FilterSetRestApiPost {
    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_JSON_METADATA = "json_metadata";
    private String jsonMetadata;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OWNER_ID = "owner_id";
    private Integer ownerId;

    /**
     * Gets or Sets ownerType
     */
    public enum OwnerTypeEnum {
        USER("User"),

        DASHBOARD("Dashboard");

        private String value;

        OwnerTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static OwnerTypeEnum fromValue(String value) {
            for (OwnerTypeEnum b : OwnerTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_OWNER_TYPE = "owner_type";
    private OwnerTypeEnum ownerType;

    public FilterSetRestApiPost() {
    }

    public FilterSetRestApiPost description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public FilterSetRestApiPost jsonMetadata(String jsonMetadata) {

        this.jsonMetadata = jsonMetadata;
        return this;
    }

    /**
     * Get jsonMetadata
     *
     * @return jsonMetadata
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getJsonMetadata() {
        return jsonMetadata;
    }


    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setJsonMetadata(String jsonMetadata) {
        this.jsonMetadata = jsonMetadata;
    }


    public FilterSetRestApiPost name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setName(String name) {
        this.name = name;
    }


    public FilterSetRestApiPost ownerId(Integer ownerId) {

        this.ownerId = ownerId;
        return this;
    }

    /**
     * Get ownerId
     *
     * @return ownerId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getOwnerId() {
        return ownerId;
    }


    @JsonProperty(JSON_PROPERTY_OWNER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }


    public FilterSetRestApiPost ownerType(OwnerTypeEnum ownerType) {

        this.ownerType = ownerType;
        return this;
    }

    /**
     * Get ownerType
     *
     * @return ownerType
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_OWNER_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public OwnerTypeEnum getOwnerType() {
        return ownerType;
    }


    @JsonProperty(JSON_PROPERTY_OWNER_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setOwnerType(OwnerTypeEnum ownerType) {
        this.ownerType = ownerType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FilterSetRestApiPost filterSetRestApiPost = (FilterSetRestApiPost) o;
        return Objects.equals(this.description, filterSetRestApiPost.description) &&
            Objects.equals(this.jsonMetadata, filterSetRestApiPost.jsonMetadata) &&
            Objects.equals(this.name, filterSetRestApiPost.name) &&
            Objects.equals(this.ownerId, filterSetRestApiPost.ownerId) &&
            Objects.equals(this.ownerType, filterSetRestApiPost.ownerType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(description, jsonMetadata, name, ownerId, ownerType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class FilterSetRestApiPost {\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    jsonMetadata: ").append(toIndentedString(jsonMetadata)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    ownerId: ").append(toIndentedString(ownerId)).append("\n");
        sb.append("    ownerType: ").append(toIndentedString(ownerType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

