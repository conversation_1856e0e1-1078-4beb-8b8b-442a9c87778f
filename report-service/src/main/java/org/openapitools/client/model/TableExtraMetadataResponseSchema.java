/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * TableExtraMetadataResponseSchema
 */
@JsonPropertyOrder({
    TableExtraMetadataResponseSchema.JSON_PROPERTY_CLUSTERING,
    TableExtraMetadataResponseSchema.JSON_PROPERTY_METADATA,
    TableExtraMetadataResponseSchema.JSON_PROPERTY_PARTITIONS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TableExtraMetadataResponseSchema {
    public static final String JSON_PROPERTY_CLUSTERING = "clustering";
    private Object clustering;

    public static final String JSON_PROPERTY_METADATA = "metadata";
    private Object metadata;

    public static final String JSON_PROPERTY_PARTITIONS = "partitions";
    private Object partitions;

    public TableExtraMetadataResponseSchema() {
    }

    public TableExtraMetadataResponseSchema clustering(Object clustering) {

        this.clustering = clustering;
        return this;
    }

    /**
     * Get clustering
     *
     * @return clustering
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CLUSTERING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getClustering() {
        return clustering;
    }


    @JsonProperty(JSON_PROPERTY_CLUSTERING)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setClustering(Object clustering) {
        this.clustering = clustering;
    }


    public TableExtraMetadataResponseSchema metadata(Object metadata) {

        this.metadata = metadata;
        return this;
    }

    /**
     * Get metadata
     *
     * @return metadata
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getMetadata() {
        return metadata;
    }


    @JsonProperty(JSON_PROPERTY_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMetadata(Object metadata) {
        this.metadata = metadata;
    }


    public TableExtraMetadataResponseSchema partitions(Object partitions) {

        this.partitions = partitions;
        return this;
    }

    /**
     * Get partitions
     *
     * @return partitions
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARTITIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getPartitions() {
        return partitions;
    }


    @JsonProperty(JSON_PROPERTY_PARTITIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPartitions(Object partitions) {
        this.partitions = partitions;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TableExtraMetadataResponseSchema tableExtraMetadataResponseSchema = (TableExtraMetadataResponseSchema) o;
        return Objects.equals(this.clustering, tableExtraMetadataResponseSchema.clustering) &&
            Objects.equals(this.metadata, tableExtraMetadataResponseSchema.metadata) &&
            Objects.equals(this.partitions, tableExtraMetadataResponseSchema.partitions);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clustering, metadata, partitions);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TableExtraMetadataResponseSchema {\n");
        sb.append("    clustering: ").append(toIndentedString(clustering)).append("\n");
        sb.append("    metadata: ").append(toIndentedString(metadata)).append("\n");
        sb.append("    partitions: ").append(toIndentedString(partitions)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

