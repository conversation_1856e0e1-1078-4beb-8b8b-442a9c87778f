/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TableMetadataForeignKeysIndexesResponse
 */
@JsonPropertyOrder({
    TableMetadataForeignKeysIndexesResponse.JSON_PROPERTY_COLUMN_NAMES,
    TableMetadataForeignKeysIndexesResponse.JSON_PROPERTY_NAME,
    TableMetadataForeignKeysIndexesResponse.JSON_PROPERTY_OPTIONS,
    TableMetadataForeignKeysIndexesResponse.JSON_PROPERTY_REFERRED_COLUMNS,
    TableMetadataForeignKeysIndexesResponse.JSON_PROPERTY_REFERRED_SCHEMA,
    TableMetadataForeignKeysIndexesResponse.JSON_PROPERTY_REFERRED_TABLE,
    TableMetadataForeignKeysIndexesResponse.JSON_PROPERTY_TYPE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TableMetadataForeignKeysIndexesResponse {
    public static final String JSON_PROPERTY_COLUMN_NAMES = "column_names";
    private List<String> columnNames;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OPTIONS = "options";
    private TableMetadataOptionsResponse options;

    public static final String JSON_PROPERTY_REFERRED_COLUMNS = "referred_columns";
    private List<String> referredColumns;

    public static final String JSON_PROPERTY_REFERRED_SCHEMA = "referred_schema";
    private String referredSchema;

    public static final String JSON_PROPERTY_REFERRED_TABLE = "referred_table";
    private String referredTable;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public TableMetadataForeignKeysIndexesResponse() {
    }

    public TableMetadataForeignKeysIndexesResponse columnNames(List<String> columnNames) {

        this.columnNames = columnNames;
        return this;
    }

    public TableMetadataForeignKeysIndexesResponse addColumnNamesItem(String columnNamesItem) {
        if (this.columnNames == null) {
            this.columnNames = new ArrayList<>();
        }
        this.columnNames.add(columnNamesItem);
        return this;
    }

    /**
     * Get columnNames
     *
     * @return columnNames
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_NAMES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getColumnNames() {
        return columnNames;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_NAMES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnNames(List<String> columnNames) {
        this.columnNames = columnNames;
    }


    public TableMetadataForeignKeysIndexesResponse name(String name) {

        this.name = name;
        return this;
    }

    /**
     * The name of the foreign key or index
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public TableMetadataForeignKeysIndexesResponse options(TableMetadataOptionsResponse options) {

        this.options = options;
        return this;
    }

    /**
     * Get options
     *
     * @return options
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OPTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public TableMetadataOptionsResponse getOptions() {
        return options;
    }


    @JsonProperty(JSON_PROPERTY_OPTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOptions(TableMetadataOptionsResponse options) {
        this.options = options;
    }


    public TableMetadataForeignKeysIndexesResponse referredColumns(List<String> referredColumns) {

        this.referredColumns = referredColumns;
        return this;
    }

    public TableMetadataForeignKeysIndexesResponse addReferredColumnsItem(String referredColumnsItem) {
        if (this.referredColumns == null) {
            this.referredColumns = new ArrayList<>();
        }
        this.referredColumns.add(referredColumnsItem);
        return this;
    }

    /**
     * Get referredColumns
     *
     * @return referredColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_REFERRED_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getReferredColumns() {
        return referredColumns;
    }


    @JsonProperty(JSON_PROPERTY_REFERRED_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setReferredColumns(List<String> referredColumns) {
        this.referredColumns = referredColumns;
    }


    public TableMetadataForeignKeysIndexesResponse referredSchema(String referredSchema) {

        this.referredSchema = referredSchema;
        return this;
    }

    /**
     * Get referredSchema
     *
     * @return referredSchema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_REFERRED_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getReferredSchema() {
        return referredSchema;
    }


    @JsonProperty(JSON_PROPERTY_REFERRED_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setReferredSchema(String referredSchema) {
        this.referredSchema = referredSchema;
    }


    public TableMetadataForeignKeysIndexesResponse referredTable(String referredTable) {

        this.referredTable = referredTable;
        return this;
    }

    /**
     * Get referredTable
     *
     * @return referredTable
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_REFERRED_TABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getReferredTable() {
        return referredTable;
    }


    @JsonProperty(JSON_PROPERTY_REFERRED_TABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setReferredTable(String referredTable) {
        this.referredTable = referredTable;
    }


    public TableMetadataForeignKeysIndexesResponse type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Get type
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TableMetadataForeignKeysIndexesResponse tableMetadataForeignKeysIndexesResponse = (TableMetadataForeignKeysIndexesResponse) o;
        return Objects.equals(this.columnNames, tableMetadataForeignKeysIndexesResponse.columnNames) &&
            Objects.equals(this.name, tableMetadataForeignKeysIndexesResponse.name) &&
            Objects.equals(this.options, tableMetadataForeignKeysIndexesResponse.options) &&
            Objects.equals(this.referredColumns, tableMetadataForeignKeysIndexesResponse.referredColumns) &&
            Objects.equals(this.referredSchema, tableMetadataForeignKeysIndexesResponse.referredSchema) &&
            Objects.equals(this.referredTable, tableMetadataForeignKeysIndexesResponse.referredTable) &&
            Objects.equals(this.type, tableMetadataForeignKeysIndexesResponse.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columnNames, name, options, referredColumns, referredSchema, referredTable, type);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TableMetadataForeignKeysIndexesResponse {\n");
        sb.append("    columnNames: ").append(toIndentedString(columnNames)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    options: ").append(toIndentedString(options)).append("\n");
        sb.append("    referredColumns: ").append(toIndentedString(referredColumns)).append("\n");
        sb.append("    referredSchema: ").append(toIndentedString(referredSchema)).append("\n");
        sb.append("    referredTable: ").append(toIndentedString(referredTable)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

