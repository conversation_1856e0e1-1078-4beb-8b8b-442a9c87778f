/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartFavStarResponseResult
 */
@JsonPropertyOrder({
    ChartFavStarResponseResult.JSON_PROPERTY_ID,
    ChartFavStarResponseResult.JSON_PROPERTY_VALUE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartFavStarResponseResult {
    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_VALUE = "value";
    private Boolean value;

    public ChartFavStarResponseResult() {
    }

    public ChartFavStarResponseResult id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * The Chart id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ChartFavStarResponseResult value(Boolean value) {

        this.value = value;
        return this;
    }

    /**
     * The FaveStar value
     *
     * @return value
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getValue() {
        return value;
    }


    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValue(Boolean value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartFavStarResponseResult chartFavStarResponseResult = (ChartFavStarResponseResult) o;
        return Objects.equals(this.id, chartFavStarResponseResult.id) &&
            Objects.equals(this.value, chartFavStarResponseResult.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, value);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartFavStarResponseResult {\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    value: ").append(toIndentedString(value)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

