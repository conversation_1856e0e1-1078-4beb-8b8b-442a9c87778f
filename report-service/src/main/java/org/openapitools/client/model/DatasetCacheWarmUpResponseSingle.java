/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * DatasetCacheWarmUpResponseSingle
 */
@JsonPropertyOrder({
    DatasetCacheWarmUpResponseSingle.JSON_PROPERTY_CHART_ID,
    DatasetCacheWarmUpResponseSingle.JSON_PROPERTY_VIZ_ERROR,
    DatasetCacheWarmUpResponseSingle.JSON_PROPERTY_VIZ_STATUS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetCacheWarmUpResponseSingle {
    public static final String JSON_PROPERTY_CHART_ID = "chart_id";
    private Integer chartId;

    public static final String JSON_PROPERTY_VIZ_ERROR = "viz_error";
    private String vizError;

    public static final String JSON_PROPERTY_VIZ_STATUS = "viz_status";
    private String vizStatus;

    public DatasetCacheWarmUpResponseSingle() {
    }

    public DatasetCacheWarmUpResponseSingle chartId(Integer chartId) {

        this.chartId = chartId;
        return this;
    }

    /**
     * The ID of the chart the status belongs to
     *
     * @return chartId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHART_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getChartId() {
        return chartId;
    }


    @JsonProperty(JSON_PROPERTY_CHART_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChartId(Integer chartId) {
        this.chartId = chartId;
    }


    public DatasetCacheWarmUpResponseSingle vizError(String vizError) {

        this.vizError = vizError;
        return this;
    }

    /**
     * Error that occurred when warming cache for chart
     *
     * @return vizError
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VIZ_ERROR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVizError() {
        return vizError;
    }


    @JsonProperty(JSON_PROPERTY_VIZ_ERROR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVizError(String vizError) {
        this.vizError = vizError;
    }


    public DatasetCacheWarmUpResponseSingle vizStatus(String vizStatus) {

        this.vizStatus = vizStatus;
        return this;
    }

    /**
     * Status of the underlying query for the viz
     *
     * @return vizStatus
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VIZ_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVizStatus() {
        return vizStatus;
    }


    @JsonProperty(JSON_PROPERTY_VIZ_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVizStatus(String vizStatus) {
        this.vizStatus = vizStatus;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetCacheWarmUpResponseSingle datasetCacheWarmUpResponseSingle = (DatasetCacheWarmUpResponseSingle) o;
        return Objects.equals(this.chartId, datasetCacheWarmUpResponseSingle.chartId) &&
            Objects.equals(this.vizError, datasetCacheWarmUpResponseSingle.vizError) &&
            Objects.equals(this.vizStatus, datasetCacheWarmUpResponseSingle.vizStatus);
    }

    @Override
    public int hashCode() {
        return Objects.hash(chartId, vizError, vizStatus);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetCacheWarmUpResponseSingle {\n");
        sb.append("    chartId: ").append(toIndentedString(chartId)).append("\n");
        sb.append("    vizError: ").append(toIndentedString(vizError)).append("\n");
        sb.append("    vizStatus: ").append(toIndentedString(vizStatus)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

