/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;

/**
 * DatasetRestApiGet
 */
@JsonPropertyOrder({
    DatasetRestApiGet.JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM,
    DatasetRestApiGet.JSON_PROPERTY_CACHE_TIMEOUT,
    DatasetRestApiGet.JSON_PROPERTY_CHANGED_BY,
    DatasetRestApiGet.JSON_PROPERTY_CHANGED_ON,
    DatasetRestApiGet.JSON_PROPERTY_CHANGED_ON_HUMANIZED,
    DatasetRestApiGet.JSON_PROPERTY_COLUMN_FORMATS,
    DatasetRestApiGet.JSON_PROPERTY_COLUMNS,
    DatasetRestApiGet.JSON_PROPERTY_CREATED_BY,
    DatasetRestApiGet.JSON_PROPERTY_CREATED_ON,
    DatasetRestApiGet.JSON_PROPERTY_CREATED_ON_HUMANIZED,
    DatasetRestApiGet.JSON_PROPERTY_CURRENCY_FORMATS,
    DatasetRestApiGet.JSON_PROPERTY_DATABASE,
    DatasetRestApiGet.JSON_PROPERTY_DATASOURCE_NAME,
    DatasetRestApiGet.JSON_PROPERTY_DATASOURCE_TYPE,
    DatasetRestApiGet.JSON_PROPERTY_DEFAULT_ENDPOINT,
    DatasetRestApiGet.JSON_PROPERTY_DESCRIPTION,
    DatasetRestApiGet.JSON_PROPERTY_EXTRA,
    DatasetRestApiGet.JSON_PROPERTY_FETCH_VALUES_PREDICATE,
    DatasetRestApiGet.JSON_PROPERTY_FILTER_SELECT_ENABLED,
    DatasetRestApiGet.JSON_PROPERTY_GRANULARITY_SQLA,
    DatasetRestApiGet.JSON_PROPERTY_ID,
    DatasetRestApiGet.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    DatasetRestApiGet.JSON_PROPERTY_IS_SQLLAB_VIEW,
    DatasetRestApiGet.JSON_PROPERTY_KIND,
    DatasetRestApiGet.JSON_PROPERTY_MAIN_DTTM_COL,
    DatasetRestApiGet.JSON_PROPERTY_METRICS,
    DatasetRestApiGet.JSON_PROPERTY_NAME,
    DatasetRestApiGet.JSON_PROPERTY_NORMALIZE_COLUMNS,
    DatasetRestApiGet.JSON_PROPERTY_OFFSET,
    DatasetRestApiGet.JSON_PROPERTY_ORDER_BY_CHOICES,
    DatasetRestApiGet.JSON_PROPERTY_OWNERS,
    DatasetRestApiGet.JSON_PROPERTY_SCHEMA,
    DatasetRestApiGet.JSON_PROPERTY_SELECT_STAR,
    DatasetRestApiGet.JSON_PROPERTY_SQL,
    DatasetRestApiGet.JSON_PROPERTY_TABLE_NAME,
    DatasetRestApiGet.JSON_PROPERTY_TEMPLATE_PARAMS,
    DatasetRestApiGet.JSON_PROPERTY_TIME_GRAIN_SQLA,
    DatasetRestApiGet.JSON_PROPERTY_UID,
    DatasetRestApiGet.JSON_PROPERTY_URL,
    DatasetRestApiGet.JSON_PROPERTY_VERBOSE_MAP
})
@JsonTypeName("DatasetRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetRestApiGet {
    public static final String JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM = "always_filter_main_dttm";
    private Boolean alwaysFilterMainDttm;

    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private DatasetRestApiGetUser changedBy;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private LocalDateTime changedOn;

    public static final String JSON_PROPERTY_CHANGED_ON_HUMANIZED = "changed_on_humanized";
    private Object changedOnHumanized = null;

    public static final String JSON_PROPERTY_COLUMN_FORMATS = "column_formats";
    private Object columnFormats = null;

    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<DatasetRestApiGetTableColumn> columns;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private DatasetRestApiGetUser1 createdBy;

    public static final String JSON_PROPERTY_CREATED_ON = "created_on";
    private LocalDateTime createdOn;

    public static final String JSON_PROPERTY_CREATED_ON_HUMANIZED = "created_on_humanized";
    private Object createdOnHumanized = null;

    public static final String JSON_PROPERTY_CURRENCY_FORMATS = "currency_formats";
    private Object currencyFormats = null;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private DatasetRestApiGetDatabase database;

    public static final String JSON_PROPERTY_DATASOURCE_NAME = "datasource_name";
    private Object datasourceName = null;

    public static final String JSON_PROPERTY_DATASOURCE_TYPE = "datasource_type";
    private Object datasourceType = null;

    public static final String JSON_PROPERTY_DEFAULT_ENDPOINT = "default_endpoint";
    private String defaultEndpoint;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private String extra;

    public static final String JSON_PROPERTY_FETCH_VALUES_PREDICATE = "fetch_values_predicate";
    private String fetchValuesPredicate;

    public static final String JSON_PROPERTY_FILTER_SELECT_ENABLED = "filter_select_enabled";
    private Boolean filterSelectEnabled;

    public static final String JSON_PROPERTY_GRANULARITY_SQLA = "granularity_sqla";
    private Object granularitySqla = null;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_IS_SQLLAB_VIEW = "is_sqllab_view";
    private Boolean isSqllabView;

    public static final String JSON_PROPERTY_KIND = "kind";
    private Object kind = null;

    public static final String JSON_PROPERTY_MAIN_DTTM_COL = "main_dttm_col";
    private String mainDttmCol;

    public static final String JSON_PROPERTY_METRICS = "metrics";
    private List<DatasetRestApiGetSqlMetric> metrics;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name = null;

    public static final String JSON_PROPERTY_NORMALIZE_COLUMNS = "normalize_columns";
    private Boolean normalizeColumns;

    public static final String JSON_PROPERTY_OFFSET = "offset";
    private Integer offset;

    public static final String JSON_PROPERTY_ORDER_BY_CHOICES = "order_by_choices";
    private Object orderByChoices = null;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<DatasetRestApiGetUser2> owners;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SELECT_STAR = "select_star";
    private Object selectStar = null;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public static final String JSON_PROPERTY_TEMPLATE_PARAMS = "template_params";
    private String templateParams;

    public static final String JSON_PROPERTY_TIME_GRAIN_SQLA = "time_grain_sqla";
    private Object timeGrainSqla = null;

    public static final String JSON_PROPERTY_UID = "uid";
    private Object uid = null;

    public static final String JSON_PROPERTY_URL = "url";
    private Object url = null;

    public static final String JSON_PROPERTY_VERBOSE_MAP = "verbose_map";
    private Object verboseMap = null;

    public DatasetRestApiGet() {
    }

    @JsonCreator
    public DatasetRestApiGet(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_HUMANIZED) Object changedOnHumanized,
        @JsonProperty(JSON_PROPERTY_COLUMN_FORMATS) Object columnFormats,
        @JsonProperty(JSON_PROPERTY_CREATED_ON_HUMANIZED) Object createdOnHumanized,
        @JsonProperty(JSON_PROPERTY_CURRENCY_FORMATS) Object currencyFormats,
        @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME) Object datasourceName,
        @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE) Object datasourceType,
        @JsonProperty(JSON_PROPERTY_GRANULARITY_SQLA) Object granularitySqla,
        @JsonProperty(JSON_PROPERTY_KIND) Object kind,
        @JsonProperty(JSON_PROPERTY_NAME) String name,
        @JsonProperty(JSON_PROPERTY_ORDER_BY_CHOICES) Object orderByChoices,
        @JsonProperty(JSON_PROPERTY_SELECT_STAR) Object selectStar,
        @JsonProperty(JSON_PROPERTY_TIME_GRAIN_SQLA) Object timeGrainSqla,
        @JsonProperty(JSON_PROPERTY_UID) Object uid,
        @JsonProperty(JSON_PROPERTY_URL) Object url,
        @JsonProperty(JSON_PROPERTY_VERBOSE_MAP) Object verboseMap
    ) {
        this();
        this.changedOnHumanized = changedOnHumanized;
        this.columnFormats = columnFormats;
        this.createdOnHumanized = createdOnHumanized;
        this.currencyFormats = currencyFormats;
        this.datasourceName = datasourceName;
        this.datasourceType = datasourceType;
        this.granularitySqla = granularitySqla;
        this.kind = kind;
        this.name = name;
        this.orderByChoices = orderByChoices;
        this.selectStar = selectStar;
        this.timeGrainSqla = timeGrainSqla;
        this.uid = uid;
        this.url = url;
        this.verboseMap = verboseMap;
    }

    public DatasetRestApiGet alwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {

        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
        return this;
    }

    /**
     * Get alwaysFilterMainDttm
     *
     * @return alwaysFilterMainDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAlwaysFilterMainDttm() {
        return alwaysFilterMainDttm;
    }


    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAlwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {
        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
    }


    public DatasetRestApiGet cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Get cacheTimeout
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public DatasetRestApiGet changedBy(DatasetRestApiGetUser changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DatasetRestApiGetUser getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(DatasetRestApiGetUser changedBy) {
        this.changedBy = changedBy;
    }


    public DatasetRestApiGet changedOn(LocalDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public LocalDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(LocalDateTime changedOn) {
        this.changedOn = changedOn;
    }


    /**
     * Get changedOnHumanized
     *
     * @return changedOnHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnHumanized() {
        return changedOnHumanized;
    }


    /**
     * Get columnFormats
     *
     * @return columnFormats
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getColumnFormats() {
        return columnFormats;
    }


    public DatasetRestApiGet columns(List<DatasetRestApiGetTableColumn> columns) {

        this.columns = columns;
        return this;
    }

    /**
     * Get columns
     *
     * @return columns
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public List<DatasetRestApiGetTableColumn> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setColumns(List<DatasetRestApiGetTableColumn> columns) {
        this.columns = columns;
    }


    public DatasetRestApiGet createdBy(DatasetRestApiGetUser1 createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DatasetRestApiGetUser1 getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(DatasetRestApiGetUser1 createdBy) {
        this.createdBy = createdBy;
    }


    public DatasetRestApiGet createdOn(LocalDateTime createdOn) {

        this.createdOn = createdOn;
        return this;
    }

    /**
     * Get createdOn
     *
     * @return createdOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public LocalDateTime getCreatedOn() {
        return createdOn;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedOn(LocalDateTime createdOn) {
        this.createdOn = createdOn;
    }


    /**
     * Get createdOnHumanized
     *
     * @return createdOnHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getCreatedOnHumanized() {
        return createdOnHumanized;
    }


    /**
     * Get currencyFormats
     *
     * @return currencyFormats
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CURRENCY_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getCurrencyFormats() {
        return currencyFormats;
    }


    public DatasetRestApiGet database(DatasetRestApiGetDatabase database) {

        this.database = database;
        return this;
    }

    /**
     * Get database
     *
     * @return database
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public DatasetRestApiGetDatabase getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabase(DatasetRestApiGetDatabase database) {
        this.database = database;
    }


    /**
     * Get datasourceName
     *
     * @return datasourceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDatasourceName() {
        return datasourceName;
    }


    /**
     * Get datasourceType
     *
     * @return datasourceType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDatasourceType() {
        return datasourceType;
    }


    public DatasetRestApiGet defaultEndpoint(String defaultEndpoint) {

        this.defaultEndpoint = defaultEndpoint;
        return this;
    }

    /**
     * Get defaultEndpoint
     *
     * @return defaultEndpoint
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDefaultEndpoint() {
        return defaultEndpoint;
    }


    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDefaultEndpoint(String defaultEndpoint) {
        this.defaultEndpoint = defaultEndpoint;
    }


    public DatasetRestApiGet description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public DatasetRestApiGet extra(String extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(String extra) {
        this.extra = extra;
    }


    public DatasetRestApiGet fetchValuesPredicate(String fetchValuesPredicate) {

        this.fetchValuesPredicate = fetchValuesPredicate;
        return this;
    }

    /**
     * Get fetchValuesPredicate
     *
     * @return fetchValuesPredicate
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getFetchValuesPredicate() {
        return fetchValuesPredicate;
    }


    @JsonProperty(JSON_PROPERTY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFetchValuesPredicate(String fetchValuesPredicate) {
        this.fetchValuesPredicate = fetchValuesPredicate;
    }


    public DatasetRestApiGet filterSelectEnabled(Boolean filterSelectEnabled) {

        this.filterSelectEnabled = filterSelectEnabled;
        return this;
    }

    /**
     * Get filterSelectEnabled
     *
     * @return filterSelectEnabled
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER_SELECT_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getFilterSelectEnabled() {
        return filterSelectEnabled;
    }


    @JsonProperty(JSON_PROPERTY_FILTER_SELECT_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterSelectEnabled(Boolean filterSelectEnabled) {
        this.filterSelectEnabled = filterSelectEnabled;
    }


    /**
     * Get granularitySqla
     *
     * @return granularitySqla
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GRANULARITY_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getGranularitySqla() {
        return granularitySqla;
    }


    public DatasetRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DatasetRestApiGet isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public DatasetRestApiGet isSqllabView(Boolean isSqllabView) {

        this.isSqllabView = isSqllabView;
        return this;
    }

    /**
     * Get isSqllabView
     *
     * @return isSqllabView
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_SQLLAB_VIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsSqllabView() {
        return isSqllabView;
    }


    @JsonProperty(JSON_PROPERTY_IS_SQLLAB_VIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsSqllabView(Boolean isSqllabView) {
        this.isSqllabView = isSqllabView;
    }


    /**
     * Get kind
     *
     * @return kind
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_KIND)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getKind() {
        return kind;
    }


    public DatasetRestApiGet mainDttmCol(String mainDttmCol) {

        this.mainDttmCol = mainDttmCol;
        return this;
    }

    /**
     * Get mainDttmCol
     *
     * @return mainDttmCol
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MAIN_DTTM_COL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMainDttmCol() {
        return mainDttmCol;
    }


    @JsonProperty(JSON_PROPERTY_MAIN_DTTM_COL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMainDttmCol(String mainDttmCol) {
        this.mainDttmCol = mainDttmCol;
    }


    public DatasetRestApiGet metrics(List<DatasetRestApiGetSqlMetric> metrics) {

        this.metrics = metrics;
        return this;
    }

    /**
     * Get metrics
     *
     * @return metrics
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public List<DatasetRestApiGetSqlMetric> getMetrics() {
        return metrics;
    }


    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setMetrics(List<DatasetRestApiGetSqlMetric> metrics) {
        this.metrics = metrics;
    }


    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    public DatasetRestApiGet normalizeColumns(Boolean normalizeColumns) {

        this.normalizeColumns = normalizeColumns;
        return this;
    }

    /**
     * Get normalizeColumns
     *
     * @return normalizeColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getNormalizeColumns() {
        return normalizeColumns;
    }


    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setNormalizeColumns(Boolean normalizeColumns) {
        this.normalizeColumns = normalizeColumns;
    }


    public DatasetRestApiGet offset(Integer offset) {

        this.offset = offset;
        return this;
    }

    /**
     * Get offset
     *
     * @return offset
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getOffset() {
        return offset;
    }


    @JsonProperty(JSON_PROPERTY_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOffset(Integer offset) {
        this.offset = offset;
    }


    /**
     * Get orderByChoices
     *
     * @return orderByChoices
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ORDER_BY_CHOICES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getOrderByChoices() {
        return orderByChoices;
    }


    public DatasetRestApiGet owners(List<DatasetRestApiGetUser2> owners) {

        this.owners = owners;
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<DatasetRestApiGetUser2> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<DatasetRestApiGetUser2> owners) {
        this.owners = owners;
    }


    public DatasetRestApiGet schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    /**
     * Get selectStar
     *
     * @return selectStar
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SELECT_STAR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getSelectStar() {
        return selectStar;
    }


    public DatasetRestApiGet sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public DatasetRestApiGet tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * Get tableName
     *
     * @return tableName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }


    public DatasetRestApiGet templateParams(String templateParams) {

        this.templateParams = templateParams;
        return this;
    }

    /**
     * Get templateParams
     *
     * @return templateParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTemplateParams() {
        return templateParams;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams;
    }


    /**
     * Get timeGrainSqla
     *
     * @return timeGrainSqla
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_GRAIN_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getTimeGrainSqla() {
        return timeGrainSqla;
    }


    /**
     * Get uid
     *
     * @return uid
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_UID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getUid() {
        return uid;
    }


    /**
     * Get url
     *
     * @return url
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getUrl() {
        return url;
    }


    /**
     * Get verboseMap
     *
     * @return verboseMap
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VERBOSE_MAP)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getVerboseMap() {
        return verboseMap;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetRestApiGet datasetRestApiGet = (DatasetRestApiGet) o;
        return Objects.equals(this.alwaysFilterMainDttm, datasetRestApiGet.alwaysFilterMainDttm) &&
            Objects.equals(this.cacheTimeout, datasetRestApiGet.cacheTimeout) &&
            Objects.equals(this.changedBy, datasetRestApiGet.changedBy) &&
            Objects.equals(this.changedOn, datasetRestApiGet.changedOn) &&
            Objects.equals(this.changedOnHumanized, datasetRestApiGet.changedOnHumanized) &&
            Objects.equals(this.columnFormats, datasetRestApiGet.columnFormats) &&
            Objects.equals(this.columns, datasetRestApiGet.columns) &&
            Objects.equals(this.createdBy, datasetRestApiGet.createdBy) &&
            Objects.equals(this.createdOn, datasetRestApiGet.createdOn) &&
            Objects.equals(this.createdOnHumanized, datasetRestApiGet.createdOnHumanized) &&
            Objects.equals(this.currencyFormats, datasetRestApiGet.currencyFormats) &&
            Objects.equals(this.database, datasetRestApiGet.database) &&
            Objects.equals(this.datasourceName, datasetRestApiGet.datasourceName) &&
            Objects.equals(this.datasourceType, datasetRestApiGet.datasourceType) &&
            Objects.equals(this.defaultEndpoint, datasetRestApiGet.defaultEndpoint) &&
            Objects.equals(this.description, datasetRestApiGet.description) &&
            Objects.equals(this.extra, datasetRestApiGet.extra) &&
            Objects.equals(this.fetchValuesPredicate, datasetRestApiGet.fetchValuesPredicate) &&
            Objects.equals(this.filterSelectEnabled, datasetRestApiGet.filterSelectEnabled) &&
            Objects.equals(this.granularitySqla, datasetRestApiGet.granularitySqla) &&
            Objects.equals(this.id, datasetRestApiGet.id) &&
            Objects.equals(this.isManagedExternally, datasetRestApiGet.isManagedExternally) &&
            Objects.equals(this.isSqllabView, datasetRestApiGet.isSqllabView) &&
            Objects.equals(this.kind, datasetRestApiGet.kind) &&
            Objects.equals(this.mainDttmCol, datasetRestApiGet.mainDttmCol) &&
            Objects.equals(this.metrics, datasetRestApiGet.metrics) &&
            Objects.equals(this.name, datasetRestApiGet.name) &&
            Objects.equals(this.normalizeColumns, datasetRestApiGet.normalizeColumns) &&
            Objects.equals(this.offset, datasetRestApiGet.offset) &&
            Objects.equals(this.orderByChoices, datasetRestApiGet.orderByChoices) &&
            Objects.equals(this.owners, datasetRestApiGet.owners) &&
            Objects.equals(this.schema, datasetRestApiGet.schema) &&
            Objects.equals(this.selectStar, datasetRestApiGet.selectStar) &&
            Objects.equals(this.sql, datasetRestApiGet.sql) &&
            Objects.equals(this.tableName, datasetRestApiGet.tableName) &&
            Objects.equals(this.templateParams, datasetRestApiGet.templateParams) &&
            Objects.equals(this.timeGrainSqla, datasetRestApiGet.timeGrainSqla) &&
            Objects.equals(this.uid, datasetRestApiGet.uid) &&
            Objects.equals(this.url, datasetRestApiGet.url) &&
            Objects.equals(this.verboseMap, datasetRestApiGet.verboseMap);
    }

    @Override
    public int hashCode() {
        return Objects.hash(alwaysFilterMainDttm, cacheTimeout, changedBy, changedOn, changedOnHumanized, columnFormats, columns, createdBy, createdOn, createdOnHumanized, currencyFormats, database, datasourceName, datasourceType, defaultEndpoint, description, extra, fetchValuesPredicate, filterSelectEnabled, granularitySqla, id, isManagedExternally, isSqllabView, kind, mainDttmCol, metrics, name, normalizeColumns, offset, orderByChoices, owners, schema, selectStar, sql, tableName, templateParams, timeGrainSqla, uid, url, verboseMap);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetRestApiGet {\n");
        sb.append("    alwaysFilterMainDttm: ").append(toIndentedString(alwaysFilterMainDttm)).append("\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    changedOnHumanized: ").append(toIndentedString(changedOnHumanized)).append("\n");
        sb.append("    columnFormats: ").append(toIndentedString(columnFormats)).append("\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
        sb.append("    createdOnHumanized: ").append(toIndentedString(createdOnHumanized)).append("\n");
        sb.append("    currencyFormats: ").append(toIndentedString(currencyFormats)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    datasourceName: ").append(toIndentedString(datasourceName)).append("\n");
        sb.append("    datasourceType: ").append(toIndentedString(datasourceType)).append("\n");
        sb.append("    defaultEndpoint: ").append(toIndentedString(defaultEndpoint)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    fetchValuesPredicate: ").append(toIndentedString(fetchValuesPredicate)).append("\n");
        sb.append("    filterSelectEnabled: ").append(toIndentedString(filterSelectEnabled)).append("\n");
        sb.append("    granularitySqla: ").append(toIndentedString(granularitySqla)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    isSqllabView: ").append(toIndentedString(isSqllabView)).append("\n");
        sb.append("    kind: ").append(toIndentedString(kind)).append("\n");
        sb.append("    mainDttmCol: ").append(toIndentedString(mainDttmCol)).append("\n");
        sb.append("    metrics: ").append(toIndentedString(metrics)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    normalizeColumns: ").append(toIndentedString(normalizeColumns)).append("\n");
        sb.append("    offset: ").append(toIndentedString(offset)).append("\n");
        sb.append("    orderByChoices: ").append(toIndentedString(orderByChoices)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    selectStar: ").append(toIndentedString(selectStar)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("    templateParams: ").append(toIndentedString(templateParams)).append("\n");
        sb.append("    timeGrainSqla: ").append(toIndentedString(timeGrainSqla)).append("\n");
        sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
        sb.append("    url: ").append(toIndentedString(url)).append("\n");
        sb.append("    verboseMap: ").append(toIndentedString(verboseMap)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

