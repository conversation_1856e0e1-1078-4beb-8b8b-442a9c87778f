/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TagRestApiPut
 */
@JsonPropertyOrder({
    TagRestApiPut.JSON_PROPERTY_DESCRIPTION,
    TagRestApiPut.JSON_PROPERTY_NAME,
    TagRestApiPut.JSON_PROPERTY_OBJECTS_TO_TAG
})
@JsonTypeName("TagRestApi.put")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TagRestApiPut {
    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OBJECTS_TO_TAG = "objects_to_tag";
    private List<Object> objectsToTag;

    public TagRestApiPut() {
    }

    public TagRestApiPut description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public TagRestApiPut name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public TagRestApiPut objectsToTag(List<Object> objectsToTag) {

        this.objectsToTag = objectsToTag;
        return this;
    }

    public TagRestApiPut addObjectsToTagItem(Object objectsToTagItem) {
        if (this.objectsToTag == null) {
            this.objectsToTag = new ArrayList<>();
        }
        this.objectsToTag.add(objectsToTagItem);
        return this;
    }

    /**
     * Get objectsToTag
     *
     * @return objectsToTag
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OBJECTS_TO_TAG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getObjectsToTag() {
        return objectsToTag;
    }


    @JsonProperty(JSON_PROPERTY_OBJECTS_TO_TAG)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setObjectsToTag(List<Object> objectsToTag) {
        this.objectsToTag = objectsToTag;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TagRestApiPut tagRestApiPut = (TagRestApiPut) o;
        return Objects.equals(this.description, tagRestApiPut.description) &&
            Objects.equals(this.name, tagRestApiPut.name) &&
            Objects.equals(this.objectsToTag, tagRestApiPut.objectsToTag);
    }

    @Override
    public int hashCode() {
        return Objects.hash(description, name, objectsToTag);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TagRestApiPut {\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    objectsToTag: ").append(toIndentedString(objectsToTag)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

