/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * CssTemplateRestApiGet
 */
@JsonPropertyOrder({
    CssTemplateRestApiGet.JSON_PROPERTY_CREATED_BY,
    CssTemplateRestApiGet.JSON_PROPERTY_CSS,
    CssTemplateRestApiGet.JSON_PROPERTY_ID,
    CssTemplateRestApiGet.JSON_PROPERTY_TEMPLATE_NAME
})
@JsonTypeName("CssTemplateRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class CssTemplateRestApiGet {
    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private CssTemplateRestApiGetUser createdBy;

    public static final String JSON_PROPERTY_CSS = "css";
    private String css;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_TEMPLATE_NAME = "template_name";
    private String templateName;

    public CssTemplateRestApiGet() {
    }

    public CssTemplateRestApiGet createdBy(CssTemplateRestApiGetUser createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public CssTemplateRestApiGetUser getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(CssTemplateRestApiGetUser createdBy) {
        this.createdBy = createdBy;
    }


    public CssTemplateRestApiGet css(String css) {

        this.css = css;
        return this;
    }

    /**
     * Get css
     *
     * @return css
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCss() {
        return css;
    }


    @JsonProperty(JSON_PROPERTY_CSS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCss(String css) {
        this.css = css;
    }


    public CssTemplateRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public CssTemplateRestApiGet templateName(String templateName) {

        this.templateName = templateName;
        return this;
    }

    /**
     * Get templateName
     *
     * @return templateName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTemplateName() {
        return templateName;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CssTemplateRestApiGet cssTemplateRestApiGet = (CssTemplateRestApiGet) o;
        return Objects.equals(this.createdBy, cssTemplateRestApiGet.createdBy) &&
            Objects.equals(this.css, cssTemplateRestApiGet.css) &&
            Objects.equals(this.id, cssTemplateRestApiGet.id) &&
            Objects.equals(this.templateName, cssTemplateRestApiGet.templateName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(createdBy, css, id, templateName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class CssTemplateRestApiGet {\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    css: ").append(toIndentedString(css)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    templateName: ").append(toIndentedString(templateName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

