/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartDataColumn
 */
@JsonPropertyOrder({
    ChartDataColumn.JSON_PROPERTY_COLUMN_NAME,
    ChartDataColumn.JSON_PROPERTY_TYPE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataColumn {
    public static final String JSON_PROPERTY_COLUMN_NAME = "column_name";
    private String columnName;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public ChartDataColumn() {
    }

    public ChartDataColumn columnName(String columnName) {

        this.columnName = columnName;
        return this;
    }

    /**
     * The name of the target column
     *
     * @return columnName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getColumnName() {
        return columnName;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnName(String columnName) {
        this.columnName = columnName;
    }


    public ChartDataColumn type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Type of target column
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataColumn chartDataColumn = (ChartDataColumn) o;
        return Objects.equals(this.columnName, chartDataColumn.columnName) &&
            Objects.equals(this.type, chartDataColumn.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columnName, type);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataColumn {\n");
        sb.append("    columnName: ").append(toIndentedString(columnName)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

