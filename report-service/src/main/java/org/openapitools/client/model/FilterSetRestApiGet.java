/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * FilterSetRestApiGet
 */
@JsonPropertyOrder({
    FilterSetRestApiGet.JSON_PROPERTY_DASHBOARD_ID,
    FilterSetRestApiGet.JSON_PROPERTY_DESCRIPTION,
    FilterSetRestApiGet.JSON_PROPERTY_ID,
    FilterSetRestApiGet.JSON_PROPERTY_NAME,
    FilterSetRestApiGet.JSON_PROPERTY_OWNER_ID,
    FilterSetRestApiGet.JSON_PROPERTY_OWNER_TYPE,
    FilterSetRestApiGet.JSON_PROPERTY_PARAMS
})
@JsonTypeName("FilterSetRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class FilterSetRestApiGet {
    public static final String JSON_PROPERTY_DASHBOARD_ID = "dashboard_id";
    private Integer dashboardId;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OWNER_ID = "owner_id";
    private Integer ownerId;

    public static final String JSON_PROPERTY_OWNER_TYPE = "owner_type";
    private String ownerType;

    public static final String JSON_PROPERTY_PARAMS = "params";
    private Object params = null;

    public FilterSetRestApiGet() {
    }

    @JsonCreator
    public FilterSetRestApiGet(
        @JsonProperty(JSON_PROPERTY_PARAMS) Object params
    ) {
        this();
        this.params = params;
    }

    public FilterSetRestApiGet dashboardId(Integer dashboardId) {

        this.dashboardId = dashboardId;
        return this;
    }

    /**
     * Get dashboardId
     *
     * @return dashboardId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDashboardId() {
        return dashboardId;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardId(Integer dashboardId) {
        this.dashboardId = dashboardId;
    }


    public FilterSetRestApiGet description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public FilterSetRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public FilterSetRestApiGet name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setName(String name) {
        this.name = name;
    }


    public FilterSetRestApiGet ownerId(Integer ownerId) {

        this.ownerId = ownerId;
        return this;
    }

    /**
     * Get ownerId
     *
     * @return ownerId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_OWNER_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getOwnerId() {
        return ownerId;
    }


    @JsonProperty(JSON_PROPERTY_OWNER_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }


    public FilterSetRestApiGet ownerType(String ownerType) {

        this.ownerType = ownerType;
        return this;
    }

    /**
     * Get ownerType
     *
     * @return ownerType
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_OWNER_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getOwnerType() {
        return ownerType;
    }


    @JsonProperty(JSON_PROPERTY_OWNER_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setOwnerType(String ownerType) {
        this.ownerType = ownerType;
    }


    /**
     * Get params
     *
     * @return params
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getParams() {
        return params;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        FilterSetRestApiGet filterSetRestApiGet = (FilterSetRestApiGet) o;
        return Objects.equals(this.dashboardId, filterSetRestApiGet.dashboardId) &&
            Objects.equals(this.description, filterSetRestApiGet.description) &&
            Objects.equals(this.id, filterSetRestApiGet.id) &&
            Objects.equals(this.name, filterSetRestApiGet.name) &&
            Objects.equals(this.ownerId, filterSetRestApiGet.ownerId) &&
            Objects.equals(this.ownerType, filterSetRestApiGet.ownerType) &&
            Objects.equals(this.params, filterSetRestApiGet.params);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dashboardId, description, id, name, ownerId, ownerType, params);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class FilterSetRestApiGet {\n");
        sb.append("    dashboardId: ").append(toIndentedString(dashboardId)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    ownerId: ").append(toIndentedString(ownerId)).append("\n");
        sb.append("    ownerType: ").append(toIndentedString(ownerType)).append("\n");
        sb.append("    params: ").append(toIndentedString(params)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

