/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * GetListSchema
 */
@JsonPropertyOrder({
    GetListSchema.JSON_PROPERTY_COLUMNS,
    GetListSchema.JSON_PROPERTY_FILTERS,
    GetListSchema.JSON_PROPERTY_KEYS,
    GetListSchema.JSON_PROPERTY_ORDER_COLUMN,
    GetListSchema.JSON_PROPERTY_ORDER_DIRECTION,
    GetListSchema.JSON_PROPERTY_PAGE,
    GetListSchema.JSON_PROPERTY_PAGE_SIZE
})
@JsonTypeName("get_list_schema")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class GetListSchema {
    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<String> columns;

    public static final String JSON_PROPERTY_FILTERS = "filters";
    private List<GetListSchemaFiltersInner> filters;

    /**
     * Gets or Sets keys
     */
    public enum KeysEnum {
        LIST_COLUMNS("list_columns"),

        ORDER_COLUMNS("order_columns"),

        LABEL_COLUMNS("label_columns"),

        DESCRIPTION_COLUMNS("description_columns"),

        LIST_TITLE("list_title"),

        NONE("none");

        private String value;

        KeysEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static KeysEnum fromValue(String value) {
            for (KeysEnum b : KeysEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_KEYS = "keys";
    private List<KeysEnum> keys;

    public static final String JSON_PROPERTY_ORDER_COLUMN = "order_column";
    private String orderColumn;

    /**
     * Gets or Sets orderDirection
     */
    public enum OrderDirectionEnum {
        ASC("asc"),

        DESC("desc");

        private String value;

        OrderDirectionEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static OrderDirectionEnum fromValue(String value) {
            for (OrderDirectionEnum b : OrderDirectionEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_ORDER_DIRECTION = "order_direction";
    private OrderDirectionEnum orderDirection;

    public static final String JSON_PROPERTY_PAGE = "page";
    private Integer page;

    public static final String JSON_PROPERTY_PAGE_SIZE = "page_size";
    private Integer pageSize;

    public GetListSchema() {
    }

    public GetListSchema columns(List<String> columns) {

        this.columns = columns;
        return this;
    }

    public GetListSchema addColumnsItem(String columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * Get columns
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<String> columns) {
        this.columns = columns;
    }


    public GetListSchema filters(List<GetListSchemaFiltersInner> filters) {

        this.filters = filters;
        return this;
    }

    public GetListSchema addFiltersItem(GetListSchemaFiltersInner filtersItem) {
        if (this.filters == null) {
            this.filters = new ArrayList<>();
        }
        this.filters.add(filtersItem);
        return this;
    }

    /**
     * Get filters
     *
     * @return filters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<GetListSchemaFiltersInner> getFilters() {
        return filters;
    }


    @JsonProperty(JSON_PROPERTY_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilters(List<GetListSchemaFiltersInner> filters) {
        this.filters = filters;
    }


    public GetListSchema keys(List<KeysEnum> keys) {

        this.keys = keys;
        return this;
    }

    public GetListSchema addKeysItem(KeysEnum keysItem) {
        if (this.keys == null) {
            this.keys = new ArrayList<>();
        }
        this.keys.add(keysItem);
        return this;
    }

    /**
     * Get keys
     *
     * @return keys
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<KeysEnum> getKeys() {
        return keys;
    }


    @JsonProperty(JSON_PROPERTY_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setKeys(List<KeysEnum> keys) {
        this.keys = keys;
    }


    public GetListSchema orderColumn(String orderColumn) {

        this.orderColumn = orderColumn;
        return this;
    }

    /**
     * Get orderColumn
     *
     * @return orderColumn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ORDER_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getOrderColumn() {
        return orderColumn;
    }


    @JsonProperty(JSON_PROPERTY_ORDER_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOrderColumn(String orderColumn) {
        this.orderColumn = orderColumn;
    }


    public GetListSchema orderDirection(OrderDirectionEnum orderDirection) {

        this.orderDirection = orderDirection;
        return this;
    }

    /**
     * Get orderDirection
     *
     * @return orderDirection
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ORDER_DIRECTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OrderDirectionEnum getOrderDirection() {
        return orderDirection;
    }


    @JsonProperty(JSON_PROPERTY_ORDER_DIRECTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOrderDirection(OrderDirectionEnum orderDirection) {
        this.orderDirection = orderDirection;
    }


    public GetListSchema page(Integer page) {

        this.page = page;
        return this;
    }

    /**
     * Get page
     *
     * @return page
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getPage() {
        return page;
    }


    @JsonProperty(JSON_PROPERTY_PAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPage(Integer page) {
        this.page = page;
    }


    public GetListSchema pageSize(Integer pageSize) {

        this.pageSize = pageSize;
        return this;
    }

    /**
     * Get pageSize
     *
     * @return pageSize
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getPageSize() {
        return pageSize;
    }


    @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetListSchema getListSchema = (GetListSchema) o;
        return Objects.equals(this.columns, getListSchema.columns) &&
            Objects.equals(this.filters, getListSchema.filters) &&
            Objects.equals(this.keys, getListSchema.keys) &&
            Objects.equals(this.orderColumn, getListSchema.orderColumn) &&
            Objects.equals(this.orderDirection, getListSchema.orderDirection) &&
            Objects.equals(this.page, getListSchema.page) &&
            Objects.equals(this.pageSize, getListSchema.pageSize);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columns, filters, keys, orderColumn, orderDirection, page, pageSize);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class GetListSchema {\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    filters: ").append(toIndentedString(filters)).append("\n");
        sb.append("    keys: ").append(toIndentedString(keys)).append("\n");
        sb.append("    orderColumn: ").append(toIndentedString(orderColumn)).append("\n");
        sb.append("    orderDirection: ").append(toIndentedString(orderDirection)).append("\n");
        sb.append("    page: ").append(toIndentedString(page)).append("\n");
        sb.append("    pageSize: ").append(toIndentedString(pageSize)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

