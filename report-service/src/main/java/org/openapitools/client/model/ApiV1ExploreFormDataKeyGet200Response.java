/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1ExploreFormDataKeyGet200Response
 */
@JsonPropertyOrder({
    ApiV1ExploreFormDataKeyGet200Response.JSON_PROPERTY_FORM_DATA
})
@JsonTypeName("_api_v1_explore_form_data__key__get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1ExploreFormDataKeyGet200Response {
    public static final String JSON_PROPERTY_FORM_DATA = "form_data";
    private String formData;

    public ApiV1ExploreFormDataKeyGet200Response() {
    }

    public ApiV1ExploreFormDataKeyGet200Response formData(String formData) {

        this.formData = formData;
        return this;
    }

    /**
     * The stored form_data
     *
     * @return formData
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getFormData() {
        return formData;
    }


    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFormData(String formData) {
        this.formData = formData;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1ExploreFormDataKeyGet200Response apiV1ExploreFormDataKeyGet200Response = (ApiV1ExploreFormDataKeyGet200Response) o;
        return Objects.equals(this.formData, apiV1ExploreFormDataKeyGet200Response.formData);
    }

    @Override
    public int hashCode() {
        return Objects.hash(formData);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1ExploreFormDataKeyGet200Response {\n");
        sb.append("    formData: ").append(toIndentedString(formData)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

