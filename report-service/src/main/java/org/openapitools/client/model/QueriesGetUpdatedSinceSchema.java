/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * QueriesGetUpdatedSinceSchema
 */
@JsonPropertyOrder({
    QueriesGetUpdatedSinceSchema.JSON_PROPERTY_LAST_UPDATED_MS
})
@JsonTypeName("queries_get_updated_since_schema")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class QueriesGetUpdatedSinceSchema {
    public static final String JSON_PROPERTY_LAST_UPDATED_MS = "last_updated_ms";
    private BigDecimal lastUpdatedMs;

    public QueriesGetUpdatedSinceSchema() {
    }

    public QueriesGetUpdatedSinceSchema lastUpdatedMs(BigDecimal lastUpdatedMs) {

        this.lastUpdatedMs = lastUpdatedMs;
        return this;
    }

    /**
     * Get lastUpdatedMs
     *
     * @return lastUpdatedMs
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_LAST_UPDATED_MS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public BigDecimal getLastUpdatedMs() {
        return lastUpdatedMs;
    }


    @JsonProperty(JSON_PROPERTY_LAST_UPDATED_MS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setLastUpdatedMs(BigDecimal lastUpdatedMs) {
        this.lastUpdatedMs = lastUpdatedMs;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        QueriesGetUpdatedSinceSchema queriesGetUpdatedSinceSchema = (QueriesGetUpdatedSinceSchema) o;
        return Objects.equals(this.lastUpdatedMs, queriesGetUpdatedSinceSchema.lastUpdatedMs);
    }

    @Override
    public int hashCode() {
        return Objects.hash(lastUpdatedMs);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class QueriesGetUpdatedSinceSchema {\n");
        sb.append("    lastUpdatedMs: ").append(toIndentedString(lastUpdatedMs)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

