/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.*;

/**
 * ImportV1DatabaseExtra
 */
@JsonPropertyOrder({
    ImportV1DatabaseExtra.JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE,
    ImportV1DatabaseExtra.JSON_PROPERTY_CANCEL_QUERY_ON_WINDOWS_UNLOAD,
    ImportV1DatabaseExtra.JSON_PROPERTY_COST_ESTIMATE_ENABLED,
    ImportV1DatabaseExtra.JSON_PROPERTY_DISABLE_DATA_PREVIEW,
    ImportV1DatabaseExtra.JSON_PROPERTY_ENGINE_PARAMS,
    ImportV1DatabaseExtra.JSON_PROPERTY_METADATA_CACHE_TIMEOUT,
    ImportV1DatabaseExtra.JSON_PROPERTY_METADATA_PARAMS,
    ImportV1DatabaseExtra.JSON_PROPERTY_SCHEMAS_ALLOWED_FOR_CSV_UPLOAD
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ImportV1DatabaseExtra {
    public static final String JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE = "allows_virtual_table_explore";
    private Boolean allowsVirtualTableExplore;

    public static final String JSON_PROPERTY_CANCEL_QUERY_ON_WINDOWS_UNLOAD = "cancel_query_on_windows_unload";
    private Boolean cancelQueryOnWindowsUnload;

    public static final String JSON_PROPERTY_COST_ESTIMATE_ENABLED = "cost_estimate_enabled";
    private Boolean costEstimateEnabled;

    public static final String JSON_PROPERTY_DISABLE_DATA_PREVIEW = "disable_data_preview";
    private Boolean disableDataPreview;

    public static final String JSON_PROPERTY_ENGINE_PARAMS = "engine_params";
    private Map<String, Object> engineParams = new HashMap<>();

    public static final String JSON_PROPERTY_METADATA_CACHE_TIMEOUT = "metadata_cache_timeout";
    private Map<String, Integer> metadataCacheTimeout = new HashMap<>();

    public static final String JSON_PROPERTY_METADATA_PARAMS = "metadata_params";
    private Map<String, Object> metadataParams = new HashMap<>();

    public static final String JSON_PROPERTY_SCHEMAS_ALLOWED_FOR_CSV_UPLOAD = "schemas_allowed_for_csv_upload";
    private List<String> schemasAllowedForCsvUpload;

    public ImportV1DatabaseExtra() {
    }

    public ImportV1DatabaseExtra allowsVirtualTableExplore(Boolean allowsVirtualTableExplore) {

        this.allowsVirtualTableExplore = allowsVirtualTableExplore;
        return this;
    }

    /**
     * Get allowsVirtualTableExplore
     *
     * @return allowsVirtualTableExplore
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAllowsVirtualTableExplore() {
        return allowsVirtualTableExplore;
    }


    @JsonProperty(JSON_PROPERTY_ALLOWS_VIRTUAL_TABLE_EXPLORE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowsVirtualTableExplore(Boolean allowsVirtualTableExplore) {
        this.allowsVirtualTableExplore = allowsVirtualTableExplore;
    }


    public ImportV1DatabaseExtra cancelQueryOnWindowsUnload(Boolean cancelQueryOnWindowsUnload) {

        this.cancelQueryOnWindowsUnload = cancelQueryOnWindowsUnload;
        return this;
    }

    /**
     * Get cancelQueryOnWindowsUnload
     *
     * @return cancelQueryOnWindowsUnload
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CANCEL_QUERY_ON_WINDOWS_UNLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getCancelQueryOnWindowsUnload() {
        return cancelQueryOnWindowsUnload;
    }


    @JsonProperty(JSON_PROPERTY_CANCEL_QUERY_ON_WINDOWS_UNLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCancelQueryOnWindowsUnload(Boolean cancelQueryOnWindowsUnload) {
        this.cancelQueryOnWindowsUnload = cancelQueryOnWindowsUnload;
    }


    public ImportV1DatabaseExtra costEstimateEnabled(Boolean costEstimateEnabled) {

        this.costEstimateEnabled = costEstimateEnabled;
        return this;
    }

    /**
     * Get costEstimateEnabled
     *
     * @return costEstimateEnabled
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COST_ESTIMATE_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getCostEstimateEnabled() {
        return costEstimateEnabled;
    }


    @JsonProperty(JSON_PROPERTY_COST_ESTIMATE_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCostEstimateEnabled(Boolean costEstimateEnabled) {
        this.costEstimateEnabled = costEstimateEnabled;
    }


    public ImportV1DatabaseExtra disableDataPreview(Boolean disableDataPreview) {

        this.disableDataPreview = disableDataPreview;
        return this;
    }

    /**
     * Get disableDataPreview
     *
     * @return disableDataPreview
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DISABLE_DATA_PREVIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getDisableDataPreview() {
        return disableDataPreview;
    }


    @JsonProperty(JSON_PROPERTY_DISABLE_DATA_PREVIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDisableDataPreview(Boolean disableDataPreview) {
        this.disableDataPreview = disableDataPreview;
    }


    public ImportV1DatabaseExtra engineParams(Map<String, Object> engineParams) {

        this.engineParams = engineParams;
        return this;
    }

    public ImportV1DatabaseExtra putEngineParamsItem(String key, Object engineParamsItem) {
        if (this.engineParams == null) {
            this.engineParams = new HashMap<>();
        }
        this.engineParams.put(key, engineParamsItem);
        return this;
    }

    /**
     * Get engineParams
     *
     * @return engineParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ENGINE_PARAMS)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)

    public Map<String, Object> getEngineParams() {
        return engineParams;
    }


    @JsonProperty(JSON_PROPERTY_ENGINE_PARAMS)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)
    public void setEngineParams(Map<String, Object> engineParams) {
        this.engineParams = engineParams;
    }


    public ImportV1DatabaseExtra metadataCacheTimeout(Map<String, Integer> metadataCacheTimeout) {

        this.metadataCacheTimeout = metadataCacheTimeout;
        return this;
    }

    public ImportV1DatabaseExtra putMetadataCacheTimeoutItem(String key, Integer metadataCacheTimeoutItem) {
        if (this.metadataCacheTimeout == null) {
            this.metadataCacheTimeout = new HashMap<>();
        }
        this.metadataCacheTimeout.put(key, metadataCacheTimeoutItem);
        return this;
    }

    /**
     * Get metadataCacheTimeout
     *
     * @return metadataCacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METADATA_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Map<String, Integer> getMetadataCacheTimeout() {
        return metadataCacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_METADATA_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMetadataCacheTimeout(Map<String, Integer> metadataCacheTimeout) {
        this.metadataCacheTimeout = metadataCacheTimeout;
    }


    public ImportV1DatabaseExtra metadataParams(Map<String, Object> metadataParams) {

        this.metadataParams = metadataParams;
        return this;
    }

    public ImportV1DatabaseExtra putMetadataParamsItem(String key, Object metadataParamsItem) {
        if (this.metadataParams == null) {
            this.metadataParams = new HashMap<>();
        }
        this.metadataParams.put(key, metadataParamsItem);
        return this;
    }

    /**
     * Get metadataParams
     *
     * @return metadataParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METADATA_PARAMS)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)

    public Map<String, Object> getMetadataParams() {
        return metadataParams;
    }


    @JsonProperty(JSON_PROPERTY_METADATA_PARAMS)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)
    public void setMetadataParams(Map<String, Object> metadataParams) {
        this.metadataParams = metadataParams;
    }


    public ImportV1DatabaseExtra schemasAllowedForCsvUpload(List<String> schemasAllowedForCsvUpload) {

        this.schemasAllowedForCsvUpload = schemasAllowedForCsvUpload;
        return this;
    }

    public ImportV1DatabaseExtra addSchemasAllowedForCsvUploadItem(String schemasAllowedForCsvUploadItem) {
        if (this.schemasAllowedForCsvUpload == null) {
            this.schemasAllowedForCsvUpload = new ArrayList<>();
        }
        this.schemasAllowedForCsvUpload.add(schemasAllowedForCsvUploadItem);
        return this;
    }

    /**
     * Get schemasAllowedForCsvUpload
     *
     * @return schemasAllowedForCsvUpload
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMAS_ALLOWED_FOR_CSV_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getSchemasAllowedForCsvUpload() {
        return schemasAllowedForCsvUpload;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMAS_ALLOWED_FOR_CSV_UPLOAD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchemasAllowedForCsvUpload(List<String> schemasAllowedForCsvUpload) {
        this.schemasAllowedForCsvUpload = schemasAllowedForCsvUpload;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ImportV1DatabaseExtra importV1DatabaseExtra = (ImportV1DatabaseExtra) o;
        return Objects.equals(this.allowsVirtualTableExplore, importV1DatabaseExtra.allowsVirtualTableExplore) &&
            Objects.equals(this.cancelQueryOnWindowsUnload, importV1DatabaseExtra.cancelQueryOnWindowsUnload) &&
            Objects.equals(this.costEstimateEnabled, importV1DatabaseExtra.costEstimateEnabled) &&
            Objects.equals(this.disableDataPreview, importV1DatabaseExtra.disableDataPreview) &&
            Objects.equals(this.engineParams, importV1DatabaseExtra.engineParams) &&
            Objects.equals(this.metadataCacheTimeout, importV1DatabaseExtra.metadataCacheTimeout) &&
            Objects.equals(this.metadataParams, importV1DatabaseExtra.metadataParams) &&
            Objects.equals(this.schemasAllowedForCsvUpload, importV1DatabaseExtra.schemasAllowedForCsvUpload);
    }

    @Override
    public int hashCode() {
        return Objects.hash(allowsVirtualTableExplore, cancelQueryOnWindowsUnload, costEstimateEnabled, disableDataPreview, engineParams, metadataCacheTimeout, metadataParams, schemasAllowedForCsvUpload);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ImportV1DatabaseExtra {\n");
        sb.append("    allowsVirtualTableExplore: ").append(toIndentedString(allowsVirtualTableExplore)).append("\n");
        sb.append("    cancelQueryOnWindowsUnload: ").append(toIndentedString(cancelQueryOnWindowsUnload)).append("\n");
        sb.append("    costEstimateEnabled: ").append(toIndentedString(costEstimateEnabled)).append("\n");
        sb.append("    disableDataPreview: ").append(toIndentedString(disableDataPreview)).append("\n");
        sb.append("    engineParams: ").append(toIndentedString(engineParams)).append("\n");
        sb.append("    metadataCacheTimeout: ").append(toIndentedString(metadataCacheTimeout)).append("\n");
        sb.append("    metadataParams: ").append(toIndentedString(metadataParams)).append("\n");
        sb.append("    schemasAllowedForCsvUpload: ").append(toIndentedString(schemasAllowedForCsvUpload)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

