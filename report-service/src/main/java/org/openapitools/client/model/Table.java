/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * Table
 */
@JsonPropertyOrder({
    Table.JSON_PROPERTY_DATABASE_ID,
    Table.JSON_PROPERTY_DESCRIPTION,
    Table.JSON_PROPERTY_EXPANDED,
    Table.JSON_PROPERTY_ID,
    Table.JSON_PROPERTY_SCHEMA,
    Table.JSON_PROPERTY_TAB_STATE_ID,
    Table.JSON_PROPERTY_TABLE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class Table {
    public static final String JSON_PROPERTY_DATABASE_ID = "database_id";
    private Integer databaseId;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXPANDED = "expanded";
    private Boolean expanded;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_TAB_STATE_ID = "tab_state_id";
    private Integer tabStateId;

    public static final String JSON_PROPERTY_TABLE = "table";
    private String table;

    public Table() {
    }

    public Table databaseId(Integer databaseId) {

        this.databaseId = databaseId;
        return this;
    }

    /**
     * Get databaseId
     *
     * @return databaseId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDatabaseId() {
        return databaseId;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabaseId(Integer databaseId) {
        this.databaseId = databaseId;
    }


    public Table description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public Table expanded(Boolean expanded) {

        this.expanded = expanded;
        return this;
    }

    /**
     * Get expanded
     *
     * @return expanded
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPANDED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getExpanded() {
        return expanded;
    }


    @JsonProperty(JSON_PROPERTY_EXPANDED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExpanded(Boolean expanded) {
        this.expanded = expanded;
    }


    public Table id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public Table schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public Table tabStateId(Integer tabStateId) {

        this.tabStateId = tabStateId;
        return this;
    }

    /**
     * Get tabStateId
     *
     * @return tabStateId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAB_STATE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getTabStateId() {
        return tabStateId;
    }


    @JsonProperty(JSON_PROPERTY_TAB_STATE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTabStateId(Integer tabStateId) {
        this.tabStateId = tabStateId;
    }


    public Table table(String table) {

        this.table = table;
        return this;
    }

    /**
     * Get table
     *
     * @return table
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTable() {
        return table;
    }


    @JsonProperty(JSON_PROPERTY_TABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTable(String table) {
        this.table = table;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Table table = (Table) o;
        return Objects.equals(this.databaseId, table.databaseId) &&
            Objects.equals(this.description, table.description) &&
            Objects.equals(this.expanded, table.expanded) &&
            Objects.equals(this.id, table.id) &&
            Objects.equals(this.schema, table.schema) &&
            Objects.equals(this.tabStateId, table.tabStateId) &&
            Objects.equals(this.table, table.table);
    }

    @Override
    public int hashCode() {
        return Objects.hash(databaseId, description, expanded, id, schema, tabStateId, table);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class Table {\n");
        sb.append("    databaseId: ").append(toIndentedString(databaseId)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    expanded: ").append(toIndentedString(expanded)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    tabStateId: ").append(toIndentedString(tabStateId)).append("\n");
        sb.append("    table: ").append(toIndentedString(table)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

