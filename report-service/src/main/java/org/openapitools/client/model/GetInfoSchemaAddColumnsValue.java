/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * GetInfoSchemaAddColumnsValue
 */
@JsonPropertyOrder({
    GetInfoSchemaAddColumnsValue.JSON_PROPERTY_PAGE,
    GetInfoSchemaAddColumnsValue.JSON_PROPERTY_PAGE_SIZE
})
@JsonTypeName("get_info_schema_add_columns_value")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class GetInfoSchemaAddColumnsValue {
    public static final String JSON_PROPERTY_PAGE = "page";
    private Integer page;

    public static final String JSON_PROPERTY_PAGE_SIZE = "page_size";
    private Integer pageSize;

    public GetInfoSchemaAddColumnsValue() {
    }

    public GetInfoSchemaAddColumnsValue page(Integer page) {

        this.page = page;
        return this;
    }

    /**
     * Get page
     *
     * @return page
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getPage() {
        return page;
    }


    @JsonProperty(JSON_PROPERTY_PAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPage(Integer page) {
        this.page = page;
    }


    public GetInfoSchemaAddColumnsValue pageSize(Integer pageSize) {

        this.pageSize = pageSize;
        return this;
    }

    /**
     * Get pageSize
     *
     * @return pageSize
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getPageSize() {
        return pageSize;
    }


    @JsonProperty(JSON_PROPERTY_PAGE_SIZE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetInfoSchemaAddColumnsValue getInfoSchemaAddColumnsValue = (GetInfoSchemaAddColumnsValue) o;
        return Objects.equals(this.page, getInfoSchemaAddColumnsValue.page) &&
            Objects.equals(this.pageSize, getInfoSchemaAddColumnsValue.pageSize);
    }

    @Override
    public int hashCode() {
        return Objects.hash(page, pageSize);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class GetInfoSchemaAddColumnsValue {\n");
        sb.append("    page: ").append(toIndentedString(page)).append("\n");
        sb.append("    pageSize: ").append(toIndentedString(pageSize)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

