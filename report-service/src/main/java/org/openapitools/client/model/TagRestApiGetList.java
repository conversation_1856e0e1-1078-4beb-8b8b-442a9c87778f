/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * TagRestApiGetList
 */
@JsonPropertyOrder({
    TagRestApiGetList.JSON_PROPERTY_CHANGED_BY,
    TagRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    TagRestApiGetList.JSON_PROPERTY_CREATED_BY,
    TagRestApiGetList.JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED,
    TagRestApiGetList.JSON_PROPERTY_DESCRIPTION,
    TagRestApiGetList.JSON_PROPERTY_ID,
    TagRestApiGetList.JSO<PERSON>_PROPERTY_NAME,
    TagRestApiGetList.JSON_PROPERTY_TYPE
})
@JsonTypeName("TagRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TagRestApiGetList {
    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private TagRestApiGetListUser1 changedBy;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private TagRestApiGetListUser createdBy;

    public static final String JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED = "created_on_delta_humanized";
    private Object createdOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    /**
     * Gets or Sets type
     */
    public enum TypeEnum {
        NUMBER_1(new BigDecimal("1")),

        NUMBER_2(new BigDecimal("2")),

        NUMBER_3(new BigDecimal("3")),

        NUMBER_4(new BigDecimal("4"));

        private BigDecimal value;

        TypeEnum(BigDecimal value) {
            this.value = value;
        }

        @JsonValue
        public BigDecimal getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static TypeEnum fromValue(BigDecimal value) {
            for (TypeEnum b : TypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_TYPE = "type";
    private TypeEnum type;

    public TagRestApiGetList() {
    }

    @JsonCreator
    public TagRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED) Object createdOnDeltaHumanized
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.createdOnDeltaHumanized = createdOnDeltaHumanized;
    }

    public TagRestApiGetList changedBy(TagRestApiGetListUser1 changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public TagRestApiGetListUser1 getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(TagRestApiGetListUser1 changedBy) {
        this.changedBy = changedBy;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public TagRestApiGetList createdBy(TagRestApiGetListUser createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public TagRestApiGetListUser getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(TagRestApiGetListUser createdBy) {
        this.createdBy = createdBy;
    }


    /**
     * Get createdOnDeltaHumanized
     *
     * @return createdOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getCreatedOnDeltaHumanized() {
        return createdOnDeltaHumanized;
    }


    public TagRestApiGetList description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public TagRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public TagRestApiGetList name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public TagRestApiGetList type(TypeEnum type) {

        this.type = type;
        return this;
    }

    /**
     * Get type
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public TypeEnum getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(TypeEnum type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TagRestApiGetList tagRestApiGetList = (TagRestApiGetList) o;
        return Objects.equals(this.changedBy, tagRestApiGetList.changedBy) &&
            Objects.equals(this.changedOnDeltaHumanized, tagRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.createdBy, tagRestApiGetList.createdBy) &&
            Objects.equals(this.createdOnDeltaHumanized, tagRestApiGetList.createdOnDeltaHumanized) &&
            Objects.equals(this.description, tagRestApiGetList.description) &&
            Objects.equals(this.id, tagRestApiGetList.id) &&
            Objects.equals(this.name, tagRestApiGetList.name) &&
            Objects.equals(this.type, tagRestApiGetList.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedBy, changedOnDeltaHumanized, createdBy, createdOnDeltaHumanized, description, id, name, type);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TagRestApiGetList {\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    createdOnDeltaHumanized: ").append(toIndentedString(createdOnDeltaHumanized)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

