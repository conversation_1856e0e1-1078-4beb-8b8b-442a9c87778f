/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1DashboardIdOrSlugDatasetsGet200Response
 */
@JsonPropertyOrder({
    ApiV1DashboardIdOrSlugDatasetsGet200Response.JSON_PROPERTY_RESULT
})
@JsonTypeName("_api_v1_dashboard__id_or_slug__datasets_get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DashboardIdOrSlugDatasetsGet200Response {
    public static final String JSON_PROPERTY_RESULT = "result";
    private List<DashboardDatasetSchema> result;

    public ApiV1DashboardIdOrSlugDatasetsGet200Response() {
    }

    public ApiV1DashboardIdOrSlugDatasetsGet200Response result(List<DashboardDatasetSchema> result) {

        this.result = result;
        return this;
    }

    public ApiV1DashboardIdOrSlugDatasetsGet200Response addResultItem(DashboardDatasetSchema resultItem) {
        if (this.result == null) {
            this.result = new ArrayList<>();
        }
        this.result.add(resultItem);
        return this;
    }

    /**
     * Get result
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<DashboardDatasetSchema> getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(List<DashboardDatasetSchema> result) {
        this.result = result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DashboardIdOrSlugDatasetsGet200Response apiV1DashboardIdOrSlugDatasetsGet200Response = (ApiV1DashboardIdOrSlugDatasetsGet200Response) o;
        return Objects.equals(this.result, apiV1DashboardIdOrSlugDatasetsGet200Response.result);
    }

    @Override
    public int hashCode() {
        return Objects.hash(result);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DashboardIdOrSlugDatasetsGet200Response {\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

