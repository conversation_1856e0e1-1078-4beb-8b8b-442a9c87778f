/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * DatasetRestApiPut
 */
@JsonPropertyOrder({
    DatasetRestApiPut.JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM,
    DatasetRestApiPut.JSON_PROPERTY_CACHE_TIMEOUT,
    DatasetRestApiPut.JSON_PROPERTY_COLUMNS,
    DatasetRestApiPut.JSON_PROPERTY_DATABASE_ID,
    DatasetRestApiPut.JSON_PROPERTY_DEFAULT_ENDPOINT,
    DatasetRestApiPut.JSON_PROPERTY_DESCRIPTION,
    DatasetRestApiPut.JSON_PROPERTY_EXTERNAL_URL,
    DatasetRestApiPut.JSON_PROPERTY_EXTRA,
    DatasetRestApiPut.JSON_PROPERTY_FETCH_VALUES_PREDICATE,
    DatasetRestApiPut.JSON_PROPERTY_FILTER_SELECT_ENABLED,
    DatasetRestApiPut.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    DatasetRestApiPut.JSON_PROPERTY_IS_SQLLAB_VIEW,
    DatasetRestApiPut.JSON_PROPERTY_MAIN_DTTM_COL,
    DatasetRestApiPut.JSON_PROPERTY_METRICS,
    DatasetRestApiPut.JSON_PROPERTY_NORMALIZE_COLUMNS,
    DatasetRestApiPut.JSON_PROPERTY_OFFSET,
    DatasetRestApiPut.JSON_PROPERTY_OWNERS,
    DatasetRestApiPut.JSON_PROPERTY_SCHEMA,
    DatasetRestApiPut.JSON_PROPERTY_SQL,
    DatasetRestApiPut.JSON_PROPERTY_TABLE_NAME,
    DatasetRestApiPut.JSON_PROPERTY_TEMPLATE_PARAMS
})
@JsonTypeName("DatasetRestApi.put")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetRestApiPut {
    public static final String JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM = "always_filter_main_dttm";
    private Boolean alwaysFilterMainDttm = false;

    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<DatasetColumnsPut> columns;

    public static final String JSON_PROPERTY_DATABASE_ID = "database_id";
    private Integer databaseId;

    public static final String JSON_PROPERTY_DEFAULT_ENDPOINT = "default_endpoint";
    private String defaultEndpoint;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXTERNAL_URL = "external_url";
    private String externalUrl;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private String extra;

    public static final String JSON_PROPERTY_FETCH_VALUES_PREDICATE = "fetch_values_predicate";
    private String fetchValuesPredicate;

    public static final String JSON_PROPERTY_FILTER_SELECT_ENABLED = "filter_select_enabled";
    private Boolean filterSelectEnabled;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_IS_SQLLAB_VIEW = "is_sqllab_view";
    private Boolean isSqllabView;

    public static final String JSON_PROPERTY_MAIN_DTTM_COL = "main_dttm_col";
    private String mainDttmCol;

    public static final String JSON_PROPERTY_METRICS = "metrics";
    private List<DatasetMetricsPut> metrics;

    public static final String JSON_PROPERTY_NORMALIZE_COLUMNS = "normalize_columns";
    private Boolean normalizeColumns;

    public static final String JSON_PROPERTY_OFFSET = "offset";
    private Integer offset;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<Integer> owners;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public static final String JSON_PROPERTY_TEMPLATE_PARAMS = "template_params";
    private String templateParams;

    public DatasetRestApiPut() {
    }

    public DatasetRestApiPut alwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {

        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
        return this;
    }

    /**
     * Get alwaysFilterMainDttm
     *
     * @return alwaysFilterMainDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAlwaysFilterMainDttm() {
        return alwaysFilterMainDttm;
    }


    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAlwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {
        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
    }


    public DatasetRestApiPut cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Get cacheTimeout
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public DatasetRestApiPut columns(List<DatasetColumnsPut> columns) {

        this.columns = columns;
        return this;
    }

    public DatasetRestApiPut addColumnsItem(DatasetColumnsPut columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * Get columns
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<DatasetColumnsPut> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<DatasetColumnsPut> columns) {
        this.columns = columns;
    }


    public DatasetRestApiPut databaseId(Integer databaseId) {

        this.databaseId = databaseId;
        return this;
    }

    /**
     * Get databaseId
     *
     * @return databaseId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDatabaseId() {
        return databaseId;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabaseId(Integer databaseId) {
        this.databaseId = databaseId;
    }


    public DatasetRestApiPut defaultEndpoint(String defaultEndpoint) {

        this.defaultEndpoint = defaultEndpoint;
        return this;
    }

    /**
     * Get defaultEndpoint
     *
     * @return defaultEndpoint
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDefaultEndpoint() {
        return defaultEndpoint;
    }


    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDefaultEndpoint(String defaultEndpoint) {
        this.defaultEndpoint = defaultEndpoint;
    }


    public DatasetRestApiPut description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public DatasetRestApiPut externalUrl(String externalUrl) {

        this.externalUrl = externalUrl;
        return this;
    }

    /**
     * Get externalUrl
     *
     * @return externalUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExternalUrl() {
        return externalUrl;
    }


    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExternalUrl(String externalUrl) {
        this.externalUrl = externalUrl;
    }


    public DatasetRestApiPut extra(String extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(String extra) {
        this.extra = extra;
    }


    public DatasetRestApiPut fetchValuesPredicate(String fetchValuesPredicate) {

        this.fetchValuesPredicate = fetchValuesPredicate;
        return this;
    }

    /**
     * Get fetchValuesPredicate
     *
     * @return fetchValuesPredicate
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getFetchValuesPredicate() {
        return fetchValuesPredicate;
    }


    @JsonProperty(JSON_PROPERTY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFetchValuesPredicate(String fetchValuesPredicate) {
        this.fetchValuesPredicate = fetchValuesPredicate;
    }


    public DatasetRestApiPut filterSelectEnabled(Boolean filterSelectEnabled) {

        this.filterSelectEnabled = filterSelectEnabled;
        return this;
    }

    /**
     * Get filterSelectEnabled
     *
     * @return filterSelectEnabled
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER_SELECT_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getFilterSelectEnabled() {
        return filterSelectEnabled;
    }


    @JsonProperty(JSON_PROPERTY_FILTER_SELECT_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterSelectEnabled(Boolean filterSelectEnabled) {
        this.filterSelectEnabled = filterSelectEnabled;
    }


    public DatasetRestApiPut isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public DatasetRestApiPut isSqllabView(Boolean isSqllabView) {

        this.isSqllabView = isSqllabView;
        return this;
    }

    /**
     * Get isSqllabView
     *
     * @return isSqllabView
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_SQLLAB_VIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsSqllabView() {
        return isSqllabView;
    }


    @JsonProperty(JSON_PROPERTY_IS_SQLLAB_VIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsSqllabView(Boolean isSqllabView) {
        this.isSqllabView = isSqllabView;
    }


    public DatasetRestApiPut mainDttmCol(String mainDttmCol) {

        this.mainDttmCol = mainDttmCol;
        return this;
    }

    /**
     * Get mainDttmCol
     *
     * @return mainDttmCol
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MAIN_DTTM_COL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMainDttmCol() {
        return mainDttmCol;
    }


    @JsonProperty(JSON_PROPERTY_MAIN_DTTM_COL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMainDttmCol(String mainDttmCol) {
        this.mainDttmCol = mainDttmCol;
    }


    public DatasetRestApiPut metrics(List<DatasetMetricsPut> metrics) {

        this.metrics = metrics;
        return this;
    }

    public DatasetRestApiPut addMetricsItem(DatasetMetricsPut metricsItem) {
        if (this.metrics == null) {
            this.metrics = new ArrayList<>();
        }
        this.metrics.add(metricsItem);
        return this;
    }

    /**
     * Get metrics
     *
     * @return metrics
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<DatasetMetricsPut> getMetrics() {
        return metrics;
    }


    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMetrics(List<DatasetMetricsPut> metrics) {
        this.metrics = metrics;
    }


    public DatasetRestApiPut normalizeColumns(Boolean normalizeColumns) {

        this.normalizeColumns = normalizeColumns;
        return this;
    }

    /**
     * Get normalizeColumns
     *
     * @return normalizeColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getNormalizeColumns() {
        return normalizeColumns;
    }


    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setNormalizeColumns(Boolean normalizeColumns) {
        this.normalizeColumns = normalizeColumns;
    }


    public DatasetRestApiPut offset(Integer offset) {

        this.offset = offset;
        return this;
    }

    /**
     * Get offset
     *
     * @return offset
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getOffset() {
        return offset;
    }


    @JsonProperty(JSON_PROPERTY_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOffset(Integer offset) {
        this.offset = offset;
    }


    public DatasetRestApiPut owners(List<Integer> owners) {

        this.owners = owners;
        return this;
    }

    public DatasetRestApiPut addOwnersItem(Integer ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<Integer> owners) {
        this.owners = owners;
    }


    public DatasetRestApiPut schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public DatasetRestApiPut sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public DatasetRestApiPut tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * Get tableName
     *
     * @return tableName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }


    public DatasetRestApiPut templateParams(String templateParams) {

        this.templateParams = templateParams;
        return this;
    }

    /**
     * Get templateParams
     *
     * @return templateParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTemplateParams() {
        return templateParams;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetRestApiPut datasetRestApiPut = (DatasetRestApiPut) o;
        return Objects.equals(this.alwaysFilterMainDttm, datasetRestApiPut.alwaysFilterMainDttm) &&
            Objects.equals(this.cacheTimeout, datasetRestApiPut.cacheTimeout) &&
            Objects.equals(this.columns, datasetRestApiPut.columns) &&
            Objects.equals(this.databaseId, datasetRestApiPut.databaseId) &&
            Objects.equals(this.defaultEndpoint, datasetRestApiPut.defaultEndpoint) &&
            Objects.equals(this.description, datasetRestApiPut.description) &&
            Objects.equals(this.externalUrl, datasetRestApiPut.externalUrl) &&
            Objects.equals(this.extra, datasetRestApiPut.extra) &&
            Objects.equals(this.fetchValuesPredicate, datasetRestApiPut.fetchValuesPredicate) &&
            Objects.equals(this.filterSelectEnabled, datasetRestApiPut.filterSelectEnabled) &&
            Objects.equals(this.isManagedExternally, datasetRestApiPut.isManagedExternally) &&
            Objects.equals(this.isSqllabView, datasetRestApiPut.isSqllabView) &&
            Objects.equals(this.mainDttmCol, datasetRestApiPut.mainDttmCol) &&
            Objects.equals(this.metrics, datasetRestApiPut.metrics) &&
            Objects.equals(this.normalizeColumns, datasetRestApiPut.normalizeColumns) &&
            Objects.equals(this.offset, datasetRestApiPut.offset) &&
            Objects.equals(this.owners, datasetRestApiPut.owners) &&
            Objects.equals(this.schema, datasetRestApiPut.schema) &&
            Objects.equals(this.sql, datasetRestApiPut.sql) &&
            Objects.equals(this.tableName, datasetRestApiPut.tableName) &&
            Objects.equals(this.templateParams, datasetRestApiPut.templateParams);
    }

    @Override
    public int hashCode() {
        return Objects.hash(alwaysFilterMainDttm, cacheTimeout, columns, databaseId, defaultEndpoint, description, externalUrl, extra, fetchValuesPredicate, filterSelectEnabled, isManagedExternally, isSqllabView, mainDttmCol, metrics, normalizeColumns, offset, owners, schema, sql, tableName, templateParams);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetRestApiPut {\n");
        sb.append("    alwaysFilterMainDttm: ").append(toIndentedString(alwaysFilterMainDttm)).append("\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    databaseId: ").append(toIndentedString(databaseId)).append("\n");
        sb.append("    defaultEndpoint: ").append(toIndentedString(defaultEndpoint)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    externalUrl: ").append(toIndentedString(externalUrl)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    fetchValuesPredicate: ").append(toIndentedString(fetchValuesPredicate)).append("\n");
        sb.append("    filterSelectEnabled: ").append(toIndentedString(filterSelectEnabled)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    isSqllabView: ").append(toIndentedString(isSqllabView)).append("\n");
        sb.append("    mainDttmCol: ").append(toIndentedString(mainDttmCol)).append("\n");
        sb.append("    metrics: ").append(toIndentedString(metrics)).append("\n");
        sb.append("    normalizeColumns: ").append(toIndentedString(normalizeColumns)).append("\n");
        sb.append("    offset: ").append(toIndentedString(offset)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("    templateParams: ").append(toIndentedString(templateParams)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

