/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * DatabaseValidateParametersSchema
 */
@JsonPropertyOrder({
    DatabaseValidateParametersSchema.JSON_PROPERTY_CATALOG,
    DatabaseValidateParametersSchema.JSON_PROPERTY_CONFIGURATION_METHOD,
    DatabaseValidateParametersSchema.JSON_PROPERTY_DATABASE_NAME,
    DatabaseValidateParametersSchema.JSON_PROPERTY_DRIVER,
    DatabaseValidateParametersSchema.JSON_PROPERTY_ENGINE,
    DatabaseValidateParametersSchema.JSON_PROPERTY_EXTRA,
    DatabaseValidateParametersSchema.JSON_PROPERTY_ID,
    DatabaseValidateParametersSchema.JSON_PROPERTY_IMPERSONATE_USER,
    DatabaseValidateParametersSchema.JSON_PROPERTY_MASKED_ENCRYPTED_EXTRA,
    DatabaseValidateParametersSchema.JSON_PROPERTY_PARAMETERS,
    DatabaseValidateParametersSchema.JSON_PROPERTY_SERVER_CERT
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseValidateParametersSchema {
    public static final String JSON_PROPERTY_CATALOG = "catalog";
    private Map<String, Object> catalog = new HashMap<>();

    /**
     * Configuration_method is used on the frontend to inform the backend whether to explode parameters or to provide only a sqlalchemy_uri.
     */
    public enum ConfigurationMethodEnum {
        SQLALCHEMY_FORM("sqlalchemy_form"),

        DYNAMIC_FORM("dynamic_form");

        private String value;

        ConfigurationMethodEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static ConfigurationMethodEnum fromValue(String value) {
            for (ConfigurationMethodEnum b : ConfigurationMethodEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_CONFIGURATION_METHOD = "configuration_method";
    private ConfigurationMethodEnum configurationMethod;

    public static final String JSON_PROPERTY_DATABASE_NAME = "database_name";
    private String databaseName;

    public static final String JSON_PROPERTY_DRIVER = "driver";
    private String driver;

    public static final String JSON_PROPERTY_ENGINE = "engine";
    private String engine;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private String extra;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IMPERSONATE_USER = "impersonate_user";
    private Boolean impersonateUser;

    public static final String JSON_PROPERTY_MASKED_ENCRYPTED_EXTRA = "masked_encrypted_extra";
    private String maskedEncryptedExtra;

    public static final String JSON_PROPERTY_PARAMETERS = "parameters";
    private Map<String, Object> parameters = new HashMap<>();

    public static final String JSON_PROPERTY_SERVER_CERT = "server_cert";
    private String serverCert;

    public DatabaseValidateParametersSchema() {
    }

    public DatabaseValidateParametersSchema catalog(Map<String, Object> catalog) {

        this.catalog = catalog;
        return this;
    }

    public DatabaseValidateParametersSchema putCatalogItem(String key, Object catalogItem) {
        if (this.catalog == null) {
            this.catalog = new HashMap<>();
        }
        this.catalog.put(key, catalogItem);
        return this;
    }

    /**
     * Gsheets specific column for managing label to sheet urls
     *
     * @return catalog
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CATALOG)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)

    public Map<String, Object> getCatalog() {
        return catalog;
    }


    @JsonProperty(JSON_PROPERTY_CATALOG)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)
    public void setCatalog(Map<String, Object> catalog) {
        this.catalog = catalog;
    }


    public DatabaseValidateParametersSchema configurationMethod(ConfigurationMethodEnum configurationMethod) {

        this.configurationMethod = configurationMethod;
        return this;
    }

    /**
     * Configuration_method is used on the frontend to inform the backend whether to explode parameters or to provide only a sqlalchemy_uri.
     *
     * @return configurationMethod
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_CONFIGURATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public ConfigurationMethodEnum getConfigurationMethod() {
        return configurationMethod;
    }


    @JsonProperty(JSON_PROPERTY_CONFIGURATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setConfigurationMethod(ConfigurationMethodEnum configurationMethod) {
        this.configurationMethod = configurationMethod;
    }


    public DatabaseValidateParametersSchema databaseName(String databaseName) {

        this.databaseName = databaseName;
        return this;
    }

    /**
     * A database name to identify this connection.
     *
     * @return databaseName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatabaseName() {
        return databaseName;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabaseName(String databaseName) {
        this.databaseName = databaseName;
    }


    public DatabaseValidateParametersSchema driver(String driver) {

        this.driver = driver;
        return this;
    }

    /**
     * SQLAlchemy driver to use
     *
     * @return driver
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DRIVER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDriver() {
        return driver;
    }


    @JsonProperty(JSON_PROPERTY_DRIVER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDriver(String driver) {
        this.driver = driver;
    }


    public DatabaseValidateParametersSchema engine(String engine) {

        this.engine = engine;
        return this;
    }

    /**
     * SQLAlchemy engine to use
     *
     * @return engine
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_ENGINE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getEngine() {
        return engine;
    }


    @JsonProperty(JSON_PROPERTY_ENGINE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setEngine(String engine) {
        this.engine = engine;
    }


    public DatabaseValidateParametersSchema extra(String extra) {

        this.extra = extra;
        return this;
    }

    /**
     * &lt;p&gt;JSON string containing extra configuration elements.&lt;br&gt;1. The &lt;code&gt;engine_params&lt;/code&gt; object gets unpacked into the &lt;a href&#x3D;\&quot;https://docs.sqlalchemy.org/en/latest/core/engines.html#sqlalchemy.create_engine\&quot; rel&#x3D;\&quot;noopener noreferrer\&quot;&gt;sqlalchemy.create_engine&lt;/a&gt; call, while the &lt;code&gt;metadata_params&lt;/code&gt; gets unpacked into the &lt;a href&#x3D;\&quot;https://docs.sqlalchemy.org/en/rel_1_0/core/metadata.html#sqlalchemy.schema.MetaData\&quot; rel&#x3D;\&quot;noopener noreferrer\&quot;&gt;sqlalchemy.MetaData&lt;/a&gt; call.&lt;br&gt;2. The &lt;code&gt;metadata_cache_timeout&lt;/code&gt; is a cache timeout setting in seconds for metadata fetch of this database. Specify it as &lt;strong&gt;\&quot;metadata_cache_timeout\&quot;: {\&quot;schema_cache_timeout\&quot;: 600, \&quot;table_cache_timeout\&quot;: 600}&lt;/strong&gt;. If unset, cache will not be enabled for the functionality. A timeout of 0 indicates that the cache never expires.&lt;br&gt;3. The &lt;code&gt;schemas_allowed_for_file_upload&lt;/code&gt; is a comma separated list of schemas that CSVs are allowed to upload to. Specify it as &lt;strong&gt;\&quot;schemas_allowed_for_file_upload\&quot;: [\&quot;public\&quot;, \&quot;csv_upload\&quot;]&lt;/strong&gt;. If database flavor does not support schema or any schema is allowed to be accessed, just leave the list empty&lt;br&gt;4. The &lt;code&gt;version&lt;/code&gt; field is a string specifying the this db&#39;s version. This should be used with Presto DBs so that the syntax is correct&lt;br&gt;5. The &lt;code&gt;allows_virtual_table_explore&lt;/code&gt; field is a boolean specifying whether or not the Explore button in SQL Lab results is shown.&lt;br&gt;6. The &lt;code&gt;disable_data_preview&lt;/code&gt; field is a boolean specifying whether or not data preview queries will be run when fetching table metadata in SQL Lab.&lt;/p&gt;
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(String extra) {
        this.extra = extra;
    }


    public DatabaseValidateParametersSchema id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Database ID (for updates)
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public DatabaseValidateParametersSchema impersonateUser(Boolean impersonateUser) {

        this.impersonateUser = impersonateUser;
        return this;
    }

    /**
     * If Presto, all the queries in SQL Lab are going to be executed as the currently logged on user who must have permission to run them.&lt;br/&gt;If Hive and hive.server2.enable.doAs is enabled, will run the queries as service account, but impersonate the currently logged on user via hive.server2.proxy.user property.
     *
     * @return impersonateUser
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IMPERSONATE_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getImpersonateUser() {
        return impersonateUser;
    }


    @JsonProperty(JSON_PROPERTY_IMPERSONATE_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setImpersonateUser(Boolean impersonateUser) {
        this.impersonateUser = impersonateUser;
    }


    public DatabaseValidateParametersSchema maskedEncryptedExtra(String maskedEncryptedExtra) {

        this.maskedEncryptedExtra = maskedEncryptedExtra;
        return this;
    }

    /**
     * &lt;p&gt;JSON string containing additional connection configuration.&lt;br&gt;This is used to provide connection information for systems like Hive, Presto, and BigQuery, which do not conform to the username:password syntax normally used by SQLAlchemy.&lt;/p&gt;
     *
     * @return maskedEncryptedExtra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MASKED_ENCRYPTED_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMaskedEncryptedExtra() {
        return maskedEncryptedExtra;
    }


    @JsonProperty(JSON_PROPERTY_MASKED_ENCRYPTED_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMaskedEncryptedExtra(String maskedEncryptedExtra) {
        this.maskedEncryptedExtra = maskedEncryptedExtra;
    }


    public DatabaseValidateParametersSchema parameters(Map<String, Object> parameters) {

        this.parameters = parameters;
        return this;
    }

    public DatabaseValidateParametersSchema putParametersItem(String key, Object parametersItem) {
        if (this.parameters == null) {
            this.parameters = new HashMap<>();
        }
        this.parameters.put(key, parametersItem);
        return this;
    }

    /**
     * DB-specific parameters for configuration
     *
     * @return parameters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMETERS)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)

    public Map<String, Object> getParameters() {
        return parameters;
    }


    @JsonProperty(JSON_PROPERTY_PARAMETERS)
    @JsonInclude(content = JsonInclude.Include.ALWAYS, value = JsonInclude.Include.NON_NULL)
    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }


    public DatabaseValidateParametersSchema serverCert(String serverCert) {

        this.serverCert = serverCert;
        return this;
    }

    /**
     * &lt;p&gt;Optional CA_BUNDLE contents to validate HTTPS requests. Only available on certain database engines.&lt;/p&gt;
     *
     * @return serverCert
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SERVER_CERT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getServerCert() {
        return serverCert;
    }


    @JsonProperty(JSON_PROPERTY_SERVER_CERT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setServerCert(String serverCert) {
        this.serverCert = serverCert;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseValidateParametersSchema databaseValidateParametersSchema = (DatabaseValidateParametersSchema) o;
        return Objects.equals(this.catalog, databaseValidateParametersSchema.catalog) &&
            Objects.equals(this.configurationMethod, databaseValidateParametersSchema.configurationMethod) &&
            Objects.equals(this.databaseName, databaseValidateParametersSchema.databaseName) &&
            Objects.equals(this.driver, databaseValidateParametersSchema.driver) &&
            Objects.equals(this.engine, databaseValidateParametersSchema.engine) &&
            Objects.equals(this.extra, databaseValidateParametersSchema.extra) &&
            Objects.equals(this.id, databaseValidateParametersSchema.id) &&
            Objects.equals(this.impersonateUser, databaseValidateParametersSchema.impersonateUser) &&
            Objects.equals(this.maskedEncryptedExtra, databaseValidateParametersSchema.maskedEncryptedExtra) &&
            Objects.equals(this.parameters, databaseValidateParametersSchema.parameters) &&
            Objects.equals(this.serverCert, databaseValidateParametersSchema.serverCert);
    }

    @Override
    public int hashCode() {
        return Objects.hash(catalog, configurationMethod, databaseName, driver, engine, extra, id, impersonateUser, maskedEncryptedExtra, parameters, serverCert);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseValidateParametersSchema {\n");
        sb.append("    catalog: ").append(toIndentedString(catalog)).append("\n");
        sb.append("    configurationMethod: ").append(toIndentedString(configurationMethod)).append("\n");
        sb.append("    databaseName: ").append(toIndentedString(databaseName)).append("\n");
        sb.append("    driver: ").append(toIndentedString(driver)).append("\n");
        sb.append("    engine: ").append(toIndentedString(engine)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    impersonateUser: ").append(toIndentedString(impersonateUser)).append("\n");
        sb.append("    maskedEncryptedExtra: ").append(toIndentedString(maskedEncryptedExtra)).append("\n");
        sb.append("    parameters: ").append(toIndentedString(parameters)).append("\n");
        sb.append("    serverCert: ").append(toIndentedString(serverCert)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

