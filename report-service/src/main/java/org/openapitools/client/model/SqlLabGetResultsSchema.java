/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * SqlLabGetResultsSchema
 */
@JsonPropertyOrder({
    SqlLabGetResultsSchema.JSON_PROPERTY_KEY
})
@JsonTypeName("sql_lab_get_results_schema")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class SqlLabGetResultsSchema {
    public static final String JSON_PROPERTY_KEY = "key";
    private String key;

    public SqlLabGetResultsSchema() {
    }

    public SqlLabGetResultsSchema key(String key) {

        this.key = key;
        return this;
    }

    /**
     * Get key
     *
     * @return key
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_KEY)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getKey() {
        return key;
    }


    @JsonProperty(JSON_PROPERTY_KEY)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setKey(String key) {
        this.key = key;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SqlLabGetResultsSchema sqlLabGetResultsSchema = (SqlLabGetResultsSchema) o;
        return Objects.equals(this.key, sqlLabGetResultsSchema.key);
    }

    @Override
    public int hashCode() {
        return Objects.hash(key);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class SqlLabGetResultsSchema {\n");
        sb.append("    key: ").append(toIndentedString(key)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

