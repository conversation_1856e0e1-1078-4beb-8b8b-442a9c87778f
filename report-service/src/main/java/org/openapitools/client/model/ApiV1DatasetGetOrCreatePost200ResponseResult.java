/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1DatasetGetOrCreatePost200ResponseResult
 */
@JsonPropertyOrder({
    ApiV1DatasetGetOrCreatePost200ResponseResult.JSON_PROPERTY_TABLE_ID
})
@JsonTypeName("_api_v1_dataset_get_or_create__post_200_response_result")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DatasetGetOrCreatePost200ResponseResult {
    public static final String JSON_PROPERTY_TABLE_ID = "table_id";
    private Integer tableId;

    public ApiV1DatasetGetOrCreatePost200ResponseResult() {
    }

    public ApiV1DatasetGetOrCreatePost200ResponseResult tableId(Integer tableId) {

        this.tableId = tableId;
        return this;
    }

    /**
     * Get tableId
     *
     * @return tableId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getTableId() {
        return tableId;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTableId(Integer tableId) {
        this.tableId = tableId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DatasetGetOrCreatePost200ResponseResult apiV1DatasetGetOrCreatePost200ResponseResult = (ApiV1DatasetGetOrCreatePost200ResponseResult) o;
        return Objects.equals(this.tableId, apiV1DatasetGetOrCreatePost200ResponseResult.tableId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tableId);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DatasetGetOrCreatePost200ResponseResult {\n");
        sb.append("    tableId: ").append(toIndentedString(tableId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

