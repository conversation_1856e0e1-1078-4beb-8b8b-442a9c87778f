/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * GetListSchemaFiltersInner
 */
@JsonPropertyOrder({
    GetListSchemaFiltersInner.JSON_PROPERTY_COL,
    GetListSchemaFiltersInner.JSON_PROPERTY_OPR,
    GetListSchemaFiltersInner.JSON_PROPERTY_VALUE
})
@JsonTypeName("get_list_schema_filters_inner")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class GetListSchemaFiltersInner {
    public static final String JSON_PROPERTY_COL = "col";
    private String col;

    public static final String JSON_PROPERTY_OPR = "opr";
    private String opr;

    public static final String JSON_PROPERTY_VALUE = "value";
    private Object value;

    public GetListSchemaFiltersInner() {
    }

    public GetListSchemaFiltersInner col(String col) {

        this.col = col;
        return this;
    }

    /**
     * Get col
     *
     * @return col
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_COL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getCol() {
        return col;
    }


    @JsonProperty(JSON_PROPERTY_COL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setCol(String col) {
        this.col = col;
    }


    public GetListSchemaFiltersInner opr(String opr) {

        this.opr = opr;
        return this;
    }

    /**
     * Get opr
     *
     * @return opr
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_OPR)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getOpr() {
        return opr;
    }


    @JsonProperty(JSON_PROPERTY_OPR)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setOpr(String opr) {
        this.opr = opr;
    }


    public GetListSchemaFiltersInner value(Object value) {

        this.value = value;
        return this;
    }

    /**
     * Get value
     *
     * @return value
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Object getValue() {
        return value;
    }


    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setValue(GetListSchemaFiltersInnerValue value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetListSchemaFiltersInner getListSchemaFiltersInner = (GetListSchemaFiltersInner) o;
        return Objects.equals(this.col, getListSchemaFiltersInner.col) &&
            Objects.equals(this.opr, getListSchemaFiltersInner.opr) &&
            Objects.equals(this.value, getListSchemaFiltersInner.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(col, opr, value);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class GetListSchemaFiltersInner {\n");
        sb.append("    col: ").append(toIndentedString(col)).append("\n");
        sb.append("    opr: ").append(toIndentedString(opr)).append("\n");
        sb.append("    value: ").append(toIndentedString(value)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

