/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartGetDatasourceObjectDataResponse
 */
@JsonPropertyOrder({
    ChartGetDatasourceObjectDataResponse.JSON_PROPERTY_DATASOURCE_ID,
    ChartGetDatasourceObjectDataResponse.JSON_PROPERTY_DATASOURCE_TYPE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartGetDatasourceObjectDataResponse {
    public static final String JSON_PROPERTY_DATASOURCE_ID = "datasource_id";
    private Integer datasourceId;

    public static final String JSON_PROPERTY_DATASOURCE_TYPE = "datasource_type";
    private Integer datasourceType;

    public ChartGetDatasourceObjectDataResponse() {
    }

    public ChartGetDatasourceObjectDataResponse datasourceId(Integer datasourceId) {

        this.datasourceId = datasourceId;
        return this;
    }

    /**
     * The datasource identifier
     *
     * @return datasourceId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDatasourceId() {
        return datasourceId;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceId(Integer datasourceId) {
        this.datasourceId = datasourceId;
    }


    public ChartGetDatasourceObjectDataResponse datasourceType(Integer datasourceType) {

        this.datasourceType = datasourceType;
        return this;
    }

    /**
     * The datasource type
     *
     * @return datasourceType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDatasourceType() {
        return datasourceType;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceType(Integer datasourceType) {
        this.datasourceType = datasourceType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartGetDatasourceObjectDataResponse chartGetDatasourceObjectDataResponse = (ChartGetDatasourceObjectDataResponse) o;
        return Objects.equals(this.datasourceId, chartGetDatasourceObjectDataResponse.datasourceId) &&
            Objects.equals(this.datasourceType, chartGetDatasourceObjectDataResponse.datasourceType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(datasourceId, datasourceType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartGetDatasourceObjectDataResponse {\n");
        sb.append("    datasourceId: ").append(toIndentedString(datasourceId)).append("\n");
        sb.append("    datasourceType: ").append(toIndentedString(datasourceType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

