/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * SavedQueryRestApiGet
 */
@JsonPropertyOrder({
    SavedQueryRestApiGet.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    SavedQueryRestApiGet.JSON_PROPERTY_CREATED_BY,
    SavedQueryRestApiGet.JSON_PROPERTY_DATABASE,
    SavedQueryRestApiGet.JSON_PROPERTY_DESCRIPTION,
    SavedQueryRestApiGet.JSON_PROPERTY_ID,
    SavedQueryRestApiGet.JSON_PROPERTY_LABEL,
    SavedQueryRestApiGet.JSON_PROPERTY_SCHEMA,
    SavedQueryRestApiGet.JSON_PROPERTY_SQL,
    SavedQueryRestApiGet.JSON_PROPERTY_SQL_TABLES,
    SavedQueryRestApiGet.JSON_PROPERTY_TEMPLATE_PARAMETERS
})
@JsonTypeName("SavedQueryRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class SavedQueryRestApiGet {
    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private SavedQueryRestApiGetUser createdBy;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private SavedQueryRestApiGetDatabase database;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_LABEL = "label";
    private String label;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_SQL_TABLES = "sql_tables";
    private Object sqlTables = null;

    public static final String JSON_PROPERTY_TEMPLATE_PARAMETERS = "template_parameters";
    private String templateParameters;

    public SavedQueryRestApiGet() {
    }

    @JsonCreator
    public SavedQueryRestApiGet(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_SQL_TABLES) Object sqlTables
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.sqlTables = sqlTables;
    }

    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public SavedQueryRestApiGet createdBy(SavedQueryRestApiGetUser createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public SavedQueryRestApiGetUser getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(SavedQueryRestApiGetUser createdBy) {
        this.createdBy = createdBy;
    }


    public SavedQueryRestApiGet database(SavedQueryRestApiGetDatabase database) {

        this.database = database;
        return this;
    }

    /**
     * Get database
     *
     * @return database
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public SavedQueryRestApiGetDatabase getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabase(SavedQueryRestApiGetDatabase database) {
        this.database = database;
    }


    public SavedQueryRestApiGet description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public SavedQueryRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public SavedQueryRestApiGet label(String label) {

        this.label = label;
        return this;
    }

    /**
     * Get label
     *
     * @return label
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLabel() {
        return label;
    }


    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLabel(String label) {
        this.label = label;
    }


    public SavedQueryRestApiGet schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public SavedQueryRestApiGet sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    /**
     * Get sqlTables
     *
     * @return sqlTables
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL_TABLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getSqlTables() {
        return sqlTables;
    }


    public SavedQueryRestApiGet templateParameters(String templateParameters) {

        this.templateParameters = templateParameters;
        return this;
    }

    /**
     * Get templateParameters
     *
     * @return templateParameters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMETERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTemplateParameters() {
        return templateParameters;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMETERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateParameters(String templateParameters) {
        this.templateParameters = templateParameters;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SavedQueryRestApiGet savedQueryRestApiGet = (SavedQueryRestApiGet) o;
        return Objects.equals(this.changedOnDeltaHumanized, savedQueryRestApiGet.changedOnDeltaHumanized) &&
            Objects.equals(this.createdBy, savedQueryRestApiGet.createdBy) &&
            Objects.equals(this.database, savedQueryRestApiGet.database) &&
            Objects.equals(this.description, savedQueryRestApiGet.description) &&
            Objects.equals(this.id, savedQueryRestApiGet.id) &&
            Objects.equals(this.label, savedQueryRestApiGet.label) &&
            Objects.equals(this.schema, savedQueryRestApiGet.schema) &&
            Objects.equals(this.sql, savedQueryRestApiGet.sql) &&
            Objects.equals(this.sqlTables, savedQueryRestApiGet.sqlTables) &&
            Objects.equals(this.templateParameters, savedQueryRestApiGet.templateParameters);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedOnDeltaHumanized, createdBy, database, description, id, label, schema, sql, sqlTables, templateParameters);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class SavedQueryRestApiGet {\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    label: ").append(toIndentedString(label)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    sqlTables: ").append(toIndentedString(sqlTables)).append("\n");
        sb.append("    templateParameters: ").append(toIndentedString(templateParameters)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

