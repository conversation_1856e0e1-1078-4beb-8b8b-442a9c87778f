/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * QueryRestApiGet
 */
@JsonPropertyOrder({
    QueryRestApiGet.JSON_PROPERTY_CHANGED_ON,
    QueryRestApiGet.JSON_PROPERTY_CLIENT_ID,
    QueryRestApiGet.JSON_PROPERTY_DATABASE,
    QueryRestApiGet.JSON_PROPERTY_END_RESULT_BACKEND_TIME,
    QueryRestApiGet.JSON_PROPERTY_END_TIME,
    QueryRestApiGet.JSON_PROPERTY_ERROR_MESSAGE,
    QueryRestApiGet.JSON_PROPERTY_EXECUTED_SQL,
    QueryRestApiGet.JSON_PROPERTY_ID,
    QueryRestApiGet.JSON_PROPERTY_LIMIT,
    QueryRestApiGet.JSON_PROPERTY_PROGRESS,
    QueryRestApiGet.JSON_PROPERTY_RESULTS_KEY,
    QueryRestApiGet.JSON_PROPERTY_ROWS,
    QueryRestApiGet.JSON_PROPERTY_SCHEMA,
    QueryRestApiGet.JSON_PROPERTY_SELECT_AS_CTA,
    QueryRestApiGet.JSON_PROPERTY_SELECT_AS_CTA_USED,
    QueryRestApiGet.JSON_PROPERTY_SELECT_SQL,
    QueryRestApiGet.JSON_PROPERTY_SQL,
    QueryRestApiGet.JSON_PROPERTY_SQL_EDITOR_ID,
    QueryRestApiGet.JSON_PROPERTY_START_RUNNING_TIME,
    QueryRestApiGet.JSON_PROPERTY_START_TIME,
    QueryRestApiGet.JSON_PROPERTY_STATUS,
    QueryRestApiGet.JSON_PROPERTY_TAB_NAME,
    QueryRestApiGet.JSON_PROPERTY_TMP_SCHEMA_NAME,
    QueryRestApiGet.JSON_PROPERTY_TMP_TABLE_NAME,
    QueryRestApiGet.JSON_PROPERTY_TRACKING_URL
})
@JsonTypeName("QueryRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class QueryRestApiGet {
    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CLIENT_ID = "client_id";
    private String clientId;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private QueryRestApiGetDatabase database;

    public static final String JSON_PROPERTY_END_RESULT_BACKEND_TIME = "end_result_backend_time";
    private BigDecimal endResultBackendTime;

    public static final String JSON_PROPERTY_END_TIME = "end_time";
    private BigDecimal endTime;

    public static final String JSON_PROPERTY_ERROR_MESSAGE = "error_message";
    private String errorMessage;

    public static final String JSON_PROPERTY_EXECUTED_SQL = "executed_sql";
    private String executedSql;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_LIMIT = "limit";
    private Integer limit;

    public static final String JSON_PROPERTY_PROGRESS = "progress";
    private Integer progress;

    public static final String JSON_PROPERTY_RESULTS_KEY = "results_key";
    private String resultsKey;

    public static final String JSON_PROPERTY_ROWS = "rows";
    private Integer rows;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SELECT_AS_CTA = "select_as_cta";
    private Boolean selectAsCta;

    public static final String JSON_PROPERTY_SELECT_AS_CTA_USED = "select_as_cta_used";
    private Boolean selectAsCtaUsed;

    public static final String JSON_PROPERTY_SELECT_SQL = "select_sql";
    private String selectSql;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_SQL_EDITOR_ID = "sql_editor_id";
    private String sqlEditorId;

    public static final String JSON_PROPERTY_START_RUNNING_TIME = "start_running_time";
    private BigDecimal startRunningTime;

    public static final String JSON_PROPERTY_START_TIME = "start_time";
    private BigDecimal startTime;

    public static final String JSON_PROPERTY_STATUS = "status";
    private String status;

    public static final String JSON_PROPERTY_TAB_NAME = "tab_name";
    private String tabName;

    public static final String JSON_PROPERTY_TMP_SCHEMA_NAME = "tmp_schema_name";
    private String tmpSchemaName;

    public static final String JSON_PROPERTY_TMP_TABLE_NAME = "tmp_table_name";
    private String tmpTableName;

    public static final String JSON_PROPERTY_TRACKING_URL = "tracking_url";
    private Object trackingUrl = null;

    public QueryRestApiGet() {
    }

    @JsonCreator
    public QueryRestApiGet(
        @JsonProperty(JSON_PROPERTY_TRACKING_URL) Object trackingUrl
    ) {
        this();
        this.trackingUrl = trackingUrl;
    }

    public QueryRestApiGet changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public QueryRestApiGet clientId(String clientId) {

        this.clientId = clientId;
        return this;
    }

    /**
     * Get clientId
     *
     * @return clientId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_CLIENT_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getClientId() {
        return clientId;
    }


    @JsonProperty(JSON_PROPERTY_CLIENT_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setClientId(String clientId) {
        this.clientId = clientId;
    }


    public QueryRestApiGet database(QueryRestApiGetDatabase database) {

        this.database = database;
        return this;
    }

    /**
     * Get database
     *
     * @return database
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public QueryRestApiGetDatabase getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabase(QueryRestApiGetDatabase database) {
        this.database = database;
    }


    public QueryRestApiGet endResultBackendTime(BigDecimal endResultBackendTime) {

        this.endResultBackendTime = endResultBackendTime;
        return this;
    }

    /**
     * Get endResultBackendTime
     *
     * @return endResultBackendTime
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_END_RESULT_BACKEND_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getEndResultBackendTime() {
        return endResultBackendTime;
    }


    @JsonProperty(JSON_PROPERTY_END_RESULT_BACKEND_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEndResultBackendTime(BigDecimal endResultBackendTime) {
        this.endResultBackendTime = endResultBackendTime;
    }


    public QueryRestApiGet endTime(BigDecimal endTime) {

        this.endTime = endTime;
        return this;
    }

    /**
     * Get endTime
     *
     * @return endTime
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_END_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getEndTime() {
        return endTime;
    }


    @JsonProperty(JSON_PROPERTY_END_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEndTime(BigDecimal endTime) {
        this.endTime = endTime;
    }


    public QueryRestApiGet errorMessage(String errorMessage) {

        this.errorMessage = errorMessage;
        return this;
    }

    /**
     * Get errorMessage
     *
     * @return errorMessage
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getErrorMessage() {
        return errorMessage;
    }


    @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }


    public QueryRestApiGet executedSql(String executedSql) {

        this.executedSql = executedSql;
        return this;
    }

    /**
     * Get executedSql
     *
     * @return executedSql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXECUTED_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExecutedSql() {
        return executedSql;
    }


    @JsonProperty(JSON_PROPERTY_EXECUTED_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExecutedSql(String executedSql) {
        this.executedSql = executedSql;
    }


    public QueryRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public QueryRestApiGet limit(Integer limit) {

        this.limit = limit;
        return this;
    }

    /**
     * Get limit
     *
     * @return limit
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getLimit() {
        return limit;
    }


    @JsonProperty(JSON_PROPERTY_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLimit(Integer limit) {
        this.limit = limit;
    }


    public QueryRestApiGet progress(Integer progress) {

        this.progress = progress;
        return this;
    }

    /**
     * Get progress
     *
     * @return progress
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PROGRESS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getProgress() {
        return progress;
    }


    @JsonProperty(JSON_PROPERTY_PROGRESS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setProgress(Integer progress) {
        this.progress = progress;
    }


    public QueryRestApiGet resultsKey(String resultsKey) {

        this.resultsKey = resultsKey;
        return this;
    }

    /**
     * Get resultsKey
     *
     * @return resultsKey
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULTS_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getResultsKey() {
        return resultsKey;
    }


    @JsonProperty(JSON_PROPERTY_RESULTS_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResultsKey(String resultsKey) {
        this.resultsKey = resultsKey;
    }


    public QueryRestApiGet rows(Integer rows) {

        this.rows = rows;
        return this;
    }

    /**
     * Get rows
     *
     * @return rows
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROWS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getRows() {
        return rows;
    }


    @JsonProperty(JSON_PROPERTY_ROWS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRows(Integer rows) {
        this.rows = rows;
    }


    public QueryRestApiGet schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public QueryRestApiGet selectAsCta(Boolean selectAsCta) {

        this.selectAsCta = selectAsCta;
        return this;
    }

    /**
     * Get selectAsCta
     *
     * @return selectAsCta
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SELECT_AS_CTA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getSelectAsCta() {
        return selectAsCta;
    }


    @JsonProperty(JSON_PROPERTY_SELECT_AS_CTA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSelectAsCta(Boolean selectAsCta) {
        this.selectAsCta = selectAsCta;
    }


    public QueryRestApiGet selectAsCtaUsed(Boolean selectAsCtaUsed) {

        this.selectAsCtaUsed = selectAsCtaUsed;
        return this;
    }

    /**
     * Get selectAsCtaUsed
     *
     * @return selectAsCtaUsed
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SELECT_AS_CTA_USED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getSelectAsCtaUsed() {
        return selectAsCtaUsed;
    }


    @JsonProperty(JSON_PROPERTY_SELECT_AS_CTA_USED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSelectAsCtaUsed(Boolean selectAsCtaUsed) {
        this.selectAsCtaUsed = selectAsCtaUsed;
    }


    public QueryRestApiGet selectSql(String selectSql) {

        this.selectSql = selectSql;
        return this;
    }

    /**
     * Get selectSql
     *
     * @return selectSql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SELECT_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSelectSql() {
        return selectSql;
    }


    @JsonProperty(JSON_PROPERTY_SELECT_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSelectSql(String selectSql) {
        this.selectSql = selectSql;
    }


    public QueryRestApiGet sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public QueryRestApiGet sqlEditorId(String sqlEditorId) {

        this.sqlEditorId = sqlEditorId;
        return this;
    }

    /**
     * Get sqlEditorId
     *
     * @return sqlEditorId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL_EDITOR_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSqlEditorId() {
        return sqlEditorId;
    }


    @JsonProperty(JSON_PROPERTY_SQL_EDITOR_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSqlEditorId(String sqlEditorId) {
        this.sqlEditorId = sqlEditorId;
    }


    public QueryRestApiGet startRunningTime(BigDecimal startRunningTime) {

        this.startRunningTime = startRunningTime;
        return this;
    }

    /**
     * Get startRunningTime
     *
     * @return startRunningTime
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_START_RUNNING_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getStartRunningTime() {
        return startRunningTime;
    }


    @JsonProperty(JSON_PROPERTY_START_RUNNING_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStartRunningTime(BigDecimal startRunningTime) {
        this.startRunningTime = startRunningTime;
    }


    public QueryRestApiGet startTime(BigDecimal startTime) {

        this.startTime = startTime;
        return this;
    }

    /**
     * Get startTime
     *
     * @return startTime
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_START_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getStartTime() {
        return startTime;
    }


    @JsonProperty(JSON_PROPERTY_START_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStartTime(BigDecimal startTime) {
        this.startTime = startTime;
    }


    public QueryRestApiGet status(String status) {

        this.status = status;
        return this;
    }

    /**
     * Get status
     *
     * @return status
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getStatus() {
        return status;
    }


    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStatus(String status) {
        this.status = status;
    }


    public QueryRestApiGet tabName(String tabName) {

        this.tabName = tabName;
        return this;
    }

    /**
     * Get tabName
     *
     * @return tabName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAB_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTabName() {
        return tabName;
    }


    @JsonProperty(JSON_PROPERTY_TAB_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTabName(String tabName) {
        this.tabName = tabName;
    }


    public QueryRestApiGet tmpSchemaName(String tmpSchemaName) {

        this.tmpSchemaName = tmpSchemaName;
        return this;
    }

    /**
     * Get tmpSchemaName
     *
     * @return tmpSchemaName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TMP_SCHEMA_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTmpSchemaName() {
        return tmpSchemaName;
    }


    @JsonProperty(JSON_PROPERTY_TMP_SCHEMA_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTmpSchemaName(String tmpSchemaName) {
        this.tmpSchemaName = tmpSchemaName;
    }


    public QueryRestApiGet tmpTableName(String tmpTableName) {

        this.tmpTableName = tmpTableName;
        return this;
    }

    /**
     * Get tmpTableName
     *
     * @return tmpTableName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TMP_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTmpTableName() {
        return tmpTableName;
    }


    @JsonProperty(JSON_PROPERTY_TMP_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTmpTableName(String tmpTableName) {
        this.tmpTableName = tmpTableName;
    }


    /**
     * Get trackingUrl
     *
     * @return trackingUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TRACKING_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getTrackingUrl() {
        return trackingUrl;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        QueryRestApiGet queryRestApiGet = (QueryRestApiGet) o;
        return Objects.equals(this.changedOn, queryRestApiGet.changedOn) &&
            Objects.equals(this.clientId, queryRestApiGet.clientId) &&
            Objects.equals(this.database, queryRestApiGet.database) &&
            Objects.equals(this.endResultBackendTime, queryRestApiGet.endResultBackendTime) &&
            Objects.equals(this.endTime, queryRestApiGet.endTime) &&
            Objects.equals(this.errorMessage, queryRestApiGet.errorMessage) &&
            Objects.equals(this.executedSql, queryRestApiGet.executedSql) &&
            Objects.equals(this.id, queryRestApiGet.id) &&
            Objects.equals(this.limit, queryRestApiGet.limit) &&
            Objects.equals(this.progress, queryRestApiGet.progress) &&
            Objects.equals(this.resultsKey, queryRestApiGet.resultsKey) &&
            Objects.equals(this.rows, queryRestApiGet.rows) &&
            Objects.equals(this.schema, queryRestApiGet.schema) &&
            Objects.equals(this.selectAsCta, queryRestApiGet.selectAsCta) &&
            Objects.equals(this.selectAsCtaUsed, queryRestApiGet.selectAsCtaUsed) &&
            Objects.equals(this.selectSql, queryRestApiGet.selectSql) &&
            Objects.equals(this.sql, queryRestApiGet.sql) &&
            Objects.equals(this.sqlEditorId, queryRestApiGet.sqlEditorId) &&
            Objects.equals(this.startRunningTime, queryRestApiGet.startRunningTime) &&
            Objects.equals(this.startTime, queryRestApiGet.startTime) &&
            Objects.equals(this.status, queryRestApiGet.status) &&
            Objects.equals(this.tabName, queryRestApiGet.tabName) &&
            Objects.equals(this.tmpSchemaName, queryRestApiGet.tmpSchemaName) &&
            Objects.equals(this.tmpTableName, queryRestApiGet.tmpTableName) &&
            Objects.equals(this.trackingUrl, queryRestApiGet.trackingUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedOn, clientId, database, endResultBackendTime, endTime, errorMessage, executedSql, id, limit, progress, resultsKey, rows, schema, selectAsCta, selectAsCtaUsed, selectSql, sql, sqlEditorId, startRunningTime, startTime, status, tabName, tmpSchemaName, tmpTableName, trackingUrl);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class QueryRestApiGet {\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    clientId: ").append(toIndentedString(clientId)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    endResultBackendTime: ").append(toIndentedString(endResultBackendTime)).append("\n");
        sb.append("    endTime: ").append(toIndentedString(endTime)).append("\n");
        sb.append("    errorMessage: ").append(toIndentedString(errorMessage)).append("\n");
        sb.append("    executedSql: ").append(toIndentedString(executedSql)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    limit: ").append(toIndentedString(limit)).append("\n");
        sb.append("    progress: ").append(toIndentedString(progress)).append("\n");
        sb.append("    resultsKey: ").append(toIndentedString(resultsKey)).append("\n");
        sb.append("    rows: ").append(toIndentedString(rows)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    selectAsCta: ").append(toIndentedString(selectAsCta)).append("\n");
        sb.append("    selectAsCtaUsed: ").append(toIndentedString(selectAsCtaUsed)).append("\n");
        sb.append("    selectSql: ").append(toIndentedString(selectSql)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    sqlEditorId: ").append(toIndentedString(sqlEditorId)).append("\n");
        sb.append("    startRunningTime: ").append(toIndentedString(startRunningTime)).append("\n");
        sb.append("    startTime: ").append(toIndentedString(startTime)).append("\n");
        sb.append("    status: ").append(toIndentedString(status)).append("\n");
        sb.append("    tabName: ").append(toIndentedString(tabName)).append("\n");
        sb.append("    tmpSchemaName: ").append(toIndentedString(tmpSchemaName)).append("\n");
        sb.append("    tmpTableName: ").append(toIndentedString(tmpTableName)).append("\n");
        sb.append("    trackingUrl: ").append(toIndentedString(trackingUrl)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

