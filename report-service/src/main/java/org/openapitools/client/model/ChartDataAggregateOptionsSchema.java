/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartDataAggregateOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataAggregateOptionsSchema.JSON_PROPERTY_AGGREGATES
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataAggregateOptionsSchema {
    public static final String JSON_PROPERTY_AGGREGATES = "aggregates";
    private Object aggregates;

    public ChartDataAggregateOptionsSchema() {
    }

    public ChartDataAggregateOptionsSchema aggregates(Object aggregates) {

        this.aggregates = aggregates;
        return this;
    }

    /**
     * The keys are the name of the aggregate column to be created, and the values specify the details of how to apply the aggregation. If an operator requires additional options, these can be passed here to be unpacked in the operator call. The following numpy operators are supported: average, argmin, argmax, cumsum, cumprod, max, mean, median, nansum, nanmin, nanmax, nanmean, nanmedian, min, percentile, prod, product, std, sum, var. Any options required by the operator can be passed to the &#x60;options&#x60; object.  In the example, a new column &#x60;first_quantile&#x60; is created based on values in the column &#x60;my_col&#x60; using the &#x60;percentile&#x60; operator with the &#x60;q&#x3D;0.25&#x60; parameter.
     *
     * @return aggregates
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_AGGREGATES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getAggregates() {
        return aggregates;
    }


    @JsonProperty(JSON_PROPERTY_AGGREGATES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAggregates(Object aggregates) {
        this.aggregates = aggregates;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataAggregateOptionsSchema chartDataAggregateOptionsSchema = (ChartDataAggregateOptionsSchema) o;
        return Objects.equals(this.aggregates, chartDataAggregateOptionsSchema.aggregates);
    }

    @Override
    public int hashCode() {
        return Objects.hash(aggregates);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataAggregateOptionsSchema {\n");
        sb.append("    aggregates: ").append(toIndentedString(aggregates)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

