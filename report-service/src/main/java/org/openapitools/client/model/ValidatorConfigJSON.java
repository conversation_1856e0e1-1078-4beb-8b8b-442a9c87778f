/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * ValidatorConfigJSON
 */
@JsonPropertyOrder({
    ValidatorConfigJSON.JSON_PROPERTY_OP,
    ValidatorConfigJSON.JSON_PROPERTY_THRESHOLD
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ValidatorConfigJSON {
    /**
     * The operation to compare with a threshold to apply to the SQL output
     */
    public enum OpEnum {
        LESS_THAN("<"),

        LESS_THAN_OR_EQUAL_TO("<="),

        G<PERSON>ATER_THAN(">"),

        GREATER_THAN_OR_EQUAL_TO(">="),

        EQUAL("=="),

        NOT_EQUAL("!=");

        private String value;

        OpEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static OpEnum fromValue(String value) {
            for (OpEnum b : OpEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_OP = "op";
    private OpEnum op;

    public static final String JSON_PROPERTY_THRESHOLD = "threshold";
    private BigDecimal threshold;

    public ValidatorConfigJSON() {
    }

    public ValidatorConfigJSON op(OpEnum op) {

        this.op = op;
        return this;
    }

    /**
     * The operation to compare with a threshold to apply to the SQL output
     *
     * @return op
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OP)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OpEnum getOp() {
        return op;
    }


    @JsonProperty(JSON_PROPERTY_OP)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOp(OpEnum op) {
        this.op = op;
    }


    public ValidatorConfigJSON threshold(BigDecimal threshold) {

        this.threshold = threshold;
        return this;
    }

    /**
     * Get threshold
     *
     * @return threshold
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_THRESHOLD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getThreshold() {
        return threshold;
    }


    @JsonProperty(JSON_PROPERTY_THRESHOLD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setThreshold(BigDecimal threshold) {
        this.threshold = threshold;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ValidatorConfigJSON validatorConfigJSON = (ValidatorConfigJSON) o;
        return Objects.equals(this.op, validatorConfigJSON.op) &&
            Objects.equals(this.threshold, validatorConfigJSON.threshold);
    }

    @Override
    public int hashCode() {
        return Objects.hash(op, threshold);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ValidatorConfigJSON {\n");
        sb.append("    op: ").append(toIndentedString(op)).append("\n");
        sb.append("    threshold: ").append(toIndentedString(threshold)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

