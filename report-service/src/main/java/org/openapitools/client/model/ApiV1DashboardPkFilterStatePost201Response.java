/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1DashboardPkFilterStatePost201Response
 */
@JsonPropertyOrder({
    ApiV1DashboardPkFilterStatePost201Response.JSON_PROPERTY_KEY
})
@JsonTypeName("_api_v1_dashboard__pk__filter_state_post_201_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DashboardPkFilterStatePost201Response {
    public static final String JSON_PROPERTY_KEY = "key";
    private String key;

    public ApiV1DashboardPkFilterStatePost201Response() {
    }

    public ApiV1DashboardPkFilterStatePost201Response key(String key) {

        this.key = key;
        return this;
    }

    /**
     * The key to retrieve the value.
     *
     * @return key
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getKey() {
        return key;
    }


    @JsonProperty(JSON_PROPERTY_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setKey(String key) {
        this.key = key;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DashboardPkFilterStatePost201Response apiV1DashboardPkFilterStatePost201Response = (ApiV1DashboardPkFilterStatePost201Response) o;
        return Objects.equals(this.key, apiV1DashboardPkFilterStatePost201Response.key);
    }

    @Override
    public int hashCode() {
        return Objects.hash(key);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DashboardPkFilterStatePost201Response {\n");
        sb.append("    key: ").append(toIndentedString(key)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

