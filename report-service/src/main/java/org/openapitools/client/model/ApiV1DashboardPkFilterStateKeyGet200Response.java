/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1DashboardPkFilterStateKeyGet200Response
 */
@JsonPropertyOrder({
    ApiV1DashboardPkFilterStateKeyGet200Response.JSON_PROPERTY_VALUE
})
@JsonTypeName("_api_v1_dashboard__pk__filter_state__key__get_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DashboardPkFilterStateKeyGet200Response {
    public static final String JSON_PROPERTY_VALUE = "value";
    private String value;

    public ApiV1DashboardPkFilterStateKeyGet200Response() {
    }

    public ApiV1DashboardPkFilterStateKeyGet200Response value(String value) {

        this.value = value;
        return this;
    }

    /**
     * The stored value
     *
     * @return value
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getValue() {
        return value;
    }


    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DashboardPkFilterStateKeyGet200Response apiV1DashboardPkFilterStateKeyGet200Response = (ApiV1DashboardPkFilterStateKeyGet200Response) o;
        return Objects.equals(this.value, apiV1DashboardPkFilterStateKeyGet200Response.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DashboardPkFilterStateKeyGet200Response {\n");
        sb.append("    value: ").append(toIndentedString(value)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

