/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ChartDataSelectOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataSelectOptionsSchema.JSON_PROPERTY_COLUMNS,
    ChartDataSelectOptionsSchema.JSON_PROPERTY_EXCLUDE,
    ChartDataSelectOptionsSchema.JSON_PROPERTY_RENAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataSelectOptionsSchema {
    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<String> columns;

    public static final String JSON_PROPERTY_EXCLUDE = "exclude";
    private List<String> exclude;

    public static final String JSON_PROPERTY_RENAME = "rename";
    private List<Object> rename;

    public ChartDataSelectOptionsSchema() {
    }

    public ChartDataSelectOptionsSchema columns(List<String> columns) {

        this.columns = columns;
        return this;
    }

    public ChartDataSelectOptionsSchema addColumnsItem(String columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * Columns which to select from the input data, in the desired order. If columns are renamed, the original column name should be referenced here.
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<String> columns) {
        this.columns = columns;
    }


    public ChartDataSelectOptionsSchema exclude(List<String> exclude) {

        this.exclude = exclude;
        return this;
    }

    public ChartDataSelectOptionsSchema addExcludeItem(String excludeItem) {
        if (this.exclude == null) {
            this.exclude = new ArrayList<>();
        }
        this.exclude.add(excludeItem);
        return this;
    }

    /**
     * Columns to exclude from selection.
     *
     * @return exclude
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXCLUDE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getExclude() {
        return exclude;
    }


    @JsonProperty(JSON_PROPERTY_EXCLUDE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExclude(List<String> exclude) {
        this.exclude = exclude;
    }


    public ChartDataSelectOptionsSchema rename(List<Object> rename) {

        this.rename = rename;
        return this;
    }

    public ChartDataSelectOptionsSchema addRenameItem(Object renameItem) {
        if (this.rename == null) {
            this.rename = new ArrayList<>();
        }
        this.rename.add(renameItem);
        return this;
    }

    /**
     * columns which to rename, mapping source column to target column. For instance, &#x60;{&#39;y&#39;: &#39;y2&#39;}&#x60; will rename the column &#x60;y&#x60; to &#x60;y2&#x60;.
     *
     * @return rename
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RENAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getRename() {
        return rename;
    }


    @JsonProperty(JSON_PROPERTY_RENAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRename(List<Object> rename) {
        this.rename = rename;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataSelectOptionsSchema chartDataSelectOptionsSchema = (ChartDataSelectOptionsSchema) o;
        return Objects.equals(this.columns, chartDataSelectOptionsSchema.columns) &&
            Objects.equals(this.exclude, chartDataSelectOptionsSchema.exclude) &&
            Objects.equals(this.rename, chartDataSelectOptionsSchema.rename);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columns, exclude, rename);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataSelectOptionsSchema {\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    exclude: ").append(toIndentedString(exclude)).append("\n");
        sb.append("    rename: ").append(toIndentedString(rename)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

