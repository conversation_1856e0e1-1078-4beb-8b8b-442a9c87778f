/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * RLSRestApiGet
 */
@JsonPropertyOrder({
    RLSRestApiGet.JSON_PROPERTY_CLAUSE,
    RLSRestApiGet.JSON_PROPERTY_DESCRIPTION,
    RLSRestApiGet.JSON_PROPERTY_FILTER_TYPE,
    RLSRestApiGet.JSON_PROPERTY_GROUP_KEY,
    RLSRestApiGet.JSON_PROPERTY_ID,
    RLSRestApiGet.JSON_PROPERTY_NAME,
    RLSRestApiGet.JSON_PROPERTY_ROLES,
    RLSRestApiGet.JSON_PROPERTY_TABLES
})
@JsonTypeName("RLSRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class RLSRestApiGet {
    public static final String JSON_PROPERTY_CLAUSE = "clause";
    private String clause;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    /**
     * filter_type_description
     */
    public enum FilterTypeEnum {
        REGULAR("Regular"),

        BASE("Base");

        private String value;

        FilterTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static FilterTypeEnum fromValue(String value) {
            for (FilterTypeEnum b : FilterTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_FILTER_TYPE = "filter_type";
    private FilterTypeEnum filterType;

    public static final String JSON_PROPERTY_GROUP_KEY = "group_key";
    private String groupKey;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_ROLES = "roles";
    private List<Roles1> roles;

    public static final String JSON_PROPERTY_TABLES = "tables";
    private List<Tables> tables;

    public RLSRestApiGet() {
    }

    public RLSRestApiGet clause(String clause) {

        this.clause = clause;
        return this;
    }

    /**
     * clause_description
     *
     * @return clause
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CLAUSE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getClause() {
        return clause;
    }


    @JsonProperty(JSON_PROPERTY_CLAUSE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setClause(String clause) {
        this.clause = clause;
    }


    public RLSRestApiGet description(String description) {

        this.description = description;
        return this;
    }

    /**
     * description_description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public RLSRestApiGet filterType(FilterTypeEnum filterType) {

        this.filterType = filterType;
        return this;
    }

    /**
     * filter_type_description
     *
     * @return filterType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public FilterTypeEnum getFilterType() {
        return filterType;
    }


    @JsonProperty(JSON_PROPERTY_FILTER_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterType(FilterTypeEnum filterType) {
        this.filterType = filterType;
    }


    public RLSRestApiGet groupKey(String groupKey) {

        this.groupKey = groupKey;
        return this;
    }

    /**
     * group_key_description
     *
     * @return groupKey
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GROUP_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getGroupKey() {
        return groupKey;
    }


    @JsonProperty(JSON_PROPERTY_GROUP_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGroupKey(String groupKey) {
        this.groupKey = groupKey;
    }


    public RLSRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * id_description
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public RLSRestApiGet name(String name) {

        this.name = name;
        return this;
    }

    /**
     * name_description
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public RLSRestApiGet roles(List<Roles1> roles) {

        this.roles = roles;
        return this;
    }

    public RLSRestApiGet addRolesItem(Roles1 rolesItem) {
        if (this.roles == null) {
            this.roles = new ArrayList<>();
        }
        this.roles.add(rolesItem);
        return this;
    }

    /**
     * Get roles
     *
     * @return roles
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Roles1> getRoles() {
        return roles;
    }


    @JsonProperty(JSON_PROPERTY_ROLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRoles(List<Roles1> roles) {
        this.roles = roles;
    }


    public RLSRestApiGet tables(List<Tables> tables) {

        this.tables = tables;
        return this;
    }

    public RLSRestApiGet addTablesItem(Tables tablesItem) {
        if (this.tables == null) {
            this.tables = new ArrayList<>();
        }
        this.tables.add(tablesItem);
        return this;
    }

    /**
     * Get tables
     *
     * @return tables
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Tables> getTables() {
        return tables;
    }


    @JsonProperty(JSON_PROPERTY_TABLES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTables(List<Tables> tables) {
        this.tables = tables;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RLSRestApiGet rlSRestApiGet = (RLSRestApiGet) o;
        return Objects.equals(this.clause, rlSRestApiGet.clause) &&
            Objects.equals(this.description, rlSRestApiGet.description) &&
            Objects.equals(this.filterType, rlSRestApiGet.filterType) &&
            Objects.equals(this.groupKey, rlSRestApiGet.groupKey) &&
            Objects.equals(this.id, rlSRestApiGet.id) &&
            Objects.equals(this.name, rlSRestApiGet.name) &&
            Objects.equals(this.roles, rlSRestApiGet.roles) &&
            Objects.equals(this.tables, rlSRestApiGet.tables);
    }

    @Override
    public int hashCode() {
        return Objects.hash(clause, description, filterType, groupKey, id, name, roles, tables);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RLSRestApiGet {\n");
        sb.append("    clause: ").append(toIndentedString(clause)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    filterType: ").append(toIndentedString(filterType)).append("\n");
        sb.append("    groupKey: ").append(toIndentedString(groupKey)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    roles: ").append(toIndentedString(roles)).append("\n");
        sb.append("    tables: ").append(toIndentedString(tables)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

