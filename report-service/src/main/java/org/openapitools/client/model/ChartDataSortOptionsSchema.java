/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartDataSortOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataSortOptionsSchema.JSON_PROPERTY_AGGREGATES,
    ChartDataSortOptionsSchema.JSON_PROPERTY_COLUMNS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataSortOptionsSchema {
    public static final String JSON_PROPERTY_AGGREGATES = "aggregates";
    private Object aggregates;

    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private Object columns;

    public ChartDataSortOptionsSchema() {
    }

    public ChartDataSortOptionsSchema aggregates(Object aggregates) {

        this.aggregates = aggregates;
        return this;
    }

    /**
     * The keys are the name of the aggregate column to be created, and the values specify the details of how to apply the aggregation. If an operator requires additional options, these can be passed here to be unpacked in the operator call. The following numpy operators are supported: average, argmin, argmax, cumsum, cumprod, max, mean, median, nansum, nanmin, nanmax, nanmean, nanmedian, min, percentile, prod, product, std, sum, var. Any options required by the operator can be passed to the &#x60;options&#x60; object.  In the example, a new column &#x60;first_quantile&#x60; is created based on values in the column &#x60;my_col&#x60; using the &#x60;percentile&#x60; operator with the &#x60;q&#x3D;0.25&#x60; parameter.
     *
     * @return aggregates
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_AGGREGATES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getAggregates() {
        return aggregates;
    }


    @JsonProperty(JSON_PROPERTY_AGGREGATES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAggregates(Object aggregates) {
        this.aggregates = aggregates;
    }


    public ChartDataSortOptionsSchema columns(Object columns) {

        this.columns = columns;
        return this;
    }

    /**
     * columns by by which to sort. The key specifies the column name, value specifies if sorting in ascending order.
     *
     * @return columns
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Object getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setColumns(Object columns) {
        this.columns = columns;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataSortOptionsSchema chartDataSortOptionsSchema = (ChartDataSortOptionsSchema) o;
        return Objects.equals(this.aggregates, chartDataSortOptionsSchema.aggregates) &&
            Objects.equals(this.columns, chartDataSortOptionsSchema.columns);
    }

    @Override
    public int hashCode() {
        return Objects.hash(aggregates, columns);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataSortOptionsSchema {\n");
        sb.append("    aggregates: ").append(toIndentedString(aggregates)).append("\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

