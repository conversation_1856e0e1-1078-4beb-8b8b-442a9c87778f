/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * DatasetRestApiGetList
 */
@JsonPropertyOrder({
    DatasetRestApiGetList.JSON_PROPERTY_CHANGED_BY,
    DatasetRestApiGetList.JSON_PROPERTY_CHANGED_BY_NAME,
    DatasetRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    DatasetRestApiGetList.JSON_PROPERTY_CHANGED_ON_UTC,
    DatasetRestApiGetList.JSON_PROPERTY_DATABASE,
    DatasetRestApiGetList.JSON_PROPERTY_DATASOURCE_TYPE,
    DatasetRestApiGetList.JSON_PROPERTY_DEFAULT_ENDPOINT,
    DatasetRestApiGetList.JSON_PROPERTY_DESCRIPTION,
    DatasetRestApiGetList.JSON_PROPERTY_EXPLORE_URL,
    DatasetRestApiGetList.JSON_PROPERTY_EXTRA,
    DatasetRestApiGetList.JSON_PROPERTY_ID,
    DatasetRestApiGetList.JSON_PROPERTY_KIND,
    DatasetRestApiGetList.JSON_PROPERTY_OWNERS,
    DatasetRestApiGetList.JSON_PROPERTY_SCHEMA,
    DatasetRestApiGetList.JSON_PROPERTY_SQL,
    DatasetRestApiGetList.JSON_PROPERTY_TABLE_NAME
})
@JsonTypeName("DatasetRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatasetRestApiGetList {
    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private DatasetRestApiGetListUser1 changedBy;

    public static final String JSON_PROPERTY_CHANGED_BY_NAME = "changed_by_name";
    private Object changedByName = null;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CHANGED_ON_UTC = "changed_on_utc";
    private Object changedOnUtc = null;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private DatasetRestApiGetListDatabase database;

    public static final String JSON_PROPERTY_DATASOURCE_TYPE = "datasource_type";
    private Object datasourceType = null;

    public static final String JSON_PROPERTY_DEFAULT_ENDPOINT = "default_endpoint";
    private String defaultEndpoint;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXPLORE_URL = "explore_url";
    private Object exploreUrl = null;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private String extra;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_KIND = "kind";
    private Object kind = null;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<DatasetRestApiGetListUser> owners;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public DatasetRestApiGetList() {
    }

    @JsonCreator
    public DatasetRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_BY_NAME) Object changedByName,
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_UTC) Object changedOnUtc,
        @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE) Object datasourceType,
        @JsonProperty(JSON_PROPERTY_EXPLORE_URL) Object exploreUrl,
        @JsonProperty(JSON_PROPERTY_KIND) Object kind
    ) {
        this();
        this.changedByName = changedByName;
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.changedOnUtc = changedOnUtc;
        this.datasourceType = datasourceType;
        this.exploreUrl = exploreUrl;
        this.kind = kind;
    }

    public DatasetRestApiGetList changedBy(DatasetRestApiGetListUser1 changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public DatasetRestApiGetListUser1 getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(DatasetRestApiGetListUser1 changedBy) {
        this.changedBy = changedBy;
    }


    /**
     * Get changedByName
     *
     * @return changedByName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedByName() {
        return changedByName;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    /**
     * Get changedOnUtc
     *
     * @return changedOnUtc
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_UTC)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnUtc() {
        return changedOnUtc;
    }


    public DatasetRestApiGetList database(DatasetRestApiGetListDatabase database) {

        this.database = database;
        return this;
    }

    /**
     * Get database
     *
     * @return database
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public DatasetRestApiGetListDatabase getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabase(DatasetRestApiGetListDatabase database) {
        this.database = database;
    }


    /**
     * Get datasourceType
     *
     * @return datasourceType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDatasourceType() {
        return datasourceType;
    }


    public DatasetRestApiGetList defaultEndpoint(String defaultEndpoint) {

        this.defaultEndpoint = defaultEndpoint;
        return this;
    }

    /**
     * Get defaultEndpoint
     *
     * @return defaultEndpoint
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDefaultEndpoint() {
        return defaultEndpoint;
    }


    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDefaultEndpoint(String defaultEndpoint) {
        this.defaultEndpoint = defaultEndpoint;
    }


    public DatasetRestApiGetList description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    /**
     * Get exploreUrl
     *
     * @return exploreUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXPLORE_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExploreUrl() {
        return exploreUrl;
    }


    public DatasetRestApiGetList extra(String extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(String extra) {
        this.extra = extra;
    }


    public DatasetRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    /**
     * Get kind
     *
     * @return kind
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_KIND)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getKind() {
        return kind;
    }


    public DatasetRestApiGetList owners(List<DatasetRestApiGetListUser> owners) {

        this.owners = owners;
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<DatasetRestApiGetListUser> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<DatasetRestApiGetListUser> owners) {
        this.owners = owners;
    }


    public DatasetRestApiGetList schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public DatasetRestApiGetList sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public DatasetRestApiGetList tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * Get tableName
     *
     * @return tableName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatasetRestApiGetList datasetRestApiGetList = (DatasetRestApiGetList) o;
        return Objects.equals(this.changedBy, datasetRestApiGetList.changedBy) &&
            Objects.equals(this.changedByName, datasetRestApiGetList.changedByName) &&
            Objects.equals(this.changedOnDeltaHumanized, datasetRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.changedOnUtc, datasetRestApiGetList.changedOnUtc) &&
            Objects.equals(this.database, datasetRestApiGetList.database) &&
            Objects.equals(this.datasourceType, datasetRestApiGetList.datasourceType) &&
            Objects.equals(this.defaultEndpoint, datasetRestApiGetList.defaultEndpoint) &&
            Objects.equals(this.description, datasetRestApiGetList.description) &&
            Objects.equals(this.exploreUrl, datasetRestApiGetList.exploreUrl) &&
            Objects.equals(this.extra, datasetRestApiGetList.extra) &&
            Objects.equals(this.id, datasetRestApiGetList.id) &&
            Objects.equals(this.kind, datasetRestApiGetList.kind) &&
            Objects.equals(this.owners, datasetRestApiGetList.owners) &&
            Objects.equals(this.schema, datasetRestApiGetList.schema) &&
            Objects.equals(this.sql, datasetRestApiGetList.sql) &&
            Objects.equals(this.tableName, datasetRestApiGetList.tableName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedBy, changedByName, changedOnDeltaHumanized, changedOnUtc, database, datasourceType, defaultEndpoint, description, exploreUrl, extra, id, kind, owners, schema, sql, tableName);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatasetRestApiGetList {\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedByName: ").append(toIndentedString(changedByName)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    changedOnUtc: ").append(toIndentedString(changedOnUtc)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    datasourceType: ").append(toIndentedString(datasourceType)).append("\n");
        sb.append("    defaultEndpoint: ").append(toIndentedString(defaultEndpoint)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    exploreUrl: ").append(toIndentedString(exploreUrl)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    kind: ").append(toIndentedString(kind)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

