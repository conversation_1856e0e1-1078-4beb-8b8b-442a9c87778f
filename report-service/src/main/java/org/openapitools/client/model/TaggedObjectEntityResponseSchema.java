/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TaggedObjectEntityResponseSchema
 */
@JsonPropertyOrder({
    TaggedObjectEntityResponseSchema.JSON_PROPERTY_CHANGED_ON,
    TaggedObjectEntityResponseSchema.JSON_PROPERTY_CREATED_BY,
    TaggedObjectEntityResponseSchema.JSON_PROPERTY_CREATOR,
    TaggedObjectEntityResponseSchema.JSON_PROPERTY_ID,
    TaggedObjectEntityResponseSchema.JSON_PROPERTY_NAME,
    TaggedObjectEntityResponseSchema.JSON_PROPERTY_OWNERS,
    TaggedObjectEntityResponseSchema.JSON_PROPERTY_TAGS,
    TaggedObjectEntityResponseSchema.JSON_PROPERTY_TYPE,
    TaggedObjectEntityResponseSchema.JSON_PROPERTY_URL
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TaggedObjectEntityResponseSchema {
    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private User createdBy;

    public static final String JSON_PROPERTY_CREATOR = "creator";
    private String creator;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<User1> owners;

    public static final String JSON_PROPERTY_TAGS = "tags";
    private List<TagGetResponseSchema> tags;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public static final String JSON_PROPERTY_URL = "url";
    private String url;

    public TaggedObjectEntityResponseSchema() {
    }

    public TaggedObjectEntityResponseSchema changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public TaggedObjectEntityResponseSchema createdBy(User createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public User getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(User createdBy) {
        this.createdBy = createdBy;
    }


    public TaggedObjectEntityResponseSchema creator(String creator) {

        this.creator = creator;
        return this;
    }

    /**
     * Get creator
     *
     * @return creator
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCreator() {
        return creator;
    }


    @JsonProperty(JSON_PROPERTY_CREATOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreator(String creator) {
        this.creator = creator;
    }


    public TaggedObjectEntityResponseSchema id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public TaggedObjectEntityResponseSchema name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public TaggedObjectEntityResponseSchema owners(List<User1> owners) {

        this.owners = owners;
        return this;
    }

    public TaggedObjectEntityResponseSchema addOwnersItem(User1 ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<User1> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<User1> owners) {
        this.owners = owners;
    }


    public TaggedObjectEntityResponseSchema tags(List<TagGetResponseSchema> tags) {

        this.tags = tags;
        return this;
    }

    public TaggedObjectEntityResponseSchema addTagsItem(TagGetResponseSchema tagsItem) {
        if (this.tags == null) {
            this.tags = new ArrayList<>();
        }
        this.tags.add(tagsItem);
        return this;
    }

    /**
     * Get tags
     *
     * @return tags
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<TagGetResponseSchema> getTags() {
        return tags;
    }


    @JsonProperty(JSON_PROPERTY_TAGS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTags(List<TagGetResponseSchema> tags) {
        this.tags = tags;
    }


    public TaggedObjectEntityResponseSchema type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Get type
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(String type) {
        this.type = type;
    }


    public TaggedObjectEntityResponseSchema url(String url) {

        this.url = url;
        return this;
    }

    /**
     * Get url
     *
     * @return url
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUrl() {
        return url;
    }


    @JsonProperty(JSON_PROPERTY_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TaggedObjectEntityResponseSchema taggedObjectEntityResponseSchema = (TaggedObjectEntityResponseSchema) o;
        return Objects.equals(this.changedOn, taggedObjectEntityResponseSchema.changedOn) &&
            Objects.equals(this.createdBy, taggedObjectEntityResponseSchema.createdBy) &&
            Objects.equals(this.creator, taggedObjectEntityResponseSchema.creator) &&
            Objects.equals(this.id, taggedObjectEntityResponseSchema.id) &&
            Objects.equals(this.name, taggedObjectEntityResponseSchema.name) &&
            Objects.equals(this.owners, taggedObjectEntityResponseSchema.owners) &&
            Objects.equals(this.tags, taggedObjectEntityResponseSchema.tags) &&
            Objects.equals(this.type, taggedObjectEntityResponseSchema.type) &&
            Objects.equals(this.url, taggedObjectEntityResponseSchema.url);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedOn, createdBy, creator, id, name, owners, tags, type, url);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TaggedObjectEntityResponseSchema {\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    creator: ").append(toIndentedString(creator)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    tags: ").append(toIndentedString(tags)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("    url: ").append(toIndentedString(url)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

