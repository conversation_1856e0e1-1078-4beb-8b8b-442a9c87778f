/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * DatabaseSchemaAccessForFileUploadResponse
 */
@JsonPropertyOrder({
    DatabaseSchemaAccessForFileUploadResponse.JSON_PROPERTY_SCHEMAS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class DatabaseSchemaAccessForFileUploadResponse {
    public static final String JSON_PROPERTY_SCHEMAS = "schemas";
    private List<String> schemas;

    public DatabaseSchemaAccessForFileUploadResponse() {
    }

    public DatabaseSchemaAccessForFileUploadResponse schemas(List<String> schemas) {

        this.schemas = schemas;
        return this;
    }

    public DatabaseSchemaAccessForFileUploadResponse addSchemasItem(String schemasItem) {
        if (this.schemas == null) {
            this.schemas = new ArrayList<>();
        }
        this.schemas.add(schemasItem);
        return this;
    }

    /**
     * The list of schemas allowed for the database to upload information
     *
     * @return schemas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getSchemas() {
        return schemas;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchemas(List<String> schemas) {
        this.schemas = schemas;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        DatabaseSchemaAccessForFileUploadResponse databaseSchemaAccessForFileUploadResponse = (DatabaseSchemaAccessForFileUploadResponse) o;
        return Objects.equals(this.schemas, databaseSchemaAccessForFileUploadResponse.schemas);
    }

    @Override
    public int hashCode() {
        return Objects.hash(schemas);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class DatabaseSchemaAccessForFileUploadResponse {\n");
        sb.append("    schemas: ").append(toIndentedString(schemas)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

