/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ChartDataRestApiGetListDashboard
 */
@JsonPropertyOrder({
    ChartDataRestApiGetListDashboard.JSON_PROPERTY_DASHBOARD_TITLE,
    ChartDataRestApiGetListDashboard.JSON_PROPERTY_ID
})
@JsonTypeName("ChartDataRestApi.get_list.Dashboard")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataRestApiGetListDashboard {
    public static final String JSON_PROPERTY_DASHBOARD_TITLE = "dashboard_title";
    private String dashboardTitle;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public ChartDataRestApiGetListDashboard() {
    }

    public ChartDataRestApiGetListDashboard dashboardTitle(String dashboardTitle) {

        this.dashboardTitle = dashboardTitle;
        return this;
    }

    /**
     * Get dashboardTitle
     *
     * @return dashboardTitle
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDashboardTitle() {
        return dashboardTitle;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_TITLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardTitle(String dashboardTitle) {
        this.dashboardTitle = dashboardTitle;
    }


    public ChartDataRestApiGetListDashboard id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataRestApiGetListDashboard chartDataRestApiGetListDashboard = (ChartDataRestApiGetListDashboard) o;
        return Objects.equals(this.dashboardTitle, chartDataRestApiGetListDashboard.dashboardTitle) &&
            Objects.equals(this.id, chartDataRestApiGetListDashboard.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dashboardTitle, id);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataRestApiGetListDashboard {\n");
        sb.append("    dashboardTitle: ").append(toIndentedString(dashboardTitle)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

