/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response
 */
@JsonPropertyOrder({
    ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response.JSON_PROPERTY_ID,
    ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response.JSON_PROPERTY_RESULT
})
@JsonTypeName("_api_v1_annotation_layer__pk__annotation__annotation_id__put_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response {
    public static final String JSON_PROPERTY_ID = "id";
    private BigDecimal id;

    public static final String JSON_PROPERTY_RESULT = "result";
    private AnnotationRestApiPut result;

    public ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response() {
    }

    public ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response id(BigDecimal id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(BigDecimal id) {
        this.id = id;
    }


    public ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response result(AnnotationRestApiPut result) {

        this.result = result;
        return this;
    }

    /**
     * Get result
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public AnnotationRestApiPut getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(AnnotationRestApiPut result) {
        this.result = result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response apiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response = (ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response) o;
        return Objects.equals(this.id, apiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response.id) &&
            Objects.equals(this.result, apiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response.result);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, result);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1AnnotationLayerPkAnnotationAnnotationIdPut200Response {\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

