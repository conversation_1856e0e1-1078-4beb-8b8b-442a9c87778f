/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * GuestTokenCreate
 */
@JsonPropertyOrder({
    GuestTokenCreate.JSON_PROPERTY_RESOURCES,
    GuestTokenCreate.JSON_PROPERTY_RLS,
    GuestTokenCreate.JSON_PROPERTY_USER
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class GuestTokenCreate {
    public static final String JSON_PROPERTY_RESOURCES = "resources";
    private List<Resource> resources = new ArrayList<>();

    public static final String JSON_PROPERTY_RLS = "rls";
    private List<RlsRule> rls = new ArrayList<>();

    public static final String JSON_PROPERTY_USER = "user";
    private User2 user;

    public GuestTokenCreate() {
    }

    public GuestTokenCreate resources(List<Resource> resources) {

        this.resources = resources;
        return this;
    }

    public GuestTokenCreate addResourcesItem(Resource resourcesItem) {
        if (this.resources == null) {
            this.resources = new ArrayList<>();
        }
        this.resources.add(resourcesItem);
        return this;
    }

    /**
     * Get resources
     *
     * @return resources
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_RESOURCES)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public List<Resource> getResources() {
        return resources;
    }


    @JsonProperty(JSON_PROPERTY_RESOURCES)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setResources(List<Resource> resources) {
        this.resources = resources;
    }


    public GuestTokenCreate rls(List<RlsRule> rls) {

        this.rls = rls;
        return this;
    }

    public GuestTokenCreate addRlsItem(RlsRule rlsItem) {
        if (this.rls == null) {
            this.rls = new ArrayList<>();
        }
        this.rls.add(rlsItem);
        return this;
    }

    /**
     * Get rls
     *
     * @return rls
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_RLS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public List<RlsRule> getRls() {
        return rls;
    }


    @JsonProperty(JSON_PROPERTY_RLS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setRls(List<RlsRule> rls) {
        this.rls = rls;
    }


    public GuestTokenCreate user(User2 user) {

        this.user = user;
        return this;
    }

    /**
     * Get user
     *
     * @return user
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public User2 getUser() {
        return user;
    }


    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUser(User2 user) {
        this.user = user;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GuestTokenCreate guestTokenCreate = (GuestTokenCreate) o;
        return Objects.equals(this.resources, guestTokenCreate.resources) &&
            Objects.equals(this.rls, guestTokenCreate.rls) &&
            Objects.equals(this.user, guestTokenCreate.user);
    }

    @Override
    public int hashCode() {
        return Objects.hash(resources, rls, user);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class GuestTokenCreate {\n");
        sb.append("    resources: ").append(toIndentedString(resources)).append("\n");
        sb.append("    rls: ").append(toIndentedString(rls)).append("\n");
        sb.append("    user: ").append(toIndentedString(user)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

