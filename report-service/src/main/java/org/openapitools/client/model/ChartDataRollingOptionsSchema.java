/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * ChartDataRollingOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataRollingOptionsSchema.JSON_PROPERTY_CENTER,
    ChartDataRollingOptionsSchema.JSON_PROPERTY_MIN_PERIODS,
    ChartDataRollingOptionsSchema.JSON_PROPERTY_ROLLING_TYPE,
    ChartDataRollingOptionsSchema.JSON_PROPERTY_ROLLING_TYPE_OPTIONS,
    ChartDataRollingOptionsSchema.JSON_PROPERTY_WIN_TYPE,
    ChartDataRollingOptionsSchema.JSON_PROPERTY_WINDOW
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataRollingOptionsSchema {
    public static final String JSON_PROPERTY_CENTER = "center";
    private Boolean center;

    public static final String JSON_PROPERTY_MIN_PERIODS = "min_periods";
    private Integer minPeriods;

    /**
     * Type of rolling window. Any numpy function will work.
     */
    public enum RollingTypeEnum {
        AVERAGE("average"),

        ARGMIN("argmin"),

        ARGMAX("argmax"),

        CUMSUM("cumsum"),

        CUMPROD("cumprod"),

        MAX("max"),

        MEAN("mean"),

        MEDIAN("median"),

        NANSUM("nansum"),

        NANMIN("nanmin"),

        NANMAX("nanmax"),

        NANMEAN("nanmean"),

        NANMEDIAN("nanmedian"),

        NANPERCENTILE("nanpercentile"),

        MIN("min"),

        PERCENTILE("percentile"),

        PROD("prod"),

        PRODUCT("product"),

        STD("std"),

        SUM("sum"),

        VAR("var");

        private String value;

        RollingTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static RollingTypeEnum fromValue(String value) {
            for (RollingTypeEnum b : RollingTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_ROLLING_TYPE = "rolling_type";
    private RollingTypeEnum rollingType;

    public static final String JSON_PROPERTY_ROLLING_TYPE_OPTIONS = "rolling_type_options";
    private Object rollingTypeOptions;

    /**
     * Type of window function. See [SciPy window functions](https://docs.scipy.org/doc/scipy/reference /signal.windows.html#module-scipy.signal.windows) for more details. Some window functions require passing additional parameters to &#x60;rolling_type_options&#x60;. For instance, to use &#x60;gaussian&#x60;, the parameter &#x60;std&#x60; needs to be provided.
     */
    public enum WinTypeEnum {
        BOXCAR("boxcar"),

        TRIANG("triang"),

        BLACKMAN("blackman"),

        HAMMING("hamming"),

        BARTLETT("bartlett"),

        PARZEN("parzen"),

        BOHMAN("bohman"),

        BLACKMANHARRIS("blackmanharris"),

        NUTTALL("nuttall"),

        BARTHANN("barthann"),

        KAISER("kaiser"),

        GAUSSIAN("gaussian"),

        GENERAL_GAUSSIAN("general_gaussian"),

        SLEPIAN("slepian"),

        EXPONENTIAL("exponential");

        private String value;

        WinTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static WinTypeEnum fromValue(String value) {
            for (WinTypeEnum b : WinTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_WIN_TYPE = "win_type";
    private WinTypeEnum winType;

    public static final String JSON_PROPERTY_WINDOW = "window";
    private Integer window;

    public ChartDataRollingOptionsSchema() {
    }

    public ChartDataRollingOptionsSchema center(Boolean center) {

        this.center = center;
        return this;
    }

    /**
     * Should the label be at the center of the window.Default: &#x60;false&#x60;
     *
     * @return center
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CENTER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getCenter() {
        return center;
    }


    @JsonProperty(JSON_PROPERTY_CENTER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCenter(Boolean center) {
        this.center = center;
    }


    public ChartDataRollingOptionsSchema minPeriods(Integer minPeriods) {

        this.minPeriods = minPeriods;
        return this;
    }

    /**
     * The minimum amount of periods required for a row to be included in the result set.
     *
     * @return minPeriods
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MIN_PERIODS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getMinPeriods() {
        return minPeriods;
    }


    @JsonProperty(JSON_PROPERTY_MIN_PERIODS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMinPeriods(Integer minPeriods) {
        this.minPeriods = minPeriods;
    }


    public ChartDataRollingOptionsSchema rollingType(RollingTypeEnum rollingType) {

        this.rollingType = rollingType;
        return this;
    }

    /**
     * Type of rolling window. Any numpy function will work.
     *
     * @return rollingType
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_ROLLING_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public RollingTypeEnum getRollingType() {
        return rollingType;
    }


    @JsonProperty(JSON_PROPERTY_ROLLING_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setRollingType(RollingTypeEnum rollingType) {
        this.rollingType = rollingType;
    }


    public ChartDataRollingOptionsSchema rollingTypeOptions(Object rollingTypeOptions) {

        this.rollingTypeOptions = rollingTypeOptions;
        return this;
    }

    /**
     * Optional options to pass to rolling method. Needed for e.g. quantile operation.
     *
     * @return rollingTypeOptions
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROLLING_TYPE_OPTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getRollingTypeOptions() {
        return rollingTypeOptions;
    }


    @JsonProperty(JSON_PROPERTY_ROLLING_TYPE_OPTIONS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRollingTypeOptions(Object rollingTypeOptions) {
        this.rollingTypeOptions = rollingTypeOptions;
    }


    public ChartDataRollingOptionsSchema winType(WinTypeEnum winType) {

        this.winType = winType;
        return this;
    }

    /**
     * Type of window function. See [SciPy window functions](https://docs.scipy.org/doc/scipy/reference /signal.windows.html#module-scipy.signal.windows) for more details. Some window functions require passing additional parameters to &#x60;rolling_type_options&#x60;. For instance, to use &#x60;gaussian&#x60;, the parameter &#x60;std&#x60; needs to be provided.
     *
     * @return winType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_WIN_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public WinTypeEnum getWinType() {
        return winType;
    }


    @JsonProperty(JSON_PROPERTY_WIN_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setWinType(WinTypeEnum winType) {
        this.winType = winType;
    }


    public ChartDataRollingOptionsSchema window(Integer window) {

        this.window = window;
        return this;
    }

    /**
     * Size of the rolling window in days.
     *
     * @return window
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_WINDOW)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getWindow() {
        return window;
    }


    @JsonProperty(JSON_PROPERTY_WINDOW)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setWindow(Integer window) {
        this.window = window;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataRollingOptionsSchema chartDataRollingOptionsSchema = (ChartDataRollingOptionsSchema) o;
        return Objects.equals(this.center, chartDataRollingOptionsSchema.center) &&
            Objects.equals(this.minPeriods, chartDataRollingOptionsSchema.minPeriods) &&
            Objects.equals(this.rollingType, chartDataRollingOptionsSchema.rollingType) &&
            Objects.equals(this.rollingTypeOptions, chartDataRollingOptionsSchema.rollingTypeOptions) &&
            Objects.equals(this.winType, chartDataRollingOptionsSchema.winType) &&
            Objects.equals(this.window, chartDataRollingOptionsSchema.window);
    }

    @Override
    public int hashCode() {
        return Objects.hash(center, minPeriods, rollingType, rollingTypeOptions, winType, window);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataRollingOptionsSchema {\n");
        sb.append("    center: ").append(toIndentedString(center)).append("\n");
        sb.append("    minPeriods: ").append(toIndentedString(minPeriods)).append("\n");
        sb.append("    rollingType: ").append(toIndentedString(rollingType)).append("\n");
        sb.append("    rollingTypeOptions: ").append(toIndentedString(rollingTypeOptions)).append("\n");
        sb.append("    winType: ").append(toIndentedString(winType)).append("\n");
        sb.append("    window: ").append(toIndentedString(window)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

