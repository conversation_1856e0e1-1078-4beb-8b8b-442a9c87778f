/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * AnnotationLayerRestApiGetList
 */
@JsonPropertyOrder({
    AnnotationLayerRestApiGetList.JSON_PROPERTY_CHANGED_BY,
    AnnotationLayerRestApiGetList.JSON_PROPERTY_CHANGED_ON,
    AnnotationLayerRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    AnnotationLayerRestApiGetList.JSON_PROPERTY_CREATED_BY,
    AnnotationLayerRestApiGetList.JSON_PROPERTY_CREATED_ON,
    AnnotationLayerRestApiGetList.JSON_PROPERTY_DESCR,
    AnnotationLayerRestApiGetList.JSON_PROPERTY_ID,
    AnnotationLayerRestApiGetList.JSON_PROPERTY_NAME
})
@JsonTypeName("AnnotationLayerRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class AnnotationLayerRestApiGetList {
    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private AnnotationLayerRestApiGetListUser1 changedBy;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private AnnotationLayerRestApiGetListUser createdBy;

    public static final String JSON_PROPERTY_CREATED_ON = "created_on";
    private OffsetDateTime createdOn;

    public static final String JSON_PROPERTY_DESCR = "descr";
    private String descr;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public AnnotationLayerRestApiGetList() {
    }

    @JsonCreator
    public AnnotationLayerRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
    }

    public AnnotationLayerRestApiGetList changedBy(AnnotationLayerRestApiGetListUser1 changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public AnnotationLayerRestApiGetListUser1 getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(AnnotationLayerRestApiGetListUser1 changedBy) {
        this.changedBy = changedBy;
    }


    public AnnotationLayerRestApiGetList changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public AnnotationLayerRestApiGetList createdBy(AnnotationLayerRestApiGetListUser createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public AnnotationLayerRestApiGetListUser getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(AnnotationLayerRestApiGetListUser createdBy) {
        this.createdBy = createdBy;
    }


    public AnnotationLayerRestApiGetList createdOn(OffsetDateTime createdOn) {

        this.createdOn = createdOn;
        return this;
    }

    /**
     * Get createdOn
     *
     * @return createdOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getCreatedOn() {
        return createdOn;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedOn(OffsetDateTime createdOn) {
        this.createdOn = createdOn;
    }


    public AnnotationLayerRestApiGetList descr(String descr) {

        this.descr = descr;
        return this;
    }

    /**
     * Get descr
     *
     * @return descr
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescr() {
        return descr;
    }


    @JsonProperty(JSON_PROPERTY_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescr(String descr) {
        this.descr = descr;
    }


    public AnnotationLayerRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public AnnotationLayerRestApiGetList name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AnnotationLayerRestApiGetList annotationLayerRestApiGetList = (AnnotationLayerRestApiGetList) o;
        return Objects.equals(this.changedBy, annotationLayerRestApiGetList.changedBy) &&
            Objects.equals(this.changedOn, annotationLayerRestApiGetList.changedOn) &&
            Objects.equals(this.changedOnDeltaHumanized, annotationLayerRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.createdBy, annotationLayerRestApiGetList.createdBy) &&
            Objects.equals(this.createdOn, annotationLayerRestApiGetList.createdOn) &&
            Objects.equals(this.descr, annotationLayerRestApiGetList.descr) &&
            Objects.equals(this.id, annotationLayerRestApiGetList.id) &&
            Objects.equals(this.name, annotationLayerRestApiGetList.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedBy, changedOn, changedOnDeltaHumanized, createdBy, createdOn, descr, id, name);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class AnnotationLayerRestApiGetList {\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
        sb.append("    descr: ").append(toIndentedString(descr)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

