/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * RelatedResultResponse
 */
@JsonPropertyOrder({
    RelatedResultResponse.JSON_PROPERTY_EXTRA,
    RelatedResultResponse.JSON_PROPERTY_TEXT,
    RelatedResultResponse.JSON_PROPERTY_VALUE
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class RelatedResultResponse {
    public static final String JSON_PROPERTY_EXTRA = "extra";
    private Object extra;

    public static final String JSON_PROPERTY_TEXT = "text";
    private String text;

    public static final String JSON_PROPERTY_VALUE = "value";
    private Integer value;

    public RelatedResultResponse() {
    }

    public RelatedResultResponse extra(Object extra) {

        this.extra = extra;
        return this;
    }

    /**
     * The extra metadata for related item
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(Object extra) {
        this.extra = extra;
    }


    public RelatedResultResponse text(String text) {

        this.text = text;
        return this;
    }

    /**
     * The related item string representation
     *
     * @return text
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getText() {
        return text;
    }


    @JsonProperty(JSON_PROPERTY_TEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setText(String text) {
        this.text = text;
    }


    public RelatedResultResponse value(Integer value) {

        this.value = value;
        return this;
    }

    /**
     * The related item identifier
     *
     * @return value
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getValue() {
        return value;
    }


    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValue(Integer value) {
        this.value = value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RelatedResultResponse relatedResultResponse = (RelatedResultResponse) o;
        return Objects.equals(this.extra, relatedResultResponse.extra) &&
            Objects.equals(this.text, relatedResultResponse.text) &&
            Objects.equals(this.value, relatedResultResponse.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(extra, text, value);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class RelatedResultResponse {\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    text: ").append(toIndentedString(text)).append("\n");
        sb.append("    value: ").append(toIndentedString(value)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

