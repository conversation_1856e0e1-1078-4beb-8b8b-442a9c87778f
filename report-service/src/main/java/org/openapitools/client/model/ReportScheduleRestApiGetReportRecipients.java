/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ReportScheduleRestApiGetReportRecipients
 */
@JsonPropertyOrder({
    ReportScheduleRestApiGetReportRecipients.JSON_PROPERTY_ID,
    ReportScheduleRestApiGetReportRecipients.JSON_PROPERTY_RECIPIENT_CONFIG_JSON,
    ReportScheduleRestApiGetReportRecipients.JSON_PROPERTY_TYPE
})
@JsonTypeName("ReportScheduleRestApi.get.ReportRecipients")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ReportScheduleRestApiGetReportRecipients {
    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_RECIPIENT_CONFIG_JSON = "recipient_config_json";
    private String recipientConfigJson;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public ReportScheduleRestApiGetReportRecipients() {
    }

    public ReportScheduleRestApiGetReportRecipients id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ReportScheduleRestApiGetReportRecipients recipientConfigJson(String recipientConfigJson) {

        this.recipientConfigJson = recipientConfigJson;
        return this;
    }

    /**
     * Get recipientConfigJson
     *
     * @return recipientConfigJson
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RECIPIENT_CONFIG_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getRecipientConfigJson() {
        return recipientConfigJson;
    }


    @JsonProperty(JSON_PROPERTY_RECIPIENT_CONFIG_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRecipientConfigJson(String recipientConfigJson) {
        this.recipientConfigJson = recipientConfigJson;
    }


    public ReportScheduleRestApiGetReportRecipients type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Get type
     *
     * @return type
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ReportScheduleRestApiGetReportRecipients reportScheduleRestApiGetReportRecipients = (ReportScheduleRestApiGetReportRecipients) o;
        return Objects.equals(this.id, reportScheduleRestApiGetReportRecipients.id) &&
            Objects.equals(this.recipientConfigJson, reportScheduleRestApiGetReportRecipients.recipientConfigJson) &&
            Objects.equals(this.type, reportScheduleRestApiGetReportRecipients.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, recipientConfigJson, type);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ReportScheduleRestApiGetReportRecipients {\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    recipientConfigJson: ").append(toIndentedString(recipientConfigJson)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

