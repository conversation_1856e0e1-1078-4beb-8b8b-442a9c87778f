/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * AnnotationLayerRestApiPost
 */
@JsonPropertyOrder({
    AnnotationLayerRestApiPost.JSON_PROPERTY_DESCR,
    AnnotationLayerRestApiPost.JSON_PROPERTY_NAME
})
@JsonTypeName("AnnotationLayerRestApi.post")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class AnnotationLayerRestApiPost {
    public static final String JSON_PROPERTY_DESCR = "descr";
    private String descr;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public AnnotationLayerRestApiPost() {
    }

    public AnnotationLayerRestApiPost descr(String descr) {

        this.descr = descr;
        return this;
    }

    /**
     * Give a description for this annotation layer
     *
     * @return descr
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescr() {
        return descr;
    }


    @JsonProperty(JSON_PROPERTY_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescr(String descr) {
        this.descr = descr;
    }


    public AnnotationLayerRestApiPost name(String name) {

        this.name = name;
        return this;
    }

    /**
     * The annotation layer name
     *
     * @return name
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AnnotationLayerRestApiPost annotationLayerRestApiPost = (AnnotationLayerRestApiPost) o;
        return Objects.equals(this.descr, annotationLayerRestApiPost.descr) &&
            Objects.equals(this.name, annotationLayerRestApiPost.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(descr, name);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class AnnotationLayerRestApiPost {\n");
        sb.append("    descr: ").append(toIndentedString(descr)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

