/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * QueryResult
 */
@JsonPropertyOrder({
    QueryResult.JSON_PROPERTY_CHANGED_ON,
    QueryResult.JSON_PROPERTY_CTAS,
    QueryResult.JSON_PROPERTY_DB,
    QueryResult.JSON_PROPERTY_DB_ID,
    QueryResult.JSON_PROPERTY_END_DTTM,
    QueryResult.JSON_PROPERTY_ERROR_MESSAGE,
    QueryResult.JSON_PROPERTY_EXECUTED_SQL,
    QueryResult.JSON_PROPERTY_EXTRA,
    QueryResult.JSON_PROPERTY_ID,
    QueryResult.JSON_PROPERTY_LIMIT,
    QueryResult.JSON_PROPERTY_LIMITING_FACTOR,
    QueryResult.JSON_PROPERTY_PROGRESS,
    QueryResult.JSON_PROPERTY_QUERY_ID,
    QueryResult.JSON_PROPERTY_RESULTS_KEY,
    QueryResult.JSON_PROPERTY_ROWS,
    QueryResult.JSON_PROPERTY_SCHEMA,
    QueryResult.JSON_PROPERTY_SERVER_ID,
    QueryResult.JSON_PROPERTY_SQL,
    QueryResult.JSON_PROPERTY_SQL_EDITOR_ID,
    QueryResult.JSON_PROPERTY_START_DTTM,
    QueryResult.JSON_PROPERTY_STATE,
    QueryResult.JSON_PROPERTY_TAB,
    QueryResult.JSON_PROPERTY_TEMP_SCHEMA,
    QueryResult.JSON_PROPERTY_TEMP_TABLE,
    QueryResult.JSON_PROPERTY_TRACKING_URL,
    QueryResult.JSON_PROPERTY_USER,
    QueryResult.JSON_PROPERTY_USER_ID
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class QueryResult {
    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CTAS = "ctas";
    private Boolean ctas;

    public static final String JSON_PROPERTY_DB = "db";
    private String db;

    public static final String JSON_PROPERTY_DB_ID = "dbId";
    private Integer dbId;

    public static final String JSON_PROPERTY_END_DTTM = "endDttm";
    private BigDecimal endDttm;

    public static final String JSON_PROPERTY_ERROR_MESSAGE = "errorMessage";
    private String errorMessage;

    public static final String JSON_PROPERTY_EXECUTED_SQL = "executedSql";
    private String executedSql;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private Object extra;

    public static final String JSON_PROPERTY_ID = "id";
    private String id;

    public static final String JSON_PROPERTY_LIMIT = "limit";
    private Integer limit;

    public static final String JSON_PROPERTY_LIMITING_FACTOR = "limitingFactor";
    private String limitingFactor;

    public static final String JSON_PROPERTY_PROGRESS = "progress";
    private Integer progress;

    public static final String JSON_PROPERTY_QUERY_ID = "queryId";
    private Integer queryId;

    public static final String JSON_PROPERTY_RESULTS_KEY = "resultsKey";
    private String resultsKey;

    public static final String JSON_PROPERTY_ROWS = "rows";
    private Integer rows;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SERVER_ID = "serverId";
    private Integer serverId;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_SQL_EDITOR_ID = "sqlEditorId";
    private String sqlEditorId;

    public static final String JSON_PROPERTY_START_DTTM = "startDttm";
    private BigDecimal startDttm;

    public static final String JSON_PROPERTY_STATE = "state";
    private String state;

    public static final String JSON_PROPERTY_TAB = "tab";
    private String tab;

    public static final String JSON_PROPERTY_TEMP_SCHEMA = "tempSchema";
    private String tempSchema;

    public static final String JSON_PROPERTY_TEMP_TABLE = "tempTable";
    private String tempTable;

    public static final String JSON_PROPERTY_TRACKING_URL = "trackingUrl";
    private String trackingUrl;

    public static final String JSON_PROPERTY_USER = "user";
    private String user;

    public static final String JSON_PROPERTY_USER_ID = "userId";
    private Integer userId;

    public QueryResult() {
    }

    public QueryResult changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public QueryResult ctas(Boolean ctas) {

        this.ctas = ctas;
        return this;
    }

    /**
     * Get ctas
     *
     * @return ctas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getCtas() {
        return ctas;
    }


    @JsonProperty(JSON_PROPERTY_CTAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCtas(Boolean ctas) {
        this.ctas = ctas;
    }


    public QueryResult db(String db) {

        this.db = db;
        return this;
    }

    /**
     * Get db
     *
     * @return db
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDb() {
        return db;
    }


    @JsonProperty(JSON_PROPERTY_DB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDb(String db) {
        this.db = db;
    }


    public QueryResult dbId(Integer dbId) {

        this.dbId = dbId;
        return this;
    }

    /**
     * Get dbId
     *
     * @return dbId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDbId() {
        return dbId;
    }


    @JsonProperty(JSON_PROPERTY_DB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDbId(Integer dbId) {
        this.dbId = dbId;
    }


    public QueryResult endDttm(BigDecimal endDttm) {

        this.endDttm = endDttm;
        return this;
    }

    /**
     * Get endDttm
     *
     * @return endDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getEndDttm() {
        return endDttm;
    }


    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEndDttm(BigDecimal endDttm) {
        this.endDttm = endDttm;
    }


    public QueryResult errorMessage(String errorMessage) {

        this.errorMessage = errorMessage;
        return this;
    }

    /**
     * Get errorMessage
     *
     * @return errorMessage
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getErrorMessage() {
        return errorMessage;
    }


    @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }


    public QueryResult executedSql(String executedSql) {

        this.executedSql = executedSql;
        return this;
    }

    /**
     * Get executedSql
     *
     * @return executedSql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXECUTED_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExecutedSql() {
        return executedSql;
    }


    @JsonProperty(JSON_PROPERTY_EXECUTED_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExecutedSql(String executedSql) {
        this.executedSql = executedSql;
    }


    public QueryResult extra(Object extra) {

        this.extra = extra;
        return this;
    }

    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(Object extra) {
        this.extra = extra;
    }


    public QueryResult id(String id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(String id) {
        this.id = id;
    }


    public QueryResult limit(Integer limit) {

        this.limit = limit;
        return this;
    }

    /**
     * Get limit
     *
     * @return limit
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getLimit() {
        return limit;
    }


    @JsonProperty(JSON_PROPERTY_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLimit(Integer limit) {
        this.limit = limit;
    }


    public QueryResult limitingFactor(String limitingFactor) {

        this.limitingFactor = limitingFactor;
        return this;
    }

    /**
     * Get limitingFactor
     *
     * @return limitingFactor
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LIMITING_FACTOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLimitingFactor() {
        return limitingFactor;
    }


    @JsonProperty(JSON_PROPERTY_LIMITING_FACTOR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLimitingFactor(String limitingFactor) {
        this.limitingFactor = limitingFactor;
    }


    public QueryResult progress(Integer progress) {

        this.progress = progress;
        return this;
    }

    /**
     * Get progress
     *
     * @return progress
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PROGRESS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getProgress() {
        return progress;
    }


    @JsonProperty(JSON_PROPERTY_PROGRESS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setProgress(Integer progress) {
        this.progress = progress;
    }


    public QueryResult queryId(Integer queryId) {

        this.queryId = queryId;
        return this;
    }

    /**
     * Get queryId
     *
     * @return queryId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getQueryId() {
        return queryId;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryId(Integer queryId) {
        this.queryId = queryId;
    }


    public QueryResult resultsKey(String resultsKey) {

        this.resultsKey = resultsKey;
        return this;
    }

    /**
     * Get resultsKey
     *
     * @return resultsKey
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULTS_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getResultsKey() {
        return resultsKey;
    }


    @JsonProperty(JSON_PROPERTY_RESULTS_KEY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResultsKey(String resultsKey) {
        this.resultsKey = resultsKey;
    }


    public QueryResult rows(Integer rows) {

        this.rows = rows;
        return this;
    }

    /**
     * Get rows
     *
     * @return rows
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROWS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getRows() {
        return rows;
    }


    @JsonProperty(JSON_PROPERTY_ROWS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRows(Integer rows) {
        this.rows = rows;
    }


    public QueryResult schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public QueryResult serverId(Integer serverId) {

        this.serverId = serverId;
        return this;
    }

    /**
     * Get serverId
     *
     * @return serverId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SERVER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getServerId() {
        return serverId;
    }


    @JsonProperty(JSON_PROPERTY_SERVER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setServerId(Integer serverId) {
        this.serverId = serverId;
    }


    public QueryResult sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public QueryResult sqlEditorId(String sqlEditorId) {

        this.sqlEditorId = sqlEditorId;
        return this;
    }

    /**
     * Get sqlEditorId
     *
     * @return sqlEditorId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL_EDITOR_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSqlEditorId() {
        return sqlEditorId;
    }


    @JsonProperty(JSON_PROPERTY_SQL_EDITOR_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSqlEditorId(String sqlEditorId) {
        this.sqlEditorId = sqlEditorId;
    }


    public QueryResult startDttm(BigDecimal startDttm) {

        this.startDttm = startDttm;
        return this;
    }

    /**
     * Get startDttm
     *
     * @return startDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getStartDttm() {
        return startDttm;
    }


    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStartDttm(BigDecimal startDttm) {
        this.startDttm = startDttm;
    }


    public QueryResult state(String state) {

        this.state = state;
        return this;
    }

    /**
     * Get state
     *
     * @return state
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getState() {
        return state;
    }


    @JsonProperty(JSON_PROPERTY_STATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setState(String state) {
        this.state = state;
    }


    public QueryResult tab(String tab) {

        this.tab = tab;
        return this;
    }

    /**
     * Get tab
     *
     * @return tab
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTab() {
        return tab;
    }


    @JsonProperty(JSON_PROPERTY_TAB)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTab(String tab) {
        this.tab = tab;
    }


    public QueryResult tempSchema(String tempSchema) {

        this.tempSchema = tempSchema;
        return this;
    }

    /**
     * Get tempSchema
     *
     * @return tempSchema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMP_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTempSchema() {
        return tempSchema;
    }


    @JsonProperty(JSON_PROPERTY_TEMP_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTempSchema(String tempSchema) {
        this.tempSchema = tempSchema;
    }


    public QueryResult tempTable(String tempTable) {

        this.tempTable = tempTable;
        return this;
    }

    /**
     * Get tempTable
     *
     * @return tempTable
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMP_TABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTempTable() {
        return tempTable;
    }


    @JsonProperty(JSON_PROPERTY_TEMP_TABLE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTempTable(String tempTable) {
        this.tempTable = tempTable;
    }


    public QueryResult trackingUrl(String trackingUrl) {

        this.trackingUrl = trackingUrl;
        return this;
    }

    /**
     * Get trackingUrl
     *
     * @return trackingUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TRACKING_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTrackingUrl() {
        return trackingUrl;
    }


    @JsonProperty(JSON_PROPERTY_TRACKING_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTrackingUrl(String trackingUrl) {
        this.trackingUrl = trackingUrl;
    }


    public QueryResult user(String user) {

        this.user = user;
        return this;
    }

    /**
     * Get user
     *
     * @return user
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUser() {
        return user;
    }


    @JsonProperty(JSON_PROPERTY_USER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUser(String user) {
        this.user = user;
    }


    public QueryResult userId(Integer userId) {

        this.userId = userId;
        return this;
    }

    /**
     * Get userId
     *
     * @return userId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getUserId() {
        return userId;
    }


    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        QueryResult queryResult = (QueryResult) o;
        return Objects.equals(this.changedOn, queryResult.changedOn) &&
            Objects.equals(this.ctas, queryResult.ctas) &&
            Objects.equals(this.db, queryResult.db) &&
            Objects.equals(this.dbId, queryResult.dbId) &&
            Objects.equals(this.endDttm, queryResult.endDttm) &&
            Objects.equals(this.errorMessage, queryResult.errorMessage) &&
            Objects.equals(this.executedSql, queryResult.executedSql) &&
            Objects.equals(this.extra, queryResult.extra) &&
            Objects.equals(this.id, queryResult.id) &&
            Objects.equals(this.limit, queryResult.limit) &&
            Objects.equals(this.limitingFactor, queryResult.limitingFactor) &&
            Objects.equals(this.progress, queryResult.progress) &&
            Objects.equals(this.queryId, queryResult.queryId) &&
            Objects.equals(this.resultsKey, queryResult.resultsKey) &&
            Objects.equals(this.rows, queryResult.rows) &&
            Objects.equals(this.schema, queryResult.schema) &&
            Objects.equals(this.serverId, queryResult.serverId) &&
            Objects.equals(this.sql, queryResult.sql) &&
            Objects.equals(this.sqlEditorId, queryResult.sqlEditorId) &&
            Objects.equals(this.startDttm, queryResult.startDttm) &&
            Objects.equals(this.state, queryResult.state) &&
            Objects.equals(this.tab, queryResult.tab) &&
            Objects.equals(this.tempSchema, queryResult.tempSchema) &&
            Objects.equals(this.tempTable, queryResult.tempTable) &&
            Objects.equals(this.trackingUrl, queryResult.trackingUrl) &&
            Objects.equals(this.user, queryResult.user) &&
            Objects.equals(this.userId, queryResult.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(changedOn, ctas, db, dbId, endDttm, errorMessage, executedSql, extra, id, limit, limitingFactor, progress, queryId, resultsKey, rows, schema, serverId, sql, sqlEditorId, startDttm, state, tab, tempSchema, tempTable, trackingUrl, user, userId);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class QueryResult {\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    ctas: ").append(toIndentedString(ctas)).append("\n");
        sb.append("    db: ").append(toIndentedString(db)).append("\n");
        sb.append("    dbId: ").append(toIndentedString(dbId)).append("\n");
        sb.append("    endDttm: ").append(toIndentedString(endDttm)).append("\n");
        sb.append("    errorMessage: ").append(toIndentedString(errorMessage)).append("\n");
        sb.append("    executedSql: ").append(toIndentedString(executedSql)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    limit: ").append(toIndentedString(limit)).append("\n");
        sb.append("    limitingFactor: ").append(toIndentedString(limitingFactor)).append("\n");
        sb.append("    progress: ").append(toIndentedString(progress)).append("\n");
        sb.append("    queryId: ").append(toIndentedString(queryId)).append("\n");
        sb.append("    resultsKey: ").append(toIndentedString(resultsKey)).append("\n");
        sb.append("    rows: ").append(toIndentedString(rows)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    serverId: ").append(toIndentedString(serverId)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    sqlEditorId: ").append(toIndentedString(sqlEditorId)).append("\n");
        sb.append("    startDttm: ").append(toIndentedString(startDttm)).append("\n");
        sb.append("    state: ").append(toIndentedString(state)).append("\n");
        sb.append("    tab: ").append(toIndentedString(tab)).append("\n");
        sb.append("    tempSchema: ").append(toIndentedString(tempSchema)).append("\n");
        sb.append("    tempTable: ").append(toIndentedString(tempTable)).append("\n");
        sb.append("    trackingUrl: ").append(toIndentedString(trackingUrl)).append("\n");
        sb.append("    user: ").append(toIndentedString(user)).append("\n");
        sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

