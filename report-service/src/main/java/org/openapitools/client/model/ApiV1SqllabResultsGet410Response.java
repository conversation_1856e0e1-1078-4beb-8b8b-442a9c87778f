/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1SqllabResultsGet410Response
 */
@JsonPropertyOrder({
    ApiV1SqllabResultsGet410Response.JSON_PROPERTY_ERRORS,
    ApiV1SqllabResultsGet410Response.JSON_PROPERTY_MESSAGE
})
@JsonTypeName("_api_v1_sqllab_results__get_410_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1SqllabResultsGet410Response {
    public static final String JSON_PROPERTY_ERRORS = "errors";
    private List<ApiV1SqllabResultsGet410ResponseErrorsInner> errors;

    public static final String JSON_PROPERTY_MESSAGE = "message";
    private String message;

    public ApiV1SqllabResultsGet410Response() {
    }

    public ApiV1SqllabResultsGet410Response errors(List<ApiV1SqllabResultsGet410ResponseErrorsInner> errors) {

        this.errors = errors;
        return this;
    }

    public ApiV1SqllabResultsGet410Response addErrorsItem(ApiV1SqllabResultsGet410ResponseErrorsInner errorsItem) {
        if (this.errors == null) {
            this.errors = new ArrayList<>();
        }
        this.errors.add(errorsItem);
        return this;
    }

    /**
     * Get errors
     *
     * @return errors
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ERRORS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<ApiV1SqllabResultsGet410ResponseErrorsInner> getErrors() {
        return errors;
    }


    @JsonProperty(JSON_PROPERTY_ERRORS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setErrors(List<ApiV1SqllabResultsGet410ResponseErrorsInner> errors) {
        this.errors = errors;
    }


    public ApiV1SqllabResultsGet410Response message(String message) {

        this.message = message;
        return this;
    }

    /**
     * Get message
     *
     * @return message
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMessage() {
        return message;
    }


    @JsonProperty(JSON_PROPERTY_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1SqllabResultsGet410Response apiV1SqllabResultsGet410Response = (ApiV1SqllabResultsGet410Response) o;
        return Objects.equals(this.errors, apiV1SqllabResultsGet410Response.errors) &&
            Objects.equals(this.message, apiV1SqllabResultsGet410Response.message);
    }

    @Override
    public int hashCode() {
        return Objects.hash(errors, message);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1SqllabResultsGet410Response {\n");
        sb.append("    errors: ").append(toIndentedString(errors)).append("\n");
        sb.append("    message: ").append(toIndentedString(message)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

