/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * ReportScheduleRestApiGet
 */
@JsonPropertyOrder({
    ReportScheduleRestApiGet.JSON_PROPERTY_ACTIVE,
    ReportScheduleRestApiGet.JSON_PROPERTY_CHART,
    ReportScheduleRestApiGet.JSON_PROPERTY_CONTEXT_MARKDOWN,
    ReportScheduleRestApiGet.JSON_PROPERTY_CREATION_METHOD,
    ReportScheduleRestApiGet.JSON_PROPERTY_CRONTAB,
    ReportScheduleRestApiGet.JSON_PROPERTY_CUSTOM_WIDTH,
    ReportScheduleRestApiGet.JSON_PROPERTY_DASHBOARD,
    ReportScheduleRestApiGet.JSON_PROPERTY_DATABASE,
    ReportScheduleRestApiGet.JSON_PROPERTY_DESCRIPTION,
    ReportScheduleRestApiGet.JSON_PROPERTY_EXTRA,
    ReportScheduleRestApiGet.JSON_PROPERTY_FORCE_SCREENSHOT,
    ReportScheduleRestApiGet.JSON_PROPERTY_GRACE_PERIOD,
    ReportScheduleRestApiGet.JSON_PROPERTY_ID,
    ReportScheduleRestApiGet.JSON_PROPERTY_LAST_EVAL_DTTM,
    ReportScheduleRestApiGet.JSON_PROPERTY_LAST_STATE,
    ReportScheduleRestApiGet.JSON_PROPERTY_LAST_VALUE,
    ReportScheduleRestApiGet.JSON_PROPERTY_LAST_VALUE_ROW_JSON,
    ReportScheduleRestApiGet.JSON_PROPERTY_LOG_RETENTION,
    ReportScheduleRestApiGet.JSON_PROPERTY_NAME,
    ReportScheduleRestApiGet.JSON_PROPERTY_OWNERS,
    ReportScheduleRestApiGet.JSON_PROPERTY_RECIPIENTS,
    ReportScheduleRestApiGet.JSON_PROPERTY_REPORT_FORMAT,
    ReportScheduleRestApiGet.JSON_PROPERTY_SQL,
    ReportScheduleRestApiGet.JSON_PROPERTY_TIMEZONE,
    ReportScheduleRestApiGet.JSON_PROPERTY_TYPE,
    ReportScheduleRestApiGet.JSON_PROPERTY_VALIDATOR_CONFIG_JSON,
    ReportScheduleRestApiGet.JSON_PROPERTY_VALIDATOR_TYPE,
    ReportScheduleRestApiGet.JSON_PROPERTY_WORKING_TIMEOUT
})
@JsonTypeName("ReportScheduleRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ReportScheduleRestApiGet {
    public static final String JSON_PROPERTY_ACTIVE = "active";
    private Boolean active;

    public static final String JSON_PROPERTY_CHART = "chart";
    private ReportScheduleRestApiGetSlice chart;

    public static final String JSON_PROPERTY_CONTEXT_MARKDOWN = "context_markdown";
    private String contextMarkdown;

    public static final String JSON_PROPERTY_CREATION_METHOD = "creation_method";
    private String creationMethod;

    public static final String JSON_PROPERTY_CRONTAB = "crontab";
    private String crontab;

    public static final String JSON_PROPERTY_CUSTOM_WIDTH = "custom_width";
    private Integer customWidth;

    public static final String JSON_PROPERTY_DASHBOARD = "dashboard";
    private ReportScheduleRestApiGetDashboard dashboard;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private ReportScheduleRestApiGetDatabase database;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private Object extra = null;

    public static final String JSON_PROPERTY_FORCE_SCREENSHOT = "force_screenshot";
    private Boolean forceScreenshot;

    public static final String JSON_PROPERTY_GRACE_PERIOD = "grace_period";
    private Integer gracePeriod;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_LAST_EVAL_DTTM = "last_eval_dttm";
    private OffsetDateTime lastEvalDttm;

    public static final String JSON_PROPERTY_LAST_STATE = "last_state";
    private String lastState;

    public static final String JSON_PROPERTY_LAST_VALUE = "last_value";
    private BigDecimal lastValue;

    public static final String JSON_PROPERTY_LAST_VALUE_ROW_JSON = "last_value_row_json";
    private String lastValueRowJson;

    public static final String JSON_PROPERTY_LOG_RETENTION = "log_retention";
    private Integer logRetention;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private ReportScheduleRestApiGetUser owners;

    public static final String JSON_PROPERTY_RECIPIENTS = "recipients";
    private ReportScheduleRestApiGetReportRecipients recipients;

    public static final String JSON_PROPERTY_REPORT_FORMAT = "report_format";
    private String reportFormat;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TIMEZONE = "timezone";
    private String timezone;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public static final String JSON_PROPERTY_VALIDATOR_CONFIG_JSON = "validator_config_json";
    private String validatorConfigJson;

    public static final String JSON_PROPERTY_VALIDATOR_TYPE = "validator_type";
    private String validatorType;

    public static final String JSON_PROPERTY_WORKING_TIMEOUT = "working_timeout";
    private Integer workingTimeout;

    public ReportScheduleRestApiGet() {
    }

    @JsonCreator
    public ReportScheduleRestApiGet(
        @JsonProperty(JSON_PROPERTY_EXTRA) Object extra
    ) {
        this();
        this.extra = extra;
    }

    public ReportScheduleRestApiGet active(Boolean active) {

        this.active = active;
        return this;
    }

    /**
     * Get active
     *
     * @return active
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getActive() {
        return active;
    }


    @JsonProperty(JSON_PROPERTY_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setActive(Boolean active) {
        this.active = active;
    }


    public ReportScheduleRestApiGet chart(ReportScheduleRestApiGetSlice chart) {

        this.chart = chart;
        return this;
    }

    /**
     * Get chart
     *
     * @return chart
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHART)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ReportScheduleRestApiGetSlice getChart() {
        return chart;
    }


    @JsonProperty(JSON_PROPERTY_CHART)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChart(ReportScheduleRestApiGetSlice chart) {
        this.chart = chart;
    }


    public ReportScheduleRestApiGet contextMarkdown(String contextMarkdown) {

        this.contextMarkdown = contextMarkdown;
        return this;
    }

    /**
     * Get contextMarkdown
     *
     * @return contextMarkdown
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CONTEXT_MARKDOWN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getContextMarkdown() {
        return contextMarkdown;
    }


    @JsonProperty(JSON_PROPERTY_CONTEXT_MARKDOWN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setContextMarkdown(String contextMarkdown) {
        this.contextMarkdown = contextMarkdown;
    }


    public ReportScheduleRestApiGet creationMethod(String creationMethod) {

        this.creationMethod = creationMethod;
        return this;
    }

    /**
     * Get creationMethod
     *
     * @return creationMethod
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCreationMethod() {
        return creationMethod;
    }


    @JsonProperty(JSON_PROPERTY_CREATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreationMethod(String creationMethod) {
        this.creationMethod = creationMethod;
    }


    public ReportScheduleRestApiGet crontab(String crontab) {

        this.crontab = crontab;
        return this;
    }

    /**
     * Get crontab
     *
     * @return crontab
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_CRONTAB)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getCrontab() {
        return crontab;
    }


    @JsonProperty(JSON_PROPERTY_CRONTAB)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setCrontab(String crontab) {
        this.crontab = crontab;
    }


    public ReportScheduleRestApiGet customWidth(Integer customWidth) {

        this.customWidth = customWidth;
        return this;
    }

    /**
     * Get customWidth
     *
     * @return customWidth
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CUSTOM_WIDTH)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCustomWidth() {
        return customWidth;
    }


    @JsonProperty(JSON_PROPERTY_CUSTOM_WIDTH)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCustomWidth(Integer customWidth) {
        this.customWidth = customWidth;
    }


    public ReportScheduleRestApiGet dashboard(ReportScheduleRestApiGetDashboard dashboard) {

        this.dashboard = dashboard;
        return this;
    }

    /**
     * Get dashboard
     *
     * @return dashboard
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ReportScheduleRestApiGetDashboard getDashboard() {
        return dashboard;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboard(ReportScheduleRestApiGetDashboard dashboard) {
        this.dashboard = dashboard;
    }


    public ReportScheduleRestApiGet database(ReportScheduleRestApiGetDatabase database) {

        this.database = database;
        return this;
    }

    /**
     * Get database
     *
     * @return database
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ReportScheduleRestApiGetDatabase getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabase(ReportScheduleRestApiGetDatabase database) {
        this.database = database;
    }


    public ReportScheduleRestApiGet description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExtra() {
        return extra;
    }


    public ReportScheduleRestApiGet forceScreenshot(Boolean forceScreenshot) {

        this.forceScreenshot = forceScreenshot;
        return this;
    }

    /**
     * Get forceScreenshot
     *
     * @return forceScreenshot
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORCE_SCREENSHOT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getForceScreenshot() {
        return forceScreenshot;
    }


    @JsonProperty(JSON_PROPERTY_FORCE_SCREENSHOT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setForceScreenshot(Boolean forceScreenshot) {
        this.forceScreenshot = forceScreenshot;
    }


    public ReportScheduleRestApiGet gracePeriod(Integer gracePeriod) {

        this.gracePeriod = gracePeriod;
        return this;
    }

    /**
     * Get gracePeriod
     *
     * @return gracePeriod
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GRACE_PERIOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getGracePeriod() {
        return gracePeriod;
    }


    @JsonProperty(JSON_PROPERTY_GRACE_PERIOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGracePeriod(Integer gracePeriod) {
        this.gracePeriod = gracePeriod;
    }


    public ReportScheduleRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ReportScheduleRestApiGet lastEvalDttm(OffsetDateTime lastEvalDttm) {

        this.lastEvalDttm = lastEvalDttm;
        return this;
    }

    /**
     * Get lastEvalDttm
     *
     * @return lastEvalDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_EVAL_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getLastEvalDttm() {
        return lastEvalDttm;
    }


    @JsonProperty(JSON_PROPERTY_LAST_EVAL_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastEvalDttm(OffsetDateTime lastEvalDttm) {
        this.lastEvalDttm = lastEvalDttm;
    }


    public ReportScheduleRestApiGet lastState(String lastState) {

        this.lastState = lastState;
        return this;
    }

    /**
     * Get lastState
     *
     * @return lastState
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_STATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLastState() {
        return lastState;
    }


    @JsonProperty(JSON_PROPERTY_LAST_STATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastState(String lastState) {
        this.lastState = lastState;
    }


    public ReportScheduleRestApiGet lastValue(BigDecimal lastValue) {

        this.lastValue = lastValue;
        return this;
    }

    /**
     * Get lastValue
     *
     * @return lastValue
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getLastValue() {
        return lastValue;
    }


    @JsonProperty(JSON_PROPERTY_LAST_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastValue(BigDecimal lastValue) {
        this.lastValue = lastValue;
    }


    public ReportScheduleRestApiGet lastValueRowJson(String lastValueRowJson) {

        this.lastValueRowJson = lastValueRowJson;
        return this;
    }

    /**
     * Get lastValueRowJson
     *
     * @return lastValueRowJson
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_VALUE_ROW_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLastValueRowJson() {
        return lastValueRowJson;
    }


    @JsonProperty(JSON_PROPERTY_LAST_VALUE_ROW_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastValueRowJson(String lastValueRowJson) {
        this.lastValueRowJson = lastValueRowJson;
    }


    public ReportScheduleRestApiGet logRetention(Integer logRetention) {

        this.logRetention = logRetention;
        return this;
    }

    /**
     * Get logRetention
     *
     * @return logRetention
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LOG_RETENTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getLogRetention() {
        return logRetention;
    }


    @JsonProperty(JSON_PROPERTY_LOG_RETENTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLogRetention(Integer logRetention) {
        this.logRetention = logRetention;
    }


    public ReportScheduleRestApiGet name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setName(String name) {
        this.name = name;
    }


    public ReportScheduleRestApiGet owners(ReportScheduleRestApiGetUser owners) {

        this.owners = owners;
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ReportScheduleRestApiGetUser getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(ReportScheduleRestApiGetUser owners) {
        this.owners = owners;
    }


    public ReportScheduleRestApiGet recipients(ReportScheduleRestApiGetReportRecipients recipients) {

        this.recipients = recipients;
        return this;
    }

    /**
     * Get recipients
     *
     * @return recipients
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_RECIPIENTS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public ReportScheduleRestApiGetReportRecipients getRecipients() {
        return recipients;
    }


    @JsonProperty(JSON_PROPERTY_RECIPIENTS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setRecipients(ReportScheduleRestApiGetReportRecipients recipients) {
        this.recipients = recipients;
    }


    public ReportScheduleRestApiGet reportFormat(String reportFormat) {

        this.reportFormat = reportFormat;
        return this;
    }

    /**
     * Get reportFormat
     *
     * @return reportFormat
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_REPORT_FORMAT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getReportFormat() {
        return reportFormat;
    }


    @JsonProperty(JSON_PROPERTY_REPORT_FORMAT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setReportFormat(String reportFormat) {
        this.reportFormat = reportFormat;
    }


    public ReportScheduleRestApiGet sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public ReportScheduleRestApiGet timezone(String timezone) {

        this.timezone = timezone;
        return this;
    }

    /**
     * Get timezone
     *
     * @return timezone
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIMEZONE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTimezone() {
        return timezone;
    }


    @JsonProperty(JSON_PROPERTY_TIMEZONE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }


    public ReportScheduleRestApiGet type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Get type
     *
     * @return type
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setType(String type) {
        this.type = type;
    }


    public ReportScheduleRestApiGet validatorConfigJson(String validatorConfigJson) {

        this.validatorConfigJson = validatorConfigJson;
        return this;
    }

    /**
     * Get validatorConfigJson
     *
     * @return validatorConfigJson
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALIDATOR_CONFIG_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getValidatorConfigJson() {
        return validatorConfigJson;
    }


    @JsonProperty(JSON_PROPERTY_VALIDATOR_CONFIG_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValidatorConfigJson(String validatorConfigJson) {
        this.validatorConfigJson = validatorConfigJson;
    }


    public ReportScheduleRestApiGet validatorType(String validatorType) {

        this.validatorType = validatorType;
        return this;
    }

    /**
     * Get validatorType
     *
     * @return validatorType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALIDATOR_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getValidatorType() {
        return validatorType;
    }


    @JsonProperty(JSON_PROPERTY_VALIDATOR_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValidatorType(String validatorType) {
        this.validatorType = validatorType;
    }


    public ReportScheduleRestApiGet workingTimeout(Integer workingTimeout) {

        this.workingTimeout = workingTimeout;
        return this;
    }

    /**
     * Get workingTimeout
     *
     * @return workingTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_WORKING_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getWorkingTimeout() {
        return workingTimeout;
    }


    @JsonProperty(JSON_PROPERTY_WORKING_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setWorkingTimeout(Integer workingTimeout) {
        this.workingTimeout = workingTimeout;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ReportScheduleRestApiGet reportScheduleRestApiGet = (ReportScheduleRestApiGet) o;
        return Objects.equals(this.active, reportScheduleRestApiGet.active) &&
            Objects.equals(this.chart, reportScheduleRestApiGet.chart) &&
            Objects.equals(this.contextMarkdown, reportScheduleRestApiGet.contextMarkdown) &&
            Objects.equals(this.creationMethod, reportScheduleRestApiGet.creationMethod) &&
            Objects.equals(this.crontab, reportScheduleRestApiGet.crontab) &&
            Objects.equals(this.customWidth, reportScheduleRestApiGet.customWidth) &&
            Objects.equals(this.dashboard, reportScheduleRestApiGet.dashboard) &&
            Objects.equals(this.database, reportScheduleRestApiGet.database) &&
            Objects.equals(this.description, reportScheduleRestApiGet.description) &&
            Objects.equals(this.extra, reportScheduleRestApiGet.extra) &&
            Objects.equals(this.forceScreenshot, reportScheduleRestApiGet.forceScreenshot) &&
            Objects.equals(this.gracePeriod, reportScheduleRestApiGet.gracePeriod) &&
            Objects.equals(this.id, reportScheduleRestApiGet.id) &&
            Objects.equals(this.lastEvalDttm, reportScheduleRestApiGet.lastEvalDttm) &&
            Objects.equals(this.lastState, reportScheduleRestApiGet.lastState) &&
            Objects.equals(this.lastValue, reportScheduleRestApiGet.lastValue) &&
            Objects.equals(this.lastValueRowJson, reportScheduleRestApiGet.lastValueRowJson) &&
            Objects.equals(this.logRetention, reportScheduleRestApiGet.logRetention) &&
            Objects.equals(this.name, reportScheduleRestApiGet.name) &&
            Objects.equals(this.owners, reportScheduleRestApiGet.owners) &&
            Objects.equals(this.recipients, reportScheduleRestApiGet.recipients) &&
            Objects.equals(this.reportFormat, reportScheduleRestApiGet.reportFormat) &&
            Objects.equals(this.sql, reportScheduleRestApiGet.sql) &&
            Objects.equals(this.timezone, reportScheduleRestApiGet.timezone) &&
            Objects.equals(this.type, reportScheduleRestApiGet.type) &&
            Objects.equals(this.validatorConfigJson, reportScheduleRestApiGet.validatorConfigJson) &&
            Objects.equals(this.validatorType, reportScheduleRestApiGet.validatorType) &&
            Objects.equals(this.workingTimeout, reportScheduleRestApiGet.workingTimeout);
    }

    @Override
    public int hashCode() {
        return Objects.hash(active, chart, contextMarkdown, creationMethod, crontab, customWidth, dashboard, database, description, extra, forceScreenshot, gracePeriod, id, lastEvalDttm, lastState, lastValue, lastValueRowJson, logRetention, name, owners, recipients, reportFormat, sql, timezone, type, validatorConfigJson, validatorType, workingTimeout);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ReportScheduleRestApiGet {\n");
        sb.append("    active: ").append(toIndentedString(active)).append("\n");
        sb.append("    chart: ").append(toIndentedString(chart)).append("\n");
        sb.append("    contextMarkdown: ").append(toIndentedString(contextMarkdown)).append("\n");
        sb.append("    creationMethod: ").append(toIndentedString(creationMethod)).append("\n");
        sb.append("    crontab: ").append(toIndentedString(crontab)).append("\n");
        sb.append("    customWidth: ").append(toIndentedString(customWidth)).append("\n");
        sb.append("    dashboard: ").append(toIndentedString(dashboard)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    forceScreenshot: ").append(toIndentedString(forceScreenshot)).append("\n");
        sb.append("    gracePeriod: ").append(toIndentedString(gracePeriod)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    lastEvalDttm: ").append(toIndentedString(lastEvalDttm)).append("\n");
        sb.append("    lastState: ").append(toIndentedString(lastState)).append("\n");
        sb.append("    lastValue: ").append(toIndentedString(lastValue)).append("\n");
        sb.append("    lastValueRowJson: ").append(toIndentedString(lastValueRowJson)).append("\n");
        sb.append("    logRetention: ").append(toIndentedString(logRetention)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    recipients: ").append(toIndentedString(recipients)).append("\n");
        sb.append("    reportFormat: ").append(toIndentedString(reportFormat)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    timezone: ").append(toIndentedString(timezone)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("    validatorConfigJson: ").append(toIndentedString(validatorConfigJson)).append("\n");
        sb.append("    validatorType: ").append(toIndentedString(validatorType)).append("\n");
        sb.append("    workingTimeout: ").append(toIndentedString(workingTimeout)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

