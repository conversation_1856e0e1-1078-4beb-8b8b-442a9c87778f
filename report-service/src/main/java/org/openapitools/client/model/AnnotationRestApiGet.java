/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * AnnotationRestApiGet
 */
@JsonPropertyOrder({
    AnnotationRestApiGet.JSON_PROPERTY_END_DTTM,
    AnnotationRestApiGet.JSON_PROPERTY_ID,
    AnnotationRestApiGet.JSON_PROPERTY_JSON_METADATA,
    AnnotationRestApiGet.JSON_PROPERTY_LAYER,
    AnnotationRestApiGet.JSON_PROPERTY_LONG_DESCR,
    AnnotationRestApiGet.JSON_PROPERTY_SHORT_DESCR,
    AnnotationRestApiGet.JSON_PROPERTY_START_DTTM
})
@JsonTypeName("AnnotationRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class AnnotationRestApiGet {
    public static final String JSON_PROPERTY_END_DTTM = "end_dttm";
    private OffsetDateTime endDttm;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_JSON_METADATA = "json_metadata";
    private String jsonMetadata;

    public static final String JSON_PROPERTY_LAYER = "layer";
    private AnnotationRestApiGetAnnotationLayer layer;

    public static final String JSON_PROPERTY_LONG_DESCR = "long_descr";
    private String longDescr;

    public static final String JSON_PROPERTY_SHORT_DESCR = "short_descr";
    private String shortDescr;

    public static final String JSON_PROPERTY_START_DTTM = "start_dttm";
    private OffsetDateTime startDttm;

    public AnnotationRestApiGet() {
    }

    public AnnotationRestApiGet endDttm(OffsetDateTime endDttm) {

        this.endDttm = endDttm;
        return this;
    }

    /**
     * Get endDttm
     *
     * @return endDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getEndDttm() {
        return endDttm;
    }


    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEndDttm(OffsetDateTime endDttm) {
        this.endDttm = endDttm;
    }


    public AnnotationRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public AnnotationRestApiGet jsonMetadata(String jsonMetadata) {

        this.jsonMetadata = jsonMetadata;
        return this;
    }

    /**
     * Get jsonMetadata
     *
     * @return jsonMetadata
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJsonMetadata() {
        return jsonMetadata;
    }


    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJsonMetadata(String jsonMetadata) {
        this.jsonMetadata = jsonMetadata;
    }


    public AnnotationRestApiGet layer(AnnotationRestApiGetAnnotationLayer layer) {

        this.layer = layer;
        return this;
    }

    /**
     * Get layer
     *
     * @return layer
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_LAYER)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public AnnotationRestApiGetAnnotationLayer getLayer() {
        return layer;
    }


    @JsonProperty(JSON_PROPERTY_LAYER)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setLayer(AnnotationRestApiGetAnnotationLayer layer) {
        this.layer = layer;
    }


    public AnnotationRestApiGet longDescr(String longDescr) {

        this.longDescr = longDescr;
        return this;
    }

    /**
     * Get longDescr
     *
     * @return longDescr
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LONG_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLongDescr() {
        return longDescr;
    }


    @JsonProperty(JSON_PROPERTY_LONG_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLongDescr(String longDescr) {
        this.longDescr = longDescr;
    }


    public AnnotationRestApiGet shortDescr(String shortDescr) {

        this.shortDescr = shortDescr;
        return this;
    }

    /**
     * Get shortDescr
     *
     * @return shortDescr
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SHORT_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getShortDescr() {
        return shortDescr;
    }


    @JsonProperty(JSON_PROPERTY_SHORT_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setShortDescr(String shortDescr) {
        this.shortDescr = shortDescr;
    }


    public AnnotationRestApiGet startDttm(OffsetDateTime startDttm) {

        this.startDttm = startDttm;
        return this;
    }

    /**
     * Get startDttm
     *
     * @return startDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getStartDttm() {
        return startDttm;
    }


    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStartDttm(OffsetDateTime startDttm) {
        this.startDttm = startDttm;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AnnotationRestApiGet annotationRestApiGet = (AnnotationRestApiGet) o;
        return Objects.equals(this.endDttm, annotationRestApiGet.endDttm) &&
            Objects.equals(this.id, annotationRestApiGet.id) &&
            Objects.equals(this.jsonMetadata, annotationRestApiGet.jsonMetadata) &&
            Objects.equals(this.layer, annotationRestApiGet.layer) &&
            Objects.equals(this.longDescr, annotationRestApiGet.longDescr) &&
            Objects.equals(this.shortDescr, annotationRestApiGet.shortDescr) &&
            Objects.equals(this.startDttm, annotationRestApiGet.startDttm);
    }

    @Override
    public int hashCode() {
        return Objects.hash(endDttm, id, jsonMetadata, layer, longDescr, shortDescr, startDttm);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class AnnotationRestApiGet {\n");
        sb.append("    endDttm: ").append(toIndentedString(endDttm)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    jsonMetadata: ").append(toIndentedString(jsonMetadata)).append("\n");
        sb.append("    layer: ").append(toIndentedString(layer)).append("\n");
        sb.append("    longDescr: ").append(toIndentedString(longDescr)).append("\n");
        sb.append("    shortDescr: ").append(toIndentedString(shortDescr)).append("\n");
        sb.append("    startDttm: ").append(toIndentedString(startDttm)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

