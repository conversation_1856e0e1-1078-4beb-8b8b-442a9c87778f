/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * ChartDataResponseResult
 */
@JsonPropertyOrder({
    ChartDataResponseResult.JSON_PROPERTY_ANNOTATION_DATA,
    ChartDataResponseResult.JSON_PROPERTY_APPLIED_FILTERS,
    ChartDataResponseResult.JSON_PROPERTY_CACHE_KEY,
    ChartDataResponseResult.JSON_PROPERTY_CACHE_TIMEOUT,
    ChartDataResponseResult.JSON_PROPERTY_CACHED_DTTM,
    ChartDataResponseResult.JSON_PROPERTY_COLNAMES,
    ChartDataResponseResult.JSON_PROPERTY_COLTYPES,
    ChartDataResponseResult.JSON_PROPERTY_DATA,
    ChartDataResponseResult.JSON_PROPERTY_ERROR,
    ChartDataResponseResult.JSON_PROPERTY_FROM_DTTM,
    ChartDataResponseResult.JSON_PROPERTY_IS_CACHED,
    ChartDataResponseResult.JSON_PROPERTY_QUERY,
    ChartDataResponseResult.JSON_PROPERTY_REJECTED_FILTERS,
    ChartDataResponseResult.JSON_PROPERTY_ROWCOUNT,
    ChartDataResponseResult.JSON_PROPERTY_STACKTRACE,
    ChartDataResponseResult.JSON_PROPERTY_STATUS,
    ChartDataResponseResult.JSON_PROPERTY_TO_DTTM
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataResponseResult {
    public static final String JSON_PROPERTY_ANNOTATION_DATA = "annotation_data";
    private Map<String, String> annotationData;

    public static final String JSON_PROPERTY_APPLIED_FILTERS = "applied_filters";
    private List<Object> appliedFilters;

    public static final String JSON_PROPERTY_CACHE_KEY = "cache_key";
    private String cacheKey;

    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CACHED_DTTM = "cached_dttm";
    private String cachedDttm;

    public static final String JSON_PROPERTY_COLNAMES = "colnames";
    private List<String> colnames;

    public static final String JSON_PROPERTY_COLTYPES = "coltypes";
    private List<Integer> coltypes;

    public static final String JSON_PROPERTY_DATA = "data";
    private List<Object> data;

    public static final String JSON_PROPERTY_ERROR = "error";
    private String error;

    public static final String JSON_PROPERTY_FROM_DTTM = "from_dttm";
    private Integer fromDttm;

    public static final String JSON_PROPERTY_IS_CACHED = "is_cached";
    private Boolean isCached;

    public static final String JSON_PROPERTY_QUERY = "query";
    private String query;

    public static final String JSON_PROPERTY_REJECTED_FILTERS = "rejected_filters";
    private List<Object> rejectedFilters;

    public static final String JSON_PROPERTY_ROWCOUNT = "rowcount";
    private Integer rowcount;

    public static final String JSON_PROPERTY_STACKTRACE = "stacktrace";
    private String stacktrace;

    /**
     * Status of the query
     */
    public enum StatusEnum {
        STOPPED("stopped"),

        FAILED("failed"),

        PENDING("pending"),

        RUNNING("running"),

        SCHEDULED("scheduled"),

        SUCCESS("success"),

        TIMED_OUT("timed_out");

        private String value;

        StatusEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static StatusEnum fromValue(String value) {
            for (StatusEnum b : StatusEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_STATUS = "status";
    private StatusEnum status;

    public static final String JSON_PROPERTY_TO_DTTM = "to_dttm";
    private Long toDttm;

    public ChartDataResponseResult() {
    }

    public ChartDataResponseResult annotationData(Map<String, String> annotationData) {

        this.annotationData = annotationData;
        return this;
    }

    public ChartDataResponseResult addAnnotationDataItem(Map<String, String> annotationDataItem) {
        this.annotationData = annotationDataItem;
        return this;
    }

    /**
     * All requested annotation data
     *
     * @return annotationData
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ANNOTATION_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Map<String, String> getAnnotationData() {
        return annotationData;
    }


    @JsonProperty(JSON_PROPERTY_ANNOTATION_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAnnotationData(Map<String, String> annotationData) {
        this.annotationData = annotationData;
    }


    public ChartDataResponseResult appliedFilters(List<Object> appliedFilters) {

        this.appliedFilters = appliedFilters;
        return this;
    }

    public ChartDataResponseResult addAppliedFiltersItem(Object appliedFiltersItem) {
        if (this.appliedFilters == null) {
            this.appliedFilters = new ArrayList<>();
        }
        this.appliedFilters.add(appliedFiltersItem);
        return this;
    }

    /**
     * A list with applied filters
     *
     * @return appliedFilters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_APPLIED_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getAppliedFilters() {
        return appliedFilters;
    }


    @JsonProperty(JSON_PROPERTY_APPLIED_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAppliedFilters(List<Object> appliedFilters) {
        this.appliedFilters = appliedFilters;
    }


    public ChartDataResponseResult cacheKey(String cacheKey) {

        this.cacheKey = cacheKey;
        return this;
    }

    /**
     * Unique cache key for query object
     *
     * @return cacheKey
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_KEY)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getCacheKey() {
        return cacheKey;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_KEY)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setCacheKey(String cacheKey) {
        this.cacheKey = cacheKey;
    }


    public ChartDataResponseResult cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Cache timeout in following order: custom timeout, datasource timeout, cache default timeout, config default cache timeout.
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public ChartDataResponseResult cachedDttm(String cachedDttm) {

        this.cachedDttm = cachedDttm;
        return this;
    }

    /**
     * Cache timestamp
     *
     * @return cachedDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHED_DTTM)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getCachedDttm() {
        return cachedDttm;
    }


    @JsonProperty(JSON_PROPERTY_CACHED_DTTM)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setCachedDttm(String cachedDttm) {
        this.cachedDttm = cachedDttm;
    }


    public ChartDataResponseResult colnames(List<String> colnames) {

        this.colnames = colnames;
        return this;
    }

    public ChartDataResponseResult addColnamesItem(String colnamesItem) {
        if (this.colnames == null) {
            this.colnames = new ArrayList<>();
        }
        this.colnames.add(colnamesItem);
        return this;
    }

    /**
     * A list of column names
     *
     * @return colnames
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLNAMES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getColnames() {
        return colnames;
    }


    @JsonProperty(JSON_PROPERTY_COLNAMES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColnames(List<String> colnames) {
        this.colnames = colnames;
    }


    public ChartDataResponseResult coltypes(List<Integer> coltypes) {

        this.coltypes = coltypes;
        return this;
    }

    public ChartDataResponseResult addColtypesItem(Integer coltypesItem) {
        if (this.coltypes == null) {
            this.coltypes = new ArrayList<>();
        }
        this.coltypes.add(coltypesItem);
        return this;
    }

    /**
     * A list of generic data types of each column
     *
     * @return coltypes
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLTYPES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getColtypes() {
        return coltypes;
    }


    @JsonProperty(JSON_PROPERTY_COLTYPES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColtypes(List<Integer> coltypes) {
        this.coltypes = coltypes;
    }


    public ChartDataResponseResult data(List<Object> data) {

        this.data = data;
        return this;
    }

    public ChartDataResponseResult addDataItem(Object dataItem) {
        if (this.data == null) {
            this.data = new ArrayList<>();
        }
        this.data.add(dataItem);
        return this;
    }

    /**
     * A list with results
     *
     * @return data
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getData() {
        return data;
    }


    @JsonProperty(JSON_PROPERTY_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setData(List<Object> data) {
        this.data = data;
    }


    public ChartDataResponseResult error(String error) {

        this.error = error;
        return this;
    }

    /**
     * Error
     *
     * @return error
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ERROR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getError() {
        return error;
    }


    @JsonProperty(JSON_PROPERTY_ERROR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setError(String error) {
        this.error = error;
    }


    public ChartDataResponseResult fromDttm(Integer fromDttm) {

        this.fromDttm = fromDttm;
        return this;
    }

    /**
     * Start timestamp of time range
     *
     * @return fromDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FROM_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getFromDttm() {
        return fromDttm;
    }


    @JsonProperty(JSON_PROPERTY_FROM_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFromDttm(Integer fromDttm) {
        this.fromDttm = fromDttm;
    }


    public ChartDataResponseResult isCached(Boolean isCached) {

        this.isCached = isCached;
        return this;
    }

    /**
     * Is the result cached
     *
     * @return isCached
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_IS_CACHED)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Boolean getIsCached() {
        return isCached;
    }


    @JsonProperty(JSON_PROPERTY_IS_CACHED)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setIsCached(Boolean isCached) {
        this.isCached = isCached;
    }


    public ChartDataResponseResult query(String query) {

        this.query = query;
        return this;
    }

    /**
     * The executed query statement
     *
     * @return query
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_QUERY)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getQuery() {
        return query;
    }


    @JsonProperty(JSON_PROPERTY_QUERY)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setQuery(String query) {
        this.query = query;
    }


    public ChartDataResponseResult rejectedFilters(List<Object> rejectedFilters) {

        this.rejectedFilters = rejectedFilters;
        return this;
    }

    public ChartDataResponseResult addRejectedFiltersItem(Object rejectedFiltersItem) {
        if (this.rejectedFilters == null) {
            this.rejectedFilters = new ArrayList<>();
        }
        this.rejectedFilters.add(rejectedFiltersItem);
        return this;
    }

    /**
     * A list with rejected filters
     *
     * @return rejectedFilters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_REJECTED_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getRejectedFilters() {
        return rejectedFilters;
    }


    @JsonProperty(JSON_PROPERTY_REJECTED_FILTERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRejectedFilters(List<Object> rejectedFilters) {
        this.rejectedFilters = rejectedFilters;
    }


    public ChartDataResponseResult rowcount(Integer rowcount) {

        this.rowcount = rowcount;
        return this;
    }

    /**
     * Amount of rows in result set
     *
     * @return rowcount
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ROWCOUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getRowcount() {
        return rowcount;
    }


    @JsonProperty(JSON_PROPERTY_ROWCOUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRowcount(Integer rowcount) {
        this.rowcount = rowcount;
    }


    public ChartDataResponseResult stacktrace(String stacktrace) {

        this.stacktrace = stacktrace;
        return this;
    }

    /**
     * Stacktrace if there was an error
     *
     * @return stacktrace
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STACKTRACE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getStacktrace() {
        return stacktrace;
    }


    @JsonProperty(JSON_PROPERTY_STACKTRACE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStacktrace(String stacktrace) {
        this.stacktrace = stacktrace;
    }


    public ChartDataResponseResult status(StatusEnum status) {

        this.status = status;
        return this;
    }

    /**
     * Status of the query
     *
     * @return status
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public StatusEnum getStatus() {
        return status;
    }


    @JsonProperty(JSON_PROPERTY_STATUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStatus(StatusEnum status) {
        this.status = status;
    }


    public ChartDataResponseResult toDttm(Long toDttm) {

        this.toDttm = toDttm;
        return this;
    }

    /**
     * End timestamp of time range
     *
     * @return toDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TO_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Long getToDttm() {
        return toDttm;
    }


    @JsonProperty(JSON_PROPERTY_TO_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setToDttm(Long toDttm) {
        this.toDttm = toDttm;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataResponseResult chartDataResponseResult = (ChartDataResponseResult) o;
        return Objects.equals(this.annotationData, chartDataResponseResult.annotationData) &&
            Objects.equals(this.appliedFilters, chartDataResponseResult.appliedFilters) &&
            Objects.equals(this.cacheKey, chartDataResponseResult.cacheKey) &&
            Objects.equals(this.cacheTimeout, chartDataResponseResult.cacheTimeout) &&
            Objects.equals(this.cachedDttm, chartDataResponseResult.cachedDttm) &&
            Objects.equals(this.colnames, chartDataResponseResult.colnames) &&
            Objects.equals(this.coltypes, chartDataResponseResult.coltypes) &&
            Objects.equals(this.data, chartDataResponseResult.data) &&
            Objects.equals(this.error, chartDataResponseResult.error) &&
            Objects.equals(this.fromDttm, chartDataResponseResult.fromDttm) &&
            Objects.equals(this.isCached, chartDataResponseResult.isCached) &&
            Objects.equals(this.query, chartDataResponseResult.query) &&
            Objects.equals(this.rejectedFilters, chartDataResponseResult.rejectedFilters) &&
            Objects.equals(this.rowcount, chartDataResponseResult.rowcount) &&
            Objects.equals(this.stacktrace, chartDataResponseResult.stacktrace) &&
            Objects.equals(this.status, chartDataResponseResult.status) &&
            Objects.equals(this.toDttm, chartDataResponseResult.toDttm);
    }

    @Override
    public int hashCode() {
        return Objects.hash(annotationData, appliedFilters, cacheKey, cacheTimeout, cachedDttm, colnames, coltypes, data, error, fromDttm, isCached, query, rejectedFilters, rowcount, stacktrace, status, toDttm);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataResponseResult {\n");
        sb.append("    annotationData: ").append(toIndentedString(annotationData)).append("\n");
        sb.append("    appliedFilters: ").append(toIndentedString(appliedFilters)).append("\n");
        sb.append("    cacheKey: ").append(toIndentedString(cacheKey)).append("\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    cachedDttm: ").append(toIndentedString(cachedDttm)).append("\n");
        sb.append("    colnames: ").append(toIndentedString(colnames)).append("\n");
        sb.append("    coltypes: ").append(toIndentedString(coltypes)).append("\n");
        sb.append("    data: ").append(toIndentedString(data)).append("\n");
        sb.append("    error: ").append(toIndentedString(error)).append("\n");
        sb.append("    fromDttm: ").append(toIndentedString(fromDttm)).append("\n");
        sb.append("    isCached: ").append(toIndentedString(isCached)).append("\n");
        sb.append("    query: ").append(toIndentedString(query)).append("\n");
        sb.append("    rejectedFilters: ").append(toIndentedString(rejectedFilters)).append("\n");
        sb.append("    rowcount: ").append(toIndentedString(rowcount)).append("\n");
        sb.append("    stacktrace: ").append(toIndentedString(stacktrace)).append("\n");
        sb.append("    status: ").append(toIndentedString(status)).append("\n");
        sb.append("    toDttm: ").append(toIndentedString(toDttm)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

