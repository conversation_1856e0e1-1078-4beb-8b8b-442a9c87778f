/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * CacheInvalidationRequestSchema
 */
@JsonPropertyOrder({
    CacheInvalidationRequestSchema.JSON_PROPERTY_DATASOURCE_UIDS,
    CacheInvalidationRequestSchema.JSON_PROPERTY_DATASOURCES
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class CacheInvalidationRequestSchema {
    public static final String JSON_PROPERTY_DATASOURCE_UIDS = "datasource_uids";
    private List<String> datasourceUids;

    public static final String JSON_PROPERTY_DATASOURCES = "datasources";
    private List<Datasource> datasources;

    public CacheInvalidationRequestSchema() {
    }

    public CacheInvalidationRequestSchema datasourceUids(List<String> datasourceUids) {

        this.datasourceUids = datasourceUids;
        return this;
    }

    public CacheInvalidationRequestSchema addDatasourceUidsItem(String datasourceUidsItem) {
        if (this.datasourceUids == null) {
            this.datasourceUids = new ArrayList<>();
        }
        this.datasourceUids.add(datasourceUidsItem);
        return this;
    }

    /**
     * The uid of the dataset/datasource this new chart will use. A complete datasource identification needs &#x60;datasource_uid&#x60;
     *
     * @return datasourceUids
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_UIDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getDatasourceUids() {
        return datasourceUids;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_UIDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceUids(List<String> datasourceUids) {
        this.datasourceUids = datasourceUids;
    }


    public CacheInvalidationRequestSchema datasources(List<Datasource> datasources) {

        this.datasources = datasources;
        return this;
    }

    public CacheInvalidationRequestSchema addDatasourcesItem(Datasource datasourcesItem) {
        if (this.datasources == null) {
            this.datasources = new ArrayList<>();
        }
        this.datasources.add(datasourcesItem);
        return this;
    }

    /**
     * A list of the data source and database names
     *
     * @return datasources
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Datasource> getDatasources() {
        return datasources;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasources(List<Datasource> datasources) {
        this.datasources = datasources;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        CacheInvalidationRequestSchema cacheInvalidationRequestSchema = (CacheInvalidationRequestSchema) o;
        return Objects.equals(this.datasourceUids, cacheInvalidationRequestSchema.datasourceUids) &&
            Objects.equals(this.datasources, cacheInvalidationRequestSchema.datasources);
    }

    @Override
    public int hashCode() {
        return Objects.hash(datasourceUids, datasources);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class CacheInvalidationRequestSchema {\n");
        sb.append("    datasourceUids: ").append(toIndentedString(datasourceUids)).append("\n");
        sb.append("    datasources: ").append(toIndentedString(datasources)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

