/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * TabState
 */
@JsonPropertyOrder({
    TabState.JSON_PROPERTY_ACTIVE,
    TabState.JSON_PROPERTY_AUTORUN,
    TabState.JSON_PROPERTY_DATABASE_ID,
    TabState.JSON_PROPERTY_EXTRA_JSON,
    TabState.JSON_PROPERTY_HIDE_LEFT_BAR,
    TabState.JSON_PROPERTY_ID,
    TabState.JSON_PROPERTY_LABEL,
    TabState.JSON_PROPERTY_LATEST_QUERY,
    TabState.JSON_PROPERTY_QUERY_LIMIT,
    TabState.JSON_PROPERTY_SAVED_QUERY,
    TabState.JSON_PROPERTY_SCHEMA,
    TabState.JSON_PROPERTY_SQL,
    TabState.JSON_PROPERTY_TABLE_SCHEMAS,
    TabState.JSON_PROPERTY_USER_ID
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class TabState {
    public static final String JSON_PROPERTY_ACTIVE = "active";
    private Boolean active;

    public static final String JSON_PROPERTY_AUTORUN = "autorun";
    private Boolean autorun;

    public static final String JSON_PROPERTY_DATABASE_ID = "database_id";
    private Integer databaseId;

    public static final String JSON_PROPERTY_EXTRA_JSON = "extra_json";
    private Object extraJson;

    public static final String JSON_PROPERTY_HIDE_LEFT_BAR = "hide_left_bar";
    private Boolean hideLeftBar;

    public static final String JSON_PROPERTY_ID = "id";
    private String id;

    public static final String JSON_PROPERTY_LABEL = "label";
    private String label;

    public static final String JSON_PROPERTY_LATEST_QUERY = "latest_query";
    private QueryResult latestQuery;

    public static final String JSON_PROPERTY_QUERY_LIMIT = "query_limit";
    private Integer queryLimit;

    public static final String JSON_PROPERTY_SAVED_QUERY = "saved_query";
    private Object savedQuery;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TABLE_SCHEMAS = "table_schemas";
    private List<Table> tableSchemas;

    public static final String JSON_PROPERTY_USER_ID = "user_id";
    private Integer userId;

    public TabState() {
    }

    public TabState active(Boolean active) {

        this.active = active;
        return this;
    }

    /**
     * Get active
     *
     * @return active
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getActive() {
        return active;
    }


    @JsonProperty(JSON_PROPERTY_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setActive(Boolean active) {
        this.active = active;
    }


    public TabState autorun(Boolean autorun) {

        this.autorun = autorun;
        return this;
    }

    /**
     * Get autorun
     *
     * @return autorun
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_AUTORUN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAutorun() {
        return autorun;
    }


    @JsonProperty(JSON_PROPERTY_AUTORUN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAutorun(Boolean autorun) {
        this.autorun = autorun;
    }


    public TabState databaseId(Integer databaseId) {

        this.databaseId = databaseId;
        return this;
    }

    /**
     * Get databaseId
     *
     * @return databaseId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDatabaseId() {
        return databaseId;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabaseId(Integer databaseId) {
        this.databaseId = databaseId;
    }


    public TabState extraJson(Object extraJson) {

        this.extraJson = extraJson;
        return this;
    }

    /**
     * Get extraJson
     *
     * @return extraJson
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExtraJson() {
        return extraJson;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtraJson(Object extraJson) {
        this.extraJson = extraJson;
    }


    public TabState hideLeftBar(Boolean hideLeftBar) {

        this.hideLeftBar = hideLeftBar;
        return this;
    }

    /**
     * Get hideLeftBar
     *
     * @return hideLeftBar
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_HIDE_LEFT_BAR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getHideLeftBar() {
        return hideLeftBar;
    }


    @JsonProperty(JSON_PROPERTY_HIDE_LEFT_BAR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setHideLeftBar(Boolean hideLeftBar) {
        this.hideLeftBar = hideLeftBar;
    }


    public TabState id(String id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(String id) {
        this.id = id;
    }


    public TabState label(String label) {

        this.label = label;
        return this;
    }

    /**
     * Get label
     *
     * @return label
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLabel() {
        return label;
    }


    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLabel(String label) {
        this.label = label;
    }


    public TabState latestQuery(QueryResult latestQuery) {

        this.latestQuery = latestQuery;
        return this;
    }

    /**
     * Get latestQuery
     *
     * @return latestQuery
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LATEST_QUERY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public QueryResult getLatestQuery() {
        return latestQuery;
    }


    @JsonProperty(JSON_PROPERTY_LATEST_QUERY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLatestQuery(QueryResult latestQuery) {
        this.latestQuery = latestQuery;
    }


    public TabState queryLimit(Integer queryLimit) {

        this.queryLimit = queryLimit;
        return this;
    }

    /**
     * Get queryLimit
     *
     * @return queryLimit
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getQueryLimit() {
        return queryLimit;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_LIMIT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryLimit(Integer queryLimit) {
        this.queryLimit = queryLimit;
    }


    public TabState savedQuery(Object savedQuery) {

        this.savedQuery = savedQuery;
        return this;
    }

    /**
     * Get savedQuery
     *
     * @return savedQuery
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SAVED_QUERY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getSavedQuery() {
        return savedQuery;
    }


    @JsonProperty(JSON_PROPERTY_SAVED_QUERY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSavedQuery(Object savedQuery) {
        this.savedQuery = savedQuery;
    }


    public TabState schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public TabState sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public TabState tableSchemas(List<Table> tableSchemas) {

        this.tableSchemas = tableSchemas;
        return this;
    }

    public TabState addTableSchemasItem(Table tableSchemasItem) {
        if (this.tableSchemas == null) {
            this.tableSchemas = new ArrayList<>();
        }
        this.tableSchemas.add(tableSchemasItem);
        return this;
    }

    /**
     * Get tableSchemas
     *
     * @return tableSchemas
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLE_SCHEMAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Table> getTableSchemas() {
        return tableSchemas;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_SCHEMAS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTableSchemas(List<Table> tableSchemas) {
        this.tableSchemas = tableSchemas;
    }


    public TabState userId(Integer userId) {

        this.userId = userId;
        return this;
    }

    /**
     * Get userId
     *
     * @return userId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getUserId() {
        return userId;
    }


    @JsonProperty(JSON_PROPERTY_USER_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TabState tabState = (TabState) o;
        return Objects.equals(this.active, tabState.active) &&
            Objects.equals(this.autorun, tabState.autorun) &&
            Objects.equals(this.databaseId, tabState.databaseId) &&
            Objects.equals(this.extraJson, tabState.extraJson) &&
            Objects.equals(this.hideLeftBar, tabState.hideLeftBar) &&
            Objects.equals(this.id, tabState.id) &&
            Objects.equals(this.label, tabState.label) &&
            Objects.equals(this.latestQuery, tabState.latestQuery) &&
            Objects.equals(this.queryLimit, tabState.queryLimit) &&
            Objects.equals(this.savedQuery, tabState.savedQuery) &&
            Objects.equals(this.schema, tabState.schema) &&
            Objects.equals(this.sql, tabState.sql) &&
            Objects.equals(this.tableSchemas, tabState.tableSchemas) &&
            Objects.equals(this.userId, tabState.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(active, autorun, databaseId, extraJson, hideLeftBar, id, label, latestQuery, queryLimit, savedQuery, schema, sql, tableSchemas, userId);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class TabState {\n");
        sb.append("    active: ").append(toIndentedString(active)).append("\n");
        sb.append("    autorun: ").append(toIndentedString(autorun)).append("\n");
        sb.append("    databaseId: ").append(toIndentedString(databaseId)).append("\n");
        sb.append("    extraJson: ").append(toIndentedString(extraJson)).append("\n");
        sb.append("    hideLeftBar: ").append(toIndentedString(hideLeftBar)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    label: ").append(toIndentedString(label)).append("\n");
        sb.append("    latestQuery: ").append(toIndentedString(latestQuery)).append("\n");
        sb.append("    queryLimit: ").append(toIndentedString(queryLimit)).append("\n");
        sb.append("    savedQuery: ").append(toIndentedString(savedQuery)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    tableSchemas: ").append(toIndentedString(tableSchemas)).append("\n");
        sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

