/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * GetOrCreateDatasetSchema
 */
@JsonPropertyOrder({
    GetOrCreateDatasetSchema.JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM,
    GetOrCreateDatasetSchema.JSON_PROPERTY_DATABASE_ID,
    GetOrCreateDatasetSchema.JSON_PROPERTY_NORMALIZE_COLUMNS,
    GetOrCreateDatasetSchema.JSON_PROPERTY_SCHEMA,
    GetOrCreateDatasetSchema.JSON_PROPERTY_TABLE_NAME,
    GetOrCreateDatasetSchema.JSON_PROPERTY_TEMPLATE_PARAMS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class GetOrCreateDatasetSchema {
    public static final String JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM = "always_filter_main_dttm";
    private Boolean alwaysFilterMainDttm = false;

    public static final String JSON_PROPERTY_DATABASE_ID = "database_id";
    private Integer databaseId;

    public static final String JSON_PROPERTY_NORMALIZE_COLUMNS = "normalize_columns";
    private Boolean normalizeColumns = false;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public static final String JSON_PROPERTY_TEMPLATE_PARAMS = "template_params";
    private String templateParams;

    public GetOrCreateDatasetSchema() {
    }

    public GetOrCreateDatasetSchema alwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {

        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
        return this;
    }

    /**
     * Get alwaysFilterMainDttm
     *
     * @return alwaysFilterMainDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getAlwaysFilterMainDttm() {
        return alwaysFilterMainDttm;
    }


    @JsonProperty(JSON_PROPERTY_ALWAYS_FILTER_MAIN_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAlwaysFilterMainDttm(Boolean alwaysFilterMainDttm) {
        this.alwaysFilterMainDttm = alwaysFilterMainDttm;
    }


    public GetOrCreateDatasetSchema databaseId(Integer databaseId) {

        this.databaseId = databaseId;
        return this;
    }

    /**
     * ID of database table belongs to
     *
     * @return databaseId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getDatabaseId() {
        return databaseId;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatabaseId(Integer databaseId) {
        this.databaseId = databaseId;
    }


    public GetOrCreateDatasetSchema normalizeColumns(Boolean normalizeColumns) {

        this.normalizeColumns = normalizeColumns;
        return this;
    }

    /**
     * Get normalizeColumns
     *
     * @return normalizeColumns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getNormalizeColumns() {
        return normalizeColumns;
    }


    @JsonProperty(JSON_PROPERTY_NORMALIZE_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setNormalizeColumns(Boolean normalizeColumns) {
        this.normalizeColumns = normalizeColumns;
    }


    public GetOrCreateDatasetSchema schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * The schema the table belongs to
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public GetOrCreateDatasetSchema tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * Name of table
     *
     * @return tableName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }


    public GetOrCreateDatasetSchema templateParams(String templateParams) {

        this.templateParams = templateParams;
        return this;
    }

    /**
     * Template params for the table
     *
     * @return templateParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTemplateParams() {
        return templateParams;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateParams(String templateParams) {
        this.templateParams = templateParams;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetOrCreateDatasetSchema getOrCreateDatasetSchema = (GetOrCreateDatasetSchema) o;
        return Objects.equals(this.alwaysFilterMainDttm, getOrCreateDatasetSchema.alwaysFilterMainDttm) &&
            Objects.equals(this.databaseId, getOrCreateDatasetSchema.databaseId) &&
            Objects.equals(this.normalizeColumns, getOrCreateDatasetSchema.normalizeColumns) &&
            Objects.equals(this.schema, getOrCreateDatasetSchema.schema) &&
            Objects.equals(this.tableName, getOrCreateDatasetSchema.tableName) &&
            Objects.equals(this.templateParams, getOrCreateDatasetSchema.templateParams);
    }

    @Override
    public int hashCode() {
        return Objects.hash(alwaysFilterMainDttm, databaseId, normalizeColumns, schema, tableName, templateParams);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class GetOrCreateDatasetSchema {\n");
        sb.append("    alwaysFilterMainDttm: ").append(toIndentedString(alwaysFilterMainDttm)).append("\n");
        sb.append("    databaseId: ").append(toIndentedString(databaseId)).append("\n");
        sb.append("    normalizeColumns: ").append(toIndentedString(normalizeColumns)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("    templateParams: ").append(toIndentedString(templateParams)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

