/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * UserResponseSchema
 */
@JsonPropertyOrder({
    UserResponseSchema.JSON_PROPERTY_EMAIL,
    UserResponseSchema.JSON_PROPERTY_FIRST_NAME,
    UserResponseSchema.JSON_PROPERTY_ID,
    UserResponseSchema.JSON_PROPERTY_IS_ACTIVE,
    UserResponseSchema.JSON_PROPERTY_IS_ANONYMOUS,
    UserResponseSchema.JSON_PROPERTY_LAST_NAME,
    UserResponseSchema.JSON_PROPERTY_USERNAME
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class UserResponseSchema {
    public static final String JSON_PROPERTY_EMAIL = "email";
    private String email;

    public static final String JSON_PROPERTY_FIRST_NAME = "first_name";
    private String firstName;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_ACTIVE = "is_active";
    private Boolean isActive;

    public static final String JSON_PROPERTY_IS_ANONYMOUS = "is_anonymous";
    private Boolean isAnonymous;

    public static final String JSON_PROPERTY_LAST_NAME = "last_name";
    private String lastName;

    public static final String JSON_PROPERTY_USERNAME = "username";
    private String username;

    public UserResponseSchema() {
    }

    public UserResponseSchema email(String email) {

        this.email = email;
        return this;
    }

    /**
     * Get email
     *
     * @return email
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EMAIL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getEmail() {
        return email;
    }


    @JsonProperty(JSON_PROPERTY_EMAIL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEmail(String email) {
        this.email = email;
    }


    public UserResponseSchema firstName(String firstName) {

        this.firstName = firstName;
        return this;
    }

    /**
     * Get firstName
     *
     * @return firstName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FIRST_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getFirstName() {
        return firstName;
    }


    @JsonProperty(JSON_PROPERTY_FIRST_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }


    public UserResponseSchema id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public UserResponseSchema isActive(Boolean isActive) {

        this.isActive = isActive;
        return this;
    }

    /**
     * Get isActive
     *
     * @return isActive
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsActive() {
        return isActive;
    }


    @JsonProperty(JSON_PROPERTY_IS_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }


    public UserResponseSchema isAnonymous(Boolean isAnonymous) {

        this.isAnonymous = isAnonymous;
        return this;
    }

    /**
     * Get isAnonymous
     *
     * @return isAnonymous
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_ANONYMOUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsAnonymous() {
        return isAnonymous;
    }


    @JsonProperty(JSON_PROPERTY_IS_ANONYMOUS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsAnonymous(Boolean isAnonymous) {
        this.isAnonymous = isAnonymous;
    }


    public UserResponseSchema lastName(String lastName) {

        this.lastName = lastName;
        return this;
    }

    /**
     * Get lastName
     *
     * @return lastName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLastName() {
        return lastName;
    }


    @JsonProperty(JSON_PROPERTY_LAST_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }


    public UserResponseSchema username(String username) {

        this.username = username;
        return this;
    }

    /**
     * Get username
     *
     * @return username
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUsername() {
        return username;
    }


    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UserResponseSchema userResponseSchema = (UserResponseSchema) o;
        return Objects.equals(this.email, userResponseSchema.email) &&
            Objects.equals(this.firstName, userResponseSchema.firstName) &&
            Objects.equals(this.id, userResponseSchema.id) &&
            Objects.equals(this.isActive, userResponseSchema.isActive) &&
            Objects.equals(this.isAnonymous, userResponseSchema.isAnonymous) &&
            Objects.equals(this.lastName, userResponseSchema.lastName) &&
            Objects.equals(this.username, userResponseSchema.username);
    }

    @Override
    public int hashCode() {
        return Objects.hash(email, firstName, id, isActive, isAnonymous, lastName, username);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class UserResponseSchema {\n");
        sb.append("    email: ").append(toIndentedString(email)).append("\n");
        sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isActive: ").append(toIndentedString(isActive)).append("\n");
        sb.append("    isAnonymous: ").append(toIndentedString(isAnonymous)).append("\n");
        sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
        sb.append("    username: ").append(toIndentedString(username)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

