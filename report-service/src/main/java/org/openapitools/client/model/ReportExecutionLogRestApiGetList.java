/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.Objects;
import java.util.UUID;

/**
 * ReportExecutionLogRestApiGetList
 */
@JsonPropertyOrder({
    ReportExecutionLogRestApiGetList.JSON_PROPERTY_END_DTTM,
    ReportExecutionLogRestApiGetList.JSON_PROPERTY_ERROR_MESSAGE,
    ReportExecutionLogRestApiGetList.JSON_PROPERTY_ID,
    ReportExecutionLogRestApiGetList.JSON_PROPERTY_SCHEDULED_DTTM,
    ReportExecutionLogRestApiGetList.JSON_PROPERTY_START_DTTM,
    ReportExecutionLogRestApiGetList.JSON_PROPERTY_STATE,
    ReportExecutionLogRestApiGetList.JSON_PROPERTY_UUID,
    ReportExecutionLogRestApiGetList.JSON_PROPERTY_VALUE,
    ReportExecutionLogRestApiGetList.JSON_PROPERTY_VALUE_ROW_JSON
})
@JsonTypeName("ReportExecutionLogRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ReportExecutionLogRestApiGetList {
    public static final String JSON_PROPERTY_END_DTTM = "end_dttm";
    private OffsetDateTime endDttm;

    public static final String JSON_PROPERTY_ERROR_MESSAGE = "error_message";
    private String errorMessage;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_SCHEDULED_DTTM = "scheduled_dttm";
    private OffsetDateTime scheduledDttm;

    public static final String JSON_PROPERTY_START_DTTM = "start_dttm";
    private OffsetDateTime startDttm;

    public static final String JSON_PROPERTY_STATE = "state";
    private String state;

    public static final String JSON_PROPERTY_UUID = "uuid";
    private UUID uuid;

    public static final String JSON_PROPERTY_VALUE = "value";
    private BigDecimal value;

    public static final String JSON_PROPERTY_VALUE_ROW_JSON = "value_row_json";
    private String valueRowJson;

    public ReportExecutionLogRestApiGetList() {
    }

    public ReportExecutionLogRestApiGetList endDttm(OffsetDateTime endDttm) {

        this.endDttm = endDttm;
        return this;
    }

    /**
     * Get endDttm
     *
     * @return endDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getEndDttm() {
        return endDttm;
    }


    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEndDttm(OffsetDateTime endDttm) {
        this.endDttm = endDttm;
    }


    public ReportExecutionLogRestApiGetList errorMessage(String errorMessage) {

        this.errorMessage = errorMessage;
        return this;
    }

    /**
     * Get errorMessage
     *
     * @return errorMessage
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getErrorMessage() {
        return errorMessage;
    }


    @JsonProperty(JSON_PROPERTY_ERROR_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }


    public ReportExecutionLogRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ReportExecutionLogRestApiGetList scheduledDttm(OffsetDateTime scheduledDttm) {

        this.scheduledDttm = scheduledDttm;
        return this;
    }

    /**
     * Get scheduledDttm
     *
     * @return scheduledDttm
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_SCHEDULED_DTTM)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public OffsetDateTime getScheduledDttm() {
        return scheduledDttm;
    }


    @JsonProperty(JSON_PROPERTY_SCHEDULED_DTTM)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setScheduledDttm(OffsetDateTime scheduledDttm) {
        this.scheduledDttm = scheduledDttm;
    }


    public ReportExecutionLogRestApiGetList startDttm(OffsetDateTime startDttm) {

        this.startDttm = startDttm;
        return this;
    }

    /**
     * Get startDttm
     *
     * @return startDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getStartDttm() {
        return startDttm;
    }


    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStartDttm(OffsetDateTime startDttm) {
        this.startDttm = startDttm;
    }


    public ReportExecutionLogRestApiGetList state(String state) {

        this.state = state;
        return this;
    }

    /**
     * Get state
     *
     * @return state
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_STATE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getState() {
        return state;
    }


    @JsonProperty(JSON_PROPERTY_STATE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setState(String state) {
        this.state = state;
    }


    public ReportExecutionLogRestApiGetList uuid(UUID uuid) {

        this.uuid = uuid;
        return this;
    }

    /**
     * Get uuid
     *
     * @return uuid
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public UUID getUuid() {
        return uuid;
    }


    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUuid(UUID uuid) {
        this.uuid = uuid;
    }


    public ReportExecutionLogRestApiGetList value(BigDecimal value) {

        this.value = value;
        return this;
    }

    /**
     * Get value
     *
     * @return value
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getValue() {
        return value;
    }


    @JsonProperty(JSON_PROPERTY_VALUE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValue(BigDecimal value) {
        this.value = value;
    }


    public ReportExecutionLogRestApiGetList valueRowJson(String valueRowJson) {

        this.valueRowJson = valueRowJson;
        return this;
    }

    /**
     * Get valueRowJson
     *
     * @return valueRowJson
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VALUE_ROW_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getValueRowJson() {
        return valueRowJson;
    }


    @JsonProperty(JSON_PROPERTY_VALUE_ROW_JSON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setValueRowJson(String valueRowJson) {
        this.valueRowJson = valueRowJson;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ReportExecutionLogRestApiGetList reportExecutionLogRestApiGetList = (ReportExecutionLogRestApiGetList) o;
        return Objects.equals(this.endDttm, reportExecutionLogRestApiGetList.endDttm) &&
            Objects.equals(this.errorMessage, reportExecutionLogRestApiGetList.errorMessage) &&
            Objects.equals(this.id, reportExecutionLogRestApiGetList.id) &&
            Objects.equals(this.scheduledDttm, reportExecutionLogRestApiGetList.scheduledDttm) &&
            Objects.equals(this.startDttm, reportExecutionLogRestApiGetList.startDttm) &&
            Objects.equals(this.state, reportExecutionLogRestApiGetList.state) &&
            Objects.equals(this.uuid, reportExecutionLogRestApiGetList.uuid) &&
            Objects.equals(this.value, reportExecutionLogRestApiGetList.value) &&
            Objects.equals(this.valueRowJson, reportExecutionLogRestApiGetList.valueRowJson);
    }

    @Override
    public int hashCode() {
        return Objects.hash(endDttm, errorMessage, id, scheduledDttm, startDttm, state, uuid, value, valueRowJson);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ReportExecutionLogRestApiGetList {\n");
        sb.append("    endDttm: ").append(toIndentedString(endDttm)).append("\n");
        sb.append("    errorMessage: ").append(toIndentedString(errorMessage)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    scheduledDttm: ").append(toIndentedString(scheduledDttm)).append("\n");
        sb.append("    startDttm: ").append(toIndentedString(startDttm)).append("\n");
        sb.append("    state: ").append(toIndentedString(state)).append("\n");
        sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
        sb.append("    value: ").append(toIndentedString(value)).append("\n");
        sb.append("    valueRowJson: ").append(toIndentedString(valueRowJson)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

