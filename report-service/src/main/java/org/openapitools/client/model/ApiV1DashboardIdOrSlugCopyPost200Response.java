/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * ApiV1DashboardIdOrSlugCopyPost200Response
 */
@JsonPropertyOrder({
    ApiV1DashboardIdOrSlugCopyPost200Response.JSON_PROPERTY_ID,
    ApiV1DashboardIdOrSlugCopyPost200Response.JSON_PROPERTY_LAST_MODIFIED_TIME
})
@JsonTypeName("_api_v1_dashboard__id_or_slug__copy__post_200_response")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DashboardIdOrSlugCopyPost200Response {
    public static final String JSON_PROPERTY_ID = "id";
    private BigDecimal id;

    public static final String JSON_PROPERTY_LAST_MODIFIED_TIME = "last_modified_time";
    private BigDecimal lastModifiedTime;

    public ApiV1DashboardIdOrSlugCopyPost200Response() {
    }

    public ApiV1DashboardIdOrSlugCopyPost200Response id(BigDecimal id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(BigDecimal id) {
        this.id = id;
    }


    public ApiV1DashboardIdOrSlugCopyPost200Response lastModifiedTime(BigDecimal lastModifiedTime) {

        this.lastModifiedTime = lastModifiedTime;
        return this;
    }

    /**
     * Get lastModifiedTime
     *
     * @return lastModifiedTime
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public BigDecimal getLastModifiedTime() {
        return lastModifiedTime;
    }


    @JsonProperty(JSON_PROPERTY_LAST_MODIFIED_TIME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastModifiedTime(BigDecimal lastModifiedTime) {
        this.lastModifiedTime = lastModifiedTime;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DashboardIdOrSlugCopyPost200Response apiV1DashboardIdOrSlugCopyPost200Response = (ApiV1DashboardIdOrSlugCopyPost200Response) o;
        return Objects.equals(this.id, apiV1DashboardIdOrSlugCopyPost200Response.id) &&
            Objects.equals(this.lastModifiedTime, apiV1DashboardIdOrSlugCopyPost200Response.lastModifiedTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, lastModifiedTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DashboardIdOrSlugCopyPost200Response {\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    lastModifiedTime: ").append(toIndentedString(lastModifiedTime)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

