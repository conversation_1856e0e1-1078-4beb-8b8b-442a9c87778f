/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Dataset
 */
@JsonPropertyOrder({
    Dataset.JSON_PROPERTY_CACHE_TIMEOUT,
    Dataset.JSON_PROPERTY_COLUMN_FORMATS,
    Dataset.JSON_PROPERTY_COLUMNS,
    Dataset.JSON_PROPERTY_CURRENCY_FORMATS,
    Dataset.JSON_PROPERTY_DATABASE,
    Dataset.JSON_PROPERTY_DATASOURCE_NAME,
    Dataset.JSON_PROPERTY_DEFAULT_ENDPOINT,
    Dataset.JSON_PROPERTY_DESCRIPTION,
    Dataset.JSON_PROPERTY_EDIT_URL,
    Dataset.JSON_PROPERTY_EXTRA,
    Dataset.JSON_PROPERTY_FETCH_VALUES_PREDICATE,
    Dataset.JSON_PROPERTY_FILTER_SELECT,
    Dataset.JSON_PROPERTY_FILTER_SELECT_ENABLED,
    Dataset.JSON_PROPERTY_GRANULARITY_SQLA,
    Dataset.JSON_PROPERTY_HEALTH_CHECK_MESSAGE,
    Dataset.JSON_PROPERTY_ID,
    Dataset.JSON_PROPERTY_IS_SQLLAB_VIEW,
    Dataset.JSON_PROPERTY_MAIN_DTTM_COL,
    Dataset.JSON_PROPERTY_METRICS,
    Dataset.JSON_PROPERTY_NAME,
    Dataset.JSON_PROPERTY_OFFSET,
    Dataset.JSON_PROPERTY_ORDER_BY_CHOICES,
    Dataset.JSON_PROPERTY_OWNERS,
    Dataset.JSON_PROPERTY_PARAMS,
    Dataset.JSON_PROPERTY_PERM,
    Dataset.JSON_PROPERTY_SCHEMA,
    Dataset.JSON_PROPERTY_SELECT_STAR,
    Dataset.JSON_PROPERTY_SQL,
    Dataset.JSON_PROPERTY_TABLE_NAME,
    Dataset.JSON_PROPERTY_TEMPLATE_PARAMS,
    Dataset.JSON_PROPERTY_TIME_GRAIN_SQLA,
    Dataset.JSON_PROPERTY_TYPE,
    Dataset.JSON_PROPERTY_UID,
    Dataset.JSON_PROPERTY_VERBOSE_MAP
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class Dataset {
    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_COLUMN_FORMATS = "column_formats";
    private Object columnFormats;

    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<Object> columns;

    public static final String JSON_PROPERTY_CURRENCY_FORMATS = "currency_formats";
    private Object currencyFormats;

    public static final String JSON_PROPERTY_DATABASE = "database";
    private Object database;

    public static final String JSON_PROPERTY_DATASOURCE_NAME = "datasource_name";
    private String datasourceName;

    public static final String JSON_PROPERTY_DEFAULT_ENDPOINT = "default_endpoint";
    private String defaultEndpoint;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EDIT_URL = "edit_url";
    private String editUrl;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private Object extra;

    public static final String JSON_PROPERTY_FETCH_VALUES_PREDICATE = "fetch_values_predicate";
    private String fetchValuesPredicate;

    public static final String JSON_PROPERTY_FILTER_SELECT = "filter_select";
    private Boolean filterSelect;

    public static final String JSON_PROPERTY_FILTER_SELECT_ENABLED = "filter_select_enabled";
    private Boolean filterSelectEnabled;

    public static final String JSON_PROPERTY_GRANULARITY_SQLA = "granularity_sqla";
    private List<List<Object>> granularitySqla;

    public static final String JSON_PROPERTY_HEALTH_CHECK_MESSAGE = "health_check_message";
    private String healthCheckMessage;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_IS_SQLLAB_VIEW = "is_sqllab_view";
    private Boolean isSqllabView;

    public static final String JSON_PROPERTY_MAIN_DTTM_COL = "main_dttm_col";
    private String mainDttmCol;

    public static final String JSON_PROPERTY_METRICS = "metrics";
    private List<Object> metrics;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OFFSET = "offset";
    private Integer offset;

    public static final String JSON_PROPERTY_ORDER_BY_CHOICES = "order_by_choices";
    private List<List<String>> orderByChoices;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<Integer> owners;

    public static final String JSON_PROPERTY_PARAMS = "params";
    private Object params;

    public static final String JSON_PROPERTY_PERM = "perm";
    private String perm;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SELECT_STAR = "select_star";
    private String selectStar;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TABLE_NAME = "table_name";
    private String tableName;

    public static final String JSON_PROPERTY_TEMPLATE_PARAMS = "template_params";
    private Object templateParams;

    public static final String JSON_PROPERTY_TIME_GRAIN_SQLA = "time_grain_sqla";
    private List<List<String>> timeGrainSqla;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public static final String JSON_PROPERTY_UID = "uid";
    private String uid;

    public static final String JSON_PROPERTY_VERBOSE_MAP = "verbose_map";
    private Object verboseMap;

    public Dataset() {
    }

    public Dataset cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Duration (in seconds) of the caching timeout for this dataset.
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public Dataset columnFormats(Object columnFormats) {

        this.columnFormats = columnFormats;
        return this;
    }

    /**
     * Column formats.
     *
     * @return columnFormats
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMN_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getColumnFormats() {
        return columnFormats;
    }


    @JsonProperty(JSON_PROPERTY_COLUMN_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumnFormats(Object columnFormats) {
        this.columnFormats = columnFormats;
    }


    public Dataset columns(List<Object> columns) {

        this.columns = columns;
        return this;
    }

    public Dataset addColumnsItem(Object columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * Columns metadata.
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<Object> columns) {
        this.columns = columns;
    }


    public Dataset currencyFormats(Object currencyFormats) {

        this.currencyFormats = currencyFormats;
        return this;
    }

    /**
     * Currency formats.
     *
     * @return currencyFormats
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CURRENCY_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getCurrencyFormats() {
        return currencyFormats;
    }


    @JsonProperty(JSON_PROPERTY_CURRENCY_FORMATS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCurrencyFormats(Object currencyFormats) {
        this.currencyFormats = currencyFormats;
    }


    public Dataset database(Object database) {

        this.database = database;
        return this;
    }

    /**
     * Database associated with the dataset.
     *
     * @return database
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getDatabase() {
        return database;
    }


    @JsonProperty(JSON_PROPERTY_DATABASE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatabase(Object database) {
        this.database = database;
    }


    public Dataset datasourceName(String datasourceName) {

        this.datasourceName = datasourceName;
        return this;
    }

    /**
     * Dataset name.
     *
     * @return datasourceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatasourceName() {
        return datasourceName;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }


    public Dataset defaultEndpoint(String defaultEndpoint) {

        this.defaultEndpoint = defaultEndpoint;
        return this;
    }

    /**
     * Default endpoint for the dataset.
     *
     * @return defaultEndpoint
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDefaultEndpoint() {
        return defaultEndpoint;
    }


    @JsonProperty(JSON_PROPERTY_DEFAULT_ENDPOINT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDefaultEndpoint(String defaultEndpoint) {
        this.defaultEndpoint = defaultEndpoint;
    }


    public Dataset description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Dataset description.
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public Dataset editUrl(String editUrl) {

        this.editUrl = editUrl;
        return this;
    }

    /**
     * The URL for editing the dataset.
     *
     * @return editUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EDIT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getEditUrl() {
        return editUrl;
    }


    @JsonProperty(JSON_PROPERTY_EDIT_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEditUrl(String editUrl) {
        this.editUrl = editUrl;
    }


    public Dataset extra(Object extra) {

        this.extra = extra;
        return this;
    }

    /**
     * JSON string containing extra configuration elements.
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExtra() {
        return extra;
    }


    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExtra(Object extra) {
        this.extra = extra;
    }


    public Dataset fetchValuesPredicate(String fetchValuesPredicate) {

        this.fetchValuesPredicate = fetchValuesPredicate;
        return this;
    }

    /**
     * Predicate used when fetching values from the dataset.
     *
     * @return fetchValuesPredicate
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getFetchValuesPredicate() {
        return fetchValuesPredicate;
    }


    @JsonProperty(JSON_PROPERTY_FETCH_VALUES_PREDICATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFetchValuesPredicate(String fetchValuesPredicate) {
        this.fetchValuesPredicate = fetchValuesPredicate;
    }


    public Dataset filterSelect(Boolean filterSelect) {

        this.filterSelect = filterSelect;
        return this;
    }

    /**
     * SELECT filter applied to the dataset.
     *
     * @return filterSelect
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER_SELECT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getFilterSelect() {
        return filterSelect;
    }


    @JsonProperty(JSON_PROPERTY_FILTER_SELECT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterSelect(Boolean filterSelect) {
        this.filterSelect = filterSelect;
    }


    public Dataset filterSelectEnabled(Boolean filterSelectEnabled) {

        this.filterSelectEnabled = filterSelectEnabled;
        return this;
    }

    /**
     * If the SELECT filter is enabled.
     *
     * @return filterSelectEnabled
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FILTER_SELECT_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getFilterSelectEnabled() {
        return filterSelectEnabled;
    }


    @JsonProperty(JSON_PROPERTY_FILTER_SELECT_ENABLED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFilterSelectEnabled(Boolean filterSelectEnabled) {
        this.filterSelectEnabled = filterSelectEnabled;
    }


    public Dataset granularitySqla(List<List<Object>> granularitySqla) {

        this.granularitySqla = granularitySqla;
        return this;
    }

    public Dataset addGranularitySqlaItem(List<Object> granularitySqlaItem) {
        if (this.granularitySqla == null) {
            this.granularitySqla = new ArrayList<>();
        }
        this.granularitySqla.add(granularitySqlaItem);
        return this;
    }

    /**
     * Name of temporal column used for time filtering for SQL datasources. This field is deprecated, use &#x60;granularity&#x60; instead.
     *
     * @return granularitySqla
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_GRANULARITY_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<List<Object>> getGranularitySqla() {
        return granularitySqla;
    }


    @JsonProperty(JSON_PROPERTY_GRANULARITY_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setGranularitySqla(List<List<Object>> granularitySqla) {
        this.granularitySqla = granularitySqla;
    }


    public Dataset healthCheckMessage(String healthCheckMessage) {

        this.healthCheckMessage = healthCheckMessage;
        return this;
    }

    /**
     * Health check message.
     *
     * @return healthCheckMessage
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_HEALTH_CHECK_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getHealthCheckMessage() {
        return healthCheckMessage;
    }


    @JsonProperty(JSON_PROPERTY_HEALTH_CHECK_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setHealthCheckMessage(String healthCheckMessage) {
        this.healthCheckMessage = healthCheckMessage;
    }


    public Dataset id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Dataset ID.
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public Dataset isSqllabView(Boolean isSqllabView) {

        this.isSqllabView = isSqllabView;
        return this;
    }

    /**
     * If the dataset is a SQL Lab view.
     *
     * @return isSqllabView
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_SQLLAB_VIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsSqllabView() {
        return isSqllabView;
    }


    @JsonProperty(JSON_PROPERTY_IS_SQLLAB_VIEW)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsSqllabView(Boolean isSqllabView) {
        this.isSqllabView = isSqllabView;
    }


    public Dataset mainDttmCol(String mainDttmCol) {

        this.mainDttmCol = mainDttmCol;
        return this;
    }

    /**
     * The main temporal column.
     *
     * @return mainDttmCol
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MAIN_DTTM_COL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMainDttmCol() {
        return mainDttmCol;
    }


    @JsonProperty(JSON_PROPERTY_MAIN_DTTM_COL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMainDttmCol(String mainDttmCol) {
        this.mainDttmCol = mainDttmCol;
    }


    public Dataset metrics(List<Object> metrics) {

        this.metrics = metrics;
        return this;
    }

    public Dataset addMetricsItem(Object metricsItem) {
        if (this.metrics == null) {
            this.metrics = new ArrayList<>();
        }
        this.metrics.add(metricsItem);
        return this;
    }

    /**
     * Dataset metrics.
     *
     * @return metrics
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Object> getMetrics() {
        return metrics;
    }


    @JsonProperty(JSON_PROPERTY_METRICS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMetrics(List<Object> metrics) {
        this.metrics = metrics;
    }


    public Dataset name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Dataset name.
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public Dataset offset(Integer offset) {

        this.offset = offset;
        return this;
    }

    /**
     * Dataset offset.
     *
     * @return offset
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getOffset() {
        return offset;
    }


    @JsonProperty(JSON_PROPERTY_OFFSET)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOffset(Integer offset) {
        this.offset = offset;
    }


    public Dataset orderByChoices(List<List<String>> orderByChoices) {

        this.orderByChoices = orderByChoices;
        return this;
    }

    public Dataset addOrderByChoicesItem(List<String> orderByChoicesItem) {
        if (this.orderByChoices == null) {
            this.orderByChoices = new ArrayList<>();
        }
        this.orderByChoices.add(orderByChoicesItem);
        return this;
    }

    /**
     * List of order by columns.
     *
     * @return orderByChoices
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ORDER_BY_CHOICES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<List<String>> getOrderByChoices() {
        return orderByChoices;
    }


    @JsonProperty(JSON_PROPERTY_ORDER_BY_CHOICES)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOrderByChoices(List<List<String>> orderByChoices) {
        this.orderByChoices = orderByChoices;
    }


    public Dataset owners(List<Integer> owners) {

        this.owners = owners;
        return this;
    }

    public Dataset addOwnersItem(Integer ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * List of owners identifiers
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<Integer> owners) {
        this.owners = owners;
    }


    public Dataset params(Object params) {

        this.params = params;
        return this;
    }

    /**
     * Extra params for the dataset.
     *
     * @return params
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getParams() {
        return params;
    }


    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setParams(Object params) {
        this.params = params;
    }


    public Dataset perm(String perm) {

        this.perm = perm;
        return this;
    }

    /**
     * Permission expression.
     *
     * @return perm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PERM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPerm() {
        return perm;
    }


    @JsonProperty(JSON_PROPERTY_PERM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPerm(String perm) {
        this.perm = perm;
    }


    public Dataset schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Dataset schema.
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public Dataset selectStar(String selectStar) {

        this.selectStar = selectStar;
        return this;
    }

    /**
     * Select all clause.
     *
     * @return selectStar
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SELECT_STAR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSelectStar() {
        return selectStar;
    }


    @JsonProperty(JSON_PROPERTY_SELECT_STAR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSelectStar(String selectStar) {
        this.selectStar = selectStar;
    }


    public Dataset sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * A SQL statement that defines the dataset.
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public Dataset tableName(String tableName) {

        this.tableName = tableName;
        return this;
    }

    /**
     * The name of the table associated with the dataset.
     *
     * @return tableName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTableName() {
        return tableName;
    }


    @JsonProperty(JSON_PROPERTY_TABLE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTableName(String tableName) {
        this.tableName = tableName;
    }


    public Dataset templateParams(Object templateParams) {

        this.templateParams = templateParams;
        return this;
    }

    /**
     * Table template params.
     *
     * @return templateParams
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getTemplateParams() {
        return templateParams;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateParams(Object templateParams) {
        this.templateParams = templateParams;
    }


    public Dataset timeGrainSqla(List<List<String>> timeGrainSqla) {

        this.timeGrainSqla = timeGrainSqla;
        return this;
    }

    public Dataset addTimeGrainSqlaItem(List<String> timeGrainSqlaItem) {
        if (this.timeGrainSqla == null) {
            this.timeGrainSqla = new ArrayList<>();
        }
        this.timeGrainSqla.add(timeGrainSqlaItem);
        return this;
    }

    /**
     * List of temporal granularities supported by the dataset.
     *
     * @return timeGrainSqla
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIME_GRAIN_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<List<String>> getTimeGrainSqla() {
        return timeGrainSqla;
    }


    @JsonProperty(JSON_PROPERTY_TIME_GRAIN_SQLA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimeGrainSqla(List<List<String>> timeGrainSqla) {
        this.timeGrainSqla = timeGrainSqla;
    }


    public Dataset type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Dataset type.
     *
     * @return type
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setType(String type) {
        this.type = type;
    }


    public Dataset uid(String uid) {

        this.uid = uid;
        return this;
    }

    /**
     * Dataset unique identifier.
     *
     * @return uid
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_UID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUid() {
        return uid;
    }


    @JsonProperty(JSON_PROPERTY_UID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUid(String uid) {
        this.uid = uid;
    }


    public Dataset verboseMap(Object verboseMap) {

        this.verboseMap = verboseMap;
        return this;
    }

    /**
     * Mapping from raw name to verbose name.
     *
     * @return verboseMap
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VERBOSE_MAP)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getVerboseMap() {
        return verboseMap;
    }


    @JsonProperty(JSON_PROPERTY_VERBOSE_MAP)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVerboseMap(Object verboseMap) {
        this.verboseMap = verboseMap;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Dataset dataset = (Dataset) o;
        return Objects.equals(this.cacheTimeout, dataset.cacheTimeout) &&
            Objects.equals(this.columnFormats, dataset.columnFormats) &&
            Objects.equals(this.columns, dataset.columns) &&
            Objects.equals(this.currencyFormats, dataset.currencyFormats) &&
            Objects.equals(this.database, dataset.database) &&
            Objects.equals(this.datasourceName, dataset.datasourceName) &&
            Objects.equals(this.defaultEndpoint, dataset.defaultEndpoint) &&
            Objects.equals(this.description, dataset.description) &&
            Objects.equals(this.editUrl, dataset.editUrl) &&
            Objects.equals(this.extra, dataset.extra) &&
            Objects.equals(this.fetchValuesPredicate, dataset.fetchValuesPredicate) &&
            Objects.equals(this.filterSelect, dataset.filterSelect) &&
            Objects.equals(this.filterSelectEnabled, dataset.filterSelectEnabled) &&
            Objects.equals(this.granularitySqla, dataset.granularitySqla) &&
            Objects.equals(this.healthCheckMessage, dataset.healthCheckMessage) &&
            Objects.equals(this.id, dataset.id) &&
            Objects.equals(this.isSqllabView, dataset.isSqllabView) &&
            Objects.equals(this.mainDttmCol, dataset.mainDttmCol) &&
            Objects.equals(this.metrics, dataset.metrics) &&
            Objects.equals(this.name, dataset.name) &&
            Objects.equals(this.offset, dataset.offset) &&
            Objects.equals(this.orderByChoices, dataset.orderByChoices) &&
            Objects.equals(this.owners, dataset.owners) &&
            Objects.equals(this.params, dataset.params) &&
            Objects.equals(this.perm, dataset.perm) &&
            Objects.equals(this.schema, dataset.schema) &&
            Objects.equals(this.selectStar, dataset.selectStar) &&
            Objects.equals(this.sql, dataset.sql) &&
            Objects.equals(this.tableName, dataset.tableName) &&
            Objects.equals(this.templateParams, dataset.templateParams) &&
            Objects.equals(this.timeGrainSqla, dataset.timeGrainSqla) &&
            Objects.equals(this.type, dataset.type) &&
            Objects.equals(this.uid, dataset.uid) &&
            Objects.equals(this.verboseMap, dataset.verboseMap);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cacheTimeout, columnFormats, columns, currencyFormats, database, datasourceName, defaultEndpoint, description, editUrl, extra, fetchValuesPredicate, filterSelect, filterSelectEnabled, granularitySqla, healthCheckMessage, id, isSqllabView, mainDttmCol, metrics, name, offset, orderByChoices, owners, params, perm, schema, selectStar, sql, tableName, templateParams, timeGrainSqla, type, uid, verboseMap);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class Dataset {\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    columnFormats: ").append(toIndentedString(columnFormats)).append("\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    currencyFormats: ").append(toIndentedString(currencyFormats)).append("\n");
        sb.append("    database: ").append(toIndentedString(database)).append("\n");
        sb.append("    datasourceName: ").append(toIndentedString(datasourceName)).append("\n");
        sb.append("    defaultEndpoint: ").append(toIndentedString(defaultEndpoint)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    editUrl: ").append(toIndentedString(editUrl)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    fetchValuesPredicate: ").append(toIndentedString(fetchValuesPredicate)).append("\n");
        sb.append("    filterSelect: ").append(toIndentedString(filterSelect)).append("\n");
        sb.append("    filterSelectEnabled: ").append(toIndentedString(filterSelectEnabled)).append("\n");
        sb.append("    granularitySqla: ").append(toIndentedString(granularitySqla)).append("\n");
        sb.append("    healthCheckMessage: ").append(toIndentedString(healthCheckMessage)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    isSqllabView: ").append(toIndentedString(isSqllabView)).append("\n");
        sb.append("    mainDttmCol: ").append(toIndentedString(mainDttmCol)).append("\n");
        sb.append("    metrics: ").append(toIndentedString(metrics)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    offset: ").append(toIndentedString(offset)).append("\n");
        sb.append("    orderByChoices: ").append(toIndentedString(orderByChoices)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    params: ").append(toIndentedString(params)).append("\n");
        sb.append("    perm: ").append(toIndentedString(perm)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    selectStar: ").append(toIndentedString(selectStar)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    tableName: ").append(toIndentedString(tableName)).append("\n");
        sb.append("    templateParams: ").append(toIndentedString(templateParams)).append("\n");
        sb.append("    timeGrainSqla: ").append(toIndentedString(timeGrainSqla)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("    uid: ").append(toIndentedString(uid)).append("\n");
        sb.append("    verboseMap: ").append(toIndentedString(verboseMap)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

