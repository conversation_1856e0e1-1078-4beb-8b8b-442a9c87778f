/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.math.BigDecimal;
import java.util.Objects;

/**
 * ChartDataProphetOptionsSchema
 */
@JsonPropertyOrder({
    ChartDataProphetOptionsSchema.JSON_PROPERTY_CONFIDENCE_INTERVAL,
    ChartDataProphetOptionsSchema.JSON_PROPERTY_MONTHLY_SEASONALITY,
    ChartDataProphetOptionsSchema.JSON_PROPERTY_PERIODS,
    ChartDataProphetOptionsSchema.JSON_PROPERTY_TIME_GRAIN,
    ChartDataProphetOptionsSchema.JSON_PROPERTY_WEEKLY_SEASONALITY,
    ChartDataProphetOptionsSchema.JSON_PROPERTY_YEARLY_SEASONALITY
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataProphetOptionsSchema {
    public static final String JSON_PROPERTY_CONFIDENCE_INTERVAL = "confidence_interval";
    private BigDecimal confidenceInterval;

    public static final String JSON_PROPERTY_MONTHLY_SEASONALITY = "monthly_seasonality";
    private Object monthlySeasonality = null;

    public static final String JSON_PROPERTY_PERIODS = "periods";
    private Integer periods;

    /**
     * Time grain used to specify time period increments in prediction. Supports [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Durations) durations.
     */
    public enum TimeGrainEnum {
        PT1S("PT1S"),

        PT5S("PT5S"),

        PT30S("PT30S"),

        PT1M("PT1M"),

        PT5M("PT5M"),

        PT10M("PT10M"),

        PT15M("PT15M"),

        PT30M("PT30M"),

        PT1H("PT1H"),

        PT6H("PT6H"),

        P1D("P1D"),

        P1W("P1W"),

        P1M("P1M"),

        P3M("P3M"),

        P1Y("P1Y"),

        _1969_12_28T00_00_00Z_P1W("1969-12-28T00:00:00Z/P1W"),

        _1969_12_29T00_00_00Z_P1W("1969-12-29T00:00:00Z/P1W"),

        P1W_1970_01_03T00_00_00Z("P1W/1970-01-03T00:00:00Z"),

        P1W_1970_01_04T00_00_00Z("P1W/1970-01-04T00:00:00Z");

        private String value;

        TimeGrainEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static TimeGrainEnum fromValue(String value) {
            for (TimeGrainEnum b : TimeGrainEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_TIME_GRAIN = "time_grain";
    private TimeGrainEnum timeGrain;

    public static final String JSON_PROPERTY_WEEKLY_SEASONALITY = "weekly_seasonality";
    private Object weeklySeasonality = null;

    public static final String JSON_PROPERTY_YEARLY_SEASONALITY = "yearly_seasonality";
    private Object yearlySeasonality = null;

    public ChartDataProphetOptionsSchema() {
    }

    public ChartDataProphetOptionsSchema confidenceInterval(BigDecimal confidenceInterval) {

        this.confidenceInterval = confidenceInterval;
        return this;
    }

    /**
     * Width of predicted confidence interval
     * minimum: 0.0
     * maximum: 1.0
     *
     * @return confidenceInterval
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_CONFIDENCE_INTERVAL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public BigDecimal getConfidenceInterval() {
        return confidenceInterval;
    }


    @JsonProperty(JSON_PROPERTY_CONFIDENCE_INTERVAL)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setConfidenceInterval(BigDecimal confidenceInterval) {
        this.confidenceInterval = confidenceInterval;
    }


    public ChartDataProphetOptionsSchema monthlySeasonality(Object monthlySeasonality) {

        this.monthlySeasonality = monthlySeasonality;
        return this;
    }

    /**
     * Should monthly seasonality be applied. An integer value will specify Fourier order of seasonality, &#x60;None&#x60; will automatically detect seasonality.
     *
     * @return monthlySeasonality
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MONTHLY_SEASONALITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getMonthlySeasonality() {
        return monthlySeasonality;
    }


    @JsonProperty(JSON_PROPERTY_MONTHLY_SEASONALITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMonthlySeasonality(Object monthlySeasonality) {
        this.monthlySeasonality = monthlySeasonality;
    }


    public ChartDataProphetOptionsSchema periods(Integer periods) {

        this.periods = periods;
        return this;
    }

    /**
     * Time periods (in units of &#x60;time_grain&#x60;) to predict into the future
     *
     * @return periods
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_PERIODS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getPeriods() {
        return periods;
    }


    @JsonProperty(JSON_PROPERTY_PERIODS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setPeriods(Integer periods) {
        this.periods = periods;
    }


    public ChartDataProphetOptionsSchema timeGrain(TimeGrainEnum timeGrain) {

        this.timeGrain = timeGrain;
        return this;
    }

    /**
     * Time grain used to specify time period increments in prediction. Supports [ISO 8601](https://en.wikipedia.org/wiki/ISO_8601#Durations) durations.
     *
     * @return timeGrain
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TIME_GRAIN)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public TimeGrainEnum getTimeGrain() {
        return timeGrain;
    }


    @JsonProperty(JSON_PROPERTY_TIME_GRAIN)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setTimeGrain(TimeGrainEnum timeGrain) {
        this.timeGrain = timeGrain;
    }


    public ChartDataProphetOptionsSchema weeklySeasonality(Object weeklySeasonality) {

        this.weeklySeasonality = weeklySeasonality;
        return this;
    }

    /**
     * Should weekly seasonality be applied. An integer value will specify Fourier order of seasonality, &#x60;None&#x60; will automatically detect seasonality.
     *
     * @return weeklySeasonality
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_WEEKLY_SEASONALITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getWeeklySeasonality() {
        return weeklySeasonality;
    }


    @JsonProperty(JSON_PROPERTY_WEEKLY_SEASONALITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setWeeklySeasonality(Object weeklySeasonality) {
        this.weeklySeasonality = weeklySeasonality;
    }


    public ChartDataProphetOptionsSchema yearlySeasonality(Object yearlySeasonality) {

        this.yearlySeasonality = yearlySeasonality;
        return this;
    }

    /**
     * Should yearly seasonality be applied. An integer value will specify Fourier order of seasonality, &#x60;None&#x60; will automatically detect seasonality.
     *
     * @return yearlySeasonality
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_YEARLY_SEASONALITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getYearlySeasonality() {
        return yearlySeasonality;
    }


    @JsonProperty(JSON_PROPERTY_YEARLY_SEASONALITY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setYearlySeasonality(Object yearlySeasonality) {
        this.yearlySeasonality = yearlySeasonality;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataProphetOptionsSchema chartDataProphetOptionsSchema = (ChartDataProphetOptionsSchema) o;
        return Objects.equals(this.confidenceInterval, chartDataProphetOptionsSchema.confidenceInterval) &&
            Objects.equals(this.monthlySeasonality, chartDataProphetOptionsSchema.monthlySeasonality) &&
            Objects.equals(this.periods, chartDataProphetOptionsSchema.periods) &&
            Objects.equals(this.timeGrain, chartDataProphetOptionsSchema.timeGrain) &&
            Objects.equals(this.weeklySeasonality, chartDataProphetOptionsSchema.weeklySeasonality) &&
            Objects.equals(this.yearlySeasonality, chartDataProphetOptionsSchema.yearlySeasonality);
    }

    @Override
    public int hashCode() {
        return Objects.hash(confidenceInterval, monthlySeasonality, periods, timeGrain, weeklySeasonality, yearlySeasonality);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataProphetOptionsSchema {\n");
        sb.append("    confidenceInterval: ").append(toIndentedString(confidenceInterval)).append("\n");
        sb.append("    monthlySeasonality: ").append(toIndentedString(monthlySeasonality)).append("\n");
        sb.append("    periods: ").append(toIndentedString(periods)).append("\n");
        sb.append("    timeGrain: ").append(toIndentedString(timeGrain)).append("\n");
        sb.append("    weeklySeasonality: ").append(toIndentedString(weeklySeasonality)).append("\n");
        sb.append("    yearlySeasonality: ").append(toIndentedString(yearlySeasonality)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

