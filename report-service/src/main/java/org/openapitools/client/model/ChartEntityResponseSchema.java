/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * ChartEntityResponseSchema
 */
@JsonPropertyOrder({
    ChartEntityResponseSchema.JSON_PROPERTY_CACHE_TIMEOUT,
    ChartEntityResponseSchema.JSON_PROPERTY_CERTIFICATION_DETAILS,
    ChartEntityResponseSchema.JSON_PROPERTY_CERTIFIED_BY,
    ChartEntityResponseSchema.JSON_PROPERTY_CHANGED_ON,
    ChartEntityResponseSchema.JSON_PROPERTY_DESCRIPTION,
    ChartEntityResponseSchema.JSON_PROPERTY_DESCRIPTION_MARKEDDOWN,
    ChartEntityResponseSchema.JSON_PROPERTY_FORM_DATA,
    ChartEntityResponseSchema.JSON_PROPERTY_ID,
    ChartEntityResponseSchema.JSON_PROPERTY_SLICE_NAME,
    ChartEntityResponseSchema.JSON_PROPERTY_SLICE_URL
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartEntityResponseSchema {
    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_DESCRIPTION_MARKEDDOWN = "description_markeddown";
    private String descriptionMarkeddown;

    public static final String JSON_PROPERTY_FORM_DATA = "form_data";
    private Object formData;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_SLICE_NAME = "slice_name";
    private String sliceName;

    public static final String JSON_PROPERTY_SLICE_URL = "slice_url";
    private String sliceUrl;

    public ChartEntityResponseSchema() {
    }

    public ChartEntityResponseSchema cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Duration (in seconds) of the caching timeout for this chart. Note this defaults to the datasource/table timeout if undefined.
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public ChartEntityResponseSchema certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Details of the certification
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public ChartEntityResponseSchema certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Person or group that has certified this chart
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    public ChartEntityResponseSchema changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * The ISO date that the chart was last changed.
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public ChartEntityResponseSchema description(String description) {

        this.description = description;
        return this;
    }

    /**
     * A description of the chart propose.
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public ChartEntityResponseSchema descriptionMarkeddown(String descriptionMarkeddown) {

        this.descriptionMarkeddown = descriptionMarkeddown;
        return this;
    }

    /**
     * Sanitized HTML version of the chart description.
     *
     * @return descriptionMarkeddown
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION_MARKEDDOWN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescriptionMarkeddown() {
        return descriptionMarkeddown;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION_MARKEDDOWN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescriptionMarkeddown(String descriptionMarkeddown) {
        this.descriptionMarkeddown = descriptionMarkeddown;
    }


    public ChartEntityResponseSchema formData(Object formData) {

        this.formData = formData;
        return this;
    }

    /**
     * Form data from the Explore controls used to form the chart&#39;s data query.
     *
     * @return formData
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getFormData() {
        return formData;
    }


    @JsonProperty(JSON_PROPERTY_FORM_DATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setFormData(Object formData) {
        this.formData = formData;
    }


    public ChartEntityResponseSchema id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * The id of the chart.
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ChartEntityResponseSchema sliceName(String sliceName) {

        this.sliceName = sliceName;
        return this;
    }

    /**
     * The name of the chart.
     *
     * @return sliceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSliceName() {
        return sliceName;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceName(String sliceName) {
        this.sliceName = sliceName;
    }


    public ChartEntityResponseSchema sliceUrl(String sliceUrl) {

        this.sliceUrl = sliceUrl;
        return this;
    }

    /**
     * The URL of the chart.
     *
     * @return sliceUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SLICE_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSliceUrl() {
        return sliceUrl;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSliceUrl(String sliceUrl) {
        this.sliceUrl = sliceUrl;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartEntityResponseSchema chartEntityResponseSchema = (ChartEntityResponseSchema) o;
        return Objects.equals(this.cacheTimeout, chartEntityResponseSchema.cacheTimeout) &&
            Objects.equals(this.certificationDetails, chartEntityResponseSchema.certificationDetails) &&
            Objects.equals(this.certifiedBy, chartEntityResponseSchema.certifiedBy) &&
            Objects.equals(this.changedOn, chartEntityResponseSchema.changedOn) &&
            Objects.equals(this.description, chartEntityResponseSchema.description) &&
            Objects.equals(this.descriptionMarkeddown, chartEntityResponseSchema.descriptionMarkeddown) &&
            Objects.equals(this.formData, chartEntityResponseSchema.formData) &&
            Objects.equals(this.id, chartEntityResponseSchema.id) &&
            Objects.equals(this.sliceName, chartEntityResponseSchema.sliceName) &&
            Objects.equals(this.sliceUrl, chartEntityResponseSchema.sliceUrl);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cacheTimeout, certificationDetails, certifiedBy, changedOn, description, descriptionMarkeddown, formData, id, sliceName, sliceUrl);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartEntityResponseSchema {\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    descriptionMarkeddown: ").append(toIndentedString(descriptionMarkeddown)).append("\n");
        sb.append("    formData: ").append(toIndentedString(formData)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    sliceName: ").append(toIndentedString(sliceName)).append("\n");
        sb.append("    sliceUrl: ").append(toIndentedString(sliceUrl)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

