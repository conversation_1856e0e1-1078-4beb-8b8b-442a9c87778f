/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * SavedQueryRestApiPut
 */
@JsonPropertyOrder({
    SavedQueryRestApiPut.JSON_PROPERTY_DB_ID,
    SavedQueryRestApiPut.JSON_PROPERTY_DESCRIPTION,
    SavedQueryRestApiPut.JSON_PROPERTY_LABEL,
    SavedQueryRestApiPut.JSON_PROPERTY_SCHEMA,
    SavedQueryRestApiPut.JSON_PROPERTY_SQL,
    SavedQueryRestApiPut.JSON_PROPERTY_TEMPLATE_PARAMETERS
})
@JsonTypeName("SavedQueryRestApi.put")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class SavedQueryRestApiPut {
    public static final String JSON_PROPERTY_DB_ID = "db_id";
    private Integer dbId;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_LABEL = "label";
    private String label;

    public static final String JSON_PROPERTY_SCHEMA = "schema";
    private String schema;

    public static final String JSON_PROPERTY_SQL = "sql";
    private String sql;

    public static final String JSON_PROPERTY_TEMPLATE_PARAMETERS = "template_parameters";
    private String templateParameters;

    public SavedQueryRestApiPut() {
    }

    public SavedQueryRestApiPut dbId(Integer dbId) {

        this.dbId = dbId;
        return this;
    }

    /**
     * Get dbId
     *
     * @return dbId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDbId() {
        return dbId;
    }


    @JsonProperty(JSON_PROPERTY_DB_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDbId(Integer dbId) {
        this.dbId = dbId;
    }


    public SavedQueryRestApiPut description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public SavedQueryRestApiPut label(String label) {

        this.label = label;
        return this;
    }

    /**
     * Get label
     *
     * @return label
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLabel() {
        return label;
    }


    @JsonProperty(JSON_PROPERTY_LABEL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLabel(String label) {
        this.label = label;
    }


    public SavedQueryRestApiPut schema(String schema) {

        this.schema = schema;
        return this;
    }

    /**
     * Get schema
     *
     * @return schema
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSchema() {
        return schema;
    }


    @JsonProperty(JSON_PROPERTY_SCHEMA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSchema(String schema) {
        this.schema = schema;
    }


    public SavedQueryRestApiPut sql(String sql) {

        this.sql = sql;
        return this;
    }

    /**
     * Get sql
     *
     * @return sql
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSql() {
        return sql;
    }


    @JsonProperty(JSON_PROPERTY_SQL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSql(String sql) {
        this.sql = sql;
    }


    public SavedQueryRestApiPut templateParameters(String templateParameters) {

        this.templateParameters = templateParameters;
        return this;
    }

    /**
     * Get templateParameters
     *
     * @return templateParameters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMETERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTemplateParameters() {
        return templateParameters;
    }


    @JsonProperty(JSON_PROPERTY_TEMPLATE_PARAMETERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTemplateParameters(String templateParameters) {
        this.templateParameters = templateParameters;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SavedQueryRestApiPut savedQueryRestApiPut = (SavedQueryRestApiPut) o;
        return Objects.equals(this.dbId, savedQueryRestApiPut.dbId) &&
            Objects.equals(this.description, savedQueryRestApiPut.description) &&
            Objects.equals(this.label, savedQueryRestApiPut.label) &&
            Objects.equals(this.schema, savedQueryRestApiPut.schema) &&
            Objects.equals(this.sql, savedQueryRestApiPut.sql) &&
            Objects.equals(this.templateParameters, savedQueryRestApiPut.templateParameters);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dbId, description, label, schema, sql, templateParameters);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class SavedQueryRestApiPut {\n");
        sb.append("    dbId: ").append(toIndentedString(dbId)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    label: ").append(toIndentedString(label)).append("\n");
        sb.append("    schema: ").append(toIndentedString(schema)).append("\n");
        sb.append("    sql: ").append(toIndentedString(sql)).append("\n");
        sb.append("    templateParameters: ").append(toIndentedString(templateParameters)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

