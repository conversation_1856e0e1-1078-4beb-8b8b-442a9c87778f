/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * EmbeddedDashboardResponseSchema
 */
@JsonPropertyOrder({
    EmbeddedDashboardResponseSchema.JSON_PROPERTY_ALLOWED_DOMAINS,
    EmbeddedDashboardResponseSchema.JSON_PROPERTY_CHANGED_BY,
    EmbeddedDashboardResponseSchema.JSON_PROPERTY_CHANGED_ON,
    EmbeddedDashboardResponseSchema.JSON_PROPERTY_DASHBOARD_ID,
    EmbeddedDashboardResponseSchema.JSON_PROPERTY_UUID
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class EmbeddedDashboardResponseSchema {
    public static final String JSON_PROPERTY_ALLOWED_DOMAINS = "allowed_domains";
    private List<String> allowedDomains;

    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private User1 changedBy;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_DASHBOARD_ID = "dashboard_id";
    private String dashboardId;

    public static final String JSON_PROPERTY_UUID = "uuid";
    private String uuid;

    public EmbeddedDashboardResponseSchema() {
    }

    public EmbeddedDashboardResponseSchema allowedDomains(List<String> allowedDomains) {

        this.allowedDomains = allowedDomains;
        return this;
    }

    public EmbeddedDashboardResponseSchema addAllowedDomainsItem(String allowedDomainsItem) {
        if (this.allowedDomains == null) {
            this.allowedDomains = new ArrayList<>();
        }
        this.allowedDomains.add(allowedDomainsItem);
        return this;
    }

    /**
     * Get allowedDomains
     *
     * @return allowedDomains
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ALLOWED_DOMAINS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getAllowedDomains() {
        return allowedDomains;
    }


    @JsonProperty(JSON_PROPERTY_ALLOWED_DOMAINS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAllowedDomains(List<String> allowedDomains) {
        this.allowedDomains = allowedDomains;
    }


    public EmbeddedDashboardResponseSchema changedBy(User1 changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public User1 getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(User1 changedBy) {
        this.changedBy = changedBy;
    }


    public EmbeddedDashboardResponseSchema changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    public EmbeddedDashboardResponseSchema dashboardId(String dashboardId) {

        this.dashboardId = dashboardId;
        return this;
    }

    /**
     * Get dashboardId
     *
     * @return dashboardId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDashboardId() {
        return dashboardId;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardId(String dashboardId) {
        this.dashboardId = dashboardId;
    }


    public EmbeddedDashboardResponseSchema uuid(String uuid) {

        this.uuid = uuid;
        return this;
    }

    /**
     * Get uuid
     *
     * @return uuid
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUuid() {
        return uuid;
    }


    @JsonProperty(JSON_PROPERTY_UUID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EmbeddedDashboardResponseSchema embeddedDashboardResponseSchema = (EmbeddedDashboardResponseSchema) o;
        return Objects.equals(this.allowedDomains, embeddedDashboardResponseSchema.allowedDomains) &&
            Objects.equals(this.changedBy, embeddedDashboardResponseSchema.changedBy) &&
            Objects.equals(this.changedOn, embeddedDashboardResponseSchema.changedOn) &&
            Objects.equals(this.dashboardId, embeddedDashboardResponseSchema.dashboardId) &&
            Objects.equals(this.uuid, embeddedDashboardResponseSchema.uuid);
    }

    @Override
    public int hashCode() {
        return Objects.hash(allowedDomains, changedBy, changedOn, dashboardId, uuid);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class EmbeddedDashboardResponseSchema {\n");
        sb.append("    allowedDomains: ").append(toIndentedString(allowedDomains)).append("\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    dashboardId: ").append(toIndentedString(dashboardId)).append("\n");
        sb.append("    uuid: ").append(toIndentedString(uuid)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

