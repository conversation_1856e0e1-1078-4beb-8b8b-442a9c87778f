/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * GetItemSchema
 */
@JsonPropertyOrder({
    GetItemSchema.JSON_PROPERTY_COLUMNS,
    GetItemSchema.JSON_PROPERTY_KEYS
})
@JsonTypeName("get_item_schema")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class GetItemSchema {
    public static final String JSON_PROPERTY_COLUMNS = "columns";
    private List<String> columns;

    /**
     * Gets or Sets keys
     */
    public enum KeysEnum {
        SHOW_COLUMNS("show_columns"),

        DESCRIPTION_COLUMNS("description_columns"),

        LABEL_COLUMNS("label_columns"),

        SHOW_TITLE("show_title"),

        NONE("none");

        private String value;

        KeysEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static KeysEnum fromValue(String value) {
            for (KeysEnum b : KeysEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_KEYS = "keys";
    private List<KeysEnum> keys;

    public GetItemSchema() {
    }

    public GetItemSchema columns(List<String> columns) {

        this.columns = columns;
        return this;
    }

    public GetItemSchema addColumnsItem(String columnsItem) {
        if (this.columns == null) {
            this.columns = new ArrayList<>();
        }
        this.columns.add(columnsItem);
        return this;
    }

    /**
     * Get columns
     *
     * @return columns
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getColumns() {
        return columns;
    }


    @JsonProperty(JSON_PROPERTY_COLUMNS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setColumns(List<String> columns) {
        this.columns = columns;
    }


    public GetItemSchema keys(List<KeysEnum> keys) {

        this.keys = keys;
        return this;
    }

    public GetItemSchema addKeysItem(KeysEnum keysItem) {
        if (this.keys == null) {
            this.keys = new ArrayList<>();
        }
        this.keys.add(keysItem);
        return this;
    }

    /**
     * Get keys
     *
     * @return keys
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<KeysEnum> getKeys() {
        return keys;
    }


    @JsonProperty(JSON_PROPERTY_KEYS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setKeys(List<KeysEnum> keys) {
        this.keys = keys;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        GetItemSchema getItemSchema = (GetItemSchema) o;
        return Objects.equals(this.columns, getItemSchema.columns) &&
            Objects.equals(this.keys, getItemSchema.keys);
    }

    @Override
    public int hashCode() {
        return Objects.hash(columns, keys);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class GetItemSchema {\n");
        sb.append("    columns: ").append(toIndentedString(columns)).append("\n");
        sb.append("    keys: ").append(toIndentedString(keys)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

