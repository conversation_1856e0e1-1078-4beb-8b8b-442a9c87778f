/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ChartGetDatasourceResponseSchema
 */
@JsonPropertyOrder({
    ChartGetDatasourceResponseSchema.JSON_PROPERTY_COUNT,
    ChartGetDatasourceResponseSchema.JSON_PROPERTY_RESULT
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartGetDatasourceResponseSchema {
    public static final String JSON_PROPERTY_COUNT = "count";
    private Integer count;

    public static final String JSON_PROPERTY_RESULT = "result";
    private ChartGetDatasourceObjectResponse result;

    public ChartGetDatasourceResponseSchema() {
    }

    public ChartGetDatasourceResponseSchema count(Integer count) {

        this.count = count;
        return this;
    }

    /**
     * The total number of datasources
     *
     * @return count
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCount() {
        return count;
    }


    @JsonProperty(JSON_PROPERTY_COUNT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCount(Integer count) {
        this.count = count;
    }


    public ChartGetDatasourceResponseSchema result(ChartGetDatasourceObjectResponse result) {

        this.result = result;
        return this;
    }

    /**
     * Get result
     *
     * @return result
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ChartGetDatasourceObjectResponse getResult() {
        return result;
    }


    @JsonProperty(JSON_PROPERTY_RESULT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setResult(ChartGetDatasourceObjectResponse result) {
        this.result = result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartGetDatasourceResponseSchema chartGetDatasourceResponseSchema = (ChartGetDatasourceResponseSchema) o;
        return Objects.equals(this.count, chartGetDatasourceResponseSchema.count) &&
            Objects.equals(this.result, chartGetDatasourceResponseSchema.result);
    }

    @Override
    public int hashCode() {
        return Objects.hash(count, result);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartGetDatasourceResponseSchema {\n");
        sb.append("    count: ").append(toIndentedString(count)).append("\n");
        sb.append("    result: ").append(toIndentedString(result)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

