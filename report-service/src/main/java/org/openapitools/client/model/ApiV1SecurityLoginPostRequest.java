/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.Objects;

/**
 * ApiV1SecurityLoginPostRequest
 */
@JsonPropertyOrder({
    ApiV1SecurityLoginPostRequest.JSON_PROPERTY_PASSWORD,
    ApiV1SecurityLoginPostRequest.JSON_PROPERTY_PROVIDER,
    ApiV1SecurityLoginPostRequest.JSON_PROPERTY_REFRESH,
    ApiV1SecurityLoginPostRequest.JSON_PROPERTY_USERNAME
})
@JsonTypeName("_api_v1_security_login_post_request")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1SecurityLoginPostRequest {
    public static final String JSON_PROPERTY_PASSWORD = "password";
    private String password;

    /**
     * Choose an authentication provider
     */
    public enum ProviderEnum {
        DB("db"),

        LDAP("ldap");

        private String value;

        ProviderEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static ProviderEnum fromValue(String value) {
            for (ProviderEnum b : ProviderEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_PROVIDER = "provider";
    private ProviderEnum provider;

    public static final String JSON_PROPERTY_REFRESH = "refresh";
    private Boolean refresh;

    public static final String JSON_PROPERTY_USERNAME = "username";
    private String username;

    public ApiV1SecurityLoginPostRequest() {
    }

    public ApiV1SecurityLoginPostRequest password(String password) {

        this.password = password;
        return this;
    }

    /**
     * The password for authentication
     *
     * @return password
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getPassword() {
        return password;
    }


    @JsonProperty(JSON_PROPERTY_PASSWORD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPassword(String password) {
        this.password = password;
    }


    public ApiV1SecurityLoginPostRequest provider(ProviderEnum provider) {

        this.provider = provider;
        return this;
    }

    /**
     * Choose an authentication provider
     *
     * @return provider
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PROVIDER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ProviderEnum getProvider() {
        return provider;
    }


    @JsonProperty(JSON_PROPERTY_PROVIDER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setProvider(ProviderEnum provider) {
        this.provider = provider;
    }


    public ApiV1SecurityLoginPostRequest refresh(Boolean refresh) {

        this.refresh = refresh;
        return this;
    }

    /**
     * If true a refresh token is provided also
     *
     * @return refresh
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_REFRESH)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getRefresh() {
        return refresh;
    }


    @JsonProperty(JSON_PROPERTY_REFRESH)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setRefresh(Boolean refresh) {
        this.refresh = refresh;
    }


    public ApiV1SecurityLoginPostRequest username(String username) {

        this.username = username;
        return this;
    }

    /**
     * The username for authentication
     *
     * @return username
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getUsername() {
        return username;
    }


    @JsonProperty(JSON_PROPERTY_USERNAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setUsername(String username) {
        this.username = username;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1SecurityLoginPostRequest apiV1SecurityLoginPostRequest = (ApiV1SecurityLoginPostRequest) o;
        return Objects.equals(this.password, apiV1SecurityLoginPostRequest.password) &&
            Objects.equals(this.provider, apiV1SecurityLoginPostRequest.provider) &&
            Objects.equals(this.refresh, apiV1SecurityLoginPostRequest.refresh) &&
            Objects.equals(this.username, apiV1SecurityLoginPostRequest.username);
    }

    @Override
    public int hashCode() {
        return Objects.hash(password, provider, refresh, username);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1SecurityLoginPostRequest {\n");
        sb.append("    password: ").append(toIndentedString(password)).append("\n");
        sb.append("    provider: ").append(toIndentedString(provider)).append("\n");
        sb.append("    refresh: ").append(toIndentedString(refresh)).append("\n");
        sb.append("    username: ").append(toIndentedString(username)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

