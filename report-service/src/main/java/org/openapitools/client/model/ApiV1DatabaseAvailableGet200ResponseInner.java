/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ApiV1DatabaseAvailableGet200ResponseInner
 */
@JsonPropertyOrder({
    ApiV1DatabaseAvailableGet200ResponseInner.JSON_PROPERTY_AVAILABLE_DRIVERS,
    ApiV1DatabaseAvailableGet200ResponseInner.JSON_PROPERTY_DEFAULT_DRIVER,
    ApiV1DatabaseAvailableGet200ResponseInner.JSON_PROPERTY_ENGINE,
    ApiV1DatabaseAvailableGet200ResponseInner.JSON_PROPERTY_ENGINE_INFORMATION,
    ApiV1DatabaseAvailableGet200ResponseInner.JSON_PROPERTY_NAME,
    ApiV1DatabaseAvailableGet200ResponseInner.JSON_PROPERTY_PARAMETERS,
    ApiV1DatabaseAvailableGet200ResponseInner.JSON_PROPERTY_PREFERRED,
    ApiV1DatabaseAvailableGet200ResponseInner.JSON_PROPERTY_SQLALCHEMY_URI_PLACEHOLDER
})
@JsonTypeName("_api_v1_database_available__get_200_response_inner")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DatabaseAvailableGet200ResponseInner {
    public static final String JSON_PROPERTY_AVAILABLE_DRIVERS = "available_drivers";
    private List<String> availableDrivers;

    public static final String JSON_PROPERTY_DEFAULT_DRIVER = "default_driver";
    private String defaultDriver;

    public static final String JSON_PROPERTY_ENGINE = "engine";
    private String engine;

    public static final String JSON_PROPERTY_ENGINE_INFORMATION = "engine_information";
    private ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation engineInformation;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_PARAMETERS = "parameters";
    private Object parameters;

    public static final String JSON_PROPERTY_PREFERRED = "preferred";
    private Boolean preferred;

    public static final String JSON_PROPERTY_SQLALCHEMY_URI_PLACEHOLDER = "sqlalchemy_uri_placeholder";
    private String sqlalchemyUriPlaceholder;

    public ApiV1DatabaseAvailableGet200ResponseInner() {
    }

    public ApiV1DatabaseAvailableGet200ResponseInner availableDrivers(List<String> availableDrivers) {

        this.availableDrivers = availableDrivers;
        return this;
    }

    public ApiV1DatabaseAvailableGet200ResponseInner addAvailableDriversItem(String availableDriversItem) {
        if (this.availableDrivers == null) {
            this.availableDrivers = new ArrayList<>();
        }
        this.availableDrivers.add(availableDriversItem);
        return this;
    }

    /**
     * Installed drivers for the engine
     *
     * @return availableDrivers
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_AVAILABLE_DRIVERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<String> getAvailableDrivers() {
        return availableDrivers;
    }


    @JsonProperty(JSON_PROPERTY_AVAILABLE_DRIVERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setAvailableDrivers(List<String> availableDrivers) {
        this.availableDrivers = availableDrivers;
    }


    public ApiV1DatabaseAvailableGet200ResponseInner defaultDriver(String defaultDriver) {

        this.defaultDriver = defaultDriver;
        return this;
    }

    /**
     * Default driver for the engine
     *
     * @return defaultDriver
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DEFAULT_DRIVER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDefaultDriver() {
        return defaultDriver;
    }


    @JsonProperty(JSON_PROPERTY_DEFAULT_DRIVER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDefaultDriver(String defaultDriver) {
        this.defaultDriver = defaultDriver;
    }


    public ApiV1DatabaseAvailableGet200ResponseInner engine(String engine) {

        this.engine = engine;
        return this;
    }

    /**
     * Name of the SQLAlchemy engine
     *
     * @return engine
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ENGINE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getEngine() {
        return engine;
    }


    @JsonProperty(JSON_PROPERTY_ENGINE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEngine(String engine) {
        this.engine = engine;
    }


    public ApiV1DatabaseAvailableGet200ResponseInner engineInformation(ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation engineInformation) {

        this.engineInformation = engineInformation;
        return this;
    }

    /**
     * Get engineInformation
     *
     * @return engineInformation
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ENGINE_INFORMATION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation getEngineInformation() {
        return engineInformation;
    }


    @JsonProperty(JSON_PROPERTY_ENGINE_INFORMATION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEngineInformation(ApiV1DatabaseAvailableGet200ResponseInnerEngineInformation engineInformation) {
        this.engineInformation = engineInformation;
    }


    public ApiV1DatabaseAvailableGet200ResponseInner name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Name of the database
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }


    public ApiV1DatabaseAvailableGet200ResponseInner parameters(Object parameters) {

        this.parameters = parameters;
        return this;
    }

    /**
     * JSON schema defining the needed parameters
     *
     * @return parameters
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMETERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getParameters() {
        return parameters;
    }


    @JsonProperty(JSON_PROPERTY_PARAMETERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setParameters(Object parameters) {
        this.parameters = parameters;
    }


    public ApiV1DatabaseAvailableGet200ResponseInner preferred(Boolean preferred) {

        this.preferred = preferred;
        return this;
    }

    /**
     * Is the database preferred?
     *
     * @return preferred
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PREFERRED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getPreferred() {
        return preferred;
    }


    @JsonProperty(JSON_PROPERTY_PREFERRED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setPreferred(Boolean preferred) {
        this.preferred = preferred;
    }


    public ApiV1DatabaseAvailableGet200ResponseInner sqlalchemyUriPlaceholder(String sqlalchemyUriPlaceholder) {

        this.sqlalchemyUriPlaceholder = sqlalchemyUriPlaceholder;
        return this;
    }

    /**
     * Example placeholder for the SQLAlchemy URI
     *
     * @return sqlalchemyUriPlaceholder
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_SQLALCHEMY_URI_PLACEHOLDER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getSqlalchemyUriPlaceholder() {
        return sqlalchemyUriPlaceholder;
    }


    @JsonProperty(JSON_PROPERTY_SQLALCHEMY_URI_PLACEHOLDER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setSqlalchemyUriPlaceholder(String sqlalchemyUriPlaceholder) {
        this.sqlalchemyUriPlaceholder = sqlalchemyUriPlaceholder;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ApiV1DatabaseAvailableGet200ResponseInner apiV1DatabaseAvailableGet200ResponseInner = (ApiV1DatabaseAvailableGet200ResponseInner) o;
        return Objects.equals(this.availableDrivers, apiV1DatabaseAvailableGet200ResponseInner.availableDrivers) &&
            Objects.equals(this.defaultDriver, apiV1DatabaseAvailableGet200ResponseInner.defaultDriver) &&
            Objects.equals(this.engine, apiV1DatabaseAvailableGet200ResponseInner.engine) &&
            Objects.equals(this.engineInformation, apiV1DatabaseAvailableGet200ResponseInner.engineInformation) &&
            Objects.equals(this.name, apiV1DatabaseAvailableGet200ResponseInner.name) &&
            Objects.equals(this.parameters, apiV1DatabaseAvailableGet200ResponseInner.parameters) &&
            Objects.equals(this.preferred, apiV1DatabaseAvailableGet200ResponseInner.preferred) &&
            Objects.equals(this.sqlalchemyUriPlaceholder, apiV1DatabaseAvailableGet200ResponseInner.sqlalchemyUriPlaceholder);
    }

    @Override
    public int hashCode() {
        return Objects.hash(availableDrivers, defaultDriver, engine, engineInformation, name, parameters, preferred, sqlalchemyUriPlaceholder);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DatabaseAvailableGet200ResponseInner {\n");
        sb.append("    availableDrivers: ").append(toIndentedString(availableDrivers)).append("\n");
        sb.append("    defaultDriver: ").append(toIndentedString(defaultDriver)).append("\n");
        sb.append("    engine: ").append(toIndentedString(engine)).append("\n");
        sb.append("    engineInformation: ").append(toIndentedString(engineInformation)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    parameters: ").append(toIndentedString(parameters)).append("\n");
        sb.append("    preferred: ").append(toIndentedString(preferred)).append("\n");
        sb.append("    sqlalchemyUriPlaceholder: ").append(toIndentedString(sqlalchemyUriPlaceholder)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

