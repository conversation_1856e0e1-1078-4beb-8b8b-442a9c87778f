/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * AnnotationLayerRestApiGet
 */
@JsonPropertyOrder({
    AnnotationLayerRestApiGet.JSON_PROPERTY_DESCR,
    AnnotationLayerRestApiGet.JSON_PROPERTY_ID,
    AnnotationLayerRestApiGet.JSON_PROPERTY_NAME
})
@JsonTypeName("AnnotationLayerRestApi.get")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class AnnotationLayerRestApiGet {
    public static final String JSON_PROPERTY_DESCR = "descr";
    private String descr;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public AnnotationLayerRestApiGet() {
    }

    public AnnotationLayerRestApiGet descr(String descr) {

        this.descr = descr;
        return this;
    }

    /**
     * Get descr
     *
     * @return descr
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescr() {
        return descr;
    }


    @JsonProperty(JSON_PROPERTY_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescr(String descr) {
        this.descr = descr;
    }


    public AnnotationLayerRestApiGet id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public AnnotationLayerRestApiGet name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AnnotationLayerRestApiGet annotationLayerRestApiGet = (AnnotationLayerRestApiGet) o;
        return Objects.equals(this.descr, annotationLayerRestApiGet.descr) &&
            Objects.equals(this.id, annotationLayerRestApiGet.id) &&
            Objects.equals(this.name, annotationLayerRestApiGet.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(descr, id, name);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class AnnotationLayerRestApiGet {\n");
        sb.append("    descr: ").append(toIndentedString(descr)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

