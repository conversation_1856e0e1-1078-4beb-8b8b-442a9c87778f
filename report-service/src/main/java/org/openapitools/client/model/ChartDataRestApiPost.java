/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * ChartDataRestApiPost
 */
@JsonPropertyOrder({
    ChartDataRestApiPost.JSON_PROPERTY_CACHE_TIMEOUT,
    ChartDataRestApiPost.JSON_PROPERTY_CERTIFICATION_DETAILS,
    ChartDataRestApiPost.JSON_PROPERTY_CERTIFIED_BY,
    ChartDataRestApiPost.JSON_PROPERTY_DASHBOARDS,
    ChartDataRestApiPost.JSON_PROPERTY_DATASOURCE_ID,
    ChartDataRestApiPost.JSON_PROPERTY_DATASOURCE_NAME,
    ChartDataRestApiPost.JSON_PROPERTY_DATASOURCE_TYPE,
    ChartDataRestApiPost.JSON_PROPERTY_DESCRIPTION,
    ChartDataRestApiPost.JSON_PROPERTY_EXTERNAL_URL,
    ChartDataRestApiPost.JSON_PROPERTY_IS_MANAGED_EXTERNALLY,
    ChartDataRestApiPost.JSON_PROPERTY_OWNERS,
    ChartDataRestApiPost.JSON_PROPERTY_PARAMS,
    ChartDataRestApiPost.JSON_PROPERTY_QUERY_CONTEXT,
    ChartDataRestApiPost.JSON_PROPERTY_QUERY_CONTEXT_GENERATION,
    ChartDataRestApiPost.JSON_PROPERTY_SLICE_NAME,
    ChartDataRestApiPost.JSON_PROPERTY_VIZ_TYPE
})
@JsonTypeName("ChartDataRestApi.post")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ChartDataRestApiPost {
    public static final String JSON_PROPERTY_CACHE_TIMEOUT = "cache_timeout";
    private Integer cacheTimeout;

    public static final String JSON_PROPERTY_CERTIFICATION_DETAILS = "certification_details";
    private String certificationDetails;

    public static final String JSON_PROPERTY_CERTIFIED_BY = "certified_by";
    private String certifiedBy;

    public static final String JSON_PROPERTY_DASHBOARDS = "dashboards";
    private List<Integer> dashboards;

    public static final String JSON_PROPERTY_DATASOURCE_ID = "datasource_id";
    private Integer datasourceId;

    public static final String JSON_PROPERTY_DATASOURCE_NAME = "datasource_name";
    private String datasourceName;

    /**
     * The type of dataset/datasource identified on &#x60;datasource_id&#x60;.
     */
    public enum DatasourceTypeEnum {
        SL_TABLE("sl_table"),

        TABLE("table"),

        DATASET("dataset"),

        QUERY("query"),

        SAVED_QUERY("saved_query"),

        VIEW("view");

        private String value;

        DatasourceTypeEnum(String value) {
            this.value = value;
        }

        @JsonValue
        public String getValue() {
            return value;
        }

        @Override
        public String toString() {
            return String.valueOf(value);
        }

        @JsonCreator
        public static DatasourceTypeEnum fromValue(String value) {
            for (DatasourceTypeEnum b : DatasourceTypeEnum.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            throw new IllegalArgumentException("Unexpected value '" + value + "'");
        }
    }

    public static final String JSON_PROPERTY_DATASOURCE_TYPE = "datasource_type";
    private DatasourceTypeEnum datasourceType;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXTERNAL_URL = "external_url";
    private String externalUrl;

    public static final String JSON_PROPERTY_IS_MANAGED_EXTERNALLY = "is_managed_externally";
    private Boolean isManagedExternally;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private List<Integer> owners;

    public static final String JSON_PROPERTY_PARAMS = "params";
    private String params;

    public static final String JSON_PROPERTY_QUERY_CONTEXT = "query_context";
    private String queryContext;

    public static final String JSON_PROPERTY_QUERY_CONTEXT_GENERATION = "query_context_generation";
    private Boolean queryContextGeneration;

    public static final String JSON_PROPERTY_SLICE_NAME = "slice_name";
    private String sliceName;

    public static final String JSON_PROPERTY_VIZ_TYPE = "viz_type";
    private String vizType;

    public ChartDataRestApiPost() {
    }

    public ChartDataRestApiPost cacheTimeout(Integer cacheTimeout) {

        this.cacheTimeout = cacheTimeout;
        return this;
    }

    /**
     * Duration (in seconds) of the caching timeout for this chart. Note this defaults to the datasource/table timeout if undefined.
     *
     * @return cacheTimeout
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getCacheTimeout() {
        return cacheTimeout;
    }


    @JsonProperty(JSON_PROPERTY_CACHE_TIMEOUT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCacheTimeout(Integer cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }


    public ChartDataRestApiPost certificationDetails(String certificationDetails) {

        this.certificationDetails = certificationDetails;
        return this;
    }

    /**
     * Details of the certification
     *
     * @return certificationDetails
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertificationDetails() {
        return certificationDetails;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFICATION_DETAILS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertificationDetails(String certificationDetails) {
        this.certificationDetails = certificationDetails;
    }


    public ChartDataRestApiPost certifiedBy(String certifiedBy) {

        this.certifiedBy = certifiedBy;
        return this;
    }

    /**
     * Person or group that has certified this chart
     *
     * @return certifiedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCertifiedBy() {
        return certifiedBy;
    }


    @JsonProperty(JSON_PROPERTY_CERTIFIED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCertifiedBy(String certifiedBy) {
        this.certifiedBy = certifiedBy;
    }


    public ChartDataRestApiPost dashboards(List<Integer> dashboards) {

        this.dashboards = dashboards;
        return this;
    }

    public ChartDataRestApiPost addDashboardsItem(Integer dashboardsItem) {
        if (this.dashboards == null) {
            this.dashboards = new ArrayList<>();
        }
        this.dashboards.add(dashboardsItem);
        return this;
    }

    /**
     * Get dashboards
     *
     * @return dashboards
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getDashboards() {
        return dashboards;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARDS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboards(List<Integer> dashboards) {
        this.dashboards = dashboards;
    }


    public ChartDataRestApiPost datasourceId(Integer datasourceId) {

        this.datasourceId = datasourceId;
        return this;
    }

    /**
     * The id of the dataset/datasource this new chart will use. A complete datasource identification needs &#x60;datasource_id&#x60; and &#x60;datasource_type&#x60;.
     *
     * @return datasourceId
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public Integer getDatasourceId() {
        return datasourceId;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_ID)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatasourceId(Integer datasourceId) {
        this.datasourceId = datasourceId;
    }


    public ChartDataRestApiPost datasourceName(String datasourceName) {

        this.datasourceName = datasourceName;
        return this;
    }

    /**
     * The datasource name.
     *
     * @return datasourceName
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDatasourceName() {
        return datasourceName;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_NAME)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDatasourceName(String datasourceName) {
        this.datasourceName = datasourceName;
    }


    public ChartDataRestApiPost datasourceType(DatasourceTypeEnum datasourceType) {

        this.datasourceType = datasourceType;
        return this;
    }

    /**
     * The type of dataset/datasource identified on &#x60;datasource_id&#x60;.
     *
     * @return datasourceType
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public DatasourceTypeEnum getDatasourceType() {
        return datasourceType;
    }


    @JsonProperty(JSON_PROPERTY_DATASOURCE_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setDatasourceType(DatasourceTypeEnum datasourceType) {
        this.datasourceType = datasourceType;
    }


    public ChartDataRestApiPost description(String description) {

        this.description = description;
        return this;
    }

    /**
     * A description of the chart propose.
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    public ChartDataRestApiPost externalUrl(String externalUrl) {

        this.externalUrl = externalUrl;
        return this;
    }

    /**
     * Get externalUrl
     *
     * @return externalUrl
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getExternalUrl() {
        return externalUrl;
    }


    @JsonProperty(JSON_PROPERTY_EXTERNAL_URL)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setExternalUrl(String externalUrl) {
        this.externalUrl = externalUrl;
    }


    public ChartDataRestApiPost isManagedExternally(Boolean isManagedExternally) {

        this.isManagedExternally = isManagedExternally;
        return this;
    }

    /**
     * Get isManagedExternally
     *
     * @return isManagedExternally
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getIsManagedExternally() {
        return isManagedExternally;
    }


    @JsonProperty(JSON_PROPERTY_IS_MANAGED_EXTERNALLY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setIsManagedExternally(Boolean isManagedExternally) {
        this.isManagedExternally = isManagedExternally;
    }


    public ChartDataRestApiPost owners(List<Integer> owners) {

        this.owners = owners;
        return this;
    }

    public ChartDataRestApiPost addOwnersItem(Integer ownersItem) {
        if (this.owners == null) {
            this.owners = new ArrayList<>();
        }
        this.owners.add(ownersItem);
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public List<Integer> getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(List<Integer> owners) {
        this.owners = owners;
    }


    public ChartDataRestApiPost params(String params) {

        this.params = params;
        return this;
    }

    /**
     * Parameters are generated dynamically when clicking the save or overwrite button in the explore view. This JSON object for power users who may want to alter specific parameters.
     *
     * @return params
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getParams() {
        return params;
    }


    @JsonProperty(JSON_PROPERTY_PARAMS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setParams(String params) {
        this.params = params;
    }


    public ChartDataRestApiPost queryContext(String queryContext) {

        this.queryContext = queryContext;
        return this;
    }

    /**
     * The query context represents the queries that need to run in order to generate the data the visualization, and in what format the data should be returned.
     *
     * @return queryContext
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getQueryContext() {
        return queryContext;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryContext(String queryContext) {
        this.queryContext = queryContext;
    }


    public ChartDataRestApiPost queryContextGeneration(Boolean queryContextGeneration) {

        this.queryContextGeneration = queryContextGeneration;
        return this;
    }

    /**
     * The query context generation represents whether the query_contextis user generated or not so that it does not update user modifiedstate.
     *
     * @return queryContextGeneration
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT_GENERATION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getQueryContextGeneration() {
        return queryContextGeneration;
    }


    @JsonProperty(JSON_PROPERTY_QUERY_CONTEXT_GENERATION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setQueryContextGeneration(Boolean queryContextGeneration) {
        this.queryContextGeneration = queryContextGeneration;
    }


    public ChartDataRestApiPost sliceName(String sliceName) {

        this.sliceName = sliceName;
        return this;
    }

    /**
     * The name of the chart.
     *
     * @return sliceName
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getSliceName() {
        return sliceName;
    }


    @JsonProperty(JSON_PROPERTY_SLICE_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setSliceName(String sliceName) {
        this.sliceName = sliceName;
    }


    public ChartDataRestApiPost vizType(String vizType) {

        this.vizType = vizType;
        return this;
    }

    /**
     * The type of chart visualization used.
     *
     * @return vizType
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getVizType() {
        return vizType;
    }


    @JsonProperty(JSON_PROPERTY_VIZ_TYPE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setVizType(String vizType) {
        this.vizType = vizType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ChartDataRestApiPost chartDataRestApiPost = (ChartDataRestApiPost) o;
        return Objects.equals(this.cacheTimeout, chartDataRestApiPost.cacheTimeout) &&
            Objects.equals(this.certificationDetails, chartDataRestApiPost.certificationDetails) &&
            Objects.equals(this.certifiedBy, chartDataRestApiPost.certifiedBy) &&
            Objects.equals(this.dashboards, chartDataRestApiPost.dashboards) &&
            Objects.equals(this.datasourceId, chartDataRestApiPost.datasourceId) &&
            Objects.equals(this.datasourceName, chartDataRestApiPost.datasourceName) &&
            Objects.equals(this.datasourceType, chartDataRestApiPost.datasourceType) &&
            Objects.equals(this.description, chartDataRestApiPost.description) &&
            Objects.equals(this.externalUrl, chartDataRestApiPost.externalUrl) &&
            Objects.equals(this.isManagedExternally, chartDataRestApiPost.isManagedExternally) &&
            Objects.equals(this.owners, chartDataRestApiPost.owners) &&
            Objects.equals(this.params, chartDataRestApiPost.params) &&
            Objects.equals(this.queryContext, chartDataRestApiPost.queryContext) &&
            Objects.equals(this.queryContextGeneration, chartDataRestApiPost.queryContextGeneration) &&
            Objects.equals(this.sliceName, chartDataRestApiPost.sliceName) &&
            Objects.equals(this.vizType, chartDataRestApiPost.vizType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cacheTimeout, certificationDetails, certifiedBy, dashboards, datasourceId, datasourceName, datasourceType, description, externalUrl, isManagedExternally, owners, params, queryContext, queryContextGeneration, sliceName, vizType);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ChartDataRestApiPost {\n");
        sb.append("    cacheTimeout: ").append(toIndentedString(cacheTimeout)).append("\n");
        sb.append("    certificationDetails: ").append(toIndentedString(certificationDetails)).append("\n");
        sb.append("    certifiedBy: ").append(toIndentedString(certifiedBy)).append("\n");
        sb.append("    dashboards: ").append(toIndentedString(dashboards)).append("\n");
        sb.append("    datasourceId: ").append(toIndentedString(datasourceId)).append("\n");
        sb.append("    datasourceName: ").append(toIndentedString(datasourceName)).append("\n");
        sb.append("    datasourceType: ").append(toIndentedString(datasourceType)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    externalUrl: ").append(toIndentedString(externalUrl)).append("\n");
        sb.append("    isManagedExternally: ").append(toIndentedString(isManagedExternally)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    params: ").append(toIndentedString(params)).append("\n");
        sb.append("    queryContext: ").append(toIndentedString(queryContext)).append("\n");
        sb.append("    queryContextGeneration: ").append(toIndentedString(queryContextGeneration)).append("\n");
        sb.append("    sliceName: ").append(toIndentedString(sliceName)).append("\n");
        sb.append("    vizType: ").append(toIndentedString(vizType)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

