/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.util.Objects;

/**
 * ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200ResponseResultInner
 */
@JsonPropertyOrder({
})
@JsonTypeName("_api_v1_datasource__datasource_type___datasource_id__column__column_name__values__get_200_response_result_inner")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200ResponseResultInner {
    public ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200ResponseResultInner() {
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        return true;
    }

    @Override
    public int hashCode() {
        return Objects.hash();
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ApiV1DatasourceDatasourceTypeDatasourceIdColumnColumnNameValuesGet200ResponseResultInner {\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

