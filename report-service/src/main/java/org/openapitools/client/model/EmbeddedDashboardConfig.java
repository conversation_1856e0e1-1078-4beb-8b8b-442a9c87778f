/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * EmbeddedDashboardConfig
 */
@JsonPropertyOrder({
    EmbeddedDashboardConfig.JSON_PROPERTY_ALLOWED_DOMAINS
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class EmbeddedDashboardConfig {
    public static final String JSON_PROPERTY_ALLOWED_DOMAINS = "allowed_domains";
    private List<String> allowedDomains = new ArrayList<>();

    public EmbeddedDashboardConfig() {
    }

    public EmbeddedDashboardConfig allowedDomains(List<String> allowedDomains) {

        this.allowedDomains = allowedDomains;
        return this;
    }

    public EmbeddedDashboardConfig addAllowedDomainsItem(String allowedDomainsItem) {
        if (this.allowedDomains == null) {
            this.allowedDomains = new ArrayList<>();
        }
        this.allowedDomains.add(allowedDomainsItem);
        return this;
    }

    /**
     * Get allowedDomains
     *
     * @return allowedDomains
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_ALLOWED_DOMAINS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public List<String> getAllowedDomains() {
        return allowedDomains;
    }


    @JsonProperty(JSON_PROPERTY_ALLOWED_DOMAINS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setAllowedDomains(List<String> allowedDomains) {
        this.allowedDomains = allowedDomains;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EmbeddedDashboardConfig embeddedDashboardConfig = (EmbeddedDashboardConfig) o;
        return Objects.equals(this.allowedDomains, embeddedDashboardConfig.allowedDomains);
    }

    @Override
    public int hashCode() {
        return Objects.hash(allowedDomains);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class EmbeddedDashboardConfig {\n");
        sb.append("    allowedDomains: ").append(toIndentedString(allowedDomains)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

