/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import com.fasterxml.jackson.annotation.JsonTypeName;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * AnnotationRestApiPost
 */
@JsonPropertyOrder({
    AnnotationRestApiPost.JSON_PROPERTY_END_DTTM,
    AnnotationRestApiPost.JSON_PROPERTY_JSON_METADATA,
    AnnotationRestApiPost.JSON_PROPERTY_LONG_DESCR,
    AnnotationRestApiPost.JSON_PROPERTY_SHORT_DESCR,
    AnnotationRestApiPost.JSON_PROPERTY_START_DTTM
})
@JsonTypeName("AnnotationRestApi.post")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class AnnotationRestApiPost {
    public static final String JSON_PROPERTY_END_DTTM = "end_dttm";
    private OffsetDateTime endDttm;

    public static final String JSON_PROPERTY_JSON_METADATA = "json_metadata";
    private String jsonMetadata;

    public static final String JSON_PROPERTY_LONG_DESCR = "long_descr";
    private String longDescr;

    public static final String JSON_PROPERTY_SHORT_DESCR = "short_descr";
    private String shortDescr;

    public static final String JSON_PROPERTY_START_DTTM = "start_dttm";
    private OffsetDateTime startDttm;

    public AnnotationRestApiPost() {
    }

    public AnnotationRestApiPost endDttm(OffsetDateTime endDttm) {

        this.endDttm = endDttm;
        return this;
    }

    /**
     * The annotation end date time
     *
     * @return endDttm
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public OffsetDateTime getEndDttm() {
        return endDttm;
    }


    @JsonProperty(JSON_PROPERTY_END_DTTM)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setEndDttm(OffsetDateTime endDttm) {
        this.endDttm = endDttm;
    }


    public AnnotationRestApiPost jsonMetadata(String jsonMetadata) {

        this.jsonMetadata = jsonMetadata;
        return this;
    }

    /**
     * JSON metadata
     *
     * @return jsonMetadata
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getJsonMetadata() {
        return jsonMetadata;
    }


    @JsonProperty(JSON_PROPERTY_JSON_METADATA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setJsonMetadata(String jsonMetadata) {
        this.jsonMetadata = jsonMetadata;
    }


    public AnnotationRestApiPost longDescr(String longDescr) {

        this.longDescr = longDescr;
        return this;
    }

    /**
     * A long description
     *
     * @return longDescr
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LONG_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLongDescr() {
        return longDescr;
    }


    @JsonProperty(JSON_PROPERTY_LONG_DESCR)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLongDescr(String longDescr) {
        this.longDescr = longDescr;
    }


    public AnnotationRestApiPost shortDescr(String shortDescr) {

        this.shortDescr = shortDescr;
        return this;
    }

    /**
     * A short description
     *
     * @return shortDescr
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_SHORT_DESCR)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getShortDescr() {
        return shortDescr;
    }


    @JsonProperty(JSON_PROPERTY_SHORT_DESCR)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setShortDescr(String shortDescr) {
        this.shortDescr = shortDescr;
    }


    public AnnotationRestApiPost startDttm(OffsetDateTime startDttm) {

        this.startDttm = startDttm;
        return this;
    }

    /**
     * The annotation start date time
     *
     * @return startDttm
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public OffsetDateTime getStartDttm() {
        return startDttm;
    }


    @JsonProperty(JSON_PROPERTY_START_DTTM)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setStartDttm(OffsetDateTime startDttm) {
        this.startDttm = startDttm;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AnnotationRestApiPost annotationRestApiPost = (AnnotationRestApiPost) o;
        return Objects.equals(this.endDttm, annotationRestApiPost.endDttm) &&
            Objects.equals(this.jsonMetadata, annotationRestApiPost.jsonMetadata) &&
            Objects.equals(this.longDescr, annotationRestApiPost.longDescr) &&
            Objects.equals(this.shortDescr, annotationRestApiPost.shortDescr) &&
            Objects.equals(this.startDttm, annotationRestApiPost.startDttm);
    }

    @Override
    public int hashCode() {
        return Objects.hash(endDttm, jsonMetadata, longDescr, shortDescr, startDttm);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class AnnotationRestApiPost {\n");
        sb.append("    endDttm: ").append(toIndentedString(endDttm)).append("\n");
        sb.append("    jsonMetadata: ").append(toIndentedString(jsonMetadata)).append("\n");
        sb.append("    longDescr: ").append(toIndentedString(longDescr)).append("\n");
        sb.append("    shortDescr: ").append(toIndentedString(shortDescr)).append("\n");
        sb.append("    startDttm: ").append(toIndentedString(startDttm)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

