/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.*;

import java.time.OffsetDateTime;
import java.util.Objects;

/**
 * ReportScheduleRestApiGetList
 */
@JsonPropertyOrder({
    ReportScheduleRestApiGetList.JSON_PROPERTY_ACTIVE,
    ReportScheduleRestApiGetList.JSON_PROPERTY_CHANGED_BY,
    ReportScheduleRestApiGetList.JSON_PROPERTY_CHANGED_ON,
    ReportScheduleRestApiGetList.JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED,
    ReportScheduleRestApiGetList.JSON_PROPERTY_CHART_ID,
    ReportScheduleRestApiGetList.JSO<PERSON>_PROPERTY_CREATED_BY,
    ReportScheduleRestApiGetList.JSON_PROPERTY_CREATED_ON,
    ReportScheduleRestApiGetList.JSON_PROPERTY_CREATION_METHOD,
    ReportScheduleRestApiGetList.JSON_PROPERTY_CRONTAB,
    ReportScheduleRestApiGetList.JSON_PROPERTY_CRONTAB_HUMANIZED,
    ReportScheduleRestApiGetList.JSON_PROPERTY_DASHBOARD_ID,
    ReportScheduleRestApiGetList.JSON_PROPERTY_DESCRIPTION,
    ReportScheduleRestApiGetList.JSON_PROPERTY_EXTRA,
    ReportScheduleRestApiGetList.JSON_PROPERTY_ID,
    ReportScheduleRestApiGetList.JSON_PROPERTY_LAST_EVAL_DTTM,
    ReportScheduleRestApiGetList.JSON_PROPERTY_LAST_STATE,
    ReportScheduleRestApiGetList.JSON_PROPERTY_NAME,
    ReportScheduleRestApiGetList.JSON_PROPERTY_OWNERS,
    ReportScheduleRestApiGetList.JSON_PROPERTY_RECIPIENTS,
    ReportScheduleRestApiGetList.JSON_PROPERTY_TIMEZONE,
    ReportScheduleRestApiGetList.JSON_PROPERTY_TYPE
})
@JsonTypeName("ReportScheduleRestApi.get_list")
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ReportScheduleRestApiGetList {
    public static final String JSON_PROPERTY_ACTIVE = "active";
    private Boolean active;

    public static final String JSON_PROPERTY_CHANGED_BY = "changed_by";
    private ReportScheduleRestApiGetListUser changedBy;

    public static final String JSON_PROPERTY_CHANGED_ON = "changed_on";
    private OffsetDateTime changedOn;

    public static final String JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED = "changed_on_delta_humanized";
    private Object changedOnDeltaHumanized = null;

    public static final String JSON_PROPERTY_CHART_ID = "chart_id";
    private Integer chartId;

    public static final String JSON_PROPERTY_CREATED_BY = "created_by";
    private ReportScheduleRestApiGetListUser1 createdBy;

    public static final String JSON_PROPERTY_CREATED_ON = "created_on";
    private OffsetDateTime createdOn;

    public static final String JSON_PROPERTY_CREATION_METHOD = "creation_method";
    private String creationMethod;

    public static final String JSON_PROPERTY_CRONTAB = "crontab";
    private String crontab;

    public static final String JSON_PROPERTY_CRONTAB_HUMANIZED = "crontab_humanized";
    private Object crontabHumanized = null;

    public static final String JSON_PROPERTY_DASHBOARD_ID = "dashboard_id";
    private Integer dashboardId;

    public static final String JSON_PROPERTY_DESCRIPTION = "description";
    private String description;

    public static final String JSON_PROPERTY_EXTRA = "extra";
    private Object extra = null;

    public static final String JSON_PROPERTY_ID = "id";
    private Integer id;

    public static final String JSON_PROPERTY_LAST_EVAL_DTTM = "last_eval_dttm";
    private OffsetDateTime lastEvalDttm;

    public static final String JSON_PROPERTY_LAST_STATE = "last_state";
    private String lastState;

    public static final String JSON_PROPERTY_NAME = "name";
    private String name;

    public static final String JSON_PROPERTY_OWNERS = "owners";
    private ReportScheduleRestApiGetListUser2 owners;

    public static final String JSON_PROPERTY_RECIPIENTS = "recipients";
    private ReportScheduleRestApiGetListReportRecipients recipients;

    public static final String JSON_PROPERTY_TIMEZONE = "timezone";
    private String timezone;

    public static final String JSON_PROPERTY_TYPE = "type";
    private String type;

    public ReportScheduleRestApiGetList() {
    }

    @JsonCreator
    public ReportScheduleRestApiGetList(
        @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED) Object changedOnDeltaHumanized,
        @JsonProperty(JSON_PROPERTY_CRONTAB_HUMANIZED) Object crontabHumanized,
        @JsonProperty(JSON_PROPERTY_EXTRA) Object extra
    ) {
        this();
        this.changedOnDeltaHumanized = changedOnDeltaHumanized;
        this.crontabHumanized = crontabHumanized;
        this.extra = extra;
    }

    public ReportScheduleRestApiGetList active(Boolean active) {

        this.active = active;
        return this;
    }

    /**
     * Get active
     *
     * @return active
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Boolean getActive() {
        return active;
    }


    @JsonProperty(JSON_PROPERTY_ACTIVE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setActive(Boolean active) {
        this.active = active;
    }


    public ReportScheduleRestApiGetList changedBy(ReportScheduleRestApiGetListUser changedBy) {

        this.changedBy = changedBy;
        return this;
    }

    /**
     * Get changedBy
     *
     * @return changedBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ReportScheduleRestApiGetListUser getChangedBy() {
        return changedBy;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedBy(ReportScheduleRestApiGetListUser changedBy) {
        this.changedBy = changedBy;
    }


    public ReportScheduleRestApiGetList changedOn(OffsetDateTime changedOn) {

        this.changedOn = changedOn;
        return this;
    }

    /**
     * Get changedOn
     *
     * @return changedOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getChangedOn() {
        return changedOn;
    }


    @JsonProperty(JSON_PROPERTY_CHANGED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChangedOn(OffsetDateTime changedOn) {
        this.changedOn = changedOn;
    }


    /**
     * Get changedOnDeltaHumanized
     *
     * @return changedOnDeltaHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHANGED_ON_DELTA_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getChangedOnDeltaHumanized() {
        return changedOnDeltaHumanized;
    }


    public ReportScheduleRestApiGetList chartId(Integer chartId) {

        this.chartId = chartId;
        return this;
    }

    /**
     * Get chartId
     *
     * @return chartId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CHART_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getChartId() {
        return chartId;
    }


    @JsonProperty(JSON_PROPERTY_CHART_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setChartId(Integer chartId) {
        this.chartId = chartId;
    }


    public ReportScheduleRestApiGetList createdBy(ReportScheduleRestApiGetListUser1 createdBy) {

        this.createdBy = createdBy;
        return this;
    }

    /**
     * Get createdBy
     *
     * @return createdBy
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ReportScheduleRestApiGetListUser1 getCreatedBy() {
        return createdBy;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_BY)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedBy(ReportScheduleRestApiGetListUser1 createdBy) {
        this.createdBy = createdBy;
    }


    public ReportScheduleRestApiGetList createdOn(OffsetDateTime createdOn) {

        this.createdOn = createdOn;
        return this;
    }

    /**
     * Get createdOn
     *
     * @return createdOn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getCreatedOn() {
        return createdOn;
    }


    @JsonProperty(JSON_PROPERTY_CREATED_ON)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreatedOn(OffsetDateTime createdOn) {
        this.createdOn = createdOn;
    }


    public ReportScheduleRestApiGetList creationMethod(String creationMethod) {

        this.creationMethod = creationMethod;
        return this;
    }

    /**
     * Get creationMethod
     *
     * @return creationMethod
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CREATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getCreationMethod() {
        return creationMethod;
    }


    @JsonProperty(JSON_PROPERTY_CREATION_METHOD)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setCreationMethod(String creationMethod) {
        this.creationMethod = creationMethod;
    }


    public ReportScheduleRestApiGetList crontab(String crontab) {

        this.crontab = crontab;
        return this;
    }

    /**
     * Get crontab
     *
     * @return crontab
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_CRONTAB)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getCrontab() {
        return crontab;
    }


    @JsonProperty(JSON_PROPERTY_CRONTAB)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setCrontab(String crontab) {
        this.crontab = crontab;
    }


    /**
     * Get crontabHumanized
     *
     * @return crontabHumanized
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_CRONTAB_HUMANIZED)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getCrontabHumanized() {
        return crontabHumanized;
    }


    public ReportScheduleRestApiGetList dashboardId(Integer dashboardId) {

        this.dashboardId = dashboardId;
        return this;
    }

    /**
     * Get dashboardId
     *
     * @return dashboardId
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getDashboardId() {
        return dashboardId;
    }


    @JsonProperty(JSON_PROPERTY_DASHBOARD_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDashboardId(Integer dashboardId) {
        this.dashboardId = dashboardId;
    }


    public ReportScheduleRestApiGetList description(String description) {

        this.description = description;
        return this;
    }

    /**
     * Get description
     *
     * @return description
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getDescription() {
        return description;
    }


    @JsonProperty(JSON_PROPERTY_DESCRIPTION)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setDescription(String description) {
        this.description = description;
    }


    /**
     * Get extra
     *
     * @return extra
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_EXTRA)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Object getExtra() {
        return extra;
    }


    public ReportScheduleRestApiGetList id(Integer id) {

        this.id = id;
        return this;
    }

    /**
     * Get id
     *
     * @return id
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getId() {
        return id;
    }


    @JsonProperty(JSON_PROPERTY_ID)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setId(Integer id) {
        this.id = id;
    }


    public ReportScheduleRestApiGetList lastEvalDttm(OffsetDateTime lastEvalDttm) {

        this.lastEvalDttm = lastEvalDttm;
        return this;
    }

    /**
     * Get lastEvalDttm
     *
     * @return lastEvalDttm
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_EVAL_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public OffsetDateTime getLastEvalDttm() {
        return lastEvalDttm;
    }


    @JsonProperty(JSON_PROPERTY_LAST_EVAL_DTTM)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastEvalDttm(OffsetDateTime lastEvalDttm) {
        this.lastEvalDttm = lastEvalDttm;
    }


    public ReportScheduleRestApiGetList lastState(String lastState) {

        this.lastState = lastState;
        return this;
    }

    /**
     * Get lastState
     *
     * @return lastState
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LAST_STATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getLastState() {
        return lastState;
    }


    @JsonProperty(JSON_PROPERTY_LAST_STATE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLastState(String lastState) {
        this.lastState = lastState;
    }


    public ReportScheduleRestApiGetList name(String name) {

        this.name = name;
        return this;
    }

    /**
     * Get name
     *
     * @return name
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getName() {
        return name;
    }


    @JsonProperty(JSON_PROPERTY_NAME)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setName(String name) {
        this.name = name;
    }


    public ReportScheduleRestApiGetList owners(ReportScheduleRestApiGetListUser2 owners) {

        this.owners = owners;
        return this;
    }

    /**
     * Get owners
     *
     * @return owners
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public ReportScheduleRestApiGetListUser2 getOwners() {
        return owners;
    }


    @JsonProperty(JSON_PROPERTY_OWNERS)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setOwners(ReportScheduleRestApiGetListUser2 owners) {
        this.owners = owners;
    }


    public ReportScheduleRestApiGetList recipients(ReportScheduleRestApiGetListReportRecipients recipients) {

        this.recipients = recipients;
        return this;
    }

    /**
     * Get recipients
     *
     * @return recipients
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_RECIPIENTS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public ReportScheduleRestApiGetListReportRecipients getRecipients() {
        return recipients;
    }


    @JsonProperty(JSON_PROPERTY_RECIPIENTS)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setRecipients(ReportScheduleRestApiGetListReportRecipients recipients) {
        this.recipients = recipients;
    }


    public ReportScheduleRestApiGetList timezone(String timezone) {

        this.timezone = timezone;
        return this;
    }

    /**
     * Get timezone
     *
     * @return timezone
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_TIMEZONE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getTimezone() {
        return timezone;
    }


    @JsonProperty(JSON_PROPERTY_TIMEZONE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }


    public ReportScheduleRestApiGetList type(String type) {

        this.type = type;
        return this;
    }

    /**
     * Get type
     *
     * @return type
     **/
    @javax.annotation.Nonnull
    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)

    public String getType() {
        return type;
    }


    @JsonProperty(JSON_PROPERTY_TYPE)
    @JsonInclude(value = JsonInclude.Include.ALWAYS)
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ReportScheduleRestApiGetList reportScheduleRestApiGetList = (ReportScheduleRestApiGetList) o;
        return Objects.equals(this.active, reportScheduleRestApiGetList.active) &&
            Objects.equals(this.changedBy, reportScheduleRestApiGetList.changedBy) &&
            Objects.equals(this.changedOn, reportScheduleRestApiGetList.changedOn) &&
            Objects.equals(this.changedOnDeltaHumanized, reportScheduleRestApiGetList.changedOnDeltaHumanized) &&
            Objects.equals(this.chartId, reportScheduleRestApiGetList.chartId) &&
            Objects.equals(this.createdBy, reportScheduleRestApiGetList.createdBy) &&
            Objects.equals(this.createdOn, reportScheduleRestApiGetList.createdOn) &&
            Objects.equals(this.creationMethod, reportScheduleRestApiGetList.creationMethod) &&
            Objects.equals(this.crontab, reportScheduleRestApiGetList.crontab) &&
            Objects.equals(this.crontabHumanized, reportScheduleRestApiGetList.crontabHumanized) &&
            Objects.equals(this.dashboardId, reportScheduleRestApiGetList.dashboardId) &&
            Objects.equals(this.description, reportScheduleRestApiGetList.description) &&
            Objects.equals(this.extra, reportScheduleRestApiGetList.extra) &&
            Objects.equals(this.id, reportScheduleRestApiGetList.id) &&
            Objects.equals(this.lastEvalDttm, reportScheduleRestApiGetList.lastEvalDttm) &&
            Objects.equals(this.lastState, reportScheduleRestApiGetList.lastState) &&
            Objects.equals(this.name, reportScheduleRestApiGetList.name) &&
            Objects.equals(this.owners, reportScheduleRestApiGetList.owners) &&
            Objects.equals(this.recipients, reportScheduleRestApiGetList.recipients) &&
            Objects.equals(this.timezone, reportScheduleRestApiGetList.timezone) &&
            Objects.equals(this.type, reportScheduleRestApiGetList.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(active, changedBy, changedOn, changedOnDeltaHumanized, chartId, createdBy, createdOn, creationMethod, crontab, crontabHumanized, dashboardId, description, extra, id, lastEvalDttm, lastState, name, owners, recipients, timezone, type);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ReportScheduleRestApiGetList {\n");
        sb.append("    active: ").append(toIndentedString(active)).append("\n");
        sb.append("    changedBy: ").append(toIndentedString(changedBy)).append("\n");
        sb.append("    changedOn: ").append(toIndentedString(changedOn)).append("\n");
        sb.append("    changedOnDeltaHumanized: ").append(toIndentedString(changedOnDeltaHumanized)).append("\n");
        sb.append("    chartId: ").append(toIndentedString(chartId)).append("\n");
        sb.append("    createdBy: ").append(toIndentedString(createdBy)).append("\n");
        sb.append("    createdOn: ").append(toIndentedString(createdOn)).append("\n");
        sb.append("    creationMethod: ").append(toIndentedString(creationMethod)).append("\n");
        sb.append("    crontab: ").append(toIndentedString(crontab)).append("\n");
        sb.append("    crontabHumanized: ").append(toIndentedString(crontabHumanized)).append("\n");
        sb.append("    dashboardId: ").append(toIndentedString(dashboardId)).append("\n");
        sb.append("    description: ").append(toIndentedString(description)).append("\n");
        sb.append("    extra: ").append(toIndentedString(extra)).append("\n");
        sb.append("    id: ").append(toIndentedString(id)).append("\n");
        sb.append("    lastEvalDttm: ").append(toIndentedString(lastEvalDttm)).append("\n");
        sb.append("    lastState: ").append(toIndentedString(lastState)).append("\n");
        sb.append("    name: ").append(toIndentedString(name)).append("\n");
        sb.append("    owners: ").append(toIndentedString(owners)).append("\n");
        sb.append("    recipients: ").append(toIndentedString(recipients)).append("\n");
        sb.append("    timezone: ").append(toIndentedString(timezone)).append("\n");
        sb.append("    type: ").append(toIndentedString(type)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

