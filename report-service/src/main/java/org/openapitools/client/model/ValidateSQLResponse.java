/*
 * Superset
 * Superset
 *
 * The version of the OpenAPI document: v1
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


package org.openapitools.client.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.util.Objects;

/**
 * ValidateSQLResponse
 */
@JsonPropertyOrder({
    ValidateSQLResponse.JSON_PROPERTY_END_COLUMN,
    ValidateSQLResponse.JSON_PROPERTY_LINE_NUMBER,
    ValidateSQLResponse.JSON_PROPERTY_MESSAGE,
    ValidateSQLResponse.JSON_PROPERTY_START_COLUMN
})
@javax.annotation.Generated(value = "org.openapitools.codegen.languages.JavaClientCodegen", date = "2023-11-02T10:04:44.572252+08:00[Asia/Shanghai]")
public class ValidateSQLResponse {
    public static final String JSON_PROPERTY_END_COLUMN = "end_column";
    private Integer endColumn;

    public static final String JSON_PROPERTY_LINE_NUMBER = "line_number";
    private Integer lineNumber;

    public static final String JSON_PROPERTY_MESSAGE = "message";
    private String message;

    public static final String JSON_PROPERTY_START_COLUMN = "start_column";
    private Integer startColumn;

    public ValidateSQLResponse() {
    }

    public ValidateSQLResponse endColumn(Integer endColumn) {

        this.endColumn = endColumn;
        return this;
    }

    /**
     * Get endColumn
     *
     * @return endColumn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_END_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getEndColumn() {
        return endColumn;
    }


    @JsonProperty(JSON_PROPERTY_END_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setEndColumn(Integer endColumn) {
        this.endColumn = endColumn;
    }


    public ValidateSQLResponse lineNumber(Integer lineNumber) {

        this.lineNumber = lineNumber;
        return this;
    }

    /**
     * Get lineNumber
     *
     * @return lineNumber
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_LINE_NUMBER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getLineNumber() {
        return lineNumber;
    }


    @JsonProperty(JSON_PROPERTY_LINE_NUMBER)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setLineNumber(Integer lineNumber) {
        this.lineNumber = lineNumber;
    }


    public ValidateSQLResponse message(String message) {

        this.message = message;
        return this;
    }

    /**
     * Get message
     *
     * @return message
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public String getMessage() {
        return message;
    }


    @JsonProperty(JSON_PROPERTY_MESSAGE)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setMessage(String message) {
        this.message = message;
    }


    public ValidateSQLResponse startColumn(Integer startColumn) {

        this.startColumn = startColumn;
        return this;
    }

    /**
     * Get startColumn
     *
     * @return startColumn
     **/
    @javax.annotation.Nullable
    @JsonProperty(JSON_PROPERTY_START_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)

    public Integer getStartColumn() {
        return startColumn;
    }


    @JsonProperty(JSON_PROPERTY_START_COLUMN)
    @JsonInclude(value = JsonInclude.Include.NON_NULL)
    public void setStartColumn(Integer startColumn) {
        this.startColumn = startColumn;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ValidateSQLResponse validateSQLResponse = (ValidateSQLResponse) o;
        return Objects.equals(this.endColumn, validateSQLResponse.endColumn) &&
            Objects.equals(this.lineNumber, validateSQLResponse.lineNumber) &&
            Objects.equals(this.message, validateSQLResponse.message) &&
            Objects.equals(this.startColumn, validateSQLResponse.startColumn);
    }

    @Override
    public int hashCode() {
        return Objects.hash(endColumn, lineNumber, message, startColumn);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ValidateSQLResponse {\n");
        sb.append("    endColumn: ").append(toIndentedString(endColumn)).append("\n");
        sb.append("    lineNumber: ").append(toIndentedString(lineNumber)).append("\n");
        sb.append("    message: ").append(toIndentedString(message)).append("\n");
        sb.append("    startColumn: ").append(toIndentedString(startColumn)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    /**
     * Convert the given object to string with each line indented by 4 spaces
     * (except the first line).
     */
    private String toIndentedString(Object o) {
        if (o == null) {
            return "null";
        }
        return o.toString().replace("\n", "\n    ");
    }

}

