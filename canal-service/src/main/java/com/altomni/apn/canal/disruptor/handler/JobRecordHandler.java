package com.altomni.apn.canal.disruptor.handler;

import cn.hutool.json.JSONObject;
import com.alibaba.csp.sentinel.util.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.entity.JobRecord;
import com.altomni.apn.canal.repository.JobRepository;
import com.altomni.apn.canal.repository.dto.JobAdditionalToJobId;
import com.altomni.apn.canal.service.changerecord.job.JobChangeRecordService;
import com.altomni.apn.canal.service.changerecord.talent.impl.TalentChangeRecordServiceImpl;
import com.altomni.apn.canal.service.elastic.EsRecordService;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static com.altomni.apn.canal.service.canal.CanalClient.*;
import static com.altomni.apn.canal.service.changerecord.job.impl.JobChangeRecordServiceImpl.JOB_ADDITIONAL_INFO;

@Slf4j
@Component
public class JobRecordHandler implements EventHandler<CanalEvent> {

    private final JobRepository jobRepository;
    private final JobChangeRecordService jobChangeRecordService;
    private final EsRecordService esRecordService;


    public JobRecordHandler(JobRepository jobRepository, JobChangeRecordService jobChangeRecordService, EsRecordService esRecordService) {
        this.jobRepository = jobRepository;
        this.jobChangeRecordService = jobChangeRecordService;
        this.esRecordService = esRecordService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> JobRecordHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Map<Long, JobRecord> jobRecordMap = new HashMap<>();
        try {
            Map<Long, Long> jobAdditional2JobidMap = getJobAdditional2JobidMap(eventContents);
            eventContents.stream()
                    .filter(eventContent -> JOB.equals(eventContent.tableName()) || JOB_RECORD_TABLES.contains(eventContent.tableName()))
                    .forEach(eventContent -> {
                        String tableName = eventContent.tableName();
                        CanalEntry.RowChange rowChange = eventContent.rowChange();
                        CanalEntry.EventType eventType = rowChange.getEventType();
                        log.info("[APN: EsFillerJobRecordService] record job, tableName: {}", tableName);

                        List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();

                        parseJobRecordMap(tableName, eventType, rowDataList, jobAdditional2JobidMap, jobRecordMap);
                    });
            if (jobRecordMap.isEmpty()) {
                return;
            }
            syncJobRecord(jobRecordMap);
        } catch (Exception ex) {
            log.error("APN RecordJob: failed to process job record", ex);
            for (Long next : jobRecordMap.keySet()) {
                JobRecord jobRecord = jobRecordMap.get(next);
                if (jobRecord != null) {
                    log.error("APN RecordJob: failed process job Id: {} Tenant Id:{}", jobRecord.getJobId(), jobRecord.getTenantId(), ex);
                }
            }
        }

    }

    private void syncJobRecord(Map<Long, JobRecord> jobRecordMap) {
        Iterator<Long> iterator = jobRecordMap.keySet().iterator();
        log.info("[APN: EsFillerJobRecordService] syncJobRecord input, jobRecordMap: {}", JSON.toJSON(jobRecordMap));
        Map<Long, List<JSONObject>> syncJobRecordsMap = new HashMap<>();
        Long tenantId = null;
        while (iterator.hasNext()) {
            Long jobId = iterator.next();
            JobRecord jobRecord = jobRecordMap.get(jobId);

            if (jobRecord.getEsDocuments().isEmpty()) {
                continue;
            }
            JSONObject record = new JSONObject();
            record.putIfAbsent("jobId", jobId);
            log.info("[APN: EsFillerJobRecordService] pre elimniatedEsDocuments: {}", jobRecord.getEsDocuments());
            List<JSONObject> eliminatedEsDocuments = eliminateJobRecordDuplicateChanges(jobRecord.getEsDocuments());
            log.info("[APN: EsFillerJobRecordService] post elimniatedEsDocuments: {}", eliminatedEsDocuments);

            log.info("[APN: EsFillerJobRecordService] Before saveJobUpdateToEs, job id: {}, tenantId: {}, esDocuments: {}", jobId, jobRecord.getTenantId(), JSON.toJSONString(eliminatedEsDocuments));
            record.putIfAbsent("changeFields", jobRecord.isInitial() ? jobChangeRecordService.generateNewJob(jobId) : eliminatedEsDocuments);
//                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
                    .withZone(ZoneId.systemDefault());
            Instant now = Instant.now();
            String formatted = formatter.format(now);
            Instant timestamp = TalentChangeRecordServiceImpl.formatTime(formatted);
            record.putIfAbsent("createdDate", timestamp);
            record.putIfAbsent("@timestamp", timestamp);
            record.putIfAbsent("createdBy", getUserIdFromCreatedBy(jobRecord.getCreatedBy()));
            if (tenantId == null && jobRecord.getTenantId() != null) {
                tenantId = jobRecord.getTenantId();
            }
            Long currentTenantId = jobRecord.getTenantId() == null ? tenantId : jobRecord.getTenantId();
            syncJobRecordsMap.computeIfAbsent(currentTenantId, a -> new ArrayList<>());
            syncJobRecordsMap.get(currentTenantId).add(record);
        }
        if (syncJobRecordsMap.isEmpty()) {
            return;
        }
        esRecordService.batchSaveJobUpdateToEs(syncJobRecordsMap);
    }

    private List<JSONObject> eliminateJobRecordDuplicateChanges(List<JSONObject> esDocuments) {
        List<JSONObject> uniqueJsonList = new ArrayList<>();
        for (JSONObject jsonObject : esDocuments) {
            if (!jsonObject.isEmpty() && isJSONUnique(jsonObject, uniqueJsonList)) {
                uniqueJsonList.add(jsonObject);
            }
        }
        return uniqueJsonList;
    }

    private boolean isJSONUnique(JSONObject jsonObject, List<JSONObject> uniqueJsonList) {
        for (JSONObject uniqueJson : uniqueJsonList) {
            if (isJSONEqual(jsonObject, uniqueJson)) {
                return false;
            }
        }
        return true;
    }

    private boolean isJSONEqual(JSONObject json1, JSONObject json2) {
        return json1.getStr("key").equals(json2.getStr("key")) &&
                json1.getStr("eventType").equals(json2.getStr("eventType")) &&
                json1.getStr("changedFrom").equals(json2.getStr("changedFrom")) &&
                json1.getStr("changedTo").equals(json2.getStr("changedTo"));
    }

    private String getUserIdFromCreatedBy(String createdBy) {
        if (StringUtils.isNotEmpty(createdBy)) {
            String[] idStrings = createdBy.split(",");
            if (idStrings.length == 2) {
                return idStrings[0];
            } else {
                return createdBy;
            }
        }
        return "unknown";
    }

    private void parseJobRecordMap(String tableName, CanalEntry.EventType eventType, List<CanalEntry.RowData> rowDataList, Map<Long, Long> jobAdditional2JobidMap, Map<Long, JobRecord> jobRecordMap) {
        for (CanalEntry.RowData rowData : rowDataList) {
            String createdBy = "";
            String createdTime = "";
            Long jobId = jobChangeRecordService.recordJobId(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, jobAdditional2JobidMap);
            Long tenantId = null;
            Boolean initalJob = false;
            List<JSONObject> esDocuments = new ArrayList<>();
            if (eventType == CanalEntry.EventType.INSERT) {
                createdTime = jobChangeRecordService.recordCreatedTime(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, createdTime);
                createdBy = jobChangeRecordService.recordCreatedBy(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, createdBy);
                if (!Objects.equals(createdBy, "")) {
                    String[] idStrings = createdBy.split(",");
                    if (idStrings.length == 2) {
                        tenantId = Long.parseLong(idStrings[1]);
                    }
                }
                initalJob = jobChangeRecordService.checkCreateJob(rowData.getAfterColumnsList(), tableName, jobId, esDocuments);
                if (!initalJob) {
                    jobChangeRecordService.recordUpdateJobRows(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, false, jobId, createdTime, createdBy, esDocuments);
                }
            } else if (eventType == CanalEntry.EventType.UPDATE) {
                createdTime = jobChangeRecordService.recordCreatedTime(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, createdTime);
                createdBy = jobChangeRecordService.recordCreatedBy(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, createdBy);
                if (!Objects.equals(createdBy, "")) {
                    String[] idStrings = createdBy.split(",");
                    if (idStrings.length == 2) {
                        tenantId = Long.parseLong(idStrings[1]);
                    }
                }
                jobChangeRecordService.recordUpdateJobRows(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, true, jobId, createdTime, createdBy, esDocuments);
            } else if (eventType == CanalEntry.EventType.DELETE) {
                jobChangeRecordService.recordDeleteJobRows(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), tableName, esDocuments);
            }

            log.info("[APN: EsFillerJobRecordService] job record input, jobId: {}, tenantId: {}, createdTime: {}, createdBy: {}, esDocuments: {}", jobId, tenantId, createdTime, createdBy, esDocuments);
            if (jobId != null) {
                JobRecord jobRecord = jobRecordMap.get(jobId);
                if (jobRecord == null) {
                    jobRecord = new JobRecord();
                    jobRecord.setJobId(jobId);
                }
                jobRecord.setInitial(initalJob);
                if (StringUtil.isNotEmpty(createdTime)) {
                    jobRecord.setCreateTime(createdTime);
                }
                if (tenantId != null) {
                    jobRecord.setTenantId(tenantId);
                }
                if (StringUtil.isNotEmpty(createdBy)) {
                    jobRecord.setCreatedBy(createdBy);
                }
                jobRecord.addEsDocuments(esDocuments);
                jobRecordMap.put(jobId, jobRecord);
            }
        }
    }

    /**
     * 批量查询 jobAdditionalId 对应的 jobId
     */
    private Map<Long, Long> getJobAdditional2JobidMap(List<EventContent> eventContents) {

        Set<Long> jobAdditionalIds = eventContents.stream().filter(eventContent -> JOB_ADDITIONAL_INFO.equals(eventContent.tableName()))
                .flatMap(eventContent -> eventContent.rowChange().getRowDatasList().stream())
                .flatMap(rowData -> rowData.getAfterColumnsList().stream())
                .filter(column -> column.getName().equals(ID) && !column.getValue().isBlank())
                .map(CanalEntry.Column::getValue).map(Long::parseLong)
                .collect(Collectors.toSet());
        if (jobAdditionalIds.isEmpty()) {
            return Collections.emptyMap();
        }
        return jobRepository.findJobIdsByAdditionalIds(jobAdditionalIds).stream()
                .collect(Collectors.toMap(JobAdditionalToJobId::jobAdditionalId, JobAdditionalToJobId::jobId));
    }
}
