package com.altomni.apn.canal.disruptor.handler;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.service.sync.agency.SyncAgencyService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.google.common.collect.Iterables;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;

@Slf4j
@Component
public class AgencySyncHandler implements EventHandler<CanalEvent> {

    private final CanalService canalService;
    private final SyncAgencyService syncAgencyService;

    public AgencySyncHandler(CanalService canalService, SyncAgencyService syncAgencyService) {
        this.canalService = canalService;
        this.syncAgencyService = syncAgencyService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> AgencySyncHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Set<Long> agencyIds = eventContents.stream()
                .filter(eventContent -> AGENCY.equals(eventContent.tableName()) || AGENCY_RELATED_TABLES.contains(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    log.info("[canal:AgencySyncHandler] syncTable, table {} changed, event type {}", tableName, eventType);

                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();

                    return switch (eventType) {
                        case INSERT -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, false))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, true))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case DELETE -> rowDataList.stream()
                                .map(rowData -> rowDeleted(rowData.getBeforeColumnsList(), tableName))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                }).collect(Collectors.toSet());

        if (agencyIds.isEmpty()) {
            return;
        }

        syncAgenciesToEs(agencyIds);
    }

    private void syncAgenciesToEs(Set<Long> pendingAgenciesToSync) {
        log.info("[Canal:AgencySyncHandler] agencySync startSyncAgenciesToEs {}", pendingAgenciesToSync);
        try {
            for (List<Long> partition : Iterables.partition(pendingAgenciesToSync, 50)) {
                syncAgencyService.synchronizeAgency(partition, RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL, 0);
            }
        } catch (Exception e) {
            log.error("[Canal:AgencySyncHandler] agencySync SyncAgencyToES Failed, agencies: {}, error: {}", pendingAgenciesToSync, ExceptionUtil.getStackTrace(e));
            canalService.insertAll(pendingAgenciesToSync, SyncIdTypeEnum.AGENCY, FailReasonEnum.ERROR, e.getMessage(), RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL);
        }
    }

    private Optional<Long> changeRows(List<CanalEntry.Column> columns, String tableName, boolean update) {
        if (AGENCY.equals(tableName)) {
            return agencyChange(columns, tableName, update);
        }
        if (AGENCY_RELATED_TABLES.contains(tableName)) {
            return agencyRelatedChange(columns, tableName);
        }
        return Optional.empty();
    }

    private Optional<Long> rowDeleted(List<CanalEntry.Column> columns, String tableName) {
        if (!AGENCY_RELATED_TABLES.contains(tableName)) {
            return Optional.empty();
        }
        return agencyRelatedChange(columns, tableName);
    }

    private Optional<Long> agencyChange(List<CanalEntry.Column> columns, String tableName, boolean update) {
        String agencyId = null;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            log.debug("[Canal:AgencySyncHandler] jobSync jobChange: table: {}, column: {}, value: {}, updated: {}, jobId: {}", tableName, name, column.getValue(), column.getUpdated(), agencyId);

//            // for fill back
//            if (update && name.equals(LAST_SYNC_TIME) && column.getUpdated()) {
//                log.info("[Canal:AgencySyncHandler] jobSync jobChange: column: {}, value: {}, updated: {} drop process", name, column.getValue(), column.getUpdated());
//                return Optional.empty();
//            }

            if (name.equals(ID)) {
                agencyId = column.getValue();
            }
        }
        log.info("[Canal:AgencySyncHandler] received changed agencyId: {} from table: {}", agencyId, tableName);
        return Optional.ofNullable(agencyId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

    private Optional<Long> agencyRelatedChange(List<CanalEntry.Column> columns, String tableName) {
        String agencyId = null;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            if (name.equals(AGENCY_ID)) {
                agencyId = column.getValue();
            }
            log.debug("agencyRelatedChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, column.getValue(), column.getUpdated());
        }
        log.info("[Canal:AgencySyncHandler] received changed agencyId: {} from tableName: {}", agencyId, tableName);
        return Optional.ofNullable(agencyId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }
}
