package com.altomni.apn.canal.service.sync.user.impl;

import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.repository.RepositoryCustom;
import com.altomni.apn.canal.service.login.LoginService;
import com.altomni.apn.canal.service.sync.SyncDataServiceTemplate;
import com.altomni.apn.canal.service.sync.user.SyncUserService;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("syncUserService")
public class SyncUserServiceImpl extends SyncDataServiceTemplate implements SyncUserService {

    public static final String URI_DATA_MQ_MESSAGE_COUNT_CHECK = "/user/api/v3/canal/check-user-mq-message-count";

    public static final String URI_DATA_SYNCHRONIZATION = "/user/api/v3/canal/sync-users-to-mq";

    public SyncUserServiceImpl(CanalProperties canalProperties, LoginService loginService, RepositoryCustom repositoryCustom, CanalService canalService) {
        super(canalProperties, loginService, repositoryCustom, canalService, SyncIdTypeEnum.USER);
    }


    @Override
    public void validateSql(String sql) {
        repositoryCustom.getIdsBySql(sql + " limit 1");
    }

    @Async
    @Override
    public void bulkSyncUsers(String sql) {
        bulkSyncData(sql);
    }

    @Override
    public boolean isUserQueueAvailable() {
        return isDataQueueAvailable();
    }

    @Override
    public void synchronizeUser(List<Long> ids, int priority, int deep) {
        doSynchronize(ids, priority, deep);
    }

    @Override
    protected String getUriDataMqMessageCountCheck() {
        return URI_DATA_MQ_MESSAGE_COUNT_CHECK;
    }

    @Override
    protected String getUriDataSynchronization() {
        return URI_DATA_SYNCHRONIZATION;
    }

}
