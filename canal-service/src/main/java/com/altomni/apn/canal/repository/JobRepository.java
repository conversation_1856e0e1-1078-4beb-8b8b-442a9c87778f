package com.altomni.apn.canal.repository;

import com.altomni.apn.canal.repository.dto.JobAdditionalToJobId;
import com.altomni.apn.common.domain.job.JobV3;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the Job entity.
 */
@Repository
public interface JobRepository extends JpaRepository<JobV3, Long> {

    @Query(value = "select new com.altomni.apn.canal.repository.dto.JobAdditionalToJobId(j.jobAdditionalInfo.id, j.id) from  JobV3 j where j.jobAdditionalInfo.id in :additionalIds")
    List<JobAdditionalToJobId> findJobIdsByAdditionalIds(@Param("additionalIds") Set<Long> additionalIds);
}
