package com.altomni.apn.canal.disruptor.handler;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.service.sync.company.SyncCompanyService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.google.common.collect.Iterables;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;

@Slf4j
@Component
public class CompanySyncHandler implements EventHandler<CanalEvent> {
    private final CanalService canalService;
    private final SyncCompanyService syncCompanyService;

    public CompanySyncHandler(CanalService canalService, SyncCompanyService syncCompanyService) {
        this.canalService = canalService;
        this.syncCompanyService = syncCompanyService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> CompanySyncHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Set<Long> companyIds = eventContents.stream()
                .filter(eventContent -> COMPANY.equals(eventContent.tableName()) || COMPANY_RELATED_TABLES.contains(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    log.info("[canal] syncTable, table {} changed, event type {}", tableName, eventType);

                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();

                    return switch (eventType) {
                        case INSERT -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, false))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, true))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case DELETE -> rowDataList.stream()
                                .map(rowData -> rowDeleted(rowData.getBeforeColumnsList(), tableName))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                }).collect(Collectors.toSet());
        if (companyIds.isEmpty()) {
            return;
        }
        syncCompaniesToEs(companyIds);

    }

    private void syncCompaniesToEs(Set<Long> pendingCompaniesToSync) {
        log.info("startSyncCompaniesToEs {}", pendingCompaniesToSync);
        try {
            for (List<Long> partition : Iterables.partition(pendingCompaniesToSync, 50)) {
                syncCompanyService.synchronizeCompanies(partition, RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL, 0);
            }
        } catch (Exception e) {
            log.error("[Canal] sync_company_to_es, company: {}, error: {}", pendingCompaniesToSync, ExceptionUtil.getStackTrace(e));
            canalService.insertAll(pendingCompaniesToSync, SyncIdTypeEnum.COMPANY, FailReasonEnum.ERROR, e.getMessage(), RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL);
        }
    }


    private Optional<Long> changeRows(List<CanalEntry.Column> columns, String tableName, boolean update) {
        if (COMPANY.equals(tableName)) {
            return companyChange(columns, tableName, update);
        }
        if (COMPANY_RELATED_TABLES.contains(tableName)) {
            return companyRelatedChange(columns, tableName);
        }
        return Optional.empty();

    }

    private Optional<Long> rowDeleted(List<CanalEntry.Column> columns, String tableName) {
        if (!COMPANY_RELATED_TABLES.contains(tableName)) {
            return Optional.empty();
        }
        return companyRelatedChange(columns, tableName);
    }

    private Optional<Long> companyChange(List<CanalEntry.Column> columns, String tableName, boolean update) {
        String companyId = null;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            log.debug("companyChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, column.getValue(), column.getUpdated());
            // for fill back
            if (update && name.equals(LAST_SYNC_TIME) && column.getUpdated()) {
                return Optional.empty();
            }
            if (name.equals(ID)) {
                companyId = column.getValue();
            }
        }
        log.info("[canal] received changed companyId: {} from table: {}", companyId, tableName);
        return Optional.ofNullable(companyId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }


    private Optional<Long> companyRelatedChange(List<CanalEntry.Column> columns, String tableName) {
        String companyId = null;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            if (name.equals(COMPANY_ID)) {
                companyId = column.getValue();
            }
            log.debug("companyRelatedChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, column.getValue(), column.getUpdated());
        }
        log.info("[canal] received changed companyId: {} from tableName", companyId, tableName);
        return Optional.ofNullable(companyId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }
}
