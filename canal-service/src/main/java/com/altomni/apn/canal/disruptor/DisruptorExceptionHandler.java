package com.altomni.apn.canal.disruptor;

import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.common.utils.NotificationUtils;
import com.lmax.disruptor.ExceptionHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DisruptorExceptionHandler implements ExceptionHandler<CanalEvent> {

    private final CanalProperties canalProperties;

    public DisruptorExceptionHandler(CanalProperties canalProperties) {
        this.canalProperties = canalProperties;
    }

    @Override
    public void handleEventException(Throwable ex, long sequence, CanalEvent event) {
        log.error("Disruptor consumer canal event exception!, event: {}", event, ex);
        NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "WARN: Disruptor consumer canal event exception! exception message: " + ex.getLocalizedMessage());
    }

    @Override
    public void handleOnStartException(Throwable ex) {
        log.error("Disruptor start exception!", ex);
        NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "WARN: Disruptor start exception! exception message: " + ex.getLocalizedMessage());
    }

    @Override
    public void handleOnShutdownException(Throwable ex) {
        log.error("Disruptor shutdown exception!", ex);
        NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "WARN: Disruptor shutdown exception! exception message: " + ex.getLocalizedMessage());
    }
}
