package com.altomni.apn.canal.disruptor;

import com.alibaba.nacos.shaded.com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.alibaba.otter.canal.client.CanalConnector;
import com.altomni.apn.canal.disruptor.handler.*;
import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.WaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.util.List;
import java.util.concurrent.ThreadFactory;

@Slf4j
@Component
public class DisruptorQueue {

    private final Disruptor<CanalEvent> disruptor;
    private final RingBuffer<CanalEvent> ringBuffer;


    public DisruptorQueue(
            DisruptorExceptionHandler disruptorExceptionH<PERSON><PERSON>,
            TalentSyncHandler talentSyncHandler,
            JobSyncHandler jobSyncHandler,
            CompanySyncHandler companySyncHandler,
            CompanyClientNoteSyncHandler companyClientNoteSyncHandler,
            CompanyProgressNoteSyncHandler companyProgressNoteSyncHandler,
            JobRecordHandler jobRecordHandler,
            TalentRecordHandler talentRecordHandler,
            AssignmentRecordHandler assignmentRecordHandler,
            CompanyContactSyncHrHandler companyContactSyncHrHandler,
            CompanySyncHrHandler companySyncHrHandler,
            JobSyncHrHandler jobSyncHrHandler,
            JobSyncAgencyHandler jobSyncAgencyHandler,
            TalentSyncHrHandler talentSyncHrHandler,
            AgencySyncHandler agencySyncHandler,
            ClearHandler clearHandler) {

        // 环形数组长度，必须是2的n次幂
        int ringBufferSize = 4096;
        // 消费者线程工厂
        ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("async-disruptor-consumer-pool-%d").build();
        // 等待策略
        WaitStrategy waitStrategy = new BlockingWaitStrategy();
        disruptor = new Disruptor<>(CanalEvent::new, ringBufferSize, threadFactory, ProducerType.SINGLE, waitStrategy);
        // 事件处理，最后 clear 事件
        disruptor.handleEventsWith(jobSyncHandler, talentSyncHandler, companySyncHandler, companyClientNoteSyncHandler,
                companyProgressNoteSyncHandler, jobRecordHandler, talentRecordHandler, assignmentRecordHandler,companyContactSyncHrHandler,
                companySyncHrHandler,jobSyncHrHandler,jobSyncAgencyHandler,talentSyncHrHandler,agencySyncHandler).then(clearHandler);
        // 异常处理
        disruptor.setDefaultExceptionHandler(disruptorExceptionHandler);
        disruptor.start();
        ringBuffer = disruptor.getRingBuffer();
    }

    public boolean publish(long batchId, List<EventContent> eventContents, CanalConnector connector) {
        return ringBuffer.tryPublishEvent((event, sequence, param1, param2) -> {
            event.setBatchId(batchId);
            event.setEventContents(param2);
            event.setConnector(connector);
        }, batchId, eventContents);
    }

    public long remainingCapacity() {
        return ringBuffer.remainingCapacity();
    }

    public long bufferSize() {
        return ringBuffer.getBufferSize();
    }

    /**
     * 关闭时保证队列里面的消息处理完
     */
    @PreDestroy
    public void shutdown() {
        long capacity = ringBuffer.getBufferSize() - ringBuffer.remainingCapacity();
        log.info("Shutdown Disruptor..., ringBuffer capacity: {}", capacity);
        disruptor.shutdown();
    }


}
