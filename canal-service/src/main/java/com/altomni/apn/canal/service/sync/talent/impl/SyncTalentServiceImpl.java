package com.altomni.apn.canal.service.sync.talent.impl;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.repository.TalentRepositoryCustom;
import com.altomni.apn.canal.service.login.LoginService;
import com.altomni.apn.canal.service.login.impl.LoginServiceImpl;
import com.altomni.apn.canal.service.sync.talent.SyncTalentService;
import com.altomni.apn.canal.web.rest.vm.MqMessageCountVM;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncTalentServiceImpl implements SyncTalentService {
    public static final String URI_TALENT_MQ_MESSAGE_COUNT_CHECK = "/talent/api/v3/canal/check-talent-mq-message-count";
    public static final String URI_TALENT_SYNCHRONIZATION = "/talent/api/v3/canal/sync-talents-to-mq";

    public static final String URI_BULK_TALENT_SYNCHRONIZATION = "/talent/api/v3/canal/bulk-sync-talents-to-mq";

    private RestTemplate restTemplate = new RestTemplate();

    @Resource
    private TalentRepositoryCustom talentRepositoryCustom;

    @Resource
    private CanalProperties canalProperties;

    @Resource
    private LoginService loginService;

    @Resource
    private CanalService canalService;

    Map<String, Object> talentParamMap = new HashMap<>();

    private MqMessageCountVM checkTalentMessageCount(){
        String api = canalProperties.getApiBase() + URI_TALENT_MQ_MESSAGE_COUNT_CHECK;
        HttpHeaders headers = loginService.buildHeaders();
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(headers);
        ResponseEntity<MqMessageCountVM> response = this.restTemplate.postForEntity(api, entity, MqMessageCountVM.class);
        MqMessageCountVM messageCountVM = response.getBody();
        log.info("checkTalentMessageCount:" + messageCountVM);
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED
                && messageCountVM.getError() != null
                && messageCountVM.getError().contains("invalid_token")) {
            log.warn("[checkTalentMessageCount @-1] Access token expired.");
            // clean the token
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            return checkTalentMessageCount();
        }
        return messageCountVM;
    }

    public boolean isTalentQueueAvailable(){
        try {
            final MqMessageCountVM messageCountVM = this.checkTalentMessageCount();
            return Objects.nonNull(messageCountVM.getMessageCount())
                    && Objects.nonNull(messageCountVM.getMaximumMessageCount())
                    && messageCountVM.getMaximumMessageCount() > messageCountVM.getMessageCount();
        } catch (Exception e) {
            log.error("isTalentQueueAvailable is error, message = {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    private boolean isTalentQueueEmpty(){
        return this.checkTalentMessageCount().getMessageCount() == 0;
    }

    @Override
    public void synchronizeTalents(Collection<Long> talentIds, int priority, int deep) {
        talentParamMap.put("talentIds", talentIds);
        talentParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_TALENT_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(talentParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeTalents @-1] synchronizeTalents error, talentIds: {}, responseCode: {}, responseBody: {}",
                        talentIds, response.getStatusCode(), response.getBody());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "SynchronizeTalents error " +
                        "\n\tTalentIds: " + talentIds +
                        "\n\tResponse Code: " + response.getStatusCode() +
                        "\n\tResponse Body: " + response.getBody();
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                log.warn("[synchronizeTalents @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeTalents] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeTalents(talentIds, priority, deep + 1);
                }else {
                    canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeTalents] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeTalents @{}] SynchronizeTalents error. TalentIds: {}, error: {}", SecurityUtils.getUserId(), talentIds, ExceptionUtil.getStackTrace(e));
                String message = "SynchronizeTalents error " +
                        "\n\ttalentIds: " + talentIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
            log.error("[synchronizeTalents @{}] SynchronizeTalents error, talentIds: {}, error: {}", SecurityUtils.getUserId(), talentIds, ExceptionUtil.getStackTrace(e));
            String message = "SynchronizeTalents error " +
                    "\n\ttalentIds: " + talentIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }


    public void bulkSynchronizeTalents(Collection<Long> talentIds, int priority, int deep) {
        talentParamMap.put("talentIds", talentIds);
        talentParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_BULK_TALENT_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(talentParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[bulkSynchronizeTalents @-1] bulkSynchronizeTalents error, talentIds: {}, responseCode: {}, responseBody: {}",
                        talentIds, response.getStatusCode(), response.getBody());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "bulkSynchronizeTalents error " +
                        "\n\tTalentIds: " + talentIds +
                        "\n\tResponse Code: " + response.getStatusCode() +
                        "\n\tResponse Body: " + response.getBody();
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                log.warn("[bulkSynchronizeTalents @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeTalents] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    bulkSynchronizeTalents(talentIds, priority, deep + 1);
                }else {
                    canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[bulkSynchronizeTalents] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[bulkSynchronizeTalents @{}] bulkSynchronizeTalents error. TalentIds: {}, error: {}", SecurityUtils.getUserId(), talentIds, ExceptionUtil.getStackTrace(e));
                String message = "BulkSynchronizeTalents error " +
                        "\n\ttalentIds: " + talentIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            canalService.insertAll(talentIds, SyncIdTypeEnum.TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
            log.error("[bulkSynchronizeTalents @{}] bulkSynchronizeTalents error, talentIds: {}, error: {}", SecurityUtils.getUserId(), talentIds, ExceptionUtil.getStackTrace(e));
            String message = "BulkSynchronizeTalents error " +
                    "\n\ttalentIds: " + talentIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    @Override
    public void validateSql(String sql) {
        talentRepositoryCustom.getIdsBySql(sql + " limit 1");
    }

    @Async
    @Override
    public void bulkSyncTalents(String sql) {
        try {
            Integer unfinished = bulkSyncTalents(sql, 50);
            syncFinish(unfinished);
        } catch (Exception e) {
            log.error("[bulkSyncTalents @-1] bulkSyncTalents error: {} at: {}", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e), System.currentTimeMillis());
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), String.format("synchronizeTalents error:  %s", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e)));
        }
    }

    private void syncFinish(Integer unfinished){
        if (unfinished.intValue() == 0){
            log.info("bulkSyncTalents finished");
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "BulkSyncTalents finished.");
        }else{
            log.warn("bulkSyncTalents unfinished. Count: {}", unfinished);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "BulkSyncTalents unfinished. Count: " + unfinished);
        }
    }

    private Integer bulkSyncTalents(String sql, int pageSize) throws Exception {
        String startTime = DateUtil.getStandardCurrentTime();
        Long lastId = Long.MAX_VALUE;
        List<Long> talentIds = talentRepositoryCustom.getIdsBySql(String.format("%s and t.id < %d order by t.id desc limit %d", sql, lastId, pageSize));
        Long maxId = 0L;
        if (!CollectionUtils.isEmpty(talentIds)) {
            maxId = talentIds.get(0);
        }
        log.info(talentIds+"");
        int checkTalentQueueTimes = 1;
        while(!CollectionUtils.isEmpty(talentIds)){
            if (checkTalentQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The talent queue is abnormal for 30 minutes.");
            }
            if (!isTalentQueueAvailable()){
                checkTalentQueueTimes ++;
                Thread.sleep(5000);
                continue;
            }
            lastId = talentIds.get(talentIds.size() - 1);
            //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "Last talent ID: " + lastId);
            log.info("LastTalentID: {}", lastId);
            bulkSynchronizeTalents(talentIds, RabbitMqConstant.MESSAGE_PRIORITY_LOW, 0);
            talentIds = talentRepositoryCustom.getIdsBySql(String.format("%s and t.id < %d order by t.id desc limit %d", sql, lastId, pageSize));
            checkTalentQueueTimes = 1;
        }
        while (!isTalentQueueEmpty()) {
            checkTalentQueueTimes ++;
            if (checkTalentQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The talent queue is abnormal for 30 minutes.");
            }
            Thread.sleep(5000);
        }
        Thread.sleep(60000);
        Long unfinished = talentRepositoryCustom.getIdsBySql(String.format("%s and t.id<= %d and (t.last_sync_time is null or t.last_sync_time < '%s')",
                sql.replaceFirst("t.id", "count(t.id)"), maxId, startTime)).get(0);
        return unfinished.intValue();
    }

    @Override
    public void bulkSyncAllTalents(Pageable pageable) {

    }
}
