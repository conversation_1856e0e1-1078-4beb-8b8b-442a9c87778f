package com.altomni.apn.canal.service.sync.agency.impl;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.repository.JobRepositoryCustom;
import com.altomni.apn.canal.service.login.LoginService;
import com.altomni.apn.canal.service.login.impl.LoginServiceImpl;
import com.altomni.apn.canal.service.sync.agency.SyncAgencyService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncAgencyServiceImpl implements SyncAgencyService {
    public static final String URI_AGENCY_SYNCHRONIZATION = "/agency/api/v3/canal/sync-agencies-to-mq";

    private RestTemplate restTemplate = new RestTemplate();

    @Resource
    private JobRepositoryCustom jobRepositoryCustom;

    @Resource
    private CanalProperties canalProperties;

    @Resource
    private LoginService loginService;

    @Resource
    private CanalService canalService;

    Map<String, Object> agencyParamMap = new HashMap<>();

    @Override
    public void synchronizeAgency(Collection<Long> agencyIds, int priority, int retryCount) {
        agencyParamMap.put("agencyIds", agencyIds);
        agencyParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_AGENCY_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(agencyParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeAgency @{}] synchronizeAgency error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(agencyIds, SyncIdTypeEnum.AGENCY, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "synchronizeAgency error " +
                        "\n\tagencyIds: " + agencyIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        } catch (HttpClientErrorException e) {
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                log.error("[synchronizeAgency @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));

                if (retryCount < 1) { // retry only once
                    synchronizeAgency(agencyIds, priority, retryCount + 1);
                } else {
                    canalService.insertAll(agencyIds, SyncIdTypeEnum.AGENCY, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeAgency] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            } else {
                canalService.insertAll(agencyIds, SyncIdTypeEnum.AGENCY, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeAgency @{}] synchronizeAgency error. JobIds: {}. Error: {}", SecurityUtils.getUserId(), agencyIds, ExceptionUtil.getStackTrace(e));
                String message = "synchronizeAgency error" +
                        "\n\tagencyIds: " + agencyIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        } catch (Exception e){
            canalService.insertAll(agencyIds, SyncIdTypeEnum.AGENCY, FailReasonEnum.ERROR, e.getMessage(), priority);
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            log.error("[synchronizeAgency @{}] synchronizeAgency error, jobIds: {}, error: {}", SecurityUtils.getUserId(), agencyIds, ExceptionUtil.getStackTrace(e));
            String message = "synchronizeAgency error" +
                    "\n\tagencyIds: " + agencyIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

}
