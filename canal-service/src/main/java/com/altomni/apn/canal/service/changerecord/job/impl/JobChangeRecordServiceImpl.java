package com.altomni.apn.canal.service.changerecord.job.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.CanalEntry.Column;
import com.altomni.apn.canal.repository.JobRepository;
import com.altomni.apn.canal.service.changerecord.job.JobChangeRecordService;
import com.altomni.apn.common.domain.enumeration.ContactCategoryType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.FeeDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.stream.Collectors;

import static com.altomni.apn.canal.constants.LogEventType.EVENT_TYPE_INSERT;
import static com.altomni.apn.canal.constants.LogEventType.EVENT_TYPE_UPDATE;


@Slf4j
@Service
@Data
public class JobChangeRecordServiceImpl implements JobChangeRecordService {

    private static final String ID = "id";

    private static final String JOB = "job";

    private static final String COMPANY_ID = "company_id";

    private static final String TITLE = "title";

    private static final String JOB_TYPE = "job_type";

    private static final String STATUS = "status";

    private static final String CURRENCY = "currency";

    private static final String START_DATE = "start_date";

    private static final String END_DATE = "end_date";

    private static final String CONTRACT_DURATION = "contract_duration";

    private static final String POSTING_TIME = "posting_time";

    private static final String JD_URL = "jd_url";

    private static final String OPENINGS = "openings";

    private static final String MAX_SUBMISSIONS = "max_submissions";

    private static final String MINIMUM_DEGREE_ID = "minimum_degree_id";

    private static final String RECRUITMENT_PROGRESS_ID = "recruitment_progress_id";

    private static final String ENUM_PRIORITY_ID = "enum_priority_id";

    private static final String FLEXIBLE_LOCATION = "flexible_location";

    private static final String SALES_LEAD_ID = "sales_lead_id";

    private static final String COOPERATION_STATUS = "cooperation_status";

    private static final String CODE = "code";

    private static final String LAST_MODIFIED_DATE = "last_modified_date";

    private static final String LAST_MODIFIED_BY = "last_modified_by";

    private static final String LAST_EDITED_TIME = "last_edited_time";

    private static final String LAST_SYNC_TIME = "last_sync_time";

    private static final String JOB_IPG_RELATION = "job_ipg_relation";

    private static final String APN_JOB_ID = "apn_job_id";

    private static final String IPG_JOB_STATUS = "ipg_job_status";

    private static final String IPG_JOB_TYPE = "ipg_job_type";

    private static final String IPG_JOB_DESCRIPTION = "ipg_job_description";

    private static final String IPG_JOB_SUMMARY = "ipg_job_summary";

    private static final String IPG_JOB_RESPONSIBILITIES = "ipg_job_responsibilities";

    private static final String IPG_JOB_REQUIREMENTS = "ipg_job_requirements";

    private static final String IPG_JOB_REQUIRED_SKILLS = "ipg_job_required_skills";

    private static final String JOB_JOB_FUNCTION_RELATION = "job_job_function_relation";

    private static final String JOB_FUNCTION_ID = "job_function_id";

    private static final String JOB_LOCATION = "job_location";

    private static final String JOB_LOCATION_ID = "job_location_id";

    private static final String JOB_OFFICIAL_COUNTRY = "official_country";

    private static final String JOB_OFFICIAL_CITY = "official_city";

    private static final String JOB_OFFICIAL_PROVINCE = "official_province";

    private static final String JOB_OFFICIAL_COUNTY = "official_county";

    private static final String JOB_ORIGINAL_LOC = "original_loc";

    private static final String JOB_COMPANY_CONTACT_RELATION = "job_company_contact_relation";

    private static final String JOB_CONTACT_CATEGORY = "contact_category";

    private static final String AGENCY_ACTIVITY = "agency_activity";

    private static final String AGENCY_ACTIVITY_TYPE = "activity_type";

    private static final String AGENCY_ACTIVITY_DETAILS = "details";

    private static final String JOB_CLIENT_CONTACT_ID = "client_contact_id";

    private static final String JOB_CLIENT_CONTACT_TALENT_ID = "talent_id";

    private static final String JOB_NOTE = "job_note";

    private static final String JOB_NOTE_TITLE = "title";

    private static final String JOB_NOTE_NOTE = "note";

    private static final String JOB_NOTE_PRIORITY = "priority";

    private static final String JOB_PREFERRED_DEGREE_RELATION = "job_preferred_degree_relation";

    private static final String JOB_PREFERRED_DEGREE_ID = "preferred_degree_id";

    private static final String JOB_PREFERRED_LANGUAGE_RELATION = "job_preferred_languages_relation";

    private static final String JOB_PREFERRED_LANGUAGE_ID = "preferred_languages_id";

    private static final String JOB_REQUIRED_LANGUAGE_RELATION = "job_required_languages_relation";

    private static final String JOB_REQUIRED_LANGUAGE_ID = "required_languages_id";

    public static final String JOB_ADDITIONAL_INFO = "job_additional_info";

    private static final String JOB_EXTEND_INFO = "extended_info";

    private static final String JOB_LOCAL_EXTEND_INFO = "local_extended_info";

    private static final String JOB_REQUIRED_SKILLS = "requiredSkills";

    private static final String JOB_PREFERRED_SKILLS = "preferredSkills";

    private static final String JOB_SKILL_NAME = "skillName";

    private static final String JOB_EXPERIENCE_YEAR_RANGE = "experienceYearRange";

    private static final String JOB_BILL_RANGE = "billRange";

    private static final String JOB_SALARY_RANGE = "salaryRange";

    private static final String JOB_PAY_TYPE = "payType";

    private static final String JOB_DEPARTMENT = "department";

    private static final String JOB_REASON_FOR_RECRUITMENT = "reasonForRecruitment";

    private static final String JOB_TEAM_COMPOSITION = "teamComposition";

    private static final String JOB_PREFERRED_COMPANIES = "preferredCompanies";

    private static final String JOB_PREFERRED_INDUSTRY = "preferredIndustry";

    private static final String JOB_SUGGESTIONS_FOR_PROSPECTING = "suggestionsForProspecting";

    private static final String JOB_RECOMMEND_APPROACH = "recommendedApproach";

    private static final String JOB_ESTIMATED_JOB_FEE = "estimatedJobFee";

    private static final String JOB_ESTIMATED_JOB_FEE_NUMBER = "fee";

    private static final String JOB_ESTIMATED_JOB_FEE_CURRENCY = "currency";

    private static final String JOB_FEE_STRUCTURE = "feeStructure";

    private static final String JOB_CONTACT_SIGNING_PARTY = "contractSigningParty";


    private static final String JOB_SUMMARY = "summary";

    private static final String JOB_RESPONSIBILITIES = "responsibilities";

    private static final String JOB_REQUIREMENTS = "requirements";

    public static final String JOB_ID = "job_id";

    public static final String USER_JOB_RELATION = "user_job_relation";

    public static final String USER_ID = "user_id";

    public static final String JOB_ASSIGNED_USER_USER_ID = "jobAssignedUserUserId";

    @Resource
    private JobRepository jobRepository;

    @Override
    public Set<String> getJobAllowDuplicatedKeysSet() {
        Set<String> excludesDuplicatedKeys = new HashSet<>(new ArrayList<>(List.of(convertSnakeCaseToCamelCase(JOB_FUNCTION_ID), JOB_ASSIGNED_USER_USER_ID)));
        return excludesDuplicatedKeys;
    }

    private void esInfo(JSONObject esDocumentJson, Column preColumn, Column afterColumn, Boolean update) {
        if (update) {
            esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
            esDocumentJson.put("changedFrom", preColumn == null ? "" : preColumn.getValue());
        } else {
            esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
            esDocumentJson.put("changedFrom", "");
        }
        esDocumentJson.put("changedTo", afterColumn == null ? "" : afterColumn.getValue());
    }

    private void checkColumnMatch(List<Column> preColumns, List<Column> afterColumns, Boolean update) {
        if (update) {
            if (preColumns.size() != afterColumns.size()) {
                log.error("The amount of changed columns is not same before changing and after.");
            }
        }
    }
    public static String transToFormatTime(String time){
        if (StringUtils.isBlank(time)){
            return StrUtil.EMPTY;
        }
        return formatTime(time).toString();
    }

    public static Instant formatTime(String time) {
        List<DateTimeFormatter> formatters = Arrays.asList(
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS"),
                DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
        );

        for (DateTimeFormatter formatter : formatters) {
            try {
                LocalDateTime localDateTime = LocalDateTime.parse(time, formatter);
                ZoneId currentZone = ZoneId.systemDefault();
                ZonedDateTime zonedDateTime = localDateTime.atZone(currentZone);
                return zonedDateTime.toInstant();
            } catch (DateTimeParseException e) {
                // Continue to try the next formatter
            }
        }

        log.error("Cannot parse current time for the talent log: time:{}", time);
        throw new IllegalArgumentException("Unsupported date-time format: " + time);
    }

    @Override
    public Boolean checkCreateJob(List<Column> afterColumns, String tableName, Long jobId, List<JSONObject> esDocuments) {
        if (JOB.equals(tableName)) {
            for (Column afterColumn : afterColumns) {
                JSONObject esDocumentJson = new JSONObject();
                if (ID.equals(afterColumn.getName()) && afterColumn.getUpdated()) {
                    esDocumentJson.put("key", JOB);
                    esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                    esDocumentJson.put("changedFrom", "");
                    esDocumentJson.put("changedTo", afterColumn.getValue());
                    esDocuments.add(esDocumentJson);
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public List<JSONObject> generateNewJob(Long jobId) {
        JSONObject esDocumentJson = new JSONObject();
        List<JSONObject> esDocuments = new ArrayList<>();
        esDocumentJson.put("key", JOB);
        esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
        esDocumentJson.put("changedFrom", "");
        esDocumentJson.put("changedTo", jobId);
        esDocuments.add(esDocumentJson);
        return esDocuments;
    }

    private void recordJob(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> jobFields = new HashSet<>(new ArrayList<>(List.of(COMPANY_ID, TITLE, JOB_TYPE, STATUS, CURRENCY, START_DATE, END_DATE, POSTING_TIME, JD_URL, OPENINGS, MAX_SUBMISSIONS, MINIMUM_DEGREE_ID, RECRUITMENT_PROGRESS_ID, ENUM_PRIORITY_ID, FLEXIBLE_LOCATION, CODE, SALES_LEAD_ID, CONTRACT_DURATION)));
        if (JOB.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (jobFields.contains(columnName) && afterColumn.getUpdated()) {
                    switch (columnName) {
                        case COMPANY_ID:
                            esDocumentJson.put("key", "companyId");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case JOB_TYPE:
                            esDocumentJson.put("key", "jobType");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", JobType.fromDbValue(Integer.valueOf(preColumn.getValue())).name());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", JobType.fromDbValue(Integer.valueOf(afterColumn.getValue())).name());
                            break;
                        case STATUS:
                            esDocumentJson.put("key", STATUS);
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", JobStatus.fromDbValue(Integer.valueOf(preColumn.getValue())).name());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", JobStatus.fromDbValue(Integer.valueOf(afterColumn.getValue())).name());
                            break;
                        case CURRENCY:
                            esDocumentJson.put("key", CURRENCY);
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case MINIMUM_DEGREE_ID:
                            esDocumentJson.put("key", "minimumDegreeId");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case START_DATE:
                            if (null != preColumn && null != preColumn.getValue()
                                && null != afterColumn && null != afterColumn.getValue()
                                && preColumn.getValue().equals(afterColumn.getValue())) {
                                break;
                            }
                            esDocumentJson.put("key", "startDate");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", transToFormatTime(preColumn.getValue()));
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", transToFormatTime(afterColumn.getValue()));
                            break;
                        case END_DATE:
                            if (null != preColumn && null != preColumn.getValue()
                                && null != afterColumn && null != afterColumn.getValue()
                                && preColumn.getValue().equals(afterColumn.getValue())) {
                                break;
                            }
                            esDocumentJson.put("key", "endDate");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", transToFormatTime(preColumn.getValue()));
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", transToFormatTime(afterColumn.getValue()));
                            break;
                        case CONTRACT_DURATION:
                            esDocumentJson.put("key", "contractDuration");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case POSTING_TIME:
                            esDocumentJson.put("key", "postingTime");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", transToFormatTime(preColumn.getValue()));
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", transToFormatTime(afterColumn.getValue()));
                            break;
                        case JD_URL:
                            esDocumentJson.put("key", "jdUrl");
                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                            break;
                        case MAX_SUBMISSIONS:
                            esDocumentJson.put("key", "maxSubmissions");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case RECRUITMENT_PROGRESS_ID:
                            esDocumentJson.put("key", "recruitmentProgressId");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case ENUM_PRIORITY_ID:
                            esDocumentJson.put("key", "priorityId");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case FLEXIBLE_LOCATION:
                            esDocumentJson.put("key", "flexibleLocation");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case SALES_LEAD_ID:
                            esDocumentJson.put("key", "salesLeadId");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        default:
                            esDocumentJson.put("key", columnName);
                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                    }
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordIPGJob(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> ipgJobFields = new HashSet<>(new ArrayList<>(List.of(APN_JOB_ID, IPG_JOB_STATUS, IPG_JOB_TYPE, IPG_JOB_DESCRIPTION, IPG_JOB_SUMMARY, IPG_JOB_RESPONSIBILITIES, IPG_JOB_REQUIREMENTS, IPG_JOB_REQUIRED_SKILLS)));
        if (JOB_IPG_RELATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (ipgJobFields.contains(columnName) && afterColumn.getUpdated()) {
                    switch (columnName) {
                        case IPG_JOB_STATUS:
                            esDocumentJson.put("key", "ipgJobStatus");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", JobStatus.fromDbValue(Integer.valueOf(preColumn.getValue())).getName());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", JobStatus.fromDbValue(Integer.valueOf(afterColumn.getValue())).getName());
                            break;
                        case IPG_JOB_TYPE:
                            esDocumentJson.put("key", "ipgJobType");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", JobType.fromDbValue(Integer.valueOf(preColumn.getValue())).getName());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", JobType.fromDbValue(Integer.valueOf(afterColumn.getValue())).getName());
                            break;
//                        case IPG_JOB_DESCRIPTION:
//                            esDocumentJson.put("key", "ipgJobDescription");
//                            esInfo(esDocumentJson, preColumn, afterColumn, update);
//                            break;
                        case IPG_JOB_SUMMARY:
                            esDocumentJson.put("key", "ipgJobSummary");
                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                            break;
                        case IPG_JOB_RESPONSIBILITIES:
                            esDocumentJson.put("key", "ipgJobResponsibilities");
                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                            break;
                        case IPG_JOB_REQUIREMENTS:
                            esDocumentJson.put("key", "ipgJobRequirements");
                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                            break;
//                        case IPG_JOB_REQUIRED_SKILLS:
//                            esDocumentJson.put("key", "ipgJobRequiredSkills");
//                            esInfo(esDocumentJson, preColumn, afterColumn, update);
//                            break;
                        default:
                            break;
                    }
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordJobLocation(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> jobLocationFields = new HashSet<>(new ArrayList<>(List.of(JOB_ORIGINAL_LOC)));
        if (JOB_LOCATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (jobLocationFields.contains(columnName) && afterColumn.getUpdated()) {
                    switch (columnName) {
//                        case JOB_OFFICIAL_COUNTRY:
//                            esDocumentJson.put("key", "officialCountry");
//                            if(update) esDocumentJson.put("changedFrom", preColumn.getValue());
//                            else esDocumentJson.put("changedFrom", "");
//                            esDocumentJson.put("changedTo",afterColumn.getValue());
//                            break;
//                        case JOB_OFFICIAL_PROVINCE:
//                            esDocumentJson.put("key", "officialProvince");
//                            if(update) esDocumentJson.put("changedFrom", preColumn.getValue());
//                            else esDocumentJson.put("changedFrom", "");
//                            esDocumentJson.put("changedTo",afterColumn.getValue());
//                            break;
//                        case JOB_OFFICIAL_CITY:
//                            esDocumentJson.put("key", "officialCity");
//                            if(update) esDocumentJson.put("changedFrom", preColumn.getValue());
//                            else esDocumentJson.put("changedFrom", "");
//                            esDocumentJson.put("changedTo",afterColumn.getValue());
//                            break;
//                        case JOB_OFFICIAL_COUNTY:
//                            esDocumentJson.put("key", "officialCounty");
//                            if(update) esDocumentJson.put("changedFrom", preColumn.getValue());
//                            else esDocumentJson.put("changedFrom", "");
//                            esDocumentJson.put("changedTo",afterColumn.getValue());
//                            break;
                        case JOB_ORIGINAL_LOC:
                            esDocumentJson.put("key", "originalLocation");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        default:
                            esDocumentJson.put("key", columnName);
                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                    }
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordJobCompanyContact(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> jobCompanyContactFields = new HashSet<>(new ArrayList<>(List.of(JOB_CONTACT_CATEGORY, JOB_CLIENT_CONTACT_TALENT_ID)));
        if (JOB_COMPANY_CONTACT_RELATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (jobCompanyContactFields.contains(columnName) && afterColumn.getUpdated()) {
                    switch (columnName) {
                        case JOB_CONTACT_CATEGORY:
                            esDocumentJson.put("key", "contactCategory");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", ContactCategoryType.fromDbValue(Integer.valueOf(preColumn.getValue())).name());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", ContactCategoryType.fromDbValue(Integer.valueOf(afterColumn.getValue())).name());
                            break;
//                        case JOB_CLIENT_CONTACT_ID:
//                            esDocumentJson.put("key", "clientContactId");
//                            if(update) {
//                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
//                                esDocumentJson.put("changedFrom", preColumn.getValue());
//                            }
//                            else {
//                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
//                                esDocumentJson.put("changedFrom", "");
//                            }
//                            esDocumentJson.put("changedTo",afterColumn.getValue());
//                            break;
                        case JOB_CLIENT_CONTACT_TALENT_ID:
                            esDocumentJson.put("key", "clientContactTalentId");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        default:
                            esDocumentJson.put("key", columnName);
                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                    }
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordJobAssignedUser(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> jobCompanyContactFields = new HashSet<>(new ArrayList<>(List.of(USER_ID, STATUS)));
        if (USER_JOB_RELATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            String userId = null;
            boolean statusChanged = false;
            boolean status = false;
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (jobCompanyContactFields.contains(columnName)) {
                    if (columnName.equals(USER_ID)) {
                        userId = afterColumn.getValue();
                        if (afterColumn.getUpdated()) { //userId is updated, either insert or update, construct esDoc and return
                            esDocumentJson.put("key", JOB_ASSIGNED_USER_USER_ID);
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }

                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            esDocuments.add(esDocumentJson);
                            return;
                        }

                    } else if (columnName.equals(STATUS) && afterColumn.getUpdated()) {
                        statusChanged = true;
                        status = BooleanUtils.toBoolean(afterColumn.getValue());
                    }
                }
            }

            if (statusChanged) {
                JSONObject esDocumentJson = new JSONObject();
                esDocumentJson.put("key", JOB_ASSIGNED_USER_USER_ID);
                if (status) { // change status from INACTIVE to ACTIVE
                    esDocumentJson.put("changedFrom", "");
                    esDocumentJson.put("changedTo", userId);
                    esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                } else { // delete
                    esDocumentJson.put("changedFrom", userId);
                    esDocumentJson.put("changedTo", "");
                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                }
                log.info("[APN: EsFillerJobRecordService] record job Assigned Users delete or reactive: {}", JSON.toJSON(esDocumentJson));
                esDocuments.add(esDocumentJson);
            }
        }
    }

    private void recordJobSharingToAgency(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (AGENCY_ACTIVITY.equals(tableName)) {
            log.info("[log agency activity] after column 0: {}", afterColumns.isEmpty() ? null : afterColumns.get(0).getValue());
            checkColumnMatch(preColumns, afterColumns, update);

            boolean jobRelatedAgencyActivity = false;
            String details = "";

            for (int i = 0; i < afterColumns.size(); i++) {
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();

                if (AGENCY_ACTIVITY_TYPE.equals(columnName) && afterColumn.getUpdated()) {
                    jobRelatedAgencyActivity = true;
                    continue;
                }
                if (AGENCY_ACTIVITY_DETAILS.equals(columnName) && afterColumn.getUpdated()) {
                    details = afterColumn.getValue();
                }
            }

            if (jobRelatedAgencyActivity && StringUtils.isNotEmpty(details) && JSON.isValid(details)) {

                try {
                    JSONObject detailsObj = JSONUtil.parseObj(details);
                    String operation = detailsObj.getStr("operation");
                    Long agencyId = detailsObj.getLong("agencyId");
                    String agencyName = detailsObj.getStr("agencyName");
                    if ("START_SHARE".equals(operation)) {
                        JSONObject esDocumentJson = new JSONObject();
                        esDocumentJson.put("key", "Agency:StartShare");
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", agencyId + ":" + agencyName);
                        esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocuments.add(esDocumentJson);
                    } else if ("STOP_SHARE".equals(operation)) {
                        JSONObject esDocumentJson = new JSONObject();
                        esDocumentJson.put("key", "Agency:StopShare");
                        esDocumentJson.put("changedFrom", "");
                        esDocumentJson.put("changedTo", agencyId + ":" + agencyName);
                        esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocuments.add(esDocumentJson);

                        if (detailsObj.containsKey("inProgressApplications")) {
                            JSONArray inProgressApplications = detailsObj.getJSONArray("inProgressApplications");
                            for (int j = 0; j < inProgressApplications.size(); j++) {
                                JSONObject application = inProgressApplications.getJSONObject(j);
                                Long talentId = application.getLong("talentId");
                                String talentName = application.getStr("talentName");

                                JSONObject applicationObj = new JSONObject();
                                applicationObj.put("key", "Agency:StopShare:InProgressApplication");
                                applicationObj.put("changedFrom", "");
                                applicationObj.put("changedTo", talentId + ":" + talentName);
                                applicationObj.put("eventType", EVENT_TYPE_INSERT);

                                esDocuments.add(applicationObj);
                            }
                        }
                    }

                    log.info("[log agency activity] esdoc: {}", esDocuments);
                } catch (Exception e) {
                    log.error("parse json exception for agency share job details: {}", details, e);
                }
            }
        }
    }

    private void recordJobPreferredDegree(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (JOB_PREFERRED_DEGREE_RELATION.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (columnName.equals(JOB_PREFERRED_DEGREE_ID) && afterColumn.getUpdated()) {
                    esDocumentJson.put("key", "preferredDegreeId");
                    if (update) {
                        esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                        esDocumentJson.put("changedFrom", preColumn.getValue());
                    } else {
                        esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                        esDocumentJson.put("changedFrom", "");
                    }
                    esDocumentJson.put("changedTo", afterColumn.getValue());
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void recordJobPreferredLanguage(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (JOB_PREFERRED_LANGUAGE_RELATION.equals(tableName)) {
            /**
             * **important: For Job saving, we use JPA cascading saving so that it will lead to set column job_id to null at first then delete the whole job preferred language record in that row.
             That's why here we need record the DELETE event by the UPDATE event when the job_id is set null.
             */
            checkColumnMatch(preColumns, afterColumns, update);
            JSONObject esDocumentJson = new JSONObject();
            boolean delete = false;
            String preValue = ""; // previous job preferred language value
            String posValue = ""; // post job preferred language value
            for (int i = 0; i < afterColumns.size(); i++) {
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (columnName.equals(JOB_ID) && afterColumn.getUpdated()) {
                    if (preColumn != null && !preColumn.getValue().isEmpty() && (!afterColumn.hasValue())) {
                        delete = true;
                    }
                }
                if (columnName.equals(JOB_PREFERRED_LANGUAGE_ID)) {
                    preValue = preColumn != null && preColumn.hasValue() ? preColumn.getValue() : "";
                    posValue = afterColumn.hasValue() ? afterColumn.getValue() : "";
                }
            }
            if (delete) {
                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                esDocumentJson.put("changedFrom", preValue);
                esDocumentJson.put("changedTo", "");
            } else if (!preValue.equals(posValue)) {
                if (preValue.equals("")) esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                else esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                esDocumentJson.put("changedFrom", preValue);
                esDocumentJson.put("changedTo", posValue);
            }
            if (!esDocumentJson.isEmpty()) {
                esDocumentJson.put("key", "preferredLanguageId");
                esDocuments.add(esDocumentJson);
            }
        }
    }

    private void recordJobRequiredLanguage(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (JOB_REQUIRED_LANGUAGE_RELATION.equals(tableName)) {
            /**
             * **important: For Job saving, we use JPA cascading saving so that it will lead to set column job_id to null at first then delete the whole job required language record in that row.
             That's why here we need record the DELETE event by the UPDATE event when the job_id is set null.
             */
            checkColumnMatch(preColumns, afterColumns, update);
            JSONObject esDocumentJson = new JSONObject();
            boolean delete = false;
            String preValue = ""; // previous job required language value
            String posValue = ""; // post job required language value
            for (int i = 0; i < afterColumns.size(); i++) {
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (columnName.equals(JOB_ID) && afterColumn.getUpdated()) {
                    if (preColumn != null && !preColumn.getValue().isEmpty() && (!afterColumn.hasValue())) {
                        delete = true;
                    }
                }
                if (columnName.equals(JOB_REQUIRED_LANGUAGE_ID)) {
                    preValue = preColumn != null && preColumn.hasValue() ? preColumn.getValue() : "";
                    posValue = afterColumn.hasValue() ? afterColumn.getValue() : "";
                }
            }
            if (delete) {
                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                esDocumentJson.put("changedFrom", preValue);
                esDocumentJson.put("changedTo", "");
            } else if (!preValue.equals(posValue)) {
                if (preValue.equals("")) esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                else esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                esDocumentJson.put("changedFrom", preValue);
                esDocumentJson.put("changedTo", posValue);
            }
            if (!esDocumentJson.isEmpty()) {
                esDocumentJson.put("key", "requiredLanguageId");
                esDocuments.add(esDocumentJson);
            }
        }
    }

    private void recordJobFunction(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        if (JOB_JOB_FUNCTION_RELATION.equals(tableName)) {
            /**
             * **important: For Job saving, we use JPA cascading saving so that it will lead to set column job_id to null at first then delete the whole job required language record in that row.
             That's why here we need record the DELETE event by the UPDATE event when the job_id is set null.
             */
            checkColumnMatch(preColumns, afterColumns, update);
            JSONObject esDocumentJson = new JSONObject();
            boolean delete = false;
            String preValue = ""; // previous job required language value
            String posValue = ""; // post job required language value
            for (int i = 0; i < afterColumns.size(); i++) {
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (columnName.equals(JOB_ID) && afterColumn.getUpdated()) {
                    if (preColumn != null && !preColumn.getValue().isEmpty() && (!afterColumn.hasValue())) {
                        delete = true;
                    }
                }
                if (columnName.equals(JOB_FUNCTION_ID)) {
                    preValue = preColumn != null && preColumn.hasValue() ? preColumn.getValue() : "";
                    posValue = afterColumn.hasValue() ? afterColumn.getValue() : "";
                }
            }
            if (delete) {
                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                esDocumentJson.put("changedFrom", preValue);
                esDocumentJson.put("changedTo", "");
            } else if (!preValue.equals(posValue)) {
                if (preValue.equals("")) esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                else esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                esDocumentJson.put("changedFrom", preValue);
                esDocumentJson.put("changedTo", posValue);
            }
            if (!esDocumentJson.isEmpty()) {
                esDocumentJson.put("key", "jobFunctionId");
                esDocuments.add(esDocumentJson);
            }
        }
    }

    private void recordJobNote(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> jobNoteFields = new HashSet<>(new ArrayList<>(List.of(JOB_NOTE_TITLE, JOB_NOTE_NOTE, JOB_NOTE_PRIORITY)));
        if (JOB_NOTE.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (jobNoteFields.contains(columnName) && afterColumn.getUpdated()) {
                    switch (columnName) {
                        case JOB_NOTE_TITLE:
                            esDocumentJson.put("key", "noteTitle");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case JOB_NOTE_NOTE:
                            esDocumentJson.put("key", "note");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        case JOB_NOTE_PRIORITY:
                            esDocumentJson.put("key", "notePriority");
                            if (update) {
                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                                esDocumentJson.put("changedFrom", preColumn.getValue());
                            } else {
                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
                                esDocumentJson.put("changedFrom", "");
                            }
                            esDocumentJson.put("changedTo", afterColumn.getValue());
                            break;
                        default:
                            esDocumentJson.put("key", columnName);
                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                    }
                    esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private String jsonRangeTransfer(JSONObject json) {

        Long lte = json.getLong("lte", null);
        Long gte = json.getLong("gte", null);
        StringBuilder result = new StringBuilder();

        if (gte != null) {
            result.append(gte);
        }
        result.append(" - ");
        if (lte != null) {
            result.append(lte);
        }

        if (gte == null && lte == null) {
            return "";
        }
        return result.toString();
    }

    private void jsonComparor(JSONObject prev, JSONObject post, String column, List<JSONObject> esDocuments) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("key", column);
        if (prev == null && post != null) {
            jsonObject.put("eventType", EVENT_TYPE_INSERT);
            jsonObject.put("changedFrom", "");
            jsonObject.put("changedTo", jsonRangeTransfer(post));
            esDocuments.add(jsonObject);
        } else if (prev != null && post != null) {
            if (!Objects.equals(prev.get("gte", String.class), post.get("gte", String.class)) || !Objects.equals(prev.get("lte", String.class), post.get("lte", String.class))) {
                jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                jsonObject.put("changedFrom", jsonRangeTransfer(prev));
                jsonObject.put("changedTo", jsonRangeTransfer(post));
                esDocuments.add(jsonObject);
            }
        } else if (prev != null && post == null) {
            jsonObject.put("eventType", EVENT_TYPE_UPDATE);
            jsonObject.put("changedFrom", jsonRangeTransfer(prev));
            jsonObject.put("changedTo", "");
            esDocuments.add(jsonObject);
        }
    }

    private String jsonSkillsTransfer(List<JSONObject> jsons) {
        List<String> skills = new ArrayList<>();
        for (JSONObject json : jsons) {
            skills.add(json.get("skillName", String.class));
        }
        return String.join(",", skills);
    }

    private void jsonArrayComparor(List<JSONObject> prev, List<JSONObject> post, String column, List<JSONObject> esDocuments) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("key", column);
        if (prev == null && post != null) {
            jsonObject.put("eventType", EVENT_TYPE_INSERT);
            jsonObject.put("changedFrom", "");
            jsonObject.put("changedTo", jsonSkillsTransfer(post));
            esDocuments.add(jsonObject);
        } else if (prev != null && post != null) {
            if (prev.size() != post.size()) {
                jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                jsonObject.put("changedFrom", jsonSkillsTransfer(prev));
                jsonObject.put("changedTo", jsonSkillsTransfer(post));
                esDocuments.add(jsonObject);
            } else {
                for (int i = 0; i < prev.size(); i++) {
                    if (!prev.get(i).get(JOB_SKILL_NAME, String.class).equals(post.get(i).get(JOB_SKILL_NAME, String.class))) {
                        jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                        jsonObject.put("changedFrom", jsonSkillsTransfer(prev));
                        jsonObject.put("changedTo", jsonSkillsTransfer(post));
                        esDocuments.add(jsonObject);
                        break;
                    }
                }
            }
        } else if (prev != null && post == null) {
            jsonObject.put("eventType", EVENT_TYPE_UPDATE);
            jsonObject.put("changedFrom", prev.toString());
            jsonObject.put("changedTo", "");
            esDocuments.add(jsonObject);
        }
    }

    private void recordJobAdditionInfo(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, List<JSONObject> esDocuments) {
        Set<String> jobAdditionalInfoFields = new HashSet<>(new ArrayList<>(List.of(JOB_EXTEND_INFO, JOB_LOCAL_EXTEND_INFO, JOB_RESPONSIBILITIES, JOB_REQUIREMENTS, JOB_SUMMARY)));
//        Set<String> jobAdditionalInfoExceptExtendInfoFields = new HashSet<>(new ArrayList<>(List.of(JOB_RESPONSIBILITIES, JOB_REQUIREMENTS, JOB_SUMMARY)));
        if (JOB_ADDITIONAL_INFO.equals(tableName)) {
            checkColumnMatch(preColumns, afterColumns, update);
            for (int i = 0; i < afterColumns.size(); i++) {
                JSONObject esDocumentJson = new JSONObject();
                Column preColumn = update ? preColumns.get(i) : null;
                Column afterColumn = afterColumns.get(i);
                String columnName = afterColumn.getName();
                if (jobAdditionalInfoFields.contains(columnName) && afterColumn.getUpdated()) {
                    switch (columnName) {
                        case JOB_EXTEND_INFO:
                            JSONObject prevJson = preColumn == null ? null : JSONUtil.parseObj(preColumn.getValue());
                            JSONObject postJson = JSONUtil.parseObj(afterColumn.getValue());
                            HashSet<String> keySet = new HashSet<>();
                            if (postJson != null && CollUtil.isNotEmpty(postJson.keySet())) {
                                keySet.addAll(postJson.keySet());
                            }
                            if (prevJson != null && CollUtil.isNotEmpty(prevJson.keySet())) {
                                keySet.addAll(prevJson.keySet());
                            }
                            for (String key : keySet) {
                                if (key.equals(JOB_EXPERIENCE_YEAR_RANGE)) {
                                    JSONObject prevExperience = prevJson == null ? null : prevJson.getJSONObject(JOB_EXPERIENCE_YEAR_RANGE);
                                    JSONObject postExperience = postJson.getJSONObject(JOB_EXPERIENCE_YEAR_RANGE);
                                    jsonComparor(prevExperience, postExperience, JOB_EXPERIENCE_YEAR_RANGE, esDocuments);
                                }
                                if (key.equals(JOB_BILL_RANGE)) {
                                    JSONObject prevBill = prevJson == null ? null : prevJson.getJSONObject(JOB_BILL_RANGE);
                                    JSONObject postBill = postJson.getJSONObject(JOB_BILL_RANGE);
                                    jsonComparor(prevBill, postBill, JOB_BILL_RANGE, esDocuments);
                                }
                                if (key.equals(JOB_SALARY_RANGE)) {
                                    JSONObject prevSalary = prevJson == null ? null : prevJson.getJSONObject(JOB_SALARY_RANGE);
                                    JSONObject postSalary = postJson.getJSONObject(JOB_SALARY_RANGE);
                                    jsonComparor(prevSalary, postSalary, JOB_SALARY_RANGE, esDocuments);
                                }
                                if (key.equals(JOB_PREFERRED_SKILLS)) {
                                    JSONArray prevPreferredSkills = prevJson == null ? new JSONArray() : prevJson.getJSONArray(JOB_PREFERRED_SKILLS);
                                    if (Objects.isNull(prevPreferredSkills)) {
                                        prevPreferredSkills = new JSONArray();
                                    }
                                    JSONArray postPreferredSkills = postJson.getJSONArray(JOB_PREFERRED_SKILLS);
                                    if (Objects.isNull(postPreferredSkills)) {
                                        postPreferredSkills = new JSONArray();
                                    }
                                    jsonArrayComparor(prevPreferredSkills.toList(JSONObject.class), postPreferredSkills.toList(JSONObject.class), JOB_PREFERRED_SKILLS, esDocuments);
                                }
                                if (key.equals(JOB_REQUIRED_SKILLS)) {
                                    JSONArray prevRequiredSkills = prevJson == null ? new JSONArray() : prevJson.getJSONArray(JOB_REQUIRED_SKILLS);
                                    if (Objects.isNull(prevRequiredSkills)) {
                                        prevRequiredSkills = new JSONArray();
                                    }
                                    JSONArray postRequiredSkills = postJson.getJSONArray(JOB_REQUIRED_SKILLS);
                                    if (Objects.isNull(postRequiredSkills)) {
                                        postRequiredSkills = new JSONArray();
                                    }
                                    jsonArrayComparor(prevRequiredSkills.toList(JSONObject.class), postRequiredSkills.toList(JSONObject.class), JOB_REQUIRED_SKILLS, esDocuments);
                                }
                                if (key.equals(JOB_PAY_TYPE)) {
                                    JSONObject jsonObject = new JSONObject();
                                    String prev = prevJson == null ? StrUtil.EMPTY : prevJson.get(JOB_PAY_TYPE, String.class, true);
                                    String post = postJson.get(JOB_PAY_TYPE, String.class, true);
                                    if (!StringUtils.equals(prev, post)) {
                                        jsonObject.put("key", JOB_PAY_TYPE);
                                        if (update) jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                                        else jsonObject.put("eventType", EVENT_TYPE_INSERT);
                                        jsonObject.put("changedFrom", prev);
                                        jsonObject.put("changedTo", post);
                                        esDocuments.add(jsonObject);
                                    }
                                }
                                if (key.equals(JOB_DEPARTMENT)) {
                                    JSONObject jsonObject = new JSONObject();
                                    String prev = prevJson == null ? StrUtil.EMPTY : prevJson.get(JOB_DEPARTMENT, String.class, true);
                                    String post = postJson.get(JOB_DEPARTMENT, String.class, true);
                                    if (!StringUtils.equals(prev, post)) {
                                        jsonObject.put("key", JOB_DEPARTMENT);
                                        jsonObject.put("eventType", EVENT_TYPE_UPDATE);
                                        jsonObject.put("changedFrom", prev);
                                        jsonObject.put("changedTo", post);
                                        esDocuments.add(jsonObject);
                                    }
                                }

                                if (key.equals(JOB_SUMMARY)) {
                                    handleAdditionalKey(JOB_SUMMARY, prevJson, postJson, esDocuments, update);
                                }

                                if (key.equals(JOB_RESPONSIBILITIES)) {
                                    handleAdditionalKey(JOB_RESPONSIBILITIES, prevJson, postJson, esDocuments, update);
                                }

                                if (key.equals(JOB_REQUIREMENTS)) {
                                    handleAdditionalKey(JOB_REQUIREMENTS, prevJson, postJson, esDocuments, update);
                                }

//                                if (key.equals(JOB_REASON_FOR_RECRUITMENT)) {
//                                    handleAdditionalKey(JOB_REASON_FOR_RECRUITMENT, prevJson, postJson, esDocuments);
//                                }
//                                if (key.equals(JOB_TEAM_COMPOSITION)) {
//                                    handleAdditionalKey(JOB_TEAM_COMPOSITION, prevJson, postJson, esDocuments);
//                                }
//                                if (key.equals(JOB_PREFERRED_COMPANIES)) {
//                                    handleAdditionalKey(JOB_PREFERRED_COMPANIES, prevJson, postJson, esDocuments);
//                                }
//                                if (key.equals(JOB_PREFERRED_INDUSTRY)) {
//                                    handleAdditionalKey(JOB_PREFERRED_INDUSTRY, prevJson, postJson, esDocuments);
//                                }
//                                if (key.equals(JOB_SUGGESTIONS_FOR_PROSPECTING)) {
//                                    handleAdditionalKey(JOB_SUGGESTIONS_FOR_PROSPECTING, prevJson, postJson, esDocuments);
//                                }
//                                if (key.equals(JOB_RECOMMEND_APPROACH)) {
//                                    handleAdditionalKey(JOB_RECOMMEND_APPROACH, prevJson, postJson, esDocuments);
//                                }
//                                if (key.equals(JOB_ESTIMATED_JOB_FEE)) {
//                                    FeeDTO prev = prevJson == null ? null : prevJson.get(key, FeeDTO.class, true);
//                                    FeeDTO post = postJson.get(key, FeeDTO.class, true);
//                                    handleFeeDTOToEsDocuments(prev, post, key, esDocuments);
//                                }
//                                if (key.equals(JOB_FEE_STRUCTURE)) {
//                                    handleAdditionalKey(JOB_FEE_STRUCTURE, prevJson, postJson, esDocuments);
//                                }
//                                if (key.equals(JOB_CONTACT_SIGNING_PARTY)) {
//                                    handleAdditionalKey(JOB_CONTACT_SIGNING_PARTY, prevJson, postJson, esDocuments);
//                                }
                            }
                            break;
                        case JOB_LOCAL_EXTEND_INFO:
                            prevJson = preColumn == null ? null : JSONUtil.parseObj(preColumn.getValue());
                            postJson = JSONUtil.parseObj(afterColumn.getValue());
                            keySet = new HashSet<>();
                            if (postJson != null && CollUtil.isNotEmpty(postJson.keySet())) {
                                keySet.addAll(postJson.keySet());
                            }
                            if (prevJson != null && CollUtil.isNotEmpty(prevJson.keySet())) {
                                keySet.addAll(prevJson.keySet());
                            }
                            for (String key : keySet) {
                                if (key.equals(JOB_REASON_FOR_RECRUITMENT)) {
                                    handleAdditionalKey(JOB_REASON_FOR_RECRUITMENT, prevJson, postJson, esDocuments);
                                }
                                if (key.equals(JOB_TEAM_COMPOSITION)) {
                                    handleAdditionalKey(JOB_TEAM_COMPOSITION, prevJson, postJson, esDocuments);
                                }
                                if (key.equals(JOB_PREFERRED_COMPANIES)) {
                                    handleAdditionalKey(JOB_PREFERRED_COMPANIES, prevJson, postJson, esDocuments);
                                }
                                if (key.equals(JOB_PREFERRED_INDUSTRY)) {
                                    handleAdditionalKey(JOB_PREFERRED_INDUSTRY, prevJson, postJson, esDocuments);
                                }
                                if (key.equals(JOB_SUGGESTIONS_FOR_PROSPECTING)) {
                                    handleAdditionalKey(JOB_SUGGESTIONS_FOR_PROSPECTING, prevJson, postJson, esDocuments);
                                }
                                if (key.equals(JOB_RECOMMEND_APPROACH)) {
                                    handleAdditionalKey(JOB_RECOMMEND_APPROACH, prevJson, postJson, esDocuments);
                                }
                                if (key.equals(JOB_ESTIMATED_JOB_FEE)) {
                                    FeeDTO prev = prevJson == null ? null : prevJson.get(key, FeeDTO.class, true);
                                    FeeDTO post = postJson.get(key, FeeDTO.class, true);
                                    handleFeeDTOToEsDocuments(prev, post, key, esDocuments);
                                }
                                if (key.equals(JOB_FEE_STRUCTURE)) {
                                    handleAdditionalKey(JOB_FEE_STRUCTURE, prevJson, postJson, esDocuments);
                                }
                                if (key.equals(JOB_CONTACT_SIGNING_PARTY)) {
                                    handleAdditionalKey(JOB_CONTACT_SIGNING_PARTY, prevJson, postJson, esDocuments);
                                }
                            }
                            break;
//                        case JOB_SUMMARY:
//                            esDocumentJson.put("key", "summary");
//                            if (update) {
//                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
//                                esDocumentJson.put("changedFrom", preColumn.getValue());
//                            } else {
//                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
//                                esDocumentJson.put("changedFrom", "");
//                            }
//                            esDocumentJson.put("changedTo", afterColumn.getValue());
//                            break;
//                        case JOB_RESPONSIBILITIES:
//                            esDocumentJson.put("key", "responsibilities");
//                            if (update) {
//                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
//                                esDocumentJson.put("changedFrom", preColumn.getValue());
//                            } else {
//                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
//                                esDocumentJson.put("changedFrom", "");
//                            }
//                            esDocumentJson.put("changedTo", afterColumn.getValue());
//                            break;
//                        case JOB_REQUIREMENTS:
//                            esDocumentJson.put("key", "requirements");
//                            if (update) {
//                                esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
//                                esDocumentJson.put("changedFrom", preColumn.getValue());
//                            } else {
//                                esDocumentJson.put("eventType", EVENT_TYPE_INSERT);
//                                esDocumentJson.put("changedFrom", "");
//                            }
//                            esDocumentJson.put("changedTo", afterColumn.getValue());
//                            break;
                        default:
                            esDocumentJson.put("key", columnName);
                            esInfo(esDocumentJson, preColumn, afterColumn, update);
                    }

//                    if (jobAdditionalInfoExceptExtendInfoFields.contains(columnName) && afterColumn.getUpdated())
//                        esDocuments.add(esDocumentJson);
                }
            }
        }
    }

    private void handleFeeDTOToEsDocuments(FeeDTO prev, FeeDTO post, String key, List<JSONObject> esDocuments) {
        String prevFee = prev == null ? StrUtil.EMPTY : ObjectUtil.isNull(prev.getFee()) ? StrUtil.EMPTY : String.valueOf(prev.getFee());
        String postFee = post == null ? StrUtil.EMPTY : ObjectUtil.isNull(post.getFee()) ? StrUtil.EMPTY : String.valueOf(post.getFee());
        String prevCurrency = prev == null ? StrUtil.EMPTY : ObjectUtil.isNull(prev.getCurrency()) ? StrUtil.EMPTY : String.valueOf(prev.getCurrency());
        String postCurrency = post == null ? StrUtil.EMPTY : ObjectUtil.isNull(post.getCurrency()) ? StrUtil.EMPTY : String.valueOf(post.getCurrency());
        if (!StringUtils.equals(prevFee, postFee)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("key", key + "Fee");
            jsonObject.put("eventType", EVENT_TYPE_UPDATE);
            jsonObject.put("changedFrom", prevFee);
            jsonObject.put("changedTo", postFee);
            esDocuments.add(jsonObject);
        }
        if (!StringUtils.equals(prevCurrency, postCurrency)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("key", key + "Currency");
            jsonObject.put("eventType", EVENT_TYPE_UPDATE);
            jsonObject.put("changedFrom", prevCurrency);
            jsonObject.put("changedTo", postCurrency);
            esDocuments.add(jsonObject);
        }
    }

    @Override
    public List<JSONObject> recordUpdateJobRows(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, Long jobId, String createdTime, String createdBy, List<JSONObject> esDocuments) {
        recordJob(preColumns, afterColumns, tableName, update, esDocuments);
        recordIPGJob(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobFunction(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobLocation(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobCompanyContact(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobPreferredDegree(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobPreferredLanguage(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobRequiredLanguage(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobNote(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobAdditionInfo(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobAssignedUser(preColumns, afterColumns, tableName, update, esDocuments);
        recordJobSharingToAgency(preColumns, afterColumns, tableName, update, esDocuments);

        return esDocuments;
    }


    @Override
    public Long recordJobId(List<Column> preColumns, List<Column> afterColumns, String tableName, Map<Long, Long> jobAdditional2JobidMap) {
        // TODO: 2023/12/15  Later, consider changing the APN_JOB_ID field to the JOB_ID field.
        Set<String> jobTables = new HashSet<>(new ArrayList<>(List.of(JOB_JOB_FUNCTION_RELATION, JOB_LOCATION, JOB_COMPANY_CONTACT_RELATION, JOB_PREFERRED_DEGREE_RELATION, JOB_PREFERRED_LANGUAGE_RELATION, JOB_REQUIRED_LANGUAGE_RELATION, USER_JOB_RELATION)));
        Set<String> deletedJobTable = new HashSet<>(new ArrayList<>(List.of(JOB_LOCATION, JOB_PREFERRED_DEGREE_RELATION, JOB_PREFERRED_LANGUAGE_RELATION, JOB_REQUIRED_LANGUAGE_RELATION, USER_JOB_RELATION, JOB_JOB_FUNCTION_RELATION)));

        if (tableName.equals(JOB)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(ID) && !column.getValue().equals("")) {
                    return Long.valueOf(column.getValue());
                }
            }
        }
        if (tableName.equals(JOB_IPG_RELATION)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(APN_JOB_ID) && !column.getValue().equals("")) {
                    return Long.valueOf(column.getValue());
                }
            }
        }
        if (jobTables.contains(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(JOB_ID) && !column.getValue().equals("")) {
                    return Long.valueOf(column.getValue());
                }
            }
        }
        if (deletedJobTable.contains(tableName)) {
            for (Column column : preColumns) {
                String columnName = column.getName();
                if (columnName.equals(JOB_ID) && !column.getValue().equals("")) {
                    return Long.valueOf(column.getValue());
                }
            }
        }
        if (JOB_ADDITIONAL_INFO.equals(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(ID) && !column.getValue().equals("")) {
                    return jobAdditional2JobidMap.get(Long.parseLong(column.getValue()));
                }
            }
        }
        if (AGENCY_ACTIVITY.equals(tableName)) {
//            for (Column column : afterColumns) {
//                String columnName = column.getName();
//                if (columnName.equals(ID) && !column.getValue().equals("")) {
//                    return jobAdditional2JobidMap.get(Long.parseLong(column.getValue()));
//                }
//            }

            boolean jobRelatedAgencyActivity = false;
            String details = "";

            for (Column afterColumn : afterColumns) {
                String columnName = afterColumn.getName();

                if (AGENCY_ACTIVITY_TYPE.equals(columnName) && afterColumn.getUpdated()) {
                    jobRelatedAgencyActivity = true;
                    continue;
                }
                if (AGENCY_ACTIVITY_DETAILS.equals(columnName) && afterColumn.getUpdated()) {
                    details = afterColumn.getValue();
                }
            }

            if (jobRelatedAgencyActivity && StringUtils.isNotEmpty(details) && JSON.isValid(details)) {
                try {
                    JSONObject detailsObj = JSONUtil.parseObj(details);
                    return detailsObj.getLong("jobId");
                } catch (Exception e) {
                    log.error("[parse job id exception]");
                }
            }
        }
        return null;
    }

    @Override
    public String recordCreatedTime(List<Column> preColumns, List<Column> afterColumns, String tableName, String createdTime) {
        //Set<String> jobTables = new HashSet<>(new ArrayList<>(List.of(JOB, JOB_COMPANY_CONTACT_RELATION)));
        Set<String> jobTables = new HashSet<>(new ArrayList<>(List.of(JOB, AGENCY_ACTIVITY)));
        if (Objects.equals(createdTime, "") && jobTables.contains(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(LAST_MODIFIED_DATE)) {
                    createdTime = formatTime(column.getValue()).toString();
                }
            }
        }
        return createdTime;
    }

    @Override
    public String recordCreatedBy(List<Column> preColumns, List<Column> afterColumns, String tableName, String createdBy) {
        Set<String> jobTables = new HashSet<>(new ArrayList<>(List.of(
                JOB,
                JOB_COMPANY_CONTACT_RELATION,
                JOB_ADDITIONAL_INFO,
                JOB_LOCATION,
                JOB_IPG_RELATION,
                AGENCY_ACTIVITY
        )));
        if (Objects.equals(createdBy, "") && jobTables.contains(tableName)) {
            for (Column column : afterColumns) {
                String columnName = column.getName();
                if (columnName.equals(LAST_MODIFIED_BY)) {
                    createdBy = column.getValue();
                }
            }
        }
        return createdBy;
    }

    @Override
    public void recordDeleteJobRows(List<Column> beforeColumnsList, List<Column> afterColumnsList, String tableName, List<JSONObject> esDocuments) {
        if (JOB_LOCATION.equals(tableName)) {
            Set<String> jobLocationFields = new HashSet<>(new ArrayList<>(List.of(JOB_ORIGINAL_LOC)));
            JSONObject esDocumentJson = new JSONObject();
            for (Column prevColumn : beforeColumnsList) {
                String columnName = prevColumn.getName();
                if (jobLocationFields.contains(columnName)) {
                    esDocumentJson.put("key", "originalLocation");
                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
                    esDocumentJson.put("changedFrom", prevColumn.getValue());
                    esDocumentJson.put("changedTo", "");
                    log.info("[APN: EsFillerJobRecordService] record job location delete: {}", JSON.toJSON(esDocumentJson));
                    esDocuments.add(esDocumentJson);
                }
            }
        }
//        if (JOB_JOB_FUNCTION_RELATION.equals(tableName)) {
//            Set<String> jobFunctionFields = new HashSet<>(new ArrayList<>(List.of(JOB_FUNCTION_ID)));
//            JSONObject esDocumentJson = new JSONObject();
//            for (Column prevColumn : beforeColumnsList) {
//                String columnName = prevColumn.getName();
//                if (jobFunctionFields.contains(columnName)) {
//                    esDocumentJson.put("key", convertSnakeCaseToCamelCase(JOB_FUNCTION_ID));
//                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
//                    esDocumentJson.put("changedFrom", prevColumn.getValue());
//                    esDocumentJson.put("changedTo", "");
//                    log.info("[APN: EsFillerJobRecordService] record job job function delete: {}", JSON.toJSON(esDocumentJson));
//                    esDocuments.add(esDocumentJson);
//                }
//            }
//        }
//        if (JOB_PREFERRED_LANGUAGE_RELATION.equals(tableName)) {
//            Set<String> jobPreferredLanguageFields = new HashSet<>(new ArrayList<>(List.of(JOB_PREFERRED_LANGUAGE_ID)));
//            JSONObject esDocumentJson = new JSONObject();
//            for (Column prevColumn : beforeColumnsList) {
//                String columnName = prevColumn.getName();
//                if (jobPreferredLanguageFields.contains(columnName)) {
//                    esDocumentJson.put("key", convertSnakeCaseToCamelCase(JOB_PREFERRED_LANGUAGE_ID));
//                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
//                    esDocumentJson.put("changedFrom", prevColumn.getValue());
//                    esDocumentJson.put("changedTo", "");
//                    log.info("[APN: EsFillerJobRecordService] record job preferred language delete: {}", JSON.toJSON(esDocumentJson));
//                    esDocuments.add(esDocumentJson);
//                }
//            }
//
//        }
//        if (JOB_REQUIRED_LANGUAGE_RELATION.equals(tableName)) {
//            Set<String> jobReauiredLanguageFields = new HashSet<>(new ArrayList<>(List.of(JOB_REQUIRED_LANGUAGE_ID)));
//            JSONObject esDocumentJson = new JSONObject();
//            for (Column prevColumn : beforeColumnsList) {
//                String columnName = prevColumn.getName();
//                if (jobReauiredLanguageFields.contains(columnName)) {
//                    esDocumentJson.put("key", convertSnakeCaseToCamelCase(JOB_REQUIRED_LANGUAGE_ID));
//                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
//                    esDocumentJson.put("changedFrom", prevColumn.getValue());
//                    esDocumentJson.put("changedTo", "");
//                    log.info("[APN: EsFillerJobRecordService] record job preferred language delete: {}", JSON.toJSON(esDocumentJson));
//                    esDocuments.add(esDocumentJson);
//                }
//            }
//        }

//        if (USER_JOB_RELATION.equals(tableName)) {
//            Set<String> jobAssignedUserFields = new HashSet<>(new ArrayList<>(List.of(USER_ID)));
//            JSONObject esDocumentJson = new JSONObject();
//            for (Column prevColumn : beforeColumnsList) {
//                String columnName = prevColumn.getName();
//                if (jobAssignedUserFields.contains(columnName)) {
//                    esDocumentJson.put("key", JOB_ASSIGNED_USER_USER_ID);
//                    esDocumentJson.put("eventType", EVENT_TYPE_UPDATE);
//                    esDocumentJson.put("changedFrom", prevColumn.getValue());
//                    esDocumentJson.put("changedTo", "");
//                    log.info("[APN: EsFillerJobRecordService] record job Assigned Users delete: {}", JSON.toJSON(esDocumentJson));
//                    esDocuments.add(esDocumentJson);
//                }
//            }
//        }

    }

    public static String convertSnakeCaseToCamelCase(String snakeCase) {
        StringBuilder camelCase = new StringBuilder();
        boolean capitalizeNext = false;

        for (char ch : snakeCase.toCharArray()) {
            if (ch == '_') {
                capitalizeNext = true;
            } else if (capitalizeNext) {
                camelCase.append(Character.toUpperCase(ch));
                capitalizeNext = false;
            } else {
                camelCase.append(ch);
            }
        }

        return camelCase.toString();
    }

    public static void handleAdditionalKey(String key, JSONObject prevJson, JSONObject postJson, List<JSONObject> esDocuments) {
        JSONObject jsonObject = new JSONObject();
        String prev = prevJson == null ? StrUtil.EMPTY : prevJson.get(key, String.class, true);
        String post = postJson.get(key, String.class, true);
        if (!StringUtils.equals(prev, post)) {
            jsonObject.put("key", key);
            jsonObject.put("eventType", EVENT_TYPE_UPDATE);
            jsonObject.put("changedFrom", prev);
            jsonObject.put("changedTo", post);
            esDocuments.add(jsonObject);
        }
    }

    public static void handleAdditionalKey(String key, JSONObject prevJson, JSONObject postJson, List<JSONObject> esDocuments, boolean update) {
        JSONObject jsonObject = new JSONObject();
        String prev = prevJson == null ? StrUtil.EMPTY : prevJson.get(key, String.class, true);
        String post = postJson.get(key, String.class, true);
        if (!StringUtils.equals(prev, post)) {
            jsonObject.put("key", key);
            if (update) jsonObject.put("eventType", EVENT_TYPE_UPDATE);
            else jsonObject.put("eventType", EVENT_TYPE_INSERT);
            jsonObject.put("changedFrom", prev);
            jsonObject.put("changedTo", post);
            esDocuments.add(jsonObject);
        }
    }
}
