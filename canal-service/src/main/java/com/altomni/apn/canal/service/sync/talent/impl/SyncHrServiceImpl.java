package com.altomni.apn.canal.service.sync.talent.impl;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.service.login.LoginService;
import com.altomni.apn.canal.service.login.impl.LoginServiceImpl;
import com.altomni.apn.canal.service.sync.talent.SyncHrService;
import com.altomni.apn.canal.web.rest.vm.MqMessageCountVM;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Service
public class SyncHrServiceImpl implements SyncHrService {

    public static final String URI_TALENT_HR_MQ_MESSAGE_COUNT_CHECK = "/talent/api/v3/canal/check-talent-hr-mq-message-count";
    public static final String URI_TALENT_HR_SYNCHRONIZATION = "/talent/api/v3/canal/sync-talents-to-hr-mq";

    private RestTemplate restTemplate = new RestTemplate();

    @Resource
    private CanalProperties canalProperties;

    @Resource
    private LoginService loginService;

    @Resource
    private CanalService canalService;

    Map<String, Object> talentParamMap = new HashMap<>();

    private MqMessageCountVM checkTalentHrMessageCount(){
        String api = canalProperties.getApiBase() + URI_TALENT_HR_MQ_MESSAGE_COUNT_CHECK;
        HttpHeaders headers = loginService.buildHeaders();
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(headers);
        ResponseEntity<MqMessageCountVM> response = this.restTemplate.postForEntity(api, entity, MqMessageCountVM.class);
        MqMessageCountVM messageCountVM = response.getBody();
        log.info("checkTalentMessageCount:" + messageCountVM);
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED
                && messageCountVM.getError() != null
                && messageCountVM.getError().contains("invalid_token")) {
            log.warn("[checkTalentMessageCount @-1] Access token expired.");
            // clean the token
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            return checkTalentHrMessageCount();
        }
        return messageCountVM;
    }

    public boolean isTalentHrQueueAvailable(){
        try {
            final MqMessageCountVM messageCountVM = this.checkTalentHrMessageCount();
            return Objects.nonNull(messageCountVM.getMessageCount())
                    && Objects.nonNull(messageCountVM.getMaximumMessageCount())
                    && messageCountVM.getMaximumMessageCount() > messageCountVM.getMessageCount();
        } catch (Exception e) {
            log.error("isTalentHrQueueAvailable is error, message = {}", ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    @Override
    public void synchronizeHrTalents(Collection<Long> talentIds, int priority, int deep) {
        talentParamMap.put("talentIds", talentIds);
        talentParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_TALENT_HR_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(talentParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeHrTalents @-1] synchronizeHrTalents error, talentIds: {}, responseCode: {}, responseBody: {}",
                        talentIds, response.getStatusCode(), response.getBody());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(talentIds, SyncIdTypeEnum.HR_TALENT, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "synchronizeHrTalents error " +
                        "\n\tHrTalentIds: " + talentIds +
                        "\n\tResponse Code: " + response.getStatusCode() +
                        "\n\tResponse Body: " + response.getBody();
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                log.warn("[synchronizeHrTalents @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeHrTalents] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeHrTalents(talentIds, priority, deep + 1);
                }else {
                    canalService.insertAll(talentIds, SyncIdTypeEnum.HR_TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeHrTalents] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(talentIds, SyncIdTypeEnum.HR_TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeHrTalents @{}] synchronizeHrTalents error. HrTalentIds: {}, error: {}", SecurityUtils.getUserId(), talentIds, ExceptionUtil.getStackTrace(e));
                String message = "SynchronizeTalents error " +
                        "\n\tHrTalentIds: " + talentIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (Exception e) {
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            canalService.insertAll(talentIds, SyncIdTypeEnum.HR_TALENT, FailReasonEnum.ERROR, e.getMessage(), priority);
            log.error("[synchronizeHrTalents @{}] synchronizeHrTalents error, talentIds: {}, error: {}", SecurityUtils.getUserId(), talentIds, ExceptionUtil.getStackTrace(e));
            String message = "synchronizeHrTalents error " +
                    "\n\tHrTalentIds: " + talentIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

}
