package com.altomni.apn.canal.web.rest;

import com.altomni.apn.canal.service.sync.company.SyncCompanyService;
import com.altomni.apn.canal.service.sync.job.SyncJobService;
import com.altomni.apn.canal.service.sync.talent.SyncTalentService;
import com.altomni.apn.canal.service.sync.user.SyncUserService;
import com.altomni.apn.canal.web.rest.vm.BulkSyncBySqlVM;
import com.altomni.apn.common.web.rest.CommonResource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class SyncResource {
    @Resource
    private SyncTalentService syncTalentService;

    @Resource
    private SyncJobService syncJobService;

    @Resource
    private SyncCompanyService syncCompanyService;

    @Resource
    private SyncUserService syncUserService;

    @PostMapping("/bulk-sync-talents")
    public ResponseEntity<Void> bulkSyncTalents(@RequestBody BulkSyncBySqlVM bulkSyncBySqlVM){
        syncTalentService.validateSql(bulkSyncBySqlVM.getSql());
        syncTalentService.bulkSyncTalents(bulkSyncBySqlVM.getSql());
        return ResponseEntity.accepted().build();
    }

    /*@PostMapping("/bulk-sync-all-talents")
    public ResponseEntity<Void> bulkSyncAllTalents(@PageableDefault(value = 1000) Pageable pageable){
        syncTalentService.bulkSyncAllTalents(pageable);
        return ResponseEntity.accepted().build();
    }*/

    @PostMapping("/bulk-sync-jobs")
    public ResponseEntity<Void> bulkSyncJobs(@RequestBody BulkSyncBySqlVM bulkSyncBySqlVM){
        syncJobService.validateSql(bulkSyncBySqlVM.getSql());
        syncJobService.bulkSyncJobs(bulkSyncBySqlVM.getSql());
        return ResponseEntity.accepted().build();
    }

    @PostMapping("/bulk-sync-companies")
    public ResponseEntity<Void> bulkSyncCompanies(@RequestBody BulkSyncBySqlVM bulkSyncBySqlVM){
        syncCompanyService.validateSql(bulkSyncBySqlVM.getSql());
        syncCompanyService.bulkSyncCompanies(bulkSyncBySqlVM.getSql());
        return ResponseEntity.accepted().build();
    }

    @PostMapping("/bulk-sync-company-client-notes")
    public ResponseEntity<Void> bulkSyncCompanyClientNotes(@RequestBody BulkSyncBySqlVM bulkSyncBySqlVM){
        syncCompanyService.validateSql(bulkSyncBySqlVM.getSql());
        syncCompanyService.bulkSyncClientNotes(bulkSyncBySqlVM.getSql());
        return ResponseEntity.accepted().build();
    }

    @PostMapping("/bulk-sync-company-progress-notes")
    public ResponseEntity<Void> bulkSyncCompanyProgressNotes(@RequestBody BulkSyncBySqlVM bulkSyncBySqlVM){
        syncCompanyService.validateSql(bulkSyncBySqlVM.getSql());
        syncCompanyService.bulkSyncProgressNotes(bulkSyncBySqlVM.getSql());
        return ResponseEntity.accepted().build();
    }

    @PostMapping("/bulk-sync-users")
    public ResponseEntity<Void> bulkSyncUsers(@RequestBody BulkSyncBySqlVM bulkSyncBySqlVM){
        syncUserService.validateSql(bulkSyncBySqlVM.getSql());
        syncUserService.bulkSyncUsers(bulkSyncBySqlVM.getSql());
        return ResponseEntity.accepted().build();
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }
}
