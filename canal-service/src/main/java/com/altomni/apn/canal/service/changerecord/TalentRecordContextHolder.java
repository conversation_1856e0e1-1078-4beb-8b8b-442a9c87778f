package com.altomni.apn.canal.service.changerecord;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Collections;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class TalentRecordContextHolder {

    public TalentRecordContextHolder(Map<Long, Long> recruitmentProcessTalentIdMap,
                                     Map<Long, Long> talentAdditional2TalentIdMap,
                                     Map<Long, String> submitToJobResumeFileNameMap,
                                     Map<Long, String> talentRecruitmentProcessJobNameMap) {

        this.recruitmentProcessTalentIdMap = recruitmentProcessTalentIdMap;
        this.talentAdditional2TalentIdMap = talentAdditional2TalentIdMap;
        this.submitToJobResumeFileNameMap = submitToJobResumeFileNameMap;
        this.talentRecruitmentProcessJobNameMap = talentRecruitmentProcessJobNameMap;
    }

    private Map<Long, Long> recruitmentProcessTalentIdMap = Collections.emptyMap();
    private Map<Long, Long> talentAdditional2TalentIdMap = Collections.emptyMap();
    private Map<Long, String> submitToJobResumeFileNameMap = Collections.emptyMap();
    private Map<Long, String> talentRecruitmentProcessJobNameMap = Collections.emptyMap();

}
