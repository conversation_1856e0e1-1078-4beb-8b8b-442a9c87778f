package com.altomni.apn.canal.repository;

import com.altomni.apn.canal.repository.dto.TalentAdditionalToTalentId;
import com.altomni.apn.common.domain.talent.TalentV3;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the Talent entity.
 */
@Repository
public interface TalentRepository extends JpaRepository<TalentV3, Long> {

    @Query("select new com.altomni.apn.canal.repository.dto.TalentAdditionalToTalentId(t.talentAdditionalInfo.id, t.id) from TalentV3 t where t.talentAdditionalInfo.id in :additionalIds")
    List<TalentAdditionalToTalentId> findTalentIdsByAdditionalIds(@Param("additionalIds") Collection<Long> additionalIds);

    @Query(value = """
            select t.id
            from talent t
            where t.id in ?1 and t.is_need_sync_hr = 1
            """, nativeQuery = true)
    Set<Long> findFilterAllSyncHrById(Set<Long> talentRelationSyncHrIdSet);

}
