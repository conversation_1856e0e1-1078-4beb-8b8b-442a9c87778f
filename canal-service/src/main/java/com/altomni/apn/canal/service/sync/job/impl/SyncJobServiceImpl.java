package com.altomni.apn.canal.service.sync.job.impl;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.repository.JobRepositoryCustom;
import com.altomni.apn.canal.service.login.LoginService;
import com.altomni.apn.canal.service.login.impl.LoginServiceImpl;
import com.altomni.apn.canal.service.sync.job.SyncJobService;
import com.altomni.apn.canal.web.rest.vm.MqMessageCountVM;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncJobServiceImpl implements SyncJobService {
    public static final String URI_JOB_MQ_MESSAGE_COUNT_CHECK = "/job/api/v3/canal/check-job-mq-message-count";
    public static final String URI_JOB_SYNCHRONIZATION = "/job/api/v3/canal/sync-jobs-to-mq";
    public static final String URI_BULK_JOB_SYNCHRONIZATION = "/job/api/v3/canal/bulk-sync-jobs-to-mq";
    public static final String URI_JOB_HR_SYNCHRONIZATION = "/job/api/v3/canal/sync-jobs-to-hr-mq";
    public static final String URI_JOB_AGENCY_SYNCHRONIZATION = "/job/api/v3/canal/sync-jobs-to-agency-mq";

    private RestTemplate restTemplate = new RestTemplate();

    @Resource
    private JobRepositoryCustom jobRepositoryCustom;

    @Resource
    private CanalProperties canalProperties;

    @Resource
    private LoginService loginService;

//    @Resource
//    private RedisService redisService;

    @Resource
    private CanalService canalService;

    Map<String, Object> jobParamMap = new HashMap<>();

    public MqMessageCountVM checkJobMessageCount(){
        String api = canalProperties.getApiBase() + URI_JOB_MQ_MESSAGE_COUNT_CHECK;
        HttpHeaders headers = loginService.buildHeaders();
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(headers);
        ResponseEntity<MqMessageCountVM> response = this.restTemplate.postForEntity(api, entity, MqMessageCountVM.class);
        MqMessageCountVM messageCountVM = response.getBody();
//        log.info("checkTalentMessageCount:" + messageCountVM);
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED
                && messageCountVM.getError() != null
                && messageCountVM.getError().contains("invalid_token")) {
            log.warn("[checkJobMessageCount @-1] Access token expired.");
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            return checkJobMessageCount();
        }
        return messageCountVM;
    }

    @Override
    public void synchronizeJobs(Collection<Long> jobIds, int priority, int deep) {
        jobParamMap.put("jobIds", jobIds);
        jobParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_JOB_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(jobParamMap, headers);
            StopWatch stopWatch = new StopWatch("synchronizeJobs");
            stopWatch.start("start synchronizeJobs");
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            stopWatch.stop();
            log.info("synchronizeJobs time [{} ms] \n {},jobsIds size:{}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint(), jobIds.size());
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeJobs @{}] synchronizeJobs error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "SynchronizeJobs error " +
                        "\n\tjobIds: " + jobIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                log.error("[synchronizeJobs @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeJobs] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeJobs(jobIds, priority, deep + 1);
                }else {
                    canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeJobs] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeJobs @{}] SynchronizeJobs error. JobIds: {}. Error: {}", SecurityUtils.getUserId(), jobIds, ExceptionUtil.getStackTrace(e));
                String message = "SynchronizeJobs error" +
                        "\n\tjobIds: " + jobIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            log.error("[synchronizeJobs @{}] synchronizeJobs error, jobIds: {}, error: {}", SecurityUtils.getUserId(), jobIds, ExceptionUtil.getStackTrace(e));
            String message = "SynchronizeJobs error" +
                    "\n\tjobIds: " + jobIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }


    public void bulkSynchronizeJobs(Collection<Long> jobIds, int priority, int deep) {
        jobParamMap.put("jobIds", jobIds);
        jobParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_BULK_JOB_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(jobParamMap, headers);
            StopWatch stopWatch = new StopWatch("bulkSynchronizeJobs");
            stopWatch.start("start bulkSynchronizeJobs");
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            stopWatch.stop();
            log.info("bulkSynchronizeJobs time [{} ms] \n {},jobsIds size:{}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint(), jobIds.size());
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[bulkSynchronizeJobs @{}] bulkSynchronizeJobs error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "bulkSynchronizeJobs error " +
                        "\n\tjobIds: " + jobIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                log.error("[bulkSynchronizeJobs @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeJobs] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    bulkSynchronizeJobs(jobIds, priority, deep + 1);
                }else {
                    canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[bulkSynchronizeJobs] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[bulkSynchronizeJobs @{}] bulkSynchronizeJobs error. JobIds: {}. Error: {}", SecurityUtils.getUserId(), jobIds, ExceptionUtil.getStackTrace(e));
                String message = "bulkSynchronizeJobs error" +
                        "\n\tjobIds: " + jobIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            log.error("[synchronizeJobs @{}] synchronizeJobs error, jobIds: {}, error: {}", SecurityUtils.getUserId(), jobIds, ExceptionUtil.getStackTrace(e));
            String message = "SynchronizeJobs error" +
                    "\n\tjobIds: " + jobIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    @Override
    public void synchronizeJobHrs(Collection<Long> jobIds, int priority, int deep) {
        jobParamMap.put("jobIds", jobIds);
        jobParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_JOB_HR_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(jobParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeJobHrs @{}] synchronizeJobHrs error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(jobIds, SyncIdTypeEnum.HR_JOB, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "synchronizeJobHrs error " +
                        "\n\tjobIds: " + jobIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                log.error("[synchronizeJobHrs @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeJobs] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeJobs(jobIds, priority, deep + 1);
                }else {
                    canalService.insertAll(jobIds, SyncIdTypeEnum.HR_JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeJobHrs] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(jobIds, SyncIdTypeEnum.HR_JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeJobHrs @{}] synchronizeJobHrs error. JobIds: {}. Error: {}", SecurityUtils.getUserId(), jobIds, ExceptionUtil.getStackTrace(e));
                String message = "synchronizeJobHrs error" +
                        "\n\tjobIds: " + jobIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            canalService.insertAll(jobIds, SyncIdTypeEnum.HR_JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            log.error("[synchronizeJobHrs @{}] synchronizeJobHrs error, jobIds: {}, error: {}", SecurityUtils.getUserId(), jobIds, ExceptionUtil.getStackTrace(e));
            String message = "synchronizeJobHrs error" +
                    "\n\tjobIds: " + jobIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    @Override
    public void synchronizeJobToAgency(Collection<Long> jobIds, int priority, int deep) {
        jobParamMap.put("jobIds", jobIds);
        jobParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_JOB_AGENCY_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(jobParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeJobToAgency @{}] synchronizeJobToAgency error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(jobIds, SyncIdTypeEnum.AGENCY_JOB, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "synchronizeJobToAgency error " +
                        "\n\tjobIds: " + jobIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        } catch (HttpClientErrorException e) {
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED) {
                log.error("[synchronizeJobToAgency @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1) {
                    //String message = "[synchronizeJobs] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeJobToAgency(jobIds, priority, deep + 1);
                } else {
                    canalService.insertAll(jobIds, SyncIdTypeEnum.AGENCY_JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeJobToAgency] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            } else {
                canalService.insertAll(jobIds, SyncIdTypeEnum.AGENCY_JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeJobToAgency @{}] synchronizeJobToAgency error. JobIds: {}. Error: {}", SecurityUtils.getUserId(), jobIds, ExceptionUtil.getStackTrace(e));
                String message = "synchronizeJobToAgency error" +
                        "\n\tjobIds: " + jobIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        } catch (Exception e){
            canalService.insertAll(jobIds, SyncIdTypeEnum.AGENCY_JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            log.error("[synchronizeJobToAgency @{}] synchronizeJobToAgency error, jobIds: {}, error: {}", SecurityUtils.getUserId(), jobIds, ExceptionUtil.getStackTrace(e));
            String message = "synchronizeJobToAgency error" +
                    "\n\tjobIds: " + jobIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    @Override
    public void validateSql(String sql) {
        jobRepositoryCustom.getIdsBySql(sql + " limit 1");
    }

    @Async
    @Override
    public void bulkSyncJobs(String sql) {
        try {
            Integer unfinished = bulkSyncJobs(sql, 50);
            syncFinish(unfinished);
        } catch (Exception e) {
            log.error("[bulkSyncJobs @-1] bulkSyncJobs error: {} at: {}", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e), System.currentTimeMillis());
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), String.format("bulkSyncJobs error:  %s", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e)));
        }
    }

    private Integer bulkSyncJobs(String sql, int pageSize) throws Exception {
        String startTime = DateUtil.getStandardCurrentTime();
        Long lastId = Long.MAX_VALUE;
        List<Long> jobIds = jobRepositoryCustom.getIdsBySql(String.format("%s and j.id < %d order by j.id desc limit %d", sql, lastId, pageSize));
        Long maxId = 0L;
        if (!CollectionUtils.isEmpty(jobIds)){
            maxId = jobIds.get(0);
        }
        int checkJobQueueTimes = 1;
        while(!CollectionUtils.isEmpty(jobIds)){
            if (checkJobQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The job queue is abnormal for 30 minutes.");
            }
            if (!isJobQueueAvailable()){
                checkJobQueueTimes ++;
                Thread.sleep(5000);
                continue;
            }
            lastId = jobIds.get(jobIds.size() - 1);
            //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "Last job ID: " + lastId);
            log.info("LastJobID: {}", lastId);
            bulkSynchronizeJobs(jobIds, RabbitMqConstant.MESSAGE_PRIORITY_LOW, 0);
            jobIds = jobRepositoryCustom.getIdsBySql(String.format("%s and j.id < %d order by j.id desc limit %d", sql, lastId, pageSize));
            checkJobQueueTimes = 1;
        }

        while (!isJobQueueEmpty()) {
            checkJobQueueTimes ++;
            if (checkJobQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The job queue is abnormal for 30 minutes.");
            }
            Thread.sleep(5000);
        }
        Thread.sleep(60000);
        Long unfinished = jobRepositoryCustom.getIdsBySql(String.format("%s and j.id <= %d and (j.last_sync_time is null or j.last_sync_time < '%s')",
                sql.replaceFirst("j.id", "count(j.id)"), maxId, startTime)).get(0);
        return unfinished.intValue();
    }

    private void syncFinish(Integer unfinished){
        if (unfinished.intValue() == 0){
            log.info("bulkSyncJobs finished");
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "bulkSyncJobs finished.");
        }else{
            log.warn("bulkSyncJobs unfinished. Count: {}", unfinished);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "bulkSyncJobs unfinished. Count: " + unfinished);
        }
    }

    private boolean isJobQueueEmpty(){
        return this.checkJobMessageCount().getMessageCount() == 0;
    }

    public boolean isJobQueueAvailable(){
        final MqMessageCountVM messageCountVM = this.checkJobMessageCount();
        return Objects.nonNull(messageCountVM.getMessageCount())
                && Objects.nonNull(messageCountVM.getMaximumMessageCount())
                && messageCountVM.getMaximumMessageCount() > messageCountVM.getMessageCount();
    }
}
