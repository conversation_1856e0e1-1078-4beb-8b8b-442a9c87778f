package com.altomni.apn.canal.repository;

import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class RepositoryCustom {

    @PersistenceContext
    private EntityManager entityManager;

    public List<Long> getIdsBySql(String sql){
        Query nativeQuery = entityManager.createNativeQuery(sql);
        List<BigInteger> resultList = nativeQuery.getResultList();
        return resultList.stream().map(BigInteger::longValue).collect(Collectors.toList());
    }

}
