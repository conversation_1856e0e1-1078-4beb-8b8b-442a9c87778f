package com.altomni.apn.canal;

import com.altomni.apn.common.config.*;
import com.altomni.apn.common.config.env.CommonApplicationProperties;
import com.altomni.apn.common.config.loadbalancer.LoadBalanceConfiguration;
import com.altomni.apn.common.errors.ExceptionTranslator;
import com.altomni.apn.common.interceptor.CustomResponseBodyAdviceAdapter;
import com.altomni.apn.common.interceptor.FeignClientInterceptor;
import com.altomni.apn.common.interceptor.SecurityObjectLevelInterceptor;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CachedFeignSsoUserMapping;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.canal.impl.CanalServiceImpl;
import com.altomni.apn.common.service.http.impl.HttpServiceImpl;
import com.altomni.apn.common.service.log.impl.LoggingServiceImpl;
import com.altomni.apn.common.utils.SecurityUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.elasticsearch.ElasticSearchRestHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableJpaRepositories("com.altomni.apn.*.repository")
@EntityScan("com.altomni.apn.*.domain")
@EnableFeignClients(basePackages = {"com.altomni.apn"})
@SpringBootApplication(exclude = {ElasticSearchRestHealthContributorAutoConfiguration.class})
@EnableDiscoveryClient
@EnableScheduling
@EnableAsync
@EnableCaching
@LoadBalancerClients(defaultConfiguration = LoadBalanceConfiguration.class)
@Import({ExceptionTranslator.class,
        PublicBeanInjection.class,
        HttpServiceImpl.class,
        CachePermission.class,
        CachedFeignSsoUserMapping.class,
        CommonRedisService.class,
        CommonApplicationProperties.class,
        FeignClientInterceptor.class,
        CanalServiceImpl.class,
        EnumCommonService.class,
        WebMvcConfig.class,
        SecurityObjectLevelInterceptor.class,
        CustomResponseBodyAdviceAdapter.class,
        LoggingServiceImpl.class,
        CacheConfig.class,
        SecurityUtils.class,
        AppInit.class,
        GlobalCacheConfig.class,
        JacksonConfiguration.class})
public class CanalApp {
    public static void main(String[] args) {
        SpringApplication.run(CanalApp.class, args);
    }


}


