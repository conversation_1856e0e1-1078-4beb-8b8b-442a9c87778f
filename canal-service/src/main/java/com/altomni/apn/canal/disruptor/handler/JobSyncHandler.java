package com.altomni.apn.canal.disruptor.handler;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.service.sync.job.SyncJobService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.google.common.collect.Iterables;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;

@Slf4j
@Component
public class JobSyncHandler implements EventHandler<CanalEvent> {

    private final CanalService canalService;
    private final SyncJobService syncJobService;

    public JobSyncHandler(CanalService canalService, SyncJobService syncJobService) {
        this.canalService = canalService;
        this.syncJobService = syncJobService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> JobSyncHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Set<Long> jobIds = eventContents.stream()
                .filter(eventContent -> JOB.equals(eventContent.tableName()) || JOB_RELATED_TABLES.contains(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    log.info("[canal] syncTable, table {} changed, event type {}", tableName, eventType);

                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();

                    return switch (eventType) {
                        case INSERT -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, false))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, true))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case DELETE -> rowDataList.stream()
                                .map(rowData -> rowDeleted(rowData.getBeforeColumnsList(), tableName))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                }).collect(Collectors.toSet());

        if (jobIds.isEmpty()) {
            return;
        }

        syncJobsToEs(jobIds);
    }

    private void syncJobsToEs(Set<Long> pendingJobsToSync) {
        log.info("[Canal] jobSync startSyncJobsToEs {}", pendingJobsToSync);
        try {
            for (List<Long> partition : Iterables.partition(pendingJobsToSync, 10)) {
                syncJobService.synchronizeJobs(partition, RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL, 0);
            }
        } catch (Exception e) {
            log.error("[Canal] jobSync SyncJobToES Failed, jobs: {}, error: {}", pendingJobsToSync, ExceptionUtil.getStackTrace(e));
            canalService.insertAll(pendingJobsToSync, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL);
        }
    }

    private Optional<Long> changeRows(List<CanalEntry.Column> columns, String tableName, boolean update) {
        if (JOB.equals(tableName)) {
            return jobChange(columns, tableName, update);
        }
        if (JOB_RELATED_TABLES.contains(tableName)) {
            return jobRelatedChange(columns, tableName);
        }
        return Optional.empty();
    }

    private Optional<Long> rowDeleted(List<CanalEntry.Column> columns, String tableName) {
        if (!JOB_RELATED_TABLES.contains(tableName)) {
            return Optional.empty();
        }
        return jobRelatedChange(columns, tableName);
    }

    private Optional<Long> jobChange(List<CanalEntry.Column> columns, String tableName, boolean update) {
        String jobId = null;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            log.debug("[Canal] jobSync jobChange: table: {}, column: {}, value: {}, updated: {}, jobId: {}", tableName, name, column.getValue(), column.getUpdated(), jobId);
            // for fill back
            if (update && name.equals(LAST_SYNC_TIME) && column.getUpdated()) {
                log.info("[Canal] jobSync jobChange: column: {}, value: {}, updated: {} drop process", name, column.getValue(), column.getUpdated());
                return Optional.empty();
            }
            if (name.equals(ID)) {
                jobId = column.getValue();
            }
        }
        log.info("[Canal] received changed jobId: {} from table: {}", jobId, tableName);
        return Optional.ofNullable(jobId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

    // TODO: 2023/12/15  Later, consider changing the APN_JOB_ID field to the JOB_ID field.
    private Optional<Long> jobRelatedChange(List<CanalEntry.Column> columns, String tableName) {
        String jobId = null;
        //only update ai source field, not sync job
        boolean onlyUpdateAiSource = columns.stream().filter(CanalEntry.Column::getUpdated)
                .allMatch(column -> "talent_recruitment_process".equals(tableName) && "ai_score".equals(column.getName()));
        if (onlyUpdateAiSource) {
            return Optional.empty();
        }
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            if (name.equals(JOB_ID) || (tableName.equals(JOB_IPG_RELATION) && name.equals(APN_JOB_ID))) {
                jobId = column.getValue();
            }
            log.debug("jobRelatedChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, column.getValue(), column.getUpdated());
        }
        log.info("[Canal] received changed jobId: {} from tableName: {}", jobId, tableName);
        return Optional.ofNullable(jobId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }
}
