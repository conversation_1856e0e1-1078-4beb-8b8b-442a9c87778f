package com.altomni.apn.canal.disruptor.handler;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.service.sync.job.SyncJobService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.google.common.collect.Iterables;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;

@Slf4j
@Component
public class JobSyncHrHandler implements EventHandler<CanalEvent> {

    private final CanalService canalService;
    private final SyncJobService syncJobService;

    public JobSyncHrHandler(CanalService canalService, SyncJobService syncJobService) {
        this.canalService = canalService;
        this.syncJobService = syncJobService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> JobSyncHrHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Set<Long> jobIds = eventContents.stream()
                .filter(eventContent -> JOB.equals(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    log.info("[canal] syncTable, table {} changed, event type {}", tableName, eventType);
                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();
                    return switch (eventType) {
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, true))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                }).collect(Collectors.toSet());

        if (jobIds.isEmpty()) {
            return;
        }
        syncJobsToHr(jobIds);
    }

    private void syncJobsToHr(Set<Long> pendingJobsToSync) {
        log.info("[Canal] jobSync startSyncJobsToHr {}", pendingJobsToSync);
        try {
            for (List<Long> partition : Iterables.partition(pendingJobsToSync, 50)) {
                syncJobService.synchronizeJobHrs(partition, RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL, 0);
            }
        } catch (Exception e) {
            log.error("[Canal] jobSync SyncJobToHr Failed, jobs: {}, error: {}", pendingJobsToSync, ExceptionUtil.getStackTrace(e));
            canalService.insertAll(pendingJobsToSync, SyncIdTypeEnum.HR_JOB, FailReasonEnum.ERROR, e.getMessage(), RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL);
        }
    }

    private Optional<Long> changeRows(List<CanalEntry.Column> columns, String tableName, boolean update) {
        if (JOB.equals(tableName)) {
            return jobChange(columns, tableName, update);
        }
        return Optional.empty();
    }

    private Optional<Long> jobChange(List<CanalEntry.Column> columns, String tableName, boolean update) {
        String jobId = null;
        boolean flag = false;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            log.debug("[Canal] jobSync jobChange: table: {}, column: {}, value: {}, updated: {}, jobId: {}", tableName, name, column.getValue(), column.getUpdated(), jobId);
            if (name.equals(IS_NEED_SYNC_HR)) {
                if ("1".equals(column.getValue())) {
                    if (update && column.getUpdated()) {
                        flag = true;
                    }
                }  else {
                    String value = column.getValue();
                    log.info("IS_NEED_SYNC_HR : {}", value);
                    return Optional.empty();
                }
            }
            // for fill back
            if (update && name.equals(LAST_SYNC_TIME) && column.getUpdated()) {
                log.info("[Canal] jobSync jobChange: column: {}, value: {}, updated: {} drop process", name, column.getValue(), column.getUpdated());
                return Optional.empty();
            }
            if (update && name.equals(JOB_TITLE) && column.getUpdated()) {
                flag = true;
            }
            if (name.equals(ID)) {
                jobId = column.getValue();
            }
        }
        if (!flag) {
            return Optional.empty();
        }
        log.info("[Canal] received changed jobId: {} from table: {}", jobId, tableName);
        return Optional.ofNullable(jobId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

}

