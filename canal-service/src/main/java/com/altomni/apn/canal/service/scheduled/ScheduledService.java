package com.altomni.apn.canal.service.scheduled;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.service.sync.company.SyncCompanyService;
import com.altomni.apn.canal.service.sync.job.SyncJobService;
import com.altomni.apn.canal.service.sync.talent.SyncHrService;
import com.altomni.apn.canal.service.sync.talent.SyncTalentService;
import com.altomni.apn.canal.service.sync.user.SyncUserService;
import com.altomni.apn.common.domain.canal.CanalSyncRecord;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.NotificationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ScheduledService {
    @Resource
    private SyncTalentService syncTalentService;

    @Resource
    private CanalService canalService;

    @Resource
    private SyncJobService syncJobService;

    @Resource
    private SyncCompanyService syncCompanyService;

    @Resource
    private SyncUserService syncUserService;

    @Resource
    private SyncHrService syncHrService;

    @Resource
    private CanalProperties properties;

    /**
     * Sync failed hr talents every 10s
     */
    //@Scheduled(fixedRateString = "10000")
    public void syncFailedHrTalents(){
        if (!syncHrService.isTalentHrQueueAvailable()) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordList = canalService.findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum.HR_TALENT, properties.getRetryBatchSize(), properties.getFailedCountLimit());
        if (CollUtil.isEmpty(canalSyncRecordList)) {
            return;
        }
        Map<Integer, List<CanalSyncRecord>> listMap = canalSyncRecordList.stream().collect(Collectors.groupingBy(CanalSyncRecord::getPriority));
        Set<Long> failedHrTalentIds = new HashSet<>();
        try {
            for (Map.Entry<Integer, List<CanalSyncRecord>> entry : listMap.entrySet()) {
                failedHrTalentIds = entry.getValue().stream().map(CanalSyncRecord::getTaskId).collect(Collectors.toSet());
                CollUtil.split(failedHrTalentIds, 50).forEach(ids -> syncHrService.synchronizeHrTalents(ids, entry.getKey(), 0));
            }
        } catch (Exception e) {
            log.error("[Scheduled] sync_failed_hr_talent_to_mq error, failedTalentIds: {}, error: {}", failedHrTalentIds, ExceptionUtil.getStackTrace(e));
            String message = "Sync_failed_talent_to_es error : " +
                    "\n\tHrTalent IDs: " + failedHrTalentIds + "\n " +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(properties.getWebhookKey(), properties.getWebhookUrl(), message);
        }
    }

    /**
     * Sync failed talents every 10s
     */
    //@Scheduled(fixedRateString = "10000")
    public void syncFailedCompanyContacts(){
        if (!syncCompanyService.isCompanyContactQueueAvailable()) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordList = canalService.findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum.HR_COMPANY_CONTACT, properties.getRetryBatchSize(), properties.getFailedCountLimit());
        if (CollUtil.isEmpty(canalSyncRecordList)) {
            return;
        }
        Map<Integer, List<CanalSyncRecord>> listMap = canalSyncRecordList.stream().collect(Collectors.groupingBy(CanalSyncRecord::getPriority));
        Set<Long> failedContactIds = new HashSet<>();
        try {
            for (Map.Entry<Integer, List<CanalSyncRecord>> entry : listMap.entrySet()) {
                failedContactIds = entry.getValue().stream().map(CanalSyncRecord::getTaskId).collect(Collectors.toSet());
                CollUtil.split(failedContactIds, 50).forEach(ids -> syncCompanyService.synchronizeCompanyContacts(ids, entry.getKey(), 0));
            }
        } catch (Exception e) {
            log.error("[Scheduled] sync_failed_company_contact_to_mq error, failedContactIds: {}, error: {}", failedContactIds, ExceptionUtil.getStackTrace(e));
            String message = "sync_failed_company_contact_to_mq error : " +
                    "\n\tContact IDs: " + failedContactIds + "\n " +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(properties.getWebhookKey(), properties.getWebhookUrl(), message);
        }
    }

    /**
     * Sync failed talents every 10s
     */
    @Scheduled(fixedRateString = "10000")
    public void syncFailedCompanyHr(){
        if (!syncCompanyService.isCompanyContactQueueAvailable()) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordList = canalService.findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum.HR_COMPANY, properties.getRetryBatchSize(), properties.getFailedCountLimit());
        if (CollUtil.isEmpty(canalSyncRecordList)) {
            return;
        }
        Map<Integer, List<CanalSyncRecord>> listMap = canalSyncRecordList.stream().collect(Collectors.groupingBy(CanalSyncRecord::getPriority));
        Set<Long> failedCompanyIds = new HashSet<>();
        try {
            for (Map.Entry<Integer, List<CanalSyncRecord>> entry : listMap.entrySet()) {
                failedCompanyIds = entry.getValue().stream().map(CanalSyncRecord::getTaskId).collect(Collectors.toSet());
                CollUtil.split(failedCompanyIds, 50).forEach(ids -> syncCompanyService.synchronizeCompanyHr(ids, entry.getKey(), 0));
            }
        } catch (Exception e) {
            log.error("[Scheduled] sync_failed_company_to_hr_mq error, failedCompanyIds: {}, error: {}", failedCompanyIds, ExceptionUtil.getStackTrace(e));
            String message = "sync_failed_company_to_hr_mq error : " +
                    "\n\tcompany IDs: " + failedCompanyIds + "\n " +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(properties.getWebhookKey(), properties.getWebhookUrl(), message);
        }
    }


    /**
     * Sync failed talents every 10s
     */
    //@Scheduled(fixedRateString = "10000")
    public void syncFailedTalents(){
        if (!syncTalentService.isTalentQueueAvailable()) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordList = canalService.findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum.TALENT, properties.getRetryBatchSize(), properties.getFailedCountLimit());
        if (CollUtil.isEmpty(canalSyncRecordList)) {
            return;
        }
        Map<Integer, List<CanalSyncRecord>> listMap = canalSyncRecordList.stream().collect(Collectors.groupingBy(CanalSyncRecord::getPriority));
        Set<Long> failedTalentIds = new HashSet<>();
        try {
            for (Map.Entry<Integer, List<CanalSyncRecord>> entry : listMap.entrySet()) {
                failedTalentIds = entry.getValue().stream().map(CanalSyncRecord::getTaskId).collect(Collectors.toSet());
                CollUtil.split(failedTalentIds, 50).forEach(ids -> syncTalentService.synchronizeTalents(ids, entry.getKey(), 0));
            }
        } catch (Exception e) {
            log.error("[Scheduled] sync_failed_talent_to_es error, failedTalentIds: {}, error: {}", failedTalentIds, ExceptionUtil.getStackTrace(e));
            String message = "Sync_failed_talent_to_es error : " +
                    "\n\tTalent IDs: " + failedTalentIds + "\n " +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(properties.getWebhookKey(), properties.getWebhookUrl(), message);
        }
    }

    /**
     * Sync failed jobs every 10s
     */
    //@Scheduled(fixedRateString = "10000")
    public void syncFailedJobs(){
        if (!syncJobService.isJobQueueAvailable()) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordList = canalService.findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum.JOB, properties.getRetryBatchSize(), properties.getFailedCountLimit());
        if (CollUtil.isEmpty(canalSyncRecordList)) {
            return;
        }
        Map<Integer, List<CanalSyncRecord>> listMap = canalSyncRecordList.stream().collect(Collectors.groupingBy(CanalSyncRecord::getPriority));
        Set<Long> failedJobIds = new HashSet<>();
        try {
            for (Map.Entry<Integer, List<CanalSyncRecord>> entry : listMap.entrySet()) {
                failedJobIds = entry.getValue().stream().map(CanalSyncRecord::getTaskId).collect(Collectors.toSet());
                CollUtil.split(failedJobIds, 50).forEach(ids -> syncJobService.synchronizeJobs(ids, entry.getKey(), 0));
            }
        } catch (Exception e) {
            log.error("[Scheduled] sync_failed_job_to_es error, failedJobIds: {}, error: {}", failedJobIds, ExceptionUtil.getStackTrace(e));
            String message = "Sync_failed_job_to_es error : " +
                    "\n\tJob IDs: " + failedJobIds + "\n " +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(properties.getWebhookKey(), properties.getWebhookUrl(), message);
        }
    }


    /**
     * Sync failed companies every 10s
     */
    //@Scheduled(fixedRateString = "10000")
    public void syncFailedCompanies(){
        if (!syncCompanyService.isCompanyQueueAvailable()) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordList = canalService.findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum.COMPANY, properties.getRetryBatchSize(), properties.getFailedCountLimit());
        if (CollUtil.isEmpty(canalSyncRecordList)) {
            return;
        }
        Map<Integer, List<CanalSyncRecord>> listMap = canalSyncRecordList.stream().collect(Collectors.groupingBy(CanalSyncRecord::getPriority));
        Set<Long> failedCompanyIds = new HashSet<>();
        try {
            for (Map.Entry<Integer, List<CanalSyncRecord>> entry : listMap.entrySet()) {
                failedCompanyIds = entry.getValue().stream().map(CanalSyncRecord::getTaskId).collect(Collectors.toSet());
                CollUtil.split(failedCompanyIds, 50).forEach(ids -> syncCompanyService.synchronizeCompanies(ids, entry.getKey(), 0));
            }
        } catch (Exception e) {
            log.error("[Scheduled] sync_failed_company_to_es error, failedCompanyIds: {}, error: {}", failedCompanyIds, ExceptionUtil.getStackTrace(e));
            String message = "Sync_failed_company_to_es error : " +
                    "\n\tCompany IDs: " + failedCompanyIds + "\n " +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(properties.getWebhookKey(), properties.getWebhookUrl(), message);
        }
    }

    /**
     * Sync failed companyClientNote every 10s
     */
    //@Scheduled(fixedRateString = "10000")
    public void syncFailedCompanyClientNotes(){
        if (!syncCompanyService.isCompanyClientNoteQueueAvailable()) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordList = canalService.findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum.COMPANY_CLIENT_NOTE, properties.getRetryBatchSize(), properties.getFailedCountLimit());
        if (CollUtil.isEmpty(canalSyncRecordList)) {
            return;
        }
        Map<Integer, List<CanalSyncRecord>> listMap = canalSyncRecordList.stream().collect(Collectors.groupingBy(CanalSyncRecord::getPriority));
        Set<Long> failedCompanyClientNoteIds = new HashSet<>();
        try {
            for (Map.Entry<Integer, List<CanalSyncRecord>> entry : listMap.entrySet()) {
                failedCompanyClientNoteIds = entry.getValue().stream().map(CanalSyncRecord::getTaskId).collect(Collectors.toSet());
                CollUtil.split(failedCompanyClientNoteIds, 50).forEach(ids -> syncCompanyService.synchronizeCompanyClientNotes(ids, entry.getKey(), 0));
            }
        } catch (Exception e) {
            log.error("[Scheduled] sync_failed_company_client_note_to_es error, failedCompanyNoteId: {}, error: {}", failedCompanyClientNoteIds, ExceptionUtil.getStackTrace(e));
            String message = "Sync_failed_company_client_note_to_es error : " +
                    "\n\tNote IDs: " + failedCompanyClientNoteIds + "\n " +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(properties.getWebhookKey(), properties.getWebhookUrl(), message);
        }
    }

    /**
     * Sync failed companyProgressNote every 10s
     */
    //@Scheduled(fixedRateString = "10000")
    public void syncFailedCompanyProgressNotes(){
        if (!syncCompanyService.isCompanyProgressNoteQueueAvailable()) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordList = canalService.findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum.COMPANY_PROGRESS_NOTE, properties.getRetryBatchSize(), properties.getFailedCountLimit());
        if (CollUtil.isEmpty(canalSyncRecordList)) {
            return;
        }
        Map<Integer, List<CanalSyncRecord>> listMap = canalSyncRecordList.stream().collect(Collectors.groupingBy(CanalSyncRecord::getPriority));
        Set<Long> failedCompanyProgressNoteIds = new HashSet<>();
        try {
            for (Map.Entry<Integer, List<CanalSyncRecord>> entry : listMap.entrySet()) {
                failedCompanyProgressNoteIds = entry.getValue().stream().map(CanalSyncRecord::getTaskId).collect(Collectors.toSet());
                CollUtil.split(failedCompanyProgressNoteIds, 50).forEach(ids -> syncCompanyService.synchronizeCompanyProgressNotes(ids, entry.getKey(), 0));
            }
        } catch (Exception e) {
            log.error("[Scheduled] sync_failed_company_progress_note_to_es error, failedCompanyNoteId: {}, error: {}", failedCompanyProgressNoteIds, ExceptionUtil.getStackTrace(e));
            String message = "Sync_failed_company_progress_note_to_es error : " +
                    "\n\tNote IDs: " + failedCompanyProgressNoteIds + "\n " +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(properties.getWebhookKey(), properties.getWebhookUrl(), message);
        }
    }

    //@Scheduled(fixedRateString = "10000")
    public void syncFailedUsers(){
        if (!syncUserService.isUserQueueAvailable()) {
            return;
        }
        List<CanalSyncRecord> canalSyncRecordList = canalService.findIdListByTypeOrderByCreatedDateAsc(SyncIdTypeEnum.USER, properties.getRetryBatchSize(), properties.getFailedCountLimit());
        if (CollUtil.isEmpty(canalSyncRecordList)) {
            return;
        }
        Map<Integer, List<CanalSyncRecord>> listMap = canalSyncRecordList.stream().collect(Collectors.groupingBy(CanalSyncRecord::getPriority));
        Set<Long> failUserIds = new HashSet<>();
        try {
            for (Map.Entry<Integer, List<CanalSyncRecord>> entry : listMap.entrySet()) {
                failUserIds = entry.getValue().stream().map(CanalSyncRecord::getTaskId).collect(Collectors.toSet());
                CollUtil.split(failUserIds, 50).forEach(ids -> syncUserService.synchronizeUser(ids, entry.getKey(), 0));
            }
        } catch (Exception e) {
            log.error("[Scheduled] sync_failed_user error, failUserIds: {}, error: {}", failUserIds, ExceptionUtil.getStackTrace(e));
            String message = "sync_failed_user error : " +
                    "\n\tUSER IDs: " + failUserIds + "\n " +
                    "\n\tError message:" +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(properties.getWebhookKey(), properties.getWebhookUrl(), message);
        }
    }

}
