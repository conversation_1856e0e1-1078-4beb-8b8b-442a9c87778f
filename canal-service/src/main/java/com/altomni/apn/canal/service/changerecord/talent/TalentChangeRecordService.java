package com.altomni.apn.canal.service.changerecord.talent;

import cn.hutool.json.JSONObject;
import com.alibaba.otter.canal.protocol.CanalEntry.Column;
import com.altomni.apn.canal.entity.RecordDataInfo;
import com.altomni.apn.canal.entity.TalentProcessRecord;
import com.altomni.apn.canal.service.changerecord.TalentRecordContextHolder;

import java.util.List;

public interface TalentChangeRecordService {

    List<JSONObject> generateNewTalent(Long talentId);

    Boolean recordInsertTalentRows(List<Column> preColumns, List<Column> afterColumns, String tableName, Long talentId, String createdTime, String createdBy, List<JSONObject> esDocuments, List<TalentProcessRecord> talentProcessRecords, TalentRecordContextHolder talentRecordContextHolder);

    List<JSONObject> recordUpdateTalentRows(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, Long talentId, String createdTime, String createdBy, List<JSONObject> esDocuments, List<TalentProcessRecord> talentProcessRecords, TalentRecordContextHolder talentRecordContextHolder);

    Long recordTalentId(List<Column> preColumns, List<Column> afterColumns, String tableName, TalentRecordContextHolder talentRecordContextHolder);

    String recordCreatedTime(List<Column> preColumns, List<Column> afterColumns, String tableName, String createdTime);

    String recordCreatedBy(List<Column> preColumns, List<Column> afterColumns, String tableName, String createdBy, RecordDataInfo recordDataInfo);

    boolean isMonitoredTable(String tableName);

    void recordDeleteTalentRows(List<Column> beforeColumnsList, List<Column> afterColumnsList, String tableName, List<JSONObject> esDocuments);

}
