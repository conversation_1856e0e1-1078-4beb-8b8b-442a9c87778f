package com.altomni.apn.canal.service.login.impl;

import com.altomni.apn.canal.service.login.LoginService;
import com.ipg.resourceserver.client.ClientTokenHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class LoginServiceImpl implements LoginService {
    public static final String AUTHORIZATION = "Authorization";
    public static final String TOKEN_APN = "APN";
//    public static final String URI_LOGIN = "/user/api/v3/login";

    @Resource
    private ClientTokenHolder clientTokenHolder;

    private static Map<String, String> tokenMap = new HashMap<>();


    @Override
    public void removeToken(String app){
        tokenMap.put(app, null);
    }
//
//    private String login(String clientId, String clientSecret){
//        // create a map for post parameters
////        Map<String, Object> map = new HashMap<>();
////        map.put("username", username);
////        map.put("password", password);
//
//        String clientAuth = Base64.getEncoder().encodeToString((clientId + ":" + clientSecret).getBytes());
//
//        // build the request
//        HttpHeaders headers = new HttpHeaders();
//        // set `content-type` header
////        headers.setContentType(MediaType.APPLICATION_JSON);
////        // set `accept` header
////        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
////        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(map, headers);
////
////        // send POST request
////        String api = canalProperties.getApiBase() + URI_LOGIN;
////        ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
////
////        // check response status code
////        if (response.getStatusCode() == HttpStatus.OK) {
////            JSONObject jsonObject = JSON.parseObject(response.getBody());
////            JSONObject credential = jsonObject.getJSONObject("credential");
////            access_token = credential.getString("access_token");
////            refresh_token = credential.getString("refresh_token");
////            expiresAt = Math.min(credential.getLongValue("expires_in"), 3000) + (System.currentTimeMillis()/1000);
////            log.info("Access token will expire at {} seconds", expiresAt);
////        }else {
////            log.error("[Executor: synchronizeTalentsAndJobsHandler @-1] login APN error: {} at: {}",response.getBody(), System.currentTimeMillis());
////        }
//
//        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
//        headers.add("Authorization", String.format("Basic %s", clientAuth));
//        MultiValueMap<String, Object> form = new LinkedMultiValueMap<>();
//        form.add("grant_type", "client_credentials");
//
//        ResponseEntity<JSONObject> tokenResponse;
//        try {
//            tokenResponse = restTemplate.postForEntity(canalProperties.getSsoLoginUrl(), new HttpEntity<>(form, headers), JSONObject.class);
//        } catch (Exception e) {
//            throw new IllegalStateException("get client token error!", e);
//        }
//        // check response status code
//        if (tokenResponse.getStatusCode() == HttpStatus.OK) {
//            JSONObject credential = tokenResponse.getBody();
//            access_token = credential.getString("access_token");
//            expiresAt = Math.min(credential.getLongValue("expires_in"), 3000) + (System.currentTimeMillis()/1000);
//            log.info("Access token will expire at {} seconds", expiresAt);
//        } else {
//            log.error("[Canal: synchronizeTalentsAndJobsHandler @-1] login APN error: {} at: {}", tokenResponse.getBody(), System.currentTimeMillis());
//        }
//        return access_token;
//    }


    @Override
    public HttpHeaders buildHeaders(){
        final String token = clientTokenHolder.getClientToken().access_token();
        //logger.debug("access token: " + token);
        // set `token` header
        HttpHeaders headers = new HttpHeaders();
        // set `content-type` header
        headers.setContentType(MediaType.APPLICATION_JSON);
        // set `accept` header
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set(AUTHORIZATION, "Bearer " + token);
        return headers;
    }
}
