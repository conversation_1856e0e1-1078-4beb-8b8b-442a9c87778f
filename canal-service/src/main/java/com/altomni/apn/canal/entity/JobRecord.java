package com.altomni.apn.canal.entity;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Data
public class JobRecord {
    Long jobId;
    Long tenantId;
    List<JSONObject> esDocuments;
    String createdBy;
    String createdTime;
    boolean initial;

    public JobRecord() {
        esDocuments = new ArrayList<>();
        initial = false;
    }

    public void setCreateTime(String createdTime) {
        if(this.createdTime == null) {
            this.createdTime = createdTime;
        }
        Instant newTime = Instant.parse(createdTime);
        Instant oldTime = Instant.parse(this.createdTime);
        if(oldTime.compareTo(newTime) < 0) {
            this.createdTime = createdTime;
        }
    }

    public void setInitial(boolean initial) {
        this.initial = this.initial || initial;
    }

    public void addEsDocuments(List<JSONObject> esDocuments) {
        this.esDocuments.addAll(esDocuments);
    }
}
