package com.altomni.apn.canal.disruptor.handler;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.service.sync.company.SyncCompanyService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.google.common.collect.Iterables;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;

@Slf4j
@Component
public class CompanyContactSyncHrHandler implements EventHandler<CanalEvent> {

    private final CanalService canalService;
    private final SyncCompanyService syncCompanyService;

    public CompanyContactSyncHrHandler(CanalService canalService, SyncCompanyService syncCompanyService) {
        this.canalService = canalService;
        this.syncCompanyService = syncCompanyService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> CompanyClientNoteSyncHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Set<Long> companyContactIds = eventContents.stream()
                .filter(eventContent -> COMPANY_CONTACT.equals(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    log.info("[canal] syncTable, table {} changed, event type {}", tableName, eventType);

                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();

                    return switch (eventType) {
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, true))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                }).collect(Collectors.toSet());
        if (companyContactIds.isEmpty()) {
            return;
        }
        syncCompanyContactHrToMq(companyContactIds);
    }


    private void syncCompanyContactHrToMq(Set<Long> companyContactIds) {
        log.info("startSyncCompanyContactHrToMq {}", companyContactIds);
        try {
            for (List<Long> partition : Iterables.partition(companyContactIds, 50)) {
                syncCompanyService.synchronizeCompanyContacts(partition, RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL, 0);
            }
        } catch (Exception e) {
            log.error("[Canal] sync_company_contact_to_hr error: {}", ExceptionUtil.getStackTrace(e));
            canalService.insertAll(companyContactIds,  SyncIdTypeEnum.HR_COMPANY_CONTACT, FailReasonEnum.ERROR, e.getMessage(), RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL);
        }
    }

    private Optional<Long> changeRows(List<CanalEntry.Column> columns, String tableName, boolean update) {
        if (COMPANY_CONTACT.equals(tableName)) {
            return companyContactChange(columns, tableName, update);
        }
        return Optional.empty();
    }


    private Optional<Long> companyContactChange(List<CanalEntry.Column> columns, String tableName, boolean update) {
        String contactId = null;
        boolean mainFlag = false;
        boolean flag = false;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            log.debug("companyContactChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, column.getValue(), column.getUpdated());
            // for fill back
            if (update && name.equals(LAST_SYNC_TIME) && column.getUpdated()) {
                return Optional.empty();
            }
            //存在 approver_id 的客户联系人才需要被同步至hr
            if (name.equals(APPROVER_ID) && StrUtil.isNotBlank(column.getValue())) {
                mainFlag = true;
            }
            if (update && List.of("inactived", "receive_email", "talent_id").contains(name) && column.getUpdated()) {
                flag = true;
            }
            if (name.equals(ID)) {
                contactId = column.getValue();
            }
        }
        if (!flag && !mainFlag) {
            return Optional.empty();
        }
        log.info("[canal] received changed companyClientNoteId: {} from table: {}", contactId, tableName);
        return Optional.ofNullable(contactId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

}
