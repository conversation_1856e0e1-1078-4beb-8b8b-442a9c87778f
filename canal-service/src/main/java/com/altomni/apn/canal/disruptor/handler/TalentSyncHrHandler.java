package com.altomni.apn.canal.disruptor.handler;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.repository.TalentRepository;
import com.altomni.apn.canal.service.sync.talent.SyncHrService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.google.common.collect.Iterables;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;

@Slf4j
@Component
public class TalentSyncHrHandler implements EventHandler<CanalEvent> {

    private final TalentRepository talentRepository;

    private final CanalService canalService;

    private final SyncHrService syncTalentService;

    public TalentSyncHrHandler(final TalentRepository talentRepository, CanalService canalService, SyncHrService syncTalentService) {
        this.talentRepository = talentRepository;
        this.canalService = canalService;
        this.syncTalentService = syncTalentService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> TalentSyncHrHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Set<Long> talentSyncHrIdSet = eventContents.stream()
                .filter(eventContent -> TALENT.equals(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();
                    return switch (eventType) {
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> talentChange(rowData.getAfterColumnsList(), tableName, true))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                })
                .collect(Collectors.toSet());

        Set<Long> talentRelationSyncHrIdSet = eventContents.stream()
                .filter(eventContent -> "talent_contact".equals(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    log.info("[canal] syncTable, table {} changed, event type {}", tableName, eventType);
                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();
                    return switch (eventType) {
                        case INSERT -> rowDataList.stream()
                                .map(rowData -> talentRelatedChange(rowData.getAfterColumnsList(), tableName))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> talentRelatedChange(rowData.getAfterColumnsList(), tableName))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                })
                .collect(Collectors.toSet());
        talentRelationSyncHrIdSet = talentRepository.findFilterAllSyncHrById(talentRelationSyncHrIdSet);

        talentSyncHrIdSet.addAll(talentRelationSyncHrIdSet);

        if (talentSyncHrIdSet.isEmpty()) {
            return;
        }

        //sync to hr
        syncToHr(talentSyncHrIdSet);
    }

    private void syncToHr(Set<Long> talentSyncHrIdSet) {
        log.info("startSyncTalentsToHr {}", talentSyncHrIdSet);
        try {
            for (List<Long> partition : Iterables.partition(talentSyncHrIdSet, 50)) {
                syncTalentService.synchronizeHrTalents(partition, RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL, 0);
            }
        } catch (Exception e) {
            log.error("[Canal] sync_talent_to_hr error, talents {}, error: {}", talentSyncHrIdSet, ExceptionUtil.getStackTrace(e));
            canalService.insertAll(talentSyncHrIdSet, SyncIdTypeEnum.HR_TALENT, FailReasonEnum.ERROR, e.getMessage(), RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL);
        }
    }

    private Optional<Long> talentChange(List<CanalEntry.Column> columns, String tableName, boolean update) {
        String talentId = null;
        boolean flag = false;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            //log.info("talentChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, column.getValue(), column.getUpdated());
            if (name.equals(IS_NEED_SYNC_HR)) {
                if ("1".equals(column.getValue())) {
                    if (update && column.getUpdated()) {
                        flag = true;
                    }
                }  else {
                    String value = column.getValue();
                    log.info("IS_NEED_SYNC_HR : {}", value);
                    return Optional.empty();
                }
            }
            if (update && name.equals(LAST_SYNC_TIME) && column.getUpdated()) {
                String value = column.getValue();
                log.info("LAST_SYNC_TIME : {}", value);
                return Optional.empty();
            }
            if (update && List.of("full_name", "phone_url").contains(name) && column.getUpdated()) {
                flag = true;
            }
            if (name.equals(ID)) {
                talentId = column.getValue();
            }
        }
        if (!flag) {
            return Optional.empty();
        }
        log.info("canal received changed talentId: {} from table: {}", talentId, tableName);
        return Optional.ofNullable(talentId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

    private Optional<Long> talentRelatedChange(List<CanalEntry.Column> columns, String tableName) {
        String talentId = null;
        boolean flag = false;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            if (name.equals(TALENT_ID)) {
                String value = column.getValue();
                if (StringUtils.isNotEmpty(value)) {
                    talentId = column.getValue();
                }
            } else if (LAST_MODIFIED_DATE.equals(name)) {
                log.info("table name : {}, LAST_MODIFIED_DATE : {}", tableName, column.getValue());
            }
            if ("jhi_type".contains(name) && List.of("2", "16").contains(column.getValue())) {
                flag = true;
            }
            log.debug("talentRelatedChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, column.getValue(), column.getUpdated());
        }
        if (!flag) {
            return Optional.empty();
        }
        log.info("[Canal] received changed talentId: {} from table: {}", talentId, tableName);
        return Optional.ofNullable(talentId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

}
