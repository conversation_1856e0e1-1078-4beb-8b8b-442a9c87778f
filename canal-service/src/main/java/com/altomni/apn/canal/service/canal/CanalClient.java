package com.altomni.apn.canal.service.canal;

import com.alibaba.otter.canal.client.CanalConnector;
import com.alibaba.otter.canal.client.CanalConnectors;
import com.alibaba.otter.canal.protocol.CanalEntry.*;
import com.alibaba.otter.canal.protocol.Message;
import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.disruptor.DisruptorQueue;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.service.elastic.EsRecordService;
import com.altomni.apn.common.utils.NotificationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.InetSocketAddress;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;


@Slf4j
@Component
public class CanalClient {

    public static String TALENT = "";

    public static Set<String> TALENT_RELATED_TABLES = new HashSet<>();

    public static Set<String> TALENT_RECORD_TABLES = new HashSet<>();

    public static String JOB = "";

    public static Set<String> JOB_RELATED_TABLES = new HashSet<>();

    public static Set<String> JOB_RECORD_TABLES = new HashSet<>();

    public static Set<String> ASSIGMENT_RECORD_TABLES = new HashSet<>();

    public static Set<String> AGENCY_JOB_RELATED_TABLES = new HashSet<>();

    public static String COMPANY = "";

    public static Set<String> COMPANY_RELATED_TABLES = new HashSet<>();

    public static String COMPANY_CLIENT_NOTE = "";

    public static String COMPANY_PROGRESS_NOTE = "";

    public static String COMPANY_CONTACT = "";

    public static String AGENCY = "";

    public static Set<String> AGENCY_RELATED_TABLES = new HashSet<>();

    public static final String ID = "id";
    public static final String TALENT_RECRUITMENT_PROCESS_ID = "talent_recruitment_process_id";

    public static final String TALENT_ID = "talent_id";
    public static final String JOB_ID = "job_id";
    public static final String COMPANY_ID = "company_id";
    public static final String LAST_SYNC_TIME = "last_sync_time";
    public static final String APPROVER_ID = "approver_id";
    public static final String LAST_MODIFIED_DATE = "last_modified_date";
    public static final Integer ERROR_THRESHOLD = 5;
    public static final String JOB_IPG_RELATION = "job_ipg_relation";
    public static final String APN_JOB_ID = "apn_job_id";
    public static final String IS_NEED_SYNC_HR = "is_need_sync_hr";
    public static final String COMPANY_NAME = "full_business_name";
    public static final String JOB_TITLE = "title";
    public static final String JOB_STATUS = "status";
    public static final String AGENCY_ID = "agency_id";

    @Resource
    private CanalProperties canalProperties;

    @Resource
    EsRecordService esRecordService;


    @Resource
    private DisruptorQueue disruptorQueue;

    @PostConstruct
    public void start() {
        TALENT = canalProperties.getTalentTable();
        TALENT_RELATED_TABLES = Arrays.stream(canalProperties.getTalentRelatedTables().split(",")).collect(Collectors.toSet());
        TALENT_RECORD_TABLES = Arrays.stream(canalProperties.getTalentRecordTables().split(",")).collect(Collectors.toSet());
        JOB = canalProperties.getJobTable();
        JOB_RELATED_TABLES = Arrays.stream(canalProperties.getJobRelatedTables().split(",")).collect(Collectors.toSet());
        JOB_RECORD_TABLES = Arrays.stream(canalProperties.getJobRecordTables().split(",")).collect(Collectors.toSet());
        COMPANY = canalProperties.getCompanyTable();
        COMPANY_RELATED_TABLES = Arrays.stream(canalProperties.getCompanyRelatedTables().split(",")).collect(Collectors.toSet());
        COMPANY_CLIENT_NOTE = canalProperties.getCompanyClientNoteTable();
        COMPANY_PROGRESS_NOTE = canalProperties.getCompanyProgressNoteTable();
        COMPANY_CONTACT = canalProperties.getCompanyContactTable();
        ASSIGMENT_RECORD_TABLES = Arrays.stream(canalProperties.getAssigmentRecordTables().split(",")).collect(Collectors.toSet());
        AGENCY_JOB_RELATED_TABLES = Arrays.stream(canalProperties.getAgencyJobRelatedTables().split(",")).collect(Collectors.toSet());
        AGENCY = canalProperties.getAgencyTable();
        AGENCY_RELATED_TABLES = Arrays.stream(canalProperties.getAgencyRelatedTables().split(",")).collect(Collectors.toSet());
        Thread thread = new Thread(() -> main());
        thread.start();
    }

    private void main() {
        initEsSchema();
        CanalConnector connector = null;
        int batchSize = 200;
        int count = 0;
        int frequency = canalProperties.getFrequency();
        int logInterval = (int) ((1000D / frequency) * 600);
        logInterval = Math.max(logInterval, 100);
        int errors = 0;
        int countLarkAlerts = 0;
        while (true) {
            try {
                connector = CanalConnectors.newSingleConnector(new InetSocketAddress(canalProperties.getApnIp(),
                        canalProperties.getApnPort()), canalProperties.getInstance(), "", "");
                connector.connect();
                connector.subscribe(canalProperties.getSubscribe());
                connector.rollback();
                int sendFail = 0;
                while (true) {
                    Message message = connector.getWithoutAck(batchSize);
                    long batchId = message.getId();
                    int size = message.getEntries().size();
                    if (batchId == -1 || size == 0) {
                        //sync(connector, true);
                        if (count % logInterval == 0) {
                            log.info("fetching data by canal...");
                            count = 0;
                        }
                        count += 1;
                        try {
                            Thread.sleep(frequency);
                        } catch (InterruptedException e) {

                        }
                    } else {
                        log.info("get data from canal, batchId: {}, size: {}, skip binlog: {}", batchId, size, canalProperties.isSkipBinlogChange());
                        if (canalProperties.isSkipBinlogChange()) {
                            connector.ack(batchId);
                            continue;
                        }
                        log.info("after binlog");
                        try {
                            List<Entry> entries = message.getEntries();
                            //main data sync
                            List<EventContent> eventContents = extractEvents(entries);
                            boolean published = disruptorQueue.publish(batchId, eventContents, connector);
                            if (published) {
                                sendFail = 0;
                            } else {
                                try {
                                    // 最长等待30s
                                    if (sendFail < 60) {
                                        sendFail++;
                                    } else {
                                        sendFail = 1;
                                    }
                                    log.info("disruptor publish failed : {}, current buffer size: {}, remainingCapacity: {}", sendFail, disruptorQueue.bufferSize(), disruptorQueue.remainingCapacity());
                                    Thread.sleep(sendFail * 500L);
                                    if (sendFail % ERROR_THRESHOLD * 2 == 0) {
                                        log.error("disruptor queue is full, retry later...");
                                        NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "WARN: Disruptor queue is full, please go check!");
                                    }
                                } catch (InterruptedException ignored) {

                                }
                            }
                        } catch (Exception e) {
                            log.error(e.getMessage());
//                            rollback(connector);
                            continue;
                        }
                    }
                    countLarkAlerts = 0;
                    errors = 0;
                }
            } catch (Exception e) {
                log.error("canal exception: {}", ExceptionUtils.getStackTrace(e));
                errors++;
                log.info("Canal errors: {}", errors);
                try {
                    Thread.sleep(errors * 500);
                    if (errors % ERROR_THRESHOLD == 0) {
                        NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), ExceptionUtils.getStackTrace(e));
                    }
                    if (errors % (ERROR_THRESHOLD * 2) == 0) {
                        NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "Canal server is abnormal, please go check!");
                        countLarkAlerts++;
                        log.info("Canal countLarkAlerts: {}", countLarkAlerts);
                        Thread.sleep(canalProperties.getSleepMillis() * countLarkAlerts);
                    }
                    if (connector != null) {
                        connector.disconnect();
                    }
                } catch (InterruptedException ie) {
                    continue;
                }
            }
        }
    }

    private void initEsSchema() {
        esRecordService.createPolicy();
        esRecordService.createTalentTemplate();
        esRecordService.createJobTemplate();
        esRecordService.createJobSnapshotTemplate();
        esRecordService.createAssignmentTemplate();
    }

    private List<EventContent> extractEvents(List<Entry> entries) {
        return entries.stream()
                .filter(entry -> entry.getEntryType() != EntryType.TRANSACTIONBEGIN && entry.getEntryType() != EntryType.TRANSACTIONEND)
                .map(entry -> {
                    try {
                        RowChange rowChange = RowChange.parseFrom(entry.getStoreValue());
                        return new EventContent(entry.getHeader().getTableName().toLowerCase(), rowChange);
                    } catch (Exception e) {
                        throw new RuntimeException("ERROR ## parser of eromanga-event has an error , data:" + entry, e);
                    }
                }).toList();
    }



    /**
     * For testing Canal instances
     *
     * @param args
     */
    public static void main(String[] args) {
        CanalConnector connector = null;
        try {
            connector = CanalConnectors.newSingleConnector(new InetSocketAddress("localhost",
                    11111), "apn", "", "");
            connector.connect();
            connector.subscribe("apnv3.*");
//            connector.rollback();
            while (true) {
                Message message = connector.getWithoutAck(10);
                long batchId = message.getId();
                List<Entry> entries = message.getEntries();
                int size = entries.size();
                if (batchId > 0) {
                    System.out.println(batchId + " " + size);
                }
                for (Entry entry : entries) {
                    if (entry.getEntryType() == EntryType.TRANSACTIONBEGIN || entry.getEntryType() == EntryType.TRANSACTIONEND) {
                        continue;
                    }
                    RowChange rowChange = null;
                    try {
                        rowChange = RowChange.parseFrom(entry.getStoreValue());
                    } catch (Exception e) {
                        throw new RuntimeException("ERROR ## parser of eromanga-event has an error , data:" + entry.toString(),
                                e);
                    }
                    String tableName = entry.getHeader().getTableName().toLowerCase();
                    System.out.println("table " + tableName + " changed");
                    for (RowData rowData : rowChange.getRowDatasList()) {
                        List<Column> columns = rowData.getAfterColumnsList();
                        for (Column column : columns) {
                            String name = column.getName();
                            System.out.println(String.format("column: %s, value: %s, updated: %s", name, column.getValue(), column.getUpdated()));
                        }
                    }
                }
                connector.ack(batchId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
