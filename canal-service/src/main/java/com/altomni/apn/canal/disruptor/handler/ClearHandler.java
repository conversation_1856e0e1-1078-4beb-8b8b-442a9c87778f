package com.altomni.apn.canal.disruptor.handler;

import com.altomni.apn.canal.disruptor.CanalEvent;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;



@Slf4j
@Component
public class ClearHandler implements EventHandler<CanalEvent> {


    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) {
        event.ack();
        event.clear();
    }
}
