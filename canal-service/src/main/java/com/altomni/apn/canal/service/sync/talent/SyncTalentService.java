package com.altomni.apn.canal.service.sync.talent;

import org.springframework.data.domain.Pageable;

import java.util.Collection;

public interface SyncTalentService {

    void synchronizeTalents(Collection<Long> talentIds, int priority, int deep);

    void validateSql(String sql);

    void bulkSyncTalents(String sql);

    void bulkSyncAllTalents(Pageable pageable);

    boolean isTalentQueueAvailable();
}
