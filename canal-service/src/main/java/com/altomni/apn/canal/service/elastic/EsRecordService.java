package com.altomni.apn.canal.service.elastic;

import cn.hutool.json.JSONObject;

import java.util.List;
import java.util.Map;

public interface EsRecordService {

    void batchSaveJobUpdateToEs(Map<Long, List<JSONObject>> syncJobRecords);

    void batchSaveTalentUpdateToEs(Map<Long, List<JSONObject>> syncTalentRecords);

    void batchSaveAssignmentUpdateToEs(Map<Long, List<JSONObject>> syncAssignmentRecords);

    void createTalentTemplate();

    void createPolicy();

    void createJobTemplate();

    void createJobSnapshotTemplate();

    void createAssignmentTemplate();
}
