package com.altomni.apn.canal.repository;

import com.altomni.apn.canal.repository.dto.TalentRecruitmentProcessToJobName;
import com.altomni.apn.common.domain.application.TalentRecruitmentProcessForCanal;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Repository
public interface TalentRecruitmentProcessRepository extends JpaRepository<TalentRecruitmentProcessForCanal, Long>{


    List<TalentRecruitmentProcessForCanal> findAllByIdIn(Collection<Long> recruitmentProcessIds);

    @Query("select new com.altomni.apn.canal.repository.dto.TalentRecruitmentProcessToJobName(trp.id, j.title) from TalentRecruitmentProcessForCanal trp LEFT JOIN JobV3 j on trp.jobId = j.id where trp.id in :recruitmentProcessIds")
    List<TalentRecruitmentProcessToJobName> findAllByRecruitmentProcessIdInWithJobName(@Param("recruitmentProcessIds") Collection<Long> recruitmentProcessIds);

    /**
     * 批量获取多个招聘流程的最大面试进度。
     *
     * @param talentRecruitmentProcessIds 多个招聘流程ID集合
     * @return 包含每个招聘流程ID及其最大面试进度的映射
     */
    @Query(value = """
            select t.talent_recruitment_process_id as talent_recruitment_process_id, max(t.progress) as max_progress
            from talent_recruitment_process_interview t
            where t.talent_recruitment_process_id in :talentRecruitmentProcessIds
            group by t.talent_recruitment_process_id
            """, nativeQuery = true)
    List<Map<String, Object>> getMaxInterviewProgresses(@Param("talentRecruitmentProcessIds") Collection<Long> talentRecruitmentProcessIds);

    // 辅助方法，用于将查询结果转换为Map
    default Map<Long, Long> getMaxInterviewProgressesAsMap(Collection<Long> talentRecruitmentProcessIds) {
        List<Map<String, Object>> resultList = getMaxInterviewProgresses(talentRecruitmentProcessIds);
        return resultList.stream()
                .collect(Collectors.toMap(
                        map -> {
                            Object value = map.get("talent_recruitment_process_id");
                            return parseLong(value);
                        },
                        map -> {
                            Object value = map.get("max_progress");
                            return parseLong(value);
                        },
                        (existing, replacement) -> existing));
    }

    private Long parseLong(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof BigInteger) {
            return ((BigInteger) value).longValue();
        } else if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        } else {
            return Long.parseLong(value.toString());
        }
    }
}
