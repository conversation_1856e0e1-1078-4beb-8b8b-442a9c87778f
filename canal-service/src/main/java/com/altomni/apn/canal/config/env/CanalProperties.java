package com.altomni.apn.canal.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class CanalProperties {

    @Value("${canal.talentTable}")
    private String talentTable;

    @Value("${canal.talentRelatedTables}")
    private String talentRelatedTables;

    @Value("${canal.talentRecordTables}")
    private String talentRecordTables;

    @Value("${canal.jobTable}")
    private String jobTable;

    @Value("${canal.jobRelatedTables}")
    private String jobRelatedTables;

    @Value("${canal.jobRecordTables}")
    private String jobRecordTables;

    @Value("${canal.assigmentRecordTables}")
    private String assigmentRecordTables;

    @Value("${canal.agencyJobRelatedTables}")
    private String agencyJobRelatedTables;

    @Value("${canal.companyTable}")
    private String companyTable;

    @Value("${canal.companyRelatedTables}")
    private String companyRelatedTables;

    @Value("${canal.companyClientNoteTable}")
    private String companyClientNoteTable;

    @Value("${canal.companyProgressNoteTable}")
    private String companyProgressNoteTable;

    @Value("${canal.companyContactTable}")
    private String companyContactTable;

    @Value("${canal.agencyTable}")
    private String agencyTable;

    @Value("${canal.agencyRelatedTables}")
    private String agencyRelatedTables;

    @Value("${canal.apnIp}")
    private String apnIp;

    @Value("${canal.apnPort}")
    private Integer apnPort;

    @Value("${canal.instance}")
    private String instance;

    @Value("${canal.subscribe:apnv3}")
    private String subscribe;

    @Value("${canal.skipBinlogChange:false}")
    private boolean skipBinlogChange;

    @Value("${canal.frequency}")
    private Integer frequency;

//    @Value("${canal.apn.loginUser}")
//    private String loginUser;
//
//    @Value("${canal.apn.loginPassword}")
//    private String loginPassword;

    @Value("${canal.apn.apiBase}")
    private String apiBase;

    @Value("${canal.sso.url_login}")
    private String ssoLoginUrl;

    @Value("${canal.sso.client_id}")
    private String ssoClientId;

    @Value("${canal.sso.client_secret}")
    private String ssoSecret;

    @Value("${canal.retryFailedBatchSize}")
    private int retryBatchSize;

    @Value("${canal.notification.lark.webhookKey}")
    private String webhookKey;

    @Value("${canal.notification.lark.webhookUrl}")
    private String webhookUrl;

    @Value("${canal.error.sleepMillis:300000}")
    private Integer sleepMillis;

    @Value("${application.elasticrecord.url}")
    private String recordUrl;

    @Value("${canal.retryFailedCountLimit:5}")
    private Integer failedCountLimit;

    @Value("${application.elasticrecord.talent-template}")
    private String talentTemplate;

    @Value("${application.elasticrecord.hot-cold-policy}")
    private String policy;

    @Value("${application.elasticrecord.job-template}")
    private String jobTemplate;

    @Value("${application.elasticrecord.job-snapshot-template}")
    private String jobSnapshotTemplate;

    @Value("${application.elasticrecord.assignment-template}")
    private String assignmentTemplate;
}
