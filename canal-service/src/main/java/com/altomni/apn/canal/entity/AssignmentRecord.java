package com.altomni.apn.canal.entity;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

@Data
public class AssignmentRecord {

    private Long assignmentId;

    private Long talentId;

    private Long tenantId;

    private Long jobId;

    private LocalDate assigmentStartDate;

    private AssignmentRecordType recordType;

    private String createdBy;

    private Instant createdDate;

    private List<JSONObject> changeFields = new ArrayList<>();


    public void addChangeField(List<JSONObject> changeFields) {
        if (changeFields == null || changeFields.isEmpty()) {
            return;
        }
        this.changeFields.addAll(changeFields);
    }

}
