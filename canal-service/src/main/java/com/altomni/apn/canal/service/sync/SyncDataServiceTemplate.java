package com.altomni.apn.canal.service.sync;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.repository.RepositoryCustom;
import com.altomni.apn.canal.service.login.LoginService;
import com.altomni.apn.canal.service.login.impl.LoginServiceImpl;
import com.altomni.apn.canal.web.rest.vm.MqMessageCountVM;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 同步批量刷数据模板方法
 */
@Slf4j
public abstract class SyncDataServiceTemplate {

    /**
     * lark配置
     */
    protected CanalProperties canalProperties;

    /**
     * rest ful
     */
    protected RestTemplate restTemplate = new RestTemplate();

    /**
     * 登录账号配置
     */
    protected LoginService loginService;

    /**
     * 直接执行db的厂库
     */
    protected RepositoryCustom repositoryCustom;

    /**
     * 模板对应的处理数据类型
     */
    protected SyncIdTypeEnum syncIdTypeEnum;

    /**
     * 失败的是存储数据用于重试
     */
    protected CanalService canalService;

    public SyncDataServiceTemplate(CanalProperties canalProperties,
                                   LoginService loginService,
                                   RepositoryCustom repositoryCustom,
                                   CanalService canalService,
                                   SyncIdTypeEnum syncIdTypeEnum) {
        this.canalProperties = canalProperties;
        this.loginService = loginService;
        this.repositoryCustom = repositoryCustom;
        this.canalService = canalService;
        this.syncIdTypeEnum = syncIdTypeEnum;
    }

    public void bulkSyncData(String sql) {
        try {
            Integer unfinished = bulkSyncData(sql, 50);
            syncFinish(unfinished);
        } catch (Exception e) {
            log.error("[bulkSync {} @-1] error: {} at: {}", syncIdTypeEnum, ExceptionUtil.stacktraceToString(e), System.currentTimeMillis());
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), String.format("synchronize " + syncIdTypeEnum + " error:  %s", ExceptionUtil.stacktraceToString(e)));
        }
    }

    private void syncFinish(Integer unfinished){
        if (unfinished == 0){
            log.info("bulkSync {} finished", syncIdTypeEnum);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "BulkSync" + syncIdTypeEnum + " finished.");
        }else{
            log.warn("bulkSync {} unfinished. Count: {}", syncIdTypeEnum, unfinished);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "BulkSync" + syncIdTypeEnum + " unfinished. Count: " + unfinished);
        }
    }

    protected Integer bulkSyncData(String sql, int pageSize) throws Exception {
        String startTime = DateUtil.getStandardCurrentTime();
        Long lastId = Long.MAX_VALUE;
        List<Long> Ids = repositoryCustom.getIdsBySql(String.format("%s and temp.id < %d order by temp.id desc limit %d", sql, lastId, pageSize));
        Long maxId = 0L;
        if (!CollectionUtils.isEmpty(Ids)) {
            maxId = Ids.get(0);
        }
        log.info(Ids+"");
        int checkDataQueueTimes = 1;
        while(!CollectionUtils.isEmpty(Ids)) {
            if (checkDataQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The " + syncIdTypeEnum.name() + " queue is abnormal for 30 minutes.");
            }
            if (!isDataQueueAvailable()){
                checkDataQueueTimes ++;
                Thread.sleep(5000);
                continue;
            }
            lastId = Ids.get(Ids.size() - 1);
            log.info("Last {} ID: {}", syncIdTypeEnum, lastId);
            doSynchronize(Ids, RabbitMqConstant.MESSAGE_PRIORITY_LOW, 0);
            Ids = repositoryCustom.getIdsBySql(String.format("%s and temp.id < %d order by temp.id desc limit %d", sql, lastId, pageSize));
            checkDataQueueTimes = 1;
        }
        while (!isDataQueueEmpty()) {
            checkDataQueueTimes ++;
            if (checkDataQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The " + syncIdTypeEnum.name() + " queue is abnormal for 30 minutes.");
            }
            Thread.sleep(5000);
        }
        Thread.sleep(60000);
        Long unfinished = repositoryCustom.getIdsBySql(String.format("%s and temp.id<= %d and (temp.last_sync_time is null or temp.last_sync_time < '%s')",
                sql.replaceFirst("temp.id", "count(temp.id)"), maxId, startTime)).get(0);
        return unfinished.intValue();
    }

    protected void doSynchronize(List<Long> ids, int priority, int deep) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("ids", ids);
        paramMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + getUriDataSynchronization();
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(paramMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronize {}  @-1] synchronize {} error, ids: {}, responseCode: {}, responseBody: {}",
                        syncIdTypeEnum, syncIdTypeEnum, ids, response.getStatusCode(), response.getBody());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(ids, syncIdTypeEnum, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "Synchronize " + syncIdTypeEnum + " error " +
                        "\n\tids : " + ids +
                        "\n\tResponse Code: " + response.getStatusCode() +
                        "\n\tResponse Body: " + response.getBody();
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        } catch (HttpClientErrorException e){
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                log.warn("[synchronize {} @{}] Access token expired. Error: {}", syncIdTypeEnum, SecurityUtils.getUserId(), ExceptionUtils.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    doSynchronize(ids, priority, deep + 1);
                } else {
                    canalService.insertAll(ids, syncIdTypeEnum, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronize " + syncIdTypeEnum + "] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            } else {
                canalService.insertAll(ids, syncIdTypeEnum, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronize {} @{}] Synchronize {} error. TalentIds: {}, error: {}", syncIdTypeEnum, SecurityUtils.getUserId(), syncIdTypeEnum, ids, ExceptionUtils.getStackTrace(e));
                String message = "Synchronize " + syncIdTypeEnum + " error " +
                        "\n\tids: " + ids +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        } catch (Exception e) {
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            canalService.insertAll(ids, syncIdTypeEnum, FailReasonEnum.ERROR, e.getMessage(), priority);
            log.error("[synchronize{} @{}] Synchronize {} error, ids: {}, error: {}", syncIdTypeEnum, SecurityUtils.getUserId(), syncIdTypeEnum, ids, com.alibaba.nacos.common.utils.ExceptionUtil.getStackTrace(e));
            String message = "Synchronize " + syncIdTypeEnum + " error " +
                    "\n\tids: " + ids +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    protected abstract String getUriDataMqMessageCountCheck();

    protected abstract String getUriDataSynchronization();

    private boolean isDataQueueEmpty() {
        return checkDataMessageCount().getMessageCount() == 0;
    }

    protected boolean isDataQueueAvailable(){
        try {
            final MqMessageCountVM messageCountVM = checkDataMessageCount();
            return Objects.nonNull(messageCountVM.getMessageCount())
                    && Objects.nonNull(messageCountVM.getMaximumMessageCount())
                    && messageCountVM.getMaximumMessageCount() > messageCountVM.getMessageCount();
        } catch (Exception e) {
            log.error("is {} QueueAvailable is error, message = {}", syncIdTypeEnum, ExceptionUtils.getStackTrace(e));
        }
        return false;
    }

    private MqMessageCountVM checkDataMessageCount() {
        String api = canalProperties.getApiBase() + getUriDataMqMessageCountCheck();
        HttpHeaders headers = loginService.buildHeaders();
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(headers);
        ResponseEntity<MqMessageCountVM> response = this.restTemplate.postForEntity(api, entity, MqMessageCountVM.class);
        MqMessageCountVM messageCountVM = response.getBody();
        log.info("check {} MessageCount: {}", syncIdTypeEnum, messageCountVM);
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED
                && messageCountVM.getError() != null
                && messageCountVM.getError().contains("invalid_token")) {
            log.warn("[check {} MessageCount @-1] Access token expired.", syncIdTypeEnum);
            // clean the token
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            return checkDataMessageCount();
        }
        return messageCountVM;
    }

}
