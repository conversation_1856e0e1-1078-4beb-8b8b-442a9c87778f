package com.altomni.apn.canal.disruptor.handler;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.entity.AssignmentOperation;
import com.altomni.apn.canal.entity.AssignmentRecord;
import com.altomni.apn.canal.entity.AssignmentRecordType;
import com.altomni.apn.canal.repository.AssignmentRepository;
import com.altomni.apn.canal.service.changerecord.assignment.AssignmentChangeRecordService;
import com.altomni.apn.canal.service.changerecord.talent.impl.TalentChangeRecordServiceImpl;
import com.altomni.apn.canal.service.elastic.EsRecordService;
import com.altomni.apn.common.domain.talent.TalentAssigment;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.altomni.apn.canal.service.canal.CanalClient.ASSIGMENT_RECORD_TABLES;

@Slf4j
@Component
public class AssignmentRecordHandler implements EventHandler<CanalEvent> {
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
            .withZone(ZoneId.systemDefault());


    private final AssignmentChangeRecordService assignmentChangeRecordService;
    private final AssignmentRepository assignmentRepository;
    private final EsRecordService esRecordService;

    public AssignmentRecordHandler(AssignmentChangeRecordService assignmentChangeRecordService,
                                   AssignmentRepository assignmentRepository, EsRecordService esRecordService) {
        this.assignmentChangeRecordService = assignmentChangeRecordService;
        this.assignmentRepository = assignmentRepository;
        this.esRecordService = esRecordService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> AssignmentRecordHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Map<Long, Map<AssignmentRecordType, AssignmentRecord>> changeRecordMap = new LinkedHashMap<>();
        try {
            List<EventContent> assignmentTableEvents = eventContents.stream()
                    .filter(eventContent -> ASSIGMENT_RECORD_TABLES.contains(eventContent.tableName()))
                    .toList();
            if (assignmentTableEvents.isEmpty()) {
                return;
            }
            Set<Long> assignmentIds = assignmentTableEvents.stream().flatMap(eventContent -> {
                String tableName = eventContent.tableName();
                CanalEntry.RowChange rowChange = eventContent.rowChange();
                CanalEntry.EventType eventType = rowChange.getEventType();
                return rowChange.getRowDatasList().stream()
                        .map(rowData -> assignmentChangeRecordService.recordAssignmentId(rowData.getBeforeColumnsList(), rowData.getAfterColumnsList(), eventType, tableName));
            }).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toSet());

            if (assignmentIds.isEmpty()) {
                return;
            }

            Map<Long, TalentAssigment> assigmentMap = assignmentRepository.findAllById(assignmentIds).stream().collect(Collectors.toMap(TalentAssigment::getId, Function.identity()));

            assignmentTableEvents.forEach(eventContent -> {
                String tableName = eventContent.tableName();
                CanalEntry.RowChange rowChange = eventContent.rowChange();
                CanalEntry.EventType eventType = rowChange.getEventType();
                log.info("[APN: AssigmentRecordHandler] record assigment, tableName: {}", tableName);

                List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();
                parseAssignmentRecordMap(tableName, eventType, rowDataList, assigmentMap, changeRecordMap);
            });

            log.info("APN RecordAssigment: {}", JSONUtil.toJsonStr(changeRecordMap));

            if (changeRecordMap.isEmpty()) {
                return;
            }

            syncAssignmentRecord(changeRecordMap);
        } catch (Exception e) {
            log.error("APN RecordAssigment: failed to process assigment record", e);
            changeRecordMap.values().stream().flatMap(map -> map.values().stream()).filter(Objects::nonNull).forEach(assignmentRecord -> {
                log.error("APN RecordAssigment: failed process assigment Id: {} talent Id:{}", assignmentRecord.getAssignmentId(), assignmentRecord.getTalentId(), e);
            });
        }
    }

    private void syncAssignmentRecord(Map<Long, Map<AssignmentRecordType, AssignmentRecord>> changeRecordMap) {
        Map<Long, List<AssignmentRecord>> tenantRecords = changeRecordMap.values().stream()
                .flatMap(map -> map.values().stream())
                .filter(Objects::nonNull)
                .filter(record -> !record.getChangeFields().isEmpty())
                .collect(Collectors.groupingBy(AssignmentRecord::getTenantId, Collectors.toList()));

        Map<Long, List<JSONObject>> tenantJsonRecords = new HashMap<>();
        tenantRecords.forEach((tenantId, records) -> {
            List<JSONObject> jsonRecords = records.stream().map(record -> {
                JSONObject json = new JSONObject();
                json.put("@timestamp", record.getCreatedDate());
                json.put("assignmentId", record.getAssignmentId());
//                json.put("talentId", record.getTalentId());
//                json.put("jobId", record.getJobId());
//                json.put("assigmentStartDate", record.getAssigmentStartDate());
                json.put("recordType", record.getRecordType().name());
                json.put("createdDate", record.getCreatedDate().toString());
                json.put("createdBy", record.getCreatedBy());
                json.put("changeFields", record.getChangeFields());
                String operation = record.getChangeFields().get(0).get("eventType").toString();
                if (operation.equals(AssignmentOperation.CREATE.name())) {
                    json.put("operationType", AssignmentOperation.CREATE);
                } else if (operation.equals(AssignmentOperation.APPROVED.name())) {
                    json.put("operationType", AssignmentOperation.APPROVED);
                } else if (operation.equals(AssignmentOperation.UNAPPROVED.name())) {
                    json.put("operationType", AssignmentOperation.UNAPPROVED);
                } else if (operation.equals(AssignmentOperation.UPDATE.name())) {
                    json.put("operationType", AssignmentOperation.UPDATE);
                }
                return json;
            }).toList();
            tenantJsonRecords.put(tenantId, jsonRecords);
        });
        // 重新组装数据
        Map<Long, List<JSONObject>> finalTenantJsonRecords = assembleJsonRecords(tenantJsonRecords);

        esRecordService.batchSaveAssignmentUpdateToEs(finalTenantJsonRecords);
    }

    private Map<Long, List<JSONObject>> assembleJsonRecords(Map<Long, List<JSONObject>> tenantRecords) {
        Map<Long, List<JSONObject>> finalRecords = new LinkedHashMap<>();
        Predicate<JSONObject> isCreateAssignment = record -> {
            String recordType = record.getStr("recordType");
            String operationType = record.getStr("operationType");
            return AssignmentOperation.CREATE.name().equals(operationType) && recordType.equals(AssignmentRecordType.ASSIGNMENT.name());
        };
        // 如果是创建 assignment，只保留创建 assignment 的记录
        tenantRecords.forEach((tenantId, records) -> {
            Optional<String> createdAssignment = records.stream().filter(isCreateAssignment).map(record -> record.getStr("assignmentId")).findAny();

            if (createdAssignment.isEmpty()) {
                finalRecords.put(tenantId, records);
            } else {
                List<JSONObject> createdRecord = records.stream().filter(isCreateAssignment).toList();
                finalRecords.put(tenantId, createdRecord);
            }
        });

        // 更新 bill 或者  pay 信息的 currency 时，只保留一个 currency 记录，因为页面联动，没必要多次记录
        List<String> currencyKey = List.of("billCurrency", "payCurrency");
        Predicate<JSONObject> isUpdateBillCurrency = record -> {
            String operationType = record.getStr("operationType");
            JSONArray changeFields = record.getJSONArray("changeFields");
            return AssignmentOperation.UPDATE.name().equals(operationType)
                    && changeFields.stream().map(obj -> (JSONObject) obj).anyMatch(json -> currencyKey.contains(json.getStr("key")));
        };
        finalRecords.forEach((tenantId, records) -> {
            records.stream().filter(isUpdateBillCurrency).forEach(updateCurrency -> {
                JSONArray changeFields = updateCurrency.getJSONArray("changeFields");
                Iterator<Object> iterator = changeFields.iterator();
                boolean first = true;
                while (iterator.hasNext()) {
                    JSONObject json = (JSONObject) iterator.next();
                    if (currencyKey.contains(json.getStr("key"))) {
                        if (first) {
                            first = false;
                        } else {
                            iterator.remove();
                        }
                    }
                }
            });
        });
        return finalRecords;
    }

    private void parseAssignmentRecordMap(String tableName, CanalEntry.EventType eventType, List<CanalEntry.RowData> rowDataList,
                                          Map<Long, TalentAssigment> assigmentRecordMap, Map<Long, Map<AssignmentRecordType, AssignmentRecord>> changeRecordMap) {
        for (CanalEntry.RowData rowData : rowDataList) {

            List<CanalEntry.Column> beforeColumnsList = rowData.getBeforeColumnsList();
            List<CanalEntry.Column> afterColumnsList = rowData.getAfterColumnsList();
            Optional<Long> assignmentIdOpt = assignmentChangeRecordService.recordAssignmentId(beforeColumnsList, afterColumnsList, eventType, tableName);
            if (assignmentIdOpt.isEmpty()) {
                continue;
            }
            Long assignmentId = assignmentIdOpt.get();
            switch (eventType) {
                case INSERT -> {
                    boolean create = assignmentChangeRecordService.beCreate(afterColumnsList, tableName);
                    if (create) {
                        List<JSONObject> changeFields = assignmentChangeRecordService.parseAssignmentCreate(afterColumnsList, tableName);
                        fillRecordMap(assignmentId, changeFields, AssignmentRecordType.ASSIGNMENT, assigmentRecordMap, changeRecordMap);
                    } else {
                        List<JSONObject> approverChanges = assignmentChangeRecordService.parseApprover(beforeColumnsList, afterColumnsList, eventType, tableName);
                        fillRecordMap(assignmentId, approverChanges, AssignmentRecordType.BILL, assigmentRecordMap, changeRecordMap);
                        List<JSONObject> contractorChanges = assignmentChangeRecordService.parseContribution(beforeColumnsList, afterColumnsList, eventType, tableName);
                        fillRecordMap(assignmentId, contractorChanges, AssignmentRecordType.BILL, assigmentRecordMap, changeRecordMap);
                    }
                }
                case DELETE -> {
                    List<JSONObject> approverChanges = assignmentChangeRecordService.parseApprover(beforeColumnsList, afterColumnsList, eventType, tableName);
                    fillRecordMap(assignmentId, approverChanges, AssignmentRecordType.BILL, assigmentRecordMap, changeRecordMap);
                    List<JSONObject> contractorChanges = assignmentChangeRecordService.parseContribution(beforeColumnsList, afterColumnsList, eventType, tableName);
                    fillRecordMap(assignmentId, contractorChanges, AssignmentRecordType.BILL, assigmentRecordMap, changeRecordMap);
                }
                case UPDATE -> {
                    List<JSONObject> approverChanges = assignmentChangeRecordService.parseApprover(beforeColumnsList, afterColumnsList, eventType, tableName);
                    fillRecordMap(assignmentId, approverChanges, AssignmentRecordType.BILL, assigmentRecordMap, changeRecordMap);
                    List<JSONObject> assignmentChanges = assignmentChangeRecordService.parseAssignmentUpdate(beforeColumnsList, afterColumnsList, tableName);
                    fillRecordMap(assignmentId, assignmentChanges, AssignmentRecordType.ASSIGNMENT, assigmentRecordMap, changeRecordMap);
                    List<JSONObject> billChanges = assignmentChangeRecordService.parseAssignmentBillInfoUpdate(beforeColumnsList, afterColumnsList, tableName);
                    fillRecordMap(assignmentId, billChanges, AssignmentRecordType.BILL, assigmentRecordMap, changeRecordMap);
                    List<JSONObject> locationChanges = assignmentChangeRecordService.parseAssignmentLocationUpdate(beforeColumnsList, afterColumnsList, tableName);
                    fillRecordMap(assignmentId, locationChanges, AssignmentRecordType.BILL, assigmentRecordMap, changeRecordMap);
                    List<JSONObject> payChanges = assignmentChangeRecordService.parseAssignmentPayInfoUpdate(beforeColumnsList, afterColumnsList, tableName);
                    fillRecordMap(assignmentId, payChanges, AssignmentRecordType.PAY, assigmentRecordMap, changeRecordMap);
                    List<JSONObject> timesheetChanges = assignmentChangeRecordService.parseAssignmentTimesheetUpdate(beforeColumnsList, afterColumnsList, tableName);
                    fillRecordMap(assignmentId, timesheetChanges, AssignmentRecordType.BILL, assigmentRecordMap, changeRecordMap);
                    List<JSONObject> contributionChanges = assignmentChangeRecordService.parseContribution(beforeColumnsList, afterColumnsList, eventType, tableName);
                    fillRecordMap(assignmentId, contributionChanges, AssignmentRecordType.BILL, assigmentRecordMap, changeRecordMap);
                }
            }
        }
    }

    private void fillRecordMap(Long assignmentId, List<JSONObject> changeFields, AssignmentRecordType recordType,
                               Map<Long, TalentAssigment> assigmentMap,
                               Map<Long, Map<AssignmentRecordType, AssignmentRecord>> changeRecordMap) {
        if (!assigmentMap.containsKey(assignmentId) || changeFields.isEmpty()) {
            return;
        }
        TalentAssigment talentAssigment = assigmentMap.get(assignmentId);
        if (talentAssigment == null) {
            return;
        }
        AssignmentRecord record;
        if (changeRecordMap.containsKey(assignmentId) && changeRecordMap.get(assignmentId).containsKey(recordType)) {
            record = changeRecordMap.get(assignmentId).get(recordType);
        } else {
            record = toRecord(talentAssigment);
        }
        record.addChangeField(changeFields);
        record.setRecordType(recordType);

        changeRecordMap.computeIfAbsent(assignmentId, k -> new LinkedHashMap<>());
        changeRecordMap.get(assignmentId).put(recordType, record);
    }

    private AssignmentRecord toRecord(TalentAssigment talentAssigment) {

        AssignmentRecord record = new AssignmentRecord();
        record.setAssignmentId(talentAssigment.getId());
        record.setTalentId(talentAssigment.getTalentId());
        record.setTenantId(talentAssigment.getTenantId());
        record.setJobId(talentAssigment.getJobId());
        record.setAssigmentStartDate(talentAssigment.getStartDate());
        record.setCreatedBy(getUserIdFromCreatedBy(talentAssigment.getLastModifiedBy()));
        Instant now = Instant.now();
        String formatted = formatter.format(now);
        //TODO canal时间获取后就给加了时区时间，暂未解决，先用日志创建时候的时间
        Instant timestamp = TalentChangeRecordServiceImpl.formatTime(formatted);
        record.setCreatedDate(timestamp);
        return record;
    }

    private String getUserIdFromCreatedBy(String createdBy) {
        if (StringUtils.isNotEmpty(createdBy)) {
            String[] idStrings = createdBy.split(",");
            if (idStrings.length == 2) {
                return idStrings[0];
            } else {
                return createdBy;
            }
        }
        return "unknown";
    }
}
