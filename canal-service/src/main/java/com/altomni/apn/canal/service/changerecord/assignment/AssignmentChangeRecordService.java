package com.altomni.apn.canal.service.changerecord.assignment;

import cn.hutool.json.JSONObject;
import com.alibaba.otter.canal.protocol.CanalEntry;

import java.util.List;
import java.util.Optional;

public interface AssignmentChangeRecordService {

    Optional<Long> recordAssignmentId(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, CanalEntry.EventType eventType, String tableName);

    boolean beCreate(List<CanalEntry.Column> afterColumnsList, String tableName);

    List<JSONObject> parseAssignmentCreate(List<CanalEntry.Column> afterColumnsList, String tableName);

    List<JSONObject> parseApprover(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, CanalEntry.EventType eventType, String tableName);

    List<JSONObject> parseContribution(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, CanalEntry.EventType eventType, String tableName);

    List<JSONObject> parseAssignmentUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName);

    List<JSONObject> parseAssignmentBillInfoUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName);

    List<JSONObject> parseAssignmentLocationUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName);

    List<JSONObject> parseAssignmentPayInfoUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName);

    List<JSONObject> parseAssignmentTimesheetUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName);
}
