package com.altomni.apn.canal.disruptor;

import com.alibaba.otter.canal.client.CanalConnector;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CanalEvent {

    private Long batchId;

    private CanalConnector connector;

    private List<EventContent> eventContents;

    public void ack() {
        if (connector!= null) {
            connector.ack(batchId);
        }
    }

    public void clear() {
        this.batchId = null;
        this.eventContents = null;
        this.connector = null;
    }
}
