package com.altomni.apn.canal.service.changerecord.assignment.impl;

import cn.hutool.json.JSONObject;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.entity.AssignmentOperation;
import com.altomni.apn.canal.service.changerecord.assignment.AssignmentChangeRecordService;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.jobdiva.*;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class AssignmentChangeRecordServiceImpl implements AssignmentChangeRecordService {

    private static final String ID = "id";
    private static final String ASSIGNMENT_TABLE = "timesheet_talent_assignment";
    private static final String ASSIGNMENT_ID = "assignment_id";

    private static final String ASSIGNMENT_BILL_INFO_TABLE = "assignment_bill_info";
    private static final String ASSIGNMENT_CONTRIBUTION_TABLE = "assignment_contribution";
    private static final String ASSIGNMENT_LOCATION_TABLE = "assignment_location";
    private static final String ASSIGNMENT_PAY_INFO_TABLE = "assignment_pay_info";
    private static final String ASSIGNMENT_PAY_RATE_TABLE = "assignment_pay_rate";
    private static final String ASSIGNMENT_TIMESHEET = "assignment_timesheet";
    private static final String TIMESHEET_MANAGER_TABLE = "timesheet_manager";

    private static final Set<String> ASSIGNMENT_TABLES = Set.of(
            ASSIGNMENT_TABLE,
            ASSIGNMENT_BILL_INFO_TABLE,
            ASSIGNMENT_CONTRIBUTION_TABLE,
            ASSIGNMENT_LOCATION_TABLE,
            ASSIGNMENT_PAY_INFO_TABLE,
            ASSIGNMENT_PAY_RATE_TABLE,
            ASSIGNMENT_TIMESHEET,
            TIMESHEET_MANAGER_TABLE
    );

    private static final String KEY = "key";
    private static final String EVENT_TYPE = "eventType";
    private static final String CHANGED_FROM = "changedFrom";
    private static final String CHANGED_TO = "changedTo";

    private static final Predicate<CanalEntry.Column> columnNotEmpty = column -> StringUtils.isNotBlank(column.getValue());
    private static final Predicate<CanalEntry.Column> columnUpdated = CanalEntry.Column::getUpdated;


    @Override
    public Optional<Long> recordAssignmentId(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, CanalEntry.EventType eventType, String tableName) {
        Predicate<CanalEntry.Column> columnNotEmpty = column -> StringUtils.isNotBlank(column.getValue());
        Predicate<CanalEntry.Column> idColumn = column -> {
            if (ASSIGNMENT_TABLE.equals(tableName)) {
                return ID.equals(column.getName());
            }
            if (ASSIGNMENT_TABLES.contains(tableName)) {
                return ASSIGNMENT_ID.equals(column.getName());
            }
            return false;
        };
        List<CanalEntry.Column> columnList = CanalEntry.EventType.DELETE.equals(eventType) ? beforeColumnsList : afterColumnsList;
        return columnList.stream()
                .filter(idColumn)
                .filter(columnNotEmpty)
                .findAny().map(column -> Long.parseLong(column.getValue()));
    }

    @Override
    public boolean beCreate(List<CanalEntry.Column> afterColumnsList, String tableName) {
        if (!ASSIGNMENT_TABLE.equals(tableName)) {
            return false;
        }

        return afterColumnsList.stream()
                .filter(column -> ID.equals(column.getName()))
                .anyMatch(column -> columnNotEmpty.and(columnUpdated).test(column));
    }

    @Override
    public List<JSONObject> parseAssignmentCreate(List<CanalEntry.Column> afterColumnsList, String tableName) {
        if (!beCreate(afterColumnsList, tableName)) {
            return List.of();
        }
        JSONObject doc = newChangeField("", AssignmentOperation.CREATE, "", "");
        return List.of(doc);
    }

    /**
     * approvers 只有添加和删除
     */
    @Override
    public List<JSONObject> parseApprover(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, CanalEntry.EventType eventType, String tableName) {
        if (!TIMESHEET_MANAGER_TABLE.equals(tableName)) {
            return List.of();
        }
        // 这张表只记录 approver， 不记录 am client 等角色的删除
        List<String> approveRole = List.of("0", "1");

        if (CanalEntry.EventType.INSERT.equals(eventType)) {
            // 添加的是 am, 忽略
            boolean addAm = afterColumnsList.stream()
                    .anyMatch(column -> "role".equals(column.getName()) && StringUtils.isNotBlank(column.getValue()) && !approveRole.contains(column.getValue()));
            if (addAm) {
                return List.of();
            }

            return afterColumnsList.stream()
                    .filter(column -> "client_id".equals(column.getName()) && StringUtils.isNotBlank(column.getValue()) && column.getUpdated())
                    .map(column -> newChangeField("approvers", AssignmentOperation.ADD, "", column.getValue())).toList();
        }
        if (CanalEntry.EventType.DELETE.equals(eventType)) {
            // 删除的是 am, 忽略
            boolean delAm = beforeColumnsList.stream()
                    .anyMatch(column -> "role".equals(column.getName()) && StringUtils.isNotBlank(column.getValue()) && !approveRole.contains(column.getValue()));
            if (delAm) {
                return List.of();
            }

            return beforeColumnsList.stream()
                    .filter(column -> "client_id".equals(column.getName()) && StringUtils.isNotBlank(column.getValue()))
                    .map(column -> newChangeField("approvers", AssignmentOperation.DELETE, column.getValue(), "")).toList();
        }
        if (CanalEntry.EventType.UPDATE.equals(eventType)) {
            boolean updateAm = beforeColumnsList.stream()
                    .anyMatch(column -> "role".equals(column.getName()) && StringUtils.isNotBlank(column.getValue()) && !approveRole.contains(column.getValue()));
            if (updateAm) {
                return List.of();
            }
            JSONObject oldJson = buildApprover(beforeColumnsList);
            JSONObject newJson = buildApprover(afterColumnsList);
            JSONObject changeField = newChangeField("approvers", AssignmentOperation.UPDATE, oldJson.toString(), newJson.toString());
            return List.of(changeField);
        }
        return List.of();
    }

    private JSONObject buildApprover(List<CanalEntry.Column> columnList) {
        Optional<String> roleOpt = columnList.stream()
                .filter(column -> "role".equals(column.getName()) && columnNotEmpty.test(column))
                .findFirst().map(CanalEntry.Column::getValue);
        Optional<String> clientIdOpt = columnList.stream()
                .filter(column -> "client_id".equals(column.getName()) && columnNotEmpty.test(column))
                .findFirst().map(CanalEntry.Column::getValue);
        JSONObject approver = new JSONObject();
        clientIdOpt.ifPresent(s -> approver.put("clientId", s));
        roleOpt.ifPresent(s -> approver.put("role", convertEnum(s, ManagerRoleType.class)));
        return approver;
    }

    @Override
    public List<JSONObject> parseContribution(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, CanalEntry.EventType eventType, String tableName) {
        if (!ASSIGNMENT_CONTRIBUTION_TABLE.equals(tableName)) {
            return List.of();
        }
        switch (eventType) {
            case INSERT -> {
                JSONObject contribution = buildContribution(afterColumnsList);
                JSONObject changeField = newChangeField("contributions", AssignmentOperation.ADD, "", contribution.toString());
                return List.of(changeField);
            }
            case DELETE -> {
                JSONObject contribution = buildContribution(beforeColumnsList);
                JSONObject changeField = newChangeField("contributions", AssignmentOperation.DELETE, contribution.toString(), "");
                return List.of(changeField);

            }
            case UPDATE -> {
                JSONObject beforeContribution = buildContribution(beforeColumnsList);
                JSONObject afterContribution = buildContribution(afterColumnsList);
                JSONObject changeField = newChangeField("contributions", AssignmentOperation.UPDATE, beforeContribution.toString(), afterContribution.toString());
                return List.of(changeField);
            }
            default -> {
                return List.of();
            }
        }
    }

    private JSONObject buildContribution(List<CanalEntry.Column> columnList) {
        Optional<String> userIdOpt = columnList.stream()
                .filter(column -> "user_id".equals(column.getName()) && columnNotEmpty.test(column))
                .findFirst().map(CanalEntry.Column::getValue);
        Optional<String> userRoleOpt = columnList.stream()
                .filter(column -> "user_role".equals(column.getName()) && columnNotEmpty.test(column))
                .findFirst().map(CanalEntry.Column::getValue);
        Optional<String> percentageOpt = columnList.stream()
                .filter(column -> "percentage".equals(column.getName()) && columnNotEmpty.test(column))
                .findFirst().map(CanalEntry.Column::getValue);
        JSONObject contribution = new JSONObject();
        userIdOpt.ifPresent(s -> contribution.put("userName", s));
        userRoleOpt.ifPresent(s -> contribution.put("userRole", convertEnum(s, UserRole.class)));
        percentageOpt.ifPresent(s -> contribution.put("contributionPercentage", s));
        return contribution;
    }

    @Override
    public List<JSONObject> parseAssignmentUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName) {
        if (!ASSIGNMENT_TABLE.equals(tableName)) {
            return List.of();
        }
        Function<String, String> dateFormat = date -> {
            if (StringUtils.isBlank(date)) {
                return "";
            }
            try {
                return date.substring(0, 10);
            } catch (Exception e) {
                log.error("parse date error, date: {}, error: ", date, e);
                return "";
            }
        };
        List<JSONObject> changeFields = new ArrayList<>();
        for (int i = 0; i < afterColumnsList.size(); i++) {
            CanalEntry.Column beforeColumn = beforeColumnsList.get(i);
            CanalEntry.Column afterColumn = afterColumnsList.get(i);
            // 过滤未更新的字段
            if (!columnUpdated.test(afterColumn)) {
                continue;
            }

            String columnName = afterColumn.getName();
            switch (columnName) {
                case "start_date" ->
                        changeFields.add(newUpdateField("startDate", dateFormat.apply(beforeColumn.getValue()), dateFormat.apply(afterColumn.getValue())));
                case "end_date" ->
                        changeFields.add(newUpdateField("endDate", dateFormat.apply(beforeColumn.getValue()), dateFormat.apply(afterColumn.getValue())));
                case "status" -> {
                    AssignmentOperation assignmentOperation = null;
                    if (beforeColumn.getValue().equals("0") && afterColumn.getValue().equals("1")) {
                        assignmentOperation = AssignmentOperation.APPROVED;
                    }
                    if (beforeColumn.getValue().equals("1") && afterColumn.getValue().equals("0")) {
                        assignmentOperation = AssignmentOperation.UNAPPROVED;
                    }
                    if (assignmentOperation != null) {
                        changeFields.add(newChangeField("", assignmentOperation, "", ""));
                    }
                }
            }
        }
        return changeFields;
    }


    @Override
    public List<JSONObject> parseAssignmentBillInfoUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName) {
        if (!List.of(ASSIGNMENT_BILL_INFO_TABLE, ASSIGNMENT_PAY_RATE_TABLE).contains(tableName)) {
            return List.of();
        }
        List<JSONObject> changeFields = new ArrayList<>();
        if (ASSIGNMENT_BILL_INFO_TABLE.equals(tableName)) {
            for (int i = 0; i < afterColumnsList.size(); i++) {
                CanalEntry.Column beforeColumn = beforeColumnsList.get(i);
                CanalEntry.Column afterColumn = afterColumnsList.get(i);
                // 过滤未更新的字段
                if (!columnUpdated.test(afterColumn)) {
                    continue;
                }

                String columnName = afterColumn.getName();
                String beforeValue = beforeColumn.getValue();
                String afterValue = afterColumn.getValue();
                String fieldKey = switch (columnName) {
                    case "contact_id" -> "contact";
                    case "group_invoice_type" -> {
                        beforeValue = convertEnum(beforeValue, GroupInvoiceType.class);
                        afterValue = convertEnum(afterValue, GroupInvoiceType.class);
                        yield "groupInvoiceType";
                    }
                    case "group_invoice_content_type" -> {
                        beforeValue = convertEnum(beforeValue, GroupInvoiceContentType.class);
                        afterValue = convertEnum(afterValue, GroupInvoiceContentType.class);
                        yield "invoiceContentType";
                    }
                    case "is_except" -> "overtimeExempt";
                    case "overtime_type" -> {
                        beforeValue = convertEnum(beforeValue, OverTimeType.class);
                        afterValue = convertEnum(afterValue, OverTimeType.class);
                        yield "overTimeType";
                    }
                    case "expense_invoice" -> {
                        beforeValue = convertEnum(beforeValue, ExpenseInvoiceType.class);
                        afterValue = convertEnum(afterValue, ExpenseInvoiceType.class);
                        yield "expenseInvoice";
                    }
                    case "assignment_division" -> {
                        beforeValue = convertEnum(beforeValue, AssignmentDivision.class);
                        afterValue = convertEnum(afterValue, AssignmentDivision.class);
                        yield "assignmentDivision";
                    }
                    case "discount_type" -> {
                        beforeValue = convertEnum(beforeValue, DiscountType.class);
                        afterValue = convertEnum(afterValue, DiscountType.class);
                        yield "discountType";
                    }
                    case "payment_terms" -> "paymentTerms";
                    case "net_bill_Rate" -> "netBillRate";
                    case "net_overtime_rate" -> "netOverTimeRate";
                    case "net_doubletime_rate" -> "netDoubleTimeRate";
                    default -> "";
                };
                if (StringUtils.isBlank(fieldKey)) {
                    continue;
                }
                switch (fieldKey) {
                    case "netOverTimeRate", "netDoubleTimeRate" -> {
                        // 这两个字段从 Null  更新为 0.0, 不记录日志
                        if (StringUtils.isBlank(beforeValue) && "0.0".equals(afterValue)) {
                            continue;
                        } else {
                            changeFields.add(newUpdateField(fieldKey, beforeValue, afterValue));
                        }
                    }
                    case "overtimeExempt" ->
                            changeFields.add(newUpdateField(fieldKey, convertBool(beforeValue), convertBool(afterValue)));
                    default -> changeFields.add(newUpdateField(fieldKey, beforeValue, afterValue));
                }
            }
        }
        if (ASSIGNMENT_PAY_RATE_TABLE.equals(tableName)) {
            changeFields.addAll(parseAssignmentPayRateUpdate(beforeColumnsList, afterColumnsList, true));
        }
        return changeFields;
    }

    @Override
    public List<JSONObject> parseAssignmentLocationUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName) {
        if (!ASSIGNMENT_LOCATION_TABLE.equals(tableName)) {
            return List.of();
        }
        List<JSONObject> changeFields = new ArrayList<>();
        List<String> location_column = List.of("city", "province", "country");

        Function<List<CanalEntry.Column>, String> toLocation = columns -> columns.stream().map(CanalEntry.Column::getValue)
                .filter(location_column::contains)
                .collect(Collectors.joining(","));
        String beforeLocation = toLocation.apply(beforeColumnsList);
        String afterLocation = toLocation.apply(afterColumnsList);
        if (!beforeLocation.equals(afterLocation)) {
            changeFields.add(newUpdateField("workLocation", beforeLocation, afterLocation));
        }

        for (int i = 0; i < afterColumnsList.size(); i++) {
            CanalEntry.Column beforeColumn = beforeColumnsList.get(i);
            CanalEntry.Column afterColumn = afterColumnsList.get(i);
            // 过滤未更新的字段
            if (!columnUpdated.test(afterColumn)) {
                continue;
            }

            String columnName = afterColumn.getName();
            switch (columnName) {
                case "detailed_address" ->
                        changeFields.add(newUpdateField("detailedAddress", beforeColumn.getValue(), afterColumn.getValue()));
                case "zip_code" ->
                        changeFields.add(newUpdateField("zipCode", beforeColumn.getValue(), afterColumn.getValue()));
            }
        }
        return changeFields;
    }

    @Override
    public List<JSONObject> parseAssignmentPayInfoUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName) {
        if (!List.of(ASSIGNMENT_PAY_INFO_TABLE, ASSIGNMENT_PAY_RATE_TABLE).contains(tableName)) {
            return List.of();
        }
        List<JSONObject> changeFields = new ArrayList<>();
        if (ASSIGNMENT_PAY_INFO_TABLE.equals(tableName)) {
            for (int i = 0; i < afterColumnsList.size(); i++) {
                CanalEntry.Column beforeColumn = beforeColumnsList.get(i);
                CanalEntry.Column afterColumn = afterColumnsList.get(i);
                // 过滤未更新的字段
                if (!columnUpdated.test(afterColumn)) {
                    continue;
                }

                String columnName = afterColumn.getName();
                switch (columnName) {
                    case "employment_category" ->
                            changeFields.add(newUpdateField("employmentCategory", convertEnum(beforeColumn.getValue(), EmploymentCategoryType.class), convertEnum(afterColumn.getValue(), EmploymentCategoryType.class)));
                    case "is_except" ->
                            changeFields.add(newUpdateField("overtimeExempt", convertBool(beforeColumn.getValue()), convertBool(afterColumn.getValue())));
                    case "frequency" ->
                            changeFields.add(newUpdateField("frequency", convertEnum(beforeColumn.getValue(), TimeSheetFrequencyType.class), convertEnum(afterColumn.getValue(), TimeSheetFrequencyType.class)));
                    case "corporation" ->
                            changeFields.add(newUpdateField("corporation", beforeColumn.getValue(), afterColumn.getValue()));
                    case "comments" ->
                            changeFields.add(newUpdateField("payComments", beforeColumn.getValue(), afterColumn.getValue()));
                }
            }
        }
        if (ASSIGNMENT_PAY_RATE_TABLE.equals(tableName)) {
            changeFields.addAll(parseAssignmentPayRateUpdate(beforeColumnsList, afterColumnsList, false));
        }
        return changeFields;
    }

    public List<JSONObject> parseAssignmentPayRateUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, boolean bill) {
        List<JSONObject> changeFields = new ArrayList<>();
        boolean inBill = afterColumnsList.stream()
                .filter(columnNotEmpty)
                .filter(column -> "content_type".equals(column.getName()))
                .anyMatch(column -> "0".equals(column.getValue()));

        if (bill && !inBill) {
            return List.of();
        }
        if (!bill && inBill) {
            return List.of();
        }

        String payType = afterColumnsList.stream()
                .filter(columnNotEmpty)
                .filter(column -> "pay_type".equals(column.getName()))
                .map(CanalEntry.Column::getValue)
                .findAny().orElse("");
        Supplier<String> billRateKey = () -> {
            if (PayRateType.BILL_RATE.toDbValue().toString().equals(payType)) {
                return "billRate";
            }
            if (PayRateType.PAY_RATE.toDbValue().toString().equals(payType)) {
                return "payRate";
            }
            if (PayRateType.OVER_TIME.toDbValue().toString().equals(payType)) {
                return inBill ? "overTimeBillRate" : "overTimePayRate";
            }
            if (PayRateType.DOUBLE_TIME.toDbValue().toString().equals(payType)) {
                return inBill ? "doubleTimeBillRate" : "doubleTimePayRate";
            }
            return "";
        };
        Supplier<String> timeUnitKey = () -> {
            if (PayRateType.BILL_RATE.toDbValue().toString().equals(payType)) {
                return "billTimeUnit";
            }
            if (PayRateType.PAY_RATE.toDbValue().toString().equals(payType)) {
                return "payTimeUnit";
            }
            if (PayRateType.OVER_TIME.toDbValue().toString().equals(payType)) {
                return inBill ? "overTimeBillTimeUnit" : "overTimePayRateTimeUnit";
            }
            if (PayRateType.DOUBLE_TIME.toDbValue().toString().equals(payType)) {
                return inBill ? "doubleTimeBillTimeUnit" : "doubleTimePayRateTimeUnit";
            }
            return "";

        };
        for (int i = 0; i < afterColumnsList.size(); i++) {
            CanalEntry.Column beforeColumn = beforeColumnsList.get(i);
            CanalEntry.Column afterColumn = afterColumnsList.get(i);
            // 过滤未更新的字段
            if (!columnUpdated.test(afterColumn)) {
                continue;
            }

            String columnName = afterColumn.getName();
            switch (columnName) {
                case "pay_rate" ->
                        changeFields.add(newUpdateField(billRateKey.get(), beforeColumn.getValue(), afterColumn.getValue()));
                case "currency" ->
                        changeFields.add(newUpdateField(inBill ? "billCurrency" : "payCurrency", beforeColumn.getValue(), afterColumn.getValue()));
                case "time_unit" ->
                        changeFields.add(newUpdateField(timeUnitKey.get(), convertEnum(beforeColumn.getValue(), RateUnitType.class), convertEnum(afterColumn.getValue(), RateUnitType.class)));
            }
        }
        return changeFields;
    }


    @Override
    public List<JSONObject> parseAssignmentTimesheetUpdate(List<CanalEntry.Column> beforeColumnsList, List<CanalEntry.Column> afterColumnsList, String tableName) {
        if (!List.of(ASSIGNMENT_TIMESHEET, ASSIGNMENT_TABLE).contains(tableName)) {
            return List.of();
        }
        List<JSONObject> changeFields = new ArrayList<>();
        for (int i = 0; i < afterColumnsList.size(); i++) {
            CanalEntry.Column beforeColumn = beforeColumnsList.get(i);
            CanalEntry.Column afterColumn = afterColumnsList.get(i);
            // 过滤未更新的字段
            if (!columnUpdated.test(afterColumn)) {
                continue;
            }

            String columnName = afterColumn.getName();
            switch (columnName) {
                case "frequency" ->
                        changeFields.add(newUpdateField("frequency", convertEnum(beforeColumn.getValue(), TimeSheetFrequencyType.class), convertEnum(afterColumn.getValue(), TimeSheetFrequencyType.class)));
                case "timesheet_type" ->
                        changeFields.add(newUpdateField("timeSheetType", convertEnum(beforeColumn.getValue(), TimeSheetType.class), convertEnum(afterColumn.getValue(), TimeSheetType.class)));
                case "week_ending" ->
                        changeFields.add(newUpdateField("weekEnding", convertEnum(beforeColumn.getValue(), WeekEndingType.class), convertEnum(afterColumn.getValue(), WeekEndingType.class)));
                case "allow_submit_Timesheet" ->
                        changeFields.add(newUpdateField("allowSubmitTimeSheet", beforeColumn.getValue(), afterColumn.getValue()));
                case "allow_submit_expense" ->
                        changeFields.add(newUpdateField("allowSubmitExpense", beforeColumn.getValue(), afterColumn.getValue()));
                case "instructions" ->
                        changeFields.add(newUpdateField("instructions", beforeColumn.getValue(), afterColumn.getValue()));
                case "working_hours" ->
                        changeFields.add(newUpdateField("workingHours", beforeColumn.getValue(), afterColumn.getValue()));
            }
        }
        return changeFields;
    }


    private JSONObject newUpdateField(String key, String changeFrom, String changeTo) {
        return newChangeField(key, AssignmentOperation.UPDATE, changeFrom, changeTo);
    }

    private JSONObject newChangeField(String key, AssignmentOperation operationType, String changedFrom, String changedTo) {
        JSONObject changeField = new JSONObject();
        changeField.put(KEY, key);
        changeField.put(EVENT_TYPE, operationType);
        changeField.put(CHANGED_FROM, changedFrom);
        changeField.put(CHANGED_TO, changedTo);
        return changeField;
    }

    private String convertEnum(String value, Class<? extends Enum> enumClass) {
        if (value == null || value.isBlank()) {
            return "";
        }
        try {
            Integer intValue = Integer.parseInt(value);
            Method fromDbValueMethod = enumClass.getMethod("fromDbValue", Integer.class);
            fromDbValueMethod.setAccessible(true);
            Object invoke = fromDbValueMethod.invoke(null, intValue);
            return invoke.toString();
        } catch (Exception e) {
            return "";
        }
    }

    private String convertBool(String value) {
        if (value == null || value.isBlank()) {
            return "";
        }
        return switch (value) {
            case "0" -> "false";
            case "1" -> "true";
            default -> "";
        };
    }

}
