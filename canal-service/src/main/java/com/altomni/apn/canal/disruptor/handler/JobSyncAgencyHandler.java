package com.altomni.apn.canal.disruptor.handler;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.disruptor.CanalEvent;
import com.altomni.apn.canal.disruptor.EventContent;
import com.altomni.apn.canal.service.sync.job.SyncJobService;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.google.common.collect.Iterables;
import com.lmax.disruptor.EventHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.altomni.apn.canal.service.canal.CanalClient.*;

@Slf4j
@Component
public class JobSyncAgencyHandler implements EventHandler<CanalEvent> {

    private final CanalService canalService;
    private final SyncJobService syncJobService;

    public JobSyncAgencyHandler(CanalService canalService, SyncJobService syncJobService) {
        this.canalService = canalService;
        this.syncJobService = syncJobService;
    }

    @Override
    public void onEvent(CanalEvent event, long sequence, boolean endOfBatch) throws Exception {
        log.info("-----------------------------> JobSyncAgencyHandler onEvent start <-");
        List<EventContent> eventContents = event.getEventContents();
        Set<Long> jobIds = eventContents.stream()
                .filter(eventContent -> JOB.equals(eventContent.tableName()) || AGENCY_JOB_RELATED_TABLES.contains(eventContent.tableName()))
                .flatMap(eventContent -> {
                    String tableName = eventContent.tableName();
                    CanalEntry.RowChange rowChange = eventContent.rowChange();
                    CanalEntry.EventType eventType = rowChange.getEventType();
                    log.info("[canal] syncTable, table {} changed, event type {}", tableName, eventType);
                    List<CanalEntry.RowData> rowDataList = rowChange.getRowDatasList();
                    return switch (eventType) {
                        case INSERT -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, false))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        case UPDATE -> rowDataList.stream()
                                .map(rowData -> changeRows(rowData.getAfterColumnsList(), tableName, true))
                                .filter(Optional::isPresent)
                                .map(Optional::get);
                        default -> Stream.of();
                    };
                }).collect(Collectors.toSet());

        if (jobIds.isEmpty()) {
            return;
        }
        syncJobsToAgency(jobIds);
    }

    private void syncJobsToAgency(Set<Long> pendingJobsToSync) {
        log.info("[Canal] jobSync startSyncJobsToAgency {}", pendingJobsToSync);
        try {
            for (List<Long> partition : Iterables.partition(pendingJobsToSync, 50)) {
                syncJobService.synchronizeJobToAgency(partition, RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL, 0);
            }
        } catch (Exception e) {
            log.error("[Canal] jobSync SyncJobToAgency Failed, jobs: {}, error: {}", pendingJobsToSync, ExceptionUtil.getStackTrace(e));
            canalService.insertAll(pendingJobsToSync, SyncIdTypeEnum.AGENCY_JOB, FailReasonEnum.ERROR, e.getMessage(), RabbitMqConstant.MESSAGE_PRIORITY_CRITICAL);
        }
    }


    private Optional<Long> changeRows(List<CanalEntry.Column> columns, String tableName, boolean update) {
        if (JOB.equals(tableName)) {
            return jobChange(columns, tableName, update);
        } else if (AGENCY_JOB_RELATED_TABLES.contains(tableName)) {
            return jobRelatedChange(columns, tableName);
        }
        return Optional.empty();
    }

    private Optional<Long> jobChange(List<CanalEntry.Column> columns, String tableName, boolean update) {
        String jobId = null;
        boolean jobStatusChanged = false;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            log.debug("[Canal] jobSyncAgency jobChange: table: {}, column: {}, value: {}, updated: {}, jobId: {}", tableName, name, column.getValue(), column.getUpdated(), jobId);

            // check if job status is changed
            if (update && name.equals(JOB_STATUS) && column.getUpdated()) {
                log.info("[Canal] jobSyncAgency jobChange: column: {}, value: {}, updated: {} set jobStatusChanged to true", name, column.getValue(), column.getUpdated());
                jobStatusChanged = true;
            }
            if (name.equals(ID)) {
                jobId = column.getValue();
            }
        }
        log.info("[Canal] jobSyncAgency received changed jobId: {} from table: {}", jobId, tableName);

        if (!jobStatusChanged) {
            log.info("[Canal] jobSyncAgency received changed jobId: {} from table: {} set to null", jobId, tableName);
            jobId = null;
        }
        return Optional.ofNullable(jobId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

    private Optional<Long> jobRelatedChange(List<CanalEntry.Column> columns, String tableName) {
        String jobId = null;
        for (CanalEntry.Column column : columns) {
            String name = column.getName();
            if (name.equals(JOB_ID)) {
                jobId = column.getValue();
            }
            log.debug("jobRelatedChange: table: {}, column: {}, value: {}, updated: {}", tableName, name, column.getValue(), column.getUpdated());
        }
        log.info("[Canal] received changed jobId: {} from tableName: {}", jobId, tableName);
        return Optional.ofNullable(jobId).filter(StringUtils::isNotEmpty).map(Long::parseLong);
    }

}

