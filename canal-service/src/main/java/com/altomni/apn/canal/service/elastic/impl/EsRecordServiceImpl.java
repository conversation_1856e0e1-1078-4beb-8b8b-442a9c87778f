package com.altomni.apn.canal.service.elastic.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.BulkRequest;
import co.elastic.clients.elasticsearch.core.BulkResponse;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.ElasticsearchTransport;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.service.elastic.EsRecordService;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.http.HttpService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class EsRecordServiceImpl implements EsRecordService {

    private final HttpService httpService;

    private final CanalProperties canalProperties;

    private final ElasticsearchClient esClient;

    private final static String TALENT_INDEX = "activity_talent";
    private final static String TALENT_TEMPLATE = "template_activity_talent";
    private final static String TALENT_POLICY = "activity_talent_policy";

    private final static String JOB_INDEX = "activity_job";
    private static final String JOB_TEMPLATE = "template_activity_job";

    private static final String JOB_SNAPSHOT_TEMPLATE = "template_snapshot_jobs";
    private final static String JOB_POLICY = "activity_job_policy";

    private static final String ASSIGNMENT_INDEX = "activity_assignment";
    private static final String ASSIGNMENT_POLICY = "activity_assignment_policy";
    private static final String ASSIGNMENT_TEMPLATE = "template_activity_assignment";

    private final static String INDEX_TEMPLATE = "_index_template/";
    private final static String POLICY = "_ilm/policy/";

    public EsRecordServiceImpl(HttpService httpService, CanalProperties canalProperties) throws MalformedURLException {
        this.httpService = httpService;
        this.canalProperties = canalProperties;
        String recordUrl = canalProperties.getRecordUrl();
        URL url = new URL(recordUrl);
        RestClient restClient = RestClient.builder(new HttpHost(url.getHost(), url.getPort(), url.getProtocol())).build();

        ElasticsearchTransport transport = new RestClientTransport(restClient, new JacksonJsonpMapper());

        esClient = new ElasticsearchClient(transport);
    }


    private String jobRecordUrl(Long tenantId) {
        return canalProperties.getRecordUrl() + JOB_INDEX + "_" + tenantId;
    }

    private String talentRecordUrl(Long tenantId) {
        return canalProperties.getRecordUrl() + TALENT_INDEX + "_" + tenantId;
    }

    private Boolean existIndex(String url) throws IOException {
        HttpResponse response = httpService.get(url);
        return response.getCode() != 404;
    }

    @Override
    public void batchSaveJobUpdateToEs(Map<Long, List<JSONObject>> syncJobRecords) {
        syncJobRecords.forEach((tenantId, records) -> {
            if (records == null || records.isEmpty()) {
                return;
            }
            List<JSONObject> filteredRecords = records.stream().filter(record -> {
                JSONArray changeFields = record.getJSONArray("changeFields");
                return changeFields != null && !changeFields.isEmpty() && (changeFields.size() != 1 || !changeFields.getJSONObject(0).isEmpty());
            }).toList();
            if (filteredRecords.isEmpty()) {
                return;
            }
            BulkRequest.Builder br = new BulkRequest.Builder();
            filteredRecords.forEach(record -> {
                HashMap<Object, Object> map = new HashMap<>(record);
                br.operations(op -> op.index(idx -> idx
                        .index(JOB_INDEX + "_" + tenantId)
                        .document(map)));
            });
            try {
                String recordsStrRequest = JSON.toJSONString(filteredRecords);
                log.info("[APN: EsFillerJobRecordService] record job to ES request, request record: {}", recordsStrRequest);
                BulkRequest bulkRequest = br.build();
                BulkResponse bulkResponse = esClient.bulk(bulkRequest);
                log.info("[APN: EsFillerJobRecordService] record job to ES response,  response  {}", bulkResponse.toString());
                if (bulkResponse.errors()) {
                    log.info("[APN: EsFillerJobRecordService] record job status to EsFiller error,  response  {}", bulkResponse);
                }
            } catch (IOException exception) {
                log.error("[APN: EsFillerJobRecordService] record job change to EsFiller IOException");
            }
        });
    }

//    @Override
//    public void batchSaveVoipUpdateToEs(Map<Long, List<JSONObject>> syncVoipRecords) {
//        syncVoipRecords.forEach((tenantId, records) -> {
//            if (records == null || records.isEmpty()) {
//                return;
//            }
//            List<JSONObject> filteredRecords = records.stream().filter(record -> {
//                JSONArray changeFields = record.getJSONArray("changeFields");
//                return changeFields != null && !changeFields.isEmpty() && (changeFields.size() != 1 || !changeFields.getJSONObject(0).isEmpty());
//            }).toList();
//            if (filteredRecords.isEmpty()) {
//                return;
//            }
//            BulkRequest.Builder br = new BulkRequest.Builder();
//            filteredRecords.forEach(record -> {
//                HashMap<Object, Object> map = new HashMap<>(record);
//                br.operations(op -> op.index(idx -> idx
//                        .index(VOIP_INDEX + "_" + tenantId)
//                        .document(map)));
//            });
//            try {
//                String recordsStrRequest = JSON.toJSONString(filteredRecords);
//                log.info("[APN: EsFillerJobRecordService] record job to ES request, request record: {}", recordsStrRequest);
//                BulkRequest bulkRequest = br.build();
//                BulkResponse bulkResponse = esClient.bulk(bulkRequest);
//                log.info("[APN: EsFillerJobRecordService] record job to ES response,  response  {}", bulkResponse.toString());
//                if (bulkResponse.errors()) {
//                    log.info("[APN: EsFillerJobRecordService] record job status to EsFiller error,  response  {}", bulkResponse);
//                }
//            } catch (IOException exception) {
//                log.error("[APN: EsFillerJobRecordService] record job change to EsFiller IOException");
//            }
//        });
//    }

    @Override
    public void batchSaveTalentUpdateToEs(Map<Long, List<JSONObject>> syncTalentRecords) {
        syncTalentRecords.forEach((tenantId, records) -> {
            if (records == null || records.isEmpty()) {
                return;
            }
            List<JSONObject> filteredRecords = records.stream().filter(record -> {
                JSONArray changeFields = record.getJSONArray("changeFields");
                return changeFields != null && !changeFields.isEmpty() && (changeFields.size() != 1 || !changeFields.getJSONObject(0).isEmpty());
            }).toList();
            if (filteredRecords.isEmpty()) {
                return;
            }
            BulkRequest.Builder br = new BulkRequest.Builder();
            filteredRecords.forEach(record -> {
                HashMap<Object, Object> map = new HashMap<>(record);
                br.operations(op -> op.index(idx -> idx
                        .index(TALENT_INDEX + "_" + tenantId)
                        .document(map)));
            });
            try {
                log.info("[APN: EsFillerTalentRecordService] record talent to ES request, request record: {}", JSON.toJSONString(filteredRecords));
                BulkResponse bulkResponse = esClient.bulk(br.build());
                log.info("[APN: EsFillerTalentRecordService] record talent to ES response: {}", bulkResponse.toString());
                if (bulkResponse.errors()) {
                    log.info("[APN: EsFillerTalentRecordService] record talent status to EsFiller error,  response: {}", bulkResponse);
                }
            } catch (IOException exception) {
                log.error("[APN: EsFillerTalentRecordService] record talent change to EsFiller IOException", exception);
            }
        });
    }

    @Override
    public void batchSaveAssignmentUpdateToEs(Map<Long, List<JSONObject>> syncAssignmentRecords) {
        syncAssignmentRecords.forEach((tenantId, records) -> {
            if (records == null || records.isEmpty()) {
                return;
            }
            List<JSONObject> filteredRecords = records.stream().filter(record -> {
                JSONArray changeFields = record.getJSONArray("changeFields");
                return changeFields != null && !changeFields.isEmpty() && !changeFields.getJSONObject(0).isEmpty();
            }).toList();
            if (filteredRecords.isEmpty()) {
                return;
            }
            BulkRequest.Builder br = new BulkRequest.Builder();
            filteredRecords.forEach(record -> {
                HashMap<Object, Object> map = new HashMap<>(record);
                br.operations(op -> op.index(idx -> idx
                        .index(ASSIGNMENT_INDEX + "_" + tenantId)
                        .document(map)));
            });
            try {
                log.info("[APN: EsFillerTalentRecordService] record assignment to ES request, request record: {}", JSON.toJSONString(filteredRecords));
                BulkResponse bulkResponse = esClient.bulk(br.build());
                log.info("[APN: EsFillerTalentRecordService] record assignment to ES response: {}", bulkResponse.toString());
                if (bulkResponse.errors()) {
                    log.info("[APN: EsFillerTalentRecordService] record assignment status to EsFiller error,  response: {}", bulkResponse);
                }
            } catch (IOException exception) {
                log.error("[APN: EsFillerTalentRecordService] record assignment change to EsFiller IOException", exception);
            }
        });
    }

    @Override
    public void createTalentTemplate() {
        createEsContent(INDEX_TEMPLATE, TALENT_TEMPLATE, canalProperties.getTalentTemplate());
    }

    @Override
    public void createJobTemplate() {
        createEsContent(INDEX_TEMPLATE, JOB_TEMPLATE, canalProperties.getJobTemplate());
    }

    @Override
    public void createJobSnapshotTemplate() {
        createEsContent(INDEX_TEMPLATE, JOB_SNAPSHOT_TEMPLATE, canalProperties.getJobSnapshotTemplate());
    }

    @Override
    public void createAssignmentTemplate() {
        createEsContent(INDEX_TEMPLATE, ASSIGNMENT_TEMPLATE, canalProperties.getAssignmentTemplate());
    }

    @Override
    public void createPolicy() {
        createEsContent(POLICY, TALENT_POLICY, canalProperties.getPolicy());
        createEsContent(POLICY, JOB_POLICY, canalProperties.getPolicy());
        createEsContent(POLICY, ASSIGNMENT_POLICY, canalProperties.getPolicy());
    }

    private void createEsContent(String module, String name, String content) {
        Map<String, String> headersBuilder = new HashMap<>();
        headersBuilder.put("Content-Type", "application/json");
        try {
            HttpResponse existsResponse = httpService.get(canalProperties.getRecordUrl() + module + name);
            if (existsResponse == null || (!ObjectUtils.equals(HttpStatus.OK.value(), existsResponse.getCode()) && !ObjectUtils.equals(HttpStatus.CREATED.value(), existsResponse.getCode()))) {
                HttpResponse response = httpService.put(canalProperties.getRecordUrl() + module + name, Headers.of(headersBuilder), content);
                if (response == null || (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode()) && !ObjectUtils.equals(HttpStatus.CREATED.value(), response.getCode()))) {
                    log.error("[APN: EsRecordService] create es {} error", name);
                }
            }
        } catch (IOException exception) {
            log.error("[APN: EsRecordService] create es {} error", name);
        }
    }
}