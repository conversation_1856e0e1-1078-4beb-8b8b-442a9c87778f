package com.altomni.apn.canal.service.sync.company.impl;

import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.canal.config.RabbitMqConstant;
import com.altomni.apn.canal.config.env.CanalProperties;
import com.altomni.apn.canal.repository.CompanyRepositoryCustom;
import com.altomni.apn.canal.service.login.LoginService;
import com.altomni.apn.canal.service.login.impl.LoginServiceImpl;
import com.altomni.apn.canal.service.sync.company.SyncCompanyService;
import com.altomni.apn.canal.web.rest.vm.MqMessageCountVM;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;

/**
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncCompanyServiceImpl implements SyncCompanyService {
    public static final String URI_COMPANY_MQ_MESSAGE_COUNT_CHECK = "/company/api/v3/canal/check-company-mq-message-count";
    public static final String URI_COMPANY_SYNCHRONIZATION = "/company/api/v3/canal/sync-companies-to-mq";

    public static final String URI_COMPANY_CONTACT_MQ_MESSAGE_COUNT_CHECK = "/company/api/v3/canal/check-company-contact-hr-mq-message-count";
    public static final String URI_COMPANY_CONTACT_SYNCHRONIZATION = "/company/api/v3/canal/sync-company-contact-to-hr-mq";
    public static final String URI_COMPANY_HR_SYNCHRONIZATION = "/company/api/v3/canal/sync-company-to-hr-mq";

    public static final String URI_COMPANY_CLIENT_NOTE_MQ_MESSAGE_COUNT_CHECK = "/company/api/v3/canal/check-company-client-note-mq-message-count";
    public static final String URI_COMPANY_CLIENT_NOTE_SYNCHRONIZATION = "/company/api/v3/canal/sync-company-client-note-to-mq";

    public static final String URI_COMPANY_PROGRESS_NOTE_MQ_MESSAGE_COUNT_CHECK = "/company/api/v3/canal/check-company-progress-note-mq-message-count";
    public static final String URI_COMPANY_PROGRESS_NOTE_SYNCHRONIZATION = "/company/api/v3/canal/sync-company-progress-note-to-mq";

    private RestTemplate restTemplate = new RestTemplate();

    @Resource
    private CompanyRepositoryCustom companyRepositoryCustom;

    @Resource
    private CanalProperties canalProperties;

    @Resource
    private LoginService loginService;

//    @Resource
//    private RedisService redisService;

    @Resource
    private CanalService canalService;

    Map<String, Object> companyParamMap = new HashMap<>();

    Map<String, Object> companyContactParamMap = new HashMap<>();

    Map<String, Object> companyClientNoteParamMap = new HashMap<>();

    Map<String, Object> companyProgressNoteParamMap = new HashMap<>();

    Map<String, Object> companyHrParamMap = new HashMap<>();

    public MqMessageCountVM checkCompanyMessageCount(){
        String api = canalProperties.getApiBase() + URI_COMPANY_MQ_MESSAGE_COUNT_CHECK;
        HttpHeaders headers = loginService.buildHeaders();
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(headers);
        ResponseEntity<MqMessageCountVM> response = this.restTemplate.postForEntity(api, entity, MqMessageCountVM.class);
        MqMessageCountVM messageCountVM = response.getBody();
        log.info("checkCompanyMessageCount:" + messageCountVM);
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED
                && messageCountVM.getError() != null
                && messageCountVM.getError().contains("invalid_token")) {
            log.warn("[checkCompanyMessageCount @-1] Access token expired.");
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            return checkCompanyMessageCount();
        }
        return messageCountVM;
    }

    public MqMessageCountVM checkCompanyContactMessageCount(){
        String api = canalProperties.getApiBase() + URI_COMPANY_CONTACT_MQ_MESSAGE_COUNT_CHECK;
        HttpHeaders headers = loginService.buildHeaders();
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(headers);
        ResponseEntity<MqMessageCountVM> response = this.restTemplate.postForEntity(api, entity, MqMessageCountVM.class);
        MqMessageCountVM messageCountVM = response.getBody();
        log.info("checkCompanyContactMessageCount:" + messageCountVM);
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED
                && messageCountVM.getError() != null
                && messageCountVM.getError().contains("invalid_token")) {
            log.warn("[checkCompanyContactMessageCount @-1] Access token expired.");
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            return checkCompanyMessageCount();
        }
        return messageCountVM;
    }

    public MqMessageCountVM checkCompanyClientNoteMessageCount(){
        String api = canalProperties.getApiBase() + URI_COMPANY_CLIENT_NOTE_MQ_MESSAGE_COUNT_CHECK;
        HttpHeaders headers = loginService.buildHeaders();
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(headers);
        ResponseEntity<MqMessageCountVM> response = this.restTemplate.postForEntity(api, entity, MqMessageCountVM.class);
        MqMessageCountVM messageCountVM = response.getBody();
        log.info("checkCompanyClientNoteMessageCount:" + messageCountVM);
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED
                && messageCountVM.getError() != null
                && messageCountVM.getError().contains("invalid_token")) {
            log.warn("[checkCompanyClientNoteMessageCount @-1] Access token expired.");
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            return checkCompanyClientNoteMessageCount();
        }
        return messageCountVM;
    }

    public MqMessageCountVM checkCompanyProgressNoteMessageCount(){
        String api = canalProperties.getApiBase() + URI_COMPANY_PROGRESS_NOTE_MQ_MESSAGE_COUNT_CHECK;
        HttpHeaders headers = loginService.buildHeaders();
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(headers);
        ResponseEntity<MqMessageCountVM> response = this.restTemplate.postForEntity(api, entity, MqMessageCountVM.class);
        MqMessageCountVM messageCountVM = response.getBody();
        log.info("checkCompanyProgressNoteMessageCount:" + messageCountVM);
        if (response.getStatusCode() == HttpStatus.UNAUTHORIZED
                && messageCountVM.getError() != null
                && messageCountVM.getError().contains("invalid_token")) {
            log.warn("[checkCompanyProgressNoteMessageCount @-1] Access token expired.");
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            return checkCompanyProgressNoteMessageCount();
        }
        return messageCountVM;
    }


    @Override
    public void synchronizeCompanies(Collection<Long> companyIds, int priority, int deep) {
        companyParamMap.put("companyIds", companyIds);
        companyParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_COMPANY_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(companyParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeCompanies @{}] synchronizeCompanies error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(companyIds, SyncIdTypeEnum.COMPANY, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "SynchronizeCompanies error " +
                        "\n\tcompanyIds: " + companyIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                log.error("[synchronizeCompanies @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeJobs] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeCompanies(companyIds, priority, deep + 1);
                }else {
                    canalService.insertAll(companyIds, SyncIdTypeEnum.COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeCompanies] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(companyIds, SyncIdTypeEnum.COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeCompanies @{}] SynchronizeCompanies error. CompanyIds: {}. Error: {}", SecurityUtils.getUserId(), companyIds, ExceptionUtil.getStackTrace(e));
                String message = "SynchronizeCompanies error" +
                        "\n\tcompanyIds: " + companyIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            canalService.insertAll(companyIds, SyncIdTypeEnum.COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
            log.error("[synchronizeCompanies @{}] synchronizeCompanies error, companyIds: {}, error: {}", SecurityUtils.getUserId(), companyIds, ExceptionUtil.getStackTrace(e));
            String message = "SynchronizeCompanies error" +
                    "\n\tcompanyIds: " + companyIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    @Override
    public void synchronizeCompanyClientNotes(Collection<Long> companyClientNoteIds, int priority, int deep) {
        companyClientNoteParamMap.put("companyClientNoteIds", companyClientNoteIds);
        companyClientNoteParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_COMPANY_CLIENT_NOTE_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(companyClientNoteParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeCompanyClientNotes @{}] synchronizeCompanyClientNotes error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(companyClientNoteIds, SyncIdTypeEnum.COMPANY_CLIENT_NOTE, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "SynchronizeCompanyClientNotes error " +
                        "\n\tnoteIds: " + companyClientNoteIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                log.error("[synchronizeCompanyClientNotes @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeJobs] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeCompanyClientNotes(companyClientNoteIds, priority, deep + 1);
                }else {
                    canalService.insertAll(companyClientNoteIds, SyncIdTypeEnum.COMPANY_CLIENT_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeCompanyClientNotes] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(companyClientNoteIds, SyncIdTypeEnum.COMPANY_CLIENT_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeCompanyClientNotes @{}] SynchronizeCompanyClientNotes error. NoteIds: {}. Error: {}", SecurityUtils.getUserId(), companyClientNoteIds, ExceptionUtil.getStackTrace(e));
                String message = "SynchronizeCompanyClientNotes error" +
                        "\n\tnoteIds: " + companyClientNoteIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            canalService.insertAll(companyClientNoteIds, SyncIdTypeEnum.COMPANY_CLIENT_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
            log.error("[synchronizeCompanyClientNotes @{}] synchronizeCompanyClientNotes error, noteIds: {}, error: {}", SecurityUtils.getUserId(), companyClientNoteIds, ExceptionUtil.getStackTrace(e));
            String message = "SynchronizeCompanyClientNotes error" +
                    "\n\tnoteIds: " + companyClientNoteIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    @Override
    public void synchronizeCompanyProgressNotes(Collection<Long> companyProgressNoteIds, int priority, int deep) {
        companyProgressNoteParamMap.put("companyProgressNoteIds", companyProgressNoteIds);
        companyProgressNoteParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_COMPANY_PROGRESS_NOTE_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(companyProgressNoteParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeCompanyProgressNotes @{}] synchronizeCompanyProgressNotes error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(companyProgressNoteIds, SyncIdTypeEnum.COMPANY_PROGRESS_NOTE, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "SynchronizeCompanyProgressNotes error " +
                        "\n\tnoteIds: " + companyProgressNoteIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                log.error("[synchronizeCompanyProgressNotes @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeJobs] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeCompanyProgressNotes(companyProgressNoteIds, priority, deep + 1);
                }else {
                    canalService.insertAll(companyProgressNoteIds, SyncIdTypeEnum.COMPANY_PROGRESS_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeCompanyProgressNotes] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(companyProgressNoteIds, SyncIdTypeEnum.COMPANY_PROGRESS_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeCompanyProgressNotes @{}] SynchronizeCompanyProgressNotes error. NoteIds: {}. Error: {}", SecurityUtils.getUserId(), companyProgressNoteIds, ExceptionUtil.getStackTrace(e));
                String message = "SynchronizeCompanyProgressNotes error" +
                        "\n\tnoteIds: " + companyProgressNoteIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            canalService.insertAll(companyProgressNoteIds, SyncIdTypeEnum.COMPANY_PROGRESS_NOTE, FailReasonEnum.ERROR, e.getMessage(), priority);
            log.error("[synchronizeCompanyProgressNotes @{}] synchronizeCompanyProgressNotes error, noteIds: {}, error: {}", SecurityUtils.getUserId(), companyProgressNoteIds, ExceptionUtil.getStackTrace(e));
            String message = "synchronizeCompanyProgressNotes error" +
                    "\n\tnoteIds: " + companyProgressNoteIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    @Override
    public void synchronizeCompanyContacts(Collection<Long> contactIds, int priority, int deep) {
        companyContactParamMap.put("contactIds", contactIds);
        companyContactParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_COMPANY_CONTACT_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(companyContactParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeCompanyContacts @{}] synchronizeCompanyContacts error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(contactIds, SyncIdTypeEnum.HR_COMPANY_CONTACT, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "synchronizeCompanyContacts error " +
                        "\n\tcontactIds: " + contactIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                log.error("[synchronizeCompanyContacts @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeJobs] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeCompanies(contactIds, priority, deep + 1);
                }else {
                    canalService.insertAll(contactIds, SyncIdTypeEnum.HR_COMPANY_CONTACT, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeCompanyContacts] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(contactIds, SyncIdTypeEnum.HR_COMPANY_CONTACT, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeCompanyContacts @{}] synchronizeCompanyContacts error. contactIds: {}. Error: {}", SecurityUtils.getUserId(), contactIds, ExceptionUtil.getStackTrace(e));
                String message = "SynchronizeCompanies error" +
                        "\n\tcontactIds: " + contactIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            canalService.insertAll(contactIds, SyncIdTypeEnum.HR_COMPANY_CONTACT, FailReasonEnum.ERROR, e.getMessage(), priority);
            log.error("[synchronizeCompanyContacts @{}] synchronizeCompanyContacts error, contactIds: {}, error: {}", SecurityUtils.getUserId(), contactIds, ExceptionUtil.getStackTrace(e));
            String message = "synchronizeCompanyContacts error" +
                    "\n\tcontactIds: " + contactIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    @Override
    public void synchronizeCompanyHr(Collection<Long> companyIds, int priority, int deep) {
        companyHrParamMap.put("companyIds", companyIds);
        companyHrParamMap.put("priority", priority);
        try {
            String api = canalProperties.getApiBase() + URI_COMPANY_HR_SYNCHRONIZATION;
            // build the request
            HttpHeaders headers = loginService.buildHeaders();
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(companyHrParamMap, headers);
            ResponseEntity<String> response = this.restTemplate.postForEntity(api, entity, String.class);
            // check response status code
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("[synchronizeCompanyHr @{}] synchronizeCompanyHr error: {} at: {}", SecurityUtils.getUserId(), response.getBody(), System.currentTimeMillis());
                loginService.removeToken(LoginServiceImpl.TOKEN_APN);
                canalService.insertAll(companyIds, SyncIdTypeEnum.HR_COMPANY, FailReasonEnum.ERROR, response.getBody(), priority);
                String message = "synchronizeCompanyHr error " +
                        "\n\tcompanyIds: " + companyIds +
                        "\n\tResponse: " + response;
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }
        }catch (HttpClientErrorException e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            if (e.getStatusCode() == HttpStatus.UNAUTHORIZED){
                log.error("[synchronizeCompanyHr @{}] Access token expired. Error: {}", SecurityUtils.getUserId(), ExceptionUtil.getStackTrace(e));
                // retry only once
                if (deep < 1){
                    //String message = "[synchronizeJobs] Access token expired. Trying to re-login.";
                    //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                    synchronizeCompanies(companyIds, priority, deep + 1);
                }else {
                    canalService.insertAll(companyIds, SyncIdTypeEnum.HR_COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "[synchronizeCompanyHr] Access token expired. Still failed after re-login.";
                    NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
                }
            }else {
                canalService.insertAll(companyIds, SyncIdTypeEnum.HR_COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
                log.error("[synchronizeCompanyHr @{}] synchronizeCompanyHr error. companyIds: {}. Error: {}", SecurityUtils.getUserId(), companyIds, ExceptionUtil.getStackTrace(e));
                String message = "synchronizeCompanyHr error" +
                        "\n\tcompanyIds: " + companyIds +
                        "\n\tError: " +
                        "\n\t\t" + ExceptionUtil.getStackTrace(e);
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
            }

        }catch (Exception e){
            loginService.removeToken(LoginServiceImpl.TOKEN_APN);
            canalService.insertAll(companyIds, SyncIdTypeEnum.HR_COMPANY, FailReasonEnum.ERROR, e.getMessage(), priority);
            log.error("[synchronizeCompanyHr @{}] synchronizeCompanyHr error, companyIds: {}, error: {}", SecurityUtils.getUserId(), companyIds, ExceptionUtil.getStackTrace(e));
            String message = "synchronizeCompanyHr error" +
                    "\n\tcompanyIds: " + companyIds +
                    "\n\tError: " +
                    "\n\t\t" + ExceptionUtil.getStackTrace(e);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), message);
        }
    }

    @Override
    public void validateSql(String sql) {
        companyRepositoryCustom.getIdsBySql(sql + " limit 1");
    }

    @Async
    @Override
    public void bulkSyncCompanies(String sql) {
        try {
            Integer unfinished = bulkSyncCompanies(sql, 50);
            syncFinish(unfinished);
        } catch (Exception e) {
            log.error("[bulkSyncCompanies @-1] bulkSyncCompanies error: {} at: {}", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e), System.currentTimeMillis());
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), String.format("BulkSyncCompanies error:  %s", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e)));
        }
    }

    @Async
    @Override
    public void bulkSyncClientNotes(String sql) {
        try {
            Integer unfinished = bulkSyncClientNotes(sql, 50);
            syncClientNoteFinish(unfinished);
        } catch (Exception e) {
            log.error("[bulkSyncCompanyClientNotes @-1] bulkSyncCompanyClientNotes error: {} at: {}", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e), System.currentTimeMillis());
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), String.format("BulkSyncCompanyClientNotes error:  %s", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e)));
        }
    }

    @Async
    @Override
    public void bulkSyncProgressNotes(String sql) {
        try {
            Integer unfinished = bulkSyncProgressNotes(sql, 50);
            syncProgressNoteFinish(unfinished);
        } catch (Exception e) {
            log.error("[bulkSyncCompanyProgressNotes @-1] bulkSyncCompanyProgressNotes error: {} at: {}", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e), System.currentTimeMillis());
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), String.format("BulkSyncCompanyProgressNotes error:  %s", cn.hutool.core.exceptions.ExceptionUtil.stacktraceToString(e)));
        }
    }

    private Integer bulkSyncCompanies(String sql, int pageSize) throws Exception {
        String startTime = DateUtil.getStandardCurrentTime();
        Long lastId = Long.MAX_VALUE;
        List<Long> companyIds = companyRepositoryCustom.getIdsBySql(String.format("%s and c.id < %d order by c.id desc limit %d", sql, lastId, pageSize));
        Long maxId = 0L;
        if (!CollectionUtils.isEmpty(companyIds)){
            maxId = companyIds.get(0);
        }
        int checkCompanyQueueTimes = 1;
        while(!CollectionUtils.isEmpty(companyIds)){
            if (checkCompanyQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The company queue is abnormal for 30 minutes.");
            }
            if (!isCompanyQueueAvailable()){
                checkCompanyQueueTimes ++;
                Thread.sleep(5000);
                continue;
            }
            lastId = companyIds.get(companyIds.size() - 1);
            //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "Last job ID: " + lastId);
            log.info("LastCompanyID: {}", lastId);
            synchronizeCompanies(companyIds, RabbitMqConstant.MESSAGE_PRIORITY_LOW, 0);
            companyIds = companyRepositoryCustom.getIdsBySql(String.format("%s and c.id < %d order by c.id desc limit %d", sql, lastId, pageSize));
            checkCompanyQueueTimes = 1;
        }

        while (!isCompanyQueueEmpty()) {
            checkCompanyQueueTimes ++;
            if (checkCompanyQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The company queue is abnormal for 30 minutes.");
            }
            Thread.sleep(5000);
        }
        Thread.sleep(60000);
        Long unfinished = companyRepositoryCustom.getIdsBySql(String.format("%s and c.id <= %d and (c.last_sync_time is null or c.last_sync_time < '%s')",
                sql.replaceFirst("c.id", "count(c.id)"), maxId, startTime)).get(0);
        return unfinished.intValue();
    }

    private Integer bulkSyncClientNotes(String sql, int pageSize) throws Exception {
        String startTime = DateUtil.getStandardCurrentTime();
        Long lastId = Long.MAX_VALUE;
        List<Long> noteIds = companyRepositoryCustom.getIdsBySql(String.format("%s and n.id < %d order by n.id desc limit %d", sql, lastId, pageSize));
        Long maxId = 0L;
        if (!CollectionUtils.isEmpty(noteIds)){
            maxId = noteIds.get(0);
        }
        int checkNoteQueueTimes = 1;
        while(!CollectionUtils.isEmpty(noteIds)){
            if (checkNoteQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The companyClientNote queue is abnormal for 30 minutes.");
            }
            if (!isCompanyClientNoteQueueAvailable()){
                checkNoteQueueTimes ++;
                Thread.sleep(5000);
                continue;
            }
            lastId = noteIds.get(noteIds.size() - 1);
            //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "Last job ID: " + lastId);
            log.info("LastCompanyClientNoteID: {}", lastId);
            synchronizeCompanyClientNotes(noteIds, RabbitMqConstant.MESSAGE_PRIORITY_LOW, 0);
            noteIds = companyRepositoryCustom.getIdsBySql(String.format("%s and n.id < %d order by n.id desc limit %d", sql, lastId, pageSize));
            checkNoteQueueTimes = 1;
        }

        while (!isCompanyClientNoteQueueEmpty()) {
            checkNoteQueueTimes ++;
            if (checkNoteQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The companyClientNote queue is abnormal for 30 minutes.");
            }
            Thread.sleep(5000);
        }
        Thread.sleep(60000);
        Long unfinished = companyRepositoryCustom.getIdsBySql(String.format("%s and n.id <= %d and (n.last_sync_time is null or n.last_sync_time < '%s')",
                sql.replaceFirst("n.id", "count(n.id)"), maxId, startTime)).get(0);
        return unfinished.intValue();
    }

    private Integer bulkSyncProgressNotes(String sql, int pageSize) throws Exception {
        String startTime = DateUtil.getStandardCurrentTime();
        Long lastId = Long.MAX_VALUE;
        List<Long> noteIds = companyRepositoryCustom.getIdsBySql(String.format("%s and n.id < %d order by n.id desc limit %d", sql, lastId, pageSize));
        Long maxId = 0L;
        if (!CollectionUtils.isEmpty(noteIds)){
            maxId = noteIds.get(0);
        }
        int checkNoteQueueTimes = 1;
        while(!CollectionUtils.isEmpty(noteIds)){
            if (checkNoteQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The companyProgressNote queue is abnormal for 30 minutes.");
            }
            if (!isCompanyProgressNoteQueueAvailable()){
                checkNoteQueueTimes ++;
                Thread.sleep(5000);
                continue;
            }
            lastId = noteIds.get(noteIds.size() - 1);
            //NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "Last job ID: " + lastId);
            log.info("LastCompanyProgressNoteID: {}", lastId);
            synchronizeCompanyProgressNotes(noteIds, RabbitMqConstant.MESSAGE_PRIORITY_LOW, 0);
            noteIds = companyRepositoryCustom.getIdsBySql(String.format("%s and n.id < %d order by n.id desc limit %d", sql, lastId, pageSize));
            checkNoteQueueTimes = 1;
        }

        while (!isCompanyProgressNoteQueueEmpty()) {
            checkNoteQueueTimes ++;
            if (checkNoteQueueTimes % 360 == 0){
                NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "The companyProgressNote queue is abnormal for 30 minutes.");
            }
            Thread.sleep(5000);
        }
        Thread.sleep(60000);
        Long unfinished = companyRepositoryCustom.getIdsBySql(String.format("%s and n.id <= %d and (n.last_sync_time is null or n.last_sync_time < '%s')",
                sql.replaceFirst("n.id", "count(n.id)"), maxId, startTime)).get(0);
        return unfinished.intValue();
    }

    private void syncFinish(Integer unfinished){
        if (unfinished.intValue() == 0){
            log.info("bulkSyncCompanies finished");
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "bulkSyncCompanies finished.");
        }else{
            log.warn("bulkSyncCompanies unfinished. Count: {}", unfinished);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "bulkSyncCompanies unfinished. Count: " + unfinished);
        }
    }

    private void syncClientNoteFinish(Integer unfinished){
        if (unfinished.intValue() == 0){
            log.info("bulkSyncCompanyClientNotes finished");
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "bulkSyncCompanyClientNotes finished.");
        }else{
            log.warn("bulkSyncCompanyClientNotes unfinished. Count: {}", unfinished);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "bulkSyncCompanyClientNotes unfinished. Count: " + unfinished);
        }
    }

    private void syncProgressNoteFinish(Integer unfinished){
        if (unfinished.intValue() == 0){
            log.info("bulkSyncCompanyProgressNotes finished");
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "bulkSyncCompanyProgressNotes finished.");
        }else{
            log.warn("bulkSyncCompanyProgressNotes unfinished. Count: {}", unfinished);
            NotificationUtils.sendAlertToLark(canalProperties.getWebhookKey(), canalProperties.getWebhookUrl(), "bulkSyncCompanyProgressNotes unfinished. Count: " + unfinished);
        }
    }

    private boolean isCompanyQueueEmpty(){
        return this.checkCompanyMessageCount().getMessageCount() == 0;
    }

    private boolean isCompanyClientNoteQueueEmpty(){
        return this.checkCompanyClientNoteMessageCount().getMessageCount() == 0;
    }

    private boolean isCompanyProgressNoteQueueEmpty(){
        return this.checkCompanyProgressNoteMessageCount().getMessageCount() == 0;
    }

    public boolean isCompanyQueueAvailable(){
        final MqMessageCountVM messageCountVM = this.checkCompanyMessageCount();
        return Objects.nonNull(messageCountVM.getMessageCount())
                && Objects.nonNull(messageCountVM.getMaximumMessageCount())
                && messageCountVM.getMaximumMessageCount() > messageCountVM.getMessageCount();
    }

    public boolean isCompanyClientNoteQueueAvailable(){
        final MqMessageCountVM messageCountVM = this.checkCompanyClientNoteMessageCount();
        return Objects.nonNull(messageCountVM.getMessageCount())
                && Objects.nonNull(messageCountVM.getMaximumMessageCount())
                && messageCountVM.getMaximumMessageCount() > messageCountVM.getMessageCount();
    }

    public boolean isCompanyProgressNoteQueueAvailable(){
        final MqMessageCountVM messageCountVM = this.checkCompanyProgressNoteMessageCount();
        return Objects.nonNull(messageCountVM.getMessageCount())
                && Objects.nonNull(messageCountVM.getMaximumMessageCount())
                && messageCountVM.getMaximumMessageCount() > messageCountVM.getMessageCount();
    }

    public boolean isCompanyContactQueueAvailable(){
        final MqMessageCountVM messageCountVM = this.checkCompanyContactMessageCount();
        return Objects.nonNull(messageCountVM.getMessageCount())
                && Objects.nonNull(messageCountVM.getMaximumMessageCount())
                && messageCountVM.getMaximumMessageCount() > messageCountVM.getMessageCount();
    }

}
