package com.altomni.apn.canal.entity;

import cn.hutool.json.JSONObject;
import lombok.Data;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

@Data
public class TalentRecord {
    Long talentId;
    Long tenantId;
    List<JSONObject> esDocuments;
    List<TalentProcessRecord> talentProcessRecords;
    String createdBy;
    String createdTime;
    boolean initial;

    public TalentRecord() {
        talentProcessRecords = new ArrayList<>();
        esDocuments = new ArrayList<>();
        initial = false;
    }

    public void setCreateTime(String createdTime) {
        if(this.createdTime == null) {
            this.createdTime = createdTime;
        }
        Instant newTime = Instant.parse(createdTime);
        Instant oldTime = Instant.parse(this.createdTime);
        if(oldTime.compareTo(newTime) < 0) {
            this.createdTime = createdTime;
        }
    }

    public void setInitial(boolean initial) {
        this.initial = this.initial || initial;
    }

    public void addEsDocuments(List<JSONObject> esDocuments) {
        this.esDocuments.addAll(esDocuments);
    }

    public void addTalentProcessRecordList(List<TalentProcessRecord> talentProcessRecords) {
        this.talentProcessRecords.addAll(talentProcessRecords);
    }
}
