package com.altomni.apn.canal.service.sync.company;

import java.util.Collection;
import java.util.List;

public interface SyncCompanyService {

    void synchronizeCompanies(Collection<Long> companyIds, int priority, int deep);

    void validateSql(String sql);

    void bulkSyncCompanies(String sql);

    void synchronizeCompanyClientNotes(Collection<Long> companyClientNoteIds, int priority, int deep);

    void synchronizeCompanyProgressNotes(Collection<Long> companyProgressNoteIds, int priority, int deep);

    void bulkSyncClientNotes(String sql);

    void bulkSyncProgressNotes(String sql);

    boolean isCompanyQueueAvailable();

    boolean isCompanyClientNoteQueueAvailable();

    boolean isCompanyProgressNoteQueueAvailable();

    void synchronizeCompanyContacts(Collection<Long> companyContactIds, int messagePriorityThree, int deep);

    boolean isCompanyContactQueueAvailable();

    void synchronizeCompanyHr(Collection<Long> companyContactIds, int messagePriorityThree, int deep);
}
