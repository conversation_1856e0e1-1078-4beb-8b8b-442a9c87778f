package com.altomni.apn.canal.service.changerecord.job;

import cn.hutool.json.JSONObject;
import com.alibaba.otter.canal.protocol.CanalEntry.Column;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface JobChangeRecordService {

    List<JSONObject> recordUpdateJobRows(List<Column> preColumns, List<Column> afterColumns, String tableName, boolean update, Long jobId, String createdTime, String createdBy, List<JSONObject> esDocuments);

    Long recordJobId(List<Column> preColumns, List<Column> afterColumns, String tableName, Map<Long, Long> jobAdditional2JobidMap);

    String recordCreatedTime(List<Column> preColumns, List<Column> afterColumns, String tableName, String createdTime);

    String recordCreatedBy(List<Column> preColumns, List<Column> afterColumns, String tableName, String createdBy);

    Set<String> getJobAllowDuplicatedKeysSet();

    Boolean checkCreateJob(List<Column> afterColumnsList, String tableName, Long jobId, List<JSONObject> esDocuments);

    Object generateNewJob(Long jobId);

    void recordDeleteJobRows(List<Column> beforeColumnsList, List<Column> afterColumnsList, String tableName, List<JSONObject> esDocuments);
}
