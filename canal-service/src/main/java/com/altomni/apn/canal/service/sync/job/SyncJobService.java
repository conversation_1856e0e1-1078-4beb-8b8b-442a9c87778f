package com.altomni.apn.canal.service.sync.job;

import java.util.Collection;

public interface SyncJobService {

    void synchronizeJobs(Collection<Long> jobIds, int priority, int deep);

    void validateSql(String sql);

    void bulkSyncJobs(String sql);

    boolean isJobQueueAvailable();

    void synchronizeJobHrs(Collection<Long> jobIds, int priority, int deep);

    void synchronizeJobToAgency(Collection<Long> jobIds, int priority, int deep);
}
