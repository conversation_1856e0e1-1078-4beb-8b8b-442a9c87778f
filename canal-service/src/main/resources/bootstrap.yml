spring:
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  application:
    name: canal-service
  cloud:
    nacos:
      config:
        server-addr: ${NACOS-SERVICE-ADDR:localhost:8848}
        username: ${NACOS-USERNAME:nacos}
        password: ${NACOS-PASSWORD:nacos}
        file-extension: yaml
        namespace: ${NAMESPACE:dev}
        refresh-enabled: true
        shared-configs:
          - data-id: public-log.yaml
            refresh: true
          - data-id: public-spring-cloud.yaml
            refresh: true
          - data-id: public-actuator.yaml
            refresh: true
          - data-id: public-redis.yaml
            refresh: true
          - data-id: public-datasource.yaml
            refresh: true
          - data-id: public-http.yaml
            refresh: true
          - data-id: public-statistic.yaml
            refresh: true
          - data-id: public-es.yaml
            refresh: true
          - data-id: public-sso.yaml
            refresh: true
          - data-id: public-mq.yaml
            refresh: true
