<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <property name="now" value="now()" dbms="h2"/>
    <property name="now" value="now()" dbms="mysql, mariadb"/>
    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb"/>
    <property name="clobType" value="clob" dbms="h2"/>
    <property name="clobType" value="clob" dbms="mysql, oracle, mssql, mariadb, postgresql"/>
    <property name="uuidType" value="varchar(36)" dbms="h2, mysql, mariadb"/>
    <property name="datetimeType" value="datetime(6)" dbms="mysql, mariadb"/>
    <property name="datetimeType" value="datetime" dbms="oracle, mssql, postgresql, h2"/>

    <include file="config/liquibase/changelog/init/1666262757633_added_entity_ActivityUnique.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757656_added_entity_AsyncRecord.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757656_added_entity_ColumnDict.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757656_added_entity_ColumnPreference.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_EnumCurrency.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_EnumDegree.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_EnumIndustry.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_EnumJobFunction.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_EnumLanguage.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_EnumUserResponsibility.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_EnumWorkAuthorization.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_Job.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_JobAdditionalInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_JobBoolString.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_JobCompanyContactRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_JobIpgRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_JobJobFunctionRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757658_added_entity_JobLocation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_JobNote.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_JobPreferredDegreeRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_JobPreferredLanguagesRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757659_added_entity_JobRequiredLanguagesRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_ReportUserJobTalent.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_SendEmailRecord.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1668672803663_added_entity_SearchPreference.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_EnumCountry.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1689823521530_added_entity_EnumMotivation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1682007626736_added_entity_JobFolder.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1682007626736_added_entity_JobFolderRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1682007626736_added_entity_JobSearchFolder.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1683582004897_added_entity_JobFolderSharingUser.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1683582004897_added_entity_JobFolderSharingTeam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1681924426000_added_entity_EnumJobPriority.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1681924426000_added_entity_JobParticipant.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1686009517453_added_entity_ActivityConfig.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1698798127123_added_entity_JobSharingPlatform.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1706461820234_added_entity_JobProject.xml" relativeToChangelogFile="false"/>
    <include file="/config/liquibase/changelog/init/20241226140000_added_entity_EnumGenderIdentity.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20241226140300_added_entity_EnumVeteran.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20241226140500_added_entity_EnumPreferredPronoun.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20241226140700_added_entity_EnumEthnicity.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250122090005_added_entity_EnumLevelOfExperience.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250207090000_added_entity_EnumIndustryMapping.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250207090001_added_entity_EnumJobFunctionMapping.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250207090002_added_entity_mapping_industry.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250207090003_added_entity_mapping_job_function.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250225090003_added_entity_job_poster.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250225090004_added_entity_job_poster_image.xml" relativeToChangelogFile="false"/>

</databaseChangeLog>
