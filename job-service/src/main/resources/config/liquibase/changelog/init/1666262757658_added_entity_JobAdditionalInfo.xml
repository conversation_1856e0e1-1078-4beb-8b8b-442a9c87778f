<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263664504-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_additional_info"/>
            </not>
        </preConditions>
        <createTable tableName="job_additional_info">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="extended_info" type="MEDIUMTEXT"/>
            <column name="public_desc" type="MEDIUMTEXT"/>
            <column name="responsibilities" type="MEDIUMTEXT"/>
            <column name="summary" type="MEDIUMTEXT"/>
            <column name="requirements" type="MEDIUMTEXT"/>
            <column name="puser_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="pteam_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <createIndex indexName="index_job_additional_info_pteam_id" tableName="job_additional_info_relation">
            <column name="pteam_id"/>
        </createIndex>
        <createIndex indexName="index_job_additional_info_puser_id" tableName="job_additional_info_relation">
            <column name="puser_id"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>
