<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity JobParticipant.
    -->
    <changeSet id="1698798127123-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_sharing_platform"/>
            </not>
        </preConditions>
        <createTable tableName="job_sharing_platform">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="job_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="user_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="platform_type" type="int">
                <constraints nullable="false" />
            </column>
            <column name="display_language_id" type="int">
                <constraints nullable="false" />
            </column>
            <column name="uuid" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column name="job_sharing_content" type="MEDIUMTEXT">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="puser_id" type="bigint"/>
            <column name="pteam_id" type="bigint"/>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <createIndex indexName="index_job_sharing_platform_job_id" tableName="job_sharing_platform">
            <column name="job_id"/>
        </createIndex>
        <createIndex indexName="index_job_sharing_platform_user_id" tableName="job_sharing_platform">
            <column name="user_id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>