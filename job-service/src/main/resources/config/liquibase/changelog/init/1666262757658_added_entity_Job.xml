<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263654106-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job"/>
            </not>
        </preConditions>
        <!-- 创建表结构 -->
        <createTable tableName="job" >
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tenant_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="company_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="title" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="start_date" type="timestamp(3)"/>
            <column name="end_date" type="timestamp(3)"/>
            <column name="posting_time" type="timestamp(3)"/>
            <column name="open_time" type="timestamp(3)"/>
            <column name="status" type="int" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="jd_url" type="varchar(255)"/>
            <column name="jd_has_display" type="bit"/>
            <column name="flexible_location" type="bit"/>
            <column name="code" type="varchar(255)"/>
            <column name="is_visible" type="tinyint" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="openings" type="int" defaultValue="1"/>
            <column name="max_submissions" type="int"/>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="created_date" type="timestamp(3)" defaultValueComputed="CURRENT_TIMESTAMP(3)">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp(3)"/>
            <column name="currency" type="int"/>
            <column name="last_edited_time" type="timestamp(3)"/>
            <column name="last_sync_time" type="timestamp(3)"/>
            <column name="sync_paused" type="int" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="puser_id" type="bigint"/>
            <column name="pteam_id" type="bigint"/>
            <column name="additional_info_id" type="bigint"/>
            <column name="minimum_degree_id" type="bigint"/>
            <column name="recruitment_process_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="recruitment_process_id" type="bigint">
                <constraints nullable="false"/>
            </column>
            <column name="enum_priority_id" type="tinyint"/>
            <column name="last_non_open_time" type="timestamp(3)"/>
            <column name="created_date_los_angeles" type="date"  >
                <constraints nullable="false"/>
            </column>
            <column name="created_date_shanghai" type="date"  >
                <constraints nullable="false"/>
            </column>
            <column name="created_date_new_york" type="date"  >
                <constraints nullable="false"/>
            </column>
            <column name="end_date_format" type="date"  />
            <column name="start_date_format" type="date"  />
            <column name="sales_lead_id" type="bigint"/>
            <column name="cooperation_status" type="varchar(128)"/>
            <column name="is_need_sync_hr" type="tinyint(1)" defaultValue="0"/>
            <column name="contract_duration" type="int"/>
        </createTable>

        <!-- 添加索引 -->
        <createIndex tableName="job" indexName="fk_job_company_entity_id">
            <column name="company_id"/>
        </createIndex>
        <createIndex tableName="job" indexName="index_job_created_date">
            <column name="created_date"/>
        </createIndex>
        <createIndex tableName="job" indexName="idxjob_status">
            <column name="status"/>
        </createIndex>
        <createIndex tableName="job" indexName="idx_last_sync_time">
            <column name="last_sync_time"/>
        </createIndex>
        <createIndex tableName="job" indexName="index_job_posting_time">
            <column name="posting_time"/>
        </createIndex>
        <createIndex tableName="job" indexName="index_job_enum_priority_id">
            <column name="enum_priority_id"/>
        </createIndex>
        <createIndex tableName="job" indexName="index_job_recruitment_process_id">
            <column name="recruitment_process_id"/>
        </createIndex>
        <createIndex tableName="job" indexName="idx_additional_info">
            <column name="id"/>
            <column name="additional_info_id"/>
        </createIndex>
        <createIndex tableName="job" indexName="idx_job_puser_id">
            <column name="puser_id"/>
        </createIndex>
        <createIndex tableName="job" indexName="idx_job_created_date_los_angeles">
            <column name="tenant_id"/>
            <column name="created_date_los_angeles"/>
        </createIndex>
        <createIndex tableName="job" indexName="idx_job_created_date_shanghai">
            <column name="tenant_id"/>
            <column name="created_date_shanghai"/>
        </createIndex>
        <createIndex tableName="job" indexName="idx_job_created_date_new_york">
            <column name="tenant_id"/>
            <column name="created_date_new_york"/>
        </createIndex>
        <createIndex tableName="job" indexName="idx_job_created_start_date_format">
            <column name="tenant_id"/>
            <column name="start_date_format"/>
        </createIndex>
        <createIndex tableName="job" indexName="idx_job_pteam_id">
            <column name="pteam_id"/>
        </createIndex>
        <createIndex tableName="job" indexName="idx_job_tenant_id_index">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>


</databaseChangeLog>
