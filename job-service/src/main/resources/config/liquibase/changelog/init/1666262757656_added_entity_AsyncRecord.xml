<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263221095-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="async_record"/>
            </not>
        </preConditions>
        <createTable tableName="async_record">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="async_type" type="INT"/>
            <column name="data_type" type="INT"/>
            <column name="data_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="status" type="INT"/>
            <column name="response_code" type="INT"/>
            <column name="response_message" type="MEDIUMTEXT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_async_record_async_type" tableName="async_record">
            <column name="async_type"/>
        </createIndex>
        <createIndex indexName="idx_async_record_data_id" tableName="async_record">
            <column name="data_id"/>
        </createIndex>
        <createIndex indexName="idx_async_record_data_type" tableName="async_record">
            <column name="data_type"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
