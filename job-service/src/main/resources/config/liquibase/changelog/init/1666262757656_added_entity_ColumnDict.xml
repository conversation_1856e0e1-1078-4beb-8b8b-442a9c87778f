<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263265809-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="column_dict"/>
            </not>
        </preConditions>
        <createTable tableName="column_dict">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="dict_code" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="ui_name" type="VARCHAR(100)"/>
            <column name="pcode" type="BIGINT"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="sort_order" type="INT"/>
            <column name="column_name" type="VARCHAR(100)"/>
            <column name="show_flag" type="BIT(1)"/>
            <column name="sort_flag" type="BIT(1)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
