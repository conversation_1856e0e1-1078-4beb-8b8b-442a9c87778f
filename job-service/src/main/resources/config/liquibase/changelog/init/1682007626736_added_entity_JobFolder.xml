<?xml version="1.0" encoding="utf-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity JobFolder.
    -->
    <changeSet id="1682011226736-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_folder"/>
            </not>
        </preConditions>
        <createTable tableName="job_folder">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(100)">
                <constraints nullable="false" />
            </column>
            <column name="folder_note" type="TEXT"/>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="tenant_id" type="BIGINT"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <createIndex indexName="index_job_folder_tenant_id" tableName="job_folder">
            <column name="tenant_id"/>
        </createIndex>
        <createIndex indexName="index_job_folder_puser_id" tableName="job_folder">
            <column name="puser_id"/>
        </createIndex>
        <createIndex indexName="index_job_folder_pteam_id" tableName="job_folder">
            <column name="pteam_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>