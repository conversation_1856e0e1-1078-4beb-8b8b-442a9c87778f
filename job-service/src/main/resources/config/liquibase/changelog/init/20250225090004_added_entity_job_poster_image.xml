<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20250225090004-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_poster_image"/>
            </not>
        </preConditions>
        <createTable tableName="job_poster_image">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="file_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="link_url" type="VARCHAR(500)">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>

            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="bigint"/>
            <column name="pteam_id" type="bigint"/>
        </createTable>
        <createIndex indexName="idx_type_status_puser_id" tableName="job_poster_image">
            <column name="type"/>
            <column name="status"/>
            <column name="puser_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
