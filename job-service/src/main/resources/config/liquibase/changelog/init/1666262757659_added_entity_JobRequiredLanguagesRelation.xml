<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263790167-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_required_languages_relation"/>
            </not>
        </preConditions>
        <createTable tableName="job_required_languages_relation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="job_id" type="BIGINT"/>
            <column name="required_languages_id" type="VARCHAR(20)">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="index_jrlr_job_id" tableName="job_required_languages_relation">
            <column name="job_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
