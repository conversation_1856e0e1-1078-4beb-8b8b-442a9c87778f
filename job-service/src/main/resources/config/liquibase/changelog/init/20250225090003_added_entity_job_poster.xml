<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20250225090003-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_poster"/>
            </not>
        </preConditions>
        <createTable tableName="job_poster">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="job_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="platform" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="template" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="job_poster_image_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="content" type="JSON"/>

            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="bigint"/>
            <column name="pteam_id" type="bigint"/>
        </createTable>
        <createIndex indexName="idx_job_id_puser_id" tableName="job_poster">
            <column name="job_id"/>
            <column name="puser_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
