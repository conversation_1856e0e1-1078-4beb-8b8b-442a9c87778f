<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264154751-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="report_user_job_talent"/>
            </not>
        </preConditions>
        <createTable tableName="report_user_job_talent">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="batch_id" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT"/>
            <column name="user_name" type="VARCHAR(50)"/>
            <column defaultValueNumeric="0" name="talent_count" type="BIGINT"/>
            <column name="talent_id" type="LONGTEXT"/>
            <column defaultValueNumeric="0" name="job_count" type="BIGINT"/>
            <column name="job_id" type="LONGTEXT"/>
        </createTable>

        <createIndex indexName="idx_report_user_job_talent_bid" tableName="report_user_job_talent">
            <column name="batch_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
