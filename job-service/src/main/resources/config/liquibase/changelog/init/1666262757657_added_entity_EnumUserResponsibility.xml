<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263545960-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="enum_user_responsibility"/>
            </not>
        </preConditions>
        <createTable tableName="enum_user_responsibility">
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="label" type="VARCHAR(255)"/>
            <column defaultValueBoolean="true" name="status" remarks="1:activated 0:invalid" type="BIT(1)"/>
            <column name="talent_es_key" type="VARCHAR(32)"/>
            <column name="talent_note_es_key" type="VARCHAR(32)"/>
            <column name="application_es_key" type="VARCHAR(32)"/>
            <column name="company_es_key" type="VARCHAR(32)"/>
            <column name="job_es_key" type="VARCHAR(32)"/>
            <column name="folders_of_pre_submit_talents_es_key" type="VARCHAR(32)"/>
            <column name="account_es_key " type="VARCHAR(64)"/>
        </createTable>

<!--        <loadData encoding="UTF-8"-->
<!--                  file="config/liquibase/changelog/data/enum_user_responsibility.csv"-->
<!--                  separator=","-->
<!--                  tableName="enum_user_responsibility"/>-->
    </changeSet>
</databaseChangeLog>
