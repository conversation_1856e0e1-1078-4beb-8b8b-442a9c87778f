<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263497336-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="enum_country"/>
            </not>
        </preConditions>
        <createTable tableName="enum_country">
            <column name="name" type="CHAR(64)"/>
            <column name="extractor_pattern" type="TEXT"/>
            <column name="regulator_pattern" type="TEXT"/>
            <column name="country_code" type="CHAR(8)"/>
            <column name="official_language_1" type="INT"/>
            <column name="official_language_2" type="INT"/>
            <column name="official_language_3" type="INT"/>
            <column name="cn_display" type="CHAR(64)"/>
            <column name="en_display" type="CHAR(64)"/>
            <column name="id" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="zipcode_pattern" type="TEXT"/>
            <column name="cn_sort_order" type="INT"/>
            <column name="en_sort_order" type="INT"/>
            <column name="phone_area_code" type="INT"/>
        </createTable>
        <sql>
            ALTER TABLE enum_country
            ADD COLUMN country_code_3 CHAR(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
        </sql>
    </changeSet>

</databaseChangeLog>
