<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263516569-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="enum_industry"/>
            </not>
        </preConditions>
        <createTable tableName="enum_industry">
            <column name="name" type="CHAR(128)"/>
            <column name="pattern" type="TEXT"/>
            <column name="sub_category_pattern" type="TEXT"/>
            <column name="cn_display" type="CHAR(128)"/>
            <column name="en_display" type="CHAR(128)"/>
            <column name="parent_category" type="CHAR(128)"/>
            <column name="tier" type="TINYINT(3)"/>
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="en_display_order" type="INT"/>
            <column name="cn_display_order" type="INT"/>
            <column name="sub_job_functions" type="JSON"/>
        </createTable>

        <createIndex indexName="name" tableName="enum_industry">
            <column name="name"/>
        </createIndex>
        <createIndex indexName="parent_category" tableName="enum_industry">
            <column name="parent_category"/>
        </createIndex>
        <addForeignKeyConstraint baseColumnNames="parent_category" baseTableName="enum_industry"
                                 constraintName="enum_industry_ibfk_1" deferrable="false" initiallyDeferred="false"
                                 onDelete="RESTRICT" onUpdate="RESTRICT" referencedColumnNames="name"
                                 referencedTableName="enum_industry" validate="true"/>

<!--        <loadData encoding="UTF-8"-->
<!--                  file="config/liquibase/changelog/data/enum_industry.csv"-->
<!--                  separator=","-->
<!--                  tableName="enum_industry"/>-->
    </changeSet>

</databaseChangeLog>
