<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity JobProject.
    -->
    <changeSet id="1706461820234-1" author="longfei.w">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_project"/>
            </not>
        </preConditions>
        <createTable tableName="job_project">
            <column name="id" type="bigint" autoIncrement="true" startWith="10000000000">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="name" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column name="tenant_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="puser_id" type="bigint"/>
            <column name="pteam_id" type="bigint"/>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
        <createIndex indexName="idx_job_project_tenant_id" tableName="job_project" unique="true">
            <column name="tenant_id"/>
        </createIndex>
        <createIndex indexName="idx_job_project_puser_id" tableName="job_project">
            <column name="puser_id"/>
        </createIndex>
        <createIndex indexName="idx_job_project_pteam_id" tableName="job_project">
            <column name="pteam_id"/>
        </createIndex>

    </changeSet>

    <!--
    <changeSet id="1706461820234-2" author="longfei.w">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_project_job_relation"/>
            </not>
        </preConditions>
        <createTable tableName="job_project_job_relation">
            <column name="id" type="bigint" autoIncrement="true" >
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="job_project_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="job_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="puser_id" type="bigint"/>
            <column name="pteam_id" type="bigint"/>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
        <createIndex indexName="idx_job_project_relation_project_id" tableName="job_project_job_relation" unique="true">
            <column name="job_project_id"/>
            <column name="job_id"/>
        </createIndex>
        <createIndex indexName="idx_job_project_relation_job_id" tableName="job_project_job_relation">
            <column name="job_id"/>
        </createIndex>
        <createIndex indexName="idx_job_project_relation_puser_id" tableName="job_project_job_relation">
            <column name="puser_id"/>
        </createIndex>
        <createIndex indexName="idx_job_project_relation_pteam_id" tableName="job_project_job_relation">
            <column name="pteam_id"/>
        </createIndex>

    </changeSet>
    -->

    <changeSet id="1706461820234-3" author="longfei.w">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_project_sharing"/>
            </not>
        </preConditions>
        <createTable tableName="job_project_sharing">
            <column name="id" type="bigint" autoIncrement="true" >
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="job_project_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="user_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="puser_id" type="bigint"/>
            <column name="pteam_id" type="bigint"/>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <createIndex indexName="idx_job_project_sharing_project_id" tableName="job_project_sharing" unique="true">
            <column name="job_project_id"/>
            <column name="user_id"/>
        </createIndex>
        <createIndex indexName="idx_job_project_sharing_user_id" tableName="job_project_sharing">
            <column name="user_id"/>
        </createIndex>
        <createIndex indexName="idx_job_project_sharing_puser_id" tableName="job_project_sharing">
            <column name="puser_id"/>
        </createIndex>
        <createIndex indexName="idx_job_project_sharing_pteam_id" tableName="job_project_sharing">
            <column name="pteam_id"/>
        </createIndex>


    </changeSet>

</databaseChangeLog>