<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263719136-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_ipg_relation"/>
            </not>
        </preConditions>
        <createTable tableName="job_ipg_relation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="apn_job_id" type="BIGINT"/>
            <column name="ipg_job_id" type="BIGINT"/>
            <column name="apn_job_status" type="TINYINT(3)"/>
            <column name="ipg_job_status" type="TINYINT(3)"/>
            <column name="ipg_job_type" type="TINYINT(3)"/>
            <column name="ipg_job_description" type="TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci"/>
            <column name="ipg_job_responsibilities" type="MEDIUMTEXT"/>
            <column name="ipg_job_summary" type="MEDIUMTEXT"/>
            <column name="ipg_job_requirements" type="MEDIUMTEXT"/>
            <column name="ipg_job_required_skills" type="MEDIUMTEXT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_job_ipg_relation_apn_job_id" tableName="job_ipg_relation">
            <column name="apn_job_id"/>
        </createIndex>
        <createIndex indexName="idx_job_ipg_relation_ipg_job_id" tableName="job_ipg_relation">
            <column name="ipg_job_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
