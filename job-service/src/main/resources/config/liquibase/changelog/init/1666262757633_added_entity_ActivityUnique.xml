<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263073775-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="activity_unique"/>
            </not>
        </preConditions>
        <createTable tableName="activity_unique">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="memo" type="VARCHAR(5000)"/>
            <column name="status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="job_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="application_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="event_date" type="timestamp"/>
            <column name="event_time_zone" type="VARCHAR(55)"/>
            <column name="event_type" type="INT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="location" type="VARCHAR(255)"/>
        </createTable>

        <addUniqueConstraint columnNames="status, application_id" constraintName="idx_activity_unique_appId_status"
                             tableName="activity_unique"/>
        <createIndex indexName="idx_activity_unique_ai" tableName="activity_unique">
            <column name="application_id"/>
        </createIndex>
        <createIndex indexName="idx_activity_unique_cd" tableName="activity_unique">
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date"/>
        </createIndex>
        <createIndex indexName="idx_activity_unique_job_status" tableName="activity_unique">
            <column name="job_id"/>
            <column name="status"/>
        </createIndex>
        <createIndex indexName="idx_activity_unique_st" tableName="activity_unique">
            <column name="status"/>
        </createIndex>
        <createIndex indexName="idx_activity_unique_ti" tableName="activity_unique">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
