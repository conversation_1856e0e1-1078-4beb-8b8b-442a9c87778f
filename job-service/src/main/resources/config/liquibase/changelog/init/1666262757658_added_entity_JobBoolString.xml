<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263675493-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_bool_string"/>
            </not>
        </preConditions>
        <createTable tableName="job_bool_string">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="job_id" type="BIGINT"/>
            <column name="relationship" type="VARCHAR(10)"/>
            <column name="section" type="VARCHAR(50)"/>
            <column name="score" type="DOUBLE"/>
            <column name="strings" type="VARCHAR(500)"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
