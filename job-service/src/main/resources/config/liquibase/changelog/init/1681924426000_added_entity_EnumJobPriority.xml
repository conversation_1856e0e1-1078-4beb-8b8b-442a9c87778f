<?xml version="1.0" encoding="utf-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity EnumJobPriority.
    -->
    <changeSet id="1681924426705-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="enum_job_priority"/>
            </not>
        </preConditions>
        <createTable tableName="enum_job_priority">
            <column name="id" type="int" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="level" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="name" type="varchar(10)">
                <constraints nullable="false"/>
            </column>
            <column name="en_display" type="varchar(10)">
                <constraints nullable="false"/>
            </column>
            <column name="cn_display" type="varchar(10)"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

</databaseChangeLog>