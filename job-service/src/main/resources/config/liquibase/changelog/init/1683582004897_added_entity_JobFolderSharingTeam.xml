<?xml version="1.0" encoding="utf-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity JobFolderSharing.
    -->
    <changeSet id="1682011226240-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_folder_sharing_team"/>
            </not>
        </preConditions>
        <createTable tableName="job_folder_sharing_team">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="job_folder_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="team_id" type="bigint">
                <constraints nullable="false" />
            </column>
            <column name="permission" type="tinyint(3)">
                <constraints nullable="false" />
            </column>
            <column name="excluded_user_ids" type="json"/>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="bigint"/>
            <column name="pteam_id" type="bigint"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <createIndex indexName="index_job_folder_sharing_team_job_folder_id" tableName="job_folder_sharing_team">
            <column name="job_folder_id"/>
        </createIndex>
        <createIndex indexName="index_job_folder_sharing_team_team_id" tableName="job_folder_sharing_team">
            <column name="team_id"/>
        </createIndex>
        <createIndex indexName="index_job_folder_sharing_team_puser_id" tableName="job_folder_sharing_team">
            <column name="puser_id"/>
        </createIndex>
        <createIndex indexName="index_job_folder_sharing_team_pteam_id" tableName="job_folder_sharing_team">
            <column name="pteam_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>