<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263497336-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="enum_currency"/>
            </not>
        </preConditions>
        <createTable tableName="enum_currency">
            <column name="name" type="CHAR(64)"/>
            <column name="from_usd_rate" type="FLOAT(12)"/>
            <column name="to_usd_rate" type="FLOAT(12)"/>
            <column name="pattern" type="TEXT"/>
            <column name="cn_display" type="CHAR(64)"/>
            <column name="en_display" type="CHAR(64)"/>
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="en_display_order" type="INT"/>
            <column name="cn_display_order" type="INT"/>
            <column name="symbol" type="VARCHAR(255)"/>
            <column name="code_symbol" type="VARCHAR(255)"/>
            <column name="country_code_symbol" type="VARCHAR(255)"/>
        </createTable>

        <createIndex indexName="name" tableName="enum_currency">
            <column name="name"/>
        </createIndex>

<!--        <loadData encoding="UTF-8"-->
<!--                  file="config/liquibase/changelog/data/enum_currency.csv"-->
<!--                  separator=","-->
<!--                  tableName="enum_currency"/>-->
    </changeSet>

</databaseChangeLog>
