<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263759503-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_note"/>
            </not>
        </preConditions>
        <createTable tableName="job_note">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <!--            <column name="title" type="VARCHAR(255)"/>-->
            <column name="note" type="TEXT"/>
            <!--            <column name="visible" type="BIT(1)"/>-->
            <!--            <column name="priority" type="INT"/>-->
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="job_id" type="BIGINT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
            <column name="sync_to_calendar" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="alert_time" type="timestamp"/>
        </createTable>

        <createIndex indexName="fk_job_note_job_id" tableName="job_note">
            <column name="job_id"/>
        </createIndex>
        <createIndex indexName="index_job_note_puser_id" tableName="job_note">
            <column name="puser_id"/>
        </createIndex>
        <createIndex indexName="index_job_note_pteam_id" tableName="job_note">
            <column name="pteam_id"/>
        </createIndex>
<!--        <createIndex indexName="fk_job_note_user_id" tableName="job_note">-->
<!--            <column name="user_id"/>-->
<!--        </createIndex>-->
<!--        <addForeignKeyConstraint baseColumnNames="user_id" baseTableName="job_note" constraintName="fk_job_note_user_id"-->
<!--                                 deferrable="false" initiallyDeferred="false" onDelete="RESTRICT" onUpdate="RESTRICT"-->
<!--                                 referencedColumnNames="id" referencedTableName="user" validate="true"/>-->
    </changeSet>

</databaseChangeLog>