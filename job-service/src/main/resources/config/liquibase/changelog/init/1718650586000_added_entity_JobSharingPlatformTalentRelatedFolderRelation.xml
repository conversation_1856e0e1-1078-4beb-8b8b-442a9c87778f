<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity job_sharing_platform_talent_related_folder_relation.
    -->
    <changeSet id="1698798127123-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_sharing_platform_talent_related_job_folder_relation"/>
            </not>
        </preConditions>
        <createTable tableName="job_sharing_platform_talent_related_job_folder_relation">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="talent_association_job_folder_folder_id" type="bigint">
                <constraints nullable="false" />
            </column>

            <column name="shared_link_expire_time" type="timestamp">
                <constraints nullable="false" />
            </column>
            <column name="created_by" type="varchar(50)">
                <constraints nullable="false" />
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="puser_id" type="bigint"/>
            <column name="pteam_id" type="bigint"/>
            <column name="last_modified_by" type="varchar(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <createIndex indexName="idx_js_platform_talent_rel_folder_rel_job_id" tableName="job_sharing_platform_talent_related_job_folder_relation">
            <column name="job_id"/>
        </createIndex>
        <createIndex indexName="idx_js_platform_talent_rel_folder_rel_folder_id" tableName="job_sharing_platform_talent_related_job_folder_relation">
            <column name="related_folder_Id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>