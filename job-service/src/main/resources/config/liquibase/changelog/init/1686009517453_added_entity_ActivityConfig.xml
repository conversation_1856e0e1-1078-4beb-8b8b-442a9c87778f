<?xml version="1.0" encoding="utf-8" standalone="no"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity ActivityConfig.
    -->
    <changeSet id="1686009517354-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="activity_config"/>
            </not>
        </preConditions>
        <createTable tableName="activity_config">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="source_type" type="SMALLINT">
                <constraints nullable="false"/>
            </column>
            <column name="field_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="column_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="service_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="service_method_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="args_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>
</databaseChangeLog>