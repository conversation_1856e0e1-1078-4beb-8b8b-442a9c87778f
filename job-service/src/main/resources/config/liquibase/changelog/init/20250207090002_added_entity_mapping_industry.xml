<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="20250207090002-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="mapping_industry"/>
            </not>
        </preConditions>
        <createTable tableName="mapping_industry">
            <column name="mapping_id" type="INT"/>
            <column name="industry_id" type="INT"/>
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
        </createTable>
        <createIndex indexName="name" tableName="mapping_industry">
            <column name="industry_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
