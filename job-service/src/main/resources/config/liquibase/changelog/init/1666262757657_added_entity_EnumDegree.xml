<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263507041-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="enum_degree"/>
            </not>
        </preConditions>
        <createTable tableName="enum_degree">
            <column name="name" type="CHAR(64)"/>
            <column name="en_case_ignore_extractor_pattern" type="TEXT"/>
            <column name="en_case_sensitive_pattern" type="TEXT"/>
            <column name="en_regulator_pattern" type="TEXT"/>
            <column name="cn_extractor_pattern" type="TEXT"/>
            <column name="cn_regulator_pattern" type="TEXT"/>
            <column name="score" type="TINYINT(3)"/>
            <column name="en_display" type="CHAR(64)"/>
            <column name="cn_display" type="CHAR(64)"/>
            <column autoIncrement="true" name="id" type="INT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="en_display_order" type="INT"/>
            <column name="cn_display_order" type="INT"/>
        </createTable>

        <createIndex indexName="name" tableName="enum_degree">
            <column name="name"/>
        </createIndex>

<!--        <loadData encoding="UTF-8"-->
<!--                  file="config/liquibase/changelog/data/enum_degree.csv"-->
<!--                  separator=","-->
<!--                  tableName="enum_degree"/>-->
    </changeSet>

</databaseChangeLog>
