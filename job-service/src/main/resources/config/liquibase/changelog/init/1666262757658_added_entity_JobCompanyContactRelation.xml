<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263686299-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="job_company_contact_relation"/>
            </not>
        </preConditions>
        <createTable tableName="job_company_contact_relation">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="contact_category" type="TINYINT(3)">
                <constraints nullable="false"/>
            </column>
            <column name="client_contact_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="job_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="talent_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="index_jccr_job_id" tableName="job_company_contact_relation">
            <column name="job_id"/>
        </createIndex>
        <createIndex indexName="index_jccr_talent_id" tableName="job_company_contact_relation">
            <column name="talent_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
