package com.altomni.apn.job.service.dto.esfiller;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class JobUpdateField implements Serializable {

    private static final long serialVersionUID = -2530912058148263714L;

    private String companyName;

    private List<String> industries;

    private List<String> industryDisplays;

    public JobUpdateField companyName(String companyName, List<String> industries, List<String> industryDisplays) {
        this.companyName = companyName;
        this.industries = industries;
        this.industryDisplays = industryDisplays;
        return this;
    }

}
