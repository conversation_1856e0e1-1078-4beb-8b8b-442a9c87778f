package com.altomni.apn.job.service.dto.job;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.dto.talent.UserResponsibility;
import com.altomni.apn.common.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobESSourceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String _index;

    private String _type;

    private Long _id;

    private BigDecimal _score;

    private JobESDocument _source;

    public static JobESSearchDTO toJobESDocument(JobESSourceDTO jobESSourceDTO) {
        JobESSearchDTO jobEsSearchDto = new JobESSearchDTO();
        String[] ignoreArrays = new String[]{"currency"};
        BeanUtil.copyProperties(jobESSourceDTO.get_source(), jobEsSearchDto, ignoreArrays);
        //id
        jobEsSearchDto.setId(jobESSourceDTO.get_id());
        //currency
        if (ObjectUtil.isNotNull(jobESSourceDTO.get_source())) {
            jobEsSearchDto.setCurrency(jobESSourceDTO.get_source().getCurrency());
        }
        //jobType
        if (ObjectUtil.isNotEmpty(jobEsSearchDto.getType())) {
            if (jobEsSearchDto.getType() instanceof ArrayList || jobEsSearchDto.getType() instanceof String[] || jobEsSearchDto.getType() instanceof cn.hutool.json.JSONArray) {
                List<String> typeList = Convert.toList(String.class, jobEsSearchDto.getType());
                jobEsSearchDto.setType(String.join(StrUtil.UNDERLINE + Constants.OR + StrUtil.UNDERLINE, typeList));
            }
        }
        //companyId
        if(ObjectUtil.isNotNull(jobESSourceDTO.get_source().getCompanyId())) {
            jobEsSearchDto.setCompanyId(Long.parseLong(jobESSourceDTO.get_source().getCompanyId()));
        }
        //account manager
        if(CollUtil.isNotEmpty(jobESSourceDTO.get_source().getUserResponsibility1())) {
//            jobEsSearchDto.setAmName(jobESSourceDTO.get_source().getUserResponsibility1().stream().map(UserResponsibility::getUserName).collect(Collectors.joining(",")));
//            jobEsSearchDto.setAmNames(jobEsSearchDto.getAmName());
            jobEsSearchDto.setAmIds(jobESSourceDTO.get_source().getUserResponsibility1().stream().map(s -> s.getUserId()).collect(Collectors.toList()));
        }
        //created by
        if(CollUtil.isNotEmpty(jobESSourceDTO.get_source().getUserResponsibility5())) {
            jobEsSearchDto.setCreatedById(jobESSourceDTO.get_source().getUserResponsibility5().stream().map(UserResponsibility::getUserId).findAny().get());
        }
        //hiringManager
        if(ObjectUtil.isNotEmpty(jobESSourceDTO.get_source().getHiringManager())) {
            jobEsSearchDto.setHiringManager(ObjectUtil.isEmpty(jobESSourceDTO.get_source().getHiringManager().getString("name")) ? null : jobESSourceDTO.get_source().getHiringManager().getString("name"));
        }
        //postingTime
        jobEsSearchDto.setPostingTime(jobESSourceDTO.get_source().getPostingTime() == null ? null : DateUtil.fromInstantToDate(jobESSourceDTO.get_source().getPostingTime()));
        jobEsSearchDto.setJobFunctions(jobESSourceDTO._source.getJobFunctionDisplays());
        return jobEsSearchDto;
    }

}
