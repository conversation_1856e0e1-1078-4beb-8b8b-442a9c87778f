package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.job.config.ScheduledProperties;
import com.altomni.apn.job.config.env.EsfillerMQProperties;
import com.altomni.apn.job.config.thread.*;
import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.altomni.apn.job.service.job.JobSyncService;
import com.altomni.apn.job.service.rabbitmq.RabbitMqService;
import com.altomni.apn.job.web.rest.vm.MqMessageCountVM;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

@Service
public class JobSyncServiceImpl implements JobSyncService {

    private final Logger log = LoggerFactory.getLogger(JobSyncServiceImpl.class);

    @Resource
    private EsFillerJobService esFillerJobService;

    @Resource
    private ScheduledProperties properties;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource
    private CanalService canalService;


    // 使用 volatile 确保可见性
    private volatile ExecutorService executorService;
    private final AtomicInteger configVersion = new AtomicInteger(0);
    private final Object lock = new Object();  // 专用锁对象

    private ExecutorService getExecutorService() {
        int currentVersion = configVersion.get();
        int newThreadNum = properties.getThreadNum();

        // 检查是否需要刷新线程池
        if (executorService == null || currentVersion != configVersion.get()) {
            synchronized (lock) {
                if (executorService == null || currentVersion != configVersion.get()) {
                    shutdownExecutor();  // 安全关闭旧线程池
                    createNewExecutor(newThreadNum);
                    configVersion.incrementAndGet();
                }
            }
        }
        return executorService;
    }

    private void shutdownExecutor() {
        if (executorService != null) {
            executorService.shutdown();  // 温和关闭
            try {
                if (!executorService.awaitTermination(30, TimeUnit.SECONDS)) {
                    log.warn("Thread pool did not terminate gracefully");
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                executorService.shutdownNow();
            }
        }
    }

    private void createNewExecutor(int threadNum) {
        executorService = new ThreadPoolExecutor(
                2,
                4,
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(500),
                ThreadUtil.newNamedThreadFactory("api-scheduled-sync-job-to-es", false),
                new CustomRejectPolicy() // 显式指定拒绝策略
        );
        log.info("Created new thread pool with size: {}", threadNum);
    }

    @Override
    public MqMessageCountVM checkJobMqMessageCount() {
        Integer messageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getToEsFillerQueue());
        Integer jobMessageCount = rabbitMqService.checkMessageCount(esfillerMQProperties.getApnNormalizedJobQueue());
        log.debug("JOB_TO_ES_QUEUE messageCount= " + messageCount);
        return new MqMessageCountVM(Math.max(messageCount, jobMessageCount), esfillerMQProperties.getToEsFillerMaximumMsgCount());
    }

    @Override
    public void syncJobsToMQ(Collection<Long> jobIds, int priority) {
        log.info("[SyncJobsToMQ] sync jobs start at: {}", LocalDateTime.now());
        List<List<Long>> jobGroups = CollUtil.split(jobIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(jobGroups.size());
        jobGroups.forEach(ids -> getExecutorService().execute(new JobSyncToMqThreadTask(esFillerJobService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[SyncJobsToMQ] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[SyncJobsToMQ] countDownLatch Error" +
                    "\n\tJob ID: " + jobIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[SyncJobsToMQ] Sync Jobs to MQ Done!");
    }

    @Override
    public void bulkSyncJobsToMQ(Collection<Long> jobIds, int priority) {
        log.info("[SyncJobsToMQ] bulk sync jobs start at: {}", LocalDateTime.now());
        List<List<Long>> jobGroups = CollUtil.split(jobIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(jobGroups.size());
        jobGroups.forEach(ids -> getExecutorService().execute(new JobBulkSyncToMqThreadTask(esFillerJobService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[SyncJobsToMQ] bulk countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[SyncJobsToMQ] countDownLatch Error" +
                    "\n\tJob ID: " + jobIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[SyncJobsToMQ] bulk Sync Jobs to MQ Done!");
    }

    @Override
    public void syncJobsToHrMQ(Collection<Long> jobIds, int priority) {
        log.info("[syncJobsToHrMQ] sync jobs to hr start at: {}", LocalDateTime.now());
        List<List<Long>> jobGroups = CollUtil.split(jobIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(jobGroups.size());
        jobGroups.forEach(ids -> getExecutorService().execute(new JobSyncToHrMqThreadTask(esFillerJobService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[syncJobsToHrMQ] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[syncJobsToHrMQ] countDownLatch Error" +
                    "\n\tJob ID: " + jobIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[SyncJobsToMQ] Sync Jobs to hr MQ Done!");
    }

    @Override
    public void syncJobsToAgencyMQ(Collection<Long> jobIds, int priority) {
        log.info("[SyncJobsToAgencyMQ] sync jobs start at: {}", LocalDateTime.now());
        List<List<Long>> jobGroups = CollUtil.split(jobIds, 5);
        CountDownLatch countDownLatch = new CountDownLatch(jobGroups.size());
        jobGroups.forEach(ids -> getExecutorService().execute(new JobSyncToAgencyMqThreadTask(esFillerJobService, countDownLatch, ids, priority, esfillerMQProperties, canalService)));
        try {
            countDownLatch.await(30, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.error("[SyncJobsToAgencyMQ] countDownLatch is error message = [{}]", ExceptionUtils.getStackTrace(e));
            canalService.insertAll(jobIds, SyncIdTypeEnum.JOB, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "[SyncJobsToAgencyMQ] countDownLatch Error" +
                    "\n\tJob ID: " + jobIds +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(esfillerMQProperties.getLarkWebhookKey(), esfillerMQProperties.getLarkWebhookUrl(), message);
        }
        log.info("[SyncJobsToAgencyMQ] Sync Jobs to Agency MQ Done!");
    }


}
