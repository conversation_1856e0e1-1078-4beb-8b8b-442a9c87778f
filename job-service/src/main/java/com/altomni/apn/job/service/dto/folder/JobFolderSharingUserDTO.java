package com.altomni.apn.job.service.dto.folder;


import com.altomni.apn.common.enumeration.folder.FolderPermission;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;


@Data
public class JobFolderSharingUserDTO {
    private Long id;
    private Long jobFolderId;
    private Long userId;
    @ApiModelProperty(value = "The permission for customFolder.", allowableValues = "READ, EDIT")
    @NotNull(message = "permission is required")
    private FolderPermission permission;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobFolderId() {
        return jobFolderId;
    }

    public void setJobFolderId(Long jobFolderId) {
        this.jobFolderId = jobFolderId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public FolderPermission getPermission() {
        return permission;
    }

    public void setPermission(FolderPermission permission) {
        this.permission = permission;
    }
}
