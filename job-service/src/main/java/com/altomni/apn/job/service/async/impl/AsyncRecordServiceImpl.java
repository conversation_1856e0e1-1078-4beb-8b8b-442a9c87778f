package com.altomni.apn.job.service.async.impl;

import com.altomni.apn.job.domain.async.AsyncRecord;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;
import com.altomni.apn.job.repository.async.AsyncRecordRepository;
import com.altomni.apn.job.service.async.AsyncRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AsyncRecordServiceImpl implements AsyncRecordService {

    @Resource
    private AsyncRecordRepository asyncRecordRepository;

    @Override
    public void clearAllSyncRecordError(AsyncEnum dataType, List<Long> dataIds) {
        asyncRecordRepository.clearAllSyncRecordError(dataType, dataIds);
    }

    @Override
    public AsyncRecord save(AsyncRecord asyncRecord) {
        return asyncRecordRepository.save(asyncRecord);
    }

    @Override
    public Integer countSyncError(AsyncEnum dataType, Long dataId) {
        return asyncRecordRepository.countSyncError(dataType, dataId);
    }

    @Override
    public void updateSyncFailureToSuccess(AsyncEnum dataType, List<Long> ids) {
        asyncRecordRepository.updateSyncFailureToSuccess(dataType, ids);
    }
}
