package com.altomni.apn.job.domain.agency;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatusConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A RecruitmentProcessNode.
 */
@Entity
@Table(name = "agency_shared_job")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AgencyJobRelation extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 3821512723147087243L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "agency_id")
    private Long agencyId;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "share_status")
    @Convert(converter = JobShareStatusConverter.class)
    private JobShareStatus shareStatus;

}
