package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The FeeType enumeration.
 */
public enum FeeType implements ConvertedEnum<Integer> {
    PERCENTAGE(1), FLAT_AMOUNT(2);

    private final int dbValue;

    FeeType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<FeeType, Integer> resolver = new ReverseEnumResolver<>(FeeType.class, FeeType::toDbValue);

    public static FeeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
