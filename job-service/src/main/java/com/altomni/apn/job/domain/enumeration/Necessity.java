package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum Necessity implements ConvertedEnum<Integer> {
    UNSURE(0),
    PREFERRED(1),
    MUST_HAVE(2);

    public final int dbValue;

    Necessity(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<Necessity, Integer> resolver =
        new ReverseEnumResolver<>(Necessity.class, Necessity::toDbValue);

    public static Necessity fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
