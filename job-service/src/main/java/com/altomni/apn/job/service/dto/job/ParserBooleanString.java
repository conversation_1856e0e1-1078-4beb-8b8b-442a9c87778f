package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.job.domain.enumeration.Relationship;
import com.altomni.apn.job.domain.enumeration.Section;
import io.swagger.annotations.ApiModelProperty;

import java.util.Arrays;

public class ParserBooleanString {

    public String[] string;

    @ApiModelProperty(allowableValues = "AND, OR")
    public Relationship relationship;

    @ApiModelProperty(allowableValues = "Summary, Overview, Skill, JD_Responsibility, JD_Requirements, Appendix")
    public Section section;

    public double score;

    @Override
    public String toString() {
        return "SearchString{" +
            "string=" + Arrays.toString(string) +
            ", relationship='" + relationship + '\'' +
            ", section='" + section + '\'' +
            ", score=" + score +
            '}';
    }
}
