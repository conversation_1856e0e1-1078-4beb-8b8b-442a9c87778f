package com.altomni.apn.job.service.dto.jobsharing;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.job.PlatformType;
import com.altomni.apn.common.domain.enumeration.job.PlatformTypeConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class JobSharingPosterDTO {

    @ApiModelProperty(value = "jobId for poster")
    private Long jobId;

    @ApiModelProperty(value = "linkUrl for poster")
    @NotNull
    @Convert(converter = PlatformTypeConverter.class)
    private PlatformType platform;

    @ApiModelProperty(value = "images for poster")
    @NotEmpty
    @Valid
    private List<JobSharingPosterTemplateDTO> images;

    @ApiModelProperty(value = "content for poster")
    private JSONObject content;

}
