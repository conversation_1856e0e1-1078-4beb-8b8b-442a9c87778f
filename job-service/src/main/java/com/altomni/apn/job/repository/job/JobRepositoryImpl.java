package com.altomni.apn.job.repository.job;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.enumeration.NodeTypeTableEnum;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.job.QJobV3;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.domain.job.MyJob;
import com.altomni.apn.job.web.rest.vm.CompanyVM;
import com.altomni.apn.job.web.rest.vm.MyApplication;
import com.altomni.apn.job.web.rest.vm.MyJobVo;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigInteger;
import java.time.Instant;
import java.util.List;
import java.util.Objects;


/**
 * Spring Data JPA repository for the MyJob entity.
 */
@Service
public class JobRepositoryImpl implements JobRepositoryCustom, QuerydslBinderCustomizer<EntityPath<QJobV3>> {

    @Override
    public void customize(QuerydslBindings bindings, EntityPath<QJobV3> root) {
        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    }

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public List<MyJob> findMyJobsForDashboard(Long userId, Long companyId, Instant from, Instant to, Boolean myJobsOnly, Long tenantId) {



        String query = "select j.id, j.title as job_title, c.full_business_name as company, j.company_id " +
                ", count(if(tn.node_type >= 10, 1, null)) as applied " +
                ", IF(am.id IS NULL AND recruiter.id IS NULL AND sourcer.id IS NULL, NULL, COUNT(IF((recruiter.id IS NOT NULL OR sourcer.id IS NOT NULL) AND tn.node_type >= 10, 1, NULL))) as my_applied" +
                ", count(if(tn.node_type >= 20, 1, null)) as submitted " +
                ", IF(am.id IS NULL AND recruiter.id IS NULL AND sourcer.id IS NULL, NULL, COUNT(IF((recruiter.id IS NOT NULL OR sourcer.id IS NOT NULL) AND tn.node_type >= 20, 1, NULL))) as my_submitted " +
                ", count(if(tn.node_type >= 30, 1, null)) as interview " +
                ", IF(am.id IS NULL AND recruiter.id IS NULL AND sourcer.id IS NULL, NULL, COUNT(IF((recruiter.id IS NOT NULL OR sourcer.id IS NOT NULL) AND tn.node_type >= 30, 1, NULL))) as my_interview" +
                ", count(if(tn.node_type >= 40, 1, null)) as offered " +
                ", IF(am.id IS NULL AND recruiter.id IS NULL AND sourcer.id IS NULL, NULL, COUNT(IF((recruiter.id IS NOT NULL OR sourcer.id IS NOT NULL) AND tn.node_type >= 40, 1, NULL))) as my_offered " +
                " from job j" +
                " left join talent_recruitment_process pro on pro.job_id=j.id" +
                " left join talent_recruitment_process_node tn on pro.id = tn.talent_recruitment_process_id And tn.node_status in (1,4) " + // tn.node_status = 1
                " LEFT JOIN talent_recruitment_process_kpi_user am ON pro.id = am.talent_recruitment_process_id AND am.user_id = ?1 AND am.user_role = 0" +
                " LEFT JOIN talent_recruitment_process_kpi_user recruiter ON pro.id = recruiter.talent_recruitment_process_id AND recruiter.user_id = ?1 AND recruiter.user_role = 1" +
                " LEFT JOIN talent_recruitment_process_kpi_user sourcer ON pro.id = sourcer.talent_recruitment_process_id AND sourcer.user_id = ?1 AND sourcer.user_role = 2 " +
                " left join company c on c.id = j.company_id ";

        if (myJobsOnly) {
            query += "LEFT JOIN (select distinct trpku.user_id user_id,trpku.talent_recruitment_process_id talent_recruitment_process_id from talent_recruitment_process_kpi_user trpku where trpku.user_id = ?1 ) ujr ON pro.id = ujr.talent_recruitment_process_id  ";
        }
        query += " WHERE j.last_modified_date >= ?3 " +
            " AND j.last_modified_date <= ?4 " +
            " AND j.tenant_id = ?5 " +
            " AND j.company_id = COALESCE(?2, j.company_id) ";
        if (myJobsOnly) {
            //TODO  APN v3 participant？
            query += " AND (ujr.user_id is not null or j.puser_id = ?1) ";
        }

        query += "GROUP BY j.id ORDER BY j.last_modified_date DESC";

        List result = entityManager.createNativeQuery(query, MyJob.class)
            .setParameter(1, userId)
            .setParameter(2, companyId)
            .setParameter(3, from)
            .setParameter(4, to)
            .setParameter(5, tenantId).getResultList();
        return result;
    }

    @Override
    public List<MyApplication> findMyApplicationsForDashboard(Long userId, Long jobId, NodeType status) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT au.id as id, j.id as job_id, j.title as job_title, j.company_id, c.full_business_name as company, t.id as talent_id, t.full_name, tce.contact as email, tcp.contact as phone, au.last_modified_by, currentNode.last_modified_date ")
                .append(" FROM talent t ")
                .append(" LEFT JOIN talent_recruitment_process au ON au.talent_id = t.id ")
                .append(" LEFT JOIN talent_recruitment_process_node tn ON au.id = tn.talent_recruitment_process_id AND tn.node_status in (1,4) ");
        if (status == NodeType.INTERVIEW) {
            sb.append(" left join (select a.talent_recruitment_process_id, max(a.last_modified_date) last_modified_date from ").append(NodeTypeTableEnum.getTableNameByNodeType(status)).append(" a group by a.talent_recruitment_process_id) ").append(" currentNode on currentNode.talent_recruitment_process_id = au.id ");;
        } else {
            sb.append(" LEFT JOIN ").append(NodeTypeTableEnum.getTableNameByNodeType(status)).append(" currentNode on currentNode.talent_recruitment_process_id = au.id ");
        }
        sb.append(" LEFT JOIN job j ON j.id = au.job_id ")
                .append(" LEFT JOIN talent_recruitment_process_kpi_user tepku ON tepku.talent_recruitment_process_id = au.id ")
                .append(" LEFT JOIN company c ON c.id = j.company_id ")
                .append(" LEFT JOIN talent_contact tcp ON tcp.talent_id = t.id AND tcp.jhi_type = 16 ")
                .append(" LEFT JOIN talent_contact tce ON tce.talent_id = t.id AND tce.jhi_type = 2 ")
                .append(" WHERE au.job_id = ?1 ")
                .append(" AND tn.node_type >= ?2 ");
        if (ObjectUtil.isNotEmpty(userId)) {
            sb.append(" AND tepku.user_id = ").append(userId)
                    .append(" AND tepku.user_role IN ( 1, 2 )");
        }
        sb.append(" GROUP BY t.id ");
        Query query = entityManager.createNativeQuery(sb.toString(), MyApplication.class);
        query.setParameter(1, jobId);
        query.setParameter(2, status.toDbValue());
        return query.getResultList();
    }

    @Override
    public List<CompanyVM> findAllCompanyForDashboard(Long userId, Instant from, Instant to, Long tenantId) {
        String sql = " SELECT j.company_id as company_id, c.full_business_name as company " +
                " FROM job j " +
                " LEFT JOIN talent_recruitment_process apl ON apl.job_id = j.id " +
                " LEFT JOIN talent_recruitment_process_kpi_user commission ON commission.talent_recruitment_process_id = apl.id AND commission.user_role IN (1,2) " +
                " LEFT JOIN company c ON c.id = j.company_id " +
                " WHERE j.last_modified_date >= ?2 AND j.last_modified_date <= ?3 " +
                " AND j.tenant_id = ?4 " +
                " AND commission.user_id = COALESCE(?1, commission.user_id) " +
                " GROUP BY j.company_id " +
                " ORDER BY j.last_modified_date DESC";
        Query query = entityManager.createNativeQuery(sql, CompanyVM.class);
        query.setParameter(1, userId);
        query.setParameter(2, from);
        query.setParameter(3, to);
        query .setParameter(4, tenantId);
        return query.getResultList();
    }

    @Override
    public MyJobVo findMyJobs(List<Long> userIdList, Long tenantId, Instant startTime, Instant endTime, Long projectId) {
        MyJobVo vo = new MyJobVo();
        String sql = "SELECT STATUS status , count(DISTINCT id) count " +
                " FROM ( " +
                " SELECT j.STATUS, j.id  " +
                " FROM recruitment_process rp INNER JOIN job j ON j.recruitment_process_id = rp.id  " +
                " WHERE rp.job_type != :jobType AND j.STATUS IN ( 0, 5 ) and j.created_date BETWEEN :startTime AND :endTime " +
                " AND j.puser_id in ( :userId ) AND j.tenant_id = :tenantId " +
                " AND j.pteam_id != :projectId " +
                " UNION " +
                " SELECT j.STATUS, j.id  " +
                " FROM  recruitment_process rp  INNER JOIN job j ON j.recruitment_process_id = rp.id " +
                " LEFT JOIN user_job_relation ujr ON ujr.job_id = j.id AND ujr.status = 1  " +
                " WHERE rp.job_type != :jobType  AND j.STATUS IN ( 0, 5 ) and j.created_date BETWEEN :startTime AND :endTime " +
                " AND ujr.user_id in ( :userId ) AND j.tenant_id = :tenantId " +
                " AND j.pteam_id != :projectId " +
                " UNION " +
                " SELECT j.STATUS, j.id  " +
                " FROM recruitment_process rp INNER JOIN job j ON j.recruitment_process_id = rp.id " +
                " LEFT JOIN talent_recruitment_process trp ON trp.job_id = j.id " +
                " LEFT JOIN talent_recruitment_process_kpi_user trpku ON trpku.talent_recruitment_process_id = trp.id  " +
                " AND trpku.user_role != 4  " +
                " WHERE rp.job_type != :jobType AND j.STATUS IN ( 0, 5 ) and j.created_date BETWEEN :startTime AND :endTime " +
                " AND trpku.user_id in ( :userId ) AND j.tenant_id = :tenantId " +
                " AND j.pteam_id != :projectId " +
                ") temp  " +
                "GROUP BY " +
                "status";
        List<Object[]> objectList = entityManager.createNativeQuery(sql)
                .setParameter("startTime", startTime)
                .setParameter("endTime", endTime)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("projectId", projectId)
                .setParameter("jobType", JobType.PAY_ROLL.toDbValue())
                .getResultList();
        if (ObjectUtil.isEmpty(objectList)) {
            return vo;
        }
        objectList.forEach(objectArray -> {
            if (Objects.equals(JobStatus.OPEN.toDbValue(), objectArray[0])) {
                vo.setOpenNums(Integer.parseInt(String.valueOf(objectArray[1])));
            } else {
                vo.setFilledNums(Integer.parseInt(String.valueOf(objectArray[1])));
            }
        });
        return vo;
    }

    @Override
    public Integer findNotRecommendedWithin14Days(List<Long> userIdList, Long tenantId, Long projectId,Integer day) {
        String sql = " select count(1) from " +
                " (SELECT j.id, TIMESTAMPDIFF(DAY, max(trp.created_date), now()) diff, TIMESTAMPDIFF(DAY, j.created_date, now()) createDdiff " +
                " FROM recruitment_process rp" +
                " INNER JOIN job j ON j.recruitment_process_id = rp.id" +
                " LEFT JOIN talent_recruitment_process trp ON trp.job_id = j.id " +
                " WHERE j.STATUS = 0 AND j.puser_id in ( :userId ) AND j.tenant_id = :tenantId " +
                " AND j.pteam_id != :projectId " +
                " GROUP BY j.id " +
                " having diff >= :diff or (diff is null and createDdiff <= :diff)) temp ";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("diff", day)
                .setParameter("projectId", projectId);
        return ((BigInteger) query.getSingleResult()).intValue();
    }

    @Override
    public Integer findNotInterviewWithin14Days(List<Long> userIdList, Long tenantId, Long projectId,Integer day) {
        String sql = " select count(1) from " +
                " (SELECT j.id, TIMESTAMPDIFF(DAY, max(trpi.created_date), now()) diff, TIMESTAMPDIFF(DAY, j.created_date, now()) createDdiff " +
                " FROM recruitment_process rp" +
                " INNER JOIN job j ON j.recruitment_process_id = rp.id" +
                " LEFT JOIN talent_recruitment_process trp ON trp.job_id = j.id " +
                " left join talent_recruitment_process_interview trpi on trpi.talent_recruitment_process_id = trp.id "+
                " WHERE j.STATUS = 0 AND j.puser_id in ( :userId ) AND j.tenant_id = :tenantId " +
                " AND j.pteam_id != :projectId " +
                " GROUP BY j.id " +
                " having diff >= :diff or (diff is null and createDdiff <= :diff)) temp ";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("userId", userIdList)
                .setParameter("tenantId", SecurityUtils.getTenantId())
                .setParameter("diff", day)
                .setParameter("projectId", projectId);
        return ((BigInteger) query.getSingleResult()).intValue();
    }

}

