//package com.altomni.apn.job.domain.application;
//
//import com.altomni.apn.common.domain.AbstractAuditingEntity;
//import com.altomni.apn.common.domain.enumeration.application.ActivityStatus;
//import com.altomni.apn.common.domain.enumeration.support.ActivityStatusConverter;
//import com.altomni.apn.common.domain.enumeration.EventType;
//import com.altomni.apn.common.domain.enumeration.EventTypeConverter;
//import com.fasterxml.jackson.annotation.JsonIgnore;
//import com.fasterxml.jackson.annotation.JsonProperty;
//import io.swagger.annotations.ApiModel;
//import io.swagger.annotations.ApiModelProperty;
//import org.springframework.data.annotation.CreatedBy;
//import org.springframework.data.annotation.CreatedDate;
//import org.springframework.data.annotation.LastModifiedBy;
//import org.springframework.data.annotation.LastModifiedDate;
//
//import javax.persistence.*;
//import javax.validation.constraints.NotNull;
//import java.io.Serializable;
//import java.time.Instant;
//
///**
// * An activity.
// */
//@ApiModel(description = "Activity group by status and application_id.")
//@Entity
//@Table(name = "activity_unique")
//public class ActivityUnique extends AbstractAuditingEntity implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    @Id
//    private Long id;
//
//    @ApiModelProperty(value = "The memo associated with the activity")
//    @Column(name = "memo")
//    private String memo;
//
//    @ApiModelProperty(required = true, value = "Activity Status", allowableValues = "Watching, Applied, Qualified, Submitted, Interview, Offered, Offer_Accepted, Offer_Rejected, Started, Called_Candidate, Meet_Candidate_In_Person")
//    @NotNull
//    @Convert(converter = ActivityStatusConverter.class)
//    @Column(name = "status", nullable = false)
//    private ActivityStatus status;
//
//    @ApiModelProperty(value = "Talent Id. This is for easy search. Update is not allowed", required = true)
//    @Column(name = "talent_id", nullable = false)
//    private Long talentId;
//
//    @ApiModelProperty(value = "User Id. This is for easy search. Update is not allowed", required = true)
//    @Column(name = "user_id", nullable = false)
//    private Long userId;
//
//    @ApiModelProperty(value = "Job Id. This is for easy search. Update is not allowed", required = true)
//    @Column(name = "job_id", nullable = false)
//    private Long jobId;
//
//    @ApiModelProperty(value = "Job Application Id. This is for easy search. Update is not allowed", required = true)
//    @Column(name = "application_id", nullable = false)
//    private Long applicationId;
//
//    @ApiModelProperty(value = "The tenant id user belongs to. For consumer, it will be 1.")
//    @Column(name = "tenant_id", nullable = false)
//    private Long tenantId;
//
//    @CreatedBy
//    @Column(name = "created_by", nullable = false, length = 50, updatable = false)
//    private String createdBy;
//
//    @CreatedDate
//    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
//    @Column(name = "created_date", nullable = false, updatable = false)
//    private Instant createdDate;
//
//    @LastModifiedBy
//    @JsonIgnore
//    @Column(name = "last_modified_by", length = 50)
//    private String lastModifiedBy;
//
//    @LastModifiedDate
//    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
//    @Column(name = "last_modified_date", nullable = false, updatable = true)
//    private Instant lastModifiedDate;
//
//    @ApiModelProperty(value = "The event date when this activity happens.")
//    @Column(name= "event_date")
//    private Instant eventDate;
//
//    @ApiModelProperty(value = "The event date belongs time zone.")
//    @Column(name= "event_time_zone")
//    private String eventTimeZone;
//
//    @ApiModelProperty(value = "The event type when this activity happens. eg: when interview,the value is phone interview, onsite interview etc.")
//    @Convert(converter = EventTypeConverter.class)
//    @Column(name= "event_type")
//    private EventType eventType;
//
//    @ApiModelProperty(value = "The location this activity takes place. This is useful for event type activity such as Interview.")
//    @Column(name = "location")
//    private String location;
//
//    public static long getSerialVersionUID() {
//        return serialVersionUID;
//    }
//
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//    public String getMemo() {
//        return memo;
//    }
//
//    public void setMemo(String memo) {
//        this.memo = memo;
//    }
//
//    public ActivityStatus getStatus() {
//        return status;
//    }
//
//    public void setStatus(ActivityStatus status) {
//        this.status = status;
//    }
//
//    public Long getTalentId() {
//        return talentId;
//    }
//
//    public void setTalentId(Long talentId) {
//        this.talentId = talentId;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    public Long getJobId() {
//        return jobId;
//    }
//
//    public void setJobId(Long jobId) {
//        this.jobId = jobId;
//    }
//
//    public Long getApplicationId() {
//        return applicationId;
//    }
//
//    public void setApplicationId(Long applicationId) {
//        this.applicationId = applicationId;
//    }
//
//    @Override
//    public String getCreatedBy() {
//        return createdBy;
//    }
//
//    @Override
//    public void setCreatedBy(String createdBy) {
//        this.createdBy = createdBy;
//    }
//
//    @Override
//    public Instant getCreatedDate() {
//        return createdDate;
//    }
//
//    @Override
//    public void setCreatedDate(Instant createdDate) {
//        this.createdDate = createdDate;
//    }
//
//    @Override
//    public String getLastModifiedBy() {
//        return lastModifiedBy;
//    }
//
//    @Override
//    public void setLastModifiedBy(String lastModifiedBy) {
//        this.lastModifiedBy = lastModifiedBy;
//    }
//
//    @Override
//    public Instant getLastModifiedDate() {
//        return lastModifiedDate;
//    }
//
//    @Override
//    public void setLastModifiedDate(Instant lastModifiedDate) {
//        this.lastModifiedDate = lastModifiedDate;
//    }
//
//    public Instant getEventDate() {
//        return eventDate;
//    }
//
//    public void setEventDate(Instant eventDate) {
//        this.eventDate = eventDate;
//    }
//
//    public String getEventTimeZone() {
//        return eventTimeZone;
//    }
//
//    public void setEventTimeZone(String eventTimeZone) {
//        this.eventTimeZone = eventTimeZone;
//    }
//
//    public EventType getEventType() {
//        return eventType;
//    }
//
//    public void setEventType(EventType eventType) {
//        this.eventType = eventType;
//    }
//
//    public String getLocation() {
//        return location;
//    }
//
//    public void setLocation(String location) {
//        this.location = location;
//    }
//
//    public Long getTenantId() {
//        return tenantId;
//    }
//
//    public void setTenantId(Long tenantId) {
//        this.tenantId = tenantId;
//    }
//
//    @Override
//    public String toString() {
//        return "ActivityUnique{" +
//            "id=" + id +
//            ", memo='" + memo + '\'' +
//            ", status=" + status +
//            ", talentId=" + talentId +
//            ", userId=" + userId +
//            ", jobId=" + jobId +
//            ", applicationId=" + applicationId +
//            ", tenantId=" + tenantId +
//            ", createdBy='" + createdBy + '\'' +
//            ", createdDate=" + createdDate +
//            ", lastModifiedBy='" + lastModifiedBy + '\'' +
//            ", lastModifiedDate=" + lastModifiedDate +
//            ", eventDate=" + eventDate +
//            ", eventTimeZone='" + eventTimeZone + '\'' +
//            ", eventType=" + eventType +
//            ", location='" + location + '\'' +
//            '}';
//    }
//}
