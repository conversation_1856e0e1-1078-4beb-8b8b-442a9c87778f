package com.altomni.apn.job.service.dto.job;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.dto.RangeDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobTranslateEsDTO {

    private List<JSONObject> requiredSkills;

    private List<JSONObject> preferredSkills;

    private String minimumDegreeLevel;

    private String[] preferredDegrees;

    private String[] requiredLanguages;

    private String[] preferredLanguages;

    private RangeDTO experienceYearRange;

}
