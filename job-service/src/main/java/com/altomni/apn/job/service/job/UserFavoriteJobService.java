//package com.altomni.apn.job.service.job;
//
//import com.altomni.apn.job.domain.job.UserFavoriteJob;
//import com.altomni.apn.common.dto.job.JobDTOV3;
//
//import java.util.List;
//
//@Deprecated
//public interface UserFavoriteJobService {
//
//    List<JobDTOV3> getUserFavoriteJobs(Long userId);
//
//    List<UserFavoriteJob> getUserFavoriteJobsByUserId(Long userId);
//
//    List<JobDTOV3> batchCreateFavoriteJobs(List<Long> favoriteJobIds);
//
//    void batchDeleteFavoriteJobs(List<Long> favoriteJobIds);
//
//    List<UserFavoriteJob> findAllByJobIdAndUserId(Long jobId, Long userId);
//
//    List<UserFavoriteJob> findAllByUserIdAndJobIdIn(Long userId, List<Long> jobIds);
//
//
//
//
//}
