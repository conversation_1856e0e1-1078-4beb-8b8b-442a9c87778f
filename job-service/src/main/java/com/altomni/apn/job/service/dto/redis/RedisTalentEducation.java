package com.altomni.apn.job.service.dto.redis;

import com.altomni.apn.common.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Comparator;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RedisTalentEducation implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "college name")
    private String collegeName;

    @ApiModelProperty(value = "The location of the college")
    private String location;

    @ApiModelProperty(value = "major name")
    private String majorName;

    @ApiModelProperty(value = "degree name")
    private String degreeName;

    @ApiModelProperty(value = "The date enrolled in the college. At least year and month are required. And use 1st as day if not specified")
    private String startDate;

    @ApiModelProperty(value = "The date graduated from the college. At least year and month are required. And use 1st as day if not specified")
    private String endDate;

    @ApiModelProperty(value = "talent id for easy search. Ready Only.")
    private Long talentId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCollegeName() {
        return collegeName;
    }

    public void setCollegeName(String collegeName) {
        this.collegeName = collegeName;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getMajorName() {
        return majorName;
    }

    public void setMajorName(String majorName) {
        this.majorName = majorName;
    }

    public String getDegreeName() {
        return degreeName;
    }

    public void setDegreeName(String degreeName) {
        this.degreeName = degreeName;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public static Comparator<RedisTalentEducation> comparator =  (e1, e2) -> {
        if(e1.getEndDate() == null && e2.getEndDate() == null){
            return DateUtil.compareLocalDateStr(e1.getStartDate(), e2.getStartDate());
        }else if(e1.getEndDate() == null){
            return 1;
        }else if(e2.getEndDate() == null){
            return -1;
        }else{
            LocalDate endDate1 = DateUtil.esDateStrToLocalDate(e1.getEndDate());
            LocalDate endDate2 = DateUtil.esDateStrToLocalDate(e2.getEndDate());
            return endDate1.compareTo(endDate2);
        }
    };

    @Override
    public String toString() {
        return "RedisTalentEducation{" +
                "id=" + id +
                ", collegeName='" + collegeName + '\'' +
                ", location='" + location + '\'' +
                ", majorName='" + majorName + '\'' +
                ", degreeName='" + degreeName + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", talentId=" + talentId +
                '}';
    }
}
