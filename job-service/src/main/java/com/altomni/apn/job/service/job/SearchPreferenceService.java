package com.altomni.apn.job.service.job;

import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.job.service.dto.job.SearchPreferenceDTO;

import java.util.List;

public interface SearchPreferenceService {


    List<SearchPreferenceDTO> findByUserId(Long userId, String searchName, ModuleType modul);

    SearchPreferenceDTO create(SearchPreferenceDTO searchPreferenceDTO);

    void deleteById(Long id);

    void deleteAllByUserId(Long userId);

    /**
     * The interface is only used to update the historical data format saved by the/search/config interface.
     * @return
     */
    Object searchPreferenceFormatUpdate();
}
