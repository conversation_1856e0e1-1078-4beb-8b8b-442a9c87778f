package com.altomni.apn.job.service.dto.redis;

import com.fasterxml.jackson.annotation.JsonInclude;

import java.io.Serializable;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RedisLocation implements Serializable {

    private static final long serialVersionUID = 1L;

    private String addressLine;

    private String city;

    private String province;

    private String country;

    private String zipcode;

    private String officialCity;

    private String officialProvince;

    private String officialCountry;

    private String officialZipcode;


    public RedisLocation() {
    }

    public String getAddressLine() {
        return addressLine;
    }

    public void setAddressLine(String addressLine) {
        this.addressLine = addressLine;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    public String getOfficialCity() {
        return officialCity;
    }

    public void setOfficialCity(String officialCity) {
        this.officialCity = officialCity;
    }

    public String getOfficialProvince() {
        return officialProvince;
    }

    public void setOfficialProvince(String officialProvince) {
        this.officialProvince = officialProvince;
    }

    public String getOfficialCountry() {
        return officialCountry;
    }

    public void setOfficialCountry(String officialCountry) {
        this.officialCountry = officialCountry;
    }

    public String getOfficialZipcode() {
        return officialZipcode;
    }

    public void setOfficialZipcode(String officialZipcode) {
        this.officialZipcode = officialZipcode;
    }

    @Override
    public String toString() {
        return "RedisLocation{" +
                "addressLine='" + addressLine + '\'' +
                ", city='" + city + '\'' +
                ", province='" + province + '\'' +
                ", country='" + country + '\'' +
                ", zipcode='" + zipcode + '\'' +
                ", officialCity='" + officialCity + '\'' +
                ", officialProvince='" + officialProvince + '\'' +
                ", officialCountry='" + officialCountry + '\'' +
                ", officialZipcode='" + officialZipcode + '\'' +
                '}';
    }
}
