package com.altomni.apn.job.domain.enumeration.aisourcing;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum AiSourcingTalentSource implements ConvertedEnum<Integer> {

    LINKEDIN(1),

    TALENT_POOL(2);

    private final Integer dbValue;

    AiSourcingTalentSource(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    /** static resolving: */
    public static final ReverseEnumResolver<AiSourcingTalentSource, Integer> resolver =
        new ReverseEnumResolver<>(AiSourcingTalentSource.class, AiSourcingTalentSource::toDbValue);

    public static AiSourcingTalentSource fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
