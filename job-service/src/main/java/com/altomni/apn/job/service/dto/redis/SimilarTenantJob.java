package com.altomni.apn.job.service.dto.redis;

import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.job.web.rest.vm.JobLocationVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class SimilarTenantJob {

    private Long id;

    private Double score;

    private String title;

    private String companyName;

    private List<JobLocationVM.Location> locations;

    private Integer candidates;

    private boolean isPrivateJob;
}
