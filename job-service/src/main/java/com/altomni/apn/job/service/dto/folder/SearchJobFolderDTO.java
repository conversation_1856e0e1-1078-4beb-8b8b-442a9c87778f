package com.altomni.apn.job.service.dto.folder;

import com.altomni.apn.common.dto.folder.FolderSharedTargetBriefDTO;
import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "entity for Folder list page")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SearchJobFolderDTO implements Serializable {

    private Long id;

    private String name;

    private String folderNote;

    private String creator;

    private Instant lastModifiedDate;

    private Instant createdDate;

    private FolderPermission folderPermission;

    private List<FolderSharedTargetBriefDTO> sharedUsers;

    private List<FolderSharedTargetBriefDTO> sharedTeams;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFolderNote() {
        return folderNote;
    }

    public void setFolderNote(String folderNote) {
        this.folderNote = folderNote;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Instant getLastModifiedDate() {
        return lastModifiedDate;
    }

    public void setLastModifiedDate(Instant lastModifiedDate) {
        this.lastModifiedDate = lastModifiedDate;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }

    public List<FolderSharedTargetBriefDTO> getSharedUsers() {
        return sharedUsers;
    }

    public void setSharedUsers(List<FolderSharedTargetBriefDTO> sharedUsers) {
        this.sharedUsers = sharedUsers;
    }

    public List<FolderSharedTargetBriefDTO> getSharedTeams() {
        return sharedTeams;
    }

    public void setSharedTeams(List<FolderSharedTargetBriefDTO> sharedTeams) {
        this.sharedTeams = sharedTeams;
    }

    public FolderPermission getFolderPermission() {
        return folderPermission;
    }

    public void setFolderPermission(FolderPermission folderPermission) {
        this.folderPermission = folderPermission;
    }

}
