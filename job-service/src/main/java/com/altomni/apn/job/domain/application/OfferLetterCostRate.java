package com.altomni.apn.job.domain.application;


import com.altomni.apn.job.domain.enumeration.application.OfferLetterCostRateType;
import com.altomni.apn.job.domain.enumeration.application.OfferLetterCostRateTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Objects;

/**
 * A OfferLetterCostRate.
 */
@Entity
@Table(name = "offer_letter_cost_rate")
public class OfferLetterCostRate implements Serializable {

    private static final long serialVersionUID = 1694212632168537154L;

    @Id
    @JsonIgnore
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The tenant id user belongs to.")
    @Column(name = "tenant_id")
    private Long tenantId;

    @NotNull
    @Column(name = "currency")
    private Integer currency;

    @NotNull
    @Convert(converter = OfferLetterCostRateTypeConverter.class)
    @Column(name = "rate_type")
    private OfferLetterCostRateType rateType;

    @NotNull
    @ApiModelProperty(value = "Identify the specific rates")
    @Column(name = "code")
    private String code;

    @ApiModelProperty(value = "Describe the specific rates")
    @Column(name = "description")
    private String description;

    @NotNull
    @ApiModelProperty(value = "The value of the cost, this could be either a rate or a specific amount of the cost")
    @Column(name = "value")
    private BigDecimal value;

    @NotNull
    @Column(name = "expire_date")
    private LocalDate expireDate;

    public OfferLetterCostRate() { }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public OfferLetterCostRateType getRateType() {
        return rateType;
    }

    public void setRateType(OfferLetterCostRateType rateType) {
        this.rateType = rateType;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public BigDecimal getValue() {
        return value;
    }

    public void setValue(BigDecimal value) {
        this.value = value;
    }

    public LocalDate getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(LocalDate expireDate) {
        this.expireDate = expireDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        OfferLetterCostRate offerLetterCostRate = (OfferLetterCostRate) o;
        if (offerLetterCostRate.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), offerLetterCostRate.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "OfferLetterCostRate{" +
            "id=" + id +
            ", tenantId=" + tenantId +
            ", currency=" + currency +
            ", rateType=" + rateType +
            ", code='" + code + '\'' +
            ", description='" + description + '\'' +
            ", value=" + value +
            ", expireDate=" + expireDate +
            '}';
    }
}
