package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Optional;

public enum SharingTargetCategory {
    USER(0),
    TEAM(1);

    private final int dbValue;

    SharingTargetCategory(int dbValue) {
        this.dbValue = dbValue;

    }

    public Integer toDbValue() {
        return dbValue;
    }


    public static final ReverseEnumResolver<SharingTargetCategory, Integer> resolver =
            new ReverseEnumResolver<>(SharingTargetCategory.class, SharingTargetCategory::toDbValue);

    public static SharingTargetCategory fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    @JsonValue
    public String getValue() {
        return this.name();
    }

}
