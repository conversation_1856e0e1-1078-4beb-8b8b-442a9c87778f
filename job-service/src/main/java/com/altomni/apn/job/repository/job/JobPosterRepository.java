package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.JobPoster;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface JobPosterRepository extends JpaRepository<JobPoster, Long>, JpaSpecificationExecutor<JobPoster> {

    JobPoster findFirstByJobIdAndPermissionUserIdOrderByCreatedDateDesc(Long jobId, Long puserId);
}
