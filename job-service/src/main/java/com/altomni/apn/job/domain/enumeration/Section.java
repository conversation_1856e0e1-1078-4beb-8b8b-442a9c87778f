package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum Section implements ConvertedEnum<Integer> {
    SUMMARY(0),
    RESPONSIBILITY(1),
    REQUIREMENTS(2),
    PREFERRED_REQUIREMENTS(3);

    private final int dbValue;

    Section (int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<Section, Integer> resolver =
            new ReverseEnumResolver<>(Section.class, Section::toDbValue);

    public static Section fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
