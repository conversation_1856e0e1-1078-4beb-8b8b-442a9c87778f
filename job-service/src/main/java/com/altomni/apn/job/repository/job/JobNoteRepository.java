package com.altomni.apn.job.repository.job;

import com.altomni.apn.common.domain.job.JobNote;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the JobNote entity.
 */

@Repository
public interface JobNoteRepository extends JpaRepository<JobNote,Long> {

    JobNote findJobNoteByIdAndVisible(Long id, Boolean visible);

    List<JobNote> findByJobIdAndVisibleOrderByCreatedDateDesc(Long jobId, Boolean visible);

    List<JobNote> findAllByJobIdAndVisible(Long jobId, Boolean visible);

    Page<JobNote> findAllByJobIdAndNoteContainingIgnoreCaseAndVisibleOrderByCreatedDateDesc(Long jobId, String keyword, Boolean visible, Pageable pageable);


}
