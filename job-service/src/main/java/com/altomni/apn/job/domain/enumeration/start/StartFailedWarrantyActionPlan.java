package com.altomni.apn.job.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum StartFailedWarrantyActionPlan implements ConvertedEnum<Integer> {

    REPLACEMENT_REQUIRED(0),
    NO_REPLACEMENT(1);

    private final int dbValue;
    StartFailedWarrantyActionPlan(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<StartFailedWarrantyActionPlan, Integer> resolver =
        new ReverseEnumResolver<>(StartFailedWarrantyActionPlan.class, StartFailedWarrantyActionPlan::toDbValue);

    public static StartFailedWarrantyActionPlan fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
