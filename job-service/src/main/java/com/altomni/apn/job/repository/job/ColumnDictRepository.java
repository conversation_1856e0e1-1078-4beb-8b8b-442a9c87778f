package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.ColumnDict;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the BizDict entity.
 */
@Repository
public interface ColumnDictRepository extends JpaRepository<ColumnDict,Long>, QuerydslPredicateExecutor<ColumnDict> {

    List<ColumnDict> findAllByPcodeOrderBySortOrder(Long dictCode);

}
