package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.JobPost;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface JobPostRepository extends JpaRepository<JobPost,Long> {
    List<JobPost> findAllByJobId(Long jobId);

    List<JobPost> findAllByJobIdAndJazzhrStatus(Long jobId, Integer status);

    List<JobPost> findAllByPrimaryRecruiterId(Long primaryRecruiterId);

    List<JobPost> findAllByIsReposted(boolean isReposted);
}
