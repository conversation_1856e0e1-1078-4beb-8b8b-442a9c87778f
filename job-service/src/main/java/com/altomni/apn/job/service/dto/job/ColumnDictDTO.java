package com.altomni.apn.job.service.dto.job;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.job.domain.job.ColumnDict;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ColumnDictDTO extends AbstractAuditingEntity implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "The dictCode for find special dict data")
    private Integer id;

    @ApiModelProperty(value = "The Chinese annotation for target value")
    private String label;

    @ApiModelProperty(value = "The Chinese annotation for target value")
    private String labelCn;

    @ApiModelProperty(value = "The label words for web")
    private Integer value;

    private String column;

    private Boolean showFlag;

    private Boolean sortFlag;

    @ApiModelProperty(value = "sign checked for web")
    private Boolean checked;

    public static ColumnDictDTO fromBizDict(ColumnDict b) {
        ColumnDictDTO dto = new ColumnDictDTO();
        if(ObjectUtil.isNotEmpty(b.getDictCode())) {
            dto.setId(b.getDictCode().intValue());
        }
        dto.setLabel(b.getUiName());
        dto.setLabelCn(b.getUiName());
        if(ObjectUtil.isNotEmpty(b.getDictCode())) {
            dto.setValue(b.getDictCode().intValue());
        }
        dto.setColumn(b.getColumnName());
        dto.setShowFlag(b.getShowFlag());
        dto.setSortFlag(b.getSortFlag());
        dto.setChecked(false);
        return dto;
    }

}
