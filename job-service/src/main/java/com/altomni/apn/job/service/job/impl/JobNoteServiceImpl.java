package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.job.JobNote;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.job.JobNoteDTO;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.repository.job.JobNoteRepository;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.service.dto.job.JobNoteKeywordDTO;
import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.altomni.apn.job.service.job.JobNoteService;
import com.altomni.apn.job.service.user.UserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class JobNoteServiceImpl implements JobNoteService {

    @Resource
    private JobNoteRepository jobNoteRepository;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private UserService userService;

    @Resource
    private EsFillerJobService esFillerJobService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Override
    public JobNoteDTO create(JobNoteDTO jobNoteDTO) {
        if (jobNoteDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_CREATE_IDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (jobNoteDTO.getJobId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_CREATE_JOBIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (jobNoteDTO.getNote() == null || jobNoteDTO.getNote().isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_CREATE_NOTENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (jobNoteDTO.getSyncToCalendar() && jobNoteDTO.getAlertTime() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_CREATE_ALERTTIMENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        JobNote result = jobNoteRepository.save(JobNoteDTO.toJobNote(jobNoteDTO));
        jobNoteDTO = JobNoteDTO.fromJobNote(result);
        //setJobNoteRelateEntity(jobNoteDTO);Jobv3 deprecated
        setJobNoteUserInfo(jobNoteDTO, result);

        esFillerJobService.sendJobNoteToMq(SecurityUtils.getTenantId(), jobNoteDTO.getJobId(), jobNoteDTO, false);
        return jobNoteDTO;
    }

    private void setJobNoteUserInfo(JobNoteDTO jobNoteDTO, JobNote jobNote) {
        if (jobNoteDTO == null) {
            return;
        }
        if (jobNote.getPermissionUserId() != null) {
            User user = userService.findById(jobNote.getPermissionUserId()).getBody();
            FullNameUserDTO userDTO = new FullNameUserDTO();
            userDTO.setId(jobNote.getPermissionUserId());
            userDTO.setFullName(CommonUtils.formatFullName(user.getFirstName(), user.getLastName()));
            jobNoteDTO.setCreatedBy(userDTO);
            jobNoteDTO.setCreatedDate(jobNote.getCreatedDate());
        }

        if (StringUtils.isNotBlank(jobNote.getLastModifiedBy())) {
            Optional<Long> userIdOpt = extractAndConvertUserId(jobNote.getLastModifiedBy());
            userIdOpt.ifPresent(userId -> {
                User user = userService.findById(jobNote.getPermissionUserId()).getBody();
                FullNameUserDTO userDTO = new FullNameUserDTO();
                userDTO.setId(jobNote.getPermissionUserId());
                userDTO.setFullName(CommonUtils.formatFullName(user.getFirstName(), user.getLastName()));
                jobNoteDTO.setLastModifiedBy(userDTO);
                jobNoteDTO.setLastModifiedDate(jobNote.getLastModifiedDate());
            });
        }
    }


    @Deprecated
    private void setJobNoteRelateEntity(JobNoteDTO jobNoteDTO) {
//        if (jobNoteDTO != null) {
//            if (jobNoteDTO.getUserId() != null) {
//                jobNoteDTO.setUser(userService.findById(jobNoteDTO.getUserId()).getBody());
//            }
//        }
    }

    @Override
    public JobNoteDTO update(JobNoteDTO jobNoteDTO) {
        JobNote existing = jobNoteRepository.findJobNoteByIdAndVisible(jobNoteDTO.getId(), true);
        if (existing == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_UPDATE_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (existing.getPermissionUserId() == null || !existing.getPermissionUserId().equals(SecurityUtils.getUserId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_UPDATE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JobNote newNote = JobNoteDTO.toJobNote(jobNoteDTO);
        ServiceUtils.myCopyProperties(newNote, existing, JobNote.UpdateSkipProperties);
        JobNote result = jobNoteRepository.save(existing);
        setJobNoteUserInfo(jobNoteDTO, result);
        //setJobNoteRelateEntity(jobNoteDTO);Jobv3 deprecated


        esFillerJobService.sendJobNoteToMq(SecurityUtils.getTenantId(), jobNoteDTO.getJobId(), jobNoteDTO, false);

        return jobNoteDTO;
    }

    @Override
    public JobNoteDTO findOneWithEntity(Long id) {
        JobNoteDTO jobNoteDTO = findOne(id);
        //setJobNoteRelateEntity(jobNoteDTO); Jobv3 deprecated
        return jobNoteDTO;
    }

    @Override
    public JobNoteDTO findOne(Long id) {
        JobNote jobNote = jobNoteRepository.findJobNoteByIdAndVisible(id, true);
        return jobNote != null ? JobNoteDTO.fromJobNote(jobNote) : null;
    }

    @Override
    public void delete(Long id) {
        JobNote existing = jobNoteRepository.findById(id).orElse(null);
        if (existing == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_UPDATE_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (existing.getPermissionUserId() == null || !existing.getPermissionUserId().equals(SecurityUtils.getUserId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_UPDATE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        existing.setVisible(false);
        jobNoteRepository.save(existing);

        JobNoteDTO jobNoteDTO = JobNoteDTO.fromJobNote(existing);

        esFillerJobService.sendJobNoteToMq(SecurityUtils.getTenantId(), existing.getJobId(), jobNoteDTO, true);
    }


    @Override
    public List<JobNoteDTO> findAllByJobId(Long jobId) {
        List<JobNote> jobNoteList = jobNoteRepository.findAllByJobIdAndVisible(jobId, true);

        HashMap<Long, UserBriefDTO> userSet = getUserSetFromCreatedByAndModified(jobNoteList);
        List<JobNoteDTO> jobNoteDTOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(jobNoteList)) {
            jobNoteDTOList = jobNoteList.stream()
                    .map(jobNote -> {
                        JobNoteDTO jobNoteDTO = convertJobNoteToJobNoteDTOWithUserInfo(jobNote, userSet);

                        return jobNoteDTO;
                    })
                    .collect(Collectors.toList());

        }
        return jobNoteDTOList;
    }


    @Override
    public Page<JobNoteDTO> searchJobNoteByKeyword(JobNoteKeywordDTO jobNoteKeywordDTO, Pageable pageable) {
        if (ObjectUtil.isEmpty(jobNoteKeywordDTO) || ObjectUtil.isEmpty(jobNoteKeywordDTO.getJobId()) || ObjectUtil.isNull(jobNoteKeywordDTO.getNoteKeyword())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_SEARCHJOBNOTEBYKEYWORD_DTONULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        Page<JobNote> jobNotePage = jobNoteRepository.findAllByJobIdAndNoteContainingIgnoreCaseAndVisibleOrderByCreatedDateDesc(jobNoteKeywordDTO.getJobId(), jobNoteKeywordDTO.getNoteKeyword(), Boolean.TRUE, pageable);
//        List<Long> userIds = jobNotePage.stream().map(JobNote::getPermissionUserId).collect(Collectors.toList());
//        userIds.addAll(extractAndConvertUserIdList(jobNotePage.stream().map(JobNote::getLastModifiedBy).collect(Collectors.toList())));
//        List<UserBriefDTO> userList = userService.getAllBriefUsersByIds(userIds).getBody();
//        if (jobNotePage.getTotalElements() != 0 && userList == null) {
//            throw new CustomParameterizedException("Cannot get user info");
//        }
//        HashMap<Long, UserBriefDTO> userSet = userList.stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity(), (user1, user2) -> user2, HashMap::new));
        HashMap<Long, UserBriefDTO> userSet = getUserSetFromCreatedByAndModified(jobNotePage.getContent());

        List<JobNoteDTO> jobNoteDTOList = jobNotePage.stream()
                .map(jobNote -> {
                    JobNoteDTO jobNoteDTO = convertJobNoteToJobNoteDTOWithUserInfo(jobNote, userSet);

                    return jobNoteDTO;
                })
                .collect(Collectors.toList());


        return new PageImpl<>(jobNoteDTOList, pageable, jobNotePage.getTotalElements());
    }

    private HashMap<Long, UserBriefDTO> getUserSetFromCreatedByAndModified(List<JobNote> jobNoteList) {
        List<Long> userIds = jobNoteList.stream().map(JobNote::getPermissionUserId).collect(Collectors.toList());
        userIds.addAll(extractAndConvertUserIdList(jobNoteList.stream().map(JobNote::getLastModifiedBy).collect(Collectors.toList())));
        List<UserBriefDTO> userList = userService.getAllBriefUsersByIds(userIds).getBody();
        if (jobNoteList.size() != 0 && userList == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBNOTE_SEARCHJOBNOTEBYKEYWORD_USERERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        HashMap<Long, UserBriefDTO> userSet = userList.stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity(), (user1, user2) -> user2, HashMap::new));
        return userSet;
    }

    private JobNoteDTO convertJobNoteToJobNoteDTOWithUserInfo(JobNote jobNote, HashMap<Long, UserBriefDTO> userSet) {
        JobNoteDTO jobNoteDTO = JobNoteDTO.fromJobNote(jobNote);

        UserBriefDTO createdUser = userSet.get(jobNote.getPermissionUserId());
        if (createdUser != null) {
            FullNameUserDTO createdBy = new FullNameUserDTO();
            createdBy.setFullName(CommonUtils.formatFullName(createdUser.getFirstName(), createdUser.getLastName()));
            createdBy.setId(jobNote.getPermissionUserId());
            jobNoteDTO.setCreatedBy(createdBy);
            jobNoteDTO.setCreatedDate(jobNote.getCreatedDate());
        }

        Optional<Long> modifiedByIdOpt = extractAndConvertUserId(jobNote.getLastModifiedBy());
        modifiedByIdOpt.ifPresent(modifiedById -> {
            UserBriefDTO modifiedUser = userSet.get(modifiedById);
            if (modifiedUser != null) {
                FullNameUserDTO modifiedBy = new FullNameUserDTO();
                modifiedBy.setId(modifiedById);
                modifiedBy.setFullName(CommonUtils.formatFullName(modifiedUser.getFirstName(), modifiedUser.getLastName()));
                jobNoteDTO.setLastModifiedBy(modifiedBy);
                jobNoteDTO.setLastModifiedDate(jobNote.getLastModifiedDate());
            }
        });
        return jobNoteDTO;
    }

    private List<Long> extractAndConvertUserIdList(List<String> userIdAndTenantIds) {
        return userIdAndTenantIds.stream()
                .map(input -> extractAndConvertUserId(input))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
    }

    private Optional<Long> extractAndConvertUserId(String userIdAndTeantId) {
        try {
            String[] parts = userIdAndTeantId.split(",");
            return Optional.of(Long.parseLong(parts[0]));
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            return Optional.<Long>empty();
        }
    }

}
