package com.altomni.apn.job.repository.job;

import com.altomni.apn.common.dto.folder.FolderNameDTO;
import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.job.domain.job.JobFolder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JobFolderRepository extends JpaRepository<JobFolder, Long> {
    JobFolder findOneByPermissionUserIdAndName(Long pUserId, String name);

    @Query(value = "SELECT new com.altomni.apn.common.dto.folder.FolderNameDTO(f.id, f.name) " +
            "FROM JobFolder f " +
            "WHERE f.permissionUserId = ?1 " +
            "ORDER BY f.createdDate DESC ")
    List<FolderNameDTO> findAllFolderNameOnlyByPermissionUserId(Long userId);

    @Query(value = "SELECT new com.altomni.apn.common.dto.folder.FolderNameDTO(f.id, f.name) " +
            "FROM JobFolder f " +
            "WHERE EXISTS ( " +
            "    SELECT 1 " +
            "    FROM JobFolderSharingUser su " +
            "    WHERE f.id = su.jobFolderId AND su.userId = ?1 " +
            "       AND su.permission = ?2 " +
            ") OR EXISTS ( " +
            "    SELECT 1 " +
            "    FROM JobFolderSharingTeam st " +
            "    INNER JOIN PermissionUserTeam ptu ON st.teamId = ptu.teamId " +
            "    WHERE f.id = st.jobFolderId AND ptu.userId = ?1 " +
            "       AND st.permission = ?2 " +
            "       AND (st.excludedUserIds IS NULL OR st.excludedUserIds NOT LIKE CONCAT('%', ?1, '%')) " +
            ") " +
            "ORDER BY f.createdDate DESC  ")
    List<FolderNameDTO> findAllSharedFolderNameOnlyByUserIdAndPermission(Long userId, FolderPermission permission);

    @Query(value = "SELECT new com.altomni.apn.common.dto.folder.FolderNameDTO(f.id, f.name) " +
            "FROM JobFolder f " +
            "WHERE EXISTS ( " +
            "    SELECT 1 " +
            "    FROM JobFolderSharingUser su " +
            "    WHERE f.id = su.jobFolderId AND su.userId = ?1 " +
            ") OR EXISTS ( " +
            "    SELECT 1 " +
            "    FROM JobFolderSharingTeam st " +
            "    INNER JOIN PermissionUserTeam ptu ON st.teamId = ptu.teamId " +
            "    WHERE f.id = st.jobFolderId AND ptu.userId = ?1 " +
            "       AND (st.excludedUserIds IS NULL OR st.excludedUserIds NOT LIKE CONCAT('%', ?1, '%')) " +
            ") ")
    List<FolderNameDTO> findAllSharedFolderNameOnlyByUserId(Long userId);


    List<JobFolder> findAllByIdIn(List<Long> jobFolderIds);

}
