package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobPosterTemplate;
import com.altomni.apn.common.domain.enumeration.job.JobPosterTemplateConverter;
import com.altomni.apn.common.domain.enumeration.job.PlatformType;
import com.altomni.apn.common.domain.enumeration.job.PlatformTypeConverter;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPosterDTO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPosterVO;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@Table(name = "job_poster")
public class JobPoster extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "platform")
    @Convert(converter = PlatformTypeConverter.class)
    private PlatformType platform;

    @Column(name = "template")
    @Convert(converter = JobPosterTemplateConverter.class)
    private JobPosterTemplate template;

    @Column(name = "job_poster_image_id")
    private Long jobPosterImageId;

    @Column(name = "content")
    private String content;

    public static JobPoster fromJobSharingPosterDTO(JobSharingPosterDTO jobSharingPosterDTO) {
        JobPoster jobPoster = new JobPoster();
        ServiceUtils.myCopyProperties(jobSharingPosterDTO, jobPoster);
        jobPoster.setContent(jobSharingPosterDTO.getContent().toString());
        return jobPoster;
    }

    public static JobSharingPosterVO toJobSharingPosterVO(JobPoster jobPoster) {
        JobSharingPosterVO vo = new JobSharingPosterVO();
        vo.setId(jobPoster.getId());
        vo.setJobId(jobPoster.getJobId());
        vo.setPlatform(jobPoster.getPlatform());
        vo.setTemplate(jobPoster.getTemplate());
        vo.setJobPosterImageId(jobPoster.getJobPosterImageId());
        vo.setContent(jobPoster.getContent());
        return vo;
    }
}

