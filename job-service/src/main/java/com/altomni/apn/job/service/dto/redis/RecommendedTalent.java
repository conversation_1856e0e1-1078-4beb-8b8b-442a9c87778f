package com.altomni.apn.job.service.dto.redis;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.enumeration.talent.RecommendedTalentSource;
import com.altomni.apn.common.domain.enumeration.talent.RecommendedTalentSourceConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.Convert;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class RecommendedTalent {

    private String id;

    private String fullName;

    private String company;

    private String title;

    private JSONArray skills;

    private Double score;

    private Instant createdDate;

    @Convert(converter = RecommendedTalentSourceConverter.class)
    private RecommendedTalentSource source;

    public RecommendedTalent(Long id, String fullName, String additionalInfo, Instant createdDate) {
        this.id = String.valueOf(id);
        this.fullName = fullName;
        JSONObject talentAdditionalInfo = JSONObject.parseObject(additionalInfo);
        if(ObjectUtil.isNotNull(talentAdditionalInfo)){
            final JSONArray experiences = (JSONArray) talentAdditionalInfo.getOrDefault("experiences", null);
            if (ObjectUtil.isNotNull(experiences)){
                for (Object experience : experiences) {
                    JSONObject exp = (JSONObject) experience;
                    final Boolean current = exp.getBoolean("current");
                    if (Boolean.TRUE.equals(current)){
                        this.company = exp.getString("companyName");
                        this.title = exp.getString("title");
                        break;
                    }
                }
            }
            final Object skills = talentAdditionalInfo.getOrDefault("skills", null);
            this.skills =  ObjectUtil.isEmpty(skills) ? null : (JSONArray) skills;
        }
        this.createdDate = createdDate;
    }
}
