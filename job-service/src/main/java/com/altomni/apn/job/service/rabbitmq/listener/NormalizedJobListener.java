package com.altomni.apn.job.service.rabbitmq.listener;

import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
@RefreshScope
public class NormalizedJobListener {

    @Resource
    private EsFillerJobService esFillerJobService;

    /*@RabbitListener(queues = RabbitMqConfig.NORMALIZED_JOB_QUEUE)
    @RabbitHandler
    public void processHandler(Channel channel, Message message) throws IOException {
        //log.info("Normalized job message received：{}", message.toString());
        String normalizedJob = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("normalizedJob_from_es");
        log.info(normalizedJob);
        esFillerJobService.saveNormalizedJobInfos(normalizedJob);
        //channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
    }*/

    @RabbitListener(containerFactory = "esfillerFactory", queues = {"${application.esfillerMQ.apnNormalizedJobQueue}"})
    @RabbitHandler
    public void processHandler(Channel channel, Message message) throws IOException {
        String normalizedJob = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("normalizedJob_from_es: {}", normalizedJob);
        try {
            LoginUtil.simulateLoginWithClient();
            esFillerJobService.saveNormalizedJobInfos(normalizedJob);
            //channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("saveNormalizedJobInfos error: {}", e.getMessage());
        } finally {
            SecurityContextHolder.clearContext();
        }
    }
}
