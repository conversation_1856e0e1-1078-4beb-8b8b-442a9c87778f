package com.altomni.apn.job.service.job;

import com.altomni.apn.job.domain.job.JobQuestion;
import com.altomni.apn.job.service.dto.job.JobQuestionDTO;

import java.util.List;

public interface JobQuestionService {

    JobQuestionDTO create(JobQuestion jobQuestion);

    JobQuestionDTO update(JobQuestion jobQuestion);

    JobQuestionDTO findOneWithEntity(Long id);

    JobQuestionDTO findOne(Long id);

    List<JobQuestionDTO> findAllByJobId(Long jobId);

    void delete(Long id);
}
