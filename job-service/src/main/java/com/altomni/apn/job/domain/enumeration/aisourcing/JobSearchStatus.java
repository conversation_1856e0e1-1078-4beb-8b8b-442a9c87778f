package com.altomni.apn.job.domain.enumeration.aisourcing;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum JobSearchStatus implements ConvertedEnum<Integer> {

    ACTIVE(1),
    ACTIVE_OVERSEAS_RETURNEE(2),
    INACTIVE(3),

    BLACKLIST(4);

    private final Integer dbValue;

    JobSearchStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    /** static resolving: */
    public static final ReverseEnumResolver<JobSearchStatus, Integer> resolver =
        new ReverseEnumResolver<>(JobSearchStatus.class, JobSearchStatus::toDbValue);

    public static JobSearchStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
