package com.altomni.apn.job.service.dto.folder;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Data
@ApiModel(value = "Jobs and Folders: many to many ")
public class JobFolderRelationListDTO {

    List<Long> jobIds;
    List<Long> folderIds;

    public boolean isJobsValid() {
        return isListValid(this.jobIds);
    }

    public boolean isFoldersValid(){
        return isListValid(this.folderIds);
    }


    private boolean isListValid(List<Long> list) {
        if (list == null || list.isEmpty()) {
            return false;
        }

        Set<Long> uniqueIds = new HashSet<>(list);
        return uniqueIds.size() == list.size();
    }
}
