package com.altomni.apn.job.repository.job;


import com.altomni.apn.common.domain.dict.JobJobFunctionRelation;
import com.altomni.apn.job.domain.job.JobFolderRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JobFunctionRelationRepository extends JpaRepository<JobJobFunctionRelation, Long> {

    boolean existsByJobId(Long jobId);
}
