package com.altomni.apn.job.service.mapper.jobFolder;



import com.altomni.apn.common.dto.search.SearchConditionDTO;
import com.altomni.apn.job.domain.enumeration.SearchCategory;
import com.altomni.apn.job.domain.job.JobSearchFolder;
import com.altomni.apn.job.service.dto.folder.JobSearchFolderDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.*;

import java.util.List;

@Mapper(componentModel = "spring")
public interface JobSearchFolderMapper {

    @Mapping(target = "searchCriteria", source = "searchCriteria", qualifiedByName = "searchCriteriaToJsonString")
    JobSearchFolder toEntity(JobSearchFolderDTO dto);

    @Mapping(target = "searchCriteria", source = "searchCriteria", qualifiedByName = "jsonStringToSearchCriteria")
    JobSearchFolderDTO toDto(JobSearchFolder entity);

    @Named("mapWithoutData")
    @Mapping(target = "searchCriteria", ignore = true)
    @Mapping(target = "tenantId", ignore = true)
    @Mapping(source = "createdDate", target = "createdDate")
    JobSearchFolderDTO mapWithoutCriteria(JobSearchFolder entity);

    @Mapping(target = "searchCriteria", source = "searchCriteria", qualifiedByName = "jsonStringToSearchCriteria")
    List<JobSearchFolderDTO> toDto(List<JobSearchFolder> entities);

    @IterableMapping(qualifiedByName = "mapWithoutData")
    List<JobSearchFolderDTO> toSimpleDto(List<JobSearchFolder> entities);

    default JobSearchFolder fromId(Long id) {
        if (id == null) {
            return null;
        }
        JobSearchFolder jobSearchFolder = new JobSearchFolder();
        jobSearchFolder.setId(id);
        return jobSearchFolder;
    }
    default void updateEntityFromDTO(JobSearchFolderDTO dto, @MappingTarget JobSearchFolder entity) {
        if (dto == null) {
            return;
        }
        entity.setName(dto.getName());
        try{
            entity.setSearchCategory(SearchCategory.valueOf(dto.getSearchCategory()));
        }catch(Exception ex) {
            ex.printStackTrace();
        }

        entity.setSearchCriteria(searchCriteriaToJsonString(dto.getSearchCriteria()));
    }

    @Named("searchCriteriaToJsonString")
    public static String searchCriteriaToJsonString(SearchConditionDTO conditionDTO) {
        if (conditionDTO == null) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(conditionDTO);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting List<T> to JSON string", e);
        }
    }

    @Named("jsonStringToSearchCriteria")
    public static SearchConditionDTO jsonStringToSearchCriteria(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            //JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class,  SearchConditionDTO);
            return objectMapper.readValue(jsonString,  SearchConditionDTO.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting JSON string to List<T>", e);
        }
    }

    // Additional mapping methods if needed
}
