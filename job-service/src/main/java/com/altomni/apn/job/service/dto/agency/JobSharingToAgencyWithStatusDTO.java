package com.altomni.apn.job.service.dto.agency;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.activity.ChangeFieldDTO;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobSharingToAgencyWithStatusDTO implements Serializable {

    private Long id;
    private Long tenantId;
    private Long jobId;
    private Long recruitmentProcessId;
    private String sharingToAgencyInfo;
    private JobStatus status;
    private Boolean companyNameVisible;
    private Long companyId;
    private String companyName;
    private Instant jobCreatedDate;
    private Long permissionUserId;
    private Long permissionTeamId;

}
