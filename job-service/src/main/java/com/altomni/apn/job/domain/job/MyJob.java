package com.altomni.apn.job.domain.job;

import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

/**
 * Created by <PERSON> on 9/19/2017.
 */
@Entity
public class MyJob implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty(value = "job id", required = true)
    private Long id;

    @ApiModelProperty(value = "the job title this talent applied")
    private String jobTitle;

    @ApiModelProperty(value = "the company name this talent applied")
    private String company;

    @ApiModelProperty(value = "the company id this talent applied")
    private Long companyId;

    @ApiModelProperty(value = "the number of application in Applied status (Submitted to AM)")
    private Long applied;

    @ApiModelProperty(value = "the number of application related to me in Applied status (Submitted to AM)")
    private Long myApplied;

    @ApiModelProperty(value = "the number of application in Submitted status (Submitted to Client)")
    private Long submitted;

    @ApiModelProperty(value = "the number of application related to me in Submitted status (Submitted to Client)")
    private Long mySubmitted;

    @ApiModelProperty(value = "the number of application in Interview status")
    private Long interview;

    @ApiModelProperty(value = "the number of application related to me in Interview status")
    private Long myInterview;

    @ApiModelProperty(value = "the number of application in Offered status")
    private Long offered;

    @ApiModelProperty(value = "the number of application related to me in Offered status")
    private Long myOffered;

//    @ApiModelProperty(value = "last update time")
//    private Instant lastModifiedDate;

//    public MyJob() {}
//
//    public MyJob(Long id, String jobTitle, String company, Long submitted, Long mySubmitted, Long interview,
//                 Long myInterview, Long offered, Long myOffered, Instant lastModifiedDate) {
//        this.id = id;
//        this.jobTitle = jobTitle;
//        this.company = company;
//        this.submitted = submitted;
//        this.mySubmitted = mySubmitted;
//        this.interview = interview;
//        this.myInterview = myInterview;
//        this.offered = offered;
//        this.myOffered = myOffered;
//        this.lastModifiedDate = lastModifiedDate;
//    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Long getCompanyId() { return companyId; }

    public void setCompanyId(Long companyId) { this.companyId = companyId; }

    public Long getApplied() { return applied; }

    public void setApplied(Long applied) { this.applied = applied; }

    public Long getMyApplied() { return myApplied; }

    public void setMyApplied(Long myApplied) { this.myApplied = myApplied; }

    public Long getSubmitted() { return submitted; }

    public void setSubmitted(Long submitted) { this.submitted = submitted; }

    public Long getMySubmitted() { return mySubmitted; }

    public void setMySubmitted(Long mySubmitted) { this.mySubmitted = mySubmitted; }

    public Long getInterview() { return interview; }

    public void setInterview(Long interview) { this.interview = interview; }

    public Long getMyInterview() { return myInterview; }

    public void setMyInterview(Long myInterview) { this.myInterview = myInterview; }

    public Long getOffered() { return offered; }

    public void setOffered(Long offered) { this.offered = offered; }

    public Long getMyOffered() { return myOffered; }

    public void setMyOffered(Long myOffered) { this.myOffered = myOffered; }

//    public Instant getLastModifiedDate() { return lastModifiedDate; }
//
//    public void setLastModifiedDate(Instant lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; }

}
