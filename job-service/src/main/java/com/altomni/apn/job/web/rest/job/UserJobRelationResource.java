//package com.altomni.apn.job.web.rest.job;
//
//import com.alibaba.fastjson.JSONObject;
//import com.altomni.apn.common.aop.request.NoRepeatSubmit;
//import com.altomni.apn.common.config.constants.AuthoritiesConstants;
//import com.altomni.apn.common.errors.CustomParameterizedException;
//import com.altomni.apn.common.utils.HeaderUtil;
//import com.altomni.apn.common.utils.ResponseUtil;
//import com.altomni.apn.job.domain.job.UserJobRelation;
//import com.altomni.apn.job.service.dto.job.UserJobRelationDTO;
//import com.altomni.apn.job.service.job.UserJobRelationService;
//import com.altomni.apn.common.utils.SecurityUtils;
//import com.altomni.apn.job.web.rest.vm.CustomJobSearchStringVM;
//import com.altomni.apn.job.web.rest.vm.MessageVM;
//import com.altomni.apn.job.web.rest.vm.UserJobRelationVM;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiImplicitParam;
//import io.swagger.annotations.ApiImplicitParams;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.HttpStatus;
//import org.springframework.http.ResponseEntity;
//import org.springframework.security.access.annotation.Secured;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.validation.Valid;
//import java.io.IOException;
//import java.net.URI;
//import java.net.URISyntaxException;
//import java.util.List;
//import java.util.Optional;
//
///**
// * REST controller for managing UserJobRelation.
// */
//@Api(tags = {"Job", "ATS-Jobs"})
//@Slf4j
//@RestController
//@RequestMapping("/api/v3")
//public class UserJobRelationResource {
//
//    private static final String ENTITY_NAME = "userJobRelation";
//
//    @Resource
//    private UserJobRelationService userJobRelationService;
//
//    /**
//     * POST  /user-job-relations : Create a new userJobRelation.
//     *
//     * @param userJobRelation the userJobRelation to create
//     * @return the ResponseEntity with status 201 (Created) and with body the new userJobRelation, or with status 400 (Bad Request) if the userJobRelation has already an ID
//     * @throws URISyntaxException if the Location URI syntax is incorrect
//     */
//    @ApiOperation(value = "Add user to a job and set permission", notes = "Need to be job owner to add another user to manage job. Only allow to set permission.")
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
//    })
//    @PostMapping("/user-job-relations")
//    @NoRepeatSubmit
//    public ResponseEntity<UserJobRelationDTO> createUserJobRelation(@Valid @RequestBody UserJobRelation userJobRelation) throws URISyntaxException, IOException {
//        log.info("[APN: UserJobRelation @{}] REST request to save UserJobRelation : {}", SecurityUtils.getUserId(), userJobRelation);
//        if (userJobRelation.getId() != null) {
//            throw new CustomParameterizedException("The object to create cannot already have an id.");
//        }
//        UserJobRelationDTO result = userJobRelationService.create(userJobRelation);
//        return ResponseEntity.created(new URI("/api/v3/user-job-relations/" + result.getId()))
//            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
//            .body(result);
//    }
//
//
//    /**
//     * POST  /user-job-relations/multi-assign : Create multiple new userJobRelations.
//     *
//     * @param userJobRelations the userJobRelation list to create
//     * @return the ResponseEntity with status 201 (Created) and with body the new created userJobRelation list, or with status 400 (Bad Request) if the userJobRelation has already an ID
//     * @throws URISyntaxException if the Location URI syntax is incorrect
//     */
//    @ApiOperation(value = "Add user to a job and set permission", notes = "Need to be job owner to add another user to manage job. Only allow to set permission.")
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
//    })
//    @PostMapping("/user-job-relations/multi-assign")
//    @NoRepeatSubmit
//    public ResponseEntity<List<UserJobRelationDTO>> createUserJobRelations(@Valid @RequestBody List<UserJobRelation> userJobRelations) throws URISyntaxException, IOException {
//        log.info("[APN: UserJobRelation @{}] REST request to save UserJobRelation list : {}", SecurityUtils.getUserId(), userJobRelations);
//        for(UserJobRelation relation: userJobRelations) {
//            if (relation.getId() != null) {
//                throw new CustomParameterizedException("The object to create cannot already have an id." + relation);
//            }
//        }
//        List<UserJobRelationDTO> result = userJobRelationService.create(userJobRelations);
//        return ResponseEntity.created(new URI("/api/v3/user-job-relations/multi-assign")).body(result);
//    }
//
//
//    @PostMapping("/user-job-relations/multi-upsert")
//    @NoRepeatSubmit
//    public ResponseEntity<List<UserJobRelationDTO>> upsertUserJobRelations(@Valid @RequestBody List<UserJobRelation> userJobRelations) throws IOException {
//        log.info("[APN: UserJobRelation @{}] REST request to upsert UserJobRelation list : {}", SecurityUtils.getUserId(), userJobRelations);
//        List<UserJobRelationDTO> result = userJobRelationService.upsert(userJobRelations);
//        return ResponseEntity.ok(result);
//    }
//
//
//    @PostMapping("/user-job-relations/multi-requests")
//    @NoRepeatSubmit
//    public ResponseEntity<List<MessageVM<UserJobRelationDTO>>> multiUserJobRelations(@Valid @RequestBody UserJobRelationVM userJobRelationVM) {
//        log.info("[APN: UserJobRelation @{}] REST request to upsert Multi User UserJobRelation : {}", SecurityUtils.getUserId(), userJobRelationVM);
//        List<MessageVM<UserJobRelationDTO>> result = userJobRelationService.multiUserJobRelations(userJobRelationVM);
//        return ResponseEntity.ok(result);
//    }
//    /**
//     * POST  /user-job-relations/job/:jobId : Save user's custom search string on a job
//     *
//     * @param searchStringVM the userJobRelation to create
//     * @return the ResponseEntity with status 201 (Created) and with body the new userJobRelation, or with status 400 (Bad Request) if the userJobRelation has already an ID
//     * @throws URISyntaxException if the Location URI syntax is incorrect
//     */
//    @ApiOperation(value = "Save user's custom search string on a job", notes = "User need to be associated with job first. Should not be a problem if looking at my jobs.", tags = {"APN-Pro"})
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
//    })
//    @PostMapping("/user-job-relations/job/{jobId}")
//    @NoRepeatSubmit
//    public ResponseEntity<UserJobRelationDTO> saveUserSearchString(@PathVariable Long jobId, @Valid @RequestBody CustomJobSearchStringVM searchStringVM) throws URISyntaxException {
//        log.info("[APN: UserJobRelation @{}] REST request to save current user's custom search string on job : {}, {}", SecurityUtils.getUserId(), searchStringVM, jobId);
//        UserJobRelationDTO result = userJobRelationService.saveSearchString(searchStringVM.searchString, jobId);
//        return ResponseEntity.created(new URI("/api/v3/user-job-relations/" + result.getId()))
//            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
//            .body(result);
//    }
//
//    /**
//     * PUT  /user-job-relations/:id : Updates an existing userJobRelation.
//     *
//     * @param id the id of the userJobRelation to update
//     * @param userJobRelation the userJobRelation to update
//     * @return the ResponseEntity with status 200 (OK) and with body the updated userJobRelation,
//     * or with status 400 (Bad Request) if the userJobRelation is not valid,
//     * or with status 500 (Internal Server Error) if the userJobRelation couldn't be updated
//     */
//    @ApiOperation(value = "Update user's data and permission on a job", notes = "Only job owner is allowed to change the permission for other users. User can update/add search string " +
//        "for the job. It is used to check the performance of parser.")
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
//    })
//    @PutMapping("/user-job-relations/{id}")
//    @NoRepeatSubmit
//    public ResponseEntity<UserJobRelationDTO> updateUserJobRelation(@PathVariable Long id, @RequestBody UserJobRelation userJobRelation) throws IOException {
//        log.info("[APN: UserJobRelation @{}] REST request to update UserJobRelation : {}", SecurityUtils.getUserId(), userJobRelation);
//        userJobRelation.setId(id);
//        UserJobRelationDTO result = userJobRelationService.update(userJobRelation);
//        return ResponseEntity.ok()
//            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, userJobRelation.getId().toString()))
//            .body(result);
//    }
//
//    /**
//     * GET  /user-job-relations/:id : get the "id" userJobRelation.
//     *
//     * @param id the id of the userJobRelation to retrieve
//     * @return the ResponseEntity with status 200 (OK) and with body the userJobRelation, or with status 404 (Not Found)
//     */
//    @ApiOperation(value = "Get details for a single user job relation")
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
//    })
//    @GetMapping("/user-job-relations/{id}")
//    @NoRepeatSubmit
//    public ResponseEntity<UserJobRelationDTO> getUserJobRelation(@PathVariable Long id) {
//        log.info("[APN: UserJobRelation @{}] REST request to get UserJobRelation : {}", SecurityUtils.getUserId(), id);
//        UserJobRelationDTO userJobRelation = userJobRelationService.findOne(id);
//        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(userJobRelation));
//    }
//
//    /**
//     * GET  /user-job-relations/:id : get the "id" userJobRelation.
//     *
//     * @param jobId the id of the job to retrieve
//     * @return the ResponseEntity with status 200 (OK) and with body the userJobRelation, or with status 404 (Not Found)
//     */
//    @ApiOperation(value = "Get details for current user's association on a job", notes = "For getting user's custom search string", tags = {"APN-Pro"})
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
//    })
//    @GetMapping("/user-job-relations/job/{jobId}")
//    @NoRepeatSubmit
//    public ResponseEntity<List<UserJobRelationDTO>> getCurrentUserJobRelation(@PathVariable Long jobId) {
//        log.info("[APN: UserJobRelation @{}] current user get UserJobRelation on job : {}", SecurityUtils.getUserId(), jobId);
//        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(userJobRelationService.getRelationByJobId(jobId)));
//    }
//
//    /**
//     * DELETE  /user-job-relations : delete the userJobRelation.
//     *
//     * @param userJobRelation the id of the userJobRelation to delete
//     * @return the ResponseEntity with status 200 (OK)
//     */
//    @ApiOperation(value = "Delete a user's association on a job", notes = "Only job owner allows to delete other users' associations on job")
//    @ApiImplicitParams({
//        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
//    })
//    @DeleteMapping("/user-job-relations")
//    @NoRepeatSubmit
//    public ResponseEntity<Void> deleteUserJobRelation(@Valid @RequestBody UserJobRelation userJobRelation) {
//        log.info("[APN: UserJobRelation @{}] REST request to delete UserJobRelation : {}", SecurityUtils.getUserId(), userJobRelation);
//        userJobRelationService.delete(userJobRelation);
//        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, userJobRelation.toString())).build();
//    }
//
//}
