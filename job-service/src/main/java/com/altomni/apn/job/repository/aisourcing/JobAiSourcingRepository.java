package com.altomni.apn.job.repository.aisourcing;

import com.altomni.apn.job.domain.aisourcing.JobAiSourcing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Repository
public interface JobAiSourcingRepository extends JpaRepository<JobAiSourcing, Long> {


    @Query("select j.jobId from JobAiSourcing j where j.jobId in :jobIds")
    Set<Long> getExistJobIds(@Param("jobIds") Set<Long> jobIds);

    List<JobAiSourcing> findFirstByJobId(Long jobId);

    @Modifying
    @Query("update JobAiSourcing j set j.enabled=:enabled where j.jobId=:jobId")
    void updateAiSourcingStatusByJobId(@Param("jobId") Long jobId, @Param("enabled") boolean enabled);

    @Modifying
    @Query("update JobAiSourcing j set j.jobInfoUpdated=:jobInfoUpdated where j.jobId=:jobId")
    void setJobInfoUpdated(@Param("jobId") Long jobId, @Param("jobInfoUpdated") boolean jobInfoUpdated);

    @Modifying
    @Query("update JobAiSourcing j set j.jobInfoUpdated=:jobInfoUpdated, j.searchVersion=j.searchVersion+1 where j.jobId=:jobId")
    void setJobInfoUpdatedAndIncreaseSearchVersion(@Param("jobId") Long jobId, @Param("jobInfoUpdated") boolean jobInfoUpdated);
}
