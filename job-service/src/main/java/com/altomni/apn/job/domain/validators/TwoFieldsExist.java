package com.altomni.apn.job.domain.validators;

import javax.validation.Constraint;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Constraint(validatedBy = TwoFieldsExistValidator.class)
@Target({ ElementType.TYPE })
@Retention(RetentionPolicy.RUNTIME)
public @interface TwoFieldsExist {

    String message() default "Missing values";

    Class<?>[] groups() default {};

    Class<?>[] payload() default {};

    String field1();

    String field2();

    @Target({ ElementType.TYPE })
    @Retention(RetentionPolicy.RUNTIME)
    @interface List {
        TwoFieldsExist[] value();
    }
}
