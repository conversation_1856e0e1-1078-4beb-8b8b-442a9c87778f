package com.altomni.apn.job.domain.validators;

import org.springframework.beans.BeanWrapperImpl;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class TwoFieldsExistValidator implements ConstraintValidator<TwoFieldsExist, Object> {

    private String field1;

    private String field2;

    @Override
    public void initialize(TwoFieldsExist constraintAnnotation) {
        this.field1 = constraintAnnotation.field1();
        this.field2 = constraintAnnotation.field2();
    }

    @Override
    public boolean isValid(Object value, ConstraintValidatorContext context) {
        Object field1Value = new BeanWrapperImpl(value).getPropertyValue(field1);
        Object field2Value = new BeanWrapperImpl(value).getPropertyValue(field2);
        return field1Value != null || field2Value != null;
    }
}
