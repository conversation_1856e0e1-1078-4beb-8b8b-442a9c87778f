package com.altomni.apn.job.web.rest.vm;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;

/**
 * View Model object for storing the user's key and password.
 */

@Entity
@Data
public class CompanyVM {

    @Id
    private Long companyId;

    private String company;

    public CompanyVM(Long companyId, String company) {
        this.companyId = companyId;
        this.company = company;
    }

    public CompanyVM() {
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String key) {
        this.company = company;
    }

    @Override
    public String toString() {
        return "CompanyVM{" +
            "companyId=" + companyId +
            ", company='" + company + '\'' +
            '}';
    }
}
