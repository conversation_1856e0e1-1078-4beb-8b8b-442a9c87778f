package com.altomni.apn.job.domain.project;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

@ApiModel(description = "JobProject is used for private job")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "job_project")
public class JobProject extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Job project name")
    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;
}
