package com.altomni.apn.job.domain.email;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.email.EmailModuleTypeEnum;
import com.altomni.apn.common.domain.enumeration.email.EmailModuleTypeEnumConverter;
import com.altomni.apn.common.domain.enumeration.system.Status;
import com.altomni.apn.common.domain.enumeration.system.StatusConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "send_email_record")
public class SendEmailRecord extends AbstractAuditingEntity implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Convert(converter = StatusConverter.class)
    @Column(name = "status")
    private Status status;

    @Column(name = "type")
    @Convert(converter = EmailModuleTypeEnumConverter.class)
    private EmailModuleTypeEnum type;

    @Column(name = "response_code")
    private Integer responseCode;

    @Column(name = "request_param")
    private String requestParam;

    @Column(name = "response_message")
    private String responseMessage;

}
