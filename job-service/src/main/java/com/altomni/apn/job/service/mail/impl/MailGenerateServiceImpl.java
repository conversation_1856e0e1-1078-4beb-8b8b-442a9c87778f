package com.altomni.apn.job.service.mail.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.JobPermission;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.job.JobNote;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.HtmlUtil;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactVM;
import com.altomni.apn.job.config.env.ApplicationProperties;
import com.altomni.apn.job.domain.enumeration.JobPriority;
import com.altomni.apn.job.domain.job.JobCompanyContactRelation;
import com.altomni.apn.job.repository.job.JobCompanyContactRelationRepository;
import com.altomni.apn.job.repository.job.JobLocationRepository;
import com.altomni.apn.job.repository.job.JobNoteRepository;
import com.altomni.apn.job.service.company.CompanyService;
import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.altomni.apn.job.service.mail.MailGenerateService;
import com.altomni.apn.job.service.mail.MailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.StringJoiner;
import java.util.stream.Collectors;

@Slf4j
@Service
public class MailGenerateServiceImpl implements MailGenerateService {
    //private MailService mailService;
    private static String DETAIL_FOR_JOB = "For more details on the job, please follow the link below:";
    private static String EXTRA_JOB_INFO = "Additional Job Information:";

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private JobNoteRepository jobNoteRepository;

    @Resource
    private JobCompanyContactRelationRepository jobCompanyContactRelationRepository;

    @Resource
    private CompanyService companyService;

    @Resource
    private EsFillerJobService esFillerJobService;

    @Resource
    private MailService mailService;

    @Resource
    private JobLocationRepository jobLocationRepository;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Override
    public void sendHtmlMailForUserJobRelationFromSupport(List<String> receivers, JobV3 job) {
        JSONObject jo = JSONUtil.parseObj(job.getJobExtendedInfo());
        MailVM mailVM = new MailVM();
        String primaryRecruiterName = "";
        String primarySalesName = "";
        String companyName = "";
        List<AssignedUserDTO> assignedUserDTOList = companyService.getApplicationUsers(job.getCompanyId()).getBody();
        if(CollUtil.isNotEmpty(assignedUserDTOList)) {
            Optional<AssignedUserDTO> prUserDTO = assignedUserDTOList.stream().filter(a -> JobPermission.PRIMARY_RECRUITER.name().equals(a.getPermission())).findFirst();
            if (prUserDTO.isPresent()) {
                primaryRecruiterName = CommonUtils.formatFullName(prUserDTO.get().getFirstName(), prUserDTO.get().getLastName());
            }
            Optional<AssignedUserDTO> acUserDTO = assignedUserDTOList.stream().filter(a -> JobPermission.AC.name().equals(a.getPermission())).findFirst();
            if (acUserDTO.isPresent()) {
                primarySalesName = CommonUtils.formatFullName(acUserDTO.get().getFirstName(), acUserDTO.get().getLastName());
            }
        }
        SalesLeadClientContactVM contact = new SalesLeadClientContactVM();
        List<JobCompanyContactRelation> jobCompanyContactRelationList = jobCompanyContactRelationRepository.findAllByJobId(job.getId());
        if(CollectionUtil.isNotEmpty(jobCompanyContactRelationList)){
            contact = companyService.getSalesLeadClientContact(jobCompanyContactRelationList.get(0).getClientContactId()).getBody();
        }

        List<JobNote> listNotes = jobNoteRepository.findByJobIdAndVisibleOrderByCreatedDateDesc(job.getId(), true);

        CompanyDTO company = companyService.getCompany(job.getCompanyId()).getBody();
        if (company != null) {
            companyName = company.getName();
        }

        mailVM.setFrom(applicationProperties.getSupportSender());
        mailVM.setIsSystemEmail(true);
        mailVM.setTo(receivers);
        mailVM.setSubject(generateJobAssignMailSubject(job));
        switch (esFillerJobService.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId())) {
            case FULL_TIME:
                mailVM.setContent(generateFullTimeJobAssignMailBodyInHTML(job, primaryRecruiterName, primarySalesName, contact, listNotes, companyName));
                break;
            case CONTRACT:
                mailVM.setContent(generateContractJobAssignMailBodyInHTML(job, primaryRecruiterName, primarySalesName, contact, listNotes, companyName));
                break;
            case PAY_ROLL:
                mailVM.setContent(generatePayRollingJobAssignMailBodyInHTML(job, primaryRecruiterName, primarySalesName, contact, listNotes, companyName));
                break;
        }

        try {
            mailService.sendHtmlMail(mailVM);
        } catch (Exception e) {
            log.error("Send Email {} Error:{}", mailVM.toString(), e.getMessage());
        }
    }

    private String generateFullTimeJobAssignMailBodyInHTML(JobV3 job, String primaryRecruiterName, String primarySalesName, SalesLeadClientContactVM contact, List<JobNote> listNotes, String companyName) {

        StringBuilder sb = new StringBuilder();

        sb.append("<body>");

        JSONObject jo = JSONUtil.parseObj(job.getJobExtendedInfo());

        HtmlUtil.appendParagraphCell(sb, generateJobAssignInception(job));

        HtmlUtil.appendParagraphCell(sb, EXTRA_JOB_INFO);

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Title", job.getTitle()));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Job ID", ObjectUtil.toString(job.getId())));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Contact", generateStringFromContact(contact)));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Company", companyName));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Primary Recruiter",  ObjectUtil.isEmpty(primaryRecruiterName) == true ? "" : primaryRecruiterName));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Primary Sales", ObjectUtil.isEmpty(primarySalesName) == true ? "" : primarySalesName));

        if(ObjectUtil.isNotEmpty(jo.get("salaryRange"))) {
            int currency = ObjectUtil.isNotEmpty(job.getCurrency()) ? job.getCurrency() : 0;
            EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(currency);
            HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Pay Rate",
                generateRateString(Convert.toBigDecimal(Convert.convert(RangeDTO.class, jo.get("salaryRange")).getGte()), Convert.toBigDecimal(Convert.convert(RangeDTO.class, jo.get("salaryRange")).getLte()),
                        jo.get("payType") == null ? RateUnitType.YEARLY : RateUnitType.valueOf(Convert.toStr(jo.get("payType"))), enumCurrency)));
        }

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Start Date",
            DateUtil.instantToDateString(job.getStartDate())));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Openings",
            generateStringFromInteger(job.getOpenings())));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Position Type",
            generateJobTypeString(esFillerJobService.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId()))));

        String locations = "";
        String json = jobLocationRepository.findOriginalLocsAllByJobId(job.getId());
        if (StrUtil.isNotBlank(json)) {
            locations = generateLocationString(json);
        }
        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Location", locations));


        HtmlUtil.appendParagraphCell(sb, "Description:");
        com.alibaba.fastjson.JSONObject extendedInfo = JSON.parseObject(job.getJobExtendedInfo(), com.alibaba.fastjson.JSONObject.class);

        HtmlUtil.appendParagraphCell(sb, extendedInfo.getString("jdText") == null ? "" : extendedInfo.getString("jdText")
            .replaceAll("(\r\n|\r|\n|\n\r)", "<br>"));

        HtmlUtil.appendParagraphCell(sb, "Note:");
        HtmlUtil.appendParagraphCell(sb, generateJobNoteString(listNotes));

        // Click link for more job detail
        HtmlUtil.appendParagraphCell(sb, DETAIL_FOR_JOB);
        HtmlUtil.appendLinkTag(sb, "link", generateUrlString(applicationProperties.getBaseUrl(),"/jobs/detail", job.getId().toString()));

        sb.append("</body>");

        return sb.toString();
    }

    private String generateContractJobAssignMailBodyInHTML(JobV3 job, String primaryRecruiterName, String primarySalesName, SalesLeadClientContactVM contact, List<JobNote> listNotes, String companyName) {

        StringBuilder sb = new StringBuilder();

        sb.append("<body>");

        JSONObject jo = JSONUtil.parseObj(job.getJobExtendedInfo());

        HtmlUtil.appendParagraphCell(sb, generateJobAssignInception(job));

        HtmlUtil.appendParagraphCell(sb, EXTRA_JOB_INFO);

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Title", job.getTitle()));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Job ID", ObjectUtil.toString(job.getId())));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Contact", generateStringFromContact(contact)));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Company", companyName));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Primary Recruiter",  ObjectUtil.isEmpty(primaryRecruiterName) == true ? "" : primaryRecruiterName));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Primary Sales", ObjectUtil.isEmpty(primarySalesName) == true ? "" : primarySalesName));

        int currency = ObjectUtil.isNotEmpty(job.getCurrency()) ? job.getCurrency() : 0;
        EnumCurrency enumCurrency = enumCurrencyService.findEnumCurrencyById(currency);

        if(ObjectUtil.isNotEmpty(jo.get("salaryRange"))) {
            HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Pay Rate",
                generateRateString(Convert.toBigDecimal(Convert.convert(RangeDTO.class, jo.get("salaryRange")).getGte()), Convert.toBigDecimal(Convert.convert(RangeDTO.class, jo.get("salaryRange")).getLte()), jo.get("payType") == null ?
                        RateUnitType.YEARLY : RateUnitType.valueOf(Convert.toStr(jo.get("payType"))), enumCurrency)));
        }

        if(ObjectUtil.isNotEmpty(jo.get("billRange"))) {
            HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Bill Rate",
                generateRateString(Convert.toBigDecimal(Convert.convert(RangeDTO.class, jo.get("billRange")).getGte()), Convert.toBigDecimal(Convert.convert(RangeDTO.class, jo.get("billRange")).getLte()), jo.get("payType") == null ?
                        RateUnitType.YEARLY : RateUnitType.valueOf(Convert.toStr(jo.get("payType"))), enumCurrency)));
        }

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Start Date",
            DateUtil.instantToDateString(job.getStartDate())));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("End Date",
            DateUtil.instantToDateString(job.getEndDate())));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Openings",
            generateStringFromInteger(job.getOpenings())));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Position Type",
            generateJobTypeString(esFillerJobService.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId()))));

        String locations = "";
        String json = jobLocationRepository.findOriginalLocsAllByJobId(job.getId());
        if (StrUtil.isNotBlank(json)) {
            locations = generateLocationString(json);
        }
        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Location", locations));


        HtmlUtil.appendParagraphCell(sb, "Description:");
        com.alibaba.fastjson.JSONObject extendedInfo = JSON.parseObject(job.getJobExtendedInfo(), com.alibaba.fastjson.JSONObject.class);
        HtmlUtil.appendParagraphCell(sb, extendedInfo.getString("jdText") == null ? "" : extendedInfo.getString("jdText")
                .replaceAll("(\r\n|\r|\n|\n\r)", "<br>"));

        HtmlUtil.appendParagraphCell(sb, "Note:");
        HtmlUtil.appendParagraphCell(sb, generateJobNoteString(listNotes));

        // Click link for more job detail
        HtmlUtil.appendParagraphCell(sb, DETAIL_FOR_JOB);
        HtmlUtil.appendLinkTag(sb, "link", generateUrlString(applicationProperties.getBaseUrl(),"/jobs/detail", job.getId().toString()));

        sb.append("</body>");

        return sb.toString();
    }

    private String generatePayRollingJobAssignMailBodyInHTML(JobV3 job, String primaryRecruiterName, String primarySalesName, SalesLeadClientContactVM contact, List<JobNote> listNotes, String companyName) {

        StringBuilder sb = new StringBuilder();

        sb.append("<body>");

        JSONObject jo = JSONUtil.parseObj(job.getJobExtendedInfo());

        HtmlUtil.appendParagraphCell(sb, generateJobAssignInception(job));

        HtmlUtil.appendParagraphCell(sb, EXTRA_JOB_INFO);

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Title", job.getTitle()));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Job ID", ObjectUtil.toString(job.getId())));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Contact", generateStringFromContact(contact)));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Company", companyName));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Primary Recruiter",  ObjectUtil.isEmpty(primaryRecruiterName) == true ? "" : primaryRecruiterName));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Primary Sales", ObjectUtil.isEmpty(primarySalesName) == true ? "" : primarySalesName));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Position Type",
            generateJobTypeString(esFillerJobService.getJobTypeByRecruitmentProcessId(job.getRecruitmentProcessId()))));

        String locations = "";
        String json = jobLocationRepository.findOriginalLocsAllByJobId(job.getId());
        if (StrUtil.isNotBlank(json)) {
            locations = generateLocationString(json);
        }

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Location", locations));

        HtmlUtil.appendParagraphCell(sb, "Description:");
        HtmlUtil.appendParagraphCell(sb, jo.getStr("jdText") == null ? "" : jo.getStr("jdText").replaceAll("(\r\n|\r|\n|\n\r)", "<br>"));

        HtmlUtil.appendParagraphCell(sb, "Note:");
        HtmlUtil.appendParagraphCell(sb, generateJobNoteString(listNotes));

        // Click link for more job detail
        HtmlUtil.appendParagraphCell(sb, DETAIL_FOR_JOB);
        HtmlUtil.appendLinkTag(sb, "link", generateUrlString(applicationProperties.getBaseUrl(),"/jobs/detail", job.getId().toString()));

        sb.append("</body>");

        return sb.toString();
    }

    /**
     * Generate Inception of the email
     * @param job job
     * @return String
     */
    private String generateJobAssignInception(JobV3 job) {
        StringBuilder sb = new StringBuilder();
        sb.append("This is to inform you that you have just been assigned to ");
        if (null != job.getId()) {
            sb.append("Job Reference #").append(job.getId()).append(" ");
        }

        if (null != job.getTitle()) {
            sb.append("(").append(job.getTitle()).append(")");
        }
        return sb.toString();
    }

    /**
     * Users are assigned to the job
     * @param content Message of the email
     * @param from The email from the sender who gives the message
     * @return String
     */
    private String generateSendMailProxyForIGPBodyInHTML(String name, String phone, String roleType, String content, String from) {
        StringBuilder sb = new StringBuilder();

        sb.append("<body>");

        content = "\n" + content;

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Name", name));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Email", from));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Phone", phone));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("UserRole", roleType));

        HtmlUtil.appendParagraphCell(sb, generateKeyValueString("Message",
            content.replaceAll("(\r\n|\r|\n|\n\r)", "<br>")));

        HtmlUtil.appendEmailLinkTag(sb, "Reply", from);

        sb.append("</body>");

        return sb.toString();
    }

    /**
     * Users are assigned to the job
     * @param name The name from the sender
     * @param from The email from the sender
     * @param phone The phone from the sender
     * @param roleType The identity type of sender
     * @param content The message content
     */
    @Override
    public void sendEmailProxyForIGP(String name, String from, String phone, String roleType, String content) {
        MailVM mailVM = new MailVM();

        List<String> tolist = new ArrayList<>();
        tolist.add(applicationProperties.getIpgProxySender());

        String subject = "An email from a " + roleType;

        mailVM.setFrom(applicationProperties.getIpgProxySender());
        mailVM.setTo(tolist);
        mailVM.setSubject(subject);
        // Make source email be a link in content for reply
        mailVM.setContent(generateSendMailProxyForIGPBodyInHTML(name, phone, roleType, content, from));
        try {
            mailService.sendHtmlMail(mailVM);
        } catch (Exception e) {
            log.error("Send Email {} Error:{}", mailVM.toString(), e.getMessage());
        }
    }

    /**
     * Generate String with pair of key and value
     * @param key Primary recruiter of the job
     * @param value Job owner
     * @return String
     */
    private String generateKeyValueString(String key, String value) {
        return key + " : " + value;
    }

    /**
     * Generate String from Integer
     * @param integer integer
     * @return String
     */
    private String generateStringFromInteger(Integer integer) {
        if (null != integer) {
            return integer.toString();
        }
        return "";
    }

    /**
     * Generate priority String from JobPriority
     * @param priority the priority of the job, default: active
     * @return String
     */
    private String generateStringFromPriority(JobPriority priority) {
        if (null != priority) {
            return priority.toString();
        }
        return "";
    }

    /**
     * Generate contact name String from ClientContact
     * @param contact Client contact of the job
     * @return String
     */
    private String generateStringFromContact(SalesLeadClientContactVM contact) {
        if (null != contact) {
            return contact.getName();
        }
        return "";
    }

    /**
     * Generate job type String from JobType
     * @param jobType Type of the job
     * @return String
     */
    private String generateJobTypeString(JobType jobType) {
        if (null != jobType) {
            return jobType.toString();
        }
        return "";
    }

    /**
     * Generate location String in the job
     * @param job job
     * @return String
     */
    private String generateLocationString(JobV3 job) {
        com.alibaba.fastjson.JSONObject extendedInfo = JSON.parseObject(job.getJobExtendedInfo(), com.alibaba.fastjson.JSONObject.class);
        JSONArray ja = extendedInfo.getJSONArray("locations");
        if (ja == null) {
            return null;
        }
        List<LocationDTO> locationDTOList = ja.toJavaList(LocationDTO.class);
        return locationDTOList.stream().map(s -> {
            if (ObjectUtil.isEmpty(s.getLocation())) {
                return s.getCity() + ", " + s.getProvince() + ", " + s.getCountry();
            } else {
                return s.getLocation();
            }
        }).collect(Collectors.joining(";"));
    }

    private String generateLocationString(String json) {
        JSONArray ja = JSON.parseArray(json);
        if (ja == null) {
            return null;
        }
        List<LocationDTO> locationDTOList = ja.toJavaList(LocationDTO.class);
        return locationDTOList.stream().map(s -> {
            if (ObjectUtil.isEmpty(s.getLocation())) {
                StringJoiner result = new StringJoiner(StrUtil.COMMA);
                spliceNonEmptyStrs(result, s.getCity());
                spliceNonEmptyStrs(result, s.getProvince());
                spliceNonEmptyStrs(result, s.getCountry());
                return result.toString();
            } else {
                return s.getLocation();
            }
        }).collect(Collectors.joining(";"));
    }

    private void spliceNonEmptyStrs(StringJoiner result, String str) {
        if (ObjectUtil.isNotEmpty(str)) {
            result.add(str);
        }
    }

    /**
     * Generate dollar range with type
     * @param from beginning of range
     * @param to end of range
     * @param type rate type of the job
     * @return String
     */
    private String generateRateString(BigDecimal from, BigDecimal to, RateUnitType type, EnumCurrency enumCurrency) {
        StringBuilder sb = new StringBuilder();

        if (null != from) {
            sb.append(enumCurrency.getLabel1()).append(from.toString());
            if (null != to) {
                sb.append("-").append(enumCurrency.getLabel1()).append(to.toString());
            }
            if (null != type) {
                sb.append("/");
                String typeString = type.toString();
                sb.append(typeString, 0, typeString.length());
            }
        }
        return sb.toString();
    }

    /**
     * Generate URL String
     * @param domain domain
     * @param path path
     * @param target target
     * @return String
     */
    private String generateUrlString(String domain, String path, String target) {
        return domain + path + "/" + target;
    }

    /**
     * Generate note information and sorted by nearest date
     * @param listNotes Note list of the job
     * @return String
     */
    private String generateJobNoteString(List<JobNote> listNotes) {
        StringBuilder sb = new StringBuilder();

        if (null == listNotes || listNotes.isEmpty()) {
            return "";
        }

        for (JobNote note : listNotes) {
            sb.append("<p>");
            sb.append(DateUtil.instantToDateString(note.getCreatedDate()));
            HtmlUtil.appendBreakCell(sb);
            sb.append(note.getTitle());
            HtmlUtil.appendBreakCell(sb);
            sb.append(note.getNote()
                .replaceAll("(\r\n|\r|\n|\n\r)", "<br>"));
            sb.append("</p>");
        }
        return sb.toString();
    }

    /**
     * Generate the subject of email for job assign use
     * @param job Primary recruiter of the job
     * @return String
     */
    private String generateJobAssignMailSubject(JobV3 job) {
        StringBuilder sb = new StringBuilder("You have just been assigned to ");
        if (null != job.getId()) {
            sb.append("Job Reference #");
            sb.append(job.getId());
            sb.append(" ");
        }

        CompanyDTO company = companyService.getCompany(job.getCompanyId()).getBody();
        if(ObjectUtil.isNotEmpty(company)) {
            sb.append("for ");
            sb.append(company.getName());
        }
        return sb.toString();
    }

    @Override
    public void sendEmail(List<String> receivers, String subject, String content) {
        log.info("[MailGenerateService] Send application status change email subject: {}", subject, " content: {}" + content);
        MailVM mailVm = new MailVM(applicationProperties.getSupportSender(), receivers, null, null, subject, content, null, true);
        try {
            mailService.sendHtmlMail(mailVm);
        } catch (Exception e) {
            log.error("[MailGenerateService] Send apply candidate Email {} Error:{}", mailVm.toString(), e.getMessage());
        }
    }
}
