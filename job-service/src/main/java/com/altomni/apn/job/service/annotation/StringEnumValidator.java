package com.altomni.apn.job.service.annotation;

import javax.validation.Constraint;
import javax.validation.Payload;
import java.lang.annotation.*;

import static java.lang.annotation.ElementType.*;

@Documented
@Constraint(validatedBy = StringEnumValidatorImpl.class)
@Target({ FIELD })
@Retention(RetentionPolicy.RUNTIME)
public @interface StringEnumValidator {
    public abstract String message() default "Invalid Enum value. Please check.";
    public abstract Class<?>[] groups() default {};
    public abstract Class<? extends Payload>[] payload() default {};
    public abstract Class<? extends java.lang.Enum<?>> enumClass();
}

