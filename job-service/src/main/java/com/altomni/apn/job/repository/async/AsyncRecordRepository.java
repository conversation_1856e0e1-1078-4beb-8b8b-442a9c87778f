package com.altomni.apn.job.repository.async;

import com.altomni.apn.job.domain.async.AsyncRecord;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;
import com.altomni.apn.common.domain.enumeration.user.Status;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;


/**
 * Spring Data JPA repository for the AsyncRecord entity.
 */
@Repository
public interface AsyncRecordRepository extends JpaRepository<AsyncRecord, Long> {

    List<AsyncRecord> findAllByAsyncTypeAndDataTypeAndStatus(AsyncEnum asyncType, AsyncEnum dataType, Status status);

    @Query(value =  "select a.* from async_record a where a.async_type = ?1 " +
            "and a.data_type = ?2 and a.status = ?3 and a.data_id%10 = ?4", nativeQuery = true)
    List<AsyncRecord> findAllByAsyncTypeAndDataTypeAndStatus(Integer asyncType, Integer dataType, Integer status, Integer endId);

    @Query(value =  "select a.* from async_record a where a.async_type = ?1 " +
        "and a.data_type = ?2 and a.status = ?3 and a.data_id%?5 = ?4", nativeQuery = true)
    List<AsyncRecord> findAllByAsyncTypeAndDataTypeAndStatus(Integer asyncType, Integer dataType, Integer status, Integer endId, Integer threads);

    @Transactional
    @Modifying
    void deleteByAsyncTypeAndDataIdAndDataTypeAndStatus(AsyncEnum asyncEnum, Long talentId, AsyncEnum dataType, Status status);

    @Transactional
    @Modifying
    @Query(value = "UPDATE AsyncRecord a SET a.status = com.altomni.apn.common.domain.enumeration.user.Status.Available WHERE a.asyncType = com.altomni.apn.job.domain.enumeration.AsyncEnum.ASYNC_TYPE_SINGLE AND a.dataType = :dataType AND a.status = com.altomni.apn.common.domain.enumeration.user.Status.Failed AND a.dataId IN (:ids)")
    void updateSyncFailureToSuccess(@Param("dataType") AsyncEnum dataType, @Param("ids") List<Long> ids);

    @Query(value = "SELECT COUNT(DISTINCT id) FROM AsyncRecord WHERE dataType = ?1 AND dataId = ?2 AND asyncType = com.altomni.apn.job.domain.enumeration.AsyncEnum.ASYNC_TYPE_ESFILLER AND status = com.altomni.apn.common.domain.enumeration.user.Status.Failed")
    Integer countSyncError(AsyncEnum dataType, Long dataId);

    @Transactional
    @Modifying
    @Query(value = "DELETE FROM AsyncRecord WHERE dataType = :dataType AND dataId IN :dataIds AND asyncType = com.altomni.apn.job.domain.enumeration.AsyncEnum.ASYNC_TYPE_ESFILLER AND status = com.altomni.apn.common.domain.enumeration.user.Status.Failed")
    void clearAllSyncRecordError(@Param("dataType") AsyncEnum dataType, @Param("dataIds") List<Long> dataIds);
}
