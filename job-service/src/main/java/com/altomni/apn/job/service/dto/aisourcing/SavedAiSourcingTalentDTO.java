package com.altomni.apn.job.service.dto.aisourcing;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SavedAiSourcingTalentDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long talentId;
    private String talentName;
    private String lastNodeType;
    private Long jobId;
    private String jobTitle;
    private Long companyId;
    private String companyName;
    private Instant lastModifiedDate;
}
