package com.altomni.apn.job.repository.job;

import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.job.domain.job.ColumnPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the UserPreference entity.
 */
@Repository
public interface ColumnPreferenceRepository extends JpaRepository<ColumnPreference,Long>, QuerydslPredicateExecutor<ColumnPreference> {

    ColumnPreference findByUserIdAndModule(Long userId, ModuleType module);

    @Modifying
    @Query(value = "UPDATE column_preference j  set j.creation_type = ?1 where j.user_id = ?2 and j.module = ?3",nativeQuery = true)
    int updateCreationType(String creationType, Long userId, Integer module);

    @Modifying
    @Query(value = "UPDATE column_preference j set j.item_sort = ?1, j.item_sort_all = ?2, j.page_size = ?3 where j.module = ?4 and j.user_id = ?5 ", nativeQuery = true)
    int updateByUserId(String itemSort, String itemSortAll, Integer pageSize, Integer module, Long userId);

}
