package com.altomni.apn.job.service.dto.redis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class SimilarJobResponse implements Serializable {

    private static final long serialVersionUID = 7547052176540537819L;

    @ApiModelProperty(value = "[STARTED, FINISHED, ON_GOING, ERROR]")
    private String status;

    private Integer total;

    private List<SimilarTenantJob> jobs = new ArrayList<>();

    public SimilarJobResponse(RedisResponse response) {
        this.total = response.getTotal();
        this.status = response.getStatus();
    }
}
