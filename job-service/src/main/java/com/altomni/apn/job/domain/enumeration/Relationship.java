package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum Relationship implements ConvertedEnum<Integer> {
    AND(0),
    OR(1);

    private final int dbValue;

    Relationship(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<Relationship, Integer> resolver =
            new ReverseEnumResolver<>(Relationship.class, Relationship::toDbValue);

    public static Relationship fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
