package com.altomni.apn.job.repository.job;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.job.domain.job.MyJob;
import com.altomni.apn.job.web.rest.vm.CompanyVM;
import com.altomni.apn.job.web.rest.vm.MyApplication;
import com.altomni.apn.job.web.rest.vm.MyJobVo;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;


/**
 * Spring Data JPA repository for the Job entity.
 */
@SuppressWarnings("unused")
@Repository
public interface JobRepositoryCustom {

    List<MyJob> findMyJobsForDashboard(Long userId, Long companyId, Instant from, Instant to, Boolean myJobsOnly, Long tenantId);

    List<MyApplication> findMyApplicationsForDashboard(Long userId, Long jobId, NodeType status);

    List<CompanyVM> findAllCompanyForDashboard(Long userId, Instant from, Instant to, Long tenantId);

    MyJobVo findMyJobs(List<Long> userIdList, Long tenantId, Instant startTime, Instant endTime, Long projectId);

    Integer findNotRecommendedWithin14Days(List<Long> userIdList, Long tenantId, Long projectId,Integer day);

    Integer findNotInterviewWithin14Days(List<Long> userIdList, Long tenantId, Long projectId,Integer day);
}

