package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.talent.CreationTalentType;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.domain.job.ColumnPreference;
import com.altomni.apn.job.repository.job.ColumnPreferenceRepository;
import com.altomni.apn.job.service.dto.job.ColumnPreferenceDTO;
import com.altomni.apn.job.service.dto.talent.CreationTypeDTO;
import com.altomni.apn.job.service.job.ColumnPreferenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class ColumnPreferenceServiceImpl implements ColumnPreferenceService {

    private final Logger log = LoggerFactory.getLogger(ColumnPreferenceServiceImpl.class);

    private final ColumnPreferenceRepository columnPreferenceRepository;

    public ColumnPreferenceServiceImpl(ColumnPreferenceRepository columnPreferenceRepository){
        this.columnPreferenceRepository = columnPreferenceRepository;
    }

    @Override
    public ColumnPreferenceDTO findOne(Long userId, ModuleType moduleType) {
        ColumnPreference result = columnPreferenceRepository.findByUserIdAndModule(userId, moduleType);
        if(ObjectUtil.isEmpty(result)){
            return new ColumnPreferenceDTO();
        }
        return ColumnPreferenceDTO.fromColumnPreference(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ColumnPreferenceDTO columnPreferenceDTO) {
        ColumnPreference result = columnPreferenceRepository.findByUserIdAndModule(SecurityUtils.getUserId(), columnPreferenceDTO.getModule());
        if(ObjectUtil.isNotEmpty(result)) {
            if(ObjectUtil.isEmpty(columnPreferenceDTO.getItemSort())) {
                columnPreferenceDTO.setItemSort(result.getItemSort());
            }
            if(ObjectUtil.isEmpty(columnPreferenceDTO.getItemSortAll())) {
                columnPreferenceDTO.setItemSortAll(result.getItemSortAll());
            }
            if(ObjectUtil.isEmpty(columnPreferenceDTO.getPageSize())) {
                columnPreferenceDTO.setPageSize(result.getPageSize());
            }
            columnPreferenceRepository.updateByUserId(columnPreferenceDTO.getItemSort(), columnPreferenceDTO.getItemSortAll(), columnPreferenceDTO.getPageSize(), columnPreferenceDTO.getModule().toDbValue(), SecurityUtils.getUserId());
        }else {
            ColumnPreference jobColumnPreference = ColumnPreference.fromColumnPreferenceDTO(columnPreferenceDTO);
            columnPreferenceRepository.saveAndFlush(jobColumnPreference);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateByUserId(Long userId, Object type, ModuleType moduleType) {
        ColumnPreference result = columnPreferenceRepository.findByUserIdAndModule(userId, moduleType);
        if(ObjectUtil.isNotEmpty(result)) {
            CreationTypeDTO creationTypeDTO = new CreationTypeDTO();
            if(ObjectUtil.isNotEmpty(result.getCreationType())) {
                creationTypeDTO = JsonUtil.fromJson(JSONUtil.toJsonStr(result.getCreationType()), CreationTypeDTO.class);
            }
            if(type instanceof JobType) {
                creationTypeDTO.setJobType(StrUtil.toString(type));
            }
            if(type instanceof CreationTalentType) {
                creationTypeDTO.setCreationTalentType(StrUtil.toString(type));
            }
            columnPreferenceRepository.updateCreationType(JSONUtil.toJsonStr(creationTypeDTO), userId, moduleType.toDbValue());
        }else {
            ColumnPreference columnPreference = new ColumnPreference();
            CreationTypeDTO creationTypeDTO = new CreationTypeDTO();
            if(type instanceof JobType) {
                creationTypeDTO.setJobType(StrUtil.toString(type));
            }
            if(type instanceof CreationTalentType) {
                creationTypeDTO.setCreationTalentType(StrUtil.toString(type));
            }
            columnPreference.setCreationType(JSONUtil.toJsonStr(creationTypeDTO));
            columnPreference.setUserId(userId);
            columnPreference.setModule(moduleType);
            columnPreferenceRepository.saveAndFlush(columnPreference);
        }
    }

}
