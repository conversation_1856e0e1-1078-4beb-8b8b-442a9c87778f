package com.altomni.apn.job.service.dto.aisourcing;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RejectCandidateDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Integer enumTagId;

    private String reason;

}
