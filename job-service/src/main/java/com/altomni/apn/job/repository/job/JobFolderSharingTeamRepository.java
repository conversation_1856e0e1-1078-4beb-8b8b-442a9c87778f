package com.altomni.apn.job.repository.job;


import com.altomni.apn.common.dto.folder.FolderSharedTeamDTO;
import com.altomni.apn.common.dto.folder.IFolderNamePermissionDTO;
import com.altomni.apn.common.dto.folder.IFolderSharedTeamNameDTO;
import com.altomni.apn.job.domain.job.JobFolderSharingTeam;
import com.altomni.apn.job.service.dto.folder.SharedFolderBriefDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface JobFolderSharingTeamRepository extends JpaRepository<JobFolderSharingTeam, Long> {
    List<JobFolderSharingTeam> findAllByJobFolderId(Long jobFolderId);


    Optional<JobFolderSharingTeam> findByJobFolderIdAndTeamId(Long folderId, Long teamId);

    @Modifying
    @Query("update JobFolderSharingTeam jf set jf.excludedUserIds = null where jf.jobFolderId = :folderId and jf.teamId in :teamIds")
    void updateExcludedUserIdsToNullByJobFolderIdAndTeamIdIn(@Param("folderId") Long folderId, @Param("teamIds") List<Long> teamIds);



    @Query("SELECT st FROM JobFolderSharingTeam st " +
            "INNER JOIN PermissionUserTeam put on st.teamId  = put.teamId " +
            "WHERE st.jobFolderId = :folderId AND put.userId = :userId")
    List<JobFolderSharingTeam> findByJobFolderIdAndUserId(@Param("folderId") Long folderId, @Param("userId") Long userId);


    List<JobFolderSharingTeam> findAllByTeamId(Long teamId);


    @Query("SELECT new com.altomni.apn.job.service.dto.folder.SharedFolderBriefDTO(jf.id, jf.name, jfst.permission, COUNT(jfr.id)) " +
            "FROM JobFolderSharingTeam jfst " +
            "LEFT JOIN JobFolder jf ON jfst.jobFolderId = jf.id " +
            "LEFT JOIN JobFolderRelation jfr ON jfr.jobFolderId = jf.id " +
            "WHERE jfst.id IN :Ids " +
            "GROUP BY jf.id, jf.name, jfst.permission")
    List<SharedFolderBriefDTO> findBriefByIdIn(@Param("Ids") List<Long> Ids);

    List<JobFolderSharingTeam> findAllByJobFolderIdInAndTeamId(List<Long> notOwnFolderIds, Long teamId);

    @Query("SELECT new com.altomni.apn.common.dto.folder.FolderSharedTeamDTO(jf.id, t.id, t.name, jf.permissionUserId) FROM JobFolder jf " +
            "JOIN JobFolderSharingTeam jfst ON jf.id = jfst.jobFolderId " +
            "JOIN PermissionTeam t ON jfst.teamId = t.id " +
            "WHERE jf.permissionUserId = :userId")
    List<FolderSharedTeamDTO> findJobFolderSharedTeamsByUserId(@Param("userId") Long userId);

    @Query("SELECT DISTINCT new com.altomni.apn.common.dto.folder.FolderSharedTeamDTO(t.id, t.name) " +
            "FROM JobFolder jf " +
            "JOIN JobFolderSharingTeam jfst ON jf.id = jfst.jobFolderId " +
            "JOIN PermissionTeam t ON jfst.teamId = t.id " +
            "WHERE jf.permissionUserId = :userId")
    List<FolderSharedTeamDTO> findDistinctJobFolderSharedTeamsByUserId(@Param("userId") Long userId);


    @Query(value = "SELECT DISTINCT t.id as teamId, t.name as teamName " +
            "FROM job_folder_sharing_team jfst " +
            "JOIN permission_team t on jfst.team_id = t.id " +
            "WHERE EXISTS (SELECT 1 FROM permission_user_team put WHERE put.user_id = :userId AND put.team_id = jfst.team_id) " +
            "AND (jfst.excluded_user_ids IS NULL OR JSON_CONTAINS(jfst.excluded_user_ids, CAST(:userId AS JSON)) <> 1 ) ", nativeQuery = true)
    List<IFolderSharedTeamNameDTO> findDistinctSharedTeamsForSharedFolderByUserId(@Param("userId") Long userId);


//    @Query("SELECT DISTINCT new com.altomni.apn.common.dto.folder.FolderSharedTeamDTO(t.id, t.name) " +
//            "FROM JobFolder jf " +
//            "JOIN JobFolderSharingTeam jfst ON jf.id = jfst.jobFolderId " +
//            "JOIN Team t ON jfst.teamId = t.id " +
//            "WHERE jf.permissionUserId = :userId")
//    List<FolderSharedTeamDTO> findJobFolderSharedMyTeamByUserId(@Param("userId") Long userId);

    @Query("SELECT new com.altomni.apn.common.dto.folder.FolderSharedTeamDTO(jfst.jobFolderId, jfst.teamId, t.name, jfst.permissionUserId, jfst.permission) " +
            "FROM JobFolderSharingTeam jfst " +
            "JOIN PermissionTeam t ON t.id = jfst.teamId " +
            "WHERE jfst.jobFolderId IN :folderIds")
    List<FolderSharedTeamDTO> findJobFolderSharedTeamByJobFolderIdIn(@Param("folderIds") List<Long> folderIds);

    @Query("SELECT st FROM JobFolderSharingTeam st " +
            "INNER JOIN PermissionUserTeam put on st.teamId  = put.teamId " +
            "WHERE st.jobFolderId IN (:folderIds) AND put.userId = :userId")
    List<JobFolderSharingTeam> findByJobFolderIdInAndUserId(@Param("folderIds") List<Long> folderIds, @Param("userId") Long userId);

    @Query(value = "SELECT jfst.job_folder_id as id, f.name as name, jfst.permission as folderPermission " +
            "FROM job_folder_sharing_team jfst " +
            "JOIN job_folder f on jfst.job_folder_id = f.id " +
            "WHERE f.puser_id <> :userId " +
            "AND EXISTS (SELECT 1 FROM permission_user_team put WHERE put.user_id = :userId AND put.team_id = jfst.team_id) " +
            "AND (jfst.excluded_user_ids is null OR json_contains(jfst.excluded_user_ids, cast(:userId as json)) = 0 ) " +
            "ORDER BY f.created_date desc ", nativeQuery = true)
    List<IFolderNamePermissionDTO> findTeamSharedFolderByUserId(@Param("userId") Long userId);


}
