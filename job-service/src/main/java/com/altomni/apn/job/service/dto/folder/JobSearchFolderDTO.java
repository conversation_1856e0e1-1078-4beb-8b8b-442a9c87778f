package com.altomni.apn.job.service.dto.folder;

import com.altomni.apn.job.service.annotation.StringEnumValidator;
import com.altomni.apn.common.dto.search.SearchConditionDTO;
import com.altomni.apn.job.domain.enumeration.SearchCategory;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class JobSearchFolderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    private String name;

    @StringEnumValidator(
            enumClass = SearchCategory.class,
            message = "Invalid SearchCategory value!"
    )
    private String searchCategory;
    //private SearchCategory searchCategory;

    private SearchConditionDTO searchCriteria;
    private Long tenantId;

    @ApiModelProperty(value = "job custom folder id")
    private Long jobFolderId;

    @ApiModelProperty(value = "indicate whether current job folder is active or not")
    private boolean isActive;

    private Instant createdDate;


    @JsonIgnore
    public Boolean isValidSearchFolder(){
        return this.isActive();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public SearchConditionDTO getSearchCriteria() {
        return searchCriteria;
    }

    public void setSearchCriteria(SearchConditionDTO searchCriteria) {
        this.searchCriteria = searchCriteria;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public String getSearchCategory() {
        return searchCategory;
    }

    public void setSearchCategory(String searchCategory) {
        this.searchCategory = searchCategory;
    }

    public Long getJobFolderId() {
        return jobFolderId;
    }

    public void setJobFolderId(Long jobFolderId) {
        this.jobFolderId = jobFolderId;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public Instant getCreatedDate() {
        return createdDate;
    }

    public void setCreatedDate(Instant createdDate) {
        this.createdDate = createdDate;
    }
}
