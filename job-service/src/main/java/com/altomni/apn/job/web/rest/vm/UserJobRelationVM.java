//package com.altomni.apn.job.web.rest.vm;
////Get multiple UserJobRelation from frontend.
//
//import java.util.List;
//
//public class UserJobRelationVM {
//    List<Long> jobIdSet;
//
//    List<Long> userIdSet;
//
//    List<String> permissionSet;
//    public List<Long> getJobId() {
//        return jobIdSet;
//    }
//
//    public void setJobId(List<Long> jobId) {
//        this.jobIdSet = jobId;
//    }
//
//    public List<Long> getUserId() {
//        return userIdSet;
//    }
//
//    public void setUserId(List<Long> userId) {
//        this.userIdSet = userId;
//    }
//
//    public List<String> getPermissionSet() {
//        return permissionSet;
//    }
//
//    public void setPermissionSet(List<String> permissionSet) {
//        this.permissionSet = permissionSet;
//    }
//
//    @Override
//    public String toString() {
//        return "UserJobRelationVm{" +
//            "jobId=" + jobIdSet +
//            ", userId=" + userIdSet +
//            ", permissionSet=" + permissionSet +
//            '}';
//    }
//}
//
