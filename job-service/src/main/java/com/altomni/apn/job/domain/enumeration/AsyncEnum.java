package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The AsyncEnum enumeration.
 * <AUTHOR>
 */
public enum AsyncEnum implements ConvertedEnum<Integer> {
    /**
     * the type of sync, single means sync only one data to es, use for normal process sync to es
     */
    ASYNC_TYPE_SINGLE(0),
    /**
     *the type of sync, esfiller means it will call esfiller to sync data. use for batch or a few or one data to sync
     */
    ASYNC_TYPE_ESFILLER(1),
    /**
     *the type of sync, parser means it will call parser to sync data. use for batch or a few or one data to sync
     */
    ASYNC_TYPE_PARSER(2),
    /**
     *the type of sync, esfiller_bulk means it will call esfiller to sync batch data. only use for syncing whole database
     */
    ASYNC_TYPE_ESFILLER_BATCH(3),

    /**
     *the type of sync, ipgfiiller_job means it will call ipgfiiller to sync data. use for batch or a few or one data to sync
     */
    ASYNC_TYPE_IPGFILLER_JOB(5),

    /**
     *the type of data to sync, talent means current sync data is a talent record, the data_id is talentId
     */
    DATA_TYPE_TALENT(10),
    /**
     *the type of data to sync, job means current sync data is a job record, the data_id is jobId
     */
    DATA_TYPE_JOB(11),
    /**
     *the type of data to sync, start means current sync data is a start record, the data_id is startId
     */
    DATA_TYPE_START(12),
    /**
     *the type of data to sync, application means current sync data is a application record, the data_id is applicationId
     */
    DATA_TYPE_APPLICATION(13),
    /**
     *the type of data(job to job_v2) to sync, job means current sync data is a job record, the data_id is jobId
     */
    DATA_TYPE_TRANSLATE_JOB(14),
    /**
     *the type of data(talent to es) to sync, talent means current sync data is a talent record, the data_id is talentId
     */
    DATA_TYPE_TRANSLATE_TALENT(15);

    private final Integer dbValue;

    AsyncEnum(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    /** static resolving: */
    public static final ReverseEnumResolver<AsyncEnum, Integer> resolver =
        new ReverseEnumResolver<>(AsyncEnum.class, AsyncEnum::toDbValue);

    public static AsyncEnum fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
