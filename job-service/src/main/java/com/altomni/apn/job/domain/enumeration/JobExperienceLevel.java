package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobExperienceLevel enumeration.
 */
public enum JobExperienceLevel implements ConvertedEnum<Integer> {
    None(0),
    One_Year_Or_Less(1),
    Two_To_Five_Years(2),
    Five_To_Ten_Years(3),
    More_Than_Ten_Years(4);

    private final int dbValue;

    JobExperienceLevel(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<JobExperienceLevel, Integer> resolver =
        new ReverseEnumResolver<>(JobExperienceLevel.class, JobExperienceLevel::toDbValue);

    public static JobExperienceLevel fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
