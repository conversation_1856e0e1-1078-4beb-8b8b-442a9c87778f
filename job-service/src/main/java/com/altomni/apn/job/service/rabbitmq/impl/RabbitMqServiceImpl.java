package com.altomni.apn.job.service.rabbitmq.impl;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.EsfillerMQProperties;
import com.altomni.apn.job.service.rabbitmq.RabbitMqService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpAdmin;
import org.springframework.amqp.core.QueueInformation;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class RabbitMqServiceImpl implements RabbitMqService {

    private final Logger log = LoggerFactory.getLogger(RabbitMqServiceImpl.class);

    public static final String QUEUE_MESSAGE_COUNT = "QUEUE_MESSAGE_COUNT";

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Resource(name = "esfillerRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource(name = "esfillerAmqpAdmin")
    private AmqpAdmin esfillerAmqpAdmin;


    @Override
    public void saveJobProfile(String jobProfile, int priority) {
        log.info("[EsFillerJobService: syncJobToMQ @{}] save job profile to rabbitMQ : {}", SecurityUtils.getUserId(), jobProfile);
        try {
            rabbitTemplate.convertAndSend(esfillerMQProperties.getEsfillerMQExchange(), esfillerMQProperties.getToEsFillerRoutingKey(), jobProfile, message -> {
                message.getMessageProperties().setPriority(priority);
                return message;
            });
        }catch (Exception e) {
            log.error("[EsFillerJobService: syncJobToMQ @{}] send job profile to rabbitMQ error: {}", SecurityUtils.getUserId(), e.getMessage());
        }
    }

    @Override
    public void saveProfile(String profile, String type, int priority) {
        log.info("[EsFillerJobService: syncProileToMQ @{}] save  profile to rabbitMQ :  type: {}, jobNote: {}", SecurityUtils.getUserId(), type, profile);
        try {
            rabbitTemplate.convertAndSend(esfillerMQProperties.getEsfillerMQExchange(), esfillerMQProperties.getToEsFillerRoutingKey(), profile, message -> {
                message.getMessageProperties().setPriority(priority);
                return message;
            });
        }catch (Exception e) {
            log.error("[EsFillerJobService: syncJobToMQ @{}] send {} profile to rabbitMQ error: {}", SecurityUtils.getUserId(), type, e.getMessage());
        }
    }

    @Override
    public Integer checkMessageCount(String queue) {
        final QueueInformation queueInfo = esfillerAmqpAdmin.getQueueInfo(queue);
        int messageCount = queueInfo.getMessageCount();
        log.info("check message count: {}", messageCount);
        return messageCount;
    }
}
