package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.JobFolderDetail;
import com.altomni.apn.job.service.dto.folder.JobFolderBriefDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface JobFolderDetailRepository extends JpaRepository<JobFolderDetail, Long> {

    @EntityGraph(type= EntityGraph.EntityGraphType.FETCH, value = "folder-graph", attributePaths = {"jobFolderRelations", "jobFolderSharingUsers", "jobFolderSharingTeams"})
    Optional<JobFolderDetail> findById(Long id);

    @EntityGraph(type= EntityGraph.EntityGraphType.FETCH, value = "folder-graph", attributePaths = {"jobFolderRelations", "jobFolderSharingUsers", "jobFolderSharingTeams"})
    Page<JobFolderDetail> findByPermissionUserId(Long userId, Pageable page);


    @Query("SELECT new com.altomni.apn.job.service.dto.folder.JobFolderBriefDTO(jf.id, jf.name, COUNT(jfr.id)) " +
            "FROM JobFolder jf " +
            "LEFT JOIN JobFolderRelation jfr ON jfr.jobFolderId = jf.id " +
            "WHERE jf.permissionUserId = :userId " +
            "GROUP BY jf.id, jf.name")
    Page<JobFolderBriefDTO> findAllCountByUserId(@Param("userId") Long userId, Pageable page);


}
