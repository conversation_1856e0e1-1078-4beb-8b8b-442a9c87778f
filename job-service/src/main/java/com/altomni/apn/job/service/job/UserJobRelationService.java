//package com.altomni.apn.job.service.job;
//
//import com.altomni.apn.job.domain.job.UserJobRelation;
//import com.altomni.apn.job.service.dto.job.UserJobRelationDTO;
//import com.altomni.apn.job.web.rest.vm.MessageVM;
//import com.altomni.apn.job.web.rest.vm.UserJobRelationVM;
//
//import java.io.IOException;
//import java.util.List;
//
//public interface UserJobRelationService {
//
//    UserJobRelationDTO create(UserJobRelation relation) throws IOException;
//
//    List<UserJobRelationDTO> create(List<UserJobRelation> userJobRelations) throws IOException;
//
//    List<UserJobRelationDTO> upsert(List<UserJobRelation> userJobRelations) throws IOException;
//
//    List<MessageVM<UserJobRelationDTO>> multiUserJobRelations(UserJobRelationVM userJobRelationVM);
//
//    UserJobRelationDTO saveSearchString(String searchString, Long jobId);
//
//    UserJobRelationDTO update(UserJobRelation update) throws IOException;
//
//    UserJobRelationDTO findOne(Long id);
//
//    void delete(UserJobRelation userJobRelation);
//
//    List<UserJobRelationDTO> getRelationByJobId(Long jobId);
//
//    UserJobRelationDTO getRelationByUserAndJob(Long userId, Long jobId);
//
//    UserJobRelation findFirstByPermissionAndJobId(Integer permission, Long jobId);
//}
