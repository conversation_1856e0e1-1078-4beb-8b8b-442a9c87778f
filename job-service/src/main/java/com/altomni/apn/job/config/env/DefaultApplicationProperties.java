package com.altomni.apn.job.config.env;

import com.altomni.apn.job.service.dto.job.DefaultApplicationInfo;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Map;

@Data
@RefreshScope
@Component
@ConfigurationProperties(prefix = "application")
public class DefaultApplicationProperties {

    private Map<String, DefaultApplicationInfo> countryConfig;

}
