package com.altomni.apn.job.domain.job;

import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * business dict table.
 */
@ApiModel(description = "BizDict entity. ")
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "column_dict")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ColumnDict implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "The dictCode for find special dict data")
    @Column(name = "dict_code")
    private Long dictCode;

    @ApiModelProperty(value = "dict's Chinese annotation")
    @Column(name = "ui_name")
    private String uiName;

    @ApiModelProperty(value = "sign which dictCode is belongs to. when 0 then it's a parents dict")
    @Column(name = "pcode")
    private Long pcode;

    @ApiModelProperty(value = "description something")
    @Column(name = "description")
    private String description;

    @ApiModelProperty(value = "use for ordering")
    @Column(name = "sort_order")
    private Integer sortOrder;

    @Column(name = "column_name")
    private String columnName;

    @Column(name = "show_flag")
    private Boolean showFlag;

    @Column(name = "sort_flag")
    private Boolean sortFlag;

}
