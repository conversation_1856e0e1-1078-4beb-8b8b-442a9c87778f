package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.dto.activity.ChangeFieldDTO;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobActivityDTO implements Serializable {

    private Long jobId;
    private List<ChangeFieldDTO> changeField;
    private FullNameUserDTO createdBy;
    private String createdDate;

}
