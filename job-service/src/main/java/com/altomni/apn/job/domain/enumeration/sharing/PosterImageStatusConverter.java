package com.altomni.apn.job.domain.enumeration.sharing;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class PosterImageStatusConverter extends AbstractAttributeConverter<PosterImageStatus, Integer> {
    public PosterImageStatusConverter() {
        super(PosterImageStatus::toDbValue, PosterImageStatus::fromDbValue);
    }
}
