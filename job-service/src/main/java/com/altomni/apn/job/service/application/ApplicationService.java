package com.altomni.apn.job.service.application;

import cn.hutool.json.JSONArray;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessStats;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.List;
import java.util.Map;

@Component
@FeignClient(value = "application-service")
public interface ApplicationService {

    @GetMapping("/application/api/v3/talent-recruitment-processes/jobId/last/{jobId}")
    ResponseEntity<TalentRecruitmentProcessVO> getTalentRecruitmentProcessLastByJobId(@PathVariable("jobId") Long jobId);

    @GetMapping("/application/api/v3/talent-recruitment-processes/jobId/{jobId}/last-interview-datetime")
    ResponseEntity<String> getTalentRecruitmentProcessLastInterviewDateTimeByJobId(@PathVariable("jobId") Long jobId);

    @PostMapping("/application/api/v3/recruitment-processes/clint-board-interview-stats")
    ResponseEntity<List<RecruitmentProcessStats>> getClintBoardInterviewStats(@RequestBody List<Long> jobIdList);

    @GetMapping("/application/api/v3/talent-recruitment-processes-brief/jobId/{jobId}")
    ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessBriefByJobId(@PathVariable("jobId") Long jobId);

    @GetMapping("/application/api/v3/talent-recruitment-processes/kpi-users/jobId/{jobId}")
    ResponseEntity<List<TalentRecruitmentProcessKpiUserVO>> getKPIUsersByJobId(@PathVariable("jobId") Long jobId);

    //TODO: replace with full authorized api
    @GetMapping("/application/api/v3/recruitment-processes/brief/{id}")
    ResponseEntity<RecruitmentProcessVO> getRecruitmentProcessBriefById(@PathVariable("id") Long recruitmentProcessId);

    @PostMapping("/application/api/v3/talent-recruitment-processes/in-process/internal")
    ResponseEntity<Map<Long, Long>> countUnfinishedApplicationsForJobs(@RequestBody List<Long> jobIds);

    @GetMapping("/application/api/v3/talent-recruitment-processes/job-id/{jobId}/in-process")
    ResponseEntity<Integer> countUnfinishedApplicationsForJob(@PathVariable("jobId") Long jobId);

    @GetMapping("/application/api/v3/recruitment-processes/my-recruitment-process-ids")
    ResponseEntity<Map<Long, JobType>> getAllMyRecruitmentProcessIds();

    @GetMapping("/application/api/v3/talent-recruitment-process-node-created-date/latest/{jobId}/{userId}/{nodeType}")
    ResponseEntity<Instant> getTalentRecruitmentProcessNodeLatestByUserIdAndJobId(@PathVariable("jobId") Long jobId, @PathVariable("userId") Long userId, @PathVariable("nodeType") NodeType nodeType);

    @DeleteMapping("/application/api/v3/talent-recruitment-processes/job/{jobId}")
    ResponseEntity<JSONArray> deleteByJobId(@PathVariable(value = "jobId") Long jobId);

}
