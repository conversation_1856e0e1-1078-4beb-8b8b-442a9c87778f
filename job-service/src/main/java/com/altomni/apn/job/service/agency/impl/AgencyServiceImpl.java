package com.altomni.apn.job.service.agency.impl;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.job.service.agency.AgencyClient;
import com.altomni.apn.job.service.agency.AgencyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class AgencyServiceImpl implements AgencyService {

    @Resource
    private AgencyClient agencyClient;

    @Override
    public void updateAgencySharedStatusByJobId(Long jobId) {
        try {
            ResponseEntity<Void> response = agencyClient.updateAgencySharedStatusByJobId(jobId);
            if (response != null && response.getStatusCode() == HttpStatus.OK) {
                log.info("Successfully updated agency shared status by job id {}", jobId);
            } else {
                log.error("Failed to update agency shared status by job id {}", jobId);
            }
        } catch (Exception e) {
            log.error("Failed to update agency shared status by job id {}, with Exception msg: {}", jobId, e.getMessage(), e);
        }
    }

}
