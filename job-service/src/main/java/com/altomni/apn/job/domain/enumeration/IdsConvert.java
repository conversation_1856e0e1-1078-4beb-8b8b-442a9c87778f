package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;
import com.altomni.apn.common.utils.CommonUtils;

import javax.persistence.Converter;
import java.util.List;

@Converter
public class IdsConvert extends AbstractAttributeConverter<List<Long>, String> {
    public IdsConvert() {
        super(CommonUtils::convertListToString, CommonUtils::convertStringToList);
    }
}
