package com.altomni.apn.job.repository.job;


import com.altomni.apn.job.domain.job.JobSearchFolder;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;

import java.util.List;
import java.util.Optional;

@Repository
public interface JobSearchFolderRepository extends JpaRepository<JobSearchFolder, Long> {

    Page<JobSearchFolder> findAllByPermissionUserId(Long userId, Pageable pageable);

    Optional<JobSearchFolder> findByNameAndPermissionUserId(String name, Long userId);

    Optional<JobSearchFolder> findByNameAndPermissionUserIdAndIdNot(String name, Long userId, Long id);

    List<JobSearchFolder> findAllByJobFolderId(Long jobFolderId);


    List<JobSearchFolder> findAllByJobFolderIdAndPermissionUserIdInAndIsActive(Long jobFolderId, List<Long> userIds, Boolean isActive);

    @Query("SELECT jsf FROM JobSearchFolder jsf " +
            "JOIN PermissionUserTeam put ON jsf.permissionUserId = put.userId " +
            "WHERE put.teamId IN :teamIds AND jsf.jobFolderId = :jobFolderId AND jsf.isActive = :isActive")
    List<JobSearchFolder> findAllByJobCustomFolderIdAndTeamIds(@Param("jobFolderId") Long jobFolderId, @Param("teamIds") List<Long> teamIds, @Param("isActive") Boolean isActive);
}
