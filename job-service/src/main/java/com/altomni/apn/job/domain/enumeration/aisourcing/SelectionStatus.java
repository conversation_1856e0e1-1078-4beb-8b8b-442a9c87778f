package com.altomni.apn.job.domain.enumeration.aisourcing;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum SelectionStatus implements ConvertedEnum<Integer> {

    ACCEPT(1),
    REJECT(2),

    SAVE_FOR_LATER(3);

    private final Integer dbValue;

    SelectionStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    /** static resolving: */
    public static final ReverseEnumResolver<SelectionStatus, Integer> resolver =
        new ReverseEnumResolver<>(SelectionStatus.class, SelectionStatus::toDbValue);

    public static SelectionStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
