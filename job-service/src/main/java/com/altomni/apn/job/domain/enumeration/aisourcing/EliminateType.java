package com.altomni.apn.job.domain.enumeration.aisourcing;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum EliminateType implements ConvertedEnum<Integer> {

    BY_RECRUITER(1),

    BY_CANDIDATE(2);

    private final Integer dbValue;

    EliminateType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    /** static resolving: */
    public static final ReverseEnumResolver<EliminateType, Integer> resolver =
        new ReverseEnumResolver<>(EliminateType.class, EliminateType::toDbValue);

    public static EliminateType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
