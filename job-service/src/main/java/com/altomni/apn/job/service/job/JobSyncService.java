package com.altomni.apn.job.service.job;

import com.altomni.apn.job.web.rest.vm.MqMessageCountVM;

import java.util.Collection;

public interface JobSyncService {

    MqMessageCountVM checkJobMqMessageCount();

    void syncJobsToMQ(Collection<Long> jobIds, int priority);

    void bulkSyncJobsToMQ(Collection<Long> jobIds, int priority);

    void syncJobsToHrMQ(Collection<Long> jobIds, int priority);

    void syncJobsToAgencyMQ(Collection<Long> jobIds, int priority);
}
