package com.altomni.apn.job.repository.project;

import com.altomni.apn.job.domain.project.JobProject;
import com.altomni.apn.job.domain.project.JobProjectSharing;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Set;


@Repository
public interface JobProjectSharingRepository extends JpaRepository<JobProjectSharing, Long> {

    List<JobProjectSharing> findAllByJobProjectIdAndUserIdIn(Long jobProjectId, Collection<Long> userId);

    void deleteAllByJobProjectIdAndUserIdIn(Long jobProjectId, Collection<Long> userIds);

    boolean existsByJobProjectIdAndPermissionUserId(Long jobProjectId, Long userId);
}
