package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Set;


@Entity
@Table(name = "job_folder")
@Data
@NamedEntityGraph(
        name = "job_folder-graph",
        attributeNodes = {
                @NamedAttributeNode(value = "jobFolderRelations"),
                @NamedAttributeNode(value = "jobFolderSharingUsers"),
                @NamedAttributeNode(value = "jobFolderSharingTeams"),
                //@NamedAttributeNode(value = "jobFolderSharings"),
        },

        includeAllAttributes = true
)
public class JobFolderDetail extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "tenant_id")
    private Long tenantId;

    @OneToMany
    @JoinColumn(name = "job_folder_id")
    private Set<JobFolderRelation> jobFolderRelations;

    @OneToMany
    @JoinColumn(name = "job_folder_id")
    private Set<JobFolderSharingUser> jobFolderSharingUsers;

    @OneToMany
    @JoinColumn(name = "job_folder_id")
    private Set<JobFolderSharingTeam> jobFolderSharingTeams;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Set<JobFolderRelation> getJobFolderRelations() {
        return jobFolderRelations;
    }

    public void setJobFolderRelations(Set<JobFolderRelation> jobFolderRelations) {
        this.jobFolderRelations = jobFolderRelations;
    }

    public Set<JobFolderSharingUser> getJobFolderSharingUsers() {
        return jobFolderSharingUsers;
    }

    public void setJobFolderSharingUsers(Set<JobFolderSharingUser> jobFolderSharingUsers) {
        this.jobFolderSharingUsers = jobFolderSharingUsers;
    }

    public Set<JobFolderSharingTeam> getJobFolderSharingTeams() {
        return jobFolderSharingTeams;
    }

    public void setJobFolderSharingTeams(Set<JobFolderSharingTeam> jobFolderSharingTeams) {
        this.jobFolderSharingTeams = jobFolderSharingTeams;
    }
}
