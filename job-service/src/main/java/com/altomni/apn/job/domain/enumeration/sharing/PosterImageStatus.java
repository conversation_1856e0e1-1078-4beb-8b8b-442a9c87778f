package com.altomni.apn.job.domain.enumeration.sharing;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The PosterImageStatus enumeration.
 * <AUTHOR>
 */
public enum PosterImageStatus implements ConvertedEnum<Integer> {
    /**
     * the type of sync, single means sync only one data to es, use for normal process sync to es
     */
    INACTIVE(10),
    /**
     *the type of sync, esfiller means it will call esfiller to sync data. use for batch or a few or one data to sync
     */
    ACTIVE(20);

    private final Integer dbValue;

    PosterImageStatus(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    /** static resolving: */
    public static final ReverseEnumResolver<PosterImageStatus, Integer> resolver =
        new ReverseEnumResolver<>(PosterImageStatus.class, PosterImageStatus::toDbValue);

    public static PosterImageStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
