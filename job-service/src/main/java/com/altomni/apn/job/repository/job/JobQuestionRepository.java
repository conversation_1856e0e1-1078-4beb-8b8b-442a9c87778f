package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.JobQuestion;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the JobQuestion entity.
 */
@Repository
public interface JobQuestionRepository extends JpaRepository<JobQuestion,Long> {
    List<JobQuestion> findAllByJobId(Long jobId);
}
