package com.altomni.apn.job.repository.agency;

import com.altomni.apn.job.domain.agency.JobSharingToAgency;
import com.altomni.apn.job.service.dto.agency.JobSharingToAgencyWithStatusDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;


/**
 * Spring Data repository for the JobSharingToAgencyInfo entity.
 */
@SuppressWarnings("unused")
@Repository
public interface JobSharingToAgencyRepository extends JpaRepository<JobSharingToAgency, Long> {

    Optional<JobSharingToAgency> findByJobId(Long jobId);

    @Query(value = "SELECT NEW com.altomni.apn.job.service.dto.agency.JobSharingToAgencyWithStatusDTO(js.id, j.tenantId, js.jobId, j.recruitmentProcessId, js.sharingToAgencyInfo, j.status, js.companyNameVisible, c.id, c.fullBusinessName, j.createdDate, j.permissionUserId, j.permissionTeamId) FROM JobSharingToAgency js LEFT JOIN JobV3 j ON js.jobId = j.id LEFT JOIN Company c ON j.companyId = c.id WHERE js.jobId IN ?1")
    List<JobSharingToAgencyWithStatusDTO> findAllWithStatus(Collection<Long> jobIds);

}
