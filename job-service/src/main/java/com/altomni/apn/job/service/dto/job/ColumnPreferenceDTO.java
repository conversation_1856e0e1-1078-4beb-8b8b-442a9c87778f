package com.altomni.apn.job.service.dto.job;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.job.domain.job.ColumnPreference;
import com.altomni.apn.job.service.dto.talent.CreationTypeDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ColumnPreferenceDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "data id")
    private Long id;

    @ApiModelProperty(value = "temp name")
    private String tempName;

    @ApiModelProperty(value = "creation type")
    private CreationTypeDTO creationType;

    @ApiModelProperty(value = "sort for front-end")
    private String itemSort;

    @ApiModelProperty(value = "sort all for front-end")
    private String itemSortAll;

    @ApiModelProperty(value = "Which module do user Preferences belong to")
    private ModuleType module;

    @ApiModelProperty(value = "Record the number of pages saved by the user")
    private Integer pageSize;


    public static ColumnPreferenceDTO fromColumnPreference(ColumnPreference result) {
        ColumnPreferenceDTO dto = new ColumnPreferenceDTO();
        ServiceUtils.myCopyProperties(result, dto);
        dto.setCreationType(JsonUtil.fromJson(JSONUtil.toJsonStr(result.getCreationType()), CreationTypeDTO.class));
        return dto;
    }
}
