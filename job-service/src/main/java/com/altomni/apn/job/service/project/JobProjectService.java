package com.altomni.apn.job.service.project;

import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.job.service.dto.project.JobProjectDTO;
import liquibase.exception.CustomChangeException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Set;


/**
 * <AUTHOR>
 */
public interface JobProjectService {

    JobProjectDTO save(JobProjectDTO jobProjectDTO);

    JobProjectDTO update(JobProjectDTO jobProjectDTO) throws CustomParameterizedException;

    Page<JobProjectDTO> getByPage(Pageable pageable);

    void shareProjectTo(Long projectId, Set<Long> userIds);

    void appendJobsToProject(Long projectId, Set<Long> jobIds);

    void removeJobsFromProject(Long projectId, Set<Long> jobIds);
}
