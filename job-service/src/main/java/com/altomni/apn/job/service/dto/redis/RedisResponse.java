package com.altomni.apn.job.service.dto.redis;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import redis.clients.jedis.Tuple;

import java.io.Serializable;
import java.util.Set;

@Data
public class RedisResponse implements Serializable {

    private static final long serialVersionUID = 7547052176540537819L;

    private String status;

    @ApiModelProperty(value = "Redis data")
    private Set<Tuple> data;

    private Integer total = 0;

    public String getStatus() {
        return status;
    }

    public RedisResponse status(String status) {
        this.status = status;
        return this;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Set<Tuple> getData() {
        return data;
    }

    public RedisResponse data(Set<Tuple> data) {
        this.data = data;
        return this;
    }

    public void setData(Set<Tuple> data) {
        this.data = data;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return "RedisResponse{" +
                "status='" + status + '\'' +
                ", data=" + data +
                ", total=" + total +
                '}';
    }
}
