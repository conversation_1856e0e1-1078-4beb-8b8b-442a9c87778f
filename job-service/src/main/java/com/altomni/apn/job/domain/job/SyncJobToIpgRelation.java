package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobStatusConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A apn job and ipg job relation.
 */
@Entity
@Data
@Table(name = "job_ipg_relation")
public class SyncJobToIpgRelation extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "apn_job_id", nullable = false)
    private Long apnJobId;

    @Column(name = "ipg_job_id", nullable = false)
    private Long ipgJobId;

    @Convert(converter = JobStatusConverter.class)
    @Column(name = "apn_job_status", nullable = false)
    private JobStatus apnJobStatus = JobStatus.OPEN;

    @Convert(converter = JobStatusConverter.class)
    @Column(name = "ipg_job_status", nullable = false)
    private JobStatus ipgJobStatus = JobStatus.OPEN;

    /**
     * jobType整改
     * 用于记录APN 同步到IPG官网的职位类型，由于IPG没有流程id的概念，沿用职位类型
     */
    @Column(name = "ipg_job_type", nullable = false)
    private JobType ipgJobType;

    @Column(name = "ipg_job_description", nullable = false)
    private String ipgJobDescription;

    @ApiModelProperty(value = "ipg job responsibilities")
    @Column(name = "ipg_job_requirements", nullable = false)
    private String ipgJobRequirements;

    @ApiModelProperty(value = "job summary")
    @Column(name = "ipg_job_summary")
    private String ipgJobSummary;

    @ApiModelProperty(value = "ipg overall description")
    @Column(name = "ipg_job_responsibilities", nullable = false)
    private String ipgJobResponsibilities;

    @ApiModelProperty(value = "desired skills")
    @Column(name = "ipg_job_required_skills")
    private String ipgJobRequiredSkills;

//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//    public Long getApnJobId() {
//        return apnJobId;
//    }
//
//    public void setApnJobId(Long apnJobId) {
//        this.apnJobId = apnJobId;
//    }
//
//    public Long getIpgJobId() {
//        return ipgJobId;
//    }
//
//    public void setIpgJobId(Long ipgJobId) {
//        this.ipgJobId = ipgJobId;
//    }
//
//    public JobStatus getApnJobStatus() {
//        return apnJobStatus;
//    }
//
//    public void setApnJobStatus(JobStatus apnJobStatus) {
//        this.apnJobStatus = apnJobStatus;
//    }
//
//    public JobStatus getIpgJobStatus() {
//        return ipgJobStatus;
//    }
//
//    public void setIpgJobStatus(JobStatus ipgJobStatus) {
//        this.ipgJobStatus = ipgJobStatus;
//    }
//
//    public JobType getIpgJobType() {
//        return ipgJobType;
//    }
//
//    public void setIpgJobType(JobType ipgJobType) {
//        this.ipgJobType = ipgJobType;
//    }
//
//    public String getIpgJobDescription() {
//        return ipgJobDescription;
//    }
//
//    public void setIpgJobDescription(String ipgJobDescription) {
//        this.ipgJobDescription = ipgJobDescription;
//    }


}
