package com.altomni.apn.job.service.job;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.job.service.dto.job.JobToIpgDTOV3;

import java.io.IOException;

public interface IpgService {

    HttpResponse syncJobToIpg(long id, JobToIpgDTOV3 job) throws IOException;

    HttpResponse updateSyncJobToIpg(long id, Long ipgJobId, JobToIpgDTOV3 job) throws IOException;

    HttpResponse deleteSyncJobToIpg(long id, Long ipgJobId, JobStatus status) throws IOException;

    HttpResponse querySyncJobToIpg(long id, Long ipgJobId) throws IOException;

    HttpResponse syncJobToIpgLogin() throws IOException;

}
