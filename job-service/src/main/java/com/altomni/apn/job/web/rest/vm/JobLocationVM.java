package com.altomni.apn.job.web.rest.vm;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
public class JobLocationVM {
    private Long jobId;
    private Location location;

    public JobLocationVM(Long jobId, String country, String province, String city) {
        this.jobId = jobId;
        this.location = new Location(country, province, city);
    }

    @Data
    @AllArgsConstructor
    public static class Location{
        private String country;
        private String province;
        private String city;
    }
}
