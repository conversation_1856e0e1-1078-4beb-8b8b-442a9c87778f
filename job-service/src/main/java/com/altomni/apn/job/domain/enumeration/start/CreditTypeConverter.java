package com.altomni.apn.job.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

/**
 * <AUTHOR>
 */
@Converter
public class CreditTypeConverter extends AbstractAttributeConverter<CreditType, Integer> {
    public CreditTypeConverter() {
        super(CreditType::toDbValue, CreditType::fromDbValue);
    }
}
