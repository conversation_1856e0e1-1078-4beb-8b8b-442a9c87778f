package com.altomni.apn.job.service.jobdiva;

import cn.hutool.json.JSONArray;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Component
@FeignClient(value = "jobdiva-service")
public interface JobdivaService {

    @DeleteMapping("/jobdiva/api/v3/assignment/job/{jobId}")
    ResponseEntity<JSONArray> deleteAssignmentByJobId(@PathVariable("jobId") Long jobId);

}
