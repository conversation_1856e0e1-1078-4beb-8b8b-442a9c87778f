package com.altomni.apn.job.repository.job;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.job.service.dto.job.IDormantJobDTO;
import com.altomni.apn.job.service.dto.job.IStatusCountDTO;
import com.altomni.apn.job.service.dto.job.SearchCategoryCountDTO;
import com.altomni.apn.job.web.rest.vm.JobCompanyIdAndSalesLeadIdVM;
import com.altomni.apn.job.web.rest.vm.JobLocationVM;
import com.altomni.apn.job.web.rest.vm.JobTitleVM;
import com.altomni.apn.job.web.rest.vm.RelateJobFolderJobInfoVM;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.repository.query.Param;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;


/**
 * Spring Data JPA repository for the Job entity.
 */
@EnableCaching
@EnableAsync
@Repository
public interface JobRepository extends JpaRepository<JobV3, Long>, QuerydslPredicateExecutor<JobV3>, JobRepositoryCustom {

    @Override
    @NotNull
    @EntityGraph(attributePaths = {"requiredLanguages", "preferredLanguages", "jobFunctions", "preferredDegrees", "jobAdditionalInfo"})
    @Query(value = "SELECT t FROM JobV3 t WHERE t.id = :id")
    Optional<JobV3> findById(@NotNull @Param("id") Long id);


    @NotNull
    @EntityGraph(attributePaths = {"requiredLanguages", "preferredLanguages", "jobFunctions", "preferredDegrees", "jobAdditionalInfo"})
    @Query(value = "SELECT t FROM JobV3 t WHERE t.id = :id and t.permissionTeamId not in (select id from JobProject where tenantId=:tenantId)")
    Optional<JobV3> findRegularJobById(@NotNull @Param("id") Long id, @Param("tenantId") Long tenantId);

    @NotNull
    @EntityGraph(attributePaths = {"requiredLanguages", "preferredLanguages", "jobFunctions", "preferredDegrees", "jobAdditionalInfo"})
    @Query(value = "SELECT t FROM JobV3 t WHERE t.id in :idList and t.permissionTeamId not in (select id from JobProject where tenantId=:tenantId)")
    List<JobV3> findRegularJobByIds(@NotNull @Param("idList") List<Long> idList, @Param("tenantId") Long tenantId);


    @NotNull
    @EntityGraph(attributePaths = {"requiredLanguages", "preferredLanguages", "jobFunctions", "preferredDegrees", "jobAdditionalInfo"})
    @Query(value = "SELECT t FROM JobV3 t " +
            "left join UserJobRelation uj on uj.jobId=t.id and uj.status = true " +
            "WHERE t.id = :id and (uj.userId =:userId or t.permissionUserId =:userId)")
    Optional<JobV3> findPrivateJobById(@NotNull @Param("id") Long id, @Param("userId") Long userId);

    @Query(value = "select DISTINCT t.id,t.tenant_id as tenantId,t.created_by as createdBy," +
            " t.company_id as companyId,jp.id as privateJob from job t " +
            " left join job_project jp on jp.id = t.pteam_id " +
            " where t.id in :idList ", nativeQuery = true)
    List<Map<String,Object>> findJobByIds(@NotNull @Param("idList") List<Long> idList);

    @Query(value = "SELECT t FROM JobV3 t " +
            "left join UserJobRelation uj on uj.jobId=t.id and uj.status = true " +
            "WHERE t.id = :id and (uj.userId =:userId or t.permissionUserId =:userId)")
    Optional<JobV3> findPrivateJobByIdForUpdate(@NotNull @Param("id") Long id, @Param("userId") Long userId);

    @Query(value = "select DISTINCT l.official_country from job_location l " +
        "left join job j on j.id = l.job_id " +
        "where j.tenant_id = ?1 and l.official_country is not null ", nativeQuery = true)
    Set<String> findAllCountriesByTenantId(Long tenantId);

    @Query(value = " select id from job where tenant_id = ?1", nativeQuery = true)
    Set<String> findAllJobIdsByTenantId(Long tenantId);

    @Query(value = " select title from job where id = ?1", nativeQuery = true)
    String findJobTitleById(Long jobId);

    @Query(value = "select DISTINCT c.id, c.full_business_name from job j " +
        "left join company c on c.id = j.company_id  " +
        "where j.tenant_id = ?1 ", nativeQuery = true)
    List<Object[]> findAllCompaniesByTenantId(Long tenantId);


    @Query(value = "SELECT j FROM JobV3 j INNER JOIN AsyncRecord r ON j.id = r.dataId AND r.asyncType = com.altomni.apn.job.domain.enumeration.AsyncEnum.ASYNC_TYPE_SINGLE AND r.dataType = com.altomni.apn.job.domain.enumeration.AsyncEnum.DATA_TYPE_JOB AND r.status = com.altomni.apn.common.domain.enumeration.user.Status.Failed")
    List<JobV3> findSyncFailedJobs();

    @Query(value = "SELECT " +
            "j.id AS 'id', " +
            "j.title AS 'jobTitle', " +
            "rp.job_type AS 'jobType', " +
            "j.status AS 'jobStatus', " +
            "j.company_id AS 'companyId', " +
            "c.full_business_name AS 'company', " +
            "j.posting_time as 'postingTime', " +
            "u.id AS 'amId', " +
            "concat(u.first_name, ' ', u.last_name) AS 'am', " +
            "MAX(trp.last_modified_date) AS 'lastModifiedDate' " +
            "FROM job j " +
            " left join recruitment_process rp on rp.id = j.recruitment_process_id " +
            "LEFT JOIN talent_recruitment_process trp " +
            "ON trp.job_id = j.id " +
            "LEFT JOIN talent_recruitment_process_kpi_user trpku " +
            "ON trpku.talent_recruitment_process_id = trp.id " +
            "LEFT JOIN user u " +
            "ON trpku.user_id = u.id " +
            "LEFT JOIN company c " +
            "ON c.id = j.company_id " +
            "WHERE " +
            "j.id NOT IN (141, 211, 2461, 3274, 3609, 3323, 4758, 95) " +
            "AND " +
            "j.status <= 2 " + // job in status: Open(0), OnHold(2)
            "AND " +
            "j.tenant_id =:tenantId " +
            "AND " +
            "u.id =:amId " +
            "AND " +
            "trpku.user_role = 0 " +
            "AND " +
            "TIMESTAMPDIFF(DAY, j.last_modified_date, SYSDATE()) > 90 " +
            "AND "+
            "(TIMESTAMPDIFF(DAY, j.last_edited_time, SYSDATE()) > 90 OR j.last_edited_time IS NULL) group by j.id " +
            "HAVING (TIMESTAMPDIFF(DAY, lastModifiedDate, SYSDATE()) > 90 OR lastModifiedDate IS NULL)", nativeQuery = true)
    List<IDormantJobDTO> findAllDormantJobsByAmId(@Param("tenantId") Long tenantId, @Param("amId") Long amId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE JobV3 j SET j.lastEditedTime = current_timestamp WHERE j.id = ?1")
    void updateJobLastEditedTime(Long jobId);

    @Query(value = "select j.* from job j left join hot_list_talent hlt on hlt.job_id = j.id where hlt.hot_list_id = ?1", nativeQuery = true)
    List<JobV3> findAllByHotListId(Long hotListId);

    @Query(value = "SELECT j.id FROM job j WHERE j.sync_paused = FALSE AND (j.last_sync_time IS NULL OR j.last_edited_time > j.last_sync_time) order by j.last_edited_time desc limit ?1, ?2 ", nativeQuery = true)
    List<Long> findOutOfSyncJobIds(Integer lineNum, Integer pageNum);

    @Query(value = "SELECT count(j.id) FROM JobV3 j WHERE j.syncPaused = FALSE AND (j.lastSyncTime IS NULL OR j.lastEditedTime > j.lastSyncTime)")
    Long countOutOfSyncJobIds();

    @Modifying
    @Transactional
    @Query(value = "UPDATE job j SET j.last_sync_time =:lastSyncTime WHERE j.id =:jobId", nativeQuery = true)
    void updateJobLastSyncTime(@Param("jobId") Long jobId, @Param("lastSyncTime") Instant lastSyncTime);

    @Modifying
    @Transactional
    @Query(value = "UPDATE JobV3 j SET j.syncPaused = TRUE WHERE j.id = ?1")
    void setJobSyncToPaused(Long jobId);

    //TODO put responsibilityText, summaryText requirementText into extended_info at first time
    @Query(value = "select extended_info from job_additional_info where id = ?1 ", nativeQuery = true)
    String getExtendedInfoById(Long additionalInfoId);

    @Modifying
    @Transactional
    @Query(value = "update job_additional_info set extended_info = ?1 where id = ?2", nativeQuery = true)
    void updateExtendedInfoById(String extendedInfo, Long additionalInfoId);

    JobV3 findFirstByCode(String jobCode);

    @Modifying
    @Transactional
    @Query(value = "UPDATE job j SET j.status = ?2 WHERE j.id = ?1", nativeQuery = true)
    int updateJobStatus(Long jobId, Integer status);

    @Modifying
    @Transactional
    @Query(value = "UPDATE job j SET j.status = :status WHERE j.id IN (:jobIds)", nativeQuery = true)
    int updateJobsStatus(@Param("jobIds") List<Long> jobIds, @Param("status") Integer status);

    @Modifying
    @Transactional
    @Query(value = "UPDATE job j SET j.status = j.status WHERE j.id = ?1", nativeQuery = true)
    int checkJobWritePermission(Long jobId);

    List<JobV3> findAllByCompanyId(Long companyId);

    List<JobV3> findAllBySalesLeadIdIn(List<Long> businessIdList);

    @Query(value = "select t.id from JobV3 t")
    List<Long> findAllIds();

    Integer countByTenantId(Long tenantId);

    @Query(value = " select count(1) from job j where j.pteam_id = ?1", nativeQuery = true)
    Integer countByTeamId(Long pTeamId);

    @Modifying
    @Query(value="SELECT new com.altomni.apn.job.service.dto.job.SearchCategoryCountDTO(j.status, COUNT(j.id)) FROM JobV3 j GROUP BY j.status")
    List<SearchCategoryCountDTO> getJobStatusCounts();

    @Query("SELECT new com.altomni.apn.job.service.dto.job.SearchCategoryCountDTO(j.status, COUNT(j.id)) " +
            "FROM UserJobRelation ujr " +
            "LEFT JOIN JobV3 j ON ujr.jobId = j.id " +
            "WHERE ujr.userId = :userId AND ujr.status = true AND j.permissionUserId != :userId " +
            "GROUP BY j.status")
    List<SearchCategoryCountDTO> getJobStatusCountsByAssignedUsers(@Param("userId") Long userId);

    //application domain cannot be used here due to no dependency
    @Query(value = "SELECT j.status as status, COUNT(j.id) as count " +
            "FROM talent_recruitment_process_kpi_user trpku " +
            "JOIN talent_recruitment_process trp ON trpku.talent_recruitment_process_id = trp.id " +
            "JOIN job j ON trp.job_id = j.id " +
            "WHERE trpku.user_id = :userId AND trpku.user_id <> trpku.puser_id " +
            "GROUP BY j.status", nativeQuery = true)
    List<IStatusCountDTO> getJobStatusCountsByApplicationParticipants(@Param("userId") Long userId);

    Long countJobV3ByPermissionUserId(Long puserid);

    Long countJobV3ByTenantId(Long tenantId);

    List<JobV3> findAllByIdInAndTenantId(List<Long> ids, Long TenantId);

    List<JobV3> findAllByIdIn(List<Long> ids);

    @Query(value = "select new com.altomni.apn.job.web.rest.vm.JobLocationVM(j.id, loc.officialCountry, loc.officialProvince, loc.officialCity)  from JobV3 j" +
            " left join JobLocation loc on loc.jobId=j.id" +
            " where j.id in :jobIds")
    List<JobLocationVM> findJobLocationsByJobIds(@Param("jobIds") List<Long> jobIds);

    @Query("select new com.altomni.apn.job.web.rest.vm.JobTitleVM(j.id, j.title) from JobV3 j " +
            "where j.id in :jobIds")
    List<JobTitleVM> findJobTitlesByIds(@Param("jobIds") List<Long> jobIds);

    @Query("select new com.altomni.apn.job.web.rest.vm.JobCompanyIdAndSalesLeadIdVM(j.companyId, j.salesLeadId) from JobV3 j " +
            "where j.id =:jobId and j.tenantId =:tenantId")
    JobCompanyIdAndSalesLeadIdVM findCompanyIdByJobIdAndTenantId(@Param("jobId") Long jobId, @Param("tenantId") Long tenantId);

    @Query(value = "SELECT j.id AS jobId, c.full_business_name AS companyName, j.title AS title, j.status AS status, p.name AS priority FROM job j LEFT JOIN company c ON j.company_id = c.id LEFT JOIN enum_job_priority p ON j.enum_priority_id =  p.id WHERE j.id = :jobId", nativeQuery = true)
    RelateJobFolderJobInfoVM findTalentRelateJobFolders(@Param("jobId") Long jobId);

    @Query(value = "select rp.job_type from job j inner join recruitment_process rp on rp.id = j.recruitment_process_id where j.id = ?1", nativeQuery = true)
    Integer findJobTypeByJobId(Long jobId);

    @Modifying
    @Query(value = "UPDATE job j SET j.pteam_id =:pteamId WHERE j.id =:jobId", nativeQuery = true)
    void updatePteamId(@Param("jobId") Long jobId, @Param("pteamId") Long pteamId);

    @Query(value = "SELECT j.id FROM job j " +
            "INNER JOIN job_project jp ON jp.id = j.pteam_id " +
            "WHERE j.id =:jobId", nativeQuery = true)
    Set<Long> findPrivateJobId(@Param("jobId") Long jobId);

    @Query(value = "SELECT j.id FROM job j " +
            "INNER JOIN job_project jp ON jp.id = j.pteam_id " +
            "LEFT JOIN user_job_relation uj ON j.id = uj.job_id AND uj.user_id =:userId and uj.status = 1 " +
            "WHERE jp.tenant_id =:tenantId AND j.puser_id !=:userId AND uj.job_id IS NULL", nativeQuery = true)
    Set<Long> findAllUnauthorizedPrivateJobIds(@Param("tenantId") Long tenantId, @Param("userId") Long userId);

    @Query(value = "select j.id from job j " +
            "inner join job_project jp on jp.id=j.pteam_id " +
            "where j.id in :jobIds", nativeQuery = true)
    Set<Long> findPrivateJobIds(@Param("jobIds") List<Long> jobIds);

    @Query(value = "select j.id from job_project j " +
            "where j.tenant_id =:tenantId", nativeQuery = true)
    Set<Long> findPrivateJobTeamIds(@Param("tenantId") Long tenantId);


//    @Query(value = "SELECT MAX(last_modified_date) AS latest_last_modified_date FROM" +
//            "( SELECT last_modified_date FROM job WHERE id = ?1" +
//            //"    UNION ALL SELECT tai.last_modified_date FROM talent_additional_info tai LEFT JOIN talent t on tai.id = t.additional_info_id WHERE t.id = ?1 " +
//            "    UNION ALL SELECT last_modified_date FROM job_company_contact_relation WHERE job_id = ?1        " +
//            "    UNION ALL SELECT last_modified_date FROM job_note WHERE job_id = ?1    " +
//            "    UNION ALL SELECT last_modified_date FROM job_participant WHERE job_id = ?1  " +
//            //"    UNION ALL SELECT last_modified_date FROM talent_recruitment_process WHERE talent_id = ?1 " +
//            ") AS dates;", nativeQuery = true)
//    Instant getJobEsLastModifiedTime(Long jobId);

    @Query(value = "SELECT MAX(last_modified_date) AS latest_last_modified_date FROM" +
            "( SELECT last_modified_date FROM job WHERE id = ?1" +
//            "    UNION ALL SELECT jai.last_modified_date FROM job_additional_info jai LEFT JOIN job j on jai.id = j.additional_info_id WHERE j.id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM job_company_contact_relation WHERE job_id = ?1        " +
            "    UNION ALL SELECT last_modified_date FROM job_note WHERE job_id = ?1    " +
            "    UNION ALL SELECT last_modified_date FROM job_participant WHERE job_id = ?1  " +
            "    UNION ALL SELECT last_modified_date FROM job_ipg_relation WHERE apn_job_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM user_job_relation WHERE job_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM talent_recruitment_process WHERE job_id = ?1 " +
//            "    UNION ALL SELECT last_modified_date FROM job_job_function_relation WHERE job_id = ?1 " +      // no last_modified_date here
            "    UNION ALL SELECT last_modified_date FROM job_company_contact_relation WHERE job_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM job_note WHERE job_id = ?1 " +
            "    UNION ALL SELECT last_modified_date FROM agency_shared_job WHERE job_id = ?1 " +
            ") AS dates;", nativeQuery = true)
    Instant getJobEsLastModifiedTime(Long jobId);

    @Query(value = "SELECT j FROM JobV3 j " +
            "JOIN TalentAssociationJobFolder tajf ON tajf.jobId = j.id " +
            "WHERE tajf.folderId IN :folderIds")
    List<JobV3> findJobByTalentRelatedJobFolderFolderIdIn(@Param("folderIds") Set<String> folderIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE job j SET j.is_need_sync_hr = 1 WHERE j.id = ?1", nativeQuery = true)
    void updateJobNeedSyncToHr(Long jobId);

    @Query(value = "select new com.altomni.apn.common.dto.job.JobBriefDTO(j.id, j.title, j.status) from JobV3 j where j.id in :jobIds")
    List<JobBriefDTO> findBriefJobsByIdIn(@Param("jobIds") List<Long> jobIds);
}
