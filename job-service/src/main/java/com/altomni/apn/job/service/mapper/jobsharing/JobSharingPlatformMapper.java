package com.altomni.apn.job.service.mapper.jobsharing;

import com.altomni.apn.common.domain.job.JobSharingPlatform;
import com.altomni.apn.job.domain.job.JobSearchFolder;
import com.altomni.apn.job.service.dto.folder.JobSearchFolderDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingContentDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPlatformDTO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPlatformVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = {})
public interface JobSharingPlatformMapper {

    JobSharingPlatform toEntity(JobSharingPlatformDTO dto);

    JobSharingPlatformDTO toDto(JobSharingPlatform entity);

    @Mapping(source = "entity.jobId", target = "jobId")
    //@Mapping(source = "entity.userId", target = "userId")
    @Mapping(source = "entity.displayLanguageId", target = "displayLanguageId")
    @Mapping(source = "entity.platformType", target = "platformType")
    @Mapping(source = "contentDTO.jobTitle", target = "jobTitle")
    @Mapping(source = "contentDTO.jobType", target = "jobType")
    @Mapping(source = "contentDTO.payType", target = "payType")
    @Mapping(source = "contentDTO.salaryRange", target = "salaryRange")
    @Mapping(source = "contentDTO.currency", target = "currency")
    @Mapping(source = "contentDTO.location", target = "location")
    @Mapping(source = "contentDTO.jobResponsibilities", target = "jobResponsibilities")
    @Mapping(source = "contentDTO.jobRequirements", target = "jobRequirements")
    @Mapping(source = "contentDTO.contactInfo", target = "contactInfo")
    @Mapping(source = "entity.uuid", target = "uuid")
    JobSharingPlatformDTO toCombine(JobSharingPlatform entity, JobSharingContentDTO contentDTO);

    JobSharingPlatformVO convertDTOToVO(JobSharingPlatformDTO dto);

    default JobSharingPlatform fromId(Long id) {
        if (id == null) {
            return null;
        }
        JobSharingPlatform jobSharingPlatform = new JobSharingPlatform();
        jobSharingPlatform.setId(id);
        return jobSharingPlatform;
    }
}
