package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.search.ModuleTypeConverter;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.job.service.dto.job.SearchPreferenceDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "search_preference")
public class SearchPreference extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "search_type")
    private String searchType;

    @Column(name = "search_name")
    private String searchName;

    @Column(name = "search_group")
    private String searchGroup;

    @Column(name = "search_content")
    private String searchContent;

    @Column(name = "module")
    @Convert(converter = ModuleTypeConverter.class)
    private ModuleType module;

    @Column(name = "sort_order")
    private Integer sortOrder;

    public static SearchPreference fromSearchPreferenceDTO(SearchPreferenceDTO dto) {
        SearchPreference searchPreference = new SearchPreference();
        ServiceUtils.myCopyProperties(dto, searchPreference);
        return searchPreference;
    }
}
