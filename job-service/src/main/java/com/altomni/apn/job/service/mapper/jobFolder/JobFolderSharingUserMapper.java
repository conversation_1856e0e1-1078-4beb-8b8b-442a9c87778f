package com.altomni.apn.job.service.mapper.jobFolder;

import com.altomni.apn.job.domain.job.JobFolderSharingUser;
import com.altomni.apn.job.service.dto.folder.JobFolderSharingDTO;
import com.altomni.apn.job.service.dto.folder.JobFolderSharingUserDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

@Mapper(componentModel = "spring")
public interface JobFolderSharingUserMapper {


    JobFolderSharingUserDTO toDto(JobFolderSharingUser entity);

    @Mapping(target = "targetId", source = "userId")
    @Mapping(target = "targetCategory", constant = "USER")
    JobFolderSharingDTO toFolderSharingDto(JobFolderSharingUser entity);
    @Mapping(target = "targetId", source = "userId")
    @Mapping(target = "targetCategory", constant = "USER")
    List<JobFolderSharingDTO> toFolderSharingDto(List<JobFolderSharingUser> entity);


    JobFolderSharingUser toEntity(JobFolderSharingUserDTO dto);


    @Mapping(target = "userId", source = "targetId")
    JobFolderSharingUser toEntity(JobFolderSharingDTO dto);


}
