package com.altomni.apn.job.service.dto.address;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LocationESDTO implements Serializable {

    private static final long serialVersionUID = -1387288056631129913L;

    private Long id;

    private String location;

    private String addressLine;

    private String city;

    private String country;

    private String county;

    private String province;

    private String zipcode;

    private String chineseDisplay;

    private String englishDisplay;

    private String officialAddressLine;

    private String officialCity;

    private String officialCountry;

    private String officialCounty;

    private String officialLocation;

    private String officialProvince;

    private String officialZipcode;

    private String originDisplay;


}
