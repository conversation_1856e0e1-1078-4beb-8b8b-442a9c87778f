package com.altomni.apn.job.domain.enumeration.aisourcing;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum AiSourcingTalentTag implements ConvertedEnum<Integer> {

    NO_HUNTING(1),
    CLIENT_CONTACT(2),

    CLIENT_EMPLOYEE(3);

    private final Integer dbValue;

    AiSourcingTalentTag(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    /** static resolving: */
    public static final ReverseEnumResolver<AiSourcingTalentTag, Integer> resolver =
        new ReverseEnumResolver<>(AiSourcingTalentTag.class, AiSourcingTalentTag::toDbValue);

    public static AiSourcingTalentTag fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
