package com.altomni.apn.job.service.common;

import cn.hutool.json.JSONArray;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Component
@FeignClient(value = "common-service")
public interface CommonClient {

    @DeleteMapping("/common/api/v3/calendar-event/relationIds")
    ResponseEntity<JSONArray> deleteCalendarEventsByRecruitmentProcessRelationIds(@RequestBody List<Long> referenceIds);


}
