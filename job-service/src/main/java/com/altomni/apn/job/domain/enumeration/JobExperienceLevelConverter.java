package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class JobExperienceLevelConverter extends AbstractAttributeConverter<JobExperienceLevel, Integer> {
    public JobExperienceLevelConverter() {
        super(JobExperienceLevel::toDbValue, JobExperienceLevel::fromDbValue);
    }
}
