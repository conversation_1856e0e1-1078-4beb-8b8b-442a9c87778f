package com.altomni.apn.job.service.xxljob;

import com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;

import java.util.List;

public interface XxlJobService {

    void addCandidateReminderForJob(Long jobId);

    void updateCandidateReminderForJob(Long jobId);

    void updateReminderByJobStatusAndIds(JobStatus status, List<Long> jobIdList);

    void updateReminderByApplicationDTO(Long jobId, List<TalentRecruitmentProcessKpiUserVO> kpiUsers, TenantMessageMinderConfigFieldCodeEnum type);

}
