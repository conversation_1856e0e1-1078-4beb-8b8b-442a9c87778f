package com.altomni.apn.job.service.start;

import com.altomni.apn.job.domain.enumeration.start.StartStatus;
import com.altomni.apn.job.service.dto.start.StartDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;


@Component
@FeignClient(value = "start-service")
public interface StartService{

    @GetMapping("/start/api/v3/start/{talentId}/{status}")
    ResponseEntity<StartDTO> findByTalentIdAndStatus(@PathVariable("talentId") Long talentId, @PathVariable("status") StartStatus status);

}
