package com.altomni.apn.job.service.dto.folder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class JobFolderAndJobsDTO implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "job folder")
    JobFolderDTO jobFolder;

    @ApiModelProperty(value = "job folder relation")
    List<Long> jobIds;
}
