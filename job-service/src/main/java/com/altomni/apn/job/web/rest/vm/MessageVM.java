package com.altomni.apn.job.web.rest.vm;

//Return a object and the message to frontend.
public class MessageVM<T> {
    T object;
    String message;

    public MessageVM(T object, String message) {
        this.object = object;
        this.message = message;
    }

    public Object getObject() {
        return object;
    }

    public void setObject(T object) {
        this.object = object;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
