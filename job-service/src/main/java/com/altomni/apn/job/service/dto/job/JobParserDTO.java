package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.job.JobSkillBriefDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobParserDTO extends AbstractAuditingEntity implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    @ApiModelProperty(value = "This is job internal code company used to identify the job")
    private String code;

    @ApiModelProperty(value = "The JD in text format, to save in DB", required = true)
    private String text;

    @ApiModelProperty(value = "The responsibility part of jd Text")
    private String responsibilityText;

    @ApiModelProperty(value = "The requirement part of jd Text")
    private String requirementText;

    @ApiModelProperty(value = "The summary part of jd Text")
    private String summaryText;

    @ApiModelProperty(value = "job lacation strings, one or more official city names ")
    private List<LocationDTO> locations;

    @ApiModelProperty(value = "job function strings, one or more official function names ")
    private List<Long> jobFunctions;

    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<JobSkillBriefDTO> requiredSkills;

    @ApiModelProperty(value = "one or more preferred skills ")
    private List<JobSkillBriefDTO> preferredSkills;

    @ApiModelProperty(value = "one or more required languages")
    private List<String> requiredLanguages;

    @ApiModelProperty(value = "one or more preferred languages")
    private List<String> preferredLanguages;

    @ApiModelProperty(value = "Minimum Degree")
    private String minimumDegreeLevel;

    @ApiModelProperty(value = "range of experience year")
    private RangeDTO experienceYearRange;

    @ApiModelProperty(value = "openings")
    private Integer openings;

    @ApiModelProperty(value = "currency")
    private String currency;

    @ApiModelProperty(value = "pay type")
    private String payType;

    @ApiModelProperty(value = "salary range")
    private RangeDTO salaryRange;

}
