package com.altomni.apn.job.service.rater;

import com.altomni.apn.job.service.dto.redis.RecommendedTalentResponse;
import com.altomni.apn.job.service.dto.redis.SimilarJobResponse;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.concurrent.ExecutionException;

public interface RaterService {

    void refreshJobRater(Long tenantId, Long jobId) throws IOException;

    RecommendedTalentResponse recommendCommonTalentsForJob(Long jobId, Pageable pageable, boolean refresh);

    RecommendedTalentResponse recommendTenantTalentsForJob(Long jobId, Pageable pageable, boolean refresh);

    RecommendedTalentResponse recommendAllTalentsForJob(Long jobId, Pageable pageable, boolean refresh);

    SimilarJobResponse getSimilarJobsByJobId(Long jobId, Pageable pageable) throws IOException, ExecutionException, InterruptedException;

    void invokeRecommendCommonTalentsForJob(Long jobId,Long tenantId);
}
