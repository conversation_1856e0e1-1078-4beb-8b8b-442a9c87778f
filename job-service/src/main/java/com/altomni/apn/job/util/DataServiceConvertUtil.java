package com.altomni.apn.job.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.config.constants.EsFillerConstants;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.dto.search.*;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.job.domain.enumeration.DatePeriod;
import lombok.experimental.UtilityClass;

import java.time.Instant;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@UtilityClass
public class DataServiceConvertUtil {

    public void convertCurrency(SearchGroup searchGroup, List<EnumCurrency> enumCurrencyList) {
        if (CollUtil.isEmpty(enumCurrencyList) || ObjectUtil.isEmpty(searchGroup)) {
            return;
        }
        
        List<SearchParam> searchParamList = searchGroup.getSearch();
        List<SearchParam> filterParamList = searchGroup.getFilter().getQueryFilter();
        Map<Integer, String> map = enumCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, EnumCurrency::getName));
        doCheckCurrencyAndConvert(searchParamList, map);
        doCheckCurrencyAndConvert(filterParamList, map);
    }

    /**
     * condition eg:
     * {
     *     "key": "preferredSalary",
     *     "value": {
     *             "data": {
     *                     "gte": 250000,
     *                     "lte": 300000
     *             },
     *             "currency": 1,
     *             "timeUnit": "YEAR"
     *     }
     * }
     * 1=>USD
     */
    private void doCheckCurrencyAndConvert(List<SearchParam> searchParamList, Map<Integer, String> map) {
        final String attributeName = "currency";
        if (CollUtil.isNotEmpty(searchParamList)) {
            searchParamList.forEach(param -> {
                List<ConditionParam> conditionList = param.getCondition().stream().filter(ObjectUtil::isNotNull).filter(condition -> JSONUtil.toJsonStr(condition).contains("\"" + attributeName + "\"")).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(conditionList)) {
                    conditionList.forEach(condition -> {
                        Object value = condition.getValue();
                        JSONObject jsonObject = JSONUtil.parseObj(value);
                        Object currency = jsonObject.get(attributeName);
                        if (currency instanceof Integer && ObjectUtil.isNotNull(map.get(currency))) {
                            jsonObject.put(attributeName, map.get(currency));
                        }
                        condition.setValue(jsonObject);
                    });
                }
            });
        }
    }


    public void convertEsAndFilter(List<SearchParam> filter, SearchGroup searchGroup, List<EnumUserResponsibility> userResponsibilityList) {
        if (CollUtil.isEmpty(filter)) {
            return;
        }
        Optional.of(searchGroup.getFilter()).ifPresent(filterList -> {
            List<ConditionParam> list = new ArrayList<>();
            filter.forEach(param -> {
                ConditionParam conditionParam = param.getCondition().stream().filter(e -> Objects.equals("ANY", e.getKey())).findAny().orElse(null);
                if (ObjectUtil.isNotNull(conditionParam)) {
                    Object value = conditionParam.getValue();
                    param.getCondition().remove(conditionParam);
                    JSONObject jsonObject = JSONUtil.parseObj(value);
                    Object data = jsonObject.get("data");
                    if (data instanceof String) {
                        String dataStr = (String) data;
                        jsonObject.put("data", CollUtil.newArrayList(Long.parseLong(dataStr)));
                    } else if (data instanceof Integer) {
                        Integer dataStr = (Integer) data;
                        jsonObject.put("data", CollUtil.newArrayList(dataStr.longValue()));
                    }
                    if (CollUtil.isNotEmpty(userResponsibilityList)) {
                        userResponsibilityList.forEach(en -> list.add(new ConditionParam(EsFillerConstants.USER_RESPONSIBILITY + en.getId() + ".userId", jsonObject, null)));
                    }
                }
            });
            if (CollUtil.isNotEmpty(list)) {
                SearchParam searchParam = new SearchParam();
                searchParam.setRelation(Relation.OR);
                searchParam.setCondition(list);
                filter.add(searchParam);
            }
            searchGroup.getFilter().setQueryFilter(filter.stream().filter(s -> CollUtil.isNotEmpty(s.getCondition())).collect(Collectors.toList()));
        });
    }

    public void convertEsFilterEnumToJobEsKey(SearchGroup searchGroup, List<EnumUserResponsibility> userResponsibilityList) {
        List<SearchParam> searchParamList = searchGroup.getSearch();
        if (CollUtil.isEmpty(searchParamList)) {
            return;
        }
        if (CollUtil.isNotEmpty(userResponsibilityList)) {
            searchParamList.forEach(param -> {
                List<ConditionParam> conditionParamList = param.getCondition();
                if (CollUtil.isNotEmpty(conditionParamList)) {
                    conditionParamList.forEach(condition ->
                            userResponsibilityList.stream().filter(e -> Objects.equals(e.getLabel(), condition.getKey())).findFirst()
                                    .ifPresent(e -> condition.setKey(e.getJobEsKey() + EsFillerConstants.ES_POSTFIX)));
                }
            });
        }
    }

    public void convertFilterParamToEsFilterEnumToJobEsKey(SearchGroup searchGroup, List<EnumUserResponsibility> userResponsibilityList) {
        SearchFilterDTO filterParam = searchGroup.getFilter();
        if (filterParam == null) {
            return;
        }
        List<SearchParam> filterParamList = filterParam.getQueryFilter();
        if (CollUtil.isEmpty(filterParamList)) {
            return;
        }
        if (CollUtil.isNotEmpty(userResponsibilityList)) {
            filterParamList.forEach(param -> {
                List<ConditionParam> conditionParamList = param.getCondition();
                if (CollUtil.isNotEmpty(conditionParamList)) {
                    conditionParamList.forEach(condition ->
                            userResponsibilityList.stream().filter(e -> Objects.equals(e.getLabel(), condition.getKey())).findFirst()
                                    .ifPresent(e -> condition.setKey(e.getJobEsKey() + EsFillerConstants.ES_POSTFIX)));
                }
            });
        }
    }

    /**
     * Update value or field in search param by condition
     * @param searchGroup
     */
    public void translateFilterSearchParams(SearchGroup searchGroup) {
        List<SearchParam> searchParamList = searchGroup.getSearch();
        if (CollUtil.isEmpty(searchParamList)) {
            return;
        }
        searchParamList.forEach(param -> {
            List<ConditionParam> conditionParamList = param.getCondition();
            if (CollUtil.isNotEmpty(conditionParamList)) {
                conditionParamList.forEach(condition -> {
                    if (condition.getKey().equals(Constants.LastRecommendTalentTime)) {
                        condition.setValue(convertLastRecommendTalentTime(condition.getValue()));
                    }
                });
            }
        });
    }

    private JSONObject convertLastRecommendTalentTime(Object value){
        JSONObject jsonObject = JSONUtil.parseObj(value);
        Object data = jsonObject.get("data");
        JSONObject root = new JSONObject();
        if (data instanceof String) {

            String dataStr = (String) data;
            DatePeriod datePeriod;
            try {
                datePeriod = DatePeriod.valueOf(dataStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                return root;
            }
            root.put("data", "now-" + datePeriod.getDays() + "d");
        }
        return root;
    }

    public void convertEsAndFilterUser(List<SearchParam> filter, SearchGroup searchGroup, List<EnumUserResponsibility> userResponsibilityList) {
        if (CollUtil.isEmpty(filter)) {
            return;
        }
        Optional.of(searchGroup.getFilter()).ifPresent(filterList -> {
            List<ConditionParam> list = new ArrayList<>();
            filter.forEach(param -> {
                ConditionParam conditionParam = param.getCondition().stream().filter(e -> Objects.equals("ANY", e.getKey())).findAny().orElse(null);
                if (ObjectUtil.isNotNull(conditionParam)) {
                    Object value = conditionParam.getValue();
                    param.getCondition().remove(conditionParam);
                    JSONObject jsonObject = JSONUtil.parseObj(value);
                    Object data = jsonObject.get("data");
                    if (data instanceof String) {
                        String dataStr = (String) data;
                        jsonObject.put("data", CollUtil.newArrayList(Long.parseLong(dataStr)));
                    } else if (data instanceof Integer) {
                        Integer dataStr = (Integer) data;
                        jsonObject.put("data", CollUtil.newArrayList(dataStr.longValue()));
                    }
                    if (CollUtil.isNotEmpty(userResponsibilityList)) {
                        userResponsibilityList.forEach(en -> list.add(new ConditionParam(EsFillerConstants.USER_RESPONSIBILITY + en.getId() + ".userId", jsonObject, null)));
                    }
                }
            });
            if (CollUtil.isNotEmpty(list)) {
                SearchParam searchParam = new SearchParam();
                searchParam.setRelation(Relation.OR);
                searchParam.setCondition(list);
                filter.add(searchParam);
            }
            searchGroup.getFilter().setQueryFilter(filter.stream().filter(s -> CollUtil.isNotEmpty(s.getCondition())).collect(Collectors.toList()));
        });
    }
}
