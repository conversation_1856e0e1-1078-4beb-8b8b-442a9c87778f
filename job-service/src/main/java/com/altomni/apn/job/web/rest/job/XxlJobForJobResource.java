package com.altomni.apn.job.web.rest.job;

import com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.service.xxljob.XxlJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@RestController
@RequestMapping("/api/v3")
public class XxlJobForJobResource {

    @Resource
    private XxlJobService xxlJobService;

    //TODO 待删除
    @PutMapping("/jobs/update-no-submit-candidate-reminder-xxl-job-for-job/{jobId}")
    public ResponseEntity<Void> updateNoSubmitCandidateReminderXxlJobForJob(@PathVariable("jobId") Long jobId, @RequestBody List<TalentRecruitmentProcessKpiUserVO> kpiUsers) {
        log.info("[APN: XxlJobForJobResource @{}] REST request to update noSubmit candidate reminder xxl job for Job , jobId = {}, param = {}", SecurityUtils.getUserId(), jobId, kpiUsers);
        xxlJobService.updateReminderByApplicationDTO(jobId, kpiUsers, TenantMessageMinderConfigFieldCodeEnum.POSITION_UNSUBMITTED_CANDIDATE_DAYS);
        return ResponseEntity.ok().build();
    }

    //TODO 待删除
    @PutMapping("/jobs/update-no-interview-candidate-reminder-xxl-job-for-job/{jobId}")
    public ResponseEntity<Void> updateNoInterviewCandidateReminderXxlJobForJob(@PathVariable("jobId") Long jobId, @RequestBody List<TalentRecruitmentProcessKpiUserVO> kpiUsers) {
        log.info("[APN: XxlJobForJobResource @{}] REST request to update interview candidate reminder xxl job for Job , jobId = {}, param = {}", SecurityUtils.getUserId(), jobId, kpiUsers);
        xxlJobService.updateReminderByApplicationDTO(jobId, kpiUsers, TenantMessageMinderConfigFieldCodeEnum.POSITION_UNINTERVIEWED_DAYS);
        return ResponseEntity.ok().build();
    }

}
