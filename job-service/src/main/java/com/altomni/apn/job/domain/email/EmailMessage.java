package com.altomni.apn.job.domain.email;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EmailMessage implements Serializable {

    private Long jobId;

    private String firstName;

    private String lastName;

    private String email;

    private String resumeName;

    private String s3Link;

    private String jobName;

    private String address;

    private String type;

    private Integer count;

    private Integer maxTryCount = 3;

    /**
     * Check whether the number of retries exceeds the maximum
     *
     * @return
     */
    public boolean checkRetryCount() {
        retryCountCalculate();
        return (count < maxTryCount);
    }

    /**
     * Recalculate the number of retries
     */
    private void retryCountCalculate() {
        if (count == null) {
            count = 0;
        }
        count = count + 1;
    }

}
