//package com.altomni.apn.job.repository.job;
//
//
//import com.altomni.apn.job.domain.enumeration.SharingTargetCategory;
//import com.altomni.apn.job.domain.job.JobFolderSharing;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//import java.util.Optional;
//
//@Repository
//public interface JobFolderSharingRepository extends JpaRepository<JobFolderSharing, Long> {
//    List<JobFolderSharing> findAllByJobFolderId(Long jobFolderId);
//
//
//    Optional<JobFolderSharing> findByJobFolderIdAndTargetCategoryAndTargetId(Long folderId, SharingTargetCategory targetCategory, Long userId);
//
//    boolean deleteByJobFolderIdAndTargetCategoryAndTargetId(Long folderId, SharingTargetCategory targetCategory, Long userId);
//}
