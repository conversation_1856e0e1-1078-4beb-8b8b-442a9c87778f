package com.altomni.apn.job.service.dto.jobsharing;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.PlatformType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.wechat.WechatJSAPIConfig;
import com.altomni.apn.job.service.enumeration.JobSharingPlatformType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobSharingPlatformDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    private Long id;

    private Long jobId;

    private PlatformType platformType;
    private Integer displayLanguageId;

    @ApiModelProperty(value = "job Display html content directly use for display")
    private String jobDisplayContent;

    private String jobTitle;
    private JobSharingPlatformType jobType;
    private RateUnitType payType;
    private RangeDTO salaryRange;
    private Integer currency;
    private String location;
    private String jobResponsibilities;
    private String jobRequirements;
    private String contactInfo;

    //shared url related property
    private String uuid;
    private String s3Links;
    private String apnLinks;

    //related folder
    @ApiModelProperty(value = "input flag that current sharing link allow submit talent or not")
    private Boolean allowTalentSubmission;
    private String talentAssociatedJobFolderFolderId;
    private Instant sharedLinkExpireTime;


    private WechatJSAPIConfig wechatJSAPIConfig;



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public PlatformType getPlatformType() {
        return platformType;
    }

    public void setPlatformType(PlatformType platformType) {
        this.platformType = platformType;
    }

    public Integer getDisplayLanguageId() {
        return displayLanguageId;
    }

    public void setDisplayLanguageId(Integer displayLanguageId) {
        this.displayLanguageId = displayLanguageId;
    }

    public String getJobDisplayContent() {
        return jobDisplayContent;
    }

    public void setJobDisplayContent(String jobDisplayContent) {
        this.jobDisplayContent = jobDisplayContent;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public JobSharingPlatformType getJobType() {
        return jobType;
    }

    public void setJobType(JobSharingPlatformType jobType) {
        this.jobType = jobType;
    }

    public RateUnitType getPayType() {
        return payType;
    }

    public void setPayType(RateUnitType payType) {
        this.payType = payType;
    }

    public RangeDTO getSalaryRange() {
        return salaryRange;
    }

    public void setSalaryRange(RangeDTO salaryRange) {
        this.salaryRange = salaryRange;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getJobResponsibilities() {
        return jobResponsibilities;
    }

    public void setJobResponsibilities(String jobResponsibilities) {
        this.jobResponsibilities = jobResponsibilities;
    }

    public String getJobRequirements() {
        return jobRequirements;
    }

    public void setJobRequirements(String jobRequirements) {
        this.jobRequirements = jobRequirements;
    }

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getS3Links() {
        return s3Links;
    }

    public void setS3Links(String s3Links) {
        this.s3Links = s3Links;
    }

    public String getApnLinks() {
        return apnLinks;
    }

    public void setApnLinks(String apnLinks) {
        this.apnLinks = apnLinks;
    }

    public WechatJSAPIConfig getWechatJSAPIConfig() {
        return wechatJSAPIConfig;
    }

    public void setWechatJSAPIConfig(WechatJSAPIConfig wechatJSAPIConfig) {
        this.wechatJSAPIConfig = wechatJSAPIConfig;
    }
}
