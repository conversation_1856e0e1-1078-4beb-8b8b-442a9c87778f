package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.job.domain.enumeration.SearchCategory;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigInteger;

@Data
public class SearchCategoryCountDTO{

    @ApiModelProperty(value = "search category value from db")
    private int categoryId;
    private Long count;
//    private String labelEn;
//    private String labelCn;
    private String category;


    public SearchCategoryCountDTO(Integer status, BigInteger count) {
        this.categoryId = status != null ? status : -1;
        this.count = count != null ? count.longValue() : 0L;
    }

    public SearchCategoryCountDTO(Integer category, Long count) {
        this.categoryId = category;
        this.count = count;
    }


    public SearchCategoryCountDTO(JobStatus status, Long count) {
        this.categoryId = status.toDbValue();
        this.count = count;
    }

    public SearchCategoryCountDTO(int category, Long count, SearchCategory searchCategory) {
        this.categoryId = category;
        this.count = count;
        this.category = searchCategory.name();;
    }


}
