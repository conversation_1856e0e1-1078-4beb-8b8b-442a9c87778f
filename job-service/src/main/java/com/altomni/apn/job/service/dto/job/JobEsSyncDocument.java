package com.altomni.apn.job.service.dto.job;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.RangeDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobEsSyncDocument {

    private Long _id;

    private String _tenant_id;

    private String _type;

    private String _return_routing_key;

    private JSONObject _source;

    private Boolean _deleted;
    //TODO parser解析后有[null]  目前版本hutool处理不了 需要升级，升级修改过大， 先临时在同步时重新覆盖这2个值
    private String requiredSkills;
    private String preferredSkills;

    @Data
    public static class Source {
        private Long jobId;
        private String snapshotCreatedDate;
        private String title;
        private String startDate;
        private Byte priority;
        private List<Location> locations;
        private boolean allowRemote;
        private String currency;
        private String payType;
        private RangeDTO salaryRange;
        private List<Long> jobFunctions;
        private String minimumDegreeLevel;
        private RangeDTO experienceYearRange;
        private List<Skill> requiredSkills;
        private String logo;
        private Float currencyUSDExchangeRate;

        // extended information
        private String requirementText;
        private String responsibilityText;
        private String summaryText;
        private String text; // combination of  requirement, responsibility, summary text;
        private String additionalInfoText; // TODO
        private List<String> requiredLanguages;
        private List<String> preferredLanguages;
        private List<Skill> preferredSkills;
        private List<String> industries;
        private List<String> preferredDegreeLevels;
        private String preferredCurrency;

        private List<String> affiliations;
        private List<String> folders;
        private String createdDate;
        private String lastActivityTime;
        private String lastSubmitInterviewTime;
        private String lastModifiedDate;
        //private JobStatus status;
        private String status;
        private String postingTime;
        private List<FoldersOfPreSubmitTalents> foldersOfPreSubmitTalents;


        // For IPG
        private String recruitmentProcessId;
        private JobType type;
        private String code;
        private String department;
        private String companyId;
        private String companyName;
        private List<ClientContact> clientContacts;
        private Boolean published;
        private Integer maxSubmissions;
        private Integer openings;
        private String endDate;
        // Extended information
        private RangeDTO billRange;

        //job note
        private List<Note> notes;

        private Short totalApplications;
        private Short totalInterviews;
        private List<String> sponsorWorkAuths;

        private List<Responsibility> responsibility0;
        private List<Responsibility> responsibility1;
        private List<Responsibility> responsibility2;
        private List<Responsibility> responsibility3;
        private List<Responsibility> responsibility4;
        private List<Responsibility> responsibility5;
        private List<Responsibility> responsibility6;
        private List<Responsibility> responsibility7;
        private List<Responsibility> responsibility8;
        private List<Responsibility> responsibility9;
        private List<Responsibility> responsibility10;
        private List<Responsibility> responsibility11;
        private List<Responsibility> responsibility12;
        private List<Responsibility> responsibility13;
        private List<Responsibility> responsibility14;
        private List<Responsibility> responsibility15;
        private List<Responsibility> responsibility16;
        private List<Responsibility> responsibility17;
        private List<Responsibility> responsibility18;
        private List<Responsibility> responsibility19;
        private List<Responsibility> responsibility20;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Accessors(chain = true)
    public static class ClientContact {
        private String clientContactId;
        private String talentId;
    }

    @Data
    public static class Location {
        private String location;
        private String addressLine;
        private String city;
        private String province;
        private String county;
        private String zipcode;
        private String country;
        private String originDisplay;
        private String id;
    }

    @Data
    public static class Skill {
        private String skillName;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Responsibility {
        private String id;
        private String name;
    }

    /**
     * Job Doc Sync update note
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Note {
        private String id; // note_id, required
        private String createdDate; // Format: YYYY-MM-DDTHH:MM:SSZ
        private String lastModifiedDate; // Format: YYYY-MM-DDTHH:MM:SSZ, required
        private String text;
        private List<Responsibility> responsibility0; // LastModifiedBy
        private List<Responsibility> responsibility5;  //createdBy
    }



}
