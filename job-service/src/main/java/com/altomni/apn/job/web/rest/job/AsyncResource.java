package com.altomni.apn.job.web.rest.job;

import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.service.dto.job.JobThirdPartyDTO;
import com.altomni.apn.job.service.job.AsyncService;
import com.altomni.apn.job.service.vo.job.JobVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api/v3")
public class AsyncResource {

    private final Logger log = LoggerFactory.getLogger(AsyncResource.class);

    private final AsyncService asyncService;

    public AsyncResource(AsyncService asyncService) {
        this.asyncService = asyncService;
    }

//    @Secured(AuthoritiesConstants.ROLE_AUTOMATION)
    @PostMapping("/jobs/sync-third-party-with-authority")
    public ResponseEntity<JobVO> syncFromThirdPartyWithAuthority(@RequestBody JobThirdPartyDTO jobThirdPartyDTO) throws URISyntaxException, IOException {
        log.info("[APN: Async @{}] REST request to sync third party jobs to apn: {}", SecurityUtils.getUserId(), jobThirdPartyDTO);
        JobVO retJob = asyncService.syncFromThirdParty(jobThirdPartyDTO);
        return ResponseEntity.created(new URI("/api/v1/jobs/" + retJob.getId()))
            .headers(HeaderUtil.createEntityCreationAlert("job", retJob.getId().toString()))
            .body(retJob);
    }
}
