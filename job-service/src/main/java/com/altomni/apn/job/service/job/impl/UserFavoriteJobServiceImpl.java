//package com.altomni.apn.job.service.job.impl;
//
//import com.altomni.apn.common.errors.CustomParameterizedException;
//import com.altomni.apn.common.utils.ServiceUtils;
//import com.altomni.apn.common.domain.job.JobV3;
//import com.altomni.apn.job.domain.job.UserFavoriteJob;
//import com.altomni.apn.job.repository.job.JobRepository;
//import com.altomni.apn.job.repository.job.UserFavoriteJobRepository;
//import com.altomni.apn.common.dto.job.JobDTOV3;
//import com.altomni.apn.job.service.job.UserFavoriteJobService;
//import com.altomni.apn.common.utils.SecurityUtils;
//import org.apache.commons.collections4.CollectionUtils;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * Service class for managing users.
// */
//@Service
//@Transactional
//@Deprecated
//public class UserFavoriteJobServiceImpl implements UserFavoriteJobService {
//
//    @Resource
//    private UserFavoriteJobRepository userFavoriteJobRepository;
//
//    @Resource
//    private JobRepository jobRepository;
//
//
//    @Override
//    public List<JobDTOV3> getUserFavoriteJobs(Long userId) {
//        List<JobDTOV3> jobDTOList = new ArrayList<>();
//        List<UserFavoriteJob> userFavoriteJobList = userFavoriteJobRepository.findAllByUserId(userId);
//        if (CollectionUtils.isNotEmpty(userFavoriteJobList)) {
//            List<Long> jobIdList = new ArrayList<>();
//            userFavoriteJobList.forEach(userFavoriteJob -> jobIdList.add(userFavoriteJob.getJobId()));
//            jobDTOList = ServiceUtils.convert2DTOList(jobRepository.findAllById(jobIdList), JobDTOV3.class);
//        }
//        return jobDTOList;
//    }
//
//    @Override
//    public List<UserFavoriteJob> getUserFavoriteJobsByUserId(Long userId) {
//        return userFavoriteJobRepository.findAllByUserId(userId);
//    }
//
//    @Override
//    public List<JobDTOV3> batchCreateFavoriteJobs(List<Long> favoriteJobIds) {
//        if (CollectionUtils.isEmpty(favoriteJobIds)) {
//            throw new CustomParameterizedException("Favorite job id can not be null.");
//        }
//        List<UserFavoriteJob> userFavoriteJobList = new ArrayList<>();
//        favoriteJobIds.forEach(jobId -> userFavoriteJobList.add(new UserFavoriteJob().jobId(jobId).userId(SecurityUtils.getUserId())));
//        userFavoriteJobRepository.saveAll(userFavoriteJobList);
//        List<JobV3> jobList = jobRepository.findAllById(favoriteJobIds);
//        return ServiceUtils.convert2DTOList(jobList, JobDTOV3.class);
//    }
//
//    @Override
//    public void batchDeleteFavoriteJobs(List<Long> favoriteJobIds) {
//        if (CollectionUtils.isEmpty(favoriteJobIds)) {
//            throw new CustomParameterizedException("Favorite job id can not be null.");
//        }
//        favoriteJobIds.forEach(jobId -> userFavoriteJobRepository.deleteByUserIdAndJobId(SecurityUtils.getUserId(), jobId));
//    }
//
//    @Override
//    public List<UserFavoriteJob> findAllByJobIdAndUserId(Long jobId, Long userId) {
//        return userFavoriteJobRepository.findAllByJobIdAndUserId(jobId, userId);
//    }
//
//    @Override
//    public List<UserFavoriteJob> findAllByUserIdAndJobIdIn(Long userId, List<Long> jobIds) {
//        return userFavoriteJobRepository.findAllByUserIdAndJobIdIn(userId, jobIds);
//    }
//}
