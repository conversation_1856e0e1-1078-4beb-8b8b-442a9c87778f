package com.altomni.apn.job.service.dto.start;


import com.alibaba.nacos.shaded.com.google.common.base.Objects;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.job.domain.enumeration.start.StartStatus;
import com.altomni.apn.job.domain.enumeration.start.StartType;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A Start.
 */
public class StartDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4407706925970162814L;

    private Long id;

    private Long tenantId;

    private LocalDate startDate;

    private LocalDate endDate;

    private LocalDate warrantyEndDate;

    private StartStatus status;

    private StartType startType;

    private Long talentId;

    private String talentName;

    private Long jobId;

    private String jobTitle;

    private Long companyId;

    private String company;

    private Long applicationId;

    private Long clientContactId;

    private JobType positionType;

    private String timeZone;

    private String note;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "talentId", "jobId"));

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public LocalDate getWarrantyEndDate() {
        return warrantyEndDate;
    }

    public void setWarrantyEndDate(LocalDate warrantyEndDate) {
        this.warrantyEndDate = warrantyEndDate;
    }

    public StartStatus getStatus() {
        return status;
    }

    public void setStatus(StartStatus status) {
        this.status = status;
    }

    public StartType getStartType() {
        return startType;
    }

    public void setStartType(StartType startType) {
        this.startType = startType;
    }

    public Long getTalentId() {
        return talentId;
    }

    public void setTalentId(Long talentId) {
        this.talentId = talentId;
    }

    public String getTalentName() {
        return talentName;
    }

    public void setTalentName(String talentName) {
        this.talentName = talentName;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Long getApplicationId() {
        return applicationId;
    }

    public void setApplicationId(Long applicationId) {
        this.applicationId = applicationId;
    }

    public Long getClientContactId() {
        return clientContactId;
    }

    public void setClientContactId(Long clientContactId) {
        this.clientContactId = clientContactId;
    }

    public JobType getPositionType() {
        return positionType;
    }

    public void setPositionType(JobType positionType) {
        this.positionType = positionType;
    }

    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }



    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        StartDTO startDTO = (StartDTO) o;
        return Objects.equal(id, startDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(id);
    }

    @Override
    public String toString() {
        return "StartDTO{" +
            "id=" + id +
            ", tenantId=" + tenantId +
            ", startDate=" + startDate +
            ", endDate=" + endDate +
            ", warrantyEndDate=" + warrantyEndDate +
            ", status=" + status +
            ", startType=" + startType +
            ", talentId=" + talentId +
            ", talentName='" + talentName + '\'' +
            ", jobId=" + jobId +
            ", jobTitle='" + jobTitle + '\'' +
            ", companyId=" + companyId +
            ", company='" + company + '\'' +
            ", applicationId=" + applicationId +
            ", clientContactId=" + clientContactId +
            ", positionType=" + positionType +
            ", timeZone='" + timeZone + '\'' +
            ", note='" + note +
            '}';
    }
}
