package com.altomni.apn.job.service.dto.job;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessStats;
import com.altomni.apn.job.service.dto.report.ActivityStats;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel
public class JobESSearchDTO {

    private Long id;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    private String companyName;

    private Long companyId;

    private String code;

    private String department;

    private Object type;

    //company only
    private JobType jobType;

    private JobStatus status;

    private Boolean favoriteFlag;

    private String postingTime;

//    @JsonProperty("AMName")
//    private String amName;
//
//    @JsonProperty("AM_NAMES")
//    private String amNames;

    private List<Long> amIds;

    /**
     * job search list web use only
     */
    private Long createdById;

    @ApiModelProperty(value = "This locations source from job city, country etc. This format is esfiller prefer format.")
    private List<JSONObject> locations;

    private List<String> cities;

    private List<String> provinces;

    private List<String> countries;

    private List<String> jobFunctions;

    private String minimumDegreeLevel;

    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<JSONObject> requiredSkills;

    @ApiModelProperty(value = "one or more preferred skills ")
    private List<JSONObject> preferredSkills;

    @ApiModelProperty(value = "one or more required languages")
    private List<String> requiredLanguages;

    @ApiModelProperty(value = "one or more preferred languages")
    private List<String> preferredLanguages;

    private String currency;

    private RangeDTO experienceYearRange;

    private Integer openings;

    private Integer maxSubmissions;

    private String hiringManager;

    private String applicationStats;

    private List<ActivityStats> activityStats;

    private Long contactCategoryId;

    private Boolean published;

    private List<RecruitmentProcessStats> processStats;

    private JSONArray companyAmIds;
}
