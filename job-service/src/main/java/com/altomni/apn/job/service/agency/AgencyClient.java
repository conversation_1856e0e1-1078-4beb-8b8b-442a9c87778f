package com.altomni.apn.job.service.agency;

import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateBySendTimeForJobAdminDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Component
@FeignClient(value = "agency-service")
public interface AgencyClient {

    @PostMapping("/agency/api/v3/job/{jobId}/stop-sharing/all")
    ResponseEntity<Void> updateAgencySharedStatusByJobId(@PathVariable("jobId") Long jobId);

}
