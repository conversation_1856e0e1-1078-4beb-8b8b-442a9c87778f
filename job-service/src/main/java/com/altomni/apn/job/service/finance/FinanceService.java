package com.altomni.apn.job.service.finance;

import cn.hutool.json.JSONArray;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Component
@FeignClient(value = "finance-service")
public interface FinanceService {

    @DeleteMapping("/finance/api/v3/starts/job/{jobId}")
    ResponseEntity<JSONArray> deleteStartByJobId(@PathVariable("jobId") Long jobId);

}
