package com.altomni.apn.job.service.dto.redis;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class RedisTalentDTOV2 {

    private Double score;

    private String fullName;

    private List<RedisTalentExperience> pastExperiences;

    private List<RedisTalentExperience> currentExperiences;

    private List<RedisTalentSkill> skills;

    public static class RedisTalentExperience {

        private String title;

        private String companyName;

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getCompanyName() {
            return companyName;
        }

        public void setCompanyName(String companyName) {
            this.companyName = companyName;
        }
    }

    public static class RedisTalentSkill {

        private String skillName;

        public String getSkillName() {
            return skillName;
        }

        public void setSkillName(String skillName) {
            this.skillName = skillName;
        }
    }
}
