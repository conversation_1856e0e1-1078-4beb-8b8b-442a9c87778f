package com.altomni.apn.job.service.dto.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobCompanyContactRelationDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "user id .", required = true)
    private Long userId;

    @ApiModelProperty(value = "job id", required = true)
    private Long jobId;

}
