package com.altomni.apn.job.service.vo.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.LinkedHashSet;
import java.util.Set;

/**
 * <AUTHOR>
 */
@ApiModel
public class ProjectTeamUserVO {

    @ApiModelProperty(value = " id")
    public Long id;

    @ApiModelProperty(value = "user id")
    public Long userId;
    @ApiModelProperty(value = "email")
    public String email;
    @ApiModelProperty(value = "user name")
    public String username;
    @ApiModelProperty(value = "first name")
    public String firstName;
    @ApiModelProperty(value = "last name")
    public String lastName;

    @ApiModelProperty(value = "team id")
    private Long teamId;

    @ApiModelProperty(value = "permissions")
    private Set<String> permissionSet=new LinkedHashSet<>();



    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public Set<String> getPermissionSet() {
        return permissionSet;
    }

    public void setPermissionSet(Set<String> permissionSet) {
        this.permissionSet = permissionSet;
    }

    @Override
    public String toString() {
        return "ProjectTeamUserDTO{" +
                "id=" + id +
                ", userId=" + userId +
                ", email='" + email + '\'' +
                ", username='" + username + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", permissionSet=" + permissionSet +
                '}';
    }
}
