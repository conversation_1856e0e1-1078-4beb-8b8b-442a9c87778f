package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.JobLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data JPA repository for the JobLocation entity.
 */
@Repository
public interface JobLocationRepository extends JpaRepository<JobLocation, Long> {

    List<JobLocation> findAllByJobIdIn(List<Long> jobId);

    @Modifying
    @Transactional
    @Query(value = "delete from job_location where job_id = ?1", nativeQuery = true)
    void deleteAllByJobId(Long jobId);

    @Modifying
    @Transactional
    @Query(value = " delete from job_location where job_id = ?1 and original_loc is null ", nativeQuery = true)
    void deleteAllByJobIdAndOriginalLoc(Long jobId);

    List<JobLocation> findAllByJobId(Long jobId);

    @Query(value = " select * from job_location loc where loc.job_id = ?1 and loc.original_loc is not null ", nativeQuery = true)
    List<JobLocation> findAllByJobIdAndOriginalLocIsNotNull(Long jobId);

    @Modifying
    @Transactional
    @Query(value = "update job_location set official_city = ?2, official_country = ?3, official_province = ?4, official_county = ?5 where id = ?1", nativeQuery = true)
    void updateOfficialInfoById(Long id, String officialCity, String officialCountry, String officialProvince, String officialCounty);


    @Query(value = " SELECT concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.job_id = ?1 and loc.original_loc is not null group by loc.job_id ", nativeQuery = true)
    String findOriginalLocsAllByJobId(Long jobId);

    @Query(value = "select group_concat(loc.format_location SEPARATOR ';') from job_location loc where job_id=:jobId", nativeQuery = true)
    String getJobLocationsByJobId(@Param("jobId") Long jobId);
}
