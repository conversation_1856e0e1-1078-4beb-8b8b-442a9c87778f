package com.altomni.apn.job.service.dto.jobsharing;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.job.service.enumeration.JobSharingPlatformType;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;


@AllArgsConstructor
@NoArgsConstructor
public class JobSharingContentDTO {

    private String jobTitle;
    private JobSharingPlatformType jobType;
    private RateUnitType payType;
    private RangeDTO salaryRange;
    private Integer currency;
    private String location;
    private String jobResponsibilities;
    private String jobRequirements;
    private String contactInfo;

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public JobSharingPlatformType getJobType() {
        return jobType;
    }

    public void setJobType(JobSharingPlatformType jobType) {
        this.jobType = jobType;
    }

    public RateUnitType getPayType() {
        return payType;
    }

    public void setPayType(RateUnitType payType) {
        this.payType = payType;
    }

    public RangeDTO getSalaryRange() {
        return salaryRange;
    }

    public void setSalaryRange(RangeDTO salaryRange) {
        this.salaryRange = salaryRange;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getJobResponsibilities() {
        return jobResponsibilities;
    }

    public void setJobResponsibilities(String jobResponsibilities) {
        this.jobResponsibilities = jobResponsibilities;
    }

    public String getJobRequirements() {
        return jobRequirements;
    }

    public void setJobRequirements(String jobRequirements) {
        this.jobRequirements = jobRequirements;
    }

    public String getContactInfo() {
        return contactInfo;
    }

    public void setContactInfo(String contactInfo) {
        this.contactInfo = contactInfo;
    }
}
