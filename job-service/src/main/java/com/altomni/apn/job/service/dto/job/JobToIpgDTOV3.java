package com.altomni.apn.job.service.dto.job;
import cn.hutool.core.util.ObjectUtil;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.job.IpgJobSalaryDTO;
import com.altomni.apn.common.enumeration.JobDescriptionType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobToIpgDTOV3 implements Serializable{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    @ApiModelProperty(value = "Job type", allowableValues = "CONTRACT, FULL_TIME, PAY_ROLL")
    private JobType type;

    @ApiModelProperty(value = "job function strings, one or more official function names ")
    private List<EnumRelationDTO> jobFunctions;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
    private JobStatus status;

    @ApiModelProperty(value = "The job of department .")
    private String department;

    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<SkillDTO> requiredSkills;

    @ApiModelProperty(value = "range of experience year")
    private RangeDTO experienceYearRange;

    @ApiModelProperty(value = "Minimum Degree")
    private EnumRelationDTO minimumDegreeLevel;

    @ApiModelProperty(value = "job lacation strings, one or more official city names ")
    private List<LocationDTO> locations;

    @ApiModelProperty(value = "The url link to the JD. E.g. link to StoreService storage for the JD")
    private String addressLine;

//    @Size(max = 16380, message = "The JD text is too long.")
//    @ApiModelProperty(value = "The JD in text format, to save in DB", required = true)
//    private String jdText;

    @Size(max = 16380, message = "The JD text is too long.")
    @ApiModelProperty(value = "The public description of JD", required = true)
    private String publicDesc;

    @ApiModelProperty(value = "Access token returned by login IPG")
    private String ipgAccessToken;

    @ApiModelProperty(value = "HTML indicates JD that the format is HTML, rich text and PLAIN_TEXT indicates that JD format is plain text.")
    private JobDescriptionType jobDescriptionType = JobDescriptionType.HTML;

    @ApiModelProperty(value = " only for apn v3 created ipg job ")
    private String jobVersion = "V3";

    @ApiModelProperty(value = "IPG job requirements")
    private String ipgJobRequirements;

    @ApiModelProperty(value = "IPG job description")
    private String ipgJobResponsibilities;

    @ApiModelProperty(value = "IPG job summary")
    private String ipgJobSummary;

    @ApiModelProperty(value = "IPG job 1：flexible location, 0：fixed work location")
    private boolean ipgFlexibleLocation;

    @ApiModelProperty(value = "bill rate range")
    private RangeDTO billRange;

    @ApiModelProperty(value = "salary rate range")
    private RangeDTO salaryRange;

    @ApiModelProperty(value = "salary rate unit")
    private RateUnitType payType;

    @ApiModelProperty(value = "currency")
    private EnumRelationDTO currency;

    @ApiModelProperty(value = "IPG job 1: include default content, 0: nothing")
    private boolean ipgIncludeDefaultContent;

    @ApiModelProperty(value = "IPG job posting default content")
    private String ipgDefaultContent;

    public static JobToIpgDTOV3 fromJobDTO(JobToIpgDTO jobDTO) {
        JobToIpgDTOV3 jobToIpgDTOV3 = new JobToIpgDTOV3();
        ServiceUtils.myCopyProperties(jobDTO, jobToIpgDTOV3);
        jobToIpgDTOV3.setType(jobDTO.getJobType());
//        List<String> jsonFunctionList = jobDTO.getJobFunctions();
//        if (CollUtil.isNotEmpty(jobDTO.getJobFunctions())) {
//            jobToIpgDTOV3.setJobFunctions(EnumRelationDTO.transfer(jsonFunctionList.stream().map(Long::parseLong).collect(Collectors.toList())));
//        }
//        List<Long> degreeList = jobDTO.getMinimumDegreeLevel();
//        if (CollUtil.isNotEmpty(degreeList)) {
//            EnumRelationDTO enumRelationDTO = new EnumRelationDTO();
//            enumRelationDTO.setEnumId(degreeList.get(0) + "");
//            jobToIpgDTOV3.setMinimumDegreeLevel(enumRelationDTO);
//        }
        if (ObjectUtil.isNotEmpty(jobDTO.getIpgJobSalaryDTO())){
            IpgJobSalaryDTO jobSalaryDTO = jobDTO.getIpgJobSalaryDTO();
            jobToIpgDTOV3.setBillRange(jobSalaryDTO.getBillRange());
            jobToIpgDTOV3.setSalaryRange(jobSalaryDTO.getSalaryRange());
            jobToIpgDTOV3.setCurrency(jobSalaryDTO.getCurrency());
            jobToIpgDTOV3.setPayType(jobSalaryDTO.getPayType());
        }
        jobToIpgDTOV3.setAddressLine(jobDTO.getJdUrl());
        return jobToIpgDTOV3;
    }

}
