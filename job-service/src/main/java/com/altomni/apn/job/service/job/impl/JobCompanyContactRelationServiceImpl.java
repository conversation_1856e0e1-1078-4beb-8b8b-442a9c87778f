package com.altomni.apn.job.service.job.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.company.ClientContactBriefInfoDTO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.job.JobCompanyContactRelation;
import com.altomni.apn.job.repository.job.JobCompanyContactRelationRepository;
import com.altomni.apn.job.service.company.CompanyService;
import com.altomni.apn.job.service.job.JobCompanyContactRelationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class JobCompanyContactRelationServiceImpl implements JobCompanyContactRelationService {

    @Resource
    private JobCompanyContactRelationRepository jobCompanyContactRelationRepository;

    @Resource
    private CompanyService companyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Override
    @Transactional
    public void create(JobDTOV3 jobDTOV3) {
        if(!doClientContactsRequired()){
            return;
        }
        ClientContactBriefInfoDTO clientContactBriefInfoDTO = validateClientContact(jobDTOV3);
        JobCompanyContactRelation relation = new JobCompanyContactRelation();
        relation.setContactCategory(jobDTOV3.getClientContactCategory());
        relation.setClientContactId(jobDTOV3.getClientContact().getId());
        relation.setJobId(jobDTOV3.getId());
        relation.setTalentId(clientContactBriefInfoDTO.getTalentId());
        jobCompanyContactRelationRepository.saveAndFlush(relation);
    }

    @Override
    @Transactional
    public void update(JobDTOV3 jobDTOV3) {
        if(!doClientContactsRequired()){
            return;
        }
        //client contact multiple input
        ClientContactBriefInfoDTO clientContactBriefInfoDTO = validateClientContact(jobDTOV3);

        List<JobCompanyContactRelation> relations = jobCompanyContactRelationRepository.findAllByJobId(jobDTOV3.getId());
        if(relations.size() == 1){
            JobCompanyContactRelation relation = relations.get(0);
            relation.setContactCategory(jobDTOV3.getClientContactCategory());
            relation.setClientContactId(jobDTOV3.getClientContact().getId());
            relation.setJobId(jobDTOV3.getId());
            relation.setTalentId(clientContactBriefInfoDTO.getTalentId());
            jobCompanyContactRelationRepository.saveAndFlush(relation);
        }

    }

    private ClientContactBriefInfoDTO validateClientContact(JobDTOV3 jobDTOV3) {
        if (jobDTOV3.getClientContact() == null || jobDTOV3.getClientContact().getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATECLIENTCONTACT_CLIENTCONTACTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (jobDTOV3.getCompany() == null || jobDTOV3.getCompany().getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATECLIENTCONTACT_COMPANYNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        ClientContactBriefInfoDTO clientContactBriefInfoDTO = companyService.getCompanyClientContactBriefInfo(jobDTOV3.getCompany().getId(), jobDTOV3.getClientContact().getId()).getBody();
        if (clientContactBriefInfoDTO == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATECLIENTCONTACT_CLIENTCONTACTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        return clientContactBriefInfoDTO;

    }

    private boolean doClientContactsRequired(){
        return SecurityUtils.getUserType() != TenantUserTypeEnum.EMPLOYER;
    }

}

