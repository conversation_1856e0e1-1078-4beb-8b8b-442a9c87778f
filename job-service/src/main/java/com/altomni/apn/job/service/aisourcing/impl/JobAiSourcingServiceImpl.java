package com.altomni.apn.job.service.aisourcing.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.job.JobAdditionalInfo;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NoPermissionException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.ApplicationProperties;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.aisourcing.JobAiSourcing;
import com.altomni.apn.job.repository.aisourcing.JobAiSourcingRepository;
import com.altomni.apn.job.repository.job.JobFunctionRelationRepository;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.service.aisourcing.JobAiSourcingService;
import com.altomni.apn.job.service.dto.aisourcing.AiSourcingSearchDTO;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.SetUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class JobAiSourcingServiceImpl implements JobAiSourcingService {

    public static final String ENDPOINT_CREATE_SEARCH_TASK  = "/api/iteration_search/sourcing";
    public static final String ENDPOINT_GET_AI_SOURCING_PROGRESS  = "/job/api/v3/ai-sourcing/job/%d/progress";
    public static final String ENDPOINT_GET_AI_SOURCING_RESULT  = "/job/api/v3/ai-sourcing/tenant/%d/job/%d/result?page=%d&size=%d";
    public static final String ENDPOINT_GET_TALENT_DETAIL  = "/talent/api/v3/ai-sourcing/tenant/%d/talent/%s";

    @Resource
    private JobAiSourcingRepository jobAiSourcingRepository;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private HttpService httpService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Resource
    JobFunctionRelationRepository jobFunctionRelationRepository;

    @Override
    public void saveAll(Set<Long> jobIds) {
        Long tenantId = SecurityUtils.getTenantId();
        Set<Long> existJobIds = jobAiSourcingRepository.getExistJobIds(jobIds);
        jobAiSourcingRepository.saveAll(SetUtils.difference(jobIds, existJobIds).stream().map(jobId -> new JobAiSourcing(null, jobId, tenantId,
                false, null, true, 1)).collect(Collectors.toSet()));
    }

    @Override
    public void enableAiSourcing(Long jobId) throws IOException {
        this.checkRequiredContent(jobId);
        jobAiSourcingRepository.updateAiSourcingStatusByJobId(jobId, true);
        jobAiSourcingRepository.flush();
        this.startAiSearching(jobId);
    }

    private void checkRequiredContent(Long jobId){
        JobV3 job = jobRepository.findById(jobId)
                .orElseThrow(() -> new NoPermissionException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_FINDONEWITHENTITY_NOPERMISSION.getKey(),
                        CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService())));
        if (Objects.isNull(job.getCurrency())
//                || Objects.isNull(job.getMinimumDegreeId())
                || Objects.isNull(job.getJobAdditionalInfo())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_INFO_INCOMPLETE_FOR_AI_SOURCING.getKey(),
                    CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        JobDTOV3 jobDto = JobDTOV3.fromJob(job);
        if (Objects.isNull(jobDto.getSalaryRange())
                || Objects.isNull(jobDto.getRequiredSkills())
                || Objects.isNull(jobDto.getExperienceYearRange())
                || Objects.isNull(jobDto.getPreferredCompanies())
                || Objects.isNull(jobDto.getPreferredIndustry())
                || StringUtils.isBlank(jobDto.getRequirements())
                || StringUtils.isBlank(jobDto.getResponsibilities())
                || !jobFunctionRelationRepository.existsByJobId(jobId)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_INFO_INCOMPLETE_FOR_AI_SOURCING.getKey(),
                    CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
    }

    private void startAiSearching(Long jobId) throws IOException {
        String api = applicationProperties.getAiSourcingHost() + ENDPOINT_CREATE_SEARCH_TASK;
        List<JobAiSourcing> jobAiSourcings = jobAiSourcingRepository.findFirstByJobId(jobId);
        if (CollectionUtils.isEmpty(jobAiSourcings)){
            return;
        }
        String condition = JSONUtil.toJsonStr(Map.of("job_id", jobId, "search_id", jobAiSourcings.get(0).getSearchVersion(), "channel", "linkedin_sales_nav"));
        HttpResponse response = httpService.post(api, condition);
        if (Objects.nonNull(response) && HttpStatus.OK.value() == response.getCode()) {
            jobAiSourcingRepository.setJobInfoUpdated(jobId, false);
        }
    }

    @Override
    public void disableAiSourcing(Long jobId) {
        jobAiSourcingRepository.updateAiSourcingStatusByJobId(jobId, false);
    }

    @Override
    public String getAiSourcingProgress(Long jobId, boolean refresh) throws IOException {
        StopWatch stopWatch = new StopWatch("echocheng6 start");
        stopWatch.start("[echocheng 6.1] enableAiSourcing");
        if (refresh){
            this.enableAiSourcing(jobId);
        }
        stopWatch.stop();
        stopWatch.start("[echocheng 6.2] aiSourcing");
        String api = applicationProperties.getAiSourcingHost() + String.format(ENDPOINT_GET_AI_SOURCING_PROGRESS, jobId);
        HttpResponse response = httpService.get(api);
        stopWatch.stop();
        log.info("[apn @{}] echocheng6 time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        if (Objects.nonNull(response) && HttpStatus.OK.value() == response.getCode()) {
            return response.getBody();
        }
        throw new CustomParameterizedException("Get AI sourcing error {}", response.toString());
    }

    @Override
    public String getAiSourcingResult(Long jobId, Pageable pageable, HttpHeaders headers) throws IOException {
        String api = applicationProperties.getAiSourcingHost() + String.format(ENDPOINT_GET_AI_SOURCING_RESULT, SecurityUtils.getTenantId(), jobId, pageable.getPageNumber(), pageable.getPageSize());
        log.info(api);
        HttpResponse response = httpService.get(api);
        if (Objects.nonNull(response) && HttpStatus.OK.value() == response.getCode()) {
            headers.set("total_num", response.getHeaders().values("total_num").get(0));
            headers.set("last_updated_time", response.getHeaders().values("last_updated_time").get(0));
            return response.getBody();
        }
        throw new CustomParameterizedException("Get AI sourcing error {}", response.toString());
    }

    @Override
    public String searchAiSourcingResult(Long jobId, AiSourcingSearchDTO aiSourcingSearchDTO, Pageable pageable, HttpHeaders headers) throws IOException {
        String api = applicationProperties.getAiSourcingHost() + String.format(ENDPOINT_GET_AI_SOURCING_RESULT, SecurityUtils.getTenantId(), jobId, pageable.getPageNumber(), pageable.getPageSize());
        log.info(api);
        HttpResponse response = httpService.post(api, JSONUtil.toJsonStr(aiSourcingSearchDTO));
        if (Objects.nonNull(response) && HttpStatus.OK.value() == response.getCode()) {
            headers.set("total_num", response.getHeaders().values("total_num").get(0));
            headers.set("last_updated_time", response.getHeaders().values("last_updated_time").get(0));
            return response.getBody();
        }
        throw new CustomParameterizedException("Search AI sourcing result error {}", response.toString());
    }

    @Override
    public String getTalentDetail(String profileId) throws IOException {
        String api = applicationProperties.getAiSourcingHost() + String.format(ENDPOINT_GET_TALENT_DETAIL, SecurityUtils.getTenantId(), profileId);
        log.info(api);
        HttpResponse response = httpService.get(api);
        if (Objects.nonNull(response) && HttpStatus.OK.value() == response.getCode()) {
            return response.getBody();
        }
        throw new CustomParameterizedException("Get AI sourcing talent detail error {}", response.toString());
    }
}
