package com.altomni.apn.job.web.rest.dict;

import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.service.enums.EnumUserResponsibilityService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = {"APN-EnumDict"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class EnumUserResponsibilityResource {
    @Resource
    private EnumUserResponsibilityService enumUserResponsibilityService;


    @GetMapping("/dict/user-responsibility/all")
    public ResponseEntity<List<EnumUserResponsibility>> findAllUserResponsibility() {
        log.info("[APN: DictCode @{}] REST request to get userResponsibility all", SecurityUtils.getUserId());
        return ResponseEntity.ok(enumUserResponsibilityService.findAllUserResponsibility());
    }

    @GetMapping("/dict/user-responsibility/info/id/{id}")
    public ResponseEntity<EnumUserResponsibility> findById(@PathVariable("id") Long id) {
        log.info("[APN: DictCode @{}] REST request to get userResponsibility dict data  : {}", SecurityUtils.getUserId(), id);
        return ResponseEntity.ok(enumUserResponsibilityService.findById(id));
    }

}
