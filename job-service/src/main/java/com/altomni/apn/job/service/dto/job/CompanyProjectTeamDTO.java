package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.company.service.dto.projectTeam.CompanyProjectTeamUserDTO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

public class CompanyProjectTeamDTO implements Serializable {

    private Long id;

    private Long tenantId;

    @NotNull
    private Long companyId;

    @Size(max = 40)
    @NotBlank
    private String name;

    @NotNull
    private Long leaderUserId;

    private String description;

    private Instant createdDate;

    private List<CompanyProjectTeamUserDTO> companyProjectTeamUsers;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getLeaderUserId() {
        return leaderUserId;
    }

    public void setLeaderUserId(Long leaderUserId) {
        this.leaderUserId = leaderUserId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Instant getCreatedDate() { return createdDate; }

    public void setCreatedDate(Instant createdDate) { this.createdDate = createdDate; }

    public List<CompanyProjectTeamUserDTO> getCompanyProjectTeamUsers() { return companyProjectTeamUsers; }

    public void setCompanyProjectTeamUsers(List<CompanyProjectTeamUserDTO> companyProjectTeamUsers) {
        this.companyProjectTeamUsers = companyProjectTeamUsers;
    }
}
