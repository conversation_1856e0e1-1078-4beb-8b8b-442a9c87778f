package com.altomni.apn.job.domain.aisourcing;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.job.domain.enumeration.aisourcing.SelectionStatus;
import com.altomni.apn.job.domain.enumeration.aisourcing.SelectionStatusConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "job_ai_sourcing_talent")
public class JobAiSourcingTalent extends AbstractPermissionAuditingEntity implements Serializable, Cloneable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Refer to Job Ai Sourcing Id")
    @Column(name = "job_ai_sourcing_id", nullable = false)
    private Long jobAiSourcingId;

    @ApiModelProperty(value = "Talent Id")
    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    @ApiModelProperty(value = "Accept | Reject | Save for later")
    @Convert(converter = SelectionStatusConverter.class)
    @Column(name = "selection_status")
    private SelectionStatus selectionStatus;
}
