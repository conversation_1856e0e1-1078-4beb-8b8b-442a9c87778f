package com.altomni.apn.job.service.dto.jobsharing;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class JobSharingPosterImageDTO {

    @ApiModelProperty(value = "fileName for poster image")
    @NotEmpty
    private String fileName;

    @ApiModelProperty(value = "linkUrl for poster image")
    @NotEmpty
    private String linkUrl;

    @ApiModelProperty(value = "id for poster image")
    private Long id;

}
