package com.altomni.apn.job.service.job;

import com.altomni.apn.common.domain.job.JobNote;
import com.altomni.apn.common.dto.job.JobNoteDTO;
import com.altomni.apn.job.service.dto.job.JobNoteKeywordDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface JobNoteService {

    JobNoteDTO create(JobNoteDTO jobNote);

    JobNoteDTO update(JobNoteDTO jobNote);

    JobNoteDTO findOneWithEntity(Long id);

    JobNoteDTO findOne(Long id);

    List<JobNoteDTO> findAllByJobId(Long jobId);

    void delete(Long id);

    Page<JobNoteDTO> searchJobNoteByKeyword(JobNoteKeywordDTO jobNoteKeywordDTO, Pageable pageable);
}
