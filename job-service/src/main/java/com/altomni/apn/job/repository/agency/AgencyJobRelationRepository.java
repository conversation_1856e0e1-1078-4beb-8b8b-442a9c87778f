package com.altomni.apn.job.repository.agency;

import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.job.domain.agency.AgencyJobRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data repository for the JobSharingToAgencyInfo entity.
 */
@SuppressWarnings("unused")
@Repository
public interface AgencyJobRelationRepository extends JpaRepository<AgencyJobRelation, Long> {

    List<AgencyJobRelation> findAllByJobIdAndShareStatus(Long jobId, JobShareStatus shareStatus);

}
