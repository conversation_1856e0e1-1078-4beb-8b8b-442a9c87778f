package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.altomni.apn.common.domain.dict.EnumLanguage;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.EnumWorkAuthorizationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.job.SearchPreference;
import com.altomni.apn.job.repository.job.SearchPreferenceRepository;
import com.altomni.apn.job.service.dto.job.SearchPreferenceDTO;
import com.altomni.apn.job.service.job.SearchPreferenceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SearchPreferenceServiceImpl implements SearchPreferenceService {

    @Resource
    private SearchPreferenceRepository searchPreferenceRepository;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private EnumWorkAuthorizationService enumWorkAuthorizationService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Override
    public List<SearchPreferenceDTO>  findByUserId(Long userId, String searchName, ModuleType modul) {
        List<SearchPreference> result = new ArrayList<>();
        if(ObjectUtil.isNotEmpty(searchName)) {
            result = searchPreferenceRepository.findAllByUserIdAndSearchNameAndModule(userId, searchName, modul.toDbValue());
        }else {
            result = searchPreferenceRepository.findAllByUserIdAndModuleOrderByCreatedDateDesc(userId, modul);
        }
        return CollUtil.isNotEmpty(result) ? result.stream().map(SearchPreferenceDTO::fromSearchPreference).collect(Collectors.toList()) : new ArrayList<>();

    }

    @Override
    public SearchPreferenceDTO create(SearchPreferenceDTO searchPreferenceDTO) {
        SearchPreference searchPreference = SearchPreference.fromSearchPreferenceDTO(searchPreferenceDTO);
        searchPreference.setUserId(SecurityUtils.getUserId());
        return SearchPreferenceDTO.fromSearchPreference(searchPreferenceRepository.saveAndFlush(searchPreference));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        SearchPreference searchPreference = searchPreferenceRepository.findById(id).orElse(null);
        if(ObjectUtil.isNotEmpty(searchPreference)) {
            if(ObjectUtil.compare(SecurityUtils.getUserId(), searchPreference != null ? searchPreference.getUserId() : null) == 0) {
                searchPreferenceRepository.deleteById(id);
            }else {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SEARCHPER_DELETEBYID_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
        }else{
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SEARCHPER_CONFIGNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
    }

    @Override
    public void deleteAllByUserId(Long userId) {
        searchPreferenceRepository.deleteAllByUserId(userId);
    }

    /**
     * The interface is only used to update the historical data format saved by the/search/config interface.
     * @return
     */
    @Override
    @Transactional
    public Object searchPreferenceFormatUpdate() {
        Instant start = Instant.now();
        log.info("search preference format update start : {}", start);
        int page = 0;
        int size = 1000;
        long total = 0L;
        Map<String, EnumDegree> enumDegreeMap = enumCommonService.findAllEnumDegree().stream().collect(Collectors.toMap(o -> String.valueOf(o.getId()), EnumDegree -> EnumDegree));
        Map<String, EnumLanguage> enumLanguageMap = enumCommonService.findAllEnumLanguages().stream().collect(Collectors.toMap(o -> String.valueOf(o.getId()), EnumLanguage -> EnumLanguage));
        Map<String, EnumCurrency> enumCurrencyMap = enumCommonService.findAllEnumCurrency().stream().collect(Collectors.toMap(o -> String.valueOf(o.getId()), EnumCurrency -> EnumCurrency));
        List<EnumDictDTO> enumWorkAuthorizationTreeList = enumWorkAuthorizationService.findAllOrderBySortType(SortType.EN);
        Map<String, EnumDictDTO> enumDictDTOMap = enumWorkAuthorizationService.findAll().stream().collect(Collectors.toMap(EnumDictDTO::getId, EnumDictDTO -> EnumDictDTO));
        while (true) {
            Pageable pageable = PageRequest.of(page, size);
            Page<SearchPreference> searchPreferencePage = searchPreferenceRepository.findAll(pageable);
            List<SearchPreference> searchPreferenceList = searchPreferencePage.getContent();
            List<SearchPreference> baseSearchPreferenceList = searchPreferenceList.stream().filter(o -> "BASE".equals(o.getSearchType())).collect(Collectors.toList());
            List<SearchPreference> advancedSearchPreferenceList = searchPreferenceList.stream().filter(o -> "ADVANCED".equals(o.getSearchType())).collect(Collectors.toList());

            log.info("search preferences count = {}, size = {}", page + 1, searchPreferenceList.size());
            total += searchPreferenceList.size();
            if (CollUtil.isEmpty(searchPreferenceList)) {
                break;
            }

            baseSearchPreferenceList.forEach(o -> {
                try {
                    updateBaseSearchPreferenceData(o, enumDegreeMap, enumLanguageMap, enumCurrencyMap, enumWorkAuthorizationTreeList, enumDictDTOMap);
                }
                catch (Exception e) {
                    log.error("Data format conversion exception, id: {}, message: {}", o.getId(), e.getMessage());
                }
            });

            advancedSearchPreferenceList.forEach(o -> {
                try {
                    updateAdvancedSearchPreferenceData(o, enumDegreeMap, enumLanguageMap, enumCurrencyMap, enumWorkAuthorizationTreeList, enumDictDTOMap);
                }
                catch (Exception e) {
                    log.error("Data format conversion exception, id: {}, message: {}", o.getId(), e.getMessage());
                }
            });

            searchPreferenceRepository.saveAll(baseSearchPreferenceList);
            searchPreferenceRepository.saveAll(advancedSearchPreferenceList);
            page++;
        }

        Instant end = Instant.now();
        log.info("search preference format update end : {}", end);
        Map<String, Object> result = new HashMap<>();
        result.put("start", start);
        result.put("end", end);
        result.put("total", total);
        return result;
    }

    private void updateBaseSearchPreferenceData(SearchPreference searchPreference, Map<String, EnumDegree> enumDegreeMap, Map<String, EnumLanguage> enumLanguageMap,
                                                Map<String, EnumCurrency> enumCurrencyMap, List<EnumDictDTO> enumWorkAuthorizationTreeList, Map<String, EnumDictDTO> enumDictDTOMap) {
        JSONObject searchGroupData = JSONObject.parseObject(searchPreference.getSearchGroup());

            if (searchGroupData.containsKey("minimumDegreeLevel")) {
                JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(searchGroupData.get("minimumDegreeLevel")));
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (int i = 0 ; i < jsonArray.size() ; i++) {
                        EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(jsonArray.getJSONObject(i)), EnumDictDTO.class);
                        if (!enumDegreeMap.containsKey(enumDictDTO.getId())) {
                            continue;
                        }
                        enumDictDTO.setName(enumDegreeMap.get(enumDictDTO.getId()).getName());
                        jsonArray.set(i, JSONObject.toJSON(enumDictDTO));
                    }
                    searchGroupData.put("minimumDegreeLevel", jsonArray);
                }
            }

            if (searchGroupData.containsKey("requiredLanguages")) {
                JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(searchGroupData.get("requiredLanguages")));
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (int i = 0 ; i < jsonArray.size() ; i++) {
                        if (JSONUtil.isJsonArray(JSONObject.toJSONString(jsonArray.get(i)))) {
                            JSONArray dataArray = JSONObject.parseArray(JSONObject.toJSONString(jsonArray.get(i)));
                            for (int j = 0 ; j < dataArray.size() ; j++) {
                                EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(dataArray.getJSONObject(j)), EnumDictDTO.class);
                                if (!enumLanguageMap.containsKey(enumDictDTO.getId())) {
                                    continue;
                                }
                                enumDictDTO.setName(enumLanguageMap.get(enumDictDTO.getId()).getName());
                                dataArray.set(j, JSONObject.toJSON(enumDictDTO));
                            }
                            jsonArray.set(i, dataArray);
                        }
                    }
                    searchGroupData.put("requiredLanguages", jsonArray);
                }
            }

            if (searchGroupData.containsKey("preferredLanguages")) {
                JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(searchGroupData.get("preferredLanguages")));
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (int i = 0 ; i < jsonArray.size() ; i++) {
                        if (JSONUtil.isJsonArray(JSONObject.toJSONString(jsonArray.get(i)))) {
                            JSONArray dataArray = JSONObject.parseArray(JSONObject.toJSONString(jsonArray.get(i)));
                            for (int j = 0 ; j < dataArray.size() ; j++) {
                                EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(dataArray.getJSONObject(j)), EnumDictDTO.class);
                                if (!enumLanguageMap.containsKey(enumDictDTO.getId())) {
                                    continue;
                                }
                                enumDictDTO.setName(enumLanguageMap.get(enumDictDTO.getId()).getName());
                                dataArray.set(j, JSONObject.toJSON(enumDictDTO));
                            }
                            jsonArray.set(i, dataArray);
                        }
                    }
                    searchGroupData.put("preferredLanguages", jsonArray);
                }
            }

            if (searchGroupData.containsKey("Rate/Salary")) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(searchGroupData.get("Rate/Salary")));
                if (jsonObject != null) {
                    if (jsonObject.containsKey("currency") && enumCurrencyMap.containsKey(String.valueOf(jsonObject.get("currency")))) {
                        jsonObject.put("currency", enumCurrencyMap.get(String.valueOf(jsonObject.get("currency"))).getName());
                    }
                    searchGroupData.put("Rate/Salary", jsonObject);
                }
            }

            if (searchGroupData.containsKey("degrees")) {
                JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(searchGroupData.get("degrees")));
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (int i = 0 ; i < jsonArray.size() ; i++) {
                        EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(jsonArray.getJSONObject(i)), EnumDictDTO.class);
                        if (!enumDegreeMap.containsKey(enumDictDTO.getId())) {
                            continue;
                        }
                        enumDictDTO.setName(enumDegreeMap.get(enumDictDTO.getId()).getName());
                        jsonArray.set(i, JSONObject.toJSON(enumDictDTO));
                    }
                    searchGroupData.put("degrees", jsonArray);
                }
            }

            if (searchGroupData.containsKey("languages")) {
                JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(searchGroupData.get("languages")));
                if (jsonArray != null && jsonArray.size() > 0) {
                    for (int i = 0 ; i < jsonArray.size() ; i++) {
                        if (JSONUtil.isJsonArray(JSONObject.toJSONString(jsonArray.get(i)))) {
                            JSONArray dataArray = JSONObject.parseArray(JSONObject.toJSONString(jsonArray.get(i)));
                            for (int j = 0 ; j < dataArray.size() ; j++) {
                                EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(dataArray.getJSONObject(j)), EnumDictDTO.class);
                                if (!enumLanguageMap.containsKey(enumDictDTO.getId())) {
                                    continue;
                                }
                                enumDictDTO.setName(enumLanguageMap.get(enumDictDTO.getId()).getName());
                                dataArray.set(j, JSONObject.toJSON(enumDictDTO));
                            }
                            jsonArray.set(i, dataArray);
                        }
                    }
                    searchGroupData.put("languages", jsonArray);
                }
            }

            if (searchGroupData.containsKey("currentSalary")) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(searchGroupData.get("currentSalary")));
                if (jsonObject != null) {
                    if (jsonObject.containsKey("currency") && enumCurrencyMap.containsKey(String.valueOf(jsonObject.get("currency")))) {
                        jsonObject.put("currency", enumCurrencyMap.get(String.valueOf(jsonObject.get("currency"))).getName());
                    }
                    searchGroupData.put("currentSalary", jsonObject);
                }
            }

            if (searchGroupData.containsKey("preferredSalary")) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(searchGroupData.get("preferredSalary")));
                if (jsonObject != null) {
                    if (jsonObject.containsKey("currency") && enumCurrencyMap.containsKey(String.valueOf(jsonObject.get("currency")))) {
                        jsonObject.put("currency", enumCurrencyMap.get(String.valueOf(jsonObject.get("currency"))).getName());
                    }
                    searchGroupData.put("preferredSalary", jsonObject);
                }
            }

            if (searchGroupData.containsKey("workAuthorization")) {
                JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(searchGroupData.get("workAuthorization")));
                if (jsonObject != null) {
                    if (jsonObject.containsKey("value")) {
                        JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(jsonObject.get("value")));
                        for (int i = 0 ; i < jsonArray.size() ; i++) {
                            if (!enumDictDTOMap.containsKey(String.valueOf(jsonArray.get(i)))) {
                                continue;
                            }
                            jsonArray.set(i, enumDictDTOMap.get(String.valueOf(jsonArray.get(i))).getDisplay());
                        }
                        jsonObject.put("value", jsonArray);
                    }

                    if (jsonObject.containsKey("option")) {
                        jsonObject.put("option", enumWorkAuthorizationTreeList);
                    }
                    searchGroupData.put("workAuthorization", jsonObject);
                }
            }


        searchPreference.setSearchGroup(JSONObject.toJSONString(searchGroupData));
    }

    private void updateAdvancedSearchPreferenceData(SearchPreference searchPreference, Map<String, EnumDegree> enumDegreeMap, Map<String, EnumLanguage> enumLanguageMap,
                                                Map<String, EnumCurrency> enumCurrencyMap, List<EnumDictDTO> enumWorkAuthorizationTreeList, Map<String, EnumDictDTO> enumDictDTOMap) {
        JSONArray searchArray = JSONObject.parseArray(searchPreference.getSearchGroup());

        if (searchArray == null || searchArray.size() <= 0) {
            return;
        }

        for (int item = 0 ; item < searchArray.size() ; item++) {
            JSONObject searchGroupData = JSONObject.parseObject(JSONObject.toJSONString(searchArray.getJSONObject(item)));
            JSONArray searchGroupDataArray = null;
            if (searchGroupData.containsKey("and")) {
                searchGroupDataArray = JSONObject.parseArray(JSONObject.toJSONString(searchGroupData.get("and")));
            } else if (searchGroupData.containsKey("or")) {
                searchGroupDataArray = JSONObject.parseArray(JSONObject.toJSONString(searchGroupData.get("or")));
            }
            if (searchGroupDataArray == null || searchGroupDataArray.size() <= 0) {
                return;
            }

                for (int i = 0 ; i < searchGroupDataArray.size() ; i++) {
                    JSONObject dataJson = searchGroupDataArray.getJSONObject(i);
                    if (dataJson.containsKey("value1") && dataJson.containsKey("value")) {
                        if ("minimumDegreeLevel".equals(String.valueOf(dataJson.get("value1")))) {
                            JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(dataJson.get("value")));
                            for (int j = 0 ; j < jsonArray.size() ; j++) {
                                EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(jsonArray.getJSONObject(j)), EnumDictDTO.class);
                                if (!enumDegreeMap.containsKey(enumDictDTO.getId())) {
                                    continue;
                                }
                                enumDictDTO.setName(enumDegreeMap.get(enumDictDTO.getId()).getName());
                                jsonArray.set(j, JSONObject.toJSON(enumDictDTO));
                            }
                            dataJson.put("value", jsonArray);
                        }

                        if ("requiredLanguages".equals(String.valueOf(dataJson.get("value1")))) {
                            JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(dataJson.get("value")));
                            for (int j = 0 ; j < jsonArray.size() ; j++) {
                                JSONArray dataArray = JSONObject.parseArray(JSONObject.toJSONString(jsonArray.get(j)));
                                for (int k = 0 ; k < dataArray.size() ; k++) {
                                    EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(dataArray.getJSONObject(k)), EnumDictDTO.class);
                                    if (!enumLanguageMap.containsKey(enumDictDTO.getId())) {
                                        continue;
                                    }
                                    enumDictDTO.setName(enumLanguageMap.get(enumDictDTO.getId()).getName());
                                    dataArray.set(k, JSONObject.toJSON(enumDictDTO));
                                }
                                jsonArray.set(j, dataArray);
                            }

                            dataJson.put("value", jsonArray);
                        }

                        if ("preferredLanguages".equals(String.valueOf(dataJson.get("value1")))) {
                            JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(dataJson.get("value")));
                            for (int j = 0 ; j < jsonArray.size() ; j++) {
                                JSONArray dataArray = JSONObject.parseArray(JSONObject.toJSONString(jsonArray.get(j)));
                                for (int k = 0 ; k < dataArray.size() ; k++) {
                                    EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(dataArray.getJSONObject(k)), EnumDictDTO.class);
                                    if (!enumLanguageMap.containsKey(enumDictDTO.getId())) {
                                        continue;
                                    }
                                    enumDictDTO.setName(enumLanguageMap.get(enumDictDTO.getId()).getName());
                                    dataArray.set(k, JSONObject.toJSON(enumDictDTO));
                                }
                                jsonArray.set(j, dataArray);
                            }

                            dataJson.put("value", jsonArray);
                        }

                        if ("Rate/Salary".equals(String.valueOf(dataJson.get("value1")))) {
                            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(dataJson.get("value")));
                            if (jsonObject.containsKey("currency") && enumCurrencyMap.containsKey(String.valueOf(jsonObject.get("currency")))) {
                                jsonObject.put("currency", enumCurrencyMap.get(String.valueOf(jsonObject.get("currency"))).getName());
                            }

                            dataJson.put("value", jsonObject);
                        }

                        if ("degrees".equals(String.valueOf(dataJson.get("value1")))) {
                            JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(dataJson.get("value")));
                            for (int j = 0 ; j < jsonArray.size() ; j++) {
                                EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(jsonArray.getJSONObject(j)), EnumDictDTO.class);
                                if (!enumDegreeMap.containsKey(enumDictDTO.getId())) {
                                    continue;
                                }
                                enumDictDTO.setName(enumDegreeMap.get(enumDictDTO.getId()).getName());
                                jsonArray.set(j, JSONObject.toJSON(enumDictDTO));
                            }
                            dataJson.put("value", jsonArray);
                        }

                        if ("languages".equals(String.valueOf(dataJson.get("value1")))) {
                            JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(dataJson.get("value")));
                            for (int j = 0 ; j < jsonArray.size() ; j++) {
                                JSONArray dataArray = JSONObject.parseArray(JSONObject.toJSONString(jsonArray.get(j)));
                                for (int k = 0 ; k < dataArray.size() ; k++) {
                                    EnumDictDTO enumDictDTO = JSONObject.parseObject(JSONObject.toJSONString(dataArray.getJSONObject(k)), EnumDictDTO.class);
                                    if (!enumLanguageMap.containsKey(enumDictDTO.getId())) {
                                        continue;
                                    }
                                    enumDictDTO.setName(enumLanguageMap.get(enumDictDTO.getId()).getName());
                                    dataArray.set(k, JSONObject.toJSON(enumDictDTO));
                                }
                                jsonArray.set(j, dataArray);
                            }

                            dataJson.put("value", jsonArray);
                        }

                        if ("currentSalary".equals(String.valueOf(dataJson.get("value1")))) {
                            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(dataJson.get("value")));
                            if (jsonObject.containsKey("currency") && enumCurrencyMap.containsKey(String.valueOf(jsonObject.get("currency")))) {
                                jsonObject.put("currency", enumCurrencyMap.get(String.valueOf(jsonObject.get("currency"))).getName());
                            }

                            dataJson.put("value", jsonObject);
                        }

                        if ("preferredSalary".equals(String.valueOf(dataJson.get("value1")))) {
                            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(dataJson.get("value")));
                            if (jsonObject.containsKey("currency") && enumCurrencyMap.containsKey(String.valueOf(jsonObject.get("currency")))) {
                                jsonObject.put("currency", enumCurrencyMap.get(String.valueOf(jsonObject.get("currency"))).getName());
                            }

                            dataJson.put("value", jsonObject);
                        }


                        if ("workAuthorization".equals(String.valueOf(dataJson.get("value1")))) {
                            JSONArray jsonArray = JSONObject.parseArray(JSONObject.toJSONString(dataJson.get("value")));
                            for (int j = 0 ; j < jsonArray.size() ; j++) {
                                if (!enumDictDTOMap.containsKey(String.valueOf(jsonArray.get(j)))) {
                                    continue;
                                }
                                jsonArray.set(j, enumDictDTOMap.get(String.valueOf(jsonArray.get(j))).getDisplay());
                            }

                            dataJson.put("value", jsonArray);

                            if (dataJson.containsKey("values")) {
                                dataJson.put("values", enumWorkAuthorizationTreeList);
                            }

                        }

                    }
                    searchGroupDataArray.set(i, dataJson);
                }

            if (searchGroupData.containsKey("and")) {
                searchGroupData.put("and", searchGroupDataArray);
            } else if (searchGroupData.containsKey("or")) {
                searchGroupData.put("or", searchGroupDataArray);
            }

            searchArray.set(item, searchGroupData);
        }

        searchPreference.setSearchGroup(JSONObject.toJSONString(searchArray));
    }
}
