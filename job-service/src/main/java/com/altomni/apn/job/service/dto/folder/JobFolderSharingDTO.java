package com.altomni.apn.job.service.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.job.domain.enumeration.SharingTargetCategory;
import com.altomni.apn.job.service.annotation.StringEnumValidator;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class JobFolderSharingDTO {
    @JsonIgnore
    private Long id;

    @NotNull(message = "targetCategory is required")

//    @StringEnumValidator(
//            enumClass = SharingTargetCategory.class,
//            message = "Invalid SharingTargetCategory value!"
//    )
    private SharingTargetCategory targetCategory;

    @NotNull(message = "targetId is required")
    private Long targetId;

    @ApiModelProperty(value = "The permission of Folder.", allowableValues = "READ, EDIT")
    @NotNull(message = "permission is required")
//    @StringEnumValidator(
//            enumClass = FolderPermission.class,
//            message = "Invalid FolderPermission value!"
//    )
    private FolderPermission permission;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public SharingTargetCategory getTargetCategory() {
        return targetCategory;
    }

    public void setTargetCategory(SharingTargetCategory targetCategory) {
        this.targetCategory = targetCategory;
    }

    public Long getTargetId() {
        return targetId;
    }

    public void setTargetId(Long targetId) {
        this.targetId = targetId;
    }

    public FolderPermission getPermission() {
        return permission;
    }

    public void setPermission(FolderPermission permission) {
        this.permission = permission;
    }
}
