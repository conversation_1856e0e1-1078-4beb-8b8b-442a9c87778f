package com.altomni.apn.job.web.rest.dict;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.aop.cache.CacheControl;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.altomni.apn.common.domain.dict.EnumJobPriority;
import com.altomni.apn.common.domain.dict.EnumWorkAuthorization;
import com.altomni.apn.common.dto.enums.CmnEnumDictDTO;
import com.altomni.apn.common.dto.enums.EnumDictDTO;
import com.altomni.apn.common.dto.enums.JobFunctionsDTO;
import com.altomni.apn.common.enumeration.enums.SortType;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.*;
import com.altomni.apn.common.utils.SecurityUtils;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * REST controller for business dict.
 */
@Api(tags = {"APN-EnumDict"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class EnumDictResource {

    @Resource
    private EnumLanguageService enumLanguageService;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private EnumWorkAuthorizationService enumWorkAuthorizationService;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private EnumDegreeService enumDegreeService;

    @Resource
    private EnumIndustryService enumIndustryService;

    @Resource
    private EnumJobFunctionService enumJobFunctionService;

    @Resource
    private EnumJobPriorityService enumJobPriorityService;

    @Resource
    private EnumDictService enumDictService;

    @Resource
    private EnumLevelOfExperienceService enumLevelOfExperienceService;

    @Resource
    private EnumIndustryMappingService enumIndustryMappingService;

    @Resource
    private EnumJobFunctionMappingService enumJobFunctionMappingService;

    /**
     * GET  /dict : get jobIntention enum data .
     *
     * @return the ResponseEntity with status 200 (OK) and the list of dict in body
     */
    @ApiOperation(value = "Get/Filter request to get motivation enum data . ")
    @GetMapping("/dict/motivation")
    @Timed
    @CacheControl
    public ResponseEntity<List<CmnEnumDictDTO>> getMotivation(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get JobIntention dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumDictService.findJobIntentionEnum(type), HttpStatus.OK);
    }

    /**
     * GET  /dict : get gender enum data .
     *
     * @return the ResponseEntity with status 200 (OK) and the list of dict in body
     */
    @ApiOperation(value = "Get/Filter request to get gender enum data . ")
    @GetMapping("/dict/gender")
    @Timed
    @CacheControl
    public ResponseEntity<List<CmnEnumDictDTO>> getGender(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get gender dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumDictService.findGenderEnum(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get veteran enum data . ")
    @GetMapping("/dict/veteran")
    @Timed
    @CacheControl
    public ResponseEntity<List<CmnEnumDictDTO>> getVeteran(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get veteran dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumDictService.findVeteran(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get preferred pronoun enum data . ")
    @GetMapping("/dict/preferred-pronoun")
    @Timed
    @CacheControl
    public ResponseEntity<List<CmnEnumDictDTO>> getPreferredPronoun(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get preferred pronoun dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumDictService.findPreferredPronoun(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get ethnicity enum data . ")
    @GetMapping("/dict/ethnicity")
    @Timed
    @CacheControl
    public ResponseEntity<List<CmnEnumDictDTO>> getEthnicity(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get ethnicity dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumDictService.findEthnicity(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get common-option enum data . ")
    @GetMapping("/dict/common-option")
    @Timed
    @CacheControl
    public ResponseEntity<List<CmnEnumDictDTO>> getCommonOption(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get common-option dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumDictService.findCommonOption(type), HttpStatus.OK);
    }

    /**
     * GET  /dict : get jobFunctions enum data .
     *
     * @return the ResponseEntity with status 200 (OK) and the list of dict in body
     */
    @ApiOperation(value = "Get/Filter request to get jobFunctions enum data . ")
    @GetMapping("/dict/industry/{id}/jobFunctions")
    @Timed
    @CacheControl
    public ResponseEntity<JobFunctionsDTO> getJobFunctions(@PathVariable(value = "id")Long id, @RequestParam(value = "type") SortType type, @RequestParam(value = "mapping", defaultValue = "false") Boolean mapping) {
        log.info("[APN: DictCode @{}] REST request to get jobFunctions dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumJobFunctionService.getJobFunctions(id, type, mapping), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get jobFunctions mapping enum data . ")
    @GetMapping("/dict/industry/{id}/jobFunctions-mapping")
    @Timed
    @CacheControl
    public ResponseEntity<JobFunctionsDTO> getJobFunctionsMapping(@PathVariable(value = "id")Long id, @RequestParam(value = "type") SortType type, @RequestParam(value = "mapping", defaultValue = "false") Boolean mapping) {
        log.info("[APN: DictCode @{}] REST request to get jobFunctions mapping dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumJobFunctionMappingService.getJobFunctionsMapping(id, type, mapping), HttpStatus.OK);
    }

//    @ApiOperation(value = "Get/Filter request to get jobFunctions enum data for creation. ")
//    @GetMapping("/dict/jobFunctions/creation")
//    @Timed
//    @NoRepeatSubmit
//    public ResponseEntity<List<EnumDictDTO>> getJobFunctionsForCreation(@RequestParam(value = "type") SortType type) {
//        log.info("[APN: DictCode @{}] REST request to get jobFunctions dict data  : ", SecurityUtils.getUserId());
//        return new ResponseEntity<>(enumJobFunctionService.findAllForCreationOrderBySortType(type), HttpStatus.OK);
//
//    }


    /**
     * GET  /dict : get industry enum data .
     *
     * @return the ResponseEntity with status 200 (OK) and the list of dict in body
     */
    @ApiOperation(value = "Get/Filter request to get Industries enum data . ")
    @GetMapping("/dict/industries")
    @Timed
    @CacheControl
    public ResponseEntity<List<EnumDictDTO>> getIndustries(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get industries dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumIndustryService.findAllOrderBySortType(type), HttpStatus.OK);
    }
    @ApiOperation(value = "Get/Filter request to get Industries enum data for creation. ")
    @GetMapping("/dict/industries/creation")
    @Timed
    @CacheControl
    public ResponseEntity<List<EnumDictDTO>> getIndustriesForCreation(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get industries dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumIndustryService.findAllForCreationOrderBySortType(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to transfer industries by ids . ")
    @PostMapping("/dict/industries/transfer-by-ids")
    @Timed
    public ResponseEntity<List<String>> transferIndustriesByIds(@RequestBody List<String> ids) {
        log.info("[APN: DictCode @{}] REST request to transfer industries by ids, ids:{}  : ", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(enumIndustryService.transferIndustriesByIds(ids), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get industries by ids . ")
    @PostMapping("/dict/industries/get-by-ids")
    @Timed
    public ResponseEntity<List<String>> getIndustriesByIds(@RequestBody List<String> ids) {
        log.info("[APN: DictCode @{}] REST request to get industries by ids, ids:{}  : ", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(enumIndustryService.getIndustriesByIds(ids), HttpStatus.OK);
    }

    @PostMapping("/dict/industries/transfer-by-names-to-ids-without-ignore-parent-class")
    public ResponseEntity<List<Long>> transferIndustriesByNamesToIdWithoutIgnoreParentClass(@RequestBody JSONArray names) {
        log.info("({},{}) REST request to transfer industries by names to id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumIndustryService.transferIndustriesByNamesToIdWithoutIgnoreParentClass(names));
    }

    @PostMapping(value = "/dict/industries/transfer-by-names-to-ids")
    public ResponseEntity<List<Long>> transferIndustriesByNamesToId(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer industries by names to id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumIndustryService.transferIndustriesByNamesToId(names));
    }

    @PostMapping("/dict/industries/ui-name")
    public ResponseEntity<List<String>> getIndustriesUINameByIds(@RequestBody String ids) {
        log.info("({},{}) REST request to get industries UI name by dict codes: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), ids);
        return ResponseEntity.ok(enumIndustryService.getIndustriesUINameByIds(ids));
    }

    @PostMapping("/dict/industries/transfer-by-names")
    public ResponseEntity<List<String>> transferIndustriesByNames(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer industries by names: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumIndustryService.transferIndustriesByNames(names));
    }

    /**
     * GET  /dict : get workAuthorization enum data .
     *
     * @return the ResponseEntity with status 200 (OK) and the list of dict in body
     */
    @ApiOperation(value = "Get/Filter request to get workAuthorization enum data . ")
    @GetMapping("/dict/workAuthorization")
    @Timed
    @CacheControl
    public ResponseEntity<List<EnumDictDTO>> getWorkAuthorization(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get workAuthorization dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumWorkAuthorizationService.findAllOrderBySortType(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to transfer workAuthorization by ids . ")
    @PostMapping("/dict/workAuthorization/transfer-by-ids")
    @Timed
    public ResponseEntity<List<String>> transferWorkAuthorizationByIds(@RequestBody List<String> ids) {
        log.info("[APN: DictCode @{}] REST request to transfer workAuthorization by ids, ids:{}  : ", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(enumWorkAuthorizationService.transferWorkAuthorizationByIds(ids), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get workAuthorization by ids . ")
    @PostMapping("/dict/workAuthorization/get-by-ids")
    @Timed
    public ResponseEntity<List<String>> getWorkAuthorizationByIds(@RequestBody List<String> ids) {
        log.info("[APN: DictCode @{}] REST request to get workAuthorization by ids, ids:{}  : ", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(enumWorkAuthorizationService.getWorkAuthorizationByIds(ids), HttpStatus.OK);
    }

    @PostMapping("/dict/workAuthorization/transfer-by-names-to-ids-without-ignore-parent-class")
    public ResponseEntity<List<Long>> transferWorkAuthorizationByNamesToIdWithoutIgnoreParentClass(@RequestBody JSONArray names) {
        log.info("({},{}) REST request to transfer workAuthorization by names to id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumWorkAuthorizationService.transferWorkAuthorizationByNamesToIdWithoutIgnoreParentClass(names));
    }

    @PostMapping(value = "/jobs/dict/workAuthorization/transfer-by-names-to-ids")
    public ResponseEntity<List<Long>> transferWorkAuthorizationByNamesToId(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer workAuthorization by names to id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumWorkAuthorizationService.transferWorkAuthorizationByNamesToId(names));
    }

    @PostMapping("/dict/workAuthorization/transfer-by-names")
    public ResponseEntity<List<String>> transferWorkAuthorizationByNames(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer workAuthorization by names: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumWorkAuthorizationService.transferWorkAuthorizationByNames(names));
    }

    @PostMapping("/dict/workAuthorization/name")
    public ResponseEntity<String> getUINameByName(@RequestBody String name) {
        log.info("({},{}) REST request to get UI name by name: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), name);
        return ResponseEntity.ok(enumWorkAuthorizationService.getUINameByName(name));
    }

    @PostMapping("/dict/workAuthorization/names")
    public ResponseEntity<Map<String, String>> getUINameByNames(@RequestBody List<String> names) {
        log.info("({},{}) REST request to get UI name by name: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumWorkAuthorizationService.getUINameByNames(names));
    }

    @PostMapping("/dict/workAuthorization/id")
    public ResponseEntity<JSONObject> getUINameByName(@RequestBody List<EnumWorkAuthorization> list) {
        log.info("({},{}) REST request to get UI name by name: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), list);
        return ResponseEntity.ok(enumWorkAuthorizationService.getUINameByName(list));
    }

    /**
     * GET  /dict : get degrees enum data .
     *
     * @return the ResponseEntity with status 200 (OK) and the list of dict in body
     */
    @ApiOperation(value = "Get/Filter request to get degrees enum data . ")
    @GetMapping("/dict/degrees")
    @Timed
    @CacheControl
    public ResponseEntity<List<EnumDictDTO>> getDegrees(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get degrees dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumDegreeService.findAllOrderBySortType(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to transfer degrees by ids . ")
    @PostMapping("/dict/degrees/transfer-by-ids")
    @Timed
    public ResponseEntity<List<String>> transferDegreesByIds(@RequestBody List<String> ids) {
        log.info("[APN: DictCode @{}] REST request to transfer degrees by ids, ids:{}  : ", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(enumDegreeService.transferDegreesByIds(ids), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get degrees by ids . ")
    @PostMapping("/dict/degrees/get-by-ids")
    @Timed
    public ResponseEntity<List<String>> getDegreesByIds(@RequestBody List<String> ids) {
        log.info("[APN: DictCode @{}] REST request to get degrees by ids, ids:{}  : ", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(enumDegreeService.getDegreesByIds(ids), HttpStatus.OK);
    }

    @PostMapping("/dict/degrees/transfer-by-names-to-ids-without-ignore-parent-class")
    public ResponseEntity<List<Long>> transferDegreesByNamesToIdWithoutIgnoreParentClass(@RequestBody JSONArray names) {
        log.info("({},{}) REST request to transfer degrees by names to id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumDegreeService.transferDegreesByNamesToIdWithoutIgnoreParentClass(names));
    }

    @PostMapping("/dict/degrees/transfer-by-names-to-ids")
    public ResponseEntity<List<Long>> transferDegreesByNamesToId(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer degrees by names to id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumDegreeService.transferDegreesByNamesToId(names));
    }

    @PostMapping("/dict/degrees/transfer-by-names-to-list")
    public ResponseEntity<List<EnumDegree>> transferDegreesByNamesToList(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer by names to ids: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumDegreeService.transferDegreesByNamesToList(names));
    }

    @PostMapping("/dict/degrees/transfer-by-names")
    public ResponseEntity<List<String>> transferDegreesByNames(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer degrees by names: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumDegreeService.transferDegreesByNames(names));
    }

    @PostMapping("/dict/degrees/transfer-by-names-to-map")
    public ResponseEntity<Map<String, List<String>>> transferDegreesByNamesToMap(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer degrees by names: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumDegreeService.transferDegreesByNamesToMap(names));
    }

    @PostMapping("/dict/degrees/findAllEnumDegree")
    public ResponseEntity<List<EnumDegree>> findAllEnumDegree() {
        log.info("({},{}) REST request to get findAllEnumDegree", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        return ResponseEntity.ok(enumCommonService.findAllEnumDegree());
    }

    /**
     * GET  /dict : get languages enum data .
     *
     * @return the ResponseEntity with status 200 (OK) and the list of dict in body
     */
    @ApiOperation(value = "Get/Filter request to get languages enum data . ")
    @GetMapping("/dict/languages")
    @Timed
    @CacheControl
    public ResponseEntity<List<EnumDictDTO>> getLanguages(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get languages dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumLanguageService.findAllOrderBySortType(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to transfer languages by ids . ")
    @PostMapping("/dict/languages/transfer-by-ids")
    @Timed
    public ResponseEntity<List<String>> transferLanguagesByIds(@RequestBody List<String> ids) {
        log.info("[APN: DictCode @{}] REST request to transfer languages by ids, ids:{}  : ", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(enumLanguageService.transferLanguagesByIds(ids), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get languages by ids . ")
    @PostMapping("/dict/languages/get-by-ids")
    @Timed
    public ResponseEntity<List<String>> getLanguagesByIds(@RequestBody List<String> ids) {
        log.info("[APN: DictCode @{}] REST request to get languages by ids, ids:{}  : ", SecurityUtils.getUserId(), ids);
        return new ResponseEntity<>(enumLanguageService.getLanguagesByIds(ids), HttpStatus.OK);
    }

    @PostMapping("/dict/languages/transfer-by-names-to-ids-without-ignore-parent-class")
    public ResponseEntity<List<Long>> transferLanguagesByNamesToIdWithoutIgnoreParentClass(@RequestBody JSONArray names) {
        log.info("({},{}) REST request to transfer languages by names to id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumLanguageService.transferLanguagesByNamesToIdWithoutIgnoreParentClass(names));
    }

    @PostMapping(value = "/dict/languages/transfer-by-names-to-ids")
    public ResponseEntity<List<Long>> transferLanguagesByNamesToId(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer languages by names to id: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumLanguageService.transferLanguagesByNamesToId(names));
    }

    @PostMapping("/dict/languages/transfer-by-names")
    public ResponseEntity<List<String>> transferLanguagesByNames(@RequestBody List<String> names) {
        log.info("({},{}) REST request to transfer languages by names: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), names);
        return ResponseEntity.ok(enumLanguageService.transferLanguagesByNames(names));
    }

    @PostMapping("/dict/languages/ui-name")
    public ResponseEntity<List<String>> getLanguagesUINameByIds(@RequestBody String ids) {
        log.info("({},{}) REST request to get languages UI name by dict codes: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), ids);
        return ResponseEntity.ok(enumLanguageService.getLanguagesUINameByIds(ids));
    }

    @GetMapping("/dict/currency/all")
    @CacheControl
    public ResponseEntity<List<EnumCurrency>> findAllEnumCurrency() {
        log.info("({},{}) REST request to get findAllEnumCurrency", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        return ResponseEntity.ok(enumCurrencyService.findAllEnumCurrency());
    }

    @GetMapping("/dict/currency/info/id/{id}")
    public ResponseEntity<EnumCurrency> findEnumCurrencyById(@PathVariable("id") Integer id) {
        log.info("({},{}) REST request to get findEnumCurrencyById id = [{}]", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), id);
        return ResponseEntity.ok(enumCurrencyService.findEnumCurrencyById(id));
    }

    @GetMapping("/dict/currency/info/name/{name}")
    public ResponseEntity<EnumCurrency> findEnumCurrencyByName(@PathVariable("name") String name) {
        log.info("({},{}) REST request to get findEnumCurrencyByName name = [{}]", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), name);
        return ResponseEntity.ok(enumCurrencyService.findEnumCurrencyByName(name));
    }

    /**
     * GET  /dict : get job priority enum data .
     *
     * @return the ResponseEntity with status 200 (OK) and the list of dict in body
     */
    @ApiOperation(value = "Get/Filter request to get job priority enum data . ")
    @GetMapping("/dict/job-priorities")
    @Timed
    @CacheControl
    public ResponseEntity<List<EnumJobPriority>> getJobPriorities() {
        log.info("[APN: DictCode @{}] REST request to get job priority dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumJobPriorityService.findAllEnumJobPriority(), HttpStatus.OK);
    }


    @PutMapping("/dict/languages/{type}")
    @Timed
    @CacheControl
    public ResponseEntity setLanguages(@PathVariable(value = "type") String language) {
        log.info("[APN: set languages @{}] REST request to set languages by type, type:{}  : ", SecurityUtils.getUserId(), language);
        if(StringUtils.isBlank(language)){
            return ResponseEntity.badRequest().body("Missing required parameter");
        }
        enumLanguageService.setLanguages(language);
        return ResponseEntity.status(HttpStatus.NO_CONTENT).build();
    }


    @ApiOperation(value = "Get/Filter request to get level of experience enum data . ")
    @GetMapping("/dict/level-of-experience")
    @Timed
    @CacheControl
    public ResponseEntity<List<CmnEnumDictDTO>> getLevelOfExperience(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get degrees dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumLevelOfExperienceService.findAllOrderBySortType(type), HttpStatus.OK);
    }

    @ApiOperation(value = "Get/Filter request to get Industries Mapping enum data . ")
    @GetMapping("/dict/industries-mapping")
    @Timed
    @CacheControl
    public ResponseEntity<List<EnumDictDTO>> getIndustriesMapping(@RequestParam(value = "type") SortType type) {
        log.info("[APN: DictCode @{}] REST request to get industries dict data  : ", SecurityUtils.getUserId());
        return new ResponseEntity<>(enumIndustryMappingService.findAllOrderBySortType(type), HttpStatus.OK);
    }



}
