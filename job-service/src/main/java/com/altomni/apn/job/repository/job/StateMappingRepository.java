package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.StateMapping;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface StateMappingRepository extends JpaRepository<StateMapping,Long> {

    StateMapping findOneByNameOrAbbreviation(String name, String abbr);
}
