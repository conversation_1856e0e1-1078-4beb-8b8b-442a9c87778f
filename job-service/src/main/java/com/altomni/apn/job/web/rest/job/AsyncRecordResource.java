package com.altomni.apn.job.web.rest.job;

import com.altomni.apn.job.domain.async.AsyncRecord;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;
import com.altomni.apn.job.repository.async.AsyncRecordRepository;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Api(tags = {"Job", "AsyncRecord"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class AsyncRecordResource {

    @Resource
    private AsyncRecordRepository asyncRecordRepository;

    @PostMapping("/asyncRecord/save")
    public ResponseEntity<AsyncRecord> save(@RequestParam("asyncRecord") AsyncRecord asyncRecord) {
//        log.info("[APN: AsyncRecord @{}] REST request to save : {}", SecurityUtils.getUserId(), asyncRecord);
        return ResponseEntity.ok(asyncRecordRepository.saveAndFlush(asyncRecord));
    }

    @PostMapping("/asyncRecord/findAllByAsyncTypeAndDataTypeAndStatus")
    public ResponseEntity<List<AsyncRecord>> findAllByAsyncTypeAndDataTypeAndStatus(@RequestParam("asyncType") Integer asyncType,
                                                                                    @RequestParam("dataType") Integer dataType,
                                                                                    @RequestParam("status") Integer status) {
        log.info("[APN: AsyncRecord @{}] REST request to findAllByAsyncTypeAndDataTypeAndStatus : {}, {}, {}", SecurityUtils.getUserId(), asyncType, dataType, status);
        return ResponseEntity.ok(asyncRecordRepository.findAllByAsyncTypeAndDataTypeAndStatus(AsyncEnum.fromDbValue(asyncType), AsyncEnum.fromDbValue(dataType), Status.fromDbValue(status)));
    }
}
