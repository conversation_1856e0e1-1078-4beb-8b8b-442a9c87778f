package com.altomni.apn.job.service.xxljob.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobApnParamDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateBySendTimeForJobAdminDTO;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.repository.xxljob.XxlJobRelationRepository;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.TenantConfigUtil;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.service.job.JobRelationService;
import com.altomni.apn.job.service.user.UserService;
import com.altomni.apn.job.service.xxljob.XxlJobClientService;
import com.altomni.apn.job.service.xxljob.XxlJobService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.TenantMessageConfigConstants.POSITION_DEFAULT_REMINDER_DAY;
import static com.altomni.apn.common.constants.TenantMessageConfigConstants.POSITION_REMINDER_DAYS_DEFAULT_SEND_TIME;

@Slf4j
@Service("xxlJobForJobService")
public class XxlJobServiceImpl implements XxlJobService {

    @Resource
    private UserService userService;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private JobRelationService jobRelationService;

    @Resource
    private XxlJobClientService xxlJobClientService;

    @Resource
    private XxlJobRelationRepository xxlJobRelationRepository;

    /**
     * 职位的提醒配置字段
     */
    List<TenantMessageMinderConfigFieldCodeEnum> reminderForJobTypeList = CollUtil.newArrayList(
            TenantMessageMinderConfigFieldCodeEnum.POSITION_UNSUBMITTED_CANDIDATE_DAYS,
            TenantMessageMinderConfigFieldCodeEnum.POSITION_UNINTERVIEWED_DAYS);

    /**
     * 创建职位后，添加职位的提醒任务
     * @param jobId
     */
    @Override
    public void addCandidateReminderForJob(Long jobId) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            Integer jobType = jobRepository.findJobTypeByJobId(jobId);
            if (jobType.equals(JobType.PAY_ROLL.toDbValue())) {
                return;
            }
            JobV3 job = jobRepository.findById(jobId).orElseThrow(() -> new NotFoundException("job is not fount"));
            TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
            Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
            reminderForJobTypeList.parallelStream().forEach(type -> {
                SecurityContextHolder.setContext(context);
                log.info("addReminderForJob is start, jobId = {}, type = {}", jobId, type);
                int reminderDays = Integer.parseInt(fieldToValueMap.getOrDefault(type.toDbValue(), POSITION_DEFAULT_REMINDER_DAY));
                addReminderForJob(job, reminderDays, type);
                log.info("addReminderForJob is success, jobId = {}, type = {}", jobId, type);
            });
        });
    }


    /**
     * assignedUser 发生变化则修改提醒任务, 时间都以assignedUser为准
     * @param jobId
     */
    @Override
    public void updateCandidateReminderForJob(Long jobId) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            Integer jobType = jobRepository.findJobTypeByJobId(jobId);
            if (jobType.equals(JobType.PAY_ROLL.toDbValue())) {
                return;
            }
            JobV3 job = jobRepository.findById(jobId).orElseThrow(() -> new NotFoundException("job is not fount"));
            //检查 assignedUser是否发生了变化
            List<AssignedUserDTO> assignedUserDTOList = jobRelationService.getJobAssignedUserDTOList(jobId);
            TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
            Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
            reminderForJobTypeList.parallelStream().forEach(type -> {
                SecurityContextHolder.setContext(context);
                log.info("updateCandidateReminderForJob is start, jobId = {}, type = {}", jobId, type);
                //查询出已经添加的任务
                List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceId(type.getXxlJobType(), jobId);
                if (!Objects.equals(JobStatus.OPEN, job.getStatus())) {
                    //delete
                    if (CollUtil.isNotEmpty(xxlJobRelationList)) {
                        xxlJobClientService.deleteXxlJobIdList(xxlJobRelationList.stream().map(XxlJobRelation::getXxlJobId).collect(Collectors.toList()));
                    }
                    log.info("updateCandidateReminderForJob is success, jobId = {}, status = {}, type = {}", jobId, job.getStatus(), type);
                    return;
                }
                //new userId
                List<Long> newUserIdList = assignedUserDTOList.stream().map(AssignedUserDTO::getUserId).distinct().sorted().collect(Collectors.toList());
                List<Long> oldUserIdList = xxlJobRelationList.stream().map(XxlJobRelation::getUserId).distinct().sorted().collect(Collectors.toList());
                if (Objects.equals(JSONUtil.toJsonStr(newUserIdList), JSONUtil.toJsonStr(oldUserIdList))) {
                    // assignedUser没有发生变化
                    log.info("updateCandidateReminderForJob is success, no changed, jobId = {}", jobId);
                    return;
                }
                //发生了变化的情况
                List<Long> addUserIdList = newUserIdList.stream().filter(userId -> !oldUserIdList.contains(userId)).collect(Collectors.toList());
                List<Long> deleteUserIdList = oldUserIdList.stream().filter(userId -> !newUserIdList.contains(userId)).collect(Collectors.toList());
                //delete xxl job
                if (CollUtil.isNotEmpty(deleteUserIdList)) {
                    Map<Long, XxlJobRelation> xxlJobMap = xxlJobRelationList.stream().collect(Collectors.toMap(XxlJobRelation::getUserId, a -> a));
                    List<Integer> xxlJobIdList = xxlJobMap.values().stream()
                            .filter(xxlJobRelation -> deleteUserIdList.contains(xxlJobRelation.getUserId()))
                            .map(XxlJobRelation::getXxlJobId).collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(xxlJobIdList)) {
                        xxlJobClientService.deleteXxlJobIdList(xxlJobIdList);
                    }
                }
                //add xxl job
                if (CollUtil.isNotEmpty(addUserIdList)) {
                    List<UserTimeZoneVO> userTimeZoneVoList = userService.getTimezoneListByUserIdList(addUserIdList).getBody();
                    if (CollUtil.isEmpty(userTimeZoneVoList)) {
                        return;
                    }
                    int reminderDays = Integer.parseInt(fieldToValueMap.getOrDefault(type.toDbValue(), POSITION_DEFAULT_REMINDER_DAY));
                    addReminderForXxlJob(userTimeZoneVoList, job, reminderDays, type);
                }
                log.info("updateCandidateReminderForJob is success, jobId = {}", jobId);
            });

        });
    }

    /**
     * 修改job的转态影响提醒，open开启提醒，其他状态关闭提醒
     * @param status
     * @param jobIdList
     */
    @Override
    public void updateReminderByJobStatusAndIds(JobStatus status, List<Long> jobIdList) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
            Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
            List<JobV3> jobV3List = jobRepository.findAllByIdIn(jobIdList);
            reminderForJobTypeList.parallelStream().forEach(type -> {
                SecurityContextHolder.setContext(context);
                log.info("updateCandidateReminderForJobStatusAndIds is start, jobIdList = {}, type = {}", jobIdList, type);
                if (Objects.equals(status, JobStatus.OPEN)) {
                    //增加xxl-job
                    int reminderDays = Integer.parseInt(fieldToValueMap.getOrDefault(type.toDbValue(), POSITION_DEFAULT_REMINDER_DAY));
                    jobV3List.forEach(jobV3 -> addReminderForJob(jobV3, reminderDays, type));
                } else {
                    //删除xxl-job
                    List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceIdIn(type.getXxlJobType(), jobIdList);
                    if (CollUtil.isNotEmpty(xxlJobRelationList)) {
                        xxlJobClientService.deleteXxlJobIdList(xxlJobRelationList.stream().map(XxlJobRelation::getXxlJobId).collect(Collectors.toList()));
                    }
                }
                log.info("updateCandidateReminderForJobStatusAndIds is success, jobIdList = {}, type = {}", jobIdList, type);
            });
        });
    }

    /**
     * submitToJob or interview 这个2个节点创建的时候都会进入这个方法
     * @param jobId
     * @param kpiUsers
     */
    @Override
    public void updateReminderByApplicationDTO(Long jobId, List<TalentRecruitmentProcessKpiUserVO> kpiUsers, TenantMessageMinderConfigFieldCodeEnum type) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            log.info("updateCandidateReminderXxlJobForJobByApplicationDTO is start, jobId = {}", jobId);
            SecurityContextHolder.setContext(context);
            Integer jobType = jobRepository.findJobTypeByJobId(jobId);
            if (jobType.equals(JobType.PAY_ROLL.toDbValue())) {
                return;
            }
            Set<Long> userIdSet = kpiUsers.stream().filter(vo -> !Objects.equals(vo.getUserRole(), UserRole.OWNER))
                    .map(TalentRecruitmentProcessKpiUserVO::getUserId).collect(Collectors.toSet());
            //可能出现创建者是 assignedUser, 但是没有添加自己进入流程参与者中
            userIdSet.add(SecurityUtils.getUserId());
            List<AssignedUserDTO> assignedUserDTOList = jobRelationService.getJobAssignedUserDTOList(jobId);
            Set<Long> assignUserIdSet = assignedUserDTOList.stream().map(AssignedUserDTO::getUserId).collect(Collectors.toSet());
            //过滤掉非 assignedUser 的参与者
            List<Long> userIdList = userIdSet.stream().filter(assignUserIdSet::contains).collect(Collectors.toList());
            List<UserTimeZoneVO> userTimeZoneVoList = userService.getTimezoneListByUserIdList(userIdList).getBody();
            if (CollUtil.isNotEmpty(userTimeZoneVoList)) {
                int reminderDays = Objects.equals(type, TenantMessageMinderConfigFieldCodeEnum.POSITION_UNSUBMITTED_CANDIDATE_DAYS)? getUnSubmitCandidateDays(): getUnInterviewCandidateDays();
                List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceId(type.getXxlJobType(), jobId);
                Map<Long, Integer> userIdXxlJobIdMap = new HashMap<>(16);
                if (CollUtil.isNotEmpty(xxlJobRelationList)) {
                    userIdXxlJobIdMap = xxlJobRelationList.stream().collect(Collectors.toMap(XxlJobRelation::getUserId, XxlJobRelation::getXxlJobId));
                }
                Instant submitToJobTime = Instant.now();
                Map<Long, Integer> finalUserIdXxlJobIdMap = userIdXxlJobIdMap;
                jobRepository.findById(jobId).ifPresent(jobV3 -> {
                    List<XxlJobUpdateBySendTimeForJobAdminDTO> jobAdminDTOList = new ArrayList<>();
                    List<XxlJobApnDTO> addXxlJobApnDTOList = new ArrayList<>();
                    userTimeZoneVoList.forEach(userTimeZoneVO -> {
                        log.info("UpdateReminderForXxlJob for xxl job, jobId = {}, time = {}, userTimeZoneVO = {}", jobV3.getId(), reminderDays, userTimeZoneVO);
                        if (StrUtil.isBlank(userTimeZoneVO.getCustomTimezone())) {
                            return;
                        }
                        if (finalUserIdXxlJobIdMap.get(userTimeZoneVO.getUserId()) != null) {
                            XxlJobUpdateBySendTimeForJobAdminDTO adminDto = new XxlJobUpdateBySendTimeForJobAdminDTO();
                            Instant triggerTimeInstant = getTriggerTime(reminderDays, submitToJobTime, userTimeZoneVO.getCustomTimezone());
                            adminDto.setSendTime(triggerTimeInstant);
                            adminDto.setXxlJobId(finalUserIdXxlJobIdMap.get(userTimeZoneVO.getUserId()));
                            adminDto.setReminderConfig(reminderDays + "");
                            adminDto.setCron(DateUtil.getCron(triggerTimeInstant));
                            adminDto.setTimezone(userTimeZoneVO.getCustomTimezone());
                            jobAdminDTOList.add(adminDto);
                            return;
                        }
                        addXxlJobApnDTOList.add(getXxlJobParam(userTimeZoneVO, jobV3, reminderDays, submitToJobTime, type));
                    });
                    if (CollUtil.isNotEmpty(jobAdminDTOList)) {
                        xxlJobClientService.updateJobsBySendTime(jobAdminDTOList);
                    }
                    if (CollUtil.isNotEmpty(addXxlJobApnDTOList)) {
                        xxlJobClientService.createXxlJobS(addXxlJobApnDTOList);
                    }
                });
            }
            log.info("updateCandidateReminderXxlJobForJobByApplicationDTO is success, jobId = {}, userIdList = {}", jobId, userIdList);
        });
    }

    private void addReminderForJob(JobV3 job, Integer reminderDays, TenantMessageMinderConfigFieldCodeEnum type) {
        log.info("reminderForJob jobId = {}, time = {}, type = {}", job.getId(), reminderDays, type);
        List<AssignedUserDTO> assignedUserDTOList = jobRelationService.getJobAssignedUserDTOList(job.getId());
        List<Long> userIdList = assignedUserDTOList.stream().map(AssignedUserDTO::getUserId).distinct().collect(Collectors.toList());
        List<UserTimeZoneVO> userTimeZoneVoList = userService.getTimezoneListByUserIdList(userIdList).getBody();
        if (CollUtil.isEmpty(userTimeZoneVoList)) {
            return;
        }
        addReminderForXxlJob(userTimeZoneVoList, job, reminderDays, type);
    }

    private int getUnSubmitCandidateDays() {
        TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
        return Integer.parseInt(fieldToValueMap.getOrDefault(TenantMessageMinderConfigFieldCodeEnum.POSITION_UNSUBMITTED_CANDIDATE_DAYS.toDbValue(), POSITION_DEFAULT_REMINDER_DAY));
    }

    private int getUnInterviewCandidateDays() {
        TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
        return Integer.parseInt(fieldToValueMap.getOrDefault(TenantMessageMinderConfigFieldCodeEnum.POSITION_UNINTERVIEWED_DAYS.toDbValue(), POSITION_DEFAULT_REMINDER_DAY));
    }

    private void addReminderForXxlJob(List<UserTimeZoneVO> userTimeZoneVoList, JobV3 job, int reminderDays, TenantMessageMinderConfigFieldCodeEnum type) {
        List<XxlJobApnDTO> xxlJobApnDTOList = new ArrayList<>();
        userTimeZoneVoList.forEach(userTimeZoneVO -> {
            log.info("addReminderForJob for xxl job, jobId = {}, time = {}, userTimeZoneVO = {}", job.getId(), reminderDays, userTimeZoneVO);
            //时间节点都是以重新加入或者开启的时间,即当时时间
            if (StrUtil.isBlank(userTimeZoneVO.getCustomTimezone())) {
                return;
            }
            XxlJobApnDTO xxlJobApnDTO = getXxlJobParam(userTimeZoneVO, job, reminderDays, Instant.now(), type);
            xxlJobApnDTOList.add(xxlJobApnDTO);
            log.info("addReminderForJob is success, update xxl-job-relation, jobId = {}", job.getId());
        });
        xxlJobClientService.createXxlJobS(xxlJobApnDTOList);
    }

    private XxlJobApnDTO getXxlJobParam(UserTimeZoneVO userTimeZoneVO, JobV3 job, int reminderDays, Instant now, TenantMessageMinderConfigFieldCodeEnum type) {
        XxlJobApnDTO xxlJobApnDTO = new XxlJobApnDTO();
        XxlJobRelationTypeEnum xxlJobRelationTypeEnum = type.getXxlJobType();
        xxlJobApnDTO.setJobDesc(StrUtil.format(xxlJobRelationTypeEnum.getCnMessage(), job.getTitle(), reminderDays) + "#userId=" + userTimeZoneVO.getUserId() + "#jobId=" + job.getId());
        String timezone = userTimeZoneVO.getCustomTimezone();
        //get triggerTime
        Instant triggerTimeInstant = getTriggerTime(reminderDays, now, timezone);
        XxlJobApnParamDTO xxlJobApnParamDTO = new XxlJobApnParamDTO();
        xxlJobApnParamDTO.setXxlJobType(xxlJobRelationTypeEnum);
        xxlJobApnParamDTO.setReferenceId(job.getId());
        xxlJobApnParamDTO.setUserId(userTimeZoneVO.getUserId());
        xxlJobApnParamDTO.setTenantId(SecurityUtils.getTenantId());
        xxlJobApnParamDTO.setTimezone(timezone);
        xxlJobApnParamDTO.setSendTime(triggerTimeInstant);
        xxlJobApnParamDTO.setReminderConfig(reminderDays + "");
        xxlJobApnParamDTO.setToken(SecurityUtils.getCurrentUserToken());
        xxlJobApnDTO.setXxlJobApnParamDTO(xxlJobApnParamDTO);
        Map<String, Object> paramMap = new JSONObject(xxlJobApnParamDTO);
        paramMap.put("cnTitle", StrUtil.format(xxlJobRelationTypeEnum.getCnMessage(), job.getTitle(), reminderDays));
        paramMap.put("enTitle", StrUtil.format(xxlJobRelationTypeEnum.getEnMessage(), job.getTitle(), reminderDays));
        paramMap.put("jobId", job.getId());
        xxlJobApnParamDTO.setXxlJobParam(paramMap);
        return xxlJobApnDTO;
    }

    private Instant getTriggerTime(int unSubmitCandidateDays, Instant createdDate, String timezone) {
        // 获取对应时区的时间
        ZoneId zoneId = ZoneId.of(timezone);
        // 将Instant转换为ZonedDateTime，使用指定的时区
        ZonedDateTime createdDateTime = createdDate.atZone(zoneId);
        createdDateTime = createdDateTime.plusDays(unSubmitCandidateDays).withHour(POSITION_REMINDER_DAYS_DEFAULT_SEND_TIME).withMinute(0).withSecond(0);
        Instant result = createdDateTime.toInstant();
        Instant now = Instant.now();
        while (result.isBefore(now)) {
            result = result.plus(unSubmitCandidateDays, ChronoUnit.DAYS);
        }
        return result;
    }

}
