package com.altomni.apn.job.service.job;

import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.job.service.dto.job.ColumnPreferenceDTO;

public interface ColumnPreferenceService {

    ColumnPreferenceDTO findOne(Long userId, ModuleType moduleType);

    void create(ColumnPreferenceDTO ColumnPreference);

    void updateByUserId(Long userId, Object type, ModuleType moduleType);

}
