package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.dto.talent.TalentContactDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.job.UserJobRelation;
import com.altomni.apn.job.repository.job.UserJobRelationRepository;
import com.altomni.apn.job.service.job.JobRelationService;
import com.altomni.apn.job.service.user.UserService;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class JobRelationServiceImpl implements JobRelationService {
    @Resource
    private UserJobRelationRepository userJobRelationRepository;

    @Resource
    private UserService userService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;


    @Override
    @Transactional
    public void saveJobAssignedUsers(List<AssignedUserDTO> assignedUsers, Long jobId, Boolean isCreated) {
        log.info("save job assigned User for job {}, with action: {}", jobId, isCreated ? "creating" : "update");
        //if no assignedUsers, delete all when update
        if (CollectionUtils.isEmpty(assignedUsers)) { //user update assigned users to empty list
            if (!isCreated){ //update
                //List<Long> participantIdsByJobId = userJobRelationRepository.getParticipantIdsByJobId(jobId);
                List<UserJobRelation> existingRelations = userJobRelationRepository.findAllByJobId(jobId);
                List<Long> ids = existingRelations.stream().map(UserJobRelation::getId).toList();
                userJobRelationRepository.disableNativeByIds(ids);
                existingRelations.forEach(userId -> cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userId.getUserId()));
            }
            return;
        }

        List<AssignedUserDTO> distinctAssignedUsers = assignedUsers.stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(AssignedUserDTO::getUserId))),
                        ArrayList::new
                ));
        List<Long> userIds = distinctAssignedUsers.stream().map(AssignedUserDTO::getUserId).collect(Collectors.toList());
        List<UserBriefDTO> userList = userService.getAllBriefUsersByIds(userIds).getBody();

        if (userList == null || userList.isEmpty() || userList.size() != userIds.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBRELATION_SAVEJOBASSIGNEDUSERS_USERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        HashMap<Long, UserBriefDTO> userSet = userList.stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity(), (user1, user2) -> user2, HashMap::new));

        // Fetch existing relations
        List<UserJobRelation> existingRelations = isCreated ? new ArrayList<>() : userJobRelationRepository.findAllByJobId(jobId);

        // Prepare new relations
        List<UserJobRelation> newRelations = distinctAssignedUsers.stream().map(s -> {
            if (!userSet.containsKey(s.getUserId()) || !userSet.get(s.getUserId()).getTenantId().equals(SecurityUtils.getTenantId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBRELATION_SAVEJOBASSIGNEDUSERS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
            UserJobRelation userJobRelation = new UserJobRelation();
            userJobRelation.setUserId(s.getUserId());
            userJobRelation.setJobId(jobId);
            return userJobRelation;
        }).collect(Collectors.toList());

//        // Delete relations that are no longer needed only when update
//        if (!isCreated) {
//            List<UserJobRelation> toDelete = existingRelations.stream()
//                    .filter(existing -> newRelations.stream()
//                            .noneMatch(newRel -> newRel.getUserId().equals(existing.getUserId()) && newRel.getJobId().equals(existing.getJobId())))
//                    .map(r -> r.status(false))
//                    .collect(Collectors.toList());
//            userJobRelationRepository.deleteAll(toDelete);
////            userJobRelationRepository.saveAll(toDelete);
//            toDelete.forEach(userJob -> cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userJob.getUserId()));
//        }
//        // Add relations that did not exist before
//        List<UserJobRelation> toAdd = newRelations.stream()
//                .filter(newRel -> existingRelations.stream().noneMatch(existing -> existing.getUserId().equals(newRel.getUserId())))
//                .collect(Collectors.toList());
//        userJobRelationRepository.saveAll(toAdd);
//        toAdd.forEach(userJob -> cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userJob.getUserId()));

        // new merge logic here
        List<UserJobRelation> update2Active = existingRelations.stream().filter(r -> existingUserJobRelationRecord(newRelations, r)).collect(Collectors.toList());
        List<Long> update2ActiveIds = new ArrayList<>();
        update2Active.forEach(r -> {
            //r.setStatus(true);
            update2ActiveIds.add(r.getId());
            cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(r.getUserId());
        });
        //userJobRelationRepository.saveAll(update2Active);
        if (!update2ActiveIds.isEmpty()) {
            userJobRelationRepository.updateStatusByIds(update2ActiveIds, 1);
        }

        List<UserJobRelation> update2Invalid = existingRelations.stream().filter(r -> !existingUserJobRelationRecord(newRelations, r)).collect(Collectors.toList());
        List<Long> update2InvalidIds = new ArrayList<>();
        update2Invalid.forEach(r -> {
            //r.setStatus(false);
            update2InvalidIds.add(r.getId());
            cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(r.getUserId());
        });
        //userJobRelationRepository.saveAll(update2Invalid);
        if (!update2InvalidIds.isEmpty()) {
            userJobRelationRepository.updateStatusByIds(update2InvalidIds, 0);
        }

        List<UserJobRelation> addRelation = newRelations.stream().filter(r -> !existingUserJobRelationRecord(existingRelations, r)).collect(Collectors.toList());
        addRelation.forEach(r -> {
            r.setStatus(true);
            cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(r.getUserId());
        });
        userJobRelationRepository.saveAll(addRelation);

    }

    private boolean existingUserJobRelationRecord(List<UserJobRelation> newRelations, UserJobRelation userJobRelation) {
        if(CollectionUtils.isEmpty(newRelations) || Objects.isNull(userJobRelation)) {
            return false;
        }

        for(UserJobRelation relation : newRelations) {
            if(relation.getJobId().equals(userJobRelation.getJobId()) && relation.getUserId().equals(userJobRelation.getUserId())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<AssignedUserDTO> getJobAssignedUserDTOList(Long jobId) {
        List<AssignedUserDTO> asList = new ArrayList<>();
        List<UserJobRelation> userJobRelationList = userJobRelationRepository.findAllByJobIdAndStatusIsTrue(jobId);
        if (ObjectUtil.isNotEmpty(userJobRelationList)) {
            List<UserBriefDTO> userBriefDTOList = userService.getAllBriefUsersByIds(userJobRelationList.stream().map(UserJobRelation::getUserId).collect(Collectors.toList())).getBody();
            if (userBriefDTOList == null || userBriefDTOList.size() == 0) {
                log.error("Cannot fetch User info from user Service");
                return asList;
            }
            HashMap<Long, UserBriefDTO> userSet = userBriefDTOList.stream().collect(Collectors.toMap(UserBriefDTO::getId, Function.identity(), (user1, user2) -> user2, HashMap::new));
            asList = userJobRelationList.stream()
                    .map(userJobRelation -> {
                        AssignedUserDTO assignedUserDTO = new AssignedUserDTO();
                        assignedUserDTO.setUserId(userJobRelation.getUserId());
                        if (userSet.containsKey(userJobRelation.getUserId())) {
                            UserBriefDTO userBriefDTO = userSet.get(userJobRelation.getUserId());
                            assignedUserDTO.setFirstName(userBriefDTO.getFirstName());
                            assignedUserDTO.setLastName(userBriefDTO.getLastName());
                            assignedUserDTO.setUsername(userBriefDTO.getUsername());
                            assignedUserDTO.setEmail(userBriefDTO.getEmail());
                            assignedUserDTO.setActivated(userBriefDTO.isActivated());
                        }
                        return assignedUserDTO;
                    }).collect(Collectors.toList());
        }
        return asList;
    }


    @Override
    public Boolean isAssignedUsers(Long jobId, Long userId){
        UserJobRelation jobRelation = userJobRelationRepository.findFirstByJobIdAndUserIdAndStatusIsTrue(jobId, userId);
        return jobRelation == null ? false: true;
    }

}