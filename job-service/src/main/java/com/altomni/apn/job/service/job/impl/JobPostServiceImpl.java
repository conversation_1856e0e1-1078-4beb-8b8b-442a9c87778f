package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.job.domain.enumeration.JazzHRCountryType;
import com.altomni.apn.job.domain.enumeration.JazzHRWorkFlowType;
import com.altomni.apn.job.domain.job.JobPost;
import com.altomni.apn.job.repository.job.JobPostRepository;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.repository.job.StateMappingRepository;
import com.altomni.apn.job.service.company.CompanyService;
import com.altomni.apn.job.service.job.JobPostService;
import com.altomni.apn.job.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.http.entity.ContentType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class JobPostServiceImpl implements JobPostService {

    @Resource
    private JobRepository jobRepository;
    @Resource
    private JobPostRepository jobPostRepository;
    @Resource
    private StateMappingRepository stateMappingRepository;
    @Resource
    private UserService userService;

    @Resource
    private CompanyService companyService;

    @Override
    public JobPost postJobByJobId(Long jobId, JobPost jobPost) {
        String pathSegment = "jobs";
        JobV3 job = jobRepository.findById(jobId).orElse(null);

        if (jobPost.getWorkflowType() == null) {
            jobPost.setWorkflowType(JazzHRWorkFlowType.Default.value);
        }

        String jsonStr = wrappingJazzHRJson(job, jobPost);

        jobPost.setJazzhrJobId(
            (String) Objects.requireNonNull(formRequestPost(pathSegment, jsonStr)).get("job_id"));

        jobPost.setJazzhrStatus(JobStatusDBValueToJazzHRFormat(job));

        return fillRestFields(jobPost);
    }

    private JobPost fillRestFields(JobPost jobPost) {
        if (jobPost != null) {
            if (!jobPost.getReposted()) {
                jobPost.setReposted(false);
            }
            if (jobPost.getFrequency() == null) {
                jobPost.setFrequency(0); // Do not re-post
            }

            if (jobPost.getStartDate() == null) {
                jobPost.setStartDate(LocalDate.now());
            }

            if (jobPost.getEndDate() == null) {
                jobPost.setEndDate(LocalDate.now());
            }
        }
        return jobPost;
    }

    private HashMap<String, Object> requestGet(String pathSegments) {
        OkHttpClient client = new OkHttpClient();
        HttpUrl originalHttpUrl = HttpUrl.parse("applicationProperties.getJazzHRService().getUrl()"); // TODO

        HttpUrl url = Objects.requireNonNull(originalHttpUrl).newBuilder()
            .addPathSegments(pathSegments)
            .addQueryParameter("apikey", "applicationProperties.getJazzHRService().getApiKey()") // TODO
            .build();

        log.debug("REST url: {}", url);
        Request request = new Request.Builder()
            .url(url)
            .build();
        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                return JsonUtil.fromJson(response.body().string(), HashMap.class);
            }
            return null;
        } catch (IOException e) {
            log.error("[JobPost] Error connect to JazzHRUsers API. Exception is {}",
                    ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    private HashMap<String, Object> formRequestPost(String pathSegments, String jsonStr) {
        MediaType jsonFormat = MediaType.parse(ContentType.APPLICATION_JSON.getMimeType());

        OkHttpClient client = new OkHttpClient();
        RequestBody body = RequestBody.create(jsonFormat, jsonStr);
        HttpUrl originalHttpUrl = HttpUrl.parse("applicationProperties.getJazzHRService().getUrl()"); // TODO

        HttpUrl url = Objects.requireNonNull(originalHttpUrl).newBuilder()
            .addPathSegments(pathSegments)
            .build();

        log.debug("REST URL: {}", url);
        Request request = new Request.Builder()
            .url(url)
            .post(body)
            .build();

        try {
            Response response = client.newCall(request).execute();
            if (response.isSuccessful() && response.body() != null) {
                return JsonUtil.fromJson(response.body().string(), HashMap.class);
            }
            return null;
        } catch (IOException e) {
            log.error("[JobPost] Error connect to JazzHRUsers API. Exception is {}",
                    ExceptionUtils.getStackTrace(e));
            return null;
        }
    }

    private String wrappingJazzHRJson(JobV3 job, JobPost jobPost) {
        JSONObject obj = new JSONObject();
      /*  obj.put("apikey", "applicationProperties.getJazzHRService().getApiKey()"); // TODO
        obj.put("title", job.getTitle());
        obj.put("hiring_lead_id", getJazzHrUserIdByEmail(jobPost.getPrimaryRecruiterId()));
        obj.put("employment_type", JobTypeDBValueToJazzHRFormat(job).toString());
//        obj.put("minimum_experience", ExpLevelDBValueToJazzHRFormat(job).toString());
        obj.put("country", CountryDBValueToJazzHRFormat(job));
        Set<JobLocation> locationDTOList = job.getJobLocations();
        JobLocation  location = locationDTOList.stream().filter(s -> ObjectUtil.isEmpty(s.getLocation())).findAny().get();
        if (ObjectUtil.isNotEmpty(location)) {
            StateMapping state = stateMappingRepository
                .findOneByNameOrAbbreviation(location.getProvince(), location.getProvince());
            if (state != null) {
                obj.put("state", state.getAbbreviation());
            } else {
                obj.put("state", "");
            }
            obj.put("city", StringUtils.trimToEmpty(location.getCity()));
            obj.put("postal_code", StringUtils.trimToEmpty(location.getZipcode()));
        } else {
            obj.put("state", "");
        }

        obj.put("description", StringUtils.trimToEmpty(job.getJobAdditionalInfo().getPublicDesc()));
        obj.put("confidential", "false");
        obj.put("private", "false");
//        if (job.getDivisionId() != null) {
//            Division division = divisionRepository.findOne(job.getDivisionId());
//            if (division != null) {
//                obj.put("department", StringUtils.trimToEmpty(division.getName()));
//            }
//        }
        if (jobPost.getJazzhrStatus() == null) {
            // new post
            obj.put("job_status", JobStatusDBValueToJazzHRFormat(job).toString());
        } else {
            // system repost job
            obj.put("job_status", jobPost.getJazzhrStatus());
        }
        obj.put("syndication", "true");  // formRequestPost to free job board
        obj.put("workflow_id", StringUtils.trimToEmpty(jobPost.getWorkflowType().toString()));

        // For canned value, 0 : Not Select, 1 : Select, Not Required, 2 : Select, Required
        obj.put("canned_address", "1");
        obj.put("canned_cover_letter", "0");
        obj.put("canned_references", "0");
        obj.put("canned_wmyu", "3");
        obj.put("canned_linked_in", "3");
        obj.put("canned_website", "0");
        obj.put("canned_twitter_username", "0");
        obj.put("canned_start", "3");
        obj.put("canned_weekends", "0");
        obj.put("canned_evenings", "0");
        obj.put("canned_overtime", "0");
        obj.put("canned_languages", "0");
        obj.put("canned_salary", "3");
        obj.put("canned_referral", "0");
        obj.put("canned_license", "0");
        obj.put("canned_cdl", "0");
        obj.put("canned_relocate", "3");
        obj.put("canned_citizen", "3");
        obj.put("canned_education", "0");
        obj.put("canned_college", "0");
        obj.put("canned_gpa", "0");
        obj.put("canned_over18", "0");
        obj.put("canned_flighthours", "0");
        obj.put("canned_flightgrade", "0");
        obj.put("canned_felony", "0");
        obj.put("canned_felonyexplain", "0");
        obj.put("internal_job_code", job.getCode()); // JobDiva id

        // might need these items below later
        //obj.put("eeo_1_job_category", "1");
        //obj.put("approved_salary_range_minimum", job.getPayRateFrom());
        //obj.put("approved_salary_range_maximum", job.getPayRateTo());
        //obj.put("job_notes", job.getNotes());

        obj.put("open_date", LocalDate.now(ZoneId.systemDefault()));

        log.debug("Request body :{}", obj.toJSONString());*/
        return obj.toJSONString();
    }

    private Integer CountryDBValueToJazzHRFormat(JobV3 job) {
        CompanyDTO company = companyService.getCompany(job.getCompanyId()).getBody();
        if(ObjectUtil.isNotEmpty(company) &&
            (company.getName().toLowerCase().equals("china") ||
                company.getName().toLowerCase().equals("cn"))) {
            return JazzHRCountryType.China.toDbValue();
        }
        return JazzHRCountryType.United_States.toDbValue();
    }

    private Integer JazzHRJobStatusStringToDBValue(String status) {
        switch (status) {
            case "open":
                return 1;
            case "Drafting":
                return 2;
            case "On Hold":
                return 3;
            case "Filled":
                return 4;
            case "Cancelled":
                return 5;
            case "Closed":
                return 6;
            case "Needs Approval":
                return 7;
            case "Approved":
                return 8;
            default:
                return 1; // set open as default value for now
        }
    }

    private Integer JobStatusDBValueToJazzHRFormat(JobV3 job) {
        //Open(0), OnHold(2), Cancelled(3), Closed(4), Filled(5), Expired(6), Ignored(7);
        if (job.getStatus() == null) {
            return 1; // open
        }
        switch (job.getStatus()) {
            case OPEN:
                return 1;
            case ONHOLD:
                return 3;
            case CANCELLED:
                return 5;
            case CLOSED:
                return 6;
            case FILLED:
                return 4;
            case EXPIRED:
                return 6;
            default:
                return 1; // set open as default value for now
        }
    }

//    private Integer JobTypeDBValueToJazzHRFormat(JobV3 job) {
//        // Direct_Placement(0), Contract(1), Right_To_Hire(2), Full_Time(3), Part_Time(4);
////        if (job.getExpLevels() == null) {
////            return 1; // full time
////        }
//
//        switch (job.getJobType()) {
//            case FULL_TIME:
//                return 1;
//            case PART_TIME:
//                return 2;
//            case CONTRACT:
//                return 6;
//            default:
//                return 1; // set default value as full time
//        }
//    }

//    private Integer ExpLevelDBValueToJazzHRFormat(JobV3 job) {
//        if (job.getExpLevels() == null) {
//            return 5; // experienced
//        }
//        switch (job.getExpLevels()) {
//            case "None":
//                return 3;
//            case "One_Year_Or_Less":
//                return 3;
//            case "Two_To_Five_Years":
//                return 4;
//            case "Five_To_Ten_Years":
//                return 5;
//            case "More_Than_Ten_Years":
//                return 6;
//            default:
//                return 5; // set default experienced
//        }
//    }

    private String getJazzHrUserIdByEmail(Long userId) {
        User user = userService.findById(userId).getBody();
        if (user == null) {
            throw new CustomParameterizedException("User dose not have account in JazzHR.");
        }
        HashMap<String, Object> jsonMap = requestGet("users/email/" + user.getEmail());
        if (jsonMap == null) {
            return "";
        }

        return (String) jsonMap.get("id");
    }

    @Override
    public Boolean checkUserExisting(Long userId) {
        User user = userService.findById(userId).getBody();
        if (user == null) {
            return false;
        }

        HashMap<String, Object> jsonMap = requestGet("users/email/" + user.getEmail());
        if (jsonMap == null) {
            return false;
        }

        String jazzHRId = (String) jsonMap.get("id");
        return jazzHRId != null && !jazzHRId.isEmpty();
    }

    private String getJazzHrJobStatusByJobId(String jazzhrJobId) {
        HashMap<String, Object> jsonMap = requestGet("jobs/" + jazzhrJobId);
        if (jsonMap == null) {
            return "";
        }

        return (String) jsonMap.get("status");
    }

    @Transactional
    public void updateJobStatus(JobPost previousJob) {
        String jazzhrStatus = getJazzHrJobStatusByJobId(previousJob.getJazzhrJobId());
        if (previousJob.getJazzhrStatus() == null ||
            !previousJob.getJazzhrStatus().equals(JazzHRJobStatusStringToDBValue(jazzhrStatus))) {
            previousJob.setJazzhrStatus(JazzHRJobStatusStringToDBValue(jazzhrStatus));
            jobPostRepository.save(previousJob);
        }
    }

    /**
     * <p>
     * This is scheduled to scan all open jobPost everyday, at 01:00 (am).
     * //  @Scheduled(cron = "0 0 1 * * ?")
     */
    @Override
    public void repostInADayJob() throws CloneNotSupportedException {
        log.debug("System : Scan all the job post on JazzHR and repost the job if actived and match the conditions.");
        // Get all active job
        List<JobPost> jobPosts = jobPostRepository.findAllByIsReposted(true);
        if (jobPosts == null) {
            return;
        }

        for (JobPost jobPost : jobPosts) {
            if (jobPost.getStartDate() == null) {
                continue;
            }

            LocalDate jobPostDate = jobPost.getStartDate().plusDays(jobPost.getFrequency());
            if (jobPostDate.isAfter(jobPost.getEndDate())) {
                jobPost.setReposted(Boolean.FALSE);
                jobPostRepository.save(jobPost);
            }

            // Check the next post date is today and the job status is open
            if (jobPostDate.isEqual(LocalDate.now()) && jobPost.getJazzhrStatus().equals(1)) {
                postJobBySystem(jobPost);
                // Check previous jobPost status
                List<JobPost> preJobPosteds = jobPostRepository.findAllByJobId(jobPost.getJobId());
                for (JobPost preJobPosted : preJobPosteds) {
                    // Update job status in DB according to jazzHR job status
                    updateJobStatus(preJobPosted);
                }
            }
        }
    }

    @Transactional
    public void postJobBySystem(JobPost jobPost) throws CloneNotSupportedException {
        JobPost newJobPost = (JobPost) jobPost.clone();
        newJobPost.setId(null);
        newJobPost = postJobByJobId(newJobPost.getJobId(), newJobPost);

        if (newJobPost.getFrequency() == null) {
            newJobPost.setFrequency(0); // Do not re-post
            newJobPost.setReposted(false);
        }

        // Update new JobPost
        newJobPost.setStartDate(LocalDate.now());
        // Don't post old job anymore , and reminder user to close this one manually on JazzHR
        jobPost.setReposted(Boolean.FALSE);

        jobPostRepository.save(jobPost);
        jobPostRepository.save(newJobPost);
    }
}
