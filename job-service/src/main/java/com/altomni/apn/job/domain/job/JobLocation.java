package com.altomni.apn.job.domain.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A JobLocation.
 */
@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "job_location")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobLocation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "job_id", updatable = false)
    private Long jobId;

    @Column(name = "official_county")
    private String officialCounty;

    @Column(name = "official_country")
    private String officialCountry;

    @Column(name = "official_province")
    private String officialProvince;

    @Column(name = "official_city")
    private String officialCity;

    @Column(name = "original_loc")
    private String originalLoc;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("originalLoc"));

}
