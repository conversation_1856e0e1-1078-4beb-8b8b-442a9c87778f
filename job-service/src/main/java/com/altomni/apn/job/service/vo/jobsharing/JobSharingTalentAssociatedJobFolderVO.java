package com.altomni.apn.job.service.vo.jobsharing;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Data
public class JobSharingTalentAssociatedJobFolderVO {

    @ApiModelProperty(value = "talent associatedJobFolder")
    private String folderId;
    private Long jobId;
    private String jobTitle;
}
