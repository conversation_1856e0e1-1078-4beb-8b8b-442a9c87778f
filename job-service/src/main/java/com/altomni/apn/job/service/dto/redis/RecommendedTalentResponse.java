package com.altomni.apn.job.service.dto.redis;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@NoArgsConstructor
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecommendedTalentResponse implements Serializable {

    private static final long serialVersionUID = 7547052176540537819L;

    @ApiModelProperty(value = "[STARTED, FINISHED, ON_GOING, ERROR, ERROR_WT_DATA]")
    private String status;

    private Boolean hasMore;

    private String talents;

    //private List<RedisCommonTalent> commonTalents;

}
