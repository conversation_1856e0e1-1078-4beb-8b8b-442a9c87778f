package com.altomni.apn.job.service.dto.jobsharing;

import com.altomni.apn.common.domain.enumeration.job.JobPosterTemplate;
import com.altomni.apn.common.domain.enumeration.job.JobPosterTemplateConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.validation.constraints.NotNull;


@AllArgsConstructor
@NoArgsConstructor
@Data
public class JobSharingPosterTemplateDTO {

    @ApiModelProperty(value = "jobPosterImageId for poster")
    @NotNull
    private Long jobPosterImageId;

    @ApiModelProperty(value = "linkUrl for poster")
    @NotNull
    @Convert(converter = JobPosterTemplateConverter.class)
    private JobPosterTemplate template;

}
