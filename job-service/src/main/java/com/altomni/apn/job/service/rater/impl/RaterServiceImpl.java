package com.altomni.apn.job.service.rater.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.RaterConstants;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.domain.enumeration.talent.RecommendedTalentSource;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.rater.RaterRequestApplication;
import com.altomni.apn.common.dto.rater.RaterRequestBody;
import com.altomni.apn.common.dto.redis.RaterResponseDTO;
import com.altomni.apn.common.dto.talent.GetTalentsForAiRecommendationDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.ApplicationProperties;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.service.application.ApplicationService;
import com.altomni.apn.job.service.company.CompanyService;
import com.altomni.apn.job.service.dto.redis.*;
import com.altomni.apn.job.service.rater.RaterService;
import com.altomni.apn.job.service.talent.TalentService;
import com.altomni.apn.job.service.user.UserService;
import com.altomni.apn.job.web.rest.vm.JobLocationVM;
import com.altomni.apn.job.web.rest.vm.JobTitleVM;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.repository.user.CreditTransactionRepository;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import redis.clients.jedis.Tuple;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RaterServiceImpl implements RaterService {

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private HttpService httpService;

    @Resource
    private UserService userService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private CompanyService companyService;

    @Resource
    private TalentService talentService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Resource
    private CreditTransactionRepository creditTransactionRepository;

    @Override
    public void refreshJobRater(Long tenantId, Long jobId) throws IOException {
        RaterRequestBody requestBody = new RaterRequestBody();
        requestBody.setApplications(getApplicationsByJobId(jobId));
//        requestBody.setRestrictions(SearchOriginalDTO.getSearchConditionDTO());
        String body = JsonUtil.toJson(requestBody);
        String url = applicationProperties.getRaterUrl() + "tenant/" + tenantId + "/find_talents_for_job/" + jobId;
        String uuid = DigestUtils.sha256Hex(url + body);
        requestBody.setUuid(RedisConstants.DATA_KEY_RATER + uuid);
        httpService.post(url, JsonUtil.toJson(requestBody));
    }

    @Override
    public RecommendedTalentResponse recommendCommonTalentsForJob(Long jobId, Pageable pageable, boolean refresh) {
        return recommendTalentsForJob(jobId, pageable, refresh, RedisConstants.DATA_KEY_DATA_COMMON, RedisConstants.DATA_KEY_TOTAL_COMMON);
    }

    public boolean hasNextPage(Pageable pageable, Integer total) {
        if (total == null || pageable == null) {
            return false;
        }

        int pageSize = pageable.getPageSize();
        int pageNumber = pageable.getPageNumber();
        int totalPages = (total + pageSize - 1) / pageSize;

        return pageNumber < totalPages - 1;
    }

    private RecommendedTalentResponse getRecommendedTalentResponseByRedis(RaterRequest raterRequest, Pageable pageable, Long jobId, String dataKey, String totalKey, boolean refresh) {
        com.altomni.apn.common.dto.redis.RedisResponse res1st = commonRedisService.getRaterData(raterRequest.getRedisPrefixKey(), dataKey, totalKey, pageable);
        log.info("Redis_response: {}", res1st);
        if (res1st != null && StringUtils.isNotEmpty(res1st.getStatus())) {
            if (refresh && !recommendOngoing(res1st.getStatus())) {
                commonRedisService.deleteRaterRedisKeyWildcard(raterRequest.getRedisPrefixKey());
            } else {
                return new RecommendedTalentResponse()
                        .setStatus(res1st.getStatus())
                        .setHasMore(hasNextPage(pageable, res1st.getTotal()))
                        .setTalents(parseTalentsFromResponse(res1st.getData(), jobId));
            }
        }
        return null;
    }

    private boolean recommendOngoing(String status) {
        return "STARTED".equals(status) || "ON_GOING".equals(status);
    }

    @Override
    public RecommendedTalentResponse recommendTenantTalentsForJob(Long jobId, Pageable pageable, boolean refresh) {
        return recommendTalentsForJob(jobId, pageable, refresh, RedisConstants.DATA_KEY_DATA_BIZ, RedisConstants.DATA_KEY_TOTAL_BIZ);
    }

    private RecommendedTalentResponse recommendTalentsForJob(Long jobId, Pageable pageable, boolean refresh, String dataKey, String totalKey) {
        RaterRequest raterRequest = this.getRaterRequest(jobId,null);
        RecommendedTalentResponse response = getRecommendedTalentResponseByRedis(raterRequest, pageable, jobId, dataKey, totalKey, refresh);
        if (response == null) {
            response = new RecommendedTalentResponse().setStatus(callRater(raterRequest.getRequestBody(), raterRequest.getUrl()));
        }
        return response;
    }

    @Override
    public RecommendedTalentResponse recommendAllTalentsForJob(Long jobId, Pageable pageable, boolean refresh) {
        return recommendTalentsForJob(jobId, pageable, refresh, RedisConstants.DATA_KEY_DATA_RECOMMENDATIONS, RedisConstants.DATA_KEY_TOTAL);
    }

    @Override
    public void invokeRecommendCommonTalentsForJob(Long jobId, Long tenantId) {
        log.info("[invokeRecommendCommonTalentsForJob]:jobId:{},tenantId:{}",jobId,tenantId);
        RaterRequest raterRequest = this.getRaterRequest(jobId,tenantId);
        log.info("[invokeRecommendCommonTalentsForJob]:RedisPrefixKey:{},url:{}",raterRequest.getRedisPrefixKey(),raterRequest.getUrl());
        if (!commonRedisService.checkRaterRedisKey(raterRequest.getRedisPrefixKey())) {
            log.info("[invokeRecommendCommonTalentsForJob]:start invoke rater:{},url:{}", JSONUtil.toJsonStr(raterRequest), raterRequest.getUrl());
            callRater(raterRequest.getRequestBody(), raterRequest.getUrl());
        }
    }

    /**
     *
     * @param jobId
     * @param sourceTenantId 不是空 程序自动触发
     * @return
     */
    private RaterRequest getRaterRequest(Long jobId,Long sourceTenantId) {
        final JobV3 job = verifyJob(jobId);
        log.info("[getRaterRequest] verifyJob jobId:{},tenantId:{}",jobId,sourceTenantId);
        RaterRequestBody requestBody = new RaterRequestBody();
        requestBody.setApplications(getApplicationsByJobId(jobId));
        log.info("[getRaterRequest] jobId:{},tenantId:{}",jobId,sourceTenantId);
//        requestBody.setRestrictions(SearchOriginalDTO.getSearchConditionDTO());
        //set jobLastModifiedDate
        String lastModifiedDateStr = DateUtil.fromInstantToUtcDateTimeWithMillisecond(getJobLastModifiedDate(job));
        log.info("[getRaterRequest] lastModifiedDateStr :{},jobId:{},tenantId:{}",lastModifiedDateStr,jobId,sourceTenantId);
        requestBody.setJobLastModifiedDate(lastModifiedDateStr);
        Long tenantId = SecurityUtils.getTenantId();
        String redisPrefixKey = String.format(RedisConstants.DATA_KEY_RATER_TALENTS_FOR_JOB, tenantId, jobId, lastModifiedDateStr.replaceAll(":", "-"));
        if (null != sourceTenantId) {
            tenantId = sourceTenantId;
            requestBody.setUseCommonTalent(true);
            requestBody.setUseTenantTalent(true);
            redisPrefixKey = String.format(RedisConstants.DATA_KEY_RATER_TALENTS_FOR_JOB_BY_AUTO_TRIGGER, tenantId, jobId);
        }
        String url = applicationProperties.getRaterUrl() + RaterConstants.TENANT + tenantId + RaterConstants.FIND_TALENTS_FOR_JOB + jobId;
        String uuid = DigestUtils.sha256Hex(url + JsonUtil.toJson(requestBody));
        requestBody.setUuid(RedisConstants.DATA_KEY_RATER + uuid);
//        RedisResponse res1st = redisService.getRaterData(uuid, pageable);
        final String request = JsonUtil.toJson(requestBody);
        log.info("[getRaterRequest] Request to get  jobs for job {}, request: {}, UUID: {}", job.getId(), request, uuid);
        log.info("[getRaterRequest] Redis_key: {},url{},", redisPrefixKey,url);
        return new RaterRequest(redisPrefixKey, requestBody, url);
    }

    @Override
    public SimilarJobResponse getSimilarJobsByJobId(Long jobId, Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[getSimilarJobsByJobId: {}] Request to get similar jobs for jobId {}, page num: {}, page size: {}", jobId, jobId, pageable.getPageNumber(), pageable.getPageSize());
        try {
            StopWatch stopWatch = new StopWatch("echocheng10 start");
            stopWatch.start("[echocheng 10.1]");
            final JobV3 job = verifyJob(jobId);
            RaterRequestBody requestBody = new RaterRequestBody();
            requestBody.setApplications(getApplicationsByJobId(jobId));
            log.info("[getSimilarJobsByJobId: {}] successfully get applications when get similar jobs for jobId {}, page num: {}, page size: {}", jobId, jobId, pageable.getPageNumber(), pageable.getPageSize());
            String lastModifiedDateStr = DateUtil.fromInstantToUtcDateTimeWithMillisecond(getJobLastModifiedDate(job));
            requestBody.setJobLastModifiedDate(lastModifiedDateStr);
            Long tenantId = SecurityUtils.getTenantId();
            String url = applicationProperties.getRaterUrl() + RaterConstants.TENANT + tenantId + RaterConstants.FIND_SIMILAR_JOBS + jobId;
            final String request = JsonUtil.toJson(requestBody);
            String uuid = DigestUtils.sha256Hex(url + request);
            log.info("[getSimilarJobsByJobId: {}] Request to get similar jobs for job {}, request: {}, UUID: {}", jobId, job.getId(), request, uuid);
            String redisPrefixKey = String.format(RedisConstants.DATA_KEY_RATER_SIMILAR_JOB, tenantId, jobId, lastModifiedDateStr.replaceAll(":", "-"));
            log.info("[getSimilarJobsByJobId: {}] Redis_key: {}", jobId, redisPrefixKey);
            com.altomni.apn.common.dto.redis.RedisResponse res1st = commonRedisService.getRaterData(redisPrefixKey, RedisConstants.DATA_KEY_DATA_RECOMMENDATIONS, RedisConstants.DATA_KEY_TOTAL, null);
            log.info("[getSimilarJobsByJobId: {}] Redis_response: {}", jobId, res1st);
            stopWatch.stop();
            if (res1st != null && StringUtils.isNotEmpty(res1st.getStatus())) {
                stopWatch.start("[echocheng 10.2]");
                SimilarJobResponse result = new SimilarJobResponse();
                result.setStatus(res1st.getStatus());
                parseTenantJobFromResponse(res1st.getData(), result, pageable);
                stopWatch.stop();
                return result;
            }
            requestBody.setUuid(RedisConstants.DATA_KEY_RATER + uuid);
            stopWatch.start("[echocheng 10.2]");
            String status = callRater(requestBody, url);
            stopWatch.stop();
            log.info("[apn @{}] echocheng10 time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
            return new SimilarJobResponse().setStatus(status);
        } catch (Exception e) {
            log.error("[getSimilarJobsByJobId: {}] exception when get similar jobs, error msg: {}", jobId, e.getLocalizedMessage(), e);
            throw e;
        }
    }

    private String callRater(RaterRequestBody requestBody, String url) {
        String status = null;
        try {
            HttpResponse response = httpService.post(url, JsonUtil.toJson(requestBody));
            if (response != null && ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.info("[APN: RaterService @{}] Get jobs and call raterService success, userId: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response.getCode(), response.getBody());
                status = JsonUtil.fromJson(response.getBody(), RaterResponseDTO.class).getStatus();
            } else if (response != null && (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.NOT_ACCEPTABLE.value(), response.getCode()))) {
                log.info("[APN: RaterService @{}] Get jobs and call raterService not success, userId: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                throw new CustomParameterizedException(response.getCode(), HttpStatus.valueOf(response.getCode()).getReasonPhrase(), response.getBody());
            } else {
                log.info("[APN: RaterService @{}] Get jobs and call raterService error, userId: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
                if (response != null) {
                    throw new ExternalServiceInterfaceException(response.getBody(), response.getCode());
                } else {
                    throw new ExternalServiceInterfaceException();
                }
            }
        } catch (IOException e) {
            log.error("[APN: RaterService @{}] Get jobs and call raterService IOException, userId: {}", SecurityUtils.getUserId(), e);
            throw new ExternalServiceInterfaceException("Call rater service error");
        }
        return status;
    }

    private JobV3 verifyJob(Long jobId) {
        log.info("[getRaterRequest] verifyJob jobId:{}",jobId);
        JobV3 job = jobRepository.findById(jobId).orElseThrow(() -> new NotFoundException("Job not exists"));
        /*if (!SecurityUtils.isCurrentTenant(job.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETJOBWITHOUTENTITY_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }*/
        log.info("[getRaterRequest] end verifyJob jobId:{}",jobId);
        return job;
    }

    /**
     * extract talent profile from redis data
     *
     * @param data: redis data
     * @return List<RecommendedTalent>: the list of talent profile
     */
    private String parseTalentsFromResponse(Set<Tuple> data, Long jobId) {
        if (CollUtil.isEmpty(data)) {
            return "[]";
        }
        Map<String, Double> map = new HashMap<>();
        // extract data from redis
        for (Tuple t : data) {
            RecommendationResult recommendationResult = JSON.parseObject(t.getElement(), RecommendationResult.class);
            if (ObjectUtil.isNull(recommendationResult)) {
                continue;
            }
            map.put(recommendationResult.get_id(), t.getScore());
        }

        GetTalentsForAiRecommendationDTO dto = new GetTalentsForAiRecommendationDTO();
        dto.setJobId(jobId);
        dto.setTalentIdMap(map);
        return talentService.getTalentsForAiRecommendationByIds(dto).getBody();
    }


    private Map<String, RecommendedTalent> buildCommonTalent(Map<String, RecommendedTalent> commonTalentIdToRecommendedTalent, Map<String, Double> commonTalentIdToScore) {

        List<CreditTransaction> creditTransactions = userService.findAllByTenantIdAndStatusAndProfileIdIn(
                SecurityUtils.getTenantId(),
                Status.Available,
                new ArrayList<>(commonTalentIdToScore.keySet()))
                .getBody();
        final Map<String, Long> esIdToTalentId = creditTransactions.stream().filter(ct -> ct.getTalentId() != null).collect(Collectors.toMap(CreditTransaction::getProfileId, CreditTransaction::getTalentId));


        commonTalentIdToRecommendedTalent.forEach((talentId, recommendedTalent) -> {
            recommendedTalent.setScore(commonTalentIdToScore.get(talentId));
            recommendedTalent.setSource(RecommendedTalentSource.COMMON_POOL);
            final JSONArray currentExperiences = ((RecommendedCommonTalent) recommendedTalent).getCurrentExperiences();
            if (ObjectUtil.isNotNull(currentExperiences)) {
                JSONObject exp = currentExperiences.getJSONObject(0);
                recommendedTalent.setCompany(exp.getString("companyName"));
                recommendedTalent.setTitle(exp.getString("title"));
            }
            //add talentId & purchased
            if (esIdToTalentId.containsKey(recommendedTalent.getId())) {
                //set Purchase flag
                ((RecommendedCommonTalent) recommendedTalent).setPurchased(true);
                //set talentId
                recommendedTalent.setId(String.valueOf(esIdToTalentId.get(recommendedTalent.getId())));
            }
        });

        return commonTalentIdToRecommendedTalent;
    }

    private void parseTenantJobFromResponse(Set<Tuple> data, SimilarJobResponse response, Pageable pageable) throws ExecutionException, InterruptedException {
        log.info("[getSimilarJobsByJobId] parseTenantJobFromResponse");
        if (CollUtil.isEmpty(data)) {
            response.setJobs(List.of());
            response.setTotal(0);
            return;
        }
        Set<Long> unauthorizedPrivateJobIds = jobRepository.findAllUnauthorizedPrivateJobIds(SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        List<SimilarTenantJob> similarJobList = new ArrayList<>();
        List<Long> jobIds = new ArrayList<>();
        log.info("[getSimilarJobsByJobId] unauthorizedPrivateJobIds= {}", unauthorizedPrivateJobIds);
        for (Tuple t : data) {
            RecommendationResult recommendationResult = JSON.parseObject(t.getElement(), RecommendationResult.class);
            Long jobId = Long.parseLong(recommendationResult.get_id());
            if (unauthorizedPrivateJobIds.contains(jobId)) {
                continue;
            }
            jobIds.add(jobId);
            SimilarTenantJob similarTenantJob = new SimilarTenantJob();
            similarTenantJob.setId(jobId);
            similarTenantJob.setScore(t.getScore());
            similarJobList.add(similarTenantJob);
        }
        response.setTotal(similarJobList.size());
        log.info("[getSimilarJobsByJobId] similarJobList= {}", similarJobList);
        int from = Math.toIntExact(pageable.getOffset());
        int to = Math.min(from + pageable.getPageSize(), similarJobList.size());
        similarJobList = similarJobList.subList(from, to);
        final Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<Map<Long, Long>> jobToInProcessCandidatesFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return applicationService.countUnfinishedApplicationsForJobs(jobIds).getBody();
        });
        CompletableFuture<Map<Long, String>> jobToCompanyFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return companyService.getCompanyNamesByJobIds(jobIds).getBody();
        });
        CompletableFuture<Map<Long, List<JobLocationVM.Location>>> jobToLocationsFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return getJobLocations(jobIds);
        });
        final Map<Long, String> jobIdToTitle = jobRepository.findJobTitlesByIds(jobIds).stream().collect(Collectors.toMap(JobTitleVM::getJobId, JobTitleVM::getJobTitle));
        similarJobList.sort((o1, o2) -> o2.getScore().compareTo(o1.getScore()));
        final Map<Long, String> jobToCompany = jobToCompanyFuture.get();
        final Map<Long, List<JobLocationVM.Location>> jobToLocations = jobToLocationsFuture.get();
        final Map<Long, Long> jobToInProcessCandidates = jobToInProcessCandidatesFuture.get();
        log.info("[getSimilarJobsByJobId] CompletableFuture finished");
        List<SimilarTenantJob> result = new ArrayList<>();
        for (SimilarTenantJob similarTenantJob : similarJobList) {
            final Long jobId = similarTenantJob.getId();
            similarTenantJob.setTitle(jobIdToTitle.getOrDefault(jobId, null));
            similarTenantJob.setCompanyName(jobToCompany.getOrDefault(jobId, null));
            similarTenantJob.setLocations(jobToLocations.getOrDefault(jobId, new ArrayList<>()));
            similarTenantJob.setCandidates(jobToInProcessCandidates.getOrDefault(jobId, 0L).intValue());
            if (jobIdToTitle.containsKey(jobId)) {
                result.add(similarTenantJob);
            }
        }
        this.fillPrivateJob(result);
        response.setJobs(result);
    }

    private void fillPrivateJob(List<SimilarTenantJob> similarJobList) {
        List<Long> jobIds = similarJobList.stream().map(SimilarTenantJob::getId).toList();
        if (CollectionUtils.isNotEmpty(jobIds)) {
            Set<Long> privateJobIds = jobRepository.findPrivateJobIds(jobIds);
            if (CollectionUtils.isNotEmpty(privateJobIds)) {
                similarJobList.forEach(c -> c.setPrivateJob(privateJobIds.contains(c.getId())));
            }
        }
    }

    private Map<Long, List<JobLocationVM.Location>> getJobLocations(List<Long> jobIds) {
        final List<JobLocationVM> jobLocations = jobRepository.findJobLocationsByJobIds(jobIds);
        return jobLocations.stream().collect(Collectors.groupingBy(JobLocationVM::getJobId, Collectors.mapping(JobLocationVM::getLocation, Collectors.toList())));
    }

    private List<RaterRequestApplication> getApplicationsByJobId(Long jobId) {
        List<TalentRecruitmentProcessVO> applications = applicationService.getTalentRecruitmentProcessBriefByJobId(jobId).getBody();
        if (CollectionUtils.isNotEmpty(applications)) {
            List<Long> talentIds = applications.stream().map(TalentRecruitmentProcessVO::getTalentId).collect(Collectors.toList());
            List<CreditTransaction> creditTransactions = creditTransactionRepository.findAllByTenantIdAndStatusAndTalentIdIn(SecurityUtils.getTenantId(), Status.Available, talentIds);
            Map<Long, String> talentToProfileMap = creditTransactions.stream()
                    .collect(Collectors.toMap(
                            CreditTransaction::getTalentId,    // key mapper - 提取talentId作为key
                            CreditTransaction::getProfileId,   // value mapper - 提取profileId作为value
                            (existingValue, newValue) -> newValue  // merge function - 遇到重复key时使用新值覆盖旧值
                    ));
            return applications.stream()
                    .sorted(Comparator.comparing(TalentRecruitmentProcessVO::getCreatedDate, Comparator.nullsLast(Comparator.reverseOrder())))
                    .limit(5)
                    .map(a -> {
                String esId = talentToProfileMap.get(a.getTalentId());
                return RaterRequestApplication.fromApplicationForJob(a, esId);

            }).collect(Collectors.toList());
        }
        return null;
    }

    private Instant getJobLastModifiedDate(JobV3 job) {

        Long id = job.getId();
        if (id == null) {
            return job.getLastModifiedDate();
        }
        return jobRepository.getJobEsLastModifiedTime(id);

    }

    @AllArgsConstructor
    @Data
    class RaterRequest {
        private String redisPrefixKey;
        private RaterRequestBody requestBody;
        private String url;
    }
}
