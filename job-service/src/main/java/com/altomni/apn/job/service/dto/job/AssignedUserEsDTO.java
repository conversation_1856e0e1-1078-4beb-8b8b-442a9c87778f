package com.altomni.apn.job.service.dto.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AssignedUserEsDTO {

    @JsonProperty(value = "AM")
    private List<String> AM;

    @JsonProperty(value = "AMName")
    private String AMName;

    @JsonProperty(value = "RECRUITER")
    private List<String> RECRUITER;

    @JsonProperty(value = "DM")
    private List<String> DM;

    @JsonProperty(value = "AC")
    private List<String> AC;

    @JsonProperty(value = "PR")
    private List<String> PR;

    private List<String> names;
}
