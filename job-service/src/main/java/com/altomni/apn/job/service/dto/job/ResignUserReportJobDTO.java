package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.job.domain.enumeration.JobPriority;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ResignUserReportJobDTO extends AbstractAuditingEntity implements Serializable{

    private static final long serialVersionUID = -8813877969704820785L;

    @Id
    private Long id;

    @ApiModelProperty(value = "The tenant id the job belongs. It is injected from the user entity when creating job. Read only.")
    private Long tenantId;

    @ApiModelProperty(value = "Name of the company publish the job")
    private String company;

    @ApiModelProperty(value = "Name of the company publish the job")
    private Long companyId;

    private String department;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    @ApiModelProperty(value = "Job type", allowableValues = "DIRECT_PLACEMENT, CONTRACT, RIGHT_TO_HIRE, FULL_TIME, PART_TIME")
    private JobType jobType;

    @ApiModelProperty(value = "This is job internal code company used to identify the job")
    private String code;

    @ApiModelProperty(value = "The date when the job starts")
    private LocalDate startDate;

    @ApiModelProperty(value = "The date when the job ends")
    private LocalDate endDate;

    @ApiModelProperty(value = "The date of the job expired.")
    private LocalDate expirationDate;

    @ApiModelProperty(value = "The date the job is posted to ATS or job boards. Read Only.")
    private Instant postingTime;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
    private JobStatus status;

    @ApiModelProperty(value = "job priority. Default is active, the newly post job.", allowableValues = "ACTIVE, LOW, HIGH, URGENT")
    private JobPriority priority;

    @ApiModelProperty(value = "Comma separated tags for the job")
    private String tags;

    @ApiModelProperty(value = "Which division in the company is the position at")
    private Long divisionId;

    @ApiModelProperty(value = "keywords, UI only")
    private String keywords;

    @ApiModelProperty(value = "required keywords, UI only")
    private String requiredKeywords;

    @ApiModelProperty(value = "isVisible(company), True(1) or False(0). Default is False(0)")
    private Boolean visible;

    @ApiModelProperty(value = "isInternal(group), True(1) or False(0). Default is False(0)")
    private Boolean internal;

    private String addressLine;

    private String city;

    private String province;

    private String country;

    private String zipcode;

    @ApiModelProperty(value = "The id of hiring manager in the client contact. Use it to create/update hiring manager.")
    private Long hiringManagerId;

    @ApiModelProperty(value = "The id of hr in the client contact. Use it to create/update hr. Hiring manager or HR is required to save job")
    private Long hrId;

    private Integer currency;

    @ApiModelProperty(value = "bill rate low end, in $ or RMB")
    private BigDecimal billRateFrom;

    @ApiModelProperty(value = "bill rate high end, in $ or RMB")
    private BigDecimal billRateTo;

    @ApiModelProperty(value = "bill rate unit", allowableValues = "HOURLY, DAILY, WEEKLY, MONTHLY, YEARLY")
    private RateUnitType billRateUnitType;

    @ApiModelProperty(value = "pay rate low end, in $ or RMB")
    private BigDecimal payRateFrom;

    @ApiModelProperty(value = "pay rate high end, in $ or RMB")
    private BigDecimal payRateTo;

    @ApiModelProperty(value = "pay rate unit", allowableValues = "HOURLY, DAILY, WEEKLY, MONTHLY, YEARLY")
    private RateUnitType payRateUnitType;

    @ApiModelProperty(value = "number of openings for the job. Default is 1.")
    private Integer openings;

    @ApiModelProperty(value = "max application submissions allowed for the job. Default is 0, means no limit.")
    private Integer maxSubmissions;

    @ApiModelProperty(value = "aggregated application statistics in JSON: { <status>: # of applications }")
    private String applicationStats;

    @ApiModelProperty(value = "Minimum Degree, Eg: Master")
    private String minimumDegree;

    @ApiModelProperty(value = "Least experience year")
    private Integer leastExperienceYear;

    @ApiModelProperty(value = "Most experience year")
    private Integer mostExperienceYear;

    private String logo;

    @ApiModelProperty(value = "Preferred Degree Json List. eg: [\"Master\"]")
    private List<String> preferredDegrees;

    @ApiModelProperty(value = "Experience level the job required.", allowableValues = "FRESH_GRADUATE, LESS_THAN_THREE_YEARS, " +
            "THREE_TO_FIVE_YEARS, FIVE_TO_EIGHT_YEARS, MORE_THAN_EIGHT_YEARS")
    private List<String> expLevels;



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Long getCompanyId() {
        return companyId;
    }

    public void setCompanyId(Long companyId) {
        this.companyId = companyId;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public String getAddressLine() {
        return addressLine;
    }

    public void setAddressLine(String addressLine) {
        this.addressLine = addressLine;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getZipcode() {
        return zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    public JobType getJobType() {
        return jobType;
    }

    public void setJobType(JobType jobType) {
        this.jobType = jobType;
    }

    public JobStatus getStatus() {
        return status;
    }

    public void setStatus(JobStatus status) {
        this.status = status;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public JobPriority getPriority() {
        return priority;
    }

    public void setPriority(JobPriority priority) {
        this.priority = priority;
    }

    public Long getHiringManagerId() {
        return hiringManagerId;
    }

    public void setHiringManagerId(Long hiringManagerId) {
        this.hiringManagerId = hiringManagerId;
    }

    public Long getHrId() {
        return hrId;
    }

    public void setHrId(Long hrId) {
        this.hrId = hrId;
    }

    public BigDecimal getBillRateFrom() {
        return billRateFrom;
    }

    public void setBillRateFrom(BigDecimal billRateFrom) {
        this.billRateFrom = billRateFrom;
    }

    public BigDecimal getBillRateTo() {
        return billRateTo;
    }

    public void setBillRateTo(BigDecimal billRateTo) {
        this.billRateTo = billRateTo;
    }

    public RateUnitType getBillRateUnitType() {
        return billRateUnitType;
    }

    public void setBillRateUnitType(RateUnitType billRateUnitType) {
        this.billRateUnitType = billRateUnitType;
    }

    public BigDecimal getPayRateFrom() {
        return payRateFrom;
    }

    public void setPayRateFrom(BigDecimal payRateFrom) {
        this.payRateFrom = payRateFrom;
    }

    public BigDecimal getPayRateTo() {
        return payRateTo;
    }

    public void setPayRateTo(BigDecimal payRateTo) {
        this.payRateTo = payRateTo;
    }

    public RateUnitType getPayRateUnitType() {
        return payRateUnitType;
    }

    public void setPayRateUnitType(RateUnitType payRateUnitType) {
        this.payRateUnitType = payRateUnitType;
    }

    public String getKeywords() {
        return keywords;
    }

    public void setKeywords(String keywords) {
        this.keywords = keywords;
    }

    public String getRequiredKeywords() {
        return requiredKeywords;
    }

    public void setRequiredKeywords(String requiredKeywords) {
        this.requiredKeywords = requiredKeywords;
    }

    public String getMinimumDegree() {
        return minimumDegree;
    }

    public void setMinimumDegree(String minimumDegree) {
        this.minimumDegree = minimumDegree;
    }

    public List<String> getPreferredDegrees() {
        return preferredDegrees;
    }

    public void setPreferredDegrees(List<String> preferredDegrees) {
        this.preferredDegrees = preferredDegrees;
    }


    public List<String> getExpLevels() {
        return expLevels;
    }

    public void setExpLevels(List<String> expLevels) {
        this.expLevels = expLevels;
    }

    public Boolean getVisible() {
        return visible;
    }

    public void setVisible(Boolean visible) {
        this.visible = visible;
    }

    public Boolean getInternal() {
        return internal;
    }

    public void setInternal(Boolean internal) {
        this.internal = internal;
    }

    public Integer getOpenings() {
        return openings;
    }

    public void setOpenings(Integer openings) {
        this.openings = openings;
    }

    public Integer getMaxSubmissions() {
        return maxSubmissions;
    }

    public void setMaxSubmissions(Integer maxSubmissions) {
        this.maxSubmissions = maxSubmissions;
    }

    public String getApplicationStats() {
        return applicationStats;
    }

    public void setApplicationStats(String applicationStats) {
        this.applicationStats = applicationStats;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Instant getPostingTime() {
        return postingTime;
    }

    public void setPostingTime(Instant postingTime) {
        this.postingTime = postingTime;
    }

    public Long getDivisionId() {
        return divisionId;
    }

    public void setDivisionId(Long divisionId) {
        this.divisionId = divisionId;
    }

    public Integer getCurrency() {
        return currency;
    }

    public void setCurrency(Integer currency) {
        this.currency = currency;
    }

    public Integer getLeastExperienceYear() {
        return leastExperienceYear;
    }

    public void setLeastExperienceYear(Integer leastExperienceYear) {
        this.leastExperienceYear = leastExperienceYear;
    }

    public Integer getMostExperienceYear() {
        return mostExperienceYear;
    }

    public void setMostExperienceYear(Integer mostExperienceYear) {
        this.mostExperienceYear = mostExperienceYear;
    }

    public LocalDate getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(LocalDate expirationDate) {
        this.expirationDate = expirationDate;
    }

    @Override
    public String toString() {
        return "ResignUserReportJobDTO{" +
                "id=" + id +
                ", tenantId=" + tenantId +
                ", company='" + company + '\'' +
                ", companyId=" + companyId +
                ", department='" + department + '\'' +
                ", title='" + title + '\'' +
                ", jobType=" + jobType +
                ", code='" + code + '\'' +
                ", startDate=" + startDate +
                ", endDate=" + endDate +
                ", expirationDate=" + expirationDate +
                ", postingTime=" + postingTime +
                ", status=" + status +
                ", priority=" + priority +
                ", tags='" + tags + '\'' +
                ", divisionId=" + divisionId +
                ", keywords='" + keywords + '\'' +
                ", requiredKeywords='" + requiredKeywords + '\'' +
                ", visible=" + visible +
                ", internal=" + internal +
                ", addressLine='" + addressLine + '\'' +
                ", city='" + city + '\'' +
                ", province='" + province + '\'' +
                ", country='" + country + '\'' +
                ", zipcode='" + zipcode + '\'' +
                ", hiringManagerId=" + hiringManagerId +
                ", hrId=" + hrId +
                ", currency=" + currency +
                ", billRateFrom=" + billRateFrom +
                ", billRateTo=" + billRateTo +
                ", billRateUnitType=" + billRateUnitType +
                ", payRateFrom=" + payRateFrom +
                ", payRateTo=" + payRateTo +
                ", payRateUnitType=" + payRateUnitType +
                ", openings=" + openings +
                ", maxSubmissions=" + maxSubmissions +
                ", applicationStats='" + applicationStats + '\'' +
                ", minimumDegree='" + minimumDegree + '\'' +
                ", leastExperienceYear=" + leastExperienceYear +
                ", mostExperienceYear=" + mostExperienceYear +
                ", logo='" + logo + '\'' +
                ", preferredDegrees=" + preferredDegrees +
                ", expLevels=" + expLevels +
                '}';
    }
}
