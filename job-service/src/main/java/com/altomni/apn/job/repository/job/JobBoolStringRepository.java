package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.JobBoolString;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the JobBoolString entity.
 */
@Repository
public interface JobBoolStringRepository extends JpaRepository<JobBoolString, Long> {

    List<JobBoolString> findAllByJobId(Long jobId);

    @Query(value = "delete from job_bool_string where job_id = ?1", nativeQuery = true)
    void deleteByByJobId(Long jobId);
}
