//package com.altomni.apn.job.service.dto.job;
//
//import com.altomni.apn.common.domain.AbstractAuditingEntity;
//import com.altomni.apn.common.domain.user.User;
//import com.altomni.apn.common.utils.ServiceUtils;
//import com.altomni.apn.common.domain.job.JobV3;
//import com.altomni.apn.job.domain.job.UserJobRelation;
//import com.fasterxml.jackson.annotation.JsonIgnore;
//import io.swagger.annotations.ApiModelProperty;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import javax.validation.constraints.NotNull;
//import java.io.Serializable;
//import java.util.Set;
//
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//public class UserJobRelationDTO extends AbstractAuditingEntity implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    private Long id;
//
//    @JsonIgnore
//    @ApiModelProperty(value = "user' permissionIntValue on job. It is required when create the entity.")
//    private Integer permission;
//
//    @ApiModelProperty(value = "permission List.")
//    private Set<String> permissionSet;
//
//    @ApiModelProperty(value = "User's own search string on job. This is to train parser's search string given the JD.")
//    private String searchString;
//
//    @ApiModelProperty(value = "user id", required = true)
//    @NotNull
//    private Long userId;
//
//    @ApiModelProperty(value = "job id", required = true)
//    @NotNull
//    private Long jobId;
//
//    // ************************************relate entity*********************************************
//    @ApiModelProperty(value = "user entity. Read Only.")
//    private User user;
//
//    @ApiModelProperty(value = "job entity. Read Only.")
//    private JobV3 job;
//
//    public static UserJobRelationDTO fromUserJobRelation(UserJobRelation userJobRelation) {
//        UserJobRelationDTO dto = new UserJobRelationDTO();
//        ServiceUtils.myCopyProperties(userJobRelation, dto);
//        return dto;
//    }
//
//}
