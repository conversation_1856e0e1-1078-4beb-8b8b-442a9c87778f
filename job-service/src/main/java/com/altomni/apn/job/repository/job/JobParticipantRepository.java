package com.altomni.apn.job.repository.job;

import com.altomni.apn.common.dto.job.ParticipantDTO;
import com.altomni.apn.job.domain.job.JobParticipant;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JobParticipantRepository extends JpaRepository<JobParticipant, Long> {
    void deleteAllByJobId(Long jobId);


    @Query("SELECT new com.altomni.apn.common.dto.job.ParticipantDTO(jp.userId, u.firstName || ' ' || u.lastName) " +
            "FROM JobParticipant jp " +
            "LEFT JOIN User u ON jp.userId = u.id " +
            "WHERE jp.jobId = :jobId")
    List<ParticipantDTO> findParticipantsByJobId(@Param("jobId") Long jobId);
}