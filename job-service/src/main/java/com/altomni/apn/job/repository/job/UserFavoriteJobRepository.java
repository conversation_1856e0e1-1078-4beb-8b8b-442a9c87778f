//jobv3 deprecated
//package com.altomni.apn.job.repository.job;
//
//import com.altomni.apn.job.domain.job.UserFavoriteJob;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Modifying;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//
///**
// * Spring Data JPA repository for the UserFavoriteJob entity.
// */
//@Repository
//@Deprecated
//public interface UserFavoriteJobRepository extends JpaRepository<UserFavoriteJob, Long> {
//
//    List<UserFavoriteJob> findAllByUserId(Long userId);
//
//    List<UserFavoriteJob> findAllByUserIdAndJobIdIn(Long userId, List<Long> jobId);
//
//    @Modifying
//    @Query(value = "delete from user_favorite_job where user_id = ?1 and job_id = ?2", nativeQuery = true)
//    void deleteByUserIdAndJobId(Long userId, Long jobId);
//
//    List<UserFavoriteJob> findAllByJobId(Long favoriteJobId);
//
//    List<UserFavoriteJob> findAllByJobIdAndUserId(Long jobId, Long userId);
//}
