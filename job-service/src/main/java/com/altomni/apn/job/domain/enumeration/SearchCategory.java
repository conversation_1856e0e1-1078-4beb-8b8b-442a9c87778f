package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.Optional;

public enum SearchCategory implements ConvertedEnum<Integer> {
    //OPEN， ONHOLD CLOSED FILLED, these are using the same db value from enum JobStatus
    OPEN(0, "0:OPEN", "open", "Open", "进展中"),
    ONHOLD(2, "2:ONHOLD", "onhold", "On Hold", "暂停"),
    CLOSED(4, "3:CLOSED", "closed", "Closed", "已关闭"),
    FILLED(5, "1:FILLED", "filled", "Filled", "已招到"),
    LOST(6, "25:LOST", "lost", "Lost", "丢单"),
    PRIVATE_JOBS(100, "100:PRIVATE_JOBS", "privateJobs", "Private Jobs", "保密职位"),
    MYJOBS(1000, "1000:MYJOBS", "my", "My Jobs", "我的职位"),

    ALLJOBS(1001, "1001:ALLJOBS", "all", "All jobs", "所有职位"),

    CUSTOMED(2001, "2001:CUSTOMED", "customed", "Customed Folder", "自定义文件夹"),

    SHARED(2002, "2002:SHARED", "shared", "Shared Folder", "分享文件夹"),

    NOCATEGORY(10000, "10000:NOCATEGORY", "nocategory", "No Category", "未指定");


    private final int dbValue;

    private final String name;

    private final String ESKey;
    private final String labelEn;
    private final String labelCn;

    SearchCategory(int dbValue, String name, String ESKey, String labelEn, String labelCn) {
        this.dbValue = dbValue;
        this.name = name;
        this.ESKey = ESKey;
        this.labelEn = labelEn;
        this.labelCn = labelCn;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getLabelEn() {
        return labelEn;
    }

    public String getLabelCn() {
        return labelCn;
    }

    public String getESKey() {
        return ESKey;
    }

    public static final ReverseEnumResolver<SearchCategory, Integer> resolver =
            new ReverseEnumResolver<>(SearchCategory.class, SearchCategory::toDbValue);

    public static SearchCategory fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }


    public static Optional<SearchCategory> fromDbValue(int dbValue) {
        for (SearchCategory status : values()) {
            if (status.toDbValue() == dbValue) {
                return Optional.of(status);
            }
        }
        return Optional.empty();
    }

    public static boolean isValid(String str) {
        for (SearchCategory category : SearchCategory.values()) {
            //build in name() method
            if (category.name().equals(str)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Use Folder page for statistics
     * @param ESKey
     * @return
     */
    public static SearchCategory fromESKey(String ESKey){
        int minValue = SearchCategory.OPEN.getDbValue();
        int maxValue = SearchCategory.ALLJOBS.getDbValue() + 1;
        for (SearchCategory category : SearchCategory.values()) {
            if(category.dbValue < minValue || category.dbValue >= maxValue ){
                continue;
            }

            if (category.getESKey().equalsIgnoreCase(ESKey)) {
                return category;
            }
        }
        return SearchCategory.NOCATEGORY;
    }


}
