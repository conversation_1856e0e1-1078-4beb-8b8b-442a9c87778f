package com.altomni.apn.job.service.aisourcing;

import com.altomni.apn.job.service.dto.aisourcing.AiSourcingSearchDTO;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;

import java.io.IOException;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface JobAiSourcingService {

    void saveAll(Set<Long> jobIds);

    void enableAiSourcing(Long jobId) throws IOException;

    void disableAiSourcing(Long jobId);

    String getAiSourcingProgress(Long jobId, boolean refresh) throws IOException;

    String getAiSourcingResult(Long jobId, Pageable pageable, HttpHeaders headers) throws IOException;

    String searchAiSourcingResult(Long jobId, AiSourcingSearchDTO aiSourcingSearchDTO, Pageable pageable, HttpHeaders headers) throws IOException;

    String getTalentDetail(String profileId) throws IOException;
}
