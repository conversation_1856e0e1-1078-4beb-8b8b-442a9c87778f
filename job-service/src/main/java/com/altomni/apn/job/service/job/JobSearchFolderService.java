package com.altomni.apn.job.service.job;

import com.altomni.apn.common.dto.folder.FolderSearchRequestDTO;
import com.altomni.apn.job.service.dto.folder.JobSearchFolderDTO;
import com.altomni.apn.job.service.dto.folder.JobSearchFolderStatusDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface JobSearchFolderService {

    /***
     * find all search folders entity by user id with pagination
     * @param pageable
     * @param userId
     * @return Page object, content is search folder dto
     */
    Page<JobSearchFolderDTO> getAllJobSearchFolders(Pageable pageable, Long userId);

    /***
     * creat a new job search folder entity by DTO
     * @param jobSearchFolderDTO
     * @return
     */
    JobSearchFolderDTO createJobSearchFolder(JobSearchFolderDTO jobSearchFolderDTO);


    /***
     * delete the job search folder entity
     * @param id, job search folder id cannot be null
     */
    void deleteJobSearchFolder(Long id);


    JobSearchFolderDTO getJobSearchFolder(Long folderId);

    /**
     * get jobSearch folder with update status
     * @param folderId
     * @return
     */
    JobSearchFolderStatusDTO getJobSearchFolderWithStatus(Long folderId);

    /*
    * Disable the search param after updating the sharing relation
    * */


    /**
     * disable all search folder after delete the jobFolderId
     * @param jobFolderId
     */
    void disableSearchFolder(Long jobFolderId);

    /**
     * disable the current folder param for team shared folder is removed
     * @param jobFolderId
     * @param teamIds
     */
    void disableSearchFolderWithFolderParamFromTeamShared(Long jobFolderId, List<Long> teamIds);

    /**
     * recover the folder param in search folder after the sharing is granted again
     * @param jobFolderId
     * @param teamIds
     */
    void enableSearchFolderWithFolderParamFromTeamShared(Long jobFolderId, List<Long> teamIds);

    /**
     * disable the folder param for the user shared folder is removed
     * @param jobFolderId
     * @param userIds
     */
    void disableSearchFolderWithFolderParamFromUserShared(Long jobFolderId, List<Long> userIds);

    /**
     * recover the folder param in search folder after previous sharing is granted again
     * @param jobFolderId
     * @param userIds
     */
    void enableSearchFolderWithFolderParamFromUserShared(Long jobFolderId, List<Long> userIds);

    /***
     * find all SearchFolders which meet the condition from searchRequestDTO
     * @param searchRequestDTO
     * @param pageable
     * @return
     */
    Page<JobSearchFolderStatusDTO> findSearchFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable);
}
