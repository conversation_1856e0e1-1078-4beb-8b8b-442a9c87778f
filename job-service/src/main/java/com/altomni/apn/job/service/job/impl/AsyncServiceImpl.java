package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.ContactCategoryType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.dto.job.JobBoolStringDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.company.repository.business.AccountBusinessAdministratorRepository;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.job.JobBoolString;
import com.altomni.apn.job.domain.job.JobCompanyContactRelation;
import com.altomni.apn.job.domain.job.UserJobRelation;
import com.altomni.apn.job.repository.job.JobBoolStringRepository;
import com.altomni.apn.job.repository.job.JobCompanyContactRelationRepository;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.repository.job.UserJobRelationRepository;
import com.altomni.apn.job.service.application.ApplicationService;
import com.altomni.apn.job.service.company.CompanyService;
import com.altomni.apn.job.service.dto.job.JobThirdPartyDTO;
import com.altomni.apn.job.service.job.AsyncService;
import com.altomni.apn.job.service.job.JobRelationService;
import com.altomni.apn.job.service.job.JobService;
import com.altomni.apn.job.service.mail.MailService;
import com.altomni.apn.job.service.user.UserService;
import com.altomni.apn.job.service.vo.job.JobVO;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Transactional
public class AsyncServiceImpl implements AsyncService {

    private final Logger log = LoggerFactory.getLogger(AsyncServiceImpl.class);

    private final JobRepository jobRepository;

    private final JobBoolStringRepository jobBoolStringRepository;

    private final CompanyService companyService;

    private final JobCompanyContactRelationRepository jobCompanyContactRelationRepository;

    private final ApplicationService applicationService;

    private final JobService jobService;

    private final CommonApiMultilingualConfig commonApiMultilingualConfig;

    private final AccountBusinessAdministratorRepository accountBusinessAdministratorRepository;

    private final JobApiPromptProperties jobApiPromptProperties;

    private final UserJobRelationRepository userJobRelationRepository;

    private final MailService mailService;

    private final CachePermissionWriteOnly cachePermissionWriteOnly;

    @Resource
    private final UserService userService;

    private final static String HTML_A_HREF_PREFIX = "<a href=\"" ;

    private final static String HTML_A_HREF_SUFFIX = "</a>";

    private final static String RIGHT_BRACKET = "\">";

    @Value("${job_detail_url}")
    String jobDetailUrl;

    public AsyncServiceImpl(JobRepository jobRepository, JobBoolStringRepository jobBoolStringRepository, CompanyService companyService,
                            UserJobRelationRepository userJobRelationRepository, JobCompanyContactRelationRepository jobCompanyContactRelationRepository,
                            ApplicationService applicationService,  JobService jobService,CommonApiMultilingualConfig commonApiMultilingualConfig,
                            JobApiPromptProperties jobApiPromptProperties,AccountBusinessAdministratorRepository accountBusinessAdministratorRepository,
                            MailService mailService, CachePermissionWriteOnly cachePermissionWriteOnly, UserService userService) {
        this.jobRepository = jobRepository;
        this.jobBoolStringRepository = jobBoolStringRepository;
        this.companyService = companyService;
        this.userJobRelationRepository = userJobRelationRepository;
        this.jobCompanyContactRelationRepository = jobCompanyContactRelationRepository;
        this.applicationService = applicationService;
        this.jobService = jobService;
        this.commonApiMultilingualConfig = commonApiMultilingualConfig;
        this.jobApiPromptProperties = jobApiPromptProperties;
        this.accountBusinessAdministratorRepository = accountBusinessAdministratorRepository;
        this.mailService =mailService;
        this.cachePermissionWriteOnly = cachePermissionWriteOnly;
        this.userService = userService;
    }

    /**
     * Sync jobs from third party
     * @param jobThirdPartyDTO JobThirdPartyDTO entity
     * @return created Job entity
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public JobVO syncFromThirdParty(JobThirdPartyDTO jobThirdPartyDTO) throws IOException {
        if (StringUtils.isEmpty(jobThirdPartyDTO.getCode())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SYNCFROMTHIRDPARTY_CODENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        validateInput(jobThirdPartyDTO);

        JobV3 codeJob = jobRepository.findFirstByCode(jobThirdPartyDTO.getCode());
        if (codeJob != null) {
            return updateJobForThirdParty(jobThirdPartyDTO, codeJob);
        }

        return createJobForThirdParty(jobThirdPartyDTO);
    }

    private void sendEmailToAM(JobV3 codeJob) {
        List<String> userIdList;
        if (codeJob.getSalesLeadId() != null) {
            userIdList = accountBusinessAdministratorRepository.findAmUserIdByCompanyIdAndSalesLeadId(codeJob.getSalesLeadId(), codeJob.getCompanyId());
        } else {
            userIdList = accountBusinessAdministratorRepository.findAmUserIdByCompanyId(codeJob.getCompanyId());
        }
        log.info("[APN: updateJobForThirdParty @{}] send email email address: {}", codeJob.getCompanyId(), JSONUtil.toJsonStr(userIdList));
        if (!userIdList.isEmpty()) {
            CompanyDTO companyDTO = companyService.getCompany(codeJob.getCompanyId()).getBody();
            StringBuilder sb = new StringBuilder();
            sb.append("<body>");
            HtmlUtil.appendParagraphCell(sb, "Job closed for " + companyDTO.getName() + " " + codeJob.getCode() + ": " + codeJob.getTitle());

            HtmlUtil.appendParagraphCell(sb, " For details, please refer to " + HTML_A_HREF_PREFIX + jobDetailUrl + codeJob.getId() + RIGHT_BRACKET + codeJob.getTitle() + HTML_A_HREF_SUFFIX);
            sb.append("</body>");
            MailVM mailVM = new MailVM();
            mailVM.setContent(sb.toString());
            mailVM.setSubject("Job closed for " + companyDTO.getName() + " " + codeJob.getCode() + " " + codeJob.getTitle());
            mailVM.setTo(userIdList);
            mailVM.setFrom("<EMAIL>");
            mailVM.setIsSystemEmail(true);
            mailService.sendHtmlMail(mailVM);
        }
    }


    private void validateInput(JobThirdPartyDTO jobThirdPartyDTO) {
        if(jobThirdPartyDTO.getRecruitmentProcessId() != null){
            RecruitmentProcessVO recruitmentProcessVO = applicationService.getRecruitmentProcessBriefById(jobThirdPartyDTO.getRecruitmentProcessId()).getBody();
            if (recruitmentProcessVO != null) {
                if (!recruitmentProcessVO.getTenantId().equals(SecurityUtils.getTenantId())) {
                    throw new ForbiddenException("You are not authorized to access this recruitmentProcess!");
                }
                if (recruitmentProcessVO.getJobType() == null) {
                    throw new ForbiddenException("Job Type is null.");
                }
            }
        }
    }


    private JobVO createJobForThirdParty(JobThirdPartyDTO jobThirdPartyDTO) throws IOException {
        log.info("[APN: createJobForThirdParty @{}] REST request to create the job from third party: {}", -1, jobThirdPartyDTO);
        JobV3 job = JobThirdPartyDTO.fromDto(jobThirdPartyDTO);
        job.setStatus(jobThirdPartyDTO.getStatus());
        job.setTenantId(SecurityUtils.getTenantId());// use the account tenant;
        JobV3 result = jobRepository.saveAndFlush(job);

        //保存assignedUsers
        saveJobAssignedUsers(jobThirdPartyDTO.getAssignedUsers(), result.getId());

        JobCompanyContactRelation relation = new JobCompanyContactRelation();
        relation.setContactCategory(ContactCategoryType.HIRING_MANAGER.toDbValue());
        relation.setClientContactId(jobThirdPartyDTO.getHiringManagerId());
        relation.setJobId(result.getId());
        jobCompanyContactRelationRepository.saveAndFlush(relation);

        jobService.saveJobLocationsV3(job.getId(), jobThirdPartyDTO.getLocations(), true);

        return toDto(result);
    }

    //TODO:
    private JobVO updateJobForThirdParty(JobThirdPartyDTO jobThirdPartyDTO, JobV3 existingJob) throws IOException {
        log.info("[APN: updateJobForThirdParty @{}] REST request to update the job from third party: {}", -1, jobThirdPartyDTO);
        JobThirdPartyDTO.updateCopy(jobThirdPartyDTO, existingJob);

        JobV3 result = jobRepository.save(existingJob);

        //set client contact;
        List<JobCompanyContactRelation> relations = jobCompanyContactRelationRepository.findAllByJobId(result.getId());
        JobCompanyContactRelation relation;
        if(relations.size() == 1){
            relation = relations.get(0);

        }else{
            relation = new JobCompanyContactRelation();
        }
        relation.setContactCategory(ContactCategoryType.HIRING_MANAGER.toDbValue());
        relation.setClientContactId(jobThirdPartyDTO.getHiringManagerId());
        relation.setJobId(result.getId());
        jobCompanyContactRelationRepository.save(relation);

        //保存assignedUsers
        saveJobAssignedUsers(jobThirdPartyDTO.getAssignedUsers(), result.getId());

        jobService.saveJobLocationsV3(existingJob.getId(), jobThirdPartyDTO.getLocations(), false);

        if (jobThirdPartyDTO.getStatus().equals(JobStatus.CLOSED)) {
            sendEmailToAM(existingJob);
        }
        return toDto(result);
    }

    private JobVO toDto(JobV3 job) {
        if (job == null) {
            return null;
        }
        JobVO result = JobVO.fromJob(job);
        List<JobBoolStringDTO> boolStringDTOS = new ArrayList<>();
        for (JobBoolString jobBoolString : jobBoolStringRepository.findAllByJobId(job.getId())) {
            JobBoolStringDTO boolStringDTO = new JobBoolStringDTO();
            ServiceUtils.myCopyProperties(jobBoolString, boolStringDTO);
            boolStringDTO.setStrings(JsonUtil.fromJsonArray(jobBoolString.getStrings()));
            boolStringDTOS.add(boolStringDTO);
        }
        result.setBoolstr(boolStringDTOS);

        result.setLocations(jobService.getJobLocation(job.getId()));
        return result;
    }

    private void saveJobAssignedUsers(List<AssignedUserDTO> assignedUsers, Long jobId) {
        log.info("save job assigned User for job: {}", jobId);
        //如果assignedUsers，不处理
        if (CollectionUtils.isEmpty(assignedUsers)) {
            return;
        }
        // 使用HashSet去重
        Set<Long> uniqueUserIds = assignedUsers.stream()
                .map(AssignedUserDTO::getUserId)
                .collect(Collectors.toSet());
        if (uniqueUserIds.isEmpty()) {
            return;
        }

        // 批量查询用户信息
        List<UserBriefDTO> userList = Optional.ofNullable(userService.getAllBriefUsersByIds(new ArrayList<>(uniqueUserIds)).getBody())
                .orElseThrow(() -> new CustomParameterizedException(getErrorMessage(JobAPIMultilingualEnum.JOBRELATION_SAVEJOBASSIGNEDUSERS_USERNULL)));

        // 提前验证租户一致性
        Long currentTenantId = SecurityUtils.getTenantId();
        Map<Long, UserBriefDTO> validUsers = userList.stream()
                .filter(user -> user.getTenantId().equals(currentTenantId))
                .collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));

        if (validUsers.size() != uniqueUserIds.size()) {
            throw new CustomParameterizedException(getErrorMessage(JobAPIMultilingualEnum.JOBRELATION_SAVEJOBASSIGNEDUSERS_NOPERMISSION));
        }

        // 查询现有关系并使用Set优化查询性能
        Set<Long> existingUserIds = userJobRelationRepository.findAllByJobId(jobId).stream()
                .map(UserJobRelation::getUserId)
                .collect(Collectors.toSet());

        // 使用流处理构建新关系
        List<UserJobRelation> newRelations = uniqueUserIds.stream()
                .filter(userId -> !existingUserIds.contains(userId))
                .map(userId -> createUserJobRelation(userId, jobId))
                .collect(Collectors.toList());


        // 批量保存和缓存处理
        if (CollectionUtils.isNotEmpty(newRelations)) {
            userJobRelationRepository.saveAll(newRelations);
            newRelations.stream()
                    .map(UserJobRelation::getUserId)
                    .distinct()
                    .forEach(cachePermissionWriteOnly::deleteDataPermissionCacheByUserId);
        }
    }

    //创建用户岗位关系对象
    private UserJobRelation createUserJobRelation(Long userId, Long jobId) {
        UserJobRelation relation = new UserJobRelation();
        relation.setUserId(userId);
        relation.setJobId(jobId);
        return relation;
    }

    //异常消息构造
    private String getErrorMessage(JobAPIMultilingualEnum errorEnum) {
        return commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(
                errorEnum.getKey(),
                CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),
                jobApiPromptProperties.getJobService()
        );
    }

}
