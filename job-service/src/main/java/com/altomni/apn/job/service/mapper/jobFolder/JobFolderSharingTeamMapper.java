package com.altomni.apn.job.service.mapper.jobFolder;

import com.altomni.apn.job.domain.job.JobFolderSharingTeam;
import com.altomni.apn.job.service.dto.folder.JobFolderSharingDTO;
import com.altomni.apn.job.service.dto.folder.JobFolderSharingTeamDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public interface JobFolderSharingTeamMapper {

    @Mapping(target = "excludedUserIds", source = "excludedUserIds", qualifiedByName = "jsonStringToSet")
    JobFolderSharingTeamDTO toDto(JobFolderSharingTeam entity);

    @Mapping(target = "targetId", source = "teamId")
    @Mapping(target = "targetCategory", constant = "TEAM")
    JobFolderSharingDTO toFolderSharingDto(JobFolderSharingTeam entity);

    @Mapping(target = "targetId", source = "teamId")
    @Mapping(target = "targetCategory", constant = "TEAM")
    List<JobFolderSharingDTO> toFolderSharingDto(List<JobFolderSharingTeam> entities);

    @Mapping(target = "excludedUserIds", source = "excludedUserIds", qualifiedByName = "setToJsonString")
    JobFolderSharingTeam toEntity(JobFolderSharingTeamDTO dto);

    @Mapping(target = "teamId", source = "targetId")
    JobFolderSharingTeam toEntity(JobFolderSharingDTO dto);

    @Named("jsonStringToSet")
    default Set<Long> jsonStringToSet(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return new HashSet<>();
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.readValue(jsonString, new TypeReference<Set<Long>>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error parsing JSON string", e);
        }
    }

    @Named("setToJsonString")
    default String setToJsonString(Set<Long> set) {
        if (set == null) {
            return null;
        }
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            return objectMapper.writeValueAsString(set);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting set to JSON string", e);
        }
    }
}
