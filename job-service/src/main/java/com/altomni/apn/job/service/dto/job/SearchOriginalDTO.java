package com.altomni.apn.job.service.dto.job;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class SearchOriginalDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private List<JSONObject> and;

//    public static SearchOriginalDTO getSearchConditionDTO() {
//        SearchOriginalDTO conditionDTO = new SearchOriginalDTO();
//        JSONObject affiliations = new JSONObject();
//        affiliations.put("affiliations", Arrays.asList("all", "user_" + SecurityUtils.getUserId()));
//        conditionDTO.setAnd(Collections.singletonList(affiliations));
//        return conditionDTO;
//    }

}
