package com.altomni.apn.job.web.rest.job;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.job.JobPost;
import com.altomni.apn.job.repository.job.JobPostRepository;
import com.altomni.apn.job.service.company.CompanyService;
import com.altomni.apn.job.service.job.JobPostService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for job posting.
 */
@Api(tags = {"Job Post"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class JobPostResource {

    private static final String ENTITY_NAME = "jobPost";

    @Resource
    private JobPostRepository jobPostRepository;

    @Resource
    private JobPostService jobPostService;
    
    @Resource
    private CompanyService companyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    /**
     * POST  /jobPosts : Create a new jobPost.
     *
     * @param jobPost the tenant to create
     * @return the ResponseEntity with status 201 (Created) and with body the jazzHR job id,
     * or with status 401 if not authorized
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/job-posts")
    @NoRepeatSubmit
    public ResponseEntity createPostJob(@Valid @RequestBody JobPost jobPost) throws URISyntaxException {
        log.info("[APN: JobPost @{}] REST request to save with job id :{}, JobPost : {}", SecurityUtils.getUserId(), jobPost.getJobId(), jobPost);
        if (jobPost.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEPOSTJOB_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        Long currentUser = SecurityUtils.getUserId();
        List<Long> amIds = companyService.getAllAmByJob(jobPost.getId()).getBody();
        if (!(amIds != null ? amIds.contains(currentUser) : false)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEPOSTJOB_NOPRIVILEGE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        // Check whether the user has a JazzHR account or not
        if (!jobPostService.checkUserExisting(currentUser)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEPOSTJOB_NOJAZZHR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        jobPost.setPrimaryRecruiterId(SecurityUtils.getUserId());

        jobPost = jobPostService.postJobByJobId(jobPost.getJobId(), jobPost);

        JobPost result = jobPostRepository.save(jobPost);
        return ResponseEntity.created(new URI("/api/jobPosts/" + result.getId()))
            .body(result);
    }

    /**
     * GET  /jobPosts : requestGet all the jobPosts.
     *
     * @return the ResponseEntity with status 200 (OK) and the jobPost information
     */
    @GetMapping("/job-posts")
    public ResponseEntity<List<JobPost>> getAllJobPosts() {
        log.info("[APN: JobPost @{}] REST request to get all JobPosts", SecurityUtils.getUserId());
        List<JobPost> jobPosts = jobPostRepository.findAll();

        return ResponseEntity.ok().body(jobPosts);
    }

    /**
     * GET  /jobPosts : requestGet all the jobPosts.
     *
     * @return the ResponseEntity with status 200 (OK) and the jobPost information
     */
    @GetMapping("/my-job-posts/")
    public ResponseEntity<List<JobPost>> getMyJobPosts() {
        log.info("[APN: JobPost @{}] REST request to get my JobPosts", SecurityUtils.getUserId());
        List<JobPost> jobPosts = jobPostRepository.findAllByPrimaryRecruiterId(SecurityUtils.getUserId());

        return ResponseEntity.ok().body(jobPosts);
    }

    /**
     * GET  /validUser : requestGet all the jobPosts.
     *
     * @return the ResponseEntity with status 200 (OK
     * or with status 404 (Not Found) if the user does not exist in JazzHR system
     */
    @GetMapping("/job-posts/valid-user/{id}")
    public ResponseEntity<Void> checkJazzHRValidUser(@PathVariable Long id) {
        log.info("[APN: JobPost @{}] REST request to requestGet all JobPosts", SecurityUtils.getUserId());
        if (!jobPostService.checkUserExisting(id)) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok().build();
    }

}
