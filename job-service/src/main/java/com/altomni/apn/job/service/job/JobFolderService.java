package com.altomni.apn.job.service.job;

import com.altomni.apn.common.dto.folder.*;
import com.altomni.apn.job.domain.job.JobFolder;
import com.altomni.apn.job.domain.job.JobFolderDetail;
import com.altomni.apn.job.service.dto.folder.*;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

public interface JobFolderService {

    /***
     * return all job folder information, including sharing information(to team and to user) and relation information(jobs inside this folder)
     * @param userId
     * @param pageable
     * @return page object, content is JobFolderDetail list
     */
    Page<JobFolderDetail> getJobFolderDetailsByUserId(Long userId, Pageable pageable);


    /**
     * get all folder with readwrite permission, includes my folder and shared to me
     * @return
     */
    FolderListDTO getCollaborativeJobFolderList();

    /***
     * Get all custom and shared job folder list
     * @return
     */
    FolderListDTO getCustomAndSharedJobFolderList();

    FolderPermissionListDTO getCustomAndSharedJobFolderWithPermissionList();

    JobFolderAndJobsDTO addJobsInNewFolder(JobFolderAndJobsDTO jobFolderAndJobsDTO);

    /**
     * create a new job Folder entity with the job sharing entities
     * @param jobFolderDTO
     * @return jobfolderDTO
     */
    JobFolderDTO createJobFolder(JobFolderDTO jobFolderDTO);

    /***
     * update existing job folder
     * @param jobFolderDTO
     * @param jobFolderId
     * @return jobfolderDTO after update
     */
    JobFolderDTO updateJobFolder(JobFolderDTO jobFolderDTO, Long jobFolderId);

    /***
     * delete the job search folder by id
     * @param folderId
     */
    void deleteFolder(Long folderId);

    /***
     * add jobs to the folders
     * @param jobFolderRelationListDTO, including list of jobs and list of folders
     */
    void addJobsToFolders(JobFolderRelationListDTO jobFolderRelationListDTO);

    /***
     * remove selected jobs from the folder by id
     * @param jobFolderRelationListDTO, only have list of jobs, list of folders is not used
     * @param folderId
     */
    void deleteJobsFromFolder(JobFolderRelationListDTO jobFolderRelationListDTO, Long folderId);

    /***
     * remove all shared job folder for current user; will not delete the folder
     * @param folderId
     */
    void removeSharingForSharedFolder(Long folderId);

    /***
     * get all folders which shared with current user, including team and user sharing
     * @param userId
     * @param teamId
     * @return
     */
    List<SharedFolderBriefDTO> getSharedFolderList(Long userId, Long teamId);

    /***
     * get all job folder under current login user
     * @param userId
     * @param pageable
     * @return page data of JobFolderBriefDTO
     */
    Page<JobFolderBriefDTO> getMyJobFolderWithCountListByUserId(Long userId, Pageable pageable);

    List<FolderSharedTeamDTO> getDistinctSharedTeamsForSharedFolderByUserId(Long userId);

    /***
     * get shared user list for the shared folder which shared to me
     * @param userId
     * @return List<FolderSharedUserDTO>
     */

    List<FolderSharedUserDTO> getDistinctSharedUsersForSharedFolderByUserId(Long userId);

    /***
     * get distinct user and team list for the shared folders which also shared to me
     * @return
     */
    FolderSharedUserAndTeamDTO getSharedUserAndTeamList();

    /*
    *          sub folder menu
    */
    //Page<SearchJobFolderDTO> searchJobFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable);


    Boolean validateFolderInSearch(List<Long> folderIds);

    List<FolderSharedTeamDTO> getSharedTeamsByUserId();


    List<FolderSharedUserDTO> getSharedUsersByUserId();

    List<FolderSharedTeamDTO> getDistinctSharedTeamsByUserId();

    List<FolderSharedUserDTO> getDistinctSharedUsersByUserId();


    Optional<FolderCreatorIdDTO> getJobFolderCreator(Long folderId);

    List<JobFolder> getJobFolders(List<Long> folderIds);

    Page<SearchJobFolderDTO> searchJobFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable);
}
