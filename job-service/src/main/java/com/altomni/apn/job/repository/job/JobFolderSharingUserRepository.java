package com.altomni.apn.job.repository.job;


import com.altomni.apn.common.dto.folder.FolderNamePermissionDTO;
import com.altomni.apn.common.dto.folder.FolderSharedUserDTO;
import com.altomni.apn.job.domain.job.JobFolderSharingUser;
import com.altomni.apn.job.service.dto.folder.SharedFolderBriefDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface JobFolderSharingUserRepository extends JpaRepository<JobFolderSharingUser, Long> {
    List<JobFolderSharingUser> findAllByJobFolderId(Long jobFolderId);

    Optional<JobFolderSharingUser> findByJobFolderIdAndUserId(Long folderId, Long userId);

    List<JobFolderSharingUser> findAllByJobFolderIdInAndUserId(List<Long> folderIds, Long userId);


    @Query("SELECT new com.altomni.apn.job.service.dto.folder.SharedFolderBriefDTO(jf.id, jf.name, jfs.permission, COUNT(jfr.id)) " +
            "FROM JobFolderSharingUser jfs " +
            "LEFT JOIN JobFolder jf ON jfs.jobFolderId = jf.id " +
            "LEFT JOIN JobFolderRelation jfr ON jfr.jobFolderId = jf.id " +
            "WHERE (jfs.userId = :userId) " +
            "GROUP BY jf.id, jf.name, jfs.permission")
    List<SharedFolderBriefDTO> findBriefBySharedUserId(@Param("userId") Long userId);

    @Query("SELECT new com.altomni.apn.common.dto.folder.FolderSharedUserDTO(jf.id, u.id, CONCAT(u.firstName, ' ', u.lastName), jf.permissionUserId, jfsu.permission) " +
            "FROM JobFolder jf " +
            "JOIN JobFolderSharingUser jfsu ON jf.id = jfsu.jobFolderId " +
            "JOIN User u ON jfsu.userId = u.id " +
            "WHERE jf.permissionUserId = :userId")
    List<FolderSharedUserDTO> findJobFolderSharedUsersByUserId(@Param("userId") Long userId);

    @Query("SELECT DISTINCT new com.altomni.apn.common.dto.folder.FolderSharedUserDTO(u.id, u.firstName, u.lastName) " +
            "FROM JobFolder jf " +
            "JOIN JobFolderSharingUser jfsu ON jf.id = jfsu.jobFolderId " +
            "JOIN User u ON jfsu.userId = u.id " +
            "WHERE jf.permissionUserId = :userId")
    List<FolderSharedUserDTO> findDistinctJobFolderSharedUsersByUserId(@Param("userId") Long userId);

    @Query("SELECT DISTINCT new com.altomni.apn.common.dto.folder.FolderSharedUserDTO(ufr1.userId, u.firstName, u.lastName) " +
            "FROM JobFolderSharingUser ufr1 " +
            "INNER JOIN JobFolderSharingUser ufr2 ON ufr1.jobFolderId = ufr2.jobFolderId " +
            "INNER JOIN User u ON ufr1.userId = u.id " +
            "WHERE ufr2.userId = :userId")
    List<FolderSharedUserDTO> findDistinctJobFolderSharedUsersForSharedFolderByUserId(@Param("userId") Long userId);



    @Query("SELECT new com.altomni.apn.common.dto.folder.FolderSharedUserDTO(jfsu.jobFolderId, jfsu.userId, u.firstName, u.lastName ,jfsu.permissionUserId, jfsu.permission) " +
            "FROM JobFolderSharingUser jfsu " +
            "JOIN User u ON jfsu.userId = u.id " +
            "WHERE jfsu.jobFolderId IN (:folderIds)")
    List<FolderSharedUserDTO> findJobFolderSharingUserByJobFolderIdIn(@Param("folderIds") List<Long> folderIds);


    @Query("SELECT DISTINCT new com.altomni.apn.common.dto.folder.FolderNamePermissionDTO(jfsu.jobFolderId, f.name, jfsu.permission) " +
            "FROM JobFolderSharingUser jfsu " +
            "JOIN JobFolder f on jfsu.jobFolderId = f.id " +
            "WHERE jfsu.userId = :userId ")
    List<FolderNamePermissionDTO> findUserSharedFolderByUserId(@Param("userId") Long userId);

}
