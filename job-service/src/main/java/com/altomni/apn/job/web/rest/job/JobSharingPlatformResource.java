package com.altomni.apn.job.web.rest.job;


import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.wechat.WechatJSAPIConfig;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageType;
import com.altomni.apn.job.domain.job.JobPosterImage;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPlatformDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPosterDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPosterImageDTO;
import com.altomni.apn.job.service.jobsharing.JobSharingPlatformService;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPlatformVO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPosterImageVO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPosterVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Api(tags = {"Job", "ATS-Jobs"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class JobSharingPlatformResource {

    @Resource
    private JobSharingPlatformService jobSharingPlatformService;



    @PostMapping("/job-sharing/jobs")
    @NoRepeatSubmit
    public ResponseEntity<JobSharingPlatformDTO> createJobSharing(@Valid @RequestBody JobSharingPlatformDTO jobSharingPlatformDTO) {
        log.info("[APN: Job @{}] REST request to get Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        JobSharingPlatformDTO result  = jobSharingPlatformService.createJobSharing(jobSharingPlatformDTO);
        return ResponseEntity.ok(result);
    }

    @PutMapping("/job-sharing/jobs")
    @NoRepeatSubmit
    public ResponseEntity<JobSharingPlatformDTO> updateJobSharing(@Valid @RequestBody JobSharingPlatformDTO jobSharingPlatformDTO) {
        log.info("[APN: Job @{}] REST request to get Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        JobSharingPlatformDTO result  = jobSharingPlatformService.updateJobSharing(jobSharingPlatformDTO);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/job-sharing/job-latest/{jobId}")
    public ResponseEntity<JobSharingPlatformVO> getLatestJobSharing(@ApiParam(value = "job id", required = true) @PathVariable("jobId") Long jobId) {
        log.info("[APN: Job @{}] REST request to get Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        JobSharingPlatformVO result  = jobSharingPlatformService.getLatestJobSharing(jobId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/job-sharing/jobs-all/{jobId}")
    public ResponseEntity<List<JobSharingPlatformDTO>> getAllJobSharing(@ApiParam(value = "job id", required = true) @PathVariable("jobId") Long jobId) {
        log.info("[APN: Job @{}] REST request to get Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        List<JobSharingPlatformDTO> result  = jobSharingPlatformService.getAllJobSharingByJobId(jobId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/job-sharing/public-job-url/{uuid}")
    public ResponseEntity<String> getJobSharingByUUID(@PathVariable("uuid") String uuid) {
        log.info("[APN: Job @{}] REST request to get Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        String result  = jobSharingPlatformService.getJobSharingHTMLURLFromS3ByUUID(uuid);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/job-sharing/js-sdk-config/{uuid}")
    public ResponseEntity<WechatJSAPIConfig> getJSAPIConfig(@PathVariable("uuid") String uuid) {
        log.info("[APN: Job @{}] REST request to get Job : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        WechatJSAPIConfig result  = jobSharingPlatformService.getWechatJsApiConfig(uuid);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/job-sharing/poster-image/search")
    public ResponseEntity<List<JobSharingPosterImageVO>> searchJobPosterImage(@RequestParam(name = "type", required = false) PosterImageType type, @PageableDefault @SortDefault(sort = "createdDate", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("[APN: Job @{}] REST request to search Job sharing poster image.", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        Page<JobPosterImage> page = jobSharingPlatformService.searchJobPosterImage(type, pageable);
        List<JobSharingPosterImageVO> result = page.getContent().stream().map(JobPosterImage::toJobSharingPosterImageVO).toList();
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/api/v3/job-sharing/poster-image/search");
        headers.add("Access-Control-Expose-Headers","Pagination-Count");
        return ResponseEntity.ok().headers(headers).body(result);
    }

    @PostMapping("/job-sharing/poster-image-bulk")
    public ResponseEntity<List<JobSharingPosterImageVO>> bulkSaveJobPosterImage(@Valid @RequestBody List<JobSharingPosterImageDTO> jobSharingPosterImageDTOList) throws IOException {
        log.info("[APN: Job @{}] REST request to bulk save Job sharing poster image : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobSharingPosterImageDTOList);
        List<JobSharingPosterImageVO> result = jobSharingPlatformService.bulkSaveJobPosterImage(jobSharingPosterImageDTOList);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    @PostMapping("/job-sharing/poster-bulk")
    public ResponseEntity<List<JobSharingPosterVO>> bulkSaveJobPoster(@Valid @RequestBody JobSharingPosterDTO jobSharingPosterDTO) {
        log.info("[APN: Job @{}] REST request to bulk save Job sharing poster : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobSharingPosterDTO);
        List<JobSharingPosterVO> result = jobSharingPlatformService.bulkSaveJobPoster(jobSharingPosterDTO);
        return new ResponseEntity<>(result, HttpStatus.CREATED);
    }

    @GetMapping("/job-sharing/{jobId}/poster-latest")
    public ResponseEntity<JobSharingPosterVO> queryLatestJobPosterImage(@PathVariable("jobId") Long jobId) {
        log.info("[APN: Job @{}] REST request to query latest Job sharing poster. jobId:{}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobId);
        JobSharingPosterVO result = jobSharingPlatformService.queryLatestJobPosterImage(jobId);
        return ResponseEntity.ok().body(result);
    }

    @PostMapping("/job-sharing/system-poster-image-zip")
    public ResponseEntity<HttpStatus> saveSystemJobPosterImageZipFile(@RequestParam("file") MultipartFile zipFile) throws IOException {
        log.info("[APN: Job @{}] REST request to save Job sharing poster image zip file.", com.altomni.apn.common.utils.SecurityUtils.getUserId());
        jobSharingPlatformService.saveSystemJobPosterImageZipFile(zipFile, SecurityContextHolder.getContext());
        return new ResponseEntity<>(HttpStatus.ACCEPTED);
    }

}
