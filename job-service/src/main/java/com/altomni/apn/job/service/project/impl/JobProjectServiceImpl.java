package com.altomni.apn.job.service.project.impl;

import cn.hutool.core.util.BooleanUtil;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.domain.project.JobProject;
import com.altomni.apn.job.domain.project.JobProjectSharing;
import com.altomni.apn.job.repository.project.JobProjectRepository;
import com.altomni.apn.job.repository.project.JobProjectSharingRepository;
import com.altomni.apn.job.service.dto.project.JobProjectDTO;
import com.altomni.apn.job.service.mapper.project.JobProjectMapper;
import com.altomni.apn.job.service.project.JobProjectService;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.google.common.collect.Sets;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class JobProjectServiceImpl implements JobProjectService {

    @Resource
    private JobProjectRepository jobProjectRepository;

    @Resource
    private JobProjectSharingRepository jobProjectSharingRepository;

    @Resource
    private JobProjectMapper jobProjectMapper;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

    @Override
    public JobProjectDTO save(JobProjectDTO jobProjectDTO) {
        jobProjectDTO.setId(null);
        JobProject jobProject = jobProjectMapper.toEntity(jobProjectDTO);
        return this.jobProjectMapper.toDto(this.jobProjectRepository.save(jobProject));
    }

    @Override
    public JobProjectDTO update(JobProjectDTO jobProjectDTO) throws CustomParameterizedException {
        if (Objects.isNull(jobProjectDTO.getId())){
            throw new CustomParameterizedException("Id cannot be empty!");
        }
        return this.save(jobProjectDTO);
    }

    @Override
    public Page<JobProjectDTO> getByPage(Pageable pageable) {
        List<JobProjectDTO> jobProjects = this.jobProjectRepository.findAllByPermissionUserId(SecurityUtils.getUserId(), pageable)
                .stream()
                .map(this.jobProjectMapper::toDto).toList();
        long total = this.jobProjectRepository.countAllByPermissionUserId(SecurityUtils.getUserId());
        return new PageImpl<>(jobProjects, pageable, total);
    }

    @Override
    public void shareProjectTo(Long projectId, Set<Long> userIds) {
        this.verityJobProject(projectId);
        Set<Long> existedUserIds = this.jobProjectSharingRepository.findAllByJobProjectIdAndUserIdIn(projectId, userIds)
                .stream()
                .map(JobProjectSharing::getUserId)
                .collect(Collectors.toSet());
        List<JobProjectSharing> newSharing = Sets.difference(userIds, existedUserIds).stream().map(useId -> new JobProjectSharing(null, projectId, useId)).toList();
        List<Long> deleteSharing = Sets.difference(existedUserIds, userIds).stream().toList();
        this.jobProjectSharingRepository.deleteAllByJobProjectIdAndUserIdIn(projectId, deleteSharing);
        this.jobProjectSharingRepository.saveAll(newSharing);
        this.jobProjectSharingRepository.flush();
        newSharing.forEach(user -> cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(user.getUserId()));
        deleteSharing.forEach(userId -> cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userId));
    }

    @Override
    public void appendJobsToProject(Long projectId, Set<Long> jobIds) {

    }

    @Override
    public void removeJobsFromProject(Long projectId, Set<Long> jobIds) {

    }

    private void verityJobProject(Long projectId){
        if (BooleanUtil.isFalse(this.jobProjectSharingRepository.existsByJobProjectIdAndPermissionUserId(projectId, SecurityUtils.getUserId()))){
            throw new CustomParameterizedException("Invalid job project!");
        }
    }
}
