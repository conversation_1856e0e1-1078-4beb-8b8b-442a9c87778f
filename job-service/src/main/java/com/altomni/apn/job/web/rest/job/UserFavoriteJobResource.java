//jobv3 deprecated

//package com.altomni.apn.job.web.rest.job;
//
//import com.altomni.apn.common.aop.request.NoRepeatSubmit;
//import com.altomni.apn.job.domain.job.UserFavoriteJob;
//import com.altomni.apn.common.dto.job.JobDTOV3;
//import com.altomni.apn.job.service.job.UserFavoriteJobService;
//import com.altomni.apn.common.utils.SecurityUtils;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//
//import javax.annotation.Resource;
//import javax.validation.Valid;
//import java.net.URI;
//import java.net.URISyntaxException;
//import java.util.Collection;
//import java.util.List;
//
///**
// * Created by <PERSON> on 9/7/2017.
// *
// * REST controller for managing FavoriteJobs.
// */
//
//@Api(hidden = true, tags = {"Job", "ATS-Jobs", "ATS-Dashboard"})
//@Slf4j
//@RestController
//@RequestMapping("/api/v3")
//@Deprecated
//public class UserFavoriteJobResource {
//
//    private static final String ENTITY_NAME = "favoriteJob";
//
//    @Resource
//    private UserFavoriteJobService userFavoriteJobService;
//
//    /**
//     * GET  /favorite/jobs : get all favorite jobs of current user
//     *
//     * @return the ResponseEntity with status 200 (OK) and with body the list of all favoriteJobs for current user,
//     *          or with status 400 (Bad Request) if the user id not exists
//     */
//    @ApiOperation(value = "Get all current user's favorite jobs")
//    @GetMapping("/favorite/jobs")
//    @NoRepeatSubmit
//    public ResponseEntity<List<JobDTOV3>> getAllFavoriteJobs() {
//        log.info("[APN: UserFavoriteJob @{}] REST request to get all favoriteJobs for current user.", SecurityUtils.getUserId());
//        return ResponseEntity.ok(userFavoriteJobService.getUserFavoriteJobs(SecurityUtils.getUserId()));
//    }
//
//    @ApiOperation(value = "Get all current user's favorite jobs")
//    @GetMapping("/favorite/jobs-by-userId")
//    @NoRepeatSubmit
//    public ResponseEntity<List<UserFavoriteJob>> getAllFavoriteJobsByUserId() {
//        log.info("[APN: UserFavoriteJob @{}] REST request to get all favoriteJobs for current user.", SecurityUtils.getUserId());
//        return ResponseEntity.ok(userFavoriteJobService.getUserFavoriteJobsByUserId(SecurityUtils.getUserId()));
//    }
//
//    /**
//     * POST  /favorite/jobs : Create a list of favoriteJobs for current user
//     *
//     * @param favoriteJobIds the list of favoriteJobIds to create
//     * @return the ResponseEntity with status 201 (Created) and with body the list of new created favoriteJobs,
//     *          or with status 400 (Bad Request) if the input list is null or empty
//     * @throws URISyntaxException if the Location URI syntax is incorrect
//     */
//    @ApiOperation(value = "Add jobs to current user's favorite by ids")
//    @PostMapping("/favorite/jobs")
//    @NoRepeatSubmit
//    public ResponseEntity<Collection<JobDTOV3>> batchCreateFavoriteJobs(@Valid @RequestBody List<Long> favoriteJobIds) throws URISyntaxException {
//        log.info("[APN: UserFavoriteJob @{}] REST request to save list of favoriteJobs : {}", SecurityUtils.getUserId(), favoriteJobIds);
//        return ResponseEntity.created(new URI("/api/favorite/jobs")).body(userFavoriteJobService.batchCreateFavoriteJobs(favoriteJobIds));
//    }
//
//    /**
//     * DELETE  /favorite/jobs : Delete a list of favoriteJobs for current user
//     *
//     * @param favoriteJobIds the list of favoriteJobIds to delete
//     * @return the ResponseEntity with status 204 (No Content),
//     *          or with status 400 (Bad Request) if the input list is null or empty
//     */
//    @ApiOperation(value = "Remove jobs from current user's favorite by ids")
//    @DeleteMapping("/favorite/jobs")
//    @NoRepeatSubmit
//    public ResponseEntity<Void> batchDeleteFavoriteJobs(@Valid @RequestBody List<Long> favoriteJobIds) {
//        log.info("[APN: UserFavoriteJob @{}] REST request to delete list of favoriteJobs : {}", SecurityUtils.getUserId(), favoriteJobIds);
//        userFavoriteJobService.batchDeleteFavoriteJobs(favoriteJobIds);
//        return ResponseEntity.noContent().build();
//    }
//
//    @ApiOperation(value = "Get user's favorite job by job id and user id ")
//    @GetMapping("/user/{userId}/favorite/jobs/{jobId}")
//    @NoRepeatSubmit
//    public ResponseEntity<List<UserFavoriteJob>> findAllByJobIdAndUserId(@PathVariable("jobId") Long jobId, @PathVariable("userId") Long userId) {
//        log.info("[APN: UserFavoriteJob @{}] REST request to get user's favorite job by job id and user id.", SecurityUtils.getUserId());
//        return ResponseEntity.ok(userFavoriteJobService.findAllByJobIdAndUserId(jobId, userId));
//    }
//}
