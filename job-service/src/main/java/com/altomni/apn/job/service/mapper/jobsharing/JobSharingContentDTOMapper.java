package com.altomni.apn.job.service.mapper.jobsharing;


import com.altomni.apn.job.service.dto.jobsharing.JobSharingContentDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPlatformDTO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {})
public interface JobSharingContentDTOMapper {


    JobSharingContentDTO toSharingContentDTO(JobSharingPlatformDTO dto);
    JobSharingPlatformDTO toSharingPlatformDTO(JobSharingContentDTO dto);

}
