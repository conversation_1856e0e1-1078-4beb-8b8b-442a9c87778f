package com.altomni.apn.job.domain.job;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A JobBoolString.
 */
@Entity
@Table(name = "job_bool_string")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobBoolString implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Job id")
    @Column(name = "job_id", updatable = false)
    private Long jobId;

    @ApiModelProperty(value = "The relationship among the skills in string key")
    @Column(name = "relationship")
    private String relationship;

    @ApiModelProperty(value = "From where the skills come")
    @Column(name = "section")
    private String section;

    @ApiModelProperty(value = "Parser's score on the talent's qualification for the job. User should not change this.")
    @Column(name = "score")
    private Double score;

    @ApiModelProperty(value = "One skill string. Parser return string array. eg: [\"java\"]")
    @Column(name = "strings")
    private String strings;

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getRelationship() {
        return relationship;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public Double getScore() {
        return score;
    }

    public void setScore(Double score) {
        this.score = score;
    }

    public String getStrings() {
        return strings;
    }

    public void setStrings(String strings) {
        this.strings = strings;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof JobBoolString)) {
            return false;
        }
        return id != null && id.equals(((JobBoolString) o).id);
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "JobBoolString{" +
                "id=" + id +
                ", jobId=" + jobId +
                ", relationship=" + relationship +
                ", section=" + section +
                ", score=" + score +
                ", strings='" + strings + '\'' +
                '}';
    }
}
