package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.JobPosterImage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JobPosterImageRepository extends JpaRepository<JobPosterImage, Long>, JpaSpecificationExecutor<JobPosterImage> {

}
