package com.altomni.apn.job.util;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

public class ObjectMapperUtils {
    private static final ObjectMapper MAPPER = new ObjectMapper()
            .registerModule(new JavaTimeModule())  // 注册Java 8时间模块
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)  // 忽略未知属性
            .configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);  // 日期序列化为字符串而不是时间戳

    public static ObjectMapper getInstance() {
        return MAPPER;
    }
}
