package com.altomni.apn.job.web.rest.job;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.domain.job.JobNote;
import com.altomni.apn.common.dto.job.JobNoteDTO;
import com.altomni.apn.job.service.dto.job.JobNoteKeywordDTO;
import com.altomni.apn.job.service.job.JobNoteService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing JobNote.
 */
@Api(tags = {"Job", "ATS-Jobs"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class JobNoteResource {

    private static final String ENTITY_NAME = "jobNote";

    @Resource
    private JobNoteService jobNoteService;

    /**
     * POST  /job-notes : Create a new jobNote.
     *
     * @param jobNoteDTO the jobNote to create
     * @return the ResponseEntity with status 201 (Created) and with body the new jobNote, or with status 400 (Bad Request) if the jobNote has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Create job note", notes = "Do not set user. It will be current user from access token.", response = JobNote.class)
    @PostMapping("/job-notes")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<JobNoteDTO> createJobNote(@RequestBody JobNoteDTO jobNoteDTO){//} throws URISyntaxException {
        //jobNoteDTO.setUserId(SecurityUtils.getUserId());
        log.info("[APN: JobNote @{}] REST request to save JobNote : {}", SecurityUtils.getUserId(), jobNoteDTO);
        JobNoteDTO result = jobNoteService.create(jobNoteDTO);
        //return ResponseEntity.created(new URI("/api/v3/job-notes/" + result.getId())).body(result);
        return ResponseEntity.ok().body(result);
    }

    /**
     * PUT  /job-notes/:id : Updates an existing jobNote.
     *
     * @param id the id of the jobNote to update
     * @param jobNoteDTO the jobNote to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated jobNote,
     * or with status 400 (Bad Request) if the jobNote is not valid,
     * or with status 500 (Internal Server Error) if the jobNote couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a job note", notes = "Do not allow to change user or job for the note.", response = JobNote.class)
    @PutMapping("/job-notes/{id}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<JobNoteDTO> updateJobNote(@PathVariable Long id, @RequestBody JobNoteDTO jobNoteDTO){// throws URISyntaxException {
        jobNoteDTO.setId(id);
        log.info("[APN: JobNote @{}] REST request to update JobNote : {}", SecurityUtils.getUserId(), jobNoteDTO);
        JobNoteDTO result = jobNoteService.update(jobNoteDTO);
        return ResponseEntity.ok()
            .body(result);
    }

    /**
     * GET  /job-notes/job/:jobId : get all the jobNotes for a specific job
     *
     * @param jobId the id of job to get all jobNotes
     * @return the ResponseEntity with status 200 (OK) and the list of jobNotes in body
     */
    @ApiOperation(value = "Get all job notes for a job", response = JobNote.class, responseContainer = "List")
    @GetMapping("/job-notes/job/{jobId}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public List<JobNoteDTO> getAllJobNotes(@ApiParam(value = "job id", required = true) @PathVariable Long jobId) {
        log.info("[APN: JobNote @{}] REST request to get all JobNotes for job {}", SecurityUtils.getUserId(), jobId);
        return jobNoteService.findAllByJobId(jobId);
    }

    /**
     * GET  /job-notes/:id : get the "id" jobNote.
     *
     * @param id the id of the jobNote to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the jobNote, or with status 404 (Not Found)
     */
    @ApiOperation(value = "Get job note given the id")
    @GetMapping("/job-notes/{id}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<JobNoteDTO> getJobNote(@ApiParam(value = "job note id", required = true) @PathVariable Long id) {
        log.info("[APN: JobNote @{}] REST request to get JobNote : {}", SecurityUtils.getUserId(), id);
        JobNoteDTO jobNote = jobNoteService.findOneWithEntity(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(jobNote));
    }

    /**
     * DELETE  /job-notes/:id : delete the "id" jobNote.
     *
     * @param id the id of the jobNote to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @ApiOperation(value = "Delete job note", notes = "Only the note creator can delete the note.")
    @DeleteMapping("/job-notes/{id}")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<Void> deleteJobNote(@ApiParam(value = "job note id", required = true) @PathVariable Long id) {
        log.info("[APN: JobNote @{}] REST request to delete JobNote : {}", SecurityUtils.getUserId(), id);
        jobNoteService.delete(id);
        return ResponseEntity.ok().build();
    }


    /**
     * POST  /job-notes/search
     *
     * @param jobNoteKeywordDTO the id of the jobNote to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the jobNote, or with status 404 (Not Found)
     */
    @ApiOperation(value = "Get job note list by give keyword in note")
    @PostMapping("/job-notes/search")
    public ResponseEntity<List<JobNoteDTO>> searchJobNote(@RequestBody JobNoteKeywordDTO jobNoteKeywordDTO, @PageableDefault Pageable pageable) {
        log.info("[APN: JobNote @{}] REST request to search JobNote : {}", SecurityUtils.getUserId());
        Page<JobNoteDTO> jobNotes = jobNoteService.searchJobNoteByKeyword(jobNoteKeywordDTO, pageable);
        return ResponseEntity.ok(jobNotes.getContent());
    }

}
