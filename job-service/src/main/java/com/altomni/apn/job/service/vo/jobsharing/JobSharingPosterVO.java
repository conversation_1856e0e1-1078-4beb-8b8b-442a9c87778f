package com.altomni.apn.job.service.vo.jobsharing;

import com.altomni.apn.common.domain.enumeration.job.JobPosterTemplate;
import com.altomni.apn.common.domain.enumeration.job.JobPosterTemplateConverter;
import com.altomni.apn.common.domain.enumeration.job.PlatformType;
import com.altomni.apn.common.domain.enumeration.job.PlatformTypeConverter;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageType;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageTypeConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import java.io.Serializable;
import java.time.Instant;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobSharingPosterVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id for poster")
    private Long id;

    @ApiModelProperty(value = "jobId for poster")
    private Long jobId;

    @ApiModelProperty(value = "platform for poster")
    @Convert(converter = PlatformTypeConverter.class)
    private PlatformType platform;

    @ApiModelProperty(value = "language for poster")
    private Integer language;

    @ApiModelProperty(value = "platform for poster")
    @Convert(converter = JobPosterTemplateConverter.class)
    private JobPosterTemplate template;

    @ApiModelProperty(value = "jobPosterImageId for poster")
    private Long jobPosterImageId;

    @ApiModelProperty(value = "content for poster")
    private String content;

}
