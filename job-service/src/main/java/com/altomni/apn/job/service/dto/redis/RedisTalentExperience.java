package com.altomni.apn.job.service.dto.redis;

import com.altomni.apn.common.utils.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RedisTalentExperience implements Serializable {

    private static final long serialVersionUID = -6746992136231230012L;

    private String company;

    private Boolean current = false;

    private Boolean isFamousCompany = false;

    private String title;

    private String startDate;

    private String endDate;

    private String description;

    @ApiModelProperty(value = "eg: JUNIOR, JUNIOR_III")
    private String level;

    @JsonIgnore
    private List<String> tokenizedTitles;

    private String companyWorldRank;

    private RedisLocation workLocation;

    private List<String> jobFunctions;

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Boolean getCurrent() {
        return current;
    }

    public void setCurrent(Boolean current) {
        this.current = current;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public List<String> getTokenizedTitles() {
        return tokenizedTitles;
    }

    public void setTokenizedTitles(List<String> tokenizedTitles) {
        this.tokenizedTitles = tokenizedTitles;
    }

    public String getCompanyWorldRank() {
        return companyWorldRank;
    }

    public void setCompanyWorldRank(String companyWorldRank) {
        this.companyWorldRank = companyWorldRank;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public RedisLocation getWorkLocation() {
        return workLocation;
    }

    public void setWorkLocation(RedisLocation workLocation) {
        this.workLocation = workLocation;
    }

    public Boolean getIsFamousCompany() {
        return isFamousCompany;
    }

    public void setIsFamousCompany(Boolean isFamousCompany) {
        this.isFamousCompany = isFamousCompany;
    }

    public List<String> getJobFunctions() {
        return jobFunctions;
    }

    public void setJobFunctions(List<String> jobFunctions) {
        this.jobFunctions = jobFunctions;
    }

    public static Comparator<RedisTalentExperience> comparator = (e1, e2) -> {
        if(e1.getCurrent()){
            return 1;
        }else if(e2.getCurrent()){
            return -1;
        }

        if(e1.getEndDate() == null && e2.getEndDate() == null){
            return DateUtil.compareLocalDateStr(e1.getStartDate(), e2.getStartDate());
        }else if(e1.getEndDate() == null){
            return 1;
        }else if(e2.getEndDate() == null){
            return -1;
        }else{
            LocalDate endDate1 = DateUtil.esDateStrToLocalDate(e1.getEndDate());
            LocalDate endDate2 = DateUtil.esDateStrToLocalDate(e2.getEndDate());
            return endDate1.compareTo(endDate2);
        }
    };

    @Override
    public String toString() {
        return "RedisTalentExperience{" +
                "company='" + company + '\'' +
                ", current=" + current +
                ", isFamousCompany=" + isFamousCompany +
                ", title='" + title + '\'' +
                ", startDate='" + startDate + '\'' +
                ", endDate='" + endDate + '\'' +
                ", description='" + description + '\'' +
                ", level='" + level + '\'' +
                ", tokenizedTitles=" + tokenizedTitles +
                ", companyWorldRank='" + companyWorldRank + '\'' +
                ", workLocation=" + workLocation +
                ", jobFunctions=" + jobFunctions +
                '}';
    }
}
