package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

@Entity
public class DormantJobDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @ApiModelProperty(value = "job id")
    private Long id;

    @ApiModelProperty(value = "the job title this talent applied")
    private String jobTitle;

    @ApiModelProperty(value = "Job type", allowableValues = "Direct_Placement, Contract, Right_To_Hire, Full_Time, Part_Time")
    private JobType jobType;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "Open, OnHold, Cancelled, Closed")
    private JobStatus jobStatus;

    @ApiModelProperty(value = "the company id this talent applied")
    private Long companyId;

    @ApiModelProperty(value = "the company name this talent applied")
    private String company;

    @ApiModelProperty(value = "The date the job is posted to ATS or job boards.")
    private Instant postingTime;

    @ApiModelProperty(value = "AM id")
    private Long amId;

    @ApiModelProperty(value = "am name")
    private String am;

    @ApiModelProperty(value = "last update time")
    private Instant lastModifiedDate;

    /*public DormantJobDTO(Long id, String jobTitle, JobType jobType, JobStatus jobStatus, Long companyId, String company, Instant postingTime, Long amId, String am, Instant lastModifiedDate) {
        this.id = id;
        this.jobTitle = jobTitle;
        this.jobType = jobType;
        this.jobStatus = jobStatus;
        this.companyId = companyId;
        this.company = company;
        this.postingTime = postingTime;
        this.amId = amId;
        this.am = am;
        this.lastModifiedDate = lastModifiedDate;
    }*/

    public DormantJobDTO(Object id, Object jobTitle, Object jobType, Object jobStatus, Object companyId, Object company, Object postingTime, Object amId, Object am, Object lastModifiedDate) {
        this.id = Objects.isNull(id) ? null : Long.parseLong(id.toString());
        this.jobTitle = Objects.isNull(jobTitle) ? null : jobTitle.toString();
        this.jobType = Objects.isNull(jobType) ? null : JobType.fromDbValue(Integer.parseInt(jobType.toString()));
        this.jobStatus = Objects.isNull(jobStatus) ? null : JobStatus.fromDbValue(Integer.parseInt(jobStatus.toString()));
        this.companyId = Objects.isNull(companyId) ? null : Long.parseLong(companyId.toString());
        this.company = Objects.isNull(company) ? null : company.toString();
        this.postingTime = Objects.isNull(postingTime) ? null : Instant.parse(postingTime.toString().replace(" ", "T") + "0Z");
        this.amId = Objects.isNull(amId) ? null : Long.parseLong(amId.toString());
        this.am = Objects.isNull(am) ? null : am.toString();
        this.lastModifiedDate = Objects.isNull(lastModifiedDate) ? null : Instant.parse(lastModifiedDate.toString().replace(" ", "T") + "0Z");
    }

    public Long getId() { return id; }

    public void setId(Long id) { this.id = id; }

    public String getJobTitle() { return jobTitle; }

    public void setJobTitle(String jobTitle) { this.jobTitle = jobTitle; }

    public JobType getJobType() { return jobType; }

    public void setJobType(JobType jobType) { this.jobType = jobType; }

    public JobStatus getJobStatus() { return jobStatus; }

    public void setJobStatus(JobStatus jobStatus) { this.jobStatus = jobStatus; }

    public Long getCompanyId() { return companyId; }

    public void setCompanyId(Long companyId) { this.companyId = companyId; }

    public String getCompany() { return company; }

    public void setCompany(String company) { this.company = company; }

    public Instant getPostingTime() { return postingTime; }

    public void setPostingTime(Instant postingTime) { this.postingTime = postingTime; }

    public Long getAmId() { return amId; }

    public void setAmId(Long amId) { this.amId = amId; }

    public String getAm() { return am; }

    public void setAm(String am) { this.am = am; }

    public Instant getLastModifiedDate() { return lastModifiedDate; }

    public void setLastModifiedDate(Instant lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; }

    @Override
    public String toString() {
        return "DormantJobDTO{" +
            "jobId=" + id +
            ", jobTitle='" + jobTitle + '\'' +
            ", jobType=" + jobType +
            ", jobStatus=" + jobStatus +
            ", companyId=" + companyId +
            ", company='" + company + '\'' +
            ", postingTime=" + postingTime +
            ", amId=" + amId +
            ", am='" + am + '\'' +
            ", lastModifiedDate=" + lastModifiedDate +
            '}';
    }
}
