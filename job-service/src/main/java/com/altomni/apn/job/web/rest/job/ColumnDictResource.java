package com.altomni.apn.job.web.rest.job;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.job.service.dto.job.ColumnDictDTO;
import com.altomni.apn.job.service.job.ColumnDictService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * REST controller for column dict.
 */
@Api(tags = {"APN-ColumnDict"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class ColumnDictResource {

    @Resource
    private ColumnDictService columnDictService;

    /**
     * GET  /dict : get all the dict which belong to this columnDict.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of dict in body
     */
    @ApiOperation(value = "Get/Filter all dict belong to this columnDict", notes = "Use search query to search for column dict. ")
    @GetMapping("/column/{dictCode}")
    public ResponseEntity<List<ColumnDictDTO>> findAllByCode(
        @ApiParam(value = "column code", required = true)@Valid @PathVariable Long dictCode) {
        log.info("[APN: DictCode @{}] REST request to get dict data by dictCode : {}", SecurityUtils.getUserId(), dictCode);
        List<ColumnDictDTO> result = columnDictService.getByDictCode(dictCode);
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

}
