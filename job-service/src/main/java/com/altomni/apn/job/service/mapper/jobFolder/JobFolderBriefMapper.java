package com.altomni.apn.job.service.mapper.jobFolder;


import com.altomni.apn.job.domain.job.JobFolderDetail;
import com.altomni.apn.job.service.dto.folder.JobFolderBriefDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring", uses = {})
public interface JobFolderBriefMapper{

    @Mapping(source = "jobFolderDetail", target = "count", qualifiedByName = "CalculateCount")
    JobFolderBriefDTO toDto(JobFolderDetail jobFolderDetail);

    @Named("CalculateCount")
    default Long calculateCount(JobFolderDetail jobFolderDetail) {
        if (jobFolderDetail.getJobFolderRelations() != null) {
            return (long) jobFolderDetail.getJobFolderRelations().size();
        } else {
            return 0L;
        }
    }

}
