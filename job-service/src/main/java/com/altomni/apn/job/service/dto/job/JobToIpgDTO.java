package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.job.IpgJobSalaryDTO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobToIpgDTO extends AbstractAuditingEntity implements Serializable{

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    @ApiModelProperty(value = "Job type", allowableValues = "CONTRACT, FULL_TIME, FULL_TIME_OR_PART_TIME, INTERNSHIP")
    private JobType jobType;

    @ApiModelProperty(value = "job function strings, one or more official function names ")
    private List<EnumRelationDTO>  jobFunctions;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
    private JobStatus status;

    @ApiModelProperty(value = "The job of department .")
    private String department;

    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<SkillDTO> requiredSkills;

    @ApiModelProperty(value = "range of experience year")
    private RangeDTO experienceYearRange;

    @ApiModelProperty(value = "Minimum Degree")
    private EnumRelationDTO minimumDegreeLevel;

    @ApiModelProperty(value = "job lacation strings, one or more official city names ")
    private List<LocationDTO> locations;

    @ApiModelProperty(value = "The url link to the JD. E.g. link to StoreService storage for the JD")
    private String jdUrl;

    @Size(max = 16380, message = "The JD text is too long.")
    @ApiModelProperty(value = "The public description of JD", required = true)
    private String publicDesc;

    @ApiModelProperty(value = "IPG job requirements")
    private String ipgJobRequirements;

    @ApiModelProperty(value = "IPG job description")
    private String ipgJobResponsibilities;

    @ApiModelProperty(value = "IPG job summary")
    private String ipgJobSummary;

    @ApiModelProperty(value = "IPG job 1：flexible location, 0：fixed work location")
    private Boolean ipgFlexibleLocation;

    @ApiModelProperty(value = "IPG job Salary Range/Rate (Currency)")
    private IpgJobSalaryDTO ipgJobSalaryDTO;

    @ApiModelProperty(value = "IPG job 1: include default content, 0: nothing")
    private Boolean ipgIncludeDefaultContent;

    @ApiModelProperty(value = "IPG job posting default content")
    private String ipgDefaultContent;

    public static JobToIpgDTO fromJob(JobV3 job) {
        JobToIpgDTO jobToIpgDTO = new JobToIpgDTO();
        ServiceUtils.myCopyProperties(job, jobToIpgDTO);
        return jobToIpgDTO;
    }

    public static JobToIpgDTO fromJobDTOV3(JobDTOV3 jobDTOV3) {
        JobToIpgDTO jobToIpgDTO = new JobToIpgDTO();
        ServiceUtils.myCopyProperties(jobDTOV3, jobToIpgDTO);
        jobToIpgDTO.setStatus(jobDTOV3.getIpgJobStatus());
        jobToIpgDTO.setJobType(jobDTOV3.getIpgJobType());
        jobToIpgDTO.setPublicDesc(jobDTOV3.getIpgJobDescription());
        if(jobDTOV3.getRequiredSkills() != null){
            jobToIpgDTO.setRequiredSkills(jobDTOV3.getRequiredSkills());
        }
        jobToIpgDTO.setIpgJobRequirements(jobDTOV3.getIpgRequirements());
        jobToIpgDTO.setIpgJobResponsibilities(jobDTOV3.getIpgResponsibilities());
        jobToIpgDTO.setIpgJobSummary(jobDTOV3.getIpgSummary());
        jobToIpgDTO.setLocations(jobDTOV3.getIpgLocations());
        return jobToIpgDTO;
    }

}
