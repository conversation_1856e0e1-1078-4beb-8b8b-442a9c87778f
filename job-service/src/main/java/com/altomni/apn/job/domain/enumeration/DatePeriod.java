package com.altomni.apn.job.domain.enumeration;

public enum DatePeriod {
    ONEDAYS(1, "One Days"),
    SEVENDAYS(7, "Seven Days"),
    FORTHDAYS(14, "Fourteen Days"),
    THIRTYDAYS(30, "Thirty Days"),
    SIXTYDAYS(60, "Sixty Days"),
    NINETYDAYS(90, "Ninety Days");

    private final Integer dbValue;
    private final String description;

    DatePeriod(Integer days, String description) {
        this.dbValue = days;
        this.description = description;
    }

    public int getDays() {
        return dbValue;
    }

    public String getDescription() {
        return description;
    }
}
