package com.altomni.apn.job.service.dto.project;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Data
public class JobProjectDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    private Long tenantId;
}
