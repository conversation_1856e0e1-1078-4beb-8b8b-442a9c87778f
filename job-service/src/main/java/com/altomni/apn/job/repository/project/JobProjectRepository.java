package com.altomni.apn.job.repository.project;

import com.altomni.apn.job.domain.project.JobProject;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * <AUTHOR>
 */
@Repository
public interface JobProjectRepository extends JpaRepository<JobProject, Long> {

    List<JobProject> findAllByPermissionUserId(Long userId, Pageable pageable);

    long countAllByPermissionUserId(Long userId);

    boolean existsById(Long id);

    Optional<JobProject> findFirstByTenantId(Long tenantId);

    @Query(value = "SELECT j.id from job j INNER JOIN job_project jp ON j.pteam_id = jp.id and j.tenant_id = :tenantId and j.id IN :jobIds", nativeQuery = true)
    Set<Long> findPrivateJobIds(@Param("tenantId") Long tenantId, @Param("jobIds") Collection<Long> jobIds);
}
