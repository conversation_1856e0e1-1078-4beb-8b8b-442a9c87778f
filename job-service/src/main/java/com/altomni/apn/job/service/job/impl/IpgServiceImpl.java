package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.IpgConstants;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.IpgConfig;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.async.AsyncRecord;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;
import com.altomni.apn.job.repository.async.AsyncRecordRepository;
import com.altomni.apn.job.service.dto.job.JobToIpgDTOV3;
import com.altomni.apn.job.service.job.IpgService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Headers;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import static com.altomni.apn.common.config.constants.IpgConstants.IPG_JOB_CREATE_URL;

@Slf4j
@Service("ipgService")
public class IpgServiceImpl implements IpgService {

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private HttpService httpService;

    @Resource
    private IpgConfig ipgConfig;

    @Resource
    private AsyncRecordRepository asyncRecordRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    private static String accessToken;

    public static String getAccessToken() {
        return accessToken;
    }

    public static void setAccessToken(String accessToken) {
        IpgServiceImpl.accessToken = accessToken;
    }

    private String getLoginUrl() {
        return getUrl() + IpgConstants.IPG_LOGIN_URL;
    }

    private String syncJobToIpgCreateUrl() {
        return getUrl() + IPG_JOB_CREATE_URL;
    }

    private String syncJobToIpgUpdateUrl(Long ipgJobId) {
        return getUrl() + IpgConstants.IPG_JOB_UPDATE_URL+ ipgJobId;
    }

    private String getUrl() {
        return ipgConfig.getUrl();
    }

    private String getEmail() {
        return ipgConfig.getEmail();
    }

    private String getPassword() {
        return ipgConfig.getPassword();
    }


    private String syncJobToIpgCloseUrl(Long ipgJobId, JobStatus status) {
        return getUrl() + IpgConstants.IPG_JOB_UPDATE_URL + ipgJobId + "/" + status;
    }

    @Override
    public HttpResponse syncJobToIpg(long id, JobToIpgDTOV3 job) throws IOException {
        if (job == null) {
            return null;
        }
        verificationTokenIsEmpty();
        String jobParamStr = JSON.toJSONString(job, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.post(syncJobToIpgCreateUrl(), getIpgRequestHeaders(), jobParamStr);
        if (verificationTokenExpired(response)) {
            response = httpService.post(syncJobToIpgCreateUrl(), getIpgRequestHeaders(), jobParamStr);
        }
        if (response != null) {
            log.info("[apn] syncJobToIpg url = {}, request = {}, response = {}", syncJobToIpgCreateUrl(), jobParamStr, JSONUtil.toJsonStr(response));
            if (!ObjectUtils.equals(HttpStatus.CREATED.value(), response.getCode())) {
                log.error("[APN: IpgFillerJobService] create a job synchronized to IpgFiller error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_IPGFILLER_JOB, id, AsyncEnum.DATA_TYPE_JOB, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[APN: IpgFillerJobService @{}] create job to synchronized error and response is null, jobId: {}", SecurityUtils.getUserId(), id);
        }
        return response;
    }

    @Override
    public HttpResponse updateSyncJobToIpg(long id, Long ipgJobId, JobToIpgDTOV3 job) throws IOException {
        if (job == null) {
            return null;
        }
        verificationTokenIsEmpty();
        String jobParamStr = JSON.toJSONString(job, SerializerFeature.WriteDateUseDateFormat);
        HttpResponse response = httpService.put(syncJobToIpgUpdateUrl(ipgJobId), getIpgRequestHeaders(), jobParamStr);
        if (verificationTokenExpired(response)) {
            response = httpService.put(syncJobToIpgUpdateUrl(ipgJobId), getIpgRequestHeaders(), jobParamStr);
        }
        if (response != null) {
            log.info("[apn] updateSyncJobToIpg url = {}, request = {}, response = {}", syncJobToIpgUpdateUrl(ipgJobId), jobParamStr, JSONUtil.toJsonStr(response));
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[APN: IpgFillerJobService] update a job synchronized to IpgFiller error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_IPGFILLER_JOB, id, AsyncEnum.DATA_TYPE_JOB, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[APN: IpgFillerJobService @{}] update job to synchronized error and response is null, ipgJobId: {}", SecurityUtils.getUserId(), ipgJobId);
        }
        return response;
    }

    @Override
    public HttpResponse deleteSyncJobToIpg(long id, Long ipgJobId, JobStatus status) throws IOException {
        verificationTokenIsEmpty();
        String jobParamStr = "";
        HttpResponse response = httpService.put(syncJobToIpgCloseUrl(ipgJobId, status), getIpgRequestHeaders(), jobParamStr);
        if (verificationTokenExpired(response)) {
            response = httpService.put(syncJobToIpgCloseUrl(ipgJobId, status), getIpgRequestHeaders(), jobParamStr);
        }
        if (response != null) {
            log.info("[apn] deleteSyncJobToIpg url = {}, request = {}, response = {}", syncJobToIpgCloseUrl(ipgJobId, status), jobParamStr, JSONUtil.toJsonStr(response));
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[APN: IpgFillerJobService] close a job synchronized to IpgFiller error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_IPGFILLER_JOB, id, AsyncEnum.DATA_TYPE_JOB, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[APN: IpgFillerJobService @{}] close job to synchronized error and response is null, ipgJobId: {}", SecurityUtils.getUserId(), ipgJobId);
        }
        return response;
    }

    @Override
    public HttpResponse querySyncJobToIpg(long id, Long ipgJobId) throws IOException {
        verificationTokenIsEmpty();
        HttpResponse response = httpService.get(syncJobToIpgUpdateUrl(ipgJobId), getIpgRequestHeaders());
        if (verificationTokenExpired(response)) {
            response = httpService.get(syncJobToIpgUpdateUrl(ipgJobId), getIpgRequestHeaders());
        }
        if (response != null) {
            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                log.error("[APN: IpgFillerJobService] query a job synchronized to IpgFiller error, response code: {}, response message: {}", response.getCode(), response.getBody());
            }
            asyncRecordRepository.saveAndFlush(new AsyncRecord(AsyncEnum.ASYNC_TYPE_IPGFILLER_JOB, id, AsyncEnum.DATA_TYPE_JOB, Status.Available, response.getCode(), response.getBody()));
        } else {
            log.error("[APN: IpgFillerJobService @{}] query job to synchronized error and response is null, ipgJobId: {}", SecurityUtils.getUserId(), ipgJobId);
        }
        return response;
    }

    @Override
    public HttpResponse syncJobToIpgLogin() throws IOException {
        JSONObject params = new JSONObject();
        params.put(IpgConstants.IPG_LOGIN_EMAIL_KEY, getEmail());
        params.put(IpgConstants.IPG_LOGIN_PASSWORD_KEY, getPassword());
        return httpService.post(getLoginUrl(), JSONUtil.toJsonStr(params));
    }

    public void verificationTokenIsEmpty() throws IOException {
        if (StrUtil.isBlank(getAccessToken())) {
            Object token = redisTemplate.opsForValue().get(IpgConstants.IPG_TOKEN_KEY);
            if (token == null) {
                refreshIpgToken();
            } else {
                setAccessToken(String.valueOf(token));
            }
        }
    }

    public void refreshIpgToken() throws IOException {
        HttpResponse response = syncJobToIpgLogin();
        String ipgToken = getIpgToken(response);
        if (StrUtil.isBlank(ipgToken)) {
            log.error("[APN: IpgFillerJobService] login to IpgFiller error, ipgToken is null.");
            return;
        }
        redisTemplate.opsForValue().set(IpgConstants.IPG_TOKEN_KEY, ipgToken);
        setAccessToken(ipgToken);
    }

    public String getIpgToken(HttpResponse response) {
        if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
            log.error("[APN: IpgFillerJobService] login to IpgFiller error, parameter error.");
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETIPGTOKEN_RESPONSESTATUSNOTOK.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JSONObject ipgLoginResponseJson = JSON.parseObject(response.getBody());
        JSONObject credential = JSON.parseObject(ipgLoginResponseJson.getString(IpgConstants.IPG_TOKEN_CREDENTIAL_KEY));
        String ipgAccessToken = credential.getString(IpgConstants.IPG_ACCESS_TOKEN_KEY);
        log.info("ipgAccessToken:{} ", ipgAccessToken);
        return ipgAccessToken;
    }

    public Headers getIpgRequestHeaders() {
        Map<String, String> headerBuilders = new HashMap<>(16);
        headerBuilders.put(IpgConstants.IPG_HEADERS_AUTHORIZATION_KEY, IpgConstants.IPG_HEADERS_AUTHORIZATION_VALUE_PREFIX + getAccessToken());
        return Headers.of(headerBuilders);
    }

    public boolean verificationTokenExpired(HttpResponse response) throws IOException {
        boolean expired = false;
        if (ObjectUtils.equals(HttpStatus.UNAUTHORIZED.value(), response.getCode())) {
            refreshIpgToken();
            expired = true;
        }
        return expired;
    }

}
