package com.altomni.apn.job.config.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

public class CustomRejectPolicy implements RejectedExecutionHandler {

    private final Logger log = LoggerFactory.getLogger(CustomRejectPolicy.class);

    @Override
    public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
        log.error("任务被拒绝，任务信息为: {}",r);
    }
}
