package com.altomni.apn.job.service.mapper.jobFolder;


import com.altomni.apn.job.domain.vm.SearchJobFolderVM;
import com.altomni.apn.job.service.dto.folder.SearchJobFolderDTO;

import com.altomni.apn.job.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {})
public interface SearchJobFolderMapper extends EntityMapper<SearchJobFolderDTO, SearchJobFolderVM> {
    default SearchJobFolderVM fromId(Long id) {
        if (id == null) {
            return null;
        }
        SearchJobFolderVM jobFolder = new SearchJobFolderVM();
        jobFolder.setId(id);
        return jobFolder;
    }

    SearchJobFolderVM toEntity(SearchJobFolderDTO dto);

    SearchJobFolderDTO toDto(SearchJobFolderVM entity);
}

