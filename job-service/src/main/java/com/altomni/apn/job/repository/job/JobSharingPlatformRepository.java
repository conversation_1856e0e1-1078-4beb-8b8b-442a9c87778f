package com.altomni.apn.job.repository.job;

import com.altomni.apn.common.domain.enumeration.job.PlatformType;
import com.altomni.apn.common.domain.job.JobSharingPlatform;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface JobSharingPlatformRepository extends JpaRepository<JobSharingPlatform, Long> {

    List<JobSharingPlatform> findAllByJobIdAndUserIdAndPlatformType(Long jobId, Long userId, PlatformType platformType);

    List<JobSharingPlatform> findAllByJobIdAndUserId(Long jobId, Long userId);

    JobSharingPlatform findTopByJobIdAndUserIdOrderByCreatedDateDesc(Long jobId, Long userId);

}
