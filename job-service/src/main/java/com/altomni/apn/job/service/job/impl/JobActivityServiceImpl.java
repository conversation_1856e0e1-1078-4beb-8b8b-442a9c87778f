package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.util.NumberUtil;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.activity.*;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.common.enumeration.enums.DisplayType;
import com.altomni.apn.common.service.activity.ActivityConfigService;
import com.altomni.apn.common.service.enums.*;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.ApplicationProperties;
import com.altomni.apn.job.service.company.CompanyService;
import com.altomni.apn.job.service.dto.job.JobActivityDTO;
import com.altomni.apn.job.service.job.JobActivityService;
import com.altomni.apn.job.service.talent.TalentService;
import com.altomni.apn.job.service.user.UserService;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class JobActivityServiceImpl implements JobActivityService {

    final static String JOB_ACTIVITY_KEY = "jobId";

    final static String esIndex = "activity_job_";

    final static String UNKNOWNUSER = "Unknown user";

    final static String DEFAULT_SORT_KEY = "createdDate";

    final static String DEFAULT_SORT_ORDER = "desc";

    //TODO: move to constant for canal
    final static String CLIENT_CONTACT_TALENT_ID = "clientContactTalentId";

    final static String JOB_ASSIGNED_USER_USER_ID = "jobAssignedUserUserId";

    final static Set<String> UserIdAsKeyValueSet = new HashSet<>(new ArrayList<>(List.of("jobAssignedUserUserId")));

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private ActivityConfigService activityConfigService;

    @Resource
    private HttpService httpService;

    @Resource
    private UserService userService;

    @Resource
    private TalentService talentService;

    /***
     * All ids and enumIds will be converted into the UI name by using service below;
     */
    @Resource
    private CompanyService companyService;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    private EnumIndustryService enumIndustryService;

    @Resource
    private EnumJobFunctionService enumJobFunctionService;

    @Resource
    private EnumDegreeService enumDegreeService;

    @Resource
    private EnumJobPriorityService enumJobPriorityService;

    @Resource
    private EnumLanguageService enumLanguageService;

    private String getCommonServiceUrl() {
        return applicationProperties.getJobActivityESUrl() + esIndex + SecurityUtils.getTenantId() + "/_search";
    }

    @Override
    public Page<JobActivityDTO> getJobActivities(Long jobId, Pageable pageable) throws Throwable {
        ActivityESRequestDTO activityESRequestDTO = activityConfigService.generateESRequest(JOB_ACTIVITY_KEY, jobId, pageable, DEFAULT_SORT_KEY, DEFAULT_SORT_ORDER);
        Gson gson = new Gson();
        String condition = gson.toJson(activityESRequestDTO);

        String url = getCommonServiceUrl();
        DisplayType displayType = DisplayType.BILINGUAL;
        List<JobActivityDTO> jobActivityDTOList = new ArrayList<>();
        Long total = 0L;
        try {
            HttpResponse response = httpService.post(url, condition);
            if (response != null) {
                if (ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                    log.info("[APN: Es JobActivity @{}] search jobActivity from activity service success, searchRequest: {}, pageable:{}", SecurityUtils.getUserId(), condition, pageable);
                    ESResponse esResponse = parseESResponse(response.getBody());
                    total = esResponse.getHits().getTotal().getValue();

                    jobActivityDTOList.addAll(formatJobActivity(getSourceChangeFromResponse(esResponse), displayType, jobId));
                } else if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode()) || ObjectUtils.equals(HttpStatus.UNPROCESSABLE_ENTITY.value(), response.getCode())) {
                    log.error("[APN: Es JobActivity @{}] search jobActivity from activity service failed, searchRequest: {}, pageable:{}, responseCode:{}", SecurityUtils.getUserId(), condition, pageable, response.getCode());
                }
            }

        } catch (Exception ex) {
            log.error("[APN: Es JobActivity @{}] JobId: {}, Error: {}",SecurityUtils.getUserId(), jobId,  ex.toString());
            throw ex;
        }

        return new PageImpl<>(jobActivityDTOList, pageable, total);
    }

    /***
     * format job activityDTO for displaying
     * @param esActivitySourceDTOList
     * @param jobId
     * @return
     */
    private List<JobActivityDTO> formatJobActivity(List<ESActivitySourceDTO> esActivitySourceDTOList, DisplayType displayType, Long jobId) {
        log.info("[APN User {} get job {} Activity: format job Activity ", SecurityUtils.getUserId(), jobId);
        if (esActivitySourceDTOList == null) {
            return null;
        }
        //current dont need to set the value for the enum
        //Map<String, ActivityConfig> activityConfigMap = activityConfigService.findAllJobActivityConfig(SourceType.JOB);
        List<String> uids = esActivitySourceDTOList.stream().map(ESActivitySourceDTO::getCreatedBy).collect(Collectors.toList());
        Map<String, UserUidNameDTO> userUidNameDTOMap = userService.findUsersByUidIn(uids).getBody();
        List<String> userIds = uids.stream().filter(NumberUtil::isNumber).collect(Collectors.toList());
        List<User> responseFindByIds = userService.findByIds(userIds.stream()
                .map(NumberUtils::toLong)
                .collect(Collectors.toList())).getBody();
        Map<String, User> userMap = responseFindByIds.stream()
                .collect(Collectors.toMap(User::getStringId, user -> user));
        List<JobActivityDTO> jobActivityDTOList = esActivitySourceDTOList.stream().map(activity -> {
            JobActivityDTO jobActivityDTO = new JobActivityDTO();
            //convert changeField from db value to view value
            if (activity.getChangeFields() != null) {
                List<ChangeFieldDTO> changeDTOList = new ArrayList<>();
                Map<String, User> valueUserSet = getUserSetAssociatedWithActivityValue(activity.getChangeFields());
                for (ActivityChangeDTO changeDTO : activity.getChangeFields()) {
                    if (changeDTO.getKey() == null || StringUtils.isBlank(changeDTO.getKey())) continue;
                    //current dont need to set the value for the enum
//                    if (activityConfigMap.containsKey(changeDTO.getKey())) {
//                        ActivityConfig config = activityConfigMap.get(changeDTO.getKey());
//                        try {
//                            ChangeFieldDTO changeFieldDTO = activityConfigService.generateChangeDTO(changeDTO, config, this, displayType);
//                            changeDTOList.add(changeFieldDTO);
//                        } catch (Exception ex) {
//                            log.error("[APN User {} get job {} Activity: failed to process {}, error", SecurityUtils.getUserId(), jobId, changeDTO.getKey(), ex);
//                        }
//                    }else{
                    //set talent id
                    if(changeDTO.getKey().equals(CLIENT_CONTACT_TALENT_ID)){
                        convertClientContactTalentIdToFullName(changeDTO);
                    }
                    //set user id
                    if(changeDTO.getKey().equals(JOB_ASSIGNED_USER_USER_ID)){
                        convertJobAssgnedUserUserIdToFullName(changeDTO, valueUserSet);
                    }
                    ChangeFieldDTO changeFieldDTO = new ChangeFieldDTO();
                    changeFieldDTO.setChangeFieldDTOFromActivityDTO(changeDTO);
                    changeDTOList.add(changeFieldDTO);
                    //}
                }
                jobActivityDTO.setChangeField(changeDTOList);
            }
            jobActivityDTO.setJobId(jobId);
            jobActivityDTO.setCreatedDate(activity.getCreatedDate());
            //set operator info
            FullNameUserDTO userDTO = new FullNameUserDTO();
            if (userMap.containsKey(activity.getCreatedBy())) {
                userDTO.setId(userMap.get(activity.getCreatedBy()).getId());
                userDTO.setFullName(CommonUtils.formatFullNameWithBlankCheck(userMap.get(activity.getCreatedBy()).getFirstName(), userMap.get(activity.getCreatedBy()).getLastName()));
            } else if (userUidNameDTOMap.containsKey(activity.getCreatedBy())) {
                userDTO.setId(userUidNameDTOMap.get(activity.getCreatedBy()).getId());
                userDTO.setFullName(CommonUtils.formatFullNameWithBlankCheck(userUidNameDTOMap.get(activity.getCreatedBy()).getFirstName(), userUidNameDTOMap.get(activity.getCreatedBy()).getLastName()));
            } else {
                userDTO.setFullName(UNKNOWNUSER);
            }
            jobActivityDTO.setCreatedBy(userDTO);
            return jobActivityDTO;
        }).collect(Collectors.toList());

        return jobActivityDTOList;
    }



    private ESResponse parseESResponse(String response) throws JsonProcessingException {
        log.info("[APN User {} get job Activity: process parse from response", SecurityUtils.getUserId());

        if (StringUtils.isEmpty(response)) {
            return null;
        }

        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        ESResponse searchResponse = objectMapper.readValue(response, ESResponse.class);
        return searchResponse;
    }

    private List<ESActivitySourceDTO> getSourceChangeFromResponse(ESResponse esResponse) {
        log.info("[APN User {} get job Activity: get changeFields Source data from response", SecurityUtils.getUserId());
        if (esResponse.getHits() == null && esResponse.getHits().getHits() == null) {
            return null;
        }

        List<ESHit> activityHits = esResponse.getHits().getHits();
        List<ESActivitySourceDTO> activityDTOList = new ArrayList<>();
        for (ESHit hit : activityHits) {
            Map<String, Object> source = hit.getSource();
            activityDTOList.add(JsonUtil.fromJson(JsonUtil.toJson(source), ESActivitySourceDTO.class));
        }

        return activityDTOList;

    }

    private void convertClientContactTalentIdToFullName(ActivityChangeDTO activityChangeDTO){
        String fromTalentString = activityChangeDTO.getChangedFrom();
        String toTalentString = activityChangeDTO.getChangedTo();

        Long fromTalentId = null;
        Long toTalentId = null;

        try {
            if (fromTalentString != null && !fromTalentString.isEmpty()) {
                fromTalentId = Long.parseLong(fromTalentString);
                String fromTalentName = talentService.findFullNameByTalentId(fromTalentId).getBody();
                activityChangeDTO.setChangedFrom(StringUtils.isBlank(fromTalentName) ? UNKNOWNUSER : fromTalentName);
            }
        } catch (NumberFormatException e) {
            log.error("[APN User {} get job Activity: fail to convert ClientContact String Id to Long : from:{}", SecurityUtils.getUserId(), fromTalentString);

        }

        try {
            if (toTalentString != null && !toTalentString.isEmpty()) {
                toTalentId = Long.parseLong(toTalentString);
                String toTalentName = talentService.findFullNameByTalentId(toTalentId).getBody();
                activityChangeDTO.setChangedTo(StringUtils.isBlank(toTalentName) ? UNKNOWNUSER: toTalentName);
            }
        } catch (NumberFormatException e) {
            log.error("[APN User {} get job Activity: fail to convert ClientContact String Id to Long : to:{}", SecurityUtils.getUserId(), toTalentString);
        }


    }

    private void convertJobAssgnedUserUserIdToFullName(ActivityChangeDTO changeDTO, Map<String, User> valueUserSet) {
        if(StringUtils.isNotBlank(changeDTO.getChangedFrom()) && valueUserSet.containsKey(changeDTO.getChangedFrom())){
            User user = valueUserSet.get(changeDTO.getChangedFrom());
            changeDTO.setChangedFrom(CommonUtils.formatFullNameWithBlankCheck(user.getFirstName(), user.getLastName()));
        }
        if(StringUtils.isNotBlank(changeDTO.getChangedTo()) && valueUserSet.containsKey(changeDTO.getChangedTo())){
            User user = valueUserSet.get(changeDTO.getChangedTo());
            changeDTO.setChangedTo(CommonUtils.formatFullNameWithBlankCheck(user.getFirstName(), user.getLastName()));
        }
    }

    private Map<String, User> getUserSetAssociatedWithActivityValue(List<ActivityChangeDTO> activityChangeDTOList){

        List<Long> filteredUserIds = activityChangeDTOList.stream()
                .flatMap(changeDTO -> {
                    Stream.Builder<Long> streamBuilder = Stream.builder();

                    if (UserIdAsKeyValueSet.contains(changeDTO.getKey())) {
                        String from = changeDTO.getChangedFrom();
                        String to = changeDTO.getChangedTo();

                        if (StringUtils.isNotBlank(from)) {
                            try {
                                streamBuilder.add(Long.parseLong(from));
                            } catch (NumberFormatException e) {
                            }
                        }

                        if (StringUtils.isNotBlank(to)) {
                            try {
                                streamBuilder.add(Long.parseLong(to));
                            } catch (NumberFormatException e) {
                            }
                        }
                    }
                    return streamBuilder.build();
                })
                .collect(Collectors.toList());

        List<User> userList = userService.findByIds(filteredUserIds).getBody();
        Map<String, User> userMap = userList.stream()
                .collect(Collectors.toMap(user-> user.getId().toString(), user -> user));
        return userMap;
    }


    /***
     * TODO: remove after full test
     * Test info
     * @param key
     * @return
     */
    private JobActivityDTO createJobActivityDTO(String key) {

        JobActivityDTO jobActivityDTO = new JobActivityDTO();
//        jobActivityDTO.setKey(key);
//        jobActivityDTO.setChangedFrom("1");
//        jobActivityDTO.setChangedTo("2");
        jobActivityDTO.setJobId(1001L);  // This can be changed according to your needs
//        jobActivityDTO.setCreatedBy(createdBy);
//        jobActivityDTO.setCreatedDate(Instant.now());
        return jobActivityDTO;
    }

    private List<JobActivityDTO> getJobActivityDummyData() {
        // List of field_names
        List<String> keys = Arrays.asList("jobFunctions", "requiredLanguages", "preferredLanguages", "currency",
                "preferredDegrees", "minimumDegreeLevel", "priority");

        // Create JobActivityDTO instances for each field_name
        return keys.stream()
                .map(this::createJobActivityDTO)
                .collect(Collectors.toList());
    }
}
