package com.altomni.apn.job.service.dto.aisourcing;

import com.altomni.apn.common.domain.dict.EnumMotivation;
import com.altomni.apn.job.domain.enumeration.aisourcing.AiScoreSearch;
import com.altomni.apn.job.domain.enumeration.aisourcing.AiSourcingTalentSource;
import com.altomni.apn.job.domain.enumeration.aisourcing.AiSourcingTalentTag;
import com.altomni.apn.job.domain.enumeration.aisourcing.JobSearchStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AiSourcingSearchDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private List<AiSourcingTalentSource> candidateSource;

    private List<AiScoreSearch> aiScore;

    private List<JobSearchStatus> jobSearchStatus;

    private List<AiSourcingTalentTag> aiSourcingTalentTag;

}
