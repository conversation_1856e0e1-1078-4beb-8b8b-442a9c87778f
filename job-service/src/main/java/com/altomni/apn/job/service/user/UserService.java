package com.altomni.apn.job.service.user;


import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.user.config.UserPersonalizationConfig;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
@FeignClient(value = "user-service")
public interface UserService {

    @PostMapping("/user/api/v3/permissions/teams/users/plain/with-team")
    ResponseEntity<List<PermissionTeamMemberDTO>> getTeamMembersByTeamIds(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM);
    @PostMapping("/user/api/v3/permissions/teams/users/plain/with-team/active")
    ResponseEntity<List<PermissionTeamUserDTO>> getActiveTeamMembersByTeamIds(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM);
    @PutMapping("/user/api/v3//user/preferences/personalization")
    ResponseEntity<Void> updatePersonalizationConfig(UserPersonalizationConfig config);
    @GetMapping("/user/api/v3//user/preferences/personalization")
    ResponseEntity<UserPersonalizationConfig> getPersonalizationConfig();
    @PostMapping("/user/api/v3/permissions/teams/users/plain")
    ResponseEntity<List<PermissionTeamMemberDTO>> getPlainTeamMembersByTeamIds(@RequestBody PermissionTeamMemberSearchVM teamMemberSearchVM);

    @PostMapping("/user/api/v3/credit-transactions/find/tenant-id-and-status-and-search-es-ids")
    ResponseEntity<List<CreditTransaction>> findAllCreditTransactionByTenantIdAndStatusAndSearchEsIdIn(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status, @RequestBody List<String> searchEsIds);

    @PostMapping("/user/api/v3/users/all-brief-by-ids/including-inactive")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsersByIds(@RequestBody List<Long> ids);

    @PostMapping("/user/api/v3/users/all-brief-by-ids")
    ResponseEntity<List<UserBriefDTO>> findBriefUsers(@RequestBody List<Long> userIds);

    @PostMapping("/user/api/v3/users/all-by-ids")
    ResponseEntity<List<User>> findByIds(@RequestBody List<Long> userIds);

    @GetMapping("/user/api/v3/users/tenant/{tenantId}/user-countries")
    ResponseEntity<Set<String>> findUserCountryByTenantId(@PathVariable("tenantId") Long tenantId);

    @GetMapping("/user/api/v3/users/{id}")
    ResponseEntity<User> findById(@PathVariable("id") Long id);

    @PostMapping("/user/api/v3/credit-transactions/find/tenant-id-and-status-and-profile-ids")
    ResponseEntity<List<CreditTransaction>> findAllByTenantIdAndStatusAndProfileIdIn(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status, @RequestBody List<String> profileId);


    @GetMapping("/user/api/v3/recruitment-processes/{recruitmentProcessId}/config/job-form")
    ResponseEntity<BaseConfig> getJobFormConfig(@PathVariable("recruitmentProcessId") Long recruitmentProcessId);

    @PutMapping("/user/api/v3/jobs/preferences/latest-update-job/{recruitmentProcessId}/user/{userId}")
    ResponseEntity<Void> updateUserRecentlyUsedRecruitmentProcess(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @PathVariable("userId") Long userId);

    @PutMapping("/user/api/v3/jobs/preferences/latest-update-private-job/{recruitmentProcessId}/user/{userId}")
    ResponseEntity<Void> updateUserRecentlyUsedRecruitmentProcessForPrivateJob(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @PathVariable("userId") Long userId);

    @PostMapping("/user/api/v3/users/get-user-names-by-uid-in")
    ResponseEntity<Map<String, UserUidNameDTO>> findUsersByUidIn(@RequestBody List<String> uids);

    @GetMapping("/user/api/v3/setting")
    ResponseEntity<TenantConfigDTO> getSettingConfig(@RequestParam(value = "configCode") TenantConfigCode configCode);

    @PostMapping("/user/api/v3/users/get-timezone-by-ids")
    ResponseEntity<List<UserTimeZoneVO>> getTimezoneListByUserIdList(@RequestBody List<Long> ids);

//    @GetMapping("/user/api/v3/permissions/users/{userId}/all-data-permissions")
//    public ResponseEntity<PermissionUserTeamPermissionVM> getAllDataPermissionsByUserId(@PathVariable("userId") Long userId);

    @GetMapping("/user/api/v3/permissions/teams/user/{userId}/primary_team_id")
    ResponseEntity<Long> getPrimaryTeamIdByUserId(@PathVariable("userId") Long userId);

    @GetMapping("/user/api/v3/permissions/users/self/all-data-permissions")
    ResponseEntity<PermissionUserTeamPermissionVM.PermissionDetail> getSelfAllClientContactDataPermissionsByUserId();
}
