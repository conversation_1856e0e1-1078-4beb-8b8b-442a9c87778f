package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.common.enumeration.folder.FolderPermissionConverter;
import com.altomni.apn.job.domain.enumeration.SharingTargetCategory;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@Table(name = "job_folder_sharing_user")
public class JobFolderSharingUser extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "job_folder_id", nullable = false)
    private Long jobFolderId;


    @Column(name = "user_id", nullable = false)
    private Long userId;

    @Convert(converter = FolderPermissionConverter.class)
    @Column(name = "permission", nullable = false)
    private FolderPermission permission;

    public boolean hasWritePermission(Long userId){
        return  this.userId.equals(userId) && this.permission.equals(FolderPermission.READWRITE);

    }

    public boolean hasReadPermission(){
        return (this.permission.toDbValue() & 0x6) != 0;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobFolderId() {
        return jobFolderId;
    }

    public void setJobFolderId(Long jobFolderId) {
        this.jobFolderId = jobFolderId;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public FolderPermission getPermission() {
        return permission;
    }

    public void setPermission(FolderPermission permission) {
        this.permission = permission;
    }

    public boolean DoesShareWithOwner(Long userId){
        return this.userId.equals(userId);
    }

    public boolean isSameFolderSharing(JobFolderSharingUser jobFolderSharingUser) {
        return this.userId.equals(jobFolderSharingUser.getUserId())
                && this.jobFolderId.equals(jobFolderSharingUser.getJobFolderId())
                && this.permission.equals(jobFolderSharingUser.getPermission());
    }
}
