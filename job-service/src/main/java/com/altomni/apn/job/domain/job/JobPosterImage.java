package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageStatus;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageStatusConverter;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageType;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageTypeConverter;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPosterImageDTO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPosterImageVO;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@Table(name = "job_poster_image")
public class JobPosterImage extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "link_url")
    private String linkUrl;

    @Column(name = "thumbnail_link_url")
    private String thumbnailLinkUrl;

    @Column(name = "type")
    @Convert(converter = PosterImageTypeConverter.class)
    private PosterImageType type;

    @Column(name = "status")
    @Convert(converter = PosterImageStatusConverter.class)
    private PosterImageStatus status;

    public static JobSharingPosterImageVO toJobSharingPosterImageVO(JobPosterImage jobPosterImage) {
        JobSharingPosterImageVO jobSharingPosterImageVO = new JobSharingPosterImageVO();
        ServiceUtils.myCopyProperties(jobPosterImage, jobSharingPosterImageVO);
        return jobSharingPosterImageVO;
    }

    public static JobPosterImage fromJobSharingPosterImageDTO(JobSharingPosterImageDTO jobSharingPosterImageDTO) {
        JobPosterImage jobPosterImage = new JobPosterImage();
        jobPosterImage.setId(jobSharingPosterImageDTO.getId());
        jobPosterImage.setFileName(jobSharingPosterImageDTO.getFileName());
        jobPosterImage.setLinkUrl(jobSharingPosterImageDTO.getLinkUrl());
        jobPosterImage.setType(PosterImageType.CUSTOM);
        jobPosterImage.setStatus(PosterImageStatus.ACTIVE);
        return jobPosterImage;
    }

}

