package com.altomni.apn.job.domain.enumeration.aisourcing;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum AiScoreSearch implements ConvertedEnum<Integer> {

    HIGHLY_RECOMMENDED(90),
    RECOMMENDED(70),

    POTENTIAL_MATCH(60);

    private final Integer dbValue;

    AiScoreSearch(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    /** static resolving: */
    public static final ReverseEnumResolver<AiScoreSearch, Integer> resolver =
        new ReverseEnumResolver<>(AiScoreSearch.class, AiScoreSearch::toDbValue);

    public static AiScoreSearch fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
