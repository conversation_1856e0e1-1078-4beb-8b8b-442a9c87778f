package com.altomni.apn.job.domain.aisourcing;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "job_ai_sourcing")
public class JobAiSourcing extends AbstractPermissionAuditingEntity implements Serializable, Cloneable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Job id")
    @Column(name = "job_id")
    private Long jobId;

    @ApiModelProperty(value = "Tenant Id")
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @ApiModelProperty(value = "if AI sourcing is enabled for current job")
    @Column(name = "enabled")
    private boolean enabled;

    @ApiModelProperty(value = "Expected candidates count from AI souring")
    @Column(name = "expected_candidates")
    private Integer expectedCandidates;

    @ApiModelProperty(value = "if job info was updated, if it was, reminder use to be available to get ai sourcing talents")
    @Column(name = "job_info_updated")
    private boolean jobInfoUpdated;

    @ApiModelProperty(value = "search version will be increased by 1 after each valid search iteration")
    @Column(name = "search_version")
    private int searchVersion;


}
