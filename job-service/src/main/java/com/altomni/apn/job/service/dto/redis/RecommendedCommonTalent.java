package com.altomni.apn.job.service.dto.redis;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class RecommendedCommonTalent extends RecommendedTalent {

    @ApiModelProperty(value = "whether talent is purchased from common db. Default is false.")
    private Boolean purchased = false;

    @ApiModelProperty(value = "currentExperiences from es")
    private JSONArray currentExperiences;

}
