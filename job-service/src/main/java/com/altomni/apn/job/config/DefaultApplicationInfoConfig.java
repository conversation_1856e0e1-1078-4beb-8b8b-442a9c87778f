package com.altomni.apn.job.config;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.job.config.env.DefaultApplicationProperties;
import com.altomni.apn.job.service.dto.job.DefaultApplicationInfo;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Configuration
public class DefaultApplicationInfoConfig {

    @Resource
    private DefaultApplicationProperties defaultApplicationProperties;

    /**
     * commission
     */
    public Float getCommissionFloat(List<String> countryList) {
        if (CollUtil.isEmpty(countryList)) {
            return null;
        }
        Map<String, DefaultApplicationInfo> defaultConfigProperties = defaultApplicationProperties.getCountryConfig();
        if (CollUtil.isEmpty(defaultConfigProperties)) {
            return null;
        }
        for (String country : countryList) {
            if (StrUtil.isBlank(country)) {
                continue;
            }
            // country 存在 bosnia and herzegovina 的数据,在 nacos 的配置中为 bosnia-and-herzegovina
            String key = country.replace(" ", "-");
            DefaultApplicationInfo defaultApplicationInfo = defaultConfigProperties.get(key);
            if (ObjectUtil.isEmpty(defaultApplicationInfo)) {
                continue;
            }
            return defaultApplicationInfo.getCommission();
        }
        return null;
    }

}
