package com.altomni.apn.job.service.job;

import net.devh.boot.grpc.client.inject.GrpcClient;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import com.google.protobuf.ByteString;


// proto生成的类
import gpu_service.GpuServiceGrpc;
import gpu_service.GpuProto;  // 注意这里改为GpuProto

@Service
@Slf4j
public class GpuGrpcService {

    @GrpcClient("gpu-grpc-server")
    private GpuServiceGrpc.GpuServiceBlockingStub gpuServiceStub;

    public GpuProto.String checkReadiness() {
        GpuProto.Empty request = GpuProto.Empty.newBuilder().build();
        return gpuServiceStub.readiness(request);
    }

    public GpuProto.KeywordExtractorResponse extractKeywords(byte[] inputIds, byte[] attentionMask, java.lang.String text) {
        GpuProto.KeywordExtractorRequest request = GpuProto.KeywordExtractorRequest.newBuilder()
                .setInputIds(ByteString.copyFrom(inputIds))
                .setAttentionMask(ByteString.copyFrom(attentionMask))
                .setText(text)
                .build();

        return gpuServiceStub.keywordExtractorApi(request);
    }

    public GpuProto.KeywordExtractorResponse extractKeywords(java.lang.String text) {
        GpuProto.KeywordExtractorRequest request = GpuProto.KeywordExtractorRequest.newBuilder()
                .setText(text)
                .build();

        return gpuServiceStub.keywordExtractorApi(request);
    }
}
