package com.altomni.apn.job.service.dto.esfiller;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class EsfillerUpdateDoc implements Serializable {

    private static final long serialVersionUID = -1278811314430834315L;

    @JsonProperty("ids")
    private List<String> ids;

    @JsonProperty("doc")
    private JobUpdateField doc;


    public EsfillerUpdateDoc ids(List<String> ids) {
        this.ids = ids;
        return this;
    }

    public List<String> getIds() {
        return ids;
    }

    public void setIds(List<String> ids) {
        this.ids = ids;
    }

    public JobUpdateField getDoc() {
        return doc;
    }

    public EsfillerUpdateDoc doc(JobUpdateField doc) {
        this.doc = doc;
        return this;
    }

    public void setDoc(JobUpdateField doc) {
        this.doc = doc;
    }

}
