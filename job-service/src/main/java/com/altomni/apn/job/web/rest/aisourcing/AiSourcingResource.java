package com.altomni.apn.job.web.rest.aisourcing;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.service.aisourcing.JobAiSourcingService;
import com.altomni.apn.job.service.dto.aisourcing.*;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(tags = {"Job-AI-Sourcing"})
@Slf4j
@RestController
@RequestMapping("/api/v3/ai-sourcing")
public class AiSourcingResource {

    @Resource
    private JobAiSourcingService jobAiSourcingService;

    @ApiOperation(value = "Set jobs that can get involved to ai sourcing")
    @PostMapping("/jobs")
    @Timed
    public ResponseEntity<Void> setAiSourcingJobs(@RequestBody JobIdDTO jobIdDTO) {
        log.info("[APN: AI_Sourcing @{}] REST request to set jobs that can get involved to ai sourcing: {}", SecurityUtils.getUserId(), jobIdDTO);
        jobAiSourcingService.saveAll(jobIdDTO.getJobIds());
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "Enable AI sourcing for a job")
    @PutMapping("/job/{jobId}/enable")
    @Timed
    public ResponseEntity<Void> enableAiSourcingForJob(@PathVariable("jobId") Long jobId) throws IOException {
        log.info("[APN: AI_Sourcing @{}] REST request to enable ai sourcing for job: {}", SecurityUtils.getUserId(), jobId);
        jobAiSourcingService.enableAiSourcing(jobId);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "disable AI sourcing for a job")
    @PutMapping("/job/{jobId}/disable")
    @Timed
    public ResponseEntity<Void> disableAiSourcingForJob(@PathVariable("jobId") Long jobId) {
        log.info("[APN: AI_Sourcing @{}] REST request to disable ai sourcing for job: {}", SecurityUtils.getUserId(), jobId);
        jobAiSourcingService.disableAiSourcing(jobId);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "Get AI sourcing progress for a job")
    @GetMapping("/job/{jobId}/progress")
    @Timed
    public ResponseEntity<String> getProgress(@PathVariable("jobId") Long jobId, @RequestParam(value = "refresh", defaultValue = "false") boolean refresh) throws IOException {
        log.info("[APN: AI_Sourcing @{}] REST request to ai sourcing progress for job: {}, with refresh: {}", SecurityUtils.getUserId(), jobId, refresh);
        return ResponseEntity.ok(jobAiSourcingService.getAiSourcingProgress(jobId, refresh));
    }

    @ApiOperation(value = "Get AI sourcing result for a job")
    @GetMapping("/job/{jobId}/result")
    @Timed
    public ResponseEntity<String> getAiSourcingResult(@PathVariable("jobId") Long jobId, Pageable pageable) throws IOException {
        log.info("[APN: AI_Sourcing @{}] REST request to get ai sourcing result for job: {}", SecurityUtils.getUserId(), jobId);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("total_num", "last_updated_time"));
        String aiSourcingResult = jobAiSourcingService.getAiSourcingResult(jobId, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(aiSourcingResult);
    }

    @ApiOperation(value = "Search AI sourcing result for a job")
    @PostMapping("/job/{jobId}/result")
    @Timed
    public ResponseEntity<String> searchAiSourcingResult(@PathVariable("jobId") Long jobId, @RequestBody  AiSourcingSearchDTO aiSourcingSearchDTO, Pageable pageable) throws IOException {
        log.info("[APN: AI_Sourcing @{}] REST request to search ai sourcing result for job: {}", SecurityUtils.getUserId(), jobId);
        HttpHeaders headers = new HttpHeaders();
        headers.setAccessControlExposeHeaders(CollUtil.newArrayList("total_num", "last_updated_time"));
        String aiSourcingResult = jobAiSourcingService.searchAiSourcingResult(jobId, aiSourcingSearchDTO, pageable, headers);
        return ResponseEntity.ok().headers(headers).body(aiSourcingResult);
    }

    @PutMapping("/job/{jobId}/accept/talent/{talentId}")
    @Timed
    public ResponseEntity<Void> acceptCandidate(@PathVariable("jobId") Long jobId, @PathVariable("talentId") String talentId) {
        log.info("[APN: AI_Sourcing @{}] REST request to accept candidate {} for job: {}", SecurityUtils.getUserId(), talentId, jobId);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/job/{jobId}/reject/talent/{talentId}")
    @Timed
    public ResponseEntity<Void> rejectCandidate(@PathVariable("jobId") Long jobId, @PathVariable("talentId") String talentId, @RequestBody  RejectCandidateDTO rejectCandidateDTO) {
        log.info("[APN: AI_Sourcing @{}] REST request to reject candidate {} for job: {}", SecurityUtils.getUserId(), talentId, jobId);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/job/{jobId}/save-for-later/talent/{talentId}")
    @Timed
    public ResponseEntity<Void> saveCandidateForLater(@PathVariable("jobId") Long jobId, @PathVariable("talentId") String talentId) {
        log.info("[APN: AI_Sourcing @{}] REST request to save candidate {} for later for job: {}", SecurityUtils.getUserId(), talentId, jobId);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/job/{jobId}/cancel/talent/{talentId}")
    @Timed
    public ResponseEntity<Void> cancelCandidateSelection(@PathVariable("jobId") Long jobId, @PathVariable("talentId") String talentId) {
        log.info("[APN: AI_Sourcing @{}] REST request to cancel candidate {} selection for job: {}", SecurityUtils.getUserId(), talentId, jobId);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "Get AI sourcing profile for a talent")
    @GetMapping("/talent/{profileId}")
    @Timed
    public ResponseEntity<String> getTalentDetail(@PathVariable("profileId") String profileId) throws IOException {
        log.info("[APN: AI_Sourcing @{}] REST request to get talent detail by profileId: {}", SecurityUtils.getUserId(), profileId);
        return ResponseEntity.ok(jobAiSourcingService.getTalentDetail(profileId));
    }

    @GetMapping("/job/{jobId}/talents")
    @Timed
    public ResponseEntity<List<SavedAiSourcingTalentDTO>> getTalentsByJobId(@PathVariable("jobId") Long jobId) {
        log.info("[APN: AI_Sourcing @{}] REST request to saved ai sourcing talents by job: {}", SecurityUtils.getUserId(), jobId);
        return ResponseEntity.ok(new ArrayList<>());
    }

    @PutMapping("/eliminate/job-ai-sourcing-talent/{id}")
    @Timed
    public ResponseEntity<Void> eliminateCandidateFromShortlist(@PathVariable("id") Long id, @RequestBody EliminateTalentDTO eliminateTalentDTO) {
        log.info("[APN: AI_Sourcing @{}] REST request to eliminate ai sourcing candidate from shortlist: {}", SecurityUtils.getUserId(), id);
        return ResponseEntity.ok().build();
    }

}
