package com.altomni.apn.job.domain.job;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.time.Instant;

/**
 * Created by <PERSON> on 9/19/2017.
 */
public class MyApplication implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "application id", required = true)
    private Long id;

    @ApiModelProperty(value = "job id")
    private Long jobId;

    @ApiModelProperty(value = "the job title this talent applied")
    private String jobTitle;

    @ApiModelProperty(value = "company id")
    private Long companyId;

    @ApiModelProperty(value = "the company name this talent applied")
    private String company;

    @ApiModelProperty(value = "talentId id")
    private Long talentId;

    @ApiModelProperty(value = "full name. Either firstName & lastName (in US) or full name (in CN) are required. Either way, " +
        "the full name can not be null.")
    private String fullName;

    @ApiModelProperty(value = "talent email")
    private String email;

    @ApiModelProperty(value = "talent phone")
    private String phone;

//    @ApiModelProperty(value = "sourcer id")
//    private Long sourcerId;
//
//    @ApiModelProperty(value = "sourcer name")
//    private String sourcer;
//
//    @ApiModelProperty(value = "recruiter id")
//    private Long recruiterId;
//
//    @ApiModelProperty(value = "recruiter name")
//    private String recruiter;
//
//    @ApiModelProperty(value = "AM id")
//    private Long amId;
//
//    @ApiModelProperty(value = "am name")
//    private String am;

    @ApiModelProperty(value = "last update user")
    private String lastModifiedBy;

    @ApiModelProperty(value = "last update time")
    private Instant lastModifiedDate;

    public MyApplication(Long id, Long jobId, String jobTitle, Long companyId, String company, Long talentId, String fullName, String email, String phone,
//                         Long sourcerId, String sourcer, Long recruiterId, String recruiter, Long amId, String am,
                         String lastModifiedBy, Instant lastModifiedDate) {
        this.id = id;
        this.jobId = jobId;
        this.jobTitle = jobTitle;
        this.companyId = companyId;
        this.company = company;
        this.talentId = talentId;
        this.fullName = fullName;
        this.email = email;
        this.phone = phone;
//        this.sourcerId = sourcerId;
//        this.sourcer = sourcer;
//        this.recruiterId = recruiterId;
//        this.recruiter = recruiter;
//        this.amId = amId;
//        this.am = am;
        this.lastModifiedBy = lastModifiedBy;
        this.lastModifiedDate = lastModifiedDate;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() { return jobId; }

    public void setJobId(Long jobId) { this.jobId = jobId; }

    public String getJobTitle() {
        return jobTitle;
    }

    public void setJobTitle(String jobTitle) {
        this.jobTitle = jobTitle;
    }

    public Long getCompanyId() { return companyId; }

    public void setCompanyId(Long companyId) { this.companyId = companyId; }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Long getTalentId() { return talentId; }

    public void setTalentId(Long talentId) { this.talentId = talentId; }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() { return email; }

    public void setEmail(String email) { this.email = email; }

    public String getPhone() { return phone; }

    public void setPhone(String phone) { this.phone = phone; }

//    public Long getSourcerId() { return sourcerId; }
//
//    public void setSourcerId(Long sourcerId) { this.sourcerId = sourcerId; }
//
//    public String getSourcer() { return sourcer; }
//
//    public void setSourcer(String sourcer) { this.sourcer = sourcer; }
//
//    public Long getRecruiterId() { return recruiterId; }
//
//    public void setRecruiterId(Long recruiterId) { this.recruiterId = recruiterId; }
//
//    public String getRecruiter() { return recruiter; }
//
//    public void setRecruiter(String recruiter) { this.recruiter = recruiter; }
//
//    public Long getAmId() { return amId; }
//
//    public void setAmId(Long amId) { this.amId = amId; }
//
//    public String getAm() { return am; }
//
//    public void setAm(String am) { this.am = am; }

    public String getLastModifiedBy() { return lastModifiedBy; }

    public void setLastModifiedBy(String lastModifiedBy) { this.lastModifiedBy = lastModifiedBy; }

    public Instant getLastModifiedDate() { return lastModifiedDate; }

    public void setLastModifiedDate(Instant lastModifiedDate) { this.lastModifiedDate = lastModifiedDate; }

    @Override
    public String toString() {
        return "MyCandidate{" +
            "id=" + id +
            ", jobId=" + jobId +
            ", jobTitle='" + jobTitle + '\'' +
            ", companyId=" + companyId +
            ", company='" + company + '\'' +
            ", talentId=" + talentId +
            ", fullName='" + fullName + '\'' +
            ", email='" + email + '\'' +
            ", phone='" + phone + '\'' +
//            ", sourcerId=" + sourcerId +
//            ", sourcer='" + sourcer + '\'' +
//            ", recruiterId=" + recruiterId +
//            ", recruiter='" + recruiter + '\'' +
//            ", amId=" + amId +
//            ", am='" + am + '\'' +
            ", lastModifiedBy='" + lastModifiedBy + '\'' +
            ", lastModifiedDate=" + lastModifiedDate +
            '}';
    }
}
