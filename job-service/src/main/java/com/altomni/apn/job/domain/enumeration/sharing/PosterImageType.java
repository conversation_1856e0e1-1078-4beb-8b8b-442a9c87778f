package com.altomni.apn.job.domain.enumeration.sharing;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The PosterImageType enumeration.
 * <AUTHOR>
 */
public enum PosterImageType implements ConvertedEnum<Integer> {
    /**
     * the type of sync, single means sync only one data to es, use for normal process sync to es
     */
    SYSTEM(10),
    /**
     *the type of sync, esfiller means it will call esfiller to sync data. use for batch or a few or one data to sync
     */
    CUSTOM(20);

    private final Integer dbValue;

    PosterImageType(Integer dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    /** static resolving: */
    public static final ReverseEnumResolver<PosterImageType, Integer> resolver =
        new ReverseEnumResolver<>(PosterImageType.class, PosterImageType::toDbValue);

    public static PosterImageType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
