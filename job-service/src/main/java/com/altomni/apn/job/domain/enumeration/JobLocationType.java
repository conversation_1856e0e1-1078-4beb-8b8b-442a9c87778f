package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum JobLocationType {
    FLEXIBLE_LOCATION(0, "Specified work location"),
    SPECIFIED_LOCATION(1, "Flexible work location");


    private final int dbValue;
    private final String description;

    JobLocationType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<FeeType, Integer> resolver = new ReverseEnumResolver<>(FeeType.class, FeeType::toDbValue);

    public static FeeType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

}
