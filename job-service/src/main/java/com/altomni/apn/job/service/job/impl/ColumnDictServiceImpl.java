package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.job.domain.job.ColumnDict;
import com.altomni.apn.job.repository.job.ColumnDictRepository;
import com.altomni.apn.job.service.dto.job.ColumnDictDTO;
import com.altomni.apn.job.service.job.ColumnDictService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ColumnDictServiceImpl implements ColumnDictService {

    @Resource
    private ColumnDictRepository columnDictRepository;


    @Override
    public List<ColumnDictDTO> getByDictCode(Long dictCode) {
        List<ColumnDict> result = columnDictRepository.findAllByPcodeOrderBySortOrder(dictCode);
        if(ObjectUtil.isNotEmpty(result)) {
            return result.stream().map(ColumnDictDTO::fromBizDict).collect(Collectors.toList());
        }else {
            return new ArrayList<>();
        }
    }

}
