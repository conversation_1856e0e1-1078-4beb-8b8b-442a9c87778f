package com.altomni.apn.job.service.async;

import com.altomni.apn.job.domain.async.AsyncRecord;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;

import java.util.List;

public interface AsyncRecordService {

    void clearAllSyncRecordError(AsyncEnum dataType, List<Long> dataIds);

    AsyncRecord save(AsyncRecord asyncRecord);

    Integer countSyncError(AsyncEnum dataType, Long dataId);

    void updateSyncFailureToSuccess(AsyncEnum dataType, List<Long> ids);
}
