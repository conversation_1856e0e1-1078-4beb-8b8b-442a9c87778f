package com.altomni.apn.job;

import com.altomni.apn.common.aop.request.SignGeneratorConfig;
import com.altomni.apn.common.auth.agency_auth.JwtAgencyUserTokenStore;
import com.altomni.apn.common.auth.timesheet_auth.JwtTimesheetUserTokenStore;
import com.altomni.apn.common.config.*;
import com.altomni.apn.common.config.email.EmailProperties;
import com.altomni.apn.common.config.env.CommonApplicationProperties;
import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import com.altomni.apn.common.config.env.WechatProperties;
import com.altomni.apn.common.config.loadbalancer.LoadBalanceConfiguration;
import com.altomni.apn.common.config.rabbitmq.JobdivaRabbitMqConfig;
import com.altomni.apn.common.datapermission.config.DataPermissionAutoConfiguration;
import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.domain.dict.EnumGenderIdentity;
import com.altomni.apn.common.errors.ExceptionTranslator;
import com.altomni.apn.common.interceptor.CustomResponseBodyAdviceAdapter;
import com.altomni.apn.common.interceptor.FeignClientInterceptor;
import com.altomni.apn.common.interceptor.SecurityDataLevelInterceptor;
import com.altomni.apn.common.interceptor.SecurityObjectLevelInterceptor;
import com.altomni.apn.common.service.activity.ActivityConfigService;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CachedFeignSsoUserMapping;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.canal.impl.CanalServiceImpl;
import com.altomni.apn.common.service.enums.*;
import com.altomni.apn.common.service.http.impl.HttpServiceImpl;
import com.altomni.apn.common.service.log.impl.LoggingServiceImpl;
import com.altomni.apn.common.service.sql.QueryProcessService;
import com.altomni.apn.common.service.wechat.WeChatUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SpringUtil;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 */
@EnableJpaRepositories("com.altomni.apn.*.repository")
@EntityScan(value = {"com.altomni.apn.*.domain", "com.altomni.apn.*.vo", "com.altomni.apn.*.web.rest.vm"})
@EnableFeignClients(basePackages = {"com.altomni.apn"})
@SpringBootApplication
@EnableDiscoveryClient
@EnableScheduling
@EnableCaching
@EnableAsync
@LoadBalancerClients(defaultConfiguration = LoadBalanceConfiguration.class)
@Import({ExceptionTranslator.class,
        PublicBeanInjection.class,
        HttpServiceImpl.class,
        DataPermissionAutoConfiguration.class,
        CachePermission.class,
        CachePermissionWriteOnly.class,
        CachedFeignSsoUserMapping.class,
        CommonRedisService.class,
        CommonApplicationProperties.class,
        TeamDataPermissionRule.class,
        FeignClientInterceptor.class,
        WebMvcConfig.class,
        SecurityObjectLevelInterceptor.class,
        SecurityDataLevelInterceptor.class,
        CustomResponseBodyAdviceAdapter.class,
        LoggingServiceImpl.class,
        SecurityUtils.class,
        AppInit.class,
        GlobalCacheConfig.class,
        JacksonConfiguration.class,
        EmailProperties.class,
        CacheConfig.class,
        SpringUtil.class,
        EnumGenderIdentity.class,
        EnumDictService.class,
        EnumCommonService.class,
        EnumCurrencyService.class,
        EnumDegreeService.class,
        EnumIndustryService.class,
        EnumIndustryMappingService.class,
        EnumJobFunctionService.class,
        EnumJobFunctionMappingService.class,
        EnumLanguageService.class,
        EnumJobPriorityService.class,
        EnumUserResponsibilityService.class,
        EnumWorkAuthorizationService.class,
        EnumLevelOfExperienceService.class,
        CanalServiceImpl.class,
        ActivityConfigService.class,
        QueryProcessService.class,
        JobdivaRabbitProperties.class,
        JobdivaRabbitMqConfig.class,
        CommonApiMultilingualConfig.class,
        WeChatUtils.class,
        WechatProperties.class,
        JwtTimesheetUserTokenStore.class,
        NoRepeatSubmitAspectConfiguration.class,
        SignGeneratorConfig.class,
        JwtAgencyUserTokenStore.class
})
public class JobApp {

    public static void main(String[] args) {
        SpringApplication.run(JobApp.class, args);
    }


}
