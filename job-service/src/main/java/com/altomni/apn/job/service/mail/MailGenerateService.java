package com.altomni.apn.job.service.mail;

import com.altomni.apn.common.domain.job.JobV3;

import java.util.List;

public interface MailGenerateService {

   void sendHtmlMailForUserJobRelationFromSupport(List<String> receivers, JobV3 job);

   void sendEmailProxyForIGP(String name, String from, String phone, String roleType, String content);

   void sendEmail(List<String> receivers, String subject, String content);
}
