package com.altomni.apn.job.service.dto.dict;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.dict.EnumDegree;
import com.altomni.apn.common.domain.dict.EnumIndustry;
import com.altomni.apn.common.domain.dict.EnumWorkAuthorization;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EnumDictDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "The dictCode for find special dict data")
    private String id;

    @ApiModelProperty(value = "The Chinese annotation for target value")
    private String label;

    @ApiModelProperty(value = "The English annotation for target value")
    private String labelCn;

    @ApiModelProperty(value = "The label words for web")
    private String value;

    @ApiModelProperty(value = "itemValue children list")
    private List<EnumDictDTO> children;

    @ApiModelProperty(value = "sign which itemValue is belongs to. when 0 then it's a parents itemValue")
    private String parentId;

    @ApiModelProperty(value = "sign checked for webSide")
    private Boolean checked;

    @ApiModelProperty(value = "The front display value of target value")
    private String display;

    @ApiModelProperty(value = "The name for target value")
    private String name;

    public static EnumDictDTO fromBizDict(EnumIndustry b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        if (ObjectUtil.isNotEmpty(b.getParentCategory())) {
            dto.setParentId(b.getParentCategory());
        } else {
            dto.setParentId(Constants.PARENT_NODE_ID);
        }
        dto.setChecked(false);
        return dto;
    }

    public static EnumDictDTO formatForCreation(EnumIndustry b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            if (b.getIsParent()) {
                dto.setId(b.getId() + Constants.PARENT_EN);
                dto.setValue(b.getId() + Constants.PARENT_EN);
            } else {
                dto.setId(StrUtil.toString(b.getId()));
                dto.setValue(StrUtil.toString(b.getId()));
            }
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        if (ObjectUtil.isNotEmpty(b.getParentCategory())) {
            dto.setParentId(b.getParentCategory());
        } else {
            dto.setParentId(Constants.PARENT_NODE_ID);
        }
        dto.setChecked(false);
        return dto;
    }

    public static EnumDictDTO fromBizDict(EnumWorkAuthorization b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        if (ObjectUtil.isNotEmpty(b.getParentCategory())) {
            dto.setParentId(b.getParentCategory());
        } else {
            dto.setParentId(Constants.PARENT_NODE_ID);
        }
        dto.setChecked(false);
        dto.setName(b.getName());
        return dto;
    }

    public static EnumDictDTO fromBizDict(EnumDegree b) {
        EnumDictDTO dto = new EnumDictDTO();
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setId(StrUtil.toString(b.getId()));
        }
        if (!b.getCnLable()){
            dto.setLabel(b.getEnDisplay());
        }else{
            dto.setLabel(b.getCnDisplay());
        }
        if (ObjectUtil.isNotEmpty(b.getId())) {
            dto.setValue(StrUtil.toString(b.getId()));
        }
        dto.setParentId(Constants.PARENT_NODE_ID);
        dto.setChecked(false);
        dto.setName(b.getName());
        return dto;
    }

}
