package com.altomni.apn.job.config.rabbit;

import com.altomni.apn.job.config.env.EsfillerMQProperties;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.SerializerMessageConverter;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.annotation.Resource;

@Configuration
@RefreshScope
public class RabbitMqConfig {

    @Value("${application.email-mq.addresses}")
    private String addresses;

    @Value("${application.email-mq.port}")
    private Integer port;

    @Value("${application.email-mq.virtual-host:/}")
    private String virtualHost;

    @Value("${application.email-mq.username}")
    private String username;

    @Value("${application.email-mq.password}")
    private String password;

    @Resource
    private EsfillerMQProperties esfillerMQProperties;

    @Bean(name = "emailConnectionFactory")
    @Primary
    public ConnectionFactory emailConnectionFactory() {
        return connectionFactory (addresses, port, virtualHost, username, password);
    }

    public CachingConnectionFactory connectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        return connectionFactory;
    }


    @Bean(name = "emailFactory")
    public SimpleRabbitListenerContainerFactory secondFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("emailConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "esfillerFactory")
    public SimpleRabbitListenerContainerFactory esfillerFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "emailRabbitAdmin")
    public RabbitAdmin emailRabbitAdmin(@Qualifier("emailConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        rabbitAdmin.setAutoStartup(true);
        return rabbitAdmin;
    }

    @Bean(name = "emailRabbitTemplate")
    @Primary
    public RabbitTemplate emailRabbitTemplate(
            @Qualifier("emailConnectionFactory") ConnectionFactory connectionFactory
    ){
        RabbitTemplate emailRabbitTemplate = new RabbitTemplate(connectionFactory);
        emailRabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        return emailRabbitTemplate;
    }

    @Bean(name = "esfillerConnectionFactory")
    public ConnectionFactory esfillerConnectionFactory() {
        return connectionFactory (esfillerMQProperties.getEsfillerMQHost(), esfillerMQProperties.getEsfillerMQPort(), esfillerMQProperties.getEsfillerMQVirtualHost(), esfillerMQProperties.getEsfillerMQUsername(), esfillerMQProperties.getEsfillerMQPassword());
    }

    @Bean(name = "esfillerRabbitTemplate")
    public RabbitTemplate esfillerRabbitTemplate(@Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitTemplate parsersRabbitTemplate = new RabbitTemplate(connectionFactory);
        parsersRabbitTemplate.setMandatory(true);
        parsersRabbitTemplate.setMessageConverter(new SerializerMessageConverter());
        return parsersRabbitTemplate;
    }

    @Bean(name = "esfillerAmqpAdmin")
    public AmqpAdmin esfillerAmqpAdmin(@Qualifier("esfillerConnectionFactory") ConnectionFactory connectionFactory) {
        AmqpAdmin amqpAdmin = new RabbitAdmin(connectionFactory);
        return amqpAdmin;
    }

    @Bean
    Queue normalizedJobQueue() {
        return new Queue(esfillerMQProperties.getApnNormalizedJobQueue(), true, false, false);
    }

    @Bean
    DirectExchange toEsFillerExchange() {
        DirectExchange directExchange = new DirectExchange(esfillerMQProperties.getEsfillerMQExchange());
        directExchange.setDelayed(true);
        return directExchange;
    }

    @Bean
    Binding normalizedJobBinding(Queue normalizedJobQueue, DirectExchange toEsFillerExchange) {
        return BindingBuilder.bind(normalizedJobQueue).to(toEsFillerExchange).with(esfillerMQProperties.getApnNormalizedJobRoutingKey());
    }
}
