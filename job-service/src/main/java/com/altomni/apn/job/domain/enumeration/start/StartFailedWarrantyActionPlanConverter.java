package com.altomni.apn.job.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class StartFailedWarrantyActionPlanConverter extends AbstractAttributeConverter<StartFailedWarrantyActionPlan, Integer> {
    public StartFailedWarrantyActionPlanConverter() {
        super(StartFailedWarrantyActionPlan::toDbValue, StartFailedWarrantyActionPlan::fromDbValue);
    }
}
