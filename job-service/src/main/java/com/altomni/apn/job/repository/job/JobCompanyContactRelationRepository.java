package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.JobCompanyContactRelation;
import com.altomni.apn.job.service.dto.job.IEsClientContact;
import com.altomni.apn.job.service.dto.job.JobCompanyContactRelationVO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the JobCompanyContactRelation entity.
 */
@Repository
public interface JobCompanyContactRelationRepository extends JpaRepository<JobCompanyContactRelation,Long> {

    List<JobCompanyContactRelation> findAllByJobId(Long jobId);

    List<JobCompanyContactRelation> findAllByJobIdIn(List<Long> jobId);

    void deleteAllByJobId(Long jobId);

    @Query(value ="SELECT distinct new  com.altomni.apn.job.service.dto.job.JobCompanyContactRelationVO(jr.clientContactId,t.fullName)  " +
            "FROM JobCompanyContactRelation jr " +
            "LEFT JOIN SalesLeadClientContact cc ON cc.id = jr.clientContactId " +
            "LEFT JOIN TalentV3 t ON t.id = cc.talentId " +
            "WHERE cc.companyId =:companyId AND cc.tenantId =:tenantId" ,nativeQuery = false)
    List<JobCompanyContactRelationVO> findJobContactByCompany(@Param("companyId") Long companyId, @Param("tenantId") Long tenantId);

    @Query(value ="select distinct con.id clientContactId, con.talent_id talentId from job_company_contact_relation rel " +
            "left join company_sales_lead_client_contact con on rel.client_contact_id=con.id " +
            "where rel.job_id =:jobId and con.talent_id is not null", nativeQuery = true)
    List<IEsClientContact> findClientContactsByJobId(@Param("jobId") Long jobId);
}
