package com.altomni.apn.job.repository.job;

import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.job.domain.job.SearchPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the jobSearch entity.
 */
@Repository
public interface SearchPreferenceRepository extends JpaRepository<SearchPreference,Long>, QuerydslPredicateExecutor<SearchPreference> {

    @Query(value = "select c.* from search_preference c where (LOWER(c.search_name) like %?2% or LOWER(c.search_content) like %?2%) and c.user_id = ?1 and module = ?3 order by c.created_date desc ", nativeQuery = true)
    List<SearchPreference> findAllByUserIdAndSearchNameAndModule(Long userId, String searchName, Integer module);

    List<SearchPreference> findAllByUserIdAndModuleOrderByCreatedDateDesc(Long userId, ModuleType modul);

    /*@Modifying
    @Query(value = "delete from search_preference where id = ?1", nativeQuery = true)
    void deleteByUserId(Long userId);*/

    @Modifying
    @Query(value = "delete from search_preference where user_id = ?1", nativeQuery = true)
    void deleteAllByUserId(Long userId);
}
