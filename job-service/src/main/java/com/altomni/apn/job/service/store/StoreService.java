package com.altomni.apn.job.service.store;

import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;

@Component
@FeignClient(value = "common-service")
public interface StoreService {

    @GetMapping("/common/api/v3/s3/store/is-exists/{uuid}/{uploadType}")
    ResponseEntity<Boolean> isExists(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @GetMapping("/common/api/v3/s3/store/detail-without-file-byte/{uuid}/{uploadType}")
    ResponseEntity<CloudFileObjectMetadata> getFileDetailWithoutFileFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @GetMapping("/common/api/v3/s3/store/file/{uuid}/{uploadType}")
    ResponseEntity<CloudFileObjectMetadata> getFileFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @GetMapping("/common/api/v3/s3/store/url-with-content-type/{uuid}/{uploadType}")
    ResponseEntity<String> getResignURLFromS3(@PathVariable("uuid") String uuid, @PathVariable("uploadType") String uploadType);

    @PostMapping("/common/api/v3/s3/store/upload-content-with-key")
    ResponseEntity<String> uploadContentStringWithKey(@RequestParam("uploadTypeEnum")  UploadTypeEnum uploadTypeEnum, @RequestParam("contentType") String contentType, @RequestParam("key") String key, @RequestBody String content);

    @PostMapping("/common/api/v3/s3/store/upload-contentBytes-with-key")
    ResponseEntity<String> uploadContentBytesWithKey(@RequestParam("uploadTypeEnum")  UploadTypeEnum uploadTypeEnum, @RequestParam("contentType") String contentType, @RequestParam("key") String key, @RequestBody byte[] contentBytes);

}
