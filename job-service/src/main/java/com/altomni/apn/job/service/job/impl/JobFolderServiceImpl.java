package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.folder.*;
import com.altomni.apn.common.dto.search.SearchConditionDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.folder.FolderPermission;
import com.altomni.apn.common.enumeration.folder.FolderType;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.sql.QueryProcessService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.enumeration.SharingTargetCategory;
import com.altomni.apn.job.domain.job.*;
import com.altomni.apn.job.domain.vm.SearchJobFolderVM;
import com.altomni.apn.job.repository.job.*;
import com.altomni.apn.job.service.dto.folder.*;
import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.altomni.apn.job.service.job.JobFolderService;
import com.altomni.apn.job.service.job.JobSearchFolderService;
import com.altomni.apn.job.service.mapper.jobFolder.JobFolderMapper;
import com.altomni.apn.job.service.mapper.jobFolder.JobFolderSharingTeamMapper;
import com.altomni.apn.job.service.mapper.jobFolder.JobFolderSharingUserMapper;
import com.altomni.apn.job.service.mapper.jobFolder.SearchJobFolderMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class JobFolderServiceImpl implements JobFolderService {

    final static String NAME_COLUMN = "f.name";

    final static String NOTE_COLUMN = "f.folder_note";
    final static String CREATED_DATE_COLUMN = "f.created_date";
    final static String LAST_MODIFIED_DATE_COLUMN = "f.last_modified_date";
    private static final String CREATOR = "f.puser_id";
    @Resource
    private JobFolderDetailRepository jobFolderDetailRepository;

    @Resource
    private JobFolderRepository jobFolderRepository;

    @Resource
    private JobFolderRelationRepository jobFolderRelationRepository;


    @Resource
    private JobFolderSharingTeamRepository jobFolderSharingTeamRepository;

    @Resource
    private JobFolderSharingUserRepository jobFolderSharingUserRepository;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private JobFolderMapper jobFolderMapper;

    @Resource
    private JobFolderSharingTeamMapper jobFolderSharingTeamMapper;
    @Resource
    private JobFolderSharingUserMapper jobFolderSharingUserMapper;

    @Resource
    private SearchJobFolderMapper searchJobFolderMapper;


    @Resource
    private JobSearchFolderService jobSearchFolderService;

    @Resource
    private EsFillerJobService esFillerJobService;

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private QueryProcessService queryProcessService;

    @Resource
    JobFolderSearchPageRepositoryCustom jobFolderSearchPageRepositoryCustom;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Override
    public Page<JobFolderDetail> getJobFolderDetailsByUserId(Long userId, Pageable pageable) {
        SearchConditionDTO.checkPageable(pageable);
        return jobFolderDetailRepository.findByPermissionUserId(userId, pageable);
    }


    @Override
    public Page<JobFolderBriefDTO> getMyJobFolderWithCountListByUserId(Long userId, Pageable pageable) {
        SearchConditionDTO.checkPageable(pageable);
        return jobFolderDetailRepository.findAllCountByUserId(userId, pageable);
    }


    @Override
    public Optional<FolderCreatorIdDTO> getJobFolderCreator(Long folderId) {
        return getJobFolder(folderId)
                .map(folder -> new FolderCreatorIdDTO(folder.getId(), folder.getPermissionUserId()));
    }

    @Override
    public List<JobFolder> getJobFolders(List<Long> folderIds) {
        return jobFolderRepository.findAllByIdIn(folderIds);
    }

    private Optional<JobFolder> getJobFolder(Long folderId) {
        return jobFolderRepository.findById(folderId);
    }

    @Override
    public FolderListDTO getCollaborativeJobFolderList() { // READWRITE permission folder list
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        log.info("[APN] fetch collaborative job folder list, user {}", SecurityUtils.getUserId());
        Long userId = SecurityUtils.getUserId();
        FolderListDTO folderListDTO = new FolderListDTO();
        folderListDTO.setUserId(userId);

        CompletableFuture<List<FolderNameDTO>> myFolderNamesFuture = CompletableFuture.supplyAsync(() -> getMyJobFolderNameList(userId));
        CompletableFuture<List<FolderNameDTO>> sharedFolderNamesFuture = CompletableFuture.supplyAsync(() -> getCollaborativeSharedJobFolderNameList(userId));
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(myFolderNamesFuture, sharedFolderNamesFuture);

        combinedFuture.exceptionally(ex -> {
            log.error("[APN] Error in asynchronous operation: " + ex.getMessage());
            return null;
        });

        combinedFuture.join();
        try {
            folderListDTO.setMyFolderList(myFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching my folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETFOLDER_FAILEDFETCHFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        try {
            folderListDTO.setSharedFolderList(sharedFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching shared folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETFOLDER_FAILEDSHAREFETCHFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        return folderListDTO;
    }

    @Override
    public FolderListDTO getCustomAndSharedJobFolderList() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        log.info("[APN] fetch collaborative job folder list, user {}", SecurityUtils.getUserId());
        Long userId = SecurityUtils.getUserId();
        FolderListDTO folderListDTO = new FolderListDTO();
        folderListDTO.setUserId(userId);

        CompletableFuture<List<FolderNameDTO>> myFolderNamesFuture = CompletableFuture.supplyAsync(() -> getMyJobFolderNameList(userId));
        CompletableFuture<List<FolderNameDTO>> sharedFolderNamesFuture = CompletableFuture.supplyAsync(() -> getAllSharedJobFolderNameList(userId));
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(myFolderNamesFuture, sharedFolderNamesFuture);

        combinedFuture.exceptionally(ex -> {
            log.error("[APN] Error in asynchronous operation: " + ex.getMessage());
            return null;
        });

        combinedFuture.join();
        try {
            folderListDTO.setMyFolderList(myFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching my folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETFOLDER_FAILEDFETCHFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        try {
            folderListDTO.setSharedFolderList(sharedFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching shared folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETFOLDER_FAILEDSHAREFETCHFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        return folderListDTO;
    }

    @Override
    public FolderPermissionListDTO getCustomAndSharedJobFolderWithPermissionList() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        log.info("[APN] fetch collaborative job folder list, user {}", SecurityUtils.getUserId());
        Long userId = SecurityUtils.getUserId();
        FolderPermissionListDTO folderListDTO = new FolderPermissionListDTO();
        folderListDTO.setUserId(userId);

        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<List<FolderNamePermissionDTO>> myFolderNamesFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return getMyJobFolderNameList(userId).stream()
                    .map(folderName -> new FolderNamePermissionDTO(folderName.getId(), folderName.getName(), FolderPermission.READWRITE))
                    .collect(Collectors.toList());
        });
        CompletableFuture<List<FolderNamePermissionDTO>> sharedFolderNamesFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return getAllSharedJobFolderNamePermissionList(userId);
        });
        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(myFolderNamesFuture, sharedFolderNamesFuture);

        combinedFuture.exceptionally(ex -> {
            log.error("[APN] Error in asynchronous operation: " + ex.getMessage());
            return null;
        });

        combinedFuture.join();
        try {
            folderListDTO.setMyFolderList(myFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching my folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETFOLDER_FAILEDFETCHFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        try {
            folderListDTO.setSharedFolderList(sharedFolderNamesFuture.join());
        } catch (CompletionException ex) {
            log.error("[APN] Error in fetching shared folder Name List: ", ex);
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETFOLDER_FAILEDSHAREFETCHFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        return folderListDTO;
    }

    public List<FolderNameDTO> getMyJobFolderNameList(Long userId) {
        return jobFolderRepository.findAllFolderNameOnlyByPermissionUserId(userId);
    }

    public List<FolderNameDTO> getCollaborativeSharedJobFolderNameList(Long userId) {
        return jobFolderRepository.findAllSharedFolderNameOnlyByUserIdAndPermission(userId, FolderPermission.READWRITE);
    }

    public List<FolderNamePermissionDTO> getAllSharedJobFolderNamePermissionList(Long userId) {
        List<FolderNamePermissionDTO> teamSharedFolderList = jobFolderSharingTeamRepository.findTeamSharedFolderByUserId(userId)
                .stream().map(f -> new FolderNamePermissionDTO(f.getId(), f.getName(), FolderPermission.fromDbValue(f.getFolderPermission())))
                .collect(Collectors.toList());
        List<FolderNamePermissionDTO> userSharedFolderList = jobFolderSharingUserRepository.findUserSharedFolderByUserId(userId);
        List<FolderNamePermissionDTO> combinedList = Stream.concat(teamSharedFolderList.stream(), userSharedFolderList.stream())
                .collect(Collectors.toList());

        Map<Long, FolderNamePermissionDTO> folderMap = combinedList.stream()
                .collect(Collectors.toMap(
                        FolderNamePermissionDTO::getId,
                        Function.identity(),
                        (f1, f2) -> new FolderNamePermissionDTO(f1.getId(), f1.getName(), FolderPermission.combine(f1.getFolderPermission(), f2.getFolderPermission()))
                ));

        List<FolderNamePermissionDTO> finalCombinedList = new ArrayList<>(folderMap.values());

        return finalCombinedList;
    }

    public List<FolderNameDTO> getAllSharedJobFolderNameList(Long userId) {
        return jobFolderRepository.findAllSharedFolderNameOnlyByUserId(userId);
    }


    @Override
    @Transactional
    public JobFolderAndJobsDTO addJobsInNewFolder(JobFolderAndJobsDTO jobFolderAndJobsDTO) {
        JobFolderDTO jobFolderDTO = createJobFolder(jobFolderAndJobsDTO.getJobFolder());
        JobFolderRelationListDTO jobFolderRelationListDTO = new JobFolderRelationListDTO();
        jobFolderRelationListDTO.setFolderIds(Collections.singletonList(jobFolderDTO.getId()));
        jobFolderRelationListDTO.setJobIds(jobFolderAndJobsDTO.getJobIds());
        addJobsToFolders(jobFolderRelationListDTO);
        return jobFolderAndJobsDTO;
    }


    @Override
    @Transactional
    public JobFolderDTO createJobFolder(JobFolderDTO jobFolderDTO) {
        if (jobFolderDTO == null || jobFolderDTO.getName().isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOBFOLDER_COMMONFOLDERDTONULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        Optional<JobFolder> jobFolderfromDB = Optional.ofNullable(jobFolderRepository.findOneByPermissionUserIdAndName(SecurityUtils.getUserId(), jobFolderDTO.getName()));
        if (jobFolderfromDB.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOBFOLDER_COMMONFOLDERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        } else {
            JobFolder jobFolder = jobFolderMapper.toEntity(jobFolderDTO);
            jobFolder.setTenantId(SecurityUtils.getTenantId());
            jobFolder = jobFolderRepository.save(jobFolder);
            Long jobFolderId = jobFolder.getId();
            if (jobFolderDTO.getShareTo() != null) {
                jobFolderDTO.setShareTo(addJobFolderSharing(jobFolderDTO.getShareTo(), jobFolderId));
            }
            jobFolderDTO.setId(jobFolder.getId());
            return jobFolderDTO;
        }

    }


    @Override
    @Transactional
    public JobFolderDTO updateJobFolder(JobFolderDTO jobFolderDTO, Long jobFolderId) {
        JobFolder jobFolder = getValidateJobFolderBeforeUpdate(jobFolderDTO, jobFolderId);

        jobFolder.setName(jobFolderDTO.getName());
        jobFolder.setFolderNote(jobFolderDTO.getFolderNote());
        jobFolder.setLastModifiedDate(Instant.now());// force update the modified date
        jobFolder = jobFolderRepository.save(jobFolder);

        if (jobFolderDTO.getShareTo() != null) {
            List<JobFolderSharingDTO> sharingDTO = updateFolderSharing(jobFolderDTO.getShareTo(), jobFolder.getId());
            jobFolderDTO.setShareTo(sharingDTO);
        } else {
            jobSearchFolderService.disableSearchFolder(jobFolderId);
        }
        jobFolderDTO.setId(jobFolder.getId());
        return jobFolderDTO;

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteFolder(Long folderId) {
        Optional<JobFolderDetail> jobFolder = jobFolderDetailRepository.findById(folderId);
        if (jobFolder.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_DELETEJOBFOLDER_FOLDERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        JobFolderDetail jobFolderDetail = jobFolder.get();
        if (jobFolderDetail.getPermissionUserId().equals(SecurityUtils.getUserId())) {
            jobFolderRepository.deleteById(folderId);
            List<Long> sharingUserIds = jobFolderDetail.getJobFolderSharingUsers().stream().map(JobFolderSharingUser::getId).collect(Collectors.toList());
            jobFolderSharingUserRepository.deleteAllByIdInBatch(sharingUserIds);
            List<Long> sharingTeamIds = jobFolderDetail.getJobFolderSharingTeams().stream().map(JobFolderSharingTeam::getId).collect(Collectors.toList());
            jobFolderSharingUserRepository.deleteAllByIdInBatch(sharingTeamIds);
            List<Long> relationIds = jobFolderDetail.getJobFolderRelations().stream().map(JobFolderRelation::getId).collect(Collectors.toList());
            jobFolderRelationRepository.deleteAllByIdInBatch(relationIds);

            jobSearchFolderService.disableSearchFolder(folderId);

        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_DELETEJOBFOLDER_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
    }


    /*
     * Folder Job Relation functions
     * */

    @Override
    @Transactional
    public void addJobsToFolders(JobFolderRelationListDTO jobFolderRelationListDTO) {
        if (!(jobFolderRelationListDTO.isJobsValid() && jobFolderRelationListDTO.isFoldersValid())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOBFOLDER_COMMONFOLDERDTONULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        if (!checkRWPermissionOnFolders(fetchAllFoldersIfExist(jobFolderRelationListDTO.getFolderIds()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_ADDJOBSTOFOLDERS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        validateJobs(jobFolderRelationListDTO.getJobIds());

        List<Pair<Long, Long>> jobAndFolderPair = getAllValidJobFolderRelation(jobFolderRelationListDTO.getJobIds(), jobFolderRelationListDTO.getFolderIds());

        List<JobFolderRelation> jobFolderRelations = jobAndFolderPair.stream()
                .map(pair -> {
                    JobFolderRelation jobFolderRelation = new JobFolderRelation();
                    jobFolderRelation.setJobId(pair.getLeft());
                    jobFolderRelation.setJobFolderId(pair.getRight());
                    return jobFolderRelation;
                })
                .collect(Collectors.toList());
        jobFolderRelationRepository.saveAll(jobFolderRelations);

        updateJobFolderRelationInES(jobFolderRelationListDTO.getJobIds(), jobFolderRelationListDTO.getFolderIds(), null);

    }


    @Override
    @Transactional
    public void deleteJobsFromFolder(JobFolderRelationListDTO jobFolderRelationListDTO, Long folderId) {
        if (!jobFolderRelationListDTO.isJobsValid()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOBFOLDER_COMMONFOLDERDTONULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        if (!checkRWPermissionOnFolders(Collections.singletonList(fetchFolderIfExist(folderId)))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_ADDJOBSTOFOLDERS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        validateJobs(jobFolderRelationListDTO.getJobIds());
        checkAllJobsUnderCurrentFolder(jobFolderRelationListDTO.getJobIds(), folderId);

        jobFolderRelationRepository.deleteAllByJobIdInAndJobFolderId(jobFolderRelationListDTO.getJobIds(), folderId);

        //sync up with es
        updateJobFolderRelationInES(jobFolderRelationListDTO.getJobIds(), null, Collections.singletonList(folderId));

    }


    /****
     * Folder Sharing functions
     * */
    private List<JobFolderSharingDTO> updateFolderSharing(List<JobFolderSharingDTO> jobFolderSharingDTOList, Long jobFolderId) {
        log.info("JobV3 update job folder {} sharing ", jobFolderId);
        List<JobFolderSharingDTO> updatedList = jobFolderSharingUserMapper.toFolderSharingDto(updateFolderSharingUser(jobFolderSharingDTOList, jobFolderId));
        updatedList.addAll(jobFolderSharingTeamMapper.toFolderSharingDto(updateFolderSharingTeam(jobFolderSharingDTOList, jobFolderId)));
        return updatedList;

    }


    private List<JobFolderSharingTeam> updateFolderSharingTeam(List<JobFolderSharingDTO> jobFolderSharingDTOList, Long jobFolderId) {
        log.info("[Job V3 {} ]: update current folder with team sharing", jobFolderId);
        List<JobFolderSharingTeam> oldFolderSharingList = jobFolderSharingTeamRepository.findAllByJobFolderId(jobFolderId);
        List<JobFolderSharingTeam> newFolderSharingList = jobFolderSharingDTOList.stream()
                .filter(sharing -> sharing.getTargetCategory() == SharingTargetCategory.TEAM)
                .map(dto -> {
                    JobFolderSharingTeam jobFolderSharingTeam = jobFolderSharingTeamMapper.toEntity(dto);
                    jobFolderSharingTeam.setJobFolderId(jobFolderId);
                    return jobFolderSharingTeam;
                })
                .collect(Collectors.toList());
        List<JobFolderSharingTeam> toAddFolderSharingList = new ArrayList<>();
        List<JobFolderSharingTeam> finalSharingList = new ArrayList<>();
        List<JobFolderSharingTeam> toDeleteSharingList = new ArrayList<>();
        if (oldFolderSharingList.size() != 0) {//delete existing and add new
            List<JobFolderSharingTeam> intersection = oldFolderSharingList.stream()
                    .filter(oldItem -> newFolderSharingList.stream()
                            .anyMatch(oldItem::isSameFolderSharing))
                    .collect(Collectors.toList());
            toAddFolderSharingList = newFolderSharingList.stream()
                    .filter(newItem -> oldFolderSharingList.stream()
                            .noneMatch(oldItem -> oldItem.isSameFolderSharing(newItem)))
                    .peek(item -> item.setJobFolderId(jobFolderId))
                    .collect(Collectors.toList());
            toDeleteSharingList = oldFolderSharingList.stream()
                    .filter(oldItem -> newFolderSharingList.stream()
                            .noneMatch(oldItem::isSameFolderSharing))
                    .collect(Collectors.toList());
            jobFolderSharingTeamRepository.deleteAllInBatch(toDeleteSharingList);
            finalSharingList.addAll(intersection);

            //recover sharing when user share to same team again;
            jobFolderSharingTeamRepository.updateExcludedUserIdsToNullByJobFolderIdAndTeamIdIn(jobFolderId, intersection.stream().map(JobFolderSharingTeam::getTeamId).collect(Collectors.toList()));

        } else if (newFolderSharingList.size() != 0) { //add if no old
            toAddFolderSharingList = newFolderSharingList;
        }

        finalSharingList.addAll(jobFolderSharingTeamRepository.saveAll(toAddFolderSharingList));

        //handle search folder
        updateSearchFolderSharingTeam(jobFolderId,
                toAddFolderSharingList.stream().map(JobFolderSharingTeam::getTeamId).collect(Collectors.toList()),
                toDeleteSharingList.stream().map(JobFolderSharingTeam::getTeamId).collect(Collectors.toList())

        );

        return finalSharingList;

    }

    private void updateSearchFolderSharingTeam(Long jobFolderId, List<Long> toEnableTeamIds, List<Long> toDisableTeamIds) {
        if (!toDisableTeamIds.isEmpty()) {
            jobSearchFolderService.disableSearchFolderWithFolderParamFromTeamShared(jobFolderId, toDisableTeamIds);
        }
        if (!toEnableTeamIds.isEmpty()) {
            jobSearchFolderService.enableSearchFolderWithFolderParamFromTeamShared(jobFolderId, toEnableTeamIds);
        }
    }


    private List<JobFolderSharingUser> updateFolderSharingUser(List<JobFolderSharingDTO> jobFolderSharingDTOList, Long jobFolderId) {
        log.info("[Job V3 {} ]: update current folder with user sharing", jobFolderId);
        Long userId = SecurityUtils.getUserId();
        List<JobFolderSharingUser> oldFolderSharingList = jobFolderSharingUserRepository.findAllByJobFolderId(jobFolderId);
        List<JobFolderSharingUser> newFolderSharingList = jobFolderSharingDTOList.stream()
                .filter(sharing -> sharing.getTargetCategory() == SharingTargetCategory.USER && !sharing.getTargetId().equals(userId))
                .map(dto -> {
                    JobFolderSharingUser jobFolderSharing = jobFolderSharingUserMapper.toEntity(dto);
                    jobFolderSharing.setJobFolderId(jobFolderId);
                    return jobFolderSharing;
                })
                .collect(Collectors.toList());
        List<JobFolderSharingUser> toAddFolderSharingList = new ArrayList<>();
        List<JobFolderSharingUser> finalSharingList = new ArrayList<>();
        List<JobFolderSharingUser> toDeleteSharingList = new ArrayList<>();
        if (oldFolderSharingList.size() != 0) {
            List<JobFolderSharingUser> intersection = oldFolderSharingList.stream()
                    .filter(oldItem -> newFolderSharingList.stream()
                            .anyMatch(oldItem::isSameFolderSharing))
                    .collect(Collectors.toList());
            toAddFolderSharingList = newFolderSharingList.stream()
                    .filter(newItem -> oldFolderSharingList.stream()
                            .noneMatch(oldItem -> oldItem.isSameFolderSharing(newItem)))
                    .peek(item -> item.setJobFolderId(jobFolderId))
                    .collect(Collectors.toList());
            toDeleteSharingList = oldFolderSharingList.stream()
                    .filter(oldItem -> newFolderSharingList.stream()
                            .noneMatch(oldItem::isSameFolderSharing))
                    .collect(Collectors.toList());
            jobFolderSharingUserRepository.deleteAllInBatch(toDeleteSharingList);
            finalSharingList.addAll(intersection);

//            //handle search folder
//            if (toDeleteSharingList.size() != 0) {
//                jobSearchFolderService.disableSearchFolderWithFolderParamFromUserShared(jobFolderId,
//                        toDeleteSharingList.stream().map(JobFolderSharingUser::getUserId).collect(Collectors.toList())
//                );
//            }
        } else if (newFolderSharingList.size() != 0) {
            toAddFolderSharingList = newFolderSharingList;
        }

        finalSharingList.addAll(jobFolderSharingUserRepository.saveAll(toAddFolderSharingList));
        //handle search folder

        updateSearchFolderSharingUser(jobFolderId,
                toAddFolderSharingList.stream().map(JobFolderSharingUser::getUserId).collect(Collectors.toList()),
                toDeleteSharingList.stream().map(JobFolderSharingUser::getUserId).collect(Collectors.toList())
        );

        return finalSharingList;

    }

    private void updateSearchFolderSharingUser(Long jobFolderId, List<Long> toEnableUserIds, List<Long> toDisableUserIds) {
        if (toDisableUserIds.size() != 0) {
            jobSearchFolderService.disableSearchFolderWithFolderParamFromUserShared(jobFolderId, toDisableUserIds);
        }
        if (toEnableUserIds.size() != 0) {
            jobSearchFolderService.enableSearchFolderWithFolderParamFromUserShared(jobFolderId, toEnableUserIds);
        }
    }


    private List<JobFolderSharingDTO> addJobFolderSharing(List<JobFolderSharingDTO> jobFolderSharingDTOList, Long jobFolderId) {
        return Stream.concat(
                addJobFolderSharingForTeams(jobFolderSharingDTOList, jobFolderId).stream(),
                addJobFolderSharingForUsers(jobFolderSharingDTOList, jobFolderId).stream())
                .collect(Collectors.toList());

    }

    private List<JobFolderSharingDTO> addJobFolderSharingForTeams(List<JobFolderSharingDTO> jobFolderSharingDTOList, Long jobFolderId) {
        log.info("[Job V3 {}]: add current folder with team sharing", jobFolderId);
        HashMap<Long, FolderPermission> existingTeamPermissions = new HashMap<>();
        List<JobFolderSharingTeam> jobFolderSharingTeamList = jobFolderSharingDTOList.stream()
                .filter(sharing -> sharing.getTargetCategory() == SharingTargetCategory.TEAM)
                .map(dto -> {
                    JobFolderSharingTeam jobFolderSharingTeam = jobFolderSharingTeamMapper.toEntity(dto);
                    jobFolderSharingTeam.setJobFolderId(jobFolderId);
                    if (existingTeamPermissions.containsKey(jobFolderSharingTeam.getTeamId())) {
                        FolderPermission existingPermission = existingTeamPermissions.get(jobFolderSharingTeam.getTeamId());
                        if (!existingPermission.equals(jobFolderSharingTeam.getPermission())) {
                            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_ADDJOBFOLDERSHARINGFORTEAMS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
                        }
                        return null;
                    } else {
                        existingTeamPermissions.put(jobFolderSharingTeam.getTeamId(), jobFolderSharingTeam.getPermission());
                    }
                    return jobFolderSharingTeam;
                })
                .collect(Collectors.toList());
        jobFolderSharingTeamList = jobFolderSharingTeamRepository.saveAll(jobFolderSharingTeamList);

        return jobFolderSharingTeamList.stream().map(jobFolderSharingTeamMapper::toFolderSharingDto).collect(Collectors.toList());
    }

    private List<JobFolderSharingDTO> addJobFolderSharingForUsers(List<JobFolderSharingDTO> jobFolderSharingDTOList, Long jobFolderId) {
        log.info("[Job V3 {}]: add current folder with user sharing", jobFolderId);
        Long userId = SecurityUtils.getUserId();
        HashMap<Long, FolderPermission> existingUserPermissions = new HashMap<>();
        List<JobFolderSharingUser> jobFolderSharingUserList = jobFolderSharingDTOList.stream()
                .filter(sharing -> sharing.getTargetCategory() == SharingTargetCategory.USER && !sharing.getTargetId().equals(userId))
                .map(dto -> {
                    JobFolderSharingUser jobFolderSharingUser = jobFolderSharingUserMapper.toEntity(dto);
                    jobFolderSharingUser.setJobFolderId(jobFolderId);
                    if (existingUserPermissions.containsKey(jobFolderSharingUser.getUserId())) {
                        FolderPermission existingPermission = existingUserPermissions.get(jobFolderSharingUser.getUserId());
                        if (!existingPermission.equals(jobFolderSharingUser.getPermission())) {
                            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_ADDJOBFOLDERSHARINGFORTEAMS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
                        }
                        return null;
                    } else {
                        existingUserPermissions.put(jobFolderSharingUser.getUserId(), jobFolderSharingUser.getPermission());
                    }

                    if (jobFolderSharingUser.DoesShareWithOwner(SecurityUtils.getUserId())) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_ADDJOBFOLDERSHARINGFORUSERS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
                    }
                    return jobFolderSharingUser;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        jobFolderSharingUserList = jobFolderSharingUserRepository.saveAll(jobFolderSharingUserList);

        return jobFolderSharingUserList.stream().map(jobFolderSharingUserMapper::toFolderSharingDto).collect(Collectors.toList());
    }


    @Override
    @Transactional
    public void removeSharingForSharedFolder(Long folderId) {
        if (folderId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOBFOLDER_COMMONFOLDERDTONULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        boolean isShareToUser = removeFolderSharingToUser(folderId);
        boolean isShareToTeam = removeUserFromFolderSharingToTeam(folderId);
        //jobSearchFolderService.disableSearchFolderWithSharedFolderParams(folderId, SecurityUtils.);

        if (!isShareToTeam && !isShareToUser) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_REMOVESHARINGFORSHAREDFOLDER_NOSHARE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
    }


    @Override
    public List<SharedFolderBriefDTO> getSharedFolderList(Long userId, Long teamId) {
        if (userId == null || teamId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOBFOLDER_COMMONFOLDERDTONULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        List<SharedFolderBriefDTO> shareToUser = jobFolderSharingUserRepository.findBriefBySharedUserId(userId);
        List<SharedFolderBriefDTO> shareToTeam = getTeamSharedFolderList(teamId);
        List<SharedFolderBriefDTO> sharedFolderBriefDTOList = Stream.concat(shareToUser.stream(), shareToTeam.stream())
                .collect(Collectors.toMap(
                        SharedFolderBriefDTO::getId,
                        Function.identity(),
                        (folder1, folder2) -> folder1.getPermission().compareTo(folder2.getPermission()) > 0 ? folder1 : folder2
                ))
                .values()
                .stream()
                .collect(Collectors.toList());

        return sharedFolderBriefDTOList;
    }

    @Override
    public Boolean validateFolderInSearch(List<Long> folderIds) {
        return checkSearchPermissionOnFolders(fetchAllFoldersIfExist(folderIds));
    }

    /*********************
     * folder utilize function
     * */


    private List<SharedFolderBriefDTO> getTeamSharedFolderList(Long teamId) {
        log.info("JobV3: get current team sharing folder list");
        List<JobFolderSharingTeam> folderSharingTeam = jobFolderSharingTeamRepository.findAllByTeamId(teamId);
        List<Long> teamSharingIds = folderSharingTeam.stream()
                .filter(sharingTeam -> {
                    Set<Long> excludedUserIds = jobFolderSharingTeamMapper.jsonStringToSet(sharingTeam.getExcludedUserIds());
                    return !excludedUserIds.contains(SecurityUtils.getUserId());
                })
                .map(JobFolderSharingTeam::getId)
                .collect(Collectors.toList());

        return jobFolderSharingTeamRepository.findBriefByIdIn(teamSharingIds);
    }

    private JobFolder getValidateJobFolderBeforeUpdate(JobFolderDTO jobFolderDTO, Long jobFolderId) {
        log.info("JobV3: get valid job folder list before updating current job folder ");
        if (jobFolderDTO == null || jobFolderId == null)
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOBFOLDER_COMMONFOLDERDTONULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));

        JobFolder jobFolderById = fetchFolderIfExist(jobFolderId);

        if (!checkRWPermissionOnFolders(Collections.singletonList(jobFolderById))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_ADDJOBSTOFOLDERS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        Optional<JobFolder> jobFolderByName = Optional.ofNullable(jobFolderRepository.findOneByPermissionUserIdAndName(SecurityUtils.getUserId(), jobFolderDTO.getName()));
        if (jobFolderByName.isPresent() && !jobFolderByName.get().getId().equals(jobFolderId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CREATEJOBFOLDER_COMMONFOLDERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        return jobFolderById;


    }

    private List<Pair<Long, Long>> getAllValidJobFolderRelation(List<Long> jobIds, List<Long> jobFolderIds) {

        Set<Pair<Long, Long>> existRelations = jobFolderRelationRepository.getAllByJobFolderIdIn(jobFolderIds)
                .stream().map(relation -> Pair.of(relation.getJobId(), relation.getJobFolderId())).collect(Collectors.toSet());
        Set<Pair<Long, Long>> receivedFolderJobPairList = jobIds.stream()
                .flatMap(jobId -> jobFolderIds.stream()
                        .map(folderId -> Pair.of(jobId, folderId)))
                .collect(Collectors.toSet());
        List<Pair<Long, Long>> newFolderJobPair = receivedFolderJobPairList.stream().filter(received -> !existRelations.contains(received)).collect(Collectors.toList());
        log.info("JOBS: Add Jobs to Folder, get all <job, folder>: {}, new added {}", receivedFolderJobPairList.size(), newFolderJobPair.size());
        return newFolderJobPair;
    }

    private List<JobFolder> fetchAllFoldersIfExist(List<Long> folderIds) {
        List<JobFolder> jobFolders = jobFolderRepository.findAllById(folderIds);
        if (jobFolders.size() != folderIds.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_FETCHALLFOLDERSIFEXIST_INVALIDFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        return jobFolders;
    }

    private JobFolder fetchFolderIfExist(Long folderId) {
        Optional<JobFolder> jobFolder = jobFolderRepository.findById(folderId);
        return jobFolder.orElseThrow(() -> new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_FETCHALLFOLDERSIFEXIST_INVALIDFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService())));
    }


    private boolean removeFolderSharingToUser(Long folderId) {

        Optional<JobFolderSharingUser> jobFolderSharing = jobFolderSharingUserRepository.findByJobFolderIdAndUserId(folderId, SecurityUtils.getUserId());
        if (jobFolderSharing.isEmpty()) {
            return false;
        }

        jobFolderSharingUserRepository.delete(jobFolderSharing.get());

        //disable the use of folderId in searchFolder
        jobSearchFolderService.disableSearchFolderWithFolderParamFromUserShared(folderId, Collections.singletonList(SecurityUtils.getUserId()));

        return true;
    }

    private boolean removeUserFromFolderSharingToTeam(Long folderId) {
        Optional<JobFolderSharingTeam> jobFolderSharing = jobFolderSharingTeamRepository.findByJobFolderIdAndTeamId(folderId, SecurityUtils.getTeamId());
        if (jobFolderSharing.isEmpty()) {
            return false;
        }

        JobFolderSharingTeam jobFolderSharingTeam = jobFolderSharing.get();
        Set<Long> ids = jobFolderSharingTeamMapper.jsonStringToSet(jobFolderSharingTeam.getExcludedUserIds());
        if (ids.contains(SecurityUtils.getUserId())) {//handle already removed sharing user
            return false;
        }
        ids.add(SecurityUtils.getUserId());
        jobFolderSharingTeam.setExcludedUserIds(jobFolderSharingTeamMapper.setToJsonString(ids));
        jobFolderSharingTeamRepository.save(jobFolderSharingTeam);

        //disable the use of folderId in searchFolder
        jobSearchFolderService.disableSearchFolderWithFolderParamFromUserShared(folderId, Collections.singletonList(SecurityUtils.getUserId()));

        return true;
    }

    private boolean checkRWPermissionOnFolders(List<JobFolder> jobFolders) {
        log.info("Check user read and write permission on folders: userId:{}, teamId: {}", SecurityUtils.getUserId(), SecurityUtils.getTeamId());
        List<Long> notOwnFolderIds = jobFolders.stream()
                .filter(folder -> !folder.getPermissionUserId().equals(SecurityUtils.getUserId()))
                .map(JobFolder::getId)
                .collect(Collectors.toList());
        if (notOwnFolderIds.size() > 0) {
            List<JobFolderSharingUser> shareToUserList = jobFolderSharingUserRepository.findAllByJobFolderIdInAndUserId(notOwnFolderIds, SecurityUtils.getUserId());
            shareToUserList.stream().filter(t -> t.hasWritePermission(SecurityUtils.getUserId())).forEach(t -> {
                notOwnFolderIds.remove(t.getJobFolderId());
            });
            if(CollUtil.isNotEmpty(notOwnFolderIds)){
                List<JobFolderSharingTeam> shareToTeamList = jobFolderSharingTeamRepository.findAllByJobFolderIdInAndTeamId(notOwnFolderIds, SecurityUtils.getTeamId());
                shareToTeamList.stream().filter(t->t.hasWritePermission(SecurityUtils.getTeamId())).forEach(t->{
                    JobFolderSharingTeamDTO jobFolderSharingTeamDTO = jobFolderSharingTeamMapper.toDto(t);
                    if(!jobFolderSharingTeamDTO.DoesUserRemoveTeamSharing(SecurityUtils.getUserId())){
                        notOwnFolderIds.remove(t.getJobFolderId());
                    }
                });
            }
        }
        return notOwnFolderIds.size() <= 0;
    }

    private boolean checkSearchPermissionOnFolders(List<JobFolder> jobFolders) {
        log.info("Check user read permission on folders: userId:{}, teamId: {}", SecurityUtils.getUserId(), SecurityUtils.getTeamId());
        List<Long> notOwnFolderIds = jobFolders.stream()
                .filter(folder -> !folder.getPermissionUserId().equals(SecurityUtils.getUserId()))
                .map(JobFolder::getId)
                .collect(Collectors.toList());
        if (notOwnFolderIds.size() > 0) {
            List<JobFolderSharingUser> shareToUserList = jobFolderSharingUserRepository.findAllByJobFolderIdInAndUserId(notOwnFolderIds, SecurityUtils.getUserId());
            shareToUserList.stream().filter(JobFolderSharingUser::hasReadPermission).forEach(t -> {
                notOwnFolderIds.remove(t.getJobFolderId());
            });
            if(CollUtil.isNotEmpty(notOwnFolderIds)){
                List<JobFolderSharingTeam> shareToTeamList = jobFolderSharingTeamRepository.findAllByJobFolderIdInAndTeamId(notOwnFolderIds, SecurityUtils.getTeamId());
                shareToTeamList.stream().filter(JobFolderSharingTeam::hasReadPermission).forEach(t->{
                    JobFolderSharingTeamDTO jobFolderSharingTeamDTO = jobFolderSharingTeamMapper.toDto(t);
                    if(!jobFolderSharingTeamDTO.DoesUserRemoveTeamSharing(SecurityUtils.getUserId())){
                        notOwnFolderIds.remove(t.getJobFolderId());
                    }
                });
            }
        }
        return notOwnFolderIds.size() <= 0;
    }

    private boolean validateJobs(List<Long> jobs) {
        List<JobV3> jobV3List = jobRepository.findAllByIdInAndTenantId(jobs, SecurityUtils.getTenantId());
        if (jobV3List.size() != jobs.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEJOBS_INVALIDJOB.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        return true;
    }

    private boolean checkAllJobsUnderCurrentFolders(List<Long> jobs, List<Long> jobFolderId) {
        List<JobFolderRelation> relations = jobFolderRelationRepository.getAllByJobFolderIdIn(jobFolderId);
        boolean doesJobsAllinFolder = new HashSet<>(relations.stream().map(JobFolderRelation::getJobId).collect(Collectors.toList())).containsAll(jobs);
        if (!doesJobsAllinFolder) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CHECKALLJOBSUNDERCURRENTFOLDER_INVALIDJOB.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        return true;
    }

    private boolean checkAllJobsUnderCurrentFolder(List<Long> jobs, Long jobFolderId) {
        List<JobFolderRelation> relations = jobFolderRelationRepository.getAllByJobFolderId(jobFolderId);
        boolean doesJobsAllinFolder = relations.stream().map(JobFolderRelation::getJobId).collect(Collectors.toSet()).containsAll(jobs);
        if (!doesJobsAllinFolder) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_CHECKALLJOBSUNDERCURRENTFOLDER_INVALIDJOB.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        return true;
    }

    /*
    sub folder menu
    */
    @Override
    public List<FolderSharedTeamDTO> getSharedTeamsByUserId() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        return jobFolderSharingTeamRepository.findJobFolderSharedTeamsByUserId(SecurityUtils.getUserId());
    }

    @Override
    public List<FolderSharedTeamDTO> getDistinctSharedTeamsByUserId() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        return jobFolderSharingTeamRepository.findDistinctJobFolderSharedTeamsByUserId(SecurityUtils.getUserId());
    }

    @Override
    public List<FolderSharedUserDTO> getSharedUsersByUserId() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        return jobFolderSharingUserRepository.findJobFolderSharedUsersByUserId(SecurityUtils.getUserId());
    }

    @Override
    public List<FolderSharedUserDTO> getDistinctSharedUsersByUserId() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        //return jobFolderSharingUserRepository.findJobFolderSharedUsersByUserId(SecurityUtils.getUserId());
        List<FolderSharedUserDTO> sharedUserDTOList = jobFolderSharingUserRepository.findDistinctJobFolderSharedUsersByUserId(SecurityUtils.getUserId());
        return sharedUserDTOList;

    }


    @Override
    public List<FolderSharedTeamDTO> getDistinctSharedTeamsForSharedFolderByUserId(Long userId) {
        if (userId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        return jobFolderSharingTeamRepository.findDistinctSharedTeamsForSharedFolderByUserId(userId)
                .stream().map(t -> new FolderSharedTeamDTO(t.getTeamId(), t.getTeamName()))
                .collect(Collectors.toList());
    }

    @Override
    public List<FolderSharedUserDTO> getDistinctSharedUsersForSharedFolderByUserId(Long userId) {
        if (userId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }
        //return jobFolderSharingUserRepository.findJobFolderSharedUsersByUserId(SecurityUtils.getUserId());
        List<FolderSharedUserDTO> sharedUserDTOList = jobFolderSharingUserRepository.findDistinctJobFolderSharedUsersForSharedFolderByUserId(userId);
        return sharedUserDTOList;

    }

    @Override
    public FolderSharedUserAndTeamDTO getSharedUserAndTeamList() {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_COMMON_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        Long userId = SecurityUtils.getUserId();
        CompletableFuture<List<FolderSharedTeamDTO>> sharedTeamsFuture =
                CompletableFuture.supplyAsync(() -> getDistinctSharedTeamsForSharedFolderByUserId(userId));
        CompletableFuture<List<FolderSharedUserDTO>> sharedUsersFuture =
                CompletableFuture.supplyAsync(() -> getDistinctSharedUsersForSharedFolderByUserId(userId));

        CompletableFuture<Void> combinedFuture = CompletableFuture.allOf(sharedTeamsFuture, sharedUsersFuture);

        combinedFuture.join(); // wait for all async tasks to complete

        List<FolderSharedTargetNameDTO> sharedTeams = sharedTeamsFuture.join().stream()
                .map(team -> new FolderSharedTargetNameDTO(team.getTeamId(), team.getTeamName()))
                .collect(Collectors.toList());
        List<FolderSharedTargetNameDTO> sharedUsers = sharedUsersFuture.join().stream()
                .map(user -> new FolderSharedTargetNameDTO(user.getUserId(), CommonUtils.formatFullName(user.getFirstName(), user.getLastName())))
                .collect(Collectors.toList());

        return new FolderSharedUserAndTeamDTO(sharedTeams, sharedUsers);
    }


    /***
     * Search sub folder: custom folder and shared folder
     * */

    @Override
    public Page<SearchJobFolderDTO> searchJobFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable) {
        Page<SearchJobFolderVM> folderVMPage = getJobFolders(searchRequestDTO, pageable);
        List<SearchJobFolderDTO> folderDTOList = toFolderDTO(folderVMPage.getContent(), searchRequestDTO.getFolderType());

        return new PageImpl<>(folderDTOList, pageable, folderVMPage.getTotalElements());
    }

    private List<SearchJobFolderDTO> toFolderDTO(List<SearchJobFolderVM> searchJobFolderVMList, FolderType folderType) {
        List<SearchJobFolderDTO> folderDTOList = searchJobFolderMapper.toDto(searchJobFolderVMList);
        List<Long> folderIds = folderDTOList.stream().map(SearchJobFolderDTO::getId).collect(Collectors.toList());
        if (folderIds.size() == 0) {
            return folderDTOList;
        }

        List<FolderSharedUserDTO> userDTOList = jobFolderSharingUserRepository.findJobFolderSharingUserByJobFolderIdIn(folderIds);
        List<FolderSharedTeamDTO> teamDTOList = jobFolderSharingTeamRepository.findJobFolderSharedTeamByJobFolderIdIn(folderIds);
        Map<Long, List<FolderSharedUserDTO>> userDTOMap = userDTOList.stream()
                .map(userDTO -> {
                    userDTO.setFullName(CommonUtils.formatFullName(userDTO.getFirstName(), userDTO.getLastName()));
                    return userDTO;
                })
                .collect(Collectors.groupingBy(FolderSharedUserDTO::getFolderId));

        Map<Long, List<FolderSharedTeamDTO>> teamDTOMap = teamDTOList.stream()
                .collect(Collectors.groupingBy(FolderSharedTeamDTO::getFolderId));

        // set the shareTo info
        folderDTOList.forEach(folderDTO -> {
            StringJoiner sharedTo = new StringJoiner(",");
            if (userDTOMap.containsKey(folderDTO.getId())) {
                folderDTO.setSharedUsers(userDTOMap.get(folderDTO.getId()).stream().map(o -> new FolderSharedTargetBriefDTO(o.getUserId(), (folderType.equals(FolderType.CUSTOMIZED) ? o.getFolderPermission() : null), o.getFullName())).collect(Collectors.toList()));
                userDTOMap.get(folderDTO.getId()).forEach(item -> sharedTo.add(item.getFullName()));
            }
            if (teamDTOMap.containsKey(folderDTO.getId())) {
                folderDTO.setSharedTeams(teamDTOMap.get(folderDTO.getId()).stream().map(o -> new FolderSharedTargetBriefDTO(o.getTeamId(), (folderType.equals(FolderType.CUSTOMIZED) ? o.getFolderPermission() : null), o.getTeamName())).collect(Collectors.toList()));
                teamDTOMap.get(folderDTO.getId()).forEach(item -> sharedTo.add(item.getTeamName()));
            }
        });

        //set the permission for each shared folder when request is for shared
        if (FolderType.SHARED.equals(folderType)) {
            Map<Long, FolderPermission> folderPermissionOnUserMap = userDTOList.stream().filter(o -> o.getUserId().equals(SecurityUtils.getUserId())).collect(Collectors.toMap(FolderSharedUserDTO::getFolderId, FolderSharedUserDTO::getFolderPermission));


            for (SearchJobFolderDTO folder : folderDTOList) {
                if (folderPermissionOnUserMap.containsKey(folder.getId())) {
                    folder.setFolderPermission(folderPermissionOnUserMap.get(folder.getId()));
                }
            }

            //set permission from the team permission
            List<JobFolderSharingTeam> jobFolderSharingTeamList = jobFolderSharingTeamRepository.findByJobFolderIdInAndUserId(teamDTOList.stream().map(FolderSharedTeamDTO::getFolderId).collect(Collectors.toList()), SecurityUtils.getUserId());
            Map<Long, FolderPermission> folderPermissionOnTeamMap = jobFolderSharingTeamList.stream()
                    .collect(Collectors.toMap(
                            JobFolderSharingTeam::getJobFolderId,
                            JobFolderSharingTeam::getPermission,
                            FolderPermission::combine
                    ));

            //overwrite the permission if the team permission on user is over user permission
            for (SearchJobFolderDTO folder : folderDTOList) {
                if (folderPermissionOnTeamMap.containsKey(folder.getId())) {
                    folder.setFolderPermission(FolderPermission.combine(
                            folder.getFolderPermission(),
                            folderPermissionOnTeamMap.get(folder.getId())
                    ));
                }
            }
        }

        return folderDTOList;
    }


    private Page<SearchJobFolderVM> getJobFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable) {
        log.info("[APN get folder list by search request userId:{}", SecurityUtils.getUserId());
        Map<Integer, Object> paramsMap = new HashMap<>(16);
        StringBuffer countSql = new StringBuffer();
        StringBuffer dataSql = new StringBuffer();
        setSearchCustomFolderSql(searchRequestDTO, pageable, paramsMap, countSql, dataSql);
        Long total = jobFolderSearchPageRepositoryCustom.searchCount(countSql.toString(), paramsMap);
        List<SearchJobFolderVM> customFolderList = jobFolderSearchPageRepositoryCustom.searchData(dataSql.toString(), SearchJobFolderVM.class, paramsMap);
        return new PageImpl<>(customFolderList, pageable, total);
    }


    private void setSearchCustomFolderSql(FolderSearchRequestDTO searchRequestDTO, Pageable pageable, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (searchRequestDTO.getFolderType().equals(FolderType.CUSTOMIZED)) {
            setSearchJobCustomFolderSQL(countSql, dataSql);

        } else if (searchRequestDTO.getFolderType().equals(FolderType.SHARED)) {
            setSearchJobSharedFolderSQL(countSql, dataSql);
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_SETSEARCHCUSTOMFOLDERSQL_NOSHAREFOLDER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), jobApiPromptProperties.getJobService()));
        }

        setFolderSearchParam(searchRequestDTO, paramsMap, countSql, dataSql);
        setFolderPageable(pageable, dataSql);
    }

    private void setSearchJobCustomFolderSQL(StringBuffer countSql, StringBuffer dataSql) {
        countSql.append("SELECT COUNT(f.id) FROM job_folder f WHERE f.puser_id = ").append(SecurityUtils.getUserId());
        dataSql.append("SELECT f.*, CASE WHEN CONCAT(u.first_name, ' ', u.last_name) REGEXP '[A-Za-z]' THEN CONCAT(u.first_name, ' ', u.last_name)\n" +
                "       ELSE CONCAT(u.last_name, u.first_name)\n" +
                "  END AS creator FROM job_folder f INNER JOIN `user` u ON u.id = f.puser_id WHERE f.puser_id = ").append(SecurityUtils.getUserId());

    }

    private void setSearchJobSharedFolderSQL(StringBuffer countSql, StringBuffer dataSql) {
        countSql.append("SELECT COUNT(f.id) \n" +
                "	FROM job_folder f \n" +
                "	WHERE f.puser_id <> ").append(SecurityUtils.getUserId()).append("\n" +
                "   AND (EXISTS ( \n" +
                "			SELECT 1 \n" +
                "			FROM job_folder_sharing_user su \n" +
                "			WHERE f.id = su.job_folder_id AND su.user_id = ").append(SecurityUtils.getUserId()).append("\n" +
                "	) OR EXISTS ( \n" +
                "			SELECT 1 \n" +
                "			FROM job_folder_sharing_team st \n" +
                "			INNER JOIN permission_user_team ptu ON st.team_id = ptu.team_id \n" +
                "			WHERE f.id = st.job_folder_id AND ptu.user_id = ").append(SecurityUtils.getUserId()).append("\n" +
                "               AND (st.excluded_user_ids IS NULL OR JSON_CONTAINS(st.excluded_user_ids, CAST(").append(SecurityUtils.getUserId()).append(" AS json)) = 0 ) \n" +
                "	))");

        dataSql.append("SELECT f.* \n" +
                "	, CONCAT(u.first_name,' ', u.last_name) creator FROM job_folder f INNER JOIN `user` u ON u.id = f.puser_id \n" +
                "	WHERE f.puser_id <> ").append(SecurityUtils.getUserId()).append("\n" +
                " AND (EXISTS ( \n" +
                "			SELECT 1 \n" +
                "			FROM job_folder_sharing_user su \n" +
                "			WHERE f.id = su.job_folder_id AND su.user_id = ").append(SecurityUtils.getUserId()).append("\n" +
                "	) OR EXISTS ( \n" +
                "			SELECT 1 \n" +
                "			FROM job_folder_sharing_team st \n" +
                "			INNER JOIN permission_user_team ptu ON st.team_id = ptu.team_id \n" +
                "			WHERE f.id = st.job_folder_id AND ptu.user_id = ").append(SecurityUtils.getUserId()).append(" AND (st.excluded_user_ids IS NULL OR JSON_CONTAINS(st.excluded_user_ids, CAST(").append(SecurityUtils.getUserId()).append(" AS json)) = 0 ) \n" +
                "	))");
    }


    //TODO: use general method;
    private void setFolderSearchParam(FolderSearchRequestDTO searchRequestDTO, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {

        if (searchRequestDTO.getCreatedDate() != null) {
            queryProcessService.setDateBetweenParamFilter(paramsMap, countSql, dataSql, CREATED_DATE_COLUMN, searchRequestDTO.getCreatedDate().getDateFrom(), searchRequestDTO.getCreatedDate().getDateTo());
        }

        if (searchRequestDTO.getLastModifiedDate() != null) {
            queryProcessService.setDateBetweenParamFilter(paramsMap, countSql, dataSql, LAST_MODIFIED_DATE_COLUMN, searchRequestDTO.getLastModifiedDate().getDateFrom(), searchRequestDTO.getLastModifiedDate().getDateTo());
        }

        //TODO: remove
        Instant createdDateFrom = searchRequestDTO.getCreatedDateFrom();
        Instant createdDateTo = searchRequestDTO.getCreatedDateTo();
        if (createdDateFrom != null) {
            countSql.append(" AND f.created_date > ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.created_date > ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, createdDateFrom);
        }
        if (createdDateTo != null) {
            countSql.append(" AND f.created_date < ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.created_date < ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, createdDateTo);
        }

        Instant lastModifiedDateFrom = searchRequestDTO.getLastModifiedDateFrom();
        Instant lastModifiedDateTo = searchRequestDTO.getLastModifiedDateTo();
        if (lastModifiedDateFrom != null) {
            countSql.append(" AND f.last_modified_date > ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.last_modified_date > ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, lastModifiedDateFrom);
        }
        if (lastModifiedDateTo != null) {
            countSql.append(" AND f.last_modified_date < ?").append(paramsMap.size() + 1);
            dataSql.append(" AND f.last_modified_date < ?").append(paramsMap.size() + 1);
            paramsMap.put(paramsMap.size() + 1, lastModifiedDateTo);
        }

        //folder name
//        String name = searchRequestDTO.getName();
//        if (name != null && StringUtils.isNotEmpty(name)) {
//            countSql.append(" AND f.name LIKE ?").append(paramsMap.size() + 1);
//            dataSql.append(" AND f.name LIKE ?").append(paramsMap.size() + 1);
//            paramsMap.put(paramsMap.size() + 1, String.format("%%%s%%", name));
//        }
        if (StringUtils.isNotBlank(searchRequestDTO.getName())) {
            queryProcessService.setColumnLikeParamFilter(paramsMap, countSql, dataSql, NAME_COLUMN, searchRequestDTO.getName());
        }


        //folder note
        if (StringUtils.isNotBlank(searchRequestDTO.getNote())) {
            queryProcessService.setColumnLikeParamFilter(paramsMap, countSql, dataSql, NOTE_COLUMN, searchRequestDTO.getNote());
        }
//        String note = searchRequestDTO.getNote();
//        if (note != null && StringUtils.isNotEmpty(note)) {
//            countSql.append(" AND f.folder_note LIKE ?").append(paramsMap.size() + 1);
//            dataSql.append(" AND f.folder_note LIKE ?").append(paramsMap.size() + 1);
//            paramsMap.put(paramsMap.size() + 1, String.format("%%%s%%", note));
//        }

        //Share User/Team query
        List<Long> sharedUsers = searchRequestDTO.getSharedUsers();
        if (sharedUsers != null && !sharedUsers.isEmpty()) {
            countSql.append(" AND EXISTS (SELECT 1 FROM job_folder_sharing_user fu WHERE fu.job_folder_id = f.id AND fu.user_id IN ?").append(paramsMap.size() + 1).append(")");
            dataSql.append(" AND EXISTS (SELECT 1 FROM job_folder_sharing_user fu WHERE fu.job_folder_id = f.id AND fu.user_id IN ?").append(paramsMap.size() + 1).append(")");
            paramsMap.put(paramsMap.size() + 1, sharedUsers);
        }

        List<Long> sharedTeams = searchRequestDTO.getSharedTeams();
        if (sharedTeams != null && !sharedTeams.isEmpty()) {
            countSql.append(" AND EXISTS (SELECT 1 FROM job_folder_sharing_team ft WHERE ft.job_folder_id = f.id AND ft.team_id IN ?").append(paramsMap.size() + 1).append(")");
            dataSql.append(" AND EXISTS (SELECT 1 FROM job_folder_sharing_team ft WHERE ft.job_folder_id = f.id AND ft.team_id IN ?").append(paramsMap.size() + 1).append(")");
            paramsMap.put(paramsMap.size() + 1, sharedTeams);
        }

        List<Long> creators = searchRequestDTO.getCreator();
//        if (creators != null && !creators.isEmpty()) {
//            countSql.append(" AND f.puser_id IN ?").append(paramsMap.size() + 1);
//            dataSql.append(" AND f.puser_id IN ?").append(paramsMap.size() + 1);
//            paramsMap.put(paramsMap.size() + 1, creators);
//        }
        if (creators != null && !creators.isEmpty()) {
            queryProcessService.setColumnIdsInParaFilter(paramsMap, countSql, dataSql, CREATOR, creators);
        }


//        List<String> generalTexts = searchRequestDTO.getGeneralText();
//        if (generalTexts != null && !generalTexts.isEmpty()) {
//            countSql.append(" AND (");
//            dataSql.append(" AND (");
//            StringJoiner joiner = new StringJoiner(" AND ");
//            generalTexts.forEach(generalText -> {
//                joiner.add("f.`name` LIKE ?" + (paramsMap.size() + 1));
//                paramsMap.put(paramsMap.size() + 1, String.format("%%%s%%", generalText));
//            });
//            countSql.append(joiner).append(")");
//            dataSql.append(joiner).append(")");
//        }
        List<String> generalTexts = searchRequestDTO.getGeneralText();
        if (generalTexts != null && !generalTexts.isEmpty()) {
            queryProcessService.setSingleGeneralSearchParamFilter(paramsMap, countSql, dataSql, NAME_COLUMN, generalTexts.get(0));
        }

    }

    /***
     * Search sub folder: sql search utilize
     */


    private void setFolderPageable(Pageable pageable, StringBuffer dataSql) {
        int size = pageable.getPageSize();
        int page = pageable.getPageNumber();
        int start = size * page;
        if (pageable.getSort().isSorted()) {
            dataSql.append(" ORDER BY ");
            StringJoiner joiner = new StringJoiner(", ");
            for (Sort.Order order : pageable.getSort()) {
                String column = null;
                switch (order.getProperty()) {
                    case "createdDate":
                        column = "created_date";
                        break;
                    case "lastModifiedDate":
                        column = "last_modified_date";
                        break;
                    case "creator":
                        column = "CASE WHEN creator IS NULL OR creator = '' THEN 0 ELSE 1 END DESC, CONVERT(creator USING gbk)";
                        break;
                    case "name":
                        column = "CONVERT(name USING gbk)";
                        break;
                    default:
                        column = order.getProperty();
                }
                joiner.add(column + " " + order.getDirection());
            }
            dataSql.append(joiner);
        }
        dataSql.append(" LIMIT ").append(start).append(", ").append(size);
    }

    /***
     * Update es filler
     */
    private void updateJobFolderRelationInES(List<Long> jobIds, List<Long> toFolderIds, List<Long> fromFolderIds) {
        if (CollUtil.isEmpty(jobIds) || (toFolderIds == null && fromFolderIds == null)) {
            return;
        }

        Long tenantId = SecurityUtils.getTenantId();
        CompletableFuture.supplyAsync(() -> {
            esFillerJobService.updateJobFolder(jobIds,
                    toFolderIds == null ? null : toFolderIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()),
                    fromFolderIds == null ? null : fromFolderIds.stream().distinct().map(String::valueOf).collect(Collectors.toList()),
                    tenantId);
            return 0;
        });
    }

}
