package com.altomni.apn.job.service.company;

import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.dto.company.AccountBusinessJobDTO;
import com.altomni.apn.common.dto.company.BriefCompanyDTO;
import com.altomni.apn.common.dto.company.ClientContactBriefInfoDTO;
import com.altomni.apn.common.dto.company.ICompanyTeamUser;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.company.domain.business.AccountBusiness;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import com.altomni.apn.company.web.rest.vm.saleslead.SalesLeadClientContactVM;
import com.altomni.apn.job.service.dto.job.CompanyProjectTeamDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@Component
//@FeignClient(value = "company-service", fallback = CompanyServiceFallback.class)
@FeignClient(value = "company-service")
public interface CompanyService {

    @GetMapping("/company/api/v3/company/{id}")
    ResponseEntity<CompanyDTO> getCompany(@PathVariable("id") Long companyId);

    @PostMapping("/company/api/v3/company/find-by-ids")
    ResponseEntity<List<CompanyDTO>> getCompanyByIds(@RequestBody List<Long> companyIds);

    @GetMapping("/company/api/v3/saleslead/client-contacts/{id}")
    ResponseEntity<SalesLeadClientContactVM> getSalesLeadClientContact(@PathVariable("id") Long id);

    @GetMapping("/project-teams/{id}")
    ResponseEntity<CompanyProjectTeamDTO> getProjectTeam(@PathVariable("id") Long id);

    @GetMapping("/project-teams/users/{teamId}")
    ResponseEntity<List<UserBriefDTO>> getUsersByTeamId(@PathVariable("teamId") Long teamId);

    @GetMapping("/company/api/v3/sales-leads/{companyId}/am")
    ResponseEntity<List<Long>> getAllAmByCompany(@PathVariable("companyId") Long companyId);

    @GetMapping("/company/api/v3/sales-leads/job/{jobId}/am")
    ResponseEntity<List<Long>> getAllAmByJob(@PathVariable("jobId") Long companyId);

    @GetMapping("/company/api/v3/sales-leads/am-group-by-company")
    ResponseEntity<Map<Long, JSONArray>> getAllAmGroupByCompany();

    @GetMapping("/company/api/v3/company/application-users/{companyId}")
    ResponseEntity<List<AssignedUserDTO>> getApplicationUsers(@PathVariable("companyId") Long companyId);

    @GetMapping("/company/api/v3/company/{companyId}/team-users")
    ResponseEntity<List<ICompanyTeamUser.CompanyTeamUserDTO>> getCompanyTeamUsersByCompanyId(@PathVariable("companyId") Long companyId, @RequestParam(value = "salesLeadId") Long salesLeadId);

    @PostMapping("/company/api/v3/company/job-to-company")
    ResponseEntity<Map<Long, String>> getCompanyNamesByJobIds(@RequestBody List<Long> jobIds);

    @GetMapping("/company/api/v3/saleslead/client-contacts/company/{companyId}/job-contact-client/{clientContactId}")
    ResponseEntity<ClientContactBriefInfoDTO> getCompanyClientContactBriefInfo(@PathVariable("companyId") Long companyId, @PathVariable("clientContactId") Long clientContactId);

    @GetMapping("/company/api/v3/company/company-name/{companyId}/")
    ResponseEntity<String> getCompanyNameByCompanyId(@PathVariable("companyId") Long companyId);

    @GetMapping("/company/api/v3/company/list/")
    ResponseEntity<List<AccountCompanyVO>> getAllClientCompanyList(@RequestParam(value = "active") Boolean active);


    @GetMapping("/company/api/v3/company/brief-company-list")
    ResponseEntity<List<BriefCompanyDTO>> getAllBriefCompanyList(@RequestParam(value = "tenantId") Long tenantId);

    @PostMapping("/company/api/v3/salesLead/getAllByBusinessUnit")
    ResponseEntity<List<AccountBusiness>> getAllByBusinessUnit(@RequestBody AccountBusinessJobDTO accountBusinessDTO);

}
