package com.altomni.apn.job.service.dto.folder;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class JobCategoryCountRequestDTO {
    private Long userId;
    private String index;
    private List<Long> recruitmentProcessIds;
    private DataPermission dataPermission;

    public JobCategoryCountRequestDTO(Long userId, String index, List<Long> recruitmentProcessIds){
        this.userId = userId;
        this.index = index;
        this.recruitmentProcessIds = recruitmentProcessIds;
    }


    public static class DataPermission {
        private List<String> affiliations;

        private String affiliationForPrivateJob;

        private boolean privateJobPermission;

        private Boolean createdByOwn;

        public List<String> getAffiliations() {
            return affiliations;
        }

        public void setAffiliations(List<String> affiliations) {
            this.affiliations = affiliations;
        }

        public String getAffiliationForPrivateJob() {
            return affiliationForPrivateJob;
        }

        public void setAffiliationForPrivateJob(String affiliationForPrivateJob) {
            this.affiliationForPrivateJob = affiliationForPrivateJob;
        }

        public boolean isPrivateJobPermission() {
            return privateJobPermission;
        }

        public void setPrivateJobPermission(boolean privateJobPermission) {
            this.privateJobPermission = privateJobPermission;
        }

        public Boolean getCreatedByOwn() {
            return createdByOwn;
        }

        public void setCreatedByOwn(Boolean createdByOwn) {
            this.createdByOwn = createdByOwn;
        }

        @Override
        public String toString() {
            return "DataPermission{" +
                    "affiliations=" + affiliations +
                    ", affiliationForPrivateJob='" + affiliationForPrivateJob + '\'' +
                    ", privateJobPermission=" + privateJobPermission +
                    ", createdByOwn=" + createdByOwn +
                    '}';
        }
    }
}
