package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.folder.FolderCreatorIdDTO;
import com.altomni.apn.common.dto.folder.FolderListDTO;
import com.altomni.apn.common.dto.search.SearchConditionDTO;
import com.altomni.apn.common.dto.user.NameEmailUserDTO;
import com.altomni.apn.common.dto.folder.FolderSearchRequestDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.folder.SearchFolderStatus;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.service.sql.QueryProcessService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.enumeration.SearchCategory;
import com.altomni.apn.job.domain.job.JobFolder;
import com.altomni.apn.job.domain.job.JobSearchFolder;
import com.altomni.apn.job.repository.job.JobFolderSearchPageRepositoryCustom;
import com.altomni.apn.job.repository.job.JobSearchFolderRepository;
import com.altomni.apn.job.service.dto.folder.JobSearchFolderDTO;
import com.altomni.apn.job.service.dto.folder.JobSearchFolderStatusDTO;
import com.altomni.apn.job.service.job.JobFolderService;
import com.altomni.apn.job.service.job.JobSearchFolderService;
import com.altomni.apn.job.service.mapper.jobFolder.JobSearchFolderMapper;
import com.altomni.apn.job.service.mapper.jobFolder.JobSearchFolderStatusDTOMapper;
import com.altomni.apn.job.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class JobSearchFolderServiceImpl implements JobSearchFolderService {

    final static String NAME_COLUMN = "f.name";
    final static String CREATED_DATE_COLUMN = "f.created_date";

    @Resource
    JobSearchFolderRepository jobSearchFolderRepository;

    @Resource
    JobSearchFolderMapper jobSearchFolderMapper;

    @Resource
    JobSearchFolderStatusDTOMapper jobSearchFolderStatusDTOMapper;

    //TODO: move jobFolderSharing function to new service from JobFolderService
    @Resource
    JobFolderService jobFolderService;
    @Resource
    QueryProcessService queryProcessService;

    @Resource
    UserService userService;

    @Resource
    JobFolderSearchPageRepositoryCustom jobFolderSearchPageRepositoryCustom;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Override
    public Page<JobSearchFolderDTO> getAllJobSearchFolders(Pageable pageable, Long userId) {
        if (userId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_SEARCH_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        SearchConditionDTO.checkPageable(pageable);

        Page<JobSearchFolder> jobSearchFolderPage = jobSearchFolderRepository.findAllByPermissionUserId(userId, pageable);

        List<JobSearchFolderDTO> jobSearchFolderDTOs = jobSearchFolderPage.stream()
                .map(jobSearchFolderMapper::toDto)
                .collect(Collectors.toList());

        return new PageImpl<>(jobSearchFolderDTOs, pageable, jobSearchFolderPage.getTotalElements());
    }

    @Override
    public JobSearchFolderDTO createJobSearchFolder(JobSearchFolderDTO jobSearchFolderDTO) {
        if (jobSearchFolderDTO == null || jobSearchFolderDTO.getName().isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_SEARCH_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        Optional<JobSearchFolder> existingJobSearchFolder = jobSearchFolderRepository.findByNameAndPermissionUserId(jobSearchFolderDTO.getName(), SecurityUtils.getUserId());
        if (existingJobSearchFolder.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_CREATEJOBSEARCHFOLDER_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (jobSearchFolderDTO.getSearchCategory() == null) {
            jobSearchFolderDTO.setSearchCategory(SearchCategory.NOCATEGORY.name());
        }
        if (jobSearchFolderDTO.getJobFolderId() != null) {
            FolderListDTO folderListDTO = jobFolderService.getCustomAndSharedJobFolderList();

            boolean folderExistsInMyFolders = folderListDTO.getMyFolderList()
                    .stream()
                    .anyMatch(folder -> folder.getId().equals(jobSearchFolderDTO.getJobFolderId()));
            boolean folderExistsInSharedFolders = folderListDTO.getSharedFolderList()
                    .stream()
                    .anyMatch(folder -> folder.getId().equals(jobSearchFolderDTO.getJobFolderId()));
            if (!(folderExistsInMyFolders || folderExistsInSharedFolders)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_CREATEJOBSEARCHFOLDER_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
            }
        }

        JobSearchFolder jobSearchFolder = jobSearchFolderMapper.toEntity(jobSearchFolderDTO);
        jobSearchFolder.setTenantId(SecurityUtils.getTenantId());
        jobSearchFolder.setActive(true);
        jobSearchFolder = jobSearchFolderRepository.save(jobSearchFolder);
        return jobSearchFolderMapper.toDto(jobSearchFolder);
    }

    public JobSearchFolderDTO updateJobSearchFolder(JobSearchFolderDTO jobSearchFolderDTO) {

        Optional<JobSearchFolder> existingJobSearchFolder = jobSearchFolderRepository.findByNameAndPermissionUserIdAndIdNot(jobSearchFolderDTO.getName(), SecurityUtils.getUserId(), jobSearchFolderDTO.getId());
        if (existingJobSearchFolder.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_UPDATEJOBSEARCHFOLDER_EXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        Optional<JobSearchFolder> jobSearchFolderOptional = jobSearchFolderRepository.findById(jobSearchFolderDTO.getId());
        if (!jobSearchFolderOptional.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_UPDATEJOBSEARCHFOLDER_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        JobSearchFolder jobSearchFolder = jobSearchFolderOptional.get();
        jobSearchFolderMapper.updateEntityFromDTO(jobSearchFolderDTO, jobSearchFolder);
        jobSearchFolder = jobSearchFolderRepository.save(jobSearchFolder);
        return jobSearchFolderMapper.toDto(jobSearchFolder);
    }


    @Override
    public void deleteJobSearchFolder(Long folderId) {
        if (folderId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_DELETEJOBSEARCHFOLDER_IDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        Optional<JobSearchFolder> jobSearchFolderOptional = jobSearchFolderRepository.findById(folderId);
        if (!jobSearchFolderOptional.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_UPDATEJOBSEARCHFOLDER_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }

        jobSearchFolderRepository.deleteById(folderId);
    }

    @Override
    public JobSearchFolderDTO getJobSearchFolder(Long folderId) {
        if (folderId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_DELETEJOBSEARCHFOLDER_IDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        Optional<JobSearchFolder> jobSearchFolderOptional = jobSearchFolderRepository.findById(folderId);
        if (!jobSearchFolderOptional.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_UPDATEJOBSEARCHFOLDER_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        return jobSearchFolderMapper.toDto(jobSearchFolderOptional.get());
    }

    @Override
    public JobSearchFolderStatusDTO getJobSearchFolderWithStatus(Long folderId) {
        if (folderId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_DELETEJOBSEARCHFOLDER_IDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        Optional<JobSearchFolder> jobSearchFolderOptional = jobSearchFolderRepository.findById(folderId);
        if (!jobSearchFolderOptional.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBSEARCHFOLDER_UPDATEJOBSEARCHFOLDER_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JobSearchFolderStatusDTO jobSearchFolderStatusDTO = jobSearchFolderStatusDTOMapper.toDto(jobSearchFolderOptional.get());
        setSearchStatus(jobSearchFolderStatusDTO);
        return jobSearchFolderStatusDTO;
    }

    private void setSearchStatus(JobSearchFolderStatusDTO jobSearchFolderStatusDTO) {
        if (jobSearchFolderStatusDTO.isActive()) {
            jobSearchFolderStatusDTO.setJobFolderStatus(SearchFolderStatus.FOLDER_ACTIVE);
        } else if (jobSearchFolderStatusDTO.getJobFolderId() != null) {
            Optional<FolderCreatorIdDTO> jobFolderDTOOptional = jobFolderService.getJobFolderCreator(jobSearchFolderStatusDTO.getJobFolderId());
            if (jobFolderDTOOptional.isPresent()) {
                jobSearchFolderStatusDTO.setJobFolderStatus(SearchFolderStatus.FOLDER_SHARE_REVOKE);
                setUserForSharedRevokedSearchFolder(jobSearchFolderStatusDTO, jobFolderDTOOptional.get().getUserId());
            } else {
                jobSearchFolderStatusDTO.setJobFolderStatus(SearchFolderStatus.FOLDER_DELETED);
            }
        }

    }

    private void setUserForSharedRevokedSearchFolder(JobSearchFolderStatusDTO jobSearchFolderStatusDTO, Long userId) {
        User user = userService.findById(userId).getBody();
        NameEmailUserDTO nameEmailUserDTO = new NameEmailUserDTO(user.getId(), CommonUtils.formatFullName(user.getFirstName(), user.getLastName()), user.getEmail());
        jobSearchFolderStatusDTO.setJobFolderCreator(nameEmailUserDTO);
    }

    @Override
    public void disableSearchFolder(Long jobFolderId) {
        List<JobSearchFolder> jobSearchFolderList = jobSearchFolderRepository.findAllByJobFolderId(jobFolderId);
        if (jobSearchFolderList.size() != 0) {
            jobSearchFolderRepository.saveAll(jobSearchFolderList.stream().map(folder -> {
                folder.setActive(false);
                return folder;
            }).collect(Collectors.toList()));
        }
    }

    @Override
    public void disableSearchFolderWithFolderParamFromTeamShared(Long jobFolderId, List<Long> teamIds) {
        List<JobSearchFolder> jobSearchFolderList = jobSearchFolderRepository.findAllByJobCustomFolderIdAndTeamIds(jobFolderId, teamIds, true);
        if (jobSearchFolderList.size() != 0) {
            jobSearchFolderRepository.saveAll(jobSearchFolderList.stream()
                    .map(folder -> {
                        folder.setActive(false);
                        return folder;
                    }).collect(Collectors.toList())
            );
        }
    }

    @Override
    public void enableSearchFolderWithFolderParamFromTeamShared(Long jobFolderId, List<Long> teamIds) {
        List<JobSearchFolder> jobSearchFolderList = jobSearchFolderRepository.findAllByJobCustomFolderIdAndTeamIds(jobFolderId, teamIds, false);
        if (jobSearchFolderList.size() != 0) {
            jobSearchFolderRepository.saveAll(jobSearchFolderList.stream()
                    .map(folder -> {
                        folder.setActive(true);
                        return folder;
                    }).collect(Collectors.toList())
            );
        }
    }


    @Override
    public void disableSearchFolderWithFolderParamFromUserShared(Long jobFolderId, List<Long> userIds) {
        List<JobSearchFolder> jobSearchFolderList = jobSearchFolderRepository.findAllByJobFolderIdAndPermissionUserIdInAndIsActive(jobFolderId, userIds, true);
        if (jobSearchFolderList.size() != 0) {
            jobSearchFolderRepository.saveAll(jobSearchFolderList.stream().
                    map(folder -> {
                        folder.setActive(false);
                        return folder;
                    }).collect(Collectors.toList())
            );
        }
    }

    @Override
    public void enableSearchFolderWithFolderParamFromUserShared(Long jobFolderId, List<Long> userIds) {
        List<JobSearchFolder> jobSearchFolderList = jobSearchFolderRepository.findAllByJobFolderIdAndPermissionUserIdInAndIsActive(jobFolderId, userIds, false);
        if (jobSearchFolderList.size() != 0) {
            jobSearchFolderRepository.saveAll(jobSearchFolderList.stream().
                    map(folder -> {
                        folder.setActive(true);
                        return folder;
                    }).collect(Collectors.toList())
            );
        }
    }

    /**
     * Search sub folder: search folder
     */


    @Override
    public Page<JobSearchFolderStatusDTO> findSearchFolders(FolderSearchRequestDTO searchRequestDTO, Pageable pageable) {
        Map<Integer, Object> paramsMap = new HashMap<>(16);
        StringBuffer countSql = new StringBuffer();
        StringBuffer dataSql = new StringBuffer();
        setSearchFolderSql(searchRequestDTO, pageable, paramsMap, countSql, dataSql);
        Long total = jobFolderSearchPageRepositoryCustom.searchCount(countSql.toString(), paramsMap);
        List<JobSearchFolder> jobSearchFolderList = jobFolderSearchPageRepositoryCustom.searchData(dataSql.toString(), JobSearchFolder.class, paramsMap);
        //List<JobSearchFolderDTO> jobSearchFolderDTOList = jobSearchFolderMapper.toSimpleDto(jobSearchFolderList);

        List<JobSearchFolderStatusDTO> jobSearchFolderStatusDTOList = jobSearchFolderStatusDTOMapper.toSimpleDto(jobSearchFolderList);
        setStatusForInactiveSearchFolder(jobSearchFolderStatusDTOList);

        return new PageImpl<>(jobSearchFolderStatusDTOList, pageable, total);
    }

    private void setSearchFolderSql(FolderSearchRequestDTO searchRequestDTO, Pageable pageable, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        countSql.append("SELECT COUNT(f.id) FROM job_search_folder f WHERE f.puser_id = ").append(SecurityUtils.getUserId());
        dataSql.append("SELECT * FROM job_search_folder f WHERE f.puser_id = ").append(SecurityUtils.getUserId());

        setSearchFolderSearchParam(searchRequestDTO, paramsMap, countSql, dataSql);
        queryProcessService.setQueryPaginationAndSort(pageable, dataSql);
    }

    private void setSearchFolderSearchParam(FolderSearchRequestDTO searchRequestDTO, Map<Integer, Object> paramsMap, StringBuffer countSql, StringBuffer dataSql) {
        if (StringUtils.isNotBlank(searchRequestDTO.getName())) {
            queryProcessService.setColumnLikeParamFilter(paramsMap, countSql, dataSql, NAME_COLUMN, searchRequestDTO.getName());
        }

        if (searchRequestDTO.getCreatedDateFrom() != null || searchRequestDTO.getCreatedDateTo() != null) {
            queryProcessService.setDateBetweenParamFilter(paramsMap, countSql, dataSql, CREATED_DATE_COLUMN, searchRequestDTO.getCreatedDateFrom(), searchRequestDTO.getCreatedDateTo());
        }

        if(searchRequestDTO.getGeneralText() != null && !searchRequestDTO.getGeneralText().isEmpty()){
            queryProcessService.setSingleGeneralSearchParamFilter(paramsMap, countSql, dataSql, NAME_COLUMN, searchRequestDTO.getGeneralText().get(0));
        }

    }


    private void setStatusForInactiveSearchFolder(List<JobSearchFolderStatusDTO> jobSearchFolderStatusDTOList) {
        List<Long> jobIds = jobSearchFolderStatusDTOList.stream()
                .filter(dto -> !dto.isActive())
                .map(JobSearchFolderStatusDTO::getJobFolderId)
                .collect(Collectors.toList());

        if (jobIds.isEmpty()) {
            jobSearchFolderStatusDTOList.forEach(dto -> dto.setJobFolderStatus(SearchFolderStatus.FOLDER_ACTIVE));
            return;
        }

        Map<Long, Long> folderUserMap = jobFolderService.getJobFolders(jobIds).stream()
                .collect(Collectors.toMap(JobFolder::getId, JobFolder::getPermissionUserId));

        List<UserBriefDTO> userBriefs = userService.getAllBriefUsersByIds(new ArrayList<>(folderUserMap.values())).getBody();
        Map<Long, UserBriefDTO> idUserMap = userBriefs.stream()
                .collect(Collectors.toMap(UserBriefDTO::getId, Function.identity()));

        jobSearchFolderStatusDTOList.forEach(dto -> {
            if (dto.isActive()) {
                dto.setJobFolderStatus(SearchFolderStatus.FOLDER_ACTIVE);
            } else if (dto.getJobFolderId() != null) {
                if (!folderUserMap.containsKey(dto.getJobFolderId())) {
                    dto.setJobFolderStatus(SearchFolderStatus.FOLDER_DELETED);
                } else {
                    dto.setJobFolderStatus(SearchFolderStatus.FOLDER_SHARE_REVOKE);
                    UserBriefDTO userBriefDTO = idUserMap.get(folderUserMap.get(dto.getJobFolderId()));
                    dto.setJobFolderCreator(new NameEmailUserDTO(userBriefDTO.getId(),
                            CommonUtils.formatFullName(userBriefDTO.getFirstName(), userBriefDTO.getLastName()),
                            userBriefDTO.getEmail()));
                }
            } else {
                //special case, the search folder is set to inactive, but it is not assicated with any job_folder.
                dto.setJobFolderStatus(SearchFolderStatus.UNKNOWN_INACTIVE);
            }
        });


    }


}
