package com.altomni.apn.job.domain.project;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "JobProjectSharing is used for sharing project to users who have permission of jobs belong to current project")
@Data
@AllArgsConstructor
@Entity
@Table(name = "job_project_sharing")
public class JobProjectSharing extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Job project id")
    @Column(name = "job_project_id", nullable = false)
    private Long jobProjectId;

    @ApiModelProperty(value = "The user has permission of jobs belong to current project")
    @Column(name = "user_id", nullable = false)
    private Long userId;
}
