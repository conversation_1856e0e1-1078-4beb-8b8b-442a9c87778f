package com.altomni.apn.job.service.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.ArrayList;
import java.util.List;

/**
 * The JobType enumeration.
 */
public enum JobSharingPlatformType implements ConvertedEnum<Integer> {

    CONTRACT(1,"General Staffing (Contract)", "Contract", "合同制职位"),

    FULL_TIME(3,"General Recruiting (FTE)", "Full Time", "全职职位"),
    PART_TIME(4,"PART_TIME", "Part Time","兼职职位"),

    INTERNSHIP(7,"Intern", "Intern","实习生职位"),
    OTHERS(88, "OTHERS", "Others", "其他职位");

    private final int dbValue;
    private final String name;

    private final String enDisplay;

    private final String cnDisplay;

    public static final List<Integer> ALL_JOB_TYPES = new ArrayList<>();

    static {
        for (JobSharingPlatformType jobType: JobSharingPlatformType.values()) {
            ALL_JOB_TYPES.add(jobType.toDbValue());
        }
    }

    JobSharingPlatformType(int dbValue, String name, String enDisplay, String cnDisplay) {
        this.dbValue = dbValue;
        this.name = name;
        this.enDisplay = enDisplay;
        this.cnDisplay = cnDisplay;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }



    // static resolving:
    public static final ReverseEnumResolver<JobSharingPlatformType, Integer> resolver =
        new ReverseEnumResolver<>(JobSharingPlatformType.class, JobSharingPlatformType::toDbValue);

    public static JobSharingPlatformType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }

    public int getDbValue() {
        return dbValue;
    }

    public String getName() {
        return name;
    }
}
