package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.job.domain.job.SearchPreference;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class SearchPreferenceDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @ApiModelProperty(value = "Job list search conditions's type")
    @NotNull
    private String searchType;

    @ApiModelProperty(value = "Job list search conditions's pickName")
    @NotNull
    private String searchName;

    @ApiModelProperty(value = "Job list search conditions .")
    @NotNull
    private String searchGroup;

    @ApiModelProperty(value = "Value for search condition, only user in web .")
    @NotNull
    private String searchContent;

    @ApiModelProperty(value = "which module this search condition belongs to .")
    private ModuleType module;


    public static SearchPreferenceDTO fromSearchPreference(SearchPreference searchPreference) {
        SearchPreferenceDTO dto = new SearchPreferenceDTO();
        ServiceUtils.myCopyProperties(searchPreference, dto);
        return dto;
    }
}
