package com.altomni.apn.job.domain.aisourcing;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "job_ai_sourcing_talent_rejection")
public class JobAiSouringTalentRejection extends AbstractPermissionAuditingEntity implements Serializable, Cloneable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Refer to Job Ai Sourcing Talent ID")
    @Column(name = "job_ai_sourcing_talent_id")
    private Long jobAiSouringTalentId;

    @ApiModelProperty(value = "Refer to Enum Job AI Sourcing Talent Rejection Tag ID")
    @Column(name = "tag_id")
    private Integer tagId;

    @ApiModelProperty(value = "The reason to reject ai sourcing talent")
    @Column(name = "reason")
    private String reason;
}
