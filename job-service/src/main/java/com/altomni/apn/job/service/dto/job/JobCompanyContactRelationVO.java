package com.altomni.apn.job.service.dto.job;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class JobCompanyContactRelationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "user id .")
    private Long hmId;

    @ApiModelProperty(value = "user id .")
    private Long hrId;

    @ApiModelProperty(value = "user name.")
    private String name;

    @ApiModelProperty(value = "job id")
    private Long jobId;

    public JobCompanyContactRelationVO() {
    }


    public JobCompanyContactRelationVO(Long hmId, String name) {
        this.hmId = hmId;
        this.name = name;
    }

    public JobCompanyContactRelationVO(Long userId, Long hr) {
        this.hmId = userId;
        this.hrId = hr;
    }
}
