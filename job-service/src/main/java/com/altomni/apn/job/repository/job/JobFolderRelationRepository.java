package com.altomni.apn.job.repository.job;


import com.altomni.apn.job.domain.job.JobFolderRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface JobFolderRelationRepository extends JpaRepository<JobFolderRelation, Long> {

    //for batch delete
    @Modifying
    @Query("DELETE FROM JobFolderRelation jfr WHERE jfr.jobId IN :jobIds AND jfr.jobFolderId = :folderId")
    void deleteAllByJobIdInAndJobFolderId(@Param("jobIds") List<Long> jobIds, @Param("folderId") Long folderId);

    List<JobFolderRelation> getAllByJobFolderId(Long jobFolderId);

    List<JobFolderRelation> getAllByJobFolderIdIn(List<Long> jobFolderId);

    @Query("SELECT jfr.jobFolderId FROM JobFolderRelation jfr WHERE jfr.jobId =:jobIds")
    Set<Long> findFolderIdsByJobId(@Param("jobIds") Long jobId);
}
