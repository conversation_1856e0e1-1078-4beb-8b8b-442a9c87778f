package com.altomni.apn.job.service.annotation;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class StringEnumValidatorImpl implements ConstraintValidator<StringEnumValidator, String> {

    private Class<? extends Enum<?>> enumClass;

    @Override
    public void initialize(StringEnumValidator constraintAnnotation) {
        enumClass = constraintAnnotation.enumClass();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if(value == null) {
            return true;
        }

        for(Enum<?> enumValue: enumClass.getEnumConstants()) {
            if(value.equals(enumValue.name())) {
                return true;
            }
        }

        return false;
    }

}
