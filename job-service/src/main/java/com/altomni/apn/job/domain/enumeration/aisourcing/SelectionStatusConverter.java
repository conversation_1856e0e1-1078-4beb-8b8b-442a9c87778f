package com.altomni.apn.job.domain.enumeration.aisourcing;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

/**
 * <AUTHOR>
 */
@Convert
public class SelectionStatusConverter extends AbstractAttributeConverter<SelectionStatus, Integer> {
    public SelectionStatusConverter() {
        super(SelectionStatus::toDbValue, SelectionStatus::fromDbValue);
    }
}
