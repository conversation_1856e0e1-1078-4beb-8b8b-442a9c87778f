package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;

import java.time.Instant;

public interface IDormantJobDTO {

    Long getId();

    String getJobTitle();

    JobType getJobType();

    JobStatus getJobStatus();

    Long getCompanyId();

    String getCompany();

    Instant getPostingTime();

    Long getAmId();

    String getAm();

    Instant getLastModifiedDate();
}
