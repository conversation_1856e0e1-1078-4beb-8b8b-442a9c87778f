//TODO: remove after full test
//package com.altomni.apn.job.domain.enumeration;
//
//import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;
//import com.fasterxml.jackson.annotation.JsonValue;
//
//public enum FolderPermission{
//    READONLY(0),
//    READWRITE(1);
//
//    private final int dbValue;
//
//    FolderPermission(int dbValue) {
//        this.dbValue = dbValue;
//    }
//
//    public Integer toDbValue() {
//        return dbValue;
//    }
//
//
//    public static final ReverseEnumResolver<FolderPermission, Integer> resolver =
//            new ReverseEnumResolver<>(FolderPermission.class, FolderPermission::toDbValue);
//    public static FolderPermission fromDbValue(Integer dbValue) {
//        return resolver.get(dbValue);
//    }
//
//    @JsonValue
//    public Integer getValue() {
//        return dbValue;
//    }
//
//}
