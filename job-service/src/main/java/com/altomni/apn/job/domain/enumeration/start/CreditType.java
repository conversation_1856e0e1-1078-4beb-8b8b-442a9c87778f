package com.altomni.apn.job.domain.enumeration.start;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The credit type enumeration.
 * <AUTHOR>
 */
public enum CreditType implements ConvertedEnum<Integer> {
    MONTHLY(0),
    BULK(1);

    private final int dbValue;

    CreditType(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<CreditType, Integer> resolver = new ReverseEnumResolver<>(CreditType.class, CreditType::toDbValue);

    public static CreditType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
