package com.altomni.apn.job.service.dto.job;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobESPageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Integer size;

    private Integer from;

    @SerializedName("_source")
    private List<String> source;

    private List<JSONObject> sort;

//    public List<JSONObject> toSort(List<SearchSortDTO> in) {
//        List<JSONObject> jsonObjects = new ArrayList<>();
//        //set sort condition
//        if (ObjectUtil.isNotEmpty(in)) {
//            for (SearchSortDTO jobSortDTO : in) {
//                JSONObject sortName = new JSONObject();
//                JSONObject sort = new JSONObject();
//                sort.put("order", jobSortDTO.getDirection());
//                sort.put("mode", "min");
//                sortName.put(jobSortDTO.getProperty(), sort);
//                jsonObjects.add(sortName);
//            }
//        }else {
//            //set default sort
//            JSONObject defaultSortName = new JSONObject();
//            JSONObject defaultSort = new JSONObject();
//            defaultSort.put("order", "DESC");
//            defaultSort.put("mode", "min");
//            defaultSortName.put("postingTime", defaultSort);
//            jsonObjects.add(defaultSortName);
//        }
//        return jsonObjects;
//    }

}
