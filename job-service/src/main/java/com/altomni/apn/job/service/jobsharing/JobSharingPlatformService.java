package com.altomni.apn.job.service.jobsharing;

import com.altomni.apn.common.domain.enumeration.job.PlatformType;
import com.altomni.apn.common.dto.wechat.WechatJSAPIConfig;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageType;
import com.altomni.apn.job.domain.job.JobPosterImage;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPlatformDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPosterDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPosterImageDTO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPlatformVO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPosterImageVO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPosterVO;
import liquibase.pro.packaged.V;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

public interface JobSharingPlatformService {
    JobSharingPlatformDTO createJobSharing(JobSharingPlatformDTO jobSharingPlatformDTO);

    JobSharingPlatformDTO getJobSharingByJobIdAndPlatform(Long jobId, PlatformType platformType);

    JobSharingPlatformVO getLatestJobSharing(Long jobId);

    List<JobSharingPlatformDTO> getAllJobSharingByJobId(Long jobId);

    String getJobSharingHTMLURLFromS3ByUUID(String uuid);

    WechatJSAPIConfig getWechatJsApiConfig(String url);

    JobSharingPlatformDTO updateJobSharing(JobSharingPlatformDTO jobSharingPlatformDTO);

    Page<JobPosterImage> searchJobPosterImage(PosterImageType type, Pageable pageable);

    List<JobSharingPosterImageVO> bulkSaveJobPosterImage(List<JobSharingPosterImageDTO> jobSharingPosterImageDTOList) throws IOException;

    List<JobSharingPosterVO> bulkSaveJobPoster(JobSharingPosterDTO jobSharingPosterDTO);

    JobSharingPosterVO queryLatestJobPosterImage(Long jobId);

    void saveSystemJobPosterImageZipFile(MultipartFile zipFile, SecurityContext context) throws IOException;

}
