package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.enumeration.folder.RelateJobFolderType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RelateJobFolderInfo {
    private String id;
    private Long jobId;
    private String title;
    private int numberOfNotes;
    private String companyName;
    private String status;
    private Integer priority;
    private List<SimpleUserInfoDTO> owner;
    private String createdDate;
    private List<SimpleUserInfoDTO> share;
    private RelateJobFolderType type;
}
