package com.altomni.apn.job.service.jobsharing.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.job.PlatformType;
import com.altomni.apn.common.domain.folder.JobSharingPlatformTalentRelatedJobFolderRelation;
import com.altomni.apn.common.domain.job.JobSharingPlatform;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.domain.talent.TalentAssociationJobFolder;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderRelationDTO;
import com.altomni.apn.common.dto.store.CloudFileObjectMetadata;
import com.altomni.apn.common.dto.wechat.WechatJSAPIConfig;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.enums.UploadTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.repository.talent.JobSharingPlatformRelatedFolderRepository;
import com.altomni.apn.common.repository.talent.TalentRelateJobFolderRepository;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageStatus;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageType;
import com.altomni.apn.job.domain.job.JobPoster;
import com.altomni.apn.job.domain.job.JobPosterImage;
import com.altomni.apn.job.repository.job.JobPosterImageRepository;
import com.altomni.apn.job.repository.job.JobPosterRepository;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.repository.job.JobSharingPlatformRepository;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingContentDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPlatformDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPosterDTO;
import com.altomni.apn.job.service.dto.jobsharing.JobSharingPosterImageDTO;
import com.altomni.apn.job.service.jobsharing.JobSharingPlatformService;
import com.altomni.apn.common.service.wechat.WeChatUtils;
import com.altomni.apn.job.service.mapper.jobsharing.JobSharingContentDTOMapper;
import com.altomni.apn.job.service.mapper.jobsharing.JobSharingPlatformMapper;
import com.altomni.apn.job.service.store.StoreService;
import com.altomni.apn.job.config.env.WechatJobSharingProperties;
import com.altomni.apn.job.service.talent.TalentService;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPlatformVO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPosterImageVO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingPosterVO;
import com.altomni.apn.job.service.vo.jobsharing.JobSharingTalentAssociatedJobFolderVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.MimeTypeUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import javax.transaction.Transactional;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Instant;
import java.util.*;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Slf4j
@Service
public class JobSharingPlatformServiceImpl implements JobSharingPlatformService {
    //final static String S3URL_PREFIX = "https://apn-doc-staging.s3.us-west-1.amazonaws.com/shared-job-pages/";

    final static String CONTENT_TYPE = "text/html; charset=UTF-8";

    //placeHolder from html, replace meta url with s3 link·
    final static String META_URL_PLACEHOLDER = "{{metaUrl}}";
    final static String META_WECHAT_APPID_PLACEHOLDER = "{{metaWechatAppId}}";

    final static String META_SIGNATURE_PLACEHOLDER = "{{metaSignature}}";

    final static String META_NONCESTR_PLACEHOLDER = "{{metaNonceStr}}";

    final static String META_TIMESTAMP_PLACEHOLDER = "{{metaTimestamp}}";

    @Resource
    private JobSharingPlatformRepository jobSharingPlatformRepository;

    @Resource
    private JobSharingPlatformRelatedFolderRepository jobSharingPlatformRelatedFolderRepository;

    @Resource
    private JobRepository jobRepository;

    @Resource
    private TalentRelateJobFolderRepository talentRelateJobFolderRepository;

    @Resource
    private JobSharingPlatformMapper jobSharingPlatformMapper;

    @Resource
    private JobSharingContentDTOMapper jobSharingContentDTOMapper;

    @Resource
    private StoreService storeService;

    @Resource
    private TalentService talentService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Resource
    private WeChatUtils weChatUtils;

    @Resource
    private WechatJobSharingProperties wechatJobSharingProperties;

    @Resource
    private JobPosterImageRepository jobPosterImageRepository;

    @Resource
    private JobPosterRepository jobPosterRepository;

    @Override
    @Transactional
    public JobSharingPlatformDTO createJobSharing(JobSharingPlatformDTO jobSharingPlatformDTO) {
        validateInput(jobSharingPlatformDTO);

        processTalentRelateJobFolder(jobSharingPlatformDTO);
        
        String apnLinks = uploadHTMLToS3(jobSharingPlatformDTO);
        String uuid = getS3LinkKeyFromSharingLinks(apnLinks);
        jobSharingPlatformDTO.setUuid(uuid);

        JobSharingPlatformDTO newjobSharingPlatformDTO = saveJobSharing(jobSharingPlatformDTO);
        newjobSharingPlatformDTO.setApnLinks(apnLinks);

        return newjobSharingPlatformDTO;
    }

    /**
     * create relate folder if the relate folder needed
     * @param jobSharingPlatformDTO
     */
    private void processTalentRelateJobFolder(JobSharingPlatformDTO jobSharingPlatformDTO) {
        if(Boolean.TRUE.equals(jobSharingPlatformDTO.getAllowTalentSubmission())){
            if(jobSharingPlatformDTO.getTalentAssociatedJobFolderFolderId() == null){
                throw new CustomParameterizedException("Cannot Set Related Folder without Folder Info");
            }
            TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO = new TalentRelateJobFolderRelationDTO();
            talentRelateJobFolderRelationDTO.setFolderId(jobSharingPlatformDTO.getTalentAssociatedJobFolderFolderId());
            talentRelateJobFolderRelationDTO.setSharedLinkExpireTime(jobSharingPlatformDTO.getSharedLinkExpireTime());
            talentService.createOrUpdateRelationOnRelatedJobFolderAndJobSharing(talentRelateJobFolderRelationDTO);

        }
        else if(Boolean.FALSE.equals(jobSharingPlatformDTO.getAllowTalentSubmission())){
            if(StringUtils.isBlank(jobSharingPlatformDTO.getTalentAssociatedJobFolderFolderId())){
                throw new CustomParameterizedException("Cannot remove function of add talent into related job Folder without Folder Info");
            }
            jobSharingPlatformRelatedFolderRepository.deleteByTalentAssociatedJobFolderFolderId(jobSharingPlatformDTO.getTalentAssociatedJobFolderFolderId());
        }
    }


    @Override
    public WechatJSAPIConfig getWechatJsApiConfig(String uuid) {
        String url = wechatJobSharingProperties.getJobSharingURL() + uuid;
        WechatJSAPIConfig wechatJSAPIConfig = weChatUtils.getWechatSDKConfig(url);
        return wechatJSAPIConfig;
    }

    @Override
    public JobSharingPlatformDTO updateJobSharing(JobSharingPlatformDTO jobSharingPlatformDTO) {
        return null;
    }

    @Override
    public JobSharingPlatformDTO getJobSharingByJobIdAndPlatform(Long jobId, PlatformType platformType) {
        List<JobSharingPlatform> jobSharingPlatform = jobSharingPlatformRepository.findAllByJobIdAndUserIdAndPlatformType(jobId, SecurityUtils.getUserId(), platformType);
        if (jobSharingPlatform.isEmpty() || jobSharingPlatform.size() > 1) {
            new CustomParameterizedException("Cannot get Shared Job Content with current platform!");
        }
        JobSharingPlatformDTO jobSharingPlatformDTO = formatJobSharingPlatformDTO(jobSharingPlatform.get(0));
        return jobSharingPlatformDTO;
    }

    @Override
    public JobSharingPlatformVO getLatestJobSharing(Long jobId) {
        JobSharingPlatformVO jobSharingPlatformVO = new JobSharingPlatformVO();
        jobSharingPlatformVO.setJobId(jobId);

        JobSharingPlatform jobSharingPlatform = jobSharingPlatformRepository.findTopByJobIdAndUserIdOrderByCreatedDateDesc(jobId, SecurityUtils.getUserId());
        JobPoster shareByPicture = jobPosterRepository.findFirstByJobIdAndPermissionUserIdOrderByCreatedDateDesc(jobId, SecurityUtils.getUserId());
        // 链接和图片都没分享过，返回空信息
        if (jobSharingPlatform == null && shareByPicture == null) {
            setTalentRelatedJobFolderInfo(jobSharingPlatformVO);
            return jobSharingPlatformVO;
        }

        // 只分享过链接，返回链接的信息
        if (jobSharingPlatform != null && shareByPicture == null) {
            return lastShareByLink(jobSharingPlatform);
        }
        // 只分享过图片
        if (shareByPicture != null && jobSharingPlatform == null) {
            return lastShareByPicture(jobSharingPlatformVO, shareByPicture);
        }
        // 链接和图片都分享过，返回最后一次分享的信息
        if (jobSharingPlatform.getCreatedDate().isAfter(shareByPicture.getCreatedDate())) {
            return lastShareByLink(jobSharingPlatform);
        } else {
            return lastShareByPicture(jobSharingPlatformVO, shareByPicture);
        }

    }

    private JobSharingPlatformVO lastShareByLink(JobSharingPlatform jobSharingPlatform) {
        JobSharingPlatformDTO jobSharingPlatformDTO = formatJobSharingPlatformDTO(jobSharingPlatform);
        JobSharingPlatformVO jobSharingPlatformVO = jobSharingPlatformMapper.convertDTOToVO(jobSharingPlatformDTO);
        setTalentRelatedJobFolderInfo(jobSharingPlatformVO);
        return jobSharingPlatformVO;
    }

    private JobSharingPlatformVO lastShareByPicture(JobSharingPlatformVO jobSharingPlatformVO, JobPoster shareByPicture) {
        JSONObject jsonObject = JSONUtil.parseObj(shareByPicture.getContent());
        JobSharingContentDTO jobSharingContentDTO = JSONUtil.toBean(jsonObject, JobSharingContentDTO.class);
        jobSharingContentDTO.setJobResponsibilities(jsonObject.getStr("jobDescription"));
        if (ObjectUtil.isNotNull(jobSharingContentDTO)) {
            JobSharingPlatformDTO jobSharingPlatformDTO = jobSharingPlatformMapper.toCombine(null, jobSharingContentDTO);
            jobSharingPlatformDTO.setJobId(shareByPicture.getJobId());
            jobSharingPlatformDTO.setPlatformType(shareByPicture.getPlatform());
            jobSharingPlatformDTO.setId(shareByPicture.getId());
            jobSharingPlatformVO = jobSharingPlatformMapper.convertDTOToVO(jobSharingPlatformDTO);
        }
        setTalentRelatedJobFolderInfo(jobSharingPlatformVO);
        return jobSharingPlatformVO;
    }



    @Override
    public List<JobSharingPlatformDTO> getAllJobSharingByJobId(Long jobId) {
        List<JobSharingPlatform> jobSharingPlatform = jobSharingPlatformRepository.findAllByJobIdAndUserId(jobId, SecurityUtils.getUserId());
        List<JobSharingPlatformDTO> jobSharingPlatformDTOList = jobSharingPlatform.stream().map(entity -> formatJobSharingPlatformDTO(entity)).collect(Collectors.toList());
        return jobSharingPlatformDTOList;

    }

    @Override
    public String getJobSharingHTMLURLFromS3ByUUID(String uuid) {
        CloudFileObjectMetadata jdMetaData = storeService.getFileDetailWithoutFileFromS3(uuid, UploadTypeEnum.JOB_SHARING_CONTENT.getKey()).getBody();
        return jdMetaData.getS3Link();

    }

    @Override
    public Page<JobPosterImage> searchJobPosterImage(PosterImageType type, Pageable pageable) {
        return jobPosterImageRepository.findAll(queryAllTalentTrackingLinkedinPending(SecurityUtils.getUserId(), type, PosterImageStatus.ACTIVE), pageable);
    }

    @Override
    @Transactional
    public List<JobSharingPosterImageVO> bulkSaveJobPosterImage(List<JobSharingPosterImageDTO> jobSharingPosterImageDTOList) throws IOException {
        Set<String> linkUrlSet = jobSharingPosterImageDTOList.stream().map(JobSharingPosterImageDTO::getLinkUrl).collect(Collectors.toSet());
        // 检查是否存在相同的图片
        if (linkUrlSet.size() != jobSharingPosterImageDTOList.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_POST_IMAGE_DUPLICATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        List<JobSharingPosterImageVO> result = new ArrayList<>();
        List<JobSharingPosterImageDTO> addItems = jobSharingPosterImageDTOList.stream().filter(o -> ObjectUtil.isEmpty(o.getId())).toList();
        List<JobSharingPosterImageDTO> updateItems = jobSharingPosterImageDTOList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getId())).toList();
        Set<Long> updateItemIds = updateItems.stream().map(JobSharingPosterImageDTO::getId).collect(Collectors.toSet());
        Map<Long, JobSharingPosterImageDTO> updateItemMap = updateItems.stream().collect(Collectors.toMap(JobSharingPosterImageDTO::getId, o -> o));
        List<JobPosterImage> addJobPosterImageList = addItems.stream().map(JobPosterImage::fromJobSharingPosterImageDTO).toList();
        List<JobPosterImage> existedJobPosterImageList = jobPosterImageRepository.findAll(queryAllTalentTrackingLinkedinPending(SecurityUtils.getUserId(), PosterImageType.CUSTOM, PosterImageStatus.ACTIVE));
        List<JobPosterImage> deleteJobPosterImageList = existedJobPosterImageList.stream().filter(o -> !updateItemIds.contains(o.getId())).toList();
        existedJobPosterImageList.removeAll(deleteJobPosterImageList);
        existedJobPosterImageList.forEach(item -> {
            if (updateItemMap.containsKey(item.getId())) {
                JobSharingPosterImageDTO jobSharingPosterImageDTO = updateItemMap.get(item.getId());
                ServiceUtils.myCopyProperties(jobSharingPosterImageDTO, item);
            }
        });
        for (JobPosterImage jobPosterImage : addJobPosterImageList) {
            String thumbnailLinkUrl = resizeImageThumbnail(jobPosterImage.getLinkUrl());
            jobPosterImage.setThumbnailLinkUrl(thumbnailLinkUrl);
        }
        addJobPosterImageList = jobPosterImageRepository.saveAll(addJobPosterImageList);
        existedJobPosterImageList = jobPosterImageRepository.saveAll(existedJobPosterImageList);
        jobPosterImageRepository.deleteAllInBatch(deleteJobPosterImageList);


        result.addAll(addJobPosterImageList.stream().map(JobPosterImage::toJobSharingPosterImageVO).collect(Collectors.toList()));
        result.addAll(existedJobPosterImageList.stream().map(JobPosterImage::toJobSharingPosterImageVO).collect(Collectors.toList()));
        return result;
    }

    @Override
    @org.springframework.transaction.annotation.Transactional(rollbackFor = Exception.class)
    public List<JobSharingPosterVO> bulkSaveJobPoster(JobSharingPosterDTO jobSharingPosterDTO) {
        List<JobPoster> jobPosterList = new ArrayList<>();
        jobSharingPosterDTO.getImages().forEach(item -> {
            JobPoster jobPoster = JobPoster.fromJobSharingPosterDTO(jobSharingPosterDTO);
            ServiceUtils.myCopyProperties(item, jobPoster);
            jobPosterList.add(jobPoster);
        });
        List<JobPoster> result = jobPosterRepository.saveAll(jobPosterList);
        return result.stream().map(JobPoster::toJobSharingPosterVO).toList();
    }

    @Override
    public JobSharingPosterVO queryLatestJobPosterImage(Long jobId) {
        JobPoster jobPoster = jobPosterRepository.findFirstByJobIdAndPermissionUserIdOrderByCreatedDateDesc(jobId, SecurityUtils.getUserId());
        JobSharingPlatform shareByLink = jobSharingPlatformRepository.findTopByJobIdAndUserIdOrderByCreatedDateDesc(jobId, SecurityUtils.getUserId());
        // 没有分享过链接和图片
        if (Objects.isNull(jobPoster) && Objects.isNull(shareByLink)) {
            return new JobSharingPosterVO();
        }
        // 只分享过图片
        if (Objects.nonNull(jobPoster) && Objects.isNull(shareByLink)) {
            return JobPoster.toJobSharingPosterVO(jobPoster);
        }
        // 只分享过链接
        if (Objects.isNull(jobPoster) && Objects.nonNull(shareByLink)) {
            return convertLastShareByLink(shareByLink);
        }
        if (jobPoster.getCreatedDate().isAfter(shareByLink.getCreatedDate())) {
            return JobPoster.toJobSharingPosterVO(jobPoster);
        } else {
            return convertLastShareByLink(shareByLink);
        }
    }

    private JobSharingPosterVO convertLastShareByLink(JobSharingPlatform shareByLink) {
        JobSharingPosterVO vo = new JobSharingPosterVO();
        vo.setId(shareByLink.getId());
        vo.setJobId(shareByLink.getJobId());
        vo.setPlatform(shareByLink.getPlatformType());
        JSONObject jsonObject = JSONUtil.parseObj(shareByLink.getJobSharingContent());
        String jobDescription = jsonObject.getStr("jobResponsibilities");
        jsonObject.remove("jobResponsibilities");
        jsonObject.put("jobDescription", jobDescription);
        vo.setContent(jsonObject.toString());
        return vo;
    }

    @Async
    @Override
    public void saveSystemJobPosterImageZipFile(MultipartFile zipFile, SecurityContext context) throws IOException {
        log.info("saveSystemJobPosterImageZipFile start: {}", Instant.now());
        SecurityContextHolder.setContext(context);
        List<JobPosterImage> systemJobPosterImageList = new ArrayList<>();
        try (InputStream zipInputStream = zipFile.getInputStream();
             ZipInputStream zipStream = new ZipInputStream(zipInputStream)) {

            ZipEntry entry;
            while ((entry = zipStream.getNextEntry()) != null) {
                log.info("saveSystemJobPosterImageZipFile fileName: {}", entry.getName());
                if (entry.getName().endsWith(".jpg") || entry.getName().endsWith(".jpeg")) {
                    // Extract the JPG file and upload to S3
                    byte[] imageBytes = IOUtils.toByteArray(zipStream);
                    String s3key = DigestUtil.md5Hex(imageBytes);
                    String s3Link = storeService.uploadContentBytesWithKey(UploadTypeEnum.JOB_SHARING_POSTER_IMAGE, MimeTypeUtils.IMAGE_JPEG_VALUE, s3key, imageBytes).getBody();
                    String thumbnailLinkUrl = resizeImageThumbnail(s3Link);
                    JobPosterImage jobPosterImage = new JobPosterImage();
                    jobPosterImage.setFileName(entry.getName());
                    jobPosterImage.setLinkUrl(s3Link);
                    jobPosterImage.setThumbnailLinkUrl(thumbnailLinkUrl);
                    jobPosterImage.setType(PosterImageType.SYSTEM);
                    jobPosterImage.setStatus(PosterImageStatus.ACTIVE);
                    systemJobPosterImageList.add(jobPosterImage);
                }
                zipStream.closeEntry();
            }
        }
        jobPosterImageRepository.saveAll(systemJobPosterImageList);
        log.info("saveSystemJobPosterImageZipFile end: {}", Instant.now());
    }

    private Specification<JobPosterImage> queryAllTalentTrackingLinkedinPending(Long creatorId, PosterImageType type, PosterImageStatus status) {
        Specification<JobPosterImage> specification = new Specification<JobPosterImage>() {
            @Override
            public Predicate toPredicate(Root<JobPosterImage> root, CriteriaQuery<?> criteriaQuery, CriteriaBuilder criteriaBuilder) {
                Predicate predicate = criteriaBuilder.conjunction();
                if (ObjectUtil.isNotEmpty(type)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("type"), type.toDbValue()));
                    //仅能查自己上传的图片或者系统图片
                    if (PosterImageType.CUSTOM.equals(type)) {
                        predicate.getExpressions().add(criteriaBuilder.equal(root.get("permissionUserId"), creatorId));
                    }
                } else {
                    //仅能查自己上传的图片和系统图片
                    Predicate customImagePredicate = criteriaBuilder.and(
                            criteriaBuilder.equal(root.get("type"), PosterImageType.CUSTOM.toDbValue()),
                            criteriaBuilder.equal(root.get("permissionUserId"), creatorId)
                    );
                    Predicate systemImagePredicate = criteriaBuilder.equal(root.get("type"), PosterImageType.SYSTEM.toDbValue());
                    predicate.getExpressions().add(criteriaBuilder.or(customImagePredicate, systemImagePredicate));
                }
                if (ObjectUtil.isNotEmpty(status)) {
                    predicate.getExpressions().add(criteriaBuilder.equal(root.get("status"), status.toDbValue()));
                }
                return predicate;
            }
        };
        return specification;
    }

    /**
     * set info for the job that associated with related folder and has been set the expired time
     * @param jobSharingPlatformVO
     */
    private void setTalentRelatedJobFolderInfo(JobSharingPlatformVO jobSharingPlatformVO) {
        Long jobId = jobSharingPlatformVO.getJobId();
        Long userId = SecurityUtils.getUserId();

        configureURLLinkToFolder(jobSharingPlatformVO, jobId, userId);
        setTalentAssoicatedJobFolderDetails(jobSharingPlatformVO, jobId, userId);
    }

    private void configureURLLinkToFolder(JobSharingPlatformVO jobSharingPlatformVO, Long jobId, Long userId) {
        Optional<JobSharingPlatformTalentRelatedJobFolderRelation> relatedFolderRelationOpt = jobSharingPlatformRelatedFolderRepository.findByJobIdAndUserId(jobId, userId);
        if (relatedFolderRelationOpt.isPresent()) {
            JobSharingPlatformTalentRelatedJobFolderRelation relatedFolderRelation = relatedFolderRelationOpt.get();
            jobSharingPlatformVO.setSharedLinkExpireTime(relatedFolderRelation.getSharedLinkExpireTime());
            jobSharingPlatformVO.setTalentAssociatedJobFolderFolderId(relatedFolderRelation.getTalentAssociatedJobFolderFolderId());
            jobSharingPlatformVO.setURLLinkToFolder(true);
        } else {
            jobSharingPlatformVO.setURLLinkToFolder(false);
        }
    }

    private void setTalentAssoicatedJobFolderDetails(JobSharingPlatformVO jobSharingPlatformVO, Long jobId, Long userId) {
        List<TalentAssociationJobFolder> talentAssociationJobFolderList = talentRelateJobFolderRepository.findAllByUserIdAndJobId(userId, jobId);
        if (!talentAssociationJobFolderList.isEmpty()) {
            Set<String> folderIds = talentAssociationJobFolderList.stream().map(TalentAssociationJobFolder::getFolderId).collect(Collectors.toSet());
            List<JobV3> jobList = jobRepository.findJobByTalentRelatedJobFolderFolderIdIn(folderIds);
            Map<Long, JobV3> jobMap = jobList.isEmpty() ? new HashMap<>() : jobList.stream().collect(Collectors.toMap(JobV3::getId, Function.identity(), (j1,j2) -> j1));

            List<JobSharingTalentAssociatedJobFolderVO> folderVOList = talentAssociationJobFolderList.stream().map(folder -> {
                JobSharingTalentAssociatedJobFolderVO folderVO = new JobSharingTalentAssociatedJobFolderVO();
                folderVO.setFolderId(folder.getFolderId());
                folderVO.setJobId(folder.getJobId());
                if (jobMap.containsKey(folder.getJobId())) {
                    JobV3 job = jobMap.get(folder.getJobId());
                    folderVO.setJobTitle(job.getTitle());
                }
                return folderVO;
            }).toList();
            jobSharingPlatformVO.setExistedTalentAssociatedJobFolder(folderVOList);
        }
    }

    /**
     * Get S3 Link after uploading html file to s3
     *
     * @param jobSharingPlatformDTO
     * @return
     */
    private String uploadHTMLToS3(JobSharingPlatformDTO jobSharingPlatformDTO) {
        //String S3Link;
        try {

            String htmlString = jobSharingPlatformDTO.getJobDisplayContent()
                    .replace("\\n", "\n")
                    .replace("\\\"", "\"")
                    .replace("\\\\", "\\");

            String s3key = generateS3Key(jobSharingPlatformDTO.getJobDisplayContent());
            //S3Link = S3URL_PREFIX + s3key;

            String apnURL = wechatJobSharingProperties.getJobSharingURL() + s3key;
            String htmlStringWithLink = htmlString.replace(META_URL_PLACEHOLDER, apnURL);
            String htmlContentStr = generateHtmlStringByPlatformType(s3key, htmlStringWithLink, jobSharingPlatformDTO.getPlatformType());

            String S3Link = storeService.uploadContentStringWithKey(UploadTypeEnum.JOB_SHARING_CONTENT, CONTENT_TYPE, s3key, htmlContentStr).getBody();

            return apnURL;
        } catch (Exception e) {
            log.error("[Job-Sharing] Cannot get Links from upload sharing content, jobId:{}, error:{}", jobSharingPlatformDTO.getJobId(), e.getMessage());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_UPLOADHTMLTOS3_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
    }

    private String generateHtmlStringByPlatformType(String uuid, String htmlString, PlatformType platformType) {
        String generatedHtmlString = htmlString;
        if (platformType.equals(PlatformType.WECHAT)) {
            WechatJSAPIConfig wechatJSAPIConfig = getWechatJsApiConfig(uuid);
            generatedHtmlString = htmlString
                    .replace(META_WECHAT_APPID_PLACEHOLDER, wechatJSAPIConfig.getAppId())
                    .replace(META_NONCESTR_PLACEHOLDER, wechatJSAPIConfig.getNonceStr())
                    .replace(META_SIGNATURE_PLACEHOLDER, wechatJSAPIConfig.getSignature())
                    .replace(META_TIMESTAMP_PLACEHOLDER, wechatJSAPIConfig.getTimestamp());
        }

        return generatedHtmlString;
    }


    private String getS3LinkKeyFromSharingLinks(String s3Link) {
        try {
            String[] strings = org.apache.commons.lang3.StringUtils.split(s3Link, "/");
            String sharingJobUuid = strings[strings.length - 1];
            return sharingJobUuid;
        } catch (Exception ex) {
            log.error("[Job-Sharing] cannot parser uuid from s3 link, error:{}", ex.getMessage());
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_GETUUIDFROMS3LINKS_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
    }

    private String generateS3Key(String content) {
        return SecureUtil.md5(content);
    }


    private JobSharingPlatformDTO saveJobSharing(JobSharingPlatformDTO jobSharingPlatformDTO) {
        JobSharingPlatform jobSharingPlatform = jobSharingPlatformMapper.toEntity(jobSharingPlatformDTO);
        String jobSharingContent = convertJobContentToJsonStr(jobSharingPlatformDTO);
        jobSharingPlatform.setUserId(SecurityUtils.getUserId());
        jobSharingPlatform.setJobSharingContent(jobSharingContent);
        JobSharingPlatform savedJobSharingPlatform = jobSharingPlatformRepository.save(jobSharingPlatform);
        jobSharingPlatformDTO = jobSharingPlatformMapper.toDto(savedJobSharingPlatform);
        return jobSharingPlatformDTO;
    }

    private JobSharingPlatformDTO formatJobSharingPlatformDTO(JobSharingPlatform jobSharingPlatform) {
        JobSharingContentDTO jobSharingContentDTO = JsonUtil.fromJson(JSONUtil.toJsonStr(jobSharingPlatform.getJobSharingContent()), JobSharingContentDTO.class);
        //JobSharingPlatformDTO jobSharingPlatformDTO = jobSharingContentDTOMapper.toSharingPlatformDTO(jobSharingContentDTO);

        JobSharingPlatformDTO jobSharingPlatformDTO = jobSharingPlatformMapper.toCombine(jobSharingPlatform, jobSharingContentDTO);
        return jobSharingPlatformDTO;
    }

    private String convertJobContentToJsonStr(JobSharingPlatformDTO jobSharingPlatformDTO) {
        JobSharingContentDTO jobSharingContentDTO = jobSharingContentDTOMapper.toSharingContentDTO(jobSharingPlatformDTO);
        Map<String, Object> jobSharingContentMap = new ObjectMapper().convertValue(jobSharingContentDTO, Map.class);
        String sharingContent = JsonUtil.toJsonIgnoreNullFields(jobSharingContentMap);
        return sharingContent;
    }

    private void validateInput(JobSharingPlatformDTO jobSharingPlatformDTO) {
        if (jobSharingPlatformDTO.getJobId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEINPUT_JOBIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (jobSharingPlatformDTO.getDisplayLanguageId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEINPUT_LANGUAGENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (SecurityUtils.getUserId() == null || SecurityUtils.getUserId().equals(-1L)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEINPUT_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        if (jobSharingPlatformDTO.getPlatformType() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOB_VALIDATEINPUT_PLATFORMTYPENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
    }

    private String resizeImageThumbnail(String s3Link) throws IOException {
        String uuid = getS3LinkKeyFromSharingLinks(s3Link);

        // 获取元数据，检查文件是否存在和大小
        CloudFileObjectMetadata imageObjectMetadata = storeService.getFileFromS3(uuid, UploadTypeEnum.JOB_SHARING_POSTER_IMAGE.getKey()).getBody();
        if (imageObjectMetadata == null) {
            return null;
        }

        // 如果文件足够小，直接返回原链接
        if (imageObjectMetadata.getContentLength() < 1024 * 1024) {
            return s3Link;
        }

        byte[] imageContent = imageObjectMetadata.getContent();

        // 使用Thumbnailator处理图片
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Thumbnails.of(new ByteArrayInputStream(imageContent))
                .size(106, 137)      // 设置目标尺寸
                .outputFormat("jpg") // 输出格式
                .outputQuality(0.8)  // 设置压缩质量，值在0到1之间
                .toOutputStream(outputStream);

        byte[] imageBytes = outputStream.toByteArray();
        String s3key = DigestUtil.md5Hex(imageBytes);

        return storeService.uploadContentBytesWithKey(
                UploadTypeEnum.JOB_SHARING_POSTER_IMAGE,
                MimeTypeUtils.IMAGE_JPEG_VALUE,
                s3key,
                imageBytes
        ).getBody();
    }
}
