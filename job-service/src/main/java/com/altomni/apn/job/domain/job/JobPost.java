package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

@ApiModel(description = "JobPost is used to post job information on third party job board")
@Entity
@Table(name = "job_post")
public class JobPost  extends AbstractPermissionAuditingEntity implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Job id")
    @Column(name = "job_id", updatable = false)
    private Long jobId;

    @ApiModelProperty(value = "JazzHRWorkFlowType Job Id")
    @Column(name = "jazzhr_job_id", nullable = false)
    private String jazzhrJobId;

    @ApiModelProperty(value = "Frequency")
    @Column(name = "frequency")
    private Integer frequency;

    @ApiModelProperty(value = "The date when the job starts")
    @Column(name = "start_date")
    private LocalDate startDate;

    @ApiModelProperty(value = "The date when the job ends")
    @Column(name = "end_date")
    private LocalDate endDate;

    @ApiModelProperty(value = "The workflow type of JazzHR")
    @Column(name = "workflow_type")
    private Integer workflowType;

    @ApiModelProperty(value = "The primary recruiter user Id")
    @Column(name = "primary_recruiter_id")
    private Long primaryRecruiterId;

    @ApiModelProperty(value = "The job status of JazzHR")
    @Column(name = "jazzhr_status")
    private Integer jazzhrStatus;

    @ApiModelProperty(value = "isReposted, True or False")
    @Column(name = "is_reposted", nullable = false)
    private boolean isReposted = false;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getJazzhrJobId() {
        return jazzhrJobId;
    }

    public void setJazzhrJobId(String jazzhrJobId) {
        this.jazzhrJobId = jazzhrJobId;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public Integer getFrequency() {
        return frequency;
    }

    public void setFrequency(Integer frequency) {
        this.frequency = frequency;
    }

    public Integer getWorkflowType() {
        return workflowType;
    }

    public void setWorkflowType(Integer workflowType) {
        this.workflowType = workflowType;
    }

    public Long getPrimaryRecruiterId() {
        return primaryRecruiterId;
    }

    public void setPrimaryRecruiterId(Long primaryRecruiterId) {
        this.primaryRecruiterId = primaryRecruiterId;
    }

    public boolean getReposted() {
        return isReposted;
    }

    public void setReposted(boolean reposted) {
        isReposted = reposted;
    }

    public Integer getJazzhrStatus() {
        return jazzhrStatus;
    }

    public void setJazzhrStatus(Integer jazzhrStatus) {
        this.jazzhrStatus = jazzhrStatus;
    }

    @Override
    public Object clone() throws CloneNotSupportedException {
        return super.clone();
    }

    @Override
    public String toString() {
        return "JobPost{" +
            "id=" + id +
            ", jobId=" + jobId +
            ", jazzhrJobId=" + jazzhrJobId +
            ", startDate=" + startDate +
            ", endDate=" + endDate +
            ", workflowType=" + workflowType +
            ", primaryRecruiterId=" + primaryRecruiterId +
            ", isReposted=" + isReposted +
            "} " + super.toString();
    }
}
