package com.altomni.apn.job.service.vo.jobsharing;

import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.PlatformType;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.wechat.WechatJSAPIConfig;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageType;
import com.altomni.apn.job.domain.enumeration.sharing.PosterImageTypeConverter;
import com.altomni.apn.job.service.enumeration.JobSharingPlatformType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;
import javax.persistence.Converter;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobSharingPosterImageVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id for image")
    private Long id;

    @ApiModelProperty(value = "fileName for image")
    private String fileName;

    @ApiModelProperty(value = "linkUrl for image")
    private String linkUrl;

    private String thumbnailLinkUrl;

    @ApiModelProperty(value = "type for image")
    @Convert(converter = PosterImageTypeConverter.class)
    private PosterImageType type;

    @ApiModelProperty(value = "createdDate for image")
    private Instant createdDate;

}
