package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.job.domain.enumeration.SearchCategory;
import com.altomni.apn.job.domain.enumeration.SearchCategoryConverter;
import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;


@Entity
@Table(name = "job_search_folder")
@Data
public class JobSearchFolder extends AbstractPermissionAuditingEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "name", nullable = false, length = 100)
    private String name;

    @Column(name = "search_category", nullable = false)
    @Convert(converter = SearchCategoryConverter.class)
    private SearchCategory searchCategory = SearchCategory.NOCATEGORY;

    @Column(name = "search_criteria", nullable = false, columnDefinition = "json")
    @JsonRawValue
    private String searchCriteria;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "job_folder_id")
    private Long jobFolderId;

    @Column(name = "is_active")
    private boolean isActive;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public SearchCategory getSearchCategory() {
        return searchCategory;
    }

    public void setSearchCategory(SearchCategory searchCategory) {
        this.searchCategory = searchCategory;
    }

    public String getSearchCriteria() {
        return searchCriteria;
    }

    public void setSearchCriteria(String searchCriteria) {
        this.searchCriteria = searchCriteria;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public Long getJobFolderId() {
        return jobFolderId;
    }

    public void setJobFolderId(Long jobFolderId) {
        this.jobFolderId = jobFolderId;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }
}
