package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 *  job contact relation.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "User、Job and client contact association. Include user' contact category on job.")
@Entity
@Table(name = "job_company_contact_relation")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobCompanyContactRelation extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "user id .", required = true)
    @JsonIgnore
    @Column(name = "client_contact_id", nullable = false)
    private Long clientContactId;

    @ApiModelProperty(value = "job id", required = true)
    @Column(name = "job_id", nullable = false)
    private Long jobId;

    @ApiModelProperty(value = "talent id", required = true)
    @Column(name = "talent_id", nullable = false)
    private Long talentId;

    @ApiModelProperty(value = "type of contact category", required = true)
    @Column(name = "contact_category", nullable = false)
    private Integer contactCategory;

}






