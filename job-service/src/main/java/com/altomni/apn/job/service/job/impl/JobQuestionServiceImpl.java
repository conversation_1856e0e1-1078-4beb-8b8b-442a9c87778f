package com.altomni.apn.job.service.job.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.job.config.env.JobApiPromptProperties;
import com.altomni.apn.job.domain.job.JobQuestion;
import com.altomni.apn.job.repository.job.JobQuestionRepository;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.service.dto.job.JobQuestionDTO;
import com.altomni.apn.job.service.job.JobQuestionService;
import com.altomni.apn.job.service.user.UserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

@Service
public class JobQuestionServiceImpl implements JobQuestionService {
    @Resource
    private JobQuestionRepository jobQuestionRepository;
    @Resource
    private JobRepository jobRepository;
    @Resource
    private UserService userService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    JobApiPromptProperties jobApiPromptProperties;

    @Override
    public JobQuestionDTO create(JobQuestion jobQuestion) {
        if (jobQuestion.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBQUESTION_CREATE_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        JobQuestion result = jobQuestionRepository.save(jobQuestion);
        JobQuestionDTO jobQuestionDTO = JobQuestionDTO.fromJobQuestion(result);
        setJobQuestionRelateEntity(jobQuestionDTO);
        return jobQuestionDTO;
    }

    private void setJobQuestionRelateEntity(JobQuestionDTO jobQuestionDTO) {
        if (jobQuestionDTO != null) {
            if (jobQuestionDTO.getUserId() != null) {
                jobQuestionDTO.setUser(userService.findById(jobQuestionDTO.getUserId()).getBody());
            }
            if (jobQuestionDTO.getJobId() != null) {
                jobQuestionDTO.setJob(jobRepository.findById(jobQuestionDTO.getJobId()).get());
            }
        }
    }

    @Override
    public JobQuestionDTO update(JobQuestion jobQuestion) {
        JobQuestion existing = jobQuestionRepository.findById(jobQuestion.getId()).get();
        if (existing == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBQUESTION_UPDATE_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        ServiceUtils.myCopyProperties(jobQuestion, existing, JobQuestion.UpdateSkipProperties);
        JobQuestion result = jobQuestionRepository.save(existing);
        JobQuestionDTO jobQuestionDTO = JobQuestionDTO.fromJobQuestion(result);
        setJobQuestionRelateEntity(jobQuestionDTO);
        return jobQuestionDTO;
    }

    @Override
    public JobQuestionDTO findOneWithEntity(Long id) {
        JobQuestionDTO jobQuestionDTO = findOne(id);
        setJobQuestionRelateEntity(jobQuestionDTO);
        return jobQuestionDTO;
    }

    @Override
    public JobQuestionDTO findOne(Long id) {
        JobQuestion jobQuestion = jobQuestionRepository.findById(id).get();
        return jobQuestion != null ? JobQuestionDTO.fromJobQuestion(jobQuestion) : null;
    }

    @Override
    public void delete(Long id) {
        JobQuestion existing = jobQuestionRepository.findById(id).get();
        if (existing == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(JobAPIMultilingualEnum.JOBQUESTION_UPDATE_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),jobApiPromptProperties.getJobService()));
        }
        jobQuestionRepository.deleteById(id);
    }

    @Override
    public List<JobQuestionDTO> findAllByJobId(Long jobId) {
        List<JobQuestion> jobNoteList = jobQuestionRepository.findAllByJobId(jobId);
        return CollUtil.isNotEmpty(jobNoteList) ? ServiceUtils.convert2DTOList(jobNoteList, JobQuestionDTO.class) : Collections.emptyList();
    }
}
