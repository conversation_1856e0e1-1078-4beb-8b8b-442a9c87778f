package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobPermission;
import com.fasterxml.jackson.annotation.JsonIdentityInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.ObjectIdGenerators;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A UserJobRelation.
 */
@ApiModel(description = "User and Job association. Include user' permissionIntValue and other info on job.")
@Entity
@Table(name = "user_job_relation")
@JsonIdentityInfo(
    generator = ObjectIdGenerators.PropertyGenerator.class,
    property = "id")
public class UserJobRelation extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @JsonIgnore
    @ApiModelProperty(value = "user' permissionIntValue on job. It is required when create the entity.")
    @Column(name = "permission")
    private Integer permission;

    // support multiple permission
    @Transient
    private Set<String> permissionSet;

    @ApiModelProperty(value = "User's own search string on job. This is to train parser's search string given the JD.")
    @Column(name = "search_string")
    private String searchString;

    @ApiModelProperty(value = "user id", required = true)
    @Column(name = "user_id", nullable = false)
    @NotNull
    private Long userId;

    @ApiModelProperty(value = "job id", required = true)
    @Column(name = "job_id", nullable = false)
    @NotNull
    private Long jobId;

    @Column(name = "status")
    private Boolean status = Boolean.TRUE;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("userId", "jobId", "id"));

    public static Set<String> FixSkipProperties = new HashSet<>(Arrays.asList("id"));

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getPermission() {
        return permission;
    }

    public UserJobRelation permission(Integer permission) {
        this.permission = permission;
        return this;
    }

    public void setPermission(Integer permission) {
        this.permission = permission;
    }

    public Set<String> getPermissionSet() {
        return JobPermission.parseJobPermission(this.permission);
    }

    public void setPermissionSet(Set<String> permissionSet) {
        this.permissionSet = permissionSet;
        this.permission = JobPermission.makeupJobPermission(permissionSet);
    }

    public Long getUserId() {
        return userId;
    }

    public UserJobRelation userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getJobId() {
        return jobId;
    }

    public UserJobRelation jobId(Long jobId) {
        this.jobId = jobId;
        return this;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public String getSearchString() {
        return searchString;
    }

    public UserJobRelation searchString(String searchString) {
        this.setSearchString(searchString);
        return this;
    }

    public void setSearchString(String searchString) {
        this.searchString = searchString;
    }

    public Boolean getStatus() {
        return status;
    }

    public UserJobRelation status(Boolean status) {
        this.status(status);
        return this;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }
}
