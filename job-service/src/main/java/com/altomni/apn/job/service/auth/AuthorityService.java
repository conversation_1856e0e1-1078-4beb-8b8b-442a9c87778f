package com.altomni.apn.job.service.auth;

import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.user.LoginVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "authority-service")
public interface AuthorityService {

    @PostMapping("/authority/api/v3/credential")
    ResponseEntity<CredentialDTO> findCredential(@RequestBody LoginVM loginVM);

}
