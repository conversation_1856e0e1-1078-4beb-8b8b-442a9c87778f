package com.altomni.apn.job.domain.async;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.job.domain.enumeration.AsyncEnum;
import com.altomni.apn.job.domain.enumeration.AsyncEnumConverter;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.enumeration.user.StatusConverter;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A AsyncRecord. This is use for tracking async talent and job records from mysql to elastic search
 */
@Entity
@Table(name = "async_record")
public class AsyncRecord extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "Async type: ESFILLER(0), PARSER(1)")
    @Convert(converter = AsyncEnumConverter.class)
    @Column(name = "async_type")
    private AsyncEnum asyncType;

    @Column(name = "data_id")
    private Long dataId;

    @ApiModelProperty(value = "Data type: Talent or Job")
    @Column(name = "data_type")
    @Convert(converter = AsyncEnumConverter.class)
    private AsyncEnum dataType;

    @Convert(converter = StatusConverter.class)
    @Column(name = "status")
    private Status status;

    @Column(name = "response_code")
    private Integer responseCode;

    @Column(name = "response_message")
    private String responseMessage;

    public AsyncRecord() {
    }

    public AsyncRecord(AsyncEnum asyncType, Long dataId, AsyncEnum dataType, Status status, Integer responseCode, String responseMessage) {
        this.asyncType = asyncType;
        this.dataId = dataId;
        this.dataType = dataType;
        this.status = status;
        this.responseCode = responseCode;
        this.responseMessage = responseMessage;
    }

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public AsyncEnum getAsyncType() {
        return asyncType;
    }

    public void setAsyncType(AsyncEnum asyncType) {
        this.asyncType = asyncType;
    }

    public AsyncRecord asyncType(AsyncEnum asyncType) {
        this.asyncType = asyncType;
        return this;
    }

    public Long getDataId() {
        return dataId;
    }

    public void setDataId(Long dataId) {
        this.dataId = dataId;
    }

    public AsyncRecord dataId(Long dataId) {
        this.dataId = dataId;
        return this;
    }

    public AsyncEnum getDataType() {
        return dataType;
    }

    public void setDataType(AsyncEnum dataType) {
        this.dataType = dataType;
    }

    public AsyncRecord dataType(AsyncEnum dataType) {
        this.dataType = dataType;
        return this;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status status) {
        this.status = status;
    }

    public AsyncRecord status(Status status) {
        this.status = status;
        return this;
    }

    public Integer getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(Integer responseCode) {
        this.responseCode = responseCode;
    }

    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AsyncRecord userFavoriteJob = (AsyncRecord) o;
        if (userFavoriteJob.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), userFavoriteJob.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "AsyncRecord{" +
            "id=" + id +
            ", asyncType=" + asyncType +
            ", dataId=" + dataId +
            ", dataType=" + dataType +
            ", status=" + status +
            ", responseCode=" + responseCode +
            ", responseMessage='" + responseMessage + '\'' +
            '}';
    }
}
