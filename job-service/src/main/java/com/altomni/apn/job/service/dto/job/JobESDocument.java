package com.altomni.apn.job.service.dto.job;

import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.job.JobSkillBriefDTO;
import com.altomni.apn.common.dto.talent.UserResponsibility;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobESDocument {

    @ApiModelProperty(value = "The tenant id the job belongs. It is injected from the user entity when creating job. Read only.")
    private Long tenantId;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    private String companyId;

    private String companyName;

    private List<String> industries;

    private String logo;

    private String code;

    private String department;

    private Object type;

    private List<String> affiliations;

    private JobStatus status;

    private String currency;

    private String payType;

    private List<String> favoriteUserIds;

    private String startDate;

    private String endDate;

    private Instant postingTime;

    private Instant createdDate;

    private Instant lastModifiedDate;

    private Instant lastActivityTime;

    private Instant lastSubmitInterviewTime;

    @ApiModelProperty(value = "The group of assigned user ids, one or more user role JsonStrings ")
    private JSONObject assignedUsers;

    @ApiModelProperty(value = "The JD in text format, to save in DB", required = true)
    private String text;

    @ApiModelProperty(value = "The responsibility part of jd Text")
    private String responsibilityText;

    @ApiModelProperty(value = "The requirement part of jd Text")
    private String requirementText;

    @ApiModelProperty(value = "The summary part of jd Text")
    private String summaryText;

    @ApiModelProperty(value = "This locations source from job city, country etc. This format is esfiller prefer format.")
    private List<JSONObject> locations;

    private List<String> jobFunctions;

    private List<String> jobFunctionDisplays;

    private String minimumDegreeLevel;

    private List<String> preferredDegrees;

    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<JobSkillBriefDTO> requiredSkills;

    @ApiModelProperty(value = "one or more preferred skills ")
    private List<JobSkillBriefDTO> preferredSkills;

    @ApiModelProperty(value = "one or more required languages")
    private List<String> requiredLanguages;

    @ApiModelProperty(value = "one or more preferred languages")
    private List<String> preferredLanguages;

    private RangeDTO salaryRange;

    private RangeDTO billRange;

    private RangeDTO experienceYearRange;

    private Integer openings;

    private Integer maxSubmissions;

    private JSONObject hiringManager;

    private Integer totalApplications;

    private Integer totalInterviews;

    private Boolean published;

    private List<UserResponsibility> userResponsibility0;
    private List<UserResponsibility> userResponsibility1;
    private List<UserResponsibility> userResponsibility2;
    private List<UserResponsibility> userResponsibility3;
    private List<UserResponsibility> userResponsibility4;
    private List<UserResponsibility> userResponsibility5;
    private List<UserResponsibility> userResponsibility6;
    private List<UserResponsibility> userResponsibility7;
    private List<UserResponsibility> userResponsibility8;
    private List<UserResponsibility> userResponsibility9;
    private List<UserResponsibility> userResponsibility10;
    private List<UserResponsibility> userResponsibility11;
    private List<UserResponsibility> userResponsibility12;
    private List<UserResponsibility> userResponsibility13;
    private List<UserResponsibility> userResponsibility14;
    private List<UserResponsibility> userResponsibility15;
    private List<UserResponsibility> userResponsibility16;
    private List<UserResponsibility> userResponsibility17;
    private List<UserResponsibility> userResponsibility18;
    private List<UserResponsibility> userResponsibility19;
    private List<UserResponsibility> userResponsibility20;

}
