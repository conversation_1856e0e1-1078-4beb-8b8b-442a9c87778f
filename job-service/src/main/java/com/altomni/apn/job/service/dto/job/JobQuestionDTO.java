package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.job.domain.job.JobQuestion;
import com.altomni.apn.common.domain.job.JobV3;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Objects;

/**
 * A JobQuestion.
 */
public class JobQuestionDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String question;

    private String answer;

    private Long jobId;

    @ApiModelProperty(value = "The user id (current user from access token) who created the question. Ready only. change this will be ignored.")
    private Long userId;

    // ************************************relate entity*********************************************
    @ApiModelProperty(value = "The job entity for the question. Read Only.")
    private JobV3 job;

    @ApiModelProperty(value = "The user entity who created the question. Read Only.")
    private User user;

    public static JobQuestionDTO fromJobQuestion(JobQuestion j) {
        JobQuestionDTO dto = new JobQuestionDTO();
        ServiceUtils.myCopyProperties(j, dto);
        return dto;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getQuestion() {
        return question;
    }

    public JobQuestionDTO question(String question) {
        this.question = question;
        return this;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    public String getAnswer() {
        return answer;
    }

    public JobQuestionDTO answer(String answer) {
        this.answer = answer;
        return this;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

    public JobV3 getJob() {
        return job;
    }

    public void setJob(JobV3 job) {
        this.job = job;
        this.jobId = job.getId();
    }

    public JobQuestionDTO job(JobV3 job) {
        this.job = job;
        return this;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
        this.userId = user.getId();
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JobQuestionDTO jobQuestion = (JobQuestionDTO) o;
        if (jobQuestion.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), jobQuestion.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "JobQuestionDTO{" +
            "id=" + id +
            ", question='" + question + '\'' +
            ", answer='" + answer + '\'' +
            ", jobId=" + jobId +
            ", userId=" + userId +
            ", job=" + job +
            ", user=" + user +
            '}';
    }
}
