package com.altomni.apn.job.converter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.enums.CellDataTypeEnum;
import com.alibaba.excel.metadata.CellData;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import com.altomni.apn.common.utils.DateUtil;

import java.time.Instant;

public class InstantExcelDateConverter implements Converter<Instant>
{

    @Override
    public Class supportJavaTypeKey() {
        return null;
    }

    @Override
    public CellDataTypeEnum supportExcelTypeKey() {
        return null;
    }

    @Override
    public Instant convertToJavaData(CellData cellData, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return null;
    }

    @Override
    public CellData convertToExcelData(Instant instant, ExcelContentProperty excelContentProperty, GlobalConfiguration globalConfiguration) throws Exception {

       return new CellData(DateUtil.instantToDateString(instant));
    }
}
