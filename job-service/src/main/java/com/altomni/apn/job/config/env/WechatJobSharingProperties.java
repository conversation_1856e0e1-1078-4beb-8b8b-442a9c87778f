package com.altomni.apn.job.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class WechatJobSharingProperties {

    @Value("${application.wechat.officialAccount.share.jobUrl}")
    private String jobSharingURL;



}
