package com.altomni.apn.job.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The JobPriority enumeration.
 */
public enum JobPriority implements ConvertedEnum<Integer> {
    ACTIVE(0),
    LOW(1),
    HIGH(3),
    URGENT(4);

    private final int dbValue;

    JobPriority(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<JobPriority, Integer> resolver =
        new ReverseEnumResolver<>(JobPriority.class, JobPriority::toDbValue);

    public static JobPriority fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
