package com.altomni.apn.job.service.dto.folder;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.List;


@ApiModel

public class JobFolderDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "job folder id")
        private Long id;

        @ApiModelProperty(value = "job folder name")
        private String name;

        @ApiModelProperty(value = "job folder note")
        private String folderNote;


        @ApiModelProperty(value = "job folder sharing target")
        @Valid
        private List<JobFolderSharingDTO> shareTo;

        public Long getId() {
                return id;
        }

        public void setId(Long id) {
                this.id = id;
        }

        public String getName() {
                return name;
        }

        public void setName(String name) {
                this.name = name;
        }

        public List<JobFolderSharingDTO> getShareTo() {
                return shareTo;
        }

        public void setShareTo(List<JobFolderSharingDTO> shareTo) {
                this.shareTo = shareTo;
        }



        public String getFolderNote() {
                return folderNote;
        }

        public void setFolderNote(String folderNote) {
                this.folderNote = folderNote;
        }
//        public JobFolder toJobFolder(JobFolderDTO dto){
//                JobFolder jobFolder = new JobFolder();
//                jobFolder.setName(dto.getName());
//                jobFolder.setId(dto.getId());
//                return jobFolder;
//        }

//        @ApiModelProperty(value = "id of user who own job folder")
//        private Long permissionUserId;
//
//        @ApiModelProperty(value = "id of team who own job folder")
//        private Long permissionTeamId;
}
