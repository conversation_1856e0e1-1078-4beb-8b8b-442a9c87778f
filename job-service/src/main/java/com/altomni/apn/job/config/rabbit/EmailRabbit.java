package com.altomni.apn.job.config.rabbit;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.enumeration.email.EmailModuleTypeEnum;
import com.altomni.apn.common.domain.enumeration.system.Status;
import com.altomni.apn.common.utils.EmailUtil;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.job.config.env.ApplicationProperties;
import com.altomni.apn.job.domain.email.EmailMessage;
import com.altomni.apn.job.domain.email.SendEmailRecord;
import com.altomni.apn.job.domain.job.SyncJobToIpgRelation;
import com.altomni.apn.job.repository.email.SendEmailRecordRepository;
import com.altomni.apn.job.repository.job.SyncJobToIpgRelationRepository;
import com.altomni.apn.job.service.job.JobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class EmailRabbit implements RabbitTemplate.ConfirmCallback {

    @Resource
    private EmailUtil emailUtil;

    @Resource(name = "emailRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource
    private JobService jobService;

    @Resource
    private SendEmailRecordRepository sendEmailRecordRepository;

    @Resource
    private SyncJobToIpgRelationRepository syncJobToIpgRelationRepository;

    @Resource
    private ApplicationProperties applicationProperties;

    @PostConstruct
    public void init() {
        //specify confirmcallback
        rabbitTemplate.setConfirmCallback(this);
    }

    /**
     * 用于消费 ipg官网提交简历后，发送email给提交job的am
     * @param message
     * @throws IOException
     * 踩坑：rabbit 的消费线程是一直存在的,当消费第一个数据,或者是连续消费的时候没有问题，整个线程信息中存在了用户的token信息了，但是当消息间隔时间72000秒（token的过期时间）
     * 这个时候 线程里的 token信息是过期的，这个时候调用了LoginUtil.simulateLogin 会调用到 customUserRepository.findOneWithRolesByUsername，在其他模块是没有问题的
     * 但是在job模块存在数据权限控制，可能会调用到initiationService.initiateDataPermissionByUserId(SecurityUtils.getUserId()), 这个方法是通过feign调用的，会检查token是否
     * 过期，过期了会导致线程的token信息无法销毁，rabbit 的 message 永远无法消费，可能存在rabbit内存泄漏的问题和rabbitListener 无线循环消费信息占用资源的问题。
     * 解决方案：在每次进入的时候，清空rabbitListener线程内的token,避免进入验证环节
     */
    @RabbitListener(containerFactory = "emailFactory", queues = {"${application.email-mq.queue}"})
    @RabbitHandler
    public void process(Message message) throws IOException {
//        log.info("{} Received message: {}，Business data：{}, properties : {}", this.getClass().getName(), message.toString(), new String(message.getBody()), JSONUtil.toJsonStr(properties));
        SecurityContextHolder.clearContext();
        LoginUtil.simulateLoginWithClient();
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        EmailMessage emailMessage = JSONUtil.toBean(json, EmailMessage.class);
        try {
            execute(emailMessage);
            log.info("[apn] ipg to apn rabbit is success");
        } catch (Exception e) {
            log.error("[apn] ipg to apn rabbit is error, error message：", e);
            if (emailMessage.checkRetryCount()) {
                rabbitTemplate.convertAndSend(applicationProperties.getExchange(), applicationProperties.getRoutingKey(), new Message(JSONUtil.toJsonStr(emailMessage).getBytes()));
            } else {
                SendEmailRecord sendEmailRecord = new SendEmailRecord();
                sendEmailRecord.setType(EmailModuleTypeEnum.SUBMIT_IPG_TO_APN);
                sendEmailRecord.setResponseCode(500);
                sendEmailRecord.setStatus(Status.Failed);
                sendEmailRecord.setRequestParam(json);
                sendEmailRecord.setResponseMessage(ExceptionUtils.getStackTrace(e));
                sendEmailRecordRepository.saveAndFlush(sendEmailRecord);
            }
        }
    }


    protected void execute(EmailMessage emailMessage) {
        long jobId = getJobIdFromIpgJobId(emailMessage.getJobId());
        if (jobId == 0L) {
            return;
        }
        String emailProject = emailUtil.getEmailSubject(jobId, emailMessage.getJobName());
        String emailContent = emailUtil.getEmailContent(jobId, emailMessage.getFirstName(), emailMessage.getLastName(), emailMessage.getEmail(), emailMessage.getResumeName(), emailMessage.getS3Link());
        jobService.sendMailToAmOfJob(emailMessage.getJobId(), emailProject, emailContent);
    }

    public long getJobIdFromIpgJobId(long ipgJobId) {
        SyncJobToIpgRelation syncJobToIpgRelation = syncJobToIpgRelationRepository.findByIpgJobId(ipgJobId);
        if (syncJobToIpgRelation == null) {
            log.error("[APN: IpgFillerJobService] send mail to AM of job error, parameter error. This job does not exist, ipg job ID: {}", ipgJobId);
            return 0;
        }
        return syncJobToIpgRelation.getApnJobId();
    }


    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String cause) {
        if (ack) {
            log.info("send message to rabbit success");
        } else {
            log.error("send message to rabbit error, error message = [{}]", cause);
        }
    }

}
