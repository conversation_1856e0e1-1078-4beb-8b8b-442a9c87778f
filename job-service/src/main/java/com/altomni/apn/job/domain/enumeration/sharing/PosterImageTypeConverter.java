package com.altomni.apn.job.domain.enumeration.sharing;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Convert;

@Convert
public class PosterImageTypeConverter extends AbstractAttributeConverter<PosterImageType, Integer> {
    public PosterImageTypeConverter() {
        super(PosterImageType::toDbValue, PosterImageType::fromDbValue);
    }
}
