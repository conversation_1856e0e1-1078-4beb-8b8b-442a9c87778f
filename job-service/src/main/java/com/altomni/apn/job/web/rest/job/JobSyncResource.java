package com.altomni.apn.job.web.rest.job;

import com.altomni.apn.job.service.elastic.EsFillerJobService;
import com.altomni.apn.job.service.job.JobSyncService;
import com.altomni.apn.job.web.rest.vm.JobSyncToMqVM;
import com.altomni.apn.job.web.rest.vm.MqMessageCountVM;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 */
@Api(tags = {"Job"})
@RestController
@RequestMapping("/api/v3")
public class JobSyncResource {

    private final Logger log = LoggerFactory.getLogger(JobSyncResource.class);

    @Resource
    private JobSyncService jobSyncService;

    @Resource
    private EsFillerJobService esFillerJobService;

    @PostMapping("/canal/check-job-mq-message-count")
    public ResponseEntity<MqMessageCountVM> checkJobMqMessageCount() {
        log.debug("[Canal] checkJobMqMessageCount!");
        return ResponseEntity.ok(jobSyncService.checkJobMqMessageCount());
    }

    @PostMapping("/canal/sync-jobs-to-mq")
    public ResponseEntity<Void> syncJobsToMQ(@RequestBody JobSyncToMqVM jobSyncToMqVM) {
//        log.info("[EsFillerJobService: syncJobToMQ @{}] syncJobsToMQ: {}", SecurityUtils.getUserId(), jobSyncToMqVM);
        jobSyncService.syncJobsToMQ(jobSyncToMqVM.getJobIds(), jobSyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/canal/bulk-sync-jobs-to-mq")
    public ResponseEntity<Void> bulkSyncJobsToMQ(@RequestBody JobSyncToMqVM jobSyncToMqVM) {
//        log.info("[EsFillerJobService: syncJobToMQ @{}] syncJobsToMQ: {}", SecurityUtils.getUserId(), jobSyncToMqVM);
        jobSyncService.bulkSyncJobsToMQ(jobSyncToMqVM.getJobIds(), jobSyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/canal/sync-jobs-to-hr-mq")
    public ResponseEntity<Void> syncJobsToHrMQ(@RequestBody JobSyncToMqVM jobSyncToMqVM) {
//        log.info("[EsFillerJobService: syncJobToMQ @{}] syncJobsToMQ: {}", SecurityUtils.getUserId(), jobSyncToMqVM);
        jobSyncService.syncJobsToHrMQ(jobSyncToMqVM.getJobIds(), jobSyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @PostMapping("/canal/sync-jobs-to-agency-mq")
    public ResponseEntity<Void> syncJobsToAgencyMQ(@RequestBody JobSyncToMqVM jobSyncToMqVM) {
//        log.info("[EsFillerJobService: syncJobToMQ @{}] syncJobsToMQ: {}", SecurityUtils.getUserId(), jobSyncToMqVM);
        jobSyncService.syncJobsToAgencyMQ(jobSyncToMqVM.getJobIds(), jobSyncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/jobs/get-job-document/{jobId}")
    public ResponseEntity<String> getJobWithoutEntity(@ApiParam(value = "job id", required = true) @PathVariable("jobId") Long jobId) {
        log.info("[APN: Job @{}] REST request to get JobEsSyncDocument : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), jobId);
        return ResponseEntity.ok(esFillerJobService.getJobDocument(jobId));
    }
}
