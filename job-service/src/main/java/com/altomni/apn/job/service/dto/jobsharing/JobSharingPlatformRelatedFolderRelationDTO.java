package com.altomni.apn.job.service.dto.jobsharing;


import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@AllArgsConstructor
@NoArgsConstructor
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobSharingPlatformRelatedFolderRelationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    //related folder
    private Long relatedFolderId;
    private Instant expiredTime;


    
}
