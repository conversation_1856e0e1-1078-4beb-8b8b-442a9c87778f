package com.altomni.apn.job.service.dto.aisourcing;

import com.altomni.apn.job.domain.enumeration.aisourcing.EliminateType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class EliminateTalentDTO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private EliminateType eliminateType;

    private String note;

}
