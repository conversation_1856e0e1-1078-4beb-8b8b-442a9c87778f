package com.altomni.apn.job.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.email-mq.exchange}")
    private String exchange;

    @Value("${application.email-mq.routing-key}")
    private String routingKey;

    @Value("${application.esfiller.pauseSyncThreshold}")
    private Integer pauseSyncThreshold;

    @Value("${application.esfiller.larkWebhookKey}")
    private String larkWebhookKey;

    @Value("${application.esfiller.larkWebhookUrl}")
    private String larkWebhookUrl;

    @Value("${application.esfiller.updateUrl}")
    private String updateUrl;

    @Value("${application.esfiller.syncUrl}")
    private String syncUrl;

    @Value("${application.commonService}")
    private String apnCommonServiceUrl;

    @Value("${application.emailService.supportSender:<EMAIL>}")
    private String supportSender;

    @Value("${application.emailService.ipgProxySender}")
    private String ipgProxySender;

    @Value("${application.mainPath.baseUrl}")
    private String baseUrl;

    @Value("${application.rater.url}")
    private String raterUrl;

    @Value("${application.rater.commonUrl}")
    private String commonUrl;

    @Value("${application.job.searchKeywordHistory.maxSize}")
    private Integer searchHistoryMaxSize;

    @Value("${application.job.searchKeywordHistory.expireTime}")
    private Integer searchHistoryExpireTime;

    @Value("${application.elasticrecord.url}")
    private String jobActivityESUrl;

//    @Value("${application.defaultJobFunctionsForIndustry}")
//    private String defaultJobFunctionsForIndustry;
//
//    @Value("${application.allJobFunctions}")
//    private String allJobFunctions;

    @Value("${application.searchPermission.job}")
    private String jobSearchAgencyPermissionUri;

    @Value("${application.aiSourcing.pagePermission:-1}")
    private String aiSourcingPermission;

    @Value("${application.aiSourcing.host:-1}")
    private String aiSourcingHost;
}
