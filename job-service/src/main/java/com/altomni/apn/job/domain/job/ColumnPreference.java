package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.domain.enumeration.search.ModuleTypeConverter;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.job.service.dto.job.ColumnPreferenceDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "column_preference")
public class ColumnPreference extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "creation_type")
    private String creationType;

    @Column(name = "temp_name")
    private String tempName;

    @Column(name = "item_sort")
    private String itemSort;

    @Column(name = "item_sort_all")
    private String itemSortAll;

    @Column(name = "module")
    @Convert(converter = ModuleTypeConverter.class)
    private ModuleType module;

    @Column(name = "page_size")
    private Integer pageSize;

    @Column(name = "description")
    private String description;

    @Column(name = "sort_order")
    private Integer sortOrder;

    @Column(name = "del_flag")
    private Boolean delFlag;

    @Column(name = "type")
    private Boolean type;


    public static ColumnPreference fromColumnPreferenceDTO(ColumnPreferenceDTO columnPreferenceDTO) {
        ColumnPreference columnPreference = new ColumnPreference();
        ServiceUtils.myCopyProperties(columnPreferenceDTO, columnPreference);
        columnPreference.setUserId(SecurityUtils.getUserId());
        return columnPreference;
    }
}
