package com.altomni.apn.job.service.dto.job;

import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.job.JobV3;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

/**
 * This entity defined column which allow to public.
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobPublicDTO extends AbstractAuditingEntity implements Serializable{

    private static final long serialVersionUID = 1L;

    private Long id;

    @ApiModelProperty(value = "The company which job belongs")
    private String company;

    private Long companyId;

    private Long companyEntityId;

    private String locations;

    @ApiModelProperty(value = "The url link to the company's logo")
    private String logo;

    @ApiModelProperty(value = "Job title")
    private String title;

    @ApiModelProperty(value = "Job location city")
    private String city;

    @ApiModelProperty(value = "Job location province")
    private String province;

    @ApiModelProperty(value = "Job location country")
    private String country;

    @ApiModelProperty(value = "Job location zipcode")
    private String zipcode;

    @ApiModelProperty(value = "The date the job is posted to ATS or job boards. Read Only.")
    private Instant postingDate;

    @ApiModelProperty(value = "Job type", allowableValues = "DIRECT_PLACEMENT, CONTRACT, RIGHT_TO_HIRE, FULL_TIME, PART_TIME")
    private JobType jobType;

    @ApiModelProperty(value = "The JD in text format, to save in DB", required = true)
    private String jdText;

    @ApiModelProperty(value = "The public description of JD")
    private String publicDesc;

    public static JobPublicDTO fromJob(JobV3 j) {
        JobPublicDTO dto = new JobPublicDTO();
        ServiceUtils.myCopyProperties(j, dto);
        return dto;
    }


}
