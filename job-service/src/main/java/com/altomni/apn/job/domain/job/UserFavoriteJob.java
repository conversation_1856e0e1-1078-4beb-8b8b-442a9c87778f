/*
jobv3 deprecated
migrated data to job_folder table

 */


//package com.altomni.apn.job.domain.job;
//
//import com.altomni.apn.common.domain.AbstractAuditingEntity;
//
//import javax.persistence.*;
//import java.io.Serializable;
//import java.util.Objects;
//
///**
// * A UserFavoriteJob.
// */
//@Entity
//@Table(name = "user_favorite_job")
//@Deprecated
//public class UserFavoriteJob extends AbstractAuditingEntity implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//
//    @Column(name = "user_id")
//    private Long userId;
//
//    @Column(name = "job_id")
//    private Long jobId;
//
//    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public UserFavoriteJob userId(Long userId) {
//        this.userId = userId;
//        return this;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove
//
//
//    public Long getJobId() {
//        return jobId;
//    }
//
//    public void setJobId(Long jobId) {
//        this.jobId = jobId;
//    }
//
//    public UserFavoriteJob jobId(Long jobId) {
//        this.jobId = jobId;
//        return this;
//    }
//
//    @Override
//    public boolean equals(Object o) {
//        if (this == o) {
//            return true;
//        }
//        if (o == null || getClass() != o.getClass()) {
//            return false;
//        }
//        UserFavoriteJob userFavoriteJob = (UserFavoriteJob) o;
//        if (userFavoriteJob.getId() == null || getId() == null) {
//            return false;
//        }
//        return Objects.equals(getId(), userFavoriteJob.getId());
//    }
//
//    @Override
//    public int hashCode() {
//        return Objects.hashCode(getId());
//    }
//
//    @Override
//    public String toString() {
//        return "UserFavoriteJob{" +
//            "id=" + id +
//            ", userId=" + userId +
//            ", jobId=" + jobId +
//            '}';
//    }
//}
