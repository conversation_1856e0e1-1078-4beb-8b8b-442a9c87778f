package com.altomni.apn.job.scheduled;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * job scheduled
 * <AUTHOR>
 */
@Slf4j
@Service
public class JobScheduled {

    /*@Resource
    private JobRepository jobRepository;

    @Resource
    private EsFillerJobService esFillerJobService;

    @Resource
    private AsyncRecordRepository asyncRecordRepository;

    *//**
     * max 40
     *//*
    @Value("${application.sync.threadNum:12}")
    private Integer threadNum;

    private volatile ExecutorService executorService;

    @Scheduled(fixedRateString = "${application.esfiller.scheduledSyncRate}"+"000")
    public void scheduledSyncJobsToEs() {
        log.info("[ElasticSearchFillerService: scheduledSyncJobsToES @-1] Scheduled sync Jobs start at: {}", LocalDateTime.now());
        Long count = jobRepository.countOutOfSyncJobIds();
        if (count <= 0) {
            return;
        }
        int startNum = 0;
        int pageSize = 2000;
        //todo redis lock to solve distributed problems
        while(true) {
            List<Long> jobIds = jobRepository.findOutOfSyncJobIds(startNum, pageSize);
            log.info("[JobScheduled: scheduledSyncJobsToES @-1] total jobs size: {}", jobIds.size());
            if (CollectionUtils.isEmpty(jobIds)) {
                break;
            }
            startNum += pageSize;
            List<List<Long>> listList = CollUtil.split(jobIds, 50);
            listList = listList.stream().filter(CollUtil::isNotEmpty).collect(Collectors.toList());
            CountDownLatch countDownLatch = new CountDownLatch(listList.size());
            listList.forEach(ids -> getExecutorService().execute(() -> {
                try {
                    List<Long> successIds = jobIds.stream().filter(esFillerJobService::scheduledSyncJobToEs).collect(Collectors.toList());
                    if (!successIds.isEmpty()) {
                        asyncRecordRepository.clearAllSyncRecordError(AsyncEnum.DATA_TYPE_JOB, successIds);
                    }
                } catch (Exception e) {
                    log.error(ExceptionUtils.getStackTrace(e));
                } finally {
                    countDownLatch.countDown();
                }
            }));
            try {
                countDownLatch.await(30,TimeUnit.MINUTES);
            } catch (InterruptedException e) {
                log.error("[apn jobScheduled await time out]");
            }
        }
        log.info("[JobScheduled: scheduledSyncJobsToES @-1] Scheduled sync Jobs to ES Done!");
    }

    private ExecutorService getExecutorService() {
        if (executorService == null) {
            synchronized (JobScheduled.class) {
                if (executorService == null) {
                    executorService = new ThreadPoolExecutor(
                            threadNum,
                            threadNum * 2,
                            60L,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(500000), ThreadUtil.newNamedThreadFactory("api-scheduled-sync-job-to-es", false));
                }
            }
        }
        return executorService;
    }*/

}
