package com.altomni.apn.job.web.rest.job;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.job.domain.job.JobQuestion;
import com.altomni.apn.job.service.dto.job.JobQuestionDTO;
import com.altomni.apn.job.service.job.JobQuestionService;
import com.altomni.apn.common.utils.SecurityUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Optional;

/**
 * REST controller for managing JobQuestion.
 */
@Api(tags = {"ATS-Jobs"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class JobQuestionResource {

    private static final String ENTITY_NAME = "jobQuestion";

    @Resource
    private JobQuestionService jobQuestionService;

    /**
     * POST  /job-questions : Create a new jobQuestion.
     *
     * @param jobQuestion the jobQuestion to create
     * @return the ResponseEntity with status 201 (Created) and with body the new jobQuestion, or with status 400 (Bad Request) if the jobQuestion has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/job-questions")
    @NoRepeatSubmit
    public ResponseEntity<JobQuestionDTO> createJobQuestion(@RequestBody JobQuestion jobQuestion) throws URISyntaxException {
        log.info("[APN: JobQuestion @{}] REST request to save JobQuestion : {}", SecurityUtils.getUserId(), jobQuestion);
        jobQuestion.setUserId(SecurityUtils.getUserId());
        JobQuestionDTO result = jobQuestionService.create(jobQuestion);
        return ResponseEntity.created(new URI("/api/job-questions/" + result.getId()))
            .body(result);
    }

    /**
     * PUT  /job-questions : Updates an existing jobQuestion.
     *
     * @param jobQuestion the jobQuestion to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated jobQuestion,
     * or with status 400 (Bad Request) if the jobQuestion is not valid,
     * or with status 500 (Internal Server Error) if the jobQuestion couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/job-questions/{id}")
    @NoRepeatSubmit
    public ResponseEntity<JobQuestionDTO> updateJobQuestion(@PathVariable Long id, @RequestBody JobQuestion jobQuestion) throws URISyntaxException {
        log.info("[APN: JobQuestion @{}] REST request to update JobQuestion : {}", SecurityUtils.getUserId(), jobQuestion);
        jobQuestion.setId(id);
        JobQuestionDTO result = jobQuestionService.update(jobQuestion);
        return ResponseEntity.ok()
            .body(result);
    }

    /**
     * GET  /job-questions/:id : get the "id" jobQuestion.
     *
     * @param id the id of the jobQuestion to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the jobQuestion, or with status 404 (Not Found)
     */
    @GetMapping("/job-questions/{id}")
    public ResponseEntity<JobQuestionDTO> getJobQuestion(@PathVariable Long id) {
        log.info("[APN: JobQuestion @{}] REST request to get JobQuestion : {}", SecurityUtils.getUserId(), id);
        JobQuestionDTO jobQuestion = jobQuestionService.findOneWithEntity(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(jobQuestion));
    }

    /**
     * DELETE  /job-questions/:id : delete the "id" jobQuestion.
     *
     * @param id the id of the jobQuestion to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/job-questions/{id}")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteJobQuestion(@PathVariable Long id) {
        log.info("[APN: JobQuestion @{}] REST request to delete JobQuestion : {}", SecurityUtils.getUserId(), id);
        jobQuestionService.delete(id);
        return ResponseEntity.ok().build();
    }

}
