package com.altomni.apn.job.service.vo.job;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ReflectUtil;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.dict.EnumRelationDTO;
import com.altomni.apn.common.domain.enumeration.ContactCategoryType;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.OperationType;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.RangeDTO;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.company.CompanyBriefDTO;
import com.altomni.apn.common.dto.company.SalesLeadClientContactDTO;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.dto.job.JobBoolStringDTO;
import com.altomni.apn.common.dto.job.JobNoteDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Id;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    @ApiModelProperty(value = "The tenant id the job belongs. It is injected from the user entity when creating job. Read only.")
    private Long tenantId;

    private Long companyId;

    @ApiModelProperty(value = "companyId, companyName and industry")
    private CompanyBriefDTO company;

    @ApiModelProperty(value = "The job of department .")
    private String department;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    @ApiModelProperty(value = "the recruitment process id for current job")
    private Long recruitmentProcessId;

    @ApiModelProperty(value = "Job type", allowableValues = "CONTRACT, FULL_TIME, PAY_ROLL")
    private JobType jobType;

    @ApiModelProperty(value = "This is job internal code company used to identify the job")
    private String code;

    @ApiModelProperty(value = "Client contact entity")
    private SalesLeadClientContactDTO clientContactName;

    @ApiModelProperty(value = "The category of client contact")
    private Integer clientContactCategory;

    @ApiModelProperty(value = "only for application send email")
    private String clientContactEmails;

    @ApiModelProperty(value = "The date when the job starts")
    private Instant startDate;

    @ApiModelProperty(value = "The date when the job ends")
    private Instant endDate;

    @ApiModelProperty(value = "The date the job is posted to ATS or job boards. Read Only.")
    private Instant postingTime;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
    private JobStatus status;

    @ApiModelProperty(value = "UUID from JD parser")
    private String uuid;

    @ApiModelProperty(value = "The JD in text format, to save in DB", required = true)
    private String jdText;

    @ApiModelProperty(value = "The public description of JD")
    private String publicDesc;

    @ApiModelProperty(value = "The url link to the JD. E.g. link to StoreService storage for the JD")
    private String jdUrl;

    @ApiModelProperty(value = "job lacation strings, one or more official city names ")
    private List<LocationDTO> locations;

    @ApiModelProperty(value = "job function strings, one or more official function names ")
    private List<EnumRelationDTO> jobFunctions;

    @ApiModelProperty(value = "The group of assigned user ids, one or more user role JsonStrings ")
    private List<AssignedUserDTO> assignedUsers;

    @ApiModelProperty(value = "Parser return skills string and save to job skillString column.")
    private List<SkillDTO> requiredSkills;

    @ApiModelProperty(value = "one or more preferred skills ")
    private List<SkillDTO> preferredSkills;

    @ApiModelProperty(value = "one or more required languages")
    private List<EnumRelationDTO> requiredLanguages;

    @ApiModelProperty(value = "one or more preferred languages")
    private List<EnumRelationDTO> preferredLanguages;

    @ApiModelProperty(value = "isVisible(company), True(1) or False(0). Default is False(0)")
    private Boolean visible;

    private Boolean favorite;

    private Integer currency;

    @ApiModelProperty(value = "bill rate range")
    private RangeDTO billRange;

    @ApiModelProperty(value = "salary rate range")
    private RangeDTO salaryRange;

    @ApiModelProperty(value = "salary rate unit")
    private RateUnitType payType;

    @ApiModelProperty(value = "number of openings for the job. Default is 1.")
    private Integer openings;

    @ApiModelProperty(value = "max application submissions")
    private Integer maxSubmissions;

    @ApiModelProperty(value = "range of experience year")
    private RangeDTO experienceYearRange;

    @ApiModelProperty(value = "Parser return bool string and save to job searchString column.")
    private List<JobBoolStringDTO> boolstr;

    private List<EnumRelationDTO> preferredDegrees;

    private EnumRelationDTO minimumDegreeLevel;

    private String logo;

    @ApiModelProperty(value = "All user created notes on the job. Read only.")
    private List<JobNoteDTO> notes;

    private Long additionalInfoId;

    @ApiModelProperty(value = "Ipg job status. Default is no_published, the newly post job to ipg.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
    private JobStatus ipgJobStatus = JobStatus.NO_PUBLISHED;

    @ApiModelProperty(value = "Ipg job type", allowableValues = "CONTRACT, FULL_TIME, FULL_TIME_OR_PART_TIME, INTERNSHIP")
    private JobType ipgJobType;

    @Size(max = 16380, message = "The ipg JD text is too long.")
    @ApiModelProperty(value = "The ipg JD in text format, to save in DB.Jdtext does not transfer text content, directly transfer HTML content")
    private String ipgJobDescription;

    @ApiModelProperty(value = "IPG job operation type, allowing CREATE, UPDATE, CLOSE and NULL")
    private OperationType operationType;

    public static JobVO fromJob(JobV3 job) {
        JobVO dto = Convert.convert(JobVO.class, job);

        JobVO additionalInfoExpand = Convert.convert(JobVO.class, com.alibaba.fastjson.JSONObject.parseObject(job.getJobExtendedInfo()));
        Set<String> skips = Arrays.stream(ReflectUtil.getFieldsDirectly(JobV3.class, true)).map(Field::getName).collect(Collectors.toSet());
        if (additionalInfoExpand != null) {
            ServiceUtils.myCopyProperties(additionalInfoExpand, dto, skips);
        }
        dto.setAdditionalInfoId(job.getAdditionalInfoId());
        return dto;
    }

    public static JobVO getNonRelationData(JobVO dto) {
        Set<String> skips = Arrays.stream(ReflectUtil.getFieldsDirectly(JobV3.class, true)).map(Field::getName).collect(Collectors.toSet());
        // ignored fields
        skips.add("additionalInfoId");
        skips.add("company");
        skips.add("assignedUsers");
        skips.add("boolstr");
        skips.add("notes");
        skips.add("uuid");
        skips.add("favorite");
        skips.add("ipgJobStatus");
        skips.add("ipgJobType");
        skips.add("ipgJobDescription");
        skips.add("operationType");
        skips.add("clientContactName");
        skips.add("clientContactCategory");
        skips.add("clientContactEmails");

        JobVO nonRelationData = new JobVO();
        nonRelationData.setCreatedDate(null);
        nonRelationData.setLastModifiedDate(null);
        nonRelationData.setIpgJobStatus(null);
        if (dto != null) {
            ServiceUtils.myCopyProperties(dto, nonRelationData, skips);
        }
        return nonRelationData;
    }
}
