package com.altomni.apn.job.service.talent;


import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFolderRelationDTO;
import com.altomni.apn.common.dto.folder.talentrelatejob.TalentRelateJobFoldersDTO;
import com.altomni.apn.common.dto.talent.GetTalentsForAiRecommendationDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.Set;


@Component
@FeignClient(value = "talent-service")
public interface TalentService {

    @GetMapping("/talent/api/v3/talents/{id}/tenant-id")
    ResponseEntity<Long> findTenantIdByTalentId(@PathVariable("id") Long id);

    @PostMapping("/talent/api/v3/talents/ai-recommendation/ids")
    ResponseEntity<String> getTalentsForAiRecommendationByIds(@RequestBody GetTalentsForAiRecommendationDTO dto);

    @GetMapping("/talent/api/v3/talents/fullName/{talentId}")
    ResponseEntity<String> findFullNameByTalentId(@PathVariable("talentId") Long talentId);


    @PostMapping("/talent/api/v3/relate-job-folders/expire-time")
    ResponseEntity<String> createOrUpdateRelationOnRelatedJobFolderAndJobSharing(@RequestBody TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO);

    @PutMapping("/talent/api/v3/relate-job-folders/{folderId}/expire-time")
    ResponseEntity<TalentRelateJobFoldersDTO> updateSharedURLExpirationTimeOnRelateJobFolder(@PathVariable("folderId") String folderId, @RequestBody TalentRelateJobFolderRelationDTO talentRelateJobFolderRelationDTO);

    @PostMapping("/talent/api/v3/talents/confidential/view-able")
    ResponseEntity<Set<Long>> filterConfidentialTalentViewAble(@RequestBody Set<Long> talentIds);
}
