package com.altomni.apn.job.service.elastic;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.job.JobNoteDTO;
import com.altomni.apn.common.dto.search.SearchGroup;
import com.altomni.apn.job.service.dto.folder.JobCategoryCountRequestDTO;
import org.springframework.data.domain.Pageable;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface EsFillerJobService {

    HttpResponse searchFromCommonService(SearchGroup searchGroup, Pageable pageable) throws IOException;

    HttpResponse searchRelateJobFolderFromCommonService(SearchGroup searchGroup, Pageable pageable) throws IOException;

    void syncJobStatusToEs(Long tenantId, Long jobId, JobStatus status);

    void syncJobStatusToEsV3(Long tenantId, Long jobId, JobStatus status);

    void extractJobToMq(Collection<Long> jobIds, int priority);

    void extractBulkJobToMq(Collection<Long> jobIds, int priority);

    void sendJobNoteToMq(Long tenantId, Long jobId, JobNoteDTO jobNoteDTO, Boolean deleted);

    void saveNormalizedJobInfos(String normalizedJob);

    Map<String, Long> getJobCategoryCount(JobCategoryCountRequestDTO requestDto) throws IOException;

    void updateJobFolder(List<Long> jobIds, List<String> toFolderNames, List<String> fromFolderNames, Long tenantId);

    JobType getJobTypeByRecruitmentProcessId(Long recruitmentProcessId);

    String getJobDocument(Long jobId);

    void extractJobToHrMq(Collection<Long> ids, int priority);

    void extractJobToAgencyMq(Collection<Long> jobIds, int priority);

    void deleteJobToMq(Long jobId, Long tenantId, int priority);
}
