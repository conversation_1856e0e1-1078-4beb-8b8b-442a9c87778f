package com.altomni.apn.job.web.rest.job;


import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.dto.folder.*;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.domain.job.JobFolder;
import com.altomni.apn.job.service.dto.folder.*;
import com.altomni.apn.job.service.job.JobFolderService;
import com.altomni.apn.job.service.job.JobSearchFolderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Api(tags = {"ATS-Jobs"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class JobFolderResource {


    @Resource
    private JobFolderService jobFolderService;

    @Resource
    private JobSearchFolderService jobSearchFolderService;

    @PostMapping("/folders/search")
    public ResponseEntity<List<SearchJobFolderDTO>> searchJobFolders(@Valid @RequestBody FolderSearchRequestDTO folderSearchRequestDTO, @ApiParam Pageable pageable) {
        log.info("[APN: Job @{}] REST request to search job folder :{}", SecurityUtils.getUserId(), pageable);
        Page<SearchJobFolderDTO> jobFolderDetails = jobFolderService.searchJobFolders(folderSearchRequestDTO, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(jobFolderDetails, "/api/v3/folders/search");
        return new ResponseEntity<>(jobFolderDetails.getContent(), headers, HttpStatus.OK);
    }


    /***
     * get all job folders under current user
     * @param pageable
     * @return
     */
    @GetMapping("/folders")
    public ResponseEntity<List<JobFolderBriefDTO>> getJobFolders(@ApiParam Pageable pageable) {
        log.info("[APN: Job @{}] REST request to get job folder :{}", SecurityUtils.getUserId(), pageable);
        Page<JobFolderBriefDTO> jobFolderDetails = jobFolderService.getMyJobFolderWithCountListByUserId(SecurityUtils.getUserId(), pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(jobFolderDetails, "/api/v3/folders");
        return new ResponseEntity<>(jobFolderDetails.getContent(), headers, HttpStatus.OK);
    }

    /***
     * add a new Job Folder
     * @return JobFolderDetail
     */
    @PostMapping("/folders")
    @NoRepeatSubmit
    public ResponseEntity<JobFolderDTO> addJobFolder(@RequestBody @Valid JobFolderDTO jobFolderDTO) {
        log.info("[APN: Job @{}] REST request to add job folder", SecurityUtils.getUserId());
        jobFolderDTO = jobFolderService.createJobFolder(jobFolderDTO);
        return ResponseEntity.ok(jobFolderDTO);
    }


    /***
     * create a new Job Folder and add jobs into this folder
     * @return JobFolderDetail
     */
    @PostMapping("/folders-with-jobs")
    @NoRepeatSubmit
    public ResponseEntity<JobFolderAndJobsDTO> addJobsInNewFolder(@RequestBody @Valid JobFolderAndJobsDTO jobFolderAndJobsDTO) {
        log.info("[APN: Job @{}] REST request to add job folder", SecurityUtils.getUserId());
        jobFolderAndJobsDTO = jobFolderService.addJobsInNewFolder(jobFolderAndJobsDTO);
        return ResponseEntity.ok(jobFolderAndJobsDTO);
    }

    /***
     * Update Job folder by FolderId
     * @Parameter jobFolderDTO
     * @Parameter folderId
     * @return JobFolderDetail
     */
    @PutMapping("/folders/{folderId}")
    public ResponseEntity<JobFolderDTO> updateJobFolder(@Valid @RequestBody JobFolderDTO jobFolderDTO, @PathVariable Long folderId) {
        log.info("[APN: Job @{}] REST request to update job folder by jobId : {}", SecurityUtils.getUserId());
        jobFolderDTO = jobFolderService.updateJobFolder(jobFolderDTO, folderId);
        return ResponseEntity.ok(jobFolderDTO);
    }

    /***
     * Delete Job folder by FolderId
     * @Parameter folderId
     */
    @DeleteMapping("/folders/{folderId}")
    public ResponseEntity<Void> deleteJobFolder(@PathVariable Long folderId) {
        log.info("[APN: Job @{}] REST request to update job folder by jobId : {}", SecurityUtils.getUserId());
        jobFolderService.deleteFolder(folderId);
        return ResponseEntity.ok().build();
    }


    /***
     * Add Jobs into folders by JobIds and FolderIds
     * @Parameter JobFolderRelationListDTO
     *
     */
    @PostMapping("/add-jobs-to-folders")
    @NoRepeatSubmit
    public ResponseEntity<Void> addJobsToFolders(@Valid @RequestBody JobFolderRelationListDTO jobFolderRelationListDTO) {
        log.info("[APN: Job @{}] REST request to add jobs into folders", SecurityUtils.getUserId());
        jobFolderService.addJobsToFolders(jobFolderRelationListDTO);
        return ResponseEntity.ok().build();
    }


    /***
     * delete jobs which assigned to current folder
     * @param folderId
     * @param jobFolderRelationListDTO
     * @return void
     */
    @DeleteMapping("/folders/{folderId}/jobs")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteJobsFromFolder(@PathVariable Long folderId, @Valid @RequestBody JobFolderRelationListDTO jobFolderRelationListDTO) {
        log.info("[APN: Job @{}] REST request to delete job from fold with folderId", SecurityUtils.getUserId());
        jobFolderService.deleteJobsFromFolder(jobFolderRelationListDTO, folderId);
        return ResponseEntity.ok().build();
    }

    /***
     * get shared team list for job folder
     * @return return the team list under the job folder
     * @throws Throwable
     */
    @ApiOperation(value = "get shared team list for job folder by user")
    @GetMapping(value = "/folders/shared-teams")
    public ResponseEntity<List<FolderSharedTeamDTO>> getJobFolderSharedTeams() {
        log.info("APN JobFolder REST request to get shared team list for Job folder by user:  {} and tenant {} ", SecurityUtils.getUserId(), SecurityUtils.getTenantId());
        List<FolderSharedTeamDTO> jobFolderSharedTeams= jobFolderService.getDistinctSharedTeamsByUserId();
        return ResponseEntity.ok(jobFolderSharedTeams);
    }

    /***
     * get shared user list for job folder
     * @return list of shared user under current user folder
     */
    @ApiOperation(value = "get shared user list for job folder by user")
    @GetMapping(value = "/folders/shared-users")
    public ResponseEntity<List<FolderSharedUserDTO>> getJobFolderSharedUsers() {
        log.info("({},{}) REST request to get shared team list for Job folder by user:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        List<FolderSharedUserDTO> jobFolderSharedUsers= jobFolderService.getDistinctSharedUsersByUserId();
        return ResponseEntity.ok(jobFolderSharedUsers);
    }


    /***
     * delete the folder sharing from current user and it will not delete real folder
     * @param folderId the folder Id
     * @return void
     */
    @DeleteMapping("/shared-folders/{folderId}")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteSharingForSharedFolder(@PathVariable Long folderId) {
        log.info("[APN: Job @{}] REST request to delete job from fold with folderId", SecurityUtils.getUserId());
        jobFolderService.removeSharingForSharedFolder(folderId);
        return ResponseEntity.ok().build();
    }

    /***
     * Get shared folder list for user
     *
     * @return List<SharedFolderBriefDTO
     */
    @GetMapping("/shared-folders")
    public ResponseEntity<List<SharedFolderBriefDTO>> getSharedFolderList() {
        log.info("[APN: Job @{}] REST request to get shared job folder :{}, {}", SecurityUtils.getUserId(), SecurityUtils.getUserId(), SecurityUtils.getTeamId());
        List<SharedFolderBriefDTO> sharedFolderList = jobFolderService.getSharedFolderList(SecurityUtils.getUserId(), SecurityUtils.getTeamId());
        return ResponseEntity.ok(sharedFolderList);
    }

    /***
     * Get shared folder team and user that have the folders access as current user
     *
     * @return List<FolderSharedUserAndTeamDTO
     */
    @GetMapping("/shared-folders/shared-users-teams")
    public ResponseEntity<FolderSharedUserAndTeamDTO> get() {
        log.info("[APN: Job @{}] REST request to get teams and users that shared job folder as me :{}, {}", SecurityUtils.getUserId(), SecurityUtils.getUserId(), SecurityUtils.getTeamId());
        FolderSharedUserAndTeamDTO folderSharedUserAndTeamDTO = jobFolderService.getSharedUserAndTeamList();
        return ResponseEntity.ok(folderSharedUserAndTeamDTO);
    }



    /***
     * get all owner and shared job folders under current user with readwrite permission
     * @return FolderListDTO
     */
    @GetMapping("/folders/collaborative-folders")
    public ResponseEntity<FolderListDTO> getAllCollaborativeJobFolders() {
        log.info("[APN: Job @{}] REST request to get owner and shared job folders", SecurityUtils.getUserId());
        FolderListDTO folderListDTO = jobFolderService.getCollaborativeJobFolderList();
        return ResponseEntity.ok(folderListDTO);
    }

    /***
     * get all owner and shared job folders with permission info under current user
     * @return FolderListDTO
     */
    @GetMapping("/folders/custom-and-shared-folders")
    public ResponseEntity<FolderPermissionListDTO> getAllCustomAndSharedJobFolders() {
        log.info("[APN: Job @{}] REST request to get owner and shared job folders", SecurityUtils.getUserId());
        FolderPermissionListDTO folderListDTO = jobFolderService.getCustomAndSharedJobFolderWithPermissionList();
        return ResponseEntity.ok(folderListDTO);
    }


    /***
     * get shared team list of these folders shared to me
     * @return return the team list under the job folder
     */
    @ApiOperation(value = "get shared team list for job folder by user")
    @GetMapping(value = "/shared-folders/shared-teams")
    public ResponseEntity<List<FolderSharedTeamDTO>> getJobFolderSharedTeamsForShared() {
        log.info("APN JobFolder REST request to get shared team list for Job folder by user:  {} and tenant {} ", SecurityUtils.getUserId(), SecurityUtils.getTenantId());
        List<FolderSharedTeamDTO> jobFolderSharedTeams= jobFolderService.getDistinctSharedTeamsForSharedFolderByUserId(SecurityUtils.getUserId());
        return ResponseEntity.ok(jobFolderSharedTeams);
    }

    /***
     * get shared user list of these folders shared to me
     * @return list of shared user under current user folder
     */
    @ApiOperation(value = "get shared user list for job folder by user")
    @GetMapping(value = "/shared-folders/shared-users")
    public ResponseEntity<List<FolderSharedUserDTO>> getJobFolderSharedUsersForShared() {
        log.info("({},{}) REST request to get shared team list for Job folder by user:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), SecurityUtils.getUserId());
        List<FolderSharedUserDTO> jobFolderSharedUsers= jobFolderService.getDistinctSharedUsersForSharedFolderByUserId(SecurityUtils.getUserId());
        return ResponseEntity.ok(jobFolderSharedUsers);
    }


    /**
     * Search Folder module
     * */

    /***
     * get Job Search Folder
     * @param pageable
     * @return list of the job search folder
     * @throws Throwable
     */
    @ApiOperation(value = "get job search folder")
    @GetMapping(value = "/search-folders")
    public ResponseEntity<List<JobSearchFolderDTO>> getJobSearchFolder(@ApiParam Pageable pageable) throws Throwable {
        log.info("[APN] Job Search Folder: REST request to get Job Search folder tenat:{} and  user: {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        Page<JobSearchFolderDTO> jobSearchCriteriaPage= jobSearchFolderService.getAllJobSearchFolders(pageable, SecurityUtils.getUserId());
        return ResponseEntity.ok(jobSearchCriteriaPage.getContent());
    }


    /***
     * create a new job search folder
     * @param jobSearchFolderDTO
     * @return
     * @throws Throwable
     */
    @ApiOperation(value = "create a job search folder")
    @PostMapping(value = "/search-folders")
    @NoRepeatSubmit
    public ResponseEntity<JobSearchFolderDTO> addJobSearchFolder(@RequestBody @Valid JobSearchFolderDTO jobSearchFolderDTO) throws Throwable {
        log.info("APN Job Search Folder ({},{}) REST request to add a new Job Search folder:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), jobSearchFolderDTO);
        JobSearchFolderDTO result  = jobSearchFolderService.createJobSearchFolder(jobSearchFolderDTO);
        return ResponseEntity.ok(result);
    }

    /***
     * delete a search folder
     * @param folderId
     * @return
     */
    @ApiOperation(value = "Delete a job search folder")
    @DeleteMapping("/search-folders/{folderId}")
    public ResponseEntity<Void> deleteJobSearchFolder(@PathVariable Long folderId) {
        log.info("[APN] Job Search Folder({},{}) REST request to Delete Job Search folder:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), folderId);

        jobSearchFolderService.deleteJobSearchFolder(folderId);
        return ResponseEntity.ok().build();
    }

    /***
     * delete a search folder
     * @param folderId
     * @return
     */
    @ApiOperation(value = "get a job search folder")
    @GetMapping("/search-folders/{folderId}")
    public ResponseEntity<JobSearchFolderDTO> getJobSearchFolder(@PathVariable Long folderId) {
        log.info("[APN] Job Search Folder({},{}) REST request to get a Job Search folder:  {} ", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), folderId);
        return ResponseEntity.ok(jobSearchFolderService.getJobSearchFolder(folderId));
    }

    @PostMapping("/search-folders/search")
    public ResponseEntity<List<JobSearchFolderStatusDTO>> findSearchJobFolders(@Valid @RequestBody FolderSearchRequestDTO folderSearchRequestDTO, @ApiParam Pageable pageable) {
        log.info("[APN: Job @{}] REST request to search job folder :{}", SecurityUtils.getUserId(), pageable);
        Page<JobSearchFolderStatusDTO> jobFolderDetails = jobSearchFolderService.findSearchFolders(folderSearchRequestDTO, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(jobFolderDetails, "/api/v3/search-folders/search");
        return new ResponseEntity<>(jobFolderDetails.getContent(), headers, HttpStatus.OK);
    }

}
