package com.altomni.apn.job.service.dto.job;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


@Data
public class JobNoteKeywordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "job id")
    private Long jobId;

    @ApiModelProperty(value = "keyword in job note to search")
    private String noteKeyword;
}
