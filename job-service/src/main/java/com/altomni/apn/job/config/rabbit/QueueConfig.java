package com.altomni.apn.job.config.rabbit;

import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

@Configuration
@RefreshScope
public class QueueConfig {

    @Resource(name = "emailRabbitAdmin")
    private RabbitAdmin emailRabbitAdmin;

    @Value("${application.email-mq.exchange}")
    private String exchange;

    @Value("${application.email-mq.queue}")
    private String queue;

    @Value("${application.email-mq.routing-key}")
    private String routingKey;

    @PostConstruct
    public void emailRabbitInit() {
        //声明交换机
        emailRabbitAdmin.declareExchange(new DirectExchange(exchange));

        //声明队列
        emailRabbitAdmin.declareQueue(new Queue(queue, true));

        //绑定队列及交换机
        emailRabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(queue, true)).to(new DirectExchange(exchange)).with(routingKey));
    }


}
