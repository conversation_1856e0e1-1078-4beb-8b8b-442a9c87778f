package com.altomni.apn.job.service.dto.job;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.SkillDTO;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.job.AssignedUserDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@ApiModel
public class JobThirdPartyDTO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4221709952568105022L;

    @ApiModelProperty(value = "The tenant id the job belongs. It is injected from the user entity when creating job. Read only.")
    private Long tenantId;

    @ApiModelProperty(value = "id of the company publish the job")
    private Long companyId;

    private String department;

    @ApiModelProperty(value = "The title for the position")
    private String title;

    @ApiModelProperty(value = "the recruitment process id for current job")
    private Long recruitmentProcessId;

    @ApiModelProperty(value = "Job type", allowableValues = "DIRECT_PLACEMENT, CONTRACT, RIGHT_TO_HIRE, FULL_TIME, PART_TIME")
    private JobType jobType;

    private Integer currency;

    @ApiModelProperty(value = "This is job internal code company used to identify the job")
    private String code;

    @ApiModelProperty(value = "The date when the job starts")
    private Instant startDate;

    @ApiModelProperty(value = "The date when the job ends")
    private Instant endDate;

    @ApiModelProperty(value = "The date the job is posted to ATS or job boards. Read Only.")
    private Instant postingTime;

    @ApiModelProperty(value = "Job status. Default is open, the newly post job.", allowableValues = "OPEN, ONHOLD, CANCELLED, CLOSED")
    private JobStatus status;

    private String responsibilities;

    private String summary;

    private String requirements;

    @ApiModelProperty(value = "The url link to the JD. E.g. link to StoreService storage for the JD")
    private String jdUrl;


    @ApiModelProperty(value = "number of openings for the job. Default is 1.")
    private Integer openings;

    @ApiModelProperty(value = "max application submissions allowed for the job. Default is 0, means no limit.")
    private Integer maxSubmissions;


    private List<LocationDTO> locations;

    /*
      billRange;

      salaryRange;

      payType;
    */
    private JSONObject additionalInformation;

    //additional info

    private List<SkillDTO> requiredSkills;

    private List<SkillDTO> preferredSkills;

            //client contact
    private Long hiringManagerId;

    @ApiModelProperty(value = "The group of assigned user ids, one or more user role ")
    private List<AssignedUserDTO> assignedUsers;

    public static JobV3 fromDto(JobThirdPartyDTO jobDTO) {
        JobV3 job = new JobV3();
        ServiceUtils.myCopyProperties(jobDTO, job);
        String translateAdditionalInfo = generateExtendedInfo(jobDTO);
        JSONObject additionalInfoJson = new JSONObject();

        if (ObjectUtil.isNotEmpty(jobDTO.getAdditionalInformation())) {
            additionalInfoJson.putAll(jobDTO.getAdditionalInformation());
        }

        if (ObjectUtil.isNotEmpty(translateAdditionalInfo)) {
            additionalInfoJson.putAll(JSON.parseObject(translateAdditionalInfo));
        }
        job.setJobExtendedInfo(JSON.toJSONString(additionalInfoJson));
//        job.setJD(jobDTO.responsibilities, jobDTO.summary, jobDTO.requirements);

        return job;
    }

    public static void updateCopy(JobThirdPartyDTO jobDTO, JobV3 job) {
        ServiceUtils.myCopyProperties(jobDTO, job, JobV3.UpdateSkipProperties);
        String translateAdditionalInfo = generateExtendedInfo(jobDTO);
        JSONObject additionalInfoJson = new JSONObject();

        if (ObjectUtil.isNotEmpty(jobDTO.getAdditionalInformation())) {
            additionalInfoJson.putAll(jobDTO.getAdditionalInformation());
        }

        if (ObjectUtil.isNotEmpty(translateAdditionalInfo)) {
            additionalInfoJson.putAll(JSON.parseObject(translateAdditionalInfo));
        }
        job.setJobExtendedInfo(JSON.toJSONString(additionalInfoJson));
    }

    public static JobThirdPartyDTO getNonRelationData(JobThirdPartyDTO dto) {
        //set extented info
        JobThirdPartyDTO nonRelationData = new JobThirdPartyDTO();
        nonRelationData.setPreferredSkills(dto.getPreferredSkills());
        nonRelationData.setRequiredSkills(dto.getRequiredSkills());
        nonRelationData.setDepartment(dto.getDepartment());
        nonRelationData.setSummary(dto.getSummary());
        nonRelationData.setRequirements(dto.getRequirements());
        nonRelationData.setResponsibilities(dto.getResponsibilities());

        nonRelationData.setCreatedDate(null);
        nonRelationData.setLastModifiedDate(null);
        return nonRelationData;
    }

    public static String generateExtendedInfo(JobThirdPartyDTO dto) {
        JobThirdPartyDTO nonRelationData = JobThirdPartyDTO.getNonRelationData(dto);
        String extendedInfo = com.alibaba.fastjson.JSONObject.toJSONString(nonRelationData, (PropertyFilter) (o, s, o1) -> ObjectUtil.isNotEmpty(o1));
        return extendedInfo;
    }
}
