package com.altomni.apn.job.domain.job;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Data
@Table(name = "job_folder_relation")
public class JobFolderRelation extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "job_id", nullable = false)
    private Long jobId;

    @Column(name = "job_folder_id", nullable = false)
    private Long jobFolderId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getJobId() {
        return jobId;
    }

    public void setJobId(Long jobId) {
        this.jobId = jobId;
    }

    public Long getJobFolderId() {
        return jobFolderId;
    }

    public void setJobFolderId(Long jobFolderId) {
        this.jobFolderId = jobFolderId;
    }



}
