package com.altomni.apn.job.service.mapper.jobFolder;

import com.altomni.apn.job.domain.job.JobFolder;
import com.altomni.apn.job.service.dto.folder.JobFolderDTO;
import com.altomni.apn.job.service.mapper.EntityMapper;
import org.mapstruct.Mapper;


@Mapper(componentModel = "spring", uses = {})
public interface JobFolderMapper extends EntityMapper<JobFolderDTO, JobFolder> {
    default JobFolder fromId(Long id) {
        if (id == null) {
            return null;
        }
        JobFolder jobFolder = new JobFolder();
        jobFolder.setId(id);
        return jobFolder;
    }




}