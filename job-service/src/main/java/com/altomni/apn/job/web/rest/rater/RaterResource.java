package com.altomni.apn.job.web.rest.rater;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.service.dto.redis.RecommendedTalentResponse;
import com.altomni.apn.job.service.dto.redis.SimilarJobResponse;
import com.altomni.apn.job.service.rater.RaterService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.ExecutionException;

@RestController
@RequestMapping("/api/v3")
public class RaterResource {

    private final Logger log = LoggerFactory.getLogger(RaterResource.class);

    @Resource
    private RaterService raterService;

    @GetMapping("/recommend-common-talents/jobId/{jobId}")
    public ResponseEntity<RecommendedTalentResponse> recommendCommonTalentsForJob(@PathVariable Long jobId, @RequestParam(name = "refresh", defaultValue = "false") boolean refresh, @PageableDefault(value = 1000) Pageable pageable) {
        log.info("[APN: Rater@{}] REST request to recommend common talents for job: {}", SecurityUtils.getUserId(), jobId);
        return new ResponseEntity<>(raterService.recommendCommonTalentsForJob(jobId, pageable, refresh), HttpStatus.OK);
    }

    @GetMapping("/recommend-tenant-talents/jobId/{jobId}")
    public ResponseEntity<RecommendedTalentResponse> recommendTenantTalentsForJob(@PathVariable Long jobId, @RequestParam(name = "refresh", defaultValue = "false") boolean refresh, @PageableDefault(value = 1000) Pageable pageable){
        log.info("[APN: Rater@{}] REST request to recommend tenant talents for job: {}", SecurityUtils.getUserId(), jobId);
        return new ResponseEntity<>(raterService.recommendTenantTalentsForJob(jobId, pageable, refresh), HttpStatus.OK);
    }

    @GetMapping("/recommend-all-talents/jobId/{jobId}")
    public ResponseEntity<RecommendedTalentResponse> recommendAllTalentsForJob(@PathVariable Long jobId, @RequestParam(name = "refresh", defaultValue = "false") boolean refresh, @PageableDefault(value = 1000) Pageable pageable){
        log.info("[APN: Rater@{}] REST request to recommend all talents for job: {}", SecurityUtils.getUserId(), jobId);
        return new ResponseEntity<>(raterService.recommendAllTalentsForJob(jobId, pageable, refresh), HttpStatus.OK);
    }

    @GetMapping("/similar-jobs/jobId/{jobId}")
    public ResponseEntity<SimilarJobResponse> getSimilarJobs(@PathVariable Long jobId, Pageable pageable) throws IOException, ExecutionException, InterruptedException {
        log.info("[APN: Rater@{}] REST request to get similar jobs : {}", SecurityUtils.getUserId(), jobId);
        return new ResponseEntity<>(raterService.getSimilarJobsByJobId(jobId, pageable), HttpStatus.OK);
    }
}