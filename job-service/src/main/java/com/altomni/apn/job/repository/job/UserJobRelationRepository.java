package com.altomni.apn.job.repository.job;

import com.altomni.apn.job.domain.job.UserJobRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.List;


/**
 * Spring Data JPA repository for the UserJobRelation entity.
 */
@Repository
public interface UserJobRelationRepository extends JpaRepository<UserJobRelation,Long> {

    List<UserJobRelation> findAllByJobId(Long jobId);

    List<UserJobRelation> findAllByUserIdAndStatusIsTrue(Long userId);

    List<UserJobRelation> findAllByJobIdAndStatusIsTrue(Long jobId);

//    void deleteAllByJobId(Long jobId);

    @Modifying
    @Query("UPDATE UserJobRelation SET status = false, lastModifiedDate = CURRENT_TIMESTAMP WHERE jobId = :jobId")
    void disableByJobId(@Param("jobId") Long jobId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE user_job_relation SET status = 0, last_modified_date = CURRENT_TIMESTAMP WHERE id in :ids ",nativeQuery = true)
    void disableNativeByIds(@Param("ids") List<Long> ids);

    @Modifying
    @Transactional
    @Query(value = "UPDATE user_job_relation SET status = :status, last_modified_date = CURRENT_TIMESTAMP WHERE id in :ids ",nativeQuery = true)
    void updateStatusByIds(@Param("ids") List<Long> ids,@Param("status") Integer status);

//    @Query("SELECT uj.userId FROM UserJobRelation uj WHERE uj.jobId = :jobId")
//    List<Long> getParticipantIdsByJobId(@Param("jobId") Long jobId);

    @Query("SELECT uj.userId FROM UserJobRelation uj WHERE uj.jobId = :jobId AND uj.status = TRUE")
    List<Long> getParticipantIdsByJobId(@Param("jobId") Long jobId);

//    UserJobRelation findFirstByJobIdAndUserId(Long jobId, Long userId);

    UserJobRelation findFirstByJobIdAndUserIdAndStatusIsTrue(Long jobId, Long userId);

//    @Query(value = "SELECT * FROM user_job_relation WHERE permission & ?1 AND job_id = ?2 LIMIT 1", nativeQuery = true)
//    UserJobRelation findFirstByPermissionAndJobId(Integer permission, Long jobId);
//
//    @Query(value = "SELECT * FROM user_job_relation WHERE user_id = ?1 AND job_id = ?2 order by permission desc LIMIT 1", nativeQuery = true)
//    UserJobRelation findFirstByUserIdAndJobId(Long userId, Long jobId);
//
//    List<UserJobRelation> findAllByJobIdAndPermission(Long jobId, Integer permission);
//
//    @Query(value = "select count(ujr.id) from user_job_relation ujr left join talent_recruitment_process a on a.job_id = ujr.job_id " +
//            " where a.id = ?1 and ujr.user_id = ?2 and ujr.permission in (?3) ",nativeQuery = true)
//    Integer countByTalentRecruitmentProcessIdUserIdAndPermissionIn(Long talentRecruitmentProcessId, Long userId, List<Integer> permissionList);
}
