package com.altomni.apn.job.service.dto.folder;

import com.altomni.apn.common.enumeration.folder.FolderPermission;
import lombok.Data;

@Data
public class SharedFolderBriefDTO {
    private Long id;
    private String name;
    private FolderPermission permission;
    private Long count;

    public SharedFolderBriefDTO(Long id, String name, FolderPermission permission, Long count) {
        this.id = id;
        this.name = name;
        this.permission = permission;
        this.count = count;
    }
}
