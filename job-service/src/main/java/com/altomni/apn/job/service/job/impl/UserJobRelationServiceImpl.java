//package com.altomni.apn.job.service.job.impl;
//
//import cn.hutool.core.collection.CollUtil;
//import com.altomni.apn.common.domain.enumeration.job.JobPermission;
//import com.altomni.apn.common.dto.http.HttpResponse;
//import com.altomni.apn.common.errors.CustomParameterizedException;
//import com.altomni.apn.common.errors.DuplicateException;
//import com.altomni.apn.common.utils.SecurityUtils;
//import com.altomni.apn.common.utils.ServiceUtils;
//import com.altomni.apn.common.domain.job.JobV3;
//import com.altomni.apn.job.domain.job.UserJobRelation;
//import com.altomni.apn.job.repository.job.JobRepository;
//import com.altomni.apn.job.repository.job.UserJobRelationRepository;
//import com.altomni.apn.job.service.dto.job.UserJobRelationDTO;
//import com.altomni.apn.job.service.elastic.EsFillerJobService;
//import com.altomni.apn.job.service.job.UserJobRelationService;
//import com.altomni.apn.job.service.mail.MailGenerateService;
//import com.altomni.apn.job.service.user.UserService;
//import com.altomni.apn.job.web.rest.vm.MessageVM;
//import com.altomni.apn.job.web.rest.vm.UserJobRelationVM;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.ListUtils;
//import org.apache.commons.lang3.ObjectUtils;
//import org.springframework.http.HttpStatus;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import javax.annotation.Resource;
//import java.io.IOException;
//import java.util.*;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Service
//public class UserJobRelationServiceImpl implements UserJobRelationService {
//
//    @Resource
//    private UserJobRelationRepository userJobRelationRepository;
//    @Resource
//    private EsFillerJobService esFillerJobService;
//    @Resource
//    private MailGenerateService mailGenerateService;
//    @Resource
//    private JobRepository jobRepository;
//    @Resource
//    private UserService userService;
//
//
//    @Transactional
//    @Override
//    public UserJobRelationDTO create(UserJobRelation relation) throws IOException {
//        if (relation.getJobId() == null) {
//            throw new CustomParameterizedException("Job id is required to create a user job relation");
//        }
//        UserJobRelation foundRelation = userJobRelationRepository.findFirstByUserIdAndJobId(relation.getUserId(), relation.getJobId());
//        if (foundRelation != null) {
//            throw new DuplicateException("User Job Relation has already existed");
//        }
//        if (relation.getPermission() != null) {
//            canChangePermission(relation);
//        }
//        //not allow to change other people's stuff
//        protectRelation(relation, null);
//        UserJobRelation result = userJobRelationRepository.saveAndFlush(relation);
//        syncJob(result);
//        // Send email to inform relation
//        Long jobId = result.getJobId();
//        JobV3 job = jobRepository.findById(jobId).get();
//        List<String> emailAddressList = new ArrayList<>();
//        emailAddressList.add(userService.findById(result.getUserId()).getBody().getEmail());
//        mailGenerateService.sendHtmlMailForUserJobRelationFromSupport(emailAddressList, job);
//        return addUserEntity(UserJobRelationDTO.fromUserJobRelation(result));
//    }
//
//    private UserJobRelationDTO addUserEntity(UserJobRelationDTO userJobRelationDTO) {
//        if (userJobRelationDTO != null && userJobRelationDTO.getUserId() != null) {
//            userJobRelationDTO.setUser(userService.findById(userJobRelationDTO.getUserId()).getBody());
//        }
//        return userJobRelationDTO;
//    }
//
//    private UserJobRelationDTO addAllEntity(UserJobRelationDTO userJobRelationDTO) {
//        if (userJobRelationDTO != null) {
//            userJobRelationDTO.setPermissionSet(JobPermission.parseJobPermission(userJobRelationDTO.getPermission()));
//            if (userJobRelationDTO.getJobId() != null) {
//                userJobRelationDTO.setJob(jobRepository.findById(userJobRelationDTO.getJobId()).get());
//            }
//            if (userJobRelationDTO.getUserId() != null) {
//                userJobRelationDTO.setUser(userService.findById(userJobRelationDTO.getUserId()).getBody());
//            }
//        }
//        return userJobRelationDTO;
//    }
//
//    @Transactional
//    @Override
//    public List<UserJobRelationDTO> create(List<UserJobRelation> userJobRelations) throws IOException {
//        List<UserJobRelationDTO> result = new ArrayList<>();
//        List<UserJobRelation> notNullRelations = userJobRelations.stream().filter(relation -> userJobRelationRepository.findFirstByUserIdAndJobId(relation.getUserId(), relation.getJobId()) == null).collect(Collectors.toList());
//        Set<Long> jobs = new HashSet<>();
//        for (UserJobRelation relation : notNullRelations) {
//            if (relation.getJobId() == null) {
//                throw new CustomParameterizedException("Job id is required to create a user job relation");
//            }
//            if (relation.getPermission() != null) {
//                canChangePermission(relation);
//            }
//            //not allow to change other people's stuff
//            protectRelation(relation, null);
//            jobs.add(relation.getJobId());
//        }
//        userJobRelations = userJobRelationRepository.saveAll(notNullRelations);
//        for (Long jobId : jobs) {
//            syncJob(jobId);
//        }
//
//        // Send email to inform relations
//        List<String> emailAddressList = new ArrayList<>();
//        for (UserJobRelation relation : userJobRelations) {
//            // Get email list from all the new relation/user
//            emailAddressList.add(userService.findById(relation.getUserId()).getBody().getEmail());
//            result.add(addUserEntity(UserJobRelationDTO.fromUserJobRelation(relation)));
//        }
//
//        UserJobRelation relation = userJobRelations.iterator().next();
//        Long jobId = relation.getJobId();
//        JobV3 job = jobRepository.findById(jobId).get();
//        mailGenerateService.sendHtmlMailForUserJobRelationFromSupport(emailAddressList, job);
//        return result;
//    }
//
//    private void syncJob(Long jobId) throws IOException {
//        HttpResponse response = esFillerJobService.syncJobToEs(jobId);
//        if (response != null) {
//            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
//                log.info("[APN: UserJobRelationService @{}] Update job to EsFiller error, id: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response.getCode(), response.getBody());
//                throw new CustomParameterizedException("Update job error.");
//            }
//        } else {
//            log.info("[APN: UserJobRelationService @{}] Update job to EsFiller error and response is null, id: {}", SecurityUtils.getUserId(), response);
//            throw new CustomParameterizedException("Update job error.");
//        }
//    }
//
//    @Transactional
//    @Override
//    public List<UserJobRelationDTO> upsert(List<UserJobRelation> userJobRelations) throws IOException {
//        if (CollUtil.isEmpty(userJobRelations)) {
//            throw new CustomParameterizedException("User Job Relation is Empty");
//        }
//
//        Long jobId = userJobRelations.get(0).getJobId();
//        if (jobId == null) {
//            throw new CustomParameterizedException("Job id is required to create a user job relation");
//        }
//
//        if (userJobRelations.stream().map(UserJobRelation::getJobId).collect(Collectors.toSet()).size() > 1) {
//            throw new CustomParameterizedException("We are only support one job for for every single request");
//        }
//
//        List<Long> userIdList = userJobRelations.stream().map(UserJobRelation::getUserId).collect(Collectors.toList());
//        long userSize = userIdList.stream().distinct().count();
//        if (userSize < userIdList.size()) {
//            throw new CustomParameterizedException("We are only support one user for every single request.");
//        }
//
//        UserJobRelation myRelation = userJobRelationRepository.findFirstByUserIdAndJobId(SecurityUtils.getUserId(), jobId);
//        if (myRelation != null) {
//            //current login user already exists in user job relation group
//            boolean updateListContainsMe = false;
//            for (UserJobRelation u : userJobRelations) {
//                if (u.getPermission() == null) {
//                    throw new CustomParameterizedException("No permission value need to set for user " + u.getUserId());
//                }
//                if (Objects.equals(u.getUserId(), myRelation.getUserId())) {
//                    updateListContainsMe = true;
//                }
//
//                if (u.getId() != null) {
//                    // already exist, the permission may updated
//                    UserJobRelation dbRelation = userJobRelationRepository.findById(u.getId()).orElse(null);
//                    if (dbRelation == null) {
//                        throw new CustomParameterizedException("We can't find user job relation with id: " + u.getId());
//                    }
//                    boolean permissionHasChanged = !Objects.equals(u.getPermission(), dbRelation.getPermission());
//                    boolean oneOfPermissionIsGreaterThanMine = (JobPermission.greaterThan(u.getPermission(), myRelation.getPermission()) || JobPermission.greaterThan(dbRelation.getPermission(), myRelation.getPermission()));
//
//                    if (permissionHasChanged && oneOfPermissionIsGreaterThanMine && !SecurityUtils.isAdmin()) {
//                        // permission has been updated and one of update permission is greater than my permission
//                        throw new CustomParameterizedException("You are not authorized to grant permission which is higher than yourself");
//                    }
//                } else {
//                    //create new
//                    if (JobPermission.greaterThan(u.getPermission(), myRelation.getPermission()) && !SecurityUtils.isAdmin()) {
//                        throw new CustomParameterizedException("You are not authorized to grant permission which is higher than yourself");
//                    }
//                }
//            }
//            if (!updateListContainsMe) {
//                throw new CustomParameterizedException("You are not authorized to remove yourself from the list.");
//            }
//        } else {
//            //current login user not exists in user job relation group
//            if (!SecurityUtils.isAdmin()) {
//                throw new CustomParameterizedException("You are not authorized to grant permission");
//            }
//        }
//        UserJobRelation dbAm = userJobRelationRepository.findFirstByPermissionAndJobId(JobPermission.AM.value, jobId);
//        List<UserJobRelation> dbUserJobRelations = userJobRelationRepository.findAllByJobId(jobId);
//        List<Long> dbUserIds = dbUserJobRelations.stream().map(UserJobRelation::getUserId).collect(Collectors.toList());
//        List<Long> newAddedUserIds = ListUtils.removeAll(userIdList, dbUserIds);
//
//        userJobRelationRepository.deleteAllByJobId(jobId);
//        List<UserJobRelation> save = userJobRelationRepository.saveAll(userJobRelations);
//        syncJob(jobId);
//        UserJobRelation newAm = userJobRelationRepository.findFirstByPermissionAndJobId(JobPermission.AM.value, jobId);
//        List<String> emailAddressList = new ArrayList<>();
//        if (ObjectUtils.equals(dbAm.getUserId(), newAm.getUserId())) {
//            if (CollUtil.isNotEmpty(newAddedUserIds)) {
//                for (Long userId : newAddedUserIds) {
//                    emailAddressList.add(userService.findById(userId).getBody().getEmail());
//                }
//            }
//        } else {
//            for (Long userId : userJobRelationRepository.findAllByJobId(jobId).stream().map(UserJobRelation::getUserId).collect(Collectors.toList())) {
//                emailAddressList.add(userService.findById(userId).getBody().getEmail());
//            }
//        }
//        log.info("UserJobRelationService Add: {} on job: {}", emailAddressList, jobId);
//        JobV3 job = jobRepository.findById(jobId).get();
//        mailGenerateService.sendHtmlMailForUserJobRelationFromSupport(emailAddressList, job);
//
//        List<UserJobRelationDTO> result = new ArrayList<>();
//        for (UserJobRelation relation : save) {
//            result.add(addUserEntity(UserJobRelationDTO.fromUserJobRelation(relation)));
//        }
//        return result;
//    }
//
//
//    /**
//     * create multiRequests by jobID and UserID list.
//     */
//    @Transactional
//    @Override
//    public List<MessageVM<UserJobRelationDTO>> multiUserJobRelations(UserJobRelationVM userJobRelationVM) {
//        List<Long> userIdSet = userJobRelationVM.getUserId();
//        List<Long> jobIdSet = userJobRelationVM.getJobId();
//        List<String> permissionSet = userJobRelationVM.getPermissionSet();
//        Long myId = SecurityUtils.getUserId();
//        if (CollUtil.isEmpty(userIdSet) || CollUtil.isEmpty(jobIdSet) || CollUtil.isEmpty(permissionSet)) {
//            throw new CustomParameterizedException("UserIDs, jobIds and permissionSet are all required.");
//        }
//
//        List<MessageVM<UserJobRelationDTO>> result = new ArrayList<>();
//        long userSize = userIdSet.stream().distinct().count();
//        long jobSize = jobIdSet.stream().distinct().count();
//        Long permissionSetSize = permissionSet.stream().distinct().count();
//        if (userSize < userIdSet.size() || jobSize < jobIdSet.size() || permissionSetSize < permissionSet.size()) {
//            throw new CustomParameterizedException("User, Job and permission must be distinct.");
//        }
//
//        jobIdSet.forEach(j -> {
//            List<UserJobRelation> newRelationList = new ArrayList<>();
//            List<String> emailList = new ArrayList<>();
//            UserJobRelation myRelation = userJobRelationRepository.findFirstByUserIdAndJobId(myId, j);
//            if (myRelation == null) {
//                throw new CustomParameterizedException("You must have permission on this job.");
//            }
//            userIdSet.forEach(u -> {
//                UserJobRelation userJobRelation = new UserJobRelation();
//                userJobRelation.setJobId(j);
//                userJobRelation.setUserId(u);
//                userJobRelation.setPermissionSet(new HashSet<>(permissionSet));
//                if (JobPermission.greaterThan(userJobRelation.getPermission(), myRelation.getPermission())) {
//                    throw new CustomParameterizedException("You are not authorized to grant permission which is higher than yourself.");
//                }
//
//                UserJobRelation exist = userJobRelationRepository.findFirstByUserIdAndJobId(u, j);
//                if (exist != null) {
//                    result.add(new MessageVM<>(addUserEntity(UserJobRelationDTO.fromUserJobRelation(userJobRelation)), "This Relation is already exist."));
//                } else {
//                    newRelationList.add(userJobRelation);
//                    emailList.add(userService.findById(u).getBody().getEmail());
//                    result.add(new MessageVM<>(addUserEntity(UserJobRelationDTO.fromUserJobRelation(userJobRelation)), "Add this relation successfully."));
//                }
//            });
//            if (CollUtil.isNotEmpty(newRelationList)) {
//                log.info("UserJobRelationService Add: {} on job: {}", emailList, j);
//                userJobRelationRepository.saveAll(newRelationList);
//                JobV3 job = jobRepository.findById(j).get();
//                try {
//                    mailGenerateService.sendHtmlMailForUserJobRelationFromSupport(emailList, job);
//                } catch (Exception ex) {
//                    log.error("UserJobRelationService send email error: {}", ex.getMessage());
//                }
//            }
//        });
//
//        return result;
//
//    }
//
//    @Override
//    public UserJobRelationDTO saveSearchString(String searchString, Long jobId) {
//        UserJobRelation myRelation = userJobRelationRepository.findFirstByUserIdAndJobId(SecurityUtils.getUserId(), jobId);
//        if (myRelation == null) {
//            throw new CustomParameterizedException("Use is not associated with the job");
//        }
//        myRelation.setSearchString(searchString);
//        return addUserEntity(UserJobRelationDTO.fromUserJobRelation(userJobRelationRepository.save(myRelation)));
//    }
//
//    @Override
//    public UserJobRelationDTO update(UserJobRelation update) throws IOException {
//        UserJobRelation relation = userJobRelationRepository.findById(update.getId()).orElse(null);
//        if (relation == null) {
//            throw new CustomParameterizedException("The relation to update dose not exist");
//        }
//        if (update.getPermission() != null && !relation.getPermission().equals(update.getPermission())) {
//            //permission change
//            canChangePermission(update);
//        }
//        protectRelation(update, relation);
//        ServiceUtils.myCopyProperties(update, relation, UserJobRelation.UpdateSkipProperties);
//        relation = userJobRelationRepository.save(relation);
//        syncJob(relation);
//        return addUserEntity(UserJobRelationDTO.fromUserJobRelation(relation));
//    }
//
//    private void syncJob(UserJobRelation relation) throws IOException {
//        HttpResponse response = esFillerJobService.syncJobToEs(relation.getJobId());
//        if (response != null) {
//            if (!ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
//                log.info("[APN: UserJobRelationService @{}] Update job to EsFiller error, id: {}, response code: {}, response message: {}", SecurityUtils.getUserId(), response, response.getCode(), response.getBody());
//                throw new CustomParameterizedException("Update job error.");
//            }
//        } else {
//            log.info("[APN: UserJobRelationService @{}] Update job to EsFiller error and response is null, id: {}", SecurityUtils.getUserId(), response);
//            throw new CustomParameterizedException("Update job error.");
//        }
//    }
//
//    @Override
//    public UserJobRelationDTO findOne(Long id) {
//        UserJobRelation userJobRelation = userJobRelationRepository.findById(id).orElse(null);
//        return userJobRelation != null ? addAllEntity(UserJobRelationDTO.fromUserJobRelation(userJobRelation)) : null;
//    }
//
//    @Override
//    public UserJobRelationDTO getRelationByUserAndJob(Long userId, Long jobId) {
//        UserJobRelation userJobRelation = userJobRelationRepository.findFirstByUserIdAndJobId(userId, jobId);
//        return userJobRelation != null ? addAllEntity(UserJobRelationDTO.fromUserJobRelation(userJobRelation)) : null;
//    }
//
//    @Override
//    public void delete(UserJobRelation userJobRelation) {
//        UserJobRelation relation = userJobRelationRepository.findFirstByUserIdAndJobId(userJobRelation.getUserId(), userJobRelation.getJobId());
//        if (relation == null) {
//            throw new CustomParameterizedException("The relation to delete dose not exist");
//        }
//        canChangePermission(relation);
//        userJobRelationRepository.delete(relation);
//    }
//
//    private void canChangePermission(UserJobRelation relation) {
//        if (relation.getPermission() >= JobPermission.AM.value) {
//            throw new CustomParameterizedException("Can not assign owner role");
//        }
//        UserJobRelation myRelation = userJobRelationRepository.findFirstByUserIdAndJobId(SecurityUtils.getUserId(), relation.getJobId());
//        if (myRelation == null || myRelation.getPermission() < JobPermission.PRIMARY_RECRUITER.value) {
//            throw new CustomParameterizedException("You are not allowed to assign job to user");
//        }
//        if (myRelation.getId().equals(relation.getId())) {
//            throw new CustomParameterizedException("You are not allowed to change your permission");
//        }
//    }
//
//
//    /**
//     * Check if current user is the owner for relation to save, dose not allow to change searchString if is not owner
//     *
//     * @param newRelation The one passed in to create / update
//     * @param existing    The existing relation, it is null for creating
//     */
//    private void protectRelation(UserJobRelation newRelation, UserJobRelation existing) {
//        Long userId;
//        if (existing == null) {
//            //this is create a new relation, userId is required
//            userId = newRelation.getUserId();
//        } else { // this is updating a relation, get userId from existing
//            userId = existing.getUserId();
//        }
//        if (!userId.equals(SecurityUtils.getUserId())) {
//            // current user dose not own the relation
//            newRelation.setSearchString(null);
//        }
//    }
//
//    @Override
//    public List<UserJobRelationDTO> getRelationByJobId(Long jobId) {
//        List<UserJobRelation> userJobRelationList = userJobRelationRepository.findAllByJobId(jobId);
//        List<UserJobRelationDTO> userJobRelationDTOList = new ArrayList<>();
//        if (CollUtil.isNotEmpty(userJobRelationList)) {
//            for (UserJobRelation userJobRelation : userJobRelationList) {
//                userJobRelationDTOList.add(addAllEntity(UserJobRelationDTO.fromUserJobRelation(userJobRelation)));
//            }
//        }
//        return userJobRelationDTOList;
//    }
//
//    @Override
//    public UserJobRelation findFirstByPermissionAndJobId(Integer permission, Long jobId) {
//        return userJobRelationRepository.findFirstByPermissionAndJobId(permission, jobId);
//    }
//
//}
