syntax = "proto3";
import "google/protobuf/struct.proto";
package gpu_service;
option java_package = "gpu_service";
option java_outer_classname = "GpuProto";
option java_multiple_files = false;

service GpuService {

  rpc Readiness(Empty) returns (String) {}

  rpc KeywordExtractorApi(KeywordExtractorRequest) returns (KeywordExtractorResponse) {}

}
message Empty{}

message String{
  string data = 1;
}

message KeywordExtractorRequest {
  bytes input_ids = 1;
  bytes attention_mask = 2;
  string text = 3;
}

message KeywordExtractorResponse {
  bytes token_ids = 1;
  string response = 2;
}
