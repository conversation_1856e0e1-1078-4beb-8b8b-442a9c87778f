package com.altomni.apn.job.test.rest.job;

import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.job.test.common.job.JobCommon;
import com.altomni.apn.job.web.rest.job.JobResource;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class JobResourceMockTest {
    @Mock
    private JobResource jobResource;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateJob() throws Exception {
//        JobDTOV3 jobDTOV3 = JobCommon.genTestData();
//        Mockito.when(jobResource.createJob(jobDTOV3)).thenReturn(new ResponseEntity<>(jobDTOV3, HttpStatus.OK));
//        ResponseEntity<JobDTOV3> response = jobResource.createJob(jobDTOV3);
//
//        Assertions.assertThat(response.getBody().getId()).isEqualTo(jobDTOV3.getId());
    }
}
