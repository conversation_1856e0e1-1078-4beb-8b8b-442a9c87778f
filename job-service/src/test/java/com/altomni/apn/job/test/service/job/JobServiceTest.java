package com.altomni.apn.job.test.service.job;

import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.job.service.job.JobService;
import com.altomni.apn.job.test.common.job.JobCommon;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;

import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class JobServiceTest
{
    @Mock
    private JobService jobService;
    
    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    public void testFormatAndSave() throws IOException {
//        JobDTOV3 jobDTOV3 = JobCommon.genTestData();
//        when(jobService.formatAndSave(jobDTOV3)).thenReturn(jobDTOV3);
//        JobDTOV3 result = jobService.formatAndSave(jobDTOV3);
//
//        Assertions.assertThat(result.getId()).isEqualTo(jobDTOV3.getId());
    }
}
