package com.altomni.apn.job.test.repository.job;

import com.altomni.apn.common.domain.job.JobV3;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.job.repository.job.JobRepository;
import com.altomni.apn.job.test.common.job.JobCommon;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit4.SpringRunner;

import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class JobRepositoryTest {

  @Mock
  private JobRepository jobRepository;

  @BeforeEach
  public void init() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testSave() {
    JobDTOV3 jobDTOV3 = JobCommon.genTestData();
    JobV3 jobV3 = new JobV3();
    jobV3.setId(jobDTOV3.getId());
    when(jobRepository.save(jobV3)).thenReturn(jobV3);
    JobV3 result = jobRepository.save(jobV3);

    Assertions.assertThat(result.getId()).isEqualTo(jobV3.getId());
  }
}
