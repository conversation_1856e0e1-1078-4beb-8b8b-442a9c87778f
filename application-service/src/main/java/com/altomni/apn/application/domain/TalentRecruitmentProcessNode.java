package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeStatusConverter;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentRecruitmentProcessNode.
 */
@Entity
@Table(name = "talent_recruitment_process_node")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessNode extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -6069022257970557790L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "node_id", updatable = false)
    private Long nodeId;

    @Column(name = "node_type", updatable = false)
    @Convert(converter = NodeTypeConverter.class)
    private NodeType nodeType;

    @Column(name = "node_status")
    @Convert(converter = NodeStatusConverter.class)
    private NodeStatus nodeStatus;

    @Column(name = "next_node_id", updatable = false)
    private Long nextNodeId;


}
