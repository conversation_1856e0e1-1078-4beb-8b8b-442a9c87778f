package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.NodePageSection;
import com.altomni.apn.common.domain.enumeration.application.NodePageSectionConverter;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A RecruitmentProcessNodePageSection.
 */
@Entity
@Table(name = "recruitment_process_node_page_section")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitmentProcessNodePageSection extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5070679567300167427L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "recruitment_process_id")
    private Long recruitmentProcessId;

    @Column(name = "tenant_id", updatable = false)
    private Long tenantId;

    @Column(name = "node_type")
    @Convert(converter = NodeTypeConverter.class)
    private NodeType nodeType;

    /**
     * jobType整改
     */
    @Convert(converter = JobTypeConverter.class)
    @Column(name = "job_type")
    private JobType jobType;

    @Column(name = "description")
    private String description;

    @Column(name = "field_config")
    private String fieldConfig;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecruitmentProcessId() {
        return recruitmentProcessId;
    }

    public void setRecruitmentProcessId(Long recruitmentProcessId) {
        this.recruitmentProcessId = recruitmentProcessId;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    public NodeType getNodeType() {
        return nodeType;
    }

    public void setNodeType(NodeType nodeType) {
        this.nodeType = nodeType;
    }

    public JobType getJobType() {
        return jobType;
    }

    public void setJobType(JobType jobType) {
        this.jobType = jobType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFieldConfig() {
        return fieldConfig;
    }

    public void setFieldConfig(String fieldConfig) {
        this.fieldConfig = fieldConfig;
    }
}
