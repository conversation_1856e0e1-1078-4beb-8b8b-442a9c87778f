package com.altomni.apn.application.web.rest;

import com.altomni.apn.application.domain.RecruitmentProcessNode;
import com.altomni.apn.application.service.recruitmentprocess.RecruitmentProcessNodeService;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessNodeVO;
import com.altomni.apn.common.errors.BadRequestAlertException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing RecruitmentProcessNode.
 */
@RestController
@RequestMapping("/api/v3/recruitment-process-nodes")
public class RecruitmentProcessNodeResource {

    private final Logger log = LoggerFactory.getLogger(RecruitmentProcessNodeResource.class);

    private static final String ENTITY_NAME = "RecruitmentProcessNode";

    private final RecruitmentProcessNodeService recruitmentProcessNodeService;

    public RecruitmentProcessNodeResource(RecruitmentProcessNodeService recruitmentProcessNodeService) {
        this.recruitmentProcessNodeService = recruitmentProcessNodeService;
    }

//    /**
//     * PUT  /recruitment-process-nodes : Updates an existing recruitmentProcessNode.
//     *
//     * @param recruitmentProcessNode the recruitmentProcessNode to update
//     * @return the ResponseEntity with status 200 (OK) and with body the updated recruitmentProcessNode,
//     * or with status 400 (Bad Request) if the recruitmentProcessNode is not valid,
//     * or with status 500 (Internal Server Error) if the recruitmentProcessNode couldn't be updated
//     * @throws URISyntaxException if the Location URI syntax is incorrect
//     */
//    @PutMapping("/recruitment-process-nodes")
//    public ResponseEntity<RecruitmentProcessNode> updateRecruitmentProcessNode(@RequestBody RecruitmentProcessNode recruitmentProcessNode) throws URISyntaxException {
//        log.debug("REST request to update RecruitmentProcessNode : {}", recruitmentProcessNode);
//        if (recruitmentProcessNode.getId() == null) {
//            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "id null");
//        }
//        RecruitmentProcessNode result = recruitmentProcessNodeService.update(recruitmentProcessNode);
//        return ResponseEntity.ok()
//            .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, recruitmentProcessNode.getId().toString()))
//            .body(result);
//    }

    /**
     * PUT  /recruitment-process-nodes : Updates an existing recruitmentProcessNode.
     *
     * @param recruitmentProcessId the recruitment process id
     * @param nodeId node id
     * @param recruitmentProcessNode the recruitmentProcessNode to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated recruitmentProcessNode,
     * or with status 400 (Bad Request) if the recruitmentProcessNode is not valid,
     * or with status 500 (Internal Server Error) if the recruitmentProcessNode couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/recruitmentProcessId/{recruitmentProcessId}/nodeId/{nodeId}")
    public ResponseEntity<RecruitmentProcessNode> updateRecruitmentProcessNode(@PathVariable Long recruitmentProcessId, @PathVariable Long nodeId, @RequestBody RecruitmentProcessNode recruitmentProcessNode) throws URISyntaxException {
        log.info("REST request to update RecruitmentProcessNode : {}", recruitmentProcessNode);
//        if (recruitmentProcessNode.getId() == null) {
//            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "id null");
//        }
        RecruitmentProcessNode result = recruitmentProcessNodeService.update(recruitmentProcessId, nodeId, recruitmentProcessNode);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    /**
     * GET  /recruitment-process-nodes : get all the RecruitmentProcessNodes.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of RecruitmentProcessNodes in body
     */
    @GetMapping("/recruitmentProcessId/{recruitmentProcessId}")
    public ResponseEntity<List<RecruitmentProcessNodeVO>> getAllRecruitmentProcessNodes(@PathVariable Long recruitmentProcessId) {
        log.info("[RecruitmentProcessNodeResource: getAllRecruitmentProcessNodes @{}] REST request to get all RecruitmentProcessNodes by recruitmentProcessId: {}", SecurityUtils.getUserId(), recruitmentProcessId);
        return new ResponseEntity<>(recruitmentProcessNodeService.findAllByTenantId(recruitmentProcessId, SecurityUtils.getTenantId()), HttpStatus.OK);
    }

    @GetMapping("/admin/recruitmentProcessId/{recruitmentProcessId}")
    public ResponseEntity<List<RecruitmentProcessNodeVO>> getAllRecruitmentProcessNodesForAdmin(@PathVariable Long recruitmentProcessId) {
        log.info("[RecruitmentProcessNodeResource: getAllRecruitmentProcessNodes @{}] REST request to get all RecruitmentProcessNodes by recruitmentProcessId: {}", SecurityUtils.getUserId(), recruitmentProcessId);
        return new ResponseEntity<>(recruitmentProcessNodeService.findAll(recruitmentProcessId), HttpStatus.OK);
    }

}
