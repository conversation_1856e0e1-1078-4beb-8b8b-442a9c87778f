package com.altomni.apn.application.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class RaterAIRecommendMQProperties {

    @Value("${application.rater-ai-recommend.host}")
    private String host;

    @Value("${application.rater-ai-recommend.port}")
    private int port;

    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;

    @Value("${application.rater-ai-recommend.username}")
    private String userName;

    @Value("${application.rater-ai-recommend.password}")
    private String password;

}