package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessNodeVO;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A RecruitmentProcessNode.
 */
@Entity
@Table(name = "recruitment_process_node")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitmentProcessNode extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 3821512723147087243L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "recruitment_process_id", updatable = false)
    private Long recruitmentProcessId;

    @Column(name = "tenant_id", updatable = false)
    private Long tenantId;

    @Column(name = "node_type", updatable = false)
    @Convert(converter = NodeTypeConverter.class)
    private NodeType nodeType;

    @Column(name = "next_node_id")
    private Long nextNodeId;

    @Column(name = "name")
    private String name;

    @Column(name = "description")
    private String description;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("recruitmentProcessId", "tenantId", "nodeType", "nextNodeId"));

    public RecruitmentProcessNode(RecruitmentProcessNodeVO nodeVO) {
        this.recruitmentProcessId = nodeVO.getRecruitmentProcessId();
        this.tenantId = nodeVO.getTenantId();
        this.nodeType = nodeVO.getNodeType();
        this.name = nodeVO.getName();
        this.description = nodeVO.getDescription();
    }

    public RecruitmentProcessNode tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }

    public RecruitmentProcessNode recruitmentProcessId(Long recruitmentProcessId) {
        this.recruitmentProcessId = recruitmentProcessId;
        return this;
    }

    public RecruitmentProcessNode nodeType(NodeType nodeType) {
        this.nodeType = nodeType;
        return this;
    }

    public RecruitmentProcessNode name(String name) {
        this.name = name;
        return this;
    }

    public RecruitmentProcessNode description(String description) {
        this.description = description;
        return this;
    }

    public RecruitmentProcessNode nextNodeId(Long nextNodeId) {
        this.nextNodeId = nextNodeId;
        return this;
    }
}
