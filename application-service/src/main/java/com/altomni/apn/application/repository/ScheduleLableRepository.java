package com.altomni.apn.application.repository;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.application.domain.vm.UserDeliveryIndustryVM;
import com.altomni.apn.application.domain.vm.UserDeliveryJobFunctionVM;
import com.altomni.apn.application.domain.vm.UserDeliveryLocationVM;
import com.altomni.apn.application.domain.vm.UserDeliveryRecruitmentProcessVM;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigInteger;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class ScheduleLableRepository {

    @PersistenceContext
    private EntityManager entityManager;


    public List<Long> findAllTenantUser() {
        String findAllTenantUser = "select u.id from user u ";
        Query query = entityManager.createNativeQuery(findAllTenantUser);
        List<BigInteger> list = query.getResultList();
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(BigInteger::longValue).collect(Collectors.toList());
        }
        return null;
    }

    public List<Object[]> getIndustryJobFunctionMappingRelationData() {
        String getIndustryJobFunctionMappingRelationData = """ 
                    SELECT 
                    eim.id,
                    JSON_UNQUOTE(JSON_EXTRACT(child.value, '$.id')) AS child_id
                FROM 
                    enum_industry_mapping eim,
                    JSON_TABLE(eim.sub_job_functions, '$.children[*]' COLUMNS (value JSON PATH '$')) AS child
                WHERE 
                    eim.sub_job_functions IS NOT NULL
                """;
        Query query = entityManager.createNativeQuery(getIndustryJobFunctionMappingRelationData);
        List<Object[]> results = query.getResultList();
        if (CollUtil.isNotEmpty(results)) {
            return results;
        }
        return null;
    }


    public List<UserDeliveryLocationVM> countLocation(Long userId) {
        String countLocationSql = """
                       SELECT
                           t.id AS enum_country_id,
                           t.name AS official_country,
                           COUNT( t.name ) AS count 
                       FROM
                           (
                           SELECT
                               frpv.user_id,
                               ec.name,
                               ec.id 
                           FROM
                               final_recruitment_process_view frpv
                               INNER JOIN `start` s ON s.talent_recruitment_process_id = frpv.talent_recruitment_process_id AND  s.`status` <> -1 AND s.`start_type` IN (0,4)
                                INNER JOIN start_address sa ON s.id = sa.start_id
                                INNER JOIN enum_country ec ON ec.country_code_3   = sa.country  
                           WHERE
                               frpv.user_id = :userId
                                UNION ALL
                           SELECT
                               frpv.user_id,
                               ec.name,
                               ec.id
                           FROM
                               final_recruitment_process_view frpv
                               INNER JOIN job_location jl ON frpv.job_id = jl.job_id 
                               INNER JOIN enum_country ec ON jl.official_country = ec.en_display
                           WHERE
                               frpv.node_type = 10 
                               AND frpv.user_id = :userId 
                           ) AS t 
                       GROUP BY
                           t.user_id,
                           t.id
                       ORDER BY count desc 
                           """;

        Query query = entityManager.createNativeQuery(countLocationSql, UserDeliveryLocationVM.class)
                .setParameter("userId", userId);

        List<UserDeliveryLocationVM> list = query.getResultList();
        if (CollUtil.isNotEmpty(list)) {
            return list;
        }
        return null;
    }

    public List<UserDeliveryRecruitmentProcessVM> countRecruitmentProcessByUserId(Long userId) {
        String countRecruitmentProcessSql = """
                SELECT
                    frpv.recruitment_process_id AS process_id,
                    COUNT( recruitment_process_id ) AS count
                FROM
                    final_recruitment_process_view frpv 
                    INNER JOIN recruitment_process rp ON frpv.recruitment_process_id = rp.id 
                WHERE
                    frpv.user_id = :userId  AND rp.job_type <> :jobType
                GROUP BY
                    frpv.recruitment_process_id
                ORDER BY count desc
                    """;

        Query query = entityManager.createNativeQuery(countRecruitmentProcessSql, UserDeliveryRecruitmentProcessVM.class)
                .setParameter("userId", userId)
                .setParameter("jobType", JobType.PAY_ROLL.toDbValue());


        List<UserDeliveryRecruitmentProcessVM> list = query.getResultList();
        if (CollUtil.isNotEmpty(list)) {
            return list;
        }
        return null;
    }

    public List<UserDeliveryIndustryVM> countDeliveryIndustryByUserId(Long userId) {
        String countDeliveryIndustrySql = """
                SELECT
                    t.mapping_id AS industry_id,
                    COUNT( mapping_id ) AS count
                FROM
                    (
                    SELECT
                        frpv.talent_recruitment_process_id,
                        mp.mapping_id 
                    FROM
                        final_recruitment_process_view frpv
                        INNER JOIN talent_industry_relation tir ON tir.talent_id = frpv.talent_id
                        INNER JOIN mapping_industry mp ON tir.industry_id = mp.industry_id 
                    WHERE
                        frpv.user_id = :userId 
                    GROUP BY
                        talent_recruitment_process_id,
                        mapping_id 
                    ) t 
                GROUP BY
                    t.mapping_id
                    ORDER BY count desc
                        """;

        Query query = entityManager.createNativeQuery(countDeliveryIndustrySql, UserDeliveryIndustryVM.class)
                .setParameter("userId", userId);

        List<UserDeliveryIndustryVM> list = query.getResultList();
        if (CollUtil.isNotEmpty(list)) {
            return list;
        }
        return null;
    }

    public List<UserDeliveryJobFunctionVM> countDeliveryJobFunctionByUserId(Long userId) {
        String countDeliveryJobFunctionSql = """
                SELECT
                    t.mapping_id AS job_function_id,
                    COUNT( mapping_id ) AS count 
                FROM
                    (
                    SELECT
                        frpv.talent_recruitment_process_id,
                        mjf.mapping_id 
                    FROM
                        final_recruitment_process_view frpv
                        INNER JOIN talent_job_function_relation tjfr ON frpv.talent_id = tjfr.talent_id
                        INNER JOIN mapping_job_function mjf ON mjf.job_function_id = tjfr.job_function_id 
                    WHERE
                        frpv.user_id = :userId 
                    GROUP BY
                        talent_recruitment_process_id,
                        mapping_id 
                    ) t 
                GROUP BY
                    job_function_id 
                ORDER BY count desc
                        """;

        Query query = entityManager.createNativeQuery(countDeliveryJobFunctionSql, UserDeliveryJobFunctionVM.class)
                .setParameter("userId", userId);

        List<UserDeliveryJobFunctionVM> list = query.getResultList();
        if (CollUtil.isNotEmpty(list)) {
            return list;
        }
        return null;
    }

    public List<String> findStartAddressCountry() {
        String findStartAddress = "SELECT country from start_address GROUP BY country ";
        Query query = entityManager.createNativeQuery(findStartAddress);
        List<String> list = query.getResultList();
        if (CollUtil.isNotEmpty(list)) {
            return list;
        }
        return List.of();
    }

    public Map<String, String> getCountryPatternMap() {
        String getCountryMap = "SELECT ec.country_code_3, ec.extractor_pattern  from enum_country ec where ec.country_code_3 is not null ";
        Query query = entityManager.createNativeQuery(getCountryMap);
        List<Object[]> list = query.getResultList();
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().collect(Collectors.toMap(t -> t[0].toString(), t -> t[1].toString()));
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateStartAddress(String newCountry, String oldCountry) {
        String updateStartAddress = "UPDATE start_address SET country = :newCountry where country = :oldCountry ";
        Query query = entityManager.createNativeQuery(updateStartAddress)
                .setParameter("newCountry", newCountry)
                .setParameter("oldCountry", oldCountry);
        query.executeUpdate();

    }
}
