package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgOfferLetterCostRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the TalentRecruitmentProcessIpgOfferLetterCostRate entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessIpgOfferLetterCostRateRepository extends JpaRepository<TalentRecruitmentProcessIpgOfferLetterCostRate, Long> {

    TalentRecruitmentProcessIpgOfferLetterCostRate findByCode(String code);

    List<TalentRecruitmentProcessIpgOfferLetterCostRate> findAllByTenantIdAndRecruitmentProcessIdIsNull(Long tenantId);
    List<TalentRecruitmentProcessIpgOfferLetterCostRate> findAllByTenantIdAndRecruitmentProcessIdIs(Long tenantId, Long recruitmentProcessId);
}
