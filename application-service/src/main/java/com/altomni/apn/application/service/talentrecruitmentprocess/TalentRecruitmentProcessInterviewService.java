package com.altomni.apn.application.service.talentrecruitmentprocess;



import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessInterviewVO;

import java.util.List;

/**
 * Service Interface for managing TalentRecruitmentProcessInterview.
 */
public interface TalentRecruitmentProcessInterviewService {

    void updateNoteOnly(TalentRecruitmentProcessInterviewVO interviewVO);

    void updateNoteOnly(Long interviewId, String note);

    TalentRecruitmentProcessInterviewVO save(TalentRecruitmentProcessInterviewVO interviewVO);

    List<TalentRecruitmentProcessInterviewVO> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
