package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgContractFeeCharge;
import com.altomni.apn.application.repository.TalentRecruitmentProcessIpgContractFeeChargeRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessIpgContractFeeChargeService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateService;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgContractFeeChargeVO;
import com.altomni.apn.common.utils.ServiceUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessIpgContractFeeCharge}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessIpgContractFeeChargeServiceImpl implements TalentRecruitmentProcessIpgContractFeeChargeService {

    @Resource
    private TalentRecruitmentProcessIpgContractFeeChargeRepository contractFeeChargeRepository;
    @Resource
    private TalentRecruitmentProcessIpgOfferLetterCostRateService offerLetterCostRateService;

    @Override
    public TalentRecruitmentProcessIpgContractFeeChargeVO save(Long talentRecruitmentProcessId, TalentRecruitmentProcessIpgContractFeeChargeVO feeChargeVO) {
        if (feeChargeVO == null) {
            return null;
        }
        offerLetterCostRateService.validateOfferLetterCostRate(feeChargeVO);
        TalentRecruitmentProcessIpgContractFeeCharge exist = contractFeeChargeRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (exist != null) {
            ServiceUtils.myCopyProperties(feeChargeVO, exist);
            return offerLetterCostRateService.addEntities(toVO(contractFeeChargeRepository.save(exist)));
        }
        feeChargeVO.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
        return offerLetterCostRateService.addEntities(toVO(contractFeeChargeRepository.save(toEntity(feeChargeVO))));
    }

    private TalentRecruitmentProcessIpgContractFeeChargeVO toVO(TalentRecruitmentProcessIpgContractFeeCharge entity) {
        if (entity != null) {
            TalentRecruitmentProcessIpgContractFeeChargeVO vo = new TalentRecruitmentProcessIpgContractFeeChargeVO();
            ServiceUtils.myCopyProperties(entity, vo);
            return vo;
        }
        return null;
    }

    private TalentRecruitmentProcessIpgContractFeeCharge toEntity(TalentRecruitmentProcessIpgContractFeeChargeVO vo) {
        if (vo != null) {
            TalentRecruitmentProcessIpgContractFeeCharge entity = new TalentRecruitmentProcessIpgContractFeeCharge();
            ServiceUtils.myCopyProperties(vo, entity);
            return entity;
        }
        return null;
    }

    @Override
    public TalentRecruitmentProcessIpgContractFeeChargeVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return offerLetterCostRateService.addEntities(toVO(contractFeeChargeRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId)));
    }


}
