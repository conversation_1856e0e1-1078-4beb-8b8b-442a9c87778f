package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.application.dto.TalentRecruitmentProcessAutoEliminationDTO;
import com.altomni.apn.application.web.rest.vm.TalentRecruitmentProcessAutoEliminationVO;

public interface TalentRecruitmentProcessAutoEliminationService {

    TalentRecruitmentProcessAutoEliminationVO queryAutoEliminationByJobId(Long jobId);

    Long countByJobIdAndPeriod(Long jobId, Integer period);

    TalentRecruitmentProcessAutoEliminationVO saveAutoElimination(TalentRecruitmentProcessAutoEliminationDTO dto);
}
