//package com.altomni.apn.application.repository.dashboard;
//
//import com.altomni.apn.application.domain.dashboard.QViewCandidate;
//import com.altomni.apn.application.domain.dashboard.ViewCandidate;
//import com.querydsl.core.types.dsl.StringExpression;
//import com.querydsl.core.types.dsl.StringPath;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.jpa.repository.Query;
//import org.springframework.data.querydsl.QuerydslPredicateExecutor;
//import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
//import org.springframework.data.querydsl.binding.QuerydslBindings;
//import org.springframework.data.querydsl.binding.SingleValueBinding;
//import org.springframework.stereotype.Repository;
//
//import java.time.Instant;
//import java.util.List;
//
///**
// * Spring Data SQL repository for the ViewCandidate entity.
// */
//@SuppressWarnings("unused")
//@Repository
//public interface ViewCandidateRepository extends JpaRepository<ViewCandidate, Long>, QuerydslPredicateExecutor<ViewCandidate>, QuerydslBinderCustomizer<QViewCandidate> {
//
//    @Override
//    default public void customize(QuerydslBindings bindings, QViewCandidate root) {
//        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
//    }
//
//    @Query(value = " select n.last_node_type, count(0) from (" +
//            "         select trp.id," +
//            "                (select trpn.node_type" +
//            "                 from talent_recruitment_process_node trpn" +
//            "                 where trp.id = trpn.talent_recruitment_process_id" +
//            "                   and trpn.node_status in (1, 4)) last_node_type," +
//            "                (select trpn.node_status" +
//            "                 from talent_recruitment_process_node trpn" +
//            "                 where trp.id = trpn.talent_recruitment_process_id" +
//            "                   and trpn.node_status in (1, 4)) last_node_status" +
//            "         FROM talent_recruitment_process trp" +
//            "         where trp.tenant_id = ?1" +
//            "           and trp.last_modified_date > ?2" +
//            "           and trp.last_modified_date < ?3" +
//            "           and exists(select 1" +
//            "                      from talent_recruitment_process_kpi_user trpku" +
//            "                      where trp.id = trpku.talent_recruitment_process_id" +
//            "                        and trpku.user_id = ?4 " +
//            "                        and trpku.user_role in ?5)" +
//            "     ) n " +
//            " where n.last_node_status = 1" +
//            " group by n.last_node_type", nativeQuery = true)
//    List<Object[]> findStatusCountsNotEliminate(Long tenantId, Instant from, Instant to, Long userId, List<Integer> userRoles);
//
//    @Query(value = " select count(0) from (" +
//            "         select trp.id," +
//            "                (select trpn.node_type" +
//            "                 from talent_recruitment_process_node trpn" +
//            "                 where trp.id = trpn.talent_recruitment_process_id" +
//            "                   and trpn.node_status in (1, 4)) last_node_type," +
//            "                (select trpn.node_status" +
//            "                 from talent_recruitment_process_node trpn" +
//            "                 where trp.id = trpn.talent_recruitment_process_id" +
//            "                   and trpn.node_status in (1, 4)) last_node_status" +
//            "         FROM talent_recruitment_process trp" +
//            "         where trp.tenant_id = ?1" +
//            "           and trp.last_modified_date > ?2" +
//            "           and trp.last_modified_date < ?3" +
//            "           and exists(select 1" +
//            "                      from talent_recruitment_process_kpi_user trpku" +
//            "                      where trp.id = trpku.talent_recruitment_process_id" +
//            "                        and trpku.user_id = ?4 " +
//            "                        and trpku.user_role in ?5)" +
//            "     ) n " +
//            " where n.last_node_status = 4", nativeQuery = true)
//    Integer findStatusCountsEliminate(Long tenantId, Instant from, Instant to, Long userId, List<Integer> userRoles);
//
//}
