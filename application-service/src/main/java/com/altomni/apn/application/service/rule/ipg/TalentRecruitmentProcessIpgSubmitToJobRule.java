package com.altomni.apn.application.service.rule.ipg;


import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.job.JobDTOV3;

import java.util.List;

public interface TalentRecruitmentProcessIpgSubmitToJobRule {

    void validate3DayProtectionRule(TalentRecruitmentProcessSubmitToJobVO submitToJobVO);

    void validateTalentResume(TalentRecruitmentProcessSubmitToJobVO submitToJobVO);

    void validateSkipSubmitToAm(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate, JobDTOV3 job, List<TalentRecruitmentProcessKpiUserVO> kpiUsers);

    void checkJobStatus(JobDTOV3 job);
}
