package com.altomni.apn.application.service.talentrecruitmentprocess.impl;


import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardClientInfo;
import com.altomni.apn.application.repository.TalentRecruitmentProcessOnboardClientInfoRepository;
import com.altomni.apn.application.service.finance.FinanceClient;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessOnboardClientInfoService;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOnboardClientInfoVO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.service.vo.invoice.InvoiceDetailInfoVO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessOnboardClientInfo}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessOnboardClientInfoServiceImpl implements TalentRecruitmentProcessOnboardClientInfoService {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessOnboardClientInfoServiceImpl.class);

    @Resource
    TalentRecruitmentProcessOnboardClientInfoRepository clientInfoRepository;

    @Resource
    FinanceClient financeClient;


    @Override
    public TalentRecruitmentProcessOnboardClientInfoVO save(Long talentRecruitmentProcessId, TalentRecruitmentProcessOnboardClientInfoVO clientInfoVO) {
        if (clientInfoVO == null) {
            return null;
        }
        if (StringUtils.isNotBlank(clientInfoVO.getClientEmail())) {
            if (!CommonUtils.isValidEmail(clientInfoVO.getClientEmail())) {
                throw new CustomParameterizedException("Client email address is not valid!");
            }
        }
        clientInfoVO.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
        TalentRecruitmentProcessOnboardClientInfo exist = clientInfoRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (exist != null) {
            ServiceUtils.myCopyProperties(clientInfoVO, exist);
            exist.setLastModifiedDate(Instant.now());
            exist.setLastModifiedBy(SecurityUtils.getUserUid());
            return toVO(clientInfoRepository.save(exist));
        }
        TalentRecruitmentProcessOnboardClientInfo clientInfo = toEntity(clientInfoVO);
        clientInfo.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
        return toVO(clientInfoRepository.save(clientInfo));
    }

    private TalentRecruitmentProcessOnboardClientInfoVO toVO(TalentRecruitmentProcessOnboardClientInfo entity) {
        if (entity != null) {
            TalentRecruitmentProcessOnboardClientInfoVO vo = new TalentRecruitmentProcessOnboardClientInfoVO();
            if (null != entity.getInvoiceTypeId() && null != entity.getClientInfoId()) {
                InvoiceDetailInfoVO invoiceDetailInfoVO = financeClient.getInvoiceInfoByTypeId(entity.getInvoiceTypeId(), entity.getClientInfoId()).getBody();
                if (null != invoiceDetailInfoVO) {
                    ServiceUtils.myCopyProperties(invoiceDetailInfoVO, vo);
                }
            } else {
                ServiceUtils.myCopyProperties(entity, vo);
            }
            vo.setId(entity.getId());
            vo.setInvoiceTypeId(entity.getInvoiceTypeId());
            vo.setClientInfoId(entity.getClientInfoId());
            vo.setClientContactId(entity.getClientContactId());
            return vo;
        }
        return null;
    }

    private TalentRecruitmentProcessOnboardClientInfo toEntity(TalentRecruitmentProcessOnboardClientInfoVO vo) {
        if (vo != null) {
            TalentRecruitmentProcessOnboardClientInfo entity = new TalentRecruitmentProcessOnboardClientInfo();
            if (null != entity.getInvoiceTypeId() && null != entity.getClientInfoId()) {
                InvoiceDetailInfoVO invoiceDetailInfoVO = financeClient.getInvoiceInfoByTypeId(entity.getInvoiceTypeId(), entity.getClientInfoId()).getBody();
                if (null != invoiceDetailInfoVO) {
                    ServiceUtils.myCopyProperties(invoiceDetailInfoVO, vo);
                }
            } else {
                ServiceUtils.myCopyProperties(vo, entity);
            }
            vo.setId(entity.getId());
            vo.setInvoiceTypeId(entity.getInvoiceTypeId());
            vo.setClientInfoId(entity.getClientInfoId());
            vo.setClientContactId(entity.getClientContactId());
            return entity;
        }
        return null;
    }

    @Override
    @Transactional(readOnly = true)
    public TalentRecruitmentProcessOnboardClientInfoVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVO(clientInfoRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }
}
