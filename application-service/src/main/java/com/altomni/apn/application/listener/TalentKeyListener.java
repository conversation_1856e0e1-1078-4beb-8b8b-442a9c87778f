package com.altomni.apn.application.listener;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessMessageService;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class TalentKeyListener {

    @Value("${application.esfillerMQ.notification.lark.webhookKey}")
    private String larkWebhookKey;

    @Value("${application.esfillerMQ.notification.lark.webhookUrl}")
    private String larkWebhookUrl;

    @Resource
    private TalentRecruitmentProcessMessageService talentRecruitmentProcessMessageService;

    @RabbitListener(containerFactory = "talentKeyFactory", queues = {"${application.socialProfileMq.talentIdQueue}"})
    @RabbitHandler
    public void process(Message message) {
        log.info("[TalentKeyListener] Received message: {}，Business data：{}", message.toString(), new String(message.getBody()));
        String talentIdStr = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StrUtil.isBlank(talentIdStr)) {
                log.error("TalentKeyListener send message is error, talentId is null");
                return;
            }
            LoginUtil.simulateLoginWithClient();
            talentRecruitmentProcessMessageService.sendKeyCandidateUpdateNotification(Long.valueOf(talentIdStr));
            log.info("TalentKeyListener is success, talentId = {}", talentIdStr);
        } catch (Exception e) {
            log.error("TalentKeyListener send message is error, talentId = {}, msg = {}", talentIdStr, ExceptionUtil.getAllExceptionMsg(e));
//            NotificationUtils.sendAlertToLark(larkWebhookKey, larkWebhookUrl, "TalentKeyListener send message is error, msg \n" + ExceptionUtil.getAllExceptionMsg(e));
        }
    }

}
