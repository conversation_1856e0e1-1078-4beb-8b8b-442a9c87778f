package com.altomni.apn.application.domain;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.ResignationReason;
import com.altomni.apn.common.domain.enumeration.application.ResignationReasonConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * A TalentRecruitmentProcessResignation.
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Entity
@Table(name = "talent_recruitment_process_resignation")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessResignation extends AbstractPermissionAuditingEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1495314777550137054L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "resign_date")
    private LocalDate resignDate;

    @Column(name = "reason")
    @Convert(converter = ResignationReasonConverter.class)
    private ResignationReason resignationReason;

    @Column(name = "note")
    private String note;
}
