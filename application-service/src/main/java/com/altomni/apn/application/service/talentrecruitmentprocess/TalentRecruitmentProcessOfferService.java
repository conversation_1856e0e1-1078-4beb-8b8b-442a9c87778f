package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOfferVO;

/**
 * Service Interface for managing TalentRecruitmentProcessOffer.
 */
public interface TalentRecruitmentProcessOfferService {

    TalentRecruitmentProcessOfferVO updateNoteOnly(TalentRecruitmentProcessOfferVO talentRecruitmentProcessOfferVO);

    TalentRecruitmentProcessOfferVO updateNoteOnly(Long talentRecruitmentProcessId, String note);

    TalentRecruitmentProcessOfferVO save(TalentRecruitmentProcessOfferVO talentRecruitmentProcessOfferVO);

    TalentRecruitmentProcessOfferVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
