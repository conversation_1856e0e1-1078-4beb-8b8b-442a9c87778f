package com.altomni.apn.application.web.rest.vm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class CongratsNotification {
    private String users;
    private String message;
    private String teams;
    private String amount;
    private String candidateProfile;
    private String botId;
}
