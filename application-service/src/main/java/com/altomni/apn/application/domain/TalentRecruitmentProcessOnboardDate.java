package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A TalentRecruitmentProcessOnboardDate.
 */
@Entity
@Table(name = "talent_recruitment_process_onboard_date")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOnboardDate extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 5525789060713605581L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "onboard_date")
    private LocalDate onboardDate;

    @ApiModelProperty(value = "For IPG FTE version only.")
    @Column(name = "warranty_end_date")
    private LocalDate warrantyEndDate;

    @ApiModelProperty(value = "For IPG contract version only.")
    @Column(name = "end_date")
    private LocalDate endDate;

    @ApiModelProperty(value = "For general version only.")
    @Column(name = "currency")
    private Integer currency;

    @ApiModelProperty(value = "For general version only.")
    @Convert(converter = RateUnitTypeConverter.class)
    @Column(name = "rate_unit_type")
    private RateUnitType rateUnitType;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("id", "createdBy", "createdDate", "lastModifiedBy", "lastModifiedDate"));
}
