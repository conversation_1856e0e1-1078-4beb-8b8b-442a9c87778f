package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessEliminate;
import com.altomni.apn.application.dto.EliminateDTO;
import com.altomni.apn.application.repository.TalentRecruitmentProcessEliminateRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessEliminateService;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessEliminateVO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;

/**
 * Service Implementation for managing TalentRecruitmentProcessSubmitToJob.
 */
@Service
@Transactional
public class TalentRecruitmentProcessEliminateServiceImpl implements TalentRecruitmentProcessEliminateService {

    @Resource
    private TalentRecruitmentProcessEliminateRepository eliminateRepository;

    @Override
    public TalentRecruitmentProcessEliminateVO save(EliminateDTO eliminate) {
        TalentRecruitmentProcessEliminate exist = eliminateRepository.findOneByTalentRecruitmentProcessId(eliminate.getTalentRecruitmentProcessId());
        if (exist != null) {
            ServiceUtils.myCopyProperties(eliminate, exist);
            exist.setLastModifiedDate(Instant.now());
            exist.setLastModifiedBy(SecurityUtils.getUserUid());
            return toVO(eliminateRepository.saveAndFlush(exist));
        }
        exist = new TalentRecruitmentProcessEliminate();
        ServiceUtils.myCopyProperties(eliminate, exist);

        return toVO(eliminateRepository.saveAndFlush(exist));
    }

    private TalentRecruitmentProcessEliminateVO toVO(Long talentRecruitmentProcessId) {
        return toVO(eliminateRepository.findOneByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }

    private TalentRecruitmentProcessEliminateVO toVO(TalentRecruitmentProcessEliminate eliminate) {
        if (eliminate == null) {
            return null;
        }
        TalentRecruitmentProcessEliminateVO result = new TalentRecruitmentProcessEliminateVO();
        ServiceUtils.myCopyProperties(eliminate, result);
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public TalentRecruitmentProcessEliminateVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVO(talentRecruitmentProcessId);
    }

    @Override
    public void delete(Long talentRecruitmentProcessId) {
        eliminateRepository.nativeDeleteByTalentRecruitmentProcessId(talentRecruitmentProcessId);
    }
}
