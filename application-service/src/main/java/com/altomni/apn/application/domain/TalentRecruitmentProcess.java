package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.talent.TalentAutoDeclassifyDto;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentRecruitmentProcess.
 */
@Entity
@Table(name = "talent_recruitment_process")
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcess extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -386368362649529657L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "recruitment_process_id")
    private Long recruitmentProcessId;

    @Column(name = "talent_id")
    private Long talentId;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "note")
    private String note;

    @Column(name = "is_substitute_talent")
    private Boolean isSubstituteTalent;

    @Column(name = "ai_score")
    private Short aiScore;

    @Transient
    private Boolean employed;

    public static TalentRecruitmentProcess fromVO(TalentRecruitmentProcessVO talentRecruitmentProcessVO) {
        TalentRecruitmentProcess result = new TalentRecruitmentProcess();
        ServiceUtils.myCopyProperties(talentRecruitmentProcessVO, result);
        return result;
    }

    public static TalentRecruitmentProcessVO fromEntity(TalentRecruitmentProcess talentRecruitmentProcess) {
        TalentRecruitmentProcessVO result = new TalentRecruitmentProcessVO();
        ServiceUtils.myCopyProperties(talentRecruitmentProcess, result);
        return result;
    }

    public static TalentRecruitmentProcess fromTalentRecruitmentProcessSubmitToJobVO(TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
        TalentRecruitmentProcess result = new TalentRecruitmentProcess();
        result.setTenantId(SecurityUtils.getTenantId());
        result.setTalentId(submitToJobVO.getTalentId());
        result.setJobId(submitToJobVO.getJobId());
        result.setRecruitmentProcessId(submitToJobVO.getRecruitmentProcessId());
        result.setNote(submitToJobVO.getNote());
        result.setIsSubstituteTalent(submitToJobVO.getIsSubstituteTalent());
        return result;
    }

    public TalentAutoDeclassifyDto toTalentAutoDeclassifyDto(NodeType nodeType) {
        TalentAutoDeclassifyDto result = new TalentAutoDeclassifyDto();
        result.setTalentId(this.getTalentId());
        result.setRecruitmentProcessId(this.getRecruitmentProcessId());
        result.setTalentRecruitmentProcessId(this.getId());
        result.setCurrentNode(nodeType);
        result.setDeclassifyType(TalentDeclassifyType.DECLASSIFY_PROCESS);
        return result;

    }
}
