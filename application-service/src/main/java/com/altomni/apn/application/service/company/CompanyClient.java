package com.altomni.apn.application.service.company;

import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;
import com.altomni.apn.company.service.dto.CompanyDTO;
import com.altomni.apn.company.vo.company.AccountCompanyVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(value = "company-service")
public interface CompanyClient {

    @GetMapping("/company/api/v3/company/{id}")
    ResponseEntity<CompanyDTO> getCompany(@PathVariable("id") Long id);

    @PostMapping("/company/api/v3/company/find-by-ids")
    ResponseEntity<List<CompanyDTO>> findALLByIds(@RequestBody List<Long> companyIds);

    @GetMapping("/company/api/v3/skip-submit-to-am-companies/{companyId}/all-users")
    ResponseEntity<List<SkipSubmitToAmCompanyUser>> getAllSkipSubmitToAmCompanyUsers(@PathVariable("companyId") Long companyId);

    @GetMapping("/company/api/v3/sales-leads/{companyId}/am")
    ResponseEntity<List<Long>> getAllAmByCompany(@PathVariable("companyId") Long companyId);

    /**
     * 查询CRM公司信息（调CRM接口）
     * @param ids
     * @return
     */
    @PostMapping("/company/api/v3/company/find-by-ids-from-crm")
    ResponseEntity<List<AccountCompanyVO>> queryCompanyByIdsFromCRM(@RequestBody List<Long> ids);


}
