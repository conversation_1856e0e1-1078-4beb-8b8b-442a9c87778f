package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessNode;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOffer;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardDate;
import com.altomni.apn.application.repository.TalentRecruitmentProcessNodeRepository;
import com.altomni.apn.application.repository.TalentRecruitmentProcessOfferRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.*;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOfferVO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;

/**
 * Service Implementation for managing TalentRecruitmentProcessOffer.
 */
@Service
@Transactional
public class TalentRecruitmentProcessOfferServiceImpl implements TalentRecruitmentProcessOfferService {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessOfferServiceImpl.class);
    @Resource
    private TalentRecruitmentProcessOfferRepository talentRecruitmentProcessOfferRepository;
    @Resource
    private TalentRecruitmentProcessOfferSalaryPackageService offerSalaryPackageService;
    @Resource
    private TalentRecruitmentProcessKpiUserService kpiUserService;
    @Resource
    private TalentRecruitmentProcessIpgAgreedPayRateService agreedPayRateService;
    @Resource
    private TalentRecruitmentProcessFeeChargeService feeChargeService;
    @Resource
    private TalentRecruitmentProcessOnboardDateService talentRecruitmentProcessOnboardDateService;

    @Resource
    private TalentRecruitmentProcessNodeRepository talentRecruitmentProcessNodeRepository;

    @Override
    public TalentRecruitmentProcessOfferVO updateNoteOnly(TalentRecruitmentProcessOfferVO talentRecruitmentProcessOfferVO) {
        return this.updateNoteOnly(talentRecruitmentProcessOfferVO.getTalentRecruitmentProcessId(), talentRecruitmentProcessOfferVO.getNote());
    }

    @Override
    public TalentRecruitmentProcessOfferVO updateNoteOnly(Long talentRecruitmentProcessId, String note) {
        TalentRecruitmentProcessOffer exist = talentRecruitmentProcessOfferRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (exist != null) {
//            exist.setNote(note);
//            exist.setLastModifiedDate(Instant.now());
//            exist.setLastModifiedBy(SecurityUtils.getUserUid());
//            exist = talentRecruitmentProcessOfferRepository.saveAndFlush(exist);
            talentRecruitmentProcessOfferRepository.updateNoteOnly(talentRecruitmentProcessId, note, SecurityUtils.getUserId());
            return toVO(exist);
        } else {
            return null;
        }
    }

    @Override
    public TalentRecruitmentProcessOfferVO save(TalentRecruitmentProcessOfferVO offerVO) {
        TalentRecruitmentProcessOffer exist = talentRecruitmentProcessOfferRepository.findByTalentRecruitmentProcessId(offerVO.getTalentRecruitmentProcessId());
        if (exist != null) {
            ServiceUtils.myCopyProperties(offerVO, exist);
            exist.setLastModifiedDate(Instant.now());
            exist.setLastModifiedBy(SecurityUtils.getUserUid());
            talentRecruitmentProcessOfferRepository.saveAndFlush(exist);
//            talentRecruitmentProcessOfferRepository.updateLastModifiedDateAndLastModifiedBy(offerVO.getTalentRecruitmentProcessId(), SecurityUtils.getUserUid());
        } else {
            talentRecruitmentProcessOfferRepository.save(TalentRecruitmentProcessOffer.fromVO(offerVO));
        }
        if (offerVO.getCurrency() != null) {
            saveTalentRecruitmentProcessOnboardDate(offerVO);
        }
        agreedPayRateService.save(offerVO.getTalentRecruitmentProcessId(), offerVO.getAgreedPayRate());
        offerSalaryPackageService.save(offerVO.getTalentRecruitmentProcessId(), offerVO.getSalaryPackages());
        feeChargeService.save(offerVO.getTalentRecruitmentProcessId(), offerVO.getFeeCharge());
        TalentRecruitmentProcessNode activeNode = talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeStatus(offerVO.getTalentRecruitmentProcessId(), NodeStatus.ACTIVE);
//        if (Constants.TENANT_IPG.equals(SecurityUtils.getTenantId()) ||
//                (!Constants.TENANT_IPG.equals(SecurityUtils.getTenantId())
//                        && NodeType.OFFER.toDbValue() >= activeNode.getNodeType().toDbValue())) {
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId()) ||
                (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())
                        && NodeType.OFFER.toDbValue() >= activeNode.getNodeType().toDbValue())) {
            kpiUserService.save(offerVO.getTalentRecruitmentProcessId(), offerVO.getKpiUsers());
        }
        return toVO(offerVO.getTalentRecruitmentProcessId());
    }

    private void saveTalentRecruitmentProcessOnboardDate(TalentRecruitmentProcessOfferVO offerVO) {
        TalentRecruitmentProcessOnboardDate onboardDate = talentRecruitmentProcessOnboardDateService.findByTalentRecruitmentProcessId(offerVO.getTalentRecruitmentProcessId());
        if (onboardDate != null) { // update
            ServiceUtils.myCopyProperties(offerVO, onboardDate, TalentRecruitmentProcessOnboardDate.UpdateSkipProperties);
            talentRecruitmentProcessOnboardDateService.save(offerVO.getTalentRecruitmentProcessId(), onboardDate);
        } else { // create
            TalentRecruitmentProcessOnboardDate create = new TalentRecruitmentProcessOnboardDate();
            ServiceUtils.myCopyProperties(offerVO, create, TalentRecruitmentProcessOnboardDate.UpdateSkipProperties);
            talentRecruitmentProcessOnboardDateService.save(offerVO.getTalentRecruitmentProcessId(), create);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public TalentRecruitmentProcessOfferVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        log.debug("Request to get TalentRecruitmentProcessOffer : {}", talentRecruitmentProcessId);
        return toVO(talentRecruitmentProcessId);
    }

    private TalentRecruitmentProcessOfferVO toVO(Long talentRecruitmentProcessId) {
        return toVO(talentRecruitmentProcessOfferRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }

    private TalentRecruitmentProcessOfferVO toVO(TalentRecruitmentProcessOffer talentRecruitmentProcessOffer) {
        if (talentRecruitmentProcessOffer == null) {
            return null;
        }
        TalentRecruitmentProcessOfferVO result = new TalentRecruitmentProcessOfferVO();
        ServiceUtils.myCopyProperties(talentRecruitmentProcessOffer, result);
        TalentRecruitmentProcessOnboardDate onboardDate = talentRecruitmentProcessOnboardDateService.findByTalentRecruitmentProcessId(talentRecruitmentProcessOffer.getTalentRecruitmentProcessId());
        if (onboardDate != null) {
            ServiceUtils.myCopyProperties(onboardDate, result, TalentRecruitmentProcessOnboardDate.UpdateSkipProperties);
        }
        return result;
    }
}
