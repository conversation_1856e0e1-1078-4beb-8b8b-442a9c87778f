package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgOfferAccept;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardDate;
import com.altomni.apn.application.repository.TalentRecruitmentProcessIpgOfferAcceptRepository;
import com.altomni.apn.application.service.rule.ipg.TalentRecruitmentProcessIpgOfferAcceptRule;
import com.altomni.apn.application.service.talentrecruitmentprocess.*;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferAcceptVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSourceChannelProfitSharingVO;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessIpgOfferAccept}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessIpgOfferAcceptServiceImpl implements TalentRecruitmentProcessIpgOfferAcceptService {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessIpgOfferAcceptServiceImpl.class);
    @Resource
    private TalentRecruitmentProcessIpgOfferAcceptRepository ipgOfferAcceptRepository;
    @Resource
    private TalentRecruitmentProcessOnboardDateService talentRecruitmentProcessOnboardDateService;
    @Resource
    private TalentRecruitmentProcessKpiUserService kpiUserService;
    @Resource
    private TalentRecruitmentProcessFeeChargeService feeChargeService;
    @Resource
    private TalentRecruitmentProcessOfferSalaryPackageService offerSalaryPackageService;
    @Resource
    private TalentRecruitmentProcessIpgContractFeeChargeService contractFeeChargeService;
    @Resource
    private TalentRecruitmentProcessOnboardClientInfoService clientInfoService;

    @Resource
    private TalentRecruitmentProcessIpgOfferAcceptRule talentRecruitmentProcessIpgOfferAcceptRule;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Override
    public TalentRecruitmentProcessIpgOfferAcceptVO updateNoteOnly(TalentRecruitmentProcessIpgOfferAcceptVO offerAcceptVO) {
        return this.updateNoteOnly(offerAcceptVO.getTalentRecruitmentProcessId(), offerAcceptVO.getNote());
    }

    @Override
    public TalentRecruitmentProcessIpgOfferAcceptVO updateNoteOnly(Long talentRecruitmentProcessId, String note) {
        TalentRecruitmentProcessIpgOfferAccept exist = ipgOfferAcceptRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
//        if (!Objects.equals(SecurityUtils.getTenantId(), Constants.TENANT_IPG)) {
        if (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            throw new ForbiddenException("You are not authorized to update this information.");
        }
        if (exist != null) {
//            exist.setNote(note);
//            exist.setLastModifiedDate(Instant.now());
//            exist.setLastModifiedBy(SecurityUtils.getUserUid());
//            exist = ipgOfferAcceptRepository.saveAndFlush(exist);
            ipgOfferAcceptRepository.updateNoteOnly(talentRecruitmentProcessId, note, SecurityUtils.getUserId());
            //return toVO(exist);
            return null;
        } else {
            return null;
        }
    }

    @Override
    public TalentRecruitmentProcessIpgOfferAcceptVO save(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessIpgOfferAcceptVO offerAcceptVO) {
        TalentRecruitmentProcessIpgOfferAccept exist = ipgOfferAcceptRepository.findByTalentRecruitmentProcessId(offerAcceptVO.getTalentRecruitmentProcessId());
//        if (!Objects.equals(SecurityUtils.getTenantId(), Constants.TENANT_IPG)) {
        if (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_OFFERNOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        if (exist != null) {
            ServiceUtils.myCopyProperties(offerAcceptVO, exist);
            exist.setLastModifiedDate(Instant.now());
            exist.setLastModifiedBy(SecurityUtils.getUserUid());
            if (null != offerAcceptVO.getProfitSharing()) {
                exist.setChannelPlatform(offerAcceptVO.getProfitSharing().getChannelPlatform());
                exist.setProfitSharingRatio(offerAcceptVO.getProfitSharing().getProfitSharingRatio());
            }
            ipgOfferAcceptRepository.save(exist);
//            ipgOfferAcceptRepository.updateLastModifiedDateAndLastModifiedBy(talentRecruitmentProcess.getId(), SecurityUtils.getUserUid());
        } else {
            talentRecruitmentProcessIpgOfferAcceptRule.validateAndUpdateJobStatusToFilled(talentRecruitmentProcess.getJobId());
            TalentRecruitmentProcessIpgOfferAccept accept =TalentRecruitmentProcessIpgOfferAccept.fromVO(offerAcceptVO);
            if (null != offerAcceptVO.getProfitSharing()) {
                accept.setChannelPlatform(offerAcceptVO.getProfitSharing().getChannelPlatform());
                accept.setProfitSharingRatio(offerAcceptVO.getProfitSharing().getProfitSharingRatio());
            }
            ipgOfferAcceptRepository.save(accept);
        }
        saveTalentRecruitmentProcessOnboardDate(offerAcceptVO);
        kpiUserService.save(offerAcceptVO.getTalentRecruitmentProcessId(), offerAcceptVO.getKpiUsers());
        contractFeeChargeService.save(offerAcceptVO.getTalentRecruitmentProcessId(), offerAcceptVO.getContractFeeCharge());
        offerSalaryPackageService.save(offerAcceptVO.getTalentRecruitmentProcessId(), offerAcceptVO.getSalaryPackages());
        feeChargeService.save(offerAcceptVO.getTalentRecruitmentProcessId(), offerAcceptVO.getFeeCharge());
        ipgOfferAcceptRepository.updateLastModifiedDateAndLastModifiedBy(offerAcceptVO.getTalentRecruitmentProcessId(), SecurityUtils.getUserUid());
        clientInfoService.save(offerAcceptVO.getTalentRecruitmentProcessId(), offerAcceptVO.getClientInfo());
        // 逾期未入职提醒
        //return toVO(offerAcceptVO.getTalentRecruitmentProcessId());
        return null;
    }


    private void saveTalentRecruitmentProcessOnboardDate(TalentRecruitmentProcessIpgOfferAcceptVO offerAcceptVO) {
        TalentRecruitmentProcessOnboardDate onboardDate = talentRecruitmentProcessOnboardDateService.findByTalentRecruitmentProcessId(offerAcceptVO.getTalentRecruitmentProcessId());
        if (onboardDate != null) { // update
            ServiceUtils.myCopyProperties(offerAcceptVO, onboardDate, TalentRecruitmentProcessOnboardDate.UpdateSkipProperties);
            talentRecruitmentProcessOnboardDateService.save(offerAcceptVO.getTalentRecruitmentProcessId(), onboardDate);
        } else { // create
            TalentRecruitmentProcessOnboardDate create = new TalentRecruitmentProcessOnboardDate();
            ServiceUtils.myCopyProperties(offerAcceptVO, create, TalentRecruitmentProcessOnboardDate.UpdateSkipProperties);
            talentRecruitmentProcessOnboardDateService.save(offerAcceptVO.getTalentRecruitmentProcessId(), create);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public TalentRecruitmentProcessIpgOfferAcceptVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        log.debug("Request to get TalentRecruitmentProcessIpgOfferAccept : {}", talentRecruitmentProcessId);
        return toVO(talentRecruitmentProcessId);
    }

    private TalentRecruitmentProcessIpgOfferAcceptVO toVO(Long talentRecruitmentProcessId) {
        return toVO(ipgOfferAcceptRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }

    private TalentRecruitmentProcessIpgOfferAcceptVO toVO(TalentRecruitmentProcessIpgOfferAccept offerAccept) {
        if (offerAccept == null) {
            return null;
        }
        TalentRecruitmentProcessIpgOfferAcceptVO result = new TalentRecruitmentProcessIpgOfferAcceptVO();
        ServiceUtils.myCopyProperties(offerAccept, result);
        TalentRecruitmentProcessOnboardDate onboardDate = talentRecruitmentProcessOnboardDateService.findByTalentRecruitmentProcessId(offerAccept.getTalentRecruitmentProcessId());
        if (onboardDate != null) {
            //fixme: 这就是使用 CopyProperties 工具类的危害，很容易漏掉不想 copt 的属性
            ServiceUtils.myCopyProperties(onboardDate, result, TalentRecruitmentProcessOnboardDate.UpdateSkipProperties);
        }
        TalentRecruitmentProcessSourceChannelProfitSharingVO sharingVO = new TalentRecruitmentProcessSourceChannelProfitSharingVO();
        if (offerAccept.getChannelPlatform() != null) {
            sharingVO.setChannelPlatform(offerAccept.getChannelPlatform());
        }
        if (offerAccept.getProfitSharingRatio() != null) {
            sharingVO.setProfitSharingRatio(offerAccept.getProfitSharingRatio());
        }
        result.setProfitSharing(sharingVO);
        return result;
    }
}
