//package com.altomni.apn.application.service.pipeline;
//
//
//import com.altomni.apn.application.domain.pipeline.PipelineColumnPreference;
//import com.altomni.apn.common.domain.enumeration.search.ModuleType;
//import com.altomni.apn.common.dto.application.pipeline.PipelineColumnPreferenceDTO;
//
//import java.util.List;
//
//public interface PiplineColumnPreferenceService {
//
//    PipelineColumnPreferenceDTO findOne(Long userId, ModuleType moduleType);
//
//    void saveTemplate(PipelineColumnPreferenceDTO ColumnPreference);
//
//    List<PipelineColumnPreference> findByUserIdAndModule(ModuleType moduleType);
//
//    void deleteById(Long id);
//
//
//}
