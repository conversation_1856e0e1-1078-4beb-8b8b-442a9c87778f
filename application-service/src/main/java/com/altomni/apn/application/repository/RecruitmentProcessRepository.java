package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.RecruitmentProcess;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.job.JobV3;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * Spring Data  repository for the RecruitmentProcess entity.
 */
@Repository
public interface RecruitmentProcessRepository extends JpaRepository<RecruitmentProcess, Long> {

    Page<RecruitmentProcess> findAllByStatus(ActiveStatus status, Pageable pageable);

    Page<RecruitmentProcess> findAllByTenantId(Long tenantId, Pageable pageable);

    Page<RecruitmentProcess> findAllByTenantIdAndStatus(Long tenantId, ActiveStatus status, Pageable pageable);

    Page<RecruitmentProcess> findAllByTenantIdAndStatusAndJobTypeNot(Long tenantId, ActiveStatus status, JobType jobType, Pageable pageable);

    RecruitmentProcess findFirstByTenantIdAndStatusAndJobTypeIsNull(Long tenantId, ActiveStatus status);

    RecruitmentProcess findByTenantIdAndStatusAndJobType(Long tenantId, ActiveStatus status, JobType jobType);

    @Query(value = " SELECT trp.job_id as jobId, IFNULL( count( trpioa.id ), 0 ) as count, 20 as node FROM talent_recruitment_process_submit_to_client trpioa " +
            " LEFT JOIN talent_recruitment_process trp ON trpioa.talent_recruitment_process_id = trp.id WHERE trp.job_id IN (?1) GROUP BY trp.job_id " +
            " UNION ALL  " +
            " SELECT trp.job_id as jobId, IFNULL( count( trpioa.id ), 0 ) as count, 60 as node FROM talent_recruitment_process_onboard trpioa " +
            " LEFT JOIN talent_recruitment_process trp ON trpioa.talent_recruitment_process_id = trp.id WHERE trp.job_id IN (?1) GROUP BY trp.job_id " +
            " UNION ALL " +
            " SELECT trp.job_id as jobId, IFNULL( count( DISTINCT trpioa.talent_recruitment_process_id ), 0 ) as count, 30 as node FROM talent_recruitment_process_interview trpioa " +
            " LEFT JOIN talent_recruitment_process trp ON trpioa.talent_recruitment_process_id = trp.id WHERE trp.job_id IN (?1) GROUP BY trp.job_id  ", nativeQuery = true)
    List<Object[]> getClintBoardInterviewStats(List<Long> jobIdList);

    @Query(value = "select j.company_id from talent_recruitment_process p " +
            " left join job j on j.id=p.job_id" +
            " where p.id=:talentRecruitmentProcessId", nativeQuery = true)
    Long getCompanyIdByTalentRecruitmentProcessId(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query(value = "SELECT a.id FROM talent_recruitment_process a " +
            "    LEFT JOIN talent_recruitment_process_eliminate e ON e.talent_recruitment_process_id = a.id " +
            "    LEFT JOIN talent_recruitment_process_node trpn ON a.id = trpn.talent_recruitment_process_id " +
            "WHERE e.id IS NULL AND a.recruitment_process_id = :recruitmentProcessId " +
            "GROUP BY a.id " +
            "HAVING COUNT(CASE WHEN trpn.node_type = 60 AND trpn.node_status = 1 THEN 1 END) = 0", nativeQuery = true)
    List<Long> getInProgressApplicationIdsByRecruitmentProcessId(@Param("recruitmentProcessId") Long recruitmentProcessId);

//    @Query(value = "SELECT COUNT(1) FROM (SELECT 1 FROM talent_recruitment_process a " +
//            "    LEFT JOIN talent_recruitment_process_eliminate e ON e.talent_recruitment_process_id = a.id " +
//            "    LEFT JOIN talent_recruitment_process_node trpn ON a.id = trpn.talent_recruitment_process_id " +
//            "WHERE e.id IS NULL AND a.recruitment_process_id = :recruitmentProcessId " +
//            "GROUP BY a.id " +
//            "HAVING COUNT(CASE WHEN trpn.node_type = 60 AND trpn.node_status = 1 THEN 1 END) = 0) temp", nativeQuery = true)
    @Query(value = "select count(a.id) " +
            "from talent_recruitment_process a " +
            "     left join talent_recruitment_process_node trpn " +
            "       on a.id = trpn.talent_recruitment_process_id and trpn.node_status = 1 " +
            "where trpn.node_type <> 60 " +
            "  and trpn.id is not null " +
            "  and a.recruitment_process_id = :recruitmentProcessId ", nativeQuery = true)
    Long getInProgressApplicationCountByRecruitmentProcessId(@Param("recruitmentProcessId") Long recruitmentProcessId);


//    @Query(value = "SELECT a.id FROM talent_recruitment_process a " +
//            "    LEFT JOIN talent_recruitment_process_eliminate e ON e.talent_recruitment_process_id = a.id " +
//            "    LEFT JOIN talent_recruitment_process_node trpn ON a.id = trpn.talent_recruitment_process_id " +
//            "WHERE e.id IS NULL AND a.recruitment_process_id = :recruitmentProcessId " +
//            "GROUP BY a.id " +
//            "HAVING COUNT(CASE WHEN trpn.node_type = 60 AND trpn.node_status = 1 THEN 1 END) = 0", nativeQuery = true)
//    List<Long> getInProgressJobIdsByRecruitmentProcessId(@Param("recruitmentProcessId") Long recruitmentProcessId);

    @Query(value = "SELECT j.id, j.title, j.status FROM job j WHERE j.stauts <> 4 and j.recruitment_process_id = :recruitmentProcessId", nativeQuery = true)
    List<Object[]> getInProgressJobIdsByRecruitmentProcessId(@Param("recruitmentProcessId") Long recruitmentProcessId);

//    @Query(value = "SELECT COUNT(DISTINCT a.id) FROM talent_recruitment_process a " +
//            "    LEFT JOIN talent_recruitment_process_eliminate e ON e.talent_recruitment_process_id = a.id " +
//            "    LEFT JOIN talent_recruitment_process_node trpn ON a.id = trpn.talent_recruitment_process_id " +
//            "WHERE e.id IS NULL AND a.recruitment_process_id = :recruitmentProcessId " +
//            "GROUP BY a.id " +
//            "HAVING COUNT(CASE WHEN trpn.node_type = 60 AND trpn.node_status = 1 THEN 1 END) = 0", nativeQuery = true)
//    Long getInProgressJobCountByRecruitmentProcessId(@Param("recruitmentProcessId") Long recruitmentProcessId);

    @Query(value = "SELECT COUNT(j.id) FROM job j " +
            "WHERE j.status = 0 AND j.recruitment_process_id = :recruitmentProcessId", nativeQuery = true)
    Long getInProgressJobCountByRecruitmentProcessId(@Param("recruitmentProcessId") Long recruitmentProcessId);


    List<RecruitmentProcess> findAllByTenantId(Long tenantId);
    //@Query("select rp.id, rp.jobType from RecruitmentProcess rp where rp.tenantId = :tenantId")
    default Map<Long, JobType> findAllIdAndJobTypeByTenantId(Long tenantId){
        return findAllByTenantId(tenantId).stream().collect(Collectors.toMap(RecruitmentProcess::getId, RecruitmentProcess::getJobType));
    }

    Long countAllByTenantIdAndStatus(Long TenantId, ActiveStatus status);


    @Query(value = "select distinct job_type from recruitment_process where id in (?1) ", nativeQuery = true)
    List<Long> findJobTypeByProcessId(List<Long> processId);

    @Query(value = "select count(1) from talent_recruitment_process trp " +
            "left join talent_recruitment_process_eliminate elim on trp.id = elim.talent_recruitment_process_id " +
            "left join talent_recruitment_process_onboard onboard on trp.id = onboard.talent_recruitment_process_id " +
            "where trp.job_id=:jobId and elim.id is null and onboard.id is null", nativeQuery = true)
    Integer countUnfinishedApplicationsForJob(@Param("jobId") Long jobId);


    @Query(value = "select " +
            "       trp.id as 'application_id', " +
            "       j.id as 'job_id', " +
            "       j.title as 'job_title', " +
            "       t.id as 'talent_id', " +
            "       t.full_name as 'talent_name', " +
            "       t.created_date as 'talent_created_date', " +
            "       node.node_type as 'node_type', " +
            "       trp.created_date as 'application_start_date', " +
            "       trp.last_modified_date as 'application_last_modified_date' " +
            "from recruitment_process rp " +
            "    left join talent_recruitment_process trp on rp.id = trp.recruitment_process_id " +
            "    left join talent_recruitment_process_node node on trp.id = node.talent_recruitment_process_id and node.node_status = 1 " +
            "    left join talent t on trp.talent_id = t.id " +
            "    left join job j on trp.job_id = j.id " +
            "where rp.id = ?1 and node_type <> 60 ", nativeQuery = true)
    List<Object[]> getUnfinishedApplicationsByRecruitmentProcessId(Long recruitmentProcessId);

    @Query(value = "select j.id, j.title, j.status, j.created_date, j.last_modified_date from job j where j.status = 0 and j.recruitment_process_id = ?1", nativeQuery = true)
    List<Object[]> getUnclosedJobsByRecruitmentProcessId(Long recruitmentProcessId);


    @Query(value = "select id from job_project where tenant_id=:tenantId", nativeQuery = true)
    Set<Long> findTeamIdsForPrivateJob(@Param("tenantId") Long tenantId);

    @Query(value = "SELECT j.id FROM job j " +
            "INNER JOIN job_project jp ON jp.id = j.pteam_id " + //归属于job_project确保是private job
            "LEFT JOIN user_job_relation uj ON j.id = uj.job_id AND uj.user_id =:userId AND uj.status = 1 " +
            "WHERE jp.tenant_id =:tenantId AND j.puser_id !=:userId AND uj.job_id IS NULL", nativeQuery = true)
    Set<Long> findAllUnauthorizedPrivateJobIds(@Param("tenantId") Long tenantId, @Param("userId") Long userId);


    @Query(value = "SELECT j.id FROM job j " +
            "INNER JOIN job_project jp ON jp.id = j.pteam_id " +
            "WHERE j.id =:jobId", nativeQuery = true)
    Set<Long> checkPrivateJob(@Param("jobId") Long jobId);


    List<RecruitmentProcess> findAllByTenantIdAndStatus(Long tenantId, ActiveStatus status);

    @Query(value = "SELECT j.id FROM job j WHERE j.id = :id and j.pteam_id not in (select jp.id from job_project jp where jp.tenant_id = :tenantId)", nativeQuery = true)
    Optional<Long> findRegularJobById(@Param("id") Long id, @Param("tenantId") Long tenantId);

}
