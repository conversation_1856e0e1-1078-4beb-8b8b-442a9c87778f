package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.application.service.message.MessageClientService;
import com.altomni.apn.application.service.talent.TalentService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessMessageService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessService;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.message.MessageCreateWithNoPoachingSubmitDTO;
import com.altomni.apn.common.dto.message.MessageCreateWithTalentInfoDTO;
import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * talent 流程消息提醒
 */
@Slf4j
@Service("talentRecruitmentProcessMessageService")
public class TalentRecruitmentProcessMessageServiceImpl implements TalentRecruitmentProcessMessageService {

    @Resource
    private TalentService talentService;

    @Resource
    private MessageClientService messageClientService;

    @Resource
    private TalentRecruitmentProcessService talentRecruitmentProcessService;

    /**
     * 1. 当关键人员信息发送变更时，自动通知相关用户，通知信息放到个人业绩预警当中
     *   通知人员取值规则
     *   候选人没有关联任何一个流程
     *   1.  当候选人的关键信息发生更新时，则通知这个候选人的创建者
     *   候选人关联了流程
     *   根据流程节点和流程的提交时间判断发给哪一个流程中的am 和 recruiter (流程节点的优先级为onboard > offer accepted >offer >interview >submit to client & submit to job)
     *   1. 假设候选人只关联了一个流程，则通知该流程中的am 和 recruiter
     *   2. 假设候选人关联了两个流程，两个流程都在submit to job/submit to client/interview/ offer /offer accepted /onboard 节点，则通知较晚开启流程（做submit to job 操作）的am 和 recruiter
     *   3. 假设候选人关联了两个流程，一个在onboard 阶段，一个在offer accepted 阶段，则通知节点在onboard 阶段的流程中的am  和 recruiter
     *   4. 假设候选人关联了两个流程，一个在offer accepted阶段，一个在offer  阶段，则通知节点在offer accepted阶段的流程中的am  和 recruiter
     *   5. 假设候选人关联了两个流程，一个在offer阶段，一个在interview阶段，则通知节点在offer 阶段的流程中的am  和 recruiter
     *   6. 假设候选人关联了两个流程，一个在interview阶段，一个在submit to client阶段，则通知节点在interview阶段的流程中的am  和 recruiter
     *   7. 假设候选人关联了两个流程，第一个流程是20240501 提交的，已经到了submit to client 阶段，第二个流程是20240502 提交的，目前在submit to job 阶段，则通知第二个流程中的am  和 recruiter
     */
    @Override
    public void sendKeyCandidateUpdateNotification(Long talentId) {
        List<TalentRecruitmentProcessVO> talentRecruitmentProcessList = talentRecruitmentProcessService.getTalentRecruitmentProcessAllByTalentId(talentId);
        Optional<TalentRecruitmentProcessVO> latestProcessOpt = talentRecruitmentProcessList.stream()
                .max(Comparator.comparing(TalentRecruitmentProcessVO::getLastNodeStatus)
                        .thenComparing(TalentRecruitmentProcessVO::getCreatedDate));
        latestProcessOpt.ifPresentOrElse(this::sendMessageByTalentRecruitmentProcessVO,
                () -> {
                    TalentBriefDTO talentBriefDTO = talentService.getTalentBrief(talentId);
                    Long createUserId = talentBriefDTO.getCreateUserId();
                    Long tenantId = talentBriefDTO.getTenantId();
                    String talentName = talentBriefDTO.getFullName();
                    MessageCreateWithTalentInfoDTO messageCreateDTO = new MessageCreateWithTalentInfoDTO(talentName, List.of(createUserId), tenantId);
                    messageClientService.createMessageWithTalentInfoUpdate(messageCreateDTO);
                });
    }

    @Override
    public void sendNoPoachingTalentSubmitNotification(MessageCreateWithNoPoachingSubmitDTO messageDTO) {
        messageClientService.createMessageWithNoPoachingSubmit(messageDTO);
    }


    private void sendMessageByTalentRecruitmentProcessVO(TalentRecruitmentProcessVO talentRecruitmentProcessVO) {
        //有关联流程的时候,需要发送通知的角色 am 和 recruiter
        Long talentId = talentRecruitmentProcessVO.getTalentId();
        List<UserRole> userRoles = CollUtil.newArrayList(UserRole.AM,UserRole.CO_AM, UserRole.RECRUITER);
        List<TalentRecruitmentProcessKpiUserVO> kpiUsers = talentRecruitmentProcessVO.getKpiUsers();
        if (CollUtil.isEmpty(kpiUsers)) {
            log.warn("talent {} application {} no have kpiUsers", talentId, talentRecruitmentProcessVO.getId());
            return;
        }
        kpiUsers = kpiUsers.stream().filter(kpi -> userRoles.contains(kpi.getUserRole())).toList();
        if (CollUtil.isEmpty(kpiUsers)) {
            log.warn("talent {} application {} no have am,recruiter kpiUsers", talentId, talentRecruitmentProcessVO.getId());
            return;
        }
        Set<Long> userIdSet = kpiUsers.stream().map(TalentRecruitmentProcessKpiUserVO::getUserId).collect(Collectors.toSet());
        Long tenantId = talentRecruitmentProcessVO.getTenantId();
        TalentBriefDTO talentBriefDTO = talentService.getTalentBrief(talentId);
        String talentName = talentBriefDTO.getFullName();
        MessageCreateWithTalentInfoDTO messageCreateDTO = new MessageCreateWithTalentInfoDTO(talentName, CollUtil.newArrayList(userIdSet), tenantId);
        messageClientService.createMessageWithTalentInfoUpdate(messageCreateDTO);
    }

}
