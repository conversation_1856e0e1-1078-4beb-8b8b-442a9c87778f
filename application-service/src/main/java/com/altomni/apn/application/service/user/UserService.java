package com.altomni.apn.application.service.user;

import com.altomni.apn.common.dto.user.UserBriefDTO;

import java.util.Collection;
import java.util.List;

public interface UserService {

    UserBriefDTO getUserById(Long userId);

    List<UserBriefDTO> findBriefUsers(Collection<Long> ids);

    Integer getTenantParamValue(String paramKey);

    void clearJobPreferenceByRecruitmentProcessId(Long recruitmentProcessId);

}
