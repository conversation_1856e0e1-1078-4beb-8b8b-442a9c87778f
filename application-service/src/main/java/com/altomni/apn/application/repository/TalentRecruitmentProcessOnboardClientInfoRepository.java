package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardClientInfo;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the TalentRecruitmentProcessOnboardClientInfo entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessOnboardClientInfoRepository
    extends JpaRepository<TalentRecruitmentProcessOnboardClientInfo, Long> {

    TalentRecruitmentProcessOnboardClientInfo findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
    
    List<TalentRecruitmentProcessOnboardClientInfo> findAllByTalentRecruitmentProcessIdIn(List<Long> recruitmentProcessIds);

}
