package com.altomni.apn.application.dto;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * A AgencyActivityDTO.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AgencyActivityForApplicationDTO {

    private Long talentId;

    private String talentName;

    private Long jobId;

    private String jobTitle;

    private Boolean isPrivateJob;

    private String privateJob;

    private Long applicationId;

    private NodeType nodeType;

    public AgencyActivityForApplicationDTO(TalentRecruitmentProcessVO talentRecruitmentProcessVO, NodeType nodeType) {
        this.talentId = talentRecruitmentProcessVO.getTalentId();
        this.talentName = talentRecruitmentProcessVO.getTalent().getFullName();
        this.jobId = talentRecruitmentProcessVO.getJobId();
        this.jobTitle = talentRecruitmentProcessVO.getJob().getTitle();
        this.isPrivateJob = talentRecruitmentProcessVO.getJob().getIsPrivateJob();
        this.privateJob = talentRecruitmentProcessVO.getJob().getIsPrivateJob().toString();
        this.applicationId = talentRecruitmentProcessVO.getId();
        this.nodeType = nodeType;
    }

}
