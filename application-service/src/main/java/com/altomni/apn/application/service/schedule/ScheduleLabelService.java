package com.altomni.apn.application.service.schedule;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.application.domain.vm.UserDeliveryIndustryVM;
import com.altomni.apn.application.domain.vm.UserDeliveryJobFunctionVM;
import com.altomni.apn.application.domain.vm.UserDeliveryLocationVM;
import com.altomni.apn.application.domain.vm.UserDeliveryRecruitmentProcessVM;
import com.altomni.apn.application.repository.ScheduleLableRepository;
import com.altomni.apn.common.domain.user.UserDeliveryCountryRelation;
import com.altomni.apn.common.domain.user.UserDeliveryIndustryRelation;
import com.altomni.apn.common.domain.user.UserDeliveryJobFunctionRelation;
import com.altomni.apn.common.domain.user.UserDeliveryProcessRelation;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.user.repository.user.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.time.Duration;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ScheduleLabelService {
    @Resource
    private ScheduleLableRepository scheduleLableRepository;
    @Resource
    private UserDeliveryCountryRelationRepository userDeliveryCountryRelationRepository;
    @Resource
    private UserDeliveryIndustryRelationRepository userDeliveryIndustryRelationRepository;
    @Resource
    private UserDeliveryJobFunctionRelationRepository userDeliveryJobFunctionRelationRepository;
    @Resource
    private UserDeliveryProcessRelationRepository userDeliveryProcessRelationRepository;
    @Resource
    private UserRepository userRepository;
    @Resource(name = "commonThreadPool")
    private Executor executor;

    private static Map<Long, Set<Long>> industryJobFunctionMappingRelation = new HashMap<>();

    private static final int BATCH_SIZE = 5;  // 每批次的任务数

    @Scheduled(cron = "0 0 10 * * ?", zone = "UTC")
    public void updateUserLabel() {
        List<Long> users = scheduleLableRepository.findAllTenantUser();
        batchProcessLabel(users);
    }

    public void batchProcessLabel(List<Long> users){
        if (CollUtil.isNotEmpty(users)) {
            int totalSize = users.size();
            log.info("Start processing user labels for {} users", totalSize);
            for (int i = 0; i < totalSize; i += BATCH_SIZE) {
                // 计算当前批次的用户ID
                int end = Math.min(i + BATCH_SIZE, totalSize);
                List<Long> batch = users.subList(i, end);

                // 提交当前批次的任务到线程池
                CountDownLatch latch = new CountDownLatch(batch.size());
                for (Long userId : batch) {
                    // 在每个任务执行时，完成后递减 latch
                    executor.execute(() -> {
                        try {
                            processLabelByUserId(userId);
                        } finally {
                            latch.countDown();  // 任务完成后递减
                        }
                    });
                }

                try {
                    latch.await(60, TimeUnit.MINUTES);
                } catch (InterruptedException e) {
                    log.error("Label Task error message :{},userBatch :{}", ExceptionUtils.getStackTrace(e), batch);
                    throw new CustomParameterizedException("Internal Server Error");
                }
            }
            log.info("Finished processing user labels for {} users", totalSize);
        }
    }
    @Transactional(rollbackFor = Exception.class)
    void processLabelByUserId(Long userId) {
        // 查询出user的createdDate
        Instant createdDate = userRepository.findCreatedDateByUserId(userId);
        boolean overDay = DateUtil.isMoreThanDays(createdDate, 180);
        // 处理location标签
        processLocationLable(userId, overDay);
        // 处理process标签
        processRecruitmentLable(userId, overDay);
        // 处理industry标签
        List<UserDeliveryIndustryVM> industries = processIndustryLable(userId, overDay);
        // 处理jobFunction标签
        processJobFunctionLable(userId , industries, overDay);
    }

    private void processJobFunctionLable(Long userId, List<UserDeliveryIndustryVM> industries, Boolean overDay) {
        List<UserDeliveryJobFunctionVM> deliveryList = scheduleLableRepository.countDeliveryJobFunctionByUserId(userId);
        List<UserDeliveryJobFunctionRelation> existsRelations = userDeliveryJobFunctionRelationRepository.findAllByUserId(userId);
        // 根据系统生成的industry去过滤jobFunction
        if (CollUtil.isNotEmpty(industries)){
            Set<Long> industrySet = industries.stream().map(UserDeliveryIndustryVM::getIndustryId).collect(Collectors.toSet());
            Map<Long, Set<Long>> map = getIndustryJobFunctionMappingRelation();
            Set<Long> resultSet = industrySet.stream()
                    .filter(map::containsKey)
                    .flatMap(industryId -> map.get(industryId).stream())
                    .collect(Collectors.toSet());
            deliveryList = deliveryList.stream().filter(t-> resultSet.contains(t.getJobFunctionId())).collect(Collectors.toList());
        }else{
            deliveryList = List.of();
        }
        // 判断是否有系统生成的数据
        if (CollUtil.isNotEmpty(deliveryList)) {
            Long maxCount = deliveryList.get(0).getCount();
            //判断是否存在历史数据
            if (CollUtil.isNotEmpty(existsRelations)) {
                // 存在历史数据，需要进行merge-update
                Map<Long, List<UserDeliveryJobFunctionVM>> statMap = deliveryList.stream().collect(Collectors.groupingBy(UserDeliveryJobFunctionVM::getJobFunctionId));
                Set<Long> existIds = existsRelations.stream().map(UserDeliveryJobFunctionRelation::getEnumJobFunctionMappingId).collect(Collectors.toSet());

                List<UserDeliveryJobFunctionRelation> saveList = new ArrayList<>();
                List<UserDeliveryJobFunctionRelation> deleteList = new ArrayList<>();
                existsRelations.forEach(dcr -> {
                    // 统计数据包含的数据，做合并更新
                    if (statMap.containsKey(dcr.getEnumJobFunctionMappingId())){
                        dcr.setCount(statMap.get(dcr.getEnumJobFunctionMappingId()).get(0).getCount());
                        if (overDay){
                            dcr.setTop(true);
                        }else{
                            dcr.setTop(false);
                        }
                        saveList.add(dcr);
                    }else{
                        // 统计数据里不包含的数据,如果在180天内，且被手动更新存在，那么保留
                        if (!overDay && Boolean.TRUE.equals(dcr.getUpdated())){
                            dcr.setCount(null);
                            dcr.setTop(false);
                            saveList.add(dcr);
                        }else{
                            // 否则直接删除
                            deleteList.add(dcr);
                        }
                    }
                });
                // 统计数据里新增的数据，做新增保存处理
                statMap.entrySet().stream().filter(t -> !existIds.contains(t.getKey()))
                        .forEach(e -> {
                            UserDeliveryJobFunctionVM vm = e.getValue().get(0);
                            UserDeliveryJobFunctionRelation relation = new UserDeliveryJobFunctionRelation(null, userId,  e.getKey(), vm.getCount(), vm.getCount() >= maxCount);
                            saveList.add(relation);
                        });

                if (CollUtil.isNotEmpty(saveList)){
                    userDeliveryJobFunctionRelationRepository.saveAll(saveList);
                }
                if (CollUtil.isNotEmpty(deleteList)){
                    userDeliveryJobFunctionRelationRepository.deleteAll(deleteList);
                }

            }else{
                // 不存在历史数据，那么直接保存系统生成数据即可,如果overDay为false，那么top统一为false
                List<UserDeliveryJobFunctionRelation> newIndustryRelation = deliveryList.stream().map(d -> new UserDeliveryJobFunctionRelation(null, userId, d.getJobFunctionId(), d.getCount(), d.getCount() >= maxCount)).collect(Collectors.toList());
                userDeliveryJobFunctionRelationRepository.saveAll(newIndustryRelation);
            }
        }else{
            // 如果系统解析没有生成的数据，当overDay为true的时候，直接删除数据
            if (overDay){
                userDeliveryJobFunctionRelationRepository.deleteAll(existsRelations);
            }
        }
    }


    private List<UserDeliveryIndustryVM> processIndustryLable(Long userId, Boolean overDay) {
        List<UserDeliveryIndustryVM> deliveryList = scheduleLableRepository.countDeliveryIndustryByUserId(userId);
        List<UserDeliveryIndustryRelation> existsRelations = userDeliveryIndustryRelationRepository.findAllByUserId(userId);
        // 判断是否有系统生成的数据
        if (CollUtil.isNotEmpty(deliveryList)) {
            Long maxCount = deliveryList.get(0).getCount();
            //判断是否存在历史数据
            if (CollUtil.isNotEmpty(existsRelations)) {
                // 存在历史数据，需要进行merge-update
                Map<Long, List<UserDeliveryIndustryVM>> statMap = deliveryList.stream().collect(Collectors.groupingBy(UserDeliveryIndustryVM::getIndustryId));
                Set<Long> existIds = existsRelations.stream().map(UserDeliveryIndustryRelation::getEnumIndustryMappingId).collect(Collectors.toSet());

                List<UserDeliveryIndustryRelation> saveList = new ArrayList<>();
                List<UserDeliveryIndustryRelation> deleteList = new ArrayList<>();
                existsRelations.forEach(dcr -> {
                    // 统计数据包含的数据，做合并更新
                    if (statMap.containsKey(dcr.getEnumIndustryMappingId())){
                        dcr.setCount(statMap.get(dcr.getEnumIndustryMappingId()).get(0).getCount());
                        if (overDay){
                            dcr.setTop(true);
                        }else{
                            dcr.setTop(false);
                        }
                        saveList.add(dcr);
                    }else{
                        // 统计数据里不包含的数据,如果在180天内，且被手动更新存在，那么保留
                        if (!overDay && Boolean.TRUE.equals(dcr.getUpdated())){
                            dcr.setCount(null);
                            dcr.setTop(false);
                            saveList.add(dcr);
                        }else{
                            // 否则直接删除
                            deleteList.add(dcr);
                        }
                    }
                });
                // 统计数据里新增的数据，做新增保存处理
                statMap.entrySet().stream().filter(t -> !existIds.contains(t.getKey()))
                        .forEach(e -> {
                            UserDeliveryIndustryVM vm = e.getValue().get(0);
                            UserDeliveryIndustryRelation relation = new UserDeliveryIndustryRelation(null, userId,  e.getKey(), vm.getCount(), vm.getCount() >= maxCount);
                            saveList.add(relation);
                        });

                if (CollUtil.isNotEmpty(saveList)){
                    userDeliveryIndustryRelationRepository.saveAll(saveList);
                }
                if (CollUtil.isNotEmpty(deleteList)){
                    userDeliveryIndustryRelationRepository.deleteAll(deleteList);
                }
            }else{
                // 不存在历史数据，那么直接保存系统生成数据即可,如果overDay为false，那么top统一为false
                List<UserDeliveryIndustryRelation> newIndustryRelation = deliveryList.stream().map(d -> new UserDeliveryIndustryRelation(null, userId, d.getIndustryId(), d.getCount(), d.getCount() >= maxCount)).collect(Collectors.toList());
                userDeliveryIndustryRelationRepository.saveAll(newIndustryRelation);
            }
        }else{
            // 如果系统解析没有生成的数据，当overDay为true的时候，直接删除数据
            if (overDay){
                userDeliveryIndustryRelationRepository.deleteAll(existsRelations);
            }
        }
        return deliveryList;
    }


    private void processRecruitmentLable(Long userId, Boolean overDay) {
        List<UserDeliveryRecruitmentProcessVM> deliveryList = scheduleLableRepository.countRecruitmentProcessByUserId(userId);
        List<UserDeliveryProcessRelation> existsRelations = userDeliveryProcessRelationRepository.findAllByUserId(userId);
        // 判断是否有系统生成的数据
        if (CollUtil.isNotEmpty(deliveryList)) {
            Long maxCount = deliveryList.get(0).getCount();
            //判断是否存在历史数据
            if (CollUtil.isNotEmpty(existsRelations)) {
                // 存在历史数据，需要进行merge-update
                Map<Long, List<UserDeliveryRecruitmentProcessVM>> statMap = deliveryList.stream().collect(Collectors.groupingBy(UserDeliveryRecruitmentProcessVM::getProcessId));
                Set<Long> existIds = existsRelations.stream().map(UserDeliveryProcessRelation::getProcessId).collect(Collectors.toSet());

                List<UserDeliveryProcessRelation> saveList = new ArrayList<>();
                List<UserDeliveryProcessRelation> deleteList = new ArrayList<>();
                existsRelations.forEach(dcr -> {
                    // 统计数据包含的数据，做合并更新
                    if (statMap.containsKey(dcr.getProcessId())){
                        dcr.setCount(statMap.get(dcr.getProcessId()).get(0).getCount());
                        if (overDay){
                            dcr.setTop(true);
                        }else{
                            dcr.setTop(false);
                        }
                        saveList.add(dcr);
                    }else{
                        // 统计数据里不包含的数据,如果在180天内，且被手动更新存在，那么保留
                        if (!overDay && Boolean.TRUE.equals(dcr.getUpdated())){
                            dcr.setCount(null);
                            dcr.setTop(false);
                            saveList.add(dcr);
                        }else{
                            // 否则直接删除
                            deleteList.add(dcr);
                        }
                    }
                });
                // 统计数据里新增的数据，做新增保存处理
                statMap.entrySet().stream().filter(t -> !existIds.contains(t.getKey()))
                        .forEach(e -> {
                            UserDeliveryRecruitmentProcessVM vm = e.getValue().get(0);
                            UserDeliveryProcessRelation relation = new UserDeliveryProcessRelation(null, userId,  e.getKey(), vm.getCount(), vm.getCount() >= maxCount);
                            saveList.add(relation);
                        });

                if (CollUtil.isNotEmpty(saveList)){
                    userDeliveryProcessRelationRepository.saveAll(saveList);
                }
                if (CollUtil.isNotEmpty(deleteList)){
                    userDeliveryProcessRelationRepository.deleteAll(deleteList);
                }

            }else{
                // 不存在历史数据，那么直接保存系统生成数据即可,如果overDay为false，那么top统一为false
                List<UserDeliveryProcessRelation> newIndustryRelation = deliveryList.stream().map(d -> new UserDeliveryProcessRelation(null, userId, d.getProcessId(), d.getCount(), d.getCount() >= maxCount)).collect(Collectors.toList());
                userDeliveryProcessRelationRepository.saveAll(newIndustryRelation);
            }
        }else{
            // 如果系统解析没有生成的数据，当overDay为true的时候，直接删除数据
            if (overDay){
                userDeliveryProcessRelationRepository.deleteAll(existsRelations);
            }
        }
    }


    private void processLocationLable(Long userId, Boolean overDay) {
        List<UserDeliveryLocationVM> deliveryCountryList = scheduleLableRepository.countLocation(userId);
        List<UserDeliveryCountryRelation> deliveryCountryRelations = userDeliveryCountryRelationRepository.findAllByUserId(userId);
        // 判断是否有系统生成的数据
        if (CollUtil.isNotEmpty(deliveryCountryList)) {
            Long maxCount = deliveryCountryList.get(0).getCount();
            //判断是否存在历史数据
            if (CollUtil.isNotEmpty(deliveryCountryRelations)) {
                // 存在历史数据，需要进行merge-update
                Map<Integer, List<UserDeliveryLocationVM>> statMap = deliveryCountryList.stream().collect(Collectors.groupingBy(UserDeliveryLocationVM::getEnumCountryId));
                Set<Integer> existCountryIds = deliveryCountryRelations.stream().map(UserDeliveryCountryRelation::getEnumCountryId).collect(Collectors.toSet());

                List<UserDeliveryCountryRelation> saveList = new ArrayList<>();
                List<UserDeliveryCountryRelation> deleteList = new ArrayList<>();
                deliveryCountryRelations.forEach(dcr -> {
                    // 统计数据包含的数据，做合并更新
                    if (statMap.containsKey(dcr.getEnumCountryId())){
                        dcr.setCount(statMap.get(dcr.getEnumCountryId()).get(0).getCount());
                        if (overDay){
                            dcr.setTop(true);
                        }else{
                            dcr.setTop(false);
                        }
                        saveList.add(dcr);
                    }else{
                        // 统计数据里不包含的数据,如果在180天内，且被手动更新存在，那么保留
                        if (!overDay && Boolean.TRUE.equals(dcr.getUpdated())){
                            dcr.setCount(null);
                            dcr.setTop(false);
                            saveList.add(dcr);
                        }else{
                            // 否则直接删除
                            deleteList.add(dcr);
                        }
                    }
                });
                // 统计数据里新增的数据，做新增保存处理
                statMap.entrySet().stream().filter(t -> !existCountryIds.contains(t.getKey()))
                        .forEach(e -> {
                            UserDeliveryLocationVM locationVM = e.getValue().get(0);
                            UserDeliveryCountryRelation countryRelation = new UserDeliveryCountryRelation(null, userId, locationVM.getOfficialCountry(), e.getKey(), locationVM.getCount(), locationVM.getCount() >= maxCount);
                            saveList.add(countryRelation);
                        });

                if (CollUtil.isNotEmpty(saveList)){
                    userDeliveryCountryRelationRepository.saveAll(saveList);
                }
                if (CollUtil.isNotEmpty(deleteList)){
                    userDeliveryCountryRelationRepository.deleteAll(deleteList);
                }

            }else{
                // 不存在历史数据，那么直接保存系统生成数据即可,如果overDay为false，那么top统一为false
                List<UserDeliveryCountryRelation> newIndustryRelation = deliveryCountryList.stream().map(d -> new UserDeliveryCountryRelation(null, userId, d.getOfficialCountry(), d.getEnumCountryId(), d.getCount(), d.getCount() >= maxCount)).collect(Collectors.toList());
                userDeliveryCountryRelationRepository.saveAll(newIndustryRelation);
            }
        }else{
            // 如果系统解析没有生成的数据，当overDay为true的时候，直接删除数据
            if (overDay){
                userDeliveryCountryRelationRepository.deleteAll(deliveryCountryRelations);
            }
        }
    }

    public <T, U> boolean areListsEqual(
            List<T> list1,
            List<U> list2,
            Function<T, Object> keyExtractor1,
            Function<T, Long> valueExtractor1,
            Function<U, Object> keyExtractor2,
            Function<U, Long> valueExtractor2) {
        // 检查 list1 中是否存在 valueExtractor1 返回 null 的元素
        boolean containsNullInList1 = list1.stream()
                .anyMatch(item -> valueExtractor1.apply(item) == null);

        if (containsNullInList1) {
            return false;  // 如果有 null，直接返回 false
        }
        // 将 list1 转换为 Map<Long, Long>，key 是 processId，value 是 count
        Map<Object, Long> map1 = list1.stream()
                .collect(Collectors.toMap(keyExtractor1, valueExtractor1));

        // 将 list2 转换为 Map<Long, Long>，key 是 processId，value 是 count
        Map<Object, Long> map2 = list2.stream()
                .collect(Collectors.toMap(keyExtractor2, valueExtractor2));

        // 比较两个 map 是否相等
        return map1.equals(map2);
    }

    public void standardizeCountry() {
        List<String> countries = scheduleLableRepository.findStartAddressCountry();
        Map<String, String> countryPatternMap = scheduleLableRepository.getCountryPatternMap();
        Map<String, String> resultMap = new HashMap<>();
        countries.forEach(country -> {
            String newCountryCode = standardizeCountry(country, countryPatternMap);
            if (StringUtils.isNotBlank(newCountryCode)){
                resultMap.putIfAbsent(country, newCountryCode);
            }
        });
        resultMap.forEach((key, value) -> {
            scheduleLableRepository.updateStartAddress(value, key);
        });

    }

    public static String standardizeCountry(String countryName, Map<String, String> countryPatternMap) {
        if (countryPatternMap.containsKey(countryName) || StringUtils.isBlank(countryName)){
            return null;
        }
        for (Map.Entry<String, String> entry : countryPatternMap.entrySet()) {
            String countryCode = entry.getKey();
            String regex = entry.getValue();

            Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(countryName);

            if (matcher.find()) {
                return countryCode;
            }
        }
        return null; // 如果没有匹配到任何国家代码，返回null
    }


    private Map<Long, Set<Long>> getIndustryJobFunctionMappingRelation(){
        if (industryJobFunctionMappingRelation.isEmpty()){
            synchronized (this){
                if (industryJobFunctionMappingRelation.isEmpty()){
                    List<Object[]> relationData = scheduleLableRepository.getIndustryJobFunctionMappingRelationData();
                    if (CollUtil.isNotEmpty(relationData)){
                        for (Object[] row : relationData) {
                            Long id = ((Integer) row[0]).longValue(); // 第一个列是 id
                            Long childId = Long.parseLong(row[1].toString()); // 第二个列是 child_id

                            // 获取或创建 Set 来存储 childIds
                            Set<Long> childIds = industryJobFunctionMappingRelation.getOrDefault(id, new HashSet<>());
                            childIds.add(childId);

                            // 将 Set 放回 Map
                            industryJobFunctionMappingRelation.putIfAbsent(id, childIds);
                        }
                    }
                }
            }
        }
        return industryJobFunctionMappingRelation;
    }
}
