package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationDTO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationVO;

import java.util.List;

/**
 * Service Interface for managing TalentRecruitmentProcessResignationService.
 * <AUTHOR>
 */
public interface TalentRecruitmentProcessResignationService {

    TalentRecruitmentProcessResignationVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    void resign(TalentRecruitmentProcessResignationDTO resignationDTO);

    List<TalentRecruitmentProcessResignationVO> findByTalentId(Long talentId);
}
