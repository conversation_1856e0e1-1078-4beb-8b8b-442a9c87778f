package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessCommissionVO;

/**
 * Service Interface for managing TalentRecruitmentProcessCommission.
 */
public interface TalentRecruitmentProcessCommissionService {

    TalentRecruitmentProcessCommissionVO updateNoteOnly(TalentRecruitmentProcessCommissionVO talentRecruitmentProcessCommission);

    TalentRecruitmentProcessCommissionVO updateNoteOnly(Long talentRecruitmentProcessId, String note);

    TalentRecruitmentProcessCommissionVO save(TalentRecruitmentProcessCommissionVO talentRecruitmentProcessCommission);

    TalentRecruitmentProcessCommissionVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
