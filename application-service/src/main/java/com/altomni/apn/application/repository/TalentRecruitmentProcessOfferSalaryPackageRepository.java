package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOfferSalaryPackage;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the TalentRecruitmentProcessOfferSalaryPackage entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessOfferSalaryPackageRepository extends JpaRepository<TalentRecruitmentProcessOfferSalaryPackage, Long> {

    List<TalentRecruitmentProcessOfferSalaryPackage> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessOfferSalaryPackage> findAllByTalentRecruitmentProcessIdIn(List<Long> recruitmentProcessIds);
}

