package com.altomni.apn.application.service.talent;

import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentNote;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.dto.xxljob.XxlJobForTalentReminderDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Component
@FeignClient(value = "talent-service")
public interface TalentClient {

    @GetMapping("/talent/api/v3/talents/{id}")
    ResponseEntity<TalentDTOV3> getTalent(@PathVariable("id") Long id);

    @GetMapping("/talent/api/v3/talents/fullName/{talentId}")
    ResponseEntity<String> findFullNameByTalentId(@PathVariable("talentId") Long talentId);

    @GetMapping("/talent/api/v3/talents/without-entity/{id}")
    ResponseEntity<TalentDTOV3> getTalentWithoutEntity(@PathVariable("id") Long talentId);

    //记录job-talent推荐进入的数据使用
    @PostMapping("/talent/api/v3/record/talent-job-recommend")
    ResponseEntity<Void> recordTalentJobRecommend(@RequestBody RecommendFeedback dto);

    @PostMapping("/talent/api/v3/talents/get-by-ids-without-entity")
    ResponseEntity<List<TalentBriefDTO>> getTalentWithoutEntityList(@RequestBody Collection<Long> talentIds);

    @GetMapping("/talent/api/v3/talent-ownerships/talentId/{talentId}")
    ResponseEntity<List<TalentOwnership>> getAllTalentOwners(@PathVariable("talentId") Long talentId, @RequestParam("talentOwnershipType") List<TalentOwnershipType> talentOwnershipType);

    @PostMapping("/talent/api/v3/talent-ownerships")
    ResponseEntity<List<TalentOwnership>> saveAllOwnerships(@RequestBody List<TalentOwnership> talentOwnerships);

    @GetMapping("/talent/api/v3/talent-notes/talent/{talentId}")
    ResponseEntity<List<TalentNote>> getAllTalentNotesForTalent(@PathVariable("talentId") Long talentId);

    @PutMapping("/talent/api/v3/talents/no-onboarded-reminder-xxl-job-for-talent")
    ResponseEntity<Void> noOnboardedReminderXxlJobForTalent(@RequestBody XxlJobForTalentReminderDTO vo);

    @PutMapping("/talent/api/v3/talents/candidate_onboard_invoice_reminder_time")
    ResponseEntity<Void> candidateOnboardInvoiceReminderTime(@RequestBody XxlJobForTalentReminderDTO vo);

    @GetMapping("/talent/api/v3/talent-resumes/relation/{relationId}")
    ResponseEntity<TalentResumeDTO> getTalentResumeByTalentResumeRelationId(@PathVariable("relationId") Long talentResumeRelationId);

    @PutMapping("/talent/api/v3/talents/{talentId}/experience")
    ResponseEntity<Void> updateTalentExperience(@PathVariable("talentId") Long talentId, @RequestBody TalentExperienceDTO talentExperienceDTO);

    @DeleteMapping("/talent/api/v3/talents/{talentId}/experience/talent-recruitment-process/{talentRecruitmentProcessId}")
    ResponseEntity<Void> deleteTalentExperience(@PathVariable("talentId") Long talentId, @PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @GetMapping("/talent/api/v3/talents/{talentId}/confidential/view-able")
    ResponseEntity<Boolean> confidentialTalentViewAble(@PathVariable("talentId") Long talentId);

    @PutMapping("/talent/api/v3/talents/declassify/process")
    ResponseEntity<Void> declassifyProcess(@RequestBody TalentAutoDeclassifyDto talentAutoDeclassifyDto);

    @PostMapping("/talent/api/v3/talents/confidential/view-able")
    ResponseEntity<Set<Long>> filterConfidentialTalentViewAble(@RequestBody Set<Long> talentIds);

    @PostMapping("/talent/api/v3/talents/confidential/info")
    ResponseEntity<Map<Long, ConfidentialInfoDto>> getTalentConfidentialInfo(@RequestBody Set<Long> talentIds);
}
