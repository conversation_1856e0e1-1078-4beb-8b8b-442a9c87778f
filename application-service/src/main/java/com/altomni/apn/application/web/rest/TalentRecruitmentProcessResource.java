package com.altomni.apn.application.web.rest;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.application.config.aop.JobSnapShotAnnotation;
import com.altomni.apn.application.config.aop.JobSnapShotAspect;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboard;
import com.altomni.apn.application.domain.enumeration.NodeSearchFilter;
import com.altomni.apn.application.dto.AgencyActivityForApplicationDTO;
import com.altomni.apn.application.dto.EliminateDTO;
import com.altomni.apn.application.service.agency.AgencyService;
import com.altomni.apn.application.service.talentrecruitmentprocess.*;
import com.altomni.apn.application.web.rest.vm.*;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.*;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import io.micrometer.core.annotation.Timed;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import liquibase.pro.packaged.V;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * REST controller for managing TalentRecruitmentProcess.
 */
@RestController
@RequestMapping("/api/v3")
public class TalentRecruitmentProcessResource {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessResource.class);

    private static final String ENTITY_NAME = "TalentRecruitmentProcess";

    @Resource
    private TalentRecruitmentProcessService talentRecruitmentProcessService;
    @Resource
    private TalentRecruitmentProcessForJobDetailService talentRecruitmentProcessForJobDetailService;
    @Resource
    private TalentRecruitmentProcessForTalentService talentRecruitmentProcessForTalentService;
    @Resource
    private TalentRecruitmentProcessKpiUserService talentRecruitmentProcessKpiUserService;

    @Resource
    private TalentRecruitmentProcessResignationService talentRecruitmentProcessResignationService;

    @Resource
    private TalentRecruitmentProcessOnboardService talentRecruitmentProcessOnboardService;

    @Resource
    private AgencyService agencyService;

    /**
     * 获取当前候选人的工作状态，并判断当前登录用户是否有权限提交该候选人到新的职位，该权限通过page permission配置。
     * @param talentId -> 候选人 ID
     * @return TalentEmploymentStatusVO -> 记录当前候选人的工作状态：入职，在职，保证期内。以及记录当前登录用户是否有权限提交该候选人到新的职位
     */
    @GetMapping("/talent-recruitment-processes/talent/{talentId}/employment-status")
    public ResponseEntity<TalentEmploymentStatusVO> getTalentEmploymentStatus(@PathVariable("talentId") Long talentId) {
        log.info("REST request to validate getTalentEmploymentStatus, talent ID: {}", talentId);
        return ResponseEntity.ok(talentRecruitmentProcessService.getTalentEmploymentStatus(talentId));
    }

    /**
     * 获取当前登录用户是否有"提交在职候选人到新职位"的权限
     * @return boolean
     */
    @GetMapping("/talent-recruitment-processes/submit-onboarded-talent-to-job-permission")
    public ResponseEntity<Boolean> getSubmitOnboardedTalentToJobPermission() {
        log.info("REST request to validate getSubmitOnboardedTalentToJobPermission");
        return ResponseEntity.ok(talentRecruitmentProcessService.getSubmitOnboardedTalentToJobPermission());
    }

    @JobSnapShotAnnotation(noteType = NodeType.SUBMIT_TO_JOB)
    @PostMapping("/talent-recruitment-processes/submit-to-job")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> submitToJob(@RequestBody TalentRecruitmentProcessSubmitToJobVO submitToJobVO) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to SUBMIT_TO_JOB : {}", submitToJobVO);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.submitToJob(submitToJobVO, false);
        setMDC(result,NodeType.SUBMIT_TO_JOB);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/submit-to-job" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    private void setMDC(TalentRecruitmentProcessVO result, NodeType nodeType) {
        Long nodeId = 0L;
        Long talentRecruitmentProcessId = 0L;
        if (nodeType.equals(NodeType.ELIMINATED)) {
            TalentRecruitmentProcessNodeVO node = result.getTalentRecruitmentProcessNodes().get(0);
            nodeId = -1L;
            talentRecruitmentProcessId = node.getTalentRecruitmentProcessId();
        } else {
            for (TalentRecruitmentProcessNodeVO node : result.getTalentRecruitmentProcessNodes()) {
                if (node.getNodeType().equals(nodeType)) {
                    nodeId = node.getId();
                    talentRecruitmentProcessId = node.getTalentRecruitmentProcessId();
                    break;
                }
            }
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("jobId", result.getJobId());
        jsonObject.put("nodeId", nodeId);
        jsonObject.put("processId", talentRecruitmentProcessId);
        jsonObject.put("tenantId", SecurityUtils.getTenantId());
        MDC.put(JobSnapShotAspect.JOB_SNAPSHOT_SYNC_JOB_ID, JSONUtil.toJsonStr(jsonObject));
    }

    @PutMapping("/talent-recruitment-processes/submit-to-job/note")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> submitToJobUpdateNoteOnly(@RequestBody TalentRecruitmentProcessSubmitToJobVO submitToJobVO) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to SUBMIT_TO_JOB(note only): {}", submitToJobVO);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.submitToJob(submitToJobVO, true);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/submit-to-job" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @JobSnapShotAnnotation(noteType = NodeType.SUBMIT_TO_CLIENT)
    @PostMapping("/talent-recruitment-processes/submit-to-client")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> submitToClient(@RequestBody TalentRecruitmentProcessSubmitToClientVO submitToClientVO) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to SUBMIT_TO_CLIENT : {}", submitToClientVO);
        talentRecruitmentProcessService.checkPermission(submitToClientVO.getTalentRecruitmentProcessId());
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.submitToClient(submitToClientVO, false);
        setMDC(result,NodeType.SUBMIT_TO_CLIENT);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/submit-to-client" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @PutMapping("/talent-recruitment-processes/submit-to-client/note")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> submitToClientUpdateNoteOnly(@RequestBody TalentRecruitmentProcessSubmitToClientVO submitToClientVO) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to SUBMIT_TO_CLIENT(note only): {}", submitToClientVO);
        talentRecruitmentProcessService.checkPermission(submitToClientVO.getTalentRecruitmentProcessId());
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.submitToClient(submitToClientVO, true);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/submit-to-client" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @JobSnapShotAnnotation(noteType = NodeType.INTERVIEW)
    @PostMapping("/talent-recruitment-processes/interview")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> interview(@RequestBody TalentRecruitmentProcessInterviewVO interviewVO) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to INTERVIEW : {}", interviewVO);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.interview(interviewVO, false);
        setMDC(result,NodeType.INTERVIEW);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/interview" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @PutMapping("/talent-recruitment-processes/interview/note")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> interviewUpdateNoteOnly(@RequestBody TalentRecruitmentProcessInterviewVO interviewVO) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to INTERVIEW(note only): {}", interviewVO);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.interview(interviewVO, true);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/interview" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @JobSnapShotAnnotation(noteType = NodeType.OFFER)
    @PostMapping("/talent-recruitment-processes/offer")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> offer(@RequestBody TalentRecruitmentProcessOfferVO offer) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to OFFER : {}", offer);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.offer(offer, false);
        setMDC(result,NodeType.OFFER);

        if (BooleanUtils.isTrue(result.getFirstTimeToThisNode())) {
            AgencyActivityForApplicationDTO agencyActivityForApplicationDTO = new AgencyActivityForApplicationDTO(result, NodeType.OFFER);
            agencyService.createAgencyActivity(agencyActivityForApplicationDTO);
        }

        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/offer" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @PutMapping("/talent-recruitment-processes/offer/note")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> offerUpdateNoteOnly(@RequestBody TalentRecruitmentProcessOfferVO offer) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to OFFER(note only): {}", offer);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.offer(offer, true);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/offer" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @JobSnapShotAnnotation(noteType = NodeType.OFFER_ACCEPT)
    @PostMapping("/talent-recruitment-processes/offer-accept")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> offerAccept(@Valid @RequestBody TalentRecruitmentProcessIpgOfferAcceptVO acceptVO) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to OFFER_ACCEPT: {}", acceptVO);
        checkRate(acceptVO.getContractFeeCharge(),acceptVO.getSalaryPackages(),acceptVO.getFeeCharge());
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.offerAccept(acceptVO, false);
        setMDC(result,NodeType.OFFER_ACCEPT);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/offer" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @PostMapping("/talent-recruitment-processes/{talentRecruitmentProcessId}/offer-accept/congrats")
    @NoRepeatSubmit
    public ResponseEntity<Void> sendCongratsNotification(@PathVariable Long talentRecruitmentProcessId) throws IOException {
        log.info("REST request to send congrats notification by talentRecruitmentProcessId: {}", talentRecruitmentProcessId);
        talentRecruitmentProcessService.sendCongratsNotification(talentRecruitmentProcessId);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/talent-recruitment-processes/offer-accept/note")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> offerAcceptUpdateNoteOnly(@RequestBody TalentRecruitmentProcessIpgOfferAcceptVO acceptVO) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to OFFER_ACCEPT(note only): {}", acceptVO);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.offerAccept(acceptVO, true);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/offer" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @JobSnapShotAnnotation(noteType = NodeType.COMMISSION)
    @PostMapping("/talent-recruitment-processes/commission")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> commission(@RequestBody TalentRecruitmentProcessCommissionVO commission) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to COMMISSION : {}", commission);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.commission(commission, false);
        setMDC(result,NodeType.COMMISSION);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/commission" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @PutMapping("/talent-recruitment-processes/commission/note")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> commissionUpdateNoteOnly(@RequestBody TalentRecruitmentProcessCommissionVO commission) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to COMMISSION(note only): {}", commission);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.commission(commission, true);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/commission" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @JobSnapShotAnnotation(noteType = NodeType.ON_BOARD)
    @PostMapping("/talent-recruitment-processes/onboard")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> onboard(@Valid @RequestBody TalentRecruitmentProcessOnboardVO onboard) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to ONBOARD : {}", onboard);
        checkRate(onboard.getContractFeeCharge(),onboard.getSalaryPackages(),onboard.getFeeCharge());
        talentRecruitmentProcessService.checkPermission(onboard.getTalentRecruitmentProcessId());
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.onboard(onboard, false);

        if (BooleanUtils.isTrue(result.getFirstTimeToThisNode())) {
            AgencyActivityForApplicationDTO agencyActivityForApplicationDTO = new AgencyActivityForApplicationDTO(result, NodeType.ON_BOARD);
            agencyService.createAgencyActivity(agencyActivityForApplicationDTO);
        }

        setMDC(result,NodeType.ON_BOARD);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/onboard" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    private void checkRate(TalentRecruitmentProcessIpgContractFeeChargeVO vo,List<TalentRecruitmentProcessOfferSalaryPackageVO> salaryPackageVOS,TalentRecruitmentProcessOfferFeeChargeVO feeChargeVO){
        if (null != vo) {
            if (vo.getFinalBillRate().compareTo(new BigDecimal("99999999")) > 0) {
                throw new CustomParameterizedException("The value cannot exceed 99999999");
            }

            if (vo.getFinalPayRate().compareTo(new BigDecimal("99999999")) > 0) {
                throw new CustomParameterizedException("The value cannot exceed 99999999");
            }
        }

        if (null != feeChargeVO) {
            if (feeChargeVO.getFeeAmount().compareTo(new BigDecimal("99999999")) > 0) {
                throw new CustomParameterizedException("The value cannot exceed 99999999");
            }
        }

        if (null != salaryPackageVOS && !salaryPackageVOS.isEmpty()) {
            salaryPackageVOS.forEach(v -> {
                if (v.getAmount().compareTo(new BigDecimal("99999999")) > 0) {
                    throw new CustomParameterizedException("The value cannot exceed 99999999");
                }
            });
        }
    }

    @PutMapping("/talent-recruitment-processes/onboard/note")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> onboardUpdateNoteOnly(@RequestBody TalentRecruitmentProcessOnboardVO onboard) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to ONBOARD(note only): {}", onboard);
        talentRecruitmentProcessService.checkPermission(onboard.getTalentRecruitmentProcessId());
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.onboard(onboard, true);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/onboard" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @JobSnapShotAnnotation(noteType = NodeType.ELIMINATED)
    @PostMapping("/talent-recruitment-processes/eliminate")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> eliminate(@RequestBody EliminateDTO eliminate) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to ELIMINATE : {}", eliminate);
        //1.eliminate方法比较复杂，优化方案具体见明细
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.eliminate(eliminate);
        setMDC(result,NodeType.ELIMINATED);

        AgencyActivityForApplicationDTO agencyActivityForApplicationDTO = new AgencyActivityForApplicationDTO(result, NodeType.ELIMINATED);
        //2.createAgencyActivity方法，优化方案：可以改为异步
        agencyService.createAgencyActivity(agencyActivityForApplicationDTO);

        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/eliminate" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    /**
     * 候选人离职
     * @param resignationDTO 离职详情
     * @return Void
     */
    @PostMapping("/talent-recruitment-processes/resign")
    @NoRepeatSubmit
    public ResponseEntity<Void> resign(@RequestBody TalentRecruitmentProcessResignationDTO resignationDTO) {
        log.info("REST request to update talent recruitment process to resign : {}", resignationDTO);
        talentRecruitmentProcessResignationService.resign(resignationDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 查询候选人离职日期，如果没有离职，则返回空
     * @param talentRecruitmentProcessId 流程ID
     * @return LocalDate 离职日期
     */
    @GetMapping("/talent-recruitment-processes/{talentRecruitmentProcessId}/resignation")
    public ResponseEntity<TalentRecruitmentProcessResignationVO> getResignation(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId){
        log.info("REST request to get talent resignation by talentRecruitmentProcessId: {}", talentRecruitmentProcessId);
        TalentRecruitmentProcessResignationVO resignationVO = talentRecruitmentProcessResignationService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        return ResponseEntity.ok(resignationVO);
    }

    @GetMapping("/talent-recruitment-processes/talent/{talentId}/resignations")
    public ResponseEntity<List<TalentRecruitmentProcessResignationVO>> getResignationsByTalentId(@PathVariable("talentId") Long talentId){
        log.info("REST request to get talent resignations by talentId: {}", talentId);
        return ResponseEntity.ok(talentRecruitmentProcessResignationService.findByTalentId(talentId));
    }

    /**
     * 接受offer后但未入职，职位关闭
     * @param eliminate
     * @return
     * @throws URISyntaxException
     */
    @PostMapping("/talent-recruitment-processes/onboard/eliminate")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> onboardEliminate(@RequestBody EliminateDTO eliminate) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to ONBOARD ELIMINATE : {}", eliminate);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.onboardEliminate(eliminate);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/onboard/eliminate" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    /**
     * 接受offer后但未入职，取消淘汰
     * @param talentRecruitmentProcessId
     * @return
     * @throws URISyntaxException
     */
    @Deprecated
    @PostMapping("/talent-recruitment-processes/onboard/cancel-eliminate/talentRecruitmentProcessId/{talentRecruitmentProcessId}")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> onboardCancelEliminate(@PathVariable Long talentRecruitmentProcessId) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to cancel ELIMINATE: {}", talentRecruitmentProcessId);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.onboardCancelEliminate(talentRecruitmentProcessId);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/onboard/cancel-eliminate" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @PostMapping("/talent-recruitment-processes/cancel-eliminate/talentRecruitmentProcessId/{talentRecruitmentProcessId}")
    @NoRepeatSubmit
    public ResponseEntity<TalentRecruitmentProcessVO> cancelEliminate(@PathVariable Long talentRecruitmentProcessId) throws URISyntaxException {
        log.info("REST request to update talent recruitment process to cancel ELIMINATE: {}", talentRecruitmentProcessId);
        TalentRecruitmentProcessVO result = talentRecruitmentProcessService.cancelEliminate(talentRecruitmentProcessId);
        return ResponseEntity.created(new URI("/api/v3/talent-recruitment-processes/cancel-eliminate" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
            .body(result);
    }

    @PutMapping("/talent-recruitment-processes/note")
    @NoRepeatSubmit
    public ResponseEntity<AllNotesVO> updateNodeByTalentRecruitmentProcessIdAndNodeType(@RequestBody UpdateNoteDTO updateNoteDTO) {
        log.info("REST request to update talent recruitment process node by: {}", updateNoteDTO);
//        talentRecruitmentProcessService.checkPermission(onboard.getTalentRecruitmentProcessId());
        AllNotesVO result = talentRecruitmentProcessService.updateNote(updateNoteDTO);
        return ResponseEntity.ok(result);
    }

    /**
     * GET  /talent-recruitment-processes/:id : get the "id" talentRecruitmentProcess.
     *
     * @param id the id of the talentRecruitmentProcess to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the talentRecruitmentProcess, or with status 404 (Not Found)
     */
    @GetMapping("/talent-recruitment-processes/{id}")
    public ResponseEntity<TalentRecruitmentProcessVO> getTalentRecruitmentProcess(@PathVariable Long id) {
        log.info("REST request to get TalentRecruitmentProcess : {}", id);
        Optional<TalentRecruitmentProcessVO> talentRecruitmentProcess = talentRecruitmentProcessService.findOne(id);
        return ResponseUtil.wrapOrNotFound(talentRecruitmentProcess);
    }

    @GetMapping("/talent-recruitment-processes-brief/{id}")
    public ResponseEntity<TalentRecruitmentProcessVO> getTalentRecruitmentProcessBriefById(@PathVariable("id") Long id) {
        log.info("REST request to get TalentRecruitmentProcess brief: {}", id);
        Optional<TalentRecruitmentProcessVO> talentRecruitmentProcess = talentRecruitmentProcessService.findOneBrief(id);
        return ResponseUtil.wrapOrNotFound(talentRecruitmentProcess);
    }


    @GetMapping("/talent-recruitment-processes/{id}/kpi-users")
    public ResponseEntity<List<TalentRecruitmentProcessKpiUserVO>> getKpiUsersByTalentRecruitmentProcessId(@PathVariable Long id) {
        log.info("REST request to get all kpi users by TalentRecruitmentProcess ID: {}", id);
        List<TalentRecruitmentProcessKpiUserVO> kpiUserVOS = talentRecruitmentProcessKpiUserService.findAllPlainByTalentRecruitmentProcessId(id);
        return ResponseEntity.ok(kpiUserVOS);
    }

    @GetMapping("/talent-recruitment-processes/all/talentId/{talentId}")
    public ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessAllByTalentId(@PathVariable("talentId") Long talentId) {
        log.info("REST request to get all TalentRecruitmentProcess by talentId: {}", talentId);
        return new ResponseEntity<>(talentRecruitmentProcessService.getTalentRecruitmentProcessAllByTalentId(talentId), HttpStatus.OK);
    }

    @Deprecated
    @GetMapping("/talent-recruitment-processes/talentId/{talentId}")
    public ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessByTalentId(@PathVariable("talentId") Long talentId) {
        log.info("REST request to get all TalentRecruitmentProcess by talentId: {}", talentId);
        return new ResponseEntity<>(talentRecruitmentProcessService.getTalentRecruitmentProcessByTalentId(talentId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes-brief/talentId/{talentId}")
    public ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessBriefByTalentId(@PathVariable("talentId") Long talentId) {
        log.info("REST request to get all TalentRecruitmentProcess by talentId: {}", talentId);
        return new ResponseEntity<>(talentRecruitmentProcessService.getTalentRecruitmentProcessBriefByTalentId(talentId), HttpStatus.OK);
    }

    @GetMapping("/talent-all-recruitment-processes-brief/talentId/{talentId}")
    public ResponseEntity<List<TalentRecruitmentProcessVO>> getAllTalentRecruitmentProcessBriefByTalentId(@PathVariable("talentId") Long talentId) {
        log.info("REST request to get all brief TalentRecruitmentProcess by talentId: {}", talentId);
        return new ResponseEntity<>(talentRecruitmentProcessService.getAllTalentRecruitmentProcessBriefByTalentId(talentId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes-count/talentId/{talentId}")
    public ResponseEntity<Integer> countTalentRecruitmentProcessByTalentId(@PathVariable("talentId") Long talentId) {
        log.info("REST request to get all TalentRecruitmentProcess by talentId: {}", talentId);
        return new ResponseEntity<>(talentRecruitmentProcessService.countTalentRecruitmentProcessByTalentId(talentId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/no-object/talentId/{talentId}")
    public ResponseEntity<List<TalentRecruitmentProcessForTalentVO>> getTalentRecruitmentProcessListVOByTalentId(@PathVariable("talentId") Long talentId) {
        log.info("REST request to get all ViewTalentRecruitmentProcess by talentId: {}", talentId);
        return new ResponseEntity<>(talentRecruitmentProcessForTalentService.getTalentRecruitmentProcessByTalentId(talentId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/list/talentId/{talentId}")
    public ResponseEntity<List<TalentRecruitmentProcessForTalentVO>> getTalentRecruitmentProcessListByTalentId(@PathVariable("talentId") Long talentId) {
        log.info("REST request to get all ViewTalentRecruitmentProcess by talentId: {}", talentId);
        return new ResponseEntity<>(talentRecruitmentProcessForTalentService.getTalentRecruitmentProcessByTalentId(talentId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/jobId/{jobId}")
    public ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessByJobId(@PathVariable("jobId") Long jobId) {
        log.info("REST request to get all TalentRecruitmentProcess by jobId: {}", jobId);
        return new ResponseEntity<>(talentRecruitmentProcessService.getTalentRecruitmentProcessByJobId(jobId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes-brief/jobId/{jobId}")
    public ResponseEntity<List<TalentRecruitmentProcessVO>> getTalentRecruitmentProcessBriefByJobId(@PathVariable("jobId") Long jobId) {
        log.info("REST request to get all TalentRecruitmentProcess by jobId: {}", jobId);
        return new ResponseEntity<>(talentRecruitmentProcessService.getTalentRecruitmentProcessBriefByJobId(jobId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/no-object/jobId/{jobId}")
    public ResponseEntity<List<TalentRecruitmentProcessForJobDetailVO>> getTalentRecruitmentProcessForJobDetailByJobId(@PathVariable("jobId") Long jobId,
                                                                                                                       @RequestParam(value = "search", required = false) String search,
                                                                                                                       @RequestParam(value = "filter", required = false) NodeSearchFilter filter,
                                                                                                                       @PageableDefault @SortDefault(sort = "id", direction = Sort.Direction.ASC) Pageable pageable) {
        log.info("REST request to get all TalentRecruitmentProcess for job detail by jobId: {}", jobId);
        Page<TalentRecruitmentProcessForJobDetailVO> res = talentRecruitmentProcessForJobDetailService.getTalentRecruitmentProcessByJobId(jobId, search, filter, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(res, "/api/v3/talent-recruitment-processes/no-object/jobId/" + jobId);
        return new ResponseEntity<>(res.getContent(), headers, HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/no-object/jobId/{jobId}/stats")
    public ResponseEntity<TalentRecruitmentProcessForJobCountByStatusVO> getTalentRecruitmentProcessForJobStatsByJobId(@PathVariable("jobId") Long jobId, @RequestParam(value = "search", required = false) String search) {
        log.info("REST request to get all TalentRecruitmentProcess for job stats by jobId: {}", jobId);
        TalentRecruitmentProcessForJobCountByStatusVO res = talentRecruitmentProcessForJobDetailService.getTalentRecruitmentProcessForJobStatsByJobId(jobId, search);
        return new ResponseEntity<>(res, HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/jobId/last/{jobId}")
    public ResponseEntity<TalentRecruitmentProcessVO> getTalentRecruitmentProcessLastByJobId(@PathVariable("jobId") Long jobId) {
        log.info("REST request to get all TalentRecruitmentProcess by jobId: {}", jobId);
        return new ResponseEntity<>(talentRecruitmentProcessService.getTalentRecruitmentProcessLastByJobId(jobId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/jobId/{jobId}/last-interview-datetime")
    public ResponseEntity<String> getTalentRecruitmentProcessLastInterviewDateTimeByJobId(@PathVariable("jobId") Long jobId) {
        log.info("REST request to get all TalentRecruitmentProcess last interview datetime by jobId: {}", jobId);
        return new ResponseEntity<>(talentRecruitmentProcessService.getTalentRecruitmentProcessLastInterviewDateTimeByJobId(jobId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/talentId/{talentId}/jobId/{jobId}")
    public ResponseEntity<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByTalentIdAndJobId(
            @ApiParam(value = "talent id", required = true) @PathVariable("talentId") Long talentId,
            @ApiParam(value = "job id", required = true) @PathVariable("jobId") Long jobId) throws URISyntaxException {
        log.info("REST request to get TalentRecruitmentProcess. talentId: {}, jobId: {}", talentId, jobId);
        TalentRecruitmentProcessVO talentRecruitmentProcess = talentRecruitmentProcessService.getTalentRecruitmentProcessByTalentIdAndJobId(talentId, jobId);
        return ResponseEntity.created(new URI("/api/v1/talent-recruitment-processes/talentId/" + talentId + "/jobId/" + jobId))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, talentRecruitmentProcess != null ? talentRecruitmentProcess.getId().toString() : ""))
                .body(talentRecruitmentProcess);
    }

    @GetMapping("/talent-recruitment-processes/kpi-users/jobId/{jobId}")
    public ResponseEntity<List<TalentRecruitmentProcessKpiUserVO>> getKPIUsersByJobId(@PathVariable("jobId") Long jobId) {
        log.info("REST request to get all TalentRecruitmentProcessKPIUsers by jobId: {}", jobId);
        return new ResponseEntity<>(talentRecruitmentProcessKpiUserService.findAllByJobId(jobId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/talentId/{talentId}/nodeType/{nodeType}")
    public ResponseEntity<Integer> countApplicationByTalentId(@PathVariable("talentId") Long talentId, @PathVariable("nodeType") Integer nodeType){
        log.info("REST request to count TalentRecruitmentProcess by talentId:{} and nodeType: {}", talentId, nodeType);
        return ResponseEntity.ok(talentRecruitmentProcessService.countApplicationByTalentId(talentId, nodeType));
    }

    @GetMapping("/talent-recruitment-processes/download/{id}")
    @Timed
    @NoRepeatSubmit
    public void downloadDetailsOfCharges(HttpServletResponse response, @PathVariable Long id, @RequestParam(value = "language", defaultValue = "en", required = false) String language) {
        log.info("[APN: Invoice @{}] REST request to download pdf Id : {}", SecurityUtils.getUserId(), id);
        talentRecruitmentProcessService.downloadDetailsOfChargesPdf(response, id, language);
    }

    @ApiOperation("Count unfinished applications by job id")
    @GetMapping("/talent-recruitment-processes/job-id/{jobId}/in-process")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> countUnfinishedApplicationsForJob(@PathVariable Long jobId) {
        log.info("[APN: Invoice @{}] REST request to count unfinished applications for job : {}", SecurityUtils.getUserId(), jobId);
        return ResponseEntity.ok(talentRecruitmentProcessService.countUnfinishedApplicationsForJob(jobId));
    }

    @ApiOperation("Count unfinished applications by job id list")
    @PostMapping("/talent-recruitment-processes/in-process")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Map<Long, Long>> countUnfinishedApplicationsForJobs(@RequestBody List<Long> jobIds) {
        log.info("[APN: Invoice @{}] REST request to count unfinished applications for jobs : {}", SecurityUtils.getUserId(), jobIds);
        return ResponseEntity.ok(talentRecruitmentProcessService.countUnfinishedApplicationsForJobs(jobIds));
    }

    @ApiOperation("Count unfinished applications by job id list")
    @PostMapping("/talent-recruitment-processes/in-process/by-application-ids/count")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Integer> countUnfinishedApplicationsByIds(@RequestBody List<Long> applicationIds) {
        log.info("[APN: TalentRecruitmentProcessResource @{}] REST request to count unfinished applications by applicationIds : {}", SecurityUtils.getUserId(), applicationIds);
        return ResponseEntity.ok(talentRecruitmentProcessService.countUnfinishedApplicationsByApplicationIds(applicationIds));
    }

    @ApiOperation("Count unfinished applications by job id list")
    @PostMapping("/talent-recruitment-processes/in-process/by-application-ids")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<InProcessApplicationBriefVM>> getUnfinishedApplicationsByIds(@RequestBody List<Long> applicationIds) {
        log.info("[APN: TalentRecruitmentProcessResource @{}] REST request to get unfinished applications by applicationIds : {}", SecurityUtils.getUserId(), applicationIds);
        return ResponseEntity.ok(talentRecruitmentProcessService.getUnfinishedApplicationsByApplicationIds(applicationIds));
    }

    @ApiOperation("Count unfinished applications by job id list")
    @PostMapping("/talent-recruitment-processes/brief-info/by-application-ids")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<List<ApplicationBriefInfoVM>> getApplicationsBriefInfoByIds(@RequestBody List<Long> applicationIds) {
        log.info("[APN: TalentRecruitmentProcessResource @{}] REST request to get applications brief ino by applicationIds : {}", SecurityUtils.getUserId(), applicationIds);
        return ResponseEntity.ok(talentRecruitmentProcessService.getApplicationsBriefInoByApplicationIds(applicationIds));
    }

    @GetMapping("/talent-recruitment-processes/note")
    public ResponseEntity<AllNotesVO> getAllNotesByTalentRecruitmentProcessId(@RequestParam(value = "talentRecruitmentProcessId") Long talentRecruitmentProcessId) throws URISyntaxException {
        log.info("REST request to get all talent recruitment process notes: {}", talentRecruitmentProcessId);
//        talentRecruitmentProcessService.checkPermission(talentRecruitmentProcessId);
        AllNotesVO result = talentRecruitmentProcessService.getAllNotes(talentRecruitmentProcessId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/talent-recruitment-processes/{id}/stages")
    public ResponseEntity<HistoryStagesVo> getStages(@PathVariable(name = "id") Long id) {
        log.info("REST request to get stages for talent recruitment process id: {}", id);
        return ResponseEntity.ok(talentRecruitmentProcessService.getStages(id));
    }

    @GetMapping("/talent-recruitment-processes/{talentRecruitmentProcessId}/stages/{stageType}")
    public ResponseEntity<Object> getStageByType(@PathVariable(name = "talentRecruitmentProcessId") Long talentRecruitmentProcessId, @PathVariable(name = "stageType") NodeType nodeType) {
        log.info("REST request to get stages for talent recruitment process id: {}, stage type: {}", talentRecruitmentProcessId, nodeType);
        return ResponseEntity.ok(talentRecruitmentProcessService.getStageByType(talentRecruitmentProcessId, nodeType));
    }


    @ApiOperation(value = "Replace submit to job resume")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping("/talent-recruitment-processes/replace/submit-to-job/resume")
    @Timed
    @NoRepeatSubmit
    public ResponseEntity<Void> replaceSubmitToJobResume(@RequestBody ReplaceSubmitToJobResumeDTO replaceSubmitToJobResumeDTO)  {
        log.info("[APN: TalentResume @{}] REST request to save replaceSubmitToJobResume : {}", SecurityUtils.getUserId(), replaceSubmitToJobResumeDTO);
        talentRecruitmentProcessService.replaceSubmitToJobResume(replaceSubmitToJobResumeDTO);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/talent-recruitment-processes/user/{userId}/filter-talent-ids")
    public ResponseEntity<Set<Long>> filterTalentIdsByUserIdAndTalentIds(@PathVariable("userId") Long userId, @RequestBody Set<Long> talentIds) {
        log.info("REST request to filter TalentIds By UserId ({}) And TalentIds: {}", userId, talentIds);
        return new ResponseEntity<>(talentRecruitmentProcessService.filterTalentIdsByUserIdAndTalentIds(userId, talentIds), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/stats")
    public ResponseEntity<ApplicationIdAndStatusListVO> getApplicationStatsByIds(@RequestParam(value = "talentRecruitmentProcessIds") Set<Long> talentRecruitmentProcessIds, @RequestParam(required = false) NodeType nodeType, Pageable pageable) throws URISyntaxException {
        log.info("REST request to get all applications stats by ids: {} and nodeType: {}", talentRecruitmentProcessIds, nodeType);
//        talentRecruitmentProcessService.checkPermission(talentRecruitmentProcessId);
        Page<ApplicationIdAndStatusVO> result = talentRecruitmentProcessService.getStats(talentRecruitmentProcessIds, nodeType, pageable);

//        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(result, "/api/v3/talent-recruitment-processes/stats");
//        return ResponseEntity.ok().headers(headers).body(result.getContent());
        ApplicationIdAndStatusListVO res = new ApplicationIdAndStatusListVO();
        res.setApplications(result.getContent());
        res.setTotal(result.getTotalElements());
        return ResponseEntity.ok(res);
    }

    @DeleteMapping("/talent-recruitment-processes/job/{jobId}")
    public ResponseEntity<JSONArray> deleteByJobId(@PathVariable(value = "jobId") Long jobId) throws URISyntaxException {
        log.info("REST request to delete applications by jobId: {}", jobId);
        JSONArray result = talentRecruitmentProcessService.deleteByJobId(jobId);
        return ResponseEntity.ok(result);
    }

    @GetMapping("/talent-recruitment-processes/job/{jobId}/substitute-talent")
    public ResponseEntity<List<SubstituteTalentVO>> getSubstituteTalentByJobId(@PathVariable(name = "jobId") Long jobId){
        log.info("REST request to delete applications by jobId: {}", jobId);
        List<SubstituteTalentVO> result = talentRecruitmentProcessService.getSubstituteTalentByJobId(jobId);
        return ResponseEntity.ok(result);
    }

    @PostMapping("/talent-recruitment-processes/updateSubstituteTalentIdByTalentRecruitmentProcessId")
    public ResponseEntity<Void> updateSubstituteTalentIdByTalentRecruitmentProcessId(@RequestBody TalentRecruitmentProcessUpdateSubstituteTalentDTO substituteTalentDTO){
        log.info("REST request to update talent recruitment process substitute talent: {}", substituteTalentDTO);
        talentRecruitmentProcessOnboardService.updateSubstituteTalentIdByTalentRecruitmentProcessId(substituteTalentDTO);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/talent-recruitment-processes/resigned-user/{userId}/recalculate-commission")
    public ResponseEntity<Void> recalculateOwnerCommission(@PathVariable("userId") Long userId){
        log.info("REST request to recalculate commission after user resigned: {}", userId);
        talentRecruitmentProcessKpiUserService.recalculateOwnerCommission(userId);
        return ResponseEntity.ok().build();
    }


    @ApiOperation("Count unfinished applications by job id list")
    @PostMapping("/talent-recruitment-processes/in-process/internal")
    @Timed
    public ResponseEntity<Map<Long, Long>> countUnfinishedApplicationsForJobsInternalUseOnly(@RequestBody List<Long> jobIds) {
        log.info("[APN: Invoice @{}] REST request to count unfinished applications for jobs (INTERNAL USE ONLY) : {}", SecurityUtils.getUserId(), jobIds);
        return ResponseEntity.ok(talentRecruitmentProcessService.countUnfinishedApplicationsForJobs(jobIds));
    }
}
