package com.altomni.apn.application.web.rest;

import com.altomni.apn.application.service.xxljob.XxlJobService;
import com.altomni.apn.common.dto.xxljob.XxlJobInvoiceOverdueDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class XxlJobForOverDueResource {

    @Resource
    private XxlJobService xxlJobService;

    /**
     * 创建 逾期未回款提醒
     * @param xxlJobInvoiceOverdueDTO
     * @return
     */
    @PostMapping("/xxl-job/invoice-overdue-reminder")
    public ResponseEntity<Void> createInvoiceOverDueReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        log.info("[APN: XxlJobForOverDueResource @{}] REST request to get createInvoiceOverDueReminder, param = {}", SecurityUtils.getUserId(), xxlJobInvoiceOverdueDTO);
        xxlJobService.createInvoiceOverDueReminder(xxlJobInvoiceOverdueDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 创建入职未开票提醒通过发票void
     * @param xxlJobInvoiceOverdueDTO
     * @return
     */
    @PostMapping("/xxl-job/onboard-no-invoice-reminder-by-invoice-void")
    public ResponseEntity<Void> candidateOnboardNoInvoiceReminderByInvoiceVoid(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        log.info("[APN: XxlJobForOverDueResource @{}] REST request to get candidateOnboardInvoiceReminderByInvoiceVoid, param = {}", SecurityUtils.getUserId(), xxlJobInvoiceOverdueDTO);
        xxlJobService.onboardNoInvoiceReminderByInvoiceVoid(xxlJobInvoiceOverdueDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 删除入职未开票提醒
     * @param xxlJobInvoiceOverdueDTO
     * @return
     */
    @DeleteMapping("/xxl-job/onboard-no-invoice-reminder")
    public ResponseEntity<Void> deleteOnboardNoInvoiceReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        log.info("[APN: XxlJobForOverDueResource @{}] REST request to get delete invoice overdue reminder, param = {}", SecurityUtils.getUserId(), xxlJobInvoiceOverdueDTO);
        xxlJobService.deleteOnboardNoInvoiceReminder(xxlJobInvoiceOverdueDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 删除发票逾期提醒
     * @param xxlJobInvoiceOverdueDTO
     * @return
     */
    @DeleteMapping("/xxl-job/invoice-overdue-reminder")
    public ResponseEntity<Void> deleteInvoiceOverDueReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        log.info("[APN: XxlJobForOverDueResource @{}] REST request to get delete invoice overdue reminder, param = {}", SecurityUtils.getUserId(), xxlJobInvoiceOverdueDTO);
        xxlJobService.deleteInvoiceOverDueReminder(xxlJobInvoiceOverdueDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * 修改发票逾期提醒，contract 可以修改 dueDate
     * @param xxlJobInvoiceOverdueDTO
     * @return
     */
    @PutMapping("/xxl-job/invoice-overdue-reminder")
    public ResponseEntity<Void> updateInvoiceOverDueReminder(@RequestBody XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        log.info("[APN: XxlJobForOverDueResource @{}] REST request to update createInvoiceOverDueReminder, param = {}", SecurityUtils.getUserId(), xxlJobInvoiceOverdueDTO);
        xxlJobService.updateInvoiceOverDueReminder(xxlJobInvoiceOverdueDTO);
        return ResponseEntity.ok().build();
    }

}
