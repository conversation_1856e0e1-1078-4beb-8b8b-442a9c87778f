package com.altomni.apn.application.service.job.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.application.repository.JobSnapshotRepository;
import com.altomni.apn.application.service.job.JobSnapshotService;
import com.altomni.apn.common.domain.job.JobSnapshot;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;

@Service
@Slf4j
public class JobSnapshotServiceImpl implements JobSnapshotService {

    @Resource
    JobSnapshotRepository jobSnapshotRepository;

    @Override
    public boolean checkSnapshotExist(String snapshotHash) {
        JobSnapshot jobSnapshot = jobSnapshotRepository.findBySnapshotHash(snapshotHash);
        return jobSnapshot == null ? true : false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(JobSnapshot bean) {
        bean.setCreatedDate(Instant.now());
        jobSnapshotRepository.save(bean);
        log.info("[JobSnapshotServiceImpl] save job snapshot info :{}", JSONUtil.toJsonStr(bean));
    }
}
