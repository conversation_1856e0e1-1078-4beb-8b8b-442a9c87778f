package com.altomni.apn.application.service.rule.ipg.impl;

import com.altomni.apn.application.domain.RecruitmentProcess;
import com.altomni.apn.application.repository.RecruitmentProcessRepository;
import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.repository.TalentRecruitmentProcessRepository;
import com.altomni.apn.application.service.job.JobService;
import com.altomni.apn.application.service.rule.ipg.TalentRecruitmentProcessIpgOfferAcceptRule;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Optional;

@Service
@Transactional
public class TalentRecruitmentProcessIpgOfferAcceptRuleImpl implements TalentRecruitmentProcessIpgOfferAcceptRule {

    @Resource
    private JobService jobService;
    @Resource
    private TalentRecruitmentProcessRepository talentRecruitmentProcessRepository;

    @Resource
    private RecruitmentProcessRepository recruitmentProcessRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Override
    public void validateAndUpdateJobStatusToFilled(Long jobId) {
        JobDTOV3 job = jobService.getJob(jobId);
        Integer numberOfOfferAccepted = talentRecruitmentProcessRepository.countByJobId(job.getId());
        RecruitmentProcess recruitmentProcess = recruitmentProcessRepository.findById(job.getRecruitmentProcess().getId()).orElseThrow();
        if (!JobType.PAY_ROLL.equals(recruitmentProcess.getJobType())) {
            if (JobStatus.FILLED.equals(job.getStatus()) || numberOfOfferAccepted >= job.getOpenings()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.IPG_VALIDATEANDUPDATEJOBSTATUSTOFILLED.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
            if (numberOfOfferAccepted.equals(job.getOpenings() - 1)) {
                jobService.updateStatus(job.getId(), JobStatus.FILLED);
            }
        }
    }
}
