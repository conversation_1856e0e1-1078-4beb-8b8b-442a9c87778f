package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.SalaryType;
import com.altomni.apn.common.domain.enumeration.application.SalaryTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A TalentRecruitmentProcessOfferSalaryPackage.
 */
@Entity
@Table(name = "talent_recruitment_process_offer_salary_package")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOfferSalaryPackage extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 8710619910850374801L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @JsonIgnore
    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Convert(converter = SalaryTypeConverter.class)
    @Column(name = "salary_type")
    private SalaryType salaryType;

    @Column(name = "amount", precision=20, scale=2)
    private BigDecimal amount;

    @Column(name = "need_charge")
    private Boolean needCharge;
}
