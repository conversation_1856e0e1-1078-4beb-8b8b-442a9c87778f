package com.altomni.apn.application.service.talent;

import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

public interface TalentService {

    TalentDTOV3 getTalentWithoutEntity(Long id);

    TalentBriefDTO getTalentBrief(Long id);

    List<TalentOwnership> getAllTalentOwners(Long talentId, List<TalentOwnershipType> talentOwnershipType);

    List<TalentOwnership> saveAllOwnerships(List<TalentOwnership> talentOwnerships);

    TalentResumeDTO getTalentResumeByTalentResumeRelationId(Long resumeId);

    void updateTalentExperience(Long talentId, TalentExperienceDTO talentExperienceDTO);

    void deleteTalentExperience(Long talentId, Long talentRecruitmentProcessId);
}
