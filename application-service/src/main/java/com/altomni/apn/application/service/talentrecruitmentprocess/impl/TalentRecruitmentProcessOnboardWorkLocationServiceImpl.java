package com.altomni.apn.application.service.talentrecruitmentprocess.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.application.config.env.ApplicationProperties;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardDate;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardWorkLocation;
import com.altomni.apn.application.dto.LocationESDTO;
import com.altomni.apn.application.repository.TalentRecruitmentProcessOnboardDateRepository;
import com.altomni.apn.application.repository.TalentRecruitmentProcessOnboardWorkLocationRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessOnboardDateService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessOnboardWorkLocationService;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.common.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.concurrent.CompletableFuture;

import static com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_COMPANY_SEARCH_HISTORY;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessOnboardWorkLocation}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessOnboardWorkLocationServiceImpl implements TalentRecruitmentProcessOnboardWorkLocationService {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessOnboardWorkLocationServiceImpl.class);

    @Resource
    private TalentRecruitmentProcessOnboardWorkLocationRepository talentRecruitmentProcessOnboardWorklocationRepository;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private HttpService httpService;

    @Override
    public TalentRecruitmentProcessOnboardWorkLocation save(Long talentRecruitmentProcessId, TalentRecruitmentProcessOnboardWorkLocation talentRecruitmentProcessOnboardWorkLocation) {
        if (talentRecruitmentProcessOnboardWorkLocation == null) {
            return null;
        }
        TalentRecruitmentProcessOnboardWorkLocation exist = talentRecruitmentProcessOnboardWorklocationRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (exist != null) {
            // update
            if (!StringUtils.equals(exist.getOriginalLoc(), talentRecruitmentProcessOnboardWorkLocation.getOriginalLoc())) {
                ServiceUtils.myCopyProperties(talentRecruitmentProcessOnboardWorkLocation, exist);

                CompletableFuture.supplyAsync(() -> {
                    String url = applicationProperties.getEsfillerBaseUrl() + "/v3/officialize_location";
                    try {
                        HttpResponse response = httpService.post(url, talentRecruitmentProcessOnboardWorkLocation.getOriginalLoc());
                        if (response.getCode() == 200 && StringUtils.isNotBlank(response.getBody())) {
                            System.out.println(response.getBody());
                            LocationESDTO locationESDTO = JSON.parseObject(response.getBody(), LocationESDTO.class);
                            talentRecruitmentProcessOnboardWorklocationRepository.updateOfficializedWorkLocationByTalentRecruitmentProcessId(talentRecruitmentProcessId, locationESDTO.getOfficialCity(), locationESDTO.getOfficialCounty(), locationESDTO.getOfficialProvince(), locationESDTO.getOfficialCountry());
                        } else {
                            log.error("[TalentRecruitmentProcessOnboardWorkLocationServiceImpl: save] async save officialized location from esfiller ERROR, for talentRecruitmentProcessId: {}, with original location string: {}, response code is: {}, body is {}", talentRecruitmentProcessId, talentRecruitmentProcessOnboardWorkLocation.getOriginalLoc(), response.getCode(), response.getBody());
                        }
                    } catch (IOException e) {
                        log.error("[TalentRecruitmentProcessOnboardWorkLocationServiceImpl: save] async save officialized location from esfiller IOException, for talentRecruitmentProcessId: {}, with original location string: {}", talentRecruitmentProcessId, talentRecruitmentProcessOnboardWorkLocation.getOriginalLoc());
//                throw new RuntimeException(e);
                    }
                    return 0;
                });
                return talentRecruitmentProcessOnboardWorklocationRepository.saveAndFlush(exist);
            }
        }
        // create
        talentRecruitmentProcessOnboardWorkLocation.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
        CompletableFuture.supplyAsync(() -> {
            String url = applicationProperties.getEsfillerBaseUrl() + "/v3/officialize_location";
            try {
                HttpResponse response = httpService.post(url, talentRecruitmentProcessOnboardWorkLocation.getOriginalLoc());
                if (response.getCode() == 200 && StringUtils.isNotBlank(response.getBody())) {
                    LocationESDTO locationESDTO = JSON.parseObject(response.getBody(), LocationESDTO.class);
                    talentRecruitmentProcessOnboardWorklocationRepository.updateOfficializedWorkLocationByTalentRecruitmentProcessId(talentRecruitmentProcessId, locationESDTO.getOfficialCity(), locationESDTO.getOfficialCounty(), locationESDTO.getOfficialProvince(), locationESDTO.getOfficialCountry());
                } else {
                    log.error("[TalentRecruitmentProcessOnboardWorkLocationServiceImpl: save] async save officialized location from esfiller ERROR, for talentRecruitmentProcessId: {}, with original location string: {}, response code is: {}, body is {}", talentRecruitmentProcessId, talentRecruitmentProcessOnboardWorkLocation.getOriginalLoc(), response.getCode(), response.getBody());
                }
            } catch (IOException e) {
                log.error("[TalentRecruitmentProcessOnboardWorkLocationServiceImpl: save] async save officialized location from esfiller IOException, for talentRecruitmentProcessId: {}, with original location string: {}", talentRecruitmentProcessId, talentRecruitmentProcessOnboardWorkLocation.getOriginalLoc());
//                throw new RuntimeException(e);
            }
            return 0;
        });
        return talentRecruitmentProcessOnboardWorklocationRepository.saveAndFlush(talentRecruitmentProcessOnboardWorkLocation);
    }

    @Override
    public TalentRecruitmentProcessOnboardWorkLocation findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return talentRecruitmentProcessOnboardWorklocationRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
    }
}
