package com.altomni.apn.application.service.pipeline;

import com.altomni.apn.application.domain.pipeline.ViewPipeline;
import com.altomni.apn.common.dto.application.pipeline.MyPipelineSearchParam;
import com.altomni.apn.common.dto.application.pipeline.ViewPipelineResult;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * Service Interface for managing {@link ViewPipeline}.
 */
public interface ViewPipelineService {

    Page<Object[]> findAll(MyPipelineSearchParam requestParam,  Pageable pageable);

    ViewPipelineResult toViewPipelineResult(List<Object[]> viewPipelines, MyPipelineSearchParam pipelineSearchParam);
}
