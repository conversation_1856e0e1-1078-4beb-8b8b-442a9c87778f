package com.altomni.apn.application.service.pipeline.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.application.config.constant.Constants;
import com.altomni.apn.application.domain.pipeline.ViewPipeline;
import com.altomni.apn.application.domain.vm.TalentNameVM;
import com.altomni.apn.application.repository.RecruitmentProcessRepository;
import com.altomni.apn.application.service.pipeline.ViewPipelineService;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.dto.application.pipeline.MyPipelineSearchParam;
import com.altomni.apn.common.dto.application.pipeline.ViewPipelineResult;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.dto.user.FullNameUserDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.service.talent.TalentFeignClient;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.StringUtil;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.job.service.user.UserService;
import com.altomni.apn.talent.domain.vm.record.UserNameVM;
import com.altomni.apn.user.config.UserPersonalizationConfig;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamMemberSearchVM;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.NoResultException;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Service Implementation for managing .
 */
@Service
@Transactional
public class ViewPipelineServiceImpl implements ViewPipelineService {

    private final Logger log = LoggerFactory.getLogger(ViewPipelineServiceImpl.class);

    @PersistenceContext
    private EntityManager entityManager;

    @Resource
    private RecruitmentProcessRepository recruitmentProcessRepository;
    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Resource
    private UserService userService;

    @Resource
    private TalentFeignClient talentFeignClient;

    @Override
    public Page<Object[]> findAll(MyPipelineSearchParam requestParam, Pageable pageable) {
        if(!checkSearchPermission(requestParam)) {
            throw new CustomParameterizedException("No permission access the user/team.");
        }

        String countQuery = "SELECT COUNT(DISTINCT trp.id) " +
                "        FROM talent_recruitment_process trp " +
                "            LEFT JOIN talent_recruitment_process_resignation resign on trp.id = resign.talent_recruitment_process_id " +
                "            LEFT JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id " +
                "            LEFT JOIN job j ON j.id = trp.job_id " +
                "            LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id " +
                "            LEFT JOIN talent t ON t.id = trp.talent_id " +
                "            LEFT JOIN talent_additional_info t_info ON t_info.id = t.additional_info_id" +
                "            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id" +
                "            LEFT JOIN company c on j.company_id = c.id" +
                "            LEFT JOIN talent_contact tc on tc.talent_id = t.id and tc.jhi_type in(0,2) and tc.status = 0 " +
                "            LEFT JOIN talent_contact tcphone on tcphone.talent_id = t.id and tcphone.jhi_type in(1,16) and tcphone.status = 0 and (tcphone.verification_status is null or tcphone.verification_status <> 9) " +
                "            LEFT JOIN job_company_contact_relation jcr on j.id = jcr.job_id" +
                (ObjectUtil.isNull(requestParam.getHiringManager())?" LEFT JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 ":" INNER JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1  " ) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate())?" LEFT JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 ":" INNER JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2  " ) +
                (ObjectUtil.isNull(requestParam.getMsp())?" LEFT JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 ":" INNER JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3  " ) +
                "            LEFT JOIN talent_recruitment_process_kpi_user recruiter on recruiter.talent_recruitment_process_id = trp.id and recruiter.user_role=1" +
                "            LEFT JOIN user u on recruiter.user_id = u.id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user am on am.talent_recruitment_process_id = trp.id and am.user_role=0" +
                "            LEFT JOIN talent_recruitment_process_submit_to_job stj on stj.talent_recruitment_process_id = trp.id" +
                "            LEFT JOIN user uam on am.user_id = uam.id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user coam on coam.talent_recruitment_process_id = trp.id and coam.user_role=7" +
                "            LEFT JOIN user ucoam on coam.user_id = ucoam.id" +
                "            LEFT JOIN start s on s.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN invoice inv on inv.start_id = s.id AND inv.STATUS != 7 " +
                "            LEFT JOIN timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN t_contractor_invoice tcin on tcin.assignment_id = tta.id and tcin.invoice_status != 3 " +
                "            LEFT JOIN talent_recruitment_process_stop_statistics trpss on trpss.talent_recruitment_process_id = trp.id " +
                "            LEFT JOIN agency_submit_application asa ON asa.talent_recruitment_process_id = trp.id " +
                "        WHERE trpn.node_status in(1,4)" +
                "          and trp.tenant_id = ?1 " +
                "          and trpku.user_id in (?2)  " +
                (ObjectUtil.isNull(requestParam.getHiringManager()) ? "" : " and hm.talent_id = " + requestParam.getHiringManager()) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate()) ? "" : " and hr.talent_id = " + requestParam.getHrCoordinate()) +
                (ObjectUtil.isNull(requestParam.getMsp()) ? "" : " and msp.talent_id = " + requestParam.getMsp()) +
                "          and trp.last_modified_date >= ?3 " +
                "          and trp.last_modified_date < ?4 " + additionalCondition(requestParam, true);

        StringBuilder orderBy = new StringBuilder(" ");
        Sort sort = pageable.getSort();
        boolean orderByStatusAsc = addSorting(orderBy, sort.isSorted() ? sort : null);
        String dataQuery = """
                 select n.* from ( 
                 SELECT            trp.id talentRecruitmentProcessId, 
                                   trp.tenant_id tenantId, 
                                   trp.talent_id talentId, 
                                   t.full_name talentName, 
                                   JSON_EXTRACT(t_info.extended_info, '$.skills') talentSkills, 
                                   group_concat(tc.contact ORDER BY tc.sort,tc.id) talentEmails, 
                                   trp.job_id jobId, 
                                   j.title jobTitle, 
                                   l.locations jobLocations, 
                                   rp.job_type jobType, 
                                   j.company_id companyId, 
                                   c.full_business_name companyName,
                                   (IF(trpn.node_status = 1, trpn.node_type, ?7)) nodeType, 
                                   trpn.node_status nodeStatus, 
                                   if(trpn.node_type=10, if(trpn.node_status=1, stj.recommend_comments, trp.note), trp.note) currentStatusNote,
                                   thm.full_name as hiringManager,
                                   thr.full_name as hrCoordinate,
                                   tmsp.full_name as msp,
                                   GROUP_CONCAT( DISTINCT CONCAT(u.first_name, ' ', u.last_name) ORDER BY u.id ) recruiter,
                                   GROUP_CONCAT( DISTINCT CONCAT(uam.first_name, ' ', uam.last_name) ORDER BY uam.id ) accountManager,
                                   trpku.user_id userId,
                                   trpku.user_role userRole,
                                   trp.last_modified_date lastModifiedDate,
                                   trpe.reason reason, 
                                   CASE WHEN inv.id IS NULL AND tcin.id IS NULL THEN 0 ELSE 1 END AS invoiceFlag,   
                                   group_concat(tcphone.contact ORDER BY tcphone.sort,tcphone.id) talentPhones, 
                                   trp.recruitment_process_id AS recruitmentPorcessId, 
                                   coalesce(interview_counts.interview_count, 0) AS interview_count, 
                                   j.pteam_id AS teamId,
                                   stj.created_date submitToJobTime,
                                   trpn.node_type lastNodeType, 
                                   JSON_UNQUOTE(JSON_EXTRACT(t_info.extended_info, '$.nickName')) nickName, 
                                   IF(trpss.id IS NOT NULL, 'true', 'false') stopProcess, 
                                   IF(j.flexible_location = 1, 'true', 'false') flexibleLocation, 
                                   TIMESTAMPDIFF(MINUTE, trp.last_modified_date, CURRENT_TIMESTAMP) AS overdue, 
                                   stc.created_date submitToClientTime,
                                   trpo.created_date offerTime,
                                   trpon.created_date onboardTime,
                                   ( SELECT MAX(created_date) FROM talent_recruitment_process_interview WHERE talent_recruitment_process_id = trp.id ) AS interviewTime, 
                                   resign.id AS resignId, 
                                   star.id AS startId, 
                                   GROUP_CONCAT( DISTINCT concat(CONCAT(ucoam.first_name, ' ', ucoam.last_name), '-', coam.country) ORDER BY ucoam.id) cooperateAccountManager, 
                                   JSON_UNQUOTE(JSON_EXTRACT(t_info.extended_info, '$.source')) AS source, 
                                   asa.agency_id as agencyId, 
                                   asj.share_status as agencySharingJobStatus,
                                   GROUP_CONCAT( DISTINCT CONCAT(uslowner.first_name, ' ', uslowner.last_name) ORDER BY uslowner.id ) salesLeadOwner,
                                   GROUP_CONCAT( DISTINCT CONCAT(ubdowner.first_name, ' ', ubdowner.last_name) ORDER BY ubdowner.id ) bdOwner
                        FROM talent_recruitment_process trp 
                            LEFT JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id 
                            LEFT JOIN job j ON j.id = trp.job_id 
                            LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id 
                            LEFT JOIN talent t ON t.id = trp.talent_id 
                            LEFT JOIN talent_additional_info t_info ON t_info.id = t.additional_info_id
                            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id
                            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id
                            LEFT JOIN company c on j.company_id = c.id
                            LEFT JOIN talent_contact tc on tc.talent_id = t.id and tc.jhi_type in(0,2) and tc.status = 0 
                            LEFT JOIN talent_contact tcphone on tcphone.talent_id = t.id and tcphone.jhi_type in(1,16) and tcphone.status = 0 and(tcphone.verification_status is null or  tcphone.verification_status <> 9) 
                            LEFT JOIN job_company_contact_relation jcr on j.id = jcr.job_id
    """ +
                (ObjectUtil.isNull(requestParam.getHiringManager())? " LEFT JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 ":" INNER JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1  " ) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate())?" LEFT JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 ":" INNER JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2  " ) +
                (ObjectUtil.isNull(requestParam.getMsp())?" LEFT JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 ":" INNER JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3  " ) +
                """
                            LEFT JOIN talent thm on hm.talent_id = thm.id 
                            LEFT JOIN talent thr on hr.talent_id = thr.id 
                            LEFT JOIN talent tmsp on msp.talent_id = tmsp.id 
                            LEFT JOIN talent_recruitment_process_kpi_user recruiter on recruiter.talent_recruitment_process_id = trp.id and recruiter.user_role=1
                            LEFT JOIN user u on recruiter.user_id = u.id
                            LEFT JOIN talent_recruitment_process_kpi_user am on am.talent_recruitment_process_id = trp.id and am.user_role=0
                            LEFT JOIN user uam on am.user_id = uam.id
                            LEFT JOIN talent_recruitment_process_kpi_user coam on coam.talent_recruitment_process_id = trp.id and coam.user_role=7
                            LEFT JOIN user ucoam on coam.user_id = ucoam.id
                            LEFT JOIN talent_recruitment_process_kpi_user slowner on slowner.talent_recruitment_process_id = trp.id and slowner.user_role=9
                            LEFT JOIN user uslowner on slowner.user_id = uslowner.id
                            LEFT JOIN talent_recruitment_process_kpi_user bdowner on bdowner.talent_recruitment_process_id = trp.id and bdowner.user_role=8
                            LEFT JOIN user ubdowner on bdowner.user_id = ubdowner.id
                            LEFT JOIN talent_recruitment_process_submit_to_job stj on stj.talent_recruitment_process_id = trp.id
                            LEFT JOIN talent_recruitment_process_submit_to_client stc on stc.talent_recruitment_process_id = trp.id
                            LEFT JOIN talent_recruitment_process_offer trpo on trpo.talent_recruitment_process_id = trp.id
                            LEFT JOIN talent_recruitment_process_onboard trpon on trpon.talent_recruitment_process_id = trp.id
                            LEFT JOIN talent_recruitment_process_eliminate trpe on trp.id = trpe.talent_recruitment_process_id 
                            LEFT JOIN talent_recruitment_process_resignation resign on trp.id = resign.talent_recruitment_process_id 
                            LEFT JOIN start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5 
                            LEFT JOIN start s on s.talent_recruitment_process_id = trp.id  
                            LEFT JOIN invoice inv on inv.start_id = s.id AND inv.STATUS != 7 
                            LEFT JOIN timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id  
                            LEFT JOIN t_contractor_invoice tcin on tcin.assignment_id = tta.id and tcin.invoice_status != 3 
                            LEFT JOIN (select trpi.talent_recruitment_process_id, count(trpi.id) as 'interview_count' from talent_recruitment_process_interview trpi group by trpi.talent_recruitment_process_id) interview_counts on trp.id = interview_counts.talent_recruitment_process_id 
                            LEFT JOIN talent_recruitment_process_stop_statistics trpss on trpss.talent_recruitment_process_id = trp.id 
                            LEFT JOIN agency_submit_application asa ON asa.talent_recruitment_process_id = trp.id 
                            LEFT JOIN agency_shared_job asj ON asj.job_id = j.id 
                        WHERE trpn.node_status in(1,4)
                          and trp.tenant_id = ?1 
                          and trpku.user_id in (?2) 
                """ +
                (ObjectUtil.isNull(requestParam.getHiringManager()) ? "" : " and hm.talent_id = " + requestParam.getHiringManager()) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate()) ? "" : " and hr.talent_id = " + requestParam.getHrCoordinate()) +
                (ObjectUtil.isNull(requestParam.getMsp()) ? "" : " and msp.talent_id = " + requestParam.getMsp()) +
                "          and trp.last_modified_date >= ?3 " +
                "          and trp.last_modified_date < ?4 " + additionalCondition(requestParam, true) +
                " group by trp.id) n " + orderBy +
                " LIMIT ?5 OFFSET  ?6 ";


        int totalSize;
        if (null == requestParam.getFromDate()) {
            requestParam.setFromDate(Instant.EPOCH);
        }
        if (null == requestParam.getToDate()) {
            requestParam.setToDate(Constants.MAX_DATETIME);
        }
        try {
            SecurityContext context = SecurityContextHolder.getContext();
            CompletableFuture<Integer> countFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                Query countQ = entityManager.createNativeQuery(countQuery);
                countQ.setParameter(1, SecurityUtils.getTenantId());
                countQ.setParameter(2, getSearchUserIds(requestParam));
                countQ.setParameter(3, requestParam.getFromDate());
                countQ.setParameter(4, requestParam.getToDate());
                return ((BigInteger)countQ.getSingleResult()).intValue();
            });
            CompletableFuture<List<Object[]>> dataFuture = CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                Query dataQ = entityManager.createNativeQuery(dataQuery);
                dataQ.setParameter(1, SecurityUtils.getTenantId());
                dataQ.setParameter(2, getSearchUserIds(requestParam));
                dataQ.setParameter(3, requestParam.getFromDate());
                dataQ.setParameter(4, requestParam.getToDate());
                dataQ.setParameter(5, pageable.getPageSize());
                dataQ.setParameter(6, pageable.getPageNumber() * pageable.getPageSize());
                dataQ.setParameter(7, orderByStatusAsc ? null : MyCandidateStatusFilter.ELIMINATED.toDbValue());
                return dataQ.getResultList();
            });
            log.info("dataQuery : {}, parameter1: {}, parameter2: {}, parameter3: {}, parameter4: {}, parameter5: {}, parameter6: {}, parameter7: {}",
                    dataQuery, SecurityUtils.getTenantId(), SecurityUtils.getUserId(), requestParam.getFromDate(), requestParam.getToDate(), pageable.getPageSize(), pageable.getPageNumber() * pageable.getPageSize(), orderByStatusAsc ? null : MyCandidateStatusFilter.ELIMINATED.toDbValue());
            List<Object[]> objects = dataFuture.get();
            totalSize = countFuture.get();
            //更新本次团队/用户搜索条件
            updatePersonalizationConfig(requestParam.getUserIds());

            if(ObjectUtil.isNotEmpty(objects)) {
                return new PageImpl<>(objects, pageable, totalSize);
            }
        } catch (NoResultException e) {

        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        return new PageImpl<>(Lists.newArrayList(), pageable, 0);
    }

    private void updatePersonalizationConfig(Set<Long> userIds) {
        if(userIds == null) {
            return;
        }
        UserPersonalizationConfig userPersonalizationConfig = userService.getPersonalizationConfig().getBody();
        if(userPersonalizationConfig == null) {
            userPersonalizationConfig = new UserPersonalizationConfig();
        }
        userPersonalizationConfig.setPipelineUserIds(userIds);
        userService.updatePersonalizationConfig(userPersonalizationConfig);
    }

    @Resource
    private InitiationService initiationService;

    private boolean checkSearchPermission(MyPipelineSearchParam requestParam) {
        if(requestParam.getUserIds() == null) {
            return true;
        }
        if(SecurityUtils.isAdmin()) {
            return true;
        }
        Set<Long> userIds = requestParam.getUserIds();

        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateCandidatePipelineManagementDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        if(teamDataPermission.getSelf()) {
            return !userIds.stream().anyMatch(id -> !id.equals(SecurityUtils.getUserId()));
        } else if (teamDataPermission.getAll()) {
            return true;
        } else {
            Set<Long> readableTeamIds = teamDataPermission.getReadableTeamIds();
            PermissionTeamMemberSearchVM vm = new PermissionTeamMemberSearchVM();
            vm.setTeamIds(readableTeamIds);
            List<Long> readableUserIds = userService.getTeamMembersByTeamIds(vm).getBody().stream().map(PermissionTeamMemberDTO::getId).toList();

            return readableUserIds.containsAll(userIds);
        }
    }

    private Set<Long> getSearchUserIds(MyPipelineSearchParam requestParam) {
        Set<Long> userIds = requestParam.getUserIds();
        if(userIds == null) {
            return Set.of(SecurityUtils.getUserId());
        }
        return userIds;
    }

    private boolean addSorting(StringBuilder sql, Sort sort) {
        boolean orderByStatusAsc = false;
        if (sort != null) {
            //先按逾期非逾期排，再按逾期时间排
            sql.append(" ORDER BY ");
            for (Sort.Order order : sort) {
                if (order.getProperty().equals("nodeType") && order.getDirection().equals(Sort.Direction.ASC)) {
                    sql.append("-").append(order.getProperty()).append(" ").append(Sort.Direction.DESC);
                    orderByStatusAsc = true;
                } else if("jobId".equals(order.getProperty())){
                    sql.append(order.getProperty()).append(" ").append(order.getDirection());
                } else if("jobLocations".equals(order.getProperty())){
                    sql.append("CONVERT  ( JSON_EXTRACT(").append(order.getProperty()).append(", '$[0].originDisplay')  USING gbk)").append(" ").append(order.getDirection());
                } else {
                    sql.append("CONVERT ( ").append(order.getProperty()).append("  USING gbk)").append(" ").append(order.getDirection());
                }
                sql.append(", ");
            }
            sql.delete(sql.length() - 2, sql.length());
        } else {
            sql.append(" ORDER BY stopProcess DESC, CASE WHEN stopProcess = 'true' THEN lastModifiedDate END ASC, CASE WHEN stopProcess = 'false' THEN lastModifiedDate END DESC ");
        }
        return orderByStatusAsc;
    }

    @Override
    public ViewPipelineResult toViewPipelineResult(List<Object[]> objects, MyPipelineSearchParam requestParam) {
        ViewPipelineResult result = new ViewPipelineResult();
        if (CollectionUtils.isEmpty(objects)) {
            return result;
        }
        SecurityContext context = SecurityContextHolder.getContext();
        List<ViewPipeline> viewPipelines = this.objectToMyPipeline(objects);
        Set<Long> talentIds = viewPipelines.stream().map(ViewPipeline::getTalentId).collect(Collectors.toSet());
        CompletableFuture<List<TalentNameVM>> hiringManagersFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return filterForHiringManager(requestParam);
        });
        CompletableFuture<List<TalentNameVM>> hrCoordinatesFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return filterForHrCoordinates(requestParam);
        });
        CompletableFuture<List<TalentNameVM>> mspsFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return filterForMsps(requestParam);
        });
        CompletableFuture<List<UserNameVM>> recruitersFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return filterForRecruiter(requestParam);
        });
        CompletableFuture<List<UserNameVM>> amsFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return filterForAM(requestParam);
        });
        CompletableFuture<List<UserNameVM>> coAmsFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return filterForCoAM(requestParam);
        });
        CompletableFuture<List<BigInteger>> nodeTypesFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return filterForNodeTypes(requestParam);
        });

        // 创建一个组合 Future 来收集所有 talentIds 并启动保密信息检查
        CompletableFuture<Set<Long>> allTalentIdsFuture = CompletableFuture.allOf(
            hiringManagersFuture, hrCoordinatesFuture, mspsFuture
        ).thenApply(v -> {
            Set<Long> allTalentIds = new HashSet<>(talentIds);
            Stream.of(hiringManagersFuture.join(), hrCoordinatesFuture.join(), mspsFuture.join())
                    .forEach(list -> list.stream().filter(Objects::nonNull).map(TalentNameVM::getTalentId).filter(Objects::nonNull).forEach(allTalentIds::add));
            return allTalentIds;
        });

        CompletableFuture<Map<Long, ConfidentialInfoDto>> talentConfidentialInfoMapFuture = allTalentIdsFuture.thenCompose(allTalentIds ->
            CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return talentFeignClient.getTalentConfidentialInfo(allTalentIds).getBody();
            })
        );

        CompletableFuture<Set<Long>> viewAbleTalentIdsFuture = allTalentIdsFuture.thenCompose(allTalentIds ->
            CompletableFuture.supplyAsync(() -> {
                SecurityContextHolder.setContext(context);
                return talentFeignClient.filterConfidentialTalentViewAble(allTalentIds).getBody();
            })
        );

        try {
            List<TalentNameVM> hiringManagers = hiringManagersFuture.get();
            List<TalentNameVM> hrCoordinates = hrCoordinatesFuture.get();
            List<TalentNameVM> msps = mspsFuture.get();
            List<UserNameVM> recruiters = recruitersFuture.get();
            List<UserNameVM> ams = amsFuture.get();
            List<UserNameVM> coAms = coAmsFuture.get();
            List<BigInteger> nodeTypes = nodeTypesFuture.get();
            Map<Long, ConfidentialInfoDto> confidentialInfoMap = talentConfidentialInfoMapFuture.get();
            Set<Long> viewAbleTalentIds = viewAbleTalentIdsFuture.get();
            result.setElements(processConfidingTalent(viewPipelines, confidentialInfoMap, viewAbleTalentIds));
            result.setHiringManagers(talentNameVMListToSet(hiringManagers, confidentialInfoMap, viewAbleTalentIds));
            result.setHrCoordinates(talentNameVMListToSet(hrCoordinates, confidentialInfoMap, viewAbleTalentIds));
            result.setMsps(talentNameVMListToSet(msps, confidentialInfoMap, viewAbleTalentIds));
            result.setRecruiters(userNameVMListToSet(recruiters));
            result.setAccountManagers(userNameVMListToSet(ams));
            result.setCooperateAccountManager(userNameVMListToSet(coAms));
            result.setNodeTypes(CollectionUtils.isNotEmpty(nodeTypes) ? nodeTypes.stream().filter(s -> ObjectUtil.isNotEmpty(s.intValue()))
                    .map(nodeType -> MyCandidateStatusFilter.fromDbValue(nodeType.intValue())).collect(Collectors.toList()) : Lists.newArrayList());
        } catch (Exception e) {
            log.error("[mypipeline @{}] toViewPipelineResult is error , message = {}", SecurityUtils.getUserId(), ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("Network error, please try again.");
        }
        return result;
    }

    private List<Object> processConfidingTalent(List<ViewPipeline> viewPipelines, Map<Long, ConfidentialInfoDto> confidentialInfoMap, Set<Long> viewAbleTalentIds) {
        List<ViewPipeline> list = viewPipelines.stream().map(pipeline -> {
            if (!confidentialInfoMap.containsKey(pipeline.getTalentId())) {
                return pipeline;
            }
            if (!viewAbleTalentIds.contains(pipeline.getTalentId())) {
                pipeline.setConfidentialTalentViewAble(false);
                pipeline.encrypt();
            }
            return pipeline;
        }).toList();
        return new ArrayList<>(list);
    }

    private Set<String> listToSet(List<String> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            Set<String> result = new HashSet<>();
            for (String s : list) {
                if (StringUtils.isNotEmpty(s)) {
                    result.add(s);
                }
            }
            return result;
        }
        return new HashSet<>();
    }

    private Set<FullNameUserDTO> talentNameVMListToSet(List<TalentNameVM> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().filter(Objects::nonNull)
                    .map(talent -> {
                        FullNameUserDTO dto = new FullNameUserDTO();
                        dto.setId(talent.getTalentId());
                        dto.setFullName(CommonUtils.formatFullName(talent.getFirstName(), talent.getLastName()));
                        return dto;
                    }).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    private Set<FullNameUserDTO> talentNameVMListToSet(List<TalentNameVM> list, Map<Long, ConfidentialInfoDto> confidentialInfoMap, Set<Long> viewAbleTalentIds) {
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().filter(Objects::nonNull)
                    .map(talent -> {
                        FullNameUserDTO dto = new FullNameUserDTO();
                        dto.setId(talent.getTalentId());

                        // 检查是否是保密候选人且当前用户无权查看
                        if (confidentialInfoMap.containsKey(talent.getTalentId()) &&
                            !viewAbleTalentIds.contains(talent.getTalentId())) {
                            // 如果是保密候选人且无权查看，设置 fullName 为空字符串
                            dto.setFullName("");
                        } else {
                            // 正常情况下设置完整姓名
                            dto.setFullName(CommonUtils.formatFullName(talent.getFirstName(), talent.getLastName()));
                        }
                        return dto;
                    }).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    private Set<FullNameUserDTO> userNameVMListToSet(List<UserNameVM> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream()
                    .filter(Objects::nonNull)
                    .map(user -> {
                        FullNameUserDTO dto = new FullNameUserDTO();
                        dto.setId(user.getId());
                        dto.setFullName(CommonUtils.formatFullName(user.getFirstName(), user.getLastName()));
                        return dto;
                    }).collect(Collectors.toSet());
        }
        return new HashSet<>();
    }


    private List<TalentNameVM> filterForHiringManager(MyPipelineSearchParam requestParam) {
        String query = "SELECT distinct hm.talent_id, hmt.first_name, hmt.last_name " +
                "        FROM talent_recruitment_process trp " +
                "            LEFT JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id " +
                "            LEFT JOIN job j ON j.id = trp.job_id " +
                //"            LEFT JOIN (SELECT loc.job_id, JSON_ARRAYAGG(JSON_OBJECT(\"official_country\", loc.official_country, \"official_city\", loc.official_city, \"official_province\", loc.official_province)) as locations from job_location loc group by loc.job_id) l ON l.job_id = j.id " +
                "            LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id " +
                "            LEFT JOIN talent t ON t.id = trp.talent_id " +
                "            LEFT JOIN talent_additional_info t_info ON t_info.id = t.id" +
                "            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id" +
                "            LEFT JOIN company c on j.company_id = c.id" +
                "            LEFT JOIN talent_contact tc on tc.talent_id = t.id and tc.jhi_type in(0,2) and tc.status = 0 " +
                "            LEFT JOIN talent_contact tcphone on tcphone.talent_id = t.id and tcphone.jhi_type in(1,16) and tcphone.status = 0 and (tcphone.verification_status is null or tcphone.verification_status <> 9) " +
                "            LEFT JOIN job_company_contact_relation jcr on j.id = jcr.job_id" +
                (ObjectUtil.isNull(requestParam.getHiringManager())?" LEFT JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 ":" INNER JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 and hm.talent_id = " + requestParam.getHiringManager()) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate())?" LEFT JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 ":" INNER JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 and hr.talent_id = " + requestParam.getHrCoordinate()) +
                (ObjectUtil.isNull(requestParam.getMsp())?" LEFT JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 ":" INNER JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 and msp.talent_id = " + requestParam.getMsp()) +
                "            LEFT JOIN talent hmt ON hmt.id = hm.talent_id " +
                "            LEFT JOIN talent_recruitment_process_kpi_user recruiter on recruiter.talent_recruitment_process_id = trp.id and recruiter.user_role=1" +
                "            LEFT JOIN user u on recruiter.user_id = u.id" +
                "            LEFT JOIN start s on s.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN invoice inv on inv.start_id = s.id AND inv.STATUS != 7 " +
                "            LEFT JOIN timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN t_contractor_invoice tcin on tcin.assignment_id = tta.id and tcin.invoice_status != 3 " +
                "        WHERE trpn.node_status in(1,4)" +
                "          and trp.tenant_id = ?1 " +
                "          and trpku.user_id = ?2 " +
                "          and trp.last_modified_date >= ?3 " +
                "          and trp.last_modified_date < ?4 " + additionalCondition(requestParam, false);
        Query dataQ = entityManager.createNativeQuery(query, TalentNameVM.class);
        dataQ.setParameter(1, SecurityUtils.getTenantId());
        dataQ.setParameter(2, SecurityUtils.getUserId());
        dataQ.setParameter(3, requestParam.getFromDate());
        dataQ.setParameter(4, requestParam.getToDate());
        return dataQ.getResultList();
    }

    private List<TalentNameVM> filterForHrCoordinates(MyPipelineSearchParam requestParam) {
        String query = "SELECT distinct hr.talent_id, hrt.first_name, hrt.last_name " +
                "        FROM talent_recruitment_process trp " +
                "            LEFT JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id " +
                "            LEFT JOIN job j ON j.id = trp.job_id " +
                "            LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id " +
                "            LEFT JOIN talent t ON t.id = trp.talent_id " +
                "            LEFT JOIN talent_additional_info t_info ON t_info.id = t.id" +
                "            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id" +
                "            LEFT JOIN company c on j.company_id = c.id" +
                "            LEFT JOIN talent_contact tc on tc.talent_id = t.id and tc.jhi_type in(0,2) and tc.status = 0 " +
                "            LEFT JOIN talent_contact tcphone on tcphone.talent_id = t.id and tcphone.jhi_type in(1,16) and tcphone.status = 0 and (tcphone.verification_status is null or tcphone.verification_status <> 9) " +
                "            LEFT JOIN job_company_contact_relation jcr on j.id = jcr.job_id" +
                (ObjectUtil.isNull(requestParam.getHiringManager())?" LEFT JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 ":" INNER JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 and hm.talent_id = " + requestParam.getHiringManager()) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate())?" LEFT JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 ":" INNER JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 and hr.talent_id = " + requestParam.getHrCoordinate()) +
                (ObjectUtil.isNull(requestParam.getMsp())?" LEFT JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 ":" INNER JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 and msp.talent_id = " + requestParam.getMsp()) +
                "            LEFT JOIN talent hrt ON hrt.id = hr.talent_id " +
                "            LEFT JOIN talent_recruitment_process_kpi_user recruiter on recruiter.talent_recruitment_process_id = trp.id and recruiter.user_role=1" +
                "            LEFT JOIN user u on recruiter.user_id = u.id" +
                "            LEFT JOIN start s on s.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN invoice inv on inv.start_id = s.id AND inv.STATUS != 7 " +
                "            LEFT JOIN timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN t_contractor_invoice tcin on tcin.assignment_id = tta.id and tcin.invoice_status != 3 " +
                "        WHERE trpn.node_status in(1,4)" +
                "          and trp.tenant_id = ?1 " +
                "          and trpku.user_id = ?2 " +
                "          and trp.last_modified_date >= ?3 " +
                "          and trp.last_modified_date < ?4 " + additionalCondition(requestParam, false);
        Query dataQ = entityManager.createNativeQuery(query, TalentNameVM.class);
        dataQ.setParameter(1, SecurityUtils.getTenantId());
        dataQ.setParameter(2, SecurityUtils.getUserId());
        dataQ.setParameter(3, requestParam.getFromDate());
        dataQ.setParameter(4, requestParam.getToDate());
        return dataQ.getResultList();
    }

    private List<TalentNameVM> filterForMsps(MyPipelineSearchParam requestParam) {
        String query = "SELECT distinct msp.talent_id, mspt.first_name, mspt.last_name " +
                "        FROM talent_recruitment_process trp " +
                "            LEFT JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id " +
                "            LEFT JOIN job j ON j.id = trp.job_id " +
                "            LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id " +
                "            LEFT JOIN talent t ON t.id = trp.talent_id " +
                "            LEFT JOIN talent_additional_info t_info ON t_info.id = t.id" +
                "            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id" +
                "            LEFT JOIN company c on j.company_id = c.id" +
                "            LEFT JOIN talent_contact tc on tc.talent_id = t.id and tc.jhi_type in(0,2) and tc.status = 0 " +
                "            LEFT JOIN talent_contact tcphone on tcphone.talent_id = t.id and tcphone.jhi_type in(1,16) and tcphone.status = 0 and (tcphone.verification_status is null or tcphone.verification_status <> 9) " +
                "            LEFT JOIN job_company_contact_relation jcr on j.id = jcr.job_id" +
                (ObjectUtil.isNull(requestParam.getHiringManager())?" LEFT JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 ":" INNER JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 and hm.talent_id = " + requestParam.getHiringManager()) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate())?" LEFT JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 ":" INNER JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 and hr.talent_id = " + requestParam.getHrCoordinate()) +
                (ObjectUtil.isNull(requestParam.getMsp())?" LEFT JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 ":" INNER JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 and msp.talent_id = " + requestParam.getMsp()) +                "            LEFT JOIN talent mspt ON mspt.id = msp.talent_id " +
                "            LEFT JOIN talent_recruitment_process_kpi_user recruiter on recruiter.talent_recruitment_process_id = trp.id and recruiter.user_role=1" +
                "            LEFT JOIN user u on recruiter.user_id = u.id" +
                "            LEFT JOIN start s on s.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN invoice inv on inv.start_id = s.id AND inv.STATUS != 7 " +
                "            LEFT JOIN timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN t_contractor_invoice tcin on tcin.assignment_id = tta.id and tcin.invoice_status != 3 " +
                "        WHERE trpn.node_status in(1,4)" +
                "          and trp.tenant_id = ?1 " +
                "          and trpku.user_id = ?2 " +
                "          and trp.last_modified_date >= ?3 " +
                "          and trp.last_modified_date < ?4 " + additionalCondition(requestParam, false);
        Query dataQ = entityManager.createNativeQuery(query, TalentNameVM.class);
        dataQ.setParameter(1, SecurityUtils.getTenantId());
        dataQ.setParameter(2, SecurityUtils.getUserId());
        dataQ.setParameter(3, requestParam.getFromDate());
        dataQ.setParameter(4, requestParam.getToDate());
        return dataQ.getResultList();
    }

    private List<UserNameVM> filterForRecruiter(MyPipelineSearchParam requestParam) {
        String query = "SELECT distinct u.id, u.first_name, u.last_name " +
                "        FROM talent_recruitment_process trp " +
                "            LEFT JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id " +
                "            LEFT JOIN job j ON j.id = trp.job_id " +
                "            LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id " +
                "            LEFT JOIN talent t ON t.id = trp.talent_id " +
                "            LEFT JOIN talent_additional_info t_info ON t_info.id = t.id" +
                "            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id" +
                "            LEFT JOIN company c on j.company_id = c.id" +
                "            LEFT JOIN talent_contact tc on tc.talent_id = t.id and tc.jhi_type in(0,2) and tc.status = 0 " +
                "            LEFT JOIN talent_contact tcphone on tcphone.talent_id = t.id and tcphone.jhi_type in(1,16) and tcphone.status = 0 and (tcphone.verification_status is null or tcphone.verification_status <> 9) " +
                "            LEFT JOIN job_company_contact_relation jcr on j.id = jcr.job_id" +
                (ObjectUtil.isNull(requestParam.getHiringManager())?" LEFT JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 ":" INNER JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 and hm.talent_id = " + requestParam.getHiringManager()) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate())?" LEFT JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 ":" INNER JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 and hr.talent_id = " + requestParam.getHrCoordinate()) +
                (ObjectUtil.isNull(requestParam.getMsp())?" LEFT JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 ":" INNER JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 and msp.talent_id = " + requestParam.getMsp()) +                "            LEFT JOIN talent_recruitment_process_kpi_user recruiter on recruiter.talent_recruitment_process_id = trp.id and recruiter.user_role=1" +
                "            LEFT JOIN user u on recruiter.user_id = u.id" +
                "            LEFT JOIN start s on s.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN invoice inv on inv.start_id = s.id AND inv.STATUS != 7 " +
                "            LEFT JOIN timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN t_contractor_invoice tcin on tcin.assignment_id = tta.id and tcin.invoice_status != 3 " +
                "        WHERE trpn.node_status in(1,4)" +
                "          and trp.tenant_id = ?1 " +
                "          and trpku.user_id = ?2 " +
                "          and trp.last_modified_date >= ?3 " +
                "          and trp.last_modified_date < ?4 " + additionalCondition(requestParam, false);
        Query dataQ = entityManager.createNativeQuery(query, UserNameVM.class);
        dataQ.setParameter(1, SecurityUtils.getTenantId());
        dataQ.setParameter(2, SecurityUtils.getUserId());
        dataQ.setParameter(3, requestParam.getFromDate());
        dataQ.setParameter(4, requestParam.getToDate());
        return dataQ.getResultList();
    }

    private List<UserNameVM> filterForAM(MyPipelineSearchParam requestParam) {
        String query = "SELECT distinct u.id, u.first_name, u.last_name " +
                "        FROM talent_recruitment_process trp " +
                "            LEFT JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id " +
                "            LEFT JOIN job j ON j.id = trp.job_id " +
                "            LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id " +
                "            LEFT JOIN talent t ON t.id = trp.talent_id " +
                "            LEFT JOIN talent_additional_info t_info ON t_info.id = t.id" +
                "            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id" +
                "            LEFT JOIN company c on j.company_id = c.id" +
                "            LEFT JOIN talent_contact tc on tc.talent_id = t.id and tc.jhi_type in(0,2) and tc.status = 0 " +
                "            LEFT JOIN talent_contact tcphone on tcphone.talent_id = t.id and tcphone.jhi_type in(1,16) and tcphone.status = 0 and (tcphone.verification_status is null or tcphone.verification_status <> 9) " +
                "            LEFT JOIN job_company_contact_relation jcr on j.id = jcr.job_id" +
                (ObjectUtil.isNull(requestParam.getHiringManager())?" LEFT JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 ":" INNER JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 and hm.talent_id = " + requestParam.getHiringManager()) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate())?" LEFT JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 ":" INNER JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 and hr.talent_id = " + requestParam.getHrCoordinate()) +
                (ObjectUtil.isNull(requestParam.getMsp())?" LEFT JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 ":" INNER JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 and msp.talent_id = " + requestParam.getMsp()) +                "            LEFT JOIN talent_recruitment_process_kpi_user recruiter on recruiter.talent_recruitment_process_id = trp.id and recruiter.user_role=1" +
                "            LEFT JOIN talent_recruitment_process_kpi_user am on am.talent_recruitment_process_id = trp.id and am.user_role=0" +
                "            LEFT JOIN user u on am.user_id = u.id" +
                "            LEFT JOIN start s on s.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN invoice inv on inv.start_id = s.id AND inv.STATUS != 7 " +
                "            LEFT JOIN timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN t_contractor_invoice tcin on tcin.assignment_id = tta.id and tcin.invoice_status != 3 " +
                "        WHERE trpn.node_status in(1,4)" +
                "          and trp.tenant_id = ?1 " +
                "          and trpku.user_id = ?2 " +
                "          and trp.last_modified_date >= ?3 " +
                "          and trp.last_modified_date < ?4 " + additionalCondition(requestParam, false);
        Query dataQ = entityManager.createNativeQuery(query, UserNameVM.class);
        dataQ.setParameter(1, SecurityUtils.getTenantId());
        dataQ.setParameter(2, SecurityUtils.getUserId());
        dataQ.setParameter(3, requestParam.getFromDate());
        dataQ.setParameter(4, requestParam.getToDate());
        return dataQ.getResultList();
    }

    private List<UserNameVM> filterForCoAM(MyPipelineSearchParam requestParam) {
        String query = "SELECT distinct u.id, u.first_name, u.last_name " +
                "        FROM talent_recruitment_process trp " +
                "            LEFT JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id " +
                "            LEFT JOIN job j ON j.id = trp.job_id " +
                "            LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id " +
                "            LEFT JOIN talent t ON t.id = trp.talent_id " +
                "            LEFT JOIN talent_additional_info t_info ON t_info.id = t.id" +
                "            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id" +
                "            LEFT JOIN company c on j.company_id = c.id" +
                "            LEFT JOIN talent_contact tc on tc.talent_id = t.id and tc.jhi_type in(0,2) and tc.status = 0 " +
                "            LEFT JOIN talent_contact tcphone on tcphone.talent_id = t.id and tcphone.jhi_type in(1,16) and tcphone.status = 0 and (tcphone.verification_status is null or tcphone.verification_status <> 9) " +
                "            LEFT JOIN job_company_contact_relation jcr on j.id = jcr.job_id" +
                (ObjectUtil.isNull(requestParam.getHiringManager())?" LEFT JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 ":" INNER JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 and hm.talent_id = " + requestParam.getHiringManager()) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate())?" LEFT JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 ":" INNER JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 and hr.talent_id = " + requestParam.getHrCoordinate()) +
                (ObjectUtil.isNull(requestParam.getMsp())?" LEFT JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 ":" INNER JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 and msp.talent_id = " + requestParam.getMsp()) +                "            LEFT JOIN talent_recruitment_process_kpi_user recruiter on recruiter.talent_recruitment_process_id = trp.id and recruiter.user_role=1" +
                "            LEFT JOIN talent_recruitment_process_kpi_user am on am.talent_recruitment_process_id = trp.id and am.user_role=7" +
                "            LEFT JOIN user u on am.user_id = u.id" +
                "            LEFT JOIN start s on s.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN invoice inv on inv.start_id = s.id AND inv.STATUS != 7 " +
                "            LEFT JOIN timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN t_contractor_invoice tcin on tcin.assignment_id = tta.id and tcin.invoice_status != 3 " +
                "        WHERE trpn.node_status in(1,4)" +
                "          and trp.tenant_id = ?1 " +
                "          and trpku.user_id = ?2 " +
                "          and trp.last_modified_date >= ?3 " +
                "          and trp.last_modified_date < ?4 " + additionalCondition(requestParam, false);
        Query dataQ = entityManager.createNativeQuery(query, UserNameVM.class);
        dataQ.setParameter(1, SecurityUtils.getTenantId());
        dataQ.setParameter(2, SecurityUtils.getUserId());
        dataQ.setParameter(3, requestParam.getFromDate());
        dataQ.setParameter(4, requestParam.getToDate());
        return dataQ.getResultList();
    }

    private List<BigInteger> filterForNodeTypes(MyPipelineSearchParam requestParam) {
        String query = "SELECT distinct (IF(trpn.node_status = 1, trpn.node_type, ?5)) " +
                "        FROM talent_recruitment_process trp " +
                "            LEFT JOIN recruitment_process rp ON rp.id = trp.recruitment_process_id " +
                "            LEFT JOIN job j ON j.id = trp.job_id " +
                "            LEFT JOIN (SELECT loc.job_id, concat('[',GROUP_CONCAT(loc.original_loc ORDER BY loc.id ASC ),']') as locations from job_location loc where loc.original_loc is not null group by loc.job_id) l ON l.job_id = j.id " +
                "            LEFT JOIN talent t ON t.id = trp.talent_id " +
                "            LEFT JOIN talent_additional_info t_info ON t_info.id = t.id" +
                "            LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id" +
                "            LEFT JOIN talent_recruitment_process_kpi_user trpku ON trp.id = trpku.talent_recruitment_process_id" +
                "            LEFT JOIN company c on j.company_id = c.id" +
                "            LEFT JOIN talent_contact tc on tc.talent_id = t.id and tc.jhi_type in(0,2) and tc.status = 0 " +
                "            LEFT JOIN talent_contact tcphone on tcphone.talent_id = t.id and tcphone.jhi_type in(1,16) and tcphone.status = 0 and (tcphone.verification_status is null or tcphone.verification_status <> 9) " +
                "            LEFT JOIN job_company_contact_relation jcr on j.id = jcr.job_id" +
                (ObjectUtil.isNull(requestParam.getHiringManager())?" LEFT JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 ":" INNER JOIN company_sales_lead_client_contact hm on jcr.client_contact_id = hm.id and jcr.contact_category=1 and hm.talent_id = " + requestParam.getHiringManager()) +
                (ObjectUtil.isNull(requestParam.getHrCoordinate())?" LEFT JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 ":" INNER JOIN company_sales_lead_client_contact hr on jcr.client_contact_id = hr.id and jcr.contact_category=2 and hr.talent_id = " + requestParam.getHrCoordinate()) +
                (ObjectUtil.isNull(requestParam.getMsp())?" LEFT JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 ":" INNER JOIN company_sales_lead_client_contact msp on jcr.client_contact_id = msp.id and jcr.contact_category=3 and msp.talent_id = " + requestParam.getMsp()) +                "            LEFT JOIN talent_recruitment_process_kpi_user recruiter on recruiter.talent_recruitment_process_id = trp.id and recruiter.user_role=1" +
                "            LEFT JOIN user u on recruiter.user_id = u.id" +
                "            LEFT JOIN start s on s.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN invoice inv on inv.start_id = s.id AND inv.STATUS != 7 " +
                "            LEFT JOIN timesheet_talent_assignment tta on tta.talent_recruitment_process_id = trp.id  " +
                "            LEFT JOIN t_contractor_invoice tcin on tcin.assignment_id = tta.id and tcin.invoice_status != 3 " +
                "        WHERE trpn.node_status in(1,4)" +
                "          and trp.tenant_id = ?1 " +
                "          and trpku.user_id = ?2 " +
                "          and trp.last_modified_date >= ?3 " +
                "          and trp.last_modified_date < ?4 " + additionalCondition(requestParam, false);
        Query dataQ = entityManager.createNativeQuery(query);
        dataQ.setParameter(1, SecurityUtils.getTenantId());
        dataQ.setParameter(2, SecurityUtils.getUserId());
        dataQ.setParameter(3, requestParam.getFromDate());
        dataQ.setParameter(4, requestParam.getToDate());
        dataQ.setParameter(5, MyCandidateStatusFilter.ELIMINATED.toDbValue());
        return dataQ.getResultList();
    }

    private List<ViewPipeline> objectToMyPipeline(List<Object[]> objects) {
        Set<Long> teamIdsForPrivateJob = recruitmentProcessRepository.findTeamIdsForPrivateJob(SecurityUtils.getTenantId());
        List<ViewPipeline> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(objects)) {
            return result;
        }
        for (Object[] obj : objects) {
            ViewPipeline element = new ViewPipeline();
            Long talentRecruitmentProcessId = Long.valueOf(StringUtil.valueOf(obj[0]));
            element.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
            element.setTenantId(Long.valueOf(StringUtil.valueOf(obj[1])));
            element.setTalentId(Long.valueOf(StringUtil.valueOf(obj[2])));
            element.setTalentName(StringUtil.valueOf(obj[3]));
            element.setTalentSkillString(StringUtil.valueOf(obj[4]));
            element.setTalentEmailString(StringUtil.valueOf(obj[5]));
            //只删除jobid 没有删除对应流程 跳过该类数据
            if(StringUtil.valueOf(obj[6]) == null) {
                continue;
            }
            element.setJobId(obj[6] != null ? Long.valueOf(StringUtil.valueOf(obj[6])) : null);
            element.setJobTitle(StringUtil.valueOf(obj[7]));
            String locations = Objects.isNull(obj[8]) ? "[]" : StringUtil.valueOf(obj[8]);
            locations = locations
                    .replaceAll("country", "official_country")
                    .replaceAll("province", "official_province")
                    .replaceAll("city", "official_city");
            element.setJobLocationString(locations);
            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[9]))) {
                element.setJobType(JobType.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[9]))));
            }
            //只删除companyId 没有删除对应流程 跳过该类数据
            if(StringUtil.valueOf(obj[10]) == null) {
                continue;
            }
            element.setCompanyId(obj[10] != null ? Long.valueOf(StringUtil.valueOf(obj[10])) : null);
            element.setCompanyName(StringUtil.valueOf(obj[11]));
            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[12]))) {
                element.setNodeType(MyCandidateStatusFilter.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[12]))));
            } else {
                element.setNodeType(MyCandidateStatusFilter.ELIMINATED);
            }
            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[13]))) {
                element.setNodeStatus(NodeStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[13]))));
                if (ObjectUtil.isNotEmpty(element.getNodeStatus()) && Objects.equals(element.getNodeStatus(), NodeStatus.ELIMINATED)) {
                    if (ObjectUtil.isNotEmpty(obj[23])) {
                        Integer reason = Integer.parseInt(StringUtil.valueOf(obj[23]));
                        element.setEliminateReason(EliminateReason.fromDbValue(reason));
                    }
                }
            }
            element.setCurrentStatusNote(StringUtil.valueOf(obj[14]));
            element.setHiringManager(StringUtil.valueOf(obj[15]));
            element.setHrCoordinate(StringUtil.valueOf(obj[16]));
            element.setMsp(StringUtil.valueOf(obj[17]));
            element.setRecruiter(StringUtil.valueOf(obj[18]));
            element.setAccountManager(StringUtil.valueOf(obj[19]));
            element.setUserId(obj[20] != null ? Long.valueOf(StringUtil.valueOf(obj[20])) : null);
            if(ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[21]))) {
                element.setUserRole(UserRole.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[21]))));
            }
            element.setLastModifiedDate(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[22])));
            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[8]))) {
                //StringUtil.valueOf(obj[8])
                element.setJobLocations(JSONUtil.toList(JSONUtil.parseArray(locations), JSONObject.class));
            }
            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[4]))) {
                element.setTalentSkills(JSONUtil.toList(JSONUtil.parseArray(StringUtil.valueOf(obj[4])), JSONObject.class));
            }
            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[5]))) {
                element.setTalentEmails(Arrays.asList(StrUtil.toString(StringUtil.valueOf(obj[5])).split(StrUtil.COMMA)).stream().distinct().collect(Collectors.toList()));
            }
            element.setInvoiceFlag(Objects.equals(String.valueOf(obj[24]), "1"));
            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[25]))) {
                element.setTalentPhones(Arrays.asList(StrUtil.toString(StringUtil.valueOf(obj[25])).split(StrUtil.COMMA)).stream().distinct().collect(Collectors.toList()));
            }
            element.setRecruitmentProcessId(Long.valueOf(StringUtil.valueOf(obj[26])));
            element.setInterviewCount(obj[27] != null ? Integer.parseInt(StringUtil.valueOf(obj[27])) : 0);
            element.setPrivateJob(Objects.nonNull(obj[28]) && teamIdsForPrivateJob.contains(Long.parseLong(obj[28].toString())));
            element.setSubmitToJobTime(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[29])));
            element.setLastNodeType(MyCandidateStatusFilter.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[30]))));
            element.setNickName(StringUtil.valueOf(obj[31]));
            element.setProcessStop(Boolean.parseBoolean(StringUtil.valueOf(obj[32])));
            element.setFlexibleLocation(Boolean.parseBoolean(StringUtil.valueOf(obj[33])));
            element.setOverdue(Integer.parseInt(StringUtil.valueOf(obj[34])));
            element.setSubmitToClientTime(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[35])));
            element.setOfferTime(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[36])));
            element.setOnboardTime(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[37])));
            element.setInterviewTime(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[38])));
            element.setResigned(Objects.nonNull(obj[39]));
            element.setConvertedToFte(Objects.nonNull(obj[40]));
            if (BooleanUtils.isTrue(element.getConvertedToFte())){
                element.setResigned(Boolean.FALSE);
            }
            element.setCoAm(StringUtil.valueOf(obj[41]));
            if (StringUtils.isNotBlank(element.getCoAm())) {
                List<UserCountryVO> userCountryList = new ArrayList<>();
                Arrays.stream(element.getCoAm().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    UserCountryVO vo = new UserCountryVO();
                    vo.setUserName(String.valueOf(amCountry[0]));
                    vo.setCountryId(amCountry[1]);
                    userCountryList.add(vo);
                });
                element.setCooperateAccountManager(userCountryList);
            }

            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[42]))) {
                element.setSource(StringUtil.valueOf(obj[42]));
            }
            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[43]))) {
                element.setAgencyId(Long.valueOf(StringUtil.valueOf(obj[43])));
            }
            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[44]))) {
                element.setAgencySharingJobStatus(JobShareStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[44]))));
            }

            element.setSalesLeadOwner(StringUtil.valueOf(obj[45]));
            element.setBdOwner(StringUtil.valueOf(obj[46]));

            result.add(element);
        }

        return result;
    }

    private String additionalCondition(MyPipelineSearchParam requestParam, boolean filterCondition) {
        StringBuilder sb = new StringBuilder(" ");

        if (CollectionUtils.isNotEmpty(requestParam.getUserRoles())) {
            sb.append(" AND trpku.user_role IN ").append(requestParam.userRoleDBValues());
        }
        if (CollectionUtils.isNotEmpty(requestParam.getRecruitmentProcessIds())) {
            List<Long> jobTypeByProcessId = recruitmentProcessRepository.findJobTypeByProcessId(requestParam.getRecruitmentProcessIds());
            sb.append(" AND rp.job_type IN ").append(jobTypeDBValues(jobTypeByProcessId));
        }
        if(filterCondition) {
            if (requestParam.getTalentName() != null) {
                sb.append(" AND LOWER(t.full_name) LIKE ").append("'%" + requestParam.getTalentName().toLowerCase() + "%'");
            }
            if (requestParam.getJobTitle() != null) {
                // 使用 jobTitle 同时匹配 j.title 和 trp.job_id
                sb.append(" AND (LOWER(j.title) LIKE '%")
                        .append(requestParam.getJobTitle().toLowerCase())
                        .append("%' OR trp.job_id = '")
                        .append(requestParam.getJobTitle()) // 假设 jobTitle 是 job_id 的字符串形式
                        .append("') ");
            }
            if (requestParam.getCompanyName() != null) {
                sb.append(" AND LOWER(c.full_business_name) LIKE ").append("'%" + requestParam.getCompanyName().toLowerCase() + "%'");
            }
            if (requestParam.getJobLocations() != null) {
                //sb.append(" AND (LOWER(l.locations) LIKE ").append("'%" + requestParam.getJobLocations().toLowerCase() + "%'").append(") ");
                sb.append(" AND (LOWER(l.locations) LIKE ").append("'%" + requestParam.getJobLocations().toLowerCase() + "%'").append(") ");
            }
            if (requestParam.getTalentEmails() != null) {
                sb.append(" AND exists (select 1 from talent_contact tctc where tctc.talent_id = t.id and tctc.jhi_type in (0,2) and tctc.status = 0 AND LOWER(tctc.contact) LIKE ").append("'%" + requestParam.getTalentEmails().toLowerCase() + "%' ) ");
            }
            if (requestParam.getTalentPhones() != null) {
                sb.append(" AND exists (select 1 from talent_contact tcphonetcphone where tcphonetcphone.talent_id = t.id and tcphonetcphone.jhi_type in(1,16) and tcphonetcphone.status = 0 and (tcphonetcphone.verification_status is null or tcphonetcphone.verification_status <> 9) AND LOWER(tcphonetcphone.contact) LIKE ").append("'%" + requestParam.getTalentPhones().toLowerCase() + "%' ) ");
            }
            if (requestParam.getTalentSkills() != null) {
                sb.append(" AND LOWER(JSON_EXTRACT(t_info.extended_info, '$.skills')) LIKE ").append("'%" + requestParam.getTalentSkills().toLowerCase() + "%'");
            }
            if (CollUtil.isNotEmpty(requestParam.getRecruiter()) && CollUtil.isNotEmpty(requestParam.getRecruiterTeam())) {
                sb.append(" and EXISTS (select 1 from talent_recruitment_process_kpi_user kk inner join permission_user_team ppt on ppt.user_id = kk.user_id where kk.talent_recruitment_process_id = trp.id and ppt.is_primary = 1 and ( ppt.team_id in (")
                        .append(requestParam.getRecruiterTeam().stream().map(String::valueOf).collect(Collectors.joining(","))).append(")")
                        .append(" or kk.user_id in (").append(requestParam.getRecruiter().stream().map(String::valueOf).collect(Collectors.joining(","))).append(")")
                        .append(" ) and kk.user_role = 1) ");
            } else if (CollUtil.isNotEmpty(requestParam.getRecruiter())) {
                sb.append(" and EXISTS (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = trp.id and kk.user_id in (").append(requestParam.getRecruiter().stream().map(String::valueOf).collect(Collectors.joining(","))).append(") and kk.user_role = 1) ");
            } else if (CollUtil.isNotEmpty(requestParam.getRecruiterTeam())) {
                sb.append(" and EXISTS (select 1 from talent_recruitment_process_kpi_user kk inner join permission_user_team ppt on ppt.user_id = kk.user_id where kk.talent_recruitment_process_id = trp.id and ppt.is_primary = 1 and ppt.team_id in (").append(requestParam.getRecruiterTeam().stream().map(String::valueOf).collect(Collectors.joining(","))).append(") and kk.user_role = 1) ");
            }
            if (CollUtil.isNotEmpty(requestParam.getAccountManager()) && CollUtil.isNotEmpty(requestParam.getAccountManagerTeam())) {
                sb.append(" and EXISTS (select 1 from talent_recruitment_process_kpi_user kk inner join permission_user_team ppt on ppt.user_id = kk.user_id where kk.talent_recruitment_process_id = trp.id and ppt.is_primary = 1 and ( ppt.team_id in (")
                        .append(requestParam.getAccountManagerTeam().stream().map(String::valueOf).collect(Collectors.joining(","))).append(")")
                        .append(" or kk.user_id in (").append(requestParam.getAccountManager().stream().map(String::valueOf).collect(Collectors.joining(","))).append(")")
                        .append(" ) and kk.user_role = 0) ");
            } else if (CollUtil.isNotEmpty(requestParam.getAccountManager())) {
                sb.append(" and EXISTS (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = trp.id and trp.id and kk.user_id in (").append(requestParam.getAccountManager().stream().map(String::valueOf).collect(Collectors.joining(","))).append(") and kk.user_role = 0) ");
            }else if (CollUtil.isNotEmpty(requestParam.getAccountManagerTeam())) {
                sb.append(" and EXISTS (select 1 from talent_recruitment_process_kpi_user kk inner join permission_user_team ppt on ppt.user_id = kk.user_id where kk.talent_recruitment_process_id = trp.id and ppt.is_primary = 1 and ppt.team_id in (").append(requestParam.getAccountManagerTeam().stream().map(String::valueOf).collect(Collectors.joining(","))).append(") and kk.user_role = 0) ");
            }

            if (CollUtil.isNotEmpty(requestParam.getCooperateAccountManager()) && CollUtil.isNotEmpty(requestParam.getCooperateAccountManagerTeam())) {
                sb.append(" and EXISTS (select 1 from talent_recruitment_process_kpi_user kk inner join permission_user_team ppt on ppt.user_id = kk.user_id where kk.talent_recruitment_process_id = trp.id and ppt.is_primary = 1 and ( ppt.team_id in (")
                        .append(requestParam.getCooperateAccountManagerTeam().stream().map(String::valueOf).collect(Collectors.joining(","))).append(")")
                        .append(" or kk.user_id in (").append(requestParam.getCooperateAccountManager().stream().map(String::valueOf).collect(Collectors.joining(","))).append(")")
                        .append(" ) and kk.user_role = 7) ");
            } else if (CollUtil.isNotEmpty(requestParam.getCooperateAccountManager())) {
                sb.append(" and EXISTS (select 1 from talent_recruitment_process_kpi_user kk where kk.talent_recruitment_process_id = trp.id and trp.id and kk.user_id in (").append(requestParam.getCooperateAccountManager().stream().map(String::valueOf).collect(Collectors.joining(","))).append(") and kk.user_role = 7) ");
            }else if (CollUtil.isNotEmpty(requestParam.getCooperateAccountManagerTeam())) {
                sb.append(" and EXISTS (select 1 from talent_recruitment_process_kpi_user kk inner join permission_user_team ppt on ppt.user_id = kk.user_id where kk.talent_recruitment_process_id = trp.id and ppt.is_primary = 1 and ppt.team_id in (").append(requestParam.getCooperateAccountManagerTeam().stream().map(String::valueOf).collect(Collectors.joining(","))).append(") and kk.user_role = 7) ");
            }
            if (requestParam.getNodeType() != null && !requestParam.getNodeType().isEmpty()) {
                if (requestParam.getNodeType().equals(MyCandidateStatusFilter.RESIGNED)){
                    sb.append(" AND resign.id is not null ");
                }else{
                    sb.append(" AND (IF(trpn.node_status = 1, trpn.node_type, " + MyCandidateStatusFilter.ELIMINATED.toDbValue() + ")) in (").append(requestParam.getNodeType().stream().map(MyCandidateStatusFilter::toDbValue).map(String::valueOf).collect(Collectors.joining(","))).append(")");
                }
            }

            if (CollectionUtils.isNotEmpty(requestParam.getGeneralText())) {
                sb.append(" AND (");
                for (int i = 0; i < requestParam.getGeneralText().size(); i++) {
                    String text = requestParam.getGeneralText().get(i);
                    sb.append(" (LOWER(t.full_name) LIKE ").append("'%" + text.toLowerCase() + "%'");
                    sb.append(" OR LOWER(c.full_business_name) LIKE ").append("'%" + text.toLowerCase() + "%'");
                    sb.append(" OR LOWER(j.title) LIKE ").append("'%" + text.toLowerCase() + "%')");
                    if (i != requestParam.getGeneralText().size() - 1) {
                        sb.append(" OR");
                    }
                }
                sb.append(" )");
            }
            if (ObjectUtil.isNotEmpty(requestParam.getInvoiceFlag()) && !requestParam.getInvoiceFlag()) {
                sb.append(" AND not exists (select 1 from invoice i where i.job_id = trp.job_id and i.talent_id = trp.talent_id and i.status != 7 and i.id = inv.id) ");
                sb.append(" AND not exists (select 1 from t_contractor_invoice tc where tc.job_id = trp.job_id and tc.talent_id = trp.talent_id and tc.invoice_status != 3 and tc.id = tcin.id) ");
            }
            if (ObjectUtil.isNotEmpty(requestParam.getSubmitToJobStartDate()) && ObjectUtil.isNotEmpty(requestParam.getSubmitToJobEndDate())) {
                sb.append(" and stj.created_date between ").append(requestParam.getSubmitToJobStartDate()).append(" and ").append(requestParam.getSubmitToJobEndDate());
            }
            if(Boolean.TRUE.equals(requestParam.getProcessStop())) {
                sb.append(" AND trpss.id IS NOT NULL ");
            }

            if (requestParam.getSources() != null) {
                if (requestParam.getSources().contains(ResumeSourceType.AGENCY)) {
                    if (Objects.nonNull(requestParam.getAgencyIds()) && !requestParam.getAgencyIds().isEmpty()) {
                        if (requestParam.getSources().size() > 1) { // search agency and other source
                            String sourcesStr = requestParam.getSources().stream().map(source -> "'" + source + "'").collect(Collectors.joining(","));
                            sb.append(" AND t_info.source IN (").append(sourcesStr).append(") ");
                            String agencyIdStr = requestParam.getAgencyIds().stream().map(String::valueOf).collect(Collectors.joining(","));
                            sb.append(" AND (asa.id IS NULL OR asa.agency_id IN (").append(agencyIdStr).append(") )");
                        } else { //only search agency
                            String agencyIdStr = requestParam.getAgencyIds().stream().map(String::valueOf).collect(Collectors.joining(","));
                            sb.append(" AND asa.agency_id IN (").append(agencyIdStr).append(") ");
                        }
                    } else {
                        String sourcesStr = requestParam.getSources().stream().filter(source -> !ResumeSourceType.AGENCY.equals(source)).map(source -> "'" + source + "'").collect(Collectors.joining(","));
                        sb.append(" AND t_info.source IN (").append(sourcesStr).append(") ");
                    }
                } else {
                    String sourcesStr = requestParam.getSources().stream().map(source -> "'" + source + "'").collect(Collectors.joining(","));
                    sb.append(" AND t_info.source IN (").append(sourcesStr).append(") ");
                }
            }
        }

        return sb.toString();
    }


    public String jobTypeDBValues(List<Long> processList) {
        String s = "";
        for (Long process : processList) {
            s += "," + process;
        }
        return "(" + s.substring(1) + ")";
    }
}
