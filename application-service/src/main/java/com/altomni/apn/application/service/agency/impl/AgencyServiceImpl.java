package com.altomni.apn.application.service.agency.impl;

import com.altomni.apn.application.dto.AgencyActivityForApplicationDTO;
import com.altomni.apn.application.service.agency.AgencyClient;
import com.altomni.apn.application.service.agency.AgencyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class AgencyServiceImpl implements AgencyService {

    @Resource
    private AgencyClient agencyClient;

    @Override
    @Async
    public void createAgencyActivity(AgencyActivityForApplicationDTO agencyActivityForApplicationDTO) {
        try {
            ResponseEntity<Void> response = agencyClient.createAgencyActivity(agencyActivityForApplicationDTO);
            if (response != null && response.getStatusCode() == HttpStatus.OK){
                log.info("[AgencyService: createAgencyActivity] create agency activity success, agencyActivityDTO: {}", agencyActivityForApplicationDTO);
            } else {
                log.info("[AgencyService: createAgencyActivity] create agency activity failed, agencyActivityDTO: {}, response: {}", agencyActivityForApplicationDTO, response);
            }
        } catch (Exception e) {
            log.error("[AgencyService: createAgencyActivity] create agency activity error, agencyActivityDTO: {}", agencyActivityForApplicationDTO, e);
        }

    }
}
