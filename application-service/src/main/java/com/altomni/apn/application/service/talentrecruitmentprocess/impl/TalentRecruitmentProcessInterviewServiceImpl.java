package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.TalentRecruitmentProcessInterview;
import com.altomni.apn.application.domain.TalentRecruitmentProcessNode;
import com.altomni.apn.application.repository.TalentRecruitmentProcessInterviewRepository;
import com.altomni.apn.application.repository.TalentRecruitmentProcessNodeRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessInterviewService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserService;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessInterviewVO;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing TalentRecruitmentProcessInterview.
 */
@Service
@Transactional
@Slf4j
public class TalentRecruitmentProcessInterviewServiceImpl implements TalentRecruitmentProcessInterviewService {

    @Resource
    private TalentRecruitmentProcessInterviewRepository interviewRepository;
    @Resource
    private TalentRecruitmentProcessKpiUserService kpiUserService;
    @Resource
    private TalentRecruitmentProcessIpgAgreedPayRateService agreedPayRateService;
    @Resource
    private TalentRecruitmentProcessNodeRepository talentRecruitmentProcessNodeRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Override
    public void updateNoteOnly(TalentRecruitmentProcessInterviewVO interviewVO) {
        this.updateNoteOnly(interviewVO.getId(), interviewVO.getNote());
    }

    @Override
    public void updateNoteOnly(Long interviewId, String note) {
        if (interviewId != null) {
            Optional<TalentRecruitmentProcessInterview> exist = interviewRepository.findById(interviewId);
            if (exist.isEmpty()) {
                throw new NotFoundException("Not found interview information by id: " + interviewId);
            }
//            exist.get().setNote(note);
//            exist.get().setLastModifiedDate(Instant.now());
//            exist.get().setLastModifiedBy(SecurityUtils.getUserUid());
//            interviewRepository.saveAndFlush(exist.get());
            interviewRepository.updateNoteOnly(interviewId, note, SecurityUtils.getUserId());
        }
    }

    /**
     * Save a talentRecruitmentProcessInterview.
     *
     * @param interviewVO the entity to save
     * @return the persisted entity
     */
    @Override
    public TalentRecruitmentProcessInterviewVO save(TalentRecruitmentProcessInterviewVO interviewVO) {
        TalentRecruitmentProcessInterview result;

        interviewRepository.updateFinalRoundByTalentRecruitmentProcessId(interviewVO.getTalentRecruitmentProcessId());
        log.info("save interview,update final round is 0,talentRecruitmentProcessId:{}",interviewVO.getTalentRecruitmentProcessId());

        if (interviewVO.getId() != null) {
            Optional<TalentRecruitmentProcessInterview> exist = interviewRepository.findById(interviewVO.getId());
            if (exist.isEmpty()) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_INTERVIEWISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(interviewVO.getId()), applicationApiPromptProperties.getAppl()));
            }
            ServiceUtils.myCopyProperties(interviewVO, exist.get());
            exist.get().setLastModifiedDate(Instant.now());
            exist.get().setLastModifiedBy(SecurityUtils.getUserUid());
            result = interviewRepository.saveAndFlush(exist.get());
        } else {
            result = interviewRepository.save(TalentRecruitmentProcessInterview.fromVO(interviewVO));
        }
        agreedPayRateService.save(interviewVO.getTalentRecruitmentProcessId(), interviewVO.getAgreedPayRate());
        TalentRecruitmentProcessNode activeNode = talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeStatus(interviewVO.getTalentRecruitmentProcessId(), NodeStatus.ACTIVE);
//        if (Constants.TENANT_IPG.equals(SecurityUtils.getTenantId()) ||
//                (!Constants.TENANT_IPG.equals(SecurityUtils.getTenantId())
//                        && NodeType.OFFER.toDbValue() >= activeNode.getNodeType().toDbValue())) {
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId()) ||
                (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())
                        && NodeType.OFFER.toDbValue() >= activeNode.getNodeType().toDbValue())) {
            kpiUserService.save(interviewVO.getTalentRecruitmentProcessId(), interviewVO.getKpiUsers());
        }
        return toVO(result);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TalentRecruitmentProcessInterviewVO> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVOs(talentRecruitmentProcessId);
    }

    private TalentRecruitmentProcessInterviewVO toVO(TalentRecruitmentProcessInterview interview) {
        if (interview == null) {
            return null;
        }
        TalentRecruitmentProcessInterviewVO result = new TalentRecruitmentProcessInterviewVO();
        ServiceUtils.myCopyProperties(interview, result);
        return result;
    }

    private List<TalentRecruitmentProcessInterviewVO> toVOs(Long talentRecruitmentProcessId) {
        List<TalentRecruitmentProcessInterview> interviews = interviewRepository.findAllByTalentRecruitmentProcessIdOrderByCreatedDateDesc(talentRecruitmentProcessId);
        if (CollectionUtils.isNotEmpty(interviews)) {
            return interviews.stream().map(this::toVO).collect(Collectors.toList());
        }
        return Lists.newArrayList();
    }

}
