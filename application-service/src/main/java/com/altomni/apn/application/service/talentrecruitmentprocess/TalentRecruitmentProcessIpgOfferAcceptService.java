package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgOfferAccept;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferAcceptVO;

/**
 * Service Interface for managing {@link TalentRecruitmentProcessIpgOfferAccept}.
 */
public interface TalentRecruitmentProcessIpgOfferAcceptService {

    TalentRecruitmentProcessIpgOfferAcceptVO updateNoteOnly(TalentRecruitmentProcessIpgOfferAcceptVO offerAcceptVO);

    TalentRecruitmentProcessIpgOfferAcceptVO updateNoteOnly(Long talentRecruitmentProcessId, String note);

    TalentRecruitmentProcessIpgOfferAcceptVO save(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessIpgOfferAcceptVO offerAcceptVO);

    TalentRecruitmentProcessIpgOfferAcceptVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

}
