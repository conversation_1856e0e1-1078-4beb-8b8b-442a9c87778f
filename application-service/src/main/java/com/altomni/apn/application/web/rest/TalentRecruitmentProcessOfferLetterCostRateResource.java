package com.altomni.apn.application.web.rest;

import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateService;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateList;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateVO;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Optional;

/**
 * REST controller for managing TalentRecruitmentProcessIpgOfferLetterCostRateVO.
 */
@RestController
@RequestMapping("/api/v3")
public class TalentRecruitmentProcessOfferLetterCostRateResource {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessOfferLetterCostRateResource.class);

    @Resource
    private TalentRecruitmentProcessIpgOfferLetterCostRateService ipgOfferLetterCostRateService;


    @GetMapping("/talent-recruitment-processes/ipg-offer-letter-cost-rates")
    public ResponseEntity<TalentRecruitmentProcessIpgOfferLetterCostRateList> getAllOfferLetterCostRates(@RequestParam(required = false) Long recruitmentProcessId) {
        log.info("[APN: TalentRecruitmentProcessIpgOfferLetterCostRate @{}] REST request to get all TalentRecruitmentProcessIpgOfferLetterCostRates", SecurityUtils.getUserId());
        return new ResponseEntity<>(ipgOfferLetterCostRateService.findAll(recruitmentProcessId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-processes/ipg-offer-letter-cost-rates/{code}")
    public ResponseEntity<TalentRecruitmentProcessIpgOfferLetterCostRateVO> getOfferLetterCostRate(@PathVariable("code") String code) {
        log.info("[APN: TalentRecruitmentProcessIpgOfferLetterCostRate @{}] REST request to get TalentRecruitmentProcessIpgOfferLetterCostRate : {}", SecurityUtils.getUserId(), code);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(ipgOfferLetterCostRateService.findOne(code)));
    }
}
