package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.application.repository.RecruitmentProcessRepository;
import com.altomni.apn.application.service.job.JobClient;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessForTalentService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessNodeService;
import com.altomni.apn.common.constants.TalentRecruitmentProcessConstants;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.CoAmUserCountryVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessForTalentVO;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.StringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing TalentRecruitmentProcessForJobDetail.
 */
@Service
@Transactional
public class TalentRecruitmentProcessForTalentServiceImpl implements TalentRecruitmentProcessForTalentService {

    @PersistenceContext
    private EntityManager entityManager;
    @Resource
    private TalentRecruitmentProcessNodeService talentRecruitmentProcessNodeService;

    @Resource
    private RecruitmentProcessRepository recruitmentProcessRepository;

    @Resource
    private JobClient jobClient;

    @Override
    public List<TalentRecruitmentProcessForTalentVO> getTalentRecruitmentProcessByTalentId(Long talentId) {
        String query = """
                SELECT trp.id                                             talent_recruitment_process_id,
                        trp.talent_id                                            talent_id,
                        t.full_name                                              talent_name,
                        JSON_EXTRACT(j_info.extended_info, '$.preferredSkills', '$.requiredSkills') skills,
                        trp.job_id                                               job_id,
                        j.title                                                  job_title,
                        j.status                                                 job_status,
                        j.open_time                                           open_time,
                        rp.job_type                                              job_type,
                        j.company_id                                             company_id,
                        (select c.full_business_name from company c where c.id = j.company_id) company_name,
                        (select trpiapr.agreed_pay_rate
                         from talent_recruitment_process_ipg_agreed_pay_rate trpiapr
                         where trpiapr.talent_recruitment_process_id = trp.id)   agreed_pay_rate,
                        (select trpn.node_type from talent_recruitment_process_node trpn where trp.id = trpn.talent_recruitment_process_id and trpn.node_status in(1,4)) last_node_type,
                        (select trpn.node_status from talent_recruitment_process_node trpn where trp.id = trpn.talent_recruitment_process_id and trpn.node_status in(1,4)) last_node_status,
                        trp.last_modified_date                                   last_modified_date,
                        (select group_concat(u.first_name, ' ', u.last_name)
                         from user u,
                              talent_recruitment_process_kpi_user trpku
                         where trp.id = trpku.talent_recruitment_process_id
                           and trpku.user_id = u.id
                           and trpku.user_role = 1)                              recruiter,
                        (select group_concat(u.first_name, ' ', u.last_name)
                         from user u,
                              talent_recruitment_process_kpi_user trpku
                         where trp.id = trpku.talent_recruitment_process_id
                           and trpku.user_id = u.id
                           and trpku.user_role = 0)                             account_manager,
                        (select group_concat(u.id, ';',u.first_name, ' ', u.last_name, ';', IFNULL(trpku.user_role, ''))
                         from user u,
                              talent_recruitment_process_kpi_user trpku
                         where trp.id = trpku.talent_recruitment_process_id
                           and trpku.user_id = u.id)                             kpi_users,
                        j.code                                                   job_code,
                        onboard_date.onboard_date                                onboard_date, 
                        eliminate.reason                                         eliminate_reason, 
                        eliminate.note                                           eliminate_note, 
                        j.pteam_id, 
                        j.created_by, 
                        (select group_concat(ujr.user_id)
                         from user_job_relation ujr 
                         where ujr.job_id = j.id and ujr.status = 1)             assigned_user_ids, 
                        resign.id resignId, 
                        sta.id convert_to_fte,
                        (select group_concat(concat(CONCAT(u.first_name, ' ', u.last_name), '-', trpku.country))
                         from user u,
                              talent_recruitment_process_kpi_user trpku
                         where trp.id = trpku.talent_recruitment_process_id
                           and trpku.user_id = u.id
                           and trpku.user_role = 7)                             co_account_manager, 
                        trp.created_date                                        application_submitted_time, 
                        asa.id                                                  agency_id, 
                        agency.name                                             agency_name 
                 FROM talent_recruitment_process trp 
                          left join recruitment_process rp on rp.id = trp.recruitment_process_id 
                          LEFT JOIN talent t ON t.id = trp.talent_id
                          LEFT JOIN job j ON j.id = trp.job_id
                          LEFT JOIN job_additional_info j_info ON j_info.id = j.additional_info_id 
                          LEFT JOIN talent_recruitment_process_onboard_date onboard_date ON trp.id = onboard_date.talent_recruitment_process_id 
                          LEFT JOIN talent_recruitment_process_eliminate eliminate ON trp.id = eliminate.talent_recruitment_process_id 
                          LEFT JOIN talent_recruitment_process_resignation resign ON trp.id = resign.talent_recruitment_process_id 
                          LEFT JOIN start sta ON resign.talent_recruitment_process_id = sta.talent_recruitment_process_id and sta.start_type=5 
                          LEFT JOIN agency_submit_application asa ON asa.talent_recruitment_process_id = trp.id 
                          LEFT JOIN agency ON asa.agency_id = agency.id 
                 where trp.tenant_id = ?1 and t.id = ?2
                """;
        List<Object[]> objects = entityManager.createNativeQuery(query)
                .setParameter(1, SecurityUtils.getTenantId())
                .setParameter(2, talentId)
                .getResultList();
        List<TalentRecruitmentProcessForTalentVO> result = objectToVO(objects);
        result.forEach(x -> {
            if (StringUtils.isNotBlank(x.getCoAccountManager())) {
                List<CoAmUserCountryVO> userCountryList = new ArrayList<>();
                Arrays.stream(x.getCoAccountManager().split(",")).forEach(v -> {
                    String[] amCountry = v.split("-");
                    CoAmUserCountryVO vo = new CoAmUserCountryVO();
                    vo.setUserName(String.valueOf(amCountry[0]));
                    vo.setCountryId(amCountry[1]);
                    userCountryList.add(vo);
                });
                x.setCoAmList(userCountryList);
            }
        });
        return result;
    }

    private List<TalentRecruitmentProcessForTalentVO> objectToVO(List<Object[]> objects) {
        if (CollectionUtils.isEmpty(objects)) {
            return Lists.newArrayList();
        }
        Long userId = SecurityUtils.getUserId();
        Long tenantId = SecurityUtils.getTenantId();
        Set<Long> teamIdsForPrivateJob = recruitmentProcessRepository.findTeamIdsForPrivateJob(tenantId);
        Set<Long> allUnauthorizedPrivateJobIds = recruitmentProcessRepository.findAllUnauthorizedPrivateJobIds(tenantId, userId);
        List<TalentRecruitmentProcessForTalentVO> result = new ArrayList<>();
        for (Object[] obj : objects) {
            TalentRecruitmentProcessForTalentVO detailVO = new TalentRecruitmentProcessForTalentVO();
            Long talentRecruitmentProcessId = Long.valueOf(StringUtil.valueOf(obj[0]));
            detailVO.setId(talentRecruitmentProcessId);
            detailVO.setTalentId(Long.valueOf(StringUtil.valueOf(obj[1])));
            detailVO.setTalentName(StringUtil.valueOf(obj[2]));
            String skillJsonStr = StringUtil.valueOf(obj[3]);
            if (StringUtils.isBlank(skillJsonStr)) {
                detailVO.setSkills(null);
            } else {
                JSONArray jsonArray = JSONArray.parseArray(skillJsonStr);
                JSONArray skillArray = new JSONArray();
                for (int i = 0; i < jsonArray.size(); i++) {
                    skillArray.addAll(jsonArray.getJSONArray(i));
                }
                detailVO.setSkills(skillArray.toJSONString());
            }

            detailVO.setJobId(Long.valueOf(StringUtil.valueOf(obj[4])));
            detailVO.setJobTitle(StringUtil.valueOf(obj[5]));
            if(ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[6]))) {
                detailVO.setJobStatus(JobStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[6]))));
            }
            detailVO.setOpenTime(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[7])));
            if(ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[8]))){
                detailVO.setJobType(JobType.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[8]))));
            }

            detailVO.setCompanyId(obj[9] != null ? Long.valueOf(StringUtil.valueOf(obj[9])) : null);
            detailVO.setCompanyName(StringUtil.valueOf(obj[10]));
            detailVO.setAgreedPayRate(obj[11] != null ? new BigDecimal(StringUtil.valueOf(obj[11])) : null);

            if(ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[12]))) {
                detailVO.setLastNodeType(NodeType.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[12]))));
            }
            if(ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[13]))) {
                detailVO.setLastNodeStatus(NodeStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[13]))));
            }
            detailVO.setLastModifiedDate(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[14])));

            //kpi users setting
            detailVO.setRecruiter(StringUtil.valueOf(obj[15]));
            detailVO.setAccountManager(StringUtil.valueOf(obj[16]));
            String kpiUsers = StringUtil.valueOf(obj[17]);
            Set<Long> kpiUserIds = new HashSet<>();
            List<String> kpiUserNames = new ArrayList<>();
            Map<Integer, List<Long>> kpiUserMap = new HashMap<>();

            if (StringUtils.isNotEmpty(kpiUsers)){
                for (String kpiUser : kpiUsers.split(",")) {
//                    String[] userIdName = kpiUser.split(";");
//                    kpiUserIds.add(Long.parseLong(userIdName[0]));
//                    kpiUserNames.add(userIdName[1]);

                    String[] userIdNameRole = kpiUser.split(";");
                    if (userIdNameRole.length >= 3) {
                        Long id = Long.parseLong(userIdNameRole[0]);
                        String userName = userIdNameRole[1];
                        Integer userRole = StringUtils.isBlank(userIdNameRole[2]) ? null : Integer.parseInt(userIdNameRole[2]);

                        kpiUserIds.add(id);
                        kpiUserNames.add(userName);

                        // 把 userRole 作为 key，userId 放到对应的 List 里
                        kpiUserMap.computeIfAbsent(userRole, k -> new ArrayList<>()).add(id);
                    } else if (userIdNameRole.length == 2) {
                        Long id = Long.parseLong(userIdNameRole[0]);
                        String userName = userIdNameRole[1];

                        kpiUserIds.add(id);
                        kpiUserNames.add(userName);
                    }
                }
            }
            detailVO.setKpiUsers(String.join(",", kpiUserNames));

            detailVO.setKpiUserIds(new ArrayList<>(kpiUserIds));
            detailVO.setRecruiterIds(kpiUserMap.getOrDefault(1, Collections.emptyList()));
            detailVO.setAccountManagerIds(kpiUserMap.getOrDefault(0, Collections.emptyList()));
            detailVO.setCoAmIds(kpiUserMap.getOrDefault(7, Collections.emptyList()));
            detailVO.setBdOwnerIds(kpiUserMap.getOrDefault(8, Collections.emptyList()));
            detailVO.setSaleLeadOwnerIds(kpiUserMap.getOrDefault(9, Collections.emptyList()));


            detailVO.setJobCode(StringUtil.valueOf(obj[18]));

            detailVO.setOnboardDate(DateUtil.stringToLocalDate(StringUtil.valueOf(obj[19])));

            detailVO.setTalentRecruitmentProcessNodes(talentRecruitmentProcessNodeService.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId));

            if (ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[20]))) {
                detailVO.setEliminateReason(EliminateReason.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[20]))));
            }
            detailVO.setEliminateNote(StringUtil.valueOf(obj[21]));
            detailVO.setPrivateJob(Objects.nonNull(obj[22]) && teamIdsForPrivateJob.contains(Long.parseLong(obj[22].toString())));
            if (detailVO.isPrivateJob()){ //保密职位(private job)
                if (allUnauthorizedPrivateJobIds.contains(detailVO.getJobId())){
                    detailVO.setHasPermission(Boolean.FALSE);
                }
//                else if (kpiUserIds.contains(userId)){
//                    detailVO.setHasPermission(Boolean.TRUE); // 流程参与者有权限
//                }
                else if (Objects.nonNull(obj[23]) && String.valueOf(obj[23]).split(",")[0].equals(String.valueOf(userId))){ // 职位创建者有权限puser_id
                    detailVO.setHasPermission(Boolean.TRUE);
                } else if (Objects.nonNull(obj[24]) && Arrays.stream(String.valueOf(obj[24]).split(",")).collect(Collectors.toSet()).contains(String.valueOf(userId))){ // 岗位负责人有权限(user_job_relation中active的users)
                    detailVO.setHasPermission(Boolean.TRUE);
                } else {
                    detailVO.setHasPermission(Boolean.FALSE);
                }
            }

//            else { //普通职位，不用打码；不表示当前用户有job的权限
//                List<JobBriefDTO> jobs = jobClient.getBriefJobListByIds(List.of(detailVO.getJobId())).getBody(); //通过job-service查询，来确定当前user对普通职位的权限
//                if (CollectionUtils.isNotEmpty(jobs) && Objects.equals(jobs.get(0).getId(), detailVO.getJobId())) {
//                    detailVO.setHasPermission(Boolean.TRUE);
//                } else {
//                    detailVO.setHasPermission(Boolean.FALSE);
//                }
//            }

            if (detailVO.isPrivateJob() && !detailVO.isHasPermission()){ //保密职位，且没有职位权限
                // 没有私有职位的权限，职位名称，职位ID，公司打码处理
                detailVO.setJobTitle(TalentRecruitmentProcessConstants.MASK);
                detailVO.setJobId(null);
                detailVO.setCompanyId(null);
                detailVO.setCompanyName(TalentRecruitmentProcessConstants.MASK);
            }
            detailVO.setResigned(Objects.nonNull(obj[25]));
            boolean isConvertedToFte = Objects.nonNull(obj[26]);
            // 如果该流程是 convert to FTE，则强制标为非离职，这是一个特殊的需求
            if (isConvertedToFte){
                detailVO.setResigned(Boolean.FALSE);
            }
            detailVO.setCoAccountManager(StringUtil.valueOf(obj[27]));

            detailVO.setTalentRecruitmentProcessSubmittedTime(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[28])));
            detailVO.setAgencyId(obj[29] != null ? Long.valueOf(StringUtil.valueOf(obj[29])) : null);
            detailVO.setAgencyName(StringUtil.valueOf(obj[30]));
            result.add(detailVO);
        }

        Set<Long> jobIds = result.stream().filter(detail -> !detail.isPrivateJob()).map(TalentRecruitmentProcessForTalentVO::getJobId).collect(Collectors.toSet()); //获取所有普通职位id

        List<JobBriefDTO> jobs = jobClient.getBriefJobListByIds(jobIds).getBody(); //通过job-service查询，来确定当前user对普通职位的权限
        if (CollectionUtils.isNotEmpty(jobs)){
            jobIds = jobs.stream().map(JobBriefDTO::getId).collect(Collectors.toSet());
        } else {
            jobIds = new HashSet<>();
        }

        Set<Long> finalJobIds = jobIds;

//        result = result.stream().filter(detail -> !detail.isPrivateJob()).map(detailVO -> {
        result = result.stream().map(detailVO -> {
            if (detailVO.isPrivateJob()){
                return detailVO;
            }

            if (CollectionUtils.isNotEmpty(finalJobIds) && finalJobIds.contains(detailVO.getJobId())){
                detailVO.setHasPermission(Boolean.TRUE);
            } else {
                detailVO.setHasPermission(Boolean.FALSE);
            }
            return detailVO;
        }).collect(Collectors.toList());

        return result;
    }

}
