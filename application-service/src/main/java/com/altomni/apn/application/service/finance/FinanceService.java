package com.altomni.apn.application.service.finance;

import com.altomni.apn.common.dto.application.dashboard.MyCandidate;
import com.altomni.apn.finance.domain.start.StartContractRate;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

public interface FinanceService {

    StartDTO createStart(@Valid @RequestBody StartDTO start);

    List<StartDTO> getActiveStartByTalentId(@PathVariable("talentId") Long talentId);

    BigDecimal getTotalBillAmount(StartContractRate startContractRate);

    List<MyCandidate> getTotalBillAmountByTalentRecruitmentProcessId(List<Long> talentRecruitmentProcessId);
}

