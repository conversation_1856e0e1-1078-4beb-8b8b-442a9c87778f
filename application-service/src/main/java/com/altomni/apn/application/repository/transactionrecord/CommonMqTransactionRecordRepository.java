package com.altomni.apn.application.repository.transactionrecord;

import com.altomni.apn.common.domain.transactionrecord.CommonMqTransactionRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.util.List;

@Repository
public interface CommonMqTransactionRecordRepository extends JpaRepository<CommonMqTransactionRecord, BigInteger> {

    @Modifying
    @Transactional
    @Query(value = " update t_mq_transaction_record set send_status=?2 where id = ?1", nativeQuery = true)
    void updateStatusById(Long id, Integer sendStatus);

    List<CommonMqTransactionRecord> findBySendStatusInAndSendCountLessThan(List<Integer> sendStatus, Integer sentCount);

    @Modifying
    @Transactional
    @Query(value = " update t_mq_transaction_record set send_count=?2 where id = ?1", nativeQuery = true)
    void updateStatusAndSendCountById(Long id, Integer count);


    @Modifying
    @Transactional
    @Query(value = " update t_mq_transaction_record set send_status=?3 where bus_id = ?1 and bus_type=?2", nativeQuery = true)
    void updateStatusByBusIdAndBusType(Long busId, Integer busType,Integer sendStatus);
}