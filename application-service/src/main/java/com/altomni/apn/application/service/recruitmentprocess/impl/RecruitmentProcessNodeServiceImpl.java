package com.altomni.apn.application.service.recruitmentprocess.impl;

import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.RecruitmentProcess;
import com.altomni.apn.application.domain.RecruitmentProcessNode;
import com.altomni.apn.application.repository.RecruitmentProcessNodeRepository;
import com.altomni.apn.application.service.recruitmentprocess.RecruitmentProcessNodeService;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessNodeVO;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing RecruitmentProcessNode.
 */
@Service
@Transactional
public class RecruitmentProcessNodeServiceImpl implements RecruitmentProcessNodeService {

    private final Logger log = LoggerFactory.getLogger(RecruitmentProcessNodeServiceImpl.class);

    @Resource
    private RecruitmentProcessNodeRepository recruitmentProcessNodeRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Override
    public void initRecruitmentProcessNodeForFirstParty(RecruitmentProcess recruitmentProcess) {
        RecruitmentProcessNode submitToJob = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Submit to job").nodeType(NodeType.SUBMIT_TO_JOB)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        RecruitmentProcessNode interview = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Interview").nodeType(NodeType.INTERVIEW)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        RecruitmentProcessNode offer = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Offer").nodeType(NodeType.OFFER)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        RecruitmentProcessNode commission = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Commission").nodeType(NodeType.COMMISSION)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        RecruitmentProcessNode onboard = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Onboard").nodeType(NodeType.ON_BOARD)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        submitToJob.setNextNodeId(interview.getId());
        recruitmentProcessNodeRepository.save(submitToJob);
        interview.setNextNodeId(offer.getId());
        recruitmentProcessNodeRepository.save(interview);
        offer.setNextNodeId(commission.getId());
        recruitmentProcessNodeRepository.save(offer);
        commission.setNextNodeId(onboard.getId());
        recruitmentProcessNodeRepository.save(commission);
    }

    @Override
    public void initRecruitmentProcessNodeForSecondParty(RecruitmentProcess recruitmentProcess) {
        RecruitmentProcessNode submitToJob = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Submit to job").nodeType(NodeType.SUBMIT_TO_JOB)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        RecruitmentProcessNode submitToClient = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Submit to client").nodeType(NodeType.SUBMIT_TO_CLIENT)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        RecruitmentProcessNode interview = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Interview").nodeType(NodeType.INTERVIEW)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        RecruitmentProcessNode offer = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Offer").nodeType(NodeType.OFFER)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        RecruitmentProcessNode commission = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Commission").nodeType(NodeType.COMMISSION)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        RecruitmentProcessNode onboard = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
            .recruitmentProcessId(recruitmentProcess.getId()).name("Onboard").nodeType(NodeType.ON_BOARD)
            .description("Default").tenantId(recruitmentProcess.getTenantId()));
        submitToJob.setNextNodeId(submitToClient.getId());
        recruitmentProcessNodeRepository.save(submitToJob);
        submitToClient.setNextNodeId(interview.getId());
        recruitmentProcessNodeRepository.save(submitToClient);
        interview.setNextNodeId(offer.getId());
        recruitmentProcessNodeRepository.save(interview);
        offer.setNextNodeId(commission.getId());
        recruitmentProcessNodeRepository.save(offer);
        commission.setNextNodeId(onboard.getId());
        recruitmentProcessNodeRepository.save(commission);
    }

    @Override
    @Transactional
    public void initRecruitmentProcessNode(List<RecruitmentProcessNodeVO> recruitmentProcessNodes) {
        List<RecruitmentProcessNode> nodes = recruitmentProcessNodes.stream().map(RecruitmentProcessNode::new).sorted(Comparator.comparing(n -> n.getNodeType().toDbValue())).collect(Collectors.toList());
        recruitmentProcessNodeRepository.saveAllAndFlush(nodes);

//        List<RecruitmentProcessNode> nodes = new ArrayList<>();
//        recruitmentProcessNodes.forEach(n -> {
//            createNode(n, nodes);
//        });

        //update next node id
        for (int i = 0; i < nodes.size() - 1; i++) {
            RecruitmentProcessNode current = nodes.get(i);
            RecruitmentProcessNode next = nodes.get(i+1);
            current.setNextNodeId(next.getId());
        }
        recruitmentProcessNodeRepository.saveAllAndFlush(nodes);
    }

    private void createNode(RecruitmentProcessNodeVO node, List<RecruitmentProcessNode> nodes) {
        NodeType nodeType = node.getNodeType();
        switch (nodeType) {
            case SUBMIT_TO_JOB:
                RecruitmentProcessNode submitToJob = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                        .recruitmentProcessId(node.getRecruitmentProcessId()).name(node.getName()).nodeType(NodeType.SUBMIT_TO_JOB)
                        .description(node.getDescription()).tenantId(node.getTenantId()));
                nodes.add(submitToJob);
                break;
            case SUBMIT_TO_CLIENT:
                RecruitmentProcessNode submitToClient = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                        .recruitmentProcessId(node.getRecruitmentProcessId()).name(node.getName()).nodeType(NodeType.SUBMIT_TO_CLIENT)
                        .description(node.getDescription()).tenantId(node.getTenantId()));
                nodes.add(submitToClient);
                break;
            case INTERVIEW:
                RecruitmentProcessNode interview = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                        .recruitmentProcessId(node.getRecruitmentProcessId()).name(node.getName()).nodeType(NodeType.INTERVIEW)
                        .description(node.getDescription()).tenantId(node.getTenantId()));
                nodes.add(interview);
                break;
            case OFFER:
                RecruitmentProcessNode offer = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                        .recruitmentProcessId(node.getRecruitmentProcessId()).name(node.getName()).nodeType(NodeType.OFFER)
                        .description(node.getDescription()).tenantId(node.getTenantId()));
                nodes.add(offer);
                break;
            case COMMISSION:
                RecruitmentProcessNode commission = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                        .recruitmentProcessId(node.getRecruitmentProcessId()).name(node.getName()).nodeType(NodeType.COMMISSION)
                        .description(node.getDescription()).tenantId(node.getTenantId()));
                nodes.add(commission);
                break;
            case ON_BOARD:
                RecruitmentProcessNode onboard = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                        .recruitmentProcessId(node.getRecruitmentProcessId()).name(node.getName()).nodeType(NodeType.ON_BOARD)
                        .description(node.getDescription()).tenantId(node.getTenantId()));
                nodes.add(onboard);
                break;
        }
    }

    @Override
    public void initRecruitmentProcessNodeForIpg(RecruitmentProcess recruitmentProcess) {
        if (JobType.PAY_ROLL.equals(recruitmentProcess.getJobType())) {
            RecruitmentProcessNode submitToJob = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                    .recruitmentProcessId(recruitmentProcess.getId()).name("Submit to job").nodeType(NodeType.SUBMIT_TO_JOB)
                    .description("Default").tenantId(recruitmentProcess.getTenantId()));
            RecruitmentProcessNode onboard = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                    .recruitmentProcessId(recruitmentProcess.getId()).name("Onboard").nodeType(NodeType.ON_BOARD)
                    .description("Default").tenantId(recruitmentProcess.getTenantId()));
            submitToJob.setNextNodeId(onboard.getId());
            recruitmentProcessNodeRepository.save(submitToJob);
        } else {
            RecruitmentProcessNode submitToJob = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                    .recruitmentProcessId(recruitmentProcess.getId()).name("Submit to job").nodeType(NodeType.SUBMIT_TO_JOB)
                    .description("Default").tenantId(recruitmentProcess.getTenantId()));
            RecruitmentProcessNode submitToClient = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                    .recruitmentProcessId(recruitmentProcess.getId()).name("Submit to client").nodeType(NodeType.SUBMIT_TO_CLIENT)
                    .description("Default").tenantId(recruitmentProcess.getTenantId()));
            RecruitmentProcessNode interview = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                    .recruitmentProcessId(recruitmentProcess.getId()).name("Interview").nodeType(NodeType.INTERVIEW)
                    .description("Default").tenantId(recruitmentProcess.getTenantId()));
            RecruitmentProcessNode offer = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                    .recruitmentProcessId(recruitmentProcess.getId()).name("Offer").nodeType(NodeType.OFFER)
                    .description("Default").tenantId(recruitmentProcess.getTenantId()));
            RecruitmentProcessNode offerAccept = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                    .recruitmentProcessId(recruitmentProcess.getId()).name("Offer Accept").nodeType(NodeType.OFFER_ACCEPT)
                    .description("Default").tenantId(recruitmentProcess.getTenantId()));
            RecruitmentProcessNode onboard = recruitmentProcessNodeRepository.save(new RecruitmentProcessNode()
                    .recruitmentProcessId(recruitmentProcess.getId()).name("Onboard").nodeType(NodeType.ON_BOARD)
                    .description("Default").tenantId(recruitmentProcess.getTenantId()));
            submitToJob.setNextNodeId(submitToClient.getId());
            recruitmentProcessNodeRepository.save(submitToJob);
            submitToClient.setNextNodeId(interview.getId());
            recruitmentProcessNodeRepository.save(submitToClient);
            interview.setNextNodeId(offer.getId());
            recruitmentProcessNodeRepository.save(interview);
            offer.setNextNodeId(offerAccept.getId());
            recruitmentProcessNodeRepository.save(offer);
            offerAccept.setNextNodeId(onboard.getId());
            recruitmentProcessNodeRepository.save(offerAccept);
        }
    }

    /**
     * Save a tenantRecruitmentProcessNode.
     *
     * @param recruitmentProcessNode the entity to save
     * @return the persisted entity
     */
    @Override
    public RecruitmentProcessNode update(Long recruitmentProcessId, Long nodeId, RecruitmentProcessNode recruitmentProcessNode) {
        log.debug("Request to update RecruitmentProcessNode : {}", recruitmentProcessNode);
        RecruitmentProcessNode exist = findOneById(nodeId);
        if (!Objects.equals(exist.getRecruitmentProcessId(), recruitmentProcessId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTE_UPDATEDIFFID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
        }
        exist.setName(recruitmentProcessNode.getName());
        exist.setDescription(recruitmentProcessNode.getDescription());
        return recruitmentProcessNodeRepository.save(exist);
    }

    private RecruitmentProcessNode findOneById(Long id) {
        if (id == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTE_FINDBYID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
        }
        Optional<RecruitmentProcessNode> recruitmentProcessNode = recruitmentProcessNodeRepository.findById(id);
        if (recruitmentProcessNode.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTE_FINDBYID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(id),applicationApiPromptProperties.getAppl()));
        }
        if (!SecurityUtils.isSystemAdmin() && !SecurityUtils.getTenantId().equals(recruitmentProcessNode.get().getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTE_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(id),applicationApiPromptProperties.getAppl()));
        }
        return recruitmentProcessNode.get();
    }

    private List<RecruitmentProcessNodeVO> toVO(List<RecruitmentProcessNode> nodes) {
        return CollectionUtils.isNotEmpty(nodes) ? nodes.stream().map(this::toVO).collect(Collectors.toList()) : Lists.newArrayList();
    }

    private RecruitmentProcessNodeVO toVO(RecruitmentProcessNode node) {
        RecruitmentProcessNodeVO result = new RecruitmentProcessNodeVO();
        if (node != null) {
            ServiceUtils.myCopyProperties(node, result);
        }
        return result;
    }

    /**
     * Get all the recruitmentProcessId.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public List<RecruitmentProcessNodeVO> findAll(Long recruitmentProcessId) {
        log.debug("Request to get all RecruitmentProcessNodes");
        return toVO(recruitmentProcessNodeRepository.findAllByRecruitmentProcessId(recruitmentProcessId));
    }

    /**
     * Get all the recruitmentProcessId.
     *
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public List<RecruitmentProcessNodeVO> findAllByTenantId(Long recruitmentProcessId, Long tenantId) {
        log.debug("Request to get all RecruitmentProcessNodes");
        return toVO(recruitmentProcessNodeRepository.findAllByRecruitmentProcessIdAndTenantId(recruitmentProcessId, tenantId));
    }

    @Override
    public List<RecruitmentProcessNode> saveAll(List<RecruitmentProcessNode> nodes) {
        return recruitmentProcessNodeRepository.saveAll(nodes);
    }
}
