package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgOfferLetterCostRate;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgContractFeeChargeVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateList;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateVO;

/**
 * Service Interface for managing {@link TalentRecruitmentProcessIpgOfferLetterCostRate}.
 */
public interface TalentRecruitmentProcessIpgOfferLetterCostRateService {

    void validateOfferLetterCostRate(TalentRecruitmentProcessIpgContractFeeChargeVO contractFeeCharge);

    TalentRecruitmentProcessIpgContractFeeChargeVO addEntities(TalentRecruitmentProcessIpgContractFeeChargeVO contractFeeCharge);

    TalentRecruitmentProcessIpgOfferLetterCostRateList findAll(Long recruitmentProcessId);

    TalentRecruitmentProcessIpgOfferLetterCostRateVO findOne(String code);
}
