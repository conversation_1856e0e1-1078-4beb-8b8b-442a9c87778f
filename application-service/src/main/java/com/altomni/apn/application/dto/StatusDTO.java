package com.altomni.apn.application.dto;

import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotNull
    private ActiveStatus status;
}
