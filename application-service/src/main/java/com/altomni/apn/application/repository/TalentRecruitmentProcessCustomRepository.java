package com.altomni.apn.application.repository;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.enumeration.NodeTypeTableEnum;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.sql.Timestamp;
import java.time.Instant;
import java.util.List;

@Repository
public class TalentRecruitmentProcessCustomRepository {

    @PersistenceContext
    private EntityManager entityManager;

    public Instant getTalentRecruitmentProcessNodeLatestByUserIdAndJobId(Long jobId, Long userId, NodeType noteTpe) {
        String sql = " SELECT max(stj.created_date) createdDate FROM talent_recruitment_process trp " +
                " inner join talent_recruitment_process_kpi_user kpi on kpi.talent_recruitment_process_id = trp.id and kpi.user_role != 4 " +
                " left join " + NodeTypeTableEnum.getTableNameByNodeType(noteTpe) + " stj on stj.talent_recruitment_process_id = trp.id " +
                " where trp.job_id = :jobId and kpi.user_id = :userId " +
                " group by trp.job_id ";
        Query query = entityManager.createNativeQuery(sql)
                .setParameter("jobId", jobId)
                .setParameter("userId", userId);
        List<Timestamp> createDateList = query.getResultList();
        if (CollUtil.isEmpty(createDateList)) {
            return null;
        }
        return createDateList.get(0).toInstant();
    }

}
