package com.altomni.apn.application.web.rest;

import com.altomni.apn.application.domain.TalentRecruitmentProcessNode;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessNodeService;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessNodeVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.Instant;
import java.util.List;

/**
 * REST controller for managing TalentRecruitmentProcessNode.
 */
@RestController
@RequestMapping("/api/v3")
public class TalentRecruitmentProcessNodeResource {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessNodeResource.class);

    private final TalentRecruitmentProcessNodeService talentRecruitmentProcessNodeService;

    public TalentRecruitmentProcessNodeResource(TalentRecruitmentProcessNodeService talentRecruitmentProcessNodeService) {
        this.talentRecruitmentProcessNodeService = talentRecruitmentProcessNodeService;
    }

    /**
     * GET  /talent-recruitment-process-nodes : get all the talentRecruitmentProcessNodes.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talentRecruitmentProcessNodes in body
     */
    @GetMapping("/talent-recruitment-process-nodes/talentRecruitmentProcessId/{talentRecruitmentProcessId}")
    public ResponseEntity<List<TalentRecruitmentProcessNodeVO>> getAllTalentRecruitmentProcessNodes(@PathVariable Long talentRecruitmentProcessId) {
        log.info("REST request to get all TalentRecruitmentProcessNodes");
        return new ResponseEntity<>(talentRecruitmentProcessNodeService.findAll(talentRecruitmentProcessId), HttpStatus.OK);
    }

    @GetMapping("/talent-recruitment-process-nodes/{jobId}/{talentId}")
    public ResponseEntity<List<TalentRecruitmentProcessNodeVO>> getAllTalentRecruitmentProcessNodesByTalentIdAndJobId(@PathVariable("jobId") Long jobId, @PathVariable("talentId") Long talentId) {
        log.info("REST request to get all getAllTalentRecruitmentProcessNodesByTalentIdAndJobId by jobId = {}, talentId = {}", jobId, talentId);
        return new ResponseEntity<>(talentRecruitmentProcessNodeService.findAllByJobIdAndTalentId(jobId, talentId), HttpStatus.OK);
    }

    /**
     * 查询用户最近操作job的流程的节点的对应的时间
     * @param jobId
     * @param userId
     * @param nodeType
     * @return
     */
    @GetMapping("/talent-recruitment-process-node-created-date/latest/{jobId}/{userId}/{nodeType}")
    public ResponseEntity<Instant> getTalentRecruitmentProcessNodeLatestByUserIdAndJobId(@PathVariable("jobId") Long jobId, @PathVariable("userId") Long userId, @PathVariable("nodeType") NodeType nodeType) {
        log.info("REST request to get all getTalentRecruitmentProcessNodeLatestByUserIdAndJobId by jobId = {}, talentId = {}, noteType = {}", jobId, userId, nodeType);
        return new ResponseEntity<>(talentRecruitmentProcessNodeService.getTalentRecruitmentProcessNodeLatestByUserIdAndJobId(jobId, userId, nodeType), HttpStatus.OK);
    }


    @GetMapping("/talent-recruitment-process-node/talentRecruitmentProcessId/{talentRecruitmentProcessId}/{nodeType}")
    public ResponseEntity<TalentRecruitmentProcessNodeVO> getNodeByTalentRecruitmentProcessNodeIdAndNodeType(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId, @PathVariable("nodeType") NodeType nodeType) {
        log.info("REST request to get all getTalentRecruitmentProcessNodeLatestByUserIdAndJobId by talentRecruitmentProcessId = {}, nodeType = {}", talentRecruitmentProcessId, nodeType);
        return new ResponseEntity<>(talentRecruitmentProcessNodeService.findNode(talentRecruitmentProcessId,nodeType), HttpStatus.OK);
    }

}
