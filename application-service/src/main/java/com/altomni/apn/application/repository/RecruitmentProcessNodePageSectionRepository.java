package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.RecruitmentProcessNodePageSection;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the RecruitmentProcessNodePageSection entity.
 */
@Repository
public interface RecruitmentProcessNodePageSectionRepository extends JpaRepository<RecruitmentProcessNodePageSection, Long> {

    List<RecruitmentProcessNodePageSection> findAllByTenantIdAndNodeType(Long tenantId, NodeType nodeType);

    List<RecruitmentProcessNodePageSection> findAllByTenantIdAndNodeTypeAndJobType(Long tenantId, NodeType nodeType, JobType jobType);

    List<RecruitmentProcessNodePageSection> findAllByTenantIdAndRecruitmentProcessId(Long tenantId, Long recruitmentProcessId);

    List<RecruitmentProcessNodePageSection> findAllByTenantIdAndJobTypeAndRecruitmentProcessId(Long tenantId, JobType jobType, Long recruitmentProcessId);

    List<RecruitmentProcessNodePageSection> findAllByRecruitmentProcessId(Long recruitmentProcessId);

    RecruitmentProcessNodePageSection findAllByTenantIdAndRecruitmentProcessIdAndNodeType(Long tenantId, Long recruitmentProcessId, NodeType nodeType);

    RecruitmentProcessNodePageSection findAllByTenantIdAndRecruitmentProcessIdAndJobTypeAndNodeType(Long tenantId, Long recruitmentProcessId, JobType jobType, NodeType nodeType);

    RecruitmentProcessNodePageSection findAllByRecruitmentProcessIdAndNodeType(Long recruitmentProcessId, NodeType nodeType);
}
