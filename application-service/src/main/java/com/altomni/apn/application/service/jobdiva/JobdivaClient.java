package com.altomni.apn.application.service.jobdiva;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(value = "jobdiva-service")
public interface JobdivaClient {

    @DeleteMapping("/jobdiva/api/v3/assignment/deleteByStartId")
    ResponseEntity<Integer> deleteByStartId(@RequestParam("startId") Long startId);



}
