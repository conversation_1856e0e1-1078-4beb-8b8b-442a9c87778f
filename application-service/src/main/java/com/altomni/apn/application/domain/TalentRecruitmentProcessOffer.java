package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOfferVO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;

/**
 * A TalentRecruitmentProcessOffer.
 */
@Entity
@Table(name = "talent_recruitment_process_offer")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOffer extends AbstractPermissionNoteAuditingEntity implements Serializable {

    private static final long serialVersionUID = -4167261817382641558L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "signed_date")
    private LocalDate signedDate;

    @Column(name = "estimate_onboard_date")
    private LocalDate estimateOnboardDate;

    @Column(name = "note")
    private String note;

    public static TalentRecruitmentProcessOffer fromVO(TalentRecruitmentProcessOfferVO offerVO) {
        TalentRecruitmentProcessOffer result = new TalentRecruitmentProcessOffer();
        ServiceUtils.myCopyProperties(offerVO, result);
        return result;
    }

    @Override
    public String getNoteValue() {
        return note;
    }
}
