package com.altomni.apn.application.service.xxljob;

import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.xxljob.XxlJobInvoiceOverdueDTO;

public interface XxlJobService {

    void deleteApplicationReminderByTalentId(TalentRecruitmentProcessVO VO);

    void deleteApplicationReminder(TalentRecruitmentProcessVO VO);

    void unOnboardedReminder(TalentRecruitmentProcessVO vo);

    void onboardNoInvoiceReminder(TalentRecruitmentProcessVO vo);

    void eliminateXxlJobForApplication(TalentRecruitmentProcessVO vo);

    void cancelEliminateXxlJobForApplication(TalentRecruitmentProcessVO result);

    void createInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    void deleteInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    void updateInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    void onboardNoInvoiceReminderByInvoiceVoid(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

    void deleteOnboardNoInvoiceReminder(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO);

}
