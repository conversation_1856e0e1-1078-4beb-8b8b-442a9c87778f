package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.*;
import com.altomni.apn.application.repository.RecruitmentProcessRepository;
import com.altomni.apn.application.repository.TalentRecruitmentProcessOnboardRepository;
import com.altomni.apn.application.repository.TalentRecruitmentProcessResignationRepository;
import com.altomni.apn.application.service.company.CompanyService;
import com.altomni.apn.application.service.finance.FinanceService;
import com.altomni.apn.application.service.job.JobService;
import com.altomni.apn.application.service.talent.TalentService;
import com.altomni.apn.application.service.talentrecruitmentprocess.*;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.address.LocationDTO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.*;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.domain.enumeration.start.StartType;
import com.altomni.apn.finance.domain.start.*;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * Service Implementation for managing TalentRecruitmentProcessOnboard.
 */
@Service
@Transactional
public class TalentRecruitmentProcessOnboardServiceImpl implements TalentRecruitmentProcessOnboardService {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessOnboardServiceImpl.class);
    @Resource
    private TalentRecruitmentProcessOnboardRepository onboardRepository;
    @Resource
    private TalentRecruitmentProcessOnboardDateService talentRecruitmentProcessOnboardDateService;

    @Resource
    private TalentRecruitmentProcessOnboardWorkLocationService talentRecruitmentProcessOnboardWorkLocationService;
    @Resource
    private TalentRecruitmentProcessKpiUserService kpiUserService;
    @Resource
    private TalentRecruitmentProcessFeeChargeService feeChargeService;
    @Resource
    private TalentRecruitmentProcessOfferSalaryPackageService offerSalaryPackageService;
    @Resource
    private TalentRecruitmentProcessIpgContractFeeChargeService contractFeeChargeService;
    @Resource
    private TalentRecruitmentProcessOnboardClientInfoService clientInfoService;
    @Resource
    private FinanceService financeService;
    @Resource
    private JobService jobService;

    @Resource
    private CompanyService companyService;

    @Resource
    private RecruitmentProcessRepository recruitmentProcessRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Resource
    private TalentRecruitmentProcessResignationRepository resignationRepository;

    @Resource
    private TalentService talentService;

    @Override
    public TalentRecruitmentProcessOnboardVO updateNoteOnly(TalentRecruitmentProcessOnboardVO onboardVO) {
        return this.updateNoteOnly(onboardVO.getTalentRecruitmentProcessId(), onboardVO.getNote());
    }

    @Override
    public TalentRecruitmentProcessOnboardVO updateNoteOnly(Long talentRecruitmentProcessId, String note) {
        if (talentRecruitmentProcessId != null) {
            TalentRecruitmentProcessOnboard exist = onboardRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
            if (exist == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_ONBOARDNOTFOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(talentRecruitmentProcessId), applicationApiPromptProperties.getAppl()));
            }
//            exist.get().setNote(note);
//            exist.get().setLastModifiedDate(Instant.now());
//            exist.get().setLastModifiedBy(SecurityUtils.getUserUid());
//            onboardRepository.saveAndFlush(exist.get());
            onboardRepository.updateNoteOnly(talentRecruitmentProcessId, note, SecurityUtils.getUserId());
            return toVO(talentRecruitmentProcessId);
        } else {
            return null;
        }
    }

    /**
     * Save a talentRecruitmentProcessOnboard.
     *
     * @param onboardVO the entity to save
     * @return the persisted entity
     */
    @Override
    public TalentRecruitmentProcessOnboardVO save(TalentRecruitmentProcessOnboardVO onboardVO) {
        TalentRecruitmentProcessOnboard exist = onboardRepository.findByTalentRecruitmentProcessId(onboardVO.getTalentRecruitmentProcessId());
        if (exist != null) {
            exist.setNote(onboardVO.getNote());
            exist.setLastModifiedDate(Instant.now());
            exist.setLastModifiedBy(SecurityUtils.getUserUid());
            exist.setChargeNumber(onboardVO.getChargeNumber());
            exist.setTvcNumber(onboardVO.getTvcNumber());
            exist.setCorpToCorp(onboardVO.getCorpToCorp());
            exist.setIsSubstituteTalent(onboardVO.getIsSubstituteTalent());
            exist.setSubstituteTalentId(onboardVO.getSubstituteTalentId());
            exist.setRelationProcessId(onboardVO.getRelationProcessId());
            if (null != onboardVO.getProfitSharing()) {
                exist.setChannelPlatform(onboardVO.getProfitSharing().getChannelPlatform());
                exist.setProfitSharingRatio(onboardVO.getProfitSharing().getProfitSharingRatio());
            }
            onboardRepository.saveAndFlush(exist);
//            onboardRepository.updateLastModifiedDateAndLastModifiedBy(onboardVO.getTalentRecruitmentProcessId(), SecurityUtils.getUserUid());
        } else {
            TalentRecruitmentProcessOnboard onboard = TalentRecruitmentProcessOnboard.fromVO(onboardVO);
            if (null != onboardVO.getProfitSharing()) {
                onboard.setChannelPlatform(onboardVO.getProfitSharing().getChannelPlatform());
                onboard.setProfitSharingRatio(onboardVO.getProfitSharing().getProfitSharingRatio());
            }
            onboardRepository.saveAndFlush(onboard);
        }
        saveTalentRecruitmentProcessOnboardDate(onboardVO);
        saveTalentRecruitmentProcessOnboardWorkLocation(onboardVO);
//        if (Objects.equals(SecurityUtils.getTenantId(), Constants.TENANT_IPG)) {
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            //fixme kpiUsers 保存不需要返回对象，onboard 保存的时候也不需要返回对象并没有使用
            //onboardVO.setKpiUsers(kpiUserService.save(onboardVO.getTalentRecruitmentProcessId(), onboardVO.getKpiUsers()));
            kpiUserService.save(onboardVO.getTalentRecruitmentProcessId(), onboardVO.getKpiUsers());
            contractFeeChargeService.save(onboardVO.getTalentRecruitmentProcessId(), onboardVO.getContractFeeCharge());
            offerSalaryPackageService.save(onboardVO.getTalentRecruitmentProcessId(), onboardVO.getSalaryPackages());
            feeChargeService.save(onboardVO.getTalentRecruitmentProcessId(), onboardVO.getFeeCharge());
            clientInfoService.save(onboardVO.getTalentRecruitmentProcessId(), onboardVO.getClientInfo());
        }
       // return toVO(onboardVO.getTalentRecruitmentProcessId());
        return null;
    }

    private void saveTalentRecruitmentProcessOnboardDate(TalentRecruitmentProcessOnboardVO onboardVO) {
        TalentRecruitmentProcessOnboardDate onboardDate = talentRecruitmentProcessOnboardDateService.findByTalentRecruitmentProcessId(onboardVO.getTalentRecruitmentProcessId());
        if (onboardDate != null) { // update
            ServiceUtils.myCopyProperties(onboardVO, onboardDate, TalentRecruitmentProcessOnboardDate.UpdateSkipProperties);
            talentRecruitmentProcessOnboardDateService.save(onboardVO.getTalentRecruitmentProcessId(), onboardDate);
        } else { // create
            TalentRecruitmentProcessOnboardDate create = new TalentRecruitmentProcessOnboardDate();
            ServiceUtils.myCopyProperties(onboardVO, create, TalentRecruitmentProcessOnboardDate.UpdateSkipProperties);
            talentRecruitmentProcessOnboardDateService.save(onboardVO.getTalentRecruitmentProcessId(), create);
        }
    }

    private void saveTalentRecruitmentProcessOnboardWorkLocation(TalentRecruitmentProcessOnboardVO onboardVO) {
        if (Objects.isNull(onboardVO.getWorkLocation())) return;
        TalentRecruitmentProcessOnboardWorkLocation workLocation = new TalentRecruitmentProcessOnboardWorkLocation();
        workLocation.setTalentRecruitmentProcessId(onboardVO.getTalentRecruitmentProcessId());
        workLocation.setOriginalLoc(JSONUtil.toJsonStr(onboardVO.getWorkLocation()));
        workLocation.setAddressLine(onboardVO.getWorkLocation().getAddressLine());
        workLocation.setResidentialLocation(onboardVO.getWorkLocation().getResidentialLocation());
        TalentRecruitmentProcessOnboardWorkLocation res = talentRecruitmentProcessOnboardWorkLocationService.save(onboardVO.getTalentRecruitmentProcessId(), workLocation);
        if (res != null) {
            LocationDTO locationDTO = JSONUtil.toBean(res.getOriginalLoc(), LocationDTO.class);
            locationDTO.setOriginalLoc(res.getOriginalLoc());
            locationDTO.setOfficialCity(res.getOfficialCity());
            locationDTO.setOfficialCounty(res.getOfficialCounty());
            locationDTO.setOfficialProvince(res.getOfficialProvince());
            locationDTO.setOfficialCountry(res.getOfficialCountry());
            locationDTO.setAddressLine(res.getAddressLine());
            locationDTO.setResidentialLocation(res.getResidentialLocation());
            onboardVO.setWorkLocation(locationDTO);
        }
    }

    private TalentRecruitmentProcessOnboardVO toVO(Long talentRecruitmentProcessId) {
        return toVO(onboardRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }

    private TalentRecruitmentProcessOnboardVO toVO(TalentRecruitmentProcessOnboard onboard) {
//        TalentRecruitmentProcessOnboard onboard = onboardRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (onboard == null) {
            return null;
        }
        Long talentRecruitmentProcessId = onboard.getTalentRecruitmentProcessId();
        TalentRecruitmentProcessOnboardVO result = new TalentRecruitmentProcessOnboardVO();
        ServiceUtils.myCopyProperties(onboard, result);
        TalentRecruitmentProcessOnboardDate onboardDate = talentRecruitmentProcessOnboardDateService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (onboardDate != null) {
            ServiceUtils.myCopyProperties(onboardDate, result, TalentRecruitmentProcessOnboardDate.UpdateSkipProperties);
        }
        TalentRecruitmentProcessOnboardWorkLocation workLocation = talentRecruitmentProcessOnboardWorkLocationService.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (workLocation != null) {
            LocationDTO locationDTO = JSONUtil.toBean(workLocation.getOriginalLoc(), LocationDTO.class);
            locationDTO.setOriginalLoc(workLocation.getOriginalLoc());
            locationDTO.setOfficialCity(workLocation.getOfficialCity());
            locationDTO.setOfficialCounty(workLocation.getOfficialCounty());
            locationDTO.setOfficialProvince(workLocation.getOfficialProvince());
            locationDTO.setOfficialCountry(workLocation.getOfficialCountry());
            locationDTO.setAddressLine(workLocation.getAddressLine());
            result.setWorkLocation(locationDTO);
        }
        result.setResigned(CollectionUtils.isNotEmpty(resignationRepository.findByTalentRecruitmentProcessIdAndNonConvertToFte(talentRecruitmentProcessId)));

        if (onboard.getChannelPlatform() != null && onboard.getProfitSharingRatio() != null) {
            TalentRecruitmentProcessSourceChannelProfitSharingVO sharingVO = new TalentRecruitmentProcessSourceChannelProfitSharingVO();
            sharingVO.setChannelPlatform(onboard.getChannelPlatform());
            sharingVO.setProfitSharingRatio(onboard.getProfitSharingRatio());
            result.setProfitSharing(sharingVO);
        }
        if (null != result.getSubstituteTalentId()) {
            TalentBriefDTO talentBriefDTO = talentService.getTalentBrief(result.getSubstituteTalentId());
            result.setSubstituteTalentName(talentBriefDTO.getFullName());
        }
        return result;
    }

    /**
     * Might be delete when the entire start function move to assignment
     * @param talentRecruitmentProcess talent recruitment process
     * @param onboardVO onboard DTO
     */
    @Override
    public StartDTO saveStart(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessOnboardVO onboardVO) { //TODO add one column to ONBOARDING node, or add one more node after onboarding
//        if (!Constants.TENANT_IPG.equals(SecurityUtils.getTenantId())) {
        log.info("SecurityUtils.getTenantId()={}", SecurityUtils.getTenantId());
        log.info("ApplicationIPGProperties.IPG_RULE_TENANT_IDS={}", ApplicationIPGProperties.IPG_RULE_TENANT_IDS);
        if (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            return null;
        }
//        List<StartDTO> starts = financeService.getActiveStartByTalentId(talentRecruitmentProcess.getTalentId());
//        if (CollectionUtils.isNotEmpty(starts)) {
//            return null;
//        }
        StartDTO startDTO = new StartDTO();
        if (null != onboardVO.getProfitSharing()) {
            startDTO.setChannelPlatform(onboardVO.getProfitSharing().getChannelPlatform());
            startDTO.setProfitSharingRatio(onboardVO.getProfitSharing().getProfitSharingRatio());
        }
        startDTO.setChargeNumber(onboardVO.getChargeNumber());
        startDTO.setTvcNumber(onboardVO.getTvcNumber());
        startDTO.setCorpToCorp(onboardVO.getCorpToCorp());
        startDTO.setTalentRecruitmentProcessId(talentRecruitmentProcess.getId());
        startDTO.setTalentId(talentRecruitmentProcess.getTalentId());
        startDTO.setJobId(talentRecruitmentProcess.getJobId());
        startDTO.setStartDate(onboardVO.getOnboardDate());
        startDTO.setEndDate(onboardVO.getEndDate());
        startDTO.setWarrantyEndDate(onboardVO.getWarrantyEndDate());
        startDTO.setNote(onboardVO.getNote());
        startDTO.setIsSubstituteTalent(onboardVO.getIsSubstituteTalent());
        startDTO.setRelationProcessId(onboardVO.getRelationProcessId());
        startDTO.setSubstituteTalentId(onboardVO.getSubstituteTalentId());
        if (Objects.nonNull(onboardVO.getClientInfo()) && Objects.nonNull(onboardVO.getClientInfo().getClientContactId())) {
            startDTO.setClientContactId(onboardVO.getClientInfo().getClientContactId());
        }

        JobDTOV3 job = jobService.getJob(talentRecruitmentProcess.getJobId());
        RecruitmentProcess recruitmentProcess = recruitmentProcessRepository.findById(job.getRecruitmentProcess().getId()).orElseThrow();
        startDTO.setPositionType(recruitmentProcess.getJobType());
        startDTO.setCompanyId(job.getCompanyId());
        startDTO.setCompany(companyService.getCompany(job.getCompanyId()).getName());
        startDTO.setWorkingMode(onboardVO.getWorkingMode());
        startDTO.setJobTitle(job.getTitle());

        StartAddress startAddress = constructStartAddress(onboardVO.getWorkLocation());
        startDTO.setStartAddress(startAddress);
        if (JobType.FULL_TIME == recruitmentProcess.getJobType()) {
            startDTO.setStartType(StartType.FTE_NEW_HIRE);
            StartFteRate fteRate = new StartFteRate();
            if (CollectionUtils.isNotEmpty(onboardVO.getSalaryPackages())) {
                List<StartFteSalaryPackage> salaryPackages = Lists.newArrayList();
                for (TalentRecruitmentProcessOfferSalaryPackageVO salaryPackage : onboardVO.getSalaryPackages()) {
                    StartFteSalaryPackage startFteSalaryPackage = new StartFteSalaryPackage();
                    startFteSalaryPackage.setSalaryType(salaryPackage.getSalaryType());
                    startFteSalaryPackage.setAmount(salaryPackage.getAmount());
                    startFteSalaryPackage.setNeedCharge(salaryPackage.getNeedCharge());
                    salaryPackages.add(startFteSalaryPackage);
                }
                fteRate.setSalaryPackages(salaryPackages);
            }
            if (onboardVO.getFeeCharge() != null) {
                fteRate.setCurrency(onboardVO.getCurrency());
                fteRate.setRateUnitType(onboardVO.getRateUnitType());
                fteRate.setFeeType(onboardVO.getFeeCharge().getFeeType());
                fteRate.setFeePercentage(onboardVO.getFeeCharge().getFeeAmount());
                fteRate.setTotalBillableAmount(onboardVO.getFeeCharge().getTotalBillableAmount());
                fteRate.setTotalBillAmount(onboardVO.getFeeCharge().getTotalAmount());
            }
            startDTO.setStartFteRate(fteRate);
        } else {
            startDTO.setStartType(StartType.CONTRACT_NEW_HIRE);
            if (onboardVO.getContractFeeCharge() != null) {
                StartContractRate contractRate = new StartContractRate();
                contractRate.setCurrency(onboardVO.getCurrency());
                contractRate.setRateUnitType(onboardVO.getRateUnitType());
                contractRate.setStartDate(onboardVO.getOnboardDate());
                contractRate.setEndDate(onboardVO.getEndDate());
                contractRate.setFinalBillRate(onboardVO.getContractFeeCharge().getFinalBillRate());
                contractRate.setFinalPayRate(onboardVO.getContractFeeCharge().getFinalPayRate());
                contractRate.setTaxBurdenRateCode(onboardVO.getContractFeeCharge().getTaxBurdenRateCode());
                contractRate.setMspRateCode(onboardVO.getContractFeeCharge().getMspRateCode());
                contractRate.setImmigrationCostCode(onboardVO.getContractFeeCharge().getImmigrationCostCode());
                contractRate.setExtraCost(onboardVO.getContractFeeCharge().getExtraCost());
                contractRate.setEstimatedWorkingHourPerWeek(onboardVO.getContractFeeCharge().getEstimatedWorkingHourPerWeek());
                contractRate.setNote(onboardVO.getNote());
                contractRate.setTotalBillAmount(financeService.getTotalBillAmount(contractRate));
                startDTO.setStartContractRates(List.of(contractRate));
            }
        }
        if (onboardVO.getClientInfo() != null) {
            StartClientInfo startClientInfo = new StartClientInfo();
            ServiceUtils.myCopyProperties(onboardVO.getClientInfo(), startClientInfo);
            startDTO.setStartClientInfo(startClientInfo);
        }
        // kpiUserService.findAllByTalentRecruitmentProcessId(onboardVO.getTalentRecruitmentProcessId());
        List<TalentRecruitmentProcessKpiUserVO> kpiUsers = onboardVO.getKpiUsers();
        if (CollectionUtils.isEmpty(kpiUsers)) {
            kpiUsers = kpiUserService.findAllByTalentRecruitmentProcessId(onboardVO.getTalentRecruitmentProcessId());
        }
        if (CollectionUtils.isNotEmpty(kpiUsers)) {
            List<StartCommission> startCommissions = new ArrayList<>();
            for (TalentRecruitmentProcessKpiUserVO kpiUser : kpiUsers) {
                StartCommission commission = new StartCommission();
                commission.setUserId(kpiUser.getUserId());
                commission.setUserRole(kpiUser.getUserRole());
                commission.setPercentage(kpiUser.getPercentage());
                commission.setTenantId(SecurityUtils.getTenantId());
                commission.setCountry(kpiUser.getCountry());
                startCommissions.add(commission);
            }
            startDTO.setStartCommissions(startCommissions);
        }
        log.info("invoke create start API in finance service: {}", startDTO);
        //financeService.createStart(startDTO);
        return startDTO;
    }

    private StartAddress constructStartAddress(LocationDTO workLocation) {
        StartAddress startAddress = new StartAddress();

        startAddress.setOriginalLoc(workLocation.getOriginalLoc());
        startAddress.setCity(StringUtils.firstNonBlank(workLocation.getOfficialCity(), workLocation.getCity()));
        startAddress.setProvince(StringUtils.firstNonBlank(workLocation.getOfficialProvince(), workLocation.getProvince()));
        startAddress.setCountry(StringUtils.firstNonBlank(workLocation.getOfficialCountry(), workLocation.getCountry()));
        startAddress.setAddress(workLocation.getAddressLine());
        startAddress.setLocation(workLocation.getLocation());
        startAddress.setZipcode(workLocation.getZipcode());
        startAddress.setAddressLine(workLocation.getAddressLine());
        startAddress.setResidentialLocation(workLocation.getResidentialLocation());

        startAddress.setCounty(StringUtils.firstNonBlank(workLocation.getOfficialCounty(), workLocation.getCounty()));
        startAddress.setCoordinate(workLocation.getCoordinate());
        startAddress.setOriginDisplay(workLocation.getOriginDisplay());
        return  startAddress;
    }

    @Override
    @Transactional(readOnly = true)
    public TalentRecruitmentProcessOnboardVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVO(talentRecruitmentProcessId);
    }

    @Override
    @Transactional(readOnly = true)
    public void updateSubstituteTalentIdByTalentRecruitmentProcessId(TalentRecruitmentProcessUpdateSubstituteTalentDTO substituteTalentDTO) {
        log.info("updateSubstituteTalentIdByTalentRecruitmentProcessId,userId:{},talentRecruitmentProcessId:{}", SecurityUtils.getUserId(), substituteTalentDTO.getTalentRecruitmentProcessId());
        //onboardRepository(substituteTalentDTO.getTalentRecruitmentProcessId(), substituteTalentDTO.getUpdatedBy(), substituteTalentDTO.getSubstituteTalentId(), substituteTalentDTO.getRelationProcessId(),substituteTalentDTO.isSubstituteCandidate());
        TalentRecruitmentProcessOnboard onboard = onboardRepository.findByTalentRecruitmentProcessId(substituteTalentDTO.getTalentRecruitmentProcessId());
        onboard.setRelationProcessId(substituteTalentDTO.getRelationProcessId());
        onboard.setSubstituteTalentId(substituteTalentDTO.getSubstituteTalentId());
        onboard.setIsSubstituteTalent(substituteTalentDTO.isSubstituteCandidate());
        onboardRepository.save(onboard);
    }
}
