package com.altomni.apn.application.service.talentrecruitmentprocess;


import com.altomni.apn.application.domain.TalentRecruitmentProcessOfferSalaryPackage;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOfferSalaryPackageVO;

import java.util.List;

/**
 * Service Interface for managing {@link TalentRecruitmentProcessOfferSalaryPackage}.
 */
public interface TalentRecruitmentProcessOfferSalaryPackageService {

    List<TalentRecruitmentProcessOfferSalaryPackageVO> save(Long talentRecruitmentProcessId, List<TalentRecruitmentProcessOfferSalaryPackageVO> salaryPackageVOS);

    List<TalentRecruitmentProcessOfferSalaryPackageVO> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

}
