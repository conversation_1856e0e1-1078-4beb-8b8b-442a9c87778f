package com.altomni.apn.application.domain.vm;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class UserDeliveryRecruitmentProcessVM implements Serializable {

    @Id
    private Long processId;
    private Long count;
}
