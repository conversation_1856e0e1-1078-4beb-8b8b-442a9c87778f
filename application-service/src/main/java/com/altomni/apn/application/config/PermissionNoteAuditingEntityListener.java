package com.altomni.apn.application.config;

import com.altomni.apn.application.domain.AbstractPermissionNoteAuditingEntity;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.Session;
import org.hibernate.engine.spi.EntityEntry;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.persister.entity.EntityPersister;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.PrePersist;
import javax.persistence.PreUpdate;
import java.time.Instant;
import java.util.Arrays;

@Configuration
public class PermissionNoteAuditingEntityListener {

//    @PersistenceContext
//    private EntityManager entityManager;

    @PrePersist
    public void touchForCreate(Object target) {
        Assert.notNull(target, "Entity must not be null!");
        if (target instanceof AbstractPermissionNoteAuditingEntity){
            AbstractPermissionNoteAuditingEntity entity = (AbstractPermissionNoteAuditingEntity) target;
            entity.setNoteLastModifiedByUserId(SecurityUtils.getUserId());
            entity.setNoteLastModifiedDate(Instant.now());
        }
    }

    @PreUpdate
    public void touchForUpdate(Object target){
        if (target instanceof AbstractPermissionNoteAuditingEntity) {
            AbstractPermissionNoteAuditingEntity entity = (AbstractPermissionNoteAuditingEntity) target;

            if (!StringUtils.equals(entity.getNoteValue(), entity.getPreviousNote())) {
                entity.setNoteLastModifiedByUserId(SecurityUtils.getUserId());
                entity.setNoteLastModifiedDate(Instant.now());
            }

//            if (hasNoteChanged(entity)) {
//                entity.setNoteLastModifiedByUserId(SecurityUtils.getUserId());
//                entity.setNoteLastModifiedDate(Instant.now());
//            }
        }
    }

//    private boolean hasNoteChanged(AbstractPermissionNoteAuditingEntity entity) {
//        Session session = entityManager.unwrap(Session.class);
//        SessionImplementor sessionImpl = (SessionImplementor) session;
//
//        EntityEntry entry = sessionImpl.getPersistenceContext().getEntry(entity);
//        if (entry == null) return false;
//
//        EntityPersister persister = entry.getPersister();
//        int propertyIndex = Arrays.asList(persister.getPropertyNames()).indexOf("note");
//        Object[] loadedState = entry.getLoadedState();
//        Object[] currentState = persister.getPropertyValues(entity);
//
//        return persister.getPropertyTypes()[propertyIndex].isDirty(loadedState[propertyIndex], currentState[propertyIndex], sessionImpl);
//    }
}
