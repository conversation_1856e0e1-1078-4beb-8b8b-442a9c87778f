package com.altomni.apn.application.utils;

import com.altomni.apn.application.web.rest.vm.HistoryStagesVo;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.application.FeeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.*;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.SpringUtil;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.StreamUtils;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class PdfUtil {

    private final Logger log = LoggerFactory.getLogger(PdfUtil.class);

    private static final Font FONT_BOLD_15 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 11, BaseColor.BLACK);
    private static final Font FONT_BOLD_20 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 20, BaseColor.BLACK);

    private final Font FONT_ARIAL = FontFactory.getFont("arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);

    private final Font FONT_CHINESE_12 = FontFactory.getFont("NotoSansCJKsc-Regular.otf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 12);

    private final Font FONT_CHINESE_15 = FontFactory.getFont("NotoSansCJKsc-Regular.otf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 15);

    private final Font FONT_CHINESE_20 = FontFactory.getFont("NotoSansCJKsc-Regular.otf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 20);

    private static final String CURRENCY_US = "USA-USD(US$)";
    private static final String CURRENCY_US_DOLLAR = "$";

    private static final Pattern ChineseCharPattern = Pattern.compile("[\u4e00-\u9fa5]");

    private static Map<String,String> NUMBER_MAP = new HashMap<>();

    static {
        NUMBER_MAP.put("1", "First");
        NUMBER_MAP.put("2", "Second");
        NUMBER_MAP.put("3", "Third");
        NUMBER_MAP.put("4", "Fourth");
        NUMBER_MAP.put("5", "Fifth");
        NUMBER_MAP.put("6", "Sixth");
        NUMBER_MAP.put("7", "Seventh");
        NUMBER_MAP.put("8", "Eighth");
        NUMBER_MAP.put("9", "Ninth");
        NUMBER_MAP.put("10", "Tenth");
    }

    private Image readImage(String imagePath, String imagePathSlash) {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        Image image = null;
        try {
            File folderInput = new File(imagePath);
            log.debug("[PdfUtil.readImage][{}] read by File: {}", imagePath, folderInput.exists());
            if (folderInput.exists()) {
                BufferedImage folderImage = ImageIO.read(folderInput);
                ImageIO.write(folderImage, "png", out);
                byte[] byteArray = out.toByteArray();
                image = Image.getInstance(byteArray);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by File error.", imagePath);
        }

        try {
            ClassPathResource imgFile = new ClassPathResource(imagePath);
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource: ", imagePath, imgFile.exists());
            if (imgFile.exists()) {
                byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
                image = Image.getInstance(bytes);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource error.", imagePath);
        }

        try {
            URL url = PdfUtil.class.getClassLoader().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() error.", imagePath);
        }

        try {
            URL url = this.getClass().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() error.", imagePath);
        }

        try {
            URL url = Thread.currentThread().getContextClassLoader().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() error.", imagePath);
        }

        try {
            URL url = ClassLoader.getSystemClassLoader().getResource(imagePath);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource(): {}", imagePath, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() not found", imagePath);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() error.", imagePath);
        }

        // ********************************imagePathSlash************************************/

        try {
            File folderInput = new File(imagePathSlash);
            log.debug("[PdfUtil.readImage][{}] read by File: {}", imagePathSlash, folderInput.exists());
            if (folderInput.exists()) {
                BufferedImage folderImage = ImageIO.read(folderInput);
                ImageIO.write(folderImage, "png", out);
                byte[] byteArray = out.toByteArray();
                image = Image.getInstance(byteArray);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by File error.", imagePathSlash);
        }

        try {
            ClassPathResource imgFile = new ClassPathResource(imagePathSlash);
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource: ", imagePathSlash, imgFile.exists());
            if (imgFile.exists()) {
                byte[] bytes = StreamUtils.copyToByteArray(imgFile.getInputStream());
                image = Image.getInstance(bytes);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassPathResource error.", imagePathSlash);
        }

        try {
            URL url = PdfUtil.class.getClassLoader().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by class.getClassLoader().getResource() error.", imagePathSlash);
        }

        try {
            URL url = this.getClass().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by this.getClass().getResource() error.", imagePathSlash);
        }

        try {
            URL url = Thread.currentThread().getContextClassLoader().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by Thread.currentThread().getContextClassLoader().getResource() error.", imagePathSlash);
        }

        try {
            URL url = ClassLoader.getSystemClassLoader().getResource(imagePathSlash);
            if (url != null) {
                File classPathInput = new File(url.getFile());
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource(): {}", imagePathSlash, classPathInput.exists());
                if (classPathInput.exists()) {
                    BufferedImage classpathImage = ImageIO.read(classPathInput);
                    ImageIO.write(classpathImage, "png", out);
                    byte[] byteArray = out.toByteArray();
                    image = Image.getInstance(byteArray);
                }
            } else {
                log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() not found", imagePathSlash);
            }
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage][{}] read by ClassLoader.getSystemClassLoader().getResource() error.", imagePathSlash);
        }


        try {
            out.close();
        } catch (Exception e) {
            log.debug("[PdfUtil.readImage] close ByteArrayOutputStream error.");
        }

        return image;
    }

    public void createDetailsOfChargePdfInEN(Document document, TalentRecruitmentProcessVO talentRecruitmentProcessVO, HistoryStagesVo stages) throws DocumentException {
        document.newPage();
        PdfPTable table = new PdfPTable(10);
        table.setWidthPercentage(85);
        table.setSpacingBefore(5f);
        table.setSpacingAfter(5f);

        // title
        PdfPCell cell = new PdfPCell(new Phrase("Details of Charges", FONT_BOLD_20));
        cell.setColspan(5);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after title
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Basic Information
        // title basic information
        cell = new PdfPCell(new Phrase("Basic Information:", FONT_BOLD_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after basic information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // company name
        cell = new PdfPCell(new Phrase("Company Name:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // company name value
        if (containsChineseCharacter(talentRecruitmentProcessVO.getJob().getCompany().getName())) {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getCompany().getName(), FONT_CHINESE_12));
        } else {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getCompany().getName()));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // job tile
        cell = new PdfPCell(new Phrase("Job Title:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // job tile value
        if (containsChineseCharacter(talentRecruitmentProcessVO.getJob().getTitle())) {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getTitle(), FONT_CHINESE_12));
        } else {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getTitle()));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // candidate name
        cell = new PdfPCell(new Phrase("Candidate:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // candidate name value
        if (containsChineseCharacter(talentRecruitmentProcessVO.getTalent().getFullName())) {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getTalent().getFullName(), FONT_CHINESE_12));
        } else {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getTalent().getFullName()));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after candidate name
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Offer Information Module
        // title offer information
        cell = new PdfPCell(new Phrase("Offer Information:", FONT_BOLD_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        TalentRecruitmentProcessIpgOfferAcceptVO offerAcceptVO = stages.getOfferAccept();
        // date of onboarded
        cell = new PdfPCell(new Phrase("Date of On boarded:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // date of onboarded value
        cell = new PdfPCell(new Phrase(offerAcceptVO.getOnboardDate().toString()));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // warranty end date
        cell = new PdfPCell(new Phrase("Warranty End Date:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // warranty end date value
        String warrantyEndDateStr = "";
        if (Objects.nonNull(offerAcceptVO.getWarrantyEndDate())) {
            warrantyEndDateStr = offerAcceptVO.getWarrantyEndDate().toString();
        }
        cell = new PdfPCell(new Phrase(warrantyEndDateStr));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // rate currency/unit type
        cell = new PdfPCell(new Phrase("Rate Currency/Unit Type:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        String currency = this.getCurrencySign(offerAcceptVO.getCurrency());
        String currencyStr = this.getCurrencyStr(offerAcceptVO.getCurrency());
        String unit = Objects.isNull(offerAcceptVO.getRateUnitType()) ? "year" : offerAcceptVO.getRateUnitType().toString();
        // rate currency/unit type value
        cell = new PdfPCell(new Phrase(currencyStr + " / " + unit, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after rate currency/unit type
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        // Salary Package Module
        // title salary package
        cell = new PdfPCell(new Phrase("Salary Package:", FONT_BOLD_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        List<TalentRecruitmentProcessOfferSalaryPackageVO> salaryPackage = offerAcceptVO.getSalaryPackages();
        BigDecimal baseSalary = BigDecimal.ZERO;
        BigDecimal signOnBonus = BigDecimal.ZERO;
        BigDecimal relocationPackage = BigDecimal.ZERO;
        BigDecimal annualBonus = BigDecimal.ZERO;
        BigDecimal extraFee = BigDecimal.ZERO;
        for (TalentRecruitmentProcessOfferSalaryPackageVO salary: salaryPackage) {
            switch (salary.getSalaryType()) {
                case BASE_SALARY:
                    baseSalary = salary.getAmount();
                    break;
                case SIGN_ON_BONUS:
                    signOnBonus = salary.getAmount();
                    break;
                case RELOCATION_PACKAGE:
                    relocationPackage = salary.getAmount();
                    break;
                case ANNUAL_BONUS:
                    annualBonus = salary.getAmount();
                    break;
                case EXTRA_FEE:
                    extraFee = salary.getAmount();
                    break;
            }
        }
        BigDecimal totalSalaryPackageAmount = baseSalary.add(signOnBonus).add(relocationPackage).add(annualBonus).add(extraFee);

        // base salary
        cell = new PdfPCell(new Phrase("Base Salary:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // base salary value
        cell = new PdfPCell(new Phrase(currency + baseSalary, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // sign-on bonus
        cell = new PdfPCell(new Phrase("Sign-on Bonus:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // sign-on bonus value
        cell = new PdfPCell(new Phrase(currency + signOnBonus, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // relocation package
        cell = new PdfPCell(new Phrase("Relocation Package:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // relocation package value
        cell = new PdfPCell(new Phrase(currency + relocationPackage, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // annual bonus
        cell = new PdfPCell(new Phrase("Annual Bonus:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // annual bonus value
        cell = new PdfPCell(new Phrase(currency + annualBonus, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total salary package amount
        cell = new PdfPCell(new Phrase("Total Salary Package Amount:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total salary package amount value
        cell = new PdfPCell(new Phrase(currency +  totalSalaryPackageAmount, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // extra fee
        cell = new PdfPCell(new Phrase("Extra Fee:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // extra fee value
        cell = new PdfPCell(new Phrase(currency + extraFee, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        TalentRecruitmentProcessOfferFeeChargeVO feeCharge = offerAcceptVO.getFeeCharge();
        // total billable amount
        cell = new PdfPCell(new Phrase("Total Billable Amount:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total billable amount value
        cell = new PdfPCell(new Phrase(currency + feeCharge.getTotalBillableAmount(), FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after total billable amount value
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        // Bill Details Module
        // title bill details
        cell = new PdfPCell(new Phrase("Bill Details:", FONT_BOLD_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        String feeType = FeeType.PERCENTAGE.equals(feeCharge.getFeeType()) ? "Fee Type:" : "Flat Amount:";
        String value = FeeType.PERCENTAGE.equals(feeCharge.getFeeType()) ? feeCharge.getFeeAmount().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%" : currency + feeCharge.getFeeAmount().setScale(2, RoundingMode.HALF_UP).toString();
        // fee type
        cell = new PdfPCell(new Phrase(feeType));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // fee type value
        cell = new PdfPCell(new Phrase(value, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total bill amount
        cell = new PdfPCell(new Phrase("Total Bill Amount:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total bill amount value
        cell = new PdfPCell(new Phrase(currency + feeCharge.getTotalAmount().toString(), FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        document.add(table);
    }

    public void createDetailsOfChargePdfInCN(Document document, TalentRecruitmentProcessVO talentRecruitmentProcessVO, HistoryStagesVo stages) throws DocumentException {
        document.newPage();
        PdfPTable table = new PdfPTable(10);
        table.setWidthPercentage(85);
        table.setSpacingBefore(5f);
        table.setSpacingAfter(5f);

        // title
        PdfPCell cell = new PdfPCell(new Phrase("收费明细", FONT_CHINESE_20));
        cell.setColspan(5);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after title
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Basic Information
        // title basic information
        cell = new PdfPCell(new Phrase("基本信息:", FONT_CHINESE_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after basic information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // company name
        cell = new PdfPCell(new Phrase("公司名称:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // company name value
        cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getCompany().getName(), FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // job tile
        cell = new PdfPCell(new Phrase("职位名称:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // job tile value
        cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getTitle(), FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // candidate name
        cell = new PdfPCell(new Phrase("候选人:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // candidate name value
        cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getTalent().getFullName(), FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after candidate name
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Offer Information Module
        // title offer information
        cell = new PdfPCell(new Phrase("Offer 信息:", FONT_CHINESE_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        TalentRecruitmentProcessIpgOfferAcceptVO offerAcceptVO = stages.getOfferAccept();
        // date of onboarded
        cell = new PdfPCell(new Phrase("入职日期:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // date of onboarded value
        cell = new PdfPCell(new Phrase(offerAcceptVO.getOnboardDate().toString(), FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // warranty end date
        cell = new PdfPCell(new Phrase("试用期结束:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // warranty end date value
        String warrantyEndDateStr = "";
        if (Objects.nonNull(offerAcceptVO.getWarrantyEndDate())) {
            warrantyEndDateStr = offerAcceptVO.getWarrantyEndDate().toString();
        }
        cell = new PdfPCell(new Phrase(warrantyEndDateStr, FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // rate currency/unit type
        cell = new PdfPCell(new Phrase("币种/类型:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        String currency = this.getCurrencySign(offerAcceptVO.getCurrency());
        String currencyStr = this.getCurrencyStr(offerAcceptVO.getCurrency());
//        String unit = Objects.isNull(offerAcceptVO.getRateUnitType()) ? "年" : offerAcceptVO.getRateUnitType().toString();
        String unit = "年";
        if (!Objects.isNull(offerAcceptVO.getRateUnitType())) {
            switch (offerAcceptVO.getRateUnitType()) {
                case DAILY:
                    unit = "日";
                    break;
                case HOURLY:
                    unit = "小时";
                    break;
                case WEEKLY:
                    unit = "周";
                    break;
                case YEARLY:
                    unit = "年";
                    break;
                case MONTHLY:
                    unit = "月";
                    break;
                default:
                    unit = "年";
                    break;
            }
        }
        // rate currency/unit type value
        cell = new PdfPCell(new Phrase(currencyStr, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);
        cell = new PdfPCell(new Phrase(" / " + unit, FONT_CHINESE_12));
        cell.setColspan(1);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after rate currency/unit type
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        // Salary Package Module
        // title salary package
        cell = new PdfPCell(new Phrase("薪资结构:", FONT_CHINESE_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        List<TalentRecruitmentProcessOfferSalaryPackageVO> salaryPackage = offerAcceptVO.getSalaryPackages();
        BigDecimal baseSalary = BigDecimal.ZERO;
        BigDecimal signOnBonus = BigDecimal.ZERO;
        BigDecimal relocationPackage = BigDecimal.ZERO;
        BigDecimal annualBonus = BigDecimal.ZERO;
        BigDecimal extraFee = BigDecimal.ZERO;
        for (TalentRecruitmentProcessOfferSalaryPackageVO salary: salaryPackage) {
            switch (salary.getSalaryType()) {
                case BASE_SALARY:
                    baseSalary = salary.getAmount();
                    break;
                case SIGN_ON_BONUS:
                    signOnBonus = salary.getAmount();
                    break;
                case RELOCATION_PACKAGE:
                    relocationPackage = salary.getAmount();
                    break;
                case ANNUAL_BONUS:
                    annualBonus = salary.getAmount();
                    break;
                case EXTRA_FEE:
                    extraFee = salary.getAmount();
                    break;
            }
        }
        BigDecimal totalSalaryPackageAmount = baseSalary.add(signOnBonus).add(relocationPackage).add(annualBonus).add(extraFee);

        // base salary
        cell = new PdfPCell(new Phrase("基本薪资:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // base salary value
        cell = new PdfPCell(new Phrase(currency + baseSalary, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // sign-on bonus
        cell = new PdfPCell(new Phrase("入职奖金:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // sign-on bonus value
        cell = new PdfPCell(new Phrase(currency + signOnBonus, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // relocation package
        cell = new PdfPCell(new Phrase("搬迁费用:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // relocation package value
        cell = new PdfPCell(new Phrase(currency + relocationPackage, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // annual bonus
        cell = new PdfPCell(new Phrase("年度奖金:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // annual bonus value
        cell = new PdfPCell(new Phrase(currency + annualBonus, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total salary package amount
        cell = new PdfPCell(new Phrase("薪资总计:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total salary package amount value
        cell = new PdfPCell(new Phrase(currency +  totalSalaryPackageAmount, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // extra fee
        cell = new PdfPCell(new Phrase("额外费用:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // extra fee value
        cell = new PdfPCell(new Phrase(currency + extraFee, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        TalentRecruitmentProcessOfferFeeChargeVO feeCharge = offerAcceptVO.getFeeCharge();
        // total billable amount
        cell = new PdfPCell(new Phrase("收费部分总计:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total billable amount value
        cell = new PdfPCell(new Phrase(currency + feeCharge.getTotalBillableAmount(), FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after total billable amount value
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        // Bill Details Module
        // title bill details
        cell = new PdfPCell(new Phrase("收费信息:", FONT_CHINESE_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        String feeType = FeeType.PERCENTAGE.equals(feeCharge.getFeeType()) ? "收费比率:" : "固定金额:";
        String value = FeeType.PERCENTAGE.equals(feeCharge.getFeeType()) ? feeCharge.getFeeAmount().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%" : currency + feeCharge.getFeeAmount().setScale(2, RoundingMode.HALF_UP).toString();
        // fee type
        cell = new PdfPCell(new Phrase(feeType, FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // fee type value
        cell = new PdfPCell(new Phrase(value, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total bill amount
        cell = new PdfPCell(new Phrase("最终收费账单金额:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total bill amount value
        cell = new PdfPCell(new Phrase(currency + feeCharge.getTotalAmount().toString(), FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        document.add(table);
    }

    public void createGeneralDetailsOfChargePdfInEN(Document document, TalentRecruitmentProcessVO talentRecruitmentProcessVO, HistoryStagesVo stages) throws DocumentException {
        document.newPage();
        PdfPTable table = new PdfPTable(10);
        table.setWidthPercentage(85);
        table.setSpacingBefore(5f);
        table.setSpacingAfter(5f);

        // title
        PdfPCell cell = new PdfPCell(new Phrase("Details of Charges", FONT_BOLD_20));
        cell.setColspan(5);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after title
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Basic Information
        // title basic information
        cell = new PdfPCell(new Phrase("Basic Information:", FONT_BOLD_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after basic information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // company name
        cell = new PdfPCell(new Phrase("Company Name:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // company name value
        if (containsChineseCharacter(talentRecruitmentProcessVO.getJob().getCompany().getName())) {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getCompany().getName(), FONT_CHINESE_12));
        } else {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getCompany().getName()));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // job tile
        cell = new PdfPCell(new Phrase("Job Title:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // job tile value
        if (containsChineseCharacter(talentRecruitmentProcessVO.getJob().getTitle())) {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getTitle(), FONT_CHINESE_12));
        } else {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getTitle()));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // candidate name
        cell = new PdfPCell(new Phrase("Candidate:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // candidate name value
        if (containsChineseCharacter(talentRecruitmentProcessVO.getTalent().getFullName())) {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getTalent().getFullName(), FONT_CHINESE_12));
        } else {
            cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getTalent().getFullName()));
        }
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after candidate name
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Offer Information Module
        // title offer information
        cell = new PdfPCell(new Phrase("Offer Information:", FONT_BOLD_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        TalentRecruitmentProcessOfferVO offerVO = stages.getOffer();
//        // date of onboarded
//        cell = new PdfPCell(new Phrase("Assign Date:"));
//        cell.setColspan(3);
//        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
//        cell.setBorder(PdfPCell.NO_BORDER);
//        table.addCell(cell);
//
//        // date of onboarded value
//        String signedDateStr = "";
//        if (Objects.nonNull(offerVO.getSignedDate())) {
//            signedDateStr = offerVO.getSignedDate().toString();
//        }
//        cell = new PdfPCell(new Phrase(signedDateStr));
//        cell.setColspan(2);
//        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
//        cell.setBorder(PdfPCell.NO_BORDER);
//        table.addCell(cell);

        // warranty end date
        cell = new PdfPCell(new Phrase("Estimated Onboard Date:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // warranty end date value
        String onboardDateStr = "";
        if (Objects.nonNull(offerVO.getEstimateOnboardDate())) {
            onboardDateStr = offerVO.getEstimateOnboardDate().toString();
        }
        cell = new PdfPCell(new Phrase(onboardDateStr));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // rate currency/unit type
        cell = new PdfPCell(new Phrase("Rate Currency/Unit Type:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        String currency = this.getCurrencySign(offerVO.getCurrency());
        String currencyStr = this.getCurrencyStr(offerVO.getCurrency());
        String unit = Objects.isNull(offerVO.getRateUnitType()) ? "year" : offerVO.getRateUnitType().toString();
        // rate currency/unit type value
        cell = new PdfPCell(new Phrase(currencyStr + " / " + unit, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

//        // space after rate currency/unit type
//        cell = new PdfPCell(new Phrase(" "));
//        cell.setColspan(5);
//        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
//        cell.setBorder(PdfPCell.NO_BORDER);
//        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Bill Details Module
        // title bill details
        cell = new PdfPCell(new Phrase("Bill Details:", FONT_BOLD_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        TalentRecruitmentProcessOfferFeeChargeVO feeCharge = offerVO.getFeeCharge();

        // total billable amount
        cell = new PdfPCell(new Phrase("Total Billable Amount:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total billable amount value
        cell = new PdfPCell(new Phrase(currency + feeCharge.getTotalBillableAmount().toString(), FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        String feeType = FeeType.PERCENTAGE.equals(feeCharge.getFeeType()) ? "Fee Type:" : "Flat Amount:";
        String value = FeeType.PERCENTAGE.equals(feeCharge.getFeeType()) ? feeCharge.getFeeAmount().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%" : currency + feeCharge.getFeeAmount().setScale(2, RoundingMode.HALF_UP).toString();
        // fee type
        cell = new PdfPCell(new Phrase(feeType));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // fee type value
        cell = new PdfPCell(new Phrase(value, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total bill amount
        cell = new PdfPCell(new Phrase("Total Bill Amount:"));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total bill amount value
        cell = new PdfPCell(new Phrase(currency + feeCharge.getTotalAmount().toString(), FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after total bill amount
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        document.add(table);
    }

    public void createGeneralDetailsOfChargePdfInCN(Document document, TalentRecruitmentProcessVO talentRecruitmentProcessVO, HistoryStagesVo stages) throws DocumentException {
        document.newPage();
        PdfPTable table = new PdfPTable(10);
        table.setWidthPercentage(85);
        table.setSpacingBefore(5f);
        table.setSpacingAfter(5f);

        // title
        PdfPCell cell = new PdfPCell(new Phrase("收费明细", FONT_CHINESE_20));
        cell.setColspan(5);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after title
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(4);
        cell.setHorizontalAlignment(Element.ALIGN_CENTER);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Basic Information
        // title basic information
        cell = new PdfPCell(new Phrase("基本信息:", FONT_CHINESE_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after basic information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // company name
        cell = new PdfPCell(new Phrase("公司名称:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // company name value
        cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getCompany().getName(), FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // job tile
        cell = new PdfPCell(new Phrase("职位名称:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // job tile value
        cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getJob().getTitle(), FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // candidate name
        cell = new PdfPCell(new Phrase("候选人:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // candidate name value
        cell = new PdfPCell(new Phrase(talentRecruitmentProcessVO.getTalent().getFullName(), FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after candidate name
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Offer Information Module
        // title offer information
        cell = new PdfPCell(new Phrase("Offer信息:", FONT_CHINESE_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        TalentRecruitmentProcessOfferVO offerVO = stages.getOffer();

//        // date of onboarded
//        cell = new PdfPCell(new Phrase("签订日期:", FONT_CHINESE_12));
//        cell.setColspan(3);
//        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
//        cell.setBorder(PdfPCell.NO_BORDER);
//        table.addCell(cell);
//
//        // date of onboarded value
//        String signDateStr = "";
//        if (Objects.nonNull(offerVO.getSignedDate())) {
//            signDateStr = offerVO.getSignedDate().toString();
//        }
//        cell = new PdfPCell(new Phrase(signDateStr, FONT_CHINESE_12));
//        cell.setColspan(2);
//        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
//        cell.setBorder(PdfPCell.NO_BORDER);
//        table.addCell(cell);

        // warranty end date
        cell = new PdfPCell(new Phrase("预计入职日期:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // warranty end date value
        String estimateOnboardDateStr = "";
        if (Objects.nonNull(offerVO.getEstimateOnboardDate())) {
            estimateOnboardDateStr = offerVO.getEstimateOnboardDate().toString();
        }
        cell = new PdfPCell(new Phrase(estimateOnboardDateStr, FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // rate currency/unit type
        cell = new PdfPCell(new Phrase("币种/类型:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        String currency = this.getCurrencySign(offerVO.getCurrency());
        String currencyStr = this.getCurrencyStr(offerVO.getCurrency());
//        String unit = Objects.isNull(offerVO.getRateUnitType()) ? "年" : offerVO.getRateUnitType().toString();
        String unit = "年";
        if (!Objects.isNull(offerVO.getRateUnitType())) {
            switch (offerVO.getRateUnitType()) {
                case DAILY:
                    unit = "日";
                    break;
                case HOURLY:
                    unit = "小时";
                    break;
                case WEEKLY:
                    unit = "周";
                    break;
                case YEARLY:
                    unit = "年";
                    break;
                case MONTHLY:
                    unit = "月";
                    break;
                default:
                    unit = "年";
                    break;
            }
        }
        // rate currency/unit type value
        cell = new PdfPCell(new Phrase(currencyStr + "/" + unit, FONT_CHINESE_12));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

//        cell = new PdfPCell(new Phrase(" / " + unit, FONT_CHINESE_12));
//        cell.setColspan(1);
//        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
//        cell.setBorder(PdfPCell.NO_BORDER);
//        table.addCell(cell);

//        // space after rate currency/unit type
//        cell = new PdfPCell(new Phrase(" "));
//        cell.setColspan(4);
//        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
//        cell.setBorder(PdfPCell.NO_BORDER);
//        table.addCell(cell);

        // space line
        cell = new PdfPCell(new Phrase("  "));
        cell.setColspan(10);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // Bill Details Module
        // title bill details
        cell = new PdfPCell(new Phrase("收费信息:", FONT_CHINESE_15));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after offer information
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setRowspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        TalentRecruitmentProcessOfferFeeChargeVO feeCharge = offerVO.getFeeCharge();

        // total billable amount
        cell = new PdfPCell(new Phrase("收费部分总计:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total billable amount value
        cell = new PdfPCell(new Phrase(currency + feeCharge.getTotalBillableAmount().toString(), FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);


        String feeType = FeeType.PERCENTAGE.equals(feeCharge.getFeeType()) ? "收费比率:" : "固定金额:";
        String value = FeeType.PERCENTAGE.equals(feeCharge.getFeeType()) ? feeCharge.getFeeAmount().multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString() + "%" : currency + feeCharge.getFeeAmount().setScale(2, RoundingMode.HALF_UP).toString();
        // fee type
        cell = new PdfPCell(new Phrase(feeType, FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // fee type value
        cell = new PdfPCell(new Phrase(value, FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total bill amount
        cell = new PdfPCell(new Phrase("最终收费账单金额:", FONT_CHINESE_12));
        cell.setColspan(3);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // total bill amount value
        cell = new PdfPCell(new Phrase(currency + feeCharge.getTotalAmount().toString(), FONT_ARIAL));
        cell.setColspan(2);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        // space after total bill amount
        cell = new PdfPCell(new Phrase(" "));
        cell.setColspan(5);
        cell.setHorizontalAlignment(Element.ALIGN_LEFT);
        cell.setBorder(PdfPCell.NO_BORDER);
        table.addCell(cell);

        document.add(table);
    }

    private String getCurrencySign(Integer currency) {
        List<EnumCurrency> allCurrencyList = getAllCurrency();
        Map<Integer, EnumCurrency> map = allCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, a -> a));
        if (map.containsKey(currency)) {
            return map.get(currency).getLabel1();
        }
        return CURRENCY_US_DOLLAR;
    }

    private String getCurrencyStr(Integer currency) {
        List<EnumCurrency> allCurrencyList = getAllCurrency();
        Map<Integer, EnumCurrency> map = allCurrencyList.stream().collect(Collectors.toMap(EnumCurrency::getId, a -> a));
        if (map.containsKey(currency)) {
            return map.get(currency).getLabel3();
        }
        return CURRENCY_US;
    }

    private boolean containsChineseCharacter(String str) {
//        return StringUtils.isNotBlank(str) && str.matches("[\\u4E00-\\u9FA5]+");
        if (StringUtils.isEmpty(str)) return false;
        Matcher m = ChineseCharPattern.matcher(str);
        return m.find();
    }

    private List<EnumCurrency> getAllCurrency() {
        EnumCurrencyService enumCurrencyService = SpringUtil.getBean(EnumCurrencyService.class);
        return enumCurrencyService.findAllEnumCurrency();
    }

}
