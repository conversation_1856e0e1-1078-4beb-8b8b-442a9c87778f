package com.altomni.apn.application.dto;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A TalentRecruitmentProcessOnboardDateDTO.
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOnboardDateDTO{

    @ApiModelProperty(value = "流程 ID")
    private Long talentRecruitmentProcessId;

    @ApiModelProperty(value = "入职日期")
    private LocalDate onboardDate;

    @ApiModelProperty(value = "保证期的截止日期")
    private LocalDate warrantyEndDate;

    @ApiModelProperty(value = "职位 ID")
    private Long jobId;
}
