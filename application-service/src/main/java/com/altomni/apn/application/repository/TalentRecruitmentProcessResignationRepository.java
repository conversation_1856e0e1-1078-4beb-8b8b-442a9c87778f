package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessResignation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * Spring Data  repository for the TalentRecruitmentProcessResignation entity.
 * <AUTHOR>
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessResignationRepository extends JpaRepository<TalentRecruitmentProcessResignation, Long> {

    TalentRecruitmentProcessResignation findOneByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Query(value = "select 1 from talent_recruitment_process_resignation resign " +
            "where resign.talent_recruitment_process_id=:talentRecruitmentProcessId " +
            "   and not exists(select 1 from start s where s.talent_recruitment_process_id=:talentRecruitmentProcessId and s.start_type=5)", nativeQuery = true)
    List<Long> findByTalentRecruitmentProcessIdAndNonConvertToFte(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query("select r.talentRecruitmentProcessId from TalentRecruitmentProcessResignation r " +
            "where r.talentRecruitmentProcessId in :talentRecruitmentProcessIds")
    Set<Long> filterResignedTalentRecruitmentProcessIds(@Param("talentRecruitmentProcessIds") List<Long> talentRecruitmentProcessIds);

    @Query("select r from TalentRecruitmentProcessResignation r " +
            "inner join TalentRecruitmentProcess p on p.id=r.talentRecruitmentProcessId " +
            "where p.talentId =:talentId")
    List<TalentRecruitmentProcessResignation> findAllByTalentId(@Param("talentId") Long talentId);
}
