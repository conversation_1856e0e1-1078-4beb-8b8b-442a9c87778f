package com.altomni.apn.application.service.management;

import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.vo.talent.TenantPublicVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Component
@FeignClient(value = "management-service")
public interface ManagementClient {

    @GetMapping("/management/api/v3/public/tenants/{id}")
    ResponseEntity<TenantPublicVO> queryTenant(@PathVariable("id") Long id);

}
