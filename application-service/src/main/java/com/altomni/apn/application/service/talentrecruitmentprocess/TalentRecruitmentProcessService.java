package com.altomni.apn.application.service.talentrecruitmentprocess;


import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.dto.EliminateDTO;
import com.altomni.apn.application.web.rest.vm.*;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.*;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;


/**
 * Service Interface for managing TalentRecruitmentProcess.
 */
public interface TalentRecruitmentProcessService {

    Optional<TalentRecruitmentProcessVO> findOne(Long id);

    Optional<TalentRecruitmentProcessVO> findOneBrief(Long id);

    void checkPermission(Long talentRecruitmentProcessId);

    TalentRecruitmentProcessVO submitToJob(TalentRecruitmentProcessSubmitToJobVO submitToJobVO, boolean noteOnly);

    TalentRecruitmentProcessVO submitToClient(TalentRecruitmentProcessSubmitToClientVO submitToClientVO, boolean noteOnly);

    TalentRecruitmentProcessVO interview(TalentRecruitmentProcessInterviewVO interviewVO, boolean noteOnly);

    TalentRecruitmentProcessVO offer(TalentRecruitmentProcessOfferVO offer, boolean noteOnly);

    TalentRecruitmentProcessVO offerAccept(TalentRecruitmentProcessIpgOfferAcceptVO acceptVO, boolean noteOnly);

    void sendCongratsNotification(Long talentRecruitmentProcessId) throws IOException;

    TalentRecruitmentProcessVO commission(TalentRecruitmentProcessCommissionVO commission, boolean noteOnly);

    TalentRecruitmentProcessVO onboard(TalentRecruitmentProcessOnboardVO onboard, boolean noteOnly);

    void updateTalentExperience(StartDTO startDTO);

    TalentRecruitmentProcessVO eliminate(EliminateDTO eliminate);

    TalentRecruitmentProcessVO eliminateForAuto(EliminateDTO eliminate);

    TalentRecruitmentProcessVO onboardEliminate(EliminateDTO eliminate);

    AllNotesVO updateNote(UpdateNoteDTO noteDTO);

    TalentRecruitmentProcessVO cancelEliminate(Long talentRecruitmentProcessId);

    TalentRecruitmentProcessVO onboardCancelEliminate(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByTalentId(Long talentId);

    List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessBriefByTalentId(Long talentId);

    List<TalentRecruitmentProcessVO> getAllTalentRecruitmentProcessBriefByTalentId(Long talentId);

    Integer countTalentRecruitmentProcessByTalentId(Long talentId);

    List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessAllByTalentId(Long talentId);

    List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessByJobId(Long jobId);

    List<TalentRecruitmentProcessVO> getTalentRecruitmentProcessBriefByJobId(Long jobId);

    TalentRecruitmentProcessVO getTalentRecruitmentProcessByTalentIdAndJobId(Long talentId, Long jobId);

    TalentRecruitmentProcessVO getTalentRecruitmentProcessLastByJobId(Long jobId);

    Integer countApplicationByTalentId(Long talentId, Integer nodeType);

    void downloadDetailsOfChargesPdf(HttpServletResponse response, Long id, String language);

    Integer countUnfinishedApplicationsForJob(Long jobId);

    Map<Long, Long> countUnfinishedApplicationsForJobs(List<Long> jobIds);

    Integer countUnfinishedApplicationsByApplicationIds(List<Long> applicationIds);

    List<InProcessApplicationBriefVM> getUnfinishedApplicationsByApplicationIds(List<Long> applicationIds);

    List<ApplicationBriefInfoVM> getApplicationsBriefInoByApplicationIds(List<Long> applicationIds);

    AllNotesVO getAllNotes(Long talentRecruitmentProcessId);

    HistoryStagesVo getStages(Long id);

    Object getStageByType(Long talentRecruitmentProcessId, NodeType nodeType);

    LocalDate getOnboardDate(Long talentRecruitmentProcessId);

    void replaceSubmitToJobResume(ReplaceSubmitToJobResumeDTO replaceSubmitToJobResumeDTO);

    Set<Long> filterTalentIdsByUserIdAndTalentIds(Long userId, Set<Long> talentIds);

    String getTalentRecruitmentProcessLastInterviewDateTimeByJobId(Long jobId);

    TalentEmploymentStatusVO getTalentEmploymentStatus(Long talentId);

    boolean getSubmitOnboardedTalentToJobPermission();

    Page<ApplicationIdAndStatusVO> getStats(Set<Long> talentRecruitmentProcessIds, NodeType nodeType, Pageable pageable);

    JSONArray deleteByJobId(Long jobId);

    List<SubstituteTalentVO> getSubstituteTalentByJobId(Long jobId);

    void aiRecommendByJobIdAndTalentId(JSONObject aiRecommendJson);
}
