package com.altomni.apn.application.service.talentrecruitmentprocess.impl;


import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardDate;
import com.altomni.apn.application.repository.TalentRecruitmentProcessOnboardDateRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessOnboardDateService;
import com.altomni.apn.common.utils.ServiceUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessOnboardDate}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessOnboardDateServiceImpl implements TalentRecruitmentProcessOnboardDateService {

    @Resource
    private TalentRecruitmentProcessOnboardDateRepository talentRecruitmentProcessOnboardDateRepository;

    @Override
    public TalentRecruitmentProcessOnboardDate save(Long talentRecruitmentProcessId, TalentRecruitmentProcessOnboardDate talentRecruitmentProcessOnboardDate) {
        if (talentRecruitmentProcessOnboardDate == null) {
            return null;
        }
        TalentRecruitmentProcessOnboardDate exist = talentRecruitmentProcessOnboardDateRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (exist != null) {
            // update
            ServiceUtils.myCopyProperties(talentRecruitmentProcessOnboardDate, exist);
            return talentRecruitmentProcessOnboardDateRepository.save(exist);
        }
        // create
        talentRecruitmentProcessOnboardDate.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
        return talentRecruitmentProcessOnboardDateRepository.save(talentRecruitmentProcessOnboardDate);
    }

    @Override
    public TalentRecruitmentProcessOnboardDate findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return talentRecruitmentProcessOnboardDateRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
    }
}
