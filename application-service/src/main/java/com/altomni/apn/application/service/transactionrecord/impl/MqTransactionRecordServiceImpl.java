package com.altomni.apn.application.service.transactionrecord.impl;

import com.altomni.apn.application.repository.transactionrecord.CommonMqTransactionRecordRepository;
import com.altomni.apn.application.service.transactionrecord.MqTransactionRecordService;
import com.altomni.apn.common.domain.transactionrecord.CommonMqTransactionRecord;
import com.altomni.apn.common.utils.NotificationUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.util.Optional;

/**
 * mq 事务记录
 * <AUTHOR>
 */
@Service
@Slf4j
public class MqTransactionRecordServiceImpl implements MqTransactionRecordService {

    @Resource
    CommonMqTransactionRecordRepository commonMqTransactionRecordRepository;

    @Value("${application.notification.lark.mq.webhookKey}")
    private String LARK_WEBHOOK_KEY;

    @Value("${application.notification.lark.mq.webhookUrl}")
    private String LARK_WEBHOOK_URL;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStatusById(Long id, Integer sendStatus) {
        commonMqTransactionRecordRepository.updateStatusById(id, sendStatus);
        log.info("update transaction record status is {}, id:{}", sendStatus, id);
    }

    /**
     * 发送lark
     *
     * @param message
     */
    @Override
    public void sendExceptionByLark(String message) {
        if (StringUtils.isNotBlank(message)) {
            NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, message);
        }
    }

    @Override
    public CommonMqTransactionRecord findById(BigInteger id) {
        Optional<CommonMqTransactionRecord> optional = commonMqTransactionRecordRepository.findById(id);
        if(optional.isPresent()){
            return optional.get();
        }else{
            try {
                Thread.sleep(10000);
                Optional<CommonMqTransactionRecord> optional1 = commonMqTransactionRecordRepository.findById(id);
                if(optional1.isPresent()) {
                    return optional1.get();
                }
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return null;
    }
}