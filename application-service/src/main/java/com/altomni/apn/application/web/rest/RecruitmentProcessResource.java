package com.altomni.apn.application.web.rest;

import com.altomni.apn.application.domain.RecruitmentProcess;
import com.altomni.apn.application.dto.StatusDTO;
import com.altomni.apn.application.service.recruitmentprocess.RecruitmentProcessService;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessConfigVO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessStats;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessTalentAndJobStats;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.errors.BadRequestAlertException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.HeaderUtil;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.SortDefault;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST controller for managing RecruitmentProcess.
 */
@RestController
@RequestMapping("/api/v3/recruitment-processes")
public class RecruitmentProcessResource {

    private final Logger log = LoggerFactory.getLogger(RecruitmentProcessResource.class);

    private static final String ENTITY_NAME = "RecruitmentProcess";

    private final RecruitmentProcessService recruitmentProcessService;

    public RecruitmentProcessResource(RecruitmentProcessService recruitmentProcessService) {
        this.recruitmentProcessService = recruitmentProcessService;
    }

    /**
     * GET  /recruitment-processes : get all the recruitmentProcess.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of recruitmentProcess in body
     */
    @GetMapping("")
    public ResponseEntity<List<RecruitmentProcessVO>> getAll(@RequestParam(required = false) ActiveStatus status, @PageableDefault(size = 50) @SortDefault(sort = "id", direction = Sort.Direction.ASC) Pageable pageable) {
        log.info("REST request to get all RecruitmentProcess");
        Page<RecruitmentProcessVO> res = recruitmentProcessService.findAll(status, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(res, "/application/api/v3/recruitment-processes");
        return new ResponseEntity<>(res.getContent(), headers, HttpStatus.OK);
    }

    @GetMapping("/private-job")
    public ResponseEntity<List<RecruitmentProcessVO>> getAllForPrivateJob(@PageableDefault(size = 50) @SortDefault(sort = "id", direction = Sort.Direction.ASC) Pageable pageable) {
        log.info("REST request to get all RecruitmentProcess");
        Page<RecruitmentProcessVO> res = recruitmentProcessService.findAllForPrivateJob(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(res, "/application/api/v3/recruitment-processes");
        return new ResponseEntity<>(res.getContent(), headers, HttpStatus.OK);
    }

    @GetMapping("/admin/tenantId/{tenantId}")
    public ResponseEntity<Page<RecruitmentProcessVO>> getAllByTenantId(@PathVariable("tenantId") Long tenantId, @RequestParam(required = false) ActiveStatus status, @PageableDefault(size = 50) @SortDefault(sort = "id", direction = Sort.Direction.ASC) Pageable pageable) {
        log.info("REST request to get all RecruitmentProcess");
        return new ResponseEntity<>(recruitmentProcessService.findAllByTenantId(tenantId, status, pageable), HttpStatus.OK);
    }

    /**
     * POST  /recruitment-processes : Create a new recruitmentProcess.
     *
     * @param recruitmentProcess the recruitmentProcess to create
     * @return the ResponseEntity with status 201 (Created) and with body the new recruitmentProcess, or with status 400 (Bad Request) if the recruitmentProcess has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("")
    public ResponseEntity<RecruitmentProcessVO> create(@RequestBody RecruitmentProcessVO recruitmentProcess) throws URISyntaxException {
        log.info("REST request to save RecruitmentProcess : {}", recruitmentProcess);
        recruitmentProcess.setTenantId(SecurityUtils.getTenantId());
        RecruitmentProcessVO result = recruitmentProcessService.create(recruitmentProcess);
        return ResponseEntity.created(new URI("/api/v3/recruitment-processes/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @PostMapping("/admin")
    public ResponseEntity<RecruitmentProcessVO> createByAdmin(@RequestBody RecruitmentProcessVO recruitmentProcess) throws URISyntaxException {
        log.info("REST request to save RecruitmentProcess : {}", recruitmentProcess);
        if (recruitmentProcess.getId() != null) {
            throw new BadRequestAlertException("A new recruitmentProcess cannot already have an ID", ENTITY_NAME, "id exists");
        }
        if (recruitmentProcess.getTenantId() == null) {
            throw new BadRequestAlertException("A new recruitmentProcess must have a TenantID for Admin User", ENTITY_NAME, "tenantId not exists");
        }
        RecruitmentProcessVO result = recruitmentProcessService.create(recruitmentProcess);
        return ResponseEntity.created(new URI("/admin/api/v3/recruitment-processes/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @PostMapping("/init/ipg/tenantId/{tenantId}")
    public ResponseEntity<List<RecruitmentProcess>> initForIpg(@PathVariable Long tenantId) throws URISyntaxException {
        log.info("REST request to save RecruitmentProcess init for ipg: {}", tenantId);
        List<RecruitmentProcess> result = recruitmentProcessService.initForIpg(tenantId);
        return ResponseEntity.created(new URI("/api/v3/recruitment-processes/init/ipg/tenantId/" + tenantId))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, tenantId.toString()))
                .body(result);
    }

    @PostMapping("/init/tenantId/{tenantId}")
    public ResponseEntity<List<RecruitmentProcessVO>> initDefaultForGeneralRecruitingProcess(@PathVariable("tenantId") Long tenantId) throws URISyntaxException {
        log.info("REST request to init default RecruitmentProcess for tenant: {}", tenantId);
        List<RecruitmentProcessVO> result = recruitmentProcessService.initDefaultForGeneralRecruitingProcess(tenantId);
        return ResponseEntity.created(new URI("/api/v3/recruitment-processes/init/tenantId/" + tenantId))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, tenantId.toString()))
                .body(result);
    }

    /**
     * DELETE  /recruitment-processes/:id : delete the "id" recruitmentProcess.
     *
     * @param id the id of the recruitmentProcess to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        log.info("REST request to delete RecruitmentProcess : {}", id);
        recruitmentProcessService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(ENTITY_NAME, id.toString())).build();
    }

    /**
     * PUT  /recruitment-processes/:recruitmentProcessId/status : update the status of "id" recruitmentProcess.
     *
     * @param recruitmentProcessId the id of the recruitmentProcess to update the status
     * @return the ResponseEntity with status 200 (OK)
     */
    @PutMapping("/{recruitmentProcessId}/status")
    public ResponseEntity<RecruitmentProcessVO> updateStatus(@PathVariable Long recruitmentProcessId, @RequestBody @Valid StatusDTO status) {
        log.info("REST request to delete RecruitmentProcess : {}", recruitmentProcessId);
        RecruitmentProcessVO result = recruitmentProcessService.updateStatus(recruitmentProcessId, status.getStatus());
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, recruitmentProcessId.toString()))
                .body(result);
    }

    /**
     * PUT  /recruitment-processes : Updates an existing recruitmentProcess.
     *
     * @param recruitmentProcessVO the recruitmentProcess to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated recruitmentProcess,
     * or with status 400 (Bad Request) if the recruitmentProcess is not valid,
     * or with status 500 (Internal Server Error) if the recruitmentProcess couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/{recruitmentProcessId}")
    public ResponseEntity<RecruitmentProcessVO> update(@PathVariable Long recruitmentProcessId, @RequestBody RecruitmentProcessVO recruitmentProcessVO) throws URISyntaxException {
        log.info("REST request to update RecruitmentProcess : {}", recruitmentProcessVO);
//        if (recruitmentProcess.getId() == null) {
//            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "id null");
//        }
        RecruitmentProcessVO result = recruitmentProcessService.update(recruitmentProcessId, recruitmentProcessVO);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    /**
     * GET  /recruitment-processes/:id : get the "id" recruitmentProcess.
     *
     * @param id the id of the recruitmentProcess to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the recruitmentProcess, or with status 404 (Not Found)
     */
    @GetMapping("/{id}")
    public ResponseEntity<RecruitmentProcessVO> findOne(@PathVariable Long id) {
        log.info("REST request to get RecruitmentProcess : {}", id);
        RecruitmentProcessVO recruitmentProcess = recruitmentProcessService.findOne(id);
        return ResponseUtil.wrapOrNotFound(Optional.of(recruitmentProcess));
    }

    @GetMapping("/brief/{id}")
    public ResponseEntity<RecruitmentProcessVO> findOneBrief(@PathVariable Long id) {
        log.info("REST request to get RecruitmentProcess : {}", id);
        RecruitmentProcessVO recruitmentProcess = recruitmentProcessService.findOneBrief(id);
        return ResponseUtil.wrapOrNotFound(Optional.of(recruitmentProcess));
    }

    @GetMapping("/default")
    public ResponseEntity<RecruitmentProcessVO> getDefaultRecruitmentProcess(@RequestParam(value = "jobType", required = false) JobType jobType) {
        log.info("REST request to get default RecruitmentProcess, jobType : {}", jobType);
        RecruitmentProcessVO recruitmentProcess = recruitmentProcessService.getDefaultRecruitmentProcess(jobType);
        return ResponseUtil.wrapOrNotFound(Optional.of(recruitmentProcess));
    }

    @PostMapping("/config")
    public ResponseEntity<List<RecruitmentProcessVO>> config(@RequestBody RecruitmentProcessConfigVO configVO) throws URISyntaxException {
        log.info("REST request to config RecruitmentProcessConfigVO : {}", configVO);
        if (configVO.getId() != null) {
            throw new BadRequestAlertException("A new recruitmentProcess cannot already have an ID", ENTITY_NAME, "id exists");
        }
        List<RecruitmentProcessVO> result = recruitmentProcessService.config(configVO);
        return ResponseEntity.created(new URI("/api/v3/recruitment-processes/"))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, configVO.getTenantId().toString()))
                .body(result);
    }

    @PostMapping("/clint-board-interview-stats")
    public ResponseEntity<List<RecruitmentProcessStats>> getClintBoardInterviewStats(@RequestBody List<Long> jobIdList) {
        log.info("REST request to config getClintBoardInterviewStats : {}", jobIdList);
        return ResponseEntity.ok(recruitmentProcessService.getClintBoardInterviewStats(jobIdList));
    }

    @GetMapping("/{recruitmentProcessId}/in-progress")
    public ResponseEntity<RecruitmentProcessTalentAndJobStats> getInProgressJobAndApplicationCountByRecruitmentProcessId(@PathVariable Long recruitmentProcessId) {
        log.info("REST request to get all RecruitmentProcess");
        return new ResponseEntity<>(recruitmentProcessService.getInProgressJobCountByRecruitmentProcessId(recruitmentProcessId), HttpStatus.OK);
    }

//    @GetMapping("/{recruitmentProcessId}/in-progress/download")
//    public void downloadInProgressJobAndApplicationCountByRecruitmentProcessId(@PathVariable Long recruitmentProcessId, @RequestParam(value = "language", defaultValue = "en", required = false) String language, HttpServletResponse response) {
//        log.info("REST request to get all RecruitmentProcess");
//        recruitmentProcessService.downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(recruitmentProcessId, language, response);
//    }

    @GetMapping("/{recruitmentProcessId}/in-progress/download")
    public ResponseEntity<Resource> downloadInProgressJobAndApplicationCountByRecruitmentProcessId(@PathVariable Long recruitmentProcessId, @RequestParam(value = "language", defaultValue = "en", required = false) String language, HttpServletRequest request) {
        try {
            byte[] excelFileData = recruitmentProcessService.downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(recruitmentProcessId, language);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String filename = "Unfinished Applications for RecruitmentProcessId_" + recruitmentProcessId + "_" + new SimpleDateFormat("yyyyMMdd").format(Date.from(Instant.now())) + ".xlsx";

            if ("cn".equalsIgnoreCase(language)) {
                filename = "失效流程数据展示_职位_" + new SimpleDateFormat("yyyyMMdd").format(Date.from(Instant.now())) + ".xlsx";
            }

            if (isBrowserRequest(request)) {
                // 如果是浏览器请求，设置内容处置为附件下载
                headers.setContentDisposition(ContentDisposition.attachment().filename(filename, StandardCharsets.UTF_8).build());
            } else {
                // 对于服务间调用，可能不需要设置为附件下载
                headers.setContentDisposition(ContentDisposition.inline().filename(filename, StandardCharsets.UTF_8).build());
            }

            return ResponseEntity.ok()
                    .headers(headers)
                    .body(new ByteArrayResource(excelFileData));
        } catch (NotFoundException | ForbiddenException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(null);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(null);
        }
    }

    private boolean isBrowserRequest(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        // 这里可以根据需要检查userAgent字符串，以确定请求是否来自浏览器
        return userAgent != null && (userAgent.contains("Mozilla") || userAgent.contains("Chrome") || userAgent.contains("Safari"));
    }

    @PostMapping("/clint-board-interview-stats-format")
    public ResponseEntity<List<Map<String, Object>>> getClintBoardInterviewStatsMap(@RequestBody List<Long> jobIdList) {
        log.info("REST request to config getClintBoardInterviewStats : {}", jobIdList);
        return ResponseEntity.ok(recruitmentProcessService.getClintBoardInterviewStatsMap(jobIdList));
    }

    /**
     * get all recruitmentProcess Ids Except one jobType

     * @return map<Long, JobType>: (recruitmentProcessId, JobType)
     */
    @GetMapping("/my-recruitment-process-ids")
    public ResponseEntity<Map<Long, JobType>> getAllRecruitmentProcessIds() {
        log.info("REST request to get all recruitment processId under current Tenant : {}", SecurityUtils.getTenantId());
        return ResponseEntity.ok(recruitmentProcessService.findAllMyRecruitmentProcessIds());
    }

    /**
     * Count the recruitment for dynamic update the job column/form config field
     * @return
     */
    @GetMapping("/count-my-recruitment-process")
    public ResponseEntity<Long> countActiveMyRecruitmentProcess() {
        log.info("REST request to count all recruitment process under current Tenant : {}", SecurityUtils.getTenantId());
        return ResponseEntity.ok(recruitmentProcessService.countMyActiveRecruitmentProcess());
    }

    /**
     * feign-use
     * @return
     */
    @GetMapping("/get-all")
    public List<RecruitmentProcessVO> getAllRecruitmentProcess() {
        log.info("REST request to get all RecruitmentProcess List");
        return recruitmentProcessService.getAllRecruitmentProcess();
    }
}
