package com.altomni.apn.application.repository.aiRecommend;

import com.altomni.apn.application.domain.aiRecommend.JobTalentAiRecommend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the JobTalentAiRecommend entity.
 */
@Repository
public interface JobTalentAiRecommendRepository extends JpaRepository<JobTalentAiRecommend, Long> {

    List<JobTalentAiRecommend> findByTenantIdAndTalentIdAndJobId(Long tenantId, Long talendId, Long jobId);

    List<JobTalentAiRecommend> findByTenantIdAndJobIdAndTalentIdIn(Long tenantId, Long jobId, List<Long> talendIds);

    List<JobTalentAiRecommend> findByTenantIdAndJobIdInAndTalentId(Long tenantId, List<Long> jobIds, Long talendId);
}
