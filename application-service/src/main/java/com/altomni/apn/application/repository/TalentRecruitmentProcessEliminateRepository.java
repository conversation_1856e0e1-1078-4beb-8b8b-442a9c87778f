package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessEliminate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data  repository for the TalentRecruitmentProcessEliminate entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessEliminateRepository extends JpaRepository<TalentRecruitmentProcessEliminate, Long> {

    TalentRecruitmentProcessEliminate findOneByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    void deleteByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = " delete from talent_recruitment_process_eliminate where talent_recruitment_process_id= ?1 ", nativeQuery = true)
    void nativeDeleteByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessEliminate> findAllByTalentRecruitmentProcessIdIn(List<Long> recruitmentProcessIds);
}
