package com.altomni.apn.application.service.message;

import com.altomni.apn.common.dto.message.MessageCreateWithNoPoachingSubmitDTO;
import com.altomni.apn.common.dto.message.MessageCreateWithTalentInfoDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "common-service")
public interface MessageClientService {

    @PostMapping("/common/api/v3/message/talent-info-update")
    ResponseEntity<Void> createMessageWithTalentInfoUpdate(@RequestBody MessageCreateWithTalentInfoDTO messageCreateDTO);


    /**
     * 发送禁猎客户员工被推荐到职位提醒
     * @param messageCreateDTO
     * @return
     */
    @PostMapping("/common/api/v3/message/no-poaching-submit-message")
    ResponseEntity<Void> createMessageWithNoPoachingSubmit(@RequestBody MessageCreateWithNoPoachingSubmitDTO messageCreateDTO);
}
