package com.altomni.apn.application.domain;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.enumeration.user.UserRoleConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A TalentRecruitmentProcessKpiUser.
 */
@Entity
@Table(name = "talent_recruitment_process_kpi_user")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessKpiUser extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -8182068746435227711L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @JsonIgnore
    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "user_id")
    private Long userId;

    @ApiModelProperty(value = "For IPG version only.")
    @Convert(converter = UserRoleConverter.class)
    @Column(name = "user_role")
    private UserRole userRole;

    @Column(name = "percentage")
    private BigDecimal percentage = new BigDecimal(0);

    @ApiModelProperty(value = "For general version only.")
    @Column(name = "currency")
    private Integer currency;

    @ApiModelProperty(value = "For general version only.")
    @Column(name = "amount")
    private BigDecimal amount;

    @Column(name = "country")
    private String country;
}
