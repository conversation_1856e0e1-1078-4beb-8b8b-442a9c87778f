package com.altomni.apn.application.service.dashboard.impl;

import cn.hutool.Hutool;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.application.dto.DashBoardTimeRangeDTO;
import com.altomni.apn.application.repository.dashboard.DashboardRepository;
import com.altomni.apn.application.service.dashboard.DashboardService;
import com.altomni.apn.application.service.finance.FinanceService;
import com.altomni.apn.application.service.user.UserClient;
import com.altomni.apn.application.web.rest.vm.KpiTopVo;
import com.altomni.apn.application.web.rest.vm.MyInvoiceVO;
import com.altomni.apn.application.web.rest.vm.TeamPerformanceVO;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.application.dashboard.MyCandidate;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateSearchParam;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.enumeration.enums.JobAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.altomni.apn.job.domain.project.JobProject;
import com.altomni.apn.job.service.dto.job.JobEsSyncDocument;
import com.altomni.apn.job.web.rest.vm.MyJobVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.TenantGeneralConfigConstants.DEFAULT_CURRENCY;
import static com.altomni.apn.common.constants.TenantHomePageConfigConstants.FISCAL_YEAR_START_MONTH;

@Slf4j
@Service
public class DashboardServiceImpl implements DashboardService {

    @Resource
    private FinanceService financeService;

    @Resource
    private DashboardRepository dashboardRepository;

    @Resource
    private UserClient userClient;

    @Resource
    CommonRedisService commonRedisService;

    @Value("${index.cache.isOpen:false}")
    private Boolean cacheIsOpen;

    @Value("${index.cache.expire-time:600}")
    private Integer cacheExpireTime;

    private List<Object> objectToMyCandidate(List<Object[]> objects) {
        List<Object> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(objects)) {
            return result;
        }
        Predicate<Object[]> onBoardFilter = obj -> {
            String statusOnboardString = StringUtil.valueOf(obj[7]);
            Integer statusOnboard = StringUtils.isEmpty(statusOnboardString) ? null : Integer.valueOf(statusOnboardString);
            return Objects.equals(MyCandidateStatusFilter.ON_BOARD.toDbValue(), statusOnboard);
        };
        List<Long> talentRecruitmentProcessIds = objects.stream().filter(onBoardFilter).map(obj -> Long.valueOf(StringUtil.valueOf(obj[0]))).toList();
        Map<Long, MyCandidate> totalBillAmountMap = financeService.getTotalBillAmountByTalentRecruitmentProcessId(talentRecruitmentProcessIds)
            .stream().collect(Collectors.toMap(MyCandidate::getTalentRecruitmentProcessId, Function.identity()));
        for (Object[] obj : objects) {
            MyCandidate element = new MyCandidate();
            Long talentRecruitmentProcessId = Long.valueOf(StringUtil.valueOf(obj[0]));
            element.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
            element.setTalentId(Long.valueOf(StringUtil.valueOf(obj[1])));
            element.setTalentName(StringUtil.valueOf(obj[2]));
            element.setJobId(Long.valueOf(StringUtil.valueOf(obj[3])));
            element.setJobTitle(StringUtil.valueOf(obj[4]));
            element.setCompanyId(Long.valueOf(StringUtil.valueOf(obj[5])));
            element.setCompanyName(StringUtil.valueOf(obj[6]));
            String statusString = StringUtil.valueOf(obj[8]);
            Integer status = StringUtils.isEmpty(statusString) ? null : Integer.valueOf(statusString);
            if (Objects.equals(NodeStatus.ELIMINATED.toDbValue(), status)) {
                element.setStatus(MyCandidateStatusFilter.ELIMINATED);
                if (ObjectUtil.isNotEmpty(obj[12])) {
                    Integer reason = Integer.parseInt(StringUtil.valueOf(obj[12]));
                    element.setEliminateReason(EliminateReason.fromDbValue(reason));
                }
            } else {
                if(ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[7]))) {
                    element.setStatus(MyCandidateStatusFilter.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[7]))));
                }
            }
            element.setLastModifiedDate(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[9])));
            element.setInterviewCount(Integer.valueOf(StringUtil.valueOf(obj[10])));
            if(ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[11]))) {
                element.setJobType(JobType.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[11]))));
            }
            if (onBoardFilter.test(obj)) {
                MyCandidate totalBillAmountObject = totalBillAmountMap.get(talentRecruitmentProcessId);
                element.setCurrency(totalBillAmountObject.getCurrency());
                element.setTotalBillAmount(totalBillAmountObject.getTotalBillAmount());
            }
            result.add(element);
        }
        return result;
    }

    private List<Integer> getUserRolesFromParam(MyCandidateSearchParam searchParam) {
        return CollectionUtils.isNotEmpty(searchParam.getUserRoles()) ?
                searchParam.getUserRoles().stream().map(UserRole::toDbValue).collect(Collectors.toList()) :
                UserRole.ALL_USER_ROLES;
    }


    @Override
    public LinkedHashMap<String, Integer> myApplicationCandidates(Instant startTime, Instant endTime,List<Long> userIdList) {

        String redisKey = getHashValue(getRedisKey(startTime,endTime,userIdList,null,"myApplicationCandidates"));

        if (cacheIsOpen) {
            String cacheResult = commonRedisService.get(redisKey);
            if(StringUtils.isNotBlank(cacheResult)){
                return JSONUtil.toBean(cacheResult,LinkedHashMap.class);
            }
            LinkedHashMap<String,Integer> result = dashboardRepository.myApplicationCandidates(startTime, endTime,userIdList);
            commonRedisService.set(redisKey,JSONUtil.toJsonStr(result),cacheExpireTime);
        }
        return dashboardRepository.myApplicationCandidates(startTime, endTime,userIdList);
    }

    @Override
    public LinkedHashMap<String, Integer> myApplicationCandidates(Instant startTime, Instant endTime, List<Long> userIdList, List<Long> applicationIds) {

        String redisKey = getHashValue(getRedisKeyWithApplicationIds(startTime,endTime,userIdList, applicationIds,"myApplicationCandidatesWithApplicationIds"));

        if (cacheIsOpen) {
            String cacheResult = commonRedisService.get(redisKey);
            if(StringUtils.isNotBlank(cacheResult)){
                return JSONUtil.toBean(cacheResult,LinkedHashMap.class);
            }
            LinkedHashMap<String,Integer> result = dashboardRepository.myApplicationCandidatesWithApplicationIds(startTime, endTime, userIdList, applicationIds);
            commonRedisService.set(redisKey,JSONUtil.toJsonStr(result),cacheExpireTime);
        }
        return dashboardRepository.myApplicationCandidatesWithApplicationIds(startTime, endTime, userIdList, applicationIds);
    }


    private String getHashValue(String value) {
        try {
            StringBuilder sb = new StringBuilder();
            MessageDigest object = MessageDigest.getInstance("SHA-256");
            byte[] encrypted = object.digest(JSONUtil.toJsonStr(value).getBytes("UTF-8"));
            for (byte b : encrypted) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {

        }
        return null;
    }

    private String getRedisKey(Instant startTime, Instant endTime, List<Long> userIdList, Integer top, String type) {
        String key = DateUtil.fromInstantToDateString(startTime, SecurityUtils.getUserTimeZone())
                + DateUtil.fromInstantToDateString(endTime, SecurityUtils.getUserTimeZone()) +
                StringUtils.join(userIdList, ",");
        if (null != top) {
            key = key + top;
        }
        return key + type;
    }

    private String getRedisKeyWithApplicationIds(Instant startTime, Instant endTime, List<Long> userIdList, List<Long> applicationIds, String type) {
        String key = DateUtil.fromInstantToDateString(startTime, SecurityUtils.getUserTimeZone())
                + DateUtil.fromInstantToDateString(endTime, SecurityUtils.getUserTimeZone())
                + StringUtils.join(userIdList, ",")
                + StringUtils.join(applicationIds, ",");
        return key + type;
    }

    private String getRedisCache(Instant startTime, Instant endTime, List<Long> userIdList,Integer top,String type){

        String redisKey = getHashValue(getRedisKey(startTime, endTime, userIdList, top, type));

        if (cacheIsOpen) {
            String cacheResult = commonRedisService.get(redisKey);
            if(StringUtils.isNotBlank(cacheResult)){
                return cacheResult;
            }
        }
        return null;
    }

    @Override
    public MyInvoiceVO myInvoices(Instant startTime, Instant endTime, List<Long> userIdList) {

        String cacheResult = getRedisCache(startTime, endTime, userIdList,null,"myInvoices");
        if (StringUtils.isNotBlank(cacheResult)) {
            return JSONUtil.toBean(cacheResult, MyInvoiceVO.class);
        }

        ConcurrentStopWatch stopWatch = new ConcurrentStopWatch("myInvoices");
        stopWatch.start("total task");
        SecurityContext context = SecurityContextHolder.getContext();
        // trp 的最后修改时间
        CompletableFuture<Integer> onboardFuture = CompletableFuture.supplyAsync(() -> {
           SecurityContextHolder.setContext(context);
            stopWatch.start("findOnboardedNotInvoiced");
            Integer count = dashboardRepository.findOnboardedNotInvoiced(startTime, endTime,userIdList);
            stopWatch.stop();
            log.info("myInvoices findOnboardedNotInvoiced time = {}ms ", stopWatch.getTotalTimeMillis());
            return count;
        });
        // invoice 创建时间在时间范围的自己在流程内的invoice
        CompletableFuture<Integer> invoiceFteFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            stopWatch.start("findFteInvoicedPendingPayment");
            Integer count = dashboardRepository.findFteInvoicedPendingPayment(startTime, endTime,userIdList);
            stopWatch.stop();
            log.info("myInvoices findFteInvoicedPendingPayment time = {}ms ", stopWatch.getTotalTimeMillis());
            return count;
        });
        CompletableFuture<Integer> invoiceContractFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            stopWatch.start("findContractInvoicedPendingPayment");
            Integer count = dashboardRepository.findContractInvoicedPendingPayment(startTime, endTime,userIdList);
            stopWatch.stop();
            log.info("myInvoices findContractInvoicedPendingPayment time = {}ms ", stopWatch.getTotalTimeMillis());
            return count;
        });
        // 所有invoice 自己在流程内的
        CompletableFuture<Integer> fteOverdueFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            stopWatch.start("findFteOverdueNotReceived");
            Integer count = dashboardRepository.findFteOverdueNotReceived(userIdList);
            stopWatch.stop();
            log.info("myInvoices findFteOverdueNotReceived time = {}ms ", stopWatch.getTotalTimeMillis());
            return count;
        });
        CompletableFuture<Integer> contractOverdueFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            stopWatch.start("findContractOverdueNotReceived");
            Integer count = dashboardRepository.findContractOverdueNotReceived(userIdList);
            stopWatch.stop();
            log.info("myInvoices findContractOverdueNotReceived time = {}ms ", stopWatch.getTotalTimeMillis());
            return count;
        });
        try {
            MyInvoiceVO myInvoiceVO = new MyInvoiceVO();
            myInvoiceVO.setOnboardedNotInvoiced(onboardFuture.get());
            myInvoiceVO.setInvoicedFtePendingPayment(invoiceFteFuture.get());
            myInvoiceVO.setInvoicedContractPendingPayment(invoiceContractFuture.get());
            myInvoiceVO.setFteOverdueNotReceived(fteOverdueFuture.get());
            myInvoiceVO.setContractOverdueNotReceived(contractOverdueFuture.get());
            stopWatch.stop();
            log.info("myInvoices total time = {}ms ", stopWatch.getTotalTimeMillis());

            if (cacheIsOpen) {
                commonRedisService.set(getHashValue(getRedisKey(startTime, endTime, userIdList,null,"myInvoices")), JSONUtil.toJsonStr(myInvoiceVO), cacheExpireTime);
            }

            return myInvoiceVO;
        } catch (Exception e) {
            log.error("request my invoices is error , userId = {}, message = {}", SecurityUtils.getUserId(), ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("Network error, please try again.");
        }
    }

    @Override
    public List<KpiTopVo> kpiTop(Integer top, Integer currency) {
        StopWatch stopWatch = new StopWatch("kpiTop");
        stopWatch.start("1.get filter begin end");
        DashBoardTimeRangeDTO dto = setStartAndEndTime();
        stopWatch.stop();

        String cacheResult = getRedisCache(dto.getStartTime(), dto.getEndTime(), Arrays.asList(SecurityUtils.getUserId()), top,"kpiTop");
        if (StringUtils.isNotBlank(cacheResult)) {
            JSONArray array = JSONUtil.parseArray(cacheResult);
            return JSONUtil.toList(array, KpiTopVo.class);
        }

        stopWatch.start("2.search kpiTop Data");
        List<KpiTopVo> kpiTopVoList = dashboardRepository.kpiTop(dto.getStartTime(), dto.getEndTime(), top, currency);
        stopWatch.stop();

        log.info("kpiTop time = {}ms \n {}", stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());

        if (cacheIsOpen) {
            commonRedisService.set(getHashValue(getRedisKey(dto.getStartTime(), dto.getEndTime(), Arrays.asList(SecurityUtils.getUserId()),top,"kpiTop")), JSONUtil.toJsonStr(kpiTopVoList), cacheExpireTime);
        }

        return kpiTopVoList;
    }

    @Override
    public BigDecimal myPerformances(Integer currency) {
        DashBoardTimeRangeDTO dto = setStartAndEndTime();
        return dashboardRepository.findAverageYieldPerUnit(dto.getStartTime(), dto.getEndTime(), currency);
    }

    @Override
    public TeamPerformanceVO teamPerformances(Instant startTime, Instant endTime, Integer currency,List<Long> userIdList) {

        String cacheResult = getRedisCache(startTime, endTime, userIdList,null,"teamPerformances");
        if (StringUtils.isNotBlank(cacheResult)) {
            return JSONUtil.toBean(cacheResult, TeamPerformanceVO.class);
        }

        ConcurrentStopWatch stopWatch = new ConcurrentStopWatch("teamPerformances");
        stopWatch.start("teamPerformances total task");
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<BigDecimal> contractPaidFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            stopWatch.start("contract amount for paid");
            BigDecimal bigDecimal = dashboardRepository.findTeamContractAmountPaid(startTime, endTime, currency,userIdList);
            stopWatch.stop();
            log.info("teamPerformances contract amount for paid time = {}ms ", stopWatch.getTotalTimeMillis());
            return bigDecimal;
        });
        CompletableFuture<BigDecimal> contractUnPaidFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            stopWatch.start("contract amount for unpaid");
            BigDecimal bigDecimal = dashboardRepository.findTeamContractAmountOutstanding(startTime, endTime, currency,userIdList);
            stopWatch.stop();
            log.info("teamPerformances contract amount for unpaid time = {}ms ", stopWatch.getTotalTimeMillis());
            return bigDecimal;
        });
        CompletableFuture<TeamPerformanceVO> fteFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            stopWatch.start("fte amount");
            TeamPerformanceVO teamPerformanceVO = dashboardRepository.findTeamFteAmount(startTime, endTime, currency,userIdList);
            stopWatch.stop();
            log.info("teamPerformances fte amount time = {}ms ", stopWatch.getTotalTimeMillis());
            return teamPerformanceVO;
        });
        try {
            TeamPerformanceVO vo = fteFuture.get();
            vo.setContractPaid(contractPaidFuture.get());
            vo.setContractUnPaid(contractUnPaidFuture.get());
            stopWatch.stop();
            log.info("teamPerformances time = {}ms ", stopWatch.getTotalTimeMillis());

            if (cacheIsOpen) {
                commonRedisService.set(getHashValue(getRedisKey(startTime, endTime, userIdList,null,"teamPerformances")), JSONUtil.toJsonStr(vo), cacheExpireTime);
            }

            return vo;
        } catch (Exception e) {
            log.error("teamPerformances is error, message = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("Internal Server Error");
        }
    }

    private DashBoardTimeRangeDTO setStartAndEndTime() {
        DashBoardTimeRangeDTO dto = new DashBoardTimeRangeDTO();
        UserTimeZoneVO userTimeZoneVO = userClient.getTimezoneByUserId().getBody();
        //获取财年的查询范围
        String timezone = userTimeZoneVO.getUserTimezone();
        ZonedDateTime zonedDateTime = getNowByTimeZone(timezone);
        TenantConfigDTO homePageConfig = userClient.getSettingConfig(TenantConfigCode.HOME_PAGE_CONFIG).getBody();
        int configMonth  = getFiscalYearStartMonth(homePageConfig.getConfigValue());
        int timezoneMonth = zonedDateTime.getMonthValue();
        //配置的10月份,现在1月份,则进入下一个财年时间范围, 配置1月份,现在是1月份
        ZonedDateTime startZoneTime;
        ZonedDateTime endZoneTime;
        LocalDate date = LocalDate.of(zonedDateTime.getYear(), configMonth, 1);
        if (timezoneMonth >= configMonth) {
            startZoneTime = date.atStartOfDay().atZone(ZoneId.of(timezone));
            endZoneTime = startZoneTime.plusYears(1);
        } else {
            endZoneTime = date.atStartOfDay().atZone(ZoneId.of(timezone));
            startZoneTime = endZoneTime.plusYears(-1);
        }
        dto.setStartTime(startZoneTime.toInstant());
        dto.setEndTime(endZoneTime.toInstant());
        return dto;
    }

    private ZonedDateTime getNowByTimeZone(String userTimeZone) {
        if (StrUtil.isEmpty(userTimeZone)) {
            return getUtc();
        }
        return Instant.now().atZone(ZoneId.of(userTimeZone));
    }

    private int getFiscalYearStartMonth(String configValue) {
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(configValue);
        return Integer.parseInt(fieldToValueMap.getOrDefault(FISCAL_YEAR_START_MONTH, "1"));
    }

    private int getCurrency(String configValue) {
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(configValue);
        return Integer.parseInt(fieldToValueMap.getOrDefault(DEFAULT_CURRENCY, "1"));
    }

    private ZonedDateTime getUtc() {
        Instant instant = Instant.now();
        return instant.atZone(ZoneId.of("UTC"));
    }

    @Override
    public MyJobVo myJobs(Instant startTime, Instant endTime, List<Long> userIdList) {
        Long tenantId = SecurityUtils.getTenantId();
        JobProject jobProjectOptional = dashboardRepository.findFirstByTenantId(tenantId);
        long projectIdForPrivateJob;
        if (null != jobProjectOptional){
            projectIdForPrivateJob = jobProjectOptional.getId();
        } else {
            projectIdForPrivateJob = -1L;
        }

        TenantConfigDTO configDTO = userClient.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();

        Integer recommendDay = 14;
        Integer interviewDay = 14;

        if(null != configDTO){
            JSONArray configArray = JSONUtil.parseArray(configDTO.getConfigValue());
            for(int i=0;i<configArray.size();i++){
                JSONObject obj = configArray.getJSONObject(i);
                if(obj.getStr("field").equals("POSITION_UNSUBMITTED_CANDIDATE_DAYS")){
                    recommendDay = obj.getInt("value");
                }

                if(obj.getStr("field").equals("POSITION_UNINTERVIEWED_DAYS")){
                    interviewDay = obj.getInt("value");
                }
            }
        }

        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture<MyJobVo> myJobVoCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return dashboardRepository.findMyJobs(userIdList, tenantId, startTime, endTime, projectIdForPrivateJob);
        });
        Integer finalRecommendDay = recommendDay;
        CompletableFuture<Integer> intCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityContextHolder.setContext(context);
            return dashboardRepository.findNotRecommendedWithin14Days(userIdList, tenantId, projectIdForPrivateJob, finalRecommendDay);
        });
//        Integer finalInterviewDay = interviewDay;
//        CompletableFuture<Integer> intInterviewFuture = CompletableFuture.supplyAsync(() -> {
//            SecurityContextHolder.setContext(context);
//            return dashboardRepository.findNotInterviewWithin14Days(userIdList, tenantId, projectIdForPrivateJob, finalInterviewDay);
//        });
        try {
            MyJobVo myJobVo = myJobVoCompletableFuture.get();
            myJobVo.setNotRecommendedWithin14Days(intCompletableFuture.get());
//            myJobVo.setNotInterviewWithin14Days(intInterviewFuture.get());
            myJobVo.setRecommendDay(recommendDay);
//            myJobVo.setInterviewDay(interviewDay);
            return myJobVo;
        } catch (Exception e) {
            log.error("request my jobs is error , userId = {}, message = {}", JSONUtil.toJsonStr(userIdList), ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException("Internal Server Error");
        }
    }




}
