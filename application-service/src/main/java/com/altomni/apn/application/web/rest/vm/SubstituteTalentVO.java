package com.altomni.apn.application.web.rest.vm;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SubstituteTalentVO {

    private BigInteger talentId;

    private String talentName;

    private BigInteger relationProcessId;
}