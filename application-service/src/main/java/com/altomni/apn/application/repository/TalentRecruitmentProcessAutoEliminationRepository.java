package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessAutoElimination;
import com.altomni.apn.application.domain.TalentRecruitmentProcessEliminate;
import com.altomni.apn.application.dto.AutoEliminationDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import javax.persistence.SqlResultSetMapping;
import javax.persistence.Tuple;
import java.util.List;


/**
 * Spring Data  repository for the TalentRecruitmentProcessAutoElimination entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessAutoEliminationRepository extends JpaRepository<TalentRecruitmentProcessAutoElimination, Long> {

    TalentRecruitmentProcessAutoElimination findOneByJobId(Long jobId);

    @Query(value = """
                    SELECT trp.id as talent_recruitment_process_id, ae.period
                    FROM talent_recruitment_process trp
                    INNER JOIN (
                        SELECT job_id, `period`  
                        FROM talent_recruitment_process_auto_elimination trpae  
                        WHERE trpae.auto = 1
                    ) ae  
                    ON trp.job_id = ae.job_id  
                    INNER JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id  AND trpn.node_status = 1 AND node_type in (10,20,30,40,41)
                    WHERE DATE_ADD(trp.last_modified_date, INTERVAL ae.period DAY) < NOW();
            """, nativeQuery = true)
    List<Tuple> findNeedAutoEliminationTalentRecruitmentProcessIds();

    @Query(value = """
                    SELECT trp.id as talent_recruitment_process_id, ae.period
                    FROM talent_recruitment_process trp
                    INNER JOIN (
                        SELECT job_id, `period`  
                        FROM talent_recruitment_process_auto_elimination trpae  
                        WHERE trpae.auto = 1  AND trpae.job_id = ?1
                    ) ae  
                    ON trp.job_id = ae.job_id
                    INNER JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id  AND trpn.node_status = 1 AND node_type in (10,20,30,40,41)
                    WHERE DATE_ADD(trp.last_modified_date, INTERVAL ae.period DAY) < NOW();
            """, nativeQuery = true)
    List<Tuple> findNeedAutoEliminationTalentRecruitmentProcessIdsByJobId(Long jobId);

    @Query(value = """
                    SELECT COUNT(trp.id) 
                    FROM talent_recruitment_process trp
                    INNER JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id  AND trpn.node_status = 1 AND node_type in (10,20,30,40,41)
                    WHERE trp.job_id = ?1 AND DATE_ADD(trp.last_modified_date, INTERVAL ?2 DAY) < NOW();
            """, nativeQuery = true)
    Long findNeedAutoEliminationTalentRecruitmentProcessIdsByJobIdAndPeriod(Long jobId, Integer period);
}
