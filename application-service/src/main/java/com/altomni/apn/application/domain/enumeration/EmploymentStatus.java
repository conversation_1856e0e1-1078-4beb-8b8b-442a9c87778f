package com.altomni.apn.application.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * <AUTHOR>
 */

public enum EmploymentStatus implements ConvertedEnum<Integer> {


    PRE_ONBOARDING(0), // 待入职
    UNDER_WARRANTY(1), // 保证期内
    ONBOARDED(2); // 已入职

    private final int dbValue;
    EmploymentStatus(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<EmploymentStatus, Integer> resolver =
        new ReverseEnumResolver<>(EmploymentStatus.class, EmploymentStatus::toDbValue);

    public static EmploymentStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
