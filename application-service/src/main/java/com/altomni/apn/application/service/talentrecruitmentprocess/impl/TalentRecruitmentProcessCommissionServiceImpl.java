package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessCommission;
import com.altomni.apn.application.repository.TalentRecruitmentProcessCommissionRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessCommissionService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessFeeChargeService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessOnboardClientInfoService;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessCommissionVO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;

/**
 * Service Implementation for managing TalentRecruitmentProcessCommission.
 */
@Service
@Transactional
public class TalentRecruitmentProcessCommissionServiceImpl implements TalentRecruitmentProcessCommissionService {

    @Resource
    private TalentRecruitmentProcessCommissionRepository talentRecruitmentProcessCommissionRepository;
    @Resource
    private TalentRecruitmentProcessKpiUserService kpiUserService;
    @Resource
    private TalentRecruitmentProcessFeeChargeService feeChargeService;
    @Resource
    private TalentRecruitmentProcessOnboardClientInfoService clientInfoService;

    @Override
    public TalentRecruitmentProcessCommissionVO updateNoteOnly(TalentRecruitmentProcessCommissionVO commissionVO) {
        return this.updateNoteOnly(commissionVO.getTalentRecruitmentProcessId(), commissionVO.getNote());
    }

    @Override
    public TalentRecruitmentProcessCommissionVO updateNoteOnly(Long talentRecruitmentProcessId, String note) {
        TalentRecruitmentProcessCommission exist = talentRecruitmentProcessCommissionRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (exist != null) {
            // update
//            exist.setNote(note);
//            exist.setLastModifiedDate(Instant.now());
//            exist.setLastModifiedBy(SecurityUtils.getUserUid());
//            exist = talentRecruitmentProcessCommissionRepository.saveAndFlush(exist);
            talentRecruitmentProcessCommissionRepository.updateNoteOnly(talentRecruitmentProcessId, note, SecurityUtils.getUserId());
            return toVO(talentRecruitmentProcessId);
        } else {
            return null;
        }
    }

    @Override
    public TalentRecruitmentProcessCommissionVO save(TalentRecruitmentProcessCommissionVO commissionVO) {
        TalentRecruitmentProcessCommission exist = talentRecruitmentProcessCommissionRepository.findByTalentRecruitmentProcessId(commissionVO.getTalentRecruitmentProcessId());
        if (exist != null) {
            // update
            ServiceUtils.myCopyProperties(commissionVO, exist);
            exist.setLastModifiedDate(Instant.now());
            exist.setLastModifiedBy(SecurityUtils.getUserUid());
            talentRecruitmentProcessCommissionRepository.saveAndFlush(exist);
//            talentRecruitmentProcessCommissionRepository.updateLastModifiedDateAndLastModifiedBy(commissionVO.getTalentRecruitmentProcessId(), SecurityUtils.getUserUid());
        } else {
            // create
            talentRecruitmentProcessCommissionRepository.save(TalentRecruitmentProcessCommission.fromTalentRecruitmentProcessCommissionVO(commissionVO));
        }

        kpiUserService.save(commissionVO.getTalentRecruitmentProcessId(), commissionVO.getKpiUsers());
        clientInfoService.save(commissionVO.getTalentRecruitmentProcessId(), commissionVO.getClientInfo());
        return toVO(commissionVO.getTalentRecruitmentProcessId());
    }

    @Override
    @Transactional(readOnly = true)
    public TalentRecruitmentProcessCommissionVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVO(talentRecruitmentProcessId);
    }

    private TalentRecruitmentProcessCommissionVO toVO(Long talentRecruitmentProcessId) {
        return toVO(talentRecruitmentProcessCommissionRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }

    private TalentRecruitmentProcessCommissionVO toVO(TalentRecruitmentProcessCommission commission) {
        if (commission == null) {
            return null;
        }
        TalentRecruitmentProcessCommissionVO result = new TalentRecruitmentProcessCommissionVO();
        ServiceUtils.myCopyProperties(commission, result);
        return result;
    }

}
