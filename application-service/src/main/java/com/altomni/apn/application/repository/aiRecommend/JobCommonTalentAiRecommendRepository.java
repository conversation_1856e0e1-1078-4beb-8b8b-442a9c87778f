package com.altomni.apn.application.repository.aiRecommend;

import com.altomni.apn.application.domain.aiRecommend.JobCommonTalentAiRecommend;
import com.altomni.apn.application.domain.aiRecommend.JobTalentAiRecommend;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the JobCommonTalentAiRecommend entity.
 */
@Repository
public interface JobCommonTalentAiRecommendRepository extends JpaRepository<JobCommonTalentAiRecommend, Long> {

    List<JobCommonTalentAiRecommend> findByTenantIdAndJobIdAndTalentIdIn(Long tenantId, Long jobId, List<String> talentIdList);

    List<JobCommonTalentAiRecommend> findByTenantIdAndJobIdInAndTalentId(Long tenantId, List<Long> jobIdList, String talentId);

    List<JobCommonTalentAiRecommend> findByTenantIdAndJobIdAndTalentId(Long tenantId, Long jobId, String talentId);


}
