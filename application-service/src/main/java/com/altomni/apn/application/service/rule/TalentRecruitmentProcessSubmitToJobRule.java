package com.altomni.apn.application.service.rule;


import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;

import java.util.List;

public interface TalentRecruitmentProcessSubmitToJobRule {

    void preProcessValidate(TalentRecruitmentProcessSubmitToJobVO submitToJobVO);

    void validate(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate, List<TalentRecruitmentProcessKpiUserVO> kpiUsers);

}
