package com.altomni.apn.application.repository;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.application.web.rest.vm.SubstituteTalentVO;
import com.altomni.apn.common.domain.enumeration.company.*;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.finance.service.dto.invoicing.InvoicingApplicationInfoSearchDTO;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.altomni.apn.finance.service.vo.invoicing.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.SQLQuery;
import org.hibernate.transform.Transformers;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class ProcessNativeRepository {

    @Resource
    private EntityManager entityManager;



    /**
     * 查询候补候选人
     *
     * @param jobId
     * @return
     */
    @Transactional(readOnly = true)
    public List<SubstituteTalentVO> selectSubstituteTalentByJobId(Long jobId) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select ta.id as talentId,ta.full_name as talentName,t.talent_recruitment_process_id as relationProcessId from start t\s
                inner join start_failed_warranty sfw on sfw.start_id = t.id
                inner join talent_recruitment_process trp on trp.id = t.talent_recruitment_process_id
                inner join talent ta on ta.id = t.talent_id
                where t.job_id = :jobId and t.status = 15 and sfw.action_plan=0
                and NOT EXISTS (
                         SELECT 1
                         FROM start r
                         LEFT JOIN start_failed_warranty sfw_r ON r.id = sfw_r.start_id
                         WHERE r.substitute_talent_id = t.talent_id
                           AND sfw_r.start_id IS NULL
                     )
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("jobId", jobId);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(SubstituteTalentVO.class));

        return dataQuery.getResultList();
    }


    /**
     * 查询已经入职的人员信息
     *
     * @param talentIds
     * @return
     */
    @Transactional(readOnly = true)
    public List<SubstituteTalentVO> selectStartTalentByTalentId(List<BigInteger> talentIds) {
        entityManager.clear();
        StringBuilder dataSql = new StringBuilder("""
                select distinct talent_id as talentId from start t
                left join talent_recruitment_process_resignation  rpr on rpr.talent_recruitment_process_id = t.talent_recruitment_process_id
                 where t.status =0 and talent_id in (:talentIds) and rpr.id is null
                """);

        Query dataQuery = entityManager.createNativeQuery(dataSql.toString());

        dataQuery.setParameter("talentIds", talentIds);
        dataQuery.unwrap(SQLQuery.class).setResultTransformer(Transformers.aliasToBean(SubstituteTalentVO.class));

        return dataQuery.getResultList();
    }
}
