package com.altomni.apn.application.service.mail;

import com.altomni.apn.common.dto.mail.MailVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "common-service")
public interface MailService {

    @PostMapping("/common/api/v3/campaign/send_html_mail")
    ResponseEntity<Long> sendHtmlMail(@RequestBody MailVM mailVM);

}
