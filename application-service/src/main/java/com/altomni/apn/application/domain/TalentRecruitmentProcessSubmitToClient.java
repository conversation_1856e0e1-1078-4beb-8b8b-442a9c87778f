package com.altomni.apn.application.domain;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToClientVO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A TalentRecruitmentProcessSubmitToClient.
 */
@Entity
@Table(name = "talent_recruitment_process_submit_to_client")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessSubmitToClient extends AbstractPermissionNoteAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4005303296711625393L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "submit_time")
    private Instant submitTime;

    @Column(name = "note")
    private String note;

    @Column(name = "email_tracking_number")
    private String emailTrackingNumber;

    public static TalentRecruitmentProcessSubmitToClient fromVO(TalentRecruitmentProcessSubmitToClientVO submitToClientVO) {
        TalentRecruitmentProcessSubmitToClient result = new TalentRecruitmentProcessSubmitToClient();
        ServiceUtils.myCopyProperties(submitToClientVO, result);
        return result;
    }

    @Override
    public String getNoteValue() {
        return note;
    }
}
