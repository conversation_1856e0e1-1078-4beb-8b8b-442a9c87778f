package com.altomni.apn.application.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.application.config.env.RaterAIRecommendMQProperties;
import com.altomni.apn.application.config.env.TalentOnboardMQProperties;
import com.altomni.apn.application.config.env.TalentProfileMQProperties;
import com.altomni.apn.application.service.transactionrecord.MqTransactionRecordService;
import com.altomni.apn.common.domain.transactionrecord.CommonMqTransactionRecord;
import com.altomni.apn.common.enumeration.enums.MqTranRecordBusTypeEnums;
import com.altomni.apn.common.enumeration.enums.MqTranRecordStatusEnums;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigInteger;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RabbitMqConfig {

    @Resource
    TalentOnboardMQProperties talentOnboardMQProperties;

    @Resource
    private TalentProfileMQProperties talentProfileMQProperties;

    @Resource
    private RaterAIRecommendMQProperties raterAIRecommendMQProperties;

    @Autowired
    private MqTransactionRecordService mqTransactionRecordService;

    private static RabbitMqConfig rabbitMqConfig;

    @PostConstruct
    public void init() {
        rabbitMqConfig = this;
        rabbitMqConfig.mqTransactionRecordService = this.mqTransactionRecordService;
    }

    private static final String AUTHORIZATION_HEADER = "Authorization";

    /****** start talent onboard tx mq config  *******/
    @Bean(name = "talentOnboardMQConnectionFactory")
    @Primary
    public ConnectionFactory talentOnboardMQConnectionFactory() {
        return talentOnboardMQConnectionFactory(talentOnboardMQProperties.getHost(), talentOnboardMQProperties.getPort(), talentOnboardMQProperties.getVirtualHost(), talentOnboardMQProperties.getUserName(), talentOnboardMQProperties.getPassword());
    }

    @Bean(name = "talentKeyConnectionFactory")
    public ConnectionFactory talentKeyConnectionFactory() {
        return talentKeyConnectionFactory(talentProfileMQProperties.getHost(), talentProfileMQProperties.getPort(), talentProfileMQProperties.getVirtualHost(), talentProfileMQProperties.getUsername(), talentProfileMQProperties.getPassword());
    }

    @Bean(name = "raterRaterAIRecommendConnectionFactory")
    public ConnectionFactory raterRaterAIRecommendConnectionFactory() {
        return talentKeyConnectionFactory(raterAIRecommendMQProperties.getHost(), raterAIRecommendMQProperties.getPort(), raterAIRecommendMQProperties.getVirtualHost(), raterAIRecommendMQProperties.getUserName(), raterAIRecommendMQProperties.getPassword());
    }

    @Bean(name = "talentKeyFactory")
    public SimpleRabbitListenerContainerFactory talentKeyFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("talentKeyConnectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(10);
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "raterRaterAIRecommendFactory")
    public SimpleRabbitListenerContainerFactory raterRaterAIRecommendFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("raterRaterAIRecommendConnectionFactory") ConnectionFactory connectionFactory) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(10);
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    public CachingConnectionFactory talentKeyConnectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPublisherConfirms(true);
        return connectionFactory;
    }


    public CachingConnectionFactory talentOnboardMQConnectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        connectionFactory.setPublisherReturns(true);
        connectionFactory.setPublisherConfirms(true);
        return connectionFactory;
    }

    @Bean(name = "talentOnboardMqFactory")
    public SimpleRabbitListenerContainerFactory talentOnboardMqFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("talentOnboardMQConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(1);
        configurer.configure(factory, connectionFactory);
        return factory;
    }


    @Bean(name = "talentOnboardMQRabbitTemplate")
    public RabbitTemplate talentOnboardMQRabbitTemplate(
            @Qualifier("talentOnboardMQConnectionFactory") ConnectionFactory connectionFactory
    ) {
        RabbitTemplate talentOnboardMQRabbitTemplate = new RabbitTemplate(connectionFactory);
        talentOnboardMQRabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
        //设置开启Mandatory,才能触发回调函数,无论消息推送结果怎么样都强制调用回调函数
        talentOnboardMQRabbitTemplate.setMandatory(true);

        //消息发送成功的回调
        talentOnboardMQRabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            log.info("talent onboard create start rabbit, correlationData:{}, ack:{}, cause:{}", correlationData, ack, cause);
            if (ack) {
                if (StringUtils.isNotBlank(correlationData.getId())) {
                    CommonMqTransactionRecord record = rabbitMqConfig.mqTransactionRecordService.findById(BigInteger.valueOf(Long.valueOf(correlationData.getId())));
                    try {
                        if (null == record) {
                            Thread.sleep(3000);
                        }
                        record = rabbitMqConfig.mqTransactionRecordService.findById(BigInteger.valueOf(Long.valueOf(correlationData.getId())));
                    } catch (Exception e) {

                    }

                    if (record.getSendCount() > 1) {
                        rabbitMqConfig.mqTransactionRecordService.updateStatusById(record.getId().longValue(), MqTranRecordStatusEnums.SEND_SUCCESS.toDbValue());
                        log.info("talent onboard rabbit, modify mq transaction record status is success");
                    } else if (record.getSendCount() >= 1 && record.getSendStatus().equals(MqTranRecordStatusEnums.PENDING.toDbValue())) {
                        rabbitMqConfig.mqTransactionRecordService.updateStatusById(record.getId().longValue(), MqTranRecordStatusEnums.SEND_SUCCESS.toDbValue());
                        log.info("talent onboard rabbit, modify mq transaction record status is success");
                    }
                }
                log.info("talent onboard create start rabbit, send message to rabbit success");
            } else {
                log.error("talent onboard create start rabbit, send message to rabbit error, error message = [{}], Data = {}", cause, correlationData);
            }
        });

        //发生异常时的消息返回提醒
        talentOnboardMQRabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
            log.error("talent onboard create start rabbit, send message to rabbit error, return call back message = {}, reply code = {}, reply text = {}", message, replyCode, replyText);
            try {
                Thread.sleep(3000);
                JSONObject json = JSON.parseObject(new String(message.getBody()));
                json.remove(AUTHORIZATION_HEADER);
                StartDTO startDTO = JSONObject.parseObject(json.toJSONString(), StartDTO.class);
                rabbitMqConfig.mqTransactionRecordService.updateStatusById(json.getLong("mqRecordId"), MqTranRecordStatusEnums.NOT_ARRIVE_QUEUE.toDbValue());
                log.info("talent onboard create start rabbit, modify mq transaction record status is {}, recordId:{}", MqTranRecordStatusEnums.NOT_ARRIVE_QUEUE.toDbValue(), startDTO.getTalentId());
                rabbitMqConfig.mqTransactionRecordService.sendExceptionByLark(String.format(" talent onboard create start rabbit Sending mq tx message abnormal, tx type is ' %s ' , replyText: %s ",
                        MqTranRecordBusTypeEnums.fromDbValue(json.getInteger("mqRecordType")).getDesc(),
                        replyText));
            } catch (Exception e) {
                log.error("talent onboard create start rabbit, {}", e.getMessage());
            }
        });
        return talentOnboardMQRabbitTemplate;
    }
}