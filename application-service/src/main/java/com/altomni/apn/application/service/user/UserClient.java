package com.altomni.apn.application.service.user;

import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@Component
@FeignClient(value = "user-service")
public interface UserClient {

    @PostMapping("/user/api/v3/users/all-brief-by-ids/including-inactive")
    ResponseEntity<List<UserBriefDTO>> getAllBriefUsersByIds(@RequestBody List<Long> ids);

    @GetMapping("/user/api/v3/users/find-by-id")
    ResponseEntity<UserBriefDTO> findBriefUserById(@RequestParam("id") Long id);

    @GetMapping("/user/api/v3/users/common/tenant-param/{paramKey}/value")
    ResponseEntity<Integer> getTenantParamValue(@PathVariable("paramKey") String paramKey);

    @GetMapping("/user/api/v3/setting")
    ResponseEntity<TenantConfigDTO> getSettingConfig(@RequestParam(value = "configCode") TenantConfigCode configCode);

    @PostMapping("/user/api/v3/users/get-timezone-by-ids")
    ResponseEntity<List<UserTimeZoneVO>> getTimezoneListByUserIdList(@RequestBody List<Long> ids);

    @GetMapping("/user/api/v3/users/timezone")
    ResponseEntity<UserTimeZoneVO> getTimezoneByUserId();

    @DeleteMapping("/user/api/v3/jobs/preferences/recently-used-recruitment-process/{recruitmentProcessId}")
    ResponseEntity<Void> removeUserPreferenceRecruitmentProcess(@PathVariable("recruitmentProcessId") Long recruitmentProcessId);

    @GetMapping("/user/api/v3/users/find-by-email")
    ResponseEntity<UserBriefDTO> findByEmail(@RequestParam("email") String email);

    /**
     * 获取所有该用户的team leaders
     */
    @GetMapping("/user/api/v3/permissions/teams/user/{userId}/upper/team-leaders")
    ResponseEntity<Set<Long>> getUpperTeamLeadersByUserId(@PathVariable("userId") Long userId);

}
