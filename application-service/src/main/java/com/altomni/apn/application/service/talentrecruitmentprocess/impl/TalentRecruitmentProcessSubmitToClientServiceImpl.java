package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessNode;
import com.altomni.apn.application.domain.TalentRecruitmentProcessSubmitToClient;
import com.altomni.apn.application.repository.TalentRecruitmentProcessNodeRepository;
import com.altomni.apn.application.repository.TalentRecruitmentProcessSubmitToClientRepository;
import com.altomni.apn.application.service.rule.TalentRecruitmentProcessSubmitToClientRule;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToClientService;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToClientVO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.Instant;

/**
 * Service Implementation for managing TalentRecruitmentProcessSubmitToClient.
 */
@Service
@Transactional
public class TalentRecruitmentProcessSubmitToClientServiceImpl implements TalentRecruitmentProcessSubmitToClientService {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessSubmitToClientServiceImpl.class);

    @Resource
    private TalentRecruitmentProcessSubmitToClientRepository submitToClientRepository;
    @Resource
    private TalentRecruitmentProcessKpiUserService kpiUserService;
    @Resource
    private TalentRecruitmentProcessIpgAgreedPayRateService agreedPayRateService;
    @Resource
    private TalentRecruitmentProcessSubmitToClientRule submitToClientRule;
    @Resource
    private TalentRecruitmentProcessNodeRepository talentRecruitmentProcessNodeRepository;

    @Override
    public TalentRecruitmentProcessSubmitToClientVO updateNoteOnly(TalentRecruitmentProcessSubmitToClientVO submitToClientVO) {
        return this.updateNoteOnly(submitToClientVO.getTalentRecruitmentProcessId(), submitToClientVO.getNote());
    }

    @Override
    public TalentRecruitmentProcessSubmitToClientVO updateNoteOnly(Long talentRecruitmentProcessId, String note) {
        TalentRecruitmentProcessSubmitToClient exist = submitToClientRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (exist != null) {
//            exist.setNote(note);
//            exist.setLastModifiedDate(Instant.now());
//            exist.setLastModifiedBy(SecurityUtils.getUserUid());
            submitToClientRepository.updateNoteOnly(talentRecruitmentProcessId, note, SecurityUtils.getUserId());
            return toVO(talentRecruitmentProcessId);
        } else {
            return null;
        }
    }

    @Override
    public TalentRecruitmentProcessSubmitToClientVO save(TalentRecruitmentProcessSubmitToClientVO submitToClientVO) {
        StopWatch stopWatch = new StopWatch("[RecruitmentProcess: submit to client (5. save)] talent recruitment process id: " + submitToClientVO.getTalentRecruitmentProcessId());

        stopWatch.start("[5.1] Submit to client rule");
        submitToClientRule.validate(submitToClientVO);
        stopWatch.stop();

        stopWatch.start("[5.2] Find existing submit to client entity");
        TalentRecruitmentProcessSubmitToClient exist = submitToClientRepository.findByTalentRecruitmentProcessId(submitToClientVO.getTalentRecruitmentProcessId());
        stopWatch.stop();

        if (exist != null) {
            stopWatch.start("[5.3.1] Existing submit to client entity, update and save entity");
            ServiceUtils.myCopyProperties(submitToClientVO, exist);
            exist.setLastModifiedDate(Instant.now());
            exist.setLastModifiedBy(SecurityUtils.getUserUid());
            submitToClientRepository.saveAndFlush(exist);
            stopWatch.stop();

            stopWatch.start("[5.3.2] Save agreed pay rate");
            agreedPayRateService.save(submitToClientVO.getTalentRecruitmentProcessId(), submitToClientVO.getAgreedPayRate());
            stopWatch.stop();

            stopWatch.start("[5.3.3] Find active node");
            TalentRecruitmentProcessNode activeNode = talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeStatus(submitToClientVO.getTalentRecruitmentProcessId(), NodeStatus.ACTIVE);
            stopWatch.stop();
//            if (Constants.TENANT_IPG.equals(SecurityUtils.getTenantId()) ||
//                    (!Constants.TENANT_IPG.equals(SecurityUtils.getTenantId())
//                            && NodeType.OFFER.toDbValue() >= activeNode.getNodeType().toDbValue())) {
            if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId()) ||
                    (!ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())
                            && NodeType.OFFER.toDbValue() >= activeNode.getNodeType().toDbValue())) {
                stopWatch.start("[5.3.4] Save kpi users");
                kpiUserService.save(submitToClientVO.getTalentRecruitmentProcessId(), submitToClientVO.getKpiUsers());
                stopWatch.stop();
            }
//            submitToClientRepository.updateLastModifiedDateAndLastModifiedBy(submitToClientVO.getTalentRecruitmentProcessId(), SecurityUtils.getUserUid());
            stopWatch.start("[5.3.5] Construct res DTO");
            TalentRecruitmentProcessSubmitToClientVO res = toVO(submitToClientVO.getTalentRecruitmentProcessId());
            stopWatch.stop();

            log.info("[Application timelapse result] submitToClient(5. save): {}", stopWatch.prettyPrint());
            return res;
        }

        stopWatch.start("[5.4] Create submit to client entity");
        TalentRecruitmentProcessSubmitToClient create = TalentRecruitmentProcessSubmitToClient.fromVO(submitToClientVO);
        create.setTalentRecruitmentProcessId(submitToClientVO.getTalentRecruitmentProcessId());
        stopWatch.stop();

        stopWatch.start("[5.5] Save submit to client entity");
        submitToClientRepository.save(create);
        stopWatch.stop();

        stopWatch.start("[5.6] Save agreed pay rate");
        agreedPayRateService.save(submitToClientVO.getTalentRecruitmentProcessId(), submitToClientVO.getAgreedPayRate());
        stopWatch.stop();

        stopWatch.start("[5.7] Save kpi users");
        kpiUserService.save(submitToClientVO.getTalentRecruitmentProcessId(), submitToClientVO.getKpiUsers());
        stopWatch.stop();

        TalentRecruitmentProcessSubmitToClientVO res = toVO(submitToClientVO.getTalentRecruitmentProcessId());

        log.info("[Application timelapse result] submitToClient(5. save): {}", stopWatch.prettyPrint());
        return res;
    }

    @Override
    @Transactional(readOnly = true)
    public TalentRecruitmentProcessSubmitToClientVO findOneByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVO(talentRecruitmentProcessId);
    }


    private TalentRecruitmentProcessSubmitToClientVO toVO(TalentRecruitmentProcessSubmitToClient submitToClient) {
        if (submitToClient == null) {
            return null;
        }
        TalentRecruitmentProcessSubmitToClientVO result = new TalentRecruitmentProcessSubmitToClientVO();
        ServiceUtils.myCopyProperties(submitToClient, result);
        return result;
    }

    private TalentRecruitmentProcessSubmitToClientVO toVO(Long talentRecruitmentProcessId) {
        return toVO(submitToClientRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }
}
