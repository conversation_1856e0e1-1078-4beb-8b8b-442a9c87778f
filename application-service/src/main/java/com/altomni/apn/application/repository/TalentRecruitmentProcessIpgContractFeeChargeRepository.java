package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgContractFeeCharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the TalentRecruitmentProcessIpgOfferAcceptContractFeeCharge entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessIpgContractFeeChargeRepository extends JpaRepository<TalentRecruitmentProcessIpgContractFeeCharge, Long> {

    TalentRecruitmentProcessIpgContractFeeCharge findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessIpgContractFeeCharge> findAllByTalentRecruitmentProcessIdIn(List<Long> recruitmentProcessIds);
}
