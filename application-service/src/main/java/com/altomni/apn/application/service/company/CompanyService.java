package com.altomni.apn.application.service.company;

import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;
import com.altomni.apn.company.service.dto.CompanyDTO;

import java.util.List;

public interface CompanyService {

    CompanyDTO getCompany(Long companyId);

    List<SkipSubmitToAmCompanyUser>  getAllSkipSubmitToAmCompanyUsers(Long companyId);

    List<Long> getAllAmIdsByCompanyId(Long companyId);
}
