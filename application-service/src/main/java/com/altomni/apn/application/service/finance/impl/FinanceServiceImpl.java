package com.altomni.apn.application.service.finance.impl;

import com.altomni.apn.application.service.finance.FinanceClient;
import com.altomni.apn.application.service.finance.FinanceService;
import com.altomni.apn.common.dto.application.dashboard.MyCandidate;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.start.StartContractRate;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class FinanceServiceImpl implements FinanceService {

    @Resource
    private FinanceClient financeClient;

    @Override
    public StartDTO createStart(StartDTO start) {
        ResponseEntity<StartDTO> response = financeClient.createStart(start);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<StartDTO> getActiveStartByTalentId(Long talentId) {
        ResponseEntity<List<StartDTO>> response = financeClient.getStartByTalentId(talentId);
        List<StartDTO> starts = response != null ? response.getBody() : null;
        return CollectionUtils.isNotEmpty(starts) ?
                starts.stream().filter(startDTO -> StartStatus.ACTIVE.equals(startDTO.getStatus())).collect(Collectors.toList()) : null;
    }

    @Override
    public BigDecimal getTotalBillAmount(StartContractRate startContractRate) {
        ResponseEntity<BigDecimal> response = financeClient.getTotalBillAmount(startContractRate);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<MyCandidate> getTotalBillAmountByTalentRecruitmentProcessId(List<Long> talentRecruitmentProcessId) {
        ResponseEntity<List<MyCandidate>> response = financeClient.getTotalBillAmountByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        return response != null ? response.getBody() : null;
    }
}
