package com.altomni.apn.application.web.rest;


import com.altomni.apn.application.service.pipeline.ViewPipelineService;
import com.altomni.apn.common.dto.application.pipeline.MyPipelineSearchParam;
import com.altomni.apn.common.dto.application.pipeline.ViewPipelineResult;
import com.altomni.apn.common.utils.PaginationUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.company.service.common.CommonClient;
import com.altomni.apn.company.service.common.RefreshApplicationStopStatisticDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


@RestController
@RequestMapping("/api/v3")
public class ViewPipelineResource {

    private final Logger log = LoggerFactory.getLogger(ViewPipelineResource.class);

    @Resource
    private ViewPipelineService viewPipelineService;

    @Resource
    private CommonClient commonClient;

    @PostMapping("/my-pipelines")
    public ResponseEntity<ViewPipelineResult> getAllMyPipelines(@RequestBody MyPipelineSearchParam requestParam, Pageable pageable) {
        log.info("[APN: MyPipeline @{}] REST request to get all my pipelines : {}", SecurityUtils.getUserId(), requestParam);
        RefreshApplicationStopStatisticDTO dto = new RefreshApplicationStopStatisticDTO();
        dto.setUserIds(requestParam.getUserIds());
        commonClient.refreshApplicationStopStatistic(dto);
        Page<Object[]> page = viewPipelineService.findAll(requestParam, pageable);
        ViewPipelineResult result = viewPipelineService.toViewPipelineResult(page.getContent(), requestParam);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(page, "/all-my-pipelines");
        return new ResponseEntity<>(result, headers, HttpStatus.OK);
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }
}
