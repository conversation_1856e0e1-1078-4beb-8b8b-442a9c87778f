package com.altomni.apn.application.service.finance;

import com.altomni.apn.common.dto.application.dashboard.MyCandidate;
import com.altomni.apn.finance.domain.enumeration.start.StartStatus;
import com.altomni.apn.finance.domain.start.StartContractRate;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.finance.service.vo.invoice.InvoiceDetailInfoVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;

@Component
@FeignClient(value = "finance-service")
public interface FinanceClient {

    @PostMapping("/finance/api/v3/starts")
    ResponseEntity<StartDTO> createStart(@Valid @RequestBody StartDTO start);

    @GetMapping("/finance/api/v3/starts/talentId/{talentId}")
    ResponseEntity<List<StartDTO>> getStartByTalentId(@PathVariable("talentId") Long talentId);

    @GetMapping("/finance/api/v3/starts/{talentId}/{talentRecruitmentProcessId}/{status}")
    ResponseEntity<StartDTO> getStartByTalentIdAndTalentRecruitmentProcessId(@PathVariable("talentId") Long talentId, @PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId,@PathVariable("status") com.altomni.apn.job.domain.enumeration.start.StartStatus status);

    @PostMapping("/finance/api/v3/start-contract-rates/get-total-bill-amount")
    ResponseEntity<BigDecimal> getTotalBillAmount(@RequestBody StartContractRate startContractRate);

    @PostMapping("/finance/api/v3/starts/total-bill-amount-by-talentRecruitmentProcess")
    ResponseEntity<List<MyCandidate>> getTotalBillAmountByTalentRecruitmentProcessId(@RequestBody List<Long> talentRecruitmentProcessIds);

    @PutMapping("/finance/api/v3/starts/talentRecruitmentProcessId/{talentRecruitmentProcessId}/eliminate")
    ResponseEntity<Void> updateStartStatusWithEliminatedCandidate(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @PutMapping("/finance/api/v3/starts/talentRecruitmentProcessId/{talentRecruitmentProcessId}/cancel-eliminate")
    ResponseEntity<Void> updateStartStatusWithCancelEliminatedCandidate(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @GetMapping("/finance/api/v3/starts/talentId/{talentId}/status/{status}")
    ResponseEntity<StartDTO> findStartByTalentIdAndStatus(@PathVariable("talentId") Long talentId, @PathVariable("status") StartStatus status);

    @GetMapping("/finance/api/v3/starts/{talentRecruitmentProcessId}/{status}")
    ResponseEntity<StartDTO> getStartByTalentRecruitmentProcessIdAndStatus(@PathVariable("talentRecruitmentProcessId") Long talentRecruitmentProcessId,@PathVariable("status") com.altomni.apn.job.domain.enumeration.start.StartStatus status);

    @GetMapping("/finance/api/v3/invoice/type/{id}/{invoiceId}")
    ResponseEntity<InvoiceDetailInfoVO> getInvoiceInfoByTypeId(@PathVariable("id") Long id, @PathVariable("invoiceId") Long invoiceId);
}
