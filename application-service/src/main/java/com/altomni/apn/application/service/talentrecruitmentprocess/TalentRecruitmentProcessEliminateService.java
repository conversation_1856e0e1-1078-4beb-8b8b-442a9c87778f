package com.altomni.apn.application.service.talentrecruitmentprocess;


import com.altomni.apn.application.dto.EliminateDTO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessEliminateVO;

/**
 * Service Interface for managing TalentRecruitmentProcessEliminateService.
 */
public interface TalentRecruitmentProcessEliminateService {

    TalentRecruitmentProcessEliminateVO save(EliminateDTO eliminate);

    TalentRecruitmentProcessEliminateVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    void delete(Long talentRecruitmentProcessId);
}
