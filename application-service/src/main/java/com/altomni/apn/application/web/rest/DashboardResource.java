package com.altomni.apn.application.web.rest;

import com.altomni.apn.application.service.dashboard.DashboardService;
import com.altomni.apn.application.web.rest.vm.KpiTopVo;
import com.altomni.apn.application.web.rest.vm.MyInvoiceVO;
import com.altomni.apn.application.web.rest.vm.TeamPerformanceVO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.web.rest.vm.MyJobVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;


@RestController
@RequestMapping("/api/v3")
public class DashboardResource {

    private final Logger log = LoggerFactory.getLogger(DashboardResource.class);

    @Resource
    private DashboardService dashboardService;

    @GetMapping("/dashboard/my-application-candidates")
    public ResponseEntity<LinkedHashMap<String, Integer>> myApplicationCandidates(@RequestParam("startTime") Instant startTime, @RequestParam("endTime") Instant endTime, @RequestParam("userIdList") List<Long> userIdList) {
        log.info("[APN: MyApplicationCandidates @{}] REST request to get myApplicationCandidates", SecurityUtils.getUserId());
        return new ResponseEntity<>(dashboardService.myApplicationCandidates(startTime, endTime,userIdList), HttpStatus.OK);
    }

    @GetMapping("/dashboard/my-application-candidates/with-application-ids")
    public ResponseEntity<LinkedHashMap<String, Integer>> myApplicationCandidatesWithApplicationIds(@RequestParam("startTime") Instant startTime, @RequestParam("endTime") Instant endTime, @RequestParam("userIdList") List<Long> userIdList, @RequestParam("applicationIds") List<Long> applicationIds) {
        log.info("[APN: MyApplicationCandidates @{}] REST request to get myApplicationCandidates", SecurityUtils.getUserId());
        return new ResponseEntity<>(dashboardService.myApplicationCandidates(startTime, endTime,userIdList,applicationIds), HttpStatus.OK);
    }

    @GetMapping("/dashboard/my-invoices")
    public ResponseEntity<MyInvoiceVO> myInvoices(@RequestParam("startTime") Instant startTime, @RequestParam("endTime") Instant endTime, @RequestParam("userIdList") List<Long> userIdList) {
        log.info("[APN: myInvoices @{}] REST request to get myInvoices", SecurityUtils.getUserId());
        return new ResponseEntity<>(dashboardService.myInvoices(startTime, endTime,userIdList), HttpStatus.OK);
    }

    @GetMapping("/dashboard/kpi-top")
    public ResponseEntity<List<KpiTopVo>> kpiTop(@RequestParam("top") Integer top, @RequestParam("currency") Integer currency) {
        log.info("[APN: kpiTop @{}] REST request to get kpiTop", SecurityUtils.getUserId());
        return new ResponseEntity<>(dashboardService.kpiTop(top, currency), HttpStatus.OK);
    }

    @GetMapping("/dashboard/my-performances")
    public ResponseEntity<BigDecimal> myPerformances(@RequestParam("currency") Integer currency) {
        log.info("[APN: kpiTop @{}] REST request to get my performances, currency = {}", SecurityUtils.getUserId(), currency);
        return new ResponseEntity<>(dashboardService.myPerformances(currency), HttpStatus.OK);
    }

    @GetMapping("/dashboard/team-performances")
    public ResponseEntity<TeamPerformanceVO> teamPerformances(@RequestParam("startTime") Instant startTime, @RequestParam("endTime") Instant endTime, @RequestParam("currency") Integer currency, @RequestParam("userIdList") List<Long> userIdList) {
        log.info("[APN: kpiTop @{}] REST request to get team performances ", SecurityUtils.getUserId());
        return new ResponseEntity<>(dashboardService.teamPerformances(startTime, endTime, currency,userIdList), HttpStatus.OK);
    }

    @GetMapping("/dashboard/my-jobs")
    public ResponseEntity<MyJobVo> myJobs(@RequestParam("startTime") Instant startTime, @RequestParam("endTime") Instant endTime, @RequestParam("userIdList") List<Long> userIdList) {
        log.info("[APN: myJobs @{}] REST request to get myJobs", SecurityUtils.getUserId());
        return ResponseEntity.ok(dashboardService.myJobs(startTime, endTime,userIdList));
    }

}
