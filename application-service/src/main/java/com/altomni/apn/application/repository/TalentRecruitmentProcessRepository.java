package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.domain.TalentRecruitmentProcessInterview;
import com.altomni.apn.application.web.rest.vm.ApplicationBriefInfoVM;
import com.altomni.apn.application.web.rest.vm.ApplicationIdAndStatusVO;
import com.altomni.apn.application.web.rest.vm.InProcessApplicationBriefVM;
import com.altomni.apn.application.web.rest.vm.InProcessApplicationCountByJobVM;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Set;


/**
 * Spring Data  repository for the TalentRecruitmentProcess entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessRepository extends JpaRepository<TalentRecruitmentProcess, Long> {

    TalentRecruitmentProcess findByTalentIdAndJobId(Long talentId, Long jobId);

    List<TalentRecruitmentProcess> findAllByTalentId(Long talentId);

    List<TalentRecruitmentProcess> findAllByJobId(Long jobId);

    List<TalentRecruitmentProcess> findByJobIdAndTalentIdIn(Long jobId,List<Long> talentIdList);

    List<TalentRecruitmentProcess> findByTalentIdAndJobIdIn(Long talentId,List<Long> jobIdList);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcess t SET t.lastModifiedDate = current_timestamp, t.lastModifiedBy = ?2, t.note = ?3  WHERE t.id = ?1")
    void updateLastModifiedDateAndLastModifiedBy(Long talentRecruitmentProcessId, String updatedBy, String note);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcess t SET t.lastModifiedDate = current_timestamp, t.lastModifiedBy = ?2 WHERE t.id = ?1")
    void updateLastModifiedDateAndLastModifiedBy(Long talentRecruitmentProcessId, String updatedBy);


    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcess t SET t.lastModifiedDate = current_timestamp WHERE t.id = ?1")
    void updateLastModifiedDate(Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE talent_recruitment_process t SET t.ai_score = ?2 WHERE t.id = ?1", nativeQuery = true)
    void updateAiSourceById(Long id, Short aiScore);

    @Query(value = "select t.* from talent_recruitment_process t where t.job_id = ?1 order by t.created_date desc limit 1", nativeQuery = true)
    TalentRecruitmentProcess findLatestByJobId(Long jobId);


    @Query(value = "select count(t.id) from talent_recruitment_process t left join talent_recruitment_process_node tn " +
            "    on t.id = tn.talent_recruitment_process_id " +
            "    where t.job_id = ?1 and tn.node_status = 1  and tn.node_type in (41,60)", nativeQuery = true)
    Integer countByJobId(Long jobId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcess t SET t.note = (select note from TalentRecruitmentProcessSubmitToJob where talentRecruitmentProcessId=:talentRecruitmentProcessId) WHERE t.id =:talentRecruitmentProcessId")
    void rollbackNoteFromSubmitToJob(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query(value = "select note from TalentRecruitmentProcessSubmitToJob where talentRecruitmentProcessId=:talentRecruitmentProcessId")
    String getNoteFromSubmitToJob(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcess t SET t.note = (select note from TalentRecruitmentProcessSubmitToClient where talentRecruitmentProcessId=:talentRecruitmentProcessId) WHERE t.id =:talentRecruitmentProcessId")
    void rollbackNoteFromSubmitToClient(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query(value = "SELECT note FROM TalentRecruitmentProcessSubmitToClient WHERE talentRecruitmentProcessId=:talentRecruitmentProcessId")
    String getNoteFromSubmitToClient(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcess t SET t.note = (select note from TalentRecruitmentProcessInterview where talentRecruitmentProcessId=:talentRecruitmentProcessId) WHERE t.id =:talentRecruitmentProcessId")
    void rollbackNoteFromInterview(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query(value = "SELECT note FROM talent_recruitment_process_interview i WHERE i.talent_recruitment_process_id = :talentRecruitmentProcessId ORDER BY progress DESC, created_date DESC LIMIT 1", nativeQuery = true)
    String getNoteFromInterview(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcess t SET t.note = (select note from TalentRecruitmentProcessOffer where talentRecruitmentProcessId=:talentRecruitmentProcessId) WHERE t.id =:talentRecruitmentProcessId")
    void rollbackNoteFromOffer(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query(value = "SELECT note FROM TalentRecruitmentProcessOffer WHERE talentRecruitmentProcessId=:talentRecruitmentProcessId")
    String getNoteFromOffer(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcess t SET t.note = (select note from TalentRecruitmentProcessIpgOfferAccept where talentRecruitmentProcessId=:talentRecruitmentProcessId) WHERE t.id =:talentRecruitmentProcessId")
    void rollbackNoteFromOfferAccepted(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query(value = "SELECT note FROM TalentRecruitmentProcessIpgOfferAccept WHERE talentRecruitmentProcessId=:talentRecruitmentProcessId")
    String getNoteFromOfferAccepted(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcess t SET t.note = (select note from TalentRecruitmentProcessOnboard where talentRecruitmentProcessId=:talentRecruitmentProcessId) WHERE t.id =:talentRecruitmentProcessId")
    void rollbackNoteFromOnBoard(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query(value = "SELECT note FROM TalentRecruitmentProcessOnboard WHERE talentRecruitmentProcessId=:talentRecruitmentProcessId")
    String getNoteFromOnBoard(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    @Query(value = """
            select count(1) from talent_recruitment_process trp 
             left join recruitment_process rp on rp.id = trp.recruitment_process_id  
             left join talent_recruitment_process_node trpn on trp.id = trpn.talent_recruitment_process_id  
             where trp.talent_id = ?1 and trpn.node_type >= ?2 and rp.job_type <> ?3 and trpn.node_status in (?4)
            """, nativeQuery = true)
    Integer countApplicationByTalentId(Long talentId, Integer nodeType, Integer jobType, List<Integer> nodeStatusList);

    @Query(value = "select count(1) from talent_recruitment_process trp " +
            "left join talent_recruitment_process_eliminate elim on trp.id = elim.talent_recruitment_process_id " +
            "left join talent_recruitment_process_onboard onboard on trp.id = onboard.talent_recruitment_process_id " +
            "where trp.job_id=:jobId and elim.id is null and onboard.id is null", nativeQuery = true)
    Integer countUnfinishedApplicationsForJob(@Param("jobId") Long jobId);

    @Query(value = "select count(1) from talent_recruitment_process trp " +
            "left join talent_recruitment_process_eliminate elim on trp.id = elim.talent_recruitment_process_id " +
            "left join talent_recruitment_process_onboard onboard on trp.id = onboard.talent_recruitment_process_id " +
            "where trp.id in :applicationIds and elim.id is null and onboard.id is null", nativeQuery = true)
    Integer countUnfinishedApplicationsByIds(@Param("applicationIds") List<Long> applicationIds);

    @Query(value = "select new com.altomni.apn.application.web.rest.vm.InProcessApplicationBriefVM(trp.id, trp.talentId, trp.jobId, t.fullName, j.title) from TalentRecruitmentProcess trp " +
            "left join TalentRecruitmentProcessEliminate elim on trp.id = elim.talentRecruitmentProcessId " +
            "left join TalentRecruitmentProcessOnboard onboard on trp.id = onboard.talentRecruitmentProcessId " +
            "left join TalentV3 t on trp.talentId = t.id " +
            "left join JobV3 j on trp.jobId = j.id " +
            "where trp.id in :applicationIds and elim.id is null and onboard.id is null")
    List<InProcessApplicationBriefVM> getUnfinishedApplicationsByIds(@Param("applicationIds") List<Long> applicationIds);

    @Query(value = "select new com.altomni.apn.application.web.rest.vm.ApplicationBriefInfoVM(trp.id, node.nodeType, node.nodeStatus, trp.talentId, trp.jobId, t.fullName, j.title) from TalentRecruitmentProcess trp " +
            "left join TalentRecruitmentProcessNode node on trp.id = node.talentRecruitmentProcessId and node.nodeStatus in (com.altomni.apn.common.domain.enumeration.application.NodeStatus.ACTIVE, com.altomni.apn.common.domain.enumeration.application.NodeStatus.ELIMINATED) " +
            "left join TalentV3 t on trp.talentId = t.id " +
            "left join JobV3 j on trp.jobId = j.id " +
            "where trp.id in :applicationIds ")
    List<ApplicationBriefInfoVM> getApplicationsBriefInoByIds(@Param("applicationIds") List<Long> applicationIds);

    @Query(value = "select new com.altomni.apn.application.web.rest.vm.InProcessApplicationCountByJobVM(trp.jobId, count(trp.id)) from TalentRecruitmentProcess trp " +
            "left join TalentRecruitmentProcessEliminate elim on trp.id = elim.talentRecruitmentProcessId " +
            "left join TalentRecruitmentProcessOnboard onboard on trp.id = onboard.talentRecruitmentProcessId " +
            "where trp.jobId in :jobIds and elim.id is null and onboard.id is null " +
            "group by trp.jobId")
    List<InProcessApplicationCountByJobVM> countUnfinishedApplicationsForJobs(@Param("jobIds") List<Long> jobIds);

    @Query(value = "select trpstj.id as id, 10 as 'node_type', trpstj.recommend_comments as 'note', CONCAT(u.first_name, ' ', u.last_name) as 'last_modified_by', trpstj.note_last_modified_date as 'note_last_modified_date', u.id as 'note_last_modified_by_user_id', trpstj.created_date as 'created_date', CONCAT(u2.first_name, ' ', u2.last_name) as 'created_by' from talent_recruitment_process_submit_to_job trpstj left join user u on trpstj.note_last_modified_by_user_id = u.id left join user u2 on trpstj.puser_id = u2.id where talent_recruitment_process_id = :talentRecruitmentProcessId " +
            "UNION " +
            "select trpstc.id as id, 20 as 'node_type', trpstc.note as 'note', CONCAT(u.first_name, ' ', u.last_name) as 'last_modified_by', trpstc.note_last_modified_date as 'note_last_modified_date', u.id as 'note_last_modified_by_user_id', trpstc.created_date as 'created_date', CONCAT(u2.first_name, ' ', u2.last_name) as 'created_by' from talent_recruitment_process_submit_to_client trpstc left join user u on trpstc.note_last_modified_by_user_id = u.id left join user u2 on trpstc.puser_id = u2.id where talent_recruitment_process_id = :talentRecruitmentProcessId " +
            "UNION " +
            "select trpi.id as id, 30 as 'node_type', trpi.note as 'note', CONCAT(u.first_name, ' ', u.last_name) as 'last_modified_by', trpi.note_last_modified_date as 'note_last_modified_date', u.id as 'note_last_modified_by_user_id', trpi.created_date as 'created_date', CONCAT(u2.first_name, ' ', u2.last_name) as 'created_by' from talent_recruitment_process_interview trpi left join user u on trpi.note_last_modified_by_user_id = u.id left join user u2 on trpi.puser_id = u2.id where talent_recruitment_process_id = :talentRecruitmentProcessId " +
            "UNION " +
            "select trpo.id as id, 40 as 'node_type', trpo.note as 'note', CONCAT(u.first_name, ' ', u.last_name) as 'last_modified_by', trpo.note_last_modified_date as 'note_last_modified_date', u.id as 'note_last_modified_by_user_id', trpo.created_date as 'created_date', CONCAT(u2.first_name, ' ', u2.last_name) as 'created_by' from talent_recruitment_process_offer trpo left join user u on trpo.note_last_modified_by_user_id = u.id left join user u2 on trpo.puser_id = u2.id where talent_recruitment_process_id = :talentRecruitmentProcessId " +
            "UNION " +
            "select trpioa.id as id, 41 as 'node_type', trpioa.note as 'note', CONCAT(u.first_name, ' ', u.last_name) as 'last_modified_by', trpioa.note_last_modified_date as 'note_last_modified_date', u.id as 'note_last_modified_by_user_id', trpioa.created_date as 'created_date', CONCAT(u2.first_name, ' ', u2.last_name) as 'created_by' from talent_recruitment_process_ipg_offer_accept trpioa left join user u on trpioa.note_last_modified_by_user_id = u.id left join user u2 on trpioa.puser_id = u2.id where talent_recruitment_process_id = :talentRecruitmentProcessId " +
            "UNION " +
            "select trpc.id as id, 50 as 'node_type', trpc.note as 'note', CONCAT(u.first_name, ' ', u.last_name) as 'last_modified_by', trpc.note_last_modified_date as 'note_last_modified_date', u.id as 'note_last_modified_by_user_id', trpc.created_date as 'created_date', CONCAT(u2.first_name, ' ', u2.last_name) as 'created_by' from talent_recruitment_process_commission trpc left join user u on trpc.note_last_modified_by_user_id = u.id left join user u2 on trpc.puser_id = u2.id where talent_recruitment_process_id = :talentRecruitmentProcessId " +
            "UNION " +
            "select trpo2.id as id, 60 as 'node_type', trpo2.note as 'note', CONCAT(u.first_name, ' ', u.last_name) as 'last_modified_by', trpo2.note_last_modified_date as 'note_last_modified_date', u.id as 'note_last_modified_by_user_id', trpo2.created_date as 'created_date', CONCAT(u2.first_name, ' ', u2.last_name) as 'created_by' from talent_recruitment_process_onboard trpo2 left join user u on trpo2.note_last_modified_by_user_id = u.id left join user u2 on trpo2.puser_id = u2.id where talent_recruitment_process_id = :talentRecruitmentProcessId", nativeQuery = true)
    List<Object[]> getAllNotes(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);

    Integer countDistinctByTalentId(Long talentId);

    @Query(value = "select i from TalentRecruitmentProcessInterview i left join TalentApplicationProcess p on i.talentRecruitmentProcessId=p.id where p.jobId = :jobId order by i.createdDate desc")
    List<TalentRecruitmentProcessInterview> getLastInterview(@Param("jobId") Long jobId);

    @Query(value = "select new com.altomni.apn.application.web.rest.vm.ApplicationIdAndStatusVO(trp.id, node.nodeType) from TalentRecruitmentProcess trp " +
            "left join TalentRecruitmentProcessEliminate elim on trp.id = elim.talentRecruitmentProcessId " +
            "left join TalentRecruitmentProcessNode node on trp.id = node.talentRecruitmentProcessId and node.nodeStatus =  com.altomni.apn.common.domain.enumeration.application.NodeStatus.ACTIVE " +
            "where trp.id in :applicationIds order by trp.id desc ")
    List<ApplicationIdAndStatusVO> getAllLatestStatusByApplicationId(@Param("applicationIds") Set<Long> applicationIds);

    @Query(value = "select new com.altomni.apn.application.web.rest.vm.ApplicationIdAndStatusVO(trp.id, node.nodeType) from TalentRecruitmentProcess trp " +
            "left join TalentRecruitmentProcessEliminate elim on trp.id = elim.talentRecruitmentProcessId " +
            "left join TalentRecruitmentProcessNode node on trp.id = node.talentRecruitmentProcessId and node.nodeStatus = com.altomni.apn.common.domain.enumeration.application.NodeStatus.ACTIVE and node.nodeType = :status " +
            "where trp.id in :applicationIds and elim.id is null and node.id is not null " +
            "order by trp.id desc ")
    Page<ApplicationIdAndStatusVO> getApplicationIdsByStatus(@Param("applicationIds") Set<Long> applicationIds, @Param("status") NodeType status, Pageable pageable);

    @Query(value = "select new com.altomni.apn.application.web.rest.vm.ApplicationIdAndStatusVO(trp.id, 'ELIMINATED') from TalentRecruitmentProcess trp " +
            "left join TalentRecruitmentProcessEliminate elim on trp.id = elim.talentRecruitmentProcessId " +
            "where trp.id in :applicationIds and elim.id is not null " +
            "order by trp.id desc ")
    Page<ApplicationIdAndStatusVO> getApplicationIdsInEliminate(@Param("applicationIds") Set<Long> applicationIds, Pageable pageable);

    @Query(value = """
        		SELECT distinct i.id
                FROM invoice i
                LEFT JOIN start s ON i.start_id = s.id
                where s.talent_recruitment_process_id = ?1
        """, nativeQuery = true)
    List<Long> getInvoiceIdByTalentProgressId(Long talentRecruitmentProcessId);

    @Query(value = """
            SELECT distinct iai.id
            FROM invoicing_application_info iai
            LEFT JOIN invoicing_candidate_info ici ON iai.id = ici.invoice_application_id
            LEFT JOIN start s ON ici.start_id = s.id
            where s.talent_recruitment_process_id = ?1
        """, nativeQuery = true)
    List<Long> getInvoicingApplicationInfoIdByTalentProgressId(Long talentRecruitmentProcessId);

    @Query(value = """
            SELECT distinct tgi.id
	        FROM t_group_invoice tgi
	        LEFT JOIN t_group_invoice_record tgir ON tgir.group_invoice_id = tgi.id
	        LEFT JOIN t_contractor_invoice tci on tgir.invoice_id = tci.id
	        LEFT JOIN timesheet_talent_assignment tta ON tci.assignment_id = tta.id
	        LEFT JOIN start s ON tta.start_id = s.id
	        WHERE s.talent_recruitment_process_id = ?1
        """, nativeQuery = true)
    List<Long> getTGroupInvoiceIdByTalentProgressId(Long talentRecruitmentProcessId);
}
