package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardDate;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardWorkLocation;

/**
 * Service Interface for managing {@link com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardWorkLocation}.
 */
public interface TalentRecruitmentProcessOnboardWorkLocationService {
    /**
     * Save a talentRecruitmentProcessOnboardWorkLocation.
     *
     * @param talentRecruitmentProcessOnboardWorkLocation the entity to save.
     * @return the persisted entity.
     */
    TalentRecruitmentProcessOnboardWorkLocation save(Long talentRecruitmentProcessId, TalentRecruitmentProcessOnboardWorkLocation talentRecruitmentProcessOnboardWorkLocation);

    TalentRecruitmentProcessOnboardWorkLocation findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
