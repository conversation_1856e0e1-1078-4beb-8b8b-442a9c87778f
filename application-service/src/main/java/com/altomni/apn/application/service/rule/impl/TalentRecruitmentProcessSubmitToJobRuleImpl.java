package com.altomni.apn.application.service.rule.impl;

import com.altomni.apn.application.config.constant.Constants;
import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.service.job.JobService;
import com.altomni.apn.application.service.rule.TalentRecruitmentProcessSubmitToJobRule;
import com.altomni.apn.application.service.rule.ipg.TalentRecruitmentProcessIpgSubmitToJobRule;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.utils.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
@Transactional
public class TalentRecruitmentProcessSubmitToJobRuleImpl implements TalentRecruitmentProcessSubmitToJobRule {

    @Resource
    private TalentRecruitmentProcessIpgSubmitToJobRule ipgSubmitToJobRule;

    @Resource
    private JobService jobService;

    @Override
    public void preProcessValidate(TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
//        if (Constants.TENANT_IPG.equals(SecurityUtils.getTenantId())) {
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            ipgSubmitToJobRule.validate3DayProtectionRule(submitToJobVO);
            ipgSubmitToJobRule.validateTalentResume(submitToJobVO);
        }
    }

    @Override
    public void validate(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate, List<TalentRecruitmentProcessKpiUserVO> kpiUsers) {
        JobDTOV3 job = jobService.getJob(talentRecruitmentProcess.getJobId());
//        if (Constants.TENANT_IPG.equals(SecurityUtils.getTenantId())) {
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            ipgSubmitToJobRule.validateSkipSubmitToAm(talentRecruitmentProcess, agreedPayRate, job, kpiUsers);
            ipgSubmitToJobRule.checkJobStatus(job);
        }
    }
}
