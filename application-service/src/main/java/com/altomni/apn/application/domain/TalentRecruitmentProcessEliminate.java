package com.altomni.apn.application.domain;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.EliminateReasonConverter;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentRecruitmentProcessEliminate.
 */
@Entity
@Table(name = "talent_recruitment_process_eliminate")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessEliminate extends AbstractPermissionNoteAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1495314777550137054L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "reason")
    @Convert(converter = EliminateReasonConverter.class)
    private EliminateReason eliminateReason;

    @Column(name = "note")
    private String note;

    @Override
    public String getNoteValue() {
        return note;
    }
}
