package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceTypeConverter;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferAcceptVO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A TalentRecruitmentProcessIpgOfferAccept.
 */
@Entity
@Table(name = "talent_recruitment_process_ipg_offer_accept")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessIpgOfferAccept extends AbstractPermissionNoteAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1913340848544094885L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @JsonIgnore
    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "note")
    private String note;

    @Column(name = "channel_platform")
    @Convert(converter = ResumeSourceTypeConverter.class)
    private ResumeSourceType channelPlatform;

    @Column(name = "profit_sharing_ratio")
    private BigDecimal profitSharingRatio = new BigDecimal(0);

    public static TalentRecruitmentProcessIpgOfferAccept fromVO(TalentRecruitmentProcessIpgOfferAcceptVO vo) {
        TalentRecruitmentProcessIpgOfferAccept result = new TalentRecruitmentProcessIpgOfferAccept();
        ServiceUtils.myCopyProperties(vo, result);
        return result;
    }

    @Override
    public String getNoteValue() {
        return note;
    }
}
