package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboard;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardDate;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardWorkLocation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


/**
 * Spring Data  repository for the TalentRecruitmentProcessOnboardWorkLocation entity.
 */
@Repository
public interface TalentRecruitmentProcessOnboardWorkLocationRepository extends JpaRepository<TalentRecruitmentProcessOnboardWorkLocation, Long> {

    TalentRecruitmentProcessOnboardWorkLocation findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Transactional
    @Modifying
    @Query("UPDATE TalentRecruitmentProcessOnboardWorkLocation location SET location.officialCity=?2, location.officialCounty=?3, location.officialProvince=?4, location.officialCountry=?5 WHERE location.talentRecruitmentProcessId=?1")
    void updateOfficializedWorkLocationByTalentRecruitmentProcessId(Long talentRecruitmentProcessId, String officialCity, String officialCounty, String officialProvince, String officialCountry);

}
