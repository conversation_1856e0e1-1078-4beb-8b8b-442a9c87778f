package com.altomni.apn.application.service.talent.impl;

import com.altomni.apn.application.service.talent.TalentClient;
import com.altomni.apn.application.service.talent.TalentService;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import com.altomni.apn.common.dto.talent.TalentDTOV3;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PathVariable;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;

@Service
public class TalentServiceImpl implements TalentService {

    @Resource
    private TalentClient talentClient;

    private final Logger log = LoggerFactory.getLogger(TalentServiceImpl.class);

    @Override
    public TalentDTOV3 getTalentWithoutEntity(Long id) {
        ResponseEntity<TalentDTOV3> response = talentClient.getTalentWithoutEntity(id);
        return response != null ? response.getBody() : null;
    }

    @Override
    public TalentBriefDTO getTalentBrief(Long id) {
        ResponseEntity<List<TalentBriefDTO>> response = talentClient.getTalentWithoutEntityList(Set.of(id));
        return response != null && response.getBody() != null ? response.getBody().stream().findFirst().orElse(null) : null;
    }

    @Override
    public List<TalentOwnership> getAllTalentOwners(Long talentId, List<TalentOwnershipType> talentOwnershipType) {
        ResponseEntity<List<TalentOwnership>> response = talentClient.getAllTalentOwners(talentId, talentOwnershipType);
        return response != null ? response.getBody() : null;
    }

    @Override
    public List<TalentOwnership> saveAllOwnerships(List<TalentOwnership> talentOwnerships) {
        ResponseEntity<List<TalentOwnership>> response = talentClient.saveAllOwnerships(talentOwnerships);
        return response != null ? response.getBody() : null;
    }

    @Override
    public TalentResumeDTO getTalentResumeByTalentResumeRelationId(Long relationId) {
        ResponseEntity<TalentResumeDTO> response = talentClient.getTalentResumeByTalentResumeRelationId(relationId);
        return response != null ? response.getBody() : null;
    }

    @Override
    public void updateTalentExperience(Long talentId, TalentExperienceDTO talentExperienceDTO) {
        talentClient.updateTalentExperience(talentId, talentExperienceDTO);
    }

    @Override
    public void deleteTalentExperience(Long talentId, Long talentRecruitmentProcessId) {
        talentClient.deleteTalentExperience(talentId, talentRecruitmentProcessId);
    }
}
