package com.altomni.apn.application.domain.aiRecommend;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "job_talent_ai_recommend")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class JobTalentAiRecommend extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4442477104358727842L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId ;

    @Column(name = "source_tenant_id")
    private Long sourceTenantId ;

    /**  */
    @Column(name = "job_id")
    private Long jobId ;

    /**  */
    @Column(name = "talent_id")
    private Long talentId ;

    /**  */
    @Column(name = "ai_score")
    private Short aiScore ;

    @Column(name = "source_type")
    private String sourceType;
}
