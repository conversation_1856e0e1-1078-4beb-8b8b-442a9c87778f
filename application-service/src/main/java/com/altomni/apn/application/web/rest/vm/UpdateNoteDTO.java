package com.altomni.apn.application.web.rest.vm;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.application.NodeTypeConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Convert;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateNoteDTO {

    private Long talentRecruitmentProcessId;

    private Long id;

    @Convert(converter = NodeTypeConverter.class)
    private NodeType nodeType;

    private String note;

}
