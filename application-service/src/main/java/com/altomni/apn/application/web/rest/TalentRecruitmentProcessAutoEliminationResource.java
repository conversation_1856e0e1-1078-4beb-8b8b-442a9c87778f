package com.altomni.apn.application.web.rest;

import com.altomni.apn.application.dto.TalentRecruitmentProcessAutoEliminationDTO;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessAutoEliminationService;
import com.altomni.apn.application.web.rest.vm.TalentRecruitmentProcessAutoEliminationVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/v3/auto-elimination")
public class TalentRecruitmentProcessAutoEliminationResource {

    @Resource
    TalentRecruitmentProcessAutoEliminationService talentRecruitmentProcessAutoEliminationService;

    @GetMapping("")
    public ResponseEntity<TalentRecruitmentProcessAutoEliminationVO> queryAutoEliminationByJobId(@RequestParam("jobId") Long jobId) {
        return ResponseEntity.ok(talentRecruitmentProcessAutoEliminationService.queryAutoEliminationByJobId(jobId));
    }

    @GetMapping("/query-count-by-job-id-period")
    public ResponseEntity<Long> queryCountByJobIdAndPeriod(@RequestParam("jobId") Long jobId,@RequestParam("period") Integer period) {
        return ResponseEntity.ok(talentRecruitmentProcessAutoEliminationService.countByJobIdAndPeriod(jobId,period));
    }

    @PostMapping("")
    public ResponseEntity<TalentRecruitmentProcessAutoEliminationVO> saveAutoElimination(@RequestBody TalentRecruitmentProcessAutoEliminationDTO dto) {
        return ResponseEntity.ok(talentRecruitmentProcessAutoEliminationService.saveAutoElimination(dto));
    }

}
