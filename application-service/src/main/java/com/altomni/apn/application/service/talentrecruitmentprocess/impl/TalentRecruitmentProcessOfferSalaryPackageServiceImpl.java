package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessKpiUser;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOfferSalaryPackage;
import com.altomni.apn.application.repository.TalentRecruitmentProcessOfferSalaryPackageRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessOfferSalaryPackageService;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOfferSalaryPackageVO;
import com.altomni.apn.common.utils.ServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessKpiUser}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessOfferSalaryPackageServiceImpl implements TalentRecruitmentProcessOfferSalaryPackageService {

    @Resource
    private TalentRecruitmentProcessOfferSalaryPackageRepository offerSalaryPackageRepository;

    @Override
    public List<TalentRecruitmentProcessOfferSalaryPackageVO> save(Long talentRecruitmentProcessId, List<TalentRecruitmentProcessOfferSalaryPackageVO> salaryPackageVOS) {
        if (CollectionUtils.isNotEmpty(salaryPackageVOS)) {
            List<TalentRecruitmentProcessOfferSalaryPackage> exist = offerSalaryPackageRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId);
            if (CollectionUtils.isNotEmpty(exist)) {
                offerSalaryPackageRepository.deleteAll(exist);
            }
            salaryPackageVOS.forEach(t -> t.setTalentRecruitmentProcessId(talentRecruitmentProcessId));
            List<TalentRecruitmentProcessOfferSalaryPackage> salaryPackages = salaryPackageVOS.stream().map(vo -> {
                TalentRecruitmentProcessOfferSalaryPackage entity = toEntity(vo);
                entity.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
                return entity;
            }).collect(Collectors.toList());
            return toVOs(offerSalaryPackageRepository.saveAll(salaryPackages));
        }
        return Lists.newArrayList();
    }

    private TalentRecruitmentProcessOfferSalaryPackage toEntity(TalentRecruitmentProcessOfferSalaryPackageVO vo) {
        if (vo != null) {
            TalentRecruitmentProcessOfferSalaryPackage entity = new TalentRecruitmentProcessOfferSalaryPackage();
            ServiceUtils.myCopyProperties(vo, entity);
            return entity;
        }
        return null;
    }

    private List<TalentRecruitmentProcessOfferSalaryPackageVO> toVOs(List<TalentRecruitmentProcessOfferSalaryPackage> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        return ServiceUtils.convert2DTOList(entities, TalentRecruitmentProcessOfferSalaryPackageVO.class);
    }

    @Override
    public List<TalentRecruitmentProcessOfferSalaryPackageVO> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVOs(offerSalaryPackageRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }
}
