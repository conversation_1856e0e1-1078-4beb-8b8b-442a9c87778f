package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessResignation;
import com.altomni.apn.application.service.finance.FinanceClient;
import com.altomni.apn.application.service.mapper.TalentRecruitmentProcessResignationVOMapper;
import com.altomni.apn.application.service.talent.TalentService;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationDTO;
import com.altomni.apn.application.repository.TalentRecruitmentProcessResignationRepository;
import com.altomni.apn.application.service.mapper.TalentRecruitmentProcessResignationMapper;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessResignationService;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationVO;
import com.altomni.apn.common.dto.talent.TalentExperienceDTO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.finance.service.dto.start.StartDTO;
import com.altomni.apn.job.domain.enumeration.start.StartStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * Service Implementation for managing TalentRecruitmentProcessResignationServiceImpl.
 * <AUTHOR>
 */
@Service
@Transactional
public class TalentRecruitmentProcessResignationServiceImpl implements TalentRecruitmentProcessResignationService {

    @Resource
    private TalentRecruitmentProcessResignationRepository resignationRepository;

    @Resource
    private TalentRecruitmentProcessResignationMapper resignationMapper;

    @Resource
    private TalentRecruitmentProcessResignationVOMapper resignationVOMapper;

    @Resource
    private TalentService talentService;

    @Resource
    private FinanceClient financeClient;

    @Override
    @Transactional(readOnly = true)
    public TalentRecruitmentProcessResignationVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        TalentRecruitmentProcessResignation resignation = resignationRepository.findOneByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (Objects.isNull(resignation)){
            return null;
        }
        TalentRecruitmentProcessResignationVO result = new TalentRecruitmentProcessResignationVO();
        ServiceUtils.myCopyProperties(resignation, result);
        return result;
    }

    @Override
    public void resign(TalentRecruitmentProcessResignationDTO resignationDTO) {
        TalentRecruitmentProcessResignation resignation = resignationMapper.toEntity(resignationDTO);
        TalentRecruitmentProcessResignation existingResignation = resignationRepository.findOneByTalentRecruitmentProcessId(resignation.getTalentRecruitmentProcessId());
        if (Objects.nonNull(existingResignation)){
            existingResignation.setResignDate(resignation.getResignDate())
                    .setResignationReason(resignation.getResignationReason())
                    .setNote(resignation.getNote());
            resignation = existingResignation;
        }
        resignationRepository.save(resignation);
        this.updateTalentExperienceWithEndDate(resignationDTO);
    }

    private void updateTalentExperienceWithEndDate(TalentRecruitmentProcessResignationDTO resignationDTO){
        StartDTO start = financeClient.getStartByTalentRecruitmentProcessIdAndStatus(resignationDTO.getTalentRecruitmentProcessId(), StartStatus.ACTIVE).getBody();
        if (Objects.isNull(start)){
            return;
        }
        TalentExperienceDTO talentExperienceDTO = new TalentExperienceDTO()
                .setTalentRecruitmentProcessId(start.getTalentRecruitmentProcessId())
                .setLocation(Objects.nonNull(start.getStartAddress()) ? start.getStartAddress().getOriginDisplay() : null)
                .setStartDate(start.getStartDate())
                .setEndDate(resignationDTO.getResignDate())
                .setCompanyId(start.getCompanyId())
                .setCompanyName(start.getCompany())
                .setTitle(start.getJobTitle())
                .setCurrent(null);
        talentService.updateTalentExperience(start.getTalentId(), talentExperienceDTO);
    }

    @Override
    public List<TalentRecruitmentProcessResignationVO> findByTalentId(Long talentId) {
        return resignationVOMapper.toDto(resignationRepository.findAllByTalentId(talentId));
    }
}
