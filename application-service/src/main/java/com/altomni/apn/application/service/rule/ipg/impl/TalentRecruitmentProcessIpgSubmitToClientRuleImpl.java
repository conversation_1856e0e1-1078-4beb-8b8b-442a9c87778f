package com.altomni.apn.application.service.rule.ipg.impl;

import com.altomni.apn.application.config.constant.Constants;
import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.repository.TalentRecruitmentProcessRepository;
import com.altomni.apn.application.service.rule.ipg.TalentRecruitmentProcessIpgSubmitToClientRule;
import com.altomni.apn.application.service.talent.TalentService;
import com.altomni.apn.application.service.user.UserService;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipUserRole;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToClientVO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class TalentRecruitmentProcessIpgSubmitToClientRuleImpl implements TalentRecruitmentProcessIpgSubmitToClientRule {

    @Resource
    private TalentService talentService;
    @Resource
    private UserService userService;
    @Resource
    private TalentRecruitmentProcessRepository talentRecruitmentProcessRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Override
    public void saveTalentOwnership(TalentRecruitmentProcessSubmitToClientVO submitToClientVO) {
        TalentRecruitmentProcess talentRecruitmentProcess = validateTalentRecruitmentProcess(submitToClientVO.getTalentRecruitmentProcessId());
        List<TalentOwnership> allOwnerships = talentService.getAllTalentOwners(talentRecruitmentProcess.getTalentId(), List.of(TalentOwnershipType.OWNER));
        if (CollectionUtils.isNotEmpty(allOwnerships)) {
            // This talent has ownership including expired.
            List<TalentOwnership> notExpiredOwners = allOwnerships.stream().filter(ownership -> Instant.now().isBefore(ownership.getExpireTime())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notExpiredOwners)) {
                // Have valid ownerships, find which recruitment process produces these ownerships
                Long talentRecruitmentProcessId = notExpiredOwners.get(0).getTalentRecruitmentProcessId(); // all ownerships should have same talentRecruitmentProcessId!
                if (talentRecruitmentProcessId != null) {
                    if (!talentRecruitmentProcessId.equals(submitToClientVO.getTalentRecruitmentProcessId())) { // if it's current recruitment process, we already saved these ownerships, no action needed/.
                        //if it's not current recruitment process, we need save those ownerships into kpi user commission list.
                        List<TalentRecruitmentProcessKpiUserVO> ownershipCommissions = buildTalentOwnershipCommission(talentRecruitmentProcess.getId(), notExpiredOwners);
                        if (CollectionUtils.isNotEmpty(ownershipCommissions)) {
                            if (Objects.isNull(submitToClientVO.getKpiUsers())) {
                                List<TalentRecruitmentProcessKpiUserVO> kpiUsers = new ArrayList<>();
                                submitToClientVO.setKpiUsers(kpiUsers);
                            }
                            submitToClientVO.getKpiUsers().addAll(ownershipCommissions);
                        }
                    }
                }
            }
            // This talent has expired ownerships, nothing to do by this case.
        } else {
            // This talent has no ownership, need to create ownership based on commission users.
            List<TalentOwnership> talentOwnerships = buildTalentOwnerships(talentRecruitmentProcess.getTalentId(), talentRecruitmentProcess.getId(), submitToClientVO.getKpiUsers());
            if (CollectionUtils.isNotEmpty(talentOwnerships)) {
                talentService.saveAllOwnerships(talentOwnerships);
            }
        }
    }

    private List<TalentOwnership> buildTalentOwnerships(Long talentId, Long talentRecruitmentProcessId, List<TalentRecruitmentProcessKpiUserVO> kpiUsers) {
        List<TalentOwnership> talentOwnerships = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(kpiUsers)) {
            List<TalentRecruitmentProcessKpiUserVO> recruiterAndSourcers = kpiUsers.stream()
                    .filter(user -> Constants.RECRUITER_AND_SOURCER.contains(user.getUserRole()))
                    .collect(Collectors.toList());
            Set<Long> distinctUserIds = recruiterAndSourcers.stream().map(TalentRecruitmentProcessKpiUserVO::getUserId).collect(Collectors.toSet());
            if (recruiterAndSourcers.size() == distinctUserIds.size()) {
                // no duplicated users
                for (TalentRecruitmentProcessKpiUserVO kpiUser : recruiterAndSourcers) {
                    TalentOwnership talentOwnership = toTalentOwnership(talentId, kpiUser.getUserId(), talentRecruitmentProcessId);
                    talentOwnership.setUserRole(UserRole.RECRUITER.equals(kpiUser.getUserRole()) ? TalentOwnershipUserRole.OWNERSHIP_RECRUITER : TalentOwnershipUserRole.OWNERSHIP_SOURCER);
                    talentOwnerships.add(talentOwnership);
                }
            } else {
                // have duplicated users, find out and distinct
                Set<Long> temp = new HashSet<>();
                Set<Long> duplicatedUserIds = new HashSet<>();
                for (TalentRecruitmentProcessKpiUserVO kpiUser : recruiterAndSourcers) {
                    if (!temp.add(kpiUser.getUserId())) {
                        duplicatedUserIds.add(kpiUser.getUserId());
                    }
                }
                // distinct users
                for (TalentRecruitmentProcessKpiUserVO kpiUser : recruiterAndSourcers) {
                    if (!duplicatedUserIds.contains(kpiUser.getUserId())) {
                        TalentOwnership talentOwnership = toTalentOwnership(talentId, kpiUser.getUserId(), talentRecruitmentProcessId);
                        talentOwnership.setUserRole(UserRole.RECRUITER.equals(kpiUser.getUserRole()) ? TalentOwnershipUserRole.OWNERSHIP_RECRUITER : TalentOwnershipUserRole.OWNERSHIP_SOURCER);
                        talentOwnerships.add(talentOwnership);
                    }
                }
                // duplicated users
                if (CollectionUtils.isNotEmpty(duplicatedUserIds)) {
                    for (Long userId : duplicatedUserIds) {
                        TalentOwnership talentOwnership = toTalentOwnership(talentId, userId, talentRecruitmentProcessId);
                        talentOwnership.setUserRole(TalentOwnershipUserRole.OWNERSHIP_RECRUITER_AND_SOURCER);
                        talentOwnerships.add(talentOwnership);
                    }
                }
            }
        }
        return talentOwnerships;
    }

    private TalentOwnership toTalentOwnership(Long talentId, Long userId, Long talentRecruitmentProcessId) {
        TalentOwnership result = new TalentOwnership();
        result.setTalentId(talentId);
        result.setUserId(userId);
        result.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
        result.setOwnershipType(TalentOwnershipType.OWNER);
        Integer protectionPeriod = userService.getTenantParamValue(Constants.TALENT_OWNERSHIP_PROTECTION_PERIOD);
        result.setExpireTime(Instant.now().plus(protectionPeriod, ChronoUnit.MINUTES));
        return result;
    }

    private TalentRecruitmentProcess validateTalentRecruitmentProcess(Long talentRecruitmentProcessId) {
        if (talentRecruitmentProcessId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.IPG_VALIDATETALENTRECRUITMENTPROCESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        Optional<TalentRecruitmentProcess> talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId);
        if (talentRecruitmentProcess.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.IPG_VALIDATETALENTRECRUITMENTPROCESSIDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(talentRecruitmentProcessId), applicationApiPromptProperties.getAppl()));
        }
        if (!talentRecruitmentProcess.get().getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.IPG_VALIDATETALENTRECRUITMENTPROCESSNOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        return talentRecruitmentProcess.get();
    }

    private List<TalentRecruitmentProcessKpiUserVO> buildTalentOwnershipCommission(Long talentRecruitmentProcessId, List<TalentOwnership> ownershipUsers) {
        List<TalentRecruitmentProcessKpiUserVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ownershipUsers)) {
            if (Constants.SPECIAL_NUMBER_OF_SIZE.contains(ownershipUsers.size())) {
                List<BigDecimal> specialPercentages = calculateSpecialPercentage(ownershipUsers.size());
                for (int i = 0; i < ownershipUsers.size(); i++) {
                    TalentRecruitmentProcessKpiUserVO ipgKpiUser = new TalentRecruitmentProcessKpiUserVO();
                    ipgKpiUser.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
                    ipgKpiUser.setUserId(ownershipUsers.get(i).getUserId());
                    ipgKpiUser.setUserRole(UserRole.OWNER);
                    ipgKpiUser.setPercentage(specialPercentages.get(i));
                    result.add(ipgKpiUser);
                }
            } else {
                BigDecimal size = new BigDecimal(ownershipUsers.size());
                BigDecimal eachUserPercentage = Constants.TALENT_OWNERSHIP_PERCENTAGE.divide(size, 4, RoundingMode.HALF_UP);
                ownershipUsers.forEach(talentOwnership -> {
                    TalentRecruitmentProcessKpiUserVO ipgKpiUser = new TalentRecruitmentProcessKpiUserVO();
                    ipgKpiUser.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
                    ipgKpiUser.setUserId(talentOwnership.getUserId());
                    ipgKpiUser.setUserRole(UserRole.OWNER);
                    ipgKpiUser.setPercentage(eachUserPercentage);
                    result.add(ipgKpiUser);
                });
            }
        }
        return result;
    }

    private List<BigDecimal> calculateSpecialPercentage(Integer size) {
        List<BigDecimal> result = new ArrayList<>();
        if (Constants.THREE.equals(size)) {
            result.add(new BigDecimal("3.3333"));
            result.add(new BigDecimal("3.3334"));
            result.add(new BigDecimal("3.3333"));
        } else if (Constants.SIX.equals(size)) {
            result.add(new BigDecimal("1.6667"));
            result.add(new BigDecimal("1.6666"));
            result.add(new BigDecimal("1.6667"));
            result.add(new BigDecimal("1.6666"));
            result.add(new BigDecimal("1.6667"));
            result.add(new BigDecimal("1.6667"));
        } else if (Constants.SEVEN.equals(size)) {
            result.add(new BigDecimal("1.4285"));
            result.add(new BigDecimal("1.4285"));
            result.add(new BigDecimal("1.4286"));
            result.add(new BigDecimal("1.4286"));
            result.add(new BigDecimal("1.4286"));
            result.add(new BigDecimal("1.4286"));
            result.add(new BigDecimal("1.4286"));
        } else {
            result.add(new BigDecimal("1.1111"));
            result.add(new BigDecimal("1.1111"));
            result.add(new BigDecimal("1.1111"));
            result.add(new BigDecimal("1.1111"));
            result.add(new BigDecimal("1.1112"));
            result.add(new BigDecimal("1.1111"));
            result.add(new BigDecimal("1.1111"));
            result.add(new BigDecimal("1.1111"));
            result.add(new BigDecimal("1.1111"));
        }
        return result;
    }


}
