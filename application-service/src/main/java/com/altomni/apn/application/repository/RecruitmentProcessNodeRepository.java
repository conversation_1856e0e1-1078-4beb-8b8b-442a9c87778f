package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.RecruitmentProcessNode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data  repository for the RecruitmentProcessNode entity.
 */
@SuppressWarnings("unused")
@Repository
public interface RecruitmentProcessNodeRepository extends JpaRepository<RecruitmentProcessNode, Long> {

    List<RecruitmentProcessNode> findAllByRecruitmentProcessId(Long recruitmentProcessId);

    List<RecruitmentProcessNode> findAllByRecruitmentProcessIdAndTenantId(Long recruitmentProcessId, Long tenantId);
}
