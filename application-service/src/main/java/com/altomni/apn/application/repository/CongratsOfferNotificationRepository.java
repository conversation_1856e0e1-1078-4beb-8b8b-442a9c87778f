package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.CongratsOfferNotification;
import com.altomni.apn.common.domain.job.JobSnapshot;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Repository
public interface CongratsOfferNotificationRepository extends JpaRepository<CongratsOfferNotification, Long> {

    List<CongratsOfferNotification> findAllByTenantId(Long tenantId);

    @Query(value = "select t.code from talent_recruitment_process_kpi_user kpi " +
            "inner join permission_user_team put on kpi.user_id = put.user_id and put.is_primary=true " +
            "inner join permission_team t on t.id=put.team_id " +
            "where kpi.talent_recruitment_process_id =:talentRecruitmentProcessId", nativeQuery = true)
    Set<String> getTeamCodesByTalentRecruitmentProcessId(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId);
}
