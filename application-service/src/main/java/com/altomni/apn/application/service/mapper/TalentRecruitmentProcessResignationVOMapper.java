package com.altomni.apn.application.service.mapper;

import com.altomni.apn.application.domain.TalentRecruitmentProcessResignation;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationDTO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessResignationVO;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 */
@Mapper(componentModel = "spring", uses = {})
public interface TalentRecruitmentProcessResignationVOMapper extends EntityMapper<TalentRecruitmentProcessResignationVO, TalentRecruitmentProcessResignation> {


}
