package com.altomni.apn.application.domain.enumeration;


import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

import java.util.ArrayList;
import java.util.List;

/**
 * The NodeType enumeration.
 */
public enum NodeSearchFilter implements ConvertedEnum<Integer> {

//    UNKNOWN(-1, "Unknown"),

    SUBMIT_TO_JOB(10, "Submit to Job"),

    SUBMIT_TO_CLIENT(20, "Submit to Client"),

    INTERVIEW(30, "Interview"),

    OFFER(40, "Offer"),

    OFFER_ACCEPT(41, "Offer Accept"),

    COMMISSION(50, "Commission"),

    ON_BOARD(60, "On Board"),

    ELIMINATED(4, "Eliminated");

    private final Integer dbValue;

    private final String description;

    public static final List<Integer> ALL_NODE_TYPES = new ArrayList<>();

    static {
        for (NodeSearchFilter nodeType: NodeSearchFilter.values()) {
            ALL_NODE_TYPES.add(nodeType.toDbValue());
        }
    }

    NodeSearchFilter(Integer dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDescription() {
        return description;
    }

    // static resolving:
    public static final ReverseEnumResolver<NodeSearchFilter, Integer> resolver =
        new ReverseEnumResolver<>(NodeSearchFilter.class, NodeSearchFilter::toDbValue);

    public static NodeSearchFilter fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
