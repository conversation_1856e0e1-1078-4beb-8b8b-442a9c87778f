package com.altomni.apn.application.service.recruitmentprocess;



import com.altomni.apn.application.domain.RecruitmentProcess;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessConfigVO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessStats;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessTalentAndJobStats;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * Service Interface for managing RecruitmentProcess.
 */
public interface RecruitmentProcessService {

    RecruitmentProcessVO create(RecruitmentProcessVO recruitmentProcess);

    RecruitmentProcessVO update(Long id, RecruitmentProcessVO recruitmentProcessVO);

    RecruitmentProcessVO updateStatus(Long id, ActiveStatus status);

    Page<RecruitmentProcessVO> findAll(ActiveStatus status, Pageable pageable);

    Page<RecruitmentProcessVO> findAllForPrivateJob(Pageable pageable);

    Page<RecruitmentProcessVO> findAllByTenantId(Long tenantId, ActiveStatus status, Pageable pageable);

    RecruitmentProcessVO findOne(Long id);

    void delete(Long id);

    RecruitmentProcessVO getDefaultRecruitmentProcess(JobType jobType);

    List<RecruitmentProcessVO> config(RecruitmentProcessConfigVO configVO);

    List<RecruitmentProcessStats> getClintBoardInterviewStats(List<Long> jobIdList);

    List<Map<String, Object>> getClintBoardInterviewStatsMap(List<Long> jobIdList);

    List<RecruitmentProcess> initForIpg(Long tenantId);

    List<RecruitmentProcessVO> initDefaultForGeneralRecruitingProcess(Long tenantId);

    RecruitmentProcessTalentAndJobStats getInProgressJobCountByRecruitmentProcessId(Long recruitmentProcessId);

    void downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(Long recruitmentProcessId, String language, HttpServletResponse response);

    byte[] downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(Long recruitmentProcessId, String language);

    Map<Long, JobType> findAllMyRecruitmentProcessIds();

    Long countMyActiveRecruitmentProcess();

    RecruitmentProcessVO findOneBrief(Long id);

    List<RecruitmentProcessVO> getAllRecruitmentProcess();

}
