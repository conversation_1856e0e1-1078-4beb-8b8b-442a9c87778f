package com.altomni.apn.application.service.recruitmentprocess.impl;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.application.config.constant.Constants;
import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.RecruitmentProcessNodePageSection;
import com.altomni.apn.application.dto.DefaultFieldConfigDTO;
import com.altomni.apn.application.dto.FieldConfigDTO;
import com.altomni.apn.application.dto.RecruitmentProcessNodePageSectionDTO;
import com.altomni.apn.application.repository.RecruitmentProcessNodePageSectionRepository;
import com.altomni.apn.application.service.mapper.RecruitmentProcessNodePageSectionMapper;
import com.altomni.apn.application.service.recruitmentprocess.RecruitmentProcessNodePageSectionService;
import com.altomni.apn.application.service.recruitmentprocess.RecruitmentProcessService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessService;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessNodeVO;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link RecruitmentProcessNodePageSection}.
 */
@Service
@Transactional
public class RecruitmentProcessNodePageSectionServiceImpl implements RecruitmentProcessNodePageSectionService {

    @Resource
    private RecruitmentProcessNodePageSectionRepository recruitmentProcessNodePageSectionRepository;
    @Resource
    private TalentRecruitmentProcessService talentRecruitmentProcessService;
    @Resource
    private RecruitmentProcessService recruitmentProcessService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    private final RecruitmentProcessNodePageSectionMapper recruitmentProcessNodePageSectionMapper;

    public RecruitmentProcessNodePageSectionServiceImpl(RecruitmentProcessNodePageSectionMapper recruitmentProcessNodePageSectionMapper) {
        this.recruitmentProcessNodePageSectionMapper = recruitmentProcessNodePageSectionMapper;
    }

    @Override
    public List<RecruitmentProcessNodePageSectionDTO> findAllRecruitmentProcessNodePageSectionsByNodeId(JobType jobType, NodeType nodeType) {
//        if (Constants.TENANT_IPG.equals(SecurityUtils.getTenantId()) &&
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId()) &&
                (JobType.FULL_TIME.equals(jobType) || JobType.CONTRACT.equals(jobType) || JobType.PAY_ROLL.equals(jobType) || JobType.MSP.equals(jobType) )) {
            return recruitmentProcessNodePageSectionRepository.findAllByTenantIdAndNodeTypeAndJobType(SecurityUtils.getTenantId(), nodeType, jobType).stream().map(recruitmentProcessNodePageSectionMapper::toDto).collect(Collectors.toList());
        }
        return recruitmentProcessNodePageSectionRepository.findAllByTenantIdAndNodeType(Constants.TENANT_DEFAULT, nodeType).stream().map(recruitmentProcessNodePageSectionMapper::toDto).collect(Collectors.toList());
    }

    @Override
    public List<RecruitmentProcessNodePageSectionDTO> create(List<RecruitmentProcessNodePageSection> sections) {
        if (CollectionUtils.isEmpty(sections)) {
            return Lists.newArrayList();
        }
        return recruitmentProcessNodePageSectionRepository.saveAll(sections).stream().map(recruitmentProcessNodePageSectionMapper::toDto).collect(Collectors.toList());
    }

    @Override
    public RecruitmentProcessNodePageSectionDTO create(Long recruitmentProcessId, NodeType nodeType, List<FieldConfigDTO> fieldConfigDTOS) {
        RecruitmentProcessVO recruitmentProcessVO = recruitmentProcessService.findOne(recruitmentProcessId);
        RecruitmentProcessNodePageSection recruitmentProcessNodePageSection = new RecruitmentProcessNodePageSection();
        recruitmentProcessNodePageSection.setRecruitmentProcessId(recruitmentProcessId);
        recruitmentProcessNodePageSection.setTenantId(recruitmentProcessVO.getTenantId());
        recruitmentProcessNodePageSection.setNodeType(nodeType);
        recruitmentProcessNodePageSection.setDescription(null);
        recruitmentProcessNodePageSection.setJobType(recruitmentProcessVO.getJobType());
        //TODO: validate field config
        recruitmentProcessNodePageSection.setFieldConfig(JSON.toJSONString(fieldConfigDTOS));
        return recruitmentProcessNodePageSectionMapper.toDto(recruitmentProcessNodePageSectionRepository.saveAndFlush(recruitmentProcessNodePageSection));
    }

    @Override
    public List<RecruitmentProcessNodePageSectionDTO> initByDefaultConfig(Long recruitmentProcessId) {
        RecruitmentProcessVO recruitmentProcessVO = recruitmentProcessService.findOne(recruitmentProcessId);

        List<RecruitmentProcessNodePageSection> res = new ArrayList<>();
        for (RecruitmentProcessNodeVO node: recruitmentProcessVO.getRecruitmentProcessNodes()) {
            RecruitmentProcessNodePageSection recruitmentProcessNodePageSection = initByDefault(recruitmentProcessId, recruitmentProcessVO.getTenantId(), node.getNodeType());
            res.add(recruitmentProcessNodePageSection);
        }
        return recruitmentProcessNodePageSectionRepository.saveAllAndFlush(res).stream().map(recruitmentProcessNodePageSectionMapper::toDto).collect(Collectors.toList());
    }

    private RecruitmentProcessNodePageSection initByDefault(Long recruitmentProcessId, Long tenantId, NodeType nodeType) {
//        RecruitmentProcessNodePageSectionDTO defaultConfigStr = this.findGeneralRecruitmentProcessNodePageSectionsDefaultConfigByNodeType(nodeType);
        RecruitmentProcessNodePageSectionDTO defaultConfigStr = this.findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(JobType.OTHERS, nodeType); //JobType.OTHERS means General-Recruitment(通用版流程)
        List<DefaultFieldConfigDTO> defaultFieldConfigDTO = JSON.parseArray(defaultConfigStr.getFieldConfig(), DefaultFieldConfigDTO.class);
        List<FieldConfigDTO> configs = defaultFieldConfigDTO.stream().map(defaultConfig -> {
            FieldConfigDTO fieldConfigDTO = defaultConfig.getDefaultConfig();
            fieldConfigDTO.setKey(defaultConfig.getKey());
            return fieldConfigDTO;
        }).collect(Collectors.toList());

        RecruitmentProcessNodePageSection recruitmentProcessNodePageSection = new RecruitmentProcessNodePageSection();
        recruitmentProcessNodePageSection.setRecruitmentProcessId(recruitmentProcessId);
        recruitmentProcessNodePageSection.setTenantId(tenantId);
        recruitmentProcessNodePageSection.setNodeType(nodeType);
        recruitmentProcessNodePageSection.setDescription(null);
        recruitmentProcessNodePageSection.setJobType(JobType.OTHERS);
        recruitmentProcessNodePageSection.setFieldConfig(JSON.toJSONString(configs));
        return recruitmentProcessNodePageSection;
    }

    @Override
    public RecruitmentProcessNodePageSectionDTO update(Long recruitmentProcessId, NodeType nodeType, List<FieldConfigDTO> fieldConfigDTOS) {
        RecruitmentProcessNodePageSection recruitmentProcessNodePageSection = recruitmentProcessNodePageSectionRepository.findAllByRecruitmentProcessIdAndNodeType(recruitmentProcessId, nodeType);
        recruitmentProcessNodePageSection.setNodeType(nodeType);
        recruitmentProcessNodePageSection.setDescription(null);
        validateConfig(recruitmentProcessNodePageSection.getJobType(), nodeType, fieldConfigDTOS);
        recruitmentProcessNodePageSection.setFieldConfig(JSON.toJSONString(fieldConfigDTOS));
        return recruitmentProcessNodePageSectionMapper.toDto(recruitmentProcessNodePageSectionRepository.saveAndFlush(recruitmentProcessNodePageSection));
    }

    private void validateConfig(JobType jobType, NodeType nodeType, List<FieldConfigDTO> fieldConfigDTOS) {
//        RecruitmentProcessNodePageSectionDTO defaultConfigStr = this.findGeneralRecruitmentProcessNodePageSectionsDefaultConfigByNodeType(nodeType);
        RecruitmentProcessNodePageSectionDTO defaultConfigStr = this.findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(jobType, nodeType);
        List<DefaultFieldConfigDTO> defaultFieldConfigDTO = JSON.parseArray(defaultConfigStr.getFieldConfig(), DefaultFieldConfigDTO.class);
        if (fieldConfigDTOS.size() != defaultFieldConfigDTO.size()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTEPAGE_CONFIGMISS.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
        }
        Map<String, DefaultFieldConfigDTO> defaultFieldConfigMap = defaultFieldConfigDTO.stream().collect(Collectors.toMap(DefaultFieldConfigDTO::getKey, Function.identity()));
        for (FieldConfigDTO config: fieldConfigDTOS) {
            try {
                DefaultFieldConfigDTO defaultConfig = defaultFieldConfigMap.get(config.getKey());
                validate(defaultConfig, config);
            } catch (NullPointerException e) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTEPAGE_CONFIGNOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(e.getMessage()),applicationApiPromptProperties.getAppl()));
            }
        }
    }

    private void validate(DefaultFieldConfigDTO defaultConfigDTO, FieldConfigDTO config) {
        if (!StringUtils.equals(defaultConfigDTO.getKey(), config.getKey())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTEPAGE_DIFFKEY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
        }
        FieldConfigDTO defaultConfig = defaultConfigDTO.getDefaultConfig();
        if (defaultConfigDTO.isConfigurable()) { //configurable
            if (!config.isVisible()) { //not visible
                config.setRequired(null);
//                config.setOnlyDisplay(null);
            } else { //visible
                if (BooleanUtils.isNotTrue(defaultConfig.getOnlyDisplay())) { //input field
                    if (BooleanUtils.isTrue(config.getOnlyDisplay())) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTEPAGE_CANNOTSETDISPLAY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(config.getKey()),applicationApiPromptProperties.getAppl()));
                    } else {
                        config.setOnlyDisplay(null);
                    }
                    if (Objects.isNull(config.getRequired())) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTEPAGE_MUSTSETDISPLAY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(config.getKey()),applicationApiPromptProperties.getAppl()));
                    }
                } else {//display field
                    if (Objects.nonNull(config.getRequired())) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTEPAGE_CANNOTSETREQUIREDISPLAY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(config.getKey()),applicationApiPromptProperties.getAppl()));
                    }
                    if (BooleanUtils.isNotTrue(config.getOnlyDisplay())) {
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESSNOTEPAGE_MUSTSETREQUIREDISPLAY.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(config.getKey()),applicationApiPromptProperties.getAppl()));
                    }
                }
            }
        } else { //not configurable
            if (!Objects.equals(defaultConfig.isVisible(), config.isVisible()) ||
                    !Objects.equals(defaultConfig.getRequired(), config.getRequired()) ||
                    !Objects.equals(defaultConfig.getOnlyDisplay(), config.getOnlyDisplay())) {
                throw new CustomParameterizedException("cannot config non-configurable key!");
            }
        }
    }

    @Override
    public List<RecruitmentProcessNodePageSectionDTO> findGeneralRecruitmentProcessNodePageSectionsDefaultConfig() {
        return recruitmentProcessNodePageSectionRepository.findAllByTenantIdAndJobTypeAndRecruitmentProcessId(-1L, JobType.OTHERS, -1L).stream().map(recruitmentProcessNodePageSectionMapper::toDto).collect(Collectors.toList());
    }

    @Override
    public RecruitmentProcessNodePageSectionDTO findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(JobType jobType, NodeType nodeType) {
        return recruitmentProcessNodePageSectionMapper.toDto(recruitmentProcessNodePageSectionRepository.findAllByTenantIdAndRecruitmentProcessIdAndJobTypeAndNodeType(-1L, -1L, jobType, nodeType));
    }

    @Override
    public List<RecruitmentProcessNodePageSectionDTO> findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(Long recruitmentProcessId) {
        if (SecurityUtils.isSystemAdmin()) {
            return recruitmentProcessNodePageSectionRepository.findAllByRecruitmentProcessId(recruitmentProcessId).stream().map(recruitmentProcessNodePageSectionMapper::toDto).collect(Collectors.toList());
        } else {
            return recruitmentProcessNodePageSectionRepository.findAllByTenantIdAndRecruitmentProcessId(SecurityUtils.getTenantId(), recruitmentProcessId).stream().map(recruitmentProcessNodePageSectionMapper::toDto).collect(Collectors.toList());
        }
    }

    @Override
    public RecruitmentProcessNodePageSectionDTO findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(Long recruitmentProcessId, NodeType nodeType) {
        if (SecurityUtils.isSystemAdmin()) {
            return recruitmentProcessNodePageSectionMapper.toDto(recruitmentProcessNodePageSectionRepository.findAllByRecruitmentProcessIdAndNodeType(recruitmentProcessId, nodeType));
        } else {
            return recruitmentProcessNodePageSectionMapper.toDto(recruitmentProcessNodePageSectionRepository.findAllByTenantIdAndRecruitmentProcessIdAndNodeType(SecurityUtils.getTenantId(), recruitmentProcessId, nodeType));
        }
    }
}
