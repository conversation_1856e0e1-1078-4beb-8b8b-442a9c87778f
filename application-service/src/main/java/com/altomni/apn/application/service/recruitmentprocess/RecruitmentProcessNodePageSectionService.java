package com.altomni.apn.application.service.recruitmentprocess;

import com.altomni.apn.application.domain.RecruitmentProcessNodePageSection;
import com.altomni.apn.application.dto.FieldConfigDTO;
import com.altomni.apn.application.dto.RecruitmentProcessNodePageSectionDTO;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;

import java.util.List;

/**
 * Service Interface for managing {@link RecruitmentProcessNodePageSection}.
 */
public interface RecruitmentProcessNodePageSectionService {

    List<RecruitmentProcessNodePageSectionDTO> findAllRecruitmentProcessNodePageSectionsByNodeId(JobType jobType, NodeType nodeType);

    List<RecruitmentProcessNodePageSectionDTO> create(List<RecruitmentProcessNodePageSection> sections);

    RecruitmentProcessNodePageSectionDTO create(Long recruitmentProcessId, NodeType nodeType, List<FieldConfigDTO> fieldConfigDTOS);

    List<RecruitmentProcessNodePageSectionDTO> initByDefaultConfig(Long recruitmentProcessId);

    RecruitmentProcessNodePageSectionDTO update(Long recruitmentProcessId, NodeType nodeType, List<FieldConfigDTO> fieldConfigDTOS);

    List<RecruitmentProcessNodePageSectionDTO> findGeneralRecruitmentProcessNodePageSectionsDefaultConfig();

    RecruitmentProcessNodePageSectionDTO findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(JobType jobType, NodeType nodeType);

    List<RecruitmentProcessNodePageSectionDTO> findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(Long recruitmentProcessId);

    RecruitmentProcessNodePageSectionDTO findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(Long recruitmentProcessId, NodeType nodeType);
}
