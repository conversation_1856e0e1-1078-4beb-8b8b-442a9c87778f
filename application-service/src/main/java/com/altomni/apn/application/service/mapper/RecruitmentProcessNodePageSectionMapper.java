package com.altomni.apn.application.service.mapper;

import com.altomni.apn.application.domain.RecruitmentProcessNodePageSection;
import com.altomni.apn.application.dto.RecruitmentProcessNodePageSectionDTO;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity {@link RecruitmentProcessNodePageSection} and its DTO {@link RecruitmentProcessNodePageSectionDTO}.
 */
@Mapper(componentModel = "spring", uses = {})
public interface RecruitmentProcessNodePageSectionMapper extends EntityMapper<RecruitmentProcessNodePageSectionDTO, RecruitmentProcessNodePageSection> {

    default RecruitmentProcessNodePageSection fromId(Long id) {
        if (id == null) {
            return null;
        }
        RecruitmentProcessNodePageSection nodePageSection = new RecruitmentProcessNodePageSection();
        nodePageSection.setId(id);
        return nodePageSection;
    }
}
