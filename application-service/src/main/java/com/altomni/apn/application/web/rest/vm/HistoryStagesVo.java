package com.altomni.apn.application.web.rest.vm;

import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.*;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class HistoryStagesVo {

    private Long talentRecruitmentProcessId;

    private Long talentId;

    private Long jobId;

    private TalentRecruitmentProcessSubmitToJobVO submitToJob;

    private TalentRecruitmentProcessSubmitToClientVO submitToClient;

    private List<TalentRecruitmentProcessInterviewVO> interviews;

    private TalentRecruitmentProcessOfferVO offer;

    private TalentRecruitmentProcessIpgOfferAcceptVO offerAccept;

    private TalentRecruitmentProcessCommissionVO commission;

    private TalentRecruitmentProcessOnboardVO onboard;

    private TalentRecruitmentProcessEliminateVO eliminate;


    public HistoryStagesVo(TalentRecruitmentProcess talentRecruitmentProcess) {
        this.talentRecruitmentProcessId = talentRecruitmentProcess.getId();
        this.talentId = talentRecruitmentProcess.getTalentId();
        this.jobId = talentRecruitmentProcess.getJobId();
    }
}
