package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgAgreedPayRate;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * Spring Data SQL repository for the TalentRecruitmentProcessIpgAgreedPayRate entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessIpgAgreedPayRateRepository extends JpaRepository<TalentRecruitmentProcessIpgAgreedPayRate, Long> {

    TalentRecruitmentProcessIpgAgreedPayRate findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessIpgAgreedPayRate> findAllByTalentRecruitmentProcessIdIn(Collection<Long> talentRecruitmentProcessIds);
}
