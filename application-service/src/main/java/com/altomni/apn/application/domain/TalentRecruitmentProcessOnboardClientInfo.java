package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Entity
@Table(name = "talent_recruitment_process_onboard_client_info")
public class TalentRecruitmentProcessOnboardClientInfo extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 3085979140546737718L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "client_contact_id")
    private Long clientContactId;

    @Column(name = "client_name")
    private String clientName;

    @Column(name = "client_division")
    private String clientDivision;

    @Column(name = "client_address")
    private String clientAddress;

    @Column(name = "client_email")
    private String clientEmail;

    @Column(name = "client_location")
    private String clientLocation;

    @Column(name = "client_info_id")
    private Long clientInfoId;

    @Column(name = "invoice_type_id")
    private Long invoiceTypeId;
}
