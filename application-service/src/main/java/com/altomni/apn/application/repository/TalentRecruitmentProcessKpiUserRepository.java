package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessKpiUser;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.transaction.Transactional;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * Spring Data SQL repository for the TalentRecruitmentProcessKpiUser entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessKpiUserRepository extends JpaRepository<TalentRecruitmentProcessKpiUser, Long> {

    List<TalentRecruitmentProcessKpiUser> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessKpiUser> findAllByTalentRecruitmentProcessIdAndUserRoleIn(Long talentRecruitmentProcessId, List<UserRole> userRoles);

    @Query("SELECT kpiUser FROM TalentRecruitmentProcessKpiUser kpiUser WHERE kpiUser.talentRecruitmentProcessId = ?1 AND (kpiUser.userRole NOT IN ?2 OR kpiUser.userRole IS NULL)")
    List<TalentRecruitmentProcessKpiUser> findAllByTalentRecruitmentProcessIdAndUserRoleNotIn(Long talentRecruitmentProcessId, List<UserRole> userRoles);

    @Query(value = "select trpku.* from talent_recruitment_process_kpi_user trpku LEFT JOIN talent_recruitment_process trp " +
            "on trp.id = trpku.talent_recruitment_process_id " +
            "where trp.job_id = ?1 and trpku.user_role<>?2  GROUP BY trpku.user_id", nativeQuery = true)
    List<TalentRecruitmentProcessKpiUser> findAllByJobId(Long jobId, Integer userRole);

    @Query(value = "select trpku.* from talent_recruitment_process_kpi_user trpku left join talent_recruitment_process trp " +
            "on trp.id = trpku.talent_recruitment_process_id " +
            "where trp.talent_id = ?1 and trp.id <> ?2", nativeQuery = true)
    List<TalentRecruitmentProcessKpiUser> findAllByTalentIdAndTalentRecruitmentProcessIdNotIn(Long talentId, Long talentRecruitmentProcessId);

    @Query(value = "select trp.talent_id from talent_recruitment_process_kpi_user trpku " +
            " inner join talent_recruitment_process trp on trp.id=trpku.talent_recruitment_process_id " +
            "where trpku.user_id=:userId and trp.talent_id in :talentIds", nativeQuery = true)
    Set<Long> filterTalentIdsByUserIdAndTalentIds(@Param("userId") Long userId, @Param("talentIds") Set<Long> talentIds);

    List<TalentRecruitmentProcessKpiUser> findAllByTalentRecruitmentProcessIdIn(List<Long> recruitmentProcessIds);

    @Query(value = "select kpi.id, kpi.talent_recruitment_process_id from talent_recruitment_process_kpi_user kpi " +
            "left join talent_recruitment_process_onboard onboard on onboard.talent_recruitment_process_id=kpi.talent_recruitment_process_id " +
            "where kpi.user_id=:resignedUserId and kpi.user_role=4 and onboard.id is null"
            , nativeQuery = true)
    List<Object[]> getKpiOwnersByUserId(@Param("resignedUserId") Long resignedUserId);

    @Modifying
    @Transactional
    @Query(value = "delete from talent_recruitment_process_kpi_user WHERE id in ( ?1 )", nativeQuery = true)
    void deleteKpiUserById(List<Long> idList);

    @Modifying
    @Query(value = "update  talent_recruitment_process_kpi_user  " +
            "set percentage=0 " +
            "where id in :ids"
            , nativeQuery = true)
    void updateCommissionByKpiId(@Param("ids") Collection<Long> ids);

    @Modifying
    @Query(value = "update talent_recruitment_process_kpi_user kpi " +
            "inner join (" +
            "    select talent_recruitment_process_id, 10/count(*) percentage from talent_recruitment_process_kpi_user " +
            "    where talent_recruitment_process_id in :talentRecruitmentProcessIds and user_role=4 and percentage > 0 and user_id !=:resignedUserId " +
            "    group by talent_recruitment_process_id " +
            "    ) temp on temp.talent_recruitment_process_id=kpi.talent_recruitment_process_id " +
            "set kpi.percentage=round(temp.percentage, 2) " +
            "where kpi.talent_recruitment_process_id in :talentRecruitmentProcessIds and kpi.user_role=4 and kpi.percentage > 0 and kpi.user_id !=:resignedUserId "
            , nativeQuery = true)
    void resetCommission(@Param("talentRecruitmentProcessIds") Collection<Long> talentRecruitmentProcessIds, @Param("resignedUserId") Long resignedUserId);

    @Modifying
    @Query(value = "update talent_recruitment_process_kpi_user kpi_u " +
            "inner join ( " +
            "    select kpi.talent_recruitment_process_id, max(kpi.id) kpi_id, 10-sum(kpi.percentage) as diff from talent_recruitment_process_kpi_user kpi " +
            "    where kpi.talent_recruitment_process_id in :talentRecruitmentProcessIds and kpi.user_role=4 and kpi.percentage > 0 and kpi.user_id !=:resignedUserId " +
            "    group by kpi.talent_recruitment_process_id " +
            "    having sum(kpi.percentage) != 10 " +
            ") temp on temp.kpi_id=kpi_u.id " +
            "set kpi_u.percentage = kpi_u.percentage+temp.diff " +
            "where 1=1"
            , nativeQuery = true)
    void resetCommissionForSumPercentageEqualsTo10(@Param("talentRecruitmentProcessIds") Collection<Long> talentRecruitmentProcessIds, @Param("resignedUserId") Long resignedUserId);
}
