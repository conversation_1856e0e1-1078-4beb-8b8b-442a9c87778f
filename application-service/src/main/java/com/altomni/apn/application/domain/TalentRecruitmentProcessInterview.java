package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.InterviewType;
import com.altomni.apn.common.domain.enumeration.application.InterviewTypeConverter;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessInterviewVO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * A TalentRecruitmentProcessInterview.
 */
@Entity
@Table(name = "talent_recruitment_process_interview")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessInterview extends AbstractPermissionNoteAuditingEntity implements Serializable {

    private static final long serialVersionUID = 8175428011204886448L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "progress")
    private Integer progress;

    @Column(name = "from_time")
    private Instant fromTime;

    @Column(name = "to_time")
    private Instant toTime;

    @Column(name = "interview_type")
    @Convert(converter = InterviewTypeConverter.class)
    private InterviewType interviewType;

    @Column(name = "time_zone")
    private String timeZone;

    @Column(name = "note")
    private String note;

    @Column(name = "final_round")
    private Integer finalRound;

    public static TalentRecruitmentProcessInterview fromVO(TalentRecruitmentProcessInterviewVO interviewVO) {
        TalentRecruitmentProcessInterview result = new TalentRecruitmentProcessInterview();
        ServiceUtils.myCopyProperties(interviewVO, result);
        return result;
    }

    @Override
    public String getNoteValue() {
        return note;
    }
}
