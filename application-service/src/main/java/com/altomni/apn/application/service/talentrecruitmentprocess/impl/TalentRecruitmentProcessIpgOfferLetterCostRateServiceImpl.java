package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgContractFeeCharge;
import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgOfferLetterCostRate;
import com.altomni.apn.application.repository.TalentRecruitmentProcessIpgOfferLetterCostRateRepository;
import com.altomni.apn.application.service.job.JobClient;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateService;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.dict.EnumCurrency;
import com.altomni.apn.common.domain.enumeration.application.OfferLetterCostRateType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgContractFeeChargeVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateList;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgOfferLetterCostRateVO;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.enums.EnumCurrencyService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.talent.constants.Constants;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessIpgContractFeeCharge}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessIpgOfferLetterCostRateServiceImpl implements TalentRecruitmentProcessIpgOfferLetterCostRateService {

    @Resource
    private JobClient jobClient;

    @Resource
    private TalentRecruitmentProcessIpgOfferLetterCostRateRepository offerLetterCostRateRepository;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private EnumCurrencyService enumCurrencyService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Override
    public void validateOfferLetterCostRate(TalentRecruitmentProcessIpgContractFeeChargeVO contractFeeCharge) {
        if (StringUtils.isNotEmpty(contractFeeCharge.getTaxBurdenRateCode())) {
            TalentRecruitmentProcessIpgOfferLetterCostRate offerLetterCostRate = offerLetterCostRateRepository.findByCode(contractFeeCharge.getTaxBurdenRateCode());
            if (offerLetterCostRate == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(contractFeeCharge.getTaxBurdenRateCode()), applicationApiPromptProperties.getAppl()));
            }
            if (!offerLetterCostRate.getTenantId().equals(SecurityUtils.getTenantId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATENOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
        }
        if (StringUtils.isNotEmpty(contractFeeCharge.getMspRateCode())) {
            TalentRecruitmentProcessIpgOfferLetterCostRate offerLetterCostRate = offerLetterCostRateRepository.findByCode(contractFeeCharge.getMspRateCode());
            if (offerLetterCostRate == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATEMAPISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(contractFeeCharge.getMspRateCode()), applicationApiPromptProperties.getAppl()));
            }
            if (!offerLetterCostRate.getTenantId().equals(SecurityUtils.getTenantId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATEMAPNOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
        }
        if (StringUtils.isNotEmpty(contractFeeCharge.getImmigrationCostCode())) {
            TalentRecruitmentProcessIpgOfferLetterCostRate offerLetterCostRate = offerLetterCostRateRepository.findByCode(contractFeeCharge.getImmigrationCostCode());
            if (offerLetterCostRate == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTIMMIGRATIONISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(contractFeeCharge.getImmigrationCostCode()), applicationApiPromptProperties.getAppl()));
            }
            if (!offerLetterCostRate.getTenantId().equals(SecurityUtils.getTenantId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTRATEIMMIGRATIONNOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
        }
    }

    @Override
    public TalentRecruitmentProcessIpgContractFeeChargeVO addEntities(TalentRecruitmentProcessIpgContractFeeChargeVO contractFeeCharge) {
        if (contractFeeCharge != null) {
            if (StringUtils.isNotEmpty(contractFeeCharge.getTaxBurdenRateCode())) {
                contractFeeCharge.setTaxBurdenRate(toVO(offerLetterCostRateRepository.findByCode(contractFeeCharge.getTaxBurdenRateCode())));
            }
            if (StringUtils.isNotEmpty(contractFeeCharge.getMspRateCode())) {
                contractFeeCharge.setMspRate(toVO(offerLetterCostRateRepository.findByCode(contractFeeCharge.getMspRateCode())));
            }
            if (StringUtils.isNotEmpty(contractFeeCharge.getImmigrationCostCode())) {
                contractFeeCharge.setImmigrationCost(toVO(offerLetterCostRateRepository.findByCode(contractFeeCharge.getImmigrationCostCode())));
            }
        }
        return contractFeeCharge;
    }

    @Override
    public TalentRecruitmentProcessIpgOfferLetterCostRateList findAll(Long recruitmentProcessId) {
        List<TalentRecruitmentProcessIpgOfferLetterCostRate> offerLetterCostRates = offerLetterCostRateRepository.findAllByTenantIdAndRecruitmentProcessIdIsNull(SecurityUtils.getTenantId());
        List<EnumCurrency> currencyList = enumCurrencyService.findAllEnumCurrency();
        List<Integer> allCurrencyIdList = currencyList.stream().map(EnumCurrency::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(offerLetterCostRates)) {
            offerLetterCostRates = new ArrayList<>();
        }
        List<Integer> existsIdList = offerLetterCostRates.stream().map(TalentRecruitmentProcessIpgOfferLetterCostRate::getCurrency).distinct().collect(Collectors.toList());
        List<Integer> addCurrencyList = allCurrencyIdList.stream().filter(id -> !existsIdList.contains(id)).collect(Collectors.toList());
        TalentRecruitmentProcessIpgOfferLetterCostRateList talentRecruitmentProcessIpgOfferLetterCostRateList = CollectionUtils.isEmpty(addCurrencyList) ? toList(offerLetterCostRates) : toList(init(addCurrencyList, SecurityUtils.getTenantId()));
        if(recruitmentProcessId != null) {
            addRecruitmentProcessLetterCostRate(talentRecruitmentProcessIpgOfferLetterCostRateList, recruitmentProcessId, currencyList);
        }
        return talentRecruitmentProcessIpgOfferLetterCostRateList;
    }

    private void addRecruitmentProcessLetterCostRate(TalentRecruitmentProcessIpgOfferLetterCostRateList talentRecruitmentProcessIpgOfferLetterCostRateList, Long recruitmentProcessId, List<EnumCurrency> currencyList) {
        List<TalentRecruitmentProcessIpgOfferLetterCostRate> recruitmentProcessIpgOfferLetterCostRate =
                offerLetterCostRateRepository.findAllByTenantIdAndRecruitmentProcessIdIs(SecurityUtils.getTenantId(), recruitmentProcessId);

        if (!CollectionUtils.isEmpty(recruitmentProcessIpgOfferLetterCostRate)) {
            // 处理 currency = -1 的情况
            List<TalentRecruitmentProcessIpgOfferLetterCostRate> allCurrencyRates = recruitmentProcessIpgOfferLetterCostRate.stream()
                    .filter(rate -> rate.getCurrency() != null && rate.getCurrency() == -1)
                    .collect(Collectors.toList());

            // 创建针对所有币种的记录
            List<TalentRecruitmentProcessIpgOfferLetterCostRate> expandedRates = new ArrayList<>();
            for (TalentRecruitmentProcessIpgOfferLetterCostRate rate : allCurrencyRates) {
                for (EnumCurrency currency : currencyList) {
                    TalentRecruitmentProcessIpgOfferLetterCostRate newRate = new TalentRecruitmentProcessIpgOfferLetterCostRate();
                    BeanUtils.copyProperties(rate, newRate);
                    newRate.setCurrency(currency.getId());
                    expandedRates.add(newRate);
                }
            }

            // 获取非-1的记录
            List<TalentRecruitmentProcessIpgOfferLetterCostRate> specificCurrencyRates = recruitmentProcessIpgOfferLetterCostRate.stream()
                    .filter(rate -> rate.getCurrency() != null && rate.getCurrency() != -1)
                    .collect(Collectors.toList());

            // 合并所有记录
            expandedRates.addAll(specificCurrencyRates);

            // 使用新的记录更新或替换原有记录
            for (TalentRecruitmentProcessIpgOfferLetterCostRate newRate : expandedRates) {
                switch (newRate.getRateType()) {
                    case TAX_BURDEN_RATE:
                        List<TalentRecruitmentProcessIpgOfferLetterCostRateVO> taxBurdenRate = talentRecruitmentProcessIpgOfferLetterCostRateList.getTaxBurdenRate();
                        if(taxBurdenRate == null) {
                            taxBurdenRate = new ArrayList<>();
                        }
                        taxBurdenRate.add(toVO(newRate));
                        talentRecruitmentProcessIpgOfferLetterCostRateList.setTaxBurdenRate(taxBurdenRate);
                        break;
                    case MSP_RATE:
                        List<TalentRecruitmentProcessIpgOfferLetterCostRateVO> mspRate = talentRecruitmentProcessIpgOfferLetterCostRateList.getMspRate();
                        if(mspRate == null) {
                            mspRate = new ArrayList<>();
                        }
                        mspRate.add(toVO(newRate));
                        talentRecruitmentProcessIpgOfferLetterCostRateList.setMspRate(mspRate);
                        break;
                    case IMMIGRATION_COST:
                        List<TalentRecruitmentProcessIpgOfferLetterCostRateVO> immigrationCost = talentRecruitmentProcessIpgOfferLetterCostRateList.getImmigrationCost();
                        if(immigrationCost == null) {
                            immigrationCost = new ArrayList<>();
                        }
                        immigrationCost.add(toVO(newRate));
                        talentRecruitmentProcessIpgOfferLetterCostRateList.setImmigrationCost(immigrationCost);
                        break;
                }
            }
        }
    }

    private TalentRecruitmentProcessIpgOfferLetterCostRateVO toVO(TalentRecruitmentProcessIpgOfferLetterCostRate entity) {
        if (entity != null) {
            TalentRecruitmentProcessIpgOfferLetterCostRateVO vo = new TalentRecruitmentProcessIpgOfferLetterCostRateVO();
            ServiceUtils.myCopyProperties(entity, vo);
            return vo;
        }
        return null;
    }

    private TalentRecruitmentProcessIpgOfferLetterCostRateList toList(List<TalentRecruitmentProcessIpgOfferLetterCostRate> offerLetterCostRates) {
        TalentRecruitmentProcessIpgOfferLetterCostRateList result = new TalentRecruitmentProcessIpgOfferLetterCostRateList();
        result.setTaxBurdenRate(offerLetterCostRates.stream()
                .filter(offerLetterCostRate -> offerLetterCostRate.getRateType().equals(OfferLetterCostRateType.TAX_BURDEN_RATE))
                .map(this::toVO)
                .collect(Collectors.toList()));
        result.setMspRate(offerLetterCostRates.stream()
                .filter(offerLetterCostRate -> offerLetterCostRate.getRateType().equals(OfferLetterCostRateType.MSP_RATE))
                .map(this::toVO)
                .collect(Collectors.toList()));
        result.setImmigrationCost(offerLetterCostRates.stream()
                .filter(offerLetterCostRate -> offerLetterCostRate.getRateType().equals(OfferLetterCostRateType.IMMIGRATION_COST))
                .map(this::toVO)
                .collect(Collectors.toList()));
        return result;
    }

    private List<TalentRecruitmentProcessIpgOfferLetterCostRate> init(List<Integer> addCurrencyList, Long tenantId) {
        // init default rates for the tenant that created in the future
        List<TalentRecruitmentProcessIpgOfferLetterCostRate> offerLetterCostRates = new ArrayList<>();
        offerLetterCostRates.addAll(createZeros(addCurrencyList, tenantId, OfferLetterCostRateType.TAX_BURDEN_RATE));
        offerLetterCostRates.addAll(createZeros(addCurrencyList, tenantId, OfferLetterCostRateType.MSP_RATE));
        offerLetterCostRates.addAll(createZeros(addCurrencyList, tenantId, OfferLetterCostRateType.IMMIGRATION_COST));
        offerLetterCostRateRepository.saveAll(offerLetterCostRates);
        return offerLetterCostRateRepository.findAllByTenantIdAndRecruitmentProcessIdIsNull(tenantId);
    }

    private List<TalentRecruitmentProcessIpgOfferLetterCostRate> createZeros(List<Integer> addCurrencyList, Long tenantId, OfferLetterCostRateType rateType) {
        List<TalentRecruitmentProcessIpgOfferLetterCostRate> zeros = new ArrayList<>();
        List<EnumCurrency> enumCurrencyList = enumCommonService.findAllEnumCurrency();
        for (EnumCurrency enumCurrency : enumCurrencyList) {
            if (!addCurrencyList.contains(enumCurrency.getId())) {
                continue;
            }
            TalentRecruitmentProcessIpgOfferLetterCostRate offerLetterCostRate = new TalentRecruitmentProcessIpgOfferLetterCostRate();
            offerLetterCostRate.setTenantId(tenantId);
            offerLetterCostRate.setExpireDate(DateUtil.maxDate());
            offerLetterCostRate.setRateType(rateType);
            offerLetterCostRate.setCode(tenantId + "_" + enumCurrency.getName() + "_" + rateType.name() + "_" + Constants.DEFAULT_ZERO_CODE + "_" + Constants.DEFAULT_ZERO_DESC);
            offerLetterCostRate.setDescription(Constants.DEFAULT_ZERO_DESC);
            offerLetterCostRate.setValue(new BigDecimal(0));
            offerLetterCostRate.setCurrency(enumCurrency.getId());
            zeros.add(offerLetterCostRate);
        }
        return zeros;
    }

    @Override
    public TalentRecruitmentProcessIpgOfferLetterCostRateVO findOne(String code) {
        return toVO(validateTenant(offerLetterCostRateRepository.findByCode(code)));
    }

    private TalentRecruitmentProcessIpgOfferLetterCostRate validateTenant(TalentRecruitmentProcessIpgOfferLetterCostRate offerLetterCostRate) {
        if (SecurityUtils.isSystemAdmin() || SecurityUtils.isAdmin()) {
            return offerLetterCostRate;
        }
        if (offerLetterCostRate != null && !SecurityUtils.isCurrentTenant(offerLetterCostRate.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_OFFERLETTERCOSTVALIDATETENANT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        return offerLetterCostRate;
    }

}
