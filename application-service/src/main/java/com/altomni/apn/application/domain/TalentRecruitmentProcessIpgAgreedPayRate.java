package com.altomni.apn.application.domain;

import com.altomni.apn.common.config.constants.CurrencyConstants;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.RateUnitType;
import com.altomni.apn.common.domain.enumeration.RateUnitTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A TalentRecruitmentProcessIpgAgreedPayRate.
 */
@Entity
@Table(name = "talent_recruitment_process_ipg_agreed_pay_rate")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessIpgAgreedPayRate extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -2449433446313322640L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @JsonIgnore
    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "currency")
    private Integer currency = CurrencyConstants.USD;

    @Convert(converter = RateUnitTypeConverter.class)
    @Column(name = "rate_unit_type")
    private RateUnitType rateUnitType;

    @Column(name = "agreed_pay_rate", precision=20, scale=2)
    private BigDecimal agreedPayRate = BigDecimal.ZERO;
}
