package com.altomni.apn.application.service.common;

import cn.hutool.json.JSONArray;
import com.altomni.apn.common.dto.calendar.CalendarEventDTO;
import com.altomni.apn.common.dto.calendar.NoPoachingRemindDTO;
import com.altomni.apn.common.vo.calendar.CalendarEventVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Component
@FeignClient(value = "common-service")
public interface CommonClient {

    @PostMapping("/common/api/v3/calendar-event")
    ResponseEntity<Void> createCalendarEvent(@Valid @RequestBody CalendarEventDTO calendarEventDto);

    @PutMapping("/common/api/v3/calendar-event")
    ResponseEntity<Void> updateCalendarEvent(@Valid @RequestBody CalendarEventDTO calendarEventDto);

    @GetMapping("/common/api/v3/calendar-event/{typeId}/{referenceId}")
    ResponseEntity<CalendarEventVO> getCalendarEventByTypeIdAndReferenceId(@PathVariable("typeId") Integer typeId, @PathVariable("referenceId") Long referenceId);

    /**
     * 禁猎公司推荐候选人到职位时给责任人发送点一点Lark消息
     */
    @PostMapping("/common/api/v3/lark-client/send-message-for-no-nopoaching")
    ResponseEntity<Void> noPoachingSendMessageByUserEmails(@RequestBody NoPoachingRemindDTO remindDTO);

    @DeleteMapping("/common/api/v3/calendar-event/relationIds")
    ResponseEntity<JSONArray> deleteCalendarEventsByRecruitmentProcessRelationIds(@RequestBody List<Long> referenceIds);


}
