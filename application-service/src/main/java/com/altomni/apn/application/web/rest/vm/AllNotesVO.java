package com.altomni.apn.application.web.rest.vm;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class AllNotesVO {

    private Long talentRecruitmentProcessId;

    private Long talentId;

    private Long jobId;

    private List<NodeAndNote> notes;

    public AllNotesVO(Long talentRecruitmentProcessId, Long talentId, Long jobId) {
        this.talentRecruitmentProcessId = talentRecruitmentProcessId;
        this.talentId = talentId;
        this.jobId = jobId;
    }
}
