package com.altomni.apn.application.service.recruitmentprocess.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.RecruitmentProcess;
import com.altomni.apn.application.domain.RecruitmentProcessNode;
import com.altomni.apn.application.repository.RecruitmentProcessRepository;
import com.altomni.apn.application.service.recruitmentprocess.RecruitmentProcessNodePageSectionService;
import com.altomni.apn.application.service.recruitmentprocess.RecruitmentProcessNodeService;
import com.altomni.apn.application.service.recruitmentprocess.RecruitmentProcessService;
import com.altomni.apn.application.service.user.UserService;
import com.altomni.apn.application.utils.ExcelUtil;
import com.altomni.apn.application.web.rest.vm.UnfinishedApplicationVM;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.recruitmentprocess.*;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing TenantRecruitmentProcess.
 */
@Service
@Transactional
public class RecruitmentProcessServiceImpl implements RecruitmentProcessService {

    private final Logger log = LoggerFactory.getLogger(RecruitmentProcessServiceImpl.class);

    @Resource
    private RecruitmentProcessRepository recruitmentProcessRepository;
    @Resource
    private RecruitmentProcessNodeService recruitmentProcessNodeService;
    @Resource
    private RecruitmentProcessNodePageSectionService recruitmentProcessNodePageSectionService;

    @Resource
    private UserService userService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    private static final List<NodeType> DEFAULT_NODE_TYPES = Arrays.asList(NodeType.SUBMIT_TO_JOB, NodeType.SUBMIT_TO_CLIENT, NodeType.INTERVIEW, NodeType.OFFER, NodeType.COMMISSION, NodeType.ON_BOARD);

    private static final Set<NodeType> REQUIRED_NODE_TYPES = new HashSet<>(Arrays.asList(NodeType.SUBMIT_TO_JOB, NodeType.INTERVIEW, NodeType.OFFER, NodeType.ON_BOARD));

    @Override
    @Transactional
    public RecruitmentProcessVO create(RecruitmentProcessVO recruitmentProcessVO) {
        log.debug("Request to create RecruitmentProcessVO : {}", recruitmentProcessVO);
        if (recruitmentProcessVO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_CREATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        checkRecruitmentProcessNodes(recruitmentProcessVO.getRecruitmentProcessNodes());

        RecruitmentProcess recruitmentProcess = new RecruitmentProcess();
        recruitmentProcess.setName(recruitmentProcessVO.getName()); //TODO: check duplicate name?
        recruitmentProcess.setDescription(recruitmentProcessVO.getDescription());
        recruitmentProcess.setTenantId(recruitmentProcessVO.getTenantId());
        recruitmentProcess.setStatus(ActiveStatus.ACTIVE);
//        recruitmentProcess.setRecruitmentProcessType(RecruitmentProcessType.FIRST_PARTY); //TODO
        recruitmentProcess.setJobType(JobType.OTHERS);
        RecruitmentProcess result = recruitmentProcessRepository.save(recruitmentProcess);
        // use another back-end admin level to create default recruitment process for IPG, DO NOT LET users to do that!
//        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(recruitmentProcess.getTenantId())) {
//            recruitmentProcessNodeService.initRecruitmentProcessNodeForIpg(result);
//            return toVO(result);
//        }

//        if (RecruitmentProcessType.FIRST_PARTY.equals(result.getRecruitmentProcessType())) {
//            recruitmentProcessNodeService.initRecruitmentProcessNodeForFirstParty(result);
//        } else {
//            recruitmentProcessNodeService.initRecruitmentProcessNodeForSecondParty(result);
//        };
        recruitmentProcessNodeService.initRecruitmentProcessNode(recruitmentProcessVO.getRecruitmentProcessNodes().stream().peek(n -> {
            n.setRecruitmentProcessId(result.getId());
            n.setTenantId(recruitmentProcessVO.getTenantId());
        }).collect(Collectors.toList()));
        recruitmentProcessNodePageSectionService.initByDefaultConfig(result.getId());
        return toVO(result);
    }

    private void checkRecruitmentProcessNodes(List<RecruitmentProcessNodeVO> recruitmentProcessNodes) {
        if (CollectionUtils.isEmpty(recruitmentProcessNodes)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESS_CREATENOTEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
        }
        Set<NodeType> nodes = new HashSet<>();
        recruitmentProcessNodes.forEach(n -> {
            if (Objects.isNull(n.getNodeType())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESS_CREATENOTETYPEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
            }
            if (!nodes.add(n.getNodeType())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESS_CREATEDUPLICATENOTETYPE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
            }
            if (StringUtils.isBlank(n.getName())) {
                n.setName(n.getNodeType().toString());
            }
        });
        if (!nodes.containsAll(REQUIRED_NODE_TYPES)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESS_CREATEMISSNOTE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
        }

        //TODO: first party cannot have submit to client node
    }

    @Override
    public RecruitmentProcessVO update(Long id, RecruitmentProcessVO recruitmentProcessVO) {
        log.debug("Request to update RecruitmentProcess : {}", recruitmentProcessVO);
        RecruitmentProcess exist = findOneById(id);
        exist.setName(recruitmentProcessVO.getName());
        exist.setDescription(recruitmentProcessVO.getDescription());
        return toVOWithoutNodes(recruitmentProcessRepository.save(exist));
    }

    @Override
    public RecruitmentProcessVO updateStatus(Long id, ActiveStatus status) {
        log.debug("Request to update status for RecruitmentProcessId : {}", id);
        Long applicationCount = recruitmentProcessRepository.getInProgressApplicationCountByRecruitmentProcessId(id);
        if (!Objects.equals(applicationCount, 0L)) {
            throw new CustomParameterizedException("You still have active applications which using current recruitment process, please finish them first!");
        }
        RecruitmentProcess recruitmentProcess = findOneById(id);
        Long jobCount = recruitmentProcessRepository.getInProgressJobCountByRecruitmentProcessId(id);
        if (!Objects.equals(jobCount, 0L)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESS_UPDATEIDISZERO.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
        }

        recruitmentProcess.setStatus(status);
        RecruitmentProcessVO res = toVOWithoutNodes(recruitmentProcessRepository.save(recruitmentProcess));
        if (ActiveStatus.INACTIVE.equals(status)) {
            userService.clearJobPreferenceByRecruitmentProcessId(id);
        }
        return res;
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RecruitmentProcessVO> findAll(ActiveStatus status, Pageable pageable) {
        log.debug("Request to get all RecruitmentProcess");
        Page<RecruitmentProcess> recruitmentProcesses;
//        if (SecurityUtils.isAdmin()) {
//            if (Objects.isNull(status)) {
//                recruitmentProcesses = recruitmentProcessRepository.findAll(pageable);
//            } else {
//                recruitmentProcesses = recruitmentProcessRepository.findAllByStatus(status, pageable);
//            }
//        } else {
//            if (Objects.isNull(status)) {
//                recruitmentProcesses = recruitmentProcessRepository.findAllByTenantId(SecurityUtils.getTenantId(), pageable);
//            } else {
//                recruitmentProcesses = recruitmentProcessRepository.findAllByTenantIdAndStatus(SecurityUtils.getTenantId(), status, pageable);
//            }
//        }
        if (Objects.isNull(status)) {
            recruitmentProcesses = recruitmentProcessRepository.findAllByTenantId(SecurityUtils.getTenantId(), pageable);
        } else {
            recruitmentProcesses = recruitmentProcessRepository.findAllByTenantIdAndStatus(SecurityUtils.getTenantId(), status, pageable);
        }
//        return CollectionUtils.isNotEmpty(recruitmentProcesses) ?
//            recruitmentProcesses.stream().map(this::toVOWithoutNodes).collect(Collectors.toList()) : Collections.emptyList();
        return new PageImpl<>(recruitmentProcesses.stream().map(this::toVOWithoutNodes).collect(Collectors.toList()), recruitmentProcesses.getPageable(), recruitmentProcesses.getTotalPages());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RecruitmentProcessVO> findAllForPrivateJob(Pageable pageable) {
        log.debug("Request to get all RecruitmentProcess");
        Page<RecruitmentProcess> recruitmentProcesses = recruitmentProcessRepository.findAllByTenantIdAndStatusAndJobTypeNot(SecurityUtils.getTenantId(), ActiveStatus.ACTIVE, JobType.PAY_ROLL, pageable);
        return new PageImpl<>(recruitmentProcesses.stream().map(this::toVOWithoutNodes).collect(Collectors.toList()), recruitmentProcesses.getPageable(), recruitmentProcesses.getTotalPages());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<RecruitmentProcessVO> findAllByTenantId(Long tenantId, ActiveStatus status, Pageable pageable) {
        Page<RecruitmentProcess> recruitmentProcesses;
        if (Objects.isNull(status)) {
            recruitmentProcesses = recruitmentProcessRepository.findAllByTenantId(tenantId, pageable);
        } else {
            recruitmentProcesses = recruitmentProcessRepository.findAllByTenantIdAndStatus(tenantId, status, pageable);
        }
//        return CollectionUtils.isNotEmpty(recruitmentProcesses) ?
//                recruitmentProcesses.stream().map(this::toVOWithoutNodes).collect(Collectors.toList()) : Collections.emptyList();
        return new PageImpl<>(recruitmentProcesses.stream().map(this::toVOWithoutNodes).collect(Collectors.toList()), recruitmentProcesses.getPageable(), recruitmentProcesses.getTotalElements());
    }

    private RecruitmentProcess findOneById(Long id) {
        Optional<RecruitmentProcess> recruitmentProcess = recruitmentProcessRepository.findById(id);
        if (recruitmentProcess.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESS_FINDBYID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(id),applicationApiPromptProperties.getAppl()));
        }
        if (!SecurityUtils.isSystemAdmin() && !SecurityUtils.getTenantId().equals(recruitmentProcess.get().getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESS_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));        }
        return recruitmentProcess.get();
    }

    /**
     * Get one tenantRecruitmentProcess by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public RecruitmentProcessVO findOne(Long id) {
        log.debug("Request to get RecruitmentProcess : {}", id);
        return toVO(findOneById(id));
    }

    private RecruitmentProcessVO toVO(RecruitmentProcess recruitmentProcess) {
        if (recruitmentProcess == null) {
            return null;
        }
        RecruitmentProcessVO result = new RecruitmentProcessVO();
        ServiceUtils.myCopyProperties(recruitmentProcess, result);
        result.setRecruitmentProcessNodes(recruitmentProcessNodeService.findAll(recruitmentProcess.getId()));
        return result;
    }

    private RecruitmentProcessVO toVOWithoutNodes(RecruitmentProcess recruitmentProcess) {
        if (recruitmentProcess == null) {
            return null;
        }
        RecruitmentProcessVO result = new RecruitmentProcessVO();
        ServiceUtils.myCopyProperties(recruitmentProcess, result);
        return result;
    }

    /**
     * Delete the tenantRecruitmentProcess by id.
     *
     * @param id the id of the entity
     */
    @Override
    public void delete(Long id) {
        log.debug("Request to delete RecruitmentProcess : {}", id);
        RecruitmentProcess exist = findOneById(id);
        recruitmentProcessRepository.delete(exist);
    }

    @Override
    public RecruitmentProcessVO getDefaultRecruitmentProcess(JobType jobType) {
        RecruitmentProcess exist = recruitmentProcessRepository.findByTenantIdAndStatusAndJobType(SecurityUtils.getTenantId(), ActiveStatus.ACTIVE, jobType);
        if (exist == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.RECRUITMENTPROCESS_RECRUITMENTPROCESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),applicationApiPromptProperties.getAppl()));
        }
        return toVO(exist);
    }

    @Override
    public List<RecruitmentProcessVO> config(RecruitmentProcessConfigVO configVO) {
        List<RecruitmentProcessVO> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(configVO.getJobTypes())) {
            configVO.getJobTypes().forEach( jobType -> {
                configVO.setJobType(jobType);
                result.add(configOne(configVO));
            });
        } else {
            result.add(configOne(configVO));
        }
        return result;
    }

    @Override
    public List<RecruitmentProcessStats> getClintBoardInterviewStats(List<Long> jobIdList) {
        List<RecruitmentProcessStats> processStatsList = new ArrayList<>();
        List<Object[]> objectList = recruitmentProcessRepository.getClintBoardInterviewStats(jobIdList);
        if (CollUtil.isEmpty(objectList)) {
            return processStatsList;
        }
        objectList.forEach(o -> {
            RecruitmentProcessStats stats = new RecruitmentProcessStats();
            stats.setJobId(((BigInteger) o[0]).longValue());
            stats.setCount(((BigInteger) o[1]).intValue());
            if(ObjectUtil.isNotEmpty(((BigInteger) o[2]).intValue())){
                stats.setNode(NodeType.fromDbValue(((BigInteger) o[2]).intValue()));
            }
            processStatsList.add(stats);
        });
        return processStatsList;
    }

    @Override
    public List<Map<String, Object>> getClintBoardInterviewStatsMap(List<Long> jobIdList) {
        List<RecruitmentProcessStats> list = getClintBoardInterviewStats(jobIdList);
        if (CollUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        Map<Long, List<RecruitmentProcessStats>> listMap = list.stream().collect(Collectors.groupingBy(RecruitmentProcessStats::getJobId));
        List<Map<String, Object>> result = new ArrayList<>();
        listMap.forEach((k,v) -> {
            Map<String, Object> map = new HashMap<>();
            map.put("jobId", k);
            map.put("data", v);
            result.add(map);
        });
        return result;
    }

    public RecruitmentProcessVO configOne(RecruitmentProcessConfigVO configVO) {
//        RecruitmentProcess exist = recruitmentProcessRepository.findByTenantIdAndStatusAndJobType(configVO.getTenantId(), ActiveStatus.ACTIVE, configVO.getJobType());
//        if (exist != null) {
//            throw new DuplicateException("Recruitment process already exists: id="  + exist.getId() + ", name=" + exist.getName()
//                    + ", tenant=" + configVO.getTenantId() + ", jobType=" + configVO.getJobType());
//        }
        RecruitmentProcess recruitmentProcess = RecruitmentProcess.fromConfigVO(configVO);
        recruitmentProcess.setStatus(ActiveStatus.ACTIVE);
        RecruitmentProcess savedRecruitmentProcess = recruitmentProcessRepository.save(recruitmentProcess);
        if (CollectionUtils.isNotEmpty(configVO.getNodeTypes())) {
            List<NodeType> nodeTypes = configVO.getNodeTypes();
            Collections.sort(nodeTypes);
            List<RecruitmentProcessNode> recruitmentProcessNodes = new ArrayList<>();
            nodeTypes.forEach(nodeType -> recruitmentProcessNodes.add(new RecruitmentProcessNode()
                    .recruitmentProcessId(savedRecruitmentProcess.getId())
                    .nodeType(nodeType)
                    .name(nodeType.name())
                    .description("Default")
                    .tenantId(savedRecruitmentProcess.getTenantId())));
            List<RecruitmentProcessNode> savedNodes = recruitmentProcessNodeService.saveAll(recruitmentProcessNodes);
            for (int i = 0; i < savedNodes.size() - 1; i++) {
                savedNodes.get(i).setNextNodeId(savedNodes.get(i+1).getId());
            }
            recruitmentProcessNodeService.saveAll(savedNodes);
        }
        return toVO(recruitmentProcess);
    }

    @Override
    public List<RecruitmentProcess> initForIpg(Long tenantId) {
        RecruitmentProcess payroll = new RecruitmentProcess();
        payroll.setTenantId(tenantId);
        payroll.setName("IPG-PAY_ROLL-流程");
        payroll.setDescription("IPG-PAY_ROLL-流程");
        payroll.setStatus(ActiveStatus.ACTIVE);
//        payroll.setRecruitmentProcessType(RecruitmentProcessType.SECOND_PARTY);
        payroll.setJobType(JobType.PAY_ROLL);
        recruitmentProcessNodeService.initRecruitmentProcessNodeForIpg(recruitmentProcessRepository.save(payroll));

        RecruitmentProcess contract = new RecruitmentProcess();
        contract.setTenantId(tenantId);
        contract.setName("IPG-CONTRACT-流程");
        contract.setDescription("IPG-CONTRACT-流程");
        contract.setStatus(ActiveStatus.ACTIVE);
//        contract.setRecruitmentProcessType(RecruitmentProcessType.SECOND_PARTY);
        contract.setJobType(JobType.CONTRACT);
        recruitmentProcessNodeService.initRecruitmentProcessNodeForIpg(recruitmentProcessRepository.save(contract));

        RecruitmentProcess fte = new RecruitmentProcess();
        fte.setTenantId(tenantId);
        fte.setName("IPG-FULL_TIME流程");
        fte.setDescription("IPG-FULL_TIME流程");
        fte.setStatus(ActiveStatus.ACTIVE);
//        fte.setRecruitmentProcessType(RecruitmentProcessType.SECOND_PARTY);
        fte.setJobType(JobType.FULL_TIME);
        recruitmentProcessNodeService.initRecruitmentProcessNodeForIpg(recruitmentProcessRepository.save(fte));

        RecruitmentProcess noJobType = new RecruitmentProcess();
        noJobType.setTenantId(tenantId);
        noJobType.setName("IPG-No_JobType默认流程");
        noJobType.setDescription("IPG-No_JobType默认流程");
        noJobType.setStatus(ActiveStatus.ACTIVE);
//        noJobType.setRecruitmentProcessType(RecruitmentProcessType.SECOND_PARTY);
        noJobType.setJobType(null);
        recruitmentProcessNodeService.initRecruitmentProcessNodeForIpg(recruitmentProcessRepository.save(noJobType));

        List<RecruitmentProcess> result = new ArrayList<>();
        result.add(payroll);
        result.add(contract);
        result.add(fte);
        result.add(noJobType);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<RecruitmentProcessVO> initDefaultForGeneralRecruitingProcess(Long tenantId) {
        List<RecruitmentProcessVO> result = new ArrayList<>();

        RecruitmentProcessConfigVO configVO = new RecruitmentProcessConfigVO();
        configVO.setName("FTE");
        configVO.setDescription("全职岗位招聘流程");
        configVO.setTenantId(tenantId);
        configVO.setStatus(ActiveStatus.ACTIVE);
        configVO.setNodeTypes(new ArrayList<>(REQUIRED_NODE_TYPES));
        configVO.setJobType(JobType.OTHERS);
        RecruitmentProcessVO recruitmentProcessVO = configOne(configVO);
        recruitmentProcessNodePageSectionService.initByDefaultConfig(recruitmentProcessVO.getId());
        result.add(recruitmentProcessVO);

        return result;
    }

    @Override
    public RecruitmentProcessTalentAndJobStats getInProgressJobCountByRecruitmentProcessId(Long recruitmentProcessId) {
        checkAuthorizationByRecruitmentProcessId(recruitmentProcessId);

        Long applicationCount = recruitmentProcessRepository.getInProgressApplicationCountByRecruitmentProcessId(recruitmentProcessId);
        Long jobCount = recruitmentProcessRepository.getInProgressJobCountByRecruitmentProcessId(recruitmentProcessId);
        RecruitmentProcessTalentAndJobStats res = new RecruitmentProcessTalentAndJobStats(jobCount, applicationCount);
        return res;
    }

    @Override
    public void downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(Long recruitmentProcessId, String language, HttpServletResponse response) {
        checkAuthorizationByRecruitmentProcessId(recruitmentProcessId);

//        List<Long> applicationIds = recruitmentProcessRepository.getInProgressApplicationIdsByRecruitmentProcessId(recruitmentProcessId);
//        List<Long> jobIds = recruitmentProcessRepository.getInProgressJobIdsByRecruitmentProcessId(recruitmentProcessId);

//        List<Object[]> objects = recruitmentProcessRepository.getInProgressJobIdsByRecruitmentProcessId(recruitmentProcessId);
//        List<JobBriefDTO> jobBriefs = objectToJobBrief(objects);

        List<Object[]> unfinishedApplications = recruitmentProcessRepository.getUnfinishedApplicationsByRecruitmentProcessId(recruitmentProcessId);
        List<UnfinishedApplicationVM> unfinishedApplicationVMList = toUnfinishedApplicationVM(unfinishedApplications);

        List<Object[]> unclosedJobs = recruitmentProcessRepository.getUnclosedJobsByRecruitmentProcessId(recruitmentProcessId);
        List<JobBriefDTO> unclosedJobDTOList = objectToJobBrief(unclosedJobs);

//        //dummy data for testing
//        List<JobBriefDTO> jobBriefs = new ArrayList<>();
//        for (int i = 0; i < 3; i++) {
//            JobBriefDTO jobBriefDTO = new JobBriefDTO();
//            jobBriefDTO.setId(10000L + i);
//            jobBriefDTO.setTitle("new FTE job " + i);
//            jobBriefDTO.setStatus(JobStatus.OPEN);
//            jobBriefs.add(jobBriefDTO);
//        }

        try {
            ServletOutputStream outputStream = response.getOutputStream();

            XSSFWorkbook workbook = new XSSFWorkbook();
            ExcelUtil excelUtil = new ExcelUtil();
//            excelUtil.toExcelForUnfinishedApplications(workbook, unfinishedApplicationVMList);
            excelUtil.toExcel(workbook, language, unclosedJobDTOList, unfinishedApplicationVMList);
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();

            HeaderUtil.setFileDownloadHeader(response, "Unfinished Applications for RecruitmentProcessId_" + recruitmentProcessId);


//        try {
//            ServletOutputStream outputStream = response.getOutputStream();
//
//            XSSFWorkbook workbook = new XSSFWorkbook();
//            ExcelUtil excelUtil = new ExcelUtil();
//            excelUtil.toExcelForUnclosedJobs(workbook, jobBriefs);
//            workbook.write(outputStream);
//            workbook.close();
//            outputStream.close();
//
////            if (isGeneralRecruitmentProcess) { // general recruitment process
////                if ("cn".equalsIgnoreCase(language)) {
////                    pdfUtil.createGeneralDetailsOfChargePdfInCN(document, talentRecruitmentProcessVO);
////                } else {
////                    pdfUtil.createGeneralDetailsOfChargePdfInEN(document, talentRecruitmentProcessVO);
////                }
////            } else {
////                if ("cn".equalsIgnoreCase(language)) { // ipg recruitment process
////                    pdfUtil.createDetailsOfChargePdfInCN(document, talentRecruitmentProcessVO);
////                } else {
////                    pdfUtil.createDetailsOfChargePdfInEN(document, talentRecruitmentProcessVO);
////                }
////            }
//
//
//            HeaderUtil.setFileDownloadHeader(response, "Unclosed Jobs for RecruitmentProcessId_" + recruitmentProcessId);

        } catch (IOException e) {
            log.error("[RecruitmentProcessServiceImpl: downloadInProgressJobAndApplicationIdsByRecruitmentProcessId @{}] Exception when output unclosed jobs to Excel file with error {}", SecurityUtils.getUserId(), e.getLocalizedMessage(), e);
        }
    }

    @Override
    public byte[] downloadInProgressJobAndApplicationIdsByRecruitmentProcessId(Long recruitmentProcessId, String language) {
        checkAuthorizationByRecruitmentProcessId(recruitmentProcessId);

        List<Object[]> unfinishedApplications = recruitmentProcessRepository.getUnfinishedApplicationsByRecruitmentProcessId(recruitmentProcessId);
        List<UnfinishedApplicationVM> unfinishedApplicationVMList = toUnfinishedApplicationVM(unfinishedApplications);

        List<Object[]> unclosedJobs = recruitmentProcessRepository.getUnclosedJobsByRecruitmentProcessId(recruitmentProcessId);
        List<JobBriefDTO> unclosedJobDTOList = objectToJobBrief(unclosedJobs);

        try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
             XSSFWorkbook workbook = new XSSFWorkbook()) {

            ExcelUtil excelUtil = new ExcelUtil();
            excelUtil.toExcel(workbook, language, unclosedJobDTOList, unfinishedApplicationVMList);
            workbook.write(byteArrayOutputStream);

            return byteArrayOutputStream.toByteArray();

        } catch (IOException e) {
            log.error("[RecruitmentProcessServiceImpl: downloadInProgressJobAndApplicationIdsByRecruitmentProcessId @{}] Exception when creating Excel file with error {}", SecurityUtils.getUserId(), e.getLocalizedMessage(), e);
            // 这里可以根据需要抛出一个适当的自定义异常
            throw new CustomParameterizedException("Error generating Excel file");
        }
    }


    private List<UnfinishedApplicationVM> toUnfinishedApplicationVM(List<Object[]> objects) {
        List<UnfinishedApplicationVM> res = new ArrayList<>();
        if (CollectionUtils.isEmpty(objects)) {
            return res;
        }
        for (Object[] obj : objects) {


            Long applicationId = Long.valueOf(StringUtil.valueOf(obj[0]));
            Long jobId = Long.valueOf(StringUtil.valueOf(obj[1]));
            String jobTitle = StringUtil.valueOf(obj[2]);
            Long talentId = Long.valueOf(StringUtil.valueOf(obj[3]));
            String talentName = StringUtil.valueOf(obj[4]);
            Instant talentCreatedDate = DateUtil.fromStringToInstant(StringUtil.valueOf(obj[5]));
            NodeType lastNodeType = NodeType.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[6])));
            Instant applicationCreatedDate = DateUtil.fromStringToInstant(StringUtil.valueOf(obj[7]));
            Instant applicationLastModifiedDate = DateUtil.fromStringToInstant(StringUtil.valueOf(obj[8]));
            UnfinishedApplicationVM unfinishedApplicationVM = new UnfinishedApplicationVM(applicationId, jobId, jobTitle, talentId, talentName, talentCreatedDate, lastNodeType, applicationCreatedDate, applicationLastModifiedDate);
            res.add(unfinishedApplicationVM);
        }
        return res;
    }

    private List<JobBriefDTO> objectToJobBrief(List<Object[]> objects) {
        List<JobBriefDTO> result = Lists.newArrayList();
        if (CollectionUtils.isEmpty(objects)) {
            return result;
        }
        for (Object[] obj : objects) {
            JobBriefDTO jobBriefDTO = new JobBriefDTO();
            Long jobId = Long.valueOf(StringUtil.valueOf(obj[0]));
            jobBriefDTO.setId(jobId);
            jobBriefDTO.setTitle(StringUtil.valueOf(obj[1]));
            if(ObjectUtil.isNotEmpty(StringUtil.valueOf(obj[2]))) {
                jobBriefDTO.setStatus(JobStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[2]))));
            }
            jobBriefDTO.setCreatedDate(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[3])));
            jobBriefDTO.setLastModifiedDate(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[4])));
            result.add(jobBriefDTO);
        }
        return result;
    }


    @Override
    public Map<Long, JobType> findAllMyRecruitmentProcessIds(){
        log.info("[APN] get all recruitment process ids by tenant Id", SecurityUtils.getTenantId());
        Map<Long, JobType> ids = recruitmentProcessRepository.findAllIdAndJobTypeByTenantId(SecurityUtils.getTenantId());
        return ids;
    }

    @Override
    public Long countMyActiveRecruitmentProcess(){
        log.info("[APN] count all recruitment process ids by tenant Id", SecurityUtils.getTenantId());
        return recruitmentProcessRepository.countAllByTenantIdAndStatus(SecurityUtils.getTenantId(), ActiveStatus.ACTIVE);
    }

    @Override
    public RecruitmentProcessVO findOneBrief(Long id) {
        return toVOWithoutNodes(findOneById(id));
    }

    @Override
    public List<RecruitmentProcessVO> getAllRecruitmentProcess() {
        List<RecruitmentProcess> processList = recruitmentProcessRepository.findAllByTenantIdAndStatus(SecurityUtils.getTenantId(), ActiveStatus.ACTIVE);
        return processList.stream().map(this::toVOWithoutNodes).collect(Collectors.toList());
    }

    private void checkAuthorizationByRecruitmentProcessId(Long recruitmentProcessId) {
        Optional<RecruitmentProcess> recruitmentProcess = recruitmentProcessRepository.findById(recruitmentProcessId);
        if (recruitmentProcess.isEmpty()) {
            throw new NotFoundException("RecruitmentProcess not found by id: " + recruitmentProcessId);
        }
        if (!SecurityUtils.isSystemAdmin() && !SecurityUtils.getTenantId().equals(recruitmentProcess.get().getTenantId())) {
            throw new ForbiddenException("You're not authorized to download this recruitment process excel");
        }
    }
}
