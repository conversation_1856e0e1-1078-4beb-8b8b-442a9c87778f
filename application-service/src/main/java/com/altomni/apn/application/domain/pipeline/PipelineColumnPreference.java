//package com.altomni.apn.application.domain.pipeline;
//
//import cn.hutool.json.JSONUtil;
//import com.altomni.apn.common.utils.SecurityUtils;
//import com.altomni.apn.common.domain.AbstractAuditingEntity;
//import com.altomni.apn.common.domain.enumeration.search.ModuleType;
//import com.altomni.apn.common.domain.enumeration.search.ModuleTypeConverter;
//import com.altomni.apn.common.dto.application.pipeline.CreationTypeDTO;
//import com.altomni.apn.common.dto.application.pipeline.PipelineColumnPreferenceDTO;
//import com.altomni.apn.common.utils.JsonUtil;
//import com.altomni.apn.common.utils.ServiceUtils;
//import lombok.AllArgsConstructor;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//import javax.persistence.*;
//import java.io.Serializable;
//
///**
// * This method is deprecated because of refactor the pipeline function, move to user-service
// *
// * @deprecated
// * @see #PipelineColumnPreference() in user-service/customConfig
// */
//@Deprecated
//@Data
//@AllArgsConstructor
//@NoArgsConstructor
//@Entity
//@Table(name = "pipeline_column_preference")
//public class PipelineColumnPreference extends AbstractAuditingEntity implements Serializable {
//
//    private static final long serialVersionUID = 3297627398848418727L;
//
//    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
//    private Long id;
//
//    @Column(name = "user_id")
//    private Long userId;
//
//    @Column(name = "creation_type")
//    private String creationType;
//
//    @Column(name = "temp_name")
//    private String tempName;
//
//    @Column(name = "item_sort")
//    private String itemSort;
//
//    @Column(name = "item_sort_all")
//    private String itemSortAll;
//
//    @Column(name = "module")
//    @Convert(converter = ModuleTypeConverter.class)
//    private ModuleType module;
//
//    @Column(name = "page_size")
//    private Integer pageSize;
//
//    @Column(name = "description")
//    private String description;
//
//    @Column(name = "sort_order")
//    private Integer sortOrder;
//
//    @Column(name = "del_flag")
//    private Boolean delFlag;
//
//    @Column(name = "type")
//    private Boolean type;
//
//
//    public static PipelineColumnPreference toEntity(PipelineColumnPreferenceDTO dto) {
//        PipelineColumnPreference entity = new PipelineColumnPreference();
//        ServiceUtils.myCopyProperties(dto, entity);
//        entity.setUserId(SecurityUtils.getUserId());
//        return entity;
//    }
//
//    public static PipelineColumnPreferenceDTO toDto(PipelineColumnPreference entity) {
//        PipelineColumnPreferenceDTO dto = new PipelineColumnPreferenceDTO();
//        ServiceUtils.myCopyProperties(entity, dto);
//        dto.setCreationType(JsonUtil.fromJson(JSONUtil.toJsonStr(entity.getCreationType()), CreationTypeDTO.class));
//        return dto;
//    }
//}
