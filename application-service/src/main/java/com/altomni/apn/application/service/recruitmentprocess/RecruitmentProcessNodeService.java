package com.altomni.apn.application.service.recruitmentprocess;



import com.altomni.apn.application.domain.RecruitmentProcess;
import com.altomni.apn.application.domain.RecruitmentProcessNode;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessNodeVO;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * Service Interface for managing RecruitmentProcess.
 */
public interface RecruitmentProcessNodeService {

    void initRecruitmentProcessNodeForFirstParty(RecruitmentProcess recruitmentProcess);

    void initRecruitmentProcessNodeForSecondParty(RecruitmentProcess recruitmentProcess);

    void initRecruitmentProcessNode(List<RecruitmentProcessNodeVO> recruitmentProcessNodes);

    RecruitmentProcessNode update(Long recruitmentProcessId, Long nodeId, RecruitmentProcessNode recruitmentProcessNode);

    /**
     * Get all the RecruitmentProcessNodes.
     *
     * @return the list of entities
     */
    List<RecruitmentProcessNodeVO> findAll(Long recruitmentProcessId);

    List<RecruitmentProcessNodeVO> findAllByTenantId(Long recruitmentProcessId, Long tenantId);

    List<RecruitmentProcessNode> saveAll(List<RecruitmentProcessNode> nodes);

    void initRecruitmentProcessNodeForIpg(RecruitmentProcess result);
}
