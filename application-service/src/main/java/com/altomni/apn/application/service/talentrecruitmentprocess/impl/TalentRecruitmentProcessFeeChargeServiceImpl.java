package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessKpiUser;
import com.altomni.apn.application.domain.TalentRecruitmentProcessOfferFeeCharge;
import com.altomni.apn.application.repository.TalentRecruitmentProcessFeeChargeRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessFeeChargeService;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.application.FeeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOfferFeeChargeVO;
import com.altomni.apn.common.utils.ServiceUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessKpiUser}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessFeeChargeServiceImpl implements TalentRecruitmentProcessFeeChargeService {

    @Resource
    private TalentRecruitmentProcessFeeChargeRepository feeChargeRepository;

    @Override
    public TalentRecruitmentProcessOfferFeeChargeVO save(Long talentRecruitmentProcessId, TalentRecruitmentProcessOfferFeeChargeVO feeChargeVO) {
        if (feeChargeVO != null) {
            feeChargeRepository.deleteByTalentRecruitmentProcessId(talentRecruitmentProcessId);
            feeChargeVO.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
            return toVO(feeChargeRepository.save(toEntity(feeChargeVO)));
        }
        return null;
    }

    private TalentRecruitmentProcessOfferFeeChargeVO toVO(TalentRecruitmentProcessOfferFeeCharge feeCharge) {
        if (feeCharge != null) {
            TalentRecruitmentProcessOfferFeeChargeVO result = new TalentRecruitmentProcessOfferFeeChargeVO();
            ServiceUtils.myCopyProperties(feeCharge, result);
            if (FeeType.FLAT_AMOUNT.equals(result.getFeeType())) {
                result.setFeeAmount(result.getFeeAmount().setScale(2, RoundingMode.HALF_UP));
            }
            return result;
        }
        return null;
    }

    private TalentRecruitmentProcessOfferFeeCharge toEntity(TalentRecruitmentProcessOfferFeeChargeVO feeChargeVO) {
        if (feeChargeVO != null) {
            TalentRecruitmentProcessOfferFeeCharge result = new TalentRecruitmentProcessOfferFeeCharge();
            ServiceUtils.myCopyProperties(feeChargeVO, result);
            return result;
        }
        return null;
    }

    @Override
    public TalentRecruitmentProcessOfferFeeChargeVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVO(feeChargeRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }
}
