package com.altomni.apn.application.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class TalentOnboardMQProperties {

    @Value("${spring.rabbitmq.addresses}")
    private String host;

    @Value("${spring.rabbitmq.port}")
    private int port;

    @Value("${spring.rabbitmq.virtual-host:/}")
    private String virtualHost;

    @Value("${spring.rabbitmq.username}")
    private String userName;

    @Value("${spring.rabbitmq.password}")
    private String password;

    @Value("${application.talent-onboard-tx.exchange}")
    private String talentOnboardTxExchange;

    @Value("${application.talent-onboard-tx.queue}")
    private String talentOnboardTxQueue;

    @Value("${application.talent-onboard-tx.routing-key}")
    private String talentOnboardTxRoutingKey;

}