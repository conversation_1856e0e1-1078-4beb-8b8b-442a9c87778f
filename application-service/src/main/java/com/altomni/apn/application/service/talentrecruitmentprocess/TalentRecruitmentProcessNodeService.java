package com.altomni.apn.application.service.talentrecruitmentprocess;



import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.domain.TalentRecruitmentProcessNode;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessNodeVO;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing TalentRecruitmentProcessNode.
 */
public interface TalentRecruitmentProcessNodeService {

    List<TalentRecruitmentProcessNode> init(TalentRecruitmentProcess talentRecruitmentProcess);

    /**
     * Get all the talentRecruitmentProcessNodes.
     *
     * @return the list of entities
     */
    List<TalentRecruitmentProcessNodeVO> findAll(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessNodeVO> findAllByJobIdAndTalentId(Long jobId, Long talentId);

    List<TalentRecruitmentProcessNodeVO> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    void completeCurrentNodeAndActiveNextNode(TalentRecruitmentProcess talentRecruitmentProcess, NodeType nodeType);

    void eliminate(TalentRecruitmentProcess talentRecruitmentProcess);

    TalentRecruitmentProcessNode cancelEliminate(TalentRecruitmentProcess talentRecruitmentProcess);

    TalentRecruitmentProcessNode findCurrentNode(Long talentRecruitmentProcessId);

    TalentRecruitmentProcessNode findCurrentNodeV2(Long talentRecruitmentProcessId);

    Optional<TalentRecruitmentProcessNode> findOneOnboardNodeByTalentRecruitmentProcessIds(List<Long> talentRecruitmentProcessIds);

    Instant getTalentRecruitmentProcessNodeLatestByUserIdAndJobId(Long jobId, Long userId, NodeType noteTpe);

    TalentRecruitmentProcessNodeVO findNode(Long talentRecruitmentProcessId, NodeType nodeType);
}
