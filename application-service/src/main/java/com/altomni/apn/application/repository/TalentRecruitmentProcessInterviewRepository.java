package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessInterview;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data  repository for the TalentRecruitmentProcessInterview entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessInterviewRepository extends JpaRepository<TalentRecruitmentProcessInterview, Long> {

    List<TalentRecruitmentProcessInterview> findAllByTalentRecruitmentProcessIdOrderByCreatedDateDesc(Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessInterview t SET t.note = ?2, t.noteLastModifiedDate = current_timestamp, t.noteLastModifiedByUserId = ?3 WHERE t.id = ?1")
    void updateNoteOnly(Long interviewId, String note, Long userId);

    @Modifying
    @Transactional
    @Query(value = "update talent_recruitment_process_interview set final_round=0 where talent_recruitment_process_id=?1",nativeQuery = true)
    void updateFinalRoundByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = " update calendar_event_attendee set status = 0,completed_time=now() where event_id in (?1) ", nativeQuery = true)
    void updateStatusByEventId(List<Long> eventId);

    @Query(value = """
    	select id from calendar_event where reference_id in(?1) and type_id=9
    """, nativeQuery = true)
    List<Long> findCalendarEventId(List<Long> referenceIds);

    List<TalentRecruitmentProcessInterview> findAllByTalentRecruitmentProcessIdIn(List<Long> talentRecruitmentProcessIds);
}
