package com.altomni.apn.application.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class LocationESDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private String officialCity;

    private String officialCounty;

    private String officialProvince;

    private String officialCountry;

    private String officialZipcode;

    private String officialLocation;

    private String officialAddressLine;


    private String englishDisplay;

    private String chineseDisplay;

    private String originDisplay;

    private String originDisplayPinYin;


}
