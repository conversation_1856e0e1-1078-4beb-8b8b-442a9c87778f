package com.altomni.apn.application.service.talentrecruitmentprocess;


import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;

/**
 * Service Interface for managing TalentRecruitmentProcessSubmitToJob.
 */
public interface TalentRecruitmentProcessSubmitToJobService {

    void preProcessValidation(TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob);

    void create(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob);

    void update(Long talentRecruitmentProcessId, TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob);

    void updateRecommendCommentsOnly(TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob);

    TalentRecruitmentProcessSubmitToJobVO updateRecommendCommentsOnly(Long talentRecruitmentProcessId, String note);

    TalentRecruitmentProcessSubmitToJobVO findOneByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

}
