package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessSubmitToJob;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data  repository for the TalentRecruitmentProcessSubmitToJob entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessSubmitToJobRepository extends JpaRepository<TalentRecruitmentProcessSubmitToJob, Long> {

    @Query(value = "select * from talent_recruitment_process_submit_to_job where talent_recruitment_process_id = ?1 order by created_date desc limit 1", nativeQuery = true)
    TalentRecruitmentProcessSubmitToJob findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessSubmitToJob t SET t.lastModifiedDate = current_timestamp, t.lastModifiedBy = ?2 WHERE t.talentRecruitmentProcessId = ?1")
    void updateLastModifiedDateAndLastModifiedBy(Long talentRecruitmentProcessId, String updatedBy);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessSubmitToJob t SET t.recommendComments = ?2, t.noteLastModifiedDate = current_timestamp, t.noteLastModifiedByUserId = ?3 WHERE t.talentRecruitmentProcessId = ?1")
    void updateRecommendCommentsOnly(Long talentRecruitmentProcessId, String recommendComments, Long userId);

    List<TalentRecruitmentProcessSubmitToJob> findAllByTalentResumeRelationIdIs(Long talentResumeRelationId);

    List<TalentRecruitmentProcessSubmitToJob> findAllByTalentResumeRelationIdIn(List<Long> talentResumeRelationIds);
}

