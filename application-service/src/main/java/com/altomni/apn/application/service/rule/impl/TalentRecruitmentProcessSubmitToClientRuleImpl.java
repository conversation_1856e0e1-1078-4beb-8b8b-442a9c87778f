package com.altomni.apn.application.service.rule.impl;

import com.altomni.apn.application.config.constant.Constants;
import com.altomni.apn.application.service.rule.TalentRecruitmentProcessSubmitToClientRule;
import com.altomni.apn.application.service.rule.ipg.TalentRecruitmentProcessIpgSubmitToClientRule;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToClientVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
@Transactional
public class TalentRecruitmentProcessSubmitToClientRuleImpl implements TalentRecruitmentProcessSubmitToClientRule {

    @Resource
    private TalentRecruitmentProcessIpgSubmitToClientRule ipgSubmitToClientRule;

    @Override
    public void validate(TalentRecruitmentProcessSubmitToClientVO submitToClientVO) {
//        if (Constants.TENANT_IPG.equals(SecurityUtils.getTenantId())) {
        if (ApplicationIPGProperties.IPG_RULE_TENANT_IDS.contains(SecurityUtils.getTenantId())) {
            ipgSubmitToClientRule.saveTalentOwnership(submitToClientVO);
        }
    }
}
