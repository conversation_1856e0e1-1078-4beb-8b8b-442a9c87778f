package com.altomni.apn.application.domain;


import com.altomni.apn.common.domain.enumeration.application.OfferLetterCostRateType;
import com.altomni.apn.common.domain.enumeration.application.OfferLetterCostRateTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * A TalentRecruitmentProcessIpgOfferLetterCostRate.
 */
@Entity
@Table(name = "talent_recruitment_process_ipg_offer_letter_cost_rate")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessIpgOfferLetterCostRate implements Serializable {

    private static final long serialVersionUID = 1694212632168537154L;

    @Id
    @JsonIgnore
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @JsonIgnore
    @ApiModelProperty(value = "The tenant id user belongs to.")
    @Column(name = "tenant_id")
    private Long tenantId;

    @NotNull
    @ApiModelProperty(value = "-1代表所有币种都生效")
    @Column(name = "currency")
    private Integer currency;

    @ApiModelProperty(value = "有值时代表该recruitmentProcessId特有的")
    @Column(name = "recruitment_process_id")
    private Long recruitmentProcessId;

    @NotNull
    @Convert(converter = OfferLetterCostRateTypeConverter.class)
    @Column(name = "rate_type")
    private OfferLetterCostRateType rateType;

    @NotNull
    @ApiModelProperty(value = "Identify the specific rates")
    @Column(name = "code")
    private String code;

    @ApiModelProperty(value = "Describe the specific rates")
    @Column(name = "description")
    private String description;

    @NotNull
    @ApiModelProperty(value = "The value of the cost, this could be either a rate or a specific amount of the cost")
    @Column(name = "value")
    private BigDecimal value;

    @NotNull
    @Column(name = "expire_date")
    private LocalDate expireDate;

}
