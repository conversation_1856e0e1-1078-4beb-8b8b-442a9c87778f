package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardClientInfo;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOnboardClientInfoVO;

/**
 * Service Interface for managing {@link TalentRecruitmentProcessOnboardClientInfo}.
 */
public interface TalentRecruitmentProcessOnboardClientInfoService {

    TalentRecruitmentProcessOnboardClientInfoVO save(Long talentRecruitmentProcessId, TalentRecruitmentProcessOnboardClientInfoVO clientInfoVO);

    TalentRecruitmentProcessOnboardClientInfoVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

}
