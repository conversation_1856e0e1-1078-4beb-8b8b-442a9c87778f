package com.altomni.apn.application.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {
    @Value("${application.mainPath.baseUrl}")
    private String baseUrl;

    @Value("${application.emailService.supportSender}")
    private String supportSender;

    @Value("${application.esfiller.baseUrl}")
    private String esfillerBaseUrl;

    @Value("${publicSpringCloud.submitToJobPermissionKey}")
    private String submitToJobPermissionKey;

    /**
     * 禁猎客户优化
     */
    @Value("${application.crmUrl}")
    private String crmUrl;

}
