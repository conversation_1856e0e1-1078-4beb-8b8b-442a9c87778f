package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A TalentRecruitmentProcessIpgContractFeeCharge.
 */
@Entity
@Table(name = "talent_recruitment_process_ipg_contract_fee_charge")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessIpgContractFeeCharge extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = -7954953234177933998L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @JsonIgnore
    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "final_bill_rate", precision=10, scale=2)
    private BigDecimal finalBillRate = new BigDecimal(0);

    @Column(name = "final_pay_rate", precision=10, scale=2)
    private BigDecimal finalPayRate = new BigDecimal(0);

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    @Column(name = "tax_burden_rate")
    private String taxBurdenRateCode;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    @Column(name = "msp_rate")
    private String mspRateCode;

    @ApiModelProperty(value = "Convert to specific value from the table offer_letter_cost_rate")
    @Column(name = "immigration_cost")
    private String immigrationCostCode;

    @Column(name = "extra_cost", precision=10, scale=2)
    private BigDecimal extraCost = new BigDecimal(0);

    @Column(name = "estimated_working_hour_per_week", precision=10, scale=2)
    private BigDecimal estimatedWorkingHourPerWeek = new BigDecimal(0);

    @Column(name = "gp", precision=10, scale=2)
    private BigDecimal gp = new BigDecimal(0);
}
