package com.altomni.apn.application.service.xxljob.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.altomni.apn.application.repository.TalentRecruitmentProcessNodeRepository;
import com.altomni.apn.application.repository.dashboard.DashboardRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessService;
import com.altomni.apn.application.service.user.UserClient;
import com.altomni.apn.application.service.xxljob.XxlJobClientService;
import com.altomni.apn.application.service.xxljob.XxlJobService;
import com.altomni.apn.application.web.rest.vm.InvoiceXxlJobVO;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum;
import com.altomni.apn.common.domain.xxljob.XxlJobRelation;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessVO;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.dto.xxljob.*;
import com.altomni.apn.common.repository.xxljob.XxlJobRelationRepository;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.TenantConfigUtil;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.altomni.apn.common.constants.TenantMessageConfigConstants.MESSAGE_DEFAULT_REMINDER_TIME;
import static com.altomni.apn.common.domain.enumeration.xxljob.XxlJobRelationTypeEnum.*;

@Slf4j
@RefreshScope
@Service("xxlJobForApplicationService")
public class XxlJobServiceImpl implements XxlJobService {

    @Resource
    private XxlJobRelationRepository xxlJobRelationRepository;

    @Resource
    private XxlJobClientService xxlJobClientService;

    @Resource
    private UserClient userService;

    @Resource
    private DashboardRepository dashboardRepository;

    @Resource
    private TalentRecruitmentProcessService talentRecruitmentProcessService;

    @Resource
    private TalentRecruitmentProcessNodeRepository talentRecruitmentProcessNodeRepository;

    @Override
    public void deleteApplicationReminderByTalentId(TalentRecruitmentProcessVO vo) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            List<Long> talentRecruitmentProcessIdList = talentRecruitmentProcessNodeRepository.findTalentRecruitmentProcessIdListByTalentId(vo.getTalentId());
            log.info("[apn@{}] 流程走到on board 需要删除多余的提醒, talentId = {}, applicationIdList = {}", SecurityUtils.getUserId(), vo.getTalentId(), talentRecruitmentProcessIdList);
            List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceIdIn(APPLICATION_NO_UPDATE_REMINDER, talentRecruitmentProcessIdList);
            if (CollUtil.isNotEmpty(xxlJobRelationList)) {
                xxlJobClientService.deleteXxlJobIdList(xxlJobRelationList.stream().map(XxlJobRelation::getXxlJobId).toList());
            }
        });
    }

    @Override
    public void deleteApplicationReminder(TalentRecruitmentProcessVO vo) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceId(APPLICATION_NO_UPDATE_REMINDER, vo.getId());
            if (CollUtil.isNotEmpty(xxlJobRelationList)) {
                xxlJobClientService.deleteXxlJobIdList(xxlJobRelationList.stream().map(XxlJobRelation::getXxlJobId).toList());
            }
        });
    }


    /**
     * 未入职提醒, 通用流程在commission创建提醒，ipg在 offerAccept创建提醒
     * @param talentRecruitmentProcessVO
     */
    @Override
    public void unOnboardedReminder(TalentRecruitmentProcessVO talentRecruitmentProcessVO) {
        SecurityContext context = SecurityContextHolder.getContext();
        LocalDate onboardDate = talentRecruitmentProcessService.getOnboardDate(talentRecruitmentProcessVO.getId());
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            try {
                Long talentRecruitmentProcessId = talentRecruitmentProcessVO.getId();
                //onboardDate ipg 的是在offerAccept, 普通版在 onboardDate中
                log.info("unOnboardedReminderXxlJob search onboardDate talentRecruitmentProcessId = {}, onboardDate = {}", talentRecruitmentProcessId, onboardDate);
                if (onboardDate == null) {
                    log.info("unOnboardedReminderXxlJob is success, onboardDate = null, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
                    return;
                }
                XxlJobForTalentReminderDTO vo = new XxlJobForTalentReminderDTO(talentRecruitmentProcessVO.getId(), talentRecruitmentProcessVO.getTalentId(),
                        talentRecruitmentProcessVO.getTalent().getFullName(), onboardDate, talentRecruitmentProcessVO.getKpiUsers());
                log.info("unOnboardedReminderXxlJob is start, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
                addOrUpdateReminderXxlJob(talentRecruitmentProcessId, vo, TenantMessageMinderConfigFieldCodeEnum.CANDIDATE_NOT_ONBOARDED_REMINDER_TIME);
                log.info("unOnboardedReminderXxlJob is success, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
            } catch (Exception e) {
                log.error("unOnboardedReminderXxlJob is error, talentRecruitmentProcessId = {}, msg = {}", talentRecruitmentProcessVO.getId(), ExceptionUtils.getStackTrace(e));
            }
        });
    }

    /**
     * 入职未开票，入职流程的时候，发票void的时候
     * @param talentRecruitmentProcessVO
     */
    @Override
    public void onboardNoInvoiceReminder(TalentRecruitmentProcessVO talentRecruitmentProcessVO) {
        SecurityContext context = SecurityContextHolder.getContext();
        LocalDate onboardDate = talentRecruitmentProcessService.getOnboardDate(talentRecruitmentProcessVO.getId());
        XxlJobForTalentReminderDTO vo = new XxlJobForTalentReminderDTO(talentRecruitmentProcessVO.getId(), talentRecruitmentProcessVO.getTalentId(),
            talentRecruitmentProcessVO.getTalent().getFullName(), onboardDate, talentRecruitmentProcessVO.getKpiUsers());
        Long talentRecruitmentProcessId = vo.getTalentRecruitmentProcessId();
        //入职创建未开票提醒
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            try {
                log.info("onboardNoInvoiceReminder is start, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
                if (BooleanUtil.isFalse(dashboardRepository.findInvoiceByTalentRecruitmentProcessId(talentRecruitmentProcessId))) {
                    //有发票则不处理
                    log.info("onboardNoInvoiceReminder is success, exists invoice, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
                    return;
                }
                addOrUpdateReminderXxlJob(talentRecruitmentProcessId, vo, TenantMessageMinderConfigFieldCodeEnum.CANDIDATE_ONBOARDING_INVOICE_REMINDER_TIME);
                log.info("onboardNoInvoiceReminder is success, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
            } catch (Exception e) {
                log.error("onboardNoInvoiceReminder is error, talentRecruitmentProcessId = {}, msg = {}", talentRecruitmentProcessId, ExceptionUtils.getStackTrace(e));
            }
        });
        //入职的时候删除 逾期未入职提醒
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            try {
                log.info("delete unOnboardedReminder is start, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
                List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceId(TALENT_NO_ONBOARD_WARN, talentRecruitmentProcessId);
                if (CollUtil.isNotEmpty(xxlJobRelationList)) {
                    xxlJobClientService.deleteXxlJobIdList(xxlJobRelationList.stream().map(XxlJobRelation::getXxlJobId).collect(Collectors.toList()));
                }
                log.info("delete unOnboardedReminder is success, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
            } catch (Exception e) {
                log.error("delete unOnboardedReminder is error, talentRecruitmentProcessId = {}, msg = {}", talentRecruitmentProcessId, ExceptionUtils.getStackTrace(e));
            }
        });
    }

    /**
     * 发票void
     * @param xxlJobInvoiceOverdueDTO
     */
    @Override
    public void onboardNoInvoiceReminderByInvoiceVoid(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        //void删除 发票逾期提醒,同时尝试创建逾期未开票提醒
        deleteInvoiceOverDueReminder(xxlJobInvoiceOverdueDTO);
        onboardNoFteInvoiceReminderByInvoiceVoid(xxlJobInvoiceOverdueDTO.getFteInvoiceIdList());
        onboardNoContractInvoiceReminderByInvoiceVoid(xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
    }

    public void onboardNoFteInvoiceReminderByInvoiceVoid(List<Long> fteInvoiceIdList) {
        SecurityContext context = SecurityContextHolder.getContext();
        if (CollUtil.isNotEmpty(fteInvoiceIdList)) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                fteInvoiceIdList.forEach(fteInvoiceId -> {
                    Long talentRecruitmentProcessId = dashboardRepository.findTalentRecruitmentProcessIdByFteInvoiceId(fteInvoiceId);
                    log.info("talent no fte invoice reminder by talentRecruitmentProcessId = {}, fteInvoiceId = {}", talentRecruitmentProcessId, fteInvoiceId);
                    talentRecruitmentProcessService.findOne(talentRecruitmentProcessId).ifPresent(this::onboardNoInvoiceReminder);
                });
            });
        }
    }

    public void onboardNoContractInvoiceReminderByInvoiceVoid(List<Long> contractInvoiceIdList) {
        SecurityContext context = SecurityContextHolder.getContext();
        if (CollUtil.isNotEmpty(contractInvoiceIdList)) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                List<Long> talentRecruitmentProcessIdList = dashboardRepository.findTalentRecruitmentProcessIdByContractInvoiceIdList(contractInvoiceIdList);
                if (CollUtil.isNotEmpty(talentRecruitmentProcessIdList)) {
                    log.info("talent no contract invoice reminder by talentRecruitmentProcessIdList = {}, contractInvoiceIdList = {}", talentRecruitmentProcessIdList, contractInvoiceIdList);
                    talentRecruitmentProcessIdList.forEach(talentRecruitmentProcessId ->
                            talentRecruitmentProcessService.findOne(talentRecruitmentProcessId).ifPresent(this::onboardNoInvoiceReminder));
                }
            });
        }
    }

    /**
     * 删除入职未开票提醒
     * @param xxlJobInvoiceOverdueDTO
     */
    @Override
    public void deleteOnboardNoInvoiceReminder(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        deleteOnboardNoFteInvoiceReminder(xxlJobInvoiceOverdueDTO.getFteInvoiceIdList());
        deleteOnboardNoContractInvoiceReminder(xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
    }

    public void deleteOnboardNoFteInvoiceReminder(List<Long> fteInvoiceIdList) {
        SecurityContext context = SecurityContextHolder.getContext();
        if (CollUtil.isNotEmpty(fteInvoiceIdList)) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                try {
                    fteInvoiceIdList.forEach(fteInvoiceId -> {
                        Long talentRecruitmentProcessId = dashboardRepository.findTalentRecruitmentProcessIdByFteInvoiceId(fteInvoiceId);
                        log.info("delete talent no fte invoice reminder talentRecruitmentProcessId = {}, fteInvoiceId = {}", talentRecruitmentProcessId, fteInvoiceId);
                        deleteXxlJobForApplicationByType(TALENT_ONBOARD_NO_INVOICE_WARN, talentRecruitmentProcessId);
                    });
                } catch (Exception e) {
                    log.error("delete talent no fte invoice reminder is error, fteInvoiceId = {}, msg = {}", fteInvoiceIdList, ExceptionUtils.getStackTrace(e));
                }
            });
        }
    }

    public void deleteOnboardNoContractInvoiceReminder(List<Long> contractInvoiceIdList) {
        SecurityContext context = SecurityContextHolder.getContext();
        if (CollUtil.isNotEmpty(contractInvoiceIdList)) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                try {
                    List<Long> talentRecruitmentProcessIdList = dashboardRepository.findTalentRecruitmentProcessIdByContractInvoiceIdList(contractInvoiceIdList);
                    if (CollUtil.isNotEmpty(talentRecruitmentProcessIdList)) {
                        log.info("delete talent no contract invoice reminder talentRecruitmentProcessIdList = {}, contractInvoiceIdList = {}", talentRecruitmentProcessIdList, contractInvoiceIdList);
                        talentRecruitmentProcessIdList.forEach(talentRecruitmentProcessId -> deleteXxlJobForApplicationByType(TALENT_ONBOARD_NO_INVOICE_WARN, talentRecruitmentProcessId));
                    }
                } catch (Exception e) {
                    log.info("delete talent no contract invoice reminder is error, contractInvoiceIdList = {}, msg = {}", contractInvoiceIdList, ExceptionUtils.getStackTrace(e));
                }
            });
        }
    }

    /**
     * 流程淘汰，需要删除提醒
     * @param vo
     */
    @Override
    public void eliminateXxlJobForApplication(TalentRecruitmentProcessVO vo) {
        vo.getTalentRecruitmentProcessNodes().forEach(node -> {
            if (!NodeStatus.ACTIVE.equals(node.getNodeStatus()) && !NodeStatus.COMPLETED.equals(node.getNodeStatus())) {
                return;
            }
            switch (node.getNodeType()) {
                case OFFER_ACCEPT -> {
                    log.info("eliminate talentRecruitmentProcessId to delete ipg no onboard reminder , talentRecruitmentProcessId = {}", vo.getId());
                    deleteXxlJobForApplicationByType(TALENT_NO_ONBOARD_WARN, vo.getId());
                }
                case COMMISSION -> {
                    log.info("eliminate talentRecruitmentProcessId to delete no onboard reminder , talentRecruitmentProcessId = {}", vo.getId());
                    deleteXxlJobForApplicationByType(TALENT_NO_ONBOARD_WARN, vo.getId());
                }
                case ON_BOARD -> {
                    log.info("eliminate talentRecruitmentProcessId to delete onboard no invoice reminder , talentRecruitmentProcessId = {}", vo.getId());
                    deleteXxlJobForApplicationByType(TALENT_ONBOARD_NO_INVOICE_WARN, vo.getId());
                }
            }
        });
    }

    /**
     * 取消淘汰需要重新创建提醒
     * @param result
     */
    @Override
    public void cancelEliminateXxlJobForApplication(TalentRecruitmentProcessVO result) {
        result.getTalentRecruitmentProcessNodes().forEach(node -> {
            if (!NodeStatus.ACTIVE.equals(node.getNodeStatus()) && !NodeStatus.COMPLETED.equals(node.getNodeStatus())) {
                return;
            }
            switch (node.getNodeType()) {
                case ON_BOARD -> {
                    //走onboard的不需要在生产逾期未入职提醒
                    log.info("cancel eliminate talentRecruitmentProcessId to add onboard no invoice reminder , talentRecruitmentProcessId = {}", result.getId());
                    onboardNoInvoiceReminder(result);
                }
                case OFFER_ACCEPT -> {
                    log.info("cancel eliminate talentRecruitmentProcessId to add ipg no onboard reminder , talentRecruitmentProcessId = {}", result.getId());
                    unOnboardedReminder(result);
                }
                case COMMISSION -> {
                    log.info("cancel eliminate talentRecruitmentProcessId to add no onboard reminder , talentRecruitmentProcessId = {}", result.getId());
                    unOnboardedReminder(result);
                }
            }
        });
    }

    /**
     * 创建发票逾期提醒
     * @param xxlJobInvoiceOverdueDTO
     */
    @Override
    public void createInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        SecurityContext context = SecurityContextHolder.getContext();
        if (CollUtil.isNotEmpty(xxlJobInvoiceOverdueDTO.getFteInvoiceIdList())) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                try {
                    log.info("createFteInvoiceOverDueReminder is start, invoice = {}", xxlJobInvoiceOverdueDTO.getFteInvoiceIdList());
                    List<InvoiceXxlJobVO> invoiceXxlJobVOList = dashboardRepository.getXxlJobVOListByFteInvoiceIdList(xxlJobInvoiceOverdueDTO.getFteInvoiceIdList());
                    if (CollUtil.isEmpty(invoiceXxlJobVOList)) {
                        log.info("createFteInvoiceOverDueReminder is success, invoice = {}", xxlJobInvoiceOverdueDTO.getFteInvoiceIdList());
                        return;
                    }
                    addInvoiceOverDueReminderXxlJob(invoiceXxlJobVOList, TALENT_FTE_INVOICE_OVERDUE_WARN);
                    log.info("createFteInvoiceOverDueReminder is success, invoice = {}", xxlJobInvoiceOverdueDTO.getFteInvoiceIdList());
                } catch (Exception e) {
                    log.error("createFteInvoiceOverDueReminder is error, invoice = {}, msg = {}", xxlJobInvoiceOverdueDTO.getFteInvoiceIdList(), ExceptionUtils.getStackTrace(e));
                }
            });
        }
        if (CollUtil.isNotEmpty(xxlJobInvoiceOverdueDTO.getContractInvoiceIdList())) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                try {
                    log.info("createContractInvoiceOverDueReminder is start, invoice = {}", xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
                    List<InvoiceXxlJobVO> invoiceXxlJobVOList = dashboardRepository.getXxlJobVOListByContractIdList(xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
                    if (CollUtil.isEmpty(invoiceXxlJobVOList)) {
                        log.info("createContractInvoiceOverDueReminder is success, invoice = {}", xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
                        return;
                    }
                    addInvoiceOverDueReminderXxlJob(invoiceXxlJobVOList, TALENT_CONTRACT_INVOICE_OVERDUE_WARN);
                    log.info("createContractInvoiceOverDueReminder is success, invoice = {}", xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
                } catch (Exception e) {
                    log.error("createContractInvoiceOverDueReminder is error, invoice = {}, msg = {}", xxlJobInvoiceOverdueDTO.getContractInvoiceIdList(), ExceptionUtils.getStackTrace(e));
                }
            });
        }
        //删除入职未开票提醒
        deleteOnboardNoInvoiceReminder(xxlJobInvoiceOverdueDTO);
    }

    /**
     * 删除发票逾期提醒
     * @param xxlJobInvoiceOverdueDTO
     */
    @Override
    public void deleteInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        SecurityContext context = SecurityContextHolder.getContext();
        if (CollUtil.isNotEmpty(xxlJobInvoiceOverdueDTO.getFteInvoiceIdList())) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                log.info("deleteFteInvoiceOverDueReminder is start, invoice = {}", xxlJobInvoiceOverdueDTO.getFteInvoiceIdList());
                deleteInvoiceOverDue(TALENT_FTE_INVOICE_OVERDUE_WARN, xxlJobInvoiceOverdueDTO.getFteInvoiceIdList());
                log.info("deleteFteInvoiceOverDueReminder is success, invoice = {}", xxlJobInvoiceOverdueDTO.getFteInvoiceIdList());
            });
        }
        if (CollUtil.isNotEmpty(xxlJobInvoiceOverdueDTO.getContractInvoiceIdList())) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                log.info("deleteContractInvoiceOverDueReminder is start, invoice = {}", xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
                deleteInvoiceOverDue(TALENT_CONTRACT_INVOICE_OVERDUE_WARN, xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
                log.info("deleteContractInvoiceOverDueReminder is success, invoice = {}", xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
            });
        }
    }

    /**
     * 修改发票逾期提醒时间
     * @param xxlJobInvoiceOverdueDTO
     */
    @Override
    public void updateInvoiceOverDueReminder(XxlJobInvoiceOverdueDTO xxlJobInvoiceOverdueDTO) {
        SecurityContext context = SecurityContextHolder.getContext();
        if (CollUtil.isNotEmpty(xxlJobInvoiceOverdueDTO.getContractInvoiceIdList())) {
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                try {
                    //修改的数据不能马上生效，等待1秒做处理
                    TimeUnit.MILLISECONDS.sleep(500L);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                log.info("updateInvoiceOverDueReminder is start, invoice = {}", xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
                List<InvoiceXxlJobVO> invoiceXxlJobVOList = dashboardRepository.getXxlJobVOListByContractIdList(xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
                if (CollUtil.isEmpty(invoiceXxlJobVOList)) {
                    log.info("createContractInvoiceOverDueReminder is success, invoice = {}", xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
                    return;
                }
                List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceIdIn(TALENT_CONTRACT_INVOICE_OVERDUE_WARN,
                        invoiceXxlJobVOList.stream().map(InvoiceXxlJobVO::getInvoiceId).collect(Collectors.toList()));
                if (CollUtil.isNotEmpty(xxlJobRelationList)) {
                    updateInvoiceOverDueReminderXxlJob(invoiceXxlJobVOList, xxlJobRelationList);
                } else {
                    //没有再这里新增
                    addInvoiceOverDueReminderXxlJob(invoiceXxlJobVOList, TALENT_CONTRACT_INVOICE_OVERDUE_WARN);
                }
                log.info("updateInvoiceOverDueReminder is success, invoice = {}", xxlJobInvoiceOverdueDTO.getContractInvoiceIdList());
            });
        }
    }

    private void deleteInvoiceOverDue(XxlJobRelationTypeEnum xxlJobRelationTypeEnum, List<Long> fteInvoiceIdList) {
        List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceIdIn(xxlJobRelationTypeEnum, fteInvoiceIdList);
        if (CollUtil.isEmpty(xxlJobRelationList)) {
            log.info("deleteInvoiceOverDueReminder is success, invoice = {}", fteInvoiceIdList);
            return;
        }
        List<Integer> xxlJobIdList = xxlJobRelationList.stream().map(XxlJobRelation::getXxlJobId).collect(Collectors.toList());
        xxlJobClientService.deleteXxlJobIdList(xxlJobIdList);
    }

    private void addInvoiceOverDueReminderXxlJob(List<InvoiceXxlJobVO> invoiceXxlJobVOList, XxlJobRelationTypeEnum xxlJobRelationTypeEnum) {
        TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
        String time = fieldToValueMap.getOrDefault(TenantMessageMinderConfigFieldCodeEnum.CANDIDATE_OVERDUE_PAYMENT_REMINDER_TIME.toDbValue(), MESSAGE_DEFAULT_REMINDER_TIME);
        List<XxlJobApnDTO> xxlJobApnDTOList = new ArrayList<>();
        invoiceXxlJobVOList.forEach(invoiceXxlJobVO -> {
            if (StrUtil.isBlank(invoiceXxlJobVO.getTimezone())) {
                return;
            }
            Instant dueTimeInstant = getDueTime(invoiceXxlJobVO.getDueDate(), invoiceXxlJobVO.getTimezone(), time);
            log.info("invoice id = {}, dueDate = {}, timezoneDueDate = {}", invoiceXxlJobVO.getInvoiceId(), invoiceXxlJobVO.getDueDate(), dueTimeInstant);
            XxlJobApnDTO xxlJobApnDTO = getParamEntity(invoiceXxlJobVO, dueTimeInstant, xxlJobRelationTypeEnum, time, invoiceXxlJobVO.getTimezone());
            xxlJobApnDTOList.add(xxlJobApnDTO);
        });
        if (CollUtil.isNotEmpty(xxlJobApnDTOList)) {
            xxlJobClientService.createXxlJobS(xxlJobApnDTOList);
        }
    }

    private void updateInvoiceOverDueReminderXxlJob(List<InvoiceXxlJobVO> invoiceXxlJobVOList, List<XxlJobRelation> xxlJobRelationList) {
        TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
        String time = fieldToValueMap.getOrDefault(TenantMessageMinderConfigFieldCodeEnum.CANDIDATE_OVERDUE_PAYMENT_REMINDER_TIME.toDbValue(), MESSAGE_DEFAULT_REMINDER_TIME);
        Map<String, XxlJobRelation> map = xxlJobRelationList.stream().collect(Collectors.toMap(relation -> relation.getReferenceId() + "-" + relation.getUserId(), a -> a));
        List<XxlJobUpdateBySendTimeForJobAdminDTO> adminDtoList = new ArrayList<>();
        invoiceXxlJobVOList.forEach(invoiceXxlJobVO -> {
            if (StrUtil.isBlank(invoiceXxlJobVO.getTimezone())) {
                return;
            }
            Instant dueTimeInstant = getDueTime(invoiceXxlJobVO.getDueDate(), invoiceXxlJobVO.getTimezone(), time);
            log.info("invoice id = {}, dueDate = {}, timezoneDueDate = {}", invoiceXxlJobVO.getInvoiceId(), invoiceXxlJobVO.getDueDate(), dueTimeInstant);
            XxlJobUpdateBySendTimeForJobAdminDTO adminDto = new XxlJobUpdateBySendTimeForJobAdminDTO();
            adminDto.setXxlJobId(map.get(invoiceXxlJobVO.getInvoiceId() + "-" + invoiceXxlJobVO.getUserId()).getXxlJobId());
            adminDto.setSendTime(dueTimeInstant);
            adminDto.setCron(DateUtil.getCron(dueTimeInstant));
            adminDto.setReminderConfig(time);
            adminDto.setTimezone(invoiceXxlJobVO.getTimezone());
            adminDtoList.add(adminDto);
        });
        if (CollUtil.isNotEmpty(adminDtoList)) {
            xxlJobClientService.updateJobsBySendTime(adminDtoList);
        }
    }

    private XxlJobApnDTO getParamEntity(InvoiceXxlJobVO invoiceXxlJobVO, Instant dueTimeInstant, XxlJobRelationTypeEnum xxlJobRelationTypeEnum, String time, String timezone) {
        XxlJobApnDTO xxlJobApnDTO = new XxlJobApnDTO();
        xxlJobApnDTO.setJobDesc(StrUtil.format(xxlJobRelationTypeEnum.getCnMessage(), invoiceXxlJobVO.getFullName(), invoiceXxlJobVO.getInvoiceNo()) + "#userId=" + invoiceXxlJobVO.getUserId() + "#invoiceId=" + invoiceXxlJobVO.getInvoiceId());
        XxlJobApnParamDTO xxlJobApnParamDTO = new XxlJobApnParamDTO();
        xxlJobApnParamDTO.setXxlJobType(xxlJobRelationTypeEnum);
        xxlJobApnParamDTO.setReferenceId(invoiceXxlJobVO.getInvoiceId());
        xxlJobApnParamDTO.setUserId(invoiceXxlJobVO.getUserId());
        xxlJobApnParamDTO.setTenantId(SecurityUtils.getTenantId());
        xxlJobApnParamDTO.setTimezone(timezone);
        xxlJobApnParamDTO.setSendTime(dueTimeInstant);
        xxlJobApnParamDTO.setReminderConfig(time);
        xxlJobApnParamDTO.setToken(SecurityUtils.getCurrentUserToken());
        xxlJobApnDTO.setXxlJobApnParamDTO(xxlJobApnParamDTO);
        Map<String, Object> paramMap = new JSONObject(xxlJobApnParamDTO);
        paramMap.put("cnTitle", StrUtil.format(xxlJobRelationTypeEnum.getCnMessage(), invoiceXxlJobVO.getFullName(), invoiceXxlJobVO.getInvoiceNo()));
        paramMap.put("enTitle", StrUtil.format(xxlJobRelationTypeEnum.getEnMessage(), invoiceXxlJobVO.getFullName(), invoiceXxlJobVO.getInvoiceNo()));
        paramMap.put("talentId", invoiceXxlJobVO.getTalentId());
        paramMap.put("invoiceId", invoiceXxlJobVO.getInvoiceId());
        paramMap.put("invoiceNo", invoiceXxlJobVO.getInvoiceNo());
        xxlJobApnParamDTO.setXxlJobParam(paramMap);
        return xxlJobApnDTO;
    }

    private void deleteXxlJobForApplicationByType(XxlJobRelationTypeEnum type, Long talentRecruitmentProcessId) {
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            try {
                log.info("deleteXxlJobForApplication is start, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
                List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceId(type, talentRecruitmentProcessId);
                if (CollUtil.isNotEmpty(xxlJobRelationList)) {
                    xxlJobClientService.deleteXxlJobIdList(xxlJobRelationList.stream().map(XxlJobRelation::getXxlJobId).collect(Collectors.toList()));
                }
                log.info("deleteXxlJobForApplication is success, talentRecruitmentProcessId = {}", talentRecruitmentProcessId);
            } catch (Exception e) {
                log.error("deleteXxlJobForApplication is error, talentRecruitmentProcessId = {}, msg = {}", talentRecruitmentProcessId, ExceptionUtils.getStackTrace(e));
            }
        });
    }

    private void addOrUpdateReminderXxlJob(Long talentRecruitmentProcessId, XxlJobForTalentReminderDTO vo, TenantMessageMinderConfigFieldCodeEnum type) {
        //增加对 assignedUser的消息提醒,去除 owner角色
        List<TalentRecruitmentProcessKpiUserVO> kpiUsers = vo.getKpiUsers();
        if (CollUtil.isEmpty(kpiUsers)) {return;}
        List<Long> userIdList = kpiUsers.stream().filter(kpiUser -> !Objects.equals(kpiUser.getUserRole(), UserRole.OWNER))
                .map(TalentRecruitmentProcessKpiUserVO::getUserId).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(userIdList)) {return;}
        //获取用户的时区
        List<UserTimeZoneVO> userTimeZoneVOList = userService.getTimezoneListByUserIdList(userIdList).getBody();
        if (CollUtil.isEmpty(userTimeZoneVOList)) {return;}
        //获取租户配置
        TenantConfigDTO tenantConfigDTO = userService.getSettingConfig(TenantConfigCode.MESSAGE_CONFIG).getBody();
        Map<String, String> fieldToValueMap = TenantConfigUtil.getMapFiledAndValueFromConfig(tenantConfigDTO.getConfigValue());
        String time = fieldToValueMap.getOrDefault(type.toDbValue(), MESSAGE_DEFAULT_REMINDER_TIME);
        XxlJobRelationTypeEnum xxlJobRelationTypeEnum = type.getXxlJobType();
        //查询已经存在的提示任务, 存在修改,不存在新增
        List<XxlJobRelation> xxlJobRelationList = xxlJobRelationRepository.findAllByTypeAndReferenceId(xxlJobRelationTypeEnum, talentRecruitmentProcessId);
        Map<Long, XxlJobRelation> xxlJobRelationMap = xxlJobRelationList.stream().collect(Collectors.toMap(XxlJobRelation::getUserId, a -> a , (a1,a2) -> a1));
        List<XxlJobUpdateBySendTimeForJobAdminDTO> updateList = new ArrayList<>();
        List<XxlJobApnDTO> addXxlJobApnDTOList = new ArrayList<>();
        userTimeZoneVOList.forEach(userTimeZoneVO -> {
            String timezone = userTimeZoneVO.getCustomTimezone();
            if (StrUtil.isBlank(timezone)) {
                return;
            }
            Instant triggerTimeInstant = getTriggerTime(time, vo.getOnboardDate(), timezone, type);
            XxlJobRelation xxlJobRelation = xxlJobRelationMap.get(userTimeZoneVO.getUserId());
            if (xxlJobRelation != null && xxlJobRelation.getSendTime() != triggerTimeInstant) {
                //存在修改
                XxlJobUpdateBySendTimeForJobAdminDTO updateBySendTimeForJobAdminDTO = new XxlJobUpdateBySendTimeForJobAdminDTO();
                updateBySendTimeForJobAdminDTO.setXxlJobId(xxlJobRelation.getXxlJobId());
                updateBySendTimeForJobAdminDTO.setSendTime(triggerTimeInstant);
                updateBySendTimeForJobAdminDTO.setCron(DateUtil.getCron(triggerTimeInstant));
                updateBySendTimeForJobAdminDTO.setReminderConfig(time);
                updateBySendTimeForJobAdminDTO.setTimezone(timezone);
                updateList.add(updateBySendTimeForJobAdminDTO);
                return;
            }
            //不存在新增
            XxlJobApnDTO xxlJobApnDTO = new XxlJobApnDTO();
            xxlJobApnDTO.setJobDesc(StrUtil.format(xxlJobRelationTypeEnum.getCnMessage(), vo.getFullName()) + "#userId=" + userTimeZoneVO.getUserId() + "#talentId=" + vo.getTalentId());
            XxlJobApnParamDTO xxlJobApnParamDTO = new XxlJobApnParamDTO();
            xxlJobApnParamDTO.setXxlJobType(xxlJobRelationTypeEnum);
            xxlJobApnParamDTO.setReferenceId(talentRecruitmentProcessId);
            xxlJobApnParamDTO.setUserId(userTimeZoneVO.getUserId());
            xxlJobApnParamDTO.setTenantId(SecurityUtils.getTenantId());
            xxlJobApnParamDTO.setTimezone(userTimeZoneVO.getCustomTimezone());
            xxlJobApnParamDTO.setSendTime(triggerTimeInstant);
            xxlJobApnParamDTO.setReminderConfig(time);
            xxlJobApnParamDTO.setToken(SecurityUtils.getCurrentUserToken());
            xxlJobApnDTO.setXxlJobApnParamDTO(xxlJobApnParamDTO);
            Map<String, Object> paramMap = new JSONObject(xxlJobApnParamDTO);
            paramMap.put("cnTitle", StrUtil.format(xxlJobRelationTypeEnum.getCnMessage(), vo.getFullName()));
            paramMap.put("enTitle", StrUtil.format(xxlJobRelationTypeEnum.getEnMessage(), vo.getFullName()));
            paramMap.put("talentId", vo.getTalentId());
            paramMap.put("talentRecruitmentProcessId", talentRecruitmentProcessId);
            xxlJobApnParamDTO.setXxlJobParam(paramMap);
            addXxlJobApnDTOList.add(xxlJobApnDTO);
        });
        if (CollUtil.isNotEmpty(addXxlJobApnDTOList)) {
            xxlJobClientService.createXxlJobS(addXxlJobApnDTOList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            xxlJobClientService.updateJobsBySendTime(updateList);
        }
    }

    private Instant getTriggerTime(String time, LocalDate onboardDate, String timezone, TenantMessageMinderConfigFieldCodeEnum type) {
        // 获取对应时区的时间
        ZoneId zoneId = ZoneId.of(timezone);
        // 将Instant转换为ZonedDateTime，使用指定的时区
        ZonedDateTime onboardDateTime = onboardDate.atStartOfDay(zoneId);
        String[] timeArray = time.split(":");
        if (type == TenantMessageMinderConfigFieldCodeEnum.CANDIDATE_NOT_ONBOARDED_REMINDER_TIME) {
            //候选人 逾期未入职,是报道时间的后一天
            onboardDateTime = onboardDateTime.plusDays(1).withHour(Integer.parseInt(timeArray[0])).withMinute(Integer.parseInt(timeArray[1]));
        } else {
            //候选人 入职未开票,是报道时间的当天
            onboardDateTime = onboardDateTime.withHour(Integer.parseInt(timeArray[0])).withMinute(Integer.parseInt(timeArray[1]));
        }
        return onboardDateTime.toInstant();
    }

    /**
     * time 的时间 HH:mm
     * @param dueDate
     * @param timezone
     * @param time
     * @return
     */
    private Instant getDueTime(LocalDateTime dueDate, String timezone, String time) {
        // 获取对应时区的时间
        ZoneId zoneId = ZoneId.of(timezone);
        // 将Instant转换为ZonedDateTime，使用指定的时区
        ZonedDateTime dueZonedDate = dueDate.atZone(zoneId);
        String[] timeArray = time.split(":");
        dueZonedDate = dueZonedDate.withHour(Integer.parseInt(timeArray[0])).withMinute(Integer.parseInt(timeArray[1]));
        return dueZonedDate.toInstant();
    }

}
