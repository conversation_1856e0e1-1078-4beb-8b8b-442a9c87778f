package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgContractFeeCharge;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgContractFeeChargeVO;

/**
 * Service Interface for managing {@link TalentRecruitmentProcessIpgContractFeeCharge}.
 */
public interface TalentRecruitmentProcessIpgContractFeeChargeService {

    TalentRecruitmentProcessIpgContractFeeChargeVO save(Long talentRecruitmentProcessId, TalentRecruitmentProcessIpgContractFeeChargeVO feeChargeVO);

    TalentRecruitmentProcessIpgContractFeeChargeVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
