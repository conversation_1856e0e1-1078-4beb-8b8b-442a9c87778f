package com.altomni.apn.application.web.rest.vm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class TalentRecruitmentProcessAutoEliminationVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long jobId;

    private Boolean auto;

    private Integer period;

    private String createdBy;

    private Instant createdDate;

    private String lastModifiedBy;

    private Instant lastModifiedDate;
}
