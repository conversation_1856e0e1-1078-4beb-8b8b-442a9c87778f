package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatus;
import com.altomni.apn.common.domain.enumeration.application.ActiveStatusConverter;
import com.altomni.apn.common.domain.enumeration.application.RecruitmentProcessType;
import com.altomni.apn.common.domain.enumeration.application.RecruitmentProcessTypeConverter;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.job.JobTypeConverter;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessConfigVO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * A RecruitmentProcess.
 */
@Entity
@Table(name = "recruitment_process")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class RecruitmentProcess extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 4442477104358727842L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

//    @NotNull
//    @Column(name = "recruitment_process_type")
//    @Convert(converter = RecruitmentProcessTypeConverter.class)
//    private RecruitmentProcessType recruitmentProcessType;

    @NotNull
    @Column(name = "tenant_id", updatable = false)
    private Long tenantId;

    /**
     * jobType整改
     * 用于记录流程id与jobType的关系
     */
    @Convert(converter = JobTypeConverter.class)
    @Column(name = "job_type")
    private JobType jobType;

    @Column(name = "name")
    private String name;

    @Column(name = "description")
    private String description;

    @Column(name = "is_substitute_talent")
    private Boolean isSubstituteTalent;

    @Convert(converter = ActiveStatusConverter.class)
    @Column(name = "status")
    private ActiveStatus status;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("recruitmentProcessId", "tenantId"));

    public static RecruitmentProcess fromConfigVO(RecruitmentProcessConfigVO vo) {
        RecruitmentProcess result = new RecruitmentProcess();
        ServiceUtils.myCopyProperties(vo, result);
        return result;
    }
}
