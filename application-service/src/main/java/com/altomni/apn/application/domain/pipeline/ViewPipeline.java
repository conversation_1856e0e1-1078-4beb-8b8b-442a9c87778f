package com.altomni.apn.application.domain.pipeline;

import cn.hutool.json.JSONObject;
import com.altomni.apn.common.domain.enumeration.agency.JobShareStatus;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.user.UserRole;
import com.altomni.apn.common.dto.application.dashboard.MyCandidateStatusFilter;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessEliminateVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessInterviewVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessNodeVO;
import com.altomni.apn.finance.service.vo.invoice.UserCountryVO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Transient;
import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * A ViewPipeline.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ViewPipeline implements Serializable {

    private static final long serialVersionUID = -7469778534468873750L;

    private Long talentRecruitmentProcessId;

    private Long tenantId;

    private Long talentId;

    private String talentName;

    private String nickName;

    @JsonIgnore
    private String talentSkillString;

    @JsonIgnore
    private String talentEmailString;

    private String talentNote;

    private Long jobId;

    private String jobTitle;

    @JsonIgnore
    private String jobLocationString;

    private JobType jobType;//TODO: JobType

    private Long companyId;

    private String companyName;

    private MyCandidateStatusFilter nodeType;

    private NodeStatus nodeStatus;

    private String currentStatusNote;

    private String recruiter;

    private String hiringManager;

    private String hrCoordinate;

    private String msp;

    private String accountManager;

    @JsonIgnore
    private String coAm;

    private List<UserCountryVO> cooperateAccountManager;

    private String salesLeadOwner;

    private String bdOwner;

    @JsonIgnore
    private Long userId;

    @JsonIgnore
    private UserRole userRole;

    private Instant lastModifiedDate;

    private Boolean processStop;
    private Boolean flexibleLocation;
    private int overdue;

    @Transient
    private Boolean invoiceFlag;

    @Transient
    @JsonProperty
    private List<JSONObject> jobLocations;

    @Transient
    @JsonProperty
    private List<JSONObject> talentSkills;

    @Transient
    @JsonProperty
    private List<String> talentEmails;

    @Transient
    @JsonProperty
    private List<String> talentPhones;

    @Transient
    private EliminateReason eliminateReason;

    @Transient
    @JsonProperty
    private List<TalentRecruitmentProcessNodeVO> talentRecruitmentProcessNodes;

    @Transient
    @JsonProperty
    private List<TalentRecruitmentProcessInterviewVO> interviews;

    @Transient
    @JsonProperty
    private TalentRecruitmentProcessEliminateVO eliminate;

    @Transient
    private Long recruitmentProcessId;

    @Transient
    private Integer interviewCount;

    @Transient
    private boolean isPrivateJob;

    @Transient
    private Instant submitToJobTime;

    private Instant submitToClientTime;
    private Instant interviewTime;
    private Instant offerTime;
    private Instant onboardTime;

    @Transient
    private MyCandidateStatusFilter lastNodeType;

    /**
     * true: 已离职， false: 未离职
     */
    @Transient
    private Boolean resigned;

    /**
     * true: 是 converted to FTE 流程
     * false: 非 converted to FTE 流程
     */
    @Transient
    private Boolean convertedToFte;

    private String source;

    private Long agencyId;

    private JobShareStatus agencySharingJobStatus;

    /**
     * 是否有保密候选人查看权限
     */
    private Boolean confidentialTalentViewAble;

    public void encrypt() {
        // 保密候选人信息加密处理 - 将除 talentId 外的所有字段设置为 null
        this.talentRecruitmentProcessId = null;
        this.tenantId = null;
        // this.talentId 保留不变
        this.talentName = null;
        this.nickName = null;
        this.talentSkillString = null;
        this.talentEmailString = null;
        this.talentNote = null;
        this.jobId = null;
        this.jobTitle = null;
        this.jobLocationString = null;
        this.jobType = null;
        this.companyId = null;
        this.companyName = null;
        this.nodeType = null;
        this.nodeStatus = null;
        this.currentStatusNote = null;
        this.recruiter = null;
        this.hiringManager = null;
        this.hrCoordinate = null;
        this.msp = null;
        this.accountManager = null;
        this.coAm = null;
        this.cooperateAccountManager = null;
        this.salesLeadOwner = null;
        this.bdOwner = null;
        this.userId = null;
        this.userRole = null;
        this.lastModifiedDate = null;
        this.processStop = null;
        this.flexibleLocation = null;
        this.overdue = 0;
        this.invoiceFlag = null;
        this.jobLocations = null;
        this.talentSkills = null;
        this.talentEmails = null;
        this.talentPhones = null;
        this.eliminateReason = null;
        this.talentRecruitmentProcessNodes = null;
        this.interviews = null;
        this.eliminate = null;
        this.recruitmentProcessId = null;
        this.interviewCount = null;
        this.isPrivateJob = false;
        this.submitToJobTime = null;
        this.submitToClientTime = null;
        this.interviewTime = null;
        this.offerTime = null;
        this.onboardTime = null;
        this.lastNodeType = null;
        this.resigned = null;
        this.convertedToFte = null;
        this.source = null;
        this.agencyId = null;
        this.agencySharingJobStatus = null;
        // confidentialTalentViewAble 和 confidentialInfo 保留，用于保密逻辑
    }
}
