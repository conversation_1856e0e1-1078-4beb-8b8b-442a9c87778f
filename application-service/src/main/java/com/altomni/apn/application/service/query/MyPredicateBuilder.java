package com.altomni.apn.application.service.query;

import com.altomni.apn.job.service.query.SearchCriteria;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.PathBuilder;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MyPredicateBuilder {
    private static Pattern pattern = Pattern.compile("(\\w+?)([:<>])(.+?),");
    private List<SearchCriteria> params;

    public MyPredicateBuilder() {
        params = new ArrayList<>();
    }

    public MyPredicateBuilder with(
        String key, String operation, Object value) {

        params.add(new SearchCriteria(key, operation, value));
        return this;
    }

    public BooleanExpression build(PathBuilder<?> entityPath) {
        if (params.size() == 0) {
            return null;
        }

        List<BooleanExpression> predicates = new ArrayList<>();
        MyPredicate predicate;
        for (SearchCriteria param : params) {
            predicate = new MyPredicate(param);
            BooleanExpression exp = predicate.getPredicate(entityPath);
            if (exp != null) {
                predicates.add(exp);
            }
        }

        BooleanExpression result = predicates.get(0);
        for (int i = 1; i < predicates.size(); i++) {
            result = result.and(predicates.get(i));
        }
        return result;
    }

    public MyPredicateBuilder search(String search) {
        if (search != null) {
            Matcher matcher = pattern.matcher(search + ",");
            while (matcher.find()) {
                this.with(matcher.group(1), matcher.group(2), matcher.group(3));
            }
        }
        return this;
    }

//    public MyPredicateBuilder nameSearch(String name) {
//        if (name != null) {
//
//        }
//    }
}
