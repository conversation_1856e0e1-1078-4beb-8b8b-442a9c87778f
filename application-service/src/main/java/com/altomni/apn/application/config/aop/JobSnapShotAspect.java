package com.altomni.apn.application.config.aop;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.altomni.apn.application.service.job.JobClient;
import com.altomni.apn.application.service.job.JobSnapshotService;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.job.JobSnapshot;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.job.service.dto.job.JobEsSyncDocument;
import liquibase.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Method;
import java.security.MessageDigest;
import java.time.Instant;

/**
 * job 快照切面
 */
@Aspect
@Slf4j
@Component
public class JobSnapShotAspect {

    public static final String JOB_SNAPSHOT_SYNC_JOB_ID = "JOB_SNAPSHOT_SYNC_JOB_ID";

    @Resource
    private JobSnapshotService jobSnapshotService;

    @Resource
    private JobClient jobClient;

    @Resource
    private HttpService httpService;

    @Value("${application.elasticrecord.url}")
    private String recordUrl;

    @Pointcut(value = "(@annotation(com.altomni.apn.application.config.aop.JobSnapShotAnnotation))")
    public void doJobSnapShotCut() {
    }

    /**
     * DataSync注解方法执行 After 触发事件
     *
     * @param joinPoint
     * @param
     */
    @Around(value = "doJobSnapShotCut()")
    public Object jobSnapshotToEs(ProceedingJoinPoint joinPoint) throws Throwable {log.info("[jobSnapshot@{}] job snapshot aop is start time = {}", SecurityUtils.getUserId(), Instant.now());
        //真正的接口执行内容
        Object keys = joinPoint.proceed();
        String jobJsonInfo = MDC.get(JOB_SNAPSHOT_SYNC_JOB_ID);
        log.info("[jobSnapshot@{}] job snapshot aop jobInfo = {}", SecurityUtils.getUserId(), jobJsonInfo);
        if (StrUtil.isNotBlank(jobJsonInfo)) {
            // 从切面织入点处通过反射机制获取织入点处的方法
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            // 获取切入点所在的方法
            Method method = signature.getMethod();
            JobSnapShotAnnotation jobSnapShotAnnotation = method.getAnnotation(JobSnapShotAnnotation.class);
            NodeType nodeType = jobSnapShotAnnotation.noteType();
            log.info("[jobSnapshot@{}] job snapshot aop type = {}", SecurityUtils.getUserId(), nodeType);
            JSONObject jobInfo = JSONUtil.parseObj(jobJsonInfo);
            JobSnapshot jobSnapshot = new JobSnapshot();
            jobSnapshot.setNodeId(jobInfo.getLong("nodeId"));
            jobSnapshot.setProcessId(jobInfo.getLong("processId"));


            Long jobId = jobInfo.getLong("jobId");
            Long tenantId = jobInfo.getLong("tenantId");


            //同步到es
            String body = jobClient.getJobWithoutEntity(jobId).getBody();
            log.info("[jobSnapshot@{}] getJobWithoutEntity :{}", SecurityUtils.getUserId(), body);
            if(StringUtil.isEmpty(body)) {
                return keys;
            }
            JobEsSyncDocument jobEsSyncDocument = JSON.parseObject(body, JobEsSyncDocument.class);
            if (null != jobEsSyncDocument) {

                JobEsSyncDocument copyBean = new JobEsSyncDocument();
                BeanUtils.copyProperties(jobEsSyncDocument, copyBean);
                JSONObject source = new JSONObject();
                source = jobEsSyncDocument.get_source();
                source.put("lastModifiedDate",null);
                copyBean.set_source(source);

                String hashValue = getHashValue(copyBean);
                if (StringUtils.isBlank(hashValue)) {
                    log.info("[jobSnapshot@{}] get hash value is null :{}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(jobEsSyncDocument.get_source()));
                } else {
                    Instant now = Instant.now();
                    if (getEsByJobSnapshot(hashValue, tenantId)) {
                        jobEsSyncDocument.get_source().put("snapshotCreatedDate",DateUtil.fromInstantToUtcDateTimeWithMillisecond(now));
                        saveJobSnapshotToEs(jobEsSyncDocument, jobId, tenantId, hashValue);
                    } else {
                        log.info("[jobSnapshot@{}] job snapshot aop data existence  = {}", SecurityUtils.getUserId(), jobJsonInfo);
                    }
                    jobSnapshot.setSnapshotHash(hashValue);
                    jobSnapshot.setCreatedDate(now);
                    jobSnapshotService.save(jobSnapshot);
                    log.info("[jobSnapshot@{}] job snapshot aop save job snapshot info:{}", JSONUtil.toJsonStr(jobSnapshot));
                }
            }

        } else {
            log.error("[jobSnapshot@{}] job snapshot is fail, sync job id is null", SecurityUtils.getUserId());
        }
        log.info("[jobSnapshot@{}] job snapshot aop is end time = {}", SecurityUtils.getUserId(), Instant.now());
        //每次处理完需要清理,防止出现内存问题
        MDC.clear();
        return keys;
    }

    private String getHashValue(JobEsSyncDocument jobEsSyncDocument) throws Exception {
        StringBuilder sb = new StringBuilder();
        MessageDigest object = MessageDigest.getInstance("SHA-256");
        byte[] encrypted = object.digest(JSONUtil.toJsonStr(jobEsSyncDocument.get_source()).getBytes("UTF-8"));
        for (byte b : encrypted) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }

    private final static String JOB_SNAPSHOT_INDEX = "snapshot_jobs";

    private String jobRecordUrl(Long tenantId) {
        return recordUrl + JOB_SNAPSHOT_INDEX + "_" + tenantId;
    }

    private void saveJobSnapshotToEs(JobEsSyncDocument jobEsSyncDocument, Long jobId, Long tenantId,String id) {
        try {
            String recordsStrRequest = JSON.toJSONString(jobEsSyncDocument.get_source());
            log.info("[jobSnapshot] record job to ES request, job id: {}, request record: {}", jobId, recordsStrRequest);
            String url = jobRecordUrl(tenantId) + "/_doc/" + id;
            HttpResponse response = httpService.post(url, recordsStrRequest);
            log.info("[jobSnapshot] record job to ES response, job id: {}, response code: {}, response message: {}", jobId, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            if (response == null || !ObjectUtils.equals(HttpStatus.CREATED.value(), response.getCode())) {
                log.info("[jobSnapshot] record job status to EsFiller error, job id: {}, response code: {}, response message: {}", jobId, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            }
        } catch (IOException exception) {
            log.error("[jobSnapshot] record job change to EsFiller IOException");
        }
    }

//
//    private static String generateJsonString(JobEsSyncDocument jobProfile) {
//        //TODO parser解析后有[null]  目前版本hutool处理不了 需要升级，升级修改过大， 先临时在同步时重新覆盖这2个值
//        com.alibaba.fastjson.JSONArray requiredSkillsArray = JSON.parseArray(jobProfile.getRequiredSkills());
//        jobProfile.setRequiredSkills(null);
//        com.alibaba.fastjson.JSONArray preferredSkillsArray = JSON.parseArray(jobProfile.getPreferredSkills());
//        jobProfile.setPreferredSkills(null);
//        JSONObject source = jobProfile.get_source();
//        com.alibaba.fastjson.JSONObject sourceFastJson = JSON.parseObject(JSONUtil.toJsonStr(source));
//        if(requiredSkillsArray == null) {
//            sourceFastJson.remove("requiredSkills");
//        } else {
//            sourceFastJson.put("requiredSkills", requiredSkillsArray);
//        }
//        if(preferredSkillsArray == null) {
//            sourceFastJson.remove("preferredSkills");
//        } else {
//            sourceFastJson.put("preferredSkills", preferredSkillsArray);
//        }
//        com.alibaba.fastjson.JSONObject jobProfileFastJson = JSON.parseObject(JSONUtil.toJsonStr(jobProfile));
//        jobProfileFastJson.put("_source", sourceFastJson);
//
//        return jobProfileFastJson.toJSONString();
//    }

    private boolean getEsByJobSnapshot(String id, Long tenantId) {
        try {
            log.info("[jobSnapshot] record job to ES request, hashCode id: {},}", id);
            HttpResponse response = httpService.post(jobRecordUrl(tenantId) + "/_doc/" + id);
            log.info("[jobSnapshot] record job to ES response, hashCode id: {}, response code: {}, response message: {}", id, response != null ? response.getCode() : null, response != null ? response.getBody() : null);
            if (response == null || !ObjectUtils.equals(HttpStatus.OK.value(), response.getCode())) {
                if (ObjectUtils.equals(HttpStatus.NOT_FOUND.value(), response.getCode())) {
                    return true;
                }
            }
        } catch (IOException exception) {
            log.error("[jobSnapshot] record job change to EsFiller IOException");
        }
        return false;
    }
}
