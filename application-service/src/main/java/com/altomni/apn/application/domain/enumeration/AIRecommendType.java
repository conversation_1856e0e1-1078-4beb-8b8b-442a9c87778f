package com.altomni.apn.application.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

public enum AIRecommendType implements ConvertedEnum<Integer> {

    JOB(0,"Job"),
    TALENT(1,"Talent"),
    COMMON_TALENT(2,"CommonTalent");

    private final int dbValue;

    private final String description;

    AIRecommendType(int dbValue, String description) {
        this.dbValue = dbValue;
        this.description = description;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public String getDescription() {
        return description;
    }

    // static resolving:
    public static final ReverseEnumResolver<AIRecommendType, Integer> resolver =
            new ReverseEnumResolver<>(AIRecommendType.class, AIRecommendType::toDbValue);

    public static AIRecommendType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
