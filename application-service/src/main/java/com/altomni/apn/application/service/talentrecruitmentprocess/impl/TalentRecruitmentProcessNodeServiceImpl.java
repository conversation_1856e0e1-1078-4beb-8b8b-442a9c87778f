package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.RecruitmentProcessNode;
import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.domain.TalentRecruitmentProcessNode;
import com.altomni.apn.application.repository.RecruitmentProcessNodeRepository;
import com.altomni.apn.application.repository.TalentRecruitmentProcessCustomRepository;
import com.altomni.apn.application.repository.TalentRecruitmentProcessNodeRepository;
import com.altomni.apn.application.repository.TalentRecruitmentProcessRepository;
import com.altomni.apn.application.service.talent.TalentClient;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessNodeService;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessNodeVO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.ServiceUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * Service Implementation for managing TalentRecruitmentProcessNode.
 */
@Service
@Transactional
public class TalentRecruitmentProcessNodeServiceImpl implements TalentRecruitmentProcessNodeService {

    @Resource
    private TalentRecruitmentProcessNodeRepository talentRecruitmentProcessNodeRepository;
    @Resource
    private RecruitmentProcessNodeRepository recruitmentProcessNodeRepository;
    @Resource
    private TalentRecruitmentProcessRepository talentRecruitmentProcessRepository;
    @Resource
    private TalentRecruitmentProcessCustomRepository talentRecruitmentProcessCustomRepository;
    @Resource
    private TalentClient talentClient;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Override
    public List<TalentRecruitmentProcessNode> init(TalentRecruitmentProcess talentRecruitmentProcess) {
        List<RecruitmentProcessNode> recruitmentProcessNodes = recruitmentProcessNodeRepository.findAllByRecruitmentProcessId(talentRecruitmentProcess.getRecruitmentProcessId());
        List<TalentRecruitmentProcessNode> talentRecruitmentProcessNodes = new ArrayList<>();
        for (RecruitmentProcessNode recruitmentProcessNode : recruitmentProcessNodes) {
            TalentRecruitmentProcessNode talentRecruitmentProcessNode = new TalentRecruitmentProcessNode();
            talentRecruitmentProcessNode.setTalentRecruitmentProcessId(talentRecruitmentProcess.getId());
            talentRecruitmentProcessNode.setNodeId(recruitmentProcessNode.getId());
            talentRecruitmentProcessNode.setNodeType(recruitmentProcessNode.getNodeType());
            talentRecruitmentProcessNode.setNextNodeId(recruitmentProcessNode.getNextNodeId());
            if (NodeType.SUBMIT_TO_JOB.equals(recruitmentProcessNode.getNodeType())) {
                talentRecruitmentProcessNode.setNodeStatus(NodeStatus.ACTIVE);
            } else {
                talentRecruitmentProcessNode.setNodeStatus(NodeStatus.INACTIVE);
            }
            talentRecruitmentProcessNodes.add(talentRecruitmentProcessNode);
        }
        return talentRecruitmentProcessNodeRepository.saveAll(talentRecruitmentProcessNodes);
    }

    @Override
    public void completeCurrentNodeAndActiveNextNode(TalentRecruitmentProcess talentRecruitmentProcess, NodeType nodeType) {
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeStatus(talentRecruitmentProcess.getId(), NodeStatus.ACTIVE);
        if (currentNode == null) {
            // all node completed
            return;
        }
        if ( currentNode.getNodeType().toDbValue() >= nodeType.toDbValue()) {
            // no need to activate the next step, update information only
            return;
        }
        if (currentNode.getNextNodeId() != null) {
            // need to activate the next step
            TalentRecruitmentProcessNode nextNode = talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeId(talentRecruitmentProcess.getId(), currentNode.getNextNodeId());
            if (nextNode == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_COMPLETECURRENTNODEANDACTIVENEXTNODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
            }
            if (!nextNode.getNodeType().equals(nodeType)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_COMPLETECURRENTNODEANDACTIVENEXTNODENODETYPE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(nextNode.getNodeType()), applicationApiPromptProperties.getAppl()));
            }
            nextNode.setNodeStatus(NodeStatus.ACTIVE);
            talentRecruitmentProcessNodeRepository.save(nextNode);
            //保密候选人，每次进入下一个节点，都尝试解除候选人保密
            talentClient.declassifyProcess(talentRecruitmentProcess.toTalentAutoDeclassifyDto(nextNode.getNodeType()));
        }
        if (!NodeStatus.COMPLETED.equals(currentNode.getNodeStatus())) {
            // complete the current node
            currentNode.setNodeStatus(NodeStatus.COMPLETED);
            talentRecruitmentProcessNodeRepository.save(currentNode);
        }
    }

    @Override
    public void eliminate(TalentRecruitmentProcess talentRecruitmentProcess) {
        TalentRecruitmentProcessNode currentNode = talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeStatus(talentRecruitmentProcess.getId(), NodeStatus.ACTIVE);
        if (currentNode == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_ELIMINATENODEISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        currentNode.setNodeStatus(NodeStatus.ELIMINATED);
        talentRecruitmentProcessNodeRepository.save(currentNode);
    }

    @Override
    public TalentRecruitmentProcessNode cancelEliminate(TalentRecruitmentProcess talentRecruitmentProcess) {
        TalentRecruitmentProcessNode eliminatedNode = talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeStatus(talentRecruitmentProcess.getId(), NodeStatus.ELIMINATED);
        if (eliminatedNode == null) {
            throw new NotFoundException("Has something wrong with node status for this application, please contact support team for help.");
        }
        eliminatedNode.setNodeStatus(NodeStatus.ACTIVE);
        return talentRecruitmentProcessNodeRepository.save(eliminatedNode);
    }

    @Override
    @Transactional(readOnly = true)
    public List<TalentRecruitmentProcessNodeVO> findAll(Long talentRecruitmentProcessId) {
        validateTalentRecruitmentProcess(talentRecruitmentProcessId);
        return toVOs(talentRecruitmentProcessNodeRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }

    @Override
    public List<TalentRecruitmentProcessNodeVO> findAllByJobIdAndTalentId(Long jobId, Long talentId) {
        validateTalentRecruitmentProcess(jobId, talentId);
        TalentRecruitmentProcess talentRecruitmentProcess = talentRecruitmentProcessRepository.findByTalentIdAndJobId(talentId, jobId);
        return toVOs(talentRecruitmentProcessNodeRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcess.getId()));
    }

    @Override
    @Transactional(readOnly = true)
    public List<TalentRecruitmentProcessNodeVO> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVOs(talentRecruitmentProcessNodeRepository.findAllByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }

    private List<TalentRecruitmentProcessNodeVO> toVOs(List<TalentRecruitmentProcessNode> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return Lists.newArrayList();
        }
        return ServiceUtils.convert2DTOList(entities, TalentRecruitmentProcessNodeVO.class);
    }

    private void validateTalentRecruitmentProcess(Long talentRecruitmentProcessId) {
        if (talentRecruitmentProcessId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_FINDINFOIDISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        Optional<TalentRecruitmentProcess> talentRecruitmentProcess = talentRecruitmentProcessRepository.findById(talentRecruitmentProcessId);
        if (talentRecruitmentProcess.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_FINDINFOPROCESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(talentRecruitmentProcessId), applicationApiPromptProperties.getAppl()));
        }
        if (!talentRecruitmentProcess.get().getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_FINDINFONOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
    }

    private void validateTalentRecruitmentProcess(Long jobId, Long talentId) {
        if (jobId == null || talentId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_FINDALLPARAMISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        TalentRecruitmentProcess talentRecruitmentProcess = talentRecruitmentProcessRepository.findByTalentIdAndJobId(talentId, jobId);
        if (talentRecruitmentProcess == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_FINDALLPROCESSISNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(talentId,jobId), applicationApiPromptProperties.getAppl()));
        }
        if (!talentRecruitmentProcess.getTenantId().equals(SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.TALENTRECRUITMENTPROCESS_FINDINFONOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
    }

    @Override
    public TalentRecruitmentProcessNode findCurrentNode(Long talentRecruitmentProcessId) { //TODO: use only one sql query
        TalentRecruitmentProcessNode activeNode = talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeStatus(talentRecruitmentProcessId, NodeStatus.ACTIVE);
        if (activeNode != null) {
            return activeNode;
        }
        return talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeStatus(talentRecruitmentProcessId, NodeStatus.ELIMINATED);
    }

    @Override
    public TalentRecruitmentProcessNode findCurrentNodeV2(Long talentRecruitmentProcessId) {
        List<NodeStatus> statuses = Arrays.asList(NodeStatus.ACTIVE, NodeStatus.ELIMINATED);
        List<TalentRecruitmentProcessNode> nodes = talentRecruitmentProcessNodeRepository.findCurrentNode(talentRecruitmentProcessId, statuses);
        return CollectionUtils.isNotEmpty(nodes) ? nodes.get(0) : null;
    }

    @Override
    public Optional<TalentRecruitmentProcessNode> findOneOnboardNodeByTalentRecruitmentProcessIds(List<Long> talentRecruitmentProcessIds) {
        if (CollectionUtils.isEmpty(talentRecruitmentProcessIds)) {
            return Optional.empty();
        }
        return talentRecruitmentProcessNodeRepository.findFirstByTalentRecruitmentProcessIdInAndNodeTypeIsAndNodeStatus(talentRecruitmentProcessIds, NodeType.ON_BOARD, NodeStatus.ACTIVE);
    }

    @Override
    public Instant getTalentRecruitmentProcessNodeLatestByUserIdAndJobId(Long jobId, Long userId, NodeType noteTpe) {
        return talentRecruitmentProcessCustomRepository.getTalentRecruitmentProcessNodeLatestByUserIdAndJobId(jobId, userId, noteTpe);
    }

    @Override
    public TalentRecruitmentProcessNodeVO findNode(Long talentRecruitmentProcessId, NodeType nodeType) {
        TalentRecruitmentProcessNode talentRecruitmentProcessNode =  talentRecruitmentProcessNodeRepository.findByTalentRecruitmentProcessIdAndNodeType(talentRecruitmentProcessId, nodeType);
        if(talentRecruitmentProcessNode == null){
            return null;
        }
        TalentRecruitmentProcessNodeVO vo = new TalentRecruitmentProcessNodeVO();
        vo.setNodeType(talentRecruitmentProcessNode.getNodeType());
        vo.setId(talentRecruitmentProcessNode.getId());
        vo.setNodeStatus(talentRecruitmentProcessNode.getNodeStatus());
        vo.setTalentRecruitmentProcessId(talentRecruitmentProcessNode.getTalentRecruitmentProcessId());
        vo.setNodeId(talentRecruitmentProcessNode.getNodeId());
        vo.setNextNodeId(talentRecruitmentProcessNode.getNextNodeId());
        return vo;
    }
}
