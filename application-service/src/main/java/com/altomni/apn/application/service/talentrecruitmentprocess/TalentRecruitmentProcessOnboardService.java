package com.altomni.apn.application.service.talentrecruitmentprocess;


import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOnboardVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessUpdateSubstituteTalentDTO;
import com.altomni.apn.finance.service.dto.start.StartDTO;

/**
 * Service Interface for managing TalentRecruitmentProcessOnboard.
 */
public interface TalentRecruitmentProcessOnboardService {

    TalentRecruitmentProcessOnboardVO updateNoteOnly(TalentRecruitmentProcessOnboardVO onboardVO);

    TalentRecruitmentProcessOnboardVO updateNoteOnly(Long talentRecruitmentProcessId, String note);

    TalentRecruitmentProcessOnboardVO save(TalentRecruitmentProcessOnboardVO talentRecruitmentProcessOnboard);

    TalentRecruitmentProcessOnboardVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    StartDTO saveStart(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessOnboardVO onboardVO);

    void updateSubstituteTalentIdByTalentRecruitmentProcessId(TalentRecruitmentProcessUpdateSubstituteTalentDTO substituteTalentDTO);
}
