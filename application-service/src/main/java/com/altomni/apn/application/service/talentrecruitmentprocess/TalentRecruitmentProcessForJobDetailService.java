package com.altomni.apn.application.service.talentrecruitmentprocess;


import com.altomni.apn.application.domain.enumeration.NodeSearchFilter;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessForJobCountByStatusVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessForJobDetailVO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;


/**
 * Service Interface for managing TalentRecruitmentProcess.
 */
public interface TalentRecruitmentProcessForJobDetailService {

    Page<TalentRecruitmentProcessForJobDetailVO> getTalentRecruitmentProcessByJobId(Long jobId, String search, NodeSearchFilter statusFilter, Pageable pageable);

    TalentRecruitmentProcessForJobCountByStatusVO getTalentRecruitmentProcessForJobStatsByJobId(Long jobId, String search);

}
