package com.altomni.apn.application.service.agency;

import com.altomni.apn.application.dto.AgencyActivityForApplicationDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "agency-service")
public interface AgencyClient {

    @PostMapping("/agency/api/v3/activity/create/application")
    ResponseEntity<Void> createAgencyActivity(@RequestBody AgencyActivityForApplicationDTO agencyActivityForApplicationDTO);

}
