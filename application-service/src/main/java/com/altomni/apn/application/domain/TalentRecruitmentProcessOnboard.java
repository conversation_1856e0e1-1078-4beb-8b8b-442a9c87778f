package com.altomni.apn.application.domain;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.WorkingMode;
import com.altomni.apn.common.domain.enumeration.application.WorkingModeConverter;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceType;
import com.altomni.apn.common.domain.enumeration.talent.ResumeSourceTypeConverter;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOnboardVO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A TalentRecruitmentProcessOnboard.
 */
@Entity
@Table(name = "talent_recruitment_process_onboard")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOnboard extends AbstractPermissionNoteAuditingEntity implements Serializable {

    private static final long serialVersionUID = 8180618810777494602L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "note")
    private String note;

    @Column(name = "charge_number")
    private String chargeNumber;

    @Column(name = "tvc_number")
    private String tvcNumber;

    @Column(name = "corp_to_corp")
    private Boolean corpToCorp;

    @Column(name = "working_mode")
    @Convert(converter = WorkingModeConverter.class)
    private WorkingMode workingMode;

    @Column(name = "channel_platform")
    @Convert(converter = ResumeSourceTypeConverter.class)
    private ResumeSourceType channelPlatform;

    @Column(name = "profit_sharing_ratio")
    private BigDecimal profitSharingRatio = new BigDecimal(0);

    @Column(name = "is_substitute_talent")
    private Boolean isSubstituteTalent;

    @Column(name = "relation_process_id")
    private Long relationProcessId;

    @Column(name = "substitute_talent_id")
    private Long substituteTalentId;

    public static TalentRecruitmentProcessOnboard fromVO(TalentRecruitmentProcessOnboardVO vo) {
        TalentRecruitmentProcessOnboard result = new TalentRecruitmentProcessOnboard();
        ServiceUtils.myCopyProperties(vo, result);
        return result;
    }

    @Override
    public String getNoteValue() {
        return note;
    }
}
