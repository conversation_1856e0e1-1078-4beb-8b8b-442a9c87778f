package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardDate;
import com.altomni.apn.application.dto.TalentRecruitmentProcessOnboardDateDTO;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

/**
 * Spring Data SQL repository for the TalentRecruitmentProcessOnboardDate entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessOnboardDateRepository extends JpaRepository<TalentRecruitmentProcessOnboardDate, Long> {

    TalentRecruitmentProcessOnboardDate findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Query(value = " select onboard_date from talent_recruitment_process_onboard_date where talent_recruitment_process_id = ?1 ", nativeQuery = true)
    LocalDate findByOnboardDateRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Query("select new com.altomni.apn.application.dto.TalentRecruitmentProcessOnboardDateDTO(d.talentRecruitmentProcessId, d.onboardDate, d.warrantyEndDate, p.jobId) from TalentRecruitmentProcessOnboardDate d " +
            "inner join TalentRecruitmentProcessNode n on n.talentRecruitmentProcessId=d.talentRecruitmentProcessId " +
            "inner join TalentRecruitmentProcess p on p.id=d.talentRecruitmentProcessId " +
            "where p.talentId=:talentId and n.nodeType=:nodeType and n.nodeStatus=:nodeStatus and d.warrantyEndDate is not null " +
            "order by d.warrantyEndDate desc ")
    List<TalentRecruitmentProcessOnboardDateDTO> getOnboardDateByTalentIdAndNodeTypeAndNodeStatus(@Param("talentId") Long talentId, @Param("nodeType") NodeType nodeType, @Param("nodeStatus") NodeStatus nodeStatus);

    List<TalentRecruitmentProcessOnboardDate> findAllByTalentRecruitmentProcessIdIn(List<Long> recruitmentProcessIds);
}
