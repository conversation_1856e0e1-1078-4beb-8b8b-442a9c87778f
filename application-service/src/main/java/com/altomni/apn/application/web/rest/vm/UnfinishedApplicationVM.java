package com.altomni.apn.application.web.rest.vm;

import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UnfinishedApplicationVM {
    private Long applicationId;
    private Long jobId;
    private String jobTitle;
    private Long talentId;
    private String talentName;
    private Instant talentCreatedDate;
    private NodeType lastNodeType;
    private Instant applicationCreatedDate;
    private Instant applicationLastModifiedDate;
}
