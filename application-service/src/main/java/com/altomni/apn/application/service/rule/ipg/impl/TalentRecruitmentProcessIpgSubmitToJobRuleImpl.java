package com.altomni.apn.application.service.rule.ipg.impl;

import com.altomni.apn.application.config.env.ApplicationApiPromptProperties;
import com.altomni.apn.application.domain.RecruitmentProcess;
import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.repository.RecruitmentProcessRepository;
import com.altomni.apn.application.service.company.CompanyService;
import com.altomni.apn.application.service.job.JobService;
import com.altomni.apn.application.service.rule.ipg.TalentRecruitmentProcessIpgSubmitToJobRule;
import com.altomni.apn.application.service.talent.TalentService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessService;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.talent.TalentOwnershipType;
import com.altomni.apn.common.domain.talent.TalentOwnership;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToClientVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.job.JobDTOV3;
import com.altomni.apn.common.dto.talent.TalentBriefDTO;
import com.altomni.apn.common.dto.talent.TalentResumeDTO;
import com.altomni.apn.common.enumeration.enums.ApplicationAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.company.domain.skipsubmit.SkipSubmitToAmCompanyUser;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
public class TalentRecruitmentProcessIpgSubmitToJobRuleImpl implements TalentRecruitmentProcessIpgSubmitToJobRule {

    @Resource
    private JobService jobService;
    @Resource
    private TalentService talentService;
    @Resource
    private CompanyService companyService;
    @Resource
    private TalentRecruitmentProcessService talentRecruitmentProcessService;

    @Resource
    private RecruitmentProcessRepository recruitmentProcessRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    ApplicationApiPromptProperties applicationApiPromptProperties;

    @Override
    public void validate3DayProtectionRule(TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {

        TalentBriefDTO talent = talentService.getTalentBrief(submitToJobVO.getTalentId());

        boolean within3Days = Instant.now().isBefore(talent.getCreatedAt().plus(3, ChronoUnit.DAYS));
        if (within3Days) {
            Long creatorId = talent.getCreateUserId();
            if (!SecurityUtils.getUserId().equals(creatorId)) {
                List<TalentOwnership> sharedOwners = talentService.getAllTalentOwners(submitToJobVO.getTalentId(), List.of(TalentOwnershipType.SHARE, TalentOwnershipType.TALENT_OWNER, TalentOwnershipType.TENANT_SHARE));
                if(sharedOwners.stream().anyMatch(p -> TalentOwnershipType.TENANT_SHARE.equals(p.getOwnershipType()))) {
                    return;
                }
                Set<Long> notExpiredOwners = sharedOwners.stream().filter(ownership -> Instant.now().isBefore(ownership.getExpireTime())).map(TalentOwnership::getUserId).collect(Collectors.toSet());
                if (!notExpiredOwners.contains(SecurityUtils.getUserId())) {
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.IPG_VALIDATE3DAYPROTECTIONRULE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
                }
            }
        }
    }

    @Override
    public void validateTalentResume(TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
        if (Objects.isNull(submitToJobVO.getTalentResumeRelationId())) {
            throw new CustomParameterizedException("Ipg-rule talent must have a resumeId!");
        }
        TalentResumeDTO talentResumeDTO = talentService.getTalentResumeByTalentResumeRelationId(submitToJobVO.getTalentResumeRelationId());
        if (!Objects.equals(talentResumeDTO.getTalentId(), submitToJobVO.getTalentId())) {
            throw new CustomParameterizedException("Wrong resumeId for this talent!");
        }
    }

    @Override
    public void validateSkipSubmitToAm(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate, JobDTOV3 job, List<TalentRecruitmentProcessKpiUserVO> kpiUsers) {
        RecruitmentProcess recruitmentProcess = recruitmentProcessRepository.findById(job.getRecruitmentProcess().getId()).orElseThrow();
        if (JobType.PAY_ROLL.equals(recruitmentProcess.getJobType())) { //TODO can we remove JobType enum here? yes, payrolling job doesn't have submit_to_am node!
            // no need to skip for payroll jobs
            return;
        }
        List<SkipSubmitToAmCompanyUser> skipSubmitToAmCompanyUsers = companyService.getAllSkipSubmitToAmCompanyUsers(job.getCompanyId()); //TODO: cache result
        if (CollectionUtils.isNotEmpty(skipSubmitToAmCompanyUsers) && skipSubmitToAmCompanyUsers.stream().map(SkipSubmitToAmCompanyUser::getUserId).collect(Collectors.toList()).contains(SecurityUtils.getUserId())) {
            TalentRecruitmentProcessSubmitToClientVO submitToClientVO = new TalentRecruitmentProcessSubmitToClientVO();
            submitToClientVO.setTalentRecruitmentProcessId(talentRecruitmentProcess.getId());
            submitToClientVO.setNote(submitToClientVO.getNote());
            submitToClientVO.setSubmitTime(Instant.now());
            submitToClientVO.setKpiUsers(kpiUsers);
            submitToClientVO.setAgreedPayRate(agreedPayRate);
            talentRecruitmentProcessService.submitToClient(submitToClientVO, false);
        }
    }

    @Override
    public void checkJobStatus(JobDTOV3 job) {
        if (!ObjectUtils.equals(job.getTenantId(), SecurityUtils.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.IPG_CHECKJOBSTATUSNOPREMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), applicationApiPromptProperties.getAppl()));
        }
        RecruitmentProcess recruitmentProcess = recruitmentProcessRepository.findById(job.getRecruitmentProcess().getId()).orElseThrow();
        if (JobType.PAY_ROLL.equals(recruitmentProcess.getJobType())) { //TODO all PAY_ROLL job status is null, if JobStatus is null, we can skip this check
            return;
        }
        if (!JobStatus.OPEN.equals(job.getStatus())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(ApplicationAPIMultilingualEnum.IPG_CHECKJOBSTATUSISNOTOPEN.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(job.getStatus()), applicationApiPromptProperties.getAppl()));
        }
    }
}
