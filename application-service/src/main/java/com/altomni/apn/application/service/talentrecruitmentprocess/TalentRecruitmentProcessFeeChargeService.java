package com.altomni.apn.application.service.talentrecruitmentprocess;


import com.altomni.apn.application.domain.TalentRecruitmentProcessOfferFeeCharge;
import com.altomni.apn.application.domain.TalentRecruitmentProcessKpiUser;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessOfferFeeChargeVO;

/**
 * Service Interface for managing {@link TalentRecruitmentProcessKpiUser}.
 */
public interface TalentRecruitmentProcessFeeChargeService {

    TalentRecruitmentProcessOfferFeeChargeVO save(Long talentRecruitmentProcessId, TalentRecruitmentProcessOfferFeeChargeVO feeChargeVO);

    TalentRecruitmentProcessOfferFeeChargeVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

}
