package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcessIpgAgreedPayRate;
import com.altomni.apn.application.repository.TalentRecruitmentProcessIpgAgreedPayRateRepository;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateService;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateVO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * Service Implementation for managing {@link TalentRecruitmentProcessIpgAgreedPayRate}.
 */
@Service
@Transactional
public class TalentRecruitmentProcessIpgAgreedPayRateServiceImpl implements TalentRecruitmentProcessIpgAgreedPayRateService {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessIpgAgreedPayRateServiceImpl.class);

    @Resource
    private TalentRecruitmentProcessIpgAgreedPayRateRepository agreedPayRateRepository;

    @Override
    public TalentRecruitmentProcessIpgAgreedPayRateVO save(Long talentRecruitmentProcessId, TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRateVO) {
        TalentRecruitmentProcessIpgAgreedPayRate exist = agreedPayRateRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        log.info("[APN: TalentRecruitmentProcessIpgAgreedPayRateServiceImpl @{}] REST request to save agreedPayRateVO, talentRecruitmentProcessId: {}, agreedPayRateVO: {}, exist: {}", SecurityUtils.getUserId(), talentRecruitmentProcessId, agreedPayRateVO, exist);
        if (agreedPayRateVO == null) {
            if (exist != null) {
                // delete
                agreedPayRateRepository.delete(exist);
            }
            return null;
        } else {
            // update
            if (exist != null) {
                ServiceUtils.myCopyProperties(agreedPayRateVO, exist);
                return toVO(agreedPayRateRepository.save(exist));
            }
            // create
            agreedPayRateVO.setTalentRecruitmentProcessId(talentRecruitmentProcessId);
            return toVO(agreedPayRateRepository.save(toEntity(agreedPayRateVO)));
        }
    }

    private TalentRecruitmentProcessIpgAgreedPayRateVO toVO(TalentRecruitmentProcessIpgAgreedPayRate entity) {
        if (entity != null) {
            TalentRecruitmentProcessIpgAgreedPayRateVO vo = new TalentRecruitmentProcessIpgAgreedPayRateVO();
            ServiceUtils.myCopyProperties(entity, vo);
            return vo;
        }
        return null;
    }

    private TalentRecruitmentProcessIpgAgreedPayRate toEntity(TalentRecruitmentProcessIpgAgreedPayRateVO vo) {
        if (vo != null) {
            TalentRecruitmentProcessIpgAgreedPayRate entity = new TalentRecruitmentProcessIpgAgreedPayRate();
            ServiceUtils.myCopyProperties(vo, entity);
            return entity;
        }
        return null;
    }

    @Override
    public TalentRecruitmentProcessIpgAgreedPayRateVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVO(agreedPayRateRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }

    @Override
    public List<TalentRecruitmentProcessIpgAgreedPayRateVO> findByTalentRecruitmentProcessIds(Collection<Long> talentRecruitmentProcessIds) {
        return agreedPayRateRepository.findAllByTalentRecruitmentProcessIdIn(talentRecruitmentProcessIds).stream().map(this::toVO).toList();
    }
}
