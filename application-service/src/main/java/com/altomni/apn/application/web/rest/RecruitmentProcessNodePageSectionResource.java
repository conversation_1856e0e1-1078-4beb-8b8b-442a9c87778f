package com.altomni.apn.application.web.rest;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.application.domain.RecruitmentProcessNodePageSection;
import com.altomni.apn.application.dto.FieldConfigDTO;
import com.altomni.apn.application.dto.RecruitmentProcessNodePageSectionDTO;
import com.altomni.apn.application.service.recruitmentprocess.RecruitmentProcessNodePageSectionService;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.utils.HeaderUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.annotation.Secured;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

@RestController
@RequestMapping("/api/v3/recruitment-process-node-page-sections")
public class RecruitmentProcessNodePageSectionResource {

    private final Logger log = LoggerFactory.getLogger(RecruitmentProcessNodePageSectionResource.class);

    private final RecruitmentProcessNodePageSectionService recruitmentProcessNodePageSectionService;

    public RecruitmentProcessNodePageSectionResource(RecruitmentProcessNodePageSectionService recruitmentProcessNodePageSectionService) {
        this.recruitmentProcessNodePageSectionService = recruitmentProcessNodePageSectionService;
    }

    @PostMapping("")
    @Secured(AuthoritiesConstants.ADMIN)
    public ResponseEntity<List<RecruitmentProcessNodePageSectionDTO>> create(@RequestBody List<RecruitmentProcessNodePageSection> sections) throws URISyntaxException {
        log.info("REST request to save RecruitmentProcessNodePageSections : {} ", sections);
        List<RecruitmentProcessNodePageSectionDTO> result = recruitmentProcessNodePageSectionService.create(sections);
        return ResponseEntity.created(new URI("/api/v3/recruitment-process-node-page-sections/nodeId/"))
                .headers(HeaderUtil.createEntityCreationAlert("RecruitmentProcessNodePageSection", ""))
                .body(result);
    }

    @PostMapping("/init-by-default-config/{recruitmentProcessId}")
    public ResponseEntity<List<RecruitmentProcessNodePageSectionDTO>> initByDefaultConfig(@PathVariable Long recruitmentProcessId) throws URISyntaxException {
        log.info("REST request to inti RecruitmentProcessNodePageSections by default config for recruitmentProcessId: {}", recruitmentProcessId);
        List<RecruitmentProcessNodePageSectionDTO> result = recruitmentProcessNodePageSectionService.initByDefaultConfig(recruitmentProcessId);
        return ResponseEntity.created(new URI("/api/v3/recruitment-process-node-page-sections/init-by-default-config/" + recruitmentProcessId))
                .headers(HeaderUtil.createEntityCreationAlert("RecruitmentProcessNodePageSection", ""))
                .body(result);
    }

    @PostMapping("/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
//    @Secured(AuthoritiesConstants.ADMIN)
    public ResponseEntity<RecruitmentProcessNodePageSectionDTO> create(@PathVariable Long recruitmentProcessId, @PathVariable NodeType nodeType, @RequestBody List<FieldConfigDTO> fieldConfigDTOS) throws URISyntaxException {
        log.info("REST request to create RecruitmentProcessNodePageSections for recruitmentProcessId: {} and nodeType: {} with fieldConfigs: {}", recruitmentProcessId, nodeType, fieldConfigDTOS);
        RecruitmentProcessNodePageSectionDTO result = recruitmentProcessNodePageSectionService.create(recruitmentProcessId, nodeType, fieldConfigDTOS);
        return ResponseEntity.created(new URI("/api/v3/recruitment-process-node-page-sections/talentRecruitmentProcessId/" + recruitmentProcessId + "/nodeType/" + nodeType))
                .headers(HeaderUtil.createEntityCreationAlert("RecruitmentProcessNodePageSection", ""))
                .body(result);
    }

    @PutMapping("/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
//    @Secured(AuthoritiesConstants.ADMIN)
    public ResponseEntity<RecruitmentProcessNodePageSectionDTO> update(@PathVariable Long recruitmentProcessId, @PathVariable NodeType nodeType, @RequestBody List<FieldConfigDTO> fieldConfigDTOS) throws URISyntaxException {
        log.info("REST request to create RecruitmentProcessNodePageSections for recruitmentProcessId: {} and nodeType: {} with fieldConfigs: {}", recruitmentProcessId, nodeType, fieldConfigDTOS);
        RecruitmentProcessNodePageSectionDTO result = recruitmentProcessNodePageSectionService.update(recruitmentProcessId, nodeType, fieldConfigDTOS);
        return ResponseEntity.created(new URI("/api/v3/recruitment-process-node-page-sections/talentRecruitmentProcessId/" + recruitmentProcessId + "/nodeType/" + nodeType))
                .headers(HeaderUtil.createEntityCreationAlert("RecruitmentProcessNodePageSection", ""))
                .body(result);
    }

    @PostMapping("/admin/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
    public ResponseEntity<RecruitmentProcessNodePageSectionDTO> createForAdmin(@PathVariable Long recruitmentProcessId, @PathVariable NodeType nodeType, @RequestBody List<FieldConfigDTO> fieldConfigDTOS) throws URISyntaxException {
        log.info("REST request to create RecruitmentProcessNodePageSections for recruitmentProcessId: {} and nodeType: {} with fieldConfigs: {}", recruitmentProcessId, nodeType, fieldConfigDTOS);
        RecruitmentProcessNodePageSectionDTO result = recruitmentProcessNodePageSectionService.create(recruitmentProcessId, nodeType, fieldConfigDTOS);
        return ResponseEntity.created(new URI("/api/v3/recruitment-process-node-page-sections/talentRecruitmentProcessId/" + recruitmentProcessId + "/nodeType/" + nodeType))
                .headers(HeaderUtil.createEntityCreationAlert("RecruitmentProcessNodePageSection", ""))
                .body(result);
    }

    @PutMapping("/admin/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
    public ResponseEntity<RecruitmentProcessNodePageSectionDTO> updateForAdmin(@PathVariable Long recruitmentProcessId, @PathVariable NodeType nodeType, @RequestBody List<FieldConfigDTO> fieldConfigDTOS) throws URISyntaxException {
        log.info("REST request to create RecruitmentProcessNodePageSections for recruitmentProcessId: {} and nodeType: {} with fieldConfigs: {}", recruitmentProcessId, nodeType, fieldConfigDTOS);
        RecruitmentProcessNodePageSectionDTO result = recruitmentProcessNodePageSectionService.update(recruitmentProcessId, nodeType, fieldConfigDTOS);
        return ResponseEntity.created(new URI("/api/v3/recruitment-process-node-page-sections/talentRecruitmentProcessId/" + recruitmentProcessId + "/nodeType/" + nodeType))
                .headers(HeaderUtil.createEntityCreationAlert("RecruitmentProcessNodePageSection", ""))
                .body(result);
    }

    /**
     * {@code GET  /recruitment-process-node-page-sections} : get all the recruitmentProcessNodePageSections.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of recruitmentProcessNodePageSections in body.
     */
    @GetMapping("/nodeType/{nodeType}")
    public ResponseEntity<List<RecruitmentProcessNodePageSectionDTO>> getAllRecruitmentProcessNodePageSectionsByNodeId(@PathVariable NodeType nodeType, @RequestParam(value = "jobType", required = false) JobType jobType) {
        log.info("REST request to get all RecruitmentProcessNodePageSections, nodeType: {}, jobType: {}", nodeType, jobType);
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findAllRecruitmentProcessNodePageSectionsByNodeId(jobType, nodeType), HttpStatus.OK);
    }

    @GetMapping("/default-config")
    public ResponseEntity<List<RecruitmentProcessNodePageSectionDTO>> getGeneralRecruitmentProcessNodePageDefaultConfig() {
        log.info("REST request to get all getGeneralRecruitmentProcessNodePageDefaultConfig");
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findGeneralRecruitmentProcessNodePageSectionsDefaultConfig(), HttpStatus.OK);
    }

    @GetMapping("/admin/default-config")
    public ResponseEntity<List<RecruitmentProcessNodePageSectionDTO>> getGeneralRecruitmentProcessNodePageDefaultConfigForAdmin() {
        log.info("REST request to get all getGeneralRecruitmentProcessNodePageDefaultConfig");
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findGeneralRecruitmentProcessNodePageSectionsDefaultConfig(), HttpStatus.OK);
    }

    @GetMapping("/default-config/nodeType/{nodeType}")
    public ResponseEntity<RecruitmentProcessNodePageSectionDTO> getGeneralRecruitmentProcessNodePageDefaultConfigByNodeType(@PathVariable NodeType nodeType) {
        log.info("REST request to get all getGeneralRecruitmentProcessNodePageDefaultConfig");
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(JobType.OTHERS, nodeType), HttpStatus.OK);
    }

    @GetMapping("/admin/default-config/nodeType/{nodeType}")
    public ResponseEntity<RecruitmentProcessNodePageSectionDTO> getGeneralRecruitmentProcessNodePageDefaultConfigByNodeTypeForAdmin(@PathVariable NodeType nodeType) {
        log.info("REST request to get all getGeneralRecruitmentProcessNodePageDefaultConfig");
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(JobType.OTHERS, nodeType), HttpStatus.OK);
    }

    @GetMapping("/admin/default-config/jobType/{jobType}/nodeType/{nodeType}")
    public ResponseEntity<RecruitmentProcessNodePageSectionDTO> getRecruitmentProcessNodePageDefaultConfigByJobTypeAndNodeTypeForAdmin(@PathVariable JobType jobType, @PathVariable NodeType nodeType) {
        log.info("REST request to get all getGeneralRecruitmentProcessNodePageDefaultConfig");
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findRecruitmentProcessNodePageSectionsDefaultConfigByJobTypeAndNodeType(jobType, nodeType), HttpStatus.OK);
    }

    @GetMapping("/recruitmentProcessId/{recruitmentProcessId}")
    public ResponseEntity<List<RecruitmentProcessNodePageSectionDTO>> getAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(@PathVariable Long recruitmentProcessId) {
        log.info("REST request to get all RecruitmentProcessNodePageSections by recruitmentProcessId: {}", recruitmentProcessId);
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(recruitmentProcessId), HttpStatus.OK);
    }

    @GetMapping("/admin/recruitmentProcessId/{recruitmentProcessId}")
    public ResponseEntity<List<RecruitmentProcessNodePageSectionDTO>> getAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdForAdmin(@PathVariable Long recruitmentProcessId) {
        log.info("REST request to get all RecruitmentProcessNodePageSections by recruitmentProcessId: {}", recruitmentProcessId);
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessId(recruitmentProcessId), HttpStatus.OK);
    }

    @GetMapping("/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
    public ResponseEntity<RecruitmentProcessNodePageSectionDTO> getAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(@PathVariable Long recruitmentProcessId, @PathVariable NodeType nodeType) {
        log.info("REST request to get all RecruitmentProcessNodePageSections by recruitmentProcessId: {}", recruitmentProcessId);
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(recruitmentProcessId, nodeType), HttpStatus.OK);
    }

    @GetMapping("/admin/recruitmentProcessId/{recruitmentProcessId}/nodeType/{nodeType}")
    public ResponseEntity<RecruitmentProcessNodePageSectionDTO> getAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeTypeForAdmin(@PathVariable Long recruitmentProcessId, @PathVariable NodeType nodeType) {
        log.info("REST request to get all RecruitmentProcessNodePageSections by recruitmentProcessId: {}", recruitmentProcessId);
        return new ResponseEntity<>(recruitmentProcessNodePageSectionService.findAllRecruitmentProcessNodePageSectionsByRecruitmentProcessIdAndNodeType(recruitmentProcessId, nodeType), HttpStatus.OK);
    }

}
