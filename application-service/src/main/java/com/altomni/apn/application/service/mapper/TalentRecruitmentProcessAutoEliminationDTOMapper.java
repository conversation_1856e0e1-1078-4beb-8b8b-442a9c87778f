package com.altomni.apn.application.service.mapper;

import com.altomni.apn.application.domain.TalentRecruitmentProcessAutoElimination;
import com.altomni.apn.application.dto.TalentRecruitmentProcessAutoEliminationDTO;
import com.altomni.apn.application.web.rest.vm.TalentRecruitmentProcessAutoEliminationVO;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {})
public interface TalentRecruitmentProcessAutoEliminationDTOMapper extends EntityMapper<TalentRecruitmentProcessAutoEliminationDTO, TalentRecruitmentProcessAutoElimination> {


}
