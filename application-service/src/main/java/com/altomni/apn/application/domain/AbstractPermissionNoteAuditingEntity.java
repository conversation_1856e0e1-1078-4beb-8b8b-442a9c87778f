package com.altomni.apn.application.domain;

import com.altomni.apn.application.config.PermissionNoteAuditingEntityListener;
import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;

import javax.persistence.*;
import java.io.Serializable;
import java.time.Instant;

/**
 * Base abstract class for entities which will hold definitions for created, last modified, created by,
 * last modified by attributes.
 */
@MappedSuperclass
@EntityListeners({PermissionNoteAuditingEntityListener.class})
public abstract class AbstractPermissionNoteAuditingEntity extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Column(name = "note_last_modified_by_user_id", length = 50)
    private Long noteLastModifiedByUserId;

    @Column(name = "note_last_modified_date")
    private Instant noteLastModifiedDate;

    @Transient
    private String previousNote;

    @PostLoad
    public void setPreviousNote() {
        this.previousNote = getNoteValue();
    }

    public String getPreviousNote() {
        return this.previousNote;
    }

    public Long getNoteLastModifiedByUserId() {
        return noteLastModifiedByUserId;
    }

    public void setNoteLastModifiedByUserId(Long noteLastModifiedBy) {
        this.noteLastModifiedByUserId = noteLastModifiedBy;
    }

    public Instant getNoteLastModifiedDate() {
        return noteLastModifiedDate;
    }

    public void setNoteLastModifiedDate(Instant noteLastModifiedDate) {
        this.noteLastModifiedDate = noteLastModifiedDate;
    }

    public abstract String getNoteValue();
}
