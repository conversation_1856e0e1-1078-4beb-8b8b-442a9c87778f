package com.altomni.apn.application.service.talentrecruitmentprocess;


import com.altomni.apn.application.domain.TalentRecruitmentProcessKpiUser;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateVO;

import java.util.Collection;
import java.util.List;

/**
 * Service Interface for managing {@link TalentRecruitmentProcessKpiUser}.
 */
public interface TalentRecruitmentProcessIpgAgreedPayRateService {

    TalentRecruitmentProcessIpgAgreedPayRateVO save(Long talentRecruitmentProcessId, TalentRecruitmentProcessIpgAgreedPayRateVO agreedPayRate);

    TalentRecruitmentProcessIpgAgreedPayRateVO findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    List<TalentRecruitmentProcessIpgAgreedPayRateVO> findByTalentRecruitmentProcessIds(Collection<Long> talentRecruitmentProcessIds);

}
