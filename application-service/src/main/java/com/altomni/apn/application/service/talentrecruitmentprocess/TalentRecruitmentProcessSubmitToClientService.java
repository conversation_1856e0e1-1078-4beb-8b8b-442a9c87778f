package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToClientVO;

/**
 * Service Interface for managing TalentRecruitmentProcessSubmitToClient.
 */
public interface TalentRecruitmentProcessSubmitToClientService {

    TalentRecruitmentProcessSubmitToClientVO updateNoteOnly(TalentRecruitmentProcessSubmitToClientVO submitToClientVO);

    TalentRecruitmentProcessSubmitToClientVO updateNoteOnly(Long talentRecruitmentProcessId, String note);

    TalentRecruitmentProcessSubmitToClientVO save(TalentRecruitmentProcessSubmitToClientVO submitToClientVO);

    TalentRecruitmentProcessSubmitToClientVO findOneByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
