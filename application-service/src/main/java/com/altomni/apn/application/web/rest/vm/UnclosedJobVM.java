package com.altomni.apn.application.web.rest.vm;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class UnclosedJobVM {
    private Long jobId;
    private String title;
    private JobStatus status;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
