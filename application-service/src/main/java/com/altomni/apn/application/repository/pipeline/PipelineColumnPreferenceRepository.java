//package com.altomni.apn.application.repository.pipeline;
//
//import com.altomni.apn.application.domain.pipeline.PipelineColumnPreference;
//import com.altomni.apn.common.domain.enumeration.search.ModuleType;
//import org.springframework.data.jpa.repository.JpaRepository;
//import org.springframework.data.querydsl.QuerydslPredicateExecutor;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
//
///**
// * This method is deprecated because of refactor the pipeline function, move to user-service
// *
// * @deprecated
// */
//@Deprecated
//@Repository
//public interface PipelineColumnPreferenceRepository extends JpaRepository<PipelineColumnPreference, Long>, QuerydslPredicateExecutor<PipelineColumnPreference> {
//
//    PipelineColumnPreference findByUserIdAndModule(Long userId, ModuleType module);
//
//    PipelineColumnPreference findByTempNameAndUserId(String tempName, Long userId);
//
//    List<PipelineColumnPreference> findAllByUserIdAndModule(Long userId, ModuleType moduleType);
//}
