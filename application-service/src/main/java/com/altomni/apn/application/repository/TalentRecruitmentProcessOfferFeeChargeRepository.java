package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOfferFeeCharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the TalentRecruitmentProcessOfferSalaryPackage entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessOfferFeeChargeRepository extends JpaRepository<TalentRecruitmentProcessOfferFeeCharge, Long> {

    List<TalentRecruitmentProcessOfferFeeCharge> findAllByTalentRecruitmentProcessIdIn(List<Long> recruitmentProcessIds);
}

