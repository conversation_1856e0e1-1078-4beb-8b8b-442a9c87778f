package com.altomni.apn.application.utils;

import com.altomni.apn.application.web.rest.vm.UnfinishedApplicationVM;
import com.altomni.apn.common.dto.job.JobBriefDTO;
import com.altomni.apn.common.utils.DateUtil;
import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Font;
import com.itextpdf.text.FontFactory;
import com.itextpdf.text.pdf.BaseFont;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class ExcelUtil {

    private final Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    private static final String ROUTING_NO_TITLE = "Routing #: ";
    private static final Font FONT_BOLD_9 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 9, BaseColor.BLACK);
    private static final Font FONT_BOLD_11 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 11, BaseColor.BLACK);
    private static final Font FONT_BOLD_15 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 11, BaseColor.BLACK);
    private static final Font FONT_BOLD_20 = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 20, BaseColor.BLACK);
    private static final Font FONT_BOLD_8_BLACK = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 8, BaseColor.BLACK);
    private static final Font FONT_BOLD_8_WHITE = FontFactory.getFont(FontFactory.HELVETICA_BOLD, 8, BaseColor.WHITE);
    private static final Font FONT_REGULAR_8 = FontFactory.getFont(FontFactory.HELVETICA, 8, BaseColor.BLACK);
    //private final Font FONT_CHINESE = FontFactory.getFont(getClass().getClassLoader().getResource("NotoSansCJKsc-Regular.otf").getFile(), BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);
    private final Font FONT_CHINESE = FontFactory.getFont("NotoSansCJKsc-Regular.otf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 8);
    private final Font FONT_ARIAL = FontFactory.getFont("arial.ttf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
    private final Font FONT_CHINESE_12 = FontFactory.getFont("NotoSansCJKsc-Regular.otf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 12);
    private final Font FONT_CHINESE_15 = FontFactory.getFont("NotoSansCJKsc-Regular.otf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 15);
    private final Font FONT_CHINESE_20 = FontFactory.getFont("NotoSansCJKsc-Regular.otf", BaseFont.IDENTITY_H, BaseFont.EMBEDDED, 20);
    private static final Pattern ChineseCharPattern = Pattern.compile("[\u4e00-\u9fa5]");
    private static Map<String,String> NUMBER_MAP = new HashMap<>();

    static {
        NUMBER_MAP.put("1", "First");
        NUMBER_MAP.put("2", "Second");
        NUMBER_MAP.put("3", "Third");
        NUMBER_MAP.put("4", "Fourth");
        NUMBER_MAP.put("5", "Fifth");
        NUMBER_MAP.put("6", "Sixth");
        NUMBER_MAP.put("7", "Seventh");
        NUMBER_MAP.put("8", "Eighth");
        NUMBER_MAP.put("9", "Ninth");
        NUMBER_MAP.put("10", "Tenth");
    }

    public void toExcel(XSSFWorkbook workbook, String language, List<JobBriefDTO> jobBriefs, List<UnfinishedApplicationVM> applicationVMS) {
        XSSFSheet sheet1 = writeHeaderLineForUnclosedJobs(workbook, language);
        writeDataLinesForUnclosedJobs(workbook, sheet1, jobBriefs);

        XSSFSheet sheet2 = writeHeaderLineForUnfinishedApplications(workbook, language);
        writeDataLinesForUnfinishedApplications(workbook, sheet2, applicationVMS);
    }

    public void toExcelForUnclosedJobs(XSSFWorkbook workbook, List<JobBriefDTO> jobBriefs) {
        XSSFSheet sheet = writeHeaderLineForUnclosedJobs(workbook, "en");
        writeDataLinesForUnclosedJobs(workbook, sheet, jobBriefs);
    }


    private XSSFSheet writeHeaderLineForUnclosedJobs(XSSFWorkbook workbook, String language) {
        String[] columnHears = new String[] {"Unclosed jobs", "Job Id", "Job Title", "Job Status", "Job Created Date", "Job Last Modified Date"};
        if ("cn".equalsIgnoreCase(language)) {
            columnHears = new String[] {"未关闭职位", "职位ID", "职位名称", "职位状态", "职位创建时间", "职位最后编辑时间"};
        }
        XSSFSheet sheet = workbook.createSheet(columnHears[0]);

        Row row = sheet.createRow(0);

        CellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeight(16);
        style.setFont(font);

        createCell(sheet, row, 0, columnHears[1], style);
        createCell(sheet, row, 1, columnHears[2], style);
        createCell(sheet, row, 2, columnHears[3], style);
        createCell(sheet, row, 3, columnHears[4], style);
        createCell(sheet, row, 4, columnHears[5], style);
//        createCell(sheet, row, 2, "Full Name", style);
//        createCell(sheet, row, 3, "Roles", style);
//        createCell(sheet, row, 4, "Enabled", style);

        return sheet;
    }

    private void writeDataLinesForUnclosedJobs(XSSFWorkbook workbook, XSSFSheet sheet, List<JobBriefDTO> jobBriefs) {
        int rowCount = 1;

        CellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setFontHeight(14);
        style.setFont(font);

//        Row row = sheet.createRow(rowCount++);
//        createCell(sheet, row, 0, jobIds, style);
//        createCell(sheet, row, 1, applicationIds, style);

        for (JobBriefDTO jobBrief : jobBriefs) {
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;

            createCell(sheet, row, columnCount++, jobBrief.getId(), style);
            createCell(sheet, row, columnCount++, jobBrief.getTitle(), style);
            createCell(sheet, row, columnCount++, jobBrief.getStatus().toString(), style);
            createCell(sheet, row, columnCount++, jobBrief.getCreatedDate(), style);
            createCell(sheet, row, columnCount++, jobBrief.getLastModifiedDate(), style);
//            createCell(sheet, row, columnCount++, user.getEmail(), style);
//            createCell(sheet, row, columnCount++, user.getFullName(), style);
//            createCell(sheet, row, columnCount++, user.getRoles().toString(), style);
//            createCell(sheet, row, columnCount++, user.isEnabled(), style);
            
        }
    }

    public void toExcelForUnfinishedApplications(XSSFWorkbook workbook, String language, List<UnfinishedApplicationVM> applicationVMS) {
        XSSFSheet sheet = writeHeaderLineForUnfinishedApplications(workbook, "en");
        writeDataLinesForUnfinishedApplications(workbook, sheet, applicationVMS);
    }

    private XSSFSheet writeHeaderLineForUnfinishedApplications(XSSFWorkbook workbook, String language) {
        String[] columnHears = new String[] {"Unfinished applications", "Application Id", "Job Id", "Job Title", "Talent Id", "Talent Name", "Talent Created Date", "Last NodeType", "Application Created Date", "Application Last Modified Date"};
        if ("cn".equalsIgnoreCase(language)) {
            columnHears = new String[] {"进行中的流程", "流程ID", "职位ID", "职位名称", "候选人ID", "候选人姓名", "候选人录入时间", "当前所在流程节点" , "流程开始时间", "流程开始时间", "当前流程最后修改时间"};
        }
        XSSFSheet sheet = workbook.createSheet(columnHears[0]);

        Row row = sheet.createRow(0);

        CellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        font.setFontHeight(16);
        style.setFont(font);

        createCell(sheet, row, 0, columnHears[1], style);
        createCell(sheet, row, 1, columnHears[2], style);
        createCell(sheet, row, 2, columnHears[3], style);
        createCell(sheet, row, 3, columnHears[4], style);
        createCell(sheet, row, 4, columnHears[5], style);
        createCell(sheet, row, 5, columnHears[6], style);
        createCell(sheet, row, 6, columnHears[7], style);
        createCell(sheet, row, 7, columnHears[8], style);
        createCell(sheet, row, 8, columnHears[9], style);

        return sheet;
    }

    private void writeDataLinesForUnfinishedApplications(XSSFWorkbook workbook, XSSFSheet sheet, List<UnfinishedApplicationVM> applicationVMS) {
        int rowCount = 1;

        CellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setFontHeight(14);
        style.setFont(font);

        for (UnfinishedApplicationVM applicationVM : applicationVMS) {
            Row row = sheet.createRow(rowCount++);
            int columnCount = 0;

            createCell(sheet, row, columnCount++, applicationVM.getApplicationId(), style);
            createCell(sheet, row, columnCount++, applicationVM.getJobId(), style);
            createCell(sheet, row, columnCount++, applicationVM.getJobTitle(), style);
            createCell(sheet, row, columnCount++, applicationVM.getTalentId(), style);
            createCell(sheet, row, columnCount++, applicationVM.getTalentName(), style);
            createCell(sheet, row, columnCount++, applicationVM.getTalentCreatedDate(), style);
            createCell(sheet, row, columnCount++, applicationVM.getLastNodeType().toString(), style);
            createCell(sheet, row, columnCount++, applicationVM.getApplicationCreatedDate(), style);
            createCell(sheet, row, columnCount++, applicationVM.getApplicationLastModifiedDate(), style);
        }
    }

    private void createCell(XSSFSheet sheet, Row row, int columnCount, Object value, CellStyle style) {
        sheet.autoSizeColumn(columnCount);
        Cell cell = row.createCell(columnCount);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Boolean) {
            cell.setCellValue((Boolean) value);
        } else if (value instanceof Long) {
            cell.setCellValue(String.valueOf(value));
        } else if (value instanceof Instant) {
//            cell.setCellValue(((Instant) value).toString());
            cell.setCellValue(DateUtil.instantToStringWithTime((Instant) value));
        } else {
            cell.setCellValue((String) value);
        }
        cell.setCellStyle(style);
    }

}
