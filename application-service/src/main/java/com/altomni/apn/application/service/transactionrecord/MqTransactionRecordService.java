package com.altomni.apn.application.service.transactionrecord;

import com.altomni.apn.common.domain.transactionrecord.CommonMqTransactionRecord;

import java.math.BigInteger;

/**
 * <AUTHOR>
 */
public interface MqTransactionRecordService {

    void updateStatusById(Long id, Integer sendStatus);

    void sendExceptionByLark(String message);

    CommonMqTransactionRecord findById(BigInteger id);
}