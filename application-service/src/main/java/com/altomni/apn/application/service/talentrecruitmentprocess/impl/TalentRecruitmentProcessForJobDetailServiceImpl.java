package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.application.domain.enumeration.NodeSearchFilter;
import com.altomni.apn.application.service.talent.TalentClient;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessForJobDetailService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateService;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessForJobCountByStatusVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessForJobDetailVO;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateVO;
import com.altomni.apn.common.dto.talent.ConfidentialInfoDto;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.StringUtil;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing TalentRecruitmentProcessForJobDetail.
 */
@Service
@Transactional
public class TalentRecruitmentProcessForJobDetailServiceImpl implements TalentRecruitmentProcessForJobDetailService {

    @PersistenceContext
    private EntityManager entityManager;
    @Resource
    private TalentRecruitmentProcessIpgAgreedPayRateService talentRecruitmentProcessIpgAgreedPayRateService;
    @Resource
    private TalentClient talentClient;

    @Override
    public Page<TalentRecruitmentProcessForJobDetailVO> getTalentRecruitmentProcessByJobId(Long jobId, String search, NodeSearchFilter statusFilter, Pageable pageable) {
        String queryStr = "SELECT trp.id id,   " +
                "     trp.talent_id," +
                "     (select t.full_name from talent t where t.id = trp.talent_id) talent_name," +
                "     trp.job_id," +
                "     JSON_EXTRACT(t_info.extended_info, '$.experiences') as experiences," +
                "     j.company_id," +
                "     JSON_EXTRACT(t_info.extended_info, '$.preferences') as 'preferences'," +
                "     (select group_concat(u.first_name, ' ', u.last_name) from user u, talent_recruitment_process_kpi_user trpku" +
                "             where trp.id = trpku.talent_recruitment_process_id" +
                "               and trpku.user_id = u.id and trpku.user_role = 1) recruiter," +
                "     (select trpn.node_type from talent_recruitment_process_node trpn where trp.id = trpn.talent_recruitment_process_id and trpn.node_status in(1,4)) last_node_type," +
                "     (select trpn.node_status from talent_recruitment_process_node trpn where trp.id = trpn.talent_recruitment_process_id and trpn.node_status in(1,4)) last_node_status," +
                "     trp.last_modified_date, " +
                "     (select group_concat(u.first_name, ' ', u.last_name) from user u, talent_recruitment_process_kpi_user trpku" +
                "             where trp.id = trpku.talent_recruitment_process_id and trpku.user_id = u.id " +
                "               and trpku.user_role = 0) account_manager, " +
                "     j.status job_status, " +
                "     j.posting_time posting_time, " +
                "     t_eliminate.reason eliminate_reason, " +
                "     (select count(trpi.id) from talent_recruitment_process_interview trpi where trp.id = trpi.talent_recruitment_process_id) interview_count, " +
                "     resign.id as resignId, " +
                "     star.id as startId, " +
                "     asa.agency_id as agencyId " +
                "FROM talent_recruitment_process trp " +
                "    LEFT JOIN job j ON j.id = trp.job_id " +
                "    LEFT JOIN talent t ON t.id = trp.talent_id " +
                "    LEFT JOIN talent_additional_info t_info ON t_info.id = t.additional_info_id " +
                "    LEFT JOIN talent_recruitment_process_eliminate t_eliminate ON t_eliminate.talent_recruitment_process_id = trp.id " +
                "    LEFT JOIN talent_recruitment_process_resignation resign ON resign.talent_recruitment_process_id = trp.id " +
                "    left join start star on resign.talent_recruitment_process_id = star.talent_recruitment_process_id and star.start_type=5 " +
                "    LEFT JOIN talent_recruitment_process_node trpn2 ON trp.id = trpn2.talent_recruitment_process_id and trpn2.node_status in(1,4) " +
                "    LEFT JOIN agency_submit_application asa ON asa.talent_recruitment_process_id = trp.id " +
                "    where trp.tenant_id = ?1 and trp.job_id = ?2 ";

        String countStr = "SELECT count(*) " +
                "FROM talent_recruitment_process trp " +
                "    LEFT JOIN talent t ON t.id = trp.talent_id " +
                "    LEFT JOIN talent_recruitment_process_node trpn2 ON trp.id = trpn2.talent_recruitment_process_id and trpn2.node_status in(1,4) " +
                "    where trp.tenant_id = ?1 and trp.job_id = ?2 ";

        if (Objects.nonNull(statusFilter)) {
            if (NodeSearchFilter.ELIMINATED.equals(statusFilter)) {
                queryStr += "and trpn2.node_status = 4 ";
                countStr += "and trpn2.node_status = 4 ";
            } else {
                queryStr = queryStr + "and trpn2.node_status = 1 and trpn2.node_type = " + statusFilter.toDbValue() + " ";
                countStr = countStr + "and trpn2.node_status = 1 and trpn2.node_type = " + statusFilter.toDbValue() + " ";
            }
        }

        if (StringUtils.isNotBlank(search)) {
            queryStr += "and t.full_name like '%" + StringUtils.trim(search) + "%' ";
            countStr += "and t.full_name like '%" + StringUtils.trim(search) + "%' ";
        }

//        Sort.Order order = pageable.getSort().iterator().next();
//        queryStr += "order by " + order.getProperty() + " " + order.getDirection() + " ";
        queryStr += generateSortSqlString(pageable.getSort().iterator().next());

        long from = pageable.getOffset();
        long to = from + pageable.getPageSize();
        queryStr += "limit " + from + ", " + to;

//        Query query = entityManager.createNativeQuery(queryStr)
//                .setParameter(1, SecurityUtils.getTenantId())
//                .setParameter(2, jobId);
//        if (StringUtils.isNotBlank(search)) {
//            query.setParameter(3, StringUtils.trim(search));
//        }

//        System.out.println(queryStr);

        List<Object[]> objects = entityManager.createNativeQuery(queryStr)
                .setParameter(1, SecurityUtils.getTenantId())
                .setParameter(2, jobId)
                .getResultList();
        Object total = entityManager.createNativeQuery(countStr)
                .setParameter(1, SecurityUtils.getTenantId())
                .setParameter(2, jobId)
                .getSingleResult();

        List<TalentRecruitmentProcessForJobDetailVO> res = objectToVO(objects);
        List<TalentRecruitmentProcessForJobDetailVO> finalRes = filterByConfidentialTalent(res);
        long totalNum = Long.parseLong(String.valueOf(total));
        return new PageImpl<>(finalRes, pageable, totalNum);
    }

    private List<TalentRecruitmentProcessForJobDetailVO> filterByConfidentialTalent(List<TalentRecruitmentProcessForJobDetailVO> res) {
        Set<Long> talentIds = res.stream().map(TalentRecruitmentProcessForJobDetailVO::getTalentId).collect(Collectors.toSet());
        Map<Long, ConfidentialInfoDto> confidentialInfoMap = talentClient.getTalentConfidentialInfo(talentIds).getBody();
        if (confidentialInfoMap == null) {
            throw new RuntimeException("Internal Server Error");
        }
        Set<Long> filterConfidentialTalentIds = talentClient.filterConfidentialTalentViewAble(confidentialInfoMap.keySet()).getBody();
        if (filterConfidentialTalentIds == null) {
            throw new RuntimeException("Internal Server Error");
        }
        res.removeIf(r -> confidentialInfoMap.containsKey(r.getTalentId()) && !filterConfidentialTalentIds.contains(r.getTalentId()));
        res.forEach(r -> r.setConfidentialInfo(confidentialInfoMap.get(r.getTalentId())));
        return res;
    }

    private String generateSortSqlString(Sort.Order order) {
        String res = "order by ";
        switch (order.getProperty()) {
            case "talentName":
                res += "talent_name";
                break;
            case "recruiter":
                res += "recruiter";
                break;
            case "accountManager":
                res += "account_manager";
                break;
            case "jobTitle":
                res += "job_title";
                break;
            case "companyName":
                res += "company_name";
                break;
            case "lastModifiedDate":
                res += "last_modified_date";
                break;
            case "lastNodeType":
                res += "last_node_status asc, last_node_type";
                break;
            default:
                res += "id";
                break;
        }
        return res + " " + order.getDirection() + " ";
    }

    private List<TalentRecruitmentProcessForJobDetailVO> objectToVO(List<Object[]> objects) {
        if (CollectionUtils.isEmpty(objects)) {
            return Lists.newArrayList();
        }
        List<TalentRecruitmentProcessForJobDetailVO> result = new ArrayList();
        List<Long> talentRecruitmentProcessIds = objects.stream().map(obj -> Long.valueOf(StringUtil.valueOf(obj[0]))).toList();
        Map<Long, TalentRecruitmentProcessIpgAgreedPayRateVO> agreedPayRateMap = talentRecruitmentProcessIpgAgreedPayRateService.findByTalentRecruitmentProcessIds(talentRecruitmentProcessIds)
            .stream().collect(Collectors.toMap(TalentRecruitmentProcessIpgAgreedPayRateVO::getTalentRecruitmentProcessId, Function.identity()));

        for (Object[] obj : objects) {
            TalentRecruitmentProcessForJobDetailVO detailVO = new TalentRecruitmentProcessForJobDetailVO();
            Long talentRecruitmentProcessId = Long.valueOf(StringUtil.valueOf(obj[0]));
            detailVO.setId(talentRecruitmentProcessId);
            detailVO.setTalentId(Long.valueOf(StringUtil.valueOf(obj[1])));
            detailVO.setTalentName(StringUtil.valueOf(obj[2]));
            detailVO.setJobId(Long.valueOf(StringUtil.valueOf(obj[3])));
//            detailVO.setJobTitle(StringUtil.valueOf(obj[4]));
            String experiencesJsonStr = StringUtil.valueOf(obj[4]);
            if (StringUtils.isBlank(experiencesJsonStr)) {
                detailVO.setJobTitle(null);
                detailVO.setCompanyName(null);
            } else {
                List<String> companyNames = new ArrayList<>();
                List<String> jobTitles = new ArrayList<>();
                JSONArray experiencesJsonArray = JSONArray.parseArray(experiencesJsonStr);
                experiencesJsonArray.toJavaList(com.alibaba.fastjson.JSONObject.class).forEach(jsonObject -> {
                    String company = jsonObject.getString("companyName");
                    if (StringUtils.isNotBlank(company)) {
                        companyNames.add(company);
                    }
                    String jobTitle = jsonObject.getString("title");
                    if (StringUtils.isNotBlank(jobTitle)) {
                        jobTitles.add(jobTitle);
                    }
                });
                detailVO.setJobTitle(String.join(", ", jobTitles));
                detailVO.setCompanyName(String.join(", ", companyNames));
            }

            detailVO.setCompanyId(obj[5] != null ? Long.valueOf(StringUtil.valueOf(obj[5])) : null);
//            detailVO.setCompanyName(StringUtil.valueOf(obj[6]));
            //站位但不使用
//            String preferredSalaryJsonStr = StringUtil.valueOf(obj[6]);
//            if (StringUtils.isBlank(preferredSalaryJsonStr)) {
//                detailVO.setPreferredSalaryRange(null);
//                detailVO.setPreferredPayType(null);
//                detailVO.setPreferredCurrency(null);
//            } else {
////                JSONValidator.Type type = JSONValidator.from(preferredSalaryJsonStr).getType();
////                if (JSONValidator.Type.Array.equals(type)) {
//                    JSONArray preferredSalaryJsonArray = JSONArray.parseArray(preferredSalaryJsonStr);
//                    if (preferredSalaryJsonArray.size() == 3) {
//                        JSONObject preferredSalaryRangeJsonObj = preferredSalaryJsonArray.getJSONObject(0);
//                        BigDecimal gte = preferredSalaryRangeJsonObj.getBigDecimal("gte");
//                        BigDecimal lte = preferredSalaryRangeJsonObj.getBigDecimal("lte");
//                        RangeDTO preferredSalaryRange = new RangeDTO(gte, lte);
//                        detailVO.setPreferredSalaryRange(preferredSalaryRange);
//
//                        String preferredPayType = preferredSalaryJsonArray.getString(1);
//                        detailVO.setPreferredPayType(RateUnitType.valueOf(preferredPayType));
//
//                        Integer preferredCurrency = getPreferredCurrency(preferredSalaryJsonArray);
//                        detailVO.setPreferredCurrency(preferredCurrency);
//                    }
////                }
//            }

            detailVO.setRecruiter(StringUtil.valueOf(obj[7]));
            if(StringUtil.valueOf(obj[8]) != null) {
                detailVO.setLastNodeType(NodeType.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[8]))));
            }
            if(StringUtil.valueOf(obj[9]) != null) {
                detailVO.setLastNodeStatus(NodeStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[9]))));
            }
            detailVO.setLastModifiedDate(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[10])));
            detailVO.setAccountManager(StringUtil.valueOf(obj[11]));
            if(StringUtil.valueOf(obj[12]) != null) {
                detailVO.setJobStatus(JobStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[12]))));
            }
            detailVO.setPostingTime(DateUtil.fromStringToInstant(StringUtil.valueOf(obj[13])));
            detailVO.setAgreedPayRate(agreedPayRateMap.get(talentRecruitmentProcessId));
            if (StringUtil.valueOf(obj[14]) != null) {
                detailVO.setEliminateReason(EliminateReason.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[14]))));
            }
//            detailVO.setJobTitle(StringUtil.valueOf(obj[15]));
//            detailVO.setCompanyName(StringUtil.valueOf(obj[16]));
            detailVO.setInterviewCount(obj[15] != null ? Integer.parseInt(StringUtil.valueOf(obj[15])) : 0);
            detailVO.setResigned(Objects.nonNull(obj[16]));
            boolean convertedToFte = Objects.nonNull(obj[17]);
            if (convertedToFte){
                detailVO.setResigned(Boolean.FALSE);
            }
            String agencyId = StringUtil.valueOf(obj[18]);
            if (StringUtils.isNotBlank(agencyId)) {
                detailVO.setAgencyId(Long.valueOf(agencyId));
            }
            result.add(detailVO);
        }
        return result;
    }

    @Override
    public TalentRecruitmentProcessForJobCountByStatusVO getTalentRecruitmentProcessForJobStatsByJobId(Long jobId, String search) {
        String queryStr = "SELECT trp.id id,   " +
                "     trpn.node_type last_node_type," +
                "     trpn.node_status last_node_status " +
                "FROM talent_recruitment_process trp " +
                "    LEFT JOIN talent t ON t.id = trp.talent_id " +
                "    LEFT JOIN talent_recruitment_process_node trpn ON trp.id = trpn.talent_recruitment_process_id AND trpn.node_status IN (1,4) " +
                "    WHERE trp.tenant_id = ?1 AND job_id = ?2 ";


        if (StringUtils.isNotBlank(search)) {
            queryStr += "and t.full_name like '%" + StringUtils.trim(search) + "%' ";
        }

        List<Object[]> objects = entityManager.createNativeQuery(queryStr)
                .setParameter(1, SecurityUtils.getTenantId())
                .setParameter(2, jobId)
                .getResultList();

        TalentRecruitmentProcessForJobCountByStatusVO res = objectToStats(objects);

        return res;
    }

    private TalentRecruitmentProcessForJobCountByStatusVO objectToStats(List<Object[]> objects) {

//        List<TalentRecruitmentProcessForJobDetailVO> result = new ArrayList();
        int total = 0;
        int submitToJob = 0;
        int submitToClient = 0;
        int interview = 0;
        int offer = 0;
        int offerAccept = 0;
        int commission = 0;
        int onboard = 0;
        int eliminated = 0;

        for (Object[] obj : objects) {
//            TalentRecruitmentProcessForJobDetailVO detailVO = new TalentRecruitmentProcessForJobDetailVO();
//            Long talentRecruitmentProcessId = Long.valueOf(StringUtil.valueOf(obj[0]));
//            detailVO.setId(talentRecruitmentProcessId);
//            if (StringUtil.valueOf(obj[1]) != null) {
//                detailVO.setLastNodeType(NodeType.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[1]))));
//            }
//            if (StringUtil.valueOf(obj[2]) != null) {
//                detailVO.setLastNodeStatus(NodeStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[2]))));
//            }
//            result.add(detailVO);

            total++;
            NodeType nodeType = NodeType.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[1])));
            NodeStatus nodeStatus = NodeStatus.fromDbValue(Integer.valueOf(StringUtil.valueOf(obj[2])));
            if (NodeStatus.ELIMINATED.equals(nodeStatus)) {
                eliminated++;
            } else {
                switch (nodeType) {
                    case SUBMIT_TO_JOB:
                        submitToJob++;
                        break;
                    case SUBMIT_TO_CLIENT:
                        submitToClient++;
                        break;
                    case INTERVIEW:
                        interview++;
                        break;
                    case OFFER:
                        offer++;
                        break;
                    case OFFER_ACCEPT:
                        offerAccept++;
                        break;
                    case COMMISSION:
                        commission++;
                        break;
                    case ON_BOARD:
                        onboard++;
                        break;
                    default:
                        break;
                }
            }
        }
        return new TalentRecruitmentProcessForJobCountByStatusVO(total, submitToJob, submitToClient, interview, offer, offerAccept, commission, onboard, eliminated);
    }

}
