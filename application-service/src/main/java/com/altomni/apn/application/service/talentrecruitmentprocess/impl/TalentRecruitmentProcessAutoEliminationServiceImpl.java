package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.application.config.constant.Constants;
import com.altomni.apn.application.domain.TalentRecruitmentProcessAutoElimination;
import com.altomni.apn.application.dto.AutoEliminationDTO;
import com.altomni.apn.application.dto.EliminateDTO;
import com.altomni.apn.application.dto.TalentRecruitmentProcessAutoEliminationDTO;
import com.altomni.apn.application.repository.TalentRecruitmentProcessAutoEliminationRepository;
import com.altomni.apn.application.service.mapper.TalentRecruitmentProcessAutoEliminationDTOMapper;
import com.altomni.apn.application.service.mapper.TalentRecruitmentProcessAutoEliminationVOMapper;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessAutoEliminationService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessService;
import com.altomni.apn.application.web.rest.vm.TalentRecruitmentProcessAutoEliminationVO;
import com.altomni.apn.common.domain.enumeration.application.EliminateReason;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import javax.persistence.Tuple;
import java.math.BigInteger;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Slf4j
public class TalentRecruitmentProcessAutoEliminationServiceImpl implements TalentRecruitmentProcessAutoEliminationService {

    @Resource
    TalentRecruitmentProcessAutoEliminationRepository talentRecruitmentProcessAutoEliminationRepository;
    @Resource
    TalentRecruitmentProcessAutoEliminationVOMapper talentRecruitmentProcessAutoEliminationVOMapper;
    @Resource
    TalentRecruitmentProcessAutoEliminationDTOMapper talentRecruitmentProcessAutoEliminationDTOMapper;
    @Resource
    TalentRecruitmentProcessService talentRecruitmentProcessService;

    @Override
    public TalentRecruitmentProcessAutoEliminationVO queryAutoEliminationByJobId(Long jobId) {
        TalentRecruitmentProcessAutoEliminationVO result = new TalentRecruitmentProcessAutoEliminationVO();
        TalentRecruitmentProcessAutoElimination processAutoElimination = talentRecruitmentProcessAutoEliminationRepository.findOneByJobId(jobId);
        if (ObjectUtil.isNotNull(processAutoElimination)) {
            result = talentRecruitmentProcessAutoEliminationVOMapper.toDto(processAutoElimination);
        }
        return result;
    }

    @Override
    public Long countByJobIdAndPeriod(Long jobId, Integer period) {
        return talentRecruitmentProcessAutoEliminationRepository.findNeedAutoEliminationTalentRecruitmentProcessIdsByJobIdAndPeriod(jobId, period);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TalentRecruitmentProcessAutoEliminationVO saveAutoElimination(TalentRecruitmentProcessAutoEliminationDTO dto) {
        StopWatch stopWatch = new StopWatch("create start");
        stopWatch.start("[echocheng 1.1] findOneByJobId");
        TalentRecruitmentProcessAutoElimination existAutoElimination = talentRecruitmentProcessAutoEliminationRepository.findOneByJobId(dto.getJobId());
        Integer originPeriod = null;

        if (ObjectUtil.isNotNull(existAutoElimination)) {
            if (existAutoElimination.getAuto()) {
                originPeriod = existAutoElimination.getPeriod();
            }
            BeanUtil.copyProperties(dto, existAutoElimination, "id");
        } else {
            existAutoElimination = talentRecruitmentProcessAutoEliminationDTOMapper.toEntity(dto);
        }
        stopWatch.stop();
        stopWatch.start("[echocheng1.2] saveAndFlush");
        talentRecruitmentProcessAutoEliminationRepository.saveAndFlush(existAutoElimination);
        stopWatch.stop();
        if (dto.getPeriod() != null  && dto.getAuto() && (originPeriod == null || originPeriod > dto.getPeriod())) {
            // 触发任务
            SecurityContext context = SecurityContextHolder.getContext();
            stopWatch.start("[echocheng 1.3] autoEliminationByJobId");
            CompletableFuture.runAsync(() -> {
                SecurityContextHolder.setContext(context);
                autoEliminationByJobId(dto.getJobId());
            });
            stopWatch.stop();
        }
        log.info("[apn @{}] echocheng1 time = {}ms \n {}", SecurityUtils.getUserId(), stopWatch.getTotalTimeMillis(), stopWatch.prettyPrint());
        return talentRecruitmentProcessAutoEliminationVOMapper.toDto(existAutoElimination);
    }

    protected void autoEliminationByJobId(Long jobId) {
        List<Tuple> autoEliminationTuples = talentRecruitmentProcessAutoEliminationRepository.findNeedAutoEliminationTalentRecruitmentProcessIdsByJobId(jobId);
        List<AutoEliminationDTO> autoEliminationList = convertToDTO(autoEliminationTuples);

        if (CollUtil.isNotEmpty(autoEliminationList)) {
            SecurityContext context = SecurityContextHolder.getContext();
            autoEliminationList.stream().map(t -> {
                EliminateDTO eliminate = new EliminateDTO();
                eliminate.setEliminateReason(EliminateReason.AUTOMATIC_ELIMINATION);
                eliminate.setTalentRecruitmentProcessId(t.getTalentRecruitmentProcessId());
                eliminate.setNote(String.format(Constants.AUTO_ELIMINATION_NOTE_TEMPLATE, t.getPeriod(), t.getPeriod()));
                return eliminate;
            }).forEach(e -> {
                SecurityContextHolder.setContext(context);
                talentRecruitmentProcessService.eliminate(e);
            });
        }
    }


    @Scheduled(cron = "0 0 0 * * ?", zone = "UTC")
    protected void updateStatusMissingByNoRecord() {
        log.info("[apn] talent auto elimination is start");
        List<Tuple> autoEliminationTuples = talentRecruitmentProcessAutoEliminationRepository.findNeedAutoEliminationTalentRecruitmentProcessIds();
        List<AutoEliminationDTO> autoEliminations = convertToDTO(autoEliminationTuples);
        LoginUtil.simulateLoginWithClient();
        SecurityContext context = SecurityContextHolder.getContext();
        autoEliminations.stream().map(t -> {
            EliminateDTO eliminate = new EliminateDTO();
            eliminate.setEliminateReason(EliminateReason.AUTOMATIC_ELIMINATION);
            eliminate.setTalentRecruitmentProcessId(t.getTalentRecruitmentProcessId());
            eliminate.setNote(String.format(Constants.AUTO_ELIMINATION_NOTE_TEMPLATE, t.getPeriod(), t.getPeriod()));
            return eliminate;
        }).forEach(e -> {
            SecurityContextHolder.setContext(context);
            talentRecruitmentProcessService.eliminateForAuto(e);
        });

        log.info("[apn] talent auto elimination is finish");
    }

    public List<AutoEliminationDTO> convertToDTO(List<Tuple> tuples) {
        return tuples.stream()
                .map(tuple -> new AutoEliminationDTO(
                        tuple.get("talent_recruitment_process_id", BigInteger.class).longValue(),
                        tuple.get("period", Integer.class)))
                .collect(Collectors.toList());
    }

}
