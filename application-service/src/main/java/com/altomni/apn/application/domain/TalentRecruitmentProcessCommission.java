package com.altomni.apn.application.domain;


import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessCommissionVO;
import com.altomni.apn.common.utils.ServiceUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A TalentRecruitmentProcessCommission.
 */
@Entity
@Table(name = "talent_recruitment_process_commission")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessCommission extends AbstractPermissionNoteAuditingEntity implements Serializable {

    private static final long serialVersionUID = -3027884309877855746L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "note")
    private String note;

    public static TalentRecruitmentProcessCommission fromTalentRecruitmentProcessCommissionVO(TalentRecruitmentProcessCommissionVO vo) {
        TalentRecruitmentProcessCommission result = new TalentRecruitmentProcessCommission();
        ServiceUtils.myCopyProperties(vo, result);
        return result;
    }

    @Override
    public String getNoteValue() {
        return note;
    }
}
