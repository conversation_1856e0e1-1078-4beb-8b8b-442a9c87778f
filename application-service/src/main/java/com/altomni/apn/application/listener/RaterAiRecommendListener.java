package com.altomni.apn.application.listener;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.ExceptionUtil;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessService;
import com.altomni.apn.common.utils.LoginUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class RaterAiRecommendListener {

    @Resource
    TalentRecruitmentProcessService talentRecruitmentProcessService;

    @RabbitListener(containerFactory = "raterRaterAIRecommendFactory", queues = {"${application.rater-ai-recommend.queue}"})
    @RabbitHandler
    public void process(Message message) {
        log.info("[RaterAiRecommendListener] Received message: {}，Business data：{}", message.toString(), new String(message.getBody()));
        String aiRecommendStr = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            if (StrUtil.isBlank(aiRecommendStr)) {
                log.error("RaterAiRecommendListener message is error, data is null");
                return;
            }
            if (!JSONUtil.isJson(aiRecommendStr)) {
                log.error("RaterAiRecommendListener json is error,{}",aiRecommendStr);
                return;
            }
            LoginUtil.simulateLoginWithClient();
            JSONObject aiRecommendJson = JSONUtil.parseObj(aiRecommendStr);
            talentRecruitmentProcessService.aiRecommendByJobIdAndTalentId(aiRecommendJson);
            log.info("RaterAiRecommendListener is success");
        } catch (Exception e) {
            log.error("RaterAiRecommendListener received message is error, data = {}, msg = {}", aiRecommendStr, ExceptionUtil.getAllExceptionMsg(e));
//            NotificationUtils.sendAlertToLark(larkWebhookKey, larkWebhookUrl, "TalentKeyListener send message is error, msg \n" + ExceptionUtil.getAllExceptionMsg(e));
        }
    }
}
