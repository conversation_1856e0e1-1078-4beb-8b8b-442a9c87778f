package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessCommission;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;


/**
 * Spring Data  repository for the TalentRecruitmentProcessCommission entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessCommissionRepository extends JpaRepository<TalentRecruitmentProcessCommission, Long> {

    TalentRecruitmentProcessCommission findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessCommission t SET t.lastModifiedDate = current_timestamp, t.lastModifiedBy = ?2 WHERE t.talentRecruitmentProcessId = ?1")
    void updateLastModifiedDateAndLastModifiedBy(Long talentRecruitmentProcessId, String updatedBy);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessCommission t SET t.note = ?2, t.noteLastModifiedDate = current_timestamp, t.noteLastModifiedByUserId = ?3 WHERE t.talentRecruitmentProcessId = ?1")
    void updateNoteOnly(Long talentRecruitmentProcessId, String note, Long userId);

}
