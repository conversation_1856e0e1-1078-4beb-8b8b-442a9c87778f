package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboardDate;

/**
 * Service Interface for managing {@link TalentRecruitmentProcessOnboardDate}.
 */
public interface TalentRecruitmentProcessOnboardDateService {
    /**
     * Save a talentRecruitmentProcessOnboardDate.
     *
     * @param talentRecruitmentProcessOnboardDate the entity to save.
     * @return the persisted entity.
     */
    TalentRecruitmentProcessOnboardDate save(Long talentRecruitmentProcessId, TalentRecruitmentProcessOnboardDate talentRecruitmentProcessOnboardDate);

    TalentRecruitmentProcessOnboardDate findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
