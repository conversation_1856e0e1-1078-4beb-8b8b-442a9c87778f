package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.domain.enumeration.application.FeeType;
import com.altomni.apn.common.domain.enumeration.application.FeeTypeConverter;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * A TalentRecruitmentProcessOfferFeeCharge.
 */
@Entity
@Table(name = "talent_recruitment_process_offer_fee_charge")
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class TalentRecruitmentProcessOfferFeeCharge extends AbstractPermissionAuditingEntity implements Serializable {

    private static final long serialVersionUID = 2807552309770081919L;

    @JsonIgnore
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @JsonIgnore
    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "total_billable_amount")
    private BigDecimal totalBillableAmount;

    @Convert(converter = FeeTypeConverter.class)
    @Column(name = "fee_type")
    private FeeType feeType;

    @Column(name = "fee_amount")
    private BigDecimal feeAmount;

    @Column(name = "total_amount")
    private BigDecimal totalAmount;
}
