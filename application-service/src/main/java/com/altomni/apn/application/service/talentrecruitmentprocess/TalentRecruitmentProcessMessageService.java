package com.altomni.apn.application.service.talentrecruitmentprocess;

import com.altomni.apn.common.dto.message.MessageCreateWithNoPoachingSubmitDTO;

/**
 * talent流程消息提醒
 */
public interface TalentRecruitmentProcessMessageService {

    void sendKeyCandidateUpdateNotification(Long talentId);

    /**
     * 发送禁猎公司被推荐消息通知
     * @param messageDTO
     */
    void sendNoPoachingTalentSubmitNotification(MessageCreateWithNoPoachingSubmitDTO messageDTO);


}
