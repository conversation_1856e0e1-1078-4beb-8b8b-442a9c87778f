package com.altomni.apn.application.web.rest.vm;

import com.altomni.apn.application.domain.enumeration.EmploymentStatus;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TalentEmploymentStatusVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 若hasPermission为true，则当前用户可以提交该候选人到新的职位
     * 若hasPermission为false，则当前用户不可以提交该候选人到新的职位
     */
    private Boolean hasPermission = Boolean.TRUE;

    private List<ExistedApplication> existedApplicationList = new ArrayList<>();


    @Data
    @Accessors(chain = true)
    public static class ExistedApplication{
        private Long talentRecruitmentProcessId;

        private Long jobId;

        private String jobTitle;

        private Long talentId;

        private EmploymentStatus employmentStatus;
    }

}
