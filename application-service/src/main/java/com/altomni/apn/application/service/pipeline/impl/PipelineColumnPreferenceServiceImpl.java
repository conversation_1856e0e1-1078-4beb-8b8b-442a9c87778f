//package com.altomni.apn.application.service.pipeline.impl;
//
//import cn.hutool.core.util.ObjectUtil;
//import com.altomni.apn.application.domain.pipeline.PipelineColumnPreference;
//import com.altomni.apn.application.repository.pipeline.PipelineColumnPreferenceRepository;
//import com.altomni.apn.application.service.pipeline.PiplineColumnPreferenceService;
//import com.altomni.apn.common.utils.SecurityUtils;
//import com.altomni.apn.common.domain.enumeration.search.ModuleType;
//import com.altomni.apn.common.dto.application.pipeline.PipelineColumnPreferenceDTO;
//import com.altomni.apn.common.errors.CustomParameterizedException;
//import com.altomni.apn.common.utils.ServiceUtils;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.List;
//
//@Service
//public class PipelineColumnPreferenceServiceImpl implements PiplineColumnPreferenceService {
//
//    @Resource
//    private PipelineColumnPreferenceRepository pipelineColumnPreferenceRepository;
//
//    @Override
//    public PipelineColumnPreferenceDTO findOne(Long userId, ModuleType moduleType) {
//        PipelineColumnPreference result = pipelineColumnPreferenceRepository.findByUserIdAndModule(userId, moduleType);
//        if (ObjectUtil.isEmpty(result)) {
//            return new PipelineColumnPreferenceDTO();
//        }
//        return PipelineColumnPreference.toDto(result);
//    }
//
//    @Override
//    public void saveTemplate(PipelineColumnPreferenceDTO columnPreferenceDTO) {
//        if (columnPreferenceDTO.getTempName() == null || columnPreferenceDTO.getTempName().trim().length() == 0) {
//            throw new CustomParameterizedException("template name is null");
//        }
//        PipelineColumnPreference jb = pipelineColumnPreferenceRepository.findByTempNameAndUserId(columnPreferenceDTO.getTempName(), SecurityUtils.getUserId());
//        if (jb != null && columnPreferenceDTO.getId() == null) {
//            throw new CustomParameterizedException("template name already exist!");
//        } else if (jb != null && columnPreferenceDTO.getId() != null && columnPreferenceDTO.getId().longValue() != jb.getId().longValue()) {
//            throw new CustomParameterizedException("template name already exist!");
//        }
//        PipelineColumnPreference columnPreference = new PipelineColumnPreference();
//        ServiceUtils.myCopyProperties(columnPreferenceDTO, columnPreference);
//        columnPreference.setUserId(SecurityUtils.getUserId());
//        pipelineColumnPreferenceRepository.save(columnPreference);
//    }
//
//    @Override
//    public List<PipelineColumnPreference> findByUserIdAndModule(ModuleType moduleType) {
//        return pipelineColumnPreferenceRepository.findAllByUserIdAndModule(SecurityUtils.getUserId(), moduleType);
//    }
//
//    @Override
//    public void deleteById(Long id) {
//        pipelineColumnPreferenceRepository.deleteById(id);
//    }
//
//}
