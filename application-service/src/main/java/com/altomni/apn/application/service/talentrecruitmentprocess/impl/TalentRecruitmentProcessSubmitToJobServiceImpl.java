package com.altomni.apn.application.service.talentrecruitmentprocess.impl;

import com.altomni.apn.application.domain.TalentRecruitmentProcess;
import com.altomni.apn.application.domain.TalentRecruitmentProcessSubmitToJob;
import com.altomni.apn.application.repository.TalentRecruitmentProcessSubmitToJobRepository;
import com.altomni.apn.application.service.rule.TalentRecruitmentProcessSubmitToJobRule;
import com.altomni.apn.application.service.talent.TalentClient;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessIpgAgreedPayRateService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserService;
import com.altomni.apn.application.service.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobService;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.altomni.apn.common.domain.enumeration.talent.TalentDeclassifyType;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessSubmitToJobVO;
import com.altomni.apn.common.dto.talent.TalentAutoDeclassifyDto;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.Instant;

/**
 * Service Implementation for managing TalentRecruitmentProcessSubmitToJob.
 */
@Service
@Transactional
public class TalentRecruitmentProcessSubmitToJobServiceImpl implements TalentRecruitmentProcessSubmitToJobService {

    private final Logger log = LoggerFactory.getLogger(TalentRecruitmentProcessSubmitToJobServiceImpl.class);

    @Resource
    private TalentRecruitmentProcessSubmitToJobRepository submitToJobRepository;
    @Resource
    private TalentRecruitmentProcessKpiUserService kpiUserService;
    @Resource
    private TalentRecruitmentProcessIpgAgreedPayRateService agreedPayRateService;
    @Resource
    private TalentRecruitmentProcessSubmitToJobRule submitToJobRule;
    @Resource
    private TalentClient talentClient;

    @Override
    public void preProcessValidation(TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob) {
        submitToJobRule.preProcessValidate(talentRecruitmentProcessSubmitToJob);
    }

    /**
     * Save a talentRecruitmentProcessSubmitToJob.
     *
     * @param submitToJobVO the entity to save
     * @return the persisted entity
     */
    @Override
    public void create(TalentRecruitmentProcess talentRecruitmentProcess, TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
        StopWatch stopWatch = new StopWatch("[RecruitmentProcess: submit to job (7. create)] talentId: " + submitToJobVO.getTalentId() + ", jobId: " + submitToJobVO.getJobId());

        stopWatch.start("[7.1] Construct submitToJob entity");
        TalentRecruitmentProcessSubmitToJob create = TalentRecruitmentProcessSubmitToJob.fromVO(submitToJobVO);
        create.setTalentRecruitmentProcessId(talentRecruitmentProcess.getId());
        stopWatch.stop();

        stopWatch.start("[7.2] Save submitToJob entity");
        submitToJobRepository.save(create);
        stopWatch.stop();

        stopWatch.start("[7.3] Save agreedPayRate");
        agreedPayRateService.save(talentRecruitmentProcess.getId(), submitToJobVO.getAgreedPayRate());
        stopWatch.stop();

        stopWatch.start("[7.4] Save kpiUser");
        kpiUserService.save(talentRecruitmentProcess.getId(), submitToJobVO.getKpiUsers());
        stopWatch.stop();

        stopWatch.start("[7.5] Construct submitToJob VO");
        //TalentRecruitmentProcessSubmitToJobVO result = toVO(talentRecruitmentProcess.getId());
        stopWatch.stop();

        stopWatch.start("[7.6] validate");
        submitToJobRule.validate(talentRecruitmentProcess, submitToJobVO.getAgreedPayRate(), submitToJobVO.getKpiUsers());
        stopWatch.stop();

        log.info("[Application timelapse result] submitToJob(7. create): {}", stopWatch.prettyPrint());
        // 保密候选人，尝试自动解除保密
        talentClient.declassifyProcess(talentRecruitmentProcess.toTalentAutoDeclassifyDto(NodeType.SUBMIT_TO_JOB));

    }

    @Override
    public void update(Long talentRecruitmentProcessId, TalentRecruitmentProcessSubmitToJobVO submitToJobVO) {
        TalentRecruitmentProcessSubmitToJob exist = submitToJobRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (exist != null) {
            ServiceUtils.myCopyProperties(submitToJobVO, exist);
            exist.setLastModifiedBy(SecurityUtils.getUserUid());
            exist.setLastModifiedDate(Instant.now());
            submitToJobRepository.saveAndFlush(exist);
            agreedPayRateService.save(talentRecruitmentProcessId, submitToJobVO.getAgreedPayRate());
            kpiUserService.save(talentRecruitmentProcessId, submitToJobVO.getKpiUsers());
//            submitToJobRepository.updateLastModifiedDateAndLastModifiedBy(talentRecruitmentProcessId, SecurityUtils.getUserUid());
        }
    }

    @Override
    public void updateRecommendCommentsOnly(TalentRecruitmentProcessSubmitToJobVO talentRecruitmentProcessSubmitToJob) {
        this.updateRecommendCommentsOnly(talentRecruitmentProcessSubmitToJob.getTalentRecruitmentProcessId(), talentRecruitmentProcessSubmitToJob.getRecommendComments());
    }

    @Override
    public TalentRecruitmentProcessSubmitToJobVO updateRecommendCommentsOnly(Long talentRecruitmentProcessId, String recommendComments) {
        TalentRecruitmentProcessSubmitToJob exist = submitToJobRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId);
        if (exist != null) {
//            exist.setNote(note);
//            exist.setLastModifiedBy(SecurityUtils.getUserUid());
//            exist.setLastModifiedDate(Instant.now());
//            exist = submitToJobRepository.saveAndFlush(exist);
            submitToJobRepository.updateRecommendCommentsOnly(talentRecruitmentProcessId, recommendComments, SecurityUtils.getUserId());
            return null;
        } else {
            return null;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public TalentRecruitmentProcessSubmitToJobVO findOneByTalentRecruitmentProcessId(Long talentRecruitmentProcessId) {
        return toVO(talentRecruitmentProcessId);
    }

    private TalentRecruitmentProcessSubmitToJobVO toVO(TalentRecruitmentProcessSubmitToJob submitToJob) {
        if (submitToJob == null) {
            return null;
        }
        TalentRecruitmentProcessSubmitToJobVO result = new TalentRecruitmentProcessSubmitToJobVO();
        ServiceUtils.myCopyProperties(submitToJob, result);
        return result;
    }

    private TalentRecruitmentProcessSubmitToJobVO toVO(Long talentRecruitmentProcessId) {
        return toVO(submitToJobRepository.findByTalentRecruitmentProcessId(talentRecruitmentProcessId));
    }
}
