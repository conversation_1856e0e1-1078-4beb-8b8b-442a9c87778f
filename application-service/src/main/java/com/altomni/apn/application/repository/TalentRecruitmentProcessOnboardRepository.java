package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOnboard;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data  repository for the TalentRecruitmentProcessOnboard entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessOnboardRepository extends JpaRepository<TalentRecruitmentProcessOnboard, Long> {

    TalentRecruitmentProcessOnboard findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessOnboard t SET t.lastModifiedDate = current_timestamp, t.lastModifiedBy = ?2 WHERE t.talentRecruitmentProcessId = ?1")
    void updateLastModifiedDateAndLastModifiedBy(Long talentRecruitmentProcessId, String updatedBy);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessOnboard t SET t.note = ?2, t.noteLastModifiedDate = current_timestamp, t.noteLastModifiedByUserId = ?3 WHERE t.talentRecruitmentProcessId = ?1")
    void updateNoteOnly(Long talentRecruitmentProcessId, String note, Long userId);

    List<TalentRecruitmentProcessOnboard> findAllByTalentRecruitmentProcessIdIn(List<Long> recruitmentProcessIds);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessOnboard t SET t.lastModifiedDate = current_timestamp, t.lastModifiedBy = ?2,t.substituteTalentId=?3,t.relationProcessId=?4,t.isSubstituteTalent=?5 WHERE t.talentRecruitmentProcessId = ?1")
    void updateSubstituteTalentIdByTalentRecruitmentProcessId(Long talentRecruitmentProcessId, String updatedBy,Long substituteTalentId,Long relationProcessId,boolean isSubstituteTalent);
}
