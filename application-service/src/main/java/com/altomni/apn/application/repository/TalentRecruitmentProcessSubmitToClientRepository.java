package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessSubmitToClient;
import feign.Param;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data  repository for the TalentRecruitmentProcessSubmitToClient entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessSubmitToClientRepository extends JpaRepository<TalentRecruitmentProcessSubmitToClient, Long> {

    TalentRecruitmentProcessSubmitToClient findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessSubmitToClient t SET t.lastModifiedDate = current_timestamp, t.lastModifiedBy = ?2 WHERE t.talentRecruitmentProcessId = ?1")
    void updateLastModifiedDateAndLastModifiedBy(Long talentRecruitmentProcessId, String updatedBy);

//    @Modifying
//    @Transactional
//    @Query(value = "UPDATE TalentRecruitmentProcessSubmitToClient t SET t.note=:note WHERE t.talentRecruitmentProcessId=:talentRecruitmentProcessId")
//    void updateNoteOnly(@Param("talentRecruitmentProcessId") Long talentRecruitmentProcessId, @Param("note") String note);

    @Modifying
    @Transactional
    @Query(value = "UPDATE TalentRecruitmentProcessSubmitToClient t SET t.note = ?2, t.noteLastModifiedDate = current_timestamp, t.noteLastModifiedByUserId = ?3 WHERE t.talentRecruitmentProcessId = ?1")
    void updateNoteOnly(Long talentRecruitmentProcessId, String note, Long userId);

    List<TalentRecruitmentProcessSubmitToClient> findAllByTalentRecruitmentProcessIdIn(List<Long> talentRecruitmentProcessIds);

}
