package com.altomni.apn.application.web.rest.vm;

import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;

@Data
@Entity
public class KpiTopVo implements Serializable {

    @Id
    private Long userId;

    private String fullName;

    private BigDecimal amount;

    public String getAmount() {
        return amount.setScale(2, RoundingMode.DOWN).toPlainString();
    }

}
