package com.altomni.apn.application.service.dashboard;

import com.altomni.apn.application.web.rest.vm.KpiTopVo;
import com.altomni.apn.application.web.rest.vm.MyInvoiceVO;
import com.altomni.apn.application.web.rest.vm.TeamPerformanceVO;
import com.altomni.apn.job.web.rest.vm.MyJobVo;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.LinkedHashMap;
import java.util.List;

public interface DashboardService {

    LinkedHashMap<String, Integer> myApplicationCandidates(Instant startTime, Instant endTime,List<Long> userIdList);

    LinkedHashMap<String, Integer> myApplicationCandidates(Instant startTime, Instant endTime, List<Long> userIdList, List<Long> applicationIds);

    MyInvoiceVO myInvoices(Instant startTime, Instant endTime, List<Long> userIdList);

    List<KpiTopVo> kpiTop(Integer top, Integer currency);

    BigDecimal myPerformances(Integer currency);

    TeamPerformanceVO teamPerformances(Instant startTime, Instant endTime, Integer currency,List<Long> userIdList);

    MyJobVo myJobs(Instant startTime, Instant endTime, List<Long> userIdList);
}
