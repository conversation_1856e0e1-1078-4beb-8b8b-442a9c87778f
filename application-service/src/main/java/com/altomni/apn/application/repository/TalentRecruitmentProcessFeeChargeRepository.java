package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessOfferFeeCharge;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

/**
 * Spring Data SQL repository for the TalentRecruitmentProcessFeeCharge entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessFeeChargeRepository extends JpaRepository<TalentRecruitmentProcessOfferFeeCharge, Long> {

    TalentRecruitmentProcessOfferFeeCharge findByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    void deleteByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);
}
