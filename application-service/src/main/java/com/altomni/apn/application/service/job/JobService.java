package com.altomni.apn.application.service.job;

import com.altomni.apn.common.domain.enumeration.job.JobStatus;
import com.altomni.apn.common.dto.application.talentrecruitmentprocess.TalentRecruitmentProcessKpiUserVO;
import com.altomni.apn.common.dto.job.JobDTOV3;

import java.util.List;

public interface JobService {

    JobDTOV3 getJob(Long jobId);

    void updateStatus(Long jobId, JobStatus status);

    void updateNoSubmitCandidateReminderXxlJobForJob(Long jobId, List<TalentRecruitmentProcessKpiUserVO> kpiUsers);

    void updateNoInterviewCandidateReminderXxlJobForJob(Long jobId, List<TalentRecruitmentProcessKpiUserVO> kpiUsers);

}
