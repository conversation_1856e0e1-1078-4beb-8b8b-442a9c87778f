package com.altomni.apn.application.domain;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@Entity
@Table(name = "talent_recruitment_process_onboard_work_location")
public class TalentRecruitmentProcessOnboardWorkLocation
//        extends AbstractPermissionAuditingEntity implements Serializable
{

    private static final long serialVersionUID = 3085979140546737718L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "talent_recruitment_process_id")
    private Long talentRecruitmentProcessId;

    @Column(name = "official_country")
    private String officialCountry;

    @Column(name = "official_province")
    private String officialProvince;

    @Column(name = "official_county")
    private String officialCounty;

    @Column(name = "official_city")
    private String officialCity;

    @Column(name = "address_line")
    private String addressLine;

    @Column(name = "original_loc")
    private String originalLoc;

    @Column(name = "residential_location")
    private Long residentialLocation;

}
