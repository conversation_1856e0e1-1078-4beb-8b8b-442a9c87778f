package com.altomni.apn.application.web.rest;

import com.altomni.apn.application.service.schedule.ScheduleLabelService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@RestController
@RequestMapping("/api/v3/schedule-label")
@Slf4j
public class ScheduleResource {

    @Resource
    ScheduleLabelService scheduleLabelService;

    @PostMapping("")
    public ResponseEntity<Void> getScheduleLabel(@RequestBody List<Long> ids) {
        CompletableFuture.runAsync(() -> {
            scheduleLabelService.batchProcessLabel(ids);
        });
        return ResponseEntity.ok().build();
    }

    @GetMapping("/standardize-country")
    public ResponseEntity<Void> standardizeCountry() {
        scheduleLabelService.standardizeCountry();
        return ResponseEntity.ok().build();
    }
}
