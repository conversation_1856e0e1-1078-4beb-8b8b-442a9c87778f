package com.altomni.apn.application.repository;

import com.altomni.apn.application.domain.TalentRecruitmentProcessNode;
import com.altomni.apn.common.domain.enumeration.application.NodeStatus;
import com.altomni.apn.common.domain.enumeration.application.NodeType;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;


/**
 * Spring Data  repository for the TalentRecruitmentProcessNode entity.
 */
@SuppressWarnings("unused")
@Repository
public interface TalentRecruitmentProcessNodeRepository extends JpaRepository<TalentRecruitmentProcessNode, Long>, QuerydslPredicateExecutor<TalentRecruitmentProcessNode> {



    List<TalentRecruitmentProcessNode> findAllByTalentRecruitmentProcessId(Long talentRecruitmentProcessId);

    TalentRecruitmentProcessNode findByTalentRecruitmentProcessIdAndNodeStatus(Long talentRecruitmentProcessId, NodeStatus nodeStatus);

    @Query("SELECT t FROM TalentRecruitmentProcessNode t WHERE t.talentRecruitmentProcessId = :id AND t.nodeStatus IN (:statuses) ORDER BY CASE WHEN t.nodeStatus = 1 THEN 0 ELSE 1 END")
    List<TalentRecruitmentProcessNode> findCurrentNode(@Param("id") Long id, @Param("statuses") List<NodeStatus> statuses);

    TalentRecruitmentProcessNode findByTalentRecruitmentProcessIdAndNodeType(Long talentRecruitmentProcessId, NodeType nodeType);

    TalentRecruitmentProcessNode findByTalentRecruitmentProcessIdAndNodeId(Long talentRecruitmentProcessId, Long nodeId);

    Optional<TalentRecruitmentProcessNode> findFirstByTalentRecruitmentProcessIdInAndNodeTypeIsAndNodeStatus(List<Long> talentRecruitmentProcessIds, NodeType nodeType, NodeStatus nodeStatus);

    @Query(value = """
    SELECT
    	distinct trpn.talent_recruitment_process_id
    FROM
    	talent_recruitment_process_node trpn
    	inner join talent_recruitment_process trp on trp.id = trpn.talent_recruitment_process_id
    WHERE
    trp.talent_id = ?1
    """, nativeQuery = true)
    List<Long> findTalentRecruitmentProcessIdListByTalentId(Long talentId);

    List<TalentRecruitmentProcessNode> findAllByTalentRecruitmentProcessIdIn(List<Long> talentRecruitmentProcessIds);

}
