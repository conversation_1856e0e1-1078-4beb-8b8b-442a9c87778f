<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.3.xsd">

    <property name="now" value="now()" dbms="h2"/>
    <property name="now" value="now()" dbms="mysql, mariadb"/>
    <property name="floatType" value="float4" dbms="postgresql, h2"/>
    <property name="floatType" value="float" dbms="mysql, oracle, mssql, mariadb"/>
    <property name="clobType" value="clob" dbms="h2"/>
    <property name="clobType" value="clob" dbms="mysql, oracle, mssql, mariadb, postgresql"/>
    <property name="uuidType" value="varchar(36)" dbms="h2, mysql, mariadb"/>
    <property name="datetimeType" value="datetime(6)" dbms="mysql, mariadb"/>
    <property name="datetimeType" value="datetime" dbms="oracle, mssql, postgresql, h2"/>

<!--    <include file="config/liquibase/changelog/init/1666262757661_added_entity_PipelineColumnPreference.xml" relativeToChangelogFile="false"/>-->
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_RecruitmentProcess.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_RecruitmentProcessNode.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_RecruitmentProcessNodePageSection.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentOwnership.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcess.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessCommission.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessEliminate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessResignation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessInterview.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessIpgAgreedPayRate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessIpgContractFeeCharge.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessIpgFteSalaryPackage.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessIpgOfferAccept.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessIpgOfferLetterCostRate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessKpiUser.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessNode.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessOffer.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757663_added_entity_TalentRecruitmentProcessOfferFeeCharge.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_TalentRecruitmentProcessOfferSalary.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_TalentRecruitmentProcessOfferSalaryPackage.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_TalentRecruitmentProcessOnboard.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_TalentRecruitmentProcessOnboardClientInfo.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_TalentRecruitmentProcessOnboardDate.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_TalentRecruitmentProcessSubmitToClient.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757664_added_entity_TalentRecruitmentProcessSubmitToJob.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1689542678323_added_entity_TalentRecruitmentProcessOnboardWorkLocation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1726797926387_added_entity_TalentRecruitmentProcessAutoElimination.xml" relativeToChangelogFile="false"/>

</databaseChangeLog>
