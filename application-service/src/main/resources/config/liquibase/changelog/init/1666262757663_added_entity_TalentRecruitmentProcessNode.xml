<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264536596-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_recruitment_process_node"/>
            </not>
        </preConditions>
        <createTable tableName="talent_recruitment_process_node">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="talent_recruitment_process_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="node_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="node_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="node_status" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="next_node_id" type="BIGINT"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_trp_node_trpid" tableName="talent_recruitment_process_node">
            <column name="talent_recruitment_process_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
