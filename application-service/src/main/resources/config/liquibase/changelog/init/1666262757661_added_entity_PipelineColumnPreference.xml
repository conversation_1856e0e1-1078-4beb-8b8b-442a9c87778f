<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264108165-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="pipeline_column_preference"/>
            </not>
        </preConditions>
        <createTable tableName="pipeline_column_preference">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="user_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="creation_type" type="VARCHAR(255)"/>
            <column name="item_sort" type="TEXT"/>
            <column name="item_sort_all" type="TEXT"/>
            <column name="module" type="INT"/>
            <column name="description" type="VARCHAR(255)"/>
            <column name="sort_order" type="INT"/>
            <column name="del_flag" type="BIT(1)"/>
            <column name="type" type="BIT(1)"/>
            <column name="page_size" type="INT"/>
            <column name="temp_name" type="VARCHAR(255)"/>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>
    </changeSet>
</databaseChangeLog>
