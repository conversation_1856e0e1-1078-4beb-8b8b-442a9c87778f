<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1689542678323-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_recruitment_process_onboard_work_location"/>
            </not>
        </preConditions>
        <createTable tableName="talent_recruitment_process_onboard_work_location">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="talent_recruitment_process_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="official_country" type="VARCHAR(200)"/>
            <column name="official_province" type="VARCHAR(200)"/>
            <column name="official_county" type="VARCHAR(200)"/>
            <column name="official_city" type="VARCHAR(200)"/>
            <column name="address_line" type="VARCHAR(200)"/>

            <column name="original_loc" type="TEXT">
                <constraints nullable="false"/>
            </column>
            <column name="residential_location" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_talent_recruitment_process_onboard_work_location_trp_id" tableName="talent_recruitment_process_onboard_work_location">
            <column name="talent_recruitment_process_id"/>
        </createIndex>

    </changeSet>

</databaseChangeLog>
