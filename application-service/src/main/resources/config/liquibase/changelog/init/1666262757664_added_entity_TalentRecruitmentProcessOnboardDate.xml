<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264612544-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_recruitment_process_onboard_date"/>
            </not>
        </preConditions>
        <createTable tableName="talent_recruitment_process_onboard_date">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="talent_recruitment_process_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="onboard_date" type="date"/>
            <column name="warranty_end_date" type="date"/>
            <column name="end_date" type="date"/>
            <column name="currency" type="INT"/>
            <column name="rate_unit_type" type="INT"/>
            <column name="created_by" type="VARCHAR(50)"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
            <column name="puser_id" type="BIGINT"/>
            <column name="pteam_id" type="BIGINT"/>
        </createTable>

        <createIndex indexName="idx_trp_onboard_date_trpid" tableName="talent_recruitment_process_onboard_date">
            <column name="talent_recruitment_process_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
