<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264143185-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="recruitment_process_node_page_section"/>
            </not>
        </preConditions>
        <createTable tableName="recruitment_process_node_page_section">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>

            <column name="node_type" type="INT">
                <constraints nullable="false"/>
            </column>

            <column name="job_type" type="INT">
                <constraints nullable="true"/>
            </column>

            <column name="node_page_section" type="INT">
                <constraints nullable="true"/>
            </column>

            <column name="description" type="VARCHAR(255)">
                <constraints nullable="true"/>
            </column>

            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>

            <column name="created_date" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>

            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>

            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>

            <column name="field_config" type="TEXT">
                <constraints nullable="true"/>
            </column>

            <column name="recruitment_process_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <!-- 添加索引 -->
        <createIndex
                indexName="idx_recruitment_process_node_page_section_tid"
                tableName="recruitment_process_node_page_section">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
