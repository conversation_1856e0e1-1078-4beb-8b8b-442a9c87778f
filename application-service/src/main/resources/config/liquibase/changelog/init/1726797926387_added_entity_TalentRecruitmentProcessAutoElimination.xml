<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1726797926387-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_recruitment_process_auto_elimination"/>
            </not>
        </preConditions>
        <createTable tableName="talent_recruitment_process_auto_elimination">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>

            <column name="job_id" type="BIGINT">
                <constraints nullable="true"/>
            </column>

            <column name="auto" type="BIT(1)">
                <constraints nullable="true"/>
            </column>

            <column name="period" type="INT">
                <constraints nullable="true"/>
            </column>

            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>

            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>

            <column name="last_modified_by" type="VARCHAR(50)">
                <constraints nullable="true"/>
            </column>

            <column name="last_modified_date" type="TIMESTAMP">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <createIndex indexName="idx_trpae_auto_jobid_period" tableName="talent_recruitment_process_auto_elimination">
            <column name="auto"/>
            <column name="job_id"/>
            <column name="period"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
