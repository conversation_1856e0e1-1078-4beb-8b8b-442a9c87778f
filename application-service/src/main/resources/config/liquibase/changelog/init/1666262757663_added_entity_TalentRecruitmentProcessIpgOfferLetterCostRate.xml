<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264517714-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_recruitment_process_ipg_offer_letter_cost_rate"/>
            </not>
        </preConditions>
        <createTable tableName="talent_recruitment_process_ipg_offer_letter_cost_rate">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="currency" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="recruitment_process_id" type="BIGINT"/>
            <column name="rate_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="code" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="description" type="VARCHAR(250)"/>
            <column name="value" type="DECIMAL(10, 4)">
                <constraints nullable="false"/>
            </column>
            <column name="expire_date" type="date">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <addUniqueConstraint columnNames="tenant_id, currency, rate_type, code, value"
                             constraintName="idx_trp_ipg_offer_letter_cost_rate_tid"
                             tableName="talent_recruitment_process_ipg_offer_letter_cost_rate"/>
    </changeSet>

</databaseChangeLog>
