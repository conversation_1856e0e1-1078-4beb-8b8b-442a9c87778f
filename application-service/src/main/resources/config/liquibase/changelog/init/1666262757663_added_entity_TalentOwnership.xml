<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666264430697-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="talent_ownership"/>
            </not>
        </preConditions>
        <createTable tableName="talent_ownership">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="talent_id" type="BIGINT"/>
            <column name="user_id" type="BIGINT">
<!--                <constraints nullable="false"/>-->
            </column>
            <column name="tenant_id" type="BIGINT"/>

            <column name="ownership_type" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="user_role" type="INT"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="expire_time" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="talent_recruitment_process_id" type="BIGINT"/>
            <column name="auto_assigned" type="BIT">
            </column>
            <column name="created_by" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP(3)" name="created_date" type="timestamp">
                <constraints nullable="false"/>
            </column>
            <column name="last_modified_by" type="VARCHAR(50)"/>
            <column name="last_modified_date" type="timestamp"/>
        </createTable>

        <createIndex indexName="idx_talent_ownership_aid" tableName="talent_ownership">
            <column name="talent_recruitment_process_id"/>
        </createIndex>
        <createIndex indexName="idx_talent_ownership_tid" tableName="talent_ownership">
            <column name="talent_id"/>
        </createIndex>
        <createIndex indexName="idx_talent_ownership_uid" tableName="talent_ownership">
            <column name="user_id"/>
        </createIndex>
        <createIndex indexName="idx_talent_ownership_tenant_id" tableName="talent_ownership">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
