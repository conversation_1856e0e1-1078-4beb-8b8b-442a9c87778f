spring:
  application:
    name: application-service
  cloud:
    nacos:
      config:
        server-addr: ${NACOS-SERVICE-ADDR:10.0.0.70:8848} # make changes here if necessary
        username: ${NACOS-USERNAME:nacos}
        password: ${NACOS-PASSWORD:nacos}
        file-extension: yaml
        namespace: ${NAMESPACE:staging-jenkins-test} # make changes here if necessary
        shared-configs: public-log.yaml,public-spring-cloud.yaml,public-seata.yaml,public-datasource.yaml,public-actuator.yaml,public-mq.yaml,public-mongodb.yaml,public-redis.yaml,public-http.yaml,public-es.yaml,parser-service.yaml
        refresh-enabled: true