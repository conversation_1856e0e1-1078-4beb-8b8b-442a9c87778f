package com.altomni.apn.application.test.repository.dashboard;

import com.altomni.apn.application.domain.TalentRecruitmentProcessNode;
import com.altomni.apn.application.repository.TalentRecruitmentProcessNodeRepository;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class TalentRecruitmentProcessNodeRepositoryTest {
    @Mock
    private TalentRecruitmentProcessNodeRepository talentRecruitmentProcessNodeRepository;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testFindAll() {
        TalentRecruitmentProcessNode talentRecruitmentProcessNode = new TalentRecruitmentProcessNode();
        talentRecruitmentProcessNode.setId(1L);
        when(talentRecruitmentProcessNodeRepository.findAll()).thenReturn(Arrays.asList(talentRecruitmentProcessNode));
        List<TalentRecruitmentProcessNode> result = talentRecruitmentProcessNodeRepository.findAll();

        Assertions.assertThat(result).isNotEmpty();
        Assertions.assertThat(result.get(0).getId()).isEqualTo(talentRecruitmentProcessNode.getId());
    }
}
