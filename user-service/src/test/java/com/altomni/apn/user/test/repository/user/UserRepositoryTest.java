package com.altomni.apn.user.test.repository.user;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.user.repository.user.UserRepository;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.altomni.apn.user.test.common.user.UserCommon.genTestData;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class UserRepositoryTest {

  @Mock
  private UserRepository userRepository;

  @BeforeEach
  public void init() {
    MockitoAnnotations.openMocks(this);
  }

  @Test
  public void testFindBriefUsersByUids() {
    UserBriefDTO userBriefDTO = genTestData();
    Mockito.when(userRepository.findBriefUsersByUids(null)).thenReturn(List.of(userBriefDTO));
    List<UserBriefDTO> result = userRepository.findBriefUsersByUids(null);

    Assertions.assertThat(result).isNotEmpty();
    Assertions.assertThat(result.get(0).getFirstName()).isEqualTo(userBriefDTO.getFirstName());
  }
}
