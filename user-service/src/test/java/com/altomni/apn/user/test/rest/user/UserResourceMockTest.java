package com.altomni.apn.user.test.rest.user;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.user.web.rest.user.UserResource;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.altomni.apn.user.test.common.user.UserCommon.genTestData;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class UserResourceMockTest {
    @Mock
    private UserResource userResource;

    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetAllByUidIn() throws Exception {
        UserBriefDTO userBriefDTO = genTestData();
        Mockito.when(userResource.getAllByUidIn(null)).thenReturn(new ResponseEntity<>(List.of(userBriefDTO), HttpStatus.OK));
        ResponseEntity<List<UserBriefDTO>> response = userResource.getAllByUidIn(null);

        Assertions.assertThat(response.getBody()).isNotEmpty();
        Assertions.assertThat(response.getBody().get(0).getFirstName()).isEqualTo(userBriefDTO.getFirstName());
    }
}
