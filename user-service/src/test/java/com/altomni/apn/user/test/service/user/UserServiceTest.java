package com.altomni.apn.user.test.service.user;

import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.user.service.user.UserService;
import org.assertj.core.api.Assertions;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

import static com.altomni.apn.user.test.common.user.UserCommon.genTestData;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@ExtendWith(MockitoExtension.class)
public class UserServiceTest
{
    @Mock
    private UserService service;
    
    @BeforeEach
    public void init() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    public void testGetBriefUsersByUids() {
        UserBriefDTO userBriefDTO = genTestData();
        when(service.getBriefUsersByUids(null)).thenReturn(List.of(userBriefDTO));
        List<UserBriefDTO> result = service.getBriefUsersByUids(null);

        Assertions.assertThat(result).isNotEmpty();
        Assertions.assertThat(result.get(0).getFirstName()).isEqualTo(userBriefDTO.getFirstName());
    }
}
