package com.altomni.apn.user.service.permission.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.permission.PermissionTenantModule;
import com.altomni.apn.user.repository.permission.PermissionTenantModuleRepository;
import com.altomni.apn.user.service.permission.PermissionTenantModuleService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTenantInvolvedDataPermissionModuleVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTenantModuleVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
@Transactional
public class PermissionTenantModuleServiceImpl implements PermissionTenantModuleService {

    @Resource
    private PermissionTenantModuleRepository permissionTenantModuleRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public PermissionTenantModule create(PermissionTenantModule permissionTenantModule) {
        permissionTenantModule.setId(null);
        return permissionTenantModuleRepository.save(permissionTenantModule);
    }

    @Override
    public PermissionTenantModule update(PermissionTenantModule permissionTenantModule) {
        return permissionTenantModuleRepository.save(permissionTenantModule);
    }

    @Override
    public void updateAll(PermissionTenantModuleVM permissionTenantModuleVM) {
        List<PermissionTenantModule> tenantModuleList = new ArrayList<>();
        for (Long involvedModuleId: permissionTenantModuleVM.getInvolvedModules()){
            tenantModuleList.add(new PermissionTenantModule()
                    .setTenantId(permissionTenantModuleVM.getTenantId())
                    .setModuleId(involvedModuleId)
                    .setTenantId(SecurityUtils.getTenantId())
                    .setInvolveDataPermission(Boolean.TRUE));
        }
        for (Long uninvolvedModuleId: permissionTenantModuleVM.getUninvolvedModules()){
            tenantModuleList.add(new PermissionTenantModule()
                    .setTenantId(permissionTenantModuleVM.getTenantId())
                    .setModuleId(uninvolvedModuleId)
                    .setTenantId(SecurityUtils.getTenantId())
                    .setInvolveDataPermission(Boolean.FALSE));
        }
        permissionTenantModuleRepository.deleteAllByTenantId(permissionTenantModuleVM.getTenantId());
        permissionTenantModuleRepository.saveAll(tenantModuleList);
    }

    @Override
    public void delete(Long id) {
        permissionTenantModuleRepository.deleteById(id);
    }

    @Override
    public PermissionTenantModuleVM findByTenantId(Long tenantId) {
        List<PermissionTenantModule> tenantModuleList = permissionTenantModuleRepository.findAllByTenantId(tenantId);
        PermissionTenantModuleVM permissionTenantModuleVM = new PermissionTenantModuleVM();
        Set<Long> involvedModules = new HashSet<>();
        Set<Long> uninvolvedModules = new HashSet<>();
        tenantModuleList.forEach(tenantModule -> {
            if (tenantModule.getInvolveDataPermission()){
                involvedModules.add(tenantModule.getModuleId());
            }else{
                uninvolvedModules.add(tenantModule.getModuleId());
            }
        });
        permissionTenantModuleVM.setTenantId(tenantId);
        permissionTenantModuleVM.setInvolvedModules(involvedModules);
        permissionTenantModuleVM.setUninvolvedModules(uninvolvedModules);
        return permissionTenantModuleVM;
    }

    @Override
    public List<PermissionTenantInvolvedDataPermissionModuleVM> findModulesByCurrentTenant() {
        Long tenantId = SecurityUtils.getTenantId();
        if (tenantId < 0){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTENANT_FINDMODULESBYCURRENTTENANT_INVALIDTENANT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(tenantId),userApiPromptProperties.getUserService()));
        }
        return permissionTenantModuleRepository.findInvolvedModulesByTenantId(tenantId);
    }
}
