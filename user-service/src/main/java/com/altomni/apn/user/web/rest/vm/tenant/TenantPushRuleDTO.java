package com.altomni.apn.user.web.rest.vm.tenant;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.enumeration.PushConfigType;
import lombok.Data;

import java.util.List;

@Data
public class TenantPushRuleDTO extends AbstractAuditingEntity {
    private Long id;
    private Long interval;
    private List<Long> teamIds;
    private List<Long> userIds;
    private List<PushConfigType> type;
}
