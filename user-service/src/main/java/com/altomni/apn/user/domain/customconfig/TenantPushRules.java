package com.altomni.apn.user.domain.customconfig;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;
import java.util.Set;

@Data
@Entity
@Table(name = "tenant_push_rules")
public class TenantPushRules extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "push_interval")
    private Long push_interval;

    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "tenant_push_rules_id")
    private Set<TenantPushRulesTeam> tenantPushRulesTeamSet;

    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "tenant_push_rules_id")
    private Set<TenantPushRulesUser> tenantPushRulesUserSet;

    @OneToMany(fetch = FetchType.EAGER, cascade = {CascadeType.ALL}, orphanRemoval = true)
    @JoinColumn(name = "tenant_push_rules_id")
    private Set<TenantPushRulesType> tenantPushRulesTypeSet;
}
