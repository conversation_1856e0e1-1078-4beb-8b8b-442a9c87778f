package com.altomni.apn.user.domain.customconfig;

import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "tenant_push_rules_user")
public class TenantPushRulesUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_push_rules_id")
    private Long tenantPushRulesId;

    @Column(name = "user_id")
    private Long userId;
}
