package com.altomni.apn.user.domain.customconfig;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A Job Column Config.
 */
@ApiModel(description = "JobColumnConfig is used to save user custom column config")
@Data
@Entity
@Table(name = "job_column_config")
public class JobColumnConfig extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "must not be null")
    @Column(name = "user_id")
    private Long userId;

    @ApiModelProperty("store raw json config and convert it into class in DTO")
    @Column(name = "custom_config")
    @JsonRawValue
    private String customConfig;


}
