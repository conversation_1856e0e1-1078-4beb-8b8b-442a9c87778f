package com.altomni.apn.user.service.mapper.permission;
import com.altomni.apn.user.domain.permission.PermissionTable;
import com.altomni.apn.user.service.dto.permission.PermissionTableDTO;
import com.altomni.apn.user.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity Tag and its DTO TagDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface TableMapper extends EntityMapper<PermissionTableDTO, PermissionTable> {

    default PermissionTable fromId(Long id) {
        if (id == null) {
            return null;
        }
        PermissionTable permissionTable = new PermissionTable();
        permissionTable.setId(id);
        return permissionTable;
    }
}
