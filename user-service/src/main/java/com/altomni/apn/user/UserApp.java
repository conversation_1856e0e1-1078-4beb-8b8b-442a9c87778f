package com.altomni.apn.user;

import com.altomni.apn.common.aop.request.SignGeneratorConfig;
import com.altomni.apn.common.auth.agency_auth.JwtAgencyUserTokenStore;
import com.altomni.apn.common.config.*;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.config.email.EmailProperties;
import com.altomni.apn.common.config.env.CommonApplicationProperties;
import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import com.altomni.apn.common.config.loadbalancer.LoadBalanceConfiguration;
import com.altomni.apn.common.config.rabbitmq.EsFillerRabbitMqConfig;
import com.altomni.apn.common.config.rabbitmq.JobdivaRabbitMqConfig;
import com.altomni.apn.common.datapermission.config.DataPermissionAutoConfiguration;
import com.altomni.apn.common.datapermission.rule.team.TeamDataPermissionRule;
import com.altomni.apn.common.errors.ExceptionTranslator;
import com.altomni.apn.common.interceptor.CustomResponseBodyAdviceAdapter;
import com.altomni.apn.common.interceptor.FeignClientInterceptor;
import com.altomni.apn.common.interceptor.SecurityDataLevelInterceptor;
import com.altomni.apn.common.interceptor.SecurityObjectLevelInterceptor;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.cache.CachedFeignSsoUserMapping;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.canal.impl.CanalServiceImpl;
import com.altomni.apn.common.service.http.impl.HttpServiceImpl;
import com.altomni.apn.common.service.log.impl.LoggingServiceImpl;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.SpringUtil;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.loadbalancer.annotation.LoadBalancerClients;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Import;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;


/**
 * <AUTHOR>
 */
@EnableJpaRepositories("com.altomni.apn.*.repository")
@EntityScan("com.altomni.apn.*.domain")
@EnableFeignClients(basePackages = {"com.altomni.apn"})
@SpringBootApplication
@EnableDiscoveryClient
@EnableCaching
@EnableAsync
@EnableScheduling
@LoadBalancerClients(defaultConfiguration = LoadBalanceConfiguration.class)
@Import({ExceptionTranslator.class,
        PublicBeanInjection.class,
        HttpServiceImpl.class,
        DataPermissionAutoConfiguration.class,
        CachePermission.class,
        CachedFeignSsoUserMapping.class,
        CommonRedisService.class,
        CommonApplicationProperties.class,
        TeamDataPermissionRule.class,
        FeignClientInterceptor.class,
        SecurityObjectLevelInterceptor.class,
        WebMvcConfig.class,
        SecurityDataLevelInterceptor.class,
        CustomResponseBodyAdviceAdapter.class,
        JobdivaRabbitProperties.class,
        LoggingServiceImpl.class,
        SecurityUtils.class,
        AppInit.class,
        SpringUtil.class,
        JacksonConfiguration.class,
        GlobalCacheConfig.class,
        EmailProperties.class,
        ApplicationIPGProperties.class,
        CanalServiceImpl.class,
        SyncToEsConfig.class,
        EsFillerRabbitMqConfig.class,
        JobdivaRabbitProperties.class,
        JobdivaRabbitMqConfig.class,
        EnumCommonService.class,
        CacheConfig.class,
        CommonApiMultilingualConfig.class,
        NoRepeatSubmitAspectConfiguration.class,
        JwtAgencyUserTokenStore.class,
        SignGeneratorConfig.class})
public class UserApp {

    public static void main(String[] args) {
        SpringApplication.run(UserApp.class, args);
    }

}
