package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.service.dto.customconfig.PipelineColumnPreferenceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;


@Mapper(componentModel = "spring", uses = JsonMapperUtil.class)
public interface PipelineColumnConfigMapper {

    default UserPreference fromId(Long id) {
        if (id == null) {
            return null;
        }
        UserPreference userPreference = new UserPreference();
        userPreference.setId(id);
        return userPreference;
    }


    @Mapping(target = "talentPipelineColumnConfig", source = "customConfig", qualifiedByName = "customColumnConfigToJsonString")
    UserPreference toEntity(PipelineColumnPreferenceDTO dto);

    @Mapping(target = "customConfig", source = "talentPipelineColumnConfig", qualifiedByName = "jsonStringToCustomColumnConfig")
    @Mapping(target = "id", ignore = true)
    PipelineColumnPreferenceDTO toDto(UserPreference entity);

    List<PipelineColumnPreferenceDTO> toDto(List<UserPreference> entity);

}