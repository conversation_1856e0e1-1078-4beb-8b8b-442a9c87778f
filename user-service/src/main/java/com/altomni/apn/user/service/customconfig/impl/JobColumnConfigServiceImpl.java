package com.altomni.apn.user.service.customconfig.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.application.ApplicationService;
import com.altomni.apn.user.service.customconfig.JobColumnConfigService;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.dto.customconfig.CustomColumnField;
import com.altomni.apn.user.service.dto.customconfig.JobColumnConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.UserCustomConfig;
import com.altomni.apn.user.service.dto.customconfig.PageConfig;
import com.altomni.apn.user.service.mapper.customconfig.JobColumnConfigMapper;
import com.altomni.apn.user.service.mapper.customconfig.JsonMapperUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class JobColumnConfigServiceImpl implements JobColumnConfigService {
    final static Category COLUMN_CONFIG = Category.JOB_COLUMN;

    final static ConfigSubcategory JOB_DEFAULT_COLUMN = ConfigSubcategory.JOB_COLUMN_DEFAULT;

    final static ConfigSubcategory JOB_CUSTOMIZED_COLUMN = ConfigSubcategory.JOB_COLUMN_CUSTOMIZED;

    final static ConfigSubcategory PRIVATE_JOB_COLUMN_CUSTOMIZED = ConfigSubcategory.PRIVATE_JOB_COLUMN_CUSTOMIZED;

    final static String STRING_KEY_PREFIX = "Config:JobColumn:";

    final static String STRING_KEY_PREFIX_PRIVATE_JOB = "Config:PrivateJobColumn:";

    //field to dynamic update
    final static String FIELD_JOB_TYPE = "jobType";

    @Resource
    private ApplicationIPGProperties applicationIPGProperties;

    @Resource
    private SystemConfigDefaultService systemConfigDefaultService;

    @Resource
    private ApplicationService applicationService;

    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    JobColumnConfigMapper jobColumnConfigMapper;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;


//    public BaseConfig getColumnConfigByUserId(Long userId) {
//        if(userId == null){
//            throw new CustomParameterizedException("UserId is invalid");
//        }
//
//        Optional<JobColumnConfig> result = Optional.ofNullable(jobColumnConfigRepository.findByUserId(userId));
//        if (result.isPresent()) {
//            return jobColumnConfigMapper.toDto(result.get());
//        } else {
//            return systemConfigDefaultService.getSystemConfigByCategory(category);
//        }
//    }

    @Override
    public Boolean removeAllCachedJobColumnConfigByTenantId(Long tenantId) {
        String pattern = GetColumnConfigPrefixKey(tenantId) + "*";
        Integer count = commonRedisService.batchDeleteByPattern(pattern);
        return count != 0;
    }


    @Override
    public BaseConfig getColumnConfigByUserId(Long userId) {
        if (userId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        BaseConfig configFromRedis = getColumnConfigFromRedisByUserId(userId);
        if (configFromRedis != null) {
            return configFromRedis;
        }
        JobColumnConfigDTO jobColumnFromDB = getColumnConfigFromDBByUserId(userId);
        saveColumnConfigToRedisByUserId(userId, jobColumnFromDB.getCustomConfig());
        return jobColumnFromDB;

    }

    @Override
    public BaseConfig getPrivateJobColumnConfigByUserId(Long userId) {
        if (userId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        BaseConfig configFromRedis = getPrivateJobColumnConfigFromRedisByUserId(userId);
        if (configFromRedis != null) {
            return configFromRedis;
        }
        JobColumnConfigDTO jobColumnFromDB = getPrivateJobColumnConfigFromDBByUserId(userId);
        savePrivateJobColumnConfigToRedisByUserId(userId, jobColumnFromDB.getCustomConfig());
        return jobColumnFromDB;

    }

    public BaseConfig getColumnConfigFromRedisByUserId(Long userId) {
        String config = commonRedisService.get(GetColumnConfigKey(SecurityUtils.getTenantId(), userId));
        if (StringUtils.isBlank(config)) {
            return null;
        }
        JobColumnConfigDTO jobColumnConfigDTO = new JobColumnConfigDTO();
        jobColumnConfigDTO.setCustomConfig(JsonMapperUtil.jsonStringToCustomColumnConfig(config));
        return jobColumnConfigDTO;
    }

    public BaseConfig getPrivateJobColumnConfigFromRedisByUserId(Long userId) {
        String config = commonRedisService.get(getPrivateJobColumnConfigKey(SecurityUtils.getTenantId(), userId));
        if (StringUtils.isBlank(config)) {
            return null;
        }
        JobColumnConfigDTO jobColumnConfigDTO = new JobColumnConfigDTO();
        jobColumnConfigDTO.setCustomConfig(JsonMapperUtil.jsonStringToCustomColumnConfig(config));
        return jobColumnConfigDTO;
    }

    public void saveColumnConfigToRedisByUserId(Long userId, UserCustomConfig userCustomConfig) {
        String config = JsonMapperUtil.customColumnConfigToJsonString(userCustomConfig);
        commonRedisService.set(GetColumnConfigKey(SecurityUtils.getTenantId(), userId), config);
    }

    public void savePrivateJobColumnConfigToRedisByUserId(Long userId, UserCustomConfig userCustomConfig) {
        String config = JsonMapperUtil.customColumnConfigToJsonString(userCustomConfig);
        commonRedisService.set(getPrivateJobColumnConfigKey(SecurityUtils.getTenantId(), userId), config);
    }


    public JobColumnConfigDTO getColumnConfigFromDBByUserId(Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        if (userPreference == null || StringUtils.isBlank(userPreference.getJobColumnConfig())) {
            return getConvertJobColumnDefaultConfig();
        }

        JobColumnConfigDTO jobColumnConfigDTO = jobColumnConfigMapper.toDto(userPreference);
        setCustomFieldProperty(jobColumnConfigDTO.getCustomConfig().getColumnConfig());
        return jobColumnConfigDTO;

        //recruitment exist but the config is null
//        if (userPreference.getRecruitmentProcessId() != null) {
//            JobColumnConfigDTO jobColumnConfigDTO = jobColumnConfigMapper.toDto(userPreference);
//            jobColumnConfigDTO.setCustomConfig(JsonMapperUtil.jsonStringToCustomColumnFieldList(getJobColumnDefaultConfig().getDefaultConfig()));
//            setCustomFieldProperty(jobColumnConfigDTO.getCustomConfig());
//            return jobColumnConfigDTO;
//        }

    }

    public JobColumnConfigDTO getPrivateJobColumnConfigFromDBByUserId(Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        if (userPreference == null || StringUtils.isBlank(userPreference.getPrivateJobColumnConfig())) {
            return getConvertPrivateJobColumnDefaultConfig();
        }

        JobColumnConfigDTO jobColumnConfigDTO = jobColumnConfigMapper.toDtoForPrivateJob(userPreference);
        setCustomFieldProperty(jobColumnConfigDTO.getCustomConfig().getColumnConfig());
        return jobColumnConfigDTO;
    }

    private void setCustomFieldProperty(List<CustomColumnField> customConfigFields) {
        Long count = applicationService.countMyRecruitmentProcess().getBody();
        if (count > 1) {
            return;
        }

        customConfigFields.forEach(customColumnField -> {
            if (FIELD_JOB_TYPE.equals(customColumnField.getField())) {
                customColumnField.setActive(false);
            }
        });
    }

    private String GetColumnConfigKey(Long tenantId, Long userId) {
        return GetColumnConfigPrefixKey(tenantId) + userId;
    }

    private String getPrivateJobColumnConfigKey(Long tenantId, Long userId) {
        return getPrivateJobColumnConfigPrefixKey(tenantId) + userId;
    }

    private String GetColumnConfigPrefixKey(Long tenantId) {
        return STRING_KEY_PREFIX + tenantId + ":";
    }

    private String getPrivateJobColumnConfigPrefixKey(Long tenantId) {
        return STRING_KEY_PREFIX_PRIVATE_JOB + tenantId + ":";
    }

    private JobColumnConfigDTO getConvertJobColumnDefaultConfig() {
        JobColumnConfigDTO jobColumnConfigDTO = initUserJobColumnConfig(SecurityUtils.getUserId(), getJobColumnDefaultConfig().getDefaultConfig(), null);
        setCustomFieldProperty(jobColumnConfigDTO.getCustomConfig().getColumnConfig());
        return jobColumnConfigDTO;
    }

    private JobColumnConfigDTO getConvertPrivateJobColumnDefaultConfig() {
        JobColumnConfigDTO jobColumnConfigDTO = initUserPrivateJobColumnConfig(SecurityUtils.getUserId(), getPrivateJobColumnDefaultConfig().getDefaultConfig(), null);
        setCustomFieldProperty(jobColumnConfigDTO.getCustomConfig().getColumnConfig());
        return jobColumnConfigDTO;
    }

    private SystemConfigDefault getJobColumnDefaultConfig() {
        if (applicationIPGProperties.getIpgRuleTenantIds().contains(SecurityUtils.getTenantId())) { // customized
            return systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(COLUMN_CONFIG, JOB_CUSTOMIZED_COLUMN);
        }
        //general
        else if (SecurityUtils.getUserType() == TenantUserTypeEnum.EMPLOYER) {
            return systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(COLUMN_CONFIG, ConfigSubcategory.JOB_COLUMN_GENERAL_EMPLOYER);
        } else if (SecurityUtils.getUserType() == TenantUserTypeEnum.HEADHUNTER) {
            return systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(COLUMN_CONFIG, ConfigSubcategory.JOB_COLUMN_HEADHUNTER);
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETJOBCOLUMNDEFAULTCONFIG_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
    }

    private SystemConfigDefault getPrivateJobColumnDefaultConfig() {
        if (applicationIPGProperties.getIpgRuleTenantIds().contains(SecurityUtils.getTenantId())) { // customized
            return systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(COLUMN_CONFIG, PRIVATE_JOB_COLUMN_CUSTOMIZED);
        }
        //general
        else if (SecurityUtils.getUserType() == TenantUserTypeEnum.EMPLOYER) {
            return systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(COLUMN_CONFIG, ConfigSubcategory.JOB_COLUMN_GENERAL_EMPLOYER);
        } else if (SecurityUtils.getUserType() == TenantUserTypeEnum.HEADHUNTER) {
            return systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(COLUMN_CONFIG, ConfigSubcategory.JOB_COLUMN_HEADHUNTER);
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETJOBCOLUMNDEFAULTCONFIG_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
    }

    @Override
    public JobColumnConfigDTO saveJobColumnConfig(JobColumnConfigDTO jobColumnConfigDTO, Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference != null) {
            userPreference.setJobColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(jobColumnConfigDTO.getCustomConfig()));
        } else {
            jobColumnConfigDTO.setUserId(userId);
            userPreference = jobColumnConfigMapper.toEntity(jobColumnConfigDTO);
        }

        jobColumnConfigDTO = saveAndConvertUserPreference(userPreference);
        saveColumnConfigToRedisByUserId(userId, jobColumnConfigDTO.getCustomConfig());

        return jobColumnConfigDTO;
    }

    @Override
    public JobColumnConfigDTO savePrivateJobColumnConfig(JobColumnConfigDTO jobColumnConfigDTO, Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference != null) {
            userPreference.setPrivateJobColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(jobColumnConfigDTO.getCustomConfig()));
        } else {
            jobColumnConfigDTO.setUserId(userId);
            userPreference = jobColumnConfigMapper.toEntity(jobColumnConfigDTO);
            userPreference.setPrivateJobColumnConfig(userPreference.getJobColumnConfig());
            userPreference.setJobColumnConfig(null);
        }

        jobColumnConfigDTO = saveAndConvertUserPreferenceForPrivateJob(userPreference);
        savePrivateJobColumnConfigToRedisByUserId(userId, jobColumnConfigDTO.getCustomConfig());
        return jobColumnConfigDTO;
    }

    public JobColumnConfigDTO saveJobPageConfig(PageConfig pageConfig, Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        JobColumnConfigDTO jobColumnConfigDTO;
        if (userPreference != null && userPreference.getJobColumnConfig() != null) {
            jobColumnConfigDTO = jobColumnConfigMapper.toDto(userPreference);
            jobColumnConfigDTO.getCustomConfig().setPageConfig(pageConfig);
            userPreference.setJobColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(jobColumnConfigDTO.getCustomConfig()));
        } else {
            jobColumnConfigDTO = initUserJobColumnConfig(SecurityUtils.getUserId(), getJobColumnDefaultConfig().getDefaultConfig(), pageConfig);
            userPreference = jobColumnConfigMapper.toEntity(jobColumnConfigDTO);
        }

        jobColumnConfigDTO = saveAndConvertUserPreference(userPreference);
        setCustomFieldProperty(jobColumnConfigDTO.getCustomConfig().getColumnConfig());

        return jobColumnConfigDTO;
    }

    public JobColumnConfigDTO savePrivateJobPageConfig(PageConfig pageConfig, Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        JobColumnConfigDTO jobColumnConfigDTO;
        if (userPreference != null && userPreference.getPrivateJobColumnConfig() != null) {
            jobColumnConfigDTO = jobColumnConfigMapper.toDto(userPreference);
            jobColumnConfigDTO.getCustomConfig().setPageConfig(pageConfig);
            userPreference.setPrivateJobColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(jobColumnConfigDTO.getCustomConfig()));
        } else {
            jobColumnConfigDTO = initUserPrivateJobColumnConfig(SecurityUtils.getUserId(), getJobColumnDefaultConfig().getDefaultConfig(), pageConfig);
            userPreference = jobColumnConfigMapper.toEntity(jobColumnConfigDTO);
        }

        jobColumnConfigDTO = saveAndConvertUserPreferenceForPrivateJob(userPreference);
        setCustomFieldProperty(jobColumnConfigDTO.getCustomConfig().getColumnConfig());

        return jobColumnConfigDTO;
    }

    private JobColumnConfigDTO saveAndConvertUserPreference(UserPreference userPreference) {
        userPreference = userPreferenceRepository.saveAndFlush(userPreference);
        return jobColumnConfigMapper.toDto(userPreference);
    }

    private JobColumnConfigDTO saveAndConvertUserPreferenceForPrivateJob(UserPreference userPreference) {
        userPreference = userPreferenceRepository.saveAndFlush(userPreference);
        return jobColumnConfigMapper.toDtoForPrivateJob(userPreference);
    }

    public JobColumnConfigDTO initUserJobColumnConfig(Long userId, String columnConfig, PageConfig pageConfig) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(SecurityUtils.getUserId());
        }

        UserCustomConfig userCustomConfig = new UserCustomConfig();
        if (columnConfig != null) {
            userCustomConfig.setColumnConfig(JsonMapperUtil.jsonStringToCustomColumnFieldList(columnConfig));
        }
        if (pageConfig != null) {
            userCustomConfig.setPageConfig(pageConfig);
        }

        userPreference.setJobColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(userCustomConfig));

        return saveAndConvertUserPreference(userPreference);
    }

    public JobColumnConfigDTO initUserPrivateJobColumnConfig(Long userId, String columnConfig, PageConfig pageConfig) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(SecurityUtils.getUserId());
        }

        UserCustomConfig userCustomConfig = new UserCustomConfig();
        if (columnConfig != null) {
            userCustomConfig.setColumnConfig(JsonMapperUtil.jsonStringToCustomColumnFieldList(columnConfig));
        }
        if (pageConfig != null) {
            userCustomConfig.setPageConfig(pageConfig);
        }

        userPreference.setPrivateJobColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(userCustomConfig));

        return saveAndConvertUserPreferenceForPrivateJob(userPreference);
    }

    @Override
    public PageConfig updateJobPageConfig(PageConfig pageConfig) {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        PageConfig pageConfigFromRedis = getPageConfigFromRedisByUserId(SecurityUtils.getUserId());
        JobColumnConfigDTO jobColumnConfigDTO;
        if (pageConfigFromRedis == null) {
            jobColumnConfigDTO = initUserJobColumnConfig(SecurityUtils.getUserId(), getJobColumnDefaultConfig().getDefaultConfig(), pageConfig);
        } else {
            jobColumnConfigDTO = saveJobPageConfig(pageConfig, SecurityUtils.getUserId());
        }

        saveColumnConfigToRedisByUserId(SecurityUtils.getUserId(), jobColumnConfigDTO.getCustomConfig());
        return jobColumnConfigDTO.getCustomConfig().getPageConfig();
    }

    @Override
    public PageConfig updatePrivateJobPageConfig(PageConfig pageConfig) {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        PageConfig pageConfigFromRedis = getPrivatePageConfigFromRedisByUserId(SecurityUtils.getUserId());
        JobColumnConfigDTO jobColumnConfigDTO;
        if (pageConfigFromRedis == null) {
            jobColumnConfigDTO = initUserPrivateJobColumnConfig(SecurityUtils.getUserId(), getJobColumnDefaultConfig().getDefaultConfig(), pageConfig);
        } else {
            jobColumnConfigDTO = savePrivateJobPageConfig(pageConfig, SecurityUtils.getUserId());
        }

        savePrivateJobColumnConfigToRedisByUserId(SecurityUtils.getUserId(), jobColumnConfigDTO.getCustomConfig());
        return jobColumnConfigDTO.getCustomConfig().getPageConfig();
    }

    @Override
    public BaseConfig getCompanyJobColumnConfigByUserId(Long userId) {
        if (SecurityUtils.getUserId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        if (userPreference != null && StringUtils.isNotBlank(userPreference.getCompanyJobColumnConfig())) {
            String companyJobColumnConfig = userPreference.getCompanyJobColumnConfig();
            JobColumnConfigDTO result = new JobColumnConfigDTO();
            result.setUserId(userId);
            result.setCustomConfig(JsonMapperUtil.jsonStringToCustomColumnConfig(companyJobColumnConfig));
            return result;
        } else {
            SystemConfigDefault systemConfigDefault = systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(Category.JOB_COLUMN, ConfigSubcategory.COMPANY_JOB_COLUMN_CUSTOMIZED);
            String defaultConfig = systemConfigDefault.getDefaultConfig();
            UserCustomConfig userCustomConfig = new UserCustomConfig();
            if (defaultConfig != null) {
                userCustomConfig.setColumnConfig(JsonMapperUtil.jsonStringToCustomColumnFieldList(defaultConfig));
            }
            JobColumnConfigDTO result = new JobColumnConfigDTO();
            result.setUserId(userId);
            result.setCustomConfig(userCustomConfig);
            return result;
        }
    }

    @Override
    @Transactional
    public JobColumnConfigDTO saveCompanyJobColumnConfig(JobColumnConfigDTO jobColumnConfigDTO, Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(userId);
        }

        userPreference.setCompanyJobColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(jobColumnConfigDTO.getCustomConfig()));

        userPreferenceRepository.saveAndFlush(userPreference);
        return jobColumnConfigDTO;
    }

    private PageConfig getPrivatePageConfigFromRedisByUserId(Long userId) {
        String config = commonRedisService.get(getPrivateJobColumnConfigKey(SecurityUtils.getTenantId(), userId));
        if (StringUtils.isBlank(config)) {
            return null;
        }
        JobColumnConfigDTO jobColumnConfigDTO = new JobColumnConfigDTO();
        jobColumnConfigDTO.setCustomConfig(JsonMapperUtil.jsonStringToCustomColumnConfig(config));
        return jobColumnConfigDTO.getCustomConfig().getPageConfig();
    }

    public PageConfig getPageConfigFromRedisByUserId(Long userId) {
        String config = commonRedisService.get(GetColumnConfigKey(SecurityUtils.getTenantId(), userId));
        if (StringUtils.isBlank(config)) {
            return null;
        }
        JobColumnConfigDTO jobColumnConfigDTO = new JobColumnConfigDTO();
        jobColumnConfigDTO.setCustomConfig(JsonMapperUtil.jsonStringToCustomColumnConfig(config));
        return jobColumnConfigDTO.getCustomConfig().getPageConfig();
    }
}

