package com.altomni.apn.user.domain.permission;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Objects;

/**
 * A leave function
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "permission_privilege")
public class PermissionPrivilege extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @NotNull
    @Size(max = 100)
    @Column(length = 100)
    private String name;

    @Size(max = 5000)
    @Column(name = "api")
    private String api;

    @Column(name = "parent_id")
    private Long parentId;

    @Column(name = "leaf")
    private Boolean isLeaf;

    @Column(name = "level")
    private Integer level;

    @Column(name = "is_public")
    private Boolean isPublic;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PermissionPrivilege role = (PermissionPrivilege) o;
        return id.equals(role.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

}
