package com.altomni.apn.user.repository.user;

import com.altomni.apn.common.domain.user.UserDeliveryJobFunctionRelation;
import com.altomni.apn.common.domain.user.UserLanguageRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;


@Repository
public interface UserLanguageRelationRepository extends JpaRepository<UserLanguageRelation, Long> {
    List<UserLanguageRelation> findAllByUserId(Long userId);

    List<UserLanguageRelation> findAllByUserIdIn(Collection<Long> userIds);
}
