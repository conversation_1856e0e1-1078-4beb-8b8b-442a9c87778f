package com.altomni.apn.user.service.tenant;

import com.altomni.apn.user.web.rest.vm.tenant.TenantPushRuleDTO;

import java.util.List;

public interface TenantPushConfigService {
    void createTenantPushRule(TenantPushRuleDTO tenantPushRuleDTO);

    void updateTenantPushRule(TenantPushRuleDTO tenantPushRuleDTO);

    void deleteTenantPushRule(Long id);

    List<TenantPushRuleDTO> getTenantPushRule();
}
