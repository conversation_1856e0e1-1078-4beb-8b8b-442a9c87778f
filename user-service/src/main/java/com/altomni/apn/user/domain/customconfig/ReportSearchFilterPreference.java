package com.altomni.apn.user.domain.customconfig;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "report_search_filter_preference")
public class ReportSearchFilterPreference extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 3297627398848418727L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "tenant_id")
    private Long tenantId ;

    @Column(name = "report_type")
    private Integer reportType ;

    @Column(name = "title_name")
    private String titleName ;

    @Column(name = "report_search_filter")
    private String reportSearchFilter ;
}
