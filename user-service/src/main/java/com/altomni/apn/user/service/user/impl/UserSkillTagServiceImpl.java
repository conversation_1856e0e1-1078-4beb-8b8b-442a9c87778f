package com.altomni.apn.user.service.user.impl;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.domain.user.UserSkillTag;
import com.altomni.apn.user.repository.user.UserSkillTagRepository;
import com.altomni.apn.user.service.user.UserSkillTagService;
import com.altomni.apn.user.web.rest.vm.SkillTagDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserSkillTagServiceImpl implements UserSkillTagService {

    @Resource
    private UserSkillTagRepository userSkillTagRepository;

    @Override
    public List<SkillTagDTO> getSkillTag() {
        return getSkillTag(SecurityUtils.getUserId());
    }

    private List<SkillTagDTO> getSkillTag(Long userId) {
        return userSkillTagRepository.findAllByUserIdOrderByCreatedDateDescTagAsc(userId).stream().map(s -> {
            SkillTagDTO dto = new SkillTagDTO();
            dto.setId(s.getId());
            dto.setTag(s.getTag());
            return dto;
        }).toList();
    }

    @Override
    @Transactional
    public List<SkillTagDTO> updateSkillTag(List<SkillTagDTO> skillTagDTOList) {
        Long userId = SecurityUtils.getUserId();
        // 1. 获取当前用户所有已存在的技能标签
        List<UserSkillTag> existingTags = userSkillTagRepository.findAllByUserIdOrderByCreatedDateDescTagAsc(userId);

        // 2. 将入参DTO转换为Set，方便后续比较
        Set<String> newTagSet = skillTagDTOList.stream()
                .map(SkillTagDTO::getTag)
                .collect(Collectors.toSet());

        // 3. 找出需要删除的标签（在现有标签中但不在新标签中的）
        List<UserSkillTag> tagsToDelete = existingTags.stream()
                .filter(tag -> !newTagSet.contains(tag.getTag()))
                .collect(Collectors.toList());

        // 4. 找出需要新增的标签（在新标签中但不在现有标签中的）
        Set<String> existingTagSet = existingTags.stream()
                .map(UserSkillTag::getTag)
                .collect(Collectors.toSet());

        List<UserSkillTag> tagsToAdd = skillTagDTOList.stream()
                .filter(dto -> !existingTagSet.contains(dto.getTag()))
                .map(dto -> {
                    UserSkillTag tag = new UserSkillTag();
                    tag.setUserId(userId);
                    tag.setTag(dto.getTag());
                    return tag;
                })
                .collect(Collectors.toList());

        // 5. 执行删除操作
        if (!tagsToDelete.isEmpty()) {
            userSkillTagRepository.deleteAll(tagsToDelete);
        }

        // 6. 执行新增操作
        if (!tagsToAdd.isEmpty()) {
            userSkillTagRepository.saveAll(tagsToAdd);
        }

        // 7. 重新查询并返回更新后的结果
        return getSkillTag(SecurityUtils.getUserId());
    }
}
