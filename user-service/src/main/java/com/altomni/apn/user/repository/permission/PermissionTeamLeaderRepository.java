package com.altomni.apn.user.repository.permission;

import com.altomni.apn.user.domain.permission.PermissionTeamLeader;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import javax.persistence.Tuple;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PermissionTeamLeaderRepository extends JpaRepository<PermissionTeamLeader,Long> {

    boolean existsByTeamIdAndUserId(Long teamId, Long userId);

    Optional<PermissionTeamLeader> findByTeamIdAndUserId(Long teamId, Long userId);

    void deleteAllByTeamIdAndUserIdIn(Long teamId, Set<Long> userIds);

    void deleteAllByUserId(Long userId);

    @Query(value = """
                    SELECT leader
                    FROM PermissionTeamLeader leader
                    LEFT JOIN User u ON leader.userId = u.id
                    WHERE leader.teamId = :teamId
                    AND u.activated = true
                    """)
    List<PermissionTeamLeader> findAllByTeamId(@Param("teamId") Long teamId);

    @Query(value = "select tl.team_id, JSON_ARRAYAGG(tl.user_id) from permission_team_leader tl where tl.tenant_id=:tenantId group by tl.team_id", nativeQuery = true)
    List<Object[]> findAllGroupByTeamId(@Param("tenantId") Long tenantId);

    @Query(value = """
                    WITH RECURSIVE team_hierarchy AS (
                    SELECT 
                        id AS team_id,
                        parent_id,
                        created_by,
                        created_date,
                        last_modified_by,
                        last_modified_date
                    FROM permission_team
                    WHERE id IN (?1)
                    UNION ALL
                    SELECT 
                        t.id AS team_id,
                        t.parent_id,
                        t.created_by,
                        t.created_date,
                        t.last_modified_by,
                        t.last_modified_date
                    FROM permission_team t
                    JOIN team_hierarchy th ON t.id = th.parent_id
                )
                SELECT 
                    th.team_id,
                    th.parent_id AS leader_team_id,
                    COALESCE(pl.user_id, 0) AS leader_id,
                    u.first_name,
                    u.last_name
                FROM team_hierarchy th
                LEFT JOIN permission_team_leader pl ON th.team_id = pl.team_id
                LEFT JOIN user u ON pl.user_id = u.id 
                GROUP BY th.team_id,th.parent_id,leader_id
            """, nativeQuery = true)
    List<Object[]> getParentTeamLeader(Collection<Long> teamId);
}
