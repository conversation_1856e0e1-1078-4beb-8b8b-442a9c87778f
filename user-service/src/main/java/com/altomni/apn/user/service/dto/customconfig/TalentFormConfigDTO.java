package com.altomni.apn.user.service.dto.customconfig;

import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

public class TalentFormConfigDTO implements BaseConfig, Serializable {
    private static final long serialVersionUID = 1L;
    @JsonIgnore
    private Long id;
    private List<CustomFormField> customConfig;

    public void setId(Long id) {
        this.id = id;
    }


    public Long getId() {
        return id;
    }

    public List<CustomFormField> getCustomConfig() {
        return customConfig;
    }

    public void setCustomConfig(List<CustomFormField> customConfig) {
        this.customConfig = customConfig;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JobColumnConfigDTO jobColumnConfigDTO = (JobColumnConfigDTO) o;
        if (jobColumnConfigDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), jobColumnConfigDTO.getId());
    }

    @Override
    public String toString() {
        return "JobColumnConfig{" +
                "id=" + getId() +
                ", customConfig='" + getCustomConfig() +
                "}";
    }
}
