package com.altomni.apn.user.service.dto.customconfig;

import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.CategoryConverter;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Convert;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;
import com.fasterxml.jackson.databind.JsonNode;

public class SystemConfigDTO implements BaseConfig, Serializable {

    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private Long id;

    private JsonNode customConfig;
    @ApiModelProperty(value = "category of the system configuration form", required = true)
    @NotNull(message = "must not be null")
    @Convert(converter = CategoryConverter.class)
    private Category category;

    @ApiModelProperty(value = "subcategory of the system configuration form", required = true)
    @NotNull(message = "must not be null")
    @Convert(converter = ConfigSubcategory.class)
    private ConfigSubcategory subcategory;

    public static SystemConfigDTO fromSystemConfigDefault(SystemConfigDefault entity) {
        SystemConfigDTO dto = new SystemConfigDTO();
        ServiceUtils.myCopyProperties(entity, dto);
        return dto;
    }


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public JsonNode getCustomConfig() {
        return customConfig;
    }

    public void setCustomConfig(JsonNode customConfig) {
        this.customConfig = customConfig;
    }


    public Category getCategory() {
        return category;
    }

    public SystemConfigDTO category(Category category) {
        this.category = category;
        return this;
    }

    public void setCategory(Category category) {
        this.category = category;
    }

    public ConfigSubcategory getSubcategory() {
        return subcategory;
    }

    public void setSubcategory(ConfigSubcategory subcategory) {
        this.subcategory = subcategory;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        SystemConfigDTO systemConfigDefault = (SystemConfigDTO) o;
        if (systemConfigDefault.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), systemConfigDefault.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "SystemConfigDefaultDTO{" +
                "id=" + getId() +
                ", category=" + getCategory() +
                ", deCustomConfig='" + getCustomConfig() + '\'' +
                '}';
    }
}
