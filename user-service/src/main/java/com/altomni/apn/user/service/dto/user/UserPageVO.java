package com.altomni.apn.user.service.dto.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.user.domain.user.UserAccount;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(description = "Vo of User service user")
public class UserPageVO extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "user id")
    private Long id;

    @ApiModelProperty(value = "user name")
    private String name;

    @ApiModelProperty(value = "user name")
    private String firstName;

    @ApiModelProperty(value = "user name")
    private String lastName;

    @ApiModelProperty(value = "user email")
    private String email;

    @JsonIgnore
    private Integer monthlyCredit;

    @JsonIgnore
    private Integer bulkCredit;

    @ApiModelProperty(value = "user available monthly purchase currency")
    private Integer availableMonthlyCredit;

    @ApiModelProperty(value = "user available long-term purchase currency")
    private Integer availableBulkCredit;

    @ApiModelProperty(value = "user status, true: means enabled, false: means frozen")
    private Boolean activated;

    public UserPageVO(Long id, String firstName, String lastName, String email, Integer monthlyCredit, Integer bulkCredit, Boolean activated, Instant createdDate, Instant lastModifiedDate) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.monthlyCredit = monthlyCredit;
        this.bulkCredit = bulkCredit;
        this.activated = activated;
        this.name = CommonUtils.formatFullName(firstName, lastName);
        super.setCreatedDate(createdDate);
        super.setLastModifiedDate(lastModifiedDate);
    }

}