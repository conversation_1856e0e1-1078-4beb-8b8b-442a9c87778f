package com.altomni.apn.user.repository.permission;

import cn.hutool.core.lang.Pair;
import com.altomni.apn.common.domain.permission.PermissionTeamSimple;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.service.dto.permission.PermissionTeamLeaderDTO;
import com.altomni.apn.user.service.dto.permission.PermissionUserTeamDTO;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.Tuple;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PermissionTeamRepository extends JpaRepository<PermissionTeam,Long> {

    @Query("SELECT MAX(code) FROM PermissionTeam WHERE parentId =:parentId and tenantId=:tenantId ")
    String findMaxCodeByParentIdAndTenantId(@Param("parentId") Long parentId, @Param("tenantId") Long tenantId);

    @Query("SELECT MAX(code) FROM PermissionTeam WHERE level = 0 AND tenantId =:tenantId ")
    String findMaxCodeOfTopestLevels(@Param("tenantId") Long tenantId);

    @Modifying
    @Query("UPDATE PermissionTeam SET isLeaf=:isLeaf WHERE id=:id")
    void updateLeafStatusTo(@Param("id") Long id, @Param("isLeaf") Boolean isLeaf);

    boolean existsByParentIdAndTenantIdAndDeleted(Long parentId, Long tenantId, Boolean deleted);

    boolean existsByParentIdAndIdNotAndDeleted(Long parentId, Long teamId, Boolean deleted);

    boolean existsByParentIdAndDeleted(Long parentId, Boolean deleted);

    boolean existsByIdAndTenantIdAndDeleted(Long id, Long tenantId, Boolean deleted);

    boolean existsByTenantIdAndNameAndDeleted(Long tenantId, String name, Boolean deleted);

    Optional<PermissionTeam> findByIdAndTenantIdAndDeleted(Long id, Long tenantId, Boolean deleted);

    @Query(value = "select t1.id from permission_team t1" +
            " where t1.deleted = 0 and t1.code like (select concat(t2.code, '%') from permission_team t2 where t2.id=:teamId and t2.deleted = 0)", nativeQuery = true)
    Set<Long> findTeamIdAndSubTeamIdsByTeamId(@Param("teamId") Long teamId);

    @Modifying
    @Query("update PermissionTeam t set t.deleted=true where t.code like :code")
    void updateDeletedStatusByTeamCodeLike(@Param("code") String code);

    List<PermissionTeam> findByTenantIdAndDeleted(Long tenantId, Boolean deleted, Sort sort);

    @Query(value = """
            select ut.user_id, JSON_ARRAYAGG(JSON_OBJECT('id', t.id,'name', t.name)) as teams 
            from permission_user_team ut
            inner join permission_team t on ut.team_id = t.id
            where t.tenant_id=:tenantId 
            and ut.user_id in :userIds
            and t.deleted = 0
            group by ut.user_id
            """, nativeQuery = true)
    List<Object[]> findTeamsGroupByUserId(@Param("tenantId") Long tenantId, @Param("userIds") List<Long> userIds);

    @Query(value = "select ut.user_id, JSON_ARRAYAGG(JSON_OBJECT('id', t.id,'name', t.name)) as teams " +
            " from permission_user_team ut" +
            " inner join permission_team t on ut.team_id = t.id " +
            " where ut.user_id in :userIds and t.deleted = 0 " +
            " group by ut.user_id", nativeQuery = true)
    List<Object[]> findTeamsGroupByUserIdAndUserIdIn(@Param("userIds") Set<Long> userIds);

    List<PermissionTeam> findAllByTenantIdAndAndParentIdAndDeleted(Long tenantId, Long parentId, Boolean deleted);

    @Query(value = "SELECT t FROM PermissionTeamSimple t" +
            " LEFT JOIN PermissionUserTeam ut on ut.teamId=t.id" +
            " WHERE ut.userId=:userId and t.deleted = false " +
            " order by ut.isPrimary desc ")
    Set<PermissionTeamSimple> findTeamsByUserId(@Param("userId") Long userId);

    @Query(value = " SELECT new com.altomni.apn.user.service.dto.permission.PermissionUserTeamDTO(t.id, t.name, ut.isPrimary, CASE WHEN pl.id IS NULL THEN false ELSE true END) " +
            " FROM PermissionTeamSimple t" +
            " INNER JOIN PermissionUserTeam ut on ut.teamId=t.id " +
            " LEFT JOIN PermissionTeamLeader pl on pl.userId = ut.userId " +
            " WHERE ut.userId=:userId and t.deleted = false  " +
            " order by ut.isPrimary desc ", nativeQuery = false)
    Set<PermissionUserTeamDTO> findTeamVOsByUserId(@Param("userId") Long userId);

    @Modifying
    @Transactional
    @Query(value = """
    update job j set j.pteam_id = ?2 where j.pteam_id = ?1 ;
""", nativeQuery = true)
    void updateJobPTeamIdByTeamId(Long deleteTeamId, Long changeTeamId);

    @Modifying
    @Transactional
    @Query(value = """
    update permission_team set deleted = 1 where id = ?1 ;
    """, nativeQuery = true)
    void deletePermissionTeamByTeamId(Long deleteTeamId);

    @Query(value = """
            WITH RECURSIVE Ancestors AS (
                SELECT * FROM permission_team WHERE id in (?1)
                UNION ALL
                SELECT pt.* FROM permission_team pt
                INNER JOIN Ancestors a ON a.parent_id = pt.id
            )
            SELECT * FROM Ancestors ORDER BY id;
            """, nativeQuery = true)
    List<PermissionTeam> getAncestorTeams(List<Long> teamIdList);


    @Query(value = """
            WITH RECURSIVE Ancestors AS (
                SELECT * FROM permission_team WHERE id in (?1) and deleted = 0
                UNION ALL
                SELECT pt.* FROM permission_team pt
                INNER JOIN Ancestors a ON a.id = pt.parent_id
                where pt.deleted = 0
            )
            SELECT * FROM Ancestors where deleted = 0 ORDER BY id;
            """, nativeQuery = true)
    List<PermissionTeam> getChildTeams(List<Long> teamIdList);

    @Query("SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamLeaderDTO(tl.userId, t.parentId) FROM PermissionTeam t " +
            "LEFT JOIN PermissionTeamLeader tl on tl.teamId=t.parentId " +
            "WHERE t.id =:teamId AND t.parentId !=-1 ")
    List<PermissionTeamLeaderDTO> findParentTeamLeaderByCurrentTeamId(@Param("teamId") Long teamId);


    @Query(value = "select * from permission_team where id in (?1) and deleted = 0;", nativeQuery = true)
    List<PermissionTeam> findActivePermissionTeamByTeamIds(Set<Long> teamIds);


    @Query(value = """
                    WITH RECURSIVE team_hierarchy AS (
                    -- 基本查询：获取每个团队及其父级团队的层级
                    SELECT id, name, parent_id, id AS topTeamId, name AS TopTeamName
                    FROM permission_team
                    WHERE parent_id = -1  -- 获取最上层团队
                    UNION ALL
                    -- 递归查询：继续获取每个团队的父团队，并将最上层团队信息传递下去
                    SELECT pt.id, pt.name, pt.parent_id, th.topTeamId, th.TopTeamName
                    FROM permission_team pt
                    INNER JOIN team_hierarchy th
                    ON pt.parent_id = th.id
                )
                -- 获取所有团队的最上层团队的关系
                SELECT th.id AS teamId, th.TopTeamName
                FROM team_hierarchy th
                WHERE th.id IN (?1);  -- 排除最上层的团队本身
                
            """, nativeQuery = true)
    List<Tuple> getAllTeamHierarchy(Collection<Long> teamId);

    @Query(value = "SELECT put.user_id from permission_user_team put INNER JOIN permission_team pt ON put.team_id = pt.id AND put.is_primary = 1 AND pt.team_category_id IN (15,20) where put.user_id IN ?1 ", nativeQuery = true)
    List<Long> getUserIdFilterByTeamCategory(List<Long> userIds);
}
