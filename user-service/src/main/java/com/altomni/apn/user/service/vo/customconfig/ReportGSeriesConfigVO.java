package com.altomni.apn.user.service.vo.customconfig;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * report G1
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportGSeriesConfigVO implements Serializable {

    private String monthlyRevenueDetailColumnConfig;

    private String quarterlyNewHiresColumnConfig;

    private String quarterlyRenewalsColumnConfig;

    private String quarterlyOffboardingColumnConfig;
}
