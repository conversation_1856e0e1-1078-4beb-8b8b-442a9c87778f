package com.altomni.apn.user.service.permission.impl;

import com.altomni.apn.common.domain.user.UserRole;
import com.altomni.apn.user.repository.permission.PermissionUserRoleRepository;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.altomni.apn.user.service.permission.PermissionRolePrivilegeService;
import com.altomni.apn.user.service.permission.PermissionUserRoleService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserRoleVM;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Transactional
public class PermissionUserRoleServiceImpl implements PermissionUserRoleService {

    @Resource
    private PermissionUserRoleRepository userRoleRepository;

    @Resource
    private PermissionRolePrivilegeService permissionRolePrivilegeService;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

    @Override
    public UserRole create(UserRole userRole) {
        return userRoleRepository.save(userRole);
    }

    @Override
    public Set<Long> update(PermissionUserRoleVM userRoleVM) {
        List<UserRole> userRoles = new ArrayList<>();
        for (Long roleId:userRoleVM.getRoleIds()){
            userRoles.add(new UserRole(null, userRoleVM.getUserId(), roleId));
        }
        //删除前拿到用户所有有效状态角色的privilegeId的集合
        Set<Long> oldPrivilegeIds = userRoleRepository.getAllPrivilegeByUserId(userRoleVM.getUserId());
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            permissionRolePrivilegeService.dealWithE1E2E5UserRolesChanged(userRoleVM.getRoleIds(), userRoleVM.getUserId(),oldPrivilegeIds);
        });
        userRoleRepository.deleteAllByUserId(userRoleVM.getUserId());
        Set<Long> ids = userRoleRepository.saveAll(userRoles).stream().map(userRole -> userRole.getId()).collect(Collectors.toSet());
        cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userRoleVM.getUserId());
        cachePermissionWriteOnly.deleteClientContactDataPermissionCacheByUserId(userRoleVM.getUserId());
        cachePermissionWriteOnly.deleteReportDataPermissionCacheByUserId(userRoleVM.getUserId());
        cachePermissionWriteOnly.deletePrivilegePermissionTreeByUserId(userRoleVM.getUserId());
        cachePermissionWriteOnly.deletePrivilegePermissionSetByUserId(userRoleVM.getUserId());
        cachePermissionWriteOnly.deletePrivilegeByUserId(userRoleVM.getUserId());
        return ids;
    }

    @Override
    public UserRole update(UserRole userRole) {
        return userRoleRepository.save(userRole);
    }

    @Override
    public UserRole get(Integer id) {
        return userRoleRepository.getById(id);
    }

    @Override
    public void delete(Integer id) {
        userRoleRepository.deleteById(id);
    }
}
