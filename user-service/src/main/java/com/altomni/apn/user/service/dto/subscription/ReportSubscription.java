package com.altomni.apn.user.service.dto.subscription;

import com.altomni.apn.common.domain.AbstractPermissionAuditingEntity;
import com.altomni.apn.common.enumeration.reportSubscriptions.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.time.LocalDate;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportSubscription extends AbstractPermissionAuditingEntity {


    private Long id;


    private Long tenantId;


    private String name;


    private PushTimeType pushTimeType;


    private Integer dayOfWeek;


    private Integer dayOfMonth;


    private String sendTime;

    private DataPeriod dataPeriod;


    private LocalDate startDate;


    private LocalDate endDate;


    private ReportType reportType;


    private String dataPermissionJson;

    private ActiveType isActive = ActiveType.ACTIVE;

    private boolean isAutoGenerate;
}
