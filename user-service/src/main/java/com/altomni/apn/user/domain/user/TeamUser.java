package com.altomni.apn.user.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

@ApiModel(description = "Mapping of user and team relation.")
@Entity
@Table(name = "team_user")
public class TeamUser extends AbstractAuditingEntity implements Serializable  {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "user id", required = true)
    @NotNull
    @Column(name = "user_id", nullable = false)
    private Long userId;

    @ApiModelProperty(value = "team id", required = true)
    @NotNull
    @Column(name = "team_id", nullable = false)
    private Long teamId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getTeamId() {
        return teamId;
    }

    public void setTeamId(Long teamId) {
        this.teamId = teamId;
    }

    public TeamUser userId(Long userId) {
        setUserId(userId);
        return this;
    }

    public TeamUser teamId(Long teamId) {
        setTeamId(teamId);
        return this;
    }

    @Override
    public String toString() {
        return "TeamUser{" +
            "id=" + id +
            ", userId=" + userId +
            ", teamId=" + teamId +
            '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        TeamUser teamUser = (TeamUser) o;
        return Objects.equals(userId, teamUser.userId) &&
            Objects.equals(teamId, teamUser.teamId);
    }

    @Override
    public int hashCode() {

        return Objects.hash(userId, teamId);
    }
}
