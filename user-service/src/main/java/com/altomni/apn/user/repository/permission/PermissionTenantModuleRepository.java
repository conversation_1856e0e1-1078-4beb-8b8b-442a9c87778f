package com.altomni.apn.user.repository.permission;

import com.altomni.apn.user.domain.permission.PermissionTenantModule;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTenantInvolvedDataPermissionModuleVM;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PermissionTenantModuleRepository extends JpaRepository<PermissionTenantModule,Long> {

    void deleteAllByTenantId(Long tenentId);

    List<PermissionTenantModule> findAllByTenantId(Long tenantId);

    @Query("select new com.altomni.apn.user.web.rest.vm.permission.PermissionTenantInvolvedDataPermissionModuleVM(m.id, m.name) from PermissionModule m " +
            " left join PermissionTenantModule tm on tm.moduleId=m.id" +
            " where tm.tenantId=:tenantId and tm.involveDataPermission=true")
    List<PermissionTenantInvolvedDataPermissionModuleVM> findInvolvedModulesByTenantId(@Param("tenantId") Long tenantId);

    List<PermissionTenantModule> findAllByTenantIdAndModuleIdIn(Long tenantId, List<Long> moduleIdList);

}
