package com.altomni.apn.user.domain.customconfig;

import com.altomni.apn.common.enumeration.PushConfigType;
import com.altomni.apn.common.enumeration.PushConfigTypeConverter;
import lombok.Data;

import javax.persistence.*;

@Data
@Entity
@Table(name = "tenant_push_rules_type")
public class TenantPushRulesType {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_push_rules_id")
    private Long tenantPushRulesId;

    @Column(name = "type")
    @Convert(converter = PushConfigTypeConverter.class)
    private PushConfigType type;
}
