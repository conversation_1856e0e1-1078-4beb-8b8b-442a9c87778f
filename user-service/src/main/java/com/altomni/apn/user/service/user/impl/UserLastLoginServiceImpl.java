package com.altomni.apn.user.service.user.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.altomni.apn.user.config.env.ApplicationProperties;
import com.altomni.apn.user.domain.user.UserLastLogin;
import com.altomni.apn.user.repository.user.UserLastLoginRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.user.UserLastLoginDTO;
import com.altomni.apn.user.service.user.UserLastLoginService;
import com.altomni.apn.user.web.rest.vm.UserLastLoginVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service("userLastLoginService")
public class UserLastLoginServiceImpl implements UserLastLoginService {

    @Resource
    private UserLastLoginRepository userLastLoginRepository;

    @Resource
    private HttpService httpService;

    @Resource
    private ApplicationProperties applicationProperties;

    @Resource
    private UserRepository userRepository;

    @Async
    @Override
    public void asyncSaveUserLastLoginLog(UserLastLoginVM userLastLoginVm) {
        Optional<UserLastLogin> optional = Optional.ofNullable(userLastLoginRepository.findUserLastLoginByUserId(userLastLoginVm.getUserId()));
        List<UserTimeZoneVO> userTimeZoneVOList = userRepository.findUserTimeZoneVOByIds(CollUtil.newArrayList(userLastLoginVm.getUserId()));
        if (CollUtil.isNotEmpty(userTimeZoneVOList)) {
            UserTimeZoneVO vo = userTimeZoneVOList.get(0);
            if (vo != null && StrUtil.isBlank(vo.getCustomTimezone())) {
                userRepository.updateTimezoneByUserId(userLastLoginVm.getUserId(), userLastLoginVm.getTimeZone());
            }
        }
        optional.ifPresentOrElse(a -> {
            String[] ignoreArrays = new String[]{"id"};
            BeanUtil.copyProperties(userLastLoginVm, a, ignoreArrays);
            saveToDb(a);
        }, () -> {
            UserLastLogin userLastLogin = new UserLastLogin();
            BeanUtil.copyProperties(userLastLoginVm, userLastLogin);
            saveToDb(userLastLogin);
        });
    }

    private void saveToDb(UserLastLogin userLastLogin){
        // Save to MySQL
        userLastLogin = userLastLoginRepository.saveAndFlush(userLastLogin);

        // Send to Statistic Service
        UserLastLoginDTO userLastLoginDTO = new UserLastLoginDTO();
        BeanUtil.copyProperties(userLastLogin, userLastLoginDTO);
        userLastLoginDTO.setSource("APN");
        httpService.asyncPost(applicationProperties.getUserLoginStatisticUrl(), JsonUtil.toJson(userLastLoginDTO));
    }
}
