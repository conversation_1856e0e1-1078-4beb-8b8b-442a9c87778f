package com.altomni.apn.user.service.dto.permission;

import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PermissionTeamMemberDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String firstName;

    private String lastName;

    private Set<Role> roles;

    private Boolean activated;

    private String email;

    private Boolean isPrimaryTeam;

    public PermissionTeamMemberDTO(User user, PermissionUserTeam userTeam){
        this.id = user.getId();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.roles = user.getRoles();
        this.activated = user.isActivated();
        this.email = user.getEmail();
        this.isPrimaryTeam = userTeam.getIsPrimary();
    }
    public PermissionTeamMemberDTO(Long id, String firstName, String lastName){
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
    }
}
