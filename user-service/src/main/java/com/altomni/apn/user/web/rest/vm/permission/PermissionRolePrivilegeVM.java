package com.altomni.apn.user.web.rest.vm.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionRolePrivilegeVM {

    private Long roleId;

    private Set<Long> privilegeIds;

    private Integer dataScope;

    private boolean modifiable = false;

    private Set<Long> teamIds;

}
