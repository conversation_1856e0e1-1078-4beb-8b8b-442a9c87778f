package com.altomni.apn.user.web.rest.vm.user;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class UserSearchVM implements Serializable {

    private static final long serialVersionUID = 1L;

    private String fullName;

    private Long authorities;

    private Long teamId;

    private Boolean activated;

    private String searchEmail;

    private Long id;

    private List<Long> languages;

    private Long levelOfExperience;

}
