package com.altomni.apn.user.web.rest.vm.user;

import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

@Data
public class UserLabelVM implements Serializable {

    private static final long serialVersionUID = 1L;

    private Set<Long> languages;

    private Long levelOfExperience;

    private Set<Integer> deliveryLocations;

    private Set<Long> deliveryProcessIds;

    private Set<Long> deliveryIndustries;

    private Set<Long> deliveryJobFunctions;

}
