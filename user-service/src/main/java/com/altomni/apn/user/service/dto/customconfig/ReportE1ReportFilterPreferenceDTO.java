package com.altomni.apn.user.service.dto.customconfig;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * report E1ReportSearchFilter
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportE1ReportFilterPreferenceDTO implements Serializable {

    private Long id;

    private String titleName;

    private String searchFilter;

    private List<Long> shareUserList;

}
