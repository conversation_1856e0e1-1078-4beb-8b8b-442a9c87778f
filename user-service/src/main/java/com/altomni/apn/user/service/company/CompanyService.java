package com.altomni.apn.user.service.company;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(value = "company-service")
public interface CompanyService {
    @DeleteMapping("/company/api/v3/folders/connect-client")
    ResponseEntity<Void> deleteCustomFolderConnectClient(@RequestParam("createdBy") String createdBy);
}
