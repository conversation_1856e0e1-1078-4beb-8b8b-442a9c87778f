package com.altomni.apn.user.service.calendar;

import com.altomni.apn.common.dto.calendar.CompleteSystemCalendarDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(value = "common-service")
public interface CalendarService {

    @GetMapping("/common/api/v3/sync-or-cancel-calendar-event-to-lark/{syncLark}")
    ResponseEntity<Void> syncOrCancelSyncCalendarEventToLark(@PathVariable("syncLark") Integer syncLark);


    @PostMapping("/common/api/v3/system-calendar/complete")
    ResponseEntity<Void> completeSystemCalendar(@RequestBody CompleteSystemCalendarDTO dto);

}