package com.altomni.apn.user.domain.permission;

import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnumConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "permission_module_privilege_config")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PermissionModulePrivilegeConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "permission_module_privilege_id")
    private Long permissionModulePrivilegeId;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "type")
    @Convert(converter = TenantUserTypeEnumConverter.class)
    private TenantUserTypeEnum type;

    @Column(name = "is_show_permission_button")
    private Boolean isShowPermissionButton;

    @Column(name = "disable")
    private Boolean disable;

    @Column(name = "sort")
    private Integer sort;

    @Column(name = "enable")
    private Boolean enable;

}
