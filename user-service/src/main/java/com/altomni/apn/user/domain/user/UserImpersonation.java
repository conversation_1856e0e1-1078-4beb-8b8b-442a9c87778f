package com.altomni.apn.user.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.Instant;

@ApiModel(description = "Mapping of impersonation login.")
@Data
@Accessors(chain = true)
@Entity
@Table(name = "user_impersonation")
public class UserImpersonation extends AbstractAuditingEntity implements Serializable  {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "grant from user id", required = true)
    @NotNull
    @Column(name = "grant_from_user_id", nullable = false)
    private Long grantFromUserId;

    @ApiModelProperty(value = "original user id", required = true)
    @NotNull
    @Column(name = "grant_to_user_id", nullable = false)
    private Long grantToUserId;

    @ApiModelProperty(value = "The id of the tenant that current user belongs to", required = true)
    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @NotNull
    @Column(name = "effective_start_at", nullable = false)
    private Instant effectiveStartAt;

    @NotNull
    @Column(name = "expire_at", nullable = false)
    private Instant expireAt;

}