package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.common.domain.config.TenantCustomConfig;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;


/**
 * Spring Data R2DBC repository for the TenantCustomConfig entity.
 */

@Repository
public interface TenantCustomConfigRepository extends JpaRepository<TenantCustomConfig, Long> {

    Optional<TenantCustomConfig> findFirstByConfigCodeAndTenantId(TenantConfigCode configCode, Long tenantId);

}

