package com.altomni.apn.user.repository.permission;

import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.user.domain.permission.PermissionExtraUserTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PermissionExtraUserTeamRepository extends JpaRepository<PermissionExtraUserTeam,Long> {
    @Query(nativeQuery = true,
            value = "select team_id from permission_extra_user_team rd " +
                    " where rd.user_id=:userId")
    Set<Long> getAllTeamIdsFromExtraUserByUserId(@Param("userId") Long userId);

    @Query(nativeQuery = true,
            value = "select team_id from permission_extra_user_team rd " +
                    " where rd.user_id=:userId and rd.writable=1")
    Set<Long> getWritableTeamIdsFromExtraUserByUserId(@Param("userId") Long userId);

    void deleteAllByUserId(Long userId);

    List<PermissionExtraUserTeam> findAllByUserIdAndModule(Long userId, Module module);

    List<PermissionExtraUserTeam> findAllByUserIdIn(List<Long> userId);
}
