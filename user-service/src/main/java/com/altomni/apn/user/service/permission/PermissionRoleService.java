package com.altomni.apn.user.service.permission;


import com.altomni.apn.user.service.dto.permission.PermissionRoleDTO;

import java.util.List;

public interface PermissionRoleService {

    PermissionRoleDTO create(PermissionRoleDTO role);

    PermissionRoleDTO update(PermissionRoleDTO role);

    PermissionRoleDTO get(Long id);

    void delete(Long id);

    List<PermissionRoleDTO> findByTenantId();

    Integer updateActiveStatus(Long roleId, Boolean isActive);

    Integer countActiveUsersByRoleId(Long roleId);

    List<PermissionRoleDTO> findRolesByUserId(Long userId);
}
