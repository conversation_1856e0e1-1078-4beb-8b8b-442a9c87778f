package com.altomni.apn.user.web.rest.permission;

import com.altomni.apn.user.service.dto.permission.PermissionModuleDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTableDTO;
import com.altomni.apn.user.service.permission.PermissionModuleService;
import com.altomni.apn.user.service.permission.PermissionTableService;
import com.altomni.apn.user.service.permission.PermissionTenantModuleService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionModuleTablesVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * REST controller for managing permissions.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/modules")
public class PermissionModuleResource {

    @Resource
    private PermissionModuleService permissionModuleService;

    @Resource
    private PermissionTableService permissionTableService;

    @Resource
    private PermissionTenantModuleService permissionTenantModuleService;

    @PostMapping("")
    public ResponseEntity<PermissionModuleDTO> createModule(@RequestBody PermissionModuleDTO permissionModuleDTO) {
        log.debug("REST request to create a module, {}", permissionModuleDTO);
        return ResponseEntity.ok(permissionModuleService.create(permissionModuleDTO));
    }

    @PutMapping("")
    public ResponseEntity<PermissionModuleDTO> updateModule(@RequestBody PermissionModuleDTO permissionModuleDTO) {
        log.debug("REST request to update a module, {}", permissionModuleDTO);
        return ResponseEntity.ok(permissionModuleService.update(permissionModuleDTO));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> updateModule(@PathVariable("id") Long id) {
        log.debug("REST request to delete a module, {}", id);
        permissionModuleService.delete(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("")
    public ResponseEntity<List<PermissionModuleDTO>> getModules() {
        log.debug("REST request to getModules");
        return ResponseEntity.ok(permissionModuleService.findAll());
    }

    @PostMapping("/set-module-tables")
    public ResponseEntity<Void> setModuleTables(@RequestBody PermissionModuleTablesVM permissionModuleTablesVM) {
        log.debug("REST request to set module tables, {}", permissionModuleTablesVM);
        permissionModuleService.setModuleTables(permissionModuleTablesVM);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{moduleId}/tables")
    public ResponseEntity<List<PermissionTableDTO>> getModuleTables(@PathVariable("moduleId") Long moduleId) {
        log.debug("REST request to get Tables by moduleId, module id: {}", moduleId);
        return ResponseEntity.ok(permissionTableService.findByModuleId(moduleId));
    }
}
