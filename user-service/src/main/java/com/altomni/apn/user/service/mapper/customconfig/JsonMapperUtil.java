package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.user.service.dto.customconfig.CustomColumnField;
import com.altomni.apn.user.service.dto.customconfig.CustomFormField;
import com.altomni.apn.user.service.dto.customconfig.UserCustomConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JavaType;
import org.mapstruct.Named;

import java.util.List;


public class JsonMapperUtil {

    @Named("jsonNodeToString")
    public static String jsonNodeToString(JsonNode jsonNode) {
        if (jsonNode == null) {
            return null;
        }
        return jsonNode.toString();
    }

    @Named("stringToJsonNode")
    public static JsonNode stringToJsonNode(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readTree(jsonString);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting String to JsonNode", e);
        }
    }
    @Named("customFieldListToJsonString")
    public static <T> String customFieldListToJsonString(List<T> customFields) {
        if (customFields == null) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(customFields);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting List<T> to JSON string", e);
        }
    }

    @Named("jsonStringToCustomFieldList")
    public static <T> List<T> jsonStringToCustomFieldList(String jsonString, Class<?> customFieldClass) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, customFieldClass);
            return objectMapper.readValue(jsonString, javaType);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting JSON string to List<T>", e);
        }
    }

    @Named("customFormFieldListToJsonString")
    public static String customFormFieldListToJsonString(List<CustomFormField> customFields) {
        if (customFields == null) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(customFields);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting List<CustomField> to JSON string", e);
        }
    }

    @Named("jsonStringToCustomFormFieldList")
    public static List<CustomFormField> jsonStringToCustomFormFieldList(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, CustomFormField.class);
            return objectMapper.readValue(jsonString, new TypeReference<List<CustomFormField>>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting JSON string to List<CustomField>", e);
        }
    }

    @Named("customColumnFieldListToJsonString")
    public static String customColumnFieldListToJsonString(List<CustomColumnField> customFields) {
        if (customFields == null) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            return objectMapper.writeValueAsString(customFields);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting List<CustomField> to JSON string", e);
        }
    }

    @Named("jsonStringToCustomColumnFieldList")
    public static List<CustomColumnField> jsonStringToCustomColumnFieldList(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JavaType javaType = objectMapper.getTypeFactory().constructCollectionType(List.class, CustomColumnField.class);
            return objectMapper.readValue(jsonString, new TypeReference<List<CustomColumnField>>() {
            });
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting JSON string to List<CustomField>", e);
        }
    }

    @Named("jsonStringToCustomColumnConfig")
    public static UserCustomConfig jsonStringToCustomColumnConfig(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonString, UserCustomConfig.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting JSON string to userCustomConfig", e);
        }
    }

    @Named("customColumnConfigToJsonString")
    public static String customColumnConfigToJsonString(UserCustomConfig userCustomConfig) {
        if (userCustomConfig == null) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            return objectMapper.writeValueAsString(userCustomConfig);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting userCustomConfig to JSON string", e);
        }
    }

    @Named("jsonNodeToCustomColumnFieldList")
    public static List<CustomColumnField> jsonNodeToCustomColumnFieldList(JsonNode jsonNode) {
        if (jsonNode == null || !jsonNode.isArray()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readValue(jsonNode.toString(), new TypeReference<List<CustomColumnField>>() {});
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert JsonNode to List<CustomColumnField>", e);
        }
    }

}