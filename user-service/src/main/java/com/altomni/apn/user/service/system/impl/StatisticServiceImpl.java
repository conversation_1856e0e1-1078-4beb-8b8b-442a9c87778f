package com.altomni.apn.user.service.system.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.constants.StatisticConstants;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.user.config.env.StatisticProperties;
import com.altomni.apn.user.repository.permission.PermissionRoleRepository;
import com.altomni.apn.user.repository.permission.PermissionTeamRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.system.TalentDetailRetrievingCountDTO;
import com.altomni.apn.user.service.dto.system.TalentDetailRetrievingNotification;
import com.altomni.apn.user.service.dto.user.UserTeamDTO;
import com.altomni.apn.user.service.system.StatisticService;
import com.altomni.apn.user.web.rest.vm.system.OnlineUserVM;
import com.altomni.apn.user.web.rest.vm.system.StatisticOnlineUser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class StatisticServiceImpl implements StatisticService {

    @Value("${application.statistic.url}")
    private String statisticUrl;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private PermissionRoleRepository roleRepository;

    @Resource
    private PermissionTeamRepository permissionTeamRepository;

    @Resource
    private HttpService httpService;

    @Resource
    private StatisticProperties statisticProperties;

    @Resource
    private ObjectMapper objectMapper;


    private Stream<String> getValidUsersByTime(List<String> keys, Integer seconds){
        return keys.stream().filter(key->{
            final String value = commonRedisService.get(key);
            if (StringUtils.isEmpty(value)){
                return Boolean.FALSE;
            }
            final String time = value.split(";")[0];
            LocalDateTime utcTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z));
            return utcTime.plusSeconds(seconds).isAfter(LocalDateTime.now(ZoneOffset.UTC));
        });
    }

    @Override
    public Long countAllOnlineUsersByTenantIdAndSecondsIn(Long tenantId, Integer seconds) {
        return this.getValidUsersByTime(commonRedisService.getAllOnlineUsersByTenantId(tenantId), seconds).count();
    }

    @Override
    public Long countAllOnlineUsersAndSecondsIn(Integer seconds){
        return this.getValidUsersByTime(commonRedisService.getAllOnlineUsers(), seconds).count();
    }

    @Override
    public List<OnlineUserVM> getOnlineUsersByTenantIdAndSecondsIn(Long tenantId, Integer seconds) throws ExecutionException, InterruptedException {
        final List<String> onlineUserKeys = commonRedisService.getAllOnlineUsersByTenantId(tenantId);
        List<OnlineUserVM> onlineUserVMList = new ArrayList();
        Map<String, String> validUserMap = new HashMap<>();
        Set<Long> validUserIds = new HashSet();

        // extract valid users
        for (String onlineUserKey : onlineUserKeys) {
            final String value = commonRedisService.get(onlineUserKey);
            if (StringUtils.isEmpty(value)){
                continue;
            }
            final String[] split = value.split(";");
            final String time = split[0];
            final String timezone = split[1];
            LocalDateTime utcTime = LocalDateTime.parse(time, DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_T_HH_MM_SS_Z));
            if (utcTime.plusSeconds(seconds).isAfter(LocalDateTime.now(ZoneOffset.UTC))){
                validUserMap.put(onlineUserKey, value);
                validUserIds.add(Long.valueOf(onlineUserKey.split(":")[3]));
            }
        }
        // get user details by user id list
        final Map<Long, OnlineUserVM> userIdToUser = this.getUsersByIds(validUserIds);
        // generate result
        validUserMap.forEach((k, v) -> {
            final String[] keySplit = k.split(":");
            final Long userId = Long.valueOf(keySplit[3]);
            final String ip = keySplit[4];
            final String[] valueSplit = v.split(";");
            final String time = valueSplit[0];
            final String timezone = valueSplit[1];
            final OnlineUserVM onlineUserVM = userIdToUser.get(userId);
            onlineUserVMList.add(onlineUserVM
                    .setIp(ip)
                    .setTimezone(timezone)
                    .setLatestActiveTime(time));
        });
        return onlineUserVMList;
    }

    private Map<Long, OnlineUserVM> getUsersByIds(Set<Long> validUserIds) throws ExecutionException, InterruptedException {
        CompletableFuture<List<OnlineUserVM>> userCompletableFuture = CompletableFuture.supplyAsync(() -> userRepository.findAllForOnlineUsersByUserIdIn(validUserIds));
        CompletableFuture<Map<Long, JSONArray>> userRolesCompletableFuture = CompletableFuture.supplyAsync(() -> roleRepository.findRolesGroupByUserIdAndUserIdIn(validUserIds).stream().collect(Collectors.toMap(userRoles -> Long.valueOf(userRoles[0].toString()), userRoles -> JSONArray.parseArray(userRoles[1].toString()))));
        CompletableFuture<Map<Long, JSONArray>> userTeamsCompletableFuture = CompletableFuture.supplyAsync(()-> permissionTeamRepository.findTeamsGroupByUserIdAndUserIdIn(validUserIds).stream().collect(Collectors.toMap(userTeams -> Long.valueOf(userTeams[0].toString()), userTeams -> JSONArray.parseArray(userTeams[1].toString()))));

        List<OnlineUserVM> result = userCompletableFuture.get();
        Map<Long, JSONArray> userToRoles = userRolesCompletableFuture.get();
        Map<Long, JSONArray> userToTeams = userTeamsCompletableFuture.get();

        return result.stream().map(onlineUserVM->{
            onlineUserVM.setRoles(userToRoles.getOrDefault(onlineUserVM.getId(), new JSONArray()));
            onlineUserVM.setSecondaryTeams(userToTeams.getOrDefault(onlineUserVM.getId(), new JSONArray()).stream().filter(t -> {
                final Long teamId = ((JSONObject) t).getLong("id");
                return Objects.isNull(onlineUserVM.getPrimaryTeam()) || !teamId.equals(onlineUserVM.getPrimaryTeam().getLong("id"));
            }).map(o -> (JSONObject)o).collect(Collectors.toList()));
            return onlineUserVM;
        }).collect(Collectors.toMap(u -> u.getId(), u -> u));
    }

//    @Override
//    public List<OnlineTenantVM> getAllOnlineUsersSecondsInAndGroupByTenant(Integer seconds) {
//        final ConcurrentMap<String, Long> tenantToCount = this.getValidUsersByTime(redisService.getAllOnlineUsers(), seconds)
//                .collect(Collectors.groupingByConcurrent(k -> k.split(":")[2], Collectors.counting()));
//        return tenantRepository.findAllSimpleTenant().stream()
//                .map(t -> t.setOnlineUserCount(tenantToCount.getOrDefault(t.getTenantId().toString(), 0L)))
//                .sorted((t1, t2) -> t2.getOnlineUserCount().compareTo(t1.getOnlineUserCount())).collect(Collectors.toList());
//    }


    /**
     * To check online users and send to statistic service
     */
    @Scheduled(cron = "59 9,19,29,39,49,59 * * * ?")
    public void checkOnlineUsers(){
        String startTime = DateUtil.getUtcTimeByOffset(-10 * 60 + 1);
        String currentUtcTime = DateUtil.currentUtcTime();
        String currentUtcDate = currentUtcTime.substring(0, 10);
        String currentUtcDateDigits = currentUtcDate.replace("-", "");
        List<StatisticOnlineUser> onlineUserList = new ArrayList<>();
        List<String> onlineUsers = commonRedisService.getAllOnlineUsers();
        for (String onlineUser : onlineUsers) {
            String[] split = onlineUser.split(":");
            Long tenantId = Long.parseLong(split[2]);
            Long userId = Long.parseLong(split[3]);
            StatisticOnlineUser user = new StatisticOnlineUser().setId(tenantId + "-" + userId + "-" + currentUtcDateDigits)
                    .setTenantId(tenantId)
                    .setUserId(userId)
                    .setDate(currentUtcDate)
                    .setDuration(10)
                    .setOnlineDetails(List.of(new StatisticOnlineUser.OnlineDetail(startTime, currentUtcTime)));
            onlineUserList.add(user);
        }
        httpService.asyncPost(statisticUrl + StatisticConstants.ONLINE_USER_APN_URL, JsonUtil.toJson(onlineUserList));
    }

    private void buildAndSendMessage(String larkWebhookKey, String larkWebhookUrl, String tenantId, Integer threshold, List<String> talents){
        cn.hutool.json.JSONObject msg = new cn.hutool.json.JSONObject();
        // set title
        msg.put("title", String.format("WARNING (Tenant: %s)", tenantId));
        cn.hutool.json.JSONArray content = new cn.hutool.json.JSONArray();
        //set sub title
        cn.hutool.json.JSONArray subTitle = new cn.hutool.json.JSONArray();
        cn.hutool.json.JSONObject subContent = new cn.hutool.json.JSONObject();
        subContent.put("tag", "text");
        subContent.put("text", String.format("Talent detail view count (>= %d) over the past 24 hours!", threshold));
        subTitle.add(subContent);
        content.add(subTitle);
        // set pre line
        cn.hutool.json.JSONArray preLine = new cn.hutool.json.JSONArray();
        cn.hutool.json.JSONObject preLineContent = new cn.hutool.json.JSONObject();
        preLineContent.put("tag", "text");
        preLineContent.put("text", "------------------------------------------------------------------------");
        preLine.add(preLineContent);
        content.add(preLine);
        // set content details
        for (String t : talents) {
            cn.hutool.json.JSONArray row = new cn.hutool.json.JSONArray();
            cn.hutool.json.JSONObject detail = new cn.hutool.json.JSONObject();
            detail.put("tag", "text");
            detail.put("text", t);
            row.add(detail);
            content.add(row);
        }
        msg.put("content", content);
        NotificationUtils.sendRichTextMessage(larkWebhookKey, larkWebhookUrl, msg);
    }

    @Scheduled(cron = "0 0 6 * * *", zone = "America/Los_Angeles")
    public void sendTalentDetailRetrievingNotification() throws IOException {
        log.info("sendTalentDetailRetrievingNotification");
        List<TalentDetailRetrievingNotification> settings = objectMapper.readValue(statisticProperties.getTalentViewNotification(), new TypeReference<List<TalentDetailRetrievingNotification>>() {});
        Map<Long, TalentDetailRetrievingNotification> tenantToSetting = settings.stream().collect(Collectors.toMap(TalentDetailRetrievingNotification::getTenantId, s -> s));
        Instant twentyFourHoursAgo = Instant.now().minusSeconds(24 * 60 * 60);
        HttpResponse post = httpService.post(statisticUrl + StatisticConstants.TALENT_VIEW_COUNT_URL, JsonUtil.toJson(new TalentDetailRetrievingCountDTO(tenantToSetting.keySet(), twentyFourHoursAgo)));
        Map<String, Integer> result = objectMapper.readValue(post.getBody(), Map.class);
        Set<String> emails = result.keySet().stream().map(s -> s.split("-")[1]).collect(Collectors.toSet());
        Map<String, String> userToTeamMap = userRepository.findUserTeamByEmails(emails).stream().collect(Collectors.toMap(UserTeamDTO::getEmail, UserTeamDTO::getTeam));
        // 先按照tenant排序，再按照访问次数排序
        List<Map.Entry<String, Integer>> sorted = result.entrySet()
                .stream()
                .filter(entry -> entry.getValue().compareTo(tenantToSetting.get(Long.parseLong(entry.getKey().split("-")[0])).getThreshold()) >= 0)
                .sorted(
                        Comparator.comparing(entry -> entry.getKey().split("-")[0] + "-" + String.format("%05d", entry.getValue()))
                )
                .toList();

        List<String> talents = new ArrayList<>();
        String tempTenantId = null;
        for (int i = sorted.size() - 1; i > -1; i--) {
            Map.Entry<String, Integer> entry = sorted.get(i);
            String[] tenantIdAndUserEmail = entry.getKey().split("-");
            String tenantId = tenantIdAndUserEmail[0];
            String userEmail = tenantIdAndUserEmail[1];
            if (StringUtils.isBlank(tempTenantId)){
                tempTenantId = tenantId;
            }
            if (!tempTenantId.equals(tenantId)){
                TalentDetailRetrievingNotification talentDetailRetrievingNotification = tenantToSetting.get(Long.parseLong(tempTenantId));
                this.buildAndSendMessage(talentDetailRetrievingNotification.getLarkWebhookKey(), talentDetailRetrievingNotification.getLarkWebhookUrl(),
                        tempTenantId, talentDetailRetrievingNotification.getThreshold(), talents);
                talents.clear();
                tempTenantId = null;
            }
            talents.add(String.format("%s (%s) -> %d", userEmail, userToTeamMap.getOrDefault(userEmail, "N/A"), entry.getValue()));
        }
        if (StringUtils.isNotBlank(tempTenantId)){
            TalentDetailRetrievingNotification talentDetailRetrievingNotification = tenantToSetting.get(Long.parseLong(tempTenantId));
            this.buildAndSendMessage(talentDetailRetrievingNotification.getLarkWebhookKey(), talentDetailRetrievingNotification.getLarkWebhookUrl(),
                    tempTenantId, talentDetailRetrievingNotification.getThreshold(), talents);
        }
    }
}
