package com.altomni.apn.user.service.dto.permission;

import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.user.domain.permission.PermissionTeamLeader;
import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import com.altomni.apn.user.web.rest.vm.user.DeliveryStatVM;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Set;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PermissionTeamMemberMemberDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String firstName;

    private String lastName;

    private String userName;

    private Boolean activated;

    private String email;

    private String jobTitle;

    private String userTeam;

    private Long teamId;

    private Set<String> teamLeader;

    private List<Long> languages;

    @JsonIgnore
    private String languagesSort;

    private Long levelOfExperience;

    @JsonIgnore
    private String levelOfExperienceSort;

    private Instant activateDate;

    private List<DeliveryStatVM> countryDeliveryCounts;

    private List<DeliveryStatVM> processDeliveryCounts;

    public PermissionTeamMemberMemberDetailDTO(User user, PermissionUserTeam userTeam, PermissionTeamLeader leader){
        this.id = user.getId();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.activated = user.isActivated();
        this.email = user.getEmail();
        this.levelOfExperience = user.getEnumLevelOfExperienceId();
        this.jobTitle = user.getJobTitle();
        this.activateDate = user.getCreatedDate();
        if (userTeam!=null){
            this.teamId = userTeam.getTeamId();
        }

    }
    public PermissionTeamMemberMemberDetailDTO(Long id,
                                               String firstName,
                                               String lastName,
                                               String email,
                                               String jobTitle,
                                               Long levelOfExperience,
                                               Instant createdDate,
                                               Boolean activated,
                                               PermissionUserTeam userTeam){
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.levelOfExperience = levelOfExperience;
        this.jobTitle = jobTitle;
        this.activateDate = createdDate;
        this.activated = activated;
        if (userTeam!=null){
            this.teamId = userTeam.getTeamId();
        }
    }

}
