package com.altomni.apn.user.service.dto.permission;

import com.alibaba.fastjson.JSONArray;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

/**
 * A leave function
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class PermissionPrivilegeUpdateDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    private JSONArray apiList;

    private Boolean isPublic;

    private List<PermissionPrivilegeUpdateDTO> children;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PermissionPrivilegeUpdateDTO role = (PermissionPrivilegeUpdateDTO) o;
        return id.equals(role.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

}
