package com.altomni.apn.user.web.rest.permission;

import com.altomni.apn.common.config.PrivilegeName;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.altomni.apn.user.service.permission.PermissionTenantModuleService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTenantModuleVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * REST controller for managing permissions.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/tenants")
public class PermissionTenantResource {

    @Resource
    private PermissionTenantModuleService permissionTenantModuleService;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

//    @GetMapping("")
//    public ResponseEntity<List<Tenant>> getTenants(){
//        log.info("({}) REST request to get tenants", SecurityUtils.getCurrentUserLogin());
//        return ResponseEntity.ok(tenantRepository.findAll());
//    }

    @PrivilegeName(value = "Set Tenant Modules")
    @PostMapping("/set-tenant-modules")
    public ResponseEntity<Void> setTenantModules(@RequestBody PermissionTenantModuleVM permissionTenantModuleVM){
        log.info("({}) REST request to set tenant modules", SecurityUtils.getCurrentUserLogin());
        permissionTenantModuleService.updateAll(permissionTenantModuleVM);
        cachePermissionWriteOnly.deletePermissionRulesByTenantId(permissionTenantModuleVM.getTenantId());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{tenantId}/modules")
    public ResponseEntity<PermissionTenantModuleVM> getTenantModules(@PathVariable("tenantId") Long tenantId){
        log.info("({}) REST request to get tenant modules by tenantId {}", tenantId);
        return ResponseEntity.ok(permissionTenantModuleService.findByTenantId(tenantId));
    }

}
