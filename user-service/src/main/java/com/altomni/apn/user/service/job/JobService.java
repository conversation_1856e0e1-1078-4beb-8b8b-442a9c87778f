package com.altomni.apn.user.service.job;


import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.user.service.dto.job.ColumnPreferenceDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@Component
@FeignClient(value = "job-service")
public interface JobService {
    @GetMapping("/job/api/v3/jobs/{userId}}/preference/{module}")
    ResponseEntity<ColumnPreferenceDTO> findUserPreferenceByUserId(@PathVariable("userId") Long userId, @PathVariable("module") ModuleType module);

    @GetMapping("/job/api/v3/jobs/search-job-by-pteamId/{teamId}")
    ResponseEntity<Integer> searchJobsCountByPTeamId(@PathVariable("teamId") Long teamId);

}
