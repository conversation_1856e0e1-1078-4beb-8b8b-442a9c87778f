package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;
@Converter
public class ConfigSubcategoryConverter extends AbstractAttributeConverter<ConfigSubcategory, Short> {
    public ConfigSubcategoryConverter() {
        super(ConfigSubcategory::toDbValue, ConfigSubcategory::fromDbValue);
        }
}
