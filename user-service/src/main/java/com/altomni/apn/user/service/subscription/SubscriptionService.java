package com.altomni.apn.user.service.subscription;

import com.altomni.apn.common.subscription.DeleteSubscriptionBatchDTO;
import com.altomni.apn.user.service.dto.subscription.ReportSubscriptionDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@Component
@FeignClient(value = "common-service")
public interface SubscriptionService {

    @PostMapping("/common/api/v3/kpi/subscription/batch")
    ResponseEntity<Void> createReportSubscription(@RequestBody List<ReportSubscriptionDTO> reportSubscriptionDTOList);

    @PostMapping("/common/api/v3/kpi/subscription/delete/batch")
    ResponseEntity<Void> deleteReportSubscriptionBatch(@RequestBody DeleteSubscriptionBatchDTO deleteSubscriptionBatchDTO);

}
