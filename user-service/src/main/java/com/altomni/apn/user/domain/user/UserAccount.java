package com.altomni.apn.user.domain.user;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.user.domain.enumeration.*;
import com.fasterxml.jackson.annotation.JsonIgnore;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A UserAccount.
 */
@Entity
@Table(name = "user_account")
public class UserAccount extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 3524846326589097872L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Convert(converter = UserAccountTypeConverter.class)
    @Column(name = "account_type")
    private UserAccountType accountType;

    @Convert(converter = CreditEffectConverter.class)
    @Column(name = "credit_effect_type")
    private CreditEffectType creditEffectType;

    @Column(name = "amount")
    private Integer monthlyAmount;

    @Column(name = "bulk_credit")
    private Integer bulkCredit=0;

    @Column(name = "effect_credit")
    private Integer effectCredit;

    @Column(name = "frozen_amount")
    private Integer frozenAmount;

    @Column(name = "total_amount")
    private Integer totalAmount;

    @Convert(converter = AccountStatusConverter.class)
    @Column(name = "account_status")
    private AccountStatus accountStatus;

    @Column(name = "expire_date")
    private String expireDate;

    @Version
    @Column(name = "version")
    @JsonIgnore
    private Integer version;

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public UserAccount userId(Long userId) {
        this.userId = userId;
        return this;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public UserAccountType getAccountType() {
        return accountType;
    }

    public UserAccount accountType(UserAccountType accountType) {
        this.accountType = accountType;
        return this;
    }

    public void setAccountType(UserAccountType accountType) {
        this.accountType = accountType;
    }

    public Integer getMonthlyAmount() {
        return monthlyAmount;
    }

    public UserAccount amount(Integer amount) {
        this.monthlyAmount = amount;
        return this;
    }

    public void setMonthlyAmount(Integer monthlyAmount) {
        this.monthlyAmount = monthlyAmount;
    }

    public Integer getFrozenAmount() {
        return frozenAmount;
    }

    public UserAccount frozenAmount(Integer frozenAmount) {
        this.frozenAmount = frozenAmount;
        return this;
    }

    public void setFrozenAmount(Integer frozenAmount) {
        this.frozenAmount = frozenAmount;
    }

    public Integer getTotalAmount() {
        return totalAmount;
    }

    public UserAccount totalAmount(Integer totalAmount) {
        this.totalAmount = totalAmount;
        return this;
    }

    public void setTotalAmount(Integer totalAmount) {
        this.totalAmount = totalAmount;
    }

    public AccountStatus getAccountStatus() {
        return accountStatus;
    }

    public UserAccount accountStatus(AccountStatus accountStatus) {
        this.accountStatus = accountStatus;
        return this;
    }

    public void setAccountStatus(AccountStatus accountStatus) {
        this.accountStatus = accountStatus;
    }

    public String getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(String expireDate) {
        this.expireDate = expireDate;
    }

    public UserAccount expireDate(String expireDate) {
        this.expireDate = expireDate;
        return this;
    }

    public Integer getVersion() {
        return version;
    }

    public UserAccount version(Integer version) {
        this.version = version;
        return this;
    }

    public Integer getBulkCredit() {
        return bulkCredit;
    }

    public void setBulkCredit(Integer bulkAmount) {
        this.bulkCredit = bulkAmount;
    }

    public UserAccount bulkAmount(Integer bulkAmount) {
        this.bulkCredit = bulkAmount;
        return this;
    }

    public UserAccount effectCredit(Integer effectCredit) {
        this.effectCredit = effectCredit;
        return this;
    }

    public UserAccount creditEffectType(CreditEffectType type) {
        this.creditEffectType = type;
        return this;
    }


    public void setVersion(Integer version) {
        this.version = version;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        UserAccount userAccount = (UserAccount) o;
        if (userAccount.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), userAccount.getId());
    }

    public CreditEffectType getCreditEffectType() {
        return creditEffectType;
    }

    public void setCreditEffectType(CreditEffectType creditEffectType) {
        this.creditEffectType = creditEffectType;
    }

    public Integer getEffectCredit() {
        return effectCredit;
    }

    public void setEffectCredit(Integer effectCredit) {
        this.effectCredit = effectCredit;
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "UserAccount{" +
                "id=" + id +
                ", userId=" + userId +
                ", amount=" + monthlyAmount +
                ", frozenAmount=" + frozenAmount +
                ", totalAmount=" + totalAmount +
                ", accountStatus=" + accountStatus +
                ", expireDate=" + expireDate +
                ", version=" + version +
                '}';
    }
}
