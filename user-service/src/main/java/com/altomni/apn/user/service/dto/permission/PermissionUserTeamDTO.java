package com.altomni.apn.user.service.dto.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class PermissionUserTeamDTO {
    private Long id;

    private String name;

    private Boolean isPrimaryTeam;

    private Boolean isTeamLeader;
}
