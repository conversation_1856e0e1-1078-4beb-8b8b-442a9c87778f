package com.altomni.apn.user.service.mapper.permission;
import com.altomni.apn.user.domain.permission.PermissionModule;
import com.altomni.apn.user.service.dto.permission.PermissionModuleDTO;
import com.altomni.apn.user.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity Tag and its DTO TagDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface ModuleMapper extends EntityMapper<PermissionModuleDTO, PermissionModule> {

    default PermissionModule fromId(Long id) {
        if (id == null) {
            return null;
        }
        PermissionModule permissionModule = new PermissionModule();
        permissionModule.setId(id);
        return permissionModule;
    }
}
