package com.altomni.apn.user.service.dto.user;

import lombok.Data;

import java.io.Serializable;
import java.time.Instant;

@Data
public class UserLastLoginDTO implements Serializable {

    private Long id;

    private Long userId;

    private String timeZone;

    private String location;

    private String ip;

    private String mac;

    private Instant lastLoginTime;

    private String userAgent;

    private String source;

}
