package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.user.domain.customconfig.JobFormConfig;
import com.altomni.apn.user.domain.customconfig.TalentFormConfig;
import com.altomni.apn.user.service.dto.customconfig.JobFormConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.TalentFormConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring", uses = JsonMapperUtil.class)
public interface TalentFormConfigMapper {

    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "customFormFieldListToJsonString")
    TalentFormConfig toEntity(TalentFormConfigDTO dto);

    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "jsonStringToCustomFormFieldList")
    TalentFormConfigDTO toDto(TalentFormConfig entity);

    default TalentFormConfig fromId(Long id) {
        if (id == null) {
            return null;
        }
        TalentFormConfig formConfig = new TalentFormConfig();
        formConfig.setId(id);
        return formConfig;
    }

}
