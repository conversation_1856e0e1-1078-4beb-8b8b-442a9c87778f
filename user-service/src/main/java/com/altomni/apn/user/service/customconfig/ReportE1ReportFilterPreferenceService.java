package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.service.dto.customconfig.ReportE1ReportFilterPreferenceDTO;
import com.altomni.apn.user.service.dto.customconfig.ReportGSeriesColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.ReportE1ReportFilterPreferenceVO;
import com.altomni.apn.user.service.vo.customconfig.ReportGSeriesConfigVO;

import java.util.List;

public interface ReportE1ReportFilterPreferenceService {

    List<ReportE1ReportFilterPreferenceVO> searchE1ReportFilterPreferenceByUserId(Long userId);

    void updateE1ReportFilterPreferenceByUserId(Long userId, ReportE1ReportFilterPreferenceDTO dto);

    void addE1ReportFilterPreferenceByUserId(Long userId, ReportE1ReportFilterPreferenceDTO dto);

    void deleteReportFilterPreferences(Long id);

    ReportE1ReportFilterPreferenceVO getReportFilterPreferences(Long id);

}
