package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.repository.customconfig.SystemConfigDefaultRepository;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.customconfig.InvoicingColumnConfigService;
import com.altomni.apn.user.service.customconfig.ReportGSeriesColumnConfigService;
import com.altomni.apn.user.service.dto.customconfig.InvoicingColumnConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.ReportGSeriesColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.InvoicingColumnConfigVO;
import com.altomni.apn.user.service.vo.customconfig.ReportGSeriesConfigVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ReportGSeriesColumnConfigServiceImpl implements ReportGSeriesColumnConfigService {

    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    private SystemConfigDefaultRepository systemConfigDefaultRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public ReportGSeriesConfigVO searchMonthlyRevenueDetailColumnConfigByUserId(Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        //记录不存在 user_preference 表的记录
        ReportGSeriesConfigVO result = new ReportGSeriesConfigVO();
        getColumnConfig(userPreference, userId, Category.MONTHLY_REVENUE_DETAIL_COLUMN, result);
        return result;
    }

    private void getColumnConfig(UserPreference userPreference, Long userId, Category category, ReportGSeriesConfigVO result) {
        boolean notExistsFlag = userPreference == null;
        String config = null;
        if (null !=userPreference) {
            if (category.equals(Category.MONTHLY_REVENUE_DETAIL_COLUMN)) {
                config = userPreference.getMonthlyRevenueDetailColumnConfig();
                result.setMonthlyRevenueDetailColumnConfig(config);
            } else if (category.equals(Category.QUARTERLY_NEW_HIRES_COLUMN)) {
                config = userPreference.getQuarterlyNewHiresColumnConfig();
                result.setQuarterlyNewHiresColumnConfig(config);
            } else if (category.equals(Category.QUARTERLY_RENEWALS_COLUMN)) {
                config = userPreference.getQuarterlyRenewalsColumnConfig();
                result.setQuarterlyRenewalsColumnConfig(config);
            } else {
                config = userPreference.getQuarterlyOffboardingColumnConfig();
                result.setQuarterlyOffboardingColumnConfig(config);
            }
        }
        if (notExistsFlag || StrUtil.isBlank(config)) {
            List<SystemConfigDefault> systemConfigDefaultList = systemConfigDefaultRepository.findByCategory(category);
            if (CollUtil.isEmpty(systemConfigDefaultList)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_SEARCHDASHBOARDCOLUMNCONFIGBYUSERID_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
            }
            String defaultConfig = systemConfigDefaultList.get(0).getDefaultConfig();
            if (notExistsFlag) {
                //从来没有记录
                UserPreference addUserPreference = new UserPreference();
                addUserPreference.setUserId(userId);
                if (category.equals(Category.MONTHLY_REVENUE_DETAIL_COLUMN)) {
                    addUserPreference.setMonthlyRevenueDetailColumnConfig(defaultConfig);
                    result.setMonthlyRevenueDetailColumnConfig(defaultConfig);
                } else if (category.equals(Category.QUARTERLY_NEW_HIRES_COLUMN)) {
                    addUserPreference.setQuarterlyNewHiresColumnConfig(defaultConfig);
                    result.setQuarterlyNewHiresColumnConfig(defaultConfig);
                } else if (category.equals(Category.QUARTERLY_RENEWALS_COLUMN)) {
                    addUserPreference.setQuarterlyRenewalsColumnConfig(defaultConfig);
                    result.setQuarterlyRenewalsColumnConfig(defaultConfig);
                } else {
                    addUserPreference.setQuarterlyOffboardingColumnConfig(defaultConfig);
                    result.setQuarterlyOffboardingColumnConfig(defaultConfig);
                }
                userPreferenceRepository.save(addUserPreference);
            } else {
                ReportGSeriesColumnConfigDTO dto = new ReportGSeriesColumnConfigDTO();
                if (category.equals(Category.MONTHLY_REVENUE_DETAIL_COLUMN)) {
                    dto.setMonthlyRevenueDetailColumnConfig(defaultConfig);
                    result.setMonthlyRevenueDetailColumnConfig(defaultConfig);
                    //已经有过其他记录
                    updateMonthlyRevenueDetailColumnConfigByUserId(userId, dto);
                } else if (category.equals(Category.QUARTERLY_NEW_HIRES_COLUMN)) {
                    dto.setQuarterlyNewHiresColumnConfig(defaultConfig);
                    result.setQuarterlyNewHiresColumnConfig(defaultConfig);
                    //已经有过其他记录
                    updateQuarterlyOnboardingNewHiresColumnConfigByUserId(userId, dto);
                } else if (category.equals(Category.QUARTERLY_RENEWALS_COLUMN)) {
                    dto.setQuarterlyRenewalsColumnConfig(defaultConfig);
                    result.setQuarterlyRenewalsColumnConfig(defaultConfig);
                    //已经有过其他记录
                    updateQuarterlyOnboardingRenewalsColumnConfigByUserId(userId, dto);
                } else {
                    dto.setQuarterlyOffboardingColumnConfig(defaultConfig);
                    result.setQuarterlyOffboardingColumnConfig(defaultConfig);
                    //已经有过其他记录
                    updateQuarterlyOffboardingColumnConfigByUserId(userId, dto);
                }
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMonthlyRevenueDetailColumnConfigByUserId(Long userId, ReportGSeriesColumnConfigDTO dto) {
        userPreferenceRepository.updateMonthlyRevenueDetailColumnConfigByUserId(userId, dto.getMonthlyRevenueDetailColumnConfig());
    }

    @Override
    public ReportGSeriesConfigVO searchQuarterlyOnboardingNewHiresColumnConfigByUserId(Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        //记录不存在 user_preference 表的记录
        ReportGSeriesConfigVO result = new ReportGSeriesConfigVO();
        getColumnConfig(userPreference, userId, Category.QUARTERLY_NEW_HIRES_COLUMN, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateQuarterlyOnboardingNewHiresColumnConfigByUserId(Long userId, ReportGSeriesColumnConfigDTO dto) {
        userPreferenceRepository.updateQuarterlyNewHiresColumnConfigByUserId(userId, dto.getQuarterlyNewHiresColumnConfig());

    }

    @Override
    public ReportGSeriesConfigVO searchQuarterlyOnboardingRenewalsColumnConfigByUserId(Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        //记录不存在 user_preference 表的记录
        ReportGSeriesConfigVO result = new ReportGSeriesConfigVO();
        getColumnConfig(userPreference, userId, Category.QUARTERLY_RENEWALS_COLUMN, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateQuarterlyOnboardingRenewalsColumnConfigByUserId(Long userId, ReportGSeriesColumnConfigDTO dto) {
        userPreferenceRepository.updateQuarterlyRenewalsColumnConfigByUserId(userId, dto.getQuarterlyRenewalsColumnConfig());
    }

    @Override
    public ReportGSeriesConfigVO searchQuarterlyOffboardingColumnConfigByUserId(Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        //记录不存在 user_preference 表的记录
        ReportGSeriesConfigVO result = new ReportGSeriesConfigVO();
        getColumnConfig(userPreference, userId, Category.QUARTERLY_OFFBOARDING_COLUMN, result);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateQuarterlyOffboardingColumnConfigByUserId(Long userId, ReportGSeriesColumnConfigDTO dto) {
        userPreferenceRepository.updateQuarterlyOffboardingColumnConfigByUserId(userId, dto.getQuarterlyOffboardingColumnConfig());
    }
}
