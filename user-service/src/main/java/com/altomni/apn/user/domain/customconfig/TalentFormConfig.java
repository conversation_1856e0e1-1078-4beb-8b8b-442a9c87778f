package com.altomni.apn.user.domain.customconfig;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A Job Form Config.
 */
@ApiModel(description = " talent form config is used to custom talent form")
@Data
@Entity
@Table(name = "talent_form_config")
public class TalentFormConfig extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "must not be null")
    @Column(name = "tenant_id")
    private Long tenantId;

    @ApiModelProperty("store raw json config and convert it into class in DTO")
    @Column(name = "custom_config" , columnDefinition = "json")
    @JsonRawValue
    private String customConfig;



}
