package com.altomni.apn.user.service.dto.customconfig;

import java.io.Serializable;
import java.util.List;

//@JsonDeserialize(using = CustomFieldDeserializer.class)
public class CustomFormSubField implements Serializable {
    private String field;
    private List<String> additionalInfoKeys;
    private String cnDisplay;

    public List<String> getAdditionalInfoKeys() {
        return additionalInfoKeys;
    }

    public void setAdditionalInfoKeys(List<String> additionalInfoKeys) {
        this.additionalInfoKeys = additionalInfoKeys;
    }

    private String enDisplay;
    private boolean visible;
    private boolean readOnly;
    private boolean required;
    private boolean customizable;

    public CustomFormSubField(){

    }


    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public boolean isReadOnly() {
        return readOnly;
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public boolean isCustomizable() {
        return customizable;
    }

    public void setCustomizable(boolean customizable) {
        this.customizable = customizable;
    }

    public String getCnDisplay() {
        return cnDisplay;
    }

    public void setCnDisplay(String cnDisplay) {
        this.cnDisplay = cnDisplay;
    }

    public String getEnDisplay() {
        return enDisplay;
    }

    public void setEnDisplay(String enDisplay) {
        this.enDisplay = enDisplay;
    }
}
