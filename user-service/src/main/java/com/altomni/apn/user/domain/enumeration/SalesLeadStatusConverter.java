package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class SalesLeadStatusConverter extends AbstractAttributeConverter<SalesLeadStatus, Integer> {
    public SalesLeadStatusConverter() {
        super(SalesLeadStatus::toDbValue, SalesLeadStatus::fromDbValue);
    }
}
