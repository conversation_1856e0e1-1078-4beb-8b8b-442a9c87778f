package com.altomni.apn.user.repository.permission;

import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.user.domain.permission.PermissionModulePrivilege;
import com.altomni.apn.common.domain.permission.PermissionModulePrivilegePageVM;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Set;

public interface PermissionModulePrivilegeRepository extends JpaRepository<PermissionModulePrivilege, Long> {

    @Query(value = "SELECT new com.altomni.apn.common.domain.permission.PermissionModulePrivilegePageVM(pmr.id, pmr.moduleId, pmr.privilegeId, pr.name, prg.disable , prg.isShowPermissionButton, pr.parentId) " +
            "FROM PermissionModulePrivilege pmr INNER JOIN PermissionPrivilege pr ON pmr.privilegeId = pr.id INNER JOIN PermissionModulePrivilegeConfig prg ON prg.permissionModulePrivilegeId = pmr.id " +
            "WHERE prg.tenantId = ?1 AND prg.type = ?2 ORDER BY prg.sort ASC")
    List<PermissionModulePrivilegePageVM> searchTenantModuleList(Long tenantId, TenantUserTypeEnum type);

    @Query(value = "SELECT new com.altomni.apn.common.domain.permission.PermissionModulePrivilegePageVM(pmr.id, pmr.moduleId, pmr.privilegeId, pr.name, prg.disable , prg.isShowPermissionButton, pr.parentId) " +
            "FROM PermissionModulePrivilege pmr INNER JOIN PermissionPrivilege pr ON pmr.privilegeId = pr.id INNER JOIN PermissionModulePrivilegeConfig prg ON prg.permissionModulePrivilegeId = pmr.id " +
            "WHERE prg.tenantId = ?1 AND prg.type = ?2 AND prg.enable = true ORDER BY prg.sort ASC")
    List<PermissionModulePrivilegePageVM> searchEnableTenantModuleList(Long tenantId, TenantUserTypeEnum type);

    @Query(value = "SELECT DISTINCT pmp.id FROM permission_module_privilege pmp INNER JOIN  permission_privilege pr ON pmp.privilege_id = pr.id " +
            "INNER JOIN permission_role_privilege prp ON pr.id = prp.privilege_id WHERE prp.tenant_id = ?1 AND pr.parent_id = 1", nativeQuery = true)
    Set<Long> findAllIdForPrivilegeByTenantId(Long tenantId);

}
