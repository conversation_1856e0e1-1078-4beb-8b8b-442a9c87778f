package com.altomni.apn.user.service.dto.system;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.Collection;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TalentDetailRetrievingCountDTO implements Serializable {

    private Collection<Long> tenantIds;

    private Instant retrievingTime;

}
