package com.altomni.apn.user.service.vo.customconfig;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

/**
 * report E1ReportSearchFilter
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportE1ReportFilterShareUserVO implements Serializable {


    private String userName;

    private Long userId;


}
