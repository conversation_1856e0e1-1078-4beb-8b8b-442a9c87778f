package com.altomni.apn.user.repository.user;

import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.user.domain.user.CreditTransaction;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the CreditTransactionDTO entity.
 */
@Repository
public interface CreditTransactionRepository extends JpaRepository<CreditTransaction, Long> {

    List<CreditTransaction> findByTenantIdAndStatus(Long tenantId, Status status);

    CreditTransaction findByProfileIdAndTenantIdAndStatus(String profileId, Long tenantId, Status status);

    @Query(value = "select sum(ct.credit) from credit_transaction ct where ct.user_id =?1",nativeQuery = true)
    Integer findCreditCount(Long userId);

    List<CreditTransaction> findAllByTenantIdAndStatusAndProfileIdIn(Long tenantId, Status status, List<String> profileId);

    List<CreditTransaction> findAllByTenantIdAndStatusAndProfileId(Long tenantId, Status status, String profileId);


    List<CreditTransaction> findAllByTenantIdAndStatusAndCommonDBSearchESIdIn(Long tenantId, Status status, List<String> profileId);
    CreditTransaction findByTenantIdAndStatusAndTalentIdIs(Long tenantId, Status status, Long talentId);

    List<CreditTransaction> findAllByTenantIdAndStatusAndTalentIdIn(Long tenantId, Status status, List<Long> talentId);
}
