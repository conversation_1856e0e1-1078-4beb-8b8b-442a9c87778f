package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The IndustryType enumeration.
 */
public enum SalesLeadStatus implements ConvertedEnum<Integer> {
    UN_ASSIGN(0),
    UPGRADE_TO_CLIENT(1),
    CONTRACT(2);

    private final int dbValue;

    SalesLeadStatus(int dbValue) {
        this.dbValue = dbValue;
    }


    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<SalesLeadStatus, Integer> resolver = new ReverseEnumResolver<>(SalesLeadStatus.class, SalesLeadStatus::toDbValue);

    public static SalesLeadStatus fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
