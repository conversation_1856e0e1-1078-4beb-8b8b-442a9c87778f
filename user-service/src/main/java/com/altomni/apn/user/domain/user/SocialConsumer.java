package com.altomni.apn.user.domain.user;

import com.altomni.apn.common.domain.user.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.persistence.*;
import java.io.Serializable;

@ApiModel(description = "Consumer signed up from a social network")
@Entity
@Table(name = "social_consumer")
public class SocialConsumer implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @ApiModelProperty(value = "LinkedIn")
    @Column(name = "provider")
    @NotBlank
    private String provider;

    @Column(name = "id_in_provider")
    @NotBlank
    private String idInProvider;

    @ApiModelProperty(value = "The user json content from provider")
    @Column(name = "details")
    private String details;

    @OneToOne(fetch = FetchType.EAGER, cascade = CascadeType.ALL)
    @JoinColumn(name = "user_id", unique = true, nullable = false)
    private User user;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getProvider() {
        return provider;
    }

    public void setProvider(String provider) {
        this.provider = provider;
    }

    public String getIdInProvider() {
        return idInProvider;
    }

    public void setIdInProvider(String idInProvider) {
        this.idInProvider = idInProvider;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    @Override
    public String toString() {
        return "SocialConsumer{" +
            "id=" + id +
            ", provider='" + provider + '\'' +
            ", idInProvider='" + idInProvider + '\'' +
            ", details='" + details + '\'' +
            ", user=" + user +
            '}';
    }
}
