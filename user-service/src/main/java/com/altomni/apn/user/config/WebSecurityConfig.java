package com.altomni.apn.user.config;

import com.altomni.apn.common.auth.SkipOAuthTokenResolver;
import com.altomni.apn.common.auth.agency_auth.AgencyTokenFilter;
import com.altomni.apn.common.auth.agency_auth.AgencyUserTokenStore;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.oauth2.server.resource.web.BearerTokenAuthenticationEntryPoint;
import org.springframework.security.oauth2.server.resource.web.BearerTokenResolver;
import org.springframework.security.oauth2.server.resource.web.authentication.BearerTokenAuthenticationFilter;
import org.springframework.security.web.SecurityFilterChain;

/**
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class WebSecurityConfig {

    private static final String[] AGENCY_ENDPOINTS = {
            "/**"
//            "/api/v3/users/common/tenant-param/{paramKey}/value",
//            "/api/v3/users/{id}",
//            "/api/v3/talents/config/talent-form",
//            "/api/v3/users/all-brief-by-ids/including-inactive",
//            "/api/v3/user/preferences/personalization"
    };

    private final AgencyUserTokenStore agencyUserTokenStore;

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        // 资源服务器配置
        http.oauth2ResourceServer().opaqueToken(Customizer.withDefaults());
        // 没有 token 或者 token 过期，返回 401
        http.exceptionHandling(exceptionHandling -> exceptionHandling.authenticationEntryPoint(new BearerTokenAuthenticationEntryPoint()));

        // 无状态 session
        http.sessionManagement((session) -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));

        http.csrf().disable()
                .authorizeRequests()
                .requestMatchers("/api/v3/register").permitAll()
                .requestMatchers("/api/v3/consumer/register").permitAll()
                .requestMatchers("/api/v3/login").permitAll()
                .requestMatchers("/api/v3/account/admin/login").permitAll()
                .requestMatchers("/api/v3/login/social").permitAll()
                .requestMatchers("/api/v3/refresh-token").permitAll()
                .requestMatchers("/api/v3/activate").permitAll()
                .requestMatchers("/api/v3/account/reset_password/init").permitAll()
                .requestMatchers("/api/v3/account/reset_password/finish").permitAll()
                .requestMatchers("/api/v3/limit-user/register").permitAll()
                .requestMatchers("/api/v3/users/get-privileges").permitAll()
                .requestMatchers("/api/v3/users/add-privilege").permitAll()
                .requestMatchers("/actuator/**").permitAll().anyRequest().authenticated();

        http.addFilterBefore(agencyTokenFilter(agencyUserTokenStore), BearerTokenAuthenticationFilter.class);

        return http.build();
    }

    @Bean
    public BearerTokenResolver bearerTokenResolver() {
        return new SkipOAuthTokenResolver();
    }

    public AgencyTokenFilter agencyTokenFilter(AgencyUserTokenStore agencyUserTokenStore) {
        return new AgencyTokenFilter(agencyUserTokenStore, AGENCY_ENDPOINTS);
    }

}
