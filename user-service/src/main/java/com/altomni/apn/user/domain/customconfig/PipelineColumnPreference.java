package com.altomni.apn.user.domain.customconfig;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.domain.AbstractAuditingEntity;

import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.ServiceUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "pipeline_column_preference")
public class PipelineColumnPreference extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 3297627398848418727L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;


    @Column(name = "template_name")
    private String templateName;


    @Column(name = "customConfig")
    private String customConfig;




//    public static PipelineColumnPreference toEntity(PipelineColumnPreferenceDTO dto) {
//        PipelineColumnPreference entity = new PipelineColumnPreference();
//        ServiceUtils.myCopyProperties(dto, entity);
//        entity.setUserId(SecurityUtils.getUserId());
//        return entity;
//    }
//
//    public static PipelineColumnPreferenceDTO toDto(PipelineColumnPreference entity) {
//        PipelineColumnPreferenceDTO dto = new PipelineColumnPreferenceDTO();
//        ServiceUtils.myCopyProperties(entity, dto);
//        dto.setCreationType(JsonUtil.fromJson(JSONUtil.toJsonStr(entity.getCreationType()), CreationTypeDTO.class));
//        return dto;
//    }
}
