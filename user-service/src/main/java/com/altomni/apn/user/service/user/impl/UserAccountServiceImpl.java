package com.altomni.apn.user.service.user.impl;

import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.user.domain.enumeration.AccountStatus;
import com.altomni.apn.user.domain.enumeration.CreditEffectType;
import com.altomni.apn.user.domain.enumeration.UserAccountType;
import com.altomni.apn.user.domain.user.UserAccount;
import com.altomni.apn.user.repository.user.UserAccountRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.user.CommonService;
import com.altomni.apn.user.service.user.UserAccountService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * Service Implementation for managing UserAccount.
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class UserAccountServiceImpl implements UserAccountService {

    @Resource
    private UserAccountRepository userAccountRepository;

    @Resource
    private CommonService commonService;

    @Resource
    private UserRepository userRepository;

    @Override
    @Transactional(readOnly = true)
    public UserAccount findByUserId(Long userId) {
        return userAccountRepository.findAvailableAccountByUserId(userId);
    }

    @Override
    public int updateById(Long id, Integer monthlyCredit, Integer bulkCredit, Integer effectCredit, CreditEffectType type, Integer version) {
        return userAccountRepository.updateCreditById(id, monthlyCredit,bulkCredit,effectCredit,type.toDbValue(), version);
    }

    @Override
    public UserAccount createUserAccount(Long userId, Long tenantId, Integer credit, Integer bulkCredit, Integer effectCredit, CreditEffectType type) {

        return userAccountRepository.save(new UserAccount().userId(userId)
            .accountType(UserAccountType.USER)
            .amount(credit)
            .bulkAmount(bulkCredit)
            .creditEffectType(type)
            .effectCredit(effectCredit)
            .frozenAmount(0)
            .totalAmount(credit)

            .accountStatus(AccountStatus.AVAILABLE)
            .expireDate(DateUtil.lastDayOfCurrentMonth()));
    }

    @Override
    public UserAccount createTenantAdminUserAccount(Long userId, Long tenantId,Integer credit) {

        return userAccountRepository.save(new UserAccount().userId(userId)
            .accountType(UserAccountType.TENANT_ADMIN)
            .amount(credit)
            .frozenAmount(0)
            .totalAmount(0)
            .accountStatus(AccountStatus.AVAILABLE)
            .expireDate(DateUtil.lastDayOfCurrentMonth()));
    }

    @Override
    public UserAccount createLimitUserAccount(Long userId, Long tenantId) {
        updateUserCredit(0, userId);
        return userAccountRepository.save(new UserAccount().userId(userId)
                .accountType(UserAccountType.LIMIT_USER)
                .amount(0)
                .frozenAmount(0)
                .totalAmount(0)
                .accountStatus(AccountStatus.AVAILABLE)
                .expireDate(DateUtil.lastDayOfCurrentMonth()));
    }

    @Override
    public UserAccount initUserAccount(Long userId, Long tenantId) {
        Integer userCreditLimit = commonService.getUserCreditLimit(tenantId);
        updateUserCredit(userCreditLimit - Constants.PER_CREDIT, userId);
        return userAccountRepository.save(new UserAccount().userId(userId)
                .accountType(UserAccountType.USER)
                .amount(userCreditLimit - Constants.PER_CREDIT)
                .frozenAmount(0)
                .totalAmount(0)
                .accountStatus(AccountStatus.AVAILABLE)
                .expireDate(DateUtil.lastDayOfCurrentMonth()));
    }

    @Override
    public UserAccount initLimitUserAccount(Long userId, Long tenantId) {
        Integer limitUserCreditLimit = commonService.getLimitUserCreditLimit(tenantId);
        updateUserCredit(limitUserCreditLimit - Constants.PER_CREDIT, userId);
        return userAccountRepository.save(new UserAccount().userId(userId)
                .accountType(UserAccountType.LIMIT_USER)
                .amount(limitUserCreditLimit - Constants.PER_CREDIT)
                .frozenAmount(0)
                .totalAmount(0)
                .accountStatus(AccountStatus.AVAILABLE)
                .expireDate(DateUtil.lastDayOfCurrentMonth()));
    }

    @Override
    public UserAccount updateLimitUserAccount(UserAccount userAccount, Long tenantId) {
        Integer limitUserCreditLimit = commonService.getLimitUserCreditLimit(tenantId);
        userAccount.setAccountStatus(AccountStatus.INVALID);
        userAccount.setExpireDate(DateUtil.todayYearMonth());
        userAccountRepository.save(userAccount);
        updateUserCredit(limitUserCreditLimit, userAccount.getUserId());
        return userAccountRepository.save(new UserAccount().userId(userAccount.getUserId())
                .accountType(UserAccountType.LIMIT_USER)
                .amount(limitUserCreditLimit)
                .frozenAmount(0)
                .totalAmount(0)
                .accountStatus(AccountStatus.AVAILABLE)
                .expireDate(DateUtil.lastDayOfCurrentMonth()));
    }

    private void updateUserCredit(Integer amount, Long userId) {
        User user = userRepository.findById(userId).orElseThrow();
        user.setMonthlyCredit(amount);
        userRepository.save(user);
    }
}
