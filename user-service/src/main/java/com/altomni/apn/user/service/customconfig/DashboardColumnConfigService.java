package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.service.dto.customconfig.DashboardColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.DashboardColumnConfigVO;

public interface DashboardColumnConfigService {

    DashboardColumnConfigVO searchDashboardColumnConfigByUserId(Long userId);

    void updateDashboardColumnConfigByUserId(Long userId, DashboardColumnConfigDTO dto);

}
