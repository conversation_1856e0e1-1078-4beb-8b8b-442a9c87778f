package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The ContractType enumeration.
 */
public enum ContractType implements ConvertedEnum<Integer> {
    GENERAL_RECRUITING(0), // EXECUTIVE_RECRUITING
    RECRUITMENT_PROCESS_OUTSOURCING(1),
    GENERAL_STAFFING(2), //STAFFING
    PAYROLL(3),
    CAMPUS_RECRUITING(4),
    INTERNSHIP(5),
    OTHERS(99);

    private final Integer dbValue;

    ContractType(Integer dbValue) { this.dbValue = dbValue; }

    @Override
    public Integer toDbValue() { return dbValue; }

    // static resolving:
    public static final ReverseEnumResolver<ContractType, Integer> resolver =
        new ReverseEnumResolver<>(ContractType.class, ContractType::toDbValue);

    public static ContractType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
