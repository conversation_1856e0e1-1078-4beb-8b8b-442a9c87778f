package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;

/**
 * The Form Category enumeration.
 */
public enum ConfigSubcategory implements ConvertedEnum<Short> {
    //first two digit represent the category enum value

    // 0 already taken by meaningful enum, use 99 represent the default
    JOB_DEFAULT((short) 1199, "JOB_DEFAULT_FORM", "Job Form default template"),
    JOB_FORM_CONTRACT((short) 1101, "CONTRACT", "Job Form template for Contract"),
    JOB_FORM_FTE((short) 1103, "FULL_TIME","Job Form template for FTE"),

    JOB_FORM_PAYROLL((short) 1105, "PAY_ROLL",  "Job Form template for PayRoll"),

    JOB_FORM_RPO_FRONT_AND_BACK((short) 1107, "RPO_FRONT_AND_BACK", "Job Form template for front and back"),
    JOB_FORM_RPO_BATCH_RECRUITMENT((short) 1109, "RPO_BATCH_RECRUITMENT", "Job Form template for batch recruitment"),

    PRIVATE_JOB_FORM_CONTRACT((short) 1111, "CONTRACT", "Job Form template for Contract"),
    PRIVATE_JOB_FORM_FTE((short) 1113, "FULL_TIME","Job Form template for FTE"),

    JOB_FORM_MSP((short) 1114, "MSP", "Job Form template for MSP"),
    PRIVATE_JOB_FORM_MSP((short) 1115, "MSP","Job Form template for MSP"),

    //通用版
    JOB_FORM_GENERAL((short) 1180, "OTHERS", "Job Form common template for general recruiting"),
    JOB_FORM_GENERAL_EMPLOYER((short)1181,"EMPLOYER","Job Form common template for general recruiting"),

    JOB_FORM_GENERAL_HEADHUNTER((short)1182, "HEADHUNTER", "Job Form common template for general recruiting"),


    //通用版 column
    JOB_COLUMN_DEFAULT((short) 1299, "JOB_COLUMN_DEFAULT", "Job Column general version template"),

    JOB_COLUMN_GENERAL((short) 1280, "GENERAL_COLUMN", "Job Column general template"),
    JOB_COLUMN_GENERAL_EMPLOYER((short) 1281, "EMPLOYER", "Job Column general For employer company template"),
    JOB_COLUMN_HEADHUNTER((short) 1282, "HEADHUNTER", "Job Column general for head hunter company template"),

    //customized
    JOB_COLUMN_CUSTOMIZED((short) 1203 , "JOB_COLUMN_CUSTOMIZED", "Job Column customized template"),

    PRIVATE_JOB_COLUMN_CUSTOMIZED((short) 1213 , "PRIVATE_JOB_COLUMN_CUSTOMIZED", "Private Job Column customized template"),

    COMPANY_JOB_COLUMN_CUSTOMIZED((short) 1214 , "COMPANY_JOB_COLUMN_CUSTOMIZED", "Company Job Column customized template"),

    /*
    talent part category
     */
    TALENT_FORM_DEFAULT((short) 2199, "TALENT_DEFAULT_FORM", "Talent DEFAULT Form template"),

    /*
    我的候选人/所有候选人
     */
    // tenant exclude 4,14,41
    TALENT_COLUMN_GENERAL((short) 2210, "TALENT_COLUMN_CONFIG","Talent column config - my candidate/all candidates"),
    // tenant 4,14,41
    TALENT_COLUMN_CUSTOMIZED((short) 2211, "TALENT_COLUMN_CUSTOMIZED_CONFIG","Talent column CUSTOMIZED config - my candidate/all candidates"),

    /*
    数据库候选人列配置
     */
    TALENT_COLUMN_DATABASE((short) 2220, "TALENT_DATABASE_COLUMN_CONFIG","Talent Database column config"),

    /*
    候选人流程管理列配置
     */
    // tenant exclude 4,14,41
    TALENT_COLUMN_PIPELINE_GENERAL((short) 2230, "MY_PIPELINE_COLUMN_CONFIG","Pipeline management column config"),
    // tenant 4,14,41
    TALENT_COLUMN_PIPELINE_CUSTOMIZED((short) 2231, "MY_PIPELINE_COLUMN_CUSTOMIZED_CONFIG","Pipeline CUSTOMIZED management column config"),


    TALENT_COLUMN_RELATE_JOB_FOLDER((short) 2240, "TALENT_RELATE_JOB_FOLDER_COLUMN_CONFIG","Talent relate job folder column config"),
    TALENT_COLUMN_RELATE_JOB_FOLDER_CUSTOMIZED((short) 2241, "TALENT_RELATE_JOB_FOLDER_COLUMN_CONFIG","Talent relate job folder column customized config"),
    //删除 使用与我的/所有候选人列表配置项
//    TALENT_COLUMN_SEARCH_FOLDER((short) 2250, "TALENT_COLUMN_SEARCH_FOLDER","Talent search folder column config"),
//    TALENT_COLUMN_SEARCH_FOLDER_CUSTOMIZED((short) 2251, "TALENT_COLUMN_SEARCH_FOLDER_CUSTOMIZED","Talent search folder column customized config"),


    //通用版 company column
    COMPANY_COLUMN_PROSPECT_DEFAULT((short) 3299, "COMPANY_COLUMN_PROSPECT_DEFAULT", "Company prospect column general version template"),
    COMPANY_COLUMN_CLIENT_DEFAULT((short) 3298, "COMPANY_COLUMN_CLIENT_DEFAULT", "Company client column general version template"),

    //定制版 company column
    COMPANY_COLUMN_PROSPECT_CUSTOMIZED((short) 3211, "COMPANY_COLUMN_PROSPECT_CUSTOMIZED", "Company prospect column customized version template"),
    COMPANY_COLUMN_CLIENT_CUSTOMIZED((short) 3210, "COMPANY_COLUMN_CLIENT_CUSTOMIZED", "Company client column customized version template"),

    //company current contractor
    COMPANY_COLUMN_CURRENT_CONTRACTOR((short) 3240, "COMPANY_COLUMN_CURRENT_CONTRACTOR", "company current contractor column"),

    COMPANY_AFFILIATES_COLUMN_CUSTOMIZED((short) 3241, "COMPANY_AFFILIATES_COLUMN_CUSTOMIZED", "Accounts affiliates column config"),


    //kpi report by user
    KPI_REPORT_BY_USER((short) 4001, "KPI_REPORT_BY_USER", "kpi report_by user"),
    KPI_REPORT_BY_USER_JOB_DETAIL((short) 4002, "KPI_REPORT_BY_USER_JOB_DETAIL", "KPI_REPORT_BY_USER_TALENT_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_DETAIL((short) 4003, "KPI_REPORT_BY_USER_TALENT_DETAIL", "KPI_REPORT_BY_USER_TALENT_DETAIL"),
    KPI_REPORT_BY_USER_SUBMIT_TO_JOB_DETAIL((short) 4004, "KPI_REPORT_BY_USER_SUBMIT_TO_JOB_DETAIL", "KPI_REPORT_BY_USER_SUBMIT_TO_JOB_DETAIL"),
    KPI_REPORT_BY_USER_SUBMIT_TO_CLIENT_DETAIL((short) 4005, "KPI_REPORT_BY_USER_SUBMIT_TO_CLIENT_DETAIL", "KPI_REPORT_BY_USER_SUBMIT_TO_CLIENT_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_1_DETAIL((short) 4006, "KPI_REPORT_BY_USER_INTERVIEW_1_DETAIL", "KPI_REPORT_BY_USER_INTERVIEW_1_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_2_DETAIL((short) 4007, "KPI_REPORT_BY_USER_INTERVIEW_2_DETAIL", "KPI_REPORT_BY_USER_INTERVIEW_2_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_FINAL_DETAIL((short) 4008, "KPI_REPORT_BY_USER_INTERVIEW_FINAL_DETAIL", "KPI_REPORT_BY_USER_INTERVIEW_FINAL_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_DETAIL((short) 4009, "KPI_REPORT_BY_USER_INTERVIEW_DETAIL", "KPI_REPORT_BY_USER_INTERVIEW_DETAIL"),
    KPI_REPORT_BY_USER_OFFER_DETAIL((short) 4010, "KPI_REPORT_BY_USER_OFFER_DETAIL", "KPI_REPORT_BY_USER_OFFER_DETAIL"),
    KPI_REPORT_BY_USER_OFFER_ACCEPT_DETAIL((short) 4011, "KPI_REPORT_BY_USER_OFFER_ACCEPT_DETAIL", "KPI_REPORT_BY_USER_OFFER_ACCEPT_DETAIL"),
    KPI_REPORT_BY_USER_ONBOARD_DETAIL((short) 4012, "KPI_REPORT_BY_USER_ONBOARD_DETAIL", "KPI_REPORT_BY_USER_ONBOARD_DETAIL"),
    KPI_REPORT_BY_USER_ELIMINATE_DETAIL((short) 4013, "KPI_REPORT_BY_USER_ELIMINATE_DETAIL", "KPI_REPORT_BY_USER_ELIMINATE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_CALL_NOTE_DETAIL((short) 4014, "KPI_REPORT_BY_USER_TALENT_CALL_NOTE_DETAIL", "KPI_REPORT_BY_USER_TALENT_CALL_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_EMAIL_NOTE_DETAIL((short) 4015, "KPI_REPORT_BY_USER_TALENT_EMAIL_NOTE_DETAIL", "KPI_REPORT_BY_USER_TALENT_EMAIL_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_PERSON_NOTE_DETAIL((short) 4016, "KPI_REPORT_BY_USER_TALENT_PERSON_NOTE_DETAIL", "KPI_REPORT_BY_USER_TALENT_PERSON_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_VIDEO_NOTE_DETAIL((short) 4017, "KPI_REPORT_BY_USER_TALENT_VIDEO_NOTE_DETAIL", "KPI_REPORT_BY_USER_TALENT_VIDEO_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_OTHER_NOTE_DETAIL((short) 4018, "KPI_REPORT_BY_USER_TALENT_OTHER_NOTE_DETAIL", "KPI_REPORT_BY_USER_TALENT_OTHER_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_PIPELINE_NOTE_DETAIL((short) 4019, "KPI_REPORT_BY_USER_TALENT_PIPELINE_NOTE_DETAIL", "KPI_REPORT_BY_USER_TALENT_PIPELINE_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_APN_PRO_NOTE_DETAIL((short) 4020, "KPI_REPORT_BY_USER_TALENT_APN_PRO_NOTE_DETAIL", "KPI_REPORT_BY_USER_TALENT_APN_PRO_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_CREATE_COMPANY_DETAIL((short) 4021, "KPI_REPORT_BY_USER_CREATE_COMPANY_DETAIL", "KPI_REPORT_BY_USER_CREATE_COMPANY_DETAIL"),
    KPI_REPORT_BY_USER_UPGRADE_TO_CLIENT_DETAIL((short) 4022, "KPI_REPORT_BY_USER_TALENT_APN_PRO_NOTE_DETAIL", "KPI_REPORT_BY_USER_TALENT_APN_PRO_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_APPOINTMENTS_DETAIL((short) 4023, "KPI_REPORT_BY_USER_INTERVIEW_APPOINTMENTS_DETAIL", "KPI_REPORT_BY_USER_INTERVIEW_APPOINTMENTS_DETAIL"),
    KPI_REPORT_BY_USER_INTERVIEW_TWO_MORE_DETAIL((short) 4024, "KPI_REPORT_BY_USER_INTERVIEW_TWO_MORE_DETAIL", "KPI_REPORT_BY_USER_INTERVIEW_TWO_MORE_DETAIL"),
    KPI_REPORT_BY_USER_TALENT_ICI_NOTE_DETAIL((short) 4025, "KPI_REPORT_BY_USER_TALENT_ICI_NOTE_DETAIL", "KPI_REPORT_BY_USER_TALENT_ICI_NOTE_DETAIL"),
    KPI_REPORT_BY_USER_VOIP((short) 4026, "KPI_REPORT_BY_USER_VOIP", "KPI_REPORT_BY_USER_VOIP"),
    KPI_REPORT_BY_USER_VOIP_DETAIL((short) 4027, "KPI_REPORT_BY_USER_VOIP_DETAIL", "KPI_REPORT_BY_USER_VOIP_DETAIL"),



    //kpi report by company
    KPI_REPORT_BY_COMPANY((short) 4101, "KPI_REPORT_BY_COMPANY", "kpi report by user"),
    KPI_REPORT_BY_COMPANY_JOB_DETAIL((short) 4102, "KPI_REPORT_BY_COMPANY_JOB_DETAIL", "KPI_REPORT_BY_COMPANY_JOB_DETAIL"),
    KPI_REPORT_BY_COMPANY_TALENT_DETAIL((short) 4103, "KPI_REPORT_BY_COMPANY_TALENT_DETAIL", "KPI_REPORT_BY_USER_TALENT_DETAIL"),
    KPI_REPORT_BY_COMPANY_SUBMIT_TO_JOB_DETAIL((short) 4104, "KPI_REPORT_BY_COMPANY_SUBMIT_TO_JOB_DETAIL", "KPI_REPORT_BY_COMPANY_SUBMIT_TO_JOB_DETAIL"),
    KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL((short) 4105, "KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL", "KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL"),
    KPI_REPORT_BY_COMPANY_INTERVIEW_1_DETAIL((short) 4106, "KPI_REPORT_BY_COMPANY_INTERVIEW_1_DETAIL", "KPI_REPORT_BY_COMPANY_INTERVIEW_1_DETAIL"),
    KPI_REPORT_BY_COMPANY_INTERVIEW_2_DETAIL((short) 4107, "KPI_REPORT_BY_COMPANY_INTERVIEW_2_DETAIL", "KPI_REPORT_BY_COMPANY_INTERVIEW_2_DETAIL"),
    KPI_REPORT_BY_COMPANY_INTERVIEW_FINAL_DETAIL((short) 4108, "KPI_REPORT_BY_COMPANY_INTERVIEW_FINAL_DETAIL", "KPI_REPORT_BY_COMPANY_INTERVIEW_FINAL_DETAIL"),
    KPI_REPORT_BY_COMPANY_INTERVIEW_DETAIL((short) 4109, "KPI_REPORT_BY_COMPANY_INTERVIEW_DETAIL", "KPI_REPORT_BY_COMPANY_INTERVIEW_DETAIL"),
    KPI_REPORT_BY_COMPANY_OFFER_DETAIL((short) 4110, "KPI_REPORT_BY_COMPANY_OFFER_DETAIL", "KPI_REPORT_BY_COMPANY_OFFER_DETAIL"),
    KPI_REPORT_BY_COMPANY_OFFER_ACCEPT_DETAIL((short) 4111, "KPI_REPORT_BY_COMPANY_OFFER_ACCEPT_DETAIL", "KPI_REPORT_BY_COMPANY_OFFER_ACCEPT_DETAIL"),
    KPI_REPORT_BY_COMPANY_ONBOARD_DETAIL((short) 4112, "KPI_REPORT_BY_COMPANY_ONBOARD_DETAIL", "KPI_REPORT_BY_COMPANY_ONBOARD_DETAIL"),
    KPI_REPORT_BY_COMPANY_ELIMINATE_DETAIL((short) 4113, "KPI_REPORT_BY_COMPANY_ELIMINATE_DETAIL", "KPI_REPORT_BY_COMPANY__ELIMINATE_DETAIL"),
    KPI_REPORT_BY_COMPANY_JOB_NOTE_DETAIL((short) 4114, "KPI_REPORT_BY_COMPANY_JOB_NOTE_DETAIL", "KPI_REPORT_BY_COMPANY_JOB_NOTE_DETAIL"),
    KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_THIS_WEEK((short) 4115, "KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_THIS_WEEK", "KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_THIS_WEEK"),
    KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_LAST_WEEK((short) 4116, "KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_LAST_WEEK", "KPI_REPORT_BY_COMPANY_SUBMIT_TO_CLIENT_DETAIL_LAST_WEEK"),
    KPI_REPORT_BY_COMPANY_BD_PROGRESS_NOTES((short) 4117, "KPI_REPORT_BY_COMPANY_BD_PROGRESS_NOTES", "KPI_REPORT_BY_COMPANY_BD_PROGRESS_NOTES"),
    KPI_REPORT_BY_COMPANY_NOTES((short) 4118, "KPI_REPORT_BY_COMPANY_NOTES", "KPI_REPORT_BY_COMPANY_NOTES"),

    USER_ADOPTION_REPORT((short) 4200, "USER_ADOPTION_REPORT", "USER_ADOPTION_REPORT"),
    USER_ADOPTION_REPORT_USER_ACTIVE_DURATION((short) 4201, "USER_ADOPTION_REPORT_USER_ACTIVE_DURATION", "USER_ADOPTION_REPORT_USER_ACTIVE_DURATION"),
    USER_ADOPTION_REPORT_CALLS_CANDIDATE((short) 4202, "USER_ADOPTION_REPORT_CALLS_CANDIDATE", "USER_ADOPTION_REPORT_CALLS_CANDIDATE"),
    USER_ADOPTION_REPORT_CALLS_CONTACT((short) 4203, "USER_ADOPTION_REPORT_CALLS_CONTACT", "USER_ADOPTION_REPORT_CALLS_CONTACT"),
    USER_ADOPTION_REPORT_NOTES_CANDIDATE((short) 4204, "USER_ADOPTION_REPORT_NOTES_CANDIDATE", "USER_ADOPTION_REPORT_NOTES_CANDIDATE"),
    USER_ADOPTION_REPORT_NOTES_CONTACT((short) 4205, "USER_ADOPTION_REPORT_NOTES_CONTACT", "USER_ADOPTION_REPORT_NOTES_CONTACT"),
    USER_ADOPTION_REPORT_EMAILS_CANDIDATE((short) 4206, "USER_ADOPTION_REPORT_EMAILS_CANDIDATE", "USER_ADOPTION_REPORT_EMAILS_CANDIDATE"),
    USER_ADOPTION_REPORT_EMAILS_CONTACT((short) 4207, "USER_ADOPTION_REPORT_EMAILS_CONTACT", "USER_ADOPTION_REPORT_EMAILS_CONTACT"),
    USER_ADOPTION_REPORT_SUBMIT_TO_JOB((short) 4208, "USER_ADOPTION_REPORT_SUBMIT_TO_JOB", "USER_ADOPTION_REPORT_SUBMIT_TO_JOB"),
    USER_ADOPTION_REPORT_INTERVIEW((short) 4209, "USER_ADOPTION_REPORT_INTERVIEW", "USER_ADOPTION_REPORT_INTERVIEW"),
    USER_ADOPTION_REPORT_ONBOARD((short) 4210, "USER_ADOPTION_REPORT_ONBOARD", "USER_ADOPTION_REPORT_ONBOARD"),

    H1_REPORT_DETAIL((short) 4300, "H1_REPORT_DETAIL", "H1_REPORT_DETAIL"),
    H2_REPORT_DETAIL((short) 4301, "H2_REPORT_DETAIL", "H2_REPORT_DETAIL"),


    REPORT_COMPANY_FTE_BD_REPORT((short) 8110, "REPORT_COMPANY_FTE_BD_REPORT", "report company fte bd report column"),
    USER_SEARCH_COLUMN((short) 9710, "USER_SEARCH_COLUMN", "user search column"),
    USER_SEARCH_ALL_COLUMN((short) 9720, "USER_SEARCH_ALL_COLUMN", "user search all column")

    ;


    private final short dbValue;

    //enumName is using  the same string used for JobType enum name;
    private final String enumName;
    private final String text;

    ConfigSubcategory(short dbValue, String enumName, String text) {
        this.dbValue = dbValue;
        this.enumName = enumName;
        this.text = text;
    }

    public short getDbValue() {
        return dbValue;
    }

    public String getText() {
        return text;
    }

    public String getEnumName(){
        return enumName;
    }

    @Override
    public Short toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<ConfigSubcategory, Short> resolver = new ReverseEnumResolver<>(ConfigSubcategory.class, ConfigSubcategory::toDbValue);

    public static ConfigSubcategory fromDbValue(Short dbValue) {
        return resolver.get(dbValue);
    }

    public static ConfigSubcategory fromJobType(JobType jobType) {
        int minValue = Category.JOB_FORM.getDbValue() * 100;
        int maxValue = (Category.JOB_FORM.getDbValue() + 1) * 100;
        for (ConfigSubcategory subCategory : values()) {
            if(subCategory.dbValue < minValue || subCategory.dbValue >= maxValue ){
                continue;
            }
            if (subCategory.getEnumName().equals(jobType.name())) {
                return subCategory;
            }
        }
        throw new CustomParameterizedException("Current Job Type dont have corresponding default Job form config. Please contact with admin.");
    }

    public static ConfigSubcategory getFormEnumByTenantUserTypeEnum(TenantUserTypeEnum userTypeEnum) {
        int minValue = ConfigSubcategory.JOB_FORM_GENERAL.getDbValue() + 1;
        int maxValue = (ConfigSubcategory.JOB_FORM_GENERAL.getDbValue() + 10);
        for (ConfigSubcategory subCategory : values()) {
            if(subCategory.dbValue < minValue || subCategory.dbValue >= maxValue ){
                continue;
            }
            if (subCategory.getEnumName().equals(userTypeEnum.name())) {
                return subCategory;
            }
        }
        throw new CustomParameterizedException("Current Company Type dont have corresponding default Job form config. Please contact with admin.");
    }

}
