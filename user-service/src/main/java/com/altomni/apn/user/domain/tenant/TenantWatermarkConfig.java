package com.altomni.apn.user.domain.tenant;


import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.Data;

import javax.persistence.*;

@Entity
@Data
@Table(name = "tenant_watermark_config")
public class TenantWatermarkConfig extends AbstractAuditingEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "tenant_id", nullable = false, updatable = false)
    private Long tenantId;

    @Column(name = "obj_key")
    private String objKey;

    @Column(name = "is_active")
    private Boolean active = true;
}
