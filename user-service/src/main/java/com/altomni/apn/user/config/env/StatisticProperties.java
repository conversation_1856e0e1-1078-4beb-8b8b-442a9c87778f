package com.altomni.apn.user.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Data
@RefreshScope
@Configuration
public class StatisticProperties {

    @Value("${public-statistic.notification.talentView}")
    private String talentViewNotification;
}
