package com.altomni.apn.user.service.permission.impl;

import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.domain.permission.PermissionTeamLeader;
import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import com.altomni.apn.user.repository.permission.PermissionTeamLeaderRepository;
import com.altomni.apn.user.repository.permission.PermissionTeamRepository;
import com.altomni.apn.user.repository.permission.PermissionUserTeamRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.permission.PermissionTeamDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamTreeDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.service.mapper.permission.TeamMapper;
import com.altomni.apn.user.service.permission.PermissionTeamService;
import com.altomni.apn.user.service.permission.PermissionUserTeamService;
import com.altomni.apn.user.service.user.UserService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamCreateVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamJobAndUserCountVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamUpdateVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class PermissionTeamServiceImpl implements PermissionTeamService {

    private static final Long TEAM_ROOT_ID = -1L;

    @Resource
    private PermissionTeamRepository permissionTeamRepository;

    @Resource
    private PermissionTeamLeaderRepository permissionTeamLeaderRepository;

    @Resource
    private PermissionUserTeamRepository permissionUserTeamRepository;

    @Resource
    private PermissionUserTeamService permissionUserTeamService;

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserService userService;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private InitiationService initiationService;

    @Resource
    private TeamMapper teamMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    private static final Set<Integer> teamCategoryIdSet = Set.of(15, 20);


    @Override
    public PermissionTeamDTO create(PermissionTeamCreateVM permissionTeamCreateVM) {
        this.checkTenant(permissionTeamCreateVM.getParentId());
        this.checkTeamName(permissionTeamCreateVM.getName());
        PermissionTeam permissionTeam = new PermissionTeam().setIsLeaf(Boolean.TRUE).setDeleted(Boolean.FALSE);
        BeanUtils.copyProperties(permissionTeamCreateVM, permissionTeam);
        permissionTeam.setTenantId(SecurityUtils.getTenantId());
        if(Objects.isNull(permissionTeam.getParentId())){
            permissionTeam.setParentId(TEAM_ROOT_ID);
        }
        Optional<PermissionTeam> optionalParentTeam = permissionTeamRepository.findById(permissionTeam.getParentId());
        if (optionalParentTeam.isPresent()){
            PermissionTeam parentTeam = optionalParentTeam.get();
            this.fillTeamCodeAndLevelForNonRootTeam(permissionTeam, parentTeam, null);
        }else{
            this.fillTeamCodeAndLevelForRootTeam(permissionTeam);
        }
        if (Objects.isNull(permissionTeam.getCode()) || Objects.isNull(permissionTeam.getLevel())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_CREATE_CODENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return teamMapper.toDto(permissionTeamRepository.save(permissionTeam));
    }

    private void checkTeamName(String teamName){
        if (StringUtils.isEmpty(teamName)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_CHECKTEAMNAME_NAMENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        // Check duplicate name
        teamName = teamName.trim();
        if (teamName.length() > 50) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_CHECKTEAMNAME_NAMETOOLONG.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
        if(this.permissionTeamRepository.existsByTenantIdAndNameAndDeleted(SecurityUtils.getTenantId(), teamName, Boolean.FALSE)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_CHECKTEAMNAME_NAMEEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(teamName),userApiPromptProperties.getUserService()));
        }
    }

    private void checkTeamLevel(PermissionTeam parentTeam){
        // Check team level which is up to 5 (start with 0).
        if (parentTeam.getLevel() >= 4){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_CHECKTEAMLEVEL_ERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
    }

    private void fillTeamCodeAndLevelForNonRootTeam(PermissionTeam permissionTeam, PermissionTeam parentTeam, Map<Long, Integer> parentToSubTeamMaxCode){
        this.checkTeamLevel(parentTeam);
        Integer level = parentTeam.getLevel() + 1;
        String code;
        if (parentTeam.getIsLeaf()) {
            // update parentTeam isLeaf = false
            permissionTeamRepository.updateLeafStatusTo(parentTeam.getId(), Boolean.FALSE);
        }
        // get max code for siblings
        String maxCode = permissionTeamRepository.findMaxCodeByParentIdAndTenantId(parentTeam.getId(), SecurityUtils.getTenantId());
        if (StringUtils.isEmpty(maxCode)){
            code = parentTeam.getCode() + "0001";
        }else {
            int maxCodeInt = Integer.parseInt(maxCode.substring(maxCode.length() - 4));
            if (Objects.nonNull(parentToSubTeamMaxCode)){
                maxCodeInt = Math.max(maxCodeInt, parentToSubTeamMaxCode.getOrDefault(parentTeam.getId(), 0));
                parentToSubTeamMaxCode.put(parentTeam.getId(), maxCodeInt + 1);
            }
            // Filling with zeroes
            code = parentTeam.getCode() + String.format("%04d", maxCodeInt + 1);
        }
        permissionTeam
                .setCode(code)
                .setLevel(level);
        if (Objects.nonNull(parentToSubTeamMaxCode)){
            permissionTeamRepository.findAllByTenantIdAndAndParentIdAndDeleted(permissionTeam.getTenantId(), permissionTeam.getId(), Boolean.FALSE).forEach(team -> {
                this.fillTeamCodeAndLevelForNonRootTeam(team, permissionTeam, parentToSubTeamMaxCode);
            });
        }
    }

    private void fillTeamCodeAndLevelForRootTeam(PermissionTeam permissionTeam){
        Integer level = 0;
        String code = null;
        // get max code for root level team
        String maxCode = permissionTeamRepository.findMaxCodeOfTopestLevels(SecurityUtils.getTenantId());
        if (StringUtils.isEmpty(maxCode)){
            code = SecurityUtils.getTenantId() + "_0001";
        }else{
            Integer maxCodeInt = Integer.parseInt(maxCode.substring(maxCode.length() - 4));
            // Filling with zeroes
            code = SecurityUtils.getTenantId() + "_" + String.format("%04d", maxCodeInt + 1);
        }
        permissionTeam
                .setCode(code)
                .setLevel(level);
    }

    @Override
    public PermissionTeamDTO update(PermissionTeamUpdateVM permissionTeamUpdateVM) {
        this.checkTenant(permissionTeamUpdateVM.getId());
        PermissionTeam permissionTeam = permissionTeamRepository.findById(permissionTeamUpdateVM.getId()).orElseThrow();
        String newTeamName = permissionTeamUpdateVM.getName().strip();
        if (!permissionTeam.getName().strip().equalsIgnoreCase(newTeamName)){
            this.checkTeamName(newTeamName);
        }
        permissionTeam.setName(newTeamName);
        permissionTeam.setTeamCategoryId(permissionTeamUpdateVM.getTeamCategoryId());
        if (Objects.nonNull(permissionTeamUpdateVM.getParentId())){
            this.checkTenant(permissionTeamUpdateVM.getParentId());
            if (!permissionTeam.getParentId().equals(permissionTeamUpdateVM.getParentId())){
                this.transferTeam(permissionTeam, permissionTeam.getParentId(), permissionTeamUpdateVM.getParentId(), true);
                permissionTeam.setParentId(permissionTeamUpdateVM.getParentId());
            }
        }else{
            // transfer to root node
            this.fillTeamCodeAndLevelForRootTeam(permissionTeam);
            permissionTeam.setParentId(TEAM_ROOT_ID);
            PermissionTeam newParentTeam = permissionTeamRepository.saveAndFlush(permissionTeam);
            PermissionTeamDTO permissionTeamDTO = teamMapper.toDto(newParentTeam);
            List<PermissionTeam> subTeams = permissionTeamRepository.findAllByTenantIdAndAndParentIdAndDeleted(SecurityUtils.getTenantId(), newParentTeam.getId(), Boolean.FALSE);
            for (PermissionTeam subTeam : subTeams) {
                this.transferTeam(subTeam, newParentTeam.getId(), newParentTeam.getId(), false);
            }
            return permissionTeamDTO;
        }
        return teamMapper.toDto(permissionTeamRepository.save(permissionTeam));
    }

    private void transferTeam(PermissionTeam currentTeam, Long preParentId, Long newParentId, Boolean exceptCurrentTeam){
        boolean existSubTeam = exceptCurrentTeam ?
                permissionTeamRepository.existsByParentIdAndIdNotAndDeleted(preParentId, currentTeam.getId(), Boolean.FALSE)
                : permissionTeamRepository.existsByParentIdAndDeleted(preParentId, Boolean.FALSE);
        if (!existSubTeam){
            permissionTeamRepository.updateLeafStatusTo(preParentId, Boolean.TRUE);
        }
        PermissionTeam newParentTeam =  permissionTeamRepository.getById(newParentId);
        // update team code and level
        this.fillTeamCodeAndLevelForNonRootTeam(currentTeam, newParentTeam, new HashMap<Long, Integer>());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer deleteAndChangeTeam(Long deleteTeamId, Long changeTeamId) {
        Long tenantId = SecurityUtils.getTenantId();
        // check if this id exists
        if (!permissionTeamRepository.existsByIdAndTenantIdAndDeleted(deleteTeamId, tenantId, Boolean.FALSE) || !permissionTeamRepository.existsByIdAndTenantIdAndDeleted(changeTeamId, tenantId, Boolean.FALSE)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_DELETE_INVALIDTEAM.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(deleteTeamId),userApiPromptProperties.getUserService()));
        }
        // check if this team has sub-teams
        if(permissionTeamRepository.existsByParentIdAndTenantIdAndDeleted(deleteTeamId, tenantId, Boolean.FALSE)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_DELETE_SUBTEAM.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        // permissionTeamRepository.updateDeletedStatusByTeamCodeLike(permissionTeam.getCode()+"%");
        Integer affectedUsers = permissionUserTeamRepository.countByTeamId(deleteTeamId);
        //判断是否还有数据
        PermissionTeamJobAndUserCountVM permissionTeamJobAndUserCountVM = permissionUserTeamService.searchJobAndUserByTeamId(deleteTeamId);
        if (permissionTeamJobAndUserCountVM != null && permissionTeamJobAndUserCountVM.getJobCount() == 0 && permissionTeamJobAndUserCountVM.getUserCount() == 0) {
            doDeleteTeam(deleteTeamId, tenantId);
            return affectedUsers;
        }
        // update user team
        List<PermissionUserTeam> addFromDeleteTeamIdPermissionUserTeamList = permissionUserTeamRepository.findAllByTeamId(deleteTeamId);
        List<PermissionUserTeam> existsPermissionUserTeamList = permissionUserTeamRepository.findAllByTeamId(changeTeamId);
        Map<Long, PermissionUserTeam> addMap = addFromDeleteTeamIdPermissionUserTeamList.stream().collect(Collectors.toMap(PermissionUserTeam::getUserId, Function.identity(), (exists, replacement) -> exists));
        Map<Long, PermissionUserTeam> existsMap = existsPermissionUserTeamList.stream().collect(Collectors.toMap(PermissionUserTeam::getUserId, Function.identity(), (exists, replacement) -> exists));
        //需要新增的关联关系
        List<PermissionUserTeam> permissionUserTeamList = new ArrayList<>();
        List<PermissionUserTeam> addPermissionUserTeamList = addMap.keySet().stream().filter(userId -> !existsMap.containsKey(userId)).map(userId -> {
            PermissionUserTeam permissionUserTeam = new PermissionUserTeam();
            permissionUserTeam.setUserId(userId);
            permissionUserTeam.setTeamId(changeTeamId);
            permissionUserTeam.setTenantId(tenantId);
            //保留原始是否主团队
            permissionUserTeam.setIsPrimary(addMap.get(userId).getIsPrimary());
            return permissionUserTeam;
        }).toList();
        //需要修改的关联关系
        List<PermissionUserTeam> updatePermissionUserTeamList = addMap.keySet().stream().filter(userId ->
                //已经存在关系且被移除的关系是组团队
                existsMap.containsKey(userId) && addMap.get(userId).getIsPrimary()).map(userId -> {
            PermissionUserTeam permissionUserTeam = existsMap.get(userId);
            permissionUserTeam.setIsPrimary(true);
            return permissionUserTeam;
        }).toList();
        permissionUserTeamList.addAll(addPermissionUserTeamList);
        permissionUserTeamList.addAll(updatePermissionUserTeamList);
        // update job pteamId
        permissionTeamRepository.updateJobPTeamIdByTeamId(deleteTeamId, changeTeamId);
        // 新增或者修改关联关系
        permissionUserTeamRepository.saveAll(permissionUserTeamList);
        // 删除
        doDeleteTeam(deleteTeamId, tenantId);
        return affectedUsers;
    }

    private void doDeleteTeam(Long deleteTeamId, Long tenantId) {
        // 删除历史关联关系
        permissionUserTeamRepository.deleteByTeamId(deleteTeamId);
        // 软删除
        permissionTeamRepository.deletePermissionTeamByTeamId(deleteTeamId);
        //查询父节点是否还有子节点
        PermissionTeam permissionTeam = permissionTeamRepository.findById(deleteTeamId).orElseGet(null);
        if (permissionTeam != null && !permissionTeamRepository.existsByParentIdAndTenantIdAndDeleted(permissionTeam.getParentId(), tenantId, Boolean.FALSE)) {
            permissionTeamRepository.updateLeafStatusTo(permissionTeam.getParentId(), Boolean.TRUE);
        }
    }

    @Override
    public List<PermissionTeamTreeDTO> getTeamTreeWithLeaders() throws ExecutionException, InterruptedException{
        Long tenantId = SecurityUtils.getTenantId();
        CompletableFuture<List<PermissionTeam>> teamsCompletableFuture = CompletableFuture.supplyAsync(() -> permissionTeamRepository.findByTenantIdAndDeleted(tenantId,
                Boolean.FALSE, Sort.by(Sort.Order.desc("level"), Sort.Order.asc("id"))));

        CompletableFuture<Map<Long, JSONArray>> teamLeadersCompletableFuture = CompletableFuture.supplyAsync(() ->
                permissionTeamLeaderRepository.findAllGroupByTeamId(tenantId).stream().collect(Collectors.toMap(teamLeaders -> Long.valueOf(teamLeaders[0].toString()), teamLeaders -> JSONArray.parseArray(teamLeaders[1].toString()))));

        List<PermissionTeam> permissionTeams = teamsCompletableFuture.get();
        Map<Long, JSONArray> teamToLeaderIds = teamLeadersCompletableFuture.get();
        Map<Long, List<PermissionTeamTreeDTO>> parentToChildren = new TreeMap<>();
        permissionTeams.forEach(team -> {
            PermissionTeamTreeDTO permissionTeamTreeDTO = new PermissionTeamTreeDTO();
            BeanUtils.copyProperties(team, permissionTeamTreeDTO);
            //permissionTeamTreeDTO.setParentId(team.getParentId());
            permissionTeamTreeDTO.setLeaderUserIds(teamToLeaderIds.getOrDefault(team.getId(), null));
            generateTree(parentToChildren, permissionTeamTreeDTO);
        });
        return parentToChildren.getOrDefault(-1L, new ArrayList<>());
    }

    @Override
    public List<PermissionTeamTreeDTO> getPlainTeamTree() {
        List<PermissionTeam> permissionTeams = permissionTeamRepository.findByTenantIdAndDeleted(SecurityUtils.getTenantId(),
                Boolean.FALSE, Sort.by(Sort.Order.desc("level"), Sort.Order.asc("id")));
        Map<Long, List<PermissionTeamTreeDTO>> parentToChildren = new TreeMap<>();
        permissionTeams.forEach(team -> {
            PermissionTeamTreeDTO permissionTeamTreeDTO = new PermissionTeamTreeDTO();
            BeanUtils.copyProperties(team, permissionTeamTreeDTO);
            permissionTeamTreeDTO.setTeamCategoryEnable(permissionTeamTreeDTO.getTeamCategoryId() != null && teamCategoryIdSet.contains(permissionTeamTreeDTO.getTeamCategoryId()));
            generateTree(parentToChildren, permissionTeamTreeDTO);
        });
        return parentToChildren.getOrDefault(-1L, new ArrayList<>());
    }

//    @Override
//    public List<PermissionTeamTreeDTO> getTeamTreeWithPermission() throws ExecutionException, InterruptedException {
//        Long tenantId = SecurityUtils.getTenantId();
//        CompletableFuture<List<PermissionTeam>> teamsCompletableFuture = CompletableFuture.supplyAsync(() -> permissionTeamRepository.findByTenantIdAndDeleted(tenantId,
//                Boolean.FALSE, Sort.by(Sort.Order.desc("level"), Sort.Order.asc("id"))));
//
//        Long userId = SecurityUtils.getUserId();
//        Authentication authentication = SecurityUtils.getAuthentication();
//        CompletableFuture<TeamDataPermissionRespDTO> teamDataPermissionCompletableFuture = CompletableFuture.supplyAsync(() -> {
//            SecurityUtils.setAuthentication(authentication);
//            return initiationService.initiateDataPermissionByUserId(SecurityUtils.getTenantId(), userId).getBody();
//        });
//        TeamDataPermissionRespDTO teamDataPermission = teamDataPermissionCompletableFuture.get();
//        List<PermissionTeam> permissionTeams = teamsCompletableFuture.get();
//        Map<Long, List<PermissionTeamTreeDTO>> parentToChildren = new TreeMap<>();
//        final TeamDataPermissionRespDTO finalTeamDataPermission = teamDataPermission;
//        log.info("NestedTeamIds: " + finalTeamDataPermission.getNestedTeamIds().toString());
//        permissionTeams.forEach(team -> {
//            boolean hasDataPermission = BooleanUtils.isTrue(finalTeamDataPermission.getAll() || finalTeamDataPermission.getNestedTeamIds().contains(team.getId()));
//            boolean hasChildren = CollectionUtils.isNotEmpty(parentToChildren.getOrDefault(team.getId(), null));
//            if (hasDataPermission || hasChildren){
//                PermissionTeamTreeDTO permissionTeamTreeDTO = new PermissionTeamTreeDTO();
//                BeanUtils.copyProperties(team, permissionTeamTreeDTO);
//                permissionTeamTreeDTO.setHasPermission(hasDataPermission);
//                generateTree(parentToChildren, permissionTeamTreeDTO);
//            }
//        });
//        return parentToChildren.getOrDefault(-1L, new ArrayList<>());
//    }

    @Override
    public List<PermissionTeamTreeDTO> getTeamTreeWithPermissionByType(Module module) throws ExecutionException, InterruptedException {
        Long tenantId = SecurityUtils.getTenantId();
        CompletableFuture<List<PermissionTeam>> teamsCompletableFuture = CompletableFuture.supplyAsync(() -> permissionTeamRepository.findByTenantIdAndDeleted(tenantId,
                Boolean.FALSE, Sort.by(Sort.Order.desc("level"), Sort.Order.asc("id"))));

        Long userId = SecurityUtils.getUserId();
        Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<TeamDataPermissionRespDTO> teamDataPermissionCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return switch (module) {
                case JOB -> initiationService.initiateDataPermissionByUserId(tenantId, userId).getBody();
                case CLIENT_CONTACT -> initiationService.initiateClientContactDataPermissionByUserId(tenantId, userId).getBody();
                case REPORT -> initiationService.initiateReportDataPermissionByUserId(tenantId, userId).getBody();
                case HOME_AND_CALENDAR -> initiationService.initiateHomeAndCalendarDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
                case CANDIDATE_PIPELINE_MANAGEMENT -> initiationService.initiateCandidatePipelineManagementDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
                case CHINA_INVOICING -> initiationService.initiateChinaInvoicingDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();

            };
        });
        TeamDataPermissionRespDTO teamDataPermission = teamDataPermissionCompletableFuture.get();
        List<PermissionTeam> permissionTeams = teamsCompletableFuture.get();
        Map<Long, List<PermissionTeamTreeDTO>> parentToChildren = new TreeMap<>();
        final TeamDataPermissionRespDTO finalTeamDataPermission = teamDataPermission;
        log.info("NestedTeamIds: " + finalTeamDataPermission.getNestedTeamIds().toString());
        permissionTeams.forEach(team -> {
            boolean hasDataPermission = BooleanUtils.isTrue(finalTeamDataPermission.getAll() || finalTeamDataPermission.getNestedTeamIds().contains(team.getId()));
            boolean hasChildren = CollectionUtils.isNotEmpty(parentToChildren.getOrDefault(team.getId(), null));
            if (hasDataPermission || hasChildren){
                PermissionTeamTreeDTO permissionTeamTreeDTO = new PermissionTeamTreeDTO();
                BeanUtils.copyProperties(team, permissionTeamTreeDTO);
                permissionTeamTreeDTO.setHasPermission(hasDataPermission);
                permissionTeamTreeDTO.setTeamCategoryEnable(permissionTeamTreeDTO.getTeamCategoryId() != null && teamCategoryIdSet.contains(permissionTeamTreeDTO.getTeamCategoryId()));
                generateTree(parentToChildren, permissionTeamTreeDTO);
            }
        });
        return parentToChildren.getOrDefault(-1L, new ArrayList<>());
    }

    @Override
    public List<PermissionTeamTreeDTO> getTeamTreeWithPermissionUserByType(Module module, Boolean isPrimary) throws ExecutionException, InterruptedException {
        Long tenantId = SecurityUtils.getTenantId();
        Long userId = SecurityUtils.getUserId();

        //查询权限控制
        Authentication authentication = SecurityUtils.getAuthentication();
        CompletableFuture<TeamDataPermissionRespDTO> teamDataPermissionCompletableFuture = CompletableFuture.supplyAsync(() -> {
            SecurityUtils.setAuthentication(authentication);
            return switch (module) {
                case JOB -> initiationService.initiateDataPermissionByUserId(tenantId, userId).getBody();
                case CLIENT_CONTACT -> initiationService.initiateClientContactDataPermissionByUserId(tenantId, userId).getBody();
                case REPORT -> initiationService.initiateReportDataPermissionByUserId(tenantId, userId).getBody();
                case HOME_AND_CALENDAR -> initiationService.initiateHomeAndCalendarDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
                case CANDIDATE_PIPELINE_MANAGEMENT -> initiationService.initiateCandidatePipelineManagementDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
                case CHINA_INVOICING -> initiationService.initiateChinaInvoicingDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
            };
        });
        CompletableFuture<List<PermissionTeam>> teamsCompletableFuture = CompletableFuture.supplyAsync(() -> permissionTeamRepository.findByTenantIdAndDeleted(tenantId,
                Boolean.FALSE, Sort.by(Sort.Order.desc("level"), Sort.Order.asc("id"))));

        CompletableFuture<List<PermissionTeamUserDTO>> userFuture = CompletableFuture.supplyAsync(() -> userService.getAllPermissionUsers(tenantId));

        //查询 对应的user 数据
        final TeamDataPermissionRespDTO teamDataPermission = SecurityUtils.isAdmin() ?
                createAdminPermission() : teamDataPermissionCompletableFuture.get();

        List<PermissionTeamUserDTO> userDTOList = userFuture.get();
        List<PermissionTeam> permissionTeams = teamsCompletableFuture.get();

        Map<Long, List<PermissionTeamUserDTO>> teamUserListMap = userDTOList.stream()
                .filter(user -> teamDataPermission.getAll() || teamDataPermission.getNestedTeamIds().contains(user.getTeamId()) || Objects.equals(user.getId(), userId))
                .filter(user -> isPrimary? user.getIsPrimaryTeam(): true).collect(Collectors.groupingBy(PermissionTeamUserDTO::getTeamId));

        List<Long> allSeftTeamList;
        if (teamDataPermission.getSelf()) {
            List<PermissionTeamUserDTO> permissionTeamUserDTOList = userRepository.findUsersWithoutRoleByUserIdIn(Set.of(userId));
            allSeftTeamList = permissionTeamRepository.getAncestorTeams(permissionTeamUserDTOList.stream().map(PermissionTeamUserDTO::getTeamId).toList()).stream().map(PermissionTeam::getId).toList();
        } else {
            allSeftTeamList = new ArrayList<>();
        }

        Map<Long, List<PermissionTeamTreeDTO>> parentToChildren = new TreeMap<>();
        final TeamDataPermissionRespDTO finalTeamDataPermission = teamDataPermission;
        log.info("NestedTeamIds: " + finalTeamDataPermission.getNestedTeamIds().toString());
        permissionTeams.forEach(team -> {
            boolean hasDataPermission = BooleanUtils.isTrue(finalTeamDataPermission.getAll() || finalTeamDataPermission.getNestedTeamIds().contains(team.getId()));
            boolean hasChildren = CollectionUtils.isNotEmpty(parentToChildren.getOrDefault(team.getId(), null));
            boolean onlySelf = teamDataPermission.getSelf() && allSeftTeamList.contains(team.getId());
            if (hasDataPermission || hasChildren || onlySelf) {
                PermissionTeamTreeDTO permissionTeamTreeDTO = new PermissionTeamTreeDTO();
                BeanUtils.copyProperties(team, permissionTeamTreeDTO);
                permissionTeamTreeDTO.setHasPermission(hasDataPermission);
                permissionTeamTreeDTO.setData(teamUserListMap.getOrDefault(team.getId(), new ArrayList<>()));
                generateTree(parentToChildren, permissionTeamTreeDTO);
            }
        });
        return parentToChildren.getOrDefault(-1L, new ArrayList<>());
    }

    private TeamDataPermissionRespDTO createAdminPermission() {
        TeamDataPermissionRespDTO dto = new TeamDataPermissionRespDTO();
        dto.setAll(true);
        return dto;
    }

    @Override
    public Map<Long, String> getPlainTeamsWithNameAndId() {
        Long tenantId = SecurityUtils.getTenantId();
        List<PermissionTeam> permissionTeams = permissionTeamRepository.findByTenantIdAndDeleted(tenantId,
                Boolean.FALSE, Sort.by(Sort.Order.desc("level"), Sort.Order.asc("id")));

        return permissionTeams.stream()
                .collect(Collectors.toMap(
                        PermissionTeam::getId,    // key 映射函数
                        PermissionTeam::getName   // value 映射函数
                ));
    }

    private void generateTree(Map<Long, List<PermissionTeamTreeDTO>> parentToChildren, PermissionTeamTreeDTO permissionTeamTreeDTO) {
        if (!parentToChildren.containsKey(permissionTeamTreeDTO.getParentId())){
            parentToChildren.put(permissionTeamTreeDTO.getParentId(), new ArrayList<>());
        }
        parentToChildren.get(permissionTeamTreeDTO.getParentId()).add(permissionTeamTreeDTO);
        if (parentToChildren.containsKey(permissionTeamTreeDTO.getId())){
            permissionTeamTreeDTO.setChildren(parentToChildren.get(permissionTeamTreeDTO.getId()));
        }
    }


    private void checkTenant(Long teamId){
        if (Objects.isNull(teamId)){
            return;
        }
        if (teamId < 1){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_DELETE_INVALIDTEAM.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(teamId),userApiPromptProperties.getUserService()));
        }
        if(!permissionTeamRepository.existsByIdAndTenantIdAndDeleted(teamId, SecurityUtils.getTenantId(), Boolean.FALSE)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_DELETE_INVALIDTEAM.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(teamId),userApiPromptProperties.getUserService()));
        }
    }

    @Override
    public void addTeamLeader(Long teamId, Long leaderUserId) {
        this.checkTeam(teamId);
        this.checkUser(leaderUserId);
        if (!permissionTeamLeaderRepository.existsByTeamIdAndUserId(teamId, leaderUserId)) {
            PermissionTeamLeader permissionTeamLeader = new PermissionTeamLeader().setTeamId(teamId).setUserId(leaderUserId).setTenantId(SecurityUtils.getTenantId());
            permissionTeamLeaderRepository.save(permissionTeamLeader);
        }
    }

    private void checkTeam(Long teamId) {
        if (!permissionTeamRepository.existsByIdAndTenantIdAndDeleted(teamId, SecurityUtils.getTenantId(), Boolean.FALSE)){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTEAM_DELETE_INVALIDTEAM.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(teamId),userApiPromptProperties.getUserService()));
        }
    }

    private void checkUser(Long userId) {
        if (!userRepository.existsByIdAndTenantId(userId, SecurityUtils.getTenantId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONROLE_FINDROLESBYUSERID_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),Arrays.asList(userId),userApiPromptProperties.getUserService()));
        }
    }

    @Override
    public void deleteTeamLeader(Long teamId, Long leaderId) {
        this.checkTeam(teamId);
        this.checkUser(leaderId);
        Optional<PermissionTeamLeader> teamLeader = permissionTeamLeaderRepository.findByTeamIdAndUserId(teamId, leaderId);
        if (teamLeader.isPresent()) {
            permissionTeamLeaderRepository.delete(teamLeader.get());
        }
    }

    @Override
    public Integer countActiveUsersByTeamId(Long teamId) {
        Set<Long> teamIds = permissionTeamRepository.findTeamIdAndSubTeamIdsByTeamId(teamId);
        return permissionUserTeamRepository.countByTeamIdIn(teamIds);
    }

    @Override
    public List<PermissionTeamTreeDTO> getSelfSubTeamsWithLeaders() throws ExecutionException, InterruptedException {
        Map<Long, List<PermissionTeamTreeDTO>> parentToChildren = new TreeMap<>();
        Map<Long, PermissionTeamTreeDTO> idToNode = new HashMap<>();
        if(SecurityUtils.getTeamId() == null) {
            return new ArrayList<>();
        }
        Set<Long> teamIdAndSubTeamIdsByTeamId = permissionTeamRepository.findTeamIdAndSubTeamIdsByTeamId(SecurityUtils.getTeamId());
        List<PermissionTeam> permissionTeams = permissionTeamRepository.findAllById(teamIdAndSubTeamIdsByTeamId).stream().filter(p -> Boolean.FALSE.equals(p.getDeleted())).collect(Collectors.toList());
        List<Object[]> teamLeaderList = permissionTeamLeaderRepository.findAllGroupByTeamId(SecurityUtils.getTenantId());
        Map<Long, JSONArray> teamToLeaderIds = teamLeaderList.stream().collect(Collectors.toMap(teamLeaders -> Long.valueOf(teamLeaders[0].toString()), teamLeaders -> JSONArray.parseArray(teamLeaders[1].toString())));
        permissionTeams.forEach(team -> {
            PermissionTeamTreeDTO permissionTeamTreeDTO = new PermissionTeamTreeDTO();
            BeanUtils.copyProperties(team, permissionTeamTreeDTO);
            permissionTeamTreeDTO.setLeaderUserIds(teamToLeaderIds.getOrDefault(team.getId(), null));
            // First pass: create all nodes and record parent-child relationships
            idToNode.put(permissionTeamTreeDTO.getId(), permissionTeamTreeDTO);
            if (!parentToChildren.containsKey(permissionTeamTreeDTO.getParentId())){
                parentToChildren.put(permissionTeamTreeDTO.getParentId(), new ArrayList<>());
            }
            parentToChildren.get(permissionTeamTreeDTO.getParentId()).add(permissionTeamTreeDTO);
        });
        // Second pass: assign children to their respective parents
        idToNode.values().forEach(node -> {
            if (parentToChildren.containsKey(node.getId())) {
                node.setChildren(parentToChildren.get(node.getId()));
            }
        });
        PermissionTeam permissionTeam = permissionTeams.stream().filter(p -> p.getId().equals(SecurityUtils.getTeamId())).findFirst().get();
        return parentToChildren.getOrDefault(permissionTeam.getParentId(), new ArrayList<>());
    }

}
