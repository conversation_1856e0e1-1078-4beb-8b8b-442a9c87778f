package com.altomni.apn.user.web.rest.vm.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor

public class ParentTeamLeaderVM implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long teamId;

    private Long leaderTeamId;

    private Long leaderId;

    private String firstName;

    private String lastName;

    private String fullName;

    public ParentTeamLeaderVM(Long teamId, Long leaderTeamId, Long leaderId, String firstName, String lastName) {
        this.teamId = teamId;
        this.leaderTeamId = leaderTeamId;
        this.leaderId = leaderId;
        this.firstName = firstName;
        this.lastName = lastName;
    }
}
