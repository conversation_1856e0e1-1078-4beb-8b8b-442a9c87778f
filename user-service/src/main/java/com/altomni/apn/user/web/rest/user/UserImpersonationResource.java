package com.altomni.apn.user.web.rest.user;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.service.dto.user.UserImpersonationDTO;
import com.altomni.apn.user.service.user.UserImpersonationService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(hidden = true, tags = {"UserImpersonation"})
@Slf4j
@RestController
@RequestMapping("/api/v3/impersonation")
public class UserImpersonationResource {

    @Resource
    private UserImpersonationService userImpersonationService;

    @PostMapping("/mine")
    @NoRepeatSubmit
    public ResponseEntity<UserImpersonationDTO> grantToUser(@RequestBody UserImpersonationDTO userImpersonationDTO){
        log.info("[Impersonation @{}] REST request to grant impersonation login: {}", SecurityUtils.getUserId(), userImpersonationDTO);
        return ResponseEntity.ok(userImpersonationService.grantToUser(userImpersonationDTO));
    }

    @GetMapping("/mine")
    public ResponseEntity<List<UserImpersonationDTO>> getMyImpersonation(){
        log.info("[Impersonation @{}] REST request to get my impersonation login: {}", SecurityUtils.getUserId());
        return ResponseEntity.ok(userImpersonationService.getMyImpersonation());
    }

    @DeleteMapping("/mine/{id}")
    @NoRepeatSubmit
    public ResponseEntity<UserImpersonationDTO> revokeMyImpersonation(@PathVariable Long id){
        log.info("[Impersonation @{}] REST request to revoke impersonation login: {}", SecurityUtils.getUserId(), id);
        return ResponseEntity.ok(userImpersonationService.revokeMyImpersonation(id));
    }

    @PostMapping("")
    @NoRepeatSubmit
    public ResponseEntity<UserImpersonationDTO> grantFromTo(@RequestBody UserImpersonationDTO userImpersonationDTO){
        log.info("[Impersonation @{}] REST request to grant impersonation login by admin: {}", SecurityUtils.getUserId(), userImpersonationDTO);
        return ResponseEntity.ok(userImpersonationService.grantFromTo(userImpersonationDTO));
    }

    @GetMapping("")
    public ResponseEntity<List<UserImpersonationDTO>> getAllImpersonation(){
        log.info("[Impersonation @{}] REST request to get all impersonation login", SecurityUtils.getUserId());
        return ResponseEntity.ok(userImpersonationService.getAllImpersonation());
    }

    @GetMapping("/grant-from/{grantFromUserId}/grant-to")
    public ResponseEntity<List<UserImpersonationDTO>> getGrantToImpersonationUsersByGrantFromUser(@PathVariable("grantFromUserId") Long grantFromUserId){
        log.info("[Impersonation @{}] REST request to get grant-to impersonation by grant-from : {}", SecurityUtils.getUserId(), grantFromUserId);
        return ResponseEntity.ok(userImpersonationService.getGrantToImpersonationUsersByGrantFromUser(grantFromUserId));
    }

    @DeleteMapping("/{id}")
    @NoRepeatSubmit
    public ResponseEntity<UserImpersonationDTO> revokeImpersonation(@PathVariable("id") Long id){
        log.info("[Impersonation @{}] REST request to revoke impersonation login by id {}", SecurityUtils.getUserId(), id);
        return ResponseEntity.ok(userImpersonationService.revokeImpersonation(id));
    }
}