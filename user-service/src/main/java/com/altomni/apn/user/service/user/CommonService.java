package com.altomni.apn.user.service.user;

import java.util.List;
import java.util.Map;

/**
 * Common service. provide some common logic on this service
 */
public interface CommonService {

    Integer getUserCreditLimit(Long tenantId);

    Integer getLimitUserCreditLimit(Long tenantId);

    String getAllApnParamValue(String paramKey);

    Integer getTenantParamValue(String paramKey);

    Integer getTenantParamValueByTenantId(String paramKey, Long tenantId);

    List<Map<Long, String>> getTenantParamList(String paramKey);

    String getParamById(Long id);

    String getTenantParamStringValue(String paramKey);
}
