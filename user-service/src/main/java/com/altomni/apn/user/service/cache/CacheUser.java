package com.altomni.apn.user.service.cache;

import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class CacheUser {

    @CacheEvict(cacheNames = {"BriefUsers"}, key = "'tenant:' + #tenantId")
    public void deleteBriefUsersByTenantId(Long tenantId){

    }

    @CacheEvict(cacheNames = {"PermissionUsers"}, key = "'tenant:' + #tenantId")
    public void deletePermissionUsersByTenantId(Long tenantId){

    }

}
