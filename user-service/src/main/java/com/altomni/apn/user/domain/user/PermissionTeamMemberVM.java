package com.altomni.apn.user.domain.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Entity;
import javax.persistence.Id;
import java.io.Serializable;
import java.time.Instant;

@Entity
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PermissionTeamMemberVM implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private Long id;

    private String firstName;

    private String lastName;

    private Boolean activated;

    private String email;

    private Long levelOfExperience;

    private Instant createdDate;

    private String jobTitle;

    private Long primaryTeamId;

    private String noPrimaryTeamIds;

    private String primaryTeamName;

    private String teamLeadIds;

    private String primaryTeamLeadIds;

    private String superiorPrimaryTeamLeaderIds;





}
