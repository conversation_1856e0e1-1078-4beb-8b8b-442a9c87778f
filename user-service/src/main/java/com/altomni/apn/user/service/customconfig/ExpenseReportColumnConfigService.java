package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.service.dto.customconfig.ExpenseReportColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.ExpenseReportColumnConfigVO;

public interface ExpenseReportColumnConfigService {

    ExpenseReportColumnConfigVO searchExpenseReportColumnConfigByUserId(Long userId);

    void updateExpenseReportColumnConfigByUserId(Long userId, ExpenseReportColumnConfigDTO dto);

}
