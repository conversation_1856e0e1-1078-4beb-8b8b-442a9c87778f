package com.altomni.apn.user.service.user.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.user.UserImpersonation;
import com.altomni.apn.user.repository.user.UserImpersonationRepository;
import com.altomni.apn.user.service.dto.user.UserImpersonationDTO;
import com.altomni.apn.user.service.user.UserImpersonationService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@Transactional
public class UserImpersonationServiceImpl implements UserImpersonationService {

    @Resource
    private UserImpersonationRepository userImpersonationRepository;

    @Resource
    private CommonRedisService commonRedisService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    private void verifyEffectiveTime(UserImpersonationDTO userImpersonationDTO) {
        if (Objects.isNull(userImpersonationDTO.getEffectiveStartAt())){
            userImpersonationDTO.setEffectiveStartAt(Instant.now());
        }
        if (userImpersonationDTO.getEffectiveStartAt().isBefore(Instant.now().minus(10, ChronoUnit.MINUTES))){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VERIFYEFFECTIVETIME_TIMEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
    }

    private void verifyExpireTime(UserImpersonationDTO userImpersonationDTO){
        if (Objects.isNull(userImpersonationDTO.getExpireAt())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VERIFYEXPIRETIME_EXPIRETIMENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if (userImpersonationDTO.getExpireAt()
                .isBefore(userImpersonationDTO.getEffectiveStartAt())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VERIFYEXPIRETIME_EXPIRETIMEAFTEREFFECTIVETIME.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if (userImpersonationDTO.getExpireAt()
                .isAfter(userImpersonationDTO.getEffectiveStartAt().plus(7, ChronoUnit.DAYS))){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VERIFYEXPIRETIME_EXPIRETIMEERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
    }

    private void deleteImpersonationLoginToken(Long userIdFrom, Long userIdTo) {
        String keyPattern = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, userIdFrom, userIdTo, "*");
        commonRedisService.deleteByKeyPattern(keyPattern);
    }

    private void resetTokenExpiration(Long userIdFrom, Long userIdTo, Instant expireAt) {
        String keyPattern = String.format(RedisConstants.DATA_KEY_ACTIVE_USER, userIdFrom, userIdTo, "*");
        final Long expireSeconds = Duration.between(Instant.now(), expireAt).getSeconds();
        commonRedisService.softExpireByKeyPattern(keyPattern, expireSeconds.intValue());
    }

    @Override
    public UserImpersonationDTO grantToUser(UserImpersonationDTO userImpersonationDTO) {
        return this.grantFromTo(userImpersonationDTO.setGrantFromUser(new UserImpersonationDTO.UserDTO(SecurityUtils.getUserId())));
    }

    @Override
    public List<UserImpersonationDTO> getMyImpersonation() {
        return this.getGrantToImpersonationUsersByGrantFromUser(SecurityUtils.getUserId());
    }

    @Override
    public UserImpersonationDTO revokeMyImpersonation(Long id) {
        if (Boolean.FALSE.equals(userImpersonationRepository.existsByIdAndGrantFromUserIdAndExpireAtAfter(id, SecurityUtils.getUserId(), Instant.now()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_REVOKEMYIMPERSONATION_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        final UserImpersonation userImpersonation = userImpersonationRepository.findById(id).get();
        userImpersonation.setExpireAt(Instant.now());
        userImpersonationRepository.save(userImpersonation);
        this.deleteImpersonationLoginToken(userImpersonation.getGrantFromUserId(), userImpersonation.getGrantToUserId());
        return userImpersonationRepository.findOneById(id);
    }

    @Override
    public UserImpersonationDTO grantFromTo(UserImpersonationDTO userImpersonationDTO) {
        this.verifyEffectiveTime(userImpersonationDTO);
        this.verifyExpireTime(userImpersonationDTO);
        this.revokeActiveGrants(userImpersonationDTO);
        userImpersonationDTO.setId(null);
        UserImpersonation userImpersonation = new UserImpersonation()
                .setGrantFromUserId(userImpersonationDTO.getGrantFromUser().getId())
                .setGrantToUserId(userImpersonationDTO.getGrantToUser().getId())
                .setTenantId(SecurityUtils.getTenantId())
                .setEffectiveStartAt(userImpersonationDTO.getEffectiveStartAt())
                .setExpireAt(userImpersonationDTO.getExpireAt());
        userImpersonation = userImpersonationRepository.save(userImpersonation);
        this.resetTokenExpiration(userImpersonation.getGrantFromUserId(), userImpersonation.getGrantToUserId(), userImpersonation.getExpireAt());
        return userImpersonationRepository.findOneById(userImpersonation.getId());
    }

    private void revokeActiveGrants(UserImpersonationDTO userImpersonationDTO) {
        userImpersonationRepository.expireAllActiveImpersonationByGrantFromUserIdAndGrantToUserIdAndTenantId(
                userImpersonationDTO.getGrantFromUser().getId(),
                userImpersonationDTO.getGrantToUser().getId(),
                SecurityUtils.getTenantId(),
                Instant.now()
        );
    }

    @Override
    public List<UserImpersonationDTO> getAllImpersonation() {
        return userImpersonationRepository.findAllByTenantId(SecurityUtils.getTenantId());
    }

    @Override
    public List<UserImpersonationDTO> getGrantToImpersonationUsersByGrantFromUser(Long grantFromUserId) {
        return userImpersonationRepository.findAllByGrantFromUserIdAndTenantId(grantFromUserId, SecurityUtils.getTenantId());
    }

    @Override
    public UserImpersonationDTO revokeImpersonation(Long id) {
        if (Boolean.FALSE.equals(SecurityUtils.isAdmin())
                && Boolean.FALSE.equals(SecurityUtils.isSystemAdmin())
                && Boolean.FALSE.equals(userImpersonationRepository
                    .existsByIdAndTenantIdAndExpireAtAfter(id, SecurityUtils.getTenantId(), Instant.now()))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_REVOKEMYIMPERSONATION_NOTFIND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        final UserImpersonation userImpersonation = userImpersonationRepository.findById(id).get();
        userImpersonationRepository.save(userImpersonation.setExpireAt(Instant.now()));
        this.deleteImpersonationLoginToken(userImpersonation.getGrantFromUserId(), userImpersonation.getGrantToUserId());
        return userImpersonationRepository.findOneById(id);
    }
}