package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.SocialConsumer;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface SocialConsumerRepository extends JpaRepository<SocialConsumer, Long> {

    Optional<SocialConsumer> findByProviderAndIdInProvider(String provider, String idInProvider);

}
