package com.altomni.apn.user.web.rest.user;


import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.UserPersonalizationConfig;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.domain.enumeration.CustomerType;
import com.altomni.apn.user.service.customconfig.*;
import com.altomni.apn.user.service.dto.customconfig.*;
import com.altomni.apn.user.service.vo.customconfig.*;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * REST controller for managing Custom Form.
 */
@Api(tags = {"User", "ATS-User"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class CustomConfigResouce {
    private static final String ENTITY_NAME = "Custom Config";

    @Resource
    private UserPreferenceService userPreferenceService;
    @Resource
    private CompanyColumnConfigService companyColumnConfigService;

    @Resource
    private JobFormConfigService jobFormConfigService;

    @Resource
    private JobColumnConfigService jobColumnConfigService;
    @Resource
    private TalentColumnConfigService talentColumnConfigService;
    @Resource
    private TalentFormConfigService talentFormConfigService;
    @Resource
    private PipelineColumnPreferenceService pipelineColumnPreferenceService;

    @Resource
    private PipelineColumnConfigService pipelineColumnConfigService;

    @Resource
    private TenantConfigService tenantConfigService;

    @Resource
    private DashboardColumnConfigService dashboardColumnConfigService;

    @Resource
    private InvoiceColumnConfigService invoiceColumnConfigService;

    @Resource
    private ExpenseReportColumnConfigService expenseReportColumnConfigService;

    @Resource
    private CompanyCurrentContractorColumnConfigService companyCurrentContractorColumnConfigService;

    @Resource
    private CompanyFteBdReportColumnConfigService companyFteBdReportColumnConfigService;

    @Resource
    private UserLabelReportColumnConfigService userLabelReportColumnConfigService;

    @Resource
    private SystemConfigDefaultService systemConfigDefaultService;

    @Resource
    InvoicingColumnConfigService invoicingChinaAreaService;

    @Resource
    CalendarColumnConfigService calendarColumnConfigService;

    @Resource
    ReportGSeriesColumnConfigService reportGSeriesColumnConfigService;

    @Resource
    ReportE1ReportFilterPreferenceService reportE1ReportFilterPreferenceService;


    /**
     * GET  /recruitment-processes/{recruitmentProcessId}/config/job-form: get form config by recruitment process id
     *
     * @param recruitmentProcessId the id of ConfigForm
     * @return the ResponseEntity with status 200 (OK) and the list of formConfig in body
     */
    @ApiOperation(value = "Get a job form config for a recruitment process", response = BaseConfig.class, responseContainer = "List")
    @GetMapping("/recruitment-processes/{recruitmentProcessId}/config/job-form")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> getJobFormConfig(@ApiParam(value = "recruitment process id", required = true) @PathVariable Long recruitmentProcessId) {
        log.info("[APN: CustomForm @{}] REST request to get a job form config {}", SecurityUtils.getUserId(), recruitmentProcessId);
        return ResponseEntity.ok()
                .body(jobFormConfigService.getFormConfigByRecruitmentProcessId(recruitmentProcessId));
    }

    @ApiOperation(value = "Get a private job form config for a recruitment process", response = BaseConfig.class, responseContainer = "List")
    @GetMapping("/recruitment-processes/{recruitmentProcessId}/private-job/config/job-form")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> getPrivateJobFormConfig(@ApiParam(value = "recruitment process id", required = true) @PathVariable Long recruitmentProcessId) {
        log.info("[APN: CustomForm @{}] REST request to get a private job form config {}", SecurityUtils.getUserId(), recruitmentProcessId);
        return ResponseEntity.ok()
                .body(jobFormConfigService.getFormConfigByRecruitmentProcessIdForPrivateJob(recruitmentProcessId));
    }


    /**
     * PUT  /custom-form/:id : Updates an existing Job Form config.
     *
     * @param recruitmentProcessId id of the JobFormConfig to update
     * @param jobFormConfigDTO     JobFormConfig to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated JobFormConfig,
     * or with status 400 (Bad Request) if the JobFormConfig is not valid,
     * or with status 500 (Internal Server Error) if the jobNote couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a Job Form Config", notes = "recruitment process id is needed.", response = JobFormConfigDTO.class)
    @PutMapping("/recruitment-processes/{recruitmentProcessId}/config/job-form")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<JobFormConfigDTO> updateJobCustomForm(@PathVariable Long recruitmentProcessId, @RequestBody JobFormConfigDTO jobFormConfigDTO) throws URISyntaxException {
        log.info("[APN: CustomForm @{}] REST request to update job form config: {}", SecurityUtils.getUserId(), jobFormConfigDTO);
        JobFormConfigDTO result = jobFormConfigService.saveJobFormConfig(jobFormConfigDTO, recruitmentProcessId);
        return ResponseEntity.ok()
                .body(result);
    }

    @ApiOperation(value = "Update a private Job Form Config", notes = "recruitment process id is needed.", response = JobFormConfigDTO.class)
    @PutMapping("/recruitment-processes/{recruitmentProcessId}/private-job/config/job-form")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<JobFormConfigDTO> updatePrivateJobCustomForm(@PathVariable Long recruitmentProcessId, @RequestBody JobFormConfigDTO jobFormConfigDTO) throws URISyntaxException {
        log.info("[APN: CustomForm @{}] REST request to update job form config: {}", SecurityUtils.getUserId(), jobFormConfigDTO);
        JobFormConfigDTO result = jobFormConfigService.savePrivateJobFormConfig(jobFormConfigDTO, recruitmentProcessId);
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * GET  recruitment-processes/{recruitmentProcessId}/config/job-form: get one job column form for a specific user
     *
     * @return the ResponseEntity with status 200 (OK) and the job column config in body
     */
    @ApiOperation(value = "Get all job column config", response = BaseConfig.class, responseContainer = "List")
    @GetMapping("/jobs/preferences/JOB_COLUMN")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> getJobColumnConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get  user column column", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(jobColumnConfigService.getColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    @ApiOperation(value = "Get all private job column config", response = BaseConfig.class, responseContainer = "List")
    @GetMapping("/jobs/preferences/PRIVATE_JOB_COLUMN")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> getPrivateJobColumnConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get  private job column ", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(jobColumnConfigService.getPrivateJobColumnConfigByUserId(SecurityUtils.getUserId()));
    }

    @GetMapping("/jobs/preferences/COMPANY_JOB_COLUMN")
    public ResponseEntity<BaseConfig> getCompanyJobColumnConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get  company job column ", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(jobColumnConfigService.getCompanyJobColumnConfigByUserId(SecurityUtils.getUserId()));
    }

    @PutMapping("/jobs/preferences/COMPANY_JOB_COLUMN")
    public ResponseEntity<JobColumnConfigDTO> updateCompanyJobColumnConfig(@RequestBody JobColumnConfigDTO jobColumnConfigDTO) {
        log.info("[APN: CustomColumn @{}] REST request to update company job column config: {}", SecurityUtils.getUserId(), jobColumnConfigDTO);
        JobColumnConfigDTO result = jobColumnConfigService.saveCompanyJobColumnConfig(jobColumnConfigDTO, SecurityUtils.getUserId());
        return ResponseEntity.ok(result);
    }

    /**
     * PUT  /custom-column/:id : Updates an existing job column config.
     *
     * @param jobColumnConfigDTO to JobColumnConfig update
     * @return the ResponseEntity with status 200 (OK) and with body the updated columnConfig,
     * or with status 400 (Bad Request) if the JobColumnConfig is not valid,
     * or with status 500 (Internal Server Error) if the Column config couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a job column config", notes = "Each user has their own config.", response = JobColumnConfigDTO.class)
    @PutMapping("/jobs/preferences/JOB_COLUMN")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<JobColumnConfigDTO> updateJobColumnConfig(@RequestBody JobColumnConfigDTO jobColumnConfigDTO) throws URISyntaxException {
        //jobFormConfigDTO.setId(id);
        log.info("[APN: CustomColumnConfig @{}] REST request to update Column config : {}", SecurityUtils.getUserId(), jobColumnConfigDTO);
        JobColumnConfigDTO result = jobColumnConfigService.saveJobColumnConfig(jobColumnConfigDTO, SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(result);
    }


    @ApiOperation(value = "Update a private job column config", notes = "Each user has their own config.", response = JobColumnConfigDTO.class)
    @PutMapping("/jobs/preferences/PRIVATE_JOB_COLUMN")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<JobColumnConfigDTO> updatePrivateJobColumnConfig(@RequestBody JobColumnConfigDTO jobColumnConfigDTO) throws URISyntaxException {
        //jobFormConfigDTO.setId(id);
        log.info("[APN: CustomColumnConfig @{}] REST request to update Private Job Column config : {}", SecurityUtils.getUserId(), jobColumnConfigDTO);
        JobColumnConfigDTO result = jobColumnConfigService.savePrivateJobColumnConfig(jobColumnConfigDTO, SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * PUT  /custom-page/:id : Updates a page config.
     *
     * @param pageConfig to PageConfig update
     * @return the ResponseEntity with status 200 (OK) and with body the updated pageConfig,
     * or with status 400 (Bad Request) if the PageConfig is not valid,
     * or with status 500 (Internal Server Error) if the Column config couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a job page config", notes = "Each user has their own config.", response = PageConfig.class)
    @PutMapping("/jobs/preferences/job-column-page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<PageConfig> updateJobPageConfig(@RequestBody PageConfig pageConfig) throws URISyntaxException {
        //jobFormConfigDTO.setId(id);
        log.info("[APN: CustomColumnConfig @{}] REST request to update Job Page config : {}", SecurityUtils.getUserId(), pageConfig);
        PageConfig result = jobColumnConfigService.updateJobPageConfig(pageConfig);
        return ResponseEntity.ok()
                .body(result);
    }


    /**
     * PUT   Update a private job page config.
     *
     * @param pageConfig to PageConfig update
     * @return the ResponseEntity with status 200 (OK) and with body the updated pageConfig,
     * or with status 400 (Bad Request) if the PageConfig is not valid,
     * or with status 500 (Internal Server Error) if the Column config couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a private job page config", notes = "Each user has their own config.", response = PageConfig.class)
    @PutMapping("/jobs/preferences/PRIVATE-JOB-COLUMN-PAGE")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<PageConfig> updatePrivateJobPageConfig(@RequestBody PageConfig pageConfig)  {
        log.info("[APN: CustomColumnConfig @{}] REST request to update Private Job Page config : {}", SecurityUtils.getUserId(), pageConfig);
        PageConfig result = jobColumnConfigService.updatePrivateJobPageConfig(pageConfig);
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * GET  {recruitmentProcessId}: get recently used recruitment process id for current user
     *
     * @return the ResponseEntity with status 200 (OK) and the id in body
     */
    @ApiOperation(value = "Get recently used recruitment process", response = Long.class, responseContainer = "List")
    @GetMapping("/jobs/preferences/recently-used-recruitment-process")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<Map<String, Long>> getRecentUsedRecruitmentProcess() {
        log.info("[APN: CustomColumn @{}] REST request to get recently used recruitment process id", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(
                        Collections.singletonMap("recruitmentProcessId",
                                userPreferenceService.getUserRecentlyUsedRecruitmentProcessByUserId(SecurityUtils.getUserId()))
                );


    }

    @ApiOperation(value = "Get recently used recruitment process for private job", response = Long.class, responseContainer = "List")
    @GetMapping("/jobs/preferences/recently-used-recruitment-process/private-job")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<Map<String, Long>> getRecentUsedRecruitmentProcessForPrivateJob() {
        log.info("[APN: CustomColumn @{}] REST request to get recently used recruitment process id for private job", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(
                        Collections.singletonMap("recruitmentProcessId",
                                userPreferenceService.getUserRecentlyUsedRecruitmentProcessByUserIdForPrivateJob(SecurityUtils.getUserId()))
                );
    }

    /**
     * DELETE  {recruitmentProcessId}: delete recently used recruitment process id after inactive
     *
     * @return the ResponseEntity with status 200 (OK) and the id in body
     */
    @ApiOperation(value = "Delete all recently used recruitment process after inactive", response = Long.class, responseContainer = "List")
    @DeleteMapping("/jobs/preferences/recently-used-recruitment-process/{recruitmentProcessId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<Void> removeUserPreferenceRecruitmentProcessAfterInactiveRecruitmentProcess(@PathVariable Long recruitmentProcessId) {
        log.info("[APN: CustomColumn @{}] REST request to disable user last used recruitment process id", SecurityUtils.getUserId());
        userPreferenceService.removeRecentUsedRecruitmentProcess(recruitmentProcessId);
        return ResponseEntity.noContent().build();
    }

    /**
     * GET  {recruitmentProcessId}: get recently used recruitment process id for current user
     *
     * @return the ResponseEntity with status 204 (noContent)
     */
    @ApiOperation(value = "delete all cached job column", response = Long.class, responseContainer = "List")
    @DeleteMapping("/jobs/preferences/cached-job-column/{tenantId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<Void> removeJobColumnInRedisByTenantId(@PathVariable Long tenantId) {
        log.info("[APN: CustomColumn @{}] REST request to get recently used recruitment process id", SecurityUtils.getUserId());
        jobColumnConfigService.removeAllCachedJobColumnConfigByTenantId(tenantId);
        return ResponseEntity.noContent().build();

    }

    /**
     * query company column config.
     *
     * @param type
     * @return
     */
    @ApiOperation(value = "Get user's company configuration", response = CompanyColumnConfigDTO.class)
    @GetMapping("/company/preferences/{module}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<CompanyColumnConfigDTO> getCompanyColumnConfig(@PathVariable("module") CustomerType type) {
        log.info("[APN: CustomColumn @{}] REST request to get user's company configuration column", SecurityUtils.getUserId());
        return ResponseEntity.ok().body(companyColumnConfigService.getColumnConfigByUserId(type, SecurityUtils.getUserId()));
    }

    /**
     * update company column config.
     *
     * @param type
     * @param columnConfigDTO
     * @return
     */
    @ApiOperation(value = "Update user's company configuration", response = CompanyColumnConfigDTO.class)
    @PutMapping("/company/preferences/{module}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<CompanyColumnConfigDTO> saveCompanyColumnConfig(@PathVariable("module") CustomerType type, @RequestBody CompanyColumnConfigDTO columnConfigDTO) {
        log.info("[APN: CustomColumn @{}] REST request to update user's company configuration column", SecurityUtils.getUserId());
        return new ResponseEntity<>(companyColumnConfigService.saveCompanyColumnConfig(type, columnConfigDTO), HttpStatus.CREATED);
    }

    /**
     * update latest used recruitment Process for a user when create a new job.
     *
     * @param recruitmentProcessId
     * @param userId
     * @return
     */
    @ApiOperation(value = "Update user's latest used recruitment process", response = Void.class)
    @PutMapping("/jobs/preferences/latest-update-job/{recruitmentProcessId}/user/{userId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<Void> updateUserRecentlyUsedRecruitmentProcess(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @PathVariable("userId") Long userId) {
        log.info("[APN: CustomColumn @{}] REST request to update user's preference configuration after creating a new job", SecurityUtils.getUserId());
        userPreferenceService.updateUserRecentlyUsedRecruitmentProcess(recruitmentProcessId, userId);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @ApiOperation(value = "Update user's latest used recruitment process for private job", response = Void.class)
    @PutMapping("/jobs/preferences/latest-update-private-job/{recruitmentProcessId}/user/{userId}")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<Void> updateUserRecentlyUsedRecruitmentProcessForPrivateJob(@PathVariable("recruitmentProcessId") Long recruitmentProcessId, @PathVariable("userId") Long userId) {
        log.info("[APN: CustomColumn @{}] REST request to update user's preference configuration after creating a new private job", SecurityUtils.getUserId());
        userPreferenceService.updateUserRecentlyUsedRecruitmentProcessForPrivateJob(recruitmentProcessId, userId);
        return new ResponseEntity<>(HttpStatus.OK);
    }


    /*
    talent section
     */


    /**
     * GET  /talents/preferences/talent-form : Get all talent form config.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talent form config in body
     */
    @GetMapping("/talents/config/talent-form")
    @ApiOperation(value = "Get all talent form config", response = BaseConfig.class, responseContainer = "List")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<String> getTalentFormConfig() {
        log.info("[APN: CustomForm @{}] REST request to get tenant talent form config, Tenant:{}", SecurityUtils.getUserId(), SecurityUtils.getTenantId());
        return ResponseEntity.ok()
                .body(talentFormConfigService.getFormConfigByTenantId(SecurityUtils.getTenantId()));
    }


    /**
     * PUT  /talents/preferences/talent-form : Update a Talent Form Config.
     *
     * @param dto the baseConfig to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated baseConfig,
     * or with status 400 (Bad Request) if the baseConfig is not valid,
     * or with status 500 (Internal Server Error) if the baseConfig couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/talents/config/talent-form")
    @ApiOperation(value = "Update a Talent Form Config", notes = "Each user has their own config.", response = BaseConfig.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<Void> updateTalentFormConfig(@RequestBody TalentPreferenceConfigDTO dto) throws URISyntaxException {
        log.info("[APN: CustomFormConfig @{}] REST request to update Talent Form config, tenant: {}", SecurityUtils.getUserId(), SecurityUtils.getTenantId());
        talentFormConfigService.saveTalentColumnConfig(dto, SecurityUtils.getUserId());
        return ResponseEntity.ok().build();
    }

    /**
     * GET  /talents/preferences/talent-column : Get all talent column config.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talent column config in body
     */
    @GetMapping("/talents/preferences/talent-column")
    @ApiOperation(value = "Get all talent column config", response = BaseConfig.class, responseContainer = "List")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<UserTalentColumnConfigDTO> getTalentColumnConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get user talent column config", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(talentColumnConfigService.getTalentColumnConfigByUserId(SecurityUtils.getUserId()));
    }

    /**
     * PUT  /talents/preferences/talent-column : Update a Talent Column Config.
     *
     * @param columnConfigDTO the talent column Config DTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated baseConfig,
     * or with status 400 (Bad Request) if the baseConfig is not valid,
     * or with status 500 (Internal Server Error) if the baseConfig couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/talents/preferences/talent-column")
    @ApiOperation(value = "Update a Talent Column Config", notes = "Each user has their own config.", response = BaseConfig.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> updateTalentColumnConfig(@RequestBody TalentColumnConfigDTO columnConfigDTO) throws URISyntaxException {
        log.info("[APN: CustomColumnConfig @{}] REST request to update talent Column config", SecurityUtils.getUserId());
        BaseConfig result = talentColumnConfigService.saveTalentColumnConfig(columnConfigDTO, SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * PUT  /custom-page/:id : Updates a page config.
     *
     * @param pageConfig to PageConfig update
     * @return the ResponseEntity with status 200 (OK) and with body the updated pageConfig,
     * or with status 400 (Bad Request) if the PageConfig is not valid,
     * or with status 500 (Internal Server Error) if the Column config couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a talent page config", notes = "Each user has their own config.", response = PageConfig.class)
    @PutMapping("/talents/preferences/talent-column-page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<PageConfig> updateTalentPageConfig(@RequestBody PageConfig pageConfig) throws URISyntaxException {
        //jobFormConfigDTO.setId(id);
        log.info("[APN: CustomColumnConfig @{}] REST request to update page config : {}", SecurityUtils.getUserId(), pageConfig);
        PageConfig result = talentColumnConfigService.updateTalentPageConfig(pageConfig);
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * GET  /talents/preferences/relate-job-folder-column : Get all talent relate job folder column config.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talent column config in body
     */
    @GetMapping("/talents/preferences/relate-job-folder-column")
    @ApiOperation(value = "Get all talent relate job folder column config", response = BaseConfig.class, responseContainer = "List")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> getTalentRelateJobFolderColumnConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get user talent relate job folder  column config", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(talentColumnConfigService.getTalentRelateJobFolderColumnConfigByUserId(SecurityUtils.getUserId()));
    }

    /**
     * PUT  /talents/preferences/talent-column : Update a Talent relate job folder Column Config.
     *
     * @param columnConfigDTO the talent column Config DTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated baseConfig,
     * or with status 400 (Bad Request) if the baseConfig is not valid,
     * or with status 500 (Internal Server Error) if the baseConfig couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/talents/preferences/relate-job-folder-column")
    @ApiOperation(value = "Update a Talent Relate Job Folder Column Config", notes = "Each user has their own config.", response = BaseConfig.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> updateTalentRelateJobFolderColumnConfig(@RequestBody TalentColumnConfigDTO columnConfigDTO) throws URISyntaxException {
        log.info("[APN: CustomColumnConfig @{}] REST request to update talent Column config", SecurityUtils.getUserId());
        BaseConfig result = talentColumnConfigService.saveTalentRelateJobFolderColumnConfig(columnConfigDTO, SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * PUT  /custom-page/:id : Updates a page config.
     *
     * @param pageConfig to PageConfig update
     * @return the ResponseEntity with status 200 (OK) and with body the updated pageConfig,
     * or with status 400 (Bad Request) if the PageConfig is not valid,
     * or with status 500 (Internal Server Error) if the Column config couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a talent relate job folder page config", notes = "Each user has their own config.", response = PageConfig.class)
    @PutMapping("/talents/preferences/relate-job-folder-column-page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<PageConfig> updateTalentRelateJobFolderPageConfig(@RequestBody PageConfig pageConfig) throws URISyntaxException {
        log.info("[APN: CustomColumnConfig @{}] REST request to update page config : {}", SecurityUtils.getUserId(), pageConfig);
        PageConfig result = talentColumnConfigService.updateTalentTalentRelateJobFolderPageConfig(pageConfig);
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * GET  /talents/preferences/search-folder-column : Get all talent search folder column config.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talent column config in body
     */
    @GetMapping("/talents/preferences/search-folder-column")
    @ApiOperation(value = "Get all talent search folder column config", response = BaseConfig.class, responseContainer = "List")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> getTalentSearchFolderColumnConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get user talent search folder  column config", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(talentColumnConfigService.getTalentSearchFolderColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * PUT  /talents/preferences/talent-column : Update a Talent relate job folder Column Config.
     *
     * @param columnConfigDTO the talent column Config DTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated baseConfig,
     * or with status 400 (Bad Request) if the baseConfig is not valid,
     * or with status 500 (Internal Server Error) if the baseConfig couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/talents/preferences/search-folder-column")
    @ApiOperation(value = "Update a Talent Search Folder Column Config", notes = "Each user has their own config.", response = BaseConfig.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<BaseConfig> updateTalentSearchFolderColumnConfig(@RequestBody TalentColumnConfigDTO columnConfigDTO) throws URISyntaxException {
        log.info("[APN: CustomColumnConfig @{}] REST request to update talent saerch folder Column config", SecurityUtils.getUserId());
        BaseConfig result = talentColumnConfigService.updateTalentSearchFolderColumnConfig(columnConfigDTO, SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * PUT  /custom-page/:id : Updates a page config.
     *
     * @param pageConfig to PageConfig update
     * @return the ResponseEntity with status 200 (OK) and with body the updated pageConfig,
     * or with status 400 (Bad Request) if the PageConfig is not valid,
     * or with status 500 (Internal Server Error) if the Column config couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a talent relate job folder page config", notes = "Each user has their own config.", response = PageConfig.class)
    @PutMapping("/talents/preferences/search-folder-column-page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<PageConfig> updateTalentSearchFolderPageConfig(@RequestBody PageConfig pageConfig) throws URISyntaxException {
        log.info("[APN: CustomColumnConfig @{}] REST request to update page config : {}", SecurityUtils.getUserId(), pageConfig);
        PageConfig result = talentColumnConfigService.updateTalentSearchFolderPageConfig(pageConfig);
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * GET  /talents/preferences/talent-database-column : Get all talent database column config.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talent database column config in body
     */
    @GetMapping("/talents/preferences/talent-database-column")
    @ApiOperation(value = "Get all talent database column config", response = BaseConfig.class, responseContainer = "List")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> getTalentDatabaseColumnConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get user talent database column config", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(talentColumnConfigService.getTalentDatabaseColumnConfigByUserId(SecurityUtils.getUserId()));
    }

    /**
     * PUT  /talents/preferences/talent-database-column : Update a Talent Database Column Config.
     *
     * @param columnConfigDTO the baseConfig to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated baseConfig,
     * or with status 400 (Bad Request) if the baseConfig is not valid,
     * or with status 500 (Internal Server Error) if the baseConfig couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is
     *                            incorrect
     */
    @PutMapping("/talents/preferences/talent-database-column")
    @ApiOperation(value = "Update a Talent Database Column Config", notes = "Each user has their own config.", response = BaseConfig.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<BaseConfig> updateTalentDatabaseColumnConfig(@RequestBody TalentColumnConfigDTO columnConfigDTO) throws URISyntaxException {
        log.info("[APN: CustomColumnConfig @{}] REST request to update talent Database Column config", SecurityUtils.getUserId());
        BaseConfig result = talentColumnConfigService.saveTalentDatabaseColumnConfig(columnConfigDTO, SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(result);
    }

    @GetMapping("/user/preferences/personalization")
    public ResponseEntity<UserPersonalizationConfig> getPersonalizationConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get user personalization config", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(userPreferenceService.getPersonalizationConfig());
    }

    @PutMapping("/user/preferences/personalization")
    public ResponseEntity<Void> updatePersonalizationConfig(@RequestBody UserPersonalizationConfig config) {
        log.info("[APN: CustomColumn @{}] REST request to get user personalization config, {}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(config));
        userPreferenceService.updatePersonalizationConfig(config);
        return ResponseEntity.ok().build();
    }

    /**
     * PUT  /custom-page/:id : Updates a page config.
     *
     * @param pageConfig to PageConfig update
     * @return the ResponseEntity with status 200 (OK) and with body the updated pageConfig,
     * or with status 400 (Bad Request) if the PageConfig is not valid,
     * or with status 500 (Internal Server Error) if the Column config couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a talent page config", notes = "Each user has their own config.", response = PageConfig.class)
    @PutMapping("/talents/preferences/talent-database-column-page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<PageConfig> updateTalentDataBasePageConfig(@RequestBody PageConfig pageConfig) throws URISyntaxException {
        //jobFormConfigDTO.setId(id);
        log.info("[APN: CustomColumnConfig @{}] REST request to update page config : {}", SecurityUtils.getUserId(), pageConfig);
        PageConfig result = talentColumnConfigService.updateTalentDatabasePageConfig(pageConfig);
        return ResponseEntity.ok()
                .body(result);
    }


    /**
     * GET  /talents/preferences/pipeline-column : Get user's talent pipeline column config.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of talent pipeline column config in body
     */
    @GetMapping("/talents/preferences/talent-pipeline-column")
    @ApiOperation(value = "Get talent pipeline column config", response = BaseConfig.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<BaseConfig> getTalentPipelineColumnConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get user talent database column config", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(pipelineColumnConfigService.findByUserId(SecurityUtils.getUserId()));
    }

    /**
     * PUT  /talents/preferences/pipeline-column : Update a Talent Database Column Config.
     *
     * @param pipelineColumnPreferenceDTO the baseConfig to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated baseConfig,
     * or with status 400 (Bad Request) if the baseConfig is not valid,
     * or with status 500 (Internal Server Error) if the baseConfig couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is
     *                            incorrect
     */
    @PutMapping("/talents/preferences/talent-pipeline-column")
    @ApiOperation(value = "Update a Talent pipeline Column Config", notes = "Each user has their own config.", response = BaseConfig.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<BaseConfig> updateTalentPipelineColumnConfig(@RequestBody PipelineColumnPreferenceDTO pipelineColumnPreferenceDTO) throws URISyntaxException {
        log.info("[APN: CustomColumnConfig @{}] REST request to update talent Database Column config", SecurityUtils.getUserId());
        BaseConfig result = pipelineColumnConfigService.updatePipelineColumnConfig(pipelineColumnPreferenceDTO, SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(result);
    }


    /**
     * PUT  /custom-page/:id : Updates a page config.
     *
     * @param pageConfig to PageConfig update
     * @return the ResponseEntity with status 200 (OK) and with body the updated pageConfig,
     * or with status 400 (Bad Request) if the PageConfig is not valid,
     * or with status 500 (Internal Server Error) if the Column config couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @ApiOperation(value = "Update a talent page config", notes = "Each user has their own config.", response = PageConfig.class)
    @PutMapping("/talents/preferences/talent-pipeline-column-page")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<PageConfig> updateTalentPipelinePageConfig(@RequestBody PageConfig pageConfig) throws URISyntaxException {
        //jobFormConfigDTO.setId(id);
        log.info("[APN: CustomColumnConfig @{}] REST request to update page config : {}", SecurityUtils.getUserId(), pageConfig);
        PageConfig result = pipelineColumnConfigService.updatePipelinePageConfig(pageConfig);
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * GET  /talents/preferences/pipeline-column-template : Get all pipeline column config.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of pipeline column config in body
     */
    @GetMapping("/talents/preferences/pipeline-column-template")
    @ApiOperation(value = "Get all pipeline column config", response = BaseConfig.class, responseContainer = "List")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<List<BaseConfig>> getPipelineColumnConfig() {
        log.info("[APN: CustomColumn @{}] REST request to get user pipeline column config", SecurityUtils.getUserId());
        return ResponseEntity.ok()
                .body(pipelineColumnPreferenceService.findByUserId(SecurityUtils.getUserId()));
    }


    /**
     * POST  /talents/preferences/pipeline-column-template : add a Pipeline Column Config.
     *
     * @param pipelineColumnPreferenceDTO the baseConfig to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated baseConfig,
     * or with status 400 (Bad Request) if the baseConfig is not valid,
     * or with status 500 (Internal Server Error) if the baseConfig couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/talents/preferences/pipeline-column-template")
    @ApiOperation(value = "Update a Pipeline Column Config", notes = "Each user has their own config.", response = BaseConfig.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<BaseConfig> createPipelineColumnConfig(@RequestBody PipelineColumnPreferenceDTO pipelineColumnPreferenceDTO) throws URISyntaxException {
        log.info("[APN: CustomColumnConfig @{}] REST request to update my Pipeline Column config id: {}", SecurityUtils.getUserId());
        BaseConfig result = pipelineColumnPreferenceService.createColumnConfig(pipelineColumnPreferenceDTO);
        return ResponseEntity.created(new URI("talents/preferences/pipeline-column"))
                .body(result);
    }

    /**
     * PUT  /talents/preferences/pipeline-column-template : Update a Pipeline Column Config.
     *
     * @param pipelineColumnPreferenceDTO the baseConfig to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated baseConfig,
     * or with status 400 (Bad Request) if the baseConfig is not valid,
     * or with status 500 (Internal Server Error) if the baseConfig couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/talents/preferences/pipeline-column-template/{templateId}")
    @ApiOperation(value = "Update a Pipeline Column Config", notes = "Each user has their own config.", response = BaseConfig.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<BaseConfig> updatePipelineColumnConfig(@PathVariable Long templateId, @RequestBody PipelineColumnPreferenceDTO pipelineColumnPreferenceDTO) throws URISyntaxException {
        log.info("[APN: CustomColumnConfig @{}] REST request to update my Pipeline Column config id: {}", SecurityUtils.getUserId(), templateId);
        BaseConfig result = pipelineColumnPreferenceService.updateColumnConfig(pipelineColumnPreferenceDTO, templateId);
        return ResponseEntity.ok()
                .body(result);
    }

    /**
     * Delete a pipeline column template
     *
     * @param templateId
     * @return
     */
    @ApiOperation(value = "delete template  ")
    @DeleteMapping("/talents/preferences/pipeline-column-template/{templateId}")
    public ResponseEntity<Integer> deleteModule(@ApiParam(value = "template id", required = true) @PathVariable Long templateId) {
        log.info("[APN: UserPreference @{}] REST request to delete template  : {}", SecurityUtils.getUserId(), templateId);
        pipelineColumnPreferenceService.deleteById(templateId);
        return new ResponseEntity<>(204, HttpStatus.NO_CONTENT);
    }

    @GetMapping("/setting")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    public ResponseEntity<TenantConfigDTO> getSettingConfig(@RequestParam(value = "configCode") TenantConfigCode configCode) {
        log.info("[APN: TenantConfig @{}] REST request to query config by configName : {}", SecurityUtils.getUserId(), configCode);
        return ResponseEntity.ok(tenantConfigService.findByConfigCode(configCode,SecurityUtils.getTenantId()));
    }

    @GetMapping("/setting/{tenantId}")
    public ResponseEntity<TenantConfigDTO> getSettingConfig(@PathVariable("tenantId") Long tenantId,@RequestParam(value = "configCode") TenantConfigCode configCode) {
        log.info("[APN: TenantConfig @{}] REST request to query config by configName : {}", SecurityUtils.getUserId(), configCode);
        return ResponseEntity.ok(tenantConfigService.findByConfigCode(configCode, tenantId));
    }

    @PostMapping("/setting/custom")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @NoRepeatSubmit
    public ResponseEntity<TenantConfigDTO> saveSettingConfig(@RequestBody @Valid TenantConfigDTO tenantConfigDTO) {
        log.info("[APN: TenantConfig @{}] REST request to save config  : {}", SecurityUtils.getUserId(), tenantConfigDTO);
        return ResponseEntity.ok().body(tenantConfigService.saveSettingConfig(tenantConfigDTO,SecurityUtils.getTenantId()));
    }

    /**
     * search dashboard column-config
     * @return
     */
    @ApiOperation(value = "search dashboard column-config  ")
    @GetMapping("/dashboard/config")
    public ResponseEntity<DashboardColumnConfigVO> dashboardColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get dashboard column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(dashboardColumnConfigService.searchDashboardColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update dashboard column-config
     * @return
     */
    @ApiOperation(value = "search dashboard column-config  ")
    @PutMapping("/dashboard/config")
    public ResponseEntity<Void> updateDashboardColumnConfig(@RequestBody DashboardColumnConfigDTO dashboardColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to get dashboard column config  : {}", SecurityUtils.getUserId(), dashboardColumnConfigDTO);
        dashboardColumnConfigService.updateDashboardColumnConfigByUserId(SecurityUtils.getUserId(), dashboardColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * search invoice column-config
     * @return
     */
    @ApiOperation(value = "search invoice column-config  ")
    @GetMapping("/invoice/config")
    public ResponseEntity<InvoiceColumnConfigVO> invoiceColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get invoice column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(invoiceColumnConfigService.searchInvoiceColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update invoice column-config
     * @return
     */
    @ApiOperation(value = "search invoice column-config  ")
    @PutMapping("/invoice/config")
    public ResponseEntity<Void> updateInvoiceColumnConfig(@RequestBody InvoiceColumnConfigDTO invoiceColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to get invoice column config  : {}", SecurityUtils.getUserId(), invoiceColumnConfigDTO);
        invoiceColumnConfigService.updateInvoiceColumnConfigByUserId(SecurityUtils.getUserId(), invoiceColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * search invoice column-config
     * @return
     */
    @ApiOperation(value = "search expense-report column-config  ")
    @GetMapping("/expense-report/config")
    public ResponseEntity<ExpenseReportColumnConfigVO> expenseReportColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get expense report column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(expenseReportColumnConfigService.searchExpenseReportColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update invoice column-config
     * @return
     */
    @ApiOperation(value = "search expense-report column-config  ")
    @PutMapping("/expense-report/config")
    public ResponseEntity<Void> updateExpenseReportColumnConfig(@RequestBody ExpenseReportColumnConfigDTO expenseReportColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to get expense report column config  : {}", SecurityUtils.getUserId(), expenseReportColumnConfigDTO);
        expenseReportColumnConfigService.updateExpenseReportColumnConfigByUserId(SecurityUtils.getUserId(), expenseReportColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * search invoice column-config
     * @return
     */
    @ApiOperation(value = "search expense-report column-config  ")
    @GetMapping("/current-contractor/config")
    public ResponseEntity<CompanyCurrentContractorColumnConfigVO> currentContractorColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get company current contractor column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(companyCurrentContractorColumnConfigService.searchCompanyCurrentContractorColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update invoice column-config
     * @return
     */
    @ApiOperation(value = "search expense-report column-config  ")
    @PutMapping("/current-contractor/config")
    public ResponseEntity<Void> updateCurrentContractorColumnConfig(@RequestBody CompanyCurrentContractorColumnConfigDTO companyCurrentContractorColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to get company current contractor column config  : {}", SecurityUtils.getUserId(), companyCurrentContractorColumnConfigDTO);
        companyCurrentContractorColumnConfigService.updateCompanyCurrentContractorColumnConfigByUserId(SecurityUtils.getUserId(), companyCurrentContractorColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "search fte-bd-report column-config  ")
    @GetMapping("/fte-bd-report/config")
    public ResponseEntity<CompanyFteBdReportColumnConfigVO> fteBdReportColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get company fte-bd-report column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(companyFteBdReportColumnConfigService.searchConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update fte-bd-report column-config
     * @return
     */
    @ApiOperation(value = "search expense-report column-config  ")
    @PutMapping("/fte-bd-report/config")
    public ResponseEntity<Void> updateFteBdReportColumnConfig(@RequestBody CompanyFteBdReportColumnConfigDTO dto) {
        log.info("[APN: UserPreference @{}] REST request to get company fte-bd-report column config  : {}", SecurityUtils.getUserId(), dto);
        companyFteBdReportColumnConfigService.updateConfigByUserId(SecurityUtils.getUserId(), dto);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "search user-search column-config  ")
    @GetMapping("/user-search/config/{configSubcategory}")
    public ResponseEntity<UserSearchColumnConfigVO> userSearchColumnConfig(@PathVariable ConfigSubcategory configSubcategory) {
        log.info("[APN: UserPreference @{}] REST request to get user-label-report column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(userLabelReportColumnConfigService.searchConfigByUserId(SecurityUtils.getUserId(), configSubcategory));
    }


    /**
     * update user-label-report column-config
     * @return
     */
    @ApiOperation(value = "search user-search column-config  ")
    @PutMapping("/user-search/config")
    public ResponseEntity<Void> updateUserSearchColumnConfig(@RequestBody UserSearchColumnConfigDTO dto) {
        log.info("[APN: UserPreference @{}] REST request to update user-label-report column config  : {}", SecurityUtils.getUserId(), dto);
        userLabelReportColumnConfigService.updateConfigByUserId(SecurityUtils.getUserId(), dto);
        return ResponseEntity.ok().build();
    }

    @ApiOperation(value = "search column-config by category ")
    @GetMapping("/column-config/detail/{category}/{subCategory}")
    public ResponseEntity<String> getColumnConfigDetailByCategory(@PathVariable("category") byte category, @PathVariable("subCategory") Short subCategory) {
        log.info("[APN: UserPreference @{}] REST request to get column config, category = {}, subCategory = {}", SecurityUtils.getUserId(), category, subCategory);
        return ResponseEntity.ok(systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(Category.fromDbValue(category), ConfigSubcategory.fromDbValue(subCategory)).getDefaultConfig());
    }

    /**
     * search china area company invoicing column-config
     * @return
     */
    @ApiOperation(value = "search china area company invoicing column-config  ")
    @GetMapping("/company/invoicing/config")
    public ResponseEntity<InvoicingColumnConfigVO> invoicingColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get china area company invoicing column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(invoicingChinaAreaService.searchInvoicingColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update china area company invoicing column-config
     * @return
     */
    @ApiOperation(value = "search china area company invoicing column-config  ")
    @PutMapping("/company/invoicing/config")
    public ResponseEntity<Void> updateInvoicingColumnConfig(@RequestBody InvoicingColumnConfigDTO invoicingColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to get china area company invoicing column config  : {}", SecurityUtils.getUserId(), invoicingColumnConfigDTO);
        invoicingChinaAreaService.updateInvoicingColumnConfigByUserId(SecurityUtils.getUserId(), invoicingColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * search calendar event config
     * @return
     */
    @ApiOperation(value = "search calendar event config  ")
    @GetMapping("/calendar-event/config")
    public ResponseEntity<CalendarColumnConfigVO> calendarColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get calendar column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(calendarColumnConfigService.searchCalendarColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update calendar event config
     * @return
     */
    @ApiOperation(value = "update calendar event config")
    @PutMapping("/calendar-event/config")
    public ResponseEntity<Void> updateCalendarColumnConfig(@RequestBody CalendarColumnConfigDTO calendarColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to get calendar column config  : {}", SecurityUtils.getUserId(), calendarColumnConfigDTO);
        calendarColumnConfigService.updateCalendarColumnConfigByUserId(SecurityUtils.getUserId(), calendarColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * search monthly revenue detail column-config
     * @return
     */
    @ApiOperation(value = "search monthly revenue detail column-config  ")
    @GetMapping("/monthly-revenue-detail/config")
    public ResponseEntity<ReportGSeriesConfigVO> monthlyRevenueDetailColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get monthly revenue detail column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(reportGSeriesColumnConfigService.searchMonthlyRevenueDetailColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update monthly revenue detail column-config
     * @return
     */
    @ApiOperation(value = "update monthly revenue detail column-config  ")
    @PutMapping("/monthly-revenue-detail/config")
    public ResponseEntity<Void> updateMonthlyRevenueDetailColumnConfig(@RequestBody ReportGSeriesColumnConfigDTO reportGSeriesColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to update monthly revenue detail column config  : {}", SecurityUtils.getUserId(), reportGSeriesColumnConfigDTO);
        reportGSeriesColumnConfigService.updateMonthlyRevenueDetailColumnConfigByUserId(SecurityUtils.getUserId(), reportGSeriesColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * search quarterly-onboarding-new-hires column-config
     * @return
     */
    @ApiOperation(value = "search quarterly-onboarding-new-hires column-config  ")
    @GetMapping("/quarterly-onboarding-new-hires/config")
    public ResponseEntity<ReportGSeriesConfigVO> quarterlyNewHiresColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get quarterly-onboarding-new-hires column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(reportGSeriesColumnConfigService.searchQuarterlyOnboardingNewHiresColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update quarterly-onboarding-new-hires column-config
     * @return
     */
    @ApiOperation(value = "update quarterly-onboarding-new-hires column-config  ")
    @PutMapping("/quarterly-onboarding-new-hires/config")
    public ResponseEntity<Void> updateQuarterlyNewHiresColumnConfig(@RequestBody ReportGSeriesColumnConfigDTO reportGSeriesColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to quarterly-onboarding-new-hires column config  : {}", SecurityUtils.getUserId(), reportGSeriesColumnConfigDTO);
        reportGSeriesColumnConfigService.updateQuarterlyOnboardingNewHiresColumnConfigByUserId(SecurityUtils.getUserId(), reportGSeriesColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * search quarterly-onboarding-renewals column-config
     * @return
     */
    @ApiOperation(value = "search quarterly-onboarding-renewals column-config  ")
    @GetMapping("/quarterly-onboarding-renewals/config")
    public ResponseEntity<ReportGSeriesConfigVO> quarterlyRenewalsColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get quarterly-onboarding-renewals column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(reportGSeriesColumnConfigService.searchQuarterlyOnboardingRenewalsColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update quarterly-onboarding-renewals column-config
     * @return
     */
    @ApiOperation(value = "update quarterly-onboarding-renewals column-config  ")
    @PutMapping("/quarterly-onboarding-renewals/config")
    public ResponseEntity<Void> updateQuarterlyRenewalsColumnConfig(@RequestBody ReportGSeriesColumnConfigDTO reportGSeriesColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to update quarterly-onboarding-renewals column config  : {}", SecurityUtils.getUserId(), reportGSeriesColumnConfigDTO);
        reportGSeriesColumnConfigService.updateQuarterlyOnboardingRenewalsColumnConfigByUserId(SecurityUtils.getUserId(), reportGSeriesColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * search quarterly-offboarding column-config
     * @return
     */
    @ApiOperation(value = "search quarterly-offboarding column-config  ")
    @GetMapping("/quarterly-offboarding/config")
    public ResponseEntity<ReportGSeriesConfigVO> quarterlyOffboardingColumnConfig() {
        log.info("[APN: UserPreference @{}] REST request to get quarterly-offboarding column config", SecurityUtils.getUserId());
        return ResponseEntity.ok(reportGSeriesColumnConfigService.searchQuarterlyOffboardingColumnConfigByUserId(SecurityUtils.getUserId()));
    }


    /**
     * update quarterly-offboarding column-config
     * @return
     */
    @ApiOperation(value = "update quarterly-offboarding column-config  ")
    @PutMapping("/quarterly-offboarding/config")
    public ResponseEntity<Void> updateQuarterlyOffboardingColumnConfig(@RequestBody ReportGSeriesColumnConfigDTO reportGSeriesColumnConfigDTO) {
        log.info("[APN: UserPreference @{}] REST request to update quarterly-offboarding column config  : {}", SecurityUtils.getUserId(), reportGSeriesColumnConfigDTO);
        reportGSeriesColumnConfigService.updateQuarterlyOffboardingColumnConfigByUserId(SecurityUtils.getUserId(), reportGSeriesColumnConfigDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * search report-filter preferences
     * @return
     */
    @ApiOperation(value = "search report-filter preferences  ")
    @GetMapping("/report-filter/preferences")
    public ResponseEntity<List<ReportE1ReportFilterPreferenceVO>> reportFilterPreferences() {
        log.info("[APN: UserPreference @{}] REST request to get report-filter preferences", SecurityUtils.getUserId());
        return ResponseEntity.ok(reportE1ReportFilterPreferenceService.searchE1ReportFilterPreferenceByUserId(SecurityUtils.getUserId()));
    }


    /**
     * add report-filter preferences
     * @return
     */
    @ApiOperation(value = "add report-filter preferences  ")
    @PostMapping("/report-filter/preferences")
    public ResponseEntity<Void> addReportFilterPreferences(@RequestBody ReportE1ReportFilterPreferenceDTO reportE1ReportFilterPreferenceDTO) {
        log.info("[APN: UserPreference @{}] REST request to add report-filter preferences : {}", SecurityUtils.getUserId(), reportE1ReportFilterPreferenceDTO);
        reportE1ReportFilterPreferenceService.addE1ReportFilterPreferenceByUserId(SecurityUtils.getUserId(), reportE1ReportFilterPreferenceDTO);
        return ResponseEntity.ok().build();
    }

    /**
     * add report-filter preferences
     * @return
     */
    @ApiOperation(value = "update report-filter preferences  ")
    @PutMapping("/report-filter/preferences")
    public ResponseEntity<Void> updateReportFilterPreferences(@RequestBody ReportE1ReportFilterPreferenceDTO reportE1ReportFilterPreferenceDTO) {
        log.info("[APN: UserPreference @{}] REST request to update report-filter preferences : {}", SecurityUtils.getUserId(), reportE1ReportFilterPreferenceDTO);
        reportE1ReportFilterPreferenceService.updateE1ReportFilterPreferenceByUserId(SecurityUtils.getUserId(), reportE1ReportFilterPreferenceDTO);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/report-filter/preferences/{id}")
    @NoRepeatSubmit
    public ResponseEntity<ReportE1ReportFilterPreferenceVO> getReportFilterPreferences(@ApiParam(value = "report filter id", required = true) @PathVariable Long id) {
        log.info("[APN: Job @{}] REST request to delete report-filter preferences : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), id);
        ReportE1ReportFilterPreferenceVO vo = reportE1ReportFilterPreferenceService.getReportFilterPreferences(id);
        return ResponseEntity.ok(vo);
    }

    @DeleteMapping("/report-filter/preferences/{id}")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteReportFilterPreferences(@ApiParam(value = "job id", required = true) @PathVariable Long id) {
        log.info("[APN: Job @{}] REST request to delete report-filter preferences : {}", com.altomni.apn.common.utils.SecurityUtils.getUserId(), id);
        reportE1ReportFilterPreferenceService.deleteReportFilterPreferences(id);
        return ResponseEntity.ok().build();
    }
}
