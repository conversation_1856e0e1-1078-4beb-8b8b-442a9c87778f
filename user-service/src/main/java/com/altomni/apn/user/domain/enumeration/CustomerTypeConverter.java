package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

/**
 * <AUTHOR>
 */
@Converter
public class CustomerTypeConverter extends AbstractAttributeConverter<CustomerType, Integer> {
    public CustomerTypeConverter() {
        super(CustomerType::toDbValue, CustomerType::fromDbValue);
    }
}
