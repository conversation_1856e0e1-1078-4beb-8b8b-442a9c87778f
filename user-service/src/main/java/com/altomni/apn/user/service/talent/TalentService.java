package com.altomni.apn.user.service.talent;

import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.folder.TeamUserSetRollList;
import com.altomni.apn.common.dto.talent.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

@Component
@FeignClient(value = "talent-service")
public interface TalentService {

    @GetMapping("/talent/api/v3/talents/commonPool/contacts")
    ResponseEntity<String> getContactsFromCommonPool(@RequestParam("esId") String esId);

    @PostMapping("/talent/api/v3/talents/commonPool/talent/document")
    TalentESDocument getTalentESDocument(TalentESConditionDTO condition) throws IOException;

    //记录job-talent推荐进入的数据使用
    @PostMapping("/talent/api/v3/record/talent-job-recommend")
    ResponseEntity<Void> recordTalentJobRecommend(@RequestBody RecommendFeedback dto);

    @PostMapping("/talent/api/v3/talents/commonPool/talent/contacts")
    String searchContactsFromCommonPool(TalentESConditionDTO condition) throws IOException;

//    @GetMapping("/talent/api/v3/es-talents/update-username/{userId}")
//    void updateUserNameFromEsDb(@PathVariable("userId") Long userId);

    @GetMapping("/talent/api/v3/query-talent")
    TalentDTOV3 queryTalentFromCommon(@RequestParam("profile") String profile, @RequestParam(value = "contacts", required = false) List<String> contacts);

    @PutMapping("/talent/api/v3/talents/common-pool/unlock")
    ResponseEntity<Void> unlockCommonTalent(@RequestBody TalentUnlockListDto talentUnlockListDto);

    @PostMapping("/talent/api/v3/talents/checkContactExist")
    ResponseEntity<List<SuspectedDuplications>> checkContactExist(@RequestBody TalentDTOV3 talentDTO);

    @PutMapping("/talent/api/v3/talent-ownerships/transfer/from/{userId}/to/{newOwnerId}")
    ResponseEntity<Void> transferOwnership(@PathVariable("userId") Long userId, @PathVariable("newOwnerId") Long newOwnerId);

    @PostMapping("/talent/api/v3/user-change-team/user/{userId}/sync-talents-to-mq")
    ResponseEntity<Void> syncTalentsToMQByOwner(@PathVariable("userId") Long userId);


    @PostMapping("/talent/api/v3/search-folders/team-add-user/roll-list")
    ResponseEntity<Void> teamAddUserSetRollList(@RequestBody TeamUserSetRollList input);

    @PostMapping("/talent/api/v3/search-folders/team-remove-user/roll-list")
    ResponseEntity<Void> teamRemoveUserSetRollList(@RequestBody TeamUserSetRollList input);

    @PutMapping("/talent/api/v3/talents/confidential/handover/{fromUser}/{toUser}")
    ResponseEntity<Void> handoverConfidentialTalent(@PathVariable("fromUser") Long fromUser, @PathVariable("toUser") Long toUser);
}
