package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.config.TenantCustomConfig;
import com.altomni.apn.common.domain.config.TenantDefaultConfig;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import com.altomni.apn.common.domain.enumeration.config.TenantMessageMinderConfigFieldCodeEnum;
import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTenantMessageConfigDTO;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.repository.customconfig.TenantCustomConfigRepository;
import com.altomni.apn.user.repository.customconfig.TenantDefaultConfigRepository;
import com.altomni.apn.user.service.customconfig.TenantConfigService;
import com.altomni.apn.user.service.mapper.customconfig.TenantCustomConfigMapper;
import com.altomni.apn.user.service.mapper.customconfig.TenantDefaultConfigMapper;
import com.altomni.apn.user.service.xxljob.XxlJobService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.altomni.apn.common.config.constants.RedisConstants.DATA_KEY_TENANT_CONFIG_UPDATE_LIMIT;
import static com.altomni.apn.common.config.constants.RedisConstants.EXPIRE_IN_1_MINUTE;

@Service
public class TenantConfigServiceImpl implements TenantConfigService {

    private static final Logger log = LoggerFactory.getLogger(TenantConfigServiceImpl.class);

    @Resource
    TenantCustomConfigRepository tenantCustomConfigRepository;
    @Resource
    TenantDefaultConfigRepository tenantDefaultConfigRepository;
    @Resource
    TenantCustomConfigMapper tenantCustomConfigMapper;
    @Resource
    TenantDefaultConfigMapper tenantDefaultConfigMapper;

    @Resource
    private XxlJobService xxlJobService;

    @Resource
    private CommonRedisService commonRedisService;

    @Override
    @Cacheable(value  = "TenantConfig", key = "'configCode:' + #configCode.toDbValue() +':tenant:' + #tenantId", unless = "#result == null")
    public TenantConfigDTO findByConfigCode(TenantConfigCode configCode, Long tenantId) {
        // 是否存在自定义配置
        Optional<TenantCustomConfig> customConfig = tenantCustomConfigRepository.findFirstByConfigCodeAndTenantId(configCode, tenantId);
        // 存在直接返回
        if (customConfig.isPresent()) {
            return tenantCustomConfigMapper.toDto(customConfig.get());
        }
        //返回通用配置
        TenantDefaultConfig defaultConfig = tenantDefaultConfigRepository.findFirstByConfigCode(configCode).orElseThrow(() -> new NotFoundException("config does not exist"));
        return tenantDefaultConfigMapper.toDto(defaultConfig);
    }



    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value  = "TenantConfig", key = "'configCode:' + #tenantConfigDTO.configCode.toDbValue() +':tenant:' + #tenantId")
    public TenantConfigDTO saveSettingConfig(TenantConfigDTO tenantConfigDTO,Long tenantId) {
        // 查询是否存在自定义配置
        Optional<TenantCustomConfig> customConfig = tenantCustomConfigRepository.findFirstByConfigCodeAndTenantId(tenantConfigDTO.getConfigCode(), tenantId);
        TenantConfigDTO result = null;
        String oldConfigValue = null;
        if (customConfig.isEmpty()) {
            // 若不存在自定义配置，那么save到TenantCustomConfig
            TenantDefaultConfig defaultConfig = tenantDefaultConfigRepository.findFirstByConfigCode(tenantConfigDTO.getConfigCode()).orElseThrow(() -> new NotFoundException("config does not exist"));
            oldConfigValue = defaultConfig.getConfigValue();
            result = saveTenantConfigByDTO(tenantConfigDTO, tenantId);
        } else {
            if (isConfigUpdated(customConfig.get(), tenantConfigDTO)) {
                oldConfigValue = customConfig.get().getConfigValue();
                result = partialUpdateConfig(customConfig.get(), tenantConfigDTO);
            } else {
                // 没有实质性更新，直接返回
                return tenantConfigDTO;
            }
        }
        // 特殊处理，当更新类型为MESSAGE_CONFIG时，如果指定类型的key被更新，那么需要同步到xxlJob任务里。（同步限制：1分钟内只允许修改1次 xxlJob指定的key）
        if (TenantConfigCode.MESSAGE_CONFIG.equals(tenantConfigDTO.getConfigCode())) {
            updateXxlJob(oldConfigValue, result.getConfigValue());
        }
        commonRedisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX+ SecurityUtils.getUserId());
        return result;
    }

    private void updateXxlJob(String oldConfigValue, String configValue) {
        Set<XxlJobUpdateByTenantMessageConfigDTO> oldConfigSet = parseConfigToXxlJobDTO(oldConfigValue);
        Set<XxlJobUpdateByTenantMessageConfigDTO> newConfigSet = parseConfigToXxlJobDTO(configValue);

        Assert.notNull(oldConfigSet, "Original configValue is null !");
        Assert.notNull(oldConfigSet, "New configValue is null !");

        Map<TenantMessageMinderConfigFieldCodeEnum, String> oldValueMap = oldConfigSet.stream().collect(Collectors.toMap(XxlJobUpdateByTenantMessageConfigDTO::getType, XxlJobUpdateByTenantMessageConfigDTO::getNewValue));
        List<XxlJobUpdateByTenantMessageConfigDTO> xxlJobUpdateList = newConfigSet.stream().filter(t -> !oldConfigSet.contains(t)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(xxlJobUpdateList)) {
            lockHandle();
            xxlJobUpdateList.forEach(t -> {
                t.setOldValue(oldValueMap.get(t.getType()));
            });
            try{
                xxlJobService.updateXxlJobByTenantMessageConfig(xxlJobUpdateList);
            }catch (Exception e){
                log.error(ExceptionUtils.getRootCauseMessage(e));
                unlock();
                throw new RuntimeException("fail to call updateXxlJobByTenantMessageConfig.");
            }
        }
    }

    private void lockHandle() {
        String limitKey = String.format(DATA_KEY_TENANT_CONFIG_UPDATE_LIMIT, SecurityUtils.getTenantId());
        checkTenantLock(limitKey);
        //不存在则放入租户锁
        commonRedisService.set(limitKey, "1", EXPIRE_IN_1_MINUTE);
    }
    private void unlock() {
        String limitKey = String.format(DATA_KEY_TENANT_CONFIG_UPDATE_LIMIT, SecurityUtils.getTenantId());
        if (StringUtils.isNotBlank(commonRedisService.get(limitKey))) {
            commonRedisService.delete(limitKey);
        }
    }
    /**
     * 检查锁是否存在,若存在，则 throw exception
     *
     * @param limitKey
     */
    private void checkTenantLock(String limitKey) {
        if (StringUtils.isNotBlank(commonRedisService.get(limitKey))) {
            throw new CustomParameterizedException("Please refrain from making frequent data modifications. Kindly wait for 1 minute");
        }
    }

    private Set<XxlJobUpdateByTenantMessageConfigDTO> parseConfigToXxlJobDTO(String jsonValue) {
        if (StringUtils.isBlank(jsonValue)) {
            return null;
        }
        return JSONUtil.parseArray(jsonValue).stream().map(t -> (JSONObject) t)
                .filter(t -> TenantMessageMinderConfigFieldCodeEnum.valueOf(t.getStr("field")).isNeedUpdateXxlJob())
                .map(t -> {
                    XxlJobUpdateByTenantMessageConfigDTO xxlJobUpdateDTO = new XxlJobUpdateByTenantMessageConfigDTO();
                    xxlJobUpdateDTO.setType(TenantMessageMinderConfigFieldCodeEnum.valueOf(t.getStr("field")));
                    xxlJobUpdateDTO.setNewValue(t.getStr("value"));
                    return xxlJobUpdateDTO;
                }).collect(Collectors.toSet());
    }

    private boolean isConfigUpdated(TenantCustomConfig existingConfig, TenantConfigDTO tenantConfigDTO) {
        return !existingConfig.getConfigValue().equals(tenantConfigDTO.getConfigValue());
    }

    private TenantConfigDTO partialUpdateConfig(TenantCustomConfig tenantCustomConfig, TenantConfigDTO tenantConfigDTO) {
        tenantCustomConfigMapper.partialUpdate(tenantCustomConfig, tenantConfigDTO);
        return tenantCustomConfigMapper.toDto(tenantCustomConfigRepository.save(tenantCustomConfig));
    }

    private TenantConfigDTO saveTenantConfigByDTO(TenantConfigDTO tenantConfigDTO, Long tenantId) {
        TenantCustomConfig config = tenantCustomConfigMapper.toEntity(tenantConfigDTO);
        config.setTenantId(SecurityUtils.getTenantId());
        return tenantCustomConfigMapper.toDto(tenantCustomConfigRepository.save(config));
    }

}
