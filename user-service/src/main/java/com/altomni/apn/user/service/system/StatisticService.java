package com.altomni.apn.user.service.system;

import com.altomni.apn.user.web.rest.vm.system.OnlineTenantVM;
import com.altomni.apn.user.web.rest.vm.system.OnlineUserVM;

import java.util.List;
import java.util.concurrent.ExecutionException;

public interface StatisticService {

    Long countAllOnlineUsersByTenantIdAndSecondsIn(Long tenantId, Integer seconds);

    Long countAllOnlineUsersAndSecondsIn(Integer seconds);

    List<OnlineUserVM> getOnlineUsersByTenantIdAndSecondsIn(Long tenantId, Integer seconds) throws ExecutionException, InterruptedException;

//    List<OnlineTenantVM> getAllOnlineUsersSecondsInAndGroupByTenant(Integer seconds);
}
