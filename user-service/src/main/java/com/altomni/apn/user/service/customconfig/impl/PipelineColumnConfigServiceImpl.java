package com.altomni.apn.user.service.customconfig.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.customconfig.PipelineColumnConfigService;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.dto.customconfig.*;
import com.altomni.apn.user.service.mapper.customconfig.JsonMapperUtil;
import com.altomni.apn.user.service.mapper.customconfig.PipelineColumnConfigMapper;
import com.altomni.apn.user.service.mapper.customconfig.SystemConfigDefaultMapper;
import com.fasterxml.jackson.databind.JsonNode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class PipelineColumnConfigServiceImpl implements PipelineColumnConfigService {

    final static Category COLUMN_CATEGORY = Category.TALENT_COLUMN;

    final static ConfigSubcategory PIPELINE_COLUMN = ConfigSubcategory.TALENT_COLUMN_PIPELINE_GENERAL;

    final static ConfigSubcategory PIPELINE_CUSTOMIZED_COLUMN = ConfigSubcategory.TALENT_COLUMN_PIPELINE_CUSTOMIZED;


    @Resource
    private ApplicationIPGProperties applicationIPGProperties;


    @Resource
    private SystemConfigDefaultService systemConfigDefaultService;

    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    private PipelineColumnConfigMapper pipelineColumnConfigMapper;

    @Resource
    private SystemConfigDefaultMapper systemConfigDefaultMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public BaseConfig findByUserId(Long userId) {
        if (userId == null || userId <= 0 ){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_PIPELINE_FINDBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        if(userPreference != null && StringUtils.isNotBlank(userPreference.getTalentPipelineColumnConfig())){
            return pipelineColumnConfigMapper.toDto(userPreference);
        }
        //return getTalentPipelineColumnConfigByTenantId(SecurityUtils.getTenantId());
        return convertSystemConfigDTOToPipelineColumnConfigDTO(getTalentPipelineColumnConfigByTenantId(SecurityUtils.getTenantId()).getCustomConfig());

    }

    private PipelineColumnPreferenceDTO convertSystemConfigDTOToPipelineColumnConfigDTO(JsonNode systemColumnConfig){
        PipelineColumnPreferenceDTO talentPipelineColumnConfigDTO = new PipelineColumnPreferenceDTO();
        UserCustomConfig userCustomConfig = new UserCustomConfig();
        userCustomConfig.setColumnConfig(JsonMapperUtil.jsonNodeToCustomColumnFieldList(systemColumnConfig));
        userCustomConfig.setPageConfig(new PageConfig(10));
        talentPipelineColumnConfigDTO.setCustomConfig(userCustomConfig);
        return talentPipelineColumnConfigDTO;
    }


    @Override
    public SystemConfigDTO getTalentPipelineColumnConfigByTenantId(Long tenantId){
        if(applicationIPGProperties.getIpgRuleTenantIds().contains(tenantId)){
            return getSystemDefaultPipelineColumnConfig(PIPELINE_CUSTOMIZED_COLUMN);
        }else{
            return getSystemDefaultPipelineColumnConfig(PIPELINE_COLUMN);
        }
    }

    private SystemConfigDTO getSystemDefaultPipelineColumnConfig(ConfigSubcategory configSubcategory){
        return systemConfigDefaultMapper.toDto(systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, configSubcategory));
    }


    @Override
    public BaseConfig updatePipelineColumnConfig(PipelineColumnPreferenceDTO pipelineColumnPreferenceDTO, Long userId) {
         if (SecurityUtils.getUserId() == null || SecurityUtils.getUserId().longValue() <= 0 ){
             throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_PIPELINE_FINDBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        UserPreference userPreference = userPreferenceRepository.findByUserId(SecurityUtils.getUserId());
        if (userPreference != null) {
            userPreference.setTalentPipelineColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(pipelineColumnPreferenceDTO.getCustomConfig()));
        } else {
            userPreference = pipelineColumnConfigMapper.toEntity(pipelineColumnPreferenceDTO);
            userPreference.setUserId(userId);
        }

        userPreference = userPreferenceRepository.saveAndFlush(userPreference);
        pipelineColumnPreferenceDTO = pipelineColumnConfigMapper.toDto(userPreference);
        return pipelineColumnPreferenceDTO;
    }

    @Override
    public PageConfig updatePipelinePageConfig(PageConfig pageConfig) {
        Long userId = SecurityUtils.getUserId();
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setId(userId);
        }

        UserCustomConfig userCustomConfig = Optional.ofNullable(userPreference.getTalentPipelineColumnConfig())
                .map(JsonMapperUtil::jsonStringToCustomColumnConfig)
                .orElse(new UserCustomConfig());

        //init the column config for update page only
        if (userCustomConfig.getColumnConfig() == null) {
            PipelineColumnPreferenceDTO pipelineColumnPreferenceDTO = convertSystemConfigDTOToPipelineColumnConfigDTO(getTalentPipelineColumnConfigByTenantId(SecurityUtils.getTenantId()).getCustomConfig());
            userCustomConfig.setColumnConfig(pipelineColumnPreferenceDTO.getCustomConfig().getColumnConfig());
        }
        userCustomConfig.setPageConfig(pageConfig);

        userPreference.setTalentPipelineColumnConfig(JsonMapperUtil.customColumnConfigToJsonString(userCustomConfig));
        userPreference = userPreferenceRepository.saveAndFlush(userPreference);

        PipelineColumnPreferenceDTO pipelineColumnPreferenceDTO = pipelineColumnConfigMapper.toDto(userPreference);

        return pipelineColumnPreferenceDTO.getCustomConfig().getPageConfig();
    }


}
