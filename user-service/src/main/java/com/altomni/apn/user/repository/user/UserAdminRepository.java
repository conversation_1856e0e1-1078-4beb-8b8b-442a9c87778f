package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.UserAdmin;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA repository for the {@link UserAdmin} entity.
 */
@Repository
public interface UserAdminRepository extends JpaRepository<UserAdmin, Long> {
    String USERS_BY_LOGIN_CACHE = "usersByLogin";

    String USERS_BY_EMAIL_CACHE = "usersByEmail";

    Optional<UserAdmin> findOneByActivationKey(String activationKey);

    List<UserAdmin> findAllByActivatedIsFalseAndActivationKeyIsNotNullAndCreatedDateBefore(Instant dateTime);

    Optional<UserAdmin> findOneByResetKey(String resetKey);

    Optional<UserAdmin> findOneByEmailIgnoreCase(String email);

    Optional<UserAdmin> findOneByUsername(String username);

    @EntityGraph(attributePaths = "roles")
    @Query("select user from UserAdmin user where user.username = :login or user.email = :login")
    Optional<UserAdmin> findOneWithRolesByUsernameOrEmail(@Param("login") String login);

    //@EntityGraph(attributePaths = "authorities")
    //@Cacheable(cacheNames = USERS_BY_LOGIN_CACHE)
    @Query("select user from UserAdmin user join fetch user.roles where user.id = :id")
    Optional<UserAdmin> findOneWithRolesById(@Param("id") Long id);

    //@EntityGraph(attributePaths = "authorities")
    //@Cacheable(cacheNames = USERS_BY_EMAIL_CACHE)
    Optional<UserAdmin> findOneWithAuthoritiesByEmailIgnoreCase(String email);

    Page<UserAdmin> findAllByIdNotNullAndActivatedIsTrue(Pageable pageable);

    @Query("select ua from UserAdmin ua" +
            " left join UserAdminRole uar on uar.userId = ua.id" +
            " left join Role r on r.id=uar.roleId" +
            " where r.name in :roleNames")
    List<UserAdmin> findByRoleNameIn(@Param("roleNames") List<String> roleNames);
}
