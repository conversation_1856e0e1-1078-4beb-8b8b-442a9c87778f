package com.altomni.apn.user.service.dto.permission;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PermissionTeamMemberWithDeliveryLabelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String firstName;

    private String lastName;

    private String roles;

    private Boolean activated;

    private String email;

    private List<Long> languages;

    private Long levelOfExperience;

    private List<DeliveryCountDTO> deliveryLocations;

    private List<DeliveryCountDTO> deliveryProcessIds;

    private List<DeliveryCountDTO> deliveryIndustries;

    private List<DeliveryCountDTO> deliveryJobFunctions;

    private Boolean isUpdatable;

    /**
     * teams 优化需求,增加字段
     */
    private Instant createdDate;

    private String jobTitle;

    //主团队id
    private Long primaryTeamId;

    //当前用户除主团队以外的团队ids
    private List<Long> noPrimaryTeamIds;

    //主团队名称
    private String primaryTeamName;

    //当前团队领导Ids
    private List<Long> teamLeadIds;

    //上级
    private List<Long> primaryTeamLeadIds;

    private Boolean leaderFlag;

}
