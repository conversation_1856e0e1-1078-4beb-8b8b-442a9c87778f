package com.altomni.apn.user.web.rest.permission;

import com.altomni.apn.common.config.PrivilegeName;
import com.altomni.apn.common.config.constants.RedisConstants;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.user.domain.permission.PermissionImpersonationPrivilege;
import com.altomni.apn.user.service.permission.PermissionImpersonationPrivilegeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * REST controller for managing permissions.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/impersonation/privileges")
public class PermissionImpersonationPrivilegeResource {

    @Resource
    private PermissionImpersonationPrivilegeService impersonationPrivilegeService;

    @Resource
    private CommonRedisService commonRedisService;

    @PrivilegeName(value = "Save Impersonation Privilege")
    @PostMapping("")
    public ResponseEntity<List<PermissionImpersonationPrivilege>> savePrivilege(@RequestBody List<PermissionImpersonationPrivilege> impersonationPrivileges){
        log.info("REST request to save impersonation privileges {}", impersonationPrivileges);
        final List<PermissionImpersonationPrivilege> permissionImpersonationPrivileges = impersonationPrivilegeService.saveAll(impersonationPrivileges);
        commonRedisService.delete(RedisConstants.DATA_KEY_IMPERSONATION_PRIVILEGES);
        return ResponseEntity.ok(permissionImpersonationPrivileges);
    }

    @PrivilegeName(value = "Get Impersonation Privileges")
    @GetMapping("")
    public ResponseEntity<List<PermissionImpersonationPrivilege>> getPrivileges(){
        log.info("REST request to get privilege");
        return ResponseEntity.ok(impersonationPrivilegeService.findAll());
    }

}