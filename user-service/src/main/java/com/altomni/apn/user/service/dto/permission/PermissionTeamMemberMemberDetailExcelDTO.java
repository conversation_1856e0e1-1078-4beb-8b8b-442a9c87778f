package com.altomni.apn.user.service.dto.permission;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.user.web.rest.vm.user.DeliveryStatVM;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class PermissionTeamMemberMemberDetailExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String fullName;

    private String email;

    private String userTeam;

    private String teamLeader;

    private String jobTitle;

    private Instant createdDate;

    private String levelOfExperience;

    private String languages;

    private List<DeliveryStatVM> processDeliveryCounts;

    private List<DeliveryStatVM> countryDeliveryCounts;

    public static PermissionTeamMemberMemberDetailExcelDTO from(PermissionTeamMemberMemberDetailDTO dto, Map<Long,String> levelOfExperienceMap){
        PermissionTeamMemberMemberDetailExcelDTO excelRow = new PermissionTeamMemberMemberDetailExcelDTO();
        excelRow.setId(dto.getId());
        excelRow.setFullName(CommonUtils.formatFullNameWithBlankCheck(dto.getFirstName(), dto.getLastName()));
        excelRow.setEmail(dto.getEmail());
        excelRow.setUserTeam(dto.getUserTeam());
        if (CollUtil.isNotEmpty(dto.getTeamLeader())){
            excelRow.setTeamLeader(String.join(",",dto.getTeamLeader()));
        }
        excelRow.setJobTitle(dto.getJobTitle());
        excelRow.setCreatedDate(dto.getActivateDate());
        excelRow.setLevelOfExperience(dto.getLevelOfExperienceSort());
        if (StringUtils.isNotEmpty(dto.getLanguagesSort())){
            excelRow.setLanguages(dto.getLanguagesSort());
        }
        excelRow.setProcessDeliveryCounts(dto.getProcessDeliveryCounts());
        excelRow.setCountryDeliveryCounts(dto.getCountryDeliveryCounts());
        return excelRow;
    }
}
