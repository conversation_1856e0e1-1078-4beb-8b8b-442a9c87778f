package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.user.domain.customconfig.JobFormConfig;
import com.altomni.apn.user.domain.customconfig.JobFormConfig;
import com.altomni.apn.user.service.dto.customconfig.JobFormConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.JobFormConfigDTO;
import org.mapstruct.*;
@Mapper(componentModel = "spring", uses = JsonMapperUtil.class)
public interface JobFormConfigMapper {

    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "customFormFieldListToJsonString")
    JobFormConfig toEntity(JobFormConfigDTO dto);

    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "jsonStringToCustomFormFieldList")
    JobFormConfigDTO toDto(JobFormConfig entity);

    @Mapping(target = "customConfig", source = "privateJobCustomConfig", qualifiedByName = "jsonStringToCustomFormFieldList")
    JobFormConfigDTO toDtoForPrivateJob(JobFormConfig entity);

    default JobFormConfig fromId(Long id) {
        if (id == null) {
            return null;
        }
        JobFormConfig jobFormConfig = new JobFormConfig();
        jobFormConfig.setId(id);
        return jobFormConfig;
    }

}
