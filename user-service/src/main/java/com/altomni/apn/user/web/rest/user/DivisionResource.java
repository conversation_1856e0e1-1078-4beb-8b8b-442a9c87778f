package com.altomni.apn.user.web.rest.user;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.user.domain.user.Division;
import com.altomni.apn.user.repository.user.DivisionRepository;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Division.
 */
@Deprecated
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class DivisionResource {

    private static final String ENTITY_NAME = "division";

    @Resource
    private DivisionRepository divisionRepository;

    /**
     * POST  /divisions : Create a new division.
     *
     * @param division the division to create
     * @return the ResponseEntity with status 201 (Created) and with body the new division, or with status 400 (Bad Request) if the division has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/divisions")
    @NoRepeatSubmit
    public ResponseEntity<Division> createDivision(@RequestBody Division division) throws URISyntaxException {
        log.info("[APN: Division @{}] REST request to save Division : {}", SecurityUtils.getUserId(), division);
        if (division.getId() != null) {
            throw new CustomParameterizedException("A new division cannot already have an ID", ENTITY_NAME, "id exists");
        }

        if (division.getTenantId() == null) {
            division.setTenantId(SecurityUtils.getTenantId());
        }

        Division result = divisionRepository.save(division);
        return ResponseEntity.created(new URI("/api/divisions/" + result.getId()))
            .body(result);
    }

    /**
     * PUT  /divisions : Updates an existing division.
     *
     * @param division the division to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated division,
     * or with status 400 (Bad Request) if the division is not valid,
     * or with status 500 (Internal Server Error) if the division couldn't be updated
     */
    @PutMapping("/divisions/{id}")
    @NoRepeatSubmit
    public ResponseEntity<Division> updateDivision(@PathVariable Long id, @RequestBody Division division) {
        log.info("[APN: Division @{}] REST request to update Division : {}", SecurityUtils.getUserId(), division);
        Division exists = divisionRepository.findById(id).orElse(null);
        if (exists == null) {
            throw new CustomParameterizedException("Division to update by id does not exists.");
        }
        ServiceUtils.myCopyProperties(division, exists, Division.UpdateSkipProperties);
        Division result = divisionRepository.save(exists);
        return ResponseEntity.ok()
            .body(result);
    }

    /**
     * GET  /divisions : get all the divisions.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of divisions in body
     */
    @GetMapping("/divisions")
    public List<Division> getAllDivisions() {
        log.info("[APN: Division @{}] REST request to get all Divisions", SecurityUtils.getUserId());
        return divisionRepository.findByTenantId(SecurityUtils.getTenantId());
    }

    /**
     * GET  /divisions/:id : get the "id" division.
     *
     * @param id the id of the division to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the division, or with status 404 (Not Found)
     */
    @GetMapping("/divisions/{id}")
    public ResponseEntity<Division> getDivision(@PathVariable Long id) {
        log.info("[APN: Division @{}] REST request to get Division : {}", SecurityUtils.getUserId(), id);
        Division division = divisionRepository.findById(id).orElse(null);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(division));
    }

    /**
     * DELETE  /divisions/:id : delete the "id" division.
     *
     * @param id the id of the division to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/divisions/{id}")
    @NoRepeatSubmit
    public ResponseEntity<Void> deleteDivision(@PathVariable Long id) {
        log.info("[APN: Division @{}] REST request to delete Division : {}", SecurityUtils.getUserId(), id);
        divisionRepository.deleteById(id);
        return ResponseEntity.ok().build();
    }
}
