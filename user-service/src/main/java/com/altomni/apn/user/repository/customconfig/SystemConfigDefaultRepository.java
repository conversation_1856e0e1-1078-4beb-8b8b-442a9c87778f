package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data R2DBC repository for the SystemConfigDefault entity.
 */

@Repository
public interface SystemConfigDefaultRepository extends JpaRepository<SystemConfigDefault, Long> {
    List<SystemConfigDefault> findAllByIdIn(List<Long> ids);

    List<SystemConfigDefault> findByCategory(Category category);

    SystemConfigDefault findByCategoryAndSubcategory(Category category, ConfigSubcategory subCategory);
}

