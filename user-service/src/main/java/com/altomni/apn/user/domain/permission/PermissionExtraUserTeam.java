package com.altomni.apn.user.domain.permission;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.enumeration.permission.ModuleConverter;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Entity
@Table(name = "permission_extra_user_team")
public class PermissionExtraUserTeam extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "module")
    @Convert(converter = ModuleConverter.class)
    private Module module;

    @Column(name = "team_id")
    private Long teamId;

    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "writable")
    private Boolean writable;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PermissionExtraUserTeam role = (PermissionExtraUserTeam) o;
        return id.equals(role.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

}
