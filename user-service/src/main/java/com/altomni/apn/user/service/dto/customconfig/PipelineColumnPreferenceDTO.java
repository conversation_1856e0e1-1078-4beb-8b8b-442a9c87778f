package com.altomni.apn.user.service.dto.customconfig;

import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PipelineColumnPreferenceDTO implements BaseConfig, Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "data id")
    private Long id;

    @ApiModelProperty(value = "template name")
    private String templateName;

    private UserCustomConfig customConfig;

}
