package com.altomni.apn.user.service.dto.customconfig;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * report G 系列
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReportGSeriesColumnConfigDTO implements Serializable {

    private String monthlyRevenueDetailColumnConfig;

    private String quarterlyNewHiresColumnConfig;

    private String quarterlyRenewalsColumnConfig;

    private String quarterlyOffboardingColumnConfig;

}
