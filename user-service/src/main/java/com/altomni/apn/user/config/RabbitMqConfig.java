package com.altomni.apn.user.config;

import com.altomni.apn.user.config.env.UserConsumerMQProperties;
import com.altomni.apn.user.config.rabbit.SsoRabbitProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AcknowledgeMode;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.amqp.SimpleRabbitListenerContainerFactoryConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class RabbitMqConfig {

    @Resource
    UserConsumerMQProperties userConsumerMQProperties;
    @Resource
    SsoRabbitProperties ssoRabbitProperties;

    /****** start user consumer mq config  *******/
    @Bean(name = "userConsumerConnectionFactory")
    public ConnectionFactory userConsumerConnectionFactory() {
        return userConsumerConnectionFactory(userConsumerMQProperties.getHost(), userConsumerMQProperties.getPort(), userConsumerMQProperties.getVirtualHost(), userConsumerMQProperties.getUserName(), userConsumerMQProperties.getPassword());
    }

    public CachingConnectionFactory userConsumerConnectionFactory(String host, int port, String virtualHost, String username, String password) {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setHost(host);
        connectionFactory.setPort(port);
        connectionFactory.setVirtualHost(virtualHost);
        connectionFactory.setUsername(username);
        connectionFactory.setPassword(password);
        connectionFactory.setPublisherConfirmType(CachingConnectionFactory.ConfirmType.CORRELATED);
        connectionFactory.setPublisherReturns(true);
        connectionFactory.setPublisherConfirms(true);
        return connectionFactory;
    }

    @Bean(name = "userConsumerFactory")
    public SimpleRabbitListenerContainerFactory userConsumerFactory(
            SimpleRabbitListenerContainerFactoryConfigurer configurer,
            @Qualifier("userConsumerConnectionFactory") ConnectionFactory connectionFactory
    ) {
        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setAcknowledgeMode(AcknowledgeMode.MANUAL);
        factory.setConcurrentConsumers(1);
        factory.setMaxConcurrentConsumers(10);
        factory.setPrefetchCount(1);
        configurer.configure(factory, connectionFactory);
        return factory;
    }

    @Bean(name = "userConsumerAdmin")
    public RabbitAdmin emailRabbitAdmin(@Qualifier("userConsumerConnectionFactory") ConnectionFactory connectionFactory) {
        RabbitAdmin rabbitAdmin = new RabbitAdmin(connectionFactory);
        rabbitAdmin.setAutoStartup(true);
        rabbitAdmin.declareExchange(new FanoutExchange(ssoRabbitProperties.getSsoUserInfoExchange()));
        rabbitAdmin.declareExchange(new FanoutExchange(ssoRabbitProperties.getSsoUserActiveEchange()));
        rabbitAdmin.declareExchange(new FanoutExchange(ssoRabbitProperties.getSsoUserBingClientExchange()));
        rabbitAdmin.declareQueue(new Queue(ssoRabbitProperties.getSsoUserInfoQueue(), true));
        rabbitAdmin.declareQueue(new Queue(ssoRabbitProperties.getSsoUserActiveQueue(), true));
        rabbitAdmin.declareQueue(new Queue(ssoRabbitProperties.getSsoUserBingClientQueue(), true));
        rabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(ssoRabbitProperties.getSsoUserInfoQueue())).to(new FanoutExchange(ssoRabbitProperties.getSsoUserInfoExchange())));
        rabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(ssoRabbitProperties.getSsoUserActiveQueue())).to(new FanoutExchange(ssoRabbitProperties.getSsoUserActiveEchange())));
        rabbitAdmin.declareBinding(BindingBuilder.bind(new Queue(ssoRabbitProperties.getSsoUserBingClientQueue())).to(new FanoutExchange(ssoRabbitProperties.getSsoUserBingClientExchange())));
        return rabbitAdmin;
    }


    @Bean(name = "userConsumerRabbitTemplate")
    public RabbitTemplate userConsumerRabbitTemplate(
            @Qualifier("userConsumerConnectionFactory") ConnectionFactory connectionFactory
    ) {
        RabbitTemplate userConsumerRabbitTemplate = new RabbitTemplate(connectionFactory);
        return userConsumerRabbitTemplate;
    }

}