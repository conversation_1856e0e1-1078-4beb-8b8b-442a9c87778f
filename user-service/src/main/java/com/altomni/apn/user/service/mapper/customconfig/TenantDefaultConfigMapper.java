package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.domain.config.TenantDefaultConfig;
import com.altomni.apn.user.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {})
public interface TenantDefaultConfigMapper extends EntityMapper<TenantConfigDTO, TenantDefaultConfig> {

    default TenantDefaultConfig fromId(Long id) {
        if (id == null) {
            return null;
        }
        TenantDefaultConfig tenantDefaultConfig = new TenantDefaultConfig();
        tenantDefaultConfig.setId(id);
        return tenantDefaultConfig;
    }
}
