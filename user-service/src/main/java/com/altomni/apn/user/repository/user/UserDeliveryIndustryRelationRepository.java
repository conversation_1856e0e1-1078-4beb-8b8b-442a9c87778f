package com.altomni.apn.user.repository.user;

import com.altomni.apn.common.domain.user.UserDeliveryIndustryRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Set;


@Repository
public interface UserDeliveryIndustryRelationRepository extends JpaRepository<UserDeliveryIndustryRelation, Long> {
    List<UserDeliveryIndustryRelation> findAllByUserId(Long userId);

    List<UserDeliveryIndustryRelation> findAllByUserIdAndTop(Long userId, Boolean top);

    List<UserDeliveryIndustryRelation> findAllByUserIdIn(Collection<Long> userIds);

    List<UserDeliveryIndustryRelation> findAllByUserIdInAndTop(Collection<Long> userIds, Boolean top);
}
