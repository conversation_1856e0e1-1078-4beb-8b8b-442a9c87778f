package com.altomni.apn.user.repository.permission;

import com.alibaba.nacos.shaded.com.google.common.collect.FluentIterable;
import com.altomni.apn.common.domain.user.UserRole;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface PermissionUserRoleRepository extends JpaRepository<UserRole, Integer> {

    void deleteAllByUserId(Long userId);

    @Query("select u.dataScope from User u where u.id=:userId")
    Integer findUserDataScopeById(@Param("userId") Long userId);

    @Modifying
    @Query("update User u set u.dataScope=:dataScope, u.clientContactDataScope=:clientContactDataScope, u.reportDataScope=:reportDataScope,u.homeAndCalendarDataScope=:homeAndCalendarDataScope,u.candidatePipelineManagementDataScope=:candidatePipelineManagementDataScope,u.chinaInvoicingDataScope=:chinaInvoicingDataScope where u.id=:userId")
    void updateUserDataScopeById(@Param("dataScope") Integer dataScope, @Param("clientContactDataScope") Integer clientContactDataScope, @Param("reportDataScope") Integer reportDataScope,@Param("homeAndCalendarDataScope") Integer homeAndCalendarDataScope,@Param("candidatePipelineManagementDataScope") Integer candidatePipelineManagementDataScope,@Param("chinaInvoicingDataScope") Integer chinaInvoicingDataScope, @Param("userId") Long userId);

    @Modifying
    @Query("update User u set u.clientContactDataScope=:dataScope where u.id=:userId")
    void updateUserClientContactDataScopeById(@Param("dataScope") Integer dataScope, @Param("userId") Long userId);

    @Query(value = "SELECT ur.user_id FROM user_role ur" +
            " LEFT JOIN permission_role_privilege rp ON rp.role_id = ur.role_id" +
            " WHERE rp.privilege_id=:privilegeId", nativeQuery = true)
    List<Long> findUserIdsByPrivilegeId(@Param("privilegeId") Long privilegeId);

    Integer countByRoleId(Long roleId);

    List<UserRole> findAllByRoleId(Long roleId);

    Boolean existsByUserId(Long userId);

    List<UserRole> findAllByUserIdIn(Set<Long> userIds);

    @Query(value = """
            SELECT
            	count( 1 )
            FROM
            	user_role ur
            	LEFT JOIN permission_role_privilege rp ON ur.role_id = rp.role_id
            	LEFT JOIN role r ON ur.role_id = r.id
            WHERE
            	rp.privilege_id =:privilegeId
            	AND ur.user_id =:userId
                AND r.id !=:roleId
            	AND r.STATUS = 1
                """, nativeQuery = true)
    Integer countPrivilegeByUserId(@Param("userId")Long userId, @Param("privilegeId")Long privilegeId, @Param("roleId")Long roleId);

    @Query(value = """
            SELECT
            	rp.privilege_id
            FROM
            	user_role ur
            	LEFT JOIN permission_role_privilege rp ON ur.role_id = rp.role_id
            	LEFT JOIN role r ON ur.role_id = r.id
            WHERE
            	ur.user_id =:userId
            	AND r.STATUS = 1
                """, nativeQuery = true)
    Set<Long> getAllPrivilegeByUserId(@Param("userId")Long userId);
}
