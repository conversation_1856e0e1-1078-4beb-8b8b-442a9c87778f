package com.altomni.apn.user.domain.customconfig;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A Job Column Config.
 */
@ApiModel(description = "User preferred config settings, including job, talent")
@Data
@Entity
@Table(name = "user_preference")
public class UserPreference extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "user Id, must not be null")
    @Column(name = "user_id")
    private Long userId;

    /**
     * Job specify column
     */
    @ApiModelProperty("recruitment process which the user recently used for creating a job")
    @Column(name = "recruitment_process_id")
    private Long recruitmentProcessId;

    @ApiModelProperty("recruitment process which the user recently used for creating a private job")
    @Column(name = "private_job_recruitment_process_id")
    private Long privateJobRecruitmentProcessId;

    @ApiModelProperty("store raw json about user preferred job column config and convert it into class in DTO")
    @Column(name = "job_column_config")
    @JsonRawValue
    private String jobColumnConfig;

    @Column(name = "private_job_column_config")
    @JsonRawValue
    private String privateJobColumnConfig;

    @Column(name = "company_job_column_config")
    @JsonRawValue
    private String companyJobColumnConfig;

    /**
     * talent specify column
     */
    @ApiModelProperty("store raw json config about user preferred talent column for my and all talent column and convert it into class in DTO")
    @Column(name = "talent_column_config")
    @JsonRawValue
    private String talentColumnConfig;

    @ApiModelProperty("store raw json config about user preferred talent column for database talent and convert it into class in DTO")
    @Column(name = "talent_database_column_config")
    @JsonRawValue
    private String talentDatabaseColumnConfig;

    @ApiModelProperty("store raw json config about user preferred talent column for database talent and convert it into class in DTO")
    @Column(name = "talent_association_job_folder_column_config")
    @JsonRawValue
    private String talentRelateJobFolderColumnConfig;

    @ApiModelProperty("store raw json config about user preferred talent column for search folder and convert it into class in DTO")
    @Column(name = "talent_search_folder_column_config")
    @JsonRawValue
    private String talentSearchFolderColumnConfig;


    @ApiModelProperty("store raw json config about user preferred talent column for pipeline talent and convert it into class in DTO")
    @Column(name = "talent_pipeline_column_config")
    @JsonRawValue
    private String talentPipelineColumnConfig;

    @ApiModelProperty("store raw json about user preferred company client column config and convert it into class in DTO")
    @Column(name = "company_client_column_config")
    @JsonRawValue
    private String companyClientColumnConfig;

    @ApiModelProperty("store raw json about user preferred company prospect column config and convert it into class in DTO")
    @Column(name = "company_prospect_column_config")
    @JsonRawValue
    private String companyProspectColumnConfig;

    @ApiModelProperty("store raw json about user preferred company affiliates column config and convert it into class in DTO")
    @Column(name = "company_affiliates_column_config")
    @JsonRawValue
    private String companyAffiliatesColumnConfig;

    @ApiModelProperty("store raw json about user preferred dashboard column config and convert it into class in DTO")
    @Column(name = "dashboard_column_config")
    @JsonRawValue
    private String dashboardColumnConfig;

    @ApiModelProperty("store raw json about user preferred fte invoice column config and convert it into class in DTO")
    @Column(name = "invoice_column_config")
    @JsonRawValue
    private String invoiceColumnConfig;

    @ApiModelProperty("store raw json about user preferred expense report column config and convert it into class in DTO")
    @Column(name = "expense_report_column_config")
    @JsonRawValue
    private String expenseReportColumnConfig;

    @ApiModelProperty("store raw json about user preferred expense company current contractor column config and convert it into class in DTO")
    @Column(name = "company_current_contractor_column_config")
    @JsonRawValue
    private String companyCurrentContractorColumnConfig;

    @ApiModelProperty("store raw json about user preferred fte bd report column config and convert it into class in DTO")
    @Column(name = "company_fte_bd_report_column_config")
    @JsonRawValue
    private String companyFteBdReportColumnConfig;


    @ApiModelProperty("user personalization config")
    @Column(name = "personalization_config")
    @JsonRawValue
    private String personalizationConfig;

    @ApiModelProperty("china area invoicing personalization config")
    @Column(name = "invoicing_column_config")
    @JsonRawValue
    private String invoicingColumnConfig;

    @ApiModelProperty("calendar personalization config")
    @Column(name = "calendar_column_config")
    @JsonRawValue
    private String calendarColumnConfig;

    @ApiModelProperty("user label column config")
    @Column(name = "user_search_column_config")
    @JsonRawValue
    private String userSearchColumnConfig;

    @ApiModelProperty("all user label column config")
    @Column(name = "user_search_all_column_config")
    @JsonRawValue
    private String userSearchAllColumnConfig;

    /**
     * G1 report
     */
    @Column(name = "monthly_revenue_detail_column_config")
    @JsonRawValue
    private String monthlyRevenueDetailColumnConfig;

    /**
     * G1 report
     */
    @Column(name = "quarterly_new_hires_column_config")
    @JsonRawValue
    private String quarterlyNewHiresColumnConfig;

    /**
     * G1 report
     */
    @Column(name = "quarterly_renewals_column_config")
    @JsonRawValue
    private String quarterlyRenewalsColumnConfig;

    /**
     * G1 report
     */
    @Column(name = "quarterly_offboarding_column_config")
    @JsonRawValue
    private String quarterlyOffboardingColumnConfig;

    /**
     * 用户是否习惯姓名分开录入  true 分开录入; false合并录入  默认true
     */
    @Column(name = "separate_name_input")
    private Boolean separateNameInput = true;
}
