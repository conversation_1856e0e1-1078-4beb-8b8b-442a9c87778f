package com.altomni.apn.user.web.rest.permission;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.service.dto.permission.PermissionTableDTO;
import com.altomni.apn.user.service.permission.PermissionTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * REST controller for managing permissions.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/tables")
public class PermissionTableResource {

    @Resource
    private PermissionTableService permissionTableService;

    @PostMapping("")
    public ResponseEntity<PermissionTableDTO> createTable(@RequestBody PermissionTableDTO permissionTableDTO) {
        log.info("REST request to create a table record : {}", permissionTableDTO);
        return ResponseEntity.ok(permissionTableService.create(permissionTableDTO));
    }

    @PutMapping("")
    public ResponseEntity<PermissionTableDTO> updateTable(@RequestBody PermissionTableDTO permissionTableDTO) {
        log.info("REST request to update a table record : Tenant id: {}, UserId: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        return ResponseEntity.ok(permissionTableService.update(permissionTableDTO));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTable(@PathVariable("id") Long id) {
        log.info("REST request to delete a table record : Tenant id: {}, UserId: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        permissionTableService.delete(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("")
    public ResponseEntity<List<PermissionTableDTO>> getTables() {
        log.info("REST request to getTables : Tenant id: {}, UserId: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId());
        return ResponseEntity.ok(permissionTableService.findAll());
    }

}
