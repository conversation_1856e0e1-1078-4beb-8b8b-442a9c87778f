package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.SystemConfigDefaultRepository;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.customconfig.UserLabelReportColumnConfigService;
import com.altomni.apn.user.service.dto.customconfig.UserSearchColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.UserSearchColumnConfigVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class UserLabelReportColumnConfigServiceImpl implements UserLabelReportColumnConfigService {

    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    private SystemConfigDefaultRepository systemConfigDefaultRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public UserSearchColumnConfigVO searchConfigByUserId(Long userId, ConfigSubcategory configSubcategory) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        //记录不存在 user_preference 表的记录
        boolean notExistsFlag = userPreference == null;
        String config = null;
        //获取对应用户个性化配置
        if (!notExistsFlag) {
            switch (configSubcategory) {
                case USER_SEARCH_COLUMN:
                    config = userPreference.getUserSearchColumnConfig();
                    break;
                case USER_SEARCH_ALL_COLUMN:
                    config = userPreference.getUserSearchAllColumnConfig();
                    break;
                default:
                    throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_SEARCHDASHBOARDCOLUMNCONFIGBYUSERID_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
            }
        }
        if (ObjectUtil.isNotNull(config)) {
            // 用户偏好设置存在且配置不为空，直接返回
            return new UserSearchColumnConfigVO(config);
        }

        //根据细分列配置，查询默认配置
        Optional<SystemConfigDefault> systemConfigDefaultOptional = Optional.ofNullable(systemConfigDefaultRepository.findByCategoryAndSubcategory(Category.USER_SEARCH_COLUMN, configSubcategory));
        return systemConfigDefaultOptional.map(systemConfigDefault -> {
            String defaultConfig = systemConfigDefault.getDefaultConfig();
            if (notExistsFlag) {
                //从来没有记录
                UserPreference addUserPreference = new UserPreference();
                addUserPreference.setUserId(userId);
                switch (configSubcategory) {
                    case USER_SEARCH_COLUMN:
                        addUserPreference.setUserSearchColumnConfig(systemConfigDefault.getDefaultConfig());
                        break;
                    case USER_SEARCH_ALL_COLUMN:
                        addUserPreference.setUserSearchAllColumnConfig(systemConfigDefault.getDefaultConfig());
                        break;
                    default:
                        throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_SEARCHDASHBOARDCOLUMNCONFIGBYUSERID_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
                }
                userPreferenceRepository.save(addUserPreference);
            } else {
                //已经有过其他记录
                updateConfigByUserId(userId, new UserSearchColumnConfigDTO(systemConfigDefault.getDefaultConfig(), configSubcategory));
            }
            return new UserSearchColumnConfigVO(defaultConfig);
        }).orElseThrow(() -> {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_SEARCHDASHBOARDCOLUMNCONFIGBYUSERID_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        });

    }

    @Override
    public void updateConfigByUserId(Long userId, UserSearchColumnConfigDTO dto) {
        switch (dto.getConfigSubcategory()) {
            case USER_SEARCH_COLUMN:
                userPreferenceRepository.updateUserSearchColumnConfigByUserId(userId, dto.getUserSearchColumnConfig());
                break;
            case USER_SEARCH_ALL_COLUMN:
                userPreferenceRepository.updateUserSearchAllColumnConfigByUserId(userId, dto.getUserSearchColumnConfig());
                break;
            default:
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_SEARCHDASHBOARDCOLUMNCONFIGBYUSERID_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), userApiPromptProperties.getUserService()));
        }
    }

}
