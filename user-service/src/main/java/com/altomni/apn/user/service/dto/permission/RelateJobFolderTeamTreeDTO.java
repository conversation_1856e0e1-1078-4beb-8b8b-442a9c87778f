package com.altomni.apn.user.service.dto.permission;

import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.user.web.rest.vm.permission.RelateJobFolderUserInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RelateJobFolderTeamTreeDTO {
    private Long id;

    private String name;

    private String code;

    private Long parentId;

    private JSONArray leaderUserIds;

    private Boolean isLeaf;

    private Integer level;

    private Boolean hasPermission;

    private List<RelateJobFolderTeamTreeDTO> children;

    List<RelateJobFolderUserInfo> data;

    private Boolean teamCategoryEnable;

}
