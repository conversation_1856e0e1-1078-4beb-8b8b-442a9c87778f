package com.altomni.apn.user.service.permission;

import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.user.service.dto.permission.PermissionModulePrivilegeDTO;
import com.altomni.apn.common.domain.permission.PermissionModulePrivilegePageVM;

import java.util.List;

public interface PermissionModulePrivilegeService {

    List<PermissionModulePrivilegeDTO> searchTenantModuleLis(Long tenantId);

    void updateTenantModuleList(Long tenantId, List<PermissionModulePrivilegeDTO> tenantModuleDTOList);

    List<PermissionModulePrivilegePageVM> findTenantDefaultModuleList(Long tenantId, TenantUserTypeEnum type);

}
