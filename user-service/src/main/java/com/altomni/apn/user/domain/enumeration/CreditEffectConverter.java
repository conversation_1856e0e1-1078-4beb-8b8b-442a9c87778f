package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

/**
 * <AUTHOR>
 */
@Converter
public class CreditEffectConverter extends AbstractAttributeConverter<CreditEffectType, Integer> {
    public CreditEffectConverter() {
        super(CreditEffectType::toDbValue, CreditEffectType::fromDbValue);
    }
}
