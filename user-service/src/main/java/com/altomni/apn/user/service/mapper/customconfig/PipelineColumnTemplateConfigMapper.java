package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.user.domain.customconfig.PipelineColumnPreference;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.service.dto.customconfig.PipelineColumnPreferenceDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;


@Mapper(componentModel = "spring", uses = JsonMapperUtil.class)
public interface PipelineColumnTemplateConfigMapper {


    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "customColumnConfigToJsonString")
    PipelineColumnPreference toEntity(PipelineColumnPreferenceDTO dto);

    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "jsonStringToCustomColumnConfig")
    PipelineColumnPreferenceDTO toDto(PipelineColumnPreference entity);

    List<PipelineColumnPreferenceDTO> toDto(List<PipelineColumnPreference> entity);

}