package com.altomni.apn.user.service.mapper.permission;
import com.altomni.apn.user.domain.permission.PermissionPrivilege;
import com.altomni.apn.user.service.dto.permission.PermissionPrivilegeDTO;
import com.altomni.apn.user.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity Tag and its DTO TagDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface PrivilegeMapper extends EntityMapper<PermissionPrivilegeDTO, PermissionPrivilege> {

}
