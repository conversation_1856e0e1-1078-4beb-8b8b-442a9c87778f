package com.altomni.apn.user.web.rest.vm.user;

import com.altomni.apn.user.domain.enumeration.CreditEffectType;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

@Data
public class UserVM implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String firstName;

    private String lastName;

    private String fullname;

    private String username;

    private String email;

    private String phone;

    private Long tenantId;

    private boolean activated;

    private String note;

    private Instant createdDate;

    private CreditEffectType creditEffectType;

    private Integer effectCredit;

    private Integer monthlyCredit;

    private Integer bulkCredit;

    private Integer usedMonthlyCredit;

    private Integer usedBulkCredit;

    private Long primaryTeamId;

    private List<Object> authorities;

    private List<Object> teams;

    private String jobTitle;

    private Instant cancellationTime;

    private Long levelOfExperience;

    private List<Long> languages;

    public UserVM(Long id, String firstName, String lastName, String username, String email, String phone, Long tenantId, boolean activated, String note, Instant createdDate, CreditEffectType creditEffectType, Integer effectCredit, Integer monthlyCredit, Integer bulkCredit, Long primaryTeamId) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.fullname = firstName + " " + lastName;
        this.username = username;
        this.email = email;
        this.phone = phone;
        this.tenantId = tenantId;
        this.activated = activated;
        this.note = note;
        this.createdDate = createdDate;
        this.creditEffectType = creditEffectType;
        this.effectCredit = effectCredit;
        this.monthlyCredit = monthlyCredit;
        this.bulkCredit = bulkCredit;
        this.primaryTeamId = primaryTeamId;
    }

    public UserVM(Long id, String firstName, String lastName, String username, String email, String phone, Long tenantId, boolean activated, String note, Instant createdDate, CreditEffectType creditEffectType, Integer effectCredit, Integer monthlyCredit, Integer bulkCredit, Long primaryTeamId, String jobTitle, Instant cancellationTime) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.fullname = firstName + " " + lastName;
        this.username = username;
        this.email = email;
        this.phone = phone;
        this.tenantId = tenantId;
        this.activated = activated;
        this.note = note;
        this.createdDate = createdDate;
        this.creditEffectType = creditEffectType;
        this.effectCredit = effectCredit;
        this.monthlyCredit = monthlyCredit;
        this.bulkCredit = bulkCredit;
        this.primaryTeamId = primaryTeamId;
        this.jobTitle = jobTitle;
        this.cancellationTime = cancellationTime;
    }

    public UserVM(Long id, String firstName, String lastName, String username, String email, String phone, Long tenantId, boolean activated, String note, Instant createdDate, CreditEffectType creditEffectType, Integer effectCredit, Integer monthlyCredit, Integer bulkCredit, Long primaryTeamId, String jobTitle, Instant cancellationTime, Long levelOfExperience) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.fullname = firstName + " " + lastName;
        this.username = username;
        this.email = email;
        this.phone = phone;
        this.tenantId = tenantId;
        this.activated = activated;
        this.note = note;
        this.createdDate = createdDate;
        this.creditEffectType = creditEffectType;
        this.effectCredit = effectCredit;
        this.monthlyCredit = monthlyCredit;
        this.bulkCredit = bulkCredit;
        this.primaryTeamId = primaryTeamId;
        this.jobTitle = jobTitle;
        this.cancellationTime = cancellationTime;
        this.levelOfExperience = levelOfExperience;
    }
}
