package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.domain.enumeration.CustomerType;
import com.altomni.apn.user.service.dto.customconfig.CompanyColumnConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.JobColumnConfigDTO;

public interface CompanyColumnConfigService {

    CompanyColumnConfigDTO getColumnConfigByUserId(CustomerType type, Long userId);
    

    CompanyColumnConfigDTO saveCompanyColumnConfig(CustomerType type, CompanyColumnConfigDTO companyColumnConfigDTO);
}
