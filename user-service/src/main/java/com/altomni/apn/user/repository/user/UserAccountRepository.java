package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.UserAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;


/**
 * Spring Data  repository for the UserAccount entity.
 * <AUTHOR>
 */
@Repository
public interface UserAccountRepository extends JpaRepository<UserAccount, Long> {

    UserAccount findByUserId(Long userId);

    @Query(value = "select t.* from user_account t where t.user_id = ?1 and t.account_status = 0 order by t.id desc limit 1", nativeQuery = true)
    UserAccount findAvailableAccountByUserId(Long userId);

    @Modifying
    @Query(value = "update user_account set amount = :monthlyCredit,bulk_credit=:bulkCredit,effect_credit=:effectCredit,credit_effect_type=:effectCreditType ,version = version + 1 where id = :id and version = :version", nativeQuery = true)
    int updateCreditById(@Param("id") Long id, @Param("monthlyCredit") Integer monthlyCredit, @Param("bulkCredit") Integer bulkCredit, @Param("effectCredit") Integer effectCredit, @Param("effectCreditType") int effectCreditType , @Param("version") int version);

    @Modifying
    @Query(value = "UPDATE UserAccount ua SET ua.monthlyAmount = CASE WHEN ua.effectCredit IS NULL THEN ua.monthlyAmount ELSE ua.effectCredit END, ua.effectCredit = NULL, ua.expireDate = ?1 WHERE ua.userId IN (SELECT u.id FROM User u WHERE u.activated = TRUE) AND ua.expireDate <> ?1")
    int updateActiveUserMonthlyCredit(String expireDate);

    @Modifying
    @Query(value = "UPDATE user_account ua LEFT JOIN user u ON u.id=ua.user_id SET ua.amount = CASE WHEN ua.effect_credit IS NULL THEN ua.amount ELSE ua.effect_credit END, ua.effect_credit = NULL, ua.expire_date = ?1 WHERE u.activated = 1 AND ua.expire_date <> ?1", nativeQuery = true)
    int updateActiveUserMonthlyCreditDebugOnly(String expireDate);
}
