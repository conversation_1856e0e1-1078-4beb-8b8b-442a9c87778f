package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.user.domain.customconfig.TalentFormConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data R2DBC repository for the TalentFormConfig entity.
 */
@Repository
public interface TalentFormConfigRepository extends JpaRepository<TalentFormConfig, Long> {
    List<TalentFormConfig> findAllByIdIn(List<Long> ids);

    TalentFormConfig findByTenantId(Long tenantId);
}

