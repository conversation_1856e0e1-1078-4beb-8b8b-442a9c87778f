package com.altomni.apn.user.repository.user;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.domain.user.*;
import com.altomni.apn.common.dto.user.ReportUserLabelSearchDTO;
import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.dto.user.UserUidNameDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.user.UserTimeZoneVO;
import com.altomni.apn.user.domain.enumeration.AccountStatus;
import com.altomni.apn.user.domain.permission.QPermissionTeam;
import com.altomni.apn.user.domain.permission.QPermissionUserTeam;
import com.altomni.apn.user.domain.user.*;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamMemberMemberDetailDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.service.dto.user.IUserWithRolesAndTeamsDTO;
import com.altomni.apn.user.service.dto.user.UserDTO;
import com.altomni.apn.user.service.dto.user.UserPageVO;
import com.altomni.apn.user.service.dto.user.UserTeamDTO;
import com.altomni.apn.user.service.query.UserSearch;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamDeliverySearchVM;
import com.altomni.apn.user.web.rest.vm.system.OnlineUserVM;
import com.altomni.apn.user.web.rest.vm.user.UserVM;
import com.querydsl.core.BooleanBuilder;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.PathBuilder;
import com.querydsl.core.types.dsl.StringExpression;
import com.querydsl.core.types.dsl.StringPath;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.EntityGraph;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.data.querydsl.binding.QuerydslBinderCustomizer;
import org.springframework.data.querydsl.binding.QuerydslBindings;
import org.springframework.data.querydsl.binding.SingleValueBinding;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.EntityManager;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, QuerydslPredicateExecutor<User>, QuerydslBinderCustomizer<QUser> {
//public interface UserRepository extends JpaRepository<User, Long> {

    String USERS_BY_LOGIN_CACHE = "usersByLogin";

    String USERS_BY_EMAIL_CACHE = "usersByEmail";

    Optional<User> findOneByEmailIgnoreCase(String email);

    Optional<User> findOneByUsername(String username);

    Page<User> findAllByIdNotNullAndActivatedIsTrue(Pageable pageable);

    @Override
    default public void customize(QuerydslBindings bindings, QUser root) {
        bindings.bind(String.class).first((SingleValueBinding<StringPath, String>) StringExpression::containsIgnoreCase);
    }

    Optional<User> findOneByActivationKey(String activationKey);

    List<User> findAllByActivatedIsFalseAndCreatedDateBefore(Instant dateTime);

    Optional<User> findOneByResetKey(String resetKey);

    Optional<User> findOneByEmail(String email);

    @Query("select user from User user where user.username = :login or user.email = :login or user.phone = :login")
    Optional<User> findUser(@Param("login") String login);

    @Query("select user from User user left join fetch user.roles where user.id = :id")
    Optional<User> findOneWithRolesById(@Param("id") Long id);

    @Query("select user.password from User user where user.id = :id")
    String findPasswordUserId(@Param("id") Long id);

    @EntityGraph(attributePaths = "roles")
    @Query("select user from User user where user.username = :login or user.email = :login")
    Optional<User> findUserWithRoles(@Param("login") String login);

    //@Query("select user from User user where user.uid = :login or user.username = :login or user.email = :login or user.phone = :login")
    @Query("select user from User user where user.username = :login or user.email = :login")
    Optional<User> findOneWithRolesByUsername(@Param("login") String login);

    @Query(value = "select new com.altomni.apn.user.service.dto.user.UserDTO(u, " +
            "ua.creditEffectType, ua.effectCredit, ua.monthlyAmount, ua.bulkCredit, 0, 0, ut.teamId) " +
            "from User u " +
            "left join PermissionUserTeam ut on ut.userId=u.id and ut.isPrimary=true " +
            "left join UserAccount ua on ua.userId=u.id and ua.accountStatus = 0 and ua.expireDate=:expireDate " +
            "where u.tenantId=:tenantId")
    Page<UserDTO> findAllByTenantId(@Param("tenantId") Long tenantId, @Param("expireDate") String expireDate, Pageable pageable);

    default JPAQuery<UserVM> buildUserQueryForSearch(JPAQueryFactory queryFactory, UserSearch userSearch, Long tenantId) {
        QUser qUser = QUser.user;
        QUserRole qUserRole = QUserRole.userRole;
        QPermissionUserTeam qPermissionUserTeam = QPermissionUserTeam.permissionUserTeam;
        QPermissionUserTeam ut = QPermissionUserTeam.permissionUserTeam;
        QUserAccount ua = QUserAccount.userAccount;

        JPAQuery<UserVM> query = queryFactory.select(
                        Projections.constructor(
                                UserVM.class,
                                qUser.id, qUser.firstName, qUser.lastName, qUser.username, qUser.email, qUser.phone, qUser.tenantId,
                                qUser.activated, qUser.note, qUser.createdDate, ua.creditEffectType, ua.effectCredit, ua.monthlyAmount,
                                ua.bulkCredit, ut.teamId, qUser.jobTitle, qUser.cancellationTime, qUser.enumLevelOfExperienceId
                        )
                )
                .from(qUser)
                .leftJoin(ut).on(ut.userId.eq(qUser.id).and(ut.isPrimary.isTrue()))
                .leftJoin(ua).on(ua.userId.eq(qUser.id).and(ua.accountStatus.eq(AccountStatus.AVAILABLE)))
                .where(qUser.tenantId.eq(tenantId))
                .offset(userSearch.getPageable().getOffset())
                .limit(userSearch.getPageable().getPageSize());
        if (StringUtils.hasText(userSearch.getSearchFullName())) {
            String likeParam = "%" + userSearch.getSearchFullName().replaceAll(" ", "") + "%";
            query = query.where(qUser.firstName.likeIgnoreCase(likeParam).or(qUser.lastName.likeIgnoreCase(likeParam))
                    .or(qUser.lastName.concat(qUser.firstName).likeIgnoreCase(likeParam))
                    .or(qUser.firstName.concat(qUser.lastName).likeIgnoreCase(likeParam)));
        }
        if (Objects.nonNull(userSearch.getRoleId())) {
            query = query.innerJoin(qUserRole).on(qUser.id.eq(qUserRole.userId)).where(qUserRole.roleId.eq(userSearch.getRoleId()));
        }
        if (Objects.nonNull(userSearch.getTeamId())) {
            query = query.innerJoin(qPermissionUserTeam).on(qUser.id.eq(qPermissionUserTeam.userId))
                    .where(qPermissionUserTeam.teamId.eq(userSearch.getTeamId()));
        }
        if (Objects.nonNull(userSearch.getActivated())) {
            query = query.where(qUser.activated.eq(userSearch.getActivated()));
        }
        if (StringUtils.hasText(userSearch.getSearchEmail())) {
            String likeParam = "%" + userSearch.getSearchEmail() + "%";
            query = query.where(qUser.email.likeIgnoreCase(likeParam));
        }
        if (Objects.nonNull(userSearch.getId())) {
            query = query.where(qUser.id.eq(userSearch.getId()));
        }
        PathBuilder<User> entityPath = new PathBuilder<>(User.class, "user");
        for (Sort.Order sort : userSearch.getPageable().getSort()) {
            Order order = sort.isAscending() ? Order.ASC : Order.DESC;
            String property = sort.getProperty();
            OrderSpecifier<?> orderSpecifier = switch (property) {
                case "fullName" -> new OrderSpecifier<>(order, qUser.firstName.concat(" ").concat(qUser.lastName));
                case "authorities" -> {
                    QRole qRole = QRole.role;
                    query = query.innerJoin(qUserRole).on(qUser.id.eq(qUserRole.userId))
                            .innerJoin(qRole).on(qUserRole.roleId.eq(qRole.id));
                    yield new OrderSpecifier<>(order, qRole.name);
                }
                case "teams" -> {
                    QTeam qTeam = QTeam.team;
                    query = query.innerJoin(qPermissionUserTeam).on(qUser.id.eq(qPermissionUserTeam.userId))
                            .innerJoin(qTeam).on(qPermissionUserTeam.teamId.eq(qTeam.id));
                    yield new OrderSpecifier<>(order, qTeam.name);
                }
                default -> new OrderSpecifier(order, entityPath.get(sort.getProperty()));
            };
            query.orderBy(orderSpecifier);

        }
        return query;
    }

    @Query(value = "select new com.altomni.apn.user.web.rest.vm.system.OnlineUserVM(u.id, u.username, u.firstName, u.lastName, u.email, t.id, t.name) " +
            "from User u " +
            "left join PermissionUserTeam ut on ut.userId=u.id and ut.isPrimary=true " +
            "left join PermissionTeam t on ut.teamId=t.id " +
            "where u.id in :userIds")
    List<OnlineUserVM> findAllForOnlineUsersByUserIdIn(@Param("userIds") Set<Long> userIds);

    @Query(value = "select u.id as id, u.username as username, u.firstName as firstName, u.lastName as lastName" +
            ", u.email as email, u.tenantId as tenantId, u.createdDate as createdDate" +
            ", r.name as roleName, t.id as teamId, t.name as teamName " +
            "from User u " +
            "left join UserRole ur on ur.userId=u.id " +
            "left join Role r on r.id=ur.roleId " +
            "left join PermissionUserTeam ut on ut.userId=u.id " +
            "left join PermissionTeam t on t.id = ut.teamId " +
            "where u.tenantId=:tenantId")
    List<IUserWithRolesAndTeamsDTO> findAllWithRolesAndTeamsByTenantId(@Param("tenantId") Long tenantId);

    @Query(value = "select user_id as userId, sum(credit) as usedMonthlyCredit from credit_transaction where tenant_id=:tenantId and user_id in :userIds and created_date >=:monthStartDate and created_date <:monthEndDate and credit_type=0 group by user_id", nativeQuery = true)
    List<Object[]> findUsedMonthlyCredit(@Param("tenantId") Long tenantId, @Param("monthStartDate") String monthStartDate, @Param("monthEndDate") String monthEndDate, @Param("userIds") List<Long> userIds);

    @Query(value = "select user_id as userId, sum(credit) as usedBulkCredit from credit_transaction where tenant_id=:tenantId and user_id in :userIds and credit_type=1 group by user_id", nativeQuery = true)
    List<Object[]> findUsedBulkCredit(@Param("tenantId") Long tenantId, @Param("userIds") List<Long> userIds);


    Page<User> findAllByTenantIdAndActivated(Long tenantId, Boolean activated, Pageable pageable);

    Optional<User> findByIdAndActivated(Long id, Boolean activated);

    @EntityGraph(attributePaths = {"roles", "teams"}, type = EntityGraph.EntityGraphType.LOAD)
    List<User> findALLByIdInAndActivated(List<Long> ids, Boolean activated);

    @EntityGraph(attributePaths = {"roles"}, type = EntityGraph.EntityGraphType.LOAD)
    List<User> findAllByIdIn(Collection<Long> ids);

    @Query(value = "SELECT CONCAT(first_name, ' ', last_name) FROM user WHERE id = ?1", nativeQuery = true)
    String findUserFullName(Long id);

    @Query(value = "SELECT u.* FROM user u LEFT JOIN user_authority a ON u.id = a.user_id " +
            " WHERE u.tenant_id = ?1 AND a.authority_name = ?2 LIMIT 1", nativeQuery = true)
    User findFirstUserByTenantIdAndAuthorityName(Long id, String authorityName);

    List<User> findAllByTenantId(Long tenantId);

    @Query("select new com.altomni.apn.common.dto.user.UserBriefDTO(u.id, u.username, u.firstName, u.lastName, u.email, u.phone, u.activated, u.tenantId, u.id, u.customTimezone) " +
            " from User u where u.tenantId=:tenantId")
    List<UserBriefDTO> findAllBriefByTenantId(@Param("tenantId") Long tenantId);

    @Query("select new com.altomni.apn.common.dto.user.UserBriefDTO(u.id, u.username, u.firstName, u.lastName, u.email, u.phone, u.activated, u.tenantId, u.id, u.customTimezone) " +
            " from PermissionUserTeam put LEFT JOIN User u ON put.userId = u.id where put.teamId = :teamId and u.activated = true")
    List<UserBriefDTO> findAllActivatedBriefByTeamId(@Param("teamId") Long teamId);

    @Query("select u.email " +
            " from User u  where u.activated is true")
    List<String> findAllActiveUserEmail();

    @Query(value = "SELECT u.id FROM user u WHERE u.division_id = ?1 AND u.activated = 1" +
            " AND EXISTS (SELECT 1 FROM team t LEFT JOIN team_user tu ON t.id = tu.team_id WHERE t.leader_user_id = ?2 AND tu.user_id = u.id) ", nativeQuery = true)
    Set<Long> findByDivisionId(Long divisionId, Long leaderUserId);

    @Query(value = "Select new com.altomni.apn.common.dto.user.UserBriefDTO(u.id,u.username,u.firstName,u.lastName,u.email,u.tenantId,u.activated) FROM User u WHERE u.id in ?1 and u.activated=true", nativeQuery = false)
    List<UserBriefDTO> findBriefUsers(List<Long> users);

    @Query(value = "Select new com.altomni.apn.common.dto.user.UserBriefDTO(u.id,u.username,u.firstName,u.lastName,u.email,u.tenantId,u.activated, u.lastPushTime) FROM User u WHERE u.id in ?1")
    List<UserBriefDTO> findAllBriefUsers(List<Long> userIds);

    @Query(value = "Select distinct new com.altomni.apn.common.dto.user.UserBriefDTO(u.id,u.username,u.firstName,u.lastName,u.email,u.tenantId,u.activated, u.lastPushTime) FROM User u " +
                   "inner join PermissionUserTeam put ON put.userId = u.id " +
                   "inner join PermissionTeam pt ON pt.id = put.teamId AND pt.teamCategoryId IN (15,20) " +
                   "where u.id in ?1")
    List<UserBriefDTO> findAllBriefUsersWithTeamCategoryFilter(List<Long> userIds);

    @Query(value = "Select new com.altomni.apn.common.dto.user.UserBriefDTO(u.id,u.firstName,u.lastName) FROM User u WHERE u.id in ?1")
    List<UserBriefDTO> findAllBriefUsersByIds(Set<Long> userIds);

    @Query(value = "Select new com.altomni.apn.common.dto.user.UserBriefDTO(u.id,u.username,u.firstName,u.lastName,u.email,u.activated) FROM User u WHERE u.tenantId =:tenantId  and u.activated=true", nativeQuery = false)
    List<UserBriefDTO> findBriefUsersByTenantId(@Param("tenantId") Long tenantId);

    @Query(value = "Select new com.altomni.apn.common.dto.user.UserBriefDTO(u.id,u.username,u.firstName,u.lastName,u.email,u.tenantId,u.activated) FROM User u WHERE u.uid in :uids")
    List<UserBriefDTO> findBriefUsersByUids(@Param("uids") List<String> uids);

    @Query(value = "Select new com.altomni.apn.common.dto.user.UserBriefDTO(u.id,u.username,u.firstName,u.lastName,u.email,u.activated) FROM User u WHERE ((u.firstName like %?1%) or (u.lastName like %?1%)) and u.tenantId = ?2 ", nativeQuery = false)
    List<UserBriefDTO> searchBriefUsers(String name, Long tenantId);

    @Query(value = "SELECT email FROM user WHERE id = ?1", nativeQuery = true)
    String findUserEmailById(Long id);

    List<User> findAllByUsernameContainingAndActivated(String username, Boolean activated);

    @Modifying
    @Query(value = "update user set activated=?1 WHERE tenant_id = ?2", nativeQuery = true)
    void inActiveUserByTenant(Integer active, Long id);

    @Query(value = "select u.* FROM user u" +
            " left join user_role ur on u.id=ur.user_id" +
            " left join role r on r.id=ur.role_id" +
            " WHERE u.tenant_id =?1 AND r.name =?2  ", nativeQuery = true)
    List<User> findUserByAuthority(Long tenantId, String authName);

    @Modifying
    @Query(value = "update user u set u.activated = ?2 where u.id = ?1", nativeQuery = true)
    void updateUserStatus(Long userId, boolean status);

    @Modifying
    @Transactional
    @Query(value = "update user set custom_timezone = ?2 where id = ?1", nativeQuery = true)
    Integer updateTimezoneByUserId(Long userId, String timezone);

    @Modifying
    @Transactional
    @Query(value = "update user set sync_lark = ?2 where id = ?1", nativeQuery = true)
    Integer updateSyncLarkByUserId(Long userId, Integer syncLark);


    @Query(value = "select SUM(ct.credit) from credit_transaction ct  where ct.tenant_id = ?1 and ct.credit_type = ?2  and Year(ct.created_date)=?3 and Month(ct.created_date)=?4", nativeQuery = true)
    Integer findTotalMonthlyUsedCreditByTenant(Long id, int creditType, int year, int month);


    @Query(value = "select SUM(ct.credit) from credit_transaction ct  left join user u on ct.user_id = u.id where u.activated=0 and ct.tenant_id = ?1 and ct.credit_type = ?2  and Year(ct.created_date)=?3 and Month(ct.created_date)=?4", nativeQuery = true)
    Integer findTotalUsedIactiveUserMonthlyCreditByTenant(Long tenant, int creditType, int year, int month);


    @Query(value = "select SUM(ct.credit) from credit_transaction ct  left join user u on ct.user_id = u.id where u.activated=0 and ct.tenant_id = ?1 and ct.credit_type = ?2", nativeQuery = true)
    Integer findTotalUsedIactiveUserBulkCreditByTenant(Long tenant, int creditType);


    @Query(value = "select SUM(ct.credit) from credit_transaction ct  where ct.tenant_id = ?1 and ct.credit_type = ?2", nativeQuery = true)
    Integer findTotalUsedCreditByTenant(Long id, int creditType);


    @Query(value = "select SUM(ct.credit) from credit_transaction ct  where ct.user_id = ?1 and ct.credit_type = ?2", nativeQuery = true)
    Integer findTotalUsedCreditByUserId(Long id, int creditType);

    @Query(value = "select SUM(ct.credit) from credit_transaction ct  where ct.user_id = ?1 and ct.credit_type = ?2 and Year(ct.created_date)=?3 and Month(ct.created_date)=?4 ", nativeQuery = true)
    Integer findTotalMonthlyUsedCreditByUserId(Long id, int creditType, int year, int month);


    @Query(value = "select SUM(ua.amount) from user_account ua left join user u on u.id=ua.user_id where u.tenant_id =?1 and u.activated=1 and ua.expire_date = ?2", nativeQuery = true)
    Integer findTotalActiveUserAssignedMonthlyCreditByTenant(Long id, String expireDate);


    @Query(value = "select SUM(u.bulkCredit) from User u  where u.tenantId = ?1 ", nativeQuery = false)
    Integer findTotalAssignBulkCreditByTenant(Long id);

    @Query(value = "select SUM(ua.effect_credit) from user_account ua left join user u on u.id = ua.user_id where u.tenant_id =?1 and u.activated=1 and ua.effect_credit is not null and ua.expire_date = ?2", nativeQuery = true)
    Integer findTotalAssignNextMonthCreditByTenant(Long id, String expireDate);

    @Query(value = "select distinct country from division where tenant_id = ?1 ", nativeQuery = true)
    Set<String> findUserCountry(Long tenantId);

    @Query(value = "select SUM(ua.amount) from user_account ua left join user u on u.id = ua.user_id where u.tenant_id =?1 and u.activated=1 and ua.effect_credit is null and ua.amount is not null and ua.expire_date = ?2", nativeQuery = true)
    Integer findTotalNextMonthEffectCreditByTenant(Long id, String expireDate);

    @Query("select distinct u from User u " +
            "left join UserRole ur on ur.userId=u.id " +
            "left join Role r on r.id=ur.roleId " +
            "where r.internal=false")
    List<User> findAllByActivatedIsTrueAndNonInternalRole();

    @Query(value = "(SELECT DISTINCT p.name FROM permission_privilege p " +
            " INNER JOIN permission_role_privilege rp ON rp.privilege_id=p.id" +
            " INNER JOIN user_role ur ON ur.role_id= rp.role_id" +
            " WHERE ur.user_id =:userId)", nativeQuery = true)
    List<Object> testHibernateInterceptor(@Param("userId") Long userId);

    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO(u, ud) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId =:pteamId", nativeQuery = false)
    List<PermissionTeamMemberDTO> findUsersByPermissionTeamId(@Param("pteamId") Long pteamId);

    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u, ud) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId in :pteamIds and u.activated = :activated ", nativeQuery = false)
    List<PermissionTeamUserDTO> findActiveUsersByPermissionTeamIdIn(@Param("pteamIds") Set<Long> pteamIds, @Param("activated") Boolean activated);

    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u, ud) FROM User u " +
                   " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
                   " WHERE ud.teamId in :pteamIds ", nativeQuery = false)
    List<PermissionTeamUserDTO> findActiveUsersByPermissionTeamIdIn(@Param("pteamIds") Set<Long> pteamIds);


    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u.id, ud.teamId, u.firstName, u.lastName, u.activated, ud.isPrimary) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId in :pteamIds", nativeQuery = false)
    List<PermissionTeamUserDTO> findUsersWithoutRoleByPermissionTeamIdIn(@Param("pteamIds") Set<Long> pteamIds);

    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u.id, ud.teamId, u.firstName, u.lastName, u.activated, ud.isPrimary) FROM User u " +
                   " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id " +
                   " INNER JOIN PermissionTeam pt ON pt.id = ud.teamId AND pt.teamCategoryId IN (15,20)" +
                   " WHERE ud.teamId in :pteamIds", nativeQuery = false)
    List<PermissionTeamUserDTO> findUsersWithoutRoleByPermissionTeamIdInFilterByTeamCategory(@Param("pteamIds") Set<Long> pteamIds);

    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u.id, ud.teamId, u.firstName, u.lastName, u.activated, ud.isPrimary) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE u.id in :userIds", nativeQuery = false)
    List<PermissionTeamUserDTO> findUsersWithoutRoleByUserIdIn(@Param("userIds") Set<Long> userIds);

    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u.id, ud.teamId, u.firstName, u.lastName, u.activated, ud.isPrimary) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE u.tenantId = :tenantId", nativeQuery = false)
    List<PermissionTeamUserDTO> findUsersWithoutRoleByTenantId(@Param("tenantId") Long tenantId);

    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u, ud) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId in :pteamIds and u.activated = true and ud.isPrimary = true", nativeQuery = false)
    List<PermissionTeamUserDTO> findActiveUsersByPrimaryPermissionTeamIdIn(@Param("pteamIds") Set<Long> pteamIds);


    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO(u, ud) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId in :pteamIds")
    List<PermissionTeamMemberDTO> findUsersByPermissionTeamIdIn(@Param("pteamIds") Set<Long> pteamIds);

    @Query(value = "SELECT u.id FROM User u " +
            " LEFT JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId =:pteamId", nativeQuery = false)
    Set<Long> findUserIdsByPermissionTeamId(@Param("pteamId") Long pteamId);

    @Query(value = "select u.id from user u " +
            " left join user_role ur on ur.user_id=u.id" +
            " where ur.role_id=:roleId", nativeQuery = true)
    List<Long> findUserIdsByRoleId(@Param("roleId") Long roleId);

    @Modifying
    @Query("update User u set u.dataScope=:dataScope where u.id=:userId")
    void updateUserDataScopeById(@Param("dataScope") Integer dataScope, @Param("userId") Long userId);

    @Query("select u.dataScope from User u where u.id=:userId")
    Integer findUserDataScopeById(@Param("userId") Long userId);

    @Query("select u from User u" +
            " left join UserRole ur on ur.userId = u.id" +
            " left join Role r on r.id=ur.roleId" +
            " where r.name in :roleNames")
    List<User> findByRoleNameIn(@Param("roleNames") List<String> roleNames);

    @Query("select u from User u" +
            " left join UserRole ur on ur.userId = u.id" +
            " left join Role r on r.id=ur.roleId" +
            " where r.name in :roleNames and u.tenantId = :tenantId")
    List<User> findByRoleNameInAndTenant(@Param("roleNames") List<String> roleNames, @Param("tenantId") Long tenantId);

    boolean existsByIdAndTenantId(Long userId, Long tenantId);

    @Query(value = "select team_id from permission_user_team ut " +
            " where ut.user_id=:userId and ut.is_primary=1 limit 1", nativeQuery = true)
    Long findPrimaryTeamId(@Param("userId") Long userId);

    @Query(value = "select count(u.user_id) from user_authority u where u.user_id = ?1 and u.authority_name = ?2 ", nativeQuery = true)
    Integer countByUserIdAndRole(Long userId, String role);

    @Query(value = "SELECT new com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO(u.id, u.firstName, u.lastName) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId =:pteamId")
    List<PermissionTeamMemberDTO> findPlainUsersByPermissionTeamId(@Param("pteamId") Long pteamId);

    @Query(value = "SELECT distinct new com.altomni.apn.user.service.dto.permission.PermissionTeamMemberDTO(u.id, u.firstName, u.lastName) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId in :pteamIds AND u.activated = true")
    List<PermissionTeamMemberDTO> findPlainUsersByPermissionTeamIdIn(@Param("pteamIds") Set<Long> pteamIds);

    @Query(value = "SELECT distinct new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u.id, ud.teamId) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId in :pteamIds AND u.activated = true AND ud.isPrimary = true")
    List<PermissionTeamUserDTO> findTeamUsersByPermissionTeamIdIn(@Param("pteamIds") Set<Long> pteamIds);

    @Query(value = "SELECT distinct new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u.id, ud.teamId) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId in :pteamIds AND ud.isPrimary = true")
    List<PermissionTeamUserDTO> findAllStatusTeamUsersByPermissionTeamIdIn(@Param("pteamIds") Set<Long> pteamIds);

    @Query(value = "SELECT distinct new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u.id, ud.teamId) FROM User u " +
                   " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id " +
                   " INNER JOIN PermissionTeam pt ON pt.id = ud.teamId " +
                   " WHERE ud.teamId in :pteamIds AND ud.isPrimary = true AND pt.teamCategoryId IN (15,20)")
    List<PermissionTeamUserDTO> getTeamUserIdsByPermissionTeamIdWithCategoryFilter(@Param("pteamIds") Set<Long> pteamIds);

    @Query(value = "SELECT distinct new com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO(u.id, ud.teamId) FROM User u " +
            " INNER JOIN PermissionUserTeam ud ON ud.userId=u.id" +
            " WHERE ud.teamId in :pteamIds AND ud.isPrimary = true and u.activated IS TRUE ")
    List<PermissionTeamUserDTO> findAllActiveStatusTeamUsersByPermissionTeamIdIn(@Param("pteamIds") Set<Long> pteamIds);

    @Query(value = """
                select distinct c.id
                from company c 
                left join business_flow_administrator csla on csla.company_id = c.id and csla.sales_lead_role in (0,3)
                where csla.user_id = ?1 
            """, nativeQuery = true)
    List<Long> findCompanyIdsByUserIdWithAm(Long userId);

    Integer countByTenantId(Long tenantId);

    @Query(value = "SELECT new com.altomni.apn.user.service.dto.user.UserPageVO(u.id, u.firstName, u.lastName, u.email, ua.monthlyAmount, ua.bulkCredit, u.activated, u.createdDate, u.lastModifiedDate) " +
            "FROM User u LEFT JOIN UserAccount ua ON u.id = ua.userId AND ua.accountStatus = 0 WHERE u.tenantId =:tenantId GROUP BY u.id ")
    Page<UserPageVO> findAllByTenantId(@Param("tenantId") Long tenantId, Pageable pageable);

    @Query(value = "select sum(credit) as usedMonthlyCredit from credit_transaction where user_id = ?1 and created_date >= ?2 and created_date <= ?3 and credit_type = 0", nativeQuery = true)
    Integer findUseMonthlyCreditByUserId(Long userId, String monthStartDate, String monthEndDate);

    @Query(value = "select sum(credit) as usedBulkCredit from credit_transaction where user_id = ?1 and credit_type = 1", nativeQuery = true)
    Integer findUseBulkCreditByUserId(Long userId);

    @Query(value = "Select new com.altomni.apn.common.dto.user.UserUidNameDTO(u.id, u.uid, u.firstName,u.lastName) FROM User u WHERE u.uid in :uids")
    List<UserUidNameDTO> findUidNameUsersByUids(@Param("uids") List<String> uids);

    @Query(value = " select if(sync_lark, 1, 0) from user where id = ?1 ", nativeQuery = true)
    Integer findSyncLarkById(Long userId);

    @Query(value = "Select new com.altomni.apn.common.vo.user.UserTimeZoneVO(u.id, u.customTimezone) FROM User u WHERE u.id in :ids")
    List<UserTimeZoneVO> findUserTimeZoneVOByIds(@Param("ids") List<Long> ids);


    @Query(value = "Select new com.altomni.apn.user.domain.user.UserInfoWithPermission(u.id, u.firstName, u.lastName, u.dataScope, put.isPrimary, put.teamId) FROM User u LEFT JOIN PermissionUserTeam put ON u.id = put.userId WHERE u.activated = true AND u.tenantId = :tenantId AND u.id != :excludeUserId")
    List<UserInfoWithPermission> findUserInfoWithPermissionByTenantId(@Param("tenantId") Long tenantId, @Param("excludeUserId") Long excludeUserId);


    @Query(value = "Select new com.altomni.apn.user.domain.user.UserRoleWithPermission(u.id, r.id, r.dataScope) FROM User u LEFT JOIN UserRole ur ON u.id = ur.userId LEFT JOIN Role r ON ur.roleId = r.id WHERE u.activated = true AND u.tenantId = :tenantId AND r.status = true")
    List<UserRoleWithPermission> findUserRoleWithPermissionByTenantId(@Param("tenantId") Long tenantId);


    @Query(value = "Select new com.altomni.apn.user.domain.user.UserRoleWithPermission(u.id, r.id, r.dataScope) FROM User u LEFT JOIN UserRole ur ON u.id = ur.userId LEFT JOIN Role r ON ur.roleId = r.id WHERE u.activated = true AND u.id = :userId AND r.status = true")
    List<UserRoleWithPermission> findUserRoleWithPermissionByUserId(@Param("userId") Long userId);


    @Query(value = "Select new com.altomni.apn.user.domain.user.UserInfoWithPermission(u.id, u.firstName, u.lastName, u.dataScope, put.isPrimary, put.teamId) FROM User u LEFT JOIN PermissionUserTeam put ON u.id = put.userId WHERE u.activated = :activated AND u.id in :userIds")
    List<UserInfoWithPermission> findUserInfoWithPermissionByUserIdIn(@Param("userIds") List<Long> userIds, @Param("activated") Boolean activated);

    @Query(value = "Select new com.altomni.apn.user.domain.user.UserInfoWithPermission(u.id, u.firstName, u.lastName, u.dataScope, put.isPrimary, put.teamId, u.activated) FROM User u LEFT JOIN PermissionUserTeam put ON u.id = put.userId WHERE u.id in :userIds")
    List<UserInfoWithPermission> findUserInfoWithPermissionByUserIdIn(@Param("userIds") List<Long> userIds);

    @Query(value = "Select new com.altomni.apn.user.domain.user.UserInfoWithPermission(u.id, u.firstName, u.lastName, u.dataScope, put.isPrimary, put.teamId, u.activated) FROM User u " +
                   "INNER JOIN PermissionUserTeam put ON u.id = put.userId " +
                   "INNER JOIN PermissionTeam pt ON pt.id = put.teamId AND pt.teamCategoryId IN (15,20) " +
                   "WHERE u.id in :userIds")
    List<UserInfoWithPermission> findUserInfoWithPermissionByUserIdInFilterByTeamCategory(@Param("userIds") List<Long> userIds);


    @Query(value = "Select new com.altomni.apn.common.dto.user.UserUidNameDTO(u.id, u.uid, u.firstName,u.lastName) FROM User u WHERE u.tenantId = :tenantId")
    List<UserUidNameDTO> findUidNameUserByTenantId(@Param("tenantId") Long tenantId);

    @Query(value = "Select new com.altomni.apn.common.dto.user.UserUidNameDTO(u.id, u.uid, u.firstName,u.lastName) FROM User u WHERE u.id in :ids")
    List<UserUidNameDTO> findUidNameUserByIds(@Param("ids") Set<Long> ids);

    @Modifying
    @Transactional
    @Query(value = "UPDATE user u SET u.last_sync_time=:lastSyncTime WHERE u.id=:userId", nativeQuery = true)
    void updateUserLastSyncTime(@Param("userId") Long userId, @Param("lastSyncTime") Instant lastSyncTime);

    @Query(value = "select min(u.id) from user u " +
            "inner join user_role ur on ur.user_id=u.id " +
            "inner join role r on r.id=ur.role_id " +
            "where u.tenant_id=:tenantId and r.name=:roleName", nativeQuery = true)
    Long findFirstAdminId(@Param("tenantId") Long tenantId, @Param("roleName") String roleName);

    @Query(value = "SELECT u1.id FROM user u1 " +
            " JOIN permission_user_team ud ON ud.user_id=u1.id " +
            " WHERE ud.team_id IN :teamIds " +
            " UNION " +
            " SELECT u2.id FROM User u2 " +
            " JOIN permission_extra_user_team peut ON peut.user_id = u2.id " +
            " WHERE peut.team_id IN :teamIds",  nativeQuery = true)
    Set<Long> findUserIdsByPermissionTeamIdIn(@Param("teamIds") List<Long> teamIds);

    @Query(value = """
            SELECT
	        s.user_id AS userId,
	        t.id AS teamId,
	        t.`name` AS teamName
            FROM
            	(
            SELECT
            	put.team_id,
            	put.user_id
            FROM
            	permission_user_team put
            WHERE
            	put.user_id IN :userIds
            	AND put.is_primary = 1
            	) s
            	LEFT JOIN permission_team t ON t.id = s.team_id
            """,nativeQuery = true)
    List<com.altomni.apn.common.dto.user.TeamInfoProjection> getTeamInfosByUserIds(@Param("userIds") List<Long> userIds);


    @Query(value = """
            SELECT
	        s.user_id AS userId,
	        t.id AS teamId,
	        t.`name` AS teamName
            FROM
            	(
            SELECT
            	put.team_id,
            	put.user_id
            FROM
            	permission_user_team put
            WHERE
            	put.user_id IN :userIds
            	AND put.is_primary = 1
            	) s
            INNER JOIN permission_team t ON t.id = s.team_id AND t.team_category_id IN (15,20)
            """,nativeQuery = true)
    List<com.altomni.apn.common.dto.user.TeamInfoProjection> getTeamInfosByUserIdsWithTeamCategoryFilter(@Param("userIds") List<Long> userIds);

    default List<TeamInfoVO> convertToTeamInfoVOs(List<com.altomni.apn.common.dto.user.TeamInfoProjection> teamInfoProjections) {
        return teamInfoProjections.stream()
                .map(projection -> new TeamInfoVO(projection.getUserId(), projection.getTeamId(), projection.getTeamName()))
                .collect(Collectors.toList());
    }


    default List<PermissionTeamMemberVM> buildCustomUserQueryWithNativeSQL(
            EntityManager entityManager,
            PermissionTeamDeliverySearchVM searchVM) {

        // 1. 构建基础SQL
        String baseSQL = """
            SELECT
                u.id AS id,
                u.first_name AS first_name,
                u.last_name AS last_name,
                u.activated AS activated,
                u.email AS email,
                u.enum_level_of_experience_id AS level_of_Experience,
                u.created_date AS created_date,
                u.job_title AS job_title,
                primary_team.id AS primary_team_id,
                COALESCE(GROUP_CONCAT(DISTINCT no_primary_put.team_id), '') AS no_primary_team_ids,
                primary_team.name AS primary_team_name,
                COALESCE(GROUP_CONCAT(DISTINCT ctl_primary.user_id), '') AS primary_team_lead_ids,
                COALESCE(GROUP_CONCAT(DISTINCT ctl.user_id), '') AS team_lead_ids,
                -- 新增：主团队的上级团队负责人ID
                COALESCE(GROUP_CONCAT(DISTINCT superior_ctl.user_id), '') AS superior_primary_team_leader_ids
            FROM user u
        """;

        // 2. 添加主团队,子团队JOIN（始终需要）
        baseSQL += """
            LEFT JOIN permission_user_team primary_put 
                ON u.id = primary_put.user_id AND primary_put.is_primary = true
            LEFT JOIN permission_team primary_team 
                ON primary_put.team_id = primary_team.id
            LEFT JOIN permission_team_leader ctl_primary 
                ON primary_team.id = ctl_primary.team_id
            -- 为主团队添加上级团队JOIN
            LEFT JOIN permission_team parent_primary_team 
                ON primary_team.parent_id = parent_primary_team.id
            LEFT JOIN permission_team_leader superior_ctl 
                ON parent_primary_team.id = superior_ctl.team_id
            LEFT JOIN permission_user_team no_primary_put 
                ON u.id = no_primary_put.user_id AND no_primary_put.is_primary = false
        """;

        // 3. 根据teamId参数添加当前团队JOIN
        List<Object> params = new ArrayList<>();

        if (searchVM.getTeamId() != null) {
                baseSQL += """
            INNER JOIN permission_user_team put 
                ON u.id = put.user_id
            INNER JOIN permission_team pt 
                ON put.team_id = pt.id
            LEFT JOIN permission_team_leader ctl 
                ON pt.id = ctl.team_id
        """;
        } else {
                baseSQL += """
            LEFT JOIN permission_user_team put ON u.id = put.user_id
            LEFT JOIN permission_team pt ON put.team_id = pt.id
            LEFT JOIN permission_team_leader ctl ON pt.id = ctl.team_id
        """;
        }

        // 4. 动态构建WHERE条件
        List<String> conditions = new ArrayList<>();

        // 动态条件：处理团队ID和showAllMembers
        if (searchVM.getTeamId() != null) {
            if (searchVM.getShowAllMembers()) {
                // 使用递归CTE查询所有子团队
                    String cteClause = """
                WITH RECURSIVE sub_teams AS (
                    SELECT id FROM permission_team WHERE id = ?
                    UNION ALL
                    SELECT pt.id FROM permission_team pt
                    JOIN sub_teams st ON pt.parent_id = st.id
                )
                """;
                baseSQL = cteClause + baseSQL;
                conditions.add("pt.id IN (SELECT id FROM sub_teams)  AND put.is_primary = 1 ");
                params.add(searchVM.getTeamId());
            } else {
                // 仅查询当前团队
                conditions.add("pt.id = ?  AND put.is_primary = 1 ");
                params.add(searchVM.getTeamId());
            }
        }

        // 固定条件：租户ID
        conditions.add("u.tenant_id = ?");
        params.add(SecurityUtils.getTenantId());

        // 动态条件：用户ID
        if (CollUtil.isNotEmpty(searchVM.getUserIds())) {
            String placeholders = buildPlaceholders(searchVM.getUserIds().size());
            conditions.add("u.id IN (" + placeholders + ")");
            params.addAll(searchVM.getUserIds());
        }

        // 动态条件：经验等级
        if (CollUtil.isNotEmpty(searchVM.getLevelOfExperiences())) {
            String placeholders = buildPlaceholders(searchVM.getLevelOfExperiences().size());
            conditions.add("u.enum_level_of_experience_id IN (" + placeholders + ")");
            params.addAll(searchVM.getLevelOfExperiences());
        }

        if (ObjectUtil.isNotNull(searchVM.getActivated())) {
            conditions.add("u.activated = ? ");
            params.add(searchVM.getActivated() ? 1 : 0);
        }

        // 动态条件：语言
        if (CollUtil.isNotEmpty(searchVM.getLanguages())) {
            String subQuery = """
                EXISTS (
                    SELECT 1 FROM user_language_relation ulr
                    WHERE ulr.user_id = u.id
                    AND ulr.enum_language_id IN (%s)
                    GROUP BY ulr.user_id
                    HAVING COUNT(DISTINCT ulr.enum_language_id) = %d
                )
                """.formatted(
                    buildPlaceholders(searchVM.getLanguages().size()),
                    searchVM.getLanguages().size()
            );
            conditions.add(subQuery);
            params.addAll(searchVM.getLanguages());
        }

        // 动态条件：交付地
        if (CollUtil.isNotEmpty(searchVM.getDeliveryLocations())) {
            String subQuery = """
                EXISTS (
                    SELECT 1 FROM user_delivery_country_relation udcr
                    WHERE udcr.user_id = u.id
                    AND udcr.enum_country_id IN (%s)
                    AND (udcr.top = TRUE OR udcr.updated = TRUE)
                )
                """.formatted(buildPlaceholders(searchVM.getDeliveryLocations().size()));
            conditions.add(subQuery);
            params.addAll(searchVM.getDeliveryLocations());
        }

        // 动态条件：流程
        if (CollUtil.isNotEmpty(searchVM.getDeliveryProcessIds())) {
            String subQuery = """
                EXISTS (
                    SELECT 1 FROM user_delivery_process_relation udpr
                    WHERE udpr.user_id = u.id
                    AND udpr.process_id IN (%s)
                    AND (udpr.top = TRUE OR udpr.updated = TRUE)
                )
                """.formatted(buildPlaceholders(searchVM.getDeliveryProcessIds().size()));
            conditions.add(subQuery);
            params.addAll(searchVM.getDeliveryProcessIds());
        }

        // 动态条件：行业
        if (CollUtil.isNotEmpty(searchVM.getDeliveryIndustries())) {
            String subQuery = """
                EXISTS (
                    SELECT 1 FROM user_delivery_industry_relation udir
                    WHERE udir.user_id = u.id
                    AND udir.enum_industry_mapping_id IN (%s)
                    AND (udir.top = TRUE OR udir.updated = TRUE)
                )
                """.formatted(buildPlaceholders(searchVM.getDeliveryIndustries().size()));
            conditions.add(subQuery);
            params.addAll(searchVM.getDeliveryIndustries());
        }

        // 动态条件：职能
        if (CollUtil.isNotEmpty(searchVM.getDeliveryJobFunctions())) {
            String subQuery = """
                EXISTS (
                    SELECT 1 FROM user_delivery_job_function_relation udjfr
                    WHERE udjfr.user_id = u.id
                    AND udjfr.enum_job_function_mapping_id IN (%s)
                    AND (udjfr.top = TRUE OR udjfr.updated = TRUE)
                )
                """.formatted(buildPlaceholders(searchVM.getDeliveryJobFunctions().size()));
            conditions.add(subQuery);
            params.addAll(searchVM.getDeliveryJobFunctions());
        }

        //动态条件：团队类别
        if(CollUtil.isNotEmpty(searchVM.getTeamCategoryIds())) {
            if (CollUtil.isNotEmpty(searchVM.getTeamCategoryIds())) {
                String placeholders = buildPlaceholders(searchVM.getTeamCategoryIds().size());
                conditions.add("primary_team.team_category_id IN (" + placeholders + ")");
                params.addAll(searchVM.getTeamCategoryIds());
            };
        }

        // 5. 拼接完整SQL
        String whereClause = "WHERE " + String.join(" AND ", conditions);
        String fullSQL = baseSQL + whereClause + " GROUP BY u.id";  // 按用户ID分组

        // 6. 创建原生查询
        javax.persistence.Query nativeQuery = entityManager.createNativeQuery(fullSQL, PermissionTeamMemberVM.class);

        // 7. 绑定参数
        for (int i = 0; i < params.size(); i++) {
            nativeQuery.setParameter(i + 1, params.get(i));
        }

        // 8. 执行查询
        return nativeQuery.getResultList();
    }


    // 辅助方法：生成占位符字符串
    private String buildPlaceholders(int count) {
        return Collections.nCopies(count, "?").stream().collect(Collectors.joining(","));
    }

    default JPAQuery<PermissionTeamMemberMemberDetailDTO> buildLabelUserQuery(JPAQueryFactory queryFactory, ReportUserLabelSearchDTO reportUserLabelSearchDTO) {
        // 团队类别ID常量
        Long ENUM_TEAM_CATEGORY_RECRUITING_TEAM_ID = 15L;
        Long ENUM_TEAM_CATEGORY_BD_TEAM_ID = 20L;
        QUser qUser = QUser.user;
        QPermissionUserTeam qPermissionUserTeam = QPermissionUserTeam.permissionUserTeam;
        QUserLanguageRelation qUserLanguageRelation = QUserLanguageRelation.userLanguageRelation;
        QPermissionTeam qPermissionTeam = QPermissionTeam.permissionTeam;

        // 动态构建 WHERE 条件
        BooleanBuilder whereClause = new BooleanBuilder();
        whereClause.and(qUser.tenantId.eq(SecurityUtils.getTenantId()));
        whereClause.and(qPermissionTeam.teamCategoryId.in(ENUM_TEAM_CATEGORY_RECRUITING_TEAM_ID, ENUM_TEAM_CATEGORY_BD_TEAM_ID));
        if (CollUtil.isNotEmpty(reportUserLabelSearchDTO.getPermissionTeamIdList())){
            whereClause.and(qPermissionUserTeam.teamId.in(reportUserLabelSearchDTO.getPermissionTeamIdList()));
        }

        BooleanExpression condition = null;

        if (CollUtil.isNotEmpty(reportUserLabelSearchDTO.getTeamIdList())) {
            condition = qPermissionUserTeam.teamId.in(reportUserLabelSearchDTO.getTeamIdList());
        }

        if (CollUtil.isNotEmpty(reportUserLabelSearchDTO.getUserIdList())) {
            if (condition != null) {
                condition = condition.or(qUser.id.in(reportUserLabelSearchDTO.getUserIdList()));
            } else {
                condition = qUser.id.in(reportUserLabelSearchDTO.getUserIdList());
            }
        }

        if (condition != null) {
            whereClause.and(condition);
        }


        // 添加其他静态和动态条件
        if (CollUtil.isNotEmpty(reportUserLabelSearchDTO.getLevelOfExperiences())){
            whereClause.and(qUser.enumLevelOfExperienceId.in(reportUserLabelSearchDTO.getLevelOfExperiences())); // 经验 ID 条件
        }

        if (CollUtil.isNotEmpty(reportUserLabelSearchDTO.getLanguages())) {
            whereClause.and(
                    queryFactory.select(qUserLanguageRelation.userId)
                            .from(qUserLanguageRelation)
                            .where(
                                    qUserLanguageRelation.userId.eq(qUser.id),
                                    qUserLanguageRelation.enumLanguageId.in(reportUserLabelSearchDTO.getLanguages())
                            )
                            .groupBy(qUserLanguageRelation.userId)
                            .having(qUserLanguageRelation.enumLanguageId.countDistinct().eq((long) reportUserLabelSearchDTO.getLanguages().size()))
                            .exists()
            );
        }

        // 主查询
        JPAQuery<PermissionTeamMemberMemberDetailDTO> query = queryFactory
                .select(
                        Projections.constructor(
                                PermissionTeamMemberMemberDetailDTO.class,
                                qUser.id,
                                qUser.firstName,
                                qUser.lastName,
                                qUser.email,
                                qUser.jobTitle,
                                qUser.enumLevelOfExperienceId,
                                qUser.createdDate,
                                qUser.activated,
                                qPermissionUserTeam
                        )
                )
                .from(qUser)
                .leftJoin(qPermissionUserTeam).on(qUser.id.eq(qPermissionUserTeam.userId).and(qPermissionUserTeam.isPrimary.isTrue()))
                .leftJoin(qPermissionTeam).on(qPermissionUserTeam.teamId.eq(qPermissionTeam.id));
        query.where(whereClause);
        return query;
    }

    @Query(value = "SELECT u.createdDate FROM User u WHERE u.id = ?1")
    Instant findCreatedDateByUserId(Long userId);

    @Query("""
            select distinct u.id from User u
            where u.id in (:ids) and u.activated = false
            """)
    List<Long> findInactiveByidIn(@Param("ids")Set<Long> ids);

    @Query(value = "select new com.altomni.apn.user.service.dto.user.UserTeamDTO(u.email, t.name) " +
            "from User u " +
            "left join PermissionUserTeam ut on ut.userId=u.id and ut.isPrimary=true " +
            "left join PermissionTeam t on t.id=ut.teamId " +
            "where u.email in :emails")
    List<UserTeamDTO> findUserTeamByEmails(@Param("emails") Collection<String> emails);


    @Query("select new com.altomni.apn.common.dto.user.UserBriefDTO(u.id, u.username, u.firstName, u.lastName, u.email, u.phone, u.activated, u.tenantId, u.id, u.customTimezone) " +
           " from User u " +
           " inner join PermissionUserTeam put on u.id = put.userId " +
           " inner join PermissionTeam pt on put.teamId = pt.id and pt.teamCategoryId IN (15, 20)" +
           " where u.tenantId=:tenantId ")
    List<UserBriefDTO> getBriefUsersByTenantIdAndTeamCategoryFilter(@Param("tenantId") Long tenantId);
}
