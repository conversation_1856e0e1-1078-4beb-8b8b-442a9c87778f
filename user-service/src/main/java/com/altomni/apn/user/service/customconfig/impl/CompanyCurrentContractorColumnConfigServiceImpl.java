package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.SystemConfigDefaultRepository;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.customconfig.CompanyCurrentContractorColumnConfigService;
import com.altomni.apn.user.service.dto.customconfig.CompanyCurrentContractorColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.CompanyCurrentContractorColumnConfigVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service("companyCurrentContractorColumnConfigService")
public class CompanyCurrentContractorColumnConfigServiceImpl implements CompanyCurrentContractorColumnConfigService {

    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    private SystemConfigDefaultRepository systemConfigDefaultRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public CompanyCurrentContractorColumnConfigVO searchCompanyCurrentContractorColumnConfigByUserId(Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        //记录不存在 user_preference 表的记录
        boolean notExistsFlag = userPreference == null;
        if (notExistsFlag || StrUtil.isBlank(userPreference.getCompanyCurrentContractorColumnConfig())) {
            Optional<SystemConfigDefault> systemConfigDefaultOptional = Optional.ofNullable(systemConfigDefaultRepository.findByCategoryAndSubcategory(Category.COMPANY_COLUMN, ConfigSubcategory.COMPANY_COLUMN_CURRENT_CONTRACTOR));
            return systemConfigDefaultOptional.map(systemConfigDefault -> {
                String defaultConfig = systemConfigDefault.getDefaultConfig();
                if (notExistsFlag) {
                    //从来没有记录
                    UserPreference addUserPreference = new UserPreference();
                    addUserPreference.setUserId(userId);
                    addUserPreference.setCompanyCurrentContractorColumnConfig(systemConfigDefault.getDefaultConfig());
                    userPreferenceRepository.save(addUserPreference);
                } else {
                    //已经有过其他记录
                    updateCompanyCurrentContractorColumnConfigByUserId(userId, new CompanyCurrentContractorColumnConfigDTO(systemConfigDefault.getDefaultConfig()));
                }
                return new CompanyCurrentContractorColumnConfigVO(defaultConfig);
            }).orElseThrow(() -> {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_SEARCHDASHBOARDCOLUMNCONFIGBYUSERID_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
            });
        } else {
            // 用户偏好设置存在且配置不为空，直接返回
            return new CompanyCurrentContractorColumnConfigVO(userPreference.getCompanyCurrentContractorColumnConfig());
        }
    }

    @Override
    public void updateCompanyCurrentContractorColumnConfigByUserId(Long userId, CompanyCurrentContractorColumnConfigDTO dto) {
        userPreferenceRepository.updateCompanyCurrentContractorColumnConfigByUserId(userId, dto.getCompanyCurrentContractorColumnConfig());
    }

}
