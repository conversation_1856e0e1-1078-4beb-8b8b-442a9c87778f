package com.altomni.apn.user.web.rest.user;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.user.Team;
import com.altomni.apn.user.service.user.TeamService;
import com.altomni.apn.user.service.user.TeamUserService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

@Api(hidden = true, tags = {"Team"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class TeamResource {

    @Resource
    private TeamService teamService;

    @Resource
    private TeamUserService teamUserService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @PostMapping("/teams")
    @NoRepeatSubmit
    public ResponseEntity<Team> createTeam(@Valid @RequestBody Team team) throws URISyntaxException {
        log.info("[APN: Team @{}] REST request to create Team : {}", SecurityUtils.getUserId(), team);
        if (team.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATETEAM_TEAMIDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        team.setTenantId(SecurityUtils.getTenantId());
        Team result = teamService.create(team);
        return ResponseEntity.created(new URI("/api/teams/" + result.getId()))
            .body(result);
    }

    @PutMapping("/teams/{id}")
    @NoRepeatSubmit
    public ResponseEntity<Team> updateTeam(@PathVariable Long id, @Valid @RequestBody Team team) {
        team.setId(id);
        team.setTenantId(SecurityUtils.getTenantId());
        log.info("[APN: Team @{}] REST request to update Team : {}", SecurityUtils.getUserId(), team);
        return ResponseEntity.ok(teamService.update(team));
    }

    @PostMapping("/teams/{teamId}/users/{userId}")
    @NoRepeatSubmit
    public ResponseEntity<Team> addUserToTeam(@PathVariable Long teamId, @PathVariable Long userId) {
        log.info("[APN: Team @{}] REST request to add User {} to Team {}", SecurityUtils.getUserId(), userId, teamId);
        return ResponseEntity.ok(teamUserService.addUser(teamId, userId));
    }

    @PostMapping("/teams/{teamId}/add-users")
    @NoRepeatSubmit
    public ResponseEntity<Team> multiAddUsersToTeam(@PathVariable Long teamId, @RequestBody List<Long> userIds) {
        log.info("[APN: Team @{}] REST request to add Users{} to Team {}", SecurityUtils.getUserId(), userIds, teamId);
        return ResponseEntity.ok(teamUserService.multiAddUsers(teamId, userIds));
    }

    @PostMapping("/teams/{teamId}/replace-users")
    @NoRepeatSubmit
    public ResponseEntity<Team> replaceUsers(@PathVariable Long teamId, @RequestBody List<Long> userIds) {
        log.info("[APN: Team @{}] REST request to add Users{} to Team {}", SecurityUtils.getUserId(), userIds, teamId);
        return ResponseEntity.ok(teamUserService.replaceUsers(teamId, userIds));
    }

    @DeleteMapping("/teams/{teamId}/users/{userId}")
    @NoRepeatSubmit
    public ResponseEntity<Team> removeUserFromTeam(@PathVariable Long teamId, @PathVariable Long userId) {
        log.info("[APN: Team @{}] REST request to remove User {} from Team {}", SecurityUtils.getUserId(), userId, teamId);
        return ResponseEntity.ok(teamUserService.removeUser(teamId, userId));
    }

    @GetMapping("/teams")
    public ResponseEntity<List<Team>> getAllTeamsForUserTenant() {
        log.info("[APN: Team @{}] REST request to get all teams for user's tenant", SecurityUtils.getUserId());
        return ResponseEntity.ok(teamService.getByTenantId(SecurityUtils.getTenantId()));
    }

    @GetMapping("/teams/{id}")
    public ResponseEntity<Team> getTeam(@PathVariable Long id) {
        log.info("[APN: Team @{}] REST request to get team {}", SecurityUtils.getUserId(), id);
        Team team = teamService.get(id);
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(team));
    }

    @DeleteMapping("/teams/{id}")
    @NoRepeatSubmit
    public ResponseEntity<?> deleteTeam(@PathVariable Long id) {
        log.info("[APN: Team @{}] REST request to delete Team : {}", SecurityUtils.getUserId(), id);
        teamService.delete(id);
        return ResponseEntity.ok().build();
    }

}
