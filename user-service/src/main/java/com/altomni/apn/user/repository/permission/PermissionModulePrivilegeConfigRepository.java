package com.altomni.apn.user.repository.permission;

import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.user.domain.permission.PermissionModulePrivilegeConfig;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

public interface PermissionModulePrivilegeConfigRepository extends JpaRepository<PermissionModulePrivilegeConfig, Long> {

    List<PermissionModulePrivilegeConfig> findAllByTenantIdAndType(Long tenantId, TenantUserTypeEnum type);
}
