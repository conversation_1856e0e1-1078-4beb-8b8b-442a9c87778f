package com.altomni.apn.user.service.query;

import com.altomni.apn.common.domain.user.QUser;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.utils.SecurityUtils;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.PathBuilder;
import lombok.Data;
import lombok.NonNull;
import lombok.experimental.Accessors;
import org.springframework.data.domain.Pageable;

@Data
@Accessors(chain = true)
public class UserSearch {

    private String searchFullName;
    private Long roleId;
    private Long teamId;
    private Boolean activated;
    private String searchEmail;
    private Long id;
    @NonNull
    private Pageable pageable;

    public static BooleanExpression getPredicate(String search, String name) {
        QUser qUser = QUser.user;
        PathBuilder<User> entityPath = new PathBuilder<>(User.class, "user");
        MyPredicateBuilder builder = new MyPredicateBuilder().search(search);
        BooleanExpression isTenantId = qUser.tenantId.eq(SecurityUtils.getTenantId());

        String[] splitName = name.split("\\s+");
        BooleanExpression userName = null;
        if (splitName.length == 1) {
            userName = qUser.firstName.contains(splitName[0]).or(qUser.lastName.contains(splitName[0]));
        }
        else if (splitName.length == 2) {
            userName = (qUser.firstName.contains(splitName[0]).or(qUser.lastName.contains(splitName[0])))
                        .and(qUser.firstName.contains(splitName[1]).or(qUser.lastName.contains(splitName[1])));
        }
        return isTenantId.and(userName).and(builder.build(entityPath));
    }
}
