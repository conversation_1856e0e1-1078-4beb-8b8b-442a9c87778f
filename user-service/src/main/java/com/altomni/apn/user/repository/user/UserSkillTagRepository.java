package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.UserSkillTag;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UserSkillTagRepository extends JpaRepository<UserSkillTag, Long> {
    List<UserSkillTag> findAllByUserIdOrderByCreatedDateDescTagAsc(Long userId);
}
