package com.altomni.apn.user.service.permission.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.enumeration.permission.DataScope;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.repository.permission.PermissionRoleRepository;
import com.altomni.apn.user.repository.permission.PermissionUserRoleRepository;
import com.altomni.apn.user.service.dto.permission.PermissionRoleDTO;
import com.altomni.apn.user.service.mapper.permission.RoleMapper;
import com.altomni.apn.user.service.permission.PermissionRolePrivilegeService;
import com.altomni.apn.user.service.permission.PermissionRoleService;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Transactional
public class PermissionRoleServiceImpl implements PermissionRoleService {

    @Resource
    private PermissionRoleRepository permissionRoleRepository;

    @Resource
    private PermissionUserRoleRepository permissionUserRoleRepository;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Resource
    private PermissionRolePrivilegeService permissionRolePrivilegeService;

    @Override
    public PermissionRoleDTO create(PermissionRoleDTO role) {
        if (Objects.nonNull(role.getId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONROLE_CREATE_ROLEIDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        role.setInternal(Boolean.FALSE);
        role.setDataScope(DataScope.PERMISSION_NO.toDbValue());
        role.setStatus(Boolean.TRUE);
        return roleMapper.toDto(permissionRoleRepository.save(roleMapper.toEntity(role).setTenantId(SecurityUtils.getTenantId())));
    }

    @Override
    public PermissionRoleDTO update(PermissionRoleDTO roleDTO) {
        if (Objects.isNull(roleDTO.getId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONROLE_CREATE_ROLEIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        Role role = permissionRoleRepository.findById(roleDTO.getId()).orElseThrow();
        role.setName(roleDTO.getName());
        role.setDescription(roleDTO.getDescription());
        return roleMapper.toDto(permissionRoleRepository.save(role));
    }

    @Override
    public PermissionRoleDTO get(Long id) {
        return roleMapper.toDto(permissionRoleRepository.getById(id));
    }

    @Override
    public void delete(Long id) {
        permissionRoleRepository.deleteById(id);
    }

    @Override
    public List<PermissionRoleDTO> findByTenantId() {
        return permissionRoleRepository.findByTenantIdAndInternalOrderByIdDesc(SecurityUtils.getTenantId(), Boolean.FALSE).stream().map(roleMapper::toDto).collect(Collectors.toList());
    }

    @Override
    public Integer updateActiveStatus(Long roleId, Boolean isActive) {
        permissionRoleRepository.updateStatus(roleId, isActive);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            permissionRolePrivilegeService.dealWithE1E2E5RoleStatusChanged(roleId, isActive);
        });
        return permissionUserRoleRepository.countByRoleId(roleId);
    }

    @Override
    public Integer countActiveUsersByRoleId(Long roleId) {
        return permissionUserRoleRepository.countByRoleId(roleId);
    }

    @Override
    public List<PermissionRoleDTO> findRolesByUserId(Long userId) {
        if (userId < 0){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONROLE_FINDROLESBYUSERID_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(userId),userApiPromptProperties.getUserService()));
        }
        return permissionRoleRepository.findRolesByUserId(userId).stream().map(roleMapper::toDto).collect(Collectors.toList());
    }
}
