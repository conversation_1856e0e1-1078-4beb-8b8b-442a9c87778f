package com.altomni.apn.user.service.dto.customconfig;

import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

@Data
public class JobFormConfigDTO implements BaseConfig, Serializable {
    private static final long serialVersionUID = 1L;
    @JsonIgnore
    private Long id;
    private Long recruitmentProcessId;
    private List<CustomFormField> customConfig;

//    private List<CustomFormField> privateJobCustomConfig;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JobColumnConfigDTO jobColumnConfigDTO = (JobColumnConfigDTO) o;
        if (jobColumnConfigDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), jobColumnConfigDTO.getId());
    }
}
