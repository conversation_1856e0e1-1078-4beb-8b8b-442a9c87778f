package com.altomni.apn.user.service.tenant.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.parser.TenantWatermarkDTO;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.DateUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.enumeration.CreditType;
import com.altomni.apn.user.domain.tenant.TenantWatermarkConfig;
import com.altomni.apn.user.repository.tenant.TenantWatermarkConfigRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.user.TenantAddressVO;
import com.altomni.apn.user.service.dto.user.TenantCreditInfoVO;
import com.altomni.apn.user.service.dto.user.TenantDTO;
import com.altomni.apn.user.service.dto.user.TenantVO;
import com.altomni.apn.user.service.management.ManagementService;
import com.altomni.apn.user.service.tenant.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class TenantServiceImpl implements TenantService {
    @Resource
    private UserRepository userRepository;

    @Resource
    private ManagementService managementService;

    @Resource(name = "commonThreadPool")
    private Executor executor;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Resource
    private TenantWatermarkConfigRepository tenantWatermarkConfigRepository;

//    @Override
//    @GlobalTransactional
//    public TenantDTO create(TenantDTO dto){
//        //create address
//        validateTenantName(dto);
//        Long addressId = null;
//        if(dto.getAddress() != null || dto.getCityId() != null){
//            TenantAddress address  =new TenantAddress();
//            address.setAddress(dto.getAddress());
//            address.setCityId(dto.getCityId());
//            addressId = tenantAddressRepository.save(address).getId();
//        }
//
//        //crate tenant
//        Tenant t = new Tenant();
//        ServiceUtils.myCopyProperties(dto, t);
//        t.setStatus(Constants.ACTIVE);
//        if(addressId != null){
//            t.setAddressId(addressId);
//        }
//        t = tenantRepository.saveAndFlush(t);
//        dto.setId(t.getId());
//        applicationService.initDefaultForGeneralRecruitingProcess(t.getId());
//
//        //send email
//        for(UserBriefDTO user: dto.getAdmin()){
//            //create user
//            user.setTenantId(t.getId());
//            String password = userService.createTenantAdminUser(user);
//            String template = CommonUtils.readFileToString("templates/tenantCreatedTemplate.html"); //TODO
//            Map<String, Object> valueMap = new HashMap<>(16);
//            valueMap.put("userName", user.getFirstName() + " " + user.getLastName());
//            valueMap.put("tenantName", t.getName());
//            valueMap.put("email", user.getEmail());
//            valueMap.put("password", password);
//            valueMap.put("url", applicationProperties.getBaseUrl());
//            template = Html2ImageUtils.convertHtmlTemplate(template,valueMap);
//
//            List<String> to = new ArrayList<>();
//            to.add(user.getEmail());
//            MailVM mailvm = new MailVM(applicationProperties.getSupportSender(), to,null,null,"Tenant create notice", template,null);
//            try {
//                mailService.sendHtmlMail(mailvm);
//                log.info("createtenantSendEmail Success");
//            } catch (Exception e) {
//                log.error("error", e);
//                throw new CustomParameterizedException("send tenant create notice email failed");
//            }
//        }
//        return dto;
//    }

    @Override
    public TenantDTO update(TenantDTO dto) {
        return managementService.updateTenant(dto.getId(), dto).getBody();
    }

//    @Override
//    public void delete(Long id)
//    {
//
//    }

//    @Override
//    public List<TenantVO> findAll() {
//        List<Tenant> ts = tenantRepository.findAll();
//        List<TenantVO> result = new LinkedList<>();
//        if(CollectionUtils.isEmpty(ts)){ return new ArrayList<>();}
//        for(Tenant t:ts){
//            TenantVO vo = new TenantVO();
//            collectData(t, vo);
//            result.add(vo);
//        }
//        return result;
//    }

    @Override
    public List<Tenant> findActiveTenant() {
        return managementService.getAllTenant(Constants.ACTIVE).getBody();
    }

    private void collectData(Tenant t, TenantVO vo) {
        ServiceUtils.myCopyProperties(t, vo);
        Calendar calendar = Calendar.getInstance();
        List<User> us = userRepository.findUserByAuthority(t.getId(), AuthoritiesConstants.TENANT_ADMIN);
        Integer bulkUsedCredit = userRepository.findTotalUsedCreditByTenant(t.getId(), CreditType.BULK.toDbValue());
        Integer monthlyUserCredit = userRepository.findTotalMonthlyUsedCreditByTenant(t.getId(), CreditType.MONTHLY.toDbValue(), calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1);
        vo.setBulkUsedCredit(bulkUsedCredit);
        vo.setMonthlyUsedCredit(monthlyUserCredit);
        List<UserBriefDTO> userBriefDTOList = new LinkedList<>();
        vo.setAdmin(userBriefDTOList);
        if (CollectionUtils.isNotEmpty(us)) {
            us.stream().forEach(item -> {
                UserBriefDTO u = new UserBriefDTO();
                u.setFirstName(item.getFirstName());
                u.setLastName(item.getLastName());
                u.setEmail(item.getEmail());
                userBriefDTOList.add(u);
            });

        }

        if (!Objects.isNull(t.getAddressId())) {
            TenantAddressVO address = managementService.queryTenantAddress(t.getAddressId()).getBody();
            if (address != null) {
                vo.setAddress(address);
            }
        }
    }


    @Override
    public TenantVO findOne(Long id) {
        Tenant t = managementService.queryTenant(id).getBody();
        if (t == null || t.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSION_FINDDATAPERMISSIONBYUSERID_TENANTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        TenantVO vo = new TenantVO();
        ServiceUtils.myCopyProperties(t, vo);
        collectData(t, vo);
        if (t.getCreatedBy().contains(",")) {
            userRepository.findById(Long.parseLong(t.getCreatedBy().split(",")[0])).ifPresent(user -> vo.setCreatedBy(user.getFirstName() + " " + user.getLastName()));
        }
        return vo;
    }

    @Override
    public TenantCreditInfoVO findAvailableCredit(Long id) {
        TenantCreditInfoVO vo = new TenantCreditInfoVO();
        Tenant tenant = managementService.queryTenant(id).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSION_FINDDATAPERMISSIONBYUSERID_TENANTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        Calendar calendar = Calendar.getInstance();
        CompletableFuture<Integer> totalBulkFuture = CompletableFuture.supplyAsync(() -> userRepository.findTotalAssignBulkCreditByTenant(id), executor);
        CompletableFuture<Integer> usedBulkFuture = CompletableFuture.supplyAsync(() -> userRepository.findTotalUsedIactiveUserBulkCreditByTenant(id, CreditType.BULK.toDbValue()), executor);
        CompletableFuture<Integer> totalActiveUserMonthlyAssignedFuture = CompletableFuture.supplyAsync(() ->
            userRepository.findTotalActiveUserAssignedMonthlyCreditByTenant(id, DateUtil.lastDayOfCurrentMonth()), executor);
        CompletableFuture<Integer> totalInactiveUserMonthlyUsedFuture = CompletableFuture.supplyAsync(() ->
            userRepository.findTotalUsedIactiveUserMonthlyCreditByTenant(id, CreditType.MONTHLY.toDbValue(), calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH)), executor);
        CompletableFuture<Integer> totalNextMonthAssignedFuture = CompletableFuture.supplyAsync(() ->
            userRepository.findTotalAssignNextMonthCreditByTenant(id, DateUtil.lastDayOfCurrentMonth()), executor);
        CompletableFuture<Integer> effectMonthTotalFuture = CompletableFuture.supplyAsync(() ->
            userRepository.findTotalNextMonthEffectCreditByTenant(id, DateUtil.lastDayOfCurrentMonth()), executor);
        CompletableFuture.allOf(totalBulkFuture, usedBulkFuture, totalActiveUserMonthlyAssignedFuture, totalInactiveUserMonthlyUsedFuture, totalNextMonthAssignedFuture, effectMonthTotalFuture)
            .exceptionally(t -> {
                log.error("Error occurred when fetching tenant credit data: ", t);
                throw new RuntimeException("Error occurred when fetching tenant credit  data ");
            }).join();
        Integer totalBulk = totalBulkFuture.join();
        Integer usedBulk = usedBulkFuture.join();
        Integer totalActiveUserMonthlyAssigned = totalActiveUserMonthlyAssignedFuture.join();
        Integer totalInactiveUserMonthlyUsed = totalInactiveUserMonthlyUsedFuture.join();
        Integer totalNextMonthAssigned = totalNextMonthAssignedFuture.join();
        Integer effectMonthTotal = effectMonthTotalFuture.join();

        if (Objects.isNull(totalBulk)) {
            totalBulk = 0;
        }
        if (Objects.isNull(usedBulk)) {
            usedBulk = 0;
        }
        if (Objects.isNull(totalActiveUserMonthlyAssigned)) {
            totalActiveUserMonthlyAssigned = 0;
        }
        if (Objects.isNull(totalInactiveUserMonthlyUsed)) {
            totalInactiveUserMonthlyUsed = 0;
        }
        if (Objects.isNull(totalNextMonthAssigned)) {
            totalNextMonthAssigned = 0;
        }
        if (Objects.isNull(effectMonthTotal)) {
            effectMonthTotal = 0;
        }

        Integer tenantBulkCredit = Objects.isNull(tenant.getBulkCredit()) ? 0 : tenant.getBulkCredit();
        Integer tenantMonthlyCredit = Objects.isNull(tenant.getMonthlyCredit()) ? 0 : tenant.getMonthlyCredit();
        vo.setTenantId(id);
        vo.setAvailableBulkCredit(tenantBulkCredit - totalBulk - usedBulk);
        vo.setTotalBulkCredit(tenant.getBulkCredit());
        log.info("tenantMonthlyCredit=" + tenantMonthlyCredit);
        log.info("totalActiveUserMonthlyAssigned=" + totalActiveUserMonthlyAssigned);
        log.info("totalInactiveUserMonthlyUsed=" + totalInactiveUserMonthlyUsed);
        vo.setAvailableMonthlyCredit(tenantMonthlyCredit - totalActiveUserMonthlyAssigned - totalInactiveUserMonthlyUsed);
        vo.setTotalMonthlyCredit(tenant.getMonthlyCredit());

        Integer updateMonthlyCredit = Objects.isNull(tenant.getUpdateMonthlyCredit()) ? tenant.getMonthlyCredit() : tenant.getUpdateMonthlyCredit();
        log.info("updateMonthlyCredit=" + updateMonthlyCredit);
        log.info("totalNextMonthAssigned=" + totalNextMonthAssigned);
        log.info("effectMonthTotal=" + effectMonthTotal);
        vo.setNextMonthAvailableCredit(updateMonthlyCredit - (totalNextMonthAssigned + effectMonthTotal));
        return vo;
    }

    @Override
    public TenantWatermarkDTO getTenantWatermarkConfig(Long tenantId) {
        String tenantName = tenantWatermarkConfigRepository.findTenantName(tenantId);
        TenantWatermarkDTO dto = new TenantWatermarkDTO();
        dto.setTenantId(tenantId);
        dto.setTenantName(tenantName);
        TenantWatermarkConfig config = tenantWatermarkConfigRepository.findTenantWatermarkConfigByTenantIdIsAndActiveIsTrue(tenantId);
        if(config != null) {
            dto.setObjKey(config.getObjKey());
            dto.setActive(true);
        }

        return dto;
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateTenantStatusToActive(Long id, TenantUpdateStatusDTO dto) {
//        Tenant existTenant = tenantRepository.findById(id).orElseThrow(() -> new NotFoundException("This tenant does not exist"));
//
//        existTenant.setStatus(1);
//        //recover user status
//        existTenant.setExpireDate(DateUtil.toInstantAtEndOfDay(dto.getExpireDate()));
//        List<TenantAdminStatus> tas = tasRepository.findByTenantId(existTenant.getId());
//        Map<Long, TenantAdminStatus> tasMap = tas.stream().collect(Collectors.toMap(TenantAdminStatus::getUserId, TenantAdminStatus -> TenantAdminStatus));
//        List<User> userList = userRepository.findAllByTenantId(existTenant.getId());
//        userList.forEach(user -> {
//            if (tasMap.containsKey(user.getId())) {
//                user.setActivated(tasMap.get(user.getId()).isActivated());
//            }
//        });
//        userRepository.saveAll(userList);
//        tenantRepository.save(existTenant);
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateTenantStatusToInActive(Long id) {
//        Tenant existTenant = tenantRepository.findById(id).orElseThrow(() -> new NotFoundException("This tenant does not exist"));
//
//        existTenant.setStatus(0);
//        //save user current status
//        List<User> userBriefs = userRepository.findAllByTenantId(existTenant.getId());
//        List<TenantAdminStatus> tss = new LinkedList<>();
//        userBriefs.forEach(item -> {
//            TenantAdminStatus ts = new TenantAdminStatus(item.getTenantId(), item.getId(),item.isActivated());
//            tss.add(ts);
//        });
//
//        tasRepository.deleteAllByTenantId(existTenant.getId());
//        tasRepository.saveAll(tss);
//        userRepository.inActiveUserByTenant(0, existTenant.getId());
//        tenantRepository.save(existTenant);
//    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void updateTenantAndActiveUserCredit(String expireDate) {
//        accountRepository.updateActiveUserMonthlyCredit(expireDate);
//        //update all tenant credit
//        tenantRepository.updateTenantMonthlyCredit();
//    }

//    private void validateTenantName(TenantDTO tenantDTO){
//        Tenant t = tenantRepository.findByName(tenantDTO.getName());
//        //create
//        if(tenantDTO.getId() == null){
//            if(t != null){
//                throw new CustomParameterizedException("tenant name already exist ");
//            }
//        }else { //update
//            if(t != null && t.getId().longValue() != tenantDTO.getId().longValue()){
//                throw new CustomParameterizedException("tenant name already exist ");
//            }
//        }
//    }

//
//    //@PostConstruct
//    public void syncCreditFromSyaParam(){
//        List<Tenant> ts = tenantRepository.findAll();
//        for(Tenant t :ts)
//        {
//            if(t.getClass() == null)
//            {
//                ApnParam p= paramRepository.findByParamKeyAndTenantIdAndStatus(Constants.USER_CREDIT_LIMIT,t.getId(), Status.Available);
//                if(p == null){ continue;}
//                String value = p.getParamValue();
//                if(value != null)
//                {
//                      t.setBulkCredit(Integer.parseInt(value));
//                      tenantRepository.save(t);
//                }
//            }
//        }
//
//    }


}
