package com.altomni.apn.user.web.rest.vm.system;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class OnlineUserVM {

    private Long id;

    private String username;

    private String firstName;

    private String lastName;

    private String email;

    private JSONObject primaryTeam;

    private List<JSONObject> secondaryTeams;

    private JSONArray roles;

    private String ip;

    private String timezone;

    private String latestActiveTime;

    public OnlineUserVM(Long id, String username, String firstName, String lastName, String email, Long primaryTeamId, String primaryTeamName) {
        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        if (Objects.nonNull(primaryTeamId)){
            this.primaryTeam = new JSONObject().fluentPut("id", primaryTeamId.toString()).fluentPut("name", primaryTeamName);
        }
    }
}
