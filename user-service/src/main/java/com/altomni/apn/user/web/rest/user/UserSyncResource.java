package com.altomni.apn.user.web.rest.user;

import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.vo.canal.MqMessageCountVM;
import com.altomni.apn.common.vo.canal.SyncToMqVM;
import com.altomni.apn.user.service.user.UserSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class UserSyncResource {

    @Resource
    private UserSyncService userSyncService;

    @PostMapping("/canal/check-user-mq-message-count")
    public ResponseEntity<MqMessageCountVM> checkTalentMqMessageCount() {
        log.debug("[Canal] checkUserMqMessageCount!");
        return ResponseEntity.ok(userSyncService.checkUserMqMessageCount());
    }

    @PostMapping("/canal/sync-users-to-mq")
    public ResponseEntity<Void> syncUsersToMQ(@RequestBody SyncToMqVM syncToMqVM) {
        log.info("[EsFillerUserService: syncUserToMQ @{}] syncToMqVM: {}", SecurityUtils.getUserId(), syncToMqVM);
        userSyncService.syncUsersToMQ(syncToMqVM.getIds(), syncToMqVM.getPriority());
        return ResponseEntity.ok().build();
    }

}
