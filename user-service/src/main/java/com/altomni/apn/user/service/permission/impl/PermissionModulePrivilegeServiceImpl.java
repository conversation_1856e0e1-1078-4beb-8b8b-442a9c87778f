package com.altomni.apn.user.service.permission.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.repository.permission.PermissionModulePrivilegeConfigRepository;
import com.altomni.apn.common.repository.permission.PermissionTenantPrivilegeRepository;
import com.altomni.apn.user.domain.permission.PermissionModulePrivilege;
import com.altomni.apn.user.domain.permission.PermissionModulePrivilegeConfig;
import com.altomni.apn.user.domain.permission.PermissionTenantModule;
import com.altomni.apn.common.domain.permission.PermissionTenantPrivilege;
import com.altomni.apn.user.repository.permission.*;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.altomni.apn.user.service.management.ManagementService;
import com.altomni.apn.common.domain.permission.PermissionModulePrivilegePageVM;
import com.altomni.apn.user.service.dto.permission.PermissionModulePrivilegeDTO;
import com.altomni.apn.user.service.permission.PermissionModulePrivilegeService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTenantInvolvedDataPermissionModuleVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PermissionModulePrivilegeServiceImpl implements PermissionModulePrivilegeService {

    @Resource
    private PermissionModulePrivilegeRepository permissionModulePrivilegeRepository;

    @Resource
    private PermissionTenantModuleRepository permissionTenantModuleRepository;

    @Resource
    private PermissionTenantPrivilegeRepository permissionTenantPrivilegeRepository;

    @Resource
    private PlatformTransactionManager platformTransactionManager;

    @Resource
    private TransactionDefinition transactionDefinition;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

    @Resource
    private ManagementService managementService;

    @Resource
    private ApplicationIPGProperties applicationIPGProperties;

    @Resource
    private PermissionModulePrivilegeConfigRepository permissionModulePrivilegeConfigRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    private final Long UNIVERSAL_TENANT_ID = -4L;

    @Override
    public List<PermissionModulePrivilegeDTO> searchTenantModuleLis(Long tenantId) {

        Tenant tenant = managementService.queryTenant(tenantId).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSION_FINDDATAPERMISSIONBYUSERID_TENANTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        List<PermissionModulePrivilege> permissionModulePrivilegeList = permissionModulePrivilegeRepository.findAll();
        Map<Long, Long> permissionModulePrivilegeMap = permissionModulePrivilegeList.stream().filter(o -> ObjectUtil.isNotEmpty(o.getPrivilegeId())).collect(Collectors.toMap(PermissionModulePrivilege::getPrivilegeId, PermissionModulePrivilege::getId));

        List<PermissionModulePrivilegePageVM> privilegePageVMList = permissionModulePrivilegeRepository.searchTenantModuleList(tenant.getId(), tenant.getUserType());
        if (CollUtil.isEmpty(privilegePageVMList)) {
            privilegePageVMList = permissionModulePrivilegeRepository.searchTenantModuleList(UNIVERSAL_TENANT_ID, tenant.getUserType());
        }
        List<PermissionTenantInvolvedDataPermissionModuleVM> permissionModuleList = permissionTenantModuleRepository.findInvolvedModulesByTenantId(tenant.getId());
        Map<Long, String> permissionModuleMap = permissionModuleList.stream().collect(Collectors.toMap(PermissionTenantInvolvedDataPermissionModuleVM::getId, PermissionTenantInvolvedDataPermissionModuleVM::getName));

        List<PermissionTenantPrivilege> permissionTenantPrivilegeList = permissionTenantPrivilegeRepository.findAllByTenantIdAndIsShow(tenant.getId(), Boolean.TRUE);
        Map<Long, PermissionTenantPrivilege> permissionTenantPrivilegeMap = permissionTenantPrivilegeList.stream().collect(Collectors.toMap(PermissionTenantPrivilege::getPrivilegeId, PermissionTenantPrivilege -> PermissionTenantPrivilege));

        List<PermissionModulePrivilegeDTO> result = new ArrayList<>();
        privilegePageVMList.forEach(o -> {
            PermissionModulePrivilegeDTO permissionModulePrivilegeDTO = PermissionModulePrivilegeDTO.fromPermissionModulePrivilegePageVM(o);
            if (permissionModulePrivilegeDTO.getIsShowPermissionButton().equals(Boolean.TRUE) && permissionModuleMap.containsKey(o.getModuleId())) {
                permissionModulePrivilegeDTO.setIsInvolveDataPermission(Boolean.TRUE);
            }
            permissionModulePrivilegeDTO.setParentId(permissionModulePrivilegeMap.getOrDefault(permissionModulePrivilegeDTO.getParentId(), null));
            permissionModulePrivilegeDTO.setIsShow(permissionTenantPrivilegeMap.containsKey(o.getPrivilegeId()) ? Boolean.TRUE : Boolean.FALSE);
            result.add(permissionModulePrivilegeDTO);
        });

        return result;
    }

    @Override
    public void updateTenantModuleList(Long tenantId, List<PermissionModulePrivilegeDTO> tenantModuleDTOList) {
        Tenant tenant = managementService.queryTenant(tenantId).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSION_FINDDATAPERMISSIONBYUSERID_TENANTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        validateTenantPrivilege(tenant.getId(), tenantModuleDTOList);

        List<PermissionModulePrivilegeConfig> permissionModulePrivilegeConfigList = permissionModulePrivilegeConfigRepository.findAllByTenantIdAndType(tenantId, tenant.getUserType());
        if (CollUtil.isEmpty(permissionModulePrivilegeConfigList)) {
            permissionModulePrivilegeConfigList = permissionModulePrivilegeConfigRepository.findAllByTenantIdAndType(UNIVERSAL_TENANT_ID, tenant.getUserType());
        }
        Map<Long, PermissionModulePrivilegeConfig> permissionModulePrivilegeConfigMap = permissionModulePrivilegeConfigList.stream().collect(Collectors.toMap(PermissionModulePrivilegeConfig::getPermissionModulePrivilegeId, o -> o));

        List<PermissionModulePrivilege> permissionModulePrivilegeList = permissionModulePrivilegeRepository.findAllById(tenantModuleDTOList.stream().map(PermissionModulePrivilegeDTO::getId).collect(Collectors.toList()));
        List<PermissionModulePrivilegePageVM> privilegeList = permissionModulePrivilegeList.stream().filter(o -> permissionModulePrivilegeConfigMap.containsKey(o.getId()) && Boolean.TRUE.equals(permissionModulePrivilegeConfigMap.get(o.getId()).getDisable())).map(o -> new PermissionModulePrivilegePageVM(o.getId(), o.getModuleId(), o.getPrivilegeId())).collect(Collectors.toList());
        List<PermissionModulePrivilegePageVM> moduleList = permissionModulePrivilegeList.stream().filter(o -> permissionModulePrivilegeConfigMap.containsKey(o.getId()) && Boolean.TRUE.equals(permissionModulePrivilegeConfigMap.get(o.getId()).getIsShowPermissionButton())).map(o -> new PermissionModulePrivilegePageVM(o.getId(), o.getModuleId(), o.getPrivilegeId())).collect(Collectors.toList());
        Map<Long, Long> privilegeMap = permissionModulePrivilegeList.stream().collect(Collectors.toMap(PermissionModulePrivilege::getPrivilegeId, PermissionModulePrivilege::getId));
        Map<Long, Long> moduleMap = permissionModulePrivilegeList.stream().filter(o -> o.getModuleId() != null).collect(Collectors.toMap(PermissionModulePrivilege::getModuleId, PermissionModulePrivilege::getId));

        List<PermissionTenantPrivilege> existPermissionTenantPrivilegeList = permissionTenantPrivilegeRepository.findAllByTenantIdAndPrivilegeIdIn(tenant.getId(), privilegeList.stream().map(PermissionModulePrivilegePageVM::getPrivilegeId).collect(Collectors.toList()));
        Map<Long, Long> existPermissionTenantPrivilegeIdMap = existPermissionTenantPrivilegeList.stream().collect(Collectors.toMap(PermissionTenantPrivilege::getPrivilegeId, PermissionTenantPrivilege::getId));
        List<PermissionTenantModule> existPermissionTenantModuleList = permissionTenantModuleRepository.findAllByTenantIdAndModuleIdIn(tenant.getId(), moduleList.stream().map(PermissionModulePrivilegePageVM::getModuleId).collect(Collectors.toList()));
        Map<Long, Long> existPermissionTenantModuleIdMap = existPermissionTenantModuleList.stream().collect(Collectors.toMap(PermissionTenantModule::getModuleId, PermissionTenantModule::getId));

        Map<Long, PermissionModulePrivilegeDTO> tenantPrivilegeDTOMap = tenantModuleDTOList.stream().
                filter(o -> permissionModulePrivilegeList.stream().map(PermissionModulePrivilege::getId).collect(Collectors.toSet()).contains(o.getId())).
                collect(Collectors.toMap(PermissionModulePrivilegeDTO::getId, PermissionModulePrivilegeDTO -> PermissionModulePrivilegeDTO));

        existPermissionTenantPrivilegeList.forEach(o -> {
            if (tenantPrivilegeDTOMap.containsKey(privilegeMap.get(o.getPrivilegeId()))) {
                o.setIsShow(tenantPrivilegeDTOMap.get(privilegeMap.get(o.getPrivilegeId())).getIsShow());
            }
        });

        existPermissionTenantModuleList.forEach(o -> {
            if (tenantPrivilegeDTOMap.containsKey(moduleMap.get(o.getModuleId()))) {
                o.setInvolveDataPermission(tenantPrivilegeDTOMap.get(moduleMap.get(o.getModuleId())).getIsInvolveDataPermission());
            }
        });

        permissionModulePrivilegeList.stream().filter(o -> permissionModulePrivilegeConfigMap.containsKey(o.getId()) && Boolean.TRUE.equals(permissionModulePrivilegeConfigMap.get(o.getId()).getDisable()) && !existPermissionTenantPrivilegeIdMap.containsKey(o.getPrivilegeId())).forEach(o -> {
            PermissionModulePrivilegeDTO permissionModulePrivilegeDTO = tenantPrivilegeDTOMap.get(o.getId());
            if (permissionModulePrivilegeDTO.getIsShow() != null) {
                existPermissionTenantPrivilegeList.add(new PermissionTenantPrivilege(tenant.getId(), o.getPrivilegeId(), permissionModulePrivilegeDTO.getIsShow()));
            }
        });

        permissionModulePrivilegeList.stream().filter(o -> permissionModulePrivilegeConfigMap.containsKey(o.getId()) && Boolean.TRUE.equals(permissionModulePrivilegeConfigMap.get(o.getId()).getIsShowPermissionButton()) && !existPermissionTenantModuleIdMap.containsKey(o.getModuleId())).forEach(o -> {
            PermissionModulePrivilegeDTO permissionModulePrivilegeDTO = tenantPrivilegeDTOMap.get(o.getId());
            if (permissionModulePrivilegeDTO.getIsInvolveDataPermission() != null) {
                existPermissionTenantModuleList.add(new PermissionTenantModule(o.getModuleId(), tenant.getId(), permissionModulePrivilegeDTO.getIsInvolveDataPermission()));
            }
        });

        cachePermissionWriteOnly.deleteAllPrivilegePermissionSet();

        TransactionStatus transactionStatus = null;
        try {
            transactionStatus = platformTransactionManager.getTransaction(transactionDefinition);

            permissionTenantPrivilegeRepository.saveAll(existPermissionTenantPrivilegeList);
            permissionTenantModuleRepository.saveAll(existPermissionTenantModuleList);

            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            if (transactionStatus != null) {
                platformTransactionManager.rollback(transactionStatus);
            }
            log.error("[APN: User permission] tenant permission update failed, {}", e.getMessage());
        } finally {
            if (transactionStatus != null) {
                if (transactionStatus.isRollbackOnly()) {
                    platformTransactionManager.rollback(transactionStatus);
                } else if (!transactionStatus.isCompleted()) {
                    platformTransactionManager.commit(transactionStatus);
                }
            }
        }

    }

    @Override
    public List<PermissionModulePrivilegePageVM> findTenantDefaultModuleList(Long tenantId, TenantUserTypeEnum type) {
        return permissionModulePrivilegeRepository.searchEnableTenantModuleList(tenantId, type);
    }

    private void validateTenantPrivilege(Long tenantId, List<PermissionModulePrivilegeDTO> tenantModuleDTOList) {
        List<PermissionModulePrivilegeDTO> existedPermissionModulePrivilegeList = searchTenantModuleLis(tenantId);
        Set<Long> existedActiveIds = existedPermissionModulePrivilegeList.stream().filter(o -> Boolean.TRUE.equals(o.getIsShow())).map(PermissionModulePrivilegeDTO :: getId).collect(Collectors.toSet());
        Set<Long> usedIdList = permissionModulePrivilegeRepository.findAllIdForPrivilegeByTenantId(tenantId);
        tenantModuleDTOList.stream().filter(o -> existedActiveIds.contains(o.getId()) && usedIdList.contains(o.getId())).forEach(o -> {
            if (o.getIsShow().equals(Boolean.FALSE)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSION_VALIDATETENANTPRIVILEGE_INVALID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
            }
        });
    }

}
