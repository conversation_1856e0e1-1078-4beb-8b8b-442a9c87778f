package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;

import java.util.List;


public interface SystemConfigDefaultService {
    List<SystemConfigDefault> getSystemConfigByCategory(Category category);

    SystemConfigDefault getSystemConfigByCategoryAndSubCategory(Category category, ConfigSubcategory subCategory);
}
