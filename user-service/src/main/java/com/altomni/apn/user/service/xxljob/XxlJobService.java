package com.altomni.apn.user.service.xxljob;

import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTenantMessageConfigDTO;
import com.altomni.apn.common.dto.xxljob.XxlJobUpdateByTimezoneDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Component
@FeignClient(value = "common-service")
public interface XxlJobService {

    @PutMapping("/common/api/v3/xxl-job/update-xxl-job-by-timezone")
    ResponseEntity<Void> updateXxlJobByTimezone(@RequestBody XxlJobUpdateByTimezoneDTO xxlJobUpdateByTimezoneDTO);

    @PutMapping("/common/api/v3/xxl-job/update-xxl-job-by-tenant-message-config")
    ResponseEntity<Void> updateXxlJobByTenantMessageConfig(@RequestBody List<XxlJobUpdateByTenantMessageConfigDTO> dtoList);

    @PostMapping("/common/api/v3/xxl-job/message/create-or-update-un-submit-talent-for-team")
    ResponseEntity<Void> createOrUpdateUnSubmittedCandidatesForTeam(@RequestParam("tenantId") Long tenantId);

}
