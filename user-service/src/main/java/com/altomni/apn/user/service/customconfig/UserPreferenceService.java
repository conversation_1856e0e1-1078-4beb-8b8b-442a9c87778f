package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.config.UserPersonalizationConfig;

public interface UserPreferenceService {
    void updateUserRecentlyUsedRecruitmentProcess(Long recruitmentProcessId, Long userId);

    void updateUserRecentlyUsedRecruitmentProcessForPrivateJob(Long recruitmentProcessId, Long userId);

    Long getUserRecentlyUsedRecruitmentProcessByUserId(Long userId);

    Long getUserRecentlyUsedRecruitmentProcessByUserIdForPrivateJob(Long userId);

    void removeRecentUsedRecruitmentProcess(Long recruitmentProcessId);

    UserPersonalizationConfig getPersonalizationConfig();

    void updatePersonalizationConfig(UserPersonalizationConfig config);
}
