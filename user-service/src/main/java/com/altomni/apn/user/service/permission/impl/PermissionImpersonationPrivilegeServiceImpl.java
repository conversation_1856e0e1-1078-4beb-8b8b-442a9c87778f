package com.altomni.apn.user.service.permission.impl;

import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.user.domain.permission.PermissionImpersonationPrivilege;
import com.altomni.apn.user.repository.permission.PermissionImpersonationPrivilegeRepository;
import com.altomni.apn.user.service.permission.PermissionImpersonationPrivilegeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Transactional
public class PermissionImpersonationPrivilegeServiceImpl implements PermissionImpersonationPrivilegeService {

    @Resource
    private PermissionImpersonationPrivilegeRepository permissionImpersonationPrivilegeRepository;

    @Override
    public List<PermissionImpersonationPrivilege> findAll() {
        return permissionImpersonationPrivilegeRepository.findAll();
    }

    @Override
    public List<PermissionImpersonationPrivilege> saveAll(Collection<PermissionImpersonationPrivilege> impersonationPrivileges) {
        permissionImpersonationPrivilegeRepository.deleteAll();
        return permissionImpersonationPrivilegeRepository.saveAll(impersonationPrivileges);
    }
}