package com.altomni.apn.user.service.management;

import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.user.service.dto.user.TenantAddressVO;
import com.altomni.apn.user.service.dto.user.TenantDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

@Component
@FeignClient(value = "management-service")
public interface ManagementService {

    @GetMapping("/management/api/v3/public/tenants/{id}")
    ResponseEntity<Tenant> queryTenant(@PathVariable("id") Long id);

    @GetMapping("/management/api/v3/public/tenants/list")
    ResponseEntity<List<Tenant>> getAllTenant(@RequestParam("status") Integer status);

    @PutMapping("/management/api/v3/common/tenants/{id}")
    ResponseEntity<TenantDTO> updateTenant(@PathVariable("id") Long id, @Valid @RequestBody TenantDTO tenant);

    @GetMapping("/management/api/v3/public/tenants/address/{id}")
    ResponseEntity<TenantAddressVO> queryTenantAddress(@PathVariable("id") Long id);

}
