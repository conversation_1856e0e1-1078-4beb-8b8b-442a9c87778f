package com.altomni.apn.user.web.rest.vm.system;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;


@Data
@Accessors(chain = true)
public class StatisticOnlineUser implements Serializable {

    private String id;

    private Long userId;

    private Long tenantId;

    private String date;

    private Integer duration; // Minutes

    private List<OnlineDetail> onlineDetails;


    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    public static class OnlineDetail{

        private String startTime;

        private String endTime;

    }
}
