package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.user.domain.customconfig.JobFormConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data R2DBC repository for the JobFormConfig entity.
 */
@Repository
public interface JobFormConfigRepository extends JpaRepository<JobFormConfig, Long> {
    List<JobFormConfig> findAllByIdIn(List<Long> ids);

    JobFormConfig findByRecruitmentProcessId(Long recruitmentProcessId);
}

