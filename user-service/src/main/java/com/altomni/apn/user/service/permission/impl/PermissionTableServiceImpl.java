package com.altomni.apn.user.service.permission.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.repository.permission.PermissionTableRepository;
import com.altomni.apn.user.service.dto.permission.PermissionTableDTO;
import com.altomni.apn.user.service.mapper.permission.TableMapper;
import com.altomni.apn.user.service.permission.PermissionTableService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTenantInvolvedDataPermissionModuleVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PermissionTableServiceImpl implements PermissionTableService {

    @Resource
    private PermissionTableRepository permissionTableRepository;

    @Resource
    private TableMapper tableMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public PermissionTableDTO create(PermissionTableDTO permissionTableDTO) {
        if (Objects.nonNull(permissionTableDTO.getId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTABLE_CREATE_TABLEIDMUSTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return tableMapper.toDto(permissionTableRepository.save(tableMapper.toEntity(permissionTableDTO)));
    }

    @Override
    public PermissionTableDTO update(PermissionTableDTO permissionTableDTO) {
        if (Objects.isNull(permissionTableDTO.getId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONTABLE_CREATE_TABLEIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return tableMapper.toDto(permissionTableRepository.save(tableMapper.toEntity(permissionTableDTO)));
    }

    @Override
    public void delete(Long id) {
        permissionTableRepository.deleteById(id);
    }

    @Override
    public List<PermissionTableDTO> findAll() {
        return permissionTableRepository.findAll().stream().map(table -> tableMapper.toDto(table)).collect(Collectors.toList());
    }

    @Override
    public List<PermissionTableDTO> findByModuleId(Long moduleId) {
        return permissionTableRepository.findByModuleId(moduleId).stream().map(table -> tableMapper.toDto(table)).collect(Collectors.toList());
    }
}
