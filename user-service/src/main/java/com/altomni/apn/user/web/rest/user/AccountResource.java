package com.altomni.apn.user.web.rest.user;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.aop.datasync.DataSyncAspect;
import com.altomni.apn.common.aop.datasync.annotation.DataSyncAnnotation;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.auth.LoginInformation;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.email.EmailProperties;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.LoginUserDTO;
import com.altomni.apn.common.dto.email.UserResetDTO;
import com.altomni.apn.common.dto.user.LoginVM;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.*;
import com.altomni.apn.common.web.rest.CommonResource;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.enumeration.LoginType;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.authority.AuthorityService;
import com.altomni.apn.user.service.dto.user.SocialUser;
import com.altomni.apn.user.service.dto.user.UserDTO;
import com.altomni.apn.user.service.mail.MailService;
import com.altomni.apn.user.service.query.UserSearch;
import com.altomni.apn.user.service.user.SocialUserService;
import com.altomni.apn.user.service.user.UserLastLoginService;
import com.altomni.apn.user.service.user.UserService;
import com.altomni.apn.user.web.rest.vm.KeyAndPasswordVM;
import com.altomni.apn.user.web.rest.vm.ManagedUserVM;
import com.altomni.apn.user.web.rest.vm.RefreshTokenVM;
import com.altomni.apn.user.web.rest.vm.UserLastLoginVM;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.MDC;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ExecutionException;

/**
 * REST controller for managing the current user's account.
 */

@Api(tags = {"Account"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class AccountResource {

    @Resource
    private UserRepository userRepository;

    @Resource
    private UserService userService;

    @Resource
    private EmailProperties properties;

    @Resource
    private SocialUserService socialUserService;

    @Resource
    private UserLastLoginService userLastLoginService;


    @Resource
    private AuthorityService authorityService;

    @Resource
    private MailService mailService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;


    /**
     * POST  /register : register the user.
     *
     * @param managedUserVM the managed user View Model
     * @return the ResponseEntity with status 201 (Created) if the user is registered or 400 (Bad Request) if the login or email is already in use
     */
    @DataSyncAnnotation(dataType = SyncIdTypeEnum.USER)
    @ApiOperation(value = "Recruiter Registration", notes = "Tenant id is required to register recruiter (user)", code = 200)
    @PostMapping(path = "/register",
            produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
    public ResponseEntity registerAccount(@Valid @RequestBody ManagedUserVM managedUserVM) {
        User user = userService.registerAccount(managedUserVM);
        LoginVM loginVM = new LoginVM();
        loginVM.setUsername(user.getUsername());
        loginVM.setPassword(managedUserVM.getPassword());
        Map<String, String> msg = new HashMap<>();
        msg.put("code", HttpStatus.OK + "");
        msg.put("content", "Congrats! You have just registered successfully, please contact your manager to active your account.");
        MDC.put(DataSyncAspect.SYNC_DATA_ID, String.valueOf(user.getId()));
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    @Deprecated
    @DataSyncAnnotation(dataType = SyncIdTypeEnum.USER)
    @ApiOperation(value = "Consumer Registration", notes = "Consumer (end user) will have tenant id 1, the rest is the same as recruiter.", code = 200, tags = {"Wechat"})
    @PostMapping(path = "/consumer/register",
            produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
    public ResponseEntity<User> registerConsumerAccount(@Valid @RequestBody ManagedUserVM managedUserVM) {
        //consumer has tenant id 1
        managedUserVM.setTenantId(1L);
        User user = userService.registerAccount(managedUserVM);
        LoginVM loginVM = new LoginVM();
        loginVM.setUsername(user.getUsername());
        loginVM.setPassword(managedUserVM.getPassword());
        MDC.put(DataSyncAspect.SYNC_DATA_ID, String.valueOf(user.getId()));
        return new ResponseEntity(userService.login(loginVM), HttpStatus.OK);
    }

    private ResponseEntity<LoginUserDTO> loginHelper(LoginVM loginVM, HttpServletRequest request){
        loginVM.setIp(ServletUtil.getClientIP(request));
        LoginUserDTO login = userService.login(loginVM);
        asyncUserLastLoginLog(login, loginVM);
        return ResponseEntity.ok(login);
    }

//    @ApiOperation(value = "User Login", response = User.class, tags = {"Wechat"})
//    @PostMapping(path = "/login", produces = {MediaType.APPLICATION_JSON_VALUE})
//    public ResponseEntity<LoginUserDTO> login(@Valid @RequestBody LoginVM loginVM, HttpServletRequest request) {
//        loginVM.setIp(ServletUtil.getClientIP(request));
//        LoginUserDTO login = userService.login(loginVM);
//        asyncUserLastLoginLog(login, loginVM);
//        return new ResponseEntity<>(login, HttpStatus.OK);
//    }
    @ApiOperation(value = "User Login", response = User.class, tags = {"Login"})
    @PostMapping(path = "/login", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<LoginUserDTO> login(@Valid @RequestBody LoginVM loginVM, HttpServletRequest request) {
        loginVM.setTargetUserId(null);
        return this.loginHelper(loginVM, request);
    }

    @ApiOperation(value = "User Impersonation Login", response = User.class, tags = {"Impersonation Login"})
    @PostMapping(path = "/login/impersonation", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<LoginUserDTO> impersonationLogin(@Valid @RequestBody LoginVM loginVM, HttpServletRequest request) {
        if (Objects.isNull(loginVM.getTargetUserId())){
            throw new CustomParameterizedException("Target user id is required!");
        }
        return this.loginHelper(loginVM, request);
    }

    @ApiOperation(value = "User Logout", response = Void.class, tags = {"Logout"})
    @PutMapping(path = "/logout")
    public ResponseEntity<Void> logout() {
        return ResponseEntity.ok().build();
    }

    @PostMapping(path = "/refresh-token", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<CredentialDTO> refreshToken(@Valid @RequestBody RefreshTokenVM refreshTokenVM) {
        log.info("User {} request to refresh token", SecurityUtils.getUserId());
        return new ResponseEntity<>(authorityService.refreshToken(refreshTokenVM).getBody(), HttpStatus.OK);
    }

    private void asyncUserLastLoginLog(LoginUserDTO login, LoginVM loginVM) {
        try {
            UserLastLoginVM userLastLogin = new UserLastLoginVM();
            userLastLogin.setTimeZone(loginVM.getTimeZone());
            userLastLogin.setUserAgent(loginVM.getUserAgent());
            userLastLogin.setUserId(login.getId());
            userLastLogin.setIp(loginVM.getIp());
            userLastLogin.setLastLoginTime(Instant.now());
            if (StrUtil.isBlank(userLastLogin.getTimeZone())) {
                userLastLogin.setTimeZone(DateUtil.US_LA_TIMEZONE);
            }
            userLastLoginService.asyncSaveUserLastLoginLog(userLastLogin);
        } catch (Exception e) {
            log.error("userLastLogin toEntity is error , message = [{}]", ExceptionUtils.getStackTrace(e));
        }
    }

    @ApiOperation(value = "Social Login", response = User.class, tags = {"Wechat"})
    @PostMapping(path = "/login/social", produces = {MediaType.APPLICATION_JSON_VALUE})
    public ResponseEntity<User> socialLogin(@Valid @RequestBody JSONObject userObj) {
        String provider = userObj.getString("provider");
        SocialUser socialUser;
        if (provider.equals(LoginType.LINKEDIN.name())) {
            socialUser = socialUserService.linkedinLogin(userObj.getString("code"), userObj.getString("redirect_uri"));
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_SOCIALLOGIN_INVALIDUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return new ResponseEntity<>(socialUserService.socialLogin(socialUser), HttpStatus.OK);
    }

    /**
     * GET  /account : get the current user.
     *
     * @return the ResponseEntity with status 200 (OK) and the current user in body, or status 500 (Internal Server Error) if the user couldn't be returned
     */
    @ApiOperation(value = "Get my account", response = UserDTO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "beaer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/account")
    public ResponseEntity<UserDTO> getAccount() throws ExecutionException, InterruptedException {
        log.info("REST request to get my account: user id {}", SecurityUtils.getUserId());
        return ResponseEntity.ok(userService.getAccount());
    }

    /**
     * POST  /account : update the current user information.
     *
     * @param userDTO the current user information
     * @return the ResponseEntity with status 200 (OK), or status 400 (Bad Request) or 500 (Internal Server Error) if the user couldn't be updated
     */
    @ApiOperation(value = "Update my account")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "beaer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping("/account")
    public ResponseEntity<UserDTO> saveAccount(@Valid @RequestBody UserDTO userDTO) {
        Optional<LoginInformation> currentUserLogin = SecurityUtils.getCurrentUserLogin();
        final String userLogin = currentUserLogin.get().getUsername();
        Optional<User> existingUser = userRepository.findOneByEmail(userDTO.getEmail());
        if (existingUser.isPresent() && (!existingUser.get().getUsername().equalsIgnoreCase(userLogin))) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_EMAILEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return userService
                .findOneByLogin(userLogin)
                .map(u -> {
                    userService.updateUser(userDTO.getFirstName(), userDTO.getLastName(), userDTO.getEmail(),
                            userDTO.getLangKey(), userDTO.getImageUrl());
                    return ResponseEntity.ok(new UserDTO(userService.getCurrentUser()));
                })
                .orElseGet(() -> new ResponseEntity<>(HttpStatus.INTERNAL_SERVER_ERROR));
    }

    /**
     * POST  /account/change_password : changes the current user's password
     *
     * @param passwordMap the new password
     * @return the ResponseEntity with status 200 (OK), or status 400 (Bad Request) if the new password is not strong enough
     */
    @ApiOperation(value = "Change password")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "beaer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @PostMapping(path = "/account/change_password")
    public ResponseEntity changePassword(@RequestBody Map<String, String> passwordMap) {
        String password = passwordMap.get("password");
        userService.changePassword(password);
        return new ResponseEntity<>(HttpStatus.OK);
    }

    /**
     * {@code POST   /reset-password/init} : Send an email to reset the password of the user.
     *
     * @param mail the mail of the user.
     */
    @PostMapping(path = "/reset-password/init")
    public void requestPasswordReset(@RequestBody String mail) {
        LoginUtil.simulateLoginWithClient();
        Optional<User> user = userService.requestPasswordReset(mail);
        if (user.isPresent()) {
            UserResetDTO userResetDTO = new UserResetDTO();
            ServiceUtils.myCopyProperties(user.get(), userResetDTO);
            mailService.sendResetPasswordEmail(userResetDTO);
        } else {
            // Pretend the request has been successful to prevent checking which emails really exist
            // but log that an invalid attempt has been made
            log.warn("Password reset requested for non existing mail");
        }
    }

    /**
     * POST   /account/reset_password/finish : Finish to reset the password of the user
     *
     * @param keyAndPassword the generated key and the new password
     * @return the ResponseEntity with status 200 (OK) if the password has been reset,
     * or status 400 (Bad Request) or 500 (Internal Server Error) if the password could not be reset
     */
    @ApiOperation(value = "Finish reset password", notes = "Use the reset key from email link to reset password")
    @PostMapping(path = "/account/reset-password/finish",
            produces = MediaType.TEXT_PLAIN_VALUE)
    public ResponseEntity<String> finishPasswordReset(@RequestBody @Valid KeyAndPasswordVM keyAndPassword) {
        return userService.completePasswordReset(keyAndPassword.getNewPassword(), keyAndPassword.getKey())
                .map(user -> new ResponseEntity<String>(HttpStatus.OK))
                .orElse(new ResponseEntity<>(HttpStatus.BAD_REQUEST));
    }

    /**
     * POST  limit-user/register : limit user register.
     *
     * @param managedUserVM the managed user View Model
     * @return the ResponseEntity with status 201 (Created) if the user is registered or 400 (Bad Request) if the login or email is already in use
     */
    @ApiOperation(value = "Recruiter Registration", notes = "Tenant id is required to register recruiter (user)", code = 200)
    @PostMapping(path = "/limit-user/register",
            produces = {MediaType.APPLICATION_JSON_VALUE, MediaType.TEXT_PLAIN_VALUE})
    public ResponseEntity limitUserRegister(@Valid @RequestBody ManagedUserVM managedUserVM) {
        if (managedUserVM.getTenantId() == null || managedUserVM.getTenantId().equals(1L)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_TENANTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        userService.findOneByLogin(managedUserVM.getEmail()).ifPresent(user -> {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_EMAILEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        });
        if (managedUserVM.getUsername() != null) {
            userService.findOneByLogin(managedUserVM.getUsername()).ifPresent(user -> {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_VALIDATEACCOUNT_USERNAMEEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
            });
        }
        User user = userService.createLimitUser(managedUserVM);
        LoginVM loginVM = new LoginVM();
        loginVM.setUsername(user.getUsername());
        loginVM.setPassword(managedUserVM.getPassword());

        Map<String, String> msg = new HashMap<>();
        msg.put("code", HttpStatus.OK + "");
        msg.put("content", "Congrats! You have just registered successfully, please contact your manager to active your account.");
        return new ResponseEntity<>(msg, HttpStatus.OK);
    }

    /**
     * GET  /users : get all the users which belong to my tenant and whose names match the keyword.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of users in body
     */
    @ApiOperation(value = "Get/Filter users belong to my tenant and match the name keyword", notes = "Use search query to search for users. Search query is search=name:value,name2:value2. Will search name contains " +
            "value and name2 contains value2. Use sort=name,desc(asc) to sort results. Use page=0&size=40 to control pagination.")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "Authentication", value = "bearer {{access token}}", required = true, dataType = "string", paramType = "header")
    })
    @GetMapping("/users/query")
    public ResponseEntity<List<UserBriefDTO>> searchUsers(
            @ApiParam(value = "search params") @RequestParam(value = "search", required = false) String search,
            @RequestParam(value = "name", required = false) String name,
            Pageable pageable) {
        log.info("[APN: User @{}] REST request to get users: name={}", SecurityUtils.getUserId(), name);
        Page<User> userPage = userService.findByUserName(UserSearch.getPredicate(search, name), pageable); //Current tenantId is checked in UserSearch.getPredicate(search, name)
        return new ResponseEntity<>(userService.toUserBriefDTOList(userPage.getContent()), PaginationUtil.generatePaginationHttpHeaders(userPage, "/users/search"), HttpStatus.OK);
    }

    @GetMapping("/liveness")
    public ResponseEntity<String> getLiveness() {
        // TODO: whilte list check for security
        return CommonResource.getLiveness(log);
    }

    /**
     * 更具用户id 获取teamIds ，用户下配置的主team和副team的以及所有的子团队id
     *
     * @return the ResponseEntity with status 200 (OK) and the current user in body, or status 500 (Internal Server Error) if the user couldn't be returned
     */
    @GetMapping("/teams-by-user-id/{userId}")
    public ResponseEntity<List<Long>> getTeamsByUserId(@PathVariable("userId") Long userId) {
        log.info("REST request to get teams by user id {}", userId);
        return ResponseEntity.ok(userService.getTeamsByUserId(userId));
    }


}
