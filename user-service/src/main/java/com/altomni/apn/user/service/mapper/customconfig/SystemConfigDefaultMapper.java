package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.service.dto.customconfig.SystemConfigDTO;
import com.altomni.apn.user.service.mapper.EntityMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

@Mapper(componentModel = "spring", uses = {})
public interface SystemConfigDefaultMapper extends EntityMapper<SystemConfigDTO, SystemConfigDefault> {
    default SystemConfigDefault fromId(Long id) {
        if (id == null) {
            return null;
        }
        SystemConfigDefault systemConfigDefault = new SystemConfigDefault();
        systemConfigDefault.setId(id);
        return systemConfigDefault;
    }

    @Mapping(source = "customConfig", target = "defaultConfig", qualifiedByName = "jsonNodeToString")
    public SystemConfigDefault toEntity(SystemConfigDTO dto);

    @Mapping(source = "defaultConfig", target = "customConfig" , qualifiedByName="stringToJsonNode")
    public SystemConfigDTO toDto(SystemConfigDefault entity);

    @Named("jsonNodeToString")
    default String jsonNodeToString(JsonNode jsonNode) {
        if (jsonNode == null) {
            return null;
        }
        return jsonNode.toString();
    }

    @Named("stringToJsonNode")
    default JsonNode stringToJsonNode(String jsonString) {
        if (jsonString == null || jsonString.isEmpty()) {
            return null;
        }
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.readTree(jsonString);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Error converting String to JsonNode", e);
        }
    }
}
