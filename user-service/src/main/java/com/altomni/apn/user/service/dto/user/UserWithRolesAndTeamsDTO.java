package com.altomni.apn.user.service.dto.user;

import com.altomni.apn.common.domain.permission.PermissionTeamSimple;
import com.altomni.apn.common.domain.user.User;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.Instant;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * A DTO representing a user, with his authorities.
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@ApiModel
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserWithRolesAndTeamsDTO {

    private Long id;

    private String username;

    private String firstName;

    private String lastName;

    private String fullName;

    private String email;

    private boolean activated;

    private Long tenantId;

    private Integer jobPermission;

    private Instant createdDate;

    private Set<Map.Entry> authorities;

    private Set<PermissionTeamSimple> teams;

    public UserWithRolesAndTeamsDTO(User user) {
        this.id = user.getId();
        this.username = user.getUsername();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.email = user.getEmail();
        this.tenantId = user.getTenantId();
        this.activated = user.isActivated();
        this.createdDate = user.getCreatedDate();
        this.authorities = user.getRoles().stream().map(role -> Map.entry("name", role.getName())).collect(Collectors.toSet());
        this.teams = user.getTeams();
    }
}
