package com.altomni.apn.user.web.rest.vm.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor

public class DeliveryStatVM implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long count;

    private String name;


    public DeliveryStatVM(Long id, Long count) {
        this.id = id;
        this.count = count;
    }
}
