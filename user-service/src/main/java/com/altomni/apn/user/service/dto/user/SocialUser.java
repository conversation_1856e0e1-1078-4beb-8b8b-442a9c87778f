package com.altomni.apn.user.service.dto.user;

import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.user.domain.enumeration.LoginType;
import com.altomni.apn.user.domain.user.SocialConsumer;
import org.hibernate.validator.constraints.Email;

import javax.validation.constraints.NotNull;

public abstract class SocialUser {

    @NotNull
    public LoginType provider;

    @Email
    public String email;

    public String phone;

    public String id;

    public String code;

    public LoginType getProvider() {
        return provider;
    }

    public void setProvider(LoginType provider) {
        this.provider = provider;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public abstract User syncUser(User existingUser);

    public abstract SocialConsumer consumer();

    public String identifier() {
        if (email != null) {
            return email;
        }
        else if (phone != null) {
            return phone;
        }
        return null;
    }

}
