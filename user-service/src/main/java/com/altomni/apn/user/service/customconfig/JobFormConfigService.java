package com.altomni.apn.user.service.customconfig;


import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.user.service.dto.customconfig.JobFormConfigDTO;

public interface JobFormConfigService {

    BaseConfig getFormConfigByRecruitmentProcessId(Long recruitmentProcessId) ;

    BaseConfig getFormConfigByRecruitmentProcessIdForPrivateJob(Long recruitmentProcessId) ;

    JobFormConfigDTO saveJobFormConfig(JobFormConfigDTO userFormConfigDTO, Long recruitmentProcessId);

    JobFormConfigDTO savePrivateJobFormConfig(JobFormConfigDTO userFormConfigDTO, Long recruitmentProcessId);

}
