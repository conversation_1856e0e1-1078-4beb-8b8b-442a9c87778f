package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The CustomerType enumeration.
 */
public enum CustomerType implements ConvertedEnum<Integer> {

    COMPANY_CLIENT(10),

    COMPANY_PROSPECT(20),

    COMPANY_AFFILIATES(30);

    private final int dbValue;

    CustomerType(int dbValue) {
        this.dbValue = dbValue;
    }

    @Override
    public Integer toDbValue() {
        return dbValue;
    }

    // static resolving:
    public static final ReverseEnumResolver<CustomerType, Integer> resolver =
        new ReverseEnumResolver<>(CustomerType.class, CustomerType::toDbValue);

    public static CustomerType fromDbValue(Integer dbValue) {
        return resolver.get(dbValue);
    }
}
