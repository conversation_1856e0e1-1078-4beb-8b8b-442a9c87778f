package com.altomni.apn.user.service.authority;

import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.dto.user.LoginVM;
import com.altomni.apn.user.web.rest.vm.RefreshTokenVM;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;


@Component
@FeignClient(value = "authority-service")
public interface AuthorityService {

    @PostMapping("/authority/api/v3/credential")
    ResponseEntity<CredentialDTO> findCredential(@RequestBody LoginVM loginVM);

    @PostMapping("/authority/api/v3/credential/admin")
    ResponseEntity<CredentialDTO> findAdminCredential(@RequestBody LoginVM loginVM);

    @PostMapping(path = "/authority/api/v3/refresh-token", produces = {MediaType.APPLICATION_JSON_VALUE})
    ResponseEntity<CredentialDTO> refreshToken(@Valid @RequestBody RefreshTokenVM refreshTokenVM);
}
