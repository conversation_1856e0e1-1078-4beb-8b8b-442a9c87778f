package com.altomni.apn.user.service.dto.user;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.Instant;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UserImpersonationDTO {

    private Long id;
    private UserDTO grantFromUser;
    private UserDTO grantToUser;
    private Instant effectiveStartAt;
    private Instant expireAt;

    public UserImpersonationDTO(Long id, Long grantToUserId, String grantToEmail, String grantToFirstName, String grantToLastName, Instant effectiveStartAt, Instant expireAt) {
        this.id = id;
        this.grantToUser = new UserDTO(grantToUserId, grantToEmail, grantToFirstName +" " + grantToLastName);
        this.effectiveStartAt = effectiveStartAt;
        this.expireAt = expireAt;
    }

    public UserImpersonationDTO(Long id, Long grantFromUserId, String grantFromEmail, String grantFromFirstName, String grantFromLastName,
                                Long grantToUserId, String grantToEmail, String grantToFirstName, String grantToLastName,
                                Instant effectiveStartAt, Instant expireAt) {
        this.id = id;
        this.grantFromUser = new UserDTO(grantFromUserId, grantFromEmail, grantFromFirstName + " " + grantFromLastName);
        this.grantToUser = new UserDTO(grantToUserId, grantToEmail, grantToFirstName + " " + grantToLastName);
        this.effectiveStartAt = effectiveStartAt;
        this.expireAt = expireAt;
    }

    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserDTO{
        private Long id;
        private String email;
        private String fullName;
        public UserDTO(Long id){
            this.id = id;
        }
    }
}