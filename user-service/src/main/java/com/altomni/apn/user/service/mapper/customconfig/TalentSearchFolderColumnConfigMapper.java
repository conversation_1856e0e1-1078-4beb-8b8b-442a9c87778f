package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.service.dto.customconfig.TalentColumnConfigDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;


@Mapper(componentModel = "spring", uses = JsonMapperUtil.class)
public interface TalentSearchFolderColumnConfigMapper {

    default UserPreference fromId(Long id) {
        if (id == null) {
            return null;
        }
        UserPreference userPreference = new UserPreference();
        userPreference.setId(id);
        return userPreference;
    }


    @Mapping(target = "talentSearchFolderColumnConfig", source = "customConfig", qualifiedByName = "customColumnConfigToJsonString")
    UserPreference toEntity(TalentColumnConfigDTO dto);

    @Mapping(target = "customConfig", source = "talentSearchFolderColumnConfig", qualifiedByName = "jsonStringToCustomColumnConfig")
    TalentColumnConfigDTO toDto(UserPreference entity);

}