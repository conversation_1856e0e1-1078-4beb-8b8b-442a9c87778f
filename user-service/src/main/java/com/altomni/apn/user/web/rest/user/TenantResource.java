package com.altomni.apn.user.web.rest.user;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.aop.user.AttachSimpleUser;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.dto.parser.TenantWatermarkDTO;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.ResponseUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.service.dto.user.TenantCreditInfoVO;
import com.altomni.apn.user.service.dto.user.TenantDTO;
import com.altomni.apn.user.service.dto.user.TenantVO;
import com.altomni.apn.user.service.tenant.TenantPushConfigService;
import com.altomni.apn.user.service.tenant.TenantService;
import com.altomni.apn.user.web.rest.vm.tenant.TenantPushRuleDTO;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Tenant.
 * <AUTHOR>
 */
@Api(hidden = true, tags = {"APN-Tenant-admin"})
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class TenantResource {

    private static final String ENTITY_NAME = "tenant";

    @Resource
    private TenantService tenantService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @GetMapping("/tenants/watermark/{tenantId}")
    public ResponseEntity<TenantWatermarkDTO> getTenantWatermarkConfig(@PathVariable("tenantId") Long tenantId){
        log.info("[APN Management: Tenant @{}] REST request to get tenants watermark config", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(tenantService.getTenantWatermarkConfig(tenantId));
    }

    /**
     * POST  /tenants : Create a new tenant.
     *
     * @param tenant the tenant to create
     * @return the ResponseEntity with status 201 (Created) and with body the new tenant, or with status 400 (Bad Request) if the tenant has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
//    @PostMapping("/tenants")
//    @NoRepeatSubmit
//    public ResponseEntity<TenantDTO> createTenant(@Valid @RequestBody TenantDTO tenant) throws URISyntaxException {
//        log.info("[APN: Tenant @{}] REST request to save Tenant : {}", SecurityUtils.getUserId(), tenant);
//        if (tenant.getId() != null) {
//            throw new CustomParameterizedException("The object to create cannot already have an id.");
//        }
//        TenantDTO result = tenantService.create(tenant);
//        return ResponseEntity.ok(result);
//    }

    /**
     * PUT  /tenants : Updates an existing tenant.
     *
     * @param tenant the tenant to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated tenant,
     * or with status 400 (Bad Request) if the tenant is not valid,
     * or with status 500 (Internal Server Error) if the tenant couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/tenants")
    @NoRepeatSubmit
    public ResponseEntity<TenantDTO> updateTenant(@Valid @RequestBody TenantDTO tenant) throws URISyntaxException {
        log.info("[APN: Tenant @{}] REST request to update Tenant : {}", SecurityUtils.getUserId(), tenant);
        if (tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_UPDATETENANT_TENANTIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        TenantDTO result = tenantService.update(tenant);
        return ResponseEntity.ok()
            .body(result);
    }

    /**
     * GET  /tenants : get all the tenants.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of tenants in body
     */
    @GetMapping("/tenants")
    public ResponseEntity<List<Tenant>> getAllTenants() {
        log.info("[APN: Tenant @{}] REST request to get all Tenants for register.");
        return ResponseEntity.ok(tenantService.findActiveTenant());
    }

//    @GetMapping("/tenants/details")
//    @NoRepeatSubmit
//    public ResponseEntity<List<TenantVO>> getAllTenantsDetails() {
//        log.info("[APN: Tenant @{}] REST request to get all Tenants details", SecurityUtils.getUserId());
//        return ResponseEntity.ok(tenantService.findAll());
//    }


    /**
     * GET  /tenants/:id : get the "id" tenant.
     *
     * @param id the id of the tenant to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the tenant, or with status 404 (Not Found)
     */
//    @GetMapping("/tenants/{id}")
//    @NoRepeatSubmit
//    public ResponseEntity<TenantVO> getTenant(@PathVariable Long id) {
//        log.info("[APN: Tenant @{}] REST request to get Tenants : {}", SecurityUtils.getUserId(), id);
//        TenantVO tenant = tenantService.findOne(id);
//        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(tenant));
//    }
//
    @GetMapping("/normal-user/tenant")
    public ResponseEntity<TenantVO> getTenantByNormalUser() {
        log.info("[APN: Tenant @{}] REST request to get Tenants by normal user : {}", SecurityUtils.getUserId());
        TenantVO tenant = tenantService.findOne(SecurityUtils.getTenantId());
        return ResponseUtil.wrapOrNotFound(Optional.ofNullable(tenant));
    }



    @GetMapping("/tenants/credit/{id}")
    public ResponseEntity<TenantCreditInfoVO> getAvailableCredit(@PathVariable Long id) {
        log.info("[APN: Tenant @{}] REST request to get tenant available credit : {}", SecurityUtils.getUserId(), id);
        TenantCreditInfoVO info = tenantService.findAvailableCredit(id);
        return ResponseEntity.ok(info);
    }

//    @PutMapping("/tenants/{id}/active")
//    @NoRepeatSubmit
//    public ResponseEntity<HttpStatus> updateTenantStatusToActive(@PathVariable("id") Long id, @RequestBody TenantUpdateStatusDTO dto) {
//        log.info("[APN: Tenant @{}] REST request to update tenant status to active: {}", SecurityUtils.getUserId(), id);
//        tenantService.updateTenantStatusToActive(id, dto);
//        return new ResponseEntity<>(HttpStatus.CREATED);
//    }
//
//    @PutMapping("/tenants/{id}/inActive")
//    @NoRepeatSubmit
//    public ResponseEntity<HttpStatus> updateTenantStatusToInActive(@PathVariable("id") Long id) {
//        log.info("[APN: Tenant @{}] REST request to update tenant status to inActive: {}", SecurityUtils.getUserId(), id);
//        tenantService.updateTenantStatusToInActive(id);
//        return new ResponseEntity<>(HttpStatus.CREATED);
//    }

    @Resource
    private TenantPushConfigService tenantPushConfigService;

    @PostMapping("/tenants/push-rule")
    @NoRepeatSubmit
    public ResponseEntity<Void> createTenantPushRule(@RequestBody TenantPushRuleDTO tenantPushRuleDTO) {
        log.info("[APN: Tenant @{}] REST request to create Tenant push rule : {}", SecurityUtils.getUserId(), tenantPushRuleDTO);
        tenantPushConfigService.createTenantPushRule(tenantPushRuleDTO);
        return ResponseEntity.ok()
                .build();
    }

    @PutMapping("/tenants/push-rule/{id}")
    @NoRepeatSubmit
    public ResponseEntity<Void> updateTenantPushRule(@PathVariable Long id, @RequestBody TenantPushRuleDTO tenantPushRuleDTO) {
        log.info("[APN: Tenant @{}] REST request to update Tenant push rule : {}", SecurityUtils.getUserId(), tenantPushRuleDTO);
        tenantPushRuleDTO.setId(id);
        tenantPushConfigService.updateTenantPushRule(tenantPushRuleDTO);
        return ResponseEntity.ok()
                .build();
    }

    @DeleteMapping("/tenants/push-rule/{id}")
    @NoRepeatSubmit
    public ResponseEntity<Void> updateTenantPushRule(@PathVariable Long id) {
        log.info("[APN: Tenant @{}] REST request to delete Tenant push rule : {}", SecurityUtils.getUserId(), id);
        tenantPushConfigService.deleteTenantPushRule(id);
        return ResponseEntity.ok()
                .build();
    }

    @GetMapping("/tenants/push-rule")
    @NoRepeatSubmit
    @AttachSimpleUser
    public ResponseEntity<List<TenantPushRuleDTO>> getTenantPushRule() {
        log.info("[APN: Tenant @{}] REST request to get Tenant push rule", SecurityUtils.getUserId());
        ;
        return ResponseEntity.ok(tenantPushConfigService.getTenantPushRule());
    }
}
