package com.altomni.apn.user.service.customconfig;


import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.user.domain.customconfig.PipelineColumnPreference;
import com.altomni.apn.user.service.dto.customconfig.PipelineColumnPreferenceDTO;

import java.util.List;

/***
 * pipeline column preference with template name
 */
public interface PipelineColumnPreferenceService {


    List<BaseConfig> findByUserId(Long userId);


    BaseConfig getTalentPipelineColumnConfigByTenatId(Long tenantId);

    void deleteById(Long id);


    BaseConfig createColumnConfig(PipelineColumnPreferenceDTO pipelineColumnPreferenceDTO);

    BaseConfig updateColumnConfig(PipelineColumnPreferenceDTO pipelineColumnPreferenceDTO, Long templateId);
}
