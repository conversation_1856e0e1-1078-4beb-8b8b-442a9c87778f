package com.altomni.apn.user.service.user.impl;

import com.altomni.apn.common.aop.datasync.template.DataSyncHandlerTemplate;
import com.altomni.apn.common.config.application.EsFillerMqBaseProperties;
import com.altomni.apn.common.config.application.LarkProperties;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.service.rabbitmq.RabbitMqService;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.service.user.EsFillerUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.concurrent.CountDownLatch;

@Slf4j
@Service("userSyncDataHandler")
public class UserSyncDataHandler extends DataSyncHandlerTemplate {

    @Resource
    private EsFillerUserService esFillerUserService;

    public UserSyncDataHandler(CanalService canalService,
                               LarkProperties larkProperties,
                               EsFillerMqBaseProperties esFillerMqBaseProperties,
                               RabbitMqService rabbitMqService) {
        super(canalService, SyncIdTypeEnum.USER, larkProperties, esFillerMqBaseProperties, rabbitMqService);
    }

    @Override
    protected void extractDataToMq(Collection<Long> ids, int priority, CountDownLatch countDownLatch) {
        try {
            esFillerUserService.extractDbDataToMq(ids, priority);
            //同步数据去 jobdiva
            esFillerUserService.buildJsonToJobdivaMq(ids, priority);
        } catch (Exception e) {
            log.error("[EsFillerUserService: syncUserToMQ @{}] scheduledSyncUsersToMQ is error, userIds: {}, error {}", SecurityUtils.getUserId(), ids, ExceptionUtils.getStackTrace(e));
            canalService.insertAll(ids, SyncIdTypeEnum.USER, FailReasonEnum.ERROR, e.getMessage(), priority);
            String message = "Extract User Error" +
                    "\n\tUser IDs: " + ids +
                    "\n\tError: " +
                    "\n\t" + ExceptionUtils.getStackTrace(e);
            NotificationUtils.sendAlertToLark(larkProperties.getLarkWebhookKey(), larkProperties.getLarkWebhookUrl(), message);
        } finally {
            countDownLatch.countDown();
        }
    }

    @Override
    protected Integer checkMessageCountByTypeQueueName() {
        //查询自己同步数据类型的队列数量, 不需要回填则返回 0
//        return rabbitMqService.checkMessageCount(queueName);
        return 0;
    }

}
