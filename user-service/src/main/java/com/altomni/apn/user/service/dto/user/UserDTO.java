package com.altomni.apn.user.service.dto.user;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.permission.PermissionTeamSimple;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.domain.user.UserLanguageRelation;
import com.altomni.apn.common.dto.permission.PermissionPrivilegeDataSimple;
import com.altomni.apn.user.domain.enumeration.CreditEffectType;
import com.altomni.apn.user.service.dto.permission.PermissionUserTeamDTO;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Email;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * A DTO representing a user, with his authorities.
 */
@ApiModel
@Data
public class UserDTO {

    private Long id;

    @ApiModelProperty(value = "optional username")
    @Pattern(regexp = Constants.USERNAME_REGEX,message = "The name format is incorrect.")
    @Size(min = 5, max = 50,message = "username length must between 5 to 50 ")
    private String username;

    @Size(max = 50)
    private String firstName;

    @Size(max = 50)
    private String lastName;

    @Email
    @NotNull
    @Size(min = 5, max = 100)
    private String email;

    private String phone;

    private String note;

    @ApiModelProperty(value = "url link to user's image")
    @Size(max = 256)
    private String imageUrl;

    private Boolean activated;

    /**
     * 去掉默认值，DTO里不给默认值，否则会导致部分逻辑copyProperties时覆盖原有数据库结果
     */
    @ApiModelProperty(value = "user's preferred language. Default is en")
    @Size(min = 2, max = 5)
    private String langKey;

    @JsonIgnore
    private String createdBy;

    private Instant createdDate;

    @JsonIgnore
    private String lastModifiedBy;

    @JsonIgnore
    private Instant lastModifiedDate;

    @ApiModelProperty(value = "The tenant id recruiter belongs to. It is required for recruiter. For consumer, it is internally set to 1")
    private Long tenantId;

    @ApiModelProperty(value = "monthly credit")
    private Integer monthlyCredit;

    @ApiModelProperty(value = "bulkCredit")
    private Integer bulkCredit;

    @ApiModelProperty(value = "creditEffectType")
    private CreditEffectType creditEffectType;

    @ApiModelProperty(value = "effectCredit")
    private Integer effectCredit;

    private Integer usedMonthlyCredit;

    private Integer usedBulkCredit;

    public static final Set<String> UPDATE_SKIP_PROPERTIES = new HashSet<>(Arrays.asList("id", "talentId", "tenant", "authorities", "teams"));

    // ************************************relate entity*********************************************
    private List<Object> authorities;

    private Set<PermissionUserTeamDTO> teams;

    private Set<Long> teamIds;

    @JsonProperty(access = JsonProperty.Access.READ_ONLY)
    private Tenant tenant;

    private List<PermissionPrivilegeDataSimple.PermissionPrivilegeContainer> privileges;

    //private PermissionPrivilegeTreeDTO privilegeTree;

    private List<Long> companyIdsWithAm;

    private Long primaryTeamId;

    @JsonIgnore
    private Long previousTeamId;

    private Set<Long> secondaryTeamIds;

    private String jobTitle;

    private String customTimezone;

    private Integer currency;

    private Long crmUserId;

    private List<Long> languages;

    private Long levelOfExperience;

    public UserDTO() {
        // Empty constructor needed for Jackson.
    }

    public UserDTO(User user) {
        this(user.getId(), user.getUsername(), user.getFirstName(), user.getLastName(),
                user.getEmail(), user.isActivated(), user.getImageUrl(), user.getLangKey(),
                user.getCreatedBy(), user.getCreatedDate(), user.getLastModifiedBy(), user.getLastModifiedDate(),
                user.getRoles().stream().map(role -> new JSONObject(new HashMap<>(){{put("name", role.getName());}})).collect(Collectors.toList()),
                user.getTenant(), user.getTenantId(), user.getMonthlyCredit(),user.getBulkCredit(),
                user.getUsedMonthlyCredit(),user.getUsedBulkCredit(), user.getNote(), user.getTeams(), user.getPhone(), user.getLanguageRelations() ,user.getEnumLevelOfExperienceId());

    }

    public UserDTO(User user, List<Object> authorities, Set<PermissionTeamSimple> teams) {
        this(user.getId(), user.getUsername(), user.getFirstName(), user.getLastName(),
                user.getEmail(), user.isActivated(), user.getImageUrl(), user.getLangKey(),
                user.getCreatedBy(), user.getCreatedDate(), user.getLastModifiedBy(), user.getLastModifiedDate(),
                authorities, user.getTenant(), user.getTenantId(), user.getMonthlyCredit(),user.getBulkCredit(),
                user.getUsedMonthlyCredit(),user.getUsedBulkCredit(), user.getNote(), teams, user.getPhone());

    }

    public UserDTO(User user, List<Object> authorities, Set<PermissionUserTeamDTO> teams, Boolean withTeamLeader) {
        this(user.getId(), user.getUsername(), user.getFirstName(), user.getLastName(),
                user.getEmail(), user.isActivated(), user.getImageUrl(), user.getLangKey(),
                user.getCreatedBy(), user.getCreatedDate(), user.getLastModifiedBy(), user.getLastModifiedDate(),
                authorities, user.getTenant(), user.getTenantId(), user.getMonthlyCredit(),user.getBulkCredit(),
                user.getUsedMonthlyCredit(),user.getUsedBulkCredit(), user.getNote(), teams, user.getPhone(), withTeamLeader);

    }


    public UserDTO(User user, CreditEffectType creditEffectType, Integer effectCredit, Integer monthlyCreditAmount,
                   Integer bulkCredit, Integer usedMonthlyCredit, Integer usedBulkCredit, Long primaryTeamId) {
        this.id = user.getId();
        this.username = user.getUsername();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.email = user.getEmail();
        this.phone = user.getPhone();
        this.activated = user.isActivated();
        this.imageUrl = user.getImageUrl();
        this.langKey = user.getLangKey();
        this.createdBy = user.getCreatedBy();
        this.createdDate = user.getCreatedDate();
        this.lastModifiedBy = user.getLastModifiedBy();
        this.lastModifiedDate = user.getLastModifiedDate();
        this.authorities = user.getRoles().stream().map(role -> Map.entry("name", role.getName())).collect(Collectors.toList());
        this.teams = user.getTeams().stream().map(t->new PermissionUserTeamDTO(t.getId(), t.getName(), t.getId().equals(primaryTeamId), null)).collect(Collectors.toSet());
        this.teamIds = user.getTeams().stream().map(t -> t.getId()).collect(Collectors.toSet());
        this.tenant = user.getTenant();
        this.tenantId = user.getTenantId();
        this.note = user.getNote();
        this.creditEffectType = creditEffectType;
        this.effectCredit = effectCredit;
        this.monthlyCredit = monthlyCreditAmount;
        this.bulkCredit = bulkCredit;
        this.usedMonthlyCredit = usedMonthlyCredit;
        this.usedBulkCredit = usedBulkCredit;
    }

    public UserDTO(Long id, String username, String firstName, String lastName,
                   String email, boolean activated, String imageUrl, String langKey,
                   String createdBy, Instant createdDate, String lastModifiedBy, Instant lastModifiedDate,
                   List<Object> authorities, Tenant tenant, Long tenantId, Integer credit, Integer bulkCredit,
                   Integer usedMonthlyCredit , Integer usedBulkCredit, String note, Set<PermissionTeamSimple> teams,
                   String phone) {

        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.activated = activated;
        this.imageUrl = imageUrl;
        this.langKey = langKey;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.lastModifiedBy = lastModifiedBy;
        this.lastModifiedDate = lastModifiedDate;
        this.authorities = authorities;
        this.tenant = tenant;
        this.tenantId = tenantId;
        this.monthlyCredit = credit;
        this.note = note;
        this.bulkCredit = bulkCredit;
        this.usedBulkCredit = usedBulkCredit;
        this.usedMonthlyCredit = usedMonthlyCredit;
        //LinkedHashSet 保证顺序, 主team在第一个
        this.teams = teams.stream().map(t->new PermissionUserTeamDTO(t.getId(), t.getName(), null, null)).collect(Collectors.toCollection(LinkedHashSet::new));
        this.teamIds = teams.stream().map(t -> t.getId()).collect(Collectors.toSet());
        this.phone = phone;
    }

    public UserDTO(Long id, String username, String firstName, String lastName,
                   String email, boolean activated, String imageUrl, String langKey,
                   String createdBy, Instant createdDate, String lastModifiedBy, Instant lastModifiedDate,
                   List<Object> authorities, Tenant tenant, Long tenantId, Integer credit, Integer bulkCredit,
                   Integer usedMonthlyCredit , Integer usedBulkCredit, String note, Set<PermissionTeamSimple> teams,
                   String phone, List<UserLanguageRelation> languages, Long levelOfExperience) {

        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.activated = activated;
        this.imageUrl = imageUrl;
        this.langKey = langKey;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.lastModifiedBy = lastModifiedBy;
        this.lastModifiedDate = lastModifiedDate;
        this.authorities = authorities;
        this.tenant = tenant;
        this.tenantId = tenantId;
        this.monthlyCredit = credit;
        this.note = note;
        this.bulkCredit = bulkCredit;
        this.usedBulkCredit = usedBulkCredit;
        this.usedMonthlyCredit = usedMonthlyCredit;
        this.teams = teams.stream().map(t->new PermissionUserTeamDTO(t.getId(), t.getName(), null, null)).collect(Collectors.toCollection(LinkedHashSet::new));
        this.teamIds = teams.stream().map(t -> t.getId()).collect(Collectors.toSet());
        this.phone = phone;
        this.languages = CollUtil.isNotEmpty(languages) ? languages.stream().map(UserLanguageRelation::getEnumLanguageId).collect(Collectors.toList()) : null;
        this.levelOfExperience = levelOfExperience;
    }

    public UserDTO(Long id, String username, String firstName, String lastName,
                   String email, boolean activated, String imageUrl, String langKey,
                   String createdBy, Instant createdDate, String lastModifiedBy, Instant lastModifiedDate,
                   List<Object> authorities, Tenant tenant, Long tenantId, Integer credit, Integer bulkCredit,
                   Integer usedMonthlyCredit , Integer usedBulkCredit, String note, Set<PermissionUserTeamDTO> teams,
                   String phone, Boolean withTeamLeader) {

        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.activated = activated;
        this.imageUrl = imageUrl;
        this.langKey = langKey;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.lastModifiedBy = lastModifiedBy;
        this.lastModifiedDate = lastModifiedDate;
        this.authorities = authorities;
        this.tenant = tenant;
        this.tenantId = tenantId;
        this.monthlyCredit = credit;
        this.note = note;
        this.bulkCredit = bulkCredit;
        this.usedBulkCredit = usedBulkCredit;
        this.usedMonthlyCredit = usedMonthlyCredit;
        //LinkedHashSet 保证顺序, 主team在第一个
        this.teams = teams;
        this.teamIds = teams.stream().map(t -> t.getId()).collect(Collectors.toSet());
        this.phone = phone;
    }

    public UserDTO(Long id, String username, String firstName, String lastName,
                   String email, boolean activated, Long divisionId, String imageUrl, String langKey,
                   String createdBy, Instant createdDate, String lastModifiedBy, Instant lastModifiedDate,
                   Tenant tenant, Long tenantId, Integer credit, Integer bulkCredit, String note) {

        this.id = id;
        this.username = username;
        this.firstName = firstName;
        this.lastName = lastName;
        this.email = email;
        this.activated = activated;
        this.imageUrl = imageUrl;
        this.langKey = langKey;
        this.createdBy = createdBy;
        this.createdDate = createdDate;
        this.lastModifiedBy = lastModifiedBy;
        this.lastModifiedDate = lastModifiedDate;
        //this.authorities = authorities;
        this.tenant = tenant;
        this.tenantId = tenantId;
        this.monthlyCredit = credit;
        this.note = note;
        this.bulkCredit = bulkCredit;
    }
}
