package com.altomni.apn.user.service.rabbmitmq.listener;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.altomni.apn.common.domain.transactionrecord.CommonMqConsumeFailedRecord;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.folder.TeamUserSetRollList;
import com.altomni.apn.common.dto.sso.SsoUserActive;
import com.altomni.apn.common.dto.sso.SsoUserBinding;
import com.altomni.apn.common.dto.sso.SsoUserInfo;
import com.altomni.apn.common.enumeration.enums.MqTranRecordBusTypeEnums;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.user.repository.mqfailedrecord.UserMqTransactionFailedRecordRepository;
import com.altomni.apn.user.service.talent.TalentService;
import com.altomni.apn.user.service.user.UserService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.security.oauth2.resource.OAuth2ResourceServerProperties;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Component
public class SsoUserMqListener {

    @Value("${application.notification.lark.mq.webhookKey}")
    private String LARK_WEBHOOK_KEY;

    @Value("${application.notification.lark.mq.webhookUrl}")
    private String LARK_WEBHOOK_URL;

    private static final String SSO_USER_INFO_PREFIX = "APN:SSO_USER_INFO:";
    private static final String SSO_USER_ACTIVE_PREFIX = "APN:SSO_USER_ACTIVE:";
    private static final String SSO_USER_BINDING_PREFIX = "APN:SSO_USER_BINDING:";


    private final UserService userService;
    private final CommonRedisService redisService;
    private final UserMqTransactionFailedRecordRepository userMqTransactionFailedRecordRepository;
    private final OAuth2ResourceServerProperties oAuth2ResourceServerProperties;

    public SsoUserMqListener(UserService userService, CommonRedisService redisService,
                             UserMqTransactionFailedRecordRepository userMqTransactionFailedRecordRepository, OAuth2ResourceServerProperties oAuth2ResourceServerProperties) {
        this.userService = userService;
        this.redisService = redisService;
        this.userMqTransactionFailedRecordRepository = userMqTransactionFailedRecordRepository;
        this.oAuth2ResourceServerProperties = oAuth2ResourceServerProperties;
    }


    /**
     * 处理用户基本信息更新
     *
     * @param channel
     * @param message
     * @throws IOException
     */
    @RabbitListener(containerFactory = "userConsumerFactory", queues = {"${application.sso.user-info-queue}"})
    @RabbitHandler
    public void processUserInfoUpdate(Channel channel, Message message) throws IOException {
        log.info("user info update ,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        SsoUserInfo ssoUserInfo = JsonUtil.fromJson(json, SsoUserInfo.class);
        if (null == ssoUserInfo) {
            log.error("ssoUserInfo is null,param:{}", json);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        BigInteger busId = toBigInteger(ssoUserInfo.getTransactionId());
        String key = SSO_USER_INFO_PREFIX.concat(ssoUserInfo.getTransactionId());
        if (!checkExits(key, busId, MqTranRecordBusTypeEnums.SSO_USERINFO_UPDATE)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        LoginUtil.simulateLoginWithClient();
        try {
            userService.updateBYSsoUserInfo(ssoUserInfo);
            saveFailedRecord(busId, 1, ssoUserInfo, MqTranRecordBusTypeEnums.SSO_USERINFO_UPDATE);
            redisService.delete(key);
            log.info("update user info success,param:{}", ssoUserInfo);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("update user info error: ", e);
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            saveFailedRecord(busId, 0, ssoUserInfo, MqTranRecordBusTypeEnums.SSO_USERINFO_UPDATE);
            NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, String.format("user-service-> 业务类型：%s , 数据：%s, 消费数据失败，请人工处理。", MqTranRecordBusTypeEnums.SSO_USERINFO_UPDATE.getDesc(), json));
        }
    }

    /**
     * 处理用户在 sso 中 active 状态更新
     *
     * @param channel
     * @param message
     * @throws IOException
     */
    @RabbitListener(containerFactory = "userConsumerFactory", queues = {"${application.sso.user-active-queue}"})
    @RabbitHandler
    public void processUserActive(Channel channel, Message message) throws IOException {
        log.info("user active ,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        SsoUserActive userActive = JsonUtil.fromJson(json, SsoUserActive.class);
        if (null == userActive) {
            log.error("userActive is null,param:{}", json);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        String key = SSO_USER_ACTIVE_PREFIX.concat(userActive.getTransactionId());
        BigInteger busId = toBigInteger(userActive.getTransactionId());
        if (!checkExits(key, busId, MqTranRecordBusTypeEnums.SSO_USERACTIVE_UPDATE)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        LoginUtil.simulateLoginWithClient();
        try {
            userService.updateUserActiveBySso(userActive);
            saveFailedRecord(busId, 1, userActive, MqTranRecordBusTypeEnums.SSO_USERACTIVE_UPDATE);
            redisService.delete(key);
            log.info("update user active success,param:{}", userActive);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("update user active error: ", e);
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            saveFailedRecord(busId, 0, userActive, MqTranRecordBusTypeEnums.SSO_USERACTIVE_UPDATE);
            NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, String.format("user-service-> 业务类型：%s , 数据：%s, 消费数据失败，请人工处理。", MqTranRecordBusTypeEnums.SSO_USERACTIVE_UPDATE.getDesc(), json));
        }
    }

    @Resource
    private TalentService talentService;

    /**
     * 处理用户在 sso 中绑定客户端，只处理自己客户端对应的client_id 的
     *
     * @param channel
     * @param message
     * @throws IOException
     */
    @RabbitListener(containerFactory = "userConsumerFactory", queues = {"${application.sso.user-binging-client-queue}"})
    @RabbitHandler
    public void processUserBinding(Channel channel, Message message) throws IOException {
        log.info("user binging ,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        SsoUserBinding ssoUserBinding = JsonUtil.fromJson(json, SsoUserBinding.class);
        if (null == ssoUserBinding) {
            log.error("ssoUserBinding is null,param:{}", json);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        String clientId = oAuth2ResourceServerProperties.getOpaquetoken().getClientId();
        if (!clientId.equals(ssoUserBinding.getClientId())) {
            log.info("client id not match,param:{}", json);
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        String key = SSO_USER_BINDING_PREFIX.concat(ssoUserBinding.getTransactionId());
        BigInteger busId = toBigInteger(ssoUserBinding.getTransactionId());
        if (!checkExits(key, busId, MqTranRecordBusTypeEnums.SSO_USER_BINGING)) {
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
            return;
        }
        LoginUtil.simulateLoginWithClient();
        try {
            User user = userService.onSsoUserBinding(ssoUserBinding);
            if(user != null) {
                talentService.teamAddUserSetRollList(new TeamUserSetRollList(List.of(user.getId()), user.getTeamId(), user.getTeamId()));
            }
            saveFailedRecord(busId, 1, ssoUserBinding, MqTranRecordBusTypeEnums.SSO_USER_BINGING);
            redisService.delete(key);
            log.info("update user success");
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("update user error: ", e);
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
            saveFailedRecord(busId, 0, ssoUserBinding, MqTranRecordBusTypeEnums.SSO_USER_BINGING);
            NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, String.format("user-service-> 业务类型：%s , 数据：%s, 消费数据失败，请人工处理。", MqTranRecordBusTypeEnums.SSO_USER_BINGING.getDesc(), json));
        }
    }


    /**
     * 幂等校验
     */
    private boolean checkExits(String cacheKey, BigInteger busId, MqTranRecordBusTypeEnums busType) {
        //redis verify exists
        String value = redisService.get(cacheKey);
        if (StringUtils.isNotBlank(value)) {
            log.error("message key {} already exists in redis", cacheKey);
            return false;
        }

        //数据库是否记录
        CommonMqConsumeFailedRecord commonMqConsumeFailedRecord = userMqTransactionFailedRecordRepository.findByBusIdAndBusTypeAndReceiceStatus(busId, busType.toDbValue(), 1);
        if (null != commonMqConsumeFailedRecord) {
            log.error("message key {} already exists in database", cacheKey);
            return false;
        }
        redisService.set(cacheKey, "1", 18000);
        return true;
    }


    /**
     * 保存消费记录
     */
    private void saveFailedRecord(BigInteger busId, Integer status, Object message, MqTranRecordBusTypeEnums busType) {
        CommonMqConsumeFailedRecord failedRecord = new CommonMqConsumeFailedRecord();
        failedRecord.setConsumeCount(1);
        failedRecord.setBusId(busId);
        failedRecord.setReceiveMessage(JsonUtil.toJson(message));
        failedRecord.setBusType(busType.toDbValue());
        failedRecord.setReceiceStatus(status);
        userMqTransactionFailedRecordRepository.save(failedRecord);
    }

    private static BigInteger toBigInteger(String transactionId) {
        long transactionIdLong = Long.parseLong(transactionId);
        return BigInteger.valueOf(transactionIdLong);
    }

}
