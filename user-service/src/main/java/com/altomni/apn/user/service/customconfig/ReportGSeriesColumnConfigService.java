package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.service.dto.customconfig.ReportGSeriesColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.ReportGSeriesConfigVO;

public interface ReportGSeriesColumnConfigService {

    ReportGSeriesConfigVO searchMonthlyRevenueDetailColumnConfigByUserId(Long userId);

    void updateMonthlyRevenueDetailColumnConfigByUserId(Long userId, ReportGSeriesColumnConfigDTO dto);


    ReportGSeriesConfigVO searchQuarterlyOnboardingNewHiresColumnConfigByUserId(Long userId);

    void updateQuarterlyOnboardingNewHiresColumnConfigByUserId(Long userId, ReportGSeriesColumnConfigDTO dto);


    ReportGSeriesConfigVO searchQuarterlyOnboardingRenewalsColumnConfigByUserId(Long userId);

    void updateQuarterlyOnboardingRenewalsColumnConfigByUserId(Long userId, ReportGSeriesColumnConfigDTO dto);


    ReportGSeriesConfigVO searchQuarterlyOffboardingColumnConfigByUserId(Long userId);

    void updateQuarterlyOffboardingColumnConfigByUserId(Long userId, ReportGSeriesColumnConfigDTO dto);

}
