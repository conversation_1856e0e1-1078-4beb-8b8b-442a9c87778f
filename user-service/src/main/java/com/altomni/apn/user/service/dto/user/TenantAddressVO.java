package com.altomni.apn.user.service.dto.user;

import com.altomni.apn.user.domain.geoinfo.TenantGeoInfoEN;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class TenantAddressVO implements Serializable
{
    private static final long serialVersionUID = 1L;

    private Long id;

    private String address;

    private String address2;

    private String zipcode;

    private Long cityId;

    private TenantGeoInfoEN geoInfoEN;

}
