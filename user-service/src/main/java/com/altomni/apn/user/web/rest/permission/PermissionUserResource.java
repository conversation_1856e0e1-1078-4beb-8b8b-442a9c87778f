package com.altomni.apn.user.web.rest.permission;

import com.altomni.apn.common.config.PrivilegeName;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.repository.permission.PermissionRoleRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.permission.PermissionRoleDTO;
import com.altomni.apn.user.service.permission.PermissionExtraUserTeamService;
import com.altomni.apn.user.service.permission.PermissionRoleService;
import com.altomni.apn.user.service.permission.PermissionUserRoleService;
import com.altomni.apn.user.service.xxljob.XxlJobService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserListVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserRoleVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * REST controller for managing permissions.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/users")
public class PermissionUserResource {

    @Resource
    private UserRepository userRepository;

    @Resource
    private PermissionRoleRepository roleRepository;

    @Resource
    private PermissionUserRoleService userRoleService;

    @Resource
    private PermissionRoleService permissionRoleService;

    @Resource
    private PermissionExtraUserTeamService permissionExtraUserTeamService;

    @Resource
    private XxlJobService xxlJobService;

    @PrivilegeName(value = "Get Users")
    @GetMapping("")
    public ResponseEntity<List<User>> getUsers(){
        log.info("({}) REST request to get users", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(userRepository.findAllByActivatedIsTrueAndNonInternalRole());
    }


    @PrivilegeName(value = "Get All Users")
    @GetMapping("/all")
    public ResponseEntity<List<PermissionUserListVM>> getAllUsers(){
        log.info("({}) REST request to get all users", SecurityUtils.getCurrentUserLogin());
        List<PermissionUserListVM> permissionUserListVMS = userRepository.findAllByActivatedIsTrueAndNonInternalRole().stream()
                .map(user -> new PermissionUserListVM(user.getId(), user.getUsername() + " [" + user.getEmail() + "]"))
                .collect(Collectors.toList());
        return ResponseEntity.ok(permissionUserListVMS);
    }

    @PrivilegeName(value = "Get Role ids By User id")
    @GetMapping("/{userId}/role-ids")
    public ResponseEntity<List<Long>> getRoleIdsByUserId(@PathVariable("userId") Long userId){
        log.info("({}) REST request to get roles by user id: {}", SecurityUtils.getCurrentUserLogin(), userId);
        return ResponseEntity.ok(roleRepository.findRoleIdsByUserId(userId));
    }

    @GetMapping("/{userId}/data-permissions")
    public ResponseEntity<PermissionUserTeamPermissionVM> getDataPermissionsByUserId(@PathVariable("userId") Long userId){
        log.info("({}) REST request to get data-permissions by user id: {}", SecurityUtils.getUserId(), userId);
        return ResponseEntity.ok(permissionExtraUserTeamService.findDataPermissionByUserId(userId));
    }

    @GetMapping("/{userId}/all-data-permissions")
    public ResponseEntity<PermissionUserTeamPermissionVM.PermissionDetail> getAllClientContactDataPermissionsByUserId(@PathVariable("userId") Long userId){
        log.info("({}) REST request to get all-client-contact-data-permissions by user id: {}", SecurityUtils.getUserId(), userId);
        return ResponseEntity.ok(permissionExtraUserTeamService.findAllClientContactDataPermissionByUserIdAndTenantId(SecurityUtils.getTenantId(), userId));
    }

    @PostMapping("/{userId}/all-data-permissions")
    public ResponseEntity<PermissionUserTeamPermissionVM.PermissionDetail> getAllDataPermissionsByTenantIdAndUserId(@RequestBody Long tenantId, @PathVariable("userId") Long userId){
        log.info("({}) REST request to get all-client-contact-data-permissions by user id: {} , tenant id: {}", SecurityUtils.getUserId(), userId, tenantId);
        return ResponseEntity.ok(permissionExtraUserTeamService.findAllClientContactDataPermissionByUserIdAndTenantId(tenantId, userId));
    }

    @GetMapping("/self/all-data-permissions")
    public ResponseEntity<PermissionUserTeamPermissionVM.PermissionDetail> getSelfAllClientContactDataPermissionsByUserId(){
        log.info("({}) REST request to get self all-client-contact-data-permissions", SecurityUtils.getUserId());
        return ResponseEntity.ok(permissionExtraUserTeamService.findAllClientContactDataPermissionByUserIdAndTenantId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()));
    }

    @GetMapping("/{userId}/roles")
    public ResponseEntity<List<PermissionRoleDTO>> getMyRolesByUserId(@PathVariable("userId") Long userId) {
        log.debug("({}) REST request to get modules of getting roles by user id: {}", SecurityUtils.getUserId(), userId);
        return ResponseEntity.ok(permissionRoleService.findRolesByUserId(userId));
    }

    @PostMapping("/{userId}/data-permissions")
    public ResponseEntity<Void> setUserDataPermission(@PathVariable("userId") Long userId, @RequestBody PermissionUserTeamPermissionVM permissionUserTeamPermissionVM){
        log.info("({}) REST request to set user data permission", SecurityUtils.getCurrentUserLogin());
        permissionExtraUserTeamService.saveUserTeamPermission(userId, permissionUserTeamPermissionVM);
        return ResponseEntity.ok().build();
    }

    @PrivilegeName(value = "Set User Role")
    @PostMapping("/{userId}/roles")
    public ResponseEntity<Void> setUserRole(@PathVariable("userId") Long userId, @RequestBody PermissionUserRoleVM userRoleVM){
        log.info("({}) REST request to set user role", SecurityUtils.getCurrentUserLogin());
        userRoleVM.setUserId(userId);
        userRoleService.update(userRoleVM);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            xxlJobService.createOrUpdateUnSubmittedCandidatesForTeam(SecurityUtils.getTenantId());
        });
        return ResponseEntity.ok().build();
    }

}
