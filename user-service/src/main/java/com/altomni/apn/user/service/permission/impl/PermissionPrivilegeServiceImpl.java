package com.altomni.apn.user.service.permission.impl;

import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.permission.PermissionPrivilege;
import com.altomni.apn.user.repository.permission.PermissionPrivilegeRepository;
import com.altomni.apn.user.repository.permission.PermissionUserRoleRepository;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.altomni.apn.user.service.dto.permission.PermissionPrivilegeDTO;
import com.altomni.apn.user.service.dto.permission.PermissionPrivilegeUpdateDTO;
import com.altomni.apn.user.service.mapper.permission.PrivilegeMapper;
import com.altomni.apn.user.service.permission.PermissionPrivilegeService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Set;

@Service
@Transactional
public class PermissionPrivilegeServiceImpl implements PermissionPrivilegeService {

    @Resource
    private PermissionPrivilegeRepository privilegeRepository;

    @Resource
    private PermissionUserRoleRepository userRoleRepository;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

    @Resource
    private PrivilegeMapper permissionPrivilegeEntityMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public PermissionPrivilegeDTO create(PermissionPrivilegeDTO privilegeDTO) {
        if (Objects.nonNull(privilegeDTO.getId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONPRIVILEGE_CREATE_INVALIDID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        PermissionPrivilege parent = privilegeRepository.getById(privilegeDTO.getParentId());
        privilegeDTO.setLevel(parent.getLevel()+1);
        if (parent.getIsLeaf()){
            parent.setIsLeaf(false);
            privilegeRepository.save(parent);
        }
        privilegeDTO.setIsLeaf(Boolean.TRUE);
        if (ObjectUtils.isNotEmpty(privilegeDTO.getApiList())){
            privilegeDTO.setApi(privilegeDTO.getApiList().toJSONString());
        }
        PermissionPrivilege savedPrivilege = privilegeRepository.saveAndFlush(permissionPrivilegeEntityMapper.toEntity(privilegeDTO));
        if (privilegeDTO.getIsPublic()){
            cachePermissionWriteOnly.deletePublicApis();
        }
        return permissionPrivilegeEntityMapper.toDto(savedPrivilege).setApiList(privilegeDTO.getApiList());
    }

    @Override
    public PermissionPrivilegeDTO update(PermissionPrivilegeUpdateDTO permissionPrivilegeDTO) {
        if (Objects.isNull(permissionPrivilegeDTO.getId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONPRIVILEGE_CREATE_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        PermissionPrivilege permissionPrivilege = privilegeRepository.findById(permissionPrivilegeDTO.getId()).orElseThrow();
        BeanUtils.copyProperties(permissionPrivilegeDTO, permissionPrivilege);
        if (ObjectUtils.isNotEmpty(permissionPrivilegeDTO.getApiList())){
            permissionPrivilege.setApi(permissionPrivilegeDTO.getApiList().toJSONString());
        }

        PermissionPrivilege updatedPrivilege = privilegeRepository.saveAndFlush(permissionPrivilege);
        List<Long> userIdsByPrivilegeId = userRoleRepository.findUserIdsByPrivilegeId(updatedPrivilege.getId());
        cachePermissionWriteOnly.deletePrivilegeByUserIds(userIdsByPrivilegeId);
        cachePermissionWriteOnly.deletePrivilegePermissionSetByUserIds(userIdsByPrivilegeId);
        cachePermissionWriteOnly.deletePublicApis();
        return permissionPrivilegeEntityMapper.toDto(updatedPrivilege).setApiList(permissionPrivilegeDTO.getApiList());
    }

    @Override
    public PermissionPrivilegeDTO get(Long id) {
        PermissionPrivilege privilege = privilegeRepository.getById(id);
        return permissionPrivilegeEntityMapper.toDto(privilege).setApiList(JSONArray.parseArray(privilege.getApi()));
    }

    @Override
    public void delete(Long id) {
        privilegeRepository.deleteById(id);
    }

    @Override
    public Set<String> findPrivilegeApisByRoleId(Long roleId) {
        return privilegeRepository.findPrivilegeApisByRoleId(roleId);
    }

    @Override
    public Set<String> findPrivilegeApisByAdminUserId(Long userId) {
        return privilegeRepository.findPrivilegeApisByAdminUserId(userId);
    }

    @Override
    public Set<String> findPrivilegeApisByUserId(Long userId) {
        return privilegeRepository.findPrivilegeApisByUserId(userId);
    }

    @Override
    public Set<String> findPrivilegeNamesByUserId(Long userId) {
        return privilegeRepository.findPrivilegeNamesByUserId(userId);
    }

    @Override
    public Set<String> findPublicPrivilegeApis() {
        return privilegeRepository.findSkipCheckedPrivilegeApis();
    }

}
