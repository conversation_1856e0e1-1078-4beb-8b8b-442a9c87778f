package com.altomni.apn.user.service.dto.customconfig;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

//@JsonDeserialize(using = CustomFieldDeserializer.class)

@Data
public class CustomFormField implements Serializable {
    private String field;
    private List<String> additionalInfoKeys;
    private boolean customizable;

    @ApiModelProperty(value = "control field visible on form or not")
    private boolean visible;
    private boolean readOnly;

    @ApiModelProperty(value = "control the require or not for a field in form ")
    private boolean required;

    @ApiModelProperty(value = "custom sub filed")
    private List<CustomFormSubField> subFields;

    private String cnDisplay;
    private String enDisplay;
    public CustomFormField(){

    }

    // Getters and Setters

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public boolean isReadOnly() {
        return readOnly;
    }

    public void setReadOnly(boolean readOnly) {
        this.readOnly = readOnly;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public boolean isCustomizable() {
        return customizable;
    }

    public void setCustomizable(boolean customizable) {
        this.customizable = customizable;
    }

    public List<CustomFormSubField> getSubFields() {
        return subFields;
    }

    public void setSubFields(List<CustomFormSubField> subFields) {
        this.subFields = subFields;
    }

    public String getCnDisplay() {
        return cnDisplay;
    }

    public void setCnDisplay(String cnDisplay) {
        this.cnDisplay = cnDisplay;
    }

    public String getEnDisplay() {
        return enDisplay;
    }

    public void setEnDisplay(String enDisplay) {
        this.enDisplay = enDisplay;
    }


    public List<String> getAdditionalInfoKeys() {
        return additionalInfoKeys;
    }

    public void setAdditionalInfoKeys(List<String> additionalInfoKeys) {
        this.additionalInfoKeys = additionalInfoKeys;
    }
}
