package com.altomni.apn.user.service.permission.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.domain.user.UserRole;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.dto.user.TeamInfoVO;
import com.altomni.apn.common.enumeration.reportSubscriptions.DataPeriod;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushMethod;
import com.altomni.apn.common.enumeration.reportSubscriptions.PushTimeType;
import com.altomni.apn.common.enumeration.reportSubscriptions.ReportType;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.subscription.DeleteSubscriptionBatchDTO;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.domain.permission.PermissionPrivilege;
import com.altomni.apn.user.domain.permission.PermissionRolePrivilege;
import com.altomni.apn.user.repository.permission.*;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.altomni.apn.user.service.dto.subscription.ReportSubscriptionDTO;
import com.altomni.apn.user.service.permission.PermissionRolePrivilegeService;
import com.altomni.apn.user.service.subscription.SubscriptionService;
import com.altomni.apn.user.service.user.UserService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionRolePrivilegeVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Transactional
@Slf4j
public class PermissionRolePrivilegeServiceImpl implements PermissionRolePrivilegeService {

    @Resource
    private PermissionRolePrivilegeRepository rolePrivilegeRepository;

    @Resource
    private PermissionPrivilegeRepository privilegeRepository;

    @Resource
    private PermissionUserRoleRepository permissionUserRoleRepository;

    @Resource
    private PermissionRoleRepository permissionRoleRepository;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

    @Resource
    private SubscriptionService subscriptionService;
    
    @Resource
    private UserService userService;

    public Long BY_USER_VIEW_PRIVILEGE = 1046L;
    public Long BY_COMPANY_VIEW_PRIVILEGE = 1049L;
    public Long USER_ADOPTION_VIEW_PRIVILEGE = 1157L;


    @Autowired
    private InitiationService initiationService;

    @Autowired
    private PermissionTeamRepository permissionTeamRepository;


    @Override
    public PermissionRolePrivilege create(PermissionRolePrivilege rolePrivilege) {
        return rolePrivilegeRepository.save(rolePrivilege);
    }

    @Override
    public Set<String> update(PermissionRolePrivilegeVM permissionRolePrivilegeVM) {
        List<PermissionPrivilege> oldPrivilege = privilegeRepository.findAllByRoleId(permissionRolePrivilegeVM.getRoleId());
        Set<Long> oldPrivilegeIds = oldPrivilege.stream().map(PermissionPrivilege::getId).collect(Collectors.toSet());
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            dealWithE1E2E5ViewPermissionChanged(permissionRolePrivilegeVM,oldPrivilegeIds);
        });

        List<PermissionRolePrivilege> rolePrivileges = new ArrayList<>();
        for (Long privilegeId: permissionRolePrivilegeVM.getPrivilegeIds()){
            rolePrivileges.add(new PermissionRolePrivilege(null, privilegeId, permissionRolePrivilegeVM.getRoleId(), SecurityUtils.getTenantId()));
        }
        rolePrivilegeRepository.deleteAllByRoleId(permissionRolePrivilegeVM.getRoleId());
        Set<Long> privilegeIds = rolePrivilegeRepository.saveAll(rolePrivileges).stream().map(rolePrivilege -> rolePrivilege.getPrivilegeId()).collect(Collectors.toSet());
        this.removePrivilegeCacheByRoleId(permissionRolePrivilegeVM.getRoleId());
        return privilegeRepository.findApisByIdIn(privilegeIds);
    }

    //判断E1,E2,E5的VIEW权限有没有变动
    @Override
    public void dealWithE1E2E5ViewPermissionChanged(PermissionRolePrivilegeVM permissionRolePrivilegeVM,Set<Long> oldPrivilegeIds){
        //判断角色是否激活，非激活的角色编辑不考虑
        Optional<Role> role = permissionRoleRepository.findById(permissionRolePrivilegeVM.getRoleId());
        if (!role.isPresent() || !role.get().getStatus()){
            return;
        }
        Set<Long> newPrivilegeIds = permissionRolePrivilegeVM.getPrivilegeIds();
        //前后判断权限是否变化
        try{
            addOrDeleteSubscription(permissionRolePrivilegeVM.getRoleId(), oldPrivilegeIds, newPrivilegeIds,ReportType.BY_USER,BY_USER_VIEW_PRIVILEGE);
        }catch (Exception e){
            log.error("Error while dealWithE1E2E5ViewPermissionChanged BY_USER:{}", e);
        }
        try {
            addOrDeleteSubscription(permissionRolePrivilegeVM.getRoleId(), oldPrivilegeIds, newPrivilegeIds,ReportType.BY_COMPANY,BY_COMPANY_VIEW_PRIVILEGE);
        }catch (Exception e){
            log.error("Error while dealWithE1E2E5ViewPermissionChanged BY_COMPANY:{}", e);
        }
        try {
            addOrDeleteSubscription(permissionRolePrivilegeVM.getRoleId(), oldPrivilegeIds, newPrivilegeIds,ReportType.USER_ADOPTION,USER_ADOPTION_VIEW_PRIVILEGE);
        }catch (Exception e){
            log.error("Error while dealWithE1E2E5ViewPermissionChanged USER_ADOPTION:{}", e);
        }
    }

    private void addOrDeleteSubscription(Long roleId, Set<Long> oldPrivilegeIds, Set<Long> newPrivilegeIds,ReportType reportType,Long privilegeId) {
        List<Long> userIds = Optional.ofNullable(permissionUserRoleRepository.findAllByRoleId(roleId))
                .orElse(Collections.emptyList())
                .stream()
                .map(UserRole::getUserId)
                .toList();
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        //新增，则为该role低下的所有角色新增一条默认订阅（新增考虑幂等）
        if (newPrivilegeIds.contains(privilegeId) && !oldPrivilegeIds.contains(privilegeId)){
            List<TeamInfoVO> teamInfoList = userService.getTeamInfoList(userIds);
            List<ReportSubscriptionDTO> reportSubscriptionDTOList = getReportSubscriptionDTOS(teamInfoList,reportType);
            subscriptionService.createReportSubscription(reportSubscriptionDTOList);
        }
        //删除，则为该role底下的所有角色删除（删除考虑，该用户去掉该权限后,是否通过其它角色或本身含有page-veiw权限）
        if(!newPrivilegeIds.contains(privilegeId) && oldPrivilegeIds.contains(privilegeId)){
            List<Long> realUserIds = userIds.stream().filter(userId -> !countPrivilegeByUserId(userId, privilegeId, roleId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(realUserIds)){
                return;
            }
            //对realUserIds删除其订阅
            DeleteSubscriptionBatchDTO deleteSubscriptionBatchDTO = new DeleteSubscriptionBatchDTO(realUserIds, reportType, SecurityUtils.getTenantId());
            subscriptionService.deleteReportSubscriptionBatch(deleteSubscriptionBatchDTO);
        }
    }

    private void addOrDeleteSubscriptionV2(Long userId,Set<Long> oldPrivilegeIds, Set<Long> newPrivilegeIds,ReportType reportType,Long privilegeId) {
        List<Long> userIds = List.of(userId);
        //新增，则为该role低下的所有角色新增一条默认订阅（新增考虑幂等）
        if (newPrivilegeIds.contains(privilegeId) && !oldPrivilegeIds.contains(privilegeId)){
            List<TeamInfoVO> teamInfoList = userService.getTeamInfoList(userIds);
            List<ReportSubscriptionDTO> reportSubscriptionDTOList = getReportSubscriptionDTOS(teamInfoList,reportType);
            subscriptionService.createReportSubscription(reportSubscriptionDTOList);
        }
        //删除，则为该role底下的所有角色删除（删除考虑，该用户去掉该权限后,是否通过其它角色或本身含有page-veiw权限）
        if(!newPrivilegeIds.contains(privilegeId) && oldPrivilegeIds.contains(privilegeId)){
            //对realUserIds删除其订阅
            DeleteSubscriptionBatchDTO deleteSubscriptionBatchDTO = new DeleteSubscriptionBatchDTO(userIds, reportType, SecurityUtils.getTenantId());
            subscriptionService.deleteReportSubscriptionBatch(deleteSubscriptionBatchDTO);
        }
    }

    //根据用户id，查找所有角色，排序当前编辑操作的角色，看其它的角色是否含有BY_USER_VIEW_PRIVILEGE，如果有，则返回true，有则返回false
    private boolean countPrivilegeByUserId(Long userId,Long privilegeId,Long roleId){
        Integer num = permissionUserRoleRepository.countPrivilegeByUserId(userId, privilegeId, roleId);
        if (num > 0){
            return true;
        }
        return false;
    }

    private List<ReportSubscriptionDTO> getReportSubscriptionDTOS(List<TeamInfoVO> teamInfoList,ReportType reportType) {
        List<ReportSubscriptionDTO> reportSubscriptionDTOList = new ArrayList<>();
        for (TeamInfoVO teamInfoVO : teamInfoList){
            ReportSubscriptionDTO vo = new ReportSubscriptionDTO();
            vo.setRecipientList(List.of(teamInfoVO.getUserId()));
            vo.setPushMethodList(List.of(PushMethod.LARK));
            vo.setTenantId(SecurityUtils.getTenantId());
            vo.setName(generateReportName(reportType));
            vo.setPushTimeType(PushTimeType.EVERY_WEEK);
            vo.setDayOfWeek(1);
            vo.setSendTime("09:00:00");
            vo.setDataPeriod(DataPeriod.LAST_WEEK);
            Map<String, List<Long>> dataPermission = getDataPermission(teamInfoVO.getUserId());
            if (dataPermission == null){
                continue;
            }
            vo.setUserIdList(dataPermission.get("userIdList"));
            vo.setTeamIdList(dataPermission.get("teamIdList"));
            vo.setStartDate(LocalDate.now());
            vo.setReportType(reportType);
            vo.setPermissionUserId(teamInfoVO.getUserId());
            vo.setPermissionTeamId(teamInfoVO.getTeamId());
            vo.setAutoGenerate(true);
            reportSubscriptionDTOList.add(vo);
        }
        return reportSubscriptionDTOList;
    }

    private String generateReportName(ReportType reportType) {
        switch (reportType){
            case BY_USER -> {
                return "Recruiting KPI Summary(by User)";
            }
            case BY_COMPANY -> {
                return "Recruiting KPI Summary(by Client)";
            }
            case USER_ADOPTION -> {
                return "Platform Adoption Summary";
            }
        }
        return "";
    }

    @Override
    public Map<String, List<Long>> getDataPermission(Long userId) {
        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateReportDataPermissionByUserId(SecurityUtils.getTenantId(), userId).getBody();
        List<Long> userIdList = new ArrayList<>();
        List<Long> teamIdList = new ArrayList<>();
        Map<String, List<Long>> datePermissionMap = new HashMap<>();
        if (teamDataPermission.getSelf()){
            List<Long> fitleredUserIds = permissionTeamRepository.getUserIdFilterByTeamCategory(List.of(userId));
            if (CollUtil.isNotEmpty(fitleredUserIds)){
                userIdList.add(userId);
            }else{
                return null;
            }
        } else if (CollectionUtils.isNotEmpty(teamDataPermission.getNestedTeamIds())){
            log.debug("PermissionRoleTeamIds:{}",teamDataPermission.getNestedTeamIds());
            Set<Long> filteredTeamIds = userService.filterTeamByTeamCategoryAndPermissionAndChildrenData(teamDataPermission.getNestedTeamIds());
            log.debug("filteredTeamIds:{}",filteredTeamIds);
            if (CollUtil.isNotEmpty(filteredTeamIds)){
                teamIdList.addAll(filteredTeamIds);
            }else{
                return null;
            }
        } else if (teamDataPermission.getAll()){
        }
        datePermissionMap.put("userIdList", userIdList);
        datePermissionMap.put("teamIdList", teamIdList);
        return datePermissionMap;
    }

    @Override
    public void dealWithE1E2E5RoleStatusChanged(Long roleId,Boolean isActive){
        List<PermissionPrivilege> privilege = privilegeRepository.findAllByRoleId(roleId);
        Set<Long> privilegeIds = privilege.stream().map(PermissionPrivilege::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(privilegeIds)){
            return;
        }
        if (isActive){
            //前后判断权限是否变化
            try{
                addOrDeleteSubscription(roleId, Collections.emptySet(), privilegeIds,ReportType.BY_USER,BY_USER_VIEW_PRIVILEGE);
            }catch (Exception e){
                log.error("Error while dealWithE1E2E5RoleStatusChanged BY_USER:{}", e);
            }
            try {
                addOrDeleteSubscription(roleId, Collections.emptySet(), privilegeIds,ReportType.BY_COMPANY,BY_COMPANY_VIEW_PRIVILEGE);
            }catch (Exception e){
                log.error("Error while dealWithE1E2E5RoleStatusChanged BY_COMPANY:{}", e);
            }
            try {
                addOrDeleteSubscription(roleId, Collections.emptySet(), privilegeIds,ReportType.USER_ADOPTION,USER_ADOPTION_VIEW_PRIVILEGE);
            }catch (Exception e){
                log.error("Error while dealWithE1E2E5RoleStatusChanged USER_ADOPTION:{}", e);
            }
        }else {
            try{
                addOrDeleteSubscription(roleId, privilegeIds, Collections.emptySet(),ReportType.BY_USER,BY_USER_VIEW_PRIVILEGE);
            }catch (Exception e){
                log.error("Error while dealWithE1E2E5RoleStatusChanged BY_USER:{}", e);
            }
            try {
                addOrDeleteSubscription(roleId, privilegeIds, Collections.emptySet(), ReportType.BY_COMPANY, BY_COMPANY_VIEW_PRIVILEGE);
            }catch (Exception e){
                log.error("Error while dealWithE1E2E5RoleStatusChanged BY_COMPANY:{}", e);
            }
            try {
                addOrDeleteSubscription(roleId, privilegeIds, Collections.emptySet(),ReportType.USER_ADOPTION,USER_ADOPTION_VIEW_PRIVILEGE);
            }catch (Exception e){
                log.error("Error while dealWithE1E2E5RoleStatusChanged USER_ADOPTION:{}", e);
            }
        }
    }

    @Override
    public void dealWithE1E2E5UserRolesChanged(Set<Long> roleIds, Long userId,Set<Long> oldPrivilegeIds){
        Set<Long> newPrivilegeIds = privilegeRepository.getPermissionPrivilegeByRoles(roleIds);
        try{
            addOrDeleteSubscriptionV2(userId,oldPrivilegeIds, newPrivilegeIds,ReportType.BY_USER,BY_USER_VIEW_PRIVILEGE);
        }catch (Exception e){
            log.error("Error while dealWithE1E2E5UserRolesChanged BY_USER:{}", e);
        }
        try {
            addOrDeleteSubscriptionV2(userId,oldPrivilegeIds, newPrivilegeIds,ReportType.BY_COMPANY,BY_COMPANY_VIEW_PRIVILEGE);
        }catch (Exception e){
            log.error("Error while dealWithE1E2E5UserRolesChanged BY_COMPANY:{}", e);
        }
        try {
            addOrDeleteSubscriptionV2(userId,oldPrivilegeIds, newPrivilegeIds,ReportType.USER_ADOPTION,USER_ADOPTION_VIEW_PRIVILEGE);
        }catch (Exception e){
            log.error("Error while dealWithE1E2E5UserRolesChanged USER_ADOPTION:{}", e);
        }

    }

    private void addOrDeleteSubscriptionForRolesChanged(Long roleId, Set<Long> oldPrivilegeIds, Set<Long> newPrivilegeIds,ReportType reportType,Long privilegeId) {
        List<Long> userIds = Optional.ofNullable(permissionUserRoleRepository.findAllByRoleId(roleId))
                .orElse(Collections.emptyList())
                .stream()
                .map(UserRole::getUserId)
                .toList();
        if (CollectionUtils.isEmpty(userIds)){
            return;
        }
        //新增，则为该role低下的所有角色新增一条默认订阅（新增考虑幂等）
        if (newPrivilegeIds.contains(privilegeId) && !oldPrivilegeIds.contains(privilegeId)){
            List<TeamInfoVO> teamInfoList = userService.getTeamInfoList(userIds);
            List<ReportSubscriptionDTO> reportSubscriptionDTOList = getReportSubscriptionDTOS(teamInfoList,ReportType.BY_USER);
            subscriptionService.createReportSubscription(reportSubscriptionDTOList);
        }
        //删除，则为该role底下的所有角色删除（删除考虑，该用户去掉该权限后,是否通过其它角色或本身含有page-veiw权限）
        if(!newPrivilegeIds.contains(privilegeId) && oldPrivilegeIds.contains(privilegeId)){
            List<Long> realUserIds = userIds.stream().filter(userId -> !countPrivilegeByUserId(userId, privilegeId, roleId)).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(realUserIds)){
                return;
            }
            //对realUserIds删除其订阅
            DeleteSubscriptionBatchDTO deleteSubscriptionBatchDTO = new DeleteSubscriptionBatchDTO(realUserIds, reportType, SecurityUtils.getTenantId());
            subscriptionService.deleteReportSubscriptionBatch(deleteSubscriptionBatchDTO);
        }
    }


    @Override
    public PermissionRolePrivilege update(PermissionRolePrivilege rolePrivilege) {
        this.removePrivilegeCacheByRoleId(rolePrivilege.getRoleId());
        return rolePrivilegeRepository.save(rolePrivilege);
    }

    @Override
    public PermissionRolePrivilege get(Long id) {
        return rolePrivilegeRepository.getById(id);
    }

    @Override
    public void delete(Long id) {
        rolePrivilegeRepository.deleteById(id);
    }

    private void removePrivilegeCacheByRoleId(Long roleId){
        List<Long> userIds = permissionUserRoleRepository.findAllByRoleId(roleId).stream().map(UserRole::getUserId).toList();
        cachePermissionWriteOnly.deletePrivilegePermissionSetByUserIds(userIds);
        cachePermissionWriteOnly.deletePrivilegeByUserIds(userIds);
        cachePermissionWriteOnly.deletePrivilegePermissionTreeByUserIds(userIds);
    }
}
