package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.service.dto.customconfig.CompanyFteBdReportColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.CompanyFteBdReportColumnConfigVO;

public interface CompanyFteBdReportColumnConfigService {

    CompanyFteBdReportColumnConfigVO searchConfigByUserId(Long userId);

    void updateConfigByUserId(Long userId, CompanyFteBdReportColumnConfigDTO dto);

}
