package com.altomni.apn.user.repository.mqfailedrecord;

import com.altomni.apn.common.domain.transactionrecord.CommonMqConsumeFailedRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.math.BigInteger;

@Repository
public interface UserMqTransactionFailedRecordRepository extends JpaRepository<CommonMqConsumeFailedRecord, Long> {

    CommonMqConsumeFailedRecord findByBusIdAndBusTypeAndReceiceStatus(BigInteger busId, Integer busType,Integer receiceStatus);
}