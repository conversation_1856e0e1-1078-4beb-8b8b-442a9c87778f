package com.altomni.apn.user.service.dto.user;

import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.talent.SuspectedDuplications;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * A CreditTransactionDTO.
 */
@Data
public class CreditTransactionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String profileId;

    private Integer credit;

    private Long tenantId;

    private Long userId;

    private Status status;

    private List<String> existsContacts;

    private String esContacts;

    private Long talentId;

    private List<SuspectedDuplications> suspectedDuplicationsList;

    //推荐候选人/工作时传入
    private RecommendFeedback recommendFeedback;

}
