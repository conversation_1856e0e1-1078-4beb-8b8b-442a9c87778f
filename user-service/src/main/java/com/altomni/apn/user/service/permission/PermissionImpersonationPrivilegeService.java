package com.altomni.apn.user.service.permission;

import com.altomni.apn.user.domain.permission.PermissionImpersonationPrivilege;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface PermissionImpersonationPrivilegeService {

    List<PermissionImpersonationPrivilege> findAll();

    List<PermissionImpersonationPrivilege> saveAll(Collection<PermissionImpersonationPrivilege> impersonationPrivileges);
}