package com.altomni.apn.user.service.permission;

import com.altomni.apn.user.domain.permission.PermissionTenantModule;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTenantInvolvedDataPermissionModuleVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTenantModuleVM;

import java.util.List;

public interface PermissionTenantModuleService {

    PermissionTenantModule create(PermissionTenantModule permissionTenantModule);

    PermissionTenantModule update(PermissionTenantModule permissionTenantModule);

    void updateAll(PermissionTenantModuleVM permissionTenantModuleVM);

    void delete(Long id);

    PermissionTenantModuleVM findByTenantId(Long tenantId);

    List<PermissionTenantInvolvedDataPermissionModuleVM> findModulesByCurrentTenant();
}
