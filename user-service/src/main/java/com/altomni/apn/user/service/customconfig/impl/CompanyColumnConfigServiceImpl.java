package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.JsonUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.domain.enumeration.CustomerType;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.customconfig.CompanyColumnConfigService;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.dto.customconfig.CompanyColumnConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.UserCustomConfig;
import com.altomni.apn.user.service.mapper.customconfig.JsonMapperUtil;
import com.altomni.apn.user.service.mapper.customconfig.SystemConfigDefaultMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class CompanyColumnConfigServiceImpl implements CompanyColumnConfigService {

    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    private SystemConfigDefaultService systemConfigDefaultService;

    @Resource
    private ApplicationIPGProperties applicationIPGProperties;

    @Resource
    private SystemConfigDefaultMapper systemConfigDefaultMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public CompanyColumnConfigDTO getColumnConfigByUserId(CustomerType type, Long userId) {
        if(userId == null){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        UserPreference userPreference = userPreferenceRepository.findByUserId(SecurityUtils.getUserId());
        return toDto(userPreference, type);
    }

    @Override
    public CompanyColumnConfigDTO saveCompanyColumnConfig(CustomerType type, CompanyColumnConfigDTO columnConfigDTO) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(SecurityUtils.getUserId());

        if (userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(SecurityUtils.getUserId());
        }
        switch (type) {
            case COMPANY_CLIENT ->
                    userPreference.setCompanyClientColumnConfig(JsonUtil.toJson(columnConfigDTO.getCustomConfig()));
            case COMPANY_PROSPECT ->
                    userPreference.setCompanyProspectColumnConfig(JsonUtil.toJson(columnConfigDTO.getCustomConfig()));
            case COMPANY_AFFILIATES ->
                    userPreference.setCompanyAffiliatesColumnConfig(JsonUtil.toJson(columnConfigDTO.getCustomConfig()));
        }

        return toDto(userPreferenceRepository.save(userPreference), type);
    }

    private CompanyColumnConfigDTO toDto(UserPreference userPreference, CustomerType type) {
        CompanyColumnConfigDTO columnConfigDTO = new CompanyColumnConfigDTO();
        if (userPreference != null) {
            switch (type) {
                case COMPANY_CLIENT ->
                        columnConfigDTO.setCustomConfig(userPreference.getCompanyClientColumnConfig() != null ? JSONUtil.toBean(userPreference.getCompanyClientColumnConfig(), UserCustomConfig.class) : null);
                case COMPANY_PROSPECT ->
                        columnConfigDTO.setCustomConfig(userPreference.getCompanyProspectColumnConfig() != null ? JSONUtil.toBean(userPreference.getCompanyProspectColumnConfig(), UserCustomConfig.class) : null);
                case COMPANY_AFFILIATES ->
                        columnConfigDTO.setCustomConfig(userPreference.getCompanyAffiliatesColumnConfig() != null ? JSONUtil.toBean(userPreference.getCompanyAffiliatesColumnConfig(), UserCustomConfig.class) : null);
            }
        }

        if (columnConfigDTO.getCustomConfig() == null) {
            SystemConfigDefault systemConfigDefault = null;
            if (applicationIPGProperties.getIpgRuleTenantIds().contains(SecurityUtils.getTenantId())) {
                systemConfigDefault = switch (type) {
                    case COMPANY_CLIENT ->
                            systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(Category.COMPANY_COLUMN, ConfigSubcategory.COMPANY_COLUMN_CLIENT_CUSTOMIZED);
                    case COMPANY_PROSPECT ->
                            systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(Category.COMPANY_COLUMN, ConfigSubcategory.COMPANY_COLUMN_PROSPECT_CUSTOMIZED);
                    case COMPANY_AFFILIATES ->
                            systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(Category.COMPANY_COLUMN, ConfigSubcategory.COMPANY_AFFILIATES_COLUMN_CUSTOMIZED);
                };
            } else {
                systemConfigDefault = switch (type) {
                    case COMPANY_CLIENT ->
                            systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(Category.COMPANY_COLUMN, ConfigSubcategory.COMPANY_COLUMN_CLIENT_DEFAULT);
                    case COMPANY_PROSPECT ->
                            systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(Category.COMPANY_COLUMN, ConfigSubcategory.COMPANY_COLUMN_PROSPECT_DEFAULT);
                    case COMPANY_AFFILIATES ->
                            systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(Category.COMPANY_COLUMN, ConfigSubcategory.COMPANY_AFFILIATES_COLUMN_CUSTOMIZED);
                };
            }
            columnConfigDTO.setCustomConfig(systemConfigDefault != null ? new UserCustomConfig(JsonMapperUtil.jsonNodeToCustomColumnFieldList(systemConfigDefaultMapper.toDto(systemConfigDefault).getCustomConfig())) : null);
        }

        return columnConfigDTO;
    }
}

