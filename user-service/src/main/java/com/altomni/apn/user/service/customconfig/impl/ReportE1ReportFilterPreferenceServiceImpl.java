package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.core.collection.CollUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.ReportSearchFilterPreference;
import com.altomni.apn.user.domain.customconfig.ReportSearchFilterShareUser;
import com.altomni.apn.user.domain.enumeration.ReportType;
import com.altomni.apn.user.repository.customconfig.ReportSearchFilterPreferenceRepository;
import com.altomni.apn.user.repository.customconfig.ReportSearchFilterShareUserRepository;
import com.altomni.apn.user.service.customconfig.ReportE1ReportFilterPreferenceService;
import com.altomni.apn.user.service.dto.customconfig.ReportE1ReportFilterPreferenceDTO;
import com.altomni.apn.user.service.user.UserService;
import com.altomni.apn.user.service.vo.customconfig.ReportE1ReportFilterPreferenceVO;
import com.altomni.apn.user.service.vo.customconfig.ReportE1ReportFilterShareUserVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Array;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ReportE1ReportFilterPreferenceServiceImpl implements ReportE1ReportFilterPreferenceService {

    @Resource
    private ReportSearchFilterPreferenceRepository reportSearchFilterPreferenceRepository;

    @Resource
    ReportSearchFilterShareUserRepository reportSearchFilterShareUserRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Resource
    UserService userService;

    @Override
    public List<ReportE1ReportFilterPreferenceVO> searchE1ReportFilterPreferenceByUserId(Long userId) {
        List<ReportE1ReportFilterPreferenceVO> result = reportSearchFilterPreferenceRepository
                .findByUserIdAndTenantIdAndReportType(userId, SecurityUtils.getTenantId(), ReportType.E1.toDbValue())
                .stream()
                .map(this::convertToVO)
                .sorted(Comparator.comparing(ReportE1ReportFilterPreferenceVO::getCreateDate).reversed())
                .collect(Collectors.toCollection(ArrayList::new));
        List<UserBriefDTO> userAll = userService.getAllBriefUsers(SecurityUtils.getTenantId());
        Map<Long, String> userMap = userAll.stream().collect(Collectors.toMap(x -> x.getId(), x -> x.getFirstName() + " " + x.getLastName()));
        List<Long> idList = result.stream().map(ReportE1ReportFilterPreferenceVO::getId).collect(Collectors.toList());
        List<ReportSearchFilterShareUser> shareUsers = reportSearchFilterShareUserRepository.findByFilterIdInAndShareType(idList, 1);
        Map<Long, List<ReportSearchFilterShareUser>> shareUsrMap = shareUsers.stream().collect(Collectors.groupingBy(x -> x.getFilterId()));
        result.forEach(x -> {
            if (shareUsrMap.containsKey(x.getId())) {
                List<ReportE1ReportFilterShareUserVO> shareUserVOS = new ArrayList();
                List<ReportSearchFilterShareUser> shareUserList = shareUsrMap.get(x.getId());
                shareUserList.forEach(z -> {
                    ReportE1ReportFilterShareUserVO bean = new ReportE1ReportFilterShareUserVO();
                    bean.setUserId(z.getUserId());
                    if (userMap.containsKey(z.getUserId())) {
                        bean.setUserName(userMap.get(z.getUserId()));
                    }
                    shareUserVOS.add(bean);
                });
                x.setShareUserList(shareUserVOS);
            }
        });
        return result;
    }

    private ReportE1ReportFilterPreferenceVO convertToVO(ReportSearchFilterPreference entity) {
        ReportE1ReportFilterPreferenceVO vo = new ReportE1ReportFilterPreferenceVO();
        vo.setId(entity.getId());
        vo.setTitleName(entity.getTitleName());
        vo.setSearchFilter(entity.getReportSearchFilter());
        vo.setCreateDate(entity.getCreatedDate());
        vo.setTenantId(entity.getTenantId());
        vo.setUserId(entity.getUserId());
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateE1ReportFilterPreferenceByUserId(Long userId, ReportE1ReportFilterPreferenceDTO dto) {
        Optional<ReportSearchFilterPreference> searchFilterPreferenceOptional = reportSearchFilterPreferenceRepository.findById(dto.getId());
        if (searchFilterPreferenceOptional.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETSYSTEMCONFIGBYCATEGORY_NOTFOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList("id info"), userApiPromptProperties.getUserService()));
        }
        ReportSearchFilterPreference oldPreference = searchFilterPreferenceOptional.get();
        oldPreference.setTitleName(dto.getTitleName());
        oldPreference.setReportSearchFilter(dto.getSearchFilter());
        oldPreference.setReportType(ReportType.E1.toDbValue());
        reportSearchFilterPreferenceRepository.save(oldPreference);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addE1ReportFilterPreferenceByUserId(Long userId, ReportE1ReportFilterPreferenceDTO dto) {

        ReportSearchFilterPreference preference = new ReportSearchFilterPreference();
        preference.setUserId(userId);
        preference.setTenantId(SecurityUtils.getTenantId());
        preference.setTitleName(dto.getTitleName());
        preference.setReportSearchFilter(dto.getSearchFilter());
        preference.setReportType(ReportType.E1.toDbValue());
        ReportSearchFilterPreference instant = reportSearchFilterPreferenceRepository.save(preference);

        List<ReportSearchFilterShareUser> shareUsers = new ArrayList<>();
        ReportSearchFilterShareUser shareUser = new ReportSearchFilterShareUser();
        shareUser.setFilterId(instant.getId());
        shareUser.setUserId(userId);
        shareUser.setShareType(0);
        shareUsers.add(shareUser);

        List<ReportSearchFilterPreference> preferences = new ArrayList<>();
        if (CollUtil.isNotEmpty(dto.getShareUserList())) {
            for (Long shareUserid : dto.getShareUserList()) {
                ReportSearchFilterPreference bean = new ReportSearchFilterPreference();
                bean.setUserId(shareUserid);
                bean.setTenantId(SecurityUtils.getTenantId());
                bean.setTitleName(dto.getTitleName());
                bean.setReportSearchFilter(dto.getSearchFilter());
                bean.setReportType(ReportType.E1.toDbValue());
                preferences.add(bean);
            }

            reportSearchFilterPreferenceRepository.saveAll(preferences);
            preferences.forEach(x->{
                ReportSearchFilterShareUser shareUserBean = new ReportSearchFilterShareUser();
                shareUserBean.setFilterId(instant.getId());
                shareUserBean.setUserId(x.getUserId());
                shareUserBean.setShareFilterId(x.getId());
                shareUserBean.setShareType(1);
                shareUsers.add(shareUserBean);
            });
        }

        reportSearchFilterShareUserRepository.saveAll(shareUsers);
    }

    @Override
    public ReportE1ReportFilterPreferenceVO getReportFilterPreferences(Long id) {
        Optional<ReportSearchFilterPreference> searchFilterPreferenceOptional = reportSearchFilterPreferenceRepository.findById(id);
        if (searchFilterPreferenceOptional.isPresent()) {
            ReportSearchFilterPreference bean = searchFilterPreferenceOptional.get();
            if(!bean.getUserId().equals(SecurityUtils.getUserId())){
                return null;
            }
            ReportE1ReportFilterPreferenceVO vo = new ReportE1ReportFilterPreferenceVO();
            vo.setSearchFilter(bean.getReportSearchFilter());
            vo.setId(bean.getId());
            vo.setTitleName(bean.getTitleName());
            vo.setUserId(bean.getUserId());
            vo.setTenantId(bean.getTenantId());

            List<UserBriefDTO> userAll = userService.getAllBriefUsers(SecurityUtils.getTenantId());
            Map<Long, String> userMap = userAll.stream().collect(Collectors.toMap(x -> x.getId(), x -> x.getFirstName() + " " + x.getLastName()));

            List<ReportSearchFilterShareUser> shareUsers = reportSearchFilterShareUserRepository.findByFilterIdInAndShareType(Arrays.asList(bean.getId()), 1);
            Map<Long, List<ReportSearchFilterShareUser>> shareUsrMap = shareUsers.stream().collect(Collectors.groupingBy(x -> x.getFilterId()));
            if (shareUsrMap.containsKey(bean.getId())) {
                List<ReportE1ReportFilterShareUserVO> shareUserVOS = new ArrayList();
                List<ReportSearchFilterShareUser> shareUserList = shareUsrMap.get(bean.getId());
                shareUserList.forEach(z -> {
                    ReportE1ReportFilterShareUserVO shareUserBean = new ReportE1ReportFilterShareUserVO();
                    shareUserBean.setUserId(z.getUserId());
                    if (userMap.containsKey(z.getUserId())) {
                        shareUserBean.setUserName(userMap.get(z.getUserId()));
                    }
                    shareUserVOS.add(shareUserBean);
                });
                vo.setShareUserList(shareUserVOS);
            }
            return vo;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReportFilterPreferences(Long id) {
        Optional<ReportSearchFilterPreference> searchFilterPreferenceOptional = reportSearchFilterPreferenceRepository.findById(id);
        if (searchFilterPreferenceOptional.isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETSYSTEMCONFIGBYCATEGORY_NOTFOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList("id info"), userApiPromptProperties.getUserService()));
        }
        if(!SecurityUtils.isAdmin()){
            if (!SecurityUtils.getUserId().equals(searchFilterPreferenceOptional.get().getUserId())) {
                throw new CustomParameterizedException("只允许本人操作。");
            }
        }
        reportSearchFilterPreferenceRepository.deleteById(id);

        List<ReportSearchFilterShareUser> shareUsers = reportSearchFilterShareUserRepository.findByFilterId(id);
        if (!shareUsers.isEmpty()) {
            List<Long> filterIdList = shareUsers.stream().filter(x -> x.getShareType() == 0).map(x -> x.getId()).collect(Collectors.toList());
            List<Long> filterIdListByShareId = shareUsers.stream().filter(x -> x.getShareType() == 1).map(x -> x.getShareFilterId()).collect(Collectors.toList());
            if (!filterIdList.isEmpty()) {
                reportSearchFilterShareUserRepository.deleteAllByFilterId(id);
                reportSearchFilterPreferenceRepository.deleteAllByIdList(filterIdListByShareId);
            } else {
                List<Long> filterIdListByShare = shareUsers.stream().filter(x -> x.getShareType() == 1).map(x -> x.getId()).collect(Collectors.toList());
                reportSearchFilterShareUserRepository.deleteAllByIdList(filterIdListByShare);
            }
        } else {
            reportSearchFilterShareUserRepository.deleteAllByShareFilterId(id);
        }
    }
}
