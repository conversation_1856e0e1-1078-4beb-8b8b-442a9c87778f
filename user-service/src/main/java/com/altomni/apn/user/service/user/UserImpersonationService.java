package com.altomni.apn.user.service.user;

import com.altomni.apn.user.service.dto.user.UserImpersonationDTO;

import java.util.List;

public interface UserImpersonationService {

    UserImpersonationDTO grantToUser(UserImpersonationDTO userImpersonationDTO);

    List<UserImpersonationDTO> getMyImpersonation();

    UserImpersonationDTO revokeMyImpersonation(Long id);

    UserImpersonationDTO grantFromTo(UserImpersonationDTO userImpersonationDTO);

    List<UserImpersonationDTO> getAllImpersonation();

    List<UserImpersonationDTO> getGrantToImpersonationUsersByGrantFromUser(Long grantFromUserId);

    UserImpersonationDTO revokeImpersonation(Long id);
}