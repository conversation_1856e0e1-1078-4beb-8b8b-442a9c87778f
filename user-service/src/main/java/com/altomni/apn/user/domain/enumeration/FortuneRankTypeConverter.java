package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.AbstractAttributeConverter;

import javax.persistence.Converter;

@Converter
public class FortuneRankTypeConverter extends AbstractAttributeConverter<FortuneRankType, Integer> {
    public FortuneRankTypeConverter() {
        super(FortuneRankType::toDbValue, FortuneRankType::fromDbValue);
    }
}
