package com.altomni.apn.user.service.tenant;

import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.dto.parser.TenantWatermarkDTO;
import com.altomni.apn.user.service.dto.user.TenantCreditInfoVO;
import com.altomni.apn.user.service.dto.user.TenantDTO;
import com.altomni.apn.user.service.dto.user.TenantVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TenantService {

//    TenantDTO create(TenantDTO dto);
//
    TenantDTO update(TenantDTO dto);

//    void delete(Long id);

//    List<TenantVO> findAll();

    List<Tenant> findActiveTenant();

    TenantVO findOne(Long id);

    TenantCreditInfoVO findAvailableCredit(Long id);

    TenantWatermarkDTO getTenantWatermarkConfig(Long tenantId);

//    void updateTenantStatusToActive(Long id, TenantUpdateStatusDTO dto);
//
//    void updateTenantStatusToInActive(Long id);

//    void updateTenantAndActiveUserCredit(String expireDate);

}
