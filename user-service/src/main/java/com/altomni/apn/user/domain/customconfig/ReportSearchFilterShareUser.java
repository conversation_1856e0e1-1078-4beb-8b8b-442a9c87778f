package com.altomni.apn.user.domain.customconfig;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "report_search_filter_share_user")
public class ReportSearchFilterShareUser extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 3297627398848418727L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "filter_id")
    private Long filterId ;

    @Column(name = "share_filter_id")
    private Long shareFilterId ;


    @Column(name = "share_type")
    private Integer shareType ;
}
