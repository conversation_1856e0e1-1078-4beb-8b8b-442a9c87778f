package com.altomni.apn.user.service.dto.user;

import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

public class EsSearchByContactsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "search Conditions")
    public List<String> searchConditions;

    @ApiModelProperty(value = "existed Contacts")
    public List<String> existContacts;

    public List<String> getSearchConditions() {
        return searchConditions;
    }

    public void setSearchConditions(List<String> searchConditions) {
        this.searchConditions = searchConditions;
    }

    public List<String> getExistContacts() {
        return existContacts;
    }

    public void setExistContacts(List<String> existContacts) {
        this.existContacts = existContacts;
    }

    @Override
    public String toString() {
        return "EsSearchByContactsDTO{" +
            "searchConditions=" + searchConditions +
            ", existContacts=" + existContacts +
            '}';
    }
}
