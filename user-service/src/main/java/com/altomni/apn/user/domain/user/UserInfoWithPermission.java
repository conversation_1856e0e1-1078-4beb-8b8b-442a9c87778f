package com.altomni.apn.user.domain.user;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserInfoWithPermission {
    private Long id;
    private String firstName;
    private String lastName;
    private Integer dataScope;
    private Boolean isPrimary;
    private Long teamId;
    private Boolean activated;


    public UserInfoWithPermission(Long id, String firstName, String lastName, Integer dataScope, Boolean isPrimary, Long teamId) {
        this.id = id;
        this.firstName = firstName;
        this.lastName = lastName;
        this.dataScope = dataScope;
        this.isPrimary = isPrimary;
        this.teamId = teamId;
    }

}
