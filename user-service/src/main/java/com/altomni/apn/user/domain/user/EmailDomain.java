package com.altomni.apn.user.domain.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import javax.persistence.*;
import java.io.Serializable;

/**
 * A EmailDomain.
 */
@Entity
@Table(name = "email_domain")
@JsonIgnoreProperties(value={"hibernateLazyInitializer","handler","fieldHandler"})
public class EmailDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "sub_account_id")
    private Integer subAccountId;

    @Column(name = "email")
    private String email;

    @Column(name = "display_name")
    private String displayName;

    @Column(name = "unsubscribe_link")
    private String unsubscribeLink;

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSubAccountId() {
        return subAccountId;
    }

    public EmailDomain subAccoundId(Integer subAccoundId) {
        this.subAccountId = subAccoundId;
        return this;
    }

    public void setSubAccountId(Integer subAccoundId) {
        this.subAccountId = subAccoundId;
    }

    public String getEmail() {
        return email;
    }

    public EmailDomain email(String email) {
        this.email = email;
        return this;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getDisplayName() {
        return displayName;
    }

    public EmailDomain displayName(String displayName) {
        this.displayName = displayName;
        return this;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public String getUnsubscribeLink() {
        return unsubscribeLink;
    }

    public EmailDomain unsubscribeLink(String unsubscribeLink) {
        this.unsubscribeLink = unsubscribeLink;
        return this;
    }

    public void setUnsubscribeLink(String unsubscribeLink) {
        this.unsubscribeLink = unsubscribeLink;
    }
    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmailDomain)) {
            return false;
        }
        return id != null && id.equals(((EmailDomain) o).id);
    }

    @Override
    public int hashCode() {
        return 31;
    }

    @Override
    public String toString() {
        return "EmailDomain{" +
            "id=" + getId() +
            ", subAccountId=" + getSubAccountId() +
            ", email='" + getEmail() + "'" +
            ", displayName='" + getDisplayName() + "'" +
            ", unsubscribeLink='" + getUnsubscribeLink() + "'" +
            "}";
    }
}
