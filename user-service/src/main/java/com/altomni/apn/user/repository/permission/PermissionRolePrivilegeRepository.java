package com.altomni.apn.user.repository.permission;

import com.altomni.apn.user.domain.permission.PermissionRolePrivilege;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PermissionRolePrivilegeRepository extends JpaRepository<PermissionRolePrivilege, Long> {

    @Modifying
    @Query(value = "delete from permission_role_privilege where role_id=:roleId", nativeQuery = true)
    void deleteAllByRoleId(@Param("roleId") Long roleId);

    @Query(value = "SELECT rp.role_id FROM permission_role_privilege rp" +
            " WHERE rp.privilege_id=:privilegeId", nativeQuery = true)
    List<Long> findRoleIdsByPrivilegeId(@Param("privilegeId") Long privilegeId);
}
