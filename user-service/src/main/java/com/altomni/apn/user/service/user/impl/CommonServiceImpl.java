package com.altomni.apn.user.service.user.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.user.ApnParam;
import com.altomni.apn.user.repository.user.ApnParamRepository;
import com.altomni.apn.user.service.user.CommonService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class CommonServiceImpl implements CommonService {

    @Resource
    private ApnParamRepository apnParamRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    /**
     * query user credit limit
     * @return param value
     */
    @Override
    public Integer getUserCreditLimit(Long tenantId) {
        String userCreditLimit = getApnParamValue("USER_CREDIT_LIMIT", tenantId);
        if (userCreditLimit == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_GETUSERCREDITLIMIT_USERCREDITLIMITNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(SecurityUtils.getTenantId()),userApiPromptProperties.getUserService()));
        }
        return Integer.valueOf(userCreditLimit);
    }

    private String getApnParamValue(String paramKey, Long tenantId) {
        ApnParam apnParam = apnParamRepository.findByParamKeyAndTenantIdAndStatus(paramKey, tenantId, Status.Available);
        return apnParam != null ? apnParam.getParamValue() : null;
    }

    /**
     * query user credit limit
     * @return param value
     */
    @Override
    public Integer getLimitUserCreditLimit(Long tenantId) {
        String userCreditLimit = getApnParamValue("LIMIT_USER_CREDIT_LIMIT", tenantId);
        if (userCreditLimit == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_GETUSERCREDITLIMIT_USERCREDITLIMITNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(SecurityUtils.getTenantId()),userApiPromptProperties.getUserService()));
        }
        return Integer.valueOf(userCreditLimit);
    }

    /**
     * query apn param value by param key
     * @param paramKey param key
     * @return param value
     */
    @Override
    public String getAllApnParamValue(String paramKey) {
        ApnParam apnParam = apnParamRepository.findByParamKeyAndStatus(paramKey, Status.Available);
        return apnParam != null ? apnParam.getParamValue() : null;
    }

    @Override
    public Integer getTenantParamValue(String paramKey) {
        ApnParam tenantParam = apnParamRepository.findByParamKeyAndTenantIdAndStatus(paramKey, SecurityUtils.getTenantId(), Status.Available);
        if (tenantParam != null) {
            return Integer.valueOf(tenantParam.getParamValue());
        }
        ApnParam defaultParam = apnParamRepository.findByParamKeyAndTenantIdAndStatus(paramKey, Constants.DEFAULT_ALL_TENANT, Status.Available);
        return defaultParam != null ? Integer.valueOf(defaultParam.getParamValue()) : null;
    }

    @Override
    public Integer getTenantParamValueByTenantId(String paramKey, Long tenantId) {
        ApnParam tenantParam = apnParamRepository.findByParamKeyAndTenantIdAndStatus(paramKey, tenantId, Status.Available);
        if (tenantParam != null) {
            return Integer.valueOf(tenantParam.getParamValue());
        }
        ApnParam defaultParam = apnParamRepository.findByParamKeyAndTenantIdAndStatus(paramKey, Constants.DEFAULT_ALL_TENANT, Status.Available);
        return defaultParam != null ? Integer.valueOf(defaultParam.getParamValue()) : null;
    }

    @Override
    public List<Map<Long, String>> getTenantParamList(String paramKey) {
        List<Map<Long, String>> result = new ArrayList<>();
        List<ApnParam> tenantParams = apnParamRepository.findAllByParamKeyAndTenantIdAndStatus(paramKey, SecurityUtils.getTenantId(), Status.Available);
        if (CollectionUtils.isNotEmpty(tenantParams)) {
            for (ApnParam apnParam : tenantParams) {
                Map<Long, String> param = new HashMap<>();
                param.put(apnParam.getId(), apnParam.getParamName());
                result.add(param);
            }
            return result;
        }
        List<ApnParam> defaultParams = apnParamRepository.findAllByParamKeyAndTenantIdAndStatus(paramKey, Constants.DEFAULT_ALL_TENANT, Status.Available);
        for (ApnParam apnParam : defaultParams) {
            Map<Long, String> param = new HashMap<>();
            param.put(apnParam.getId(), apnParam.getParamName());
            result.add(param);
        }
        return result;
    }

    /**
     * query user credit limit
     * @return param value
     */
    @Override
    public String getParamById(Long id) {
        ApnParam param = apnParamRepository.findById(id).orElse(null);
        if (param == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_GETPARAMBYID_PARAMNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return param.getParamValue();
    }

    @Override
    public String getTenantParamStringValue(String paramKey) {
        ApnParam tenantParam = apnParamRepository.findByParamKeyAndTenantIdAndStatus(paramKey, SecurityUtils.getTenantId(), Status.Available);
        if (tenantParam != null) {
            return tenantParam.getParamValue();
        }
        ApnParam defaultParam = apnParamRepository.findByParamKeyAndTenantIdAndStatus(paramKey, Constants.DEFAULT_ALL_TENANT, Status.Available);
        return defaultParam != null ? defaultParam.getParamValue() : null;

    }
}
