package com.altomni.apn.user.web.rest.permission;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.service.dto.permission.PermissionModulePrivilegeDTO;
import com.altomni.apn.user.service.permission.PermissionModulePrivilegeService;
import com.altomni.apn.common.domain.permission.PermissionModulePrivilegePageVM;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/modules-privilege")
public class PermissionModulePrivilegeResource {

    @Resource
    private PermissionModulePrivilegeService permissionModulePrivilegeService;

    @GetMapping("/{tenantId}/list")
    @ApiOperation(value = "search tenant module List", tags = {"APN V3"})
    public ResponseEntity<List<PermissionModulePrivilegeDTO>> searchTenantModuleList(@PathVariable("tenantId") Long tenantId) {
        log.info("[APN User: id @{}] REST request to search tenant module list : {}", SecurityUtils.getUserId(), tenantId);
        return ResponseEntity.ok(permissionModulePrivilegeService.searchTenantModuleLis(tenantId));
    }

    @PutMapping("/modules/{tenantId}")
    @NoRepeatSubmit
    @ApiOperation(value = "update tenant module List", tags = {"APN V3"})
    public ResponseEntity<HttpStatus> updateTenantModuleList(@PathVariable("tenantId") Long tenantId, @Valid @RequestBody List<PermissionModulePrivilegeDTO> tenantModuleDTOList) {
        log.info("[APN User: id @{}] REST request to update tenant module list : {}", SecurityUtils.getUserId(), tenantId);
        permissionModulePrivilegeService.updateTenantModuleList(tenantId, tenantModuleDTOList);
        return new ResponseEntity<>(HttpStatus.CREATED);
    }

    @GetMapping("/modules/defalut/{tenantId}/{type}")
    @ApiOperation(value = "query tenant module List", tags = {"APN V3"})
    public ResponseEntity<List<PermissionModulePrivilegePageVM>> findTenantDefaultModuleList(@PathVariable("tenantId") Long tenantId, @PathVariable("type") TenantUserTypeEnum type) {
        log.info("[APN User: id @{}] REST request to query tenant defalut module list : {}", SecurityUtils.getUserId(), tenantId);
        return ResponseEntity.ok(permissionModulePrivilegeService.findTenantDefaultModuleList(tenantId, type));
    }

}
