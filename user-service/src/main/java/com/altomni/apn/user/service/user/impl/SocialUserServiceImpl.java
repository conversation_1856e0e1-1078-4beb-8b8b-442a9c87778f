package com.altomni.apn.user.service.user.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.dto.CredentialDTO;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.RandomUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.enumeration.LoginType;
import com.altomni.apn.user.domain.user.SocialConsumer;
import com.altomni.apn.user.repository.user.SocialConsumerRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.user.LinkedInUser;
import com.altomni.apn.user.service.dto.user.SocialUser;
import com.altomni.apn.user.service.management.ManagementService;
import com.altomni.apn.user.service.user.SocialUserService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.FormBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Service
public class SocialUserServiceImpl implements SocialUserService {

    private String LINKEDIN_REDIRECTURI;

    private String LINKEDIN_CLIENTID;

    private String LINKEDIN_CLIENTSECRET;

    private String LINKEDIN_ACCESSTOKENURL;

    private String LINKEDIN_ACCESSTOKENHOST;

    private String LINKEDIN_PEOPLEURL;

    private String LINKEDIN_PEOPLEHOST;

    @Resource
    private UserRepository userRepository;

    @Resource
    private SocialConsumerRepository socialConsumerRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    private PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    private String serverPort;

    private String Oauth2AuthHeader;

    @Resource
    private ManagementService managementService;

    @Override
    public User socialLogin(SocialUser socialUser) {
        try {
            OkHttpClient client = new OkHttpClient();
            okhttp3.RequestBody form = new FormBody.Builder()
                .add(Constants.CUSTOM_AUTHENTICATION_KEY, "social-login")
                .add(Constants.CUSTOM_AUTHENTICATION_PROVIDER_KEY, socialUser.provider.name())
                .add("userJson", JSON.toJSONString(socialUser))
                .add("grant_type", "password").build();
            Request request = new Request.Builder().url("http://127.0.0.1:" + serverPort + "/oauth/token")
                .addHeader("Authorization", "Basic " + Oauth2AuthHeader)
                .post(form).build();
            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject cred = JSONObject.parseObject(res);
            if (!response.isSuccessful()) {
                String error = cred.getString("error_description");
                log.error("[SocialUserService.socialLogin] Login user {}, error {}", socialUser, error);
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_SOCIALLOGIN_BADCREDENTIAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
            }
            return socialConsumerRepository.findByProviderAndIdInProvider(socialUser.provider.name(), socialUser.id)
                .map(SocialConsumer::getUser)
                .map(user -> {
                    user.credential = JSON.parseObject(res, CredentialDTO.class);
                    return user;
                })
                .orElseThrow(() -> new CustomParameterizedException("User not found"));
        } catch (Exception e) {
            log.error("[UserService.login] Login user {}, exception {}", socialUser, ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_SOCIALLOGIN_BADCREDENTIAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
    }

    @Transactional
    @Override
    public User saveUserFromSocialLogin(String userJson, String provider) {
        SocialUser socialUser;
        if (provider.equals(LoginType.LINKEDIN.name())) {
            socialUser = JSON.parseObject(userJson, LinkedInUser.class);
        } else {
            return null;
        }
        String identifier = socialUser.identifier();
        if (identifier == null){
            return null;
        }
        return socialConsumerRepository.findByProviderAndIdInProvider(provider, socialUser.id)
            .map(consumer -> userRepository.save(socialUser.syncUser(consumer.getUser()))).orElseGet(() -> {
                User user = userRepository.findUser(identifier).orElse(null); // check existing users
                user = socialUser.syncUser(user);
                if (user.getId() == null) { // this is new user
                    /*Role authority = roleRepository.findById(AuthoritiesConstants.CONSUMER).orElseThrow();
                    Set<Role> authorities = new HashSet<>();
                    authorities.add(authority);*/
                    user.setTenantId(1L);
                    user.setPassword(passwordEncoder.encode(RandomUtil.generatePassword()));
                    //user.setRoles(authorities);
                    user = addUser(user);
                    //user.setUid(SecurityUtils.getUid(user));
//                    user = userRepository.saveAndFlush(user);
                } else {
                    user = userRepository.saveAndFlush(user);
                }
                SocialConsumer socialConsumer = socialUser.consumer();
                socialConsumer.setUser(user);
                socialConsumerRepository.save(socialConsumer);
                return user;
            });
    }

    private synchronized User addUser(User user) {
        Tenant tenant = managementService.queryTenant(user.getTenantId()).getBody();
        int userCount = userRepository.countByTenantId(user.getTenantId());
        if (tenant != null && userCount >= tenant.getUserMaxLimit()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_ADDUSER_ACCOUNTLIMIT.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        return userRepository.saveAndFlush(user);
    }

    @Override
    public SocialUser linkedinLogin(String code, String redirect_uri) {

        String linkedinAccessToken;
        // region linkedin login
        try {
            OkHttpClient client = new OkHttpClient();
            String redirectUri = StringUtils.isEmpty(redirect_uri) ? LINKEDIN_REDIRECTURI : redirect_uri;
            okhttp3.RequestBody form = new FormBody.Builder()
                .add("grant_type", "authorization_code")
                .add("code", code)
                .add("redirect_uri", redirectUri)
                .add("client_id", LINKEDIN_CLIENTID)
                .add("client_secret", LINKEDIN_CLIENTSECRET)
                .build();
            Request request = new Request.Builder().url(LINKEDIN_ACCESSTOKENURL)
                .addHeader("Host", LINKEDIN_ACCESSTOKENHOST)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .post(form).build();

            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject token = JSONObject.parseObject(res);
            linkedinAccessToken = token.getString("access_token"); // access_token, expires_in
        } catch (Exception e) {
            log.error("linkedin login error, code {}, exception {}", code, ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_SOCIALLOGIN_BADCREDENTIAL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        // endregion


        // region linkedin user basic info
        try {
            OkHttpClient client = new OkHttpClient();
            Request request = new Request.Builder().url(LINKEDIN_PEOPLEURL)
                .addHeader("Host", LINKEDIN_PEOPLEHOST)
                .addHeader("Connection", "Keep-Alive")
                .addHeader("Authorization", "Bearer " + linkedinAccessToken)
                .get().build();

            Response response = client.newCall(request).execute();
            String res = response.body().string();
            JSONObject resJson = JSONObject.parseObject(res);
            String id = resJson.getString("id");
            String firstName = resJson.getString("firstName");
            String lastName = resJson.getString("lastName");
            String email = resJson.getString("emailAddress");

            LinkedInUser user = new LinkedInUser();
            user.setProvider(LoginType.LINKEDIN);
            user.setId(id);
            user.setCode(code);
            user.setFirstName(firstName);
            user.setLastName(lastName);
            user.setEmail(email);

            return user;
        } catch (Exception e) {
            log.error("linkedin get basic info error, code {}, exception {}", code, ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_LINKEDINLOGIN_FAILGETUSER.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        // endregion
    }

}
