package com.altomni.apn.user.service.mail;

import com.altomni.apn.common.dto.email.UserResetDTO;
import com.altomni.apn.common.dto.mail.MailVM;
import com.altomni.apn.common.utils.SecurityUtils;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.io.IOException;

@Component
@FeignClient(value = "common-service")
public interface MailService {

    @PostMapping("/common/api/v3/system/reset-pwd-init")
    ResponseEntity<Long> sendPasswordResetMail(@RequestBody UserResetDTO user);

    @PostMapping("/common/api/v3/campaign/send_html_mail")
    ResponseEntity<Long> sendHtmlMail(@RequestBody MailVM mailVM);

    @PostMapping("/common/api/v3/system/reset-pwd-init")
    ResponseEntity<Void> sendResetPasswordInitEmail(@RequestBody @Valid UserResetDTO userResetDTO);

    @PostMapping(value = "/common/api/v3/system/reset-pwd")
    ResponseEntity<Void> sendResetPasswordEmail(@RequestBody @Valid UserResetDTO userResetDTO);
}
