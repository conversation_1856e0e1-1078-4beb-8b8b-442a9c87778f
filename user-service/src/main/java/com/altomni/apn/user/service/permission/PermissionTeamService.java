package com.altomni.apn.user.service.permission;

import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.user.service.dto.permission.PermissionTeamDTO;
import com.altomni.apn.user.service.dto.permission.PermissionTeamTreeDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamCreateVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamUpdateVM;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public interface PermissionTeamService {

    PermissionTeamDTO create(PermissionTeamCreateVM permissionTeamCreateVM);

    PermissionTeamDTO update(PermissionTeamUpdateVM permissionTeamUpdateVM);

    Integer deleteAndChangeTeam(Long deleteTeamId, Long changeTeamId);

    List<PermissionTeamTreeDTO> getTeamTreeWithLeaders() throws ExecutionException, InterruptedException;

    List<PermissionTeamTreeDTO> getPlainTeamTree();

//    List<PermissionTeamTreeDTO> getTeamTreeWithPermission() throws ExecutionException, InterruptedException;

    List<PermissionTeamTreeDTO> getTeamTreeWithPermissionByType(Module module) throws ExecutionException, InterruptedException;

    void addTeamLeader(Long teamId, Long leaderUserId);

    void deleteTeamLeader(Long teamId, Long leaderId);

    Integer countActiveUsersByTeamId(Long teamId);

    List<PermissionTeamTreeDTO> getSelfSubTeamsWithLeaders() throws ExecutionException, InterruptedException;


    List<PermissionTeamTreeDTO> getTeamTreeWithPermissionUserByType(Module module, Boolean isPrimary) throws ExecutionException, InterruptedException;

    Map<Long, String> getPlainTeamsWithNameAndId();
}
