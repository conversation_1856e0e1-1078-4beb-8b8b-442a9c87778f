package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.repository.customconfig.SystemConfigDefaultRepository;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.customconfig.DashboardColumnConfigService;
import com.altomni.apn.user.service.dto.customconfig.DashboardColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.DashboardColumnConfigVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("dashboardService")
public class DashboardColumnConfigServiceImpl implements DashboardColumnConfigService {

    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    private SystemConfigDefaultRepository systemConfigDefaultRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    /**
     * 根据userId 查询首页的自定义仪表盘
     * @param userId
     * @return
     */
    @Override
    public DashboardColumnConfigVO searchDashboardColumnConfigByUserId(Long userId) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);
        //记录不存在 user_preference 表的记录
        boolean notExistsFlag = userPreference == null;
        if (notExistsFlag || StrUtil.isBlank(userPreference.getDashboardColumnConfig())) {
            List<SystemConfigDefault> systemConfigDefaultList = systemConfigDefaultRepository.findByCategory(Category.DASHBOARD_COLUMN);
            if (CollUtil.isEmpty(systemConfigDefaultList)) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_SEARCHDASHBOARDCOLUMNCONFIGBYUSERID_PARAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
            }
            String defaultConfig = systemConfigDefaultList.get(0).getDefaultConfig();
            if (notExistsFlag) {
                //从来没有记录
                UserPreference addUserPreference = new UserPreference();
                addUserPreference.setUserId(userId);
                addUserPreference.setDashboardColumnConfig(defaultConfig);
                userPreferenceRepository.save(addUserPreference);
            } else {
                //已经有过其他记录
                updateDashboardColumnConfigByUserId(userId, new DashboardColumnConfigDTO(defaultConfig));
            }
            return new DashboardColumnConfigVO(defaultConfig);
        }
        return new DashboardColumnConfigVO(userPreference.getDashboardColumnConfig());
    }

    /**
     * 根据userId 修改首页自定义仪表盘
     * @param userId
     * @param dto
     */
    @Override
    public void updateDashboardColumnConfigByUserId(Long userId, DashboardColumnConfigDTO dto) {
        userPreferenceRepository.updateDashboardColumnConfigByUserId(userId, dto.getDashboardColumnConfig());
    }
}
