package com.altomni.apn.user.web.rest.vm.permission;

import com.altomni.apn.user.domain.permission.PermissionExtraUserTeam;
import com.altomni.apn.user.domain.user.UserInfoWithPermission;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class RelateJobFolderUserAllInfo {
    private Long teamId;
    private List<UserInfoWithPermission> userInfoWithPermissionList;
    private Map<Long, List<PermissionExtraUserTeam>> permissionExtraUserTeamListMap;

    public RelateJobFolderUserAllInfo(Long teamId) {
        this.teamId = teamId;
        userInfoWithPermissionList = new ArrayList<>();
        permissionExtraUserTeamListMap = new HashMap<>();
    }

    public void addUserInfoWithPermissionList(List<UserInfoWithPermission> userInfoWithPermissionList) {
        if(userInfoWithPermissionList == null) {
            return;
        }
        this.userInfoWithPermissionList.addAll(userInfoWithPermissionList);
    }


    public void addPermissionExtraUserTeamListMap(Long userId, List<PermissionExtraUserTeam> permissionExtraUserTeamList) {
        if(permissionExtraUserTeamList == null) {
            return;
        }
        this.permissionExtraUserTeamListMap.put(userId, permissionExtraUserTeamList);
    }
}
