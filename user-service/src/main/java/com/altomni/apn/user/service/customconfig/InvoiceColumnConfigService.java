package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.service.dto.customconfig.InvoiceColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.InvoiceColumnConfigVO;

public interface InvoiceColumnConfigService {

    InvoiceColumnConfigVO searchInvoiceColumnConfigByUserId(Long userId);

    void updateInvoiceColumnConfigByUserId(Long userId, InvoiceColumnConfigDTO dto);

}
