package com.altomni.apn.user.web.rest.user;

import com.altomni.apn.user.service.user.UserDeliveryLabelService;
import com.altomni.apn.user.web.rest.vm.user.UserLabelVM;
import com.altomni.apn.user.web.rest.vm.user.UserProfileWithStatVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class UserDeliveryLabelResource {

    @Resource
    UserDeliveryLabelService userDeliveryLabelService;


    @PutMapping("/users/{id}/labels")
    public ResponseEntity<Void> updateUserDeliveryLabel(@PathVariable("id") Long userId, @RequestBody UserLabelVM userLabelVM) {
        log.debug("updateUserDeliveryLabel  with userId: {}, userLabelVM: {}", userId, userLabelVM);
        userDeliveryLabelService.updateUserDeliveryLabel(userId, userLabelVM);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/users/personal-profile/{id}")
    public ResponseEntity<UserProfileWithStatVM> getUserPersonalProfileById(@PathVariable("id") Long userId) {
        log.debug("getUserDeliveryLabel  with userId: {}", userId);
        UserProfileWithStatVM result = userDeliveryLabelService.getUserPersonalProfileById(userId);
        return ResponseEntity.ok(result);
    }

}
