package com.altomni.apn.user.repository.permission;

import com.altomni.apn.user.domain.permission.PermissionTable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PermissionTableRepository extends JpaRepository<PermissionTable,Long> {

    @Query("select t from PermissionTable t" +
            " left join PermissionModuleTable mt on mt.tableId=t.id" +
            " left join PermissionModule m on m.id=mt.moduleId" +
            " left join PermissionTenantModule tm on tm.moduleId=m.id" +
            " where tm.tenantId=:tenantId and tm.involveDataPermission=true")
    List<PermissionTable> findInvolvedPermissionByTenantId(@Param("tenantId") Long tenantId);

    @Query("select t from PermissionTable t " +
            " left join PermissionModuleTable  mt on mt.tableId=t.id" +
            " where mt.moduleId=:moduleId")
    List<PermissionTable> findByModuleId(@Param("moduleId") Long moduleId);
}
