package com.altomni.apn.user.service.permission;

import com.altomni.apn.user.service.dto.permission.PermissionTableDTO;

import java.util.List;

public interface PermissionTableService {

    PermissionTableDTO create(PermissionTableDTO permissionTable);

    PermissionTableDTO update(PermissionTableDTO permissionTable);

    void delete(Long id);

    List<PermissionTableDTO> findAll();

    List<PermissionTableDTO> findByModuleId(Long moduleId);

}
