package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.service.dto.customconfig.CompanyCurrentContractorColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.CompanyCurrentContractorColumnConfigVO;

public interface CompanyCurrentContractorColumnConfigService {

    CompanyCurrentContractorColumnConfigVO searchCompanyCurrentContractorColumnConfigByUserId(Long userId);

    void updateCompanyCurrentContractorColumnConfigByUserId(Long userId, CompanyCurrentContractorColumnConfigDTO dto);

}
