package com.altomni.apn.user.service.tenant.impl;

import com.altomni.apn.common.enumeration.PushConfigType;
import com.altomni.apn.common.errors.NotFoundException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.domain.customconfig.TenantPushRules;
import com.altomni.apn.user.domain.customconfig.TenantPushRulesTeam;
import com.altomni.apn.user.domain.customconfig.TenantPushRulesType;
import com.altomni.apn.user.domain.customconfig.TenantPushRulesUser;
import com.altomni.apn.user.repository.customconfig.TenantPushRulesRepository;
import com.altomni.apn.user.service.tenant.TenantPushConfigService;
import com.altomni.apn.user.web.rest.vm.tenant.TenantPushRuleDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TenantPushConfigServiceImpl implements TenantPushConfigService {
    @Resource
    private TenantPushRulesRepository tenantPushRulesRepository;

    @Override
    public void createTenantPushRule(TenantPushRuleDTO tenantPushRuleDTO) {
        TenantPushRules rules = new TenantPushRules();
        rules.setTenantId(SecurityUtils.getTenantId());
        rules.setPush_interval(tenantPushRuleDTO.getInterval());
        rules.setTenantPushRulesTeamSet(getTenantPushRulesTeamSet(tenantPushRuleDTO.getTeamIds()));
        rules.setTenantPushRulesUserSet(getTenantPushRulesUserSet(tenantPushRuleDTO.getUserIds()));
        rules.setTenantPushRulesTypeSet(getTenantPushRulesTypeSet(tenantPushRuleDTO.getType()));
        tenantPushRulesRepository.save(rules);
    }

    @Override
    public void updateTenantPushRule(TenantPushRuleDTO tenantPushRuleDTO) {
        TenantPushRules tenantPushRules = tenantPushRulesRepository.findById(tenantPushRuleDTO.getId()).orElseThrow(() -> new NotFoundException("Tenant push config not exist!"));
        if(!Objects.equals(SecurityUtils.getTenantId(), tenantPushRules.getTenantId())) {
            throw new NotFoundException("Tenant push config not exist!");
        }

        tenantPushRules.setPush_interval(tenantPushRuleDTO.getInterval());
        updateTenantPushRulesTeamSet(tenantPushRules, tenantPushRuleDTO.getTeamIds());
        updateTenantPushRulesUserSet(tenantPushRules, tenantPushRuleDTO.getUserIds());
        updateTenantPushRulesTypeSet(tenantPushRules, tenantPushRuleDTO.getType());
        tenantPushRulesRepository.save(tenantPushRules);
    }

    @Override
    public void deleteTenantPushRule(Long id) {
        TenantPushRules tenantPushRules = tenantPushRulesRepository.findById(id).orElseThrow(() -> new NotFoundException("Tenant push config not exist!"));
        if(!Objects.equals(SecurityUtils.getTenantId(), tenantPushRules.getTenantId())) {
            throw new NotFoundException("Tenant push config not exist!");
        }
        tenantPushRulesRepository.delete(tenantPushRules);
    }

    @Override
    public List<TenantPushRuleDTO> getTenantPushRule() {
        List<TenantPushRules> tenantPushRules = tenantPushRulesRepository.getTenantPushRulesByTenantIdIsOrderByCreatedDateDesc(SecurityUtils.getTenantId());
        if (tenantPushRules == null) {
            return Collections.emptyList();
        }

        return tenantPushRules.stream().map(rule -> {
            TenantPushRuleDTO dto = new TenantPushRuleDTO();
            dto.setId(rule.getId());
            dto.setInterval(rule.getPush_interval());
            Set<TenantPushRulesTeam> teamSet = rule.getTenantPushRulesTeamSet();
            dto.setTeamIds(teamSet == null ? Collections.emptyList() :
                    teamSet.stream().map(TenantPushRulesTeam::getTeamId).collect(Collectors.toList()));

            Set<TenantPushRulesUser> userSet = rule.getTenantPushRulesUserSet();
            dto.setUserIds(userSet == null ? Collections.emptyList() :
                    userSet.stream().map(TenantPushRulesUser::getUserId).collect(Collectors.toList()));

            Set<TenantPushRulesType> typeSet = rule.getTenantPushRulesTypeSet();
            dto.setType(typeSet == null ? Collections.emptyList() :
                    typeSet.stream().map(TenantPushRulesType::getType).collect(Collectors.toList()));
            dto.setCreatedBy(rule.getCreatedBy());
            dto.setCreatedDate(rule.getCreatedDate());
            dto.setLastModifiedBy(rule.getLastModifiedBy());
            dto.setLastModifiedDate(rule.getLastModifiedDate());

            return dto;
        }).collect(Collectors.toList());
    }

    private void updateTenantPushRulesTypeSet(TenantPushRules tenantPushRules, List<PushConfigType> types) {
        Set<TenantPushRulesType> tenantPushRulesTypeSet = tenantPushRules.getTenantPushRulesTypeSet();
        if (tenantPushRulesTypeSet == null) {
            tenantPushRulesTypeSet = new HashSet<>();
        }

        // 创建现有type的集合，用于后续比较
        Set<PushConfigType> existingTypes = tenantPushRulesTypeSet.stream()
                .map(TenantPushRulesType::getType)
                .collect(Collectors.toSet());

        // 移除不在新types列表中的项
        tenantPushRulesTypeSet.removeIf(typeEntity -> !types.contains(typeEntity.getType()));

        // 添加新的types中不存在于当前集合中的项
        if (types != null) {
            for (PushConfigType type : types) {
                if (!existingTypes.contains(type)) {
                    TenantPushRulesType newType = new TenantPushRulesType();
                    newType.setType(type);
                    newType.setTenantPushRulesId(tenantPushRules.getId()); // 假设TenantPushRules有getId方法
                    tenantPushRulesTypeSet.add(newType);
                }
            }
        }

        // 更新tenantPushRules的tenantPushRulesTypeSet
        tenantPushRules.setTenantPushRulesTypeSet(tenantPushRulesTypeSet);
    }

    private void updateTenantPushRulesTeamSet(TenantPushRules tenantPushRules, List<Long> teamIds) {
        Set<TenantPushRulesTeam> tenantPushRulesTeamSet = tenantPushRules.getTenantPushRulesTeamSet();
        if (tenantPushRulesTeamSet == null) {
            tenantPushRulesTeamSet = new HashSet<>();
        }

        // 创建现有teamId的集合，用于后续比较
        Set<Long> existingTeamIds = tenantPushRulesTeamSet.stream()
                .map(TenantPushRulesTeam::getTeamId)
                .collect(Collectors.toSet());

        // 移除不在新teamIds列表中的项
        tenantPushRulesTeamSet.removeIf(team -> !teamIds.contains(team.getTeamId()));

        // 添加新的teamIds中不存在于当前集合中的项
        if (teamIds != null) {
            for (Long teamId : teamIds) {
                if (!existingTeamIds.contains(teamId)) {
                    TenantPushRulesTeam newTeam = new TenantPushRulesTeam();
                    newTeam.setTeamId(teamId);
                    newTeam.setTenantPushRulesId(tenantPushRules.getId()); // 假设TenantPushRules有getId方法
                    tenantPushRulesTeamSet.add(newTeam);
                }
            }
        }

        // 更新tenantPushRules的tenantPushRulesTeamSet
        tenantPushRules.setTenantPushRulesTeamSet(tenantPushRulesTeamSet);
    }

    private void updateTenantPushRulesUserSet(TenantPushRules tenantPushRules, List<Long> userIds) {
        Set<TenantPushRulesUser> tenantPushRulesUserSet = tenantPushRules.getTenantPushRulesUserSet();
        if (tenantPushRulesUserSet == null) {
            tenantPushRulesUserSet = new HashSet<>();
        }

        // 创建现有userId的集合，用于后续比较
        Set<Long> existingUserIds = tenantPushRulesUserSet.stream()
                .map(TenantPushRulesUser::getUserId)
                .collect(Collectors.toSet());

        // 移除不在新userIds列表中的项
        tenantPushRulesUserSet.removeIf(user -> !userIds.contains(user.getUserId()));

        // 添加新的userIds中不存在于当前集合中的项
        if (userIds != null) {
            for (Long userId : userIds) {
                if (!existingUserIds.contains(userId)) {
                    TenantPushRulesUser newUser = new TenantPushRulesUser();
                    newUser.setUserId(userId);
                    newUser.setTenantPushRulesId(tenantPushRules.getId()); // 假设TenantPushRules有getId方法
                    tenantPushRulesUserSet.add(newUser);
                }
            }
        }

        // 更新tenantPushRules的tenantPushRulesUserSet
        tenantPushRules.setTenantPushRulesUserSet(tenantPushRulesUserSet);
    }

    private Set<TenantPushRulesType> getTenantPushRulesTypeSet(List<PushConfigType> type) {
        if(type == null) {
            return new HashSet<>();
        }
        return type.stream().map(pushConfigType -> {
            TenantPushRulesType tenantPushRulesType = new TenantPushRulesType();
            tenantPushRulesType.setType(pushConfigType);
            return tenantPushRulesType;
        }).collect(Collectors.toSet());
    }

    private Set<TenantPushRulesUser> getTenantPushRulesUserSet(List<Long> userIds) {
        if(userIds == null) {
            return new HashSet<>();
        }
        return userIds.stream().map(userId -> {
            TenantPushRulesUser tenantPushRulesUser = new TenantPushRulesUser();
            tenantPushRulesUser.setUserId(userId);
            return tenantPushRulesUser;
        }).collect(Collectors.toSet());
    }

    private Set<TenantPushRulesTeam> getTenantPushRulesTeamSet(List<Long> teamIds) {
        if(teamIds == null) {
            return new HashSet<>();
        }
        return teamIds.stream().map(teamId -> {
            TenantPushRulesTeam tenantPushRulesTeam = new TenantPushRulesTeam();
            tenantPushRulesTeam.setTeamId(teamId);
            return tenantPushRulesTeam;
        }).collect(Collectors.toSet());
    }
}
