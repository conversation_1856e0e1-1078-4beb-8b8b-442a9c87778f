package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.service.dto.customconfig.InvoicingColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.InvoicingColumnConfigVO;

public interface InvoicingColumnConfigService {

    InvoicingColumnConfigVO searchInvoicingColumnConfigByUserId(Long userId);

    void updateInvoicingColumnConfigByUserId(Long userId, InvoicingColumnConfigDTO dto);

}
