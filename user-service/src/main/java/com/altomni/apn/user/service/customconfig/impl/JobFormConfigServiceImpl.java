package com.altomni.apn.user.service.customconfig.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.enumeration.job.JobType;
import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.enumeration.tenant.TenantUserTypeEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.JobFormConfig;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.JobFormConfigRepository;
import com.altomni.apn.user.service.application.ApplicationService;
import com.altomni.apn.user.service.customconfig.JobFormConfigService;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.dto.customconfig.CustomFormField;
import com.altomni.apn.user.service.dto.customconfig.JobFormConfigDTO;
import com.altomni.apn.user.service.mapper.customconfig.JobFormConfigMapper;
import com.altomni.apn.user.service.mapper.customconfig.JsonMapperUtil;
import com.altomni.apn.user.service.mapper.customconfig.SystemConfigDefaultMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Optional;

@Service
public class JobFormConfigServiceImpl implements JobFormConfigService {
    final static Category category = Category.JOB_FORM;
    private static final String FIELD_RECRUITMENT_PROCESS = "application";
    @Resource
    private JobFormConfigRepository jobFormConfigRepository;

    @Resource
    JobFormConfigMapper jobFormConfigMapper;
    @Resource
    private SystemConfigDefaultMapper systemConfigDefaultMapper;

    @Resource
    SystemConfigDefaultService systemConfigDefaultService;

    @Resource
    ApplicationService applicationService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public BaseConfig getFormConfigByRecruitmentProcessId(Long recruitmentProcessId) {
        if (recruitmentProcessId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETFORMCONFIGBYRECRUITMENTPROCESSID_RECRUITMENTPROCESSIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }


        Optional<JobFormConfig> result = Optional.ofNullable(jobFormConfigRepository.findByRecruitmentProcessId(recruitmentProcessId));
        if (result.isPresent() && StringUtils.isNotEmpty(result.get().getCustomConfig())) {
            JobFormConfigDTO jobFormConfigDTO = jobFormConfigMapper.toDto(result.get());
            updateCustomFieldProperty(jobFormConfigDTO.getCustomConfig());
            return jobFormConfigDTO;
        } else {
            ConfigSubcategory subCategory = getJobFormSubCategory(recruitmentProcessId);
            return systemConfigDefaultMapper.toDto(systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(category, subCategory));
        }
    }

    @Override
    public BaseConfig getFormConfigByRecruitmentProcessIdForPrivateJob(Long recruitmentProcessId) {
        if (recruitmentProcessId == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETFORMCONFIGBYRECRUITMENTPROCESSID_RECRUITMENTPROCESSIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        Optional<JobFormConfig> result = Optional.ofNullable(jobFormConfigRepository.findByRecruitmentProcessId(recruitmentProcessId));
        if (result.isPresent() && StringUtils.isNotEmpty(result.get().getPrivateJobCustomConfig())) {
            JobFormConfigDTO jobFormConfigDTO = jobFormConfigMapper.toDtoForPrivateJob(result.get());
            updateCustomFieldProperty(jobFormConfigDTO.getCustomConfig());
            return jobFormConfigDTO;
        } else {
            ConfigSubcategory subCategory = getJobFormSubCategory(recruitmentProcessId);
            if (subCategory == ConfigSubcategory.JOB_FORM_FTE){
                subCategory = ConfigSubcategory.PRIVATE_JOB_FORM_FTE;
            } else if (subCategory == ConfigSubcategory.JOB_FORM_CONTRACT){
                subCategory = ConfigSubcategory.PRIVATE_JOB_FORM_CONTRACT;
            } else if (subCategory == ConfigSubcategory.JOB_FORM_MSP){
                subCategory = ConfigSubcategory.PRIVATE_JOB_FORM_MSP;
            }
            return systemConfigDefaultMapper.toDto(systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(category, subCategory));
        }
    }

    private ConfigSubcategory getJobFormSubCategory(Long recruitmentProcessId) {
        ResponseEntity<RecruitmentProcessVO> responseEntity = applicationService.getRecruitmentProcessById(recruitmentProcessId);

        if (responseEntity.getStatusCode() != HttpStatus.OK) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETJOBFORMSUBCATEGORY_RESPONSESTATUSNOTOK.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        RecruitmentProcessVO recruitmentProcessVO = responseEntity.getBody();
        if (recruitmentProcessVO == null || recruitmentProcessVO.getJobType() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETJOBFORMSUBCATEGORY_RESPONSESTATUSNOTOK.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        JobType jobType = recruitmentProcessVO.getJobType();
        if (jobType != JobType.OTHERS) {
            return ConfigSubcategory.fromJobType(jobType);
        }

        TenantUserTypeEnum tenantUserTypeEnum = SecurityUtils.getUserType();
        return ConfigSubcategory.getFormEnumByTenantUserTypeEnum(tenantUserTypeEnum);
    }


    @Override
    public JobFormConfigDTO saveJobFormConfig(JobFormConfigDTO jobFormConfigDTO, Long recruitmentProcessId) {
        JobFormConfig jobFormConfig = jobFormConfigRepository.findByRecruitmentProcessId(recruitmentProcessId);
        if (jobFormConfig != null) {
            jobFormConfig.setCustomConfig(JsonMapperUtil.customFieldListToJsonString(jobFormConfigDTO.getCustomConfig()));
        } else {
            jobFormConfig = jobFormConfigMapper.toEntity(jobFormConfigDTO);
            jobFormConfig.setRecruitmentProcessId(recruitmentProcessId);
        }

        jobFormConfigRepository.saveAndFlush(jobFormConfig);
        jobFormConfigDTO = jobFormConfigMapper.toDto(jobFormConfig);

        return jobFormConfigDTO;
    }

    @Override
    public JobFormConfigDTO savePrivateJobFormConfig(JobFormConfigDTO jobFormConfigDTO, Long recruitmentProcessId) {
        JobFormConfig jobFormConfig = jobFormConfigRepository.findByRecruitmentProcessId(recruitmentProcessId);
        if (jobFormConfig != null) {
            jobFormConfig.setPrivateJobCustomConfig(JsonMapperUtil.customFieldListToJsonString(jobFormConfigDTO.getCustomConfig()));
        } else {
            jobFormConfig = jobFormConfigMapper.toEntity(jobFormConfigDTO);
            jobFormConfig.setPrivateJobCustomConfig(jobFormConfig.getCustomConfig());
            jobFormConfig.setCustomConfig(null);
            jobFormConfig.setRecruitmentProcessId(recruitmentProcessId);
        }

        jobFormConfigRepository.saveAndFlush(jobFormConfig);
        jobFormConfigDTO = jobFormConfigMapper.toDto(jobFormConfig);

        return jobFormConfigDTO;
    }

    private void updateCustomFieldProperty(List<CustomFormField> customConfigFields) {
        Long count = applicationService.countMyRecruitmentProcess().getBody();
        if (count > 1) { // only hidden the recruitmentproces field when over 1 recruitment process
            return;
        }

        customConfigFields.forEach(customColumnField -> {
            if (FIELD_RECRUITMENT_PROCESS == customColumnField.getField()) {
                customColumnField.setVisible(false);
            }
        });
    }

}
