package com.altomni.apn.user.repository.user;

import com.altomni.apn.common.domain.user.UserDeliveryCountryRelation;
import com.altomni.apn.common.domain.user.UserDeliveryProcessRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Set;


@Repository
public interface UserDeliveryProcessRelationRepository extends JpaRepository<UserDeliveryProcessRelation, Long> {
    List<UserDeliveryProcessRelation> findAllByUserId(Long userId);

    List<UserDeliveryProcessRelation> findAllByUserIdAndTop(Long userId, Boolean top);

    List<UserDeliveryProcessRelation> findAllByUserIdIn(Collection<Long> userIds);

    List<UserDeliveryProcessRelation> findAllByUserIdInAndTop(Collection<Long> userIds, Boolean top);
}
