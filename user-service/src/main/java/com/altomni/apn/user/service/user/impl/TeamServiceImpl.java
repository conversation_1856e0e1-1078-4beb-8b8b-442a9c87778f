package com.altomni.apn.user.service.user.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.QUser;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.user.QTeamUser;
import com.altomni.apn.user.domain.user.Team;
import com.altomni.apn.user.repository.user.TeamRepository;
import com.altomni.apn.user.service.dto.user.UserInTeam;
import com.altomni.apn.user.service.user.TeamService;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class TeamServiceImpl implements TeamService {

    @Resource
    private JPAQueryFactory queryFactory;

    @Resource
    private TeamRepository teamRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public Team create(Team team) {
        team.setTenantId(SecurityUtils.getTenantId());
        if (teamRepository.findByNameAndTenantId(team.getName(), team.getTenantId()).isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_TEAM_CREATE_TEAMEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return teamRepository.saveAndFlush(team);
    }

    @Override
    public Team update(Team team) {
        team.setTenantId(SecurityUtils.getTenantId());
        teamRepository.findByNameAndTenantId(team.getName(), team.getTenantId()).ifPresent(t -> {
            if (!t.getId().equals(team.getId())) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_TEAM_CREATE_TEAMEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
            }
        });
        return teamRepository.saveAndFlush(team);
    }

    @Override
    public Team get(Long teamId) {
        Team team = teamRepository.findById(teamId).orElseThrow();
        return populateTeam(team);
    }

    private Team populateTeam(Team team) {
        if (team == null) {
            return null;
        }
        if (!team.getTenantId().equals(SecurityUtils.getTenantId())) {
            return null;
        }
        QTeamUser teamUser = QTeamUser.teamUser;
        QUser user = QUser.user;
        BooleanExpression usersExp = user.id.in(
            JPAExpressions.select(teamUser.userId).from(teamUser).where(teamUser.teamId.eq(team.getId())))
            .and(user.activated.eq(true));
        team.users = queryFactory.select(Projections.constructor(UserInTeam.class, user.id, user.email, user.username, user.firstName, user.lastName))
            .from(user).where(usersExp).fetch();
        return team;
    }

    @Override
    public List<Team> getByTenantId(Long tenantId) {
        List<Team> teams = teamRepository.findAllByTenantId(tenantId);
        return teams.stream().map(this::populateTeam).collect(Collectors.toList());
    }

    @Override
    public void delete(Long teamId) {
        Team team = teamRepository.findById(teamId).orElseThrow();
        if (team == null) {
            return;
        }
        if (!team.getTenantId().equals(SecurityUtils.getTenantId())) {
            return;
        }

        QTeamUser teamUser = QTeamUser.teamUser;
        BooleanExpression expression = teamUser.teamId.eq(teamId);
        queryFactory.delete(teamUser).where(expression).execute();
        teamRepository.deleteById(teamId);
    }

}
