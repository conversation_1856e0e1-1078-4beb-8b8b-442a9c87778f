package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.user.domain.customconfig.TenantPushRules;
import com.altomni.apn.user.domain.customconfig.TenantPushRulesType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data R2DBC repository for the SystemConfigDefault entity.
 */
@Repository
public interface TenantPushRulesRepository extends JpaRepository<TenantPushRules, Long> {
    List<TenantPushRules> getTenantPushRulesByTenantIdIsOrderByCreatedDateDesc(Long tenantId);

    @Query(" SELECT MIN(COALESCE(t.push_interval, :defaultValue)) FROM TenantPushRules t " +
            " LEFT JOIN TenantPushRulesUser u ON t.id = u.tenantPushRulesId" +
            " LEFT JOIN TenantPushRulesTeam tm ON t.id = tm.tenantPushRulesId" +
            " WHERE u.userId = :userId OR tm.teamId = :teamId")
    Long findMinIntervalWithNullHandlingByUserIdAndTeamId(
            @Param("userId") Long userId,
            @Param("teamId") Long teamId,
            @Param("defaultValue") Long defaultValue);

    @Query("SELECT DISTINCT rt FROM TenantPushRules t " +
            "JOIN t.tenantPushRulesTypeSet rt " +
            "WHERE EXISTS (SELECT 1 FROM t.tenantPushRulesUserSet u WHERE u.userId = :userId) " +
            "OR EXISTS (SELECT 1 FROM t.tenantPushRulesTeamSet tm WHERE tm.teamId = :teamId)")
    List<TenantPushRulesType> findTenantPushRulesTypeByUserIdAndTeamId(
            @Param("userId") Long userId,
            @Param("teamId") Long teamId);

}

