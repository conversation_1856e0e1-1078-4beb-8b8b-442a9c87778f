package com.altomni.apn.user.web.rest.permission;

import com.altomni.apn.common.config.PrivilegeName;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.repository.permission.PermissionPrivilegeRepository;
import com.altomni.apn.user.service.cache.CachePermissionWriteOnly;
import com.altomni.apn.user.service.dto.permission.PermissionRoleDTO;
import com.altomni.apn.user.service.permission.PermissionExtraRoleTeamService;
import com.altomni.apn.user.service.permission.PermissionRolePrivilegeService;
import com.altomni.apn.user.service.permission.PermissionRoleService;
import com.altomni.apn.user.service.permission.PermissionTenantModuleService;
import com.altomni.apn.user.service.xxljob.XxlJobService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionRolePrivilegeVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionRoleTeamVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionRoleTreeVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTenantInvolvedDataPermissionModuleVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * REST controller for managing permissions.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/roles")
public class PermissionRoleResource {

    @Resource
    private PermissionRoleService permissionRoleService;

    @Resource
    private PermissionPrivilegeRepository privilegeRepository;

    @Resource
    private PermissionRolePrivilegeService rolePrivilegeService;

    @Resource
    private PermissionExtraRoleTeamService permissionExtraRoleTeamService;

    @Resource
    private CachePermissionWriteOnly cachePermissionWriteOnly;

    @Resource
    private PermissionTenantModuleService permissionTenantModuleService;

    @Resource
    private XxlJobService xxlJobService;

    @PostMapping("")
    public ResponseEntity<PermissionRoleDTO> createRole(@RequestBody PermissionRoleDTO roleDTO){
        log.info("({}) REST request to create a role", SecurityUtils.getCurrentUserLogin(), roleDTO);
        return ResponseEntity.ok(permissionRoleService.create(roleDTO));
    }

    @PutMapping("")
    public ResponseEntity<PermissionRoleDTO> updateRole(@RequestBody PermissionRoleDTO roleDTO){
        log.info("({}) REST request to update a role", SecurityUtils.getCurrentUserLogin(), roleDTO);
        return ResponseEntity.ok(permissionRoleService.update(roleDTO));
    }

    @PrivilegeName(value = "Get Roles")
    @GetMapping("")
    public ResponseEntity<List<PermissionRoleDTO>> getRoles(){
        log.info("({}) REST request to get roles", SecurityUtils.getCurrentUserLogin());
        return ResponseEntity.ok(permissionRoleService.findByTenantId());
    }

    @PrivilegeName(value = "Get Roles Tree")
    @GetMapping("/tree")
    public ResponseEntity<List<PermissionRoleTreeVM>> getRolesTree(){
        log.info("({}) REST request to get roles tree", SecurityUtils.getCurrentUserLogin());
        List<PermissionRoleTreeVM> permissionRoleTreeVMS = permissionRoleService.findByTenantId()
                .stream()
                .map(role -> new PermissionRoleTreeVM(role.getId(), role.getName())).collect(Collectors.toList());
        return ResponseEntity.ok(permissionRoleTreeVMS);
    }

    @PrivilegeName(value = "Get Privileges By Role")
    @GetMapping("/{roleId}/privileges")
    public ResponseEntity<List<Long>> getPrivilegesByRoleId(@PathVariable("roleId") Long roleId){
        log.info("({}) REST request to get privileges by role: {}", SecurityUtils.getCurrentUserLogin(), roleId);
        List<Long> privilegeIds = privilegeRepository.findAllByRoleId(roleId)
                .stream()
                .map(privilege -> privilege.getId())
                .collect(Collectors.toList());
        return ResponseEntity.ok(privilegeIds);
    }

    @GetMapping("/{roleId}/data-permissions")
    public ResponseEntity<PermissionRoleTeamVM> getDataPermissionsByRoleId(@PathVariable("roleId") Long roleId){
        log.info("({}) REST request to get privileges by role: {}", SecurityUtils.getCurrentUserLogin(), roleId);
        return ResponseEntity.ok(permissionExtraRoleTeamService.findPermissionByRoleId(roleId));
    }

    @PostMapping("/{roleId}/privileges")
    public ResponseEntity<Void> setRolePrivileges(@PathVariable("roleId") Long roleId, @RequestBody PermissionRolePrivilegeVM permissionRolePrivilegeVM){
        log.info("({}) REST request to set role privilege", SecurityUtils.getCurrentUserLogin());
        permissionRolePrivilegeVM.setRoleId(roleId);
        rolePrivilegeService.update(permissionRolePrivilegeVM);
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            xxlJobService.createOrUpdateUnSubmittedCandidatesForTeam(SecurityUtils.getTenantId());
        });
        return ResponseEntity.ok().build();
    }

    @PostMapping("/{roleId}/data-permissions")
    public ResponseEntity<Void> setRoleDataPermissions(@PathVariable("roleId") Long roleId, @RequestBody PermissionRoleTeamVM permissionRoleTeamVM){
        log.info("({}) REST request to set role data permission", SecurityUtils.getCurrentUserLogin());
        List<Long> userIds = permissionExtraRoleTeamService.saveRoleDataPermission(roleId, permissionRoleTeamVM);
        userIds.forEach(userId ->{
            cachePermissionWriteOnly.deleteDataPermissionCacheByUserId(userId);
            cachePermissionWriteOnly.deleteClientContactDataPermissionCacheByUserId(userId);
            cachePermissionWriteOnly.deleteReportDataPermissionCacheByUserId(userId);
            cachePermissionWriteOnly.deleteHomeAndCalendarDataPermissionCacheByUserId(userId);
            cachePermissionWriteOnly.deleteCandidatePipelineManagementPermissionCacheByUserId(userId);
            cachePermissionWriteOnly.deleteChinaInvoicingDataPermissionCacheByUserId(userId);
        });
        return ResponseEntity.ok().build();
    }

    @PutMapping("/{roleId}/active")
    public ResponseEntity<Void> activeRole(@PathVariable("roleId") Long roleId){
        log.info("({}) REST request to active the role {}", SecurityUtils.getCurrentUserLogin(), roleId);
        permissionRoleService.updateActiveStatus(roleId, Boolean.TRUE);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/{roleId}/count/active-users")
    public ResponseEntity<Integer> countActiveUsersByRole(@PathVariable("roleId") Long roleId){
        log.info("({}) REST request to count active user by role {}", SecurityUtils.getCurrentUserLogin(), roleId);
        return ResponseEntity.ok(permissionRoleService.countActiveUsersByRoleId(roleId));
    }

    /**
     *
     * @param roleId
     * @return the number of users under this role
     */
    @PutMapping("/{roleId}/inactive")
    public ResponseEntity<Integer> inactiveRole(@PathVariable("roleId") Long roleId){
        log.info("({}) REST request to inactive the role {}", SecurityUtils.getCurrentUserLogin(), roleId);
        return ResponseEntity.ok(permissionRoleService.updateActiveStatus(roleId, Boolean.FALSE));
    }

    @GetMapping("/my-involved-modules")
    public ResponseEntity<List<PermissionTenantInvolvedDataPermissionModuleVM>> getMyInvolvedDataPermissionModules() {
        log.debug("REST request to get modules of getting involved in data permission for current tenant", SecurityUtils.getTenantId());
        return ResponseEntity.ok(permissionTenantModuleService.findModulesByCurrentTenant());
    }
}
