package com.altomni.apn.user.service.dto.user;

import com.altomni.apn.common.domain.enumeration.company.IndustryType;
import com.altomni.apn.common.dto.user.UserBriefDTO;
import com.altomni.apn.common.domain.enumeration.StaffSizeType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel
public class TenantVO implements Serializable
{
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "data id")
    private Long id;

    @ApiModelProperty(value = "tenant name")
    private String name;

    @ApiModelProperty(value = "industry")
    private IndustryType industry;

    @ApiModelProperty(value = "website")
    private String  website;

    @ApiModelProperty(value = "organization name")
    private String organizationName;

    @ApiModelProperty(value = "staff size type")
    private StaffSizeType staffSizeType;


    @ApiModelProperty(value = "tenant founded date")
    private Instant foundedDate;

    @ApiModelProperty(value = "tenant admins")
    private List<UserBriefDTO> admin;


    @ApiModelProperty(value = "detail address")
    private TenantAddressVO address;

    @ApiModelProperty(value = "data id")
    private String description;

    @ApiModelProperty(value = "tenant bulk credit")
    private Integer bulkCredit;

    @ApiModelProperty(value = "tenant monthly credit")
    private Integer monthlyCredit;

    @ApiModelProperty(value = "tenant monthly credit will be update")
    private Integer updateMonthlyCredit;

    @ApiModelProperty(value = "used bulk credit ")
    private Integer bulkUsedCredit;


    @ApiModelProperty(value = "monthly used  credit ")
    private Integer monthlyUsedCredit;

    @ApiModelProperty(value = "created date")
    private Instant createdDate;

    @ApiModelProperty(value = "last modified  date")
    private Instant lastModifiedDate;

    @ApiModelProperty(value = "name for create tenant")
    private String  createdBy;

    @ApiModelProperty(value = "tenant logo ")
    private String  logo;

    @ApiModelProperty(value = "tenant status")
    private Integer  status;

    @ApiModelProperty(value = "tenant email")
    private String tenantEmail;

    @ApiModelProperty(value = "tenant phone")
    private String tenantPhone;






}
