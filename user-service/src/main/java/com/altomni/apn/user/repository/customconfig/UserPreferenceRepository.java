package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.user.domain.customconfig.UserPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * Spring Data R2DBC repository for the UserPreference entity.
 */
@Repository
public interface UserPreferenceRepository extends JpaRepository<UserPreference, Long> {
    List<UserPreference> findAllByIdIn(List<Long> ids);

    UserPreference findByUserId(Long userId);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET dashboard_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateDashboardColumnConfigByUserId(Long userId, String json);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET invoice_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateInvoiceColumnConfigByUserId(Long userId, String json);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET expense_report_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateExpenseReportColumnConfigByUserId(Long userId, String json);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET company_current_contractor_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateCompanyCurrentContractorColumnConfigByUserId(Long userId, String companyCurrentContractorColumnConfig);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET recruitment_process_id = null WHERE recruitment_process_id = ?1", nativeQuery = true)
    void disableRecruitmentProcessByRecruitmentProcessId(Long recruitmentProcessId);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET company_fte_bd_report_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateCompanyFteBdReportColumnConfigByUserId(Long userId, String companyFteBdReportColumnConfig);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET invoicing_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateInvoicingColumnConfigByUserId(Long userId, String json);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET calendar_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateCalendarColumnConfigByUserId(Long userId, String json);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET user_search_all_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateUserSearchAllColumnConfigByUserId(Long userId, String userSearchColumnConfig);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET user_search_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateUserSearchColumnConfigByUserId(Long userId, String userSearchColumnConfig);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET monthly_revenue_detail_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateMonthlyRevenueDetailColumnConfigByUserId(Long userId, String json);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET quarterly_new_hires_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateQuarterlyNewHiresColumnConfigByUserId(Long userId, String json);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET quarterly_renewals_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateQuarterlyRenewalsColumnConfigByUserId(Long userId, String json);

    @Modifying
    @Transactional
    @Query(value = "update user_preference SET quarterly_offboarding_column_config = ?2 WHERE user_id = ?1", nativeQuery = true)
    void updateQuarterlyOffboardingColumnConfigByUserId(Long userId, String json);
}

