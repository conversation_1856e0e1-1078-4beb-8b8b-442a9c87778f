package com.altomni.apn.user.service.customconfig;


import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.user.service.dto.customconfig.PageConfig;
import com.altomni.apn.user.service.dto.customconfig.PipelineColumnPreferenceDTO;

public interface PipelineColumnConfigService {


    BaseConfig findByUserId(Long userId);


    BaseConfig getTalentPipelineColumnConfigByTenantId(Long tenantId);

    BaseConfig updatePipelineColumnConfig(PipelineColumnPreferenceDTO pipelineColumnPreferenceDTO, Long userId);

    PageConfig updatePipelinePageConfig(PageConfig pageConfig);


}
