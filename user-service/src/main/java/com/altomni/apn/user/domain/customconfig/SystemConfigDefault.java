package com.altomni.apn.user.domain.customconfig;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.CategoryConverter;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategoryConverter;
import com.fasterxml.jackson.annotation.JsonRawValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A SystemConfigDefault.
 */
@ApiModel(description = "SystemConfigDefault is used to default job form and job column")
@Data
@Entity
@Table(name = "system_config_default")
public class SystemConfigDefault extends AbstractAuditingEntity implements Serializable, Cloneable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull(message = "must not be null")
    @Convert(converter = CategoryConverter.class)
    @Column(name = "category")
    private Category category;

    @Convert(converter = ConfigSubcategoryConverter.class)
    @Column(name = "subcategory")
    private ConfigSubcategory subcategory;

    @ApiModelProperty("store raw json config and convert it into class in DTO")
    @Column(name = "default_config", columnDefinition = "json")
    //@Convert(converter = JsonNodeConverter.class)
    @JsonRawValue
    private String defaultConfig;



}
