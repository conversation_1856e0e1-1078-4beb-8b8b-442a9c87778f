package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.Team;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@SuppressWarnings("unused")
@Repository
public interface TeamRepository extends JpaRepository<Team, Long> {

    Optional<Team> findByNameAndTenantId(String name, Long tenantId);

    List<Team> findAllByTenantId(Long tenantId);

    List<Team> findAllByLeaderUserId(Long leaderUserId);

}
