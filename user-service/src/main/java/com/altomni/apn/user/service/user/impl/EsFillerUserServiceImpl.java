package com.altomni.apn.user.service.user.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.EsFillerMqBaseProperties;
import com.altomni.apn.common.config.application.LarkProperties;
import com.altomni.apn.common.config.env.JobdivaRabbitProperties;
import com.altomni.apn.common.domain.dict.EnumUserResponsibility;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.canal.FailReasonEnum;
import com.altomni.apn.common.domain.enumeration.canal.SyncIdTypeEnum;
import com.altomni.apn.common.domain.enumeration.dict.EnumStatus;
import com.altomni.apn.common.domain.user.Tenant;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.enumeration.enums.JobdivaDataSyncTypeEnum;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.service.canal.CanalService;
import com.altomni.apn.common.service.rabbitmq.RabbitMqService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.repository.permission.PermissionTeamRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.permission.PermissionUserTeamDTO;
import com.altomni.apn.user.service.management.ManagementService;
import com.altomni.apn.user.service.user.EsFillerUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATE_PATTERN;
import static com.altomni.apn.common.constants.ResponsibilityConstants.CREATED_BY;
import static com.altomni.apn.common.utils.DateUtil.YYYY_MM_DD_T_HH_MM_SS_SSS_Z;

@Slf4j
@RefreshScope
@Service("esFillerUserService")
public class EsFillerUserServiceImpl implements EsFillerUserService {

    @Resource
    private UserRepository userRepository;

    @Resource
    private PermissionTeamRepository permissionTeamRepository;

    @Resource
    private ManagementService managementService;

    @Resource
    private RabbitMqService rabbitMqService;

    @Resource
    private CanalService canalService;

    @Resource
    private LarkProperties larkProperties;

    @Resource
    private EnumCommonService enumCommonService;

    @Resource
    private EsFillerMqBaseProperties esFillerMqBaseProperties;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Resource(name = "jobdivaRabbitTemplate")
    private RabbitTemplate rabbitTemplate;

    @Resource
    private JobdivaRabbitProperties jobdivaRabbitProperties;

    @Override
    public void extractDbDataToMq(Collection<Long> dataIds, int priority) {
        List<JSONObject> userProfiles = userRepository.findAllById(dataIds).stream().map(user -> buildUserProfile(user, 0)).toList();
        log.info("[EsFillerUserService: syncUserToMQ @{}] userProfiles length: {}, ids: {}", SecurityUtils.getUserId(), userProfiles.size(), dataIds);
        for (JSONObject userProfile : userProfiles) {
            if (Objects.nonNull(userProfile)){
                Long id = userProfile.getLong("_id");
                try {
                    if (id > 5) {
                        //id <= 5 不需要同步，属于系统内置用户
                        rabbitMqService.saveDataProfile(JSONUtil.toJsonStr(userProfile), priority, SyncIdTypeEnum.USER);
                    }
                    userRepository.updateUserLastSyncTime(id, Instant.now());
                    canalService.deleteByTaskIdAndType(id, SyncIdTypeEnum.USER);
                    log.info("[EsFillerUserService: syncUserToMQ @{}] save user to MQ success, id: {}", SecurityUtils.getUserId(), id);
                } catch (Exception e) {
                    log.error("[EsFillerUserService: syncUserToMQ @{}] save user to MQ error, id: {}", SecurityUtils.getUserId(), id);
                    canalService.insertAll(CollUtil.newArrayList(id), SyncIdTypeEnum.USER, FailReasonEnum.ERROR, e.getMessage(), priority);
                    String message = "Send User to MQ Error" +
                            "\n\tUSER ID: " + id +
                            "\n\tError: " +
                            "\n\t" + ExceptionUtils.getStackTrace(e);
                    NotificationUtils.sendAlertToLark(larkProperties.getLarkWebhookKey(), larkProperties.getLarkWebhookUrl(), message);
                }
            }
        }
    }

    @Override
    public void buildJsonToJobdivaMq(Collection<Long> userIds, int priority) {
        userRepository.findAllById(userIds).forEach(user -> {
            log.info("[buildJonsToJobdivaMq: syncDataToMQ @{}] userId {} to rabbitMQ", SecurityUtils.getUserId(), user.getId());
            try {
                JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
                JSONObject jsonObject = new JSONObject(jsonConfig);
                jsonObject.put("type", JobdivaDataSyncTypeEnum.USER);
                jsonObject.put("entity", buildApnToJobdivaProfile(user));
                rabbitTemplate.convertAndSend(jobdivaRabbitProperties.getApnToJobdivaExchange(), jobdivaRabbitProperties.getApnToJobdivaRoutingKey(), JSONUtil.toJsonStr(jsonObject), message -> {
                    message.getMessageProperties().setPriority(priority);
                    return message;
                });
            } catch (Exception e) {
                log.error("[buildJonsToJobdivaMq: syncDataToMQ @{}] send {} to rabbitMQ error: {}", SecurityUtils.getUserId(), user.getId(), e.getMessage());
            }
        });
    }

    private JSONObject buildApnToJobdivaProfile(User user) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("id", user.getId());
        jsonObject.put("tenantId", user.getTenantId());
        jsonObject.put("firstName", user.getFirstName());
        jsonObject.put("lastName", user.getLastName());
        jsonObject.put("email", user.getEmail());
        return jsonObject;
    }

    private JSONObject buildUserProfile(User user, int deep) {
        JSONConfig jsonConfig = JSONConfig.create().setDateFormat(YYYY_MM_DD_T_HH_MM_SS_SSS_Z);
        cn.hutool.json.JSONObject request = new cn.hutool.json.JSONObject(jsonConfig);
        try {
            if (deep > 0){
                Thread.sleep(2000);
            }
            cn.hutool.json.JSONObject es = translateEs(user);
            //不需要回填没有 route key
            request.put("_id", user.getId());
            request.put("_tenant_id", String.valueOf(user.getTenantId()));
            request.put("_type", "apn_user");
            request.put("_source", es);
        }catch (Exception e){
            log.error("[EsFillerUserService: syncUserToMQ @{}] buildUserProfile error, id: {}, error: {}", SecurityUtils.getUserId(), user.getId(), ExceptionUtils.getStackTrace(e));
            if (deep < esFillerMqBaseProperties.getRetryThreshold()){
                request = buildUserProfile(user, deep + 1);
            }else{
                canalService.insertAll(CollUtil.newArrayList(user.getId()), SyncIdTypeEnum.USER, FailReasonEnum.ERROR, e.getMessage(), 1);
                request = null;
                String message = "Build User Profile Error" +
                        "\n\tUser ID: " + user.getId() +
                        "\n\tUser Name: " + CommonUtils.formatFullName(user.getFirstName(), user.getLastName()) +
                        "\n\tError: " +
                        "\n\t" + ExceptionUtils.getStackTrace(e);
                NotificationUtils.sendAlertToLark(larkProperties.getLarkWebhookKey(), larkProperties.getLarkWebhookUrl(), message);
            }
        }
        return request;
    }

    private JSONObject translateEs(User user) {
        JSONObject esDocumentJson = new JSONObject();
        //createdDate
        esDocumentJson.put("createdDate", user.getCreatedDate());
        //contacts
        addContacts(esDocumentJson, user);
        //firstName
        esDocumentJson.put("firstName", user.getFirstName());
        //lastName
        esDocumentJson.put("lastName", user.getLastName());
        //lastModifiedDate
        esDocumentJson.put("lastModifiedDate", user.getLastModifiedDate());
        //user responsibility
        List<EnumUserResponsibility> enumUserResponsibility = enumCommonService.findAllEnumUserResponsibility();
        Map<String, EnumUserResponsibility> enumUserResponsibilityMap = enumUserResponsibility.stream().filter(o -> EnumStatus.ACTIVE.equals(o.getStatus())).collect(Collectors.toMap(EnumUserResponsibility::getLabel, o -> o));
        if (enumUserResponsibilityMap.containsKey(CREATED_BY)) {
            JSONObject createdByObject = new JSONObject();
            String[] createdBy = user.getCreatedBy().split(",");
            if (createdBy.length > 1 && NumberUtil.isNumber(createdBy[0]) && NumberUtil.parseNumber(createdBy[0]).intValue() > 0) {
                createdByObject.put("id", createdBy[0]);
                //user 和 talent 公用es key
                esDocumentJson.put(enumUserResponsibilityMap.get(CREATED_BY).getTalentEsKey(), CollUtil.newArrayList(createdByObject));
            }
        }
        //notes
        if (StrUtil.isNotBlank(user.getNote())) {
            JSONObject note = new JSONObject();
            note.put("text", user.getNote());
            esDocumentJson.put("notes", CollUtil.newArrayList(note));
        }
        //experiences
        JSONObject experience = new JSONObject();
        //current
        experience.put("current", user.isActivated());
        //startDate
        experience.put("startDate", user.getCreatedDate().atZone(ZoneId.of("UTC")).toLocalDate().format(DateTimeFormatter.ofPattern(NORM_DATE_PATTERN)));
        // endDate user 取消激活日期, 取消激活的user填写这个字段，激活的user不填
        if (!user.isActivated()) {
            if (ObjectUtil.isNotNull(user.getCancellationTime())) {
                experience.put("endDate", user.getCancellationTime().atZone(ZoneId.of("UTC")).toLocalDate().format(DateTimeFormatter.ofPattern(NORM_DATE_PATTERN)));
            } else {
                //历史数据
                if (ObjectUtil.isNotNull(user.getLastModifiedDate())) {
                    experience.put("endDate", user.getLastModifiedDate().atZone(ZoneId.of("UTC")).toLocalDate().format(DateTimeFormatter.ofPattern(NORM_DATE_PATTERN)));
                }
            }
        }
        //title
        if (StrUtil.isNotBlank(user.getJobTitle())) {
            experience.put("title", user.getJobTitle());
        }
        //companyName 租户公司的公司名
        Tenant tenant = managementService.queryTenant(user.getTenantId()).getBody();
        if (tenant == null || tenant.getId() == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSION_FINDDATAPERMISSIONBYUSERID_TENANTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        experience.put("companyName", tenant.getName());
        //department 主 Team name
        Set<PermissionUserTeamDTO> permissionUserTeamDTOS = permissionTeamRepository.findTeamVOsByUserId(user.getId());
        if (CollUtil.isNotEmpty(permissionUserTeamDTOS)) {
            permissionUserTeamDTOS.stream().filter(PermissionUserTeamDTO::getIsPrimaryTeam).findFirst().ifPresent(dto -> {
                experience.put("department", dto.getName());
                //set primaryTeamId
                esDocumentJson.put("primaryTeamId", dto.getId().toString());
            });
            //设置主team, 副team, 以及所有的父类
            //set secondaryTeamIds
            List<String> secondaryTeamIds = permissionUserTeamDTOS.stream().filter(dto -> !dto.getIsPrimaryTeam()).map(PermissionUserTeamDTO::getId).map(StrUtil::toString).toList();
            if (CollUtil.isNotEmpty(secondaryTeamIds)) {
                esDocumentJson.put("secondaryTeamIds", secondaryTeamIds);
            }
            List<PermissionTeam> teamList = permissionTeamRepository.getAncestorTeams(permissionUserTeamDTOS.stream().map(PermissionUserTeamDTO::getId).toList());
            if (CollUtil.isNotEmpty(teamList)) {
                esDocumentJson.put("ancestralTeamIds", teamList.stream().map(PermissionTeam::getId).map(StrUtil::toString).toList());
            }
        }
        esDocumentJson.put("experiences", CollUtil.newArrayList(experience));
        return esDocumentJson;
    }

    private void addContacts(JSONObject esDocumentJson, User user) {
        List<JSONObject> contactList = new ArrayList<>();
        if (StrUtil.isNotBlank(user.getEmail())) {
            JSONObject contact = new JSONObject();
            contact.put("contact", user.getEmail());
            contact.put("type", ContactType.EMAIL);
            contactList.add(contact);
        }
        if (StrUtil.isNotBlank(user.getPhone())) {
            JSONObject contact = new JSONObject();
            contact.put("contact", user.getPhone());
            contact.put("type", ContactType.PHONE);
            contactList.add(contact);
        }
        if (CollUtil.isNotEmpty(contactList)) {
            esDocumentJson.put("contacts", contactList);
        }
    }

}
