package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.TalentFormConfig;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.TalentFormConfigRepository;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.application.ApplicationService;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.customconfig.TalentFormConfigService;
import com.altomni.apn.user.service.dto.customconfig.SystemConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.TalentFormConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.TalentPreferenceConfigDTO;
import com.altomni.apn.user.service.mapper.customconfig.JsonMapperUtil;
import com.altomni.apn.user.service.mapper.customconfig.SystemConfigDefaultMapper;
import com.altomni.apn.user.service.mapper.customconfig.TalentFormConfigMapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Optional;

@Service
public class TalentFormConfigServiceImpl implements TalentFormConfigService {
    final static Category TALENT_FORM = Category.TALENT_FORM;
    final static ConfigSubcategory TALENT_DEFAULT_FORM = ConfigSubcategory.TALENT_FORM_DEFAULT;
    @Resource
    private TalentFormConfigRepository talentFormConfigRepository;

    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    TalentFormConfigMapper talentFormConfigMapper;
    @Resource
    private SystemConfigDefaultMapper systemConfigDefaultMapper;

    @Resource
    SystemConfigDefaultService systemConfigDefaultService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;



    @Override
    public String getFormConfigByTenantId(Long tenantId) {
        if(tenantId == null){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETFORMCONFIGBYTENANTID_INVALIDTENANTID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        Optional<TalentFormConfig> result = Optional.ofNullable(talentFormConfigRepository.findByTenantId(tenantId));
        Optional<UserPreference> userPreferenceResult = Optional.ofNullable(userPreferenceRepository.findByUserId(SecurityUtils.getUserId()));
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            if (result.isPresent() && StringUtils.isNotEmpty(result.get().getCustomConfig())) {
                TalentFormConfigDTO dto = talentFormConfigMapper.toDto(result.get());
                // 转换为 ObjectNode 并添加字段
                ObjectNode node = objectMapper.valueToTree(dto);
                //设置用户名称输入方式偏好， 默认true
                if (userPreferenceResult.isPresent() && userPreferenceResult.get().getSeparateNameInput() != null) {
                    node.put("separateNameInput", userPreferenceResult.get().getSeparateNameInput());
                }else {
                    node.put("separateNameInput", true);
                }
                return objectMapper.writeValueAsString(node);
            } else {
                SystemConfigDTO dto = systemConfigDefaultMapper.toDto(systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(TALENT_FORM, TALENT_DEFAULT_FORM));
                // 使用 Jackson 的 ObjectNode 动态添加字段
                ObjectNode node = objectMapper.valueToTree(dto);
                //设置用户名称输入方式偏好， 默认true
                if (userPreferenceResult.isPresent() && userPreferenceResult.get().getSeparateNameInput() != null) {
                    node.put("separateNameInput", userPreferenceResult.get().getSeparateNameInput());
                }else {
                    node.put("separateNameInput", true);
                }
                return objectMapper.writeValueAsString(node);
            }
        } catch (JsonProcessingException e) {
            throw new CustomParameterizedException("json transfer error!");
        }
    }

    @Override
    public void saveTalentColumnConfig(TalentPreferenceConfigDTO dto, Long userId) {
        if (ObjectUtil.isNull(dto.getSeparateNameInput())) {
            return;
        }
        UserPreference userPreference = userPreferenceRepository.findByUserId(userId);

        if (userPreference != null) {
            //更新分割候选人姓名输入偏好
            userPreference.setSeparateNameInput(dto.getSeparateNameInput());
        } else {
            userPreference = new UserPreference();
            userPreference.setUserId(userId);
            //更新分割候选人姓名输入偏好
            userPreference.setSeparateNameInput(dto.getSeparateNameInput());
        }
        userPreferenceRepository.save(userPreference);
    }

}
