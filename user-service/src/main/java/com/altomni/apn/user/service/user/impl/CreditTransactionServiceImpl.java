package com.altomni.apn.user.service.user.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.constants.Constants;
import com.altomni.apn.common.config.constants.ContactTypeConstants;
import com.altomni.apn.common.domain.enumeration.ContactType;
import com.altomni.apn.common.domain.enumeration.RecommendFeedbackReason;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.domain.talent.TalentContact;
import com.altomni.apn.common.dto.RecommendFeedback;
import com.altomni.apn.common.dto.http.HttpResponse;
import com.altomni.apn.common.dto.talent.*;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ErrorConstants;
import com.altomni.apn.common.errors.ExternalServiceInterfaceException;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.service.http.HttpService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.user.config.env.ApplicationProperties;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.enumeration.CreditType;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.domain.user.UserAccount;
import com.altomni.apn.user.repository.user.CreditTransactionRepository;
import com.altomni.apn.user.repository.user.UserAccountRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.user.CreditTransactionDTO;
import com.altomni.apn.user.service.talent.TalentService;
import com.altomni.apn.user.service.user.CreditTransactionService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class CreditTransactionServiceImpl implements CreditTransactionService {

    @Resource
    private CreditTransactionRepository creditTransactionRepository;

    @Resource
    private UserAccountRepository userAccountRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    private TalentService talentService;

    @Resource
    CommonRedisService commonRedisService;

    @Resource
    private HttpService httpService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Resource
    private ApplicationProperties applicationProperties;

    private static final String API_SOURCE_QUERY_TALENT = "query-talent";

    private static final String API_SOURCE_SEARCH_TALENTS = "search-talents";

    public static final String ENDPOINT_AI_SOURCING_UNLOCK_TALENT  = "/talent/api/v3/ai-sourcing/unlock-talent";

    @Override
    public List<CreditTransaction> findAllByTenantIdAndStatusAndSearchEsIdIn(Long tenantId, Status status, List<String> profileId) {
        return this.creditTransactionRepository.findAllByTenantIdAndStatusAndCommonDBSearchESIdIn(tenantId, status, profileId);
    }

    @Override
    public CreditTransaction findByTenantIdAndStatusAndTalentIdIs(Long tenantId, Status status, Long talentId) {
        return this.creditTransactionRepository.findByTenantIdAndStatusAndTalentIdIs(tenantId, status, talentId);
    }

    @Override
    @Transactional
    public CreditTransactionDTO create(CreditTransactionDTO creditTransactionDTO) {
        CreditTransaction exists = creditTransactionRepository.findByProfileIdAndTenantIdAndStatus(creditTransactionDTO.getProfileId(), SecurityUtils.getTenantId(), Status.Available);
        if ( exists != null) {
            log.debug("The talent with profile id " + creditTransactionDTO.getProfileId() + " already purchased");
            return convertEntity2DTO(exists);
        }

        CreditTransaction transaction = new CreditTransaction();
        if (creditTransactionDTO.getExistsContacts() != null && creditTransactionDTO.getExistsContacts().size() > 0) {
            // query common db compare talent contacts if has extra contacts
            TalentDTOV3 talent = talentService.queryTalentFromCommon(creditTransactionDTO.getProfileId(), creditTransactionDTO.getExistsContacts());
            if (talent == null) {
                throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREDITTRANSACTION_CREATE_TALENTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
            }
            transaction.setExistsContacts(String.valueOf(creditTransactionDTO.getExistsContacts()));
            transaction.setEsContacts(String.valueOf(talent.getContacts()));
            transaction.setApiSource(API_SOURCE_QUERY_TALENT);
        } else {
            transaction.setApiSource(API_SOURCE_SEARCH_TALENTS);
        }

        CreditType creditType = updateBalance();

        transaction.setCreditType(creditType);
        transaction.setCredit(Constants.PER_CREDIT);
        transaction.setTenantId(SecurityUtils.getTenantId());
        transaction.setUserId(SecurityUtils.getUserId());
        transaction.setProfileId(creditTransactionDTO.getProfileId());
        transaction.setStatus(Status.Available);
        CreditTransaction result = creditTransactionRepository.save(transaction);

        return convertEntity2DTO(result);
    }

    private CreditType updateBalance() {
        Long userId = SecurityUtils.getUserId();
        UserAccount exist = userAccountRepository.findByUserId(userId);
        if (exist == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREDITTRANSACTION_UPDATEBALANCE_USERACCOUNTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        Calendar calendar = Calendar.getInstance();
        Integer totalUsedMonthlyCredit = userRepository.findTotalMonthlyUsedCreditByUserId(userId, CreditType.MONTHLY.toDbValue(), calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1);
        if (totalUsedMonthlyCredit == null) {
            totalUsedMonthlyCredit = 0;
        }
        Integer totalUserBulkCredit = userRepository.findTotalUsedCreditByUserId(SecurityUtils.getUserId(), CreditType.BULK.toDbValue());
        if (totalUserBulkCredit == null) {
            totalUserBulkCredit = 0;
        }
        return updateUserBalance(exist, totalUsedMonthlyCredit, totalUserBulkCredit);
    }

    private CreditType updateUserBalance(UserAccount exist,Integer totalUsedMonthlyCredit,Integer totalUserBulkCredit) {
        if (exist.getMonthlyAmount() == null) {
            exist.setMonthlyAmount(0);
        }
        if (exist.getBulkCredit() == null) {
            exist.setBulkCredit(0);
        }
        if (exist.getMonthlyAmount() - totalUsedMonthlyCredit >= Constants.PER_CREDIT) {
            return CreditType.MONTHLY;
        } else if (exist.getBulkCredit() - totalUserBulkCredit >= Constants.PER_CREDIT) {
            return CreditType.BULK;
        } else {
            throw new CustomParameterizedException(ErrorConstants.USER_ACCOUNT_NO_AMOUNT);
        }
    }

    @Override
    public List<CreditTransactionDTO> findByTenantId(Long tenantId) {
        return creditTransactionRepository.findByTenantIdAndStatus(tenantId, Status.Available).stream().map(this::convertEntity2DTO).collect(Collectors.toList());
    }

    private CreditTransactionDTO convertEntity2DTO(CreditTransaction creditTransaction) {
        if (creditTransaction == null) {
            return null;
        }
        CreditTransactionDTO creditTransactionDTO = new CreditTransactionDTO();
        ServiceUtils.myCopyProperties(creditTransaction, creditTransactionDTO, CreditTransaction.UpdateSkipProperties);
        return creditTransactionDTO;
    }

    @Override
    public CreditTransaction findByProfileIdAndTenantIdAndStatus(String profileId, Long tenantId, Status status) {
        return creditTransactionRepository.findByProfileIdAndTenantIdAndStatus(profileId, tenantId, status);
    }

    @Override
    public List<CreditTransaction> findAllByTenantIdAndStatusAndProfileIdIn(Long tenantId, Status status, List<String> profileId) {
        return this.creditTransactionRepository.findAllByTenantIdAndStatusAndProfileIdIn(tenantId, status, profileId);
    }

    @Override
    public List<CreditTransaction> findByTenantIdAndStatus(Long tenantId, Status status) {
        return this.creditTransactionRepository.findByTenantIdAndStatus(tenantId, status);
    }

    @Override
    @Transactional
    //@GlobalTransactional
    public CreditTransactionDTO updateTalentId(CreditTransactionDTO creditTransactionDTO, Long tenantId) {
        if (ObjectUtil.isNull(creditTransactionDTO.getId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREDITTRANSACTION_UPDATETALENTID_IDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if (ObjectUtil.isNull(creditTransactionDTO.getTalentId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREDITTRANSACTION_UPDATETALENTID_TALENTIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        Optional<CreditTransaction> existing = creditTransactionRepository.findById(creditTransactionDTO.getId());
        if (!existing.isPresent()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREDITTRANSACTION_UPDATETALENTID_NOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        CreditTransaction creditTransaction = existing.get();
        if (!tenantId.equals(creditTransaction.getTenantId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREDITTRANSACTION_UPDATETALENTID_NOTPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        creditTransaction.setTalentId(creditTransactionDTO.getTalentId());
        creditTransaction.setLastModifiedDate(Instant.now());
        return convertEntity2DTO(creditTransactionRepository.save(creditTransaction));
    }

    @SneakyThrows
    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.SERIALIZABLE)
    public CreditTransactionDTO createCommonPoolCreditTransaction(CreditTransactionDTO creditTransactionDTO) {
        CreditTransaction exists = creditTransactionRepository.findByProfileIdAndTenantIdAndStatus(creditTransactionDTO.getProfileId(), SecurityUtils.getTenantId(), Status.Available);
        if (ObjectUtil.isNotEmpty(exists)) {
            log.debug("The talent with profile id " + creditTransactionDTO.getProfileId() + " already purchased");
            return convertEntity2DTO(exists);
        }
        CreditTransaction transaction = new CreditTransaction();
        TalentESConditionDTO condition = new TalentESConditionDTO();
        condition.setEsId(creditTransactionDTO.getProfileId());
        TalentESDocument talentESDocument = talentService.getTalentESDocument(condition);
        if(ObjectUtil.isNotEmpty(talentESDocument)){
            condition.setDomains(talentESDocument.getDomains());
        }
        String commonTalentContacts = talentService.searchContactsFromCommonPool(condition);
        List<TalentContact> contacts = JSONUtil.toList(JSONUtil.parseArray(commonTalentContacts), TalentContact.class);
        List<SuspectedDuplications> existTalent = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(contacts)) {
            //translate contacts type
            contacts.forEach(c -> {
                if (ContactTypeConstants.EXTRA_CONTACT_TYPES_PHONES.contains(c.getType())) {
                    c.setType(ContactType.PHONE);
                }
                if (ContactType.PRIMARY_EMAIL.equals(c.getType())) {
                    c.setType(ContactType.EMAIL);
                }
            });
            //查重
            transaction.setEsContacts(ObjectUtil.toString(JSONUtil.parseArray(contacts)));
            List<TalentContactDTO> contactDTOList = Convert.toList(TalentContactDTO.class, contacts.stream().sorted(Comparator.comparing(TalentContact::getSort)).collect(Collectors.toList()));
            TalentDTOV3 param = new TalentDTOV3();
            param.setContacts(contactDTOList);
            existTalent = talentService.checkContactExist(param).getBody();
        }
        transaction.setApiSource(API_SOURCE_SEARCH_TALENTS);
        CreditType creditType = updateBalance();
        transaction.setCreditType(creditType);
        transaction.setCredit(Constants.PER_CREDIT);
        transaction.setTenantId(SecurityUtils.getTenantId());
        transaction.setUserId(SecurityUtils.getUserId());
        transaction.setProfileId(creditTransactionDTO.getProfileId()); //TODO: to delete
        transaction.setCommonDBSearchESId(creditTransactionDTO.getProfileId());
        transaction.setStatus(Status.Available);
        CreditTransaction result = creditTransactionRepository.save(transaction);

        //unlock talent
        TalentUnlockListDto talentUnlockListDto = new TalentUnlockListDto();
        TalentUnlockDto talentUnlockDto = new TalentUnlockDto();
        talentUnlockDto.setId(creditTransactionDTO.getProfileId());
        talentUnlockDto.setTenantIds(CollUtil.newArrayList(String.valueOf(SecurityUtils.getTenantId())));
        talentUnlockListDto.setTalentUnlockDtoList(CollUtil.newArrayList(talentUnlockDto));
        unlockTalent(talentUnlockListDto);
        addRecommendFeedback(creditTransactionDTO.getRecommendFeedback());
        CreditTransactionDTO creditTransactionDTO1 = convertEntity2DTO(result);
        if (creditTransactionDTO1 != null && CollUtil.isNotEmpty(existTalent)) {
            creditTransactionDTO1.setSuspectedDuplicationsList(existTalent);
        }
        commonRedisService.delete(LoginUtil.USER_ACCOUNT_REDIS_EXPIRE_PREFIX + SecurityUtils.getUserId());
        return creditTransactionDTO1;
    }

    @Override
    public String createAiSourcingCreditTransaction(CreditTransactionDTO creditTransactionDTO) throws IOException {
//        String json = """
//                {
//                    "contacts": [
//                        {
//                            "type": "EMAIL",
//                            "contact": "<EMAIL>"
//                        },
//                        {
//                            "type": "PHONE",
//                            "contact": "+***********"
//                        },
//                        {
//                            "type": "LINKEDIN",
//                            "contact": "heike-pröhl-9152473b",
//                            "details": "http://www.linkedin.com/in/heike-pröhl-9152473b"
//                        }
//                    ]
//                }
//
//                """;
        String api = applicationProperties.getAiSourcingHost() + ENDPOINT_AI_SOURCING_UNLOCK_TALENT;
        JSONObject param = new JSONObject();
        param.put("talentId", creditTransactionDTO.getProfileId());
        param.put("tenantId", SecurityUtils.getTenantId());
        HttpResponse response = httpService.put(api, JSONUtil.toJsonStr(param));
        if (Objects.nonNull(response) && org.springframework.http.HttpStatus.OK.value() == response.getCode()) {
            return response.getBody();
        }
        throw new CustomParameterizedException("Unlock AI sourcing talent error {}", response.toString());
    }

    private void addRecommendFeedback(RecommendFeedback recommendFeedback) {
        if(recommendFeedback == null) {
            return;
        }
        SecurityContext context = SecurityContextHolder.getContext();
        CompletableFuture.runAsync(() -> {
            SecurityContextHolder.setContext(context);
            recommendFeedback.setReason(RecommendFeedbackReason.UNLOCK_CANDIDATE);
            talentService.recordTalentJobRecommend(recommendFeedback);
        });
    }


    private void unlockTalent(TalentUnlockListDto talentUnlockListDto) {
        try {
            ResponseEntity<Void> response = talentService.unlockCommonTalent(talentUnlockListDto);
            if (!Objects.equals(HttpStatus.HTTP_OK, response.getStatusCodeValue())) {
                throw new ExternalServiceInterfaceException(response.toString(), response.getStatusCodeValue());
            }
        } catch (Exception e) {
            log.error("unlockTalent is error , msg = {}", ExceptionUtils.getStackTrace(e));
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREDITTRANSACTION_UNLOCKTALENT_INTERNALERROR.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
    }
}
