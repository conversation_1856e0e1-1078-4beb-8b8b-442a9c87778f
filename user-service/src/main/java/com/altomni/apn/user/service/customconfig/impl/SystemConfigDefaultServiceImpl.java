package com.altomni.apn.user.service.customconfig.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.SystemConfigDefault;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.SystemConfigDefaultRepository;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.mapper.customconfig.SystemConfigDefaultMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Service
public class SystemConfigDefaultServiceImpl implements SystemConfigDefaultService {

    @Resource
    private SystemConfigDefaultRepository systemConfigDefaultRepository;

    @Resource
    private SystemConfigDefaultMapper systemConfigDefaultMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public List<SystemConfigDefault> getSystemConfigByCategory(Category category) {
        List<SystemConfigDefault> defaultConfig = systemConfigDefaultRepository.findByCategory(category);

        if(!defaultConfig.isEmpty())
            return defaultConfig;
        else{
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETSYSTEMCONFIGBYCATEGORY_NOTFOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(category.getText()),userApiPromptProperties.getUserService()));
            // Handle the case when the entity with the given ID doesn't exist
        }
    }

    @Override
    public SystemConfigDefault getSystemConfigByCategoryAndSubCategory(Category category, ConfigSubcategory subCategory) {
        Optional<SystemConfigDefault> defaultConfig = Optional.ofNullable(systemConfigDefaultRepository.findByCategoryAndSubcategory(category, subCategory));

        if(defaultConfig.isPresent())
            return defaultConfig.get();
        else{
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETSYSTEMCONFIGBYCATEGORY_NOTFOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(category.getText()),userApiPromptProperties.getUserService()));
            // Handle the case when the entity with the given ID doesn't exist
        }
    }
}
