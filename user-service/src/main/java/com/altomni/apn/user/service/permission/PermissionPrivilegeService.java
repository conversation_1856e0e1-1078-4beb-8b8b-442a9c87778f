package com.altomni.apn.user.service.permission;

import com.altomni.apn.user.service.dto.permission.PermissionPrivilegeDTO;
import com.altomni.apn.user.service.dto.permission.PermissionPrivilegeUpdateDTO;

import java.util.Set;

public interface PermissionPrivilegeService {

    PermissionPrivilegeDTO create(PermissionPrivilegeDTO privilegeDTO);

    PermissionPrivilegeDTO update(PermissionPrivilegeUpdateDTO permissionPrivilegeDTO);

    PermissionPrivilegeDTO get(Long id);

    void delete(Long id);

    Set<String> findPrivilegeApisByRoleId(Long roleId);

    Set<String> findPrivilegeApisByAdminUserId(Long userId);

    Set<String> findPrivilegeApisByUserId(Long userId);

    Set<String> findPrivilegeNamesByUserId(Long userId);

    Set<String> findPublicPrivilegeApis();
}
