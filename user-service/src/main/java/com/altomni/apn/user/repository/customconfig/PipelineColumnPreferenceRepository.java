package com.altomni.apn.user.repository.customconfig;


import com.altomni.apn.common.domain.enumeration.search.ModuleType;
import com.altomni.apn.user.domain.customconfig.PipelineColumnPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.querydsl.QuerydslPredicateExecutor;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the UserPreference entity.
 */
@Repository
public interface PipelineColumnPreferenceRepository extends JpaRepository<PipelineColumnPreference, Long>, QuerydslPredicateExecutor<PipelineColumnPreference> {

    PipelineColumnPreference findByTemplateNameAndUserId(String templateName, Long userId);


    List<PipelineColumnPreference> findAllByUserId(Long userId);
}
