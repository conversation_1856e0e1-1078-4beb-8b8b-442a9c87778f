package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.common.dto.customconfig.TenantConfigDTO;
import com.altomni.apn.common.domain.config.TenantCustomConfig;
import com.altomni.apn.user.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring", uses = {})
public interface TenantCustomConfigMapper extends EntityMapper<TenantConfigDTO, TenantCustomConfig> {

    default TenantCustomConfig fromId(Long id) {
        if (id == null) {
            return null;
        }
        TenantCustomConfig tenantCustomConfig = new TenantCustomConfig();
        tenantCustomConfig.setId(id);
        return tenantCustomConfig;
    }
}
