package com.altomni.apn.user.repository.tenant;

import com.altomni.apn.user.domain.tenant.TenantWatermarkConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface TenantWatermarkConfigRepository extends JpaRepository<TenantWatermarkConfig,Long> {
    TenantWatermarkConfig findTenantWatermarkConfigByTenantIdIsAndActiveIsTrue(Long tenantId);

    @Query(value = "select name from tenant where id=:tenantId ", nativeQuery = true)
    String findTenantName(@Param("tenantId")Long tenantId);
}
