package com.altomni.apn.user.service.permission;

import com.altomni.apn.user.domain.permission.PermissionRolePrivilege;
import com.altomni.apn.user.web.rest.vm.permission.PermissionRolePrivilegeVM;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface PermissionRolePrivilegeService {

    PermissionRolePrivilege create(PermissionRolePrivilege rolePrivilege);

    //RolePrivilegeVM create(RolePrivilegeVM rolePrivilegeVM);

    Set<String> update(PermissionRolePrivilegeVM permissionRolePrivilegeVM);

    void dealWithE1E2E5ViewPermissionChanged(PermissionRolePrivilegeVM permissionRolePrivilegeVM,Set<Long> oldPrivilege);

    Map<String, List<Long>> getDataPermission(Long userId);

    void dealWithE1E2E5RoleStatusChanged(Long roleId,Boolean isActive );

    void dealWithE1E2E5UserRolesChanged(Set<Long> roleIds, Long userId,Set<Long> oldPrivilegeIds);

    PermissionRolePrivilege update(PermissionRolePrivilege rolePrivilege);

    PermissionRolePrivilege get(Long id);

    void delete(Long id);
}
