package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.user.service.dto.customconfig.PageConfig;
import com.altomni.apn.user.service.dto.customconfig.TalentColumnConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.TalentPreferenceConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.UserTalentColumnConfigDTO;

public interface TalentColumnConfigService {

    BaseConfig getTalentDatabaseColumnConfigByUserId(Long userId);

    UserTalentColumnConfigDTO getTalentColumnConfigByUserId(Long userId);

    TalentColumnConfigDTO saveTalentDatabaseColumnConfig(TalentColumnConfigDTO talentColumnConfigDTO, Long userId);

    TalentColumnConfigDTO saveTalentColumnConfig(TalentColumnConfigDTO columnConfigDTO, Long userId);

    PageConfig updateTalentPageConfig(PageConfig pageConfig);

    PageConfig updateTalentDatabasePageConfig(PageConfig pageConfig);

    BaseConfig getTalentRelateJobFolderColumnConfigByUserId(Long userId);

    BaseConfig saveTalentRelateJobFolderColumnConfig(TalentColumnConfigDTO columnConfigDTO, Long userId);

    PageConfig updateTalentTalentRelateJobFolderPageConfig(PageConfig pageConfig);

    BaseConfig getTalentSearchFolderColumnConfigByUserId(Long userId);

    BaseConfig updateTalentSearchFolderColumnConfig(TalentColumnConfigDTO columnConfigDTO, Long userId);

    PageConfig updateTalentSearchFolderPageConfig(PageConfig pageConfig);
}
