package com.altomni.apn.user.domain.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "permission_module_privilege")
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PermissionModulePrivilege implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "module_id")
    private Long moduleId;

    @Column(name = "privilege_id")
    private Long privilegeId;

//    @Column(name = "is_show_permission_button")
//    private Boolean isShowPermissionButton;
//
//    @Column(name = "disable")
//    private Boolean disable;
//
//    @Column(name = "sort")
//    private Integer sort;
//
//    @Column(name = "enable")
//    private Boolean enable;

}
