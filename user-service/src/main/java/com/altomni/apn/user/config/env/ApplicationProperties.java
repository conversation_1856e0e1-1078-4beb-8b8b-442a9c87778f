package com.altomni.apn.user.config.env;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
public class ApplicationProperties {

    @Value("${application.emailService.supportSender}")
    private String supportSender;

    @Value("${application.mainPath.baseUrl}")
    private String baseUrl;

    @Value("${application.statistic.userLoginAuditApi}")
    private String userLoginStatisticUrl;

    @Value("${application.security.aes.secretKey}")
    private String secret;

    @Value("${application.activeUserPeriod:3600}")
    private Integer activeUserPeriod;

    @Value("${application.aiSourcing.host:-1}")
    private String aiSourcingHost;
}
