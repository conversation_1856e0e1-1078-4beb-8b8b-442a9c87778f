package com.altomni.apn.user.service.application;

import com.altomni.apn.common.dto.application.recruitmentprocess.RecruitmentProcessVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;

/**
 * Service Interface for managing.
 */
@Component
@FeignClient(value = "application-service")
public interface ApplicationService {

    @PostMapping("/application/api/v3/recruitment-processes/init/tenantId/{tenantId}")
    ResponseEntity<List<RecruitmentProcessVO>> initDefaultForGeneralRecruitingProcess(@PathVariable("tenantId") Long tenantId);

    @GetMapping("/application/api/v3/recruitment-processes/{id}")
    ResponseEntity<RecruitmentProcessVO> getRecruitmentProcessById(@PathVariable("id")Long recruitmentProcessId);

    @GetMapping("/application/api/v3/recruitment-processes/count-my-recruitment-process")
    ResponseEntity<Long> countMyRecruitmentProcess();

}
