package com.altomni.apn.user.service.mapper.permission;
import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.user.domain.permission.PermissionTable;
import com.altomni.apn.user.service.dto.permission.PermissionRoleDTO;
import com.altomni.apn.user.service.mapper.EntityMapper;
import org.mapstruct.Mapper;

/**
 * Mapper for the entity Tag and its DTO TagDTO.
 */
@Mapper(componentModel = "spring", uses = {})
public interface RoleMapper extends EntityMapper<PermissionRoleDTO, Role> {

    default PermissionTable fromId(Long id) {
        if (id == null) {
            return null;
        }
        PermissionTable permissionTable = new PermissionTable();
        permissionTable.setId(id);
        return permissionTable;
    }
}
