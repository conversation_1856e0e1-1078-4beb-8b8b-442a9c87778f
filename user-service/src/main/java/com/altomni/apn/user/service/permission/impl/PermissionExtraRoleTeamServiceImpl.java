package com.altomni.apn.user.service.permission.impl;

import com.altomni.apn.common.domain.user.Role;
import com.altomni.apn.common.enumeration.permission.DataScope;
import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.domain.permission.PermissionExtraRoleTeam;
import com.altomni.apn.user.repository.permission.PermissionExtraRoleTeamRepository;
import com.altomni.apn.user.repository.permission.PermissionRoleRepository;
import com.altomni.apn.user.service.permission.PermissionExtraRoleTeamService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionRoleTeamVM;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PermissionExtraRoleTeamServiceImpl implements PermissionExtraRoleTeamService {

    @Resource
    private PermissionExtraRoleTeamRepository extraRoleTeamRepository;

    @Resource
    private PermissionRoleRepository roleRepository;

    @Override
    @Transactional
    public List<Long> saveRoleDataPermission(Long roleId, PermissionRoleTeamVM permissionRoleTeamVM) {
        roleRepository.updateRoleDataScopeById(permissionRoleTeamVM.getJobPermission().getDataScope(),
                permissionRoleTeamVM.getClientContactPermission().getDataScope(),
                permissionRoleTeamVM.getReportPermission().getDataScope(),
                permissionRoleTeamVM.getHomeAndCalendarPermission().getDataScope(),
                permissionRoleTeamVM.getCandidatePipelineManagementPermission().getDataScope(),
                permissionRoleTeamVM.getChinaInvoicingModulePermission().getDataScope(),
                roleId);
        extraRoleTeamRepository.deleteAllByRoleId(roleId.longValue());
        this.saveRoleDataPermissionByModule(roleId, permissionRoleTeamVM.getJobPermission(), Module.JOB);
        this.saveRoleDataPermissionByModule(roleId, permissionRoleTeamVM.getClientContactPermission(), Module.CLIENT_CONTACT);
        this.saveRoleDataPermissionByModule(roleId, permissionRoleTeamVM.getReportPermission(), Module.REPORT);
        this.saveRoleDataPermissionByModule(roleId, permissionRoleTeamVM.getHomeAndCalendarPermission(), Module.HOME_AND_CALENDAR);
        this.saveRoleDataPermissionByModule(roleId, permissionRoleTeamVM.getCandidatePipelineManagementPermission(), Module.CANDIDATE_PIPELINE_MANAGEMENT);
        this.saveRoleDataPermissionByModule(roleId, permissionRoleTeamVM.getChinaInvoicingModulePermission(), Module.CHINA_INVOICING);
        return roleRepository.findUserIdsByRoleId(roleId.longValue());
    }

    private void saveRoleDataPermissionByModule(Long roleId, PermissionRoleTeamVM.PermissionDetail permissionDetail, Module module){
        if (permissionDetail.getDataScope().equals(DataScope.PERMISSION_EXTRA_TEAM.toDbValue()) && CollectionUtils.isNotEmpty(permissionDetail.getTeamIds())){
            Long tenantId = SecurityUtils.getTenantId();
            extraRoleTeamRepository.saveAll(permissionDetail.getTeamIds().stream().map(teamId -> {
                return new PermissionExtraRoleTeam(null, roleId.longValue(), module, teamId, tenantId, permissionDetail.isModifiable());
            }).collect(Collectors.toList()));
        }
    }

    @Override
    public PermissionRoleTeamVM findPermissionByRoleId(Long roleId) {
        Role role = roleRepository.findById(roleId).orElseThrow(() -> new CustomParameterizedException("Cannot find this role!"));
        PermissionRoleTeamVM vm = new PermissionRoleTeamVM();
        vm.setJobPermission(this.getPermissionDetail(roleId, role.getDataScope(), Module.JOB));
        vm.setClientContactPermission(this.getPermissionDetail(roleId, role.getClientContactDataScope(), Module.CLIENT_CONTACT));
        vm.setReportPermission(this.getPermissionDetail(roleId, role.getReportDataScope(), Module.REPORT));
        vm.setHomeAndCalendarPermission(this.getPermissionDetail(roleId, role.getHomeAndCalendarDataScope(), Module.HOME_AND_CALENDAR));
        vm.setCandidatePipelineManagementPermission(this.getPermissionDetail(roleId, role.getCandidatePipelineManagementDataScope(), Module.CANDIDATE_PIPELINE_MANAGEMENT));
        vm.setChinaInvoicingModulePermission(this.getPermissionDetail(roleId, role.getChinaInvoicingDataScope(), Module.CHINA_INVOICING));
        return vm;
    }

    private PermissionRoleTeamVM.PermissionDetail getPermissionDetail(Long roleId, Integer dataScope, Module module){
        PermissionRoleTeamVM.PermissionDetail permissionDetail = new PermissionRoleTeamVM.PermissionDetail();
        if (Objects.isNull(dataScope)){
            dataScope = DataScope.PERMISSION_SELF.toDbValue();
        }
        permissionDetail.setRoleId(roleId);
        permissionDetail.setDataScope(dataScope);
        if (dataScope.equals(DataScope.PERMISSION_EXTRA_TEAM.toDbValue())){
            permissionDetail.setTeamIds(extraRoleTeamRepository.findAllByRoleIdAndModule(roleId, module).stream().map(permissionExtraRoleTeam -> {
                permissionDetail.setModifiable(permissionExtraRoleTeam.getWritable());
                return permissionExtraRoleTeam.getTeamId();
            }).collect(Collectors.toSet()));
        }
        return permissionDetail;
    }
}
