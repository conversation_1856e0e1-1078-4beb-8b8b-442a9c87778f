package com.altomni.apn.user.repository.user;

import com.altomni.apn.common.domain.user.UserDeliveryJobFunctionRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Set;


@Repository
public interface UserDeliveryJobFunctionRelationRepository extends JpaRepository<UserDeliveryJobFunctionRelation, Long> {

    List<UserDeliveryJobFunctionRelation> findAllByUserId(Long userId);

    List<UserDeliveryJobFunctionRelation> findAllByUserIdAndTop(Long userId, Boolean top);

    List<UserDeliveryJobFunctionRelation> findAllByUserIdIn(Collection<Long> userIds);

    List<UserDeliveryJobFunctionRelation> findAllByUserIdInAndTop(Collection<Long> userIds, Boolean top);
}
