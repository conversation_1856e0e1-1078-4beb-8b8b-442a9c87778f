package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.common.domain.config.TenantDefaultConfig;
import com.altomni.apn.common.domain.enumeration.config.TenantConfigCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;


/**
 * Spring Data R2DBC repository for the TenantDefaultConfig entity.
 */

@Repository
public interface TenantDefaultConfigRepository extends JpaRepository<TenantDefaultConfig, Long> {

    Optional<TenantDefaultConfig> findFirstByConfigCode(TenantConfigCode configCode);

}

