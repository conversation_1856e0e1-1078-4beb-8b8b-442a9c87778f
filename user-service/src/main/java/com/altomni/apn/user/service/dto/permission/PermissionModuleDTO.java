package com.altomni.apn.user.service.dto.permission;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class PermissionModuleDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

}
