package com.altomni.apn.user.service.rabbmitmq.listener;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.altomni.apn.common.config.audit.AuditUserHolder;
import com.altomni.apn.common.domain.transactionrecord.CommonMqConsumeFailedRecord;
import com.altomni.apn.common.enumeration.enums.MqTranRecordBusTypeEnums;
import com.altomni.apn.common.service.cache.CommonRedisService;
import com.altomni.apn.common.utils.LoginUtil;
import com.altomni.apn.common.utils.NotificationUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.repository.mqfailedrecord.UserMqTransactionFailedRecordRepository;
import com.altomni.apn.user.service.dto.user.CreditTransactionDTO;
import com.altomni.apn.user.service.user.CreditTransactionService;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RefreshScope
public class UserTxMqListener {

    @Autowired
    UserMqTransactionFailedRecordRepository userMqTransactionFailedRecordRepository;

    @Autowired
    CommonRedisService redisService;

    @Autowired
    CreditTransactionService creditTransactionService;

    private UserTxMqListener userTxMqListener;

    @Value("${application.notification.lark.mq.webhookKey}")
    private String LARK_WEBHOOK_KEY;

    @Value("${application.notification.lark.mq.webhookUrl}")
    private String LARK_WEBHOOK_URL;

    @PostConstruct
    public void init() {
        userTxMqListener = this;
        userTxMqListener.userMqTransactionFailedRecordRepository = this.userMqTransactionFailedRecordRepository;
        userTxMqListener.redisService = this.redisService;
//        userTxMqListener.tokenStore = this.tokenStore;
        userTxMqListener.creditTransactionService = this.creditTransactionService;
    }

    private static final String AUTHORIZATION_HEADER = "Authorization";

    private static final String USER_CONSUMER_PREFIX = "USER_TX_";

    @RabbitListener(containerFactory = "userConsumerFactory", queues = {"${application.talent-tx-mq.queue}"})
    @RabbitHandler
    public void processHandler(Channel channel, Message message) throws IOException {
        log.info("talent tx ,{} Received message: {}，Business data：{}", this.getClass().getName(), message.toString(), new String(message.getBody()));
        String json = new String(message.getBody(), StandardCharsets.UTF_8);
        try {

            JSONObject param = JSON.parseObject(json);
            if (!param.containsKey(AUTHORIZATION_HEADER)) {
                log.error("{} , token is null param:{}", MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.getDesc(), param);
                channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            String token = param.getString(AUTHORIZATION_HEADER);
            param.remove(AUTHORIZATION_HEADER);

            CreditTransactionDTO creditTransactionDTO = JSONObject.parseObject(param.toJSONString(), CreditTransactionDTO.class);
            if (!checkExits(creditTransactionDTO, param)) {
                channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
                return;
            }
            if (param.containsKey(SecurityUtils.OPERATOR_UID)) {
                AuditUserHolder.set(param.getOrDefault(SecurityUtils.OPERATOR_UID, "").toString());
            }

            try {
                LoginUtil.simulateLoginWithClient();
                //do something
                creditTransactionService.updateTalentId(creditTransactionDTO, creditTransactionDTO.getTenantId());
                saveFailedRecord(creditTransactionDTO, param, 1);
                String key = USER_CONSUMER_PREFIX.concat(creditTransactionDTO.getTalentId() + "-" + creditTransactionDTO.getId());
                userTxMqListener.redisService.delete(key);
                log.info("{},rabbit mq consume success", MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.getDesc());
            } catch (Exception e) {
                saveFailedRecord(creditTransactionDTO, param, 0);
                NotificationUtils.sendAlertToLark(LARK_WEBHOOK_KEY, LARK_WEBHOOK_URL, String.format("user-service-> 业务类型：%s ,消费数据失败，请人工处理。", MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.getDesc()));
            }

            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            log.error("{},message is:{}, error message：{}", MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.getDesc(), json, e.getMessage());
            channel.basicReject(message.getMessageProperties().getDeliveryTag(), false);
        } finally {
            AuditUserHolder.clear();
        }
    }


    /**
     * 幂等校验
     *
     * @param creditTransactionDTO
     * @param param
     * @return
     */
    private boolean checkExits(CreditTransactionDTO creditTransactionDTO, JSONObject param) {
        String key = USER_CONSUMER_PREFIX.concat(creditTransactionDTO.getTalentId() + "-" + creditTransactionDTO.getId());
        //redis verify exists
        String value = userTxMqListener.redisService.get(key);
        if (StringUtils.isNotBlank(value)) {
            log.error("{}, redis search id exists,param:{}", MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.getDesc(), param);
            return false;
        }

        //数据库是否记录
        CommonMqConsumeFailedRecord commonMqConsumeFailedRecord = userTxMqListener.userMqTransactionFailedRecordRepository.findByBusIdAndBusTypeAndReceiceStatus(BigInteger.valueOf(creditTransactionDTO.getTalentId()), MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.toDbValue(),1);
        if (null != commonMqConsumeFailedRecord) {
            log.error("{}, id exists,param:{}", MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.getDesc(), param);
            return false;
        }
        userTxMqListener.redisService.set(key, "1", 18000);
        return true;
    }

    /**
     * 保存消费记录
     *
     * @param creditTransactionDTO
     * @param param
     * @param status
     */
    private void saveFailedRecord(CreditTransactionDTO creditTransactionDTO, JSONObject param, Integer status) {
        CommonMqConsumeFailedRecord failedRecord = new CommonMqConsumeFailedRecord();
        failedRecord.setConsumeCount(1);
        failedRecord.setBusId(BigInteger.valueOf(creditTransactionDTO.getTalentId()));
        failedRecord.setReceiveMessage(param.toJSONString());
        failedRecord.setBusType(MqTranRecordBusTypeEnums.CREATE_TALENT_FROM_COMMONPOOL.toDbValue());
        failedRecord.setReceiceStatus(status);
        userTxMqListener.userMqTransactionFailedRecordRepository.save(failedRecord);
    }
}