package com.altomni.apn.user.service.dto.customconfig;



import java.util.List;

public class UserCustomConfig {

    PageConfig pageConfig;

    List<CustomColumnField> columnConfig;



    public PageConfig getPageConfig() {
        return pageConfig;
    }

    public void setPageConfig(PageConfig pageConfig) {
        this.pageConfig = pageConfig;
    }

    public List<CustomColumnField> getColumnConfig() {
        return columnConfig;
    }

    public void setColumnConfig(List<CustomColumnField> columnConfig) {
        this.columnConfig = columnConfig;
    }

    public UserCustomConfig() {
    }

    public UserCustomConfig(List<CustomColumnField> columnConfig) {
        this.columnConfig = columnConfig;
    }
}
