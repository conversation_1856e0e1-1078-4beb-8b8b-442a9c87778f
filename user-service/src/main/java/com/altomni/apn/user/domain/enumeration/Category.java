package com.altomni.apn.user.domain.enumeration;

import com.altomni.apn.common.domain.enumeration.support.ConvertedEnum;
import com.altomni.apn.common.domain.enumeration.support.ReverseEnumResolver;

/**
 * The Form Category enumeration.
 */
public enum Category implements ConvertedEnum<Byte> {
    JOB_FORM((byte) 11, "Job Form"),
    JOB_COLUMN((byte) 12, "Job Column"),

    TALENT_FORM((byte) 21, "Talent Form"),
    TALENT_COLUMN((byte) 22, "Talent Column"),

    COMPANY_COLUMN((byte) 32, "Company Column"),

    DASHBOARD_COLUMN((byte) 41, "dashboard Column"),

    INVOICE_COLUMN((byte) 51, "invoice Column"),

    REPORT_EXPENSE_COLUMN((byte) 61, "report expense Column"),

    KPI_REPORT_BY_USER_COLUMN((byte) 71, "kpi report by user column"),

    KPI_REPORT_BY_COMPANY_COLUMN((byte) 72, "kpi report by company column"),

    USER_ADOPTION_REPORT((byte) 73, "user adoption report"),

    H_REPORT_DETAIL_COLUMN((byte) 74, "h report detail column"),

    REPORT_COMPANY_COLUMN((byte) 81, "Report Company Column"),

    MONTHLY_REVENUE_DETAIL_COLUMN((byte) 82, "monthly revenue detail column"),

    QUARTERLY_NEW_HIRES_COLUMN((byte) 83, "quarterly new hires column"),

    QUARTERLY_RENEWALS_COLUMN((byte) 84, "quarterly renewals column"),

    QUARTERLY_OFFBOARDING_COLUMN((byte) 85, "quarterly offboarding column"),

    INVOICING_COLUMN((byte) 90, "invoicing Column"),

    CALENDAR_COLUMN((byte) 95, "calendar Column"),

    USER_SEARCH_COLUMN((byte) 97, "user search column"),

    E1_REPORT_SEARCH_FILTER((byte) 99, "e1 report search filter"),

    ;

    private final byte dbValue;
    private final String text;

    Category(byte dbValue, String text) {
        this.dbValue = dbValue;
        this.text = text;
    }

    public byte getDbValue() {
        return dbValue;
    }

    public String getText() {
        return text;
    }


    @Override
    public Byte toDbValue() {
        return dbValue;
    }

    public static final ReverseEnumResolver<Category, Byte> resolver = new ReverseEnumResolver<>(Category.class, Category::toDbValue);

    public static Category fromDbValue(Byte dbValue) {
        return resolver.get(dbValue);
    }

//
//    JOB_FORM((byte)1),
//
//    JOB_COLUMN((byte)2);
//
//    private final byte dbValue;
//
//    Category(byte dbValue) {
//        this.dbValue = dbValue;
//    }
//
//
//    @Override
//    public Byte toDbValue() {
//        return dbValue;
//    }
//
//    public static final ReverseEnumResolver<Category, Byte> resolver = new ReverseEnumResolver<>(Category.class, Category::toDbValue);
//
//    public static Category fromDbValue(Byte dbValue) {
//        return resolver.get(dbValue);
//    }
}
