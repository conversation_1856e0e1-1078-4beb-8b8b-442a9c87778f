package com.altomni.apn.user.service.user;

import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.service.dto.user.CreditTransactionDTO;

import java.io.IOException;
import java.util.List;

public interface CreditTransactionService {

    CreditTransactionDTO create(CreditTransactionDTO creditTransactionDTO);

    List<CreditTransactionDTO> findByTenantId(Long tenantId);

    CreditTransaction findByProfileIdAndTenantIdAndStatus(String profileId, Long tenantId, Status status);

    List<CreditTransaction> findAllByTenantIdAndStatusAndProfileIdIn(Long tenantId, Status status, List<String> profileId);

    List<CreditTransaction> findByTenantIdAndStatus(Long tenantId, Status status);

    CreditTransactionDTO updateTalentId(CreditTransactionDTO creditTransactionDTO, Long tenantId);

    CreditTransactionDTO createCommonPoolCreditTransaction(CreditTransactionDTO creditTransactionDTO);

    String createAiSourcingCreditTransaction(CreditTransactionDTO creditTransactionDTO) throws IOException;

    List<CreditTransaction> findAllByTenantIdAndStatusAndSearchEsIdIn(Long tenantId, Status status, List<String> searchEsId);

    CreditTransaction findByTenantIdAndStatusAndTalentIdIs(Long tenantId, Status status, Long talentId);
}
