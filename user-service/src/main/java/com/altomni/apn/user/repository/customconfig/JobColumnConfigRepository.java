package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.user.domain.customconfig.JobColumnConfig;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data R2DBC repository for the SystemConfigDefault entity.
 */
@Repository
public interface JobColumnConfigRepository extends JpaRepository<JobColumnConfig, Long> {
    List<JobColumnConfig> findAllByIdIn(List<Long> ids);
    JobColumnConfig findByUserId(Long id);
}

