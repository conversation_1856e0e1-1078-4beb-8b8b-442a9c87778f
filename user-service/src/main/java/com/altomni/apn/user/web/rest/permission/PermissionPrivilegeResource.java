package com.altomni.apn.user.web.rest.permission;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.altomni.apn.common.config.PrivilegeName;
import com.altomni.apn.common.dto.permission.PermissionPrivilegeVM;
import com.altomni.apn.common.repository.permission.PermissionRepository;
import com.altomni.apn.common.service.cache.CachePermission;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.common.utils.ServiceUtils;
import com.altomni.apn.user.domain.permission.PermissionModulePrivilege;
import com.altomni.apn.user.domain.permission.PermissionPrivilege;
import com.altomni.apn.user.repository.permission.PermissionModulePrivilegeRepository;
import com.altomni.apn.user.repository.permission.PermissionPrivilegeRepository;
import com.altomni.apn.user.service.dto.permission.PermissionPrivilegeDTO;
import com.altomni.apn.user.service.dto.permission.PermissionPrivilegeUpdateDTO;
import com.altomni.apn.user.service.permission.PermissionPrivilegeService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionQueryPrivilegesVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * REST controller for managing permissions.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/privileges")
public class PermissionPrivilegeResource {

    @Resource
    private PermissionPrivilegeRepository privilegeRepository;

    @Resource
    private PermissionPrivilegeService privilegeService;

    @Resource
    private CachePermission cachePermission;

    @Resource
    private PermissionModulePrivilegeRepository permissionModulePrivilegeRepository;

    @Resource
    private PermissionRepository permissionRepository;

    @PrivilegeName(value = "Add Privilege")
    @PostMapping("")
    public ResponseEntity<PermissionPrivilegeDTO> addPrivilege(@RequestBody PermissionPrivilegeDTO privilegeDTO){
        log.info("() REST request to add privilege {}", privilegeDTO);
        return ResponseEntity.ok(privilegeService.create(privilegeDTO));
    }

    @PrivilegeName(value = "Update Privilege")
    @PutMapping("")
    public ResponseEntity<PermissionPrivilegeDTO> updatePrivilege(@RequestBody PermissionPrivilegeUpdateDTO privilegeDTO){
        log.info("() REST request to update privilege {}", privilegeDTO);
        return ResponseEntity.ok(privilegeService.update(privilegeDTO));
    }

    @PrivilegeName(value = "Delete Privilege")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePrivilege(@PathVariable("id") Long id){
        log.info("() REST request to delete privilege {}", id);
        privilegeService.delete(id);
        return ResponseEntity.ok().build();
    }

    @PrivilegeName(value = "Get Privilege")
    @GetMapping("/{id}")
    public ResponseEntity<PermissionPrivilegeDTO> getPrivilege(@PathVariable("id") Long id){
        log.info("() REST request to get privilege by id {}", id);
        return ResponseEntity.ok(privilegeService.get(id));
    }

    @PrivilegeName(value = "Get private Privileges")
    @GetMapping("/tree")
    public ResponseEntity<List<PermissionPrivilegeDTO>> getPrivatePrivilegeTree(){
        log.info("({}) REST request to get private privileges", SecurityUtils.getCurrentUserLogin());
        List<PermissionPrivilege> privileges = privilegeRepository.findAllByIsPublicEqualsOrderByLevelDesc(Boolean.FALSE);
        Map<Long, List<PermissionPrivilegeDTO>> map = new HashMap<>();
        privileges.forEach(privilege -> {
            PermissionPrivilegeDTO permissionPrivilegeDTO = new PermissionPrivilegeDTO();
            BeanUtils.copyProperties(privilege, permissionPrivilegeDTO);
            permissionPrivilegeDTO.setApiList(JSONArray.parseArray(privilege.getApi()));
            permissionPrivilegeDTO.setParentId(privilege.getParentId());
            if (!map.containsKey(permissionPrivilegeDTO.getParentId())){
                map.put(permissionPrivilegeDTO.getParentId(), new ArrayList<>());
            }
            map.get(permissionPrivilegeDTO.getParentId()).add(permissionPrivilegeDTO);

            if (map.containsKey(permissionPrivilegeDTO.getId())){
                permissionPrivilegeDTO.setChildren(map.get(permissionPrivilegeDTO.getId()));
            }
        });
        return ResponseEntity.ok(map.getOrDefault(-1L, new ArrayList<>()));
    }

    @PrivilegeName(value = "Get All Privileges")
    @GetMapping("/tree/all")
    public ResponseEntity<List<PermissionPrivilegeDTO>> getAllPrivilegeTree(){
        log.info("({}) REST request to get all privileges", SecurityUtils.getCurrentUserLogin());
        List<PermissionPrivilege> privileges = privilegeRepository.findAll(Sort.by(Sort.Direction.DESC, "level"));
        Map<Long, List<PermissionPrivilegeDTO>> map = new HashMap<>();
        Map<Long, PermissionPrivilegeDTO> idPathMap = new HashMap<>();
        privileges.forEach(privilege -> {
            PermissionPrivilegeDTO permissionPrivilegeDTO = new PermissionPrivilegeDTO();
            BeanUtils.copyProperties(privilege, permissionPrivilegeDTO);
            permissionPrivilegeDTO.setApiList(JSONArray.parseArray(privilege.getApi()));
            permissionPrivilegeDTO.setParentId(privilege.getParentId());
            if (!map.containsKey(permissionPrivilegeDTO.getParentId())){
                map.put(permissionPrivilegeDTO.getParentId(), new ArrayList<>());
            }
            idPathMap.put(privilege.getId(), permissionPrivilegeDTO);
            map.get(permissionPrivilegeDTO.getParentId()).add(permissionPrivilegeDTO);
            if (map.containsKey(permissionPrivilegeDTO.getId())){
                List<PermissionPrivilegeDTO> privilegeDTOS = map.get(permissionPrivilegeDTO.getId());
                permissionPrivilegeDTO.setChildren(privilegeDTOS);
            }
        });
        privileges.forEach(privilege->{
            PermissionPrivilegeDTO pDTO = idPathMap.get(privilege.getId());
            List<Long> idPath = pDTO.getIdPath();
            idPath.add(pDTO.getId());
            Long parentId = pDTO.getParentId();
            idPath.add(0, parentId);
            while (idPathMap.containsKey(parentId)){
                parentId = idPathMap.get(parentId).getParentId();
                idPath.add(0, parentId);
            }
        });

//        List<PermissionModulePrivilege> permissionModulePrivilegeList = permissionModulePrivilegeRepository.findAllByTenantId(SecurityUtils.getTenantId());
//        Map<Long, PermissionModulePrivilege> permissionModulePrivilegeMap = permissionModulePrivilegeList.stream().collect(Collectors.toMap(PermissionModulePrivilege::getPrivilegeId, o -> o));
//        List<PermissionPrivilegeDTO> result = new ArrayList<>();
//        if (map.containsKey(-1L)) {
//            map.get(-1L).get(0).getChildren().forEach(o -> {
//                if (permissionModulePrivilegeMap.containsKey(o.getId())) {
//                    o.setSort(permissionModulePrivilegeMap.get(o.getId()).getSort());
//                    result.add(o);
//                }
//            });
//            CollUtil.sort(result, Comparator.comparing(PermissionPrivilegeDTO::getSort, Comparator.nullsLast(Integer::compareTo)));
//            map.get(-1L).get(0).setChildren(result);
//        }
        return ResponseEntity.ok(map.getOrDefault(-1L, new ArrayList<>()));
    }

    @PrivilegeName(value = "Get Tenant Privileges")
    @GetMapping("/tree/tenant")
    public ResponseEntity<List<PermissionPrivilegeDTO>> getTenantPrivilegeTree(){
        log.info("({}) REST request to get tenant privileges", SecurityUtils.getCurrentUserLogin());
        List<PermissionPrivilege> privileges = privilegeRepository.findAll(Sort.by(Sort.Direction.DESC, "level"));
        Map<Long, List<PermissionPrivilegeDTO>> map = new HashMap<>();
        Map<Long, PermissionPrivilegeDTO> idPathMap = new HashMap<>();
        privileges.forEach(privilege -> {
            PermissionPrivilegeDTO permissionPrivilegeDTO = new PermissionPrivilegeDTO();
            BeanUtils.copyProperties(privilege, permissionPrivilegeDTO);
            permissionPrivilegeDTO.setApiList(JSONArray.parseArray(privilege.getApi()));
            permissionPrivilegeDTO.setParentId(privilege.getParentId());
            if (!map.containsKey(permissionPrivilegeDTO.getParentId())){
                map.put(permissionPrivilegeDTO.getParentId(), new ArrayList<>());
            }
            idPathMap.put(privilege.getId(), permissionPrivilegeDTO);
            map.get(permissionPrivilegeDTO.getParentId()).add(permissionPrivilegeDTO);
            if (map.containsKey(permissionPrivilegeDTO.getId())){
                List<PermissionPrivilegeDTO> privilegeDTOS = map.get(permissionPrivilegeDTO.getId());
                permissionPrivilegeDTO.setChildren(privilegeDTOS);
            }
        });
        privileges.forEach(privilege->{
            PermissionPrivilegeDTO pDTO = idPathMap.get(privilege.getId());
            List<Long> idPath = pDTO.getIdPath();
            idPath.add(pDTO.getId());
            Long parentId = pDTO.getParentId();
            idPath.add(0, parentId);
            while (idPathMap.containsKey(parentId)){
                parentId = idPathMap.get(parentId).getParentId();
                idPath.add(0, parentId);
            }
        });

        List<Long> idList = permissionRepository.findAllIdByTenantId(SecurityUtils.getTenantId());
        List<PermissionPrivilegeDTO> result = map.getOrDefault(-1L, new ArrayList<>());

//        if (CollectionUtils.isNotEmpty(result) && CollectionUtils.isNotEmpty(result.get(0).getChildren())) {
//            result.get(0).setChildren(result.get(0).getChildren().stream().filter(o -> idList.contains(o.getId())).collect(Collectors.toList()));
//        }
//
//        return ResponseEntity.ok(result);

        List<PermissionModulePrivilege> permissionModulePrivilegeList = permissionModulePrivilegeRepository.findAll();

        Set<Long> filterIds = new HashSet<>(idList);
        Set<Long> configIds = permissionModulePrivilegeList.stream().map(PermissionModulePrivilege::getPrivilegeId).collect(Collectors.toSet());

        List<PermissionPrivilegeDTO> filteredTree = filterTree(result, filterIds, configIds);
        return ResponseEntity.ok(filteredTree);
    }

    private static List<PermissionPrivilegeDTO> filterTree(List<PermissionPrivilegeDTO> result, Set<Long> filterIds, Set<Long> configIds) {
        List<PermissionPrivilegeDTO> filteredNodes = new ArrayList<>();
        for (PermissionPrivilegeDTO node : result) {
            PermissionPrivilegeDTO filteredNode = filterTreeHelper(node, filterIds, configIds);
            if (filteredNode != null) {
                filteredNodes.add(filteredNode);
            }
        }
        return filteredNodes;
    }

    private static PermissionPrivilegeDTO filterTreeHelper(PermissionPrivilegeDTO node, Set<Long> filterIds, Set<Long> configIds) {
        if (node == null) {
            return null;
        }

        if (!configIds.contains(node.getId())) {
            return node;
        }

        if (filterIds.contains(node.getId()) && CollUtil.isNotEmpty(node.getChildren()) && node.getChildren().stream().noneMatch(o -> configIds.contains(o.getId()))) {
            return node;
        }

        PermissionPrivilegeDTO filteredNode = null;
        if (filterIds.contains(node.getId())) {
            filteredNode = new PermissionPrivilegeDTO();
            ServiceUtils.myCopyProperties(node, filteredNode, PermissionPrivilegeDTO.UpdateSkipProperties);
            if (CollUtil.isNotEmpty(node.getChildren())) {
                List<PermissionPrivilegeDTO> childrenList = new ArrayList<>();
                for (PermissionPrivilegeDTO child : node.getChildren()) {
                    PermissionPrivilegeDTO childPermissionPrivilegeDTO = filterTreeHelper(child, filterIds, configIds);
                    if (childPermissionPrivilegeDTO != null) {
                        childrenList.add(childPermissionPrivilegeDTO);
                    }
                }
                filteredNode.setChildren(childrenList);
            }

        }

        return filteredNode;
    }

    @PrivilegeName(value = "Get System's Privileges")
    @PostMapping("/get-system-privileges-by-services")
    public ResponseEntity<List<PermissionPrivilegeVM.Detail>> getPrivilegesByServices(@RequestBody PermissionQueryPrivilegesVM queryPrivilegesVM){
        log.info("({}) REST request to generate privileges: {}", SecurityUtils.getCurrentUserLogin());
        List<PermissionPrivilegeVM.Detail> apis = new ArrayList<>();
        queryPrivilegesVM.getServices().forEach(service -> {
            Map<String, List<PermissionPrivilegeVM.Detail>> systemApis = cachePermission.getSystemApis(service);
            systemApis.values().forEach(sysApis -> apis.addAll(sysApis));
        });
        return ResponseEntity.ok(apis);
    }
}
