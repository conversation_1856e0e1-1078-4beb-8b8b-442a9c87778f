package com.altomni.apn.user.service.dto.user;

import com.alibaba.fastjson.JSON;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.user.domain.user.SocialConsumer;

/**
 * User info from LinkedIn login
 * https://developer.linkedin.com/docs/fields/basic-profile
 */
public class LinkedInUser extends SocialUser {

    public String firstName;

    public String lastName;

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Override
    public User syncUser(User existingUser) {
        if (existingUser == null) {
            existingUser = new User();
            existingUser.setActivated(true);
            existingUser.setLangKey("en");
            //these only set first time
            existingUser.setUsername(this.email);
            existingUser.setEmail(this.email);
            if (this.phone != null) {
                existingUser.setPhone(this.phone);
            }
        }
        //these are update every time
        existingUser.setFirstName(this.firstName);
        existingUser.setLastName(this.lastName);
        return existingUser;
    }

    @Override
    public SocialConsumer consumer() {
        SocialConsumer socialConsumer = new SocialConsumer();
        socialConsumer.setProvider(this.provider.name());
        socialConsumer.setIdInProvider(this.id);
        socialConsumer.setDetails(JSON.toJSONString(this));
        return socialConsumer;
    }

    @Override
    public String toString() {
        return "LinkedInUser{" +
            "firstName='" + firstName + '\'' +
            ", lastName='" + lastName + '\'' +
            ", email='" + email + '\'' +
            ", id='" + id + '\'' +
            ", provider=" + provider +
            '}';
    }
}
