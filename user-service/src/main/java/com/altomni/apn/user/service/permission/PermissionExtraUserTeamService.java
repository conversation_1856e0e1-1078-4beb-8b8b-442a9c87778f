package com.altomni.apn.user.service.permission;

import com.altomni.apn.user.web.rest.vm.permission.PermissionUserTeamPermissionVM;

public interface PermissionExtraUserTeamService {

    void saveUserTeamPermission(Long userId, PermissionUserTeamPermissionVM permissionUserTeamPermissionVM);

    PermissionUserTeamPermissionVM findDataPermissionByUserId(Long userId);

    PermissionUserTeamPermissionVM.PermissionDetail findAllClientContactDataPermissionByUserIdAndTenantId(Long tenantId, Long userId);
}
