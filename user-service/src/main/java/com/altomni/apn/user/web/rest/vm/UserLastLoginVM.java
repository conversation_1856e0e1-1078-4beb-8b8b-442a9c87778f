package com.altomni.apn.user.web.rest.vm;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UserLastLoginVM implements Serializable {

    private Long id;

    private Long userId;

    private String timeZone;

    private String location;

    private String ip;

    private String mac;

    private Instant lastLoginTime;

    private String userAgent;

}
