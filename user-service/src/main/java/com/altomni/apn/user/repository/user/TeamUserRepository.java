package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.TeamUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the TeamUser entity.
 */
@Repository
public interface TeamUserRepository extends JpaRepository<TeamUser, Long> {

    TeamUser findByTeamIdAndUserId(Long teamId, Long userId);

    List<TeamUser> findByTeamId(Long teamId);

    void deleteAllByTeamId(Long teamId);
}
