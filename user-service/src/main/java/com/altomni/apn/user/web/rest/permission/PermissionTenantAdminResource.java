package com.altomni.apn.user.web.rest.permission;

import com.altomni.apn.common.config.constants.AuthoritiesConstants;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.user.service.dto.user.UserDTO;
import com.altomni.apn.user.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * REST controller for managing permissions.
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/v3/permissions/tenant-admins")
public class PermissionTenantAdminResource {

    @Resource
    private UserService userService;

    @PostMapping("")
    public ResponseEntity<User> createTenantAdminUser(@Valid @RequestBody UserDTO userDTO) {
        log.debug("REST request to createTenantAdminUser : {}", userDTO);
        User newUser = userService.createTenantAdminUser(userDTO, AuthoritiesConstants.TENANT_ADMIN, userDTO.getTenantId());
        return ResponseEntity.ok(newUser);
    }

    @GetMapping("")
    public ResponseEntity<List<User>> getTenantAdminUsers() {
        log.debug("REST request to getTenantAdminUsers : {}");
        return ResponseEntity.ok(userService.getTenantAdminUsers());
    }
}