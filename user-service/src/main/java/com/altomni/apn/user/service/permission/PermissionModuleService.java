package com.altomni.apn.user.service.permission;

import com.altomni.apn.user.service.dto.permission.PermissionModuleDTO;
import com.altomni.apn.user.web.rest.vm.permission.PermissionModuleTablesVM;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PermissionModuleService {

    PermissionModuleDTO create(PermissionModuleDTO permissionModuleDTO);

    PermissionModuleDTO update(PermissionModuleDTO permissionModuleDTO);

    void delete(Long id);

    List<PermissionModuleDTO> findAll();

    void setModuleTables(PermissionModuleTablesVM permissionModuleTablesVM);
}
