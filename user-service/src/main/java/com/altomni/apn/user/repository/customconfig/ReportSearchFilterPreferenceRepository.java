package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.user.domain.customconfig.ReportSearchFilterPreference;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReportSearchFilterPreferenceRepository extends JpaRepository<ReportSearchFilterPreference, Long> {

    List<ReportSearchFilterPreference> findByUserIdAndTenantIdAndReportType(Long userId, Long tenantId,Integer reportType);

    @Modifying
    @Query(value = "delete from report_search_filter_preference where id in (?1)",nativeQuery = true)
    void deleteAllByIdList(List<Long> idList);
}
