package com.altomni.apn.user.service.user.impl;

import com.altomni.apn.common.vo.canal.MqMessageCountVM;
import com.altomni.apn.user.service.user.UserSyncService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;

@Slf4j
@Service("userSyncService")
public class UserSyncServiceImpl implements UserSyncService {

    @Resource
    private UserSyncDataHandler userSyncDataHandler;

    @Override
    public MqMessageCountVM checkUserMqMessageCount() {
        return userSyncDataHandler.checkMqMessageCount();
    }


    @Override
    public void syncUsersToMQ(Collection<Long> userIds, Integer priority) {
        userSyncDataHandler.doSyncDataToMQ(userIds, priority);
    }

}
