package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.Division;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the Division entity.
 */
@SuppressWarnings("unused")
@Repository
public interface DivisionRepository extends JpaRepository<Division, Long> {

    List<Division> findByTenantId(Long tenantId);
}
