package com.altomni.apn.user.domain.geoinfo;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A GeoInfoEN.
 */
@Entity
@Table(name = "city_locations_cn")
public class TenantGeoInfoCN implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long cityId;

    @Column(name = "city_name")
    private String city;

    @Column(name = "subdivision_1_iso_code")
    private String provinceCode;

    @Column(name = "subdivision_1_name")
    private String province;

    @Column(name = "country_iso_code")
    private String countryCode;

    @Column(name = "country_name")
    private String country;

    @Column(name = "time_zone")
    private String timeZone;

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove

    public Long getCityId() {
        return cityId;
    }

    public void setCityId(Long cityId) {
        this.cityId = cityId;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }
// jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove


    public String getTimeZone() {
        return timeZone;
    }

    public void setTimeZone(String timeZone) {
        this.timeZone = timeZone;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TenantGeoInfoCN geoInfoEN = (TenantGeoInfoCN) o;
        return cityId.equals(geoInfoEN.cityId) &&
            city.equals(geoInfoEN.city) &&
            Objects.equals(province, geoInfoEN.province) &&
            Objects.equals(country, geoInfoEN.country);
    }

    @Override
    public int hashCode() {
        return Objects.hash(cityId, city, province, country);
    }

    @Override
    public String toString() {
        return "GeoInfoEN{" +
            "cityId=" + cityId +
            ", city='" + city + '\'' +
            ", provinceCode='" + provinceCode + '\'' +
            ", province='" + province + '\'' +
            ", countryCode='" + countryCode + '\'' +
            ", country='" + country + '\'' +
            '}';
    }
}
