package com.altomni.apn.user.service.customconfig.impl;

import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.dto.permission.TeamDataPermissionRespDTO;
import com.altomni.apn.common.service.initiation.InitiationService;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.UserPersonalizationConfig;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.repository.customconfig.UserPreferenceRepository;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.customconfig.UserPreferenceService;
import com.altomni.apn.user.service.dto.permission.PermissionTeamUserDTO;
import com.altomni.apn.user.service.user.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UserPreferenceServiceImpl implements UserPreferenceService {

    private final static Category category = Category.JOB_COLUMN;
    @Resource
    private UserPreferenceRepository userPreferenceRepository;

    @Resource
    private SystemConfigDefaultService systemConfigDefaultService;

    @Override
    public void updateUserRecentlyUsedRecruitmentProcess(Long recruitmentProcessId, Long userId) {
        if(recruitmentProcessId == null || userId == null){
            return; // not a valid job
        }

        Optional<UserPreference> userPreferenceOpt = Optional.ofNullable(userPreferenceRepository.findByUserId(userId));
        if (userPreferenceOpt.isPresent()) {
            UserPreference userPreference = userPreferenceOpt.get();
            userPreference.setRecruitmentProcessId(recruitmentProcessId);
            userPreferenceRepository.saveAndFlush(userPreference);

        } else {
            UserPreference userPreference = new UserPreference();
            userPreference.setRecruitmentProcessId(recruitmentProcessId);
            userPreference.setUserId(userId);
            //current front end don't need the default config
            //SystemConfigDefault systemConfigDefault = systemConfigDefaultService.getSystemConfigByCategory(category);
            //userPreference.setJobColumnConfig(systemConfigDefault.getDefaultConfig());

            userPreferenceRepository.saveAndFlush(userPreference);

        }
    }

    @Override
    public void updateUserRecentlyUsedRecruitmentProcessForPrivateJob(Long recruitmentProcessId, Long userId) {
        if(recruitmentProcessId == null || userId == null){
            return; // not a valid job
        }

        Optional<UserPreference> userPreferenceOpt = Optional.ofNullable(userPreferenceRepository.findByUserId(userId));
        if (userPreferenceOpt.isPresent()) {
            UserPreference userPreference = userPreferenceOpt.get();
            userPreference.setPrivateJobRecruitmentProcessId(recruitmentProcessId);
            userPreferenceRepository.saveAndFlush(userPreference);

        } else {
            UserPreference userPreference = new UserPreference();
            userPreference.setRecruitmentProcessId(recruitmentProcessId);
            userPreference.setPrivateJobRecruitmentProcessId(recruitmentProcessId);
            userPreference.setUserId(userId);
            //current front end don't need the default config
            //SystemConfigDefault systemConfigDefault = systemConfigDefaultService.getSystemConfigByCategory(category);
            //userPreference.setJobColumnConfig(systemConfigDefault.getDefaultConfig());

            userPreferenceRepository.saveAndFlush(userPreference);

        }
    }

    @Override
    public Long getUserRecentlyUsedRecruitmentProcessByUserId(Long userId) {
        Optional<UserPreference> userPreference = Optional.ofNullable(userPreferenceRepository.findByUserId(userId));

        if (userPreference.isPresent() && userPreference.get().getRecruitmentProcessId() != null) {
            UserPreference preference = userPreference.get();
            return preference.getRecruitmentProcessId();
        }
        return null;
    }

    @Override
    public Long getUserRecentlyUsedRecruitmentProcessByUserIdForPrivateJob(Long userId) {
        Optional<UserPreference> userPreference = Optional.ofNullable(userPreferenceRepository.findByUserId(userId));

        if (userPreference.isPresent() && userPreference.get().getRecruitmentProcessId() != null) {
            UserPreference preference = userPreference.get();
            return preference.getPrivateJobRecruitmentProcessId();
        }
        return null;
    }

    @Override
    public void removeRecentUsedRecruitmentProcess(Long recruitmentProcessId) {
        if(recruitmentProcessId == null && recruitmentProcessId < 0){
            return;
        }
        userPreferenceRepository.disableRecruitmentProcessByRecruitmentProcessId(recruitmentProcessId);
    }

    @Override
    public UserPersonalizationConfig getPersonalizationConfig() {
        UserPreference userPreference = userPreferenceRepository.findByUserId(SecurityUtils.getUserId());
        UserPersonalizationConfig ret = userPreference != null ? JSONUtil.toBean(userPreference.getPersonalizationConfig(), UserPersonalizationConfig.class) : new UserPersonalizationConfig();
        if(ret != null) {
            filterPersonalizationConfig(ret);
        }
        log.info("Current user id :{}, personalization config:{}", SecurityUtils.getUserId(), JSONUtil.toJsonStr(ret));
        return ret != null ? ret : new UserPersonalizationConfig();
    }

    @Resource
    private InitiationService initiationService;

    @Resource
    private UserService userService;

    private void filterPersonalizationConfig(UserPersonalizationConfig ret) {
        //根据最新权限过滤
        Set<Long> pipelineUserIds = ret.getPipelineUserIds();
        if(pipelineUserIds == null) {
            return;
        }

        TeamDataPermissionRespDTO teamDataPermission = initiationService.initiateCandidatePipelineManagementDataPermissionByUserId(SecurityUtils.getTenantId(), SecurityUtils.getUserId()).getBody();
        if(teamDataPermission.getSelf()) {
            ret.setPipelineUserIds(new HashSet<>());
        } else if (teamDataPermission.getAll()) {
            return;
        } else {
            Set<Long> readableTeamIds = teamDataPermission.getReadableTeamIds();
            List<Long> readableUserIds = userService.findActiveUsersByPermissionTeamIdIn(readableTeamIds).stream().map(PermissionTeamUserDTO::getId).toList();

            ret.setPipelineUserIds(pipelineUserIds.stream().filter(readableUserIds::contains).collect(Collectors.toSet()));
        }
    }

    @Override
    public void updatePersonalizationConfig(UserPersonalizationConfig config) {
        UserPreference userPreference = userPreferenceRepository.findByUserId(SecurityUtils.getUserId());
        if(userPreference == null) {
            userPreference = new UserPreference();
            userPreference.setUserId(SecurityUtils.getUserId());
        }
        userPreference.setPersonalizationConfig(JSONUtil.toJsonStr(config));
        userPreferenceRepository.save(userPreference);
    }
}
