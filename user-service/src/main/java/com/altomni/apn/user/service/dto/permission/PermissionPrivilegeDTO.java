package com.altomni.apn.user.service.dto.permission;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.*;

/**
 * A leave function
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Accessors(chain = true)
public class PermissionPrivilegeDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String name;

    @JsonIgnore
    private String api;

    private JSONArray apiList;

    private Long parentId;

    private Boolean isLeaf;

    private Integer level;

    private Boolean isPublic;

    private List<Long> idPath = new LinkedList<>();

    @JsonIgnore
    private Integer sort;

    private List<PermissionPrivilegeDTO> children;

    public static Set<String> UpdateSkipProperties = new HashSet<>(Arrays.asList("children"));

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PermissionPrivilegeDTO role = (PermissionPrivilegeDTO) o;
        return id.equals(role.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

}
