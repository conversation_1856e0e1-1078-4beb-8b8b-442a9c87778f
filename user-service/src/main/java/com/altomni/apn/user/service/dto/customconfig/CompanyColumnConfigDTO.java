package com.altomni.apn.user.service.dto.customconfig;

import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;

public class CompanyColumnConfigDTO implements BaseConfig, Serializable {
    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private Long id;
    @JsonIgnore
    private Long userId;

    private UserCustomConfig customConfig;

    @JsonIgnore
    private Long recruitmentProcessId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    public Long getRecruitmentProcessId() {
        return recruitmentProcessId;
    }
    public void setRecruitmentProcessId(Long recruitmentProcessId) {
        this.recruitmentProcessId = recruitmentProcessId;
    }

    public UserCustomConfig getCustomConfig() {
        return customConfig;
    }

    public void setCustomConfig(UserCustomConfig customConfig) {
        this.customConfig = customConfig;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JobColumnConfigDTO jobColumnConfigDTO = (JobColumnConfigDTO) o;
        if (jobColumnConfigDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), jobColumnConfigDTO.getId());
    }

    @Override
    public String toString() {
        return "JobColumnConfig{" +
                "id=" + getId() +
                ", recruitmentProcessId=" + getUserId() +
                ", customConfig='" + getCustomConfig() +
                "}";
    }
}
