package com.altomni.apn.user.service.dto.user;


import com.altomni.apn.common.domain.enumeration.application.ActivityStatus;

import java.io.Serializable;

public class ActivityStats implements Serializable {

    private static final long serialVersionUID = 6633209022958652216L;

    private ActivityStatus status;

    private Integer count;

    public ActivityStats() { }

    public ActivityStats(ActivityStatus status, Integer count) {
        this.status = status;
        this.count = count;
    }

    public ActivityStatus getStatus() {
        return status;
    }

    public void setStatus(ActivityStatus status) {
        this.status = status;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    @Override
    public String toString() {
        return "ActivityStats{" +
            "status=" + status +
            ", count=" + count +
            '}';
    }
}
