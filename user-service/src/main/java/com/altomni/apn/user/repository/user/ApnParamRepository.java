package com.altomni.apn.user.repository.user;

import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.user.domain.user.ApnParam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the ApnParam entity.
 */
@Repository
public interface ApnParamRepository extends JpaRepository<ApnParam, Long> {

    ApnParam findByParamKeyAndTenantIdAndStatus(String paramKey, Long tenantId, Status status);

    ApnParam findByParamKeyAndStatus(String paramKey, Status status);

    List<ApnParam> findAllByParamKeyAndTenantIdAndStatus(String paramKey, Long tenantId, Status status);

    @Query(value = "update anp_param set param_value=?2 where param_name=?1 and tenant_id=?3",nativeQuery = true)
    Integer updateParamKey(String key,String value,Long tenant);
}
