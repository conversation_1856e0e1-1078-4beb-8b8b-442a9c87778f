package com.altomni.apn.user.service.permission;

import com.altomni.apn.common.dto.user.UserTeamPariDTO;
import com.altomni.apn.user.domain.permission.PermissionTeam;
import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamJobAndUserCountVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamUsersTransferPrimaryVM;
import com.altomni.apn.user.web.rest.vm.permission.PermissionTeamUsersTransferSecondaryVM;

import java.util.Collection;
import java.util.List;
import java.util.Set;

public interface PermissionUserTeamService {

    void addUsersToTeam(Set<Long> userIds, Long teamId);

    void removeUsersFromTeam(Set<Long> userIds, Long teamId);

    void primaryTeamUsersTransfer(PermissionTeamUsersTransferPrimaryVM transferVM);

    void secondaryTeamUsersTransfer(PermissionTeamUsersTransferSecondaryVM transferVM);

    PermissionUserTeam findPrimaryTeamByUserId(Long userId);

    PermissionTeamJobAndUserCountVM searchJobAndUserByTeamId(Long teamId);

    Long getSuperiorLeader(Long userId);

    Set<Long> getAllUpperTeamLeaders(Long userId);

    Long getLevel1TeamIdByUserId(Long userId);

    Long getLevel1TeamProfitLeaderByUserId(Long userId);

    List<PermissionTeam> getAllChildTeamsByParentTeamId(List<Long> parentTeamIds);

    List<Long> getUserIdFilterByTeamCategory(List<Long> userIds);

    Set<Long> getAllActiveUserIdsIncludingChildTeamUsersByTeamIds(List<Long> parentTeamIds);

    List<UserTeamPariDTO> getUserTeamPairs(Collection<Long> userIds);

}
