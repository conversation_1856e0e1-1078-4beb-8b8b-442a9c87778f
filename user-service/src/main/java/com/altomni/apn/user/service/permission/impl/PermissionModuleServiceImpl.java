package com.altomni.apn.user.service.permission.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.permission.PermissionModuleTable;
import com.altomni.apn.user.repository.permission.PermissionModuleRepository;
import com.altomni.apn.user.repository.permission.PermissionModuleTableRepository;
import com.altomni.apn.user.service.dto.permission.PermissionModuleDTO;
import com.altomni.apn.user.service.mapper.permission.ModuleMapper;
import com.altomni.apn.user.service.permission.PermissionModuleService;
import com.altomni.apn.user.web.rest.vm.permission.PermissionModuleTablesVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
public class PermissionModuleServiceImpl implements PermissionModuleService {

    @Resource
    private PermissionModuleRepository permissionModuleRepository;

    @Resource
    private PermissionModuleTableRepository permissionModuleTableRepository;

    @Resource
    private ModuleMapper moduleMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public PermissionModuleDTO create(PermissionModuleDTO permissionModuleDTO) {
        if (Objects.nonNull(permissionModuleDTO.getId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONMODULE_CREATE_INVALIDID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return moduleMapper.toDto(permissionModuleRepository.save(moduleMapper.toEntity(permissionModuleDTO)));
    }

    @Override
    public PermissionModuleDTO update(PermissionModuleDTO permissionModuleDTO) {
        if (Objects.isNull(permissionModuleDTO.getId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.PERMISSIONMODULE_CREATE_INVALIDID.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return moduleMapper.toDto(permissionModuleRepository.save(moduleMapper.toEntity(permissionModuleDTO)));
    }

    @Override
    public void delete(Long id) {
        permissionModuleRepository.deleteById(id);
    }

    @Override
    public List<PermissionModuleDTO> findAll() {
        return permissionModuleRepository.findAll().stream().map(module -> moduleMapper.toDto(module)).collect(Collectors.toList());
    }

    @Override
    public void setModuleTables(PermissionModuleTablesVM permissionModuleTablesVM) {
        permissionModuleTableRepository.deleteAllByModuleId(permissionModuleTablesVM.getModuleId());
        List<PermissionModuleTable> moduleTableList = permissionModuleTablesVM.getTableIds().stream().map(tableId ->
                new PermissionModuleTable(null, permissionModuleTablesVM.getModuleId(), tableId)).collect(Collectors.toList());
        permissionModuleTableRepository.saveAll(moduleTableList);
    }
}
