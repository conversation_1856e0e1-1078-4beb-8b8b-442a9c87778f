package com.altomni.apn.user.service.vo.customconfig;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.Instant;
import java.util.List;

/**
 * report E1ReportSearchFilter
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class ReportE1ReportFilterPreferenceVO implements Serializable {

    private Long id;

    private String titleName;

    private String searchFilter;

    private Instant createDate;

    private Long userId;

    private Long tenantId;

    List<ReportE1ReportFilterShareUserVO> shareUserList;

}
