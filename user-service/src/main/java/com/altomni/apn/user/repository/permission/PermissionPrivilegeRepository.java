package com.altomni.apn.user.repository.permission;

import com.altomni.apn.user.domain.permission.PermissionPrivilege;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
public interface PermissionPrivilegeRepository extends JpaRepository<PermissionPrivilege, Long> {

    List<PermissionPrivilege> findAllByIsPublicEqualsOrderByLevelDesc(Boolean isPublic);

    @Query(value = "SELECT p FROM PermissionPrivilege p" +
            " LEFT JOIN PermissionRolePrivilege rp ON rp.privilegeId = p.id" +
            " WHERE rp.roleId=:roleId")
    List<PermissionPrivilege> findAllByRoleId(@Param("roleId") Long roleId);

    @Query(value = """
    SELECT
	p.id
    FROM
	permission_privilege p
	LEFT JOIN permission_role_privilege rp ON rp.privilege_id = p.id
	LEFT JOIN role r ON rp.role_id = r.id
    WHERE
	r.`status` = 1
	AND rp.role_id IN (:roleIds)
       """,nativeQuery = true)
    Set<Long> getPermissionPrivilegeByRoles(@Param("roleIds") Set<Long> roleIds);

    @Query(value = "SELECT p.api FROM permission_privilege p " +
            " WHERE p.id in (:privilegeIds)", nativeQuery = true)
    Set<String> findApisByIdIn(@Param("privilegeIds") Set<Long> privilegeIds);

    @Query(value = "SELECT p.api FROM permission_privilege p " +
            " INNER JOIN permission_role_privilege rp ON rp.privilege_id = p.id" +
            " WHERE rp.role_id =:roleId", nativeQuery = true)
    Set<String> findPrivilegeApisByRoleId(@Param("roleId") Long roleId);

    @Query(value = "SELECT DISTINCT p.api FROM permission_privilege p " +
            " LEFT JOIN permission_role_privilege rp ON rp.privilege_id=p.id" +
            " LEFT JOIN user_admin_role ur ON ur.role_id= rp.role_id" +
            " WHERE ur.user_id =:userId and p.api is not null ",nativeQuery = true)
    Set<String> findPrivilegeApisByAdminUserId(@Param("userId") Long userId);

    @Query(value = "SELECT DISTINCT p.api FROM permission_privilege p " +
            " LEFT JOIN permission_role_privilege rp ON rp.privilege_id=p.id" +
            " LEFT JOIN user_role ur ON ur.role_id= rp.role_id" +
            " WHERE ur.user_id =:userId and p.api is not null ",nativeQuery = true)
    Set<String> findPrivilegeApisByUserId(@Param("userId") Long userId);

    @Query(value = "SELECT DISTINCT p.name FROM permission_privilege p " +
            " INNER JOIN permission_role_privilege rp ON rp.privilege_id=p.id" +
            " INNER JOIN user_role ur ON ur.role_id= rp.role_id" +
            " WHERE ur.user_id =:userId",nativeQuery = true)
    Set<String> findPrivilegeNamesByUserId(@Param("userId") Long userId);

    @Query(value = "SELECT p.api FROM permission_privilege p" +
            " WHERE p.controlled=0",nativeQuery = true)
    Set<String> findSkipCheckedPrivilegeApis();
}
