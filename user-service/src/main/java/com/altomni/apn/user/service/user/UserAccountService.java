package com.altomni.apn.user.service.user;

import com.altomni.apn.user.domain.enumeration.CreditEffectType;
import com.altomni.apn.user.domain.user.UserAccount;

/**
 * Service Interface for managing UserAccount.
 */
public interface UserAccountService {

    UserAccount findByUserId(Long userId);

    int updateById(Long id, Integer monthlyCredit, Integer bulkCredit, Integer effectCredit, CreditEffectType type, Integer version);

    UserAccount createUserAccount(Long userId, Long tenantId, Integer credit, Integer bullCredit,  Integer effectCredit, CreditEffectType type);

    UserAccount createTenantAdminUserAccount(Long userId, Long tenantId,Integer credit);

    UserAccount createLimitUserAccount(Long userId, Long tenantId);

    UserAccount initUserAccount(Long userId, Long tenantId);

    UserAccount initLimitUserAccount(Long userId, Long tenantId);

    UserAccount updateLimitUserAccount(UserAccount userAccount, Long tenantId);
}
