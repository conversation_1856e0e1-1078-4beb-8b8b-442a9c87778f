package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.service.dto.customconfig.UserSearchColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.UserSearchColumnConfigVO;

public interface UserLabelReportColumnConfigService {

    UserSearchColumnConfigVO searchConfigByUserId(Long userId, ConfigSubcategory configSubcategory);

    void updateConfigByUserId(Long userId, UserSearchColumnConfigDTO dto);

}
