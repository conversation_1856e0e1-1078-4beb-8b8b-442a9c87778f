package com.altomni.apn.user.repository.permission;

import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.user.domain.permission.PermissionExtraRoleTeam;
import com.altomni.apn.user.domain.permission.PermissionTeamLeaderProfit;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PermissionTeamLeaderProfitRepository extends JpaRepository<PermissionTeamLeaderProfit,Long> {

    Optional<PermissionTeamLeaderProfit> findByTeamId(Long teamId);

}
