package com.altomni.apn.user.service.user.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.dict.EnumCountry;
import com.altomni.apn.common.domain.user.*;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.service.cache.EnumCommonService;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.permission.PermissionUserTeam;
import com.altomni.apn.user.repository.permission.PermissionTeamRepository;
import com.altomni.apn.user.repository.permission.PermissionUserTeamRepository;
import com.altomni.apn.user.repository.user.*;
import com.altomni.apn.user.service.user.UserDeliveryLabelService;
import com.altomni.apn.user.web.rest.vm.user.DeliveryStatVM;
import com.altomni.apn.user.web.rest.vm.user.UserLabelVM;
import com.altomni.apn.user.web.rest.vm.user.UserProfileWithStatVM;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class UserDeliveryLabelServiceImpl implements UserDeliveryLabelService {

    @Resource
    UserRepository userRepository;
    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;
    @Resource
    UserApiPromptProperties userApiPromptProperties;
    @Resource
    UserDeliveryCountryRelationRepository userDeliveryCountryRelationRepository;
    @Resource
    UserDeliveryIndustryRelationRepository userDeliveryIndustryRelationRepository;
    @Resource
    UserDeliveryJobFunctionRelationRepository userDeliveryJobFunctionRelationRepository;
    @Resource
    UserDeliveryProcessRelationRepository userDeliveryProcessRelationRepository;
    @Resource
    EnumCommonService enumCommonService;
    @Resource
    PermissionTeamRepository permissionTeamRepository;
    @Resource
    PermissionUserTeamRepository permissionUserTeamRepository;

    private static volatile Map<Integer, String> countryMap = new HashMap<>();


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUserDeliveryLabel(Long userId, UserLabelVM vm) {
        User user = userRepository.findById(userId).orElse(null);
        if (user == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_UPDATEUSER_USERIDNOTEXIST.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(userId), userApiPromptProperties.getUserService()));
        }
        if (ChronoUnit.DAYS.between(user.getCreatedDate(), Instant.now()) > 180 &&
            (null != vm.getDeliveryLocations() ||
             null != vm.getDeliveryIndustries() ||
             null != vm.getDeliveryProcessIds() ||
             null != vm.getDeliveryJobFunctions())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_UPDATEUSER_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(userId), userApiPromptProperties.getUserService()));
        }

        Boolean updateUser = false;
        if (vm.getLevelOfExperience() != null) {
            user.setEnumLevelOfExperienceId(vm.getLevelOfExperience());
            updateUser = true;
        }
        if (vm.getLanguages() != null) {
            user.setUserLanguageRelation(vm.getLanguages());
            updateUser = true;
        }
        if (updateUser) {
            userRepository.save(user);
        }
        if (null != vm.getDeliveryLocations()) {
            List<UserDeliveryCountryRelation> saveList = new ArrayList<>();
            List<UserDeliveryCountryRelation> deleteList = new ArrayList<>();

            // 查出已存在的 countryRelation
            List<UserDeliveryCountryRelation> countryRelationList = userDeliveryCountryRelationRepository.findAllByUserId(userId);
            if (CollUtil.isNotEmpty(countryRelationList)) {
                countryRelationList.forEach(t -> {
                    // 当原有数据包含传参数据且原数据为非手动更新时，更新为手动更新
                    if (vm.getDeliveryLocations().contains(t.getEnumCountryId()) && !Boolean.TRUE.equals(t.getUpdated())) {
                        t.setUpdated(true);
                        saveList.add(t);
                    } else if (!vm.getDeliveryLocations().contains(t.getEnumCountryId()) && Boolean.TRUE.equals(t.getUpdated())) {
                        // 当原有数据不包含传参且原数据为手动更新时，当count为null则直接删除，否则更新为非手动更新即可
                        if (t.getCount() == null) {
                            deleteList.add(t);
                        } else {
                            t.setUpdated(false);
                            saveList.add(t);
                        }
                    }
                });
                Set<Integer> existCountryIds = countryRelationList.stream().map(UserDeliveryCountryRelation::getEnumCountryId).collect(Collectors.toSet());
                List<UserDeliveryCountryRelation> newRelations = vm.getDeliveryLocations().stream().filter(t -> !existCountryIds.contains(t)).map(d -> new UserDeliveryCountryRelation(null, userId, getEnumCountry().get(d), d, null, false, true)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(newRelations)) {
                    saveList.addAll(newRelations);
                }
            } else {
                saveList.addAll(vm.getDeliveryLocations().stream().map(d -> new UserDeliveryCountryRelation(null, userId, getEnumCountry().get(d), d, null, false, true)).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(saveList)){
                userDeliveryCountryRelationRepository.saveAll(saveList);
            }
            if (CollUtil.isNotEmpty(deleteList)){
                userDeliveryCountryRelationRepository.deleteAll(deleteList);
            }
        }
        if (null != vm.getDeliveryProcessIds()) {
            List<UserDeliveryProcessRelation> saveList = new ArrayList<>();
            List<UserDeliveryProcessRelation> deleteList = new ArrayList<>();
            // 查出已存在的 Relation
            List<UserDeliveryProcessRelation> relationList = userDeliveryProcessRelationRepository.findAllByUserId(userId);
            if (CollUtil.isNotEmpty(relationList)) {
                relationList.forEach(t -> {
                    // 当原有数据包含传参数据且原数据为非手动更新时，更新为手动更新
                    if (vm.getDeliveryProcessIds().contains(t.getProcessId()) && !Boolean.TRUE.equals(t.getUpdated())) {
                        t.setUpdated(true);
                        saveList.add(t);
                    } else if (!vm.getDeliveryProcessIds().contains(t.getProcessId()) && Boolean.TRUE.equals(t.getUpdated())) {
                        // 当原有数据不包含传参且原数据为手动更新时，当count为null则直接删除，否则更新为非手动更新即可
                        if (t.getCount() == null) {
                            deleteList.add(t);
                        } else {
                            t.setUpdated(false);
                            saveList.add(t);
                        }
                    }
                });
                Set<Long> existIds = relationList.stream().map(UserDeliveryProcessRelation::getProcessId).collect(Collectors.toSet());
                List<UserDeliveryProcessRelation> newRelations = vm.getDeliveryProcessIds().stream().filter(t -> !existIds.contains(t)).map(d -> new UserDeliveryProcessRelation(null, userId, d, null, false, true)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(newRelations)) {
                    saveList.addAll(newRelations);
                }
            } else {
                saveList.addAll(vm.getDeliveryProcessIds().stream().map(d -> new UserDeliveryProcessRelation(null, userId, d, null, false, true)).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(saveList)) {
                userDeliveryProcessRelationRepository.saveAll(saveList);
            }
            if (CollUtil.isNotEmpty(deleteList)) {
                userDeliveryProcessRelationRepository.deleteAll(deleteList);
            }
        }

        if (null != vm.getDeliveryIndustries()) {
            List<UserDeliveryIndustryRelation> saveList = new ArrayList<>();
            List<UserDeliveryIndustryRelation> deleteList = new ArrayList<>();
            // 查出已存在的 Relation
            List<UserDeliveryIndustryRelation> relationList = userDeliveryIndustryRelationRepository.findAllByUserId(userId);
            if (CollUtil.isNotEmpty(relationList)) {
                relationList.forEach(t -> {
                    // 当原有数据包含传参数据且原数据为非手动更新时，更新为手动更新
                    if (vm.getDeliveryIndustries().contains(t.getEnumIndustryMappingId()) && !Boolean.TRUE.equals(t.getUpdated())) {
                        t.setUpdated(true);
                        saveList.add(t);
                    } else if (!vm.getDeliveryIndustries().contains(t.getEnumIndustryMappingId()) && Boolean.TRUE.equals(t.getUpdated())) {
                        // 当原有数据不包含传参且原数据为手动更新时，当count为null则直接删除，否则更新为非手动更新即可
                        if (t.getCount() == null) {
                            deleteList.add(t);
                        } else {
                            t.setUpdated(false);
                            saveList.add(t);
                        }
                    }
                });
                Set<Long> existIds = relationList.stream().map(UserDeliveryIndustryRelation::getEnumIndustryMappingId).collect(Collectors.toSet());
                List<UserDeliveryIndustryRelation> newRelations = vm.getDeliveryIndustries().stream().filter(t -> !existIds.contains(t)).map(d -> new UserDeliveryIndustryRelation(null, userId, d, null, false, true)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(newRelations)) {
                    saveList.addAll(newRelations);
                }
            } else {
                saveList.addAll(vm.getDeliveryIndustries().stream().map(d -> new UserDeliveryIndustryRelation(null, userId, d, null, false, true)).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(saveList)) {
                userDeliveryIndustryRelationRepository.saveAll(saveList);
            }
            if (CollUtil.isNotEmpty(deleteList)) {
                userDeliveryIndustryRelationRepository.deleteAll(deleteList);
            }
        }

        if (null != vm.getDeliveryJobFunctions()) {
            List<UserDeliveryJobFunctionRelation> saveList = new ArrayList<>();
            List<UserDeliveryJobFunctionRelation> deleteList = new ArrayList<>();
            // 查出已存在的 Relation
            List<UserDeliveryJobFunctionRelation> relationList = userDeliveryJobFunctionRelationRepository.findAllByUserId(userId);
            if (CollUtil.isNotEmpty(relationList)) {
                relationList.forEach(t -> {
                    // 当原有数据包含传参数据且原数据为非手动更新时，更新为手动更新
                    if (vm.getDeliveryJobFunctions().contains(t.getEnumJobFunctionMappingId()) && !Boolean.TRUE.equals(t.getUpdated())) {
                        t.setUpdated(true);
                        saveList.add(t);
                    } else if (!vm.getDeliveryJobFunctions().contains(t.getEnumJobFunctionMappingId()) && Boolean.TRUE.equals(t.getUpdated())) {
                        // 当原有数据不包含传参且原数据为手动更新时，当count为null则直接删除，否则更新为非手动更新即可
                        if (t.getCount() == null) {
                            deleteList.add(t);
                        } else {
                            t.setUpdated(false);
                            saveList.add(t);
                        }
                    }
                });
                Set<Long> existIds = relationList.stream().map(UserDeliveryJobFunctionRelation::getEnumJobFunctionMappingId).collect(Collectors.toSet());
                List<UserDeliveryJobFunctionRelation> newRelations = vm.getDeliveryJobFunctions().stream().filter(t -> !existIds.contains(t)).map(d -> new UserDeliveryJobFunctionRelation(null, userId, d, null, false, true)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(newRelations)) {
                    saveList.addAll(newRelations);
                }
            } else {
                saveList.addAll(vm.getDeliveryJobFunctions().stream().map(d -> new UserDeliveryJobFunctionRelation(null, userId, d, null, false, true)).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(saveList)) {
                userDeliveryJobFunctionRelationRepository.saveAll(saveList);
            }
            if (CollUtil.isNotEmpty(deleteList)) {
                userDeliveryJobFunctionRelationRepository.deleteAll(deleteList);
            }
        }
    }


    @Override
    public UserProfileWithStatVM getUserPersonalProfileById(Long userId) {
        User user = userRepository.findById(userId).orElseThrow();
        PermissionUserTeam prePrimaryTeam = permissionUserTeamRepository.findFirstByUserIdAndIsPrimary(userId, Boolean.TRUE).orElseThrow(() -> new CustomParameterizedException("Invalid primary team!"));
        UserProfileWithStatVM result = new UserProfileWithStatVM();
        result.setUserInfo(user);
        JSONObject primaryTeamObject = permissionTeamRepository.getAllTeamHierarchy(List.of(prePrimaryTeam.getTeamId()))
                .stream().map(t -> JSONUtil.createObj()
                        .put("id", t.get(0, BigInteger.class).longValue())
                        .put("name", t.get(1, String.class))).findFirst().orElseThrow(() -> new CustomParameterizedException("Invalid primary team!"));
        result.setPrimaryTeam(primaryTeamObject);
        getDeliveryLocationStat(userId).ifPresent(result::setDeliveryLocationStat);
        handleLocationStat(result);
        getDeliveryProcessIdStat(userId).ifPresent(result::setDeliveryProcessStat);
        getDeliveryIndustryStat(userId).ifPresent(result::setDeliveryIndustryStat);
        getDeliveryJobFunctionStat(userId).ifPresent(result::setDeliveryJobFunctionStat);
        return result;
    }

    private void handleLocationStat(UserProfileWithStatVM result) {
        if(CollUtil.isNotEmpty(result.getDeliveryLocationStat())){
            result.getDeliveryLocationStat().sort(
                    Comparator.comparing(DeliveryStatVM::getCount, Comparator.nullsLast(Comparator.reverseOrder()))
                            .thenComparing(DeliveryStatVM::getName, Comparator.nullsLast(Comparator.naturalOrder()))
            );
            if (CollUtil.isNotEmpty(result.getDeliveryLocationStat()) && result.getDeliveryLocationStat().size() > 10) {
                // 大于十个，就取第十个开始到最后加在一起作为OTHERS返回
                List<DeliveryStatVM> deliveryLocationStat = result.getDeliveryLocationStat();
                // 获取第10个及以后的元素总和
                Long othersCount = deliveryLocationStat.stream()
                        .skip(9)
                        .mapToLong(DeliveryStatVM::getCount)
                        .sum();

                // 创建"Others"项并加入
                DeliveryStatVM othersStat = new DeliveryStatVM();
                othersStat.setId(-3L);
                othersStat.setName("OTHERS");
                othersStat.setCount(othersCount);

                // 更新原列表
                List<DeliveryStatVM> topTenStats = deliveryLocationStat.stream()
                        .limit(9)
                        .collect(Collectors.toList());

                topTenStats.add(othersStat);

                // 更新结果
                result.setDeliveryLocationStat(topTenStats);
            }
        }

    }

    private Optional<List<DeliveryStatVM>> getDeliveryLocationStat(Long userId) {
        List<UserDeliveryCountryRelation> deliveryCountryStat = userDeliveryCountryRelationRepository.findAllByUserId(userId);
        return Optional.of(deliveryCountryStat.stream().filter(d-> d.getCount() != null).map(d -> new DeliveryStatVM(d.getEnumCountryId().longValue(), d.getCount(), d.getOfficialCountry())).collect(Collectors.toList()));
    }

    private Optional<List<DeliveryStatVM>> getDeliveryProcessIdStat(Long userId) {
        List<UserDeliveryProcessRelation> deliveryCountryStat = userDeliveryProcessRelationRepository.findAllByUserId(userId);
        return Optional.of(deliveryCountryStat.stream().filter(d-> d.getCount() != null).map(d -> new DeliveryStatVM(d.getProcessId(), d.getCount())).collect(Collectors.toList()));
    }

    private Optional<List<DeliveryStatVM>> getDeliveryIndustryStat(Long userId) {
        List<UserDeliveryIndustryRelation> deliveryCountryStat = userDeliveryIndustryRelationRepository.findAllByUserId(userId);
        return Optional.of(deliveryCountryStat.stream().filter(d-> d.getCount() != null).map(d -> new DeliveryStatVM(d.getEnumIndustryMappingId(), d.getCount())).collect(Collectors.toList()));
    }

    private Optional<List<DeliveryStatVM>> getDeliveryJobFunctionStat(Long userId) {
        List<UserDeliveryJobFunctionRelation> deliveryCountryStat = userDeliveryJobFunctionRelationRepository.findAllByUserId(userId);
        return Optional.of(deliveryCountryStat.stream().filter(d-> d.getCount() != null).map(d -> new DeliveryStatVM(d.getEnumJobFunctionMappingId(), d.getCount())).collect(Collectors.toList()));
    }


    private Map<Integer, String> getEnumCountry() {
        if (countryMap.isEmpty()) {
            synchronized (UserDeliveryLabelServiceImpl.class) {
                if (countryMap.isEmpty()) {
                    List<EnumCountry> countryList = enumCommonService.findAllEnumAreaCode();
                    if (CollUtil.isNotEmpty(countryList)) {
                        countryMap = countryList.stream().filter(t -> StringUtils.isNotEmpty(t.getEnDisplay())).collect(Collectors.toMap(EnumCountry::getId, EnumCountry::getName));
                    }
                }
            }
        }
        return countryMap;
    }
}
