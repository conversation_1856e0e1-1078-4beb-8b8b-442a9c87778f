package com.altomni.apn.user.service.dto.customconfig;


import java.io.Serializable;

public class CustomColumnField implements Serializable {

    private String field;
    private String cnDisplay;
    private String enDisplay;
    private boolean search;
    private boolean visible;
    private boolean sortable;
    private boolean active;

    private Integer width;

    public CustomColumnField(){

    }

    public String getField() {
        return field;
    }

    public void setField(String field) {
        this.field = field;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public boolean isSortable() {
        return sortable;
    }

    public void setSortable(boolean sortable) {
        this.sortable = sortable;
    }

    public String getCnDisplay() {
        return cnDisplay;
    }

    public void setCnDisplay(String cnDisplay) {
        this.cnDisplay = cnDisplay;
    }

    public String getEnDisplay() {
        return enDisplay;
    }

    public void setEnDisplay(String enDisplay) {
        this.enDisplay = enDisplay;
    }

    public boolean isActive() {
        return active;
    }

    public void setActive(boolean active) {
        this.active = active;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public boolean isSearch() {
        return search;
    }

    public void setSearch(boolean search) {
        this.search = search;
    }
}
