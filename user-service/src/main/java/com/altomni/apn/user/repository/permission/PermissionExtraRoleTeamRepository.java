package com.altomni.apn.user.repository.permission;

import com.altomni.apn.common.enumeration.permission.Module;
import com.altomni.apn.user.domain.permission.PermissionExtraRoleTeam;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the JobNote entity.
 */
@SuppressWarnings("unused")
@Repository
public interface PermissionExtraRoleTeamRepository extends JpaRepository<PermissionExtraRoleTeam,Long> {
    @Query(nativeQuery = true,
            value = "select team_id from permission_extra_role_team rd " +
                    " left join role r on r.id = rd.role_id" +
                    " left join user_role ur on ur.role_id=r.id" +
                    " where ur.user_id=:userId")
    Set<Long> getAllTeamIdsFromExtraRoleByUserId(@Param("userId") Long userId);

    @Query(nativeQuery = true,
            value = "select team_id from permission_extra_role_team rd " +
                    " left join role r on r.id = rd.role_id" +
                    " left join user_role ur on ur.role_id=r.id" +
                    " where ur.user_id=:userId and rd.writable=1")
    Set<Long> getWritableTeamIdsFromExtraRoleByUserId(@Param("userId") Long userId);

    void deleteAllByRoleId(Long roleId);

    List<PermissionExtraRoleTeam> findAllByRoleIdAndModule(Long roleId, Module module);


    List<PermissionExtraRoleTeam> findAllByRoleIdIn(List<Long> roleId);
}
