package com.altomni.apn.user.service.user.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.domain.user.QUser;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.user.QTeamUser;
import com.altomni.apn.user.domain.user.Team;
import com.altomni.apn.user.domain.user.TeamUser;
import com.altomni.apn.user.repository.user.TeamRepository;
import com.altomni.apn.user.repository.user.TeamUserRepository;
import com.altomni.apn.user.repository.user.UserRepository;
import com.altomni.apn.user.service.dto.user.UserInTeam;
import com.altomni.apn.user.service.user.TeamUserService;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPAExpressions;
import com.querydsl.jpa.impl.JPAQueryFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
public class TeamUserServiceImpl implements TeamUserService {

    @Resource
    private JPAQueryFactory queryFactory;

    @Resource
    private TeamRepository teamRepository;

    @Resource
    private TeamUserRepository teamUserRepository;

    @Resource
    private UserRepository userRepository;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

    @Override
    public Team addUser(Long teamId, Long userId) {
        Team team = teamRepository.findById(teamId).orElseThrow();
        User user = userRepository.findById(userId).orElseThrow();
        checkPermission(team, user);
        TeamUser teamUser = teamUserRepository.findByTeamIdAndUserId(teamId, userId);
        if (teamUser == null) {
            teamUserRepository.saveAndFlush(new TeamUser().teamId(teamId).userId(userId));
        }
        return this.get(teamId);
    }

    @Override
    public Team multiAddUsers(Long teamId, List<Long> userIds) {
        Team team = teamRepository.findById(teamId).orElseThrow();
        if (team == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_TEAMUSER_MULTIADDUSERS_TEAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if (CollectionUtils.isNotEmpty(userIds)) {
            for (Long userId : userIds) {
                TeamUser teamUser = teamUserRepository.findByTeamIdAndUserId(teamId, userId);
                if (teamUser == null) {
                    teamUserRepository.saveAndFlush(new TeamUser().teamId(teamId).userId(userId));
                }
            }
        }
        return this.get(teamId);
    }

    @Override
    public Team removeUser(Long teamId, Long userId) {
        Team team = teamRepository.findById(teamId).orElseThrow();
        if (team == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_TEAMUSER_MULTIADDUSERS_TEAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if (userId != null) {
            TeamUser teamUser = teamUserRepository.findByTeamIdAndUserId(teamId, userId);
            if (teamUser != null) {
                teamUserRepository.delete(teamUser);
            }
        }
        return this.get(teamId);
    }

    public Team get(Long teamId) {
        Team team = teamRepository.findById(teamId).orElseThrow();
        if (team == null) {
            return null;
        }
        if (!team.getTenantId().equals(SecurityUtils.getTenantId())) {
            return null;
        }
        QTeamUser teamUser = QTeamUser.teamUser;
        QUser user = QUser.user;
        BooleanExpression usersExp = user.id.in(
            JPAExpressions.select(teamUser.userId).from(teamUser).where(teamUser.teamId.eq(team.getId())))
            .and(user.activated.eq(true));
        team.users = queryFactory.select(Projections.constructor(UserInTeam.class, user.id, user.email, user.username, user.firstName, user.lastName))
            .from(user).where(usersExp).fetch();
        return team;
    }

    private void checkPermission(Team team, User user) {
        if (team == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_TEAMUSER_MULTIADDUSERS_TEAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if (user == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_TEAMUSER_CHECKPERMISSION_USERNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        Long tenantId = SecurityUtils.getTenantId();
        if (!team.getTenantId().equals(tenantId) ||
            !user.getTenantId().equals(tenantId)) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_TEAMUSER_CHECKPERMISSION_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
    }

    @Override
    public Set<Long> getAllTeamUsersByLeader(Long leaderUserId) {
        List<Team> teams = teamRepository.findAllByLeaderUserId(leaderUserId);
        Set<Long> result  = new HashSet<>();
        if (CollectionUtils.isNotEmpty(teams)) {
            teams.forEach(team -> {
                List<TeamUser> teamUsers = teamUserRepository.findByTeamId(team.getId());
                if (CollectionUtils.isNotEmpty(teamUsers)) {
                    result.addAll(teamUsers.stream().map(TeamUser::getUserId).collect(Collectors.toSet()));
                }
            });
        }
        return result;
    }

    @Override
    public Set<Long> getAllTeamUsersByTeamId(Long teamId) {
        List<TeamUser> teamUsers = teamUserRepository.findByTeamId(teamId);
        Set<Long> result  = new HashSet<>();
        if (CollectionUtils.isNotEmpty(teamUsers)) {
            result.addAll(teamUsers.stream().map(TeamUser::getUserId).collect(Collectors.toSet()));
        }
        return result;
    }

    @Override
    public Team replaceUsers(Long teamId, List<Long> userIds) {
        Team team = teamRepository.findById(teamId).orElseThrow();
        if (team == null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_TEAMUSER_MULTIADDUSERS_TEAMNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if (CollectionUtils.isNotEmpty(userIds)) {
            teamUserRepository.deleteAllByTeamId(teamId);
            List<TeamUser> userList = new ArrayList<>();
            userIds.forEach(userId -> userList.add(new TeamUser().teamId(teamId).userId(userId)));
            teamUserRepository.saveAll(userList);
        }
        return this.get(teamId);
    }
}
