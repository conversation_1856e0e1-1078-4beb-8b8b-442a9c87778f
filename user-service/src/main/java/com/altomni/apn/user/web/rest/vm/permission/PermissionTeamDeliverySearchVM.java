package com.altomni.apn.user.web.rest.vm.permission;

import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class PermissionTeamDeliverySearchVM {
    private Long teamId;
    private List<Long> userIds;
    private Set<Long> languages;
    private Set<Long> levelOfExperiences;
    private Set<Integer> deliveryLocations;
    private Set<Long> deliveryProcessIds;
    private Set<Long> deliveryIndustries;
    private Set<Long> deliveryJobFunctions;
    //是否展示全部成员 true: 展示全部成员； false: 展示直系成员
    private Boolean showAllMembers = true;
    private Boolean activated;
    /**
     * 团队类别
     */
    private Set<Integer> teamCategoryIds;
}
