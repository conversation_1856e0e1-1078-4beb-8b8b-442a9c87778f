package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.UserBasic;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface UserBasicRepository extends JpaRepository<UserBasic, Long> {

    List<UserBasic> findALLByTenantIdAndActivatedIsTrue(Long tenantId);

}
