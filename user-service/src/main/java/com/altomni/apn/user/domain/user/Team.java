package com.altomni.apn.user.domain.user;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import com.altomni.apn.user.service.dto.user.UserInTeam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.hibernate.validator.constraints.NotBlank;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@ApiModel(description = "Team is used to organize users in a tenant")
@Entity
@Table(name = "team")
public class Team extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;


    @ApiModelProperty(value = "The team name. It should be unique in a tenant. Max length is 40.")
    @Column(name = "name")
    @Size(max = 40)
    @NotBlank
    private String name;

    @ApiModelProperty(value = "Leader user id")
    @Column(name = "leader_user_id")
    private Long leaderUserId;

    @ApiModelProperty(value = "Optional description for the team")
    @Column(name = "description")
    private String description;

    @ApiModelProperty(value = "The tenant id the team belongs to.")
    @Column(name = "tenant_id", nullable = false)
    private Long tenantId;

    @Transient
    public List<UserInTeam> users = new ArrayList<>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getLeaderUserId() {
        return leaderUserId;
    }

    public void setLeaderUserId(Long leaderUserId) {
        this.leaderUserId = leaderUserId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Long getTenantId() {
        return tenantId;
    }

    public void setTenantId(Long tenantId) {
        this.tenantId = tenantId;
    }

    @Override
    public String toString() {
        return "Team{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", leaderUserId=" + leaderUserId +
                ", description='" + description + '\'' +
                ", tenantId=" + tenantId +
                ", users=" + users +
                '}';
    }
}
