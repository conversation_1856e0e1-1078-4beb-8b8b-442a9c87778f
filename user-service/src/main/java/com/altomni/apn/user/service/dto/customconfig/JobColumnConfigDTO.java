package com.altomni.apn.user.service.dto.customconfig;

import java.io.Serializable;
import java.util.List;
import java.util.Objects;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.fasterxml.jackson.annotation.JsonIgnore;

public class JobColumnConfigDTO implements BaseConfig, Serializable {
    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private Long id;
    @JsonIgnore
    private Long userId;
    //private List<CustomColumnField> customConfig;

    private UserCustomConfig customConfig;



    @JsonIgnore
    private Long recruitmentProcessId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }
    public Long getRecruitmentProcessId() {
        return recruitmentProcessId;
    }
    public void setRecruitmentProcessId(Long recruitmentProcessId) {
        this.recruitmentProcessId = recruitmentProcessId;
    }

//    public List<CustomColumnField> getCustomConfig() {
//        return customConfig;
//    }
//
//    public void setCustomConfig(List<CustomColumnField> customConfig) {
//        this.customConfig = customConfig;
//    }


    public UserCustomConfig getCustomConfig() {
        return customConfig;
    }

    public void setCustomConfig(UserCustomConfig customConfig) {
        this.customConfig = customConfig;
    }

    //    public Class<?> getCustomColumnFieldType() {
//        // You can return either CustomField1.class or CustomField2.class, depending on the actual type of elements in the customConfig list
//        return CustomColumnField.class;
//    }
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        JobColumnConfigDTO jobColumnConfigDTO = (JobColumnConfigDTO) o;
        if (jobColumnConfigDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), jobColumnConfigDTO.getId());
    }

    @Override
    public String toString() {
        return "JobColumnConfig{" +
                "id=" + getId() +
                ", recruitmentProcessId=" + getUserId() +
                ", customConfig='" + getCustomConfig() +
                "}";
    }
}
