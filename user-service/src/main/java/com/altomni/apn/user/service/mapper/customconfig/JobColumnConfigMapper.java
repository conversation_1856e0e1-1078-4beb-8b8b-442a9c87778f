package com.altomni.apn.user.service.mapper.customconfig;

import com.altomni.apn.user.domain.customconfig.JobColumnConfig;
import com.altomni.apn.user.domain.customconfig.JobFormConfig;
import com.altomni.apn.user.domain.customconfig.UserPreference;
import com.altomni.apn.user.service.dto.customconfig.JobColumnConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.JobFormConfigDTO;
import com.altomni.apn.user.service.mapper.EntityMapper;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

//@Mapper(componentModel = "spring", uses = JsonMapperUtil.class)
//public interface JobColumnConfigMapper{// implements EntityMapper<JobColumnConfigDTO, JobColumnConfig> {
//    JobColumnConfig fromId(Long id) {
//        if (id == null) {
//            return null;
//        }
//        JobColumnConfig jobColumnConfig = new JobColumnConfig();
//        jobColumnConfig.setId(id);
//        return jobColumnConfig;
//    }
//
//    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "customFieldListToJsonString")
//    JobColumnConfig toEntity(JobColumnConfigDTO dto);
//
//    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "jsonStringToCustomFieldList")
//    JobColumnConfigDTO toDto(JobColumnConfig entity);
//
//    @AfterMapping
//    public void customConfigToEntity(JobColumnConfigDTO dto, @MappingTarget JobColumnConfig entity) {
//        entity.setCustomConfig(JsonMapperUtil.customFieldListToJsonString(dto.getCustomConfig()));
//    }
//
//    @AfterMapping
//    public void customConfigToDto(JobColumnConfig entity, @MappingTarget JobColumnConfigDTO dto) {
//        dto.setCustomConfig(JsonMapperUtil.jsonStringToCustomFieldList(entity.getCustomConfig(), dto.getCustomColumnFieldType()));
//    }
//}

@Mapper(componentModel = "spring", uses = JsonMapperUtil.class)
public interface JobColumnConfigMapper {

    default UserPreference fromId(Long id) {
        if (id == null) {
            return null;
        }
        UserPreference userPreference = new UserPreference();
        userPreference.setId(id);
        return userPreference;
    }

//    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "customColumnFieldListToJsonString")
//    JobColumnConfig toEntity(JobColumnConfigDTO dto);
//
//    @Mapping(target = "customConfig", source = "customConfig", qualifiedByName = "jsonStringToCustomColumnFieldList")
//    JobColumnConfigDTO toDto(JobColumnConfig entity);

    @Mapping(target = "jobColumnConfig", source = "customConfig", qualifiedByName = "customColumnConfigToJsonString")
    UserPreference toEntity(JobColumnConfigDTO dto);

    @Mapping(target = "customConfig", source = "jobColumnConfig", qualifiedByName = "jsonStringToCustomColumnConfig")
    JobColumnConfigDTO toDto(UserPreference entity);

    @Mapping(target = "customConfig", source = "privateJobColumnConfig", qualifiedByName = "jsonStringToCustomColumnConfig")
    JobColumnConfigDTO toDtoForPrivateJob(UserPreference entity);

}