package com.altomni.apn.user.web.rest.system;

import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.user.service.system.StatisticService;
import com.altomni.apn.user.web.rest.vm.system.OnlineTenantVM;
import com.altomni.apn.user.web.rest.vm.system.OnlineUserVM;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ExecutionException;

@Slf4j
@RestController
@RequestMapping("/api/v3")
public class StatisticResource {

    private static final String ENTITY_NAME = "StatisticResource";

    @Resource
    private StatisticService statisticService;

    @GetMapping("/statistic/tenants/{tenantId}/online-users/seconds-in/{seconds}/count")
    public ResponseEntity<Long> countAllOnlineUsersByTenantId(@PathVariable("tenantId") Long tenantId, @PathVariable("seconds") Integer seconds) {
        log.info("request to count online users by tenant ID: {}", tenantId);
        return ResponseEntity.ok(statisticService.countAllOnlineUsersByTenantIdAndSecondsIn(tenantId, seconds));
    }

    @GetMapping("/statistic/online-users/seconds-in/{seconds}/count")
    public ResponseEntity<Long> countAllOnlineUsers(@PathVariable("seconds") Integer seconds) {
        log.info("request to count all online users");
        return ResponseEntity.ok(statisticService.countAllOnlineUsersAndSecondsIn(seconds));
    }

    @GetMapping("/statistic/tenants/{tenantId}/online-users/seconds-in/{seconds}")
    public ResponseEntity<List<OnlineUserVM>> getAllOnlineUsersByTenantId(@PathVariable("tenantId") Long tenantId, @PathVariable("seconds") Integer seconds) throws ExecutionException, InterruptedException {
        log.info("request to get online users by tenant ID: {}", tenantId);
        return ResponseEntity.ok(statisticService.getOnlineUsersByTenantIdAndSecondsIn(tenantId, seconds));
    }

//    @GetMapping("/statistic/tenants/online-users/seconds-in/{seconds}/count")
//    @NoRepeatSubmit
//    public ResponseEntity<List<OnlineTenantVM>> getAllOnlineUsersCountGroupByTenant(@PathVariable("seconds") Integer seconds) {
//        log.info("request to get all online users count group by tenant");
//        return ResponseEntity.ok(statisticService.getAllOnlineUsersSecondsInAndGroupByTenant(seconds));
//    }
}
