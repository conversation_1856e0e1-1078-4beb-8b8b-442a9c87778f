package com.altomni.apn.user.web.rest.vm.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.altomni.apn.common.domain.user.User;
import com.altomni.apn.common.domain.user.UserLanguageRelation;
import com.altomni.apn.common.utils.CommonUtils;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class UserProfileWithStatVM implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String firstName;

    private String lastName;

    private String fullName;

    private String jobTitle;

    private Long levelOfExperience;

    private List<Long> languages;

    private String email;

    private String imageUrl;


    private JSONObject primaryTeam;

    private List<JSONObject> authorities;

    private List<DeliveryStatVM> deliveryLocationStat;

    private List<DeliveryStatVM> deliveryProcessStat;

    private List<DeliveryStatVM> deliveryIndustryStat;

    private List<DeliveryStatVM> deliveryJobFunctionStat;

    public void setUserInfo(User user) {
        this.id = user.getId();
        this.firstName = user.getFirstName();
        this.lastName = user.getLastName();
        this.fullName = CommonUtils.formatFullName(user.getFirstName(), user.getLastName());
        this.jobTitle = user.getJobTitle();
        this.levelOfExperience = user.getEnumLevelOfExperienceId();
        this.languages = CollUtil.isNotEmpty(user.getLanguageRelations()) ? user.getLanguageRelations().stream().map(UserLanguageRelation::getEnumLanguageId).toList() : null;
        this.email = user.getEmail();
        this.authorities = CollUtil.isNotEmpty(user.getRoles()) ? user.getRoles().stream().map(role -> JSONUtil.createObj().put("name", role.getName()).put("id", role.getId())).collect(Collectors.toList()) : null;
        this.imageUrl = user.getImageUrl();
    }

}
