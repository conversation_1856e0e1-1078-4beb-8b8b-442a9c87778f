package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.user.service.dto.customconfig.CalendarColumnConfigDTO;
import com.altomni.apn.user.service.vo.customconfig.CalendarColumnConfigVO;

public interface CalendarColumnConfigService {

    CalendarColumnConfigVO searchCalendarColumnConfigByUserId(Long userId);

    void updateCalendarColumnConfigByUserId(Long userId, CalendarColumnConfigDTO dto);

}
