package com.altomni.apn.user.repository.permission;

import com.altomni.apn.common.domain.user.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Set;

/**
 * Spring Data JPA repository for the {@link Role} entity.
 */
public interface PermissionRoleRepository extends JpaRepository<Role, Long> {
    @Query(value = "SELECT ur.roleId FROM UserRole ur" +
            " WHERE ur.userId=:userId")
    List<Long> findRoleIdsByUserId(@Param("userId") Long userId);

    @Query(value = "SELECT r FROM Role r" +
            " LEFT JOIN UserRole ur on ur.roleId=r.id" +
            " WHERE ur.userId=:userId")
    List<Role> findRolesByUserId(@Param("userId") Long userId);

    Role findFirstByNameAndInternal(String name, Boolean isInternal);

    Set<Role> findByName(String name);

    @Query(value = "select max(r.data_scope) from role r" +
            " left join user_role ur on ur.role_id=r.id" +
            " where ur.user_id=:userId", nativeQuery = true)
    Integer getMaxDataScopeByUserId(@Param("userId") Long userId);

    @Modifying
    @Query("update Role r set r.dataScope=:dataScope, r.clientContactDataScope=:clientContactDataScope, r.reportDataScope=:reportDataScope,r.homeAndCalendarDataScope=:homeAndCalendarDataScope,r.candidatePipelineManagementDataScope=:candidatePipelineManagementDataScope,r.chinaInvoicingDataScope=:chinaInvoicingDataScope where r.id=:roleId")
    void updateRoleDataScopeById(@Param("dataScope") Integer dataScope, @Param("clientContactDataScope") Integer clientContactDataScope, @Param("reportDataScope") Integer reportDataScope,@Param("homeAndCalendarDataScope") Integer homeAndCalendarDataScope,@Param("candidatePipelineManagementDataScope") Integer candidatePipelineManagementDataScope,@Param("chinaInvoicingDataScope") Integer chinaInvoicingDataScope, @Param("roleId") Long roleId);

//    @Modifying
//    @Query("update Role r set r.clientContactDataScope=:dataScope where r.id=:roleId")
//    void updateRoleClientContactDataScopeById(@Param("dataScope") Integer dataScope, @Param("roleId") Long roleId);


    List<Role> findByTenantIdAndInternalOrderByIdDesc(Long tenantId, Boolean isInterval);

    @Query(value = """
             select ur.user_id, JSON_ARRAYAGG(JSON_OBJECT('name', r.name)) as roles
             from user_role ur
             inner join role r on ur.role_id = r.id
             where r.tenant_id=:tenantId
             and ur.user_id in :userIds
             group by ur.user_id 
             """, nativeQuery = true)
    List<Object[]> findRolesGroupByUserId(@Param("tenantId") Long tenantId, @Param("userIds") List<Long> userIds);

    @Query(value = "select ur.user_id, JSON_ARRAYAGG(JSON_OBJECT('name', r.name)) as roles " +
            " from user_role ur " +
            " inner join role r on ur.role_id = r.id " +
            " where ur.user_id in :userIds " +
            " group by ur.user_id", nativeQuery = true)
    List<Object[]> findRolesGroupByUserIdAndUserIdIn(@Param("userIds") Set<Long> userIds);

    @Query(value = "select u.id from user u " +
            " left join user_role ur on ur.user_id=u.id" +
            " where ur.role_id=:roleId", nativeQuery = true)
    List<Long> findUserIdsByRoleId(@Param("roleId") Long roleId);

    @Modifying
    @Query("update Role set status=:status where id =:roleId")
    void updateStatus(@Param("roleId") Long roleId, @Param("status") Boolean status);
}
