package com.altomni.apn.user.service.customconfig.impl;

import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.config.application.ApplicationIPGProperties;
import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.errors.ForbiddenException;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.common.utils.SecurityUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.customconfig.PipelineColumnPreference;
import com.altomni.apn.user.domain.enumeration.Category;
import com.altomni.apn.user.domain.enumeration.ConfigSubcategory;
import com.altomni.apn.user.repository.customconfig.PipelineColumnPreferenceRepository;
import com.altomni.apn.user.service.customconfig.PipelineColumnPreferenceService;
import com.altomni.apn.user.service.customconfig.SystemConfigDefaultService;
import com.altomni.apn.user.service.dto.customconfig.PipelineColumnPreferenceDTO;
import com.altomni.apn.user.service.mapper.customconfig.JsonMapperUtil;
import com.altomni.apn.user.service.mapper.customconfig.PipelineColumnTemplateConfigMapper;
import com.altomni.apn.user.service.mapper.customconfig.SystemConfigDefaultMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Service
public class PipelineColumnPreferenceServiceImpl implements PipelineColumnPreferenceService {

    final static Category COLUMN_CATEGORY = Category.TALENT_COLUMN;
    final static ConfigSubcategory PIPELINE_COLUMN = ConfigSubcategory.TALENT_COLUMN_PIPELINE_GENERAL;

    final static ConfigSubcategory PIPELINE_CUSTOMIZED_COLUMN = ConfigSubcategory.TALENT_COLUMN_PIPELINE_CUSTOMIZED;


    @Resource
    private ApplicationIPGProperties applicationIPGProperties;

    @Resource
    private PipelineColumnPreferenceRepository pipelineColumnPreferenceRepository;

    @Resource
    private SystemConfigDefaultService systemConfigDefaultService;

    @Resource
    private PipelineColumnTemplateConfigMapper pipelineColumnTemplateConfigMapper;

    @Resource
    private SystemConfigDefaultMapper systemConfigDefaultMapper;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;

//    @Override
//    public PipelineColumnPreferenceDTO findOne(Long userId, ModuleType moduleType) {
//        PipelineColumnPreference result = pipelineColumnPreferenceRepository.findByUserIdAndModule(userId, moduleType);
//        if (ObjectUtil.isEmpty(result)) {
//            return new PipelineColumnPreferenceDTO();
//        }
//        return PipelineColumnPreferenceDTO.toDto(result);
//    }

    @Override
    public List<BaseConfig> findByUserId(Long userId) {
        List<PipelineColumnPreference> pipelineColumnPreferenceList = pipelineColumnPreferenceRepository.findAllByUserId(userId);
        if(pipelineColumnPreferenceList.isEmpty()){
            return new ArrayList<>();
        }
        List<PipelineColumnPreferenceDTO> pipelineColumnPreferenceDTOList = pipelineColumnTemplateConfigMapper.toDto(pipelineColumnPreferenceList);
        List<BaseConfig> pipelineConfig = new ArrayList<>(pipelineColumnPreferenceDTOList);
        return pipelineConfig;
    }

    @Override
    public BaseConfig getTalentPipelineColumnConfigByTenatId(Long tenantId){
        if(applicationIPGProperties.getIpgRuleTenantIds().contains(tenantId)){
            return getSystemDefaultPipelineColumnConfig(PIPELINE_CUSTOMIZED_COLUMN);
        }else{
            return getSystemDefaultPipelineColumnConfig(PIPELINE_COLUMN);
        }
    }

    private BaseConfig getSystemDefaultPipelineColumnConfig(ConfigSubcategory configSubcategory){
        return systemConfigDefaultMapper.toDto(systemConfigDefaultService.getSystemConfigByCategoryAndSubCategory(COLUMN_CATEGORY, configSubcategory));
    }

    @Override
    public void deleteById(Long id) {
        if (pipelineColumnPreferenceRepository.existsById(id)) {
            pipelineColumnPreferenceRepository.deleteById(id);
        } else {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_PIPELINE_DELETEBYID_NOTFOUND.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()), Arrays.asList(id),userApiPromptProperties.getUserService()));
        }
    }

    @Override
    public BaseConfig createColumnConfig(PipelineColumnPreferenceDTO columnPreferenceDTO) {
        if (SecurityUtils.getUserId() == null || SecurityUtils.getUserId() <= 0L ){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if (columnPreferenceDTO.getTemplateName() == null || columnPreferenceDTO.getTemplateName().trim().isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_PIPELINE_CREATECOLUMNCONFIG_TEMPLATENAMENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        PipelineColumnPreference columnConfig = pipelineColumnPreferenceRepository.findByTemplateNameAndUserId(columnPreferenceDTO.getTemplateName(), SecurityUtils.getUserId());
        if(columnConfig != null){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_PIPELINE_CREATECOLUMNCONFIG_SAMETEMPLATENAME.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return saveTemplate(columnPreferenceDTO);
    }

    @Override
    public BaseConfig updateColumnConfig(PipelineColumnPreferenceDTO columnPreferenceDTO, Long templateId) {
        if (SecurityUtils.getUserId() == null || SecurityUtils.getUserId() <= 0 ){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_GETCOLUMNCONFIGBYUSERID_USERIDNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if (columnPreferenceDTO.getTemplateName() == null || columnPreferenceDTO.getTemplateName().trim().isEmpty()) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_PIPELINE_CREATECOLUMNCONFIG_TEMPLATENAMENULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        Optional<PipelineColumnPreference> columnConfigOptional = pipelineColumnPreferenceRepository.findById(templateId);
        if(columnConfigOptional.isEmpty()){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_PIPELINE_UPDATECOLUMNCONFIG_INVALIDTEMPLATE.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        if(columnConfigOptional.isPresent() && !columnConfigOptional.get().getUserId().equals(SecurityUtils.getUserId())){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_PIPELINE_UPDATECOLUMNCONFIG_NOPERMISSION.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        PipelineColumnPreference duplicated = pipelineColumnPreferenceRepository.findByTemplateNameAndUserId(columnPreferenceDTO.getTemplateName(), SecurityUtils.getUserId());
        if(duplicated != null && duplicated.getId().longValue() != templateId.longValue()){
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.CUSTOMCONFIG_PIPELINE_CREATECOLUMNCONFIG_SAMETEMPLATENAME.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }

        PipelineColumnPreference columnConfig = columnConfigOptional.get();
        columnConfig.setCustomConfig(JsonMapperUtil.customColumnConfigToJsonString(columnPreferenceDTO.getCustomConfig()));
        columnConfig.setTemplateName(columnPreferenceDTO.getTemplateName());

        return pipelineColumnTemplateConfigMapper.toDto(pipelineColumnPreferenceRepository.save(columnConfig));
    }

    private BaseConfig saveTemplate(PipelineColumnPreferenceDTO columnPreferenceDTO){
        PipelineColumnPreference columnPreference = pipelineColumnTemplateConfigMapper.toEntity(columnPreferenceDTO);
//        ServiceUtils.myCopyProperties(columnPreferenceDTO, columnPreference);
        columnPreference.setUserId(SecurityUtils.getUserId());
        return pipelineColumnTemplateConfigMapper.toDto(pipelineColumnPreferenceRepository.save(columnPreference));

    }

}
