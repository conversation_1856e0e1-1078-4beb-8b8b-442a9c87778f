package com.altomni.apn.user.web.rest.vm.system;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class OnlineTenantVM {

    private Long tenantId;

    private String tenantName;

    private Long onlineUserCount;

    public OnlineTenantVM(Long tenantId, String name) {
        this.tenantId = tenantId;
        this.tenantName = name;
    }
}
