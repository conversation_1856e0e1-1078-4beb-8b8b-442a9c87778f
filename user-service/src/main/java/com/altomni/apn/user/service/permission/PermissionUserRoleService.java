package com.altomni.apn.user.service.permission;

import com.altomni.apn.common.domain.user.UserRole;
import com.altomni.apn.user.web.rest.vm.permission.PermissionUserRoleVM;

import java.util.Set;

public interface PermissionUserRoleService {

    UserRole create(UserRole userRole);

    Set<Long> update(PermissionUserRoleVM userRoleVM);

    UserRole update(UserRole userRole);

    UserRole get(Integer id);

    void delete(Integer id);
}
