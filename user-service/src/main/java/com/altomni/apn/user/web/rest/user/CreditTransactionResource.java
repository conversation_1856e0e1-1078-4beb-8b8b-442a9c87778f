package com.altomni.apn.user.web.rest.user;

import cn.hutool.core.util.ObjectUtil;
import com.altomni.apn.common.aop.request.NoRepeatSubmit;
import com.altomni.apn.common.config.CommonApiMultilingualConfig;
import com.altomni.apn.common.enumeration.enums.UserAPIMultilingualEnum;
import com.altomni.apn.common.errors.CustomParameterizedException;
import com.altomni.apn.common.domain.enumeration.user.Status;
import com.altomni.apn.common.utils.CommonUtils;
import com.altomni.apn.user.config.env.UserApiPromptProperties;
import com.altomni.apn.user.domain.user.CreditTransaction;
import com.altomni.apn.user.service.dto.user.CreditTransactionDTO;
import com.altomni.apn.user.service.user.CreditTransactionService;
import com.altomni.apn.common.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing CreditTransactionDTO.
 */
@Slf4j
@RestController
@RequestMapping("/api/v3")
public class CreditTransactionResource {

    private static final String ENTITY_NAME = "creditTransaction";

    @Resource
    private CreditTransactionService creditTransactionService;

    @Resource
    CommonApiMultilingualConfig commonApiMultilingualConfig;

    @Resource
    UserApiPromptProperties userApiPromptProperties;


    /**
     * POST  /credit-transactions : Create a new creditTransaction.
     *
     * @param creditTransactionDTO the creditTransaction to create
     * @return the ResponseEntity with status 201 (Created) and with body the new creditTransaction, or with status 400 (Bad Request) if the creditTransaction has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/credit-transactions")
    @NoRepeatSubmit
    public ResponseEntity<CreditTransactionDTO> createCreditTransaction(@RequestBody CreditTransactionDTO creditTransactionDTO) throws URISyntaxException {
        log.info("[APN: CreditTransaction @{}] REST request to save CreditTransactionDTO : {}", SecurityUtils.getUserId(), creditTransactionDTO);
        if (creditTransactionDTO.getId() != null) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATECREDITTRANSACTION_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        CreditTransactionDTO result = creditTransactionService.create(creditTransactionDTO);
        return ResponseEntity.created(new URI("/api/credit-transactions/" + result.getId()))
            .body(result);
    }

    /**
     * GET  /credit-transactions : get all the creditTransactions.
     *
     * @return the ResponseEntity with status 200 (OK) and the list of creditTransactions in body
     */
    @GetMapping("/credit-transactions")
    public List<CreditTransactionDTO> getAllCreditTransactions() {
        log.info("[APN: CreditTransaction @{}] REST request to get all CreditTransactions", SecurityUtils.getUserId());
        return creditTransactionService.findByTenantId(SecurityUtils.getTenantId());
    }

    @GetMapping("/credit-transactions/find/profile-id-and-tenant-id-and-status")
    public ResponseEntity<CreditTransaction> findByProfileIdAndTenantIdAndStatus(@RequestParam("profileId") String profileId, @RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status){
        log.info("({},{}) REST request to get credit transaction by profileId: {}, tenantId: {}, and status: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), profileId, tenantId, status);
        return ResponseEntity.ok(creditTransactionService.findByProfileIdAndTenantIdAndStatus(profileId, tenantId, status));
    }

    @PostMapping("/credit-transactions/find/tenant-id-and-status-and-profile-ids")
    public ResponseEntity<List<CreditTransaction>> findAllCreditTransactionByTenantIdAndStatusAndProfileIdIn(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status, @RequestBody List<String> profileId){
        log.info("({},{}) REST request to get credit transaction by profileId in: {}, tenantId: {}, and status: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), profileId, tenantId, status);
        return ResponseEntity.ok(creditTransactionService.findAllByTenantIdAndStatusAndProfileIdIn(tenantId, status, profileId));
    }

    @GetMapping("/credit-transactions/find/tenant-id-and-status")
    public ResponseEntity<List<CreditTransaction>> findCreditTransactionByTenantIdAndStatus(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status){
        log.info("({},{}) REST request to get credit transaction by tenantId: {}, and status: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), tenantId, status);
        return ResponseEntity.ok(creditTransactionService.findByTenantIdAndStatus(tenantId, status));
    }

    @PostMapping("/credit-transactions/find/tenant-id-and-status-and-search-es-ids")
    public ResponseEntity<List<CreditTransaction>> findAllCreditTransactionByTenantIdAndStatusAndSearchEsIn(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status, @RequestBody List<String> searchEsId){
        log.info("({},{}) REST request to get credit transaction by searchEsId in: {}, tenantId: {}, and status: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), searchEsId, tenantId, status);
        return ResponseEntity.ok(creditTransactionService.findAllByTenantIdAndStatusAndSearchEsIdIn(tenantId, status, searchEsId));
    }

    @PostMapping("/credit-transactions/find/talent-id")
    public ResponseEntity<CreditTransaction> findCreditTransactionByTenantIdAndStatusAndTalentIdIs(@RequestParam("tenantId") Long tenantId, @RequestParam("status") Status status, @RequestParam("talentId") Long talentId){
        log.info("({},{}) REST request to get credit transaction by talent id: {}, tenantId: {}, and status: {}", SecurityUtils.getTenantId(), SecurityUtils.getUserId(), talentId, tenantId, status);
        return ResponseEntity.ok(creditTransactionService.findByTenantIdAndStatusAndTalentIdIs(tenantId, status, talentId));
    }

    @PostMapping("/credit-transactions/commonPool")
    @NoRepeatSubmit
    public ResponseEntity<CreditTransactionDTO> createCreditTransactionForCommonPool(@RequestBody CreditTransactionDTO creditTransactionDTO) throws URISyntaxException {
        log.info("[APN: CreditTransaction @{}] REST request to save CreditTransactionDTO : {}", SecurityUtils.getUserId(), creditTransactionDTO);
        if (ObjectUtil.isNotNull(creditTransactionDTO.getId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATECREDITTRANSACTION_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        CreditTransactionDTO result = creditTransactionService.createCommonPoolCreditTransaction(creditTransactionDTO);
        return ResponseEntity.created(new URI("/api/credit-transactions/" + result.getId())).body(result);
    }

    @PutMapping("/credit-transactions/commonPool")
    public ResponseEntity<CreditTransactionDTO> updateCreditTalentIdForCommonPool(@RequestBody CreditTransactionDTO creditTransactionDTO) throws IOException {
        log.info("[APN: Talent @{}] REST request to update CreditTransaction: {}, creditTransactionDTO:{}", SecurityUtils.getUserId(), creditTransactionDTO);
        CreditTransactionDTO result = creditTransactionService.updateTalentId(creditTransactionDTO, SecurityUtils.getTenantId());
        return new ResponseEntity<>(result, HttpStatus.OK);
    }

    @PostMapping("/credit-transactions/ai-sourcing")
    @NoRepeatSubmit
    public ResponseEntity<String> createCreditTransactionForAiSourcing(@RequestBody CreditTransactionDTO creditTransactionDTO) throws IOException {
        log.info("[APN: CreditTransaction @{}] REST request to save CreditTransactionDTO for AI_Sourcing: {}", SecurityUtils.getUserId(), creditTransactionDTO);
        if (ObjectUtil.isNotNull(creditTransactionDTO.getId())) {
            throw new CustomParameterizedException(commonApiMultilingualConfig.getTitleInfoByKeyAndLanguage(UserAPIMultilingualEnum.USER_CREATECREDITTRANSACTION_IDNOTNULL.getKey(), CommonUtils.userLanguageSetConcat(SecurityUtils.getUserUid()),userApiPromptProperties.getUserService()));
        }
        return ResponseEntity.ok(creditTransactionService.createAiSourcingCreditTransaction(creditTransactionDTO));
    }
}
