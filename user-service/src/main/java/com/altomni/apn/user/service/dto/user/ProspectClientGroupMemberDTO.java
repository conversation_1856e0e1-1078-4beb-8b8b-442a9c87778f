package com.altomni.apn.user.service.dto.user;

import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link } entity.
 */
public class ProspectClientGroupMemberDTO implements Serializable {

    private Long id;

//    private Long companyId;

    private Long userId;

//    private ProspectClientGroupMemberRole role;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

//    public Long getCompanyId() {
//        return companyId;
//    }

//    public void setCompanyId(Long companyId) {
//        this.companyId = companyId;
//    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

//    public ProspectClientGroupMemberRole getRole() {
//        return role;
//    }

//    public void setRole(ProspectClientGroupMemberRole role) {
//        this.role = role;
//    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        ProspectClientGroupMemberDTO prospectClientGroupMemberDTO = (ProspectClientGroupMemberDTO) o;
        if (prospectClientGroupMemberDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), prospectClientGroupMemberDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "ProspectClientGroupMemberDTO{" +
            "id=" + getId() +
//            ", companyId=" + getCompanyId() +
            ", userId=" + getUserId() +
//            ", role='" + getRole() + "'" +
            "}";
    }
}
