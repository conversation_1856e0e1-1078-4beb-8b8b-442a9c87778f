package com.altomni.apn.user.service.user;

import com.altomni.apn.user.domain.user.Team;

import java.util.List;
import java.util.Set;

public interface TeamUserService {

    Team addUser(Long teamId, Long userId);

    Team multiAddUsers(Long teamId, List<Long> userIds);

    Team removeUser(Long teamId, Long userId);

    Set<Long> getAllTeamUsersByLeader(Long leaderUserId);

    Set<Long> getAllTeamUsersByTeamId(Long teamId);

    Team replaceUsers(Long teamId, List<Long> userIds);
}
