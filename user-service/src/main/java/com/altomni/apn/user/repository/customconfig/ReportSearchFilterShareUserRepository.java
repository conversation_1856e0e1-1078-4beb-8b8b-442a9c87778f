package com.altomni.apn.user.repository.customconfig;

import com.altomni.apn.user.domain.customconfig.ReportSearchFilterShareUser;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReportSearchFilterShareUserRepository extends JpaRepository<ReportSearchFilterShareUser, Long> {

    List<ReportSearchFilterShareUser> findByFilterId(Long filterId);

    List<ReportSearchFilterShareUser> findByFilterIdInAndShareType(List<Long> filterId,Integer shareType);

    List<ReportSearchFilterShareUser> findByShareFilterId(Long filterId);

    @Modifying
    @Query(value = "delete from report_search_filter_share_user where id in (?1)",nativeQuery = true)
    void deleteAllByIdList(List<Long> idList);

    @Modifying
    @Query(value = "delete from report_search_filter_share_user where filter_id =?1",nativeQuery = true)
    void deleteAllByFilterId(Long filterId);

    @Modifying
    @Query(value = "delete from report_search_filter_share_user where share_filter_id =?1",nativeQuery = true)
    void deleteAllByShareFilterId(Long filterId);
}
