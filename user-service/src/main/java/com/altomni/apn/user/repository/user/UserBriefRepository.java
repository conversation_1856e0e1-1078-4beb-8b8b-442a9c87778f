package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.UserBrief;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;


/**
 * Spring Data JPA repository for the User entity.
 */
@Repository
public interface UserBriefRepository extends JpaRepository<UserBrief, Long>
{
    List<UserBrief> findALLByIdInAndActivated(List<Long> ids, Boolean activated);

    List<UserBrief> findALLByIdInAndActivatedIsTrue(Set<Long> ids);

    List<UserBrief> findAllByTenantIdAndActivatedIsTrue(Long tenantId);

    List<UserBrief> findAllByIdInAndTenantIdAndActivatedIsTrue(Set<Long> ids, Long tenantId);

    List<UserBrief> findAllByUidIn(List<String> uids);
}
