package com.altomni.apn.user.repository.user;

import com.altomni.apn.user.domain.user.UserImpersonation;
import com.altomni.apn.user.service.dto.user.UserImpersonationDTO;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.Optional;


/**
 * Spring Data JPA repository for the UserImpersonation entity.
 */
@Repository
public interface UserImpersonationRepository extends JpaRepository<UserImpersonation, Long> {

    @Query("SELECT NEW com.altomni.apn.user.service.dto.user.UserImpersonationDTO(ui.id, u.id, u.email, u.firstName, u.lastName, ui.effectiveStartAt, ui.expireAt) " +
            "FROM UserImpersonation ui " +
            "LEFT JOIN User u ON u.id = ui.grantToUserId " +
            "WHERE ui.grantFromUserId=:fromUserId " +
            "AND ui.tenantId=:tenantId " +
            "AND ui.expireAt > :now")
    List<UserImpersonationDTO> findAllAvailableByGrantFromUserIdAndTenantId(@Param("fromUserId") Long fromUserId, @Param("tenantId") Long tenantId, @Param("now") Instant now);

    @Query("SELECT NEW com.altomni.apn.user.service.dto.user.UserImpersonationDTO(ui.id, u.id, u.email, u.firstName, u.lastName, ui.effectiveStartAt, ui.expireAt) " +
            "FROM UserImpersonation ui " +
            "LEFT JOIN User u ON u.id = ui.grantToUserId " +
            "WHERE ui.grantFromUserId=:fromUserId " +
            "AND ui.tenantId=:tenantId " +
            "ORDER BY ui.id DESC")
    List<UserImpersonationDTO> findAllByGrantFromUserIdAndTenantId(@Param("fromUserId") Long fromUserId, @Param("tenantId") Long tenantId);

    @Query("SELECT NEW com.altomni.apn.user.service.dto.user.UserImpersonationDTO(ui.id, u.id, u.email, u.firstName, u.lastName, ui.effectiveStartAt, ui.expireAt) " +
            "FROM UserImpersonation ui " +
            "LEFT JOIN User u ON u.id = ui.grantFromUserId " +
            "WHERE ui.grantToUserId=:toUserId " +
            "AND ui.tenantId=:tenantId " +
            "AND ui.expireAt > :now")
    List<UserImpersonationDTO> findAllAvailableGrantFromUserByGrantToUserIdAndTenantId(@Param("toUserId") Long toUserId, @Param("tenantId") Long tenantId, @Param("now") Instant now);

    @Query("SELECT NEW com.altomni.apn.user.service.dto.user.UserImpersonationDTO(ui.id, fromU.id, fromU.email, fromU.firstName, fromU.lastName, toU.id, toU.email, toU.firstName, toU.lastName, ui.effectiveStartAt, ui.expireAt) " +
            "FROM UserImpersonation ui " +
            "LEFT JOIN User fromU ON fromU.id = ui.grantFromUserId " +
            "LEFT JOIN User toU ON toU.id = ui.grantToUserId " +
            "WHERE ui.tenantId=:tenantId " +
            "AND ui.expireAt > :now")
    List<UserImpersonationDTO> findAllAvailableByTenantId(@Param("tenantId") Long tenantId, @Param("now") Instant now);

    @Query("SELECT NEW com.altomni.apn.user.service.dto.user.UserImpersonationDTO(ui.id, fromU.id, fromU.email, fromU.firstName, fromU.lastName, toU.id, toU.email, toU.firstName, toU.lastName, ui.effectiveStartAt, ui.expireAt) " +
            "FROM UserImpersonation ui " +
            "LEFT JOIN User fromU ON fromU.id = ui.grantFromUserId " +
            "LEFT JOIN User toU ON toU.id = ui.grantToUserId " +
            "WHERE ui.tenantId=:tenantId " +
            "ORDER BY ui.id DESC")
    List<UserImpersonationDTO> findAllByTenantId(@Param("tenantId") Long tenantId);

    @Query("SELECT NEW com.altomni.apn.user.service.dto.user.UserImpersonationDTO(ui.id, fromU.id, fromU.email, fromU.firstName, fromU.lastName, toU.id, toU.email, toU.firstName, toU.lastName, ui.effectiveStartAt, ui.expireAt) " +
            "FROM UserImpersonation ui " +
            "LEFT JOIN User fromU ON fromU.id = ui.grantFromUserId " +
            "LEFT JOIN User toU ON toU.id = ui.grantToUserId " +
            "WHERE ui.id=:id ")
    UserImpersonationDTO findOneById(@Param("id") Long id);

    boolean existsByIdAndGrantFromUserIdAndExpireAtAfter(Long id, Long grantFromUserId, Instant now);

    boolean existsByIdAndTenantIdAndExpireAtAfter(Long id, Long tenantId, Instant now);

    Optional<UserImpersonation> findFirstByGrantFromUserIdAndGrantToUserIdAndTenantIdAndExpireAtAfter(Long fromUserId, Long toUserId, Long tenantId, Instant now);

    @Modifying
    @Query("update UserImpersonation ui set ui.expireAt =:now " +
            "where ui.grantFromUserId =:fromUserId " +
            "and ui.grantToUserId =:toUserId " +
            "and ui.tenantId =:tenantId " +
            "and ui.expireAt >:now")
    void expireAllActiveImpersonationByGrantFromUserIdAndGrantToUserIdAndTenantId(@Param("fromUserId") Long fromUserId, @Param("toUserId") Long toUserId, @Param("tenantId") Long tenantId, @Param("now") Instant now);

    @Query("SELECT MAX(ui.expireAt) FROM UserImpersonation ui " +
            "WHERE ui.grantFromUserId =:fromUserId " +
            "AND ui.grantToUserId =:toUserId " +
            "AND ui.tenantId =:tenantId " +
            "And ui.effectiveStartAt <:now " +
            "AND ui.expireAt >:now")
    Optional<Instant> findExpireTime(@Param("fromUserId") Long fromUserId, @Param("toUserId") Long toUserId, @Param("tenantId") Long tenantId, @Param("now") Instant now);
}