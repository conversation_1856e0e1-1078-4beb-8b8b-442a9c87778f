package com.altomni.apn.user.service.customconfig;

import com.altomni.apn.common.dto.customconfig.BaseConfig;
import com.altomni.apn.user.service.dto.customconfig.JobColumnConfigDTO;
import com.altomni.apn.user.service.dto.customconfig.PageConfig;

public interface JobColumnConfigService {
    Boolean removeAllCachedJobColumnConfigByTenantId(Long tenantId);

    BaseConfig getColumnConfigByUserId(Long userid);

    BaseConfig getPrivateJobColumnConfigByUserId(Long userid);

    JobColumnConfigDTO saveJobColumnConfig(JobColumnConfigDTO JobColumnConfigDTO, Long userId);

    JobColumnConfigDTO savePrivateJobColumnConfig(JobColumnConfigDTO JobColumnConfigDTO, Long userId);

    PageConfig updateJobPageConfig(PageConfig pageConfig);

    PageConfig updatePrivateJobPageConfig(PageConfig pageConfig);

    BaseConfig getCompanyJobColumnConfigByUserId(Long userId);

    JobColumnConfigDTO saveCompanyJobColumnConfig(JobColumnConfigDTO jobColumnConfigDTO, Long userId);
}
