package com.altomni.apn.user.repository.user;

import com.altomni.apn.common.domain.user.UserDeliveryCountryRelation;
import com.altomni.apn.common.domain.user.UserLanguageRelation;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Set;


@Repository
public interface UserDeliveryCountryRelationRepository extends JpaRepository<UserDeliveryCountryRelation, Long> {
    List<UserDeliveryCountryRelation> findAllByUserId(Long userId);

    List<UserDeliveryCountryRelation> findAllByUserIdAndTop(Long userId, Boolean top);

    List<UserDeliveryCountryRelation> findAllByUserIdIn(Collection<Long> userIds);


    List<UserDeliveryCountryRelation> findAllByUserIdInAndTop(Collection<Long> userIds, Boolean top);
}
