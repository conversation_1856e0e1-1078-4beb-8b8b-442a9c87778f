package com.altomni.apn.user.domain.permission;

import com.altomni.apn.common.domain.AbstractAuditingEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Accessors(chain = true)
@Entity
@Table(name = "permission_tenant_module")
public class PermissionTenantModule extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Access(AccessType.PROPERTY)
    private Long id;

    @NotNull
    @Column(name = "module_id")
    private Long moduleId;

    @NotNull
    @Column(name = "tenant_id")
    private Long tenantId;

    @Column(name = "involve_data_permission")
    private Boolean involveDataPermission;

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        PermissionTenantModule role = (PermissionTenantModule) o;
        return id.equals(role.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    public PermissionTenantModule(Long moduleId, Long tenantId, Boolean involveDataPermission) {
        this.moduleId = moduleId;
        this.tenantId = tenantId;
        this.involveDataPermission = involveDataPermission;
    }
}
