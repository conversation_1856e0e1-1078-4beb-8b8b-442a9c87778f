<?xml version='1.0' encoding='utf-8'?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <include file="config/liquibase/changelog/init/1666262757655_added_entity_ApnParam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757657_added_entity_CreditTransaction.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionExtraRoleTeam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionExtraUserTeam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionModule.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionModuleTable.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionPrivilege.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionRolePrivilege.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionTable.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionTeam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionTeamLeader.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionTenantModule.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757660_added_entity_PermissionUserTeam.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_Role.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_User.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserAccount.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserAdmin.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserAdminRole.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserEmailDomain.xml" relativeToChangelogFile="false"/>
<!-- deperated and merge into job_folder_relation in job-service   <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserFavoriteJob.xml" relativeToChangelogFile="false"/>-->
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserFavoriteLinkedinProject.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserFavoriteTalent.xml" relativeToChangelogFile="false"/>
<!--    <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserJobRelation.xml" relativeToChangelogFile="false"/>-->
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserLastLogin.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757666_added_entity_UserRole.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1668672803663_added_entity_Division.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1668672803663_added_entity_Team.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1668672803663_added_entity_TeamUser.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1683831627325_added_entity_UserImpersonation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1683831629355_added_entity_PermissionImpersonationPrivilege.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/1668672803770_added_entity_PermissionTenantPrivilege.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1668672803771_added_entity_PermissionModulePrivilege.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1682007626736_added_entity_UserPreference.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1682007626736_added_entity_JobFormConfig.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1682007626736_added_entity_SystemConfigDefault.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1689195289736_added_entity_TalentFormConfig.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1666262757661_added_entity_PipelineColumnPreference.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1697679170509_added_entity_TenantCustomConfig.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1697679199959_added_entity_TenantDefaultConfig.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20241217144400_added_entity_UserSkillTag.xml" relativeToChangelogFile="false"/>

    <include file="config/liquibase/changelog/init/20250122090000_added_entity_UserLanguageRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250122090001_added_entity_UserDeliveryCountryRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250122090002_added_entity_UserDeliveryIndustryRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250122090003_added_entity_UserDeliveryJobFunctionRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/1669672803991_added_entity_PermissionModulePrivilegeConfig.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250122090004_added_entity_UserDeliveryProcessRelation.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250122090015_added_view_final_recruitment_process_view.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250515132700_added_entity_tenant_push_rules.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250515132800_added_entity_tenant_push_rules_team.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250515132900_added_entity_tenant_push_rules_type.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/init/20250515133000_added_entity_tenant_push_rules_team.xml" relativeToChangelogFile="false"/>

</databaseChangeLog>