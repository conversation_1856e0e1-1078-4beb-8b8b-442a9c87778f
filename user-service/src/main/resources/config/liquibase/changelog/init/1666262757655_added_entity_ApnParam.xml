<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1666263097866-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="apn_param"/>
            </not>
        </preConditions>
        <createTable tableName="apn_param">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="param_name" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="param_key" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="param_value" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="user_id" type="BIGINT"/>
            <column defaultValueNumeric="0" name="status" type="INT">
                <constraints nullable="false"/>
            </column>
        </createTable>

        <createIndex indexName="idx_apn_param_tenant_id" tableName="apn_param">
            <column name="tenant_id"/>
        </createIndex>
    </changeSet>

</databaseChangeLog>
