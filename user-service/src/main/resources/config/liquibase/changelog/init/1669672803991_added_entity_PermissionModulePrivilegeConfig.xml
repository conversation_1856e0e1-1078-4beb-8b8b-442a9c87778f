<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog" xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext" xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-latest.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <changeSet author="m (generated)" id="1668672688942-1">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="permission_module_privilege_config"/>
            </not>
        </preConditions>
        <createTable tableName="permission_module_privilege_config">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="permission_module_privilege_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="tenant_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="type" type="TINYINT">
                <constraints nullable="false"/>
            </column>
            <column name="is_show_permission_button" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="disable" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
            <column name="sort" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="enable" type="BIT(1)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>
