<?xml version="1.0" encoding="utf-8" standalone="no"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity TenantDefaultConfig.
    -->
    <changeSet id="1697679199959-1" author="jhipster">
        <preConditions onFail="MARK_RAN">
            <not>
                <tableExists tableName="tenant_default_config"/>
            </not>
        </preConditions>
        <createTable tableName="tenant_default_config">
            <column name="id" type="bigint" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="config_code" type="tinyint"/>
            <column name="config_value" type="json"/>
        </createTable>
        <createIndex indexName="index_tenant_default_config_config_code" tableName="tenant_default_config">
            <column name="config_code"/>
        </createIndex>


    </changeSet>

</databaseChangeLog>
